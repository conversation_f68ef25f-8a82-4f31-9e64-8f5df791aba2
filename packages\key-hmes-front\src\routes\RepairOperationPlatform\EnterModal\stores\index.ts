/**
 * @Description: 工序作业平台
 * @Author: <<EMAIL>>
 * @Date: 2023-2-26 18:50:17
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

// 登录工位ds
const enterModalDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'workStationCode',
      type: FieldType.string,
      label: '工位编码',
    },
    {
      name: 'workStationName',
      type: FieldType.string,
      label: '工位',
    },
    {
      name: 'operationName',
      type: FieldType.string,
    },
  ],
});

export { enterModalDS };
