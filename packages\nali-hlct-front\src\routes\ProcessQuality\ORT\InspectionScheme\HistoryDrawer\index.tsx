import React, { useMemo } from 'react';
import { Button, DataSet, Modal, Table } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import {ColumnAlign, ColumnLock} from 'choerodon-ui/pro/lib/table/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import {Badge, Collapse} from 'choerodon-ui';
import { headDS, lineDS  } from './stores';

const modelPrompt = 'tarzan.ort.InspectionScheme';
const { Panel } = Collapse;

export interface ApprovalDrawerProps {
  type?: 'button' | 'text';
  objectTypeList: String[];
  objectId: string;
  disabled?: boolean;
}

const AttributeDrawer: React.FC<ApprovalDrawerProps> = ({
  type = 'button',
  objectId,
  disabled = false,
}) => {
  const headDs = useMemo(() => new DataSet(headDS()), []);
  const lineDs = useMemo(() => new DataSet(lineDS()), []);

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectSchemeCode',
        lock: ColumnLock.left,
        width: 180,
      },
      {
        name: 'inspectSchemeName',
        width: 130,
      },
      { name: 'siteName', width: 180, lock: ColumnLock.left },
      { name: 'materialCode', width: 180 },
      { name: 'materialName' },
      {
        name: 'enableFlag',
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      { name: 'enableDate', width: 150 },
      { name: 'disableDate', width: 150 },
      { name: 'createdName' },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'sequence' },
      { name: 'inspectItem', width: 180, lock: ColumnLock.left },
      { name: 'inspectMethod' },
      { name: 'standardRequirement', width: 150 },
      { name: 'inspectFrequency' },
      {
        name: 'outsourceFlag',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
      },
      { name: 'enclosure', width: 150, lock: ColumnLock.right },
    ],
    [],
  );

  const handleOpenDrawer = () => {
    if (objectId !== 'create') {
      headDs.setQueryParameter('schemeId', objectId);
      headDs.query();
      lineDs.setQueryParameter('schemeId', objectId);
      lineDs.query();
    }
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
        ds: headDs,
      }),
      title: intl.get(`${modelPrompt}.title.approvalInfo`).d('历史查询'),
      style: {
        width: 1080,
      },
      children: <>
        <Table dataSet={headDs} columns={headColumns} />
        <Collapse bordered={false} defaultActiveKey={['line']}>
          <Panel key="line" header={intl.get(`${modelPrompt}.title.testItem`).d('测试项目')}>
            <Table dataSet={lineDs} columns={lineColumns} />
          </Panel>
        </Collapse>

      </>,
    });
  };

  return (
    <>
      {type === 'text' ? (
        <span className="action-link">
          <Button funcType={FuncType.flat} onClick={handleOpenDrawer} disabled={disabled}>
            {intl.get(`${modelPrompt}.button.approvalInfo`).d('历史查询')}
          </Button>
        </span>
      ) : (
        <Button onClick={handleOpenDrawer} disabled={disabled}>
          {intl.get(`${modelPrompt}.button.approvalInfo`).d('历史查询')}
        </Button>
      )}
    </>
  );
};

export default AttributeDrawer;
