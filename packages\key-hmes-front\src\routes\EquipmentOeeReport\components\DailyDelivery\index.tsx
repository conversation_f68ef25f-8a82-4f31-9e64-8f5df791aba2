import React, { useEffect, useRef, useMemo } from 'react';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import DashboardCard from '../DashboardCard.jsx';

const DailyDelivery = (props) => {

  const { xData, seriesData } = props;
  const chartRef = useRef(null);

  const LabelFormatter = (value: { data }) => {
    if (value.data) {
      return `${(value.data * 100).toFixed(2)}%`;
    }
    return 0;
  };

  const option: any = useMemo(() => {
    let legendData: any = [];
    const getRandomColor = () => {
      const letters = '0123456789ABCDEF';
      let color = '#';
      for (let i =0; i <6; i++) {
        color += letters[Math.floor(Math.random() *16)];
      }
      return color;
    }
    const seriesList = seriesData.map((item: any) => {
      const { assetNum, assetDesc, assetId, ...itemData } = item;
      legendData.push(`${assetNum}-${assetDesc}`);
      return {
        name: `${assetNum}-${assetDesc}`,
        type: 'line',
        data: Object.values(itemData).map((ele) => (ele && 'oee' in ele) ? ele.oee : 0),
        stack: 'total',
        color: getRandomColor(),
        label: {
          show: true,
          position: 'top',
          formatter: LabelFormatter,
        },
        lineStyle: {
          width: 5,  // 设置线条的粗细为3
        },
      };
    });
    return {
      title:{
        top:'3%',
        text: '设备OEE趋势图',
        left: 'center',
        textStyle: {
          fontWeight: 'bold', // 加粗
          color: '#585b5b',
        },
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: legendData,
        bottom: '2%',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      toolbox: {},
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xData,
      },
      yAxis: {
        type: 'value',
        max: 1,
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { // 分隔线样式
          lineStyle: {
            type: 'dashed', // 改变分隔线样式为虚线 默认是直线
          },
        },
        axisLabel: { // 控制轴标签样式
          textStyle: {
            color: '#0e0d0d', // 设置轴标签文字颜色
          },
          formatter: value => `${(value * 100).toFixed(2)}%`,
        },
      },
      series: [ ...seriesList ],
    };
  }, [xData, seriesData]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  return (
    <DashboardCard height="100%">
      <div style={{ width: '100%', height: '100%' }}>
        <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
      </div>
    </DashboardCard>
  );
};
export default DailyDelivery;
