import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType, ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores/index';

const modelPrompt = 'tarzan.duplicateProblem';

const DuplicateProblem = props => {
  const {
    tableDs,
    match: { path },
    history,
  } = props;

  useEffect(() => {
    if (tableDs) {
      tableDs.query(props.tableDs.currentPage);
    } else {
      tableDs.query();
    }
  }, []);

  const handleCreate = () => {
    history.push(`/hwms/problem-management/duplicate-problem/detail/create`);
  };

  const columns: ColumnProps[] = [
    {
      name: 'problemReplayCode',
      width: 180,
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              history.push(
                `/hwms/problem-management/duplicate-problem/detail/${record!.get(
                  'problemReplayId',
                )}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
      lock: ColumnLock.left,
    },
    {
      name: 'problemReplayStatus',
      align: ColumnAlign.center,
    },
    {
      name: 'problemCode',
      width: 180,
    },
    {
      name: 'siteName',
      width: 160,
    },
    {
      name: 'problemTitle',
    },
    {
      name: 'createdByName',
    },
    {
      name: 'startReason',
      width: 180,
    },
    {
      name: 'principalUserDept',
    },
    {
      name: 'principalUserName',
    },
    {
      name: 'background',
    },
    {
      name: 'creationDate',
      width: 180,
      align: ColumnAlign.center,
    },
    {
      name: 'planEndTime',
      width: 180,
      align: ColumnAlign.center,
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.type.create`).d('问题复盘')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.create`,
              type: 'button',
              meaning: '列表页-新建',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleCreate()}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          searchCode="wtfp1"
          customizedCode="wtfp1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common','hzero.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });

      return {
        tableDs,
      };
    },
    { cacheState: true, keepOriginDataSet: true },
  )(DuplicateProblem),
);
