import React, { useMemo } from 'react';
import {
  DataSet,
  Table,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { tableDS  } from './stores';

const modelPrompt = 'tarzan.hmes.deliverQuery';

const DeliverQuery = observer(() => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const columns = [
    {
      name: 'equipmentCode',
      lock: 'left',
      width: 120,
    },
    {
      name: 'equipmentName',
      lock: 'left',
    },
    {
      name: 'equipmentDesc',
    },
    {
      name: 'workcellCode',
    },
    {
      name: 'workcellName',
    },
    {
      name: 'equipmentStatusDesc',
    },
    {
      name: 'equipmentBrand',
    },
    {
      name: 'equipmentModel',
    },
    {
      name: 'identification',
    },
    {
      name: 'equipmentLocationCode',
    },
    {
      name: 'equipmentLocationName',
    },
    {
      name: 'equipmentPhysicsStatusDesc',
    },
    {
      name: 'equipmentFinanceStatusDesc',
    },
    {
      name: 'equipmentRegcheckStatusDesc',
    },
  ];
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('设备查询')}>
      </Header>
      <Content>
        <Table
          queryBar='filterBar'
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
          searchCode="scrapBarcodeGeneration"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.scrapBarcodeGeneration'],
})(DeliverQuery);
