/**
 * @Description: 检验任务执行平台-详情界面DS
 */
import {DataSetProps} from 'choerodon-ui/pro/lib/data-set/DataSet';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import {getCurrentOrganizationId, getCurrentUser} from 'utils/utils';
import moment from 'moment';
import {BASIC} from '@utils/config';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();
const modelPrompt = 'tarzan.inspectExecute.MeasureHavePlatform';

const outerDetailDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  forceValidate: true,
  primaryKey: 'inspectDocId',
  fields: [
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检定单号'),
      disabled: true,
    },
    {
      name: 'inspectDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('单据状态'),
      lookupCode: 'YP.QIS.MS_INSPECT_DOC_STATUS',
      lovPara: { tenantId },
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'applicationCreatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationCreatedByName`).d('申请人'),
      disabled: true,
      defaultValue: userInfo.realName,
    },
    {
      name: 'applicationCreatedBy',
    },
    {
      name: 'departmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.departmentName`).d('申请部门'),
      disabled: true,
    },
    {
      name: 'departmentId',
    },
    {
      name: 'docType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_TYPE',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'applicationDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationDocNum`).d('申请单号'),
      disabled: true,
    },
    {
      name: 'applicationCreationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.applicationCreationDate`).d('申请时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
      disabled: true,
    },
    {
      name: 'responName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responName`).d('量具责任人'),
      disabled: true,
    },
    {
      name: 'toolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编码'),
      disabled: true,
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
      disabled: true,
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
      disabled: true,
    },
    {
      name: 'verificationMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
      lookupCode: 'QIS.MS_VRFCT_METHOD',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检定周期'),
      disabled: true,
    },
    {
      name: 'verificationAgency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationAgency`).d('检定机构'),
      lookupCode: 'YP.QIS.MS_INSPECT_PLACE',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      disabled: true,
    },
    {
      name: 'createdBy',
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      type: FieldType.dateTime,
      disabled: true,
    },
    {
      name: 'completionTime',
      label: intl.get(`${modelPrompt}.completionTime`).d('检定完成时间'),
      type: FieldType.dateTime,
      dynamicProps: {
        disabled: ({ record }) => record.get('inspectDocStatus') === 'NEW',
        required: ({ record }) => record.get('inspectDocStatus') !== 'NEW',
      },
    },
    {
      name: 'reviewByName',
      label: intl.get(`${modelPrompt}.reviewByName`).d('审批人'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'reviewDate',
      label: intl.get(`${modelPrompt}.reviewDate`).d('审批时间'),
      type: FieldType.dateTime,
      disabled: true,
    },
    {
      name: 'rejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
      disabled: true,
    },
    {
      name: 'inspectorObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检定人'),
      lovCode: 'YP.QIS.USER_INSPECTOR',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      // textField: 'realName',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !['NEW', 'RELEASED'].includes(record.get('inspectDocStatus'));
        },
      },
    },
    {
      name: 'inspectorId',
      type: FieldType.number,
      bind: 'inspectorObj.id',
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      bind: 'inspectorObj.realName',
    },
    {
      name: 'inspectPlace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectPlace`).d('检定地点'),
      lookupCode: 'YP.QIS.MS_INSPECT_PLACE',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW';
        },
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
      },
    },
    {
      name: 'deliveryTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.deliveryTime`).d('送检时间'),
      dynamicProps: {
        // required: ({ record }) => {
        //   return record.get('inspectDocStatus') !== 'NEW';
        // },
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
      },
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResult`).d('检定结果'),
      lookupCode: 'YP.QIS.MS_INSPECT_RESULT',
      lovPara: { tenantId },
      dynamicProps: {
        // required: ({ record }) => {
        //   return !['NEW'].includes(record.get('inspectDocStatus')) && !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        // },
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW';
        },
        disabled: ({ record }) => {
          return ['NEW'].includes(record.get('inspectDocStatus'))
        },
      },
    },
    {
      name: 'ngReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngReason`).d('不合格原因'),
      dynamicProps: {
        disabled: ({ record }) => {
          return ['NEW'].includes(record.get('inspectDocStatus'))
        },
      },
    },
    {
      name: 'servicedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.servicedFlag`).d('是否维修'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => record?.get('inspectResult') === 'NG',
        disabled: ({ record }) => record.get('inspectDocStatus') === 'NEW' || record?.get('inspectResult') !== 'NG',
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      dynamicProps: {
        disabled: ({ record }) => {
          return ['NEW'].includes(record.get('inspectDocStatus'))
        },
      },
    },
    {
      name: 'reportCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reportCode`).d('外部证书编号'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW' && record.get('inspectResult') !== 'NG';
        },
        // required: ({ record }) => {
        //   if (record.get('reportCode')) {
        //     return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'))
        //   }
        //   return false;
        // },
      },
    },
    {
      name: 'inspectReportUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.externalCertificate`).d('外部证书'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW' && record.get('inspectResult') !== 'NG';
        },
      },
    },
    {
      name: 'returnedUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.returnedUuid`).d('退检单'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW' && record.get('inspectResult') === 'NG';
        },
      },
    },
    {
      name: 'overtimeFileUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.overtimeFile`).d('超期改善单'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
      },
    },
    {
      name: 'ncFileUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.ncFile`).d('不合格通知单'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
      },
    },
    {
      name: 'cancelReason',
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消原因'),
      type: FieldType.string,
    },
    {
      name: 'cancelDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelDate`).d('取消时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/external-info/ui`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && datas.rows) {
            if (datas.rows.inspectorId === 0) {
              datas.rows.inspectorId = null;
            }
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
});

const insideDetailDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  primaryKey: 'inspectDocId',
  fields: [
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检定单号'),
      disabled: true,
    },
    {
      name: 'inspectDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('单据状态'),
      lookupCode: 'YP.QIS.MS_INSPECT_DOC_STATUS',
      lovPara: { tenantId },
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'applicationCreatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationCreatedByName`).d('申请人'),
      disabled: true,
      defaultValue: userInfo.realName,
    },
    {
      name: 'applicationCreatedBy',
    },
    {
      name: 'departmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.departmentName`).d('申请部门'),
      disabled: true,
    },
    {
      name: 'departmentId',
    },
    {
      name: 'docType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_TYPE',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'applicationDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationDocNum`).d('申请单号'),
      disabled: true,
    },
    {
      name: 'applicationCreationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.applicationCreationDate`).d('申请时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
      disabled: true,
    },
    {
      name: 'responName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responName`).d('量具责任人'),
      disabled: true,
    },
    {
      name: 'toolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编码'),
      disabled: true,
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
      disabled: true,
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
      disabled: true,
    },
    {
      name: 'verificationMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
      lookupCode: 'QIS.MS_VRFCT_METHOD',
      lovPara: { tenantId },
      disabled: true,
    },

    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检定周期'),
      disabled: true,
    },
    {
      name: 'verificationAgency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationAgency`).d('检定机构'),
      lookupCode: 'YP.QIS.MS_INSPECT_PLACE',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      disabled: true,
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      type: FieldType.dateTime,
      disabled: true,
    },
    {
      name: 'completionTime',
      label: intl.get(`${modelPrompt}.completionTime`).d('检定完成时间'),
      type: FieldType.dateTime,
      dynamicProps: {
        disabled: ({ record }) => record.get('inspectDocStatus') === 'NEW',
        required: ({ record }) => record.get('inspectDocStatus') !== 'NEW',
      },
    },
    {
      name: 'inspectorObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检定人'),
      lovCode: 'YP.QIS.USER_INSPECTOR',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !['NEW', 'RELEASED'].includes(record.get('inspectDocStatus'));
        },
      },
    },
    {
      name: 'inspectorId',
      type: FieldType.number,
      bind: 'inspectorObj.id',
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      bind: 'inspectorObj.realName',
    },
    {
      name: 'inspectPlace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectPlace`).d('检定地点'),
      lookupCode: 'YP.QIS.MS_INSPECT_PLACE',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW';
        },
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
      },
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResult`).d('检定结果'),
      lookupCode: 'YP.QIS.MS_INSPECT_RESULT',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW';
        },
        // required: ({ record }) => {
        //   return !['NEW'].includes(record.get('inspectDocStatus')) && !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        // },
        disabled: ({ record }) => {
          return ['NEW'].includes(record.get('inspectDocStatus'))
        },
      },
    },
    {
      name: 'appearanceResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.appearanceResult`).d('外观是否合格'),
      lookupCode: 'YP.QIS.MS_APPEARANCE_RESULT',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW';
        },
        disabled: ({ record }) => {
          return ['NEW'].includes(record.get('inspectDocStatus'))
        },
      },
    },
    {
      name: 'ngReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngReason`).d('不合格原因'),
      dynamicProps: {
        disabled: ({ record }) => {
          return ['NEW'].includes(record.get('inspectDocStatus'))
        },
      },
    },
    {
      name: 'servicedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.servicedFlag`).d('是否维修'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => record?.get('inspectResult') === 'NG',
        disabled: ({ record }) => record.get('inspectDocStatus') === 'NEW' || record?.get('inspectResult') !== 'NG',
      },
    },
    {
      name: 'temperature',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.temperature`).d('温度（°C）'),
      dynamicProps: {
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW';
        },
        disabled: ({ record }) => {
          return ['NEW'].includes(record.get('inspectDocStatus'))
        },
      },
    },
    {
      name: 'humidness',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.humidness`).d('湿度（%rh）'),
      dynamicProps: {
        required: ({ record }) => {
          return record.get('inspectDocStatus') !== 'NEW';
        },
        disabled: ({ record }) => {
          return ['NEW'].includes(record.get('inspectDocStatus'))
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      dynamicProps: {
        disabled: ({ record }) => {
          return ['NEW'].includes(record.get('inspectDocStatus'))
        },
      },
    },
    {
      name: 'reviewByName',
      label: intl.get(`${modelPrompt}.reviewByName`).d('审批人'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'reviewDate',
      label: intl.get(`${modelPrompt}.reviewDate`).d('审批时间'),
      type: FieldType.dateTime,
      disabled: true,
    },
    {
      name: 'rejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
      disabled: true,
    },
    {
      name: 'overtimeFileUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.overtimeFile`).d('超期改善单'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
      },
    },
    {
      name: 'ncFileUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.ncFile`).d('不合格通知单'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !['RELEASED', 'REJECTED'].includes(record.get('inspectDocStatus'));
        },
      },
    },
    {
      name: 'cancelReason',
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消原因'),
      type: FieldType.string,
    },
    {
      name: 'cancelDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelDate`).d('取消时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/internal-info/ui`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && datas.rows) {
            if (datas.rows.inspectorId === 0) {
              datas.rows.inspectorId = null;
            }
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
});



const recordDS: () => DataSetProps = (dataType, siteId) => ({
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'sequenceNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequenceNumber`).d('顺序'),
      dynamicProps: {
        defaultValue: ({ dataSet }) => {
          let maxNum = 0;
          dataSet.forEach(_record => {
            if (_record?.get('sequenceNumber') > maxNum) {
              maxNum = _record?.get('sequenceNumber');
            }
          });
          return maxNum + 1;
        },
      },
    },
    {
      name: 'toolLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.msToolManageCode`).d('标准量具编码'),
      lovCode: 'YP.QIS.STANDARD_MS_TOOL',
      multiple: true,
      ignore: FieldIgnore.always,
      lovPara: { tenantId, siteId },
    },
    {
      name: 'toolCode',
      bind: 'toolLov.toolCode',
    },
    {
      name: 'msToolManageIdList',
      bind: 'toolLov.msToolManageId',
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardSpeciesName`).d('标准量具种别'),
    },
    {
      name: 'verificationExpDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationExpDate`).d('标准量具有效期'),
    },
    {
      name: 'modelName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardModelName`).d('标准量具型号'),
    },
    {
      name: 'usingStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardUsingStatus`).d('标准量具状态'),
      lookupCode: 'QIS.MS_TOOL_USING_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'modelRange',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelRange`).d('量程'),
    },
    {
      name: 'resolution',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.resolution`).d('分辨力'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.inspectFileUuid`).d('证书'),
    },
    {
      name: 'standardValue',
      label: intl.get(`${modelPrompt}.standardValue`).d('标准值'),
      dynamicProps: {
        type: () => {
          if (dataType === 'VALUE') {
            return FieldType.number
          }
          return FieldType.string
        },
      },
    },
    {
      name: 'actualValue',
      label: intl.get(`${modelPrompt}.actualValue`).d('被测仪器示值'),
      // required: true,
      dynamicProps: {
        type: () => {
          if (dataType === 'VALUE') {
            return FieldType.number
          }
          return FieldType.string
        },
      },
    },
    {
      name: 'deviation',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.deviation`).d('误差'),
      // dynamicProps: {
      //   defaultValue: ({ record }) => {
      //     console.log('7777', dataType);
      //     if (dataType === 'VALUE') {
      //       debugger
      //       // 误差=标准值-被测仪器示值
      //       return record.get('standardValue') - record.get('actualValue');
      //     }
      //   },
      // },
    },
    {
      name: 'deviationStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.deviation`).d('误差'),
    },
    {
      name: 'lowerLimitPercent',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lowerLimitPercent`).d('下限百分比'),
      min: 0,
      max: 100,
      computedProps: {
        disabled: ({ record }) => !record?.get('standardValue') || (!record?.get('lowerLimitPercent') && record?.get('lowerLimitValue')),
      },
    },
    {
      name: 'lowerLimitValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lowerLimitValue`).d('下限值'),
      computedProps: {
        disabled: ({ record }) => record?.get('lowerLimitPercent'),
      },
    },
    {
      name: 'upperLimitPercent',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.upperLimitPercent`).d('上限百分比'),
      min: 0,
      max: 100,
      computedProps: {
        disabled: ({ record }) => !record?.get('standardValue') || (!record?.get('upperLimitPercent') && record?.get('upperLimitValue')),
      },
    },
    {
      name: 'upperLimitValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.upperLimitValue`).d('上限值'),
      computedProps: {
        disabled: ({ record }) => record?.get('upperLimitPercent'),
        min: ({ record }) => record?.get('lowerLimitValue'),
      },
    },
    {
      name: 'inspectDate',
      type: FieldType.date,
      format: 'YYYY-MM-DD',
      label: intl.get(`${modelPrompt}.inspectDate`).d('实校日期'),
      required: true,
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.isOk`).d('是否合格'),
      lookupCode: 'YP.OIS.INSPECT_RESUALT',
      lovPara: { tenantId },
      required: true,
      dynamicProps: {
        defaultValue: ({ record }) => {
          if (record.get('actualValue') && record.get('lowerLimit') && record.get('upperLimit')){
            if (record.get('actualValue') > record.get('lowerLimit') && record.get('actualValue') > record.get('upperLimit')) {
              return 'OK';
            }
            return 'NG';
          }
          return null;
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

export { outerDetailDS, insideDetailDS, recordDS };
