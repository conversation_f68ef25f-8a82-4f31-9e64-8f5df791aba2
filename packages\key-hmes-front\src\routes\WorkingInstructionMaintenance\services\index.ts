/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-24 09:48:03
 * @LastEditTime: 2023-07-26 10:22:53
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { HZERO_FILE } from 'utils/config';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 查询作业指导书
 * @function FetchWorkingInstruction
 * @returns  fetch Promise
 */
export function FetchWorkingInstruction() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-sop-header/detail`,
    method: 'GET',
  };
}

/**
 * 保存作业指导书
 * @function SaveWorkingInstruction
 * @returns  fetch Promise
 */
export function SaveWorkingInstruction() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-sop-header/save`,
    method: 'POST',
  };
}


// 初始化附件ID
export function InitAttachmentUUID() {
  return {
    url: `${HZERO_FILE}/v1/${tenantId}/files/uuid`,
    method: 'POST',
  };
}

export interface UploadFilePayload {
  bucketName: 'key-hmes', // 文件保存的桶名
  attachmentUUID: string, // 附件ID
  file: File, // 文件流
  directory: 'working-instruction-maintenance', // 文件目录，支持多级
  fileName?: string, // 文件名，不指定时使用文件原本的名字
  docType?: number, // 锁定文件类型为 application/octet-stream，默认值0
  storageCode?: string, // 存储配置编码，不指定时使用默认配置
}

// 附件上传
export function UploadFile() {
  return {
    url: `${HZERO_FILE}/v1/${tenantId}/files/attachment/multipart`,
    method: 'POST',
  };
}

// 删除文件
export function DeleteFile() {
  return {
    url: `${HZERO_FILE}/v1/${tenantId}/files/delete-by-url?bucketName=key-hmes`,
    method: 'POST',
  };
}

export interface PreviewFilePayload {
  bucketName: 'key-hmes', // 文件保存的桶名
  storageCode?: string, // 存储配置编码，不指定时使用默认配置
  url: string, // 文件url
}

// 预览指定文件
export function PreviewFile() {
  return {
    url: `${HZERO_FILE}/v1/${tenantId}/file-preview/by-url`,
    method: 'GET',
  };
}
