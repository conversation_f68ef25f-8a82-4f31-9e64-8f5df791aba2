/**
 * @Description: 量具种类维护
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21 09:29:43
 * @LastEditTime: 2023-06-21 16:56:17
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { DataSet,Select,Table} from 'choerodon-ui/pro';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { openTab } from 'utils/menuTab';
import { getCurrentOrganizationId } from 'utils/utils';
import { Button as PermissionButton } from 'components/Permission';
import queryString from 'query-string';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import ExcelExport from 'components/ExcelExport';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import Axios from 'axios';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { tableDS } from './stories/tableDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.qms.toolTypeMaintain';

const kingMaintenance = props => {
  const {
    tableDs,
  } = props;

  const { Option } = Select;
  const [list,setList] = useState([]);

  useEffect(() => {
    Axios({
      url: `/hpfm/v1/${tenantId}/lovs/value/batch?data=QIS.MS_TOOL_CTGR`,
      method: 'GET',
    }).then(res => {
      const { data } = res;
      setList(data)
    })
  },[])

  const getStatus = list.map((ele) => (
    <Option key={ele.orderSeq} value={ele.meaning}>
      {ele.meaning}
    </Option>
  ));

  const handleChage = (value) => {
    const item = list.filter((val) => (val.meaning === value));
    tableDs.current.set('categoryName', item[0].meaning)
    tableDs.current.set('categoryCode', item[0].value);
  }

  const columns: ColumnProps[] = [
    {
      name: 'siteObj',
      align: ColumnAlign.center,
      width: 120,
      editor:(record) => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'siteName',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'categoryCode',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'categoryName',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ record }) => {
        if (record?.status === 'add' || record?.getState('editing')) {
          return <Select required onChange={handleChage} value={record.get('categoryName')}>{getStatus}</Select>
        }
        return <span>{record?.get('categoryName')}</span>
      },
    },
    {
      name: 'speciesCode',
      align: ColumnAlign.center,
      width: 120,
      editor: (record) => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'speciesName',
      align: ColumnAlign.center,
      width: 120,
      editor:(record) => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'verificationMethod',
      align: ColumnAlign.center,
      width: 120,
      editor:(record) => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'verificationPeriod',
      align: ColumnAlign.center,
      width: 120,
      editor:(record) => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'reminderPeriod',
      align: ColumnAlign.center,
      width: 120,
      editor:(record) => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'enableFlag',
      align: ColumnAlign.center,
      width: 120,
      editor:(record) => record.status === 'add' || record?.getState('editing'),
    },
    {
      title: intl.get(`${modelPrompt}.operation`).d('操作'),
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ record, dataSet }) =>
        record?.status === 'add' || record?.getState('editing') ? (
          <>
            <a
              onClick={() => {
                if (record.status === 'add') {
                  dataSet?.remove(record);
                } else {
                  record.reset();
                  record.setState('editing', false);
                }
              }}
            >
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </a>
            <a
              style={{ marginLeft: 5 }}
              onClick={() => {
                dataSet?.submit().then((res) => {
                  if (res) {
                    dataSet.query();
                    record.setState('editing', false);
                  }
                });
              }}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </a>
          </>
        ) : (
          <>
            <a
              onClick={() => {
                record?.setState('editing', true);
              }}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </a>
          </>
        ),
      lock: ColumnLock.right,
    },
  ];

  const handleBatchExport = () => {
    openTab({
      key: `/hmes/measure-have/comment-import/YP.QIS_MS_TOOL_TYPE`,
      title: 'hzero.common.title.import',
      search: queryString.stringify({
        action: intl.get(`tarzan.common.button.import`).d('导入'),
      }),
    });
  };

  const handleAdd = () => {
    tableDs.create({}, 0);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('量具种类数据维护')}>
        <ExcelExport
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-types/export`}
          method="get"
          otherButtonProps={{ type: 'primary' }}
          queryParams={tableDs.toData()}
        >
          {intl.get('tarzan.common.button.export').d('导出')}
        </ExcelExport>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `htms.common.button.export`,
              type: 'button',
              meaning: '导入',
            },
          ]}
          onClick={handleBatchExport}
        >
          {intl.get(`${modelPrompt}.button.import`).d('导入')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `tarzan.hwms.inspectionInfoManagement.new`,
              type: 'button',
              meaning: '新建',
            },
          ]}
          onClick={handleAdd}
        >
          {intl.get(`${modelPrompt}.create`).d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          searchCode="kingMaintenance"
          customizedCode="kingMaintenance"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet(tableDS());
      return {
        tableDs,
      };
    },
  )(kingMaintenance),
);
