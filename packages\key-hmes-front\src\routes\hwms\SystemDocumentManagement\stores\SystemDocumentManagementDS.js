/**
 * @Description: 抽样计划模板维护DS
 * @Author: <<EMAIL>>
 * @Date: 2021-08-16 09:54:13
 * @LastEditTime: 2022-11-09 14:38:32
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.qualitySystemDocumentManagement';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();
// const prefix = '/tznq-40695';

const tableDS = () => ({
  selection: 'multiple',
  autoQuery: false,
  autoQueryAfterSubmit: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'fileCode',
      type: FieldType.string,
      label: '文件编号',
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: '文件名称',
    },
    {
      name: 'fileLevel',
      type: FieldType.string,
      label: '文件级别',
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.FILE_LEVEL',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: '编制专业',
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: 'always',
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'editedDepartment',
      type: FieldType.number,
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsibleDeptCode',
      bind: 'departmentLov.unitCode',
    },
    {
      name: 'editedDepartmentName',
      bind: 'departmentLov.unitName',
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: '编制人',
      lovCode: 'MT.USER.ORG',
      ignore: 'always',
      textField: 'realName',
      lovPara: {
        // dataType: 'CALCULATE_FORMULA',
        tenantId,
      },
    },
    {
      name: 'editedBy',
      bind: 'responsLov.id',
    },
    {
      name: 'fileStatus',
      type: FieldType.string,
      label: '文件状态',
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.FILE_STATUS',
      defaultValue: 'PUBLISHED',
    },
    {
      name: 'editedDateFrom',
      type: FieldType.date,
      label: '编制日期从',
      max: 'editedDateTo',
    },
    {
      name: 'editedDateTo',
      type: FieldType.date,
      label: '编制日期至',
      min: 'editedDateFrom',
    },
    {
      name: 'publishDateFrom',
      type: FieldType.date,
      label: '发布日期从',
      max: 'publishDateTo',
    },
    {
      name: 'publishDateTo',
      type: FieldType.date,
      label: '发布日期至',
      min: 'publishDateFrom',
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: '所属过程',
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
    },
    // {
    //   name: 'createLov',
    //   type: FieldType.object,
    //   label: '创建人',
    //   lovCode: 'MT.USER.ORG',
    //   ignore: 'always',
    //   lovPara: {
    //     // dataType: 'CALCULATE_FORMULA',
    //     tenantId,
    //   },
    // },
    // {
    //   name: 'createdBy',
    //   bind: 'createLov.id',
    // },
    {
      name: 'currentFlag',
      type: FieldType.boolean,
      label: '是否最新',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
  fields: [
    {
      name: 'seq',
      type: FieldType.number,
      label: '序号',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: '工厂',
    },
    {
      name: 'fileCode',
      type: FieldType.string,
      label: '文件编码',
      disabled: true,
    },
    {
      name: 'fileName',
      type: FieldType.string,
      required: true,
      label: '文件名称',
      // dynamicProps: {
      //   disabled: ({ record }) => {
      //     return record.get('fileStatus') !== 'STAGING';
      //   },
      // },
    },
    {
      name: 'fileLevel',
      type: FieldType.string,
      label: '文件级别',
      required: true,
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.FILE_LEVEL',
      // dynamicProps: {
      //   disabled: ({ record }) => {
      //     return record.get('fileStatus') !== 'STAGING';
      //   },
      // },
    },
    {
      name: 'processType',
      type: FieldType.string,
      label: '程序类型',
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PROCESS_TYPE',
      // dynamicProps: {
      //   disabled: ({ record }) => {
      //     return record.get('fileStatus') !== 'STAGING';
      //   },
      // },
    },
    {
      name: 'version',
      type: FieldType.string,
      label: '版本号',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: '编制专业',
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: 'always',
      required: true,
      textField: 'unitName',
      lovPara: { tenantId },
      computedProps: {
        disabled: ({ record, dataSet }) => {
          // fileStatus为STAGING且编制人为当前操作人时可编辑
          // fileStatus为UNPUBLISHED，fileLevel为SECOND且编制人为当前操作人或管理员时可编辑
          // 其余情况不可编辑
          return !(
            (record.get('fileStatus') === 'STAGING' && record?.get('editedBy') === userInfo.id) ||
            (record.get('fileStatus') === 'UNPUBLISHED' &&
            ['FIRST', 'SECOND', 'THIRD'].includes(record.get('fileLevel')) &&
              (record?.get('editedBy') === userInfo.id ||
              dataSet.getState('adminFlag'))) ||
            (dataSet?.getState('editType') === 'upgrades' &&
            record.get('fileStatus') === 'PUBLISHED' &&
            ['FIRST', 'SECOND', 'THIRD'].includes(record.get('fileLevel')))
          );
        },
        lovPara: ({ record, dataSet }) => {
          if ((record.get('fileStatus') === 'UNPUBLISHED' || dataSet?.getState('editType') === 'upgrades')
            && record.get('fileLevel') === 'THIRD') {
            return {
              tenantId,
              editedDepartmentParent: record?.get('originEditedDepartmentParent'),
            }
          }
          return { tenantId };
        },
      },
    },
    {
      name: 'editedDepartment',
      type: FieldType.number,
      bind: 'departmentLov.unitId',
    },
    {
      name: 'editedDepartmentName',
      label: '编制专业',
      bind: 'departmentLov.unitName',
    },
    // 打开抽屉时初始的编制部门
    {
      name: 'originEditedDepartmentParent',
    },
    {
      name: 'editedDepartmentParent',
      type: FieldType.number,
      bind: 'departmentLov.parentUnitId',
    },
    {
      name: 'editedDepartmentParentName',
      label: '编制部门',
      bind: 'departmentLov.parentUnitName',
      disabled: true,
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: '编制人',
      lovCode: 'MT.USER.ORG',
      ignore: 'always',
      textField: 'realName',
      lovPara: {
        // dataType: 'CALCULATE_FORMULA',
        tenantId,
      },
      computedProps: {
        disabled: ({ record, dataSet }) => {
          // fileStatus为STAGING 且编制人为当前操作人时可编辑
          // fileStatus为UNPUBLISHED，fileLevel为SECOND或THIRD 且编制人为当前操作人或管理员时可编辑
          // 其余情况不可编辑
          return !(
            (record.get('fileStatus') === 'STAGING' && record?.get('editedBy') === userInfo.id) ||
            (record.get('fileStatus') === 'UNPUBLISHED' &&
              ['FIRST', 'SECOND', 'THIRD'].includes(record.get('fileLevel')) &&
              (record?.get('editedBy') === userInfo.id ||
              dataSet.getState('adminFlag'))) ||
            (dataSet?.getState('editType') === 'upgrades' &&
            record.get('fileStatus') === 'PUBLISHED')
          );
        },
      },
    },
    {
      name: 'editedBy',
      bind: 'responsLov.id',
    },
    {
      name: 'editedByRealName',
      label: '编制人',
      bind: 'responsLov.realName',
    },
    {
      name: 'editedDate',
      type: FieldType.string,
      label: '编制日期',
      required: true,
      computedProps: {
        disabled: ({ record, dataSet }) => {
          // fileStatus为STAGING 且编制人为当前操作人时可编辑
          // fileStatus为UNPUBLISHED，fileLevel为SECOND或THIRD 且编制人为当前操作人或管理员时可编辑
          // 其余情况不可编辑
          return !(
            (record.get('fileStatus') === 'STAGING' && record?.get('editedBy') === userInfo.id) ||
            (record.get('fileStatus') === 'UNPUBLISHED' &&
              ['FIRST', 'SECOND', 'THIRD'].includes(record.get('fileLevel')) &&
              (record?.get('editedBy') === userInfo.id ||
              dataSet.getState('adminFlag'))) ||
            (dataSet?.getState('editType') === 'upgrades' &&
              record.get('fileStatus') === 'PUBLISHED')
          );
        },
      },
    },
    {
      name: 'publishDate',
      type: FieldType.string,
      label: '发布日期',
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: '所属过程',
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
      required: true,
      computedProps: {
        disabled: ({ record }) => {
          return !record.get('fileLevel') || record.get('fileLevel') === 'FORTH';
        },
        required: ({ record }) => {
          return !['FIRST', 'FORTH'].includes(record.get('fileLevel'));
        },
      },
    },
    {
      name: 'uuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.uuid`).d('上传文档'),
      // max: 9,
    },
    {
      name: 'upUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.upUuid`).d('上传文档'),
      // max: 9,
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('editType') === 'upgrades',
      },
    },
    {
      name: 'levelLimitLov',
      type: FieldType.object,
      label: '关联上级文件',
      lovCode: 'YP.QIS.FILE',
      ignore: 'always',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            limitFileLevel: 'SECOND,THIRD',
          };
        },
        disabled: ({ record }) => {
          return record.get('fileLevel') !== 'FORTH';
        },
        required: ({ record }) => {
          return record.get('fileLevel') === 'FORTH';
        },
      },
      // lovPara: {
      //   // dataType: 'CALCULATE_FORMULA',
      //   tenantId,
      // },
    },
    {
      name: 'parentFile',
      bind: 'levelLimitLov.fileId',
    },
    {
      name: 'parentFileName',
      bind: 'levelLimitLov.fileName',
    },
    {
      name: 'parentUuid',
      type: FieldType.string,
      label: '关联文件',
    },
    {
      name: 'fileStatus',
      type: FieldType.string,
      label: '文件状态',
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.FILE_STATUS',
      defaultValue: 'STAGING',
    },
    {
      name: 'status',
      type: FieldType.string,
      label: '状态',
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.FILE_STATUS',
    },
    {
      name: 'upgradeType',
      type: FieldType.string,
      label: '升版类型',
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.UPGRADE_TYPE',
      dynamicProps: {
        required: ({ dataSet }) => {
          return dataSet.getState('editType') === 'upgrades';
        },
      },
    },
    {
      name: 'currentFlag',
      type: FieldType.boolean,
      label: '是否最新',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      disabled: true,
    },
    {
      name: 'cancelReason',
      type: FieldType.string,
      label: '取消原因',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/list/for/ui`,
        method: 'GET',
      };
    },
    submit: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/confirm/for/ui`,
        method: 'POST',
        data: data[0],
      };
    },
  },
});

const historyDS = () => ({
  selection: false,
  autoQuery: false,
  fields: [
    {
      name: 'seq',
      type: FieldType.number,
      label: '序号',
    },
    {
      name: 'fileCode',
      type: FieldType.string,
      label: '文件编码',
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: '文件名称',
    },
    {
      name: 'version',
      type: FieldType.string,
      label: '版本号',
    },
    {
      name: 'editedDepartmentName',
      type: FieldType.string,
      label: '编制专业',
    },
    {
      name: 'editedDepartmentParentName',
      type: FieldType.string,
      label: '编制部门',
    },
    {
      name: 'editedByRealName',
      type: FieldType.string,
      label: '编制人',
    },
    {
      name: 'editedDate',
      type: FieldType.string,
      label: '编制日期',
    },
    {
      name: 'publishDate',
      type: FieldType.string,
      label: '发布日期',
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: '所属过程',
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
    },
    {
      name: 'fileStatus',
      type: FieldType.string,
      label: '状态',
      lookupCode: 'YP.QIS.FILE_STATUS',
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: '最后更新时间',
    },
    {
      name: 'lastUpdatedByRealName',
      type: FieldType.string,
      label: '最后更新人',
    },
    {
      name: 'operationTypeMeaning',
      type: FieldType.string,
      label: '操作类型',
      // lookupCode: 'MT.SAMPLING.STANDARD',
      // lovPara: {
      //   tenantId,
      // },
    },
    {
      name: 'uuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.uuid`).d('上传文档'),
      // max: 9,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-file-hiss/list/for/ui`,
        method: 'GET',
      };
    },
  },
});

const approveViewDS = () => ({
  selection: false,
  autoQuery: false,
  fields: [
    {
      name: 'seq',
      type: FieldType.number,
      label: '序号',
    },
    {
      name: 'fileCode',
      type: FieldType.string,
      label: '文件编码',
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: '文件名称',
    },
    {
      name: 'version',
      type: FieldType.string,
      label: '版本号',
    },
    {
      name: 'operationTypeMeaning',
      type: FieldType.string,
      label: '审批类型',
    },
    {
      name: 'approvedByName',
      type: FieldType.string,
      label: '审批人',
    },
    {
      name: 'approveResultMeaning',
      type: FieldType.string,
      label: '审批结果',
    },
    {
      name: 'approvedReason',
      type: FieldType.string,
      label: '审批意见',
    },
    {
      name: 'approvedDate',
      type: FieldType.string,
      label: '审批时间',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/approve/view/for/ui`,
        method: 'GET',
      };
    },
  },
});

const examineDS = () => ({
  autoCreate: true,
  selection: false,
  autoQuery: false,
  fields: [
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: '审核部门',
      required: true,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      multiple: true,
      ignore: 'always',
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'unitId',
      // type: FieldType.,
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsibleDeptCode',
      bind: 'departmentLov.unitCode',
    },
    {
      name: 'responsibleDeptName',
      bind: 'departmentLov.unitName',
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: '通知人员',
      required: true,
      lovCode: 'MT.USER.ORG',
      multiple: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'noticeId',
      bind: 'responsLov.id',
    },
    {
      name: 'applicationReason',
      type: FieldType.string,
      label: '申请理由',
      required: true,
    },
  ],
});

const formDS = () => ({
  selection: false,
  autoQuery: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编码'),
    },
    {
      name: 'version',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.version`).d('版本号'),
    },
    {
      name: 'editedDepartmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedDepartmentName`).d('编制专业'),
    },
    {
      name: 'editedDepartmentParentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedDepartmentParentName`).d('编制部门'),
    },
    {
      name: 'editedByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedByRealName`).d('编制人'),
    },
    {
      name: 'editedDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedDate`).d('编制日期'),
    },
    {
      name: 'publishDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.publishDate`).d('发布日期'),
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.affliatedProcess`).d('所属过程'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
    },
    {
      name: 'fileLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileLevel`).d('文件级别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_LEVEL',
    },
    {
      name: 'history',
      label: intl.get(`${modelPrompt}.historyVersion`).d('查看历史版本'),
    },
    {
      name: 'subFileList',
      label: intl.get(`${modelPrompt}.parentUuid`).d('关联文件'),
    },
    {
      name: 'uuid',
      type: FieldType.string,
      // label: '关联文件',
      // max: 9,
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.parentUuid`).d('关联文件'),
      // max: 9,
    },
    {
      name: 'parentUuid',
      type: FieldType.string,
      // label: '关联文件',
    },
    {
      name: 'relatedFileList',
      label: intl.get(`${modelPrompt}.relatedFileList`).d('相关文件'),
    },
    {
      name: 'abandonReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.abandonReason`).d('废弃理由'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/view/for/ui`,
        method: 'GET',
      };
    },
  },
});

const cancelReasonDS = () => ({
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'cancelReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消原因'),
    },
  ],
});

export { tableDS, historyDS, approveViewDS, examineDS, formDS, cancelReasonDS };
