/**
 * @Description: 选择资产按钮组件
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-03-30 11:43:05
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */
import React, { useMemo, useCallback } from 'react';
import { Button, Modal, Table, DataSet } from 'choerodon-ui/pro';
import { Tooltip } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps, ColumnRenderProps } from 'choerodon-ui/pro/lib/table/Column';
import { Renderer } from 'choerodon-ui/pro/lib/field/interface';
import { assetModalDS } from '../Stores/modalDs';
import getLang from '../Langs';

const modalKey = Modal.key();
const AssetBtn = props => {
  const { style, transactionTypeId, onOk, assetIdList, disabled } = props;
  const columns: ColumnProps[] = useMemo(() => {
    const columnsRender: Renderer<ColumnRenderProps> = ({ value, record }) => {
      const { lineNum, processStatus, transactionTypeName, changeNum } = record?.toData();
      const flag = lineNum && !['CANCELED', 'COMPLETED'].includes(processStatus);
      if (flag) {
        const html =
          !transactionTypeName && !changeNum && !lineNum
            ? ''
            : `${transactionTypeName || ''}:${changeNum || ''}-${lineNum || ''}`;
        return (
          <Tooltip placement="topRight" title={html}>
            <span style={{ color: 'rgba(0, 0, 0, 0.25)' }}>{value}</span>
          </Tooltip>
        );
      }
      return value;
    };
    return [
      {
        name: 'assetNum',
        width: 150,
        renderer: columnsRender,
      },
      {
        name: 'assetDesc',
        renderer: columnsRender,
      },
      {
        name: 'assetStatusName',
        width: 100,
        renderer: columnsRender,
      },
    ];
  }, []);

  const handleOpenModal = useCallback(() => {
    // 每次打开创建新的ds，重置查询条件、分页、缓存记录
    const assetModalDs = new DataSet(assetModalDS());
    assetModalDs.setQueryParameter('transactionTypeId', transactionTypeId);
    assetModalDs.setQueryParameter('removeAssetId', assetIdList);
    assetModalDs.query();
    Modal.open({
      key: modalKey,
      style: {
        width: 650,
      },
      title: getLang('ASSET_OR_EQUIPMENT'),
      children: (
        <Table
          key="assetTransactionsAssetModal"
          customizedCode="AORI.ASSET_TRANSACTIONS.ASSET_MODAL"
          style={{ height: 300 }}
          queryFieldsLimit={2}
          columns={columns}
          dataSet={assetModalDs}
        />
      ),
      onOk: () => {
        if (assetModalDs.selected.length > 0) {
          onOk(assetModalDs.toJSONData());
        }
      },
      okText: getLang('SAVE'),
    });
  }, [transactionTypeId, assetIdList]);

  return (
    <Button color={ButtonColor.primary} icon="add" style={style} onClick={handleOpenModal} disabled={disabled}>
      {getLang('CHOOSE_ASSET')}
    </Button>
  );
};

export default AssetBtn;
