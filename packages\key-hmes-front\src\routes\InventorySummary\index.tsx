import React, { useMemo } from 'react';
import { Table, DataSet, Modal, TextField } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import { useRequest } from '@components/tarzan-hooks';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { tableDS, detailDS } from './stores';


const tenantId = getCurrentOrganizationId();
const modelPrompt = 'inventorySummary';

const InventorySummary = (props) => {
  const {
    tableDs,
    detailDs,
  } = props;

  // 入库&出货明细-数据获取-接口
  const { run: remarkSaveApi } = useRequest({
    url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/wms-report-edit/weekly-report/save-remark/for/ui`,
    method: 'POST',
  }, {
    manual: true,
    needPromise: true,
  });

  const columns: ColumnProps[] = useMemo(() => {
    const updateTime = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      const hour = now.getHours();
      const minute = now.getMinutes();
      const second = now.getSeconds();
      // 格式化为两位数
      const formattedDate = `${year  }-${  month < 10 ? `0${  month}` : month  }-${  day < 10 ? `0${  day}` : day  } ${  hour < 10 ? `0${  hour}` : hour  }:${  minute < 10 ? `0${  minute}` : minute  }:${ second < 10 ? `0${  second}` : second}`;
      return formattedDate;
    }
    return [
      {
        title: '查询日期',
        align: ColumnAlign.right,
        children: [
          {
            children:[
              {
                name: 'bomCode',
              },
              {
                name: 'modelCode',
              },
              {
                name: 'materialCode',
                footer: () => (<span>{intl.get(`${modelPrompt}.summation`).d('合计')}</span>),
                footerStyle: {
                  textAlign: 'center',
                },
              },
            ],
          },
        ],
      },
      {
        title: `${updateTime()}`,
        align: ColumnAlign.center,
        children:[
          {
            title: intl.get(`${modelPrompt}.dateAssembly`).d('装配日期'),
            children: [
              {
                name: 'windTimeSum10',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('windTimeSum10') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
                renderer: ({value,record}) => (<a onClick={()=>{handleOpenModal(record,'-4', 'WIND')}}>{value}</a>),
              },
              {
                name: 'windTimeSum11',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('windTimeSum11') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
                renderer: ({value,record}) => (<a onClick={()=>{handleOpenModal(record,'4-7', 'WIND')}}>{value}</a>),
              },
              {
                name: 'windTimeSum12',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('windTimeSum12') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
                renderer: ({value,record}) => (<a onClick={()=>{handleOpenModal(record,'7-12', 'WIND')}}>{value}</a>),
              },
              {
                name: 'windTimeSum13',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('windTimeSum13') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
                renderer: ({value,record}) => (<a onClick={()=>{handleOpenModal(record,'12-24', 'WIND')}}>{value}</a>),
              },
              {
                name: 'windTimeSum14',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('windTimeSum14') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
                renderer: ({value,record}) => (<a onClick={()=>{handleOpenModal(record,'24-', 'WIND')}}>{value}</a>),
              },
              {
                name: 'windTimeSumTotal',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('windTimeSumTotal') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
              },
            ],
          },
          {
            title: intl.get(`${modelPrompt}.warehousingDate`).d('入库日期'),
            align: ColumnAlign.center,
            children: [
              {
                name: 'inLocatorTimeSum20',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('inLocatorTimeSum20') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
                renderer: ({value,record}) => (<a onClick={()=>{handleOpenModal(record,'-1', 'IN_LOCATOR')}}>{value}</a>),
              },
              {
                name: 'inLocatorTimeSum21',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('inLocatorTimeSum21') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
                renderer: ({value,record}) => (<a onClick={()=>{handleOpenModal(record,'1-3', 'IN_LOCATOR')}}>{value}</a>),
              },
              {
                name: 'inLocatorTimeSum22',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('inLocatorTimeSum22') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
                renderer: ({value,record}) => (<a onClick={()=>{handleOpenModal(record,'3-6', 'IN_LOCATOR')}}>{value}</a>),
              },
              {
                name: 'inLocatorTimeSum23',
                align: ColumnAlign.center,
                // @ts-ignore
                footer: ({ dataSet }) => {
                  const totality = dataSet?.data?.map(record=> Number(record.get('inLocatorTimeSum23') || 0)).reduce((prev,curr)=> prev + curr);
                  return (<>{totality || 0}</>)
                },
                footerStyle: {
                  textAlign: 'center',
                },
                renderer: ({value,record}) => (<a onClick={()=>{handleOpenModal(record,'6-', 'IN_LOCATOR')}}>{value}</a>),
              },
            ],
          },
        ],
      },
    ];
  }, []);

  const columnsDetail = (detailType) => {
    return [
      {
        name: 'numMonth',
        title: detailType === 'WIND' ? intl.get(`${modelPrompt}.numMonth`).d('距今月数（装配日期）') : intl.get(`${modelPrompt}.numMonth`).d('距今月数（入库日期）'),
        width: 160,
      },
      {
        name: 'sum',
        title: intl.get(`${modelPrompt}.sum`).d('数量'),
      },
      {
        name: 'locatorInfo',
        title: intl.get(`${modelPrompt}.locatorInfo`).d('库位'),
      },
      {
        name: 'remark',
        title: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
      {
        name: 'latestRemark',
        title: intl.get(`${modelPrompt}.latestRemark`).d('最新库存处理意见'),
        width: 150,
        editor: record => <TextField name='latestRemark' onChange={() => handleRemarkSave(record)} />,
      },
      {
        name: 'lastRemark',
        title: intl.get(`${modelPrompt}.lastRemark`).d('上月库存处理意见'),
        width: 150,
      },
    ]
  };

  const groups = (detailType) => {
    return [
      {
        name: 'materialName',
        type: 'column',
        columnProps: {
          title: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
        },
      },
      {
        name: 'materialCode',
        type: 'column',
        columnProps: {
          title: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        },
      },
      {
        name: 'bomCode',
        type: 'column',
        columnProps: {
          title: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
        },
      },
      {
        name: 'modelCode',
        type: 'column',
        columnProps: {
          title: intl.get(`${modelPrompt}.modelCode`).d('规格'),
        },
      },
      {
        name: 'uomName',
        type: 'column',
        columnProps: {
          title: intl.get(`${modelPrompt}.uomName`).d('主计量单位'),
        },
      },
      {
        name: 'lot',
        type: 'column',
        columnProps: {
          title: intl.get(`${modelPrompt}.lot`).d('批次号（箱号、托盘号）'),
          width: 180,
        },
      },
      {
        name: 'freezeFlag',
        type: 'column',
        columnProps: {
          title: intl.get(`${modelPrompt}.freezeFlag`).d('冻结状态'),
        },
      },
      {
        name: 'property',
        type: 'column',
        columnProps: {
          title: intl.get(`${modelPrompt}.property`).d('产品属性'),
        },
      },
      {
        name: 'inLocatorTime',
        type: 'column',
        columnProps: {
          title: detailType === 'WIND' ? intl.get(`${modelPrompt}.windTime`).d('装配时间'): intl.get(`${modelPrompt}.windTime`).d('入库时间'),
        },
      },
    ]
  };

  const handleOpenModal = (record, type, detailType) => {
    const gradeHead = tableDs.queryDataSet.current.get('grade');
    const locatorIdsHead = tableDs.queryDataSet.current.get('locatorIds');
    detailDs.setQueryParameter('grade', gradeHead);
    detailDs.setQueryParameter('locatorIds', locatorIdsHead);
    detailDs.setQueryParameter('materialId', record?.get('materialId'));
    detailDs.setQueryParameter('timeRange', type);
    detailDs.setQueryParameter('detailType', detailType);
    detailDs.query().then(() => {
      Modal.open({
        title: intl.get(`${modelPrompt}.detail`).d('详情'),
        key: Modal.key(),
        okButton: false,
        closable: true,
        drawer: true,
        style: {
          width: "100%",
        },
        children:(
          <Table
            customizedCode="inventorySummaryDetail"
            groups={groups(detailType) as any}
            dataSet={detailDs}
            columns={columnsDetail(detailType)}
            virtual
            border
          />
        ),
      })
    });
  }

  // 备注保存
  const handleRemarkSave = (record) => {
    remarkSaveApi({
      params: {
        keyId: record.get('keyId'),
        dataType: record.get('dataType'),
        remark: record.get('latestRemark'),
        dataObject: record.get('dataType') === 'FINISHED_INVENTORY_WIND'
          ? `${record.get('materialId')}_${record.get('lot')}_${record.get('windTime')}_${record.get('currentMonth')}`
          : `${record.get('materialId')}_${record.get('lot')}_${record.get('currentMonth')}`,
      },
      onSuccess: () => detailDs.query(),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('成品库存汇总报表')} />
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="inventorySummary"
          customizedCode="inventorySummary"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      const detailDs = new DataSet({
        ...detailDS(),
      })
      return {
        tableDs,
        detailDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(InventorySummary),
);
