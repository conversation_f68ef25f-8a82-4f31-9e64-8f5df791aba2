/**
 * @Description: 容器类型维护（重构）-入口页面DS
 * @Author: <EMAIL>
 * @Date: 2022/7/11 10:32
 * @LastEditTime: 2023-05-30 17:26:14
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSet } from 'choerodon-ui/pro';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.hagd.containerType.model.containerType';

const tenantId = getCurrentOrganizationId();

// 包装等级下拉框数据源
const packingLevelOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        data: { typeGroup: 'PACKING_LEVEL', module: 'MATERIAL_LOT' },
      };
    },
  },
});

const tableDS: () => DataSetProps = () => ({
  primaryKey: 'containerTypeId',
  selection: false,
  autoQuery: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('容器类型编码'),
    },
    {
      name: 'containerTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeDescription`).d('容器类型描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'APEX_WMS.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'packingLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packingLevel`).d('包装等级'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=PACKING_LEVEL`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'containerClassification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerClassification`).d('容器类型'),
      lookupCode: 'APEX_WMS.CONTAINER_CLASSIFICATION',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
  ],
  fields: [
    {
      name: 'containerTypeId',
      type: FieldType.number,
    },
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('容器类型的编码'),
    },
    {
      name: 'containerTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeDescription`).d('容器类型的描述'),
    },
    {
      name: 'containerClassificationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerClassification`).d('容器分类'),
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'packingLevelDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packingLevel`).d('包装等级'),
    },
    {
      name: 'capacityQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.capacityQty`).d('最大数量'),
    },
    {
      name: 'maxLoadWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxLoadWeight`).d('最大承重'),
    },
    {
      name: 'weightUomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.weightUomName`).d('重量单位'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-container-type/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_LIST.LIST`,
        method: 'GET',
      };
    },
  },
});

export { tableDS, packingLevelOptionDs };
