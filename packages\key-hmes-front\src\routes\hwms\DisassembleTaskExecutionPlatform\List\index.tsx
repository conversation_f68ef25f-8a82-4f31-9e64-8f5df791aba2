import React, { FC, useEffect, useState } from 'react';
import { RouteComponentProps } from 'react-router';
import { Button, DataSet, Table, Modal, Form, TextArea } from 'choerodon-ui/pro';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import Axios from 'axios';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import notification from 'utils/notification';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { listPageFactory, cancelReasonDS } from '../stores/listPageFactory';

const modelPrompt = 'tarzan.qms.disassembleTaskExecutionPlatform';

interface InspectItemListProps extends RouteComponentProps {
  tableDs: DataSet;
  cancelReasonDs: DataSet;
  customizeTable: any;
}

const DisassembleTaskExecutionPlatform: FC<InspectItemListProps> = props => {
  const {
    match: { path },
    tableDs,
    cancelReasonDs,
    history,
    customizeTable,
  } = props;

  const [getFlag, setGetFlag] = useState<boolean>(true);
  const [cancelFlag, setCancelFlag] = useState<boolean>(true);
  const [editRoleFlag, setEditRoleFlag] = useState(false);

  useEffect(() => {
    getCanEditRoles('YP.QIS.TEARDOWN_TASK_EDIT').then((res: any) => {
      const _editRoleFlag = res?.lookupCode.map(item => item.value).includes(getCurrentUser().currentRoleCode);
      setEditRoleFlag(_editRoleFlag);
    })
  }, [])

  const handleUpdateSelect = () => {
    setGetFlag(getDisable());
    setCancelFlag(cancelDisable());
  };

  useDataSetEvent(tableDs, 'batchSelect', handleUpdateSelect);
  useDataSetEvent(tableDs, 'batchUnSelect', handleUpdateSelect);

  useEffect(() => {
    tableDs.query();
  }, [path])


  const getCanEditRoles = async (lookupCode: string) => {
    const res = await Axios.get(`/hpfm/v1/${getCurrentOrganizationId()}/lovs/value/batch`, {
      params: {
        lookupCode,
      },
    })
    return res;
  };


  const columns: ColumnProps[] = [
    {
      name: 'teardownTaskNum',
      minWidth: 160,
      renderer: ({ record, value }) => <a onClick={() => handleAdd(record?.get('teardownTaskId'))}>{value}</a>,
    },
    {
      name: 'materialLotType',
      minWidth: 150,
    },
    {
      name: 'teardownApplyNum',
      minWidth: 150,
    },
    {
      name: 'materialLotCode',
      minWidth: 150,
    },
    {
      name: 'model',
      minWidth: 150,
    },
    {
      name: 'materialCode',
      minWidth: 150,
    },
    {
      name: 'materialName',
      minWidth: 150,
    },
    {
      name: 'siteCode',
      minWidth: 150,
    },
    {
      name: 'prodLineCode',
      minWidth: 150,
    },
    {
      name: 'productFormCode',
      minWidth: 150,
    },
    {
      name: 'teardownTaskType',
      minWidth: 150,
    },
    {
      name: 'teardownReason',
      minWidth: 150,
    },
    {
      name: 'clientName',
      minWidth: 150,
    },
    {
      name: 'sampleDeliveryTime',
      minWidth: 150,
    },
    {
      name: 'teardownPersonName',
      minWidth: 150,
    },
    {
      name: 'teardownTaskTime',
      minWidth: 150,
    },
    {
      name: 'electricVoltage',
      minWidth: 150,
    },
    {
      name: 'operationName',
      minWidth: 150,
    },
    {
      name: 'operationDesc',
      minWidth: 150,
    },
    {
      name: 'teardownTaskStatus',
      minWidth: 150,
    },
    {
      name: 'teardownTaskScore',
      minWidth: 150,
    },
    {
      name: 'teardownTaskResult',
      minWidth: 150,
    },
    {
      name: 'judgeReason',
      minWidth: 150,
    },
    {
      name: 'teardownReviewedName',
      minWidth: 150,
    },
    {
      name: 'teardownStage',
      minWidth: 150,
    },
    {
      name: 'cancelReason',
      minWidth: 150,
    },
  ]

  // 新建
  const handleAdd = (id) => {
    history.push(`/hwms/disassemble/task-execution-platform/detail/${id}`);
  };

  // 领取
  const handleGet = () => {
    if (!editRoleFlag) {
      return notification.error({
        message: intl.get(`${modelPrompt}.pickError.nonEditorialRole`).d('当前角色不在拆解任务可编辑角色中，请检查！'),
      })
    }
    Axios.post(`${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-teardown-task/receive/ui`, {
      teardownTaskIds: tableDs.selected.map(item => item.get('teardownTaskId')),
    }).then((res: any) => {
      if (res && res?.success) {
        tableDs.query(tableDs.currentPage)
        return notification.success({
          message: res?.message || intl.get(`${modelPrompt}.notification.success`).d('操作成功'),
          description: '',
        })
      }
    }).catch((res: any) => {
      return notification.error({
        message: res.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
        description: '',
      })
    })
  };

  // 取消提示 -- 确认取消
  const handleCancelReasonOk = () => {
    return new Promise(async resolve => {
      const valRes = await cancelReasonDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      const res: any = await Axios.post(`${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-teardown-task/cancel/ui`, {
        teardownTaskIds: tableDs.selected.map(item => item.get('teardownTaskId')),
        cancelReason: cancelReasonDs.current?.get('cancelReason'),
      }).catch((_res: any) => {
        notification.error({
          message: _res.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          description: '',
        })
        return resolve(false);
      })
      if (res && res?.success) {
        tableDs.query(tableDs.currentPage)
        notification.success({
          message: res?.message || intl.get(`${modelPrompt}.notification.success`).d('操作成功'),
          description: '',
        })
        return resolve(true);
      }
    });
  };

  const handleCancel = () => {
    // if (!editRoleFlag) {
    //   return notification.error({
    //     message: intl.get(`${modelPrompt}.pickError.nonEditorialRole`).d('当前角色不在拆解任务可编辑角色中，请检查！'),
    //   })
    // }
    Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.common.button.cancel').d('取消'),
      destroyOnClose: true,
      style: {
        width: 420,
      },
      afterClose: () => cancelReasonDs?.reset(),
      onOk: handleCancelReasonOk,
      children: (
        <Form dataSet={cancelReasonDs}>
          <TextArea name="cancelReason" autoSize={{ minRows: 2, maxRows: 8 }} />
        </Form>
      ),
    });
  };

  // 领取按钮时候可以点击逻辑判断
  const getDisable = () => tableDs.selected.length === 0 || tableDs.selected.some((item: any) => {
    return !!item.get('teardownPersonId') || item.get('teardownTaskStatus') !== 'NEW';
  })


  // 领取按钮时候可以点击逻辑判断
  const cancelDisable = () => tableDs.selected.length === 0 || tableDs.selected.some((item: any) => {
    return item.get('teardownTaskStatus') !== 'NEW';
  })


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('拆解任务执行平台')}>
        <Button color={ButtonColor.primary} icon="add" disabled={!editRoleFlag} onClick={() => handleAdd('create')}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <Button onClick={handleGet} disabled={getFlag}>
          {intl.get(`${modelPrompt}.button.get`).d('领取')}
        </Button>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.cancel`,
              type: 'button',
              meaning: '列表页-取消按钮',
            },
          ]}
          disabled={cancelFlag}
          onClick={() => handleCancel()}
        >
          {intl.get('tarzan.common.button.cancel').d('取消')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.DISASSEMBLE_TASK_EXECUTION_PLATFORM.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.DISASSEMBLE_TASK_EXECUTION_PLATFORM.TABLE`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="DisassembleTaskExecutionPlatform"
            customizedCode="DisassembleTaskExecutionPlatform"
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = listPageFactory();
      const cancelReasonDs = new DataSet({
        ...cancelReasonDS(),
      });
      return {
        tableDs,
        cancelReasonDs,
      };
    },
    { cacheState: true },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.DISASSEMBLE.TASK.EXECUTION.PLATFORM.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.DISASSEMBLE.TASK.EXECUTION.PLATFORM.TABLE`],
    })(DisassembleTaskExecutionPlatform as any),
  ),
);
