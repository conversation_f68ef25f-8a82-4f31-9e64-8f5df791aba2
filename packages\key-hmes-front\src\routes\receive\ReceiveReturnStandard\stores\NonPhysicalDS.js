/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2021-12-14 16:12:24
 * @LastEditTime: 2023-05-18 15:33:27
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.receive.receiveReturn';
// const endUrl = '-30711';
const endUrl = '';

const tenantId = getCurrentOrganizationId();

const pickingDetailsDS = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  selection: false,
  paging: true,
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/wms-receive-return-bill/material-lot-manage/get/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.picking.materialCode`).d('物料编码'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.picking.qualityStatus`).d('质量状态'),
    },
    {
      name: 'sumActualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.picking.sumActualQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.picking.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'receiveLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.picking.receiveLocatorCode`).d('领取库位'),
    },
    {
      name: 'receiveDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.picking.receiveDate`).d('领取时间'),
    },
    {
      name: 'receiveBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.picking.receiveBy`).d('领取人'),
    },
  ],
});

const receivingDetailsDS = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  selection: false,
  paging: true,
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/wms-receive-return-bill/material-lot-manage/get/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiving.materialCode`).d('物料编码'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiving.qualityStatus`).d('质量状态'),
    },
    {
      name: 'sumActualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiving.sumActualQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiving.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiving.lot`).d('批次'),
    },
    {
      name: 'signedLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiving.signedLocatorCode`).d('签收库位'),
    },
    {
      name: 'signedDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiving.signedDate`).d('签收时间'),
    },
    {
      name: 'signedBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiving.signedBy`).d('签收人'),
    },
  ],
});

const returnDetailsDS = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  selection: false,
  paging: true,
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/wms-receive-return-bill/material-lot-manage/get/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.return.materialCode`).d('物料编码'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.return.qualityStatus`).d('质量状态'),
    },
    {
      name: 'sumActualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.return.sumActualQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.return.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}return.lot`).d('批次'),
    },
    {
      name: 'returnLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.return.returnLocatorCode`).d('退料库位'),
    },
    {
      name: 'returnDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.return.returnDate`).d('退料时间'),
    },
    {
      name: 'returnName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.return.returnName`).d('退料人'),
    },
  ],
});

export { pickingDetailsDS, receivingDetailsDS, returnDetailsDS };
