/**
 * @feature 新数据收集项维护-详情页基本信息
 * @date 2021-4-16
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { BASIC } from '@utils/config';
// eslint-disable-next-line import/named
import { collectionMethodPointOptionDs, valueTypePointOptionDs } from './EntranceDs';

const modelPrompt = 'tarzan.acquisition.dataItem.model.dataItem';

const tenantId = getCurrentOrganizationId();
/**
 * 基础信息
 */
const baseInfoDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  paging: false,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'tagCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.collectCode`).d('收集项编码'),
      required: true,
    },
    {
      name: 'tagDescription',
      label: intl.get(`${modelPrompt}.collectDescription`).d('收集项描述'),
      type: FieldType.intl,
    },
    {
      name: 'collectionMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.collectionMethod`).d('数据收集方式'),
      lovPara: { tenantId },
      options: collectionMethodPointOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      required: true,
    },
    {
      name: 'valueType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueType`).d('数据类型'),
      lovPara: { tenantId },
      options: valueTypePointOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      required: true,
    },
    {
      name: 'displayValueFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.displayValueFlag`).d('是否显示标准值'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'allowUpdateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.allowUpdateFlag`).d('是否允许更新'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'valueAllowMissing',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueAllowMissing`).d('允许缺失值'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'originOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.originOperationLov`).d('特殊参数来源工艺'),
      lovCode: 'MT.APS.OPERATION.URL',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'originOperationId',
      bind: 'originOperationLov.operationId',
    },
    {
      name: 'originOperationName',
      bind: 'originOperationLov.operationName',
    },
    {
      name: 'modifyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modifyFlag`).d('是否变更'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    // {
    //   name: 'minimumValue',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.minimumValue`).d('最小值'),
    //   dynamicProps: {
    //     disabled: ({ record }) => {
    //       return record.get('valueType') !== 'VALUE';
    //     },
    //   },
    // },
    // {
    //   name: 'maximalValue',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.maximalValue`).d('最大值'),
    //   dynamicProps: {
    //     disabled: ({ record }) => {
    //       return record.get('valueType') !== 'VALUE';
    //     },
    //   },
    // },
    {
      name: 'uomCodeObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.uomCode`).d('计量单位'),
      lovCode: 'MT.COMMON.UOM',
      locPara: {
        tenantId,
      },
      required: false,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('valueType') !== 'VALUE';
        },
      },
    },
    {
      name: 'uomCode',
      bind: 'uomCodeObject.uomCode',
    },
    {
      name: 'uomDesc',
      bind: 'uomCodeObject.uomName',
    },
    {
      name: 'uomId',
      bind: 'uomCodeObject.uomId',
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !['DECISION_VALUE', 'VALUE'].includes(record.get('valueType'));
        },
      },
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !['DECISION_VALUE', 'VALUE'].includes(record.get('valueType'))
        },
      },
    },
    {
      name: 'valueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
      multiple: ',',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('valueType') !== 'VALUE_LIST';
        },
        required: ({ record }) => {
          return record.get('valueType') === 'VALUE_LIST';
        },
      },
    },
    // {
    //   name: 'mandatoryNum',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.mandatoryNum`).d('必须采集条数'),
    // },
    // {
    //   name: 'optionalNum',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.optionalNum`).d('可选采集条数'),
    // },
    {
      name: 'dateFormat',
      type: FieldType.string,
      lookupCode: 'MT.MES_DATE_FORMAT',
      textField: 'meaning',
      valueField: 'value',
      label: intl.get(`${modelPrompt}.dateFormat`).d('日期格式'),
    },
    {
      name: 'specialRecordFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.specialRecordFlag`).d('特殊采集标识'),
    },
    {
      name: 'defaultNcCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defaultNcCodeLov`).d('默认不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !['DECISION_VALUE', 'VALUE'].includes(record.get('valueType'));
        },
      },
      textField: 'ncCode',
      valueField: 'ncCodeId',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'defaultNcCodeId',
      type: FieldType.number,
      bind: 'defaultNcCodeLov.ncCodeId',
    },
    {
      name: 'defaultNcCode',
      type: FieldType.string,
      bind: 'defaultNcCodeLov.ncCode',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag/detail/ui`,
        method: 'get',
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.mes.domain.entity.MtTag';
      return {
        data: { tagId: record.data.tagId },
        params: { fieldName, className },
        url: `${BASIC.HMES_BASIC}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
});

const numberListDS = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  dataKey: 'rows',
  fields: [
    { name: 'dataValue' },
    {
      name: 'multipleValue',
      dynamicProps: {
        range: ({ record }) => {
          return record.get('valueType') === 'section' ? ['leftValue', 'rightValue'] : false;
        },
      },
    },
    {
      name: 'leftChar',
      type: FieldType.string,
      defaultValue: '[',
    },
    {
      name: 'leftValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.leftValue';
          }
        },
      },
    },
    {
      name: 'rightChar',
      type: FieldType.string,
      defaultValue: ']',
    },
    {
      name: 'rightValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.rightValue';
          }
        },
      },
    },
    {
      name: 'valueType',
      type: FieldType.string,
      defaultValue: 'single',
    },
    {
      name: 'valueShow',
      type: FieldType.string,
    },
    {
      name: 'standard',
      type: FieldType.string,
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeLov`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: 'always',
      textField: 'ncCode',
      valueField: 'ncCodeId',
    },
    {
      name: 'ncCodeId',
      type: FieldType.number,
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      bind: 'ncCodeLov.ncCode',
    },
  ],
  events: {
    load: ({ dataSet }) => {
      if (!dataSet.length) {
        dataSet.loadData([{ leftChar: '(', rightChar: ')', valueType: 'single' }]);
        return;
      }
      dataSet.forEach(record => {
        if (record?.get('valueType') === 'section') {
          record?.set('multipleValue', {
            leftValue: record?.get('leftValue'),
            rightValue: record?.get('rightValue'),
          });
        } else {
          record?.set('multipleValue', record?.get('dataValue'));
        }
      });
    },
    update: ({ record, name }) => {
      switch (name) {
        case 'valueType':
        case 'leftValue':
        case 'rightValue':
        case 'leftChar':
        case 'rightChar':
        case 'multipleValue':
          handleUpdateRangeValue(record, name);
          break;
        default:
          break;
      }
    },
  },
});

const historicalQueryDS = () => ({
  autoCreate: false,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  queryFields: [
    {
      name: 'startDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.stratDate`).d('创建时间从'),
    },
    {
      name: 'endDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endDate`).d('创建时间至'),
    },
  ],
  fields: [
    {
      name: 'tagCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCode`).d('收集项编码'),
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('收集项描述'),
    },
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('是否启用'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
    {
      name: 'collectionMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.collectionMethod`).d('数据收集方式'),
      lookupCode: 'TAG_COLLECTION_METHOD',
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_COLLECTION_METHOD`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'valueType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueType`).d('数据类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON
        }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=GENERAL&typeGroup=TAG_VALUE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
    },
    {
      name: 'valueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
      multiple: true,
    },
    {
      name: 'dateFormat',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateFormat`).d('日期格式'),
      lookupCode: 'MT.MES_DATE_FORMAT',
    },
    {
      name: 'displayValueFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.displayValueFlag`).d('是否显示标准值'),
      lookupCode: 'MT.FLAG'
    },
    {
      name: 'valueAllowMissing',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueAllowMissing`).d('允许缺失值'),
      lookupCode: 'MT.FLAG'
    },
    {
      name: 'allowUpdateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.allowUpdateFlag`).d('允许更新值'),
      lookupCode: 'MT.FLAG'
    },
    {
      name: 'specialRecordFlag',
      type: FieldType.string,
      lookupCode: 'MT.FLAG',
      label: intl.get(`${modelPrompt}.specialRecordFlag`).d('特殊采集标识'),
    },
    {
      name: 'defaultNcCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defaultNcCode`).d('默认不良代码'),
    },
    {
      name: 'uomDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomDesc`).d('计量单位'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },



  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-tag-his/query/ui`,
        method: 'get',
      };
    },
  },
});

const handleUpdateRangeValue = (record, name) => {
  if (record.get('valueType') === 'section') {
    if (!record.get('leftChar')) {
      record.set('leftChar', '(');
    }
    if (!record.get('rightChar')) {
      record.set('rightChar', ')');
    }
    const leftValue = record.get('leftValue') || '';
    const rightValue = record.get('rightValue') || '';
    const leftChar = record.get('leftChar') === '(' ? '<' : '≤';
    const rightChar = record.get('rightChar') === ')' ? '<' : '≤';
    record.set('valueShow', `${leftValue}${leftChar}X${rightChar}${rightValue}`);
  } else if (name === 'valueType') {
    record.set('multipleValue', undefined);
  }
};

export { baseInfoDS, numberListDS, historicalQueryDS };
