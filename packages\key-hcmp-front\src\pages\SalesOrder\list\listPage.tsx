import { Card } from 'choerodon-ui';
import { Record } from 'choerodon-ui/dataset';
import { Action } from 'choerodon-ui/lib/trigger/enum';
import {
  Button,
  DataSet,
  DatePicker,
  Dropdown,
  Form,
  Lov,
  Menu,
  NumberField,
  Select,
  Table,
  TextField,
  useModal,
} from 'choerodon-ui/pro';
import { BASE_SERVER } from '@/utils/constants';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { AttributeDrawer } from 'hcm-components-front/lib/components/tarzan-ui';
import { ColumnProps, ColumnRenderProps } from 'choerodon-ui/pro/lib/table/Column';
import { SelectionMode, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import ExpandTable from 'hcmp-front-common/lib/components/ExpandTable';
import { PageHeaderWrapper } from 'components/Page';
import notification from 'hzero-front/lib/utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import withProps from 'hzero-front/lib/utils/withProps';
import React, { FC, useEffect, useState } from 'react';
import { RouteComponentProps } from 'react-router';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TagRender } from 'utils/renderer';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import SearchForm from './searchForm';
import headInfoDS from '../stores/headInfoDS';
import lineInfoDS from '../stores/lineInfoDS';

interface ListPageProps extends RouteComponentProps {
  headInfoDs: DataSet;
  lineInfoDs: DataSet;
}
const LARGE_SIZE_LABEL_WIDTH = 130;
const statusColorMap = [
  {
    status: 'USABLE',
    color: 'green',
  },
  {
    status: 'CANCEL',
    color: 'pink',
  },
  {
    status: 'CLOSED',
    color: 'blue',
  },
];
const intlPrefix = 'tarzan.aps.sales.managememt';
const ListPageComponent: FC<ListPageProps> = ({ headInfoDs, lineInfoDs }) => {
  const [headInfoTableColumns, setHeadInfoTableColumns] = useState<Array<any>>([]);
  const [lineInfoTableColumns, setLineInfoTableColumns] = useState<Array<any>>([]);
  const [headInfoSelected, setHeadInfoSelected] = useState(false);
  const [lineInfoSelectedRecords, setLineInfoSelectedRecords] = useState<Array<any>>([]);
  const [curSoId, setCurSoId] = useState(null);
  const Modal = useModal();

  useEffect(() => {
    headInfoDs.query();
  }, []);

  useEffect(() => {
    lineInfoDs.addEventListener('select', () => {
      setLineInfoSelectedRecords(lineInfoDs.selected);
    });
    lineInfoDs.addEventListener('unSelect', () => {
      setLineInfoSelectedRecords(lineInfoDs.selected);
    });
    lineInfoDs.addEventListener('selectAll', () => {
      setLineInfoSelectedRecords(lineInfoDs.selected);
    });
    lineInfoDs.addEventListener('unSelectAll', () => {
      setLineInfoSelectedRecords(lineInfoDs.selected);
    });
    return ()=>{
      lineInfoDs.removeEventListener('select', () => {
        setLineInfoSelectedRecords(lineInfoDs.selected);
      });
      lineInfoDs.removeEventListener('unSelect', () => {
        setLineInfoSelectedRecords(lineInfoDs.selected);
      });
      lineInfoDs.removeEventListener('selectAll', () => {
        setLineInfoSelectedRecords(lineInfoDs.selected);
      });
      lineInfoDs.removeEventListener('unSelectAll', () => {
        setLineInfoSelectedRecords(lineInfoDs.selected);
      });
    }
  }, [lineInfoDs.selected]);


  const headInfoTableBeforeColumns: ColumnProps[] = [
    {
      name: 'soNumber',
      width: LARGE_SIZE_LABEL_WIDTH,
      renderer: ({ record }) => (
        <a
          onClick={() => {
            handleHeadEdit(record);
          }}
        >
          {record?.get('soNumber')}
        </a>
      ),
    },
    { name: 'siteCode', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'customerCode' },
    { name: 'customerNameAlt' },
    { name: 'customerName', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'customerSiteCode', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'customerSiteName', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'soType', width: LARGE_SIZE_LABEL_WIDTH },
    {
      name: 'soStatus',
      width: LARGE_SIZE_LABEL_WIDTH,
      renderer: ({ value, text }: ColumnRenderProps) => {
        return TagRender(value, statusColorMap, text);
      },
    },
    { name: 'shipToSiteDesc', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'salesPerson' },
    { name: 'freezeFlag', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'remark' },
    { name: 'currencyCode' },
    { name: 'soCategory' },
    { name: 'salesChannel' },
    { name: 'commercialTerm', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'paymentTerm',width: LARGE_SIZE_LABEL_WIDTH },
  ];

  const lineInfoTableBeforeColumns: ColumnProps[] = [
    {
      name: 'soLineNum',
      width: LARGE_SIZE_LABEL_WIDTH,
      renderer: ({ record }) => (
        <a
          onClick={() => {
            handleLineEdit(record);
          }}
        >
          {record?.get('soLineNum')}
        </a>
      ),
    },
    { name: 'siteCode', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'materialCode' },
    { name: 'materialName' },
    { name: 'revisionCode' },
    { name: 'uomName' },
    { name: 'lineType' },
    {
      name: 'lineStatus',
      renderer: ({ value, text }: ColumnRenderProps) => {
        return TagRender(value, statusColorMap, text);
      },
    },
    { name: 'orderedQuantity' },
    { name: 'shippedQuantity' },
    { name: 'reservedQuantity' },
    { name: 'cancelledQuantity' },
    { name: 'scheduleShipDate', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'scheduleArrivalDate', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'actualShipDate', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'actualArrivalDate', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'contractNum' },
    { name: 'customerPoNum', width: LARGE_SIZE_LABEL_WIDTH },
    // { name: 'customerPoLineNum', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'parentLineNum', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'remark' },
    { name: 'freezeFlag', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'locatorCode', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'locatorName', width: LARGE_SIZE_LABEL_WIDTH },
    { name: 'shipMethod' },
    { name: 'packingInstructions' },
    { name: 'itemCategory' },
    { name: 'reason' },
  ];

  const changeHeadInfoTableColumn = val => {
    setHeadInfoTableColumns(val);
  };
  const changeLineInfoTableColumn = val => {
    setLineInfoTableColumns(val);
  };

  // 行信息状态变更
  const handleStateChange = async e => {
    if (lineInfoDs.selected.length === 0) {
      notification.warning({
        message: intl.get(`${intlPrefix}.notification.need.choose`).d('请至少勾选一条数据'),
      });
      return;
    }
    const soLineIdList: any[] = [];
    lineInfoDs.selected.forEach(record => {
      soLineIdList.push(record.get('soLineId'));
    });
    const requestUrl = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-order-lines/update/status/for/ui`;
    const res = await request(requestUrl, {
      method: 'POST',
      body: {
        soLineIdList,
        lineStatus: e.key,
        // targetStatusMeaning: e.item.props.children,
      },
    });
    if (res && res.success) {
      notification.success({
        message: res?.message,
      });
      await lineInfoDs.query();
    } else {
      const msg = res.message.split('！').map(v => {
        return v ? <div>{v}!</div> : <></>;
      });
      notification.error({
        message: msg,
      });
    }
  };

  // 状态变更下拉选择菜单项目
  const dropdownItem = (
    <Menu onClick={handleStateChange}>
      <Menu.Item key="CLOSED">{intl.get(`${intlPrefix}.status.close`).d('关闭')}</Menu.Item>
      <Menu.Item key="CANCEL">{intl.get(`${intlPrefix}.status.cancel`).d('取消')}</Menu.Item>
    </Menu>
  );

  const handleHeadCreate = async () => {
    openHeadModal(
      headInfoDs.create(),
      intl.get(`${intlPrefix}.model.title.create`).d('销售订单头新建'),
    );
  };

  const headInfoTableButtons = [
    <Button color={ButtonColor.primary} icon="add" onClick={handleHeadCreate}>
      {intl.get(`${intlPrefix}.model.button.create`).d('新建')}
    </Button>,
  ];
  const lineInfoTableButtons = () => {
    return headInfoSelected ? (
      <div
        style={{
          textAlign: 'right',
          marginTop: '10px',
        }}
      >
        {lineInfoSelectedRecords.length !==1 ? (
          <AttributeDrawer
            className="org.tarzan.mes.domain.entity.MtSoLine"
            type="button"
            tablename="mt_so_line"
            kid={lineInfoSelectedRecords[0]?.get('soLineId')}
            canEdit={lineInfoSelectedRecords[0]?.get('lineStatus')==="USABLE"}
            serverCode="/key-focus-mes"
            onClick={() => {
              notification.warning({
                message: intl
                  .get(`${intlPrefix}.notification.lineheader.expenderror`)
                  .d('仅允许对单条数据的扩展属性进行操作，请检查！'),
              });
            }}
          />
        ) : (
          <AttributeDrawer
            type="button"
            tablename="mt_so_line"
            kid={lineInfoSelectedRecords[0]?.get('soLineId')}
            canEdit={lineInfoSelectedRecords[0]?.get('lineStatus')==="USABLE"}
            serverCode="/key-focus-mes"
            className="org.tarzan.mes.domain.entity.MtSoLine"
          />
        )}
        <Button color={ButtonColor.primary} icon="add" onClick={()=>handleLineCreate()}>
          {intl.get(`${intlPrefix}.model.button.create`).d('新建')}
        </Button>
        <Dropdown
          key="LineStateChange"
          trigger={[Action.click, Action.focus, Action.hover]}
          overlay={dropdownItem}
        >
          <Button icon="repeat" funcType={FuncType.raised}>
            {intl.get(`${intlPrefix}.status.change`).d('状态变更')}
          </Button>
        </Dropdown>
      </div>
    ) : (
      <></>
    );
  };

  const handleHeadEdit = (record: Record | null | undefined) => {
    if(record?.get('soStatus')!=="USABLE"){
      notification.warning({    message: intl.get(`${intlPrefix}.notification.head.cantEdit`).d('仅可用状态订单可编辑')})
      return;
    }
    openHeadModal(record, intl.get(`${intlPrefix}.model.title.edit`).d('销售订单头编辑'));
  };

  const handleCustomerCodeChange = () => {
    headInfoDs!.current!.set('customerSiteCode', '');
  };

  const openHeadModal = (record?: Record | null | undefined, title?: string) => {
    Modal.open({
      title,
      destroyOnClose: true,
      children: (
        <Form dataSet={headInfoDs} columns={2}>
          <TextField name="soNumber" disabled />
          <Lov name="siteCode" />
          <Lov name="customerCode" onChange={handleCustomerCodeChange} />
          <TextField disabled name="customerNameAlt" />
          <TextField disabled name="customerName" />
          <Lov name="customerSiteCode" />
          <TextField disabled name="customerSiteName" />
          <Select name="soType" />
          <TextField disabled name="soStatus" />
          <Lov name="shipToCode" />
          <TextField name="shipToSiteDesc" />
          <TextField name="salesPerson" />
          <Select name="freezeFlag" />
          <TextField name="remark" />
          <TextField name="currencyCode" />
          <TextField name="soCategory" />
          <TextField name="salesChannel" />
          <TextField name="commercialTerm" />
          <TextField name="paymentTerm" />
        </Form>
      ),
      drawer: true,
      onOk: async () => {
        const validate = await record?.validate();
        if (validate) {
          const res = await headInfoDs.submit();
          if (res?.success) {
            headInfoDs.query();
            // notification.success({
            //   message: intl.get(`${intlPrefix}.notification.lineheader.success1`).d('操作成功1'),
            // });
          } else if (headInfoDs.dirty) {
            notification.warning({
              message: intl.get(`${intlPrefix}.notification.lineheader.error`).d('操作失败'),
            });
          }
          return true;
        }
        return false;
      },
      onCancel: () => {
        headInfoDs.reset();
      },
    });
  };

  const handleLineCreate = () => {
    openLineModal(
      lineInfoDs.create({ soId: curSoId }),
      intl.get(`${intlPrefix}.model.line.create`).d('销售订单行新建'),
    );
  };

  const handleLineEdit = (record: Record | null | undefined) => {
    if(record?.get('lineStatus')!=="USABLE"){
      notification.warning({    message: intl.get(`${intlPrefix}.notification.line.cantEdit`).d('仅可用状态的订单行可编辑')})
      return;
    }
    openLineModal(record, intl.get(`${intlPrefix}.model.line.edit`).d('销售订单行编辑'));
  };

  const handleSiteCodeChange = () => {
    headInfoDs!.current!.set('materialCode', '');
  };

  const openLineModal = (record?: Record | null, title?: string) => {
    Modal.open({
      title,
      destroyOnClose: true,
      children: (
        <Form dataSet={lineInfoDs} columns={2}>
          <Lov name="siteCode" onChange={handleSiteCodeChange} />
          <Lov name="materialCode" />
          <TextField disabled name="materialName" />
          <Select name="revisionCode" />
          <TextField disabled name="uomName" />
          <Select name="lineType" />
          <TextField disabled name="lineStatus" />
          <NumberField name="orderedQuantity" />
          <NumberField name="shippedQuantity" />
          <NumberField name="reservedQuantity" />
          <NumberField name="cancelledQuantity" />
          <DatePicker name="scheduleShipDate" />
          <DatePicker name="scheduleArrivalDate" />
          <DatePicker name="actualShipDate" />
          <DatePicker name="actualArrivalDate" />
          <TextField name="contractNum" />
          <TextField name="customerPoNum" />
          <TextField name="parentLineNum" />
          <TextField name="remark" />
          <Select name="freezeFlag" />
          <Lov name="locatorCode" />
          <TextField disabled name="locatorName" />
          <TextField name="shipMethod" />
          <TextField name="packingInstructions" />
          <TextField name="itemCategory" />
          <TextField name="reason" />
        </Form>
      ),
      drawer: true,
      onOk: async () => {
        const validate = await record?.validate();
        if (validate) {
          const res = await lineInfoDs.submit();
          if (res?.success) {
            await lineInfoDs.query();
            // notification.success({
            //   message: intl.get(`${intlPrefix}.notification.lineheader.success`).d('操作成功2'),
            // });
          } else if (lineInfoDs.dirty) {
            notification.warning({
              message: intl.get(`${intlPrefix}.notification.lineheader.error`).d('操作失败'),
            });
          }
          return true;
        }
        return false;
      },
      onCancel: () => {
        lineInfoDs.reset();
      },
    });
  };

  // 对头表格行对象处理
  const onRow = ({ record }) => {
    return {
      onClick: () => {
        lineInfoDs.setQueryParameter('soId', record?.get('soId'));
        setCurSoId(record?.get('soId'));
        lineInfoDs.query();
        setHeadInfoSelected(true);
      },
      onblur: () => {
        setHeadInfoSelected(false);
      },
    };
  };

  return (
    <PageHeaderWrapper
      contentProps={{
        wrapperClassName: 'hcmp-wrap',
      }}
      title={intl.get(`${intlPrefix}.title.management`).d('销售订单管理')}
      header={headInfoTableButtons}
    >
      <Card bordered={false}>
        <SearchForm dataSet={headInfoDs} />
      </Card>
      <Card bordered={false}>
        <ExpandTable
          keyStr="soNunmber"
          columnProps={headInfoTableBeforeColumns}
          dataSet={headInfoDs}
          type="pro"
          changeColumn={changeHeadInfoTableColumn}
          baseServer={BASE_SERVER}
        />
        <Table
          dataSet={headInfoDs}
          selectionMode={SelectionMode.mousedown}
          highLightRow
          columns={headInfoTableColumns}
          queryBar={TableQueryBarType.none}
          onRow={onRow}
        />
      </Card>
      <Card bordered={false}>
        {lineInfoTableButtons()}
        <ExpandTable
          keyStr="soLineNum"
          columnProps={lineInfoTableBeforeColumns}
          dataSet={lineInfoDs}
          type="pro"
          changeColumn={changeLineInfoTableColumn}
          baseServer={BASE_SERVER}
        />
        <Table
          queryBar={TableQueryBarType.bar}
          queryBarProps={lineInfoDs.queryDataSet}
          dataSet={lineInfoDs}
          columns={lineInfoTableColumns}
          selectionMode={SelectionMode.rowbox}
        />
      </Card>
    </PageHeaderWrapper>
  );
};

const ListPage = withProps(
  () => {
    const headInfoDs = new DataSet(headInfoDS());
    const lineInfoDs = new DataSet(lineInfoDS());
    return {
      headInfoDs,
      lineInfoDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['sale.order.manage.code'],
})(ListPage);
