/**
 * @Description: 检验方案维护-按钮组件
 * @Author: <<EMAIL>>
 * @Date: 2023-01-18 10:18:05
 * @LastEditTime: 2023-03-09 15:42:31
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { DataSet, Button } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.initialManagementActivity';

const AddActiveTabDimensionButton = observer(
  ({
    inspectBusinessType,
    batchAddInspectionDimension,
    activeKey,
    canEdit,
    inspectSchemeLines,
  }: {
    ds: DataSet;
    addInspectionDimension;
    copyBusinessType;
    batchAddInspectionDimension;
    inspectBusinessType,
    activeKey;
    canEdit;
    inspectSchemeLines;
  }) => {
    // 判断当前tab是否选中 检验类型
    const hasInspectBusinessType = () => {
      let flag = true;
      inspectSchemeLines.forEach(item => {
        if (activeKey === item.key) {
          item.inspectionItemBasisDs.forEach(record => {
            if (record.get('inspectBusinessType')) {
              flag = false;
            }
            if (record.get('inspectBusinessType') === 'PRODUCT_CQGL' || record.get('inspectBusinessType') === 'PARTS_CQGL') {
              flag = true;
            }
          });
        }
      });

      return flag;
    };

    return (
      <>
        <Button
          icon="add"
          onClick={() => {
            batchAddInspectionDimension(inspectBusinessType);
          }}
          disabled={
            inspectSchemeLines.length === 0 ||
            activeKey === '' ||
            !canEdit ||
            hasInspectBusinessType()
          }
        >
          {intl.get(`${modelPrompt}.batchAddInspectionDimension`).d('批量新增检验项目')}
        </Button>
      </>
    );
  },
);

export default AddActiveTabDimensionButton;
