/**
 * @Description: 不良代码组维护详情
 * @Author: <<EMAIL>>
 * @Date: 2022-07-12 10:59:30
 * @LastEditTime: 2023-05-18 15:12:26
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  DataSet,
  Table,
  Spin,
  Button,
  Form,
  TextField,
  Lov,
  IntlField,
  Switch, NumberField,
} from 'choerodon-ui/pro';
import { Popconfirm, Collapse, Tabs } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { RecordStatus } from "choerodon-ui/dataset/data-set/enum";
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { AttributeDrawer } from '@components/tarzan-ui';
import notification from 'utils/notification';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { SubmitData } from '../services';
import { formDS, ncCodeDS, technologyDS } from '../stores/DefectGroupDetailDS';

const { Panel } = Collapse;
const { TabPane } = Tabs;

const modelPrompt = 'tarzan.badCode.defectGroup.model.defectGroup';

const DefectGroupDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    custConfig,
    customizeForm,
  } = props;

  const submitData = useRequest(SubmitData(), { manual: true });

  const ncCodeDs = useMemo(() => new DataSet(ncCodeDS()), []);
  const technologyDs = useMemo(() => new DataSet(technologyDS()), []);
  const formDs = useMemo(() => new DataSet({
    ...formDS(),
    children: {
      ncCodeList: ncCodeDs,
      mtNcValidOperationList: technologyDs,
    },
  }), []);

  const [canEdit, setCanEdit] = useState(id === 'create');
  const [loading, setLoading] = useState(false);
  const [deleteNcCodeIds, setNcGroupIds] = useState([]);
  const [deleteTechnologyIds, setTechnologyIds] = useState([]);

  useEffect(() => {
    if (id === 'create') {
      return;
    }
    handleQuery();
  }, [id]);

  const handleQuery = () => {
    setLoading(true);
    setCanEdit(false);
    formDs.setQueryParameter('ncGroupId', id);
    formDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_DETAIL.BASIC`);
    formDs
      .query()
      .then(() => {
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const changEditStatus = () => setCanEdit(true);

  const handleSaveBomList = async () => {
    const formDsResult = await formDs.validate();
    const ncCodeDsResult = await ncCodeDs.validate();
    const technologyDsResult = await technologyDs.validate();
    if (formDsResult && ncCodeDsResult && technologyDsResult) {
      const formDsData = formDs.current.toData();
      const ncCodeData = [];
      ncCodeDs.forEach(record => {
        if ([RecordStatus.add, RecordStatus.update].includes(record.status)) {
          ncCodeData.push(record.toData());
        }
      });
      const technologyData = [];
      technologyDs.forEach(record => {
        if ([RecordStatus.add, RecordStatus.update].includes(record.status)) {
          technologyData.push(record.toData());
        }
      });

      submitData.run({
        params: {
          deleteNcGroupNcRelIds: deleteNcCodeIds,
          deleteNcValidOperationIds: deleteTechnologyIds,
          ncGroup: {
            ...formDsData,
            ncCodeList: undefined,
            mtNcValidOperationList: undefined,
          },
          ncCodeList: ncCodeData,
          mtNcValidOperationList: technologyData,
        },
        onSuccess: res => {
          notification.success();
          if (id === 'create') {
            props.history.push({
              pathname: `/hmes/bad/defect-group/dist/${res}`,
            });
          } else {
            handleQuery();
          }
        },
      });
    }
  };

  const onCancel = () => {
    if (id === 'create') {
      props.history.push({
        pathname: `/hmes/bad/defect-group/list`,
      });
    } else {
      handleQuery();
      setNcGroupIds([]);
      setTechnologyIds([]);
    }
  };

  const handleAddRowTechnology = () => {
    technologyDs.create({}, 0);
  };

  const handleSiteChange = () => {
    if (ncCodeDs.toData().length > 0 || technologyDs.toData().length > 0) {
      notification.info({
        message: intl
          .get(`tarzan.badCode.defectGroup.message.deleteMessage`)
          .d('因更换站点，已删除不良代码与工艺维护里面的所有新增状态数据'),
      });
      ncCodeDs.loadData([]);
      technologyDs.loadData([]);
    }
  };

  const handleAddNcCode = () => {
    let _maxSeq = 0;
    ncCodeDs.forEach((_record) => {
      if (_record.get('sequence') > _maxSeq) {
        _maxSeq = _record.get('sequence');
      }
    });
    ncCodeDs.create({
      sequence: Math.floor(_maxSeq / 10) * 10 + 10,
    }, 0)
  };

  const deleteNcCode = record => {
    if (record.get('ncGroupNcRelId')) {
      const _deleteIds = deleteNcCodeIds || [];
      _deleteIds.push(record.get('ncGroupNcRelId'));
      setNcGroupIds(_deleteIds);
    }
    ncCodeDs.remove(record)
  };

  const ncCodeColumns = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit || !formDs.current.get('siteId')}
          funcType="flat"
          onClick={handleAddNcCode}
          shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          disabled={!canEdit || !formDs.current.get('siteId')}
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          okText={intl.get('tarzan.common.button.confirm').d('确定')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          onConfirm={() => deleteNcCode(record)}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit || !formDs.current.get('siteId')}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'sequence',
      width: 130,
      editor: record => canEdit && record.status === 'add' && <NumberField />,
    },
    {
      name: 'ncCodeLov',
      editor: record => canEdit && record.status === 'add' && <Lov />,
    },
    { name: 'description' },
    {
      name: 'scrapDetail',
    },
    { name: 'ncTypeDesc' },
  ];

  const deleteRecordTechnology = record => {
    if (record.get('ncValidOperationId')) {
      const _deleteIds = deleteTechnologyIds || [];
      _deleteIds.push(record.get('ncValidOperationId'));
      setTechnologyIds(_deleteIds);
    }
    technologyDs.remove(record);
  };

  const technologyColumns = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit || !formDs.current.get('siteId')}
          funcType="flat"
          onClick={handleAddRowTechnology}
          shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          disabled={!canEdit || !formDs.current.get('siteId')}
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          okText={intl.get('tarzan.common.button.confirm').d('确定')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          onConfirm={() => deleteRecordTechnology(record)}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit || !formDs.current.get('siteId')}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'operation',
      editor: record => canEdit && record.status === 'add' && <Lov />,
    },
    {
      name: 'dispositionGroupObject',
      editor: () => canEdit && <Lov />,
    },
  ];

  return (
    <div className="hmes-style">
      <Spin spinning={submitData.loading || loading}>
        <Header
          title={intl.get('tarzan.badCode.defectGroup.title.defectGroup').d('不良代码组维护')}
          backPath="/hmes/bad/defect-group/list"
        >
          {!canEdit ? (
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              onClick={changEditStatus}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          ) : (
            <>
              <Button
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="save"
                loading={submitData.loading}
                onClick={handleSaveBomList}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button type="button" icon="close" onClick={onCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          <AttributeDrawer
            serverCode={BASIC.TARZAN_METHOD}
            canEdit={canEdit}
            disabled={id === 'create'}
            kid={id}
            className="org.tarzan.method.domain.entity.MtNcCode"
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_DETAIL.BUTTON`}
            custConfig={custConfig}
          />
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={['basic', 'technology']}
          >
            <Panel
              header={intl.get(`${modelPrompt}.basic`).d('基础属性')}
              key="basic"
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_DETAIL.BASIC`,
                },
                <Form disabled={!canEdit} dataSet={formDs} columns={3}>
                  <TextField name="ncGroupCode" />
                  <IntlField
                    name="description"
                    modalProps={{
                      title: intl.get(`${modelPrompt}.ncGroupDesc`).d('不良代码组描述'),
                    }}
                  />
                  <Lov name="site" onChange={handleSiteChange} />
                  <Switch name="enableFlag" />
                  <Switch name="validAtAllOperations" />
                </Form>,
              )}
            </Panel>
          </Collapse>
          <Tabs defaultActiveKey="basic">
            <TabPane tab={intl.get(`${modelPrompt}.badCode`).d('不良代码')} key="badCode">
              <Table dataSet={ncCodeDs} columns={ncCodeColumns} />
            </TabPane>
            <TabPane tab={intl.get(`${modelPrompt}.technology`).d('工艺分配')} key="technology">
              <Table dataSet={technologyDs} columns={technologyColumns} />
            </TabPane>
          </Tabs>
        </Content>
      </Spin>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.badCode.defectGroup', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_DETAIL.BUTTON`, `${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_DETAIL.BASIC`],
  })(DefectGroupDetail),
);
