/**
 * @Description: 问题警示平台-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-06-30 10:38:58
 * @LastEditTime: 2023-06-30 10:38:58
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.warning';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

// 列表-ds
const listTableDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: false,
  cacheSelection: true,
  primaryKey: 'problemWarnListId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: ({ data }) => {
      const {
        registDateFrom,
        registDateTo,
        planStartDateFrom,
        planStartDateTo,
        planEndDateFrom,
        planEndDateTo,
        ...others
      } = data;
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/list/query/for/ui`,
        method: 'get',
        data: {
          ...others,
          registDateFrom: registDateFrom ? moment(registDateFrom).format('YYYY-MM-DD') : undefined,
          registDateTo: registDateTo ? moment(registDateTo).format('YYYY-MM-DD') : undefined,
          planStartDateFrom: planStartDateFrom ? moment(planStartDateFrom).format('YYYY-MM-DD') : undefined,
          planStartDateTo: planStartDateTo ? moment(planStartDateTo).format('YYYY-MM-DD') : undefined,
          planEndDateFrom: planEndDateFrom ? moment(planEndDateFrom).format('YYYY-MM-DD') : undefined,
          planEndDateTo: planEndDateTo ? moment(planEndDateTo).format('YYYY-MM-DD') : undefined,
        },
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnListCode`).d('警示编号'),
      name: 'warnListCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnStatus`).d('警示状态'),
      name: 'status',
      lookupCode: 'YP.QIS.PROBLEM_WARN_LIST_STATUS',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('警示来源'),
      name: 'createMethod',
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceProblem`).d('问题编号'),
      name: 'sourceProblemLov',
      lovCode: 'SOURCE_PROBLEM_NUM',
    },
    {
      name: 'sourceProblemNum',
      bind: 'sourceProblemLov.sourceProblemNum',
    },
    {
      name: 'sourceProblemId',
      bind: 'sourceProblemLov.sourceProblemId',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('问题严重程度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registUser`).d('登记人'),
      name: 'registUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'registUserId',
      bind: 'registUserLov.id',
    },
    {
      name: 'registUserName',
      bind: 'registUserLov.realName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registUserDapartment`).d('登记部门'),
      name: 'registUserDapartmentLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'registUserDapartmentId',
      bind: 'registUserDapartmentLov.unitId',
    },
    {
      name: 'registUserDapartmentName',
      bind: 'registUserDapartmentLov.unitName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerSiteName`).d('触发警示站点名称'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerProdLineName`).d('触发警示产线名称'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warningLeadUser`).d('警示主导人'),
      name: 'leadUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'leadUserId',
      bind: 'leadUserLov.id',
    },
    {
      name: 'leadUserName',
      bind: 'leadUserLov.realName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.leadUserDapartment`).d('警示主导部门'),
      name: 'leadUserDapartmentLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'leadUserDapartmentId',
      bind: 'leadUserDapartmentLov.unitId',
    },
    {
      name: 'leadUserDapartmentName',
      bind: 'leadUserDapartmentLov.unitName',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.registDateFrom`).d('登记时间从'),
      name: 'registDateFrom',
      max: 'registDateTo',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.registDateTo`).d('登记时间至'),
      name: 'registDateTo',
      min: 'registDateFrom',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productLevel`).d('产品层级'),
      name: 'productLevel',
      lookupCode: 'YP.QIS.PRODUCT_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnListType`).d('警示类型'),
      name: 'warnListType',
      lookupCode: 'YP.QIS.WARN_LIST_TYPE',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planStartDateFrom`).d('计划开始时间从'),
      name: 'planStartDateFrom',
      max: 'planStartDateTo',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planStartDateTo`).d('计划开始时间至'),
      name: 'planStartDateTo',
      min: 'planStartDateFrom',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planEndDateFrom`).d('计划结束时间从'),
      name: 'planEndDateFrom',
      max: 'planEndDateTo',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planEndDateTo`).d('计划结束时间至'),
      name: 'planEndDateTo',
      min: 'planEndDateFrom',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnListCode`).d('警示编号'),
      name: 'warnListCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnStatus`).d('警示状态'),
      name: 'status',
      lookupCode: 'YP.QIS.PROBLEM_WARN_LIST_STATUS',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnStatus`).d('警示状态'),
      name: 'statusDesc',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('警示来源'),
      name: 'createMethod',
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethodDesc`).d('警示来源'),
      name: 'createMethodDesc',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceProblem`).d('问题编号'),
      name: 'sourceProblemNum',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('问题严重程度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registUser`).d('登记人'),
      name: 'registUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'registUserId',
      bind: 'registUserLov.id',
    },
    {
      name: 'registUserName',
      bind: 'registUserLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.registUserDapartment`).d('登记部门'),
      name: 'registUserDapartmentName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerSiteName`).d('触发警示站点名称'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.triggerSiteName`).d('触发警示站点名称'),
      bind: 'siteLov.siteName',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerProdLineName`).d('触发警示产线名称'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineName',
      label: intl.get(`${modelPrompt}.triggerProdLineName`).d('触发警示产线名称'),
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warningLeadUser`).d('警示主导人'),
      name: 'leadUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'leadUserId',
      bind: 'leadUserLov.id',
    },
    {
      name: 'leadUserName',
      label: intl.get(`${modelPrompt}.warningLeadUser`).d('警示主导人'),
      bind: 'leadUserLov.realName',
    },
    {
      name: 'leadUserDapartmentName',
      label: intl.get(`${modelPrompt}.leadUserDapartment`).d('警示主导部门'),
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.registDate`).d('登记时间'),
      name: 'registDate',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planStartDate`).d('计划开始时间'),
      name: 'planStartDate',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planEndDate`).d('计划完成时间'),
      name: 'planEndDate',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productLevel`).d('产品层级'),
      name: 'productLevel',
      lookupCode: 'YP.QIS.PRODUCT_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnListType`).d('警示类型'),
      name: 'warnListType',
      lookupCode: 'YP.QIS.WARN_LIST_TYPE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnListTypeDesc`).d('警示类型'),
      name: 'warnListTypeDesc',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      readOnly: true,
    },
  ],
});

// 详情-问题警示平台信息ds
const detailFormDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnListCode`).d('警示编号'),
      name: 'warnListCode',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnStatus`).d('警示状态'),
      name: 'status',
      lookupCode: 'YP.QIS.PROBLEM_WARN_LIST_STATUS',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('警示来源'),
      name: 'createMethod',
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceProblem`).d('问题编号'),
      name: 'sourceProblemLov',
      lovCode: 'SOURCE_PROBLEM_NUM',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => !record?.get('createMethod'),
        lovPara: ({ record }) => ({
          tenantId,
          problemCategory: record?.get('createMethod'),
        }),
      },
    },
    {
      name: 'sourceProblemNum',
      bind: 'sourceProblemLov.problemCode',
    },
    {
      name: 'sourceProblemId',
      bind: 'sourceProblemLov.problemId',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('问题严重程度'),
      disabled: true,
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
      bind: 'sourceProblemLov.severityLevel',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warningRegistUser`).d('警示登记人'),
      name: 'registUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
      disabled: true,
    },
    {
      name: 'registUserId',
      bind: 'registUserLov.id',
    },
    {
      name: 'registUserName',
      bind: 'registUserLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warningregistUserDapartment`).d('警示登记部门'),
      name: 'registUserDapartmentName',
      disabled: true,
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.warningRegistDate`).d('警示登记时间'),
      name: 'registDate',
      required: true,
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerSiteName`).d('触发警示站点名称'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerProdLineName`).d('触发警示产线名称'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.leadUserDapartment`).d('警示主导部门'),
      name: 'leadUserDapartmentName',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warningLeadUser`).d('警示主导人'),
      name: 'leadUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'leadUserId',
      bind: 'leadUserLov.id',
    },
    {
      name: 'leadUserName',
      bind: 'leadUserLov.realName',
    },

    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productLevel`).d('产品层级'),
      name: 'productLevel',
      lookupCode: 'YP.QIS.PRODUCT_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnListType`).d('警示类型'),
      name: 'warnListType',
      lookupCode: 'YP.QIS.WARN_LIST_TYPE',
      computedProps: {
        required: ({ dataSet }) => {
          return (dataSet.getState('authList') || []).includes('DETAIL');
        },
      },
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      name: 'enclosure',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDesc`).d('问题描述'),
      name: 'problemDesc',
      computedProps: {
        required: ({ dataSet }) => {
          return (dataSet.getState('authList') || []).includes('DETAIL');
        },
      },
      maxLength: 999,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonAnalyse`).d('原因分析'),
      name: 'reason',
      computedProps: {
        required: ({ dataSet }) => {
          return (dataSet.getState('authList') || []).includes('DETAIL');
        },
      },
      maxLength: 999,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containmentAction`).d('警示措施'),
      name: 'containmentAction',
      computedProps: {
        required: ({ dataSet }) => {
          return (dataSet.getState('authList') || []).includes('DETAIL');
        },
      },
      maxLength: 999,
    },
  ],
});

// 详情-问题警示平台维度的DS
const detailTableDS = (formDs): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: DataSetSelection.multiple,
  fields: [
    {
      name: 'problemWarnListId',
    },
    {
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processName`).d('工序名称'),
      name: 'processLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        // workcellType: 'PROCESS',
      },
      multiple: true,
      required: true,
    },
    {
      name: 'processId',
      bind: 'processLov.workcellId',
    },
    {
      name: 'processDesc',
      bind: 'processLov.workcellName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      name: 'equipmentLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentName',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'equipmentDesc',
      bind: 'equipmentLov.equipmentName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibilityDepartment`).d('责任部门'),
      name: 'responsibilityDepartmentName',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibilityUser`).d('责任人'),
      name: 'responsibilityUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'responsibilityUserId',
      bind: 'responsibilityUserLov.id',
    },
    {
      name: 'responsibilityUserName',
      bind: 'responsibilityUserLov.realName',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planStartDate`).d('计划开始时间'),
      name: 'planStartDate',
      max: 'planEndDate',
      required: true,
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planEndDate`).d('计划完成时间'),
      name: 'planEndDate',
      min: 'planStartDate',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      name: 'status',
      lookupCode: 'YP.QIS.PROPLEM_WARN_TASK_STATUS',
      disabled: true,
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.evidence`).d('证据'),
      name: 'evidence',
      computedProps: {
        disabled: ({ dataSet, record }) => {
          return (
            (!(dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE') ||
            record.get('responsibilityUserId') !== dataSet.getState('userId'))|| !["TO_EXECUTE", "AUDIT_REJECT"].includes(record?.get('status'))
          );
        },
        readOnly: ({ dataSet, record }) => {
          return (
            (!(dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE') ||
            record.get('responsibilityUserId') !== dataSet.getState('userId'))|| !["TO_EXECUTE", "AUDIT_REJECT"].includes(record?.get('status'))
          );
        },
        required: ({ dataSet, record }) => {
          return (
            (dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE_REQUIRED') &&
            record.get('responsibilityUserId') !== dataSet.getState('userId')
          );
        },
      },
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      name: 'enclosure',
      computedProps: {
        disabled: ({ dataSet, record }) => {
          return (
            (!(dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE') ||
            record.get('responsibilityUserId') !== dataSet.getState('userId'))|| !["TO_EXECUTE", "AUDIT_REJECT"].includes(record?.get('status'))
          );
        },
        readOnly: ({ dataSet, record }) => {
          return (
            (!(dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE') ||
            record.get('responsibilityUserId') !== dataSet.getState('userId')) || !["TO_EXECUTE", "AUDIT_REJECT"].includes(record?.get('status'))
          );
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消理由'),
      name: 'reason',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remindFlag`).d('启用提醒'),
      name: 'remindFlag',
      lookupCode: 'MT.FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => !["NEW", "TO_EXECUTE", "AUDIT_REJECT"].includes(record?.get('status')),
      },
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => {
        return record.get('selectAuth');
      },
    },
  },
});

// 详情-问题警示平台信息ds
const modalDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消理由'),
      name: 'cancelReason',
      computedProps: {
        required: ({ dataSet }) => {
          return dataSet.getState('modalType') === 'cancelReason';
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
      name: 'rejectReason',
      computedProps: {
        required: ({ dataSet }) => {
          return dataSet.getState('modalType') === 'rejectReason';
        },
      },
    },
  ],
});

export { listTableDS, detailFormDS, detailTableDS, modalDS };
