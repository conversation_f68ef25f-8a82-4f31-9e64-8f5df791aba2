import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Form, Lov, Table, TextField, NumberField } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { Popconfirm } from 'choerodon-ui';
import uuid from 'uuid/v4';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import myInstance from '@utils/myAxios';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { downloadFile } from '@services/api';
import moment from 'moment/moment';
import { useMemoizedFn } from 'ahooks';
import { API_HOST } from '@/utils/constants';
import { formDS } from '../stores/deflectiveDS';
import { analyseResultDS } from '../stores/DetailDS';
import DeflectiveGraphic from '../components/DeflectiveGraphic';
import ExcelUpload, { ExcelUploadProps } from './components/ExcelUpload';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

interface GraphicDataProps {
  xBarChartRule: object; // xBar图rules
  rChartRule: object; // r图rules
  data: []; // xBar-r图数据
  biasHistogramChartInfo: object; // 直方图数据
  biasTableInfo: object; // 表格数据
  msaResult: string; // 分析结果
  msaConclusion: string; // 分析结论
  tableInfo: [];
}

// 默认数据
const apiDataDefault = [
  {
    measureDate: '',
    measureTableList: [
      {
        measureDataColumn: 1,
        measureDataRow: 1,
        measureDataValue: '',
      },
      // {
      //   measureDataColumn: 1,
      //   measureDataRow: 2,
      //   measureDataValue: '',
      // },
    ],
    range: '',
    average: '',
  },
];

const Deflective = props => {
  const { msaStatus, currentUserFlag } = props;
  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
      }),
    [],
  );
  // 分析结果DS
  const analyseResultDs = useMemo(() => new DataSet(analyseResultDS()), []);
  const [graphicData, setGraphicData] = useState<GraphicDataProps>({
    xBarChartRule: {}, // xBar图rules
    rChartRule: {}, // r图rules
    data: [], // xBar-r图数据
    biasHistogramChartInfo: {}, // 直方图数据
    biasTableInfo: {}, // 表格数据
    msaResult: '', // 分析结果
    msaConclusion: '', // 分析结论
    tableInfo: [],
  });

  const templateData = [
    {
      measureDate: intl.get(`${modelPrompt}.measuringTime`).d('测量时间'),
      uuid: uuid(),
      type: 'measureDate',
    },
    {
      measureDataRow: 1,
      measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
      uuid: uuid(),
      type: 'measureDataValue',
    },
    {
      measureDate: intl.get(`${modelPrompt}.meanValue`).d('平均值'),
      uuid: uuid(),
      type: 'average',
    },
    {
      measureDate: intl.get(`${modelPrompt}.range`).d('极差'),
      uuid: uuid(),
      type: 'range',
    },
  ];

  const [apiData, setApiData] = useState([]);
  const [defaultData, setDefaultData] = useState(templateData); // 有默认数据时初始化
  const [havaSaveData, setHaveSaveData] = useState(false);

  const [defaultField, setDefaultField] = useState([
    {
      name: 'measureDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subGroup`).d('子组号'),
    },
    {
      name: 'measureDataRow',
      type: FieldType.string,
    },
    {
      name: 'measureDataColumn1',
      computedProps: {
        type: ({ record }) => {
          if (record.get('type') === 'measureDate') {
            return FieldType.dateTime;
          }
          return FieldType.number;
        },
        disabled: ({ record }) => {
          if (record.get('type') === 'average' || record.get('type') === 'range') {
            return true;
          }
        },
        required: ({ record }) => {
          return record.get('type') === 'measureDataValue';
        },
      },
    },
  ]);

  const userDs = useMemo(
    () =>
      new DataSet({
        forceValidate: true,
        autoCreate: false,
        selection: false,
        paging: false,
        primaryKey: 'uuid',
        data: defaultData,
        fields: defaultField,
      }),
    [],
  );

  const [currentColumns, setCurrentColumns] = useState([
    {
      name: 'measureDataRow',
      width: 80,
      lock: ColumnLock.left,
      align: ColumnAlign.center,
      renderer: ({ value, dataSet, record }) => {
        if (value) {
          return (
            <>
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => removeData(value)}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <PermissionButton
                  type="c7n-pro"
                  disabled={Number(value) === 1} // 测量值最少需要一行数据
                  icon="remove"
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </Popconfirm>

              <span>{value}</span>
            </>
          );
        }
      },
    },
    {
      name: 'measureDataColumn1',
      editor: record => {
        return !(record.get('type') === 'average' || record.get('type') === 'range');
      },
      align: ColumnAlign.center,
      header: () => (
        <>
          <span>1</span>
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteColumn('measureDataColumn1')}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              funcType="flat"
              shape="circle"
              size="small"
            />
          </Popconfirm>
        </>
      ),
    },
  ]); // 实际columns

  useEffect(() => {
    myInstance
      .get(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/info/ui?msaTaskLineId=${props.msaTaskLineId}`,
      )
      .then(res => {
        if (res.data.success) {
          // 处理数据
          const {
            standardValue,
            expectedDeterioration,
            sampleDescription,
            measuredBy,
            measuredByName,
          } = res.data.rows;
          if (standardValue) formDs.current?.set('standardValue', standardValue);
          if (expectedDeterioration)
            formDs.current?.set('expectedDeterioration', expectedDeterioration);
          if (sampleDescription) formDs.current?.set('sampleDescription', sampleDescription);
          if (measuredBy) {
            formDs.current?.set('improveByObj', {
              userId: measuredBy,
              realName: measuredByName,
            });
          }
          if ((res.data.rows.tableInfo || []).length) {
            setApiData(res.data.rows.tableInfo);
            setHaveSaveData(true);
          } else {
            setApiData(apiDataDefault);
            setHaveSaveData(false);
          }
        } else {
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          });
        }
        handleUpdateChartData(res.data.rows || {});
      });
  }, []);

  useEffect(() => {
    const colums = apiData.length; // 总共有多少列数据
    if (colums > 0) {
      const rows = apiData[0].measureTableList.length; // 测量数据共有多少行
      // 先生成，时间，极差，平均值的数据
      let transformDataResult = [];
      const timeObj = {
        measureDate: intl.get(`${modelPrompt}.measuringTime`).d('测量时间'),
        uuid: uuid(),
        type: 'measureDate',
      };
      const rangeObj = {
        measureDate: intl.get(`${modelPrompt}.range`).d('极差'),
        uuid: uuid(),
        type: 'range',
      };
      const averageObj = {
        measureDate: intl.get(`${modelPrompt}.meanValue`).d('平均值'),
        uuid: uuid(),
        type: 'average',
      };
      let tiledData: any = [];

      apiData.forEach((item: any) => {
        timeObj[`measureDataColumn${item.measureTableList[0].measureDataColumn}`] =
          item.measureDate;
        rangeObj[`measureDataColumn${item.measureTableList[0].measureDataColumn}`] = item.range;
        averageObj[`measureDataColumn${item.measureTableList[0].measureDataColumn}`] = item.average;
        tiledData = [...tiledData, ...item.measureTableList];
      });
      // @ts-ignore
      transformDataResult = [timeObj];

      // 生成测量值数据
      for (let rowSeq = 0; rowSeq < rows; rowSeq++) {
        const obj = {
          measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
          uuid: uuid(),
          type: 'measureDataValue',
          measureDataRow: String(rowSeq + 1),
        };
        for (let colSeq = 0; colSeq < colums; colSeq++) {
          tiledData.forEach((item: any) => {
            if (item.measureDataRow === rowSeq + 1 && item.measureDataColumn === colSeq + 1) {
              obj[`measureDataColumn${colSeq + 1}`] = item.measureDataValue;
            }
          });
        }
        transformDataResult.push(obj);
      }

      transformDataResult = [...transformDataResult, rangeObj, averageObj];

      // 生产行列配置
      const feild = [
        {
          name: 'measureDate',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.subGroup`).d('子组号'),
        },
        {
          name: 'measureDataRow',
          type: FieldType.string,
        },
      ];

      const column = [
        {
          name: 'measureDataRow',
          width: 80,
          lock: ColumnLock.left,
          align: ColumnAlign.center,
          renderer: ({ value }) => {
            if (value) {
              return (
                <>
                  <Popconfirm
                    title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                    onConfirm={() => removeData(value)}
                    okText={intl.get('tarzan.common.button.confirm').d('确认')}
                    cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                  >
                    <PermissionButton
                      type="c7n-pro"
                      disabled={
                        Number(value) === 1 || Number(value) <= transformDataResult.length - 3
                      } // 测量值最少需要一行数据(-3是因为减去三行的非测量值
                      icon="remove"
                      funcType="flat"
                      shape="circle"
                      size="small"
                    />
                  </Popconfirm>
                  <span>{value}</span>
                </>
              );
            }
          },
        },
      ];
      for (let colSeq = 0; colSeq < colums; colSeq++) {
        feild.push({
          name: `measureDataColumn${colSeq + 1}`,
          // @ts-ignore
          computedProps: {
            type: ({ record }) => {
              if (record.get('type') === 'measureDate') {
                return FieldType.dateTime;
              }
              return FieldType.number;
            },
            disabled: ({ record }) => {
              if (record.get('type') === 'average' || record.get('type') === 'range') {
                return true;
              }
            },
            required: ({ record }) => {
              return record.get('type') === 'measureDataValue';
            },
          },
        });
        column.push({
          name: `measureDataColumn${colSeq + 1}`,
          // @ts-ignore
          editor: record => {
            if (['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag) {
              return false;
            }
            return !(record.get('type') === 'average' || record.get('type') === 'range');
          },
          align: ColumnAlign.center,
          // @ts-ignore
          header: () => (
            <>
              <span>{colSeq + 1}</span>
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => deleteColumn(`measureDataColumn${colSeq + 1}`)}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <PermissionButton
                  type="c7n-pro"
                  icon="remove"
                  disabled={havaSaveData || Number(colSeq) === 0}
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </Popconfirm>
            </>
          ),
        });
      }
      setDefaultField(feild);
      feild.forEach((item: any) => {
        userDs.addField(item.name, {
          ...item,
        });
      });
      setCurrentColumns(column);
      setDefaultData(transformDataResult);
      userDs.loadData(transformDataResult);
    }
  }, [apiData, havaSaveData]);

  const handleUpdateChartData = dataSource => {
    const {
      tableInfo = [],
      xBarChartRule = {},
      rChartRule = {},
      data = [],
      biasHistogramChartInfo = {},
      biasTableInfo = {},
      msaResult = '',
      msaConclusion = '',
    } = dataSource;
    setGraphicData({
      tableInfo,
      xBarChartRule, // xBar图rules
      rChartRule, // r图rules
      data, // xBar-r图数据
      biasHistogramChartInfo, // 直方图数据
      biasTableInfo, // 表格数据
      msaResult, // 分析结果
      msaConclusion, // 分析结论
    });
    analyseResultDs.loadData([{
      msaResult,
      msaConclusion,
    }])
  };

  const deleteColumn = name => {
    setCurrentColumns(prevColumns => prevColumns.filter(item => item.name !== name));
  };

  const removeData = useMemoizedFn(measureDataRow => {
    const data = userDs.toData();
    const newData = [];
    data.forEach((item: any) => {
      if (item.measureDataRow !== measureDataRow) {
        return newData.push(item);
      }
    });
    userDs.loadData(newData);
  });

  const addColumn = async () => {
    const maxNumber = Number(
      currentColumns[currentColumns.length - 1].name.replace('measureDataColumn', ''),
    );
    const columnName = `measureDataColumn${maxNumber + 1}`;

    userDs.addField(columnName, {
      computedProps: {
        type: ({ record }) => {
          if (record.get('type') === 'measureDate') {
            return FieldType.dateTime;
          }
          return FieldType.number;
        },
        disabled: ({ record }) => {
          if (record.get('type') === 'average' || record.get('type') === 'range') {
            return true;
          }
        },
        required: ({ record }) => {
          return record.get('type') === 'measureDataValue';
        },
      },
    });
    const newFields = [
      ...defaultField,
      {
        name: columnName,
        computedProps: {
          type: ({ record }) => {
            if (record.get('type') === 'measureDate') {
              return FieldType.dateTime;
            }
            return FieldType.number;
          },
          disabled: ({ record }) => {
            if (record.get('type') === 'average' || record.get('type') === 'range') {
              return true;
            }
          },
          required: ({ record }) => {
            return record.get('type') === 'measureDataValue';
          },
        },
      },
    ];
    setDefaultField(newFields);
    const newColumns: Array<object> = [
      ...currentColumns,
      {
        name: columnName,
        editor: record => {
          return !(record.get('type') === 'average' || record.get('type') === 'range');
        },
        align: ColumnAlign.center,
        header: () => (
          <>
            <span>{maxNumber + 1}</span>
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => deleteColumn(columnName)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <PermissionButton
                type="c7n-pro"
                icon="remove"
                funcType="flat"
                shape="circle"
                size="small"
              />
            </Popconfirm>
          </>
        ),
      },
    ];
    setCurrentColumns(newColumns);
  };

  const addMeasuredValue = () => {
    const data = userDs.toData();
    let max = 0;
    data.forEach((item: any) => {
      if (item.measureDataRow > max) {
        max = item.measureDataRow;
      }
    });

    userDs.loadData([
      ...userDs.toData(),
      {
        // @ts-ignore
        measureDataRow: Number(max) + 1,
        measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
        uuid: uuid(),
        type: 'measureDataValue',
      },
    ]);
  };

  const groups = [
    {
      name: 'measureDate',
      align: ColumnAlign.center,
      parentField: undefined,
      type: 'column',
      lock: ColumnLock.left,
      columnProps: {
        width: 100,
        header: () => (
          <>
            <span>{intl.get(`${modelPrompt}.subGroup`).d('子组号')}</span>
            <PermissionButton
              type="c7n-pro"
              icon="add"
              disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
              onClick={addColumn}
              funcType="flat"
              shape="circle"
              size="small"
            />
          </>
        ),
        renderer: ({ text }) => (
          <>
            <span>{text}</span>
            {text === intl.get(`${modelPrompt}.measureDataValue`).d('测量值') && (
              <PermissionButton
                type="c7n-pro"
                icon="add"
                disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
                onClick={addMeasuredValue}
                funcType="flat"
                shape="circle"
                size="small"
              />
            )}
          </>
        ),
      },
    },
  ];

  const handleSave = async () => {
    // @ts-ignore
    const validateResult = await userDs.validate();
    const formValidate = await formDs.validate();
    if (!validateResult || !formValidate) {
      return;
    }
    const originData = userDs.toData();
    let measureDataColumnArr = [];
    const measureDataRowArr = [];
    originData.forEach((item: any) => {
      if (item.type === 'measureDataValue' && measureDataColumnArr.length < 1) {
        // 打平key
        measureDataColumnArr = Object.keys(item).filter(
          word => word.indexOf('measureDataColumn') !== -1,
        );
      }
      if (item.type === 'measureDataValue') {
        measureDataRowArr.push(item.measureDataRow);
      }
    });
    console.log(measureDataColumnArr, measureDataRowArr);

    const resultData: Array<any> = [];

    measureDataColumnArr.map(colums => {
      let measureDate = '';
      originData.forEach(rowData => {
        if (rowData.type === 'measureDate') {
          measureDate = rowData[colums] || moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
        }
      });
      const measureTableList = [] as any;
      measureDataRowArr.forEach(rowSquence => {
        originData.forEach((originDataItem: any) => {
          if (
            originDataItem.type === 'measureDataValue' &&
            originDataItem.measureDataRow === rowSquence
          ) {
            measureTableList.push({
              measureDataValue: originDataItem[colums],
              measureDataColumn: colums.replace('measureDataColumn', ''),
              measureDataRow: rowSquence,
            });
          }
        });
      });
      resultData.push({
        measureDate,
        measureTableList,
      });
    });

    const formData = formDs.toData();
    myInstance
      .post(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/save/ui`, {
        msaTaskLineId: props.msaTaskLineId,
        ...formData[0],
        tableInfo: resultData,
      })
      .then(res => {
        if (res.data.success) {
          // 处理数据
          setApiData(res.data.rows.tableInfo);
          setHaveSaveData(true);
          props.updateHeaderInfo(); // 更新头部
          notification.success({});
          handleUpdateChartData(res.data.rows || {});
        } else {
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          });
        }
      });
  };

  // 模板下载
  const downloadTemp = async () => {
    await downloadFile({
      requestUrl: `/himp/v1/${tenantId}/template/YP.QIS_MSA_IMPORT_BIASEDNESS/excel`,
      queryParams: [{ name: 'tenantId', value: tenantId }],
      method: 'GET',
    });
  };

  const handleUploadSuccess = res => {
    setApiData(res.rows.tableInfo);
    setHaveSaveData(false);
    notification.success({
      message: intl.get(`${modelPrompt}.notification.importSuccess`).d('导入成功'),
    });
  };

  const excelUploadProps: ExcelUploadProps = {
    url: `${API_HOST}${
      BASIC.TARZAN_SAMPLING
    }/v1/${tenantId}/qis-msa-analysis/import/ui?msaTaskLineId=${
      props.msaTaskLineId
    }&standardValue=${formDs.current?.get('standardValue') ||
      ''}&expectedDeterioration=${formDs.current?.get('expectedDeterioration') ||
      ''}&measuredBy=${formDs.current?.get('measuredBy') || ''}`,
    params: {},
    onSuccess: res => handleUploadSuccess(res),
  };

  return (
    <div>
      <Button
        color={ButtonColor.primary}
        onClick={handleSave}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        {intl.get(`${modelPrompt}.button.save`).d('保存')}
      </Button>
      <Button onClick={downloadTemp} icon="get_app">
        {intl.get(`${modelPrompt}.templateDownload`).d('模板下载')}
      </Button>
      <ExcelUpload
        {...excelUploadProps}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      />
      <Form
        dataSet={formDs}
        columns={3}
        style={{ marginTop: 10 }}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        <NumberField name="standardValue" />
        <NumberField name="expectedDeterioration" />
        <TextField name="sampleDescription" />
        <Lov name="improveByObj" />
      </Form>
      <Table
        columnDraggable
        columnTitleEditable
        aggregation={false}
        border
        dataSet={userDs}
        columns={currentColumns as any}
        groups={groups as any}
      />
      <DeflectiveGraphic dataSoure={graphicData} analyseResultDs={analyseResultDs} />
    </div>
  );
};

export default Deflective;
