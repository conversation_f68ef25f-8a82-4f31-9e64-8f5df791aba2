/**
 * @Description: 抽样方式维护
 * @Author: <<EMAIL>>
 * @Date: 2022-12-27 16:23:37
 * @LastEditTime: 2023-05-18 16:36:35
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { listTableDS } from '../stores/SamplingModeDS';

const modelPrompt = 'tarzan.sampling.samplingMethod.samplingMode';

const SamplingModeList = props => {
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;

  useEffect(() => {
    tableDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_LIST.TABLE`,
    );

    if (tableDs?.currentPage) {
      tableDs.query(props.tableDs.currentPage);
    } else {
      tableDs.query();
    }
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'samplingMethodCode',
      lock: ColumnLock.left,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
      width: 120,
    },
    {
      name: 'samplingMethodDesc',
      width: 120,
    },
    {
      name: 'samplingTypeDesc',
    },
    {
      name: 'samplingMethodValue',
      width: 120,
    },
    {
      name: 'processMode',
      width: 120,
    },
    {
      name: 'samplingStandard',
    },
    {
      name: 'samplingPlanType',
      width: 120,
    },
    {
      name: 'strictness',
    },
    {
      name: 'aql',
    },
    {
      name: 'inspectionLevel',
    },
    {
      name: 'acceptQty',
    },
    {
      name: 'rejectQty',
    },
    {
      name: 'enableFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  const handleEdit = record => {
    props.history.push(
      `/sampling/sampling-method/sampling-mode/detail/${record.get('samplingMethodId')}`,
    );
  };

  const handleCreate = () => {
    props.history.push(`/sampling/sampling-method/sampling-mode/detail/create`);
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.samplingModeMaintenance`).d('抽样方式维护')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleCreate()}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_LIST.TABLE`,
          },
          <Table
            searchCode="cyfswh1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
          />,
        )}
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.sampling.samplingMethod.samplingMode', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...listTableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_LIST.TABLE`],
    })(SamplingModeList as any),
  ),
);
