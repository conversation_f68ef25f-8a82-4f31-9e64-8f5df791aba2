import intl from 'utils/intl';
// import { Host } from '@/utils/config';
// import { BASIC } from '@utils/config';
import { BASIC, HZERO_PLATFORM } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro/lib';

const Host = BASIC.HMES_BASIC;
// const Host = '/mes-202207';
const organizationId = getCurrentOrganizationId();

export const tableDs = {
  // autoCreate: true,
  // autoQuery: true,
  currentPage: 1,
  transport: {
    read: config => {
      const url = `${Host}/v1/${organizationId}/itf-onhand-quantity-ifaces/list/StandingCrop`;
      const serviceConfig = {
        ...config,
        url,
        method: 'GET',
      };
      return serviceConfig;
    },
  },
  fields: [
    {
      name: 'plantCode',
      type: 'string',
      label: intl.get('hmes.operationPlatform.view.message.factory').d('工厂'),
    },
    {
      name: 'locatorCode',
      type: 'string',
      label: intl.get('hmes.operationPlatform.view.message.locatorCode').d('库位编码'),
    },
    {
      name: 'locatorType',
      type: 'string',
      label: intl.get('hmes.operationPlatform.view.message.locatorType').d('库位类型'),
    },
    {
      name: 'locatorLocation',
      type: 'string',
      label: intl.get('hmes.operationPlatform.view.message.locatorLocation').d('库位地点'),
    },
    {
      name: 'materialCode',
      type: 'string',
      label: intl.get('hmes.operationPlatform.view.message.materialCode').d('物料编码'),
    },
    {
      name: 'quantity',
      type: 'number',
      label: intl.get('hmes.operationPlatform.view.message.quantity').d('数量'),
    },
    {
      name: 'uomCode',
      type: 'string',
      label: intl.get('hmes.operationPlatform.view.message.uomCode').d('单位'),
    },
    {
      name: 'batchDate',
      type: 'date',
      label: intl.get('hmes.operationPlatform.view.message.batchDate').d('批次更新时间'),
    },
  ],
  queryFields: [
    {
      name: 'plantCode',
      type: 'string',
      required: true,
      label: intl.get('hmes.operationPlatform.view.message.factory').d('工厂'),
      textField: 'siteName',
      valueField: 'siteCode',
      options: new DataSet({
        selection: 'single',
        paging: false,
        dataKey: null,
        transport: {
          read() {
            return {
              url: `${HZERO_PLATFORM}/v1/${organizationId}/lovs/data`,
              params: {
                lovCode: 'WMS.MOD_SITE',
              },
              method: 'GET',
            };
          },
        },
        autoQuery: true,
      }),
    },
    {
      name: 'materialId',
      type: 'object',
      label: intl.get('hmes.operationPlatform.view.message.materialCode').d('物料编码'),
      lovCode: 'WMS.MATERIAL_CODE',
      lovPara: {
        tenantId:organizationId,
      },
      textField: 'materialCode',
      valueField: 'materialId',
      noCache: true,
      required: true,
      ignore: 'always',
    },
    {
      name: 'materialCode',
      type: 'string',
      label: intl.get(`hmes.operationPlatform.view.message.materialCode`).d('物料编码'),
      // required: true,
      bind: 'materialId.materialCode',
    },
    {
      name: 'locatorCode',
      type: 'string',
      label: intl.get('hmes.operationPlatform.view.message.locatorCode').d('库位编码'),
    },
  ],
};
