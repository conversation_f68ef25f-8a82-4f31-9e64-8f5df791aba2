/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-12-14
 * @description 执行执行规则维护-列表页
 */
import React, { FC, useEffect, useState, useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Badge, Popconfirm } from 'hzero-ui';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Tag } from 'choerodon-ui';
import { BASIC } from '@utils/config';
import myInstance from '@utils/myAxios';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { listDS } from './stories/listDs';

const modelPrompt = 'tarzan.commonConfig.orderExecuteRuleMes';

const tenantId = getCurrentOrganizationId();

interface OrderExecuteRuleMesProps extends RouteComponentProps {
  tableDs: DataSet;
  customizeTable: any;
}

const OrderExecuteRuleMes: FC<OrderExecuteRuleMesProps> = (props) => {

  const {
    match: { path },
    tableDs,
    history,
    customizeTable,
  } = props;

  // 存储选中数据的key
  const [selectedKey, setSelectedKey] = useState([]);

  // 监听C7N pro列表选中操作与数据加载完后事件（load）
  useEffect(() => {
    if (tableDs) {
      tableDs.addEventListener('select', handleDataSetSelectUpdate);
      tableDs.addEventListener('unSelect', handleDataSetSelectUpdate);
      tableDs.addEventListener('selectAll', handleDataSetSelectUpdate);
      tableDs.addEventListener('unSelectAll', handleDataSetSelectUpdate);
      tableDs.addEventListener('load', handleQueryDataSetLoad);
      tableDs.queryDataSet!.addEventListener('update', handleQueryDataSetUpdate);
      tableDs.addEventListener('load', handleDataSetSelectUpdate);
    }
    return () => {
      if (tableDs) {
        tableDs.removeEventListener('select', handleDataSetSelectUpdate);
        tableDs.removeEventListener('unSelect', handleDataSetSelectUpdate);
        tableDs.removeEventListener('selectAll', handleDataSetSelectUpdate);
        tableDs.removeEventListener('unSelectAll', handleDataSetSelectUpdate);
        tableDs.removeEventListener('load', handleQueryDataSetLoad);
        tableDs.queryDataSet!.removeEventListener('update', handleQueryDataSetUpdate);
        tableDs.removeEventListener('load', handleDataSetSelectUpdate);
      }
    };
  }, []);

  // 初始化标志Y,没有权限的不可选
  const handleQueryDataSetLoad = () => {
    if (!tableDs.records.length) return;
    tableDs.records.forEach(i => {
      // @ts-ignore
      if (i.data.autoInstallPointFlag === 'Y') {
        // eslint-disable-next-line
        i.selectable = false;
      }
    });
  };

  const handleQueryDataSetUpdate = ({ record }) => {
    if (record.get('instructionDocExeStrategy') !== 'TWO_STEP') {
      record.set('instructionCreateMode', '');
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'instructionDocType',
        lock: ColumnLock.left,
        width: 280,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/apex-hmes/commonConfig/order-execute-rule/detail/${record!.get('instructionDocExeRuleId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'typeDescription', align: ColumnAlign.left },
      {
        name: 'instructionDocExeStrategy',
        renderer: ({ value }) => {
          if (value === 'ONE_STEP') {
            return (
              <Tag color="orange">{intl.get(`${modelPrompt}.oenStep`).d('一步法')}</Tag>
            );
          } if (value === 'TWO_STEP') {
            return (
              <Tag color="blue">{intl.get(`${modelPrompt}.twoStep`).d('两步法')}</Tag>
            );
          } if (value === 'No_Exe_Strategy') {
            return (
              <Tag color="green">
                {intl.get(`${modelPrompt}.NoExeStrategy`).d('无执行策略')}
              </Tag>
            );
          }
          return '';
        },
        align: ColumnAlign.left,
      },
      {
        name: 'instructionCreateMode',
        width: 120,
        align: ColumnAlign.left,
      },
      { name: 'statusGroup', width: 400, align: ColumnAlign.left },
      {
        name: 'fromLocatorRequiredFlag',
        width: 140,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('fromLocatorRequiredFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('fromLocatorRequiredFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        align: ColumnAlign.center,
      },
      {
        name: 'toLocatorRequiredFlag',
        width: 140,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('toLocatorRequiredFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('toLocatorRequiredFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        align: ColumnAlign.center,
      },
      {
        name: 'accountCategoryDesc',
        width: 120,
        align: ColumnAlign.center,
      },
      {
        name: 'initialFlag',
        renderer: ({ record }) => (
          <Badge
            status={record!.get('initialFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('initialFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        align: ColumnAlign.center,
      },
    ];
  }, [])

  const goDetail = () => {
    history.push(`/apex-hmes/commonConfig/order-execute-rule/detail/create`);
  };

  useEffect(() => {
    handleDataSetSelectUpdate();
  }, []);

  // 删除选中的数据
  const deleteSelectData = () => {
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-instruction-doc-exe-rules/delete/ui`;
    const data = [...selectedKey];
    myInstance.post(url, data).then(res => {
      if (res.data.success) {
        notification.success({});
        // 删除成功后重查列表
        tableDs.query();
      } else if (res.data.message) {
        notification.error({
          description: res.data.message,
        });
      }
    });
  };

  // 处理选中条状态
  const handleDataSetSelectUpdate = () => {
    if (tableDs && tableDs.selected) {
      const selectList = tableDs.selected;
      if (selectList && selectList.length) {
        const arr = [];
        selectList.forEach(i => {
          // @ts-ignore
          arr.push(i.data.instructionDocExeRuleId);
        });
        setSelectedKey(arr);
      } else {
        setSelectedKey([]);
      }
    } else {
      setSelectedKey([]);
    }
  };

  const onFieldEnterDown = () => {
    tableDs.query(props.tableDs.currentPage);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.order-execute-rule`).d('指令执行规则维护-MES')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={goDetail}
          permissionList={[
            {
              code: `tarzan${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Popconfirm
          title={intl
            .get(`${modelPrompt}.confirm.delete`, {
              count: selectedKey.length,
            })
            .d(`总计${selectedKey.length}条数据，是否确认删除?`)}
          onConfirm={deleteSelectData}
        >
          <PermissionButton
            type="c7n-pro"
            icon="delete_black-o"
            disabled={!selectedKey.length}
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.delete').d('删除')}
          </PermissionButton>
        </Popconfirm>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.LIST`,
          },
          <Table
            searchCode="orderExecuteRuleMes"
            customizedCode="orderExecuteRuleMes"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            dataSet={tableDs}
            columns={columns as ColumnProps[]}
            queryFieldsLimit={3}
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.commonConfig.orderExecuteRuleMes', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...listDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.LIST`] }),
)(OrderExecuteRuleMes);
