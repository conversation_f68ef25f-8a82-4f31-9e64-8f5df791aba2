/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-12-14
 * @description 执行执行规则维护-列表DS
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.commonConfig.orderExecuteRule';
const tenantId = getCurrentOrganizationId();
const instructionCreateModeOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_CREATE_MODE', tenantId },
      };
    },
  },
});

const instructionDocExeStrategyOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_DOC_EXE_STRATEGY', tenantId },
      };
    },
  },
});

const statusOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_DOC_EXE_STRATEGY', tenantId },
      };
    },
  },
});

const listDS = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: 'multiple',
  primaryKey: 'assembleGroupId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'businessTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('指令单据类型'),
      lovCode: 'APEX_WMS.INSTRUCTION_DOC_TYPE',
      lovPara: {
        tenantId,
        typeGroup: 'INSTRUCTION_DOC_TYPE',
      },
      ignore: 'always',
    },
    {
      name: 'instructionDocType',
      bind: 'businessTypeObj.value',
    },
    {
      name: 'businessObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      lovCode: 'APEX_WMS.COMMON.BUSINESS_TYPE',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
    },
    {
      name: 'businessType',
      bind: 'businessObj.typeCode',
    },
    {
      name: 'instructionTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionType`).d('移动类型'),
      lovCode: 'APEX_WMS.COMMON.INSTRUCTION_TYPE',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
    },
    {
      name: 'instructionType',
      bind: 'instructionTypeObj.typeCode',
    },
    {
      name: 'instructionDocExeStrategy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocExeStrategy`).d('单据执行策略'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSTRUCTION_DOC_EXE_STRATEGY`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'instructionCreateMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionCreateMode`).d('指令创建模式'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSTRUCTION_CREATE_MODE`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('instructionDocExeStrategy') !== 'TWO_STEP';
        },
      },
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialFlag`).d('初始化'),
      lookupCode: 'APEX_WMS.INITIAL_FLAG',
    },
  ],
  fields: [
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('指令单据类型'),
    },
    {
      name: 'typeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.typeDescription`).d('类型描述'),
    },
    {
      name: 'instructionDocExeStrategy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocExeStrategy`).d('单据执行策略'),
      options: instructionDocExeStrategyOptionDs,
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'instructionCreateMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionCreateMode`).d('指令创建模式'),
      options: instructionCreateModeOptionDs,
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'statusGroup',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusGroup`).d('单据状态值集'),
    },
    // {
    //   name: 'onPassageLocatorTypeDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.onPassageLocatorType`).d('在途库位类型'),
    // },
    // {
    //   name: 'onPassageLocatorDirectionDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.onPassageLocatorDirection`).d('在途库位方向'),
    // },
    {
      name: 'fromLocatorRequiredFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.fromLocatorRequiredFlag`).d('来源库位必输标识'),
    },
    {
      name: 'toLocatorRequiredFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.toLocatorRequiredFlag`).d('目标库位必输标识'),
    },
    {
      name: 'accountCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountCategory`).d('结算类别'),
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.initialFlag`).d('初始化'),
    },
  ],
  transport: {
    read: () => {
      // 查询请求的 axios 配置或 url 字符串
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-instruction-doc-exe-rules/query/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.LIST`,
        method: 'POST',
      };
    },
  },
});

export { listDS, statusOptionDs, instructionCreateModeOptionDs, instructionDocExeStrategyOptionDs };
