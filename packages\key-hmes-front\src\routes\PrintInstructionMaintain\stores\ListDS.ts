/**
 * 打印指令模版维护 - store
 * @Author: 20379 <EMAIL>
 * @Date: 2024-01-23 15:32:25
 * @LastEditors: 20379 <EMAIL>
 * @LastEditTime: 2024-01-23 15:36:47
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'hmes.bartenderTemplateMaintain.field';
const tenantId = getCurrentOrganizationId();

const listDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'instructionTemplateId',
  transport: {
    read: () => ({
      url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-prt-instruction-templates/list/ui`,
      method: 'GET',
    }),
    submit: () => ({
      url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-prt-instruction-templates/batch/save/ui`,
      method: 'POST',
    }),
    destroy: ({ data }) => ({
      url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-prt-instruction-templates/batch/delete/ui`,
      method: 'POST',
      data: data.map(e => e.instructionTemplateId),
    }),
  },
  queryFields: [
    {
      name: 'instructionTemplateCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionTemplateCode`).d('模版编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('模版名称'),
    },
    {
      name: 'instructionLang',
      type: FieldType.string,
      lookupCode: "MT.COMMON.PRINT_INSTRUCTION_LANG",
      label: intl.get(`${modelPrompt}.instructionLang`).d('指令类型'),
    },
  ],
  fields: [
    {
      name: 'instructionTemplateCode',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.instructionTemplateCode`).d('模版编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.description`).d('模版名称'),
    },
    {
      name: 'instructionLang',
      type: FieldType.string,
      required: true,
      lookupCode: "MT.COMMON.PRINT_INSTRUCTION_LANG",
      label: intl.get(`${modelPrompt}.instructionLang`).d('指令类型'),
    },
    {
      name: 'copies',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.copies`).d('打印份数'),
    },
    {
      name: 'instructionContent',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.instructionContent`).d('指令内容'),
    },
  ],
});

export { listDS };
