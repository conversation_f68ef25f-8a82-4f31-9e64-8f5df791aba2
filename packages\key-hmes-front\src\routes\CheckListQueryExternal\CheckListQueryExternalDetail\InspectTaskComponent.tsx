/**
 * @Description: 检验单维护-详情页 检验任务信息组件
 * @Author: <EMAIL>
 * @Date: 2023/2/17 16:19
 */
import React, { useEffect, useState } from 'react';
import { Attachment, Form, Output, Select, NumberField } from 'choerodon-ui/pro';
import { Collapse, Tag } from 'choerodon-ui';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { BASIC } from 'hcm-components-front/lib/utils/config';
import { AttributeDrawer } from 'hcm-components-front/lib/components/tarzan-ui';
import PanelTable from './PanelTable';
import styles from './index.module.less';

const { Panel } = Collapse;
const { ItemGroup, Item } = Form;
const modelPrompt = 'tarzan.hwms.CheckListQueryExternal';


const InspectTaskComponent = (props) => {
  const { customizeForm, customizeTable, custConfig, history, taskInfoDs, defaultKey, editing } = props;
  const [activeKey, setActiveKey] = useState<any>(defaultKey);
  useEffect(() => setActiveKey(defaultKey), [defaultKey]);

  const CollapsePanelTitle = observer(({ record, editing }) => {
    const renderStatusTag = record => {
      const { inspectTaskStatus, inspectTaskStatusDesc } = record.toData();
      switch (inspectTaskStatus) {
        case 'RELEASED':
          return <Tag color="orange">{inspectTaskStatusDesc}</Tag>;
        case 'CANCEL':
          return <Tag color="grey">{inspectTaskStatusDesc}</Tag>;
        case 'COMPLETED':
          return <Tag color="blue">{inspectTaskStatusDesc}</Tag>;
        default:
          return <Tag color="yellow">{inspectTaskStatusDesc}</Tag>;
      }
    };

    const handleJump = record => {
      if (record?.get('operatingEnd') === 'PC-2') {
        history.push(`/hwms/inspection-platform-workshop/dist/${record?.get('inspectTaskId')}`);
      } else {
        history.push(`/hwms/inspection-platform/dist/${record?.get('inspectTaskId')}`);
      }
    };

    return (
      <span>
        <span className={styles['panel-title-code']}>
          {record.get('inspectTaskCode')}
        </span>
        {renderStatusTag(record)}
        <span className={styles['panel-title-item']}>
          {intl.get(`${modelPrompt}.inspectTask.inspectTaskType`).d('检验任务类别：')}
          {record.get('inspectTaskTypeDesc')}
        </span>
        <span className={styles['panel-title-item']}>
          {intl.get(`${modelPrompt}.inspectTask.inspectorName`).d('检验员：')}
          {record.get('inspectorName') || ' '}
        </span>
        {(record.get('actualStartTime') || record.get('actualEndTime')) && (
          <span className={styles['panel-title-item']}>
            {intl.get(`${modelPrompt}.inspectTask.inspectionTime`).d('检验时间：')}
            {record.get('actualStartTime') || ' '}{' '}
            {record.get('actualEndTime')
              ? `~${record.get('actualEndTime')}`
              : record.get('actualEndTime')}
          </span>
        )}
        {record?.get('inspectResult') === 'OK' && !editing && (
          <Tag color="green">{record?.get('inspectResultDesc')}</Tag>
        )}
        {record?.get('inspectResult') === 'NG' && !editing && (
          <Tag color="magenta">{record?.get('inspectResultDesc')}</Tag>
        )}
        {editing && (
          <Select record={record} name='inspectResult' />
        )}
        {record?.get('sourceTaskCode') && (
          <span className={styles['panel-title-item']}>
            <Tag color="blue">
              {intl.get(`${modelPrompt}.inspectTask.sourceTaskCode`).d('复检 关联任务:')}
              {record.get('sourceTaskCode') || ' '}
            </Tag>
          </span>
        )}
      </span>
    );
  });

  const handleChangePanel = props => setActiveKey(props);

  const panelDetailEnclosure: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const displayQtyValue = text => {
    if (text?.value && text?.value !== '') {
      const colorList = ['#11d954', '#ff3333', '#fbad00'];
      const textList = text.value.split('/');
      return (
        <>
          {textList.map((text, index) => {
            return (
              <span
                style={{
                  color: colorList[index],
                }}
              >
                {` ${text} `}
              </span>
            );
          })}
        </>
      );
    }
    return <></>;
  };

  return (
    <Collapse activeKey={activeKey} onChange={handleChangePanel} collapsible="icon">
      {taskInfoDs?.length &&
        taskInfoDs.map(record => (
          <Panel
            key={String(record.get('inspectTaskId'))}
            header={<CollapsePanelTitle record={record} editing={editing} />}
            className={styles['panel-item']}
            extra={
              <AttributeDrawer
                serverCode={BASIC.TARZAN_SAMPLING}
                className="org.tarzan.qms.domain.entity.MtInspectInfoDtl"
                kid={record?.get('inspectTaskId')}
                canEdit={false}
                disabled={!record?.get('inspectTaskId')}
                custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.TASK_ATTR`}
                custConfig={custConfig}
                type="text"
              />
            }
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.TASK_BASIC`,
              },
              <Form record={record} labelWidth="auto" columns={6}>
                {!editing && <Output name="displayQty" renderer={displayQtyValue} />}
                {editing && (
                  <ItemGroup
                    // @ts-ignore
                    name="itemGroup1"
                    label={intl.get(`${modelPrompt}.inspectTask.displayQty`).d('合格品数 不合格品数 报废数')}
                  >
                    <Item>
                      <NumberField name="okQty" />
                    </Item>
                    <Item>
                      <NumberField name="ngQty" />
                    </Item>
                    <Item>
                      <NumberField name="scrapQty" />
                    </Item>
                  </ItemGroup>
                )}
                {!editing && <Output name="samplingQty" renderer={displayQtyValue} />}
                {editing && (
                  <ItemGroup
                    // @ts-ignore
                    name="itemGroup2"
                    label={intl.get(`${modelPrompt}.inspectTask.itemGroup2`).d('抽样数量 合格数 不合格数')}
                  >
                    <Item>
                      <NumberField name="samplQty" />
                    </Item>
                    <Item>
                      <NumberField name="samplingOkQty" />
                    </Item>
                    <Item>
                      <NumberField name="samplingNgQty" />
                    </Item>
                  </ItemGroup>
                )}
                <Output name="remark" />
                <Attachment readOnly={!editing} {...panelDetailEnclosure} />
              </Form>,
            )}
            <PanelTable record={record} customizeTable={customizeTable} custConfig={custConfig} editing={editing} />
          </Panel>
        ))}
    </Collapse>
  );
};

export default InspectTaskComponent;
