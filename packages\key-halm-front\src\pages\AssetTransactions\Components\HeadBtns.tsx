/*
 * @Description: 资产事务公共按钮
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-03-28 13:42:30
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */

// TODO: 目前资产事务头行上的附件都只在单据头为拟定的状态才可上传删除附件

import React, { FC, MouseEventHandler, useState } from 'react';
import { Button, Menu, Dropdown, Icon } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum'; // FuncType
import { isFunction } from 'lodash';
import FileUpload from 'alm/components/FileUpload';
import ApprovalHistoryBtn from 'alm/components/ApprovalHistoryBtn';
import { History } from 'history';
import getLang from '../Langs';

export interface IHeadBtnsStyle {
  [key: string]: React.CSSProperties;
}

interface Props {
  history: History;
  detail: any;
  isNew: boolean;
  saveLoading: boolean;
  assetTransUploadAttrData: any[];
  headBtnsStyle: IHeadBtnsStyle;
  onSave: MouseEventHandler<HTMLElement>;
  onSwitchEdit: MouseEventHandler<HTMLElement>;
  onCancel: Function;
  onWithdraw: Function;
  onSubmit: Function;
}

const HeadBtns: FC<Props> = props => {
  const {
    history,
    detail,
    isNew,
    assetTransUploadAttrData = [],
    saveLoading,
    headBtnsStyle,
    onSave,
    onSwitchEdit,
    onWithdraw,
    onSubmit,
    onCancel,
  } = props;

  const {
    saveBtnSty,
    editBtnSty,
    closeBtnSty,
    recallBtnSty,
    approveHistoryBtnSty,
    submitBtnSty,
    dropBtnSty,
  } = headBtnsStyle;

  // 附件上传附件列参数
  const assetFileUploadAttribute = {
    data: assetTransUploadAttrData,
  };

  const [recallLoading, setRecallLoading] = useState(false);
  const handleWithdraw = async () => {
    if (isFunction(onWithdraw)) {
      try {
        setRecallLoading(true);
        await onWithdraw();
      } finally {
        setRecallLoading(false);
      }
    }
  };

  const [submitLoading, setSubmitLoading] = useState(false);
  const handleSubmit = async () => {
    if (isFunction(onSubmit)) {
      setSubmitLoading(true);
      await onSubmit();
      setSubmitLoading(false);
    }
  };

  const menu = (
    <Menu>
      <Menu.Item onClick={() => onCancel('CANCEL')}>{getLang('CANCEL')}</Menu.Item>
    </Menu>
  );

  return (
    <>
      <ApprovalHistoryBtn
        style={approveHistoryBtnSty}
        wkInstanceId={detail?.wkInstanceId}
        history={history}
      />
      <Dropdown overlay={menu}>
        <Button style={dropBtnSty}>
          <Icon type="arrow_drop_down" style={{ fontSize: 14 }} />
        </Button>
      </Dropdown>
      <Button
        icon="submit"
        color={ButtonColor.primary}
        style={submitBtnSty}
        loading={submitLoading}
        onClick={handleSubmit}
      >
        {getLang('SUBMIT_OA')}
      </Button>
      <Button
        color={ButtonColor.primary}
        style={saveBtnSty}
        icon="save"
        onClick={onSave}
        loading={saveLoading}
      >
        {getLang('SAVE')}
      </Button>
      <Button icon="edit" style={editBtnSty} color={ButtonColor.primary} onClick={onSwitchEdit}>
        {getLang('EDIT')}
      </Button>
      <Button icon="close" style={closeBtnSty} onClick={onSwitchEdit}>
        {getLang('CLOSE')}
      </Button>
      {/* <Button
        icon="submit"
        color={ButtonColor.red}
        style={recallBtnSty}
        loading={recallLoading}
        onClick={handleWithdraw}
      >
        {getLang('WITHDRAW')}
      </Button> */}
      {!isNew && (
        <FileUpload
          uploadButtonName={{ code: 'asset-attachment-management', name: '' }}
          moduleName="aatn-asset-change-header"
          moduleId={detail?.changeHeaderId}
          showDeleteFlag={detail?.processStatus === 'DRAFT'}
          attribute={assetFileUploadAttribute}
        />
      )}
    </>
  );
};

export default HeadBtns;
