import React, { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import echarts from 'echarts';
import {
  isNumber,
  max as lodashMax,
  min as lodashMin,
  floor as lodashFloor,
  ceil as lodashCeil,
} from 'lodash';
import intl from 'utils/intl';
import _uuid from 'uuid/v4';

const modelPrompt = 'tarzan.hspc.chartInfo';

let mouseMoveTimeout = false;
let dataZoomTimeout = false;
const chart = {};

const TestHistoricalGraphicChart = (props, ref) => {
  const [pageInit, setpageInit] = useState(false);
  const [dataUuid, setDataUuid] = useState();
  const [chartId] = useState(_uuid());
  const chartRef = useRef();
  const linkRef = useRef();

  const {
    onHandleClick = e => e,
    onHandleZoom = e => e,
    onHover = e => e,
    chartData,
    chartToolsConfig,
  } = props;

  // useEffect(() => {
  //   return () => {
  //     delete chart[chartId];
  //   };
  // }, []);

  const {
    chartType,
    mainConfig,
    secondConfig,
    dataId,
    callipers,
    measure,
    mainData,
    mainDataUcl,
    mainDataLcl,
    secondData,
    mainOccList,
    secondOccList,
    xTickLabel,
    uuid,
    pageType,
  } = chartData;

  useEffect(() => {
    // 初始化chart实例
    if (!chartType) {
      return;
    }
    if (!pageInit) {
      chart[chartId] = echarts.init(chartRef.current);
      window.onresize = () => {
        setTimeout(() => {
          chart[chartId].resize();
        }, 300);
      };
    }

    const option = {};

    option.backgroundColor = '#fff';
    // 处理数据的缩放
    if (!pageInit || uuid !== dataUuid) {
      if (mainData && mainData.length > 50) {
        const dataZoomXAxisIndex = [0];
        if (secondConfig) {
          dataZoomXAxisIndex.push(1);
        }
        option.dataZoom = [
          {
            realtime: true,
            startValue: 0,
            endValue: mainData.length > 5000 ? 5000 : mainData.length, // 要根据数据量改
            minValueSpan: 50,
            maxValueSpan: mainData.length > 5000 ? 5000 : mainData.length,
            xAxisIndex: dataZoomXAxisIndex,
          },
          {
            type: 'inside',
            realtime: true,
            startValue: 0,
            endValue: mainData.length > 5000 ? 5000 : mainData.length, // 要根据数据量改
            minValueSpan: 50,
            maxValueSpan: mainData.length > 5000 ? 5000 : mainData.length,
            xAxisIndex: dataZoomXAxisIndex,
          },
        ];
      } else {
        option.dataZoom = [];
      }
    } else {
      const _option = chart[chartId].getOption();
      if (_option.dataZoom) {
        option.dataZoom = _option.dataZoom;
      }
    }

    // 处理Y轴 main 上下限
    const _yAxis = [
      {
        type: 'value',
        ...yAxisRange(
          mainConfig,
          chartToolsConfig,
          (chartType === 'P' && pageType !== 'control') ||
          (chartType === 'U' && pageType !== 'control'),
        ),
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.03)',
          },
        },
      },
    ];
    if (secondConfig) {
      _yAxis.push({
        gridIndex: 1,
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.03)',
          },
        },
        ...yAxisRange(
          secondConfig,
          chartToolsConfig,
          (chartType === 'P' && pageType !== 'control') ||
          (chartType === 'U' && pageType !== 'control'),
        ),
      });
    }
    option.yAxis = _yAxis;
    // 处理X轴
    let _callipers = callipers;
    if (xTickLabel === 'ATTRIBUTE' && chartToolsConfig.showXAttr) {
      _callipers = measure;
    }
    const _xAxis = [
      {
        type: 'category',
        boundaryGap: false,
        data: _callipers,
      },
    ];
    if (secondConfig) {
      _xAxis.push({
        gridIndex: 1,
        type: 'category',
        boundaryGap: false,
        data: _callipers,
      });
    }
    option.xAxis = _xAxis;

    // 处理浮动格式化
    const _tooltip = {
      trigger: 'axis',
      axisPointer: {
        animation: false,
      },
      borderColor: '#fff',
      backgroundColor: '#fff',
      extraCssText: 'box-shadow: 5px 5px 5px #888888;',
      textStyle: {
        color: '#4c4c4c',
      },
      formatter: '{b0}<br />{a0}: {c0}',
    };

    if (secondConfig) {
      _tooltip.formatter = '{b0}<br />{a0}: {c0}<br />{a1}: {c1}';
    }
    if (
      (chartType === 'P' && pageType !== 'control') ||
      (chartType === 'U' && pageType !== 'control')
    ) {
      _tooltip.formatter = '{b0}<br />{a0}: {c0}<br />{a1}: {c1}<br />{a2}: {c2}';
    }
    option.tooltip = _tooltip;

    // 处理是否上下同时显示
    if (secondConfig) {
      option.axisPointer = {
        link: { xAxisIndex: 'all' },
      };
    }

    // 处理上下图标大小和位置配置
    if (secondConfig) {
      option.grid = [
        {
          id: 'main',
          left: 70,
          right: 100,
          top: '5%',
          height: '35%',
        },
        {
          id: 'second',
          left: 70,
          right: 100,
          top: '50%',
          height: '35%',
        },
      ];
    } else {
      option.grid = [
        {
          id: 'main',
          left: 70,
          right: 100,
          height: '75%',
        },
      ];
    }
    const chartTypeMap = {
      P: [intl.get(`${modelPrompt}.p`).d('不合格品率')],
      U: [intl.get(`${modelPrompt}.u`).d('单位缺陷数')],
      nP: [intl.get(`${modelPrompt}.np`).d('不合格数')],
      C: [intl.get(`${modelPrompt}.c`).d('缺陷数')],
      'X-MR': [
        intl.get(`${modelPrompt}.X`).d('单值'),
        intl.get(`${modelPrompt}.movingRange`).d('移动极差'),
      ],
      'Me-R': [
        intl.get(`${modelPrompt}.median`).d('中位数'),
        intl.get(`${modelPrompt}.range`).d('极差'),
      ],
      'XBAR-S': [
        intl.get(`${modelPrompt}.average`).d('均值'),
        intl.get(`${modelPrompt}.sigma`).d('标准差'),
      ],
      'XBAR-R': [
        intl.get(`${modelPrompt}.average`).d('均值'),
        intl.get(`${modelPrompt}.range`).d('极差'),
      ],
    };

    // 处理上下图表数据
    const _series = [
      {
        name: chartTypeMap[chartType][0],
        type: 'line',
        symbol: 'circle',
        symbolSize: 6,
        hoverAnimation: false,
        color: '#1890ff',
        markLine: {
          silent: true,
          symbol: 'none',
          data: markLineFormat(
            mainConfig,
            chartToolsConfig,
            (chartType === 'P' && pageType !== 'control') ||
            (chartType === 'U' && pageType !== 'control'),
          ),
        },
        markPoint: {
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#f71a1b',
          },
          label: {
            position: [10, -10],
          },
          data: mainOccList,
        },
        markArea: {
          silent: true,
          label: {
            show: true,
            position: 'insideLeft',
            color: '#000',
            fontSize: 10,
          },
          itemStyle: {
            color: 'rgba(0,0,0,0)',
          },
          data: markAreaFormat(mainConfig, chartToolsConfig),
        },
        data: mainData,
      },
    ];
    if (secondConfig) {
      _series.push({
        name: chartTypeMap[chartType][1],
        type: 'line',
        xAxisIndex: 1,
        yAxisIndex: 1,
        symbol: 'circle',
        symbolSize: 6,
        hoverAnimation: false,
        color: '#1890ff',
        markLine: {
          silent: true,
          symbol: 'none',
          data: markLineFormat(
            secondConfig,
            chartToolsConfig,
            (chartType === 'P' && pageType !== 'control') ||
            (chartType === 'U' && pageType !== 'control'),
          ),
        },
        markPoint: {
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#f71a1b',
          },
          label: {
            position: [10, -10],
          },
          data: secondOccList,
        },
        markArea: {
          silent: true,
          label: {
            show: true,
            position: 'insideLeft',
            color: '#000',
            fontSize: 10,
          },
          itemStyle: {
            color: 'rgba(0,0,0,0)',
          },
          data: markAreaFormat(secondConfig, chartToolsConfig),
        },
        data: secondData,
      });
    }
    if (
      (chartType === 'P' && pageType !== 'control' && chartToolsConfig.showCL) ||
      (chartType === 'U' && pageType !== 'control' && chartToolsConfig.showCL)
    ) {
      if (mainDataUcl) {
        _series.push({
          name: 'Ucl',
          type: 'line',
          step: 'middle',
          symbolSize: 1,
          symbol: 'circle',
          hoverAnimation: false,
          color: '#f71a1b',
          data: mainDataUcl,
          lineStyle: {
            type: 'dotted',
            width: 1,
          },
        });
      }
      if (mainDataLcl) {
        _series.push({
          name: 'Lcl',
          type: 'line',
          step: 'middle',
          symbolSize: 1,
          symbol: 'circle',
          hoverAnimation: false,
          color: '#f71a1b',
          data: mainDataLcl,
          lineStyle: {
            type: 'dotted',
            width: 1,
          },
        });
      }
    }
    option.series = _series;

    // 处理标题
    const _title = [];
    if (chartData.chartTitle) {
      _title.push({
        show: chartToolsConfig.showCLTitle,
        left: 'center',
        top: 0,
        textStyle: {
          overflow: 'truncate',
          color: '#333',
          fontSize: 12,
          fontWeight: 'normal',
          fontFamily:
            "'Monospaced Number', 'Chinese Quote', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif",
        },
        text: `${chartData.chartTitle}`,
      });
    }
    if (mainConfig && mainConfig.yAxisLabel) {
      _title.push({
        show: chartToolsConfig.showCLTitle,
        left: 10,
        top: secondConfig ? '25%' : '40%',
        rotate: -90,
        textStyle: {
          overflow: 'truncate',
          color: '#aeaeae',
          fontSize: 12,
          fontWeight: 'normal',
          fontFamily:
            "'Monospaced Number', 'Chinese Quote', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif",
        },
        text: `${mainConfig.yAxisLabel.split('').join('\n')}`,
      });
    }
    if (mainConfig && mainConfig.xAxisLabel) {
      _title.push({
        show: chartToolsConfig.showCLTitle,
        left: 'center',
        top: secondConfig ? '45%' : '90%',
        rotate: -90,
        textStyle: {
          overflow: 'truncate',
          color: '#aeaeae',
          fontSize: 12,
          fontWeight: 'normal',
          fontFamily:
            "'Monospaced Number', 'Chinese Quote', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif",
        },
        text: `${mainConfig.xAxisLabel}`,
      });
    }
    if (secondConfig && secondConfig.yAxisLabel) {
      _title.push({
        show: chartToolsConfig.showCLTitle,
        top: '70%',
        left: 10,
        rotate: -90,
        textStyle: {
          overflow: 'truncate',
          color: '#aeaeae',
          fontSize: 12,
          fontWeight: 'normal',
          fontFamily:
            "'Monospaced Number', 'Chinese Quote', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif",
        },
        text: `${secondConfig.yAxisLabel.split('').join('\n')}`,
      });
    }
    if (secondConfig && secondConfig.xAxisLabel) {
      _title.push({
        show: chartToolsConfig.showCLTitle,
        left: 'center',
        top: '90%',
        rotate: -90,
        textStyle: {
          overflow: 'truncate',
          color: '#aeaeae',
          fontSize: 12,
          fontWeight: 'normal',
          fontFamily:
            "'Monospaced Number', 'Chinese Quote', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif",
        },
        text: `${secondConfig.xAxisLabel}`,
      });
    }

    option.title = _title;

    chart[chartId].setOption(option, true);
    // 初始化chart事件绑定
    if (!pageInit) {
      chart[chartId].getZr().on('click', 'series.line', params => {
        getChartPoint(params, 'click');
      });
      chart[chartId].getZr().on('mousemove', 'series.line', params => {
        getChartPoint(params, 'mousemove');
      });
      chart[chartId].on('datazoom', 'series.line', () => {
        clearTimeout(dataZoomTimeout);
        dataZoomTimeout = setTimeout(() => {
          getDataZoomOption();
        }, 300);
      });
      setpageInit(true);
    }
    setDataUuid(uuid);
  }, [chartData, chartToolsConfig]);

  useImperativeHandle(ref, () => ({
    chartShowTip,
    downloadImage,
  }));

  const getChartPoint = (params, type) => {
    if (type === 'click') {
      getChartPointRun(params, type);
    } else {
      clearTimeout(mouseMoveTimeout);
      mouseMoveTimeout = setTimeout(() => {
        getChartPointRun(params, type);
      }, 300);
    }
  };

  const getDataZoomOption = () => {
    const _dataZoomOption = chart[chartId].getOption().dataZoom;
    if (_dataZoomOption[0]) {
      const { startValue, endValue } = _dataZoomOption[0];
      onHandleZoom({
        startValue,
        endValue,
      });
    }
  };

  const getChartPointRun = (params, type) => {
    // 转换像素坐标值到逻辑坐标系上的点
    const pointInPixel = [params.offsetX, params.offsetY];
    // 存点击的坐标系(上下两个图两个坐标系)
    let clickIndex;
    // 存数据索引
    let dataIndex;
    // 判断事件所在坐标系并保存坐标系和数据索引
    if (chart[chartId].containPixel({ gridId: 'main' }, pointInPixel)) {
      clickIndex = 0;
      [dataIndex] = chart[chartId].convertFromPixel({ gridId: 'main' }, pointInPixel);
    }
    if (chart[chartId].containPixel({ gridId: 'second' }, pointInPixel)) {
      clickIndex = 1;
      [dataIndex] = chart[chartId].convertFromPixel({ gridId: 'second' }, pointInPixel);
    }
    // 根据结果执行回调
    if (clickIndex === 0 || clickIndex === 1) {
      // 获得图表中我们想要的数据
      if (type === 'click') {
        onHandleClick(dataId[dataIndex], dataIndex, clickIndex);
      } else {
        onHover(dataId[dataIndex], dataIndex, clickIndex);
      }
    }
  };

  // 计算范围
  const yAxisRange = (chartConfig, toolsConfig, upLowShow) => {
    // cl 控制线 sl规格) 配置项
    const { showCL, showSL } = toolsConfig;
    // entiretyUcl 控制上限 (控制线)
    // entiretyLcl 控制下线(不计算)
    // entiretyUsl 规格上限
    // entiretyLsl 规格下线
    const {
      entiretyUcl,
      entiretyLcl,
      entiretySl,
      entiretyUsl,
      entiretyLsl,
      chartMax,
      chartMin,
    } = chartConfig;
    const _maxArr = [chartMax];
    const _minArr = [chartMin];
    if (showCL && !upLowShow) {
      _maxArr.push(entiretyUcl);
      _minArr.push(entiretyLcl);
    }
    if (showSL) {
      _maxArr.push(entiretySl);
      _maxArr.push(entiretyUsl);
      _minArr.push(entiretyLsl);
    }
    return {
      max: lodashCeil(lodashMax(_maxArr) * 100) / 100,
      min: lodashFloor(lodashMin(_minArr) * 100) / 100,
    };
  };

  const markLineFormat = (chartConfig, toolsConfig, upLowShow) => {
    // cl 控制线 sl 规格线 al 分曲线
    const { showCL, showCLSIGMA, showSL } = toolsConfig;
    const {
      entiretyCl,
      entiretyUcl,
      entiretyLcl,
      entiretySl,
      entiretyUsl,
      entiretyLsl,
    } = chartConfig;

    const markLineData = [];
    const formatLabel = {
      formatter: value => {
        return `${value.name}:${value.data.yAxis}`;
      },
      color: '#000',
      fontSize: 10,
    };
    if (showCL) {
      if (isNumber(entiretyCl)) {
        markLineData.push({
          name: 'CL',
          yAxis: lodashFloor(entiretyCl, 4).toString(),
          label: formatLabel,
          precision: 4,
          lineStyle: {
            color: '#b8b8b8',
            width: 1.2,
          },
        });
      }
      if (!upLowShow) {
        if (isNumber(entiretyUcl)) {
          markLineData.push({
            name: 'UCL',
            yAxis: lodashFloor(entiretyUcl, 4).toString(),
            label: formatLabel,
            precision: 4,
            lineStyle: {
              color: '#f71a1b',
              width: 1.2,
            },
          });
        }
        if (isNumber(entiretyLcl)) {
          markLineData.push({
            name: 'LCL',
            yAxis: lodashFloor(entiretyLcl, 4).toString(),
            label: formatLabel,
            precision: 4,
            lineStyle: {
              color: '#f71a1b',
              width: 1.2,
            },
          });
        }
      }
    }
    if (showSL) {
      if (isNumber(entiretySl)) {
        markLineData.push({
          name: 'SL',
          yAxis: lodashFloor(entiretySl, 4).toString(),
          label: formatLabel,
          precision: 4,
          lineStyle: {
            color: '#61ff58',
            width: 1.2,
          },
        });
      }
      if (isNumber(entiretyUsl)) {
        markLineData.push({
          name: 'USL',
          yAxis: lodashFloor(entiretyUsl, 4).toString(),
          label: formatLabel,
          precision: 4,
          lineStyle: {
            color: '#61ff58',
            width: 1.2,
          },
        });
      }
      if (isNumber(entiretyLsl)) {
        markLineData.push({
          name: 'LSL',
          yAxis: lodashFloor(entiretyLsl, 4).toString(),
          label: formatLabel,
          precision: 4,
          lineStyle: {
            color: '#61ff58',
            width: 1.2,
          },
        });
      }
    }
    if (showCLSIGMA) {
      const clSetp = (entiretyUcl - entiretyCl) / 3;
      if (isNumber(entiretyCl)) {
        markLineData.push({
          name: 'CL+2*σ',
          yAxis: lodashFloor(entiretyCl + clSetp * 2, 4).toString(),
          label: formatLabel,
          precision: 4,
          lineStyle: {
            color: '#fba530',
            width: 1.2,
          },
        });
      }
      if (isNumber(entiretyCl)) {
        markLineData.push({
          name: 'CL+σ',
          yAxis: lodashFloor(entiretyCl + clSetp, 4).toString(),
          label: formatLabel,
          precision: 4,
          lineStyle: {
            color: '#fba530',
            width: 1.2,
          },
        });
      }
      if (isNumber(entiretyCl)) {
        markLineData.push({
          name: 'CL-σ',
          yAxis: lodashFloor(entiretyCl - clSetp, 4).toString(),
          label: formatLabel,
          precision: 4,
          lineStyle: {
            color: '#fba530',
            width: 1.2,
          },
        });
      }
      if (isNumber(entiretyCl)) {
        markLineData.push({
          name: 'CL-2*σ',
          yAxis: lodashFloor(entiretyCl - clSetp * 2, 4).toString(),
          label: formatLabel,
          precision: 4,
          lineStyle: {
            color: '#fba530',
            width: 1.2,
          },
        });
      }
    }
    return markLineData;
  };

  const markAreaFormat = (chartConfig, toolsConfig) => {
    const { showCLSIGMA } = toolsConfig;
    const { entiretyCl, entiretyUcl } = chartConfig;
    const markAreaData = [];
    if (showCLSIGMA) {
      const clSetp = (entiretyUcl - entiretyCl) / 3;
      if (isNumber(entiretyCl)) {
        markAreaData.push([
          {
            name: 'A',
            yAxis: lodashFloor(entiretyCl + clSetp * 3, 4),
          },
          {
            yAxis: lodashFloor(entiretyCl + clSetp * 2, 4),
          },
        ]);
        markAreaData.push([
          {
            name: 'B',
            yAxis: lodashFloor(entiretyCl + clSetp * 2, 4),
          },
          {
            yAxis: lodashFloor(entiretyCl + clSetp, 4),
          },
        ]);
        markAreaData.push([
          {
            name: 'C',
            yAxis: lodashFloor(entiretyCl + clSetp, 4),
          },
          {
            yAxis: lodashFloor(entiretyCl, 4),
          },
        ]);
        markAreaData.push([
          {
            name: 'C',
            yAxis: lodashFloor(entiretyCl, 4),
          },
          {
            yAxis: lodashFloor(entiretyCl - clSetp, 4),
          },
        ]);
        markAreaData.push([
          {
            name: 'B',
            yAxis: lodashFloor(entiretyCl - clSetp, 4),
          },
          {
            yAxis: lodashFloor(entiretyCl - clSetp * 2, 4),
          },
        ]);
        markAreaData.push([
          {
            name: 'A',
            yAxis: lodashFloor(entiretyCl - clSetp * 2, 4),
          },
          {
            yAxis: lodashFloor(entiretyCl - clSetp * 3, 4),
          },
        ]);
      }
    }
    return markAreaData;
  };

  const chartShowTip = dataFlag => {
    const dataIndex = dataId.indexOf(dataFlag);
    if (isNumber(dataIndex) && dataIndex > -1) {
      chart[chartId].dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex,
      });
    } else if (isNumber(dataFlag)) {
      chart[chartId].dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex: dataFlag,
      });
    }
  };

  const downloadImage = () => {
    if (chart) {
      const _imageInfo = chart[chartId].getDataURL();
      linkRef.current.href = _imageInfo;
      linkRef.current.click();
    }
  };

  return (
    <>
      <div
        id="historical-graphic-chart"
        ref={chartRef}
        style={{
          height: '500px',
        }}
      />
      <a
        ref={linkRef}
        download="echarts"
        style={{
          display: 'none',
        }}
      >
        download
      </a>
    </>
  );
};

export default forwardRef(TestHistoricalGraphicChart);
