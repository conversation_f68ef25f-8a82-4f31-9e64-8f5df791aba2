/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-12-14
 * @description 执行执行规则维护-详情页
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  Form,
  Select,
  Lov,
  Switch,
  TextField,
  NumberField,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import { isArray } from 'lodash';
import { Collapse, Tabs } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@/utils/config';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { detailDS, detailListDS } from '../stories/detailDs';
import '../index.module.less';

const { Panel } = Collapse;
const TabPane = Tabs.TabPane;
const { ItemGroup } = Form;

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.commonConfig.orderExecuteRule';

const ControlDetail = props => {
  const {
    history,
    match: {
      path,
      params: { id },
    },
    customizeForm,
  } = props;

  const [activeKey, setActiveKey] = useState('');

  const list0Ds = useMemo(() => new DataSet(detailListDS()), []);
  const list1Ds = useMemo(() => new DataSet(detailListDS()), []);
  list0Ds.setState('type', 'ONE_STEP');
  list1Ds.setState('type', 'TWO_STEP');
  const formDs: DataSet = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          list0: list0Ds,
          list1: list1Ds,
        },
      }),
    [],
  );

  const rulesSave = useRequest(
    {
      url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-ins-doc-func-exe-rules/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DETAIL,${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DRAWER`,
      method: 'POST',
    },
    { manual: true, needPromise: true },
  );

  const getHeader = useRequest(
    {
      url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-ins-doc-func-exe-rules/query/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.BASIC`,
      method: 'POST',
    },
    { manual: true, needPromise: true },
  );
  const getList = useRequest(
    {
      url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-instruction-func-exe-rules/instruction/exe/rules/query/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DETAIL,${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DRAWER`,
      method: 'GET',
    },
    { manual: true, needPromise: true },
  );

  const [saveLoading, setSaveLoading] = useState(false);
  const [canEdit, setCanEdit] = useState(false);

  // 初始化页面
  useEffect(() => {
    if (id === 'create') {
      setActiveKey('');
      setCanEdit(true);
      list0Ds.loadData([
        {
          inspectionNodeFlag: 'N',
          addStrategyFlag: 'N',
          toleranceFlag: 'N',
          transferFlag: 'N',
          initialFlag: 'N',
          interactiveMode: 'ITEM',
          disabledUnloadFlag: 'N',
          disabledQtyUpdateFlag: 'N',
        },
      ]);
      list1Ds.loadData([
        {
          inspectionNodeFlag: 'N',
          addStrategyFlag: 'N',
          toleranceFlag: 'N',
          transferFlag: 'N',
          initialFlag: 'N',
          interactiveMode: 'ITEM',
          onPassageLocatorDirection: 'TARGET',
          disabledQtyUpdateFlag: 'N',
          disabledUnloadFlag: 'N',
        },
      ]);
    } else {
      queryTable(id);
    }
  }, [id]);

  useEffect(() => {
    list0Ds.addEventListener('update', handleChangeListDsFields);
    list1Ds.addEventListener('update', handleChangeListDsFields);
    return () => {
      list0Ds.removeEventListener('update', handleChangeListDsFields);
      list1Ds.removeEventListener('update', handleChangeListDsFields);
    };
  }, []);

  const changeStatus = () => {
    setCanEdit(true);
  };

  const queryTable = async instructionDocFuncExeRuleId => {
    setSaveLoading(true);

    const headerRes = await getHeader.run({
      params: { instructionDocFuncExeRuleId },
    });
    const listRes = await getList.run({
      params: { instructionDocFuncExeRuleId },
    });
    let formData: any = {
      list0: [],
      list1: [],
    };

    if (headerRes?.success) {
      if (headerRes?.rows?.content) {
        formData = { ...formData, ...(headerRes.rows.content[0] || {}) };
        if (formData.instructionDocType === 'NO_INSTRUCTION_DOC') {
          formData.instructionDocExeStrategy = 'No_Exe_Strategy';
        }
      }
    }

    if (
      !formData.instructionDocExeStrategy ||
      formData.instructionDocExeStrategy === 'No_Exe_Strategy'
    ) {
      setActiveKey('');
    } else if (
      formData.instructionDocExeStrategy === 'ONE_STEP' ||
      (formData.instructionDocExeStrategy === 'TWO_STEP' && activeKey === '')
    ) {
      setActiveKey('step1');
    }

    if (listRes?.success) {
      if (listRes?.rows?.content) {
        const list = listRes.rows.content;
        formData.list0 = [
          list[0] || {
            inspectionNodeFlag: 'N',
            addStrategyFlag: 'N',
            toleranceFlag: 'N',
            transferFlag: 'N',
            initialFlag: 'N',
            interactiveMode: 'ITEM',
            disabledQtyUpdateFlag: 'N',
            disabledUnloadFlag: 'N',
          },
        ];
        formData.list1 = [
          list[1] || {
            inspectionNodeFlag: 'N',
            addStrategyFlag: 'N',
            toleranceFlag: 'N',
            transferFlag: 'N',
            initialFlag: 'N',
            interactiveMode: 'ITEM',
            onPassageLocatorDirection: 'TARGET',
            disabledQtyUpdateFlag: 'N',
            disabledUnloadFlag: 'N',
          },
        ];
      }
    }

    formDs.loadData([formData]);
    setSaveLoading(false);
  };

  const handleSaveList = async () => {
    const validateResult = await formDs.validate(false, true);
    const instructionDocExeStrategy = formDs.current.get('instructionDocExeStrategy');
    const firstStepValidateResult = await formDs.children.list0.validate();

    let flag;
    if (instructionDocExeStrategy === "ONE_STEP") {
      flag = validateResult && firstStepValidateResult
    }
    if (instructionDocExeStrategy === "TWO_STEP") {
      const secondStepValidateResult = await formDs.children.list1.validate();
      flag = validateResult && firstStepValidateResult && secondStepValidateResult;
    }
    if (!flag) {
      return;
    }

    const data: any = formDs.toData()[0];

    const { list0, list1, ...headerData } = data;
    list0[0].sequence = 10;
    list1[0].sequence = 20;
    list0[0].stocktakeFlagList = isArray(list0[0].stocktakeFlagList) ? list0[0].stocktakeFlagList : [list0[0].stocktakeFlagList];
    list0[0].freezeFlagCheckList = isArray(list0[0].freezeFlagCheckList) ? list0[0].freezeFlagCheckList : [list0[0].freezeFlagCheckList];
    list1[0].stocktakeFlagList = isArray(list1[0].stocktakeFlagList) ? list1[0].stocktakeFlagList : [list1[0].stocktakeFlagList];
    list1[0].freezeFlagCheckList = isArray(list1[0].freezeFlagCheckList) ? list1[0].freezeFlagCheckList : [list1[0].freezeFlagCheckList];

    if (headerData.instructionDocExeStrategy === 'ONE_STEP') {
      headerData.instructionExeFuncRuleList = [list0[0] || {}];
    } else if (headerData.instructionDocExeStrategy === 'TWO_STEP') {
      headerData.instructionExeFuncRuleList = [list0[0] || {}, list1[0] || {}];
    } else {
      headerData.instructionExeFuncRuleList = [];
    }
    const res = await rulesSave.run({
      params: {
        ...headerData,
      },
    });
    if (res.success) {
      notification.success({});

      setCanEdit(false);
      if (id === 'create') {
        history.push(
          `/hmes/commonConfig/order-execute-rule-new/detail/${res.rows.instructionDocFuncExeRuleId}`,
        );
      } else {
        queryTable(res.rows.instructionDocFuncExeRuleId);
      }
    }
  };

  const onCancel = async () => {
    if (id === 'create') {
      history.push(`/hmes/commonConfig/order-execute-rule-new/list`);
    } else {
      queryTable(id);
      setCanEdit(false);
    }
  };

  const changeInstruction = (value, oldValue) => {
    if (!oldValue || oldValue === 'No_Exe_Strategy' || oldValue==='TWO_STEP') {
      if (value === 'No_Exe_Strategy') {
        setActiveKey('');
      } else {
        setActiveKey('step1');
      }
    }
    if (value === 'ONE_STEP') {
      list0Ds.current?.init('onPassageLocatorDirection', null);
    }
    formDs.current?.init('instructionCreateMode', null);
  };

  const changeLov = () => {
    const instructionDocType = formDs.current?.get('instructionDocType');
    if (instructionDocType === 'NO_INSTRUCTION_DOC') {
      formDs.current?.init('instructionDocExeStrategy', 'No_Exe_Strategy');
    } else {
      formDs.current?.init('instructionDocExeStrategy', null);
    }
  };

  const handleTabChange = tab => {
    setActiveKey(tab);
  };

  const materialLotScanOperationListSelectFilter = (record, dataSet) => {
    const moveType = dataSet.current.get('moveType');
    const materialLotEnableFlagList = dataSet.current.get('materialLotEnableFlagList');
    let optionList = [];
    if(materialLotEnableFlagList.includes('Y') && moveType === 'TRANSFER') {
      optionList = ["SPLIT"];
    }
    if(materialLotEnableFlagList.includes('Y') && moveType === 'SENT') {
      optionList = ["SPLIT", "MODIFY_QTY", "ERROR"];
    }
    if(materialLotEnableFlagList.includes('N') && moveType === 'RECEIVE') {
      optionList = ["MODIFY_QTY", "CREATE"];
    }

    return optionList.includes(record.get('value'));
  };

  const handleChangeListDsFields = ({ name, dataSet }) => {
    if(name === "moveType" || name === "materialLotEnableFlagList") {
      dataSet.current.set("materialLotScanOperationList", []);
    }
  }

  const TabsRender = observer(({ ds }) => {
    return (
      <Tabs animated={false} activeKey={activeKey} onChange={handleTabChange}>
        {['ONE_STEP', 'TWO_STEP'].includes(ds.current.get('instructionDocExeStrategy')) && (
          <TabPane key="step1" tab={intl.get(`${modelPrompt}.step1`).d('第一步')} dataSet={list0Ds}>
            <Collapse bordered={false} defaultActiveKey={[`panel1Tabstep1`, `panel2Tabstep1`]}>
              <Panel
                header={intl.get(`${modelPrompt}.panel.order`).d('指令相关')}
                key="panel1Tabstep1"
                dataSet={list0Ds}
              >
                {customizeForm(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DETAIL`,
                  },
                  <Form dataSet={list0Ds} columns={3} disabled={!canEdit} labelWidth={112}>
                    <Select
                      name="instructionObj"
                    />
                    <Lov name="businessObj" />
                    <Select name="instructionExeStrategy" />

                    <Lov
                      name="operableFunctionInsListObj"
                      // maxTagCount={3}
                      // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                    />
                    <Select name="interactiveMode" />
                    <Switch name="inspectionNodeFlag" />

                    <Switch name="addStrategyFlag" />
                    <Select name="addStrategyCheck" />
                    <Select name="instructionDetailCheck" />

                    <Select name="onPassageLocatorDirection" />
                    <Select
                      name="onPassageLocatorType"
                      // maxTagCount={3}
                      // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                    />
                    <Switch name="toleranceFlag" />

                    <Select name="toleranceType" />
                    <NumberField name="toleranceMaxValue" precision={0} />
                    <NumberField name="toleranceMinValue" precision={0} />

                    <Switch name="transferFlag" />
                    <Switch name="initialFlag" />
                  </Form>,
                )}
              </Panel>
              <Panel
                header={intl.get(`${modelPrompt}.panel.barcode`).d('条码相关')}
                key="panel2Tabstep1"
                dataSet={formDs}
              >
                <Form dataSet={list0Ds} columns={3} disabled={!canEdit} labelWidth={112}>
                  <ItemGroup
                    label={intl.get(`${modelPrompt}.materialLotEnableFlagList`).d('有效性')}
                    required
                    useColon
                    compact
                  >
                    <Select
                      name="materialLotEnableFlagList"
                      style={{ width: '100%' }}
                      // maxTagCount={3}
                      // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                    />
                    <Select
                      name="materialLotScanOperationList"
                      style={{ width: '100%' }}
                      optionsFilter={(record) => materialLotScanOperationListSelectFilter(record, list0Ds)}
                    />
                  </ItemGroup>
                  <Select
                    name="qualityStatusCheckList"
                    // maxTagCount={3}
                    // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                  />
                  <Select name="revisionCheck" />

                  <Select
                    name="freezeFlagCheckList"
                    // maxTagCount={3}
                    // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                  />
                  <Select
                    name="stocktakeFlagList"
                    // maxTagCount={3}
                    // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                  />
                  <Select
                    name="materialLotStatusCheckList"
                    // maxTagCount={3}
                    // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                  />

                  <Select name="executedMaterialLotStatus" />
                  <Select name="disabledUnloadFlag" />
                  <Select name="disabledQtyUpdateFlag" />
                </Form>
              </Panel>
            </Collapse>
          </TabPane>
        )}
        {['TWO_STEP'].includes(ds.current.get('instructionDocExeStrategy')) && (
          <TabPane key="step2" tab={intl.get(`${modelPrompt}.step2`).d('第二步')} dataSet={list1Ds}>
            <Collapse bordered={false} defaultActiveKey={[`panel1Tabstep2`, `panel2Tabstep2`]}>
              <Panel
                header={intl.get(`${modelPrompt}.panel.order`).d('指令相关')}
                key="panel1Tabstep2"
                dataSet={list1Ds}
              >
                {customizeForm(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DETAIL`,
                  },
                  <Form dataSet={list1Ds} columns={3} disabled={!canEdit} labelWidth={112}>
                    <Select
                      name="instructionObj"
                    />
                    <Lov name="businessObj" />
                    <Select name="instructionExeStrategy" />

                    <Lov
                      name="operableFunctionInsListObj"
                      // maxTagCount={3}
                      // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                    />
                    <Select name="interactiveMode" />
                    <Switch name="inspectionNodeFlag" />

                    <Switch name="addStrategyFlag" />
                    <Select name="addStrategyCheck" />
                    <Select name="instructionDetailCheck" />

                    <Select name="onPassageLocatorDirection" />
                    <Select
                      name="onPassageLocatorType"
                      // maxTagCount={3}
                      // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                    />
                    <Switch name="toleranceFlag" />

                    <Select name="toleranceType" />
                    <NumberField name="toleranceMaxValue" precision={0} />
                    <NumberField name="toleranceMinValue" precision={0} />
                    <Switch name="transferFlag" />
                    <Switch name="initialFlag" />
                  </Form>,
                )}
              </Panel>
              <Panel
                header={intl.get(`${modelPrompt}.panel.barcode`).d('条码相关')}
                key="panel2Tabstep2"
                dataSet={list1Ds}
              >
                <Form dataSet={list1Ds} columns={3} disabled={!canEdit} labelWidth={112}>
                  <Select
                    name="materialLotEnableFlagList"
                    style={{ width: '100%' }}
                    // maxTagCount={3}
                    // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                  />
                  <Select
                    name="qualityStatusCheckList"
                    // maxTagCount={3}
                    // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                  />
                  <Select name="revisionCheck" />

                  <Select
                    name="freezeFlagCheckList"
                    // maxTagCount={3}
                    // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                  />
                  <Select
                    name="stocktakeFlagList"
                    // maxTagCount={3}
                    // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                  />
                  <Select
                    name="materialLotStatusCheckList"
                    // maxTagCount={3}
                    // maxTagPlaceholder={restValues => `+${restValues.length}...`}
                  />
                  <Select name="executedMaterialLotStatus" />
                  <Select name="disabledUnloadFlag" />
                  <Select name="disabledQtyUpdateFlag" />
                </Form>
              </Panel>
            </Collapse>
          </TabPane>
        )}
      </Tabs>
    );
  });

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.order-execute-rule-new`).d('指令执行规则维护')}
        backPath="/hmes/commonConfig/order-execute-rule-new/list"
      >
        {canEdit ? (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSaveList}
              loading={saveLoading}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button color={ButtonColor.default} icon="close" onClick={onCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={changeStatus}
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['panel1', 'panel2']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.billsRule`).d('指令单据规则')}
            key="panel1"
            dataSet={formDs}
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.BASIC`,
              },
              <Form dataSet={formDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <Lov name="businessObj" onChange={changeLov} />
                <TextField name="description" />
                <Lov name="statusGroupObj" />

                <Select name="instructionDocExeStrategy" onChange={changeInstruction} />
                <Select name="instructionCreateMode" />
                <Select name="permissionConfig" />

                <Lov name="operableFunctionDocObj" />
                <Switch name="fromLocatorRequiredFlag" />
                <Switch name="toLocatorRequiredFlag" />
                <Switch name="docInitialFlag" />
                <Select name="accountCategory" />
              </Form>,
            )}
          </Panel>
          <Panel header={intl.get(`${modelPrompt}.title.orderRule`).d('指令规则')} key="panel2">
            <TabsRender ds={formDs} />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.commonConfig.orderExecuteRule', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.BASIC`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DRAWER`,
    ],
    // @ts-ignore
  })(ControlDetail),
);
