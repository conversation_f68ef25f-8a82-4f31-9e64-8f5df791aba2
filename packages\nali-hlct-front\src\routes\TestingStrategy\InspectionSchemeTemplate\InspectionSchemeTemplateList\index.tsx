/**
 * @Description: 检验方案模板-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-02-15 10:49:38
 * @LastEditTime: 2023-05-18 16:44:43
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo } from 'react';
import { DataSet, Table, Modal, Form, Lov } from 'choerodon-ui/pro';
import { Badge, Tag, Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { useDataSetEvent } from 'utils/hooks';
import intl from 'utils/intl';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { drawerPropsC7n } from '@components/tarzan-ui';
import {
  inspectSchemeTemplateDS,
  inspectBusTypeDS,
  inspectItemDS,
  dimensionTableDS,
} from '../stores/HistoryDS';
import { listTableDS, copyDS } from '../stores/InspectionSchemeTemplateDS';

const modelPrompt = 'tarzan.inspectionSchemeTemplate';
const { Panel } = Collapse;

const InspectionSchemeTemplateList = props => {
  const {
    tableDs,
    copyDs,
    match: { path },
    customizeTable,
  } = props;
  const inspectItemDs = useMemo(() => new DataSet(inspectItemDS()), []);
  const dimensionTableDs = useMemo(
    () =>
      new DataSet({
        ...dimensionTableDS(),
        children: { items: inspectItemDs },
      }),
    [],
  );
  const inspectBusTypeDs = useMemo(
    () =>
      new DataSet({
        ...inspectBusTypeDS(),
        children: { dimensions: dimensionTableDs },
      }),
    [],
  );
  const inspectSchemeTemplateDs = useMemo(
    () =>
      new DataSet({
        ...inspectSchemeTemplateDS(),
        children: { inspectSchemeLines: inspectBusTypeDs },
      }),
    [],
  );

  useEffect(() => {
    tableDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP.TABLE`,
    );
    if (tableDs?.currentPage) {
      tableDs.query(props.tableDs.currentPage);
    } else {
      tableDs.query();
    }
  }, []);

  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteObject':
        record.set('inspectBusinessTypeObject', null);
        break;
      default:
        break;
    }
  });

  const inspectSchemeTemplateColumns: ColumnProps[] = [
    {
      name: 'lastUpdateDate',
      width: 150,
      align: ColumnAlign.center,
      lock: ColumnLock.left,
    },
    {
      name: 'lastUpdatedByName',
      lock: ColumnLock.left,
      width: 150,
    },
    { name: 'operationTypeDesc' },
    {
      name: 'inspectSchemeTmpCode',
      lock: ColumnLock.left,
      width: 150,
    },
    {
      name: 'siteName',
      minWidth: 160,
    },
    {
      name: 'inspectSchemeObjectTypeDesc',
      minWidth: 160,
    },
    {
      name: 'enableFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    { name: 'remark' },
    { name: 'creationDate', width: 150, align: ColumnAlign.center },
    { name: 'createdByName' },
    { name: 'enclosure', lock: ColumnLock.right },
  ];

  const inspectBusTypeColumns: ColumnProps[] = [
    {
      name: 'inspectBusinessTypeDesc',
    },
    {
      name: 'samplingDimension',
    },
    {
      name: 'samplingMethodDesc',
    },
  ];

  const dimensionTableColumns: ColumnProps[] = [
    {
      name: 'sequence',
      align: ColumnAlign.left,
    },
    { name: 'areaName' },
    { name: 'prodLineName' },
    { name: 'processWorkcellName' },
    { name: 'stationWorkcellName' },
    { name: 'equipmentName' },
    { name: 'operationName' },
    { name: 'supplierName' },
    { name: 'customerName' },
    { name: 'otherObject' },
  ];

  const getDataValueShow = (record, name) => {
    const _dataType = record?.get('dataType');
    const _valueData = record?.get(name) || [];
    const _dataShow = _valueData.length > 0 ? _valueData[0].dataValue : '';
    return ['CALCULATE_FORMULA', 'VALUE', 'VALUE_LIST'].includes(_dataType)
      ? _valueData.map(item => <Tag>{item.dataValue}</Tag>)
      : _dataShow;
  };

  const inspectItemColumns: ColumnProps[] = [
    { name: 'inspectItemCode', width: 150, lock: ColumnLock.left },
    {
      name: 'inspectItemDesc',
      width: 180,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectBasis',
    },
    {
      name: 'qualityCharacteristic',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectTool',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'dataQtyDisposition',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectMethod',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'requiredFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'enableFlag',
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'technicalRequirement',
    },
    {
      name: 'remark',
    },
    {
      name: 'enterMethod',
      align: ColumnAlign.center,
    },
    {
      name: 'dataType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'uomName',
      align: ColumnAlign.center,
    },
    {
      name: 'decimalNumber',
    },
    {
      name: 'processMode',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'processModeDesc',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'valueLists',
      width: 200,
      renderer: ({ value }) => {
        if (value) {
          return (
            value?.length &&
            value.map(item => {
              return (
                <Tag className="hcm-tag-blue" key={item}>
                  {item}
                </Tag>
              );
            })
          );
        }
      },
    },

    {
      name: 'trueValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'trueValueList'),
    },
    {
      name: 'falseValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'falseValueList'),
    },
    {
      name: 'warningValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'warningValueList'),
    },
    {
      name: 'dataQty',
    },
    {
      name: 'samplingMethodDesc',
      align: ColumnAlign.center,
    },
    {
      name: 'ncCodeGroupDesc',
      align: ColumnAlign.center,
    },
    {
      name: 'employeePosition',
      width: 120,
    },
    {
      name: 'inspectFrequency',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
        if (inspectFrequencyShow) {
          inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
          inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
          return inspectFrequencyShow;
        }
        return value;
      },
    },
    {
      name: 'actionItem',
    },
    {
      name: 'sameGroupIdentification',
    },
    {
      name: 'destructiveExperimentFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'outsourceFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'enclosure',
      lock: ColumnLock.right,
    },
  ];

  const handleOpenHistoryDrawer = record => {
    inspectSchemeTemplateDs.loadData([]);
    inspectSchemeTemplateDs.setQueryParameter(
      'inspectSchemeTmpId',
      record?.get('inspectSchemeTmpId'),
    );
    inspectSchemeTemplateDs.query();

    Modal.open({
      ...drawerPropsC7n({
        ds: inspectSchemeTemplateDs,
        canEdit: false,
      }),
      title: intl.get(`${modelPrompt}.title.queryHistory`).d('历史查询'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      afterClose: () => inspectSchemeTemplateDs.loadData([]),
      children: (
        <>
          <Table
            highLightRow
            dataSet={inspectSchemeTemplateDs}
            columns={inspectSchemeTemplateColumns}
            customizedCode="inspectSchemeTemp-drawer"
          />
          <Collapse
            bordered={false}
            defaultActiveKey={['inspectBusList', 'dimension', 'inspectItem']}
          >
            <Panel
              key="inspectBusList"
              header={intl.get(`${modelPrompt}.title.inspectBusList`).d('检验业务类型列表')}
            >
              <Table
                highLightRow
                dataSet={inspectBusTypeDs}
                columns={inspectBusTypeColumns}
                customizedCode="inspectSchemeTemp-inspectBusList"
              />
            </Panel>
            <Panel
              key="dimension"
              header={intl.get(`${modelPrompt}.title.dimension`).d('检验维度列表')}
            >
              <Table
                highLightRow
                dataSet={dimensionTableDs}
                columns={dimensionTableColumns}
                customizedCode="inspectSchemeTemp-dimension"
              />
            </Panel>
            <Panel
              key="inspectItem"
              header={intl.get(`${modelPrompt}.title.inspectItem`).d('检验项目列表')}
            >
              <Table
                dataSet={inspectItemDs}
                columns={inspectItemColumns}
                customizedCode="inspectSchemeTemp-inspectItem"
              />
            </Panel>
          </Collapse>
        </>
      ),
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'inspectSchemeTmpCode',
      lock: ColumnLock.left,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
      minWidth: 160,
    },
    {
      name: 'siteName',
      minWidth: 160,
    },
    {
      name: 'inspectSchemeObjectTypeDesc',
      minWidth: 160,
    },
    {
      name: 'inspectBusinessTypeDesc',
      minWidth: 220,
      renderer: ({ value }) => (value || []).map(item => <Tag color="blue">{item}</Tag>),
    },
    {
      name: 'enableFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'remark',
      minWidth: 180,
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      width: 100,
      renderer: ({ record }) => (
        <a onClick={() => handleOpenHistoryDrawer(record)}>
          {intl.get(`${modelPrompt}.operation.history`).d('历史查询')}
        </a>
      ),
    },
  ];

  const handleEdit = record => {
    props.history.push(
      `/hwms/inspection-scheme-template-maintenance/detail/${record.get('inspectSchemeTmpId')}`,
    );
  };

  const handleCreate = () => {
    props.history.push(`/hwms/inspection-scheme-template-maintenance/detail/create`);
  };

  const handleCopy = () => {
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.copyInspection`).d('复制检验方案'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: copyConfirm,
      onClose: copyReset,
      children: (
        <Form dataSet={copyDs} columns={1}>
          <Lov name="inspectSchemeTmpObject" />
        </Form>
      ),
    });
  };

  const copyReset = () => {
    copyDs.reset();
  };

  const copyConfirm = async () => {
    const formValidate = await copyDs.validate();
    if (formValidate) {
      props.history.push(
        `/hwms/inspection-scheme-template-maintenance/detail/${copyDs.current.get(
          'inspectSchemeTmpId',
        )}copy`,
      );
      return Promise.resolve(true);
    }
    return Promise.resolve(false);
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.InspectionSchemeMaintenance`).d('检验方案模板维护')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleCreate()}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          icon="content_copy"
          onClick={handleCopy}
        >
          {intl.get(`tarzan.common.button.copy`).d('复制')}
        </PermissionButton>
        {/* <PermissionButton
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
          permissionList={[
            {
              code: `tarzan${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </PermissionButton> */}
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP.TABLE`,
          },
          <Table
            searchCode="jyfambwh1"
            customizedCode="jyfambwh1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            filter={record => {
              return record.get('dataType') !== 'CALCULATE_FORMULA';
            }}
            dataSet={tableDs}
            columns={columns}
          />,
        )}
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: [modelPrompt, 'tarzan.common', 'tarzan.qms.inspectGroupMaintenance'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...listTableDS(),
      });
      const copyDs = new DataSet({
        ...copyDS(),
      });
      return {
        tableDs,
        copyDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP.TABLE`,
      ],
    })(InspectionSchemeTemplateList as any),
  ),
);
