/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-06-28 15:10:32
 * @LastEditTime: 2023-06-30 17:51:00
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSet } from 'choerodon-ui/pro';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import uuid from 'uuid/v4';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.purchase.purchaseReturn';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
      disabled: true,
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lookupCode: 'APEX_WMS.PO_RETURN_TYPE',
      lovPara: {
        tenantId,
        tag: 'RETURN',
      },
      required: true,
      defaultValue: 'PO_RETURN',
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_RETURN`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      defaultValue: 'RELEASED',
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'APEX_WMS.MODEL.SITE',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierLov`).d('供应商'),
      lovCode: 'APEX_WMS.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierId',
      type: FieldType.number,
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'supplierSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierSiteLov`).d('供应商地点'),
      lovCode: 'APEX_WMS.MODEL.SUPPLIER_SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => {
          return !record.get('supplierId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            supplierId: record.get('supplierId'),
          };
        },
      },
    },
    {
      name: 'supplierSiteId',
      type: FieldType.number,
      bind: 'supplierSiteLov.supplierSiteId',
    },
    {
      name: 'supplierSiteCode',
      type: FieldType.string,
      bind: 'supplierSiteLov.supplierSiteCode',
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      bind: 'supplierSiteLov.supplierSiteName',
    },
    {
      name: 'poNumberLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.poNumberLov`).d('采购订单号'),
      lovCode: 'APEX_WMS.PO',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'poNumber',
      bind: 'poNumberLov.poNumber',
    },
    {
      name: 'poHeaderId',
      bind: 'poNumberLov.poHeaderId',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      lookupCode: 'SOURCE_SYSTEM',
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
    {
      name: 'supplyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialReturnMethod`).d('退料方式'),
      lookupCode: 'WMS.RETURN_METHOD',
      computedProps: {
        required: ({ record }) => {
          return record.get('instructionDocType') === "PO_RETURN";
        },
      },
    },
  ],

  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-instruction-doc/purchase-return/detail/ui`,
        method: 'GET',
      };
    },
  },
});

const lineTableDS: (detailDs) => DataSetProps = (detailDs) => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      disabled: true,
    },
    {
      name: 'identifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
      lookupCode: 'APEX_WMS.APS.GEN_TYPE_URL',
      lovPara: {
        typeGroup: 'IDENTITY_TYPE',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      valueField: 'typecode',
      textField: 'description',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
      lovCode: 'APEX_WMS.METHOD.MATERIAL',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      required: true,
      ignore: FieldIgnore.always,
      computedProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: dataSet.getState('siteId') || '',
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'materialLov.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      bind: 'materialLov.currentRevisionCode',
      textField: 'description',
      valueField: 'description',
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: uuid(),
                  description: item,
                };
              });
            }
            if (record) {
              if (firstlyQueryData.length > 0) {
                if (!record?.get('revisionCode')) {
                  // eslint-disable-next-line no-unused-expressions
                  record?.init('revisionCode', firstlyQueryData[0].description);
                }
              } else {
                // eslint-disable-next-line no-unused-expressions
                record?.init('revisionCode', null);
              }
            }
            return firstlyQueryData;
          },
        };
      },
      computedProps: {
        disabled: ({ record }) => {
          return !record?.get('materialId');
        },
        required({ record }) {
          return record?.get('revisionFlag') === 'Y' && record?.get('materialId');
        },
        lovPara: ({ record, dataSet }) => {
          return {
            tenantId,
            siteIds: dataSet.getState('siteId') || undefined,
            materialId: record?.get('materialId') || undefined,
            kid: record?.get('kid') || undefined,
          };
        },
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
      bind: 'materialLov.decimalNumber',
    },
    {
      name: 'materialBomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBomCode`).d('BOM号'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
    },
    {
      name: 'materialBrand',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBrand`).d('品牌'),
    },
    {
      name: 'deliveryDocNum',
      label: intl.get(`${modelPrompt}.deliveryDocNum`).d('送货单号'),
      bind: 'deliveryDocLineNumLov.deliveryDocNum',
    },
    {
      name: 'deliveryDocLineNumLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.deliveryDocLineNumLov`).d('送货单行号'),
      lovCode: 'APEX_WMS.DELIVERY_DOC_LINE',
      required: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            poId: detailDs?.current?.get('poHeaderId'),
          };
        },
      },
    },
    {
      name: 'deliveryDocLineId',
      bind: 'deliveryDocLineNumLov.instructionDocLineId',
    },
    {
      name: 'deliveryDocLineNum',
      bind: 'deliveryDocLineNumLov.deliveryDocLineNum',
    },
    {
      name: 'needReturnQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.needReturnQty`).d('需退货数量'),
      required: true,
      computedProps: {
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
      },
    },
    {
      name: 'actualReturnQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.actualReturnQty`).d('实际退货数量'),
    },
    // {
    //   name: 'uomLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.uomLov`).d('单位'),
    //   lovCode: 'APEX_WMS.COMMON.UOM',
    //   lovPara: { tenantId },
    //   required: true,
    //   ignore: FieldIgnore.always,
    // },
    {
      name: 'uomId',
      type: FieldType.number,
      bind: 'materialLov.uomId',
    },
    {
      name: 'uomCode',
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      type: FieldType.string,
      bind: 'materialLov.uomCode',
    },
    {
      name: 'fromWarehouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromWarehouseLov`).d('退货来源仓库'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      computedProps: {
        required: ({ dataSet }) => {
          return (dataSet.getState('instructionRule') || {}).fromLocatorRequiredFlag === 'Y';
        },
        lovPara: ({ dataSet }) => {
          const { businessTypes } = dataSet.getState('instructionRule') || {};
          return {
            tenantId,
            businessTypes,
            enableFlag: 'Y',
            locatorCategoryAreaFlag: 'Y',
            queryType: 'SOURCE',
            siteId: dataSet.getState('siteId') || '',
          };
        },
      },
    },
    {
      name: 'fromWarehouseId',
      type: FieldType.number,
      bind: 'fromWarehouseLov.locatorId',
    },
    {
      name: 'fromWarehouseCode',
      type: FieldType.string,
      bind: 'fromWarehouseLov.locatorCode',
    },
    {
      name: 'fromLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromLocatorLov`).d('退货来源库位'),
      lovCode: 'APEX_WMS.MODEL.SUB_LOCATOR',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => {
          return !record.get('fromWarehouseId');
        },
        lovPara: ({ record }) => {
          return {
            locatorCategory: ['LOCATION', 'INVENTORY'].join(','),
            locatorIds: record.get('fromWarehouseId'),
          };
        },
      },
    },
    {
      name: 'fromLocatorId',
      type: FieldType.number,
      bind: 'fromLocatorLov.locatorId',
    },
    {
      name: 'fromLocatorCode',
      type: FieldType.string,
      bind: 'fromLocatorLov.locatorCode',
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
      lookupCode: 'APEX_WMS.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      computedProps: {
        defaultValue: ({ dataSet }) => {
          return (dataSet.getState('instructionRule') || {}).toleranceFlag;
        },
      },
    },
    {
      name: 'toleranceType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
      options: toleranceTypeOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      computedProps: {
        defaultValue: ({ dataSet }) => {
          return (dataSet.getState('instructionRule') || {}).toleranceType;
        },
        disabled: ({ record }) => {
          return record?.get('toleranceFlag') !== 'Y';
        },
        required: ({ record }) => {
          return record?.get('toleranceFlag') === 'Y';
        },
      },
    },
    {
      name: 'toleranceTypeDesc',
      type: FieldType.string,
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差值'),
      min: 0,
      computedProps: {
        disabled: ({ record }) => {
          return (
            !record?.get('toleranceType') || record?.get('toleranceType') === 'OVER_MATERIAL_LOT'
          );
        },
        required: ({ record }) => {
          return (
            record?.get('toleranceType') &&
            ['NUMBER', 'PERCENTAGE'].includes(record?.get('toleranceType'))
          );
        },
        defaultValue: ({ dataSet }) => {
          return (dataSet.getState('instructionRule') || {}).toleranceMaxValue;
        },
      },
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差值'),
      min: 0,
      computedProps: {
        disabled: ({ record }) => {
          return (
            !record?.get('toleranceType') || record?.get('toleranceType') === 'OVER_MATERIAL_LOT'
          );
        },
        required: ({ record }) => {
          return (
            record?.get('toleranceType') &&
            ['NUMBER', 'PERCENTAGE'].includes(record?.get('toleranceType'))
          );
        },
        defaultValue: ({ dataSet }) => {
          return (dataSet.getState('instructionRule') || {}).toleranceMinValue;
        },
      },
    },
  ],
});

// 允差类型下拉框数据源
const toleranceTypeOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/limit-group/type`,
        method: 'POST',
        data: { typeGroup: 'INSTRUCTION_TOLERANCE_TYPE', module: 'GENERAL', tenantId },
      };
    },
  },
});

export { detailDS, lineTableDS };
