import intl from 'utils/intl';
// import { BASIC } from '@/utils/config';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentUser, getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';

// const Host = `${BASIC.HMES_BASIC}`
// const Host = '/yp-mes-20000'

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.event.badRecordPlatform';

const ncRecordDS = () => ({
  name: 'ncRecordDS',
  primaryKey: 'ncRecordId',
  paging: false,
  autoQuery: false,
  selection: 'multiple',
  fields: [
    {
      name: 'ncRecordId',
      type: 'number',
    },
    {
      name: 'identification',
      type: 'string',
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
    },
    {
      name: 'materialLotCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'ncRecordStatusDesc',
      type: 'string',
      label: intl.get(`${modelPrompt}.ncRecordStatusDesc`).d('不良记录状态'),
      // disabled: true,
      // lovPara: { tenantId },
      // lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_RECORD_STATUS`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
      //     if (data instanceof Array) {
      //       return data;
      //     }
      //     const { rows } = JSON.parse(data);
      //     return rows;
      //   },
      // },
    },
    {
      name: 'qty',
      type: 'number',
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'uomName',
      type: 'string',
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'workOrderNum',
      type: 'string',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
    },
    {
      name: 'workOrderQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.workOrderQty`).d('生产指令数量'),
    },
    {
      name: 'eoNum',
      type: 'string',
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'locatorName',
      type: 'string',
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    },
    {
      name: 'containerCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.containerCode`).d('装载容器'),
    },
    {
      name: 'prodLineName',
      type: 'string',
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线'),
    },
    {
      name: 'routerName',
      type: 'string',
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线'),
    },
    {
      name: 'operationId',
    },
    {
      name: 'operationName',
      type: 'string',
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'equipmentCode',
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备'),
      type: 'string',
    },
    {
      type: 'workCellName',
      name: 'workCellName',
      label: intl.get(`${modelPrompt}.workCellName`).d('工位'),
    },
    {
      name: 'workNumOrderLov',
      type: 'object',
      required: true,
      lovCode: 'HME.WO_NOT_CLOSED',
      textField: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workNumOrder`).d('工单'),
      dynamicProps: {
        lovPara: ({ dataSet, record }) => {
          const ncRecordType = dataSet.getState('ncRecordType');
          if (ncRecordType === 'RM_NC') {
            return {
              tenantId,
              materialLotId: record?.get('materialLotId'),
            };
          }
          return { tenantId };
        },
      },
    },
    {
      name: 'workOrderId',
      bind: 'workNumOrderLov.workOrderId',
    },{
      name:'workOrderNum',
      bind: 'workNumOrderLov.workOrderNum',
    },
    {
      name: 'scrapReason',
      type: 'string',
      required: true,
      lookupCode: 'HME.SCRAP_REASON',
      label: intl.get(`${modelPrompt}.scrapReason`).d('报废原因'),
    },
    // {
    //   name: 'costCenterLov',
    //   type: 'object',
    //   label: intl.get(`${modelPrompt}.costCenterLov`).d('成本中心'),
    //   lovCode: 'YP_WMS.MES.COST_CENTER',
    //   ignore: 'always',
    //   textField: 'costcenterCode',
    //   lovPara: {
    //     tenantId,
    //   },
    // },
    // {
    //   name: 'costCenter',
    //   bind: 'costCenterLov.costcenterCode',
    // },
    {
      name: 'intendedDisposal',
      type: 'string',
      lookupCode: 'MT.MES.NC_DISPOSAL_METHOD',
      label: intl.get(`${modelPrompt}.intendedDisposal`).d('处置结果'),
    },
    {
      name: 'intendedDisposalName',
      type: 'string',
      label: intl.get(`${modelPrompt}.intendedDisposalName`).d('处置人'),
    },
    {
      name: 'intendedDisposalTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.intendedDisposalTime`).d('处置时间'),
    },
    {
      type: 'string',
      name: 'relatedUnitName',
      label: intl.get(`${modelPrompt}.relatedUnitName`).d('责任部门'),
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
      lovCode: 'APEX_MES.OPERATION_NC_INCIDENT',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId: getCurrentOrganizationId(),
          methodType: record.get('intendedDisposal'),
          workOrderId: record?.get('workOrderId'),
        }),
        required: ({ record }) => record?.get('intendedDisposal') === 'CONCESSION_INTERCEPTION' || record?.get('intendedDisposal') === 'REWORK',
        disabled: ({ record }) => record?.get('intendedDisposal') !== 'CONCESSION_INTERCEPTION' && record?.get('intendedDisposal') !== 'REWORK',
      },
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    // {
    //   name: 'disposalWay',
    //   type: 'string',
    //   label: intl.get(`${modelPrompt}.disposalWay`).d('处置类型'),
    // },
    {
      name: 'reviewResult',
      type: 'string',
      label: intl.get(`${modelPrompt}.reviewResult`).d('评审结果'),
    },
    {
      name: 'reviewName',
      type: 'string',
      label: intl.get(`${modelPrompt}.reviewName`).d('评审人'),
    },
    {
      name: 'reviewTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.reviewTime`).d('评审时间'),
    },
    {
      name: 'reviewRemark',
      type: 'string',
      label: intl.get(`${modelPrompt}.reviewRemark`).d('评审附加'),
    },
  ],
  events: {
    update: ({ record, name, value }) => {
      if(name === 'intendedDisposal'&&value){
        if(value){
          record?.init('intendedDisposalName',getCurrentUser().realName)
          record?.init('intendedDisposalTime', moment().format('YYYY-MM-DD HH:mm:ss'))
        }else{
          record?.init('intendedDisposalName',null)
          record?.init('intendedDisposalTime', null)
        }
      }
    },
  },
});

export { ncRecordDS };
