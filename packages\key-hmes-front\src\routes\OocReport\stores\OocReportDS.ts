/**
 * @Description: ooc统计报表-ds
 * @Author: <<EMAIL>>
 * @Date: 2022-11-28 16:46:38
 * @LastEditTime: 2022-11-30 15:30:45
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from 'hcm-components-front/lib/utils/config';

const modelPrompt = 'tarzan.hspc.OocReport';
const tenantId = getCurrentOrganizationId();
const servePath = '/tznl'

// 列表页数据DS
const tableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'processObjectList',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.controlObj`).d('过程对象'),
      lovCode: 'MT.SPC.CONTROL_PROCESS_OBJECT',
      multiple: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'objectTypeList',
      bind: 'processObjectList.objectType',
      ignore: FieldIgnore.always,
    },
    {
      name: 'objectCodeList',
      bind: 'processObjectList.objectCode',
      ignore: FieldIgnore.always,
    },
    {
      name: 'controlCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlCode`).d('控制图编码'),
    },
    {
      name: 'controlDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlDesc`).d('控制图描述'),
    },
    {
      name: 'oocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oocStatus`).d('OOC状态'),
      lookupCode: 'MT.SPC.OOC_STATUS',
    },
    {
      name: 'startDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startDate`).d('开始时间'),
      max: 'endDate',
    },
    {
      name: 'endDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endDate`).d('结束时间'),
      min: 'startDate',
    },
  ],
  fields: [
    {
      name: 'controlOocId',
    },
    {
      name: 'controlCode',
      label: intl.get(`${modelPrompt}.controlCode`).d('控制图编码'),
    },
    {
      name: 'controlDesc',
      label: intl.get(`${modelPrompt}.controlDesc`).d('控制图描述'),
    },
    {
      name: 'chartDetailTypeDesc',
      label: intl.get(`${modelPrompt}.chartDetailType`).d('控制图明细类型'),
    },
    {
      name: 'oocStatus',
      label: intl.get(`${modelPrompt}.oocStatus`).d('OOC状态'),
      // lookupUrl: `/hpfm/v1/${tenantId}/lovs/value/batch?typeGroup=MT.SPC.OOC_STATUS`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     if (Array.isArray(data)) {
      //       return data;
      //     }
      //     const { typeGroup } = JSON.parse(data);
      //     const list = typeGroup.filter(val => val.tag === 'Y');
      //     return list;
      //   },
      // },
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'judgementDesc',
      label: intl.get(`${modelPrompt}.judgementDesc`).d('判异规则'),
    },
    {
      name: 'dataValue',
      label: intl.get(`${modelPrompt}.dataValue`).d('样本值'),
    },
    {
      name: 'upperControlLimit',
      label: intl.get(`${modelPrompt}.upperControlLimit`).d('控制上限'),
    },
    {
      name: 'lowerControlLimit',
      label: intl.get(`${modelPrompt}.lowerControlLimit`).d('控制下限'),
    },
    {
      name: 'sampleTime',
      label: intl.get(`${modelPrompt}.sampleTime`).d('样本时间'),
    },
    {
      name: 'siteDesc',
      label: intl.get(`${modelPrompt}.siteDesc`).d('站点'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      label: intl.get(`${modelPrompt}.inspectBusinessTypeDesc`).d('检验业务类型'),
    },
    {
      name: 'inspectSchemeCode',
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案'),
    },
    {
      name: 'inspectItemDesc',
      label: intl.get(`${modelPrompt}.inspectItemDesc`).d('检验项目'),
    },
    {
      name: 'supplierName',
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'customerName',
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
    },
    {
      name: 'areaDesc',
      label: intl.get(`${modelPrompt}.areaDesc`).d('区域'),
    },
    {
      name: 'prodLineDesc',
      label: intl.get(`${modelPrompt}.prodLineDesc`).d('生产线'),
    },
    {
      name: 'workcellDesc',
      label: intl.get(`${modelPrompt}.workcellDesc`).d('工作单元'),
    },
    {
      name: 'materialDesc',
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料'),
    },
    {
      name: 'operationDesc',
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺'),
    },
    {
      name: 'tagDesc',
      label: intl.get(`${modelPrompt}.tagDesc`).d('数据收集项'),
    },
    {
      name: 'shiftTeamDesc',
      label: intl.get(`${modelPrompt}.shiftTeamDesc`).d('班组'),
    },
    {
      name: 'chartTypeDesc',
      label: intl.get(`${modelPrompt}.chartTypeDesc`).d('控制图类型'),
    },
    {
      name: 'abnormalGroup',
      label: intl.get(`${modelPrompt}.abnormalGroup`).d('异常组'),
    },
    {
      name: 'abnormalType',
      label: intl.get(`${modelPrompt}.abnormalType`).d('异常类型'),
    },
    {
      name: 'abnormalAnalysis',
      label: intl.get(`${modelPrompt}.abnormalAnalysis`).d('异常问题分析'),
    },
    {
      name: 'tempAction',
      label: intl.get(`${modelPrompt}.tempAction`).d('暂行措施'),
    },
    {
      name: 'permanentAction',
      label: intl.get(`${modelPrompt}.permanentAction`).d('永久措施'),
    },
    {
      name: 'remark',
      label: intl.get(`${modelPrompt}.remark`).d('处理备注'),
    },
    {
      name: 'remarkProcessBy',
      label: intl.get(`${modelPrompt}.remarkProcessBy`).d('处理人'),
    },
    {
      name: 'processDate',
      label: intl.get(`${modelPrompt}.processDate`).d('处理时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/ooc/query/ui`,
        method: 'POST',
      };
    },
    submit: ({ data }) => {
      return {
        url: `${servePath}/v1/${tenantId}/hspc-control-measure-oocs/updateMeasureOoc`,
        method: 'POST',
        data: data[0],
      }
    },
  },
});

export { tableDS };
