import React, { useMemo, ReactNode, useCallback } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Collapse, Tabs } from 'choerodon-ui';
import cls from 'classnames';
import {
  ColumnProps,
  ColumnAlign,
  TableColumnTooltip,
  TableAutoHeightType,
} from 'choerodon-ui/pro/lib/table/interface';
import { withRouter, RouteComponentProps } from 'react-router-dom';
import { myTodoDS, mySubmitDS, lastFinishDS } from './Stores';
import styles from './index.module.less';

const TaskAnalysisCard = ({ history }: RouteComponentProps) => {
  const myPendingDS = useMemo(() => {
    return new DataSet(myTodoDS());
  }, []);
  const proposeDS = useMemo(() => {
    return new DataSet(mySubmitDS());
  }, []);
  const finishDS = useMemo(() => {
    return new DataSet(lastFinishDS());
  }, []);

  const jumpToOrder = useCallback((record): void => {
    const { orderId, orderCode, baseOrderType } = record.toData();
    if (baseOrderType === 'WO') {
      history.push(`/amtc/work-order/detail/${orderId}/${orderCode}`);
    } else if (baseOrderType === 'SR') {
      history.push(`/amtc/service-apply-current/detail/${orderId}`);
    }
  }, []);

  const countingColumns = useMemo((): ColumnProps[] => {
    return [
      {
        name: 'orderCode',
        align: ColumnAlign.center,
        tooltip: TableColumnTooltip.overflow,
        renderer: ({ record, value }): ReactNode => {
          return <a onClick={() => jumpToOrder(record)}>{value}</a>;
        },
      },
      { name: 'orderName', align: ColumnAlign.center, tooltip: TableColumnTooltip.overflow },
      { name: 'orderTypeName', align: ColumnAlign.center, tooltip: TableColumnTooltip.overflow },
      { name: 'headerName', align: ColumnAlign.center, tooltip: TableColumnTooltip.overflow },
      {
        name: 'scheduledFinishDate',
        align: ColumnAlign.center,
        tooltip: TableColumnTooltip.overflow,
      },
    ];
  }, []);

  return (
    <div className={styles.container}>
      <Collapse
        bordered={false}
        expandIconPosition="right"
        defaultActiveKey={['A']}
        trigger="icon"
        className={cls(styles['customize-collapse'], styles['table-auto-height'])}
      >
        <Collapse.Panel key="A" showArrow={false} header="待办单据-维修">
          <Tabs defaultActiveKey="1" style={{ height: '100%' }}>
            <Tabs.TabPane tab="我的待办" key="1">
              <Table
                autoHeight={{ type: TableAutoHeightType.maxHeight, diff: 50 }}
                key="myPendingTable"
                dataSet={myPendingDS}
                columns={countingColumns}
              />
            </Tabs.TabPane>
            <Tabs.TabPane tab="我发起的" key="2">
              <Table
                autoHeight={{ type: TableAutoHeightType.maxHeight, diff: 50 }}
                key="proposeTable"
                dataSet={proposeDS}
                columns={countingColumns}
              />
            </Tabs.TabPane>
            <Tabs.TabPane tab="最近完成" key="3">
              <Table
                autoHeight={{ type: TableAutoHeightType.maxHeight, diff: 50 }}
                key="finishTable"
                dataSet={finishDS}
                columns={countingColumns}
              />
            </Tabs.TabPane>
          </Tabs>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default withRouter(TaskAnalysisCard);
