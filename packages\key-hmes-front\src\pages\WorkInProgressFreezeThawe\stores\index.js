/**
 * @Description: 执行作业管理列表页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2021-08-31 17:17:52
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'work.in.progress.freeze.thawe';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: 'multiple',
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-freeze-records/query`,
        method: 'POST',
        data: {
          ...data,
        },
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'recordMark',
  queryFields: [
    {
      name: 'identifications',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.identifications`).d('条码号'),
      // multiple: ",",
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.operation`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      noCache: true,
      ignore: 'always',
    },
    {
      name: 'operationId',
      type: FieldType.string,
      bind: 'operationLov.operationId',
    },
    {
      name: 'wipTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.wipTimeFrom`).d('加工时间从'),
      max: 'wipTimeTo',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('operationId');
        },
        required: ({ record }) => {
          return record.get('operationId')
        },
      },
    },
    {
      name: 'wipTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.wipTimeTo`).d('加工时间至'),
      min: 'wipTimeFrom',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('operationId');
        },
        required: ({ record }) => {
          return record.get('operationId')
        },
      },
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellLov`).d('工位'),
      lovCode: 'HME.WORKCELL_CODE',
      lovPara: {
        tenantId,
      },
      textField: 'organizationCode',
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'workcellIds',
      bind: 'workcellLov.organizationId',
    },
    {
      name: 'modelObj',
      lovCode: 'APEX_MES.MATERIAL_MODEL_CODEANDMAL',
      type: FieldType.object,
      ignore: FieldIgnore.always,
      textField: 'modelCode',
      label: intl.get(`${modelPrompt}.modelCode`).d('型号'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      bind: 'modelObj.modelCode',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.materialId`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'freezeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.freezeStatus`).d('冻结解冻状态'),
      lookupCode: 'HME.EO_FREEZE_RECORD',
      lovPara: { tenantId },
    },
    {
      name: 'workOrder',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.workOrderNum3`).d('工单'),
      lovCode: 'MT.WORK_ORDER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workOrderId',
      type: FieldType.string,
      bind: 'workOrder.workOrderId',
    },
    {
      name: 'freezeTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.freezeTimeFrom`).d('冻结时间从'),
      max: 'freezeTimeTo',
    },
    {
      name: 'freezeTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.freezeTimeTo`).d('冻结时间至'),
      min: 'freezeTimeFrom',
    },
    {
      name: 'unFreezeTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.unFreezeTimeFrom`).d('解冻时间从'),
      max: 'unFreezeTimeTo',
    },
    {
      name: 'unFreezeTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.unFreezeTimeTo`).d('解冻时间至'),
      min: 'unFreezeTimeFrom',
    },
    {
      name: 'freezeReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeReason`).d('冻结原因'),
    },
    {
      name: 'unFreezeReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unFreezeReason`).d('解冻原因'),
    },
    {
      name: 'containerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerName`).d('容器'),
      lovCode: 'MT.CONTAINER',
      textField: 'containerName',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
    },
    {
      name: 'containerId',
      bind: 'containerLov.containerId',
    },
    {
      name: 'containerCode',
      bind: 'containerLov.containerCode',
    },
  ],
  fields: [
    // {
    //   name: 'orderNumber',
    // },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.identification1`).d('条码号'),
    },
    {
      name: 'identificationTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.identificationType`).d('条码状态'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工位描述'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialName`).d('物料描述'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.qty1`).d('数量'),
    },
    {
      name: 'operationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.operationDesc`).d('当前工序'),
    },
    {
      name: 'specifyOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.specifyOperationName`).d('指定冻结工艺'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.prodLineName`).d('生产线描述'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.workOrderNum2`).d('工单编码'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.siteCode`).d('工厂编码'),
    },
    {
      name: 'freezeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.freezeStatus`).d('状态'),
      lookupCode: 'HME.EO_FREEZE_RECORD',
    },
    {
      name: 'freezeReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.freezeReason`).d('原因'),
    },
    {
      name: 'freezeTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.freezeTime`).d('时间'),
    },
    {
      name: 'operatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.operatorName`).d('冻结解冻操作人'),
    },
  ],
});

const modalDS = () => ({
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  fields: [
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
      required: true,
    },
  ],
});
const modalProcessDS = () => ({
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  fields: [
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.operation`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      noCache: true,
      ignore: 'always',
      required: true,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
      required: true,
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单'),
    },
  ],
});

const InputLovDS = () => ({
  autoQuery: false,
  selection: 'multiple',
  paging: false,
  fields: [
    {
      type: 'string',
      name: 'code',
      label: intl.get('Default.code').d('编码'),
    },
  ],
  queryFields: [
    {
      type: 'string',
      name: 'code',
      label: intl.get('Default.code').d('编码'),
    },
  ],
});

export { tableDS, modalDS, modalProcessDS, InputLovDS };
