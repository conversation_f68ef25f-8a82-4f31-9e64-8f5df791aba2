/*
 * @Description: 市场活动评估单-详情界面DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-15 11:07:27
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-04 13:56:39
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.marketActive.marketEstimateDoc';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'marketEstimateCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketEstimateCode`).d('评估单编号'),
      disabled: true,
    },
    {
      name: 'marketEstimateStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketEstimateStatus`).d('状态'),
      lookupCode: 'YP.QIS.MARKET_ESTIMATE_STATUS',
      lovPara: { tenantId },
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'marketEstimateResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketEstimateResult`).d('评估结果'),
      lookupCode: 'YP.QIS.MARKET_ESTIMATE_RESULT',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'problemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
      lovCode: 'YP.QIS.PROBLEM_LIST',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'problemId',
      bind: 'problemLov.problemId',
    },
    {
      name: 'problemCode',
      bind: 'problemLov.problemCode',
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题描述'),
      bind: 'problemLov.problemTitle',
      disabled: true,
    },

    {
      name: 'responsibleDepartmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleDepartmentName`).d('责任部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'responsibleDepartmentId',
      bind: 'responsibleDepartmentLov.unitId',
    },
    {
      name: 'responsibleDepartmentName',
      bind: 'responsibleDepartmentLov.unitName',
    },
    {
      name: 'faultPhenomenon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultPhenomenon`).d('故障现象'),
      required: true,
    },
    {
      name: 'occurrence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.occurrence`).d('发生情况'),
      required: true,
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('重要度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'batteryPackModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryPackModel`).d('电池包型号'),
      disabled: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({ tenantId, siteId: record?.get('siteId') }),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'ypItemCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'ypItemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'itemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemCode`).d('客户零件号'),
      required: true,
    },
    {
      name: 'itemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemName`).d('客户零件名称'),
      required: true,
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'customerName',
      bind: 'customerLov.customerName',
    },
    {
      name: 'applyDepartmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.applyDepartmentName`).d('申请部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'applyDepartmentId',
      bind: 'applyDepartmentLov.unitId',
    },
    {
      name: 'applyDepartmentName',
      bind: 'applyDepartmentLov.unitName',
    },
    {
      name: 'vehicleModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('车型'),
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      lovPara: { tenantId },
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('提案理由'),
      required: true,
    },
    {
      name: 'objectQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.objectQty`).d('对象数量'),
      required: true,
      min: 0,
      validator: (value, name) => {
        if (value === 0) {
          return intl
            .get(`${modelPrompt}.validation.objectQtyMoreThanZero`)
            .d(`${name}必须大于0, 请检查!`);
        }
      },
    },
    {
      name: 'objectRange',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.objectRange`).d('对象范围'),
      required: true,
      bucketName: 'qms',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
    },
    {
      name: 'reviewInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewInfo`).d('评估信息'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('marketEstimateStatus') !== 'REVIEWING',
        required: ({ record }) => record?.get('marketEstimateStatus') === 'REVIEWING',
      },
    },
    {
      name: 'reviewBy',
    },
    {
      name: 'reviewByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewByName`).d('评估人'),
      disabled: true,
    },
    {
      name: 'reviewTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.reviewTime`).d('评估时间'),
      disabled: true,
    },
    {
      name: 'reviewEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.reviewEnclosure`).d('评估附件'),
      bucketName: 'qms',
      dynamicProps: {
        disabled: ({ record }) => record?.get('marketEstimateStatus') !== 'REVIEWING',
      },
    },
    {
      name: 'createdBy',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      disabled: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      disabled: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-estimates/details/ui`,
        method: 'GET',
      };
    },
  },
});
export { detailDS };