/* eslint-disable array-callback-return */
/**
 * @Description: 标准体系文件维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-06-27
 * @LastEditTime: 2022-06-28
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useState, useMemo } from 'react';
import {
  DataSet,
  Form,
  Table,
  TextField,
  Button,
  Switch,
  TextArea,
  Modal,
  NumberField,
  Attachment,
} from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { Content, Header } from 'components/Page';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import formatterCollections from 'utils/intl/formatterCollections';
import { ExpandCardC7n } from '@components/tarzan-ui';
import notification from 'utils/notification';
import {
  tableDetailDS,
  tableDetailLineDS,
} from '../stores/MaintenanceOfStandardSystemDocumentsDetailDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'key.hwms.front.MaintenanceOfStandardSystemDocuments';
const MaintenanceOfStandardSystemDocumentsDetail = props => {
  const {
    match: {
      params: { id },
    },
  } = props;

  let newList = [];

  const tableDetailDs = useMemo(() => new DataSet(tableDetailDS()), []);

  const tableDetailLineDs = useMemo(() => new DataSet(tableDetailLineDS()), []);

  const [expandedRender] = useState(false);

  const [expandIconColumnIndex] = useState(0);

  // const [headInfo, setHeadInfo] = useState({});

  const [editFlag, setEditFlag] = useState(false);

  const [saveFlag, setSaveFlag] = useState(true);

  const [addFlag, setAddFlag] = useState(true);

  const [formFlag, setFormFlag] = useState(true);

  useEffect(() => {
    if (id === 'create') {
      setEditFlag(true);
      setSaveFlag(false);
      setAddFlag(false);
      setFormFlag(false);
      setUuid();
    } else {
      queryDetail();
      setEditFlag(false);
      setSaveFlag(true);
      setAddFlag(true);
      setFormFlag(true);
    }
  }, [id]);

  const setUuid = async () => {
    tableDetailDs.current?.set('uuid', uuid());
    // setHeadInfo(tableDetailDs.toData()[0]);
  };

  // 正常进入查询
  const queryDetail = async () => {
    await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-template-file/detail/for/ui`, {
      method: 'GET',
      query: {
        templateFileId: id,
      },
    }).then(res => {
      if (res && res.success) {
        const newObj = {
          ...res.rows,
        };
        // setHeadInfo(newObj);
        tableDetailDs.loadData([newObj]);
        tableDetailLineDs.loadData(sort(res.rows.fileDtlList, 'sequence'));
      } else {
        return notification.error({ message: res.message });
      }
    });
  };

  const querySaveDetail = async value => {
    await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-template-file/detail/for/ui`, {
      method: 'GET',
      query: {
        templateFileId: value,
      },
    }).then(res => {
      if (res && res.success) {
        const newObj = {
          ...res.rows,
        };
        setSaveFlag(false);
        setAddFlag(false);
        setFormFlag(false);
        // setHeadInfo(newObj);
        tableDetailDs.loadData([newObj]);
        tableDetailLineDs.loadData(sort(res.rows.fileDtlList, 'sequence'));
      } else {
        return notification.error({ message: res.message });
      }
    });
  };

  const columns = [
    {
      name: 'nodeName',
      width: 500,
      editor: record => {
        return record.getState('myEditing');
      },
      renderer:({record})=>{
        return `${record.get('nodeCode')}${" "}${record.get('nodeName')}`
      }
    },
    {
      name: 'operate',
      renderer: ({ record }) => tableCommand(record),
      align: 'center',
      lock: 'right',
    },
  ];

  const sort = (arr, key) => {
    return arr.sort(function (a, b) {
      const a1 = a[key];
      const b1 = b[key];
      return a1 - b1;
    });
  };

  const handleEdit = record => {
    const data = tableDetailLineDs.toData().filter(ele => {
      return ele.templateFileDId === record.get('parentTemplateFileDId');
    });
    const directToryDS = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'nodeCode',
          label: intl.get(`${modelPrompt}.nodeCode`).d('节点编码'),
          type: FieldType.string,
          required: true,
        },
        {
          name: 'nodeName',
          label: intl.get(`${modelPrompt}.nodeName`).d('节点名称'),
          type: FieldType.string,
          required: true,
        },
        {
          name: 'parentTemplateFileDnodeName',
          label: intl.get(`${modelPrompt}.parentTemplateFileDnodeName`).d('上级节点'),
          type: FieldType.string,
        },
        {
          name: 'sequence',
          label: intl.get(`${modelPrompt}.sequence`).d('序号'),
          type: FieldType.number,
          required: true,
        },
      ],
    });
    const newObj = {
      ...record.data,
      parentTemplateFileDnodeName: data[0]?.nodeName,
    };
    directToryDS.loadData([newObj]);
    Modal.open({
      key: Modal.key(),
      title: intl.get('hzero.common.button.edit').d('编辑'),
      children: (
        <>
          <Form dataSet={directToryDS} columns={1}>
            <TextField name="nodeCode" />
            <TextField name="nodeName" />
            <TextField name="parentTemplateFileDnodeName" disabled />
            <NumberField name="sequence" />
          </Form>
        </>
      ),
      onOk: async () => {
        const nodeCode = directToryDS.current.get('nodeCode');
        const nodeName = directToryDS.current.get('nodeName');
        const sequence = directToryDS.current.get('sequence');
        const vFlag = await directToryDS.validate();
        if (!vFlag) {
          return false;
        }
        record.set('nodeName', nodeName);
        record.set('sequence', sequence);
        record.set('nodeCode', nodeCode);
        handSave();
      },
    });
  };

  // 点击操作列取消
  const handleCancel = record => {
    record.reset();
    record.setState('myEditing', false);
  };

  const tableCommand = record => {
    const buttons = [];
    const myEditing = record.getState('myEditing') || undefined;
    if (myEditing) {
      buttons.push(
        <a onClick={() => handleCancel(record)}>
          {intl.get('hzero.common.button.cancel').d('取消')}
        </a>,
      );
    } else {
      buttons.push(
        <>
          <a
            onClick={() => handleEdit(record)}
            style={{
              pointerEvents: !addFlag ? 'auto' : 'none',
              color: !addFlag ? '' : '#A9A9A9',
            }}
          >
            {intl.get('hzero.common.button.edit').d('编辑')}
          </a>
        </>,
        <a
          onClick={() => addSubordinate(record)}
          style={{
            pointerEvents: !addFlag ? 'auto' : 'none',
            color: !addFlag ? '' : '#A9A9A9',
          }}
        >
          {intl.get(`${modelPrompt}.addSubordinate`).d('新增下级')}
        </a>,
        <Popconfirm
          title={intl
            .get(`${modelPrompt}.message.confirm.deleteRecord`)
            .d('是否确认删除该条数据及其下层数据?')}
          onConfirm={() => handleBatchDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <a
            style={{
              pointerEvents: !addFlag ? 'auto' : 'none',
              color: !addFlag ? '' : '#A9A9A9',
            }}
          >
            {intl.get('hzero.common.button.delete').d('删除')}
          </a>
        </Popconfirm>,
      );
    }
    return [<span className="action-link">{buttons}</span>];
  };

  const addSubordinate = async record => {
    const len=(record.children||[]).length
    let nodeCode
    if(record.children&&len>0){
      const temp=record.children[len-1]
      const node=temp.get('nodeCode')
      const nodeAfter=node.slice(0,node.length-1)
      const nodeLast=node.slice(node.length-1,node.length)
      nodeCode=`${nodeAfter}${Number(nodeLast)+1}`
    }else{
      nodeCode=`${record.get('nodeCode')}.1`
    }
    let sum = 0;
    const sumData = tableDetailLineDs
      .toData()
      .filter(ele => {
        return (
          ele.nodeLevel === Number(record.get('nodeLevel') + 1) &&
          record.get('templateFileDId') === ele.parentTemplateFileDId
        );
      })
      .map(s => {
        return s.sequence;
      });
    if (sumData.length > 0) {
      const data = sumData.reduce((a, b) => (a > b ? a : b));
      sum = Number(data + 1);
    } else {
      sum = 1;
    }

    const directToryDS = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'nodeCode',
          label: intl.get(`${modelPrompt}.nodeCode`).d('节点编码'),
          type: FieldType.string,
        },
        {
          name: 'nodeName',
          label: intl.get(`${modelPrompt}.nodeName`).d('节点名称'),
          type: FieldType.string,
          required: true,
        },
        {
          name: 'parentTemplateFileDnodeName',
          label: intl.get(`${modelPrompt}.parentTemplateFileDnodeName`).d('上级节点'),
          type: FieldType.string,
        },
        {
          name: 'sequence',
          label: intl.get(`${modelPrompt}.sequence`).d('序号'),
          type: FieldType.number,
          required: true,
        },
      ],
    });
    directToryDS.current.set('sequence', sum);
    directToryDS.current.set('nodeCode', nodeCode);
    directToryDS.current.set('parentTemplateFileDnodeName', record.get('nodeName'));
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.button.addDirectory`).d('新增目录'),
      children: (
        <>
          <Form dataSet={directToryDS} columns={1}>
            <TextField name="nodeCode" />
            <TextField name="nodeName" />
            <TextField name="parentTemplateFileDnodeName" disabled />
            <NumberField name="sequence" />
          </Form>
        </>
      ),
      onOk: async () => {
        const nodeName = directToryDS.current.get('nodeName');
        const sequence = directToryDS.current.get('sequence');
        const nodeCode = directToryDS.current.get('nodeCode');
        const vFlag = await directToryDS.validate();
        if (!vFlag) {
          return false;
        }
        tableDetailLineDs.create(
          {
            _status: 'create',
            prentUuid: null,
            nodeCode,
            nodeName,
            sequence,
            templateFileDId: null,
            parentTemplateFileDId: record.get('templateFileDId'),
            nodeLevel: record.get('nodeLevel') + 1,
          },
          0,
        );
        handSave();
      },
    });
  };

  const addDirectory = async () => {
    let sum = 0;
    const data = tableDetailLineDs
      .toData()
      .filter(ele => {
        return ele.nodeLevel === 1;
      })
      .map(a => {
        return a.sequence;
      });
    if (data.length > 0) {
      const sumData = data.reduce((a, b) => (a > b ? a : b));
      sum = Number(sumData + 1);
    } else {
      sum = 1;
    }
    const directToryDS = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'nodeCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.nodeCode`).d('节点编码'),
        },
        {
          name: 'nodeName',
          label: intl.get(`${modelPrompt}.nodeName`).d('节点名称'),
          type: FieldType.string,
          required: true,
        },
        {
          name: 'parentTemplateFileDId',
          label: intl.get(`${modelPrompt}.parentTemplateFileDId`).d('上级节点'),
          type: FieldType.string,
        },
        {
          name: 'sequence',
          label: intl.get(`${modelPrompt}.sequence`).d('序号'),
          type: FieldType.number,
          required: true,
        },
      ],
    });
    directToryDS.current.set('sequence', sum);
    directToryDS.current.set('nodeCode', sum);
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.addNewcatalogue`).d('新增目录'),
      children: (
        <>
          <Form dataSet={directToryDS} columns={1}>
            <TextField name="nodeCode" />
            <TextField name="nodeName" />
            <TextField name="parentTemplateFileDId" disabled />
            <NumberField name="sequence" />
          </Form>
        </>
      ),
      onOk: async () => {
        const nodeName = directToryDS.current.get('nodeName');
        const nodeCode = directToryDS.current.get('nodeCode');
        const sequence = directToryDS.current.get('sequence');
        const vFlag = await directToryDS.validate();
        if (!vFlag) {
          return false;
        }
        tableDetailLineDs.create(
          {
            _status: 'create',
            nodeCode,
            nodeName,
            sequence,
            templateFileDId: null,
            prentUuid: null,
            parentTemplateFileDId: null,
            nodeLevel: 1,
          },
          0,
        );
        handSave();
      },
    });
  };

  // const UploadComponent = useCallback(() => {
  //   return (
  //     <UploadModal
  //       name="uuid"
  //       bucketName="fyds-lims"
  //       btnText="附件"
  //       attachmentUUID={headInfo.uuid}
  //     />
  //   );
  // }, [headInfo?.uuid]);

  const handSave = async value => {
    const flag = (await tableDetailDs.validate()) && (await tableDetailLineDs.validate());
    if (flag) {
      await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-template-file/save/for/ui`, {
        method: 'POST',
        body: {
          ...tableDetailDs.toData()[0],
          fileDtlList: tableDetailLineDs.toJSONData(),
          deleteFileDtlIds: value,
        },
      }).then(res => {
        if (res && res.success) {
          notification.success({});
          querySaveDetail(res.rows);
        } else {
          return notification.error({ message: res.message });
        }
      });
    }
  };

  const handSaveAll = async value => {
    const flag = (await tableDetailDs.validate()) && (await tableDetailLineDs.validate());
    if (flag) {
      await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-template-file/save/for/ui`, {
        method: 'POST',
        body: {
          ...tableDetailDs.toData()[0],
          fileDtlList: tableDetailLineDs.toJSONData(),
          deleteFileDtlIds: value,
        },
      }).then(res => {
        if (res && res.success) {
          notification.success({});
          if (id === 'create') {
            props.history.push({
              pathname: `/hwms/maintenance-of-standard-systemDocuments/dist/${res.rows}`,
            });
          } else {
            queryDetail();
            setEditFlag(false);
            setSaveFlag(true);
            setAddFlag(true);
            setFormFlag(true);
          }
        } else {
          return notification.error({ message: res.message });
        }
      });
    }
  };

  const handEdit = () => {
    setSaveFlag(!saveFlag);
    setAddFlag(!addFlag);
    setFormFlag(!formFlag);
  };

  // 删除行
  const handleBatchDelete = record => {
    newList = [];
    const recordList = [record];
    const data = deleteList(recordList);
    handSave(data);
    // const deleteList = [record];
    // const newList = tableDetailLineDs.filter((ele) => { return ele.get('parentTemplateFileDId') === record.get('templateFileDId') });
    // tableDetailLineDs.forEach((ele) => {
    //   newList.forEach((item) => {
    //     if (ele.get('parentTemplateFileDId') === item.get('templateFileDId')) {
    //       deleteList.push({
    //         ...ele,
    //       })
    //     }
    //   })
    // });
    // const list = deleteList.concat(newList)
    // tableDetailLineDs.remove(list)
    // const data = list.map((ele) => ele.data);
    // const idList = data.map((v) => { return v.templateFileDId })
    // setDelIdList(idList);
  };

  const deleteList = arryList => {
    if (arryList.length > 0) {
      arryList.map(item => {
        newList.push({
          ...item,
        });
        if (item.children) {
          if (item.children.length > 0) {
            item.children.map(ele => {
              newList.push({
                ...ele,
              });
              deleteList(item.children);
            });
          }
        }
      });
    }
    const data = [...new Set(newList.map(ele => ele.data))].map(ele => {
      return ele.templateFileDId;
    });
    return data;
  };

  // 附件配置
  const attachmentProps = {
    name: 'uuid',
    bucketName: 'qms',
    bucketDirectory: 'verification-library-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const handCancel = () => {
    if (id === 'create') {
      props.history.push('/hwms/maintenance-of-standard-systemDocuments/list');
    } else {
      queryDetail();
      setEditFlag(false);
      setSaveFlag(true);
      setAddFlag(true);
      setFormFlag(true);
    }
  };

  return (
    <div style={{ height: '100%' }} className="hmes-style">
      <Header
        title={
          id === 'create'
            ? intl.get(`${modelPrompt}.new`).d('标准体系文件维护创建')
            : intl.get(`${modelPrompt}.edit`).d('标准体系文件维护详情')
        }
        backPath="/hwms/maintenance-of-standard-systemDocuments/list"
      >
        {!saveFlag && (
          <>
            <Button
              icon="save"
              style={{
                backgroundColor: '#0840f8',
                color: 'white',
              }}
              type="c7n-pro"
              onClick={() => handSaveAll()}
              disabled={saveFlag}
            >
              {intl.get('hzero.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={() => handCancel()}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {saveFlag && (
          <>
            <Button
              style={{
                backgroundColor: '#0840f8',
                color: 'white',
              }}
              disabled={editFlag}
              onClick={() => handEdit()}
            >
              {intl.get('hzero.common.button.edit').d('编辑')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Form dataSet={tableDetailDs} columns={3}>
          <TextField name="templateFileCode" disabled={formFlag} />
          <TextField name="templateFileName" disabled={formFlag} />
          <Switch defaultChecked name="status" disabled={formFlag} />
          <Attachment {...attachmentProps} disabled={formFlag} />
          <TextArea name="remark" disabled={formFlag} rows={1} autoSize />
        </Form>
        <ExpandCardC7n
          showExpandIcon
          title={intl.get(`${modelPrompt}.detail`).d('详情')}
        >
          <div>
            <Table
              mode="tree"
              defaultRowExpanded
              expandedRowRenderer={expandedRender}
              columns={columns}
              expandIconColumnIndex={expandIconColumnIndex}
              dataSet={tableDetailLineDs}
              queryBar={TableQueryBarType.none}
              queryFieldsLimit={3}
              buttons={[
                'expandAll',
                'collapseAll',
                <Button
                  key="create"
                  type="c7n-pro"
                  icon="playlist_add"
                  funcType="flat"
                  color="primary"
                  onClick={() => addDirectory()}
                  disabled={addFlag}
                >
                  {intl.get(`${modelPrompt}.button.addDirectory`).d('新增目录')}
                </Button>,
              ]}
              pagination={{ showPager: true }}
              queryBarProps={{ autoQueryAfterReset: false }}
              rowHeight={34}
              border
            />
          </div>
        </ExpandCardC7n>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['key.hwms.front.MaintenanceOfStandardSystemDocuments', 'tarzan.common'],
})(MaintenanceOfStandardSystemDocumentsDetail);
