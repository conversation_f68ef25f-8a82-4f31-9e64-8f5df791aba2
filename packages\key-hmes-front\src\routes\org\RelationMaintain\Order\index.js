import React, { useState } from 'react';
import { Modal, Icon, Form, Switch, InputNumber, Popover } from 'hzero-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import EditTable from 'components/EditTable';
import intl from 'utils/intl';
import styles from './index.module.less';

const modelPrompt = 'tarzan.model.org.relation';

let _localStorage;

if (localStorage) {
  _localStorage = window.localStorage;
}

const OrderModal = props => {
  const [modalVisible, setModalVisible] = useState(false);
  const [enableFlagVisible, setEnableFlagVisible] = useState(
    _localStorage.getItem('enableFlagVisible') === 'N' ? 'N' : 'Y',
  );
  const [descriptionSequence, setDescriptionSequence] = useState(
    JSON.parse(_localStorage.getItem('descriptionSequence') || '{}').descriptionSequence || [
      {
        name: 'organizationType',
        value: '10',
        visible: true,
        _status: 'update',
      },
      {
        name: 'organizationCode',
        value: '20',
        visible: true,
        _status: 'update',
      },
      {
        name: 'organizationName',
        value: '30',
        visible: true,
        _status: 'update',
      },
    ],
  );
  const {
    form: { getFieldDecorator, getFieldValue },
    dispatch,
  } = props;

  const handleOk = () => {
    const _descriptionSequence = [];
    descriptionSequence.forEach(item => {
      const { name, value, visible, _status } = item;
      item.$form.validateFields((err, values) => {
        _descriptionSequence.push({
          name,
          value,
          visible,
          _status,
          ...values,
        });
      });
    });
    let visibleSum = 0;
    _descriptionSequence.forEach(item => {
      if (item.visible) {
        visibleSum++;
      }
    });
    if (visibleSum === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.displayFlagRule`).d('“是否显示”一列中至少启用一个'),
      });
      return;
    }
    setModalVisible(false);
    const _enableFlagVisible = getFieldValue('enableFlagVisible');
    _localStorage.setItem(
      'descriptionSequence',
      JSON.stringify({ descriptionSequence: _descriptionSequence }),
    );
    _localStorage.setItem('enableFlagVisible', _enableFlagVisible);
    setEnableFlagVisible(_enableFlagVisible);
    setDescriptionSequence(_descriptionSequence);
    setTimeout(() => {
      getAllTreeData();
    }, 300);
  };

  const handleOpen = () => {
    setModalVisible(true);
  };

  const handleCancel = () => {
    setModalVisible(false);
  };

  const getAllTreeData = () => {
    dispatch({
      type: 'relationMaintain/allTreeData',
    }).then(res => {
      if (res) {
        dispatch({
          type: 'relationMaintain/updateState',
          payload: {
            treeData: res,
            originData: res,
          },
        });
      }
    });
  };

  const modalProps = {
    title: intl.get(`${modelPrompt}.organizationCustom`).d('组织关系个性化'),
    width: 400,
    destroyOnClose: true,
    maskClosable: false,
    style: {
      minWidth: 400,
    },
    bodyStyle: { padding: '16px' },
    visible: modalVisible,
    wrapClassName: 'ant-modal-sidebar-right hcm-hzero-drawer-style',
    transitionName: 'move-right',
    onOk: handleOk,
    onCancel: handleCancel,
  };

  const labelNameMap = {
    organizationName: intl.get(`${modelPrompt}.organizationDesc`).d('组织描述'),
    organizationCode: intl.get(`${modelPrompt}.organizationCode`).d('组织编码'),
    organizationType: intl.get(`${modelPrompt}.organizationType`).d('组织类型'),
  };

  const columns = [
    {
      title: intl.get(`${modelPrompt}.fieldName`).d('字段名称'),
      align: 'left',
      dataIndex: 'name',
      width: '33%',
      render: val => {
        return labelNameMap[val];
      },
    },
    {
      title: intl.get(`${modelPrompt}.displayFlag`).d('是否显示'),
      align: 'center',
      dataIndex: 'visible',
      width: '33%',
      render: (val, record) => (
        <Form.Item>
          {record.$form.getFieldDecorator(`visible`, {
            initialValue: record.visible,
          })(<Switch />)}
        </Form.Item>
      ),
    },
    {
      title: intl.get(`${modelPrompt}.displayOrder`).d('显示顺序'),
      dataIndex: 'value',
      width: '33%',
      render: (val, record) => (
        <Form.Item>
          {record.$form.getFieldDecorator(`value`, {
            initialValue: record.value,
          })(
            <InputNumber
              min={0}
              formatter={newVal => {
                return `${newVal}`.replace(/[^0-9]/gi, '');
              }}
            />,
          )}
        </Form.Item>
      ),
    },
  ];

  return (
    <>
      <Popover
        placement="topRight"
        content={intl.get(`${modelPrompt}.organizationCustom`).d('组织关系个性化')}
      >
        <Icon
          type="bars"
          style={{ fontSize: 20 }}
          className={styles.iconStyle}
          onClick={handleOpen}
        />
      </Popover>
      <Modal {...modalProps}>
        <Form>
          <div className={styles.switchBox}>
            <Form.Item>
              {intl.get(`${modelPrompt}.displayProhibitOrg`).d('显示失效的组织')}:&nbsp;
            </Form.Item>
            <Form.Item>
              {getFieldDecorator('enableFlagVisible', {
                initialValue: enableFlagVisible,
              })(<Switch checkedValue="Y" unCheckedValue="N" />)}
            </Form.Item>
          </div>
        </Form>
        <EditTable
          bordered={false}
          rowKey="name"
          columns={columns}
          dataSource={descriptionSequence}
          pagination={false}
        />
      </Modal>
    </>
  );
};
export default formatterCollections({
  code: ['tarzan.model.org.relation', 'tarzan.common'],
})(Form.create()(OrderModal));
