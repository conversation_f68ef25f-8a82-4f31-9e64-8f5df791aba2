/**
 * @Description: 库存查询 - 入口页DS
 * @Author: <EMAIL>
 * @Date: 2022/7/6 15:39
 * @LastEditTime: 2022-11-21 13:37:27
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@/utils/config';
import uuid from 'uuid/v4';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import { getCurrentSiteInfo } from '@utils/utils';

const modelPrompt = 'tarzan.inventory.query.mes.model.query';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  paging: 'server',
  pageSize: 10,
  primaryKey: 'uuid',
  parentField: 'parentUuid',
  idField: 'uuid',
  expandField: 'expand',
  modifiedCheck: false,
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        defaultValue: () => {
          const  siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo }
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'areaLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategories: 'AREA',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'wareHouseIds',
      bind: 'areaLocatorLov.locatorId',
    },
    {
      name: 'wareHouseCodes',
      bind: 'areaLocatorLov.locatorCode',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorLovCode`).d('货位编码'),
      lovCode: 'MT.MODEL.SUB_LOCATOR',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategories: ["INVENTORY", "LOCATION"],
            locatorIds: [record.get('wareHouseIds')],
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'locatorIds',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorCode',
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialId`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            enableFlag: 'Y',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'revisionCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      multiple: true,
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
    },
    {
      name: 'lotCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次'),
      multiple: true,
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'holdFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.holdFlag`).d('预留库存'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
      textField: 'description',
      valueField: 'typeCode',
      options: new DataSet({
        autoQuery: true,
        dataKey: 'rows',
        paging: false,
        transport: {
          read: () => {
            return {
              url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
              method: 'GET',
              params: { tenantId },
              transformResponse: val => {
                const data = JSON.parse(val);
                data.rows.push({
                  description: intl.get(`tarzan.common.ownerType`).d('自有'),
                  typeCode: 'ALL',
                  typeGroup: 'OWNER_TYPE',
                });
                return {
                  ...data,
                };
              },
            };
          },
        },
      }),
    },
    {
      name: 'ownerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'MT.MODEL.CUSTOMER';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'MT.MODEL.SUPPLIER';
            case 'OI':
              return 'MT.MES.SO_LINE';
            default:
              return 'MT.MES.EMPTY';
          }
        },
        textField({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'customerCode';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'supplierCode';
            case 'OI':
              return 'soNumContent';
            default:
              return 'noData';
          }
        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerLov.customerId',
      dynamicProps: {
        bind({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'ownerLov.customerId';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'ownerLov.supplierId';
            case 'OI':
              return 'ownerLov.soLineId';
            default:
              return 'ownerLov.customerId';
          }
        },
      },
    },
    {
      name: 'visibility',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.visibility`).d('汇总维度'),
      options: new DataSet({
        data: [
          {
            typeCode: 'locatorFlag',
            description: intl.get(`${modelPrompt}.locatorLovCode`).d('货位编码'),
          },
          {
            typeCode: 'revisionCodeFlag',
            description: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
          },
          {
            typeCode: 'lotCodeFlag',
            description: intl.get(`${modelPrompt}.lotCode`).d('批次'),
          },
          {
            typeCode: 'statusFlag',
            description: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
          },
          {
            typeCode: 'ownerCodeFlag',
            description: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
          },
          {
            typeCode: 'ownerTypeFlag',
            description: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
          },
        ],
        selection: DataSetSelection.multiple,
        autoQuery: true,
      }),
      defaultValue: [
        'locatorFlag',
        'revisionCodeFlag',
        'lotCodeFlag',
        'statusFlag',
        'ownerCodeFlag',
        'ownerTypeFlag',
      ],
      textField: 'description',
      valueField: 'typeCode',
      multiple: true,
    },
  ],
  fields: [
    {
      name: 'uuid',
      type: FieldType.string,
    },
    {
      name: 'parentUuid',
      type: FieldType.string,
    },
    {
      name: 'expand',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'wareHouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorLovCode`).d('货位编码'),
    },
    {
      name: 'wareHouseName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wareHouseDesc`).d('仓库描述'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorLovDesc`).d('货位描述'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'lotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'onhandQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.onhandQty`).d('库存'),
    },
    {
      name: 'availableQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.availableQty`).d('可用库存'),
    },
    {
      name: 'holdQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.holdQty`).d('预留库存'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uom`).d('单位'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
    },
    {
      name: 'ownerId',
      type: FieldType.string,
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
    {
      name: 'materialBomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBomCode`).d('BOM号'),
    },
    {
      name: 'materialBrand',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBrand`).d('品牌'),
    },
    {
      name: 'materialModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialModel`).d('型号'),
    },
    {
      name: 'productionDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    },
    {
      name: 'grade',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.grade`).d('等级'),
    },
    {
      name: 'property',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.property`).d('属性'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inv-onhand-quantity/query/ui`,
        method: 'POST',
        data: {
          ...data,
          locatorFlag: data.visibility.includes('locatorFlag'),
          revisionCodeFlag: data.visibility.includes('revisionCodeFlag'),
          statusFlag: data.visibility.includes('statusFlag'),
          ownerCodeFlag: data.visibility.includes('ownerCodeFlag'),
          ownerTypeFlag: data.visibility.includes('ownerTypeFlag'),
          lotCodeFlag: data.visibility.includes('lotCodeFlag'),
          lotCodes: data.lotCodes || undefined,
        },
        transformResponse: val => {
          const data = JSON.parse(val);
          const { content } = data.rows;
          content.forEach(item => {
            item.uuid = uuid();
          });
          return {
            ...data,
          };
        },
      };
    },
  },
});

export { entranceDS };
