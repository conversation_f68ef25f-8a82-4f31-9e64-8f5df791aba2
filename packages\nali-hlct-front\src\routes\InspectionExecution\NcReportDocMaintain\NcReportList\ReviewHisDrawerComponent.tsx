/**
 * @Description: 主界面-审批历史抽屉
 * @Author: <EMAIL>
 * @Date: 2023/3/15 15:02
 */
import React, { useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Icon, Table } from 'choerodon-ui/pro';
import { Collapse, Tag } from 'choerodon-ui';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TarzanSpin } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import { reviewHisInfoDS, reviewHisLineDS } from '../stores';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hwms.ncReportDocMaintain';
const { Panel } = Collapse;

export default props => {
  const { customizeTable, record } = props;
  const reviewHisInfoDs = useMemo(() => new DataSet(reviewHisInfoDS()), []);
  const [activeKey, setActiveKey] = useState<any>([]);
  const [reviewHisInfoDsData, setReviewHisInfoDsData] = useState<any>([]);

  useEffect(() => {
    reviewHisInfoDs.setQueryParameter('ncReportId', record.get('ncReportId'));
    reviewHisInfoDs.query().then(res => {
      if (res?.rows) {
        const _keyList = res.rows.map(item => String(item.ncReportReviewId));
        setActiveKey(_keyList);
        setReviewHisInfoDsData(res.rows);
      } else {
        setActiveKey([]);
        setReviewHisInfoDsData([]);
      }
    });
  }, []);

  const CollapsePanelTitle = observer(({ record }) => {
    const renderStatusTag = record => {
      const { reviewResult, reviewResultDesc } = record;
      switch (reviewResult) {
        case 'PASS':
          return (
            <>
              <span style={{ color: 'rgba(32, 212, 137, 1)' }}>
                <Icon
                  type="check_circle_outline-o"
                  style={{
                    fontSize: 16,
                    color: 'rgba(32, 212, 137, 1)',
                    position: 'relative',
                    top: '-1px',
                    marginRight: '2px',
                  }}
                />
                {reviewResultDesc}
              </span>
            </>
          );
        default:
          return (
            <>
              <span style={{ color: 'rgba(232, 28, 28, 0.85)' }}>
                <Icon
                  type="cancel-o"
                  style={{
                    fontSize: 16,
                    color: 'rgba(232, 28, 28, 0.85)',
                    position: 'relative',
                    top: '-1px',
                    marginRight: '2px',
                  }}
                />
                {reviewResultDesc}
              </span>
            </>
          );
      }
    };

    return (
      <span>
        <span className={styles['panel-title-item']}>
          {intl.get(`${modelPrompt}.sequence`).d('审核顺序')}:{record.sequence}
        </span>
        <span className={styles['panel-title-item']}>
          {intl.get(`${modelPrompt}.reviewUserName`).d('审核人')}:{record.reviewUserName}
        </span>
        <span className={styles['panel-title-item']}>
          {intl.get(`${modelPrompt}.reviewTime`).d('审核时间')}:{record.reviewTime}
        </span>
        {renderStatusTag(record)}
      </span>
    );
  });

  const PanelTable = observer(({ record }) => {
    const panelLineDs = useMemo(
      () =>
        new DataSet({
          ...reviewHisLineDS(),
          data: record.ncReportReviewDtls,
        }),
      [],
    );

    const panelLineColumns: ColumnProps[] = useMemo(
      () => [
        { name: 'ncRecordTypeDesc', width: 120 },
        { name: 'ncObjectTypeDesc', width: 120 },
        { name: 'ncObjectCode', width: 120 },
        { name: 'revisionCode' },
        { name: 'dispositionFunctionDesc' },
        { name: 'qty' },
        { name: 'uomName' },
        { name: 'reworkRouterName', width: 150 },
        // { name: 'reworkStepName', width: 150 },
        { name: 'reworkOperationName', width: 150 },
        { name: 'reworkWorkcellName', width: 150 },
        { name: 'remark' },
      ],
      [],
    );

    return customizeTable(
      {
        code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.LINE_BASIC`,
      },
      <Table
        dataSet={panelLineDs}
        columns={panelLineColumns}
        highLightRow={false}
        customizedCode="ncReportDocMaintain-panelTable"
      />,
    );
  });

  return (
    <TarzanSpin dataSet={reviewHisInfoDs}>
      <Collapse bordered={false} activeKey={activeKey} onChange={value => setActiveKey(value)}>
        {reviewHisInfoDsData?.length &&
          reviewHisInfoDsData.map(record => (
            <Panel
              key={String(record.ncReportReviewId)}
              header={<CollapsePanelTitle record={record} />}
              extra={
                record.finalConfirmFlag === 'Y' && (
                  <Tag
                    style={{
                      color: 'rgb(32, 212, 137)',
                      background: 'rgba(32, 212, 137, .1)',
                      border: '1px solid rgba(32, 212, 137, .1)',
                    }}
                  >
                    {intl.get(`${modelPrompt}.finalConfirmFlag`).d('最终审核结果')}
                  </Tag>
                )
              }
            >
              <PanelTable record={record} />
            </Panel>
          ))}
      </Collapse>
    </TarzanSpin>
  );
};
