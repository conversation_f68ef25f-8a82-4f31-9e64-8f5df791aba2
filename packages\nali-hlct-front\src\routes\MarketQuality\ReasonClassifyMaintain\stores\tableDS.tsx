/*
 * @Description: 原因分类维护-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-11 14:07:22
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-15 16:42:13
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { getProductWithPrecision } from '@/utils';

const modelPrompt = 'tarzan.qms.reasonClassifyMaintain';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  selection: false,
  forceValidate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'reasonCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonCode`).d('原因编码'),
    },
    {
      name: 'reasonClassify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonClassify`).d('原因分类'),
      lookupCode: 'YP.QIS.REASON',
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
    },
    {
      name: 'reasonStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonStatus`).d('原因状态'),
      lookupCode: 'YP.QIS.ENABLED_FLAG',
    },
  ],
  fields: [
    {
      name: 'reasonCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonCode`).d('原因编码'),
    },
    {
      name: 'reasonClassify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonClassify`).d('原因分类'),
      lookupCode: 'YP.QIS.REASON',
      required: true,
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
      required: true,
    },
    {
      name: 'aResponsibilityRatio',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.aResponsibilityRatio`).d('甲方责任比例'),
      max: 100,
      min: 0,
      validator: (value, _, record: any) => {
        const a = value;
        record?.set('bResponsibilityRatio', getProductWithPrecision(100, a, 6, 'subtraction'));
        return true;
      },
      required: true,
    },
    {
      name: 'bResponsibilityRatio',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.bResponsibilityRatio`).d('乙方责任比例'),
      max: 100,
      min: 0,
      validator: (value, _, record: any) => {
        const b = value;
        record?.set('aResponsibilityRatio', getProductWithPrecision(100, b, 6, 'subtraction'));
        return true;
      },
      required: true,
    },
    {
      name: 'reasonStatus',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.reasonStatus`).d('原因状态'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-reason/list/ui`,
      method: 'get',
    },
  },
});

export { tableDS };
