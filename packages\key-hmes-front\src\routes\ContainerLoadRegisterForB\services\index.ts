import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
// const BASIC = {
//   HMES_BASIC: '/aprs-mes-38283',
// };

/**
 * 获取物料批/EO装载信息
 * @function GetBarcodeInfo
 * @returns {object} fetch Promise
 */
export function GetBarcodeInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-to-b/container-barcode/scan/ui`,
    method: 'GET',
  };
}

/**
 * 解绑物料批/EO
 * @function UnbindMaterialLotInfo
 * @returns {object} fetch Promise
 */
export function UnbindMaterialLotInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-to-b/box/unload/ui`,
    method: 'POST',
  };
}

/**
 * 打包物料批/EO
 * @function PackingMaterialLotInfo
 * @returns {object} fetch Promise
 */
export function PackingMaterialLotInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-to-b/load-register/execute/ui`,
    method: 'POST',
  };
}

export function GetDetailBarcodeInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-to-b/tray/scan/ui`,
    method: 'POST',
  };
}

export function HandleLoad(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-to-b/tray/load/ui`,
    method: 'POST',
  };
}

export function HandleUnLoad(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-to-b/tray/unload/ui`,
    method: 'POST',
  };
}
