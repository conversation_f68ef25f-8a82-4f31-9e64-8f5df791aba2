/**
 * @Description: 质量检验数据查询报表
 */

import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hmes.QualityInspectionDataQuery';
const tenantId = getCurrentOrganizationId();

const qualityInspectionDataTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  forceValidate: true,dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      required: true,
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
      // required: true,
      dynamicProps: {
        required: ({ record }) => !record?.get('processWorkcellIds').length,
      },
    },
    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'inspectBusTypeRul',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lovPara: { tenantId },
      required: true,
      lookupCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      textField: 'inspectBusinessTypeDesc',
      valueField: 'inspectBusinessType',
      dynamicProps: {
        disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('机台'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'equipmentIds',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'startDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startDate`).d('查询日期从'),
      max: 'endDate',
      // required: true,
    },
    {
      name: 'endDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endDate`).d('查询日期到'),
      min: 'startDate',
      // required: true,
    },
    {
      name: 'baseMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.baseMaterialCode`).d('母卷号'),
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        workcellType: 'PROCESS',
      },
      ignore: FieldIgnore.always,
      multiple: true,
      // required: true,
      dynamicProps: {
        required: ({ record }) => !record?.get('materialIds').length,
      },
    },
    {
      name: 'processWorkcellIds',
      bind: 'processWorkcellLov.workcellId',
    },
  ],
  fields: [
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'inspectInfoCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfoCreationDate`).d('报检时间'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectionOrderCompletionTime`).d('检验单完成时间'),
    },
    {
      name: 'ncReportLastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportLastUpdateDate`).d('检验单关闭时间'),
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('机台'),
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单号'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格型号'),
    },
    {
      name: 'baseMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.baseMaterialCode`).d('基膜卷号'),
    },
    {
      name: 'mixMaterialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mixMaterialLotCode`).d('电镀药水编码'),
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectCode`).d('报检条码'),
    },
    {
      name: 'lastInspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastInspectResult`).d('判定'),
    },
    {
      name: 'firstInspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstInspectorName`).d('初次检验员'),
    },
    {
      name: 'reviewUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewUserName`).d('最终审核员'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('评审结果'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('评审备注'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/report/query/ui`,
        method: 'POST',
      };
    },
  },
});


export {
  qualityInspectionDataTableDS,
};
