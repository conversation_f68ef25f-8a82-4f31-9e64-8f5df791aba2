/**
 * @Description: 检证任务执行平台-主界面
 */
import React, { FC, useMemo } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { Badge, Collapse, Tag } from 'choerodon-ui';
import { DataSet, Table } from 'choerodon-ui/pro';
// import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { headDS, lineDS } from '../stores';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';

const modelPrompt = 'tarzan.inspectExecute.taskExePlatform';
const { Panel } = Collapse;

interface TaskListProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
}

const SaVerificationList: FC<TaskListProps> = props => {
  const { headDs, lineDs, history } = props;
  // const { run: changeDocStatus, loading: changeDocStatusLoading } = useRequest(ChangeDocStatus(), {
  //   manual: true,
  // });
  // const { run: batchReview, loading: reviewLoading } = useRequest(BatchReview(), {
  //   manual: true,
  // });

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('verificationTaskGroupId'));
    } else {
      queryLineTable(null);
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = verificationTaskGroupId => {
    lineDs.loadData([]);
    if (verificationTaskGroupId) {
      lineDs.setQueryParameter('verificationTaskGroupId', verificationTaskGroupId);
      lineDs.query();
    }
  };

  const renderStatusTag = (value, record, name) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'IN_APPROVAL':
        className = 'orange';
        break;
      case 'REJECTED':
        className = 'red';
        break;
      case 'RELESAED':
        className = 'purple';
        break;
      case 'COMPLETED':
        className = 'green';
        break;
      case 'NEW':
        className = 'blue';
        break;
      default:
        className = 'grey';
    }
    return <Tag color={className}>{record!.getField(name)!.getText()}</Tag>;
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'taskGroupCode',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/inspect-execute/task-exe-platform/dist/${record!.get(
                    'verificationTaskGroupId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'taskGroupStatus',
        width: 130,
        renderer: ({ value, record }) => renderStatusTag(value, record, 'taskGroupStatus'),
      },
      { name: 'siteName', width: 180, lock: ColumnLock.left },
      { name: 'responsibleUserName' },
      { name: 'lastResponsibleUserName' },
      { name: 'departmentName' },
      { name: 'planEndTime' },
      { name: 'actualEndTime' },
      {
        name: 'verificationCode',
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/inspect-execute/sa-verification-platform/dist/${record!.get(
                    'verificationId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'projectName' },
      { name: 'createMethod' },
      { name: 'productType' },
      { name: 'verificationFrom' },
      { name: 'verificationPeriod' },
      { name: 'verificationType' },
      { name: 'failureMode' },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'itemGroup' },
      {
        name: 'problemCode',
        width: 180,
        renderer: ({ value, record }) => {
          if (!value) {
            return;
          }
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/problem-management/problem-management-platform/dist/${record!.get(
                    'problemId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'createdByName' },
      { name: 'creationDate', align: ColumnAlign.center, width: 150 },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 150,
        renderer: ({ record }) => (
          <ApprovalInfoDrawer
            objectTypeList={['QIS_JZRW_LWS']}
            objectId={record?.get('verificationTaskGroupId')}
            type="text"
          />
        ),
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'taskCode', width: 180, lock: ColumnLock.left },
      { name: 'taskContent' },
      { name: 'planEndTime', align: ColumnAlign.center, width: 150 },
      {
        name: 'applyFlag',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
      },
      { name: 'taskResult' },
      { name: 'noApplyReason' },
      { name: 'enclosure', width: 150, lock: ColumnLock.right },
    ],
    [],
  );

  // const handleAdd = useCallback(() => {
  //   history.push(`/hwms/inspect-execute/sa-verification-platform/dist/create`);
  // }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('检证任务执行平台')}>
        {/* <Button color={ButtonColor.primary} icon="add" onClick={handleAdd}> */}
        {/*  {intl.get('tarzan.common.button.create').d('新建')} */}
        {/* </Button> */}
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headDs}
          columns={headColumns}
          searchCode="taskExePlatform1"
          customizedCode="taskExePlatform-listHeader"
          onRow={({ record }) => ({
            onClick: () => queryLineTable(record?.get('verificationTaskGroupId')),
          })}
        />
        <Collapse bordered={false} defaultActiveKey={['line']}>
          <Panel key="line" header={intl.get(`${modelPrompt}.title.line`).d('行信息')}>
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="taskExePlatform-listLine"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common','hzero.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(SaVerificationList as any),
);
