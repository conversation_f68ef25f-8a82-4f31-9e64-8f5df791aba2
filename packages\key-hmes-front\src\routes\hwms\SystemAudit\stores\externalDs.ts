import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import {  API_HOST } from '@src/utils/constants';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.aps.external';
// const BASIC={
//   TARZAN_SAMPLING:'/tznq-24175'
// }
const ExternalFactory = () =>
  new DataSet({
    primaryKey: 'fileId',
    selection: DataSetSelection.multiple,
    autoQuery: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    cacheSelection:true,
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'fileCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.table.fileCode`).d('文件编号'),
        },
        {
          name: 'fileName',
          label: intl.get(`${modelPrompt}.table.fileName`).d('文件名称'),
          type: FieldType.string,
        },
        {
          name: 'fileLevel',
          lookupCode: 'YP.QIS.FILE_LEVEL',
          label: intl.get(`${modelPrompt}.table.fileLevel`).d('文件级别'),
          type: FieldType.string,
        },
        {
          name: 'version',
          label: intl.get(`${modelPrompt}.table.version`).d('版本号'),
          type: FieldType.string,
        },
        {
          name: 'processType',
          lookupCode: 'YP.QIS.PROCESS_TYPE',
          label: intl.get(`${modelPrompt}.table.processType`).d('程序类型'),
          type: FieldType.string,
        },
        {
          name: 'affliatedProcess',
          lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
          label: intl.get(`${modelPrompt}.table.affliatedProcess`).d('所属过程'),
          type: FieldType.string,
        },
      ],
    }),
    fields: [
      {
        name: 'fileId',
        type: FieldType.string,
      },
      {
        name: 'fileCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.fileCode`).d('文件编号'),
      },
      {
        name: 'fileName',
        label: intl.get(`${modelPrompt}.table.fileName`).d('文件名称'),
        type: FieldType.string,
      },
      {
        name: 'fileLevel',
        lookupCode: 'YP.QIS.FILE_LEVEL',
        label: intl.get(`${modelPrompt}.table.fileLevel`).d('文件级别'),
        type: FieldType.string,
      },
      {
        name: 'version',
        label: intl.get(`${modelPrompt}.table.version`).d('版本号'),
        type: FieldType.string,
      },
      {
        name: 'processType',
        lookupCode: 'YP.QIS.PROCESS_TYPE',
        label: intl.get(`${modelPrompt}.table.processType`).d('程序类型'),
        type: FieldType.string,
      },
      {
        name: 'affliatedProcess',
        lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
        label: intl.get(`${modelPrompt}.table.affliatedProcess`).d('所属过程'),
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          // url:  `${API_HOST}/tznq-24175/v1/${getCurrentOrganizationId()}/qis-system-files/internal-file/ui`,
          url: `${API_HOST}${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-system-files/internal-file/ui`,
        };
      },
    },
  });

export default ExternalFactory;
