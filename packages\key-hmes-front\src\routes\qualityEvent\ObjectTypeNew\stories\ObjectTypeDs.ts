/**
 * @Description: 事件对象类型维护 DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-19 09:53:45
 * @LastEditTime: 2022-12-26 15:42:22
 * @LastEditors: <<EMAIL>>
 */

import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.event.objectType.model.objectType';

const tableDS = (): DataSetProps => ({
  autoQuery: true,
  autoCreate: true,
  pageSize: 10,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  primaryKey: 'objectTypeId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-object-type/limit-property/list/ui`,
        method: 'GET',
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.mes.domain.entity.MtEventObjectType';
      return {
        data: { objectTypeId: record.get('objectTypeId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_SAMPLING}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  queryFields: [
    {
      name: 'objectTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectTypeCode`).d('对象类型编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('对象类型描述'),
    },
  ],
  fields: [
    {
      name: 'objectTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectTypeCode`).d('对象类型编码'),
      required: true,
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.description`).d('对象类型描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'objectQuerySQL',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.objectQuerySQL`).d('对象查询语句'),
    },
    {
      name: 'objectExhibitionCol',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectExhibitionCol`).d('对象展示列'),
    },
    {
      name: 'objectPreview',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectPreview`).d('对象展示预览'),
    },
    {
      name: 'operator',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.action').d('操作'),
    },
    {
      name: 'tableName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tableName`).d('对象查询表信息'),
    },
    {
      name: 'conditionClause',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.whereClause`).d('对象查询语句'),
    },
    {
      name: 'notice',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.noLabel`).d(''),
    },
    {
      name: 'eventTypeQuerySql',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeQuerySql`).d('对象语句查询'),
    },
  ],
});

const tableDrawerDS = (): DataSetProps => ({
  autoQuery: true,
  autoCreate: true,
  pageSize: 10,
  selection: false,
  primaryKey: 'objectColumnId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-object-column/limit-property/list/ui`,
        method: 'GET',
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.mes.domain.entity.MtEventObjectColumn';
      return {
        data: { objectColumnId: record.get('objectColumnId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_SAMPLING}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('展示顺序'),
      step: 1,
      nonStrictStep: true,
      required: true,
    },
    {
      name: 'columnField',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.columnField`).d('展示列字段'),
      required: true,
    },
    {
      name: 'columnType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.columnType`).d('展示列类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=EVENT&typeGroup=OBJECT_COLUMN_TYPE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'columnTitle',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.columnTitle`).d('展示标题'),
    },
    {
      name: 'eventFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventFlag`).d('事件主键标识'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'displayFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.displayFlag`).d('是否展示'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'operator',
      type: FieldType.string,
      label: intl.get('hzero.common.button.action').d('操作'),
    },
  ],
});

export { tableDS, tableDrawerDS };
