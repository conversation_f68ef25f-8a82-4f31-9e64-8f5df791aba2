/**
 * @Description: ORT测试申请-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-18 10:38:58
 * @LastEditTime: 2023-09-18 10:38:58
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.ort.testApplication';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

// 列表-ds
const headerDS = (type): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  primaryKey: 'problemWarnListId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-apply/query/head/list/ui`,
        method: 'get',
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('申请单号'),
      name: 'inspectDocNum',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('状态'),
      name: 'inspectDocStatus',
      lookupCode: 'YP.QIS.ORT_INSPECT_DOC_STATUS',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectType`).d('申请分类'),
      name: 'inspectType',
      lookupCode: 'YP.QIS.ORT_INSPECT_TYPE',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      name: 'materialLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.MATERIAL',
      valueField: 'materialId',
      textField: 'materialCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      bind: 'materialLov.materialName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('委托人'),
      name: 'createdByLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      bind: 'createdByLov.userId',
    },
    {
      name: 'createdByName',
      bind: 'createdByLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
      name: 'projectStage',
      lookupCode: 'YP.QIS.ORT_PROJECT_STAGE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResultDemand`).d('测试结果要求'),
      name: 'inspectResultDemand',
      lookupCode: 'YP.QIS.ORT_INSPECT_RESULT_DEMAND',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgencyDegree`).d('紧急程度'),
      name: 'urgencyDegree',
      lookupCode: 'YP.QIS.ORT_URGENCY_DEGREE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleType`).d('样品类型'),
      name: 'sampleType',
      lookupCode: 'YP.QIS.ORT_SAMPLE_TYPE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.internalProductFlag`).d('是否厂内电芯'),
      name: 'internalProductFlag',
      lookupCode: 'YP.QIS.Y_N',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      valueField: 'prodLineId',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('申请单号'),
      name: 'inspectDocNum',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('状态'),
      name: 'inspectDocStatus',
      lookupCode: 'YP.QIS.ORT_INSPECT_DOC_STATUS',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteLov',
      lovCode: 'MT.MODEL.SITE',
      valueField: 'siteId',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectType`).d('申请分类'),
      name: 'inspectType',
      lookupCode: 'YP.QIS.ORT_INSPECT_TYPE',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      name: 'materialLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.MATERIAL',
      valueField: 'materialId',
      textField: 'materialCode',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('internalProductFlag') === 'Y';
        },
      },
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'productType',
      label: intl.get(`${modelPrompt}.productType`).d('产品规格'),
      bind: 'materialLov.productType',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('委托人'),
      name: 'createdByLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      bind: 'createdByLov.userId',
    },
    {
      name: 'createdByName',
      bind: 'createdByLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.department`).d('委托部门'),
      name: 'department',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectPurpose`).d('申请目的'),
      name: 'inspectPurpose',
      required: true,
    },
    {
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleQty`).d('送样数量'),
      name: 'sampleQty',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleType`).d('样品类型'),
      name: 'sampleType',
      lookupCode: 'YP.QIS.ORT_SAMPLE_TYPE',
      required: true,
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.expectCompleteTime`).d('期望完成时间'),
      name: 'expectCompleteTime',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgencyDegree`).d('紧急程度'),
      name: 'urgencyDegree',
      lookupCode: 'YP.QIS.ORT_URGENCY_DEGREE',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
      name: 'projectStage',
      lookupCode: 'YP.QIS.ORT_PROJECT_STAGE',
      required: true,
    },

    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.internalProductFlag`).d('是否厂内电芯'),
      name: 'internalProductFlag',
      lookupCode: 'YP.QIS.Y_N',
      trueValue: 'Y',
      falseValue: 'N',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingMonth`).d('送样抽样月份'),
      name: 'samplingMonth',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      valueField: 'prodLineId',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('internalProductFlag') === 'Y';
        },
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ratedCapacity`).d('额定容量'),
      name: 'ratedCapacity',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResultDemand`).d('测试结果要求'),
      name: 'inspectResultDemand',
      lookupCode: 'YP.QIS.ORT_INSPECT_RESULT_DEMAND',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.withoutMaterialCode`).d('外场物料编码'),
      name: 'withoutMaterialCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sampleReceiveBy`).d('收样人'),
      name: 'sampleReceiveByLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'sampleReceiveBy',
      bind: 'sampleReceiveByLov.userId',
    },
    {
      name: 'sampleReceiverName',
      bind: 'sampleReceiveByLov.realName',
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sampleReceiveDate`).d('收样时间'),
      name: 'sampleReceiveDate',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('测试报告'),
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      readOnly: type === 'list',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      name: 'remark',
    },
    {
      name: 'inspectSchemeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
      lovCode: 'YP.QIS.SCHEME',
      lovPara: { tenantId },
      // valueField: 'inspectSchemeId',
      // textField: 'inspectSchemeCode',
      computedProps: {
        disabled: ({ record }) => {
          return !record.get('siteId') || !record.get('materialId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
          };
        },
      },
    },
    {
      name: 'ortSchemeId',
      bind: 'inspectSchemeLov.inspectSchemeId',
    },
    {
      name: 'ortSchemeCode',
      bind: 'inspectSchemeLov.inspectSchemeCode',
    },
    {
      name: 'ortSchemeDesc',
      bind: 'inspectSchemeLov.inspectSchemeDesc',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.chooseItem`).d('选择项目'),
      name: 'batchAddLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'QIS.ORT.TEST.ITEM',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      multiple: true,
      dynamicProps: {
        // disabled: ({ record }) => {
        //   return !record.get('siteId') || !record.get('materialId');
        // },
        disabled: ({ record }) => {
          return !record.get('ortSchemeId');
        },
        lovPara: ({ record, dataSet }) => {
          const inspectItemIds: any = [];
          dataSet?.children?.tableData?.forEach(childrenRecord => {
            if (childrenRecord.get('inspectItemId')) {
              inspectItemIds.push(childrenRecord.get('inspectItemId'));
            }
          });
          return {
            tenantId,
            schemeId: record.get('ortSchemeId'),
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
            inspectItemIds,
          };
        },
      },
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => {
        return ['NEW', 'REJECTED'].includes(record.get('inspectDocStatus'));
      },
    },
  },
});

// 详情-ORT测试申请信息ds
const childrenDS = (type): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: type === 'detail' ? DataSetSelection.multiple : false,
  primaryKey: '_uuid',
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-apply/query/line/list/ui`,
        method: 'get',
      };
    },
  },
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItem`).d('测试项目名称'),
      name: 'inspectItem',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('inspectItemId');
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('测试方法'),
      name: 'inspectMethod',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('inspectItemId');
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardRequirement`).d('标准要求'),
      name: 'standardRequirement',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('inspectItemId');
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectFrequency`).d('测试频率'),
      name: 'inspectFrequency',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('inspectItemId');
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsourceFlag`).d('委外标识'),
      name: 'outsourceFlag',
      lookupCode: 'YP.QIS.Y_N',
      required: true,
      trueValue: 'Y',
      falseValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('inspectItemId');
        },
      },
    },
    {
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.inspectQty`).d('测试数量'),
      name: 'inspectQty',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.exSampleSolveMethod`).d('预期样本处理'),
      name: 'exSampleSolveMethod',
      lookupCode: 'YP.QIS.ORT_SAMPLE_SAVE_METHOD',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.acSampleSolveMethod`).d('实际样本处理'),
      name: 'acSampleSolveMethod',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('inspectItemId');
        },
      },
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      readOnly: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemId');
        },
      },
    },
  ],
});

export { headerDS, childrenDS };
