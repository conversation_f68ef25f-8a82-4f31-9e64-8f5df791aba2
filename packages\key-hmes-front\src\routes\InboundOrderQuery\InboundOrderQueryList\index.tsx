/**
 * @feature 入库单查询功能-入口页面
 * @date 2022-01-03
 * <AUTHOR>
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Dropdown, Menu, Modal, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
// import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
import { Collapse } from 'choerodon-ui';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TemplatePrintButton } from '../../../components/tarzan-ui';
import { drawerTableDS, headerTableDS, lineTableDS } from '../stories/EntranceDS';
import styles from './index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inbound.inboundOrderQuery';

function isAllEqualWithKeyWord(array: string[], keyWord: string) {
  const cancelList: string[] = ['NEW', 'RELEASED']; // 勾选的都是新建/下达状态，状态变更可点击，且下拉显示取消
  const closeList: string[] = ['NEW', 'CANCEL', 'RELEASED']; // 不包含新建、取消、已下达，则状态变更可点击，且下拉显示关闭
  if (array.length > 0) {
    if (keyWord === 'CANCEL') {
      return array.some(value => {
        return cancelList.indexOf(value) === -1;
      });
    }
    if (keyWord === 'CLOSED') {
      return array.some(value => {
        return closeList.indexOf(value) !== -1;
      });
    }
  } else {
    return false;
  }
}

const InboundOrderQueryList = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedInstructionDocIds, setSelectedInstructionDocIds] = useState<any[]>([]);
  // 动态列实际columns
  const drawerTableDs: DataSet = useMemo(() => new DataSet(drawerTableDS()), []);

  const { run: handleChangeStatus, loading: changeStatusLoading } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-storage-document/status/update/ui`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );
  let _copyDrawer;

  // 当在页签上右键刷新时，如果当前表格有勾选数据时，需要随之变按钮禁用状态
  useEffect(() => {
    handleDataSetSelectUpdate();
  }, []);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'batchSelect', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'batchUnSelect', handleDataSetSelectUpdate);
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    handleDataSetSelectUpdate();
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable(null);
    }
  };

  const headerRowClick = record => {
    queryLineTable(record?.toData()?.instructionDocId);
  };

  // 行列表数据查询
  const queryLineTable = instructionDocId => {
    if (instructionDocId) {
      lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
    } else {
      lineTableDs.setQueryParameter('instructionDocId', 0);
    }
    lineTableDs.query();
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _selectedStatus: string[] = [];
    const _instructionDocIds: string[] = [];
    headerTableDs.selected.forEach(item => {
      const { instructionDocStatusCode, instructionDocId } = item.toData();
      _instructionDocIds.push(instructionDocId);
      if (_selectedStatus.indexOf(instructionDocStatusCode) === -1) {
        _selectedStatus.push(instructionDocStatusCode);
      }
    });
    setSelectedInstructionDocIds(_instructionDocIds);
    setSelectedStatus(_selectedStatus);
  };

  // 点击状态变更的回调
  const clickMenu = async key => {
    const selectIds = headerTableDs.selected.map(item => item?.toData()?.instructionDocId);

    return handleChangeStatus({
      params: {
        instructionDocIds: selectIds,
        statusCode: key,
      },
    }).then(res => {
      if (res && res.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        notification.success({});
        setSelectedStatus([]);
        headerTableDs.clearCachedSelected();
        headerTableDs.query(headerTableDs.currentPage);
      } else {
        return Promise.resolve(false);
      }
    });
  };

  const drawerTableColumns: ColumnProps[] = [
    { name: 'identification', width: 150 },
    { name: 'description', width: 150 },
    { name: 'code', width: 150 },
    { name: 'primaryUomQty' },
    { name: 'uomCode' },
    { name: 'lot' },
    { name: 'toLocatorCode', width: 150 },
    { name: 'creationDate', width: 150, align: ColumnAlign.center },
    { name: 'createdByName' },
    { name: 'shelvesCreationDate', width: 150, align: ColumnAlign.center },
    { name: 'shelvesCreatedBy' },
  ];

  const goDetail = record => {
    drawerTableDs.setQueryParameter('instructionDocLineId', record.toData().instructionDocLineId);
    drawerTableDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.MATERIAL_LOT`);
    drawerTableDs.query();
    _copyDrawer = Modal.open({
      title: intl.get(`${modelPrompt}.title.queryDetail`).d('明细查看'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: customizeTable(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.MATERIAL_LOT`,
        },
        <Table dataSet={drawerTableDs} columns={drawerTableColumns} />,
      ),
      footer: (
        <Button
          style={{ float: 'right' }}
          onClick={() => {
            _copyDrawer.close();
          }}
        >
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  const headerTableColumns: ColumnProps[] = [
    { name: 'siteCode' },
    { name: 'instructionDocNum' },
    { name: 'instructionDocTypeDesc' },
    { name: 'instructionDocStatus' },
    { name: 'createdByName' },
    { name: 'creationTimes', align: ColumnAlign.center },
    { name: 'toLocatorCode' },
  ];

  // 行信息操作列配置
  const lineTableColumns: ColumnProps[] = [
    { name: 'lineNumber', lock: ColumnLock.left, align: ColumnAlign.left },
    {
      name: 'identifyType',
      lock: ColumnLock.left,
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
        return '';
      },
    },
    { name: 'lineStatusDesc', lock: ColumnLock.left },
    { name: 'materialCode', lock: ColumnLock.left, width: 150 },
    { name: 'revisionCode', lock: ColumnLock.left },
    { name: 'materialName', width: 150 },
    { name: 'quantity' },
    { name: 'uomCode' },
    { name: 'firstExecutedQty' },
    { name: 'secondExecutedQty' },
    { name: 'locatorCode', width: 150 },
    { name: 'workOrderNum', width: 150 },
    { name: 'prodLineCode', width: 150 },
    { name: 'soNumber' },
    { name: 'soLineNum' },
    {
      header: intl.get(`${modelPrompt}.column.operation`).d('操作列'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goDetail(record)}>
              {intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细')}
            </a>
          </span>
        );
      },
    },
  ];

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item disabled={isAllEqualWithKeyWord(selectedStatus, 'CANCEL')} key="CANCEL">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CANCEL')}>
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={isAllEqualWithKeyWord(selectedStatus, 'CLOSED')} key="CLOSED">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CLOSED')}>
          {intl.get(`${modelPrompt}.button.close`).d('关闭')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const onFieldEnterDown = () => {
    headerTableDs.query(props.headerTableDs.currentPage);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.inboundOrderQuery`).d('入库单查询')}>
        <Dropdown
          overlay={menu}
          placement={Placements.bottomRight}
          disabled={
            selectedStatus.length === 0 ||
            (isAllEqualWithKeyWord(selectedStatus, 'CLOSED') &&
              isAllEqualWithKeyWord(selectedStatus, 'CANCEL'))
          }
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={
              selectedStatus.length === 0 ||
              (isAllEqualWithKeyWord(selectedStatus, 'CLOSED') &&
                isAllEqualWithKeyWord(selectedStatus, 'CANCEL'))
            }
            loading={changeStatusLoading}
            permissionList={[
              {
                code: `${path}.button.changeStatus`,
                type: 'button',
                meaning: '列表页-状态变更按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        {/* <FRPrintButton
          kid="PRODUCT_DOC"
          queryParams={selectedInstructionDocIds}
          disabled={!selectedInstructionDocIds.length}
          printObjectType="INSTRUCTION_DOC"
        /> */}
        <TemplatePrintButton
          disabled={!selectedInstructionDocIds.length}
          printButtonCode='IN_BOUND_ORDER_QUERY'
          printParams={{ docId: selectedInstructionDocIds.join(',') }}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.HEAD`,
          },
          <Table
            searchCode="rkdcx1"
            customizedCode="rkdcx1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            showCachedSelection={false}
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            {lineTableDs && (
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.LINE`,
                },
                <Table
                  customizedCode="rkdcx2"
                  dataSet={lineTableDs}
                  highLightRow={false}
                  columns={lineTableColumns}
                />,
              )
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.inbound.inboundOrderQuery', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.HEAD`, `${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.LINE`, `${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.MATERIAL_LOT`],
  }),
)(InboundOrderQueryList);
