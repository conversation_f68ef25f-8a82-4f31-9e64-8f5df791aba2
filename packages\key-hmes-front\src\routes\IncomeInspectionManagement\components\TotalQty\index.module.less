.container {
  height: 100%;
  padding: 0 12px;
}

.space-total-count-material {
  display: flex;
  justify-content: left;
  width: 100%;
  height: 70%;
  padding-top: 1%;

  .space-total-count-item-material {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 13%;
    height: 100%;
    color: #65ffff;
    font-size: 12px;
    background: url('../../assets/SpaceTotalCountBg.png') center center no-repeat;
    background-position: center;
    background-size: contain;
    padding-left: 1%;
    margin-bottom: 1%;
  }

  .space-total-count-item-pad-material {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 13%;
    height: 100%;
    color: #339ac7;
    font-weight: bold;
    font-size: 32px;
    background: url('../../assets/SpaceTotalCountBg.png') center center no-repeat;
    background-position: center;
    background-size: contain;
    margin-right: 1%;
    margin-bottom: 1%;
  }
}

.space-total-process-material {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 20%;
}

.item-center-top{
  background-image: url('../../assets/top1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-bottom: 1% ;
}

.item-center-bottom{
  background-image: url('../../assets/top2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}