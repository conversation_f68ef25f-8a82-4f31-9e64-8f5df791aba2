.graphic-chart {
  height: 800px;
  overflow: auto;
  :global(.c7n-card-extra) {
    padding: 2px 0 0;

    i {
      margin-left: 8px;
      cursor: pointer;
    }

    i:hover {
      color: #4ad0df;
    }
  }

  :global {
    .page-content-wrap {
      height: calc(100% - 16px);

      & > .page-content {
        height: 100%;
      }
    }
  }
}

.cpk {
  display: flex;
  .cpkLeft {
    flex: 11;
    div {
      div {
        div:nth-child(2) {
          display: none;
        }
      }
    }
  }
  .cpkRight {
    flex: 5;
  }
}
