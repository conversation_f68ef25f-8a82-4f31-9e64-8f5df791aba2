/**
 * @Description: 站点维护DS
 * @Author: <<EMAIL>>
 * @Date: 2021-02-02 15:48:05
 * @LastEditTime: 2023-05-18 11:26:46
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.model.org.site';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  selection: false,
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
    },
    {
      name: 'siteType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=ORGANIZATION_REL_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.siteType`).d('站点类型'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'plantCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.plantCode`).d('ERP站点编码'),
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
    },
    {
      name: 'siteTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteType`).d('站点类型'),
    },
    {
      name: 'plantCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.plantCode`).d('ERP站点编码'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-site/query/ui`,
        method: 'get',
      };
    },
  },
});

const siteBasicInfoDS = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.intl,
      required: true,
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
    },
    {
      name: 'siteType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=ORGANIZATION_REL_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
      label: intl.get(`${modelPrompt}.siteType`).d('站点类型'),
      dynamicProps: {
        disabled({ record }) {
          return record.data.siteId;
        },
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'plantCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.plantCode`).d('ERP站点编码'),
    },
    {
      name: 'country',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.country`).d('国家'),
    },
    {
      name: 'province',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.province`).d('省'),
    },
    {
      name: 'city',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.city`).d('城市'),
    },
    {
      name: 'county',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.county`).d('县'),
    },
    {
      name: 'address',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.address`).d('详细地址'),
    },
  ],
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.model.domain.entity.MtModSite';
      return {
        data: { siteId: record.data.siteId },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_MODEL}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
});

const siteManufacturingDS = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  fields: [
    {
      name: 'attritionCalculateStrategy',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=ATTRITION_STRATEGY`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.attritionCalculateStrategy`).d('损耗计算策略'),
    },
    {
      name: 'ncrCreateFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      label: intl.get(`${modelPrompt}.ncrCreateFlag`).d('不良记录单创建标识'),
    },
    {
      name: 'compWarehousingPrioStrty',
      type: FieldType.object,
      // label: intl.get(`${modelPrompt}.compWarehousingPrioStrty`).d('完工入库优先级'),
    },
    {
      name: 'compWarehousingPrioStrtySeq',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.compWarehousingPrioStrty`).d('完工入库优先级'),
      multiple: true,
    },
  ],
});

const siteScheduleDS = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  fields: [
    {
      name: 'siteType', // 给装配清单策略和工艺路线策略判断是否必输
    },
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划滚动开始时间'),
    },
    {
      name: 'forwardPlanningTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.forwardPlanningTimeFence`).d('顺排时间栏'),
      min: 0,
    },
    {
      name: 'orderTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.orderTimeFence`).d('提前顶层下达时间'),
      min: 0,
    },
    {
      name: 'demandTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.demandTimeFence`).d('需求时间栏'),
      min: 0,
    },
    {
      name: 'releaseTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.releaseTimeFence`).d('下达时间栏'),
      min: 0,
    },
    {
      name: 'frozenTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.frozenTimeFence`).d('冻结时间栏'),
      min: 0,
    },
    {
      name: 'fixTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.fixTimeFence`).d('固定时间栏'),
      min: 0,
    },
    {
      name: 'schedulingNode',
      type: FieldType.string,
      lookupCode: 'MT.SCHEDULING_NODE',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      label: intl.get(`${modelPrompt}.schedulingNode`).d('排程节点'),
    },
    {
      name: 'orgSelectionAlgorithm',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&type=orgSelectionList&typeGroup=PROD_LINE_RULE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.orgSelectionAlgorithm`).d('资源选择策略'),
    },
    {
      name: 'rollingPeriod',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.rollingPeriod`).d('计划滚动周期'),
      min: 0,
    },
    {
      name: 'forceCompleteCycle',
      type: FieldType.string,
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupCode: 'MT.FORCE_COMPLETE_CYCLE',
      label: intl.get(`${modelPrompt}.forceCompleteCycle`).d('强制完成周期'),
    },
    {
      name: 'schedulingAlgorithm',
      type: FieldType.string,
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupCode: 'MT.BASIC_ALGORITHM',
      label: intl.get(`${modelPrompt}.schedulingAlgorithm`).d('排程策略'),
    },
    {
      name: 'bomSourceSiteType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      // required: true,
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&type=bomTypeList&typeGroup=SOURCE_SITE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        required({ record }) {
          return record.data.siteType === 'SCHEDULE';
        },
      },
      label: intl.get(`${modelPrompt}.bomSourceSiteType`).d('装配清单策略'),
    },
    {
      name: 'routerSourceSiteType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&type=routerTypeList&typeGroup=SOURCE_SITE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        required({ record }) {
          return record.data.siteType === 'SCHEDULE';
        },
      },
      // dynamicProps: ({ record }) => {
      //   if (record.get('siteType') === 'SCHEDULE') {
      //     return {
      //       required: true,
      //     };
      //   }
      //   return {
      //     required: false,
      //   };
      // },
      label: intl.get(`${modelPrompt}.routerSourceSiteType`).d('工艺路线策略'),
    },
  ],
});

const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'nowDate',
      type: FieldType.number,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-site/record/query/ui`,
        method: 'get',
      };
    },
    submit: ({ dataSet }) => {
      const { site, siteSchedule, siteManufacturing } = dataSet.children;
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-site/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SITE_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.SITE`,
        data: {
          site: site.toData()[0],
          siteSchedule: siteSchedule.toData()[0],
          siteManufacturing: siteManufacturing.toData()[0],
          insertFlag: dataSet.records[0].get('insertFlag'),
        },
        method: 'POST',
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
});

export { tableDS, detailDS, siteBasicInfoDS, siteManufacturingDS, siteScheduleDS };
