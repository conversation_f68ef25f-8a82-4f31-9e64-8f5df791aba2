import { DataSet } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.commonConfig.orderExecuteRule';

const onPassageLocatorTypeOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'LOCATOR_TYPE', tenantId },
      };
    },
  },
});

const onPassageLocatorDirectionOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'LOCATOR_DIRECTION', tenantId },
      };
    },
  },
});

const instructionCreateModeOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_CREATE_MODE', tenantId },
      };
    },
  },
});

const instructionDocExeStrategyOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_DOC_EXE_STRATEGY', tenantId },
      };
    },
  },
});

const statusOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_DOC_EXE_STRATEGY', tenantId },
      };
    },
  },
});

const typeOptionsDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_TOLERANCE_TYPE', tenantId },
      };
    },
  },
});

const strategyOptionsDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_EXE_STRATEGY', tenantId },
      };
    },
  },
});

const accountCategoryOptionsDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/limit-group-list/combo-box/ui`,
        method: 'GET',
        params: {
          typeGroupList: ['ACCOUNT_CATEGORY_C', 'ACCOUNT_CATEGORY_O'].toString(),
          tenantId,
        },
      };
    },
  },
});

const enableFlagDs = new DataSet({
  data: [
    { value: 'Y', meaning: intl.get(`${modelPrompt}.enableFlagY`).d('生效') },
    { value: 'N', meaning: intl.get(`${modelPrompt}.enableFlagN`).d('失效') },
  ],
});

const yesNoDs = new DataSet({
  data: [
    { value: 'Y', meaning: intl.get('tarzan.common.label.yes').d('是') },
    { value: 'N', meaning: intl.get('tarzan.common.label.no').d('否') },
  ],
});

export {
  onPassageLocatorTypeOptionDs,
  onPassageLocatorDirectionOptionDs,
  instructionCreateModeOptionDs,
  instructionDocExeStrategyOptionDs,
  statusOptionDs,
  typeOptionsDs,
  strategyOptionsDs,
  accountCategoryOptionsDs,
  enableFlagDs,
  yesNoDs,
};
