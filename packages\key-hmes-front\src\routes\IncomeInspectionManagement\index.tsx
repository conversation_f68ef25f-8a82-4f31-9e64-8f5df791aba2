import React, { useEffect, useState, useMemo } from 'react';
import moment from 'moment';
import { Spin } from 'choerodon-ui';
import { DataSet, Lov } from 'choerodon-ui/pro';
import { Content } from 'components/Page';
import { FullScreenContainer } from '@jiaminghi/data-view-react';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from "utils/intl";
import {FieldIgnore, FieldType} from "choerodon-ui/pro/lib/data-set/enum";

import styles from './index.module.less';

import DailyDelivery from './components/DailyDelivery';
import TotalQty from './components/TotalQty';
import NgQty from './components/NgQty';
import EmergencyTask from './components/EmergencyTask';
import CheckSupplier from './components/CheckSupplier';
import NgReport from './components/NgReport';


const modelPrompt = 'hmes.incomeInspectionManagement';
const tenantId = getCurrentOrganizationId();

// 原材料超期报检信息查询
const emergencyTaskUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/material-overdue`;

// 来料检验不合格信息
const checkSupplierUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/material-inspect-unQualified`;

// 来料检验合格信息
const ngReportUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/material-inspect-qualified`;

const CurrentTime = () => {
  const [nowTime, setNowTime] = useState(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
  useEffect(() => {
    const timer = setInterval(() => {
      return setNowTime(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
    }, 1000)

    return () => {  // 每次卸载都执行此函数，清除定时器
      clearInterval(timer)
    }
  }, []);
  return <span style={{ color: '#36C5FF', fontSize: 18, fontWeight: 600, marginLeft: '3%', marginBottom: '5%' }}> {nowTime} </span>;
};

// const Main = ({ topFilterFormDs, filterValue, setFilterValue }) => {
const Main = ({ isFullScreen }) => {
  const [taskData, setTaskData] = useState([]);
  const [checkData, setCheckData] = useState([]);
  const [ngData, setNgData] = useState([]);
  const [materialId, setMaterialId] = useState();
  const [timers, setTimers] = useState<any>(null);

  const lovDataSet = useMemo(() => new DataSet({
    autoCreate: true,
    fields: [
      {
        name: 'materialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
        ignore: FieldIgnore.always,
        lovCode: 'MT.MATERIAL',
        textField: 'materialName',
        lovPara: tenantId,
        // dynamicProps: {
        //   lovPara: () => ({
        //     tenantId,
        //   }),
        // },
      },
      {
        name: 'materialCode',
        bind: 'materialLov.materialCode',
      },
      {
        name: 'materialId',
        bind: 'materialLov.materialId',
      },
    ],
  }), [])

  const [initLoading, setInitLoading] = useState<boolean>(false);


  useEffect(() => {
    getTimers();
    getTaskData();
    getCheckData();
    getNgData();
  }, []);

  const getTimers = async () => {
    const url = `/hpfm/v1/${tenantId}/lovs/value/batch?QMS.MANAGEMENT_FREQUENCY=QMS.MANAGEMENT_FREQUENCY`
    const result = await request(url, {
      method: 'GET',
    });
    const data = result['QMS.MANAGEMENT_FREQUENCY'].filter(item => item.value === 'INCOME')
    if (data.length > 0) {
      setTimers(Number(data[0]?.meaning))
    }
  }

  useEffect(() => {
    let time
    if(timers) {
      time = setInterval(() => {
        getTaskData();
        getCheckData();
        getNgData();
      }, timers * 60000)
    }
    return () => {
      clearInterval(time)
    }
  }, [timers])

  useEffect(() => {
    lovDataSet.addEventListener("update", handleChangeMaterialId);
    return () => {
      lovDataSet.removeEventListener("update", handleChangeMaterialId);
    };
  }, []);

  const handleChangeMaterialId = ({ name, value }) => {
    if (name === "materialLov") {
      setMaterialId(value?.materialId);
      getTaskData();
      getCheckData();
      getNgData();
    }
  };

  /**
   * 原材料超期报检信息查询
   */
  const getTaskData = async () => {
    setInitLoading(true);
    const result = await request(emergencyTaskUrl, {
      method: 'GET',
      query: { materialId: lovDataSet.current?.get('materialId') },
    });
    setTaskData(result || []);
    setInitLoading(false);
  };

  /**
   * 交验不合格Top10供应商
   */
  const getCheckData = async () => {
    const result = await request(checkSupplierUrl, {
      method: 'GET',
      query: { materialId: lovDataSet.current?.get('materialId') },
    });
    setCheckData(result || []);
  };
  /**
   * 不合格信息滚动播报
   */
  const getNgData = async () => {
    const result = await request(ngReportUrl, {
      method: 'GET',
      query: { materialId: lovDataSet.current?.get('materialId') },
    });
    setNgData(result || []);
  };

  return (
    <>
      <Content style={{ padding: 0, margin: 0, height: '100%' }}>
        {/* loading */}
        {initLoading && <Spin spinning={initLoading} className={styles['center-loading']} />}
        <div className={styles['dashboard-container']}>
          {/* header */}
          <div className={styles['dashboard-title']}>
            <div className={styles['dashboard-title-left']}>
              <div style={{
                width: '35%',
                height: '100%',
                marginLeft: '2%',
                color: '#65ffff',
                fontWeight: 'bold',
                fontSize: '32px',
                letterSpacing: '0.1em',
              }} >
                安普瑞斯
              </div>
              <CurrentTime />
            </div>
            <div className={styles['dashboard-title-center']}>原材料管理看板</div>
            <div className={styles['dashboard-title-right']}>
              <Lov dataSet={lovDataSet}
                name="materialLov"
                placeholder={intl.get(`${modelPrompt}.placeholder.materialLov`).d('物料')} />
            </div>
          </div>

          {/* Content */}
          <div className={styles['dashboard-content']}>
            {/* 第一列 */}
            <div className={styles['dashboard-col-side']}>
              {/* 日到货单数趋势图 */}
              <div className={styles['dashboard-item-left-top']}>
                <DailyDelivery materialId={materialId} timers={timers}/>
              </div>
              {/* 原材料超期报检信息 */}
              <div className={styles['dashboard-item-left-bottom']}>
                <EmergencyTask data={taskData} />
              </div>
            </div>
            {/* 第二列 */}
            <div className={styles['dashboard-col-center']}>
              {/* 检验合格率趋势图 */}
              <div className={styles['dashboard-item-center-top']}>
                <TotalQty materialId={materialId} timers={timers}/>
              </div>
              {/* 来料检验不合格信息 */}
              <div className={styles['dashboard-item-center-bottom']}>
                <CheckSupplier data={checkData} />
              </div>
            </div>
            {/* 第三列 */}
            <div className={styles['dashboard-col-side']}>
              {/* 当日检验合格笔数/不合格笔数饼图 */}
              <div className={styles['dashboard-item-left-top']}>
                <NgQty isFullScreen={isFullScreen} materialId={materialId} timers={timers}/>
              </div>
              {/* 来料检验合格信息 */}
              <div className={styles['dashboard-item-left-bottom']}>
                <NgReport data={ngData} />
              </div>
            </div>

          </div>
        </div>
      </Content>
    </>
  );
};

const IncomeInspectionManagement = () => {
  const [isFullScreen, setIsFullScreen] = useState(false); // 是否全屏

  const windowFullScreenChange = () => {
    if (document.fullscreenElement) {
      setIsFullScreen(true);
    } else {
      setIsFullScreen(false);
    }
  };
  useEffect(() => {
    document.addEventListener('fullscreenchange', windowFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', windowFullScreenChange);
    };
  }, []);

  return (
    <>
      <div className={styles['screen-container']}>
        {isFullScreen ? (
          <FullScreenContainer>
            <Main
              isFullScreen={isFullScreen}
            />
          </FullScreenContainer>
        ) : (
          <Main
            isFullScreen={isFullScreen}
          />
        )}
      </div>
    </>
  );
};
export default IncomeInspectionManagement;
