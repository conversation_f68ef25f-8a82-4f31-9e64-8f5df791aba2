/**
 * @Description: 物料批管理平台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-1-25 14:38:56
 * @LastEditTime: 2023-05-18 15:34:27
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import {
  DataSet,
  Table,
  Button,
  Modal,
  TextField,
  Lov,
  Icon,
  DateTimePicker,
  Row,
  Col,
  Form,
  // Dropdown,
  // Menu,
  // ModalProvider,
  // useModal,
  Select,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import ExcelExport from 'components/ExcelExport';
import { observer } from 'mobx-react';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { Badge } from 'choerodon-ui';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { TemplatePrintButton } from '../../../components/tarzan-ui';
import withProps from 'utils/withProps';
import { flow, isNil } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import myInstance from '@utils/myAxios';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST, BASIC } from '@utils/config';
import notification from 'utils/notification';
import {openTab} from "utils/menuTab";
import queryString from "query-string";
// import request from 'utils/request';
// import { getResponse } from '@utils/utils';
import { tableDS, historyDS, printInfoDS } from './stores/ListTable';
import HistoryDrawer from './HistoryDrawer';
// import ModalContent from './ModalContent';
import { FetchDynamicColumn } from './services';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import LovModal from '@/components/BatchInput/LovModal';
import './index.module.less';


const modelPrompt = 'tarzan.hmes.product.MaterialLotTraceMes';
const tenantId = getCurrentOrganizationId();

let TableHeight = 600;
let printerModal;
const { Option } = Select;



const MaterialLotTrace = observer((props: any) => {
  const { tableDs, customizeTable } = props;

  useEffect(() => {
    setTimeout(() => {
      TableHeight = window.screen.height - 450;
    }, 1000);
  }, []);

  const historyDs = useMemo(() => new DataSet(historyDS()), []);
  const printInfoDs = useMemo(() => new DataSet(printInfoDS()), []);

  const fetchDynamicColumn = useRequest(FetchDynamicColumn('mt_material_lot_attr'));

  const [selectedMaterialLotList, setSelectedMaterialLotList] = useState<any[]>([]);
  const [printArr, setPrintArr] = useState<any[]>([]);
  const [printStatus, setPrintStatus] = useState(false);
  const [editing, setEditing] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.state || {}).length === 0 && props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      tableDs.query(props.tableDs.currentPage);
      return;
    }
    if (Object.keys(props?.location?.state || {}).length !== 0 && !props?.location?.state?._back) {
      // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
      const {
        siteId,
        siteCode,
        materialId,
        materialCode,
        revisionCode,
        lotCode,
        qualityStatus,
        ownerType,
        ownerId,
        ownerCode,
        locatorInfo,
      } = props?.location?.state || {};
      const queryParams = {
        siteLov: siteId ? { siteId, siteCode } : undefined,
        materialLov: materialId ? { materialId, materialCode } : undefined,
        revisionCodes: revisionCode && revisionCode.length ? [revisionCode] : undefined,
        lotList: lotCode && lotCode.length ? [lotCode] : undefined,
        qualityStatus,
        ownerType,
        ownerLov: ownerId
          ? { soLineId: ownerId, customerId: ownerId, soNumContent: ownerCode }
          : undefined,
        locatorLov: [locatorInfo] || null,
        enableFlag: "Y",
      };
      tableDs.queryDataSet.loadData([queryParams]);
      setTimeout(() => {
        tableDs.query();
      }, 200);
    }
  }, [props?.location?.state]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
      tableDs.clearCachedRecords();
    };
  });

  // 获取打印模板
  useEffect(() => {
    if (!printStatus) {
      const url = `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-print-functions/print/template/list/ui?printButtonCode=MATERIAL_LOT`;
      myInstance.get(url).then(res => {
        if (res.data.success) {
          setPrintArr(res.data.rows?.printTemplateGroupList);
          setPrintStatus(true);
        } else if (res.data.message) {
          notification.error({
            description: res.data.message,
          });
        }
      });
    }
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (tableDs) {
      const handerQuery = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
      handler.call(tableDs, 'load', handleDataSetSelectUpdate);
      // 头选中和撤销选中事件
      handler.call(tableDs, 'select', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelect', handleDataSetSelectUpdate);
      handler.call(tableDs, 'selectAll', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelectAll', handleDataSetSelectUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', {});
    }
    if (name === 'reservedObjectType') {
      record.set('reservedObjectLov', {});
    }
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _materialLotList: string[] = [];
    tableDs.selected.forEach(item => {
      const { materialLotId } = item.toData();
      _materialLotList.push(materialLotId);
    });
    setSelectedMaterialLotList(_materialLotList);
  };

  const [dynamicColumns, setDynamicColumns] = useState<ColumnProps[]>([]);

  useEffect(() => {
    const dynamicColumn: ColumnProps[] = [];
    ((fetchDynamicColumn.data || {}).content || []).forEach(item => {
      if (item.enableFlag === 'Y') {
        dynamicColumn.push({
          header: item.attrMeaning,
          name: item.attrName,
          align: ColumnAlign.left,
          width: 150,
          renderer: ({ record }) => {
            return (record?.get('attrMap') || {})[item.attrName];
          },
        });
      }
    });
    setDynamicColumns(dynamicColumn);
  }, [fetchDynamicColumn.data]);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        align: ColumnAlign.left,
        width: 250,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/apex-hmes/product/material-lot-traceability/detail/${record?.get('materialLotId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'materialLotCode',
        width: 250,
      },
      {
        name: 'materialCode',
        width: 250,
      },
      {
        name: 'bomCode',
        width: 150,
      },
      {
        name: 'revisionCode',
        width: 100,
      },
      {
        name: 'materialDesc',
        width: 250,
      },
      {
        name: 'primaryUomQty',
        align: ColumnAlign.right,
        width: 100,
        renderer: ({ record }) => {
          const primaryUomQty = record?.get('primaryUomQty');
          return parseFloat(
            String(Number(primaryUomQty).toFixed(Number(record?.get('decimalNumber')))),
          );
        },
      },
      {
        name: 'primaryUomCode',
        width: 120,
      },
      {
        name: 'wareHouseCode',
        width: 250,
      },
      {
        name: 'locatorCode',
        width: 250,
      },
      {
        name: 'lot',
        width: 100,
      },
      {
        name: 'productionDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'expirationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'extendedShelfLifeTimes',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'supplierLot',
        width: 100,
      },
      {
        name: 'secondaryUomQty',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'secondaryUomCode',
        width: 120,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'qualityStatusDesc',
        width: 120,
      },
      {
        name: 'createReasonDesc',
        width: 150,
      },
      {
        name: 'inLocatorTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'inSiteTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'siteCode',
        width: 250,
      },
      {
        name: 'eoNum',
        width: 150,
      },
      {
        name: 'ownerTypeDesc',
        width: 150,
        renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
      },
      {
        name: 'ownerCode',
        width: 250,
      },
      {
        name: 'ownerDesc',
        width: 250,
      },
      {
        name: 'supplierCode',
        width: 250,
      },
      {
        name: 'supplierDesc',
        width: 250,
      },
      {
        name: 'supplierSiteCode',
        width: 250,
      },
      {
        name: 'supplierSiteDesc',
        width: 250,
      },
      {
        name: 'customerCode',
        width: 250,
      },
      {
        name: 'customerDesc',
        width: 250,
      },
      {
        name: 'customerSiteCode',
        width: 250,
      },
      {
        name: 'customerSiteDesc',
        width: 250,
      },
      {
        name: 'reservedFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'reservedObjectTypeDesc',
        width: 120,
      },
      {
        name: 'reservedObjectCode',
        width: 250,
      },
      {
        name: 'assembleToolCode',
        width: 250,
      },
      {
        name: 'assemblePointCode',
        width: 250,
      },
      {
        name: 'unloadTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'loadTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'ovenNumber',
        width: 250,
      },
      {
        name: 'overOrderInterceptionFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'freezeFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'stocktakeFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'materialLotStatus',
        width: 150,
      },
      {
        name: 'instructionDocNum',
        width: 150,
      },
      {
        name: 'instructionNum',
        width: 250,
      },
      {
        name: 'currentContainerCode',
        width: 250,
      },
      {
        name: 'topContainerCode',
        width: 250,
      },
      {
        name: 'printTimes',
        width: 250,
        renderer: ({ value }) => value || 0,
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'createdUsername',
        width: 250,
      },
      {
        name: 'lastUpdateDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'lastUpdatedUsername',
        width: 250,
      },
      {
        name: 'equipmentCode',
        width: 250,
      },
      {
        name: 'equipmentName',
        width: 250,
      },
      {
        name: 'sourceMaterialLotCode',
        width: 150,
      },
    ];
  }, []);

  const handleCancel = () => {
    setEditing(false);
    tableDs.clearCachedSelected();
    tableDs.query(tableDs.currentPage);
  };
  const handleEdit = () => {
    setEditing(true);
  };
  const handleSave = async () => {
    await tableDs.submit();
    setEditing(false);
    tableDs.query(tableDs.currentPage);
  };

  const handleQueryMaterialLotsHistory = () => {
    historyDs.setQueryParameter('selectedMaterialLotList', selectedMaterialLotList);
    historyDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}</div>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/export/his/ui`}
            queryParams={{
              materialLotIds: selectedMaterialLotList,
            }}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        </div>
      ),
      destroyOnClose: true,
      children: <HistoryDrawer ds={historyDs} />,
    });
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    const {
      siteLov,
      wareHouseLov,
      locatorLov,
      materialLov,
      ownerLov,
      reservedObjectLov,
      eoLov,
      creatorLov,
      supplierLov,
      customerLov,
      currentContainerLov,
      topContainerLov,
      assembleToolLov,
      assemblePointLov,
      equipmentLov,
      ...other
    } = queryParmas;
    return {
      ...other,
      locatorIds: (queryParmas.locatorLov || []).map(item => item.locatorId).join(),
      wareHouseIds: (queryParmas.wareHouseLov || []).map(item => item.locatorId).join(),
      ownerType: queryParmas.ownerType === 'ALL' ? '' : queryParmas.ownerType,
      identifyType: 'MATERIAL_LOT',
      size: tableDs.pageSize,
      page: tableDs.currentPage - 1,
      idList: tableDs.selected.map(item => item.data.materialLotId),
    };
  };

  // const Print = () => {

  //   const Modal = useModal();

  //   const openModal = React.useCallback(() => {
  //     Modal.open({
  //       title: intl.get(`${modelPrompt}.printSelect`).d('打印模板选择'),
  //       children: <ModalContent modal={Modal} printArr={printArr} dataSet={tableDs}/>,
  //       okText: intl.get('tarzan.common.button.print').d('打印'),
  //       okProps: { disabled: true },
  //       autoCenter: true,
  //       style: {
  //         width: 680,
  //       },
  //     });
  //   }, [Modal]);

  //   return  <Button disabled={!selectedMaterialLotList.length} onClick={openModal}>
  //     {intl.get('tarzan.common.button.print').d('打印')}
  //   </Button>;
  // };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
      // handleSearch()
    }
  };
  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };
  const toggleForm = () => {
    setExpandForm(!expandForm);
  };
  const renderQueryBar = ({ buttons, queryDataSet, queryFields, dataSet }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identifications', '物料批标识')}
                    />
                  </div>
                }
              />
              <TextField
                name="materialLotCodes"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'materialLotCodes', '物料批编码')}
                    />
                  </div>
                }
              />
              <TextField name="lotList" />
              {expandForm && (
                <>
                  <Lov name="siteLov" />
                  <Lov name="wareHouseLov" />
                  <Lov name="locatorLov" />
                  <Lov name="materialLov" />
                  <TextField name="revisionCodes" />
                  <TextField name="bomCode" />
                  <TextField name="materialName" />
                  <Select name="enableFlag" />
                  <Select name="qualityStatus" />
                  <Select name="createReason" />
                  <Select name="ownerType" />
                  <Lov name="ownerLov" />
                  <Select name="reservedFlag" />
                  <Select name="reservedObjectType" />
                  <Lov name="reservedObjectLov" />
                  <Lov name="eoLov" />
                  <Lov name="supplierLov" />
                  <Lov name="customerLov" />
                  <Select name="materialLotStatus" />
                  <DateTimePicker name="inLocatorTimeStart" />
                  <DateTimePicker name="inLocatorTimeEnd" />
                  <Select name="stocktakeFlag" />
                  <DateTimePicker name="inSiteTimeStart" />
                  <DateTimePicker name="inSiteTimeEnd" />
                  <Select name="freezeFlag" />
                  <Lov name="currentContainerLov" />
                  <Lov name="topContainerLov" />
                  <TextField name="ovenNumber" />
                  <Lov name="assembleToolLov" />
                  <Lov name="assemblePointLov" />
                  <TextField name="supplierLotList" />
                  <Lov name="equipmentLov" />
                  <TextField name="workOrderNum" />
                  <Lov name="creatorLov" />
                  <TextField name="sourceMaterialLotCode" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                funcType={FuncType.link}
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                  queryDataSet.loadData([]);
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button onClick={handleSearch} color={ButtonColor.primary}>
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleSearch = async () => {
    // let {identifications} = tableDs.queryDataSet?.toJSONData()[0]
    tableDs.query();
  };
  // const handlePrint = async (selected, type) => {
  //   const result = await request(
  //     `${
  //       BASIC.HMES_BASIC
  //     }/v1/${getCurrentOrganizationId()}/mes-materialLot-print/print/pdf/${type}`,
  //     {
  //       method: 'POST',
  //       responseType: 'blob',
  //       body: selected,
  //     },
  //   );
  //   const res = getResponse(result);
  //   if (res) {
  //     if (res.type === 'application/json') {
  //       const fileReader: any = new FileReader();
  //       fileReader.onloadend = () => {
  //         const jsonData = JSON.parse(fileReader.result);
  //         // 普通对象，读取信息
  //         getResponse(jsonData);
  //       };
  //       fileReader.readAsText(res);
  //     } else {
  //       const file = new Blob([res], { type: 'application/pdf' });
  //       const fileURL = URL.createObjectURL(file);
  //       const newwindow = window.open(fileURL, 'newwindow');
  //       if (newwindow) {
  //         newwindow.print();
  //         notification.success({
  //           message: intl.get(`${modelPrompt}.notification.print.success`).d('打印成功'),
  //         });
  //       } else {
  //         notification.error({
  //           message: intl
  //             .get(`${modelPrompt}.notification.browser.config`)
  //             .d('当前窗口已被浏览器拦截，请手动设置浏览器！'),
  //         });
  //       }
  //     }
  //   }
  // };

  const selectPrinter = (printerList) => {
    printerModal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.info.printer`).d('请选择打印机'),
      destroyOnClose: true,
      closable: true,
      style: {
        width: 400,
      },
      children: (
        <React.Fragment>
          <Form>
            <Select
              clearButton={false}
              onChange={(value) => handlePrint(value)}
              placeholder={intl.get(`${modelPrompt}.info.printer`).d('请选择打印机')}
            >
              {printerList.map(i => (
                <Option key={i.value} value={i}>
                  {i.meaning}
                </Option>
              ))}
            </Select>
          </Form>

        </React.Fragment>
      ),
      footer: null,
    });
  };


  // 物料批打印
  const handlePrint = (printer) => {
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot/barcode-print`;
    const params = {
      materialLotIds: selectedMaterialLotList,
      badQuantity: printInfoDs.current?.get('badQuantity'),
      remark: printInfoDs.current?.get('remark'),
      printer,
    };
    myInstance
      .post(url, params)
      .then(res => {
        if (res.data.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.success.print`).d('打印成功!'),
          });
          tableDs.query();
          printerModal.close();
        } else {
          if (res.data.statusCode === "PRINTER_CHOOSE") {
            return selectPrinter(res.data.attr);
          }
          printerModal.close();
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.error.print`).d('打印失败!'),
          });
        }
      });
  };

  const printInfoModal = () => {
    printInfoDs.reset();
    Modal.open({
      title: intl.get(`${modelPrompt}.printInfo`).d('打印信息录入'),
      key: Modal.key(),
      closable: true,
      destroyOnClose: true,
      style: {
        width: 400,
      },
      children:(
        <Form columns={1} dataSet={printInfoDs} labelWidth={120}>
          <TextField name="badQuantity" />
          <TextField name="remark" />
        </Form>
      ),
      onOk: () => handlePrint(null),
    });
  };


  const handleBatchExport = () => {
    openTab({
      key: `/apex-hmes/product/material-lot-traceability/comment-import/MES.MATERIAL_LOT_TEMPLATE`,
      title: 'hzero.common.title.import',
      search: queryString.stringify({
        action: intl.get(`${modelPrompt}.mes.button.import`).d('物料批管理平台-MES导入'),
      }),
    });
  };

  // const menu = (
  //   <Menu style={{ width: '100px' }}>
  //     <Menu.Item key="a4">
  //       <a
  //         target="_blank"
  //         rel="noopener noreferrer"
  //         onClick={() => handlePrint(selectedMaterialLotList, 'a4')}
  //       >
  //         {intl.get(`${modelPrompt}.button.print.a4`).d('A4打印')}
  //       </a>
  //     </Menu.Item>
  //     <Menu.Item key="barcode">
  //       <a
  //         target="_blank"
  //         rel="noopener noreferrer"
  //         onClick={() => handlePrint(selectedMaterialLotList, 'barcode')}
  //       >
  //         {intl.get(`${modelPrompt}.button.print.tag`).d('标签打印')}
  //       </a>
  //     </Menu.Item>
  //   </Menu>
  // );

  return (
    <div className="hmes-style" style={{ height: '99%', overflow: 'auto' }}>
      <Header title={intl.get(`${modelPrompt}.materialLotMaintenanceMes`).d('物料批管理平台-MES')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => {
            props.history.push('/apex-hmes/product/material-lot-traceability/detail/create');
          }}
          permissionList={[
            {
              code: `/apex-hmes/product/material-lot-traceability/list.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button disabled={!selectedMaterialLotList.length} onClick={handleQueryMaterialLotsHistory}>
          {intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}
        </Button>
        <ExcelExport
          method="POST"
          exportAsync
          allBody
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        {/* <ModalProvider>
          <Print/>
        </ModalProvider> */}
        {/* <Dropdown overlay={menu} disabled={selectedMaterialLotList.length === 0}>
          <Button disabled={selectedMaterialLotList.length === 0}>
            {intl.get('tarzan.common.button.print').d('打印')}
          </Button>
        </Dropdown> */}
        <Button
          color={ButtonColor.primary}
          disabled={!selectedMaterialLotList.length}
          onClick={printInfoModal}
        >
          {intl.get(`${modelPrompt}.button.network.print`).d('网络打印')}
        </Button>
        <TemplatePrintButton
          name={intl.get(`${modelPrompt}.button.local.print`).d('本地打印')}
          disabled={!selectedMaterialLotList.length}
          printButtonCode='MT.SCARP_BARCODE_PRINT_LABEL'
          printParams={{ materialLotId: selectedMaterialLotList.join(',') }}
        />

        {editing ? (
          <>
            <Button onClick={handleSave} color={ButtonColor.primary}>
              {intl.get(`tarzan.common.button.save`).d('保存')}
            </Button>
            <Button onClick={handleCancel}>
              {intl.get(`tarzan.common.button.cancel`).d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            onClick={() => handleEdit()}
            permissionList={[
              {
                code: `/apex-hmes/product/material-lot-traceability/list.button.attribute`,
                type: 'button',
                meaning: '列表页-扩展属性修改',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.attr`).d('扩展属性修改')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `hmes.common.materialLot.maintenance.mes.button.export`,
              type: 'button',
              meaning: '导入',
            },
          ]}
          onClick={handleBatchExport}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.MES.MATERIAL_LOT.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.MES.MATERIAL_LOT.LIST`,
            columnEditorRender: () => editing,
          },
          <Table
            // @ts-ignore
            queryBar={renderQueryBar}
            searchCode="wlpglpt"
            customizedCode="wlpglpt"
            dataSet={tableDs}
            columns={columns.concat(dynamicColumns)}
            style={{ height: TableHeight }}
            headerRowHeight={30}
            rowHeight={28}
            footerRowHeight={20}
            virtual
            virtualCell
            pagination={{
              showPager: true, // 显示数字按钮
              pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
            }}
          />,
        )}

        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default flow(
  formatterCollections({ code: ['tarzan.hmes.product.MaterialLotTraceMes', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.MES.MATERIAL_LOT.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.MES.MATERIAL_LOT.LIST`,
    ],
  }),
)(MaterialLotTrace);
