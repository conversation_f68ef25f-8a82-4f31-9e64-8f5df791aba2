/**
 * @Description: 控制控制图维护-历史图形查询-表格
 * @Author: <<EMAIL>>
 * @Date: 2021-06-01 09:26:53
 * @LastEditTime: 2023-01-10 11:06:35
 * @LastEditors: <<EMAIL>>
 */

import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { PerformanceTable, Radio } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Tag } from 'choerodon-ui';

const modelPrompt = 'tarzan.hspc.chartInfo';

const Table = (props, ref) => {
  const { dataSource = [], graphicInfo = {}, activePorintIndex, onTableRowClick } = props;
  const { sampleType } = graphicInfo;

  useEffect(() => {
    tableRef.current.scrollTop(activePorintIndex * 30);
  }, [activePorintIndex]);

  const tableRef = useRef();

  useImperativeHandle(ref, () => ({
    getTableColumns: getColumns,
  }));

  const getColumns = () => {
    let dynamicColumns = [];
    let fixedColumns = [];
    if (sampleType === 'measure') {
      dynamicColumns = [
        {
          dataIndex: 'id',
          key: 'id',
          width: 50,
          align: 'center',
          fixed: true,
          // render: ({ rowData, dataIndex, rowIndex }) => {
          render: ({ rowIndex }) => {
            return (
              <Radio name="controlled" value={rowIndex} checked={activePorintIndex === rowIndex} />
            );
          },
        },
        {
          title: intl.get('tarzan.common.label.serialNumber').d('序号'),
          dataIndex: 'subgroupIndex',
          flexGrow: 1,
          align: 'center',
          render: ({ rowIndex }) => {
            return rowIndex;
          },
        },
      ];
      if (dataSource[0] && dataSource[0].subDataInfo) {
        dataSource[0].subDataInfo.forEach((_, index) => {
          dynamicColumns.push({
            key: `sampleValue${index}`,
            title: intl.get(`${modelPrompt}.sample`).d('样本') + (index + 1),
            dataIndex: `sampleValue${index}`,
            minWidth: 100,
            flexGrow: 1,
          });
        });
      }
      fixedColumns = [
        {
          title: intl.get(`${modelPrompt}.status`).d('状态'),
          flexGrow: 1,
          align: 'center',
          dataIndex: 'pointStatus',
          key: 'pointStatus',
          render: ({ rowData }) => {
            let tagClassName = '';
            if (rowData.pointStatusCode === 'NORMAL') {
              tagClassName = 'green';
              return <Tag color={tagClassName}>{intl.get(`${modelPrompt}.normal`).d('正常')}</Tag>;
            }
            if (rowData.pointStatusCode === 'ABNORMAL') {
              tagClassName = 'red';
              return (
                <Tag color={tagClassName}>{intl.get(`${modelPrompt}.abnormal`).d('失控')}</Tag>
              );
            }
            if (rowData.pointStatusCode === 'PROCESSED') {
              tagClassName = 'orange';
              return (
                <Tag color={tagClassName}>{intl.get(`${modelPrompt}.processed`).d('已处理')}</Tag>
              );
            }
          },
        },
        {
          title: intl.get(`${modelPrompt}.average`).d('平均值'),
          dataIndex: 'subgroupBar',
          key: 'subgroupBar',
          flexGrow: 1,
        },
        {
          title: intl.get(`${modelPrompt}.range`).d('极差值'),
          dataIndex: 'subgroupR',
          key: 'subgroupR',
          flexGrow: 1,
        },
        {
          title: intl.get(`${modelPrompt}.sigma`).d('标准差值'),
          dataIndex: 'subgroupSigma',
          key: 'subgroupSigma',
          flexGrow: 1,
        },
        {
          title: intl.get(`${modelPrompt}.median`).d('中位数'),
          dataIndex: 'subgroupMe',
          key: 'subgroupMe',
          flexGrow: 1,
        },
        {
          title: intl.get(`${modelPrompt}.max`).d('最大值'),
          dataIndex: 'subgroupMax',
          key: 'subgroupMax',
          flexGrow: 1,
        },
        {
          title: intl.get(`${modelPrompt}.min`).d('最小值'),
          dataIndex: 'subgroupMin',
          key: 'subgroupMin',
          flexGrow: 1,
        },
      ];
    } else {
      dynamicColumns = [
        {
          dataIndex: 'id',
          key: 'id',
          width: 50,
          align: 'center',
          fixed: true,
          // render: ({ rowData, dataIndex, rowIndex }) => {
          render: ({ rowIndex }) => {
            return (
              <Radio name="controlled" value={rowIndex} checked={activePorintIndex === rowIndex} />
            );
          },
        },
        {
          title: intl.get('tarzan.common.label.serialNumber').d('序号'),
          dataIndex: 'dataIndex',
          flexGrow: 1,
          align: 'center',
          render: ({ rowData }) => {
            return rowData.dataIndex + 1;
          },
        },
        {
          title: intl.get(`${modelPrompt}.sampleAttr`).d('样本属性'),
          flexGrow: 1,
          align: 'center',
          dataIndex: 'dataColumnBatch',
          key: 'dataColumnBatch',
        },
        {
          title: intl.get(`${modelPrompt}.sampleData`).d('样本数'),
          flexGrow: 1,
          align: 'center',
          dataIndex: 'sampleCount',
          key: 'sampleCount',
        },
        {
          title: intl.get(`${modelPrompt}.unqualified`).d('不合格数'),
          flexGrow: 1,
          align: 'center',
          dataIndex: 'unqualifiedQuantity',
          key: 'unqualifiedQuantity',
        },
        {
          title: intl.get(`${modelPrompt}.status`).d('状态'),
          flexGrow: 1,
          align: 'center',
          dataIndex: 'pointStatus',
          key: 'pointStatus',
          render: ({ rowData }) => {
            let tagClassName = '';
            if (rowData.pointStatusCode === 'NORMAL') {
              tagClassName = 'green';
              return <Tag color={tagClassName}>{intl.get(`${modelPrompt}.normal`).d('正常')}</Tag>;
            }
            if (rowData.pointStatusCode === 'ABNORMAL') {
              tagClassName = 'red';
              return (
                <Tag color={tagClassName}>{intl.get(`${modelPrompt}.abnormal`).d('失控')}</Tag>
              );
            }
            if (rowData.pointStatusCode === 'PROCESSED') {
              tagClassName = 'orange';
              return (
                <Tag color={tagClassName}>{intl.get(`${modelPrompt}.processed`).d('已处理')}</Tag>
              );
            }
          },
        },
        {
          title: intl.get(`${modelPrompt}.p/u`).d('不合格率/单位缺陷数'),
          flexGrow: 1,
          align: 'center',
          dataIndex: 'unqualifiedPercent',
          key: 'unqualifiedPercent',
        },
      ];
    }
    return dynamicColumns.concat(fixedColumns);
  };

  return (
    <PerformanceTable
      virtualized
      hover={false}
      rowKey="subgroupIndex"
      columns={getColumns()}
      height={dataSource.length < 12 ? (dataSource.length + 1.2) * 30 : 400}
      rowHeight={30}
      data={dataSource}
      ref={tableRef}
      onRowClick={data => {
        onTableRowClick(data.xTickLabel - 1);
      }}
    />
  );
};

export default forwardRef(Table);
