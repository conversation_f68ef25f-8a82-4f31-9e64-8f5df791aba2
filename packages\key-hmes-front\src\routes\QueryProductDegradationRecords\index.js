import React, { useState, useMemo } from 'react';
import {
  DataSet,
  Table,
  Button,
  Row,
  Col,
  TextField,
  Form,
  Icon,
  Lov,
  DateTimePicker,
  Modal,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';

import { observer } from 'mobx-react';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import formatterCollections from 'utils/intl/formatterCollections';
import { BASIC } from '@utils/config';
import { tableDS, drawerDS } from './stores';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.QueryProductDegradationRecords';
const tenantId = getCurrentOrganizationId();

const QueryProductDegradationRecords = observer(() => {
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  const drawerDs = useMemo(() => new DataSet(drawerDS()), []); // 复制ds

  const openNcDrawer = async record => {
    // drawerDs.loadData(record.data.details);
    drawerDs.setQueryParameter('ncRecordId', record.data.ncRecordId);
    drawerDs.query().then(res => {
      if (res.rows.content && res.rows.content.length > 0) {
        const list = res.rows.content;
        const drawerList = list.map(item => {
          return {
            ...item,
            identification: record.data.identification,
          };
        });
        drawerDs.loadData(drawerList);
      }
    });
    Modal.open({
      closable: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.ncDetail`).d('不良明细'),
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table dataSet={drawerDs} columns={drawerColumns} style={{ height: 400 }} />,
    });
  };

  const drawerColumns = [
    {
      name: 'identification',
      width: 170,
    },
    {
      name: 'ncCode',
      width: 170,
    },
    {
      name: 'operationName',
      width: 150,
    },
    {
      name: 'ncStatusDesc',
      width: 150,
    },
    {
      name: 'ncRecordTime',
      width: 150,
    },
    {
      name: 'ncUserName',
      width: 150,
    },
    {
      name: 'lastUpdateTime',
      width: 150,
    },
  ];

  const columns = [
    {
      header: intl.get(`${modelPrompt}.ncDetail`).d('不良明细'),
      align: 'center',
      lock: 'left',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => {
                openNcDrawer(record);
              }}
            >
              {intl.get(`${modelPrompt}.loadDetail`).d('明细')}
            </a>
          </span>
        );
      },
    },
    {
      name: 'siteCode',
    },
    {
      name: 'identification',
      width: 200,
    },
    {
      name: 'eoNum',
      width: 200,
    },
    {
      name: 'materialLotCode',
      width: 200,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'ncCode',
      width: 150,
    },
    {
      name: 'ncDescription',
      width: 150,
    },
    {
      name: 'operationName',
      width: 150,
    },
    {
      name: 'qualityStatusDesc',
      width: 150,
    },
    {
      name: 'workOrderNum',
      width: 150,
    },
    {
      name: 'disposalFunctionDescription',
    },
    {
      name: 'degradeLevelDesc',
    },
    {
      name: 'disposalTime',
      width: 170,
    },
    {
      name: 'realName',
      width: 150,
    },
  ];
  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Col span={18}>
            <Form columns={4} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'identifications', '条码号', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <Lov name="materialLov" />
              <Lov name="defaultNcCodeLov" />
              <TextField
                name="workOrderNumstr"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'workOrderNumstr', '生产指令', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              {expandForm && (
                <>
                  <DateTimePicker name="startTime" />
                  <DateTimePicker name="endTime" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                funcType="link"
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleSearch = async () => {
    const {
      identifications,
      materialIds,
      ncCodeIds,
      workOrderNumstr,
      startTime,
      endTime,
    } = tableDs?.queryDataSet?.toJSONData()[0];
    if (
      !identifications &&
      !materialIds.length &&
      !ncCodeIds.length &&
      !workOrderNumstr &&
      !startTime &&
      !endTime
    ) {
      notification.error({
        message: intl.get(`${modelPrompt}.onequeryField`).d('请至少输入一个查询条件'),
      });
      return;
    }

    if(materialIds.length||ncCodeIds.length){
      if(!startTime && !endTime){
        notification.error({message: intl.get(`${modelPrompt}.queryDate`).d('请输入时间查询！')});
        return
      }
    }
    if(startTime&&!endTime||!startTime&&endTime){
      notification.error({message: intl.get(`${modelPrompt}.dateValidate`).d('开始时间和结束时间必须同时输入！')});
      return
    }
    tableDs.query();
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleSearch();
    }
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const getExportDisabledFlag = (dataSet) => {
    const queryParmas = dataSet.current?.toData() || {};
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i]) || i === '__dirty') {
        delete queryParmas[i];
      }
    })
    const queryDataLength = Object.keys(queryParmas)?.length;
    return !queryDataLength;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('产品降级记录查询')}>
        {getExportDisabledFlag(tableDs.queryDataSet) ? (
          <Button disabled>
            {intl.get(`${modelPrompt}.export`).d('导出')}
          </Button>
        ) : (
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-product-report/degrade/record/export`}
            queryParams={getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        )}
      </Header>
      <Content>
        <Table
          searchCode="QueryProductDegradationRecords"
          customizedCode="QueryProductDegradationRecords"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.QueryProductDegradationRecords', 'tarzan.common'],
})(QueryProductDegradationRecords);
