/**
 * @Description: 产线审核计划管理平台-表格DS
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.prodlineReview.prodlineReviewPlan';

const tableDS = () => ({
  autoQuery: true,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'prodlineRevplanCode',
      label: intl.get(`${modelPrompt}.prodlineRevplanCode`).d('产线审核计划编码'),
      type: FieldType.string,
    },
    {
      name: 'siteObj',
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      type: FieldType.object,
      textField: 'siteName',
      valueField: 'siteId',
      lovPara: { tenantId },
    },
    {
      name: 'siteId',
      bind: 'siteObj.siteId',
      type: FieldType.string,
    },
    {
      name: 'siteName',
      bind: 'siteObj.siteName',
      type: FieldType.string,
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.PRODLINE_REVPLAN_STATUS',
    },
    {
      name: 'prodlineReviewTmpNum',
      label: intl.get(`${modelPrompt}.prodlineReviewTmpNum`).d('产线审核模板编码'),
      type: FieldType.string,
    },
    {
      name: 'reviewType',
      label: intl.get(`${modelPrompt}.reviewType`).d('审核类型'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.REVIEW_TYPE',
    },
    {
      name: 'reviewStage',
      label: intl.get(`${modelPrompt}.reviewStage`).d('审核阶段'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.REVIEW_STAGE',
    },
    {
      name: 'prodObj',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      ignore: 'always',
      multiple: true,
      type: FieldType.object,
      textField: 'prodLineName',
      valueField: 'prodLineId',
      lovPara: { tenantId },
    },
    {
      name: 'prodLineIdList',
      bind: 'prodObj.prodLineId',
      type: FieldType.string,
    },
    {
      name: 'prodLineName',
      bind: 'prodObj.prodLineName',
      type: FieldType.string,
    },
    {
      name: 'projectName',
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.PROJECT_NAME',
      lovPara: { tenantId },
    },
    {
      name: 'userObj',
      label: intl.get(`${modelPrompt}.creationByName`).d('创建人'),
      lovCode: 'LOV_USER',
      ignore: 'always',
      type: FieldType.object,
      textField: 'realName',
      valueField: 'createdBy',
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'userObj.id',
      type: FieldType.string,
    },
    {
      name: 'realName',
      bind: 'userObj.realName',
      type: FieldType.string,
    },
  ],
  fields: [
    {
      name: 'prodlineRevplanCode',
      label: intl.get(`${modelPrompt}.prodlineRevplanCode`).d('产线审核计划编码'),
      type: FieldType.string,
    },
    {
      name: 'statusName',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      type: FieldType.string,
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      type: FieldType.string,
    },
    {
      name: 'prodlineReviewTmpNum',
      label: intl.get(`${modelPrompt}.prodlineReviewTmpNum`).d('产线审核模板编码'),
      type: FieldType.string,
    },
    {
      name: 'reviewTypeName',
      label: intl.get(`${modelPrompt}.reviewType`).d('审核类型'),
      type: FieldType.string,
    },
    {
      name: 'reviewStageName',
      label: intl.get(`${modelPrompt}.reviewStage`).d('审核阶段'),
      type: FieldType.string,
    },
    {
      name: 'projectName',
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.PROJECT_NAME',
      lovPara: { tenantId },
    },
    {
      name: 'prodLineNameList',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
    },
    {
      name: 'scheFinishTime',
      label: intl.get(`${modelPrompt}.scheFinishTime`).d('计划完成时间'),
      type: FieldType.string,
    },
    {
      name: 'closeDate',
      label: intl.get(`${modelPrompt}.closeDate`).d('审核关闭时间'),
      type: FieldType.string,
    },
    {
      name: 'oneQty',
      label: intl.get(`${modelPrompt}.oneQty`).d('放行等级1'),
      type: FieldType.string,
    },
    {
      name: 'twoQty',
      label: intl.get(`${modelPrompt}.twoQty`).d('放行等级2'),
      type: FieldType.string,
    },
    {
      name: 'threeQty',
      label: intl.get(`${modelPrompt}.threeQty`).d('放行等级3'),
      type: FieldType.string,
    },
    {
      name: 'fourQty',
      label: intl.get(`${modelPrompt}.fourQty`).d('放行等级4'),
      type: FieldType.string,
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      type: FieldType.string,
    },
    {
      name: 'creationByName',
      label: intl.get(`${modelPrompt}.creationByName`).d('创建人'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/head/list/query/for/ui`,
      method: 'GET',
    },
  },
});
const tableLineDS = () => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'elementNum',
      label: intl.get(`${modelPrompt}.elementNum`).d('产线审核要素编码'),
      type: FieldType.string,
    },
    {
      name: 'statusName',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      type: FieldType.string,
    },
    {
      name: 'reviewDimensionName',
      label: intl.get(`${modelPrompt}.reviewDimension`).d('评审维度'),
      type: FieldType.string,
    },
    {
      name: 'reviewItem',
      label: intl.get(`${modelPrompt}.reviewItem`).d('审核项目'),
      type: FieldType.string,
    },
    {
      name: 'reviewContent',
      label: intl.get(`${modelPrompt}.reviewContent`).d('评审要素'),
      type: FieldType.string,
    },
    {
      name: 'deliveryName',
      label: intl.get(`${modelPrompt}.deliveryName`).d('交付物名称'),
      type: FieldType.string,
    },
    {
      name: 'deliveryTemplate',
      label: intl.get(`${modelPrompt}.deliveryTemplate`).d('交付物模板'),
      type: FieldType.attachment,
      bucketName: 'qms',
    },
    {
      name: 'deliveryUuid',
      label: intl.get(`${modelPrompt}.deliveryUuid`).d('交付物'),
      type: FieldType.attachment,
      bucketName: 'qms',
    },
    {
      name: 'responsibleDeptName',
      label: intl.get(`${modelPrompt}.responsibleDeptName`).d('责任部门'),
      type: FieldType.string,
    },
    {
      name: 'responsibleEmName',
      label: intl.get(`${modelPrompt}.responsibleEmName`).d('责任人'),
      type: FieldType.string,
    },
    {
      name: 'scheFinishTime',
      label: intl.get(`${modelPrompt}.scheFinishTime`).d('计划完成时间'),
      type: FieldType.string,
    },
    {
      name: 'applyFlag',
      label: intl.get(`${modelPrompt}.applyFlag`).d('是否适用'),
      type: FieldType.string,
    },
    {
      name: 'noApplyReason',
      label: intl.get(`${modelPrompt}.noApplyReason`).d('不适用原因'),
      type: FieldType.string,
    },
    {
      name: 'autualFinishTime',
      label: intl.get(`${modelPrompt}.autualFinishTime`).d('实际完成时间'),
      type: FieldType.string,
    },
    {
      name: 'reviewResult',
      label: intl.get(`${modelPrompt}.reviewResult`).d('评审结果'),
      type: FieldType.string,
    },
    {
      name: 'nonConTerm',
      label: intl.get(`${modelPrompt}.nonConTerm`).d('不符合项'),
      type: FieldType.string,
    },
    {
      name: 'releaseClass',
      label: intl.get(`${modelPrompt}.releaseClass`).d('放行等级'),
      type: FieldType.string,
    },
    {
      name: 'closeDate',
      label: intl.get(`${modelPrompt}.closeDate`).d('审核关闭时间'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/line/list/query/for/ui`,
      method: 'GET',
    },
  },
});
const lovDS = () => ({
  autoQuery: false,
  selection: false,
  fields: [
    {
      name: 'prodlineReviewTmpNumObj',
      label: intl.get(`${modelPrompt}.prodlineReviewTmpNum`).d('产线审核模板编码'),
      lovCode: 'YP.QIS.PRODLINE_REVIEW_TMP',
      ignore: 'always',
      type: FieldType.object,
      textField: 'prodlineReviewTmpNum',
      valueField: 'prodlineReviewTmpId',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'prodlineReviewTmpId',
      bind: 'prodlineReviewTmpNumObj.prodlineReviewTmpId',
      type: FieldType.string,
    },
    {
      name: 'prodlineReviewTmpNum',
      bind: 'prodlineReviewTmpNumObj.prodlineReviewTmpNum',
      type: FieldType.string,
    },
  ],
});
export { tableDS, tableLineDS, lovDS }
