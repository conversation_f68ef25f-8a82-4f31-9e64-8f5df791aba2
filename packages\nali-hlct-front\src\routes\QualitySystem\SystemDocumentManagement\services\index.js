/*
 * @Description: 质量体系文件管理-service
 * @Author: <<EMAIL>>
 * @Date: 2023-10-26 15:38:32
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-07 11:10:59
 */
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { HZERO_IAM } from 'utils/config';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 保存文件
export function SaveFile() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/confirm/for/ui`,
    method: 'POST',
  };
}

// 暂存文件
export function StagingFile() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/staging/for/ui`,
    method: 'POST',
  };
}

// 文件升版
export function UpdateFile() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/upgrade/version/for/ui`,
    method: 'POST',
  };
}

// 取消文件
export function CancelFile() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/cancel/for/ui`,
    method: 'POST',
  };
}

// 文件发布校验
export function ValidatePublishFile() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/publish/apply/for/vilde`,
    method: 'POST',
  };
}

// 文件发布
export function PublishFile() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/publish/apply/for/ui`,
    method: 'POST',
  };
}

// 文件废弃
export function DiscardFile() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/discard/apply/for/ui`,
    method: 'POST',
  };
}

/**
 * 查询用户所有角色
 * @function GetUserRole
 * @returns {object} fetch Promise
 */
export function GetUserRole() {
  return {
    url: `${HZERO_IAM}/hzero/v1/${tenantId}/member-roles/user-all-roles/${getCurrentUserId()}`,
    method: 'GET',
  };
}

/**
 * 导出
 * @function ExportSystemFile
 * @returns {object} fetch Promise
 */
export function ExportSystemFile() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/export/for/ui`,
    method: 'GET',
  };
}
