import React, { useState, useMemo, useEffect } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  Select,
  DateTimePicker,
  TextArea,
  Attachment,
  // TextField,
  Spin,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';

import uuid from 'uuid/v4';
import { useRequest } from '@components/tarzan-hooks';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { QueryDetail, SaveDetail, SubmitDetail } from '../services';
import { fetchDefaultSite } from '../../../services/api';
import { detailDS } from '../stores/MarkMaintenanceDS';

const modelPrompt = 'tarzan.hmes.MarkMaintenance';


const markMaintenanceDetail = props => {
  const { run: queryDetail, loading: queryDetailLoading } = useRequest(QueryDetail(), {
    manual: true,
    needPromise: true,
  });
  const { run: saveDetail, loading: saveDetailLoading } = useRequest(SaveDetail(), {
    manual: true,
    needPromise: true,
  });
  const { run: submitDetail, loading: submitLoading } = useRequest(SubmitDetail(), {
    manual: true,
    needPromise: true,
  });
  const [title, setTitle] = useState('');
  const [canEdit, setCanEdit] = useState(false);

  const formDs = useMemo(() => new DataSet(detailDS()), []);
  const {
    match: {
      params: { id },
    },
  } = props;
  useEffect(() => {
    if (id === 'create') {
      // 获取默认站点
      queryBasicData();
      formDs.current?.set('applyBasis', uuid());
      formDs.current?.set('attachments', uuid());
      setTitle(intl.get(`${modelPrompt}.detail.title`).d('标记维护新建'));
      setCanEdit(true);
    } else {
      handleQuery()
      setTitle(intl.get(`${modelPrompt}.detail.title`).d('标记维护详情'));
    }
  }, [id]);
  const handleQuery = () => {
    queryDetail({
      params: {
        markingId: id,
      },
    }).then(res => {
      setCanEdit(false);
      formDs.loadData([res]);
    })
  }

  // 查询站点
  const queryBasicData = async () => {
    fetchDefaultSite().then(res => {
      if (res && res.success) {
        formDs.current?.set('siteLov', res.rows);
        formDs.current?.set('siteId', res.rows?.siteId);
        formDs.current?.set('siteCode', res.rows?.siteCode);
      }
    });
  };

  // 保存
  const handelSave = async () => {
    if(!formDs.toJSONData()[0])return
    if(await formDs.validate()){
      saveDetail({
        params: formDs.toJSONData()[0],
      }).then(res => {
        if(res&&res.failed){
          // notification.error({
          //   message: res.message,
          // });
        }else{
          notification.success({
            message: '保存成功！',
          });
          setCanEdit(false);
          if(id === 'create'){
            props.history.push(`/hmes/mark-maintenance/detail/${res}`);
          }else{
            handleQuery(id);
          }
        }
      })
    }
  }

  // 提交
  const handelSubmit = async () => {
    if(await formDs.validate()){
      submitDetail({
        params: formDs.current.toData(),
      }).then(res => {
        if(res&&res.success){
          notification.success({
            message: '提交成功！',
          });
          handleQuery(id);
        // }else{
        }
      })
    }
  }

  // 编辑按钮
  const handelEdit = () => {
    setCanEdit(true);
  };

  // 取消按钮
  const handelCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/mark-maintenance/list');
    } else {
      setCanEdit(false);
      handleQuery();
    }
  };

  const reasonProps  = {
    label: intl.get(`${modelPrompt}.applyBasisNew`).d('申请依据（需提交制造、质量、工艺同意证据）'),
    name: 'applyBasis',
    bucketName: 'hmes',
    bucketDirectory: 'inspect-group-maintain',
    accept: [ 'image/*' ],
    viewMode: 'popup',
  };
  const panelDetailEnclosure  = {
    label: intl.get(`${modelPrompt}.attachments`).d('附件'),
    name: 'attachments',
    bucketName: 'hmes',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.xlsx', '.xls','image/*', '.pptx'],
    viewMode: 'popup',
  };

  return (
    <div className="hmes-style">
      <Spin spinning={saveDetailLoading||queryDetailLoading||submitLoading}>
        <Header title={title} backPath="/hmes/mark-maintenance/list">
          {!canEdit && (
            <>
              <Button onClick={handelEdit} disabled={formDs?.current?.data?.status === 'AUDIT' || formDs?.current?.data?.status === 'CANCEL'} icon="edit" color="primary">
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
              <Button onClick={handelSubmit} disabled={formDs?.current?.data?.status === 'AUDIT' || formDs?.current?.data?.status === 'CANCEL'} color="primary">
                {intl.get('tarzan.common.button.submit').d('提交')}
              </Button>
            </>
          )}
          {canEdit && (
            <>
              <Button onClick={handelSave}  style={{ marginRight: 15 }} icon="save" color="primary">
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button onClick={handelCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
        </Header>
        <Content>
          <Form dataSet={formDs} columns={3} disabled={!canEdit}>
            <Lov name="siteLov" disabled={id !== 'create'} />
            <Select name="markingCode" disabled/>
            <Select name="status" disabled />
            <Select
              name="type"
              onOption={({ record }) => ({
                disabled: record?.get('value') === 'PASS' && id === 'create',
              })}
            />
            <Select name="markingContent" disabled={id !== 'create'} />
            <Select name="enableFlag" disabled={id === 'create'} />
            <DateTimePicker name="expirationDate" />
            <TextArea name="applyReason" />
            <Attachment name="applyBasis" {...reasonProps}/>
            <Lov name="operationObj" />
            <TextArea name="interceptionDisposalWay" placeholder={intl.get(`${modelPrompt}.applyBasisNew`).d('请按固定格式填写：marking编号+联系人+处置方法+原因，例如：MKSP001+张三+二氦拦截+极片暗痕验证')} />
            <TextArea name="disposalResult" disabled={id === 'create'} />
            <Attachment name="attachments" {...panelDetailEnclosure}/>
          </Form>
        </Content>
      </Spin>

    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.MarkMaintenance', 'tarzan.common'],
})(markMaintenanceDetail);
