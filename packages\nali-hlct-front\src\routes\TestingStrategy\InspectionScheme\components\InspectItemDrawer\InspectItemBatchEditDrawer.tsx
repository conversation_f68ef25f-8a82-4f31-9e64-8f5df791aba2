/**
 * @Description: 检验方案明细界面-批量编辑
 * @Author: <<EMAIL>>
 * @Date: 2023-03-08 11:23:38
 * @LastEditTime: 2023-05-18 16:41:54
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect } from 'react';
import intl from 'utils/intl';
import { Form, TextField, Select, Switch, NumberField, Lov, Col, CheckBox } from 'choerodon-ui/pro';
import { LabelLayout, LabelAlign } from 'choerodon-ui/pro/lib/form/enum';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { FetchRuleCodeDetailConfig } from '@/utils';
import InputValueComponent from '../InputValueComponent';
import styles from '../index.modules.less';

const modelPrompt = 'tarzan.qms.inspectGroupMaintenance';
const { Item, ItemGroup } = Form;
const { Option } = Select;

const checkBoxStyle = { marginLeft: '5px' };

const InspectItemInfoDrawer = props => {
  const {
    tableDS,
    canEdit,
    warnNumberDS,
    trueNumberDS,
    falseNumberDS,
    tenantId,
    formulaListTableDs,
  } = props;

  const { run: fetchRuleCodeDetail, loading: fetchRuleCodeDetailLoading } = useRequest(
    FetchRuleCodeDetailConfig(),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 数据类型
  const [dataType, setDataType] = useState(
    tableDS.current && tableDS.current?.get('dataType') ? tableDS.current?.get('dataType') : null,
  );
  // 值列表
  const [valueLists, setValueList] = useState(
    tableDS.current && tableDS.current?.get('valueLists') ? tableDS.current?.get('valueLists') : [],
  );

  // 数值类型-预警值
  const [warnNumberList, setWarnNumberList] = useState([]);
  // 数值类型-符合值
  const [trueNumberList, setTrueNumberList] = useState([]);
  // 数值类型-不符合值
  const [falseNumberList, setFalseNumberList] = useState([]);

  useEffect(() => {
    warnNumberDS.loadData([]);
    trueNumberDS.loadData([]);
    falseNumberDS.loadData([]);
  }, [tableDS.current]);

  // 数据类型变化时清空相关数据
  const handleChangeDataType = value => {
    tableDS.current.set('uomLov', null);
    tableDS.current.set('decimalNumber', null);
    tableDS.current.set('processMode', null);
    tableDS.current.init('valueLists', null);
    tableDS.current.init('trueValue', null);
    tableDS.current.init('falseValue', null);
    tableDS.current.set('earlyWarningValue', null);
    tableDS.current.init('trueValueList', []);
    tableDS.current.init('falseValueList', []);
    tableDS.current.set('warningValueList', []);
    tableDS.current.set('formulaLov', []);
    tableDS.current.set('formulaList', []);
    tableDS.current.set('dimension', []);
    tableDS.current.set('inspectFrequencyObject', null);
    tableDS.current.set('m', null);
    tableDS.current.set('n', null);
    if (value === 'CALCULATE_FORMULA') {
      tableDS.current.init('enterMethod', 'AUTOMATIC_COLLECTION');
      tableDS.current.init('samplingMethodLov', undefined);
      tableDS.current.init('employeePosition', undefined);
      tableDS.current.init('frequencyParams', undefined);
      tableDS.current.init('sameGroupIdentification', undefined);
      tableDS.current.init('outsourceFlag', 'N');
      tableDS.current.init('destructiveExperimentFlag', 'N');
    }
    setValueList([]);
    setWarnNumberList([]);
    warnNumberDS.loadData([]);
    setTrueNumberList([]);
    trueNumberDS.loadData([]);
    setFalseNumberList([]);
    falseNumberDS.loadData([]);
    setDataType(value);
  };

  const handleChangeValueList = value => {
    tableDS.current.init('trueValue', null);
    tableDS.current.init('falseValue', null);
    setValueList(value);
  };

  // 检验频率处理列表展示值
  const handleChangeFrequency = value => {
    if (!value) {
      tableDS.current.set('m', null);
      tableDS.current.set('n', null);
    }
  };

  const clearCheckBoxVal = (name, value) => {
    const selectList = tableDS.current?.get('selectList') || {};
    selectList[name] = value;
    tableDS.current.set('selectList', selectList);
    tableDS.current.set(name, null);
    switch (name) {
      case 'inspectFrequencyObject':
        tableDS.current.set('m', null);
        tableDS.current.set('n', null);
        break;
      case 'valueLists':
        tableDS.current.init('trueValue', null);
        tableDS.current.init('falseValue', null);
        setValueList([]);
        break;
      default:
        break;
    }
  };

  const formulaLovChange = async value => {
    if (value) {
      const { ruleCode } = value;
      const ruleCodeDetail = await fetchRuleCodeDetail({
        params: {
          ruleCode,
          tenantId,
        },
      });
      if (ruleCodeDetail && typeof ruleCodeDetail === 'object') {
        const newRuleCodeDetail = ruleCodeDetail.map(item => {
          return {
            fieldCode: item.fieldCode,
            fieldName: item.fieldName,
            isRequired: item.fieldCode === 'decimalNumber' ? 'N' : item.isRequired,
          };
        });
        tableDS.current.set('formulaList', newRuleCodeDetail);
        formulaListTableDs.loadData(newRuleCodeDetail);
      } else {
        tableDS.current.set('formulaList', undefined);
      }
    } else {
      tableDS.current.set('formulaList', undefined);
    }
  };

  return (
    <>
      <Form
        record={tableDS.current}
        columns={3}
        labelWidth={112}
        disabled={!canEdit}
        labelLayout={LabelLayout.horizontal}
        labelAlign={LabelAlign.right}
        className={styles['input-group-maintenance-batch-edit']}
      >
        <Item name="inspectItemType">
          <ItemGroup>
            <Select name="inspectItemType" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('inspectItemType', value);
              }}
              name="inspectItemType_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="inspectBasis">
          <ItemGroup>
            <TextField name="inspectBasis" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('inspectBasis', value);
              }}
              name="inspectBasis_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="qualityCharacteristic">
          <ItemGroup>
            <Select name="qualityCharacteristic" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('qualityCharacteristic', value);
              }}
              name="qualityCharacteristic_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="inspectTool">
          <ItemGroup>
            <Select name="inspectTool" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('inspectTool', value);
              }}
              name="inspectTool_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="inspectMethod">
          <ItemGroup>
            <Select name="inspectMethod" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('inspectMethod', value);
              }}
              name="inspectMethod_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="requiredFlag">
          <ItemGroup>
            <Switch
              name="requiredFlag"
              label={intl?.get(`${modelPrompt}.label.requiredFlag`).d('必填项目')}
            />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('requiredFlag', value);
              }}
              name="requiredFlag_select"
              disabled
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="technicalRequirement">
          <ItemGroup>
            <TextField name="technicalRequirement" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('technicalRequirement', value);
              }}
              name="technicalRequirement_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="enterMethod">
          <ItemGroup>
            <Select name="enterMethod" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('enterMethod', value);
              }}
              name="enterMethod_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="dataType">
          <ItemGroup>
            <Select
              name="dataType"
              onChange={handleChangeDataType}
              optionsFilter={record => {
                return record?.get('value') !== 'CALCULATE_FORMULA';
              }}
            />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('dataType', value);
              }}
              name="dataType_select"
              disabled
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="uomLov">
          <ItemGroup>
            <Lov name="uomLov" disabled={dataType !== 'VALUE'} />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('uomLov', value);
              }}
              name="uomLov_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="decimalNumber">
          <ItemGroup>
            <NumberField name="decimalNumber" disabled={dataType !== 'VALUE'} />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('decimalNumber', value);
              }}
              name="decimalNumber_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="processMode">
          <ItemGroup>
            <Select name="processMode" disabled={dataType !== 'VALUE'} />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('processMode', value);
              }}
              name="processMode_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        {(!dataType || !['VALUE_LIST', 'TEXT', 'DECISION_VALUE'].includes(dataType)) && (
          <>
            <Item
              name="trueValue"
              rowSpan={trueNumberList.length || 1}
              label={intl?.get(`${modelPrompt}.model.trueValue`).d('符合值')}
              style={{ padding: 0 }}
            >
              <InputValueComponent
                {...{
                  showStandard: true,
                  clearFlag: true,
                  disabledFlag: true,
                  name: 'trueValue',
                  canEdit:
                    canEdit &&
                    falseNumberList.length < 1 &&
                    ['VALUE', 'CALCULATE_FORMULA'].includes(dataType),
                  numberDS: trueNumberDS,
                  numberList: trueNumberList,
                  setNumberList: setTrueNumberList,
                }}
              />
            </Item>
            <Item
              name="falseValue"
              rowSpan={falseNumberList.length || 1}
              label={intl?.get(`${modelPrompt}.model.falseValue`).d('不符合值')}
              style={{ padding: 0 }}
            >
              <InputValueComponent
                {...{
                  clearFlag: true,
                  disabledFlag: true,
                  name: 'falseValue',
                  canEdit:
                    canEdit &&
                    trueNumberList.length < 1 &&
                    ['VALUE', 'CALCULATE_FORMULA'].includes(dataType),
                  numberDS: falseNumberDS,
                  numberList: falseNumberList,
                  setNumberList: setFalseNumberList,
                }}
              />
            </Item>
          </>
        )}
        {dataType === 'VALUE_LIST' && (
          <>
            <Item name="trueValue">
              <ItemGroup>
                <Select name="trueValue" multiple>
                  {(valueLists || []).map(item => (
                    <Option value={item} key={item}>
                      {item}
                    </Option>
                  ))}
                </Select>
                <CheckBox
                  onChange={value => {
                    clearCheckBoxVal('trueValue', value);
                  }}
                  disabled
                  name="trueValue_select"
                  style={checkBoxStyle}
                />
              </ItemGroup>
            </Item>
            <Item name="falseValue">
              <ItemGroup>
                <Select name="falseValue" multiple>
                  {(valueLists || []).map(item => (
                    <Option value={item} key={item}>
                      {item}
                    </Option>
                  ))}
                </Select>
                <CheckBox
                  onChange={value => {
                    clearCheckBoxVal('falseValue', value);
                  }}
                  disabled
                  name="falseValue_select"
                  style={checkBoxStyle}
                />
              </ItemGroup>
            </Item>
          </>
        )}
        {['TEXT', 'DECISION_VALUE'].includes(dataType) && (
          <>
            <Item name="trueValue">
              <ItemGroup>
                <TextField name="trueValue" />
                <CheckBox
                  onChange={value => {
                    clearCheckBoxVal('trueValue', value);
                  }}
                  name="trueValue_select"
                  style={checkBoxStyle}
                />
              </ItemGroup>
            </Item>
            <Item name="falseValue">
              <ItemGroup>
                <TextField name="falseValue" />
                <CheckBox
                  onChange={value => {
                    clearCheckBoxVal('falseValue', value);
                  }}
                  name="falseValue_select"
                  style={checkBoxStyle}
                />
              </ItemGroup>
            </Item>
          </>
        )}
        <Item
          name="earlyWarningValue"
          rowSpan={warnNumberList.length || 1}
          label={intl?.get(`${modelPrompt}.model.earlyWarningValue`).d('预警值')}
          style={{ padding: 0 }}
        >
          <InputValueComponent
            {...{
              clearFlag: true,
              name: 'earlyWarningValue',
              canEdit:
                canEdit &&
                trueNumberList.length > 0 &&
                ['VALUE', 'CALCULATE_FORMULA'].includes(dataType),
              numberDS: warnNumberDS,
              numberList: warnNumberList,
              setNumberList: setWarnNumberList,
            }}
          />
        </Item>
        {['CALCULATE_FORMULA'].includes(dataType) && (
          <>
            <Item name="formulaLov">
              <ItemGroup>
                <Lov
                  name="formulaLov"
                  disabled={fetchRuleCodeDetailLoading}
                  onChange={formulaLovChange}
                />
                <CheckBox
                  onChange={value => {
                    clearCheckBoxVal('formulaLov', value);
                  }}
                  name="formulaLov_select"
                  disabled
                  style={checkBoxStyle}
                />
              </ItemGroup>
            </Item>
            <Item name="dimension">
              <ItemGroup>
                <Select name="dimension" onChange={handleChangeFrequency} />
                <CheckBox
                  onChange={value => {
                    clearCheckBoxVal('dimension', value);
                  }}
                  name="dimension_select"
                  disabled
                  style={checkBoxStyle}
                />
              </ItemGroup>
            </Item>
          </>
        )}
        <Item name="valueLists">
          <ItemGroup>
            <TextField
              name="valueLists"
              multiple
              disabled={dataType !== 'VALUE_LIST'}
              onChange={handleChangeValueList}
            />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('valueLists', value);
              }}
              name="valueLists_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="dataQty">
          <ItemGroup>
            <NumberField name="dataQty" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('dataQty', value);
              }}
              name="dataQty_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="samplingMethodLov">
          <ItemGroup>
            <Lov name="samplingMethodLov" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('samplingMethodLov', value);
              }}
              name="samplingMethodLov_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="ncCodeGroupLov">
          <ItemGroup>
            <Lov name="ncCodeGroupLov" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('ncCodeGroupLov', value);
              }}
              name="ncCodeGroupLov_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="employeePosition">
          <ItemGroup>
            <TextField name="employeePosition" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('employeePosition', value);
              }}
              name="employeePosition_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="inspectFrequencyObject">
          <ItemGroup>
            <Select name="inspectFrequencyObject" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('inspectFrequencyObject', value);
              }}
              name="inspectFrequencyObject_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="m" label={intl?.get(`${modelPrompt}.label.frequencyParameter`).d('频率参数')}>
          <ItemGroup>
            <div>
              <Col span={12} style={{ textAlign: 'left', lineHeight: 2.3 }}>
                <span>M=</span>
                <NumberField name="m" style={{ width: 'calc(90% - 16px)' }} />
              </Col>
              <Col span={12} style={{ textAlign: 'right', lineHeight: 2.3 }}>
                <span>N=</span>
                <NumberField name="n" style={{ width: 'calc(90% - 16px)' }} />
              </Col>
            </div>
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('m', value);
                clearCheckBoxVal('n', value);
              }}
              disabled
              name="frequencyParameter_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="actionItem">
          <ItemGroup>
            <TextField name="actionItem" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('actionItem', value);
              }}
              name="actionItem_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="sameGroupIdentification">
          <ItemGroup>
            <TextField name="sameGroupIdentification" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('sameGroupIdentification', value);
              }}
              name="sameGroupIdentification_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="outsourceFlag">
          <ItemGroup>
            <Switch name="outsourceFlag" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('outsourceFlag', value);
              }}
              name="outsourceFlag_select"
              disabled
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="destructiveExperimentFlag">
          <ItemGroup>
            <Switch name="destructiveExperimentFlag" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('destructiveExperimentFlag', value);
              }}
              name="destructiveExperimentFlag_select"
              disabled
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
        <Item name="remark">
          <ItemGroup>
            <TextField name="remark" />
            <CheckBox
              onChange={value => {
                clearCheckBoxVal('remark', value);
              }}
              name="remark_select"
              style={checkBoxStyle}
            />
          </ItemGroup>
        </Item>
      </Form>
    </>
  );
};

export default InspectItemInfoDrawer;
