import React, { useMemo } from 'react';
// import { RouteComponentProps } from 'react-router';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Collapse, Tag } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
// import notification from 'utils/notification';
import { observer } from 'mobx-react';
import { lineTableDS, tableDS } from '../stores/platformListDS';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';

// interface BadRecordPlatformProps extends RouteComponentProps {
//   headDs: DataSet;
//   lineDs: DataSet;
//   customizeTable: any;
// }

const BadRecordPlatform = observer(props => {
  const {
    // match: { path },
    history,
    headDs,
    lineDs,
  } = props;


  const headerRowClick = record => {
    // queryLineTable(record.get('ncRecordId'));
    lineDs.setQueryParameter('ncRecordId', record.get('ncRecordId'));
    lineDs.query();
  };

  const queryLineTable = ncRecordId => {
    if (ncRecordId) {
      lineDs.setQueryParameter('ncRecordId', ncRecordId);
    } else {
      lineDs.setQueryParameter('ncRecordId', 0);
    }
    lineDs.query();
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('ncRecordId'));
    } else {
      queryLineTable(null);
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const openDetail = id => {
    history.push(`/hmes/bad-record/platform-new/detail/${id}`);
  };

  // const openPrint = () => {
  //   if(headDs.selected.length&&headDs.selected.some(item => item.get('ncRecordTypeDesc')!== headDs.selected[0]?.get('ncRecordTypeDesc'))){
  //     notification.warning({
  //       message: intl
  //         .get(`${modelPrompt}.error.print.same`)
  //         .d(`物料批与在制品不允许同时打印`),
  //     })
  //   }else{
  //     let list = []
  //     if(headDs.selected[0]?.data?.ncRecordTypeDesc === '库存品材料不良'){
  //       list = headDs.selected.map(item => item.data.materialLotCode);
  //     }else{
  //       list = headDs.selected.map(item => item.data.identification);
  //     }
  //     history.push({
  //       pathname: `/hmes/bad-record/platform-new/print/create`,
  //       state: {
  //         list,
  //       },
  //     });
  //     // history.push(`/hmes/bad-record/platform-new/print/${id}`);
  //   }
  // };

  const renderNcRecordTag = (value, record) => {
    switch (record.get('ncRecordStatus')) {
      case 'NEW':
        return <Tag color="green">{value}</Tag>;
      case 'RELEASED':
        return <Tag color="blue">{value}</Tag>;
      case 'COMPLETED':
        return <Tag color="red">{value}</Tag>;
      case 'CANCEL':
        return <Tag color="gray">{value}</Tag>;
      case 'WORKING':
        return <Tag color="volcano">{value}</Tag>;
      default:
        return null;
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'identification', width: 180,
        lock: ColumnLock.left,

        renderer: ({ record }) => {
          return (
            <a
              onClick={() => {
                openDetail(record?.get('ncRecordId'));
              }}
            >
              {record?.get('identification')}
            </a>
          );
        } },
      { name: 'materialLotCode', width: 180,lock: ColumnLock.left,
        renderer: ({ record }) => {
          return (
            <a
              onClick={() => {
                openDetail(record?.get('ncRecordId'));
              }}
            >
              {record?.get('materialLotCode')}
            </a>
          );
        } },
      {
        name: 'ncRecordStatusDesc',
        width: 120,
        renderer: ({ value, record }) => renderNcRecordTag(value, record),
      },
      { name: 'ncRecordTypeDesc', width: 120 },
      { name: 'materialName' },
      { name: 'qty' },
      // {
      //   name: 'ncCodeAndDefectQty',
      //   width: 200,
      //   renderer: ({ value }) => (
      //     <div className="hcm-hzero-table-tag">
      //       {(value || []).map((item: any) => (
      //         <Tag color="blue" key={item}>
      //           {item}
      //         </Tag>
      //       ))}
      //     </div>
      //   ),
      // },
      { name: 'prodLineName' },
      { name: 'workcellName' },
      { name: 'operationName' },
      { name: 'description' },
      { name: 'equipmentCode' },
      { name: 'routerName', width: 120 },
      { name: 'locatorName' },
      { name: 'containerCode' },
      { name: 'uomName' },
      { name: 'siteName' },
      {
        name: 'eoNum', width: 150,
      },
      { name: 'ncStartTime', width: 150, align: ColumnAlign.center },
      { name: 'ncStartUserName' },
      { name: 'ncCloseTime', width: 150, align: ColumnAlign.center },
      { name: 'ncCloseUserName' },
      { name: 'ncRecordNum', width: 150},
    ];
  }, []);

  const listTableColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'lineNumber', width: 80, lock: ColumnLock.left },
      { name: 'ncCodeDesc', width: 120, lock: ColumnLock.left },
      { name: 'ncCodeStatusDesc', width: 150 },
      { name: 'rootCauseWorkcellName', width: 150 },
      { name: 'rootCauseEquipmentCode', width: 150 },
      { name: 'rootCauseOperationCode', width: 150 },
      { name: 'responsibleUserName' },
      { name: 'responsibleApartment', width: 150 },
    ];
  }, []);

  return (
    <div className="hmes-style" style={{height: '98%', overflow: 'auto'}}>
      <Header title={intl.get(`${modelPrompt}.title.list`).d('不良记录创建-MES')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => openDetail('create')}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        {/* <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          onClick={() => openPrint('create')}
        >
          {intl.get('tarzan.common.button.print').d('标签打印')}
        </PermissionButton> */}
      </Header>
      <Content>
        <Table
          highLightRow
          queryFieldsLimit={3}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          style={{height: 400}}
          dataSet={headDs}
          columns={columns}
          searchCode="badRecordPlatform-listHeader"
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
        />
        <Collapse bordered={false} defaultActiveKey={['transDetail']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.listLine`).d('不良记录明细')}
            key="transDetail"
          >
            <Table
              dataSet={lineDs}
              columns={listTableColumns}
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(tableDS());
      const lineDs = new DataSet(lineTableDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(BadRecordPlatform as any),
);
