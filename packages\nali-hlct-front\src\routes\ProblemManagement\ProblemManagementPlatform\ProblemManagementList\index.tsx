/**
 * @Description: 问题管理平台-列表页
 * @Author: <EMAIL>
 * @Date: 2023/7/3 13:48
 */
import React, { useEffect, useMemo } from 'react';
import { DataSet, Table, Modal } from 'choerodon-ui/pro';
import { Badge, Tag } from 'choerodon-ui';
import { observer } from 'mobx-react';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import ExcelExport from 'components/ExcelExport';
import { isNil } from 'lodash';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import { drawerPropsC7n, TemplatePrintButton } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { openTab } from 'utils/menuTab';
import { SaveProblem, SaveProblemByJudgement, SaveProblemByLayerTskdtlItem } from '../services';
import { problemInfoDS } from '../stores/ProblemInfoDS';
import CreateProblemDrawer from './CreateProblemDrawer';
import { tableDS } from '../stores';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';
const tenantId = getCurrentOrganizationId();

const ProblemManagementList = props => {
  const { tableDs, history } = props;
  const problemInfoDs = useMemo(() => new DataSet(problemInfoDS()), []);
  const selectProblemIdList = useMemo(() => tableDs.selected.map(_record => _record?.get('problemId')), [tableDs.selected]);
  // 问题基础属性新建
  const { run: saveProblem } = useRequest(SaveProblem(), {
    manual: true,
  });
  // 问题基础属性新建-基于零部件信息
  const { run: saveProblemByJudgement } = useRequest(SaveProblemByJudgement(), {
    manual: true,
  });
  // 问题基础属性新建-基于分层审核任务明细
  const { run: saveProblemByLayerTskdtlItem } = useRequest(SaveProblemByLayerTskdtlItem(), {
    manual: true,
  });

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    const { stateType = 'query', problemStatus, onlyForMeFalg, onlyExtensionFalg, ...others } = props?.location?.state || {};
    if (Object.keys(props?.location?.state || {}).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      tableDs.query(props.tableDs.currentPage);
      return;
    }
    // 2. 第二种情况，需使用state中的传参，来设置表格查询参数
    if (stateType === 'query') {
      tableDs.queryDataSet.current?.set('problemStatusList', problemStatus ? problemStatus.split(',') : undefined);
      tableDs.queryDataSet.current?.set('onlyForMeFalg', onlyForMeFalg || undefined);
      tableDs.queryDataSet.current?.set('onlyExtensionFalg', onlyExtensionFalg || undefined);
      tableDs.query();
    } else if (stateType === 'create') {
      handleAdd({ ...others });
    }
    history.replace({ ...history.location, state: {} });
  }, [props?.location?.state]);

  const renderRiskLight = value => {
    if (!value) {
      return;
    }
    let color;
    switch (value) {
      case 'B':
        color = '#0073DD';
        break;
      case 'G':
        color = '#1AFA29';
        break;
      case 'H':
        color = '#8A8A8A';
        break;
      case 'R':
        color = '#D81E06';
        break;
      default:
        color = '#F4EA2A';
    }
    return <Badge color={color} />;
  };

  const renderProblemStatusTag = (value, record) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
      case 'RELEASED':
        className = 'green';
        break;
      case 'FOLLOWING':
        className = 'yellow';
        break;
      case 'PUBLISH':
        className = 'blue';
        break;
      case 'CLOSED':
      case 'FREEZE':
        className = 'gray';
        break;
      default:
        className = 'geekblue';
    }
    return <Tag color={className}>{record!.getField('problemStatus')!.getText()}</Tag>;
  };

  const renderEnableFlag = ({ value, record, name }) => {
    if (!value) {
      return;
    }
    let status;
    if (value === 'NO') {
      status = "default";
    } else if (value === 'TO_EVALUE') {
      status = "processing";
    } else if (value === 'Y') {
      status = "success";
    } else {
      status = "error";
    }
    return (
      <Badge
        status={status}
        text={record!.getField(name)!.getText()}
      />
    )
  };

  const renderYesNoFlag = value => (
    <Badge
      status={value === 'Y' ? 'success' : 'error'}
      text={
        value === 'Y'
          ? intl.get(`tarzan.common.label.yes`).d('是')
          : intl.get(`tarzan.common.label.no`).d('否')
      }
    />
  );

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'problemCode',
        width: 150,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/problem-management/problem-management-platform/dist/${record!.get(
                    'problemId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'problemTitle', width: 230 },
      {
        name: 'riskLight',
        width: 80,
        align: ColumnAlign.center,
        renderer: ({ value }) => renderRiskLight(value),
      },
      {
        name: 'problemStatus',
        renderer: ({ record, value }) => renderProblemStatusTag(value, record),
      },
      { name: 'problemCategory' },
      { name: 'problemDescription', width: 300 },
      { name: 'severityLevel' },
      { name: 'influenceLevel' },
      { name: 'proposePersonRealName' },
      { name: 'proposePersonCompanyName' },
      { name: 'proposeTimePeriod', width: 150 },
      { name: 'registerPersonRealName' },
      { name: 'registerTime', align: ColumnAlign.center, width: 150 },
      { name: 'leadPersonRealName' },
      { name: 'solutionTool', width: 150 },
      // {
      //   name: 'verificationLibraryFlag',
      //   align: ColumnAlign.center,
      //   width: 150,
      //   renderer: ({ value }) => renderYesNoFlag(value),
      // },
      {
        name: 'marketActEvaluationFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => renderYesNoFlag(value),
      },
      {
        name: 'tempMeasureExeEnableFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: renderEnableFlag,
      },
      {
        name: 'perpMeasureExeEnableFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: renderEnableFlag,
      },
      {
        name: 'rootReasonConfirmFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: renderEnableFlag,
      },
      { name: 'unitName' },
      { name: 'responsiblePersonName' },
      { name: 'attribute1', align: ColumnAlign.center, width: 150 },
      { name: 'attribute2', align: ColumnAlign.center, width: 150 },
      { name: 'attribute3', align: ColumnAlign.center, width: 150 },
      { name: 'attribute4', align: ColumnAlign.center, width: 150 },
      { name: 'problemApplyCode' },
      { name: 'projectName' },
      { name: 'projectPhase' },
      { name: 'majorDivision1' },
      { name: 'majorDivision2' },
      { name: 'majorDivision3' },
      { name: 'productionLineName', width: 150 },
      { name: 'processName', width: 150 },
      { name: 'materialName', width: 150 },
      { name: 'ncType' },
      { name: 'qualityProblemType', width: 150 },
      { name: 'previewProblemType', width: 150 },
      { name: 'marketProblemType', width: 150 },
      { name: 'qualityProblemFindingMethod', width: 150 },
      { name: 'previewProblemFindingMethod', width: 150 },
    ];
  }, []);

  const handleAdd = (drawerState) => {
    Modal.open({
      ...drawerPropsC7n({
        ds: problemInfoDs,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.create`).d('新建问题'),
      destroyOnClose: true,
      style: {
        width: 420,
      },
      onOk: () => handleSave(drawerState),
      okText: intl.get(`${modelPrompt}.button.nextStep`).d('下一步'),
      children: (
        <CreateProblemDrawer problemInfoDs={problemInfoDs} drawerState={drawerState} />
      ),
    });
  };

  const handleSave = drawerState => {
    return new Promise(async resolve => {
      const valRes = await problemInfoDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      const { proposeTimePeriodStart, proposeTimePeriodEnd, ...others } = problemInfoDs.current?.toData();
      let _run;
      if (drawerState?.prianalResjudgItemId) {
        _run = saveProblemByJudgement;
      } else if (drawerState?.layerTskdtlItemId) {
        _run = saveProblemByLayerTskdtlItem;
      } else {
        _run = saveProblem;
      }
      _run({
        params: {
          ...others,
          proposeTimePeriod: `${proposeTimePeriodStart}～${proposeTimePeriodEnd}`,
          judgmentItemId: drawerState?.prianalResjudgItemId || undefined,
          layerTskdtlItemId: drawerState?.layerTskdtlItemId || undefined,
        },
        onSuccess: res => {
          notification.success({});
          history.push({
            pathname: `/hwms/problem-management/problem-management-platform/dist/${res}`,
            state: {
              createFlag: 'Y',
              problemApplyInfo: problemInfoDs.current?.get('problemApplyLov'),
            },
          });
          return resolve(true);
        },
        onFailed: () => {
          return resolve(false);
        },
      });
    });
  };

  const handleFullTextSearch = () => {
    openTab({
      key: `/hwms/problem-management/problem-management-platform/full-text-search`,
      title: intl.get(`${modelPrompt}.title.full-text`).d('问题管理平台-全文检索'),
    });
  };

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
      problemIds: tableDs.selected.map((_record) => _record?.get('problemId')),
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('问题管理平台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleAdd(null)}
          permissionList={[
            {
              code: `${modelPrompt}.list.button.createProblem`,
              type: 'button',
              meaning: '列表页-创建问题按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.create`).d('创建问题')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `problemManagementPlatform.list.button.hightCheck`,
              type: 'button',
              meaning: '列表页-高级检索按钮',
            },
          ]}
          onClick={() => handleFullTextSearch()}
        >
          {intl.get(`${modelPrompt}.button.hightCheck`).d('高级检索')}
        </PermissionButton>
        <ExcelExport
          exportAsync
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/export/ui`}
          queryParams={getExportQueryParams}
        >
          {intl.get('tarzan.common.button.export').d('导出')}
        </ExcelExport>
        <TemplatePrintButton
          disabled={selectProblemIdList.length !== 1}
          printButtonCode='PROBLEM_PRINT'
          printParams={{ PROBLEM_ID: (selectProblemIdList || []).join(',') }}
          permissionList={[
            {
              code: `${modelPrompt}.list.button.printReport`,
              type: 'button',
              meaning: '列表页-打印报告按钮',
            },
          ]}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={4}
          dataSet={tableDs}
          columns={columns}
          searchCode="problemManagementPlatformList_searchCode"
          customizedCode="problemManagementPlatformList_customizedCode"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true },
  )(observer(ProblemManagementList)),
);
