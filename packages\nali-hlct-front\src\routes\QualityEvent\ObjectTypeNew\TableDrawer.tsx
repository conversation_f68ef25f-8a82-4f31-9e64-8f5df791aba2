/**
 * @Description: 事件对象类型维护-展示列维护抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-10-19 09:53:45
 * @LastEditTime: 2022-10-19 16:31:39
 * @LastEditors: <<EMAIL>>
 */

import React, { FC, useMemo, useEffect } from 'react';
import { RouteComponentProps } from 'react-router';
import intl from 'utils/intl';
import {
  DataSet,
  Table,
  TextField,
  Switch,
  IntlField,
  Select,
  NumberField,
} from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { EnableRender } from '@components/tarzan-ui';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { tableDrawerDS } from './stories/ObjectTypeDs';
import { saveEventObjectTypeDrawerConfig, deleteEventObjectTypeDrawerConfig } from './services';

const modelPrompt = 'tarzan.event.objectType.model.objectType';

let objectSQLDrawer;

// @ts-ignore
const ObjectTypeDrawer: FC<RouteComponentProps> = ({ objectTypeId, path }) => {
  const saveEventObjectTypeDrawer = useRequest(saveEventObjectTypeDrawerConfig(), {
    manual: true,
    needPromise: true,
  });

  const deleteEventObjectTypeDrawer = useRequest(deleteEventObjectTypeDrawerConfig(), {
    manual: true,
    needPromise: true,
  });

  const tableDrawerDs = useMemo(() => {
    return new DataSet(tableDrawerDS());
  }, []);

  useEffect(() => {
    if (objectTypeId) {
      tableDrawerDs.setQueryParameter('objectTypeId', objectTypeId);
      tableDrawerDs.query();
    }
  }, [objectTypeId]);

  const handleCreateEventRequestType = () => {
    const newLine = tableDrawerDs.create(
      {
        columnType: 'DATE',
        eventFlag: 'N',
        displayFlag: 'N',
        enableFlag: 'N',
      },
      0,
    );
    newLine.setState('editing', true);
  };

  const handleDeleteObjectType = record => {
    deleteEventObjectTypeDrawer.run({
      params: [record.get('objectColumnId')],
      onSuccess: () => {
        tableDrawerDs.delete(record, false);
        // @ts-ignore
        notification.success();
      },
    });
  };

  const handleEditMessage = record => {
    record.setState('editing', true);
  };

  const handleCleanLine = record => {
    if (record.get('objectTypeId')) {
      record.reset();
      record.setState('editing', false);
    } else {
      tableDrawerDs.delete(record, false);
    }
  };

  const handleSave = async record => {
    const recordData = record.toData();
    const validate = await record.validate();
    if (!validate) {
      return;
    }
    recordData.description = recordData.description || '';
    recordData.objectTypeId = objectTypeId;

    if (recordData._tls) {
      const newTls = { ...recordData._tls };
      Object.keys(newTls).forEach(_fieldName => {
        if (
          newTls[_fieldName] &&
          Object.keys(newTls[_fieldName]) &&
          Object.keys(newTls[_fieldName]).length > 0
        ) {
          Object.keys(newTls[_fieldName]).forEach(_tlsKey => {
            newTls[_fieldName][_tlsKey] = newTls[_fieldName][_tlsKey] || '';
          });
        }
      });
      recordData._tls = newTls;
    }
    return saveEventObjectTypeDrawer.run({
      params: recordData,
      onSuccess: res => {
        record.reset();
        record.setState('editing', false);
        Object.keys(recordData).forEach(_key => {
          record.init(_key, recordData[_key]);
        });
        Object.keys(res).forEach(_key => {
          record.init(_key, res[_key]);
        });
        // @ts-ignore
        record.status = 'sync';
        // @ts-ignore
        notification.success();
        if (objectSQLDrawer) {
          objectSQLDrawer.close();
          objectSQLDrawer = undefined;
        }
      },
    });
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleCreateEventRequestType}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDeleteObjectType(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'lineNumber',
      // width: 150,
      editor: record => record.getState('editing') && <NumberField />,
    },
    {
      name: 'columnField',
      // width: 150,
      editor: record => record.getState('editing') && <TextField />,
    },
    {
      name: 'columnType',
      // width: 200,
      editor: record => record.getState('editing') && <Select clearButton={false} />,
    },
    {
      name: 'columnTitle',
      // width: 120,
      editor: record =>
        record.getState('editing') && (
          <IntlField
            modalProps={{ title: intl.get(`${modelPrompt}.description`).d('对象类型描述') }}
          />
        ),
    },
    {
      name: 'eventFlag',
      // width: 120,
      renderer: props => <EnableRender {...props} />,
      editor: record => record.getState('editing') && <Switch />,
    },
    {
      name: 'displayFlag',
      // width: 250,
      renderer: props => <EnableRender {...props} />,
      editor: record => record.getState('editing') && <Switch />,
    },
    {
      name: 'enableFlag',
      // width: 90,
      renderer: props => <EnableRender {...props} />,
      editor: record => record.getState('editing') && <Switch />,
    },
    {
      name: 'operator',
      // width: 120,
      align: ColumnAlign.center,
      renderer: ({ record }) =>
        record?.getState('editing') ? (
          <>
            <a onClick={() => handleCleanLine(record)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </a>
            <a style={{ marginLeft: '10px' }} onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </a>
          </>
        ) : (
          <>
            <a onClick={() => handleEditMessage(record)}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </a>
          </>
        ),
    },
  ];

  return <Table dataSet={tableDrawerDs} columns={columns} />;
};

export default ObjectTypeDrawer;
