/**
 * @Description: 分层审核计划-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-08-16 11:31:24
 * @LastEditTime: 2023-08-16 11:31:24
 * @LastEditors: <<EMAIL>>

*/
import React, { useEffect, useState } from 'react';
import { DataSet, Table, Row, Col, Spin, Select } from 'choerodon-ui/pro';
import { Popover, Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ColumnLock, TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { useDataSetEvent } from 'utils/hooks';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import moment from 'moment';
import { listTableDS } from '../stores/ListDS';
import { detailWeekTableDS } from '../stores/DetailDS';
import styles from '../index.modules.less';

import { fetchReviewPlanConfig } from '../services';

const { Option } = Select;

const { Panel } = Collapse;

const modelPrompt = 'tarzan.hierarchicalAuditPlan';

const HierarchicalAuditPlanList = props => {
  const {
    tableDs,
    match: { path },
  } = props;

  const [id, setId]: any = useState();
  const [colHeaderList, setColHeaderList]: any = useState([]);

  const [detailWeekTableDs, setDetailWeekTableDs] = useState(
    new DataSet({
      ...detailWeekTableDS(),
    }),
  );

  // 查询审核计划明细信息
  const fetchReviewPlan = useRequest(fetchReviewPlanConfig(), {
    manual: true,
    needPromise: true,
  });

  const handleQueryFirstLine = queryProps => {
    queryLineDetail(queryProps?.dataSet?.current);
  };

  useDataSetEvent(tableDs, 'load', handleQueryFirstLine);

  useEffect(() => {
    if (tableDs) {
      tableDs.query(props.tableDs.currentPage);
    } else {
      tableDs.query();
    }
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'layerReviewPlanCode',
      lock: ColumnLock.left,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
      width: 220,
    },
    {
      name: 'siteLov',
    },
    {
      name: 'planYear',
    },
    {
      name: 'planMonth',
    },
    {
      name: 'layerReviewPlanStatus',
    },
  ];

  const handleEdit = record => {
    props.history.push(
      `/hwms/hierarchical-audit-plan/audit-plan/detail/${record.get('layerReviewPlanId')}`,
    );
  };

  const handleCreate = () => {
    props.history.push(`/hwms/hierarchical-audit-plan/audit-plan/detail/create`);
  };

  const headerRowClick = record => {
    queryLineDetail(record);
  };

  const queryLineDetail = record => {
    const key = record?.get('layerReviewPlanId');
    getAuditInformation(key);
  };

  const getAuditInformation = async key => {
    const _detailWeekTableDs = new DataSet({
      ...detailWeekTableDS(),
    });
    if (key) {
      // 判断单据当前进度
      const _res = await fetchReviewPlan.run({
        tempUrl: fetchReviewPlanConfig(key).url,
      });

      if (_res?.success) {
        const _colHeaderList: any = [];
        const { qisLayerReviewGroupList } = _res.rows;
        const newqisLayerReviewGroupList: any = [];
        (qisLayerReviewGroupList || []).forEach(item => {
          const { qisLayerReviewWeekList, ...other } = item;
          (qisLayerReviewWeekList || []).forEach((subItem, index) => {
            other[`qisLayerReviewWeekListItem${index}`] = [
              ...(subItem.qisLayerReviewPlandtlList || []),
            ];
            if (!_colHeaderList[index]) {
              _colHeaderList.push({
                showFlag: true,
                beginDate: subItem.beginDate,
                endDate: subItem.endDate,
              });
            } else {
              if (!_colHeaderList[index].beginDate) {
                _colHeaderList[index].beginDate = subItem.beginDate;
              }
              if (!_colHeaderList[index].endDate) {
                _colHeaderList[index].endDate = subItem.endDate;
              }
            }
          });
          let employeeList: any = [];
          if (typeof other.groupMember === 'string') {
            try {
              employeeList = [...(JSON.parse(other.groupMember)?.groupMember || [])];
            } catch (e) {
              // 尝试删除多余属性
            }
          }
          let leaderLov = {};
          const membersLov: any = [];

          employeeList.forEach(employeeItem => {
            if (employeeItem.leaderFlag) {
              leaderLov = {
                ...employeeItem,
              };
            } else {
              membersLov.push(employeeItem);
            }
          });

          other.leaderLov = leaderLov;
          other.membersLov = membersLov;
          newqisLayerReviewGroupList.push(other);
        });
        setColHeaderList(_colHeaderList);
        _detailWeekTableDs.loadData(newqisLayerReviewGroupList);
      } else {
        setColHeaderList([]);
        _detailWeekTableDs.loadData([]);
      }
      setDetailWeekTableDs(_detailWeekTableDs);
    } else {
      setColHeaderList([]);
      _detailWeekTableDs.loadData([]);
      setDetailWeekTableDs(_detailWeekTableDs);
    }
  };

  const createOtherColumns = () => {
    const otherColumns: ColumnProps[] = [];
    let weekindex = 0;
    colHeaderList.forEach((item, index) => {
      if (item.showFlag) {
        weekindex++;
        otherColumns.push({
          name: `qisLayerReviewWeekListItem${index}`,
          title: `${intl.get(`${modelPrompt}.number.squence`).d('第')}${weekindex}${intl.get(`${modelPrompt}.number.week`).d('周')}`,
          hidden: !item.showFlag,
          width: 400,
          align: ColumnAlign.center,
          header: (record: any) => {
            return (
              <div>
                <div>
                  {record?.title}
                  <br />
                  {`${moment(item.beginDate).format('yyyy-MM-DD')} - ${moment(item.endDate).format(
                    'yyyy-MM-DD',
                  )}`}
                </div>
              </div>
            );
          },
          renderer: ({ record }) => {
            const _list = record?.get(`qisLayerReviewWeekListItem${index}`) || [];
            return _list.map(item => {
              return (
                <Row key={item.uuid}>
                  <Col span={12}>{item.operationName}</Col>
                  <Col span={12}>
                    <Popover content={intl.get(`${modelPrompt}.taskCreateDate`).d('任务创建日期')}>{item.taskCreationDate}</Popover>
                  </Col>
                </Row>
              );
            });
          },
        });
      }
    });
    return otherColumns;
  };

  const detailWeekTableColumns: ColumnProps[] = [
    {
      name: 'layerReviewGroupId',
      title: intl.get(`${modelPrompt}.groupWeek`).d('小组/周次'),
      children: [
        {
          name: `leaderLov`,
          title: intl.get(`${modelPrompt}.column.leader`).d('组长'),
          width: 200,
        },
        {
          name: `membersLov`,
          title: intl.get(`${modelPrompt}.column.groupmen`).d('组员'),
          width: 200,
        },
      ],
    },
    ...createOtherColumns(),
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.HierarchicalAuditPlan`).d('分层审核计划')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.create`,
              type: 'button',
              meaning: '列表页-新建',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleCreate()}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Spin spinning={fetchReviewPlan.loading}>
          <Table
            searchCode="fcshjh1"
            customizedCode="fcshjh1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            onRow={({ record }) => ({
              onClick: () => headerRowClick(record),
            })}
            queryFields={{
              planMonth: (
                <Select>
                  <Option value="1">1{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="2">2{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="3">3{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="4">4{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="5">5{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="6">6{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="7">7{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="8">8{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="9">9{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="10">10{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="11">11{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                  <Option value="12">12{intl.get(`${modelPrompt}.month`).d('月')}</Option>
                </Select>
              ),
            }}
          />
          <Collapse collapsible="icon" bordered={false} defaultActiveKey={['panel1']}>
            <Panel
              header={intl.get(`${modelPrompt}.hierarchicalAuditPlanInfo`).d('分层审核计划明细')}
              key="panel1"
            >
              <div className={styles['table-conrtal-box']}>
                <div className="table-box">
                  <Table
                    headerRowHeight="auto"
                    rowHeight="auto"
                    autoWidth
                    border
                    dataSet={detailWeekTableDs}
                    columns={detailWeekTableColumns}
                  />
                </div>
              </div>
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...listTableDS(),
      });

      return {
        tableDs,
      };
    },
    { cacheState: true, keepOriginDataSet: true },
  )(HierarchicalAuditPlanList),
);
