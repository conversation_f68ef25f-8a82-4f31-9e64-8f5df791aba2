/**
 * @Description: 物料装配清单 - 入口页面（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/8/15 9:26
 * @LastEditTime: 2023-05-18 15:00:22
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { AssemblyList, AssemblyListProps } from '../../../components/C7nAssemblyCommon/AssemblyList';

const C7nMaterialBomList = props => {
  // 装配组件列表页入参
  const assemblyListProps: AssemblyListProps = {
    history: props.history,
    location: props.location,
    featureTitle: intl.get('tarzan.product.bom.title.methodBom').d('物料装配清单'), // 列表页标题title
    exportServerCode: BASIC.TARZAN_METHOD, // 导出接口访问的服务
    uiServerCode: BASIC.TARZAN_METHOD, // 功能接口（如列表页的查询接口）要访问的服务
    match: props.match,
    typeGroup: 'METHOD_BOM_TYPE', // 查询条件与详情页中类型的值集code
    searchCode: 'wlzpqd1',
    customizedCode: 'wlzpqd1',
    customizeTable: props.customizeTable,
    custCode: 'MATERIAL_BOM_LIST',
    pageType: 'materialBom',
  };

  return <AssemblyList {...assemblyListProps} />;
};

export default flow(
  formatterCollections({ code: ['tarzan.product.bom', 'tarzan.common'] }),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_BOM_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_BOM_LIST.LIST`] }),
)(C7nMaterialBomList);
