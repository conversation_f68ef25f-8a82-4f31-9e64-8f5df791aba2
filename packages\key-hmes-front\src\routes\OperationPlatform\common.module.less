#operationPlatform{
  // 多选框
  :global{
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper{
      color: #fff !important;
      margin-right: 16px !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper:not(.c7n-pro-checkbox-button) .c7n-pro-checkbox:checked + .c7n-pro-checkbox-inner{
      background: #4BE3EE !important;
      border-color: #4BE3EE !important;
    }
    .c7n-pro-checkbox-inner{
      background-color: #608da5 !important;
      border-color: transparent !important;
    }
  }
  // 按钮
  :global{
    .c7n-pro-btn{
      font-size: unset !important;
    }
    .c7n-pro-btn.c7n-pro-btn-primary{
      background: rgb(0, 212, 205);
      color: white;
      border-color: rgb(0, 212, 205);
      height: 36px !important;
      line-height: 1;
    }
    .c7n-pro-btn.c7n-pro-btn-primary.c7n-pro-btn-raised:not(.c7n-pro-btn-disabled):not(.c7n-pro-btn-loading):hover{
      background: rgb(0, 212, 205);
      color: white;
      border-color: rgb(0, 212, 205);
    }
    .c7n-pro-btn.c7n-pro-btn-default{
      background: rgb(50, 97, 127);
      color: white;
      border: none !important;
      height: 36px !important;
      outline: none !important;
      line-height: 1;
    }
    .c7n-pro-btn.c7n-pro-btn-default.c7n-pro-btn-raised:not(.c7n-pro-btn-disabled):not(.c7n-pro-btn-loading):hover{
      background: rgb(50, 97, 127);
      color: white;
      border-color: rgb(50, 97, 127);
    }
  }
  // textField/input/number/output
  :global{
    .c7n-pro-field-label.c7n-pro-field-label.c7n-pro-field-label{
      color: #33F1FF !important;
      font-size: unset !important;
    }
    .c7n-pro-input-wrapper.c7n-pro-input-wrapper{
      font-size: 1vw !important;
      ::selection, body ::selection{
        background: #11C2CF !important;
      }
    }
    .c7n-pro-input-wrapper.c7n-pro-input-wrapper label input{
      font-size: unset !important;
    }
    .c7n-pro-input-wrapper.c7n-pro-input-wrapper label .c7n-pro-input{
      // border: 1px solid rgba(255, 255, 255, 0.5) !important;
      border-color: rgba(255, 255, 255, 0.5);
      background-color: rgb(47, 94, 129) !important;
      color: #fff !important;
      height: 36px !important;
      // &:focus{
      //   border: 1px solid #11C2CF !important;
      // }
    }
    .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper{
      font-size: unset !important;
      ::selection, body ::selection{
        background: #11C2CF !important;
      }
    }
    .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper label .c7n-pro-input-number{
      // border: 1px solid rgba(255, 255, 255, 0.5) !important;
      border-color: rgba(255, 255, 255, 0.5);
      background-color: rgb(47, 94, 129) !important;
      color: #fff !important;
      height: 36px !important;
      font-size: unset !important;
    }
    .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper.c7n-pro-input-number-disabled label input{
      color: rgba(255, 255, 255, 0.3) !important;
      -webkit-text-fill-color: rgba(255, 255, 255, 0.3) !important;
    }
    .c7n-pro-output.c7n-pro-output{
      color: #fff !important;
      font-size: unset !important;
    }
  }
   // lov
  :global{
    .c7n-pro-modal.c7n-pro-modal .c7n-pro-modal-header .c7n-pro-modal-title{
      color: #fff !important;
      font-size: unset !important;
    }
    .c7n-pro-table-filter-content{
      background-color: #2f5e81 !important;
      height: auto !important;
      .c7n-pro-table-filter-label{
        color: #fff !important;
        font-size: unset !important;
      }
      .c7n-pro-table-filter-item .c7n-pro-input-wrapper.c7n-pro-input-wrapper label .c7n-pro-input{
       border: none !important;
      }
      .c7n-pro-field-label label > span{
        font-size: unset !important;
      }
    }
  }
  // 表格
  :global{
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table table{
      font-size: 1vw !important;
    }
    .c7n-pro-table.c7n-pro-table{
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        background-color: transparent;
      }

      ::-webkit-scrollbar-thumb {
        border-radius: 14px;
        background: #88a9bc;
      }
    }
    .c7n-pro-table.c7n-pro-table{
      border: none !important;
    }
    .c7n-pro-table-add-fields{
      color: #fff !important;
    }
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table .c7n-pro-table-cell-inner{
      height: auto !important;
      white-space: normal !important;
    }
    .c7n-pro-table:not(.c7n-pro-table-bordered) .c7n-pro-table-tbody{
      border: none !important;
    }
    .c7n-pro-table:not(.c7n-pro-table-bordered) .c7n-pro-table-thead.c7n-pro-table-thead .c7n-pro-table-cell {
      background-color: #2A6382 !important;
      color: #33F1FF !important;
      border: none !important;
      font-size: 1vw !important;
    }
    .c7n-pro-table.c7n-pro-table .c7n-pro-table-tbody .c7n-pro-table-row .c7n-pro-table-cell{
      background-color: #38708F;
      color: #fff !important;
      font-size: 1vw !important;
      border: none !important;
    }
    .c7n-pro-table .c7n-pro-table-tbody .c7n-pro-table-row{
      background-color: #38708F !important;
    }
    .c7n-pro-pagination.c7n-pro-pagination-wrapper .c7n-pro-pagination-perpage{
      color: #fff;
    }
    .c7n-pro-pagination.c7n-pro-pagination-wrapper .c7n-pro-pagination-page-info{
      color: #fff;
    }
    .c7n-pro-pagination.c7n-pro-pagination-wrapper .c7n-pro-btn.c7n-pro-btn-flat.c7n-pro-btn-wrapper.c7n-pro-pagination-pager.c7n-pro-btn-disabled i.icon.icon::before{
      color: #fff;
    }
    .c7n-pro-pagination.c7n-pro-pagination-wrapper .c7n-pro-btn.c7n-pro-btn-flat.c7n-pro-btn-wrapper.c7n-pro-pagination-pager i.icon::before{
      color: #fff;
    }
    .c7n-pro-pagination.c7n-pro-pagination-wrapper .c7n-pro-btn.c7n-pro-btn-flat.c7n-pro-btn-wrapper.c7n-pro-pagination-pager.c7n-pro-pagination-pager-jump-next > span{
      color: #fff;
    }
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper{
      font-size: 1vw !important;
      ::selection, body ::selection{
        background: #11C2CF !important;
      }
    }
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label{
      background-color: rgb(47, 94, 129) !important;
      color: #fff !important;
    }
    .c7n-pro-select-wrapper.c7n-pro-select-empty{
      color: rgba(255, 255, 255, 0.5) !important;
    }
    // 分页
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label input{
      color: white !important;
      height: 36px !important;
      font-size: unset !important;
      border: 1px solid rgba(255, 255, 255, 0.5) !important;
      line-height: 1;
    }
    .c7n-pro-select-wrapper.c7n-pro-select-empty label input{
      color: rgba(255, 255, 255, 0.5) !important;
      height: 36px !important;
      font-size: unset !important;
      border: 1px solid rgba(255, 255, 255, 0.5) !important;
      line-height: 1;
    }
    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }
    .c7n-pro-input-wrapper.c7n-pro-input-wrapper.c7n-pro-input-disabled label input{
      color: rgba(255, 255, 255, 0.3) !important;
      -webkit-text-fill-color: rgba(255, 255, 255, 0.3) !important;
    }
    // .c7n-pro-btn-wrapper {
    //   background-color: #3c87ad !important;
    //   color: white !important;
    // }

    .icon {
      font-size: 1.2vw !important;
    }
    .icon.icon-close{
      color: #fff !important;
    }
    .c7n-pro-select-inner-button.c7n-pro-select-inner-button .icon-close::before, .c7n-pro-select-clear-button.c7n-pro-select-clear-button .icon-close::before{
      color: rgba(255, 255, 255, 0.3) !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
      font-size: unset !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
      font-size: unset !important;
    }
  }
  // modal
  :global{
    .c7n-pro-modal.c7n-pro-modal .c7n-pro-modal-content{
      font-size: 1vw !important;
    }
    .c7n-pro-modal.c7n-pro-modal .c7n-pro-modal-content .c7n-pro-modal-body{
      color: #fff !important;
      font-size: 1vw !important;
    }
  }
  //timeLine
  :global{
    .c7n-timeline-item{
      font-size: unset !important;
    }
    .c7n-timeline.c7n-timeline .c7n-timeline-item .c7n-timeline-item-content{
      font-size: unset !important;
    }
    .c7n-timeline-item-head-green{
      background-color: #00bf96 !important;
    }
    .c7n-timeline-item-head-red{
      background-color: #d50000 !important;
    }
  }
  // Radio
  :global{
    .c7n-pro-radio-inner{
      background-color: #2F5E81 !important;
      border-color: #01E1EF !important;
    }
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper:not(.c7n-pro-radio-button) .c7n-pro-radio:checked + .c7n-pro-radio-inner{
      border-color: #2F5E81 !important;
      background-color: #01E1EF !important;
    }
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper:not(.c7n-pro-radio-button) .c7n-pro-radio-label{
      color: #fff !important;
    }
  }
  // tree
  :global{
    .c7n-tree.c7n-tree{
      font-size: unset !important;
    }
  }
  // tag
  :global{
    .c7n-tag.c7n-tag{
      font-size: unset !important;
      height: 28px !important;
      line-height: 22px !important;
    }
  }
  // progress
  :global{
    .c7n-progress-outer{
      width: 14vw !important;
    }
    .c7n-progress-line{
      font-size: unset !important;
    }
    .c7n-progress-text{
      font-size: unset !important;
    }
    .c7n-progress-line .c7n-progress-inner{
      background-color: rgba(42, 99, 130, 1) !important;
      height: 15px !important;
      overflow: hidden !important;
    }
    .c7n-progress-line.c7n-progress-status-normal .c7n-progress-bg{
      background: linear-gradient(90deg, rgba(51, 241, 255, 1) 0%, rgba(17, 194, 207, 1) 100%) !important;
      height: 15px !important;
    }
    .c7n-progress-line.c7n-progress-status-success .c7n-progress-bg{
      // background: linear-gradient(90deg, rgba(51, 241, 255, 1) 0%, rgba(17, 194, 207, 1) 100%) !important;
      background-color: rgba(242, 58, 80, 1) !important;
      height: 15px !important;
    }
  }
  // select
  :global{
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-select-suffix .icon{
      color: rgba(255, 255, 255, 1) !important;
    }
  }
  // icon-antion
  :global{
    .anticon.anticon-caret-right{
      color: rgba(255, 255, 255, 0.7) !important;
    }
    .anticon.anticon-caret-down{
      color: rgba(51, 241, 255, 1) !important;
    }
  }
  :global{
    .current-line___34D14{
      opacity: 1 !important;
    }
  }
  // popup
  :global{
    .c7n-pro-popup{
      background-color: #38708f !important;
    }
    .icon.icon-search{
      color: #fff !important;
    }
    .c7n-pro-select-dropdown-menu.c7n-pro-select-dropdown-menu .c7n-pro-select-dropdown-menu-item{
      color: rgba(255, 255, 255, 0.85) !important;
    }
    .c7n-pro-select-dropdown-menu.c7n-pro-select-dropdown-menu .c7n-pro-select-dropdown-menu-item.c7n-pro-select-dropdown-menu-item-active{
      background-color: rgba(17, 194, 207, 1) !important;
      color: #fff !important;
    }
    .c7n-pro-select-dropdown-menu.c7n-pro-select-dropdown-menu .c7n-pro-select-dropdown-menu-item.c7n-pro-select-dropdown-menu-item-selected{
      background: none !important;
    }
    .c7n-pro-select-dropdown-menu.c7n-pro-select-dropdown-menu .c7n-pro-select-dropdown-menu-item.c7n-pro-select-dropdown-menu-item-disabled.c7n-pro-select-dropdown-menu-item-disabled{
      background-color: rgba(17, 194, 207, 1) !important;
      color: #fff !important;
    }
  }
  // badge
  :global{
    .c7n-badge-status-text{
      font-size: unset !important;
    }
  }
}
