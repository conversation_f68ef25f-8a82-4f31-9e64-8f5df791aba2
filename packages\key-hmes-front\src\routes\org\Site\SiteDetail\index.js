/**
 * @Description: 站点维护-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-02-02 16:35:18
 * @LastEditTime: 2023-05-18 11:27:03
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useRef } from 'react';
import { Button } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Header, Content } from 'components/Page';
import { AttributeDrawer } from '@components/tarzan-ui';
import Detail from '../components/Detail';

// const TABLENAME = 'mt_mod_site_attr';
const modelPrompt = 'tarzan.model.org.site';

const SiteDetail = props => {
  const {
    match,
    custConfig,
  } = props;
  const {
    path,
    params: { siteId },
  } = match;
  const [canEdit, setCanEdit] = useState(siteId === 'create');
  const childRef = useRef();

  const handleSave = async () => {
    const { success, newKid } = await childRef.current.submit();
    if (success) {
      setCanEdit(prev => !prev);
      props.history.push(`/hmes/organization-modeling/site/detail/${newKid}`);
    }
  };

  const handleCancel = () => {
    if (siteId === 'create') {
      props.history.push('/hmes/organization-modeling/site/list');
    } else {
      childRef.current.reset();
      setCanEdit(prev => !prev);
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.siteMaintenance`).d('站点维护')}
        backPath="/hmes/organization-modeling/site/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <AttributeDrawer
          // tablename={TABLENAME}
          className="org.tarzan.model.domain.entity.MtModSite"
          kid={siteId}
          canEdit={canEdit}
          disabled={siteId === 'create'}
          serverCode={BASIC.TARZAN_MODEL}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.SITE_DETAIL.BUTTON`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <Detail canEdit={canEdit} ref={childRef} kid={siteId} columns={3} componentType="SITE" />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.model.org.site', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.SITE_DETAIL.BUTTON`],
})(SiteDetail));
