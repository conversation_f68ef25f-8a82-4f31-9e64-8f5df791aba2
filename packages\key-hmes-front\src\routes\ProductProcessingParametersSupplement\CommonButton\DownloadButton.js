import React from 'react';
import { Button as PermissionButton } from 'components/Permission';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import intl from 'utils/intl';
import { BASIC, API_HOST } from '@utils/config';
// import myInstance from '@utils/myAxios';
// import { notification } from 'choerodon-ui';

const tenantId = getCurrentOrganizationId();
// const prefix = '/mes-38546';

const modelPrompt = 'tarzan.inventory.initial.model';

const DownloadButton = ({ path }) => {
  const download = () => {
    // const queryData = dataSet.queryDataSet.toData()[0];
    // if (queryData && queryData.siteId && queryData.materialId && queryData.equipmentId) {
    //   const params = {
    //     siteId: queryData.siteId,
    //     siteCode: queryData.siteCode,
    //     materialId: queryData.materialId,
    //     materialCode: queryData.materialCode,
    //     equipmentId: queryData.equipmentId,
    //     equipmentCode: queryData.equipmentCode,
    //     workcellId: queryData.workcellId,
    //     workcellCode: queryData.workcellCode,
    //   };
    //   const importUrl = `${prefix}/v1/${tenantId}/hme-product-ps-pa-supps/excel/output/get?access_token=${getAccessToken()}`;
    //   myInstance.post(importUrl, params).then(res => {
    //     const { success } = res.data;

    //     const blob = new Blob([res.data], {
    //       type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    //     });
    // const objectUrl = URL.createObjectURL(blob);
    const elink = document.createElement('a');
    elink.style.display = 'none';
    // elink.href = `${API_HOST}${
    //   prefix
    // }/v1/${tenantId}/hme-product-ps-pa-supps/excel/output/get?access_token=${getAccessToken()}&siteId=${queryData.siteId}&siteCode=${queryData.siteCode}&materialId=${queryData.materialId}&materialCode=${queryData.materialCode}&equipmentId=${queryData.equipmentId}&equipmentCode=${queryData.equipmentCode}`;
    elink.href = `${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot/download/model-attr/ui?access_token=${getAccessToken()}`;
    document.body.appendChild(elink);
    elink.click();
    document.body.removeChild(elink);
    // if (success) {
    //   notification.error({ message: res.data.message });
    // }
    // else {
    // }
    //   });
    // } else {
    //   notification.error({ message: '请选择查询条件的必输项' });
    // }
  };
  return (
    <PermissionButton
      type="c7n-pro"
      icon="get_app"
      onClick={download}
      permissionList={[
        {
          code: `${path}.button.tempalte`,
          type: 'button',
          meaning: '列表页-模板下载按钮',
        },
      ]}
    >
      {intl.get(`${modelPrompt}.button.download.importTempalte`).d('导入模板获取')}
    </PermissionButton>
  );
};

export default DownloadButton;
