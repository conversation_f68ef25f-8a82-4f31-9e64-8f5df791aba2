/**
 * @since 2020/06/17
 * <AUTHOR> <kejie.lu@hand-china>
 */
import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import {FieldIgnore, FieldType} from "choerodon-ui/pro/lib/data-set/enum";

const organizationId = getCurrentOrganizationId();
const promptCode = 'amtc.woType.model.woType';

function detailFormDS() {
  return {
    autoQuery: false,
    selection: false,
    primaryKey: 'woTypeId',
    paging: false,
    fields: [
      {
        name: 'woBasicType',
        type: 'string',
        label: intl.get(`${promptCode}.woBasicType`).d('基础大类'),
        lookupCode: 'AMTC.WO_BASIC_TYPE',
        required: true,
      },
      {
        name: 'woTypeName',
        type: 'intl',
        label: intl.get(`${promptCode}.woTypeName`).d('工单类型名称'),
        required: true,
        maxLength: 40,
      },
      {
        name: 'woTypeCode',
        type: 'string',
        label: intl.get(`${promptCode}.woTypeCode`).d('类型代码'),
        required: true,
        maxLength: 40,
      },
      {
        name: 'icon',
        type: 'string',
        label: intl.get(`${promptCode}.icon`).d('图标'),
        dynamicProps: {
          maxLength: ({ record }) => {
            return record?.get('iconTypeCode') === 'USER_DEFINED' ? 1 : null;
          },
        },
      },
      {
        name: 'iconTypeCode',
        type: 'string',
        label: intl.get(`${promptCode}.icon`).d('图标'),
        defaultValue: 'ICON',
        lookupCode: 'AMMT.ICONTYPE',
      },
      {
        name: 'wonumRuleCodeLov',
        label: intl.get(`${promptCode}.woNumRule`).d('默认编码规则'),
        type: 'object',
        lovCode: 'HALM.CODE_RULE_URL',
        lovPara: {
          tenantId: organizationId,
        },
        ignore: 'always',
        required: true,
      },
      {
        name: 'woNumRuleCode',
        type: 'string',
        bind: 'wonumRuleCodeLov.ruleCode',
      },
      {
        name: 'woNumRuleCodeMeaning',
        label: intl.get(`${promptCode}.woNumRule`).d('默认编码规则'),
        type: 'string',
        bind: 'wonumRuleCodeLov.ruleName',
      },
      {
        name: 'manualcreateEnableFlag',
        type: 'number',
        label: intl.get(`${promptCode}.manualcreateEnableFlag`).d('允许手工创建'),
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'enabledFlag',
        type: 'number',
        label: intl.get(`${promptCode}.enabledFlag`).d('是否启用'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      {
        name: 'billingInfoFlag',
        type: 'number',
        label: intl.get(`${promptCode}.billingInfoFlag`).d('结算页签'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'description',
        type: 'intl',
        label: intl.get(`${promptCode}.description`).d('描述'),
        trim: 'none',
        maxLength: 240,
      },
      {
        name: 'scheduleRequirmentStatus',
        type: 'string',
        label: intl.get(`${promptCode}.scheduleRequirmentStatus`).d('计划状态'),
        lookupCode: 'AMTC.ORDER_PLAN_STATUS',
      },
      {
        name: 'woopCompletedFlag',
        type: 'number',
        label: intl.get(`${promptCode}.woopCompletedFlag`).d('完工前任务均需完工'),
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'completionRuleCode',
        type: 'string',
        label: intl.get(`${promptCode}.completionRuleCode`).d('工单完成规则'),
        defaultValue: 'AUTOMATE_WITH_TASKS',
        lookupCode: 'AMTC.ORDER_COMPLETION_RULE', // 随任务自动完工
      },
      {
        name: 'finishModeCode',
        type: 'string',
        label: intl.get(`${promptCode}.finishModeCode`).d('工单结束规则'),
        lookupCode: 'AMTC.NEED_TO_CLOSE',
      },
      {
        name: 'failureRequiredFlag',
        type: 'string',
        label: intl.get(`${promptCode}.failureRequiredFlag`).d('故障登记规则'),
        lookupCode: 'AMTC.NEED_RC_RECORD',
      },
      {
        name: 'woopPicDefRuleCode',
        type: 'string',
        label: intl.get(`${promptCode}.woopPicDefRuleCode`).d('任务负责人默认规则'),
        defaultValue: 'WO_PIC_GROUP',
        lookupCode: 'AMTC.WOOP_PIC_DEF_RULE', // 工单负责人组
      },
      {
        name: 'workObjCtrlCode',
        type: 'string',
        label: intl.get(`${promptCode}.workObjCtrlCode`).d('工作对象限制'),
        lookupCode: 'AMTC.MUST_BE_PROVIDED',
      },
      {
        name: 'enableOrgFlag',
        type: 'number',
        label: intl.get(`${promptCode}.enableOrgFlag`).d('维护需求组织/合作伙伴'),
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'checklistsFlag',
        type: 'number',
        label: intl.get(`${promptCode}.checklistsFlag`).d('工单检查项页签'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      {
        name: 'outsourceFlag',
        type: 'number',
        label: intl.get(`${promptCode}.outsourceFlag`).d('费用页签'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'wprevFlag',
        type: 'number',
        label: intl.get(`${promptCode}.wprevFlag`).d('等待前序'),
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'itemsFlag',
        type: 'number',
        label: intl.get(`${promptCode}.itemsFlag`).d('物料页签'),
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'workcenterPeopleFlag',
        type: 'number',
        label: intl.get(`${promptCode}.workcenterPeopleFlag`).d('人员页签'),
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'costFlag',
        type: 'number',
        label: intl.get(`${promptCode}.costFlag`).d('成本页签'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'requiredBfWorkCode',
        type: 'string',
        label: intl.get(`${promptCode}.requiredBfWorkCode`).d('开工签到规则'),
        defaultValue: 'NONE',
        lookupCode: 'AMTC.REQUIRED_BF_WORK',
      },
      {
        name: 'costObjectLimitType',
        type: 'string',
        label: intl.get(`${promptCode}.costObjectLimitType`).d('成本对象限制'),
        lookupCode: 'HALM.COST_OBJECT_LIMIT_TYPE',
        defaultValue: 'DISPLAY&OPTIONAL',
      },
      {
        name: 'defaultCostObjectType',
        type: 'string',
        label: intl.get(`${promptCode}.defaultCostObjectType`).d('默认成本对象类型'),
        lookupCode: 'HPFM.FINANCIAL_CODE_TYPE',
        dynamicProps: {
          required: ({ record }) => record.get('costObjectLimitType') === 'DISPLAY&MANDATORY',
          disabled: ({ record }) => record.get('costObjectLimitType') === 'NOT_DISPLAY',
        },
      },
      {
        name: 'showSourceFlag',
        type: 'number',
        label: intl.get(`${promptCode}.showSourceFlag`).d('显示报告来源'),
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'defaultPriorityLov',
        type: 'object',
        label: intl.get(`${promptCode}.defaultPriority`).d('计划优先级'),
        required: true,
        ignore: 'always',
        lovCode: 'AMTC.PRIORITIES',
      },
      {
        name: 'defaultPriorityId',
        type: 'number',
        bind: 'defaultPriorityLov.priorityId',
      },
      {
        name: 'defaultPriorityName',
        type: 'string',
        bind: 'defaultPriorityLov.priorityName',
        label: intl.get(`${promptCode}.defaultPriority`).d('计划优先级'),
      },
      {
        name: 'projectRelatedFlag',
        type: 'number',
        label: intl.get(`${promptCode}.projectRelatedFlag`).d('关联项目信息'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'compCheckFlag',
        type: 'number',
        label: intl.get(`${promptCode}.compCheckFlag`).d('工单完工验收'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'checkTempLov',
        type: 'object',
        label: intl.get(`${promptCode}.checkTemp`).d('工单验收模板'),
        lovCode: 'AMTC.EVALUATE_TEMP',
        lovPara: { tenantId: organizationId },
        valueField: 'evaluateTempCode',
        textField: 'evaluateTempName',
        dynamicProps: {
          required: ({ record }) => record.get('compCheckFlag') === 1,
        },
        ignore: 'always',
      },
      // 以code关联
      {
        name: 'checkTempCode',
        type: 'string',
        bind: 'checkTempLov.evaluateTempCode',
      },
      {
        name: 'checkTempId',
        type: 'number',
        bind: 'checkTempLov.evaluateTempId',
      },
      {
        name: 'checkTempName',
        type: 'string',
        bind: 'checkTempLov.evaluateTempName',
      },
      {
        name: 'workflowFlag',
        label: intl.get(`${promptCode}.workflowFlag`).d('启用工作流'),
        type: 'boolean',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'defWorkflowLov',
        label: intl.get(`${promptCode}.defWorkflow`).d('流程定义'),
        type: 'object',
        lovCode: 'HALM.WORKFLOW_DEF',
        lovPara: {
          tenantId: organizationId,
        },
        dynamicProps: {
          required: ({ record }) => {
            return record.get('workflowFlag') === 1;
          },
        },
      },
      {
        name: 'defWorkflowId',
        type: 'string',
        bind: 'defWorkflowLov.flowId',
      },
      {
        label: intl.get(`${promptCode}.defWorkflow`).d('流程定义'),
        name: 'defWorkflowName',
        type: 'string',
        bind: 'defWorkflowLov.flowName',
        // 查看时前面也显示必输的*提示
        dynamicProps: {
          required: ({ record }) => {
            return record.get('workflowFlag') === 1;
          },
        },
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/workorder-type/${data.id}`,
          method: 'GET',
        };
      },
      update: ({ data }) => {
        return {
          data: data[0],
          url: `${HALM_MTC}/v1/${organizationId}/workorder-type`,
          method: 'PUT',
        };
      },
      submit: ({ data, dataSet }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/workorder-type`,
          data: data[0],
          method: dataSet.requestType,
        };
      },
    },
    events: {
      update: ({ name, value, record }) => {
        if (name === 'workflowFlag') {
          record.set('defWorkflowLov', undefined);
        } else if (name === 'costObjectLimitType') {
          if (value === 'NOT_DISPLAY') {
            record.set('defaultCostObjectType', null);
          }
        } else if (name === 'compCheckFlag') {
          record.set('checkTempLov', null);
        } else if (name === 'requiredBfWorkCode') {
          if (['SCAN', 'RFID'].includes(value)) {
            record.set('workObjCtrlCode', 'LOCATION_OR_DEVICE');
          }
        }
      },
    },
  };
}

function reminderRulesDS() {
  return {
    autoQuery: false,
    selection: false,
    primaryKey: 'woTypeId',
    paging: false,
    fields: [
      {
        name: 'woTypeId',
      },
      {
        name: 'firstReminderInterval',
        type: FieldType.number,
        label: intl.get(`${promptCode}.firstReminderInterval`).d('一级提醒时长间隔（小时）'),
        pattern: /^[1-9]\d*$/,
      },
      {
        name: 'firstReminderByLov',
        type: FieldType.object,
        label: intl.get(`${promptCode}.firstReminderByName`).d('一级提醒人员'),
        ignore: FieldIgnore.always,
        lovCode: 'HWKF.EMPLOYEE',
        lovPara: { organizationId },
        textField: 'name',
        multiple: ',',
      },
      {
        name: 'firstReminderBy',
        multiple: ',',
        bind: 'firstReminderByLov.employeeId',
      },
      {
        name: 'firstReminderByName',
        label: intl.get(`${promptCode}.firstReminderByName`).d('一级提醒人员'),
        multiple: ',',
        bind: 'firstReminderByLov.name',
      },
      {
        name: 'secondReminderInterval',
        type: FieldType.number,
        label: intl.get(`${promptCode}.secondReminderInterval`).d('二级提醒时长间隔（小时）'),
        pattern: /^[1-9]\d*$/,
      },
      {
        name: 'secondReminderByLov',
        type: FieldType.object,
        label: intl.get(`${promptCode}.secondReminderByName`).d('二级提醒人员'),
        ignore: FieldIgnore.always,
        lovCode: 'HWKF.EMPLOYEE',
        lovPara: { organizationId },
        textField: 'name',
        multiple: ',',
      },
      {
        name: 'secondReminderBy',
        multiple: ',',
        bind: 'secondReminderByLov.employeeId',
      },
      {
        name: 'secondReminderByName',
        label: intl.get(`${promptCode}.secondReminderByName`).d('二级提醒人员'),
        multiple: ',',
        bind: 'secondReminderByLov.name',
      },
      {
        name: 'thirdReminderInterval',
        type: FieldType.number,
        label: intl.get(`${promptCode}.thirdReminderInterval`).d('三级提醒时长间隔（小时）'),
        pattern: /^[1-9]\d*$/,
      },
      {
        name: 'thirdReminderByLov',
        type: FieldType.object,
        label: intl.get(`${promptCode}.thirdReminderByName`).d('三级提醒人员'),
        ignore: FieldIgnore.always,
        lovCode: 'HWKF.EMPLOYEE',
        lovPara: { organizationId },
        textField: 'name',
        multiple: ',',
      },
      {
        name: 'thirdReminderBy',
        multiple: ',',
        bind: 'thirdReminderByLov.employeeId',
      },
      {
        name: 'thirdReminderByName',
        label: intl.get(`${promptCode}.thirdReminderByName`).d('三级提醒人员'),
        multiple: ',',
        bind: 'thirdReminderByLov.name',
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/workorder-type/time-rule/${data.id}`,
          method: 'GET',
        };
      },
      submit: ({ data }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/workorder-type/time-rule/save-or-update`,
          data: data[0],
          method: 'POST',
        };
      },
    },
  };
}

export { detailFormDS, reminderRulesDS };
