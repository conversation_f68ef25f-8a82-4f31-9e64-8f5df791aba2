/**
 * @Description: 入库属性维护-DS
 */
import intl from 'utils/intl';
import {DataSetSelection, FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.receipt.attribute.maintenance';
const tenantId = getCurrentOrganizationId();

const tableDS: (approve: boolean) => DataSetProps = (approve = false) => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'inWarehourseId',
  forceValidate: true,
  queryFields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'APEX_MES.TYPE_MATERIAL_LOV',
      noCache: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        type: 'D2',
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'materialLov.materialId',
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemModelCode`).d('物料型号代码'),
    },
  ],
  fields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'APEX_MES.TYPE_MATERIAL_LOV',
      noCache: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        type: 'D2',
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号代码'),
    },
    {
      name: 'gradingStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gearStatus`).d('分档状态'),
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'gradingLabel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gradingLabel`).d('分档标签'),
      lookupCode: 'APEX_MES.GRADING_LABEL',
    },
    {
      name: 'gradingLabelPrint',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gradingLabelPrint`).d('分档标签打印项目'),
      lookupCode: 'APEX_MES.GRADING_LABEL_PRINT',
      multiple: ',',
    },
    {
      name: 'gatherItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packingValidationProperties`).d('打包校验属性'),
      lookupCode: 'APEX_MES.IN_WAREHOUSE_GATHER',
      multiple: ',',
    },
    {
      name: 'assembleMarking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMarking`).d('装配喷码'),
    },
    {
      name: 'gradingMarking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gradingMarking`).d('分档喷码'),
    },
    {
      name: 'standardLoadQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardLoadQty`).d('标准装箱数量'),
      required: true,
      min: 0,
    },
    {
      name: 'positiveTabLength',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.positiveTabLength`).d('正极耳长度'),
    },
    {
      name: 'negativeTabLength',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.negativeTabLength`).d('负极耳长度'),
    },
    {
      name: 'tabAppear',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tabAppear`).d('极耳形貌'),
      lookupCode: 'APEX_MES.TAB_APPEAR',
    },
    {
      name: 'voltage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.voltage`).d('电压'),
    },
    {
      name: 'stickGlue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stickGlue`).d('贴胶'),
      lookupCode: 'APEX_MES.STICK_GLUE',
    },
    {
      name: 'breakSide',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.breakSide`).d('折边'),
      lookupCode: 'APEX_MES.BREAK_SIDE',
    },
    {
      name: 'specialRequirements',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialRequirements`).d('特殊要求'),
    },
  ],
  record: {
    dynamicProps: {
      // 有权限的角色才可选择初始化数据
      selectable: record => record.get('initialFlag') !== 'Y' && approve,
    },
  },
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-in-warehouses/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
