import React, { useEffect, useMemo, useRef } from 'react';
import { Table } from 'choerodon-ui/pro';
import { Row, Col, Badge, Popover } from 'choerodon-ui';
import { Content } from 'components/Page';
import intl from 'utils/intl';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import './index.less';

const modelPrompt = 'problem.table';

const Underway = props => {
  let page = 0;
  let totalPages = 1;
  const {
    // match: { path },
    ds,
    underwayQueryInfo,
    underwayLoading,
    history,
  } = props;

  const tableRef = useRef<any>(null);

  useEffect(() => {
    loadData(0);
  }, []);

  const loadData = page => {
    if (page > 0) {
      const data = ds.toData();
      underwayQueryInfo({
        params: {
          page,
          size: 10,
        },
        onSuccess: res => {
          if (res) {
            totalPages = res.totalPages;
            const dataList = data.concat(res.content);
            ds.loadData(dataList);
          }
        },
      });
    } else {
      underwayQueryInfo({
        params: {
          page,
          size: 10,
        },
        onSuccess: res => {
          if (res) {
            totalPages = res.totalPages;
            ds.loadData(res.content);
          }
        },
      });
    }
  };

  const handleScroll = e => {
    const { scrollTop, clientHeight, scrollHeight } = e.target;
    if (scrollTop > 0 && Math.ceil(scrollTop + clientHeight) >= scrollHeight) {
      if (page < totalPages) {
        page += 1;
        loadData(page);
      }
    }
  };

  useEffect(() => {
    if (tableRef.current) {
      tableRef.current?.element?.addEventListener('scroll', handleScroll, true);
    }
    return () => tableRef.current?.element?.removeEventListener('scroll', handleScroll, true);
  }, []);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'problemCode',
        width: 120,
        renderer: ({ record, value }) => (
          <a
            onClick={() => {
              history.push(
                `/hwms/problem-management/problem-management-platform/dist/${record?.get(
                  'problemId',
                )}`,
              );
            }}
          >
            {value}
          </a>
        ),
      },
      {
        name: 'problemTitle',
      },
      {
        name: 'problemStatusDesc',
        renderer: ({ record, value }) => {
          switch (record?.get('problemStatus')) {
            case 'NEW':
              return <Badge color="blue" text={value} />;
            case 'RELEASED':
              return <Badge color="orange" text={value} />;
            case 'FOLLOWING':
              return <Badge color="yellow" text={value} />;
            case 'PUBLISH':
              return <Badge color="gold" text={value} />;
            case 'CLOSED':
              return <Badge color="green" text={value} />;
            case 'FREEZE':
              return <Badge color="purple" text={value} />;
            case 'CLOSING':
              return <Badge color="red" text={value} />;
            case 'FREEZE_APPLY':
              return <Badge color="yellow" text={value} />;
            case 'SRM_HANDLE':
              return <Badge color="cyan" text={value} />;
            default:
              return <Badge color="gray" text={value} />;
          }
        },
      },
      {
        name: 'proposePersonName',
      },
      {
        name: 'proposeDepartment',
      },
      {
        name: 'registerTime',
        width: 150,
      },
      {
        name: 'leadPersonName',
      },
      {
        name: 'responsiblePersonName',
      },
      {
        name: 'responsibleDepartment',
      },
      {
        name: 'problemCategory',
      },
      {
        name: 'severityLevel',
      },
      {
        name: 'riskLight',
        align: ColumnAlign.center,
        renderer: ({ record }) => {
          switch (record?.get('riskLight')) {
            case 'R':
              return <div className="riskLight" style={{ backgroundColor: 'red' }} />;
            case 'Y':
              return <div className="riskLight" style={{ backgroundColor: 'yellow' }} />;
            case 'B':
              return <div className="riskLight" style={{ backgroundColor: 'blue' }} />;
            case 'G':
              return <div className="riskLight" style={{ backgroundColor: '#11d954' }} />;
            case 'H':
              return <div className="riskLight" style={{ backgroundColor: 'gray' }} />;
            default:
              return null;
          }
        },
      },
      {
        name: 'schedule',
        width: 250,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <div className="schedule_container">
            <Popover
              content={intl.get(`${modelPrompt}.problem.creation`).d('问题创建')}
              trigger="hover"
            >
              <div className="schedule_status" />
            </Popover>
            <div className="schedule_course" />
            <Popover
              content={intl
                .get(
                  `${modelPrompt}.responsibility.team.formation`,
                )
                .d('责任小组组建')}
              trigger="hover"
            >
              <div className="schedule_status" />
            </Popover>
            <div className="schedule_course" />
            <Popover
              content={intl.get(`${modelPrompt}.reason.analysis`).d('原因分析')}
              trigger="hover"
            >
              <div
                className="schedule_status"
                style={record?.get('causeAnalysis') === 'red' ? { backgroundColor: 'red' }
                  : record?.get('causeAnalysis') === 'green'
                    ? { backgroundColor: '#11d954' }
                    : record?.get('causeAnalysis') === 'blue'
                      ? { backgroundColor: 'blue' }
                      : { backgroundColor: 'gray' }
                }
              />
            </Popover>
            <div className="schedule_course" />
            <Popover
              content={intl
                .get(`${modelPrompt}.countermeasure.making`)
                .d('对策制定')}
              trigger="hover"
            >
              <div
                className="schedule_status"
                style={record?.get('measureEnact') === 'red' ? { backgroundColor: 'red' }
                  : record?.get('measureEnact') === 'green'
                    ? { backgroundColor: '#11d954' }
                    : record?.get('measureEnact') === 'blue'
                      ? { backgroundColor: 'blue' }
                      : { backgroundColor: 'gray' }
                }
              />
            </Popover>
            <div className="schedule_course" />
            <Popover
              content={intl.get(`${modelPrompt}.effect.verification`).d('效果验证')}
              trigger="hover"
            >
              <div
                className="schedule_status"
                style={record?.get('resultVerification') === 'red' ? { backgroundColor: 'red' }
                  : record?.get('resultVerification') === 'green'
                    ? { backgroundColor: '#11d954' }
                    : record?.get('resultVerification') === 'blue'
                      ? { backgroundColor: 'blue' }
                      : { backgroundColor: 'gray' }
                }
              />
            </Popover>
            <div className="schedule_course" />
            <Popover
              content={intl.get(`${modelPrompt}.verification.off`).d('问题关闭')}
              trigger="hover"
            >
              <div
                className="schedule_status"
                style={record?.get('verifyClosed') === 'red' ? { backgroundColor: 'red' }
                  : record?.get('verifyClosed') === 'green'
                    ? { backgroundColor: '#11d954' }
                    : record?.get('verifyClosed') === 'blue'
                      ? { backgroundColor: 'blue' }
                      : { backgroundColor: 'gray' }
                }
              />
            </Popover>
          </div>
        ),
      },
    ];
  }, []);

  // const handleAdd = useCallback(() => {
  //   history.push(`/page_route/create`);
  // }, []);

  const handleJumpToList = (state = {}) => {
    history.push({
      pathname: `/hwms/problem-management/problem-management-platform/list`,
      state: {
        stateType: 'query',
        ...state,
      },
    });
  };

  return (
    <Content>
      <Row>
        <Col span={23}>
          <span className="title">
            {intl.get(`${modelPrompt}.personal_todo`).d('进行中的问题')}
          </span>
        </Col>
        <Col span={1}>
          <a
            onClick={() =>
              handleJumpToList({
                problemStatus: 'RELEASED,FOLLOWING,CLOSING,FREEZE_APPLY,SRM_HANDLE',
                onlyForMeFalg: 'Y',
              })
            }
            style={{ fontSize: '14px' }}
          >
            {intl.get(`${modelPrompt}.more`).d('更多')}
          </a>
        </Col>
      </Row>
      <Table
        dataSet={ds}
        columns={columns}
        ref={tableRef}
        virtual
        virtualCell
        style={{
          height: 'calc(100% - 70px)',
        }}
      />
      {underwayLoading ? (
        <p style={{ textAlign: 'center', padding: '0', margin: '0' }}>{intl.get(`${modelPrompt}.loading`).d('加载中...')}</p>
      ) : (
        ''
      )}
    </Content>
  );
};

export default Underway;
