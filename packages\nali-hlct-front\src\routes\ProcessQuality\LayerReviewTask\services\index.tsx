/**
 * @Description: 分层审核方案-service
 * @Author: <EMAIL>
 * @Date: 2023/8/8 20:08
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

/**
 * 保存审核任务
 * @function SaveReviewTask
 * @returns {object} fetch Promise
 */
export function SaveReviewTask(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-revtsk-dtl/update/ui`,
    method: 'POST',
  };
}

/**
 * 提交审核任务
 * @function SubmitSchemeItem
 * @returns {object} fetch Promise
 */
export function SubmitSchemeTask(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-revtsk-dtl/submit/ui`,
    method: 'POST',
  };
}

/**
 * 上次审核查看
 * @function QueryLastReview
 * @returns {object} fetch Promise
 */
export function QueryLastReview(layerReviewTaskId): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-review-task/lastTaskDetail/${layerReviewTaskId}/ui`,
    method: 'GET',
  };
}
