import React, { FC, useMemo, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Button, DataSet, Modal } from 'choerodon-ui/pro';
import { Collapse, Tabs } from 'choerodon-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { useEditFlag } from 'alm/hooks';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { WaitType } from 'choerodon-ui/pro/lib/core/enum';
import notification from 'utils/notification';
import request from 'utils/request';
import { HALM_ORI } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import getLang from '../Langs';
import { detailDs as _detailDs } from '../Stores/detailDs';
import forecastImg from '../assets/forecast.svg';
import logImg from '../assets/log.svg';
import { forecast } from '../api';
import { tableDs as _resultTableDs } from '../Stores/resultDs';

import styles from './index.modules.less';
import BasicForm from './BasicForm';
import Plan from './Plan';
import Result from './Result';
import LogModal from './LogModal';

const organizationId = getCurrentOrganizationId();

const PlanSchedDetail: FC = props => {
  const { history } = props as any;
  const { isNew, editFlag, setEditFlag, id } = useEditFlag(props); // 注意：新建时editFlag也为true
  const [forecastLoading, setForecastLoading] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState<string>('plan');
  const [hasPlan, setHasPlan] = useState<boolean>(false);

  const detailDs = useMemo(() => new DataSet(_detailDs()), []);
  const detail = detailDs.current?.toData() || {};

  const [editRecord, setEditRecord] = useState<any>(null);
  const resultTableDs = useMemo(
    () =>
      new DataSet(
        _resultTableDs(setEditRecord, detail.maintSiteId, detail.cycleTypeCode, detail.schedId)
      ),
    [detail.schedId]
  ); // id变 服务区域 类型才会变

  useEffect(() => {
    if (!isNew) {
      detailDs.setQueryParameter('id', id);
      detailDs.query();
    }
  }, [id]);

  const handleBack = () => {
    if (isNew) {
      history.push('/aori/plan-sched/list');
    } else {
      detailDs.reset();
      setEditFlag(false);
    }
  };

  const handleNext = async () => {
    if (detailDs.current) {
      const ok = await detailDs.current.validate();
      if (ok) {
        detailDs.submit().then(res => {
          if (res && res.success) {
            history.push(`/aori/plan-sched/detail/${res.content[0].schedId}`, {
              isEdit: true,
            });
          }
        });
      }
    }
  };

  const handleSave = async () => {
    const ok = await detailDs?.current?.validate();
    if (!ok) {
      return;
    }
    detailDs.submit().then(res => {
      if (res && res.success) {
        setEditFlag(false);
        detailDs.query();
      }
    });
  };

  const handleForecast = async () => {
    if (!editFlag) {
      // 查看时直接预测
      Modal.confirm({
        key: Modal.key(),
        title: getLang('NOTICE'),
        children: getLang('FORECAST_TEXT'),
        onOk: () => {
          _forecast();
        },
      });
    } else {
      // 编辑时 先保存头 成功再执行预测逻辑
      const ok = await detailDs?.current?.validate();
      if (!ok) {
        return;
      }
      Modal.confirm({
        key: Modal.key(),
        title: getLang('NOTICE'),
        children: getLang('FORECAST_TEXT'),
        onOk: () => {
          detailDs.submit().then(res => {
            if (res && res.success) {
              detailDs.query();
              _forecast();
            }
          });
        },
      });
    }
  };

  const _forecast = () => {
    // 预测
    setForecastLoading(true);
    forecast({
      schedId: id,
    }).then(res => {
      setForecastLoading(false);
      if (res === false) {
        notification.error({
          message: getLang('FORECAST_ERROR'),
        });
      } else if (res && !res.failed) {
        // 预测会修改头上的最近预测时间 所以需要刷新
        detailDs.query();
        // 跳转到结果页签
        if (activeKey !== 'result') {
          setActiveKey('result');
        }
        // 刷新结果数据
        resetResultTable();
      } else {
        notification.error({
          message: res.message,
        });
      }
    });
  };

  // 刷新结果数据 所有行重置为未选择状态
  const resetResultTable = () => {
    resultTableDs.query();
    // 要清除 不然在缓存记录里会存在
    resultTableDs.clearCachedSelected();
    resultTableDs.unSelectAll();
  };

  const handleLog = () => {
    const modalProps = {
      schedId: id,
    };
    Modal.open({
      key: Modal.key(),
      maskClosable: false, // 点击蒙层是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      drawer: true,
      closable: true,
      style: {
        width: 560,
      },
      title: getLang('TITLE_LOG'),
      footer: null,
      children: <LogModal {...modalProps} />,
    });
  };

  const getBtns = () => {
    // 新建： 返回 下一步； 编辑： 返回 保存 日志 预测； 查看：编辑 日志 预测
    const isView = !editFlag;
    // 非新建时会显示 此时没有计划时置灰（因为直接!hasPlan会导致新建时有个灰色按钮 所以这样写）
    const disForecast = !isNew && !hasPlan;
    return (
      <>
        <Button onClick={handleNext} color={ButtonColor.primary} hidden={!isNew} key="next">
          {getLang('NEXT')}
        </Button>
        <Button
          onClick={handleSave}
          color={ButtonColor.primary}
          hidden={isNew || isView}
          key="save"
        >
          {getLang('SAVE')}
        </Button>
        <Button onClick={handleBack} hidden={isView} key="back">
          {getLang('BACK')}
        </Button>
        <Button
          color={ButtonColor.primary}
          onClick={() => setEditFlag(true)}
          hidden={editFlag}
          key="edit"
        >
          {getLang('EDIT')}
        </Button>
        <Button onClick={handleDraftAll}>批量下达</Button>
        <Button
          onClick={handleForecast}
          hidden={isNew}
          disabled={disForecast}
          waitType={WaitType.throttle}
          wait={1000}
          loading={forecastLoading}
          key="forecast"
        >
          <img src={forecastImg} alt="forecast" className={styles.btn} />
          {getLang('FORECAST')}
        </Button>
        <Button onClick={handleLog} hidden={isNew} key="log">
          <img src={logImg} alt="log" className={styles.btn} />
          {getLang('LOG')}
        </Button>
      </>
    );
  };

  const handleTabClick = tab => {
    setActiveKey(tab);
  };

  const handleDraftAll = () => {
    Modal.confirm({
      key: Modal.key(),
      title: getLang('NOTICE'),
      children: (<p>是否确认下达已预测未生成工单的所有结果，下达后不可删除！</p>),
      onOk: onOkDraftAll,
    });
  }

  const onOkDraftAll = async() => {
    const res = await request(`${HALM_ORI}/v1/${organizationId}/plan-sched-results/list`, {
      method: 'GET',
      query: {
        cycleTypeCode: detail.cycleTypeCode,
        planSchedId: detail.schedId,
        planStatusListStr: 'DRAFT',
        page: -1,
        size: -1,
      },
    })
    if (res?.content.length) {
      const resultIdList = res.content.map(item => item.resultId);
      request(`${HALM_ORI}/v1/${organizationId}/plan-schedules/release`, {
        method: 'POST',
        body: {
          resultIdList,
        },
      }).then(res => {
        if (res) {
          resultTableDs.query();
        } else {
          notification.error({
            message: res?.message,
          });
        }
      })
    } else if(!res?.content.length){
      return true;
    } else {
      notification.error({
        message: res?.message,
      });
      return false
    }
  }

  return (
    <PageHeaderWrapper
      title={getLang('HEADER')}
      header={getBtns()}
      headerProps={{ backPath: '/aori/plan-sched/list' }}
    >
      <Collapse bordered={false} defaultActiveKey={['A']} className={styles['plan-collapse']}>
        <Collapse.Panel key="A" header={getLang('PANEL_SCOPE')}>
          <BasicForm header={detail} detailDs={detailDs} editFlag={editFlag} isNew={isNew} />
        </Collapse.Panel>
      </Collapse>
      {!isNew && (
        <Tabs activeKey={activeKey} onTabClick={handleTabClick}>
          <Tabs.TabPane tab={getLang('TAB_PLAN')} key="plan">
            <Plan header={detail} detailDs={detailDs} history={history} setHasPlan={setHasPlan} />
          </Tabs.TabPane>
          <Tabs.TabPane tab={getLang('TAB_RESULT')} key="result">
            <Result
              header={detail}
              detailDs={detailDs}
              history={history}
              resultTableDs={resultTableDs}
              editRecord={editRecord}
              setEditRecord={setEditRecord}
            />
          </Tabs.TabPane>
        </Tabs>
      )}
    </PageHeaderWrapper>
  );
};

export default formatterCollections({
  code: ['alm.common', 'alm.component', 'aori.planSched', 'alm.common'],
})(observer(PlanSchedDetail));
