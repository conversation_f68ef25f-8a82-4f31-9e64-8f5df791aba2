/*
 * @Description: CGK分析
 * @Author: <<EMAIL>>
 * @Date: 2024-02-02 14:43:00
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-02-06 16:55:06
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, Form, TextField, Lov, DataSet, Table, TextArea, NumberField } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/es/table/Column';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { downloadFile } from '@services/api';
import { BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST } from '@/utils/constants';
import ExcelUpload, { ExcelUploadProps } from './components/ExcelUpload';
import { formDS, tableDS } from '../stores/cgkDS';
import { analyseResultDS } from '../stores/DetailDS';
import CGKGraphic from '../components/CGKGraphic';
import { QueryMsaTypeData, SaveMsaTypeData } from '../services';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

interface GraphicDataProps {
  cgkInfo: object;
  standardValue: number;
  tolerance: number;
}

const CGK = props => {
  const { msaStatus, currentUserFlag, msaTaskLineId, checkLocation } = props;
  // 一行有几组数据
  const [ columnGroupQty ] = useState(3);
  // 当前数据是否有保存过，是否展示分析结果信息
  const [showAnalyseResult, setShowAnalyseResult] = useState<boolean>(false);
  const [graphicData, setGraphicData] = useState<GraphicDataProps>({
    cgkInfo: {},
    standardValue: 0,
    tolerance: 0,
  });
  const formDs = useMemo(() => new DataSet(formDS()), []);
  const tableDs = useMemo(() => new DataSet(tableDS(columnGroupQty)), [columnGroupQty]);
  // 分析结果DS
  const analyseResultDs = useMemo(() => new DataSet(analyseResultDS()), []);
  // 查询任务数据
  const { run: queryMsaTypeData, loading: queryDataLoading } = useRequest(QueryMsaTypeData(), { manual: true, needPromise: true });
  // 保存任务数据
  const { run: saveMsaTypeData, loading: saveDataLoading } = useRequest(SaveMsaTypeData(), { manual: true });

  useEffect(() => {
    handleInitData();
  }, []);

  const handleInitData = async () => {
    const res = await queryMsaTypeData({ params: { msaTaskLineId }});
    if (res?.success) {
      handleFormatAsyncData(res?.rows);
    }
  };

  // 处理接口返回的数据，得到DS的数据
  const handleFormatAsyncData = (dataSource, saveFlag = true) => {
    const {
      cgkInfo = {},
      msaResult = '',
      msaConclusion = '',
      measuredBy,
      measuredByName,
      sampleDescription,
      tolerance,
      standardValue,
    } = dataSource;
    const { measureValueList } = cgkInfo;
    if (saveFlag) {
      setGraphicData({
        standardValue,
        tolerance,
        cgkInfo: {
          ...cgkInfo,
          standardValue,
          tolerance,
        },
      });
    } else {
      setGraphicData({
        cgkInfo: {},
        standardValue: 0,
        tolerance: 0,
      });
    }
    if (measureValueList?.length) {
      // 表里有数据
      setShowAnalyseResult(saveFlag);
      const _tableData: any = [];
      (measureValueList || []).forEach((columnItem) => {
        const { measureDataRow, measureDataValue } = columnItem;
        // 当前数据应该在第一列还是第二列
        const columnIndex: number = (measureDataRow - 1) % columnGroupQty;
        // 当前数据在第几行
        const rowIndex: number = Math.floor((measureDataRow - 1) / columnGroupQty);
        const currentData = {
          [`sequence_${columnIndex}`]: measureDataRow,
          [`measureDataValue_${columnIndex}`]: measureDataValue,
          [`saveFlag_${columnIndex}`]: saveFlag,
        };
        if (columnIndex === 0) {
          _tableData.push(currentData);
        } else {
          _tableData[rowIndex] = {
            ..._tableData[rowIndex],
            ...currentData,
          }
        }
      });
      // 当某一行只存了部分数据时，对其余数据做处理
      if ((measureValueList || [])?.length % columnGroupQty !== 0) {
        const addColumnQty = columnGroupQty - (measureValueList || [])?.length % columnGroupQty;
        // 要添加的列序号从多少开始
        let startRow = (measureValueList || [])?.length + 1;
        for (let i = 0; i < addColumnQty; i++) {
          // 当前数据应该在第一列还是第二列
          const columnIndex: number = (startRow - 1) % columnGroupQty;
          // 当前数据在第几行
          const rowIndex: number = Math.floor((startRow - 1) / columnGroupQty);
          _tableData[rowIndex] = {
            ..._tableData[rowIndex],
            [`sequence_${columnIndex}`]: startRow,
            [`saveFlag_${columnIndex}`]: false,
          };
          // 序号+1
          startRow++;
        }
      }
      tableDs.loadData(_tableData);
    } else {
      const createItem = {};
      for (let i = 0; i < columnGroupQty; i++) {
        createItem[`sequence_${i}`] = 1 + i;
        createItem[`saveFlag_${i}`] = false;
      }
      tableDs.create(createItem);
    }
    if (saveFlag) {
      formDs.loadData([{
        sampleDescription,
        measuredBy,
        measuredByName,
        tolerance,
        standardValue,
      }]);
    }
    analyseResultDs.loadData([{
      msaResult,
      msaConclusion,
    }]);
    formDs.current?.set('checkLocation', checkLocation);
  };

  const getColumnsGroup = (columnIndex) => ([
    {
      name: `sequence_${columnIndex}`,
      align: ColumnAlign.center,
    },
    {
      name: `measureDataValue_${columnIndex}`,
      align: ColumnAlign.center,
      editor: () => !(['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag),
    },
  ]);

  const handleAdd = () => {
    let maxSequence = 0;
    tableDs.forEach((_record) => {
      if (_record?.get(`sequence_${columnGroupQty - 1}`) > maxSequence) {
        maxSequence = _record?.get(`sequence_${columnGroupQty - 1}`);
      }
    });
    const createItem = {};
    for (let i = 0; i < columnGroupQty; i++) {
      createItem[`sequence_${i}`] = maxSequence + 1 + i;
      createItem[`saveFlag_${i}`] = false;
    }
    tableDs.create(createItem);
  };

  const handleDelete = (record) => {
    tableDs.remove(record);
  };

  const columns: any = useMemo(() => {
    let _columnGroup: ColumnProps[] = [];
    for (let i = 0; i < columnGroupQty; i++) {
      _columnGroup = [
        ..._columnGroup,
        ...getColumnsGroup(i),
      ];
    }
    const getDisabledDelete = (record, _columnGroupQty) => {
      let disabledFlag = true;
      for (let i = 0; i < _columnGroupQty; i++) {
        if (!record?.get(`saveFlag_${i}`)) {
          disabledFlag = false;
        }
      }
      return disabledFlag;
    }
    return [
      {
        header: () => (
          <Button
            icon="add"
            funcType={FuncType.flat}
            onClick={handleAdd}
            size={Size.small}
            disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
          />
        ),
        align: ColumnAlign.center,
        width: 60,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDelete(record)}
          >
            <Button
              icon="remove"
              funcType={FuncType.flat}
              size={Size.small}
              disabled={getDisabledDelete(record, columnGroupQty) || ['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
            />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      ..._columnGroup,
    ];
  }, [msaStatus, currentUserFlag, columnGroupQty]);

  // 模板下载
  const downloadTemp = async () => {
    await downloadFile({
      requestUrl: `/himp/v1/${tenantId}/template/QIS_MSA_IMPORT_CGK/excel`,
      queryParams: [{ name: 'tenantId', value: tenantId }],
      method: 'GET',
    });
  };

  const excelUploadProps: ExcelUploadProps = {
    url: `${API_HOST}${
      BASIC.TARZAN_SAMPLING
    }/v1/${tenantId}/qis-msa-analysis/import/ui?msaTaskLineId=${
      props.msaTaskLineId
    }&sampleDescription=${formDs.current?.get('sampleDescription') ||
      ''}&measuredBy=${formDs.current?.get('measuredBy') || ''}`,
    params: {},
    onSuccess: res => handleFormatAsyncData(
      res.rows || {},
      false,
    ),
  };

  // 点击保存按钮的回调
  const handleSaveData = async () => {
    const tableValidateRes = await tableDs.validate();
    const formValidateRes = await formDs.validate();
    if (!tableValidateRes || !formValidateRes) {
      return;
    }
    saveMsaTypeData({
      params: handleFormatSaveData(),
      onSuccess: (res) => {
        // 处理数据
        handleFormatAsyncData(res);
        props.updateHeaderInfo(); // 更新头部
        notification.success({});
      },
    })
  };

  const handleFormatRow = (rowData, _columnGroupQty, _maxSeq) => {
    const _newData: any = [];
    for (let i = 0; i < _columnGroupQty ; i++) {
      if (rowData[`measureDataValue_${i}`]) {
        _newData.push({
          measureDataColumn: 1,
          measureDataRow: _maxSeq + 1 + i,
          measureDataValue: rowData[`measureDataValue_${i}`],
        });
      }
    }
    return _newData;
  }

  // 处理保存数据
  const handleFormatSaveData = () => {
    const formData = formDs.current?.toData();
    const tableData = tableDs.toData();
    let _newTableData: any = [];
    let currentMaxSequence = 0;
    tableData.forEach((item) => {
      const _rowData = handleFormatRow(item, columnGroupQty, currentMaxSequence);
      _newTableData = [
        ..._newTableData,
        ..._rowData,
      ];
      currentMaxSequence += _rowData?.length;
    });
    return {
      msaTaskLineId: props.msaTaskLineId,
      ...formData,
      tableInfo: [{ measureTableList: _newTableData }],
    };
  }

  return (
    <div>
      <Button
        color={ButtonColor.primary}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
        loading={saveDataLoading || queryDataLoading}
        onClick={() => handleSaveData()}
      >
        {intl.get(`${modelPrompt}.button.save`).d('保存')}
      </Button>
      <Button onClick={downloadTemp} icon="get_app">
        {intl.get(`${modelPrompt}.templateDownload`).d('模板下载')}
      </Button>
      <ExcelUpload
        {...excelUploadProps}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      />
      <Form
        dataSet={formDs}
        columns={3}
        style={{ marginTop: 10 }}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        <TextField name="checkLocation" />
        <NumberField name="tolerance" />
        <TextField name="standardValue" />
        <TextField name="sampleDescription" />
        <Lov name="improveByObj" />
      </Form>
      <Table dataSet={tableDs} columns={columns} />
      {showAnalyseResult && (
        <CGKGraphic dataSoure={graphicData} analyseResultDs={analyseResultDs} />
      )}
    </div>
  );
};

export default CGK;
