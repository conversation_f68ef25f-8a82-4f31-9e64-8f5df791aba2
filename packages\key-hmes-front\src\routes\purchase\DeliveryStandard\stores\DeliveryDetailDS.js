import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.purchase.delivery';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  dataKey: 'rows.deliveryDoc',
  cacheSelection: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-delivery-doc/info/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('送货单号'),
    },
    {
      name: 'instructionDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatusDesc`).d('送货单状态'),
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocTypeDesc`).d('送货单类型'),
    },
    {
      name: 'sourceSystemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSystemDesc`).d('来源系统'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteName`).d('供应商地点'),
    },
    {
      name: 'demandTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandTime`).d('预计到货时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loginName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdDate`).d('创建时间'),
    },
  ],
});

const lineTableDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    paging: false,
    selection: false,
    primaryKey: 'instructionId',
    fields: [
      {
        name: 'lineNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      },
      {
        name: 'identifyType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
        lookupCode: 'MT.APS.GEN_TYPE_URL',
        lovPara: {
          typeGroup: 'IDENTITY_TYPE',
          tenantId: getCurrentOrganizationId(),
          userId: getCurrentUserId(),
        },
        valueField: 'typecode',
        textField: 'description',
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'siteCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'quantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.quantity`).d('制单数量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'instructionStatusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.instructionStatusDesc`).d('状态'),
      },
      {
        name: 'poNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
      },
      {
        name: 'lineNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineNum`).d('采购订单行号'),
      },
      {
        name: 'actualQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.actualQty`).d('已接收数量'),
      },
      {
        name: 'storageActualQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.storageActualQty`).d('已入库数量'),
      },
      {
        name: 'locator',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.locatorCode`).d('接收仓库'),
        lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              siteIds: [record.get('siteId')].join(','),
              locatorCategoryList: ['AREA'],
              businessTypes: [record.get('businessType')],
              queryType: 'TARGET',
            };
          },
          required: ({ record }) => {
            return record.get('toLocatorRequiredFlag') === 'Y';
          },
        },
      },
      {
        name: 'locatorCode',
        type: FieldType.string,
        bind: 'locator.locatorCode',
      },
      {
        name: 'toLocatorId',
        type: FieldType.number,
        bind: 'locator.locatorId',
      },
      {
        name: 'urgentFlag',
        type: FieldType.string,
        lookupCode: 'MT.YES_NO',
        lovPara: { tenantId },
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'N',
        label: intl.get(`${modelPrompt}.urgentFlag`).d('是否加急'),
      },
      {
        name: 'toleranceFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'toleranceType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
        textField: 'description',
        valueField: 'typeCode',
        lovPara: { tenantId },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'toleranceMinValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差'),
      },
      {
        name: 'toleranceMaxValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差'),
      },
      {
        name: 'soNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.soNum`).d('销售订单号'),
      },
      {
        name: 'soLineNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
      },
      {
        name: 'demandTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.demandDate`).d('需求日期'),
      },
    ],
  };
};

export { headerTableDS, lineTableDS };
