import React, { useMemo } from 'react';
import { Button, DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
// import { routerRedux } from 'dva/router';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { tableDS } from './stores/NationalCodingMaintenanceDS';

const modelPrompt = 'tarzan.hmes.NationalCodingMaintenance';
const NationalCodingMaintenance = props => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  const edit = record => {
    props.history.push(`/hmes/national-coding-maintenance/${record?.get('gbCodeRuleId')||'create'}`);
  };
  const columns = [
    // 站点
    {
      name: 'siteCode',
      align: 'left',
    },
    // 规则编码
    {
      name: 'ruleCode',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => edit(record)}>{value}</a>
          </span>
        );
      },
    },
    // 规则描述
    {
      name: 'description',
      align: 'left',
    },
    // 位数
    {
      name: 'numberBit',
      align: 'left',
    },
    // 生产线
    {
      name: 'prodLineCode',
      align: 'left',
    },
    // 生产线描述
    {
      name: 'prodLineName',
      align: 'left',
    },
    // 物料
    {
      name: 'materialCode',
      align: 'left',
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
    },
    // 物料版本
    {
      name: 'revisionCode',
      align: 'left',
    },
    // 生产类型
    {
      name: 'workOrderTypeDesc',
      align: 'left',
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '有效' : '无效'}>
          {}
        </Badge>
      ),
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('国标码编码规则维护')}>
        <Button onClick={() => edit()} style={{ marginRight: 15 }} icon="add" color="primary">
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
      </Header>
      <Content>
        {/* <Spin loading={false}> */}
        <Table
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
          dragRow
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={3}
          searchCode="NationalCodingMaintenance"
          customizedCode="NationalCodingMaintenance"
        />
        {/* </Spin> */}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.NationalCodingMaintenance', 'tarzan.common'],
})(NationalCodingMaintenance);
