/**
 * @Description: 产线审核计划管理平台-详情DS
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'key.hwms.front.CommissionedExperimentalPlatform';

const tableDetailDS = () => ({
  autoQuery: false,
  selection: false,
  forceValidate: true,
  fields: [
    {
      name: 'entrustApplyCode',
      label: intl.get(`${modelPrompt}.entrustApplyCode`).d('委托申请单编码'),
      type: FieldType.string,
      disabled: true,
    },
    // {
    //   name: 'status',
    //   label: intl.get(`${modelPrompt}.status`).d('状态'),
    //   type: FieldType.string,
    //   disabled: true,
    //   lookupCode: 'YP.QIS.PRODLINE_REVPLAN_STATUS',
    // },
    {
      name: 'siteObj',
      label: intl.get(`${modelPrompt}.siteObj`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      type: FieldType.object,
      // textField: 'siteName',
      // valueField: 'siteId',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteObj.siteId',
      type: FieldType.string,
    },
    {
      name: 'siteCode',
      bind: 'siteObj.siteCode',
      type: FieldType.string,
    },
    {
      name: 'materialObj',
      label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      ignore: 'always',
      type: FieldType.object,
      // textField: 'materialName',
      // valueField: 'materialId',
      lovPara: { tenantId },
    },
    {
      name: 'materialId',
      bind: 'materialObj.materialId',
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      bind: 'materialObj.materialCode',
      type: FieldType.string,
    },
    {
      name: 'materialName',
      bind: 'materialObj.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'unitObj',
      label: intl.get(`${modelPrompt}.unitObj`).d('委托专业'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: 'always',
      type: FieldType.object,
      // textField: 'entrustUnitName',
      // valueField: 'entrustUserId',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'entrustUnitId',
      bind: 'unitObj.unitId',
      type: FieldType.string,
    },
    {
      name: 'entrustUnitName',
      bind: 'unitObj.unitName',
      type: FieldType.string,
    },
    {
      name: 'entrustUserId',
      type: FieldType.number,
    },
    {
      name: 'entrustUserName',
      label: intl.get(`${modelPrompt}.entrustUserName`).d('委托人'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'sampleQty',
      label: intl.get(`${modelPrompt}.sampleQty`).d('样本数量'),
      type: FieldType.number,
      required: true,
    },
    {
      name: 'sampleUom',
      label: intl.get(`${modelPrompt}.sampleUom`).d('样本单位'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'expectCompleteTime',
      label: intl.get(`${modelPrompt}.expectCompleteTime`).d('期望完成时间'),
      type: FieldType.dateTime,
      required: true,
      // disabled: true,
    },
    {
      name: 'reason',
      label: intl.get(`${modelPrompt}.reason`).d('实验原因'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'sampleDisposalMethod',
      label: intl.get(`${modelPrompt}.sampleDisposalMethod`).d('测试后样本处理'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.SAMPLE_DISPOSAL_METHOD',
      required: true,
    },
    {
      name: 'relatedFileUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.relatedFileUuid`).d('相关支持文件'),
      bucketName: 'qms',
      // computedProps: {
      //   disabled: ({ record }) => {
      //     return !record.get('inspectItemLov') || (record.get('selectList') || {}).enclosure;
      //   },
      // },
    },
    {
      name: 'remark',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      type: FieldType.string,
    },
  ],
});
const tableDetailLineDS = () => ({
  autoQuery: false,
  selection: 'multiple',
  forceValidate: true,
  paging: false,
  fields: [
    {
      name: 'sequence',
      label: intl.get(`${modelPrompt}.itemSequence`).d('序号'),
      type: FieldType.number,
    },
    {
      name: 'itemSequence',
      label: intl.get(`${modelPrompt}.itemSequence`).d('序号'),
      type: FieldType.number,
    },
    {
      name: 'itemDesc',
      label: intl.get(`${modelPrompt}.itemDesc`).d('检验项描述'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'inspectMethod',
      label: intl.get(`${modelPrompt}.inspectMethod`).d('检验工具/方法'),
      type: FieldType.string,
    },
    {
      name: 'inspectStandard',
      label: intl.get(`${modelPrompt}.inspectStandard`).d('检验标准'),
      type: FieldType.string,
    },
    {
      name: 'sampleQty',
      label: intl.get(`${modelPrompt}.sampleQty`).d('抽样数量'),
      type: FieldType.number,
      required: true,
      min: 1,
      precision: 0,
    },
    {
      name: 'sampleCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleCode`).d('已输入样本编码'),
      required: true,
    },
    // {
    //   name: 'sampleCodeBtn',
    //   label: intl.get(`${modelPrompt}.sampleCode`).d('样品编码'),
    //   type: FieldType.string,
    // },
  ],
});
export { tableDetailDS, tableDetailLineDS };
