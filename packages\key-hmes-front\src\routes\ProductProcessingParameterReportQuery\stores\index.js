import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/tznr-20000`;
const modelPrompt = 'tarzan.hmes.productProcessingParameterReportQuery';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'ncRecordId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      },
      {
        name: 'tagCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagCode`).d('收集项编码'),
      },
      {
        name: 'tagDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagDescription`).d('收集项描述'),
      },
      {
        name: 'tagValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagValue`).d('收集值'),
      },
      {
        name: 'tagCalculateResultMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagCalculateResultMeaning`).d('判定结果'),
      },
      {
        name: 'trueValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
      },
      {
        name: 'falseValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
      },
      {
        name: 'recordDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.recordDate`).d('收集日期'),
      },
      {
        name: 'createByName',
        type: 'string',
        label: intl.get(`${modelPrompt}.createByName`).d('收集人'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
      },
      {
        name: 'tagGroupCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupCode`).d('收集组编码'),
      },
      {
        name: 'tagGroupDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupDescription`).d('收集组描述'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupDescription`).d('单位编码'),
      },
      {
        name: 'uomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupDescription`).d('单位描述'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    queryFields: [
      {
        name: 'equipmentLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.equipmentLov`).d('设备编码'),
        lovCode: 'MT.MODEL.EQUIPMENT',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'equipmentCode',
        multiple: true,
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'startDate',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.startDate`).d('时间开始'),
      },
      {
        name: 'endDate',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.endDate`).d('时间结束'),
      },
      {
        name: 'tagLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.tagLov`).d('收集项编码'),
        lovCode: 'YP_MES.TAG',
        multiple:true,
        ignore: 'always',
        textField: 'tagCode',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'tagCode',
        bind: 'tagLov.tagCode',
      },
      {
        name: 'tagGroupLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.tagGroupLov`).d('收集组编码'),
        lovCode: 'YP_MES.TAG_GROUP',
        ignore: 'always',
        textField: 'tagGroupCode',
        lovPara: {
          tenantId,
        },
        multiple: true,
      },
      {
        name: 'tagGroupCode',
        bind: 'tagGroupLov.tagGroupCode',
      },
      {
        name: 'identifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.identifications`).d('条码号'),
      },
      {
        name: 'tagCalculateResult',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagCalculateResult`).d('判定结果'),
        lookupCode: 'HME.TAG_CAL_RESULT',
      },

    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-product-param-collect/equipment/list/ui`,
          method: 'POST',
        };
      },
    },
  };
};


export { tableDS };


