/*
 * @Description: 市场活动单-详情界面DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-18 18:04:36
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-04 11:29:04
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.marketActivity.marketActivityDoc';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'marketActivityCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.marketActivityCode`).d('活动单编号'),
      disabled: true,
    },
    {
      name: 'marketActivityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.marketActivityStatus`).d('状态'),
      lookupCode: 'YP.QIS.MARKET_ACTIVITY_STATUS',
      lovPara: { tenantId },
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'marketEstimateCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.marketEstimateCode`).d('评估单编号'),
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.marketActive.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      disabled: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'problemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.marketActive.problemCode`).d('问题编码'),
      lovCode: 'YP.QIS.PROBLEM_LIST',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'problemId',
      bind: 'problemLov.problemId',
    },
    {
      name: 'problemCode',
      bind: 'problemLov.problemCode',
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.problemTitle`).d('问题描述'),
      bind: 'problemLov.problemTitle',
      disabled: true,
    },
    {
      name: 'createdBy',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      disabled: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      disabled: true,
    },

    {
      name: 'applyDepartmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.marketActive.applyDepartmentName`).d('申请部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'applyDepartmentId',
      bind: 'applyDepartmentLov.unitId',
    },
    {
      name: 'applyDepartmentName',
      bind: 'applyDepartmentLov.unitName',
    },
    {
      name: 'faultPhenomenon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.faultPhenomenon`).d('故障现象'),
      required: true,
    },
    {
      name: 'occurrence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.occurrence`).d('发生情况'),
      required: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.marketActive.ypItemCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({ tenantId, siteId: record?.get('siteId') }),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'ypItemCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'ypItemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.ypItemName`).d('物料名称'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'itemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.itemCode`).d('客户零件号'),
      required: true,
    },
    {
      name: 'itemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.itemName`).d('客户零件名称'),
      required: true,
    },
    {
      name: 'batteryPackModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.batteryPackModel`).d('电池包型号'),
      disabled: true,
    },
    {
      name: 'vehicleModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.vehicleModel`).d('车型'),
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      lovPara: { tenantId },
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.marketActive.customerName`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      disabled: true,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'customerName',
      bind: 'customerLov.customerName',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.severityLevel`).d('重要度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'responsibleDepartmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.marketActive.responsibleDepartmentName`).d('责任部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'responsibleDepartmentId',
      bind: 'responsibleDepartmentLov.unitId',
    },
    {
      name: 'responsibleDepartmentName',
      bind: 'responsibleDepartmentLov.unitName',
    },
    {
      name: 'objectQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.marketActive.objectQty`).d('对象数量'),
      required: true,
      min: 0,
      validator: (value) => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.marketActive.objectQty`).d('对象数量') + intl
            .get(`${modelPrompt}.marketActive.validation.objectQtyMoreThanZero`)
            .d(`必须大于0, 请检查!`);
        }
      },
    },
    {
      name: 'objectRange',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.marketActive.objectRange`).d('对象范围'),
      required: true,
      bucketName: 'qms',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.marketActive.enclosure`).d('附件'),
      bucketName: 'qms',
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.reason`).d('提案理由'),
      required: true,
    },
    {
      name: 'activityOverview',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.activityOverview`).d('活动概述'),
      required: true,
    },
    {
      name: 'materialCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.marketActive.materialCost`).d('材料费/元'),
      required: true,
      min: 0,
    },
    {
      name: 'manageCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.marketActive.manageCost`).d('管理费/元'),
      required: true,
      min: 0,
    },
    {
      name: 'laborCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.marketActive.laborCost`).d('工时费/元'),
      required: true,
      min: 0,
    },
    {
      name: 'singleUnitCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.marketActive.singleUnitCost`).d('单台费用/元'),
      required: true,
      min: 0,
    },
    {
      name: 'measurementSumCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.marketActive.measurementSumCost`).d('测算总费用/元'),
      required: true,
      min: 0,
    },
    {
      name: 'startTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.startTime`).d('开始时间'),
      disabled: true,
    },
    {
      name: 'completionDegree',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.marketActive.completionDegree`).d('完成度（%）'),
      min: 0,
      max: 100,
      computedProps: {
        required: ({ record }) => record?.get('marketActivityStatus') === 'EXECUTING',
        disabled: ({ record }) => record?.get('marketActivityStatus') !== 'EXECUTING',
      },
    },
    {
      name: 'endTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActive.endTime`).d('结束时间'),
      disabled: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-activitys/details/ui`,
        method: 'GET',
      };
    },
  },
});
export { detailDS };
