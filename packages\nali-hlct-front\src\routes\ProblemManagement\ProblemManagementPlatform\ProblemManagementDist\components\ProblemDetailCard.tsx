/**
 * @Description: 问题管理平台-详情界面问题明细卡片
 * @Author: <EMAIL>
 * @Date: 2023/7/4 13:36
 */
import React, { useMemo } from 'react';
import {
  Button,
  DataSet,
  Form,
  Output,
  Modal,
  Select,
  Lov,
  TextField,
  TextArea,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { drawerPropsC7n } from '@components/tarzan-ui';
import lightBlueImg from '@/assets/icons/light_blue.svg';
import lightGreenImg from '@/assets/icons/light_green.svg';
import lightGreyImg from '@/assets/icons/light_grey.svg';
import lightRedImg from '@/assets/icons/light_red.svg';
import lightYellowImg from '@/assets/icons/light_yellow.svg';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import styles from '../index.module.less';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';

const ProblemDetailCard = ({ dataSet, userRole, problemStatus, handleSaveBasicInfo }) => {
  const handleOpenDetailModal = () => {
    Modal.open({
      ...drawerPropsC7n({
        ds: dataSet,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.edit`).d('编辑'),
      destroyOnClose: true,
      style: {
        width: 420,
      },
      onOk: () => handleSaveBasicInfo(dataSet),
      children: (
        <Form dataSet={dataSet} columns={1}>
          <Select name="problemCategory" />
          <Select name="problemStatus" />
          <TextField name="problemTitle" />
          <TextArea name="problemDescription" colSpan={3} />
          <Lov name="leadPersonLov" />
          <Lov name="registerPersonLov" />
          <TextField name="registerPersonCompanyName" />
          <Lov name="proposePersonLov" />
          <TextField name="proposePersonCompanyName" />
          <DateTimePicker name="proposeTimePeriodStart" />
          <DateTimePicker name="proposeTimePeriodEnd" />
          <Lov name="problemApplyLov"  />
        </Form>
      ),
    });
  };

  const RenderStatusTag = observer(({ ds }: { ds: DataSet }) => {
    const problemStatus = ds.current?.get('problemStatus');
    let className;
    switch (problemStatus) {
      case 'NEW':
      case 'RELEASED':
        className = 'green';
        break;
      case 'FOLLOWING':
        className = 'yellow';
        break;
      case 'PUBLISH':
        className = 'blue';
        break;
      case 'CLOSED':
      case 'FREEZE':
        className = 'gray';
        break;
      default:
        className = 'geekblue';
    }
    return <Tag color={className}>{ds.current?.getField('problemStatus')!.getText()}</Tag>;
  });

  const RenderProblemGroupTag = ({ ds }) => {
    const text = useMemo(() => {
      const problemCategoryDs = ds.getField('problemCategory')?.getOptions(ds.current) || [];
      const currentCategoryRecord = problemCategoryDs.find(
        _record => _record?.get('value') === ds.current?.get('problemCategory'),
      );
      return currentCategoryRecord?.get('description') || '';
    }, []);

    return <Tag color="blue">{text}</Tag>;
  };

  const RenderLightImg = () => {
    switch (dataSet.current?.get('riskLight')) {
      case 'B':
        return <img alt="light" src={lightBlueImg} />;
      case 'G':
        return <img alt="light" src={lightGreenImg} />;
      case 'R':
        return <img alt="light" src={lightRedImg} />;
      case 'Y':
        return <img alt="light" src={lightYellowImg} />;
      case 'H':
        return <img alt="light" src={lightGreyImg} />;
      default:
        return <></>;
    }
  };

  const RendererProposeTime = record => {
    const { proposeTimePeriodStart, proposeTimePeriodEnd } = record.toData();
    return `${proposeTimePeriodStart}～${proposeTimePeriodEnd}`;
  };

  return (
    <div className={styles['problem-detail-card']}>
      <div className={styles['problem-detail-card-head']}>
        <div className={styles['problem-detail-card-head-title']}>
          {RenderLightImg()}
          <div className={styles['problem-detail-card-head-title-content']}>
            {intl.get(`${modelPrompt}.problemTitle`).d('问题标题')}：
            {dataSet.current?.get('problemTitle')}({dataSet.current?.get('problemCode')})
          </div>
        </div>
        <div className={styles['problem-detail-card-head-tags']}>
          <RenderStatusTag ds={dataSet} />
          <RenderProblemGroupTag ds={dataSet} />
          <Button
            funcType={FuncType.flat}
            icon="edit-o"
            onClick={handleOpenDetailModal}
            disabled={
              !(
                userRole.includes('LEAD_PERSON') &&
                ['PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus)
              ) &&
              !(userRole.includes('REGISTER_PERSON') && ['NEW', 'DRAFT'].includes(problemStatus))
            }
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        </div>
      </div>
      <Form columns={5} dataSet={dataSet} labelWidth={100}>
        <Output
          name="registerPersonRealName"
          label={intl.get(`${modelPrompt}.registerInfo`).d('登记信息')}
          renderer={({ text, record }) => `${text || ''} ${record?.get('registerTime') || ''}`}
        />
        <Output
          name="proposePersonRealName"
          label={intl.get(`${modelPrompt}.proposeInfo`).d('提出人/部门')}
          renderer={({ text, record }) =>
            `${text || ''}/${record?.get('proposePersonCompanyName')}`
          }
        />
        <Output name="leadPersonRealName" />
        <Output name="proposeTimePeriod" colSpan={2} renderer={({ record }) => RendererProposeTime(record)} />
        <Output name="problemApplyCode" />
        <Output name="problemDescription" colSpan={4} />
      </Form>
    </div>
  );
};
export default ProblemDetailCard;
