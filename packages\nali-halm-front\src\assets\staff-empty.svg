<?xml version="1.0" encoding="UTF-8"?>
<svg width="120px" height="121px" viewBox="0 0 120 121" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>空态图</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="120" height="120"></rect>
        <path d="M9.74798081,4.06785716e-13 L9.341,9.872 L12.5158671,10.5884304 C12.5505278,14.729098 11.7347422,17.0385001 10.0685102,17.5166366 C8.58578509,17.9421146 6.07189264,17.0201333 2.52683281,14.7506927 L1.60985376,14.7507774 L1.6338863,14.1651885 C1.47879014,14.0611932 1.32189592,13.9548483 1.16320363,13.8461538 L1.84909865e-11,8.6908793 L1.871,8.391 L2.21741644,4.06785716e-13 L9.74798081,4.06785716e-13 Z" id="path-3"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="弹窗-空态" transform="translate(-513.000000, -193.000000)">
            <g id="空态图" transform="translate(513.000000, 193.500000)">
                <mask id="mask-2" fill="white">
                    <use xlink:href="#path-1"></use>
                </mask>
                <g id="矩形"></g>
                <rect id="矩形" fill="#F2F3F6" mask="url(#mask-2)" x="42.725062" y="10.366005" width="73.3995037" height="53.1513648" rx="0.595533499"></rect>
                <rect id="矩形备份-3" fill="#FFFFFF" mask="url(#mask-2)" x="74.1394541" y="21.8300248" width="38.8585608" height="12.9528536" rx="0.595533499"></rect>
                <rect id="矩形备份-9" fill="#FFFFFF" mask="url(#mask-2)" x="74.1394541" y="37.6116625" width="38.8585608" height="12.9528536" rx="0.595533499"></rect>
                <rect id="矩形备份-10" fill="#D5DAE2" mask="url(#mask-2)" x="42.725062" y="10.366005" width="73.3995037" height="5.35980149" rx="0.595533499"></rect>
                <g id="编组-6" mask="url(#mask-2)">
                    <g transform="translate(2.828784, 26.650124)">
                        <rect id="矩形备份-2" fill="#F2F3F6" fill-rule="evenodd" x="0" y="1.191067" width="73.3995037" height="50.1736973" rx="0.595533499"></rect>
                        <rect id="矩形" fill="#FFFFFF" fill-rule="evenodd" x="2.97766749" y="7.59305211" width="66.8486352" height="40.1985112" rx="0.297766749"></rect>
                        <rect id="矩形" fill="#D5DAE2" fill-rule="evenodd" x="10.2" y="13.35" width="17.2704715" height="17.7171216" rx="0.297766749"></rect>
                        <path d="M19.4032047,23.5760728 L19.4032047,23.4536502 L19.4032047,23.4536502 C19.3973018,23.2193081 19.4445254,23.0230467 19.5448757,22.8648658 C19.651129,22.6949678 19.8400237,22.4987063 20.1115598,22.2760814 C20.8376237,21.7312361 21.1829467,21.1043711 21.147529,20.3954864 C21.0412757,19.2765031 20.4096592,18.6789309 19.2526793,18.6027697 C17.8595811,18.5500427 17.0420213,19.2501396 16.8,20.7030604 L17.9864947,20.9930586 C18.1340686,20.17872 18.4941491,19.7774092 19.0667361,19.7891263 C19.5212639,19.8242777 19.7750911,20.0908417 19.8282178,20.5888186 C19.8459266,20.9520488 19.6009538,21.347501 19.0932994,21.7751752 C18.5679361,22.1969909 18.3141089,22.7242606 18.3318178,23.3569841 L18.3318178,23.5760728 C18.3318178,23.6589155 18.398975,23.7260728 18.4818178,23.7260728 L19.2532047,23.7260728 C19.3360474,23.7260728 19.4032047,23.6589155 19.4032047,23.5760728 Z M19.5271669,25.65 L19.5271669,24.5087964 C19.5271669,24.4259537 19.4600096,24.3587964 19.3771669,24.3587964 L18.3667101,24.3587964 C18.2838673,24.3587964 18.2167101,24.4259537 18.2167101,24.5087964 L18.2167101,25.65 C18.2167101,25.7328427 18.2838673,25.8 18.3667101,25.8 L19.3771669,25.8 C19.4600096,25.8 19.5271669,25.7328427 19.5271669,25.65 Z" id="？" fill="#FFFFFF" fill-rule="nonzero"></path>
                        <rect id="矩形" fill="#E9EDF4" fill-rule="evenodd" x="31.5632754" y="13.3995037" width="29.0322581" height="1.7866005" rx="0.297766749"></rect>
                        <rect id="矩形备份-5" fill="#E9EDF4" fill-rule="evenodd" x="31.5632754" y="17.866005" width="29.0322581" height="1.7866005" rx="0.297766749"></rect>
                        <rect id="矩形备份-7" fill="#0F72FF" fill-rule="evenodd" opacity="0.45988973" x="39.4540943" y="28.7344913" width="6.55086849" height="2.382134" rx="1.191067"></rect>
                        <rect id="矩形备份-8" fill="#FFFFFF" fill-rule="evenodd" opacity="0.45988973" x="32.0099256" y="28.7344913" width="6.55086849" height="2.382134" rx="1.191067"></rect>
                        <rect id="矩形备份-6" fill="#E9EDF4" fill-rule="evenodd" x="31.5632754" y="22.3325062" width="15.483871" height="1.7866005" rx="0.297766749"></rect>
                        <rect id="矩形备份-4" fill="#D5DAE2" fill-rule="evenodd" x="0" y="0" width="73.3995037" height="5.21091811" rx="0.595533499"></rect>
                    </g>
                </g>
                <path d="M70.1127681,120 L68.7840113,112.865488 L71.0210113,112.86542 L71.0217523,112.86142 L92.4682092,112.86142 L89.8027839,120 L70.1127681,120 Z" id="矩形备份-3" fill="#D5DAE2" mask="url(#mask-2)" transform="translate(80.626110, 116.430710) scale(-1, 1) translate(-80.626110, -116.430710) "></path>
                <path d="M65.4560601,73.6775118 C67.8460114,73.1702396 70.4667693,75.8267458 71.5483394,79.7181751 C72.6310468,81.2154757 73.6354309,83.6418971 74.2378103,86.4718072 C74.7219847,88.7464089 74.8627205,90.8883578 74.7028364,92.5805565 C75.3005461,93.3858373 75.8494834,94.4086511 76.3171918,95.6918279 C78.5183193,101.730719 77.7091908,111.29934 75.2559384,112.190972 C72.8026861,113.082604 68.0231904,107.33404 65.8220593,101.29515 C64.7259641,98.2879637 64.5695752,95.7634715 64.9917413,93.8765415 C64.4444432,93.5112752 63.9065256,93.0564993 63.406776,92.5213519 C61.2950329,90.2600245 60.6644847,87.4199182 61.9984087,86.1777948 C62.407844,85.7965373 62.9547674,85.6101439 63.5772704,85.6003025 C63.1143745,84.6810874 62.7424043,83.632509 62.500775,82.4973615 C61.5792875,78.1683099 62.9024135,74.2195288 65.4560601,73.6775118 Z" id="椭圆形-3备份" fill="#F4F7FA" mask="url(#mask-2)" transform="translate(69.449985, 92.949133) rotate(-16.000000) translate(-69.449985, -92.949133) "></path>
                <path d="M94.6236847,75.8410043 C98.190129,75.3711708 102.490902,80.2368661 104.740775,87.0091018 L104.608051,87.0072578 C107.067701,86.9511333 108.795835,90.8255241 108.467946,95.6609519 C108.399387,96.6720026 108.246368,97.6450155 108.024722,98.5522077 C108.372495,99.0614141 108.700117,99.627488 109.002651,100.254285 C111.984692,106.432534 110.888503,116.22197 107.56489,117.134178 C104.241278,118.046386 97.7661218,112.165163 94.7840804,105.986913 C93.8609209,104.074294 93.4297787,102.352528 93.3730997,100.859683 C90.0947167,94.8549725 88.9668101,89.9303594 90.8083855,89.3230976 C89.3854338,82.2103763 90.9770499,76.3214018 94.6236847,75.8410043 Z" id="椭圆形-3备份-4" fill="#E9EDF4" mask="url(#mask-2)" transform="translate(100.333872, 96.518894) scale(-1, 1) rotate(-40.000000) translate(-100.333872, -96.518894) "></path>
                <path d="M82.1562721,87.216021 C91.2744759,92.1837009 97.9064609,96.3158058 102.052227,99.612336 C106.197993,102.908866 110.289237,107.303399 114.325957,112.795933" id="路径-2备份-3" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(98.241115, 100.005977) scale(-1, 1) translate(-98.241115, -100.005977) "></path>
                <line x1="96.093245" y1="98.3991588" x2="106.327419" y2="98.3991588" id="路径-5" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(101.210332, 98.399159) scale(-1, 1) translate(-101.210332, -98.399159) "></line>
                <line x1="92.7865579" y1="88.9174324" x2="98.1481721" y2="99.9227434" id="路径-5备份" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(95.455257, 94.383707) scale(-1, 1) translate(-95.455257, -94.383707) "></line>
                <path d="M70.425751,58.6722473 C73.6440484,57.9893639 77.2649976,62.190061 78.5134103,68.0549648 C78.8624875,69.694893 78.9891039,71.2912942 78.9200165,72.7552247 C78.9859338,72.748429 79.052907,72.7447351 79.1202722,72.7435592 C81.5908899,72.7004344 83.6530731,76.0608738 83.726285,80.2491728 C83.7591723,82.1305833 83.3851179,83.8590067 82.7391414,85.1961545 C83.179148,86.1845852 83.5732227,87.3347189 83.9058706,88.6669852 C86.0151635,97.1147858 84.4183386,109.681 81.5363022,110.398543 C78.6542658,111.116086 73.5610223,102.562599 71.4517262,94.1147991 C71.0363138,92.4510557 70.7845924,90.91571 70.670214,89.517238 C69.7175446,88.2167587 68.8334593,86.6311881 68.1143626,84.853913 C66.1491041,79.9966871 66.0663742,75.3146426 67.75537,73.5322907 C67.3893169,72.5991516 67.0852572,71.5912004 66.8590751,70.5286223 C65.6106641,64.6637181 67.2075378,59.355527 70.425751,58.6722473 Z" id="椭圆形-3备份-3" fill="#E9EDF4" mask="url(#mask-2)" transform="translate(75.652989, 84.520122) scale(-1, 1) rotate(21.000000) translate(-75.652989, -84.520122) "></path>
                <path d="M85.830128,58.5102745 C89.2897525,57.775962 93.10637,61.9351281 94.3547827,67.8000319 C94.7161226,69.497569 94.8227054,71.1519466 94.7078445,72.6637424 C95.0086065,72.6101294 95.3123256,72.6052955 95.61497,72.653161 C98.0555431,73.0391562 99.5020357,76.7060635 98.845802,80.8434278 C98.6816957,81.8780699 98.4016328,82.8441548 98.035704,83.7052317 C98.9712037,84.854952 99.8282027,86.3694565 100.544551,88.3347876 C103.526591,96.5161452 102.430401,109.479503 99.1067885,110.687467 C95.783176,111.89543 89.3080199,104.107402 86.3259757,95.9260463 C84.8409251,91.8517428 84.6291135,88.431458 85.2011771,85.8750392 C84.4592492,85.3803162 83.7306842,84.764322 83.0538028,84.0394963 C80.1928595,80.9758994 79.3386067,77.1281856 81.1457776,75.4453841 C81.7008422,74.9285196 82.4424217,74.6760057 83.2864754,74.6629838 C82.6578123,73.4180966 82.1537749,71.9973134 81.8263726,70.4592136 C80.5779615,64.5943095 82.3705035,59.2445874 85.830128,58.5102745 Z" id="椭圆形-3" stroke="#8C8C8C" stroke-width="0.3" fill="#FFFFFF" stroke-linecap="round" mask="url(#mask-2)" transform="translate(91.241011, 84.619045) scale(-1, 1) translate(-91.241011, -84.619045) "></path>
                <line x1="83.0816492" y1="63.4207539" x2="96.1560425" y2="113.062498" id="路径-2" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(89.618846, 88.241626) scale(-1, 1) translate(-89.618846, -88.241626) "></line>
                <line x1="80.1883588" y1="64.8247244" x2="72.443805" y2="113.01014" id="路径-2备份" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(76.347857, 88.851946) scale(-1, 1) translate(-76.347857, -88.851946) "></line>
                <line x1="90.6521446" y1="78.0884315" x2="99.2943687" y2="84.5201964" id="路径-4" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(94.973257, 81.304314) scale(-1, 1) translate(-94.973257, -81.304314) "></line>
                <line x1="89.7415532" y1="77.3172396" x2="86.4650112" y2="88.3225507" id="路径-4备份" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(87.975405, 82.847485) scale(-1, 1) translate(-87.975405, -82.847485) "></line>
                <line x1="77.8689687" y1="78.8910145" x2="80.2519084" y2="96.6508942" id="路径-6" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(79.060439, 87.814240) scale(-1, 1) translate(-79.060439, -87.814240) "></line>
                <line x1="76.3796314" y1="79.9942072" x2="68.6350776" y2="88.9174324" id="路径-6备份" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(72.507355, 84.393670) scale(-1, 1) translate(-72.507355, -84.393670) "></line>
                <path d="M46.3274162,82.3843282 L41.5024003,50.272609 L41.5024003,50.272609 L43.2649961,45.9549912 L56.8375052,41.1253737 L70.4097797,45.9549912 L72.1723755,50.272609 L67.3476065,82.382685 C67.1974188,83.3822227 66.4976287,84.2122321 65.5378561,84.5291997 L56.8218932,87.4076716 L56.8218932,87.4076716 L48.1348767,84.5300855 C47.1762139,84.2125271 46.4774749,83.3830073 46.3274162,82.3843282 Z" id="路径" fill="#E3E3E3" mask="url(#mask-2)" transform="translate(56.837388, 64.266523) scale(-1, 1) translate(-56.837388, -64.266523) "></path>
                <g id="编组-5" mask="url(#mask-2)">
                    <g transform="translate(56.304409, 42.385815) scale(-1, 1) rotate(-7.000000) translate(-56.304409, -42.385815) translate(50.045945, 33.577115)">
                        <mask id="mask-4" fill="white">
                            <use xlink:href="#path-3"></use>
                        </mask>
                        <use id="矩形-2" stroke="#000000" stroke-width="0.3" fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-3"></use>
                        <path d="M10.0725011,-13.2950497 L11.6380683,-13.2101403 C15.3989954,-13.0061645 18.3341258,-9.8798525 18.3006879,-6.11354652 L18.2344066,1.35213069 C18.203684,4.81260655 15.3735084,7.59297091 11.9130326,7.5622483 C11.6707202,7.56009702 11.4287366,7.5438913 11.1883012,7.5137128 L9.6728136,7.32349481 C6.46050913,6.92029911 3.93660477,4.37948894 3.5548629,1.16456438 L2.76323374,-5.50231761 C2.3086914,-9.33034763 5.04344349,-12.8020585 8.87147351,-13.2566008 C9.26996644,-13.3039181 9.67179763,-13.316782 10.0725011,-13.2950497 Z" id="矩形" stroke="none" fill="#C4C4C4" fill-rule="evenodd" mask="url(#mask-4)"></path>
                    </g>
                </g>
                <path d="M49.7741114,29.4922045 C49.9305823,29.4922045 51.7954903,27.0431438 52.1068615,24.9444581 C52.4182328,22.8457725 52.5665582,20.7656028 50.8711632,20.5574344 C49.1757682,20.3492661 48.447396,22.1203906 48.787296,24.5368667 C49.1271959,26.9533428 49.6176405,29.4922045 49.7741114,29.4922045 Z" id="椭圆形备份-40" fill="#9299A4" mask="url(#mask-2)" transform="translate(50.503835, 25.016510) scale(-1, 1) rotate(-7.000000) translate(-50.503835, -25.016510) "></path>
                <path d="M55.2805699,20.6325604 C58.7680589,20.6325604 61.6095821,23.3862259 61.7314784,26.8289251 L61.7315037,26.8675604 L61.7136716,27.521509 C61.6902638,28.4259949 61.6718447,29.294925 61.658414,30.1282991 L63.3390348,33.5115348 C63.3755958,33.5851854 63.3455286,33.6745295 63.271878,33.7110905 C63.2635936,33.7152029 63.254944,33.7185345 63.2460413,33.7210419 L61.5886011,34.1879176 C61.3398521,36.8200942 60.046204,39.3514462 58.821615,39.462865 C58.1887114,39.5204495 57.4171006,39.4131629 56.5826058,39.1702167 L56.4132562,43.2675604 L59.5881233,43.9836668 C59.6227841,48.1243344 58.8069984,50.4337365 57.1407664,50.9118731 C55.6580413,51.337351 53.1441489,50.4153697 49.5990891,48.1459292 L48.68211,48.1460138 L48.7061425,47.5604249 C48.5510464,47.4564296 48.3941522,47.3500847 48.2354599,47.2413903 L47.0722562,42.0861157 L48.9432562,41.7865604 L49.2693428,33.872631 C49.1066683,33.6092469 48.9624974,33.3410094 48.8395037,33.0689486 L48.9500168,27.1183264 C48.8500584,23.6372823 51.6033112,20.7346591 55.0995792,20.6351359 C55.159894,20.633419 55.2202304,20.6325604 55.2805699,20.6325604 Z" id="矩形-2" stroke="#8C8C8C" stroke-width="0.3" fill="#FFFFFF" mask="url(#mask-2)" transform="translate(55.213426, 35.822598) scale(-1, 1) rotate(-7.000000) translate(-55.213426, -35.822598) "></path>
                <path d="M53.6927627,36.0154755 C55.2923471,37.5390772 57.3914561,38.7312556 59.2170651,39.2941927 L59.1099138,40.8228572 C56.5353614,40.3396427 54.4645783,38.4703407 53.6927627,36.0154755 Z" id="矩形-2" fill="#EDF0F4" mask="url(#mask-2)" transform="translate(56.454914, 38.419204) scale(-1, 1) rotate(-7.000000) translate(-56.454914, -38.419204) "></path>
                <path d="M61.1291042,21.8089456 C62.1869507,20.708346 63.9329,19.8595678 66.3669521,19.2626109 L67.4049314,29.6980552 L65.8033067,31.2718474 L65.8033067,34.3282011 L63.9339207,37.5257802 C63.6209146,36.3551446 63.0680708,35.2892849 62.2753893,34.3282011 C60.8312187,32.5772218 59.8103628,31.1621449 59.3454192,29.4656144 C58.1923253,25.2580957 59.5423345,23.459845 61.1291042,21.8089456 Z" id="矩形" fill="#9299A4" mask="url(#mask-2)" transform="translate(63.159362, 28.394196) scale(-1, 1) rotate(-7.000000) translate(-63.159362, -28.394196) "></path>
                <path d="M49.7110324,22.432424 L51.2469974,22.432424 L51.2469974,25.0383086 L51.0300605,26.0470787 C50.8117713,24.8796453 50.6280895,24.0916672 50.4790149,23.6831444 C50.3299403,23.2746216 50.0739462,22.8577148 49.7110324,22.432424 Z" id="矩形" fill="#8F949C" mask="url(#mask-2)" transform="translate(50.479015, 24.239751) scale(-1, 1) rotate(-7.000000) translate(-50.479015, -24.239751) "></path>
                <g id="编组备份" mask="url(#mask-2)">
                    <g transform="translate(60.037663, 32.123489) rotate(7.000000) translate(-60.037663, -32.123489) translate(57.851763, 29.470550)">
                        <ellipse id="椭圆形" stroke="none" fill="#FFFFFF" fill-rule="evenodd" transform="translate(2.185900, 2.652939) rotate(14.000000) translate(-2.185900, -2.652939) " cx="2.18589964" cy="2.65293852" rx="1.67525817" ry="2.31646582"></ellipse>
                        <path d="M1.14407812,2.65293852 C1.32692333,2.02175565 1.64882346,1.65086198 2.1097785,1.54025752 C2.57073354,1.42965306 2.95448744,1.59983283 3.26104019,2.05079681" id="路径-25" stroke="#8C8C8C" stroke-width="0.3" fill="none" stroke-linecap="round"></path>
                        <path d="M2.35748528,1.50758115 C2.51620171,1.65230314 2.60585168,1.82004622 2.62643518,2.01081041 C2.64701869,2.20157459 2.60262397,2.45324903 2.49325105,2.76583371" id="路径-26" stroke="#8C8C8C" stroke-width="0.3" fill="none" stroke-linecap="round"></path>
                    </g>
                </g>
                <path d="M57.8073462,26.0556333 C61.8173089,26.0525295 65.0694756,24.1835249 65.0712645,21.8810906 C65.073052,19.5786562 61.8237847,17.7146771 57.813822,17.7177728 C53.8038593,17.7208763 50.7408661,18.4910897 50.3719743,21.4200695 C50.0030825,24.3490492 53.7973836,26.0587287 57.8073462,26.0556333 Z" id="椭圆形" fill="#9299A4" mask="url(#mask-2)" transform="translate(57.709185, 21.886703) scale(-1, 1) rotate(2.000000) translate(-57.709185, -21.886703) "></path>
                <ellipse id="椭圆形" fill="#9299A4" mask="url(#mask-2)" transform="translate(53.005564, 18.804197) scale(-1, 1) rotate(-48.000000) translate(-53.005564, -18.804197) " cx="53.0055637" cy="18.804197" rx="1" ry="3.05210918"></ellipse>
                <path d="M58.3951485,31.2287027 C58.5515849,31.2285534 60.2277693,28.2146433 60.2865926,26.0464874 C60.345416,23.8783314 60.1694023,22.1410002 58.4751891,22.1425485 C56.780976,22.1442349 56.751199,23.4533578 57.0887077,25.8700443 C57.4262163,28.2867307 58.238712,31.2288521 58.3951485,31.2287027 Z" id="椭圆形备份-41" fill="#9299A4" mask="url(#mask-2)" transform="translate(58.607527, 26.685626) scale(-1, 1) rotate(6.000000) translate(-58.607527, -26.685626) "></path>
                <path d="M51.8089827,30.6742206 C52.0555388,30.6787073 52.2616372,30.3105403 52.2693163,29.8518966 C52.2769954,29.3932529 52.0833473,29.0178115 51.8367912,29.0133248 C51.590235,29.0088382 51.3841366,29.3770052 51.3764575,29.8356489 C51.3687784,30.2942926 51.5624265,30.6697339 51.8089827,30.6742206 Z" id="椭圆形" fill="#8C8C8C" mask="url(#mask-2)" transform="translate(51.822887, 29.843773) scale(-1, 1) rotate(-8.000000) translate(-51.822887, -29.843773) "></path>
                <path d="M51.150695,27.8698917 C51.6671837,27.809547 52.1234975,27.809547 52.5196363,27.8698917 C52.9157751,27.9302365 53.3049893,28.0340543 53.6872789,28.1813451" id="路径-24" stroke="#8C8C8C" stroke-width="0.5" stroke-linecap="round" mask="url(#mask-2)" transform="translate(52.418987, 28.002989) scale(-1, 1) rotate(-7.000000) translate(-52.418987, -28.002989) "></path>
                <path d="M50.9772889,34.5822993 C50.8991763,34.3357667 50.7319093,34.1673098 50.4754881,34.0769287 C50.2190668,33.9865477 49.9396836,34.0274347 49.6373385,34.1995899" id="路径-9" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" transform="translate(50.307314, 34.306278) scale(-1, 1) rotate(-15.000000) translate(-50.307314, -34.306278) "></path>
                <path d="M65.8733469,86.4337873 L68.1449828,91.4582536 L76.1805737,121.124775 L63.8882015,121.744331 L57.5477179,103.984458 C57.4363021,103.67238 57.0929919,103.50971 56.7809131,103.621126 C56.5294326,103.710907 56.3675482,103.956077 56.3837554,104.222612 L57.4492043,121.744331 L57.4492043,121.744331 L45.4945978,121.744331 L43.3121272,91.0408332 L44.0174616,86.9429251 L65.8733469,86.4337873 Z" id="路径" stroke="#8C8C8C" stroke-width="0.3" fill="#FFFFFF" stroke-linecap="round" mask="url(#mask-2)"></path>
                <polygon id="矩形" stroke="#8C8C8C" stroke-width="0.3" fill="#FFFFFF" stroke-linecap="round" mask="url(#mask-2)" points="39.753722 59.020969 46.3027295 58.9578164 46.3027295 77.2704715 36.8811195 76.6714992"></polygon>
                <path d="M51.9670591,42.3573201 C44.8520969,44.3904834 40.6371066,45.9362399 39.3220883,46.9945896 C38.00707,48.0529393 38.8098371,52.9237265 41.7303896,61.6069512 L46.6841255,85.4680429 L46.2172474,86.1844701 L46.7400836,87.4076716 L69.4789518,87.2755108 L70.0847633,85.9348246 L69.0219729,84.619045 L71.0693027,62.5310174 L74.7301581,62.5310174 L73.078878,48.7636944 C72.9251921,47.4823592 72.0641798,46.3962488 70.8517072,45.9542651 L60.9843814,42.3573201 L60.9843814,42.3573201 L59.8932355,47.4814282 L51.9670591,42.3573201 Z" id="路径-35" fill="#CCD2DA" mask="url(#mask-2)" transform="translate(56.719918, 64.882496) scale(-1, 1) translate(-56.719918, -64.882496) "></path>
                <path d="M48.4392167,38.1715555 C48.8503468,38.1715555 49.1836336,38.5048422 49.1836336,38.9159723 L49.1836336,41.8936398 L49.1836336,41.8936398 L47.6947998,41.8936398 L47.6947998,38.9159723 C47.6947998,38.5048422 48.0280866,38.1715555 48.4392167,38.1715555 Z" id="矩形" stroke="#8C8C8C" stroke-width="0.3" fill="#FFFFFF" stroke-linecap="round" mask="url(#mask-2)" transform="translate(48.439217, 40.032598) rotate(-13.000000) translate(-48.439217, -40.032598) "></path>
                <path d="M64.5987676,37.8952018 L65.743575,38.4392317 L65.743575,38.4392317 C66.0976105,38.3864158 66.3575376,38.4175043 66.5233562,38.5324973 C66.6891749,38.6474902 66.77502,38.8051517 66.7808915,39.0054817 C66.8908855,38.9805699 67.003756,39.0334963 67.0549245,39.1340003 C67.7774778,40.5524702 68.1673766,41.3618939 68.2246209,41.5622716 C68.3138772,41.8747042 68.1751741,42.1808682 67.9504695,42.2661935 C67.8006664,42.323077 67.6022724,42.3055469 67.3552876,42.2136032 L66.9133289,41.5622716 C66.8244295,42.1066515 66.7802838,42.4169796 66.7808915,42.4932559 C66.7840442,42.8889105 67.2383044,43.8081346 67.5049822,44.3746107 C67.6653549,44.715274 67.9314701,45.2200129 68.3033275,45.8888273 C69.0910503,47.3056075 69.0777554,49.0317817 68.2683036,50.4362606 L68.0782028,50.7661042 L68.0782028,50.7661042 L61.1987158,78.1908808 L58.5383517,77.4696774 L58.4009888,78.4052228 L53.3476701,79.9503722 L45.3067884,61.8011052 L52.7157085,58.6442033 L56.5864907,69.9330521 L63.4894624,49.9799895 C63.5918578,49.684015 63.5983104,49.3632875 63.5079022,49.0634341 L62.2740089,44.9710288 L62.2740089,44.9710288 L61.0561917,40.9319428 C60.9880783,40.7060338 61.0597642,40.4612146 61.2389703,40.3077236 L63.9557536,37.9807838 C64.1343865,37.8277838 64.3863352,37.7942506 64.5987676,37.8952018 Z" id="路径" stroke="#8C8C8C" stroke-width="0.3" fill="#FFFFFF" stroke-linecap="round" mask="url(#mask-2)" transform="translate(57.449204, 58.839792) scale(-1, 1) translate(-57.449204, -58.839792) "></path>
                <g id="编组" mask="url(#mask-2)">
                    <g transform="translate(36.874588, 69.543053)">
                        <path d="M4.04890748,3.81785035 L21.32,5.91659858 L27.8480125,4.54219607 C28.1276435,4.48330129 28.4183163,4.50612588 28.6853276,4.60794484 L31.547835,5.6995 C31.7332655,5.77020991 31.8262649,5.97785276 31.755555,6.16328328 C31.7422736,6.19811268 31.7236537,6.23066344 31.7003634,6.2597675 C31.5125123,6.49451034 31.2058042,6.59936385 30.9135614,6.52874938 L28.840099,6.0277396 L28.0919902,6.12252462 L28.1320766,6.44524254 C28.1565544,6.64230186 28.2776411,6.81419691 28.4549519,6.90359721 L31.7298589,8.55480904 C31.9217988,8.65164222 32.0466885,8.84438494 32.0565929,9.05913943 C32.0641817,9.22368517 32.0480585,9.36780946 32.0082231,9.49151231 C31.9413014,9.69932813 31.7940788,9.83152248 31.5665553,9.88809537 C31.6313169,10.123955 31.6092964,10.3423285 31.5004938,10.5432159 C31.3916911,10.7441034 31.1496163,10.8843077 30.7742692,10.963829 C30.7506289,11.1563315 30.6606262,11.3285105 30.5042609,11.4803661 C30.3478956,11.6322217 30.1439862,11.7404513 29.8925326,11.8050551 L29.1303733,12.6055009 L27.3889767,12.8964966 C27.2244597,12.9239882 27.0557472,12.9115905 26.8970161,12.8603453 L20.6822986,10.8539696 L20.682,10.8475986 L4.48965358,10.817822 C3.55680823,10.8160735 2.65862695,10.4641058 1.97295113,9.83160898 L0.479369698,8.45386542 C-0.125020866,7.89634965 -0.163020391,6.95443846 0.394495383,6.35004789 L0.434340639,6.30849416 L0.434340639,6.30849416 L2.32104963,4.41554656 C2.77423074,3.96086696 3.41164447,3.74037442 4.04890748,3.81785035 Z" id="路径-15" stroke="#8C8C8C" stroke-width="0.3" fill="#FFFFFF" fill-rule="evenodd"></path>
                        <polygon id="矩形" stroke="none" fill="#FFFFFF" fill-rule="evenodd" points="1.7866005 0.0805142137 6.1428071 0 6.57102595 4.60409139 0.192434053 7.01868252 0.336095773 6.0114243"></polygon>
                        <line x1="30.2718139" y1="9.51401919" x2="31.4628809" y2="9.81178594" id="路径-13" stroke="#8C8C8C" stroke-width="0.3" fill="none" stroke-linecap="round"></line>
                        <line x1="29.8251638" y1="10.8539696" x2="30.7770035" y2="10.9116194" id="路径-13备份" stroke="#8C8C8C" stroke-width="0.3" fill="none" stroke-linecap="round"></line>
                        <line x1="29.0807469" y1="11.8961532" x2="29.881122" y2="11.8049196" id="路径-13备份-2" stroke="#8C8C8C" stroke-width="0.3" fill="none" stroke-linecap="round"></line>
                        <line x1="4.66387349" y1="3.85645095" x2="6.86470062" y2="4.16558716" id="路径-18" stroke="#8C8C8C" stroke-width="0.3" fill="none" stroke-linecap="round"></line>
                    </g>
                </g>
                <path d="M51.7779211,41.4601404 L50.6243256,39.6992664 C50.5126858,39.5288569 50.3360849,39.4116464 50.135693,39.3749596 L48.117517,39.0054817 L48.117517,39.0054817" id="路径-3" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)"></path>
                <polyline id="路径-3备份" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)" points="52.8925282 40.8208863 51.4137165 38.7202353 49.0902923 38.4392317"></polyline>
                <path d="M47.5431209,42.2136032 L48.598447,40.5846287 C48.6374524,40.524421 48.7138467,40.5005902 48.7801659,40.5279424 L49.2258174,40.7117433 L49.2258174,40.7117433 L49.7009564,41.5429108 C49.8092955,41.73243 49.8052604,41.9660353 49.6904405,42.1516997 L47.0128575,46.4813704 C46.8663164,46.7183284 46.7972823,46.9950722 46.8153608,47.2730948 L46.9853413,49.8871569 L46.9853413,49.8871569" id="路径-10" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)"></path>
                <line x1="58.3119179" y1="69.9330521" x2="58.8622559" y2="72.1011655" id="路径-14" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)"></line>
                <polyline id="路径-16" stroke="#8C8C8C" stroke-width="0.3" fill="#CCD2DA" mask="url(#mask-2)" points="63.4465944 49.1373764 60.9078835 58.1021203 70.6315677 62.2137675 71.5012961 60.8189719"></polyline>
                <line x1="41.9812287" y1="53.5511926" x2="42.3705328" y2="62.5310174" id="路径-17" stroke="#8C8C8C" stroke-width="0.3" stroke-linecap="round" mask="url(#mask-2)"></line>
            </g>
        </g>
    </g>
</svg>