import React, { useMemo } from 'react';
import DashboardCard from '../DashboardCard.jsx';
import ScrollBoard from '../ScrollBoard.jsx';
import styles from '../../index.module.less';

const EmergencyTask = ({data}) => {
  const tableData = [];
  if (data.length) {
    data.forEach((val) => {
      const {
        supplierCode,
        bomCode,
        model,
        inspectSumQty,
        creationDate,
      } = val;
      tableData.push([
        `<Tooltip title='${supplierCode}'><span>${supplierCode}</span></Tooltip>`,
        `<Tooltip title='${bomCode}'><span>${bomCode}</span></Tooltip>`,
        `<Tooltip title='${model}'><span>${model}</span></Tooltip>`,
        `<Tooltip title='${inspectSumQty}'><span>${inspectSumQty}</span></Tooltip>`,
        `<Tooltip title='${creationDate}'><span>${creationDate}</span></Tooltip>`,
      ]);
    });
  }
  const config = useMemo(
    () => ({
      header: ['供应商代码', 'BOM', '规格', '数量', '超期报检时间'],
      data: tableData,
      rowNum: 7,
      align: ['center'],
      oddRowBGC: 'rgba(22,66,127,0.3)',
      headerBGC: 'rgb(3, 157, 206,0.3)',
      evenRowBGC: 'rgba(3,28,60, 0.3)',
      headerHeight: 40,
      columnWidth: [120, 100, 110, 100, 150],
    }),
    [tableData],
  );
  return (
    <DashboardCard height="100%">
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-scroll-board-title']}>
          原材料超期报检信息
        </div>
        <div className={styles['my-scroll-board-table']}>
          <ScrollBoard config={config} style={{ width: '100%', height: '100%', marginLeft: '10px' }} />
        </div>
      </div>
    </DashboardCard>
  );
};

export default EmergencyTask;
