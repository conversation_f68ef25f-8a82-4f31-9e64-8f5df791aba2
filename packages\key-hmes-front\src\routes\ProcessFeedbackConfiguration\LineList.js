import React from 'react';
import { Table, Button, Modal, Lov, Form, Switch, TextField, Select } from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
// import notification from 'utils/notification';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.hmes.EquipmentGroupMaintenance';

const LineList = props => {
  const { canEdit, assObjectsDs, handleCreateLine } = props;

  const editModal = record => {
    // if (canEdit) {
    const modalKey = Modal.key();
    Modal.open({
      key: modalKey,
      drawer: true,
      width: 600,
      title: intl.get(`${modelPrompt}.batchCreate`).d('批量新建'),
      closable: true,
      children: (
        <Form dataSet={assObjectsDs} columns={1} record={record}>
          <Lov name="operationObj" disabled={!canEdit} />
          <TextField name="description" disabled />
          <Lov name="tagObj" disabled={!canEdit} />
          <TextField name="tagDescription" disabled />
          <Switch name="enableFlag" disabled={!canEdit} />
          <TextField name="realName" disabled />
          <TextField name="lastUpdateDate" disabled />
        </Form>
      ),
      onOk: () => createLine(),
    });
    // }
    // else {
    //   return notification.warning({
    //     message: '请点击编辑',
    //     placement: 'bottomRight',
    //   });
    // }
  };

  const createLine = () => {
    assObjectsDs.current.set(assObjectsDs[0]);
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!canEdit}
          onClick={() => handleCreateLine()}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      lock: 'left',
      renderer: ({ record }) => {
        return (
          record.get('operationTagConfigLineId') === '' && (
            <Popconfirm
              title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => {
                assObjectsDs.remove(record);
              }}
            >
              <Button funcType="flat" icon="remove" shape="circle" size="small" />
            </Popconfirm>
          )
        );
      },
    },
    // 参数项工艺
    {
      name: 'operationObj',
      align: 'left',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a onClick={() => editModal(record)}>{record.get('operationName')}</a>
          </span>
        );
      },
      editor: record => {
        return (
          record.get('operationTagConfigLineId') === '' && (
            <Lov dataSet={assObjectsDs} name="operationObj" />
          )
        );
      },
    },
    // 工艺名称
    {
      name: 'description',
      align: 'left',
      renderer: ({ record }) => record.get('description'),
    },
    // 采集项
    {
      name: 'tagObj',
      align: 'left',
      renderer: ({ record }) => record.get('tagCode'),
      editor: record => {
        return (
          record.get('operationTagConfigLineId') === '' && (
            <Lov dataSet={assObjectsDs} name="tagObj" />
          )
        );
      },
    },
    // 采集项描述
    {
      name: 'tagDescription',
      align: 'left',
      renderer: ({ record }) => record.get('tagDescription'),
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      editor: record => {
        return (
          record.get('operationTagConfigLineId') === '' && (
            <Select dataSet={assObjectsDs} name="enableFlag" />
          )
        );
      },
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '有效' : '无效'}>
          {}
        </Badge>
      ),
    },
    // 更新人
    {
      name: 'realName',
      align: 'left',
      renderer: ({ record }) => record.get('realName'),
    },
    // 更新时间
    {
      name: 'lastUpdateDate',
      align: 'left',
      renderer: ({ record }) => record.get('lastUpdateDate'),
    },
  ];

  return <Table dataSet={assObjectsDs} columns={columns} virtual style={{ height: 200 }} />;
};

export default LineList;
