/**
 * @Description: 体系审核管理维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.systemAudit';
const tenantId = getCurrentOrganizationId();

// 详情-审核记录表单
const auditProblemFormDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureType`).d('措施类型'),
      name: 'measureType',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_TYPE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编号'),
      name: 'problemCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposeTimePeriod`).d('发生时间段'),
      name: 'proposeTimePeriod',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
      name: 'problemTitle',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      name: 'problemStatus',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_STATUS',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.registerPerson`).d('登记人'),
      name: 'registerPersonRealName',
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.registerTime`).d('登记时间'),
      name: 'registerTime',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposePerson`).d('提出人'),
      name: 'proposePersonRealName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposeDapartment`).d('登记部门'),
      name: 'proposePersonCompanyName',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.proposeTime`).d('提出时间'),
      name: 'proposeTimePeriod',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.findingMethod`).d('问题发现方式'),
      name: 'findingMethod',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_FINDING_METHOD',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.frequency`).d('新发/再发'),
      name: 'frequency',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_FREQUENCY',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.leadPerson`).d('问题跟进人'),
      name: 'leadPersonRealName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemProperty`).d('问题属性'),
      name: 'problemProperty',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PREVIEW_PROBLEM_PROPERTY',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemRange`).d('问题影响范围'),
      name: 'influenceRange',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_INFLUENCE_RANGE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewStandard`).d('不符合规章制度/标准条款'),
      name: 'sysReviewStandardLov',
      lovPara: { tenantId },
      lovCode: 'YP.QIS.SYS_REVIEW_STANDARD',
      multiple: true,
    },
    {
      name: 'sysReviewStandardIdList',
      bind: 'sysReviewStandardLov.uniqueCode',
    },
    {
      name: 'sysReviewStandardNameList',
      bind: 'sysReviewStandardLov.fileName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.conformityLevel`).d('符合度等级'),
      name: 'conformityLevel',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CONFORITY_LEVEL',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      name: 'projectName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectPhase`).d('项目阶段'),
      name: 'projectPhase',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_PROJECT_PHASE',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.factory`).d('发现工厂'),
      name: 'factoryLov',
      textField: 'siteName',
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'factoryLov.siteId',
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.factory`).d('发现工厂'),
      bind: 'factoryLov.siteName',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineName`).d('发现产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      lovPara: { tenantId },
    },
    {
      name: 'productionLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineName',
      label: intl.get(`${modelPrompt}.prodLineName`).d('发现产线'),
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'operationWkcLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationWkc`).d('发现工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        workcellType: 'PROCESS',
      },
    },
    {
      name: 'processId',
      bind: 'operationWkcLov.workcellId',
    },
    {
      name: 'processWorkcellName',
      label: intl.get(`${modelPrompt}.operationWkc`).d('发现工序'),
      bind: 'operationWkcLov.workcellName',
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipment`).d('发现设备'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentCode',
      lovPara: { tenantId },
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'equipmentCode',
      label: intl.get(`${modelPrompt}.equipment`).d('发现设备'),
      bind: 'equipmentLov.equipmentCode',
    },
    {
      name: 'equipmentName',
      label: intl.get(`${modelPrompt}.equipment`).d('发现设备'),
      bind: 'equipmentLov.equipmentName',
    },

    {
      name: 'externalReportFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.externalReportFlag`).d('外审报告体现'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_EXTERIAL_REPORT_FLAG',
    },
    {
      name: 'leadPersonRealName',
      label: intl.get(`${modelPrompt}.leadPerson`).d('跟进人'),
      bind: 'leadPersonLov.realName',
    },

    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDescription`).d('问题详细描述'),
      name: 'problemDescription',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemType`).d('问题类型'),
      name: 'problemType',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_TYPE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceLevel`).d('影响程度'),
      name: 'influenceLevel',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_INFLUENCE_LEVEL',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      name: 'severityLevel',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsiblePersonUnitCompanyName`).d('整改责任部门'),
      name: 'responsiblePersonUnitCompanyName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.solutionTool`).d('问题解决工具'),
      name: 'solutionTool',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SOLUTION_TOOL',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('上传附件'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    },
    {
      name: 'evidence',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.image`).d('上传图片'),
      bucketName: 'qms',
      accept: ['image/*'],
    },
  ],
});

// 详情-审核问题-责任小组
const distributeDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureType`).d('措施类型'),
      name: 'measureType',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_TYPE',
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'responsiblePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
    },
    {
      name: 'unitCompanyName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.apartment`).d('所属部门'),
    },
    {
      name: 'positionName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.position`).d('岗位'),
    },
    {
      name: 'phone',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.phone`).d('联系方式'),
    },
    {
      name: 'principalPersonFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.principalPersonFlag`).d('主责任人标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/responsible-person/ui`,
        method: 'GET',
      };
    },
  },
});

// 临时措施, 长期措施
const measureDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureType`).d('措施类型'),
      name: 'measureType',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_TYPE',
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'measureDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureDescription`).d('措施描述'),
    },
    {
      name: 'measureVerification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureVerification`).d('措施验证'),
    },
    {
      name: 'responsiblePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成时间'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际完成时间'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_ENABLE_FLAG',
    },
    {
      name: 'measureStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureStatus`).d('措施状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_STATUS',
    },
    {
      name: 'evidence',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/measure-change-object/ui`,
        method: 'GET',
      };
    },
  },
});

// 原因分析
const reasonAnalysisDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureType`).d('措施类型'),
      name: 'measureType',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_TYPE',
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'reasonStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonStatus`).d('原因状态'),
      lookupCode: 'YP.QIS.REASON_STATUS',
    },
    {
      name: 'occurrenceReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.occurrenceReason`).d('发生原因'),
    },
    {
      name: 'outflowReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outflowReason`).d('流出原因'),
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成时间'),
      required: true,
    },
    {
      name: 'responsiblePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
    },
    {
      name: 'recordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.recordTime`).d('记录时间'),
    },
    {
      name: 'rationalityFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rationalityFlag`).d('合理性'),
      lookupCode: 'YP.QIS_REASONABLE',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason-influence-range/ui`,
        method: 'GET',
      };
    },
  },
});

// 止呼待范围
const influenceRangeDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureType`).d('措施类型'),
      name: 'measureType',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_TYPE',
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
    },
    {
      name: 'manufacturingProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manufacturingProcess`).d('制程'),
    },
    {
      name: 'marketplace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketplace`).d('市场'),
    },
    {
      name: 'responsiblePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
    },
    {
      name: 'recordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.recordTime`).d('记录时间'),
    },
  ],
});

// 变更对象
const changeObjectDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureType`).d('措施类型'),
      name: 'measureType',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_TYPE',
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'objectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureDescription`).d('变更对象类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.CHANGE_OBJECT_TYPE',
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectCode`).d('变更对象编码'),
    },
    {
      name: 'objectDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectDescription`).d('变更对象说明'),
      required: true,
    },
    {
      name: 'responsiblePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
    },
    {
      name: 'recordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.recordTime`).d('记录时间'),
    },
  ],
});

// 验证关闭
const validateCloseDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureType`).d('措施类型'),
      name: 'measureType',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_TYPE',
    },
    {
      name: 'verifyClosedInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verifyClosedInfo`).d('验证关闭内容'),
    },
    {
      name: 'verifyClosedPersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verifyClosedPersonRealName`).d('验证人'),
    },
    {
      name: 'verifyClosedTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.verifyClosedTime`).d('验证时间'),
    },
    {
      name: 'enclosure8d',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure8d`).d('8D报告上传'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    },
  ],
});

const problemInfoDS: () => DataSetProps = () => ({
  autoCreate: true,
  selection: false,
  dataKey: 'rows',
  paging: false,
  primaryKey: 'problemId',
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureType`).d('措施类型'),
      name: 'measureType',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_TYPE',
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
      required: true,
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'siteName',
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'problemStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_STATUS',
      textField: 'meaning',
      valueField: 'value',
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题标题'),
      required: true,
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.detailDescription`).d('详细描述'),
      required: true,
    },
    {
      name: 'leadPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.leadPerson`).d('跟进人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      textField: 'realName',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'leadPerson',
      bind: 'leadPersonLov.id',
    },
    {
      name: 'leadPersonRealName',
      label: intl.get(`${modelPrompt}.leadPerson`).d('跟进人'),
      bind: 'leadPersonLov.realName',
    },
    {
      name: 'registerPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registerPerson`).d('登记人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      textField: 'realName',
      disabled: true,
    },
    {
      name: 'registerPerson',
      bind: 'registerPersonLov.id',
    },
    {
      name: 'registerPersonRealName',
      bind: 'registerPersonLov.realName',
    },
    {
      name: 'registerPersonCompanyName',
      bind: 'registerPersonLov.unitName',
      disabled: true,
      label: intl.get(`${modelPrompt}.registerPersonApartment`).d('登记人部门'),
    },
    {
      name: 'registerTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.registerTime`).d('登记时间'),
    },
    {
      name: 'proposePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.proposePerson`).d('提出人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.EMPLOYEE_POSITION',
      lovPara: { tenantId },
      textField: 'name',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => record?.get('problemId'),
      },
    },
    {
      name: 'proposePerson',
      bind: 'proposePersonLov.employeeId',
    },
    {
      name: 'proposePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposePerson`).d('提出人'),
      bind: 'proposePersonLov.name',
    },
    {
      name: 'proposePersonCompanyName',
      disabled: true,
      bind: 'proposePersonLov.unitName',
      label: intl.get(`${modelPrompt}.proposePersonCompanyName`).d('提出部门'),
    },
    {
      name: 'proposeTimePeriod',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.proposeTimePeriod`).d('问题发生时段'),
      range: ['start', 'end'],
      required: true,
    },
    // 跟进人填写内容
    {
      name: 'influenceLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceLevel`).d('影响程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_INFLUENCE_LEVEL',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet.getState('userRoleList')?.includes('LEAD_PERSON') &&
          record?.get('problemStatus') === 'PUBLISH',
      },
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet.getState('userRoleList')?.includes('LEAD_PERSON') &&
          record?.get('problemStatus') === 'PUBLISH',
      },
    },
    {
      name: 'responsiblePersonUnitCompanyName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsiblePersonUnitCompanyName`).d('整改责任部门'),
      disabled: true,
    },
    {
      name: 'solutionTool',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.solutionTool`).d('问题解决工具'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SOLUTION_TOOL',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet.getState('userRoleList')?.includes('LEAD_PERSON') &&
          record?.get('problemStatus') === 'PUBLISH',
      },
    },
    {
      name: 'marketProblemType',
      type: FieldType.string,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料号'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record, dataSet }) =>
          dataSet.getState('problemGroup') === 'MARKET' &&
          record.get('marketProblemType') === 'INCOMING' &&
          dataSet.getState('userRoleList')?.includes('LEAD_PERSON') &&
          record?.get('problemStatus') === 'PUBLISH',
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record, dataSet }) =>
          dataSet.getState('problemGroup') === 'MARKET' &&
          record.get('marketProblemType') === 'INCOMING' &&
          dataSet.getState('userRoleList')?.includes('LEAD_PERSON') &&
          record?.get('problemStatus') === 'PUBLISH',
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'repeatFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.repeatFlag`).d('是否重复问题'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_REPEAT_FLAG',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet.getState('problemGroup') === 'MARKET' &&
          dataSet.getState('userRoleList')?.includes('LEAD_PERSON') &&
          record?.get('problemStatus') === 'PUBLISH',
      },
    },
    {
      name: 'mergeProblemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.mergeProblemCode`).d('并入问题编号'),
      lovCode: 'YP.QMS.PROBLEM',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record, dataSet }) =>
          dataSet.getState('problemGroup') === 'MARKET' &&
          record.get('repeatFlag') === 'Y' &&
          dataSet.getState('userRoleList')?.includes('LEAD_PERSON') &&
          record?.get('problemStatus') === 'PUBLISH',
      },
    },
    {
      name: 'mergeProblemId',
      bind: 'mergeProblemLov.problemId',
    },
    {
      name: 'mergeProblemCode',
      bind: 'mergeProblemLov.problemCode',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/base-detail/ui`,
        method: 'GET',
      };
    },
  },
});

export {
  auditProblemFormDS,
  distributeDS,
  measureDS,
  reasonAnalysisDS,
  influenceRangeDS,
  changeObjectDS,
  validateCloseDS,
  problemInfoDS,
};
