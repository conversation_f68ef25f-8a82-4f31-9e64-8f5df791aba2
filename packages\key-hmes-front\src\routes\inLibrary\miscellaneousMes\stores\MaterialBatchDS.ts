import intl from 'utils/intl';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { isNull, isUndefined } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';
const tenantId = getCurrentOrganizationId();

const headDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  forceValidate: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-miscellaneous/material-lot-create/instruction-query/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.returnDeliveryNumber`).d('退货单号/行号'),
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'orderedQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.needQty`).d('需求数量'),
    },
    {
      name: 'createdQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.createdQuantity`).d('已创建数量'),
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
    },
    {
      name: 'materialLotQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
      required: true,
      // pattern: /^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/,
      defaultValidationMessages: {
        rangeUnderflow: intl.get(`${modelPrompt}.greater.than.zero`).d('必须大于零'), // 正则不匹配的报错信息
      },
      // validator: (...args: Array<any>) => {
      //   const {
      //     data: {
      //       materialSheets,
      //       materialLotQty = 0,
      //       orderedQuantity,
      //       createdQuantity,
      //       decimalNumber,
      //     },
      //   } = args[2];
      //   const inputQty = isUndefined(materialSheets) ? 0 : materialSheets * materialLotQty;
      //   const demandQty = parseFloat((orderedQuantity - createdQuantity).toFixed(decimalNumber));
      //   if (isUndefined(materialSheets) || isNull(materialSheets)) {
      //     return true;
      //   } if (inputQty > demandQty) {
      //     return intl
      //       .get(`${modelPrompt}.quantity.verification`)
      //       .d('输入的张数*数量必须小于等于需求数量-已创建数量!');
      //   }
      // },
      dynamicProps: {
        min: ({ record }) => {
          return 10 ** -record?.get('decimalNumber');
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return 10 ** -record?.get('decimalNumber');
        },
      },
    },
    {
      name: 'materialSheets',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialSheets`).d('张数'),
      required: true,
      step: 1,
      min: 1,
      validator: (...args: Array<any>) => {
        const {
          data: {
            materialSheets,
            materialLotQty = 0,
            orderedQuantity,
            createdQuantity,
            decimalNumber,
          },
        } = args[2];
        const inputQty = isUndefined(materialLotQty) ? 0 : materialLotQty * materialSheets;
        const demandQty = parseFloat((orderedQuantity - createdQuantity).toFixed(decimalNumber));
        if (isUndefined(materialLotQty) || isNull(materialLotQty)) {
          return true;
        } if (inputQty > demandQty) {
          return intl
            .get(`${modelPrompt}.quantity.verification`)
            .d('输入的张数*数量必须小于等于需求数量-已创建数量!');
        }
      },
    },
    {
      name: 'productionDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
      required: true,
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
  ],
});

const tableDS = (): DataSetProps => {
  return {
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    selection: DataSetSelection.multiple,
    autoLocateFirst: false,
    forceValidate: true,
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-miscellaneous/material-lot-create/material-lot-query/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_MATERIAL_LOT.CREATE`,
          method: 'GET',
        };
      },
    },
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    primaryKey: 'materialLotId',
    fields: [
      {
        name: 'identification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
      },
      {
        name: 'primaryUomQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
      },
      {
        name: 'primaryUomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'materialLotStatus',
        type: FieldType.string,
      },
      {
        name: 'materialLotStatusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
      },
      {
        name: 'productionDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
      },
      {
        name: 'lot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lot`).d('批次'),
      },
      {
        name: 'printTimes',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
      },
      {
        name: 'createdByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.createdByName`).d('操作人'),
      },
      {
        name: 'creationDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.creationDate`).d('操作时间'),
      },
    ],
  };
};

export { headDS, tableDS };
