/**
 * @Description: 体系审核管理维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { DataSet, Form, Attachment, Button, TextField, Spin } from 'choerodon-ui/pro';
import { Card } from 'choerodon-ui';
import notification from 'utils/notification';

import { getCurrentUser } from 'utils/utils';
import echarts from 'echarts';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import styles from '../index.modules.less';
import { auditCloseFormDS } from '../stores/AuditTab6CloseDS';
import {
  fetchAuditCloseInfoConfig,
  saveAuditCloseConfig,
  submitAuditCloseConfig,
} from '../services';


const modelPrompt = 'tarzan.systemAudit';

const option = {
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)',
  },
  legend: {
    data: [],
  },
  series: [
    {
      name: intl.get(`${modelPrompt}.sectorProportion`).d('部门占比'),
      type: 'pie',
      // selectedMode: 'single',
      radius: [0, '30%'],
      label: {
        position: 'inner',
        fontSize: 14,
      },
      labelLine: {
        show: false,
      },
      data: [],
    },
    {
      name: intl.get(`${modelPrompt}.problemProportion`).d('问题占比'),
      type: 'pie',
      radius: ['45%', '60%'],
      labelLine: {
        length: 30,
      },
      label: {
        formatter: '{a|{a}} \n {b|{b}：}{c}  {per|{d}%}  ',
        backgroundColor: '#F6F8FC',
        borderColor: '#8C8D8E',
        borderWidth: 1,
        borderRadius: 4,
        rich: {
          a: {
            color: '#6E7079',
            lineHeight: 22,
            align: 'center',
          },
          hr: {
            borderColor: '#8C8D8E',
            width: '100%',
            borderWidth: 1,
            height: 0,
          },
          b: {
            color: '#4C5058',
            fontSize: 14,
            fontWeight: 'bold',
            lineHeight: 33,
          },
          per: {
            color: '#fff',
            backgroundColor: '#4C5058',
            padding: [3, 4],
            borderRadius: 4,
          },
        },
      },
      data: [],
    },
  ],
};

const AuditClose = props => {
  const { id, canEdit, activeKey, setCanEdit, threeResList, pubFlag } = props;
  const chartRef = useRef<echarts>(null);

  const [user] = useState(getCurrentUser()); // 用户详细信息

  // 查询审核关闭详情
  const fetchAuditCloseInfo = useRequest(fetchAuditCloseInfoConfig(), {
    manual: true,
    needPromise: true,
  });
  // 保存审核关闭
  const saveAuditClose = useRequest(saveAuditCloseConfig(), {
    manual: true,
    needPromise: true,
  });
  // 提交审核关闭
  const submitAuditClose = useRequest(submitAuditCloseConfig(), {
    manual: true,
    needPromise: true,
  });

  const formDs = useMemo(() => new DataSet(auditCloseFormDS()), []);

  useEffect(() => {
    queryCloseDetail();
  }, [id, activeKey]);

  const handleCancel = () => {
    formDs.loadData([]);
    setCanEdit(false);
  };

  const handleSave = async () => {
    const validateResult = await formDs.validate();
    if (!validateResult) {
      return;
    }
    const formData = formDs.toData()[0] || {};
    const res = await saveAuditClose.run({
      params: formData,
      queryParams: {
        sysReviewPlanId: id,
      },
    });

    if (res?.success) {
      // @ts-ignore
      notification.success();
      setCanEdit(false);
      queryCloseDetail();
    }
  };

  const handleSubmit = async () => {
    const res = await submitAuditClose.run({
      queryParams: {
        sysReviewPlanId: id,
      },
    });

    if (res?.success) {
      // @ts-ignore
      notification.success();
      setCanEdit(false);
      queryCloseDetail();
    }
  };

  const clickChart = (e, data, _chart1) => {
    const { dataIndex, seriesType, componentIndex } = e;
    if (seriesType === 'pie' && componentIndex === 0) {
      option.series[1].data = data[dataIndex];
      _chart1.setOption(option);
      option.series[0].data.forEach((item, index) => {
        if (dataIndex === index) {
          _chart1.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: index,
          });
        } else {
          _chart1.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: index,
          });
        }
      });
    }
  };

  const queryCloseDetail = async () => {
    const res = await fetchAuditCloseInfo.run({
      params: {
        sysReviewPlanId: id,
      },
    });
    if (res?.success) {
      const { departmentChartInfo, ...other } = res?.rows || {};
      formDs.loadData([other]);

      formatChartData(departmentChartInfo);
    }
  };

  const formatChartData = departmentChartInfo => {
    const devName: any = [];
    const depData: any = [];
    const _depInfoData: any = [];

    (departmentChartInfo || []).forEach(item => {
      devName.push(item.departmentName);
      depData.push({
        value: item.departmentSize,
        name: item.departmentName,
      });
      _depInfoData.push(
        (item.statusChartInfo || []).map(infoItem => {
          return {
            value: infoItem.statusSize,
            name: infoItem.statusName,
          };
        }),
      );
    });

    option.legend.data = devName;
    option.series[0].data = depData;
    option.series[1].data = _depInfoData[0];

    const newChart = echarts.init(chartRef.current);
    newChart.setOption(option);
    newChart.on('click', 'series', e => {
      clickChart(e, _depInfoData, newChart);
    });
    option.series[0].data.forEach((item, index) => {
      newChart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: index,
      });
    });
    newChart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: 0,
    });
  };

  const getAuth = () => {
    return (
      user.id === threeResList[0]?.rows?.createdBy &&
      threeResList[0]?.rows?.sysReviewPlanStatus !== 'CLOSED'
    );
  };

  return (
    <div className={styles.tabsBody}>
      <Spin spinning={fetchAuditCloseInfo.loading}>
        <div className="control-row">
          <div></div>
          <div>
            {!canEdit && !pubFlag && (
              <>
                <Button disabled={!getAuth()} onClick={handleSubmit}>
                  {
                    intl.get(`${modelPrompt}.submitClosureReview`).d('提交关闭审核')
                  }
                </Button>
                <Button
                  disabled={!getAuth()}
                  color={ButtonColor.primary}
                  onClick={() => {
                    setCanEdit(true);
                  }}
                >
                  {intl.get('hzero.common.button.edit').d('编辑')}
                </Button>
              </>
            )}
            {canEdit && (
              <>
                <Button onClick={handleCancel}>
                  {intl.get('hzero.common.button.cancel').d('取消')}
                </Button>
                <Button color={ButtonColor.primary} onClick={handleSave}>
                  {intl.get('hzero.common.button.save').d('保存')}
                </Button>
              </>
            )}
          </div>
        </div>

        <div>
          <Card title={intl.get(`${modelPrompt}.problemFollowUp`).d('问题跟进情况')}>
            <div id="audit-tab-6-close-chart" ref={chartRef} className="close-chart"></div>
          </Card>
          <Card title={intl.get(`${modelPrompt}.AuditTab6Close`).d('审核关闭')}>
            <Form dataSet={formDs} columns={1} labelWidth={112} disabled={!canEdit}>
              <TextField name="reviewSummary" />
              <Attachment name="reviewReportUuid" />
            </Form>
          </Card>
        </div>
      </Spin>
    </div>
  );
};

export default AuditClose;
