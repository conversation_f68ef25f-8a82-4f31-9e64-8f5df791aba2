/*
 * @Description: 异常管理
 * @Version: 0.0.1
 * @Autor: quanhe.z<PERSON>@hand-china.com
 * @Date: 2020-06-22 14:51:49
 */
import request from 'utils/request';
import { BASIC, HZERO_PLATFORM } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const Host = BASIC.HMES_BASIC;
// const Host = '/mes-202207';
const organizationId = getCurrentOrganizationId();

// 获取默认站点
export async function getSiteList(params) {
  return request(`${Host}/v1/${organizationId}/wms-warehouse-locator/site/property`, {
    method: 'GET',
    query: params,
  });
}

// 工位输入
export async function enterSite(params) {
  return request(`${Host}/v1/${organizationId}/hme-exception-handle-platform/list/exception/ui`, {
    method: 'GET',
    query: params,
  });
}

/**
 * @description: 异常提交
 * @param {type} params 异常数据
 * @return: list
 */
export async function commitException(params) {
  return request(`${Host}/v1/${organizationId}/wms-warehouse-locator/site/property`, {
    method: 'POST',
    body: params,
  });
}

// 扫描设备
export async function enterEquipment(params) {
  return request(
    `${Host}/v1/${organizationId}/hme-exception-handle-platform/equipment/verification/ui`,
    {
      method: 'GET',
      query: params,
    },
  );
}

// 扫描物料
export async function enterMaterial(params) {
  return request(
    `${Host}/v1/${organizationId}/hme-exception-handle-platform/material/lot/verification/ui`,
    {
      method: 'GET',
      query: params,
    },
  );
}

// 创建异常消息记录
export async function createExceptionRecord(params) {
  return request(
    `${Host}/v1/${organizationId}/hme-exception-handle-platform/create/exception/record`,
    {
      method: 'POST',
      body: params,
    },
  );
}

// 查看历史
export async function showExceptionRecordModal(params) {
  return request(
    `${Host}/v1/${organizationId}/hme-exception-handle-platform/query/exception/history/ui`,
    {
      method: 'GET',
      query: params,
    },
  );
}

// 查看员工具体信息
export async function fetchLineList(params) {
  return request(`${Host}/v1/${organizationId}/hme-exception-router/list/ui`, {
    method: 'GET',
    query: params,
  });
}

// 查看岗位下的员工具体信息
export async function fetchPositionData(params) {
  return request(`${HZERO_PLATFORM}/v1/${organizationId}/positions/user/${params.positionId}`, {
    method: 'GET',
  });
}

// 调用异步接口
export async function asyncSetData(params) {
  return request(`${Host}/v1/${organizationId}/itf-exception-wkc-record/send/ebs/exception/info`, {
    method: 'POST',
    body: params,
  });
}

// 查看岗位下的员工具体信息
export async function fetchUserData(params) {
  return request(`${HZERO_PLATFORM}/v1/${organizationId}/employee-users/employee`, {
    method: 'GET',
    query: params,
  });
}

/**
 * 异常取消/异常关闭
 * @async
 * @function closeException
 * @param {object} params - 请求参数
 * @returns {object} fetch Promise
 */
export async function closeException(params) {
  return request(
    `${Host}/v1/${organizationId}/hme-exception-handle-platform/close-exception-record`,
    {
      method: 'POST',
      body: params,
    },
  );
}

/**
 * 异常响应/异常升级
 * @async
 * @function responseException
 * @param {object} params - 请求参数
 * @returns {object} fetch Promise
 */
export async function responseException(params) {
  return request(
    `${Host}/v1/${organizationId}/hme-exception-handle-platform/update-exception-record`,
    {
      method: 'POST',
      body: params,
    },
  );
}

/**
 * 扫描员工号
 * @async
 * @function queryEmployee
 * @param {object} params - 请求参数
 * @returns {object} fetch Promise
 */
export async function queryEmployee(params) {
  return request(
    `${Host}/v1/${organizationId}/hme-exception-handle-platform/query/employee-name/ui`,
    {
      method: 'GET',
      query: params,
    },
  );
}

// 现有量获取
export async function getNowQty(params) {
  return request(`${Host}/v1/${organizationId}/itf-onhand-quantity-ifaces/request-sap-iface`, {
    method: 'POST',
    body: params,
  });
}

// 查询默认责任人
export async function queryDefaultResponsible(params) {
  return request(`${Host}/v1/${organizationId}/hme-exception-handle-platform/query/user/ui`, {
    method: 'GET',
    query: params,
  });
}

// 查询自动升级信息
export async function refreshUpgrade(params) {
  return request(`${Host}/v1/${organizationId}/hme-exception-handle-platform/query/msg/ui`, {
    method: 'GET',
    query: params,
  });
}
