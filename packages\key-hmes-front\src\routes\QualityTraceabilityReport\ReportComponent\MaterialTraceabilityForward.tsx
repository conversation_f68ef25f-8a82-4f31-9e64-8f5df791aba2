import React, { useMemo, useCallback } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { useDataSetEvent } from 'utils/hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableMode, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from '../stores/MaterialTraceabilityForwardDS';
import { FetchMaterialForwardChildren } from '../services';

const MaterialTraceabilityForward = () => {

  const { run: fetchMaterialForwardChildren } = useRequest(FetchMaterialForwardChildren(), { manual: true, needPromise: true });

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const onLoadData = ({ dataSet }) => {
    if (dataSet.queryDataSet.current.get('allOpenFlag') === 'Y') {
      // 全部展开时不做处理
      return;
    }
    if (dataSet.getState('processing')) {
      dataSet.setState('processing', false)
      return;
    }
    const _data = dataSet.toData();
    let _dealData: any[] = [];
    _data.forEach((item) => {
      _dealData.push({
        ...item,
        childMaterialDtlList: [1],
      })
      if (item.childMaterialDtlList?.length) {
        _dealData = [..._dealData, ...item.childMaterialDtlList];
      }
    })
    dataSet.setState('processing', true)
    dataSet.loadData(_dealData);
  }

  useDataSetEvent(tableDs, 'load', onLoadData);

  const columns: ColumnProps[] = useMemo(
    () => [
      { name: 'materialCode', width: 240, lock: ColumnLock.left },
      { name: 'materialName', width: 150 },
      { name: 'componentTypeDesc', width: 150 },
      {
        name: 'materialLotCode',
        width: 150,
        renderer: ({ value, record }) => {
          if (record!.get('identifyType') === 'MATERIAL_LOT') {
            return value
          }
          return '';
        },
      },
      {
        name: 'lot',
        width: 150,
        renderer: ({ value, record }) => {
          if (record!.get('identifyType') === 'LOT') {
            return value
          }
          return '';
        },
      },
      { name: 'sumAssembleQty', width: 150 },
      { name: 'workcellCode', width: 150 },
      { name: 'equipmentCode', width: 150 },
      { name: 'equipmentName', width: 150 },
      { name: 'bindDate', width: 150 },
      { name: 'overStationDate', width: 150 },
      { name: 'overStationByName', width: 150 },
    ],
    [],
  );

  const handleExpand = useCallback(
    ({ record, dataSet }) => {
      if (record.get('childMaterialDtlList')?.length) {
        return;
      }
      if (dataSet.queryDataSet.current.get('allOpenFlag') === 'Y') {
        return;
      }
      return fetchMaterialForwardChildren({
        params: {
          parentEoId: record.get('childEoId'),
        },
      }).then(res => {
        if (
          !res?.success ||
          !res?.rows?.length
        ) {
          return
        }
        record.init('childMaterialDtlList', [1])
        dataSet.appendData(res.rows[0].childMaterialDtlList, record);
      })
    },
    [],
  )

  const nodeCover = useCallback(
    ({ record }) => {
      const nodeProps = { isLeaf: false };
      if (record.get('hasChildFlag') !== 'Y') {
        nodeProps.isLeaf = true;
      }
      return nodeProps;
    },
    [],
  )

  return (
    <>
      <Table
        mode={TableMode.tree}
        queryBar={TableQueryBarType.filterBar}
        queryBarProps={{
          fuzzyQuery: false,
        }}
        dataSet={tableDs}
        columns={columns}
        treeLoadData={handleExpand}
        onRow={nodeCover}
        defaultRowExpanded
      />
    </>
  );
};

export default MaterialTraceabilityForward;
