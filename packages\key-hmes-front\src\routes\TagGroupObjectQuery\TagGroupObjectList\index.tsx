/*
 * @Description: 对象数据收集组管关系查询-列表页
 * @Author: <<EMAIL>>
 * @Date: 2024-02-29 16:06:01
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-02-29 17:47:55
 */
import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores';

const modelPrompt = 'tarzan.hmes.tagGroupObject';

const TagGroupObjectList = (props) => {
  const {
    tableDs,
    history,
  } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'tagGroupCode',
        minWidth: 150,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hmes/acquisition/data-collection/detail/${record!.get('tagGroupId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'tagGroupDescription', minWidth: 150 },
      { name: 'materialCodeRevision', minWidth: 180 },
      { name: 'operationName', minWidth: 150 },
      { name: 'workcellCode', minWidth: 150 },
      { name: 'ncCode', minWidth: 120 },
      { name: 'workOrderNum', minWidth: 150 },
      { name: 'eoNum', minWidth: 180 },
      { name: 'materialCategoryCode', minWidth: 150 },
      { name: 'materialLotCode', minWidth: 180 },
      { name: 'substepName' },
    ];
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('对象收集组关系查询')} />
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="tagGroupObject_searchCode"
          customizedCode="tagGroupObject_customizedCode"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(TagGroupObjectList),
);