/**
 * @Description:  领退料工作台-详情页
 */
import React, { useEffect, useState } from 'react';
import {
  DataSet,
  Table,
  TextField,
  Form,
  DatePicker,
  NumberField,
  Lov,
  Select,
  Button,
  Switch,
  Spin,
} from 'choerodon-ui/pro';
import { <PERSON>confirm, Badge, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import { queryMapIdpValue } from 'services/api';
import { useRequest } from '@components/tarzan-hooks';
import { headerFormDS, lineTableDS } from '../stores/DetailDS';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.receiveReturn';

// 行查询接口
export function AvailableQuantity() {
  return {
    url: `${BASIC.HWMS_BASIC}/v1/${getCurrentOrganizationId()}/mt-inv-onhand-quantity/sum-available-qty/ui`,
    method: 'POST',
  };
}

// 领退料类型一步法两步法查询
export function InstructionDocTypeStrategy() {
  return {
    url: `${BASIC.HWMS_BASIC}/v1/${getCurrentOrganizationId()}/receive-return-bill/exe/strategy/get/ui`,
    method: 'GET',
  };
}

// const endUrl = '-30711';
const endUrl = '';

const Order = props => {
  const {
    headerFormDs,
    lineTableDs,
    match: {
      path,
      params: { id, docType, docTypeTag },
    },
    customizeForm,
    customizeTable,
  } = props;

  const [hasPermissionFlag, setHasPermissionFlag] = useState(false);
  const [changeAll, setChangeAll] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [typeObj, setTypeObj] = useState({});
  const [pageWorkOrderIds, setPageWorkOrderIds] = useState([]);
  const [instructionDocStatus, setInstructionDocStatus] = useState('');

  const availableQuantity = useRequest(AvailableQuantity(), {
    manual: true,
  });

  const instructionDocTypeStrategy = useRequest(InstructionDocTypeStrategy(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      headerFormDs.loadData([
        {
          instructionDocStatus: 'RELEASED',
        },
      ]);
      lineTableDs.loadData([]);
      setInstructionDocStatus('RELEASED');
      handleFetchDefaultSourceSystem();
    } else {
      setCanEdit(false);
      setPageWorkOrderIds([]);
      setInstructionDocStatus();
      headerFormDs.loadData([]);
      lineTableDs.loadData([]);
      queryDeatil();
    }
  }, [id]);

  const queryDeatil = () => {
    setLoading(true);
    headerFormDs.setQueryParameter('instructionDocId', id);
    headerFormDs.setQueryParameter('instructionDocType', docType);
    headerFormDs.query().then(res => {
      setLoading(false);
      if (res?.rows?.instructionDoc) {
        const { instructionDoc } = res?.rows;
        const { siteId } = instructionDoc;
        headerFormDs.current.set('instructionType', instructionDoc.instructionTypeList);
        headerFormDs.current.set('docTypeTag', instructionDoc.docTypeTag);
        setInstructionDocStatus(instructionDoc.instructionDocStatus);
        if (res?.rows?.instructionList) {
          const { instructionList } = res?.rows;
          const permissionFlagList = [];
          lineTableDs.loadData(
            instructionList.map(item => {
              if (item.permissionFlag !== 'Y') {
                permissionFlagList.push(item);
              }
              return {
                ...item,
                instructionDocType: instructionDoc.instructionDocType,
                instructionType: instructionDoc.instructionTypeList,
                docTypeTag: instructionDoc.docTypeTag,
                quantityMax: item.quantity,
                siteId,
                soNumConfig: Math.abs(item.soLineId) > 0 ? 'Y' : 'N',
              };
            }),
          );
          setHasPermissionFlag(permissionFlagList.length === 0);

          instructionDocTypeStrategy.run({
            params: {
              instructionDocType: instructionDoc.instructionDocType,
            },
            onSuccess: subRes => {
              setTypeObj(subRes[0]);
              const { instructionDocExeStrategy, instructionType, fromLocatorRequiredFlag, toLocatorRequiredFlag } = subRes[0] || {};
              headerFormDs.current.init('strategy', instructionDocExeStrategy);
              headerFormDs.current.init('strategyList', subRes.rows || []);
              lineTableDs.forEach(record => {
                record.init('strategy', instructionDocExeStrategy);
                record.init('strategyList', subRes || []);
                record.init('instructionType', instructionType);
                record.init('fromLocatorRequiredFlag', fromLocatorRequiredFlag);
                record.init('toLocatorRequiredFlag', toLocatorRequiredFlag);
              });
            },
            onFailed: () => {
              headerFormDs.current.init('strategy', null);
              headerFormDs.current.init('strategyList', []);
              lineTableDs.forEach(record => {
                record.init('strategy', null);
                record.init('strategyList', []);
              });
            },
          });
        }
      } else {
        notification.error({
          message: res.message,
        });
        headerFormDs.current.init('strategy', null);
        headerFormDs.current.init('strategyList', []);
        lineTableDs.forEach(record => {
          record.init('strategy', null);
          record.init('strategyList', []);
        });
      }
    });
  };

  const handleFetchDefaultSourceSystem = () => {
    queryMapIdpValue({
      sourceSystemList: 'SOURCE_SYSTEM',
    }).then(res => {
      if(res) {
        const defaultSourceSystem = res.sourceSystemList.find(e => e.tag === "Y");
        if(defaultSourceSystem) {
          headerFormDs.current.set('sourceSystem', defaultSourceSystem.value);
        };
      }
    });
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push(`/hwms/receive/receive-return/list`);
    } else {
      setCanEdit(false);
      queryDeatil();
    }
  };

  const lovChange = type => {
    if (type === 'site') {
      headerFormDs.current.init('prodLine', null);
    }
    headerFormDs.current.init('workOrder', null);
    lineTableDs.loadData([]);
    setPageWorkOrderIds([]);
  };

  const lineTableChange = async type => {
    const headerData = headerFormDs.toData()[0];
    const { workOrderIds, siteId, instructionDocType, prodLineId } = headerData;

    if (type === 'workOrder') {
      if (workOrderIds && workOrderIds.length > 0 && siteId && instructionDocType) {
        getLineDefault({
          siteId,
          instructionDocType,
          workOrderIds,
          productionLineId: prodLineId,
          newObj:typeObj
        });
      }
      if (workOrderIds && workOrderIds.length === 0) {
        lineTableDs.loadData([]);
      }
    }

    if (type === 'instructionDocType') {
      if (instructionDocType) {
        await instructionDocTypeStrategy.run({
          params: {
            instructionDocType,
          },
          onSuccess: res => {
            setTypeObj(res[0])
            const { instructionDocExeStrategy, docTypeTag } = res[0] || {};
            const instructionTypeArr = [];
            res.forEach(item => {
              instructionTypeArr.push(item.instructionType)
            })
            headerFormDs.current.init('instructionType', instructionTypeArr);
            headerFormDs.current.init('docTypeTag', docTypeTag);
            headerFormDs.current.init('strategy', instructionDocExeStrategy);
            headerFormDs.current.init('strategyList', res || []);

            lineTableDs.loadData([]);
            if (workOrderIds && workOrderIds.length > 0 && siteId && instructionDocType) {
              getLineDefault({
                siteId,
                instructionDocType,
                workOrderIds,
                productionLineId: prodLineId,
                newObj:res[0]
              });
            }
          },
          onFailed: () => {
            headerFormDs.current.init('strategy', null);
            headerFormDs.current.init('strategyList', []);
          },
        });
      }
    }
  };

  const handleSave = async () => {
    if (headerFormDs) {
      headerFormDs.forEach(item => {
        item.set({ nowDate: new Date().getTime() });
      });
    }
    if (lineTableDs) {
      lineTableDs.forEach(item => {
        item.set({ nowDate: new Date().getTime() });
      });
    }

    const validateHeader = await headerFormDs.validate();
    const validateLine = await lineTableDs.validate();
    const poIds = [];
    if (validateHeader && validateLine) {
      const headerData = headerFormDs.toData()[0];
      const lines = lineTableDs.toData();
      lines.forEach(item => {
        if (poIds.indexOf(item?.poHeaderId) === -1) {
          poIds.push(item.poHeaderId);
        }
        if (item.soNumConfig === 'N') {
          item.soLineId = '';
        }
      });
      const createUrl = `${BASIC.HWMS_BASIC}/v1/${tenantId}/receive-return-bill/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.LINE`;
      const updateUrl = `${BASIC.HWMS_BASIC}/v1/${tenantId}/receive-return-bill/detail-save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.LINE`;
      setLoading(true);
      return request(id === 'create' ? createUrl : updateUrl, {
        method: 'POST',
        body: {
          [id === 'create' ? 'returnBillHeader' : 'instructionDoc']: headerData,
          [id === 'create' ? 'instructionMessageList' : 'instructionList']: lines,
        },
      })
        .then(res => {
          setLoading(false);
          if (res && res.success) {
            setCanEdit(false);
            setPageWorkOrderIds([]);
            setInstructionDocStatus('');
            if (id === 'create') {
              headerFormDs.setQueryParameter('instructionDocId', res.rows);
              headerFormDs.setQueryParameter('instructionDocType', headerData.instructionDocType);
              headerFormDs.query().then(data => {
                setLoading(false);
                props.history.push(
                  `/hwms/receive/receive-return/detail/${res.rows}/${headerData.instructionDocType}/${data.rows.instructionDoc.docTypeTag}`,
                );
              })
            } else {
              queryDeatil();
            }
          } else {
            notification.error({
              message: res && res.message,
            });
          }
        })
        .catch(() => {
          setLoading(false);
        });
    }
    return false;
  };

  const getLineDefault = async body => {
    setLoading(true);
    const { workOrderIds, newObj } = body;
    const deleteWorkOrderIds = [];
    const addWorkOrderIds = [];

    pageWorkOrderIds.forEach(item => {
      if (workOrderIds.indexOf(item) === -1) {
        deleteWorkOrderIds.push(item);
      }
    });
    workOrderIds.forEach(item => {
      if (pageWorkOrderIds.indexOf(item) === -1) {
        addWorkOrderIds.push(item);
      }
    });
    setPageWorkOrderIds(workOrderIds);
    const deleteRecords = [];
    const addedRecordsFindId = [];
    lineTableDs.forEach(item => {
      const workOrderId = item.get('workOrderId');
      if (deleteWorkOrderIds.indexOf(workOrderId) > -1) {
        deleteRecords.push(item);
      } else {
        addedRecordsFindId.push(item.get('workOrderId'));
      }
    });
    lineTableDs.delete(deleteRecords, false);
    lineTableDs.forEach((item, index) => {
      item.init('docTypeTag', headerFormDs.current.get('docTypeTag'))
      item.init('instructionType', headerFormDs.toData()[0].instructionType)
      item.init('strategy', headerFormDs.current.get('strategy'));
      item.init('strategyList', headerFormDs.current.get('strategyList'));
      item.init('lineNumber', (index + 1) * 10);
      item.init('fromLocatorRequiredFlag', newObj.fromLocatorRequiredFlag);
      item.init('toLocatorRequiredFlag', newObj.toLocatorRequiredFlag);
    });
    return request(
      `${BASIC.HWMS_BASIC}${endUrl}/v1/${tenantId}/receive-return-bill/line/create/ui`,
      {
        method: 'POST',
        body,
      },
    )
      .then(res => {
        if (res?.success) {
          if (res?.rows?.length > 0) {
            res.rows.forEach(item => {
              const findId = `${item.workOrderId}${item.materialId}${item.lineNumber}`;
              if (addedRecordsFindId.indexOf(item.workOrderId) === -1) {
                lineTableDs.create({
                  ...item,
                  fromLocatorRequiredFlag: newObj.fromLocatorRequiredFlag,
                  toLocatorRequiredFlag: newObj.toLocatorRequiredFlag,
                  docTypeTag: headerFormDs.current.get('docTypeTag'),
                  instructionType: headerFormDs.toData()[0].instructionType,
                  siteId: headerFormDs.current.get('siteId'),
                  instructionDocType: headerFormDs.current.get('instructionDocType'),
                  quantityMax: item.quantity,
                  sourceLocatorId: item.sourceLocatorId || null,
                  sourceLocatorCode: item.sourceLocatorCode || null,
                  sourceSubLocatorId: item.sourceSubLocatorId || null,
                  sourceSubLocatorCode: item.sourceSubLocatorCode || null,
                  targetLocatorId: item.targetLocatorId || null,
                  targetLocatorCode: item.targetLocatorCode || null,
                  toLocatorId: item.subLocatorId || null,
                  toLocatorCode: item.subLocatorCode || null,
                  findId,
                  sourceSumOnhandQty: item.sourceSumOnhandQty || 0,
                  targetSumOnhandQty: item.targetSumOnhandQty || 0,
                  toleranceFlag: item.toleranceFlag || 'N',
                  soNumConfig: Math.abs(item.soLineId) > 0 ? 'Y' : 'N',
                });
              }
            });
            lineTableDs.forEach((item, index) => {
              item.init('strategy', headerFormDs.current.get('strategy'));
              item.init('strategyList', headerFormDs.current.get('strategyList'));
              item.init('fromLocatorRequiredFlag', newObj.fromLocatorRequiredFlag);
              item.init('toLocatorRequiredFlag', newObj.toLocatorRequiredFlag);
              item.init('lineNumber', (index + 1) * 10);
            });
          }
        } else {
          notification.error({
            message: res && res.message,
          });
        }
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 新建表格后可删除
  const handleDelete = record => {
    lineTableDs.delete(record, false);
    setTimeout(() => {
      updateWorkOrder();
    }, 0);
  };

  const updateWorkOrder = () => {
    const workOrderNumbers = [];
    const workOrder = [];
    lineTableDs.forEach((item, index) => {
      const workOrderNum = item.get('workOrderNum');
      const workOrderId = item.get('workOrderId');
      if (workOrderNum && workOrderNumbers.indexOf(workOrderNum) === -1) {
        workOrderNumbers.push(workOrderNum);
        workOrder.push({
          workOrderNumber: workOrderNum,
          workOrderId,
        });
      }
      item.init('lineNumber', (index + 1) * 10);
      item.init('strategy', headerFormDs.current.get('strategy'));
      item.init('strategyList', headerFormDs.current.get('strategyList'));
    });
    headerFormDs.current.set('workOrder', workOrder);
  };

  const columnFilter = () => {
    if (id === 'create') {
      return createLineTableColumns;
    }
    if (
      id !== 'create' && docTypeTag === "PICK"
    ) {
      return requisitionLineTableColumns;
    }
    if (id !== 'create' && docTypeTag === "RETURN") {
      return returnLineTableColumns;
    }
    return [];
  };

  const wareHouseChange = (record, type, value) => {
    const _fromWarehouse = record.get('fromWarehouse');
    const _toWarehouse = record.get('toWarehouse');
    if (changeAll) {
      lineTableDs.forEach(item => {
        item.set(type, value);
        if (type === 'fromWarehouse' || type === 'fromLocator') {
          item.set('sourceSumOnhandQty', null);
          if (type === 'fromWarehouse') {
            item.set('fromLocator', null);
          } else {
            item.set('fromWarehouse', _fromWarehouse);
          }
        } else {
          item.set('targetSumOnhandQty', null);
          if (type === 'toWarehouse') {
            item.set('toLocator', null);
          } else {
            item.set('toWarehouse', _toWarehouse);
          }
        }
        const _materialId = item.get('materialId');
        const _soId = item.get('soId');
        const _siteId = item.get('siteId');
        const _revisionCode = item.get('revisionCode');
        let _locatorId;
        if (type === 'fromWarehouse' || type === 'fromLocator') {
          _locatorId = item.get('sourceSubLocatorId') || item.get('sourceLocatorId');
        }
        if (type === 'toWarehouse' || type === 'toLocator') {
          _locatorId = item.get('toLocatorId') || item.get('targetLocatorId');
        }

        if (_locatorId) {
          const _params = {
            materialId: _materialId,
            siteId: _siteId,
            revisionCode: _revisionCode || '',
            locatorId: _locatorId,
          };
          if (_soId) {
            _params.ownerType = 'OI';
            _params.ownerId = _soId;
          }
          request(
            `${BASIC.HWMS_BASIC
            }/v1/${getCurrentOrganizationId()}/mt-inv-onhand-quantity/sum-available-qty/ui`,
            {
              method: 'POST',
              body: _params,
            },
          )
            .then(res => {
              if (res?.success) {
                item.set(
                  type === 'fromWarehouse' || type === 'fromLocator'
                    ? 'sourceSumOnhandQty'
                    : 'targetSumOnhandQty',
                  res.rows,
                );
              }
            })
            .catch(() => {
              item.set(
                type === 'fromWarehouse' || type === 'fromLocator'
                  ? 'sourceSumOnhandQty'
                  : 'targetSumOnhandQty',
                null,
              );
            });
        }
      });
      setChangeAll(false);
    } else {
      record.set(type, value);
      if (type === 'fromWarehouse' || type === 'fromLocator') {
        record.set('sourceSumOnhandQty', null);
        if (type === 'fromWarehouse') {
          record.set('fromLocator', null);
        }
      } else {
        record.set('targetSumOnhandQty', null);
        if (type === 'toWarehouse') {
          record.set('toLocator', null);
        }
      }
      const _materialId = record.get('materialId');
      const _soId = record.get('soId');
      const _siteId = record.get('siteId');
      const _revisionCode = record.get('revisionCode');
      let _locatorId;
      if (type === 'fromWarehouse' || type === 'fromLocator') {
        _locatorId = record.get('sourceSubLocatorId') || record.get('sourceLocatorId');
      }
      if (type === 'toWarehouse' || type === 'toLocator') {
        _locatorId = record.get('toLocatorId') || record.get('targetLocatorId');
      }
      if (_locatorId) {
        const _params = {
          materialId: _materialId,
          siteId: _siteId,
          revisionCode: _revisionCode || '',
          locatorId: _locatorId,
        };
        if (_soId) {
          _params.ownerType = 'OI';
          _params.ownerId = _soId;
        }
        availableQuantity.run({
          params: _params,
          onSuccess: res => {
            record.set(
              type === 'fromWarehouse' || type === 'fromLocator'
                ? 'sourceSumOnhandQty'
                : 'targetSumOnhandQty',
              res,
            );
          },
          onFailed: () => {
            record.set(
              type === 'fromWarehouse' || type === 'fromLocator'
                ? 'sourceSumOnhandQty'
                : 'targetSumOnhandQty',
              null,
            );
          },
        });
      }
    }
  };

  const fromWarehouseFooter = (okBtn, cancelBtn) => {
    const allBtn = (
      <Button
        onClick={() => {
          setChangeAll(true);
          setTimeout(() => {
            okBtn.props.onClick();
          });
        }}
      >
        {intl.get(`${modelPrompt}.all.rows`).d('应用至所有行')}
      </Button>
    );
    return (
      <div>
        {allBtn}
        {cancelBtn}
        {okBtn}
      </div>
    );
  };

  // 新建行信息表配置
  const createLineTableColumns = [
    {
      title: <Button icon="add" disabled funcType="flat" shape="circle" size="small" />,
      align: 'center',
      name: 'name',
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button
            disabled={record.get('permissionFlag') !== 'Y' && id !== 'create'}
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'lineNumber',
      width: 100,
    },
    {
      name: 'materialCode',
      width: 140,
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'componentQty',
      width: 120,
    },
    id === 'create'
      ? {
        name: 'receivedQty',
        width: 120,
      }
      : undefined,
    id === 'create'
      ? {
        name: 'signedQty',
        width: 120,
      }
      : undefined,
    {
      name: 'returnedQty',
      width: 120,
    },
    {
      name: 'haveDoneQty',
      width: 120,
    },
    {
      name: 'quantity',
      width: 120,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <NumberField nonStrictStep precision={6} step={1} />
        ),
    },
    {
      name: 'primaryUomCode',
      width: 100,
    },
    {
      name: 'soNum',
      title: intl.get(`${modelPrompt}.soNumSoLineNumber`).d('销单/行号'),
      width: 140,
      renderer: ({ record }) => {
        if (record.data.soNumber && record.data.soNumConfig === 'Y') {
          return `${record.data.soNumber || ''}/${record.data.soLineNum || ''}`;
        }
        return '';
      },
    },
    {
      name: 'soNumConfig',
      width: 100,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' ||
          Math.abs(record.get('soLineId')) > 0 ||
          hasPermissionFlag ||
          id === 'create') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'workOrderNum',
      width: 100,
    },
    {
      name: 'productionLineCode',
      width: 100,
    },
    {
      name: 'fromIdentifyType',
      width: 120,
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   } if (value === 'MATERIAL_LOT' || value === '') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      // },
    },
    {
      name: 'fromWarehouse',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'fromWarehouse', value);
            }}
          />
        ),
    },
    {
      name: 'fromLocator',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'fromLocator', value);
            }}
          />
        ),
    },
    {
      name: 'sourceSumOnhandQty',
      width: 120,
    },
    {
      name: 'toIdentifyType',
      width: 120,
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   } if (value === 'MATERIAL_LOT' || value === '') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      // },
    },
    {
      name: 'toWarehouse',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'toWarehouse', value);
            }}
          />
        ),
    },
    {
      name: 'toLocator',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'toLocator', value);
            }}
          />
        ),
    },
    {
      name: 'targetSumOnhandQty',
      width: 120,
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: 'center',
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') &&
        id === 'create' && (
          <Switch
            onChange={() => {
              record.set('toleranceType', null);
              record.set('toleranceMinValue', null);
              record.set('toleranceMaxValue', null);
            }}
          />
        ),
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') &&
        id === 'create' && (
          <Select
            onChange={() => {
              record.set('toleranceMinValue', null);
              record.set('toleranceMaxValue', null);
            }}
          />
        ),
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') &&
        id === 'create' && <NumberField nonStrictStep precision={6} step={1} />,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') &&
        id === 'create' && <NumberField nonStrictStep precision={6} step={1} />,
    },
  ];

  // 领料行信息表配置
  const requisitionLineTableColumns = [
    {
      name: 'lineNumber',
      width: 100,
    },
    {
      name: 'materialCode',
      width: 140,
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'quantity',
      width: 120,
    },
    {
      name: 'primaryUomCode',
      width: 100,
    },
    {
      name: 'instructionStatusDesc',
      width: 100,
    },
    {
      name: 'soNum',
      title: intl.get(`${modelPrompt}.soNumSoLineNumber`).d('销单/行号'),
      width: 140,
      renderer: ({ record }) => {
        if (record.data.soNumber && record.data.soNumConfig === 'Y') {
          return `${record.data.soNumber || ''}/${record.data.soLineNum || ''}`;
        }
        return '';
      },
    },
    {
      name: 'soNumConfig',
      width: 100,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' ||
          Math.abs(record.get('soLineId')) > 0 ||
          hasPermissionFlag ||
          id === 'create') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'workOrderNum',
      width: 100,
    },
    {
      name: 'productionLineCode',
      width: 100,
    },
    {
      name: 'fromIdentifyType',
      width: 120,
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   } if (value === 'MATERIAL_LOT' || value === '') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      // },
      // lock: 'left',
    },
    id === 'create'
      ? {
        name: 'receivedQty',
        width: 120,
      }
      : undefined,
    id === 'create'
      ? {
        name: 'signedQty',
        width: 120,
      }
      : undefined,
    {
      name: 'fromWarehouse',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'fromWarehouse', value);
            }}
          />
        ),
    },
    {
      name: 'fromLocator',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'fromLocator', value);
            }}
          />
        ),
    },
    {
      name: 'sourceSumOnhandQty',
      width: 120,
    },
    {
      name: 'toIdentifyType',
      width: 120,
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   } if (value === 'MATERIAL_LOT' || value === '') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      // },
      // lock: 'left',
    },
    {
      name: 'toWarehouse',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'toWarehouse', value);
            }}
          />
        ),
    },
    {
      name: 'toLocator',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'toLocator', value);
            }}
          />
        ),
    },
    {
      name: 'targetSumOnhandQty',
      width: 120,
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 140,
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
    },
  ];

  // 退料行信息表配置
  const returnLineTableColumns = [
    {
      name: 'lineNumber',
      width: 100,
    },
    {
      name: 'materialCode',
      width: 140,
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'quantity',
      width: 120,
    },
    {
      name: 'primaryUomCode',
      width: 100,
    },
    {
      name: 'instructionStatusDesc',
      width: 100,
    },
    {
      name: 'soNum',
      title: intl.get(`${modelPrompt}.soNumSoLineNumber`).d('销单/行号'),
      width: 140,
      renderer: ({ record }) => {
        if (record.data.soNumber && record.data.soNumConfig === 'Y') {
          return `${record.data.soNumber || ''}/${record.data.soLineNum || ''}`;
        }
        return '';
      },
    },
    {
      name: 'soNumConfig',
      width: 100,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' ||
          Math.abs(record.get('soLineId')) > 0 ||
          hasPermissionFlag ||
          id === 'create') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'workOrderNum',
      width: 100,
    },
    {
      name: 'productionLineCode',
      width: 100,
    },
    {
      name: 'fromIdentifyType',
      width: 120,
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   } if (value === 'MATERIAL_LOT' || value === '') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      // },
      // lock: 'left',
    },
    {
      name: 'returnedQty',
      width: 120,
    },
    {
      name: 'fromWarehouse',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'fromWarehouse', value);
            }}
          />
        ),
    },
    {
      name: 'fromLocator',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'fromLocator', value);
            }}
          />
        ),
    },
    {
      name: 'sourceSumOnhandQty',
      width: 120,
    },
    {
      name: 'toWarehouse',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'toWarehouse', value);
            }}
          />
        ),
    },
    {
      name: 'toLocator',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'toLocator', value);
            }}
          />
        ),
    },
    {
      name: 'targetSumOnhandQty',
      width: 120,
    },
    {
      name: 'toIdentifyType',
      width: 120,
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   } if (value === 'MATERIAL_LOT' || value === '') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      // },
      // lock: 'left',
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 140,
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
    },
  ];

  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        <Header
          title={intl.get(`${modelPrompt}.title.receiveReturnMaintain`).d('领退料单维护')}
          backPath="/hwms/receive/receive-return/list"
        >
          {canEdit && (
            <>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
                disabled={instructionDocStatus !== 'RELEASED'}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </PermissionButton>
              <PermissionButton type="c7n-pro" icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </PermissionButton>
            </>
          )}
          {!canEdit && (
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              disabled={instructionDocStatus !== 'RELEASED' || !hasPermissionFlag}
              onClick={() => {
                setCanEdit(prev => !prev);
              }}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
            <Panel
              header={intl.get(`${modelPrompt}.header.information`).d('头信息')}
              key="basicInfo"
              dataSet={headerFormDs}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.HEAD`,
                },
                <Form
                  disabled={!canEdit}
                  dataSet={headerFormDs}
                  columns={3}
                  labelLayout="horizontal"
                  labelWidth={110}
                >
                  <TextField name="instructionDocNum" />
                  <Select
                    name="instructionDocType"
                    onChange={() => {
                      lineTableChange('instructionDocType');
                    }}
                    disabled={id !== 'create' || !canEdit}
                  />
                  <Select name="instructionDocStatus" />
                  <Lov
                    name="site"
                    onChange={() => {
                      lovChange('site');
                    }}
                    disabled={id !== 'create' || !canEdit}
                  />
                  <Lov
                    name="prodLine"
                    onChange={() => {
                      lovChange('prodLine');
                    }}
                    disabled={id !== 'create' || !canEdit}
                  />
                  <DatePicker name="demandTime" mode="dateTime" disabled={!canEdit} />
                  <Lov
                    colSpan={2}
                    name="workOrder"
                    onChange={() => {
                      lineTableChange('workOrder');
                    }}
                    disabled={id !== 'create' || !canEdit}
                  />
                  <Select name='sourceSystem' disabled />
                  <TextField name="remark" disabled={!canEdit} />
                </Form>,
              )}
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
              key="location"
              dataSet={lineTableDs}
            >
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.LINE`,
                },
                <Table dataSet={lineTableDs} highLightRow={false} columns={columnFilter()} />,
              )}
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.receive.receiveReturn', 'tarzan.common'] }),
  withProps(
    () => {
      const headerFormDs = new DataSet({ ...headerFormDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerFormDs,
        lineTableDs,
      };
    },
    {},
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.HEAD`, `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.LINE`] }),
)(Order);
