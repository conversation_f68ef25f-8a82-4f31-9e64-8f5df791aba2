/**
 * @Description: 检验项目维护-详情页
 * @Author: <EMAIL>
 * @Date: 2023/1/9 15:26
 */
import React, { useState, useRef } from 'react';
import { Button } from 'choerodon-ui/pro';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { BASIC } from '@utils/config';
import { AttributeDrawer } from '@components/tarzan-ui';
import notification from 'utils/notification';
import DetailComponent from '@/routes/TestingStrategy/InspectItemMaintain/components/DetailComponent';

const modelPrompt = 'tarzan.hwms.inspectItemMaintain';

const InspectItemDetail = props => {
  const {
    history,
    custConfig,
    customizeForm,
    match: { path, params },
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState<boolean>(kid === 'create');
  const childRef = useRef<any>();

  const handleCancel = () => {
    if (kid === 'create') {
      history.push('/hwms/inspect-item-maintain/list');
    } else {
      childRef.current.handleQueryDetail(kid);
      setCanEdit(false);
    }
  };

  const handleEdit = () => {
    setCanEdit(true);
    childRef.current.handleUpdateDisabled();
  };

  const handleSave = async createFLag => {
    const { success, rows } = await childRef.current?.submit(createFLag);
    if (success) {
      notification.success({});
      if (createFLag && kid !== 'create') {
        history.push('/hwms/inspect-item-maintain/dist/create');
      } else if (!createFLag) {
        setCanEdit(false);
        history.push(`/hwms/inspect-item-maintain/dist/${rows}`);
        childRef.current.handleQueryDetail(rows);
      }
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.list`).d('检验项目维护')}
        backPath="/hwms/inspect-item-maintain/list"
      >
        {canEdit ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave(false)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="save" onClick={() => handleSave(true)}>
              {intl.get(`${modelPrompt}.button.saveAndNew`).d('保存并新建下一条')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            icon="edit-o"
            color={ButtonColor.primary}
            onClick={handleEdit}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <AttributeDrawer
          serverCode={BASIC.TARZAN_SAMPLING}
          className="org.tarzan.qms.domain.entity.MtInspectBusTypeRule"
          kid={kid}
          canEdit={canEdit}
          disabled={kid === 'create'}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_DETAIL.ATTR`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <DetailComponent
          ref={childRef}
          canEdit={canEdit}
          kid={kid}
          column={3}
          customizeForm={customizeForm}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_DETAIL.ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_DETAIL.BASIC`,
    ],
  })(InspectItemDetail as any),
);
