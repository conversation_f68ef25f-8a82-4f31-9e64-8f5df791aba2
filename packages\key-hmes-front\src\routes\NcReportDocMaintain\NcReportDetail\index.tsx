/**
 * @Description: 不良记录单管理平台 -详情界面
 * @Author: <EMAIL>
 * @Date: 2023/3/9 14:37
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  Modal,
  NumberField,
  Select,
  SelectBox,
  Table,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import {
  drawerPropsC7n,
  TarzanSpin,
  C7nFormItemSort,
  AttributeDrawer,
} from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { observer } from 'mobx-react';
import { FieldType } from 'choerodon-ui/dataset/data-set/enum';
import moment from 'moment/moment';
import { getCurrentUser } from 'utils/utils';
import { BASIC } from 'hcm-components-front/lib/utils/config';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'hzero-front/src/utils/utils';
import scanImg from '@/assets/icons/scan-o.svg';
import {
  detailDS,
  disposalDS,
  lineDtlDS,
  ncRecordLineDS,
  scanFormDS,
} from '../stores/NcReportDocDtlDS';
import DetailDrawerComponent from './DetailDrawerComponent';
import {
  BatchReview,
  GetInspectBusInfo,
  QueryEoOrMaterialLot,
  SaveAndSubmitNcReportDoc,
  SaveNcReportDoc,
  DirectDisposal,
  QueryInspectDocLine,
} from '../services';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.ncReportDocMaintain';
const userInfo = getCurrentUser();

interface DynamicDisplayFlagProps {
  reworkRouterFlag: boolean; // dispositionFunction=REWORK_ROUTER
  reworkSourceFlag: boolean; // dispositionFunction=REWORK_SOURCE/REWORK_ANY
  degradeFlag: boolean; // dispositionFunction=DEGRADE
  eoDischargeFlag: boolean; // dispositionFunction=EO_DISCHARGE
  crossWoFlag: boolean; // dispositionFunction=CROSS_WO_INTERCEPT
  concessionFlag: boolean; // dispositionFunction=CONCESSION_INTERCEPTION
}

const NcReportDetail = props => {
  const {
    history,
    match: { path, params },
    customizeForm,
    customizeTable,
    custConfig,
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState(kid === 'create');
  const [activeKey, setActiveKey] = useState<any>(['baseInfo', 'ncRecordLine']);
  const [disposalType, setDisposalType] = useState<string>('NO'); // 处置类型
  const [ncReportStatus, setNcReportStatus] = useState<string>('NEW'); // 不良记录单状态
  const [reviewType, setReviewType] = useState<string>(''); // 评审规则类型
  const [createMethod, setCreateMethod] = useState<string>(''); // 创建方式
  const [ncReviewStatus, setNcReviewStatus] = useState<string>(''); // 审核状态
  const [disposalFunList, setDisposalFunList] = useState<any[]>([]); // 处置方法列表
  const [dispositionFunction, setDispositionFunction] = useState(''); // 处置方法（整体处置）
  const [dynamicDisplayFlag, setDynamicDisplayFlag] = useState<DynamicDisplayFlagProps>({
    reworkRouterFlag: false,
    reworkSourceFlag: false,
    degradeFlag: false,
    eoDischargeFlag: false,
    crossWoFlag: false,
    concessionFlag: false,
  });
  const lineDetailDs = useMemo(() => new DataSet(lineDtlDS()), []);
  const ncRecordLineDs = useMemo(
    () =>
      new DataSet({
        ...ncRecordLineDS(),
        children: { ncReportLineDtlList: lineDetailDs },
      }),
    [],
  );
  const disposalDs = useMemo(() => new DataSet(disposalDS()), []);
  const scanFormDs = useMemo(() => new DataSet(scanFormDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          ncReportLineList: ncRecordLineDs,
          disposalList: disposalDs,
          scanFormDs,
        },
      }),
    [],
  );
  const { run: saveNcReportDoc, loading: saveLoading } = useRequest(SaveNcReportDoc(), {
    manual: true,
  });
  const { run: saveAndSubmitNcReportDoc, loading: saveAndSubmitLoading } = useRequest(
    SaveAndSubmitNcReportDoc(),
    {
      manual: true,
    },
  );
  const { run: getInspectBusInfo, loading: queryLoading } = useRequest(GetInspectBusInfo(), {
    manual: true,
  });
  const { run: batchReview, loading: reviewLoading } = useRequest(BatchReview(), {
    manual: true,
  });
  const { run: directDisposal, loading: directDisposalLoading } = useRequest(DirectDisposal(), {
    manual: true,
  });
  const { run: queryInspectDocLine, loading: queryInspectDocLoading } = useRequest(
    QueryInspectDocLine(),
    {
      manual: true,
    },
  );
  const { run: queryEoOrMaterialLot, loading: queryEOLoading } = useRequest(
    QueryEoOrMaterialLot(),
    {
      manual: true,
      needPromise: true,
    },
  );

  useEffect(() => {
    if (kid === 'create') {
      queryBusinessTypeRuleInfo(undefined);
      return;
    }
    handleQueryDetail(kid);
  }, [kid]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('ncReportId', id);
    detailDs.query().then(res => {
      const {
        disposalType,
        ncReportStatus,
        ncReviewStatus,
        reviewType,
        inspectBusinessType,
        createMethod,
        disposalList,
      } = res;
      setDisposalType(disposalType);
      // handleResetDisposal();
      if (disposalType) {
        setActiveKey(['baseInfo', 'ncRecordLine', disposalType]);
      }
      if (disposalType === 'ALL' && disposalList?.length) {
        setDispositionFunction(disposalList[0].dispositionFunction);
      }
      setCreateMethod(createMethod);
      setNcReportStatus(ncReportStatus);
      setNcReviewStatus(ncReviewStatus);
      setReviewType(reviewType);

      // 同步处置行上的optionList
      (ncRecordLineDs || []).forEach(_record => handleSyncOptionList(_record, false));

      // 查询业务类型规则对应的处置方法配置
      const optionDs = detailDs.getField('inspectBusinessType')?.getOptions(detailDs.current);
      const currentRecord = optionDs?.find(
        _record => _record?.get('inspectBusinessType') === inspectBusinessType,
      );
      let params;
      if (!inspectBusinessType) {
        params = undefined;
      } else {
        params = { dispositionGroupId: currentRecord?.get('dispositionGroupId') };
      }
      queryBusinessTypeRuleInfo(params);
    });
  };

  // 根据业务类型规则查询动态列信息
  const queryBusinessTypeRuleInfo = params => {
    getInspectBusInfo({
      params,
      onSuccess: res => {
        setDisposalFunList(res || []);
        if (res && res.length) {
          res.forEach(item => {
            disposalDs.addField(item?.dispositionFunction, {
              type: FieldType.number,
              label: item.description,
              defaultValue: 0,
              min: 0,
              validator: (value, _, record: any) => {
                // 当前行除“通过”外其余总数
                let sumQty = 0;
                res.forEach(item => {
                  if (item?.dispositionFunction !== 'PASS') {
                    sumQty += Number(record.get(item?.dispositionFunction) || 0);
                  }
                });
                let parentNumber;
                if (record.get('ncRecordType') === 'EO_MATERIAL_NC') {
                  // optionList
                  const optionItem = (record.get('optionList') || []).find(
                    item =>
                      item.componentMaterialLotCode === record?.get('componentMaterialLotCode'),
                  );
                  parentNumber = Number(optionItem?.sumAssembleQty);
                } else {
                  // 当前处置行对应的不良记录行
                  const parentRecord = ncRecordLineDs.find(
                    _rec =>
                      _rec.get('ncRecordType') === record?.get('ncRecordType') &&
                      _rec.get('ncObjectType') === record?.get('ncObjectType') &&
                      _rec.get('ncObjectCode') === record?.get('ncObjectCode'),
                  );
                  parentNumber = Number(parentRecord?.get('qty'));
                }
                // 处置方法为“通过”时直接跳过校验
                if (item?.dispositionFunction === 'PASS') {
                  return true;
                }
                // 更新“通过”数量
                record.set('PASS', parentNumber - sumQty);
                // 所填总数大于不良行上的数量时报错
                if (sumQty > parentNumber) {
                  return intl
                    .get(`${modelPrompt}.validator.dispositionFunction`, {
                      functionDesc: item?.description,
                      count: parentNumber - sumQty + value,
                    })
                    .d(
                      `${item?.description}项所填数量必须小于等于${parentNumber - sumQty + value}`,
                    );
                }
                return true;
              },
            });
          });
          const _displayFlag: DynamicDisplayFlagProps = {
            reworkRouterFlag: false,
            reworkSourceFlag: false,
            degradeFlag: false,
            eoDischargeFlag: false,
            crossWoFlag: false,
            concessionFlag: false,
          };
          res.forEach(item => {
            switch (item?.dispositionFunction) {
              case 'REWORK_ROUTER':
                _displayFlag.reworkRouterFlag = true;
                break;
              case 'REWORK_SOURCE':
                _displayFlag.reworkSourceFlag = true;
                break;
              case 'DEGRADE':
                _displayFlag.degradeFlag = true;
                break;
              case 'EO_DISCHARGE':
                _displayFlag.eoDischargeFlag = true;
                break;
              case 'CROSS_WO_INTERCEPT':
                _displayFlag.crossWoFlag = true;
                break;
              case 'CONCESSION_INTERCEPTION':
                _displayFlag.concessionFlag = true;
                break;
              default:
                break;
            }
          });
          setDynamicDisplayFlag(_displayFlag);
        }
      },
    });
  };

  // 发起工作流
  const handleReview = () => {
    batchReview({
      params: {
        ncReportIds: [kid],
        ncReportStatus: 'HANDLE',
      },
      onSuccess: () => {
        notification.success({});
        setCanEdit(false);
        handleQueryDetail(kid);
      },
    });
  };

  // 直接处置
  const handleDirectDisposal = () => {
    if (
      !ncRecordLineDs.length ||
      (disposalType === 'ALL' && !disposalDs.current?.get('disposalFunctionId')) ||
      disposalType === 'NO'
    ) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.directDisposalValidate`)
          .d(`直接处置时处置意见不能为空，请先补充处置意见才可提交成功！`),
      });
      return;
    }
    directDisposal({
      params: { ncReportId: detailDs.current?.get('ncReportId') },
      onSuccess: () => {
        notification.success({});
        handleQueryDetail(kid);
      },
    });
  };

  const handleCancel = () => {
    if (kid === 'create') {
      history.push('/hwms/ncReport-doc-maintain/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(kid);
    }
  };

  const handleSaveAndSubmit = async () => {
    const _reviewType = detailDs.current?.get('reviewType');
    if (
      _reviewType === 'NO_REVIEW' &&
      (!ncRecordLineDs.length ||
        (disposalType === 'ALL' && !disposalDs.current?.get('disposalFunctionId')) ||
        disposalType === 'NO')
    ) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.disposalFunRequired`)
          .d(`保存并提交时评审规则类型为【无需审核】场景下需存在行且每个行需给出处置意见！`),
      });
      return;
    }
    await handleSave(false, true);
  };

  const handleSave = async (createFlag = false, submitFlag = false) => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    // 处理保存数据
    const data = detailDs.current?.toData();
    const { ncReportLineList, disposalType, disposalList } = data;
    // 处理班次日期保存格式
    ncReportLineList.forEach(i => (i.shiftDate = i.shiftDate?.split(' ')[0] || undefined));
    // 处理处置数据保存格式
    const disposalData = disposalList;
    if (disposalType === 'PART') {
      disposalData.forEach(item => {
        const _functionList: any = [];
        disposalFunList.forEach(listItem => {
          if (item[listItem?.dispositionFunction]) {
            _functionList.push({
              disposalFunctionId: listItem?.dispositionFunctionId,
              dispositionFunction: listItem?.dispositionFunction,
              qty: item[listItem?.dispositionFunction],
            });
          }
        });
        item.disposalFunctionList = _functionList;
      });
    }
    const params = {
      ...data,
      disposalList: undefined,
      disposalType: disposalType === 'NO' ? undefined : disposalType,
      ncReportLineList,
      allNcReportLineDisposal: disposalType === 'ALL' ? disposalData[0] : undefined,
      partNcReportLineDisposalList: disposalType === 'PART' ? disposalData : undefined,
    };

    if (submitFlag) {
      // 保存并提交
      saveAndSubmitNcReportDoc({
        params,
        onSuccess: res => {
          notification.success({});
          setCanEdit(false);
          history.push(`/hwms/ncReport-doc-maintain/dist/${res}`);
          handleQueryDetail(res);
        },
      });
    } else {
      // 保存
      saveNcReportDoc({
        params,
        onSuccess: res => {
          notification.success({});
          if (createFlag) {
            if (kid === 'create') {
              detailDs.reset();
              lineDetailDs.loadData([]);
              disposalDs.loadData([]);
            } else {
              history.push(`/hwms/ncReport-doc-maintain/dist/create`);
              detailDs.loadData([
                {
                  disposalType: 'NO',
                  ncReportStatus: 'NEW',
                  createMethod: 'MANUAL',
                  ncReportType: 'NC_REPORT',
                },
              ]);
            }
            queryBusinessTypeRuleInfo(undefined);
            setDisposalType('NO');
            setNcReportStatus('NEW');
            setNcReviewStatus('');
            setCreateMethod('');
            setReviewType('');
          } else {
            setCanEdit(false);
            history.push(`/hwms/ncReport-doc-maintain/dist/${res}`);
            handleQueryDetail(res);
          }
        },
      });
    }
  };

  const handleChangeDetail = (oldVal, fieldName) => {
    if (
      !ncRecordLineDs.length ||
      (!ncRecordLineDs.current?.get('ncRecordType') && !ncRecordLineDs.current?.get('ncRecordType'))
    ) {
      return;
    }
    const content = detailDs.current?.getField(fieldName)?.get('label');
    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <p>
          {intl
            .get(`${modelPrompt}.info.clearLineData`, {
              content,
            })
            .d(`不良记录单行、整体/部分处置信息会清空，确定更换${content}？`)}
        </p>
      ),
    }).then(button => {
      if (button === 'ok') {
        ncRecordLineDs.loadData([]);
        setDisposalType('NO');
        handleResetDisposal();
      } else {
        detailDs.current?.set(fieldName, oldVal);
      }
    });
  };

  const handleChangeBusRule = value => {
    if (value) {
      // 切换业务类型规则是，清空处置行上的数量
      if (disposalFunList.length && disposalDs.length) {
        disposalDs.forEach(_record => {
          disposalFunList.forEach(item => {
            _record.set(item?.dispositionFunction, 0);
          });
        });
      }
      const optionDs = detailDs.getField('inspectBusinessType')?.getOptions(detailDs.current);
      const currentRecord = optionDs?.find(
        _record => _record?.get('inspectBusinessType') === value,
      );
      detailDs.current?.set('reviewType', currentRecord?.get('reviewType'));
      disposalDs.current?.set('dispositionGroupId', currentRecord?.get('dispositionGroupId'));
      disposalDs.current?.set('dispositionGroupDesc', currentRecord?.get('dispositionGroupDesc'));
      queryBusinessTypeRuleInfo({ dispositionGroupId: currentRecord?.get('dispositionGroupId') });
    }
  };

  const handleResetDisposal = () => {
    const _disposalType = detailDs.current?.get('disposalType');
    const optionDs = detailDs.getField('inspectBusinessType')?.getOptions(detailDs.current);
    const currentRecord = optionDs?.find(
      _record =>
        _record?.get('inspectBusinessType') === detailDs.current?.get('inspectBusinessType'),
    );
    if (_disposalType === 'PART' || !_disposalType) {
      disposalDs.loadData([]);
    } else {
      disposalDs.loadData([
        {
          dispositionGroupId: currentRecord?.get('dispositionGroupId'),
          dispositionGroupDesc: currentRecord?.get('dispositionGroupDesc'),
          disposalUserLov: {
            id: userInfo.id,
            realName: userInfo.realName,
          },
          disposalTime: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
        },
      ]);
    }
  };

  const handleChangeDisposalType = value => {
    setDisposalType(value);
    if (value) {
      // 展开处置对应的折叠面板
      setActiveKey(['baseInfo', 'ncRecordLine', value]);
    }
    // 切换处置类型时，处置组旧数据需要清空
    handleResetDisposal();
    // 没有行明细时不做处理
    if (!ncRecordLineDs.length) {
      return;
    }
    if (createMethod === 'QMS') {
      if (value === 'ALL') {
        // 当不良记录单头创建方式=QMS，处置类型选择了整体处置时需自动生成不良对象类型=INSPECT_DOC的行
        handleCreateQmsLine();
      } else {
        // 当不良记录单头创建方式=QMS，处置类型切换了整体处置外的其余类型时需删除不良对象类型=INSPECT_DOC的行
        ncRecordLineDs.forEach(_record => {
          if (_record.get('ncObjectType') === 'INSPECT_DOC') {
            ncRecordLineDs.remove(_record);
          }
        });
      }
    }
    if (value === 'PART') {
      disposalDs.remove(disposalDs.current);
      // 根据不良记录行上的信息初始化处置DS
      ncRecordLineDs.forEach(_record => {
        const recordTypeRecord = getNcRecordTypeDesc(_record.get('ncRecordType'), _record);
        const objectTypeRecord = getNcObjectTypeDesc(_record.get('ncObjectType'), _record);
        disposalDs.create({
          ncRecordType: _record.get('ncRecordType'),
          ncRecordTypeDesc: recordTypeRecord?.get('meaning'),
          ncObjectType: _record.get('ncObjectType'),
          ncObjectTypeDesc: objectTypeRecord?.get('meaning'),
          ncObjectId: _record.get('ncObjectId'),
          ncObjectCode: _record.get('ncObjectCode'),
          ncObjectRevisionCode: _record.get('revisionCode'),
          PASS: _record.get('ncRecordType') !== 'EO_MATERIAL_NC' ? _record.get('qty') : 0,
          uomId: _record.get('uomId'),
          uomName: _record.get('uomName'),
          optionList: getOptionList(_record.toData().ncReportLineDtlList),
        });
      });
    }
  };

  // 自动生成不良对象类型=INSPECT_DOC的行
  const handleCreateQmsLine = () => {
    queryInspectDocLine({
      params: { inspectDocIds: ncRecordLineDs.map(_record => _record.get('sourceDocId')) },
      onSuccess: res => {
        (res || []).forEach(item => {
          ncRecordLineDs.create(
            {
              ncRecordType: ncRecordLineDs.current?.get('ncRecordType'),
              ncObjectType: 'INSPECT_DOC',
              ncObjectId: item.inspectDocId,
              ncObjectCode: item.inspectDocNum,
              qty: item.inspectSumQty,
              uomLov: item.uomId ? { uomId: item.uomId, uomName: item.uomName } : undefined,
              supplierLov: { supplierId: item.supplierId, supplierName: item.supplierName },
              customerLov: { customerId: item.customerId, customerName: item.customerName },
              locatorLov: { locatorId: item.locatorId, locatorName: item.locatorName },
              equipmentLov: { equipmentId: item.equipmentId, equipmentCode: item.equipmentCode },
              operationLov: { operationId: item.operationId, operationName: item.operationName },
            },
            0,
          );
        });
      },
    });
  };

  const syncPartDisposalRecord = (
    oldNcRecordType,
    oldNcObjectType,
    oldNcObjectLov,
    record,
    item = null,
  ) => {
    const {
      ncRecordType,
      ncObjectType,
      ncObjectId,
      ncObjectCode,
      ncObjectRevisionCode,
      qty,
      uomId,
      uomName,
    } = item || record.toData();
    const _curRecordRecord = getNcRecordTypeDesc(ncRecordType, record);
    const _curObjectRecord = getNcObjectTypeDesc(ncObjectType, record);

    const getNcObjectCode = type => {
      switch (type) {
        case 'MATERIAL_LOT':
          return oldNcObjectLov?.materialLotCode;
        case 'MAT':
        case 'LOT':
          return oldNcObjectLov?.materialName;
        case 'EO':
          return oldNcObjectLov?.eoNum;
        default:
          return undefined;
      }
    };

    const disposalRecords = disposalDs.filter(
      _record =>
        _record.get('ncRecordType') === oldNcRecordType &&
        _record.get('ncObjectType') === oldNcObjectType &&
        _record.get('ncObjectCode') === getNcObjectCode(oldNcObjectType),
    );
    if (!disposalRecords.length) {
      return;
    }
    disposalRecords.forEach(_record => {
      _record?.set('ncRecordType', ncRecordType);
      _record?.set('ncRecordTypeDesc', _curRecordRecord?.get('meaning'));
      _record?.set('ncObjectType', ncObjectType);
      _record?.set('ncObjectTypeDesc', _curObjectRecord?.get('meaning'));
      _record?.set('ncRecordType', ncRecordType);
      _record?.set('ncObjectId', ncObjectId);
      _record?.set('ncObjectCode', ncObjectCode);
      _record?.set('ncObjectRevisionCode', ncObjectRevisionCode);
      _record?.set('uomId', uomId);
      _record?.set('uomName', uomName);
      // 返修步骤和拦截步骤与eoId有关，需要清空
      _record?.set('interceptRouterStepLov', undefined);
      _record?.set('reworkStepLov', undefined);
      if (ncRecordType !== 'EO_MATERIAL_NC') {
        disposalFunList.forEach(item => {
          _record?.set(item?.dispositionFunction, item?.dispositionFunction === 'PASS' ? qty : 0);
        });
      }
    });
  };

  const findPartDisposalRecords = (ncRecordType, ncObjectType, ncObjectLov) => {
    const getNcObjectCode = type => {
      switch (type) {
        case 'MATERIAL_LOT':
          return ncObjectLov?.materialLotCode;
        case 'MATERIAL':
          return ncObjectLov?.materialName;
        case 'EO':
          return ncObjectLov?.eoNum;
        default:
          return undefined;
      }
    };
    return disposalDs.filter(
      _record =>
        _record.get('ncRecordType') === ncRecordType &&
        _record.get('ncObjectType') === ncObjectType &&
        _record.get('ncObjectCode') === getNcObjectCode(ncObjectType),
    );
  };

  const getNcRecordTypeDesc = (value, record) => {
    // 拿到类型对应的OptionDs
    const optionDs = ncRecordLineDs.getField('ncRecordType')?.getOptions(record);
    return optionDs?.find(_rec => _rec.get('value') === value);
  };

  const getNcObjectTypeDesc = (value, record) => {
    // 拿到类型对应的OptionDs
    const optionDs = ncRecordLineDs.getField('ncObjectType')?.getOptions(record);
    return optionDs?.find(_rec => _rec.get('value') === value);
  };

  // 不良记录单行上不良记录类型变化的回调
  const handleChangeNcRecordType = (val, oldVal, record) => {
    // 同步更新部分处置DS中对应的record
    if (disposalType === 'PART') {
      syncPartDisposalRecord(
        oldVal,
        record?.get('ncObjectType'),
        record?.get('ncObjectLov'),
        record,
        {
          ...record.toData(),
          ncObjectType: ['EO_MATERIAL_NC', 'EO_ALL_NC'].includes(val) ? 'EO' : undefined,
          ncObjectId: undefined,
          ncObjectCode: undefined,
          ncObjectRevisionCode: undefined,
        },
      );
    }
    // record.set('ncObjectType', ['EO_MATERIAL_NC', 'EO_ALL_NC'].includes(val) ? 'EO' : undefined);
    // record.set('ncObjectLov', undefined);
  };

  // 不良记录单行上不良对象类型变化的回调
  const handleChangeNcObjectType = (_, oldVal, record) => {
    // 同步更新部分处置DS中对应的record
    if (disposalType === 'PART') {
      syncPartDisposalRecord(
        record?.get('ncRecordType'),
        oldVal,
        record?.get('ncObjectLov'),
        record,
        {
          ...record.toData(),
          ncObjectId: undefined,
          ncObjectCode: undefined,
          ncObjectRevisionCode: undefined,
        },
      );
    }
    record.set('ncObjectLov', undefined);
  };

  // 切换清空逻辑待补充
  const handleUpdateLine = (value, record) => {
    const materialLotFlag = ['MAT', 'LOT', 'MATERIAL_LOT'].includes(record.get('ncObjectType'));
    record.set('uomId', materialLotFlag ? value?.primaryUomId : value?.uomId);
    record.set('uomName', materialLotFlag ? value?.primaryUomName : value?.uomName);
    record.set('lot', value?.lot || undefined);
    record.set('supplierLot', value?.supplierLot || undefined);
    record.set('supplierId', value?.supplierId || undefined);
    record.set('supplierName', value?.supplierName || undefined);
    record.set('customerId', value?.customerId || undefined);
    record.set('customerName', value?.customerName || undefined);
    record.set('locatorId', value?.locatorId || undefined);
    record.set('locatorName', value?.locatorName || undefined);
    record.set('containerId', value?.currentContainerId || undefined);
    record.set('containerName', value?.currentContainerName || undefined);
    record.set('containerCode', value?.currentContainerCode || undefined);
  };

  // 不良记录单行上不良对象编码变化的回调
  const handleChangeObjectLov = (value, oldVal, record) => {
    // 带出行上其余数据
    handleUpdateLine(value, record);
    handleValidateStepLov();

    if (disposalType === 'PART') {
      // 同步更新部分处置DS中对应的record
      syncPartDisposalRecord(
        record?.get('ncRecordType'),
        record?.get('ncObjectType'),
        oldVal,
        record,
      );
    }
  };

  const handleChangeQty = record => {
    if (disposalType === 'PART') {
      // 同步更新部分处置DS中对应的record
      syncPartDisposalRecord(
        record?.get('ncRecordType'),
        record?.get('ncObjectType'),
        record?.get('ncObjectLov'),
        record,
      );
    }
  };

  const DetailDrawerTitle = observer(({ record, dataSet }) => {
    const _title = record.get('revisionCode')
      ? `${intl.get(`${modelPrompt}.title.lineDetail`).d('行明细')}:
          ${record.get('ncObjectTypeDesc') || ''}${record.get('ncObjectCode') || ''}/
          ${record.get('revisionCode') || ''}`
      : `${intl.get(`${modelPrompt}.title.lineDetail`).d('行明细')}:
          ${record.get('ncObjectTypeDesc') || ''}${record.get('ncObjectCode') || ''}`;

    const handleAddLineDetail = () => {
      let _maxLineNum = 0;
      lineDetailDs.forEach(_record => {
        if (_record.get('lineNumber') > _maxLineNum) {
          _maxLineNum = _record.get('lineNumber');
        }
      });
      const newLine = Math.floor(_maxLineNum / 10) * 10 + 10;
      lineDetailDs.create({
        lineNumber: newLine,
      });
    };

    return (
      <div className={styles['drawer-title']}>
        <span className={styles['title-left']} style={{ fontSize: '14px' }}>
          {_title}
        </span>
        <span className={styles['title-right']}>
          <Button
            color={ButtonColor.primary}
            onClick={handleAddLineDetail}
            disabled={!canEdit || ncReportStatus !== 'NEW'}
          >
            {intl.get(`${modelPrompt}.button.addLineDtl`).d('添加')}
          </Button>
          <span className={styles['title-right-info']}>
            {intl
              .get(`${modelPrompt}.info.totalDetail`, {
                count: dataSet.length,
              })
              .d(`共 ${dataSet.length} 项明细`)}
          </span>
        </span>
      </div>
    );
  });

  const handleOpenLineDtlDrawer = record => {
    record.setState('isCancel', false);
    record.setState('isSubmit', false);
    if (canEdit && !lineDetailDs.length) {
      lineDetailDs.create({});
    }
    Modal.open({
      ...drawerPropsC7n({
        ds: lineDetailDs,
        canEdit: canEdit && ncReportStatus === 'NEW',
      }),
      title: (
        <DetailDrawerTitle record={record} dataSet={lineDetailDs} customizeForm={customizeForm} />
      ),
      style: {
        width: 1080,
      },
      onClose: () =>
        record.getState('isSubmit')
          ? record.setState('isCancel', false)
          : record.setState('isCancel', true),
      afterClose: () => record.getState('isCancel') && lineDetailDs.reset(),
      onCancel: () => {
        record.setState('isSubmit', false);
        record.setState('isCancel', true);
      },
      onOk: async () => {
        const validateRes = await lineDetailDs.validate();
        if (!validateRes) {
          return false;
        }
        record.setState('isSubmit', true);
        record.setState('isCancel', false);
        lineDetailDs.loadData(lineDetailDs.toData());
        // 将行明细的数据同步到处置上
        handleSyncOptionList(record, true);
      },
      children: (
        <DetailDrawerComponent
          record={record}
          lineDtlDs={lineDetailDs}
          customizeForm={customizeForm}
          canEdit={canEdit && ncReportStatus === 'NEW'}
          custConfig={custConfig}
        />
      ),
    });
  };

  // 新增不良记录行
  const handleAddRecordLine = () => {
    ncRecordLineDs.create({});
    if (disposalType === 'PART') disposalDs.create({});
  };

  const handleComponentDisposal = record => {
    const recordTypeRecord = getNcRecordTypeDesc(record.get('ncRecordType'), record);
    const objectTypeRecord = getNcObjectTypeDesc(record.get('ncObjectType'), record);
    disposalDs.create({
      ncRecordType: record.get('ncRecordType'),
      ncRecordTypeDesc: recordTypeRecord?.get('meaning'),
      ncObjectType: record.get('ncObjectType'),
      ncObjectTypeDesc: objectTypeRecord?.get('meaning'),
      ncObjectId: record.get('ncObjectId'),
      ncObjectCode: record.get('ncObjectCode'),
      ncObjectRevisionCode: record.get('revisionCode'),
      PASS: record.get('ncRecordType') !== 'EO_MATERIAL_NC' ? record.get('qty') : 0,
      uomId: record.get('uomId'),
      uomName: record.get('uomName'),
      optionList: getOptionList(record.toData().ncReportLineDtlList),
    });
  };

  // 删除不良记录行
  const deleteRecord = record => {
    // 拿到部分处置DS中对应的record
    const oldDisRecords = findPartDisposalRecords(
      record?.get('ncRecordType'),
      record?.get('ncObjectType'),
      record?.get('ncObjectLov'),
    );
    ncRecordLineDs.remove(record);
    if (disposalType === 'PART') disposalDs.remove(oldDisRecords);
    if (!ncRecordLineDs.length) {
      setDisposalType('NO');
      detailDs.current?.set('disposalType', 'NO');
      handleResetDisposal();
    }
  };

  const ncRecordLineColumns: ColumnProps[] = useMemo(() => {
    const DisposalButton = observer(({ record, disposalType }) => {
      return (
        <a
          disabled={
            !canEdit || record.get('ncRecordType') !== 'EO_MATERIAL_NC' || disposalType !== 'PART'
          }
          onClick={() => handleComponentDisposal(record)}
        >
          {intl.get(`${modelPrompt}.operation.physicalDisposal`).d('组件实物处置')}
        </a>
      );
    });

    return [
      {
        header: () => (
          <PermissionButton
            type="c7n-pro"
            icon="add"
            disabled={
              !canEdit ||
              ncReportStatus !== 'NEW' ||
              !detailDs.current?.get('siteId') ||
              !detailDs.current?.get('materialId') ||
              (detailDs.current?.get('revisionFlag') === 'Y' &&
                !detailDs.current?.get('revisionCode'))
            }
            onClick={handleAddRecordLine}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `dist.button.editNcRecordLine`,
                type: 'button',
                meaning: '详情页-新增删除不良记录单行',
              },
            ]}
          />
        ),
        name: 'editFirstColumn',
        align: ColumnAlign.center,
        lock: ColumnLock.left,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteRecord(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              disabled={
                !canEdit ||
                ncReportStatus !== 'NEW' ||
                record?.get('ncObjectType') === 'INSPECT_DOC'
              }
              funcType="flat"
              shape="circle"
              size="small"
              permissionList={[
                {
                  code: `dist.button.editNcRecordLine`,
                  type: 'button',
                  meaning: '详情页-新增删除不良记录单行',
                },
              ]}
            />
          </Popconfirm>
        ),
      },
      {
        name: 'ncRecordType',
        lock: ColumnLock.left,
        width: 80,
        editor: record =>
          canEdit &&
          ncReportStatus === 'NEW' && (
            <Select
              onChange={(val, oldVal) => handleChangeNcRecordType(val, oldVal, record)}
              optionsFilter={optionRecord => {
                const _ncObjectType = record.get('ncObjectType');
                if (!_ncObjectType) {
                  return (disposalType !== 'ALL' ||
                    optionRecord.get('value') !== 'EO_MATERIAL_NC');
                }
                if (_ncObjectType === 'EO') {
                  return (
                    optionRecord.get('value') === 'EO_ALL_NC' ||
                    (disposalType !== 'ALL' &&
                      optionRecord.get('value') === 'EO_MATERIAL_NC')
                  );
                }
                return !['EO_MATERIAL_NC', 'EO_ALL_NC'].includes(optionRecord.get('value'));
              }}
            />
          ),
      },
      {
        name: 'ncObjectType',
        lock: ColumnLock.left,
        width: 120,
        editor: record =>
          canEdit &&
          ncReportStatus === 'NEW' &&
          record.get('ncObjectType') !== 'INSPECT_DOC' && (
            <Select
              optionsFilter={optionRecord => {
                const _ncRecordType = record.get('ncRecordType');
                if (!_ncRecordType) {
                  return optionRecord.get('value') !== 'INSPECT_DOC';
                }
                if (['EO_MATERIAL_NC', 'EO_ALL_NC'].includes(_ncRecordType)) {
                  return optionRecord.get('value') === 'EO';
                }
                return !['INSPECT_DOC', 'EO'].includes(optionRecord.get('value'));
              }}
              onChange={(val, oldVal) => handleChangeNcObjectType(val, oldVal, record)}
            />
          ),
      },
      {
        name: 'ncObjectLov',
        width: 140,
        editor: record =>
          canEdit &&
          ncReportStatus === 'NEW' &&
          record.get('ncObjectType') !== 'INSPECT_DOC' && (
            <Lov onChange={(val, oldVal) => handleChangeObjectLov(val, oldVal, record)} />
          ),
      },
      {
        name: 'revisionCode',
        width: 140,
      },
      {
        name: 'qty',
        editor: record =>
          canEdit &&
          ncReportStatus === 'NEW' && <NumberField onChange={() => handleChangeQty(record)} />,
      },
      {
        name: 'sourceDocNum',
        hidden: kid === 'create',
      },
      {
        name: 'sourceNcRecordNum',
        width: 150,
        hidden: kid === 'create',
      },
      {
        name: 'uomLov',
        editor: () => canEdit && ncReportStatus === 'NEW' && <Lov />,
      },
      {
        name: 'lot',
        editor: () => canEdit && ncReportStatus === 'NEW' && <TextField />,
      },
      {
        name: 'supplierLot',
        editor: () => canEdit && ncReportStatus === 'NEW' && <TextField />,
      },
      {
        name: 'supplierLov',
        editor: () => canEdit && ncReportStatus === 'NEW' && <Lov />,
      },
      {
        name: 'customerLov',
        editor: () => canEdit && ncReportStatus === 'NEW' && <Lov />,
      },
      { name: 'containerLov', editor: () => canEdit && ncReportStatus === 'NEW' && <Lov /> },
      { name: 'workcellLov', editor: () => canEdit && ncReportStatus === 'NEW' && <Lov /> },
      { name: 'locatorLov', editor: () => canEdit && ncReportStatus === 'NEW' && <Lov /> },
      { name: 'equipmentLov', editor: () => canEdit && ncReportStatus === 'NEW' && <Lov /> },
      { name: 'operationLov', editor: () => canEdit && ncReportStatus === 'NEW' && <Lov /> },
      { name: 'shiftTeamLov', editor: () => canEdit && ncReportStatus === 'NEW' && <Lov /> },
      {
        name: 'shiftDate',
        editor: () => canEdit && ncReportStatus === 'NEW' && <DateTimePicker />,
      },
      { name: 'shiftCode', editor: () => canEdit && ncReportStatus === 'NEW' && <Select /> },
      { name: 'ncStartUserLov', editor: () => canEdit && ncReportStatus === 'NEW' && <Lov /> },
      {
        name: 'ncStartTime',
        editor: () => canEdit && ncReportStatus === 'NEW' && <DateTimePicker />,
      },
      { name: 'remark', editor: () => canEdit && ncReportStatus === 'NEW' && <TextField /> },
      { name: 'ncDesc' },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        name: 'attrColumn',
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 230,
        renderer: ({ record }) => (
          <span className="action-link">
            <AttributeDrawer
              serverCode={BASIC.TARZAN_SAMPLING}
              className="org.tarzan.qms.domain.entity.MtNcReportLine"
              kid={record?.get('ncReportLineId')}
              canEdit={canEdit}
              disabled={!record?.get('ncReportLineId')}
              custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.ATTR1`}
              custConfig={custConfig}
              type="text"
            />
            <a onClick={() => handleOpenLineDtlDrawer(record)}>
              {intl.get(`${modelPrompt}.operation.lineDetail`).d('行明细')}
            </a>
            <DisposalButton record={record} disposalType={disposalType} />
          </span>
        ),
      },
    ];
  }, [canEdit, disposalType, createMethod, kid]);

  // 更新处置上的下拉数据源
  const handleSyncOptionList = (record, currentFlag) => {
    const disRecords = findPartDisposalRecords(
      record?.get('ncRecordType'),
      record?.get('ncObjectType'),
      record?.get('ncObjectLov'),
    );
    const lineDtlList = currentFlag ? lineDetailDs.toData() : record.toData().ncReportLineDtlList;
    const _newOptionList = getOptionList(lineDtlList);
    (disRecords || []).forEach(_record => {
      _record.set('optionList', _newOptionList);
      // 检查处置所选的实物是否都在_newOptionList中,不在的话清空
      if (
        _record.get('componentMaterialLotCode') &&
        _newOptionList.findIndex(
          i => i.componentMaterialLotCode === _record.get('componentMaterialLotCode'),
        ) === -1
      ) {
        _record.init('componentMaterialLotCode', undefined);
        _record.init('PASS', 0);
      }
    });
  };

  // 拼接返回optionList的displayValue
  const getOptionValue = item => {
    const labelText = !['MAT', 'LOT'].includes(item.identifyType)
      ? intl.get(`${modelPrompt}.option.materialLot`).d('物料批')
      : intl.get(`${modelPrompt}.option.material`).d('物料');
    const materialText = item.componentRevisionCode
      ? `${item.componentMaterialName}/${item.componentRevisionCode}`
      : item.componentMaterialName;

    return !['MAT', 'LOT'].includes(item.identifyType)
      ? `${labelText}: ${item.componentMaterialLotCode}`
      : `${labelText}: ${materialText}`;
  };

  const getOptionList = data => {
    // 行明细去重，作为实物数据源
    const _optionList: any = [];
    (data || []).forEach(item => {
      const _index = _optionList.findIndex(
        (i: any) => i.componentMaterialLotCode === item.componentMaterialLotCode,
      );
      if (_index === -1) {
        _optionList.push({
          ...item,
          displayValue: getOptionValue(item),
        });
      }
    });
    return _optionList;
  };

  const handleChangeMaterialLotCode = (value, record) => {
    const currentData: any = (record.get('optionList') || []).find(
      (i: any) => i.componentMaterialLotCode === value,
    );
    record.set('PASS', currentData?.sumAssembleQty || 0);
    record.set('uomId', currentData?.uomId || undefined);
    record.set('uomName', currentData?.uomName || undefined);
  };

  // ncReportStatus=NEW时详情页所有字段均可编辑，同新建页面
  // ncReportStatus=HANDLE且ncReviewStatus=空或UNREVIEW或REJECT时详情页仅允许处置字段编辑
  // ncReportStatus=COMPLETED/CANCEL时详情页所有字段均不可编辑；
  // ncReportStatus=HANDLE且ncReviewStatus=REVIEWING审核中、REVIEW已审核时所详情页有字段均不可编辑；
  const partDisposalColumns: any = useMemo(() => {
    const disposalEditFlag =
      canEdit &&
      (!['COMPLETED', 'CANCEL'].includes(ncReportStatus) ||
        (ncReportStatus === 'HANDLE' && !['REVIEWING', 'REVIEW'].includes(ncReviewStatus)));

    return [
      {
        name: 'editColumn',
        align: ColumnAlign.center,
        lock: ColumnLock.left,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => disposalDs.remove(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              disabled={!disposalEditFlag || record.get('ncRecordType') !== 'EO_MATERIAL_NC'}
              funcType="flat"
              shape="circle"
              size="small"
              permissionList={[
                {
                  code: `dist.button.editPartDisposal`,
                  type: 'button',
                  meaning: '详情页-删除部分处置行按钮',
                },
              ]}
            />
          </Popconfirm>
        ),
      },
      { name: 'ncRecordTypeDesc', width: 150 },
      { name: 'ncObjectTypeDesc', width: 150 },
      {
        name: 'ncObjectCode',
        width: 150,
        renderer: ({ record }) =>
          record?.get('ncObjectRevisionCode')
            ? `${record?.get('ncObjectCode')}/${record?.get('ncObjectRevisionCode')}`
            : record?.get('ncObjectCode'),
      },
      {
        name: 'componentMaterialLotCode',
        width: 150,
        editor: record => {
          const groupRecords = record.dataSet.filter(_record => {
            return (
              _record.get('ncRecordType') === record.get('ncRecordType') &&
              _record.get('ncObjectType') === record.get('ncObjectType') &&
              _record.get('ncObjectCode') === record.get('ncObjectCode') &&
              (!record.get('ncObjectRevisionCode') ||
                _record.get('ncObjectRevisionCode') === record.get('ncObjectRevisionCode')) &&
              _record.get('componentMaterialLotCode') !== record.get('componentMaterialLotCode')
            );
          });
          const filterList = (groupRecords || []).map(_record =>
            _record.get('componentMaterialLotCode'),
          );
          return (
            disposalEditFlag &&
            record.get('ncRecordType') === 'EO_MATERIAL_NC' && (
              <Select
                name="componentMaterialLotCode"
                onChange={val => handleChangeMaterialLotCode(val, record)}
                optionsFilter={optionRecord => {
                  return !filterList.includes(optionRecord.get('componentMaterialLotCode'));
                }}
              />
            )
          );
        },
        renderer: ({ record, value }) =>
          record.get('ncRecordType') === 'EO_MATERIAL_NC' ? value : null,
      },
      ...disposalFunList.map(item => ({
        name: item.dispositionFunction,
        header: item.description,
        width: 150,
        editor: () => disposalEditFlag && item.dispositionFunction !== 'PASS' && <NumberField />,
      })),
      {
        name: 'uomLov',
        width: 130,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.reworkRouterFlag && {
        name: 'reworkRouterLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.reworkSourceFlag && {
        name: 'reworkStepLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.reworkSourceFlag && {
        name: 'reworkWorkcellLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.reworkSourceFlag && {
        name: 'reworkOperationLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.degradeFlag && {
        name: 'degradeLevel',
        width: 150,
        editor: () => disposalEditFlag && <TextField />,
      },
      dynamicDisplayFlag.degradeFlag && {
        name: 'degradeMaterialLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.degradeFlag && {
        name: 'degradeRevisionCode',
        width: 150,
        editor: () => disposalEditFlag && <Select />,
      },
      dynamicDisplayFlag.eoDischargeFlag && {
        name: 'dischargeWorkcellLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.crossWoFlag && {
        name: 'crossOperationLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.concessionFlag && {
        name: 'interceptOperationLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.concessionFlag && {
        name: 'interceptWorkcellLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      dynamicDisplayFlag.concessionFlag && {
        name: 'interceptRouterStepLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      {
        name: 'remark',
        editor: () => disposalEditFlag && <TextField />,
      },
      {
        name: 'disposalUserLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      {
        name: 'disposalTime',
        width: 150,
        editor: () => disposalEditFlag && <DateTimePicker />,
      },
      {
        name: 'disposalApartment',
        width: 150,
        editor: () => disposalEditFlag && <TextField />,
      },
    ];
  }, [canEdit, disposalFunList, dynamicDisplayFlag, ncReportStatus, ncReviewStatus]);

  const handleScanInspectObj = async () => {
    const val = scanFormDs.current?.get('scanInspectObj');
    if (!val) {
      return;
    }
    const result = await queryEoOrMaterialLot({
      params: {
        tenantId: getCurrentOrganizationId(),
        siteId: detailDs.current?.get('siteId'),
        materialId: detailDs.current?.get('materialId'),
        objectCode: val,
        permissionFlag: 'N',
      },
    });
    if (result && result?.content) {
      const res = result?.content[0];
      handleResetQueryDs();
      if (res) {
        const scanRecord = ncRecordLineDs?.find(
          _rec =>
            _rec?.get('ncObjectCode') === res?.objectCode &&
            _rec?.get('ncObjectType') === res?.objectType,
        );
        if (scanRecord) {
          notification.error({
            message: intl
              .get(`${modelPrompt}.info.scanValidate`, {
                ncObjectType: scanRecord?.get('ncObjectType'),
                ncObjectCode: scanRecord?.get('ncObjectCode'),
              })
              .d(
                `同一个不良记录下仅允许存在唯一不良对象类型【${scanRecord?.get(
                  'ncObjectType',
                )}】和不良对象【${scanRecord?.get('ncObjectCode')}】！`,
              ),
          });
        } else {
          handleAddLine(res);
        }
      }
    }
  };

  const handleChangeFunction = val => {
    setDispositionFunction(val?.dispositionFunction);
    disposalDs.current?.init('interceptWorkcellLov', undefined);
    disposalDs.current?.init('interceptRouterStepLov', undefined);
    disposalDs.current?.init('dischargeWorkcellLov', undefined);
    disposalDs.current?.init('crossOperationLov', undefined);
    disposalDs.current?.init('reworkStepLov', undefined);
    disposalDs.current?.init('reworkWorkcellLov', undefined);
    disposalDs.current?.init('reworkRouterLov', undefined);
    disposalDs.current?.init('degradeLevel', undefined);
    disposalDs.current?.init('degradeMaterialLov', undefined);
    disposalDs.current?.init('degradeRevisionCode', undefined);
  };

  const handleValidateStepLov = () => {
    if (disposalType === 'ALL') {
      const isDisabled = disposalDs.getField('interceptRouterStepLov')?.get('disabled');
      if (isDisabled) {
        disposalDs.current?.init('interceptRouterStepLov', undefined);
        disposalDs.current?.init('reworkStepLov', undefined);
      }
    }
  };

  const handleAddLine = value => {
    // 移除自动创建的行和处置记录
    if (
      ncRecordLineDs.length === 1 &&
      !ncRecordLineDs.current?.get('ncRecordType') &&
      !ncRecordLineDs.current?.get('ncObjectType')
    ) {
      ncRecordLineDs.remove(ncRecordLineDs.current);
    }
    if (disposalDs.length === 1 && !disposalDs.current?.get('ncObjectType')) {
      disposalDs.remove(disposalDs.current);
    }
    ncRecordLineDs.create({
      ...value,
      ncObjectType: value.objectType,
      ncObjectCode: value.objectCode,
      uomId: value.primaryUomId,
      uomName: value.primaryUomName,
      containerId: value.currentContainerId,
      containerName: value.currentContainerName,
    });
    if (disposalType === 'PART') {
      disposalDs.create({
        ncObjectType: value.objectType,
        ncObjectTypeDesc: value.objectTypeDesc,
        ncObjectCode: value.objectCode,
        uomId: value.primaryUomId,
        uomName: value.primaryUomName,
      });
    }
  };

  const handleResetQueryDs = () => {
    const lovDs = scanFormDs.getField('scanInspectObjLov')?.getOptions(scanFormDs.current);
    lovDs?.queryDataSet?.loadData([]);
    lovDs?.loadData([]);
  };

  return (
    <div className="hmes-style">
      <TarzanSpin
        dataSet={detailDs}
        spinning={
          saveLoading ||
          queryLoading ||
          saveAndSubmitLoading ||
          reviewLoading ||
          queryEOLoading ||
          directDisposalLoading ||
          queryInspectDocLoading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('不良记录单维护')}
          backPath="/hwms/ncReport-doc-maintain/list"
        >
          {canEdit ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave(false)}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="save" onClick={() => handleSave(true)}>
                {intl.get(`tarzan.common.button.saveAndCreate`).d('保存并新建下一条')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button icon="save" onClick={handleSaveAndSubmit}>
                {intl.get(`${modelPrompt}.button.submit`).d('保存并提交')}
              </Button>
            </>
          ) : (
            <>
              <PermissionButton
                type="c7n-pro"
                icon="edit-o"
                color={ButtonColor.primary}
                onClick={() => setCanEdit(true)}
                disabled={
                  ['COMPLETED', 'CANCEL'].includes(ncReportStatus) ||
                  (ncReportStatus === 'HANDLE' && ncReviewStatus === 'REVIEWING')
                }
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </PermissionButton>
              <PermissionButton
                disabled={
                  reviewType !== 'MANUAL_REVIEW' ||
                  !['UNREVIEWED', 'REJECT'].includes(ncReviewStatus)
                }
                loading={reviewLoading}
                onClick={handleReview}
                permissionList={[
                  {
                    code: `dist.button.review`,
                    type: 'button',
                    meaning: '详情页-发起工作流按钮',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.review`).d('发起工作流')}
              </PermissionButton>
              <PermissionButton
                disabled={
                  !(
                    createMethod === 'QMS' &&
                    reviewType === 'MANUAL_REVIEW' &&
                    ncReviewStatus === 'UNREVIEWED' &&
                    ncReportStatus === 'HANDLE'
                  )
                }
                loading={directDisposalLoading}
                onClick={handleDirectDisposal}
                permissionList={[
                  {
                    code: `dist.button.directDisposal`,
                    type: 'button',
                    meaning: '详情页-直接处置按钮',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.directDisposal`).d('直接处置')}
              </PermissionButton>
            </>
          )}
          <AttributeDrawer
            serverCode={BASIC.TARZAN_SAMPLING}
            className="org.tarzan.qms.domain.entity.MtNcReport"
            kid={kid}
            canEdit={canEdit}
            disabled={kid === 'create'}
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.ATTR`}
            custConfig={custConfig}
          />
        </Header>
        <Content>
          <Collapse bordered={false} activeKey={activeKey} onChange={val => setActiveKey(val)}>
            <Panel key="baseInfo" header={intl.get(`${modelPrompt}.title.baseInfo`).d('基础信息')}>
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.HEAD`,
                },
                <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                  <TextField name="ncReportNum" />
                  <Select name="ncReportType" />
                  <Select name="ncReportStatus" />
                  <Lov
                    name="siteLov"
                    onChange={(_, oldValue) => handleChangeDetail(oldValue, 'siteLov')}
                  />
                  <Select
                    name="inspectBusinessType"
                    onChange={value => handleChangeBusRule(value)}
                    noCache
                  />
                  <Select name="reviewType" />
                  <Select name="ncReviewStatus" />
                  <NumberField name="inspectSumQty" />
                  <NumberField name="okQty" />
                  <NumberField name="ngQty" />
                  <NumberField name="samplingQty" />
                  <Select name="createMethod" />
                  <SelectBox
                    name="disposalType"
                    onChange={handleChangeDisposalType}
                    onOption={({ record }) => {
                      const filterLine = ncRecordLineDs.filter((_record) => _record.get('ncRecordType') === 'EO_MATERIAL_NC')
                      return {
                        disabled: record.get('value') === 'ALL' && !!filterLine?.length,
                      };
                    }}
                  />
                  <C7nFormItemSort name="materialLov" itemWidth={['70%', '30%']}>
                    <Lov
                      name="materialLov"
                      onChange={(_, oldVal) => handleChangeDetail(oldVal, 'materialLov')}
                    />
                    <Select
                      name="revisionCode"
                      onChange={(_, oldVal) => handleChangeDetail(oldVal, 'revisionCode')}
                    />
                  </C7nFormItemSort>
                  <TextField name="remark" />
                  <TextArea name="ncDescription" colSpan={3} rowSpan={2} newLine />
                </Form>,
              )}
            </Panel>
            <Panel
              key="ncRecordLine"
              header={intl.get(`${modelPrompt}.title.ncRecordLine`).d('不良记录单行')}
            >
              <TextField
                className={styles['login-scan-form']}
                name="scanInspectObj"
                dataSet={scanFormDs}
                placeholder={intl
                  .get(`${modelPrompt}.placeholder.scanInspectObjLov`)
                  .d('请选择/扫描物料批或EO')}
                suffix={<img alt="" src={scanImg} />}
                onEnterDown={handleScanInspectObj}
                disabled={!canEdit || ncReportStatus !== 'NEW'}
              />
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.LINE`,
                },
                <Table
                  dataSet={ncRecordLineDs}
                  columns={ncRecordLineColumns}
                  customizedCode="ncReportDocMaintain-detailLine"
                  filter={record => {
                    if (createMethod === 'QMS') {
                      return disposalType === 'ALL'
                        ? record.get('ncObjectType') === 'INSPECT_DOC'
                        : record.get('ncObjectType') !== 'INSPECT_DOC';
                    }
                    return true;
                  }}
                />,
              )}
            </Panel>
            {disposalType === 'ALL' && (
              <Panel key="ALL" header={intl.get(`${modelPrompt}.title.allDisposal`).d('整体处置')}>
                <Form
                  dataSet={disposalDs}
                  columns={3}
                  disabled={
                    !canEdit ||
                    !(
                      !['COMPLETED', 'CANCEL'].includes(ncReportStatus) ||
                      (ncReportStatus === 'HANDLE' &&
                        !['REVIEWING', 'REVIEW'].includes(ncReviewStatus))
                    )
                  }
                >
                  <Lov name="dispositionGroupLov" />
                  <Lov name="dispositionFunctionLov" onChange={handleChangeFunction} />
                  <TextField name="functionTypeDesc" />
                  {dispositionFunction === 'CONCESSION_INTERCEPTION' && (
                    <Lov name="interceptOperationLov" />
                  )}
                  {dispositionFunction === 'CONCESSION_INTERCEPTION' && (
                    <Lov name="interceptWorkcellLov" />
                  )}
                  {dispositionFunction === 'CONCESSION_INTERCEPTION' && (
                    <Lov name="interceptRouterStepLov" />
                  )}
                  {dispositionFunction === 'EO_DISCHARGE' && <Lov name="dischargeWorkcellLov" />}
                  {dispositionFunction === 'CROSS_WO_INTERCEPT' && <Lov name="crossOperationLov" />}
                  {dispositionFunction === 'REWORK_SOURCE' && <Lov name="reworkStepLov" />}
                  {dispositionFunction === 'REWORK_SOURCE' && <Lov name="reworkWorkcellLov" />}
                  {dispositionFunction === 'REWORK_SOURCE' && <Lov name="reworkOperationLov" />}
                  {dispositionFunction === 'REWORK_ROUTER' && <Lov name="reworkRouterLov" />}
                  {dispositionFunction === 'DEGRADE' && <TextField name="degradeLevel" />}
                  {dispositionFunction === 'DEGRADE' && <Lov name="degradeMaterialLov" />}
                  {dispositionFunction === 'DEGRADE' && <Select name="degradeRevisionCode" />}
                  <TextField name="remark" />
                  <Lov name="disposalUserLov" />
                  <DateTimePicker name="disposalTime" />
                  <TextField name="disposalApartment" />
                </Form>
              </Panel>
            )}
            {disposalType === 'PART' && (
              <Panel
                key="PART"
                header={intl.get(`${modelPrompt}.title.partDisposal`).d('对象处置')}
              >
                <Table
                  dataSet={disposalDs}
                  columns={partDisposalColumns}
                  customizedCode="ncReportDocMaintain-partDisposal"
                />
              </Panel>
            )}
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.ATTR1`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.ATTR2`,
    ],
  })(NcReportDetail as any),
);
