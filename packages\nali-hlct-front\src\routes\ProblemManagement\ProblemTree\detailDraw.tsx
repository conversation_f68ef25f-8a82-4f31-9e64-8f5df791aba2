/**
 * RelationMaintain - 故障树
 * @date: 2023-8-24
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React, { useEffect, useState } from 'react';
import { Form, TextField, Select, Table, Lov, Button, Spin } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import {
  saveSubordinateRelationshipConfig,
  updateTreeEventConfig,
  fetchTreeEventConfig,
  deleteSelfAndRelationshipConfig,
} from './services';
import intl from 'utils/intl';
import styles from './index.module.less';

import { getCurrentOrganizationId, getCurrentRole } from 'utils/utils';

import { queryUnifyIdpValue } from '@/services/api.js';

const tenantId = getCurrentOrganizationId();
const { code: currentRoleCode  } = getCurrentRole();

function ProblemTree(props) {
  const {
    type,
    addSubRelationDs,
    addSubRelationTableDs,
    data,
    callBack,
    closeModal,
    setloading,
    loading,
    user,
    openProblemDetail,
  } = props;

  const [canEdit, setCanEdit] = useState(false);

  // 新增下级及关系
  const saveSubordinateRelationship = useRequest(saveSubordinateRelationshipConfig(), {
    manual: true,
    needPromise: true,
  });

  // 更新详情
  const updateTreeEvent = useRequest(updateTreeEventConfig(), {
    manual: true,
    needPromise: true,
  });

  // 获取详情
  const fetchTreeEvent = useRequest(fetchTreeEventConfig(), {
    manual: true,
    needPromise: true,
  });

  // 删除自身及关系
  const deleteSelfAndRelationship = useRequest(deleteSelfAndRelationshipConfig(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if (type === 'create') {
      setCanEdit(true);
      addSubRelationDs.loadData([{}]);
    } else {
      addSubRelationDs.loadData([{}]);
      handleInitDraw(data.failureTreeId);
    }

    getType();
  }, [data, type]);

  const getType = async () => {
    const res = await queryUnifyIdpValue('YP.QIS.FAILURE_TREE_EVENT_TYPE', { tenantId });
    const _typeList: any = {
      TOP: [],
      MIDDLE: [],
      BOTTOM: [],
    };
    (res || []).forEach(item => {
      if (_typeList[item.tag]) {
        _typeList[item.tag].push({ ...item });
      }
    });

    if (type === 'create') {
      if (data.failureTreeType === 'CLASS') {
        if (_typeList.TOP.length === 1) {
          addSubRelationDs.current?.set('failureTreeType', _typeList.TOP[0].value);
        }
      } else if ([..._typeList.TOP, ..._typeList.BOTTOM].length === 1) {
        addSubRelationDs.current?.set(
          'failureTreeType',
          [..._typeList.TOP, ..._typeList.BOTTOM][0].value,
        );
      }
    }
  };

  const handleInitDraw = async id => {
    const res = await fetchTreeEvent.run({
      params: {
        failureTreeId: id,
      },
    });

    if (res?.success) {
      const { relatedProblem, ...other } = res?.rows;
      const problemId: any = [];
      const problemTitle: any = [];
      const mainDepartment: any = [];
      const problemCode: any = [];
      const problemCategory: any = [];
      (relatedProblem || []).forEach(item => {
        problemId.push((item.problemId || 0) - 0);
        problemTitle.push(item.problemTitle);
        mainDepartment.push(item.mainDepartment);
        problemCode.push(item.problemCode);
        problemCategory.push(item.problemCategory);
      });
      addSubRelationDs.loadData([
        {
          ...other,
          problemId,
          problemTitle,
          mainDepartment,
          problemCode,
          problemCategory,
        },
      ]);
    }
  };

  const columns: ColumnProps[] = [
    {
      name: 'problemCode',
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            openProblemDetail(record?.get('problemId'));
          }}
          // @ts-ignore
          disabled={record?.get('problemCategory') === 'SALES' && currentRoleCode !== 'shzlgcs'}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'problemTitle',
    },
    {
      name: 'problemCategory',
    },
    {
      name: 'mainDepartment',
    },
  ];

  const handleAddSubSave = async () => {
    const validateResult = await addSubRelationDs.validate();
    if (!validateResult) {
      return false;
    }
    const parmas: any = addSubRelationDs.toData()[0];

    if (parmas.problemCode) {
      delete parmas.problemCode;
    }
    if (parmas.problemId) {
      delete parmas.problemId;
    }

    const problemJason = (parmas.relatedProblem || []).map((item, index) => {
      return {
        sequence: index * 10 + 10,
        problemid: (item.problemId || '0') - 0,
      };
    });

    parmas.problemJason = JSON.stringify(problemJason);

    setloading(true);

    let res;
    if (type === 'create') {
      if (data.failureTreeType === 'CLASS') {
        res = await saveSubordinateRelationship.run({
          params: {
            ...parmas,
            siteId: data.siteId,
            failureClass: data.failureTreeClass,
            sequence: data.subSum * 10 + 10,
          },
        });
      } else {
        res = await saveSubordinateRelationship.run({
          params: {
            ...parmas,
            parentPath: data.path,
            parentFailureTreeId: data.failureTreeId,
            sequence: data.subSum * 10 + 10,
          },
        });
      }
    } else {
      res = await updateTreeEvent.run({
        params: { ...data, ...parmas },
      });
    }

    if (res?.success) {
      notification.success({});
      if (type === 'create') {
        closeModal();
        callBack(data.path);
      } else {
        setCanEdit(prev => !prev);
        callBack(data.path);
      }
    }
    setloading(false);

    return false;
  };

  const handleDelete = async () => {
    setloading(true);

    const params: any = {};
    params.parentFailureTreeId = data.parentFailureTreeId;
    params.currentFailureTreeId = data.failureTreeId;
    params.currentPath = data.path;

    const res = await deleteSelfAndRelationship.run({
      params,
    });

    if (res?.success) {
      notification.success({});
      closeModal();
      callBack();
    }
    setloading(false);

    return false;
  };

  const handleClose = () => {
    closeModal();
  };

  const handleEdit = () => {
    setCanEdit(prev => !prev);
  };

  const handleCancel = () => {
    if (type === 'create') {
      closeModal();
    } else {
      setCanEdit(prev => !prev);
    }
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.modalControl}>
        {!canEdit && (
          <>
            <Button onClick={handleClose}>
              {intl.get('hzero.common.button.close').d('关闭')}
            </Button>
            <Popconfirm placement="left" title={intl.get('hzero.common.button.delete').d('删除')} onConfirm={handleDelete}>
              <Button disabled={user?.id !== data?.userId}>
                {intl.get('hzero.common.button.delete').d('删除')}
              </Button>
            </Popconfirm>

            <Button
              color={ButtonColor.primary}
              onClick={handleEdit}
              disabled={user?.id !== data?.userId}
            >
              {intl.get('hzero.common.button.edit').d('编辑')}
            </Button>
          </>
        )}
        {canEdit && (
          <>
            <Button onClick={handleCancel}>
              {intl.get('hzero.common.button.cancel').d('取消')}
            </Button>
            <Button color={ButtonColor.primary} onClick={handleAddSubSave}>
              {intl.get('hzero.common.button.save').d('保存')}
            </Button>
          </>
        )}
      </div>
      <Form dataSet={addSubRelationDs} columns={2} labelWidth={112} disabled={!canEdit}>
        <TextField name="failureTreeName" />
        <Select
          name="failureTreeType"
          optionsFilter={record => {
            if (type === 'create') {
              if (data.failureTreeType === 'CLASS') {
                return record.get('tag') === 'TOP';
              }
              return ['MIDDLE', 'BOTTOM'].includes(record.get('tag'));
            }
            if (data.tag === 'TOP') {
              return record.get('tag') === 'TOP';
            }
            return ['MIDDLE', 'BOTTOM'].includes(record.get('tag'));
          }}
        />
        <TextField name="failureTreeCode" />
        <Lov name="relatedProblem" />
      </Form>
      <Table dataSet={addSubRelationTableDs} columns={columns} />
    </Spin>
  );
}

export default formatterCollections({
  code: [
    'tarzan.qms.problemTree',
    'hzero.common',
  ],
})(ProblemTree);
