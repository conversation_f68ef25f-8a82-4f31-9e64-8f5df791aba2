/**
 * 检查项明细
 * @since 2021-01-20
 * <AUTHOR>
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';

const organizationId = getCurrentOrganizationId();

const modelPrompt = 'alm.checklistEditModal.model.checklistEditModal';

const queryUrl = `${HALM_MTC}/v1/${organizationId}/wo-checklists/list`;

const parentChecklistCodes = {
  WO: 'AMTC.PARENT_WO_CHECKLISTS',
  ACT: 'AMTC.PARENT_ACT_CHECKLISTS',
  CHECKLIST_GROUP: 'AMTC.PARENT_BASE_CHECKLISTS',
  SPOT_CHECK: 'AMTC.PARENT_WO_CHECKLISTS',
  WO_TYPE: 'AMTC.PARENT_ACT_CHECKLISTS',
  WOOP: 'AMTC.PARENT_WO_CHECKLISTS',
  MAINTAIN_PLAN: 'AMTC.PARENT_ACT_CHECKLISTS',
};

// 列表页table
function detailDs({ compParentId, compParentType, maintSiteIds }) {
  return {
    autoCreate: true,
    fields: [
      {
        name: 'itemSeq',
        label: intl.get(`${modelPrompt}.itemSeq`).d('序号'),
        type: 'number',
        min: 1,
        step: 1,
      },
      {
        name: 'checklistId',
        type: 'number',
      },
      {
        name: 'parentTypeCode',
        label: intl.get(`${modelPrompt}.parentTypeCode`).d('类型'),
        type: 'string',
        dynamicProps: {
          lookupCode: () => {
            if (compParentType === 'WO') {
              return 'AMTC.WO_RELATION_PARENT_TYPE';
            } else if (['ACT', 'MAINTAIN_PLAN'].includes(compParentType)) {
              return 'AMTC.ACT_CHECKLIST_PARENT_TYPE';
            }
          },
          required: () => ['ACT', 'WO', 'MAINTAIN_PLAN'].includes(compParentType),
        },
      },
      {
        name: 'parentName',
        type: 'object',
        dynamicProps: {
          lovCode: ({ record }) => {
            const parentTypeCode = record?.get('parentTypeCode');
            if (compParentType === 'WO') {
              return parentTypeCode === 'WOOP' ? 'AMTC.WOOP' : 'AMTC.WORK_ORDER';
            } else if (compParentType === 'ACT') {
              return parentTypeCode === 'ACT_OP' ? 'AMTC.ACT_OP' : 'AMTC.ACT';
            } else if (compParentType === 'MAINTAIN_PLAN') {
              return parentTypeCode === 'MAINTAIN_PLAN_STEP' ? 'AMTC.ACT_OP' : 'ALM.MAINTAIN_PLAN';
            }
          },
          lovPara: ({ record }) => {
            const parentTypeCode = record?.get('parentTypeCode');
            if (compParentType === 'WO') {
              return {
                organizationId,
                woId: compParentId,
                excludeWoopStatusList: 'CANCELED,UNABLE,COMPLETED,CLOSED',
              };
            } else if (['ACT', 'MAINTAIN_PLAN'].includes(compParentType)) {
              return ['ACT_OP', 'MAINTAIN_PLAN_STEP'].includes(parentTypeCode)
                ? {
                    organizationId,
                    headerId: compParentId,
                    headerTypeCode: compParentType,
                  }
                : {
                    organizationId,
                  };
            }
          },
          required: () => ['ACT', 'WO', 'MAINTAIN_PLAN'].includes(compParentType),
        },
      },
      {
        name: 'parentId',
        type: 'number',
      },
      // 以下四个字段仅仅是为了加载值集视图 避免上面 动态lovCode导致值集视图打不开
      {
        name: 'lov',
        type: 'object',
        lovCode: 'AMTC.WOOP',
      },
      {
        name: 'lov2',
        type: 'object',
        lovCode: 'AMTC.WORK_ORDER',
      },
      {
        name: 'lov3',
        type: 'object',
        lovCode: 'AMTC.ACT_OP',
      },
      {
        name: 'lov4',
        type: 'object',
        lovCode: 'AMTC.ACT',
      },
      {
        name: 'lov5',
        type: 'object',
        lovCode: 'ALM.MAINTAIN_PLAN',
      },
      {
        name: 'businessScenarioCode', // WO ACT SPOT_CHECK(点巡检单) 时使用
        label: intl.get(`${modelPrompt}.checkTiming`).d('检查时点'),
        type: 'string',
        lookupCode: 'AMTC.CHECK_TIMING',
        dynamicProps: {
          required: () =>
            ['ACT', 'WO', 'SPOT_CHECK', 'WO_TYPE', 'WOOP', 'MAINTAIN_PLAN'].includes(
              compParentType
            ),
        },
      },
      {
        name: 'checkTiming', // CHECKLIST_GROUP（标准检查组） 时使用
        label: intl.get(`${modelPrompt}.checkTiming`).d('检查时点'),
        type: 'string',
        lookupCode: 'AMTC.CHECK_TIMING',
        dynamicProps: {
          required: () => compParentType === 'CHECKLIST_GROUP',
        },
      },
      {
        name: 'checklistName',
        label: intl.get(`${modelPrompt}.checklistName`).d('名称'),
        type: 'string',
        required: true,
        computedProps: {
          maxLength: ({ record }) => {
            return record.get('columnTypeCode') === 'INDEX' ? 16 : 40;
          },
        },
      },
      {
        name: 'methodCode',
        label: intl.get(`${modelPrompt}.methodCode`).d('检测方式'),
        type: 'string',
        maxLength: 240,
      },
      {
        name: 'standardReference',
        label: intl.get(`${modelPrompt}.standardReference`).d('参考标准'),
        type: 'string',
        maxLength: 240,
      },
      {
        name: 'columnTypeCode',
        label: intl.get(`${modelPrompt}.columnType`).d('记录方式'),
        type: 'string',
        required: true,
        lookupCode: 'AMTC.FIELD_TYPE',
      },
      {
        name: 'isThereAlarm',
        label: intl.get(`${modelPrompt}.isThereAlarm`).d('是否告警'),
        type: 'string',
        trueValue: '1',
        falseValue: '0',
        defaultValue: '0',
      },
      {
        name: 'trueValue',
        label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
        type: 'string',
      },
      {
        name: 'parentChecklistLov',
        label: intl.get(`${modelPrompt}.parentChecklist`).d('父项'),
        type: 'object',
        dynamicProps: {
          lovCode: () => {
            return parentChecklistCodes[compParentType];
          },
          lovPara: ({ record }) => {
            const { parentId, parentTypeCode, businessScenarioCode, checklistId, checkTiming } =
              record?.toData() || {};
            return compParentType === 'CHECKLIST_GROUP'
              ? {
                  organizationId,
                  checklistGroupId: parentId,
                  checkTiming,
                }
              : {
                  organizationId,
                  parentId,
                  parentTypeCode,
                  businessScenarioCode,
                  columnTypeCode: 'INDEX',
                  notInIdList: checklistId,
                };
          },
        },
      },
      {
        name: 'lov5',
        type: 'object',
        lovCode: 'AMTC.PARENT_WO_CHECKLISTS',
      },
      {
        name: 'lov6',
        type: 'object',
        lovCode: 'AMTC.PARENT_ACT_CHECKLISTS',
      },
      {
        name: 'parentChecklistId',
        type: 'number',
        bind: 'parentChecklistLov.checklistId',
      },
      {
        name: 'parentChecklistName',
        label: intl.get(`${modelPrompt}.parentChecklist`).d('父项'),
        type: 'string',
        bind: 'parentChecklistLov.checklistName',
      },
      {
        name: 'minimumWordLimit',
        label: intl.get(`${modelPrompt}.minimumWordLimit`).d('最低字数限制'),
        type: 'number',
        min: 1,
        pattern: /^(\d{1,2}|1\d{2}|2[0-3]\d|240)$/g,
      },
      {
        name: 'listValueCode',
        label: intl.get(`${modelPrompt}.listValueCode`).d('值列表'),
        type: 'string',
        lookupCode: 'AMTC.VALUE_LIST',
      },
      {
        name: 'meterTypeLov',
        label: intl.get(`${modelPrompt}.meterTypeName`).d('仪表点类型'),
        type: 'object',
        lovCode: 'AMTR.METER_TYPES',
        lovPara: {
          tenantId: organizationId,
          enabledFlag: 1,
        },
        dynamicProps: {
          required: ({ record }) =>
            ['ACT', 'WO_TYPE', 'CHECKLIST_GROUP', 'MAINTAIN_PLAN'].includes(compParentType) &&
            record?.get('columnTypeCode') === 'NUMBER',
        },
        ignore: 'always',
      },
      {
        name: 'meterTypeId',
        type: 'number',
        bind: 'meterTypeLov.meterTypeId',
      },
      {
        name: 'meterTypeName',
        type: 'string',
        bind: 'meterTypeLov.meterTypeName',
      },
      {
        name: 'meterLov',
        label: intl.get(`${modelPrompt}.meter`).d('仪表点'),
        type: 'object',
        lovCode: 'AMTR.METERS',
        lovPara: {
          maintSiteIds: maintSiteIds.join(','),
        },
        dynamicProps: {
          required: ({ record }) =>
            !['ACT', 'WO_TYPE', 'CHECKLIST_GROUP', 'MAINTAIN_PLAN'].includes(compParentType) &&
            record?.get('columnTypeCode') === 'NUMBER',
        },
      },
      {
        name: 'meterId',
        type: 'number',
        bind: 'meterLov.meterId',
      },
      {
        name: 'meterName',
        label: intl.get(`${modelPrompt}.meter`).d('仪表点'),
        type: 'string',
        bind: 'meterLov.meterName',
      },
      {
        name: 'meterUomId',
        type: 'number',
      },
      {
        name: 'meterUomName',
        label: intl.get(`${modelPrompt}.meterUom`).d('仪表单位'),
        type: 'string',
      },
      {
        name: 'alarmAccordType',
        label: intl.get(`${modelPrompt}.alarmAccordType`).d('告警状态影响'),
        lookupCode: 'HALM.ALARM_ACCORD_TYPE',
        type: 'string',
      },
      {
        name: 'enableAlarmFlag', // 启用告警管理 - 控制 告警状态影响 等信息的显示
        type: 'number',
      },
      {
        name: 'meterClassCode', // 仪表点分类
        type: 'string',
      },
      {
        name: 'valueOfListValueCode',
        label: intl.get(`${modelPrompt}.valueOfListValueCode`).d('值'),
        type: 'string',
      },
      // 以下字段仅供查看
      {
        name: 'actValue',
        label: intl.get(`${modelPrompt}.actValue`).d('实际值'),
        dynamicProps: {
          type: ({ record }) => {
            const columnTypeCode = record?.get('columnTypeCode');
            // 仪表类型为：监控状态、运行状态 时为下拉框
            const isSelectMeter = ['RUN_STATUS', 'MONITOR_STATUS'].includes(
              record?.get('meterClassCode')
            );
            return columnTypeCode === 'NUMBER' && !isSelectMeter ? 'number' : 'string';
          },
          lookupCode: ({ record }) => {
            const columnTypeCode = record?.get('columnTypeCode');
            // 仪表类型为：监控状态、运行状态 时为下拉框
            const meterClassCode = record?.get('meterClassCode');
            const isSelectMeter = ['RUN_STATUS', 'MONITOR_STATUS'].includes(meterClassCode);
            const meterCodeMap = {
              RUN_STATUS: 'AMTR.METER_STATUS',
              MONITOR_STATUS: 'AMTC.READING_VALUE',
            };
            const case1 = columnTypeCode === 'NUMBER' && isSelectMeter;
            return case1 ? meterCodeMap[meterClassCode] : '';
          },
        },
      },
      {
        name: 'actValueMeaning',
        label: intl.get(`${modelPrompt}.actValue`).d('实际值'),
      },
      {
        name: 'description',
        label: intl.get(`${modelPrompt}.description`).d('记录备注'),
        type: 'string',
        maxLength: 240,
      },
    ],
    transport: {
      read: () => {
        return {
          url: queryUrl,
          method: 'GET',
        };
      },
    },
  };
}

function numberListDS(){
  return {
    autoCreate: true,
    autoLocateFirst: true,
    dataKey: 'rows',
    fields: [
      { name: 'dataValue' },
      {
        name: 'multipleValue',
        dynamicProps: {
          range: ({ record }) => {
            return record.get('valueType') === 'section' ? ['leftValue', 'rightValue'] : false;
          },
        },
      },
      {
        name: 'leftChar',
        type: 'string',
        defaultValue: '[',
      },
      {
        name: 'leftValue',
        type: 'string',
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('valueType') === 'section') {
              return 'multipleValue.leftValue';
            }
          },
        },
      },
      {
        name: 'rightChar',
        type: 'string',
        defaultValue: ']',
      },
      {
        name: 'rightValue',
        type: 'string',
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('valueType') === 'section') {
              return 'multipleValue.rightValue';
            }
          },
        },
      },
      {
        name: 'valueType',
        type: 'string',
        defaultValue: 'single',
      },
      {
        name: 'valueShow',
        type: 'string',
      },
      {
        name: 'standard',
        type: 'string',
      },
    ],
    events: {
      load: ({ dataSet }) => {
        if (!dataSet.length) {
          dataSet.loadData([{ leftChar: '(', rightChar: ')', valueType: 'single' }]);
          return;
        }
        dataSet.forEach(record => {
          if (record?.get('valueType') === 'section') {
            record?.set('multipleValue', {
              leftValue: record?.get('leftValue'),
              rightValue: record?.get('rightValue'),
            });
          } else {
            record?.set('multipleValue', record?.get('dataValue'));
          }
        });
      },
      update: ({ record, name }) => {
        switch (name) {
          case 'valueType':
          case 'leftValue':
          case 'rightValue':
          case 'leftChar':
          case 'rightChar':
          case 'multipleValue':
            handleUpdateRangeValue(record, name);
            break;
          default:
            break;
        }
      },
    },
  }
};

function handleUpdateRangeValue(record, name) {
  if (record.get('valueType') === 'section') {
    if (!record.get('leftChar')) {
      record.set('leftChar', '(');
    }
    if (!record.get('rightChar')) {
      record.set('rightChar', ')');
    }
    const regPos = /^[+-]?[0-9]+.?[0-9]*/;
    const leftValue = regPos.test(record.get('leftValue')) ? record.get('leftValue') : '-∞';
    const rightValue = regPos.test(record.get('rightValue')) ? record.get('rightValue') : '+∞';
    const leftChar = record.get('leftChar') === '(' ? '<' : '≤';
    const rightChar = record.get('rightChar') === ')' ? '<' : '≤';
    record.set('valueShow', `${leftValue}${leftChar}X${rightChar}${rightValue}`);
  } else if (name === 'valueType') {
    record.set('multipleValue', undefined);
  }
};

function optionsTableDs(isView) {
  return {
    primaryKey: 'checklistOptionId',
    selection: false,
    fields: [
      {
        name: 'description',
        label: intl.get(`${modelPrompt}.description`).d('选项描述'),
        type: 'string',
        required: true,
        maxLength: 8,
      },
      {
        name: 'exceptionFlag',
        label: intl.get(`${modelPrompt}.exceptionFlag`).d('异常标记'),
        type: 'boolean',
        trueValue: 1,
        falseValue: 0,
        computedProps: {
          disabled: () => isView,
        },
      },
      {
        name: 'alarmFlag',
        label: intl.get(`${modelPrompt}.alarmFlag`).d('是否告警'),
        type: 'boolean',
        trueValue: 1,
        falseValue: 0,
        computedProps: {
          disabled: ({ record }) => isView || !record.get('exceptionFlag'),
        },
      },
    ],
    events: {
      update: ({ name, value, record }) => {
        if (name === 'exceptionFlag' && !value) {
          record.init('alarmFlag');
        }
      },
    },
  };
}

export { detailDs, optionsTableDs, numberListDS };
