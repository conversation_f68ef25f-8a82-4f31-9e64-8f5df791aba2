import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.hlct.dataMaintenance';
const tenantId = getCurrentOrganizationId();

const tableDS = (): DataSetProps => ({
  selection: false,
  autoQuery: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'productFormCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productFormCode`).d('产品形式'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PRODUCT_FORM',
    },
    {
      name: 'teardownLocationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownLocationDesc`).d('拆解位置'),
    },

    {
      name: 'ncLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncLov`).d('缺陷编码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: FieldIgnore.always,
      textField: 'ncCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'ncCodeId',
      bind: 'ncLov.ncCodeId',
    },
  ],
  fields: [
    {
      name: 'seq',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.seq`).d('序号'),
    },
    {
      name: 'siteName',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      required: true,
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'productFormCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productFormCode`).d('产品形式'),
      required: true,
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PRODUCT_FORM',
    },
    {
      name: 'teardownLocationDescList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownLocationDescList`).d('拆解位置'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-teardown-head`,
        method: 'GET',
      };
    },
  },
});

const detailBasicDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'siteNameLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteNameLov`).d('站点'),
      required: true,
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },

    {
      name: 'siteId',
      bind: 'siteNameLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteNameLov.siteName',
    },

    {
      name: 'productFormCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productFormCode`).d('产品形式'),
      required: true,
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PRODUCT_FORM',
    },

    {
      name: 'sipFileLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sipFileLov`).d('SIP文件'),
      required: true,
      lovCode: 'YP.QMS.SIP_FILE',
      ignore: FieldIgnore.always,
      textField: 'sipCode',
      lovPara: { tenantId },
      computedProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          tabType: 'center',
        }),
        disabled: ({ record }) => !record?.get('siteId'),
      },
    },
    {
      name: 'sipId',
      bind: 'sipFileLov.sipId',
    },
    {
      name: 'sipCode',
      bind: 'sipFileLov.sipCode',
    },
  ],
});

const defectFormDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'teardownLocationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownLocationDesc`).d('拆解位置'),
      required: true,
    },
  ],
});

const defectiveContentDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'seq',
      label: intl.get(`${modelPrompt}.seq`).d('序号'),
    },
    {
      name: 'ncCodeIdLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeIdLov`).d('缺陷编码'),
      required: true,
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: FieldIgnore.always,
      textField: 'ncCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'ncCode',
      bind: 'ncCodeIdLov.ncCode',
    },
    {
      name: 'ncCodeId',
      bind: 'ncCodeIdLov.ncCodeId',
    },
    {
      name: 'description',
      label: intl.get(`${modelPrompt}.description`).d('缺陷名称'),
      bind: 'ncCodeIdLov.description',
    },
    {
      name: 'qisTeardownNcLevelList',
      label: intl.get(`${modelPrompt}.qisTeardownNcLevelList`).d('缺陷等级/缺陷评分'),
      required: true,
    },
  ],
  events: {
    update: ({ dataSet, record, name, value }) => {
      if (name === 'ncCodeIdLov') {
        const ncCodeList: any = [];
        dataSet.forEach(_record => {
          if (_record.index !== record.index) {
            ncCodeList.push(`${_record.get('ncCode')}`);
          }
        });
        if (ncCodeList.includes(`${value.ncCode}`)) {
          notification.error({
            message: intl.get(`${modelPrompt}.message.sameCode`).d(`缺陷编码重复`),
          });
          record.set('ncCodeIdLov', null);
        }
      }
    },
  },
});

const viewDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'teardownNcLevelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownNcLevelCode`).d('缺陷等级'),
      required: true,
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.TEARDOWN_NC_LEVEL',
    },
    {
      name: 'teardownNcScore',
      type: FieldType.number,
      min: 0,
      required: true,
      label: intl.get(`${modelPrompt}.teardownNcScore`).d('缺陷评分'),
      dynamicProps: {
        max: ({ record }) => {
          const teardownNcLevelCode = record.get('teardownNcLevelCode');
          if (teardownNcLevelCode === 'OTHER') {
            return 0;
          }
        },
      },
    },
  ],
  events: {
    update: ({ record }) => {
      const teardownNcLevelCode = record.get('teardownNcLevelCode');
      if (teardownNcLevelCode === 'OTHER') {
        record.set('teardownNcScore', 0);
      }
    },
  },
});

export { tableDS, detailBasicDS, defectFormDS, defectiveContentDS, viewDS };
