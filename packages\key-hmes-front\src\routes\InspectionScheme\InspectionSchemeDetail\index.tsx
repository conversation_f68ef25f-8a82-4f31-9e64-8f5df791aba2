/**
 * @Description: 检验方案维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-06-15 14:09:31
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import uuid from 'uuid/v4';
import myInstance from '@utils/myAxios';
import { isJSONString } from '@/utils';
import {
  DataSet,
  Button,
  Form,
  TextField,
  Select,
  Switch,
  Lov,
  Attachment,
  Modal,
} from 'choerodon-ui/pro';
import { Collapse, Icon, Tabs } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import {
  fetchInspectSchemeTmp,
  fetchInspectScheme,
  saveInspectScheme,
  inspectSchemePublishConfig,
  inspectSchemeChangePublishConfig,
  mtInspectItemDetailConfig,
  fetchRuleDtlConfig,
  mtInspectSchemePageUiConfig,
} from '../services';
import {
  detailFormDS,
  inspectionItemBasisDS,
  dimensionTableDS,
  copyBusinessTypeDS,
} from '../stores/InspectionSchemeDS';
import { DetailTableDS } from '../components/stores';
import DimensionTableListDrawer from './DimensionTableListDrawer';
import DimensionDetailTableList from '../components/DimensionDetailTableList/InspectItemTab';
import StatisticsTable from './StatisticsTable';
import DetailComponent from '../components/DetailComponent';
import CollapsePanelTitle from '../components/CollapsePanelTitle';
import TabsTabPaneTitle from '../components/TabsTabPaneTitle';
import AddActiveTabDimensionButton from '../components/AddActiveTabDimensionButton';

let batchAddInspectionDimensionModal;

const tenantId = getCurrentOrganizationId();


const _newIgnoreKeys = [
  'creationDate',
  'createdBy',
  'lastUpdateDate',
  'lastUpdatedBy',
  'objectVersionNumber',
  '_token',
  'inspectGroupItemId',
  'sequence',
  'inspectItemLov',
  'inspectItemId',
  'inspectItemCode',
  'inspectItemDesc',
  'enclosure',
  'inspectFrequencyDesc',
];

const modelPrompt = 'tarzan.inspectionScheme';
const { Panel } = Collapse;
const { TabPane } = Tabs;
const { Option } = Select;

const InspectionSchemeTable = props => {
  const {
    match: {
      path,
      params: { id },
    },
  } = props;

  const statisticsTableRef = useRef<any>(null);

  const childRef = useRef<any>();


  const titleMap = useMemo(() => {
    return {
      areaId: {
        valueKey: 'areaName',
        text: intl.get(`${modelPrompt}.area`).d('区域'),
      },
      prodLineId: {
        valueKey: 'prodLineName',
        text: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      },
      processWorkcellId: {
        valueKey: 'processWorkcellName',
        text: intl.get(`${modelPrompt}.process`).d('工序'),
      },
      stationWorkcellId: {
        valueKey: 'stationWorkcellName',
        text: intl.get(`${modelPrompt}.station`).d('工位'),
      },
      equipmentId: {
        valueKey: 'equipmentName',
        text: intl.get(`${modelPrompt}.equipment`).d('设备'),
      },
      operationId: {
        valueKey: 'operationName',
        text: intl.get(`${modelPrompt}.operation`).d('工艺'),
      },
      supplierId: {
        valueKey: 'supplierName',
        text: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      },
      customerId: {
        valueKey: 'customerName',
        text: intl.get(`${modelPrompt}.customer`).d('客户'),
      },
    };
  }, []);

  // 编辑状态
  const [canEdit, setCanEdit] = useState(false);

  const [saveLoading, sateSaveLoading] = useState(false);

  // 状态
  const [pageStatus, setPageStatus] = useState('');
  // 启用状态
  const [enableFlag, setEnableFlag] = useState('Y');

  const [pageIdObject, setPageIdObject] = useState({ id: null, isCopy: false, isTmp: false });

  // 生成新tabkey
  const [newTabIndex, setNewTabIndex] = useState(100);
  // 当前tabkey
  const [activeKey, setActiveKey] = useState('');
  // tab及数据列表
  const [inspectSchemeLines, setInspectSchemeLines] = useState<Array<any>>([]);

  // 维度折叠面板数据
  const [collapseActiveKeys, setCollapseActiveKeys] = useState({});

  // 获取请选择检验业务类型列表
  const fetchRuleDtl = useRequest(fetchRuleDtlConfig(), {
    manual: true,
    needPromise: true,
  });

  // 查询检验方案列表
  const mtInspectSchemePageUi = useRequest(mtInspectSchemePageUiConfig(), {
    manual: true,
    needPromise: true,
  });

  const queryDetail = useRequest(fetchInspectScheme(), {
    // 详情页数据查询
    manual: true,
  });

  const queryTmpDetail = useRequest(fetchInspectSchemeTmp(), {
    // 详情页数据查询
    manual: true,
  });

  const saveDetail = useRequest(saveInspectScheme(), {
    // 详情页数据保存
    manual: true,
    needPromise: true,
  });

  // 检验方案发布
  const inspectSchemePublish = useRequest(inspectSchemePublishConfig(), {
    manual: true,
    needPromise: true,
  });

  // 检验方案发布确认
  const inspectSchemeChangePublish = useRequest(inspectSchemeChangePublishConfig(), {
    manual: true,
    needPromise: true,
  });

  // 检验项目详情
  const mtInspectItemDetail = useRequest(mtInspectItemDetailConfig(), {
    manual: true,
    needPromise: true,
  });

  const formDs = useMemo(() => new DataSet(detailFormDS()), []);
  const dimensionTableDs = useMemo(() => new DataSet(dimensionTableDS()), []);
  const copyBusinessTypeDs = useMemo(() => new DataSet(copyBusinessTypeDS()), []);

  // 添加检验方案信息表单监听
  useEffect(() => {
    function processDataSetListener(flag) {
      const handler = flag ? formDs.addEventListener : formDs.removeEventListener;
      handler.call(formDs, 'update', handleformDsUpdate);
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  // 初始化页面
  useEffect(() => {
    let isCopy = false;
    let isTmp = false;
    let newId = id;
    if (id.indexOf('Tmp') > -1) {
      isTmp = true;
    }
    if (id.indexOf('copy') > -1) {
      isCopy = true;
      newId = id.split('copy')[0];
    }
    setPageIdObject({
      id: newId,
      isCopy,
      isTmp,
    });
  }, [id]);

  // 初始化页面
  useEffect(() => {
    if (pageIdObject.id === 'create') {
      pageReset();
      setTimeout(() => {
        setCanEdit(true);
        addTabs('new');
      }, 100);
      return;
    }
    if (pageIdObject.id && pageIdObject.id !== 'create') {
      initPageData();
    }
  }, [pageIdObject]);

  // 基础表单监听事件
  const handleformDsUpdate = ({ name, record, value }) => {
    switch (name) {
      case 'siteObject':
        record.init('inspectSchemeObject', null);
        record.init('revisionCode', null);
        handleFormDsValueChange('siteObject', value);
        handleResetDimensionsDs(false);
        break;
      case 'inspectSchemeObjectTypeObject':
        record.init('inspectSchemeObject', null);
        record.init('revisionCode', null);
        handleInspectSchemeObjectTypeObjectChange();
        handleResetDimensionsDs(false);
        break;
      case 'inspectSchemeObject':
        record.init('revisionCode', null);
        handleFormDsValueChange('materialObject', value);
        handleResetDimensionsDs(false);
        if (!(value?.revisionFlag === 'Y')) {
          queryInspectionList();
        }
        break;
      case 'revisionCode':
        handleFormDsValueChange('revisionCode', value);
        queryInspectionList();
        break;
      default:
        break;
    }
  };

  const editChange = () => {
    let hasChange = false;
    inspectSchemeLines.forEach(item => {
      const { inspectionItemBasisDs, key } = item;
      const editFlag = inspectionItemBasisDs.current.get('editFlag');
      if (editFlag === 'Y' && !hasChange) {
        setActiveKey(`${key}`);
        hasChange = true;
      }
    });
  };

  const queryInspectionList = () => {
    const formDsData: any = formDs.toData()[0];
    mtInspectSchemePageUi.run({
      params: {
        siteId: formDsData.siteId,
        inspectSchemeObjectType: formDsData.inspectSchemeObjectType,
        inspectSchemeObjectId: formDsData.inspectSchemeObjectId,
        revisionCode: formDsData.revisionCode,
        currentFlag: 'Y',
        status: 'PUBLISHED',
      },
      onSuccess: res => {
        if (res?.content?.length === 1) {
          Modal.confirm({
            title: intl.get(`tarzan.common.title.tips`).d('提示'),
            children: (
              <div>
                {intl
                  .get(`${modelPrompt}.inspectionUpdateNotiec`, {
                    siteName: formDsData.siteName,
                    inspectSchemeObjectCode: formDsData.inspectSchemeObjectCode,
                    inspectSchemeCode: res.content[0].inspectSchemeCode,
                  })
                  .d(
                    `当前站点【${formDsData.siteName}】下已有检验对象【${formDsData.inspectSchemeObjectCode}】已发布的检验方案【${res.content[0].inspectSchemeCode}】，是否升版？`,
                  )}
              </div>
            ),
            // 当前站点【】下已有检验对象【】已发布的检验方案【】，是否升版？
          }).then(button => {
            if (button === 'ok') {
              props.history.push(
                `/hwms/inspection-scheme-maintenance/detail/${res.content[0].inspectSchemeId}copy`,
              );
            } else {
              props.history.push(
                `/hwms/inspection-scheme-maintenance/detail/${res.content[0].inspectSchemeId}`,
              );
            }
          });
        }
      },
    });
  };

  const pageReset = () => {
    setActiveKey(`${newTabIndex + 1}`);
    setPageStatus('');
    setEnableFlag('Y');
    setNewTabIndex(newTabIndex + 1);
    setInspectSchemeLines([]);
    formDs.loadData([{ enableFlag: 'Y' }]);
    dimensionTableDs.loadData([]);
  };

  // 初始化页面
  const initPageData = () => {
    setCanEdit(!!pageIdObject.isCopy);
    (pageIdObject.isTmp ? queryTmpDetail : queryDetail).run({
      params: {
        [pageIdObject.isTmp ? 'inspectSchemeTmpId' : 'inspectSchemeId']: pageIdObject.id,
      },
      onSuccess: res => {
        const enclosureRecordList: Array<any> = [];
        const { inspectSchemeLines, ...other } = res;
        if (pageIdObject.isCopy && !pageIdObject.isTmp) {
          delete other.inspectSchemeId;
          delete other.inspectSchemeCode;
          other.currentFlag = 'N';
          other.status = undefined;
        }
        setPageStatus(other.status);
        setEnableFlag(other.enableFlag);
        // 检验方案信息加载
        formDs.loadData([other]);

        // 复制复制附件
        if (pageIdObject.isCopy) {
          formDs.forEach(record => {
            if (record.get('enclosure')) {
              enclosureRecordList.push(record);
            }
          });
        }
        // 初始化tab单元

        let _newTabIndex = newTabIndex;
        const _activeKey = newTabIndex + 1;
        // 保存所有tab的检验业务类型, 用做tab的检验业务类型过滤条件
        const allInspectBusinessType: Array<any> = [];

        // 保存维度折叠面板key
        const initCollapseActiveKeys: any = {};
        const newInspectSchemeLines = inspectSchemeLines.map(item => {
          // 更新tab最新key
          _newTabIndex += 1;
          const { dimensions, ...linesOther } = item;
          // tab基础信息ds
          const inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());
          inspectionItemBasisDs.loadData([linesOther]);
          if (linesOther.inspectBusinessType) {
            allInspectBusinessType.push(linesOther.inspectBusinessType);
          }

          return {
            key: `${_newTabIndex}`,
            inspectionItemBasisDs,
            // tab下维度信息读取
            dimensions: dimensions.map(dimensionsItem => {
              const { items, ...dimensionsItemother } = dimensionsItem;
              const itemsDs = new DataSet(DetailTableDS({}));
              const itemsSimple: any = [];
              const itemsFormula: any = [];
              items.forEach(itemsItem => {
                const _valueItem = { ...itemsItem };
                if (['TEXT', 'DECISION_VALUE'].includes(_valueItem.dataType)) {
                  _valueItem.trueValue =
                    (_valueItem.trueValueList || []).length > 0
                      ? _valueItem.trueValueList[0].dataValue
                      : null;
                  _valueItem.falseValue =
                    (_valueItem.falseValueList || []).length > 0
                      ? _valueItem.falseValueList[0].dataValue
                      : null;
                }
                if (_valueItem.dataType === 'VALUE_LIST') {
                  _valueItem.trueValue =
                    (_valueItem.trueValueList || []).length > 0
                      ? _valueItem.trueValueList.map(trueItem => trueItem.dataValue)
                      : null;
                  _valueItem.falseValue =
                    (_valueItem.falseValueList || []).length > 0
                      ? _valueItem.falseValueList.map(falseItem => falseItem.dataValue)
                      : null;
                }

                if (['CALCULATE_FORMULA'].includes(_valueItem.dataType)) {
                  const formula = isJSONString(_valueItem.formula || '');
                  if (formula) {
                    const {
                      formulaSourceId,
                      formulaMode,
                      formulaDisplayPosition,
                      formulaId,
                      formulaCode,
                      formulaName,
                      dimension,
                      formulaList,
                    } = formula;
                    _valueItem.formulaSourceId = formulaSourceId;
                    _valueItem.formulaMode = formulaMode;
                    _valueItem.formulaDisplayPosition = formulaDisplayPosition;
                    _valueItem.formulaId = formulaId;
                    _valueItem.formulaCode = formulaCode;
                    _valueItem.formulaName = formulaName;
                    _valueItem.dimension = dimension;
                    _valueItem.formulaList = formulaList;
                  } else {
                    _valueItem.formulaSourceId = null;
                    _valueItem.formulaMode = null;
                    _valueItem.formulaDisplayPosition = null;
                    _valueItem.formulaId = null;
                    _valueItem.formulaCode = null;
                    _valueItem.formulaName = null;
                    _valueItem.dimension = null;
                    _valueItem.formulaList = null;
                  }
                }

                if (_valueItem.dataType === 'CALCULATE_FORMULA') {
                  itemsFormula.push({
                    ..._valueItem,
                    inspectionItemRowUuid: itemsItem.inspectObjectDmsItemId,
                    samplingDimension: linesOther.samplingDimension,
                  });
                } else {
                  itemsSimple.push({
                    ..._valueItem,
                    inspectionItemRowUuid: itemsItem.inspectObjectDmsItemId,
                    samplingDimension: linesOther.samplingDimension,
                  });
                }
              });
              itemsDs.loadData([...itemsSimple, ...itemsFormula]);
              // 复制复制附件
              if (pageIdObject.isCopy) {
                itemsDs.forEach(record => {
                  if (record.get('enclosure')) {
                    enclosureRecordList.push(record);
                  }
                });
              }

              // 维度折叠面板id插入
              if (initCollapseActiveKeys[`${_newTabIndex}`]) {
                initCollapseActiveKeys[`${_newTabIndex}`].push(
                  `${dimensionsItemother.inspectObjectDimensionId ||
                    dimensionsItemother.inspectObjectDmsTmpId}`,
                );
              } else {
                initCollapseActiveKeys[`${_newTabIndex}`] = [
                  `${dimensionsItemother.inspectObjectDimensionId ||
                    dimensionsItemother.inspectObjectDmsTmpId}`,
                ];
              }

              const dimensionDetailDs = new DataSet(dimensionTableDS());
              dimensionDetailDs.loadData([
                {
                  ...dimensionsItemother,
                  uuid:
                    dimensionsItemother.inspectObjectDimensionId ||
                    dimensionsItemother.inspectObjectDmsTmpId,
                  siteId: other.siteId,
                  siteCode: other.siteCode,
                  revisionCode: other.revisionCode,
                },
              ]);
              const newDimensionsItem = {
                itemsDs,
                dimensionDetailDs,
              };
              return newDimensionsItem;
            }),
          };
        });

        setCollapseActiveKeys(initCollapseActiveKeys);
        setNewTabIndex(_newTabIndex);

        newInspectSchemeLines.forEach(item => {
          item.inspectionItemBasisDs.forEach(record => {
            record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
            record.set('inspectSchemeObjectType', other.inspectSchemeObjectType);
            record.set('inspectSchemeObjectId', other.inspectSchemeObjectId);
            record.set('siteId', other.siteId);
          });
        });

        setInspectSchemeLines(newInspectSchemeLines);

        enclosureRecordList.forEach(record => {
          getNewUuid(record);
        });

        // 设置当前tabkey
        setActiveKey(`${_activeKey}`);
      },
    });
  };

  // 校验所有ds
  const validateAllDs = async () => {
    // 不需要弹窗提示表单列表
    const validateDsListNormal: Array<any> = [];
    // 需要弹窗提示表单列表
    const validateDsListNotice: Array<any> = [];

    // 整理所有ds插入对应列表

    // 维度检验信息

    const dimensionsValidateList = [];

    // 检验方案信息不需要
    validateDsListNormal.push(formDs);
    inspectSchemeLines.forEach(tabPaneItem => {
      const { inspectionItemBasisDs, dimensions } = tabPaneItem;
      if (inspectionItemBasisDs) {
        // 检验项目tab基础信息 不需要
        validateDsListNormal.push(inspectionItemBasisDs);
      }
      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        // 保存当前维度下 非计算公式类型检验项目的 inspectItemId
        const dmsInspectItemId: any = [];
        const dmsInspectItemMap: any = {};

        if (itemsDs) {
          // 检验项目维度列表 将所有tab下的维度列表record 提取插入临时数组 并记录对应报错信息
          itemsDs.toData().forEach((tableRecord: any) => {
            if (tableRecord.inspectItemId && tableRecord.dataType !== 'CALCULATE_FORMULA') {
              dmsInspectItemId.push(tableRecord.inspectItemId);
              dmsInspectItemMap[tableRecord.inspectItemId] = tableRecord.inspectItemDesc;
            }
          });
          itemsDs.forEach(record => {
            const tableRecord = record.toData();
            if (tableRecord.dataType === 'CALCULATE_FORMULA') {
              const formulaList = tableRecord.formulaList || [];
              if (formulaList?.length > 0) {
                formulaList.forEach(formulaListItem => {
                  if (
                    formulaListItem.isRequired === 'Y' &&
                    !formulaListItem.inspectItemId &&
                    formulaListItem.fieldCode !== 'decimalNumber'
                  ) {
                    dimensionsValidateList.push(
                      // @ts-ignore
                      `${intl
                        .get(`${modelPrompt}.inspectBusinessType`)
                        .d('检验业务类型')}【${inspectionItemBasisDs.current.get(
                        'inspectBusinessTypeDesc',
                      )}】- ${intl
                        .get(`${modelPrompt}.inspectionDimension`)
                        .d('检验维度')}【${selectOptionFormat(dimensionsItem)}】- ${intl
                        .get(`${modelPrompt}.inspectionItem`)
                        .d('检验项目')}【${
                        dmsInspectItemMap[record.get('formulaSourceId')]
                      }】- ${intl.get(`${modelPrompt}.calculateFormula`).d('计算公式')}【${
                        tableRecord.formulaCode
                      }】${intl
                        .get(`${modelPrompt}.message.needBindInspectItem`)
                        .d('需要关联检验项目')}`,
                    );
                  }
                  // @ts-ignore
                  if (
                    formulaListItem.inspectItemId &&
                    // @ts-ignore
                    !dmsInspectItemId.includes(formulaListItem.inspectItemId) &&
                    formulaListItem.fieldCode !== 'decimalNumber'
                  ) {
                    dimensionsValidateList.push(
                      // @ts-ignore
                      `${intl
                        .get(`${modelPrompt}.inspectBusinessType`)
                        .d('检验业务类型')}【${inspectionItemBasisDs.current.get(
                        'inspectBusinessTypeDesc',
                      )}】- ${intl
                        .get(`${modelPrompt}.inspectionDimension`)
                        .d('检验维度')}【${selectOptionFormat(dimensionsItem)}】- ${intl
                        .get(`${modelPrompt}.inspectionItem`)
                        .d('检验项目')}【${
                        dmsInspectItemMap[record.get('formulaSourceId')]
                      }】- ${intl.get(`${modelPrompt}.calculateFormula`).d('计算公式')}【${
                        tableRecord.formulaCode
                      }】${intl
                        .get(`${modelPrompt}.message.matchBindInspectItem`)
                        .d('关联的检验项目未找到')}`,
                    );
                  }
                });
              }
            }

            validateDsListNotice.push({
              record,
              title: `${intl
                .get(`${modelPrompt}.inspectBusinessType`)
                .d('检验业务类型')}【${inspectionItemBasisDs.current.get(
                'inspectBusinessTypeDesc',
              )}】- ${intl
                .get(`${modelPrompt}.inspectionDimension`)
                .d('检验维度')}【${selectOptionFormat(dimensionsItem)}】- ${intl
                .get(`${modelPrompt}.inspectionItem`)
                .d('检验项目')}【${record.get('inspectItemDesc')}】${intl
                .get(`${modelPrompt}.submitError`)
                .d('必输字段未维护，请检查！')}`,
            });
          });
        }
      });
    });

    // 校验无需弹窗的表单
    const normalValidate = await Promise.all(
      validateDsListNormal.map(async validateDsListItem => {
        const itemValidate = await validateDsListItem.validate();
        return itemValidate;
      }),
    );

    // 校验需要弹窗的表单
    const noticeValidate = await Promise.all(
      validateDsListNotice.map(async validateDsListItem => {
        let itemValidate = await validateDsListItem.record.validate('all');
        const recordData = validateDsListItem.record.toData();
        if (recordData.dataType === 'VALUE') {
          let listLength = 0;
          if (recordData.trueValueList && recordData.trueValueList.length) {
            listLength = recordData.trueValueList.length;
          }
          if (recordData.falseValueList && recordData.falseValueList.length) {
            listLength = recordData.falseValueList.length;
          }
          if (!listLength || listLength === 0) {
            itemValidate = false;
          }
        }

        return { itemValidate, title: validateDsListItem.title };
      }),
    );

    // 汇总校验结果
    const normalResult = normalValidate.every(val => val);

    // 校验不通过的信息提取一条并提示
    let valmessage;
    const noticeResult = noticeValidate.every(val => {
      if (!val.itemValidate) {
        valmessage = val.title;
      }
      return val.itemValidate;
    });

    if (!noticeResult) {
      notification.error({
        message: `${valmessage}`,
      });
    }

    if (dimensionsValidateList.length > 0) {
      notification.error({
        message: `${dimensionsValidateList[0]}`,
      });
    }

    // 返回校验结果
    return normalResult && noticeResult && dimensionsValidateList.length === 0;
  };

  // 组合数据
  const getAllData = () => {
    const params: any = formDs.toData()[0] || {};

    if (params.siteObject) {
      delete params.siteObject;
    }
    if (params.inspectSchemeObjectTypeObject) {
      delete params.inspectSchemeObjectTypeObject;
    }
    if (params.inspectSchemeObject) {
      delete params.inspectSchemeObject;
    }

    const _inspectSchemeLines = inspectSchemeLines.map(item => {
      // 更新tab最新key
      const { inspectionItemBasisDs, dimensions, ...linesOther } = item;
      return {
        ...linesOther,
        ...inspectionItemBasisDs.toData()[0],
        // tab下维度信息读取
        dimensions: dimensions.map(dimensionsItem => {
          const { itemsDs, dimensionDetailDs } = dimensionsItem;
          return {
            ...dimensionDetailDs.toData()[0],
            items: itemsDs.toData().map(itemsDsDataItem => {
              const _itemsDsDataItem = { ...itemsDsDataItem };
              _itemsDsDataItem.trueValueList = itemsDsDataItem.trueValueList || [];
              _itemsDsDataItem.falseValueList = itemsDsDataItem.falseValueList || [];
              _itemsDsDataItem.warningValueList = itemsDsDataItem.warningValueList || [];
              return _itemsDsDataItem;
            }),
          };
        }),
      };
    });

    params.inspectSchemeLines = _inspectSchemeLines;
    return params;
  };

  // 保存
  const handleSave = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      return;
    }

    const params = getAllData();

    sateSaveLoading(true);
    await saveDetail.run({
      params,
      onSuccess: res => {
        // @ts-ignore
        notification.success();
        if (pageIdObject.id === 'create' || pageIdObject.isCopy) {
          props.history.push(`/hwms/inspection-scheme-maintenance/detail/${res}`);
        } else {
          initPageData();
        }
      },
    });
    sateSaveLoading(false);
  };

  // 保存并新建下一条
  const handleSaveAndNext = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      return;
    }

    const params = getAllData();
    sateSaveLoading(true);
    await saveDetail.run({
      params,
      onSuccess: () => {
        // @ts-ignore
        notification.success();
        if (pageIdObject.id === 'create') {
          pageReset();
          setTimeout(() => {
            setCanEdit(true);
            addTabs('new');
          }, 100);
        } else {
          props.history.push(`/hwms/inspection-scheme-maintenance/detail/create`);
        }
      },
    });
    sateSaveLoading(false);
  };

  // 另存
  const handleSaveAnother = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      return;
    }

    const enclosureRecordList: Array<any> = [];

    formDs.forEach(record => {
      if (record.get('enclosure')) {
        enclosureRecordList.push(record);
      }
    });

    inspectSchemeLines.forEach(item => {
      const { dimensions } = item;
      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        itemsDs.forEach(record => {
          if (record.get('enclosure')) {
            enclosureRecordList.push(record);
          }
        });
      });
    });

    await Promise.all(
      enclosureRecordList.map(async record => {
        await getNewUuid(record);
        return true;
      }),
    );

    const params = getAllData();
    if (params.inspectSchemeId) {
      delete params.inspectSchemeId;
    }
    if (params.inspectSchemeCode) {
      delete params.inspectSchemeCode;
    }
    sateSaveLoading(true);
    await saveDetail.run({
      params,
      onSuccess: res => {
        // @ts-ignore
        notification.success();
        props.history.push(`/hwms/inspection-scheme-maintenance/detail/${res}`);
      },
    });
    sateSaveLoading(false);
  };

  // 取消
  const handleCancel = () => {
    if (pageIdObject.id === 'create' || pageIdObject.isCopy) {
      props.history.push('/hwms/inspection-scheme-maintenance/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  // 切换tab
  const handleChangeTab = newActiveKey => {
    setActiveKey(newActiveKey);
  };

  // 增加tab
  const addTabs = value => {
    const allInspectBusinessType: Array<any> = [];
    const formDsData: any = formDs.toData()[0] || {};

    if (value !== 'new') {
      inspectSchemeLines.forEach(item => {
        item.inspectionItemBasisDs.forEach(record => {
          if (record.get('inspectBusinessType')) {
            allInspectBusinessType.push(record.get('inspectBusinessType'));
          }
        });
      });
    }

    const inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());
    inspectionItemBasisDs.loadData([
      {
        editFlag: 'Y',
        samplingDimension: 'INSPECT_ITEM_SAMPLING',
        inspectBusinessTypeObjectIgnore: allInspectBusinessType.join(','),
        inspectSchemeObjectType: formDsData.inspectSchemeObjectType,
        inspectSchemeObjectId: formDsData.inspectSchemeObjectId,
        siteId: formDsData.siteId,
      },
    ]);

    const dimensionDetailDs = new DataSet(dimensionTableDS());
    const newDsData: any = {
      uuid: uuid(),
      sequence: 10,
      siteId: formDsData.siteId,
    };

    if (['MATERIAL'].includes(formDsData.inspectSchemeObjectType)) {
      newDsData.materialId = formDsData.inspectSchemeObjectId;
      newDsData.materialCode = formDsData.inspectSchemeObjectCode;
      newDsData.materialName = formDsData.inspectSchemeObjectName;
      newDsData.revisionCode = formDsData.revisionCode;
    }

    if (['MATERIAL_CATEGORY'].includes(formDsData.inspectSchemeObjectType)) {
      newDsData.materialCategoryId = formDsData.inspectSchemeObjectId;
      newDsData.categoryCode = formDsData.inspectSchemeObjectCode;
      newDsData.materialCategoryName = formDsData.inspectSchemeObjectName;
    }

    dimensionDetailDs.loadData([newDsData]);
    const itemsDs = new DataSet(DetailTableDS({}));

    const newCollapseActiveKeys = { ...collapseActiveKeys };
    newCollapseActiveKeys[`${newTabIndex + 1}`] = [`${newDsData.uuid}`];
    setCollapseActiveKeys(newCollapseActiveKeys);

    setInspectSchemeLines([
      ...(value !== 'new' ? inspectSchemeLines : []),
      {
        key: `${newTabIndex + 1}`,
        inspectionItemBasisDs,
        dimensions: [
          {
            itemsDs,
            dimensionDetailDs,
          },
        ],
      },
    ]);

    setActiveKey(`${newTabIndex + 1}`);
    setNewTabIndex(newTabIndex + 1);
  };

  // 删除tab
  const removeTabs = targetKey => {
    let _activeIndex = 0;
    const _tabPanes = inspectSchemeLines.filter((item, index) => {
      if (item.key === targetKey) {
        _activeIndex = index;
      }
      return item.key !== targetKey;
    });
    if (_tabPanes.length === 0) {
      setActiveKey('');
    } else if (_activeIndex > _tabPanes.length - 1) {
      setActiveKey(`${_tabPanes[_tabPanes.length - 1].key}`);
    } else {
      setActiveKey(`${_tabPanes[_activeIndex].key}`);
    }
    setInspectSchemeLines(_tabPanes);
  };

  const onTabsEdit = (targetKey, action) => {
    if (action === 'add') {
      addTabs(null);
    } else if (action === 'remove') {
      removeTabs(targetKey);
    }
  };

  // 新增维护
  const addActiveTabDimensionHandleOk = () => {
    const _tabPanes: Array<any> = [];
    const dimensionTableDsData = dimensionTableDs.toData();
    const duplicateList: Array<any> = [];
    const compareList = dimensionTableDsData.map((item: any) => {
      const valueString = `materialId${item.materialId || ''}+areaId${item.areaId ||
        ''}+materialCategoryId${item.materialCategoryId || ''}+prodLineId${item.prodLineId ||
        ''}+processWorkcellId${item.processWorkcellId ||
        ''}+stationWorkcellId${item.stationWorkcellId || ''}+equipmentId${item.equipmentId ||
        ''}+operationId${item.operationId || ''}+supplierId${item.supplierId ||
        ''}+customerId${item.customerId || ''}+otherObject${item.otherObject || ''}+
      `;
      if (duplicateList.indexOf(valueString) === -1) {
        duplicateList.push(valueString);
      }
      return valueString;
    });
    if (duplicateList.length !== compareList.length) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.repeatNotice`).d('请勿输入完全相同的检验维度'),
      });
      return false;
    }

    const newCollapseActiveKeys = { ...collapseActiveKeys };
    // initCollapseActiveKeys[`${_newTabIndex}`].push(
    //   dimensionsItemother.inspectObjectDimensionId,
    // );
    //     uuid: dimensionsItemother.inspectObjectDimensionId,

    inspectSchemeLines.forEach(item => {
      if (activeKey === item.key) {
        const newDimensions: Array<any> = [];
        const _dimensions = item.dimensions;
        dimensionTableDsData.forEach((dimensionTableDsDataItem: any) => {
          const newDimensionsItem: any = {};
          _dimensions.forEach(dimensionsItem => {
            if (
              dimensionTableDsDataItem.uuid === dimensionsItem.dimensionDetailDs.current.get('uuid')
            ) {
              newDimensionsItem.itemsDs = dimensionsItem.itemsDs;
              newDimensionsItem.dimensionDetailDs = dimensionsItem.dimensionDetailDs;
              newDimensionsItem.dimensionDetailDs.loadData([dimensionTableDsDataItem]);
            }
          });
          if (!newDimensionsItem.itemsDs) {
            newDimensionsItem.itemsDs = new DataSet(DetailTableDS({}));
          }
          if (!newDimensionsItem.dimensionDetailDs) {
            const dimensionDetailDs = new DataSet(dimensionTableDS());
            dimensionDetailDs.loadData([dimensionTableDsDataItem]);
            newDimensionsItem.dimensionDetailDs = dimensionDetailDs;
            newCollapseActiveKeys[item.key].push(`${dimensionTableDsDataItem.uuid}`);
          }
          newDimensions.push(newDimensionsItem);
        });
        item.dimensions = newDimensions;
        item.inspectionItemBasisDs.forEach(record => {
          record.init('inspectionDimensionObject', null);
          record.init('inspectionItemObject', null);
        });
      }
      _tabPanes.push(item);
    });
    setCollapseActiveKeys(newCollapseActiveKeys);
    setInspectSchemeLines(_tabPanes);
    dimensionTableDs.loadData([]);
  };

  // 检验维度tab点击事件和新增检验维度
  const addActiveTabDimension = type => {
    const _dimensions: Array<any> = [];
    let inspectionItemBasisDs;

    inspectSchemeLines.forEach(item => {
      if (activeKey === item.key) {
        inspectionItemBasisDs = item.inspectionItemBasisDs;
        item.dimensions.forEach(dimensionsItem => {
          const { dimensionDetailDs } = dimensionsItem;
          _dimensions.push(dimensionDetailDs.toData()[0]);
        });
      }
    });

    dimensionTableDs.loadData(_dimensions);
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title:
        type === 'new'
          ? intl.get(`${modelPrompt}.addInspectionDimension`).d('新增检验维度')
          : intl.get(`${modelPrompt}.editInspectionDimension`).d('编辑检验维度'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      footer: (okBtn, cancelBtn) => {
        return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
      },
      onOk: addActiveTabDimensionHandleOk,
      children: (
        <DimensionTableListDrawer
          modelPrompt={modelPrompt}
          formDs={formDs}
          inspectionItemBasisDs={inspectionItemBasisDs}
          tableDs={dimensionTableDs}
          canEdit={canEdit}
          path={path}
        />
      ),
    });
  };

  const handleCopyBusinessTypeCopyDone = () => {
    const {
      originInspectBusinessType,
      targetInspectBusinessType,
      targetInspectBusinessTypeDesc,
    }: any = copyBusinessTypeDs.toData()[0];

    const allInspectBusinessType: Array<any> = [targetInspectBusinessType];

    const inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());

    const dimensions: any = [];

    const newItemDimensionsUuid: any = [];

    inspectSchemeLines.forEach(item => {
      const inspectionItemBasisDsData = item.inspectionItemBasisDs.toData()[0];
      item.inspectionItemBasisDs.forEach(record => {
        if (record.get('inspectBusinessType')) {
          allInspectBusinessType.push(record.get('inspectBusinessType'));
        }
      });

      if (inspectionItemBasisDsData.inspectBusinessType === originInspectBusinessType) {
        inspectionItemBasisDs.loadData([
          {
            ...inspectionItemBasisDsData,
            inspectBusinessTypeObject: {
              inspectBusinessType: targetInspectBusinessType,
              inspectBusinessTypeDesc: targetInspectBusinessTypeDesc,
            },
            inspectBusinessType: targetInspectBusinessType,
            inspectBusinessTypeDesc: targetInspectBusinessTypeDesc,
            inspectSchemeId: null,
            inspectSchemeLineId: null,
          },
        ]);

        if (item?.dimensions?.length > 0) {
          item.dimensions.forEach(dimensionsItem => {
            let _itemsDsData = dimensionsItem.itemsDs.toData();
            _itemsDsData = _itemsDsData.map(itemsDsItem => {
              return {
                ...itemsDsItem,
                inspectObjectDmsItemId: null,
              };
            });
            const _dimensionDetailDsData = dimensionsItem.dimensionDetailDs.toData()[0];
            const itemsDs = new DataSet(DetailTableDS({}));
            const dimensionDetailDs = new DataSet(dimensionTableDS());
            const newUuid = uuid();
            newItemDimensionsUuid.push(`${newUuid}`);
            dimensions.push({
              itemsDs: itemsDs.loadData(_itemsDsData),
              dimensionDetailDs: dimensionDetailDs.loadData([
                {
                  ..._dimensionDetailDsData,
                  uuid: newUuid,
                },
              ]),
            });
          });
        }
      }
    });

    inspectSchemeLines.forEach(item => {
      item.inspectionItemBasisDs.forEach(record => {
        record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
      });
    });

    inspectionItemBasisDs.forEach(record => {
      record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
    });

    setInspectSchemeLines([
      ...inspectSchemeLines,
      {
        key: `${newTabIndex + 1}`,
        inspectionItemBasisDs,
        dimensions,
      },
    ]);

    const newCollapseActiveKeys = { ...collapseActiveKeys };
    newCollapseActiveKeys[`${newTabIndex + 1}`] = [...newItemDimensionsUuid];
    setCollapseActiveKeys(newCollapseActiveKeys);

    setActiveKey(`${newTabIndex + 1}`);
    setNewTabIndex(newTabIndex + 1);
  };

  const handleCopyBusinessType = async () => {
    const validateRes = await copyBusinessTypeDs.validate();
    if (!validateRes) {
      return false;
    }
    const originResult = await fetchRuleDtl.run({
      params: {
        inspectBusinessType: copyBusinessTypeDs.current?.get('originInspectBusinessType'),
        siteId: copyBusinessTypeDs.current?.get('siteId'),
      },
    });
    const targetResult = await fetchRuleDtl.run({
      params: {
        inspectBusinessType: copyBusinessTypeDs.current?.get('targetInspectBusinessType'),
        siteId: copyBusinessTypeDs.current?.get('siteId'),
      },
    });

    if (typeof originResult?.rows === 'object' && typeof targetResult?.rows === 'object') {
      const originResultJsonString = JSON.stringify(originResult);
      const targetResultJsonString = JSON.stringify(targetResult);
      if (originResultJsonString === targetResultJsonString) {
        handleCopyBusinessTypeCopyDone();
        return true;
      }
      notification.error({
        message: intl
          .get(`${modelPrompt}.copyBusinessTypeError`)
          .d('来源和目标的检验维度不一致，不能复制！'),
      });
      return false;
    }
  };

  const copyBusinessType = () => {
    const originInspectBusinessTypes: any = [];
    inspectSchemeLines.forEach(tabItem => {
      const {
        inspectBusinessType,
        inspectBusinessTypeDesc,
        editFlag,
      } = tabItem.inspectionItemBasisDs.toData()[0];
      if (inspectBusinessType && inspectBusinessTypeDesc && editFlag === 'Y') {
        // @ts-ignore
        originInspectBusinessTypes.push({
          inspectBusinessType,
          inspectBusinessTypeDesc,
        });
      }

      copyBusinessTypeDs.loadData([
        {
          siteId: formDs.current?.get('siteId'),
          inspectBusinessTypes: originInspectBusinessTypes.map(item => {
            return item.inspectBusinessType;
          }),
        },
      ]);
    });

    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.copyBusinessType`).d('复制检验业务类型'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: handleCopyBusinessType,
      children: (
        <Form dataSet={copyBusinessTypeDs} columns={1} labelWidth={112} disabled={!canEdit}>
          <Select name="originInspectBusinessTypeObject" colSpan={2}>
            {originInspectBusinessTypes.map(originInspectBusinessType => (
              <Option value={originInspectBusinessType.inspectBusinessType}>
                {originInspectBusinessType.inspectBusinessTypeDesc}
              </Option>
            ))}
          </Select>
          <Lov autoSelectSingle={false} noCache name="targetInspectBusinessTypeObject" />
        </Form>
      ),
    });
  };

  const batchAddInspectionDimension = () => {
    inspectSchemeLines.forEach(item => {
      if (activeKey === item.key) {
        item.inspectionItemBasisDs.current.set('inspectionDimensionObject', []);
        batchAddInspectionDimensionModal = Modal.open({
          key: Modal.key(),
          title: intl.get(`${modelPrompt}.batchAddInspectionDimension`).d('批量新增检验项目'),
          destroyOnClose: true,
          style: {
            width: 360,
          },
          okCancel: false,
          okText: intl.get(`${modelPrompt}.close`).d('关闭'),
          children: (
            <Form
              dataSet={item.inspectionItemBasisDs}
              columns={1}
              labelWidth={112}
              disabled={!canEdit}
            >
              <Select name="inspectionDimensionObject" colSpan={2}>
                {item.dimensions &&
                  item.dimensions.map(dimensionsItem => (
                    <Option value={selectOptionValue(dimensionsItem)}>
                      {selectOptionFormat(dimensionsItem)}
                    </Option>
                  ))}
              </Select>
              <Lov
                autoSelectSingle={false}
                noCache
                name="inspectionItemObject"
                onChange={value => {
                  handleInspectionItemObjectChange(value, item, false);
                }}
                modalProps={{
                  footer: (okBtn, cancelBtn, modal) => {
                    return [
                      <Button
                        onClick={() => {
                          handleCreateInspection(modal, item);
                        }}
                      >
                        {intl.get(`${modelPrompt}.inspectSchemeObjectCreate`).d('新建检验项目')}
                      </Button>,
                      cancelBtn,
                      okBtn,
                    ];
                  },
                }}
                tableProps={{
                  queryFieldsLimit: 10,
                }}
              />
            </Form>
          ),
        });
      }
    });
  };

  // 检验方案信息变更事件
  const handleFormDsValueChange = (name, value) => {
    const formDsData: any = formDs.toData()[0] || {};
    const _tabPanes: any = [];
    inspectSchemeLines.forEach(item => {
      if (item.dimensions) {
        item.dimensions.forEach(dimensionsItem => {
          const { dimensionDetailDs } = dimensionsItem;
          dimensionDetailDs.forEach(record => {
            if (name === 'siteObject') {
              record.set('siteId', value?.siteId || null);
              record.set('materialCode', null);
              record.set('materialName', null);
              record.set('materialId', null);
              record.set('revisionCode', null);
              record.set('areaObject', null);
              record.set('prodLineObject', null);
              record.set('processObject', null);
              record.set('stationObject', null);
              record.set('equipmentObject', null);
              record.set('operationObject', null);
              record.set('supplierObject', null);
              record.set('customerObject', null);
              record.set('otherObject', null);
            }
            if (name === 'materialObject') {
              record.set('materialCode', null);
              record.set('materialName', null);
              record.set('materialId', null);
              record.set('revisionCode', null);
              record.set('areaObject', null);
              record.set('prodLineObject', null);
              record.set('processObject', null);
              record.set('stationObject', null);
              record.set('equipmentObject', null);
              record.set('operationObject', null);
              record.set('supplierObject', null);
              record.set('customerObject', null);
              record.set('otherObject', null);
              record.set('categoryCode', null);
              record.set('materialCategoryId', null);
              record.set('materialCategoryName', null);
              if (formDsData.inspectSchemeObjectType === 'MATERIAL') {
                record.set('materialId', formDsData.inspectSchemeObjectId || null);
                record.set('materialCode', formDsData.inspectSchemeObjectCode || null);
                record.set('materialName', formDsData.inspectSchemeObjectName || null);
                record.set('revisionCode', null);
              }
              if (formDsData.inspectSchemeObjectType === 'MATERIAL_CATEGORY') {
                record.set('categoryCode', formDsData.inspectSchemeObjectId || null);
                record.set('materialCategoryId', formDsData.inspectSchemeObjectId || null);
                record.set('materialCategoryName', formDsData.inspectSchemeObjectName || null);
              }
            }

            if (name === 'revisionCode') {
              record.set('revisionCode', value || null);
            }
          });
        });
      }
      if (name === 'siteObject') {
        item.dimensions = [item.dimensions[0]];
        item.inspectionItemBasisDs.forEach(record => {
          record.init('inspectBusinessTypeObject', null);
          record.init('siteId', value?.siteId || null);
          record.init('inspectBusinessTypeObjectIgnore', '');
        });
      }
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 检验对象类型修改触发
  const handleInspectSchemeObjectTypeObjectChange = () => {
    const formDsData: any = formDs.toData()[0] || {};
    const _tabPanes: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      const _dimensions: Array<any> = [];

      if (item.dimensions && item.dimensions.length > 0) {
        const { uuid: oldUuid } = item.dimensions[0].dimensionDetailDs.toData()[0];
        const dimensionDetailDs = new DataSet(dimensionTableDS());
        const newDsData: any = {
          uuid: oldUuid,
          sequence: 10,
          siteId: formDsData.siteId,
        };

        if (formDsData.inspectSchemeObjectType === 'MATERIAL') {
          newDsData.materialId = formDsData.inspectSchemeObjectId;
          newDsData.materialCode = formDsData.inspectSchemeObjectCode;
          newDsData.materialName = formDsData.inspectSchemeObjectName;
          newDsData.revisionCode = formDsData.revisionCode;
        }
        if (formDsData.inspectSchemeObjectType === 'MATERIAL_CATEGORY') {
          newDsData.materialCategoryId = formDsData.inspectSchemeObjectId;
          newDsData.categoryCode = formDsData.inspectSchemeObjectCode;
          newDsData.materialCategoryName = formDsData.inspectSchemeObjectName;
        }

        dimensionDetailDs.loadData([newDsData]);
        const _item = {
          itemsDs: item.dimensions[0].itemsDs,
          dimensionDetailDs,
        };
        _dimensions.push(_item);
      }
      item.dimensions = _dimensions;
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 重置 tab下Dimensions里的inspectionItemBasisDs数据
  const handleResetDimensionsDs = keepModal => {
    const _tabPanes: Array<any> = [];
    const formDsData: any = formDs.toData()[0];
    inspectSchemeLines.forEach(item => {
      if (item.inspectionItemBasisDs) {
        item.inspectionItemBasisDs.forEach(record => {
          if (!keepModal) {
            record.init('inspectionDimensionObject', null);
          }
          record.init('inspectionItemObject', null);
          record.init('inspectSchemeObjectType', formDsData.inspectSchemeObjectType);
          record.init('inspectSchemeObjectId', formDsData.inspectSchemeObjectId);
        });
      }
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 维度排序
  const sortList = (type, index) => {
    const _tabPanes: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      if (item.key === activeKey) {
        const _dimensions = item.dimensions;
        if (type === 'up' && index > 0) {
          const spliceItem = _dimensions.splice(index, 1);
          _dimensions.splice(index - 1, 0, spliceItem[0]);
        }
        if (type === 'down' && index < _dimensions.length) {
          const spliceItem = _dimensions.splice(index, 1);
          _dimensions.splice(index + 1, 0, spliceItem[0]);
        }
        _dimensions.forEach((dimensionsItem, dimensionsItemIndex) => {
          const { dimensionDetailDs } = dimensionsItem;
          dimensionDetailDs.forEach(record => {
            record.set('sequence', dimensionsItemIndex * 10 + 10);
          });
        });
        item.dimensions = _dimensions;
        _tabPanes.push(item);
        item.inspectionItemBasisDs.forEach(record => {
          record.init('inspectionDimensionObject', null);
          record.init('inspectionItemObject', null);
        });
      } else {
        _tabPanes.push(item);
      }
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 附件配置
  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-item-maintain',
    accept: [
      '.doc',
      '.ppt',
      '.docx',
      '.xlsx',
      '.xls',
      '.deb',
      '.txt',
      '.pdf',
      'image/*',
      'video/*',
    ],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  // 检验维度值
  const selectOptionValue = dimensionsItem => {
    const { dimensionDetailDs } = dimensionsItem;
    const dimensionsItemData = dimensionDetailDs.toData()[0] || {};
    return dimensionsItemData.sequence;
  };

  // 检验维度和维度tabtitle格式化
  const selectOptionFormat = dimensionsItem => {
    const { dimensionDetailDs } = dimensionsItem;
    const dimensionsItemData = dimensionDetailDs.toData()[0] || {};
    const inspectSchemeObjectTypeDesc =
      formDs.current?.get('inspectSchemeObjectTypeDesc') ||
      intl.get(`${modelPrompt}.inspectSchemeObjectType`).d('检验对象类型');

    const inspectSchemeObjectType = formDs.current?.get('inspectSchemeObjectType');
    const inspectSchemeObjectName = formDs.current?.get('inspectSchemeObjectName') || '';
    const inspectSchemeObjectRevision = formDs.current?.get('revisionCode') || '';

    let frontText = '';
    const titleMapKeys = Object.keys(titleMap);
    titleMapKeys.forEach(key => {
      if (dimensionsItemData[key]) {
        frontText += ` + ${titleMap[key].text}${dimensionsItemData[titleMap[key].valueKey]}`;
      }
    });

    if (
      ['MATERIAL_CATEGORY', 'MATERIAL'].includes(inspectSchemeObjectType) &&
      inspectSchemeObjectName
    ) {
      return `${inspectSchemeObjectTypeDesc}${inspectSchemeObjectName}${inspectSchemeObjectRevision}${frontText}`;
    }
    return `${inspectSchemeObjectTypeDesc}${inspectSchemeObjectName}`;
  };

  // 新建检验维度选中值后触发
  const handleInspectionItemObjectChange = (value, tabItem, keepModal) => {
    if (!value) {
      return;
    }

    tabItem.dimensions.forEach(dimensionsItem => {
      const { dimensionDetailDs } = dimensionsItem;
      const dimensionsItemData = dimensionDetailDs.toData()[0] || {};

      const inspectionItemBasisDsData = tabItem.inspectionItemBasisDs.toData()[0];
      if (inspectionItemBasisDsData.inspectionDimension.indexOf(dimensionsItemData.sequence) > -1) {
        const { itemsDs } = dimensionsItem;

        // 已有数据转为对象进行对比
        const inDsCodeMap: Array<any> = [];
        let sequence = 1;
        itemsDs.forEach(recordItem => {
          const recordItemData = recordItem.toData();
          inDsCodeMap.push(recordItemData.inspectItemCode);
          if (recordItemData?.sequence > sequence) {
            sequence = recordItemData.sequence;
          }
        });
        sequence = (Math.floor(sequence / 10) + 1) * 10;
        // 取得的value值还需要再处理
        value.forEach(valueItem => {
          const _valueItem = { ...valueItem };

          _valueItem.requiredFlag = _valueItem.requiredFlag || 'Y';
          _valueItem.destructiveExperimentFlag = _valueItem.destructiveExperimentFlag || 'N';
          _valueItem.outsourceFlag = _valueItem.outsourceFlag || 'N';

          if (['TEXT', 'DECISION_VALUE'].includes(_valueItem.dataType)) {
            _valueItem.trueValue =
              (_valueItem.trueValueList || []).length > 0
                ? _valueItem.trueValueList[0].dataValue
                : null;
            _valueItem.falseValue =
              (_valueItem.falseValueList || []).length > 0
                ? _valueItem.falseValueList[0].dataValue
                : null;
          }
          if (_valueItem.dataType === 'VALUE_LIST') {
            _valueItem.trueValue =
              (_valueItem.trueValueList || []).length > 0
                ? _valueItem.trueValueList.map(trueItem => trueItem.dataValue)
                : null;
            _valueItem.falseValue =
              (_valueItem.falseValueList || []).length > 0
                ? _valueItem.falseValueList.map(falseItem => falseItem.dataValue)
                : null;
          }
          if (inDsCodeMap.indexOf(valueItem.inspectItemCode) === -1) {
            const newRecord = itemsDs.create({
              ..._valueItem,
              inspectionItemRowUuid: uuid(),
              sequence,
            });
            if (!newRecord.get('samplingMethodId')) {
              newRecord.init('samplingMethodCode', inspectionItemBasisDsData.samplingMethodCode);
              newRecord.init('samplingMethodDesc', inspectionItemBasisDsData.samplingMethodDesc);
              newRecord.init('samplingMethodId', inspectionItemBasisDsData.samplingMethodId);
            }
            newRecord.init('samplingDimension', inspectionItemBasisDsData.samplingDimension);

            sequence += 10;
            getNewUuid(newRecord);
          }
        });
      }
      // batchAddInspectionDimensionModal.close();
    });
    handleResetDimensionsDs(keepModal);
    // @ts-ignore
    notification.success();
    upDateStatisticsTableRef();
  };

  // tab下检验业务类型修改
  const handleInspectBusinessTypeObjectChange = key => {
    const allInspectBusinessType: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      item.inspectionItemBasisDs.forEach(record => {
        if (record.get('inspectBusinessType')) {
          allInspectBusinessType.push(record.get('inspectBusinessType'));
        }
      });
    });

    const _tabPanes: any = [];

    inspectSchemeLines.forEach(item => {
      if (item.key === key) {
        if (item.dimensions) {
          item.dimensions.forEach(dimensionsItem => {
            const { dimensionDetailDs } = dimensionsItem;
            dimensionDetailDs.forEach(record => {
              record.set('areaObject', null);
              record.set('prodLineObject', null);
              record.set('processObject', null);
              record.set('stationObject', null);
              record.set('equipmentObject', null);
              record.set('operationObject', null);
              record.set('supplierObject', null);
              record.set('customerObject', null);
              record.set('otherObject', null);
            });
          });
        }
        item.inspectionItemBasisDs.current.set(
          'inspectBusinessTypeObjectIgnore',
          allInspectBusinessType,
        );
        item.inspectionItemBasisDs.forEach(record => {
          record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
        });
        item.dimensions = [item.dimensions[0]];
      }
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 抽样方式变更
  const handleSamplingMethodLovChange = (value, dimensions) => {
    dimensions.forEach(dimensionsItem => {
      const { itemsDs } = dimensionsItem;
      itemsDs.forEach(record => {
        record.set('samplingMethodLov', value);
      });
    });
  };

  // 抽样维度变更
  const handleSamplingDimensionChange = (value, changeDs, dimensions) => {
    changeDs.forEach(record => {
      record.set('samplingMethodLov', null);
    });
    dimensions.forEach(dimensionsItem => {
      const { itemsDs } = dimensionsItem;
      itemsDs.forEach(record => {
        record.set('samplingDimension', value);
        record.set('samplingMethodLov', null);
      });
    });
  };

  // 分配检验维度后复制附件并获得uuid
  const getNewUuid = async record => {
    const enclosure = record.get('enclosure');
    if (!enclosure) {
      return Promise.resolve(true);
    }
    await myInstance
      .post(`hfle/v1/${tenantId}/files/copy-file`, { uuidList: [enclosure] })
      .then(res => {
        if (res && res.data && res.data[enclosure]) {
          record.init('enclosure', res.data[enclosure]);
        }
      });
    return Promise.resolve(true);
  };

  const getInspectItemDetail = async (id, item, details) => {
    const inspectItemDetail = await mtInspectItemDetail.run({
      params: { inspectItemId: id },
    });
    if (inspectItemDetail?.success && inspectItemDetail?.rows) {
      handleInspectionItemObjectChange(
        [
          {
            ...inspectItemDetail.rows,
            defaultValue: details.defaultValue,
            dataQtyDisposition: details.dataQtyDisposition,
          },
        ],
        item,
        'keep',
      );
    }
  };

  // 新建检验项目点击确定的回调函数
  const handleCreateInspectionCallback = async item => {
    const { success, rows, details } = await childRef.current?.submit(false);
    if (success) {
      getInspectItemDetail(rows, item, details);
      if (batchAddInspectionDimensionModal) {
        batchAddInspectionDimensionModal.close();
      }
      return Promise.resolve(true);
    }
    return Promise.resolve(false);
  };

  // 新建检验项目点击确定的回调函数
  const handleCreateInspectionCallbackKeep = async item => {
    const { success, rows, details } = await childRef.current?.submit(true);
    if (success) {
      getInspectItemDetail(rows, item, details);
      return Promise.resolve(false);
    }
    return Promise.resolve(false);
  };

  const handleCreateInspection = (_modal, item) => {
    _modal.close();
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.inspectSchemeObjectCreate`).d('新建检验项目'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      onOk: () => {
        return handleCreateInspectionCallback(item);
      },
      footer: (okBtn, cancelBtn) => {
        return [
          cancelBtn,
          <Button
            onClick={() => {
              return handleCreateInspectionCallbackKeep(item);
            }}
          >
            {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并新建下一条')}
          </Button>,
          okBtn,
        ];
      },
      children: (
        <DetailComponent
          ref={childRef}
          canEdit={canEdit}
          kid="create"
          column={3}
          requiredField={['enterMethod', 'dataQty', 'samplingMethodLov']}
          validateType="InspectionScheme"
          operationType="SCHEME_FUNC_CREATE"
          needDefault
        />
      ),
    });
  };

  // 折叠面板样式i
  const expandIconButton = panelProps => {
    if (panelProps.isActive) {
      return <Icon type="expand_less" />;
    }
    return <Icon type="expand_more" />;
  };

  const upDateStatisticsTableRef = () => {
    if (statisticsTableRef.current) {
      statisticsTableRef.current.updateTable();
    }
  };

  const coverInspectSchemeLines = newData => {
    const inspectItemDrawerDsDataMap: any = {};
    newData.forEach((item: any) => {
      const { inspectionItemRowUuid } = item;
      inspectItemDrawerDsDataMap[inspectionItemRowUuid] = { ...item };
    });

    inspectSchemeLines.forEach(tabPaneItem => {
      const { dimensions } = tabPaneItem;
      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        if (itemsDs) {
          // 检验项目维度列表 将所有tab下的维度列表record 提取插入临时数组 并记录对应报错信息
          itemsDs.forEach(record => {
            const inspectionItemRowUuid = record.get('inspectionItemRowUuid');
            if (inspectItemDrawerDsDataMap[inspectionItemRowUuid]) {
              const newData = inspectItemDrawerDsDataMap[inspectionItemRowUuid];
              const keys = Object.keys(newData) || [];
              keys.forEach(key => {
                if (_newIgnoreKeys.indexOf(key) === -1) {
                  record.set(key, newData[key]);
                }
              });
            }
          });
        }
      });
    });
  };

  // handleChangeCollapse
  const handleChangeCollapse = (value, key) => {
    const newCollapseActiveKeys = { ...collapseActiveKeys };
    if (newCollapseActiveKeys[key]) {
      newCollapseActiveKeys[key] = value;
    }
    setCollapseActiveKeys(newCollapseActiveKeys);
  };

  // 维度展开key
  const getCollapseActiveKey = key => {
    return collapseActiveKeys[key];
  };

  // 当前维度key
  const getPanelActiveKey = dimensionsItem => {
    let key = '';
    if (dimensionsItem?.dimensionDetailDs) {
      dimensionsItem?.dimensionDetailDs.forEach(record => {
        if (record.get('uuid')) {
          key = `${record.get('uuid')}`;
        }
      });
    }
    return key;
  };

  // 发布
  const handleRelease = async () => {
    const inspectSchemePublishRes = await inspectSchemePublish.run({
      params: [formDs.current?.get('inspectSchemeId')],
      onFailed: () => {
        return Promise.resolve(null);
      },
    });
    if (inspectSchemePublishRes?.success) {
      if (inspectSchemePublishRes?.rows?.message) {
        return Modal.confirm({
          title: intl.get(`tarzan.common.title.tips`).d('提示'),
          children: <div>{inspectSchemePublishRes?.rows?.message}</div>,
        }).then(button => {
          if (button === 'ok') {
            return inspectSchemeChangePublish.run({
              params: {
                inspectSchemeInfo: inspectSchemePublishRes?.rows?.inspectSchemeInfo,
              },
              onSuccess: () => {
                // @ts-ignore
                notification.success();
                initPageData();
              },
            });
          }
          return Promise.resolve(null);
        });
      }
      // @ts-ignore
      notification.success();
      initPageData();
      return Promise.resolve(null);
    }
    return Promise.resolve(null);
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.InspectionSchemeMaintenance`).d('检验方案维护')}
        backPath="/hwms/inspection-scheme-maintenance/list"
      >
        {canEdit && (
          <>
            <Button
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              loading={saveLoading}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button onClick={handleSaveAndNext} loading={saveLoading}>
              {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并新建下一条')}
            </Button>
            <Button
              loading={saveLoading}
              disabled={pageIdObject.id === 'create' || pageIdObject.isCopy}
              onClick={handleSaveAnother}
            >
              {intl.get(`${modelPrompt}.button.saveAs`).d('另存为')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
              editChange();
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `dist.button.release`,
              type: 'button',
              meaning: '详情页-发布按钮',
            },
          ]}
          icon="send-o"
          disabled={canEdit || pageStatus !== 'UNPUBLISHED' || enableFlag !== 'Y'}
          onClick={handleRelease}
        >
          {intl.get(`${modelPrompt}.button.release`).d('发布')}
        </PermissionButton>
      </Header>
      <Content>
        <Collapse collapsible="icon" bordered={false} defaultActiveKey={['panel1', 'panel2']}>
          <Panel
            header={intl.get(`${modelPrompt}.inspectSchemeInformationTab`).d('检验方案信息')}
            key="panel1"
          >
            <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
              <TextField name="inspectSchemeCode" />
              <Lov name="siteObject" />
              <TextField name="remark" />
              <Select name="inspectSchemeObjectTypeObject" />
              <Lov name="inspectSchemeObject" />
              <TextField name="inspectSchemeObjectCode" />
              <Select name="revisionCode" />
              <TextField name="documentNum" />
              <TextField name="documentRevision" />
              <TextField name="controlledNum" />
              <Switch name="enableFlag" />
              <Switch name="currentFlag" />
              <Select name="status" />
              <Attachment {...attachmentProps} />
              <Select name="schemeCategory" />
            </Form>
          </Panel>
          <Panel
            header={intl
              .get(`${modelPrompt}.inspectSchemeProjectDistributionTab`)
              .d('检验项目分配')}
            key="panel2"
          >
            <Tabs
              onChange={handleChangeTab}
              activeKey={activeKey}
              // @ts-ignore
              type={canEdit ? 'editable-card' : 'card'}
              onEdit={onTabsEdit}
              tabBarExtraContent={
                <AddActiveTabDimensionButton
                  addInspectionDimension={addActiveTabDimension}
                  batchAddInspectionDimension={batchAddInspectionDimension}
                  copyBusinessType={copyBusinessType}
                  activeKey={activeKey}
                  canEdit={canEdit}
                  ds={formDs}
                  inspectSchemeLines={inspectSchemeLines}
                />
              }
            >
              {inspectSchemeLines.map(item => {
                if (canEdit && item.inspectionItemBasisDs.current.get('editFlag') !== 'Y') {
                  return null;
                }
                return (
                  <TabPane
                    tab={<TabsTabPaneTitle ds={item.inspectionItemBasisDs} />}
                    key={item.key}
                    forceRender
                  >
                    <>
                      <Form
                        dataSet={item.inspectionItemBasisDs}
                        columns={3}
                        labelWidth={112}
                        disabled={!canEdit}
                      >
                        <Lov
                          autoSelectSingle={false}
                          name="inspectBusinessTypeObject"
                          onChange={() => {
                            handleInspectBusinessTypeObjectChange(item.key);
                          }}
                        />
                        <Select
                          name="samplingDimension"
                          onChange={value => {
                            handleSamplingDimensionChange(
                              value,
                              item.inspectionItemBasisDs,
                              item.dimensions,
                            );
                          }}
                        />
                        <Lov
                          name="samplingMethodLov"
                          onChange={value => {
                            handleSamplingMethodLovChange(value, item.dimensions);
                          }}
                        />
                      </Form>
                      {item.dimensions && (
                        <div
                          style={{
                            paddingLeft: '12px',
                          }}
                        >
                          <Collapse
                            collapsible="icon"
                            expandIcon={expandIconButton}
                            activeKey={getCollapseActiveKey(item.key)}
                            onChange={value => {
                              handleChangeCollapse(value, item.key);
                            }}
                          >
                            {(item.dimensions || []).map((dimensionsItem, dimensionsItemIndex) => {
                              return (
                                <Panel
                                  header={
                                    <CollapsePanelTitle
                                      ds={formDs}
                                      itemDs={item.inspectionItemBasisDs}
                                      index={dimensionsItemIndex}
                                      sortList={sortList}
                                      title={selectOptionFormat(dimensionsItem)}
                                      clickTitleCallback={addActiveTabDimension}
                                      activeKey={activeKey}
                                      canEdit={canEdit}
                                    />
                                  }
                                  key={getPanelActiveKey(dimensionsItem)}
                                  dataSet={dimensionsItem.itemsDs}
                                >
                                  <DimensionDetailTableList
                                    getNewUuid={getNewUuid}
                                    formDs={item.inspectionItemBasisDs}
                                    tableDS={dimensionsItem.itemsDs}
                                    path={path}
                                    canEdit={canEdit}
                                    statisticsTableUpdate={upDateStatisticsTableRef}
                                    item={item}
                                    dimensionsItem={dimensionsItem}
                                    selectOptionValue={selectOptionValue}
                                    selectOptionFormat={selectOptionFormat}
                                    handleCreateInspection={handleCreateInspection}
                                  />
                                </Panel>
                              );
                            })}
                          </Collapse>
                        </div>
                      )}
                    </>
                  </TabPane>
                );
              })}
            </Tabs>
          </Panel>
          <Panel
            header={intl
              .get(`${modelPrompt}.inspectSchemeProjectDistributionAllTab`)
              .d('检验项目分配总览')}
            key="panel3"
          >
            <StatisticsTable
              inspectSchemeLines={inspectSchemeLines}
              selectOptionFormat={selectOptionFormat}
              canEdit={canEdit}
              ref={statisticsTableRef}
              coverInspectSchemeLines={coverInspectSchemeLines}
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [
    modelPrompt,
    'tarzan.qms.inspectGroupMaintenance',
    'tarzan.inspectionScheme',
    'tarzan.hwms.inspectItemMaintain',
    'tarzan.common',
    'tarzan.qms.inspectGroupMaintenance.model',
  ],
})(InspectionSchemeTable);
