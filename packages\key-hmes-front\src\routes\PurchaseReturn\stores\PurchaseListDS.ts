/**
 * @Description: 采购退货平台
 * @Author: <<EMAIL>>
 * @Date: 2021-12-21 14:14:48
 * @LastEditTime: 2023-05-18 15:23:35
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.purchase.purchaseReturn';

const tenantId = getCurrentOrganizationId();

const PurchaseListDS = (): DataSetProps => ({
  autoQuery: false,
  pageSize: 10,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-instruction-doc/purchase-return/page/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.HEAD`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'instructionDocId',
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('采购退货单号'),
    },
    {
      name: 'instructionDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('状态'),
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('退货类型'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteName`).d('供应商地点'),
    },
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
    },
    {
      name: 'manualPoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manualPoNumber`).d('采购订单号(手动)'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'printTimes',
      type: FieldType.string,
      label: intl.get(`tarzan.common.printTimes`).d('打印次数'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.realName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
  queryFields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('采购退货单号'),
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_RETURN`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteIdLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.parentLocatorLov`).d('站点'),
      lovCode: 'APEX_WMS.MODEL.SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            siteType: 'MANUFACTURING',
          };
        },
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteIdLov.siteId',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierLov`).d('供应商'),
      lovCode: 'APEX_WMS.MODEL.SUPPLIER',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
      },
    },
    {
      name: 'supplierId',
      type: FieldType.number,
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierSiteName`).d('供应商地点'),
      lovCode: 'APEX_WMS.MODEL.SUPPLIER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
        disabled: ({ record }) => {
          return !record.get('supplierId');
        },
      },
    },
    {
      name: 'supplierSiteId',
      type: FieldType.number,
      bind: 'supplierSiteLov.supplierSiteId',
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('退货类型'),
      lookupCode: 'APEX_WMS.INSTRUCTION_DOC_RETURN_TYPE',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
      lovCode: 'APEX_WMS.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'poHeaderLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.poHeaderId`).d('采购订单号'),
      lovCode: 'APEX_WMS.PO',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'poHeaderId',
      type: FieldType.number,
      bind: 'poHeaderLov.poHeaderId',
    },
    /* {
      name: 'parentLocatorName',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.parentLocatorLov`).d('退货时间从'),
    },
    {
      name: 'parentLocatorName',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.parentLocatorLov`).d('退货时间至'),
    }, */
    {
      name: 'printFlag',
      type: FieldType.string,
      lookupCode: 'APEX_WMS.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`tarzan.common.printFlag`).d('打印标识'),
    },
  ],
});

const LineDs = (): DataSetProps => ({
  autoQuery: false,
  pageSize: 10,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-instruction/purchase-return-line/page/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.LINE`,
        method: 'GET',
      };
    },
  },
  primaryKey: 'instructionId',
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'identifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
      lookupCode: 'APEX_WMS.APS.GEN_TYPE_URL',
      lovPara: {
        typeGroup: 'IDENTITY_TYPE',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      valueField: 'typecode',
      textField: 'description',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialBomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBomCode`).d('BOM号'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
    },
    {
      name: 'materialBrand',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBrand`).d('品牌'),
    },
    {
      name: 'instructionStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionStatus`).d('状态'),
    },
    {
      name: 'deliveryDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.deliveryDocNum`).d('送货单号'),
    },
    {
      name: 'deliveryDocLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.deliveryDocLineNum`).d('送货单行号'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('退货数量'),
    },
    {
      name: 'actualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualQty`).d('执行数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'fromLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromLocatorCode`).d('退货库位'),
    },
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
    },
    {
      name: 'manualPoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manualPoNumber`).d('采购订单号(手动)'),
    },
    {
      name: 'lineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNum`).d('采购订单行号'),
    },
    {
      name: 'soNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNum`).d('销售订单号'),
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qmsNumber`).d('检验单号'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

const DetailDs = (): DataSetProps => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-instruction/purchase-return-line/material-lot/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.LINE_DETAIL`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'identification',
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantityStatus`).d('质量状态'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'actualQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('条码状态'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('退货库位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateBy`).d('更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('更新时间'),
    },
  ],
});

export { PurchaseListDS, LineDs, DetailDs };
