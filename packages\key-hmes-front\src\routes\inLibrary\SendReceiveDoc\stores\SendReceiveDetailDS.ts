/**
 * @Description: 库存调拨平台 - 详情页面DS
 * @Author: <EMAIL>
 * @Date: 2022/2/10 9:15
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@/utils/config';
import request from 'utils/request';
import uuid from 'uuid/v4';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import { isNull, isUndefined } from 'lodash';

const modelPrompt = 'tarzan.inLibrary.sendReceiveDoc';
const tenantId = getCurrentOrganizationId();
const url = `${BASIC.HWMS_BASIC}/v1/${tenantId}/inventory-send-receive/identify/get/for/ui`;

// 允差类型下拉框数据源
const toleranceTypeOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/limit-group/type`,
        method: 'POST',
        data: { typeGroup: 'INSTRUCTION_TOLERANCE_TYPE', module: 'GENERAL', tenantId },
      };
    },
  },
});

const detailHeaderDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows',
  paging: false,
  cacheSelection: true,
  fields: [
    {
      name: 'instructionDocId',
      type: FieldType.number,
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单号'),
    },
    {
      name: 'instructionType',
      type: FieldType.boolean,
    },
    {
      name: 'instructionDocTypeTag',
      type: FieldType.string,
    },
    {
      name: 'minBusinessType',
      type: FieldType.string,
    },
    {
      name: 'maxBusinessType',
      type: FieldType.string,
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('调拨单类型'),
      lookupCode: 'APEX_WMS.SEND_RECEIVE_EXECUTE_DOC_TYPE',
      required: true,
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_SEND_RECEIVE_EXECUTE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'fromSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromSite`).d('来源站点'),
      lovCode: 'APEX_WMS.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
      validator: (...args: Array<any>) => {
        const {
          data: { instructionType, fromSiteLov, toSiteLov },
        } = args[2];
        if (instructionType) {
          return true;
        }
        if (isUndefined(toSiteLov) || isNull(toSiteLov)) {
          return true;
        }
        if (fromSiteLov.siteId === toSiteLov.siteId) {
          return intl.get(`${modelPrompt}.siteLov.validator`).d('来源站点与目标站点不可相同');
        }
      },
    },
    {
      name: 'fromSiteId',
      type: FieldType.number,
      bind: 'fromSiteLov.siteId',
    },
    {
      name: 'fromSiteCode',
      type: FieldType.string,
      bind: 'fromSiteLov.siteCode',
    },
    {
      name: 'fromWareHouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromWareHouse`).d('来源仓库'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('fromSiteId');
        },
        lovPara: ({ record }) => {
          const tagArr = ['SEND', 'RECEIVE', 'SEND_RECEIVE'];
          if (tagArr.indexOf(record?.get('instructionDocTypeTag')) > -1) {
            return {
              tenantId,
              siteId: record?.get('fromSiteId'),
              enableFlag: 'Y',
              locatorCategoryAreaFlag: 'Y',
              businessTypes: record?.get('minBusinessType'),
              queryType: 'SOURCE',
            };
          }
          return {
            tenantId,
            siteId: record?.get('fromSiteId'),
            enableFlag: 'Y',
            locatorCategoryAreaFlag: 'Y',
          };
        },
        required: ({ record }) => {
          return record.get('fromLocatorRequiredFlag') === 'Y';
        },
      },
    },
    {
      name: 'fromWareHouseId',
      type: FieldType.number,
      bind: 'fromWareHouseLov.locatorId',
    },
    {
      name: 'fromWareHouseCode',
      type: FieldType.string,
      bind: 'fromWareHouseLov.locatorCode',
    },
    {
      name: 'fromLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromLocator`).d('来源货位'),
      lovCode: 'APEX_WMS.MODEL.SUB_LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('fromWareHouseId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorIds: record?.get('fromWareHouseId'),
            locatorCategory: ['LOCATION', 'INVENTORY'].join(','),
          };
        },
      },
    },
    {
      name: 'fromLocatorId',
      type: FieldType.number,
      bind: 'fromLocatorLov.locatorId',
    },
    {
      name: 'fromLocatorCode',
      type: FieldType.string,
      bind: 'fromLocatorLov.locatorCode',
    },
    {
      name: 'toSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toSite`).d('目标站点'),
      lovCode: 'APEX_WMS.MODEL.SITE',
      required: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionType');
        },
      },
      validator: (...args: Array<any>) => {
        const {
          data: { instructionType, fromSiteLov, toSiteLov },
        } = args[2];
        if (instructionType) {
          return true;
        }
        if (isUndefined(fromSiteLov) || isNull(fromSiteLov)) {
          return true;
        }
        if (fromSiteLov.siteId === toSiteLov.siteId) {
          return intl.get(`${modelPrompt}.siteLov.validator`).d('来源站点与目标站点不可相同');
        }
      },
    },
    {
      name: 'toSiteId',
      type: FieldType.number,
      bind: 'toSiteLov.siteId',
    },
    {
      name: 'toSiteCode',
      type: FieldType.string,
      bind: 'toSiteLov.siteCode',
    },
    {
      name: 'toWareHouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toWareHouse`).d('目标仓库'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('toLocatorRequiredFlag') === 'Y';
        },
        disabled: ({ record }) => {
          return !record?.get('toSiteId');
        },
        lovPara: ({ record }) => {
          const tagArr = ['SEND', 'RECEIVE'];
          if (tagArr.indexOf(record?.get('instructionDocTypeTag')) > -1) {
            return {
              tenantId,
              siteId: record?.get('toSiteId'),
              enableFlag: 'Y',
              locatorCategoryAreaFlag: 'Y',
              businessTypes: record?.get('minBusinessType'),
              queryType: 'TARGET',
            };
          }
          if (record?.get('instructionDocTypeTag') === 'SEND_RECEIVE') {
            return {
              tenantId,
              siteId: record?.get('toSiteId'),
              enableFlag: 'Y',
              locatorCategoryAreaFlag: 'Y',
              businessTypes: record?.get('maxBusinessType'),
              queryType: 'TARGET',
            };
          }
          return {
            tenantId,
            siteId: record?.get('toSiteId'),
            enableFlag: 'Y',
            locatorCategoryAreaFlag: 'Y',
          };
        },
      },
    },
    {
      name: 'toWareHouseId',
      type: FieldType.number,
      bind: 'toWareHouseLov.locatorId',
    },
    {
      name: 'toWareHouseCode',
      type: FieldType.string,
      bind: 'toWareHouseLov.locatorCode',
    },
    {
      name: 'toLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toLocator`).d('目标库位'),
      lovCode: 'APEX_WMS.MODEL.SUB_LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('toWareHouseId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorIds: record?.get('toWareHouseId'),
            locatorCategory: ['LOCATION', 'INVENTORY'].join(','),
          };
        },
      },
    },
    {
      name: 'toLocatorId',
      type: FieldType.number,
      bind: 'toLocatorLov.locatorId',
    },
    {
      name: 'toLocatorCode',
      type: FieldType.string,
      bind: 'toLocatorLov.locatorCode',
    },
    {
      name: 'demandTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandTime`).d('需求时间'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.executeSite`).d('执行站点'),
      lovCode: 'APEX_WMS.MODEL.SITE',
      required: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionType');
        },
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      lookupCode: 'SOURCE_SYSTEM',
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/inventory-send-receive/doc/head/detail/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.LINE`,
        method: 'GET',
      };
    },
  },
});

const detailLineDS = (): DataSetProps => {
  // @ts-ignore
  return ({
    autoQuery: false,
    autoCreate: false,
    selection: false,
    dataKey: 'rows',
    paging: false,
    cacheSelection: true,
    primaryKey: 'instructionDocLineId',
    fields: [
      {
        name: 'instructionDocLineId',
        type: FieldType.number,
      },
      {
        name: 'lineNumber',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      },
      {
        name: 'materialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        lovCode: 'APEX_WMS.METHOD.BOM_MATERIAL',
        lovPara: {
          tenantId,
        },
        required: true,
        dynamicProps: {
          lovPara: ({dataSet}) => {
            const _siteIds = [dataSet.parent?.current?.get('fromSiteId')];
            if (_siteIds.indexOf(dataSet.parent?.current?.get('toSiteId')) === -1) {
              _siteIds.push(dataSet.parent?.current?.get('toSiteId'));
            }
            return {
              tenantId,
              siteIds: _siteIds.join(','),
            };
          },
        },
      },
      {
        name: 'materialId',
        type: FieldType.number,
        bind: 'materialLov.materialId',
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        bind: 'materialLov.materialCode',
      },
      {
        name: 'revisionFlag',
        type: FieldType.string,
        bind: 'materialLov.revisionFlag',
      },
      {
        name: 'decimalNumber',
        type: FieldType.number,
        bind: 'materialLov.decimalNumber',
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        bind: 'materialLov.currentRevisionCode',
        textField: 'description',
        valueField: 'description',
        lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
        lookupAxiosConfig: ({record}) => {
          return {
            transformResponse(data) {
              if (data instanceof Array) {
                return data;
              }
              const { rows } = JSON.parse(data);
              const firstlyQueryData = [] as Array<object>;
              if (rows instanceof Array) {
                rows.forEach(item => {
                  firstlyQueryData.push({
                    kid: uuid(),
                    description: item,
                  });
                });
              }
              if (record) {
                if (firstlyQueryData.length > 0) {
                  if (!record?.get('revisionCode')) {
                    // eslint-disable-next-line no-unused-expressions
                    record?.init('revisionCode', firstlyQueryData[0].description);
                  }
                } else {
                  // eslint-disable-next-line no-unused-expressions
                  record?.init('revisionCode');
                }
              }
              return firstlyQueryData;
            },
          };
        },
        dynamicProps: {
          disabled: ({record}) => {
            return !record?.get('materialId');
          },
          required({record}) {
            return record?.get('revisionFlag') === 'Y' && record?.get('materialId');
          },
          lovPara: ({record, dataSet}) => {
            const _siteIds = [
              dataSet.parent?.current?.get('toSiteId'),
              dataSet.parent?.current?.get('fromSiteId'),
            ];
            return {
              tenantId,
              siteIds: _siteIds.join(','),
              materialId: record?.get('materialId') || undefined,
              kid: record?.get('kid') || undefined,
            };
          },
        },
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'materialLov.materialName',
      },
      {
        name: 'requiredQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.requiredQty`).d('调拨数量'),
        // min: 0,
        // precision: 2,
        validator: value => {
          if (value === 0) {
            return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
          }
        },
        required: true,
        dynamicProps: {
          min: ({record}) => {
            return parseFloat(
              (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
            );
          },
          precision: ({record}) => {
            return record?.get('decimalNumber');
          },
          step: ({record}) => {
            return parseFloat(
              (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
            );
          },
        },
      },
      {
        name: 'uomId',
        type: FieldType.number,
        bind: 'materialLov.uomId',
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
        bind: 'materialLov.uomCode',
      },
      {
        name: 'statusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.statusDesc`).d('状态'),
      },
      {
        name: 'fromIdentifyType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.fromIdentifyType`).d('来源管理模式'),
        lookupCode: 'APEX_WMS.APS.GEN_TYPE_URL',
        lovPara: {
          typeGroup: 'IDENTITY_TYPE',
          tenantId: getCurrentOrganizationId(),
          userId: getCurrentUserId(),
        },
        valueField: 'typecode',
        textField: 'description',
      },
      {
        name: 'fromSiteLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.fromSite`).d('来源站点'),
        lovCode: 'APEX_WMS.MODEL.SITE',
        required: true,
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'fromSiteId',
        type: FieldType.number,
        bind: 'fromSiteLov.siteId',
      },
      {
        name: 'fromSiteCode',
        type: FieldType.string,
        bind: 'fromSiteLov.siteCode',
      },
      {
        name: 'fromWareHouseLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.fromWareHouse`).d('来源仓库'),
        lovCode: 'APEX_WMS.MODEL.LOCATOR',
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
        dynamicProps: {
          required: ({record}) => {
            return record.get('fromLocatorRequiredFlag') === 'Y';
          },
          disabled: ({record}) => {
            return !record?.get('fromSiteId');
          },
          lovPara: ({record, dataSet}) => {
            const tagArr = ['SEND', 'RECEIVE', 'SEND_RECEIVE'];
            if (tagArr.indexOf(dataSet.parent?.current?.get('instructionDocTypeTag')) > -1) {
              return {
                tenantId,
                siteId: record?.get('fromSiteId'),
                enableFlag: 'Y',
                locatorCategoryAreaFlag: 'Y',
                businessTypes: dataSet.parent?.current?.get('minBusinessType'),
                queryType: 'SOURCE',
              };
            }
            return {
              tenantId,
              siteId: record?.get('fromSiteId'),
              enableFlag: 'Y',
              locatorCategoryAreaFlag: 'Y',
            };
          },
        },
      },
      {
        name: 'fromWareHouseId',
        type: FieldType.number,
        bind: 'fromWareHouseLov.locatorId',
      },
      {
        name: 'fromWareHouseCode',
        type: FieldType.string,
        bind: 'fromWareHouseLov.locatorCode',
      },
      {
        name: 'fromLocatorLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.fromLocator`).d('来源库位'),
        lovCode: 'APEX_WMS.MODEL.SUB_LOCATOR',
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
        dynamicProps: {
          disabled: ({record}) => {
            return !record?.get('fromWareHouseId');
          },
          lovPara: ({record}) => {
            return {
              tenantId,
              locatorIds: record?.get('fromWareHouseId'),
              locatorCategory: ['LOCATION', 'INVENTORY'].join(','),
            };
          },
        },
      },
      {
        name: 'fromLocatorId',
        type: FieldType.number,
        bind: 'fromLocatorLov.locatorId',
      },
      {
        name: 'fromLocatorCode',
        type: FieldType.string,
        bind: 'fromLocatorLov.locatorCode',
      },
      {
        name: 'soNumberLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.soNumber`).d('销售订单号'),
        // lovCode: 'APEX_WMS.SO_NUMBER',
        // lovPara: {
        //   tenantId,
        // },
        // ignore: FieldIgnore.always,
        lovCode: 'APEX_WMS.MES.SO_LINE',
        valueField: 'soLineId',
        noCache: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              siteId: record?.get('fromSiteId'),
            };
          },
          disabled: ({ record }) => {
            return !record?.get('fromSiteId') || record.get('instructionDocLineId');
          },
        },
      },
      {
        name: 'soId',
        type: FieldType.number,
        bind: 'soNumberLov.soId',
      },
      {
        name: 'soNumber',
        type: FieldType.string,
        bind: 'soNumberLov.soNumber',
      },
      // {
      //   name: 'soLineLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
      //   lovCode: 'APEX_WMS.MES.SO_LINE',
      //   valueField: 'soLineId',
      //   textField: 'soLineNum',
      //   dynamicProps: {
      //     disabled: ({record}) => {
      //       return !record?.get('soNumber');
      //     },
      //     required: ({record}) => {
      //       return record?.get('soNumber');
      //     },
      //     lovPara: ({record}) => {
      //       return {
      //         tenantId,
      //         soNumber: record?.get('soNumber'),
      //       };
      //     },
      //   },
      //   ignore: FieldIgnore.always,
      // },
      {
        name: 'soLineId',
        type: FieldType.number,
        bind: 'soNumberLov.soLineId',
      },
      {
        name: 'soLineNumber',
        label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
        type: FieldType.string,
        bind: 'soNumberLov.soLineNum',
      },
      {
        name: 'fromOnhandQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.fromOnhandQty`).d('来源库存现有量'),
      },
      {
        name: 'toIdentifyType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toIdentifyType`).d('目标管理模式'),
        lookupCode: 'APEX_WMS.APS.GEN_TYPE_URL',
        lovPara: {
          typeGroup: 'IDENTITY_TYPE',
          tenantId: getCurrentOrganizationId(),
          userId: getCurrentUserId(),
        },
        valueField: 'typecode',
        textField: 'description',
      },
      {
        name: 'toSiteLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.toSite`).d('目标站点'),
        lovCode: 'APEX_WMS.MODEL.SITE',
        required: true,
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'toSiteId',
        type: FieldType.number,
        bind: 'toSiteLov.siteId',
      },
      {
        name: 'toSiteCode',
        type: FieldType.string,
        bind: 'toSiteLov.siteCode',
      },
      {
        name: 'toWareHouseLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.toWareHouse`).d('目标仓库'),
        lovCode: 'APEX_WMS.MODEL.LOCATOR',
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
        dynamicProps: {
          required: ({record}) => {
            return record.get('toLocatorRequiredFlag') === 'Y';
          },
          disabled: ({record}) => {
            return !record?.get('toSiteId');
          },
          lovPara: ({record, dataSet}) => {
            const tagArr = ['SEND', 'RECEIVE'];
            if (tagArr.indexOf(dataSet.parent?.current?.get('instructionDocTypeTag')) > -1) {
              return {
                tenantId,
                siteId: record?.get('toSiteId'),
                enableFlag: 'Y',
                locatorCategoryAreaFlag: 'Y',
                businessTypes: dataSet.parent?.current?.get('minBusinessType'),
                queryType: 'TARGET',
              };
            }
            if (dataSet.parent?.current?.get('instructionDocTypeTag') === 'SEND_RECEIVE') {
              return {
                tenantId,
                siteId: record?.get('toSiteId'),
                enableFlag: 'Y',
                locatorCategoryAreaFlag: 'Y',
                businessTypes: dataSet.parent.current?.get('maxBusinessType'),
                queryType: 'TARGET',
              };
            }
            return {
              tenantId,
              siteId: record?.get('toSiteId'),
              enableFlag: 'Y',
              locatorCategoryAreaFlag: 'Y',
            };
          },
        },
      },
      {
        name: 'toWareHouseId',
        type: FieldType.number,
        bind: 'toWareHouseLov.locatorId',
      },
      {
        name: 'toWareHouseCode',
        type: FieldType.string,
        bind: 'toWareHouseLov.locatorCode',
      },
      {
        name: 'toLocatorLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.toLocator`).d('目标库位'),
        lovCode: 'APEX_WMS.MODEL.SUB_LOCATOR',
        ignore: FieldIgnore.always,
        dynamicProps: {
          disabled: ({record}) => {
            return !record?.get('toWareHouseId');
          },
          // required: ({record, dataSet}) => {
          //   return (
          //     record?.get('toWareHouseId') &&
          //     dataSet.parent?.current?.get('instructionDocTypeTag') === 'SEND'
          //   );
          // },
          lovPara: ({record}) => {
            return {
              tenantId,
              locatorIds: record?.get('toWareHouseId'),
              locatorCategory: ['LOCATION', 'INVENTORY'].join(','),
            };
          },
        },
      },
      {
        name: 'toLocatorId',
        type: FieldType.number,
        bind: 'toLocatorLov.locatorId',
      },
      {
        name: 'toLocatorCode',
        type: FieldType.string,
        bind: 'toLocatorLov.locatorCode',
      },
      {
        name: 'toOnhandQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toOnhandQty`).d('目标库存现有量'),
      },
      {
        name: 'toleranceFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'N',
      },
      {
        name: 'toleranceType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
        options: toleranceTypeOptionDs,
        textField: 'description',
        valueField: 'typeCode',
        dynamicProps: {
          required: ({record}) => {
            return record?.get('toleranceFlag') === 'Y';
          },
        },
      },
      {
        name: 'toleranceTypeDesc',
        type: FieldType.string,
      },
      {
        name: 'toleranceMaxValue',
        type: FieldType.number,
        min: 0,
        label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差值'),
        dynamicProps: {
          disabled: ({record}) => {
            return (
              !record?.get('toleranceType') || record?.get('toleranceType') === 'OVER_MATERIAL_LOT'
            );
          },
          required: ({record}) => {
            return (
              record?.get('toleranceType') &&
              ['NUMBER', 'PERCENTAGE'].includes(record?.get('toleranceType'))
            );
          },
        },
      },
      {
        name: 'toleranceMinValue',
        type: FieldType.number,
        min: 0,
        label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差值'),
        dynamicProps: {
          disabled: ({record}) => {
            return (
              !record?.get('toleranceType') || record?.get('toleranceType') === 'OVER_MATERIAL_LOT'
            );
          },
          required: ({record}) => {
            return (
              record?.get('toleranceType') &&
              ['NUMBER', 'PERCENTAGE'].includes(record?.get('toleranceType'))
            );
          },
        },
      },
    ],
    events: {
      update: ({record, name}) => {
        if (
          name === 'fromLocatorLov' ||
          name === 'fromSiteLov' ||
          name === 'materialLov' ||
          name === 'toLocatorLov' ||
          name === 'toSiteLov' ||
          name === 'fromWareHouseLov' ||
          name === 'materialName' ||
          name === 'toWareHouseLov'
        ) {
          const {
            fromLocatorLov,
            fromSiteLov,
            materialLov,
            toLocatorLov,
            toSiteLov,
            toWareHouseLov,
            fromWareHouseId,
          } = record.toData();
          let toLocatorIdValue = toLocatorLov?.locatorId;
          let toWareHouseIdValue = toWareHouseLov?.locatorId;
          if (name === 'toWareHouseLov') {
            toLocatorIdValue = toWareHouseLov?.locatorId;
            toWareHouseIdValue = undefined;
          }

          let fromLocatorIdValue = fromLocatorLov?.locatorId
          if (name === 'fromWareHouseLov') {
            fromLocatorIdValue = fromWareHouseId
          }

          const obj = {
            fromLocatorId: fromLocatorIdValue,
            fromSiteId: fromSiteLov?.siteId,
            materialId: materialLov?.materialId,
            organizationId: tenantId,
            toLocatorId: toLocatorIdValue,
            toSiteId: toSiteLov?.siteId,
            toWareHouseId: toWareHouseIdValue,
          };
          const params = {};
          Object.entries(obj).forEach(item => {
            if (item[1] !== undefined) {
              params[item[0]] = item[1];
            }
          });
          request(url, {
            method: 'GET',
            query: {...params},
          }).then(res => {
            if (res?.success) {
              if (res?.rows) {
                if (res.rows?.fromIdentifyType) {
                  record.init('fromIdentifyType', res.rows.fromIdentifyType);
                }
                if (res.rows?.toIdentifyType) {
                  record.init('toIdentifyType', res.rows.toIdentifyType);
                }
              }
            }
          });
        }
      },
    },
    record: {
      dynamicProps: {
        disabled: record => {
          return (
            record.get('instructionDocLineId') &&
            (!record.get('permissionFlag') || record.get('permissionFlag') === 'N')
          );
        },
      },
    },
  });
};

export { detailHeaderDS, detailLineDS };
