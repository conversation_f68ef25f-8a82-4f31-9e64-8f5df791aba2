/**
 * @Description: ORT测试申请-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-09-18 10:38:58
 * @LastEditTime: 2023-09-18 10:38:58
 * @LastEditors: <<EMAIL>>

*/
import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import { observer } from 'mobx-react';
import { useRequest } from '@components/tarzan-hooks';
import { headerDS, childrenDS } from '../stores/TestApplicationDS';
import { cancelApplyConfig } from '../services';

const { Panel } = Collapse;

const modelPrompt = 'tarzan.qms.ort.testApplication';

const TestApplicationList = props => {
  const { headerDs, childrenDs } = props;

  useEffect(() => {
    if (headerDs?.currentPage) {
      headerDs.query(props.headerDs.currentPage);
    } else {
      headerDs.query();
    }
  }, []);

  // 取消申请
  const cancelApply = useRequest(cancelApplyConfig(), {
    manual: true,
    needPromise: true,
  });

  const queryChildrenLineDetail = record => {
    childrenDs.setQueryParameter('ortInspectHeadId', record?.get('ortInspectHeadId'));
    childrenDs.query();
  };

  const handleQueryFirstLine = queryProps => {
    queryChildrenLineDetail(queryProps?.dataSet?.current);
  };

  useDataSetEvent(headerDs, 'load', handleQueryFirstLine);

  const columnsHeader: ColumnProps[] = [
    {
      name: 'inspectDocNum',
      lock: ColumnLock.left,
      width: 260,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'inspectDocStatus',
    },
    {
      name: 'siteLov',
    },
    {
      name: 'inspectType',
    },
    {
      name: 'materialLov',
    },
    {
      name: 'materialName',
    },
    {
      name: 'productType',
    },
    {
      name: 'createdByLov',
    },
    {
      name: 'department',
    },
    {
      name: 'inspectPurpose',
    },
    {
      name: 'sampleQty',
    },
    {
      name: 'sampleType',
    },
    {
      name: 'expectCompleteTime',
    },
    {
      name: 'urgencyDegree',
    },
    {
      name: 'projectStage',
    },
    {
      name: 'internalProductFlag',
    },
    {
      name: 'samplingMonth',
    },
    {
      name: 'prodLineLov',
    },
    {
      name: 'ratedCapacity',
    },
    {
      name: 'inspectResultDemand',
    },
    {
      name: 'withoutMaterialCode',
    },
    {
      name: 'sampleReceiveByLov',
    },
    {
      name: 'sampleReceiveDate',
    },
    {
      name: 'remark',
    },
    {
      name: 'enclosure',
      lock: ColumnLock.right,
    },
  ];
  const columnsChildren: ColumnProps[] = [
    {
      name: 'sequence',
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItem',
    },
    {
      name: 'inspectMethod',
    },
    {
      name: 'standardRequirement',
    },
    {
      name: 'inspectFrequency',
    },
    {
      name: 'outsourceFlag',
    },
    {
      name: 'inspectQty',
    },
    {
      name: 'exSampleSolveMethod',
    },
    {
      name: 'acSampleSolveMethod',
    },
    {
      name: 'enclosure',
      lock: ColumnLock.right,
    },
  ];

  const handleEdit = record => {
    props.history.push(`/hwms/ort/test-application/detail/${record.get('ortInspectHeadId')}`);
  };

  const handleCreate = () => {
    props.history.push(`/hwms/ort/test-application/detail/create`);
  };

  const handleCancel = async () => {
    const res = await cancelApply.run({
      params: headerDs?.selected.map(record => {
        return record.get('ortInspectHeadId');
      }),
    });
    if (res?.success) {
      notification.success({});
      headerDs.batchUnSelect(headerDs.selected);
      if (headerDs?.currentPage) {
        headerDs.query(props.headerDs.currentPage);
      } else {
        headerDs.query();
      }
    }
  };

  const CancelBtn = observer(props => {
    const { dataSet } = props;
    const buttonAuth = () => {
      if (!(dataSet?.selected?.length > 0)) {
        return false;
      }
      let statusOk = true;
      const statusList = ['NEW', 'REJECTED'];
      dataSet?.selected?.forEach(record => {
        if (!statusList.includes(record.get('inspectDocStatus'))) {
          statusOk = false;
        }
      });
      return statusOk;
    };
    return (
      <PermissionButton type="c7n-pro" disabled={!buttonAuth()} onClick={handleCancel}>
        {intl.get(`${modelPrompt}.cancel`).d('取消申请')}
      </PermissionButton>
    );
  });

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.testApplication`).d('ORT测试申请')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <CancelBtn dataSet={headerDs} />
      </Header>
      <Content>
        <Table
          searchCode="ortcssq1"
          customizedCode="ortcssq1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headerDs}
          columns={columnsHeader}
          onRow={({ record }) => ({
            onClick: () => queryChildrenLineDetail(record),
          })}
        />
        <Collapse bordered={false} defaultActiveKey={['line']}>
          <Panel key="line" header={intl.get(`${modelPrompt}.childrenTitle`).d('测试项目')}>
            <Table customizedCode="ortcssq2" dataSet={childrenDs} columns={columnsChildren} />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.qms.ort.testApplication', 'tarzan.common'],
})(
  withProps(
    () => {
      const childrenDs = new DataSet({
        ...childrenDS('list'),
      });
      const headerDs = new DataSet({
        ...headerDS('list'),
      });
      return {
        headerDs,
        childrenDs,
      };
    },
    { cacheState: true, keepOriginDataSet: true },
  )(TestApplicationList),
);
