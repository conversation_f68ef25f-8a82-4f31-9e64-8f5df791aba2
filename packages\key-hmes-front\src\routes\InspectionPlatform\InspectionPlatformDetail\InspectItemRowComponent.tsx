/**
 * @Description: 检验平台-检验项目行模式
 * @Author: <<EMAIL>>
 * @Date: 2023-02-14 15:23:15
 * @LastEditTime: 2023-05-18 16:59:25
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect } from 'react';
import { maxBy, isNumber } from 'lodash';
import {
  Attachment,
  DateTimePicker,
  Form,
  NumberField,
  Currency,
  Select,
  Table,
  TextField,
} from 'choerodon-ui/pro';
import { Tag, Popover } from 'choerodon-ui';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { Tooltip } from 'choerodon-ui/pro/lib/core/enum';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import NcRecordComponent from './NcRecordComponent';
import styles from './index.modules.less';

const { Option } = Select;
const { ItemGroup, Item } = Form;

const modelPrompt = 'tarzan.qms.inspectionPlatform';

const InspectItemRowComponent = props => {
  const {
    NcRecordDimension,
    inspectInfoDS,
    inspectItemRowDS,
    customizeTable,
    cacheMinWidth,
    setCacheMinWidth,
    onRenderValueList,
    handleComputedQty,
    handleChangeValueColor,
    handleChangeNgQty,
    handleBatchChangeData,
  } = props;

  const [height, setHeight] = useState(0);

  useEffect(() => {
    setTimeout(() => {
      setHeight(600);
    }, 1000);
  }, []);

  // 抽样数量改变时自动计算录入值列宽
  const handleChangeSamplingQty = (newValue, oldValue, fieldName, dataType, curRecord) => {
    if (newValue !== oldValue) {
      let _dataQty = 0;
      if (
        curRecord.get('dataQtyDisposition') === 'DATA' ||
        curRecord.get('dataQtyDisposition') === 'SAMPLE'
      ) {
        _dataQty = 1;
      } else {
        _dataQty = Number(curRecord.get('dataQty') || 0);
      }
      curRecord.set('samplingQtyCount', _dataQty * Number(newValue || 0));
      handleComputedQty(fieldName, dataType, true);
      const _maxSamplingQty = Number(
        maxBy(inspectItemRowDS.records, record => record.get('samplingQtyCount'))?.get(
          'samplingQtyCount',
        ) || 0,
      );
      if (_maxSamplingQty > 2) {
        setCacheMinWidth(_maxSamplingQty * 65 + (_maxSamplingQty - 1) * 5 + 16);
      } else {
        setCacheMinWidth(180);
      }
      inspectItemRowDS.setState('maxSamplingQty', _maxSamplingQty);
    }
  };

  // 设置录入值宽度
  // 抽样数量改变时自动计算录入值列宽
  const handleChangeInspectTextWidth = () => {
    const inspectBusinessType = inspectInfoDS.current.get('inspectBusinessType');
    let maxQtyRecord;
    if (inspectBusinessType === 'RATO-IQC' || inspectBusinessType === 'RATO-SQC') {
      maxQtyRecord = maxBy(inspectItemRowDS.records, record => {
        const _frequencyQtyCount = !isNaN(record?.get('recordFrequency'))
          ? Number(record?.get('recordFrequency') || 0)
          : 1;
        let _count = Number(record.get('samplingQtyCount') || 0);
        if (_frequencyQtyCount > 1) {
          _count = Number(_frequencyQtyCount) <= _count ? Number(_frequencyQtyCount) : _count;
        } else {
          _count = _count >= 1 ? 1 : _count;
        }
        return _count + Number(record.get('addQtyCount') || 0);
      });
    } else {
      maxQtyRecord = maxBy(
        inspectItemRowDS.records,
        record =>
          Number(record.get('samplingQtyCount') || 0) + Number(record.get('addQtyCount') || 0),
      );
    }

    const maxQty =
      Number(maxQtyRecord?.get('samplingQtyCount') || 0) +
      Number(maxQtyRecord.get('addQtyCount') || 0);
    if (maxQty > 2) {
      setCacheMinWidth(maxQty * 65 + (maxQty - 1) * 5 + 16);
    } else {
      setCacheMinWidth(180);
    }
    inspectItemRowDS.setState('maxSamplingQty', maxQty);
  };

  const attachmentProps: any = {
    bucketName: 'qms',
    bucketDirectory: 'inspection-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const columns: ColumnProps[] = [
    {
      name: 'sequence',
      width: 60,
      align: ColumnAlign.center,
    },
    {
      name: 'inspectItemDesc',
      minWidth: 120,
      renderer: ({ value, record }) => {
        return (
          <span>
            {record?.get('requiredFlag') === 'Y' && <span style={{ color: 'red' }}>*&nbsp;</span>}
            {value}
          </span>
        );
      },
    },
    {
      name: 'inspectItemTypeDesc',
    },
    {
      name: 'inspectToolAndMethod',
    },
    // {
    //   name: 'technicalRequirement',
    // },
    {
      name: 'acceptStandard',
    },
    {
      name: 'samplingQty',
      header: (...args: Array<any>) => {
        return (
          <span
            onClick={() => handleBatchChangeData('BATCH_SAMPLING')}
            style={{ cursor: 'pointer' }}
          >
            {args[2]}
          </span>
        );
      },
      minWidth: 150,
      align: ColumnAlign.left,
      renderer: ({ value, record }) => {
        if (!record) {
          return;
        }
        const _canEdit = inspectInfoDS.getState('canEdit');
        const _samplingType = record?.get('samplingType');
        if (_samplingType !== 'USER_DEFINED_SAMPLING') {
          return (
            <span>
              {value}
              {/* &nbsp;{record?.get('uomName')} */}
            </span>
          );
        }
        const _fieldName = record.get('fieldName');
        const _dataType = record.get('dataType');
        return (
          <span>
            <NumberField
              record={record}
              name="samplingQty"
              disabled={!_canEdit}
              style={{ width: 80, verticalAlign: 'baseline' }}
              onChange={(newValue, oldValue) =>
                handleChangeSamplingQty(newValue, oldValue, _fieldName, _dataType, record)
              }
            />
            {/* &nbsp;{record?.get('uomName')} */}
          </span>
        );
      },
    },
    {
      name: 'valueRange',
      width: 180,
      title: intl.get(`${modelPrompt}.model.line.valueRange`).d('符合值/不符合值/预警值'),
      tooltip: Tooltip.none,
      renderer: ({ record }) => {
        if(record?.get('dataType') === 'TEXT'){
          return (
            <Popover
              placement="top"
              content={<div>{record.get('technicalRequirement')}</div>}>
              {record.get('technicalRequirement')}
            </Popover>
          );
        }
        return (
          <Popover
            placement="top"
            content={
              <div>
                {(record?.get('trueValues') || []).map(item => (
                  <div
                    className={styles['table-tooltip-tag']}
                    style={{ color: '#11d954', backgroundColor: '#E6FFEA' }}
                  >
                    {item}
                  </div>
                ))}
                {(record?.get('falseValues') || []).map(item => (
                  <div
                    className={styles['table-tooltip-tag']}
                    style={{ color: '#f23a50', backgroundColor: '#fff0f0' }}
                  >
                    {item}
                  </div>
                ))}
                {(record?.get('warningValues') || []).map(item => (
                  <div
                    className={styles['table-tooltip-tag']}
                    style={{ color: '#fbad00', backgroundColor: '#fffbe6' }}
                  >
                    {item}
                  </div>
                ))}
                &nbsp;{record?.get('uomName')}
              </div>
            }
            trigger="hover"
          >
            <div>
              {(record?.get('trueValues') || []).map(item => (
                <Tag color="green">{item}</Tag>
              ))}
              {(record?.get('falseValues') || []).map(item => (
                <Tag color="red">{item}</Tag>
              ))}
              {(record?.get('warningValues') || []).map(item => (
                <Tag color="yellow">{item}</Tag>
              ))}
              &nbsp;{record?.get('uomName')}
            </div>
          </Popover>
        );
      },
    },
    {
      name: 'okQty',
    },
    {
      name: 'ngQty',
      editor: record =>
        inspectInfoDS.getState('canEdit') && (
          <NumberField
            onChange={(newValue, oldValue) => handleChangeNgQty(newValue, oldValue, '', record)}
          />
        ),
    },
    {
      name: 'inspectResult',
      header: (...args: Array<any>) => {
        return (
          <a onClick={() => handleBatchChangeData('BATCH_RESULT')} style={{ cursor: 'pointer' }}>
            {args[2]}
          </a>
        );
      },
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        if (!record) {
          return '';
        }
        return (
          <Select
            record={record}
            name="inspectResult"
            readOnly={!inspectInfoDS.getState('canEdit')}
            style={{
              verticalAlign: 'baseline',
              backgroundColor:
                value === 'OK' ? 'rgb(230, 255, 234)' : value === 'NG' ? 'rgb(255, 240, 240)' : '',
            }}
          />
        );
      },
    },
    {
      name: 'sourceInspectValue',
      hidden: `${inspectInfoDS.current?.get('inspectTimes')}` === '1',
    },
    {
      name: 'inspectValueRecord',
      width: cacheMinWidth,
      minWidth: cacheMinWidth,
      defaultWidth: cacheMinWidth,
      renderer: ({ record }) => {
        if (!record) {
          return;
        }
        const _canEdit = inspectInfoDS.getState('canEdit');
        const _dataType = record?.get('dataType');
        const _samplingQtyCount = Number(record?.get('samplingQtyCount') || 0);
        const _addQtyCount = Number(record?.get('addQtyCount') || 0);
        const _fieldName = record?.get('fieldName');
        let _count =
          _dataType === 'CALCULATE_FORMULA'
            ? Number(record?.get('formulaCount') || 0)
            : _samplingQtyCount;
        // 取最大的数
        let taskLineObjects = 0;
        record?.get('taskLineObjects')?.forEach(taskLineObjectItem => {
          if (taskLineObjectItem.taskLineActDtls && taskLineObjectItem.taskLineActDtls.length > 0) {
            taskLineObjects += taskLineObjectItem.taskLineActDtls.length;
          } else {
            taskLineObjects++;
          }
        });
        if (taskLineObjects > _count) {
          _count = taskLineObjects;
        }

        _count += _addQtyCount;

        const _dataQtyValue: Array<any> = [];
        for (let i = 0; i < _count; i++) {
          _dataQtyValue.push(`${_fieldName}_VALUE${i}`);
        }
        const _trueValues = record?.get('trueValues') || [];
        const _falseValues = record?.get('falseValues') || [];
        const _valueLists = record?.get('valueLists') || [];
        const _decimalNumber = record?.get('decimalNumber');
        // 计算列宽
        const _valueWidth = `${100 / _count}%`;
        return (
          <ItemGroup>
            {_dataQtyValue.map((valueName, index) => {
              const _formulaValue = record?.get(valueName);
              return (
                <>
                  {_dataType === 'VALUE' && !_decimalNumber && _decimalNumber !== 0 && (
                    <Item name={valueName}>
                      <NumberField
                        onEnterDown={() => {
                          if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                            return;
                          }
                          // 新增字段
                          // 获取之前的输入框是否为必输
                          const required = record?.getField(valueName)?.required;
                          const type = record?.getField(valueName)?.type;
                          record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                            name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            type,
                            required,
                          });
                          record.set('addQtyCount', _addQtyCount + 1);
                          handleChangeInspectTextWidth();

                          // 聚焦
                          setTimeout(() => {
                            const elementInput = document.getElementsByName(
                              `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            );
                            if (elementInput.length > 0) {
                              elementInput[0].focus();
                            }
                          }, 100);
                        }}
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${
                              styles['number-input-left']
                            }`
                            : styles['number-input-left']
                        }
                        disabled={!_canEdit}
                        onChange={(value, oldValue) => {
                          if (
                            !(
                              (value === undefined || value === null) &&
                              (oldValue === undefined || oldValue === null)
                            )
                          ) {
                            handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
                          }
                        }}
                      />
                    </Item>
                  )}
                  {_dataType === 'VALUE' && isNumber(_decimalNumber) && _decimalNumber >= 0 && (
                    <Item name={valueName}>
                      <Currency
                        onEnterDown={() => {
                          if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                            return;
                          }
                          // 新增字段
                          // 获取之前的输入框是否为必输
                          const required = record?.getField(valueName)?.required;
                          const type = record?.getField(valueName)?.type;
                          record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                            name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            type,
                            required,
                          });
                          record.set('addQtyCount', _addQtyCount + 1);
                          handleChangeInspectTextWidth();

                          // 聚焦
                          setTimeout(() => {
                            const elementInput = document.getElementsByName(
                              `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            );
                            if (elementInput.length > 0) {
                              elementInput[0].focus();
                            }
                          }, 100);
                        }}
                        record={record}
                        name={valueName}
                        precision={_decimalNumber > 6 ? 6 : _decimalNumber}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${
                              styles['number-input-left']
                            }`
                            : styles['number-input-left']
                        }
                        disabled={!_canEdit}
                        onChange={(value, oldValue) => {
                          if (
                            !(
                              (value === undefined || value === null) &&
                              (oldValue === undefined || oldValue === null)
                            )
                          ) {
                            handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
                          }
                        }}
                      />
                    </Item>
                  )}
                  {_dataType === 'DECISION_VALUE' && (
                    <Item name={valueName}>
                      <Select
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                            : ''
                        }
                        disabled={!_canEdit}
                        onChange={value =>
                          handleChangeValueColor(_fieldName, value, valueName, record, _dataType)
                        }
                      >
                        {_trueValues.concat(_falseValues).map(item => (
                          <Option value={item} key={item}>
                            {item}
                          </Option>
                        ))}
                      </Select>
                    </Item>
                  )}
                  {_dataType === 'TEXT' && (
                    <Item name={valueName}>
                      <TextField
                        onEnterDown={() => {
                          if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                            return;
                          }
                          // 新增字段
                          // 获取之前的输入框是否为必输
                          const required = record?.getField(valueName)?.required;
                          const type = record?.getField(valueName)?.type;
                          record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                            name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            type,
                            required,
                          });
                          record.set('addQtyCount', _addQtyCount + 1);
                          handleChangeInspectTextWidth();

                          // 聚焦
                          setTimeout(() => {
                            const elementInput = document.getElementsByName(
                              `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            );
                            if (elementInput.length > 0) {
                              elementInput[0].focus();
                            }
                          }, 100);
                        }}
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        disabled={!_canEdit}
                        onChange={() =>
                          handleComputedQty(
                            _fieldName,
                            _dataType,
                            false,
                            true,
                            record,
                            true,
                            valueName,
                          )
                        }
                      />
                    </Item>
                  )}
                  {_dataType === 'VALUE_LIST' && (
                    <Item name={valueName}>
                      <Select
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                            : ''
                        }
                        disabled={!_canEdit}
                        onChange={value =>
                          handleChangeValueColor(_fieldName, value, valueName, record, _dataType)
                        }
                      >
                        {_valueLists.map(item => (
                          <Option value={item} key={item}>
                            {item}
                          </Option>
                        ))}
                      </Select>
                    </Item>
                  )}
                  {_dataType === 'DATE' && (
                    <Item name={valueName}>
                      <DateTimePicker
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        disabled={!_canEdit}
                        onChange={() =>
                          handleComputedQty(
                            _fieldName,
                            _dataType,
                            false,
                            true,
                            record,
                            true,
                            valueName,
                          )
                        }
                      />
                    </Item>
                  )}
                  {_dataType === 'CALCULATE_FORMULA' && (_formulaValue || _formulaValue === 0) && (
                    <Item name={valueName}>
                      <TextField
                        onEnterDown={() => {
                          if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                            return;
                          }
                          // 新增字段
                          // 获取之前的输入框是否为必输
                          const required = record?.getField(valueName)?.required;
                          const type = record?.getField(valueName)?.type;
                          record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                            name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            type,
                            required,
                          });
                          record.set('addQtyCount', _addQtyCount + 1);
                          handleChangeInspectTextWidth();

                          // 聚焦
                          setTimeout(() => {
                            const elementInput = document.getElementsByName(
                              `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            );
                            if (elementInput.length > 0) {
                              elementInput[0].focus();
                            }
                          }, 100);
                        }}
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                            : ''
                        }
                        disabled
                      />
                    </Item>
                  )}
                </>
              );
            })}
          </ItemGroup>
        );
      },
    },
    {
      name: 'remark',
      editor: () => inspectInfoDS.getState('canEdit'),
      width: 120,
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      width: 380,
      renderer: ({ record }) => {
        return (
          <>
            <Attachment
              {...attachmentProps}
              record={record}
              name="enclosure"
              readOnly
              disabled={!inspectInfoDS.getState('canEdit')}
            />
            <Attachment
              {...attachmentProps}
              record={record}
              name="actEnclosure"
              disabled={!inspectInfoDS.getState('canEdit')}
            />
            <NcRecordComponent
              canEdit={inspectInfoDS.getState('canEdit')}
              type="text"
              visible={
                inspectInfoDS.current?.get('inspectNcRecordDimension') ===
                  NcRecordDimension.itemNc && !!record?.get('inspectTaskLineId')
              }
              customizeTable={customizeTable}
              queryParams={{
                inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
                inspectDocId: inspectInfoDS.current?.get('inspectDocId'),
                inspectTaskLineId: record?.get('inspectTaskLineId'),
                inspectItemId: record?.get('inspectItemId'),
                inspectDocLineId: record?.get('inspectDocLineId'),
                inspectItemDesc: record?.get('inspectItemDesc'),
                inspectNcRecordDimension: inspectInfoDS.current?.get('inspectNcRecordDimension'),
              }}
              style={{ marginLeft: '0.16rem' }}
            />
          </>
        );
      },
    },
  ];

  return (
    <>
      {customizeTable(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.QLINE`,
        },
        <Table
          dataSet={inspectItemRowDS}
          columns={columns}
          highLightRow
          customizedCode="jyptql"
          virtual
          virtualCell
          style={{ height }}
        />,
      )}
    </>
  );
};

export default InspectItemRowComponent;
