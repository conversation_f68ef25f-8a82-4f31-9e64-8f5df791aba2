import React, { useMemo, useEffect } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { tableDS } from './stories';
import { fetchDefaultSite } from '../../services/api';

const modelPrompt = 'tarzan.mes.barcodeWorkstationBinding';

const WorkMobileEventDetailReport = (props) => {
  const { tableDs } = props;
  useEffect(() => {
    fetchDefaultSite().then(res => {
      if (res && res.success) {
        tableDs.queryDataSet.getField('assemblePointLov').set('lovPara', { siteId: res.rows.siteId,tenantId: getCurrentOrganizationId() })
      }
    });
  }, []);
  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'equipmentCode',
      },
      {
        name: 'equipmentName',
      },
      {
        name: 'workcellCode',
      },
      {
        name: 'workcellName',
      },
      {
        name: 'assemblePointCode',
      },
      {
        name: 'assemblePointName',
      },
      {
        name: 'materialLotCode',
      },
      {
        name: 'materialCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'revisionCode',
      },
      {
        name: 'qty',
      },
      {
        name: 'materialLotSequence',
      },
      {
        name: 'enableFlag',
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      {
        name: 'createdByRealName',
      },
      {
        name: 'creationDate',
      },
      {
        name: 'lastUpdatedByRealName',
      },
      {
        name: 'lastUpdateDate',
      },
    ];
  }, []);


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('物料批工位关系报表')}>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          style={{height: '400px'}}
          dataSet={tableDs}
          columns={columns}
          searchCode="barcodeWorkstationBinding"
          customizedCode="barcodeWorkstationBinding"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.mes.barcodeWorkstationBinding', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(WorkMobileEventDetailReport),
);
