/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-04-18 16:54:45
 * @LastEditTime: 2023-04-19 13:49:29
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { DataSet, Output } from 'choerodon-ui/pro';

import moment from 'moment';
import { LabelAlign, LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { Tag } from 'choerodon-ui';

const modelPrompt = 'tarzan.qms.inspectionPlatform';

const TabInfoBox = observer(
  ({ inspectInfoDS, activeKey }: { inspectInfoDS: DataSet; activeKey }) => {
    return (
      activeKey === 'INSPECT_INFO' && (
        <>
          <Output
            renderer={() => (
              <>
                <span style={{ marginRight: '10px', fontWeight: '600' }}>
                  {inspectInfoDS.current?.get('inspectTaskCode')}
                </span>
                {inspectInfoDS.current?.get('inspectTaskStatusDesc') && (
                  <Tag color="blue">{inspectInfoDS.current?.get('inspectTaskStatusDesc')}</Tag>
                )}
                {inspectInfoDS.current?.get('urgentFlag') === 'Y' && (
                  <Tag color="red">{intl.get(`${modelPrompt}.urgent`).d('加急')}</Tag>
                )}
                {inspectInfoDS.current?.get('taskCategory') && (
                  <Tag color="green">{inspectInfoDS.current?.get('taskCategory')}</Tag>
                )}
                <Tag color="orange">{inspectInfoDS.current?.get('inspectBusinessTypeDesc')}</Tag>
                {inspectInfoDS.current?.get('actualStartTime') && (
                  <>
                    {intl.get(`${modelPrompt}.label.actualDateShow`).d('开始/结束时间')}:
                    {inspectInfoDS.current?.get('actualStartTime')
                      ? moment(inspectInfoDS.current?.get('actualStartTime')).format('YYMMDD HH:mm')
                      : inspectInfoDS.current?.get('actualStartTime')}
                    {inspectInfoDS.current?.get('actualEndTime')
                      ? `~${moment(inspectInfoDS.current?.get('actualEndTime')).format(
                        'YYMMDD HH:mm',
                      )}`
                      : inspectInfoDS.current?.get('actualEndTime')}
                  </>
                )}
              </>
            )}
          />
        </>
      )
    );
  },
);

export default TabInfoBox;
