/**
 * @Description: 检验方案维护-详情-新增维度抽屉
 * @Author: <<EMAIL>>
 * @Date: 2023-01-09 17:48:04
 * @LastEditTime: 2023-05-18 16:41:35
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { Table, Lov, Button, TextField } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC } from '@utils/config';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.initialManagementActivity';

const DetailTable = props => {
  const { ds, canEdit, customizeTable, detailRowSelectList } = props;

  useEffect(() => {}, []);

  const handleAddLine = () => {
    ds.create();
  };
  const deleteRecord = () => {
    ds.remove(detailRowSelectList);
  };

  const columns: ColumnProps[] = [
    {
      name: 'sequence',
      width: 60,
      renderer: ({ record }: any) => {
        return record.index * 10 + 10;
      },
    },
    {
      name: 'unitName',
      editor: () => canEdit && <TextField name="unitName" />,
    },
    {
      name: 'responsibleEmLov',
      editor: () => canEdit && <Lov name="responsibleEmLov" required />,
    },
    {
      name: 'employeeNum',
      editor: () => canEdit && <TextField name="employeeNum" />,
    },
    {
      name: 'positionName',
      editor: () => canEdit && <TextField name="positionName" />,
    },
    {
      name: 'email',
      editor: () => canEdit && <TextField name="email" />,
    },
    {
      name: 'mobile',
      width: 200,
      editor: () => canEdit && <TextField name="mobile" />,
    },
    {
      name: 'roleAssign',
      width: 200,
      editor: () => canEdit && <TextField name="roleAssign" required />,
    },
  ];

  const buttons = [
    <Button
      icon="playlist_add"
      onClick={() => {
        handleAddLine();
      }}
      disabled={!canEdit}
    >
      {intl.get(`${modelPrompt}.button.create`).d('新增')}
    </Button>,
    <Button
      icon="delete"
      onClick={() => {
        deleteRecord();
      }}
      disabled={!canEdit}
    >
      {intl.get(`${modelPrompt}.button.delete`).d('删除')}
    </Button>,
  ];

  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_BASIC.DMS_DETAIL`,
    },
    <Table dataSet={ds} columns={columns} buttons={buttons} />,
  );
};
export default DetailTable;
