/**
 * @Description: 检验业务类型规则维护-列表页
 * @Author: <EMAIL>
 * @Date: 2023/1/30 15:47
 */
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { DataSet, Modal, Table, Tabs } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import ExcelExport from 'components/ExcelExport';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Badge, Tag } from 'choerodon-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import withProps from 'utils/withProps';
import { useDataSetEvent } from 'utils/hooks';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import { detailTableDS, messageDetailDS, messageDS, tableDS } from '../stories';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.hwms.inspectionInfoManagement';
const tenantId = getCurrentOrganizationId();
const { TabPane } = Tabs;

interface InspectItemListProps extends RouteComponentProps {
  tableDs: DataSet;
  searchFormDs: any;
  unHandledTableDs: DataSet;
  inspectFailureTableDs: DataSet;
  detailTableDs: DataSet;
  inspectFailureDetailDs: DataSet;
  customizeTable: any;
  location: any;
}

const InspectionInfoManagementList: FC<InspectItemListProps> = props => {
  const {
    tableDs,
    searchFormDs,
    unHandledTableDs,
    detailTableDs,
    inspectFailureTableDs,
    inspectFailureDetailDs,
    location: { state },
    history,
    customizeTable,
  } = props;
  const [cancleAble, setCancleAble] = useState(true);
  const [canFlag, setCanFlag] = useState(true);
  const [currentTab, setCurrentTab] = useState('all');

  const { run: pushMessage, loading: pushMessageLoading } = useRequest(
    {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-info-wait/re-create/ui`,
      method: 'POST',
    },
    { manual: true },
  );

  const handleSetSearchFromDsQuery = () => {
    handleChangeTab(searchFormDs.getState('currentTab') || 'all');
    return false;
  };

  useDataSetEvent(searchFormDs, 'query', handleSetSearchFromDsQuery);
  useDataSetEvent(unHandledTableDs, 'batchSelect', () => {
    if (unHandledTableDs.selected.length !== 0) {
      setCancleAble(false);
      setCanFlag(false)
    }
  });
  useDataSetEvent(unHandledTableDs, 'batchUnSelect', () => {
    if (unHandledTableDs.selected.length === 0) {
      setCancleAble(true);
      setCanFlag(true)
    }
  });
  useDataSetEvent(tableDs, 'batchSelect', () => {
    if (tableDs.selected.length !== 0) {
      setCanFlag(false)
    }
  });
  useDataSetEvent(tableDs, 'batchUnSelect', () => {
    if (tableDs.selected.length === 0) {
      setCanFlag(true)
    }
  });
  useEffect(() => {
    if (state?.inspectInfoCodes) {
      searchFormDs.queryDataSet?.current?.set('inspectInfoCodes', state?.inspectInfoCodes);
      history.replace({ ...history.location, state: undefined });
    }
  }, [history.location.state]);

  useDataSetEvent(searchFormDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteLov':
        record.set('inspectBusinessTypeObject', null);
        break;
      default:
        break;
    }
  });

  const columns: any = useMemo(
    () => [
      {
        name: 'inspectInfoCode',
        width: 150,
        lock: ColumnLock.left,
      },
      currentTab === 'inspectFailure' && {
        name: 'dealStatus',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value, record }) => {
          let className = 'red';
          if (['SEND_SUCCESS', 'CONSUME_SUCCESS'].includes(value)) {
            className = 'green';
          } else if (value === 'UN_SEND') {
            className = 'orange';
          }
          return value && <Tag color={className}>{record!.getField('dealStatus')!.getText()}</Tag>;
        },
      },
      currentTab === 'inspectFailure' && { name: 'dealMessage', width: 600 },
      { name: 'siteCode' },
      { name: 'inspectBusinessTypeDesc', width: 150 },
      currentTab !== 'inspectFailure' && {
        name: 'inspectInfoStatusDesc',
        width: 150,
        renderer: ({ record }) => {
          if (record!.get('inspectInfoStatus') === 'NEW') {
            return <Tag color="blue">{intl.get(`${modelPrompt}.new`).d('新建')}</Tag>;
          }
          if (record!.get('inspectInfoStatus') === 'RELEASED') {
            return (
              <Tag color="yellow">{intl.get(`${modelPrompt}.released`).d('已生成检验单')}</Tag>
            );
          }
          if (record!.get('inspectInfoStatus') === 'COMPLETED') {
            return <Tag color="green">{intl.get(`${modelPrompt}.completed`).d('已完成')}</Tag>;
          }
          if (record!.get('inspectInfoStatus') === 'CANCEL') {
            return <Tag color="red">{intl.get(`${modelPrompt}.cancel`).d('取消')}</Tag>;
          }
        },
      },
      currentTab === 'inspectFailure' && {
        name: 'inspectDocReleasedFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('urgentFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('urgentFlag') === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'urgentFlag',
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('urgentFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('urgentFlag') === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      { name: 'quantity', align: ColumnAlign.right },
      { name: 'uomName' },
      { name: 'materialCode', width: 150 },
      { name: 'materialName', width: 150 },
      { name: 'revisionCode', width: 150 },
      { name: 'woNumber', width: 150 },
      { name: 'sourceObjectTypeDesc', width: 150 },
      { name: 'sourceNumber', width: 200 },
      currentTab === 'all' && {
        name: 'inspectionDocNumber',
        width: 200,
        renderer: ({ record }) => {
          if(!record?.get('inspectDocIdList')?.length) {
            return (
              <a onClick={() => handleToInspectDetail(record)}>{record!.get('inspectionDocNumber')}</a>
            )
          }
          return (
            <a onClick={() => handleToInspectIndex(record)}>{record!.get('inspectionDocNumber')}</a>
          )
        },
      },
      { name: 'inspectInfoUserName' },
      currentTab !== 'inspectFailure' && {
        name: 'inspectInfoLot',
        width: 150,
      },
      { name: 'inspectInfoCreationDate', width: 150, align: ColumnAlign.center },
      { name: 'supplierName', width: 150 },
      { name: 'customerName', width: 150 },
      { name: 'areaName', width: 150 },
      { name: 'prodLineName', width: 150 },
      { name: 'operationName', width: 150 },
      { name: 'processWorkcellName', width: 150 },
      { name: 'stationWorkcellName', width: 150 },
      { name: 'equipmentCode', width: 150 },
      { name: 'locatorName', width: 150 },
      { name: 'shiftTeamCode', width: 150 },
      { name: 'shiftDate', align: ColumnAlign.center, width: 150 },
      { name: 'shiftCode' },
      { name: 'poHeadLineNumber', width: 150 },
      { name: 'soHeadLineNumber', width: 150 },
      currentTab !== 'inspectFailure' && {
        name: 'infoCreateMethodDesc',
      },
      currentTab !== 'inspectFailure' && { name: 'remark' },
      {
        title: intl.get(`tarzan.common.label.action`).d('操作'),
        lock: ColumnLock.right,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <PermissionButton
            type="text"
            permissionList={[
              {
                code: `list.table.inspectDetail`,
                type: 'button',
                meaning: '列表页-报检明细按钮',
              },
            ]}
            onClick={() => handleOpenDetailModal(record)}
          >
            {intl.get(`${modelPrompt}.model.detail`).d('报检明细')}
          </PermissionButton>
        ),
      },
    ],
    [currentTab],
  );

  const detailColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'objectTypeDesc', width: 120 },
      {
        name: 'objectCode',
        width: 150,
        renderer: ({ value, record }) => {
          if (['MAT', 'LOT'].includes(record?.get('objectType'))) {
            return '';
          }
          return value;
        },
      },
      { name: 'materialCode', width: 150 },
      { name: 'materialName', width: 150 },
      { name: 'revisionCode' },
      { name: 'quantity' },
      { name: 'uomName' },
      { name: 'locatorName', width: 150 },
      { name: 'supplierLot', width: 150 },
      { name: 'lot', width: 150 },
    ],
    [],
  );

  const handleToInspectDetail = record => {
    history.push(`/hwms/inspect-doc-maintain/dist/${record.get('inspectDocId')}`);
  };

  const handleToInspectIndex = record => {
    history.push({
      pathname: `/hwms/inspect-doc-maintain/list`,
      state: {
        inspectDocIdList: record.get('inspectDocIdList'),
        inspectionDocNumber: record?.get('inspectionDocNumber'),
      },
    });
  }

  const handleOpenDetailModal = useCallback(
    record => {
      const ds = currentTab === 'inspectFailure' ? inspectFailureDetailDs : detailTableDs;
      if (currentTab === 'inspectFailure') {
        ds.setQueryParameter('inspectInfoWaitId', record.get('inspectInfoWaitId'));
      } else {
        ds.setQueryParameter('inspectInfoId', record.get('inspectInfoId'));
      }
      ds.query();
      Modal.open({
        key: Modal.key(),
        title: intl.get(`${modelPrompt}.model.detail`).d('报检明细'),
        destroyOnClose: true,
        drawer: true,
        closable: true,
        style: {
          width: 1000,
        },
        children: <Table dataSet={ds} columns={detailColumns} />,
      });
    },
    [currentTab],
  );

  const handleCreateInspectDoc = () => {
    if (currentTab === 'unhandled') {
      const selectedId = unHandledTableDs.selected.map(e => e.get('inspectInfoId'));
      request(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/inspection-info/limit/create`,
        {
          method: 'POST',
          body: {
            inspectInfoIds: selectedId,
            releasedFlag: 'Y',
          },
        },
      ).then(res => {
        if (res && res.success) {
          notification.success({});
          unHandledTableDs.query();
        } else {
          notification.warning({ description: res.message });
        }
      });
    } else {
      notification.warning({ description: '请针对未处理的报检信息进行单据创建！' });
    }
  };

  const handleCancel = () => {
    if (currentTab === 'unhandled') {
      const selectedId = unHandledTableDs.selected.map(e => e.get('inspectInfoId'));
      request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-info/cancel/ui`, {
        method: 'POST',
        body: selectedId,
      }).then(res => {
        if (res && res.success) {
          notification.success({});
          unHandledTableDs.query();
        } else {
          notification.warning({ description: res.message });
        }
      });
    } else {
      notification.warning({ description: '请针对未处理的报检信息进行单据取消！' });
    }
  };

  const handleAllOk = () => {
    if (currentTab === 'unhandled') {
      const selectedId = unHandledTableDs.selected.map(e => e.get('inspectInfoId'));
      request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-info/batch/approval`, {
        method: 'POST',
        body: selectedId,
      }).then(res => {
        if (res && res.success) {
          notification.success({});
          unHandledTableDs.query();
        } else {
          notification.warning({ description: res.message });
        }
      });
    } else {
      const selectedId = tableDs.selected.map(e => e.get('inspectInfoId'));
      request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-info/batch/approval`, {
        method: 'POST',
        body: selectedId,
      }).then(res => {
        if (res && res.success) {
          notification.success({});
          tableDs.query();
        } else {
          notification.warning({ description: res.message });
        }
      });
    }
  };

  const handlePushMessage = () => {
    pushMessage({
      params:inspectFailureTableDs.selected.map((record) => record.get('inspectInfoWaitId')),
      onSuccess: () => {
        notification.success({});
        searchFormDs.query();
      },
    });
  };

  const handleChangeTab = async newActiveKey => {
    setCurrentTab(newActiveKey);
    searchFormDs.setState('currentTab', newActiveKey);
    if (newActiveKey === 'unhandled') {
      unHandledTableDs.queryDataSet = searchFormDs.queryDataSet;
      unHandledTableDs.setQueryParameter('tabFlag', 'UNTREATED');
      unHandledTableDs.setQueryParameter(
        'customizeUnitCode',
        'MT.INSPECTION_INFO_MANAGEMENT.QUERY,MT.INSPECTION_INFO_MANAGEMENT.UNHANDLED_TABLE',
      );
      await unHandledTableDs.query();
    } else if (newActiveKey === 'all') {
      tableDs.queryDataSet = searchFormDs.queryDataSet;
      tableDs.setQueryParameter('tabFlag', undefined);
      tableDs.setQueryParameter(
        'customizeUnitCode',
        'MT.INSPECTION_INFO_MANAGEMENT.QUERY,MT.INSPECTION_INFO_MANAGEMENT.TABLE',
      );
      await tableDs.query();
    } else {
      inspectFailureTableDs.queryDataSet = searchFormDs.queryDataSet;
      inspectFailureTableDs.setQueryParameter(
        'customizeUnitCode',
        'MT.INSPECTION_INFO_MANAGEMENT.QUERY,MT.INSPECTION_INFO_MANAGEMENT.TABLE',
      );
      await inspectFailureTableDs.query();
    }
  };

  const getExportQueryParams = () => {
    // const { selected } = tableDs;
    const queryParams = searchFormDs.queryDataSet?.toData()[0] || {};
    return {
      ...queryParams,
      tabFlag: currentTab === "unhandled" ? "UNTREATED" : '',
    };
  };

  const PushMessageBtn = observer(({ds})=>{
    return (
      <PermissionButton
        type="c7n-pro"
        color={ButtonColor.default}
        permissionList={[
          {
            code: `list.button.messagePush`,
            type: 'button',
            meaning: '列表页-消息重推按钮',
          },
        ]}
        disabled={!(ds.selected?.length > 0)}
        onClick={handlePushMessage}
        loading={pushMessageLoading}
      >
        {intl.get(`${modelPrompt}.button.messagePush`).d('消息重推')}
      </PermissionButton>
    )
  });

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('报检信息管理平台')}>
        {currentTab !== 'inspectFailure' && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="add"
              permissionList={[
                {
                  code: `list.button.createInspectDoc`,
                  type: 'button',
                  meaning: '列表页-创建检验单按钮',
                },
              ]}
              onClick={handleCreateInspectDoc}
              disabled={cancleAble}
            >
              {intl.get(`${modelPrompt}.button.list`).d('创建检验单')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.default}
              icon="cancel"
              permissionList={[
                {
                  code: `list.button.cancel`,
                  type: 'button',
                  meaning: '列表页-取消按钮',
                },
              ]}
              onClick={handleCancel}
              disabled={cancleAble}
            >
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        )}
        {currentTab === 'inspectFailure' && (
          <PushMessageBtn  ds={inspectFailureTableDs}/>
        )}
        {
          ['all', 'unhandled'].includes(currentTab) && (
            <>
              <ExcelExport
                method="GET"
                requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-info/list/ui`}
                queryParams={getExportQueryParams}
                buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
              />
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.default}
                icon="cached"
                permissionList={[
                  {
                    code: `list.button.allOk`,
                    type: 'button',
                    meaning: '列表页-批量合格',
                  },
                ]}
                onClick={handleAllOk}
                disabled={canFlag}
              >
                {intl.get('tarzan.common.button.allOk').d('批量合格')}
              </PermissionButton>
            </>
          )
        }
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: 'MT.INSPECTION_INFO_MANAGEMENT.QUERY',
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={searchFormDs}
            queryFieldsLimit={4}
            columns={[]}
            searchCode="InspectBusTypeRule"
            customizedCode="InspectBusTypeRule"
            className={styles['inspection-info-management-search-table']}
          />,
        )}
        <Tabs onChange={handleChangeTab} activeKey={currentTab}>
          <TabPane key="all" tab={intl.get(`${modelPrompt}.tab.allDoc`).d('全部报检信息')}>
            {customizeTable(
              {
                code: 'MT.INSPECTION_INFO_MANAGEMENT.TABLE',
              },
              <Table
                queryBar={TableQueryBarType.none}
                queryBarProps={{
                  fuzzyQuery: false,
                }}
                dataSet={tableDs}
                columns={columns}
                searchCode="InspectionInfoManagement"
                customizedCode="InspectionInfoManagement"
              />,
            )}
          </TabPane>
          <TabPane
            key="unhandled"
            title={intl.get(`${modelPrompt}.tab.unhandledDoc`).d('未处理报检信息')}
          >
            {customizeTable(
              {
                code: 'MT.INSPECTION_INFO_MANAGEMENT.UNHANDLED_TABLE',
              },
              <Table
                queryBar={TableQueryBarType.none}
                queryBarProps={{
                  fuzzyQuery: false,
                }}
                dataSet={unHandledTableDs}
                columns={columns}
                searchCode="InspectionInfoManagement"
                customizedCode="InspectionInfoManagement"
              />,
            )}
          </TabPane>
          <TabPane
            key="inspectFailure"
            title={intl.get(`${modelPrompt}.tab.inspectFailure`).d('报检失败消息')}
          >
            {customizeTable(
              {
                code: 'MT.INSPECTION_INFO_MANAGEMENT.UNHANDLED_TABLE',
              },
              <Table
                queryBar={TableQueryBarType.none}
                queryBarProps={{
                  fuzzyQuery: false,
                }}
                dataSet={inspectFailureTableDs}
                columns={columns}
                searchCode="InspectionInfoManagement-inspectFailure"
                customizedCode="InspectionInfoManagement-inspectFailure"
              />,
            )}
          </TabPane>
        </Tabs>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
        // selection: false,
      });
      const unHandledTableDs = new DataSet(tableDS());
      const detailTableDs = new DataSet(detailTableDS());
      // 报检失败消息Ds
      const inspectFailureTableDs = new DataSet(messageDS());
      // 报检失败消息明细DS
      const inspectFailureDetailDs = new DataSet(messageDetailDS());
      const searchFormDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
        detailTableDs,
        unHandledTableDs,
        inspectFailureTableDs,
        inspectFailureDetailDs,
        searchFormDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.SHIFT_TEAM_LIST.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.SHIFT_TEAM_LIST.LIST`,
      ],
    })(InspectionInfoManagementList as any),
  ),
);
