import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import moment from 'moment';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.deviceInterface';
const tableDS = () => {
  return {
    name: 'tableDs',
    primaryKey: 'inboundId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'interfaceName',
        type: 'string',
        label: intl.get(`${modelPrompt}.interfaceName`).d('接口名称'),
      },
      {
        name: 'interfaceUrl',
        type: 'string',
        label: intl.get(`${modelPrompt}.interfaceUrl`).d('接口地址'),
      },
      {
        name: 'ip',
        type: 'string',
        label: intl.get(`${modelPrompt}.ip`).d('ip地址'),
      },
      {
        name: 'requestMethod',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestMethod`).d('请求方式'),
      },
      {
        name: 'requestBodyParameter',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestBodyParameter`).d('请求body参数'),
      },
      {
        name: 'requestStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestStatus`).d('请求状态'),
      },
      {
        name: 'requestTime',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.requestTime`).d('请求时间'),
      },
      {
        name: 'userAgent',
        type: 'string',
        label: intl.get(`${modelPrompt}.userAgent`).d('用户代理'),
      },
      {
        name: 'responseTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.responseTime`).d('响应时间'),
      },
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
    ],
    queryFields: [
      {
        name: 'interfaceName',
        type: 'string',
        label: intl.get(`${modelPrompt}.interfaceName`).d('接口名称'),
      },
      {
        name: 'interfaceUrl',
        type: 'string',
        label: intl.get(`${modelPrompt}.interfaceUrl`).d('接口地址'),
      },
      {
        name: 'ip',
        type: 'string',
        label: intl.get(`${modelPrompt}.ip`).d('ip地址'),
      },
      {
        name: 'requestBodyParameter',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestBodyParameter`).d('请求body参数'),
      },
      {
        name: 'requestStatus',
        type: 'string',
        lookupCode: 'YP_MES.MES.INVOKE_STATUS',
        label: intl.get(`${modelPrompt}.requestStatus`).d('请求状态'),
      },
      {
        name: 'timeFrom',
        type: 'dateTime',
        required: true,
        label: intl.get(`${modelPrompt}.timeFrom`).d('开始时间从'),
        dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
        defaultValue: moment().subtract(180, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        name: 'timeTo',
        type: 'dateTime',
        required: true,
        label: intl.get(`${modelPrompt}.timeTo`).d('开始时间至'),
        defaultValue: moment().format('YYYY-MM-DD HH:mm:ss'),
        dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
        dynamicProps: {
          min: ({ record }) => record?.get('timeFrom'),
          max: ({ record }) => moment(record?.get('timeFrom')).add(180, 'minutes'),
          disabled: ({record}) => !record?.get('timeFrom'),
        },
      },
      {
        name: 'responseTime',
        type: 'number',
        label: intl.get(`${modelPrompt}.responseTime`).d('响应时间'),
      },
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('原材料条码'),
      },
      {
        name: 'processBarcode',
        type: 'string',
        label: intl.get(`${modelPrompt}.processBarcode`).d('产品条码'),
      },
      {
        name: 'equipmentLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.equipmentLov`).d('设备号'),
        lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
        ignore: 'always',
        multiple: true,
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId',
        multiple: ',',
      },
      {
        name: 'equipmentCode',
        bind: 'equipmentLov.equipmentCode',
        multiple: ',',
      },
    ],
    events: {
      update: ({ record, name, value, oldValue })  => {
        if(name === 'timeFrom'&&value!==oldValue){
          record.init('timeTo', null)
        }
      },
    },
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-invoke-inbounds/list/ui`,
          method: 'POST',
        };
      },
    },
  };
};

const detailFormDS = () => {
  return {
    name: 'detailFormDS',
    paging: false,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'ip',
        type: 'string',
        label: intl.get(`${modelPrompt}.ip`).d('ip地址'),
      },
      {
        name: 'interfaceName',
        type: 'string',
        label: intl.get(`${modelPrompt}.interfaceName`).d('接口名称'),
      },
      {
        name: 'interfaceUrl',
        type: 'string',
        label: intl.get(`${modelPrompt}.interfaceUrl`).d('接口地址'),
      },
      {
        name: 'requestTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestTime`).d('请求时间'),
      },
      {
        name: 'requestHeaderParameter',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestHeaderParameter`).d('请求header参数'),
      },
      {
        name: 'requestBodyParameter',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestBodyParameter`).d('请求body参数'),
      },
      {
        name: 'requestMethod',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestMethod`).d('请求方式'),
      },
      {
        name: 'requestStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestStatus`).d('请求状态'),
      },
      {
        name: 'responseContent',
        type: 'string',
        label: intl.get(`${modelPrompt}.responseContent`).d('响应内容'),
      },
      {
        name: 'responseTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.responseTime`).d('响应时间'),
      },
      {
        name: 'userAgent',
        type: 'string',
        label: intl.get(`${modelPrompt}.userAgent`).d('用户代理'),
      },
      {
        name: 'stacktrace',
        type: 'string',
        label: intl.get(`${modelPrompt}.stacktrace`).d('错误堆栈'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-invoke-inbounds/detail/ui`,
          method: 'GET',
        };
      },
    },
  };
};


export { tableDS, detailFormDS };
