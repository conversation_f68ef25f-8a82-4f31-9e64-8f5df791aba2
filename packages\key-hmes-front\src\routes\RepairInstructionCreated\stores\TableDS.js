import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
// import uuid from 'uuid/v4';
// import moment from 'moment';
import notification from 'utils/notification';
// import { stepOptionDS } from './CommonDS';

const Host = BASIC.HMES_BASIC;
// const Host = '/key-focus-mes-33275';
const serveCode = BASIC.TARZAN_METHOD;

const modelPrompt = 'tarzan.workshop.MachineProductionDispatch';
const tenantId = getCurrentOrganizationId();

const formDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-work-order/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && datas.rows) {
            if (datas.rows.bomId === 0) {
              datas.rows.bomId = null;
            }
            if (datas.rows.routerId === 0) {
              datas.rows.routerId = null;
            }
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
  fields: [
    // 基本属性
    {
      name: 'site',
      type: FieldType.object,
      label: '站点',
      lovCode: 'HME.REPAIR_TASK_SITE',
      textField: 'siteCode',
      valueField: 'siteId',
      noCache: true,
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
        // siteType: 'MANUFACTURING',
      },
      // dynamicProps: {
      //   disabled({ record }) {
      //     return record.get('workOrderNum');
      //   },
      // },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'site.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: '返修指令编码',
      disabled: true,
    },
    {
      name: 'workOrderTypeMeaning',
      type: FieldType.string,
      label: '生产指令类型',
      disabled: true,
      defaultValue: '返修',
    },
    // 需求属性
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialCode`).d('物料编码'),
      lovCode: 'HME.REPAIR_TASK_MATERIAL',
      textField: 'materialCode',
      valueField: 'materialId',
      required: true,
      noCache: true,
      // lovPara: { tenantId },
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('siteId');
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'material.materialCode',
    },
    {
      name: 'materialSiteId',
      type: FieldType.string,
      bind: 'material.materialSiteId',
    },
    {
      name: 'productionVersionFlag',
      type: FieldType.string,
      bind: 'material.productionVersionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.revision`).d('版本'),
      textField: 'revisionCode',
      valueField: 'revisionCode',
      noCache: true,
      lookupUrl: `${Host}/v1/${tenantId}/hme-repair-task-create/permission/material/revision/ui`,
      // lookupAxiosConfig: ({ record }) => {
      //   return {
      //     transformResponse(data) {
      //       let rows;
      //       if (Array.isArray(data)) {
      //         rows = data;
      //       } else {
      //         rows = JSON.parse(data).rows;
      //       }
      //       let firstlyQueryData = [];
      //       if (rows instanceof Array) {
      //         firstlyQueryData = rows.map(item => {
      //           return {
      //             // kid: uuid(),
      //             description: item,
      //           };
      //         });
      //       }
      //       if (record) {
      //         if (firstlyQueryData.length > 0) {
      //           if (!record.get('revisionCode')) {
      //             record.set('revisionCode', firstlyQueryData[0].description);
      //           }
      //         } else {
      //           record.set('revisionCode', null);
      //         }
      //         // record.set('revisionCodeUpdate', uuid());
      //       }
      //       return firstlyQueryData;
      //     },
      //   };
      // },
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('materialId') || record.get('productionVersionFlag') === 'N';
        },
        // required({ record }) {
        //   return record.get('revisionFlag') === 'Y' && record.get('materialId');
        // },
        lovPara: ({ record }) => {
          return {
            tenantId,
            materialSiteId: record.get('materialSiteId') || undefined,
            // materialId: record.get('materialId') || undefined,
            // kid: record.get('kid') || undefined,
          };
        },
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: '物料描述',
      bind: 'material.materialName',
      disabled: true,
    },
    {
      name: 'primaryUomQtySum',
      type: FieldType.number,
      label: '数量',
      min: 1,
      step: 1,
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.uomCode`).d('单位编码'),
      bind: 'material.uomCode',
      disabled: true,
    },
    {
      name: 'productionVersionCode',
      type: FieldType.string,
      label: '生产版本',
      textField: 'productionVersionCode',
      valueField: 'productionVersionCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${Host}/v1/${tenantId}/hme-repair-task-create/permission/production/version/ui`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     if (data instanceof Array) {
      //       return data;
      //     }
      //     const { rows } = JSON.parse(data);
      //     return rows;
      //   },
      // },
      dynamicProps: {
        disabled({ record }) {
          return !record.get('materialId') || !record.get('siteId') || !record.get('revisionCode');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId') || undefined,
            materialId: record.get('materialId') || undefined,
            revisionCode: record.get('revisionCode') || undefined,
          };
        },
      },
    },
    {
      name: 'bom',
      type: FieldType.object,
      label: '装配清单',
      lovCode: 'HME.REPAIR_TASK_BOM',
      textField: 'bomName',
      valueField: 'bomId',
      noCache: true,
      required: true,
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            materialId: record.get('materialId') || undefined,
            revisionCode: record.get('revisionCode') || undefined,
            productionVersionCode: record.get('productionVersionCode') || undefined,
          };
        },
        disabled: ({ record }) => {
          return !record.get('materialId');
        },
      },
    },
    {
      name: 'bomId',
      type: FieldType.number,
      bind: 'bom.bomId',
    },
    {
      name: 'bomName',
      type: FieldType.string,
      bind: 'bom.bomName',
    },
    {
      name: 'bomRevision',
      type: FieldType.string,
      bind: 'bom.bomRevision',
    },
    {
      name: 'router',
      type: FieldType.object,
      label: '工艺路线',
      lovCode: 'HME.REPAIR_TASK_ROUTER',
      textField: 'routerName',
      valueField: 'routerId',
      noCache: true,
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            materialId: record.get('materialId') || undefined,
            revisionCode: record.get('revisionCode') || undefined,
            productionVersionCode: record.get('productionVersionCode') || undefined,
          };
        },
        disabled: ({ record }) => {
          return !record.get('materialId')|| !record.get('bomId') ;
        },
      },
    },
    {
      name: 'routerId',
      type: FieldType.string,
      bind: 'router.routerId',
    },
    {
      name: 'routerName',
      type: FieldType.string,
      bind: 'router.routerName',
    },
    {
      name: 'routerRevision',
      type: FieldType.string,
      bind: 'router.routerRevision',
    },
    {
      name: 'prodLine',
      type: FieldType.object,
      label: '生产线',
      lovCode: 'HME.REPAIR_TASK_PRODLINE',
      textField: 'prodLineCode',
      valueField: 'prodLineId',
      noCache: true,
      required: true,
      ignore: 'always',
      lovPara: { tenantId },
      dynamicProps: {
        disabled({ record }) {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'prodLineId',
      type: FieldType.number,
      bind: 'prodLine.prodLineId',
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      bind: 'prodLine.prodLineCode',
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      bind: 'prodLine.prodLineName',
    },
    {
      name: 'locator',
      type: FieldType.object,
      label: '默认完工货位',
      lovCode: 'HME.REPAIR_TASK_LOCATOR',
      textField: 'locatorCode',
      valueField: 'locatorId',
      noCache: true,
      ignore: 'always',
      lovPara: { tenantId },
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'locator.locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'locator.locatorCode',
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      bind: 'locator.locatorName',
    },

    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartTime`).d('计划开始日期'),
      required: true,
      max: 'planEndTime',
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planEndTime`).d('计划结束日期'),
      required: true,
      min: 'planStartTime',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.remark`).d('备注'),
    },
  ],
});

// 物料信息
const materialDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-production-capacitys/wo/dispatch/query`,
        method: 'GET',
      };
    },
  },
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'kid',
  autoLocateFirst: false,
  fields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: '物料批',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: '物料编码',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: '物料描述',
    },

    {
      name: 'revisionCode',
      type: FieldType.string,
      label: '物料版本',
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: '单位',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: '库位',
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      label: '不良代码',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: '不良代码描述',
    },
    {
      name: 'ncRecordNum',
      type: FieldType.string,
      label: '不良单号',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: '处置备注',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: '原生产编码',
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: '客户名称',
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      label: '销售订单',
    },
  ],
});

// 创建物料信息
const materialCreateDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'materialLotCodes',
      type: FieldType.string,
      label: '物料批',
    },

    {
      name: 'currentFlag',
      label: '强制工艺路线',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
});

// 工艺路线头DS
const routerHeadDS = () => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  autoCreate: true,
  fields: [
    {
      name: 'routerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerName`).d('编码'),
    },
    {
      name: 'revision',
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
      type: FieldType.string,
    },
    {
      name: 'currentFlag',
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      type: FieldType.string,
    },
    {
      name: 'site',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'relaxedFlowFlag',
      label: intl.get(`${modelPrompt}.relaxedFlowFlag`).d('松散标识'),
      type: FieldType.string,
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'routerStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerStatus`).d('状态'),
    },
    {
      name: 'routerId', // 装配清单入参会用到
      type: FieldType.string,
    },
    {
      name: 'usageFlag',
      label: intl.get(`${modelPrompt}.usageFlag`).d('使用状态标识'),
      type: FieldType.string,
    },
    {
      name: 'routerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerType`).d('类型'),
    },
    {
      name: 'selectedRouterType', // 装配清单传参需要使用
    },
    {
      name: 'bomId',
    },
    {
      name: 'bomType',
    },
    {
      name: 'bomTypeDesc',
    },
    {
      name: 'bomRevision',
    },
    {
      name: 'bomName',
    },
    {
      name: 'autoRevisionFlag',
      label: intl.get(`${modelPrompt}.autoRevisionFlag`).d('自动升版本标识'),
      type: FieldType.string,
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
    },
    {
      name: 'copiedFromRouterName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.copiedFromRouterName`).d('来源工艺路线'),
    },
    {
      name: 'copiedFromRouterRevision',
      type: FieldType.string,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${serveCode}/v1/${tenantId}/mt-router/detail/ui`,
        method: 'get',
      };
    },
  },
});

// 工艺路线行列表
const detailTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  autoLocateFirst: true,
  primaryKey: 'routerStepId',
  childrenField: 'mtRouterStepGroupStepDTO',
  expandField: 'expand',
  dataKey: 'rows',
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      width: 120,
      label: intl.get(`${modelPrompt}.sequence`).d('步骤顺序'),
    },
    {
      name: 'description',
      type: FieldType.string,
      width: 150,
      label: intl.get(`${modelPrompt}.descriptionStep`).d('步骤描述'),
    },
    {
      name: 'routerDoneStepFlag',
      type: FieldType.string,
      width: 160,
      label: intl.get(`${modelPrompt}.stepNameAndRevision`).d('编码/版本'),
    },
    {
      name: 'routerStepType',
      type: FieldType.string,
      width: 160,
      label: intl.get(`${modelPrompt}.routerStepType`).d('步骤类型'),
      options: new DataSet({ ...stepOptionDS() }),
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'entryStepFlag',
      type: FieldType.string,
      width: 200,
      label: intl.get(`${modelPrompt}.entryStepFlag`).d('步骤属性'),
    },
    {
      name: 'requiredTimeInProcess',
      type: FieldType.string,
      width: 130,
      label: intl.get(`${modelPrompt}.requiredTimeInProcess`).d('标准工时（分钟）'),
    },
  ],
  transport: {
    read: () => {
      return {
        // url: `${serveCode}/v1/${tenantId}/mt-router-step/list/ui`,
        url: `${serveCode}/v1/${tenantId}/hme-repair-task-create/list/ui`,
        method: 'get',
        transformResponse: data => {
          if (Array.isArray(data)) {
            return data;
          }
          const res = JSON.parse(data); // 处理接口报错
          if (res && !res.success) {
            if (res.message) {
              notification.error({
                message: res.message,
              });
            }
            return {
              rows: [],
            };
          }
          res.rows.forEach(i => {
            if (i.mtRouterStepGroupDTO?.routerStepGroupId) {
              i.mtRouterStepGroupStepDTO = i.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO;
            }
          });
          return res.rows;
        },
      };
    },
  },
});

// 获取步骤类型
const stepOptionDS = () => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          module: 'ROUTER',
          typeGroup: 'ROUTER_STEP_TYPE',
        },
      };
    },
  },
});

// 数据收集组列表
const dataCollectionGroupDS = () => ({
  autoQuery: false,
  paging: false,
  selection: 'multiple',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-production-capacitys/wo/dispatch/query`,
        method: 'GET',
      };
    },
  },
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'kid',
  autoLocateFirst: false,
  fields: [
    {
      name: 'a',
      type: FieldType.string,
      label: '编码',
    },
    {
      name: 'b',
      type: FieldType.string,
      label: '描述',
    },
    {
      name: 'c',
      type: FieldType.string,
      label: '收集组类型',
    },
    {
      name: 'd',
      type: FieldType.string,
      label: '收集时间点',
    },
  ],
});

// 数据收集组新建
const dataCollectionGroupCreateDS = () => ({
  autoQuery: false,
  // paging: false,
  pageSize: 10,
  selection: 'multiple',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-repair-task-create/tag/group/ui`,
        method: 'GET',
      };
    },
  },
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'kid',
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'tagGroupCode',
      type: FieldType.string,
      label: '数据收集组编码',
    },
    {
      name: 'tagGroupDescription',
      type: FieldType.string,
      label: '数据收集组描述',
    },
  ],
  fields: [
    {
      name: 'tagGroupCode',
      type: FieldType.string,
      label: '数据收集组编码',
    },
    {
      name: 'tagGroupDescription',
      type: FieldType.string,
      label: '数据收集组描述',
    },
  ],
});

// 待派工单
const tableDS = () => ({
  autoQuery: false,
  paging: false,
  selection: 'multiple',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-production-capacitys/wo/dispatch/query`,
        method: 'GET',
      };
    },
  },
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'kid',
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'a',
      type: FieldType.string,
      label: '数据收集组编码',
    },
    {
      name: 'b',
      type: FieldType.string,
      label: '数据收集组描述',
    },
  ],
  fields: [
    {
      name: 'a',
      type: FieldType.string,
      label: '数据收集组编码',
    },
    {
      name: 'b',
      type: FieldType.string,
      label: '数据收集组描述',
    },
  ],
});


export {
  dataCollectionGroupDS,
  dataCollectionGroupCreateDS,
  tableDS,
  formDS,
  materialDS,
  materialCreateDS,
  detailTableDS,
  stepOptionDS,
  routerHeadDS,
};
