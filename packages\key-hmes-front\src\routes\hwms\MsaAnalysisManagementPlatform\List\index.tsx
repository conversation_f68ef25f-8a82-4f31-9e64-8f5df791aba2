import React, { useMemo, useCallback, useState } from 'react';
import { useDataSetEvent } from 'utils/hooks';
import { Table, DataSet, Attachment, Button, Spin } from 'choerodon-ui/pro';
import { Collapse, Tag, Badge } from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { observer } from 'mobx-react-lite';
import intl from 'utils/intl';
import { useRequest } from '@components/tarzan-hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { headTableDS, lineTableDS } from '../stores/tableListDS';
import { CancelInfo, LineQueryDataInfo } from '../services';

interface OrganizationTreeProps {
  headTableDs: DataSet;
  lineTableDs: DataSet;
  history: any;
}

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const Panel = Collapse.Panel;

const AnalysisManagementPlatformList = observer((props: OrganizationTreeProps) => {
  const { headTableDs, lineTableDs, history } = props;

  const { run: cancelInfo, loading: CancelLoading } = useRequest(CancelInfo(), {
    manual: true,
  });

  const { run: lineQueryDataInfo, loading: lineQueryDataLoading } = useRequest(
    LineQueryDataInfo(),
    {
      manual: true,
    },
  );

  const [selectedRecords, setSelectedRecords] = useState([]);

  useDataSetEvent(headTableDs, 'load', () => {
    lineTableDs.loadData([]);
  });

  useDataSetEvent(headTableDs, 'batchSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  useDataSetEvent(headTableDs, 'batchUnSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  const attachmentProps: any = {
    name: 'sipFileUuid',
    bucketName: 'qms',
    bucketDirectory: 'msa-analysis-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'msaCode',
        lock: ColumnLock.left,
        width: 120,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/msa-analysis-management-platform/detail/${record!.get('msaTaskId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'msaStatusDesc',
        align: ColumnAlign.center,
        renderer: ({ value, record }) => {
          switch (record?.get('msaStatus')) {
            case 'NEW':
              return <Tag color="blue">{value}</Tag>;
            case 'TO_IMPROVE':
              return <Tag color="cyan">{value}</Tag>;
            case 'TO_ANALYZE':
              return <Tag color="orange">{value}</Tag>;
            case 'ANALYZING':
              return <Tag color="purple">{value}</Tag>;
            case 'COMPLETED':
              return <Tag color="green">{value}</Tag>;
            case 'IMPROVING':
              return <Tag color="red">{value}</Tag>;
            default:
              return <Tag color="gray">{value}</Tag>;
          }
        },
      },
      {
        name: 'siteName',
      },
      {
        name: 'msaTypeDesc',
      },
      {
        name: 'projectStageDesc',
      },
      {
        name: 'projectNameDesc',
      },
      {
        name: 'qualityCharacteristic',
      },
      {
        name: 'specialCharacteristic',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ record }) => {
          return (
            <Badge
              status={record?.get('specialCharacteristic') === 'Y' ? 'success' : 'error'}
              text={
                record?.get('specialCharacteristic') === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
      {
        name: 'modelCode',
        width: 150,
      },
      {
        name: 'modelName',
        width: 150,
      },
      {
        name: 'speciesName',
        width: 150,
      },
      {
        name: 'msaAnalysisMethod',
        width: 220,
      },
      {
        name: 'toolCode',
        width: 120,
      },
      {
        name: 'prodlineName',
      },
      {
        name: 'workcellName',
      },
      {
        name: 'onlineFlag',
        align: ColumnAlign.center,
        renderer: ({ record }) => {
          return (
            <Badge
              status={record?.get('onlineFlag') === 'Y' ? 'success' : 'error'}
              text={
                record?.get('onlineFlag') === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
      {
        name: 'analyzedByDesc',
      },
      {
        name: 'assistantByDesc',
      },
      {
        name: 'completeTimeLimit',
      },
      {
        name: 'planStartTime',
        width: 180,
      },
      {
        name: 'planEndTime',
        width: 180,
      },
      {
        name: 'actualStartTime',
        width: 180,
      },
      {
        name: 'actualFinishTime',
        width: 180,
      },
      {
        name: 'msaResult',
      },
      {
        name: 'sourceMsaTaskCode',
        renderer: ({ value, record }) => <a onClick={() => {
          history.push(`/hwms/msa-analysis-management-platform/detail/${record!.get('sourceMsaTaskId')}`);
        }}>{value}</a>,
      },
      {
        name: 'improveByDesc',
      },
      {
        name: 'creationDate',
        width: 180,
      },
      {
        name: 'creationByDesc',
      },
      { name: 'remark' },
      {
        title: intl.get(`${modelPrompt}.operation`).d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 300,
        renderer: ({ record }) => (
          <>
            <Button funcType={FuncType.link}
              disabled={!['ANALYZING', 'COMPLETED', 'IMPROVING'].includes(record?.get('msaStatus'))}
              onClick={() => {
                history.push(`/hwms/msa-analysis-management-platform/analysisDetail/${record!.get('msaTaskId')}`);
              }}
            >
              {intl.get(`${modelPrompt}.analysisDetails`).d('分析详情')}
            </Button>
            <Attachment record={record} readOnly {...attachmentProps} />
          </>
        ),
      },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    history.push(`/hwms/msa-analysis-management-platform/detail/create`);
  }, []);

  const lineColums: ColumnProps[] = useMemo(
    () => [
      {
        name: 'msaAnalysisMethodDesc',
        align: ColumnAlign.center,
        width: 220,
        renderer: ({ value, record }) => {
          switch (record?.get('msaAnalysisMethod')) {
            case 'STABILITY':
              return <Tag color="blue">{value}</Tag>;
            case 'BIASEDNESS':
              return <Tag color="orange">{value}</Tag>;
            case 'GRR':
              return <Tag color="green">{value}({record?.get('checkLocation')})</Tag>;
            default:
              return <Tag color="green">{value}</Tag>;
          }
        },
      },
      {
        name: 'msaResult',
      },
      {
        name: 'msaConclusion',
      },
      {
        name: 'analyzeDate',
      },
      {
        name: 'analyzeStatus',
        align: ColumnAlign.center,
        renderer: ({ record }) => {
          return (
            <Badge
              status={record?.get('analyzeStatus') === 'Y' ? 'success' : 'error'}
              text={
                record?.get('analyzeStatus') === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
    ],
    [],
  );

  // 头表行点击
  const headerRowClick = record => {
    const msaTaskId = record.toData().msaTaskId;
    lineQueryDataInfo({
      params: {
        msaTaskId,
      },
      onSuccess: res => {
        if (res) {
          lineTableDs.loadData(res);
        }
      },
    });
  };

  // 取消按钮
  const handleCancel = () => {
    const msaTaskId = selectedRecords.map(record => {
      const { msaTaskId } = record.toData();
      return msaTaskId;
    });
    const status = selectedRecords.map(record => {
      const { msaStatus } = record.toData();
      return msaStatus;
    });
    if (status.every(element => element === 'NEW')) {
      cancelInfo({
        params: { msaTaskIds: msaTaskId },
        onSuccess: () => {
          headTableDs.query();
        },
      });
    } else {
      notification.error({ message: intl.get(`${modelPrompt}.error.cancelStatusError`).d('只有新建状态的任务单才可取消！') });
    }
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.analysisManagementPlatform`).d('MSA分析管理平台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${modelPrompt}.list.button.MSAcreate`,
              type: 'button',
              meaning: '列表页-新建按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.MSAcreate`).d('新建MSA任务')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          onClick={handleCancel}
          disabled={selectedRecords.length === 0}
          permissionList={[
            {
              code: `${modelPrompt}.list.button.MSAcancel`,
              type: 'button',
              meaning: '列表页-取消按钮',
            },
          ]}
          loading={CancelLoading}
        >
          {intl.get(`${modelPrompt}.button.MSAcancel`).d('取消MSA任务')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headTableDs}
          columns={columns}
          searchCode="AnalysisManagementPlatform"
          customizedCode="AnalysisManagementPlatform"
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
        />
        <Collapse defaultActiveKey={['MSAanalyzeContent']}>
          <Panel
            header={intl.get(`${modelPrompt}.MSAanalyzeContent`).d('MSA分析内容')}
            key="MSAanalyzeContent"
          >
            <Spin dataSet={lineTableDs} spinning={lineQueryDataLoading}>
              <Table dataSet={lineTableDs} columns={lineColums} />
            </Spin>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headTableDs = new DataSet({
        ...headTableDS(),
      });
      const lineTableDs = new DataSet({
        ...lineTableDS(),
      });
      return {
        headTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(AnalysisManagementPlatformList),
);
