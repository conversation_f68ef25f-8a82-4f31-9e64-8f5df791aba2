/**
 * @Description: 分析控制图-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-11-24 17:27:42
 * @LastEditTime: 2022-12-13 17:15:44
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Content } from 'components/Page';
import { Form, DateTimePicker, Button } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { ViewMode } from 'choerodon-ui/pro/es/date-picker/enum';
import { ButtonType } from 'choerodon-ui/pro/es/button/enum';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { getQueryParam } from 'hcm-components-front/lib/utils/utils';
import uuid from 'uuid/v4';
import intl from 'utils/intl';
import GraphicChart from '../components/GraphicChart';
import CPKCard from '../components/CPKCard';
import {
  FetchHistoryControlChartGraphicData,
  FetchCPKData,
  HideControl,
  HideControlAll,
} from '../services';
import NormalityTest from './NormalityTestCard';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hspc.controlChartMaintain';
const tenantId = getCurrentOrganizationId();
// let tempDataObj: any;
// let dataObj: any;
interface QueryParams {
  fromDate: any;
  toDate: any;
}

const HistoryChartDisplay = props => {
  const { id } = props.match.params;

  const [timeParmas, setTimeParmas] = useState({});

  const [chartUuid] = useState(uuid());
  const [queryParams, setQueryParams] = useState<QueryParams | null>(null);
  const queryParamsProxy = useMemo(() => ({ params: queryParams }), []);
  queryParamsProxy.params = queryParams;
  const fetchControlChartGraphicData = useRequest(FetchHistoryControlChartGraphicData(), {
    manual: true,
    needPromise: true,
  });
  const hideControl = useRequest(HideControl(), { manual: true });
  const hideControlAll = useRequest(HideControlAll(), { manual: true });
  const fetchCPKData = useRequest(FetchCPKData(), { manual: true });

  const chartRef = useRef();

  useEffect(() => {
    if (!id) {
      return;
    }
    getChartData(false);
  }, [id]);

  const getChartData = index => {
    const urlQueryParams = getQueryParam();
    if (index || index === 0) {
      if (index === 'hide') {
        // const paramObj = {
        //   controlId: id,
        // };
        request(`/wr-tznl/v1/${tenantId}/hme-control-measure-record/show-hide-record/ui?controlId=${id}`, {
          method: 'POST',
          // params: {
          //   ...paramObj,
          // },
        }).then(res => {
          if (res.success) {
            notification.success({});
            fetchControlChartGraphicData.run({
              params: {
                controlId: id,
              },
            });
          }
        });
        // hideControlAll.run({
        //   params: paramObj,
        //   onSuccess: () => {
        //     notification.success({});
        //     fetchControlChartGraphicData.run({
        //       params: {
        //         controlId: id,
        //       },
        //     });
        //   },
        // });
      } else {
        // tempDataObj = {
        //   data: {
        //     ...dataObj.data,
        //     data: dataObj.data.data.filter((_, ele) => ele !== index),
        //   },
        // };
        const tempDataList = fetchControlChartGraphicData.data.data;
        const nowList = tempDataList?.filter(item => item.hideFlag !== 'Y');
        const nowObj = {
          data: {
            ...fetchControlChartGraphicData.data,
            data: nowList,
          },
        };
        const paramObj = {
          controlId: Number(id),
          subgroupIndex: nowObj.data.data[index].subgroupIndex,
        };
        request(`/wr-tznl/v1/${tenantId}/hme-control-measure-record/hide-record/ui?controlId=${id}&subgroupIndex=${paramObj.subgroupIndex}`, {
          method: 'POST',
          // params: {
          //   ...paramObj,
          // },
        }).then(res => {
          if (res.success) {
            notification.success({});
            fetchControlChartGraphicData.run({
              params: {
                controlId: id,
              },
            });
          }
        });
      }
    } else {
      // tempDataObj = null;
      fetchControlChartGraphicData
        .run({
          params: {
            controlId: id,
            ...(urlQueryParams as object),
          },
        })
        .then(res => {
          if (res?.rows?.sampleType === 'MEASURE' && chartRef.current) {
            chartRef.current.handleSearch({
              controlId: id,
            });
            handleQueryCPK(() => {});
          }
        });
    }
  };

  const handleChangeTime = (value, type) => {
    const newParams = {
      ...queryParamsProxy.params,
      [type]: value ? value.format('YYYY-MM-DD HH:mm:ss') : null,
    };
    setQueryParams(newParams as QueryParams);
  };

  const handleReset = () => {
    setQueryParams(null);
  };

  const handleSubmit = () => {
    const urlQueryParams = getQueryParam();
    fetchControlChartGraphicData.run({
      params: {
        controlId: id,
        ...queryParams,
        ...(urlQueryParams as object),
      },
    });
    // @ts-ignore
    const { fromDate, toDate } = queryParams || {};
    setTimeParmas({
      creationDateFrom: fromDate,
      creationDateTo: toDate,
    });
    if (fetchControlChartGraphicData.data?.sampleType === 'MEASURE' && chartRef.current) {
      chartRef.current.handleSearch({
        fromDate: fromDate || null,
        toDate: toDate || null,
        controlId: id,
      });
      handleQueryCPK(() => {});
    }
  };

  const handleQueryCPK = callbackFunction => {
    const urlQueryParams = getQueryParam();
    fetchCPKData.run({
      params: {
        controlId: id,
        ...queryParams,
        ...(urlQueryParams as object),
      },
      onSuccess: () => {
        callbackFunction();
      },
    });
  };



  const normalityProps = {
    ...timeParmas,
    id,
    showTitle: true,
    controlChartCode: fetchControlChartGraphicData.data?.controlCode,
    controlChartDesc: fetchControlChartGraphicData.data?.controlDesc,
  };


  const filterList = fetchControlChartGraphicData?.data?.data;
  const finalList = filterList?.filter(item => item.hideFlag !== 'Y');
  const finalObj = {
    data: {
      ...fetchControlChartGraphicData.data,
      data: finalList,
    },
  };
  // dataObj = tempDataObj || finalObj;

  return (
    <div className="hmes-style">
      <Content style={{ height: '100%' }}>
        <Collapse bordered={false} defaultActiveKey={['information']} trigger="icon">
          <Panel
            header={
              <div className={styles['chart-card-title']}>
                <div className={styles['analys-code']}>
                  {`${intl
                    .get(`${modelPrompt}.controlChartCode`)
                    .d('控制控制图编码')}: ${fetchControlChartGraphicData.data?.controlCode || ''}`}
                </div>
                <div className={styles['analys-code']}>
                  {`${intl
                    .get(`${modelPrompt}.controlChartDesc`)
                    .d('控制控制图描述')}: ${fetchControlChartGraphicData.data?.controlDesc || ''}`}
                </div>
              </div>
            }
            key="information"
            showArrow={false}
          >
            <Form columns={3} labelWidth={140}>
              <DateTimePicker
                mode={ViewMode.dateTime}
                placeholder={intl.get(`${modelPrompt}.startingTime`).d('开始时间')}
                label={intl.get(`${modelPrompt}.sampleEntryTimeFrom`).d('样本时间从')}
                onChange={value => handleChangeTime(value, 'fromDate')}
                max={queryParams?.toDate}
              />
              <DateTimePicker
                mode={ViewMode.dateTime}
                placeholder={intl.get(`${modelPrompt}.endingTime`).d('结束时间')}
                label={intl.get(`${modelPrompt}.sampleEntryTimeTo`).d('样本时间至')}
                onChange={value => handleChangeTime(value, 'toDate')}
                min={queryParams?.fromDate}
              />
              <div className="btn">
                <Button onClick={handleReset} type={ButtonType.reset}>
                  {intl.get('tarzan.common.button.reset').d('重置')}
                </Button>
                <Button
                  onClick={handleSubmit}
                  style={{
                    backgroundColor: '#29BECE',
                    borderColor: '#29BECE',
                    color: 'white',
                  }}
                >
                  {intl.get('tarzan.common.button.search').d('查询')}
                </Button>
              </div>
            </Form>
          </Panel>
        </Collapse>
        {fetchControlChartGraphicData.data ? (
          <GraphicChart
            chartData={finalObj.data}
            loading={fetchControlChartGraphicData.loading}
            chartUuid={chartUuid}
            getChangeData={getChartData}
          />
        ) : null}
        {fetchControlChartGraphicData.data?.sampleType === 'MEASURE' ? (
          <>
            <NormalityTest {...normalityProps} ref={chartRef} />
            <CPKCard dataSource={fetchCPKData.data} />
          </>
        ) : null}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hspc.controlChartMaintain', 'tarzan.hspc.chartInfo', 'tarzan.common'],
})(HistoryChartDisplay);
