import React, { useEffect, useState, useMemo } from 'react';
import moment from 'moment';
import { Spin } from 'choerodon-ui';
import { DataSet, Select } from 'choerodon-ui/pro';
import { Content } from 'components/Page';
import { FullScreenContainer } from '@jiaminghi/data-view-react';
import request from 'utils/request';
import intl from 'utils/intl';
import notification from "utils/notification";
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
// import { fetchDefaultSite } from '@utils/utils';
import EquipmentAccept from './components/EquipmentAccept';
import ProductionConfirm from './components/ProductionConfirm';
import WorkshopAcceptConfirm from './components/WorkshopAcceptConfirm';
import EquipmentInfo from './components/EquipmentInfo';
import { TopFilterDS } from './Stores';
import styles from './index.module.less';


const modelPrompt = 'tarzan.wms.DeviceStatusMonitoringBoard';
const tenantId = getCurrentOrganizationId();
// 设备信息播报
const equipmentInfoUrl = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-ledger-summary/ledger-list`;
// 设备总数
const equipmentTotalUrl = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-ledger-summary/asset-num`;


const CurrentTime = () => {
  const [nowDate, setNowDate] = useState(moment(new Date()).format('YYYY-MM-DD'));
  const [nowWeek, setNowWeek] = useState('');
  const [nowTime, setNowTime] = useState(moment(new Date()).format('HH:mm:ss'));

  const weekDay = () => {
    const dateList = ['星期日','星期一','星期二','星期三','星期四','星期五','星期六'];
    setNowWeek(dateList[new Date().getDay()]);
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setNowDate(moment(new Date()).format('YYYY-MM-DD'));
      weekDay();
      setNowTime(moment(new Date()).format('HH:mm:ss'));
    }, 1000)

    return () => {  // 每次卸载都执行此函数，清楚定时器
      clearTimeout(timer)
    }
  }, []);
  return <span className={styles['dashboard-title-right-three-time-text']}> {nowDate}&nbsp;&nbsp;{nowWeek}&nbsp;&nbsp;{nowTime} </span>;
};

const Main = ({isFullScreen}) => {

  const [initLoading, setInitLoading] = useState<boolean>(false);
  const [timers, setTimers] = useState<any>(null);
  const [assetSetId, setAssetSetId] = useState<any>(null);

  const topFilterDs = useMemo(() => new DataSet(TopFilterDS()), []);
  const [equipmentInfoData, setEquipmentInfoData] = useState([]);
  const [totalData, setTotalData] = useState(null);


  const getTimers = async () => {
    const url = `/hpfm/v1/${tenantId}/lovs/value/batch?QMS.MANAGEMENT_FREQUENCY=QMS.MANAGEMENT_FREQUENCY`
    const result = await request(url, {
      method: 'GET',
    });
    const data = result['QMS.MANAGEMENT_FREQUENCY'].filter(item => item.value === 'INCOME')
    if (data.length > 0) {
      setTimers(Number(data[0].meaning))
    }
  }

  useEffect(() => {
    getTimers().then(async () => {
      const url = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-ledger-summary/get-asset-class`
      const result: any = await request(url, {
        method: 'GET',
        query: { tenantId, page: 0, size: 10000 },
      });
      if (result?.length > 0) {
        topFilterDs.current?.set('assetSetLov', result.filter(item => item.assetSetNum === 'ProductioneQuipment')[0]);
        getEquipmentInfoData();
        getEquipmentTotalData();
      }
    });
  }, []);

  useEffect(() => {
    let time
    if(timers) {
      time = setInterval(() => {
        getEquipmentTotalData();
        getEquipmentInfoData();
      }, timers * 60000)
    }
    return () => {
      clearInterval(time)
    }
  }, [timers])

  useEffect(() => {
    topFilterDs.addEventListener("update", handleChangeAssetSetLov);
    return () => {
      topFilterDs.removeEventListener("update", handleChangeAssetSetLov);
    };
  }, []);

  const handleChangeAssetSetLov = ({ name, value }) => {
    if (name === "assetSetLov" && value?.assetSetId) {
      setAssetSetId(value.assetSetId);
      getEquipmentInfoData();
      getEquipmentTotalData();
    }
  };

  /**
   * 设备信息滚动播报
   */
  const getEquipmentInfoData = async () => {
    setInitLoading(true);
    const result = await request(equipmentInfoUrl, {
      method: 'GET',
      query: { assetSetId: topFilterDs.current?.get('assetSetId')},
    });
    if(result && !result.failed) {
      setEquipmentInfoData(result);
    } else {
      notification.warning({ description: result.message });
    }
    setInitLoading(false)
  };

  const getEquipmentTotalData = async () => {
    setInitLoading(true);
    const result = await request(equipmentTotalUrl, {
      method: 'GET',
      query: { assetSetId: topFilterDs.current?.get('assetSetId')},
    });
    if((result || Number(result) === 0) && !result.failed) {
      setTotalData(result);
    } else {
      notification.warning({ description: result.message });
    }
    setInitLoading(false)
  };


  return (
    <>
      <Content style={{ padding: 0, margin: 0, height: '100%' }}>
        {/* loading */}
        <Spin spinning={initLoading}>
          <div className={styles['dashboard-container']}>
            {/* header */}
            <div className={styles['dashboard-title']}>
              <div className={styles['dashboard-title-left']}>
                <div className={styles['dashboard-title-left-one']} />
                <div className={styles['dashboard-title-left-two']} />
                <div className={styles['dashboard-title-left-three']}>
                  {intl.get(`${modelPrompt}.title.DeviceStatusMonitoringBoard`).d('设备台账汇总看板')}
                </div>
              </div>
              <div className={styles['dashboard-title-right']}>
                <div className={styles['dashboard-title-right-one']}>
                  <div className={styles['dashboard-title-right-one-text']}>
                    {intl.get(`${modelPrompt}.field.totalNumberOfDevices`).d('设备总数')}
                  </div>
                  <div className={styles['dashboard-title-left-one-dom']}>
                    {totalData}
                  </div>
                </div>
                <div className={styles['dashboard-title-right-two']}>
                  <div className={styles['dashboard-title-right-two-text']}>
                    {intl.get(`${modelPrompt}.field.assetClassification`).d('资产分类')}
                  </div>
                  <div className={styles['dashboard-title-left-two-dom']}>
                    <div className={styles['top-select']}>
                      <Select
                        dataSet={topFilterDs}
                        name="assetSetLov"
                      />
                    </div>
                  </div>
                </div>
                <div className={styles['dashboard-title-right-three']}>
                  <div className={styles['dashboard-title-right-three-time']}>
                    <CurrentTime/>
                  </div>
                  <div className={styles['dashboard-title-right-three-home']}/>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className={styles['dashboard-content']}>
              <div className={styles['dashboard-row-up']}>
                <div className={styles['dashboard-item-left']}>
                  <EquipmentAccept isFullScreen={isFullScreen} assetSetId={assetSetId} timers={timers}/>
                </div>
                <div className={styles['dashboard-item-middle']}>
                  <WorkshopAcceptConfirm assetSetId={assetSetId} timers={timers}/>
                </div>
                <div className={styles['dashboard-item-right']}>
                  <ProductionConfirm isFullScreen={isFullScreen} assetSetId={assetSetId} timers={timers} />
                </div>
              </div>
              <div className={styles['dashboard-col-down']}>
                <EquipmentInfo data={equipmentInfoData}/>
              </div>
            </div>
          </div>
        </Spin>
      </Content>
    </>
  );
};

const DeviceStatusMonitoringBoard = () => {
  const [isFullScreen, setIsFullScreen] = useState(false); // 是否全屏

  const windowFullScreenChange = () => {
    if (document.fullscreenElement) {
      // console.log('进入全屏');
      setIsFullScreen(true);
    } else {
      // console.log('退出全屏');
      setIsFullScreen(false);
    }
  };
  useEffect(() => {
    document.addEventListener('fullscreenchange', windowFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', windowFullScreenChange);
    };
  }, []);

  return (
    <>
      <div className={styles['screen-container']}>
        {isFullScreen ? (
          <FullScreenContainer>
            <Main
              isFullScreen={isFullScreen}
            />
          </FullScreenContainer>
        ) : (
          <Main
            isFullScreen={isFullScreen}
          />
        )}
      </div>
    </>
  );
};
export default DeviceStatusMonitoringBoard;
