import React, { useMemo, useState, useEffect } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import request from 'utils/request';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from './stores';

const modelPrompt = 'InspectionTimeRule';
const tenantId = getCurrentOrganizationId();

const InspectionTimeRule = observer((props) => {
  const {
    tableDs,
  } = props;

  const [edit, setEdit] = useState(false);


  useEffect(() => {
    request(`/tznm/v1/${tenantId}/mt-mod-site/user-organization/site/lov/ui?page=0&size=1000`, {
      method: 'GET',
    }).then(res => {
      if (res && res.content && res.content.length === 1) {
        tableDs.queryDataSet.current?.set('siteLov',
          {
            siteId: res.content[0].siteId,
            siteCode: res.content[0].siteCode,
            siteName: res.content[0].siteName,
          });
      } else {
        tableDs.queryDataSet.current?.set('siteLov', {});
      }
    });
  }, [])

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'orderNumber',
        align: ColumnAlign.center,
      },
      {
        name: 'siteLov',
        width: 150,
        editor: (record) => record.getState('editing'),
      },
      {
        name: 'inspectBusinessType',
        width: 120,
        editor:(record) => record.getState('editing'),
      },
      {
        name: 'shiftCode',
        width: 120,
        editor:(record) => record.getState('editing'),
      },
      {
        name: 'reminderLevel',
        editor: (record) => record.getState('editing'),
      },
      {
        name: 'processWorkcellLov',
        width: 120,
        editor: (record) => record.getState('editing'),
      },
      {
        name: 'provingTime',
        width: 180,
        align: ColumnAlign.center,
        editor: (record) => record.getState('editing'),
      },
      {
        name: 'firstReminderInterval',
        width: 180,
        editor: (record) => record.getState('editing'),
      },
      {
        name: 'firstReminderByLov',
        editor: (record) => record.getState('editing'),
        width:180,
      },
      {
        name: 'secondReminderInterval',
        width: 180,
        editor: (record) => record.getState('editing'),
      },
      {
        name: 'secondReminderByLov',
        width:180,
        editor: (record) => record.getState('editing'),
      },
      {
        name: 'thirdReminderInterval',
        width: 180,
        editor: (record) => record.getState('editing'),
      },
      {
        name: 'threeReminderByLov',
        width:180,
        editor: (record) => record.getState('editing'),
      },
      {
        name: 'creatorName',
      },
      {
        name: 'creationDate',
        width: 150,
      },
    ];
  }, []);

  const handleAdd = () => {
    if (!edit) {
      setEdit(true);
    }
    const record = tableDs.create({ orderNumber : tableDs.toData().length + 1});
    record.setState('editing', true);
  }

  const handleSave = async() => {
    const validate = await tableDs.validate();
    if (!validate) {
      return;
    }
    const data = tableDs.toJSONData();
    const dataList = data.map(item => ({
      ...item,
      ...item.provingTime,
    }))
    request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-inspect-time-rule/batch/update`, {
      method: 'POST',
      body: dataList,
    }).then(res => {
      if (res.success) {
        tableDs.query()
        notification.success({});
        setEdit(false);
      } else {
        notification.error({message: res?.message})
      }
    })
  }

  const handleEdit = () => {
    tableDs.selected.forEach(record =>{
      record.setState('editing', true);
    })
    if (!edit) {
      setEdit(true);
    }
  }

  const handleDelete = () => {
    request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-inspect-time-rule/batch/delete`, {
      method: 'POST',
      body: tableDs.selected.map(record => record.get('inspectTimeRuleId')),
    }).then(res => {
      if (res.success) {
        tableDs.query()
        notification.success({});
      } else {
        notification.error({ message: res?.message })
      }
    })
  };

  const handleCancel = () => {
    tableDs.query();
    setEdit(false);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('检验时间提醒规则配置')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          onClick={handleEdit}
          disabled={!tableDs.selected?.length}
        >
          {intl.get(`${modelPrompt}.editButton`).d('编辑')}
        </PermissionButton>
        {edit && (
          <>
            <PermissionButton
              type="c7n-pro"
              onClick={handleCancel}
            >
              {intl.get(`${modelPrompt}.cancelButton`).d('取消')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              onClick={handleSave}
            >
              {intl.get(`${modelPrompt}.saveButton`).d('保存')}
            </PermissionButton>
          </>
        )}
        {!edit && (
          <PermissionButton
            type="c7n-pro"
            onClick={handleDelete}
            disabled={!tableDs.selected?.length}
          >
            {intl.get(`${modelPrompt}.deleteButton`).d('删除')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="inspection_time_rule"
          customizedCode="inspection_time_rule"
        />
      </Content>
    </div>
  );
})

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(InspectionTimeRule),
);
