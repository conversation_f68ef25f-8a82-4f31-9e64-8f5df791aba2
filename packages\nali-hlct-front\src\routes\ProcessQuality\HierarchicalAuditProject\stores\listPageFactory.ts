import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.qms.hierarchicalAuditProject';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    selection: false,
    autoQuery: true,
    queryDataSet: new DataSet({
      fields: [
        {
          label: intl.get(`${modelPrompt}.form.layerReviewItemCode`).d('项目编码'),
          name: 'layerReviewItemCode',
          type: FieldType.string,
        },
        {
          label: intl.get(`${modelPrompt}.form.projectName`).d('项目内容'),
          name: 'projectName',
          type: FieldType.string,
        },
        {
          label: intl.get(`${modelPrompt}.form.projectClassify`).d('项目分类'),
          name: 'projectClassify',
          type: FieldType.string,
          lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM.PROJECT_FROM',
          lovPara: { tenantId },
        },
        {
          label: intl.get(`${modelPrompt}.form.projectFrom`).d('标准来源'),
          name: 'projectFrom',
          type: FieldType.string,
        },
        {
          name: 'operationLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.operation`).d('工艺'),
          lovCode: 'YP_QIS.OPERATION',
          lovPara: { tenantId },
          ignore: FieldIgnore.always,
        },
        {
          name: 'operationId',
          bind: 'operationLov.operationId',
        },
        {
          label: intl.get(`${modelPrompt}.form.levelFlays`).d('适用层级'),
          name: 'levelFlays',
          type: FieldType.string,
          lookupCode: 'YP.QIS.LEVEL',
          lovPara: { tenantId },
          multiple: true,
        },
        {
          label: intl.get(`${modelPrompt}.form.layerReviewItemStatus`).d('状态'),
          name: 'layerReviewItemStatus',
          type: FieldType.string,
          lookupCode: 'YP.QIS_LAYER_REVIEW_ITEM_STATUS',
          lovPara: { tenantId },
        },
      ],
    }),
    fields: [
      {
        label: intl.get(`${modelPrompt}.table.layerReviewItemCode`).d('项目编码'),
        name: 'layerReviewItemCode',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.projectName`).d('项目内容'),
        name: 'projectName',
        type: FieldType.string,
        required: true,
      },
      {
        label: intl.get(`${modelPrompt}.table.projectClassify`).d('项目分类'),
        name: 'projectClassify',
        type: FieldType.string,
        lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM.PROJECT_FROM',
        lovPara: { tenantId },
        required: true,
      },
      {
        label: intl.get(`${modelPrompt}.table.projectFrom`).d('标准来源'),
        name: 'projectFrom',
        type: FieldType.string,
        required: true,
      },
      {
        name: 'operationLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.operation`).d('工艺'),
        lovCode: 'YP_QIS.OPERATION',
        lovPara: { tenantId },
        ignore: FieldIgnore.always,
        textField: 'operationName',
        multiple: true,
        required: true,
      },
      {
        name: 'operationIdList',
        multiple: ',',
        bind: 'operationLov.operationId',
      },
      {
        name: 'operationNameList',
        type: FieldType.string,
        multiple: ',',
        bind: 'operationLov.operationName',
      },
      {
        label: intl.get(`${modelPrompt}.table.levelFlays`).d('适用层级'),
        name: 'levelFlays',
        required: true,
        type: FieldType.string,
        lookupCode: 'YP.QIS.LEVEL',
        lovPara: { tenantId },
        multiple: true,
      },
      {
        label: intl.get(`${modelPrompt}.table.layerReviewItemFrequency`).d('频率'),
        name: 'layerReviewItemFrequency',
        type: FieldType.string,
        lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM_FREQUENCY',
        lovPara: { tenantId },
        required: true,
      },
      {
        label: intl.get(`${modelPrompt}.table.layerReviewItemStatus`).d('状态'),
        name: 'layerReviewItemStatus',
        type: FieldType.string,
        trueValue: 'Y',
        defaultValue: 'Y',
        falseValue: 'N',
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-layer-review-item`,
          method: 'GET',
        };
      },
      destroy: ({ data }) => {
        return {
          url: `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-layer-review-item`,
          data,
          method: "DELETE",
        };
      },
    },
  });

export default listPageFactory;
