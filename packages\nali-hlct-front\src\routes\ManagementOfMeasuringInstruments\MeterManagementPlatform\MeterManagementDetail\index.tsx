/**
 * @Description: 检验项目组维护查询界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:29:43
 * @LastEditTime: 2023-05-24 16:56:17
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { DataSet, Table, Modal,  } from 'choerodon-ui/pro';
import { Content } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import withProps from 'utils/withProps';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from 'utils/intl';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { curriculumDS } from '../stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.qms.meterManagementPlatform';

let modal;

const MeterManagementDetail = props => {
  const { ds, curriDS, handleAdd, history } = props;

  useEffect(() => {
    ds.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.LIST`,
    );

    if (ds?.currentPage) {
      ds.query(props.ds.currentPage);
    } else {
      ds.query();
    }
  }, []);

  const handleLineEdit = (record, flag) => {
    handleAdd(record, flag);
  };

  // 导出传参
  const handleQuerySearchForm = id => {
    return {
      ...curriDS.queryDataSet?.toData()[0],
      msToolManageId: id,
    };
  };

  const handleViewDrawer = id => {
    curriDS.setQueryParameter('msToolManageId', id);
    curriDS.query();
    modal = Modal.open({
      ...drawerPropsC7n({ ds: curriDS }),
      key: Modal.key(),
      closeOnLocationChange: true,
      title: (
        <div
          style={{
            width: 'calc(100% - 30px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.title.historyQuery`).d('履历查看')}</div>
          <ExcelExport
            style={{ float: 'right', marginRight: 20 }}
            requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/ms-tool-manages/his-list/export/ui`}
            method="get"
            otherButtonProps={{ type: 'primary' }}
            queryParams={() => handleQuerySearchForm(id)}
          >
            {intl.get(`tarzan.common.button.export`).d('导出')}
          </ExcelExport>
        </div>
      ),
      style: {
        width: '75%',
      },
      children: (
        <div className="hmes-style">
          <Content>
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={curriDS}
              columns={curriColumns}
              searchCode="curriculumTable"
              customizedCode="curriculumTable"
            />
            ,
          </Content>
        </div>
      ),
    });
  };

  const curriColumns: ColumnProps[] = [
    {
      name: 'toolCode',
      width: 180,
      align: ColumnAlign.center,
    },
    {
      name: 'siteCode',
      align: ColumnAlign.center,
    },
    {
      name: 'siteName',
      align: ColumnAlign.center,
    },
    {
      name: 'categoryDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'speciesDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'modelCode',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'range',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'resolution',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'assetNum',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'usingStatusDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'userName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'usingDepartmentDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'responName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'prodLineName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'processName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'otherPosition',
      width: 120,
    },
    {
      name: 'manufacturingNum',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'manufacturer',
      width: 120,
      align: ColumnAlign.center,
    },
    // {
    //   name: 'verStatus',
    //   width: 120,
    //   align: ColumnAlign.center,
    // },
    {
      name: 'verificationMethodDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'reminderPeriod',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'reportCode',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationAgency',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationPeriod',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'operationType',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'createdByDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'lastUpdateDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'sourceDocType',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'sourceDocNum',
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <a onClick={() => onHandleDetail(record)}>{record?.get('sourceDocNum')}</a>
      ),
    },
    {
      title: intl.get(`${modelPrompt}.operation.inspectReportQuery`).d('检定报告查看'),
      name: 'enclosure',
      width: 120,
      align: ColumnAlign.center,
      // renderer: () => (
      //   <>
      //     <a>预览</a>
      //   </>
      // ),
      lock: ColumnLock.right,
    },
  ];

  const onHandleDetail = record => {
    const type = record.get('sourceDocType');
    if (type === 'MS_APPLICATION_DOC') {
      modal.close();
      history.push(`/hmes/measure-have/platform/dist/${record.get('sourceDocId')}`);
    } else if (type === 'INSPECT_DOC') {
      modal.close();
      history.push(
        `/hmes/measure-have/platform/inspect/${record.get('verificationMethod')}/${record.get(
          'sourceDocId',
        )}`,
      );
    } else if (type === 'MS_TOOL_TRANSFER_APPLY') {
      modal.close();
      history.push(`/hwms/measuring-transfer-platform/detail/${record.get('sourceDocId')}`);
    } else if (type === 'MS_TOOL_SCRAP_APPLY') {
      modal.close();
      history.push(`/hwms/measuring-scrap-platform/detail/${record.get('sourceDocId')}`);
    } else if (type === 'MSA_TASK') {
      modal.close();
      history.push(`/hwms/msa-analysis-management-platform/detail/${record.get('sourceDocId')}`);
    }
  };

  const columns: ColumnProps[] = [
    {
      name: 'siteCode',
      align: ColumnAlign.center,
    },
    {
      name: 'siteName',
      align: ColumnAlign.center,
    },
    {
      name: 'toolCode',
      align: ColumnAlign.center,
      width: 180,
      renderer: ({ value, record }) => {
        return <a onClick={() => handleLineEdit(record, 'edit')}>{value}</a>;
      },
    },
    {
      name: 'categoryCode',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'speciesDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'modelCode',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'range',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'resolution',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'assetNum',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'usingStatus',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'userName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'usingDepartmentDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'responName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'prodLineName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'processName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'otherPosition',
      width: 120,
    },
    {
      name: 'manufacturingNum',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'manufacturer',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationStatus',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationMethod',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'reminderPeriod',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationAgency',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationPeriod',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'lastVerificationDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'lastVerificationResultDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationExpDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'reportCode',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'creationDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'createdByDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'lastUpdateDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'lastUpdatedByDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'scrappedDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'remark',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      title: intl.get(`${modelPrompt}.operation.resumeReview`).d('履历查看'),
      width: 120,
      align: ColumnAlign.center,
      renderer: (record: any) => (
        <>
          <a
            onClick={() => {
              handleViewDrawer(record.record.data.msToolManageId);
            }}
          >
            {intl.get(`${modelPrompt}.operation.look`).d('查看')}
          </a>
        </>
      ),
      lock: ColumnLock.right,
    },
    {
      title: intl.get(`${modelPrompt}.operation.inspectReportQuery`).d('检定报告查看'),
      name: 'enclosure',
      width: 120,
      align: ColumnAlign.center,
      // renderer: () => (
      //   <>
      //     <a>预览</a>
      //   </>
      // ),
      lock: ColumnLock.right,
    },
  ];

  return (
    <Table
      queryBar={TableQueryBarType.filterBar}
      queryBarProps={{
        fuzzyQuery: false,
      }}
      dataSet={ds}
      columns={columns}
      searchCode="MeterManagementDetail"
      customizedCode="MeterManagementDetail"
    />
    // <div className="hmes-style">
    //   <Content>
    //     <Table
    //       queryBar={TableQueryBarType.filterBar}
    //       queryBarProps={{
    //         fuzzyQuery: false,
    //       }}
    //       dataSet={ds}
    //       columns={columns}
    //       searchCode="MeterManagementDetail"
    //       customizedCode="MeterManagementDetail"
    //     />
    //     ,
    //   </Content>
    // </div>
  );
};

export default withProps(
  () => {
    const curriDS = new DataSet({
      ...curriculumDS(),
    });
    return {
      // tableDS,
      curriDS,
    };
  },
  { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
)(MeterManagementDetail);
