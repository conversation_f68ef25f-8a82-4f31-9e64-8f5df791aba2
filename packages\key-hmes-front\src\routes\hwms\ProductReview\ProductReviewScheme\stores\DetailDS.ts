/**
 * @Description: 产品审核方案-详情界面DS
 */
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hwms.ProductReviewScheme';


const detailDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'productRevSchemeId',
  fields: [
    {
      name: 'productRevSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeCode`).d('产品审核方案编码'),
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'siteName',
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('productRevSchemeCode');
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'productRevSchemeName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeName`).d('方案名称'),
      required: true,
    },
    {
      name: 'productRevSchemeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_SCHEME_STATUS',
      lovPara: { tenantId },
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: { tenantId },
      required: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record }) => {
          return ['NEW', 'REJECTED'].includes(record.get('productRevSchemeStatus'));
        },
        disabled: ({ record }) => {
          return !['NEW', 'REJECTED'].includes(record.get('productRevSchemeStatus'));
        },
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序编码'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellCode',
      lovPara: {
        tenantId,
        workcellType: 'PROCESS',
        userFlag: '',
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId') || (!['NEW', 'REJECTED'].includes(record.get('productRevSchemeStatus')) && record.get('productRevSchemeCode'));
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            workcellType: 'PROCESS',
            userFlag: '',
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'workcellId',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'workcellCode',
      bind: 'processWorkcellLov.workcellCode',
    },
    {
      name: 'workcellName',
      bind: 'processWorkcellLov.workcellName',
      label: intl.get(`${modelPrompt}.workcellName`).d('工序描述'),
      disabled: true,
    },
    {
      name: 'productDate',
      label: intl.get(`${modelPrompt}.productDate`).d('量产时间'),
      type: FieldType.dateTime,
      required: true,
      format: 'YYYY-MM-DD',
    },
    {
      name: 'cusMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件编码'),
    },
    {
      name: 'cusMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialName`).d('客户零件描述'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-scheme/info/ui`,
        method: 'GET',
      };
    },
  },
});

const taskDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  selection: false,
  primaryKey: 'productReviewItemId',
  fields: [
    {
      name: 'productReviewItemId',
    },
    {
      name: 'productReviewItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemCode`).d('产品审核项目编码'),
      disabled: true,
    },
    {
      name: 'productReviewItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemDesc`).d('项目描述'),
      required: true,
    },
    {
      name: 'productReviewMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewMethod`).d('测试方法'),
      lookupCode: 'MT.QMS.INSPECT_METHOD',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'specRequire',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specRequire`).d('规格要求'),
    },
    {
      name: 'reviewFrequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewFrequency`).d('测试频率'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_FREQUENCY',
      lovPara: { tenantId },
    },
    {
      name: 'weightCoefficient',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.weightCoefficient`).d('特征加权系数'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_WEIGHT_COEFFICIENT',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'recordDataFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recordDataFlag`).d('是否记录测试数据'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'fromOrtFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromOrtFlag`).d('是否来源于ORT'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

const reviewItemDS: () => DataSetProps = () => ({
  autoCreate: true,
  dataKey: 'rows',
  primaryKey: 'productReviewItemId',
  fields: [
    {
      name: 'type',
    },
    {
      name: 'productReviewItemCode',
      disabled: true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemCode`).d('产品审核项目编码'),
    },
    {
      name: 'productReviewItemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemType`).d('项目类别'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_ITEM_TYPE',
      lovPara: { tenantId },
      required: true,
      dynamicProps: {
        disabled: ({ record }) => record?.get('type'),
      },
    },
    {
      name: 'productReviewItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemDesc`).d('项目描述'),
      required: true,
    },
    {
      name: 'productReviewMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewMethod`).d('测试方法'),
      lookupCode: 'MT.QMS.INSPECT_METHOD',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'specRequire',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specRequire`).d('规格要求'),
    },
    {
      name: 'reviewFrequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewFrequency`).d('测试频率'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_FREQUENCY',
      lovPara: { tenantId },
    },
    {
      name: 'weightCoefficient',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.weightCoefficient`).d('特征加权系数'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_WEIGHT_COEFFICIENT',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'recordDataFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recordDataFlag`).d('是否记录测试数据'),
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'fromOrtFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromOrtFlag`).d('是否来源于ORT'),
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('是否启用'),
      lookupCode: 'MT.YES_NO',
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
      disabled: true,
    },
  ],
});


export { detailDS, taskDS, reviewItemDS };
