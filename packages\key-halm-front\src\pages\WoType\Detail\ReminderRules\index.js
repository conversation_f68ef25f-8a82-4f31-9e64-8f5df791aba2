/**
 * 工单类型明细页
 * @date 2020/07/01
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2019,Hand
 */

import { connect } from 'dva';
import React from 'react';
import { Collapse } from 'choerodon-ui';
import {Form, Output, NumberField, Lov } from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';

@connect(() => ({
  tenantId: getCurrentOrganizationId(),
}))
@observer
class ReminderRules extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const commonPromptCode = 'amtc.woType.view.message';
    const { reminderRulesDS, isNew, editFlag } = this.props;
    return (
      <React.Fragment>
        <Collapse
          bordered={false}
          defaultActiveKey={['C']}
          className="wo-type-detail-collapse"
        >
          <Collapse.Panel
            key="C"
            header={intl.get(`${commonPromptCode}.defaultExecuteRule`).d('执行规则')}
          >
            {isNew || editFlag ? (
              <Form dataSet={reminderRulesDS} columns={3} labelWidth={180}>
                <NumberField name="firstReminderInterval" />
                <Lov name="firstReminderByLov" />
                <NumberField name="secondReminderInterval" />
                <Lov name="secondReminderByLov" />
                <NumberField name="thirdReminderInterval" />
                <Lov name="thirdReminderByLov" />
              </Form>
            ) : (
              <Form dataSet={reminderRulesDS} columns={3} labelWidth={180}>
                <Output name="firstReminderInterval" />
                <Output name="firstReminderByName" />
                <Output name="secondReminderInterval" />
                <Output name="secondReminderByName" />
                <Output name="thirdReminderInterval" />
                <Output name="thirdReminderByName" />
              </Form>
            )}
          </Collapse.Panel>
        </Collapse>
      </React.Fragment>
    );
  }
}
export default ReminderRules;
