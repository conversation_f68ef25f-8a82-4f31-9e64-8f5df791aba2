import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.qms.raiseHandReport';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    selection: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          label: intl.get(`${modelPrompt}.form.problemApplyCode`).d('举手申请编码'),
          name:'problemApplyCode',
          type:FieldType.string,
        },
        {
          label: intl.get(`${modelPrompt}.form.problemTitle`).d('问题标题'),
          name:'problemTitle',
          type:FieldType.string,
        },
        {
          label: intl.get(`${modelPrompt}.form.problemApplyStatus`).d('状态'),
          name:'problemApplyStatus',
          lookupCode: 'YP.QIS.PROBLEM_APPLY_STATUS',
          lovPara: { tenantId },
          type:FieldType.string,
        },
        {
          label: intl.get(`${modelPrompt}.form.apply`).d('申请人'),
          name:'apply',
          ignore:FieldIgnore.always,
          lovCode: 'YP.QIS.EMPLOYEE_POSITION',
          lovPara: { tenantId },
          type:FieldType.object,
        },
        {
          name: 'applyByList',
          bind: 'apply.employeeId',
          transformRequest:(value)=>{
            return [value];
          },
        },
        {
          label: intl.get(`${modelPrompt}.form.applyTimeBegin`).d('申请时间从'),
          name:'applyTimeBegin',
          type:FieldType.dateTime,
          max: 'applyTimeEnd',
        },
        {
          label: intl.get(`${modelPrompt}.form.applyTimeEnd`).d('申请时间至'),
          name:'applyTimeEnd',
          type:FieldType.dateTime,
          min: 'applyTimeBegin',
        },
        {
          label: intl.get(`${modelPrompt}.form.applyByDept`).d('申请部门'),
          name:'applyByDept',
          ignore:FieldIgnore.always,
          lovCode: 'YP.QIS.COMPANY_UNIT',
          lovPara: { tenantId },
          type:FieldType.object,
        },
        {
          name: 'applyByDeptId',
          bind: 'applyByDept.unitId',
        },
        {
          label: intl.get(`${modelPrompt}.form.siteName`).d('站点'),
          name:'siteName',
          type:FieldType.object,
          ignore:FieldIgnore.always,
          lovCode: 'MT.MODEL.SITE',
          lovPara: { tenantId },
        },
        {
          name:'siteId',
          bind: 'siteName.siteId',
        },
        {
          label: intl.get(`${modelPrompt}.form.prodLineName`).d('生产线'),
          name:'prodLineName',
          type:FieldType.object,
          ignore:FieldIgnore.always,
          lovCode: 'MT.MODEL.PRODLINE',
          dynamicProps: {
            disabled: ({record})=>!record.get('siteId'),
            lovPara:({record})=>({
              siteId: record.get('siteId'),
              tenantId,
            }),
          },
        },
        {
          name: 'prodLineId',
          bind: 'prodLineName.prodLineId',
        },
        {
          label: intl.get(`${modelPrompt}.form.workcell`).d('工作单元'),
          name:'operationDescription',
          type:FieldType.object,
          ignore:FieldIgnore.always,
          textField: 'workcellName',
          lovCode: 'MT.MODEL.WORKCELL_SITE',
          dynamicProps: {
            lovPara: ({ record }) => ({
              tenantId,
              siteIds: [record?.get('siteId')],
            }),
            disabled: ({record})=>!record.get('siteId'),
          },
        },
        {
          name: 'operationId',
          bind: 'operationDescription.workcellId',
        },
        {
          label: intl.get(`${modelPrompt}.form.equipmentCode`).d('设备'),
          name:'equipmentCode',
          type:FieldType.object,
          ignore:FieldIgnore.always,
          lovCode: 'MT.MODEL.EQUIPMENT',
          lovPara: { tenantId },
        },
        {
          name:'equipmentId',
          bind: 'equipmentCode.equipmentId',
        },
        {
          label: intl.get(`${modelPrompt}.form.reviewResult`).d('审批结果'),
          name:'reviewResult',
          lookupCode: 'YP.QIS.PROBLEM_TASK_RESULT',
          lovPara: { tenantId },
          type:FieldType.string,
        },
        {
          label: intl.get(`${modelPrompt}.form.review`).d('审批人'),
          name:'review',
          type:FieldType.object,
          ignore:FieldIgnore.always,
          lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
          lovPara: { tenantId },
        },
        {
          name: 'reviewBy',
          bind: 'review.id',
        },
        {
          label: intl.get(`${modelPrompt}.form.reviewTimeBegin`).d('审批时间从'),
          name:'reviewTimeBegin',
          type:FieldType.dateTime,
          max: 'reviewTimeEnd',
        },
        {
          label: intl.get(`${modelPrompt}.form.reviewTimeEnd`).d('审批时间至'),
          name:'reviewTimeEnd',
          type:FieldType.dateTime,
          min: 'reviewTimeBegin',
        },
      ],
    }),
    fields: [
      {
        label: intl.get(`${modelPrompt}.table.problemApplyCode`).d('举手申请编码'),
        name:'problemApplyCode',
        type:FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.problemTitle`).d('问题标题'),
        name:'problemTitle',
        required: true,
        type:FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.problemApplyStatus`).d('状态'),
        name:'problemApplyStatus',
        lookupCode: 'YP.QIS.PROBLEM_APPLY_STATUS',
        type:FieldType.string,
        lovPara: { tenantId },
        defaultValue: 'NEW',
      },
      {
        label: intl.get(`${modelPrompt}.table.siteObj`).d('站点'),
        name:'siteObj',
        lovCode: 'MT.MODEL.SITE',
        ignore: FieldIgnore.always,
        lovPara: { tenantId },
        type:FieldType.object,
        textField: 'siteName',
        required: true,
      },
      {
        name:'siteId',
        bind: 'siteObj.siteId',
      },
      {
        name:'siteName',
        bind: 'siteObj.siteName',
      },
      {
        label: intl.get(`${modelPrompt}.table.prodLine`).d('生产线'),
        name:'prodLine',
        type:FieldType.object,
        lovCode: 'MT.MODEL.PRODLINE',
        lovPara: { tenantId },
        ignore: FieldIgnore.always,
        textField: 'prodLineName',
        required: true,
        dynamicProps:{
          disabled:({record})=>!record.get('siteId'),
        },
      },
      {
        name: 'prodLineId',
        bind: 'prodLine.prodLineId',
      },
      {
        name: 'prodLineName',
        bind: 'prodLine.prodLineName',
      },
      {
        label: intl.get(`${modelPrompt}.table.operation`).d('工作单元'),
        name:'operation',
        lovCode: 'MT.MODEL.WORKCELL_SITE',
        ignore: FieldIgnore.always,
        textField: 'workcellName',
        type:FieldType.object,
        required: true,
        dynamicProps: {
          lovPara: ({ record }) => ({
            tenantId,
            siteIds: [record?.get('siteId')],
          }),
          disabled:({record})=>!record.get('siteId'),
        },
      },
      {
        name:'operationId',
        bind: 'operation.workcellId',
      },
      {
        name:'description',
        bind: 'operation.workcellName',
      },
      {
        label: intl.get(`${modelPrompt}.table.equipment`).d('设备'),
        name:'equipment',
        type:FieldType.object,
        lovCode: 'MT.MODEL.EQUIPMENT',
        lovPara: { tenantId },
        ignore: FieldIgnore.always,
        required: true,
      },
      {
        name: 'equipmentId',
        bind: 'equipment.equipmentId',
      },
      {
        name: 'equipmentCode',
        bind: 'equipment.equipmentCode',
      },
      {
        label: intl.get(`${modelPrompt}.table.apply`).d('申请人姓名'),
        name:'apply',
        required: true,
        type:FieldType.object,
        lovPara: { tenantId },
        lovCode: 'YP.QIS.EMPLOYEE_POSITION',
        ignore: FieldIgnore.always,
      },
      {
        name: 'applyBy',
        bind: 'apply.employeeId',
      },
      {
        name: 'applyByName',
        bind: 'apply.name',
      },
      {
        label: intl.get(`${modelPrompt}.table.applyByDept`).d('申请部门'),
        name:'applyByDept',
        bind: 'apply.unitName',
      },
      {
        label: intl.get(`${modelPrompt}.table.creationDate`).d('申请时间'),
        name:'creationDate',
        type:FieldType.dateTime,
        defaultValue: new Date(),
      },
      {
        label: intl.get(`${modelPrompt}.table.reviewResult`).d('审批结果'),
        name:'reviewResult',
        lookupCode: 'YP.QIS.PROBLEM_TASK_RESULT',
        lovPara: { tenantId },
        type:FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.reviewByName`).d('审批人姓名'),
        name:'reviewByName',
        type:FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.reviewTime`).d('审批时间'),
        name:'reviewTime',
        type:FieldType.dateTime,
      },
      {
        label: intl.get(`${modelPrompt}.table.problemDetail`).d('问题/隐患详述'),
        name:'problemDetail',
        type:FieldType.string,
        required: true,
      },
      {
        label: intl.get(`${modelPrompt}.table.measure`).d('建议措施'),
        name:'measure',
        type:FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.reviewSuggestion`).d('审批意见'),
        name:'reviewSuggestion',
        type:FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.enclosure`).d('附件'),
        name:'enclosure',
        type:FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-problem-apply/ui`,
          method: 'GET',
          transformResponse: value => {
            let listData: any = {};
            try {
              listData = JSON.parse(value);
            } catch (err) {
              listData = {
                message: err,
              };
            }
            if (!listData.success) {
              return {
                ...listData,
                failed: true,
              };
            }
            return listData;
          },
        };
      },
    },
  });

export default listPageFactory;
