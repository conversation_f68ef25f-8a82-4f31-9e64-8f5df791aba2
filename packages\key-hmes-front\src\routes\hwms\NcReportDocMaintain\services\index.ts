/**
 * @Description: 不良记录单管理平台 - services
 * @Author: <EMAIL>
 * @Date: 2023/3/9 14:39
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存不良记录单
 * @function SaveNcReportDoc
 * @returns {object} fetch Promise
 */
export function SaveNcReportDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.LINE,${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.DETAIL`,
    method: 'POST',
  };
}

/**
 * 取消不良记录单
 * @function SaveNcReportDoc
 * @returns {object} fetch Promise
 */
export function CancelNcReportDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/batch/cancel-new/ui`,
    method: 'POST',
  };
}

/**
 * 保存并提交不良记录单
 * @function SaveAndSubmitNcReportDoc
 * @returns {object} fetch Promise
 */
export function SaveAndSubmitNcReportDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/save-submit/ui`,
    method: 'POST',
  };
}

/**
 * 根据检验业务类型查询所需信息
 * @function GetInspectBusInfo
 * @returns {object} fetch Promise
 */
export function GetInspectBusInfo(): object {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-disposition-group-member/property/list/ui`,
    method: 'GET',
  };
}

/**
 * 状态变更
 * @function ChangeDocStatus
 * @returns {object} fetch Promise
 */
export function ChangeDocStatus(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/status/ui`,
    method: 'POST',
  };
}

/**
 * 批量发起工作流
 * @function BatchReview
 * @returns {object} fetch Promise
 */
export function BatchReview(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/batch/review/ui`,
    method: 'POST',
  };
}

/**
 * 查询EO或物料批
 * @function QueryEoOrMaterialLot
 * @returns {object} fetch Promise
 */
export function QueryEoOrMaterialLot(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/eo-materialLot/lov/ui`,
    method: 'GET',
  };
}

/**
 * 直接处置
 * @function DirectDisposal
 * @returns {object} fetch Promise
 */
export function DirectDisposal(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/direct-disposal/ui`,
    method: 'GET',
  };
}

/**
 * 查询检验单行数据
 * @function QueryInspectDocLine
 * @returns {object} fetch Promise
 */
export function QueryInspectDocLine(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/source-doc-ids`,
    method: 'GET',
  };
}

/**
 * 流程类型变更
 * @function ChangeFlowType
 * @returns {object} fetch Promise
 */
export function ChangeFlowType(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/flow-type-button`,
    method: 'POST',
  };
}
