import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentUser, getCurrentOrganizationId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.qms.disassembleTaskExecutionPlatform';

const detailFactory = () =>
  new DataSet({
    paging: false,
    selection: false,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        label: intl.get(`${modelPrompt}.table.index`).d('序号'),
        name: 'index',
        type: FieldType.number,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownLocation`).d('拆解位置'),
        name: 'teardownLocation',
        type: FieldType.object,
        lovCode: 'YP.QIS.TEARDOWN_LOCATION',
        ignore: FieldIgnore.always,
        lovPara: { tenantId },
        dynamicProps: {
          disabled: ({ dataSet }) =>
            !['NEW', 'SPILTING'].includes(dataSet.getState('basicData')?.teardownTaskStatus || ''),
          required: ({ dataSet }) =>
            ['NEW', 'SPILTING'].includes(dataSet.getState('basicData')?.teardownTaskStatus || ''),
          lovPara: ({ dataSet }) => {
            return {
              tenantId,
              teardownHeadId: dataSet.getState('locator')?.teardownHeadId,
            };
          },
        },
      },
      {
        name: 'teardownTaskLocationId',
        bind: 'teardownLocation.teardownLocationId',
      },
      {
        name: 'teardownTaskLocationDesc',
        bind: 'teardownLocation.teardownLocationDesc',
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskNcFlag`).d('有无缺陷'),
        name: 'teardownTaskNcFlag',
        type: FieldType.string,
        lookupCode: 'YP.QIS.YN_FLAG',
        dynamicProps: {
          disabled: ({ dataSet, record }) =>
            !record.get('teardownLocation') ||
            dataSet.getState('basicData')?.teardownTaskStatus !== 'SPILTING',
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.defectCode`).d('缺陷编码'),
        name: 'defectCode',
        type: FieldType.object,
        lovCode: 'YP.QIS.TEARDOWN_NC',
        ignore: FieldIgnore.always,
        dynamicProps: {
          disabled: ({ dataSet, record }) =>
            record.get('teardownTaskNcFlag') !== 'Y' ||
            dataSet.getState('basicData')?.teardownTaskStatus !== 'SPILTING',
          required: ({ dataSet, record }) =>
            record.get('teardownTaskNcFlag') === 'Y' &&
            dataSet.getState('basicData')?.teardownTaskStatus === 'SPILTING',
          lovPara: ({ record }) => {
            return {
              tenantId,
              teardownLocationId: record.get('teardownTaskLocationId'),
            };
          },
        },
      },
      {
        name: 'teardownNcId',
        bind: 'defectCode.teardownNcId',
      },
      {
        name: 'teardownNcCode',
        bind: 'defectCode.ncCode',
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownNcName`).d('缺陷名称'),
        name: 'teardownNcName',
        bind: 'defectCode.description',
      },
      {
        label: intl.get(`${modelPrompt}.table.defectLeave`).d('缺陷等级'),
        name: 'defectLeave',
        type: FieldType.object,
        lovCode: 'YP.QIS.TEARDOWN_NC_LEVEL_LOV',
        ignore: FieldIgnore.always,
        textField: 'teardownNcLevelCodeDesc',
        dynamicProps: {
          disabled: ({ dataSet, record }) =>
            !record.get('teardownNcId') ||
            dataSet.getState('basicData')?.teardownTaskStatus !== 'SPILTING',
          required: ({ dataSet, record }) => {
            return (
              record.get('teardownNcId') &&
              dataSet.getState('basicData')?.teardownTaskStatus === 'SPILTING'
            );
          },
          lovPara: ({ record }) => {
            return {
              tenantId,
              teardownNcId: record.get('teardownNcId'),
            };
          },
        },
      },
      {
        name: 'teardownNcLevelId',
        bind: 'defectLeave.teardownNcLevelId',
      },
      {
        name: 'teardownNcLevelCode',
        bind: 'defectLeave.teardownNcLevelCode',
      },
      {
        name: 'teardownNcLevelDesc',
        bind: 'defectLeave.teardownNcLevelCodeDesc',
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownNcScore`).d('评分'),
        name: 'teardownNcScore',
        bind: 'defectLeave.teardownNcScore',
      },
      {
        label: intl.get(`${modelPrompt}.table.enclosure`).d('上传图片'),
        name: 'enclosure',
        type: FieldType.attachment,
        bucketName: 'qms',
        dynamicProps: {
          disabled: ({ dataSet }) =>
            dataSet.getState('basicData')?.teardownTaskStatus !== 'SPILTING',
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.engineerScore`).d('工程师评分'),
        name: 'engineerScore',
        type: FieldType.number,
        step: 1,
        dynamicProps: {
          min: ({ record }) => (record.get('teardownNcLevelCode') === 'OTHER' ? 0 : 1),
          disabled: ({ dataSet, record }) => {
            return !(
              dataSet.getState('reviewRoles')?.includes(getCurrentUser().currentRoleCode) &&
              dataSet.getState('basicData')?.teardownTaskStatus === 'REVIEWING' &&
              record.get('teardownNcLevelCode') === 'OTHER'
            );
          },
          required: ({ dataSet, record }) =>
            dataSet.getState('reviewRoles')?.includes(getCurrentUser().currentRoleCode) &&
            record.get('teardownTaskStatus') === 'REVIEWING' &&
            record.get('teardownNcLevelCode') === 'OTHER',
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.remarks`).d('备注'),
        name: 'remarks',
        type: FieldType.string,
      },
    ],
    events: {
      update: ({ record, name }) => {
        if (name === 'teardownLocation') {
          record.set('teardownTaskNcFlag', null);
          record.set('defectCode', null);
          record.set('defectLeave', null);
          record.set('mark', null);
        }
        if (name === 'teardownTaskNcFlag') {
          record.set('defectCode', null);
          record.set('defectName', null);
          record.set('defectLeave', null);
          record.set('mark', null);
        }
        if (name === 'defectCode') {
          record.set('defectName', null);
          record.set('defectLeave', null);
          record.set('mark', null);
          record.set('engineerMark', null);
        }
        if (name === 'defectLeave') {
          record.set('engineerScore', record.get('teardownNcScore'));
        }
      },
    },
  });

export default detailFactory;
