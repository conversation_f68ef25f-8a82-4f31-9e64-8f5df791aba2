/**
 * @Description: 用户权限抽屉-分配/取消分配按钮
 * @Author: <<EMAIL>>
 * @Date: 2022-10-13 17:58:46
 * @LastEditTime: 2023-02-28 15:31:34
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { observer } from 'mobx-react';
import { DataSet, Button } from 'choerodon-ui/pro';

interface DistributeButtonProps {
  ds: DataSet;
  className: any;
  organizationTableDs: DataSet;
  canEdit: boolean;
  handleUpdateTabDisabled: (record, changeFlag) => {};
}

interface RevokeButtonProps {
  ds: DataSet;
  className: any;
  organizationTableDs: DataSet;
  canEdit: boolean;
  handleUpdateTabDisabled: (record, changeFlag) => {};
}

const DistributeButton = observer((props: DistributeButtonProps) => {
  const { ds, className, organizationTableDs, canEdit, handleUpdateTabDisabled } = props;

  const handleSave = () => {
    const checkedKeys = ds?.current?.get('checkedKeys');
    checkedKeys.forEach(item => {
      organizationTableDs.create(item);
      if (ds.selected.length > 1) {
        ds.selected.forEach(record => {
          const originList = record?.get('organizationList') || [];
          const index = originList.findIndex(i => i?.organizationId === item.organizationId);
          if (index < 0) {
            originList.push(item);
          }
          record.set('organizationList', originList);
          record.set('checkedKeys', []);
        });
      } else {
        const originList = ds.current?.get('organizationList') || [];
        const index = originList.findIndex((i: any) => i.organizationId === item.organizationId);
        if (index < 0) {
          originList.push(item);
        }
        ds.current?.set('organizationList', originList);
        ds.current?.set('checkedKeys', []);
      }
    });
    handleUpdateTabDisabled(ds.current, false);
  };

  const treeCheckedKeysLength: number = ds!.current?.get('checkedKeys')?.length;

  return (
    <Button
      className={className}
      disabled={!canEdit || !treeCheckedKeysLength}
      onClick={handleSave}
    >
      <span>&gt;</span>
    </Button>
  );
});

const RevokeButton = observer((props: RevokeButtonProps) => {
  const { ds, className, organizationTableDs, canEdit, handleUpdateTabDisabled } = props;

  const handleRevoke = () => {
    const cancelList = [...organizationTableDs?.selected];
    (cancelList || []).forEach((_record: any) => {
      // 删除organizationTableDs中的数据
      organizationTableDs.remove(_record);
      organizationTableDs.unSelectAll();
      if (ds.selected.length > 1) {
        ds.selected.forEach(record => {
          const originList = record?.get('organizationList') || [];
          const index = originList.findIndex(
            i => i?.organizationId === _record?.get('organizationId'),
          );
          if (index > -1) {
            originList.splice(index, 1);
          }
          record.set('organizationList', originList);
        });
      } else {
        const originList = ds.current?.get('organizationList') || [];
        const index = originList.findIndex(
          (i: any) => i.organizationId === _record?.get('organizationId'),
        );
        if (index > -1) {
          originList.splice(index, 1);
        }
        ds.current?.set('organizationList', originList);
      }
    });
    // 详情主界面ds update监听未生效，暂时这样处理
    handleUpdateTabDisabled(ds.current, false);
  };

  return (
    <Button
      className={className}
      disabled={!canEdit || !organizationTableDs?.selected.length}
      onClick={handleRevoke}
    >
      <span>&lt;</span>
    </Button>
  );
});

export { DistributeButton, RevokeButton };
