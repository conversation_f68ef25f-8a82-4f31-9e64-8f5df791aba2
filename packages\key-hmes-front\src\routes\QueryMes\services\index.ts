/**
 * @Description: 库存查询-services
 * @Author: <EMAIL>
 * @Date: 2022/7/7 14:17
 * @LastEditTime: 2022/7/7 14:17
 * @LastEditors: <EMAIL>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const tenantId = getCurrentOrganizationId();

// 库存查询-查询库存日记账子节点数据
export function QueryChildList() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inv-onhand-quantity/sub-locator/query/ui`,
    method: 'POST',
  };
}

// 库存查询-查询库存日记账数据
export function FetchSubLocatorInfo() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inv-onhand-quantity/sub-locator/info/ui`,
    method: 'POST',
  };
}
