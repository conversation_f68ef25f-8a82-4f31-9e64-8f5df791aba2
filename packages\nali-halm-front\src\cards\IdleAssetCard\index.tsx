import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Collapse } from 'choerodon-ui';
// import { Spin } from 'choerodon-ui/pro/lib';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import ReactEchartsCore from 'echarts-for-react/lib/core';
import axios from 'axios';
import classNames from 'classnames';
import { HALM_ATN } from 'alm/utils/config';
import styles from './index.module.less';
import Color from './Color';

const organizationId = getCurrentOrganizationId();
const url = `${HALM_ATN}/v1/${organizationId}/asset-cards/idle-asset-percent`;

const AssetUsageCard = () => {
  const [data, setData] = useState<any>(undefined);
  useEffect(() => {
    fetchData();
  }, []);
  const fetchData = useCallback(async () => {
    const res = await axios.get<any, any>(url);
    setData(res);
  }, []);
  const option = useMemo(() => {
    return {
      grid: {
        top: 10,
      },
      tooltip: {
        trigger: 'none',
      },
      series: [
        {
          name: 'Pyramid',
          type: 'funnel',
          width: '100',
          height: '175',
          left: '30',
          top: '5',
          sort: 'none',
          label: {
            show: false,
            formatter: () => '',
          },
          labelLine: {
            show: false,
          },
          data: [
            {
              value: data?.withinThreeDaysCount,
              name: '3天以下',
              itemStyle: {
                color: Color[0],
              },
            },
            {
              value: data?.threeSevenDaysCount,
              name: '3~7天',
              itemStyle: {
                color: Color[1],
              },
            },
            {
              value: data?.sevenTwentyOneDaysCount,
              name: '7~21天',
              itemStyle: {
                color: Color[2],
              },
            },
            {
              value: data?.twentyOneThirtyDaysCount,
              name: '21~30天',
              itemStyle: {
                color: Color[3],
              },
            },
            {
              value: data?.oneThreeMonthsCount,
              name: '1月~3月',
              itemStyle: {
                color: Color[4],
              },
            },
            {
              value: data?.threeSixMonthsCount,
              name: '3月~6月',
              itemStyle: {
                color: Color[5],
              },
            },
            {
              value: data?.overSixMonthsCount,
              name: '6月以上',
              itemStyle: {
                color: Color[6],
              },
            },
          ],
        },
      ],
    };
  }, [data]);
  return (
    <div className={styles.container}>
      <Collapse
        bordered={false}
        expandIconPosition="right"
        defaultActiveKey={['A']}
        trigger="icon"
        className={styles['customize-collapse']}
      >
        <Collapse.Panel key="A" showArrow={false} header="闲置资产占比情况">
          <div className={styles['label-container']}>
            <div className={classNames(styles['label-item'], styles['label-item-1'])}>
              <span>3天以下</span>
              <span className={styles.value}>{data?.withinThreeDaysCount}</span>
              <span>{data?.withinThreeDaysPercent}%</span>
            </div>
            <div className={classNames(styles['label-item'], styles['label-item-2'])}>
              <span>7天</span>
              <span className={styles.value}>{data?.threeSevenDaysCount}</span>
              <span>{data?.threeSevenDaysPercent}%</span>
            </div>
            <div className={classNames(styles['label-item'], styles['label-item-3'])}>
              <span>7~21天</span>
              <span className={styles.value}>{data?.sevenTwentyOneDaysCount}</span>
              <span>{data?.sevenTwentyOneDaysPercent}%</span>
            </div>
            <div className={classNames(styles['label-item'], styles['label-item-4'])}>
              <span>21~30天</span>
              <span className={styles.value}>{data?.twentyOneThirtyDaysCount}</span>
              <span>{data?.twentyOneThirtyDaysPercent}%</span>
            </div>
            <div className={classNames(styles['label-item'], styles['label-item-5'])}>
              <span>1月~3月</span>
              <span className={styles.value}>{data?.oneThreeMonthsCount}</span>
              <span>{data?.oneThreeMonthsPercent}%</span>
            </div>
            <div className={classNames(styles['label-item'], styles['label-item-6'])}>
              <span>3月~6月</span>
              <span className={styles.value}>{data?.threeSixMonthsCount}</span>
              <span>{data?.threeSixMonthsPercent}%</span>
            </div>
            <div className={classNames(styles['label-item'], styles['label-item-7'])}>
              <span>6月以上</span>
              <span className={styles.value}>{data?.overSixMonthsCount}</span>
              <span>{data?.overSixMonthsPercent}%</span>
            </div>
          </div>
          <ReactEchartsCore
            echarts={echarts}
            option={option}
            notMerge
            lazyUpdate
            style={{
              height: 200,
            }}
          />
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default AssetUsageCard;
