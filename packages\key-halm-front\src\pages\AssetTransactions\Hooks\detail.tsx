/*
 * @Description:
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-03-28 15:05:05
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */
import React, { useEffect, useMemo, useCallback, useState } from 'react';
import { useDataSet, useDataSetEvent } from 'utils/hooks';
import { DataSet, Modal, Button } from 'choerodon-ui/pro';
import { isUndefined, isArray, isEmpty } from 'lodash';
import {
  saveAssetTranLine,
  tpHeaderWithdraw,
  tpHeaderSubmit,
} from 'alm/services/assetTransacionBasicTypeService';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import notification from 'utils/notification';
import { fetchFieldConfigTpAuto, fetchFieldConfig } from 'alm/services/api';
import { queryOrgByEmployee } from 'alm/services/organizationService';
import { getCurrentEmployee } from 'alm/services/workOrderService';
import { parseFields } from 'alm/utils/dynamicFieldRender';
import intl from 'utils/intl';
import { handleNonPermissionErr } from 'alm/utils/response';
import { assetTransactionTypeDS } from '../Stores/detailDs';
import { getCommonLineCols, getOperateCol, getFileCol } from '../columns';
import { lineViewFormRender, lineEditFormRender } from '../utils/renderer';
import {
  getRecordDynamicProps,
  parseDynamicPropsOfRecords,
  validateRecords,
  getHeadDynamicProps,
  handleCreateAsset,
} from '../utils';
import getLang from '../Langs';
import { IHeadBtnsStyle } from '../Components/HeadBtns';

const organizationId = getCurrentOrganizationId();

/**
 * @description: 根据id变化调用ds.query()
 * @param {DataSet} dataSet
 * @param {*} id 参数值
 * @param {*} name 参数名
 * @return {*}
 */
export function useDsQueryById(dataSet: DataSet, id, name = 'changeHeaderId') {
  useEffect(() => {
    // id变化时调用详情接口
    dataSet.setQueryParameter(name, id);
    if (id) {
      dataSet.query();
    }
  }, [id]);
}

/**
 * @description: 监听ds数据加载前，设置数据状态
 * @param {DataSet} dataSet
 * @return {*}
 */
export function useDataSetBeforeLoad(dataSet: DataSet) {
  const [dsData, setDsData] = useState<any>(null);
  const handleListBeforeLoad = ({ data }) => {
    if (isArray(data) && data.length > 0) {
      setDsData(data[0]);
    }
  };
  // 监听load事件
  useDataSetEvent(dataSet, 'beforeLoad', handleListBeforeLoad);
  return dsData;
}

/**
 * @description: 获取资产事务类型详情
 * @param {*}
 * @return {*}
 */
export function useAssetTransactionType(detail, detailDs: DataSet) {
  const assetTransactionTypeDs = useDataSet(() => new DataSet(assetTransactionTypeDS()));
  const [assetTransType, setAssetTransType] = useState<any>({});
  useEffect(() => {
    const query = async () => {
      if (detail?.changeHeaderId || detailDs.current?.get('transactionTypeId')) {
        assetTransactionTypeDs.setQueryParameter(
          'transactionTypeId',
          detail?.transactionTypeId || detailDs.current?.get('transactionTypeId')
        );
        const res = await assetTransactionTypeDs.query();
        setAssetTransType(res);
      }
    };
    query();
  }, [detail]);
  return { assetTransactionTypeDs, assetTransType };
}

/**
 * @description: 资产事务行显示的列
 * @param {*} editFlag 是否可编辑
 * @param {*} assetTransUploadAttrData 附件上传附加参数
 * @param {*} onEditLine 行编辑事件
 * @param {*} onDeleteLine 行删除事件
 * @return {*}
 */
export function useLineColumns(
  editFlag = false,
  assetTransUploadAttrData: any[] = [],
  headProcessStatus: string,
  onEditLine,
  onDeleteLine
) {
  const columns = useMemo(() => {
    const cols: any = getCommonLineCols();
    cols.push(getFileCol(headProcessStatus === 'DRAFT', assetTransUploadAttrData));
    const operate = getOperateCol(editFlag, onEditLine, onDeleteLine);
    // 操作列在倒数第二位显示
    cols.splice(-2, 0, operate);
    return cols;
  }, [editFlag, assetTransUploadAttrData, headProcessStatus]);
  return columns;
}

// 审批表单资产事务行显示的列
export function useApprovalLineColumns() {
  const columns = useMemo(() => {
    const cols: any = getCommonLineCols();
    cols.push(getFileCol());
    return cols;
  }, []);
  return columns;
}

/**
 * 1.从资产事务列表新建时，带出选择的资产事务类型相关的值，
 *   包括根据资产事务类型获取头的动态字段
 * 2.从盘点清单列表新建时，带出选择的资产事务类型相关的值，头上动态字段根据盘点任务id查询
 * @description: 新建资产事务字段默认值设置
 * @param {DataSet} ds
 * @param {*} props
 * @return {*}
 */
export function useInitNewDetail(
  ds: DataSet,
  props,
  setPageLoading,
  setAttrField,
  lineDs: DataSet
) {
  useEffect(() => {
    const {
      location = {},
      match: { params = {} },
    } = props;
    const { state } = location;
    const { id } = params;
    // ! 在新建页面打开的情况下，从任意页面激活props都是一个新的引用，即发生了变化，并且里面的state为undefined
    // 避免从任意界面激活新建页面都重新渲染
    if (isUndefined(id) && !isUndefined(state)) {
      handleInit();
    }
    // 新建带出负责人、负责组织、调出组织
    if (isUndefined(id)) {
      getCurrentEmployee({ tenantId: organizationId }).then(res => {
        ds?.current?.set({
          principalPersonId: res?.employeeId,
          principalPersonIdMeaning: res?.employeeName,
        });
        queryOrgByEmployee({ tenantId: organizationId, employeeId: res?.employeeId }).then(
          orgInfo => {
            ds?.current?.set({
              usingOrgId: orgInfo?.unitId,
              usingOrgName: orgInfo?.unitName,
              usingOrgType: orgInfo?.orgType,
              transferInOrgId: orgInfo?.unitId,
              transferInOrgName: orgInfo?.unitName,
              transferInOrgType: orgInfo?.orgType,
            });
          }
        );
      });
    }
    async function handleInit() {
      const {
        transaction = {},
        moduleSource,
        taskId,
        defaultValues = {},
        assets = [],
        assetOrg = {},
      } = state;
      if (ds?.current) {
        try {
          setPageLoading(true);
          ds.current.reset(); // 新建页面已打开时从别的页面多次进入重置数据
          ds.current.init('codeRule', transaction?.codeRule);
          ds.current.init('codeRuleName', transaction?.codeRuleName);
          ds.current.init('transactionTypeId', transaction?.transactionTypeId);
          ds.current.init('transactionTypeIdMeaning', transaction?.transactionTypeName);
          // ! 盘点清单新建资产事务时，动态字段默认值取盘点默认值
          if (moduleSource === 'countingLine' && isUndefined(id)) {
            ds.current.init(defaultValues);
            await fetchFieldConfigTpAuto({
              taskId,
              scopeId: transaction?.transactionTypeId,
              scopeCode: 'ASSET_TRANSACTION_HEAD',
            }).then(res => {
              getResponse(res);
              if (!res?.failed) {
                ds.current!.init('attrField', res?.attrField);
                setAttrField(res?.attrField);
              }
            });
          } else {
            // 获取附加字段配置信息
            const getFieldConfigsByScopeId = async (scopeId, scopeCode) => {
              if (scopeId) {
                const paramsObj = {
                  scopeId,
                  scopeCode,
                };
                const res = await fetchFieldConfig(paramsObj);
                return res;
              }
            };
            let resAttrField = [];
            // 附加字段
            const headFields = await getFieldConfigsByScopeId(
              transaction?.transactionTypeId,
              'ASSET_TRANSACTION_HEAD'
            );
            resAttrField = headFields?.attrField;
            ds.current!.init('attrField', resAttrField);
            setAttrField(resAttrField);
            // 创建行
            lineDs.loadData([]);
            handleCreateAsset(lineDs, assets, transaction?.transactionTypeId);
          }
          // 带出资产所属组织
          const orgId = assetOrg?.orgId;
          if (orgId) {
            ds.current.set('transferOutOrgId', assetOrg.orgId);
            ds.current.set('transferOutOrgName', assetOrg.orgName);
            ds.current.set('transferOutOrgType', assetOrg?.orgType);
          }
          if (moduleSource === 'idleAssets') {
            ds.current.set('idleCreateFlag', 1);
          }
        } finally {
          setPageLoading(false);
        }
      }
    }
    // ! 当新建界面打开时，从盘点清单进入新建时需要重新初始化，所以依赖props
  }, [props]);
}

/**
 * 资产事务新建的时只保存头信息，修改时会保存行信息，
 * 但是行信息添加时直接选中数据进行添加，再头修改或者行保存时才会校验行信息
 * @description: 资产事务保存
 * @param {DataSet} ds
//  * @param {*} updateAttrFieldOfDs
 * @param {DataSet} lineDs
 * @param {DataSet} typeDs
 * @return {*}
 */
export function useSave(ds: DataSet, lineDs: DataSet, typeDs: DataSet) {
  const [saveLoading, setSaveLoading] = useState(false);
  const handleSave = useCallback(async () => {
    try {
      setSaveLoading(true);
      const validateFlag = await ds.validate();
      if (validateFlag) {
        const updateAttrFieldOfDs = () => {
          const current = ds?.current?.toData() || {};
          const attrField = ds?.current?.get('attrField') || [];
          const parseAttrField = parseFields(attrField, current);
          if (ds.current) {
            ds.current.set('attrField', parseAttrField);
          }
        };
        updateAttrFieldOfDs();
        if (ds?.current) {
          // 详情修改时，先判断有没有构建行动态字段ds
          lineDs.records.forEach(record => {
            getRecordDynamicProps(lineDs, record);
          });
          // 详情修改, 修改的时候需要构建行的保存数据结构
          let tpChangeLines = parseDynamicPropsOfRecords(
            lineDs,
            ds.current.get('transactionTypeId')
          );
          // 为了处理新建行没有点击编辑 导致目标资产状态无值 感觉逻辑有点问题
          tpChangeLines = handleLineNewAssetStatus(tpChangeLines, typeDs);
          const illegalArray = await validateRecords(lineDs);
          if (illegalArray.length !== 0) {
            const message = intl
              .get('aatn.assetTransactionBasicType.view.message.incompleteLine', {
                lineNums: illegalArray.join('、'),
              })
              .d(`存在第${illegalArray.join('、')}行事务行信息未填写完整，请完善行信息再保存！`);
            notification.warning({ message });
            return;
          }
          ds.current.set('tpChangeLines', tpChangeLines);
        }
        const res = await ds.submit();
        return res;
      }
    } catch (error) {
      throw error;
    } finally {
      setSaveLoading(false);
    }
  }, []);

  return { saveLoading, handleSave };
}

/**
 * 为了处理新建行后,没有点击编辑,导致保存行时不存在目标资产状态
 * @param {*} lines 行数组
 */
export const handleLineNewAssetStatus = (lines, typeDs) => {
  let newLines = lines;
  const typeDetail = typeDs.current.toData();
  const { statusUpdateFlag, targetAssetStatusId, targetAssetStatusName } = typeDetail;
  // 目标资产状态是否可编辑
  if (statusUpdateFlag) {
    if (!isUndefined(targetAssetStatusId)) {
      newLines = lines.map(record => {
        if (isUndefined(record.newObjectStatusId)) {
          return {
            ...record,
            newObjectStatusId: targetAssetStatusId,
            newAssetStatusName: targetAssetStatusName,
          };
        }
        return record;
      });
    }
  }
  return newLines;
};

/**
 * @description: 添加资产事务行-编辑
 * @param {DataSet} lineDs
 * @param {*} detail
 * @return {*}
 */
export function useSaveLine(lineDs: DataSet, detail) {
  const handleAddLine = useCallback(
    async (objectType, records) => {
      const { transactionTypeId, changeHeaderId } = detail;
      let id: string = '';
      let num: string = '';
      let desc: string = '';
      if (objectType === 'ITEM') {
        id = 'itemId';
        num = 'itemNum';
        desc = 'itemDesc';
      } else if (objectType === 'STANDARD_ASSET') {
        id = 'standardAssetId';
        num = 'assetNum';
        desc = 'assetDesc';
      } else if (objectType === 'ASSET') {
        id = 'assetId';
        num = 'assetNum';
        desc = 'assetDesc';
      }

      const data = records.map(i => ({
        objectId: i[id],
        objectNum: i[num],
        objectDesc: i[desc],
        objectType,
        requisitionsNumber: 1,
        transactionTypeId,
        changeHeaderId,
        tenantId: organizationId,
      }));
      const params = {
        tenantId: organizationId,
        data: { ...detail, tpChangeLines: data },
      };
      const res = await saveAssetTranLine(params).catch(e => {
        console.log('%c [ e ]-409', 'font-size:13px; background:pink; color:#bf2c9f;', e);
      });
      getResponse(res);
      if (!res?.failed) {
        lineDs.query();
      }
    },
    [detail]
  );

  return handleAddLine;
}

/**
 * @description: 删除行
 * @param {DataSet} lineDs
 * @return {*}
 */
export function useDeleteLine(lineDs: DataSet, detailDs: DataSet) {
  const handleDeleteLine = useCallback(async ({ record }) => {
    if (record?.get('lineId')) {
      const headData = detailDs.current?.toData();
      lineDs.setState('headData', headData);
      const res = await lineDs.delete(record);
      if (res?.success) {
        lineDs.query();
      }
    } else {
      lineDs.remove(record, true);
    }
  }, []);
  return handleDeleteLine;
}

/**
 * @description: 资产事务行编辑、详情modal
 * @param {*} lineDs
 * @param {*} assetTransType 资产事务类型详情
 * @return {*}
 */
export function useOpenLineModal(lineDs, assetTransType) {
  useEffect(() => {
    lineDs.setState('typeData', assetTransType);
  }, [assetTransType]);

  const handleOpenLineModal = useCallback(
    ({ record }, canEdit) => {
      const targetAssetStatusDisabled = getTargetAssetStatusDisabled();
      const dynamicProps = getRecordDynamicProps(lineDs, record);

      record.save(); // ! 第一次打开modal后修改数据点取消，修改不会被取消掉，故先保存一下以解决问题
      const lineModal = Modal.open({
        drawer: true,
        width: 600,
        key: Modal.key(),
        title: getLang('TRANSACTION_LINES'),
        children: (
          <>
            {canEdit
              ? lineEditFormRender(record, targetAssetStatusDisabled, dynamicProps)
              : lineViewFormRender(record, targetAssetStatusDisabled, dynamicProps)}
          </>
        ),
        footer: (okBtn, cancelBtn) =>
          canEdit ? (
            <>
              {okBtn}
              {cancelBtn}
            </>
          ) : (
            <Button onClick={lineModal.close}>{getLang('CLOSE')}</Button>
          ),
        onOk: () => handleLineModalOk(),
        onCancel: () => handleModalCancel(),
      });

      function handleModalCancel() {
        record.restore(); // 从缓存中恢复数据同时会校验数据这个不处理
      }

      async function handleLineModalOk() {
        const flag = await record.validate(true);
        if (flag) {
          record.save();
        } else {
          return false;
        }
      }

      // 目标资产状态是否禁用标识
      function getTargetAssetStatusDisabled() {
        const {
          statusUpdateFlag,
          targetAssetStatusScope,
          targetAssetStatusId,
          targetAssetStatusName,
        } = assetTransType;
        // 目标资产状态禁用flag
        let targetAssetStatusDisabledFlag = false;
        // 目标资产状态是否可编辑
        if (statusUpdateFlag) {
          if (!isUndefined(targetAssetStatusId)) {
            // targetAssetStatusScope为空的是否返回的是字符串空数组[]，有值的时候是字符串
            // 目标资产状态：有值，且“目标资产状态范围”为空时，则单据上“目标资产状态”不可编辑。默认为所选。
            const reg = /\[\]/;
            if (isEmpty(targetAssetStatusScope) || reg.test(targetAssetStatusScope)) {
              targetAssetStatusDisabledFlag = true;
            }
            if (isUndefined(record.get('newObjectStatusId'))) {
              record.set('newObjectStatusId', targetAssetStatusId);
              record.set('newAssetStatusName', targetAssetStatusName);
            }
          }
        } else {
          targetAssetStatusDisabledFlag = true;
        }
        return targetAssetStatusDisabledFlag;
      }
    },
    [assetTransType]
  );

  return { handleOpenLineModal };
}

export function useWithdraw(detail, dataSet: DataSet, lineDs: DataSet) {
  async function handleWithdraw() {
    if (detail.processStatus === 'APPROVING') {
      const res = await tpHeaderWithdraw({ ...detail, tenantId: organizationId });
      handleNonPermissionErr(res);
      if (res) {
        dataSet.query();
        lineDs.query();
      }
    } else {
      // 不开启工作流的资产事务类型撤回时状态变为拟定
      if (dataSet?.current) {
        dataSet.current.set('processStatus', 'DRAFT');
        dataSet.current.set('tpChangeLines', lineDs.toData());
      }
      // 强制提交不校验：当单据处于可撤回时，单据是不可以修改的，避免提交后将动态字段配置为必输
      await dataSet.forceSubmit();
      dataSet.query();
      lineDs.query();
    }
  }
  return handleWithdraw;
}

/**
 * @description: 更新资产事务状态
 * @param {*} dataSet
 * @param {*} status
 * @return {*}
 */
export function useUpdateStatus(dataSet: DataSet, lineDs: DataSet) {
  async function handleUpdateStatus(status) {
    if (dataSet?.current) {
      dataSet.current.set('processStatus', status);
      dataSet.current.set('tpChangeLines', lineDs.toData());
    }
    // 强制提交不校验：当单据处于可撤回时，单据是不可以修改的，避免提交后将动态字段配置为必输
    await dataSet.forceSubmit();
    dataSet.query();
    lineDs.query();
  }
  return handleUpdateStatus;
}

export function useSubmit(detail, dataSet: DataSet, lineDs: DataSet) {
  async function handleSubmit() {
    const res = await tpHeaderSubmit({ ...detail, tenantId: organizationId, businessType: 'TRANSFER' });
    handleNonPermissionErr(res);
    // getResponse(res);
    if (!res?.failed) {
      dataSet.query();
      lineDs.query();
    }
  }
  return handleSubmit;
}

export function useDynamicProps(dataSet: DataSet) {
  const [attrField, setAttrField] = useState<any[]>([]);
  // 详情、动态字段配置信息变化时，重新解析动态字段配置
  const dynamicProps = useMemo(() => {
    return getHeadDynamicProps(dataSet);
  }, [dataSet?.current, attrField]);
  return { dynamicProps, setAttrField };
}

/**
 * @description: 常规资产事务头部按钮显示逻辑，特殊则重写
 * @param {*} detail
 * @param {*} isNew
 * @param {*} editFlag
 * @return {*}
 */
export function useHeadBtnsStyle(detail, isNew, editFlag) {
  const headBtnsStyle: IHeadBtnsStyle = useMemo(() => {
    // 编辑状态显示保存按钮
    const saveBtnSty = editFlag ? { display: 'block' } : { display: 'none' };

    // 拟定中、已拒绝状态显示编辑按钮
    const editBtnSty =
      !editFlag && ['REJECTED', 'DRAFT'].includes(detail?.processStatus)
        ? { display: 'block' }
        : { display: 'none' };

    // 编辑非新建状态显示关闭按钮
    const closeBtnSty = !isNew && editFlag ? { display: 'block' } : { display: 'none' };

    // 非编辑状态单据为待处理、审批中显示撤回按钮
    const recallBtnSty =
      !editFlag && ['PENDING', 'APPROVING'].includes(detail?.processStatus)
        ? { display: 'block' }
        : { display: 'none' };

    // 非编辑状态存在工作流审批记录时显示审批历史按钮
    const approveHistoryBtnSty =
      !editFlag && detail?.workflowFlag && detail?.wkInstanceId
        ? { display: 'block' }
        : { display: 'none' };

    // 非编辑状态已拒绝、拟定状态时显示提交按钮
    const submitBtnSty =
      !editFlag && ['REJECTED', 'DRAFT'].includes(detail?.processStatus)
        ? { display: 'block' }
        : { display: 'none' };

    // 非编辑状态已拒绝、拟定状态时显示提交按钮
    const dropBtnSty =
      !editFlag && ['REJECTED', 'DRAFT'].includes(detail?.processStatus)
        ? { display: 'block', marginLeft: 0, width: 30, padding: 0 }
        : { display: 'none' };

    return {
      saveBtnSty,
      editBtnSty,
      closeBtnSty,
      recallBtnSty,
      approveHistoryBtnSty,
      submitBtnSty,
      dropBtnSty,
    };
  }, [detail, isNew, editFlag]);

  return headBtnsStyle;
}
