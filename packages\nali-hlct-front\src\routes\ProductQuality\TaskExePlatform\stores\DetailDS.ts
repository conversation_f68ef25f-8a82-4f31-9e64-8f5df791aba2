/**
 * @Description: 检证任务执行平台-详情界面DS
 */
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import moment from 'moment';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();
const modelPrompt = 'tarzan.inspectExecute.taskExePlatform';

const detailDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'verificationTaskGroupId',
  fields: [
    {
      name: 'verificationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationCode`).d('检证项目编码'),
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'siteName',
      lovPara: { tenantId },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('状态'),
      lookupCode: 'YP.QIS.SA_VERIFICATION_STATUS',
      lovPara: { tenantId },
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'verificationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationType`).d('检证类型'),
      lookupCode: 'YP.QIS.VERIFICATION_TYPE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'verificationFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationFrom`).d('检证来源'),
      lookupCode: 'YP.QIS.VERIFICATION_FROM',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检证阶段'),
      lookupCode: 'YP.QIS.VERIFICATION_PERIOD',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
      lookupCode: 'YP.QIS.PRODUCT_TYPE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialCode',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      disabled: true,
      bind: 'materialLov.materialName',
    },
    {
      name: 'itemGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemGroup`).d('产品对象'),
      disabled: true,
      bind: 'materialLov.itemGroupDesc',
    },
    {
      name: 'failureMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureMode`).d('失效模式'),
      lookupCode: 'YP.QIS.FAILURE_MODE',
      lovPara: { tenantId },
    },
    {
      name: 'applyArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyArea`).d('运用业务'),
      lookupCode: 'YP.QIS.APPLY_AREA',
      lovPara: { tenantId },
      multiple: ',',
    },
    {
      name: 'applicableProductLine',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicableProductLine`).d('适用产品线'),
      lookupCode: 'YP.QIS.APPLICABLE_PRODUCT_LINE',
      lovPara: { tenantId },
      multiple: ',',
    },
    {
      name: 'applicableChemicalSystem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicableChemicalSystem`).d('适用化学体系'),
      lookupCode: 'YP.QIS.APPLICABLE_CHEMICAL_SYSTEM',
      lovPara: { tenantId },
      multiple: ',',
    },
    {
      name: 'applicableProductStructure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicableProductStructure`).d('适用产品结构'),
      lookupCode: 'YP.QIS.APPLICABLE_PRODUCT_STRUCTURE',
      lovPara: { tenantId },
      multiple: ',',
    },
    {
      name: 'createMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('创建方式'),
      lookupCode: 'YP.QIS.VERIFICATION_CREATE_METHOD',
      lovPara: { tenantId },
      defaultValue: 'MANUAL',
      disabled: true,
    },
    {
      name: 'sourceTempLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceTempCode`).d('检证模板编码'),
      lovCode: 'YP.QIS.VERIFICATION_TEMP_CODE',
      textField: 'sourceTempCode',
      valueField: 'sourceTempId',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'sourceTempId',
      bind: 'sourceTempLov.sourceTempId',
    },
    {
      name: 'sourceTempCode',
      bind: 'sourceTempLov.sourceTempCode',
    },
    {
      name: 'createPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createPerson`).d('创建人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      disabled: true,
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      bind: 'createPersonLov.id',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      bind: 'createPersonLov.realName',
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
      disabled: true,
    },

    {
      name: 'problemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemCode`).d('关联问题编号'),
      lovCode: 'YP.QIS.PROBLEM_LIST',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'problemId',
      bind: 'problemLov.problemId',
    },
    {
      name: 'problemCode',
      bind: 'problemLov.problemCode',
    },
    {
      name: 'fromProcessLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromProcessCode`).d('问题归属工序'),
      lovCode: 'MT.MODEL.WORKCELL_SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      dynamicProps: {
        // disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({
          tenantId,
          siteIds: [record?.get('siteId')],
        }),
      },
    },
    {
      name: 'fromProcessId',
      bind: 'fromProcessLov.workcellId',
    },
    {
      name: 'fromProcessName',
      bind: 'fromProcessLov.workcellName',
    },
    {
      name: 'responsibleArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleArea`).d('问题责任领域'),
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      lovPara: { tenantId },
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDescription`).d('问题描述'),
    },
    {
      name: 'verificationUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.verificationUuid`).d('相关照片'),
      bucketName: 'qms',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-platform-execute/info/ui`,
        method: 'GET',
      };
    },
  },
});

const problemReasonDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'occur',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.occur`).d('发生原因'),
      required: true,
    },
    {
      name: 'escape',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.escape`).d('逃逸原因'),
      required: true,
    },
  ],
});

const measureDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'measure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measure`).d('措施'),
      required: true,
    },
  ],
});

const validateTaskDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'taskGroupCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskGroupCode`).d('任务组编号'),
    },
    {
      name: 'taskGroupStatus',
      type: FieldType.string,
      lovPara: { tenantId },
      defaultValue: 'NEW',
      label: intl.get(`${modelPrompt}.taskGroupStatus`).d('状态'),
      lookupCode: 'YP.QIS.VERIFICATION_TASK_GROUP_STATUS',
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际完成时间'),
    },
    {
      name: 'responsibleUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('责任人'),
    },
    {
      name: 'departmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.departmentName`).d('责任部门'),
    },
    {
      name: 'lastResponsibleUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastResponsibleUserName`).d('上一责任人'),
    },
  ],
});

const transferDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'currentResponsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.currentResponsibleUserName`).d('当前责任人'),
      lovCode: 'YP.QIS.USER_UNIT',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'responsibleUserName',
      bind: 'currentResponsibleUserLov.realName',
    },
    {
      name: 'responsibleUserId',
      bind: 'currentResponsibleUserLov.unitId',
    },
    {
      name: 'transferResponsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transferResponsibleUserName`).d('转交责任人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
      required: true,
    },
    {
      name: 'transferResponsibleUserId',
      bind: 'transferResponsibleUserLov.userId',
    },
  ],
});

const taskDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'taskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskCode`).d('任务编码'),
    },
    {
      name: 'taskContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskContent`).d('检证内容'),
      required: true,
    },
    {
      name: 'planEndTime',
      type: FieldType.date,
      format: 'YYYY-MM-DD',
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成时间'),
    },
    {
      name: 'applyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyFlag`).d('是否适用'),
      lookupCode: 'YP.QIS.Y_N',
      required: true,
    },
    {
      name: 'taskResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskResult`).d('检证结果'),
      dynamicProps: {
        required: ({ record }) => record.get('applyFlag') === 'Y',
      },
    },
    {
      name: 'noApplyReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.noApplyReason`).d('不适用原因'),
      // required: true,
      dynamicProps: {
        required: ({ record }) => record.get('applyFlag') === 'N',
      },
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('检证附件'),
      bucketName: 'qms',
    },
  ],
});

export { detailDS, problemReasonDS, measureDS, taskDS, validateTaskDS, transferDS };
