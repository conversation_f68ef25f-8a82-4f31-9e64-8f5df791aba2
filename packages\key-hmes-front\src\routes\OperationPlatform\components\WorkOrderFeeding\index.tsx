// 机台物料
import React, { useState, useEffect, useMemo } from 'react';
import { Badge } from 'choerodon-ui';
import {
  Form,
  Button,
  DataSet,
  TextField,
  Modal,
  Table,
  NumberField,
  Output,
  Icon,
  Switch,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { LabelAlign } from 'choerodon-ui/pro/lib/form/enum';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { observer } from 'mobx-react';
import { useRequest } from '@components/tarzan-hooks';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import { scanDetailDS, tableDS, subTableDS, modalTableDS } from './stores/WorkOrderFeedingDS';
import { CardLayout } from '../commonComponents';
import { useOperationPlatform } from '../../contextsStore';
import { FetchWoFeeding, ScanBarcode, AssembleDone, RevocationDone, RemoveDone } from './services';

import styles from './index.modules.less';

const WorkOrderFeeding = props => {
  const { woReportDetail, enterInfo, dispatch, workOrderData} = useOperationPlatform();

  // 获取工单下各组件及替代料
  const fetchWoFeeding = useRequest(FetchWoFeeding(), {
    manual: true,
    needPromise: true,
  });
  // 物料批扫描
  const scanBarcode = useRequest(ScanBarcode(), {
    manual: true,
    needPromise: true,
  });
  // 装配组件执行
  const assembleDone = useRequest(AssembleDone(), {
    manual: true,
    needPromise: true,
  });
  // 撤回操作
  const revocationDone = useRequest(RevocationDone(), {
    manual: true,
    needPromise: true,
  });
  // 移除组件执行
  const removeDone = useRequest(RemoveDone(), {
    manual: true,
    needPromise: true,
  });

  // 扫描信息ds
  const scanDetailDs = useMemo(
    () =>
      new DataSet({
        ...scanDetailDS(),
      }),
    [],
  );

  // modalDs
  const modalTableDs = useMemo(
    () =>
      new DataSet({
        ...modalTableDS(),
      }),
    [],
  );

  const [tableDs, setTableDs] = useState(
    new DataSet({
      ...tableDS(),
    }),
  );

  const [revokeStatus, setRevokeStatus] = useState(false);
  const [removeQtyFlag, setRemoveQtyFlag] = useState(false);
  const [replaceFlag, setReplaceFlag] = useState(false);

  useEffect(() => {
    if (woReportDetail?.workOrderId && woReportDetail?.routerStepId) {
      getWoFeedingDetail(-1);
      scanDetailDs.loadData([]);
    } else {
      setRevokeStatus(false);
      scanDetailDs.loadData([]);
      setTableDs(
        new DataSet({
          ...tableDS(),
        }),
      );
    }
  }, [woReportDetail]);

  const getWoFeedingDetail = async bomComponentId => {
    const res = await fetchWoFeeding.run({
      params: {
        workOrderId: woReportDetail?.workOrderId,
        routerStepId: woReportDetail?.routerStepId,
      },
    });

    const _tableDs = new DataSet({
      ...tableDS(),
    });

    if (res?.success && res?.rows) {
      _tableDs.loadData(res.rows);
      _tableDs?.forEach(record => {
        const subTableDs = new DataSet({
          ...subTableDS(),
        });
        subTableDs.loadData(record.get('assembleMaterialList'));
        record.set('subTableDs', subTableDs);
      });
      setRemoveQtyFlag(res.rows.every(item => item.bomComponentType !== 'REMOVE'));
      setReplaceFlag(res.rows.every(i => i.substituteMaterialList.length === 0));
    }
    setTableDs(_tableDs);
    // _tableDs.forEach(record => {
    //   if (bomComponentId === record.get('bomComponentId')) {
    //     record.set('expand', true);
    //   }
    // });
  };

  const onScan = async () => {
    // let assembledQty = 0;
    const identification = scanDetailDs?.current?.get('identification');
    if (!identification) {
      return;
    }
    const res = await scanBarcode.run({
      params: {
        identification,
        workOrderId: woReportDetail?.workOrderId,
        routerStepId: woReportDetail?.routerStepId,
      },
    });

    if (res?.success && res?.rows) {
      scanDetailDs.loadData([res.rows]);
      scanDetailDs.current?.set('scanQty', res.rows.displayQty);

      tableDs.forEach((record: any) => {
        if (record.get('bomComponentId') === res.rows.bomComponentId) {
          record.set('currentFlag', true);
        } else {
          record.set('currentFlag', false);
        }
      });
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        message: `扫描物料批${identification}成功`,
      });
      // tableDs.toData().forEach((item: any) => {
      //   if(item.identification === res.rows.identification){
      //     assembledQty = item.investedQty;
      //   }
      //   if (
      //     item.assembleMaterialList.filter(i => i.identification === res.rows.identification)
      //       .length === 1
      //   ) {
      //     assembledQty = item.investedQty;
      //   }
      // });
      // if (assembledQty > res.rows.primaryUomQty) {
      //   scanDetailDs.current?.set('scanQty', res.rows.primaryUomQty);
      // } else if (res.rows.substituteFlag === 'Y') {
      //   scanDetailDs.current?.set(
      //     'scanQty',
      //     res.rows.primaryUomQty - assembledQty * res.rows.substituteUsage,
      //   );
      // } else {
      //   scanDetailDs.current?.set('scanQty', res.rows.primaryUomQty - assembledQty);
      // }
    } else {
      scanDetailDs.loadData([{ identification }]);
      scanDetailDs.current?.set('scanQty', null);
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'FAIL',
        message: `扫描物料批${identification}失败`,
      });
    }

    setTimeout(() => {
      // @ts-ignore
      document.querySelector('#workOrderFeedingQty').focus();
    }, 200);
  };

  const onScanAdd = async () => {
    const scanDetailData = scanDetailDs?.current?.toData();
    const tableData = tableDs.toData();
    let bomComponent;
    tableData.forEach((item: any) => {
      if (scanDetailData.bomComponentId === item.bomComponentId) {
        bomComponent = item;
      };
      // if (item.materialId === scanDetailData.materialId) {
      //   if (!bomComponent) {
      //     bomComponent = item;
      //   } else if (bomComponent.lineNumber && bomComponent.lineNumber > item.lineNumber) {
      //     bomComponent = item;
      //   }
      // }
      // if (
      //   item.substituteMaterialList.filter(i => i.materialId === scanDetailData.materialId)
      //     .length === 1
      // ) {
      //   bomComponent = item;
      // }
    });

    if (!bomComponent || !scanDetailData.scanQty) {
      return;
    }
    if (bomComponent.investedQty >= bomComponent.requiredQty) {
      return notification.warning({
        message: '物料对应组件已满足需求数量，请检查！',
      });
    }


    const res = await assembleDone.run({
      params: {
        ...scanDetailDs.toData()[0],
        investedQty: bomComponent.investedQty,
        bomComponentId: bomComponent.bomComponentId,
        workOrderId: woReportDetail?.workOrderId,
        routerStepId: woReportDetail?.routerStepId,
        operationId: enterInfo?.selectOperation?.operationId,
        trxAssembleQty: scanDetailData.scanQty,
        workcellId: enterInfo.workStationId,
      },
    });

    if (res?.success) {
      notification.success({});
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        message: `物料批${scanDetailDs?.current?.get('identification')}投料${scanDetailData.scanQty}/${scanDetailData.primaryUomCode}成功`,
      });
      dispatch({
        type: 'update',
        payload: {
          workOrderData: {
            ...workOrderData,
          },
        },
      });
      getWoFeedingDetail(res?.rows?.bomComponentId || bomComponent.bomComponentId);
      onScan();
      // scanDetailDs.loadData([]);
    }
  };

  const onScanDelete = record => {
    const removeQty = record.get('removeQty');
    if (!removeQty) {
      return;
    }

    Modal.open({
      title: '生成物料批',
      destroyOnClose: true,
      key: Modal.key(),
      style: { width: '350px' },
      contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
      className: styles.machinedPartModal,
      children: (
        <Form className={styles.modalForm} labelWidth={80} dataSet={scanDetailDs}>
          <TextField name="substituteMaterialCode" />
        </Form>
      ),
      onOk: () => {
        onScanDeleteConfirm(record);
      },
    });
  };

  const onScanDeleteConfirm = async record => {
    const removeQty = record.get('removeQty');

    const bomComponentData = record.toData();

    const res = await removeDone.run({
      params: {
        bomComponentId: bomComponentData.bomComponentId,
        operationId: enterInfo?.selectOperation?.operationId,
        routerStepId: woReportDetail?.routerStepId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        substituteMaterialList: bomComponentData.substituteMaterialList,
        substituteUsage: bomComponentData.substituteUsage,
        trxAssembleQty: removeQty,
        workOrderId: woReportDetail?.workOrderId,
        workcellId: enterInfo?.workStationId,
        materialLotCode: scanDetailDs?.current?.get('substituteMaterialCode'),
      },
    });

    if (res?.success) {
      notification.success({});
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        message: `组件移除成功`,
      });
      dispatch({
        type: 'update',
        payload: {
          workOrderData: {
            ...workOrderData,
          },
        },
      });
      getWoFeedingDetail(res?.rows?.bomComponentId || bomComponentData.bomComponentId);
    }
  };

  const handleRevokeConfirm = async () => {
    const validateDsList: any = [];
    const bomComponentList: any = [];
    tableDs.forEach(record => {
      const subTableDs = record.get('subTableDs');
      validateDsList.push(subTableDs);
      const materialLotList: any = [];
      subTableDs.forEach(subRecord => {
        if (subRecord.get('revokeQty') > 0) {
          materialLotList.push({
            substituteFlag: subRecord.get('substituteFlag'),
            lotCreateFlag: subRecord.get('lotCreateFlag') === 'Y' ? 'Y' : 'N',
            materialLotId: subRecord.get('materialLotId'),
            trxAssembleQty: subRecord.get('revokeQty'),
          });
        }
      });
      if (materialLotList.length > 0) {
        bomComponentList.push({
          bomComponentId: record.get('bomComponentId'),
          bomComponentType: record.get('bomComponentType'),
          materialLotList,
        });
      }
    });

    if (bomComponentList.length === 0) {
      setRevokeStatus(false);
      return;
    }

    const normalValidate = await Promise.all(
      validateDsList.map(async validateDsListItem => {
        const itemValidate = await validateDsListItem.validate();
        return itemValidate;
      }),
    );

    const normalResult = normalValidate.every(val => val);

    if (!normalResult) {
      return;
    }

    const res = await revocationDone.run({
      params: {
        bomComponentList,
        operationId: enterInfo?.selectOperation?.operationId,
        routerStepId: woReportDetail?.routerStepId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        workOrderId: woReportDetail?.workOrderId,
        workcellId: enterInfo?.workStationId,
      },
    });

    if (res?.success) {
      notification.success({});
      const _removeType = bomComponentList.find(item => item.bomComponentType === 'REMOVE');
      if (_removeType) {
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          message: `组件移除撤回成功`,
        });
      }
      const _assembleType = bomComponentList.find(item => item.bomComponentType === 'ASSEMBLING');
      if (_assembleType) {
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          message: `组件取料投料成功`,
        });
      }
      dispatch({
        type: 'update',
        payload: {
          workOrderData: {
            ...workOrderData,
          },
        },
      });
      setRevokeStatus(false);
      getWoFeedingDetail(-1);
      onScan();
    }
  };

  const clearHeaderNum = focusRecord => {
    // scanDetailDs?.current?.set('scanQty', null);
    tableDs.forEach((record, index) => {
      if (index !== focusRecord.index) {
        record.init('removeQty', null);
      }
    });
  };

  const widthFormat = record => {
    if (!record || !record?.get('investedQty') || !record?.get('requiredQty')) {
      return `0%`;
    }
    let width = (record.get('investedQty') / record.get('requiredQty')) * 100;
    if (width > 100) {
      width = 100;
    }
    return `${width}%`;
  };

  // 标记点击的行
  function handleOnCellAndRow({ record }) {
    return {
      className: record.get('currentFlag') ? styles['current-line'] : '',
    };
  }

  const columnsParent: ColumnProps[] = [
    {
      name: 'lineNumber',
      width: 100,
      align: ColumnAlign.right,
      onCell: handleOnCellAndRow,
    },
    {
      name: 'materialCode',
      width: 230,
      onCell: handleOnCellAndRow,
      renderer: ({ value, record }: any) => {
        const showRender: any = [];
        if (record?.get('revisionCode')) {
          showRender.push(<span>{`${value}/${record?.get('revisionCode')}`}</span>);
        } else {
          showRender.push(<span>{value}</span>);
        }
        if(record?.get('bomComponentType') === 'ASSEMBLING'){
          if(record?.get('assembleMethod') === 'ISSUE'){
            showRender.push(
              <Output
                style={{
                  marginLeft: '6px',
                  borderRadius: '2px 4px',
                  padding: '2px 4px',
                  backgroundColor: 'rgba(17, 194, 207, 1)',
                  color: '#fff',
                }}
                record={record}
                name="bomComponentType"
              />,
            );
          }else{
            // bomComponentType=ASSEMBLING时assembleMethod=ISSUE展示为装配，assembleMethod=BACKFLASH展示为反冲
            showRender.push(
              <Output
                style={{
                  marginLeft: '6px',
                  borderRadius: '2px 4px',
                  padding: '2px 4px',
                  backgroundColor:'rgba(17, 194, 207, 1)',
                  color: 'rgba(255, 255, 255, 0.5)',
                }}
                record={record}
                name="bomComponentType"
                renderer={() => {
                  return '反冲';
                }}
              />,
            );
          }
        }else{
          showRender.push(
            <Output
              style={{
                marginLeft: '6px',
                borderRadius: '2px 4px',
                padding: '2px 4px',
                backgroundColor: 'rgba(224, 190, 94, 1)',
                color: '#fff',
              }}
              record={record}
              name="bomComponentType"
            />,
          );
        }
        return showRender;
      },
    },
    {
      name: 'materialDesc',
      onCell: handleOnCellAndRow,
    },
    {
      name: 'requiredQty',
      onCell: handleOnCellAndRow,
      renderer: ({ record }) => {
        return (
          <div className={styles['qty-box']}>
            <div className="qty-bg">
              <div className="qty-bg-box">
                <div
                  className="qty-bg-inner"
                  style={{
                    width: widthFormat(record),
                  }}
                ></div>
              </div>
            </div>
            <div className="qty-front">
              {`${record?.get('investedQty')}/${record?.get('requiredQty')}`}
            </div>
          </div>
        );
      },
      align: ColumnAlign.center,
    },
    {
      name: 'removeQty',
      width: 200,
      onCell: handleOnCellAndRow,
      hidden: revokeStatus || removeQtyFlag,
      renderer: ({ record }: any) => {
        if (!revokeStatus && record.get('bomComponentType') === 'REMOVE') {
          return (
            <NumberField
              disabled={
                fetchWoFeeding.loading ||
                scanBarcode.loading ||
                assembleDone.loading ||
                revocationDone.loading ||
                removeDone.loading
              }
              name="removeQty"
              onFocus={() => {
                clearHeaderNum(record);
              }}
              onEnterDown={() => {
                onScanDelete(record);
              }}
              record={record}
            />
          );
        }
        return <Output />;
      },
    },
    {
      name: 'revokeQtySum',
      onCell: handleOnCellAndRow,
      hidden: !revokeStatus,
      renderer: ({ record }: any) => {
        const _subTableDs = record.get('subTableDs');
        let _revokeQtySum = 0;
        _subTableDs.forEach(subRecord => {
          const _revokeQty = subRecord.get('revokeQty');
          if (_revokeQty) {
            _revokeQtySum += _revokeQty;
          }
        });
        return (
          <span
            style={{
              color: 'rgba(224, 190, 94, 1)',
              fontWeight: 'bolder',
            }}
          >
            {_revokeQtySum}
          </span>
        );
      },
    },
    {
      name: 'replace',
      width: 80,
      align: ColumnAlign.center,
      onCell: handleOnCellAndRow,
      hidden: revokeStatus || replaceFlag,
      renderer: ({ record }) => {
        if (record?.get('substituteMaterialList')?.length > 0) {
          return (
            <a
              style={{
                color: 'rgba(51, 241, 255, 1)',
              }}
              onClick={() => {
                showSubModal(record);
              }}
            >
              查看
            </a>
          );
        }
        return null;
      },
    },
  ];

  const columnsChild: ColumnProps[] = [
    {
      name: 'identification',
    },
    {
      name: 'materialCode',
      renderer: ({ value, record }: any) => {
        const showRender: any = [];
        showRender.push(<span>{value}</span>);
        if (record?.get('substituteFlag') === 'Y') {
          showRender.push(
            <span
              style={{
                marginLeft: '6px',
                borderRadius: '2px 4px',
                padding: '2px 4px',
                backgroundColor: 'rgba(224, 190, 94, 1)',
                color: '#3c87ad',
                fontWeight: 'bolder',
              }}
            >
              T
            </span>,
          );
        }

        return showRender;
      },
    },
    {
      name: 'lot',
    },
    {
      name: 'assembleQty',
    },
    {
      name: 'lotCreateFlag',
      width: 100,
      align: ColumnAlign.center,
      hidden: !revokeStatus,
      renderer: ({ record }: any) => {
        return <Switch name="lotCreateFlag" record={record} />;
      },
    },
    {
      name: 'revokeQty',
      width: 100,
      hidden: !revokeStatus,
      renderer: ({ record }: any) => {
        const _type = tableDs.data.find(item => item.get('bomComponentId') === record.get('bomComponentId'))?.get('bomComponentType') || '';
        const _method = tableDs.data.find(item => item.get('bomComponentId') === record.get('bomComponentId'))?.get('assembleMethod') || '';
        const _flag = (_type === 'ASSEMBLING' && _method !== 'ISSUE');
        return (
          <NumberField
            disabled={
              fetchWoFeeding.loading ||
              scanBarcode.loading ||
              assembleDone.loading ||
              revocationDone.loading ||
              removeDone.loading ||
              _flag
            }
            className="revoke-input"
            name="revokeQty"
            record={record}
          />
        );
      },
    },
  ];

  const columnsModal: ColumnProps[] = [
    {
      name: 'materialCode',
      renderer: ({ value, record }: any) => {
        const showRender: any = [];
        if (record?.get('revisionCode')) {
          showRender.push(<span>{`${value}/${record?.get('revisionCode')}`}</span>);
        } else {
          showRender.push(<span>{value}</span>);
        }
        return showRender;
      },
    },
    {
      name: 'substituteUsage',
    },
    {
      name: 'assembleQty',
    },
  ];

  const FormRender = ({ ds }) => {
    if (ds.current?.get('materialLotId')) {
      return (
        <Form
          dataSet={ds}
          columns={3}
          labelWidth="auto"
          className={styles['form-box']}
          labelAlign={LabelAlign.left}
        >
          <Output name="identification" />
          <Output
            name="materialCode"
            renderer={({ value, record }) => {
              if (record?.get('revisionCode')) {
                return `${value}/${record?.get('revisionCode')}`;
              }
              return value;
            }}
          />
          <Output
            name="primaryUomQty"
            renderer={({ value, record }) => {
              if (record?.get('primaryUomCode')) {
                return `${value}/${record?.get('primaryUomCode')}`;
              }
              return value;
            }}
          />
          <Output name="lot" />
          <Output name="locatorCode" />
          <Output
            name="qualityStatus"
            renderer={({ value, record }) => {
              if (value) {
                return (
                  <span style={{display: 'flex', alignItems: 'center'}}>
                    <Badge status={value === 'OK' ? 'success' : 'error'} />
                    {record?.get('qualityStatusDesc')}
                  </span>
                );
              }
              return null;
            }}
          />
        </Form>
      );
    }
    return null;
  };

  const revokeChange = type => {
    setRevokeStatus(type);
  };

  const handleConfirm = () => {
    if (scanDetailDs?.current?.get('scanQty')) {
      onScanAdd();
    } else {
      let _record;
      tableDs.forEach(record => {
        if (record.get('removeQty')) {
          _record = record;
        }
      });
      onScanDelete(_record);
    }
  };

  const expandedRowRenderers = ({ record }) => {
    const subTableDs = record.get('subTableDs');
    return (
      <Table
        className="feeding-table-sub"
        columns={columnsChild}
        dataSet={subTableDs}
        rowHeight={40}
        customizedCode="WorkOrderFeedingChild"
      />
    );
  };

  const expandIcon = ({ record }) => {
    const subTableDs = record.get('subTableDs');
    if (subTableDs?.length > 0) {
      return (
        <Icon
          onClick={() => {
            record.set('expand', !record.get('expand'));
          }}
          style={{
            fontSize: 16,
            color: `${record.get('expand') ? 'rgba(51, 241, 255, 1)' : 'rgba(255, 255, 255, 0.7)'}`,
          }}
          type={record.get('expand') ? 'baseline-arrow_drop_down' : 'baseline-arrow_right'}
        />
      );
    }
    return null;
  };

  const showSubModal = record => {
    const { substituteMaterialList } = record.toData() || {};
    modalTableDs.loadData(substituteMaterialList || []);
    Modal.open({
      title: '替代物料',
      destroyOnClose: true,
      key: Modal.key(),
      style: { width: '720px' },
      contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
      className: styles.machinedPartModal,
      children: <Table dataSet={modalTableDs} columns={columnsModal} />,
      cancelButton: false,
    });
  };

  return (
    <CardLayout.Layout
      spinning={
        fetchWoFeeding.loading ||
        scanBarcode.loading ||
        assembleDone.loading ||
        revocationDone.loading ||
        removeDone.loading
      }
      className={styles.machineMaterials}
    >
      <CardLayout.Header
        className='WorkOrderFeedingHead'
        title="工单投料"
        help={props?.cardUsage?.remark}
        content={
          !revokeStatus && (
            <>
              <TextField
                placeholder="请扫描物料批"
                disabled={
                  !woReportDetail?.workOrderId ||
                  fetchWoFeeding.loading ||
                  scanBarcode.loading ||
                  assembleDone.loading ||
                  revocationDone.loading ||
                  removeDone.loading
                }
                dataSet={scanDetailDs}
                name="identification"
                onEnterDown={onScan}
                prefix={<img src={scanIcon} alt='' style={{height: '19px'}}/>}
                style={{
                  marginRight: '10px',
                }}
              />
              <NumberField
                id="workOrderFeedingQty"
                placeholder="回车/确认执行"
                disabled={
                  !woReportDetail?.workOrderId ||
                  fetchWoFeeding.loading ||
                  scanBarcode.loading ||
                  assembleDone.loading ||
                  revocationDone.loading ||
                  removeDone.loading
                }
                dataSet={scanDetailDs}
                onEnterDown={onScanAdd}
                name="scanQty"
                onFocus={() => {
                  clearHeaderNum({ index: -1 });
                }}
              />
            </>
          )
        }
        addonAfter={
          <>
            { revokeStatus ? (
              <>
                <Button
                  loading={
                    fetchWoFeeding.loading ||
                    scanBarcode.loading ||
                    assembleDone.loading ||
                    revocationDone.loading ||
                    removeDone.loading
                  }
                  className={styles['cancel-button']}
                  onClick={() => {
                    revokeChange(false);
                  }}
                >
                  取消
                </Button>
                <Button
                  loading={
                    fetchWoFeeding.loading ||
                    scanBarcode.loading ||
                    assembleDone.loading ||
                    revocationDone.loading ||
                    removeDone.loading
                  }
                  className={styles['ok-button']}
                  onClick={handleRevokeConfirm}
                >
                  确认
                </Button>
              </>
            ) :
              (
                <>
                  <Button
                    loading={
                      fetchWoFeeding.loading ||
                        scanBarcode.loading ||
                        assembleDone.loading ||
                        revocationDone.loading ||
                        removeDone.loading
                    }
                    className={styles['ok-button']}
                    disabled={!woReportDetail?.workOrderId}
                    onClick={handleConfirm}
                  >
                      确认
                  </Button>
                  <Button
                    loading={
                      fetchWoFeeding.loading ||
                        scanBarcode.loading ||
                        assembleDone.loading ||
                        revocationDone.loading ||
                        removeDone.loading
                    }
                    className={styles['revoke-button']}
                    disabled={!woReportDetail?.workOrderId}
                    onClick={() => {
                      revokeChange(true);
                    }}
                  >
                      撤回
                  </Button>
                </>
              )
            }
          </>
        }
      />
      <CardLayout.Content className='WorkOrderFeedingForm'>
        <FormRender ds={scanDetailDs} />
        <div className={styles['feeding-table']} id={styles.WorkOrderFeeding}>
          <Table
            dataSet={tableDs}
            columns={columnsParent}
            expandIconColumnIndex={0}
            expandedRowRenderer={expandedRowRenderers}
            expandIconAsCell={false}
            expandIcon={expandIcon}
            rowHeight={40}
            customizable
            columnResizable
            columnHideable
            customizedCode="WorkOrderFeeding"
            onRow={({ record }) => {
              if (record && record.data && record.get('bomComponentType') === 'ASSEMBLING' && record.get('assembleMethod') === 'BACKFLASH') {
                return {
                  className: styles.backFlashType,
                };
              }
              return {};
            }}
          />
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(WorkOrderFeeding);
