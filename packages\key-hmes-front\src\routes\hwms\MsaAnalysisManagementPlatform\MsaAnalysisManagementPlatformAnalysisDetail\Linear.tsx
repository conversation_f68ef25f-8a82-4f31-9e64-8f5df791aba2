import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Form, Lov, Table, TextField, NumberField } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import uuid from 'uuid/v4';
import { useMemoizedFn } from 'ahooks';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import myInstance from '@utils/myAxios';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { downloadFile } from '@services/api';
import { API_HOST } from '@/utils/constants';
import { getCurrentOrganizationId } from 'utils/utils';
import ExcelUpload, { ExcelUploadProps } from './components/ExcelUpload';

import LinearGraphic from '../components/LinearGraphic';
import { formDS } from '../stores/linearDS';
import { analyseResultDS } from '../stores/DetailDS';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

interface GraphicDataProps {
  linearChartInfo: object;
  linearTableInfo: object;
  msaResult: string; // 分析结果
  msaConclusion: string; // 分析结论
  tableInfo: [];
}

// 默认数据
const apiDataDefault = [
  {
    measureDate: '',
    measureTableList: [
      {
        measureDataColumn: 1,
        measureDataRow: 1,
        measureDataValue: '',
        biasValue: '',
      },
    ],
    range: '',
    average: '',
  },
];

const Linear = props => {
  const { msaStatus, currentUserFlag } = props;

  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
      }),
    [],
  );
// 分析结果DS
  const analyseResultDs = useMemo(() => new DataSet(analyseResultDS()), []);
  const [graphicData, setGraphicData] = useState<GraphicDataProps>({
    linearChartInfo: {},
    linearTableInfo: {},
    msaResult: '',
    msaConclusion: '',
    tableInfo: [],
  });

  const templateData = [
    {
      measureDate: intl.get(`${modelPrompt}.referenceValue`).d('基准值'),
      uuid: uuid(),
      type: 'measureDate',
    },
    {
      measureDataRow: 1,
      measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
      uuid: uuid(),
      type: 'measureDataValue',
    },
    {
      measureDate: intl.get(`${modelPrompt}.biasedMean`).d('偏倚平均值'),
      uuid: uuid(),
      type: 'average',
    },
    {
      measureDate: intl.get(`${modelPrompt}.measureValueRange`).d('测量值极差'),
      uuid: uuid(),
      type: 'range',
    },
  ];

  const [apiData, setApiData] = useState([]);
  const [defaultData, setDefaultData] = useState(templateData); // 有默认数据时初始化
  const [havaSaveData, setHaveSaveData] = useState(false);

  const [defaultField, setDefaultField] = useState([
    {
      name: 'measureDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subGroup`).d('子组号'),
    },
    {
      name: 'measureDataRow',
      type: FieldType.string,
    },
    {
      name: 'measureDataColumn1',
      computedProps: {
        type: ({ record }) => {
          if (record.get('type') === 'measureDate') {
            return FieldType.number;
          }
        },
        disabled: ({ record }) => {
          if (record.get('type') === 'average' || record.get('type') === 'range') {
            return true;
          }
        },
        required: ({ record }) => {
          return record.get('type') === 'measureDataValue' || record.get('type') === 'measureDate';
        },
      },
    },
  ]);

  const userDs = useMemo(
    () =>
      new DataSet({
        forceValidate: true,
        autoCreate: false,
        selection: false,
        paging: false,
        primaryKey: 'uuid',
        data: defaultData,
        fields: defaultField,
      }),
    [],
  );

  const [currentColumns, setCurrentColumns] = useState([
    {
      name: 'measureDataRow',
      width: 80,
      lock: ColumnLock.left,
      align: ColumnAlign.center,
      renderer: ({ value, dataSet, record }) => {
        if (value) {
          return (
            <>
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => dataSet?.remove(record)}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <PermissionButton
                  type="c7n-pro"
                  disabled={Number(value) === 1} // 测量值最少需要一行数据
                  icon="remove"
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </Popconfirm>
              <span>{value}</span>
            </>
          );
        }
      },
    },
    {
      name: 'measureDataColumn1',
      editor: record => {
        return !(record.get('type') === 'average' || record.get('type') === 'range');
      },
      align: ColumnAlign.center,
      header: () => (
        <>
          <span>1</span>
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteColumn('measureDataColumn1')}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              funcType="flat"
              shape="circle"
              size="small"
            />
          </Popconfirm>
        </>
      ),
    },
  ]); // 实际columns

  useEffect(() => {
    myInstance
      .get(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/info/ui?msaTaskLineId=${props.msaTaskLineId}`,
      )
      .then(res => {
        if (res.data.success) {
          // 处理数据
          const {
            expectedDeterioration,
            sampleDescription,
            measuredBy,
            measuredByName,
          } = res.data.rows;
          if (expectedDeterioration) {
            if (expectedDeterioration)
              formDs.current?.set('expectedDeterioration', expectedDeterioration);
            if (sampleDescription) formDs.current?.set('sampleDescription', sampleDescription);
            if (measuredBy)
              formDs.current?.set('improveByObj', {
                userId: measuredBy,
                realName: measuredByName,
              });
          }
          if ((res.data.rows.tableInfo || []).length) {
            setApiData(res.data.rows.tableInfo);
            setHaveSaveData(true);
          } else {
            setApiData(apiDataDefault);
            setHaveSaveData(false);
          }
          handleUpdateChartData(res.data.rows || {});
        } else {
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          });
        }
      });
  }, []);

  useEffect(() => {
    const colums = apiData.length; // 总共有多少列数据
    if (colums > 0) {
      const rows = apiData[0].measureTableList.length; // 测量数据共有多少行
      // 先生成，时间，测量值极差，偏倚平均值的数据
      let transformDataResult = [];
      const timeObj = {
        measureDate: intl.get(`${modelPrompt}.referenceValue`).d('基准值'),
        uuid: uuid(),
        type: 'measureDate',
      };
      const rangeObj = {
        measureDate: intl.get(`${modelPrompt}.measureValueRange`).d('测量值极差'),
        uuid: uuid(),
        type: 'range',
      };
      const averageObj = {
        measureDate: intl.get(`${modelPrompt}.biasedMean`).d('偏倚平均值'),
        uuid: uuid(),
        type: 'average',
      };
      let tiledData: any = [];

      apiData.forEach((item: any) => {
        timeObj[`measureDataColumn${item.measureTableList[0].measureDataColumn}`] =
          item.standardValue; // 基准值
        rangeObj[`measureDataColumn${item.measureTableList[0].measureDataColumn}`] = item.range;
        averageObj[`measureDataColumn${item.measureTableList[0].measureDataColumn}`] =
          item.biasAverage; // 偏倚平均值
        tiledData = [...tiledData, ...item.measureTableList];
      });
      // @ts-ignore
      transformDataResult = [
        timeObj,
        {
          measureDate: intl.get(`${modelPrompt}.measureDesc`).d('测量值与偏倚'),
          uuid: uuid(),
          type: 'measureDesc',
        },
      ];

      // 生成测量值数据
      for (let rowSeq = 0; rowSeq < rows; rowSeq++) {
        const obj = {
          measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
          uuid: uuid(),
          type: 'measureDataValue',
          measureDataRow: String(rowSeq + 1),
        };
        for (let colSeq = 0; colSeq < colums; colSeq++) {
          tiledData.forEach((item: any) => {
            if (item.measureDataRow === rowSeq + 1 && item.measureDataColumn === colSeq + 1) {
              obj[`measureDataColumn${colSeq + 1}`] = item.measureDataValue;
              // 增加偏倚展示的数据biasValue
              obj[`biasValue${colSeq + 1}`] = item.biasValue;
            }
          });
        }
        transformDataResult.push(obj);
      }

      transformDataResult = [...transformDataResult, averageObj, rangeObj];

      // 生产行列配置
      const feild = [
        {
          name: 'measureDate',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.subGroup`).d('子组号'),
        },
        {
          name: 'measureDataRow',
          type: FieldType.string,
        },
      ];

      const column = [
        {
          name: 'measureDataRow',
          width: 80,
          lock: ColumnLock.left,
          align: ColumnAlign.center,
          renderer: ({ value }) => {
            if (value) {
              return (
                <>
                  <Popconfirm
                    title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                    // onConfirm={() => dataSet?.remove(record)}
                    onConfirm={() => removeData(value)}
                    okText={intl.get('tarzan.common.button.confirm').d('确认')}
                    cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                  >
                    <PermissionButton
                      type="c7n-pro"
                      disabled={
                        Number(value) === 1 || Number(value) <= transformDataResult.length - 4
                      } // 测量值最少需要一行数据(-4是因为减去4行的非测量值
                      icon="remove"
                      funcType="flat"
                      shape="circle"
                      size="small"
                    />
                  </Popconfirm>
                  <span>{value}</span>
                </>
              );
            }
          },
        },
      ];
      for (let colSeq = 0; colSeq < colums; colSeq++) {
        feild.push({
          name: `measureDataColumn${colSeq + 1}`,
          // @ts-ignore
          computedProps: {
            type: ({ record }) => {
              if (record.get('type') === 'measureDate') {
                return FieldType.number;
              }
              return FieldType.number;
            },
            disabled: ({ record }) => {
              if (record.get('type') === 'average' || record.get('type') === 'range') {
                return true;
              }
            },
            required: ({ record }) => {
              return (
                record.get('type') === 'measureDataValue' || record.get('type') === 'measureDate'
              );
            },
          },
        });
        column.push({
          name: `measureDataColumn${colSeq + 1}`,
          align: ColumnAlign.center,
          // @ts-ignore
          header: () => (
            <>
              <span>{colSeq + 1}</span>
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => deleteColumn(`measureDataColumn${colSeq + 1}`)}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <PermissionButton
                  type="c7n-pro"
                  icon="remove"
                  disabled={havaSaveData || Number(colSeq) === 0}
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </Popconfirm>
            </>
          ),
          // @ts-ignore
          editor: record => {
            if (['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag) {
              return false;
            }
            if (record.get('type') === 'average' || record.get('type') === 'range') {
              return false;
            }
            if (record.get('type') === 'measureDate') {
              return true;
            }
            if (record.get('type') === 'measureDataValue') {
              return false;
            }
          },
          renderer: ({ value, record }) => {
            if (record.get('type') === 'measureDataValue') {
              return (
                <div className={styles['cell-num-container']}>
                  <NumberField
                    disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
                    record={record}
                    name={`measureDataColumn${colSeq + 1}`}
                  />
                  <span>{record.get(`biasValue${colSeq + 1}`)}</span>
                </div>
              );
            }
            if (record.get('type') === 'measureDesc') {
              return (
                <div className={styles['cell-measure-desc']}>
                  <div>{intl.get(`${modelPrompt}.measureDataValue`).d('测量值')}</div>
                  <div>{intl.get(`${modelPrompt}.bias`).d('偏倚')}</div>
                </div>
              );
            }
            if (value && typeof value !== 'number') {
              return value?.toString() || '';
            }
            return value;
          },
        });
      }
      setCurrentColumns(column);
      feild.forEach((item: any) => {
        userDs.addField(item.name, {
          ...item,
        });
      });
      setDefaultField(feild);
      setDefaultData(transformDataResult);
      userDs.loadData(transformDataResult);
    }
  }, [apiData, havaSaveData]);

  const handleUpdateChartData = dataSource => {
    const {
      linearChartInfo = {},
      linearTableInfo = {},
      msaResult = '',
      msaConclusion = '',
      tableInfo = [],
    } = dataSource;
    setGraphicData({
      linearChartInfo,
      linearTableInfo,
      msaResult,
      msaConclusion,
      tableInfo,
    });
    analyseResultDs.loadData([{
      msaResult,
      msaConclusion,
    }])
  };

  const deleteColumn = name => {
    setCurrentColumns(prevColumns => prevColumns.filter(item => item.name !== name));
  };

  const removeData = useMemoizedFn(measureDataRow => {
    const data = userDs.toData();
    const newData = [];
    data.forEach((item: any) => {
      if (item.measureDataRow !== measureDataRow) {
        return newData.push(item);
      }
    });
    userDs.loadData(newData);
  });

  const addColumn = async () => {
    const maxNumber = Number(
      currentColumns[currentColumns.length - 1].name.replace('measureDataColumn', ''),
    );
    const columnName = `measureDataColumn${maxNumber + 1}`;
    userDs.addField(columnName, {
      computedProps: {
        type: ({ record }) => {
          if (record.get('type') === 'measureDate') {
            return FieldType.number;
          }
          return FieldType.number;
        },
        disabled: ({ record }) => {
          if (record.get('type') === 'average' || record.get('type') === 'range') {
            return true;
          }
        },
        required: ({ record }) => {
          return record.get('type') === 'measureDataValue' || record.get('type') === 'measureDate';
        },
      },
    });
    setDefaultField([
      ...defaultField,
      {
        name: columnName,
        computedProps: {
          type: ({ record }) => {
            if (record.get('type') === 'measureDate') {
              return FieldType.number;
            }
            return FieldType.number;
          },
          disabled: ({ record }) => {
            if (record.get('type') === 'average' || record.get('type') === 'range') {
              return true;
            }
          },
          required: ({ record }) => {
            return (
              record.get('type') === 'measureDataValue' || record.get('type') === 'measureDate'
            );
          },
        },
      },
    ]);
    const newColumns: Array<object> = [
      ...currentColumns,
      {
        name: columnName,
        header: () => (
          <>
            <span>{maxNumber + 1}</span>
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => deleteColumn(columnName)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <PermissionButton
                type="c7n-pro"
                icon="remove"
                funcType="flat"
                shape="circle"
                size="small"
              />
            </Popconfirm>
          </>
        ),
        align: ColumnAlign.center,
        editor: record => {
          if (record.get('type') === 'average' || record.get('type') === 'range') {
            return false;
          }
          if (record.get('type') === 'measureDate') {
            return true;
          }
          if (record.get('type') === 'measureDataValue') {
            return false;
          }
        },
        renderer: ({ value, record }) => {
          if (record.get('type') === 'measureDataValue') {
            return (
              <div className={styles['cell-num-container']}>
                <NumberField
                  disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
                  record={record}
                  name={columnName}
                />
                <span>{record.get(`biasValue${maxNumber + 1}`)}</span>
              </div>
            );
          }
          if (record.get('type') === 'measureDesc') {
            return (
              <div className={styles['cell-measure-desc']}>
                <div>{intl.get(`${modelPrompt}.measureDataValue`).d('测量值')}</div>
                <div>{intl.get(`${modelPrompt}.bias`).d('偏倚')}</div>
              </div>
            );
          }
          if (value && typeof value !== 'number') {
            return value?.toString() || '';
          }
          return value;
        },
      },
    ];
    setCurrentColumns(newColumns);
  };

  const addMeasuredValue = () => {
    const data = userDs.toData();
    let max = 0;
    data.forEach((item: any) => {
      if (item.measureDataRow > max) {
        max = item.measureDataRow;
      }
    });

    userDs.loadData([
      ...userDs.toData(),
      {
        // @ts-ignore
        measureDataRow: Number(max) + 1,
        measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
        uuid: uuid(),
        type: 'measureDataValue',
      },
    ]);
  };

  const groups = [
    {
      name: 'measureDate',
      align: ColumnAlign.center,
      parentField: undefined,
      type: 'column',
      lock: ColumnLock.left,
      columnProps: {
        width: 120,
        header: () => (
          <>
            <span>{intl.get(`${modelPrompt}.partNum`).d('零件编号')}</span>
            <PermissionButton
              type="c7n-pro"
              icon="add"
              onClick={addColumn}
              disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
              funcType="flat"
              shape="circle"
              size="small"
            />
          </>
        ),
        renderer: ({ text }) => (
          <>
            <span>{text}</span>
            {text === intl.get(`${modelPrompt}.measureDataValue`).d('测量值') && (
              <PermissionButton
                type="c7n-pro"
                icon="add"
                onClick={addMeasuredValue}
                disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
                funcType="flat"
                shape="circle"
                size="small"
              />
            )}
          </>
        ),
      },
    },
  ];

  const handleSave = async () => {
    // @ts-ignore
    const validateResult = await userDs.validate();
    const formValidate = await formDs.validate();
    if (!validateResult || !formValidate) {
      return;
    }
    const originData = userDs.toData();

    let measureDataColumnArr: any[] = [];
    const measureDataRowArr: any[] = [];
    originData.forEach((item: any) => {
      if (item.type === 'measureDataValue' && measureDataColumnArr.length < 1) {
        // 打平key
        measureDataColumnArr = Object.keys(item).filter(
          word => word.indexOf('measureDataColumn') !== -1,
        );
      }
      if (item.type === 'measureDataValue') {
        measureDataRowArr.push(item.measureDataRow);
      }
    });

    const resultData: Array<any> = [];

    // eslint-disable-next-line array-callback-return
    measureDataColumnArr.map(colums => {
      let measureDate = '';
      originData.forEach((rowData: any) => {
        if (rowData.type === 'measureDate') {
          measureDate = rowData[colums];
        }
      });
      const measureTableList = [] as any;
      measureDataRowArr.forEach(rowSquence => {
        originData.forEach((originDataItem: any) => {
          if (
            originDataItem.type === 'measureDataValue' &&
            originDataItem.measureDataRow === rowSquence
          ) {
            measureTableList.push({
              measureDataValue: originDataItem[colums],
              measureDataColumn: colums.replace('measureDataColumn', ''),
              measureDataRow: rowSquence,
            });
          }
        });
      });
      resultData.push({
        standardValue: measureDate, // 基准值
        measureTableList,
      });
    });
    const formData = formDs.toData();
    myInstance
      .post(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/save/ui`, {
        msaTaskLineId: props.msaTaskLineId,
        ...formData[0],
        tableInfo: resultData,
      })
      .then(res => {
        if (res.data.success) {
          // 处理数据
          setApiData(res.data.rows.tableInfo);
          setHaveSaveData(true);
          props.updateHeaderInfo(); // 更新头部
          handleUpdateChartData(res.data.rows || {});
          notification.success({});
        } else {
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          });
        }
      });
  };

  // 模板下载
  const downloadTemp = async () => {
    await downloadFile({
      requestUrl: `/himp/v1/${tenantId}/template/YP.QIS_MSA_IMPORT_LINEARITY/excel`,
      queryParams: [{ name: 'tenantId', value: tenantId }],
      method: 'GET',
    });
  };

  const handleUploadSuccess = res => {
    setApiData(res.rows.tableInfo);
    setHaveSaveData(false);
    notification.success({
      message: intl.get(`${modelPrompt}.notification.importSuccess`).d('导入成功'),
    });
  };

  const excelUploadProps: ExcelUploadProps = {
    url: `${API_HOST}${
      BASIC.TARZAN_SAMPLING
    }/v1/${tenantId}/qis-msa-analysis/import/ui?msaTaskLineId=${
      props.msaTaskLineId
    }&expectedDeterioration=${formDs.current?.get('expectedDeterioration') ||
      ''}&sampleDescription=${formDs.current?.get('sampleDescription') ||
      ''}&measuredBy=${formDs.current?.get('measuredBy') || ''}`,
    params: {},
    onSuccess: res => handleUploadSuccess(res),
  };

  return (
    <div>
      <Button
        color={ButtonColor.primary}
        onClick={handleSave}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        {intl.get(`${modelPrompt}.button.save`).d('保存')}
      </Button>
      <Button onClick={downloadTemp} icon="get_app">
        {intl.get(`${modelPrompt}.templateDownload`).d('模板下载')}
      </Button>
      <ExcelUpload {...excelUploadProps} />
      <Form
        dataSet={formDs}
        columns={3}
        style={{ marginTop: 10 }}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        <NumberField name="expectedDeterioration" />
        <TextField name="sampleDescription" />
        <Lov name="improveByObj" />
      </Form>
      <Table
        columnDraggable
        columnTitleEditable
        aggregation={false}
        border
        dataSet={userDs}
        columns={currentColumns as any}
        groups={groups as any}
        // style={{ height: 500 }}
        // virtual
      />
      <LinearGraphic dataSoure={graphicData} analyseResultDs={analyseResultDs} />
    </div>
  );
};

export default Linear;
