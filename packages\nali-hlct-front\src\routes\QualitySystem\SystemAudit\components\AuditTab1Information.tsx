/**
 * @Description: 体系审核管理维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect } from 'react';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import { Tabs } from 'choerodon-ui';
import {
  Form,
  Button,
  Lov,
  TextField,
  TextArea,
  Select,
  Attachment,
  Spin,
  Table,
  Icon,
  Modal,
} from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';
import { getCurrentUser } from 'utils/utils';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import TabPane from 'choerodon-ui/lib/tabs/TabPane';
import { getCurrentOrganizationId } from 'utils/utils';
import { useDataSet } from 'utils/hooks';
import { compact } from 'lodash';
import axios from 'axios';
import { TableMode, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import styles from '../index.modules.less';
import { saveAuditInformationConfig } from '../services';
import ExternalFactory from '../stores/externalDs';
import TreeInterFactory from '../stores/treeInternalDs';

const modelPrompt = 'tarzan.systemAudit';

const AuditInformation = props => {
  const {
    id,
    canEdit,
    setCanEdit,
    readonly,
    auditInformationDs,
    initPageData = () => {},
    fetchAuditInformation,
    sysReviewPlanStatus,
    commitOaSubmit,
    threeResList,
    pubFlag,
  } = props;

  const [user] = useState(getCurrentUser()); // 用户详细信息

  const externalDs = useDataSet(ExternalFactory, 'ExternalFactory');

  const treeInternalDs = useDataSet(TreeInterFactory, 'TreeInterFactory');

  // 审核信息保存
  const saveAuditInformation = useRequest(saveAuditInformationConfig(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if (id === 'create') {
      auditInformationDs.loadData([
        {
          sysReviewPlanStatus: 'NEW',
          planCreaterId: user.id,
          planCreaterName: user.realName,
        },
      ]);
    }
  }, [id]);

  const handleCancel = () => {
    setCanEdit(false);
    if (id === 'create') {
      props.history.push(`/hwms/system-audit/list`);
    } else {
      initPageData('AuditTab1Information');
    }
  };

  const handleSave = async () => {
    const validateResult = await auditInformationDs.validate();
    if (!validateResult) {
      return;
    }
    const { ...params } = auditInformationDs.toData()[0];
    params.sysReviewStandard = (params.sysReviewStandardIdList || []).join(',');
    const res = await saveAuditInformation.run({
      params,
    });
    if (res?.success) {
      // @ts-ignore
      notification.success();
      setCanEdit(false);
      if (id === 'create') {
        props.history.push(`/hwms/system-audit/detail/${res.rows}`);
      } else {
        initPageData('AuditTab1Information');
      }
    }
  };

  const handleSiteChange = () => {
    auditInformationDs.current.init('sysReviewStandardLov', null);
  };

  const externalColumns: ColumnProps[] = [
    {
      name: 'fileCode',
    },
    {
      name: 'fileName',
    },
    {
      name: 'fileLevel',
    },
    {
      name: 'version',
    },
    {
      name: 'processType',
    },
    {
      name: 'affliatedProcess',
    },
  ];

  const internalColumns: ColumnProps[] = [
    {
      name: 'templateFileCode',
      width: 150,
      renderer: ({ record }) => {
        const { text } = treeInternalDs.queryDataSet?.toData()[0] || { text: null };
        if (record?.get('templateFileCode').indexOf(text) > -1) {
          return <span style={{ color: 'red' }}>{record?.get('templateFileCode')}</span>;
        }
        return record?.get('templateFileCode');
      },
    },
    {
      name: 'templateFileName',
      width: 150,
      renderer: ({ record }) => {
        const { text } = treeInternalDs.queryDataSet?.toData()[0];
        if (record?.get('templateFileName').indexOf(text) > -1) {
          return <span style={{ color: 'red' }}>{record?.get('templateFileName')}</span>;
        }
        return record?.get('templateFileName');
      },
    },
    {
      name: 'action',
      renderer: ({ record }) => {
        if (
          (record?.get('subInfo') && record?.get('subInfo').length > 0) ||
          (record?.get('subList') && record?.get('subList').length > 0)
        ) {
          if (!record?.get('all') && !record?.isSelected) {
            return (
              <span>
                <a
                  onClick={() => {
                    // 勾选顶层的对色
                    treeInternalDs.select(record);
                    // 色选子层的对勾，顶层跟子层取值不同
                    if (record.get('nodeLevel') === 0) {
                      treeIndex(record.get('subInfo'));
                    } else {
                      treeIndex(record.get('subList'));
                    }
                    record?.set('all', true);
                  }}
                >
                  全选
                </a>
              </span>
            );
          }
          return (
            <span>
              <a
                onClick={() => {
                  treeInternalDs.treeUnSelect(record);
                  record?.set('all', false);
                }}
              >
                取消
              </a>
            </span>
          );
        }
      },
    },
  ];

  const treeIndex = arr => {
    arr.forEach(item => {
      const index = treeInternalDs.findIndex(
        r => r.get('templateFileDId') === item.templateFileDId,
      );
      treeInternalDs.select(index);
      if (item.subList && item.subList.length > 0) {
        treeIndex(item.subList);
      }
    });
    return list;
  };

  const list: Array<any> = [];
  // 弹框中树结构的数据处理
  const treeList = (arr, path) => {
    if (path === -1) {
      arr.map((item, i) => {
        list.push({
          ...item,
          parentId: i,
          id: item.templateFileId,
          templateFileDId: item.templateFileId,
        });
        if (item.subInfo && item.subInfo.length > 0) {
          treeList(item.subInfo, item.templateFileId);
        }
      });
    } else {
      arr.forEach(item => {
        list.push({
          ...item,
          parentId: path,
          id: item.templateFileDId,
          templateFileCode: item.nodeCode,
          templateFileName: item.nodeName,
        });
        if (item.subList && item.subList.length > 0) {
          treeList(item.subList, item.templateFileDId);
        }
      });
    }
    return list;
  };

  const treeQuery = async () => {
    const { siteId } = auditInformationDs.toData()[0];
    externalDs.setQueryParameter('siteId', siteId);
    externalDs.query();
    // 在回填勾选时先清空原来的勾选
    externalDs.unSelectAll();
    const url1 = `${
      BASIC.TARZAN_SAMPLING
    }/v1/${getCurrentOrganizationId()}/qis-sys-review-platform/outside-file/ui`;
    const res = await axios.get(url1);
    const lists = treeList(res, -1);
    treeInternalDs.loadData(lists);
    // 将查询到的审批数据分别回填进树结构及表格中，前缀是S是树结构
    const { sysReviewStandardIdList } = auditInformationDs.toData()[0];
    sysReviewStandardIdList.forEach(item => {
      if (item.startsWith('S')) {
        const index = lists.findIndex(r => `S${r.templateFileDId}` === item);
        treeInternalDs.select(index);
      } else {
        const index = externalDs.toData().findIndex((r: any) => r.fileId === item);
        externalDs.select(index);
      }
    });
  };

  const onChangeCallback = key => {
    // 有时在刚进去页面第一次时可能右侧弹框的表格中没有数据，在切换时再一次进行回填
    if (key === '2') {
    // 在回填勾选时先清空原来的勾选
      externalDs.unSelectAll();
      const { sysReviewStandardIdList } = auditInformationDs.toData()[0];
      sysReviewStandardIdList.forEach(item => {
        if (!item.startsWith('S')) {
          const index = externalDs.toData().findIndex((r: any) => r.fileId === item);
          externalDs.select(index);
        }
      });
    }
  };

  const viewRenderer = () => {
    treeQuery();
    Modal.open({
      title: intl.get(`${modelPrompt}.reviewStandard`).d('审核标准'),
      contentStyle: { height: '620px', width: '720px' },
      children: (
        <>
          <div style={{ height: '500px' }}>
            <Tabs defaultActiveKey="1" onChange={onChangeCallback}>
              <TabPane tab="外部体系文件" key="1">
                <Form dataSet={treeInternalDs.queryDataSet} columns={2} labelWidth={112}>
                  <TextField name="text" />
                </Form>
                <Table
                  buttons={buttons}
                  mode={TableMode.tree}
                  searchCode="internalDs"
                  customizedCode="internalDs"
                  defaultRowExpanded
                  queryBar={TableQueryBarType.none}
                  queryBarProps={{
                    fuzzyQuery: false,
                  }}
                  dataSet={treeInternalDs}
                  columns={internalColumns}
                />
              </TabPane>
              <TabPane tab="内部体系文件" key="2">
                <Table
                  searchCode="external"
                  customizedCode="external"
                  queryBar={TableQueryBarType.filterBar}
                  queryBarProps={{
                    fuzzyQuery: false,
                  }}
                  dataSet={externalDs}
                  columns={externalColumns}
                />
              </TabPane>
            </Tabs>
          </div>
        </>
      ),
      okText: '确定',
      onOk: () => {
        onHangeReviewStandard();
      },
      autoCenter: true,
    });
  };

  const onHangeReviewStandard = () => {
    const sysReviewStandardIdList: Array<string> = [];
    const sysReviewStandardNameList: Array<string> = [];
    // 将弹框的内容写进lov里面
    treeInternalDs.selected.forEach(item => {
      if (item.get('nodeLevel') !== 0) {
        sysReviewStandardIdList.push(`S${item.get('templateFileDId')}`);
        sysReviewStandardNameList.push(
          `${item.get('templateFileCode')}${' '}${item.get('templateFileName')}`,
        );
      }
    });
    externalDs.selected.forEach(item => {
      sysReviewStandardIdList.push(item.get('fileId'));
      sysReviewStandardNameList.push(`${item.get('fileCode')}${' '}${item.get('fileName')}`);
    });
    auditInformationDs.current.set('sysReviewStandardIdList', sysReviewStandardIdList);
    auditInformationDs.current.set('sysReviewStandardNameList', sysReviewStandardNameList);
  };

  const onHandleChange=(value,oldValue)=>{
    // 点击输入框中点击每次删除时，同时要更新原数据的id的删除
      const { sysReviewStandardIdList } = auditInformationDs.toData()[0];
      let indexName;
      oldValue.some((_, index) => {
        indexName = index;
        return oldValue[index] !== value[index];
      });
      const array = [...sysReviewStandardIdList];
      array.splice(indexName, 1);
      auditInformationDs.current.set('sysReviewStandardIdList', compact(array));
  }

  const buttons: any[] = ['expandAll', 'collapseAll'];

  return (
    <div className={styles.tabsBody}>
      <Spin spinning={fetchAuditInformation.loading}>
        {!readonly && (
          <div className="control-row">
            <div></div>
            <div>
              {!canEdit && !pubFlag && (
                <Button
                  disabled={
                    !['NEW', 'TO_RELEASE', 'REJECTED'].includes(sysReviewPlanStatus) ||
                    (threeResList && user.id !== threeResList[0]?.rows?.createdBy)
                  }
                  color={ButtonColor.primary}
                  onClick={() => {
                    setCanEdit(true);
                  }}
                >
                  {intl.get('hzero.common.button.edit').d('编辑')}
                </Button>
              )}
              {canEdit && !pubFlag && (
                <>
                  <Button onClick={handleCancel}>
                    {intl.get('hzero.common.button.cancel').d('取消')}
                  </Button>
                  <Button
                    disabled={commitOaSubmit.loading || saveAuditInformation.loading}
                    color={ButtonColor.primary}
                    onClick={handleSave}
                  >
                    {intl.get('hzero.common.button.save').d('保存')}
                  </Button>
                </>
              )}
            </div>
          </div>
        )}
        <div>
          <Form dataSet={auditInformationDs} columns={3} labelWidth={112} disabled={!canEdit}>
            <TextField name="sysReviewPlanCode" />
            <Lov name="planCreaterLov" />
            <Lov name="leaderLov" />
            <TextField name="sysReviewTitle" colSpan={2} newLine />
            <Select name="sysReviewType" />
            <Select name="sysReviewPlanStatus" newLine />
            <Lov name="siteLov" onChange={handleSiteChange} />
            <TextArea name="sysReviewAim" colSpan={2} newLine />
            <Attachment name="reviewUuid" newLine />
            <TextArea name="sysReviewScope" colSpan={2} newLine />
            <TextField
              name="sysReviewStandardNameList"
              colSpan={2}
              newLine
              onChange={(value, oldValue) => onHandleChange(value,oldValue)}
              suffix={<Icon type="search" onClick={() => viewRenderer()} />}
            />
            <TextField name="standardMoreInfo" colSpan={2} newLine />
            <TextField name="otherBasis" colSpan={2} newLine />
          </Form>
        </div>
      </Spin>
    </div>
  );
};

export default AuditInformation;
