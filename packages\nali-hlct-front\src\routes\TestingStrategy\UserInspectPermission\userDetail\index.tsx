/**
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-27
 * @description 用户检验权限维护详情
 */
import React, { useMemo, useEffect, useState } from 'react';
import { Popconfirm } from 'choerodon-ui';
import { DataSet, Table, Lov, Button, Form, CheckBox, Tabs, Modal } from 'choerodon-ui/pro';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { Header, Content } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { useDataSetEvent } from 'utils/hooks';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import OrganizationComponent from '../components/OriganizationComponent/OrganizationComponent';
import UserRightsComponentDrawer from '@/components/UserRightsComponentDrawer';
import {
  detailTableDS,
  materialCategoryDS,
  materialDS,
  listDS,
  taskDS,
  queryDS,
} from '../stores/detailTableDS';
import { InitUserInspectPermission, SaveUserInspectPermission } from '../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.userInspectPermission';

export interface DisableObj {
  materialCategoryDisabled: boolean;
  materialDisabled: boolean;
  originationDisabled: boolean;
  taskDisabled: boolean;
  changeFlag: boolean; // 是否更新activeKey
}

const { TabPane } = Tabs;

const UserInspectPermissionDetail = props => {
  const {
    match: { params },
  } = props;

  useEffect(() => {
    if (params.id && params.id !== 'create') {
      setCanEdit(false);
      listDs.selection = false;

      initPage();
    } else {
      setCanEdit(true);
      listDs.selection = DataSetSelection.multiple;
    }
  }, [params]);

  const [canEdit, setCanEdit] = useState(false);
  const [inspectBusinessList, setInspectBusinessList] = useState(0);
  const [selectedList, setSelectedList] = useState([]);
  const [activeKey, setActiveKey] = useState('materialCategory');
  const [permissionFlag, setPermissionFlag] = useState('Y');
  const [disableObj, setDisableObj] = useState<DisableObj>({
    materialCategoryDisabled: false,
    materialDisabled: false,
    originationDisabled: false,
    taskDisabled: false,
    changeFlag: false,
  });
  const queryDs = useMemo(() => new DataSet(queryDS()), []);
  const materialCategoryDs = useMemo(() => new DataSet(materialCategoryDS()), []);
  const materialDs = useMemo(() => new DataSet(materialDS()), []);
  const taskDs = useMemo(() => new DataSet(taskDS()), []);
  const listDs = useMemo(() => new DataSet(listDS()), []);
  const tableDs = useMemo(
    () =>
      new DataSet({
        ...detailTableDS(),
        children: {
          inspectBusinessList: listDs,
        },
      }),
    [],
  );

  // 新建初始化表
  const initUserInspectPermission = useRequest(InitUserInspectPermission(), {
    manual: true,
  });
  const saveUserInspectPermission = useRequest(SaveUserInspectPermission(), {
    manual: true,
  });

  // ds 监听
  useDataSetEvent(listDs, 'update', ({ name, value }) => {
    if (name === 'permissionFlag') {
      setPermissionFlag(value);
    }
  });
  useDataSetEvent(listDs, 'load', ({ dataSet }) => handleIndexChange(dataSet.current, dataSet));
  useDataSetEvent(listDs, 'batchSelect', ({ dataSet }) => {
    setSelectedList(dataSet.selected);
  });
  useDataSetEvent(listDs, 'batchUnselect', ({ dataSet }) => {
    setSelectedList(dataSet.selected);
  });
  useDataSetEvent(listDs, 'indexChange', ({ record, dataSet }) =>
    handleIndexChange(record, dataSet),
  );

  // tab页签之间禁用联动
  const handleUpdateTabDisabled = (record, changeFlag = true) => {
    if (!record?.isCurrent) {
      return;
    }
    const {
      materialCategoryList = [],
      materialList = [],
      organizationList = [],
      taskList = [],
    } = record.toData();
    const _newMaterialCategoryList = materialCategoryList?.filter(
      item => item?.permissionFlag === 'Y',
    );
    const _newMaterialList = materialList?.filter(item => item?.permissionFlag === 'Y');
    const _newTaskList = taskList?.filter(item => item?.permissionFlag === 'Y');
    setDisableObj({
      materialCategoryDisabled: organizationList?.length || _newTaskList?.length,
      materialDisabled: organizationList?.length || _newTaskList?.length,
      originationDisabled:
        _newMaterialCategoryList?.length || _newMaterialList?.length || _newTaskList?.length,
      taskDisabled:
        _newMaterialCategoryList?.length || _newMaterialList?.length || organizationList?.length,
      changeFlag,
    });
  };

  const handleIndexChange = (record, dataSet) => {
    if (dataSet?.selected?.length > 0) {
      const focusRecordData = dataSet.selected.find(
        _record =>
          _record.get('materialList')?.length ||
          _record.get('materialCategoryList')?.length ||
          _record.get('organizationList')?.length ||
          _record.get('taskList')?.length,
      );
      handleUpdateTabDisabled(focusRecordData || record);
      setPermissionFlag(record?.get('permissionFlag'));
      materialDs.loadData([]);
      materialCategoryDs.loadData([]);
      taskDs.loadData([]);
      const tableDsData = tableDs.current?.toData();
      setDsSiteId(tableDsData.siteId);
    } else {
      handleUpdateTabDisabled(record);
      setPermissionFlag(record?.get('permissionFlag'));
      materialDs.loadData(record?.get('materialList') || []);
      materialCategoryDs.loadData(record?.get('materialCategoryList') || []);
      taskDs.loadData(record?.get('taskList') || []);
      const tableDsData = tableDs.current?.toData();
      setDsSiteId(tableDsData.siteId);
    }
  };

  const handleSave = async () => {
    const validate = await tableDs.validate();
    if (!validate) {
      return;
    }
    const param: any = { ...tableDs.toData()[0], inspectBusinessList: listDs.toData() };

    saveUserInspectPermission.run({
      params: param,
      onSuccess: res => {
        notification.success({});
        if (`${params.id}` === `${param.userId}` && `${params.siteId}` === `${param.siteId}`) {
          initPage();
        } else {
          replacePage(res);
        }
      },
    });
  };

  const handleEdit = () => {
    setCanEdit(true);
    listDs.selection = DataSetSelection.multiple;
    listDs.forEach(record => {
      record.setState('editing', true);
    });
  };

  const replacePage = res => {
    props.history.push(`/hwms/user-inspect-permission/detail/${res.userId}/${res.siteId}`);
  };

  const setDsSiteId = _siteId => {
    queryDs.forEach(record => {
      record.set('siteId', _siteId);
    });
    materialCategoryDs.forEach(record => {
      record.set('siteId', _siteId);
    });
    materialDs.forEach(record => {
      record.set('siteId', _siteId);
    });
    taskDs.forEach(record => {
      record.set('siteId', _siteId);
    });
  };

  const initPage = () => {
    setSelectedList([]);
    setCanEdit(false);
    listDs.selection = false;

    tableDs.setQueryParameter('userId', params.id);
    tableDs.setQueryParameter('siteId', params.siteId);

    setDsSiteId(params.siteId);

    tableDs.query().then(res => {
      if (res?.success) {
        if (!res.rows?.inspectBusinessList) {
          initUserInspectPermission.run({
            params: {
              siteId: params.siteId,
            },
            onSuccess: res => {
              listDs.loadData(res);
              setInspectBusinessList(res.length);
              listDs.forEach(record => {
                record.setState('editing', false);
              });
              listDs.locate(0);
            },
          });
        } else {
          const { inspectBusinessList } = res.rows || {};
          listDs.loadData(inspectBusinessList || []);
          setInspectBusinessList(inspectBusinessList.length);
          listDs.forEach(record => {
            record.setState('editing', false);
          });

          listDs?.locate(0);
          materialDs.loadData((inspectBusinessList[0] || {}).materialList || []);
          materialCategoryDs.loadData((inspectBusinessList[0] || {}).materialCategoryList || []);
          taskDs.loadData((inspectBusinessList[0] || {}).taskList || []);
          setDsSiteId(params.siteId);
        }
      }
    });
  };

  const handleCancel = () => {
    if (params.id === 'create') {
      props.history.push('/hwms/user-inspect-permission/list');
    } else {
      initPage();
    }
  };

  const handleChangeName = () => {
    tableDs.current?.init('siteLov', null);
    listDs.loadData([]);
    materialDs.loadData([]);
    materialCategoryDs.loadData([]);
    taskDs.loadData([]);
    const tableDsData = tableDs.current?.toData();
    setDsSiteId(tableDsData.siteId);
  };

  // 改变站点时重新改变List检验业务 并重新渲染树
  const handleChangeSite = value => {
    if (!value) {
      setDsSiteId(null);
      return;
    }
    const tableDsData = tableDs.current?.toData();
    tableDs.setQueryParameter('userId', tableDsData.userId);
    tableDs.setQueryParameter('siteId', tableDsData.siteId);
    tableDs.query().then(res => {
      if (res?.success) {
        if (!res.rows?.inspectBusinessList) {
          initUserInspectPermission.run({
            params: {
              siteId: tableDsData.siteId,
            },
            onSuccess: res => {
              listDs.loadData(res);
              setInspectBusinessList(res.length);
              listDs.forEach(record => {
                record.setState('editing', true);
              });
              listDs.locate(0);
            },
          });
        } else {
          const { inspectBusinessList } = res.rows || {};
          listDs.loadData(inspectBusinessList || []);
          setInspectBusinessList(inspectBusinessList.length);
          listDs.forEach(record => {
            record.setState('editing', true);
          });
          listDs?.locate(0);

          materialDs.loadData((inspectBusinessList[0] || {}).materialList || []);
          materialCategoryDs.loadData((inspectBusinessList[0] || {}).materialCategoryList || []);
          taskDs.loadData((inspectBusinessList[0] || {}).taskList || []);
          setDsSiteId(tableDsData.siteId);
        }
      }
    });
  };

  const headerRowClick = record => {
    if (listDs.selected.length > 0) {
      return;
    }
    const tempMatsData = record?.get('materialCategoryList');
    const tempMatData = record?.get('materialList');
    const tempTaskData = record?.get('taskList');
    materialCategoryDs.loadData(tempMatsData || []);
    materialDs.loadData(tempMatData || []);
    taskDs.loadData(tempTaskData || []);
    const tableDsData = tableDs.current?.toData();
    setDsSiteId(tableDsData.siteId);
  };

  const addMaterialCategory = value => {
    if (!value?.length) {
      return;
    }
    value.forEach(item => {
      const tempObj = {
        materialCategoryId: item.materialCategoryId,
        materialCategoryCode: item.categoryCode,
        materialCategoryDesc: item.description,
        permissionFlag: 'Y',
        taskCategory: '',
        taskCategoryDesc: '',
      };

      const matscreate = () => {
        const tempData = materialCategoryDs.toData();
        const index = (tempData || []).findIndex(
          (item: any) => item.materialCategoryId === tempObj.materialCategoryId,
        );
        if (index < 0) {
          materialCategoryDs.create(tempObj);
        }
      };
      matscreate();
      if (listDs.selected?.length > 0) {
        listDs.selected.forEach(record => {
          const originList = record?.get('materialCategoryList') || [];
          const index = originList.findIndex(
            item => item?.materialCategoryId === tempObj.materialCategoryId,
          );
          if (index < 0) {
            originList.push(tempObj);
          }
          record.set('materialCategoryList', originList);
        });
      } else {
        const originList = listDs.current?.get('materialCategoryList') || [];
        const index = originList.findIndex(
          (item: any) => item.materialCategoryId === tempObj.materialCategoryId,
        );
        if (index < 0) {
          originList.push(tempObj);
        }
        listDs.current?.set('materialCategoryList', originList);
      }
    });
    handleUpdateTabDisabled(listDs.current, false);
    queryDs.current?.init('addMatsLov', undefined);
  };

  const addMaterial = value => {
    if (!value?.length) {
      return;
    }
    value.forEach(item => {
      const tempObj = {
        materialName: item.materialName,
        materialId: item.materialId,
        materialCode: item.materialCode,
        materialCategoryId: item.materialCategoryId,
        materialCategoryCode: item.categoryCode,
        materialCategoryDesc: item.description,
        permissionFlag: 'Y',
        revisionCode: item.revisionFlag,
        taskCategory: '',
        taskCategoryDesc: '',
      };

      const materialcreate = () => {
        const tempData = materialDs.toData();
        const index = (tempData || []).findIndex(
          (item: any) => item.materialId === tempObj.materialId,
        );
        if (index < 0) {
          materialDs.create(tempObj);
        }
      };
      materialcreate();
      if (listDs.selected?.length > 0) {
        listDs.selected.forEach(record => {
          const originList = record?.get('materialList') || [];
          const index = originList.findIndex(i => i?.materialId === tempObj.materialId);
          if (index < 0) {
            originList.push(tempObj);
          }
          record.set('materialList', originList);
        });
      } else {
        const originList = listDs.current?.get('materialList') || [];
        const index = originList.findIndex((item: any) => item.materialId === tempObj.materialId);
        if (index < 0) {
          originList.push(tempObj);
        }
        listDs.current?.set('materialList', originList);
      }
    });
    handleUpdateTabDisabled(listDs.current, false);
    queryDs.current?.init('addMatLov', undefined);
  };

  const addTask = value => {
    if (!value?.length) {
      return;
    }
    value.forEach(item => {
      const tempObj = {
        taskCategory: item.taskCategory,
        permissionFlag: 'Y',
      };
      const taskcreate = () => {
        const tempData = taskDs.toData();
        const index = (tempData || []).findIndex(
          (item: any) => item.taskCategory === tempObj.taskCategory,
        );
        if (index < 0) {
          taskDs.create(tempObj);
        }
      };
      taskcreate();
      if (listDs.selected?.length > 0) {
        listDs.selected.forEach(record => {
          const originList = record?.get('taskList') || [];
          const index = originList.findIndex(item => item?.taskCategory === tempObj.taskCategory);
          if (index < 0) {
            originList.push(tempObj);
          }
          record.set('taskList', originList);
        });
      } else {
        const originList = listDs.current?.get('taskList') || [];
        const index = originList.findIndex(
          (item: any) => item.taskCategory === tempObj.taskCategory,
        );
        if (index < 0) {
          originList.push(tempObj);
        }
        listDs.current?.set('taskList', originList);
      }
    });
    handleUpdateTabDisabled(listDs.current, false);
    queryDs.current?.init('addTaskLov', undefined);
    return undefined;
  };
  // 同步数据 将当前materialCategoryDs数据同步至listDs
  const syncmaterialCategoryDsData = () => {
    if (listDs.current) {
      const tempData = materialCategoryDs.toData();
      listDs.current.set('materialCategoryList', tempData);
      handleUpdateTabDisabled(listDs.current, false);
    }
  };

  // 同步数据 将当前materialDs数据同步至listDs
  const syncmaterialDsData = () => {
    if (listDs.current) {
      const tempData = materialDs.toData();
      listDs.current.set('materialList', tempData);
      handleUpdateTabDisabled(listDs.current, false);
    }
  };

  // 同步数据 将当前taskDs数据同步至listDs
  const syncTaskDsData = () => {
    if (listDs.current) {
      const tempData = taskDs.toData();
      listDs.current.set('taskList', tempData);
      handleUpdateTabDisabled(listDs.current, false);
    }
  };

  const selectAll = type => {
    if (type === 'mats') {
      materialCategoryDs.forEach(item => {
        item.set('permissionFlag', 'Y');
      });
      syncmaterialCategoryDsData();
    } else if (type === 'mat') {
      materialDs.forEach(item => {
        item.set('permissionFlag', 'Y');
      });
      syncmaterialDsData();
    } else if (type === 'list') {
      setPermissionFlag('Y');
      listDs.forEach(item => {
        item.set('permissionFlag', 'Y');
        item.set('createFlag', 'Y');
        item.set('editFlag', 'Y');
        item.set('executeFlag', 'Y');
        item.set('inspectPermissionFlag', 'Y');
        item.set('reinspectPermissionFlag', 'Y');
      });
    } else if (type === 'task') {
      taskDs.forEach(item => {
        item.set('permissionFlag', 'Y');
      });
      syncTaskDsData();
    }
  };

  const cancelSelectAll = type => {
    if (type === 'mats') {
      materialCategoryDs.forEach(item => {
        item.set('permissionFlag', 'N');
      });
      syncmaterialCategoryDsData();
    } else if (type === 'mat') {
      materialDs.forEach(item => {
        item.set('permissionFlag', 'N');
      });
      syncmaterialDsData();
    } else if (type === 'list') {
      setPermissionFlag('N');
      listDs.forEach(item => {
        item.set('permissionFlag', 'N');
        item.set('createFlag', 'N');
        item.set('editFlag', 'N');
        item.set('executeFlag', 'N');
        item.set('inspectPermissionFlag', 'N');
        item.set('reinspectPermissionFlag', 'N');
      });
    } else if (type === 'task') {
      taskDs.forEach(item => {
        item.set('permissionFlag', 'N');
      });
      syncTaskDsData();
    }
  };

  const listDsSelectAll = () => {
    let flag = Boolean(listDs?.length);
    const data: any = listDs.toData();
    for (let i = 0; i < data.length; i++) {
      if (
        data[i].permissionFlag === 'N' ||
        data[i].createFlag === 'N' ||
        data[i].editFlag === 'N' ||
        data[i].executeFlag === 'N' ||
        data[i].inspectPermissionFlag === 'N' ||
        data[i].reinspectPermissionFlag === 'N'
      ) {
        flag = false;
        break;
      }
    }
    return flag;
  };

  const materialCategoryDsSelectAll = () => {
    let flag = Boolean(listDs?.length);
    const data: any = materialCategoryDs.toData();
    for (let i = 0; i < data.length; i++) {
      if (data[i].permissionFlag === 'N') {
        flag = false;
        break;
      }
    }
    return flag;
  };

  const materialDsSelectAll = () => {
    let flag = Boolean(listDs?.length);
    const data: any = materialDs.toData();
    for (let i = 0; i < data.length; i++) {
      if (data[i].permissionFlag === 'N') {
        flag = false;
        break;
      }
    }
    return flag;
  };

  const taskDsSelectAll = () => {
    let flag = Boolean(listDs?.length);
    const data: any = taskDs.toData();
    for (let i = 0; i < data.length; i++) {
      if (data[i].permissionFlag === 'N') {
        flag = false;
        break;
      }
    }
    return flag;
  };

  const columns: ColumnProps[] = [
    {
      name: 'inspectBusinessTypeDesc',
      lock: ColumnLock.left,
    },
    {
      header: () => (
        <span>
          <span>{intl.get(`${modelPrompt}.permissionFlag`).d('是否有权限')}</span>
          <span>(</span>
          {!listDsSelectAll() ? (
            <a onClick={() => selectAll('list')} disabled={!canEdit || selectedList.length > 0}>
              {intl.get(`${modelPrompt}.button.selectAll`).d('全选')}
            </a>
          ) : (
            <Popconfirm
              title={intl
                .get(`${modelPrompt}.confirm.deletePermissionFlag`, {
                  name: tableDs.current?.get('userName'),
                  siteCode: tableDs.current?.get('siteCode'),
                })
                .d(
                  `点击取消全选，将删除员工${tableDs.current?.get(
                    'userName',
                  )}在站点${tableDs.current?.get('siteCode')}下的所有检验权限，请慎重选择!`,
                )}
              onConfirm={() => cancelSelectAll('list')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              okText={intl.get('tarzan.common.button.confirm').d('确定')}
            >
              <a disabled={!canEdit}>
                {intl.get(`${modelPrompt}.button.cancelSelectAll`).d('取消全选')}
              </a>
            </Popconfirm>
          )}
          <span>)</span>
        </span>
      ),
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <CheckBox
          name="permissionFlag"
          record={record || undefined}
          disabled={!canEdit || selectedList.length > 0}
        />
      ),
    },
    {
      header: <span>{intl.get(`${modelPrompt}.operationAuthority`).d('操作权限')}</span>,
      align: ColumnAlign.center,
      name: 'createFlag',
      width: 500,
      renderer: ({ record }) =>
        record && (
          <>
            <CheckBox
              name="createFlag"
              record={record}
              value="Y"
              unCheckedValue="N"
              disabled={!canEdit || record.get('permissionFlag') === 'N' || selectedList.length > 0}
            >
              {intl.get(`${modelPrompt}.checkbox.create`).d('创建')}
            </CheckBox>
            <CheckBox
              name="editFlag"
              record={record}
              value="Y"
              unCheckedValue="N"
              disabled={!canEdit || record.get('permissionFlag') === 'N' || selectedList.length > 0}
            >
              {intl.get(`${modelPrompt}.checkbox.edit`).d('编辑检验方案')}
            </CheckBox>
            <CheckBox
              name="executeFlag"
              record={record}
              value="Y"
              unCheckedValue="N"
              disabled={!canEdit || record.get('permissionFlag') === 'N' || selectedList.length > 0}
            >
              {intl.get(`${modelPrompt}.checkbox.execute`).d('执行')}
            </CheckBox>
          </>
        ),
    },
    {
      header: <span>{intl.get(`${modelPrompt}.inspectionAuthority`).d('检验权限')}</span>,
      align: ColumnAlign.center,
      renderer: ({ record }) =>
        record && (
          <>
            <CheckBox
              name="inspectPermissionFlag"
              record={record}
              value="Y"
              unCheckedValue="N"
              disabled={!canEdit || record.get('permissionFlag') === 'N' || selectedList.length > 0}
            >
              {intl.get(`${modelPrompt}.checkbox.inspectPermission`).d('初检')}
            </CheckBox>
            <CheckBox
              name="reinspectPermissionFlag"
              record={record}
              value="Y"
              unCheckedValue="N"
              disabled={!canEdit || record.get('permissionFlag') === 'N' || selectedList.length > 0}
            >
              {intl.get(`${modelPrompt}.checkbox.reinspectPermission`).d('复检')}
            </CheckBox>
          </>
        ),
    },
  ];

  const matsColumns: ColumnProps[] = [
    { name: 'materialCategoryCode' },
    { name: 'materialCategoryDesc' },
    {
      align: ColumnAlign.center,
      header: () => (
        <span>
          <span>{intl.get(`${modelPrompt}.permissionFlag`).d('是否有权限')}</span>
          <span>(</span>
          {!materialCategoryDsSelectAll() ? (
            <a onClick={() => selectAll('mats')} disabled={!canEdit || permissionFlag === 'N'}>
              {intl.get(`${modelPrompt}.button.selectAll`).d('全选')}
            </a>
          ) : (
            <a
              onClick={() => cancelSelectAll('mats')}
              disabled={!canEdit || permissionFlag === 'N'}
            >
              {intl.get(`${modelPrompt}.button.cancelSelectAll`).d('取消全选')}
            </a>
          )}
          <span>)</span>
        </span>
      ),
      renderer: ({ record }) => (
        <CheckBox
          name="permissionFlag"
          record={record || undefined}
          onChange={syncmaterialCategoryDsData}
          disabled={!canEdit}
        />
      ),
    },
    // {
    //   name: 'taskCategoryLov',
    //   editor: canEdit && <Lov autoSelectSingle={false} onChange={syncmaterialCategoryDsData}></Lov>,
    // },
  ];

  const matColumns: ColumnProps[] = [
    { name: 'materialCode' },
    { name: 'materialName' },
    { name: 'materialCategoryCode' },
    { name: 'materialCategoryDesc' },
    {
      align: ColumnAlign.center,
      name: 'permissionFlag',
      header: () => (
        <span>
          <span>{intl.get(`${modelPrompt}.permissionFlag`).d('是否有权限')}</span>
          <span>(</span>
          {!materialDsSelectAll() ? (
            <a onClick={() => selectAll('mat')} disabled={!canEdit || permissionFlag === 'N'}>
              {intl.get(`${modelPrompt}.button.selectAll`).d('全选')}
            </a>
          ) : (
            <a onClick={() => cancelSelectAll('mat')} disabled={!canEdit || permissionFlag === 'N'}>
              {intl.get(`${modelPrompt}.button.cancelSelectAll`).d('取消全选')}
            </a>
          )}
          <span>)</span>
        </span>
      ),
      renderer: ({ record }) => (
        <CheckBox
          name="permissionFlag"
          record={record || undefined}
          onChange={syncmaterialDsData}
          disabled={!canEdit}
        />
      ),
    },
  ];

  const taskColumns: ColumnProps[] = [
    { name: 'taskCategory' },
    {
      align: ColumnAlign.center,
      name: 'permissionFlag',
      header: () => (
        <span>
          <span>{intl.get(`${modelPrompt}.permissionFlag`).d('是否有权限')}</span>
          <span>(</span>
          {!taskDsSelectAll() ? (
            <a onClick={() => selectAll('task')} disabled={!canEdit || permissionFlag === 'N'}>
              {intl.get(`${modelPrompt}.button.selectAll`).d('全选')}
            </a>
          ) : (
            <a
              onClick={() => cancelSelectAll('task')}
              disabled={!canEdit || permissionFlag === 'N'}
            >
              {intl.get(`${modelPrompt}.button.cancelSelectAll`).d('取消全选')}
            </a>
          )}
          <span>)</span>
        </span>
      ),
      renderer: ({ record }) => (
        <CheckBox
          name="permissionFlag"
          record={record || undefined}
          onChange={syncTaskDsData}
          disabled={!canEdit}
        />
      ),
    },
  ];

  useEffect(() => {
    const getActiveKey = () => {
      if (!disableObj.changeFlag) {
        return activeKey;
      }
      const materialList = listDs.current?.get('materialList');
      const {
        materialCategoryDisabled,
        materialDisabled,
        originationDisabled,
        taskDisabled,
      } = disableObj;
      if (!materialDisabled && materialList?.length) {
        return 'material';
      }
      if (!materialCategoryDisabled) {
        return 'materialCategory';
      }
      if (!originationDisabled) {
        return 'origination';
      }
      if (!taskDisabled) {
        return 'task';
      }
      return 'materialCategory';
    };

    setActiveKey(getActiveKey());
  }, [disableObj]);

  const addUserSitePermission = (_modal, tableDs) => {
    const { userLov } = tableDs.toData()[0];
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.addUserSitePermission`).d('新增用户站点权限'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      cancelText: intl.get(`tarzan.common.button.close`).d('关闭'),
      onClose: () => {
        _modal.props.children.props.dataSet.query();
      },
      children: <UserRightsComponentDrawer userInfo={userLov} />,
    });
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.detail.title`).d('用户检验权限维护')}
        backPath="/hwms/user-inspect-permission/list"
      >
        {canEdit ? (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              permissionList={[
                {
                  code: `dist.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            permissionList={[
              {
                code: `dist.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            onClick={handleEdit}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Form dataSet={tableDs} columns={3}>
          <Lov name="userLov" disabled={!canEdit} onChange={handleChangeName} noCache />
          <Lov
            name="siteLov"
            onChange={handleChangeSite}
            disabled={!canEdit}
            noCache
            autoSelectSingle={false}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return [
                  <Button
                    onClick={() => {
                      addUserSitePermission(modal, tableDs);
                    }}
                  >
                    {intl.get(`${modelPrompt}.addUserSitePermission`).d('新增用户站点权限')}
                  </Button>,
                  cancelBtn,
                  okBtn,
                ];
              },
            }}
            tableProps={{
              queryFieldsLimit: 10,
            }}
          />
        </Form>
        <Table
          dataSet={listDs}
          columns={columns}
          highLightRow
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
          className={styles.table}
          style={{
            height: inspectBusinessList > 10 ? 400 : 'auto',
          }}
          // autoHeight={inspectBusinessList > 10 ? { type: 'maxHeight', diff: 1 } : false}
        />
        <div>
          <Tabs activeKey={activeKey} onChange={val => setActiveKey(val)}>
            <TabPane
              tab={intl.get(`${modelPrompt}.tab.materialCategory`).d('物料类别权限')}
              key="materialCategory"
              disabled={disableObj.materialCategoryDisabled}
            >
              <Form columns={3} dataSet={queryDs}>
                <Lov
                  disabled={!canEdit || permissionFlag === 'N'}
                  name="addMatsLov"
                  onChange={addMaterialCategory}
                  autoSelectSingle={false}
                  noCache
                />
              </Form>
              <Table dataSet={materialCategoryDs} columns={matsColumns} />
            </TabPane>
            <TabPane
              tab={intl.get(`${modelPrompt}.tab.material`).d('物料权限')}
              key="material"
              disabled={disableObj.materialDisabled}
            >
              <Form columns={3} dataSet={queryDs}>
                <Lov
                  disabled={!canEdit || permissionFlag === 'N'}
                  name="addMatLov"
                  onChange={addMaterial}
                  autoSelectSingle={false}
                  noCache
                />
              </Form>
              <Table dataSet={materialDs} columns={matColumns} />
            </TabPane>
            <TabPane
              tab={intl.get(`${modelPrompt}.tab.origination`).d('组织权限')}
              key="origination"
              disabled={disableObj.originationDisabled}
            >
              <OrganizationComponent
                tableDs={tableDs}
                listDs={listDs}
                canEdit={canEdit && permissionFlag === 'Y'}
                handleUpdateTabDisabled={handleUpdateTabDisabled}
              />
            </TabPane>
            <TabPane
              tab={intl.get(`${modelPrompt}.tab.task`).d('任务类别')}
              key="task"
              disabled={disableObj.taskDisabled}
            >
              <Form columns={3} dataSet={queryDs}>
                <Lov
                  disabled={!canEdit || permissionFlag === 'N'}
                  name="addTaskLov"
                  onChange={addTask}
                  autoSelectSingle={false}
                  noCache
                />
              </Form>
              <Table dataSet={taskDs} columns={taskColumns} />
            </TabPane>
          </Tabs>
        </div>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(UserInspectPermissionDetail as any);
