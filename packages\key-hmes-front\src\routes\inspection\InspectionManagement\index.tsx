/**
 * 报检请求管理平台-入口文件
 * @date 2023-5-15
 * <AUTHOR> <<EMAIL>>
 */
import React, {useEffect, useState} from 'react';
import {Button, DataSet, Modal, Table, Tabs, Icon} from 'choerodon-ui/pro';
import {Badge, Tag} from 'choerodon-ui';
import intl from 'utils/intl';
import myInstance from '@utils/myAxios';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import {ColumnAlign, ColumnLock, TableQueryBarType} from "choerodon-ui/pro/lib/table/enum";
import { BASIC } from '@/utils/config';
import notification from "utils/notification";
import {getCurrentOrganizationId} from "utils/utils";
import request from 'utils/request';
import { headerTableDS, headerTableTwoDS, searchFormDS, detailDS } from './stores/ListDS';
import styles from './index.module.less';
import CreateMaterial from "./CreateMaterialDrawer";

const modelPrompt = 'tarzan.hmes.inspection.inspection-management';
const tenantId = getCurrentOrganizationId();

let modalMaterial;

const statusList = {
  NEW: 'blue',
  CANCE: 'red',
  COMPLETED: 'green',
  FIRST_COMPLETED: 'yellow',
  INSPECTING: 'geekblue',
  REINSPECTING: 'orange',
  LAST_COMPLETED: 'gray',
};

const Order = props => {
  const {
    headerTableDs,
    headerTableTwoDs,
    searchFormDs,
    detailDs,
    match: { path },
  } = props;
  const [inspectRequestIds, setInspectRequestIds]= useState<any[]>([]);

  const [currentTab, setCurrentTab] = useState<any>('1');
  const [selectDatas, setSelectDatas] = useState<any>([]);

  // 头列表配置
  const tableColumns = [
    { name: 'inspectRequestCode', width: 150, lock: ColumnLock.left },
    { name: 'siteCode', width: 220 },
    { name: 'businessTypeDesc', width: 120 },
    { name: 'inspectBusinessTypeDesc', width: 120 },
    {
      name: 'inspectRequestStatusDesc',
      width: 120,
      renderer: ({ record }) => {
        if (record!.get('inspectRequestStatus')) {
          return <Tag color={statusList[record!.get('inspectRequestStatus')]}>{record!.get('inspectRequestStatusDesc')}</Tag>;
        }
        return <Tag color="pink">{record!.get('inspectRequestStatusDesc')}</Tag>;
      },
    },
    {
      name: 'urgentFlag',
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <Badge
          status={record?.get('urgentFlag') === 'Y' ? 'success' : 'error'}
          text={
            record?.get('urgentFlag') === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    { name: 'quantity', align: ColumnAlign.right, width: 90 },
    { name: 'uomName', width: 120 },
    { name: 'materialCode', width: 120 },
    { name: 'materialName', width: 180 },
    { name: 'revisionCode', width: 120 },
    { name: 'sourceObjectTypeDesc', width: 130 },
    { name: 'sourceNumber', width: 200, align: ColumnAlign.left,
      renderer: ({ record }) => (
        <>
          {record.get('sourceObjectCode')}{record.get('sourceObjectLineCode') && '#'}{record.get('sourceObjectLineCode')}
        </>
      ),
    },
    {
      name: 'inspectDocNum',
      width: 200,
      renderer: ({ record }) => (
        <a onClick={() => handleToInspectDetail(record)}>{record!.get('inspectDocNum')}</a>
      ),
    },
    { name: 'locatorCode', width: 150 },
    { name: 'locatorName', width: 150 },
    { name: 'lot', width: 150 },
    { name: 'supplierLot', width: 150 },
    { name: 'inspectReqUserName', width: 90 },
    { name: 'inspectReqCreationDate', width: 150, align: ColumnAlign.center},
    { name: 'okQty', width: 80, align: ColumnAlign.right },
    { name: 'ngQty', width: 90, align: ColumnAlign.right },
    { name: 'scrapQty', width: 80, align: ColumnAlign.right },
    { name: 'inspectorName', width: 150 },
    { name: 'inspectReqCompleteUserName', width: 150 },
    { name: 'inspectReqCompleteDate', width: 150, align: ColumnAlign.center },
    { name: 'inspectReqCancelUserName', width: 150 },
    { name: 'inspectReqCancelDate', width: 150, align: ColumnAlign.center },
    {
      title: intl.get(`tarzan.common.label.action`).d('操作'),
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <PermissionButton
          type="text"
          permissionList={[
            {
              code: `list.table.inspectDetail`,
              type: 'button',
              meaning: '列表页-报检明细按钮',
            },
          ]}
          onClick={() => handleOpenDetailModal(record)}
        >
          {intl.get(`${modelPrompt}.detail`).d('报检明细')}
        </PermissionButton>
      ),
    },
  ];

  // 处理失败消息columns
  const tableTwoColumns = [
    {
      name: 'inspectRequestCode',
      width: 150,
      // lock: ColumnLock.left,
    },
    { name: 'dealStatusDesc', width: 100,
      renderer: ({ record, value }) => {
        if (record!.get('dealStatus') === 'CONSUME_FAILURE') {
          return <Tag color="red">{value}</Tag>;
        }
        if (record!.get('dealStatus') === 'CONSUME_PROCESS') {
          return <Tag color="orange">{value}</Tag>;
        }
        if (record!.get('dealStatus') === 'CONSUME_SUCCESS') {
          return <Tag color="green">{value}</Tag>;
        }
      },
    },
    { name: 'dealMessage', width: 420 },
    { name: 'siteCode', width: 100 },
    { name: 'businessTypeDesc', width: 120 },
    { name: 'inspectBusinessTypeDesc', width: 120 },
    {
      name: 'urgentFlag',
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <Badge
          status={record?.get('urgentFlag') === 'Y' ? 'success' : 'error'}
          text={
            record?.get('urgentFlag') === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    { name: 'quantity', align: ColumnAlign.right, width: 90 },
    { name: 'uomName', width: 120 },
    { name: 'materialCode', width: 120 },
    { name: 'materialName', width: 180 },
    { name: 'revisionCode', width: 120 },
    { name: 'sourceObjectTypeDesc', width: 130 },
    { name: 'sourceNumber', width: 200, align: ColumnAlign.left,
      // renderer: ({ record }) => (
      //   <>
      //     {record.get('sourceObjectCode')}{record.get('sourceObjectLineCode') && '#'}{record.get('sourceObjectLineCode')}
      //   </>
      // ),
    },
    {
      name: 'inspectDocNum',
      width: 200,
      // renderer: ({ record }) => (
      //   <a onClick={() => handleToInspectDetail(record)}>{record!.get('inspectDocNum')}</a>
      // ),
    },
    { name: 'locatorCode', width: 150 },
    { name: 'locatorName', width: 150 },
    { name: 'lot', width: 150 },
    { name: 'supplierLot', width: 150 },
    { name: 'inspectReqUserName', width: 90 },
    { name: 'inspectReqCreationDate', width: 150, align: ColumnAlign.center},
    { name: 'okQty', width: 80, align: ColumnAlign.right },
    { name: 'ngQty', width: 90, align: ColumnAlign.right },
    { name: 'scrapQty', width: 80, align: ColumnAlign.right },
    // { name: 'inspectorName', width: 150 },
    // { name: 'inspectReqCompleteUserName', width: 150 },
    // { name: 'inspectReqCompleteDate', width: 150, align: ColumnAlign.center },
    // { name: 'inspectReqCancelUserName', width: 150 },
    // { name: 'inspectReqCancelDate', width: 150, align: ColumnAlign.center },
    {
      title: intl.get(`tarzan.common.label.action`).d('操作'),
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <PermissionButton
          type="text"
          permissionList={[
            {
              code: `list.table.inspectDetail`,
              type: 'button',
              meaning: '列表页-处置明细按钮',
            },
          ]}
          onClick={() => handleOpenDetailTwoModal(record)}
        >
          {intl.get(`${modelPrompt}.dispose.detail`).d('处置明细')}
        </PermissionButton>
      ),
    },
  ];

  // 处置明细columns
  const tableDetailColumns = [
    {
      name: 'objectTypeDesc',
      width: 120,
    },
    {
      name: 'objectCode',
      width: 140,
    },
    {
      name: 'quantity',
      width: 60,
      align: ColumnAlign.right,
    },
    {
      name: 'uomCode',
      width: 70,
    },
    {
      name: 'qualityStatusDesc',
      width: 90,
    },
    {
      name: 'disposalFunctionDesc',
      width: 90,
    },
    {
      name: 'locatorCode',
      width: 120,
    },
    {
      name: 'locatorName',
      width: 140,
    },
    {
      name: 'lot',
      width: 120,
    },
    {
      name: 'supplierLot',
      width: 120,
    },
    // {
    //   name: 'quantity',
    //   width: 120,
    // },
    // {
    //   name: 'uomName',
    //   width: 120,
    // },
    {
      name: 'disposalUserName',
      width:120,
    },
    {
      name: 'disposalTime',
      width: 150,
      align: ColumnAlign.center,
    },
    {
      name: 'disposalApartment',
      width: 120,
    },
    {
      name: 'degradeLevel',
      width: 120,
    },
    {
      name: 'degradeMaterialName',
      width: 120,
    },
    {
      name: 'degradeRevisionCode',
      width: 120,
    },
    {
      name: 'disposalExecuteUserName',
      width: 120,
    },
    {
      name: 'disposalExecuteTime',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'disposalExecuteDocNum',
      width: 120,
      align: ColumnAlign.left,
    },
    {
      name: 'finalExecuteObjectCode',
      width: 150,
    },
    {
      name: 'remark',
      width: 120,
    },
  ];

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;

      const handlerSearch = flag ? searchFormDs.addEventListener : searchFormDs.removeEventListener;

      handlerSearch.call(searchFormDs, 'query', handleSearchFormQuery);
      // 查询条件更新时操作
      handler.call(headerTableDs, 'load', handleDataSetSelectUpdate);
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'select', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'unSelect', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'selectAll', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'unSelectAll', handleDataSetSelectUpdate);

      handler.call(headerTableTwoDs, 'select', handleDataSetSelectTwoUpdate);
      handler.call(headerTableTwoDs, 'unSelect', handleDataSetSelectTwoUpdate);
      handler.call(headerTableTwoDs, 'selectAll', handleDataSetSelectTwoUpdate);
      handler.call(headerTableTwoDs, 'unSelectAll', handleDataSetSelectTwoUpdate);
    }
  };

  // 处理选中条执行作业状态
  const handleDataSetSelectTwoUpdate = () => {
    setSelectDatas(headerTableTwoDs.selected);
  };
  const handleSearchFormQuery = async () => {
    if(currentTab === '1'){
      headerTableDs.queryDataSet = searchFormDs.queryDataSet;
      await headerTableDs.query();
    }
    if(currentTab === '2'){
      if(searchFormDs.queryDataSet.current.data.inspectRequestStatus || searchFormDs.queryDataSet.current.data.inspectReqCreationDateFrom || searchFormDs.queryDataSet.current.data.inspectReqCreationDateEnd){
        headerTableTwoDs.loadData([]);
      } else {
        headerTableTwoDs.queryDataSet = searchFormDs.queryDataSet;
        await headerTableTwoDs.query();
      }
    }
    setSelectDatas([])
  }

  const handleDataSetSelectUpdate = () => {
    const _inspectRequestIdList: string[] = [];
    headerTableDs.selected.forEach(item => {
      const { inspectRequestId } = item.toData();
      _inspectRequestIdList.push(inspectRequestId);
    });
    setInspectRequestIds(_inspectRequestIdList)
  };

  const handleOpenDetailModal = (record) => {
    modalMaterial = Modal.open({
      title: intl.get(`${modelPrompt}.detail`).d('报检明细'),
      className: 'hmes-style-modal',
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      onCancel: () => {},
      style: {
        width: 1080,
      },
      children: (
        <CreateMaterial
          inspectRequestId={record.data.inspectRequestId}
        />
      ),
      footer: (
        <Button onClick={() => modalMaterial.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  }

  const handleOpenDetailTwoModal = async (record) => {
    detailDs.setQueryParameter('inspectRequestWaitId', record.get("inspectRequestWaitId"));
    await detailDs.query();
    modalMaterial = Modal.open({
      title: intl.get(`${modelPrompt}.dispose.detail`).d('处置明细'),
      className: 'hmes-style-modal',
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      onCancel: () => {},
      style: {
        width: 1080,
      },
      children: (
        <Table dataSet={detailDs} columns={tableDetailColumns} />
      ),
      footer: (
        <Button onClick={() => modalMaterial.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  }

  const handleToInspectDetail = record => {
    props.history.push(`/hwms/inspect-doc-maintain/dist/${record.get('inspectDocId')}`);
  };

  const disposal = () => {
    const url = `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inspect-requests/disposal/execute/ui`;
    myInstance
      .post(url, {
        inspectRequestIdList: inspectRequestIds,
      })
      .then(res => {
        if (res.data.success) {
          notification.success({});
          headerTableDs.query();
        } else if (res.data.message) {
          notification.error({
            description: res.data.message,
          });
        }
      });
  };

  const onTabClick = async (key) => {
    setCurrentTab(key)
    if(key === '1'){
      headerTableDs.queryDataSet = searchFormDs.queryDataSet;
      await headerTableDs.query();
    }
    if(key === '2'){
      if(searchFormDs.queryDataSet.current.data.inspectRequestStatus || searchFormDs.queryDataSet.current.data.inspectReqCreationDateFrom || searchFormDs.queryDataSet.current.data.inspectReqCreationDateEnd){
        headerTableTwoDs.loadData([]);
      } else {
        headerTableTwoDs.queryDataSet = searchFormDs.queryDataSet;
        await headerTableTwoDs.query();
      }
    }
    setSelectDatas([])
  };

  const messageRetransmission = async() => {
    const params =  headerTableTwoDs.selected.map((item) => item.data.inspectRequestWaitId);
    await request(
      `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inspect-request-wait/re-execute/ui`,
      {
        method: 'POST',
        body: params,
      },
    ).then(res => {
      if (res && res.success) {
        notification.success({});
        headerTableTwoDs.query();
        setSelectDatas([]);
      } else {
        notification.warning({ description: res.message });
      }
    });
  }

  const onFieldEnterDown = () => {
    searchFormDs.query(props.searchFormDs.currentPage);
  }

  // @ts-ignore
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.InspectionManagement`).d('报检请求管理平台')}>
        {currentTab === '1' && <PermissionButton
          disabled={!inspectRequestIds.length}
          onClick={() => disposal()}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          <Icon type="cached" style={{marginBottom: '3px', marginRight:'3px'}}/>{intl.get(`${modelPrompt}.button.dispose`).d('处置')}
        </PermissionButton>}
        {currentTab === '2' && <PermissionButton
          disabled={selectDatas.length === 0}
          onClick={() => messageRetransmission()}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-消息重推',
            },
          ]}
        >
          <Icon type="cached" style={{marginBottom: '3px', marginRight:'3px'}}/>{intl.get(`${modelPrompt}.button.message.push`).d('消息重推')}
        </PermissionButton>}
      </Header>
      <Content className={styles['deliver-content']}>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
          dataSet={searchFormDs}
          queryFieldsLimit={4}
          columns={[]}
          searchCode="InspectionManagement"
          customizedCode="InspectionManagement"
          className={styles['inspection-management-search-table']}
        />
        <Tabs
          defaultActiveKey="1"
          onTabClick={onTabClick}
        >
          <Tabs.TabPane key="1" tab={intl.get(`${modelPrompt}.round1`).d('全部报检请求')}>
            <Table
              searchCode="bjqqgl1"
              dataSet={headerTableDs}
              columns={tableColumns as any}
              highLightRow
              queryBar={TableQueryBarType.none}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              customizable
              customizedCode="Order"
            />,
          </Tabs.TabPane>
          <Tabs.TabPane key="2" tab={intl.get(`${modelPrompt}.round2`).d('处理失败消息')}>
            <Table
              searchCode="bjqqgl2"
              dataSet={headerTableTwoDs}
              columns={tableTwoColumns as any}
              highLightRow
              queryBar={TableQueryBarType.none}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              customizedCode="Order"
            />,
          </Tabs.TabPane>
        </Tabs>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.hmes.inspection.inspection-management', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const searchFormDs = new DataSet({ ...searchFormDS() });
      const headerTableTwoDs = new DataSet({ ...headerTableTwoDS() });
      const detailDs = new DataSet({ ...detailDS()});
      return {
        headerTableDs,
        headerTableTwoDs,
        searchFormDs,
        detailDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(Order);
