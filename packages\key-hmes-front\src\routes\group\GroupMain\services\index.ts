/**
 * @Description: 班组维护-分配组织-接口
 * @Author: <<EMAIL>>
 * @Date: 2022-07-29 16:20:12
 * @LastEditTime: 2023-05-18 15:17:43
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 组织树
export function orgConfig() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-org-rel/org/tree/ui`,
    method: 'GET',
  };
}

// 库位树
export function locaotrConfig() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-org-rel/locator/tree/ui`,
    method: 'GET',
  };
}

// 分配组织
export function saveOrgRelConfig() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-org-rel/save/ui`,
    method: 'POST',
  };
}

// 列表数据
export function tablesConfig() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-org-rel/query/ui`,
    method: 'GET',
  };
}


// 借调-左侧树
export function shiftTeamSecondmentLeft() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-secondment/left-box/ui`,
    method: 'GET',
  };
}

// 借调-右侧树
export function shiftTeamSecondmentRight() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-secondment/right-box/ui`,
    method: 'POST',
  };
}

// 借调-借出
export function shiftTeamSecondmentOut() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-secondment/secondment/ui`,
    method: 'POST',
  };
}

// 借调-退回
export function shiftTeamSecondmentReturn() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-secondment/secondment-return/ui`,
    method: 'POST',
  };
}

// 详情界面-详情页-保存
export function SaveDetail() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SHIFT_TEAM_DETAIL.BASIC`,
    method: 'POST',
  };
}

// 详情界面-分配班次抽屉-查询可分配班次
export function GetShiftList() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-org-rel/available/ui`,
    method: 'POST',
  };
}

// 详情界面-分配班次抽屉-保存
export function SaveShiftList() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-schedule/save/ui`,
    method: 'POST',
  };
}

// 详情界面-分配班次抽屉-删除
export function DeleteShiftListItem() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-schedule/delete/ui`,
    method: 'POST',
  };
}

// 详情界面-分配班次抽屉-批量修改
export function BatchUpdateShiftList() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-schedule/update/ui`,
    method: 'POST',
  };
}

// 详情界面-分配班次抽屉-获取班次
export function QueryShiftCodes() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-schedule/shift-codes/ui`,
    method: 'POST',
  };
}

// 同步设备
export function getSynchronizeDevicesApi() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-equipment-release/sync/eqt`,
    method: 'GET',
  };
}
