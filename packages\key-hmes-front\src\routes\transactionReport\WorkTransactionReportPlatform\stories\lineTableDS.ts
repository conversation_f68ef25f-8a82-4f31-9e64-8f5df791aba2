/**
 * @Description: 事务报表平台-行表-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 18:25:02
 * @LastEditTime: 2023-03-21 14:56:34
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.mes.event.materialTransIface';
const tenantId = getCurrentOrganizationId();

// const prefix = '/mes-42638'


export const lineTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'transTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transTypeCode`).d('ERP事务编码'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lookupCode: 'MT.INTERFACE_STATUS',
    },
    {
      name: 'materialTransIfaceId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialTransIfaceId`).d('事务汇总ID'),
    },
    {
      name: 'eventId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
    },
    {
      name: 'eventTypeCodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCodeDesc`).d('事件类型'),
    },
    {
      name: 'businessTypeCodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessTypeCode`).d('业务类型编码'),
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('报错消息'),
    },
    {
      name: 'transTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.transTime`).d('事务时间'),
    },
    {
      name: 'accountTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.accountTime`).d('账务时间'),
    },
    {
      name: 'plantCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.plantCode`).d('工厂'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'scrapQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapQty`).d('报废数量'),
    },
    {
      name: 'reworkQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkQty`).d('返工数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('数量单位'),
    },
    {
      name: 'cancelRueck',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelRueck`).d('确认号'),
    },
    {
      name: 'cancelRmzhl',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelRmzhl`).d('确认计数器'),
    },
    {
      name: 'cancelFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelFlag`).d('撤销报工标识'),
    },
    {
      name: 'workCenter',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workCenter`).d('工作中心'),
    },

    {
      name: 'operationSeqNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationSeqNum`).d('组件工序代码'),
    },
    {
      name: 'operationStep',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationStep`).d('组件工序状态'),
    },
    {
      name: 'sourceOperationSeqNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOperationSeqNum`).d('来源组件工序代码'),
    },
    {
      name: 'sourceOperationStep',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOperationStep`).d('来源组件工序状态'),
    },
    {
      name: 'manualDuration',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manualDuration`).d('人工工时'),
    },
    {
      name: 'manualDurationUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manualDurationUomCode`).d('人工工时单位'),
    },
    {
      name: 'machineDuration',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.machineDuration`).d('机器工时'),
    },
    {
      name: 'machineDurationUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.machineDurationUomCode`).d('机器工时单位'),
    },
    {
      name: 'otherDuration',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherDuration`).d('其他工时'),
    },
    {
      name: 'otherDurationUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherDurationUomCode`).d('其他工时单位'),
    },
    {
      name: 'eventTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.eventTime`).d('事件时间'),
    },
    {
      name: 'transAccount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transAccount`).d('事务帐户信息'),
    },
    {
      name: 'transReasonCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transReasonCode`).d('事务原因'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
    {
      name: 'attribute1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute1`).d('扩展字段1'),
    },
    {
      name: 'attribute2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute2`).d('扩展字段2'),
    },
    {
      name: 'attribute3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute3`).d('扩展字段3'),
    },
    {
      name: 'attribute4',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute4`).d('扩展字段4'),
    },
    {
      name: 'attribute5',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute5`).d('扩展字段5'),
    },












    // {
    //   name: 'transCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.transCode`).d('事务代码'),
    // },
    // {
    //   name: 'sumFlagDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sumFlag`).d('汇总标识'),
    // },
    // {
    //   name: 'locatorCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.locatorCode`).d('ERP仓库'),
    // },
    // {
    //   name: 'infoLocatorCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.infoLocatorCode`).d('库位'),
    // },
    // {
    //   name: 'sourcePlantCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourcePlantCode`).d('来源工厂'),
    // },
    // {
    //   name: 'sourceSiteCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceSiteCode`).d('来源站点'),
    // },
    // {
    //   name: 'sourceLocatorCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceLocatorCode`).d('来源ERP仓库'),
    // },
    // {
    //   name: 'infoSourceLocatorCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.infoSourceLocatorCode`).d('来源库位'),
    // },
    // {
    //   name: 'materialCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    // },
    // {
    //   name: 'revisionCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    // },
    // {
    //   name: 'primaryUomQty',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.primaryUomQty`).d('主单位数量'),
    // },
    // {
    //   name: 'primaryUomCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.primaryUomCode`).d('主计量单位'),
    // },
    // {
    //   name: 'secondaryUomQty',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.secondaryUomQty`).d('辅助单位数量'),
    // },
    // {
    //   name: 'secondaryUomCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.secondaryUomCode`).d('辅助计量单位'),
    // },
    // {
    //   name: 'transReasonCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.transReasonCode`).d('事务原因'),
    // },
    // {
    //   name: 'materialLotCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批条码'),
    // },
    // {
    //   name: 'containerCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.containerCode`).d('容器条码'),
    // },
    // {
    //   name: 'lot',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.lot`).d('批次'),
    // },
    // {
    //   name: 'workcellCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元'),
    // },
    // {
    //   name: 'prodLineCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线'),
    // },
    // {
    //   name: 'sourceOrderType',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceOrderType`).d('来源订单类型'),
    //   lookupCode: 'MT.SOURCE_ORDER_TYPE',
    // },
    // {
    //   name: 'sourceOrder',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceOrder`).d('来源订单'),
    // },
    // {
    //   name: 'sourceOrderLineNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceOrderLineNum`).d('来源订单行'),
    // },
    // {
    //   name: 'sourceOrderCompLineNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceOrderCompLineNum`).d('来源订单组件行'),
    // },
    // {
    //   name: 'instructionDocTypeDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
    // },
    // {
    //   name: 'instructionDocNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据号'),
    // },
    // {
    //   name: 'lineNumber',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.lineNumber`).d('单据行号'),
    // },
    // {
    //   name: 'instructionNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.instructionNum`).d('指令'),
    // },
    // {
    //   name: 'supplierCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.supplierCode`).d('供应商'),
    // },
    // {
    //   name: 'supplierSiteCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.supplierSiteCode`).d('供应商地点'),
    // },
    // {
    //   name: 'customerCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.customerCode`).d('客户'),
    // },
    // {
    //   name: 'customerSiteCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.customerSiteCode`).d('客户地点'),
    // },
    // {
    //   name: 'operationSequence',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.operationSequence`).d('组件工序代码'),
    // },
    // {
    //   name: 'rsnum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.rsnum`).d('预留号'),
    // },
    // {
    //   name: 'rspos',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.rspos`).d('预留项目行号'),
    // },
    // {
    //   name: 'ownerTypeDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
    // },
    // {
    //   name: 'specStockType',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.specStockType`).d('目标ERP特殊库存标识'),
    // },
    // {
    //   name: 'ownerCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ownerCode`).d('所有者'),
    // },
    // {
    //   name: 'ownerLineCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ownerLineCode`).d('目标所有者行编码'),
    // },
    // {
    //   name: 'sourceOwnerTypeDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceOwnerType`).d('来源所有者类型'),
    // },
    // {
    //   name: 'sourceSpecStockType',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceSpecStockType`).d('来源ERP特殊库存标识'),
    // },
    // {
    //   name: 'sourceOwnerCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceOwnerCode`).d('来源所有者'),
    // },
    // {
    //   name: 'sourceOwnerLineCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceOwnerLineCode`).d('来源所有者行编码'),
    // },
    // {
    //   name: 'reservedObjectTypeDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reservedObjectType`).d('预留类型'),
    // },
    // {
    //   name: 'reservedObjectCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象'),
    // },
    // {
    //   name: 'sourceReservedObjectTypeDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceReservedObjectType`).d('来源预留类型'),
    // },
    // {
    //   name: 'sourceReservedObjectCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.sourceReservedObjectCode`).d('来源预留对象'),
    // },
    // {
    //   name: 'attribute1',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.attribute1`).d('扩展字段1'),
    // },
    // {
    //   name: 'attribute2',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.attribute2`).d('扩展字段2'),
    // },
    // {
    //   name: 'attribute3',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.attribute3`).d('扩展字段3'),
    // },
    // {
    //   name: 'attribute4',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.attribute4`).d('扩展字段4'),
    // },
    // {
    //   name: 'attribute5',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.attribute5`).d('扩展字段5'),
    // },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-wo-report-trans-dtls/query/ui`,
        // url: `${prefix}/v1/${tenantId}/mt-wo-report-trans-dtls/query/ui`,
        method: 'GET',
      };
    },
  },
});
