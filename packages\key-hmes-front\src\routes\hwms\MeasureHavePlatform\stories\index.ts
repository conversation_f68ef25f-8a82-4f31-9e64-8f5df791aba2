/**
 * @Description: 量具检定平台-DS
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import {getCurrentOrganizationId, getCurrentUser} from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.inspectExecute.MeasureHavePlatform';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'applicationDocId',
  queryFields: [
    {
      name: 'applicationDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationDocNum`).d('申请单号'),
    },
    {
      name: 'applicationDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationDocStatus`).d('状态'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'docType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_TYPE',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('申请部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
    },
    {
      name: 'departmentId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('申请人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'createdBy',
      bind: 'responsibleUserLov.userId',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('申请时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('申请时间至'),
      min: 'creationDateFrom',
    },
  ],
  fields: [
    {
      name: 'applicationDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationDocNum`).d('申请单号'),
    },
    {
      name: 'applicationDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationDocStatus`).d('状态'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_STATUS',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'docType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_TYPE',
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('申请时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('申请人'),
    },
    {
      name: 'departmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.departmentName`).d('申请部门'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'reviewByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewByName`).d('审批人'),
    },
    {
      name: 'reviewDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewDate`).d('审批时间'),
    },
    {
      name: 'rejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/application/header/ui`,
        method: 'GET',
      };
    },
  },
  record: {
    dynamicProps: {
      selectable: record => ['NEW', 'REJECTED'].includes(record?.get('applicationDocStatus')) && Number(userInfo.id) === record?.get('createdBy'),
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'applicationDocId',
  fields: [
    {
      name: 'toolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编号'),
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
    },
    {
      name: 'modelName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelName`).d('型号描述'),
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检定单号'),
    },
    {
      name: 'inspectDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('检定单状态'),
      lookupCode: 'YP.QIS.MS_INSPECT_DOC_STATUS',
    },
    {
      name: 'inspectPlace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectPlace`).d('检定地点'),
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResult`).d('检定结果'),
      lookupCode: 'YP.QIS.MS_INSPECT_RESULT',
      lovPara: { tenantId },
    },
    {
      name: 'appearanceResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.appearanceResult`).d('外观是否合格'),
      lookupCode: 'YP.QIS.MS_APPEARANCE_RESULT',
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检定人'),
    },
    {
      name: 'completionTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completionTime`).d('检定完成时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'inspectReportUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.inspectReport`).d('检定报告'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/application/line/ui`,
        method: 'GET',
      };
    },
  },
});


const inspectDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectDocId',
  queryFields: [
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检定单号'),
    },
    {
      name: 'inspectDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('状态'),
      lookupCode: 'YP.QIS.MS_INSPECT_DOC_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'docType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_TYPE',
    },
    {
      name: 'toolLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toolLov`).d('量具编号'),
      lovCode: 'YP.QIS.ALL_MT_TOOL',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'toolCode',
      bind: 'toolLov.toolCode',
    },
    {
      name: 'msToolManageId',
      bind: 'toolLov.msToolManageId',
    },
    {
      name: 'modelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.modelLov`).d('量具型号'),
      lovCode: 'YP.QMS_TOOL_MODEL',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'modelId',
      bind: 'modelLov.toolModelId',
    },
    {
      name: 'typeDescLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.typeDesc`).d('种别描述'),
      lovCode: 'YP.QMS_YP_TYPE_TEST',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'typeId',
      bind: 'typeDescLov.toolTypeId',
    },
    {
      name: 'inspectorUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectorUserName`).d('检定人'),
      lovCode: 'YP.QIS.USER_INSPECTOR',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'inspectorId',
      bind: 'inspectorUserLov.id',
    },
    {
      name: 'inspectPlace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectPlace`).d('检定地点'),
      lookupCode: 'YP.QIS.MS_INSPECT_PLACE',
    },
    {
      name: 'verificationMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
      lookupCode: 'QIS.MS_VRFCT_METHOD',
    },
    {
      name: 'usingStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingStatus`).d('量具使用状态'),
      lookupCode: 'QIS.MS_TOOL_USING_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'applicationCreationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.applicationCreationDateFrom`).d('申请时间从'),
      max: 'applicationCreationDateTo',
    },
    {
      name: 'applicationCreationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.applicationCreationDateTo`).d('申请时间至'),
      min: 'applicationCreationDateFrom',
    },
    {
      name: 'responsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.applicationCreatedName`).d('申请人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'applicationCreatedBy',
      bind: 'responsibleUserLov.userId',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('申请部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
    },
    {
      name: 'departmentId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'applicationDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationDocNum`).d('申请单号'),
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResult`).d('检定结果'),
      lookupCode: 'YP.QIS.MS_INSPECT_RESULT',
      lovPara: { tenantId },
    },
    {
      name: 'appearanceResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.appearanceResult`).d('外观是否合格'),
      lookupCode: 'YP.QIS.MS_APPEARANCE_RESULT',
    },
    {
      name: 'servicedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.servicedFlag`).d('是否维修'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'createdByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'createdBy',
      bind: 'createdByLov.userId',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
  ],
  fields: [
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检定单号'),
    },
    {
      name: 'inspectDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('状态'),
      // lookupCode: 'YP.QIS.MS_APPLICATION_DOC_STATUS',
      lookupCode: 'YP.QIS.MS_INSPECT_DOC_STATUS',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'docType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_TYPE',
    },
    {
      name: 'toolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编码'),
    },
    {
      name: 'responName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responName`).d('量具责任人'),
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
    },
    {
      name: 'modelName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelName`).d('型号描述'),
    },
    {
      name: 'verificationMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
      lookupCode: 'QIS.MS_VRFCT_METHOD',
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检定人'),
    },
    {
      name: 'usingStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingStatus`).d('量具使用状态'),
      lookupCode: 'QIS.MS_TOOL_USING_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'applicationCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationCreationDate`).d('申请时间'),
    },
    {
      name: 'departmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.departmentName`).d('申请部门'),
    },
    {
      name: 'applicationDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationDocNum`).d('申请单'),
    },
    {
      name: 'applicationCreatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationCreatedByName`).d('申请人'),
    },
    {
      name: 'inspectPlace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectPlace`).d('检定地点'),
    },
    {
      name: 'inspectResult',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectResult`).d('检定结果'),
      lookupCode: 'YP.QIS.MS_INSPECT_RESULT',
      lovPara: { tenantId },
    },
    {
      name: 'appearanceResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.appearanceResult`).d('外观是否合格'),
      lookupCode: 'YP.QIS.MS_APPEARANCE_RESULT',
    },
    {
      name: 'ngReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngReason`).d('不合格原因'),
    },
    {
      name: 'servicedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.servicedFlag`).d('是否维修'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'completionTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completionTime`).d('检定完成时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'inspectReportUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.inspectReportUuid`).d('检定报告'),
    },
    {
      name: 'cancelReason',
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消原因'),
      type: FieldType.string,
    },
    {
      name: 'cancelDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelDate`).d('取消时间'),
    },
    {
      name: 'graphUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.graphUuid`).d('图纸附件'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/header/ui`,
        method: 'GET',
      };
    },
  },
  record: {
    dynamicProps: {
      selectable: record => ['OK', 'NG'].includes(record?.get('inspectResult')),
    },
  },
});

export { headDS, lineDS, inspectDS };
