/**
 * @Description: 体系审核管理维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { Form, Select, Lov, TextField, TextArea, DateTimePicker } from 'choerodon-ui/pro';
import myInstance from '@/utils/myAxios';
import { getCurrentUserId } from 'utils/utils';
import notification from 'utils/notification';
import intl from 'utils/intl';

const AuditProblem = props => {
  const { formDs } = props;

  useEffect(() => {
    myInstance
      .get(
        `/hpfm/v1/lovs/sql/data?lovCode=HPFM.USER_LIMIT_EMPLOYEE_POSITION&page=0&size=10&userId=${getCurrentUserId()}`,
      )
      .then(res => {
        const { content, empty, failed, message } = res.data;
        if (res && failed) {
          notification.error({
            message: message || intl.get('hzero.common.notification.error').d('操作失败'),
          });
          return;
        }
        if (empty) {
          return;
        }
        const userInfo = content?.length ? content[0] : {};
        formDs.current?.set('registerPersonLov', {
          ...userInfo,
        });
        // formDs.current?.set('proposePersonLov', {
        //   ...userInfo,
        // });
      });
  }, []);

  return (
    <>
      <Form dataSet={formDs} columns={1} labelWidth={112}>
        <Select name="problemCategory" />
        <Lov name="siteLov" />
        <Select name="problemStatus" />
        <TextField name="problemTitle" />
        <TextArea name="problemDescription" rowSpan={3} />
        <Lov name="leadPersonLov" />
        <Lov name="registerPersonLov" />
        <TextField name="registerPersonCompanyName" />
        <Lov name="proposePersonLov" />
        <TextField name="proposePersonCompanyName" />
        <DateTimePicker name="proposeTimePeriod" />
      </Form>
    </>
  );
};

export default AuditProblem;
