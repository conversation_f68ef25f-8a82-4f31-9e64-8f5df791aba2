/*
 * @Description: 发货报告打印平台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2024-03-06 16:46:27
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-08 16:19:33
 */
import React, { useMemo, useState, useRef } from 'react';
import JsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Table, DataSet, TextField, Icon, Button } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { observer } from 'mobx-react';
import { overrideTableBar } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import LovModal from '@/components/BatchInput/LovModal';
import PerviewComponent from './PerviewComponent';
import { tableDS } from '../stores';
import { QueryPrintInfo } from '../services';

const modelPrompt = 'tarzan.hmes.deliveryReportPrint';

const DeliveryReportPrintList = props => {
  const { tableDs } = props;
  const childRef = useRef<any>(null);
  const [dataSource, setDataSource] = useState<any>([]);
  const [showPdfFlag, setShowPdfFlag] = useState<boolean>(false);
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);

  const queryPrintInfo = useRequest(QueryPrintInfo(), { manual: true, needPromise: true });

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
      // handleSearch()
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'identification' },
      { name: 'materialCode' },
      {
        name: 'actualEndTime',
        align: ColumnAlign.center,
      },
      {
        name: 'result',
        align: ColumnAlign.center,
      },
    ];
  }, []);

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const exportBtnDisabled = useMemo(() => !tableDs.selected?.length, [tableDs.selected]);

  const handlePreviewReport = () => {
    return new Promise(async resolve => {
      const res = await queryPrintInfo.run({
        params: tableDs.selected.map(record => record?.toData()),
      });

      if (res?.length) {
        // 第一页填满所需行数
        const firstPageCount = 40;
        const _res = (res || []).map(item => {
          const { lineList, ...others } = item;
          let _lineList = lineList;
          if (lineList?.length < firstPageCount) {
            _lineList = _lineList.concat(new Array(firstPageCount - (lineList?.length || 0)).fill({}));
          }
          return {
            ...others,
            lineList: _lineList,
          };
        })
        setDataSource(_res || []);
        setShowPdfFlag(true);

        const elements = document.querySelectorAll('.print-pdf-report');
        const pdf = new JsPDF('p', 'pt', 'a4');
        pdf.setDisplayMode('fullwidth', 'continuous', 'FullScreen');

        const promises = Array.from(elements).map(element => {
          return html2canvas(element, {
            dpi: window.devicePixelRatio > 1 ? window.devicePixelRatio : 2,
            useCORS: true,
          });
        });

        Promise.all(promises).then(canvases => {
          canvases.forEach((canvas, index) => {
            const ctx = canvas.getContext('2d');
            const a4w = 555;
            const a4h = 802;  
            const imgHeight = Math.floor((a4h * canvas.width) / a4w);
            let renderedHeight = 0;

            if (index > 0) {
              pdf.addPage();
            }

            while (renderedHeight < canvas.height) {
              if (renderedHeight > 0) {
                pdf.addPage();
              }
              const page = document.createElement('canvas');
              page.width = canvas.width;
              page.height = Math.min(imgHeight, canvas.height - renderedHeight);

              page
                .getContext('2d')
                .putImageData(
                  ctx.getImageData(
                    0,
                    renderedHeight,
                    canvas.width,
                    Math.min(imgHeight, canvas.height - renderedHeight),
                  ),
                  0,
                  0,
                );

              pdf.addImage(
                page.toDataURL('image/jpeg', 1.0),
                'JPEG',
                20,
                20,
                a4w,
                Math.min(a4h, (a4w * page.height) / page.width),
              );

              renderedHeight += imgHeight;
            }
          });

          window.open(pdf.output('bloburl'))
          setShowPdfFlag(false);
          resolve(true);
        });
      } else {
        setShowPdfFlag(false);
        resolve(false);
      }
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('发货报告打印平台')}>
        <Button
          color={ButtonColor.primary}
          disabled={exportBtnDisabled}
          onClick={() => handlePreviewReport()}
        >
          {intl.get(`${modelPrompt}.button.export`).d('导出')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={overrideTableBar}
          queryFields={{
            identifications: (
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(
                          true,
                          'identifications',
                          intl.get(`${modelPrompt}.identification`).d('条码号'),
                        )
                      }
                    />
                  </div>
                }
              />
            ),
          }}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="page_searchCode"
          customizedCode="page_customizedCode"
        />
        <div style={{opacity: 0, height: 1, overflow: 'hidden'}}>
          {showPdfFlag && <PerviewComponent dataSource={dataSource} ref={childRef} />}
        </div>
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(observer(DeliveryReportPrintList)),
);
