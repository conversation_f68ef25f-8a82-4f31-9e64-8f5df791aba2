/**
 * @Description: 检验方案维护-详情-tab title组件
 * @Author: <<EMAIL>>
 * @Date: 2023-01-17 12:04:40
 * @LastEditTime: 2023-01-18 16:21:54
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.initialManagementActivity';

const PanelTitle = observer(({ ds }: { ds: DataSet }) => {
  return (
    <>
      <span>
        {ds.current?.get('inspectBusinessTypeDesc') ||
          intl.get(`${modelPrompt}.tab.inspectBusinessType`).d('请选择检验业务类型')}
      </span>
    </>
  );
});

export default PanelTitle;
