/**
 * WorkOrder - 工作单
 * @since 2020-09-18
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component } from 'react';
import moment from 'moment';
import { Bind } from 'lodash-decorators';
import { isUndefined } from 'lodash';
import { Lov, DataSet, Table, Button, Modal, Select, Form, TextField } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { yesOrNoRender, dateTimeRender } from 'utils/renderer';
import notification from 'utils/notification';
import queryString from 'querystring';
import { getDateTimeFormat, getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';
import { modifyBtnsClickStatus } from 'alm/utils/utils';
import IconSelect from 'alm/components/IconSelect';
import request from 'utils/request';
import withProps from 'utils/withProps';
import EmployeesLov from 'alm/components/EmployeesLov';
import { tableProps as priorityTableProps } from 'alm/components/PriorityLov/tableProps';

import { statusColors } from 'alm/utils/constants';
import { tableDs } from './Stores/listDs';
import CopyAdd from './Detail/CopyAdd/index';

import getLang from './Langs';

const viewPrompt = `amtc.workOrder.view.message`;

const organizationId = getCurrentOrganizationId();
const modalKey = Modal.key();

// 删除
const deleteUrl = `${HALM_MTC}/v1/${organizationId}/work-orders`;

@formatterCollections({
  code: ['alm.common', 'alm.component', 'amtc.workOrder'],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDs());
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true },
)
export default class WorkOrder extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // btnCanClick: false, // 按钮可点击
      showBtnList: false,
    };
  }

  componentDidMount() {
    this.props.listDS.modifyBtnsClickStatus = modifyBtnsClickStatus.bind(
      this,
      this.props.listDS,
      'btnCanClick',
    );
  }

  /**
   * 新建
   */
  @Bind()
  handleCreate() {
    this.props.history.push(`/amtc/work-order/create`);
  }

  /**
   * 跳转到详情页
   */
  @Bind()
  handleLinkToDetail(record) {
    const id = record.get('woId');
    const woNum = record.get('woNum');
    const linkUrl = isUndefined(id) ? 'create' : `detail/${id}/${woNum}`;
    this.props.history.push({
      pathname: `/amtc/work-order/${linkUrl}`,
    });
  }

  /**
   * 删除
   */
  @Bind()
  handleDelete() {
    Modal.confirm({
      title: intl.get(`${viewPrompt}.modal.deleteLine`).d('是否删除选中的工单？'),
      onOk: async () => {
        const data = this.props.listDS.selected.map(i => i.toData());
        request(deleteUrl, {
          method: 'DELETE',
          body: data,
        }).then(res => {
          if (res && !res.failed) {
            this.props.listDS.query();
          }
        });
      },
    });
  }

  /**
   * 修改状态修改按钮列表是否显示的state属性:showBtnList
   */
  @Bind()
  changeShowBtnList() {
    const { showBtnList } = this.state;
    this.setState({ showBtnList: !showBtnList });
  }

  @Bind()
  openCancelModal() {
    const cancelDs = new DataSet({
      autoQuery: false,
      selection: false,
      autoCreate: true,
      paging: false,
      fields: [
        {
          name: 'closingReason',
          label: intl.get(`${viewPrompt}.closingReason`).d('取消原因'),
          type: 'string',
          required: true,
        },
      ],
    })
    if (!this.props.listDS.selected.length) {
      return notification.error({
        message: intl.get(`${viewPrompt}.pleaseSelect`).d('请选择数据！'),
      });
    }
    Modal.open({
      closable: true,
      children: (
        <Form dataSet={cancelDs}>
          <TextField name='closingReason'/>
        </Form>
      ),
      onOk: async () => {
        const validate = await cancelDs.validate()
        if (!validate) {
          return false;
        }
        request(`${HALM_MTC}/v1/${organizationId}/work-orders/change-status/batch/cancel`, {
          method: 'POST',
          body: {
            woIds: this.props.listDS.selected.map(record => record.get('woId')),
            closingReason: cancelDs.current.get('closingReason'),
          },
        }).then(() => {
          this.props.listDS.query();
        })
      },
    })
  }

  @Bind()
  handleCopy() {
    Modal.open({
      key: modalKey,
      destroyOnClose: true,
      closable: true,
      style: {
        width: 700,
      },
      title: intl.get(`${viewPrompt}.modal.act`).d('标准作业'),
      children: (
        <CopyAdd
          props={this.props}
          ref={node => {
            this.childRef = node;
          }}
        />
      ),
      onOk: () => {
        const { state, copyAddDs } = this.childRef;
        const { selected, malfunctionParams } = state;
        const selectData = copyAddDs.current.toData();
        const queryData = copyAddDs.getState('queryData') || {};
        const { woAsset, assetLocation } = queryData;
        const { defaultCostObjectType } = selectData;
        let newCostObject = {};
        let woMalfunctions = {};
        if (selected) {
          if (queryData) {
            // 选择了设备
            if (woAsset) {
              newCostObject = {
                ...newCostObject,
                assetId: woAsset.assetId,
                descAndLabel: woAsset.descAndLabel,
                assetLocationId: woAsset.assetLocationId,
                assetLocationName: woAsset.assetLocationName,
              };
              switch (defaultCostObjectType) {
                case 'COST_CENTER':
                  newCostObject = {
                    ...newCostObject,
                    costCenterId: woAsset.costCenterId,
                    costCenterMeaning: woAsset.costCenterMeaning,
                  };
                  break;
                case 'INTERNAL_ORDER':
                  newCostObject = {
                    ...newCostObject,
                    internalOrderId: woAsset.internalOrderId,
                    internalOrderMeaning: woAsset.internalOrderMeaning,
                  };
                  break;
                case 'WBS_ELEMENT':
                  newCostObject = {
                    ...newCostObject,
                    wbsElementId: woAsset.wbsElementId,
                    wbsElementMeaning: woAsset.wbsElementMeaning,
                  };
                  break;
                default:
              }
            }
            // 选择了位置
            if (isUndefined(woAsset) && assetLocation) {
              newCostObject = {
                ...newCostObject,
                assetLocationId: assetLocation.assetLocationId,
                assetLocationName: assetLocation.locationName,
              };
            }
            // 选择故障
            if (malfunctionParams) {
              woMalfunctions = [
                {
                  ...malfunctionParams,
                  malfunctionTime: moment().format(getDateTimeFormat()),
                  tenantId: organizationId,
                },
              ];
            }
          }
          // 数据直接参照编辑页面选择标准作业时的数据变动（handleAct）
          const detail = {
            sourceParamType: 'ACT',
            actId: selected,
            woMalfunctions,
            ...newCostObject,
          };
          this.props.history.push({
            pathname: `/amtc/work-order/create`,
            search: queryString.stringify(detail),
            state: { detail },
          });
        } else {
          notification.error({
            message: intl.get(`${viewPrompt}.pleaseSelect`).d('请选择一条数据！'),
          });
          return Promise.reject();
        }
      },
    });
  }

  // 导出
  @Bind()
  handleExport() {
    const params = this.props.listDS?.queryDataSet?.toData()[0];
    const data = this.props.listDS?.queryParameter
    delete params.scheduledStartDate;
    request(`${HALM_MTC}/v1/${organizationId}/work-orders/export`, {
      method: "GET",
      query: {
        ...params,
        ...data,
        woIdStr: this.props.listDS.selected.map((ele) => ele.get('woId')),
      },
      responseType: 'blob',
    }).then((response) => {
      if (response) {
        if (response.type === 'application/json') {
          const file = new FileReader();
          file.readAsText(response, 'utf-8');
          file.onload = () => {
            if (typeof file.result === 'string') {
              const message = JSON.parse(file.result);
              return notification.error({ message: message.message });
            }
          };
        } else {
          const fileName = `工作单`;
          const elink = document.createElement('a');
          elink.download = fileName ? window.decodeURI(fileName) : `工作单`;
          elink.style.display = 'none';
          const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          document.body.removeChild(elink);
        }
      }
    });
  };

  get btns() {
    return [
      <Button color="primary" onClick={this.handleCreate} key="create">
        {intl.get('hzero.common.button.create').d('新建')}
      </Button>, // 新建
      <Button onClick={this.handleCopy} key="copy">
        {intl.get(`${viewPrompt}.button.copy`).d('复制自模板')}
      </Button>,
      <Button onClick={this.openCancelModal}>
        {intl.get(`${viewPrompt}.cancel`).d('取消')}
      </Button>,
      <Button onClick={this.handleExport}>
        {intl.get(`${viewPrompt}.excelExport`).d('导出')}
      </Button>,
    ];
  }

  get columns() {
    return [
      {
        name: 'woTypeIcon',
        width: 70,
        align: 'center',
        renderer: ({ record }) => {
          const { woTypeIcon, woTypeIconTypeCode, priorityColor } = record.toData();
          return (
            <IconSelect
              border
              title={woTypeIcon}
              iconTypeCode={woTypeIconTypeCode}
              color={priorityColor}
              type={woTypeIcon}
              style={{
                width: 36,
                height: 36,
              }}
            />
          );
        },
      },
      {
        name: 'woNum',
        align: 'center',
        renderer: ({ value, record }) => {
          return <a onClick={() => this.handleLinkToDetail(record, false)}>{value}</a>;
        },
      },
      {
        name: 'woName',
        width: 290,
      },
      {
        name: 'woStatusMeaning',
        width: 100,
        align: 'center',
        renderer: ({ record }) => {
          const { woStatus, woStatusMeaning } = record.toData();
          return (
            <Tag
              style={{
                color: (statusColors[woStatus] && statusColors[woStatus].fontColor) || '#000',
                border: 0,
              }}
              color={(statusColors[woStatus] && statusColors[woStatus].bgColor) || '#fff'}
            >
              {woStatusMeaning}
            </Tag>
          );
        },
      },
      {
        name: 'descAndLabel',
        minWidth: 120,
      },
      {
        name: 'assetLocationName',
        minWidth: 120,
        renderer: ({ value, record }) => {
          const code = record?.get('locationCode');
          return value ? `${value}-${code}` : '-';
        },
      },
      {
        name: 'priorityName',
      },
      {
        name: 'woTypeName',
        minWidth: 120,
      },
      {
        name: 'ownerName',
        minWidth: 100,
      },
      {
        name: 'plannerName',
        minWidth: 100,
      },
      {
        name: 'scheduledStartDate',
        width: 150,
        align: 'center',
        renderer: ({ value }) => {
          return dateTimeRender(value);
        },
      },
      {
        name: 'scheduledFinishDate',
        width: 150,
        align: 'center',
        renderer: ({ value }) => {
          return dateTimeRender(value);
        },
      },
      {
        name: 'durationScheduled',
        width: 90,
        renderer: ({ value, record }) => {
          const durationUomMeaning = record?.get('durationUomMeaning');
          if (!!value && !!durationUomMeaning) {
            return value + durationUomMeaning;
          }
          return '-';

        },
      },
      {
        name: 'reworkFlag',
        width: 90,
        renderer: ({ value = 0 }) => yesOrNoRender(value),
      },

      {
        name: 'maintSiteName',
        minWidth: 120,
      },
      {
        name: 'creationDate',
        minWidth: 120,
      },
      {
        name: 'actualStartDate',
        minWidth: 120,
      },
      {
        name: 'actualFinishDate',
        minWidth: 120,
      },
    ];
  }

  @Bind
  handleChangeOwnerGroup(e) {
    const { queryDataSet } = this.props.listDS;
    this.props.listDS.setQueryParameter('ownerGroupId', e.workCenterId);
    queryDataSet.current.set('ownerGroupName', e.workCenterName);
    if (e.workCenterId) {
      this.props.listDS.setQueryParameter('ownerId', e.employeeId);
      queryDataSet.current.set('ownerName', e.employeeName);
    }
  }

  @Bind
  handleChangeOwner(e) {
    this.props.listDS.setQueryParameter('ownerId', e.employeeId);
    this.props.listDS.queryDataSet.current.set('ownerName', e.employeeName);
  }

  @Bind
  handleChangePlannerGroup(e) {
    const { queryDataSet } = this.props.listDS;
    this.props.listDS.setQueryParameter('plannerGroupId', e.workCenterId);
    queryDataSet.current.set('plannerGroupName', e.workCenterName);
    if (e.workCenterId) {
      this.props.listDS.setQueryParameter('plannerId', e.employeeId);
      queryDataSet.current.set('plannerName', e.employeeName);
    }
  }

  @Bind
  handleChangePlanner(e) {
    this.props.listDS.setQueryParameter('plannerId', e.employeeId);
    this.props.listDS.queryDataSet.current.set('plannerName', e.employeeName);
  }

  @Bind
  woStatusFilter(record) {
    const code = record.get('value');
    // 过滤掉旧状态“等待安排、已签单、暂停作业、需改派”
    return !['WSCH', 'SIGNED', 'PAUSE', 'WRD'].includes(code);
  }

  render() {
    return (
      <React.Fragment>
        <Header title={intl.get(`${viewPrompt}.title.workOrder`).d('工作单')}>{this.btns}</Header>
        <Content>
          <Table
            key="list"
            searchCode="AORI.WORK_ORDER.LIST"
            customizedCode="AORI.WORK_ORDER.LIST"
            dataSet={this.props.listDS}
            columns={this.columns}
            queryBar="filterBar"
            queryFieldsLimit={3}
            queryFields={{
              woStatusList: <Select name="woStatusList" optionsFilter={this.woStatusFilter} />,
              ownerGroupName: <EmployeesLov onOk={this.handleChangeOwnerGroup} />,
              ownerName: <EmployeesLov onOk={this.handleChangeOwner} />,
              plannerGroupName: <EmployeesLov onOk={this.handleChangePlannerGroup} />,
              plannerName: <EmployeesLov onOk={this.handleChangePlanner} />,
              priorityLov: <Lov tableProps={priorityTableProps} />,
            }}
            queryBarProps={{
              fuzzyQueryPlaceholder: getLang('KEYWORD_PLACEHOLDER'),
              queryFieldsLimit: 3,
              onReset: () => {
                this.props.listDS.setQueryParameter('plannerGroupId', null);
                this.props.listDS.setQueryParameter('plannerId', null);
                this.props.listDS.setQueryParameter('ownerGroupId', null);
                this.props.listDS.setQueryParameter('ownerId', null);
              },
            }}
          />
        </Content>
      </React.Fragment>
    );
  }
}
