/**
 * @Description: 拆解申请单-详情页
 * @Author: <EMAIL>
 * @Date: 2023/8/14 14:30
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Collapse, Popconfirm } from 'choerodon-ui';
import {
  Button,
  DataSet,
  Form,
  Lov,
  NumberField,
  Select,
  TextField,
  DateTimePicker,
  TextArea,
  Attachment,
  Modal,
  Output,
} from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';

import { detailDS } from '../stores/DetailDS';
import {
  QueryMaterialLotInfo,
  SaveTeardownApplyDoc,
  SubmitTeardownApplyDoc,
  UpdateTeardownApplyDoc,
} from '../services';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.teardownApplyDoc';

const TeardownApplyDetail = props => {
  const {
    history,
    match: { params, path },
  } = props;
  const teardownApplyId = params.teardownApplyId;

  // pub路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  const [canEdit, setCanEdit] = useState(teardownApplyId === 'create');
  const [teardownApplyStatus, setStatus] = useState('NEW');
  const [materialLotType, setMaterialLotType] = useState('');
  const detailDs = useMemo(() => new DataSet(detailDS()), [teardownApplyId]);
  // 保存详情信息
  const { run: saveTeardownApplyDoc, loading: saveLoading } = useRequest(SaveTeardownApplyDoc(), {
    manual: true,
  });
  // 更新详情信息
  const { run: updateTeardownApplyDoc, loading: updateLoading } = useRequest(
    UpdateTeardownApplyDoc(),
    {
      manual: true,
    },
  );
  // 提交详情信息
  const { run: submitTeardownApplyDoc, loading: submitLoading } = useRequest(
    SubmitTeardownApplyDoc(),
    {
      manual: true,
    },
  );
  // 查询物料批信息
  const { run: queryMaterialLotInfo, loading: queryLoading } = useRequest(QueryMaterialLotInfo(), {
    manual: true,
  });

  useEffect(() => {
    if (teardownApplyId === 'create') {
      return;
    }
    // 编辑时
    handleQuery(teardownApplyId);
  }, [teardownApplyId]);

  const handleQuery = async id => {
    detailDs.setQueryParameter('teardownApplyId', id);
    await detailDs.query().then(res => {
      const { rows } = res;
      setStatus(rows.teardownApplyStatus);
      setMaterialLotType(rows.materialLotType);
    });
  };

  const handleEdit = () => {
    setCanEdit(true);
  };

  const handleSubmit = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    submitTeardownApplyDoc({
      params: {
        ...detailDs.current?.toData(),
      },
      onSuccess: () => {
        notification.success({});
        setCanEdit(false);
        handleQuery(teardownApplyId);
      },
    });
  };

  const handleCancel = () => {
    if (teardownApplyId === 'create') {
      history.push('/hwms/product-teardown/teardown-apply-doc/list');
    } else {
      setCanEdit(false);
      handleQuery(teardownApplyId);
    }
  };

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const _run = teardownApplyId === 'create' ? saveTeardownApplyDoc : updateTeardownApplyDoc;
    _run({
      params: {
        ...detailDs.current?.toData(),
      },
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        history.push(`/hwms/product-teardown/teardown-apply-doc/dist/${res.teardownApplyId}`);
        handleQuery(res.teardownApplyId);
      },
    });
  };

  const handleChangeType = (value, oldVal) => {
    if (detailDs.current?.get('materialLotCode')) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.changeType`)
              .d('电芯条码及相关字段会清空，确定更换电芯类型？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          detailDs.current?.set('materialLotLov', undefined);
          handleChangeMaterialLot(null);
          setMaterialLotType(value);
        } else {
          detailDs.current?.set('materialLotType', oldVal);
        }
      });
    } else {
      setMaterialLotType(value);
    }
  };

  const handleChangeMaterialLot = value => {
    if (value?.materialLotCode) {
      queryMaterialLotInfo({
        params: {
          materialLotCode: value?.materialLotCode,
        },
        onSuccess: res => {
          const { mtMaterialVO, mtModProductionLine, mtModSite } = res;
          detailDs.current?.set('siteId', mtModSite?.siteId);
          detailDs.current?.set('siteCode', mtModSite?.siteCode);
          detailDs.current?.set('prodlineId', mtModProductionLine?.prodLineId);
          detailDs.current?.set('prodLineCode', mtModProductionLine?.prodLineCode);
          detailDs.current?.set('model', mtMaterialVO?.model);
        },
      });
    } else {
      detailDs.current?.set('siteId', undefined);
      detailDs.current?.set('siteCode', undefined);
      detailDs.current?.set('prodlineId', undefined);
      detailDs.current?.set('prodLineCode', undefined);
      detailDs.current?.set('model', undefined);
    }
  };

  const handleJumpTask = () => {
    if (!detailDs.current?.get('teardownTaskId')) {
      return;
    }
    props.history.push(`/hwms/disassemble/task-execution-platform/detail/${detailDs.current?.get('teardownTaskId')}`);
  }

  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'teardown-apply-doc',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  return (
    <div className="hmes-style">
      <TarzanSpin
        dataSet={detailDs}
        spinning={saveLoading || updateLoading || submitLoading || queryLoading}
      >
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('拆解申请单')}
          backPath={pubFlag ? '' : '/hwms/product-teardown/teardown-apply-doc/list'}
        >
          {canEdit && !pubFlag && (
            <>
              <Button
                loading={saveLoading || updateLoading}
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && !pubFlag && (
            <>
              <Button
                icon="edit-o"
                disabled={!['NEW', 'REJECT', 'REVOKE'].includes(teardownApplyStatus)}
                color={ButtonColor.primary}
                onClick={handleEdit}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
              <Popconfirm
                title={intl.get(`${modelPrompt}.message.submitConfirm`).d('是否确认提交？')}
                onConfirm={handleSubmit}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <Button
                  icon="send-o"
                  disabled={!['NEW', 'REJECT', 'REVOKE'].includes(teardownApplyStatus)}
                  loading={submitLoading}
                >
                  {intl.get(`${modelPrompt}.button.submit`).d('提交')}
                </Button>
              </Popconfirm>
            </>
          )}
          <ApprovalInfoDrawer objectTypeList={['QIS_TEARDOWN_APPLY']} objectId={teardownApplyId} />
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.basicInfo`).d('申请信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="teardownApplyNum" />
                <Select name="materialLotType" onChange={handleChangeType} />
                {materialLotType === 'OWN' ? (
                  <Lov name="materialLotLov" onChange={handleChangeMaterialLot} />
                ) : (
                  <TextField name="materialLotCode" />
                )}
                <TextField name="model" />
                <TextField name="materialCode" />
                <TextField name="materialName" />
                <Select name="productFormCode" />
                <Lov name="siteLov" />
                <TextField name="prodLineCode" />
                <NumberField
                  className={styles['suffix-percent-input']}
                  name="electricVoltage"
                  suffix="%"
                />
                <Lov name="operationLov" />
                <TextField name="operationDesc" />
                <TextField name="teardownReason" />
                <Lov name="createPersonLov" />
                <DateTimePicker name="sampleDeliveryTime" />
                <Select name="teardownApplyStatus" />
                <Select name="teardownStage" />
                <Attachment {...attachmentProps} />
                <Output
                  name="teardownTaskNum"
                  style={{ color: 'rgb(8, 64, 248)', cursor: 'pointer' }}
                  renderer={({ value }) => <a onClick={handleJumpTask}>{value}</a>}
                />
                <TextArea name="teardownRemarks" colSpan={3} />
              </Form>
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(TeardownApplyDetail);
