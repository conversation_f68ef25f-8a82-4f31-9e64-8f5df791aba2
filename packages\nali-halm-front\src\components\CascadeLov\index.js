import React from 'react';
import { Modal, Input, Icon, Button, message } from 'hzero-ui';
import { isEmpty, isFunction, omit, isNil } from 'lodash';
import { Bind, Throttle } from 'lodash-decorators';
import uuid from 'uuid/v4';
import intl from 'utils/intl';
import { queryLov } from 'services/api';
import LovModal from './LovModal';
import './index.less';

const defaultRowKey = 'key';
export default class Lov extends React.Component {
  // 选中记录
  record;

  static displayName = 'Lov';

  loading = false;

  constructor(props) {
    super(props);
    this.state = {
      currentText: null,
      text: props.isInput ? props.value : props.textValue, // 文本值，暂不支持文本框
      textField: props.textField, // 映射字段名
      lov: {}, // 当前组件对应的lov视图编码
      loading: false,
      ldpData: {}, // 查询条件表单字段集，级联暂不支持设置模糊查询字段
      viewCodeList: props.viewCodeList, // 级联lov视图编码集
      lovList: [], // lov接口配置信息
    };
    this.modalRef = React.createRef();
  }

  // eslint-disable-next-line
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { currentText, text } = this.state;
    let data = {
      currentText: nextProps.textValue === currentText ? currentText : nextProps.textValue,
    };

    if (currentText && currentText !== nextProps.textValue) {
      data = {
        ...data,
        text: nextProps.textValue,
      };
    }
    if (!text && nextProps.textValue) {
      data = {
        ...data,
        text: nextProps.textValue,
      };
    }
    if (nextProps.value === null || nextProps.value === undefined) {
      data = {
        ...data,
        text: null,
      };
    }
    if (nextProps.isInput) {
      data = {
        ...data,
        text: nextProps.value,
      };
    }

    this.setState({
      ...data,
    });
  }

  @Bind()
  onSelect(record) {
    this.record = record;
  }

  // 选择关闭Modal
  @Bind()
  selectAndClose() {
    if (!this.record) {
      Modal.warning({
        title: intl.get('hzero.common.validation.atLeast').d('请至少选择一条数据'),
      });
      return false;
    }
    this.selectRecord(this.record);
    this.setState({
      modalVisible: false,
    });
  }

  // 获取并注册映射字段到表单
  getTextField() {
    const { form } = this.props;
    const { textField } = this.state;
    if (form && textField) {
      form.registerField(textField);
    }
    return textField;
  }

  // 设置lov的文本值，以及映射字段的值
  selectRecord() {
    const { isInput } = this.props;
    const { valueField: rowkey = defaultRowKey, displayField: displayName } = this.state.lov;
    // TODO: 值为 0 -0 '' 等的判断
    this.setState(
      {
        text: this.parseField(this.record, displayName),
      },
      () => {
        const { form } = this.props;
        const textField = this.getTextField();
        if (form && textField) {
          form.setFieldsValue({
            [textField]: this.parseField(this.record, displayName),
          });
        }
        // 设置额外表单值
        if (form && this.props.extSetMap) {
          this.setExtMapToForm(this.record, this.props.extSetMap, form);
        }

        if (this.props.onChange) {
          const valueField = isInput ? displayName : rowkey;
          this.props.onChange(this.parseField(this.record, valueField), this.record);
        }
        if (isFunction(this.props.onOk)) {
          this.props.onOk(this.record);
        }
        this.record = null;
      }
    );
  }

  /**
   * 设置额外表单值
   * @param {Object} record 数据对象
   * @param {String} extSetMap 额外字段映射, 可以有多个, 以逗号分隔 bankId,bankName->bankDescription
   * @param {表单对象} form 表单对象
   */
  setExtMapToForm(record, extSetMap, form) {
    const dataSet = {};
    extSetMap.split(/\s*,\s*/g).forEach(entryStr => {
      const [recordField, formFieldTmp] = entryStr.split('->');
      const formField = formFieldTmp || recordField;
      form.getFieldDecorator(formField);
      dataSet[formField] = record[recordField];
    });
    form.setFieldsValue(dataSet);
  }

  // 关闭modal
  @Bind()
  onCancel() {
    const { onCancel = e => e } = this.props;
    this.setState({
      modalVisible: false,
    });
    if (isFunction(onCancel)) {
      onCancel();
    }
    this.record = null;
  }

  showLoading(partialState = {}) {
    this.setState({
      loading: true,
      ...partialState,
    });
  }

  hideLoading() {
    this.setState({
      loading: false,
    });
  }

  // 设置modal的宽
  @Bind()
  modalWidth() {
    const { viewCodeList = [] } = this.state;
    let width = 200;
    viewCodeList.forEach(() => {
      width += 120;
    });
    return width;
  }

  @Bind()
  onSearchBtnClick() {
    const {
      disabled = false,
      onClick = e => e,
      lovOptions: { valueField: customValueField, displayField: customDisplayField } = {},
    } = this.props;
    if (disabled || this.loading) return; // 节流
    this.record = null;
    const { originTenantId: tenantId, code } = this.props;
    const { viewCodeList } = this.state;
    this.loading = true;
    this.showLoading({
      loading: true,
      modalVisible: true,
      lovModalKey: uuid(),
    });
    queryLov({ viewCode: viewCodeList[0], tenantId })
      .then(oriLov => {
        const lov = { ...oriLov };
        if (viewCodeList[0] === code && customValueField) {
          lov.valueField = customValueField;
        }
        if (viewCodeList[0] === code && customDisplayField) {
          lov.displayField = customDisplayField;
        }
        if (!isEmpty(lov)) {
          const { viewCode: hasCode, title = '' } = lov;
          if (hasCode) {
            const width = this.modalWidth();
            // 设置lov接口配置
            this.setState(
              {
                lovList: [lov],
                lov: viewCodeList[0] === code ? lov : {},
                title,
                width,
              },
              () => {
                const { modalVisible: lovModalVisible } = this.state;
                if (lovModalVisible && this.modalRef.current) {
                  this.modalRef.current.loadOnFirstVisible(); // 查询lov列表数据
                }
              }
            );
            if (isFunction(onClick)) {
              onClick();
            }
          } else {
            this.hideLoading();
            message.error(
              intl.get('hzero.common.components.lov.notification.undefined').d('值集视图未定义!')
            );
          }
        }
      })
      .finally(() => {
        this.hideLoading();
        this.loading = false;
      });
  }

  searchButton() {
    if (this.state.loading) {
      return <Icon key="search" type="loading" />;
    }
    return (
      <Icon
        key="search"
        type="search"
        onClick={this.onSearchBtnClick}
        style={{ cursor: 'pointer', color: '#666' }}
      />
    );
  }

  @Bind()
  emitEmpty() {
    const { text, lov } = this.state;
    const { form, onClear = e => e, value } = this.props;
    if (this.props.onChange) {
      const record = {};
      this.setState(
        {
          text: '',
        },
        () => {
          this.props.onChange(undefined, record);
          const textField = this.getTextField();
          if (form && textField) {
            form.setFieldsValue({
              [textField]: undefined,
            });
          }
        }
      );
    }
    // TODO: 当初次进入时的情况
    if (isFunction(onClear)) {
      const record = {
        [lov.displayField]: text,
        [lov.valueField]: value,
      };
      onClear(record);
    }
  }

  /**
   * 访问对象由字符串指定的多层属性
   * @param {Object} obj 访问的对象
   * @param {String} str 属性字符串，如 'a.b.c.d'
   */
  @Bind()
  parseField(obj, str) {
    if (/[.]/g.test(str)) {
      const arr = str.split('.');
      const newObj = obj[arr[0]];
      const newStr = arr.slice(1).join('.');
      return this.parseField(newObj, newStr);
    }
    return obj[str];
  }

  /**
   * 同步 Lov 值节流以提高性能
   * @param {String} value - Lov 组件变更值
   */
  @Bind()
  @Throttle(500)
  setValue(value) {
    if (this.props.onChange) {
      this.props.onChange(value);
    }
  }

  /**
   * 同步输入值至 Input 及 Lov
   * @param {String} value - 输入框内的值
   */
  @Bind()
  setText(value) {
    const { isInput } = this.props;
    if (isInput) {
      this.setState(
        {
          text: value,
        },
        () => {
          this.setValue(value);
        }
      );
    }
  }

  /**
   * 改变lov接口配置集
   * @param {Number} index 当前选择的级联层级索引
   * @param {String} params 当前所选节点的key值
   */
  @Bind()
  changeLovList(index, params) {
    const { lovList } = this.state;
    const {
      lovOptions: { valueField: customValueField, displayField: customDisplayField } = {},
    } = this.props;
    const { originTenantId: tenantId, code } = this.props;
    const nextIndex = index + 1;
    const { viewCodeList } = this.state;
    if (isEmpty(lovList[nextIndex])) {
      queryLov({ viewCode: viewCodeList[nextIndex], tenantId }).then(oriLov => {
        const lov = { ...oriLov };
        if (viewCodeList[nextIndex] === code && customValueField) {
          lov.valueField = customValueField;
        }
        if (viewCodeList[nextIndex] === code && customDisplayField) {
          lov.displayField = customDisplayField;
        }
        if (!isEmpty(lov)) {
          const { viewCode: hasCode } = lov;
          if (hasCode) {
            // 设置lov接口配置
            this.setState(
              {
                lovList: [...lovList, lov],
                lov: viewCodeList[nextIndex] === code ? lov : this.state.lov,
              },
              () => {
                const { modalVisible: lovModalVisible } = this.state;
                if (lovModalVisible && this.modalRef.current) {
                  this.modalRef.current.queryData(nextIndex, params); // 查询lov列表数据
                }
              }
            );
          } else {
            this.hideLoading();
            message.error(
              intl.get('hzero.common.components.lov.notification.undefined').d('值集视图未定义!')
            );
          }
        }
      });
    } else {
      const { modalVisible: lovModalVisible } = this.state;
      if (lovModalVisible && this.modalRef.current) {
        this.modalRef.current.queryData(nextIndex, params); // 查询lov列表数据
      }
    }
  }

  render() {
    const { text: stateText, ldpData = {}, viewCodeList = [], lovList } = this.state;
    const {
      form,
      value,
      textValue,
      queryParams,
      queryInputProps,
      style,
      isButton,
      isInput,
      className,
      allowClear = true,
      columnsName,
      code,
      customDefineParams,
      type, // 为Y代表C7n用来控制组件显示的值
      ...otherProps
    } = this.props;
    const textField = this.getTextField();
    let text;
    const omitProps = ['onOk', 'onCancel', 'onClick', 'onClear', 'textField', 'lovOptions'];
    if (isInput) {
      text = stateText;
      omitProps.push('onChange');
    } else if (type === 'Y') {
      const texts = stateText;
      text = texts === 0 ? 0 : texts || textValue;
    } else {
      const texts = textField ? form && form.getFieldValue(textField) : stateText;
      text = isNil(value) ? '' : texts === 0 ? 0 : texts || textValue;
    }
    const inputStyle = isButton
      ? style
      : {
          ...style,
          verticalAlign: 'middle',
          position: 'relative',
          top: -1,
        };
    const isDisabled = this.props.disabled !== undefined && !!this.props.disabled;
    const showSuffix = text && allowClear && !isButton && !isDisabled;
    const suffix = (
      <>
        <Icon key="clear" className="lov-clear" type="close" onClick={this.emitEmpty} />
        {this.searchButton()}
      </>
    );

    const lovClassNames = [className, 'lov-input'];
    if (showSuffix) {
      lovClassNames.push('lov-suffix');
    }
    if (isDisabled) {
      lovClassNames.push('lov-disabled');
    }
    const { title = '', width = 400, lov = {}, modalVisible, loading, lovModalKey } = this.state;
    const modalProps = {
      title,
      width,
      destroyOnClose: true,
      wrapClassName: 'lov-modal',
      maskClosable: false,
      onOk: this.selectAndClose,
      bodyStyle: title ? { padding: '16px' } : { padding: '56px 16px 0' },
      onCancel: this.onCancel,
      style: {
        minWidth: 400,
      },
      visible: modalVisible,
    };
    return (
      <>
        {isButton ? (
          <Button
            onClick={this.onSearchBtnClick}
            {...otherProps}
            style={style}
            className={lovClassNames.join(' ')}
          />
        ) : (
          <Input
            readOnly={!isInput}
            // addonAfter={this.searchButton()}
            value={text}
            style={inputStyle} // Lov 组件垂直居中样式，作用于 ant-input-group-wrapper
            suffix={suffix}
            onChange={e => this.setText(e.target.value)}
            {...omit(otherProps, omitProps)}
            className={lovClassNames.join(' ')}
          />
        )}
        <Modal {...modalProps}>
          <LovModal
            key={lovModalKey}
            lov={lov} // lov对应的接口配置信息
            ldpData={ldpData} // 查询条件表单
            queryParams={queryParams}
            queryInputProps={queryInputProps}
            onSelect={this.onSelect}
            onClose={this.selectAndClose}
            lovLoadLoading={loading}
            wrappedComponentRef={this.modalRef}
            viewCodeList={viewCodeList}
            code={code}
            columnsName={columnsName}
            lovList={lovList}
            onChangeLovList={this.changeLovList}
            customDefineParams={customDefineParams}
          />
        </Modal>
      </>
    );
  }
}
