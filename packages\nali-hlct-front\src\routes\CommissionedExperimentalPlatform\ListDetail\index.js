/* eslint-disable array-callback-return */
/**
 * @Description: 产线审核计划管理平台-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useState, useMemo, useCallback } from 'react';
import {
  DataSet,
  Form,
  Lov,
  Select,
  Table,
  TextField,
  DateTimePicker,
  Button,
  Menu,
  Dropdown,
  Modal,
  TextArea,
  Attachment,
  NumberField,
} from 'choerodon-ui/pro';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Popconfirm } from 'choerodon-ui';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { Content, Header } from 'components/Page';
import moment from 'moment';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import { getCurrentOrganizationId, getCurrentUser } from 'hzero-front/lib/utils/utils';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import formatterCollections from 'utils/intl/formatterCollections';
import { ExpandCardC7n } from '@components/tarzan-ui';
import { useDataSetEvent } from 'utils/hooks';
// import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import notification from 'utils/notification';
import {
  tableDetailDS,
  tableDetailLineDS,
} from '../stores/CommissionedExperimentalPlatformDetailDS';

const tenantId = getCurrentOrganizationId();
const currentUser = getCurrentUser();
const modelPrompt = 'key.hwms.front.CommissionedExperimentalPlatform';
// const statusList = ['NEW', 'REJECTED'];
// ${BASIC.TARZAN_SAMPLING}

const CommissionedExperimentalPlatformDetail = props => {
  const {
    match: {
      params: { id },
    },
    location: { state = {} },
  } = props;
  const approvalDs = new DataSet({
    autoCreate: true,
    fields: [
      {
        name: 'rejectReason',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.approval.sampleCode`).d('样本编码'),
        required: true,
        multiple: true,
      },
    ],
  });

  const { status } = state;

  const tableDetailDs = useMemo(() => new DataSet(tableDetailDS()), []);

  const tableDetailLineDs = useMemo(() => new DataSet(tableDetailLineDS()), []);

  const [editFlag, setEditFlag] = useState(false);

  const [disabledFlag, setDisabledFlag] = useState(false);

  const [lineBtnFlag, setLineBtnFlag] = useState(false);

  const [saveFlag, setSaveFlag] = useState(false);

  const [judege, setJudge] = useState(true);

  // const [delIdList, setDelIdList] = useState([]);

  const [ids, setId] = useState('');

  const [lineSelected, setLineSelected] = useState([]);

  const handleDataSetSelect = () => {
    const data = tableDetailLineDs.selected.map(ele => ele.toData());
    if (data.length > 0) {
      setLineSelected(tableDetailLineDs.selected);
    } else {
      setLineSelected([]);
    }
  };
  useDataSetEvent(tableDetailLineDs, 'select', handleDataSetSelect);
  useDataSetEvent(tableDetailLineDs, 'selectAll', handleDataSetSelect);
  useDataSetEvent(tableDetailLineDs, 'unselect', handleDataSetSelect);
  useDataSetEvent(tableDetailLineDs, 'unselectAll', handleDataSetSelect);
  useEffect(() => {
    if (status === 'create') {
      // setEditFlag(true);
      setDisabledFlag(false);
      setLineBtnFlag(false);
      setSaveFlag(false);
      const newObj = {
        // status: 'NEW',
        // creationDate: moment(moment().format('YYYY-MM-DD')),
        // creationByName: currentUser.realName,
        entrustUserId: currentUser.id,
        entrustUserName: currentUser.realName,
      };
      tableDetailDs.loadData([newObj]);
    } else if (status === 'module') {
      // setEditFlag(true);
      setDisabledFlag(false);
      setLineBtnFlag(false);
      setSaveFlag(false);
      // queryModule(prodlineReviewTmpId);
    } else {
      // setEditFlag(false);
      setDisabledFlag(true);
      setLineBtnFlag(true);
      queryDetail();
    }
  }, [status]);
  // 保存
  const handleSave = async () => {
    // 移除删除的记录
    tableDetailLineDs.forEach((record) => {
      if (record.getState('deleteFlag')) {
        tableDetailLineDs.remove(record);
      }
    })
    const headFlag = await tableDetailDs.validate();
    const lineFlag = await tableDetailLineDs.validate();
    if (headFlag) {
      if (lineFlag && tableDetailLineDs.toData().length > 0) {
        const _expectCompleteTime = moment(tableDetailDs.current?.get('expectCompleteTime')).format('X');
        const _currentTime2 = moment(new Date()).format('X');
        if (_expectCompleteTime <= _currentTime2) {
          notification.warning({
            message: intl.get(`${modelPrompt}.message.one`).d('期望完成时间早于当前时间，无法进行保存或提交，请检查！'),
          });
          return;
        }
        setJudge(!judege);
        await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-apply/save`, {
          method: 'POST',
          body: {
            ...tableDetailDs.toData()[0],
            qisEntrustApplyItemList: tableDetailLineDs.toData(),
          },
        }).then(res => {
          if (res && !res.failed) {
            notification.success({});
            setTimeout(() => {
              props.history.push({
                pathname: `/hwms/commissioned-experimental-platform/create/${res}`,
                state: {
                  status: judege,
                },
              });
            }, 500);
            setDisabledFlag(!disabledFlag);
            setLineBtnFlag(!lineBtnFlag);
            tableDetailLineDs.all.forEach(v => {
              v.setState('myEditing', disabledFlag);
            });
          } else {
            return notification.error({ message: res.message });
          }
        });
      } else {
        return notification.error({ message: intl.get(`${modelPrompt}.message.two`).d('样本编码未填写完整，请确认！') });
      }
    } else {
      return notification.error({ message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项') });
    }
  };
  // 保存提交
  const handleSaveSubmit = async () => {
    // 移除删除的记录
    tableDetailLineDs.forEach((record) => {
      if (record.getState('deleteFlag')) {
        tableDetailLineDs.remove(record);
      }
    })
    const headFlag = await tableDetailDs.validate();
    const lineFlag = await tableDetailLineDs.validate();
    if (headFlag) {
      if (lineFlag && tableDetailLineDs.toData().length > 0) {
        const _expectCompleteTime = moment(tableDetailDs.current?.get('expectCompleteTime')).format('X');
        const _currentTime2 = moment(new Date()).format('X');
        if (_expectCompleteTime <= _currentTime2) {
          notification.warning({
            message: intl.get(`${modelPrompt}.message.one`).d('期望完成时间早于当前时间，无法进行保存或提交，请检查！'),
          });
          return;
        }
        await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-apply/save/submit`, {
          method: 'POST',
          body: {
            ...tableDetailDs.toData()[0],
            qisEntrustApplyItemList: tableDetailLineDs.toData(),
          },
        }).then(res => {
          if (res && !res.failed) {
            notification.success({});
            setTimeout(() => {
              props.history.push({
                pathname: `/hwms/commissioned-experimental-platform/create/${res}`,
                state: {
                  status: judege,
                },
              });
            }, 500);
            setDisabledFlag(!disabledFlag);
            setLineBtnFlag(!lineBtnFlag);
            tableDetailLineDs.all.forEach(v => {
              v.setState('myEditing', disabledFlag);
            });
          } else {
            return notification.error({ message: res.message });
          }
        });
      } else {
        return notification.error({ message: intl.get(`${modelPrompt}.message.two`).d('样本编码未填写完整，请确认！')});
      }
    } else {
      return notification.error({ message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项') });
    }
  };

  // 正常进入查询
  const queryDetail = async () => {
    setId(id);
    await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-apply/detail/ui`, {
      method: 'GET',
      query: {
        entrustApplyId: id,
      },
    }).then(res => {
      if (res && !res.failed) {
        tableDetailDs.loadData([res]);
        const newList = [];
        if (
          res.entrustApplyStatus === 'NEW' &&
          res.entrustUserId === currentUser.id &&
          (res.oaApplyStatus === 'REJECT' || res.oaApplyStatus === 'NEW')
        ) {
          setEditFlag(false);
        } else {
          setEditFlag(true);
        }
        // if (statusList.includes(res.rows.status)) {
        //   setEditFlag(false);
        //   setSaveFlag(false);
        // } else {
        //   setEditFlag(true);
        //   setSaveFlag(true);
        // }
        if (res.qisEntrustApplyItemList && res.qisEntrustApplyItemList.length > 0) {
          res.qisEntrustApplyItemList.forEach(ele => {
            newList.push({
              ...ele,
              // responseUserObj: {
              //   responsibleEmId: ele.responsibleEmId,
              //   realname: ele.responsibleEmName,
              // },
              // responseObj: {
              //   responsibleDeptId: ele.responsibleDeptId,
              //   unitName: ele.responsibleDeptName,
              // },
            });
          });
        }
        tableDetailLineDs.loadData(newList);
      } else {
        return notification.error({ message: res.message });
      }
    });
  };

  // 样本编码编辑
  // const onInputSampleCode = async record => {
  //   approvalDs?.current?.set('rejectReason', record?.get('sampleCode')?.split(','));
  //   Modal.open({
  //     key: Modal.key(),
  //     title: `输入样本编码`,
  //     children: (
  //       <>
  //         <Form dataSet={approvalDs} columns={1}>
  //           <TextField name="rejectReason" />
  //         </Form>
  //       </>
  //     ),
  //     onOk: async () => {
  //       const vFlag = await approvalDs.validate();
  //       if (!vFlag) {
  //         return false;
  //       }
  //       console.log(approvalDs.toData()[0].rejectReason.length, record.get('sampleQty'));
  //       if (approvalDs.toData()[0].rejectReason.length !== record.get('sampleQty')) {
  //         notification.error({
  //           message: '请样本编码的个数与抽样数量是否一致',
  //         });
  //         return false;
  //       }
  //       record.set('sampleCode', approvalDs.toData()[0].rejectReason.toString());
  //     },
  //   });
  // };

  // 样本编码编辑
  const onInputSampleCodeNew = async record => {
    const qty = record.get('sampleQty');
    const codeFields = [];
    for (let i = 0; i < qty; i++) {
      codeFields.push({
        name: `code${i}`,
        type: 'string',
        label: `${intl.get(`${modelPrompt}.sampleCoding`).d('样本编码')}${i + 1}`,
        required: true,
      });
    }
    const formDs = new DataSet({
      fields: codeFields,
    });
    const lastData = record?.get('sampleCode')?.split(',') || [];

    const newObj = {};
    (lastData || []).map((item, index) => {
      newObj[`code${index}`] = item;
    });
    formDs.loadData([newObj]);
    Modal.open({
      key: Modal.key(),
      title:  intl.get(`${modelPrompt}.enter.sampleCoding`).d(`输入样本编码`),
      children: (
        <>
          <Form dataSet={formDs} columns={1}>
            {(codeFields || []).map(item => (
              <TextField name={item.name} />
            ))}
          </Form>
        </>
      ),
      onOk: async () => {
        const obj = formDs.toData()[0];
        const noRepeat = [...new Set(Object.values(obj))];
        const vFlag = await formDs.validate();
        if (!vFlag || Object.values(obj).length !== qty) {
          notification.error({ message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项') });
          return false;
        }
        if (noRepeat.length !== Object.values(obj).length) {
          notification.error({
            message: intl.get(`${modelPrompt}.message.three`).d('请检查样本编码是否重复'),
          });
          return false;
        }
        record.set('sampleCode', Object.values(obj).toString());
      },
    });
  };

  const columnLine = [
    {
      name: 'sequence',
      width: 80,
      renderer: ({ record }) => record.index + 1,
    },
    {
      name: 'itemDesc',
      // width: 120,
      align: 'center',
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'inspectMethod',
      // width: 120,
      align: 'center',
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'inspectStandard',
      // width: 180,
      align: 'center',
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'sampleQty',
      // width: 180,
      align: 'center',
      editor: record => {
        return (
          record.getState('myEditing') && (
            <NumberField
              onChange={(newValue, oldValue) => {
                record.set('sampleCode', '');
                approvalDs.loadData([]);
                // record.set('sampleQty', newValue);
              }}
            />
          )
        );
      },
    },
    {
      name: 'sampleCodeBtn',
      // width: 180,
      align: 'center',
      title: intl.get(`${modelPrompt}.sampleCoding`).d('样本编码'),
      renderer: ({ record }) => (
        // record.getState('myEditing') &&
        <>
          <a
            onClick={() => onInputSampleCodeNew(record)}
            disabled={!record.get('sampleQty') || lineBtnFlag}
          >
            {intl.get(`${modelPrompt}.button.enterSampleCoding`).d('编码输入')}
          </a>
        </>
      ),
    },
    {
      name: 'sampleCode',
      width: 200,
    },
  ];
  // 新增行
  const handleAddLine = () => {
    let maxSeq = 0;
    tableDetailLineDs.forEach((record) => {
      if (record.get('itemSequence') > maxSeq) {
        maxSeq = record.get('itemSequence');
      }
    })
    tableDetailLineDs.create({
      _status: 'create',
      // status: 'NEW',
      // deliveryTemplate: uuid(),
      itemSequence: maxSeq + 1,
    });
    tableDetailLineDs.current.setState('myEditing', true);
  };
  // 删除行
  const handleBatchDelete = () => {
    // const data = lineSelected.map(ele => ele.get('qisProdlineRevplanEId'));
    // tableDetailLineDs.remove(lineSelected);
    // setDelIdList(data);
    lineSelected.forEach((record) => {
      if (record.status === 'add') {
        tableDetailLineDs.remove(record);
      } else {
        record.setState('deleteFlag', true);
      }
    })
    tableDetailLineDs.batchUnSelect(lineSelected);
    setLineSelected([]);
  };
  // 编辑
  const handEdit = () => {
    if (editFlag) {
      return notification.error({
        message: intl.get(`${modelPrompt}.message.four`).d('仅允许编辑申请单状态为“新建”、且审批状态为“待审核”或“驳回”的申请单，请检查！'),
      });
    }
    setDisabledFlag(!disabledFlag);
    setLineBtnFlag(!lineBtnFlag);
    tableDetailLineDs.all.forEach(v => {
      v.setState('myEditing', disabledFlag);
    });
  };

  const handCancel = () => {
    if (status === 'create' || status === 'module') {
      props.history.push('/hwms/commissioned-experimental-platform');
    } else {
      queryDetail();
    }
    setDisabledFlag(!disabledFlag);
    setLineBtnFlag(!lineBtnFlag);
    tableDetailLineDs.all.forEach(v => {
      v.setState('myEditing', disabledFlag);
    });
  };

  return (
    <div style={{ height: '100%' }} className="hmes-style">
      <Header
        title={
          status === 'create'
            ? intl.get(`${modelPrompt}.title.create`).d('委托申请单创建')
            : intl.get(`${modelPrompt}.title.detail`).d('委托申请单详情')
        }
        backPath="/hwms/commissioned-experimental-platform/list"
      >
        <>
          {!lineBtnFlag && (
            <>
              <Button
                icon="save"
                color={ButtonColor.primary}
                onClick={() => handleSaveSubmit()}
                disabled={saveFlag}
              >
                {intl.get(`${modelPrompt}.flims.NewProjectPlanApplication.button.submitSave`).d('保存并提交')}
              </Button>
              <Button
                icon="save"
                color={ButtonColor.primary}
                onClick={() => handleSave()}
                disabled={saveFlag}
              >
                {intl.get('hzero.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={() => handCancel()}>
                {intl.get('hzero.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {lineBtnFlag && (
            <>
              <PermissionButton
                color={ButtonColor.primary}
                type="c7n-pro"
                permissionList={[
                  {
                    code: `${modelPrompt}.dist.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑按钮',
                  },
                ]}
                disabled={editFlag}
                onClick={() => handEdit()}
              >
                {intl.get('hzero.common.button.edit').d('编辑')}
              </PermissionButton>
            </>
          )}
          {/* <ApprovalInfoDrawer objectTypeList={['QIS_CXSH_LWS']} objectId={id} /> */}
        </>
      </Header>
      <Content>
        <ExpandCardC7n
          showExpandIcon
          title={intl.get(`${modelPrompt}.fydstms.common.model.message.basicInfo`).d('基础信息')}
        >
          <Form dataSet={tableDetailDs} columns={3} labelWidth={110} disabled={disabledFlag}>
            <TextField name="entrustApplyCode" />
            <Lov name="siteObj" />
            <Lov name="materialObj" />
            <TextField name="materialName" />
            <Lov name="unitObj" />
            <TextField name="entrustUserName" />
            <NumberField name="sampleQty" precision={0} />
            <TextField name="sampleUom" />
            <DateTimePicker name="expectCompleteTime" />
            <TextArea name="reason" newLine colSpan={3} />
            <Select name="sampleDisposalMethod" />
            <Attachment name="relatedFileUuid" />
            <TextArea name="remark" newLine colSpan={3} />

            {/* <Attachment readOnly {...panelDetailEnclosure} /> */}
          </Form>
        </ExpandCardC7n>
        {/* <ExpandCardC7n
          showExpandIcon
          title={intl.get('fydstms.common.model.message.ListTable').d('产线审核要素')}
        > */}
        <div>
          <Table
            customizable
            customizedCode="CommissionedExperimentalPlatformTableDetailLine"
            dataSet={tableDetailLineDs}
            columns={columnLine}
            buttons={[
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="add"
                funcType="flat"
                shape="circle"
                size="small"
                disabled={lineBtnFlag}
                permissionList={[
                  {
                    code: `key.hwms.front.CommissionedExperimentalPlatform.button.addLine`,
                    type: 'button',
                    meaning: '新建按钮',
                  },
                ]}
                onClick={() => handleAddLine()}
              >
                {intl.get(`${modelPrompt}.flims.NewProjectPlanApplication.button.addLine`).d('新建')}
              </PermissionButton>,
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => handleBatchDelete()}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <PermissionButton
                  type="c7n-pro"
                  color={ButtonColor.primary}
                  icon="delete_black-o"
                  funcType="flat"
                  shape="circle"
                  size="small"
                  permissionList={[
                    {
                      code: `key.hwms.front.CommissionedExperimentalPlatform.button.deleteLine`,
                      type: 'button',
                      meaning: '删除按钮',
                    },
                  ]}
                  disabled={lineBtnFlag || lineSelected.length < 1}
                >
                  {intl.get('hzero.common.button.delete').d('删除')}
                </PermissionButton>
              </Popconfirm>,
            ]}
            queryBar={TableQueryBarType.none}
            queryFieldsLimit={3}
            pagination={{ showPager: true }}
            queryBarProps={{ autoQueryAfterReset: false }}
            rowHeight={34}
            border
            filter={(record) => !record.getState('deleteFlag')}
          />
        </div>
        {/* </ExpandCardC7n> */}
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: [
    'tarzan.common',
    'hzero.common',
    'key.hwms.front.CommissionedExperimentalPlatform',
  ],
})(CommissionedExperimentalPlatformDetail);
