/**
 * @Description: 量具汇总查询界面
 */

import React, { useMemo} from 'react';
import { Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import {ColumnAlign, ColumnLock, TableQueryBarType} from 'choerodon-ui/pro/lib/table/enum';
import { Collapse, Tag} from "choerodon-ui";
import intl from "utils/intl";
import {useDataSetEvent} from "utils/hooks";

const modelPrompt = 'tarzan.inspectExecute.MeasureHavePlatform';
const { Panel } = Collapse;

const ApplyTab = props => {
  const { headDs, lineDs, history  } = props;

  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('applicationDocId'));
    } else {
      queryLineTable(null);
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = applicationDocId => {
    lineDs.loadData([]);
    if (applicationDocId) {
      lineDs.setQueryParameter('applicationDocId', applicationDocId);
      lineDs.query();
    }
  };

  const renderStatusTag = (value, record, name) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'IN_APPROVAL':
        className = 'orange';
        break;
      case 'REJECTED':
        className = 'red';
        break;
      case 'RELEASED':
        className = 'purple';
        break;
      case 'COMPLETED':
        className = 'green';
        break;
      case 'NEW':
        className = 'blue';
        break;
      case 'CANCEL':
        className = 'gray';
        break;
      default:
        className = 'yellow';
    }
    return <Tag color={className}>{record!.getField(name)!.getText()}</Tag>;
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'applicationDocNum',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hmes/measure-have/platform/dist/${record!.get(
                    'applicationDocId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'applicationDocStatus',
        width: 130,
        renderer: ({ value, record }) => renderStatusTag(value, record, 'applicationDocStatus'),
      },
      { name: 'siteName', width: 180, lock: ColumnLock.left },
      { name: 'docType' },
      { name: 'creationDate', align: ColumnAlign.center, width: 150 },
      { name: 'createdByName' },
      { name: 'departmentName' },
      { name: 'remark' },
      { name: 'reviewByName' },
      { name: 'reviewDate' },
      { name: 'rejectReason' },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'toolCode', width: 180, lock: ColumnLock.left },
      { name: 'speciesName' },
      { name: 'modelCode', align: ColumnAlign.center, width: 150 },
      {
        name: 'modelName',
        align: ColumnAlign.center,
        width: 100,
      },
      {
        name: 'inspectDocNum',
        width: 150,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hmes/measure-have/platform/inspect/${record!.get(
                    'verificationMethod')}/${record!.get(
                      'inspectDocId',
                    )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'inspectDocStatus' },
      { name: 'inspectPlace' },
      { name: 'inspectResult' },
      { name: 'appearanceResult' },
      { name: 'inspectorName' },
      { name: 'completionTime', align: ColumnAlign.center, width: 150 },
      { name: 'remark' },
      { name: 'inspectReportUuid', width: 150, lock: ColumnLock.right },
    ],
    [],
  );

  return (
    <div>
      <Table
        queryBar={TableQueryBarType.filterBar}
        queryBarProps={{
          fuzzyQuery: false,
        }}
        dataSet={headDs}
        columns={headColumns}
        searchCode="msPlatform1"
        customizedCode="msPlatform-listHeader"
        onRow={({ record }) => ({
          onClick: () => queryLineTable(record?.get('applicationDocId')),
        })}
      />
      <Collapse bordered={false} defaultActiveKey={['line']}>
        <Panel key="line" header={intl.get(`${modelPrompt}.title.line`).d('行信息')}>
          <Table
            dataSet={lineDs}
            columns={lineColumns}
            customizedCode="msPlatform-listLine"
          />
        </Panel>
      </Collapse>
    </div>
  );
};

export default ApplyTab;
