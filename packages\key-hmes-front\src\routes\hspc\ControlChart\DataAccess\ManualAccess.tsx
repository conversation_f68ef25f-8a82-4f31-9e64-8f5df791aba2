/**
 * @Description: 控制控制图-自动接入
 * @Author: <<EMAIL>>
 * @Date: 2021-12-01 10:00:27
 * @LastEditTime: 2022-11-09 14:58:11
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useRef } from 'react';
import { TextField, NumberField, Tooltip } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import uuid from 'uuid/v4';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';

import VirtualizedEditTable, {
  VirtualizedEditTableProps,
  VirtualizedEditTableBlock,
} from '@/components/VirtualizedEditTable';
import reduce from '@/assets/icons/reduce.svg';
import addFill from '@/assets/icons/add-fill.svg';
import success from '@/assets/icons/success.svg';
import GraphicChart from '../components/GraphicChart';
import {
  FetchControlChartManaulAccessGraphicData,
  SaveontrolChartManaulAccessGraphicData,
} from '../services';
import styles from './manualAccess.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hspc.controlChartMaintain';

const ControlChartDisplay = props => {
  const { id } = props.match.params;
  const tableRef = useRef(null);
  const vTable = useRef<VirtualizedEditTable | null>(null);

  const [chartUuid] = useState(uuid());
  const fetchControlChartManaulAccessGraphicData = useRequest(
    FetchControlChartManaulAccessGraphicData(),
    { manual: true },
  );
  const saveontrolChartManaulAccessGraphicData = useRequest(
    SaveontrolChartManaulAccessGraphicData(),
    { manual: true },
  );

  useEffect(() => {
    if (tableRef.current) {
      const vTableProps: VirtualizedEditTableProps = {
        container: tableRef.current!,
        dataSource: null,
        differRender: true,
        cellRenderer: selfCellRenderer,
      };
      vTable.current = new VirtualizedEditTable(vTableProps);
    }
  }, [tableRef]);

  const selfCellRenderer = (dataSource, { columnIndex, key, rowIndex, style }) => {
    if (!dataSource || !dataSource.length) {
      return;
    }
    const { col, row, value, type }: VirtualizedEditTableBlock = dataSource[columnIndex][rowIndex];
    const currentStyle = {
      ...style,
    };
    if (col === 1) {
      currentStyle.borderLeftWidth = '0';
    }
    if (row !== 1) {
      // 不是第一行的数据，没有上边框
      currentStyle.borderTopWidth = '0';
    }
    if (!type) {
      currentStyle.padding = '4px 8px';
      currentStyle.lineHeight = `${Number(currentStyle.height) - 8}px`;
      return (
        <div key={key} className="hcm-virtualized-table-block" style={currentStyle}>
          {value}
        </div>
      );
    }
    if (type === 'string') {
      return (
        <div key={key} className="hcm-virtualized-table-block" style={currentStyle}>
          <TextField
            className="hcm-virtualized-table-input"
            defaultValue={value}
            placeholder="请输入"
            onChange={inputValue => {
              vTable!.current!.modifyValue(col, row, inputValue);
            }}
          />
        </div>
      );
    }
    if (type === 'number') {
      return (
        <div key={key} className="hcm-virtualized-table-block" style={currentStyle}>
          <NumberField
            className="hcm-virtualized-table-input"
            defaultValue={value}
            placeholder="请输入"
            onChange={inputValue => {
              vTable!.current!.modifyValue(col, row, inputValue);
            }}
          />
        </div>
      );
    }
  };

  useEffect(() => {
    if (!id) {
      return;
    }
    getChartData();
  }, [id]);

  const getNewCol = (res?, initTableList?) => {
    const { tableStructure, subgroupSize } =
      res || fetchControlChartManaulAccessGraphicData.data || {};
    if (!tableStructure || !subgroupSize) {
      return;
    }
    const rowNumber = Object.values(tableStructure).length;
    const addTableList: VirtualizedEditTableBlock[][] = [];
    if (rowNumber > 2) {
      // 按列分析，每次增加 1 列
      const newCol: VirtualizedEditTableBlock[] = [];
      for (let i = 1; i <= rowNumber; i++) {
        const newBlock: VirtualizedEditTableBlock = {
          col: initTableList
            ? initTableList.length + 1
            : (vTable.current?.DataSource || []).length + 1,
          row: i,
          value: null,
          type: i === 1 ? 'string' : 'number',
        };
        newCol.push(newBlock);
      }
      addTableList.push(newCol);
    } else {
      // 按行分析
      for (let index = 1; index <= subgroupSize; index++) {
        const newCol: VirtualizedEditTableBlock[] = [];
        for (let i = 1; i <= rowNumber; i++) {
          const newBlock: VirtualizedEditTableBlock = {
            col: initTableList
              ? initTableList.length + index
              : (vTable.current?.DataSource || []).length + index,
            row: i,
            value: null,
            type: i === 1 ? 'string' : 'number',
          };
          newCol.push(newBlock);
        }
        addTableList.push(newCol);
      }
    }
    return addTableList;
  };

  const getChartData = () => {
    fetchControlChartManaulAccessGraphicData.run({
      params: {
        controlId: id,
      },
      onSuccess: res => {
        const { tableStructure } = res;
        const initTableList: VirtualizedEditTableBlock[][] | null = [[]];
        Object.keys(tableStructure).forEach(key => {
          const element = tableStructure[key];
          initTableList[0].push({
            col: 1,
            row: Number(key),
            value: element,
          });
        });
        const newCol = getNewCol(res, initTableList);
        if (newCol) {
          initTableList.push(...newCol);
        }
        if (vTable.current) {
          vTable.current.refresh(initTableList);
        }
      },
    });
  };

  const handleAddColumn = () => {
    if (vTable.current?.DataSource) {
      const newCol = getNewCol();
      if (newCol) {
        const newList = JSON.parse(JSON.stringify(vTable.current.DataSource));
        newList.push(...newCol);
        vTable.current.refresh(newList);
      }
    }
  };

  const handleDeleteColumn = () => {
    // TODO  有点问题，这里的删除列后续还需要修改
    const rowNumber = Object.values(fetchControlChartManaulAccessGraphicData.data.tableStructure)
      .length;
    if (rowNumber > 2) {
      // 按列分析
      if (!vTable.current?.DataSource || vTable.current.DataSource.length === 2) {
        notification.warning({
          message: '已是最小子组',
        });
        return;
      }
      if (vTable.current?.DataSource) {
        const newList = JSON.parse(JSON.stringify(vTable.current.DataSource));
        newList.pop();
        vTable.current.refresh(newList);
      }
    } else {
      if (
        vTable.current?.DataSource?.length ===
        fetchControlChartManaulAccessGraphicData.data.subgroupSize + 1
      ) {
        notification.warning({
          message: '已是最小子组',
        });
        return;
      }
      const newList = JSON.parse(JSON.stringify(vTable?.current?.DataSource));
      for (
        let index = 0;
        index < fetchControlChartManaulAccessGraphicData.data.subgroupSize;
        index++
      ) {
        newList.pop();
      }
      vTable!.current!.refresh(newList);
    }
  };

  const handleSaveTable = () => {
    if (!showSaveIconFlag()) {
      notification.warning({
        message: '请填写所有数据',
      });
      return;
    }
    const initArr: VirtualizedEditTableBlock[] = [];
    const _tableList = JSON.parse(JSON.stringify(vTable.current!.DataSource!));
    _tableList.shift();
    const currentTableList: VirtualizedEditTableBlock[] = initArr.concat(..._tableList);
    saveontrolChartManaulAccessGraphicData.run({
      queryParams: {
        controlId: id,
      },
      params: currentTableList.map(item => ({
        dataColumn: item.col,
        dataRow: item.row,
        dataValue: item.value,
      })),
      onSuccess: () => {
        getChartData();
      },
    });
  };

  const showSaveIconFlag = () => {
    if (vTable.current?.DataSource) {
      let canSaveFlag = true;
      const currentTableList = vTable.current.DataSource;
      currentTableList.forEach(col => {
        col.forEach(block => {
          if (!block.value && `${block.value}` !== '0') {
            canSaveFlag = false;
          }
        });
      });
      return canSaveFlag;
    }
  };

  return (
    <div className="hmes-style">
      <Content style={{ height: '100%' }}>
        <Collapse bordered={false} defaultActiveKey={['information']} trigger="icon">
          <Panel
            header={
              <div className={styles['chart-card-title']}>
                <div className={styles['analys-code']}>
                  {`${intl
                    .get(`${modelPrompt}.controlChartCode`)
                    .d('控制控制图编码')}: ${fetchControlChartManaulAccessGraphicData.data
                    ?.controlCode || ''}`}
                </div>
                <div className={styles['analys-code']}>
                  {`${intl
                    .get(`${modelPrompt}.controlChartDesc`)
                    .d('控制控制图描述')}: ${fetchControlChartManaulAccessGraphicData.data
                    ?.controlDesc || ''}`}
                </div>
              </div>
            }
            key="information"
            showArrow={false}
          >
            <div ref={tableRef} />
            <div className={styles['hcm-operation-column']}>
              <Tooltip title={intl.get(`${modelPrompt}.addColumn`).d('添加列')}>
                <img draggable="false" src={addFill} alt="" onClick={handleAddColumn} />
              </Tooltip>
              <Tooltip title={intl.get(`${modelPrompt}.deleteColumn`).d('删除列')}>
                <img draggable="false" src={reduce} alt="" onClick={handleDeleteColumn} />
              </Tooltip>
              <Tooltip title={intl.get('tarzan.common.button.save').d('保存')}>
                <img
                  draggable="false"
                  style={{ width: 16, marginLeft: 2 }}
                  src={success}
                  alt=""
                  onClick={handleSaveTable}
                />
              </Tooltip>
            </div>
          </Panel>
        </Collapse>
        {fetchControlChartManaulAccessGraphicData.data ? (
          <GraphicChart
            chartData={fetchControlChartManaulAccessGraphicData.data}
            loading={fetchControlChartManaulAccessGraphicData.loading}
            chartUuid={chartUuid}
          />
        ) : null}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hspc.controlChartMaintain', 'tarzan.hspc.chartInfo', 'tarzan.common'],
})(ControlChartDisplay);
