/**
 * @Description: 检验平台-service
 * @Author: <<EMAIL>>
 * @Date: 2023-02-13 10:34:16
 * @LastEditTime: 2023-05-18 16:59:10
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

const endUrl= ``

// 主界面-领取检验任务
export function AssignTask() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/task/assign/ui`,
    method: 'POST',
  };
}

// 主界面-撤销检验任务
export function RevokeTask() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/task/assign/cancel/ui`,
    method: 'POST',
  };
}

// 主界面-取消检验任务
export function CancelTask() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/task/cancel/ui`,
    method: 'POST',
  };
}

// 单据信息-查询
export function FetchDocInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/doc/detail/ui`,
    method: 'GET',
  };
}

// 检验对象明细-查询
export function FetchInspectObj() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/object/detail/ui`,
    method: 'GET',
  };
}

// 检验对象明细-添加检验对象
export function AddInspectObj() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/object/task/act/add/ui`,
    method: 'POST',
  };
}

// 检验对象明细-保存
export function SaveInspectObj() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/object/task/act/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_OBJECT.LIST`,
    method: 'POST',
  };
}

// 检验项目信息-查询
export function FetchInspectInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/detail/ui`,
    method: 'GET',
  };
}

// 检验项目信息-开始检验
export function StartInspectInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/task/start/ui`,
    method: 'POST',
  };
}

// 检验项目信息-保存
export function SaveInspectInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/detail/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.HEAD,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.LINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.VALUE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.BLINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.QLINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_OBJECT.LIST`,
    method: 'POST',
  };
}

// 修改保存
export function EditingSaveInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/detail/complete/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.HEAD,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.LINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.VALUE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.BLINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.QLINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_OBJECT.LIST`,
    method: 'POST',
  };
}

// 检验项目信息-保存并提交
export function SubmitInspectInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/detail/submit/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.HEAD,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.LINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.VALUE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.BLINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.QLINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_OBJECT.LIST`,
    method: 'POST',
  };
}

// 检验项目信息-删除
export function ItemDetailDelete() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/detail/delete?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.HEAD,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.LINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.VALUE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.BLINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.QLINE,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_OBJECT.LIST`,
    method: 'POST',
  };
}

// 检验项目信息-获取条码或单据信息
export function ScanBarcode() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot/lot/eo/ui`,
    method: 'GET',
  };
}

// 检验项目信息-扫描检验对象传输
export function ScanInspectObj() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/detail/scan/object/ui`,
    method: 'POST',
  };
}

// 检验项目信息-复制检验值
export function CopyInspectObj() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/detail/copy/ui`,
    method: 'POST',
  };
}

// 检验项目信息-获取抽样数量
export function FetchSamplingQty() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-sampling-method/limit/sample-size/query`,
    method: 'POST',
  };
}

// 检验项目信息-获取计算公式所得值
export function GetCalculateFormula() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/formula-calculation`,
    method: 'POST',
  };
}

// 检验不良记录-保存
export function SaveNcRecord() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/nc/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.NCRECORD`,
    method: 'POST',
  };
}

// 部分处置-保存
export function SavePartDisposal() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/part/disposal/save/ui`,
    method: 'POST',
  };
}

// 发起不良审批
export function NcReview() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/detail/nc-review/ui`,
    method: 'POST',
  };
}

// 处置
export function NcReviewSubmit() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/item/detail/nc-review/submit/ui`,
    method: 'POST',
  };
}

// 不良记录默认值处理
export function NcDefaultType() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-platform-app/item/ncRecord/app`,
    method: 'POST',
  };
}

// 主界面-批量合格
export function BatchQualified() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-task/batch/approval`,
    method: 'POST',
  };
}

// 检验对象明细-查询
export function GetInspectAttachMent(params) {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}mt-inspect-task/roHS?materialId=${params.materialId}`,
    method: 'GET',
  };
}


/**
 * 发起不良评审校验
 * @function initiateNcReviewCalibration
 * @returns {object} fetch Promise
 */
export function initiateNcReviewCalibration(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/nc/review/batch/validate/for/ui`,
    method: 'POST',
  };
}

/**
 * 发起不良评审-保存
 * @function initiateNcReviewSave
 * @returns {object} fetch Promise
 */
export function initiateNcReviewSave(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/nc/review/batch/call/oa/for/ui`,
    method: 'POST',
  };
}
