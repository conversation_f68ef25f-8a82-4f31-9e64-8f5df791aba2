/**
 * @Description: MSA分析管理平台-偏倚图表界面
 * @Author: <EMAIL>
 * @Date: 2023/8/24 15:42
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, TextField, TextArea } from 'choerodon-ui/pro';
import { Collapse, Row, Col } from 'choerodon-ui';
import echarts from 'echarts';
import {
  max as lodashMax,
  min as lodashMin,
  floor as lodashFloor,
  ceil as lodashCeil,
} from 'lodash';
import intl from 'utils/intl';
import { getProductWithPrecision } from '@/utils';
import Table from './TableComponent';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const { Panel } = Collapse;
let xBarRChart; // xBarR图chart
let histogramChart; // 直方图chart

const DeflectiveGraphic = ({ dataSoure, analyseResultDs }) => {
  const [pageInit, setPageInit] = useState(false);
  const xBarRChartRef = useRef(null);
  const histogramChartRef = useRef(null);

  useEffect(() => {
    if (!dataSoure.data?.length) {
      return;
    }
    if (!pageInit) {
      xBarRChart = echarts.init(xBarRChartRef.current);
      histogramChart = echarts.init(histogramChartRef.current);
      window.onresize = () => {
        setTimeout(() => {
          xBarRChart.resize();
          histogramChart.resize();
        }, 300);
      };
    }
    handleInitXBarRChart(dataSoure);
    handleInitHistogramChart(dataSoure);
  }, [dataSoure?.data, dataSoure?.tableInfo]);

  const getRulePercision = (dataRule: any) => {
    const { centerLine, upperControlLimit, lowerControlLimit } = dataRule;
    const data = [centerLine, upperControlLimit, lowerControlLimit];
    let maxDigits;
    data.forEach((item: number) => {
      const _digits = item.toString().split('.')[1]?.length || 0;
      if (_digits > maxDigits || !maxDigits) {
        maxDigits = _digits;
      }
    });
    return maxDigits;
  };

  const handleInitXBarRChart = dataSource => {
    const {
      data,
      mainChartRule,
      secondaryChartRule,
      mainChartMax,
      mainChartMin,
      secondChartMax,
      secondChartMin,
      callipers,
      mainData,
      secondData,
    } = handleFormatXBarData(dataSource);
    const formatLabel = {
      formatter: value => {
        return `${value.name}:${value.data.yAxis}`;
      },
      color: '#000',
      fontSize: 10,
    };
    const option: any = {
      backgroundColor: '#fff',
      title: [
        {
          text: intl.get(`${modelPrompt}.title.Xbar`).d('XBar控制图'),
          left: 'center',
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
        {
          top: '45%',
          text: intl.get(`${modelPrompt}.title.r`).d('R控制图'),
          left: 'center',
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
      ],
      dataZoom:
        data.length > 50
          ? [
              {
                realtime: true,
                startValue: 0,
                endValue: data.length > 1000 ? 1000 : data.length, // 要根据数据量改
                minValueSpan: 50,
                maxValueSpan: data.length > 1000 ? 1000 : data.length,
                xAxisIndex: [0, 1],
              },
              {
                type: 'inside',
                realtime: true,
                startValue: 0,
                endValue: data.length > 1000 ? 1000 : data.length, // 要根据数据量改
                minValueSpan: 50,
                maxValueSpan: data.length > 1000 ? 1000 : data.length,
                xAxisIndex: [0, 1],
              },
            ]
          : [],
      yAxis: [
        {
          type: 'value',
          max: lodashCeil(lodashMax([mainChartRule.upperControlLimit, mainChartMax]) * 100) / 100,
          min: lodashFloor(lodashMin([mainChartRule.lowerControlLimit, mainChartMin]) * 100) / 100,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.03)',
            },
          },
        },
        {
          gridIndex: 1,
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.03)',
            },
          },
          max:
            lodashCeil(lodashMax([secondaryChartRule.upperControlLimit, secondChartMax]) * 100) /
            100,
          min:
            lodashFloor(lodashMin([secondaryChartRule.lowerControlLimit, secondChartMin]) * 100) /
            100,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: callipers,
          axisTick: {
            show: true,
            alignWithLabel: true,
          },
        },
        {
          gridIndex: 1,
          type: 'category',
          data: callipers,
          axisTick: {
            show: true,
            alignWithLabel: true,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          animation: false,
        },
        borderColor: '#fff',
        backgroundColor: '#fff',
        extraCssText: 'box-shadow: 5px 5px 5px #888888;',
        textStyle: {
          color: '#4c4c4c',
        },
        formatter: '{b0}<br />{a0}: {c0}<br />{a1}: {c1}',
      },
      axisPointer: {
        link: { xAxisIndex: 'all' },
      },
      grid: [
        {
          id: 'main',
          left: 70,
          right: 100,
          top: '5%',
          height: '35%',
        },
        {
          id: 'second',
          left: 70,
          right: 100,
          top: '50%',
          height: '35%',
        },
      ],
      series: [
        {
          name: intl.get(`${modelPrompt}.title.average`).d('均值'),
          type: 'line',
          symbol: 'circle',
          symbolSize: 6,
          hoverAnimation: false,
          color: '#1890ff',
          markLine: {
            silent: true,
            symbol: 'none',
            precision: getRulePercision(mainChartRule),
            data: [
              {
                name: 'CL',
                yAxis: lodashFloor(mainChartRule.centerLine, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#b8b8b8',
                  width: 1.2,
                },
              },
              {
                name: 'UCL',
                yAxis: lodashFloor(mainChartRule.upperControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
              {
                name: 'LCL',
                yAxis: lodashFloor(mainChartRule.lowerControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
            ],
          },
          data: mainData,
        },
        {
          name: intl.get(`${modelPrompt}.title.range`).d('极差'),
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 1,
          symbol: 'circle',
          symbolSize: 6,
          hoverAnimation: false,
          color: '#1890ff',
          markLine: {
            silent: true,
            symbol: 'none',
            precision: getRulePercision(secondaryChartRule),
            data: [
              {
                name: 'CL',
                yAxis: lodashFloor(secondaryChartRule.centerLine, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#b8b8b8',
                  width: 1.2,
                },
              },
              {
                name: 'UCL',
                yAxis: lodashFloor(secondaryChartRule.upperControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
              {
                name: 'LCL',
                yAxis: lodashFloor(secondaryChartRule.lowerControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
            ],
          },
          data: secondData,
        },
      ],
    };
    xBarRChart.setOption(option, true);
  };

  const handleFormatXBarData = dataSource => {
    const { data, xBarChartRule, rChartRule } = dataSource;
    const dataIdList: any[] = []; // 数据Id列表
    const mainData: any[] = []; // 主图数据
    const secondData: any[] = []; // 次图数据
    const xTickLabelList: any[] = []; // x轴坐标名称
    let mainChartMax;
    let mainChartMin;
    let secondChartMax;
    let secondChartMin;
    if (data) {
      data.forEach(item => {
        if (item.xBarStatsValue > mainChartMax || !mainChartMax) {
          mainChartMax = item.xBarStatsValue;
        }
        if (item.xBarStatsValue < mainChartMin || !mainChartMin) {
          mainChartMin = item.xBarStatsValue;
        }
        if (item.rStatsValue > secondChartMax || secondChartMax === undefined) {
          secondChartMax = item.rStatsValue;
        }
        if (item.rStatsValue < secondChartMin || secondChartMin === undefined) {
          secondChartMin = item.rStatsValue;
        }
        dataIdList.push(item.subgroupIndex);
        mainData.push(item.xBarStatsValue);
        secondData.push(item.rStatsValue);
        xTickLabelList.push(item.subgroupIndex);
      });
    }
    return {
      data,
      mainChartRule: xBarChartRule,
      secondaryChartRule: rChartRule,
      dataIdList,
      mainData,
      secondData,
      callipers: xTickLabelList,
      mainChartMax,
      mainChartMin,
      secondChartMax,
      secondChartMin,
    };
  };

  const handleInitHistogramChart = dataSoure => {
    const { xTickLabelList, yTickData } = handleFormatHistogramData(dataSoure);
    const option: any = {
      title: [
        {
          text: intl.get(`${modelPrompt}.title.measureDataHistogram`).d('测量值直方图'),
          left: 'center',
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: xTickLabelList,
        axisLabel: { rotate: 38 },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: yTickData,
          type: 'bar',
          itemStyle: { color: 'rgb(27, 184, 206)' },
        },
      ],
    };
    histogramChart.setOption(option, true);
    // 初始化chart事件绑定
    if (!pageInit) {
      setPageInit(true);
    }
  };

  const handleFormatHistogramData = dataSource => {
    const {
      allValueList = [], // 数据点集合
      distance, // 区间间距
      starting, // 区间起始点
      pillarNum, // 区间个数
    } = dataSource?.biasHistogramChartInfo;
    const xTickLabelList: string[] = []; // x轴坐标集合
    for (let i = 0; i < pillarNum; i++) {
      const leftValue = getProductWithPrecision(
        starting,
        getProductWithPrecision(i, distance),
        6,
        'add',
      );
      const rightValue = getProductWithPrecision(leftValue, distance, 6, 'add');
      const displayRangeValue = `[${leftValue},${rightValue})`;
      xTickLabelList.push(displayRangeValue);
    }
    const yTickData: number[] = countPointsInRange(starting, distance, allValueList); // 直方图数据
    return {
      xTickLabelList,
      yTickData,
    };
  };

  const countPointsInRange = (x, distance, numbers) => {
    // 创建一个长度为10的数组，用于记录每个区间内的点的个数
    const counts = new Array(10).fill(0);

    // 遍历传入的数据点数组
    for (let i = 0; i < numbers.length; i++) {
      const num = numbers[i];
      // 判断当前随机数所在的区间
      const rangeIndex = Math.floor((num - x) / distance);
      // 增加对应区间的点的个数
      counts[rangeIndex]++;
    }

    return counts;
  };

  const bisaColumn = [
    {
      name: 'barValue',
      title: intl.get(`${modelPrompt}.barValue`).d('测量值平均值'),
    },
    {
      name: 'biasValue',
      title: intl.get(`${modelPrompt}.biasValue`).d('偏倚均值'),
    },
    {
      name: 'ev',
      title: intl.get(`${modelPrompt}.repetitiveStandardDeviation`).d('重复性标准差σr'),
    },
    {
      name: 'evPercentValue',
      title: intl.get(`${modelPrompt}.evPercentValue`).d('重复性%EV'),
    },
    {
      name: 'evSigma',
      title: intl.get(`${modelPrompt}.evSigma`).d('均值标准差σb'),
    },
    {
      name: 'biasStats',
      title: intl.get(`${modelPrompt}.biasStats`).d('偏倚的t统计量'),
    },
    {
      name: 'criticalValue',
      title: intl.get(`${modelPrompt}.criticalValue`).d('临界值'),
    },
    {
      name: 'confidenceInterval',
      title: intl.get(`${modelPrompt}.confidenceInterval`).d('偏倚值的1-α置信区间'),
    },
  ];

  const RenderHelp = () => {
    const info = intl.get(`${modelPrompt}.deflective.help`).d('1、所有的子组均值及子组极差均落在各自控制图的上下控制限之内，说明稳定性良好；<br />2、直方图的P值大于0.05，说明直方图呈正态分布；<br />3、若重复性EV%<10%，重复性可以被接受；10%<=%EV<=30%，重复性可以条件接受；若%EV>30%，重复性不能接受；<br />4、偏倚的t统计量是否小于临界值；<br />5、0需要落在95%置信区间内；<br />同时满足以上条件，证明测量系统无偏倚');
    const descriptionList = info.split('<br />');
    return (
      <div>
        {descriptionList.map(item => (
          <p>{item}</p>
        ))}
      </div>
    );
  };

  return (
    <div>
      {Boolean(dataSoure?.tableInfo?.length) && (
        <Collapse bordered={false} defaultActiveKey={['analyseContent']}>
          <Panel key="analyseContent" header={intl.get(`${modelPrompt}.analyseContent`).d('分析内容')}>
            <div
              id="bias-xBarR-chart"
              ref={xBarRChartRef}
              style={{
                height: '500px',
              }}
            />
            <Row>
              <Col span={8}>
                <div
                  id="bias-graphic-chart"
                  ref={histogramChartRef}
                  style={{
                    height: '300px',
                  }}
                />
              </Col>
              <Col span={16}>
                <Table data={[{ ...dataSoure.biasTableInfo }]} columns={bisaColumn} />
                <Form labelWidth={100} disabled dataSet={analyseResultDs}>
                  <TextField name="msaResult"  help={<RenderHelp />} />
                  <TextArea name="msaConclusion" rows={7} />
                </Form>
              </Col>
            </Row>
          </Panel>
        </Collapse>
      )}
    </div>
  );
};

export default DeflectiveGraphic;
