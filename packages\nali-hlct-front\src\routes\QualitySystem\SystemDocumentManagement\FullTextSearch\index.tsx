/*
 * @Description: 体系文件管理-高级检索
 * @Author: <<EMAIL>>
 * @Date: 2023-10-20 11:27:43
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-19 20:38:57
 */
import React, { useMemo, useState } from 'react';
import { Content, Header } from 'components/Page';
import {
  Row,
  Col,
  DataSet,
  Form,
  TextField,
  Button,
  Radio,
  Pagination,
  Table,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Tag, Menu } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { observer } from 'mobx-react';
import { openTab } from 'utils/menuTab';
import { HZERO_FILE } from 'utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import { TarzanSpin } from '@components/tarzan-ui';
import { useDataSetEvent } from 'utils/hooks';
import { searchFormDS, searchTableDS } from '../stores/FullTextSearchDS';
import styles from './index.module.less';

const tenantId = getCurrentOrganizationId();
const { Item } = Menu;

const modelPrompt = 'tarzan.qms.systemDocumentManagement';
interface PaginationInfo {
  pageSize: number;
  currentPage: number;
  total: number;
}

const SearchResultComponent = observer(({ dataSet }) => {
  const formField = [
    'fileCode',
    'fileName',
    'editedDepartmentName',
    'editedDepartmentParentName',
    'editedByRealName',
    'editedDate',
    'publishDate',
    'version',
    'affliatedProcess',
    'fileLevel',
  ];

  const renderFileStatusTag = record => {
    let className;
    switch (record?.get('fileStatus')) {
      case 'STAGING':
        className = 'green';
        break;
      case 'UNPUBLISHED':
        className = 'cyan';
        break;
      case 'PUBLISHED':
        className = 'blue';
        break;
      case 'DISCARD_APPROVING':
      case 'PUBLISH_APPROVING':
        className = 'orange';
        break;
      case 'CANCEL':
      case 'DISCARD':
        className = 'gray';
        break;
      default:
        break;
    }
    return <Tag color={className}>{record!.getField('fileStatus')!.getText()}</Tag>;
  };

  const handleJumpToDetail = id => {
    openTab({
      key: `/hwms/system-document-management/detail/${id}`,
      title: intl.get(`${modelPrompt}.fullText`).d('体系文件管理'),
    });
  };

  const handlePreviewFile = record => {
    window.open(
      ''
        .concat(HZERO_FILE, '/v1/')
        .concat(tenantId, '/file-preview/by-url?url=')
        .concat(encodeURIComponent(record?.get('qisEsFileList')[0]?.fileUrl), '&bucketName=qms')
        .concat(
          record.storageCode ? '&storageCode='.concat(record.storageCode) : '',
          '&access_token=',
        )
        .concat(getAccessToken()),
    );
  };

  return (
    <div className={styles['search-result']}>
      {dataSet.map(record => (
        <div className={styles['search-result-item']}>
          <div className={styles['file-header']}>
            {record?.get('qisEsFileList')?.length &&
            record?.get('qisEsFileList')[0]?.restHighLevelFileName && (
              <div
                className={styles['file-name']}
                dangerouslySetInnerHTML={{
                  __html: record?.get('qisEsFileList')?.length
                    ? record?.get('qisEsFileList')[0]?.restHighLevelFileName
                    : '',
                }}
                onClick={() => handleJumpToDetail(record?.get('fileId'))}
              />
            )
            }
            {(!record?.get('qisEsFileList')?.length ||
              !record?.get('qisEsFileList')[0]?.restHighLevelFileName) && (
              <div
                className={styles['file-name']}
                onClick={() => handleJumpToDetail(record?.get('fileId'))}
              >
                {record?.get('qisEsFileList')?.length ? record?.get('qisEsFileList')[0]?.fileName : ''}
              </div>
            )}

            <div className={styles['read-source-doc']} onClick={() => handlePreviewFile(record)}>
              {intl.get(`${modelPrompt}.previewStartFile`).d('查看原文件')}
            </div>
            {renderFileStatusTag(record)}
          </div>
          <div
            className={styles['file-content']}
            dangerouslySetInnerHTML={{
              __html: record?.get('qisEsFileList')?.length
                ? record?.get('qisEsFileList')[0]?.restHighLevelContent
                : '',
            }}
            onClick={() => handleJumpToDetail(record?.get('fileId'))}
          />

          <div className={styles['file-footer']}>
            {formField.map(name => {
              const displayValue =
                ['affliatedProcess', 'fileLevel'].includes(name) ? record!.getField(name)!.getText() : record?.get(name);
              return (
                <div className={styles['file-footer-item']}>
                  <span className={styles['file-footer-item-label']}>
                    {dataSet.getField(name)?.get('label')}:
                  </span>
                  <span className={styles['file-footer-item-content']}>{displayValue}</span>
                </div>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
});

const FullTextSearchPage = () => {
  const searchFormDs = useMemo(() => new DataSet(searchFormDS()), []);
  const tableDs = useMemo(() => new DataSet(searchTableDS()), []);
  const [paginationInfo, setPaginationInfo] = useState<PaginationInfo>({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
  const [selectedMenu, setSelectedMenu] = useState('ALL');

  const handleChangePagination = (page, pageSize) => {
    if (pageSize === paginationInfo.pageSize) {
      handleQuery({ page, pageSize });
    } else {
      handleQuery({ page: 1, pageSize });
    }
  };

  const handleReset = () => {
    searchFormDs.reset();
    handleQuery({ page: 1, pageSize: 10 });
  };

  const handleJumpToTop = () => {
    const _container = document.getElementsByClassName('page-content');
    if (_container?.length) {
      _container[0].scrollTo(_container[0].scrollTop, 0);
    }
  };

  const handleQuery = ({
    page = paginationInfo.currentPage,
    pageSize = paginationInfo.pageSize,
    period = selectedMenu,
  }) => {
    tableDs.pageSize = pageSize;
    const { queryContent, searchType } = searchFormDs.current?.toData();
    tableDs.setQueryParameter('queryContent', queryContent);
    tableDs.setQueryParameter('searchType', searchType);
    tableDs.setQueryParameter('publishDateType', period === 'ALL' ? undefined : period);
    tableDs.query(page).then(() => {
      setPaginationInfo(pre => ({
        ...pre,
        currentPage: page,
        pageSize,
        total: tableDs.totalCount,
      }));
      handleJumpToTop();
    });
  };
  
  useDataSetEvent(tableDs, 'load', ({ dataSet }) => {
    setPaginationInfo(pre => ({
      ...pre,
      total: dataSet.totalCount,
    }));
    handleJumpToTop();
  });

  const handleChangeTime = e => {
    const _value = e.key;
    setSelectedMenu(_value);
    handleQuery({ page: 1, pageSize: 10, period: _value });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.fullTextSearch`).d('全文检索')} />
      <Content className={styles['full-text-search-page']}>
        <TarzanSpin dataSet={tableDs}>
          <Row className={styles['search-content']}>
            <Col span={20}>
              <Form dataSet={searchFormDs} columns={5}>
                <TextField name="queryContent" colSpan={5} onEnterDown={() => handleQuery({ page: 1, pageSize: 10 })} />
                <Radio name="searchType" value="any" onChange={() => handleQuery({ page: 1, pageSize: 10 })}>
                  {intl.get(`${modelPrompt}.contains.anyKeyword`).d('包含任意一个关键词')}
                </Radio>
                {/* <Radio name="searchType" value="all" onChange={() => handleQuery({ page: 1, pageSize: 10 })}>
                  包含全部的关键词
                </Radio> */}
                <Radio name="searchType" value="perfect" onChange={() => handleQuery({ page: 1, pageSize: 10 })}>
                  {
                    intl.get(`${modelPrompt}.match.keywordExactly`).d('完全匹配关键字')
                  }
                </Radio>
              </Form>
            </Col>
            <Col span={4} style={{ lineHeight: '36px', paddingLeft: '16px' }}>
              <Button onClick={() => handleReset()}>
                {intl.get('hzero.common.status.reset').d('重置')}
              </Button>
              <Button color={ButtonColor.primary} onClick={() => handleQuery({ page: 1, pageSize: 10 })}>
                {intl.get(`${modelPrompt}.searchText`).d('检索')}
              </Button>
            </Col>
          </Row>
          <div className={styles['full-text-search-container']}>
            <div className={styles['search-total-count']}>
              {
                intl.get(`${modelPrompt}.searchApproximate`).d('搜索到约')
              }
              <span className={styles['total-count-num']}>{paginationInfo.total}</span>
              {
                intl.get(`${modelPrompt}.item.result`).d('项结果')
              }
            </div>
            <div className={styles['search-result-container']}>
              <div className={styles['full-text-search-container-left']}>
                <div className={styles['search-bar']}>
                  <Table
                    queryBar={TableQueryBarType.filterBar}
                    queryBarProps={{
                      fuzzyQuery: false,
                    }}
                    dataSet={tableDs}
                    columns={[]}
                    searchCode="systemDocumentManagement-searchCode"
                    className={styles['full-text-search-table']}
                    pagination={false}
                  />
                </div>
                <SearchResultComponent dataSet={tableDs} />
                <div className={styles['search-pagination']}>
                  <Pagination
                    showSizeChanger
                    showTotal
                    showPager
                    page={paginationInfo.currentPage}
                    pageSize={paginationInfo.pageSize}
                    total={paginationInfo.total}
                    onChange={handleChangePagination}
                  />
                </div>
              </div>
              <div className={styles['full-text-search-container-right']}>
                <Menu
                  defaultSelectedKeys={['ALL']}
                  selectedKeys={[selectedMenu]}
                  mode="inline"
                  onClick={handleChangeTime}
                >
                  <Menu.Item disabled key="title">
                    <div
                      className={styles['publish-time-title']}
                    >
                      {
                        intl.get(`${modelPrompt}.byReleaseTime`).d('按发布时间')
                      }
                    </div>
                  </Menu.Item>
                  <Item key="DAYS">
                    {intl.get(`${modelPrompt}.oneDay`).d('一天内')}
                  </Item>
                  <Item key="WEEKS">
                    {intl.get(`${modelPrompt}.oneWeek`).d('一周内')}
                  </Item>
                  <Item key="MONTHS">
                    {intl.get(`${modelPrompt}.oneMonth`).d('一月内')}
                  </Item>
                  <Item key="YEARS">
                    {intl.get(`${modelPrompt}.oneYear`).d('一年内')}
                  </Item>
                  <Item key="ALL">
                    {intl.get(`${modelPrompt}.allTime`).d('不限')}
                  </Item>
                </Menu>
              </div>
            </div>
          </div>
        </TarzanSpin>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(FullTextSearchPage);
