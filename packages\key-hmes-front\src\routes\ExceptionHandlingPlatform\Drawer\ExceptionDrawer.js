/* eslint-disable no-nested-ternary */
import React, { Component } from 'react';
import { Modal, Row, Col, Form, Timeline, Button } from 'hzero-ui';
import { Bind } from 'lodash-decorators';
import { isFunction } from 'lodash';
import styles from '../index.less';
import ExceptionTipModal from './ExceptionTipModal';

@Form.create({ fieldNameProp: null })
export default class ExceptionDrawer extends Component {
  constructor(props) {
    super(props);
    if (isFunction(props.onRef)) {
      props.onRef(this);
    }
    this.state = {
      tipVisible: false, // 下排按钮小弹窗
      type: '', // 弹窗类型
      title: '', // 弹窗标题
      content: '', // 弹窗内容
    };
  }

  componentDidMount() {}

  @Bind()
  hideModal() {
    this.props.hideModal(false, false, '');
  }

  @Bind()
  openTipModal(title, type, content) {
    this.setState({
      tipVisible: true,
      title,
      type,
      content,
    });
  }

  @Bind()
  hideTipModal() {
    this.setState({
      tipVisible: false,
    });
  }

  render() {
    const {
      exceptioListVisible,
      modalType,
      exceptionRecord,
      exceptionResultList,
      // exceptionStatus,
      handleTipException,
      tenantId,
      queryEmployee,
      onRef,
      Ref,
      solvedResult,
      changeSolvedResult,
      closeExceptionLoading,
      responseExceptionLoading,
      abnormalTypeList,
      enterSiteInfo,
      fetchVal,
    } = this.props;
    const { tipVisible, title, type, content } = this.state;
    const formLayout = {
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
    };
    const exceptionTipProps = {
      tipVisible,
      title,
      fetchVal,
      type,
      content,
      tenantId,
      abnormalTypeList,
      hideTipModal: this.hideTipModal,
      handleTipException,
      queryEmployee,
      onRef,
      Ref,
      // eslint-disable-next-line no-return-assign
      threeRef: ref => (this.threeLov = ref),
      closeExceptionLoading,
      responseExceptionLoading,
      modalType,
      exceptionRecord,
      enterSiteInfo,
    };
    return (
      <>
        <Modal
          title="异常处理"
          width="70%"
          visible={exceptioListVisible}
          className={styles['exception-model']}
          style={{ paddingBottom: '0px' }}
          onCancel={() => this.hideModal()}
          footer={[
            // <Button
            //   key="upgrade"
            //   type="primary"
            //   onClick={() =>
            //     this.openTipModal('异常升级', 'upgrade', Number(exceptionRecord.exceptionLevel) + 1)
            //   }
            //   style={{ marginLeft: '5px' }}
            //   // disabled={exceptionRecord.exceptionLevel === '5'}
            // >
            //   异常升级
            // </Button>,
            <Button
              key="response"
              type="primary"
              onClick={() => this.openTipModal('异常响应', 'response', null)}
              style={{ marginLeft: '5px' }}
              disabled={
                exceptionRecord.exceptionRes !== 'Y' ||
                exceptionRecord.attribute5 === '1' ||
                (exceptionRecord.createdWays === '1' && modalType === 'EQUIPMENT')
              }
              // disabled={exceptionRecord.createdWays === '1'&&modalType==='EQUIPMENT'}
            >
              异常响应
            </Button>,
            <Button
              key="cancel"
              type="primary"
              onClick={() => this.openTipModal('异常取消', 'cancel', exceptionRecord.exceptionName)}
              style={{ marginLeft: '5px' }}
              disabled={exceptionRecord.attribute5 === '1' || exceptionRecord.createdWays === '1'}
            >
              异常取消
            </Button>,
            <Button
              onClick={() => this.openTipModal('异常关闭', 'close', exceptionRecord.exceptionName)}
              key="close"
              disabled={exceptionRecord.attribute5 !== '1'}
            >
              异常关闭
            </Button>,
          ]}
        >
          <Form layout="vertical">
            <Row gutter={40} style={{ padding: '20px' }}>
              <Col span={12} className={styles['exception-model-form']} id="leftDrawer">
                <Row>
                  <Col span={12}>
                    <Form.Item {...formLayout} label="异常项">
                      {exceptionRecord.exceptionName}
                    </Form.Item>
                  </Col>
                  {/* <Col span={12}>
                    <Form.Item {...formLayout} label="班次">
                      {exceptionRecord.shiftCode}
                    </Form.Item>
                  </Col> */}
                </Row>
                <Row>
                  <Col span={12}>
                    <Form.Item {...formLayout} label="工作单元">
                      {exceptionRecord.workcellName}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item {...formLayout} label="操作者">
                      {exceptionRecord.realName}
                    </Form.Item>
                  </Col>
                  <Col span={12} />
                </Row>
                {/* <Row>
                  <Col>
                    <Form.Item labelCol={{ span: 5 }} wrapperCol={{ span: 16 }} label="附件名称">
                      {getFieldDecorator(
                        'workOrderNum',
                        {}
                      )(
                        <UploadModal
                          bucketName="file-mes"
                          btnText="附件预览"
                          viewOnly
                          icon="paper-clip"
                          attachmentUUID={exceptionRecord.attachmentUuid}
                        />
                      )}
                    </Form.Item>
                  </Col>
                </Row>
                {modalType === 'EQUIPMENT' && (
                  <Row>
                    <Col>
                      <Form.Item labelCol={{ span: 5 }} wrapperCol={{ span: 16 }} label="设备编码">
                        {getFieldDecorator(
                          'workOrderNum',
                          {}
                        )(<span>{exceptionRecord.equipmentCode}</span>)}
                      </Form.Item>
                    </Col>
                  </Row>
                )}
                {modalType === 'MATERIAL' && (
                  <Row>
                    <Col span={12}>
                      <Form.Item labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} label="物料条码">
                        {getFieldDecorator(
                          'workOrderNum',
                          {}
                        )(<span>{exceptionRecord.materialLotCode}</span>)}
                      </Form.Item>
                    </Col>
                  </Row>
                )}
                {modalType === 'MATERIAL' && (
                  <Row>
                    <Col span={12}>
                      <Form.Item labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} label="物料编码">
                        {getFieldDecorator(
                          'workOrderNum',
                          {}
                        )(<span>{exceptionRecord.materialCode}</span>)}
                      </Form.Item>
                    </Col>
                  </Row>
                )} */}
                <Row>
                  <Col>
                    <Form.Item
                      labelCol={{ span: 5 }}
                      wrapperCol={{ span: 16 }}
                      label="异常处理结果"
                    >
                      {exceptionResultList.map(item => (
                        <Button
                          onClick={() => changeSolvedResult(item.value)}
                          style={{ width: '64px', marginRight: '15px' }}
                          size="small"
                          type={solvedResult === item.value ? 'primary' : 'default'}
                          // type={exceptionRecord.attribute5 !== '1'?'default':'primary'}
                        >
                          {item.meaning}
                        </Button>
                      ))}
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col>
                    <Form.Item
                      labelCol={{ span: 5 }}
                      wrapperCol={{ span: 16 }}
                      label="异常发起描述"
                    >
                      {exceptionRecord.exceptionRemark}
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col span={12} id="rightDrawer" style={{ paddingRight: '0 !important' }}>
                <div style={{ height: '100%' }} className={styles['exception-model-form']}>
                  <div style={{ height: '8%', fontSize: 14 }}>操作记录：</div>
                  <Timeline style={{ padding: '7px, 20px', height: '92%' }}>
                    {exceptionRecord.statusHistoryList.map(item => {
                      return (
                        <Timeline.Item>
                          {item.creationDate}&nbsp;&nbsp;{item.exceptionStatusMeaning}&nbsp;&nbsp;
                          {item.realName}
                        </Timeline.Item>
                      );
                    })}
                  </Timeline>
                </div>
              </Col>
            </Row>
          </Form>
        </Modal>
        <ExceptionTipModal {...exceptionTipProps} />
      </>
    );
  }
}
