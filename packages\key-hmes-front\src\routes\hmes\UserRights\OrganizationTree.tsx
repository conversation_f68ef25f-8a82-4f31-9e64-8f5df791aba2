/**
 * @Description: 用户权限编辑抽屉-左侧树
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 20:26:51
 * @LastEditTime: 2022-10-14 10:57:22
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Tree } from 'choerodon-ui/pro';
import intl from 'utils/intl';

const { TreeNode } = Tree;
const modelPrompt = 'tarzan.model.hmes.userRights';

/**
 * 树节点类型
 * @export
 * @interface OrgTreeNode
 */
export interface OrgTreeNode {
  organizationRelId: number, // 节点唯一Id
  organizationId: number, // 节点Id
  organizationCode: string, // 节点编码
  organizationDesc: string | null, // 节点描述
  organizationType: string, // 节点类型
  organizationTypeDesc: string, // 节点类型描述
  alreadyDefined: boolean | null, // 是否已分配
  sequence: number | null, // 排序
  subUserOrgRelList: OrgTreeNode[], // 子节点信息
}

interface OrganizationTreeProps {
  ds: DataSet,
  orgTreeConfig: OrgTreeNode[],
  locatorTreeConfig: OrgTreeNode[],
  maxHeight: number,
}

const OrganizationTree = observer((props: OrganizationTreeProps) => {
  const { ds, orgTreeConfig, locatorTreeConfig, maxHeight } = props;

  const allotType = ds!.current!.get('allotType');

  const treeConfig = useMemo(() => {
    if (allotType === 'Organization') {
      return orgTreeConfig;
    }
    return locatorTreeConfig;
  }, [allotType, orgTreeConfig, locatorTreeConfig])

  const selectAll = useMemo(() => intl.get(`${modelPrompt}.selectAll`).d('全选'), []);

  const renderTreeItems = (nodes: OrgTreeNode[] | null) => {
    if (!nodes || !nodes.length) {
      return;
    }
    return nodes.map(item => {
      return (
        <TreeNode
          title={
            <div>
              {item.organizationTypeDesc}-{item.organizationCode}-{item.organizationDesc}
              {item.subUserOrgRelList && item.subUserOrgRelList.length > 0 && (
                <a
                  onClick={() => handleSelectAll(item)}
                  style={{ marginLeft: '8px' }}
                >
                  {selectAll}
                </a>
              )}
            </div>
          }
          key={`${item.organizationRelId}`}
          id={item.organizationRelId}
          dataRef={item}
          disableCheckbox={
            item.organizationType === 'ENTERPRISE' || item.alreadyDefined || undefined
          }
        >
          {item.subUserOrgRelList && renderTreeItems(item.subUserOrgRelList)}
        </TreeNode>
      );
    });
  };

  const handleExpand = (_expandedKeys) => {
    ds!.current!.set('expandedKeys', _expandedKeys);
  };

  const handleCheck = (_checkedKeys) => {
    const { checked } = _checkedKeys;
    ds!.current!.set('checkedKeys', checked);
  };

  const handleSelectAll = (_treeNode: OrgTreeNode) => {
    const _expandedKeys: string[] = ds!.current!.get('expandedKeys');
    const _checkedKeys: string[] = ds!.current!.get('checkedKeys');
    // 将当前节点和下层数据打平
    const _list = _treeNode.subUserOrgRelList.concat([_treeNode]);
    // 当前展开节点keys
    const currentExpandedKeys = [...new Set(_expandedKeys.concat([`${_treeNode.organizationRelId}`]))];
    // 本次新选中的节点
    const newCheckedNodes = _list.filter(item => item.organizationType !== "ENTERPRISE" && !item.alreadyDefined);
    // 本次新选中的节点organizationRelId
    const newCheckedKeys = newCheckedNodes.map(item => `${item.organizationRelId}`);
    // 计算此次数据是否已全选中
    const calcAllSelectedFlag = _list.every(item =>
      item.organizationType === "ENTERPRISE" ||
      item.alreadyDefined ||
      _checkedKeys.includes(`${item.organizationRelId}`),
    );
    // 当前选中的节点Keys
    let currentCheckedKeys: string[];
    if (calcAllSelectedFlag) {
      // 取消全选，取差集
      currentCheckedKeys = [...new Set(_checkedKeys)].filter(item => !(new Set(newCheckedKeys).has(item)));
    } else {
      // 全选，取并集
      currentCheckedKeys = [...new Set(_checkedKeys.concat(newCheckedKeys))];
    }
    ds!.current!.set('expandedKeys', currentExpandedKeys);
    ds!.current!.set('checkedKeys', currentCheckedKeys);
  };

  return (
    <Tree
      checkable
      onExpand={handleExpand}
      expandedKeys={ds?.current!.get('expandedKeys')}
      onCheck={handleCheck}
      checkedKeys={{ checked: ds?.current!.get('checkedKeys'), halfChecked: [] }}
      checkStrictly
      height={maxHeight}
    >
      {renderTreeItems(treeConfig)}
    </Tree>
  )
});

export default OrganizationTree;
