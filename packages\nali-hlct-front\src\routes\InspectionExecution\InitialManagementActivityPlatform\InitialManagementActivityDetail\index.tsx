/**
 * @Description: 检验方案维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-06-15 14:09:31
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect, useMemo, useRef } from 'react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import moment from 'moment';
import uuid from 'uuid/v4';
import myInstance from '@utils/myAxios';
import request from 'utils/request';
import { isJSONString } from '@/utils';
import {
  DataSet,
  Button,
  Form,
  TextField,
  Select,
  Switch,
  Lov,
  Attachment,
  Modal,
  TextArea,
  Spin,
  DateTimePicker,
  Output,
} from 'choerodon-ui/pro';
import { Collapse, Icon, Tabs } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { useDataSetEvent } from 'utils/hooks';
import { useRequest } from '@components/tarzan-hooks';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { drawerPropsC7n } from '@components/tarzan-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { BASIC } from '@utils/config';
import { TabsType } from 'choerodon-ui/lib/tabs/enum';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import {
  mtInspectItemDetailConfig,
  fetchRuleDtlConfig,
  // mtInspectSchemePageUiConfig,
} from '../services';
import {
  detailFormDS,
  inspectionItemBasisDS,
  dimensionTableDS,
  copyBusinessTypeDS,
  detailTableDS,
  submitFormDS,
} from '../stores/InspectionSchemeDS';
import { DetailTableDS } from '../components/stores';
import DimensionTableListDrawer from './DimensionTableListDrawer';
import DimensionDetailTableList from '../components/DimensionDetailTableList/InspectItemTab';
import DetailTable from './DetailTable';
import DetailComponent from '@/routes/TestingStrategy/InspectItemMaintain/components/DetailComponent';
import CollapsePanelTitle from '../components/CollapsePanelTitle';
import TabsTabPaneTitle from '../components/TabsTabPaneTitle';
import AddActiveTabDimensionButton from '../components/AddActiveTabDimensionButton';

let batchAddInspectionDimensionModal;

// let tempList: any = [];

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();
const endUrl = '';

const modelPrompt = 'tarzan.initialManagementActivity';
const { Panel } = Collapse;
const { TabPane } = Tabs;
const { Option } = Select;

const InspectionSchemeTable = props => {
  const {
    match: {
      path,
      params: { id },
    },
    customizeForm,
    customizeTable,
    custConfig,
  } = props;

  // pub路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  const statisticsTableRef = useRef<any>(null);

  const childRef = useRef<any>();

  const [titleMap, setTitleMap] = useState({});

  const [schemeTypeData] = useState('');

  const [inspectBusinessType, setInspectBusinessType] = useState('');

  const [itemPatternList, setItemPatternList] = useState([]);

  const titleMapOri = useMemo(() => {
    return {
      areaId: {
        valueKey: 'areaName',
        text: intl.get(`${modelPrompt}.area`).d('区域'),
      },
      prodLineId: {
        valueKey: 'prodLineName',
        text: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      },
      processWorkcellId: {
        valueKey: 'processWorkcellName',
        text: intl.get(`${modelPrompt}.process`).d('工序'),
      },
      stationWorkcellId: {
        valueKey: 'stationWorkcellName',
        text: intl.get(`${modelPrompt}.station`).d('工位'),
      },
      equipmentId: {
        valueKey: 'equipmentName',
        text: intl.get(`${modelPrompt}.equipment`).d('设备'),
      },
      operationId: {
        valueKey: 'operationName',
        text: intl.get(`${modelPrompt}.operation`).d('工艺'),
      },
      supplierId: {
        valueKey: 'supplierName',
        text: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      },
      customerId: {
        valueKey: 'customerName',
        text: intl.get(`${modelPrompt}.customer`).d('客户'),
      },
    };
  }, []);

  // 编辑状态
  const [canEdit, setCanEdit] = useState(false);

  const [saveLoading, setSaveLoading] = useState(false);

  // 状态
  const [pageStatus, setPageStatus] = useState('');

  const [pageIdObject, setPageIdObject] = useState({ id: null, isCopy: false, isTmp: false });

  // 生成新tabkey
  const [newTabIndex, setNewTabIndex] = useState(100);
  // 当前tabkey
  const [activeKey, setActiveKey] = useState('');
  // tab及数据列表
  const [inspectSchemeLines, setInspectSchemeLines] = useState<Array<any>>([]);

  // 维度折叠面板数据
  const [collapseActiveKeys, setCollapseActiveKeys] = useState({});

  const [detailRowSelectList, setDetailRowSelectList] = useState([]);

  const [queryLoading, setQueryLoading] = useState(false);

  const [operationAllEquipmentFlag, setOperationAllEquipmentFlag] = useState(false); // 是否工艺下全部设备开关是否显示

  const [addAction, setAddAction] = useState(false); // 从检验方案添加活动项只有活动对象=PARTS_COGL或PROCESS_CQGL时，才可点击此按钮，否则置灰；

  const [handAddAction, setHandAddAction] = useState(false); // 手工添加活动项按钮 从检验方案添加活动项只有活动对象=PARTS_COGL或PROCESS_CQGL时，才可点击此按钮，否则置灰；

  const [actObjectValue, setActObject] = useState(''); // 活动对象 值；

  const [showHandAdd, setShowHandAdd] = useState(false); // 手工添加活动项

  // 获取请选择检验业务类型列表
  const fetchRuleDtl = useRequest(fetchRuleDtlConfig(), {
    manual: true,
    needPromise: true,
  });

  // 查询检验方案列表
  // const mtInspectSchemePageUi = useRequest(mtInspectSchemePageUiConfig(), {
  //   manual: true,
  //   needPromise: true,
  // });

  // 检验项目详情
  const mtInspectItemDetail = useRequest(mtInspectItemDetailConfig(), {
    manual: true,
    needPromise: true,
  });

  const formDs = useMemo(() => new DataSet(detailFormDS()), []);
  const detailTableDs = useMemo(() => new DataSet(detailTableDS()), []);
  const dimensionTableDs = useMemo(() => new DataSet(dimensionTableDS()), []);
  const copyBusinessTypeDs = useMemo(() => new DataSet(copyBusinessTypeDS()), []);
  const submitFormDs = useMemo(() => new DataSet(submitFormDS()), []);
  // 监听活动小组勾选数据
  const handleDetailRowTableSelect = ({ dataSet }) => {
    setDetailRowSelectList(dataSet.selected || []);
  };
  useDataSetEvent(detailTableDs, 'select', handleDetailRowTableSelect);
  useDataSetEvent(detailTableDs, 'selectAll', handleDetailRowTableSelect);
  useDataSetEvent(detailTableDs, 'unselect', handleDetailRowTableSelect);
  useDataSetEvent(detailTableDs, 'unselectAll', handleDetailRowTableSelect);

  // 添加检验方案信息表单监听
  useEffect(() => {
    function processDataSetListener(flag) {
      const handler = flag ? formDs.addEventListener : formDs.removeEventListener;
      handler.call(formDs, 'update', handleformDsUpdate);
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  // 初始化页面
  useEffect(() => {
    let isCopy = false;
    let isTmp = false;
    let newId = id;
    if (id.indexOf('Tmp') > -1) {
      isTmp = true;
    }
    if (id.indexOf('copy') > -1) {
      isCopy = true;
      newId = id.split('copy')[0];
    }
    setPageIdObject({
      id: newId,
      isCopy,
      isTmp,
    });
  }, [id]);

  // 初始化页面
  useEffect(() => {
    if (pageIdObject.id === 'create') {
      pageReset();
      setTimeout(() => {
        setCanEdit(true);
        // addTabs('new');
      }, 100);
      return;
    }
    if (pageIdObject.id && pageIdObject.id !== 'create') {
      initPageData();
    }
    formatTitleMap();
  }, [pageIdObject]);

  // 基础表单监听事件
  const handleformDsUpdate = ({ name, record, value }) => {
    switch (name) {
      case 'siteObject':
        record.init('inspectSchemeObject', null);
        record.init('revisionCode', null);
        handleFormDsValueChange('siteObject', value);
        handleResetDimensionsDs(false);
        break;
      case 'inspectSchemeObjectTypeObject':
        record.init('inspectSchemeObject', null);
        record.init('revisionCode', null);
        handleInspectSchemeObjectTypeObjectChange();
        handleResetDimensionsDs(false);
        break;
      case 'inspectSchemeObject':
        record.init('revisionCode', null);
        handleFormDsValueChange('materialObject', value);
        handleResetDimensionsDs(false);
        if (!(value?.revisionFlag === 'Y')) {
          // queryInspectionList();
        }
        break;
      case 'revisionCode':
        handleFormDsValueChange('revisionCode', value);
        // queryInspectionList();
        break;
      case 'actObject':
        // 切换或清空活动对象，则清空是否工艺下全部设备、设备号、设备名称的值
        record.set('operationAllEquipmentFlag', value === 'PROCESS_CQGL' ? 'N' : null);
        record.set('equipmentObject', null);
        record.set('inspectBusinessType', null);
        setOperationAllEquipmentFlag(value === 'PROCESS_CQGL');
        setAddAction(['PARTS_COGL', 'PROCESS_CQGL'].includes(value));
        setHandAddAction(['PRODUCT_COGL'].includes(value));
        setInspectSchemeLines([]);
        setActObject(value);
        break;
      case 'materialLov':
        // 校验当用户切换或清空了物料，清空活动内容下的项目信息
        setInspectSchemeLines([]);
        break;
      case 'prodLineLov':
        // 校验当用户切换或清空了产线，清空活动内容下的项目信息
        setInspectSchemeLines([]);
        break;
      case 'operationLov':
        // 校验当用户切换或清空了工艺，清空活动内容下的项目信息
        setInspectSchemeLines([]);
        break;
      case 'supplierObject':
        // 校验当用户切换或清空了供应商，清空活动内容下的项目信息
        setInspectSchemeLines([]);
        break;
      case 'equipmentObject':
        // 校验当用户切换或清空了供应商，清空活动内容下的项目信息
        setInspectSchemeLines([]);
        break;
      case 'operationAllEquipmentFlag':
        // 当 是否工艺下全部设备=否 时必输，多选，LOV逻辑和MT.MODEL.EQUIPMENT一致，但是要支持多选 （是否工艺下全部设备值更改的时候将设备号晴空）
        record.set('equipmentObject', null);
        break;
      case 'inspectBusinessType':
        record.set('inspectBusinessTypeList', value);
        setShowHandAdd(value==='PRODUCT_COGL')
        setInspectSchemeLines([]);
        break;
      default:
        break;
    }
  };

  const editChange = () => {
    let hasChange = false;
    inspectSchemeLines.forEach(item => {
      const { inspectionItemBasisDs, key } = item;
      handleClosable(inspectionItemBasisDs);
      // const editFlag = inspectionItemBasisDs.current.get('editFlag');
      // console.log('first', editFlag, key)
      if (!hasChange) {
        setActiveKey(`${key}`);
        hasChange = true;
      }
    });
  };

  const formatTitleMap = () => {
    const codeConfigFields =
      custConfig[`${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DMS_DETAIL`]?.fields;
    const newTitleMap = {
      ...titleMapOri,
    };
    codeConfigFields.forEach(item => {
      newTitleMap[item.fieldCode] = {
        valueKey: item.fieldCode,
        text: item.fieldName,
      };
    });

    setTitleMap(newTitleMap);
  };

  const pageReset = () => {
    setActiveKey(`${newTabIndex + 1}`);
    setPageStatus('');
    setNewTabIndex(newTabIndex + 1);
    setInspectSchemeLines([]);
    formDs.loadData([
      {
        enableFlag: 'Y',
        createdBy: userInfo.realName,
        creationDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        initialManagementStatus: 'NEW',
      },
    ]);
    detailTableDs.loadData([]);
    dimensionTableDs.loadData([]);

    // tempList = [];
  };

  // 初始化页面
  const initPageData = () => {
    setCanEdit(!!pageIdObject.isCopy);
    const params = {
      // customizeUnitCode,
      initialManagementId: pageIdObject.id,
      // [pageIdObject.isTmp ? 'inspectSchemeTmpId' : 'initialManagementId']: pageIdObject.id,
    };
    request(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/initial-managements/detail/ui`, {
      method: 'GET',
      query: { ...params },
    }).then(res => {
      if (res.failed) {
        notification.error({ message: res.message });
      } else {
        handleRender(res, 'init');
      }
      setShowHandAdd(res?.headerManagementInfo?.actObject === "PRODUCT_COGL");
      setQueryLoading(false);
    });
  };

  const handleRender = (res, flag) => {
    const enclosureRecordList: Array<any> = [];
    let lines: any = [];
    let formOther: any = {};
    setActiveKey('');
    setNewTabIndex(100);
    setCollapseActiveKeys({});
    let _newTabIndex = 101;
    const _activeKey = _newTabIndex + 1;
    if (flag === 'init') {
      const { inspectSchemeLines, headerManagementInfo, groupManagementInfos } = res;
      lines = inspectSchemeLines;
      formOther = headerManagementInfo;
      setAddAction(['PARTS_COGL', 'PROCESS_CQGL'].includes(headerManagementInfo.actObject));
      setOperationAllEquipmentFlag(headerManagementInfo.actObject === 'PROCESS_CQGL');
      setHandAddAction(['PRODUCT_COGL'].includes(headerManagementInfo.actObject));
      setActObject(headerManagementInfo.actObject);
      if (pageIdObject.isCopy && !pageIdObject.isTmp) {
        delete formOther.initialManagementId;
        delete formOther.initialManagementNum;
        formOther.currentFlag = 'N';
        formOther.initialManagementStatus = undefined;
      }
      setPageStatus(formOther.initialManagementStatus);
      if (formOther?.equipmentIdList?.length > 0) {
        formOther.equipmentId = formOther.equipmentIdList;
        formOther.equipmentName = formOther.equipmentNameList;
      }
      // 检验方案信息加载
      formDs.loadData([formOther]);
      if (groupManagementInfos) {
        detailTableDs.loadData(groupManagementInfos);
      } else {
        detailTableDs.loadData([]);
      }
    } else {
      lines = res;
      formOther = formDs.current?.toData();
    }

    // 复制复制附件
    if (pageIdObject.isCopy) {
      formDs.forEach(record => {
        if (record.get('enclosure')) {
          enclosureRecordList.push(record);
        }
      });
    }
    // 初始化tab单元
    if (formOther.equipmentId) {
      formDs?.current?.set('equipmentIdList', formOther.equipmentIdList);
      formDs?.current?.set('equipmentIdOriginList', formOther.equipmentIdList);
      if (formOther.equipmentNameList) {
        formDs?.current?.set('equipmentName', formOther.equipmentNameList.join(','));
      }
    }
    // 保存所有tab的检验业务类型, 用做tab的检验业务类型过滤条件
    const allInspectBusinessType: Array<any> = [];

    // 保存维度折叠面板key
    const initCollapseActiveKeys: any = {};
    if (lines) {
      // if (inspectSchemeLines && inspectSchemeLines.length > 0) {
      if (formOther.actObject !== 'PARTS_COGL') {
        const queryType = lines?.map(ele => ele.inspectBusinessType);
        formDs?.current?.set('inspectBusinessType', queryType);
      } else {
        formDs?.current?.set('inspectBusinessType', '');
      }
      // }
      const newInspectSchemeLines = lines?.map(item => {
        // 更新tab最新key
        _newTabIndex += 1;
        const { dimensions, ...linesOther } = item;
        // tab基础信息ds
        const inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());
        inspectionItemBasisDs.loadData([linesOther]);
        if (linesOther.inspectBusinessType) {
          allInspectBusinessType.push(linesOther.inspectBusinessType);
        }
        return {
          key: `${_newTabIndex}`,
          inspectionItemBasisDs,
          // tab下维度信息读取
          dimensions: dimensions?.map(dimensionsItem => {
            const { itemPatternList, ...dimensionsItemother } = dimensionsItem;
            const itemsDs = new DataSet(DetailTableDS({}));
            const itemsSimple: any = [];
            const itemsFormula: any = [];
            setItemPatternList(itemPatternList);
            if (itemPatternList.length > 0) {
              itemPatternList.forEach(val => {
                val.items.forEach(itemsItem => {
                  const _valueItem = { ...itemsItem };
                  if (['TEXT', 'DECISION_VALUE'].includes(_valueItem.dataType)) {
                    _valueItem.trueValue =
                      (_valueItem.trueValueList || []).length > 0
                        ? _valueItem.trueValueList[0].dataValue
                        : null;
                    _valueItem.falseValue =
                      (_valueItem.falseValueList || []).length > 0
                        ? _valueItem.falseValueList[0].dataValue
                        : null;
                  }
                  if (_valueItem.dataType === 'VALUE_LIST') {
                    _valueItem.trueValue =
                      (_valueItem.trueValueList || []).length > 0
                        ? _valueItem.trueValueList?.map(trueItem => trueItem.dataValue)
                        : null;
                    _valueItem.falseValue =
                      (_valueItem.falseValueList || []).length > 0
                        ? _valueItem.falseValueList?.map(falseItem => falseItem.dataValue)
                        : null;
                  }

                  if (['CALCULATE_FORMULA'].includes(_valueItem.dataType)) {
                    const formula = isJSONString(_valueItem.formula || '');
                    if (formula) {
                      const {
                        formulaSourceId,
                        formulaMode,
                        formulaDisplayPosition,
                        formulaId,
                        formulaCode,
                        formulaName,
                        dimension,
                        formulaList,
                      } = formula;
                      _valueItem.formulaSourceId = formulaSourceId;
                      _valueItem.formulaMode = formulaMode;
                      _valueItem.formulaDisplayPosition = formulaDisplayPosition;
                      _valueItem.formulaId = formulaId;
                      _valueItem.formulaCode = formulaCode;
                      _valueItem.formulaName = formulaName;
                      _valueItem.dimension = dimension;
                      _valueItem.formulaList = formulaList;
                    } else {
                      _valueItem.formulaSourceId = null;
                      _valueItem.formulaMode = null;
                      _valueItem.formulaDisplayPosition = null;
                      _valueItem.formulaId = null;
                      _valueItem.formulaCode = null;
                      _valueItem.formulaName = null;
                      _valueItem.dimension = null;
                      _valueItem.formulaList = null;
                    }
                  }

                  if (_valueItem.dataType === 'CALCULATE_FORMULA') {
                    itemsFormula.push({
                      ..._valueItem,
                      inspectionItemRowUuid: itemsItem.inspectItemId,
                      samplingDimension: linesOther.samplingDimension,
                    });
                  } else {
                    itemsSimple.push({
                      ..._valueItem,
                      inspectionItemRowUuid: itemsItem.inspectItemId,
                      samplingDimension: linesOther.samplingDimension,
                    });
                  }
                });
              });
            }
            itemsDs.loadData([...itemsSimple, ...itemsFormula]);
            // 复制复制附件
            if (pageIdObject.isCopy) {
              itemsDs.forEach(record => {
                if (record.get('enclosure')) {
                  enclosureRecordList.push(record);
                }
              });
            }
            // 维度折叠面板id插入
            // if (initCollapseActiveKeys[`${_newTabIndex}`]) {
            //   initCollapseActiveKeys[`${_newTabIndex}`].push(
            //     `${dimensionsItemother.inspectObjectDimensionId ||
            //       dimensionsItemother.inspectObjectDmsTmpId}`,
            //   );
            // } else {
            // initCollapseActiveKeys[`${_newTabIndex}`] = [
            //   `${dimensionsItemother.inspectObjectDimensionId ||
            //     dimensionsItemother.inspectObjectDmsTmpId}`,
            // ];
            // }
            if (initCollapseActiveKeys[`${_newTabIndex}`]) {
              initCollapseActiveKeys[`${_newTabIndex}`].push('0');
            } else if (flag === 'init') {
              initCollapseActiveKeys[`${_newTabIndex}`] = ['0'];
            } else {
              initCollapseActiveKeys[`${_newTabIndex}`] = [
                '0',
                `${dimensionsItemother.inspectObjectDimensionId}`,
              ];
            }

            const dimensionDetailDs = new DataSet(dimensionTableDS());
            dimensionDetailDs.loadData([
              {
                ...dimensionsItemother,
                uuid:
                  dimensionsItemother.inspectObjectDimensionId ||
                  dimensionsItemother.inspectObjectDmsTmpId,
                siteId: formOther.siteId,
                siteCode: formOther.siteCode,
                revisionCode: formOther.revisionCode,
              },
            ]);
            const newDimensionsItem = {
              itemsDs,
              dimensionDetailDs,
            };
            return newDimensionsItem;
          }),
        };
      });
      setCollapseActiveKeys(initCollapseActiveKeys);

      setNewTabIndex(_newTabIndex);
      newInspectSchemeLines.forEach(item => {
        item.inspectionItemBasisDs.forEach(record => {
          record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
          record.set('inspectSchemeObjectType', formOther.inspectSchemeObjectType);
          record.set('inspectSchemeObjectId', formOther.inspectSchemeObjectId);
          record.set('siteId', formOther.siteId);
        });
      });
      setInspectSchemeLines(newInspectSchemeLines);
    }
    // tempList = newInspectSchemeLines;

    enclosureRecordList.forEach(record => {
      getNewUuid(record);
    });

    // 设置当前tabkey
    setActiveKey(`${_activeKey}`);
  };

  // 校验所有ds
  const validateAllDs = async () => {
    // 不需要弹窗提示表单列表
    const validateDsListNormal: Array<any> = [];
    // 需要弹窗提示表单列表
    const validateDsListNotice: Array<any> = [];

    // 整理所有ds插入对应列表

    // 维度检验信息

    const dimensionsValidateList = [];
    // 检验方案信息不需要
    validateDsListNormal.push(formDs);
    inspectSchemeLines.forEach(tabPaneItem => {
      const { inspectionItemBasisDs, dimensions } = tabPaneItem;
      if (inspectionItemBasisDs && actObjectValue !== 'PRODUCT_COGL') {
        // 检验项目tab基础信息 不需要
        validateDsListNormal.push(inspectionItemBasisDs);
      }
      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        // 保存当前维度下 非计算公式类型检验项目的 inspectItemId
        const dmsInspectItemId: any = [];
        const dmsInspectItemMap: any = {};

        if (itemsDs) {
          // 检验项目维度列表 将所有tab下的维度列表record 提取插入临时数组 并记录对应报错信息
          itemsDs.toData().forEach((tableRecord: any) => {
            if (tableRecord.inspectItemId && tableRecord.dataType !== 'CALCULATE_FORMULA') {
              dmsInspectItemId.push(tableRecord.inspectItemId);
              dmsInspectItemMap[tableRecord.inspectItemId] = tableRecord.inspectItemDesc;
            }
          });
        }
      });
    });
    // 校验无需弹窗的表单
    const normalValidate = await Promise.all(
      validateDsListNormal?.map(async validateDsListItem => {
        const itemValidate = await validateDsListItem.validate();
        return itemValidate;
      }),
    );

    // 校验需要弹窗的表单
    const noticeValidate = await Promise.all(
      validateDsListNotice?.map(async validateDsListItem => {
        let itemValidate = await validateDsListItem.record.validate('all');
        const recordData = validateDsListItem.record.toData();
        if (recordData.dataType === 'VALUE') {
          let listLength = 0;
          if (recordData.trueValueList && recordData.trueValueList.length) {
            listLength = recordData.trueValueList.length;
          }
          if (recordData.falseValueList && recordData.falseValueList.length) {
            listLength = recordData.falseValueList.length;
          }
          if (!listLength || listLength === 0) {
            itemValidate = false;
          }
        }

        return { itemValidate, title: validateDsListItem.title };
      }),
    );

    // 汇总校验结果
    const normalResult = normalValidate.every(val => val);
    // const normalResult = true;

    // 校验不通过的信息提取一条并提示
    let valmessage;
    const noticeResult = noticeValidate.every(val => {
      if (!val.itemValidate) {
        valmessage = val.title;
      }
      return val.itemValidate;
    });

    if (!noticeResult) {
      notification.error({
        message: `${valmessage}`,
      });
    }

    if (dimensionsValidateList.length > 0) {
      notification.error({
        message: `${dimensionsValidateList[0]}`,
      });
    }

    // 返回校验结果
    return normalResult && noticeResult && dimensionsValidateList.length === 0;
  };

  // 组合数据
  const getAllData = () => {
    const params: any = {};

    const _inspectSchemeLines = inspectSchemeLines?.map(item => {
      // 更新tab最新key
      const { inspectionItemBasisDs, dimensions, ...linesOther } = item;
      return {
        ...linesOther,
        ...inspectionItemBasisDs.toData()[0],
        // tab下维度信息读取
        dimensions: dimensions?.map(dimensionsItem => {
          const { itemsDs, dimensionDetailDs } = dimensionsItem;
          const itemPatternListSave: any = itemPatternList?.map((ele: any) => {
            return {
              ...ele,
              items: itemsDs.toData()?.map(itemsDsDataItem => {
                const _itemsDsDataItem = { ...itemsDsDataItem };
                _itemsDsDataItem.trueValueList = itemsDsDataItem.trueValueList || [];
                _itemsDsDataItem.falseValueList = itemsDsDataItem.falseValueList || [];
                _itemsDsDataItem.warningValueList = itemsDsDataItem.warningValueList || [];
                return _itemsDsDataItem;
              }),
            };
          });
          return {
            ...dimensionDetailDs.toData()[0],
            itemPatternList: itemPatternListSave,
          };
        }),
      };
    });

    const headerManagementInfo: any = formDs.toData()[0];
    params.inspectSchemeLines = _inspectSchemeLines;
    params.groupManagementInfos = detailTableDs.toData();
    params.headerManagementInfo = headerManagementInfo;
    return params;
  };

  // 保存
  const handleSave = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      notification.error({
        message: intl.get(`${modelPrompt}.notification.requiredEmpty`).d('有必输项未填写'),
      });
      return;
    }
    if(inspectSchemeLines.length<=0){
      notification.error({ message: intl.get(`${modelPrompt}.notification.mustHaveOneData`).d('请至少维护1条初期活动项目！') });
      return;
    }

    const data:any = [];
    inspectSchemeLines.forEach(item => {
      const { dimensions } = item;
      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        const itemsData = itemsDs.toJSONData();
        data.push(itemsData);
      });
    });
    const dataList = data.reduce((acc, curr) => acc.concat(curr), []);
    const flag = dataList.map(item => {
      const keys = Object.keys(item);
      if (!item.enterMethod || !item.samplingMethodId || !item.dataQty || !item.inspectFrequency || !item.m || !item.n) {
        return true;
      }
      if (['CALCULATE_FORMULA', 'VALUE'].includes(item.dataType)) {
        if (!keys.includes('trueValueList') && !keys.includes('falseValueList')) {
          if (item.trueValueList?.length < 1 && item.falseValueList?.length < 1) {
            return true;
          }
          return true;
        }
      }
      if (!['DATE', 'TEXT'].includes(item.dataType)) {
        if (!keys.includes('trueValueList') && !keys.includes('falseValueList')) {
          if (item.trueValueList?.length === 0 && item.falseValueList?.length === 0) {
            return true;
          }
          return true;
        }
      }
      return false;
    })
    if (flag.includes(true)) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.notification.validate.message`)
          .d('检验项有必输字段未输，请检查！'),
      });
      return;
    }
    const params = getAllData();
    if (params.groupManagementInfos && params.groupManagementInfos.length > 0) {
      const validateLineResult = await detailTableDs.validate();
      if (!validateLineResult) {
        notification.error({
          message: intl.get(`${modelPrompt}.notification.requiredEmpty`).d('有必输项未填写'),
        });
        return false;
      }
    }
    if (params.inspectSchemeLines && params.inspectSchemeLines.length > 0) {
      if (
        params.headerManagementInfo.inspectBusinessType &&
        params.headerManagementInfo.inspectBusinessType.length > 0 &&
        actObjectValue !== 'PRODUCT_COGL'
      ) {
        const typeList = params.inspectSchemeLines?.map(item => item.inspectBusinessType);
        const typeListStr = typeList.join();
        const headStr = params.headerManagementInfo.inspectBusinessType.join();
        if (typeListStr !== headStr) {
          return notification.error({
            message: intl.get(`${modelPrompt}.notification.pleaseQueryAgain`).d('检验业务类型更改，请重新查询检验项目再保存'),
          });
        }
      }
      if (
        params.inspectSchemeLines[0].dimensions &&
        params.inspectSchemeLines[0].dimensions.length > 0 &&
        params?.headerManagementInfo?.actObject === 'PARTS_COGL'
      ) {
        if (
          (
            params?.inspectSchemeLines[0]?.dimensions[0]?.supplierId &&
            Number(params.inspectSchemeLines[0].dimensions[0].supplierId) !==
            Number(params.headerManagementInfo.supplierId)
          ) ||
          (
            params?.inspectSchemeLines[0]?.dimensions[0]?.materialId &&
            Number(params.inspectSchemeLines[0].dimensions[0].materialId) !==
            Number(params.headerManagementInfo.materialId)
          )
        ) {
          return notification.error({
            message: intl.get(`${modelPrompt}.notification.pleaseFetchFromSchemeAgain`).d('活动内容的物料或供应商有变更，请重新从检验方案获取活动项！'),
          });
        }
      }
    }

    delete params.headerManagementInfo.inspectBusinessType;
    delete params.headerManagementInfo.equipmentCode;
    delete params.headerManagementInfo.equipmentId;
    delete params.headerManagementInfo.equipmentName;
    delete params.headerManagementInfo.equipmentObject;
    // let validateFlag = false;
    // const list = [];
    // params.inspectSchemeLines.forEach(dimension => {
    //   dimension.dimensions.forEach(itemPattern => {
    //     itemPattern.itemPatternList.forEach(item => {
    //       item.;
    //     });
    //   });
    // });
    setSaveLoading(true);
    return request(
      `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/initial-managements/update/ui`,
      {
        method: 'POST',
        body: params,
      },
    ).then(res => {
      setSaveLoading(false);
      if (res && !res.failed) {
        notification.success({});
        if (pageIdObject.id === 'create' || pageIdObject.isCopy) {
          props.history.push(`/hwms/initial-management-activity-platform/detail/${res}`);
        } else {
          initPageData();
        }
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  // 取消
  const handleCancel = () => {
    if (pageIdObject.id === 'create' || pageIdObject.isCopy) {
      props.history.push('/hwms/initial-management-activity-platform/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  // 切换tab
  const handleChangeTab = newActiveKey => {
    setActiveKey(newActiveKey);
  };

  // 删除tab
  const removeTabs = targetKey => {
    let _activeIndex = 0;
    const _tabPanes = inspectSchemeLines.filter((item, index) => {
      if (item.key === targetKey) {
        _activeIndex = index;
      }
      return item.key !== targetKey;
    });
    if (_tabPanes.length === 0) {
      setActiveKey('');
    } else if (_activeIndex > _tabPanes.length - 1) {
      setActiveKey(`${_tabPanes[_tabPanes.length - 1].key}`);
    } else {
      setActiveKey(`${_tabPanes[_activeIndex].key}`);
    }
    setInspectSchemeLines(_tabPanes);
  };

  const onTabsEdit = (targetKey) => {
    removeTabs(targetKey);
  };

  // 新增维护
  const addActiveTabDimensionHandleOk = () => {
    const _tabPanes: Array<any> = [];
    const dimensionTableDsData = dimensionTableDs.toData();
    const duplicateList: Array<any> = [];
    const compareList = dimensionTableDsData?.map((item: any) => {
      const valueString = `materialId${item.materialId || ''}+areaId${item.areaId ||
        ''}+materialCategoryId${item.materialCategoryId || ''}+prodLineId${item.prodLineId ||
        ''}+processWorkcellId${item.processWorkcellId ||
        ''}+stationWorkcellId${item.stationWorkcellId || ''}+equipmentId${item.equipmentId ||
        ''}+operationId${item.operationId || ''}+supplierId${item.supplierId ||
        ''}+customerId${item.customerId || ''}+otherObject${item.otherObject || ''}+
      `;
      if (duplicateList.indexOf(valueString) === -1) {
        duplicateList.push(valueString);
      }
      return valueString;
    });
    if (duplicateList.length !== compareList.length) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.repeatNotice`).d('请勿输入完全相同的检验维度'),
      });
      return false;
    }

    const newCollapseActiveKeys = { ...collapseActiveKeys };

    inspectSchemeLines.forEach(item => {
      if (activeKey === item.key) {
        const newDimensions: Array<any> = [];
        const _dimensions = item.dimensions;
        dimensionTableDsData.forEach((dimensionTableDsDataItem: any) => {
          const newDimensionsItem: any = {};
          _dimensions.forEach(dimensionsItem => {
            if (
              dimensionTableDsDataItem.uuid === dimensionsItem.dimensionDetailDs.current.get('uuid')
            ) {
              newDimensionsItem.itemsDs = dimensionsItem.itemsDs;
              newDimensionsItem.dimensionDetailDs = dimensionsItem.dimensionDetailDs;
              newDimensionsItem.dimensionDetailDs.loadData([dimensionTableDsDataItem]);
            }
          });
          if (!newDimensionsItem.itemsDs) {
            newDimensionsItem.itemsDs = new DataSet(DetailTableDS({}));
          }
          if (!newDimensionsItem.dimensionDetailDs) {
            const dimensionDetailDs = new DataSet(dimensionTableDS());
            dimensionDetailDs.loadData([dimensionTableDsDataItem]);
            newDimensionsItem.dimensionDetailDs = dimensionDetailDs;
            newCollapseActiveKeys[item.key].push(`${dimensionTableDsDataItem.uuid}`);
          }
          newDimensions.push(newDimensionsItem);
        });
        item.dimensions = newDimensions;
        item.inspectionItemBasisDs.forEach(record => {
          record.init('inspectionDimensionObject', null);
          record.init('inspectionItemObject', null);
        });
      }
      _tabPanes.push(item);
    });
    setCollapseActiveKeys(newCollapseActiveKeys);
    setInspectSchemeLines(_tabPanes);
    dimensionTableDs.loadData([]);
  };

  // 检验维度tab点击事件和新增检验维度
  const addActiveTabDimension = type => {
    const _dimensions: Array<any> = [];
    let inspectionItemBasisDs;

    inspectSchemeLines.forEach(item => {
      if (activeKey === item.key) {
        inspectionItemBasisDs = item.inspectionItemBasisDs;
        item.dimensions.forEach(dimensionsItem => {
          const { dimensionDetailDs } = dimensionsItem;
          _dimensions.push(dimensionDetailDs.toData()[0]);
        });
      }
    });

    dimensionTableDs.loadData(_dimensions);
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title:
        type === 'new'
          ? intl.get(`${modelPrompt}.addInspectionDimension`).d('新增检验维度')
          : intl.get(`${modelPrompt}.editInspectionDimension`).d('编辑检验维度'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      footer: (okBtn, cancelBtn) => {
        return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
      },
      onOk: addActiveTabDimensionHandleOk,
      children: (
        <DimensionTableListDrawer
          customizeTable={customizeTable}
          modelPrompt={modelPrompt}
          formDs={formDs}
          inspectionItemBasisDs={inspectionItemBasisDs}
          tableDs={dimensionTableDs}
          canEdit={canEdit}
          path={path}
        />
      ),
    });
  };

  const handleCopyBusinessTypeCopyDone = () => {
    const {
      originInspectBusinessType,
      targetInspectBusinessType,
      targetInspectBusinessTypeDesc,
    }: any = copyBusinessTypeDs.toData()[0];

    const allInspectBusinessType: Array<any> = [targetInspectBusinessType];

    const inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());

    const dimensions: any = [];

    const newItemDimensionsUuid: any = [];

    inspectSchemeLines.forEach(item => {
      const inspectionItemBasisDsData = item.inspectionItemBasisDs.toData()[0];
      item.inspectionItemBasisDs.forEach(record => {
        if (record.get('inspectBusinessType')) {
          allInspectBusinessType.push(record.get('inspectBusinessType'));
        }
      });

      if (inspectionItemBasisDsData.inspectBusinessType === originInspectBusinessType) {
        inspectionItemBasisDs.loadData([
          {
            ...inspectionItemBasisDsData,
            inspectBusinessTypeObject: {
              inspectBusinessType: targetInspectBusinessType,
              inspectBusinessTypeDesc: targetInspectBusinessTypeDesc,
            },
            inspectBusinessType: targetInspectBusinessType,
            inspectBusinessTypeDesc: targetInspectBusinessTypeDesc,
            initialManagementId: null,
            inspectSchemeLineId: null,
          },
        ]);

        if (item?.dimensions?.length > 0) {
          item.dimensions.forEach(dimensionsItem => {
            let _itemsDsData = dimensionsItem.itemsDs.toData();
            _itemsDsData = _itemsDsData?.map(itemsDsItem => {
              return {
                ...itemsDsItem,
                inspectObjectDmsItemId: null,
              };
            });
            const _dimensionDetailDsData = dimensionsItem.dimensionDetailDs.toData()[0];
            const itemsDs = new DataSet(DetailTableDS({}));
            const dimensionDetailDs = new DataSet(dimensionTableDS());
            const newUuid = uuid();
            newItemDimensionsUuid.push(`${newUuid}`);
            dimensions.push({
              itemsDs: itemsDs.loadData(_itemsDsData),
              dimensionDetailDs: dimensionDetailDs.loadData([
                {
                  ..._dimensionDetailDsData,
                  uuid: newUuid,
                },
              ]),
            });
          });
        }
      }
    });

    inspectSchemeLines.forEach(item => {
      item.inspectionItemBasisDs.forEach(record => {
        record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
      });
    });

    inspectionItemBasisDs.forEach(record => {
      record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
    });

    setInspectSchemeLines([
      ...inspectSchemeLines,
      {
        key: `${newTabIndex + 1}`,
        inspectionItemBasisDs,
        dimensions,
      },
    ]);

    const newCollapseActiveKeys = { ...collapseActiveKeys };
    newCollapseActiveKeys[`${newTabIndex + 1}`] = [...newItemDimensionsUuid];
    setCollapseActiveKeys(newCollapseActiveKeys);

    setActiveKey(`${newTabIndex + 1}`);
    setNewTabIndex(newTabIndex + 1);
  };

  const handleCopyBusinessType = async () => {
    const validateRes = await copyBusinessTypeDs.validate();
    if (!validateRes) {
      return false;
    }
    const originResult = await fetchRuleDtl.run({
      params: {
        inspectBusinessType: copyBusinessTypeDs.current?.get('originInspectBusinessType'),
        siteId: copyBusinessTypeDs.current?.get('siteId'),
      },
    });
    const targetResult = await fetchRuleDtl.run({
      params: {
        inspectBusinessType: copyBusinessTypeDs.current?.get('targetInspectBusinessType'),
        siteId: copyBusinessTypeDs.current?.get('siteId'),
      },
    });

    if (typeof originResult?.rows === 'object' && typeof targetResult?.rows === 'object') {
      const originResultJsonString = JSON.stringify(originResult);
      const targetResultJsonString = JSON.stringify(targetResult);
      if (originResultJsonString === targetResultJsonString) {
        handleCopyBusinessTypeCopyDone();
        return true;
      }
      notification.error({
        message: intl
          .get(`${modelPrompt}.copyBusinessTypeError`)
          .d('来源和目标的检验维度不一致，不能复制！'),
      });
      return false;
    }
  };

  const copyBusinessType = () => {
    const originInspectBusinessTypes: any = [];
    inspectSchemeLines.forEach(tabItem => {
      const {
        inspectBusinessType,
        inspectBusinessTypeDesc,
        editFlag,
      } = tabItem.inspectionItemBasisDs.toData()[0];
      if (inspectBusinessType && inspectBusinessTypeDesc && editFlag === 'Y') {
        // @ts-ignore
        originInspectBusinessTypes.push({
          inspectBusinessType,
          inspectBusinessTypeDesc,
        });
      }

      copyBusinessTypeDs.loadData([
        {
          siteId: formDs.current?.get('siteId'),
          inspectBusinessTypes: originInspectBusinessTypes?.map(item => {
            return item.inspectBusinessType;
          }),
        },
      ]);
    });

    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.copyBusinessType`).d('复制检验业务类型'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: handleCopyBusinessType,
      children: (
        <Form dataSet={copyBusinessTypeDs} columns={1} labelWidth={112} disabled={!canEdit}>
          <Select name="originInspectBusinessTypeObject" colSpan={2}>
            {originInspectBusinessTypes?.map(originInspectBusinessType => (
              <Option value={originInspectBusinessType.inspectBusinessType}>
                {originInspectBusinessType.inspectBusinessTypeDesc}
              </Option>
            ))}
          </Select>
          <Lov autoSelectSingle={false} noCache name="targetInspectBusinessTypeObject" />
        </Form>
      ),
    });
  };

  const batchAddInspectionDimension = inspectBusinessType => {
    inspectSchemeLines.forEach(item => {
      if (activeKey === item.key) {
        item.inspectionItemBasisDs.current.set('inspectionDimensionObject', []);
        batchAddInspectionDimensionModal = Modal.open({
          key: Modal.key(),
          title: intl.get(`${modelPrompt}.batchAddInspectionDimension`).d('批量新增检验项目'),
          destroyOnClose: true,
          style: {
            width: 360,
          },
          okCancel: false,
          okText: intl.get(`${modelPrompt}.close`).d('关闭'),
          children: (
            <Form
              dataSet={item.inspectionItemBasisDs}
              columns={1}
              labelWidth={112}
              disabled={!canEdit}
            >
              <Select name="inspectionDimensionObject" colSpan={2}>
                {item.dimensions &&
                  item.dimensions?.map(dimensionsItem => (
                    <Option value={selectOptionValue(dimensionsItem)}>
                      {selectOptionFormat(dimensionsItem)}
                    </Option>
                  ))}
              </Select>
              <Lov
                autoSelectSingle={false}
                noCache
                name="inspectionItemObject"
                onChange={value => {
                  handleInspectionItemObjectChange(value, item, false);
                }}
                modalProps={{
                  footer: (okBtn, cancelBtn, modal) => {
                    return [
                      <Button
                        onClick={() => {
                          handleCreateInspection(modal, item, inspectBusinessType);
                        }}
                      >
                        {intl.get(`${modelPrompt}.inspectSchemeObjectCreate`).d('新建检验项目')}
                      </Button>,
                      cancelBtn,
                      okBtn,
                    ];
                  },
                }}
                tableProps={{
                  queryFieldsLimit: 10,
                }}
              />
            </Form>
          ),
        });
      }
    });
  };

  // 检验方案信息变更事件
  const handleFormDsValueChange = (name, value) => {
    const formDsData: any = formDs.toData()[0] || {};
    const _tabPanes: any = [];
    inspectSchemeLines.forEach(item => {
      if (item.dimensions) {
        item.dimensions.forEach(dimensionsItem => {
          const { dimensionDetailDs } = dimensionsItem;
          dimensionDetailDs.forEach(record => {
            if (name === 'siteObject') {
              record.set('siteId', value?.siteId || null);
              record.set('materialCode', null);
              record.set('materialName', null);
              record.set('materialId', null);
              record.set('revisionCode', null);
              record.set('areaObject', null);
              record.set('prodLineObject', null);
              record.set('processObject', null);
              record.set('stationObject', null);
              record.set('equipmentObject', null);
              record.set('operationObject', null);
              record.set('supplierObject', null);
              record.set('customerObject', null);
              record.set('otherObject', null);
            }
            if (name === 'materialObject') {
              record.set('materialCode', null);
              record.set('materialName', null);
              record.set('materialId', null);
              record.set('revisionCode', null);
              record.set('areaObject', null);
              record.set('prodLineObject', null);
              record.set('processObject', null);
              record.set('stationObject', null);
              record.set('equipmentObject', null);
              record.set('operationObject', null);
              record.set('supplierObject', null);
              record.set('customerObject', null);
              record.set('otherObject', null);
              record.set('categoryCode', null);
              record.set('materialCategoryId', null);
              record.set('materialCategoryName', null);
              if (formDsData.inspectSchemeObjectType === 'MATERIAL') {
                record.set('materialId', formDsData.inspectSchemeObjectId || null);
                record.set('materialCode', formDsData.inspectSchemeObjectCode || null);
                record.set('materialName', formDsData.inspectSchemeObjectName || null);
                record.set('revisionCode', null);
              }
              if (formDsData.inspectSchemeObjectType === 'MATERIAL_CATEGORY') {
                record.set('categoryCode', formDsData.inspectSchemeObjectId || null);
                record.set('materialCategoryId', formDsData.inspectSchemeObjectId || null);
                record.set('materialCategoryName', formDsData.inspectSchemeObjectName || null);
              }
            }

            if (name === 'revisionCode') {
              record.set('revisionCode', value || null);
            }
          });
        });
      }
      if (name === 'siteObject') {
        item.dimensions = [item.dimensions[0]];
        item.inspectionItemBasisDs.forEach(record => {
          record.init('inspectBusinessTypeObject', null);
          record.init('siteId', value?.siteId || null);
          record.init('inspectBusinessTypeObjectIgnore', '');
        });
      }
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
    // tempList = _tabPanes;
  };

  // 检验对象类型修改触发
  const handleInspectSchemeObjectTypeObjectChange = () => {
    const formDsData: any = formDs.toData()[0] || {};
    const _tabPanes: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      const _dimensions: Array<any> = [];

      if (item.dimensions && item.dimensions.length > 0) {
        const { uuid: oldUuid } = item.dimensions[0].dimensionDetailDs.toData()[0];
        const dimensionDetailDs = new DataSet(dimensionTableDS());
        const newDsData: any = {
          uuid: oldUuid,
          sequence: 10,
          siteId: formDsData.siteId,
        };

        if (formDsData.inspectSchemeObjectType === 'MATERIAL') {
          newDsData.materialId = formDsData.inspectSchemeObjectId;
          newDsData.materialCode = formDsData.inspectSchemeObjectCode;
          newDsData.materialName = formDsData.inspectSchemeObjectName;
          newDsData.revisionCode = formDsData.revisionCode;
        }
        if (formDsData.inspectSchemeObjectType === 'MATERIAL_CATEGORY') {
          newDsData.materialCategoryId = formDsData.inspectSchemeObjectId;
          newDsData.categoryCode = formDsData.inspectSchemeObjectCode;
          newDsData.materialCategoryName = formDsData.inspectSchemeObjectName;
        }

        dimensionDetailDs.loadData([newDsData]);
        const _item = {
          itemsDs: item.dimensions[0].itemsDs,
          dimensionDetailDs,
        };
        _dimensions.push(_item);
      }
      item.dimensions = _dimensions;
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
    // tempList = _tabPanes;
  };

  // 重置 tab下Dimensions里的inspectionItemBasisDs数据
  const handleResetDimensionsDs = keepModal => {
    const _tabPanes: Array<any> = [];
    const formDsData: any = formDs.toData()[0];
    inspectSchemeLines.forEach(item => {
      if (item.inspectionItemBasisDs) {
        item.inspectionItemBasisDs.forEach(record => {
          if (!keepModal) {
            record.init('inspectionDimensionObject', null);
          }
          record.init('inspectionItemObject', null);
          record.init('inspectSchemeObjectType', formDsData.inspectSchemeObjectType);
          record.init('inspectSchemeObjectId', formDsData.inspectSchemeObjectId);
        });
      }
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
    // tempList = _tabPanes;
  };

  // 维度排序
  const sortList = (type, index) => {
    const _tabPanes: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      if (item.key === activeKey) {
        const _dimensions = item.dimensions;
        if (type === 'up' && index > 0) {
          const spliceItem = _dimensions.splice(index, 1);
          _dimensions.splice(index - 1, 0, spliceItem[0]);
        }
        if (type === 'down' && index < _dimensions.length) {
          const spliceItem = _dimensions.splice(index, 1);
          _dimensions.splice(index + 1, 0, spliceItem[0]);
        }
        _dimensions.forEach((dimensionsItem, dimensionsItemIndex) => {
          const { dimensionDetailDs } = dimensionsItem;
          dimensionDetailDs.forEach(record => {
            record.set('sequence', dimensionsItemIndex * 10 + 10);
          });
        });
        item.dimensions = _dimensions;
        _tabPanes.push(item);
        item.inspectionItemBasisDs.forEach(record => {
          record.init('inspectionDimensionObject', null);
          record.init('inspectionItemObject', null);
        });
      } else {
        _tabPanes.push(item);
      }
    });
    setInspectSchemeLines(_tabPanes);
    // tempList = _tabPanes;
  };

  // 附件配置
  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'initial-activity-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const attachment1Props: any = {
    name: 'actReportEnclosure',
    bucketName: 'qms',
    bucketDirectory: 'initial-activity-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  // 检验维度值
  const selectOptionValue = dimensionsItem => {
    const { dimensionDetailDs } = dimensionsItem;
    const dimensionsItemData = dimensionDetailDs.toData()[0] || {};
    return dimensionsItemData.sequence;
  };

  // 检验维度和维度tabtitle格式化
  const selectOptionFormat = dimensionsItem => {
    const { dimensionDetailDs } = dimensionsItem;
    const dimensionsItemData = dimensionDetailDs.toData()[0] || {};
    const inspectSchemeObjectTypeDesc =
      formDs.current?.get('materialName') ||
      intl.get(`${modelPrompt}.inspectSchemeObjectType`).d('检验对象类型');
    const inspectSchemeObjectType = formDs.current?.get('inspectSchemeObjectType');
    const inspectSchemeObjectName = formDs.current?.get('supplierName') || '';
    const inspectSchemeObjectRevision = formDs.current?.get('revisionCode') || '';
    const inspectSchemeObjectProdLine = formDs.current?.get('prodLineName') || '';
    const inspectSchemeObjectOperation = formDs.current?.get('operationName') || '';
    const inspectSchemeObjectEquipment = dimensionDetailDs.current.get('equipmentName') || '';

    let frontText = '';
    const titleMapKeys = Object.keys(titleMap);
    titleMapKeys.forEach(key => {
      if (dimensionsItemData[key]) {
        frontText += ` + ${titleMap[key].text}${dimensionsItemData[titleMap[key].valueKey]}`;
      }
    });
    // 活动对象=过程初期管理 检验维度展示：物料+产线+工艺+设备,
    if (actObjectValue === 'PROCESS_CQGL') {
      let showMessage = '';
      if (inspectSchemeObjectTypeDesc) {
        showMessage += `物料:${inspectSchemeObjectTypeDesc}`;
      }
      if (inspectSchemeObjectProdLine) {
        showMessage += `+产线:${inspectSchemeObjectProdLine}`;
      }
      if (inspectSchemeObjectOperation) {
        showMessage += `+工艺:${inspectSchemeObjectOperation}`;
      }
      if (inspectSchemeObjectEquipment) {
        showMessage += `+设备:${inspectSchemeObjectEquipment}`;
      }
      return showMessage;
    };

    if (
      ['MATERIAL_CATEGORY', 'MATERIAL'].includes(inspectSchemeObjectType) &&
      inspectSchemeObjectName
    ) {
      return `${inspectSchemeObjectTypeDesc}${inspectSchemeObjectName}${inspectSchemeObjectRevision}${frontText}`;
    }
    return `${inspectSchemeObjectTypeDesc}+${inspectSchemeObjectName}`;
  };

  const schemeStageData = dimensionsItem => {
    const { dimensionDetailDs } = dimensionsItem;
    const dimensionsItemData = dimensionDetailDs.toData()[0] || {};
    const dataObj = {
      schemeStage: null,
      initialActivityDocNum: null,
      validDateFrom: null,
      validDateTo: null,
    };
    dataObj.schemeStage = dimensionsItemData.schemeStage;
    dataObj.initialActivityDocNum = dimensionsItemData.initialActivityDocNum;
    dataObj.validDateFrom = dimensionsItemData.validDateFrom;
    dataObj.validDateTo = dimensionsItemData.validDateTo;
    return dataObj;
  };

  // 新建检验维度选中值后触发
  const handleInspectionItemObjectChange = (value, tabItem, keepModal) => {
    // debugger
    if (!value) {
      return;
    }

    tabItem.dimensions.forEach(dimensionsItem => {
      const { dimensionDetailDs } = dimensionsItem;
      const dimensionsItemData = dimensionDetailDs.toData()[0] || {};

      const inspectionItemBasisDsData = tabItem.inspectionItemBasisDs.toData()[0];
      if (inspectionItemBasisDsData.inspectionDimension.indexOf(dimensionsItemData.sequence) > -1) {
        const { itemsDs } = dimensionsItem;

        // 已有数据转为对象进行对比
        const inDsCodeMap: Array<any> = [];
        let sequence = 1;
        itemsDs.forEach(recordItem => {
          const recordItemData = recordItem.toData();
          inDsCodeMap.push(recordItemData.inspectItemCode);
          if (recordItemData?.sequence > sequence) {
            sequence = recordItemData.sequence;
          }
        });
        sequence = (Math.floor(sequence / 10) + 1) * 10;
        // 取得的value值还需要再处理
        value.forEach(valueItem => {
          const _valueItem = { ...valueItem };

          _valueItem.requiredFlag = _valueItem.requiredFlag || 'Y';
          _valueItem.destructiveExperimentFlag = _valueItem.destructiveExperimentFlag || 'N';
          _valueItem.outsourceFlag = _valueItem.outsourceFlag || 'N';

          if (['TEXT', 'DECISION_VALUE'].includes(_valueItem.dataType)) {
            _valueItem.trueValue =
              (_valueItem.trueValueList || []).length > 0
                ? _valueItem.trueValueList[0].dataValue
                : null;
            _valueItem.falseValue =
              (_valueItem.falseValueList || []).length > 0
                ? _valueItem.falseValueList[0].dataValue
                : null;
          }
          if (_valueItem.dataType === 'VALUE_LIST') {
            _valueItem.trueValue =
              (_valueItem.trueValueList || []).length > 0
                ? _valueItem.trueValueList?.map(trueItem => trueItem.dataValue)
                : null;
            _valueItem.falseValue =
              (_valueItem.falseValueList || []).length > 0
                ? _valueItem.falseValueList?.map(falseItem => falseItem.dataValue)
                : null;
          }
          if (inDsCodeMap.indexOf(valueItem.inspectItemCode) === -1) {
            const newRecord = itemsDs.create({
              ..._valueItem,
              inspectionItemRowUuid: uuid(),
              sequence,
            });
            if (!newRecord.get('samplingMethodId')) {
              newRecord.init('samplingMethodCode', inspectionItemBasisDsData.samplingMethodCode);
              newRecord.init('samplingMethodDesc', inspectionItemBasisDsData.samplingMethodDesc);
              newRecord.init('samplingMethodId', inspectionItemBasisDsData.samplingMethodId);
            }
            newRecord.init('samplingDimension', inspectionItemBasisDsData.samplingDimension);

            sequence += 10;
            getNewUuid(newRecord);
          }
        });
      }
      // batchAddInspectionDimensionModal.close();
    });
    handleResetDimensionsDs(keepModal);
    // @ts-ignore
    notification.success();
    upDateStatisticsTableRef();
  };

  // tab下检验业务类型修改
  const handleInspectBusinessTypeObjectChange = (value, key, ds) => {
    handleClosable(ds);
    if (value.inspectBusinessType === 'LLJ') {
      formDs.setState('inspectBusinessType', true);
    } else {
      formDs.setState('inspectBusinessType', false);
    }
    setInspectBusinessType(value.inspectBusinessType);
    const allInspectBusinessType: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      item.inspectionItemBasisDs.forEach(record => {
        if (record.get('inspectBusinessType')) {
          allInspectBusinessType.push(record.get('inspectBusinessType'));
        }
      });
    });

    const _tabPanes: any = [];
    inspectSchemeLines.forEach(item => {
      if (item.key === key) {
        if (item.dimensions) {
          item.dimensions.forEach(dimensionsItem => {
            const { dimensionDetailDs } = dimensionsItem;
            dimensionDetailDs.forEach(record => {
              record.set('areaObject', null);
              record.set('prodLineObject', null);
              record.set('processObject', null);
              record.set('stationObject', null);
              record.set('equipmentObject', null);
              record.set('operationObject', null);
              record.set('supplierObject', null);
              record.set('customerObject', null);
              record.set('otherObject', null);
            });
          });
        }
        item.inspectionItemBasisDs.current.set(
          'inspectBusinessTypeObjectIgnore',
          allInspectBusinessType,
        );
        item.inspectionItemBasisDs.forEach(record => {
          record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
        });
        item.dimensions = [item.dimensions[0]];
      }
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
    // tempList = _tabPanes;
  };

  const handleAdd = () => {
    if (canEdit) {
      const params: any = formDs.toData()[0];
      if (!params.actObject) {
        return notification.error({
          message: intl
            .get(`${modelPrompt}.notification.pleaseInputActivityContent`)
            .d('请输入活动内容信息后，再获取活动项！'),
        });
      }
      if (!params.materialId) {
        return notification.error({
          message: intl
            .get(`${modelPrompt}.notification.pleaseInputMaterial`)
            .d('请选择物料后，再获取活动项！'),
        });
      }
      if (params.actObject === 'PROCESS_CQGL' && params.inspectBusinessType?.length === 0) {
        return notification.error({
          message: intl
            .get(`${modelPrompt}.notification.pleaseSelectInspectBusinessType`)
            .d('请先维护检验业务类型！'),
        });
      }
      setQueryLoading(true);
      const type = params.actObject === 'PARTS_COGL' ? ['COA'] : params.inspectBusinessType;
      const paramsbody = {
        actObject: params.actObject,
        inspectBusinessTypeList: type,
        materialId: params.materialId,
        materialName: params.materialName,
        siteId: params.siteId,
        siteName: params.siteName,
        supplierId: params.supplierId,
        supplierName: params.supplierName,
        equipmentIdList: params.equipmentIdList,
        operationAllEquipmentFlag: params?.operationAllEquipmentFlag,
        operationId: params?.operationId,
      };
      request(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/initial-managements/item/ui`, {
        method: 'POST',
        body: paramsbody,
      }).then(res => {
        if (res.failed) {
          notification.error({ message: res.message });
        } else if (res.length > 0) {
          for (let i = 0; i < res.length; i++) {
            for (let j = 0; j < res[i].dimensions.length; j++) {
              res[i].dimensions[j].itemPatternList = res[i].dimensions[j].itemPatternList
                ? res[i].dimensions[j].itemPatternList
                : [];
            }
          }
          handleRender(res, 'query');
        }
        setQueryLoading(false);
      });
    }
  };

  // 手工添加活动项
  const handleHandAdd = () => {
    setShowHandAdd(true);
    handleRender(
      [
        {
          dimensions: [
            {
              itemPatternList: [
                {
                  items: [],
                },
              ],
            },
          ],
        },
      ],
      'query',
    );
  };

  // 分配检验维度后复制附件并获得uuid
  const getNewUuid = async record => {
    const enclosure = record.get('enclosure');
    if (!enclosure) {
      return Promise.resolve(true);
    }
    await myInstance
      .post(`hfle/v1/${tenantId}/files/copy-file`, { uuidList: [enclosure] })
      .then(res => {
        if (res && res.data && res.data[enclosure]) {
          record.init('enclosure', res.data[enclosure]);
        }
      });
    return Promise.resolve(true);
  };

  const getInspectItemDetail = async (id, item) => {
    const inspectItemDetail = await mtInspectItemDetail.run({
      params: { inspectItemId: id },
    });
    if (inspectItemDetail?.success && inspectItemDetail?.rows) {
      handleInspectionItemObjectChange([inspectItemDetail.rows], item, 'keep');
    }
  };

  // 新建检验项目点击确定的回调函数
  const handleCreateInspectionCallback = async item => {
    const { success, rows } = await childRef.current?.submit(false);
    if (success) {
      getInspectItemDetail(rows, item);
      if (batchAddInspectionDimensionModal) {
        batchAddInspectionDimensionModal.close();
      }
      return Promise.resolve(true);
    }
    return Promise.resolve(false);
  };

  // 新建检验项目点击确定的回调函数
  const handleCreateInspectionCallbackKeep = async item => {
    const { success, rows } = await childRef.current?.submit(true);
    if (success) {
      getInspectItemDetail(rows, item);
      return Promise.resolve(false);
    }
    return Promise.resolve(false);
  };

  const handleCreateInspection = (_modal, item, inspectBusinessType) => {
    _modal.close();
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.inspectSchemeObjectCreate`).d('新建检验项目'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      onOk: () => {
        return handleCreateInspectionCallback(item);
      },
      footer: (okBtn, cancelBtn) => {
        return [
          cancelBtn,
          <Button
            onClick={() => {
              return handleCreateInspectionCallbackKeep(item);
            }}
          >
            {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并新建下一条')}
          </Button>,
          okBtn,
        ];
      },
      children: (
        <DetailComponent
          ref={childRef}
          canEdit={canEdit}
          schemeType={schemeTypeData}
          inspectBusinessType={inspectBusinessType}
          kid="create"
          column={3}
          customizeForm={customizeForm}
          requiredField={['enterMethod', 'dataQty', 'samplingMethodLov']}
          validateType="InspectionScheme"
          operationType="SCHEME_FUNC_CREATE"
        />
      ),
    });
  };

  // 折叠面板样式i
  const expandIconButton = panelProps => {
    if (panelProps.isActive) {
      return <Icon type="expand_less" />;
    }
    return <Icon type="expand_more" />;
  };

  const upDateStatisticsTableRef = () => {
    if (statisticsTableRef.current) {
      statisticsTableRef.current.updateTable();
    }
  };

  // handleChangeCollapse
  const handleChangeCollapse = (value, key) => {
    const newCollapseActiveKeys = { ...collapseActiveKeys };
    if (newCollapseActiveKeys[key]) {
      newCollapseActiveKeys[key] = value;
    }
    setCollapseActiveKeys(newCollapseActiveKeys);
  };

  // 当前维度key
  const getPanelActiveKey = dimensionsItem => {
    let key = '';
    if (dimensionsItem?.dimensionDetailDs) {
      dimensionsItem?.dimensionDetailDs.forEach(record => {
        key = `${record.get('materialCode')}${record.get('equipmentId')}`
      });
    }
    return key;
  };

  const handleClosable = ds => {
    const data = ds.toData();
    if (
      data[0].inspectBusinessType === 'PRODUCT_CQGL' ||
      data[0].inspectBusinessType === 'PARTS_CQGL'
    ) {
      ds.setState('closable', false);
    } else {
      ds.setState('closable', true);
    }
  };

  // 启动提交
  const handleSubmit = () => {
    const params = getAllData();
    if (!params.inspectSchemeLines) {
      return notification.error({
        message: intl
          .get(`${modelPrompt}.notification.activityItemEmpty`)
          .d('活动项为空，不可提交！'),
      });
    }
    if (params.inspectSchemeLines && params.inspectSchemeLines.length === 0) {
      return notification.error({
        message: intl
          .get(`${modelPrompt}.notification.activityItemEmpty`)
          .d('活动项为空，不可提交！'),
      });
    }
    if (
      params.inspectSchemeLines &&
      params.inspectSchemeLines.length &&
      params.inspectSchemeLines[0].dimensions[0].itemPatternList[0].items.length === 0
    ) {
      return notification.error({
        message: intl
          .get(`${modelPrompt}.notification.activityItemEmpty`)
          .d('活动项为空，不可提交！'),
      });
    }
    setSaveLoading(true);
    request(
      `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/initial-managements/submit/ui?initialManagementId=${id}`,
      {
        method: 'POST',
      },
    ).then(res => {
      if (res.failed) {
        notification.error({
          message: `${res.message}`,
        });
      } else {
        notification.success({
          message: intl.get(`${modelPrompt}.notification.success`).d('操作成功'),
        });
        initPageData();
      }
      setSaveLoading(false);
    });
  };

  // 退出提交弹框
  const handleexitSubmitModal = () => {
    submitFormDs.create({});
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.exitSubmit`).d('退出提交'),
      destroyOnClose: true,
      style: {
        width: 640,
      },
      footer: (okBtn, cancelBtn) => {
        // return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
        return [cancelBtn, okBtn];
      },
      onOk: handleexitSubmit,
      children: (
        <Form dataSet={submitFormDs} columns={1} labelWidth={112}>
          <TextArea name="actReport" />
          <Attachment {...attachment1Props} />
        </Form>
      ),
    });
  };

  // 退出提交
  const handleexitSubmit = () => {
    setSaveLoading(true);
    request(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/initial-managements/exit/ui`, {
      method: 'POST',
      data: {
        initialManagementId: id,
        ...submitFormDs.toData()[0],
      },
    }).then(res => {
      if (res.failed) {
        notification.error({
          message: `${res.message}`,
        });
      } else {
        notification.success({
          message: intl.get(`${modelPrompt}.notification.success`).d('操作成功'),
        });
        initPageData();
      }
      setSaveLoading(false);
    });
  };

  const handleEquipQuery = () => {
    formDs?.current?.set('equipmentId', null);
    formDs?.current?.set('equipmentIdList', []);
    formDs?.current?.set('equipmentCode', '');
    formDs?.current?.set('equipmentName', '');
    formDs?.current?.set('equipmentNameList', []);
    formDs?.current?.set('equipmentIdOriginList', []);
  };

  const handleEquipChange = value => {
    formDs?.current?.set(
      'equipmentIdList',
      value?.map(item => item.equipmentId),
    );
  };

  const handleJumpScheme = () => {
    if (!formDs.current?.get('inspectSchemeId')) {
      return;
    }
    props.history.push(`/hwms/inspection-scheme-maintenance/detail/${formDs.current?.get('inspectSchemeId')}`);
  }

  const setDefaultKey = (items) => {
    const key:any[] = []
    items.forEach((item) => {
      if (item?.dimensionDetailDs) {
        item?.dimensionDetailDs.forEach(record => {
          key.push(`${record.get('materialCode')}${record.get('equipmentId')}`);
        });
      }
    })
    return key;
  };

  return (
    <div className="hmes-style" style={{ overflow: 'auto' }}>
      <Spin spinning={queryLoading || saveLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.list`).d('初期管理活动平台')}
          backPath={pubFlag ? '' : '/hwms/initial-management-activity-platform/list'}
        >
          {canEdit && (
            <>
              <Button
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
                loading={saveLoading}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && !pubFlag && (
            <>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                onClick={handleSubmit}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
                disabled={!['NEW', 'REJECTED', 'REVISE'].includes(pageStatus)}
              >
                {intl.get(`${modelPrompt}.button.startSubmit`).d('启动提交')}
              </PermissionButton>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                onClick={handleexitSubmitModal}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
                disabled={!['EXECUTE', 'EXIT_REJECTED'].includes(pageStatus)}
              >
                {intl.get(`${modelPrompt}.button.exitSubmit`).d('退出提交')}
              </PermissionButton>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="edit-o"
                onClick={() => {
                  setCanEdit(prev => !prev);
                  editChange();
                }}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
                disabled={!['NEW', 'REJECTED', 'REVISE'].includes(pageStatus)}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </PermissionButton>
            </>
          )}
          <ApprovalInfoDrawer
            objectTypeList={['QIS_LJCQGL_LWS_START', 'QIS_LJCQGL_LWS_END']}
            objectId={id}
          />
        </Header>
        <Content>
          <Collapse
            collapsible="icon"
            bordered={false}
            defaultActiveKey={[
              'panel1',
              'panel2',
              'panel3',
              'panel4',
              'panel5',
              'panel6',
              'panel7',
              'panel8',
            ]}
          >
            <Panel header={intl.get(`${modelPrompt}.title.basicInfo`).d('活动基本信息')} key="panel1">
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DETAIL`,
                },
                <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
                  <TextField name="initialManagementNum" />
                  <TextField name="initialManagementDesc" />
                  <Select name="initialManagementStatus" />
                  <TextField name="createdByDesc" />
                  <TextField name="creationDate" />
                  <Lov name="siteObject" />
                  <TextField name="siteName" />
                  <Lov name="materialPartsLov" />
                  <Select name="phase" />
                  <TextField name="model" />
                  <Lov name="materialLov" />
                  {/* <Switch name="enableFlag" /> */}
                  <TextField name="materialName" />
                  <DateTimePicker name="scheduleStartDate" />
                  <DateTimePicker name="scheduleEndDate" />
                  <TextField name="actualStartDate" />
                  <TextField name="actualEndDate" />
                </Form>,
              )}
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.title.activityStartReason`).d('活动启动原因')} key="panel2">
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DETAIL`,
                },
                <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
                  <Select name="actInitiationReason" />
                  <Attachment {...attachmentProps} />
                  <TextArea name="actInitiationRemark" colSpan={3} newLine />
                </Form>,
              )}
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.title.activityGroup`).d('活动小组')} key="panel3">
              <DetailTable
                ds={detailTableDs}
                detailRowSelectList={detailRowSelectList}
                canEdit={canEdit}
                customizeTable={customizeTable}
              />
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.title.activityContent`).d('活动内容')} key="panel4">
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DETAIL`,
                },
                <Form
                  dataSet={formDs}
                  columns={3}
                  labelWidth={112}
                  disabled={!canEdit}
                  style={{ marginBottom: '10px' }}
                >
                  <Select name="actObject" />
                  <Lov name="materialLov" />
                  <TextField name="materialName" />
                  <Lov name="prodLineLov" />
                  <TextField name="prodLineName" />
                  <Lov name="customerObject" />
                  <TextField name="customerName" />
                  <Lov name="operationLov" />
                  <TextField name="operationName" />
                  <Lov name="supplierObject" />
                  <TextField name="supplierName" />
                  {operationAllEquipmentFlag && (
                    <Switch name="operationAllEquipmentFlag" onChange={handleEquipQuery} />
                  )}
                  <Lov name="equipmentObject" onChange={handleEquipChange} />
                  <TextField name="equipmentName" />
                  <Select name="inspectBusinessType" />
                  <Output
                    name="inspectSchemeCode"
                    style={{ color: 'rgb(8, 64, 248)', cursor: 'pointer' }}
                    // @ts-ignore
                    renderer={({ value }) => <a disabled={pubFlag} onClick={handleJumpScheme}>{value}</a>}
                  />
                </Form>,
              )}
              {canEdit && addAction && (
                <a style={{ fontSize: '14px', paddingRight: '10px' }} onClick={() => handleAdd()}>
                  +{intl.get(`${modelPrompt}.button.addItemFromScheme`).d('从检验方案添加活动项')}
                </a>
              )}
              {(!canEdit || !addAction) && (
                <span style={{ fontSize: '14px', paddingRight: '10px' }}>
                  +{intl.get(`${modelPrompt}.button.addItemFromScheme`).d('从检验方案添加活动项')}
                </span>
              )}
              {canEdit && handAddAction && (
                <a style={{ fontSize: '14px' }} onClick={() => handleHandAdd()}>
                  +{intl.get(`${modelPrompt}.button.manualAddItem`).d('手工添加活动项')}
                </a>
              )}
              {(!canEdit || !handAddAction) && (
                <span style={{ fontSize: '14px' }}>+{intl.get(`${modelPrompt}.button.manualAddItem`).d('手工添加活动项')}</span>
              )}
              {showHandAdd ? (
                inspectSchemeLines?.map(item => {
                  return (
                    <>
                      {item.dimensions && (
                        <div
                          style={{
                            paddingLeft: '12px',
                          }}
                        >
                          {itemPatternList.length === 1 ||
                            itemPatternList.length === 0 ||
                            id === 'create' ?
                            (<DimensionDetailTableList
                              schemeType={schemeTypeData}
                              getNewUuid={getNewUuid}
                              formDs={item.inspectionItemBasisDs}
                              inspectBusinessType={item.inspectionItemBasisDs.current.get(
                                'inspectBusinessType',
                              )}
                              tableDS={item.dimensions[0].itemsDs}
                              path={path}
                              canEdit={canEdit}
                              customizeTable={customizeTable}
                              customizeForm={customizeForm}
                              statisticsTableUpdate={upDateStatisticsTableRef}
                              item={item}
                              dimensionsItem={item.dimensions[0]}
                              selectOptionValue={selectOptionValue}
                              selectOptionFormat={selectOptionFormat}
                              handleCreateInspection={handleCreateInspection}
                              actObject={actObjectValue}
                            />) :
                            (itemPatternList?.map(() => (
                              <DimensionDetailTableList
                                schemeType={schemeTypeData}
                                getNewUuid={getNewUuid}
                                formDs={item.inspectionItemBasisDs}
                                inspectBusinessType={item.inspectionItemBasisDs.current.get(
                                  'inspectBusinessType',
                                )}
                                tableDS={item.dimensions[0].itemsDs}
                                path={path}
                                canEdit={canEdit}
                                customizeTable={customizeTable}
                                customizeForm={customizeForm}
                                statisticsTableUpdate={upDateStatisticsTableRef}
                                item={item}
                                dimensionsItem={item.dimensions[0]}
                                selectOptionValue={selectOptionValue}
                                selectOptionFormat={selectOptionFormat}
                                handleCreateInspection={handleCreateInspection}
                                actObject={actObjectValue}
                              />
                            )))
                          }
                        </div>
                      )}
                    </>
                  );
                })
              ) : (
                <Tabs
                  hideAdd
                  onChange={handleChangeTab}
                  activeKey={activeKey}
                  defaultActiveKey={activeKey}
                  // @ts-ignore
                  type={canEdit && actObjectValue !== 'PROCESS_CQGL' ? 'editable-card' : 'card'}
                  // type='card'
                  onEdit={onTabsEdit}
                  style={{ marginTop: '10px' }}
                  tabBarExtraContent={
                    <AddActiveTabDimensionButton
                      addInspectionDimension={addActiveTabDimension}
                      batchAddInspectionDimension={batchAddInspectionDimension}
                      inspectBusinessType={inspectBusinessType}
                      copyBusinessType={copyBusinessType}
                      activeKey={activeKey}
                      canEdit={canEdit}
                      ds={formDs}
                      inspectSchemeLines={inspectSchemeLines}
                    />
                  }
                >
                  {inspectSchemeLines?.map(item => {
                    // if (canEdit && item.inspectionItemBasisDs.current.get('editFlag') !== 'Y') {
                    //   console.log('first',canEdit)
                    //   return null;
                    // }
                    return (
                      <TabPane
                        tab={<TabsTabPaneTitle ds={item.inspectionItemBasisDs} />}
                        key={item.key}
                        forceRender
                        closable={item.inspectionItemBasisDs.getState('closable')}
                      >
                        <>
                          {customizeForm(
                            {
                              code: `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.LINE_DETAIL`,
                            },
                            <Form
                              dataSet={item.inspectionItemBasisDs}
                              columns={3}
                              labelWidth={112}
                              disabled={!canEdit}
                            >
                              <Lov
                                autoSelectSingle={false}
                                name="inspectBusinessTypeObject"
                                onChange={value => {
                                  handleInspectBusinessTypeObjectChange(
                                    value,
                                    item.key,
                                    item.inspectionItemBasisDs,
                                  );
                                }}
                                disabled
                              />
                            </Form>,
                          )}
                          {item.dimensions && (
                            <div
                              style={{
                                paddingLeft: '12px',
                              }}
                            >
                              <Collapse
                                collapsible="icon"
                                expandIcon={expandIconButton}
                                defaultActiveKey={setDefaultKey(item.dimensions)}
                                onChange={value => {
                                  handleChangeCollapse(value, item.key);
                                }}
                              >
                                {(item.dimensions || [])?.map(
                                  (dimensionsItem, dimensionsItemIndex) => {
                                    return (
                                      <Panel
                                        header={
                                          <CollapsePanelTitle
                                            ds={formDs}
                                            itemDs={item.inspectionItemBasisDs}
                                            index={dimensionsItemIndex}
                                            sortList={sortList}
                                            title={selectOptionFormat(dimensionsItem)}
                                            dataObj={schemeStageData(dimensionsItem)}
                                            clickTitleCallback={addActiveTabDimension}
                                            activeKey={activeKey}
                                            canEdit={canEdit}
                                          />
                                        }
                                        key={getPanelActiveKey(dimensionsItem)}
                                        dataSet={dimensionsItem.itemsDs}
                                      >
                                        {itemPatternList.length === 1 ||
                                          itemPatternList.length === 0 ||
                                          id === 'create' ?
                                          (<DimensionDetailTableList
                                            schemeType={schemeTypeData}
                                            getNewUuid={getNewUuid}
                                            formDs={item.inspectionItemBasisDs}
                                            inspectBusinessType={item.inspectionItemBasisDs.current.get(
                                              'inspectBusinessType',
                                            )}
                                            tableDS={dimensionsItem.itemsDs}
                                            path={path}
                                            canEdit={canEdit}
                                            customizeTable={customizeTable}
                                            customizeForm={customizeForm}
                                            statisticsTableUpdate={upDateStatisticsTableRef}
                                            item={item}
                                            dimensionsItem={dimensionsItem}
                                            selectOptionValue={selectOptionValue}
                                            selectOptionFormat={selectOptionFormat}
                                            handleCreateInspection={handleCreateInspection}
                                            actObject={actObjectValue}
                                          />) :
                                          (<Tabs type={TabsType.card}>
                                            {itemPatternList?.map((val: any) => (
                                              <TabPane
                                                tab={val.itemPatternMeaning}
                                                key={val.itemPattern}
                                              >
                                                <DimensionDetailTableList
                                                  schemeType={schemeTypeData}
                                                  getNewUuid={getNewUuid}
                                                  formDs={item.inspectionItemBasisDs}
                                                  inspectBusinessType={item.inspectionItemBasisDs.current.get(
                                                    'inspectBusinessType',
                                                  )}
                                                  tableDS={dimensionsItem.itemsDs}
                                                  path={path}
                                                  canEdit={canEdit}
                                                  customizeTable={customizeTable}
                                                  customizeForm={customizeForm}
                                                  statisticsTableUpdate={upDateStatisticsTableRef}
                                                  item={item}
                                                  dimensionsItem={dimensionsItem}
                                                  selectOptionValue={selectOptionValue}
                                                  selectOptionFormat={selectOptionFormat}
                                                  handleCreateInspection={handleCreateInspection}
                                                  actObject={actObjectValue}
                                                />
                                              </TabPane>
                                            ))}
                                          </Tabs>)
                                        }
                                      </Panel>
                                    );
                                  },
                                )}
                              </Collapse>
                            </div>
                          )}
                        </>
                      </TabPane>
                    );
                  })}
                </Tabs>
              )}
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.title.startReviewInformation`).d('启动评审信息')} key="panel6">
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DETAIL`,
                },
                <Form dataSet={formDs} columns={1} labelWidth={112} disabled>
                  <TextArea name="launchApplicationInformation" disabled />
                </Form>,
              )}
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.title.activityReport`).d('活动报告')} key="panel7">
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DETAIL`,
                },
                <Form dataSet={formDs} columns={1} labelWidth={112}>
                  <TextArea name="actReport" disabled />
                  <Attachment {...attachment1Props} disabled={!canEdit} />
                </Form>,
              )}
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.title.exitReviewInformation`).d('退出评审信息')} key="panel8">
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DETAIL`,
                },
                <Form dataSet={formDs} columns={1} labelWidth={112}>
                  <TextArea name="exitApplicationInformation" disabled />
                </Form>,
              )}
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [
    modelPrompt,
    'tarzan.common',
  ],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.LINE_DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.ITEM_DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DMS_DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.ITEM_DRAWER`,
      `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.ATTR`,
    ],
  })(InspectionSchemeTable as any),
);
