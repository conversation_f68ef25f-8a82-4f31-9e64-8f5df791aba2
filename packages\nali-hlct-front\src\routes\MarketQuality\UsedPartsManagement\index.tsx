import React, { useEffect, useMemo, useState } from 'react';
import { Table, DataSet, Button, useModal, Form, TextField, Select, Lov, NumberField } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import ExcelExport from 'components/ExcelExport';
import notification from 'utils/notification';
import { API_HOST } from '@/utils/constants';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { observer } from 'mobx-react';
import { useRequest } from '@components/tarzan-hooks';
import axios from 'axios';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import { BASIC } from '@utils/config';
import { tableDS, detailDS } from './stores';
import { GetDefaultSite, GetTableColumn, SaveInfo } from './services';

const modelPrompt = 'tarzan.oldManagement.oldPartManagement';
const tenantId = getCurrentOrganizationId();

const UsedPartsManagement = observer(props => {
  const modal = useModal();
  const {
    match: { path },
    tableDs,
    detailDs,
  } = props;

  const [disabled, setDisabled] = useState(true);

  const { run: getDefaultSite } = useRequest(GetDefaultSite(), {
    manual: true,
  });
  const { run: saveInfo } = useRequest(SaveInfo(), {
    needPromise: true,
    manual: true,
  });
  const { run: getTableColumn } = useRequest(GetTableColumn(), {
    manual: true,
    needPromise: true,
  });
  const [defaultSite, setDefaultSite] = useState({});
  const [typeGroup, setTypeGroup] = useState<any>([]);

  useEffect(() => {
    getDefaultSite({
      onSuccess: res => {
        if (res?.siteId) {
          setDefaultSite(res);
        }
      },
    });
    getTableColumn({
      params: { typeGroup: 'YP.QIS.OLD_PART_INSPECT_ITEM' },
    }).then(res => {
      if (res?.typeGroup) {
        res.typeGroup.forEach(item => {
          const measure = item.tag;
          tableDs.addField(item.value, {
            name: item.value,
            label: item.meaning,
            type: FieldType.string,
            lookupCode: 'YP.QIS.OLD_PART_ITEM_INSPECT_RESULT',
            help: intl.get(`${modelPrompt}.help.info`, { measure }).d(`推荐处理防护措施: ${measure}`),
          });
        });
        setTypeGroup(res.typeGroup);
      }
    });
  }, []);

  const handleDataSetSelect = () => {
    const statusList = tableDs.selected.map(record => record.get('oldPartStatus'));
    if (statusList.length) {
      const data = statusList.every(item => {
        return item === 'OUT_STORAGE' || item === 'SCRAP';
      });
      setDisabled(!data);
    } else {
      setDisabled(true);
    }
  };

  useDataSetEvent(tableDs, 'select', handleDataSetSelect);
  useDataSetEvent(tableDs, 'selectAll', handleDataSetSelect);
  useDataSetEvent(tableDs, 'unselect', handleDataSetSelect);
  useDataSetEvent(tableDs, 'unselectAll', handleDataSetSelect);

  const columns = useMemo(() => {
    const columnsList: ColumnProps[] = [
      {
        name: 'materialLotCode',
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                handleOpenDetail(record);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'docNum',
      },
      {
        name: 'oldPartStatusDesc',
        align: ColumnAlign.center,
        renderer: ({ record, value }) => {
          switch (record?.get('oldPartStatus')) {
            case 'EN_ROUTE':
              return <Tag color="blue">{value}</Tag>;
            case 'INSPECTING':
              return <Tag color="purple">{value}</Tag>;
            case 'IN_STORAGE':
              return <Tag color="orange">{value}</Tag>;
            case 'OUT_STORAGE':
              return <Tag color="green">{value}</Tag>;
            default:
              return <Tag color="gray">{value}</Tag>;
          }
        },
      },
      { name: 'siteName' },
      { name: 'productType' },
      { name: 'batteryType' },
      { name: 'customerMaterialCode' },
      { name: 'customerMaterialName' },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'qty' },
      { name: 'receiveDate' },
      { name: 'receiveByName' },
      { name: 'locatorCode' },
      { name: 'inspectResult' },
      { name: 'outStorageDate' },
      { name: 'outStorageByName' },
    ];
    typeGroup.forEach(item => {
      columnsList.push({
        name: item.value,
        minWidth: 150,
        align: ColumnAlign.center,
      });
    });
    return columnsList;
  }, [typeGroup.length]);

  const handleOpenDetail = record => {
    if (record === 'create') {
      detailDs.setState('editing', false);
      detailDs.loadData([{ siteLov: defaultSite }]);
    } else {
      detailDs.current.set({
        ...record.toData(),
      });
      detailDs.setState('editing', true);
    }
    modal.open({
      drawer: true,
      title:
        record === 'create'
          ? intl.get(`${modelPrompt}.title.createOldBackApply`).d('新建旧件返回申请')
          : intl.get(`${modelPrompt}.title.oldBackApplyDetail`).d('旧件返回申请详情'),
      children: (
        <Form dataSet={detailDs} labelWidth="auto">
          <TextField name="materialLotCode" />
          <Lov name="siteLov" />
          <TextField name="docNum" />
          <Select name="productType" />
          <Select name="batteryType" />
          <TextField name="customerMaterialCode" />
          <TextField name="customerMaterialName" />
          <Lov name="materialLov" />
          <TextField name="materialName" />
          <NumberField name="qty" />
        </Form>
      ),
      onOk: handleSave,
    });
  };

  const handleSave = async () => {
    const validate = await detailDs.validate();
    if (!validate) {
      return false;
    }
    const res = await saveInfo({
      params: detailDs.toData()[0],
    })
    if (res?.success) {
      notification.success({});
      tableDs.query();
      return true;
    }
    return false;
  };

  const print = async () => {
    axios
      .post(
        `/hrpt/v1/${tenantId}/reports/QIS_OLD_PART_MANAGEMENT_PDF/data?oldPartIdList=${tableDs.selected.map(
          record => record.get('oldPartId'),
        )}&organizationId=${tenantId}`,
      )
      .then(res => {
        if (res) {
          const url = `${API_HOST}/hrpt/v1/${tenantId}/reports/export/QIS_OLD_PART_MANAGEMENT_PDF/PRINT?access_token=${getAccessToken()}&organizationId=${tenantId}&oldPartIdList=${tableDs.selected.map(
            record => record.get('oldPartId'),
          )}`;
          window.open(url, 'newWindow');
        }
      });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('旧件台账管理')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => {
            handleOpenDetail('create');
          }}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.createApply`).d('新建申请')}
        </PermissionButton>
        <Button onClick={print} disabled={!tableDs.selected.length}>
          {intl.get(`${modelPrompt}.button.printLabel`).d('打印标签')}
        </Button>
        <ExcelExport
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-old-part-management/export/destroy`}
          method="get"
          buttonText={intl.get(`${modelPrompt}.scrap`).d('报废')}
          queryParams={{ oldPartIdList: tableDs.selected.map(record => record.get('oldPartId')) }}
          otherButtonProps={{
            disabled,
          }}
        />
        <ExcelExport
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-old-part-management/export/ui`}
          method="get"
          buttonText={intl.get(`${modelPrompt}.export.button`).d('导出')}
          queryParams={{ oldPartIdList: tableDs.selected.map(record => record.get('oldPartId')) }}
          otherButtonProps={{
            disabled: !tableDs.selected?.length,
          }}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="UsedPartsManagement"
          customizedCode="UsedPartsManagement"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      const detailDs = new DataSet(detailDS());
      return {
        tableDs,
        detailDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(UsedPartsManagement),
);
