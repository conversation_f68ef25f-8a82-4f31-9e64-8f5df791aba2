/**
 * @Description: 产线审核计划管理平台-详情DS
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'key.hwms.front.CommissionedExperimentalPlatform';

const tableDetailDS = () => ({
  autoCreate: true,
  autoQuery: false,
  selection: false,
  forceValidate: true,
  fields: [
    {
      name: 'entrustApplyCode',
      label: intl.get(`${modelPrompt}.entrustApplyCode`).d('委托申请单编码'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'siteObj',
      label: intl.get(`${modelPrompt}.siteObj`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      type: FieldType.object,
      // textField: 'siteName',
      // valueField: 'siteId',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'siteId',
      bind: 'siteObj.siteId',
      type: FieldType.string,
    },
    {
      name: 'siteCode',
      bind: 'siteObj.siteCode',
      type: FieldType.string,
    },
    {
      name: 'materialObj',
      label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      ignore: 'always',
      type: FieldType.object,
      // textField: 'materialName',
      // valueField: 'materialId',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'materialId',
      bind: 'materialObj.materialId',
      type: FieldType.string,
    },
    {
      name: 'materialName',
      bind: 'materialObj.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'unitObj',
      label: intl.get(`${modelPrompt}.unitObj`).d('委托专业'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: 'always',
      type: FieldType.object,
      // textField: 'unitName',
      // valueField: 'unitId',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'entrustUnitId',
      bind: 'unitObj.unitId',
      type: FieldType.string,
    },
    // {
    //   name: 'unitName',
    //   bind: 'unitObj.unitName',
    //   type: FieldType.string,
    // },
    {
      name: 'entrustUserId',
      type: FieldType.number,
    },
    {
      name: 'entrustUserName',
      label: intl.get(`${modelPrompt}.entrustUserName`).d('委托人'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'sampleQty',
      label: intl.get(`${modelPrompt}.sampleQty`).d('样本数量'),
      type: FieldType.number,
      disabled: true,
    },
    {
      name: 'sampleUom',
      label: intl.get(`${modelPrompt}.sampleUom`).d('样本单位'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'expectCompleteTime',
      label: intl.get(`${modelPrompt}.expectCompleteTime`).d('期望完成时间'),
      type: FieldType.dateTime,
      disabled: true,
      // disabled: true,
    },
    {
      name: 'reason',
      label: intl.get(`${modelPrompt}.reason`).d('实验原因'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'sampleDisposalMethod',
      label: intl.get(`${modelPrompt}.sampleDisposalMethod`).d('测试后样本处理'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.SAMPLE_DISPOSAL_METHOD',
      disabled: true,
    },
    {
      name: 'relatedFileUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.relatedFileUuid`).d('相关支持文件'),
      bucketName: 'qms',
      disabled: true,
      // computedProps: {
      //   disabled: ({ record }) => {
      //     return !record.get('inspectItemLov') || (record.get('selectList') || {}).enclosure;
      //   },
      // },
    },
    {
      name: 'remark',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'oaApplyDesc',
      label: intl.get(`${modelPrompt}.oaApplyDesc`).d('OA审批意见'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'receiveSampleTime',
      label: intl.get(`${modelPrompt}.receiveSampleTime`).d('收样时间'),
      type: FieldType.dateTime,
      dynamicProps: {
        disabled: ({ record }) => record.get('taskStatus') === 'COMPLETED' || record.get('applyStatus') === 'PASS',
        required: ({ record }) => record.get('taskStatus') !== 'COMPLETED' && record.get('applyStatus') !== 'PASS',
      },
    },
    {
      name: 'startTime',
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
      type: FieldType.dateTime,
      dynamicProps: {
        disabled: ({ record }) => record.get('taskStatus') === 'COMPLETED' || record.get('applyStatus') === 'PASS',
        required: ({ record }) => record.get('taskStatus') !== 'COMPLETED' && record.get('applyStatus') !== 'PASS',
      },
    },
    {
      name: 'endTime',
      label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
      type: FieldType.dateTime,
      dynamicProps: {
        disabled: ({ record }) => record.get('taskStatus') === 'COMPLETED' || record.get('applyStatus') === 'PASS',
        required: ({ record }) => record.get('taskStatus') !== 'COMPLETED' && record.get('applyStatus') !== 'PASS',
      },
    },
  ],
});
const tableDetailLineDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  autoLocateFirst: true,
  forceValidate: true,
  // autoQuery: false,
  // selection: false,
  // // forceValidate: true,
  // paging: false,
  fields: [
    {
      name: 'itemSequence',
      label: intl.get(`${modelPrompt}.itemSequence`).d('序号'),
      type: FieldType.number,
    },
    {
      name: 'itemDesc',
      label: intl.get(`${modelPrompt}.itemDesc`).d('检验项描述'),
      type: FieldType.string,
    },
    {
      name: 'inspectMethod',
      label: intl.get(`${modelPrompt}.inspectMethod`).d('检验工具/方法'),
      type: FieldType.string,
    },
    {
      name: 'inspectStandard',
      label: intl.get(`${modelPrompt}.inspectStandard`).d('检验标准'),
      type: FieldType.string,
    },
    {
      name: 'sampleQty',
      label: intl.get(`${modelPrompt}.sampleQty`).d('抽样数量'),
      type: FieldType.string,
    },
    {
      name: 'splitSampleCode',
      label: intl.get(`${modelPrompt}.splitSampleCode`).d('样品编码'),
      type: FieldType.string,
    },
    {
      name: 'inspectValue',
      label: intl.get(`${modelPrompt}.inspectValue`).d('检验值'),
      type: FieldType.string,
      dynamicProps: {
        disabled: ({ record }) => record.get('taskStatus') === 'COMPLETED' || record.get('applyStatus') !== 'NEW',
        required: ({ record }) => record.get('taskStatus') !== 'COMPLETED' && record.get('applyStatus') === 'NEW',
      },
    },
    {
      name: 'reinspectValue',
      label: intl.get(`${modelPrompt}.reinspectValue`).d('复检值'),
      type: FieldType.string,
      dynamicProps: {
        disabled: ({ record }) => record.get('taskStatus') === 'COMPLETED' || record.get('applyStatus') !== 'REJECT',
        required: ({ record }) => record.get('taskStatus') !== 'COMPLETED' && record.get('applyStatus') === 'REJECT',
      },
    },
    {
      name: 'inspectUom',
      label: intl.get(`${modelPrompt}.inspectUom`).d('检验单位'),
      type: FieldType.string,
      required: true,
      // dynamicProps: {
      //   disabled: ({ record }) => !record.get('inspectValue') && !record.get('reinspectValue'),
      // },
    },
    {
      name: 'inspectUserName',
      label: intl.get(`${modelPrompt}.inspectUserName`).d('责任人'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'remark',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      type: FieldType.string,
      dynamicProps: {
        disabled: ({ record }) => !record.get('inspectValue') && !record.get('reinspectValue'),
      },
    },
    {
      name: 'applyStatus',
      type: FieldType.string,
    },
    {
      name: 'taskStatus',
      type: FieldType.string,
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      dynamicProps: {
        disabled: ({ record }) => record.get('taskStatus') === 'COMPLETED' || !['NEW', 'REJECT'].includes(record.get('applyStatus')),
      },
    },
  ],
});
export { tableDetailDS, tableDetailLineDS };