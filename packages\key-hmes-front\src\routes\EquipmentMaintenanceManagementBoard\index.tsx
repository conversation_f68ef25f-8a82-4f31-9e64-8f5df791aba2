import React, { useEffect, useState, useMemo } from 'react';
import intl from 'utils/intl';
import styles from './index.module.less';
import { Content } from 'components/Page';
import { DataSet, Lov } from 'choerodon-ui/pro';
import { Spin } from 'choerodon-ui';
import { TopFilterDS } from './stores';
import request from 'utils/request';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from "utils/notification";
import MaintenanceCompletionRateBoard from './components/MaintenanceCompletionRateBoard';
import MonthlyMaintenanceCompletionTrendChart from './components/MonthlyMaintenanceCompletionTrendChart';
import DailyStaffCompletionRank from './components/DailyStaffCompletionRank';
import EquipmentInfo from './components/EquipmentInfo';
import { FullScreenContainer } from '@jiaminghi/data-view-react';
import FaultStatisticsTable from './components/FaultStatisticsTable';
import DashboardStatistic from './components/DashboardStatistic';

const modelPrompt = 'tarzan.wms.EquipmentMaintenanceManagementBoard';
const tenantId = getCurrentOrganizationId();

// 列表当天未完成的保养单
const maintenanceSheetInfoUrl = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-repair/dayUnFinish`;

// 日完成数
const dailyCompletionInfoUrl = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-repair/peopleSort`;

// 故障次数
const faultCountInfoUrl = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-repair/yearUnWork`;

// 顶部五个信息
const topListUrl = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-repair/selectTopList`;

const Main = ({ isFullScreen }) => {

    const [initLoading, setInitLoading] = useState<boolean>(false);
    const [assetLocationId, setAssetLocationId] = useState<any>(null);
    const [timers, setTimers] = useState<any>(null);
    const [maintenanceSheetInfoData, setMaintenanceSheetInfoData] = useState([]); // 月度保养单日完成数数据
    const [dailyCompletionCounts, setDailyCompletionCounts] = useState([]); // 日均完成数数据
    const [dailyFaultCounts, setDailyFaultCounts] = useState([]); // 日均故障数数据

    const [statistics, setStatistics] = useState({
        breakTotal: null,
        maintainTotal: null,
        finishTotal: null,
        waitTotal: null,
        executeTotal: null,
    });

    const topFilterDs = useMemo(() => new DataSet(TopFilterDS()), []);


    const getTimers = async () => {
        const url = `/hpfm/v1/${tenantId}/lovs/value/batch?QMS.MANAGEMENT_FREQUENCY=QMS.MANAGEMENT_FREQUENCY`
        const result = await request(url, {
            method: 'GET',
        });
        const data = result['QMS.MANAGEMENT_FREQUENCY'].filter(item => item.value === 'INCOME')
        if (data.length > 0) {
            setTimers(Number(data[0].meaning))
        }
    }

    // 获取保养单列表数据
    const getMaintainTotalData = async () => {
        setInitLoading(true);

        const assetLocationId = topFilterDs.current?.get('assetLocationId');

        const query = assetLocationId ? { assetLocationId } : {};

        const result = await request(maintenanceSheetInfoUrl, {
            method: 'GET',
            query,
        });

        if (result && !result.failed) {
            setMaintenanceSheetInfoData(result);
        } else {
            notification.warning({ description: result.message });
        }

        setInitLoading(false);
    };

    const handleChangeLocationLov = (value) => {

        if (value && value.assetLocationId) {
            getMaintainTotalData();
        }
    }


    const handleClearLocationLov = () => {
        if (topFilterDs?.current) {
            topFilterDs.current.set("assetLocationId", null);
        }
        getMaintainTotalData();
    }


    // 获取日完成数数据
    const getDailyCompletionData = async () => {
        setInitLoading(true);

        const assetLocationId = topFilterDs.current?.get('assetLocationId');

        const query = assetLocationId ? { assetLocationId } : {};

        const result = await request(dailyCompletionInfoUrl, {
            method: 'GET',
            query,
        });

        result.sort((a, b) => parseInt(b.total) - parseInt(a.total));

        if (result && !result.failed) {
            setDailyCompletionCounts(result);
        } else {
            notification.warning({ description: result.message });
        }
        setInitLoading(false)
    }

    // 故障次数数据
    const getDailyFaultCounts = async () => {
        setInitLoading(true);

        const assetLocationId = topFilterDs.current?.get('assetLocationId');

        const query = assetLocationId ? { assetLocationId } : {};

        const result = await request(faultCountInfoUrl, {
            method: 'GET',
            query,
        });

        result.sort((a, b) => parseInt(b.total) - parseInt(a.total));

        if (result && !result.failed) {
            setDailyFaultCounts(result);
        } else {
            notification.warning({ description: result.message });
        }
        setInitLoading(false)
    };

    // 获取顶部信息展示列表
    const getTopListData = async () => {

        const assetLocationId = topFilterDs.current?.get('assetLocationId');

        const query = assetLocationId ? { assetLocationId } : {};

        const result = await request(topListUrl, {
            method: 'GET',
            query,
        });

        if (result && !result.failed) {
            const { breakTotal, maintainTotal, finishTotal, waitTotal, executeTotal } = result;
            setStatistics({
                breakTotal,
                maintainTotal,
                finishTotal,
                waitTotal,
                executeTotal,
            });
        } else {
            notification.warning({ description: result.message });
        }
    };


    useEffect(() => {
        let time
        if (timers) {
            time = setInterval(() => {
                getMaintainTotalData();
                getDailyCompletionData();
                getDailyFaultCounts();
                getTopListData();
            }, timers * 60000)
        }
        return () => {
            clearInterval(time)
        }
    }, [timers])

    useEffect(() => {
        getTimers().then(async () => {
            getMaintainTotalData();
            getDailyCompletionData();
            getDailyFaultCounts();
            getTopListData();
        });

        topFilterDs.addEventListener("update", handleChangeAssetSetLov);
        return () => {
            topFilterDs.removeEventListener("update", handleChangeAssetSetLov);
        };
    }, []);

    const handleChangeAssetSetLov = ({ name, value }) => {
        if (name === "assetSetLov" && value?.assetSetId) {
            setAssetLocationId(value.assetSetId);
        }
    };


    return (
        <>
            <Content style={{ padding: 0, margin: 0, height: '100%' }}>
                {/* loading */}
                <Spin spinning={initLoading} style={{ height: '100%' }}>
                    <div className={styles['dashboard-container']}>
                        {/* header */}
                        <div className={styles['dashboard-title']}>
                            <div className={styles['dashboard-title-left']}>
                                <div className={styles['dashboard-title-left-one']} />
                                <div className={styles['dashboard-title-left-two']} />
                                <div className={styles['dashboard-title-left-three']}>
                                    {intl.get(`${modelPrompt}.title.DeviceStatusMonitoringBoard`).d('设备维保管理看板')}
                                </div>
                            </div>
                            <div className={styles['dashboard-title-right']}>
                                <DashboardStatistic
                                    title={intl.get(`${modelPrompt}.field.totalNumberOfDevices`).d('故障单总数')}
                                    value={statistics.breakTotal}
                                    style={{
                                        outsideStyle: {
                                            width: '44%'
                                        },
                                        insideStyle: {
                                            width: '80%'
                                        }
                                    }}
                                />
                                <DashboardStatistic
                                    title={intl.get(`${modelPrompt}.field.totalNumberOfDevices`).d('保养单总数')}
                                    value={statistics.maintainTotal}
                                    style={{
                                        outsideStyle: {
                                            width: '44%'
                                        },
                                        insideStyle: {
                                            width: '80%'
                                        }
                                    }}
                                />
                                <DashboardStatistic
                                    title={intl.get(`${modelPrompt}.field.totalNumberOfDevices`).d('已完成')}
                                    value={statistics.finishTotal}
                                    style={{}}
                                />
                                <DashboardStatistic
                                    title={intl.get(`${modelPrompt}.field.totalNumberOfDevices`).d('待处理')}
                                    value={statistics.waitTotal}
                                    style={{}}
                                />
                                <DashboardStatistic
                                    title={intl.get(`${modelPrompt}.field.totalNumberOfDevices`).d('执行中')}
                                    value={statistics.executeTotal}
                                    style={{}}
                                />

                                <div className={styles['dashboard-title-right-device-location']}>
                                    <div className={styles['dashboard-title-right-device-location-text']}>
                                        {intl.get(`${modelPrompt}.field.totalNumberOfDevices`).d('设备位置')}
                                    </div>
                                    <div className={styles['dashboard-title-left-device-location-dom']}>
                                        <div className={styles['top-select']}>
                                            <Lov
                                                dataSet={topFilterDs}
                                                onChange={handleChangeLocationLov}
                                                name="equipmentLocationLov"
                                                onClear={handleClearLocationLov}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Content */}
                        <div className={styles['dashboard-content']}>
                            <div className={styles['dashboard-row-up']}>
                                <div className={styles['dashboard-item-left']}>
                                    <MaintenanceCompletionRateBoard isFullScreen={isFullScreen} assetLocationId={assetLocationId} timers={timers} />
                                </div>
                                <div className={styles['dashboard-item-middle']}>
                                    <MonthlyMaintenanceCompletionTrendChart isFullScreen={isFullScreen} assetLocationId={assetLocationId} timers={timers} />
                                </div>
                                <div className={styles['dashboard-item-right']}>
                                    <DailyStaffCompletionRank dailyCompletionCounts={dailyCompletionCounts} />
                                </div>
                            </div>
                            <div className={styles['dashboard-col-down']}>
                                <div className={styles['dashboard-down-left']}>
                                    <EquipmentInfo info={maintenanceSheetInfoData} />
                                </div>
                                <div className={styles['dashboard-down-right']}>
                                    <FaultStatisticsTable info={dailyFaultCounts} />
                                </div>
                            </div>
                        </div>
                    </div>
                </Spin>
            </Content>
        </>
    );
};

const EquipmentMaintenanceManagementBoard = () => {
    const [isFullScreen, setIsFullScreen] = useState(false); // 是否全屏

    const windowFullScreenChange = () => {
        if (document.fullscreenElement) {
            // console.log('进入全屏');
            setIsFullScreen(true);
        } else {
            // console.log('退出全屏');
            setIsFullScreen(false);
        }
    };
    useEffect(() => {
        document.addEventListener('fullscreenchange', windowFullScreenChange);
        return () => {
            document.removeEventListener('fullscreenchange', windowFullScreenChange);
        };
    }, []);

    return (
        <>
            <div className={styles['screen-container']}>
                {isFullScreen ? (
                    <FullScreenContainer>
                        <Main
                            isFullScreen={isFullScreen}
                        />
                    </FullScreenContainer>
                ) : (
                    <Main
                        isFullScreen={isFullScreen}
                    />
                )}
            </div>
        </>
    );
};
export default EquipmentMaintenanceManagementBoard;