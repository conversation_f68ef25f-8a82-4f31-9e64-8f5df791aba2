/*
 * @Description: 资产事务头行表单渲染
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-03-28 14:55:30
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */
import React from 'react';
import QueryBarNew from 'alm/components/QueryBarNew';
import { getCurrentOrganizationId } from 'utils/utils';
import { queryOrgByEmployee } from 'alm/services/organizationService';
import OrgPartnerLov from 'alm/components/OrgPartnerLov';
import { TextField, Select, Output, Lov, Form } from 'choerodon-ui/pro';
// import { ASSET_TRANSFER } from '../Constants';
import { BasicCode } from '../enum';

const organizationId = getCurrentOrganizationId();

/**
 * @description: 渲染列表自定义查询条件表单
 * @param {*} props
 * @param {BasicCode} basicCode
 * @return {*}
 */
function queryBarRender(props, basicCode: BasicCode) {
  const defaultQueryFields = () => [
    <TextField name="changeNum" />,
    <TextField name="titleOverview" />,
    <Select name="processStatus" />,
  ];
  const queryProps = {
    ...props,
    moduleName: 'ASSET_TRANSACTION',
    systemFields: ['changeNum', 'titleOverview', 'processStatus'],
    defaultQueryFields: defaultQueryFields(),
    extQueryParams: {
      basicCode,
    },
  };
  return <QueryBarNew {...queryProps} />;
}

/**
 * @description: 头基础信息以及动态字段只读表单渲染
 * @param {*} dynamicProps
 * @return {*}
 */
function baseViewFormRender(dynamicProps) {
  return (
    <>
      <Output name="changeNum" />
      <Output name="titleOverview" />
      <Output name="transactionTypeIdMeaning" />
      <Output name="processStatusMeaning" />
      <Output name="principalPersonLov" />
      <Output name="usingOrgName" />
      <Output name="reason"/>
      {dynamicProps?.outputDisplayRender}
    </>
  );
}

/**
 * @description: 头基础信息以及动态字段编辑表单渲染
 * @param {*} dynamicProps
 * @param {*} customRender
 * @return {*}
 */
function baseEditFormRender(detailDs, dynamicProps, customRender?) {
  const handleChangeUsingOrg = (record, type) => {
    detailDs.current.set('usingOrgId', type === 'PLATFORM' ? record.unitId : record.orgId);
    detailDs.current.set('usingOrgName', type === 'PLATFORM' ? record.unitName : record.orgName);
    detailDs.current.set('usingOrgType', type);
  };
  const handleChangePrincipal = record => {
    if (record) {
      queryOrgByEmployee({ tenantId: organizationId, employeeId: record?.employeeId }).then(
        orgInfo => {
          detailDs?.current?.set({
            usingOrgId: orgInfo?.unitId,
            usingOrgName: orgInfo?.unitName,
            usingOrgType: orgInfo?.orgType,
          });
        }
      );
    }
  };
  return (
    <>
      <TextField name="changeNum" restrict="a-zA-Z0-9" />
      <TextField name="titleOverview" />
      <Lov name="transactionTypeLov" />
      <Select name="processStatus" />
      <Lov name="principalPersonLov" onChange={handleChangePrincipal} />
      <OrgPartnerLov required name="usingOrgName" handleOk={handleChangeUsingOrg} />,{customRender}
      {dynamicProps?.formDisplayRender}
      <TextField name='reason'/>
    </>
  );
}

/**
 * @description: 资产事务行以及动态字段只读表单渲染
 * @param {*} record
 * @param {*} targetAssetStatusDisabled
 * @param {*} dynamicProps
 * @param {*} customRender 针对某个资产事务基础类型特有的界面内容
 * @return {*}
 */
function lineViewFormRender(record, targetAssetStatusDisabled, dynamicProps, customRender?) {
  return (
    <Form record={record}>
      <Output name="objectNum" />
      <Output name="objectDesc" />
      {customRender}
      {!targetAssetStatusDisabled && <Output name="oldAssetStatus" />}
      {!targetAssetStatusDisabled && <Output name="newAssetStatus" />}
      {dynamicProps?.outputDisplayRender}
    </Form>
  );
}

/**
 * @description: 资产事务行以及动态字段编辑表单渲染
 * @param {*} record
 * @param {*} targetAssetStatusDisabled
 * @param {*} dynamicProps
 * @param {*} customRender 针对某个资产事务基础类型特有的界面内容
 * @return {*}
 */
function lineEditFormRender(record, targetAssetStatusDisabled, dynamicProps, customRender?) {
  return (
    <Form record={record}>
      <Output name="objectNum" />
      <Output name="objectDesc" />
      {customRender}
      {!targetAssetStatusDisabled && <Lov name="oldAssetStatus" disabled />}
      {!targetAssetStatusDisabled && (
        <Lov name="newAssetStatus" disabled={targetAssetStatusDisabled} />
      )}
      {dynamicProps?.formDisplayRender}
    </Form>
  );
}

export {
  queryBarRender,
  baseViewFormRender,
  baseEditFormRender,
  lineViewFormRender,
  lineEditFormRender,
};
