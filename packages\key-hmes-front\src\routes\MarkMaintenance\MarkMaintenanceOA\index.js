import React, { useMemo, useEffect } from 'react';
import { DataSet, Table, Attachment } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Content } from 'components/Page';
// import { routerRedux } from 'dva/router';
import formatterCollections from 'utils/intl/formatterCollections';
import { tableOADS } from '../stores/MarkMaintenanceDS';

const MarkMaintenanceOA = props => {
  const {
    match: { params },
  } = props;
  const tableDs = useMemo(() => new DataSet(tableOADS()), []);

  useEffect(() => {
    if (params && params.code) {
      tableDs.setQueryParameter('markingCode', params.code);
    }
    tableDs.query();
  }, []);

  const applyBasisProps = {
    name: 'applyBasis',
    bucketName: 'hmes',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['image/*'],
    viewMode: 'popup',
    readOnly: true,
  };
  const attachmentProps = {
    name: 'attachments',
    bucketName: 'hmes',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.xlsx', '.xls', 'image/*', '.pptx'],
    viewMode: 'popup',
    readOnly: true,
  };

  const columns = [
    // 站点
    {
      name: 'siteCode',
      align: 'left',
    },
    {
      name: 'markingCode',
    },
    {
      name: 'statusValue',
    },
    {
      name: 'typeValue',
    },
    {
      name: 'markingContentValue',
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      renderer: ({ value }) => (
        <Badge
          status={value === 'FORBIDDEN' ? 'error' : value === 'INVALID' ? 'yellow' : 'success'}
          text={value === 'FORBIDDEN' ? '禁用' : value === 'INVALID' ? '无效' : '有效'}
        ></Badge>
      ),
    },
    {
      name: 'expirationDate',
    },
    {
      name: 'applyBasis',
      width: 120,
      renderer: ({ record }) => {
        return <Attachment {...applyBasisProps} record={record} disabled />;
      },
    },
    {
      name: 'operationDesc',
    },
    {
      name: 'interceptionDisposalWay',
    },
    {
      name: 'disposalResult',
    },
    {
      name: 'attachments',
      width: 120,
      renderer: ({ record }) => {
        return <Attachment {...attachmentProps} record={record} disabled />;
      },
    },
  ];

  return (
    <div className="hmes-style">
      <Content>
        <Table dataSet={tableDs} columns={columns} customizedCode="MarkMaintenance" />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.MarkMaintenance', 'tarzan.common'],
})(MarkMaintenanceOA);
