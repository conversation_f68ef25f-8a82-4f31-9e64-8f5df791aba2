/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-10 13:39:12
 * @LastEditors: 20379 <EMAIL>
 * @LastEditTime: 2023-12-30 20:44:30
 * @Description:
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Header, Content } from 'components/Page';
import {
  DataSet,
  Form,
  TextField,
  DateTimePicker,
  Table,
  Button,
  Lov,
  NumberField,
  Spin,
  Select,
  Switch,
} from 'choerodon-ui/pro';
import { Badge, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { ColumnAlign, TableColumnTooltip } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { isNull, flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { queryMapIdpValue } from 'services/api';
import { BASIC, API_HOST } from '@/utils/config';
import { useRequest } from '@components/tarzan-hooks';
import { headDS, tableDS } from '../stores/OutsourcingBillDS';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.purchase.outsourcingManage';

// 保存
export function Preservation() {
  return {
    url: `${API_HOST}${BASIC.HWMS_BASIC}/v1/${getCurrentOrganizationId()}/mt-out-source/out-source-replenishment/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_SUPPLEMENT.HEAD,${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_SUPPLEMENT.LINE`,
    method: 'POST',
  };
}

// 根据库位获取现有量的接口
export function AvailableQuantity() {
  return {
    url: `${API_HOST}${BASIC.HWMS_BASIC}/v1/${getCurrentOrganizationId()}/mt-inv-onhand-quantity/sum-available-qty/ui`,
    method: 'POST',
  };
}

const CreateDetail = props => {
  const {
    match: { params, path },
    customizeForm,
    customizeTable,
  } = props;

  const headDs = useMemo(() => new DataSet(headDS()), []);

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const [isSubmit, setIsSubmit] = useState(false); // 是否已保存

  const preservation = useRequest(Preservation(), {
    manual: true,
  });

  const availableQuantity = useRequest(AvailableQuantity(), {
    manual: true,
  });

  useEffect(() => {
    handleQuery();
  }, [params.id]);

  // 查询头
  const handleQuery = async () => {
    headDs.setQueryParameter('instructionDocId', params.id);
    const res = await headDs.query();
    handleFetchDefaultSourceSystem();
    if (res?.success) {
      const { outSourceReplenishmentSaveLines } = res.rows;
      tableDs.loadData(
        outSourceReplenishmentSaveLines.map((item, index) => {
          return {
            ...item,
            lineNumber: (index + 1) * 10,
            sumAvailableQty: 0,
          };
        }),
      );
    }
  };

  const handleFetchDefaultSourceSystem = () => {
    queryMapIdpValue({
      sourceSystemList: 'SOURCE_SYSTEM',
    }).then(res => {
      if(res) {
        const defaultSourceSystem = res.sourceSystemList.find(e => e.tag === "Y");
        if(defaultSourceSystem) {
          headDs.current?.set('sourceSystem', defaultSourceSystem.value);
        }
      }
    });
  };

  // 允差标识改变时
  const handleFlag = (record, value) => {
    if (value === 'N') {
      record.set('toleranceType', null);
      record.set('toleranceMaxValue', null);
      record.set('toleranceMinValue', null);
    }
  };

  const getPageDetail = (record, data) => {
    const { materialId, revisionCode, siteId } = record.toData();
    const { locatorId } = data;
    availableQuantity.run({
      params: {
        materialId,
        revisionCode,
        locatorId,
        siteId,
      },
      onSuccess: res => {
        record.set('sumAvailableQty', res);
      },
    });
  };

  // 退货仓库改变时
  const handleFromLocator = (record, data) => {
    if (!isNull(data)) {
      getPageDetail(record, data);
    } else {
      record.set('sumAvailableQty', 0);
    }
  };

  const columns: ColumnProps[] = [
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'materialCode',
      align: ColumnAlign.left,
      tooltip: TableColumnTooltip.overflow,
      width: 180,
    },
    {
      name: 'revisionCode',
      align: ColumnAlign.left,
      tooltip: TableColumnTooltip.overflow,
      width: 120,
    },
    {
      name: 'materialName',
      align: ColumnAlign.left,
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'siteCode',
      align: ColumnAlign.left,
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'quantity',
      width: 120,
      align: ColumnAlign.right,
      editor: () => (!isSubmit ? <NumberField nonStrictStep precision={6} step={1} /> : false),
    },
    {
      name: 'uomCode',
      align: ColumnAlign.left,
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'fromLocatorLov',
      width: 170,
      align: ColumnAlign.left,
      editor: record =>
        !isSubmit ? <Lov onChange={value => handleFromLocator(record, value)} noCache /> : false,
    },
    {
      name: 'sumAvailableQty',
      align: ColumnAlign.right,
      tooltip: TableColumnTooltip.overflow,
      width: 120,
    },

    {
      name: 'toleranceFlag',
      align: ColumnAlign.left,
      width: 150,
      editor: record =>
        !isSubmit ? (
          <Switch readOnly={isSubmit} onChange={value => handleFlag(record, value)} />
        ) : (
          false
        ),
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      align: ColumnAlign.left,
      width: 150,
      editor: () => (!isSubmit ? <Select /> : false),
    },
    {
      name: 'toleranceMaxValue',
      align: ColumnAlign.left,
      width: 150,
      editor: () => (!isSubmit ? <NumberField nonStrictStep precision={6} step={1} /> : false),
    },
    {
      name: 'toleranceMinValue',
      align: ColumnAlign.left,
      width: 150,
      editor: () => (!isSubmit ? <NumberField nonStrictStep precision={6} step={1} /> : false),
    },
  ];

  // 保存校验
  const handleSaveCheck = async () => {
    const validate = await headDs.validate();
    const validateTable = await tableDs.validate();
    if (validate && validateTable) {
      const listData = [];
      tableDs.toData().forEach(item => {
        // @ts-ignore
        if (item.quantity > 0) {
          // @ts-ignore
          listData.push({
            ...item,
          });
        }
      });
      preservation.run({
        params: {
          outSourceReplenishmentSaveHeader: {
            ...headDs.toData()[0],
            instructionDocId: params.id,
          },
          outSourceReplenishmentSaveLines: listData,
        },
        onSuccess: res => {
          // eslint-disable-next-line no-unused-expressions
          headDs.current?.set('instructionDocNum', res);
          setIsSubmit(true);
          tableDs.loadData(listData);
          notification.success({
            message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
          });
        },
      });
    }
  };

  // 取消
  const handleCancel = () => {
    props.history.push({
      pathname: `/hmes/purchase/outsourcing-manage/list`,
      state: {
        queryFlag: isSubmit,
      },
    });
  };

  return (
    <div className="hmes-style">
      <Spin spinning={preservation.loading}>
        <Header
          title={intl.get(`${modelPrompt}.create.replenishmentSheet`).d('创建外协补料单')}
          backPath="/hmes/purchase/outsourcing-manage/list"
          onBack={handleCancel}
        >
          {!isSubmit && (
            <div>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <PermissionButton
                icon="save"
                type="c7n-pro"
                color={ButtonColor.primary}
                onClick={handleSaveCheck}
                loading={preservation.loading}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </PermissionButton>
            </div>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
            <Panel
              header={intl.get(`${modelPrompt}.title.head`).d('头信息')}
              key="basicInfo"
              dataSet={headDs}
            >
              <Spin dataSet={headDs}>
                {customizeForm(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_SUPPLEMENT.HEAD`,
                  },
                  <Form
                    dataSet={headDs}
                    labelLayout={LabelLayout.horizontal}
                    labelWidth={112}
                    columns={3}
                    disabled={isSubmit}
                  >
                    <TextField disabled name="instructionDocNum" />
                    <TextField disabled name="supplierName" />
                    <TextField disabled name="supplierSiteName" />
                    <DateTimePicker name="expectedArrivalTime" />
                    <Select name='sourceSystem' disabled />
                    <TextField name="remark" />
                  </Form>,
                )}
              </Spin>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.line`).d('行信息')}
              key="location"
              dataSet={tableDs}
            >
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_SUPPLEMENT.LINE`,
                },
                <Table dataSet={tableDs} columns={columns} />,
              )}
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.hmes.purchase.outsourcingManage', 'tarzan.common'] }),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_SUPPLEMENT.HEAD`, `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_SUPPLEMENT.LINE`] }),
)(CreateDetail);
