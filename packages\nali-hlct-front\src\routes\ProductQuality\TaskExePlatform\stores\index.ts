/**
 * @Description: 检证任务执行平台-主界面DS
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from "moment";

const modelPrompt = 'tarzan.inspectExecute.taskExePlatform';
const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'verificationTaskGroupId',
  queryFields: [
    {
      name: 'taskGroupCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskGroupCode`).d('任务组编码'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'taskGroupStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('状态'),
      lookupCode: 'YP.QIS.VERIFICATION_TASK_GROUP_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'responsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('责任人'),
      lovCode: 'YP.QIS.USER_UNIT',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'responsibleUserId',
      bind: 'responsibleUserLov.id',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('责任部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
    },
    {
      name: 'departmentId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'planEndTimeFrom',
      type: FieldType.dateTime,
      format: 'YYYY-MM-DD',
      label: intl.get(`${modelPrompt}.planTimeFrom`).d('计划完成日期从'),
      max: 'planEndTimeTo',
    },
    {
      name: 'planEndTimeTo',
      type: FieldType.dateTime,
      format: 'YYYY-MM-DD',
      label: intl.get(`${modelPrompt}.planTimeTo`).d('计划完成日期至'),
      min: 'planEndTimeFrom',
    },
    {
      name: 'verificationFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationFrom`).d('检证来源'),
      lookupCode: 'YP.QIS.VERIFICATION_FROM',
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
    },
    {
      name: 'verificationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationCode`).d('检证项目编码'),
    },
    {
      name: 'createMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createType`).d('创建方式'),
      lookupCode: 'YP.QIS.VERIFICATION_CREATE_METHOD',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
      lookupCode: 'YP.QIS.PRODUCT_TYPE',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料描述'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialName',
      lovPara: { tenantId },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'itemGroup',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemGroup`).d('产品对象'),
      // bind: 'materialLov.itemGroupDesc',
      lookupCode: 'YP.QMS.ITEM_GROUP',
    },
    {
      name: 'verificationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationType`).d('检证类型'),
      lookupCode: 'YP.QIS.VERIFICATION_TYPE',
    },
    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检证阶段'),
      lookupCode: 'YP.QIS.VERIFICATION_PERIOD',
    },
    {
      name: 'createPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createPerson`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'createPersonLov.id',
    },
  ],
  fields: [
    {
      name: 'taskGroupCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskGroupCode`).d('任务组编号'),
    },
    {
      name: 'taskGroupStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('状态'),
      lookupCode: 'YP.QIS.VERIFICATION_TASK_GROUP_STATUS',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'responsibleUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('责任人'),
    },
    {
      name: 'lastResponsibleUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastResponsibleUserName`).d('上一责任人'),
    },
    {
      name: 'departmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.departmentName`).d('责任部门'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成日期'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际完成时间'),
    },
    {
      name: 'verificationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationCode`).d('检证项目编码'),
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
    },
    {
      name: 'createMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createType`).d('创建方式'),
      lookupCode: 'YP.QIS.VERIFICATION_CREATE_METHOD',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
      lookupCode: 'YP.QIS.PRODUCT_TYPE',
    },
    {
      name: 'verificationFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationFrom`).d('检证来源'),
    },
    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检证阶段'),
    },
    {
      name: 'verificationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationType`).d('检证类型'),
    },
    {
      name: 'failureMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureMode`).d('失效模式'),
      lookupCode: 'YP.QIS.FAILURE_MODE',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'itemGroup',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemGroup`).d('产品对象'),
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemId',
      type: FieldType.number,
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建日期'),
    },
  ],
  transport: {
    read: ({data}) => {
      const _data = { ...data };
      const keyList = [
        'planEndTimeFrom',
        'planEndTimeTo',
      ];
      keyList.forEach(key => {
        if (_data[key]) {
          _data[key] = moment(_data[key]).format('YYYY-MM-DD');
        }
      });
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-platform-execute/header/ui`,
        method: 'GET',
        data: _data,
      };
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'taskId',
  fields: [
    {
      name: 'taskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskCode`).d('检证任务编码'),
    },
    {
      name: 'taskContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskContent`).d('任务内容'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成日期'),
    },
    {
      name: 'applyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyFlag`).d('是否适用'),
      lookupCode: 'YP.QIS.Y_N',
    },
    {
      name: 'taskResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskResult`).d('检证结果'),
    },
    {
      name: 'noApplyReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.noApplyReason`).d('不适用原因'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.enclosure`).d('检证附件'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-platform-execute/line/ui`,
        method: 'GET',
      };
    },
  },
});

export { headDS, lineDS };
