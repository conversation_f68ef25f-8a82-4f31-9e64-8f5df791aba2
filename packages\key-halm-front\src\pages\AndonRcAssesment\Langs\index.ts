/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2021-10-29 10:21:02
 * @LastEditTime: 2021-11-09 15:14:13
 * @LastEditors: <<EMAIL>>
 */
/**
 * 委外申请类型-多语言
 * @date 2021-09-9
 * <AUTHOR> <<EMAIL>>
 * @version: 0.0.1
 * @copyright: Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';
// import getCommonLangs from 'alm-src/langs';
import getCommonLangs from 'alm/langs';

const getLang = key => {
  const PREFIX = 'aori.andonRcAssesment';
  const MODEL_PREFIX = `${PREFIX}.model.andonRcAssesment`;
  const LANGS = {
    PREFIX,
    ...getCommonLangs(),
    // titile
    HEADER: intl.get(`${PREFIX}.title.andonRcAssesment`).d('安灯故障字典'),
    HEADER_IMPORT: intl.get(`${PREFIX}.title.andonRcAssesmentImport`).d('安灯故障字典导入'),
    // model
    EVALITEM_CODE: intl.get(`${MODEL_PREFIX}.evalItemCode`).d('故障编码'),
    EVALITEM_DESC: intl.get(`${MODEL_PREFIX}.evalItemDesc`).d('故障描述'),
    RELATE_ASSET: intl.get(`${MODEL_PREFIX}.relatedAsset`).d('关联设备'),
    DESC: intl.get(`${MODEL_PREFIX}.description`).d('备注信息'),
    CONFIRM_EMPLOYEE: intl.get(`${MODEL_PREFIX}.confirmEmployee`).d('确认人'),
    CREATION_DATE: intl.get(`${MODEL_PREFIX}.creationDate`).d('创建时间'),
    LAST_UPDATE_DATE: intl.get(`${MODEL_PREFIX}.lastUpdateDate`).d('最近更新时间'),
    UPDATE_EMPLOYEE: intl.get(`${MODEL_PREFIX}.updateEmployee`).d('更新人'),
  };
  return LANGS[key];
};

export default getLang;
