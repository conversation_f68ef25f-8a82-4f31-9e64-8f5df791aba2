/**
 * @Description: MSA分析管理平台-GRR图表界面
 * @Author: <EMAIL>
 * @Date: 2023/8/24 15:42
 */
import React, { useState, useEffect, useRef } from 'react';
import { Col, Row } from 'choerodon-ui';
import {
  max as lodashMax,
  min as lodashMin,
  floor as lodashFloor,
  ceil as lodashCeil,
} from 'lodash';
import echarts from 'echarts';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import styles
  from "@/routes/hwms/MsaAnalysisManagementPlatform/MsaAnalysisManagementPlatformAnalysisDetail/index.modules.less";

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
let grrVariationChart; // 变差构成图
let grrColumnChart; // 零件链图
let xBarChart; // xBar图chart
let rChart; // R图chart
let grrUser<PERSON>hart; // 测量人链图
let grrUserColumnChart; // 测量人与零件交互作用图

const colorList = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
];

const symbolList = ['circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow'];

const GRRGraphic = ({ dataSoure, analyseResultDs }) => {
  const [pageInit, setPageInit] = useState(false);
  const grrVariationChartRef = useRef(null);
  const grrColumnChartRef = useRef(null);
  const xBarChartRef = useRef(null);
  const rChartRef = useRef(null);
  const grrUserChartRef = useRef(null);
  const grrUserColumnChartRef = useRef(null);

  useEffect(() => {
    if (!dataSoure.data?.length) {
      return;
    }
    if (!pageInit) {
      grrVariationChart = echarts.init(grrVariationChartRef.current);
      grrColumnChart = echarts.init(grrColumnChartRef.current);
      xBarChart = echarts.init(xBarChartRef.current);
      rChart = echarts.init(rChartRef.current);
      grrUserChart = echarts.init(grrUserChartRef.current);
      grrUserColumnChart = echarts.init(grrUserColumnChartRef.current);
      window.onresize = () => {
        setTimeout(() => {
          grrVariationChart.resize();
          grrColumnChart.resize();
          xBarChart.resize();
          rChart.resize();
          grrUserChart.resize();
          grrUserColumnChart.resize();
        }, 300);
      };
    }
    handleInitGrrVariationChart(dataSoure);
    handleInitGrrColumnChart(dataSoure);
    handleInitXBarChart(dataSoure);
    handleInitRChart(dataSoure);
    handleInitGrrUserChart(dataSoure);
    handleInitGrrUserColumnChart(dataSoure);
  }, [dataSoure.data?.length]);

  // 条形图option
  const handleInitGrrVariationChart = dataSoure => {
    const { xTickLabelList, dataMap, legendList } = handleFormatGrrVariationData(dataSoure);
    const option = {
      title: [
        {
          left: 'center',
          text: intl.get(`${modelPrompt}.title.componentOfVariation`).d('变异分量'),
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: xTickLabelList,
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.03)',
          },
        },
        axisLabel: {
          formatter: '{value} %',
        },
      },
      legend: {
        top: '10%',
        left: 'center',
        data: legendList,
      },
      grid: [
        {
          left: '15%',
          right: 0,
          top: '20%',
        },
      ],
      series: [
        {
          name: intl.get(`${modelPrompt}.contribution`).d('贡献率'),
          type: 'bar',
          data: dataMap.contribution,
          itemStyle: {
            color: 'rgb(186, 67, 59)',
          },
        },
        {
          name: intl.get(`${modelPrompt}.variation`).d('变差百分比'),
          type: 'bar',
          data: dataMap.variation,
          itemStyle: {
            color: 'rgb(87, 137, 49)',
          },
        },
        {
          name: intl.get(`${modelPrompt}.tolerancePercent`).d('公差百分比'),
          type: 'bar',
          data: dataMap.tolerance,
          itemStyle: {
            color: 'rgb(114, 159, 208)',
          },
        },
      ],
    };
    grrVariationChart.setOption(option, true);
  };

  // 条形图数据处理
  const handleFormatGrrVariationData = dataSource => {
    const {
      grrVariationChartInfo: {
        avContribution,
        avTolerance,
        avVariation,
        evContribution,
        evTolerance,
        evVariation,
        grrContribution,
        grrTolerance,
        grrVariation,
        pvContribution,
        pvTolerance,
        pvVariation,
      },
    } = dataSource;
    const xTickLabelList = [
      intl.get(`${modelPrompt}.grrContribution`).d('GRR'),
      intl.get(`${modelPrompt}.repeatability`).d('重复性'),
      intl.get(`${modelPrompt}.av`).d('再现性'),
      intl.get(`${modelPrompt}.pv`).d('零件间变差'),
    ]; // x轴坐标名称
    const dataMap = {
      contribution: [grrContribution, evContribution, avContribution, pvContribution],
      variation: [grrVariation, evVariation, avVariation, pvVariation],
      tolerance: [grrTolerance, evTolerance, avTolerance, pvTolerance],
    };
    // const dataMap = {
    //   contribution: [1, 2, 3, 4],
    //   variation: [5, 6, 7, 8],
    //   tolerance: [8, 7, 6, 5],
    // };
    return {
      xTickLabelList,
      dataMap,
      legendList: [
        intl.get(`${modelPrompt}.contribution`).d('贡献率'),
        intl.get(`${modelPrompt}.variation`).d('变差百分比'),
        intl.get(`${modelPrompt}.tolerancePercent`).d('公差百分比'),
      ],
    };
  };

  // 零件链图
  const handleInitGrrColumnChart = dataSoure => {
    const {
      xTickLabelList,
      averageDataList,
      groupDataList,
      minValue,
      maxValue,
    } = handleFormatGrrColumnData(dataSoure?.grrColumnChartInfo || []);
    const option = {
      title: [
        {
          left: 'center',
          text: intl.get(`${modelPrompt}.title.measureDataByPart`).d('测量值 按 零件'),
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
      ],
      tooltip: {
        trigger: 'item',
      },
      xAxis: {
        type: 'category',
        data: xTickLabelList,
        axisTick: {
          show: true,
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        max: lodashCeil(maxValue * 100) / 100,
        min: lodashFloor(minValue * 100) / 100,
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.03)',
          },
        },
      },
      series: [
        {
          type: 'line',
          data: averageDataList,
          z: 20,
          itemStyle: {
            color: 'rgb(49, 113, 173)',
          },
          symbolSize: 8,
        },
        {
          type: 'scatter',
          data: groupDataList,
          itemStyle: {
            color: 'rgb(142, 142, 142)',
          },
        },
      ],
    };
    grrColumnChart.setOption(option, true);
  };

  // 零件链图数据处理
  const handleFormatGrrColumnData = dataSource => {
    const xTickLabelList: any = []; // x轴坐标名称
    const averageDataList: any = []; // 平均值数据
    let groupDataList: any = []; // 散点图数据
    let minValue; // 最小值
    let maxValue; // 最大值
    (dataSource || []).forEach((item) => {
      const { groupDataInfo = [], subgroupIndex, xBarStatsValue } = item;
      xTickLabelList.push(subgroupIndex);
      averageDataList.push(xBarStatsValue);
      if (!groupDataList?.length) {
        groupDataList = new Array(groupDataInfo?.length || 0).fill([]);
      }
      groupDataInfo.forEach(item => {
        groupDataList.push([String(subgroupIndex), item]);
        if (item < minValue || !minValue) {
          minValue = item;
        }
        if (item > maxValue || !maxValue) {
          maxValue = item;
        }
      });
    });
    return {
      xTickLabelList,
      averageDataList,
      groupDataList,
      minValue,
      maxValue,
    };
  };

  const getRulePercision = (dataRule: any) => {
    const { centerLine, upperControlLimit, lowerControlLimit } = dataRule;
    const data = [centerLine, upperControlLimit, lowerControlLimit];
    let maxDigits;
    data.forEach((item: number) => {
      const _digits = item.toString().split('.')[1]?.length || 0;
      if (_digits > maxDigits || !maxDigits) {
        maxDigits = _digits;
      }
    });
    return maxDigits;
  };

  // xBar图
  const handleInitXBarChart = dataSource => {
    const {
      mainChartRule,
      mainChartMax,
      mainChartMin,
      callipers,
      mainData,
      legendList,
    } = handleFormatXBarData(dataSource);
    const formatLabel = {
      formatter: value => {
        return `${value.name}:${value.data.yAxis}`;
      },
      color: '#000',
      fontSize: 10,
    };
    const option: any = {
      backgroundColor: '#fff',
      title: [
        {
          left: 'center',
          text: intl.get(`${modelPrompt}.title.Xbar`).d('XBar控制图'),
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
      ],
      color: colorList,
      legend: {
        top: '10%',
        left: 'center',
        data: legendList,
      },
      yAxis: [
        {
          type: 'value',
          max: lodashCeil(lodashMax([mainChartRule.upperControlLimit, mainChartMax]) * 100) / 100,
          min: lodashFloor(lodashMin([mainChartRule.lowerControlLimit, mainChartMin]) * 100) / 100,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.03)',
            },
          },
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: callipers,
          axisTick: {
            show: true,
            alignWithLabel: true,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          animation: false,
        },
        borderColor: '#fff',
        backgroundColor: '#fff',
        extraCssText: 'box-shadow: 5px 5px 5px #888888;',
        textStyle: {
          color: '#4c4c4c',
        },
      },
      axisPointer: {
        link: { xAxisIndex: 'all' },
      },
      grid: [
        {
          id: 'main',
          left: 70,
          right: 100,
          top: '20%',
        },
      ],
      series: [
        ...(mainData || []).map((item, index) => ({
          name: item.measuredByName,
          type: 'line',
          hoverAnimation: false,
          symbolSize: 8,
          symbol: symbolList[index % symbolList?.length],
          markLine: {
            silent: true,
            symbol: 'none',
            precision: getRulePercision(mainChartRule),
            data: [
              {
                name: 'CL',
                yAxis: lodashFloor(mainChartRule.centerLine, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#b8b8b8',
                  width: 1.2,
                },
              },
              {
                name: 'UCL',
                yAxis: lodashFloor(mainChartRule.upperControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
              {
                name: 'LCL',
                yAxis: lodashFloor(mainChartRule.lowerControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
            ],
          },
          data: item.data,
        })),
      ],
    };
    xBarChart.setOption(option, true);
  };

  // r图
  const handleInitRChart = dataSource => {
    const {
      secondaryChartRule,
      secondChartMax,
      secondChartMin,
      callipers,
      secondData,
      legendList,
    } = handleFormatXBarData(dataSource);
    const formatLabel = {
      formatter: value => {
        return `${value.name}:${value.data.yAxis}`;
      },
      color: '#000',
      fontSize: 10,
    };
    const option: any = {
      backgroundColor: '#fff',
      color: colorList,
      title: [
        {
          left: 'center',
          text: intl.get(`${modelPrompt}.title.r`).d('R控制图'),
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
      ],
      legend: {
        top: '10%',
        left: 'center',
        data: legendList,
      },
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.03)',
            },
          },
          max:
            lodashCeil(lodashMax([secondaryChartRule.upperControlLimit, secondChartMax]) * 100) /
            100,
          min:
            lodashFloor(lodashMin([secondaryChartRule.lowerControlLimit, secondChartMin]) * 100) /
            100,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: callipers,
          axisTick: {
            show: true,
            alignWithLabel: true,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          animation: false,
        },
        borderColor: '#fff',
        backgroundColor: '#fff',
        extraCssText: 'box-shadow: 5px 5px 5px #888888;',
        textStyle: {
          color: '#4c4c4c',
        },
      },
      axisPointer: {
        link: { xAxisIndex: 'all' },
      },
      grid: [
        {
          id: 'second',
          left: 70,
          right: 100,
          top: '20%',
          // height: '85%',
        },
      ],
      series: [
        ...(secondData || []).map((item, index) => ({
          name: item.measuredByName,
          type: 'line',
          symbolSize: 8,
          symbol: symbolList[index % symbolList?.length],
          hoverAnimation: false,
          markLine: {
            silent: true,
            symbol: 'none',
            precision: getRulePercision(secondaryChartRule),
            data: [
              {
                name: 'CL',
                yAxis: lodashFloor(secondaryChartRule.centerLine, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#b8b8b8',
                  width: 1.2,
                },
              },
              {
                name: 'UCL',
                yAxis: lodashFloor(secondaryChartRule.upperControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
              {
                name: 'LCL',
                yAxis: lodashFloor(secondaryChartRule.lowerControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
            ],
          },
          data: item.data,
        })),
      ],
    };
    rChart.setOption(option, true);
  };

  // xBar-R图数据处理
  const handleFormatXBarData = dataSource => {
    const { grrUserColumnChartInfo, xBarChartRule, rChartRule } = dataSource;
    const dataIdList: any = []; // 数据Id列表
    const mainData: any = []; // 主图数据
    const secondData: any = []; // 次图数据
    const xTickLabelList: any = []; // x轴坐标名称
    const legendList: any = []; // 图例
    let mainChartMax;
    let mainChartMin;
    let secondChartMax;
    let secondChartMin;
    if (grrUserColumnChartInfo?.length) {
      grrUserColumnChartInfo.forEach((userItem, userIndex) => {
        const { measuredBy, measuredByName, lineData } = userItem;
        legendList.push(measuredByName);
        const _mainData: any = [];
        const _secondData: any = [];
        (lineData || []).forEach(item => {
          if (item.xBarStatsValue > mainChartMax || !mainChartMax) {
            mainChartMax = item.xBarStatsValue;
          }
          if (item.xBarStatsValue < mainChartMin || !mainChartMin) {
            mainChartMin = item.xBarStatsValue;
          }
          if (item.rStatsValue > secondChartMax || secondChartMax === undefined) {
            secondChartMax = item.rStatsValue;
          }
          if (item.rStatsValue < secondChartMin || secondChartMin === undefined) {
            secondChartMin = item.rStatsValue;
          }
          if (userIndex === 0) {
            dataIdList.push(item.subgroupIndex);
            xTickLabelList.push(item.subgroupIndex);
          }
          _mainData.push(item.xBarStatsValue);
          _secondData.push(item.rStatsValue);
        });
        mainData.push({ measuredBy, measuredByName, data: _mainData });
        secondData.push({ measuredBy, measuredByName, data: _secondData });
      });
    }
    return {
      mainChartRule: xBarChartRule,
      secondaryChartRule: rChartRule,
      dataIdList,
      mainData,
      secondData,
      callipers: xTickLabelList,
      mainChartMax,
      mainChartMin,
      secondChartMax,
      secondChartMin,
      legendList,
    };
  };

  // 测量人链图
  const handleInitGrrUserChart = dataSoure => {
    const {
      xTickLabelList,
      averageDataList,
      groupDataList,
      minValue,
      maxValue,
    } = handleFormatGrrUserChartData(dataSoure);
    const option = {
      title: [
        {
          left: 'center',
          text: intl.get(`${modelPrompt}.title.measureDataByPerson`).d('测量值 按 测量人'),
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
      ],
      tooltip: {
        trigger: 'item',
      },
      xAxis: {
        type: 'category',
        data: xTickLabelList,
        axisTick: {
          show: true,
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        max: lodashCeil(maxValue * 100) / 100,
        min: lodashFloor(minValue * 100) / 100,
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.03)',
          },
        },
      },
      series: [
        {
          type: 'line',
          data: averageDataList,
          z: 20,
          itemStyle: {
            color: 'rgb(49, 113, 173)',
          },
          symbolSize: 8,
        },
        {
          type: 'scatter',
          data: groupDataList,
          itemStyle: {
            color: 'rgb(142, 142, 142)',
          },
        },
      ],
    };
    grrUserChart.setOption(option, true);
  };

  // 测量人链图数据处理
  const handleFormatGrrUserChartData = dataSource => {
    const { grrUserChartInfo } = dataSource;
    const xTickLabelList: any = []; // x轴坐标名称
    const averageDataList: any = []; // 平均值数据
    const groupDataList: any = []; // 散点图数据
    let minValue; // 最小值
    let maxValue; // 最大值
    (grrUserChartInfo || []).forEach(item => {
      const { measuredByName, groupDataInfo, xBarStatsValue } = item;
      xTickLabelList.push(measuredByName);
      averageDataList.push(xBarStatsValue);
      groupDataInfo.forEach(item => {
        groupDataList.push([measuredByName, item]);
        if (item < minValue || !minValue) {
          minValue = item;
        }
        if (item > maxValue || !maxValue) {
          maxValue = item;
        }
      });
    });
    return {
      xTickLabelList,
      averageDataList,
      groupDataList,
      minValue,
      maxValue,
    };
  };

  // 测量人与零件交互作用图
  const handleInitGrrUserColumnChart = dataSoure => {
    const {
      xTickLabelList,
      dataList,
      legendList,
      maxValue,
      minValue,
    } = handleFormatGrrUserColumnChartData(dataSoure);
    const option = {
      title: [
        {
          left: 'center',
          text: intl.get(`${modelPrompt}.title.partAndPerson`).d('零件 乘 测量人 交互作用'),
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
      },
      color: colorList,
      legend: {
        top: '10%',
        left: 'center',
        data: legendList,
      },
      xAxis: {
        type: 'category',
        data: xTickLabelList,
        axisTick: {
          show: true,
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        max: lodashCeil(maxValue * 100) / 100,
        min: lodashFloor(minValue * 100) / 100,
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.03)',
          },
        },
      },
      series: [
        ...(dataList || []).map((item, index) => ({
          name: String(item.measuredByName),
          type: 'line',
          data: item.data,
          symbolSize: 8,
          symbol: symbolList[index % symbolList?.length],
        })),
      ],
    };
    grrUserColumnChart.setOption(option, true);
    // 初始化chart事件绑定
    if (!pageInit) {
      setPageInit(true);
    }
  };

  // 测量人与零件交互作用图数据处理
  const handleFormatGrrUserColumnChartData = dataSource => {
    const { grrUserColumnChartInfo } = dataSource;
    const xTickLabelList: any = []; // x轴坐标名称
    const dataList: any = grrUserColumnChartInfo || []; // 数据
    const legendList: any = []; // 图例数据
    let maxValue;
    let minValue;
    (dataList || []).forEach((colItem, colIndex) => {
      const { measuredByName, lineData } = colItem;
      legendList.push({
        name: String(measuredByName),
        // icon: symbolList[colIndex % symbolList?.length],
      });
      const data: any = [];
      (lineData || []).forEach(i => {
        data.push(i?.xBarStatsValue);
        if (maxValue < i?.xBarStatsValue || !maxValue) {
          maxValue = i?.xBarStatsValue;
        }
        if (minValue > i?.xBarStatsValue || !minValue) {
          minValue = i?.xBarStatsValue;
        }
        if (colIndex === 0) {
          xTickLabelList.push(i.subgroupIndex);
        }
      });
      colItem.data = data;
    });
    return {
      xTickLabelList,
      dataList,
      legendList,
      maxValue,
      minValue,
    };
  };

  const bisaColumn = [
    {
      name: 'ev',
      title: '设备变差(EV)',
    },
    {
      name: 'av',
      title: '评价人变差(AV)',
    },
    {
      name: 'pv',
      title: '零件间变差(PV)',
    },
    {
      name: 'grr',
      title: '重复性与再现性(GRR)',
    },
    {
      name: 'tv',
      title: '总变差(TV)',
    },
    {
      name: 'ndc',
      title: '分级数(ndc)',
    },
    {
      name: 'evPercent',
      title: '%EV',
    },
    {
      name: 'avPercent',
      title: '%AV',
    },
    {
      name: 'pvPercent',
      title: '%PV',
    },
    {
      name: 'grrPercent',
      title: '%GRR',
    },
  ];

  const RenderRemark = () => {
    const info = intl.get(`${modelPrompt}.grr.remark`).d('AV－如果计算中根号下出现负值，评价人变差缺省为0；<br />计算临界值时，默认α=0.25。');
    const descriptionList = info.split('<br />');
    return (
      <div>
        {descriptionList.map(item => (
          <p>&nbsp;&nbsp;&nbsp;{item}</p>
        ))}
      </div>
    );
  };

  return (
    <div>
      {Boolean(dataSoure?.tableInfo?.length) && (
        <>
          <br/>
          <br/>
          <h4>{intl.get(`${modelPrompt}.title.dataAnalyse`).d('数据分析')}</h4>
          <div className='dataAnayContainer'>
            {/* <Table data={[{ ...dataSoure.grrTableInfo }]} columns={bisaColumn} autoWidth /> */}
            <table>
              <tbody>
                <tr>
                  <td>{intl.get(`${modelPrompt}.templateColumn.ev`).d('设备变差(EV)')}</td>
                  <td>{intl.get(`${modelPrompt}.templateColumn.pv`).d('零件间变差(PV)')}</td>
                  <td>{intl.get(`${modelPrompt}.templateColumn.av`).d('评价人变差(AV)')}</td>
                  <td>{intl.get(`${modelPrompt}.column.iv`).d('评价人x零件间变差(IV)')}</td>
                  <td>{intl.get(`${modelPrompt}.column.rb`).d('再现性')}</td>
                  <td>{intl.get(`${modelPrompt}.templateColumn.grr`).d('重复性与再现性(GRR)')}</td>
                  <td>{intl.get(`${modelPrompt}.templateColumn.tv`).d('总变差(TV)')}</td>
                </tr>
                <tr>
                  <td>
                    {dataSoure.grrTableInfo.ev}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.pv}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.av}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.iv}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.rb}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.grr}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.tv}
                  </td>
                </tr>
              </tbody>
            </table>
            <br/>
            <table>
              <tbody>
                <tr>
                  <td>{intl.get(`${modelPrompt}.templateColumn.evPercent`).d('%EV')}</td>
                  <td>{intl.get(`${modelPrompt}.templateColumn.pvPercent`).d('%PV')}</td>
                  <td>{intl.get(`${modelPrompt}.templateColumn.avPercent`).d('%AV')}</td>
                  <td>{intl.get(`${modelPrompt}.column.ivPercent`).d('%IV')}</td>
                  <td>{intl.get(`${modelPrompt}.column.rbPercent`).d('%再现性')}</td>
                  <td>{intl.get(`${modelPrompt}.templateColumn.grrPercent`).d('%GRR')}</td>
                  <td>{intl.get(`${modelPrompt}.templateColumn.ndc`).d('分级数(ndc)')}</td>
                </tr>
                <tr>
                  <td>
                    {dataSoure.grrTableInfo.evPercent}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.pvPercent}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.avPercent}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.ivPercent}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.rbPercent}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.grrPercent}
                  </td>
                  <td>
                    {dataSoure.grrTableInfo.ndc}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <br/>
          <br/>
          <h4>{intl.get(`${modelPrompt}.title.msaResult`).d('分析结果')}</h4>
          <div className={styles.resultContainer}>
            <table>
              <tbody>
                <tr>
                  <td> {intl.get(`${modelPrompt}.label.msaResult`).d('分析结果')}:</td>
                  <td>{analyseResultDs.current?.getField('msaResult')!.getText()}</td>
                </tr>
                <tr>
                  <td> {intl.get(`${modelPrompt}.label.msaConclusion`).d('分析结论')}: </td>
                  <td>
                    {analyseResultDs.current?.get('msaConclusion')}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <h4>{intl.get(`${modelPrompt}.title.analyseGraphic`).d('分析图')}</h4>
          <Row>
            <Col span={12}>
              <div
                id="grr-variation-chart"
                ref={grrVariationChartRef}
                style={{
                  height: '300px',
                }}
              />
            </Col>
            <Col span={12}>
              <div
                id="grr-column-chart"
                ref={grrColumnChartRef}
                style={{
                  height: '300px',
                }}
              />
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <div
                id="xbar-chart"
                ref={xBarChartRef}
                style={{
                  height: '300px',
                }}
              />
            </Col>
            <Col span={12}>
              <div
                id="grr-user-chart"
                ref={grrUserChartRef}
                style={{
                  height: '300px',
                }}
              />
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <div
                id="r-chart"
                ref={rChartRef}
                style={{
                  height: '300px',
                }}
              />
            </Col>
            <Col span={12}>
              <div
                id="grr-user-column-chart"
                ref={grrUserColumnChartRef}
                style={{
                  height: '300px',
                }}
              />
            </Col>
          </Row>
          <h4>{intl.get(`${modelPrompt}.label.remark`).d('备注')}</h4>
          <RenderRemark />
        </>
      )}
    </div>
  );
};

export default observer(GRRGraphic);
