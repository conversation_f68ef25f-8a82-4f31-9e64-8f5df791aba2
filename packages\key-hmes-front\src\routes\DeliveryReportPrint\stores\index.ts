/*
 * @Description: 发货报告打印平台-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2024-03-06 16:46:22
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-08 12:48:53
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.deliveryReportPrint';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'identifications',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
    },
  ],
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('客户零件号'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('下线时间'),
    },
    {
      name: 'eoId',
    },
    {
      name: 'result',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.result`).d('查询结果'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-shipment-print/list/ui`,
        method: 'POST',
      };
    },
  },
});

export { tableDS };
