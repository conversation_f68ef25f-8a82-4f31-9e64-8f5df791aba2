import React, { useMemo } from 'react';
import DashboardCard from '../../components/DashboardCard';
import ScrollBoard from '../ScrollBoard';
import styles from '../../index.module.less';
import intl from "utils/intl";

const modelPrompt = 'tarzan.wms.EquipmentMaintenanceManagementBoard';

const FaultStatisticsTable = ({ info }) => {

    const tableData: any[] = [];
    if (info && info.length) {
        info.forEach((val) => {
            const {
                equFieldIdentification,
                total,
            } = val;
            tableData.push([
                equFieldIdentification,
                total,
            ]);
        });
    }

    const config = useMemo(
        () => ({
            header: [
                intl.get(`${modelPrompt}.field.equFieldIdentification`).d('设备'),
                intl.get(`${modelPrompt}.field.total`).d('故障次数'),
            ],
            data: tableData,
            rowNum: 8,
            align: ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'],
            oddRowBGC: 'rgba(22,66,127,0.3)',
            headerBGC: 'rgb(3, 157, 206,0.3)',
            evenRowBGC: 'rgba(3,28,60, 0.3)',
            headerHeight: 40,
        }),
        [tableData],
    );

    return (
        <DashboardCard style={{ height: '100%' }} >
            <div style={{ width: '100%', height: '100%' }}>
                <div className={styles['my-scroll-board-table']}>
                    <ScrollBoard config={config} style={{ width: '100%', height: '100%', marginLeft: '0px 10px' }} />
                </div>
            </div>
        </DashboardCard>
    );
};


export default FaultStatisticsTable;