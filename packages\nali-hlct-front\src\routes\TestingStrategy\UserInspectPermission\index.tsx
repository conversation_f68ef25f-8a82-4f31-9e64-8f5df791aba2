/**
 * @Description: 用户检验权限维护-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-02-28 10:23:48
 * @LastEditTime: 2023-04-12 15:42:46
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { DataSet, Table, Modal, Select, TextField, Lov, Button, Switch } from 'choerodon-ui/pro';
import { Badge, Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { Header, Content } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import { openTab } from 'utils/menuTab';
import notification from 'utils/notification';
import queryString from 'querystring';
import withProps from 'utils/withProps';
import { tableDS } from './stores';
import UserRightsComponentDrawer from './UserRightsComponentDrawer';

import { saveUserInspectPermissionConfig, deleteUserInspectPermissionConfig } from './services';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.userInspectPermission';

const UserInspectPermission = props => {
  const {
    tableDs,
    match: { path },
  } = props;

  // 保存
  const saveUserInspectPermission = useRequest(saveUserInspectPermissionConfig(), {
    manual: true,
    needPromise: true,
  });
  // 删除
  const deleteUserInspectPermission = useRequest(deleteUserInspectPermissionConfig(), {
    manual: true,
    needPromise: true,
  });

  const [selectedRecords, setSelectedRecords] = useState([]);
  const [editing, setEditing] = useState(false);

  useEffect(() => {
    if (tableDs?.currentPage) {
      tableDs.query(props.tableDs.currentPage);
      setEditing(false);
    } else {
      tableDs.query();
      setEditing(false);
    }
  }, []);

  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteObject':
        record.set('inspectSchemeObject', null);
        break;
      case 'inspectObjectTypeObject':
        record.set('inspectSchemeObject', null);
        break;
      default:
        break;
    }
  });

  useDataSetEvent(tableDs, 'batchSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  useDataSetEvent(tableDs, 'batchUnSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  useDataSetEvent(tableDs, 'query', ({ dataSet }) => {
    setEditing(false);
    setSelectedRecords(dataSet.selected);
  });

  const userLovChange = record => {
    record.set('siteObject', null);
    record.set('inspectBusinessTypeObject', null);
    record.set('inspectSchemeObject', null);
  };

  const siteObjectChange = record => {
    record.set('inspectBusinessTypeObject', null);
    record.set('inspectSchemeObject', null);
  };
  const inspectObjectTypeObjectChange = record => {
    record.set('inspectSchemeObject', null);
  };

  const columns: ColumnProps[] = [
    {
      name: 'userLov',
      lock: ColumnLock.left,
      editor: record =>
        record.getState('editing') && (
          <Lov
            tableProps={{
              queryFieldsLimit: 10,
            }}
            onChange={() => {
              userLovChange(record);
            }}
          />
        ),
      renderer: ({ record }) => {
        return (
          <>
            <a
              onClick={() =>
                props.history.push(
                  `/hwms/user-inspect-permission/detail/${record?.get('userId')}/${record?.get(
                    'siteId',
                  )}`,
                )
              }
            >
              {record?.get('userName')}
            </a>
          </>
        );
      },
      minWidth: 140,
    },
    {
      name: 'siteObject',
      editor: record =>
        record.getState('editing') && (
          <Lov
            onChange={() => {
              siteObjectChange(record);
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return [
                  <Button
                    onClick={() => {
                      addUserSitePermission(modal, record);
                    }}
                  >
                    {intl.get(`${modelPrompt}.addUserSitePermission`).d('新增用户站点权限')}
                  </Button>,
                  cancelBtn,
                  okBtn,
                ];
              },
            }}
            tableProps={{
              queryFieldsLimit: 10,
            }}
          />
        ),
      minWidth: 150,
    },
    {
      name: 'permissionType',
      editor: record => record.getState('editing') && <Select />,
      minWidth: 120,
    },
    {
      name: 'inspectBusinessTypeObject',
      editor: record => record.getState('editing') && <Lov />,
      minWidth: 160,
    },
    {
      name: 'inspectObjectTypeObject',
      editor: record =>
        record.getState('editing') && (
          <Select
            onChange={() => {
              inspectObjectTypeObjectChange(record);
            }}
          />
        ),
      minWidth: 120,
    },
    {
      name: 'inspectSchemeObject',
      editor: record => record.getState('editing') && <Lov />,
      minWidth: 160,
    },
    {
      name: 'taskCategory',
      editor: record => record.getState('editing') && <TextField />,
      minWidth: 140,
    },
    {
      name: 'inspectPermissionFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
      editor: record => record.getState('editing') && <Switch name="inspectPermissionFlag" />,
    },
    {
      name: 'reinspectPermissionFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
      editor: record => record.getState('editing') && <Switch name="reinspectPermissionFlag" />,
    },
  ];

  const handleCreateNew = () => {
    props.history.push('/hwms/user-inspect-permission/detail/create/0');
  };

  const handleDelete = async () => {
    return deleteUserInspectPermission.run({
      params: selectedRecords.map(record => {
        // @ts-ignore
        const { userInspectPermissionId } = record.toData();
        return userInspectPermissionId;
      }),
      onSuccess: () => {
        // @ts-ignore
        notification.success();
        tableDs.batchUnSelect(selectedRecords);
        tableDs.query(tableDs.currentPage);
      },
    });
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/MT.QMS.USER_INSPECT_PERMISSION',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const addUserSitePermission = (_modal, _record) => {
    const { userLov } = _record.toData();
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.addUserSitePermission`).d('新增用户站点权限'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      cancelText: intl.get(`tarzan.common.button.close`).d('关闭'),
      onClose: () => {
        _modal.props.children.props.dataSet.query();
      },
      children: <UserRightsComponentDrawer userInfo={userLov} />,
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.UserInspectPermission`).d('用户检验权限维护')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleCreateNew()}
          disabled={editing || saveUserInspectPermission.loading}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Popconfirm
          title={intl
            .get(`${modelPrompt}.confirm.delete`, {
              count: selectedRecords.length,
            })
            .d(`总计${selectedRecords.length}条数据，是否确认删除?`)}
          onConfirm={handleDelete}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          okText={intl.get('tarzan.common.button.confirm').d('确定')}
        >
          <PermissionButton
            type="c7n-pro"
            permissionList={[
              {
                code: `list.button.delete`,
                type: 'button',
                meaning: '列表页-删除按钮',
              },
            ]}
            icon="delete_black-o"
            disabled={selectedRecords.length === 0 || saveUserInspectPermission.loading}
            loading={deleteUserInspectPermission.loading}
          >
            {intl.get('tarzan.common.button.delete').d('删除')}
          </PermissionButton>
        </Popconfirm>
        <PermissionButton
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
          permissionList={[
            {
              code: `tarzan${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          searchCode="yhjyqxwh1"
          customizedCode="yhjyqxwh1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: [modelPrompt, 'tarzan.model.hmes.userRights', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(UserInspectPermission),
);
