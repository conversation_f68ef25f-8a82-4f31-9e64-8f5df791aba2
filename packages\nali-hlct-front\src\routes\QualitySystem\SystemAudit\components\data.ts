export const rowsDate1 = [
  {
    templateFileCode: '12213123',
    templateFileName: '231231321',
    nodeLevel: 0,
    templateFileId: 16001,
    subInfo: [
      {
        templateFileDId: 163001,
        templateFileId: 16001,
        nodeCode: '',
        nodeName: '123123',
        nodeLevel: 1,
        sequence: 1,
        subList: [],
      },
    ],
  },
  {
    templateFileCode: '编码123',
    templateFileName: '名称',
    nodeLevel: 0,
    templateFileId: 17001,
    subInfo: [
      {
        templateFileDId: 165001,
        templateFileId: 17001,
        nodeCode: '',
        nodeName: '1.1',
        nodeLevel: 1,
        sequence: 1,
        subList: [
          {
            templateFileDId: 167001,
            templateFileId: 17001,
            nodeCode: '',
            nodeName: '1.1.1',
            nodeLevel: 2,
            sequence: 1,
            subList: [
              {
                templateFileDId: 168001,
                templateFileId: 17001,
                nodeCode: '',
                nodeName: '1.1.1.2',
                nodeLevel: 3,
                sequence: 1,
                subList: [],
              },
            ],
          },
        ],
      },
      {
        templateFileDId: 166001,
        templateFileId: 17001,
        nodeCode: '',
        nodeName: '1.2',
        nodeLevel: 1,
        sequence: 2,
        subList: [],
      },
    ],
  },
  {
    templateFileCode: '编码',
    templateFileName: '标准体系文件测试',
    nodeLevel: 0,
    templateFileId: 13001,
    subInfo: [
      {
        templateFileDId: 160001,
        templateFileId: 13001,
        nodeCode: '',
        nodeName: '1',
        nodeLevel: 1,
        sequence: 1,
        subList: [],
      },
    ],
  },
  {
    templateFileCode: 'IATF16949',
    templateFileName: '汽车质量管理体系标准',
    nodeLevel: 0,
    templateFileId: 5001,
    subInfo: [
      {
        templateFileDId: 115001,
        templateFileId: 5001,
        nodeCode: '',
        nodeName: '1 范围',
        nodeLevel: 1,
        sequence: 1,
        subList: [
          {
            templateFileDId: 118001,
            templateFileId: 5001,
            nodeCode: '',
            nodeName: '1.1 范围---汽车行业对 ISO9001:2015 的补充',
            nodeLevel: 2,
            sequence: 1,
            subList: [],
          },
        ],
      },
      {
        templateFileDId: 116001,
        templateFileId: 5001,
        nodeCode: '',
        nodeName: '2 引用标准',
        nodeLevel: 1,
        sequence: 2,
        subList: [
          {
            templateFileDId: 119001,
            templateFileId: 5001,
            nodeCode: '',
            nodeName: '2.1 规范性引用标准和参考性引用标准',
            nodeLevel: 2,
            sequence: 1,
            subList: [],
          },
        ],
      },
      {
        templateFileDId: 117001,
        templateFileId: 5001,
        nodeCode: '',
        nodeName: '3 述语和定义',
        nodeLevel: 1,
        sequence: 3,
        subList: [
          {
            templateFileDId: 120001,
            templateFileId: 5001,
            nodeCode: '',
            nodeName: '3.1 汽车行业的述语和定义',
            nodeLevel: 2,
            sequence: 1,
            subList: [],
          },
        ],
      },
      {
        templateFileDId: 69001,
        templateFileId: 5001,
        nodeCode: '',
        nodeName: '4 组织的背景环境',
        nodeLevel: 1,
        sequence: 4,
        subList: [
          {
            templateFileDId: 71001,
            templateFileId: 5001,
            nodeCode: '',
            nodeName: '4.1 理解组织及其背景环境',
            nodeLevel: 2,
            sequence: 1,
            subList: [],
          },
        ],
      },
      {
        templateFileDId: 41001,
        templateFileId: 5001,
        nodeCode: '',
        nodeName: '5 领导作用',
        nodeLevel: 1,
        sequence: 5,
        subList: [
          {
            templateFileDId: 42001,
            templateFileId: 5001,
            nodeCode: '',
            nodeName: '5.1 领导作用与承诺',
            nodeLevel: 2,
            sequence: 1,
            subList: [
              {
                templateFileDId: 43001,
                templateFileId: 5001,
                nodeCode: '',
                nodeName: '5.1.1 总则',
                nodeLevel: 3,
                sequence: 1,
                subList: [
                  {
                    templateFileDId: 44001,
                    templateFileId: 5001,
                    nodeCode: '',
                    nodeName: '5.1.1.1 公司责任',
                    nodeLevel: 4,
                    sequence: 1,
                    subList: [],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        templateFileDId: 75001,
        templateFileId: 5001,
        nodeCode: '',
        nodeName: '6 策划',
        nodeLevel: 1,
        sequence: 6,
        subList: [
          {
            templateFileDId: 76001,
            templateFileId: 5001,
            nodeCode: '',
            nodeName: '6.1 风险和机遇的应对措施',
            nodeLevel: 2,
            sequence: 1,
            subList: [
              {
                templateFileDId: 123001,
                templateFileId: 5001,
                nodeCode: '',
                nodeName: '6.1.1',
                nodeLevel: 3,
                sequence: 1,
                subList: [
                  {
                    templateFileDId: 130001,
                    templateFileId: 5001,
                    nodeCode: '',
                    nodeName: '6.1.1.1',
                    nodeLevel: 4,
                    sequence: 1,
                    subList: [],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        templateFileDId: 131001,
        templateFileId: 5001,
        nodeCode: '',
        nodeName: '7 支持',
        nodeLevel: 1,
        sequence: 7,
        subList: [
          {
            templateFileDId: 132001,
            templateFileId: 5001,
            nodeCode: '',
            nodeName: '7.1 资源',
            nodeLevel: 2,
            sequence: 1,
            subList: [
              {
                templateFileDId: 133001,
                templateFileId: 5001,
                nodeCode: '',
                nodeName: '7.1.1 总则',
                nodeLevel: 3,
                sequence: 1,
                subList: [],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    templateFileCode: 'IATF 16949',
    templateFileName: '汽车质量管理体系标准',
    nodeLevel: 0,
    templateFileId: 22001,
    subInfo: [
      {
        templateFileDId: 185001,
        templateFileId: 22001,
        nodeCode: '',
        nodeName: '1 引言',
        nodeLevel: 1,
        sequence: 1,
        subList: [
          {
            templateFileDId: 187001,
            templateFileId: 22001,
            nodeCode: '',
            nodeName: '1.1 大纲',
            nodeLevel: 2,
            sequence: 1,
            subList: [],
          },
        ],
      },
      {
        templateFileDId: 186001,
        templateFileId: 22001,
        nodeCode: '',
        nodeName: '2 总则',
        nodeLevel: 1,
        sequence: 2,
        subList: [
          {
            templateFileDId: 190001,
            templateFileId: 22001,
            nodeCode: '',
            nodeName: '2.1 总则1',
            nodeLevel: 2,
            sequence: 1,
            subList: [],
          },
        ],
      },
      {
        templateFileDId: 194001,
        templateFileId: 22001,
        nodeCode: '',
        nodeName: '3 目录',
        nodeLevel: 1,
        sequence: 3,
        subList: [],
      },
    ],
  },
  {
    templateFileCode: '文件编码123',
    templateFileName: '名称',
    nodeLevel: 0,
    templateFileId: 18001,
    subInfo: [
      {
        templateFileDId: 172001,
        templateFileId: 18001,
        nodeCode: '',
        nodeName: '1 目录',
        nodeLevel: 1,
        sequence: 1,
        subList: [],
      },
    ],
  },
  {
    templateFileCode: '22',
    templateFileName: '22',
    nodeLevel: 0,
    templateFileId: 10001,
    subInfo: [
      {
        templateFileDId: 125001,
        templateFileId: 10001,
        nodeCode: '',
        nodeName: '4444',
        nodeLevel: 1,
        sequence: 1,
        subList: [],
      },
    ],
  },
  {
    templateFileCode: 'wqw',
    templateFileName: 'wqw',
    nodeLevel: 0,
    templateFileId: 6001,
    subInfo: [
      {
        templateFileDId: 59001,
        templateFileId: 6001,
        nodeCode: '',
        nodeName: '我的天了撸',
        nodeLevel: 1,
        sequence: 2,
        subList: [
          {
            templateFileDId: 60001,
            templateFileId: 6001,
            nodeCode: '',
            nodeName: '天的我',
            nodeLevel: 2,
            sequence: 1,
            subList: [
              {
                templateFileDId: 61001,
                templateFileId: 6001,
                nodeCode: '',
                nodeName: '啊啊啊啊啊',
                nodeLevel: 3,
                sequence: 1,
                subList: [
                  {
                    templateFileDId: 66001,
                    templateFileId: 6001,
                    nodeCode: '',
                    nodeName: '455555',
                    nodeLevel: 4,
                    sequence: 1,
                    subList: [],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        templateFileDId: 82001,
        templateFileId: 6001,
        nodeCode: '',
        nodeName: '23',
        nodeLevel: 1,
        sequence: 3,
        subList: [],
      },
      {
        templateFileDId: 83001,
        templateFileId: 6001,
        nodeCode: '',
        nodeName: '66666',
        nodeLevel: 1,
        sequence: 4,
        subList: [],
      },
    ],
  },
  {
    templateFileCode: '编码123',
    templateFileName: '名称',
    nodeLevel: 0,
    templateFileId: 19001,
    subInfo: [
      {
        templateFileDId: 176001,
        templateFileId: 19001,
        nodeCode: '',
        nodeName: '1',
        nodeLevel: 1,
        sequence: 1,
        subList: [
          {
            templateFileDId: 178001,
            templateFileId: 19001,
            nodeCode: '',
            nodeName: '1.1',
            nodeLevel: 2,
            sequence: 1,
            subList: [
              {
                templateFileDId: 179001,
                templateFileId: 19001,
                nodeCode: '',
                nodeName: '1.1.1',
                nodeLevel: 3,
                sequence: 1,
                subList: [],
              },
            ],
          },
        ],
      },
      {
        templateFileDId: 177001,
        templateFileId: 19001,
        nodeCode: '',
        nodeName: '2',
        nodeLevel: 1,
        sequence: 2,
        subList: [
          {
            templateFileDId: 180001,
            templateFileId: 19001,
            nodeCode: '',
            nodeName: '2.1',
            nodeLevel: 2,
            sequence: 1,
            subList: [],
          },
        ],
      },
    ],
  },
  {
    templateFileCode: '编码1',
    templateFileName: '名称',
    nodeLevel: 0,
    templateFileId: 15001,
    subInfo: [
      {
        templateFileDId: 161001,
        templateFileId: 15001,
        nodeCode: '',
        nodeName: '1.1',
        nodeLevel: 1,
        sequence: 1,
        subList: [],
      },
    ],
  },
  {
    templateFileCode: 'ISO 13485',
    templateFileName: '医疗器械质量管理体系',
    nodeLevel: 0,
    templateFileId: 11001,
    subInfo: [
      {
        templateFileDId: 143001,
        templateFileId: 11001,
        nodeCode: '',
        nodeName: '1 范围',
        nodeLevel: 1,
        sequence: 1,
        subList: [],
      },
      {
        templateFileDId: 144001,
        templateFileId: 11001,
        nodeCode: '',
        nodeName: '2 规范性引用文件',
        nodeLevel: 1,
        sequence: 2,
        subList: [],
      },
      {
        templateFileDId: 155001,
        templateFileId: 11001,
        nodeCode: '',
        nodeName: '3 术语和定义',
        nodeLevel: 1,
        sequence: 3,
        subList: [],
      },
      {
        templateFileDId: 146001,
        templateFileId: 11001,
        nodeCode: '',
        nodeName: '4 质量管理体系',
        nodeLevel: 1,
        sequence: 4,
        subList: [
          {
            templateFileDId: 147001,
            templateFileId: 11001,
            nodeCode: '',
            nodeName: '4.1 总要求',
            nodeLevel: 2,
            sequence: 1,
            subList: [],
          },
        ],
      },
    ],
  },
  {
    templateFileCode: 'wqw测试',
    templateFileName: 'wqw测试哈哈哈哈哈哈',
    nodeLevel: 0,
    templateFileId: 3001,
    subInfo: [
      {
        templateFileDId: 10001,
        templateFileId: 3001,
        nodeCode: '',
        nodeName: 'WQW哭死222',
        nodeLevel: 1,
        sequence: 1,
        subList: [
          {
            templateFileDId: 12001,
            templateFileId: 3001,
            nodeCode: '',
            nodeName: '444444',
            nodeLevel: 2,
            sequence: 2,
            subList: [],
          },
        ],
      },
    ],
  },
];
export const rowsDate = [
  {
    expand: true,
    functionCode: 'HR',
    icon: 'airline_seat_flat-o',
    id: 2,
    ischecked: null,
    score: 10,
    shortcutId: null,
    name: '组织架构',
    bomType: 'CAPITAL',
    hpsFactoryBomId: 1234,
    materialCode: '111',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'SYS_REPORT_MANAGE',
    icon: 'airline_seat_flat_angled-o',
    id: 24,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '报表管理',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'ATTACH',
    icon: 'airline_seat_flat-o',
    id: 69,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '附件管理',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'JOB',
    icon: 'airline_seat_flat-o',
    id: 16,
    ischecked: true,
    score: 40,
    shortcutId: null,
    name: '计划任务',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'WFL_OFFICE',
    icon: 'airline_seat_flat-o',
    id: 39,
    ischecked: true,
    score: 49,
    shortcutId: null,
    name: '工作流',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'WFL',
    icon: 'airline_seat_flat-o',
    id: 27,
    ischecked: true,
    score: 50,
    shortcutId: null,
    name: '流程管理',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'IF',
    icon: 'airline_seat_flat-o',
    id: 45,
    ischecked: true,
    score: 80,
    shortcutId: null,
    name: '接口管理',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'API',
    icon: 'airline_seat_flat-o',
    id: 49,
    ischecked: true,
    score: 90,
    shortcutId: null,
    name: '服务管理',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'TASK',
    icon: 'airline_seat_flat-o',
    id: 53,
    ischecked: true,
    score: 95,
    shortcutId: null,
    name: '任务管理',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'SYSTEM',
    icon: 'airline_seat_flat-o',
    id: 1,
    ischecked: true,
    score: 99,
    shortcutId: null,
    name: '系统管理',
    url: null,
    symbol: '0',
  },
  {
    expand: false,
    functionCode: 'SYS_CONFIG',
    icon: 'airline_seat_flat-o',
    id: 63,
    ischecked: true,
    score: 6,
    shortcutId: null,
    name: '系统配置',
    url: 'sys/sys_config.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'SYS_METRICS',
    icon: 'airline_seat_flat-o',
    id: 78,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '系统指标',
    url: 'sys/sys_detail_metrics.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'ACCOUNT',
    icon: 'airline_seat_flat-o',
    id: 8,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '账户管理',
    url: null,
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'FORM',
    icon: 'airline_seat_flat-o',
    id: 87,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '表单管理',
    url: 'sys/ui-builder.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'HOTKEY',
    icon: 'airline_seat_flat-o',
    id: 88,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '热键配置',
    url: 'sys/sys_hotkey.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'FUNCTION',
    icon: 'airline_seat_flat-o',
    id: 3,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '功能管理',
    url: null,
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'SYS_DASHBOARD',
    icon: 'airline_seat_flat-o',
    id: 77,
    ischecked: true,
    score: 15,
    shortcutId: null,
    name: '仪表盘配置',
    url: 'sys/sys_dashboard.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'PROMPT',
    icon: 'airline_seat_flat-o',
    id: 58,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '描述维护',
    url: 'sys/sys_prompt.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'PROMPT_REACT',
    icon: 'airline_seat_flat-o',
    id: 14,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '描述维护(react)',
    url: 'hap-core/sys/prompt',
    symbol: '1',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'CODE',
    icon: 'airline_seat_flat-o',
    id: 59,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '代码维护',
    url: 'sys/sys_code.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'CODE_REACT',
    icon: 'airline_seat_flat-o',
    id: 4,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '代码维护(react)',
    url: 'hap-core/sys/code',
    symbol: '1',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'LOV',
    icon: 'airline_seat_flat-o',
    id: 60,
    ischecked: true,
    score: 40,
    shortcutId: null,
    name: 'LOV定义',
    url: 'sys/sys_lov.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'SYS_CODE_RULE',
    icon: 'airline_seat_flat-o',
    id: 83,
    ischecked: true,
    score: 45,
    shortcutId: null,
    name: '编码规则',
    url: 'code/rule/code_rules.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'LANGUAGE',
    icon: 'airline_seat_flat-o',
    id: 61,
    ischecked: true,
    score: 50,
    shortcutId: null,
    name: '语言维护',
    url: 'sys/sys_language.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'LANGUAGE_REACT',
    icon: 'airline_seat_flat-o',
    id: 15,
    ischecked: true,
    score: 50,
    shortcutId: null,
    name: '语言维护(react)',
    url: 'hap-core/sys/language',
    symbol: '1',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'PROFILE',
    icon: 'airline_seat_flat-o',
    id: 62,
    ischecked: true,
    score: 50,
    shortcutId: null,
    name: '配置维护',
    url: 'sys/sys_profile.html',
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'CODE_RULE_REACT',
    icon: 'airline_seat_flat-o',
    id: 90,
    ischecked: true,
    score: 60,
    shortcutId: null,
    name: '编码规则(react)',
    url: 'hap-core/sys/code_rules',
    symbol: '1',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'EMAIL',
    icon: 'airline_seat_flat-o',
    id: 19,
    ischecked: true,
    score: 80,
    shortcutId: null,
    name: '邮件',
    url: null,
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'FLEX_FIELD',
    icon: 'airline_seat_flat-o',
    id: 79,
    ischecked: true,
    score: 90,
    shortcutId: null,
    name: '弹性域',
    url: null,
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'DATA_PERMISSION',
    icon: 'airline_seat_flat-o',
    id: 84,
    ischecked: false,
    score: 100,
    shortcutId: null,
    name: '数据屏蔽',
    url: null,
    symbol: '0',
    parentId: 1,
  },
  {
    expand: false,
    functionCode: 'EMPLOYEE_REACT',
    icon: 'airline_seat_flat-o',
    id: 7,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '员工管理(react)',
    bomType: 'OPERATION',
    hpsFactoryBomId: 55366,
    url: 'hap-core/hr/employee',
    symbol: '1',
    parentId: 2,
  },
  {
    expand: false,
    functionCode: 'EMPLOYEE_REACT',
    icon: 'airline_seat_flat-o',
    id: 1000,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '员工管理(1000)',
    bomType: 'OPERATION',
    hpsFactoryBomId: 5566,
    url: 'hap-core/hr/employee',
    symbol: '1',
    parentId: 7,
  },
  {
    expand: false,
    functionCode: 'HR_UNIT',
    icon: 'airline_seat_flat_angled-o',
    id: 73,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '组织管理',
    url: 'hr/org_unit.html',
    symbol: '0',
    parentId: 2,
  },
  {
    expand: false,
    functionCode: 'COMPANY_REACT',
    icon: 'airline_seat_flat-o',
    id: 12,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '公司管理(react)',
    url: 'hap-core/hr/company',
    symbol: '1',
    parentId: 2,
  },
  {
    expand: false,
    functionCode: 'ORGUNIT_REACT',
    icon: 'airline_seat_flat_angled-o',
    id: 5,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '组织管理(react)',
    url: 'hap-core/hr/orgunit',
    symbol: '1',
    parentId: 2,
  },
  {
    expand: false,
    functionCode: 'POSITION_REACT',
    icon: 'airline_seat_flat-o',
    id: 6,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '岗位管理(react)',
    url: 'hap-core/hr/position',
    symbol: '1',
    parentId: 2,
  },
  {
    expand: false,
    functionCode: 'HR_POSITION',
    icon: 'airline_seat_flat-o',
    id: 75,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '岗位管理',
    url: 'hr/position.html',
    symbol: '0',
    parentId: 2,
  },
  {
    expand: false,
    functionCode: 'HR_EMPLOYEE',
    icon: 'airline_seat_flat-o',
    id: 74,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '员工管理',
    url: 'hr/employee.html',
    symbol: '0',
    parentId: 2,
  },
  {
    expand: false,
    functionCode: 'FND_COMPANY',
    icon: 'airline_seat_flat-o',
    id: 76,
    ischecked: null,
    score: 40,
    shortcutId: null,
    name: '公司管理',
    url: 'fnd/company.html',
    symbol: '0',
    parentId: 2,
  },
  {
    expand: false,
    functionCode: 'FUNCTION_ADD',
    icon: 'airline_seat_flat-o',
    id: 66,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '功能维护',
    url: 'sys/sys_function.html',
    symbol: '0',
    parentId: 3,
  },
  {
    expand: false,
    functionCode: 'RESOYRCE_REACT',
    icon: 'airline_seat_flat-o',
    id: 11,
    ischecked: null,
    score: 10,
    shortcutId: null,
    name: '资源管理(react)',
    url: 'hap-core/sys/resource',
    symbol: '1',
    parentId: 3,
  },
  {
    expand: false,
    functionCode: 'FUNCTION_REACT',
    icon: 'airline_seat_flat-o',
    id: 13,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '功能维护(react)',
    url: 'hap-core/sys/function',
    symbol: '1',
    parentId: 3,
  },
  {
    expand: false,
    functionCode: 'SYS_RESOURCE',
    icon: 'airline_seat_flat-o',
    id: 67,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '资源管理',
    url: 'sys/sys_resource.html',
    symbol: '0',
    parentId: 3,
  },
  {
    expand: false,
    functionCode: 'FUNCTION_ASSIGN',
    icon: 'airline_seat_flat-o',
    id: 68,
    ischecked: true,
    score: 90,
    shortcutId: null,
    name: '功能分配',
    url: 'sys/sys_role_function.html',
    symbol: '0',
    parentId: 3,
  },
  {
    expand: false,
    functionCode: 'ACCOUNT_USER',
    icon: 'airline_seat_flat-o',
    id: 64,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '用户管理',
    url: 'sys/sys_user.html',
    symbol: '0',
    parentId: 8,
  },
  {
    expand: false,
    functionCode: 'ACCOUNT_USER_REACT',
    icon: 'airline_seat_flat-o',
    id: 9,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '用户管理(react)',
    url: 'hap-core/account/user',
    symbol: '1',
    parentId: 8,
  },
  {
    expand: false,
    functionCode: 'ACCOUNT_ROLE_REACT',
    icon: 'airline_seat_flat-o',
    id: 10,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '角色管理(react)',
    url: 'hap-core/account/role',
    symbol: '1',
    parentId: 8,
  },
  {
    expand: false,
    functionCode: 'ACCOUNT_ROLE',
    icon: 'airline_seat_flat-o',
    id: 65,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '角色管理',
    url: 'sys/sys_role.html',
    symbol: '0',
    parentId: 8,
  },
  {
    expand: false,
    functionCode: 'JOB_DETAIL',
    icon: 'airline_seat_flat-o',
    id: 17,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '任务明细',
    url: 'job/job_detail.html',
    symbol: '0',
    parentId: 16,
  },
  {
    expand: false,
    functionCode: 'JOB_RUNNING_INFO',
    icon: 'airline_seat_flat-o',
    id: 18,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '执行记录',
    url: 'job/job_running_info.html',
    symbol: '0',
    parentId: 16,
  },
  {
    expand: false,
    functionCode: 'EMAIL_ACCOUNT',
    icon: 'airline_seat_flat-o',
    id: 20,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '邮件账户',
    url: 'mail/sys_message_email_config.html',
    symbol: '0',
    parentId: 19,
  },
  {
    expand: false,
    functionCode: 'EMAIL_TEMPLATE',
    icon: 'airline_seat_flat-o',
    id: 21,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '邮件模板',
    url: 'mail/sys_message_template.html',
    symbol: '0',
    parentId: 19,
  },
  {
    expand: false,
    functionCode: 'EMAIL_TEST',
    icon: 'airline_seat_flat-o',
    id: 22,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '邮件测试',
    url: 'mail/sys_message_test.html',
    symbol: '0',
    parentId: 19,
  },
  {
    expand: false,
    functionCode: 'EMAIL_STATUS',
    icon: 'airline_seat_flat-o',
    id: 23,
    ischecked: true,
    score: 40,
    shortcutId: null,
    name: '邮件状态查询',
    url: 'mail/message_status.html',
    symbol: '0',
    parentId: 19,
  },
  {
    expand: false,
    functionCode: 'SYS_REPORT_LIST',
    icon: 'airline_seat_flat-o',
    id: 25,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '报表定义',
    url: 'rpt/report.html',
    symbol: '0',
    parentId: 24,
  },
  {
    expand: false,
    functionCode: 'SYS_REPORT_DESIGN',
    icon: 'airline_seat_flat-o',
    id: 26,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '报表设计',
    url: 'ureport/designer',
    symbol: '0',
    parentId: 24,
  },
  {
    expand: false,
    functionCode: 'WFL_TEST',
    icon: 'airline_seat_flat-o',
    id: 30,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '工作流测试',
    url: 'activiti/start_process_test.html',
    symbol: '0',
    parentId: 27,
  },
  {
    expand: false,
    functionCode: 'WFL_VACATION_TEST',
    icon: 'airline_seat_flat-o',
    id: 38,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '工作流测试(请假申请)',
    url: 'activiti/demo/vacation_list.html',
    symbol: '0',
    parentId: 27,
  },
  {
    expand: false,
    functionCode: 'WFL_TASK',
    icon: 'airline_seat_flat-o',
    id: 28,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '待办事项(管理员)',
    url: 'activiti/task_list.html',
    symbol: '0',
    parentId: 27,
  },
  {
    expand: false,
    functionCode: 'WFL_MODEL',
    icon: 'airline_seat_flat-o',
    id: 29,
    ischecked: true,
    score: 40,
    shortcutId: null,
    name: '流程设计',
    url: 'activiti/models.html',
    symbol: '0',
    parentId: 27,
  },
  {
    expand: false,
    functionCode: 'WFL_DEFINITION',
    icon: 'airline_seat_flat-o',
    id: 31,
    ischecked: true,
    score: 50,
    shortcutId: null,
    name: '流程部署',
    url: 'activiti/process_definitions.html',
    symbol: '0',
    parentId: 27,
  },
  {
    expand: false,
    functionCode: 'WFL_LOG',
    icon: 'airline_seat_flat-o',
    id: 33,
    ischecked: true,
    score: 60,
    shortcutId: null,
    name: '报错日志',
    url: 'activiti/execption.html',
    symbol: '0',
    parentId: 27,
  },
  {
    expand: false,
    functionCode: 'WFL_MONITOR',
    icon: 'airline_seat_flat-o',
    id: 32,
    ischecked: true,
    score: 60,
    shortcutId: null,
    name: '流程监控',
    url: 'activiti/process_monitor.html',
    symbol: '0',
    parentId: 27,
  },
  {
    expand: false,
    functionCode: 'WFL_APPROVE',
    icon: 'airline_seat_flat-o',
    id: 34,
    ischecked: true,
    score: 90,
    shortcutId: null,
    name: '审批配置',
    url: null,
    symbol: '0',
    parentId: 27,
  },
  {
    expand: false,
    functionCode: 'WFL_APV_STRATEGY',
    icon: 'airline_seat_flat-o',
    id: 35,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '审批方式',
    url: 'activiti/approval/approve_strategy.html',
    symbol: '0',
    parentId: 34,
  },
  {
    expand: false,
    functionCode: 'WFL_APV_TYPE',
    icon: 'airline_seat_flat-o',
    id: 36,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '审批规则',
    url: 'activiti/approval/approve_candidate_rule.html',
    symbol: '0',
    parentId: 34,
  },
  {
    expand: false,
    functionCode: 'WFL_APV_RULE',
    icon: 'airline_seat_flat-o',
    id: 37,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '审批权限',
    url: 'activiti/approval/business_rule_header.html',
    symbol: '0',
    parentId: 34,
  },
  {
    expand: false,
    functionCode: 'WFL_AUTO_DELEGATE',
    icon: 'airline_seat_flat-o',
    id: 44,
    ischecked: true,
    score: 5,
    shortcutId: null,
    name: '自动转交配置',
    url: 'activiti/auto_delegate_config.html',
    symbol: '0',
    parentId: 39,
  },
  {
    expand: false,
    functionCode: 'WFL_MY_START',
    icon: 'airline_seat_flat-o',
    id: 41,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '我发起的流程',
    url: 'wfl/activiti/process_history_start.html',
    symbol: '0',
    parentId: 39,
  },
  {
    expand: false,
    functionCode: 'WFL_MY_TASK',
    icon: 'airline_seat_flat-o',
    id: 42,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '我的待办列表',
    url: 'activiti/my_task.html',
    symbol: '0',
    parentId: 39,
  },
  {
    expand: false,
    functionCode: 'WFL_HISROTY',
    icon: 'airline_seat_flat-o',
    id: 43,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '我参与的流程',
    url: 'activiti/process_history.html',
    symbol: '0',
    parentId: 39,
  },
  {
    expand: false,
    functionCode: 'WFL_CARBON',
    icon: 'airline_seat_flat-o',
    id: 40,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '我的抄送流程',
    url: 'wfl/activiti/process_history_carbon.html',
    symbol: '0',
    parentId: 39,
  },
  {
    expand: false,
    functionCode: 'IF_CONFIG',
    icon: 'airline_seat_flat-o',
    id: 46,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '接口定义',
    url: 'intergration/sys_interface_header.html',
    symbol: '0',
    parentId: 45,
  },
  {
    expand: false,
    functionCode: 'IF_INVOKE',
    icon: 'airline_seat_flat-o',
    id: 89,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '调用记录',
    url: 'intergration/sys_interface_invoke.html',
    symbol: '0',
    parentId: 45,
  },
  {
    expand: false,
    functionCode: 'IF_CLIENT',
    icon: 'airline_seat_flat-o',
    id: 47,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '客户端管理',
    url: 'sys/sys_oauth_client_details.html',
    symbol: '0',
    parentId: 45,
  },
  {
    expand: false,
    functionCode: 'IF_TOKEN',
    icon: 'airline_seat_flat-o',
    id: 48,
    ischecked: true,
    score: 40,
    shortcutId: null,
    name: '授权管理',
    url: 'sys/sys_token_logs.html',
    symbol: '0',
    parentId: 45,
  },
  {
    expand: false,
    functionCode: 'API_SERVER',
    icon: 'airline_seat_flat-o',
    id: 50,
    ischecked: true,
    score: 5,
    shortcutId: null,
    name: '服务注册',
    url: 'gateway/api_server.html',
    symbol: '0',
    parentId: 49,
  },
  {
    expand: false,
    functionCode: 'API_APPLICATION',
    icon: 'airline_seat_flat-o',
    id: 51,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '应用管理',
    url: 'gateway/api_application.html',
    symbol: '0',
    parentId: 49,
  },
  {
    expand: false,
    functionCode: 'API_INVOKE',
    icon: 'airline_seat_flat-o',
    id: 52,
    ischecked: true,
    score: 15,
    shortcutId: null,
    name: '调用记录',
    url: 'gateway/api_invoke_record.html',
    symbol: '0',
    parentId: 49,
  },
  {
    expand: false,
    functionCode: 'TASK_DETAIL',
    icon: 'airline_seat_flat-o',
    id: 54,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '任务管理',
    url: 'task/task_details.html',
    symbol: '0',
    parentId: 53,
  },
  {
    expand: false,
    functionCode: 'TASK_EXECUTE',
    icon: 'airline_seat_flat-o',
    id: 55,
    ischecked: true,
    score: 15,
    shortcutId: null,
    name: '任务执行',
    url: 'task/task_execute.html',
    symbol: '0',
    parentId: 53,
  },
  {
    expand: false,
    functionCode: 'TASK_EXECUTION',
    icon: 'airline_seat_flat-o',
    id: 56,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '执行记录',
    url: 'task/task_execution.html',
    symbol: '0',
    parentId: 53,
  },
  {
    expand: false,
    functionCode: 'TASK_ADMIN_EXECUTION',
    icon: 'airline_seat_flat-o',
    id: 57,
    ischecked: true,
    score: 25,
    shortcutId: null,
    name: '执行记录(管理员)',
    url: 'sys/task/execution/admin/task_execution.html',
    symbol: '0',
    parentId: 53,
  },
  {
    expand: false,
    functionCode: 'ATTACH_CATEGORY',
    icon: 'airline_seat_flat-o',
    id: 72,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '目录管理',
    url: 'attach/sys_attach_category_manage.html',
    symbol: '0',
    parentId: 69,
  },
  {
    expand: false,
    functionCode: 'ATTACH_FILE',
    icon: 'airline_seat_flat-o',
    id: 70,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '文件管理',
    url: 'attach/sys_file_manage.html',
    symbol: '0',
    parentId: 69,
  },
  {
    expand: false,
    functionCode: 'ATTACH_TEST',
    icon: 'airline_seat_flat-o',
    id: 71,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '上传测试',
    url: 'attach/sys_attachment_create.html',
    symbol: '0',
    parentId: 69,
  },
  {
    expand: false,
    functionCode: 'FLEX_FIELD_MODEL',
    icon: 'airline_seat_flat-o',
    id: 80,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '弹性域模型',
    url: 'flexfield/flex_model.html',
    symbol: '0',
    parentId: 79,
  },
  {
    expand: false,
    functionCode: 'FLEX_FIELD_RULE_SET',
    icon: 'airline_seat_flat-o',
    id: 81,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '弹性域规则',
    url: 'flexfield/flex_rule_set.html',
    symbol: '0',
    parentId: 79,
  },
  {
    expand: false,
    functionCode: 'FLEX_FIELD_DEMO',
    icon: 'airline_seat_flat-o',
    id: 82,
    ischecked: true,
    score: 30,
    shortcutId: null,
    name: '弹性域示例',
    url: 'demo/flexfield.html',
    symbol: '0',
    parentId: 79,
  },
  {
    expand: false,
    functionCode: 'DATA_PERMISSION_RULE',
    icon: 'airline_seat_flat-o',
    id: 85,
    ischecked: true,
    score: 10,
    shortcutId: null,
    name: '屏蔽规则管理',
    url: 'permission/data_permission_rule.html',
    symbol: '0',
    parentId: 84,
  },
  {
    expand: false,
    functionCode: 'DATA_PERMISSION_TABLE',
    icon: 'airline_seat_flat-o',
    id: 86,
    ischecked: true,
    score: 20,
    shortcutId: null,
    name: '屏蔽权限设置',
    url: 'permission/data_permission_table.html',
    symbol: '0',
    parentId: 84,
  },
];
