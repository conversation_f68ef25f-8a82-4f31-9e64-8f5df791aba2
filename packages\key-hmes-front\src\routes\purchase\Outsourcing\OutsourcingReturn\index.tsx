/**
 * @Description: 创建外协退料单
 * @Author: <<EMAIL>>
 * @Date: 2022-01-17 14:36:48
 * @LastEditTime: 2023-05-18 15:30:53
 * @LastEditors: <<EMAIL>>
 */
// @ts-ignore
import React, { useMemo, useState, useEffect } from 'react';
import { Header, Content } from 'components/Page';
import {
  DataSet,
  Form,
  TextField,
  DateTimePicker,
  Table,
  Button,
  Lov,
  NumberField,
  Spin,
  Select,
  Switch,
  Icon,
} from 'choerodon-ui/pro';
import { Popconfirm, Badge, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/lib/_util/enum';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { ColumnAlign, TableColumnTooltip, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { isNull, flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { C7nFormItemSort } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { queryMapIdpValue } from 'services/api';
import { BASIC, API_HOST } from '@/utils/config';
import { headDS, tableDS } from '../stores/OutsourcingReturnDS';

const { Panel } = Collapse;
const _urlEnd = '';

const modelPrompt = 'tarzan.hmes.purchase.outsourcingManage';

// 行查询接口
export function QueryLine() {
  return {
    url: `${BASIC.HWMS_BASIC}${_urlEnd}/v1/${getCurrentOrganizationId()}/mt-out-source/line/create/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_RETURN.HEAD,${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_RETURN.LINE`,
    method: 'GET',
  };
}

// 保存
export function Preservation() {
  return {
    url: `${API_HOST}${BASIC.HWMS_BASIC}/v1/${getCurrentOrganizationId()}/mt-out-source/out-source-return/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_RETURN.HEAD,${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_RETURN.LINE`,
    method: 'POST',
  };
}

// 根据库位获取现有量的接口
export function AvailableQuantity() {
  return {
    url: `${API_HOST}${BASIC.HWMS_BASIC}/v1/${getCurrentOrganizationId()}/mt-inv-onhand-quantity/sum-available-qty/ui`,
    method: 'POST',
  };
}

const CreateDetail = props => {
  const {
    match: { path },
    customizeForm,
    customizeTable,
  } = props;

  const headDs = useMemo(() => new DataSet(headDS()), []);

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const [poNumber, setPoNumber] = useState(); // 是否已保存
  const [statue, setStatus] = useState(false);

  const [statueLov, setStatusLov] = useState(true); // 行站点lov和物料lov是否可选

  const [isSubmit, setIsSubmit] = useState(false); // 是否已保存

  const preservation = useRequest(Preservation(), {
    manual: true,
  });

  const availableQuantity = useRequest(AvailableQuantity(), {
    manual: true,
  });

  const queryLine = useRequest(QueryLine(), {
    manual: true,
  });

  useEffect(() => {
    handleFetchDefaultSourceSystem();
  }, []);

  const handleFetchDefaultSourceSystem = () => {
    queryMapIdpValue({
      sourceSystemList: 'SOURCE_SYSTEM',
    }).then(res => {
      if(res) {
        const defaultSourceSystem = res.sourceSystemList.find(e => e.tag === "Y");
        if(defaultSourceSystem) {
          headDs.current.set('sourceSystem', defaultSourceSystem.value);
        };
      }
    });
  };

  // 行新增
  const handleAdd = () => {
    const listData = tableDs.toData();
    let maxNumber = 0;
    listData.forEach(item => {
      const { lineNumber } = item as any;
      if (lineNumber) {
        if (lineNumber > maxNumber) {
          maxNumber = lineNumber;
        }
      }
    });
    tableDs.create({
      lineNumber: parseInt(String(maxNumber / 10), 10) * 10 + 10,
      actualQty: 0,
      isQuantity: 'Y',
      sumAvailableQty: 0,
      quantityExists: 0,
    });
  };
  // 行删除
  const handleDelete = record => {
    tableDs.delete(record, false);
    setTimeout(() => {
      lineNumFormat();
    }, 0);
  };

  const lineNumFormat = () => {
    const lineNums = [];
    tableDs.forEach(item => {
      // @ts-ignore
      lineNums.push(`${item.get('poLineNum')}`);
    });
    // @ts-ignore
    const { outSourcePoLov } = headDs.toData()[0];
    const _outSourcePoLov = [];
    if (outSourcePoLov) {
      outSourcePoLov.forEach(item => {
        // @ts-ignore
        if (lineNums.indexOf(`${item.lineNum}`) > -1) {
          // @ts-ignore
          _outSourcePoLov.push(item);
        }
      });
    }
    if (_outSourcePoLov.length === 0) {
      setPoNumber(undefined);
      headDs!.current!.set('poHeaderId', null);
      headDs!.current!.set('poNumber', null);
      headDs!.current!.set('siteId', null);
      headDs!.current!.set('supplierCode', null);
      headDs!.current!.set('supplierId', null);
      headDs!.current!.set('supplierSiteCode', null);
      headDs!.current!.set('supplierSiteId', null);
      headDs!.current!.set('receivingAddress', null);
      headDs!.current!.set('outSourcePoId', null);
      headDs!.current!.set('outSourcePoNum', null);
      setStatus(false);
      setStatusLov(true);
    }
    headDs!.current!.set('outSourcePoLov', _outSourcePoLov);
    tableDs.forEach((item, index) => {
      item.init('lineNumber', (index + 1) * 10);
    });
  };

  // 行物料lov选择时
  const hanleMaterial = (record, value) => {
    if (isNull(value)) {
      record.set('revisionFlag', null);
      record.set('materialName', null);
      record.set('uomCode', null);
      record.set('uomId', null);
      record.set('revisionCode', null);
    } else {
      const { revisionFlag, materialName, uomCode, uomId, currentRevisionCode } = value;
      record.set('revisionFlag', revisionFlag);
      record.set('materialName', materialName);
      record.set('uomCode', uomCode);
      record.set('uomId', uomId);
      if (!isNull(currentRevisionCode)) {
        record.set('revisionCode', currentRevisionCode);
      }
    }
  };

  // 允差标识改变时
  const handleFlag = (record, value) => {
    if (value === 'N') {
      record.set('toleranceType', null);
      record.set('toleranceMaxValue', null);
      record.set('toleranceMinValue', null);
    }
  };

  const getPageDetail = (record, data) => {
    const { materialId, revisionCode, siteId } = record.toData();
    const { locatorId } = data;
    availableQuantity.run({
      params: {
        materialId,
        revisionCode,
        locatorId,
        siteId,
      },
      onSuccess: res => {
        record.set('sumAvailableQty', res);
      },
    });
  };

  // 退货仓库改变时
  const handleToSite = (record, data) => {
    if (!isNull(data)) {
      getPageDetail(record, data);
    } else {
      record.set('sumAvailableQty', 0);
    }
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          funcType="flat"
          shape="circle"
          size="small"
          disabled={isSubmit || statue}
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 60,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button icon="remove" disabled={isSubmit} funcType={FuncType.flat} size={Size.small} />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'siteLov',
      align: ColumnAlign.left,
      width: 150,
      editor: () => (!isSubmit ? statueLov && <Lov noCache /> : false),
    },
    {
      name: 'materialLov',
      align: ColumnAlign.left,
      width: 150,
      editor: record =>
        !isSubmit
          ? statueLov && <Lov onChange={value => hanleMaterial(record, value)} noCache />
          : false,
    },
    {
      name: 'revisionCode',
      align: ColumnAlign.left,
      tooltip: TableColumnTooltip.overflow,
      width: 150,
      editor: () => !isSubmit && !statue && <Select noCache />,
    },
    {
      name: 'materialName',
      align: ColumnAlign.left,
      width: 150,
    },
    {
      name: 'actualQty',
      width: 100,
      align: ColumnAlign.right,
    },
    {
      name: 'quantityExists',
      width: 100,
      align: ColumnAlign.right,
    },
    {
      name: 'quantity',
      width: 120,
      align: ColumnAlign.right,
      editor: () => (!isSubmit ? <NumberField step={1} /> : false),
    },
    {
      name: 'uomCode',
      width: 100,
      align: ColumnAlign.left,
    },
    {
      name: 'toSiteLov',
      width: 170,
      align: ColumnAlign.left,
      editor: record =>
        !isSubmit ? <Lov onChange={value => handleToSite(record, value)} noCache /> : false,
    },
    {
      name: 'sumAvailableQty',
      align: ColumnAlign.right,
      width: 120,
    },
    {
      name: 'poNumber',
      align: ColumnAlign.left,
      width: 150,
    },
    {
      name: 'poLineNum',
      align: ColumnAlign.left,
      width: 150,
    },

    {
      name: 'toleranceFlag',
      align: ColumnAlign.center,
      width: 150,
      editor: record =>
        !isSubmit ? (
          <Switch readOnly={isSubmit} onChange={value => handleFlag(record, value)} />
        ) : (
          false
        ),
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      align: ColumnAlign.left,
      width: 150,
      editor: () => (!isSubmit ? <Select disabled={isSubmit} /> : false),
    },
    {
      name: 'toleranceMaxValue',
      align: ColumnAlign.left,
      width: 150,
      editor: () => (!isSubmit ? <NumberField nonStrictStep precision={6} step={1} /> : false),
    },
    {
      name: 'toleranceMinValue',
      align: ColumnAlign.left,
      width: 150,
      editor: () => (!isSubmit ? <NumberField nonStrictStep precision={6} step={1} /> : false),
    },
  ];

  // 保存校验
  const handleSaveCheck = async () => {
    const validate = await headDs.validate();
    const validateTable = await tableDs.validate();
    if (validate && validateTable) {
      const _tableDs = [];
      tableDs.toData().forEach(item => {
        // @ts-ignore
        if (item.quantity > 0) {
          // @ts-ignore
          _tableDs.push({ ...item });
        }
      });
      preservation.run({
        params: {
          outSourceReturnHeader: headDs.toData()[0],
          outSourceReturnLine: _tableDs,
        },
        onSuccess: res => {
          // eslint-disable-next-line no-unused-expressions
          headDs.current?.set('outSourceReturnNum', res);
          setIsSubmit(true);
          tableDs.loadData(_tableDs);
          notification.success({
            message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
          });
        },
      });
    }
  };

  // 取消
  const handleCancel = () => {
    props.history.push({
      pathname: `/hmes/purchase/outsourcing-manage/list`,
      state: {
        queryFlag: isSubmit,
      },
    });
  };

  // 选择单据前判断
  const outSourcePoLovOnBeforeSelect = records => {
    const poHeaderIds = [];
    records.forEach(item => {
      // @ts-ignore
      if (poHeaderIds.indexOf(item.toData().poHeaderId) === -1) {
        // @ts-ignore
        poHeaderIds.push(item.toData().poHeaderId);
      }
    });
    if (!(poHeaderIds.length === 1)) {
      notification.error({
        message: intl.get(`${modelPrompt}.operation.error`).d('请选择相同采购订单号'),
      });
    }
    return poHeaderIds.length === 1;
  };

  // 选择外协采购订单lov
  const handleSource = (value, oldValue) => {
    if (value) {
      if (value && oldValue && value[0]?.poNumber !== oldValue[0]?.poNumber) {
        tableDs.loadData([]);
      }
      headDs!.current!.set('poHeaderId', value[0].poHeaderId);
      headDs!.current!.set('poNumber', value[0].poNumber);
      headDs!.current!.set('siteId', value[0].siteId);
      headDs!.current!.set('supplierCode', value[0].supplierCode);
      headDs!.current!.set('supplierId', value[0].supplierId);
      headDs!.current!.set('supplierName', value[0].supplierName);
      headDs!.current!.set('supplierSiteCode', value[0].supplierSiteCode);
      headDs!.current!.set('supplierSiteId', value[0].supplierSiteId);
      headDs!.current!.set('supplierSiteName', value[0].supplierSiteName);
      headDs!.current!.set('receivingAddress', value[0].receivingAddress);
      headDs!.current!.set('outSourcePoId', value[0].poHeaderId);
      headDs!.current!.set('outSourcePoNum', value[0].poNumber);

      setStatus(true);
      setStatusLov(false);

      setPoNumber(value[0].poNumber);

      const poHeaderIds = [];
      const lineNums = [];
      value.forEach(item => {
        // @ts-ignore
        if (poHeaderIds.indexOf(`${item.poHeaderId}`) === -1) {
          // @ts-ignore
          poHeaderIds.push(`${item.poHeaderId}`);
        }
        // @ts-ignore
        if (lineNums.indexOf(`${item.lineNum}`) === -1) {
          // @ts-ignore
          lineNums.push(`${item.lineNum}`);
        }
      });

      const addedLineNums = [];

      // @ts-ignore
      const toDeleteRecords: Record[] = [];

      tableDs.forEach(record => {
        // @ts-ignore
        if (lineNums.indexOf(`${record.get('poLineNum')}`) === -1) {
          toDeleteRecords.push(record);
        } else {
          // @ts-ignore
          addedLineNums.push(`${record.get('poLineNum')}`);
        }
      });

      const queryLineMuns = [];

      lineNums.forEach(item => {
        // @ts-ignore
        if (addedLineNums.indexOf(`${item}`) === -1) {
          // @ts-ignore
          queryLineMuns.push(`${item}`);
        }
      });

      tableDs.delete(toDeleteRecords, false);

      tableDs.forEach((record, index) => {
        record.init('lineNumber', (index + 1) * 10);
      });

      if (queryLineMuns.length === 0) {
        return;
      }

      queryLine.run({
        params: {
          instructionDocType: 'OUTSOURCING_RETURN_DOC',
          poHeaderIds,
          lineNums: queryLineMuns,
        },
        onSuccess: res => {
          const _res = res.map(item => {
            const setQty = headDs.current?.get('setQty');
            let newQuantity = 0;
            if (setQty > 0) {
              newQuantity = ((item.componentQty || 0) / (item.quantityOrdered || 0)) * setQty;
            } else {
              newQuantity =
                (item.componentQty || 0) - (item.sumAvailableQty || 0) - (item.haveSendQty || 0);
            }
            return {
              ...item,
              usedOverDeliveryQty: item.sumAvailableQty,
              quantity: newQuantity > 0 ? newQuantity : 0,
            };
          });
          _res.forEach(item => {
            tableDs.create(item, tableDs.length);
            // get
          });
          tableDs.forEach((record, index) => {
            record.init('lineNumber', (index + 1) * 10);
          });
        },
        onFailed: () => {
          // eslint-disable-next-line no-unused-expressions
          headDs.current?.set('outSourcePoLov', undefined);
        },
      });
    } else {
      tableDs.loadData([]);
      headDs!.current!.set('poHeaderId', null);
      headDs!.current!.set('poNumber', null);
      headDs!.current!.set('siteId', null);
      headDs!.current!.set('supplierCode', null);
      headDs!.current!.set('supplierId', null);
      headDs!.current!.set('supplierSiteCode', null);
      headDs!.current!.set('supplierSiteId', null);
      headDs!.current!.set('receivingAddress', null);
      headDs!.current!.set('outSourcePoId', null);
      headDs!.current!.set('outSourcePoNum', null);
      setPoNumber(undefined);
      setStatus(false);
      setStatusLov(true);
    }
  };

  // 供应商lov改变时
  const handleSupplier = value => {
    if (isNull(value)) {
      // eslint-disable-next-line no-unused-expressions
      headDs.current?.set('supplierSiteLov', undefined);
    }
  };

  // 套数改变时
  const handleNumber = value => {
    tableDs.forEach(record => {
      // @ts-ignore
      record.init('quantity', isNull(value) ? 0 : (record.data?.quantityTemp || 0) * value);
    });
  };

  const revisionLinkBom = () => {
    const _num = headDs?.current?.get('poNumber');
    if (_num) {
      props.history.push({
        pathname: `/hmes/purchase/order-management/list`,
        // @ts-ignore
        query: {
          poNumber: _num,
        },
      });
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.create.materialReturn`).d('创建外协退料单')}
        backPath="/hmes/purchase/outsourcing-manage/list"
        onBack={handleCancel}
      >
        {!isSubmit && (
          <div>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <PermissionButton
              icon="save"
              type="c7n-pro"
              color={ButtonColor.primary}
              loading={preservation.loading}
              onClick={handleSaveCheck}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
          </div>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.head`).d('头信息')}
            key="basicInfo"
            dataSet={headDs}
          >
            <Spin dataSet={headDs}>
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_RETURN.HEAD`,
                },
                <Form
                  dataSet={headDs}
                  labelLayout={LabelLayout.horizontal}
                  disabled={isSubmit}
                  labelWidth={112}
                  columns={3}
                >
                  <TextField disabled name="outSourceReturnNum" />
                  <C7nFormItemSort name="outSourcePoLov" itemWidth={['92%', '8%']} colSpan={2}>
                    <Lov
                      name="outSourcePoLov"
                      onChange={(value, oldValue) => {
                        handleSource(value, oldValue);
                      }}
                      // @ts-ignore
                      onBeforeSelect={outSourcePoLovOnBeforeSelect}
                    />
                    <Icon
                      type="link2"
                      // @ts-ignore
                      itemType="link"
                      onClick={revisionLinkBom}
                      style={{
                        color: poNumber ? '#29bece' : 'rgba(0,0,0,0.25)',
                      }}
                      iconDisabled={!poNumber}
                    />
                  </C7nFormItemSort>
                  <Lov name="supplierLov" onChange={value => handleSupplier(value)} />
                  <Lov name="supplierSiteLov" />
                  <Select name="reason" />
                  <NumberField
                    name="setNumber"
                    nonStrictStep
                    precision={6}
                    step={1}
                    onChange={value => handleNumber(value)}
                  />
                  <DateTimePicker name="demandTime" />
                  <Select name='sourceSystem' disabled />
                  <TextField name="remark" />
                </Form>,
              )}
            </Spin>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.title.line`).d('行信息')}
            key="location"
            dataSet={tableDs}
          >
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_RETURN.LINE`,
              },
              <Table
                dataSet={tableDs}
                columns={columns}
                filter={record => {
                  return record.status !== 'delete';
                }}
              />,
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.hmes.purchase.outsourcingManage', 'tarzan.common'] }),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_RETURN.HEAD`, `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_RETURN.LINE`] }),
)(CreateDetail);
