/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-07-20 13:41:07
 * @LastEditors: <PERSON><PERSON>Q
 * @LastEditTime: 2023-07-20 15:23:34
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const API = `${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}`;
// const API = `/key-focus-model-30607/v1/${getCurrentOrganizationId()}`;

// 工艺与工作单元关系维护-删除关系
export function DeleteItems() {
  return {
    url: `${API}/mt-mod-locator/delete-erp/ui`,
    method: 'POST',
  };
}
