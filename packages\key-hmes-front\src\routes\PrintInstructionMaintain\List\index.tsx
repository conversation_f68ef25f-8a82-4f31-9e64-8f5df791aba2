/**
 * 打印指令模版维护
 * 
 * <AUTHOR> <<EMAIL>>
 * @date 2024-01-23 3:55:28 pm
 * @copyright Copyright (c) 2024 , Hand
 */


import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Table, Button, Modal, NumberField, TextArea, TextField, Select, Form } from 'choerodon-ui/pro';
import { flow } from "lodash";
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { listDS } from '../stores/ListDS';

const modelPrompt = 'hmes.printInstructionMaintain';

const PrintInstructionMaintain = (props: any) => {
  const { listDs } = props;

  // 删除按钮禁用
  const [deleteDisabled, setDeleteDisabled] = useState(true)

  useEffect(() => {
    listDs.addEventListener("batchSelect", handleSelectTable);
    listDs.addEventListener("batchUnSelect", handleSelectTable);
    return () => {
      listDs.removeEventListener("batchSelect", handleSelectTable);
      listDs.removeEventListener("batchUnSelect", handleSelectTable);
    };
  }, []);

  const handleSelectTable = () => {
    setDeleteDisabled(listDs.selected.length <= 0);
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'instructionTemplateCode',
        width: 200,
      },
      {
        name: 'description',
        width: 200,
      },
      {
        name: 'instructionLang',
      },
      {
        name: 'copies',
      },
      {
        name: 'instructionContent',
        width: 800,
      },
      {
        title: intl.get('tarzan.common.label.action').d('操作'),
        name: 'action',
        width: 150,
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        renderer: ({ record }) => (
          <a style={{ 'marginLeft': '20px' }} onClick={() => handleEdit(record)}>
            {intl.get(`${modelPrompt}.cancel`).d('编辑')}
          </a>
        ),
      },
    ],
    [],
  );

  /**
   * @description 编辑当前行
   * <AUTHOR>
   * @param {*} record
   */
  const handleEdit = (record) => {
    handleOpenDrawer(record);
  };

  /**
   * @description 批量删除
   * <AUTHOR>
   */
  const handleDelete = () => {
    if(listDs.selected.length > 0) {
      listDs.delete(listDs.selected, intl.get(`${modelPrompt}.message.deleteConfirm`).d('确认是否删除勾选的模版？'));
    }
  };


  /**
   * @description 批量保存
   * <AUTHOR>
   */
  const handleSave = async () => {
    const flag = await listDs.validate();
    if(flag) {
      return listDs.submit().then(() => {
        listDs.query(props.listDs.currentPage)
      })
    }
    return false;
  }

  /**
   * @description 编辑弹窗
   * <AUTHOR>
   * @param {Record} record
   */
  const handleOpenDrawer = (record) => {
    Modal.open({
      title: record.status === "add" ? intl.get(`${modelPrompt}.title.createInstruction`).d('新建指令模版') : intl.get(`${modelPrompt}.title.updateInstruction`).d('更新指令模版'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      children: (
        <Form dataSet={listDs} record={record}>
          <TextField disabled={record.status !== "add"} name="instructionTemplateCode" />
          <TextField disabled={record.status !== "add"} name="description" />
          <Select name="instructionLang" />
          <NumberField name="copies" />
          <TextArea name="instructionContent" rows={24} />
        </Form>
      ),
      onOk: handleSave,
      onCancel: () => {
        if(record.status === "add") {
          listDs.remove(record);
        } else {
          record.reset();
        }
      },
    });
  }

  /**
   * @description 新建方法
   * <AUTHOR>
   */
  const handleCreate = () => {
    const record = listDs.create({
      copies: 1,
    });
    handleOpenDrawer(record);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.instructionTemplateMaintain`).d('打印指令模版维护')}>
        <Button icon="delete" disabled={deleteDisabled} color={ButtonColor.red} onClick={handleDelete}>
          {intl.get('hzero.common.button.delete').d('删除')}
        </Button>
        <Button icon="add" color={ButtonColor.primary} onClick={handleCreate}>
          {intl.get('hzero.common.button.create').d('新建')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['hmes.instructionTemplateMaintain', 'tarzan.common'] }),
  withProps(() => {
    const listDs = new DataSet({
      ...listDS(),
    });
    return {
      listDs,
    };
  },
  { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(PrintInstructionMaintain);

