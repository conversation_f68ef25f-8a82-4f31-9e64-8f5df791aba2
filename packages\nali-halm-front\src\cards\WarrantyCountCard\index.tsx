import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Collapse } from 'choerodon-ui';
import { DataSet, Select } from 'choerodon-ui/pro/lib';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import ReactEchartsCore from 'echarts-for-react/lib/core';
import axios from 'axios';
import { HALM_ATN } from 'alm/utils/config';
import queryDS from './Stores';
import styles from './index.module.less';

const organizationId = getCurrentOrganizationId();
const url = `${HALM_ATN}/v1/${organizationId}/table-cards/warranty-count`;

const WarrantyCountCard = () => {
  const [data, setData] = useState<any>(undefined);
  const [allOrderCount, setAllOrderCount] = useState<number>(0);
  useEffect(() => {
    fetchData();
  }, []);

  const queryDs = useMemo(() => {
    return new DataSet(queryDS());
  }, []);

  const fetchData = useCallback(async () => {
    const res = await axios.get<any, any>(url, {
      params: queryDs?.current?.data,
    });
    setData(res.dtos);
    setAllOrderCount(res.allOrderCount);
  }, []);

  const renderTooltip = useCallback(params => {
    const line1 = `<span style="color:#282828;font-weight:bold;margin:0 20px">报修次数</span><br/>`;
    const percentText = `<span style="color:#3B87F5;margin: 8px 0"> ${params[0].value}</span>`;
    return line1 + percentText;
  }, []);

  const colorRenderer = useCallback(({ text }) => {
    return <span style={{ color: '#666' }}>{text}</span>;
  }, []);

  const option = useMemo(() => {
    return {
      grid: {
        top: 40,
        left: 30,
        right: 20,
        bottom: 30,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#FFF',
        extraCssText: 'box-shadow: 0 2px 10px 5px rgba(124,133,155,0.10);text-align: center;',
        axisPointer: {
          type: 'none',
        },
        formatter: params => renderTooltip(params),
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          axisTick: {
            length: 3,
            alignWithLabel: true,
          },
          axisLabel: {
            color: '#A9A9A9',
          },
          axisLine: {
            lineStyle: {
              color: '#CCCCCC',
            },
          },
          splitLine: {
            show: true,
            interval: 0,
          },
          data: data?.map(i => i.dt).sort((a, b) => Number(a.replace(/\-/g, '')) - Number(b.replace(/\-/g, ''))),
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '报修次数',
          minInterval: 1,
          axisTick: { show: false },
          axisLine: {
            lineStyle: {
              color: '#CCCCCC',
            },
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#A9A9A9',
          },
        },
      ],
      series: [
        {
          type: 'line',
          itemStyle: {
            color: '#4C77FE',
          },
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#6887E8', // 100% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgb(255,255,255, 0)', // 0% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },
          data: data?.map(i => i.orderCount),
        },
      ],
      dataZoom: data?.length > 7 && {
        realtime: true,
        height: 10,
        start: 0,
        end: 20,
        bottom: 0,
      },
    };
  }, [data]);
  return (
    <div className={styles.container}>
      <Collapse
        bordered={false}
        expandIconPosition="right"
        defaultActiveKey={['A']}
        trigger="icon"
        className={styles['customize-collapse']}
      >
        <Collapse.Panel key="A" showArrow={false} header="报修次数">
          <div className={styles['query-bar']}>
            <Select
              dataSet={queryDs}
              name="dateUnitCode"
              clearButton={false}
              onChange={fetchData}
              renderer={colorRenderer}
            />
          </div>
          <div className={styles.legend}>
            <span>{`总报修次数： ${allOrderCount}`}</span>
          </div>
          <ReactEchartsCore
            echarts={echarts}
            option={option}
            notMerge
            lazyUpdate
            style={{
              height: '100%',
            }}
          />
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default WarrantyCountCard;
