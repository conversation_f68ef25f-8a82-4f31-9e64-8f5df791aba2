import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  routes: [
    // 问题管理-问题管理平台
    {
      path: '/hwms/problem-management/problem-management-platform',
      routes: [
        {
          path: '/hwms/problem-management/problem-management-platform/list',
          component: '@/routes/ProblemManagement/ProblemManagementPlatform/ProblemManagementList',
        },
        {
          path: '/hwms/problem-management/problem-management-platform/full-text-search',
          component: '@/routes/ProblemManagement/ProblemManagementPlatform/FullTextSearch',
        },
        {
          path: '/hwms/problem-management/problem-management-platform/dist/:problemId',
          component: '@/routes/ProblemManagement/ProblemManagementPlatform/ProblemManagementDist',
        },
        {
          path: '/pub/hwms/problem-management/problem-management-platform/dist/:problemId',
          component: '@/routes/ProblemManagement/ProblemManagementPlatform/ProblemManagementDist',
        },
      ],
    },
    // 问题管理-问题横展平台
    {
      path: '/hwms/problem-management/transverse-expand',
      routes: [
        {
          path: '/hwms/problem-management/transverse-expand/list',
          component: '@/routes/ProblemManagement/TransverseExpand/TransverseExpandList',
        },
        {
          path: '/hwms/problem-management/transverse-expand/detail/:id',
          component: '@/routes/ProblemManagement/TransverseExpand/TransverseExpandDetail',
        },
        {
          path: '/pub/hwms/problem-management/transverse-expand/detail/:id',
          component: '@/routes/ProblemManagement/TransverseExpand/TransverseExpandDetail',
        },
      ],
    },
    // 问题管理-问题警示平台
    {
      path: '/hwms/problem-management/warning',
      routes: [
        {
          path: '/hwms/problem-management/warning/list',
          component: '@/routes/ProblemManagement/Warning/WarningList',
        },
        {
          path: '/hwms/problem-management/warning/detail/:id',
          component: '@/routes/ProblemManagement/Warning/WarningDetail',
        },
        {
          path: '/pub/hwms/problem-management/warning/detail/:id',
          component: '@/routes/ProblemManagement/Warning/WarningDetail',
        },
      ],
    },
    // 问题管理-问题复盘
    {
      path: '/hwms/problem-management/duplicate-problem',
      routes: [
        {
          path: '/hwms/problem-management/duplicate-problem/list',
          component: '@/routes/ProblemManagement/DuplicateProblem/List',
        },
        {
          path: '/hwms/problem-management/duplicate-problem/detail/:id',
          component: '@/routes/ProblemManagement/DuplicateProblem/Detail',
        },
      ],
    },
    // 问题管理-故障树
    {
      path: '/hwms/problem-tree',
      component: '@/routes/ProblemManagement/ProblemTree',
    },
    // 问题管理-举手填报
    {
      path: '/hwms/raise-hand-report',
      routes: [
        {
          path: '/hwms/raise-hand-report/list',
          component: '@/routes/ProblemManagement/RaiseHandReport/list',
        },
        {
          path: '/hwms/raise-hand-report/detail/:id',
          component: '@/routes/ProblemManagement/RaiseHandReport/detail',
        },
      ],
    },
    // 问题管理-问题管理工作台
    {
      path: '/hwms/problem-table',
      component: '@/routes/ProblemManagement/ProblemTable/ProblemTableList',
    },
    // 问题管理-问题积分表
    {
      path: '/hwms/problem-management/problem-score',
      routes: [
        {
          path: '/hwms/problem-management/problem-score/list',
          component: '@/routes/ProblemManagement/ProblemScore/ProblemScoreList',
        },
      ],
    },
    // 问题管理-问题管理监控看板
    {
      path: '/hwms/problem-management/problem-sum-info',
      routes: [
        {
          path: '/hwms/problem-management/problem-sum-info/list',
          component: '@/routes/ProblemManagement/ProblemSumInfo/ProblemSumInfoList',
        },
      ],
    },
    // 产品质量-检证库管理
    {
      path: '/hwms/verification-library-management-platform',
      routes: [
        {
          path: '/hwms/verification-library-management-platform/list',
          component:
            '@/routes/ProductQuality/VerificationLibraryManagementPlatform/VerificationibraryList',
        },
        {
          path: '/hwms/verification-library-management-platform/dist/:id',
          component:
            '@/routes/ProductQuality/VerificationLibraryManagementPlatform/VerificationibraryDetail',
        },
      ],
    },
    // 产品质量-SA/专项检证平台
    {
      path: '/hwms/inspect-execute/sa-verification-platform',
      routes: [
        {
          path: '/hwms/inspect-execute/sa-verification-platform/list',
          component: '@/routes/ProductQuality/SaVerificationPlatform/List',
        },
        {
          path: '/hwms/inspect-execute/sa-verification-platform/dist/:verificationId',
          component: '@/routes/ProductQuality/SaVerificationPlatform/Detail',
        },
        {
          path: '/pub/hwms/inspect-execute/sa-verification-platform/dist/:verificationId',
          component: '@/routes/ProductQuality/SaVerificationPlatform/Detail',
        },
      ],
    },
    // 产品质量-检验任务执行平台
    {
      path: '/hwms/inspect-execute/task-exe-platform',
      routes: [
        {
          path: '/hwms/inspect-execute/task-exe-platform/list',
          component: '@/routes/ProductQuality/TaskExePlatform/List',
        },
        {
          path: '/hwms/inspect-execute/task-exe-platform/dist/:verificationTaskGroupId',
          component: '@/routes/ProductQuality/TaskExePlatform/Detail',
        },
      ],
    },
    // 产品质量-产线审核模板管理平台
    {
      path: '/hwms/prodline_template_management_platform',
      routes: [
        {
          path: '/hwms/prodline_template_management_platform/list',
          component:
            '@/routes/ProductQuality/ProdlineTemplateManagementPlatform/ProdlineTemplateList',
        },
        {
          path: '/hwms/prodline_template_management_platform/dist/:id',
          component:
            '@/routes/ProductQuality/ProdlineTemplateManagementPlatform/ProdlineTemplateDetail',
        },
      ],
    },
    // 产品质量-产线审核计划管理平台
    {
      path: '/hwms/production-line-audit-plan-management-platform',
      routes: [
        {
          path: '/hwms/production-line-audit-plan-management-platform/list',
          component: '@/routes/ProductQuality/ProductionLineAuditPlanManagementPlatform/ListTable',
        },
        {
          path: '/hwms/production-line-audit-plan-management-platform/create/:id',
          component: '@/routes/ProductQuality/ProductionLineAuditPlanManagementPlatform/ListDetail',
        },
      ],
    },
    // 产品质量-产线审核任务执行平台
    {
      path: '/hwms/production_line_audit_task_execution_platform',
      routes: [
        {
          path: '/hwms/production_line_audit_task_execution_platform/list',
          component:
            '@/routes/ProductQuality/ProductionLineAuditTaskExecutionPlatform/ProductionLineAuditTaskExecutionList',
        },
        {
          path: '/hwms/production_line_audit_task_execution_platform/dist/:id',
          component:
            '@/routes/ProductQuality/ProductionLineAuditTaskExecutionPlatform/ProductionLineAuditTaskExecutionDetail',
        },
      ],
    },
    // 质量体系-标准体系文件维护
    {
      path: '/hwms/maintenance-of-standard-systemDocuments',
      routes: [
        {
          path: '/hwms/maintenance-of-standard-systemDocuments/list',
          component: '@/routes/QualitySystem/MaintenanceOfStandardSystemDocuments/ListTable',
        },
        {
          path: '/hwms/maintenance-of-standard-systemDocuments/dist/:id',
          component: '@/routes/QualitySystem/MaintenanceOfStandardSystemDocuments/ListDetail',
        },
      ],
    },
    // 质量体系-体系文件管理
    {
      path: '/hwms/system-document-management',
      routes: [
        {
          path: '/hwms/system-document-management/list',
          component: '@/routes/QualitySystem/SystemDocumentManagement/SystemDocumentList',
        },
        {
          path: '/hwms/system-document-management/detail/:id',
          component: '@/routes/QualitySystem/SystemDocumentManagement/SystemDocumentListDetail',
        },
        {
          path: '/pub/hwms/system-document-management/detail/:id',
          component: '@/routes/QualitySystem/SystemDocumentManagement/SystemDocumentListDetail',
        },
        {
          path: '/hwms/system-document-management/full-text-search',
          component: '@/routes/QualitySystem/SystemDocumentManagement/FullTextSearch',
        },
      ],
    },
    // 质量体系-体系审核管理
    {
      path: '/hwms/system-audit',
      routes: [
        {
          path: '/hwms/system-audit/list',
          component: '@/routes/QualitySystem/SystemAudit/SystemAuditList',
        },
        {
          path: '/hwms/system-audit/detail/:id',
          component: '@/routes/QualitySystem/SystemAudit/SystemAuditDetail',
        },
        {
          path: '/pub/hwms/system-audit/detail/:id',
          component: '@/routes/QualitySystem/SystemAudit/SystemAuditDetail',
        },
      ],
    },
    // 委托实验平台
    {
      path: '/hwms/commissioned-experimental-platform',
      routes: [
        {
          path: '/hwms/commissioned-experimental-platform/list',
          component: '@/routes/CommissionedExperimentalPlatform/ListTable',
        },
        {
          path: '/hwms/commissioned-experimental-platform/create/:id',
          component: '@/routes/CommissionedExperimentalPlatform/ListDetail',
        },
        {
          path: '/hwms/commissioned-experimental-platform/line/:id',
          component: '@/routes/CommissionedExperimentalPlatform/ListLineDetail',
        },
      ],
    },
    // 制程质量-产品拆解基础数据维护
    {
      path: '/hwms/product-disassembly-data-maintenance',
      routes: [
        {
          path: '/hwms/product-disassembly-data-maintenance/list',
          component: '@/routes/ProcessQuality/DataMaintenance/List',
        },
        {
          path: '/hwms/product-disassembly-data-maintenance/detail/:id',
          component: '@/routes/ProcessQuality/DataMaintenance/Detail',
        },
      ],
    },
    // 制程质量-拆解申请单
    {
      path: '/hwms/product-teardown/teardown-apply-doc',
      routes: [
        {
          path: '/hwms/product-teardown/teardown-apply-doc/list',
          component: '@/routes/ProcessQuality/TeardownApplyDoc/TeardownApplyList',
        },
        {
          path: '/hwms/product-teardown/teardown-apply-doc/dist/:teardownApplyId',
          component: '@/routes/ProcessQuality/TeardownApplyDoc/TeardownApplyDetail',
        },
        {
          path: '/pub/hwms/product-teardown/teardown-apply-doc/dist/:teardownApplyId',
          component: '@/routes/ProcessQuality/TeardownApplyDoc/TeardownApplyDetail',
        },
      ],
    },
    // 制程质量-拆解任务执行平台
    {
      path: '/hwms/disassemble/task-execution-platform',
      routes: [
        {
          path: '/hwms/disassemble/task-execution-platform/list',
          component: '@/routes/ProcessQuality/DisassembleTaskExecutionPlatform/List',
        },
        {
          path: '/hwms/disassemble/task-execution-platform/detail/:id',
          component: '@/routes/ProcessQuality/DisassembleTaskExecutionPlatform/Detail',
        },
      ],
    },
    // 制程质量-分层审核项目
    {
      path: '/hwms/hierarchical-audit-project',
      component: '@/routes/ProcessQuality/HierarchicalAuditProject/list',
    },
    // 制程质量-分层审核计划
    {
      path: '/hwms/hierarchical-audit-plan',
      routes: [
        {
          path: '/hwms/hierarchical-audit-plan/audit-plan/list',
          component: '@/routes/ProcessQuality/HierarchicalAudit/AuditPlan/AuditPlanList',
        },
        {
          path: '/hwms/hierarchical-audit-plan/audit-plan/detail/:id',
          component: '@/routes/ProcessQuality/HierarchicalAudit/AuditPlan/AuditPlanDetail',
        },
        {
          path: '/pub/hwms/hierarchical-audit-plan/audit-plan/detail/:id',
          component: '@/routes/ProcessQuality/HierarchicalAudit/AuditPlan/AuditPlanDetail',
        },
      ],
    },
    // 制程质量-分层审核方案
    {
      path: '/hwms/layer-review/layer-review-scheme',
      routes: [
        {
          path: '/hwms/layer-review/layer-review-scheme/list',
          component: '@/routes/ProcessQuality/LayerReviewScheme/LayerReviewSchemeList',
        },
        {
          path: '/hwms/layer-review/layer-review-scheme/dist/:layerReviewSchemeId',
          component: '@/routes/ProcessQuality/LayerReviewScheme/LayerReviewSchemeDetail',
        },
        {
          path: '/pub/hwms/layer-review/layer-review-scheme/dist/:layerReviewSchemeId',
          component: '@/routes/ProcessQuality/LayerReviewScheme/LayerReviewSchemeDetail',
        },
      ],
    },
    // 制程质量-分层审核任务
    {
      path: '/hwms/layer-review/layer-review-task',
      routes: [
        {
          path: '/hwms/layer-review/layer-review-task/list',
          component: '@/routes/ProcessQuality/LayerReviewTask/LayerReviewTaskList',
        },
        {
          path: '/hwms/layer-review/layer-review-task/dist/:layerReviewTaskId',
          component: '@/routes/ProcessQuality/LayerReviewTask/LayerReviewTaskDetail',
        },
      ],
    },
    // 制程质量-产品审核-产品审核项目
    {
      path: '/hwms/product-review/product-review-item',
      routes: [
        {
          path: '/hwms/product-review/product-review-item/list',
          component:
            '@/routes/ProcessQuality/ProductReview/ProductReviewItem/ProductReviewItemList',
        },
      ],
    },
    // 制程质量-产品审核-产品审核方案
    {
      path: '/hwms/product-review/product-review-scheme',
      routes: [
        {
          path: '/hwms/product-review/product-review-scheme/list',
          component: '@/routes/ProcessQuality/ProductReview/ProductReviewScheme/List',
        },
        {
          path: '/hwms/product-review/product-review-scheme/:id',
          component: '@/routes/ProcessQuality/ProductReview/ProductReviewScheme/Detail',
        },
        {
          path: '/pub/hwms/product-review/product-review-scheme/:id',
          component: '@/routes/ProcessQuality/ProductReview/ProductReviewScheme/Detail',
        },
      ],
    },
    // 制程质量-产品审核-产品审核计划
    {
      path: '/hwms/product-review/product-review-plan',
      routes: [
        {
          path: '/hwms/product-review/product-review-plan/list',
          component:
            '@/routes/ProcessQuality/ProductReview/ProductReviewPlan/ProductReviewPlanList',
        },
        {
          path: '/hwms/product-review/product-review-plan/dist/:id',
          component:
            '@/routes/ProcessQuality/ProductReview/ProductReviewPlan/ProductReviewPlanDist',
        },
        {
          path: '/pub/hwms/product-review/product-review-plan/dist/:id',
          component:
            '@/routes/ProcessQuality/ProductReview/ProductReviewPlan/ProductReviewPlanDist',
        },
      ],
    },
    // 制程质量-产品审核-产品审核任务
    {
      path: '/hwms/product-review/product-review-task',
      routes: [
        {
          path: '/hwms/product-review/product-review-task/list',
          component:
            '@/routes/ProcessQuality/ProductReview/ProductReviewTask/ProductReviewTaskList',
        },
        {
          path: '/hwms/product-review/product-review-task/dist/:id',
          component:
            '@/routes/ProcessQuality/ProductReview/ProductReviewTask/ProductReviewTaskDist',
        },
        {
          path: '/pub/hwms/product-review/product-review-task/dist/:id',
          component:
            '@/routes/ProcessQuality/ProductReview/ProductReviewTask/ProductReviewTaskDist',
        },
      ],
    },
    // 制程质量-ORT测试执行
    {
      path: '/hwms/ort/test-execution',
      routes: [
        {
          path: '/hwms/ort/test-execution/list',
          component: '@/routes/ProcessQuality/ORT/TestExecution/TestExecutionList',
        },
        {
          path: '/hwms/ort/test-execution/detail/:id',
          component: '@/routes/ProcessQuality/ORT/TestExecution/TestExecutionDetail',
        },
        // {
        //   path: '/pub/hwms/ort/test-execution/detail/:id',
        //   component: '@/routes/ProcessQuality/ORT/TestExecution/TestExecutionDetail',
        // },
      ],
    },
    // 制程质量-ORT检验方案维护
    {
      path: '/hmes/ort/inspection-scheme',
      routes: [
        {
          path: '/hmes/ort/inspection-scheme/list',
          component: '@/routes/ProcessQuality/ORT/InspectionScheme/List',
        },
        {
          path: '/hmes/ort/inspection-scheme/:id',
          component: '@/routes/ProcessQuality/ORT/InspectionScheme/Detail',
        },
      ],
    },
    // 制程质量-ORT测试申请
    {
      path: '/hwms/ort/test-application',
      routes: [
        {
          path: '/hwms/ort/test-application/list',
          component: '@/routes/ProcessQuality/ORT/TestApplication/TestApplicationList',
        },
        {
          path: '/hwms/ort/test-application/detail/:id',
          component: '@/routes/ProcessQuality/ORT/TestApplication/TestApplicationDetail',
        },
        {
          path: '/pub/hwms/ort/test-application/detail/:id',
          component: '@/routes/ProcessQuality/ORT/TestApplication/TestApplicationDetail',
        },
      ],
    },
    // 市场质量-维修标准管理
    {
      path: '/hwms/maintenance-standard',
      component: '@/routes/MarketQuality/MaintenanceStandard',
    },
    // 市场质量-质量反馈平台
    {
      path: '/hwms/market-quality/quality-feedback-platform',
      routes: [
        {
          path: '/hwms/market-quality/quality-feedback-platform/list',
          component: '@/routes/MarketQuality/QualityFeedbackPlatform/FeedbackPlatformList',
        },
        {
          path: '/hwms/market-quality/quality-feedback-platform/dist/:id',
          component: '@/routes/MarketQuality/QualityFeedbackPlatform/FeedbackPlatformDist',
        },
      ],
    },
    // 市场质量-一次解析责任判定
    {
      path: '/hwms/market-quality/liability-judgment',
      routes: [
        {
          path: '/hwms/market-quality/liability-judgment/list',
          component: '@/routes/MarketQuality/LiabilityJudgment/List',
        },
        {
          path: '/hwms/market-quality/liability-judgment/:id',
          component: '@/routes/MarketQuality/LiabilityJudgment/Detail',
        },
      ],
    },
    // 市场质量-市场活动评估
    {
      path: '/hwms/market-quality/market-estimate-doc',
      routes: [
        {
          path: '/hwms/market-quality/market-estimate-doc/list',
          component: '@/routes/MarketQuality/MarketEstimateDoc/MarketEstimateList',
        },
        {
          path: '/hwms/market-quality/market-estimate-doc/dist/:id',
          component: '@/routes/MarketQuality/MarketEstimateDoc/MarketEstimateDist',
        },
      ],
    },
    // 市场质量-市场活动管理
    {
      path: '/hwms/market-quality/market-activity-doc',
      routes: [
        {
          path: '/hwms/market-quality/market-activity-doc/list',
          component: '@/routes/MarketQuality/MarketActivityDoc/MarketActivityList',
        },
        {
          path: '/hwms/market-quality/market-activity-doc/dist/:id',
          component: '@/routes/MarketQuality/MarketActivityDoc/MarketActivityDist',
        },
        {
          path: '/pub/hwms/market-quality/market-activity-doc/dist/:id',
          component: '@/routes/MarketQuality/MarketActivityDoc/MarketActivityDist',
        },
      ],
    },
    // 市场质量-索赔管理
    {
      path: '/hwms/market-quality/claim-doc-management',
      routes: [
        {
          path: '/hwms/market-quality/claim-doc-management/list',
          component: '@/routes/MarketQuality/ClaimDocManagement/ClaimDocList',
        },
        {
          path: '/hwms/market-quality/claim-doc-management/dist/:id',
          component: '@/routes/MarketQuality/ClaimDocManagement/ClaimDocDist',
        },
      ],
    },
    // 市场质量-原因分类维护
    {
      path: '/hwms/market-quality/reason-classify-maintain',
      routes: [
        {
          path: '/hwms/market-quality/reason-classify-maintain/list',
          component: '@/routes/MarketQuality/ReasonClassifyMaintain/ReasonClassifyList',
        },
      ],
    },
    // 市场质量-旧件台账管理
    {
      path: '/hwms/used-parts-management',
      component: '@/routes/MarketQuality/UsedPartsManagement',
    },
    // 检验策略-检验方案维护
    {
      path: '/hwms/inspection-scheme-maintenance',
      routes: [
        {
          path: '/hwms/inspection-scheme-maintenance/list',
          component: '@/routes/TestingStrategy/InspectionScheme/InspectionSchemeList',
        },
        {
          path: '/hwms/inspection-scheme-maintenance/detail/:id',
          component: '@/routes/TestingStrategy/InspectionScheme/InspectionSchemeDetail',
        },
      ],
    },
    // 检验策略-检验方案模板维护
    {
      path: '/hwms/inspection-scheme-template-maintenance',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-scheme-template-maintenance/list',
          component:
            '@/routes/TestingStrategy/InspectionSchemeTemplate/InspectionSchemeTemplateList',
          priority: 10,
        },
        {
          path: '/hwms/inspection-scheme-template-maintenance/detail/:id',
          component:
            '@/routes/TestingStrategy/InspectionSchemeTemplate/InspectionSchemeTemplateDetail',
          priority: 10,
        },
      ],
    },
    // 检验策略-抽样方式维护
    {
      path: '/sampling/sampling-method/sampling-mode',
      routes: [
        {
          path: '/sampling/sampling-method/sampling-mode/list',
          component: '@/routes/TestingStrategy/SamplingMode/SamplingModeList',
        },
        {
          path: '/sampling/sampling-method/sampling-mode/detail/:id',
          component: '@/routes/TestingStrategy/SamplingMode/SamplingModeDetail',
        },
      ],
    },
    // 检验策略-用户检验权限维护
    {
      path: '/hwms/user-inspect-permission',
      routes: [
        {
          path: '/hwms/user-inspect-permission/list',
          component: '@/routes/TestingStrategy/UserInspectPermission',
        },
        {
          path: '/hwms/user-inspect-permission/detail/:id/:siteId',
          component: '@/routes/TestingStrategy/UserInspectPermission/userDetail',
        },
      ],
    },
    // 检验策略-检验项目维护
    {
      path: '/hwms/inspect-item-maintain',
      routes: [
        {
          path: '/hwms/inspect-item-maintain/list',
          component: '@/routes/TestingStrategy/InspectItemMaintain/InspectItemList',
        },
        {
          path: '/hwms/inspect-item-maintain/dist/:id',
          component: '@/routes/TestingStrategy/InspectItemMaintain/InspectItemDetail',
        },
      ],
    },
    // 检验策略-检验项目组维护
    {
      path: '/hwms/inspect-group-maintenance',
      routes: [
        {
          path: '/hwms/inspect-group-maintenance/list',
          component: '@/routes/TestingStrategy/InspectGroupMaintenance/InspectGroupList',
        },
        {
          path: '/hwms/inspect-group-maintenance/dist/:id',
          component: '@/routes/TestingStrategy/InspectGroupMaintenance/InspectGroupDetail',
        },
      ],
    },
    // 检验策略-检验业务类型规则维护
    {
      path: '/hwms/inspect-business-type-rule',
      routes: [
        {
          path: '/hwms/inspect-business-type-rule/list',
          component: '@/routes/TestingStrategy/InspectBusTypeRule/InspectBusList',
        },
        {
          path: '/hwms/inspect-business-type-rule/dist/:id',
          component: '@/routes/TestingStrategy/InspectBusTypeRule/InspectBusDetail',
        },
      ],
    },

    // 检验执行-报检信息管理平台
    {
      path: '/hwms/inspection-info-management',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-info-management/list',
          component: '@/routes/InspectionExecution/InspectionInfoManagement/List',
          priority: 10,
        },
      ],
    },
    // 检验执行-SIP文件管理
    {
      path: '/hwms/sip-file-manage',
      routes: [
        {
          path: '/hwms/sip-file-manage/list/:sipCode?',
          component: '@/routes/InspectionExecution/SIPFileManagement/SIPFileList',
        },
      ],
    },
    // 检验执行-临时工艺变更单
    {
      path: '/hwms/temporary-process-change-orders',
      routes: [
        {
          path: '/hwms/temporary-process-change-orders/list',
          component: '@/routes/InspectionExecution/TemporaryProcessChangeOrders/list',
        },
        {
          path: '/hwms/temporary-process-change-orders/detail/:id',
          component: '@/routes/InspectionExecution/TemporaryProcessChangeOrders/details',
        },
        {
          path: '/pub/hwms/temporary-process-change-orders/detail/:id',
          component: '@/routes/InspectionExecution/TemporaryProcessChangeOrders/details',
        },
      ],
    },
    // 检验执行-初期管理活动平台
    {
      path: '/hwms/initial-management-activity-platform',
      priority: 10,
      routes: [
        {
          path: '/hwms/initial-management-activity-platform/list',
          component:
            '@/routes/InspectionExecution/InitialManagementActivityPlatform/InitialManagementActivityList',
          priority: 10,
        },
        {
          path: '/hwms/initial-management-activity-platform/detail/:id',
          component:
            '@/routes/InspectionExecution/InitialManagementActivityPlatform/InitialManagementActivityDetail',
          priority: 10,
        },
        {
          path: '/pub/hwms/initial-management-activity-platform/detail/:id',
          component:
            '@/routes/InspectionExecution/InitialManagementActivityPlatform/InitialManagementActivityDetail',
          priority: 10,
        },
        {
          path: '/himp/commentImport/:code',
          component: '@/components/CommentImport',
        },
      ],
    },
    // 检验执行-检验严格度管理
    {
      path: '/hmes/management-of-inspection-strictness-status',
      component: '@/routes/InspectionExecution/ManagementOfInspectionStrictnessStatus',
    },
    // 检验执行-检验单维护
    {
      path: '/hwms/inspect-doc-maintain',
      routes: [
        {
          path: '/hwms/inspect-doc-maintain/list/:timer?',
          component: '@/routes/InspectionExecution/InspectDocMaintain/InspectDocList',
        },
        {
          path: '/hwms/inspect-doc-maintain/dist/:id',
          component: '@/routes/InspectionExecution/InspectDocMaintain/InspectDocDetail',
        },
      ],
    },
    // 检验执行-检验平台仓库
    {
      path: '/hwms/inspection-platform',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-platform/list',
          priority: 10,
          component: '@/routes/InspectionExecution/InspectionPlatform/InspectionPlatformList',
        },
        {
          path: '/hwms/inspection-platform/dist/:id',
          priority: 10,
          component: '@/routes/InspectionExecution/InspectionPlatform/InspectionPlatformDetail',
        },
        {
          path: '/hwms/inspection-platform/inspect-doc/:id',
          priority: 10,
          component: '@/routes/InspectionExecution/InspectDocMaintain/InspectDocDetail',
        },
        {
          path: '/hwms/inspection-platform/:code',
          priority: 10,
          component:
            '@/routes/InspectionExecution/InspectionPlatform/InspectionPlatformList/CommentImport',
        },
      ],
    },
    // 检验执行-检验平台车间
    {
      path: '/hwms/inspection-platform-workshop',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-platform-workshop/list',
          priority: 10,
          component:
            '@/routes/InspectionExecution/InspectionPlatformWorkshop/InspectionPlatformList',
        },
        {
          path: '/hwms/inspection-platform-workshop/dist/:id',
          priority: 10,
          component:
            '@/routes/InspectionExecution/InspectionPlatformWorkshop/InspectionPlatformDetail',
        },
        {
          path: '/hwms/inspection-platform-workshop/inspect-doc/:id',
          priority: 10,
          component: '@/routes/InspectionExecution/InspectDocMaintain/InspectDocDetail',
        },
        {
          path: '/hwms/inspection-platform-workshop/:code',
          priority: 10,
          component:
            '@/routes/InspectionExecution/InspectionPlatformWorkshop/InspectionPlatformList/CommentImport',
        },
      ],
    },
    // 检验执行-不良记录单管理平台
    {
      path: '/hwms/ncReport-doc-maintain',
      routes: [
        {
          path: '/hwms/ncReport-doc-maintain/list',
          component: '@/routes/InspectionExecution/NcReportDocMaintain/NcReportList',
        },
        {
          path: '/hwms/ncReport-doc-maintain/dist/:id',
          component: '@/routes/InspectionExecution/NcReportDocMaintain/NcReportDetail',
        },
      ],
    },
    // 检验执行-检验追溯报表
    {
      path: '/hwms/inspect-trace-report',
      component: '@/routes/InspectionExecution/InspectTraceReport/InspectTraceList',
    },
    // 涂炭-正向追溯查询
    {
      path: '/hlct/carbon-forward-traceability-report',
      component: '@/routes/Report/CarbonForwardTraceabilityReport',
    },
    // 涂炭-逆向追溯报表
    {
      path: '/hlct/carbon-backward-traceability-report',
      component: '@/routes/Report/CarbonBackwardTraceabilityReport',
    },
    // 月度来料合格率报表
    {
      path: '/hlct/monthly-incoming-material-pass-rate-report',
      component: '@/routes/Report/MonthlyIncomingMaterialPassRateReport',
    },
    // 不合格品处置-不良评审单管理平台
    {
      path: '/hwms/ncReport-doc-maintain-new',
      routes: [
        {
          path: '/hwms/ncReport-doc-maintain-new/list',
          component: '@/routes/InspectionExecution/NcReviewDocMaintain/NcReviewList',
        },
        {
          path: '/hwms/ncReport-doc-maintain-new/dist/:id',
          component: '@/routes/InspectionExecution/NcReviewDocMaintain/NcReviewDetail',
        },
      ],
    },
    // 质量事件管理-质量事件查询
    {
      path: '/hmes/event/quality/event/query',
      component: '@/routes/QualityEvent/EventQuery',
    },
    // 质量检验数据查询报表
    {
      path: '/hlct/quality-inspection-data-query-report',
      component: '@/routes/Report/QualityInspectionDataQuery',
    },
    // 镀铜-正向追溯查询
    {
      path: '/hlct/copper-forward-traceability-report',
      component: '@/routes/Report/CopperForwardTraceabilityReport',
    },
    // 镀铜-逆向追溯报表
    {
      path: '/hlct/copper-backward-traceability-report',
      component: '@/routes/Report/CopperBackwardTraceabilityReport',
    },
    // 质量事件管理-质量事件请求类型维护
    {
      path: '/hmes/event/quality/event-request-type',
      component: '@/routes/QualityEvent/EventRequestTypeDemo',
    },
    // 质量事件管理-质量事件对象类型维护
    {
      path: '/hmes/event/quality/object-type',
      component: '@/routes/QualityEvent/ObjectTypeNew',
    },
    // 质量事件管理-质量事件类型维护
    {
      path: '/hmes/event/quality/event-type',
      component: '@/routes/QualityEvent/EventType',
    },
    // 计量器具管理-量具种类维护
    {
      path: '/hmes/measure-have/king-maintenance',
      routes: [
        {
          path: '/hmes/measure-have/king-maintenance/list',
          component: '@/routes/ManagementOfMeasuringInstruments/measureHave/kingMaintenance',
        },
        {
          path: '/hmes/measure-have/comment-import/:code',
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },
    // 计量器具管理-量具型号维护
    {
      path: '/hmes/measure-have/type-maintenance',
      routes: [
        {
          path: '/hmes/measure-have/type-maintenance/list',
          component: '@/routes/ManagementOfMeasuringInstruments/measureHave/typeMaintenance',
        },
        {
          path: '/hmes/measure-have/comment-import/:code',
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },
    // 计量器具管理-量具检定平台
    {
      path: '/hmes/measure-have/platform',
      routes: [
        {
          path: '/hmes/measure-have/platform/list/:tab?',
          component: '@/routes/ManagementOfMeasuringInstruments/MeasureHavePlatform/List',
        },
        {
          path: '/hmes/measure-have/platform/dist/:id',
          component: '@/routes/ManagementOfMeasuringInstruments/MeasureHavePlatform/Detail',
        },
        {
          path: '/hmes/measure-have/platform/inspect/:verificationMethod/:inspectDocId',
          component:
            '@/routes/ManagementOfMeasuringInstruments/MeasureHavePlatform/Detail/inspectDetail',
        },
      ],
    },
    // 计量器具管理-计量器具管理平台
    {
      path: '/hwms/meter-management-platform',
      routes: [
        {
          path: '/hwms/meter-management-platform/list',
          component:
            '@/routes/ManagementOfMeasuringInstruments/MeterManagementPlatform/MeterManagementList',
        },
      ],
    },
    // 计量器具管理-量具转移平台
    {
      path: '/hwms/measuring-transfer-platform',
      routes: [
        {
          path: '/hwms/measuring-transfer-platform/list',
          component: '@/routes/ManagementOfMeasuringInstruments/MeasuringTransferPlatform/list',
        },
        {
          path: '/hwms/measuring-transfer-platform/detail/:id?',
          component: '@/routes/ManagementOfMeasuringInstruments/MeasuringTransferPlatform/detail',
        },
      ],
    },
    // 计量器具管理-量具注销平台
    {
      path: '/hwms/measuring-scrap-platform',
      routes: [
        {
          path: '/hwms/measuring-scrap-platform/list',
          component: '@/routes/ManagementOfMeasuringInstruments/MeasuringScrapPlatform/list',
        },
        {
          path: '/hwms/measuring-scrap-platform/detail/:id',
          component: '@/routes/ManagementOfMeasuringInstruments/MeasuringScrapPlatform/detail',
        },
      ],
    },
    // 计量器具管理-量具型号与MSA分析质量特性关系维护
    {
      path: '/hwms/model-management/model-msa-relation',
      routes: [
        {
          path: '/hwms/model-management/model-msa-relation/list',
          component:
            '@/routes/ManagementOfMeasuringInstruments/ModelMsaRelation/ModelMsaRelationList',
        },
      ],
    },
    // 计量器具管理-MSA分析管理平台
    {
      path: '/hwms/msa-analysis-management-platform',
      routes: [
        {
          path: '/hwms/msa-analysis-management-platform/list',
          component: '@/routes/ManagementOfMeasuringInstruments/MsaAnalysisManagementPlatform/List',
        },
        {
          path: '/hwms/msa-analysis-management-platform/detail/:id',
          component:
            '@/routes/ManagementOfMeasuringInstruments/MsaAnalysisManagementPlatform/Detail',
        },
        {
          path: '/hwms/msa-analysis-management-platform/analysisDetail/:id',
          component:
            '@/routes/ManagementOfMeasuringInstruments/MsaAnalysisManagementPlatform/MsaAnalysisManagementPlatformAnalysisDetail',
        },
      ],
    },

    // 物料维护
    {
      path: '/hmes/product/material-manager',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/material-manager/list',
          component: '@/routes/product/Material/MaterialList',
          priority: 10,
        },
        {
          path: '/hmes/product/material-manager/dist/:id',
          component: '@/routes/product/Material/MaterialDetail',
          priority: 10,
        },
        {
          path: '/hmes/product/material-manager/site-assignment/:id',
          component: '@/routes/product/Material/MaterialSiteAssignment',
          priority: 10,
        },
      ],
    },

    // RoHS文件上传及预警管理
    {
      tilte: 'RoHS文件管理',
      path: '/qms/rohs-manage',
      component: '@/routes/RohsManage',
      authorized: true,
    },
  ],
  hash: true,
  hzeroMicro: {},
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
        },
      },
    ],
  ],
});
