/**
 * @Description: 一次解析责任判定-详情界面DS
 */
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hwms.LiabilityJudgment';


const detailDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'prianalResjudgId',
  fields: [
    {
      name: 'prianalResjudgCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prianalResjudgCode`).d('一次解析单编号'),
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'siteName',
      lovPara: { tenantId },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'QaNumObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.feedbackNum`).d('质量反馈单编号'),
      lovCode: 'YP.QIS.QA_FEEDBACK_LIST',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'feedbackNum',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => !record?.get('siteId'),
        required: ({ record }) => record?.get('siteId'),
      },
    },
    {
      name: 'feedbackId',
      bind: 'QaNumObj.feedbackId',
    },
    {
      name: 'feedbackNum',
      bind: 'QaNumObj.feedbackNum',
    },
    {
      name: 'partsCode',
    },
    {
      name: 'partsName',
    },
    {
      name: 'ypPartsId',
    },
    {
      name: 'ypPartsCode',
    },
    {
      name: 'ypPartsName',
    },
    {
      name: 'theme',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.theme`).d('主题'),
      required: true,
    },
    {
      name: 'batteryMatLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.batteryMatLotCode`).d('电池包编码'),
      lovCode: 'YP_MES.MES.MATERIAL_LOT',
      required: true,
      lovPara: { tenantId },
      textField: "materialLotCode",
      ignore: FieldIgnore.always,
    },
    {
      name: 'batteryMatLotId',
      bind: 'batteryMatLotLov.materialLotId',
    },
    {
      name: 'batteryMatLotCode',
      bind: 'batteryMatLotLov.materialLotCode',
    },
    {
      name: 'batteryMatLotModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryMatLotModel`).d('电池包型号'),
      bind: 'batteryMatLotLov.batteryMatLotModel',
      disabled: true,
    },
    {
      name: 'qualityProblemFlay',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityProblemFlay`).d('是否质量问题'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'analResDivision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analResDivision`).d('分析责任划分'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MARKET_PROBLEM_TYPE',
      required: true,
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
      required: true,
    },

    {
      name: 'accidentalTag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accidentalTag`).d('偶发/必现'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.ACCIDENTAL_TAG',
      required: true,
    },
    {
      name: 'frequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.frequency`).d('新发/再发'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_FREQUENCY',
      required: true,
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('责任部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      lovPara: { 
        tenantId,
        specDept: 'N',
      },
      required: true,
    },
    {
      name: 'responsibleDeptId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsibleDeptName',
      bind: 'departmentLov.unitName',
    },
    {
      name: 'responsibleUserObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.responsibleUserId`).d('责任人'),
      lovCode: 'HPFM.UNIT_LIMIT_EMPLOYEE',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
      dynamicProps: {
        required: ({ record }) => record.get('responsibleDeptId'),
        disabled: ({ record }) => !record.get('responsibleDeptId'),
        lovPara: ({record}) => {
          return {
            tenantId,
            unitId: record?.get('responsibleDeptId'),
          };
        },
      },
    },
    {
      name: 'responsibleEmId',
      bind: 'responsibleUserObj.id',
    },
    {
      name: 'responsibleEmName',
      bind: 'responsibleUserObj.realName',
    },
    {
      name: 'reasonClassify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonClassify`).d('原因分类'),
      lookupCode: 'YP.QIS.REASON',
    },
    {
      name: 'reasonObj',
      type: FieldType.object,
      lovCode: 'YP.QIS.SALES_REASON_LIST',
      label: intl.get(`${modelPrompt}.reasonCode`).d('原因编码'),
      dynamicProps: {
        required: ({ record }) => record.get('reasonClassify'),
        disabled: ({ record }) => !record.get('reasonClassify'),
        lovPara: ({ record }) => {
          return {
            reasonClassify: record.get('reasonClassify'),
            tenantId,
          }
        },
      },
    },
    {
      name: 'reasonId',
      bind: 'reasonObj.reasonId',
    },
    {
      name: 'reasonCode',
      bind: 'reasonObj.reasonCode',
      label: intl.get(`${modelPrompt}.reasonCode`).d('原因编码'),
    },
    {
      name: 'reason',
      bind: 'reasonObj.reason',
      disabled: true,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
    },
    {
      name: 'bResponsibilityRatio',
      bind: 'reasonObj.bResponsibilityRatio',
      disabled: true,
      label: intl.get(`${modelPrompt}.bResponsibilityRatio`).d('乙方责任'),
    },
    {
      name: 'aResponsibilityRatio',
      bind: 'reasonObj.aResponsibilityRatio',
      disabled: true,
      label: intl.get(`${modelPrompt}.aResponsibilityRatio`).d('甲方责任'),
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      type: FieldType.dateTime,
      disabled: true,
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      disabled: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prianal-resjudg/detail/ui`,
        method: 'GET',
      };
    },
  },
});

const taskDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  primaryKey: 'prianalResjudgItemNum',
  fields: [
    {
      name: 'prianalResjudgItemNum',
      label: intl.get(`${modelPrompt}.prianalResjudgItemNum`).d('序号'),
      type: FieldType.number,
      dynamicProps: {
        defaultValue: ({ dataSet }) => {
          let maxNum = 0;
          dataSet.forEach(_record => {
            if (_record?.get('prianalResjudgItemNum') > maxNum) {
              maxNum = _record?.get('prianalResjudgItemNum');
            }
          });
          return maxNum + 10;
        },
      },
    },
    {
      name: 'partsCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partsCode`).d('客户零件号'),
      required: true,
    },
    {
      name: 'partsName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partsName`).d('客户零件名称'),
      required: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: { tenantId },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'ypPartsCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'ypPartsName',
      label: intl.get(`${modelPrompt}.materialLov`).d('物料名称'),
      bind: 'materialLov.materialName',
    },
    {
      name: 'ypPartsId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'primaryUnitFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUnitFlag`).d('主因件'),
      lookupCode: 'YP.QIS.YN_FLAG',
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
    // 上面的
    {
      name: 'majorFaultMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorFaultMode`).d('主故障模式'),
      lookupCode: 'YP.QIS.MAJOR_FAULT_MODE',
      required: true,
    },
    {
      name: 'majorDivision1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision1`).d('主要区分1'),
      lookupCode: 'YP.QIS.MAJOR_DIVISION1',
      required: true,
    },
    {
      name: 'majorDivision2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision2`).d('主要区分2'),
      lookupCode: 'YP.QIS.MAJOR_DIVISION2',
      required: true,
    },
    {
      name: 'majorDivision3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision3`).d('主要区分3'),
      lookupCode: 'YP.QIS.MAJOR_DIVISION3',
      required: true,
    },
    {
      name: 'description',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.description`).d('具体描述'),
    },
    {
      name: 'ware',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ware`).d('软件/硬件'),
      lookupCode: 'YP.QIS.WARE',
      required: true,
    },
    {
      name: 'softwareVersion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.softwareVersion`).d('软件版本'),
      dynamicProps: {
        required: ({ record }) => record.get('ware') === 'SOFTWARE',
        disabled: ({ record }) => record.get('ware') !== 'SOFTWARE',
      },
    },
    {
      name: 'problemCreateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCreateFlag`).d('是否触发问题管理'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      disabled: true,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题管理编号'),
    },
  ],
});


export { detailDS, taskDS };
