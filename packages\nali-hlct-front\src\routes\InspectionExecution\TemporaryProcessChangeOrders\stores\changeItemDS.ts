/*
 * @Description: 工程暂允-修改信息项DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-26 13:43:40
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-26 14:43:44
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`;
const tenantId = getCurrentOrganizationId();

const changeItemDS: () => DataSetProps = () => ({
  paging: false,
  autoQuery: false,
  autoCreate: false,
  selection: false,
  primaryKey: 'itemId',
  fields: [
    {
      name: 'changeInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.changeInfo`).d('修改前/后'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessTypeDesc`).d('检验业务类型'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工序'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备'),
    },
    {
      name: 'inspectItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemCode`).d('检验项编码'),
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemDesc`).d('检验项描述'),
    },
    {
      name: 'trueValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValueList`).d('符合值'),
    },
    {
      name: 'inspectFrequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectFrequency`).d('检测频率'),
      lookupCode: 'MT.QMS.INSPECT_FREQUENCY',
      lovPara: { tenantId },
    },
    {
      name: 'm',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.m`).d('频率参数M'),
    },
    {
      name: 'n',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.n`).d('频率参数N'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/change-item/ui`,
        method: 'GET',
      };
    },
  },
});

export { changeItemDS };
