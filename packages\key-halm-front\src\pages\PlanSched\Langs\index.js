import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLangs = key => {
  const PREFIX = 'aori.planSched';
  const MODELPREFIX = `${PREFIX}.model.planSched`;
  const LANGS = {
    ...getCommonLangs(),
    // message
    HEADER: intl.get(`${PREFIX}.view.message.title`).d('计划预测'),
    DEL_CONFIRM: intl.get(`${PREFIX}.view.message.delConfirm`).d('计划预测删除确认'),
    DEL_BATCH_TEXT: intl
      .get(`${PREFIX}.view.message.delBatchText`)
      .d('删除计划预测行，对应的维保预测记录也会删除，确认删除？'),
    PLAN_DEL_CONFIRM: intl.get(`${PREFIX}.view.message.planDelConfirm`).d('维保计划行移除确认'),
    PLAN_DEL_TEXT: intl.get(`${PREFIX}.view.message.planDelText`).d('确认移除所选维保计划行？'),
    KEYWORD_PLACEHOLDER: intl.get(`${PREFIX}.view.message.keywordPlaceholder`).d('请搜索关键字'),
    RELEASE: intl.get(`${PREFIX}.view.message.button.release`).d('下达'),
    FORECAST: intl.get(`${PREFIX}.view.message.button.forecast`).d('预测'),
    LOG: intl.get(`${PREFIX}.view.message.button.log`).d('日志'),
    SKIP: intl.get(`${PREFIX}.view.message.button.skip`).d('跳过'),
    RESULT_DEL_CONFIRM: intl.get(`${PREFIX}.view.message.resultDelConfirm`).d('删除确认'),
    RESULT_DEL_TEXT: intl.get(`${PREFIX}.view.message.resultDelText`).d('确认删除当前预测结果值？'),
    RESULT_DEL_TEXT_LIST: intl.get(`${PREFIX}.view.message.resultDelTextList`).d('确认删除所选择的预测结果值？'),
    RELEASE_TEXT: intl
      .get(`${PREFIX}.view.message.releaseText`)
      .d('存在行未保存，是否取消编辑并下达？'),
    SKIP_TEXT: intl.get(`${PREFIX}.view.message.skipText`).d('存在行未保存，是否取消编辑并跳过？'),
    SKIP_FIRST_TEXT: intl
      .get(`${PREFIX}.view.message.skipFirstText`)
      .d('确认跳过当前勾选的预测结果行？'),
    FORECAST_TEXT: intl
      .get(`${PREFIX}.view.message.forecastText`)
      .d('系统将按计划结果的提醒日期/提醒仪表读数进行下达'),
    TITLE_LOG: intl.get(`${PREFIX}.view.message.title.log`).d('日志'),
    CHANGE_RESULT_PAGE: intl
      .get(`${PREFIX}.view.message.changeResultPage`)
      .d('存在行未保存，放弃修改并翻页？'),
    FORECAST_ERROR: intl
      .get(`${PREFIX}.view.message.forecastError`)
      .d('部分维保计划预测报错，具体请查看日志'),
    TITLE_LOG_DETAIL: intl.get(`${PREFIX}.view.message.title.logDetail`).d('日志详情'),
    DAY: intl.get(`${PREFIX}.view.message.day`).d('天'),
    CATCH_TIME_INFO: intl
      .get(`${PREFIX}.view.message.catchTime`)
      .d('表示当前仪表的维保向后预测读数触发的次数。'),
    CYCLE_FLAG_INFO: intl
      .get(`${PREFIX}.view.message.cycleFlag`)
      .d('启用后系统自动循环执行当前计划预测进行预测。'),
    SCHED_INTERVAL_INFO: intl
      .get(`${PREFIX}.view.message.schedInterval`)
      .d('表示每次系统自动运行当前计划预测的时间间隔。'),
    LIMIT_999: intl.get(`${PREFIX}.view.message.limit999`).d('允许输入范围1~999'),
    RELEASE_ERROR: intl.get(`${PREFIX}.view.message.releaseError`).d('下达失败，详情查看日志。'),
    // model
    ASSET: intl.get(`${MODELPREFIX}.asset`).d('设备'),
    LOCATION: intl.get(`${MODELPREFIX}.location`).d('位置'),
    SCHED_NAME: intl.get(`${MODELPREFIX}.schedName`).d('预测名称'),
    SCHED_CODE: intl.get(`${MODELPREFIX}.schedCode`).d('预测编码'),
    MAINTAIN_PLAN: intl.get(`${MODELPREFIX}.maintainPlan`).d('维保计划'),
    CYCLE_FLAG: intl.get(`${MODELPREFIX}.cycleFlag`).d('周期性'),
    SCHED_START_DATE: intl.get(`${MODELPREFIX}.schedStartDate`).d('预测开始日期'),
    SCHED_END_DATE: intl.get(`${MODELPREFIX}.schedEndDate`).d('预测结束日期'),
    PLAN_DURING_DAY: intl.get(`${MODELPREFIX}.planDuringDay`).d('计划区间(天)'),
    LAST_PLAN_TIME: intl.get(`${MODELPREFIX}.lastPlanTime`).d('最近预测时间'),
    MAINTSITE: intl.get(`${MODELPREFIX}.maintSite`).d('服务区域'),
    SCHED_TYPE: intl.get(`${MODELPREFIX}.schedTypeCode`).d('预测类型'),
    SCHED_INTERVAL: intl.get(`${MODELPREFIX}.schedInterval`).d('预测间隔'),
    PLAN_NAME: intl.get(`${MODELPREFIX}.maintainPlanName`).d('维保计划名称'),
    PLAN_CODE: intl.get(`${MODELPREFIX}.maintainPlanCode`).d('维保计划编号'),
    CYCLE_INTERVAL: intl.get(`${MODELPREFIX}.cycleInterval`).d('周期间隔'),
    CYCLE_INTERVAL_OF_METER: intl.get(`${MODELPREFIX}.cycleIntervalOfMeter`).d('维保间隔'),
    CYCLE_UOM: intl.get(`${MODELPREFIX}.cycleUom`).d('周期单位'),
    LAST_WORK_TIME: intl.get(`${MODELPREFIX}.lastWorkTime`).d('最近作业时间'),
    PLAN_STATUS: intl.get(`${MODELPREFIX}.planStatus`).d('计划状态'),
    PLAN_START_TIME: intl.get(`${MODELPREFIX}.planStartTime`).d('计划开始时间'),
    STANDARD_TIME: intl.get(`${MODELPREFIX}.standardTime`).d('标准时间'),
    WO_NUM: intl.get(`${MODELPREFIX}.woNum`).d('工单编号'),
    WO_TYPE: intl.get(`${MODELPREFIX}.woType`).d('工单类型'),
    REMINDER_TIME: intl.get(`${MODELPREFIX}.reminderTime`).d('提醒时间'),
    FORECAST_TIME: intl.get(`${MODELPREFIX}.forecastTime`).d('预测时间'),
    PLAN_STOP_TIME: intl.get(`${MODELPREFIX}.planStopTime`).d('计划完成时间'),
    EDIT_FLAG: intl.get(`${MODELPREFIX}.editFlag`).d('已编辑'),
    CREATE_TIME: intl.get(`${MODELPREFIX}.createTime`).d('时间'),
    LOG_INFO: intl.get(`${MODELPREFIX}.logInfo`).d('详细信息'),
    CYCLE_TYPE: intl.get(`${MODELPREFIX}.cycleTypeCode`).d('预测维保方式'),
    CATCH_TIME: intl.get(`${MODELPREFIX}.catchTime`).d('触发次数'),
    METER: intl.get(`${MODELPREFIX}.meter`).d('仪表点'),
    METER_UOM: intl.get(`${MODELPREFIX}.meterUom`).d('仪表单位'),
    METER_CURR_VAL: intl.get(`${MODELPREFIX}.currentValue`).d('计划仪表读数'),
    METER_STANDARD_VAL: intl.get(`${MODELPREFIX}.standardValue`).d('标准仪表读数'),
    METER_REMIND_VAL: intl.get(`${MODELPREFIX}.reminderValue`).d('提醒仪表读数'),
    // tabs panels
    PANEL_SCOPE: intl.get(`${PREFIX}.view.message.panel.scope`).d('预测范围'),
    TAB_PLAN: intl.get(`${PREFIX}.view.message.tab.plan`).d('维保计划'),
    TAB_RESULT: intl.get(`${PREFIX}.view.message.tab.result`).d('预测结果'),
  };
  return LANGS[key];
};

export default getLangs;
