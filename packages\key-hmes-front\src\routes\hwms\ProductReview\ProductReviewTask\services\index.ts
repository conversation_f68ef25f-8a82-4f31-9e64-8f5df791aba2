/*
 * @Description: 产品审核任务-services
 * @Author: <<EMAIL>>
 * @Date: 2023-10-10 15:06:05
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-13 19:56:17
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 取消/领取产品审核任务
 * @function CancelProductRevTask
 * @returns {object} fetch Promise
 */
export function CancelOrPickTask(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/update-status/ui`,
    method: 'POST',
  };
}

/**
 * 发布产品审核任务
 * @function PublishProductRevTask
 * @returns {object} fetch Promise
 */
export function PublishProductRevTask(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/a-submit/ui`,
    method: 'POST',
  };
}

/**
 * bWrite提交
 * @function SubmitBWriteInfo
 * @returns {object} fetch Promise
 */
export function SubmitBWriteInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/b-submit/ui`,
    method: 'POST',
  };
}

/**
 * cWrite提交
 * @function SubmitCWriteInfo
 * @returns {object} fetch Promise
 */
export function SubmitCWriteInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/c-submit/ui`,
    method: 'POST',
  };
}

/**
 * 审批驳回
 * @function RejectProductRevTask
 * @returns {object} fetch Promise
 */
export function RejectProductRevTask(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/reject/ui`,
    method: 'POST',
  };
}

/**
 * 保存产品审核任务
 * @function SaveProductRevTask
 * @returns {object} fetch Promise
 */
export function SaveProductRevTask(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/save/ui`,
    method: 'POST',
  };
}

/**
 * 保存产品审核项目
 * @function SaveProductItem
 * @returns {object} fetch Promise
 */
export function SaveProductItem(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-review-item/save/ui`,
    method: 'POST',
  };
}

/**
 * 查询条码信息
 * @function QueryBarcodeInfo
 * @returns {object} fetch Promise
 */
export function QueryBarcodeInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/scan-barcode`,
    method: 'GET',
  };
}

/**
 * 保存样本信息
 * @function SaveProductSample
 * @returns {object} fetch Promise
 */
export function SaveProductSample(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/sample-save/ui`,
    method: 'POST',
  };
}

/**
 * 保存任务的条码审核结果
 * @function SaveProductDtlSample
 * @returns {object} fetch Promise
 */
export function SaveProductDtlSample(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/dtl-sample-save/ui`,
    method: 'POST',
  };
}

/**
 * 查询产品审核方案的详情
 * @function QueryProductRevSchemeInfo
 * @returns {object} fetch Promise
 */
export function QueryProductRevSchemeInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-scheme/info/ui`,
    method: 'GET',
  };
}
