import React, { useState, useMemo } from 'react';
import {
  DataSet,
  Table,
  Button,
  Row,
  Col,
  TextField,
  Form,
  Icon,
  Select,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';
import ExcelExport from 'components/ExcelExport';
import request from 'utils/request';
import { BASIC } from '@utils/config';

import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { tableDS  } from './stores';
import { Host, API_HOST } from '@/utils/config';
import LovModal from "./LovModal";
import InputLovDS from '../../stores/InputLovDS';

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.rawMaterialBarcodeTraceabilityReport';

const RawMaterialBarcodeTraceabilityReport = observer(() => {
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  const columns = [
    {
      name: 'parentIdentification',
      width: 200,
      lock: 'left',
    },
    {
      name: 'parentMaterialCode',
      width: 150,
    },
    {
      name: 'parentMaterialName',
      width: 150,
    },
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'materialLotIdentification',
      width: 150,
    },
    {
      name: 'materialLotSupplier',
      width: 150,
    },
    {
      name: 'assembleQty',
      width: 150,
    },
    {
      name: 'operationName',
      width: 150,
    },
    {
      name: 'description',
      width: 150,
    },
    {
      name: 'workcellCode',
      width: 150,
    },
    {
      name: 'workcellName',
      width: 150,
    },
    {
      name: 'loginName',
    },
    {
      name: 'creationDate',
    },
  ];
  const toggleForm = () => {
    setExpandForm(!expandForm);
  }

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identifications', '原材料条码', queryDataSet)}
                    />
                  </div>
                }
              />
              <TextField
                name="parentIdentifications"
                onChange={handleBarCode}
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'parentIdentifications', '产品条码', queryDataSet)}
                    />
                  </div>
                }
              />
              <Select name="parentTraceLevels" />
              {expandForm && (
                <>
                  <TextField name="materialLotIdentification" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button funcType="link" icon={
                expandForm? 'expand_less':'expand_more'
              } onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  queryDataSet.getField('parentTraceLevels')?.set('disabled', false);
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  }
  const handleSearch = async () => {
    const {parentTraceLevels, parentIdentifications, identifications, materialLotIdentification } = tableDs?.queryDataSet?.toJSONData()[0]
    if(!parentIdentifications&&!identifications&&!parentTraceLevels.length&&!materialLotIdentification){
      notification.error({
        message: intl.get(`${modelPrompt}.queryField`).d('请输入查询条件'),
      });
      return
    }
    if(parentIdentifications&&!parentTraceLevels.length){
      notification.error({
        message: '产品类型为空，请检查!',
      });
      return
    }
    tableDs.query()
  }
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      if (inputLovFlag !== 'parentIdentifications') {
        handleSearch();
      } else {
        handleFill();
      }
    }
  }
  const handleBarCode = (e) => {
    if (!e) {
      tableDs?.queryDataSet.current.set('parentTraceLevels', '');
      tableDs?.queryDataSet.getField('parentTraceLevels')?.set('disabled', false);
    }
  }
  const handleFill = () => {
    const { parentIdentifications } = tableDs?.queryDataSet?.toJSONData()[0];
    if (parentIdentifications) {
      return request(
        `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-trace-rels/identification/query`,
        {
          method: 'GET',
          params: { identifications: parentIdentifications ? parentIdentifications.split(',') : [] },
        },
      ).then(res => {
        if (res?.success) {
          if (res.rows) {
            tableDs?.queryDataSet.current.set('parentTraceLevels', [res.rows]);
            tableDs?.queryDataSet.getField('parentTraceLevels')?.set('disabled', true);
            handleSearch();
          } else{
            tableDs?.queryDataSet.current.set('parentTraceLevels', '');
            tableDs?.queryDataSet.getField('parentTraceLevels')?.set('disabled', true);
            notification.error({
              message: '当前条码无对应的产品类型，请检查!',
            });
          }
        } else {
          tableDs?.queryDataSet.current.set('parentTraceLevels', '');
          tableDs?.queryDataSet.getField('parentTraceLevels')?.set('disabled', true);
          notification.error({
            message: res?.message,
          });
        }
      });
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };
  const getExportQueryParams = () => {
    return {
      ...tableDs.queryDataSet.toJSONData()[0],
      traceRelIds: tableDs.selected.map(item => item.get('traceRelId')),
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('原材料条码追溯报表')}>
        <ExcelExport
          method="GET"
          exportAsync={false}
          otherButtonProps={{disabled:
            (!tableDs?.queryDataSet?.toJSONData()[0]?.parentIdentifications
            &&!tableDs?.queryDataSet?.toJSONData()[0]?.materialLotIdentification
            &&!tableDs?.queryDataSet?.toJSONData()[0]?.identifications)
          &&!tableDs?.queryDataSet?.toJSONData()[0]?.parentTraceLevels.length}}
          queryParams={getExportQueryParams}
          requestUrl={`${API_HOST}${Host}/v1/${tenantId}/hme-trace-rels/export/ui`}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="ScrapBarcodeGeneration"
          customizedCode="ScrapBarcodeGeneration"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.rawMaterialBarcodeTraceabilityReport', 'tarzan.common'],
})(RawMaterialBarcodeTraceabilityReport);
