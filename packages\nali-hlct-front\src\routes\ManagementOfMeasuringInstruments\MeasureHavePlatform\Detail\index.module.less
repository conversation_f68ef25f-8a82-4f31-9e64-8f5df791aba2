.flow-content {
  float: right;
  font-size: 12px;
  margin-bottom: 10px;

  span {
    margin-left: 3px;
  }
}
.auditRecord {
  width: 100%;
  display: flex;
  .audit-record-list {
    overflow: hidden;
    width: 200px;
    flex: 0 0 auto;
    border-right: 1px solid #e8e8e8;
    .c7n-menu-inline,
    .c7n-menu-vertical,
    .c7n-menu-vertical-left {
      border-right: none;
    }
  }
  .audit-record-detial {
    position: relative;
    width: calc(100% - 200px);
    flex: 1 0 auto;
  }
  .audit-record-list {
    .record-list-title {
      font-size: 1.2em;
      color: #595959;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      span {
        flex-grow: 1;
      }
    }
    .record-list-item {
      padding-left: 10px;
      .record-list-item-text {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .record-list-item-info{
        display: inline-flex;

        div{
          flex: 1;
        }
        .left{
          margin-right: 5px;
        }
      }
    }
    .record-list-add {
      padding-left: 10px;
      // text-align: center;
    }
  }

  :global {
    .c7n-menu.c7n-menu.c7n-menu.c7n-menu.c7n-menu-vertical .c7n-menu-item, .c7n-menu.c7n-menu.c7n-menu.c7n-menu.c7n-menu-vertical-left .c7n-menu-item, .c7n-menu.c7n-menu.c7n-menu.c7n-menu.c7n-menu-vertical-right .c7n-menu-item, .c7n-menu.c7n-menu.c7n-menu.c7n-menu.c7n-menu-inline .c7n-menu-item:not(:first-child) {
      height: 56px!important;
    }
  }
}

.table-cell {
  height: 1.5em;
  text-align: center;
}

