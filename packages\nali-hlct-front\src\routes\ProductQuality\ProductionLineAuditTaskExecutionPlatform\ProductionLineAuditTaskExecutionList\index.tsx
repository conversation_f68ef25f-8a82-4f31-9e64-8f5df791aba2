import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentUserId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ListTableDS } from '../stories';

const modelPrompt = 'tarzan.prodlineReview.prodlineReviewTask';

const ProductionLineAuditTaskExecutionList = props => {
  const { tableDS } = props;

  useEffect(() => {
    tableDS.setQueryParameter('responsibleEmId', getCurrentUserId());
    if (tableDS?.currentPage) {
      tableDS.query(props.tableDS.currentPage);
    } else {
      tableDS.query();
    }
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'siteName',
      align: ColumnAlign.center,
    },
    {
      name: 'prodlineRevplanCode',
      align: ColumnAlign.center,
    },
    {
      name: 'lineRevplanTaskCode',
      align: ColumnAlign.center,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(
                `/hwms/production_line_audit_task_execution_platform/dist/${record?.get(
                  'lineRevplanTaskId',
                )}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'statusMeaning',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewTypeMeaning',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewStageMeaning',
      align: ColumnAlign.center,
    },
    {
      name: 'projectName',
      align: ColumnAlign.center,
    },
    {
      name: 'prodLineNameList',
      align: ColumnAlign.center,
      width: 180,
      renderer: ({ value }) => {
        if (!value?.length) {
          return;
        }
        return value.map((item) => <Tag>{item}</Tag>);
      },
    },
    {
      name: 'responsibleDeptName',
      align: ColumnAlign.center,
    },
    {
      name: 'responsibleEmName',
      align: ColumnAlign.center,
    },
    {
      name: 'scheFinishTime',
      align: ColumnAlign.center,
    },
    {
      name: 'createdByName',
      align: ColumnAlign.center,
    },
    {
      name: 'creationDate',
      align: ColumnAlign.center,
    },
  ];

  return (
    <div className="hmes-style" style={{ height: '100%' }}>
      <Header title={intl.get(`${modelPrompt}.title.list`).d('产线审核任务执行平台')} />
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDS}
          columns={columns}
          searchCode="ProductionLineAuditTaskExecutionPlatform"
          customizedCode="ProductionLineAuditTaskExecutionPlatform"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [ modelPrompt, 'tarzan.common' ],
})(
  withProps(
    () => {
      const tableDS = new DataSet({
        ...ListTableDS(),
      });
      return {
        tableDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ProductionLineAuditTaskExecutionList),
);
