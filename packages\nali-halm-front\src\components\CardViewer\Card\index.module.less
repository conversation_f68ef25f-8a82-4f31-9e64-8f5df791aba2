.card-basic {
  display: flex;
  flex-direction: column;
  border: 1px solid #cbcbcb;
  border-radius: 8px;
  box-shadow: 2px 2px 5px #cbcbcb;
  margin: 8px 0;
  &.card-select {
    border: 1px solid #3889ff;
    box-shadow: 2px 2px 5px #b5cbed;
  }
  .card-header {
    padding: 0 16px;
    .card-header-right {
      height: 17px;
      .card-check {
        width: 25px;
        height: 17px;
        position: absolute;
        right: 7px;
        top: 9px;
      }
    }
    .card-header-description {
      width: 100%;
      .card-header-title {
        word-break: keep-all;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        span {
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          color: #5a6677;
        }
      }
      .card-status-tag {
        min-height: 30px;
      }
      :global {
        .c7n-tag {
          margin: 8px 8px 8px 0;
          &:hover {
            cursor: context-menu;
          }
        }
      }
    }
  }
  .card-body {
    padding: 0 16px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 8px;
    .card-row {
      width: 100%;
      .card-field {
        display: inline-flex;
        width: 100%;
        margin: 8px 0;
        .field-label {
          font-size: 12px;
          margin-right: 8px;
          color: #5a6677;
          line-height: 17px;
          word-break: keep-all;
          white-space: nowrap;
          &::after {
            content: ':';
            margin-left: 2px;
            color: #5a6677;
          }
        }
        .field-value {
          font-size: 12px;
          color: #151515;
          line-height: 17px;
          font-weight: 700;
          word-break: keep-all;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
  .card-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 16px;
    border-top: 1px solid #f0f0f0;
    [class*='footer-'] {
      display: inline-flex;
      justify-content: center;
      width: 50%;
      padding: 16px 0;
      line-height: 17px;
      font-size: 12px;
      cursor: pointer;
    }
    img {
      width: 14px;
      height: 14px;
      margin: 0 8px;
    }
    :global {
      .c7n-divider {
        height: 30px;
      }
    }
  }
}
