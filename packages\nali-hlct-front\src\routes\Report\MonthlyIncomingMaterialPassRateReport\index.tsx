/**
 * @Description: 月度来料合格率报表
 */

import React, { useMemo } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
// import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
// import { BASIC } from '@utils/config';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import {
  monthlyIncomingMaterialPassRateReportTableDS,
} from './stories';

const modelPrompt = 'tarzan.hmes.MonthlyIncomingMaterialPassRateReport';
// const tenantId = getCurrentOrganizationId();

const MonthlyIncomingMaterialPassRateReport = observer(props => {
  const {
    monthlyIncomingMaterialPassRateReportTableDs,
  } = props;

  const columnsOutput: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'supplierName',
        width: 150,
      },
      {
        name: 'inspectCount',
        // width: 150,
      },
      {
        name: 'inspectSumQty',
        // width: 150,
      },
      {
        name: 'checkoutCount',
        // width: 150,
      },
      {
        name: 'checkoutSumQty',
        // width: 150,
      },
      {
        name: 'initialCount',
        // width: 150,
      },
      {
        name: 'initialYield',
        // width: 150,
      },
      {
        name: 'ultimateCount',
        // width: 150,
      },
      {
        name: 'ultimateYield',
        // width: 150,
      },
    ];
  }, []);

  // 分组依据：按月份时间分组
  const groups: any[]  = useMemo(() => ([
    {
      name: 'formattedDate',
      type: 'column',
      columnProps: {
        width: 120,
      },

    },
  ]), []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('月度来料合格率报表')} />
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            queryFieldsLimit: 7,
          }}
          border
          rowHeight={34}
          dataSet={monthlyIncomingMaterialPassRateReportTableDs}
          columns={columnsOutput}
          groups={groups}
          searchCode="MONTHLYINCOMINGMATERIALPASSRATEREPORTTABLE"
          customizedCode="MONTHLYINCOMINGMATERIALPASSRATEREPORTTABLE"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.MonthlyIncomingMaterialPassRateReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const monthlyIncomingMaterialPassRateReportTableDs = new DataSet({
        ...monthlyIncomingMaterialPassRateReportTableDS(),
      });

      return {
        monthlyIncomingMaterialPassRateReportTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MonthlyIncomingMaterialPassRateReport),
);
