/**
 * @Description: 检验平台-查询界面
 * @Author: <<EMAIL>>
 * @Date: 2023-02-10 17:18:09
 * @LastEditTime: 2023-05-18 17:03:16
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState, useMemo } from 'react';
import moment from 'moment';
import { Table, DataSet, Modal, Spin, Lov, Form,Button } from 'choerodon-ui/pro';
import { Badge, Tabs, Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { openTab } from 'utils/menuTab';
import queryString from 'query-string';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import ExcelExport from 'components/ExcelExport';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import withProps from 'utils/withProps';
import { useDataSetEvent } from 'utils/hooks';
import { isNil } from 'lodash';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC, API_HOST } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { TableDS, InspectMaterialDS, DistributeDS } from '../stories';
import { AssignTask, RevokeTask, CancelTask, BatchQualified } from '../services';
import styles from './index.modules.less';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.qms.inspectionPlatform';
const { TabPane } = Tabs;

const InspectionPlatformList = props => {
  const {
    location: { state },
    searchTableDS,
    pendingTableDS,
    receivedTableDS,
    unclaimedTableDS,
    approvealTableDS,
    allTableDS,
    history,
    customizeTable,
    customizeTabPane,
  } = props;

  enum TabKey {
    processTask = 'PENDING',
    receiveTask = 'UNCLAIMED',
    approvealTask = 'APPROVEAL',
    completeTask = 'RECEIVED',
    allTask = 'ALL',
  }

  // 检验条码明细
  const inspectMaterialDS = useMemo(() => new DataSet({ ...InspectMaterialDS() }), []);
  // 分配检验员
  const distributeDS = useMemo(() => new DataSet({ ...DistributeDS() }), []);
  // tab键
  const [tabKey, setTabKey] = useState('processTask');
  // 待处理任务列表勾选
  const [pendingSelectList, setPendingSelectList] = useState([]);
  // 待领取任务列表勾选
  const [unclaimedSelectList, setUnclaimedSelectList] = useState([]);
  // 待审核任务列表勾选
  const [approvealSelectList, setApprovealSelectList] = useState([]);
  // 全部任务列表勾选
  const [allSelectList, setAllSelectList] = useState([]);
  // 待处理总数量
  const [pendingCount, setPendingCount] = useState(0);
  // 待领取总数量
  const [unclaimedCount, setUnclaimedCount] = useState(0);

  // 监听关系表格勾选数据
  const handlePendingTableSelect = ({ dataSet }) => {
    setPendingSelectList(dataSet.selected || []);
  };
  useDataSetEvent(pendingTableDS, 'select', handlePendingTableSelect);
  useDataSetEvent(pendingTableDS, 'selectAll', handlePendingTableSelect);
  useDataSetEvent(pendingTableDS, 'unselect', handlePendingTableSelect);
  useDataSetEvent(pendingTableDS, 'unselectAll', handlePendingTableSelect);

  const handleUnclaimedTableSelect = ({ dataSet }) => {
    setUnclaimedSelectList(dataSet.selected || []);
  };
  useDataSetEvent(unclaimedTableDS, 'select', handleUnclaimedTableSelect);
  useDataSetEvent(unclaimedTableDS, 'selectAll', handleUnclaimedTableSelect);
  useDataSetEvent(unclaimedTableDS, 'unselect', handleUnclaimedTableSelect);
  useDataSetEvent(unclaimedTableDS, 'unselectAll', handleUnclaimedTableSelect);

  const handleApprovealTableSelect = ({ dataSet }) => {
    setApprovealSelectList(dataSet.selected || []);
  };
  useDataSetEvent(approvealTableDS, 'select', handleApprovealTableSelect);
  useDataSetEvent(approvealTableDS, 'selectAll', handleApprovealTableSelect);
  useDataSetEvent(approvealTableDS, 'unselect', handleApprovealTableSelect);
  useDataSetEvent(approvealTableDS, 'unselectAll', handleApprovealTableSelect);

  const handleAllTableSelect = ({ dataSet }) => {
    setAllSelectList(dataSet.selected || []);
  };
  useDataSetEvent(allTableDS, 'select', handleAllTableSelect);
  useDataSetEvent(allTableDS, 'selectAll', handleAllTableSelect);
  useDataSetEvent(allTableDS, 'unselect', handleAllTableSelect);
  useDataSetEvent(allTableDS, 'unselectAll', handleAllTableSelect);

  // 监听查询
  const handleSearchTableQuery = async ({ dataSet }) => {
    await handleChangeActiveKey(dataSet.getQueryParameter('tableKey'));
    return false;
  };
  useDataSetEvent(searchTableDS, 'query', handleSearchTableQuery);

  useDataSetEvent(searchTableDS.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteObj':
        record.set('inspectBusinessTypeObj', null);
        break;
      default:
        break;
    }
  });

  // 领取检验任务
  const { run: receiveTask, loading: receiveTaskLoading } = useRequest(AssignTask(), {
    manual: true,
    needPromise: true,
  });
  // 撤销检验任务
  const { run: revokeTask, loading: revokeTaskLoading } = useRequest(RevokeTask(), {
    manual: true,
    needPromise: true,
  });
  // 取消检验任务
  const { run: cancelTask, loading: cancelTaskLoading } = useRequest(CancelTask(), {
    manual: true,
    needPromise: true,
  });
  // 分配检验任务
  const { run: distributeTask, loading: distributeTaskLoading } = useRequest(AssignTask(), {
    manual: true,
    needPromise: true,
  });
  // 批量合格
  const { run: batchQualified, loading: batchQualifiedLoading } = useRequest(BatchQualified(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    pendingTableDS.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
    );
    pendingTableDS.setQueryParameter('tableKey', TabKey[tabKey]);
    searchTableDS.setQueryParameter('tableKey', tabKey);

    if (pendingTableDS?.currentPage) {
      pendingTableDS.query(props.receivedTableDS.currentPage).then(() => {
        setPendingCount(pendingTableDS.totalCount);
      });
    } else {
      pendingTableDS.query().then(() => {
        setPendingCount(pendingTableDS.totalCount);
      });
    }

    unclaimedTableDS.setQueryParameter('tableKey', TabKey.receiveTask);
    unclaimedTableDS.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
    );
    unclaimedTableDS.query().then(() => {
      setUnclaimedCount(unclaimedTableDS.totalCount);
    });
  }, []);

  // 不良记录单管理跳转过来时自动查询的逻辑
  useEffect(() => {
    if (state?.sourceDocId) {
      searchTableDS?.queryDataSet.current?.set('inspectDocObj', {
        inspectDocId: state?.sourceDocId,
        inspectDocNum: state?.sourceDocNum,
      });
      handleChangeActiveKey('allTask');
    }
  }, [state?.sourceDocId]);

  // 切换tab
  const handleChangeActiveKey = async activeKey => {
    searchTableDS.setQueryParameter('tableKey', activeKey);
    setTabKey(activeKey);
    setPendingSelectList([]);
    setUnclaimedSelectList([]);
    setApprovealSelectList([]);
    setAllSelectList([]);
    await handleSearchDifferenceTab(activeKey);
  };
  // 不同tab查询
  const handleSearchDifferenceTab = async activeKey => {
    if (activeKey === 'processTask') {
      pendingTableDS.queryDataSet = searchTableDS.queryDataSet;
      pendingTableDS.setQueryParameter('tableKey', TabKey[activeKey]);
      pendingTableDS.setQueryParameter(
        'customizeUnitCode',
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
      );
      await pendingTableDS.query().then(() => {
        setPendingCount(pendingTableDS.totalCount);
      });
    } else if (activeKey === 'completeTask') {
      receivedTableDS.queryDataSet = searchTableDS.queryDataSet;
      receivedTableDS.setQueryParameter('tableKey', TabKey[activeKey]);
      receivedTableDS.setQueryParameter(
        'customizeUnitCode',
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
      );
      await receivedTableDS.query();
    } else if (activeKey === 'approvealTask') {
      approvealTableDS.queryDataSet = searchTableDS.queryDataSet;
      approvealTableDS.setQueryParameter('tableKey', TabKey[activeKey]);
      approvealTableDS.setQueryParameter(
        'customizeUnitCode',
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
      );
      await approvealTableDS.query();
    } else if (activeKey === 'receiveTask') {
      unclaimedTableDS.queryDataSet = searchTableDS.queryDataSet;
      unclaimedTableDS.setQueryParameter('tableKey', TabKey[activeKey]);
      unclaimedTableDS.setQueryParameter(
        'customizeUnitCode',
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
      );
      await unclaimedTableDS.query().then(() => {
        setUnclaimedCount(unclaimedTableDS.totalCount);
      });
    } else {
      allTableDS.queryDataSet = searchTableDS.queryDataSet;
      allTableDS.setQueryParameter('tableKey', TabKey[activeKey]);
      allTableDS.setQueryParameter(
        'customizeUnitCode',
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
      );
      await allTableDS.query();
    }
  };

  // 领取检验任务
  const handleReceiveTask = () => {
    const inspectTaskIds = unclaimedSelectList.map((record: Record) => record.get('inspectTaskId'));
    return receiveTask({
      params: inspectTaskIds,
      onSuccess: async () => {
        notification.success({});
        await handleSearchDifferenceTab(tabKey);
        setUnclaimedSelectList([]);
      },
    });
  };

  // 撤销检验任务
  const handleRevokeTask = async () => {
    const inspectTaskList = pendingSelectList.concat(allSelectList);
    const revokeInspectTaskList = inspectTaskList.filter(
      (record: Record) =>
        record.get('inspectTaskStatus') === 'RELEASED' &&
        record.get('inspectorId') &&
        `${record.get('inspectorId')}` !== '0',
    );
    if (revokeInspectTaskList.length !== inspectTaskList.length) {
      Modal.confirm({
        title: intl
          .get(`${modelPrompt}.message.revokeStatusValidTip`)
          .d(`您勾选的数据中包含了非下达或检验员为空的数据，这些数据将不做处理，是否继续？`),
        onOk: () => handleOkRevokeTask(revokeInspectTaskList),
      });
    } else {
      await handleOkRevokeTask(revokeInspectTaskList);
    }
  };

  // 确定撤销检验任务
  const handleOkRevokeTask = async inspectTaskList => {
    if (inspectTaskList.length < 1) {
      notification.success({});
      await handleSearchDifferenceTab(tabKey);
      setPendingSelectList([]);
      setAllSelectList([]);
    } else {
      const inspectTaskIds = inspectTaskList.map((record: Record) => record.get('inspectTaskId'));
      revokeTask({
        params: inspectTaskIds,
        onSuccess: async () => {
          notification.success({});
          await handleSearchDifferenceTab(tabKey);
          setPendingSelectList([]);
          setAllSelectList([]);
        },
      });
    }
  };

  // 取消检验任务
  const handleCancelTask = async () => {
    const inspectTaskList = pendingSelectList.concat(unclaimedSelectList).concat(allSelectList);
    const releasedTaskList = inspectTaskList.filter(
      (record: Record) => record.get('inspectTaskStatus') === 'RELEASED',
    );
    if (releasedTaskList.length !== inspectTaskList.length) {
      Modal.confirm({
        title: intl
          .get(`${modelPrompt}.message.cancelStatusValidTip`)
          .d(`您勾选的数据中包含了非下达的数据，这些数据将不做处理，是否继续？`),
        onOk: () => handleOkCancelTask(releasedTaskList),
      });
    } else {
      await handleOkCancelTask(releasedTaskList);
    }
  };

  // 确定取消检验任务
  const handleOkCancelTask = async inspectTaskList => {
    if (inspectTaskList.length < 1) {
      await handleSearchDifferenceTab(tabKey);
      setPendingSelectList([]);
      setUnclaimedSelectList([]);
      setAllSelectList([]);
    } else {
      const inspectTaskIds = inspectTaskList.map((record: Record) => record.get('inspectTaskId'));
      cancelTask({
        params: inspectTaskIds,
        onSuccess: async () => {
          notification.success({});
          await handleSearchDifferenceTab(tabKey);
          setPendingSelectList([]);
          setUnclaimedSelectList([]);
          setAllSelectList([]);
        },
      });
    }
  };

  // 分配检验任务
  const handleDistributeTask = () => {
    distributeDS.current?.set(
      'inspectTaskIds',
      unclaimedSelectList.map((record: Record) => record.get('inspectTaskId')),
    );
    Modal.open({
      ...drawerPropsC7n({
        ds: distributeDS,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.distributeTask`).d('分配检验任务'),
      drawer: false,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: 500,
      },
      contentStyle: {
        width: 500,
      },
      className: 'hmes-style-modal',
      children: (
        <Form dataSet={distributeDS} columns={1} labelWidth={80} style={{ marginRight: 20 }}>
          <Lov name="inspectorObj" />
        </Form>
      ),
      onOk: () => handleOkDistributeTask(),
    });
  };

  // 确定分配检验任务
  const handleOkDistributeTask = async () => {
    const validFlag = await distributeDS.validate();
    if (!validFlag) {
      return Promise.resolve(false);
    }
    return distributeTask({
      params: distributeDS?.current?.get('inspectTaskIds'),
      queryParams: {
        inspectorId: distributeDS?.current?.get('inspectorId'),
      },
      onSuccess: async () => {
        notification.success({});
        await handleSearchDifferenceTab(tabKey);
        setUnclaimedSelectList([]);
      },
    });
  };

  // 新建检验单
  const handleCreateInspectDoc = () => {
    history.push({
      pathname: `/hwms/inspection-platform/inspect-doc/create`,
      state: {
        inspectionPlatformFlag: 'PC',
      },
    });
  };

  // 报检条码明细弹窗
  const handleInspectMaterialDetail = async record => {
    inspectMaterialDS.setQueryParameter('inspectTaskId', record?.get('inspectTaskId'));
    inspectMaterialDS.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.OBJECT`,
    );
    const _inspectMaterialDrawer = Modal.open({
      ...drawerPropsC7n({
        ds: inspectMaterialDS,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.inspectMaterialDetail`).d('报检条码明细'),
      drawer: true,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: '70%',
      },
      className: 'hmes-style-modal',
      children: (
        <>
          {customizeTable(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.OBJECT`,
            },
            <Table
              dataSet={inspectMaterialDS}
              columns={inspectMaterialColumns}
              customizedCode="jypto"
            />,
          )}
        </>
      ),
      onCancel: () => {
        _inspectMaterialDrawer?.close();
      },
      footer: [],
    });
    await inspectMaterialDS.query();
  };

  // 状态渲染
  const renderTag = (value, record) => {
    switch (record.get('inspectTaskStatus')) {
      case 'NEW':
      case 'RELEASED':
        return <Tag color="green">{value}</Tag>;
      case 'INSPECTING':
      case 'REINSPECTING':
        return <Tag color="yellow">{value}</Tag>;
      case 'LAST_COMPLETED':
        return <Tag color="geekblue">{value}</Tag>;
      case 'FIRST_COMPLETED':
      case 'COMPLETED':
        return <Tag color="blue">{value}</Tag>;
      case 'CANCEL':
        return <Tag color="gray">{value}</Tag>;
      default:
        return null;
    }
  };

  const renderStatusTag = (value, record) => {
    switch (record.get('reviewStatus')) {
      case 'UNREVIEWED':
        return <Tag color="red">{value}</Tag>;
      case 'REVIEWING':
        return <Tag color="green">{value}</Tag>;
      case 'REVIEWED':
        return <Tag color="geekblue">{value}</Tag>;
      case 'REJECT':
        return <Tag color="red">{value}</Tag>;
      default:
        return null;
    }
  };

  // 报检条码明细
  const inspectMaterialColumns: ColumnProps[] = [
    {
      name: 'objectType',
    },
    {
      name: 'objectCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'quantity',
    },
    {
      name: 'uomName',
    },
    {
      name: 'locatorName',
    },
    {
      name: 'supplierLot',
    },
    {
      name: 'lot',
    },
  ];

  // 报检任务
  const columns: ColumnProps[] = [
    {
      name: 'materialName',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'supplierLot',
    },
    {
      name: 'warehouseName',
    },
    {
      name: 'inspectTaskCode',
      width: 160,
      renderer: ({ value, record }) => {
        // 跳转详情
        return (
          <a
            onClick={() => {
              history.push(`/hwms/inspection-platform/dist/${record?.get('inspectTaskId')}`);
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'inspectTaskStatusDesc',
      align: ColumnAlign.center,
      minWidth: 120,
      renderer: ({ value, record }) => renderTag(value, record),
    },
    {
      name: 'materialCode',
    },
    {
      name: 'inspectBusinessTypeDesc',
    },
    {
      name: 'urgentFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'inspectInfoUserName',
    },
    {
      name: 'inspectInfoCreationDate',
      align: ColumnAlign.center,
      width: 150,
      renderer: ({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value),
    },
    {
      name: 'inspectSumQty',
    },
    {
      name: 'uomName',
    },
    {
      name: 'inspectorName',
    },
    {
      name: 'inspectResultDesc',
    },
    {
      name: 'sourceObjectCode',
    },
    {
      name: 'siteCode',
    },
    {
      name: 'reviewStatusMeaning',
      align: ColumnAlign.center,
      minWidth: 120,
      renderer: ({ value, record }) => renderStatusTag(value, record),
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'woNumber',
    },
    {
      name: 'customerName',
    },
    {
      name: 'areaName',
    },
    {
      name: 'prodLineName',
    },
    {
      name: 'operationName',
    },
    {
      name: 'processWorkcellName',
    },
    {
      name: 'stationWorkcellName',
    },
    {
      name: 'equipmentCode',
    },
    {
      name: 'locatorName',
    },
    {
      name: 'inspectDocNum',
    },
    {
      name: 'okQty',
    },
    {
      name: 'ngQty',
    },
    {
      name: 'scrapQty',
    },
    {
      name: 'sourceTaskCode',
    },
    {
      name: 'taskCategory',
    },
    {
      name: 'sourceObjectTypeDesc',
    },
    {
      name: 'sourceObjectLineCode',
    },
    {
      name: 'shiftTeamCode',
    },
    {
      name: 'shiftDate',
      align: ColumnAlign.center,
      width: 150,
      renderer: ({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value),
    },
    {
      name: 'shiftCode',
    },
    {
      name: 'outsourceSupplierName',
    },
    {
      name: 'actualStartTime',
      align: ColumnAlign.center,
      width: 150,
      renderer: ({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value),
    },
    {
      name: 'actualEndTime',
      align: ColumnAlign.center,
      width: 150,
      renderer: ({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value),
    },
    {
      name: 'creationDate',
      align: ColumnAlign.center,
      width: 150,
      renderer: ({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value),
    },
    {
      name: 'remark',
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      width: 120,
      lock: ColumnLock.right,
      renderer: ({ record }) => {
        return (
          <PermissionButton
            type="text"
            permissionList={[
              {
                code: `list.button.inspectMaterialDetail`,
                type: 'button',
                meaning: '列表页-报检条码明细',
              },
            ]}
            onClick={() => handleInspectMaterialDetail(record)}
          >
            {intl.get(`${modelPrompt}.button.inspectMaterialDetail`).d('报检条码明细')}
          </PermissionButton>
        );
      },
    },
  ];

  // 处理导出按钮使用的查询参数
  const getExportQueryParams = () => {
    let ds;
    let key;
    switch (tabKey) {
      case "processTask":
        ds = pendingTableDS;
        key = 'PENDING';
        break;
      case "receiveTask":
        ds = unclaimedTableDS;
        key = 'UNCLAIMED';
        break;
      case "approvealTask":
        ds = approvealTableDS;
        key = 'APPROVEAL';
        break;
      case "completeTask":
        ds = receivedTableDS;
        key = 'RECEIVED';
        break;
      case "allTask":
        ds = allTableDS;
        key = 'ALL';
        break;
      default:
    }
    if (!ds.queryDataSet || !ds.queryDataSet.current) {
      return {};
    }
    const queryParmas = ds.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
      tableKey: key,
    };
  };

  // 批量合格
  const handleBatchQualified = async () => {
    const inspectTaskList = pendingSelectList.concat(unclaimedSelectList);
    const data = inspectTaskList
      .map((item: any) => item.toData())
      .filter((j: any) => ['COMPLETED', 'CANCEL'].includes(j.inspectTaskStatus));
    if (data.length > 0) {
      return notification.error({ message: '勾选检验任务不全为下达或检验中状态，请检查!' });
    }
    handleOkBatchQualified(inspectTaskList);
  };

  // 确定批量合格
  const handleOkBatchQualified = async inspectTaskList => {
    const inspectTaskIds = inspectTaskList.map((record: Record) => record.get('inspectTaskId'));
    batchQualified({
      params: { inspectTaskIds },
      onSuccess: async res => {
        if (res.confirmFlag === 'Y' && res.message) {
          Modal.confirm({
            title: res.message,
            okText: intl.get('tarzan.common.label.yes').d('是'),
            cancelText: intl.get('tarzan.common.label.no').d('否'),
            onOk: async() => {
              await batchQualified({ params: { inspectTaskIds, confirmFlag: 'Y' } });
              notification.success({});
              handleSearchDifferenceTab(tabKey);
              setPendingSelectList([]);
              setUnclaimedSelectList([]);
            },
            onCancel: async() => {
              await batchQualified({ params: { inspectTaskIds, confirmFlag: 'N' } });
              notification.success({});
              handleSearchDifferenceTab(tabKey);
              setPendingSelectList([]);
              setUnclaimedSelectList([]);
            },
          });
        } else {
          notification.success({});
          handleSearchDifferenceTab(tabKey);
          setPendingSelectList([]);
          setUnclaimedSelectList([]);
        }
      },
    });
  };

  const handleBatchExport = () => {
    openTab({
      key: `/hwms/inspection-platform/MT.INSPECTION_CREATE_IMPORT`,
      title: intl.get(`${modelPrompt}.inspectionExport`).d('检验平台导入'),
      search: queryString.stringify({
        prefixPath: '',
        action: intl.get(`${modelPrompt}.inspectionExport`).d('检验平台导入'),
      }),
    });
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('检验平台')}>
        {tabKey === 'receiveTask' && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              disabled={unclaimedSelectList.length < 1}
              onClick={handleReceiveTask}
              permissionList={[
                {
                  code: `list.button.receiveTask`,
                  type: 'button',
                  meaning: '列表页-领取检验任务',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.receiveTask`).d('领取检验任务')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              disabled={unclaimedSelectList.length < 1}
              loading={distributeTaskLoading}
              onClick={handleDistributeTask}
              permissionList={[
                {
                  code: `list.button.distributeTask`,
                  type: 'button',
                  meaning: '列表页-分配检验任务',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.distributeTask`).d('分配检验任务')}
            </PermissionButton>
          </>
        )}
        {['processTask', 'allTask'].includes(tabKey) && (
          <PermissionButton
            type="c7n-pro"
            disabled={pendingSelectList.length < 1 && allSelectList.length < 1}
            loading={revokeTaskLoading}
            onClick={handleRevokeTask}
            permissionList={[
              {
                code: `list.button.revokeTask`,
                type: 'button',
                meaning: '列表页-撤销检验任务',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.revokeTask`).d('撤销检验任务')}
          </PermissionButton>
        )}
        {['processTask', 'receiveTask', 'allTask'].includes(tabKey) && (
          <>
            <PermissionButton
              type="c7n-pro"
              disabled={
                pendingSelectList.length < 1 &&
                unclaimedSelectList.length < 1 &&
                allSelectList.length < 1
              }
              loading={cancelTaskLoading}
              onClick={handleCancelTask}
              permissionList={[
                {
                  code: `list.button.cancelTask`,
                  type: 'button',
                  meaning: '列表页-取消检验任务',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.cancelTask`).d('取消检验任务')}
            </PermissionButton>
          </>
        )}
        {/* {['processTask', 'receiveTask'].includes(tabKey) && (
          <>
            <PermissionButton
              type="c7n-pro"
              disabled={pendingSelectList.length < 1 && unclaimedSelectList.length < 1}
              loading={batchQualifiedLoading}
              onClick={handleBatchQualified}
              permissionList={[
                {
                  code: `list.button.batchQualified`,
                  type: 'button',
                  meaning: '列表页-批量合格',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.batchQualified`).d('批量合格')}
            </PermissionButton>
          </>
        )} */}
        <PermissionButton
          type="c7n-pro"
          onClick={handleCreateInspectDoc}
          permissionList={[
            {
              code: `list.button.createInspectDoc`,
              type: 'button',
              meaning: '列表页-新建检验单',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.createInspectDoc`).d('新建检验单')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          style={{ border: 'none', backgroundColor: 'white', outline: 'none' }}
          permissionList={[
            {
              code: `button.export`,
              type: 'button',
              meaning: '列表页-导出',
            },
          ]}
          className={styles['lov-btn-permissbutton-a']}
        >
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-task/list/export/ui`}
            queryParams={getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
            otherButtonProps={{
              disabled: true,
            }}
          />
        </PermissionButton>
        <Button onClick={handleBatchExport}>
          {intl.get(`${modelPrompt}.import`).d('导入')}
        </Button>
      </Header>
      <Content className={styles['inspection-platform']}>
        <Spin
          spinning={
            receiveTaskLoading ||
            revokeTaskLoading ||
            cancelTaskLoading ||
            distributeTaskLoading ||
            batchQualifiedLoading
          }
        >
          {customizeTable(
            {
              filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY`,
            },
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
                queryFieldsLimit: 7,
              }}
              dataSet={searchTableDS}
              columns={[]}
              searchCode="InspectionPlatform"
              className={styles['inspection-platform-search-table']}
            />,
          )}
          {customizeTabPane(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.PAGE`,
            },
            <Tabs activeKey={tabKey} onChange={handleChangeActiveKey} animated={false}>
              <TabPane
                tab={intl.get(`${modelPrompt}.tab.pendingTask`).d('待处理检验任务')}
                countRenderer={() => (
                  <Badge count={pendingCount} showZero className={styles['tab-super-count']} />
                )}
                key="processTask"
              >
                {customizeTable(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
                  },
                  <Table
                    queryBar={TableQueryBarType.none}
                    queryBarProps={{
                      fuzzyQuery: false,
                    }}
                    dataSet={pendingTableDS}
                    columns={columns}
                    customizedCode="jyptli"
                  />,
                )}
              </TabPane>
              <TabPane
                tab={intl.get(`${modelPrompt}.tab.unclaimedTask`).d('待领取检验任务')}
                countRenderer={() => (
                  <Badge
                    count={unclaimedCount}
                    showZero
                    className={styles['tab-super-count']}
                    style={{ backgroundColor: '#11D954' }}
                  />
                )}
                key="receiveTask"
              >
                {customizeTable(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
                  },
                  <Table
                    queryBar={TableQueryBarType.none}
                    dataSet={unclaimedTableDS}
                    columns={columns}
                    customizedCode="jyptli"
                  />,
                )}
              </TabPane>
              <TabPane
                tab={intl.get(`${modelPrompt}.tab.approvealTask`).d('待审核检验任务')}
                key="approvealTask"
              >
                {customizeTable(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
                  },
                  <Table
                    queryBar={TableQueryBarType.none}
                    queryBarProps={{
                      fuzzyQuery: false,
                    }}
                    dataSet={approvealTableDS}
                    columns={columns}
                    customizedCode="jyptli"
                  />,
                )}
              </TabPane>
              <TabPane
                tab={intl.get(`${modelPrompt}.tab.receivedTask`).d('已处理检验任务')}
                key="completeTask"
              >
                {customizeTable(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
                  },
                  <Table
                    queryBar={TableQueryBarType.none}
                    queryBarProps={{
                      fuzzyQuery: false,
                    }}
                    dataSet={receivedTableDS}
                    columns={columns}
                    customizedCode="jyptli"
                  />,
                )}
              </TabPane>
              <TabPane tab={intl.get(`${modelPrompt}.tab.allTask`).d('全部检验任务')} key="allTask">
                {customizeTable(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
                  },
                  <Table
                    queryBar={TableQueryBarType.none}
                    dataSet={allTableDS}
                    columns={columns}
                    customizedCode="jyptli"
                  />,
                )}
              </TabPane>
            </Tabs>,
          )}
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.qms.inspectionPlatform', 'tarzan.common'],
})(
  withProps(
    () => {
      const searchTableDS = new DataSet({
        ...TableDS(),
      });
      // 待处理检验任务
      const pendingTableDS = new DataSet({
        ...TableDS(),
      });
      // 已领取检验任务
      const receivedTableDS = new DataSet({
        ...TableDS(),
      });
      // 待领取检验任务
      const unclaimedTableDS = new DataSet({
        ...TableDS(),
      });
      // 待审核检验任务
      const approvealTableDS = new DataSet({
        ...TableDS(),
      });
      // 全部检验任务
      const allTableDS = new DataSet({
        ...TableDS(),
      });
      return {
        searchTableDS,
        pendingTableDS,
        receivedTableDS,
        unclaimedTableDS,
        approvealTableDS,
        allTableDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.OBJECT`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.PAGE`,
      ],
      // @ts-ignore
    })(InspectionPlatformList),
  ),
);
