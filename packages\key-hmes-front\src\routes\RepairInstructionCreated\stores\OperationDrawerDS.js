/**
 * 详情页表格中-工艺组件-抽屉表格中的ds
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.process.routes.model.routes';

const operationDrawerDS = () => ({
  primaryKey: 'routerStepId',
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentLineNumber`).d('组件行号'),
    },
    {
      name: 'bomComponentId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomComponentId`).d('组件物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomComponentRevision`).d('组件物料版本'),
    },
    {
      name: 'bomComponentMaterialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentDescription`).d('组件物料描述'),
    },
    {
      name: 'perQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.perQty`).d('单位用量'),
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});


export { operationDrawerDS };
