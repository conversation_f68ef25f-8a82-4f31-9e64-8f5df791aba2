import React, { FC, useRef } from 'react';
import classnames from 'classnames';
import FileSaver from 'file-saver';
import { Modal, Picture } from 'choerodon-ui/pro';
import { Tooltip } from 'choerodon-ui';
import { getFileSuffix, canPreviewByOnlyOffice } from '@/utils/file';
import download from '@/assets/download.png';
import { HZERO_FILE } from 'utils/config';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';

import './index.modules.less';

import rar from './image/rar.png';
import pdf from './image/pdf.png';
import doc from './image/doc.png';
import xls from './image/xls.png';
import ppt from './image/ppt.png';
import txt from './image/txt.png';
import audio from './image/audio.svg';
import video from './image/video.svg';
import unknown from './image/unknown.png';
import closeImg from './image/close.png';

const tenantId = getCurrentOrganizationId();

export interface UploadProps {
  fileName: string;
  fileUrl: string;
  mediaType: string;
  mode?: string;
  bucketName?: string;
}

const FileItem: FC<UploadProps> = props => {
  const { fileName, fileUrl, mediaType, mode, bucketName } = props;
  const picRef = useRef<any>(null);
  const suffix = getFileSuffix(fileUrl);

  const imageSuffixList = ['png', 'jpg', 'jpeg'];

  const suffixImgMap = new Map([
    ['doc', doc],
    ['docx', doc],
    ['xls', xls],
    ['xlsx', xls],
    ['pdf', pdf],
    ['rar', rar],
    ['zip', rar],
    ['ppt', ppt],
    ['pptx', ppt],
    ['pot', ppt],
    ['potx', ppt],
    ['txt', txt],
    ['png', fileUrl],
    ['jpg', fileUrl],
    // 视频
    ['mp4', video],
    ['webm', video],
    ['3gp', video],
    // 音频文件
    ['mp3', audio],
    ['wav', audio],
    ['ogg', audio],
  ]);

  const handleDownload = e => {
    e.stopPropagation();
    FileSaver.saveAs(fileUrl, fileName);
  };

  /**
   * 预览文件
   */
  const previewFile = file => {
    const url = `${HZERO_FILE}/v1/${tenantId}/file-preview/by-url?url=${encodeURIComponent(
      file.url,
    )}&bucketName=${file.bucketName}${
      file.storageCode ? `&storageCode=${file.storageCode}` : ''
    }&access_token=${getAccessToken()}`;
    window.open(url);
  };

  const handlePreview = () => {
    if (mode === 'view' && canPreviewByOnlyOffice(fileName)) {
      if (imageSuffixList.includes(suffix)) {
        // eslint-disable-next-line no-unused-expressions
        picRef?.current.preview();
      } else {
        previewFile({
          suffix,
          fileName,
          bucketName,
          url: fileUrl,
        });
      }
    } else if (mode === 'view' && ['mp4', 'webm', '3gp'].includes(suffix)) {
      // app端上传的3gp音频文件mediaType为record。在PC端不可播放音频文件，只播放视频文件
      if (mediaType !== 'record') {
        openVideo();
      }
    }
  };

  const openVideo = React.useCallback(() => {
    const vProps = {
      width: 800,
      height: 450,
      controls: true,
      autoPlay: true,
      loop: true,
      src: fileUrl,
    };
    const m = Modal.open({
      header: null,
      maskClosable: true,
      destroyOnClose: true,
      closable: true,
      contentStyle: {
        padding: 0,
        width: 800,
        height: 450,
      },
      bodyStyle: {
        padding: 0,
        width: 800,
        height: 450,
        overflow: 'hidden',
      },
      children: (
        <div className="video-wrapper">
          <video {...vProps}>
            <track default kind="captions" />
            您的浏览器不支持Video标签。
          </video>
          <img
            src={closeImg}
            alt=""
            onClick={() => {
              m.close();
            }}
          />
        </div>
      ),
      footer: null,
    });
  }, []);

  return (
    // 音频文件只可下载查看，可播放视频文件、可预览文件的文本高亮
    <div
      className={classnames('kb-doc-file-item', {
        preview:
          canPreviewByOnlyOffice(fileName) ||
          ['mp4', 'mov', 'webm'].includes(suffix) ||
          (mediaType === 'video' && ['3gp'].includes(suffix)),
      })}
      onClick={handlePreview}
    >
      <div className={classnames('kb-doc-file-image')}>
        {/* 针对3gp拓展名可能是视频或音频文件进行处理 */}
        <img
          src={
            (mediaType === 'record' && suffix === '3gp' ? audio : suffixImgMap.get(suffix)) ||
            unknown
          }
          alt=""
        />
      </div>
      <div className={classnames('kb-doc-file-name')}>
        <Tooltip title={fileName}>
        <span>{fileName}</span>
        </Tooltip>
      </div>
      <div className={classnames('kb-doc-file-operation')}>
        {mode === 'view' ? (
          <>
            <Tooltip title="下载">
              <img
                className={classnames('kb-doc-file-operation-download')}
                src={download}
                alt=""
                onClick={handleDownload}
              />
            </Tooltip>
          </>
        ) : null}
      </div>
      <Picture ref={picRef} style={{ display: 'none' }} src={fileUrl} />
    </div>
  );
};

export default FileItem;
