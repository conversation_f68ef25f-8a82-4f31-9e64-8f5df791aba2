import React, { useEffect, useMemo } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  TextField,
  Select,
  DateTimePicker,
  Attachment,
  Spin,
  TextArea,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { useDataSetEvent } from 'utils/hooks';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import queryString from 'querystring';
import { DocInfoDS } from '../stores/DetailDS';
import { GetDefaultSite } from '../services';

const tenantId = getCurrentOrganizationId();

const { Panel } = Collapse;
const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';

const MsaAnalysisManagementPlatformDetail = props => {
  const { history } = props;

  const detailDs = useMemo(
    () =>
      new DataSet({
        ...DocInfoDS(),
      }),
    [],
  );
  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });

  useDataSetEvent(detailDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialLov':
        record.set('productionVersionLov', {});
        record.set('defaultBomLov', {});
        record.set('defaultRouterLov', {});
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    const routerParam = queryString.parse(history.location.search.substr(1));
    console.log(routerParam)
    // 此处需修改为查询具体数据
    getDefaultSite({
      onSuccess: res => {
        if (res?.siteId) {
          detailDs.current?.set('siteObj', res);
        }
      },
    });
  }, []);

  /**
   *
   * @param 判断是保存还是提交
   * @returns
   */
  const handleSave = async ({ submitFlag }) => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    console.log(submitFlag);
  };

  return (
    <div className="hmes-style">
      <Spin dataSet={detailDs} spinning={siteLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('MSA分析管理平台/MSA分析任务')}
          backPath="/hwms/msa-analysis-management/platform/list"
        >
          <Button
            color={ButtonColor.primary}
            onClick={() => handleSave({ submitFlag: 'Y' })}
          >
            {intl.get(`${modelPrompt}.button.saneAndSubmit`).d('提交')}
          </Button>
          <Button
            color={ButtonColor.primary}
            icon="save"
            onClick={() => handleSave({ submitFlag: 'N' })}
          >
            {intl.get('tarzan.common.button.save').d('保存')}
          </Button>
          <Button
            onClick={() => handleSave({ submitFlag: 'N' })}
          >
            {intl.get('tarzan.common.button.analysisDetail').d('分析详情')}
          </Button>
          <Button
            onClick={() => handleSave({ submitFlag: 'N' })}
          >
            {intl.get('tarzan.common.button.analysis').d('开始分析')}
          </Button>
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'basicInfo',
              'problemDesc',
              'problemReason',
              'problemMeasure',
              'taskInfo',
            ]}
          >
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.verificationInformation`).d('验证信息')}
            >
              <Form dataSet={detailDs} columns={3}>
                <TextField name="msaCode" />
                <Select name="msaStatus" />
                <Lov name="siteObj" disabled />
                <Select name="msaType" disabled />
                <Lov name="toolModelIdObj" />
                <TextField name="modelName" />
                <TextField name="qualityCharacteristic" disabled />
                <TextField name="speciesName" disabled />
                <TextField name="msaAnalysisMethod" disabled />
                <TextField name="completeTimeLimit" disabled />
                <Lov name="measureToolNumObj" />
                <TextField name="measureToolByName" disabled />
                <Select name="onlineFlag" />
                <Select name="specialCharacteristicFlag" />
                <Select name="projectStage" />
                <Select name="projectName" />
                <DateTimePicker name="planStartTime" />
                <DateTimePicker name="planEndTime" />
                <Lov name="analyzedByObj" />
                <Lov name="assistantByObj" />
                <Attachment name="enclosure" />
                <TextField name="remark" colSpan={3} />
              </Form>
              {detailDs?.current?.get('msaType') === '2' && (
                <Form dataSet={detailDs} columns={3}>
                  <TextField name="sourceMsaCode" />
                  <Lov name="improveByObj" />
                  <Attachment name="improveEnclosure" />
                  <TextArea name="reasonAnalyzed" colSpan={3} />
                  <TextArea name="effectVerification" colSpan={3} />
                </Form>
              )}
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(MsaAnalysisManagementPlatformDetail);
