import React, { useMemo } from 'react';
import DashboardCard from '../../components/DashboardCard';
import ScrollBoard from '../../components/ScrollBoard';
import styles from '../../index.module.less';
import intl from "utils/intl";

const modelPrompt = 'tarzan.wms.EquipmentMaintenanceManagementBoard';

const DailyStaffCompletionRank = ({ dailyCompletionCounts }) => {

    const tableData: any[] = [];
    if (dailyCompletionCounts.length) {
        dailyCompletionCounts.forEach((val) => {
            const {
                name,
                total,
            } = val;
            tableData.push([
                name,
                total,
            ]);
        });
    }

    const config = useMemo(
        () => ({
            header: [
                intl.get(`${modelPrompt}.field.name`).d('人员'),
                intl.get(`${modelPrompt}.field.total`).d('完成单数'),
            ],
            data: tableData,
            rowNum: 8,
            align: ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'],
            oddRowBGC: 'rgba(22,66,127,0.3)',
            headerBGC: 'rgb(3, 157, 206,0.3)',
            evenRowBGC: 'rgba(3,28,60, 0.3)',
            headerHeight: 40,
        }),
        [tableData],
    );

    return (
        <DashboardCard style={{ height: '100%' }} >
            <div style={{ width: '100%', height: '100%' }}>
                <div className={styles['my-scroll-board-table']}>
                    <ScrollBoard config={config} style={{ width: '100%', height: '100%', marginLeft: '0px 10px' }} />
                </div>
            </div>
        </DashboardCard>
    );
};

export default DailyStaffCompletionRank;