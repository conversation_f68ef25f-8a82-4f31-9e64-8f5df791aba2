/**
 * @Description: 执行作业管理列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:40:22
 * @LastEditTime: 2023-01-10 13:56:55
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { DataSet, Table, Dropdown, Menu, Modal, Button, Select, Form, TextField } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import { getResponse } from '@utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Button as PermissionButton } from 'components/Permission';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import myInstance from '@utils/myAxios';
import { TemplatePrintButton } from '../../../components/tarzan-ui';
import HistoryDrawer from './HistoryDrawer';
import { tableDS, historyDS, printInfoDS } from '../stores/ExecuteListDS';
import styles from '../index.module.less';


const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.workshop.execute';
let printerModal;
const { Option } = Select;

const ExecuteList = props => {
  const {
    dataSet,
    historyDs,
    printInfoDs,
    match: { path },
    customizeTable,
  } = props;

  // 选中列表项执行作业状态 (选中条状态不同时为空字符)
  const [status, setStatus] = useState('');
  // 选中列表项执行作业状态 (选中条状态不同时为空字符)
  const [selectIds, setSelectIds] = useState([]);

  const columns = [
    {
      name: 'eoNum',
      lock: 'left',
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              orderDetail(record.data.eoId);
            }}
          >
            {value}
          </a>
        );
      },
      width: 220,
    },
    {
      name: 'identification',
      width: 220,
    },
    {
      name: 'statusDesc',
      width: 150,
    },
    {
      name: 'equipmentCode',
      width: 150,
    },
    {
      name: 'createdName',
      width: 150,
    },
    {
      name: 'bomName',
      width: 150,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'revisionCode',
      width: 120,
    },
    {
      name: 'materialName',
      width: 200,
    },
    {
      name: 'qty',
      width: 150,
      align: 'right',
    },
    {
      name: 'completedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'scrappedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'workOrderNum',
      width: 200,
    },
    {
      name: 'planStartTime',
      width: 150,
      align: 'center',
    },
    {
      name: 'planEndTime',
      width: 150,
      align: 'center',
    },
    {
      name: 'eoTypeDesc',
      width: 150,
    },
    {
      name: 'productionLineCode',
      width: 200,
    },
    // {
    //   name: 'productionLineName',
    //   width: 200,
    // },
    {
      name: 'siteCode',
      width: 120,
    },
    {
      name: 'qualityStatusDesc',
      width: 120,
    },
    {
      name: 'reworkFlagDesc',
      width: 120,
    },
    {
      name: 'concessiveInterceptionFlagDesc',
      width: 120,
    },
    {
      name: 'degradeFlagDesc',
      width: 120,
    },
    {
      name: 'modelCode',
      width: 150,
    },
    {
      name: 'lastUpdateDate',
      width: 150,
    },
  ];

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.query).length === 0 && props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      // dataSet.query(dataSet.currentPage);
      dataSet.setQueryParameter('customizeUnitCode', 'MT_EO.LIST.QUERY,MT_EO.LIST.TABLE');
      dataSet.query(props.dataSet.currentPage);
      handleDataSetSelectUpdate();
    }
    if (Object.keys(props?.location?.query).length !== 0 ) {
      // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
      const {
        workOrderNum,
        workOrderId,
      } = props?.location?.query || {};
      // const queryParams = {
      //   workOrder: workOrderNum ? { workOrderNum, workOrderId } : undefined, // 回显生产指令编码
      //   workOrderId: workOrderId || undefined, // 回显生产指令编码
      // };
      setTimeout(() => {
        // dataSet.queryDataSet.loadData([queryParams]);
        dataSet.queryDataSet.current.set('workOrder', workOrderNum ? { workOrderNum, workOrderId } : undefined)
        dataSet.queryDataSet.current.set('workOrderId',  workOrderId || undefined)
        dataSet.setQueryParameter('customizeUnitCode', 'MT_EO.LIST.QUERY,MT_EO.LIST.TABLE');
        dataSet.query(props.dataSet.currentPage);
        handleDataSetSelectUpdate();
      }, 200);
    }
  }, [props?.location?.query, props?.location?.state]);


  // useEffect(() => {
  //   dataSet.setQueryParameter('customizeUnitCode', 'MT_EO.LIST.QUERY,MT_EO.LIST.TABLE');
  //   dataSet.query(props.dataSet.currentPage);
  //   handleDataSetSelectUpdate();
  // }, []);

  // 监听C7Npro列表选中操作
  useEffect(() => {
    if (dataSet) {
      dataSet.addEventListener('select', handleDataSetSelectUpdate);
      dataSet.addEventListener('unSelect', handleDataSetSelectUpdate);
      dataSet.addEventListener('selectAll', handleDataSetSelectUpdate);
      dataSet.addEventListener('unSelectAll', handleDataSetSelectUpdate);
    }
    return () => {
      if (dataSet) {
        dataSet.removeEventListener('select', handleDataSetSelectUpdate);
        dataSet.removeEventListener('unSelect', handleDataSetSelectUpdate);
        dataSet.removeEventListener('selectAll', handleDataSetSelectUpdate);
        dataSet.removeEventListener('unSelectAll', handleDataSetSelectUpdate);
      }
    };
  });

  // 处理选中条执行作业状态
  const handleDataSetSelectUpdate = () => {
    if (dataSet && dataSet.selected) {
      const selectList = dataSet.selected;
      if (selectList && selectList.length > 0) {
        const { status: _status } = selectList[0].toData();
        let statusSame = true;
        const _selectIds = [];
        selectList.forEach(item => {
          const { status: itemStatus, eoId: itemEoId } = item.toData();
          if (itemEoId) {
            _selectIds.push(itemEoId);
          }
          if (itemStatus !== _status) {
            statusSame = false;
          }
        });
        if (statusSame) {
          setStatus(_status);
          setSelectIds(_selectIds);
        } else {
          setStatus('');
          setSelectIds([]);
        }
      } else {
        setStatus('');
        setSelectIds([]);
      }
    } else {
      setStatus('');
      setSelectIds([]);
    }
  };

  const orderDetail = id => {
    props.history.push(`/hmes/workshop/execute-operation-management/detail/${id}`);
  };

  const selectPrinter = (printerList) => {
    printerModal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.info.printer`).d('请选择打印机'),
      destroyOnClose: true,
      closable: true,
      style: {
        width: 400,
      },
      children: (
        <React.Fragment>
          <Form>
            <Select
              clearButton={false}
              onChange={(value) => handlePrint(value)}
              placeholder={intl.get(`${modelPrompt}.info.printer`).d('请选择打印机')}
            >
              {printerList.map(i => (
                <Option key={i.value} value={i}>
                  {i.meaning}
                </Option>
              ))}
            </Select>
          </Form>

        </React.Fragment>
      ),
      footer: null,
    });
  };


  // 打印
  const handlePrint = (printer) => {
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/barcode-print`;
    const params = {
      materialLotIds: selectIds,
      badQuantity: printInfoDs.current?.get('badQuantity'),
      remark: printInfoDs.current?.get('remark'),
      printer,
    };
    myInstance
      .post(url, params)
      .then(res => {
        if (res.data.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.success.print`).d('打印成功!'),
          });
          dataSet.query();
          printerModal?.close();
        } else {
          if (res.data.statusCode === "PRINTER_CHOOSE") {
            return selectPrinter(res.data.attr);
          }
          printerModal?.close();
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.error.print`).d('打印失败!'),
          });
        }
      });
  };

  const printInfoModal = () => {
    printInfoDs.reset();
    Modal.open({
      title: intl.get(`${modelPrompt}.printInfo`).d('打印信息录入'),
      key: Modal.key(),
      closable: true,
      destroyOnClose: true,
      style: {
        width: 400,
      },
      children:(
        <Form columns={1} dataSet={printInfoDs} labelWidth={120}>
          <TextField name="badQuantity" />
          <TextField name="remark" />
        </Form>
      ),
      onOk: () => handlePrint(null),
    });
  };


  const clickMenu = async ({ key }) => {
    const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/status/update/ui`, {
      method: 'POST',
      body: {
        eoIds: selectIds,
        operationType: key,
      },
    });
    const res = getResponse(response);
    if (res) {
      if (dataSet) {
        dataSet.batchUnSelect(dataSet.selected.map(item => item.id));
        setStatus('');
        setSelectIds([]);
      }
      notification.success();
      dataSet.query(props.dataSet.currentPage);
    }
  };

  const handleQueryHistory = () => {
    historyDs.setQueryParameter('eoIdList', dataSet.selected.map(item => item.get('eoId')));
    historyDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}</div>
        </div>
      ),
      destroyOnClose: true,
      children: (
        <HistoryDrawer ds={historyDs} />
      ),
    });
  };


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('执行作业管理')}>
        {/* <Dropdown
          overlay={
            <Menu onClick={clickMenu} className={styles['split-menu']}>
              {status === 'NEW' && (
                <Menu.Item key="RELEASE">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.issued`).d('下达')}
                  </a>
                </Menu.Item>
              )}
              {status === 'WORKING' && (
                <Menu.Item key="EO_WORKING_CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.workingCancel`).d('取消运行')}
                  </a>
                </Menu.Item>
              )}
              {status === 'RELEASED' && (
                <Menu.Item key="WORKING">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.working`).d('运行')}
                  </a>
                </Menu.Item>
              )}
              {(status === 'RELEASED' || status === 'WORKING') && (
                <Menu.Item key="HOLD">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.hold`).d('保留')}
                  </a>
                </Menu.Item>
              )}
              {(status === 'RELEASED' || status === 'WORKING') && (
                <Menu.Item key="COMPLETED">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.complete`).d('完成')}
                  </a>
                </Menu.Item>
              )}
              {(status === 'NEW' || status === 'RELEASED') && (
                <Menu.Item key="ABANDON">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.abandoned`).d('废弃')}
                  </a>
                </Menu.Item>
              )}
              {status === 'COMPLETED' && (
                <Menu.Item key="COMPLETED_CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.completeCancel`).d('取消完成')}
                  </a>
                </Menu.Item>
              )}
              {status === 'COMPLETED' && (
                <Menu.Item key="CLOSE">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.close`).d('关闭')}
                  </a>
                </Menu.Item>
              )}
              {status === 'CLOSED' && (
                <Menu.Item key="CLOSE_CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.closeCancel`).d('取消关闭')}
                  </a>
                </Menu.Item>
              )}
              {status === 'HOLD' && (
                <Menu.Item key="HOLD_CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.holdCancel`).d('取消保留')}
                  </a>
                </Menu.Item>
              )}
            </Menu>
          }
          trigger={['click']}
          disabled={status === ''}
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={status === '' || status === 'ABANDON'}
            permissionList={[
              {
                code: `${path}.button.changeStatus`,
                type: 'button',
                meaning: '列表页-状态变更按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>  */}
        <Button
          disabled={!dataSet.selected.length}
          onClick={handleQueryHistory}
        >
          {intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}
        </Button>
        <Button
          color="primary"
          disabled={!selectIds.length}
          onClick={printInfoModal}
        >
          {intl.get(`${modelPrompt}.button.network.print`).d('网络打印')}
        </Button>
        <TemplatePrintButton
          name={intl.get(`${modelPrompt}.button.local.print`).d('本地打印')}
          disabled={!selectIds.length}
          printButtonCode='MT.PROD_BARCODE_PRINT_EO'
          printParams={{ materialLotId: selectIds.join(',') }}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: 'MT_EO.LIST.QUERY',
            code: 'MT_EO.LIST.TABLE',
          },
          <Table
            queryBar="filterBar"
            queryFieldsLimit={18}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={dataSet}
            columns={columns}
            searchCode="ExecuteList"
            customizedCode="ExecuteList"
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.workshop.execute', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...tableDS() });
      const historyDs = new DataSet({ ...historyDS() });
      const printInfoDs = new DataSet({ ...printInfoDS() });
      return {
        dataSet,
        historyDs,
        printInfoDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: ['MT_EO.LIST.QUERY', 'MT_EO.LIST.TABLE'],
    })(ExecuteList),
  ),
);
