/**
 * @Description: 检验业务类型规则维护-service
 * @Author: <EMAIL>
 * @Date: 2023/1/30 17:40
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();




const endUrl = '';

/**
 * 保存物料计划属性
 * @function SaveMaterialPlan
 * @returns {object} fetch Promise
 */
export function SaveInspectBusTypeRule(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-bus-type-rule/update/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_DETAIL.DTL.BASIC`,
    method: 'POST',
  };
}
