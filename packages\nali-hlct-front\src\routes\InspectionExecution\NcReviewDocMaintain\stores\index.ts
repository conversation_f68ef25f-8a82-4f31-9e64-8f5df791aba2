/**
 * @Description: 不良记录单管理平台 - 主界面DS
 * @Author: <EMAIL>
 * @Date: 2023/3/7 13:54
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.ncReportDocMaintainNew';
const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'ncReportId',
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/page/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.LIST`,
        method: 'POST',
        data: {
          ...data,
          ncReportType: data?.ncReportType ? [data?.ncReportType] : undefined,
          ncReportStatus: data?.ncReportStatus ? [data?.ncReportStatus] : undefined,
          ncReviewStatus: data?.ncReviewStatus ? [data?.ncReviewStatus] : undefined,
          inspectBusinessTypes: data?.inspectBusinessTypes
            ? [data?.inspectBusinessTypes]
            : undefined,
          reviewTypes: data?.reviewTypes ? [data?.reviewTypes] : undefined,
          createMethods: data?.createMethods ? [data?.createMethods] : undefined,
          disposalType: data?.disposalType ? [data?.disposalType] : undefined,
          sourceDocIds: data?.sourceDocIds ? [data?.sourceDocIds] : undefined,
          inspectObjects: data?.inspectObjects ? [data?.inspectObjects] : undefined,
          materialIds: data?.materialIds ? [data?.materialIds] : undefined,
          sourceNcRecordNumList: data?.sourceNcRecordNumList
            ? [data?.sourceNcRecordNumList]
            : undefined,
        },
      };
    },
  },
  queryFields: [
    {
      name: 'ncReportNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportNum`).d('不良记录单编码'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.MATERIAL',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'materialIds',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'ncReportStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportStatus`).d('不良记录单状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_REPORT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'sourceDocLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceDoc`).d('来源检验单'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_DOC',
      lovPara: { tenantId },
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'sourceDocIds',
      bind: 'sourceDocLov.inspectDocId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      name: 'supplierObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      // multiple: true,
    },
    {
      name: 'supplierId',
      bind: 'supplierObject.supplierId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      textField: 'locatorName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      lovPara: { tenantId },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineObject.prodLineId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      name: 'operationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      // multiple: true,
    },
    {
      name: 'operationId',
      bind: 'operationObject.operationId',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'createdObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdObj`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'realName',
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      bind: 'createdObj.id',
    },
    // {
    //   name: 'ncReportType',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncReportType`).d('不良记录单类型'),
    //   textField: 'description',
    //   valueField: 'typeCode',
    //   lovPara: { tenantId },
    //   lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=NC_REPORT_TYPE`,
    //   lookupAxiosConfig: {
    //     transformResponse(data) {
    //       // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
    //       if (data instanceof Array) {
    //         return data;
    //       }
    //       const { rows } = JSON.parse(data);
    //       return rows;
    //     },
    //   },
    // },
    {
      name: 'inspectBusinessTypes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lookupCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      textField: 'inspectBusinessTypeDesc',
      valueField: 'inspectBusinessType',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'createMethods',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('创建方式'),
      lookupCode: 'MT.QMS.NC_REPORT_CREATE_METHOD',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    // {
    //   name: 'ncReviewStatus',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncReviewStatus`).d('审核状态'),
    //   lookupCode: 'MT.QMS.REVIEW_STATUS',
    //   lovPara: { tenantId },
    //   textField: 'meaning',
    //   valueField: 'value',
    // },
    // {
    //   name: 'reviewTypes',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reviewType`).d('评审规则'),
    //   lookupCode: 'MT.QMS.REVIEW_TYPE',
    //   lovPara: { tenantId },
    //   textField: 'meaning',
    //   valueField: 'value',
    // },
    // {
    //   name: 'disposalType',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.disposalType`).d('处置类型'),
    //   lookupCode: 'MT.DISPOSAL_TYPE',
    //   lovPara: { tenantId },
    //   textField: 'meaning',
    //   valueField: 'value',
    // },
    // {
    //   name: 'revisionCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    // },
    // {
    //   name: 'identifications',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.materialLotOrEo`).d('物料批/执行作业'),
    //   multiple: true,
    // },
    // {
    //   name: 'ncObjectType',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncObjectType`).d('不良对象类型'),
    //   lookupCode: 'MT.QMS.NC_OBJECT_TYPE',
    //   lovPara: { tenantId },
    //   textField: 'meaning',
    //   valueField: 'value',
    // },
    // {
    //   name: 'inspectObjectLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.ncObjectCode`).d('不良对象编码'),
    //   ignore: FieldIgnore.always,
    //   dynamicProps: {
    //     lovCode: ({ record }) => {
    //       switch (record?.get('ncObjectType')) {
    //         case 'MATERIAL_LOT':
    //           return 'MT.MATERIAL_LOT';
    //         case 'EO':
    //           return 'MT.EO';
    //         default:
    //           return 'MT.MES.MAT_LOT_MATERIAL_LOT';
    //       }
    //     },
    //     lovPara: ({ record }) => {
    //       switch (record?.get('ncObjectType')) {
    //         case 'MATERIAL_LOT':
    //           return {
    //             tenantId,
    //             enableFlag: 'Y',
    //             siteId: record?.get('siteId'),
    //           };
    //         case 'EO':
    //           return {
    //             tenantId,
    //             siteId: record?.get('siteId'),
    //           };
    //         case 'MAT':
    //           return {
    //             tenantId,
    //             siteId: record?.get('siteId'),
    //             identifyType: 'MAT',
    //           };
    //         default:
    //           return {
    //             tenantId,
    //             siteId: record?.get('siteId'),
    //             identifyType: 'LOT',
    //           };
    //       }
    //     },
    //     textField: ({ record }) => {
    //       switch (record?.get('ncObjectType')) {
    //         case 'EO':
    //           return 'eoNum';
    //         default:
    //           return 'materialLotCode';
    //       }
    //     },
    //     disabled: ({ record }) => !record?.get('ncObjectType'),
    //   },
    // },
    // {
    //   name: 'inspectObjects',
    //   type: FieldType.string,
    //   dynamicProps: {
    //     bind: ({ record }) => {
    //       switch (record?.get('ncObjectType')) {
    //         case 'EO':
    //           return 'ncObjectLov.eoNum';
    //         default:
    //           return 'ncObjectLov.materialLotCode';
    //       }
    //     },
    //   },
    // },
    // {
    //   name: 'inspectObjectRevisionCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.inspectObjectRevisionCode`).d('不良对象版本'),
    // },
    // {
    //   name: 'sourceNcRecordLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.sourceNcRecord`).d('来源不良记录编码'),
    //   ignore: FieldIgnore.always,
    //   lovCode: 'MT.MES.NC_RECORD',
    //   lovPara: { tenantId },
    //   dynamicProps: {
    //     lovPara: ({ record }) => ({
    //       tenantId,
    //       siteId: record?.get('siteId'),
    //     }),
    //   },
    // },
    // {
    //   name: 'sourceNcRecordNumList',
    //   bind: 'sourceNcRecordLov.ncRecordNum',
    // },
  ],
  fields: [
    {
      name: 'ncReportId',
      type: FieldType.number,
    },
    {
      name: 'ncReportNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportNum`).d('不良记录单编码'),
    },
    {
      name: 'ncReportTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportType`).d('不良记录单类型'),
    },
    {
      name: 'ncReportStatus',
      type: FieldType.string,
    },
    {
      name: 'ncReportStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportStatus`).d('不良记录单状态'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'ncReviewStatus',
      type: FieldType.string,
    },
    {
      name: 'ncReviewStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReviewStatus`).d('审核状态'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
    },
    {
      name: 'reviewTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('评审规则'),
    },
    {
      name: 'disposalTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalType`).d('处置类型'),
    },
    {
      name: 'createMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('创建方式'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'inspectSumQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.inspectSumQty`).d('报检总数'),
    },
    {
      name: 'okQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.okQty`).d('合格数'),
    },
    {
      name: 'ngQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.ngQty`).d('不合格数'),
    },
    {
      name: 'samplingQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.samplingQty`).d('抽样数'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'ncDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncDesc`).d('不良描述'),
    },
    // {
    //   name: 'createdByUserName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.createdByUserName`).d('创建人'),
    // },
    {
      name: 'recorderPersonDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recorderPersonDesc`).d('登记人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationTime`).d('创建时间'),
    },
    {
      name: 'sourceDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceDocNum`).d('来源检验单'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationCodeName`).d('工艺编码'),
    },
    {
      name: 'operationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationNameDesc`).d('工艺描述'),
    },
    {
      name: 'productionLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionLineCode`).d('生产线编码'),
    },
    {
      name: 'productionLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionLineName`).d('生产线描述'),
    },
  ],
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'ncReportLineId',
  transport: {
    read: () => ({
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report-line/page/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.LINE.LIST`,
      method: 'POST',
    }),
  },
  fields: [
    {
      name: 'ncReportLineId',
      type: FieldType.number,
    },
    {
      name: 'ncRecordTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
    },
    {
      name: 'ncObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncObjectType`).d('不良对象类型'),
    },
    {
      name: 'sourceDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceDocNum`).d('来源单据'),
    },
    {
      name: 'sourceNcRecordNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceNcRecordNum`).d('来源不良记录编码'),
    },
    {
      name: 'ncObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncObjectCode`).d('不良对象编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectObjectRevisionCode`).d('不良对象版本'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
    },
    {
      name: 'containerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerName`).d('容器'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备编码'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
    },
    {
      name: 'interceptWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.interceptWorkcellName`).d('拦截工作单元编码'),
    },
    {
      name: 'interceptOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.interceptOperationName`).d('拦截工艺编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位编码'),
    },
    {
      name: 'ncStartUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncStartUserName`).d('不良记录人'),
    },
    {
      name: 'ncStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncStartTime`).d('不良发生时间'),
    },
    {
      name: 'shiftTeamCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftTeamCode`).d('班组编码'),
    },
    {
      name: 'shiftDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftDate`).d('班次日期'),
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'ncDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncDesc`).d('不良描述'),
    },
  ],
});

const reviewHisInfoDS: () => DataSetProps = () => ({
  primaryKey: 'ncReportReviewId',
  paging: false,
  transport: {
    read: () => ({
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report-review/page/ui`,
      method: 'POST',
    }),
  },
  fields: [
    {
      name: 'ncReportReviewId',
      type: FieldType.number,
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('审核顺序'),
    },
    {
      name: 'reviewApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewApartment`).d('审核部门'),
    },
    {
      name: 'reviewUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewUserName`).d('审核人'),
    },
    {
      name: 'reviewResult',
      type: FieldType.string,
    },
    {
      name: 'reviewResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewResult`).d('审核结果'),
    },
    {
      name: 'reviewTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.reviewTime`).d('审核时间'),
    },
    {
      name: 'finalConfirmFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.finalConfirmFlag`).d('是否最终审核结果'),
    },
  ],
});

const reviewHisLineDS: () => DataSetProps = () => ({
  primaryKey: 'ncReportReviewDtlId',
  selection: false,
  paging: false,
  transport: {
    read: () => ({
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report-review/page/ui`,
      method: 'POST',
    }),
  },
  fields: [
    {
      name: 'ncReportReviewDtlId',
      type: FieldType.number,
    },
    {
      name: 'ncRecordTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
    },
    {
      name: 'ncObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncObjectType`).d('不良对象类型'),
    },
    {
      name: 'ncObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncObjectCode`).d('不良对象编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectObjectRevisionCode`).d('不良对象版本'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'dispositionFunctionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispositionFunction`).d('处置方法'),
    },
    {
      name: 'reworkRouterName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkRouterName`).d('返修工艺路线'),
    },
    // {
    //   name: 'reworkStepName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reworkStepName`).d('返修步骤识别码'),
    // },
    {
      name: 'reworkOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkOperationName`).d('返修工艺'),
    },
    {
      name: 'reworkWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkWorkcellName`).d('返修工作单元'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

export { headDS, lineDS, reviewHisInfoDS, reviewHisLineDS };
