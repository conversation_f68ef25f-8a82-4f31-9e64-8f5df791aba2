/**
 * @Description: 标准体系文件维护-表格DS
 * @Author: <<EMAIL>>
 * @Date: 2023-06-27
 * @LastEditTime: 2022-06-28
 * @LastEditors: <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'key.hwms.front.MaintenanceOfStandardSystemDocuments';
const tableDS = () => ({
  autoQuery: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'templateFileCode',
      label: intl.get(`${modelPrompt}.templateFileCode`).d('编码'),
      type: FieldType.string,
    },
    {
      name: 'templateFileName',
      label: intl.get(`${modelPrompt}.templateFileName`).d('名称'),
      type: FieldType.string,
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.TEMPLATE_FILE_STATUS',
    },
    {
      name: 'userObj',
      label: intl.get(`${modelPrompt}.userObj`).d('创建人'),
      lovCode: 'LOV_USER',
      ignore: 'always',
      type: FieldType.object,
      textField: 'realName',
      valueField: 'createdBy',
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'userObj.id',
      type: FieldType.string,
    },
    {
      name: 'realName',
      bind: 'userObj.realName',
      type: FieldType.string,
    },
  ],
  fields: [
    {
      name: 'templateFileCode',
      label: intl.get(`${modelPrompt}.templateFileCode`).d('编码'),
      type: FieldType.string,
    },
    {
      name: 'templateFileName',
      label: intl.get(`${modelPrompt}.templateFileName`).d('名称'),
      type: FieldType.string,
    },
    {
      name: 'statusName',
      label: intl.get(`${modelPrompt}.statusName`).d('状态（启用/禁用）'),
      type: FieldType.string,
    },
    {
      name: 'remark',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      type: FieldType.string,
    },
    {
      name: 'createdByName',
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      type: FieldType.string,
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-template-file/list/query/for/ui`,
      method: 'GET',
    },
  },
});
export {tableDS}
