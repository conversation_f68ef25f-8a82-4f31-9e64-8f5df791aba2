import intl from 'utils/intl';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'inventorySummary';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.contentList',
  fields: [
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('电池型号'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号代码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    // 装配日期
    {
      name: 'windTimeSum10',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windTimeSum10`).d('4个月内'),
    },
    {
      name: 'windTimeSum11',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windTimeSum11`).d('4-7个月'),
    },
    {
      name: 'windTimeSum12',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windTimeSum12`).d('7-12个月'),
    },
    {
      name: 'windTimeSum13',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windTimeSum13`).d('12-24个月'),
    },
    {
      name: 'windTimeSum14',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windTimeSum14`).d('24个月以上'),
    },
    {
      name: 'windTimeSumTotal',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windTimeSumTotal`).d('合计'),
    },
    // 入库日期
    {
      name: 'inLocatorTimeSum20',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTimeSum20`).d('1个月内'),
    },
    {
      name: 'inLocatorTimeSum21',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTimeSum21`).d('1-3个月'),
    },
    {
      name: 'inLocatorTimeSum22',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTimeSum22`).d('3-6个月'),
    },
    {
      name: 'inLocatorTimeSum23',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTimeSum23`).d('6个月以上'),
    },
  ],
  queryFields: [
    {
      name: 'areaLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLocator`).d('仓库'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      required: true,
      multiple: true,
      noCache: true,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            locatorCategories: 'AREA',
          };
        },
      },
    },
    {
      name: 'locatorIds',
      bind: 'areaLocatorLov.locatorId',
    },
    {
      name: 'grade',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.grade`).d('等级'),
      lookupCode: "MT_FINISHED_INVENTORY_LEVEL",
      required: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.WMS_TARZAN_REPORT}/v1/${tenantId}/wms-finished-inventory-report/list/ui`,
        method: 'POST',
      };
    },
  },
});

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('主计量单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次号（箱号、托盘号）'),
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结状态'),
    },
    {
      name: 'property',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.property`).d('产品属性'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windTime`).d('装配时间'),
    },
    {
      name: 'numMonth',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.numMonth`).d('距今月数（装配日期）'),
    },
    {
      name: 'sum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sum`).d('数量'),
    },
    {
      name: 'locatorInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorInfo`).d('库位'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'latestRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.latestRemark`).d('最新库存处理意见'),
    },
    {
      name: 'lastRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastRemark`).d('上月库存处理意见'),
    },
  ],
  transport: {
    read: ({ params, data }) => {
      const inLocatorUrl = `${BASIC.WMS_TARZAN_REPORT}/v1/${tenantId}/wms-finished-inventory-report/in-locator/dtl/list/ui`
      const windUrl = `${BASIC.WMS_TARZAN_REPORT}/v1/${tenantId}/wms-finished-inventory-report/wind/dtl/list/ui`
      return {
        url: data.detailType === 'WIND' ? windUrl : inLocatorUrl,
        method: 'POST',
        params,
      };
    },
  },
});

export { tableDS, detailDS };
