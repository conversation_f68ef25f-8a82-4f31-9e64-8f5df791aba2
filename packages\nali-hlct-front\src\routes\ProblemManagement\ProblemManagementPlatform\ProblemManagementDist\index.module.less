.problem-detail-card {
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.08);
  padding: 12px;

  .problem-detail-card-head {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    .problem-detail-card-head-title {
      font-weight: 600;
      font-size: 16px;
      margin-left: 20px;
      display: flex;
      align-items: end;

      img {
        width: 30px;
        height: 30px;
        margin-right: 5px;
        color: black;
      }
    }
  }
}

.problem-detail-steps {
  margin: 15px 0;
}

.collapse-style {
  :global {
    .c7n-collapse-item {
      border: 1px solid rgba(0, 0, 0, 0.08) !important;
      margin-bottom: 8px;
      .c7n-collapse-header {
        background: rgb(242, 245, 254);
      }
    }
  }
}

.split-menu {
  :global {
    .c7n-menu-item > a,
    .c7n-menu-item > a:hover {
      color: #000;
    }
    .c7n-menu-item.c7n-menu-item-selected {
      background-color: inherit;
    }
  }
  .split-menu-title {
    :global {

    }
  }
}

.attachment-menu {
  display: flex;
  justify-content: space-between;
}

.enable-flag-button {
  padding: 0 4px;
  border: 1px dashed rgba(153, 153, 153, 0.6);
  font-size: 12px;
  border-radius: 2px;
  font-weight: 400;
  max-width: 140px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  i {
    width: 20px;
  }
}

.enable-flag-buttons-group {
  display: flex;
  height: 28px;
  line-height: 28px;

  .enable-flag-buttons-group-label {
    width: 100px;
    text-align: end;
    color: rgb(140, 140, 140);
  }

  .enable-flag-button {
    margin-right: 10px;
  }
}

.score-panel-content {
  padding: 0 20px;
}

.problem-score-header {
  display: flex;
  font-size: 24px;
  line-height: 64px;
  padding: 0 20px;
  color: rgb(68, 68, 68);
  background-color: rgb(246, 246, 246);
  border-radius: 4px;
  margin-bottom: 10px;

  .problem-score-header-label,
  .problem-score-remark {
    font-size: 16px;
    color: rgb(138, 138, 138);
  }
  .problem-score-remark {
    color: rgb(180, 180, 180);
  }
  .problem-score-header-score {
    color: rgb(5, 49, 241);
    margin-right: 20px;
  }
}

.steps-content {
  padding: 10px 0;
  width: 100%;
  display: flex;
  .step {
    position: relative;
    padding: 0 20px;
    color: #999;
    background: #F5F7FA;
    line-height: 34px;
    width: 15%;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .step:after {
    content: "";
    position: absolute;
    right: -15px;
    top: 0;
    z-index: 10;
    height: 34px;
    display: block;
    border-top: 17px solid transparent;
    border-bottom: 17px solid transparent;
    border-left: 17px solid #F5F7FA;
  }
  .step:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    height: 34px;
    border-top: 17px solid transparent;
    border-bottom: 17px solid transparent;
    border-left: 17px solid rgb(224, 227, 232);
  }
  .step:first-child {
    border-radius: 17px 0 0 17px;
  }
  .step:last-child {
    border-radius: 0 17px 17px 0;
  }
  .step:first-child:before {
    display: none;
  }
  .step:last-child:after {
    display: none;
  }
  .step.active {
    background-color: #0840F8;
    color: #fff;
    .step-sequence {
      border-color: #fff;
    }
  }
  .step.active:after {
    border-color: #F5F7FA;
    border-left-color: #0840F8;
  }
  .step-sequence {
    width: 20px;
    height: 20px;
    border-radius: 10px;
    border: 1px solid #999;
    line-height: 20px;
    text-align: center;
    margin-right: 10px;
  }
}

.user-role-component {
  display: flex;
  font-size: 14px;

  .user-role-item {
    width: 28px;
    height: 28px;
    border-radius: 14px;
    background: rgb(230, 230, 230);
    color: rgb(102, 102, 102);
    margin: 0 5px;
    text-align: center;
    line-height: 28px;
  }

  .user-role-item-selected {
    background: rgb(8, 64, 248);
    color: #fff;
  }
}

.delay-days-icon {
  margin-right: 10px;
}