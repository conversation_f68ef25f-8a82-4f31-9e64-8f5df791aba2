/**
 * @Description: 检验方案维护-详情-折叠栏title
 * @Author: <<EMAIL>>
 * @Date: 2023-01-17 11:15:22
 * @LastEditTime: 2023-02-10 17:09:44
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Icon } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import intl from 'utils/intl';
import moment from 'moment';
import axios from 'axios';

const modelPrompt = 'tarzan.initialManagementActivity';

const CollapsePanelTitle = observer(
  ({
    ds,
    itemDs,
    index,
    sortList,
    title,
    clickTitleCallback,
    activeKey,
    canEdit,
    dataObj,
  }: {
      ds: DataSet;
      // dimensionTableDs: DataSet;
      itemDs: DataSet;
      index: number;
      sortList: Function;
      title: string;
      clickTitleCallback: Function;
      activeKey;
      canEdit;
      dataObj: any;
    }) => {
    const { 
      schemeStage,
      initialActivityDocNum,
      validDateFrom,
      validDateTo,
    } = dataObj;
    const [list, setList] = useState<Array<any>>([])
    
    useEffect(() => {
      axios({
        url:`/hpfm/v1/0/lovs/value/batch?shiftList=YP.QIS.SCHEME_STAGE`,
      }).then((res: any) => {
        if (res) {
          setList(res?.shiftList);
        }
      })
    }, [])
    
    return (
      <span
        style={{
          fontWeight: 400,
          marginLeft: '18px',
        }}
      >
        {activeKey === '' ||
          !canEdit ||
          !itemDs.current?.get('inspectBusinessType') ||
          !(
            ['MATERIAL_CATEGORY', 'MATERIAL'].includes(ds.current?.get('inspectSchemeObjectType')) &&
            !!ds.current?.get('inspectSchemeObjectId')
          ) ? (
            <>
              <>{title}</>
              {schemeStage ? <Tag color="blue">
                {list.map(val => (val.value === schemeStage ? val.meaning : ''))}
              </Tag> : ''}
              {initialActivityDocNum ? <Tag color="yellow">
                {intl.get(`${modelPrompt}.initial`).d('初期')}{`：${initialActivityDocNum}\u00A0\u00A0 ${moment(validDateFrom).format('YYYY/MM/DD')}-${moment(validDateTo).format('YYYY/MM/DD')}`}
              </Tag> : ''}
            </>
          ) : (
            <>
              <a
                onClick={() => {
                  clickTitleCallback('edit');
                }}
              >
                {title}
              </a>
              {schemeStage ? <Tag color="blue">
                {list.map(val => (val.value === schemeStage ? val.meaning : ''))}
              </Tag> : ''}
              {initialActivityDocNum ? <Tag color="yellow">
                {intl.get(`${modelPrompt}.initial`).d('初期')}{`初期：${initialActivityDocNum}\u00A0\u00A0 ${moment(validDateFrom).format('YYYY/MM/DD')}-${moment(validDateTo).format('YYYY/MM/DD') }`}
              </Tag> : ''}
              <Icon
                onClick={() => {
                  sortList('up', index);
                }}
                type="arrow_upward"
                style={{ marginLeft: '18px' }}
              />
              <Icon
                onClick={() => {
                  sortList('down', index);
                }}
                type="arrow_downward"
                style={{ marginLeft: '6px' }}
              />
            </>
          )}
      </span>
    );
  },
);

export default CollapsePanelTitle;
