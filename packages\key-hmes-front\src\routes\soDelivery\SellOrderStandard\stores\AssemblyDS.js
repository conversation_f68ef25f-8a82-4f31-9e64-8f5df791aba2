import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const assemblyDS = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-so-head/detail/query`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'lpNumber',
      type: FieldType.string,
    },
    {
      name: 'lpCategoryDesc',
      type: FieldType.string,
    },
    {
      name: 'scheduleShipDate',
      type: FieldType.string,
    },
    {
      name: 'scheduleArrivalDate',
      type: FieldType.string,
    },
    {
      name: 'orderedQuantity',
      type: FieldType.string,
    },
    {
      name: 'shippedQuantity',
      type: FieldType.string,
    },
    {
      name: 'reservedQuantity',
      type: FieldType.string,
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'deleteFlag',
      type: FieldType.string,
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'remark',
      type: FieldType.string,
    },
  ],
});

export { assemblyDS };
