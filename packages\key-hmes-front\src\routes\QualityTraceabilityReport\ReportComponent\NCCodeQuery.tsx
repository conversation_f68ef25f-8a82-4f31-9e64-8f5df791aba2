import React, { useMemo, useCallback } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { useDataSetEvent } from 'utils/hooks';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableMode, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores/NCCodeQueryDS';

const modelPrompt = 'modelPrompt_code';

const NCCodeQuery = () => {

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const queryValidate = ({ dataSet }) => {
    const _record = dataSet.queryDataSet.current;
    if (
      !_record.get('materialLotCode') &&
      !_record.get('identification') &&
      !_record.get('ncCodeId')
    ) {
      notification.error({
        message: '除站点外，请至少输入一个查询条件！',
      });
      return false
    }
  }

  useDataSetEvent(tableDs, 'query', queryValidate);
  useDataSetEvent(tableDs.queryDataSet!, 'update', ({ record, name }) => {
    if (name === 'siteLov') {
      record.set('ncCodeLov', null)
    }
  });

  const columns: ColumnProps[] = useMemo(
    () => [
      { name: 'materialCode', lock: ColumnLock.left },
      { name: 'materialLotCode' },
      { name: 'identification' },
      { name: 'ncCodeDesc' },
      { name: 'ncStartTime', width: 180 },
      { name: 'ncStartUserName', width: 180 },
    ],
    [],
  );

  const nodeCover = useCallback(
    ({ record }) => {
      const nodeProps = { isLeaf: false };
      if (!record.get('childMaterialDtlList')?.length) {
        nodeProps.isLeaf = true;
      }
      return nodeProps;
    },
    [],
  )

  return (
    <div className="hmes-style">
      <Table
        mode={TableMode.tree}
        queryBar={TableQueryBarType.filterBar}
        queryBarProps={{
          fuzzyQuery: false,
          onQuery: () => false,
        }}
        queryFieldsLimit={8}
        dataSet={tableDs}
        columns={columns}
        onRow={nodeCover}
      />
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(NCCodeQuery);
