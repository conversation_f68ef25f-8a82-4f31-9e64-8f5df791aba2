import React, { FC, useEffect } from 'react';
import { RouteComponentProps } from 'react-router';
import { Button, DataSet, Table } from 'choerodon-ui/pro';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { observer } from 'mobx-react';
import notification from 'utils/notification';
import { Tag } from 'choerodon-ui';
import Axios from 'axios';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import lineTableFactory from '../stores/lineTableFactory';
import listPageFactory from '../stores/listPageFactory';

interface HierarchicalAuditProjectProps extends RouteComponentProps {
  tableDs: DataSet;
  lineDs: DataSet;
  customizeTable: any;
}

const modelPrompt = 'tarzan.qms.MeasuringScrapPlatform';
// const endUrl = '-138685';
const endUrl = '';

const HierarchicalAuditProject: FC<HierarchicalAuditProjectProps> = ({ tableDs, customizeTable, lineDs, history }) => {
  

  useEffect(()=>{
    tableDs.query();
    tableDs.addEventListener('query',()=>{
      tableDsQueryEvent();
    })
    return ()=>{
      tableDs.removeEventListener('query',()=>{
        tableDsQueryEvent();
      })
    }
  }, [])

  const tableDsQueryEvent = ()=>{
    lineDs.loadData([]);
  }

  const handleToDetails = (id)=>{
    history.push(`/hwms/measuring-scrap-platform/detail/${id}`);
  };


  const tags = (value, text)=>{
    let tag;
    switch (value){
      case 'NEW':
        tag = <Tag color="purple">{text}</Tag>;
        break;
      case 'TO_APPROVAL':
        tag = <Tag color="orange">{text}</Tag>;
        break;
      case 'COMPLETED':
        tag = <Tag color="green">{text}</Tag>;
        break;
      case 'REJECTED':
        tag = <Tag color="magenta">{text}</Tag>;
        break;
      case 'CANCEL':
        tag = <Tag color="gray">{text}</Tag>;
        break;
      default:
        tag=text;
    }
    return tag;
  };

  const columns: ColumnProps[] = [
    {
      name: 'applicationDocNum',
      renderer: ({ value,record }) => 
        <a onClick={()=>handleToDetails(record?.get('applyId'))}>{value}</a>,
      minWidth: 190,
    },
    {
      name: 'applicationDocStatus',
      renderer:({value,text})=>tags(value,text),
    },
    {
      name: 'modelSite',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'departmentUser',
    },
    {
      name: 'companyUnit',
    },
    {
      name: 'remark',
    },
    {
      name: 'reviewByName',
    },
    {
      name: 'reviewDate',
      minWidth: 150,
    },
    {
      name: 'rejectReason',
    },
  ];

  const lineColumn: ColumnProps[] = [
    {
      name: 'toolCodeObj',
      minWidth: 150,
    },
    {
      name: 'speciesName',
      minWidth: 150,
    },
    {
      name: 'modelCode',
      minWidth: 150,
    },
    {
      name: 'modelName',
      minWidth: 150,
    },
    {
      name: 'lastVerificationDate',
      minWidth: 150,
    },
    {
      name: 'usingStatus',
      minWidth: 150,
    },
    {
      name: 'verificationStatus',
      minWidth: 150,
    },
    {
      name: 'currentUserId',
      minWidth: 150,
      renderer: ({ record }) => {
        return record?.get('currentUserName')
      },
    },
    {
      name: 'currentUseDepartmentName',
      minWidth: 150,
    },
    {
      name: 'currentProdlineName',
      minWidth: 150,
    },
    {
      name: 'currentProcessName',
      minWidth: 150,
    },
    {
      name: 'otherPosition',
      minWidth: 150,
    },
    {
      name: 'reason',
      minWidth: 150,
    },
    {
      name: 'enclosureUuid',
      align: ColumnAlign.center,
      minWidth: 150,
    },
  ];

  const handleCreate = ()=>{
    history.push(`/hwms/measuring-scrap-platform/detail/create`);
  };

  const handleCancel = ()=>{
    const flag = tableDs.selected.filter(_record=>!['NEW','REJECTED'].includes(_record.get('applicationDocStatus'))).length;
    if(flag){
      return notification.warning({
        message: intl.get(`${modelPrompt}.message.status.notisNEW.REJECTED`).d('只有新建或驳回状态的申请单才可取消！'),
        description: '',
      }) 
    }
    const url = `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/cancel/ui`;
    Axios.put(url,tableDs.selected.map(item=>item.toData())).then(()=>{
      tableDs.query(tableDs.currentPage);
    }).catch((res: any) => {
      return notification.error({
        message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
        description: '',
      })
    })
  };

  const headerRowClick = (record)=>{
    Axios.get(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/list/details/ui`, {
      params: {
        'id': record?.get('applyId'),
      },
    }).then((res: any) => {
      const { qisMsToolChangeLineVOList } = res;
      lineDs.loadData(qisMsToolChangeLineVOList || []);
    }).catch((res: any) => {
      return notification.error({
        message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
        description: '',
      })
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('量具注销平台')}>
        <Button color={ButtonColor.primary} onClick={handleCreate}>
          {intl.get(`${modelPrompt}.button.newCreateApply`).d('新建申请')}
        </Button>
        <Button onClick={handleCancel} disabled={!tableDs.selected.length || tableDs.selected.some(record=>record.get('createdBy')!==getCurrentUserId())}>
          {intl.get(`${modelPrompt}.button.cancelApply`).d('取消申请单')}
        </Button>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.MEASURING_SCRAP_PLATFORM.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.MEASURING_SCRAP_PLATFORM.TABLE`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            onRow={({ record }) => ({
              onClick: () => headerRowClick(record),
            })}
            searchCode="MeasuringScrapPlatform"
            customizedCode="MeasuringScrapPlatform"
          />,
        )}
        <Table
          dataSet={lineDs}
          columns={lineColumn}
          queryBar={TableQueryBarType.none}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = listPageFactory();
      const lineDs = lineTableFactory();
      return {
        lineDs,
        tableDs,
      };
    },
    { cacheState: true },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.MEASURING_SCRAP_PLATFORM.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.MEASURING_SCRAP_PLATFORM.TABLE`],
    })(observer(HierarchicalAuditProject) as any),
  ),
);
