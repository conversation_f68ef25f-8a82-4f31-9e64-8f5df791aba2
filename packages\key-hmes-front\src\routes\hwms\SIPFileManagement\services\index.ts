/*
 * @Description: SIP文件管理-services
 * @Author: <<EMAIL>>
 * @Date: 2023-09-22 09:36:10
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-22 15:16:30
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const endUrl = "";

/**
 * 提交审批
 * @function ApprovalSIPFiles
 * @returns {object} fetch Promise
 */
export function ApprovalSIPFiles(): AxiosRequestConfig  {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sip-file/approvalBatch`,
    method: 'PUT',
  };
}

/**
 * 保存SIP文件
 * @function SaveSIPFile
 * @returns {object} fetch Promise
 */
export function SaveSIPFile(): AxiosRequestConfig {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sip-file`,
    method: 'POST',
  };
}

/**
 * 保存SIP文件
 * @function UpdateSIPFile
 * @returns {object} fetch Promise
 */
export function UpdateSIPFile(): AxiosRequestConfig {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sip-file/upgrade`,
    method: 'POST',
  };
}
