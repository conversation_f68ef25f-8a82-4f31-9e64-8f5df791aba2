import React, { useState } from "react";
import {<PERSON><PERSON>, Modal} from "choerodon-ui/pro";
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { API_HOST, BASIC } from '@utils/config';
import notification from 'utils/notification';
import myInstance from '@utils/myAxios';
import PrintElement from '@components/tarzan-ui/PrintButton'
import { getCurrentOrganizationId } from 'utils/utils';
import noData from './img/noData.png'
import styles from './index.module.less';

const tenantId = getCurrentOrganizationId();

const { Panel } = Collapse;

export default ({ modal, printArr, dataSet }) => {
  const [ selectItem, SetSelectedItem ] = useState()


  modal.handleOk(() => {
    // @ts-ignore
    if (selectItem.printChannel === 'H0') {
      // 平台打印
      openPdfModal()
    } else {
      // 初始化帆软
      initFRPlug();
      // 帆软打印
      setTimeout(() => {
        FRPrint();
      }, 1000)

    }
    modal.close();
    return false;
  });

  modal.handleCancel(() => {
    modal.close();
  });

  const FRPrint = () => {
    // @ts-ignore
    if (!window.FR) {
      notification.error({
        message: intl.get('tarzan.common.message.initFRPrintFailed').d('打印插件初始化失败'),
      });
      return;
    }
    const printurl = `${selectItem.printChannelPath}/decision/view/report`;

    let materialLotId = '';
    const select = dataSet.selected;
    // eslint-disable-next-line array-callback-return
    select.map((item, index) => {
      if (index === 0) {
        materialLotId = item.data.materialLotId;
      } else {
        materialLotId = `${materialLotId  },${  item.data.materialLotId}`;
      }
    });

    const _printParams = {
      reportlet: selectItem.printTemplateCode,
      materialLotId,
    };
    const reportlets = JSON.stringify([_printParams]);
    // @ts-ignore
    window.FR.doURLPrint({
      printUrl: printurl,
      isPopUp: false, // 是否弹出设置窗口 true/false 弹出/不弹出，零客户端打印不弹出，本地打印弹出
      data: {
        reportlets, // 需要打印的模板列表
      },
      printType: 0, // 打印类型，0为零客户端打印，1为本地打印，暂时默认客户端打印
      // 以下为零客户端打印的参数，仅当 printType 为 0 时生效
      ieQuietPrint: true, // IE静默打印设置 true为静默，false为不静默
      // 以下为本地打印的参数，仅当 printType 为 1 时生效
      printerName: 'Microsoft Print to PDF', // 打印机名
      pageType: 0, // 打印页码类型：0：所有页，1：当前页，2：指定页
      // pageIndex: '1-3', // 页码范围。当 pageType 为 2 时有效
      // copy: 1, // 打印份数
    });
  };

  const initFRPlug = () => {
    // @ts-ignore
    if (window.FR) {
      // 已注册过帆软打印插件，不再重复注册
      return;
    }
    const jsUrlList = [
      `${selectItem.printChannelPath}/decision/view/report?op=emb&resource=finereport.js`,
      `${selectItem.printChannelPath}/decision/view/report?op=resource&resource=/com/fr/web/core/js/socket.io.js`,
      `${selectItem.printChannelPath}/decision/view/report?op=resource&resource=/com/fr/web/core/js/jquery.watermark.js`,
    ];
    const cssUrl = `${selectItem.printChannelPath}/decision/view/report?op=emb&resource=finereport.css`;
    for (let i = 0; i < jsUrlList.length; i++) {
      const element = jsUrlList[i];
      const script = document.createElement('script');
      script.src = element;
      script.async = true;
      document.body.appendChild(script);
    }
    const link = document.createElement('link');
    link.href = cssUrl;
    link.rel = 'stylesheet';
    link.type = 'text/css';
    document.body.appendChild(link);
  };



  const openPdfModal = async () => {
    const select = dataSet.selected;
    console.log('dataSet',  dataSet.selected)
    if ((select || []).length === 0) {
      notification.warning({
        message: intl.get(`tarzan.common.info.lessOne`).d('请至少勾选一条数据！'),
      });
      return;
    }
    let materialLotId = '';
    // eslint-disable-next-line array-callback-return
    select.map((item, index) => {
      if (index === 0) {
        materialLotId = item.data.materialLotId;
      } else {
        materialLotId = `${materialLotId  },${  item.data.materialLotId}`;
      }
    });
    // @ts-ignore
    const printDomRef = React.createRef(null);
    const html = await getHtml(materialLotId);
    // @ts-ignore
    await Modal.open({
      drawer: true,
      maskClosable: true,
      key: 'ModalKey',
      destroyOnClose: true,
      closable: true,
      style: {
        width: 720,
      },
      title: intl.get('tarzan.common.title.preview').d('预览'),
      children: (
        <div
          id="pdf"
          style={{ paddingBottom: 20, overflow: 'hidden' }}
          ref={printDomRef}
          dangerouslySetInnerHTML={{ __html: html }}
        />
      ),
      footer: () => (
        <div>
          <Button
            icon="print"
            disabled={!select.length}
            onClick={() => {
              PrintElement({
                content: document.getElementById('pdf'),
              });
            }}
          >
            {intl.get('tarzan.common.button.print').d('打印')}
          </Button>
        </div>
      ),
    });
  };

  const getHtml = materialLotId => {
    if (!materialLotId) {
      return;
    }
    const url = `${API_HOST}${BASIC.HRPT_COMMON}/v1/${tenantId}/label-prints/view/html?labelTemplateCode=${selectItem.printTemplateCode}&tenantId=${tenantId}&labelTenantId=${tenantId}&materialLotId=${materialLotId}`;
    myInstance.get(url).then(res => {
      if (res.statusText === 'OK' && (res.data || {}).label) {
        // @ts-ignore
        document.getElementById('pdf').innerHTML = res.data.label.replace(/↵/gm, ''); // 去掉回车换行;
      } else {
        notification.error({
          message: res.data.message,
          description: '',
        });
      }
    });
  };

  const clickItem = (item) => {
    SetSelectedItem(item)
    console.log('item,', item)
    modal.update({
      okProps: { disabled: false },
    });
  }


  return (
    <div>
      {
        (printArr || []).map((item) => {
          return (
            <Collapse bordered={false} defaultActiveKey={[item.printTemplateGroup]}>
              <Panel key={item.printTemplateGroup} header={item.printTemplateGroup}>
                {
                  item.printTemplateList.map((listItem) => {
                    return (
                      <Button onClick={() => {clickItem(listItem)}}>{listItem.printTemplateCode}</Button>
                    )
                  })
                }
              </Panel>
            </Collapse>
          )
        })
      }{
        (printArr || []).length === 0 && (
          <div className={styles['nodata-contariner']}>
            <img src={noData} alt='nodata' />
            <p>{intl.get('tarzan.common.info.noData').d('暂无数据')}</p>
          </div>
        )
      }
    </div>
  );
};
