.container {
  height: 100%;
  padding: 0 12px;
  :global(.c7n-spin-nested-loading) {
    height: 100%;
  }
  :global(.c7n-spin-container) {
    height: 100%;
  }
  :global(.c7n-tabs) {
    display: grid;
    grid-template-rows: auto auto 1fr;
    height: 100%;
  }

  :global(.c7n-tabs-bar) {
    height: 30px;
    margin: 0;
  }
  :global(.c7n-tabs-nav-container) {
    height: 30px;
    padding: 0 !important;
  }
  :global(.c7n-tabs-content) {
    min-width: 0;
  }
  :global(.c7n-tabs-tab) {
    margin: 0 8px 0 0 !important;
    padding: 0 !important;
  }
}
.collapse-panel-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  min-width: 0;
}
.card-container {
  width: 160px;
  min-width: 120px;
  height: 72px;
  margin: 6px;
  padding: 6px 0 0 0;
  overflow: hidden;

  border-radius: 3px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.15);
  cursor: pointer;
  &-yellow {
    color: #f8ac3d;
    background-image: linear-gradient(157deg, #ffc670 0%, #f7a835 83%);
  }
  &-blue {
    color: #3889ff;
    background-image: linear-gradient(145deg, #69beff 0%, #3889ff 86%);
  }
  &-red {
    color: #ec4c4c;
    background-image: linear-gradient(145deg, #ff8787 0%, #ec4c4c 81%);
  }
  .card-content {
    position: relative;
    height: 100%;
    padding-top: 6px;
    background: #fff;
  }
  .card-title {
    display: flex;
    align-items: center;
    img {
      width: 20px;
      margin: 0 8px;
      color: inherit;
    }
    span {
      color: #333;
      font-weight: 400;
      font-size: 12px;
      font-family: PingFangSC-Regular, sans-serif;
    }
  }
  .card-value {
    margin-left: 40px;
    font-weight: 700;
    font-size: 24px;
    font-family: DINAlternate-Bold, sans-serif;
  }
}

.circle {
  border-radius: 50%;
  &-1 {
    position: absolute;
    right: 0;
    bottom: -23px;
    z-index: 3;
    width: 54px;
    height: 54px;
    opacity: 0.2;
    &-yellow {
      background-image: linear-gradient(-41deg, rgba(253, 220, 137, 0.06) 21%, #fab651 99%);
    }
    &-blue {
      background-image: linear-gradient(-41deg, rgba(70, 152, 255, 0) 21%, #4293ff 99%);
    }
    &-red {
      background-image: linear-gradient(-41deg, rgba(236, 76, 76, 0) 21%, #ec4c4c 99%);
    }
  }
  &-2 {
    position: absolute;
    right: -18px;
    bottom: 0;
    width: 40px;
    height: 40px;
    opacity: 0.15;
    &-yellow {
      background-image: linear-gradient(-12deg, rgba(253, 220, 137, 0.26) 23%, #fab651 95%);
    }
    &-blue {
      background-image: linear-gradient(-3deg, rgba(70, 152, 255, 0) 14%, #4293ff 100%);
    }
    &-red {
      background-image: linear-gradient(-12deg, rgba(236, 76, 76, 0) 23%, #ec4c4c 95%);
    }
  }
}

.chart-title {
  margin: 0;
  color: #505a70;
  font-weight: 400;
  font-size: 14px;
  font-family: PingFangSC-Regular, sans-serif;
  line-height: 22px;
}

.customize-collapse {
  height: 100%;
  :global(.c7n-collapse-item) {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
  }
  :global(.c7n-collapse-content-box) {
    display: grid;
    grid-template-rows: 120px auto 1fr;
    height: 100%;
    padding-bottom: 0;
  }
  :global(.c7n-collapse-header) {
    padding: 12px 0 4px 8px !important;
    &::before {
      top: calc(50% - 0.07rem + 4px) !important;
    }
  }
}

.collapse-header {
  width: fit-content;
}
