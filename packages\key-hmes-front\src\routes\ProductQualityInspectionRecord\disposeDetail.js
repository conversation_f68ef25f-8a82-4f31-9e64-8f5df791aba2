import React, { useMemo, useEffect } from 'react';
import { DataSet, Table, Modal } from 'choerodon-ui/pro';

import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { verifyObjectHasValue } from '@utils/utils';
import { detailDS, drawerDS } from './stores';

// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.DisposeDetail';

const DisposeDetail = observer(props => {
  const {
    history,
    location: { state },
  } = props;

  const detailDs = useMemo(() => new DataSet(detailDS()), []); // 复制ds
  const drawerDs = useMemo(() => new DataSet(drawerDS()), []); // 复制ds

  useEffect(() => {
    detailDs.setQueryParameter();
    // detailDs.setQueryParameter('ncRecordId', props.match.params.id);
    detailDs.setQueryParameter('identification', state.data.identification);
    detailDs.setQueryParameter('materialLotId', state.data.materialLotId);
    detailDs.setQueryParameter('ncRecordType', state.data.ncRecordType);
    detailDs.setQueryParameter('eoId', state.data.eoId);
    detailDs.query();
  }, [props.match.params.id]);

  const openNcDrawer = async record => {
    drawerDs.setQueryParameter('ncRecordId', record.data.ncRecordId);
    drawerDs.setQueryParameter('identification', record.data.identification);
    drawerDs.query();
    Modal.open({
      closable: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.ncRecordDetail`).d('不良记录明细'),
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table dataSet={drawerDs} columns={drawerColumns} style={{ height: 400 }} />,
    });
  };

  const drawerColumns = [
    {
      name: 'identification',
      width: 170,
    },
    {
      name: 'ncCode',
      width: 170,
    },
    {
      name: 'operationName',
      width: 150,
    },
    {
      name: 'ncStatusDesc',
      width: 150,
    },
    {
      name: 'ncRecordTime',
      width: 150,
    },
    {
      name: 'ncUserName',
      width: 150,
    },
    {
      name: 'lastUpdateTime',
      width: 150,
    },
  ];

  const columns = [
    {
      header: intl.get(`${modelPrompt}.ncRecord`).d('不良记录'),
      align: 'center',
      lock: 'left',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => {
                openNcDrawer(record);
              }}
            >
              {intl.get(`${modelPrompt}.detail`).d('明细')}
            </a>
          </span>
        );
      },
    },
    {
      name: 'ncRecordNum',
    },
    {
      name: 'identification',
      width: 200,
    },
    {
      name: 'operationName',
    },
    {
      name: 'workcellName',
    },
    {
      name: 'disposalFunctionDescription',
      width: 200,
    },
    {
      name: 'review',
      width: 200,
    },
    {
      name: 'disposalTime',
    },
    {
      name: 'realName',
    },
    {
      name: 'remark',
    },
  ];

  useDataSetEvent(detailDs, 'load', () => {
    if (detailDs.records.length) {
      detailDs.records.forEach(record => {
        record.init('identification', detailDs.getQueryParameter('identification'));
      });
    }
  });
  useDataSetEvent(drawerDs, 'load', () => {
    if (drawerDs.records.length) {
      drawerDs.records.forEach(record => {
        record.init('identification', drawerDs.getQueryParameter('identification'));
      });
    }
  });

  const headerOnBack = () => {
    history.push({
      pathname: `/hmes/product-quality-inspection-record/list`,
      state: verifyObjectHasValue(state.queryList) ? { ...state.queryList } : {},
    });
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title`).d('处置明细')}
        backPath="/hmes/product-quality-inspection-record/list"
        onBack={headerOnBack.bind(this)}
      ></Header>
      <Content>
        <Table
          customizedCode="DisposeDetail"
          dataSet={detailDs}
          columns={columns}
          style={{ height: 400 }}
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.DisposeDetail', 'tarzan.common'],
})(DisposeDetail);
