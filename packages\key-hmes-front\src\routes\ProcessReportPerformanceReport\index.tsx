import React, { useMemo, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import { isNil } from 'lodash';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from './stores';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const ProcessReportPerformanceReportList = observer(props => {
  const {
    // match: { path },
    tableDs,
    history,
  } = props;

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (tableDs) {
      const handlerQuery = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      // 查询条件更新时操作
      handlerQuery.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = async ({ name, record }) => {
    if (name === 'siteLov') {
      record.set('productionLine', null);
      record.set('productionLineId', null);
      record.set('stationWorkcellObj', null);
      record.set('workcellId', null);
      record.set('materialObj', null);
      record.set('materialId', null);
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'workOrderNum',
        width: 140,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hmes/workshop/production-order-mgt/detail/${record?.get('workOrderId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'prodLineCode' },
      { name: 'prodLineName' },
      { name: 'woStatus', width: 120 },
      { name: 'operationName' },
      { name: 'opDescription', width: 140 },
      { name: 'workcellCode' },
      { name: 'workcellName', width: 140 },
      { name: 'reportDate', width: 160 },
      { name: 'shiftDesc', width: 170 },
      { name: 'woQty', width: 120 },
      { name: 'qty' },
      { name: 'materialCodeRevision' },
      // { name: 'version' },
    ];
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('工序报工实绩报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/export/ui`}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          queryParams={getExportQueryParams}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={9}
          dataSet={tableDs}
          columns={columns}
          searchCode="processReportPerformance"
          customizedCode="processReportPerformance"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ProcessReportPerformanceReportList),
);
