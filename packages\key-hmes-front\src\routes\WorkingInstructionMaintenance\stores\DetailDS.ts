/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-24 09:50:50
 * @LastEditTime: 2023-07-26 10:22:26
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'sopCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sopCode`).d('工艺文件编码'),
      required: true,
    },
    {
      name: 'sopName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sopName`).d('工艺文件名称'),
    },
    {
      name: 'startDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startDate`).d('生效时间'),
      required: true,
    },
    {
      name: 'endDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endDate`).d('失效时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

const fileTableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  fields: [
    { name: 'sopFileId' },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('序号'),
    },
    { name: 'fileUrl' },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件'),
    },
    {
      name: 'exts',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.exts`).d('格式'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('上传时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('上传人'),
    },
  ],
});

export { detailDS, fileTableDS };
