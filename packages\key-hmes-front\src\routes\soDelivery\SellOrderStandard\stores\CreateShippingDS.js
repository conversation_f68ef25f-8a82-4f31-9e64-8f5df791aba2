/**
 * 生成送货单
 * @date 2023-7-5
 * <AUTHOR> <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.purchase.sellOrder';
const tenantId = getCurrentOrganizationId();

const shippingDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  fields: [
    // 基本属性
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('发货单号'),
      disabled: true,
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('发货单类型'),
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-instruction-doc/operation-type/limit/doc/type/list?operationType=SO_DELIVERY_DOC`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      name: 'expectedArrivalTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.arrivalDate`).d('计划发货/退货日期'),
      // defaultValue: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      name: 'customerId',
    },
    {
      name: 'customerSiteId',
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
      disabled: true,
    },
    {
      name: 'customerSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerSiteCode`).d('客户地点'),
      disabled: true,
    },

    {
      name: 'contactAddress',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactAddress`).d('送货地址'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'contactPerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactPerson`).d('收货人'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'contactTel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactTel`).d('收货人电话'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'contactFax',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactFax`).d('传真'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'logisticsMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logisticsMode`).d('物流方式'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'logisticsCompanyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logisticsCompanyCode`).d('物流公司编码'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'logisticsCompanyDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logisticsCompanyDesc`).d('物流公司名称'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'paymentType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paymentType`).d('付款方式'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

const shippingListDS = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'poLineId',
  autoLocateFirst: false,
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNum`).d('行号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'manageMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manageMode`).d('管理模式'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'targetSiteId',
      type: FieldType.number,
    },
    {
      name: 'targetSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shipFromSiteCode`).d('站点'),
    },
    {
      name: 'needQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.needQty`).d('需求数量'),
    },
    {
      name: 'processedOrdered',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.processedOrdered`).d('已制单数量'),
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.productionQuantity`).d('制单数量'),
      required: true,
      validator: (...args) => {
        const {
          data: { quantity },
        } = args[2];
        if (quantity === 0) {
          return intl
            .get(`hzero.c7nProUI.Validator.range_overflow_min`, {
              label: intl.get(`${modelPrompt}.productionQuantity`).d('制单数量'),
              max: 0,
            })
            .d(`制单数量必须大于0。`);
        }
      },
      dynamicProps: {
        max: ({ record }) => {
          return record?.get('needQty')-record?.get('processedOrdered');
        },
      },
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.receiveWarehouse`).d('发运/接收仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('targetSiteId'),
            businessTypes: record.get('businessType'),
            queryType: 'TARGET',
            locatorCategories: 'AREA',
          };
        },
        required: ({ dataSet }) => {
          return (
            (dataSet.getState('instructionDocTypeTag') === 'RETURN' &&
              dataSet.getState('toLocatorRequiredFlag') === 'Y') ||
            (dataSet.getState('instructionDocTypeTag') === 'DELIVERY' &&
              dataSet.getState('fromLocatorRequiredFlag') === 'Y')
          );
        },
      },
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'targetLocatorId',
      type: FieldType.string,
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'orderFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderFlag`).d('按单标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNum`).d('销售订单'),
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行'),
    },
    {
      name: 'soId',
      type: FieldType.number,
    },
    {
      name: 'soLineId',
      type: FieldType.number,
    },

    {
      name: 'toleranceFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'toleranceType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('toleranceFlag') === 'Y';
        },
        disabled: ({ record }) => {
          return record.get('toleranceFlag') !== 'Y';
        },
      },
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差'),
      min: 0,
      dynamicProps: {
        required: ({ record }) => {
          return (
            record.get('toleranceFlag') === 'Y' &&
            (record.get('toleranceType') === 'PERCENTAGE' ||
              record.get('toleranceType') === 'NUMBER')
          );
        },
        max: ({ record }) => {
          if (record.get('toleranceType') === 'PERCENTAGE') {
            return 100;
          }
        },
        disabled: ({ record }) => {
          return (
            record.get('toleranceFlag') === 'N' ||
            (record.get('toleranceType') !== 'PERCENTAGE' &&
              record.get('toleranceType') !== 'NUMBER')
          );
        },
      },
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差'),
      min: 0,
      dynamicProps: {
        required: ({ record }) => {
          return (
            record.get('toleranceFlag') === 'Y' &&
            (record.get('toleranceType') === 'PERCENTAGE' ||
              record.get('toleranceType') === 'NUMBER')
          );
        },
        disabled: ({ record }) => {
          return (
            record.get('toleranceFlag') === 'N' ||
            (record.get('toleranceType') !== 'PERCENTAGE' &&
              record.get('toleranceType') !== 'NUMBER')
          );
        },
        max: ({ record }) => {
          if (record.get('toleranceType') === 'PERCENTAGE') {
            return 100;
          }
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineRemark`).d('行备注'),
    },
    {
      name: 'fromLocatorRequiredFlag',
    },
    {
      name: 'toLocatorRequiredFlag',
    },
    {
      name: 'instructionDocTypeTag',
    },
  ],
});

export { shippingDS, shippingListDS };
