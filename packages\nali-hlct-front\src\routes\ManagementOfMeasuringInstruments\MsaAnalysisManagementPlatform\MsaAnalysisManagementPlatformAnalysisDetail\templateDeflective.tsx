import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import uuid from 'uuid/v4';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import myInstance from '@utils/myAxios';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { formDS } from '../stores/deflectiveDS';
import { analyseResultDS } from '../stores/DetailDS';
import DeflectiveGraphic from '../templateComponents/DeflectiveGraphic';
import './index.modules.less';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';

interface GraphicDataProps {
  xBarChartRule: object; // xBar图rules
  rChartRule: object; // r图rules
  data: []; // xBar-r图数据
  biasHistogramChartInfo: object; // 直方图数据
  biasTableInfo: object; // 表格数据
  msaResult: string; // 分析结果
  msaConclusion: string; // 分析结论
  tableInfo: [];
}

// 默认数据
const apiDataDefault = [
  {
    measureDate: '',
    measureTableList: [
      {
        measureDataColumn: 1,
        measureDataRow: 1,
        measureDataValue: '',
      },
    ],
    range: '',
    average: '',
  },
];

const Deflective = props => {
  const { msaStatus, currentUserFlag, detailDs, checkLocation } = props;
  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
      }),
    [],
  );
  // 分析结果DS
  const analyseResultDs = useMemo(() => new DataSet(analyseResultDS()), []);
  const [graphicData, setGraphicData] = useState<GraphicDataProps>({
    xBarChartRule: {}, // xBar图rules
    rChartRule: {}, // r图rules
    data: [], // xBar-r图数据
    biasHistogramChartInfo: {}, // 直方图数据
    biasTableInfo: {}, // 表格数据
    msaResult: '', // 分析结果
    msaConclusion: '', // 分析结论
    tableInfo: [],
  });

  const templateData = [
    {
      measureDate: intl.get(`${modelPrompt}.measuringTime`).d('测量时间'),
      uuid: uuid(),
      type: 'measureDate',
    },
    {
      measureDataRow: 1,
      measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
      uuid: uuid(),
      type: 'measureDataValue',
    },
    {
      measureDate: intl.get(`${modelPrompt}.meanValue`).d('平均值'),
      uuid: uuid(),
      type: 'average',
    },
    {
      measureDate: intl.get(`${modelPrompt}.range`).d('极差'),
      uuid: uuid(),
      type: 'range',
    },
  ];

  const [apiData, setApiData] = useState([]);
  const [defaultData, setDefaultData] = useState(templateData); // 有默认数据时初始化
  const [havaSaveData, setHaveSaveData] = useState(false);

  const [defaultField, setDefaultField] = useState([
    {
      name: 'measureDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subGroup`).d('子组号'),
    },
    {
      name: 'measureDataRow',
      type: FieldType.string,
    },
    {
      name: 'measureDataColumn1',
      computedProps: {
        type: ({ record }) => {
          if (record.get('type') === 'measureDate') {
            return FieldType.dateTime;
          }
          return FieldType.number;
        },
        disabled: ({ record }) => {
          if (record.get('type') === 'average' || record.get('type') === 'range') {
            return true;
          }
        },
        required: ({ record }) => {
          return record.get('type') === 'measureDataValue';
        },
      },
    },
  ]);

  const userDs = useMemo(
    () =>
      new DataSet({
        forceValidate: true,
        autoCreate: false,
        selection: false,
        paging: false,
        primaryKey: 'uuid',
        data: defaultData,
        fields: defaultField,
      }),
    [],
  );

  const [currentColumns, setCurrentColumns] = useState([
    {
      name: 'measureDataRow',
      width: 30,
      lock: ColumnLock.left,
      align: ColumnAlign.center,
      renderer: ({ value }) => {
        if (value) {
          return (
            <>
              <span>{value}</span>
            </>
          );
        }
      },
    },
    {
      name: 'measureDataColumn1',
      editor: record => {
        return !(record.get('type') === 'average' || record.get('type') === 'range');
      },
      align: ColumnAlign.center,
      header: () => (
        <>
          <span>1</span>
        </>
      ),
    },
  ]); // 实际columns

  useEffect(() => {
    myInstance
      .get(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/info/ui?msaTaskLineId=${props.msaTaskLineId}`,
      )
      .then(res => {
        if (res.data.success) {
          // 处理数据
          const {
            standardValue,
            expectedDeterioration,
            sampleDescription,
            measuredBy,
            measuredByName,
          } = res.data.rows;
          if (standardValue) formDs.current?.set('standardValue', standardValue);
          if (expectedDeterioration)
            formDs.current?.set('expectedDeterioration', expectedDeterioration);
          if (sampleDescription) formDs.current?.set('sampleDescription', sampleDescription);
          if (measuredBy) {
            formDs.current?.set('improveByObj', {
              userId: measuredBy,
              realName: measuredByName,
            });
          }
          if ((res.data.rows.tableInfo || []).length) {
            setApiData(res.data.rows.tableInfo);
            setHaveSaveData(true);
          } else {
            // @ts-ignore
            setApiData(apiDataDefault);
            setHaveSaveData(false);
          }
        } else {
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          });
        }
        handleUpdateChartData(res.data.rows || {});
      });
  }, []);

  useEffect(() => {
    const colums = apiData.length; // 总共有多少列数据
    if (colums > 0) {
      // @ts-ignore
      const rows = apiData[0].measureTableList.length; // 测量数据共有多少行
      // 先生成，时间，极差，平均值的数据
      let transformDataResult = [];
      const timeObj = {
        measureDate: intl.get(`${modelPrompt}.measuringTime`).d('测量时间'),
        uuid: uuid(),
        type: 'measureDate',
      };
      const rangeObj = {
        measureDate: intl.get(`${modelPrompt}.range`).d('极差'),
        uuid: uuid(),
        type: 'range',
      };
      const averageObj = {
        measureDate: intl.get(`${modelPrompt}.meanValue`).d('平均值'),
        uuid: uuid(),
        type: 'average',
      };
      let tiledData: any = [];

      apiData.forEach((item: any) => {
        timeObj[`measureDataColumn${item.measureTableList[0].measureDataColumn}`] =
          item.measureDate;
        rangeObj[`measureDataColumn${item.measureTableList[0].measureDataColumn}`] = item.range;
        averageObj[`measureDataColumn${item.measureTableList[0].measureDataColumn}`] = item.average;
        tiledData = [...tiledData, ...item.measureTableList];
      });
      // @ts-ignore
      transformDataResult = [timeObj];

      // 生成测量值数据
      for (let rowSeq = 0; rowSeq < rows; rowSeq++) {
        const obj = {
          measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
          uuid: uuid(),
          type: 'measureDataValue',
          measureDataRow: String(rowSeq + 1),
        };
        for (let colSeq = 0; colSeq < colums; colSeq++) {
          tiledData.forEach((item: any) => {
            if (item.measureDataRow === rowSeq + 1 && item.measureDataColumn === colSeq + 1) {
              obj[`measureDataColumn${colSeq + 1}`] = item.measureDataValue;
            }
          });
        }
        // @ts-ignore
        transformDataResult.push(obj);
      }
      // @ts-ignore
      transformDataResult = [...transformDataResult, rangeObj, averageObj];

      // 生产行列配置
      const feild = [
        {
          name: 'measureDate',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.subGroup`).d('子组号'),
        },
        {
          name: 'measureDataRow',
          type: FieldType.string,
        },
      ];

      const column = [
        {
          name: 'measureDataRow',
          width: 25,
          lock: ColumnLock.left,
          align: ColumnAlign.center,
          renderer: ({ value }) => {
            if (value) {
              return (
                <>
                  <span>{value}</span>
                </>
              );
            }
          },
        },
      ];
      for (let colSeq = 0; colSeq < colums; colSeq++) {
        feild.push({
          name: `measureDataColumn${colSeq + 1}`,
          // @ts-ignore
          computedProps: {
            type: ({ record }) => {
              if (record.get('type') === 'measureDate') {
                return FieldType.dateTime;
              }
              return FieldType.number;
            },
            disabled: ({ record }) => {
              if (record.get('type') === 'average' || record.get('type') === 'range') {
                return true;
              }
            },
            required: ({ record }) => {
              return record.get('type') === 'measureDataValue';
            },
          },
        });
        column.push({
          name: `measureDataColumn${colSeq + 1}`,
          width: colums > 6 && 70,
          // @ts-ignore
          editor: record => {
            if (['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag) {
              return false;
            }
            return !(record.get('type') === 'average' || record.get('type') === 'range');
          },
          align: ColumnAlign.center,
          // width: 70,
          // @ts-ignore
          header: () => (
            <>
              <span>{colSeq + 1}</span>
            </>
          ),
          // renderer: ({ record, value }) => {
          //   if (record.get('type') === 'measureDate') {
          //     if (value) {
          //       const arr = moment(value).format('YYYY-MM-DD HH:mm:ss').toString().split(" ")
          //       return `${arr[0]} \n ${arr[1]} `
          //     }
          //   } else {
          //     return value;
          //   }
          // },
        });
      }
      setDefaultField(feild);
      feild.forEach((item: any) => {
        userDs.addField(item.name, {
          ...item,
        });
      });
      setCurrentColumns(column);
      setDefaultData(transformDataResult);
      userDs.loadData(transformDataResult);
    }
  }, [apiData, havaSaveData]);

  const handleUpdateChartData = async dataSource => {
    const {
      tableInfo = [],
      xBarChartRule = {},
      rChartRule = {},
      data = [],
      biasHistogramChartInfo = {},
      biasTableInfo = {},
      msaResult = '',
      msaConclusion = '',
    } = dataSource;
    setGraphicData({
      tableInfo,
      xBarChartRule, // xBar图rules
      rChartRule, // r图rules
      data, // xBar-r图数据
      biasHistogramChartInfo, // 直方图数据
      biasTableInfo, // 表格数据
      msaResult, // 分析结果
      msaConclusion, // 分析结论
    });
    await analyseResultDs.loadData([{
      msaResult,
      msaConclusion,
    }])
  };

  const groups = [
    {
      name: 'measureDate',
      align: ColumnAlign.center,
      parentField: undefined,
      type: 'column',
      lock: ColumnLock.left,
      columnProps: {
        // width: 70,
        header: () => (
          <>
            <span>{intl.get(`${modelPrompt}.subGroup`).d('子组号')}</span>
          </>
        ),
        renderer: ({ text }) => (
          <>
            <span>{text}</span>
          </>
        ),
      },
    },
  ];

  return (
    <div>
      <br/>
      <br/>
      <div className="pdf-page-height">
        <img
          src="data:image/png;base64,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"
          alt="logo"
          width="120"
          height="27"
          loading="eager"
          decoding="sync"
        />
        <div>
          <h3>{intl.get(`${modelPrompt}.title.deflectiveReport`).d('测量系统偏倚性分析报告')}</h3>
        </div>
        <br/>
        <p>
          <span>
            {intl.get(`${modelPrompt}.reportLabel.msaCode`).d('MSA编号')}：
            {detailDs.current?.get('msaCode')}
          </span>
          <span>
            {intl.get(`${modelPrompt}.reportLabel.msaType`).d('MSA类型')}：
            {detailDs.current?.get('msaTypeDesc')}
          </span>
        </p>
        <table>
          <tbody>
          <tr>
            <td>{intl.get(`${modelPrompt}.reportLabel.toolCode`).d('量具编号')}</td>
            <td>{detailDs.current?.get('toolCode')}</td>
            <td>{intl.get(`${modelPrompt}.reportLabel.modelName`).d('量具名称')}</td>
            <td>{detailDs.current?.get('modelName')}</td>
            <td>{intl.get(`${modelPrompt}.reportLabel.creationDate`).d('日期')}</td>
            <td>{detailDs.current?.get('creationDate')}</td>
          </tr>
          <tr>
            <td>{intl.get(`${modelPrompt}.reportLabel.modelCode`).d('量具型号')}</td>
            <td>{detailDs.current?.get('modelCode')}</td>
            <td>{intl.get(`${modelPrompt}.reportLabel.modelRange`).d('量具量程')}</td>
            <td>{detailDs.current?.get('modelRange')}</td>
            <td>{intl.get(`${modelPrompt}.reportLabel.analyzedByDesc`).d('分析人')}</td>
            <td>{detailDs.current?.get('analyzedByDesc')}</td>
          </tr>
          <tr>
            <td>{intl.get(`${modelPrompt}.reportLabel.TemplateStability`).d('基准件名称')}</td>
            <td>{formDs.current?.get('sampleDescription')}</td>
            <td>{intl.get(`${modelPrompt}.reportLabel.qualityCharacteristic`).d('质量特性')}</td>
            <td>{detailDs.current?.get('qualityCharacteristic')}</td>
            <td>{intl.get(`${modelPrompt}.reportLabel.msaResultDesc`).d('测量人')}</td>
            <td>{formDs.current?.get('measuredByName')}</td>
          </tr>
          <tr>
            <td>
              {intl.get(`${modelPrompt}.reportLabel.expectedDeterioration`).d('预期过程变差')}
            </td>
            <td>{formDs.current?.get('expectedDeterioration')}</td>
            <td>{intl.get(`${modelPrompt}.reportLabel.standardValue`).d('基准值')}</td>
            <td>{formDs.current?.get('standardValue')}</td>
            <td>{intl.get(`${modelPrompt}.reportLabel.checkLocation`).d('测量位置')}</td>
            <td>{checkLocation}</td>
          </tr>
          </tbody>
        </table>
      </div>
      <div className="containerDataBorder">
        <br/>
        <h4>{intl.get(`${modelPrompt}.label.remark`).d('备注:')}</h4>
        <span style={{float: 'left'}}>{detailDs.current?.get('remark')}</span>
        <br/>
      </div>
      <div className="containerDataBorder">
        <br/>
        <h4>{intl.get(`${modelPrompt}.reportLabel.measureData`).d('测量数据')}</h4>
      </div>
      <div className="containerDataBorder">
        <Table
          columnDraggable
          columnTitleEditable
          aggregation={false}
          border
          dataSet={userDs}
          columns={currentColumns as any}
          groups={groups as any}
        />
      </div>
      <DeflectiveGraphic dataSoure={graphicData} analyseResultDs={analyseResultDs}/>
    </div>
  );
};

export default Deflective;
