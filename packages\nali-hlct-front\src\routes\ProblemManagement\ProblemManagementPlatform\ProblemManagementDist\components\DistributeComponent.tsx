/**
 * @Description: 问题管理平台-分配责任人页面组件
 * @Author: <EMAIL>
 * @Date: 2023/7/6 10:59
 */
import React, { useMemo, useState } from 'react';
import { Badge, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button, Switch, Table, Lov, Form, TextField, NumberField, Select } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnAlign } from 'choerodon-ui/pro/es/table/enum';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { observer } from 'mobx-react';
import notification from 'utils/notification';
import styles from '../index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';

const DistributeComponent = ({
  dataSet,
  supplierInfoDs,
  userRole,
  problemStatus,
  srmFlag,
  handleSaveCategoryDetail,
  handleSubmitProblemInfo,
  handleSaveSupplierInfo,
}) => {

  const handleChangePrincipalPersonFlag = (val, record) => {
    if (val === 'N') {
      return;
    }
    const principalLines = dataSet.filter(_record => _record.get('principalPersonFlag') === 'Y');
    if (principalLines && principalLines?.length > 1) {
      record.set('principalPersonFlag', 'N');
      notification.error({
        message: intl
          .get(`${modelPrompt}.validation.onePrincipalCanExist`)
          .d('同时只能存在一个主责人,请检查！'),
      });
    }
  };

  const handleChangePerson = (value, record) => {
    if (!value) {
      return;
    }
    const repeatFlag = dataSet.find(
      _record => _record?.id !== record.id && value?.id === _record?.get('responsiblePerson'),
    );
    if (repeatFlag) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.validation.responsiblePersonRepeat`)
          .d('当前选择的责任人已存在,请检查！'),
      });
      record.init('responsiblePersonLov', undefined);
    }
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      { name: 'sequence', align: ColumnAlign.left },
      {
        name: 'responsiblePersonLov',
        editor: record =>
          (userRole.includes('LEAD_PERSON') || userRole.includes('MAJOR_RESPONSIBLE_PERSON')) &&
          ['RELEASED', 'PUBLISH', 'FOLLOWING'].includes(problemStatus) &&
          record.dataSet.getState('canEdit') && (
            <Lov onChange={value => handleChangePerson(value, record)} />
          ),
      },
      { name: 'unitCompanyName', },
      { name: 'positionName' },
      { name: 'phone' },
      {
        name: 'principalPersonFlag',
        editor: record =>
          userRole.includes('LEAD_PERSON') &&
          ['RELEASED', 'PUBLISH', 'FOLLOWING'].includes(problemStatus) &&
          record.dataSet.getState('canEdit') && (
            <Switch onChange={value => handleChangePrincipalPersonFlag(value, record)} />
          ),
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
    ],
    [userRole?.length, problemStatus],
  );

  const handleCancel = dataSet => {
    dataSet.reset();
    dataSet.setState('canEdit', false);
  };

  const onHandleEdit = () => {
    const flag = dataSet.toData().some(item => item.principalPersonFlag === 'Y')
    dataSet.setState('canEdit', true);
    dataSet.setState('principalFlag', flag);
  }

  const RenderButtonGroup = observer(({ dataSet, userRole, problemStatus }) => {
    if (dataSet.getState('canEdit')) {
      return (
        <>
          <Button
            icon="delete"
            funcType={FuncType.flat}
            color={ButtonColor.red}
            disabled={!dataSet.selected.length}
            onClick={() => dataSet.remove(dataSet.selected)}
          >
            {intl.get(`${modelPrompt}.button.delete`).d('删除')}
          </Button>
          <Button
            icon="playlist_add"
            funcType={FuncType.flat}
            onClick={() => dataSet.create({}, 0)}
          >
            {intl.get(`${modelPrompt}.button.add`).d('新增')}
          </Button>
          <Button icon="close" funcType={FuncType.flat} onClick={() => handleCancel(dataSet)}>
            {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
          </Button>
          <Button
            icon="save"
            funcType={FuncType.flat}
            onClick={() => handleSaveCategoryDetail('lead')}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </Button>
        </>
      );
    }
    return (
      <>
        <Button
          icon="send-o"
          funcType={FuncType.flat}
          disabled={problemStatus !== 'PUBLISH' || !userRole.includes('LEAD_PERSON')}
          onClick={() => handleSubmitProblemInfo('lead')}
        >
          {intl.get(`${modelPrompt}.button.submit`).d('提交')}
        </Button>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={!['RELEASED', 'PUBLISH', 'FOLLOWING'].includes(problemStatus) || !(userRole.includes('LEAD_PERSON') || userRole.includes('MAJOR_RESPONSIBLE_PERSON'))}
          onClick={onHandleEdit}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </Button>
      </>
    );
  });

  const RenderSupplierButtonGroup = observer(({ dataSet, userRole, problemStatus, srmFlag }) => {
    if (dataSet.getState('canEdit')) {
      return (
        <>
          <Button icon="close" funcType={FuncType.flat} onClick={() => handleCancel(dataSet)}>
            {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
          </Button>
          <Button
            icon="save"
            funcType={FuncType.flat}
            onClick={() => handleSaveSupplierInfo(dataSet)}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </Button>
        </>
      );
    }
    return (
      <>
        <PermissionButton
          type="c7n-pro"
          icon="send-o"
          permissionList={[
            {
              code: `${modelPrompt}.dist.button.supplierSubmit`,
              type: 'button',
              meaning: '详情界面-责任供应商提交按钮',
            },
          ]}
          funcType={FuncType.flat}
          disabled={!['PUBLISH', 'RELEASED'].includes(problemStatus) || !userRole.includes('RESPONSIBLE_PERSON') || !srmFlag}
          onClick={() => handleSubmitProblemInfo('supplierInfo')}
        >
          {intl.get(`${modelPrompt}.button.pushSrm`).d('推送SRM')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="edit-o"
          permissionList={[
            {
              code: `${modelPrompt}.dist.button.supplierEdit`,
              type: 'button',
              meaning: '详情界面-责任供应商编辑按钮',
            },
          ]}
          funcType={FuncType.flat}
          disabled={!['PUBLISH', 'RELEASED'].includes(problemStatus) || !userRole.includes('RESPONSIBLE_PERSON') || !srmFlag}
          onClick={() => {
            dataSet.setState('canEdit', true);
          }}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </PermissionButton>
      </>
    );
  });

  const handleResetSupplierLot = () => {
    supplierInfoDs.current?.set('supplierLotLov', undefined);
  };

  return (
    <Collapse
      bordered={false}
      defaultActiveKey={['group', 'dutySupplier']}
      collapsible="icon"
      className={styles['collapse-style']}
    >
      <Panel
        key="group"
        header={intl.get(`${modelPrompt}.title.group`).d('责任小组')}
        extra={
          <RenderButtonGroup dataSet={dataSet} userRole={userRole} problemStatus={problemStatus} />
        }
      >
        <Table dataSet={dataSet} columns={columns} />
      </Panel>
      <Panel
        key="dutySupplier"
        header={intl.get(`${modelPrompt}.title.dutySupplier`).d('责任供应商')}
        extra={
          <RenderSupplierButtonGroup dataSet={supplierInfoDs} userRole={userRole} problemStatus={problemStatus} srmFlag={srmFlag} />
        }
      >
        <Form dataSet={supplierInfoDs} columns={3}>
          <Lov name="supplierLov" onChange={() => handleResetSupplierLot()} />
          <Lov name="materialLov" onChange={() => handleResetSupplierLot()} />
          <TextField name="materialName" />
          <NumberField name="reasonNcQty" />
          <Lov name="supplierLotLov" />
          <Select name="ppmFlag" />
        </Form>
      </Panel>
    </Collapse>
  );
};
export default DistributeComponent;
