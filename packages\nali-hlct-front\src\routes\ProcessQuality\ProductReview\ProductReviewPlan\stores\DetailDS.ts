/*
 * @Description: 产品审核计划-详情页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-10-09 14:16:00
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-10 16:58:43
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.productReview.productReviewPlan';
const tenantId = getCurrentOrganizationId();
const endUrl = "";

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'productRevPlanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanCode`).d('产品审核计划编码'),
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      textField: 'siteName',
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'productRevPlanDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanDesc`).d('计划描述'),
      required: true,
    },
    {
      name: 'revYear',
      type: FieldType.year,
      label: intl.get(`${modelPrompt}.revYear`).d('年份'),
      required: true,
    },
    {
      name: 'productRevPlanStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_PLAN_STATUS',
      lovPara: { tenantId },
      defaultValue: 'NEW',
      disabled: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      disabled: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-product-rev-plan/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          return {
            ...rows?.headerInfo,
            lines: rows?.lineInfos,
            tasks: rows?.taskInfos,
          };
        },
      };
    },
  },
});

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) =>
          !dataSet.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工序编码'),
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      computedProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
          workcellType: 'PROCESS',
        }),
        disabled: ({ dataSet }) =>
          !dataSet.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'workcellId',
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'workcellCode',
      bind: 'workcellLov.workcellCode',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序描述'),
      bind: 'workcellLov.workcellName',
    },
    {
      name: 'cusMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件编码'),
    },
    {
      name: 'cusMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialName`).d('客户零件描述'),
    },
    {
      name: 'reviewMonth',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewMonth`).d('审核时间'),
      required: true,
    },
    {
      name: 'reviewLeaderLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reviewLeader`).d('审核组长'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      required: true,
      lovPara: { tenantId },
    },
    {
      name: 'reviewLeader',
      bind: 'reviewLeaderLov.id',
    },
    {
      name: 'reviewLeaderName',
      bind: 'reviewLeaderLov.realName',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'executeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.executeStatus`).d('执行状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_PLAN_EXECUTE_STATUS',
      lovPara: { tenantId },
      defaultValue: 'NOT_STARTED',
    },
  ],
});

const reviewTaskDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'productRevTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskCode`).d('产品审核任务编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工序编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序描述'),
    },
    {
      name: 'productRevTaskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskStatus`).d('审核任务状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'reviewResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewResult`).d('审核结果'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_RESULT',
      lovPara: { tenantId },
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('任务创建时间'),
    },
  ],
});

export { detailDS, tableDS, reviewTaskDS };
