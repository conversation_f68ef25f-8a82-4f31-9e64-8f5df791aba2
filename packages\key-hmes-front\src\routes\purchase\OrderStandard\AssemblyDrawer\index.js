/**
 * @Description: 创建送货单抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-12-13 10:31:13
 * @LastEditTime: 2023-05-29 18:13:20
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { AttributeDrawer } from '@components/tarzan-ui';
import { Badge } from 'choerodon-ui';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { assemblyDS } from '../stores/AssemblyDS';

const modelPrompt = 'tarzan.hmes.purchase.order';

const AssemblyDrawer = props => {
  const { poLineId, siteId } = props.record;
  const assemblyDs = useMemo(() => {
    return new DataSet(assemblyDS());
  }, []);

  useEffect(() => {
    assemblyDs.queryParameter = {
      poLineId,
      siteId,
    };
    assemblyDs.query();
  }, [poLineId, siteId]);

  // 组件息表配置
  const subColumns = [
    {
      name: 'materialCode',
      title: intl.get(`${modelPrompt}.material`).d('物料'),
      width: 180,
    },
    {
      name: 'materialName',
      title: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      title: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
      width: 80,
    },
    {
      name: 'materialTypeName',
      title: intl.get(`${modelPrompt}.materialTypeName`).d('物料类别描述'),
      width: 160,
    },
    {
      name: 'uomCode',
      title: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      width: 60,
    },
    {
      name: 'componentQty',
      title: intl.get(`${modelPrompt}.componentQty`).d('组件用量'),
      align: 'right',
      width: 80,
    },
    {
      name: 'shipQty',
      title: intl.get(`${modelPrompt}.shipQty`).d('已发料数量'),
      align: 'right',
      width: 80,
    },
    {
      name: 'remaiInventoryQty',
      title: intl.get(`${modelPrompt}.remaiInventoryQty`).d('供应商剩余库存'),
      width: 120,
    },
    {
      name: 'deleteFlag',
      title: intl.get(`${modelPrompt}.deleteFlag`).d('删除标识'),
      width: 80,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'locatorCode',
      title: intl.get(`${modelPrompt}.sourceLocator`).d('来源库位'),
      width: 80,
    },
    {
      name: 'attrColumn',
      fixed: 'right',
      lock: 'right',
      width: 100,
      align: 'center',
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => {
        return record.data && record.data.poComponentId ? (
          <AttributeDrawer
            type="text"
            className="org.tarzan.mes.domain.entity.MtPoComponent"
            canEdit={false}
            kid={record.data.poComponentId}
            serverCode={BASIC.HMES_BASIC}
          />
        ) : (
          ''
        );
      },
    },
  ];
  return props.customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_COMPONENT.QUERY`,
    },
    <Table dataSet={assemblyDs} columns={subColumns} />,
  )
};

export default AssemblyDrawer;
