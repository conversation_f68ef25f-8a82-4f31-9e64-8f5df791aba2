/**
 * @Description: SA/专项检证平台-主界面DS
 * @Author: <EMAIL>
 * @Date: 2023/8/7 11:19
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.inspectExecute.saVerificationPlatform';
const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'verificationId',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'verificationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationCode`).d('检证项目编码'),
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('状态'),
      lookupCode: 'YP.QIS.SA_VERIFICATION_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'verificationFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationFrom`).d('检证来源'),
      lookupCode: 'YP.QIS.VERIFICATION_FROM',
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
    },
    {
      name: 'createMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('检证项目来源'),
      lookupCode: 'YP.QIS.VERIFICATION_CREATE_METHOD',
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
      lookupCode: 'YP.QIS.PRODUCT_TYPE',
    },
    {
      name: 'sourceTempLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceTempCode`).d('检证模板编码'),
      lovCode: 'YP.QIS.VERIFICATION_TEMP_CODE',
      ignore: FieldIgnore.always,
      textField: 'verificationCode',
      lovPara: { tenantId },
    },
    {
      name: 'sourceTempId',
      bind: 'sourceTempLov.verificationId',
    },
    {
      name: 'failureMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureMode`).d('失效模式'),
      lookupCode: 'YP.QIS.FAILURE_MODE',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialName',
      lovPara: { tenantId },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'itemGroup',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemGroup`).d('产品对象'),
      lookupCode: 'YP.QMS.ITEM_GROUP',
    },
    {
      name: 'verificationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationType`).d('检证类型'),
      lookupCode: 'YP.QIS.VERIFICATION_TYPE',
    },
    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检证阶段'),
      lookupCode: 'YP.QIS.VERIFICATION_PERIOD',
    },
    {
      name: 'problemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tempProblemCode`).d('关联问题编号'),
      lovCode: 'YP.QIS.PROBLEM_LIST',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'problemId',
      bind: 'problemLov.problemId',
    },
    {
      name: 'problemCode',
      bind: 'problemLov.problemCode',
    },
    {
      name: 'createPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createPerson`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'createPersonLov.id',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
  ],
  fields: [
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'verificationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationCode`).d('检证项目编码'),
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('状态'),
      lookupCode: 'YP.QIS.SA_VERIFICATION_STATUS',
    },
    {
      name: 'createMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('检证项目来源'),
      lookupCode: 'YP.QIS.VERIFICATION_CREATE_METHOD',
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
      lookupCode: 'YP.QIS.PRODUCT_TYPE',
    },
    {
      name: 'sourceTempCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceTempCode`).d('检证模板编码'),
    },
    {
      name: 'verificationFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationFrom`).d('检证来源'),
      lookupCode: 'YP.QIS.VERIFICATION_FROM',
    },
    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检证阶段'),
      lookupCode: 'YP.QIS.VERIFICATION_PERIOD',
    },
    {
      name: 'verificationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationType`).d('检证类型'),
      lookupCode: 'YP.QIS.VERIFICATION_TYPE',
    },
    {
      name: 'failureMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureMode`).d('失效模式'),
      lookupCode: 'YP.QIS.FAILURE_MODE',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'itemGroup',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemGroup`).d('产品对象'),
      lookupCode: 'YP.QMS.ITEM_GROUP',
    },
    {
      name: 'problemId',
      type: FieldType.number,
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建日期'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-platform/header/ui`,
        method: 'GET',
      };
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'taskId',
  fields: [
    {
      name: 'taskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskCode`).d('检证任务编码'),
    },
    {
      name: 'taskContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskContent`).d('任务内容'),
    },
    {
      name: 'taskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskStatus`).d('任务状态'),
      lookupCode: 'YP.QIS.VERIFICATION_TASK_STATUS',
    },
    {
      name: 'responsibleUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('责任人'),
    },
    {
      name: 'lastResponsibleUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastResponsibleUserName`).d('上一责任人'),
    },
    {
      name: 'departmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.departmentName`).d('责任部门'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成日期'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际完成时间'),
    },
    {
      name: 'applyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyFlag`).d('是否适用'),
      lookupCode: 'YP.QIS.Y_N',
    },
    {
      name: 'noApplyReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.noApplyReason`).d('不适用原因'),
    },
    {
      name: 'taskResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskResult`).d('审批结果'),
      lookupCode: 'YP.QIS.VERIFICATION_TASK_RESULT',
    },
    {
      name: 'resultJudgeDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.resultJudgeDate`).d('审批结果判定时间'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-platform/line/ui`,
        method: 'GET',
      };
    },
  },
});

export { headDS, lineDS };
