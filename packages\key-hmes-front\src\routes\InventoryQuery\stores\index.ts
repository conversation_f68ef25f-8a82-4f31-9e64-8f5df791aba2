import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { getCurrentSiteInfo } from '@utils/utils';

const modelPrompt = 'inventoryQuery';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        defaultValue: () => {
          const siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo };
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'workCenterLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workCenter`).d('工作中心'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorCategories: ['AREA'],
            siteId: record.get('siteId'),
          };
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'workCenter',
      type: FieldType.number,
      bind: 'workCenterLov.locatorId',
    },
    {
      name: 'bankAreaLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.bankArea`).d('库区/货架'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorCategories: ['AREA'],
            siteId: record.get('siteId'),
          };
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'warehouseId',
      type: FieldType.number,
      bind: 'bankAreaLov.locatorId',
    },
    {
      name: 'goodsAllocationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.goodsAllocation`).d('货位名称'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorCategories: ['INVENTORY', 'LOCATION'],
            siteId: record.get('siteId'),
          };
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'goodsAllocationLov.locatorId',
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次号'),
    },
    {
      name: 'materialClassLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialClass`).d('物料类别'),
      lovCode: 'APEX_WMS.MATERIAL_CATEGORY',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialCategory',
      type: FieldType.number,
      bind: 'materialClassLov.categoryCode',
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'APEX_WMS.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      bind: 'materialLov.materialName',
    },
  ],
  fields: [
    {
      name: 'index',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位名称'),
    },
    {
      name: 'materialCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCategory`).d('物料类别'),
    },
    {
      name: 'customCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customCode`).d('客商代码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
    },
    {
      name: 'materialQuality',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialQuality`).d('材质'),
    },
    {
      name: 'onHandQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onHandQty`).d('库存数量'),
    },
    {
      name: 'inventoryQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inventoryQuantity`).d('历史入库数量'),
    },
    {
      name: 'outboundQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outboundQuantity`).d('历史出库数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('主计量单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次号（箱号、托盘号）'),
    },
    {
      name: 'grade',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.grade`).d('冻结状态'),
    },
    {
      name: 'property',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.property`).d('产品属性'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/wms-inv-onhand-quantity/query/ui`,
        method: 'POST',
      };
    },
  },
});

export { tableDS };
