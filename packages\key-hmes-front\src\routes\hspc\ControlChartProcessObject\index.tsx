/**
 * @Description: 控制图过程对象维护-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 15:24:17
 * @LastEditTime: 2023-05-29 16:54:30
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { Table, DataSet, Dropdown, Modal } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import { Menu, Popconfirm, Tabs, Tag } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import intl from 'utils/intl';
import { openTab } from 'utils/menuTab';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { toJS } from 'mobx';
import queryString from 'querystring';
import { PermissionProvider, drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { getResponse } from '@utils/utils';
import {
  ProcessObjectDrawer,
  ChartProcessObjectDrawer,
} from './Drawers'
import {
  controlChartTableDS,
  analysisControlChartTableDS,
  ProcessObjectTableDS,
  chartProcessObjectDS,
  chartProcessObjectTableDS,
} from './stories';
import {
  FetchLovsBatchInfo,
  DeleteControlProcessObject,
  DeleteAnalysisProcessObject,
  SaveControlProcessObjects,
  SaveAnalysisProcessObjects,
  ReplaceControlProcessObjects,
  ReplaceAnalysisProcessObjects,
} from './services';
import { jsonString2object } from './utils';

const TabPane = Tabs.TabPane;
const modelPrompt = 'tarzan.hspc.controlChartProcessObject';
const tenantId = getCurrentOrganizationId();

type ChartType = 'control' | 'analysis'

const ControlChartProcessObjectList = (props) => {

  const {
    match: { path },
    permissionDetail,
  } = props;

  const controlChartTableDs = useMemo(() => new DataSet(controlChartTableDS()), []);
  const analysisControlChartTableDs = useMemo(() => new DataSet(analysisControlChartTableDS()), []);
  const ProcessObjectTableDs = useMemo(() => new DataSet(ProcessObjectTableDS()), []);
  const chartProcessObjectTableDs = useMemo(() => new DataSet(chartProcessObjectTableDS()), []);
  const chartProcessObjectDs = useMemo(
    () =>
      new DataSet({
        ...chartProcessObjectDS(),
        children: {
          table: chartProcessObjectTableDs,
        },
      }),
    [],
  );
  const fetchLovsBatchInfo = useRequest(FetchLovsBatchInfo(), { manual: true, needPromise: true });
  const deleteControlProcessObject = useRequest(DeleteControlProcessObject(), { manual: true });
  const deleteAnalysisProcessObject = useRequest(DeleteAnalysisProcessObject(), { manual: true });
  const saveControlProcessObjects = useRequest(SaveControlProcessObjects(), { manual: true, needPromise: true });
  const saveAnalysisProcessObjects = useRequest(SaveAnalysisProcessObjects(), { manual: true, needPromise: true });
  const replaceControlProcessObjects = useRequest(ReplaceControlProcessObjects(), { manual: true, needPromise: true });
  const replaceAnalysisProcessObjects = useRequest(ReplaceAnalysisProcessObjects(), { manual: true, needPromise: true });
  const [processObjInputType, setProcessObjInputType] = useState<{} | null>(null);
  const [processObjField, setProcessObjField] = useState<{} | null>(null);

  useEffect(() => {
    initialProcessObjectTypeInfo();
  }, [])

  async function initialProcessObjectTypeInfo() {
    const processObjList = await chartProcessObjectTableDs!.getField("objectType")!.fetchLookup() || [];
    const lovCodeList: string[] = [];
    const _type2Tag = {};
    const _type2Field = {};
    for (let i = 0; i < (processObjList as any[]).length; i++) {
      const item: any = processObjList[i];
      if (item.tag) {
        const _tag = jsonString2object(item.tag);
        if (!_tag) {
          return;
        }
        lovCodeList.push(_tag.lovCode);
        _type2Tag[item.value] = _tag.lovCode;
        _type2Field[item.value] = _tag;
      } else {
        _type2Tag[item.value] = 'EMPTY';
      }
    }
    fetchLovsBatchInfo.run({
      params: {
        lovCodeList: lovCodeList.join(','),
      },
    }).then((res) => {
      if (!(res instanceof Array)) {
        return;
      }
      const _tag2LovTypeCode = {};
      res.forEach(item => {
        _tag2LovTypeCode[item.lovCode] = item.lovTypeCode;
      })
      Object.keys(_type2Tag).forEach(key => {
        if (_type2Tag[key] === 'EMPTY') {
          return;
        }
        _type2Tag[key] = _tag2LovTypeCode[_type2Tag[key]];
      })
      setProcessObjInputType(_type2Tag);
      setProcessObjField(_type2Field);
    })
  }

  const handleDelete = (record: Record, type: ChartType) => {
    if (type === 'control') {
      deleteControlProcessObject.run({
        params: {
          controlId: record.get('controlId'),
        },
        onSuccess: () => {
          controlChartTableDs.query(controlChartTableDs.currentPage);
        },
      })
    } else {
      deleteAnalysisProcessObject.run({
        params: {
          analysisId: record.get('analysisId'),
        },
        onSuccess: () => {
          analysisControlChartTableDs.query(analysisControlChartTableDs.currentPage);
        },
      })
    }
  }

  const controlChartColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'controlCode',
        width: 200,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => handldEditChartProcessObject('control', record)}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'controlDesc', width: 300 },
      {
        name: 'processObjectDTOList',
        renderer: ({ value }) => {
          const data = toJS(value);
          return (
            <>
              {data.map(item => (<Tag key={item.controlProcessObjectId} color='geekblue'>{item.objectTypeDesc}: {(item.lovType === 'URL' || item.lovType === 'SQL') ? item.objectCode : item.objectDesc}</Tag>))}
            </>
          )
        },
      },
      {
        width: 200,
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        renderer: ({ record }) => {
          return (
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => handleDelete(record!, 'control')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              okText={intl.get('tarzan.common.button.confirm').d('确定')}
            >
              <PermissionButton
                type="text"
                permissionList={[
                  {
                    code: `tarzan${path}.button.edit`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.delete').d('删除')}
              </PermissionButton>
            </Popconfirm>
          );
        },
      },
    ];
  }, [processObjInputType, processObjField]);

  const analysisControlChartColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'analysisCode',
        width: 200,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => handldEditChartProcessObject('analysis', record)}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'analysisDesc', width: 300 },
      {
        name: 'processObjectDTOList',
        renderer: ({ value }) => {
          const data = toJS(value);
          return (
            <>
              {data.map(item => (<Tag key={item.analysisProcessObjectId} color='geekblue'>{item.objectTypeDesc}: {(item.lovType === 'URL' || item.lovType === 'SQL') ? item.objectCode : item.objectDesc}</Tag>))}
            </>
          )
        },
      },
      {
        width: 200,
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        renderer: ({ record }) => {
          return (
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => handleDelete(record!, 'analysis')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              okText={intl.get('tarzan.common.button.confirm').d('确定')}
            >
              <PermissionButton
                type="text"
                permissionList={[
                  {
                    code: `tarzan${path}.button.edit`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.delete').d('删除')}
              </PermissionButton>
            </Popconfirm>
          );
        },
      },
    ];
  }, [processObjInputType, processObjField]);

  const handleChartProcessObjectDrawerConfirm = useCallback(
    (key) => {
      return new Promise(async (resolve) => {
        const validateFlag = await chartProcessObjectDs.validate();
        if (!validateFlag || !processObjField) {
          return resolve(false);
        }
        const _filterList: any[] = [];
        const _tableList = chartProcessObjectTableDs.map(item => {
          const _objectCode =
            typeof item.get('objectCode') === 'string' ?
              item.get('objectCode') :
              item.get('objectCode')[processObjField[item.get('objectType')].codeField];
          _filterList.push(`${item.get('objectType')}${_objectCode}`);
          return {
            ...item.toData(),
            objectCode: _objectCode,
          }
        });
        const _filterListLength = _filterList.length;
        if (
          _filterListLength !== [...new Set(_filterList)].length
        ) {
          notification.error({
            message: intl.get(`${modelPrompt}.error.repeatChartProcessObj`).d('同一过程对象类型下，不允许选择重复的过程对象，请检查！'),
          });
          return resolve(false);
        }
        const _deleteList = chartProcessObjectTableDs.destroyed.map(item => {
          return {
            ...item.toData(),
            deleteFlag: 'Y',
          }
        });
        if (key === 'control') {
          return saveControlProcessObjects.run({ params: _tableList.concat(_deleteList) }).then(response => {
            const res = getResponse(response)
            if (res) {
              controlChartTableDs.query(controlChartTableDs.currentPage);
              return resolve(true);
            }
            return resolve(false);
          })
        }
        return saveAnalysisProcessObjects.run({ params: _tableList.concat(_deleteList) }).then(response => {
          const res = getResponse(response)
          if (res) {
            analysisControlChartTableDs.query(analysisControlChartTableDs.currentPage);
            return resolve(true);
          }
          return resolve(false);
        })
      })
    },
    [processObjInputType, processObjField],
  )

  const handleProcessObjectDrawerConfirm = useCallback(
    (key) => {
      return new Promise(async (resolve) => {
        const validateFlag = await ProcessObjectTableDs.validate();
        if (!validateFlag) {
          return resolve(false);
        }
        const _tableList = ProcessObjectTableDs.toData();
        for (let i = 0; i < _tableList.length; i++) {
          const item: any = _tableList[i];
          if (
            `${item.objectCode}${item.objectDesc}` ===
            `${item.newObjectCode}${item.newObjectDesc}`
          ) {
            notification.error({
              message: intl.get(`${modelPrompt}.error.repeatProcessObj`).d('修改后编码值和说明不能与修改前完全相同，请检查！'),
            });
            return resolve(false);
          }
        }
        if (key === 'control') {
          return replaceControlProcessObjects.run({ params: _tableList }).then(response => {
            const res = getResponse(response)
            if (res) {
              controlChartTableDs.query(controlChartTableDs.currentPage);
              return resolve(true);
            }
            return resolve(false);
          })
        }
        return replaceAnalysisProcessObjects.run({ params: _tableList }).then(response => {
          const res = getResponse(response)
          if (res.success) {
            analysisControlChartTableDs.query(analysisControlChartTableDs.currentPage);
            return resolve(true);
          }
          return resolve(false);
        })
      })
    },
    [],
  )

  const handldEditChartProcessObject = useCallback(
    (key, record) => {
      if (!processObjInputType || !processObjField) {
        return;
      }
      chartProcessObjectDs.loadData([{}]);
      chartProcessObjectTableDs.loadData([]);
      const _lovData: any = {};
      let _title = '';
      if (key === 'control') {
        _lovData.controlId = record.get('controlId');
        _lovData.controlCode = record.get('controlCode');
        _title = intl.get(`${modelPrompt}.editControlProcessObj`).d('编辑控制控制图过程对象')
      } else {
        _lovData.analysisId = record.get('analysisId');
        _lovData.analysisCode = record.get('analysisCode');
        _title = intl.get(`${modelPrompt}.editAnalysisProcessObj`).d('编辑分析控制图过程对象')
      }
      Modal.open({
        ...drawerPropsC7n({ ds: chartProcessObjectDs }),
        key: Modal.key(),
        title: _title,
        style: {
          width: 720,
        },
        children: (
          <ChartProcessObjectDrawer
            formDs={chartProcessObjectDs}
            tableDs={chartProcessObjectTableDs}
            chartType={key as ChartType}
            processObjInputType={processObjInputType}
            processObjField={processObjField}
            lovData={_lovData}
          />
        ),
        onOk: () => handleChartProcessObjectDrawerConfirm(key),
        okText: intl.get('tarzan.common.button.save').d('保存'),
      });
    },
    [processObjInputType, processObjField],
  )

  const handldCreateChartProcessObject = useCallback(
    ({ key }) => {
      if (!processObjInputType || !processObjField) {
        return;
      }
      chartProcessObjectDs.loadData([{}]);
      chartProcessObjectTableDs.loadData([]);
      let _title = '';
      if (key === 'control') {
        _title = intl.get(`${modelPrompt}.createControlProcessObj`).d('新建控制控制图过程对象')
      } else {
        _title = intl.get(`${modelPrompt}.createAnalysisProcessObj`).d('新建分析控制图过程对象')
      }
      Modal.open({
        ...drawerPropsC7n({ ds: chartProcessObjectDs }),
        key: Modal.key(),
        title: _title,
        style: {
          width: 720,
        },
        children: (
          <ChartProcessObjectDrawer
            formDs={chartProcessObjectDs}
            tableDs={chartProcessObjectTableDs}
            chartType={key as ChartType}
            processObjInputType={processObjInputType}
            processObjField={processObjField}
            lovData={undefined}
          />
        ),
        onOk: () => handleChartProcessObjectDrawerConfirm(key),
        okText: intl.get('tarzan.common.button.save').d('保存'),
      });
    },
    [processObjInputType, processObjField],
  )

  const handldEditProcessObject = useCallback(
    ({ key }) => {
      ProcessObjectTableDs.loadData([]);
      Modal.open({
        ...drawerPropsC7n({ ds: ProcessObjectTableDs }),
        key: Modal.key(),
        title: intl.get(`${modelPrompt}.editProcessObject`).d('编辑过程对象'),
        style: {
          width: 720,
        },
        children: (
          <ProcessObjectDrawer
            chartType={key as ChartType}
            tableDs={ProcessObjectTableDs}
          />
        ),
        onOk: () => handleProcessObjectDrawerConfirm(key),
        okText: intl.get('tarzan.common.button.save').d('保存'),
      });
    },
    [],
  )

  const createProcessObjMenu = useMemo(() => (
    <Menu onClick={handldCreateChartProcessObject}>
      <Menu.Item key="control">
        <a>{intl.get(`${modelPrompt}.createControlProcessObj`).d('新建控制控制图过程对象')}</a>
      </Menu.Item>
      <Menu.Item key="analysis">
        <a>{intl.get(`${modelPrompt}.createAnalysisProcessObj`).d('新建分析控制图过程对象')}</a>
      </Menu.Item>
    </Menu>
  ), [processObjInputType, processObjField]);

  const editProcessObjMenu = useMemo(() => (
    <Menu onClick={handldEditProcessObject}>
      <Menu.Item key="control">
        <a>{intl.get(`${modelPrompt}.editControlChartProcessObj`).d('编辑过程对象（控制控制图）')}</a>
      </Menu.Item>
      <Menu.Item key="analysis">
        <a>{intl.get(`${modelPrompt}.editAnalysisControlChartProcessObj`).d('编辑过程对象（分析控制图）')}</a>
      </Menu.Item>
    </Menu>
  ), []);

  const goImport = useCallback(
    ({ key }) => {
      let _key = ''
      if (key === 'control') {
        _key = '/himp/commentImport/MT.SPC.CONTROL_PROCESS_OBJECT';
      } else {
        _key = '/himp/commentImport/MT.SPC.ANALYSIS_PROCESS_OBJECT';
      }
      openTab({
        key: _key,
        title: 'hzero.common.title.templateImport',
        search: queryString.stringify({
          title: 'hzero.common.title.templateImport',
          action: 'himp.commentImport.view.button.templateImport',
          tenantId,
          prefixPatch: '',
          templateType: 'C',
        }),
      });
    },
    [],
  );

  const ImportMenu = useMemo(() => (
    <Menu onClick={goImport}>
      <Menu.Item key="control">
        <a>{intl.get(`${modelPrompt}.importControlChartProcessObj`).d('导入控制控制图过程对象')}</a>
      </Menu.Item>
      <Menu.Item key="analysis">
        <a>{intl.get(`${modelPrompt}.importAnalysisControlChartProcessObj`).d('导入分析控制图过程对象')}</a>
      </Menu.Item>
    </Menu>
  ), []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('控制图过程对象维护')}>
        <Dropdown overlay={createProcessObjMenu} disabled={!permissionDetail!.approve}>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
        </Dropdown>
        <Dropdown overlay={ImportMenu} disabled={!permissionDetail!.approve}>
          <PermissionButton
            type="c7n-pro"
            icon="file_upload"
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.data.confirm`).d('数据导入')}
          </PermissionButton>
        </Dropdown>
        <Dropdown overlay={editProcessObjMenu} disabled={!permissionDetail!.approve}>
          <PermissionButton
            type="c7n-pro"
            icon="edit-o"
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.editProcessObj`).d('编辑过程对象')}
          </PermissionButton>
        </Dropdown>
      </Header>
      <Content>
        <Tabs defaultActiveKey="controlChart">
          <TabPane tab={intl.get(`${modelPrompt}.controlChart`).d('控制控制图')} key="controlChart" dataSet={controlChartTableDs} forceRender>
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={controlChartTableDs}
              columns={controlChartColumns}
              searchCode="Material"
              customizedCode="Material"
            />
          </TabPane>
          <TabPane tab={intl.get(`${modelPrompt}.analysisControlChart`).d('分析控制图')} key="analysisControlChart" dataSet={analysisControlChartTableDs} forceRender>
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={analysisControlChartTableDs}
              columns={analysisControlChartColumns}
              searchCode="Material"
              customizedCode="Material"
            />
          </TabPane>
        </Tabs>
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.hspc.controlChartProcessObject', 'tarzan.common'],
})(PermissionProvider({ noDisplayCode: 'button.edit' })(ControlChartProcessObjectList));
