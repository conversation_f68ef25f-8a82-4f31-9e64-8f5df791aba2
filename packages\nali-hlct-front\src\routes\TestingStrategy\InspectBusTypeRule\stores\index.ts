/**
 * @Description: 检验业务类型规则维护-列表页DS
 * @Author: <EMAIL>
 * @Date: 2023/1/30 15:46
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.inspectBusTypeRule';
const tenantId = getCurrentOrganizationId();


const endUrl = '';

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectBusinessTypeRuleId',
  queryFields: [
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessTypeCode`).d('业务类型编码'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessTypeDesc`).d('业务类型描述'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'operatingMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operatingMode`).d('操作模式'),
      lookupCode: 'MT.QMS.OPERATING_MODE',
      lovPara: { tenantId },
    },
    {
      name: 'reviewType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('不良记录单评审规则类型'),
      lookupCode: 'MT.QMS.REVIEW_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'taskAssignType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskAssignType`).d('任务分配类型'),
      lookupCode: 'MT.QMS.TASK_ASSIGN_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'recheckTaskAssignType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recheckTaskAssignType`).d('复检任务分配类型'),
      lookupCode: 'MT.QMS.TASK_ASSIGN_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'docCompleteFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docCompleteFlag`).d('检验单自动完成标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'recheckFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recheckFlag`).d('复检标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'mergeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mergeFlag`).d('合批标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'taskSplitMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskSplitMethod`).d('检验任务拆分方式'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.TASK_SPLIT_METHOD',
    },
    {
      name: 'transferFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferFlag`).d('启用转移规则标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'transferDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferDimension`).d('转移维度'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.TRANSFER_DIMENSION',
    },
    {
      name: 'ncCodeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeFlag`).d('不良代码管理标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'blankInspectOrderFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.blankInspectOrderFlag`).d('空白检验单标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'ngAssignRuleType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngAssignRuleType`).d('不合格品分配原则'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.ASSIGN_RULE_TYPE',
    },
    {
      name: 'scrapAssignRuleType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapAssignRuleType`).d('报废品分配原则'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.ASSIGN_RULE_TYPE',
    },
    {
      name: 'dispositionGroupLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dispositionGroup`).d('处置组'),
      lovCode: 'MT.DISPOSITION_GROUP',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'dispositionGroupId',
      type: FieldType.number,
      bind: 'dispositionGroupLov.dispositionGroupId',
    },
    {
      name: 'autoCreateNcReportFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoCreateNcReportFlag`).d('自动生成不良记录单标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'ncReportType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportType`).d('不良记录单单据类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=NC_REPORT_TYPE`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'inspectNcRecordDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectNcRecordDimension`).d('检验不良记录维度'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.INSPECT_NC_RECORD_TYPE',
    },
    {
      type: FieldType.object,
      label: intl
        .get(`${modelPrompt}.actualInspectBusinessType`)
        .d('实际获取检验方案的检验业务类型'),
      name: 'actualInspectBusinessTypeLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      lovPara: { tenantId },
    },
    {
      name: 'actualInspectBusinessType',
      bind: 'actualInspectBusinessTypeLov.inspectBusinessType',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operatingEnd`).d('操作端'),
      name: 'operatingEnd',
      lookupCode: 'MT.QMS.OPERATING_END',
    },
    {
      name: 'oneselfFlag',
      type: FieldType.string,
      label: intl
        .get(`${modelPrompt}.oneselfFlag`)
        .d('是否强校验检验员仅能查询和操作自己的检验任务'),
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'templateType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.templateType`).d('模板类型'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.TEMPLATE_TYPE',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: { tenantId },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'enterFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterFlag`).d('回车增加记录框'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      name: 'inspectBusinessTypeRuleId',
      type: FieldType.number,
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessTypeCode`).d('检验业务类型编码'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessTypeDesc`).d('检验业务类型描述'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'operatingModeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operatingMode`).d('操作模式'),
    },
    {
      name: 'reviewTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('不良记录单评审规则类型'),
    },
    {
      name: 'workFlowCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workFlowCode`).d('工作流编码'),
    },
    {
      name: 'recheckFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recheckFlag`).d('复检标识'),
    },
    {
      name: 'recheckTaskCreateNodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recheckTaskCreateNode`).d('复检任务生成节点'),
    },
    {
      name: 'recheckResultDimensionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recheckResultDimension`).d('复检结果记录维度'),
    },
    {
      name: 'taskAssignType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskAssignType`).d('任务分配类型'),
      lookupCode: 'MT.QMS.TASK_ASSIGN_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'recheckTaskAssignType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recheckTaskAssignType`).d('复检任务分配类型'),
      lookupCode: 'MT.QMS.TASK_ASSIGN_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'docCompleteFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docCompleteFlag`).d('检验单自动完成标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'mergeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mergeFlag`).d('合批标识'),
    },
    {
      name: 'mergeDimensionDesc',
      // type: FieldType.string,
      label: intl.get(`${modelPrompt}.mergeDimension`).d('合批维度'),
    },
    {
      name: 'resultDimensionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.resultDimension`).d('结果记录维度'),
    },
    {
      name: 'taskSplitMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskSplitMethod`).d('检验任务拆分方式'),
    },
    {
      name: 'transferFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferFlag`).d('启用转移规则标识'),
    },
    {
      name: 'transferDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferDimension`).d('转移维度'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.TRANSFER_DIMENSION',
    },
    {
      name: 'transferRuleCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferRuleCode`).d('转移规则编码'),
    },
    {
      name: 'ncCodeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeFlag`).d('不良代码管理标识'),
    },
    {
      name: 'blankInspectOrderFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.blankInspectOrderFlag`).d('空白检验单标识'),
    },
    {
      name: 'ngAssignRuleTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngAssignRuleType`).d('不合格品分配原则'),
    },
    {
      name: 'scrapAssignRuleTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapAssignRuleType`).d('报废品分配原则'),
    },
    {
      name: 'dispositionGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispositionGroup`).d('处置组'),
    },
    {
      name: 'autoCreateNcReportFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoCreateNcReportFlag`).d('自动生成不良记录单标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'inspectNcRecordDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectNcRecordDimension`).d('检验不良记录维度'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.INSPECT_NC_RECORD_TYPE',
    },
    {
      type: FieldType.object,
      label: intl
        .get(`${modelPrompt}.actualInspectBusinessType`)
        .d('实际获取检验方案的检验业务类型'),
      name: 'actualInspectBusinessTypeLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      lovPara: { tenantId },
    },
    {
      name: 'actualInspectBusinessType',
      bind: 'actualInspectBusinessTypeLov.inspectBusinessType',
    },
    {
      name: 'actualInspectBusinessTypeDesc',
      bind: 'actualInspectBusinessTypeLov.inspectBusinessTypeDesc',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operatingEnd`).d('操作端'),
      name: 'operatingEndDesc',
    },
    {
      name: 'ncReportType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportType`).d('不良记录单单据类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=NC_REPORT_TYPE`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'oneselfFlag',
      type: FieldType.string,
      label: intl
        .get(`${modelPrompt}.oneselfFlag`)
        .d('是否强校验检验员仅能查询和操作自己的检验任务'),
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'templateTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.templateType`).d('模板类型'),
    },
    {
      name: 'printTemplate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.printTemplate`).d('打印模板'),
    },
    {
      name: 'messageCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.messageCode`).d('消息代码'),
    },
    {
      name: 'receiverTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiverType`).d('接收组'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
    {
      name: 'enterFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterFlag`).d('回车增加记录框'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-bus-type-rule/query/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
