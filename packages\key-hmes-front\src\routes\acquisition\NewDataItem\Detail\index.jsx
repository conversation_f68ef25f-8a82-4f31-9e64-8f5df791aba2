/**
 * @feature 新数据收集项维护
 * @date 2021-4-13
 * <AUTHOR> <<EMAIL>>
 */
import React, { useState, useRef, useMemo, useEffect } from 'react';
import { Button, DataSet, Form, IntlField, Modal, TextField,Table } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import intl from 'utils/intl';
import myInstance from '@utils/myAxios';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { AttributeDrawer } from '@components/tarzan-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { copySourceDS } from '../stories/CopyPageDs';
import Detail from './detail';
import { baseInfoDS, historicalQueryDS } from '../stories/BaseInfoDs';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.acquisition.dataItem.model.dataItem';

const index = props => {
  const {
    match: { path },
    customizeForm,
    custConfig,
  } = props;
  const { id, type } = props.match.params;
  const [canEdit, setCanEdit] = useState(id === 'create');
  const [valueType, setValueType] = useState('');

  const childRef = useRef();
  const copySourceDs = useMemo(() => new DataSet(copySourceDS()), []);
  const baseInfoDs = useMemo(
    () =>
      new DataSet({
        ...baseInfoDS(),
      }),
    [],
  );
  const historicalQueryDs = useMemo(() => new DataSet({
    ...historicalQueryDS()
  }),[])
  useEffect(() => {
    baseInfoDs.addEventListener('load', handleLoadData);
    return () => {
      baseInfoDs.removeEventListener('load', handleLoadData);
    };
  }, []);

  const handleLoadData = () => {
    setValueType(baseInfoDs.current.toJSONData()?.valueType)
  }

  let copyDrawer;

  const handleSave = async () => {
    const { success, resultId } = await childRef.current.submit();
    if (success) {
      setCanEdit(prev => !prev);
      props.history.push(`/hmes/acquisition/new-data-item-new/detail/${resultId}`);
      childRef.current.detailQuery(resultId);
    }
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/acquisition/new-data-item-new/list');
    } else {
      setCanEdit(prev => !prev);
      childRef.current.detailQuery(id);
    }
  };

  const copyDrawerSave = async () => {
    const validate = await copySourceDs.validate(false, true);
    if (validate) {
      const copySourceData = copySourceDs.toData();
      const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag/copy/ui`;
      myInstance
        .post(url, {
          ...copySourceData[0],
          sourceTagId: id,
        })
        .then(res => {
          if (res.data.success) {
            const resultId = res.data.rows;
            copyDrawer.close();
            notification.success();
            props.history.push(`/hmes/acquisition/new-data-item-new/detail/${resultId}`);
            // 复制成功后调用query方法
            childRef.current.detailQuery(resultId);
          } else {
            notification.error({
              message: res.data.message,
            });
          }
        });
    }
  };

  // 打开复制抽屉
  const handleCopyDrawerShow = () => {
    copySourceDs.reset();
    copyDrawer = Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.common.button.copy').d('复制'),
      drawer: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal copy-drawer-modal',
      children: (
        <>
          <Form dataSet={copySourceDs} columns={1} labelLayout="horizontal" labelWidth={112}>
            <TextField name="tagCode" />
            <IntlField
              name="tagDescription"
              modalProps={{
                title: intl.get(`${modelPrompt}.description`).d('描述'),
              }}
            />
          </Form>
        </>
      ),
      footer: (
        <>
          <div style={{ float: 'right' }}>
            <Button
              onClick={() => {
                copyDrawer.close();
              }}
            >
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button
              type="submit"
              onClick={() => {
                // eslint-disable-next-line
                copyDrawerSave().then(r => console.log(r));
              }}
              color={ButtonColor.primary}
            >
              {intl.get('tarzan.common.button.confirm').d('确定')}
            </Button>
          </div>
        </>
      ),
    });
  };

  const columns = [
    {
      name: 'tagCode',
      width:120,
    },
    {
      name: 'tagDescription',
      width:120,
    },
    {name: 'eventId'},
    {name: 'enableFlag'},
    {name: 'collectionMethod'},
    {name: 'valueType'},
    {name: 'enableFlag'},
    {
      name: 'trueValue',
      renderer: ({ value, record }) => {
        if(record.get('valueType') === 'VALUE'){
          if (record.get('trueValueList') && record.get('trueValueList').length) {
            const temp = record.get('trueValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue&&<Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {
      name: 'falseValue',
      renderer: ({ value, record }) => {
        if (record.get('valueType') === 'VALUE') {
          if (record.get('falseValueList') && record.get('falseValueList').length) {
            const temp = record.get('falseValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue && <Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {name: 'valueList'},
    {name: 'dateFormat'},
    {name: 'displayValueFlag'},
    {name: 'valueAllowMissing'},
    {name: 'allowUpdateFlag'},
    {
      name: 'specialRecordFlag',
      minWidth: 150,
    },
    {name: 'defaultNcCode'},
    {name: 'uomDesc'},
    {name: 'remark'},
    {name: 'createdByName'},
    {name: 'creationDate'},
  ];

  const handleHistoricalQuery = () => {
    historicalQueryDs.setQueryParameter('tagId', id)
    historicalQueryDs.query()
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.HistoricalQuery`).d('历史查询'),
      drawer: true,
      style: {
        width: 900,
      },
      cancelButton: false,
      okButton: false,
      maskClosable: true,
      closable: true,
      children: (
        <Table
          searchCode="sjsjxwh"
            customizedCode="sjsjxwh"
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
          dataSet={historicalQueryDs}
          columns={columns}
        />
      ),
    });
  }

  return (
    <div className="hmes-style">
      <Header
        title={intl.get('tarzan.acquisition.dataItem.title.list').d('数据收集项维护')}
        backPath="/hmes/acquisition/new-data-item-new/list"
      >
        {canEdit ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          icon="content_copy-o"
          disabled={canEdit}
          style={{ marginLeft: '10px' }}
          onClick={handleCopyDrawerShow}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.copy').d('复制')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          disabled={canEdit}
          style={{ marginLeft: '10px' }}
          onClick={handleHistoricalQuery}
        >
          {intl.get(`${modelPrompt}.HistoricalQuery`).d('历史查询')}
        </PermissionButton>
        <AttributeDrawer
          serverCode={BASIC.HMES_BASIC}
          className="org.tarzan.mes.domain.entity.MtTag"
          kid={id}
          canEdit={canEdit}
          disabled={id === 'create'}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.TAG_DETAIL.ATTR`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <Detail
          customizeForm={customizeForm}
          canEdit={canEdit}
          ref={childRef}
          id={id}
          type={type}
          columns={3}
          copySourceDs={copySourceDs}
          baseInfoDs={baseInfoDs}
          valueType={valueType}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.acquisition.dataItem', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.TAG_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.TAG_DETAIL.ATTR`],
  })(index),
);
