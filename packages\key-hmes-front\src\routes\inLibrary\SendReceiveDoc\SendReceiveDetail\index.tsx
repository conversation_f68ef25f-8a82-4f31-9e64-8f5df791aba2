/**
 * @Description: 库存调拨平台 - 详情页
 * @Author: <EMAIL>
 * @Date: 2022/3/8 10:12
 * @LastEditTime: 2023-05-18 16:22:21
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  NumberField,
  Select,
  Switch,
  Table,
  TextField,
  Button,
} from 'choerodon-ui/pro';
import { Badge, Collapse, Popover, Icon, Popconfirm } from 'choerodon-ui';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import intl from 'utils/intl';
import moment from 'moment';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@/utils/config';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { queryMapIdpValue } from 'services/api';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { detailHeaderDS, detailLineDS } from '../stores/SendReceiveDetailDS';
import { HandleSaveInstruction, GetToleranceInfo } from '../services';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inLibrary.sendReceiveDoc';

const SendReceiveDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    customizeForm,
    customizeTable,
  } = props;
  const tableDs = useMemo(() => new DataSet(detailLineDS()), []);
  const formDs: DataSet = useMemo(
    () =>
      new DataSet({
        ...detailHeaderDS(),
        children: {
          instructionDocLineList: tableDs,
        },
      }),
    [],
  );
  const [headerState, setHeaderState] = useState(''); // 用于存储头上的指令单据状态
  const [canEdit, setCanEdit] = useState(false); // "编辑"按钮标识
  const createPage = useMemo(() => id === 'create', []); // “新建”标识
  const [lineAdd, setLineAdd] = useState(false); // 当头上必输项全部输入时行上可新增
  const [isSubmit, setIsSubmit] = useState(false); // 是否保存成功标识
  const [toleranceInfo, setToleranceInfo] = useState<any>({}); // 允差信息
  const [lineLength, setLineLength] = useState<number>(0); // 用于存储行上的数据条数
  const [permissionFlag, setPermissionFlag] = useState<boolean>(true); // 用于判断当前界面是否有编辑权限

  const { run: handleSaveInstruction, loading: changeSaveLoading } = useRequest(HandleSaveInstruction(),
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: getToleranceInfo } = useRequest(GetToleranceInfo(),
    {
      manual: true,
      needPromise: true,
    },
  );

  useEffect(() => {
    if (id !== 'create') {
      queryDetail();
    } else {
      setCanEdit(true);
      formDs.loadData([{ demandTime: moment(moment().format('YYYY-MM-DD HH:mm:ss')) }]);
      handleFetchDefaultSourceSystem();
    }
  }, [id]);

  const queryDetail = async () => {
    formDs.setQueryParameter('instructionDocId', id);
    formDs.query().then(res => {
      if (res.success) {
        setLineAdd(true);
        const content = res.rows || {};
        const { instructionDocLineList = [] } = content;
        setHeaderState(content.instructionDocStatus);
        // 当行上有某行没有库位权限时，界面不可编辑
        if (instructionDocLineList.length) {
          instructionDocLineList.forEach(item => {
            if (!item.permissionFlag || item.permissionFlag === 'N') {
              setPermissionFlag(false);
            }
          });
        }
        // 进入详情界面时查允差信息并存到state里面
        return getToleranceInfo({
          params: {
            instructionDocType: content.instructionDocType,
            lovCode: 'APEX_WMS.SEND_RECEIVE_EXECUTE_DOC_TYPE',
          },
        }).then(response => {
          if (response && response.success) {
            setToleranceInfo({ ...response.rows[0] });
            // 返回的结果中，instructionType全为TRANSFER_OVER_LOCATOR，则目标站点默认来源站点
            let _instructionTypeStatus = true;
            response.rows.forEach(item => {
              if (item.instructionType !== 'TRANSFER_OVER_LOCATOR') {
                _instructionTypeStatus = false;
              }
            });
            formDs.current!.set('instructionType', _instructionTypeStatus);
            // 更新头上的instructionDocTypeTag, minBusinessType maxBusinessType
            formDs.current!.set('instructionDocTypeTag', response.rows[0].instructionDocTypeTag);
            formDs.current!.set('minBusinessType', response.rows[0].minBusinessType);
            formDs.current!.set('maxBusinessType', response.rows[0].maxBusinessType);
          }
        });
      }
    });
  };

  const handleFetchDefaultSourceSystem = () => {
    queryMapIdpValue({
      sourceSystemList: 'SOURCE_SYSTEM',
    }).then(res => {
      if(res) {
        const defaultSourceSystem = res.sourceSystemList.find(e => e.tag === "Y");
        if(defaultSourceSystem) {
          formDs.current.set('sourceSystem', defaultSourceSystem.value);
        };
      }
    });
  };

  const handleCancel = () => {
    if (id !== 'create') {
      queryDetail();
      setCanEdit(false);
    } else {
      props.history.push({
        pathname: `/hwms/in-library/send-receive-doc-new/list`,
        state: {
          queryFlag: isSubmit,
        },
      });
    }
  };

  const handleBack = () => {
    props.history.push({
      pathname: `/hwms/in-library/send-receive-doc-new/list`,
      state: {
        queryFlag: isSubmit,
      },
    });
  };

  const handleSave = async () => {
    const headerData: any = formDs.toData()[0];
    const lines: any = tableDs.toData();
    // 保存时校验单据行，没有时报错
    if (lines.length === 0) {
      notification.warning({
        description: intl
          .get(`${modelPrompt}.notification.lineLengthIsZero`)
          .d('未创建单据行，请检查！'),
      });
      return;
    }
    formDs.current!.set('newDate', new Date());
    tableDs.current!.set('newDate', new Date());
    getResult().then(response => {
      if (response) {
        // 过滤不必要的入参
        const _lines: any = [];
        lines.forEach(item => {
          const _lineItem = {};
          Object.keys(item).forEach(i => {
            if (!i.includes('Lov')) {
              _lineItem[i] = item[i];
            }
          })
          _lines.push({ ..._lineItem });
        });
        const _header = {};
        Object.keys(headerData).forEach(i => {
          if (!i.includes('Lov')) {
            _header[i] = headerData[i];
          }
        })
        return handleSaveInstruction({
          params: {
            ..._header,
            instructionDocLineList: _lines,
          },
        }).then(res => {
          if (res && res.success) {
            notification.success({});
            setCanEdit(false);
            setLineAdd(false);
            setIsSubmit(true);
            if (id === 'create') {
              props.history.push(`/hwms/in-library/send-receive-doc-new/detail/${res.rows}`);
            } else {
              queryDetail();
            }
          }
        });
      }
    });
  };

  const getResult = async () => {
    const validate = await formDs.validate(false, true);
    const pointValidate = await tableDs.validate();
    return validate && pointValidate;
  };

  const handleAddLine = () => {
    const {
      fromSiteLov,
      fromWareHouseLov,
      fromLocatorLov,
      toSiteLov,
      toWareHouseLov,
      toLocatorLov,
    } = formDs!.current!.toData();
    const listData = tableDs.toData();
    let maxNumber = 0;
    listData.forEach(item => {
      const { lineNumber } = item as any;
      if (lineNumber && lineNumber > maxNumber) {
        maxNumber = lineNumber;
      }
    });
    tableDs.create(
      {
        fromSiteLov,
        fromWareHouseLov,
        fromLocatorLov,
        toSiteLov,
        toWareHouseLov,
        toLocatorLov,
        lineNumber: Math.floor(maxNumber / 10) * 10 + 10,
        toleranceFlag: toleranceInfo.toleranceFlag,
        toleranceType: toleranceInfo.toleranceType,
        toleranceTypeDesc: toleranceInfo.toleranceTypeDesc,
        toleranceMaxValue: toleranceInfo.toleranceMaxValue,
        toleranceMinValue: toleranceInfo.toleranceMinValue,
        fromLocatorRequiredFlag: toleranceInfo.fromLocatorRequiredFlag,
        toLocatorRequiredFlag: toleranceInfo.toLocatorRequiredFlag,
      },
      0,
    );
    setLineLength(tableDs.toData().length);
  };

  // 删除按钮的回调
  const deleteRecord = record => {
    tableDs.remove(record);
    setLineLength(tableDs.toData().length);
  };

  // 头上“调拨单类型”变化回调
  const handleTypeChange = async (val, oldVal) => {
    const list: any = formDs.toData()[0];
    const { fromSiteId, toSiteId, instructionDocType, instructionType } = list;
    let validate;
    if (fromSiteId && toSiteId) {
      validate = await formDs.validate(false, true);
    }
    setLineAdd(fromSiteId && toSiteId && instructionDocType && validate);
    if (val && val !== '') {
      if (oldVal !== val) {
        formDs.current!.init('fromWareHouseLov', null);
        formDs.current!.init('fromLocatorLov', null);
        formDs.current!.init('toWareHouseLov', null);
        formDs.current!.init('toLocatorLov', null);
      }
      return getToleranceInfo({
        params: {
          instructionDocType: val,
          lovCode: 'APEX_WMS.SEND_RECEIVE_EXECUTE_DOC_TYPE',
        },
      }).then(res => {
        if (res && res.success) {
          setToleranceInfo({ ...res.rows[0] });
          // 返回的结果中，instructionType全为TRANSFER_OVER_LOCATOR，则目标站点默认来源站点
          let _instructionTypeStatus = true;
          res.rows.forEach(item => {
            if (item.instructionType !== 'TRANSFER_OVER_LOCATOR') {
              _instructionTypeStatus = false;
            }
          });
          formDs.current!.set('instructionType', _instructionTypeStatus);
          // 当instructionType变化时清空来源站点和目标站点
          if (instructionType !== _instructionTypeStatus) {
            formDs.current!.init('fromSiteLov', null);
            formDs.current!.init('toSiteLov', null);
            formDs.current!.init('siteLov', null);
          }
          // 更新头上的instructionDocTypeTag, minBusinessType maxBusinessType
          formDs.current!.set('instructionDocTypeTag', res.rows[0].instructionDocTypeTag);
          formDs.current!.set('minBusinessType', res.rows[0].minBusinessType);
          formDs.current!.set('maxBusinessType', res.rows[0].maxBusinessType);
          // 新增界面--切换类型时需要更新行上的允差信息
          const lineList = tableDs.toData();
          if (lineList.length > 0) {
            const newLine: any = [];
            lineList.forEach((item: any) => {
              const _item = {
                ...item,
                fromWareHouseLov: null,
                fromWareHouseId: null,
                fromWareHouseCode: null,
                toWareHouseLov: null,
                toWareHouseId: null,
                toWareHouseCode: null,
                toleranceFlag: res.rows[0].toleranceFlag,
                toleranceType: res.rows[0].toleranceType,
                toleranceTypeDesc: res.rows[0].toleranceTypeDesc,
                toleranceMaxValue: res.rows[0].toleranceMaxValue,
                toleranceMinValue: res.rows[0].toleranceMinValue,
                fromLocatorRequiredFlag: res.rows[0].fromLocatorRequiredFlag,
                toLocatorRequiredFlag: res.rows[0].toLocatorRequiredFlag,
              };
              newLine.push(_item);
            });
            tableDs.loadData(newLine);
          }
        }
      });
    }
  };

  // “来源站点,目标站点”变化时的回调
  const handleSiteChange = async (val, dataSet, fieldName) => {
    const list: any = formDs.toData()[0];
    const { fromSiteId, toSiteId, instructionDocType, instructionType, siteId } = list;
    let validate;
    if (fromSiteId && toSiteId && siteId) {
      validate = await formDs.validate(false, true);
    }
    setLineAdd(fromSiteId && toSiteId && instructionDocType && siteId && validate);
    if (fieldName === 'fromSiteLov') {
      dataSet.current!.init('fromWareHouseLov', null);
      dataSet.current!.init('fromLocatorLov', null);
      // 当instructionType为true时，选择来源站点则默认带出与其相同的目标站点，且不可编辑
      if (instructionType) {
        if (val) {
          dataSet.current!.init('toSiteLov', val);
          dataSet.current!.init('siteLov', val);
          handleSiteChange(val, formDs, 'toSiteLov');
        } else {
          dataSet.current!.init('toSiteLov', null);
          dataSet.current!.init('siteLov', null);
          handleSiteChange(null, formDs, 'toSiteLov');
        }
      }
    } else if (fieldName === 'toSiteLov') {
      dataSet.current!.init('toWareHouseLov', null);
      dataSet.current!.init('toLocatorLov', null);
    }
  };

  // “来源仓库，目标仓库"变化的回调
  const handleWareHouseChange = async (val, record, fieldName) => {
    if (fieldName === 'fromWareHouseLov') {
      record.init('fromLocatorLov', null);
      record.init('fromOnhandQty', null);
    } else {
      record.init('toLocatorLov', null);
      record.init('toOnhandQty', null);
    }
    if (val && tableDs.toData()?.length) {
      const {
        materialId,
        fromSiteId,
        toSiteId,
        fromWareHouseId,
        toWareHouseId,
      } = tableDs.current!.toData();
      if (fieldName === 'fromWareHouseLov' && materialId && fromSiteId && fromWareHouseId) {
        await getSourceLocatorInfo('fromWareHouseLov', record);
      }
      if (fieldName === 'toWareHouseLov' && materialId && toSiteId && toWareHouseId) {
        await getSourceLocatorInfo('toWareHouseLov', record);
      }
    }
  };

  // “来源库位，目标库位"变化的回调
  const handleLocatorChange = async (val, fieldName, record) => {
    if (val && tableDs.toData()?.length) {
      const {
        materialId,
        fromSiteId,
        toSiteId,
        fromLocatorId,
        toLocatorId,
      } = tableDs.current!.toData();
      if (fieldName === 'fromLocatorLov' && materialId && fromSiteId && fromLocatorId) {
        await getSourceLocatorInfo('fromWareHouseLov', record);
      }
      if (fieldName === 'toLocatorLov' && materialId && toSiteId && toLocatorId) {
        await getSourceLocatorInfo('toWareHouseLov', record);
      }
    }
  };

  // 行上“物料”Lov变化的回调
  const materialChange = async (val, record) => {
    if (val) {
      tableDs.current!.init('revisionCode', val.currentRevisionCode);
      const {
        materialId,
        fromSiteId,
        toSiteId,
        fromWareHouseId,
        toWareHouseId,
      } = tableDs.current!.toData();
      if (materialId && fromSiteId && fromWareHouseId) {
        await getSourceLocatorInfo('fromWareHouseLov', record);
      }
      if (materialId && toSiteId && toWareHouseId) {
        await getSourceLocatorInfo('toWareHouseLov', record);
      }
    } else {
      tableDs.current!.init('revisionCode');
      tableDs.current!.init('fromOnhandQty');
      tableDs.current!.init('toOnhandQty');
    }
  };

  // 行上“物料版本”Select变化的回调
  const materialRevisionChange = async (val, record) => {
    if (val) {
      const {
        materialId,
        revisionCode,
        fromSiteId,
        toSiteId,
        fromWareHouseId,
        toWareHouseId,
      } = tableDs.current!.toData();
      if (materialId && revisionCode && fromSiteId && fromWareHouseId) {
        await getSourceLocatorInfo('fromWareHouseLov', record);
      }
      if (materialId && revisionCode && toSiteId && toWareHouseId) {
        await getSourceLocatorInfo('toWareHouseLov', record);
      }
    }
  };

  // 获得“来源库存现有量”信息
  const getSourceLocatorInfo = async (fieldName, record) => {
    const {
      materialId,
      revisionCode,
      fromSiteId,
      toSiteId,
      fromWareHouseId,
      toWareHouseId,
      fromLocatorId,
      toLocatorId,
      soLineId,
    } = tableDs.current!.toData();
    let _param;
    if (materialId && (fromSiteId || toSiteId) && (fromWareHouseId || toWareHouseId)) {
      if (fieldName === 'fromWareHouseLov') {
        _param = {
          materialId,
          revisionCode,
          soLineId,
          siteId: fromSiteId,
          locatorId: fromLocatorId || fromWareHouseId,
        };
      } else {
        _param = {
          materialId,
          revisionCode,
          soLineId,
          siteId: toSiteId,
          locatorId: toLocatorId || toWareHouseId,
        };
      }
      await request(`${BASIC.HWMS_BASIC}/v1/${tenantId}/inventory-send-receive/onhand/qty/ui`, {
        method: 'get',
        query: _param,
      }).then(res => {
        if (res && res.success) {
          if (fieldName === 'fromWareHouseLov') {
            record.set('fromOnhandQty', res.rows);
          } else {
            record.set('toOnhandQty', res.rows);
          }
        }
      });
    }
  };

  // 行上“销单”Lov变化的回调
  const handleSoNumberChange = async record => {
    tableDs!.current!.init('soLineLov');
    const {
      materialId,
      fromSiteId,
      toSiteId,
      fromWareHouseId,
      toWareHouseId,
      soId,
      soLineId,
    } = tableDs.current!.toData();
    if (!soId && !soLineId) {
      if (materialId && fromSiteId && fromWareHouseId) {
        await getSourceLocatorInfo('fromWareHouseLov', record);
      }
      if (materialId && toSiteId && toWareHouseId) {
        await getSourceLocatorInfo('toWareHouseLov', record);
      }
    }
  };

  // 行上“销单行号”Lov变化的回调
  // const handleSoLineChange = async record => {
  //   const {
  //     materialId,
  //     fromSiteId,
  //     toSiteId,
  //     fromWareHouseId,
  //     toWareHouseId,
  //   } = tableDs.current!.toData();
  //   if (materialId && fromSiteId && fromWareHouseId) {
  //     await getSourceLocatorInfo('fromWareHouseLov', record);
  //   }
  //   if (materialId && toSiteId && toWareHouseId) {
  //     await getSourceLocatorInfo('toWareHouseLov', record);
  //   }
  // };

  // 行上“允差标识”Switch变化的回调
  const handleToleranceChange = () => {
    tableDs!.current!.init('toleranceType');
    tableDs!.current!.init('toleranceMaxValue');
    tableDs!.current!.init('toleranceMinValue');
  };

  // 行上“允差类型”Select变化的回调
  const handleToleranceTypeChange = () => {
    tableDs!.current!.init('toleranceMaxValue');
    tableDs!.current!.init('toleranceMinValue');
  };

  const lineTableColumns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!(canEdit && lineAdd)}
          onClick={handleAddLine}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={record?.get('instructionDocLineId')}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      width: 100,
    },
    {
      name: 'materialLov',
      width: 140,
      editor: record =>
        !record?.get('instructionDocLineId') && (
          <Lov onChange={val => materialChange(val, record)} />
        ),
      renderer: ({ record }) => record?.get('materialCode'),
    },
    {
      name: 'revisionCode',
      width: 100,
      editor: record =>
        record?.get('revisionFlag') === 'Y' && (
          <Select noCache onChange={val => materialRevisionChange(val, record)} />
        ),
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'requiredQty',
      width: 140,
      align: ColumnAlign.right,
      editor: record => canEdit && record?.get('statusCode') !== 'CANCEL' && <NumberField />,
    },
    {
      name: 'uomCode',
      width: 100,
    },
    {
      name: 'statusDesc',
      width: 80,
    },
    {
      name: 'fromIdentifyType',
      width: 120,
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   } if (value === 'MATERIAL_LOT' || value === '') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      // },
    },
    {
      name: 'fromSiteLov',
      width: 140,
      renderer: ({ record }) => record?.get('fromSiteCode'),
    },
    {
      name: 'fromWareHouseLov',
      width: 140,
      editor: record =>
        canEdit &&
        record?.get('statusCode') !== 'CANCEL' && (
          <Lov onChange={val => handleWareHouseChange(val, record, 'fromWareHouseLov')} />
        ),
      renderer: ({ record }) => record?.get('fromWareHouseCode'),
    },
    {
      name: 'fromLocatorLov',
      width: 140,
      editor: record =>
        canEdit &&
        record?.get('statusCode') !== 'CANCEL' && (
          <Lov onChange={val => handleLocatorChange(val, 'fromLocatorLov', record)} />
        ),
      renderer: ({ record }) => record?.get('fromLocatorCode'),
    },
    {
      name: 'soNumberLov',
      width: 140,
      editor: record =>
        !record?.get('instructionDocLineId') && (
          <Lov onChange={() => handleSoNumberChange(record)} />
        ),
      renderer: ({ record }) => record?.get('soNumber'),
    },
    // {
    //   name: 'soLineLov',
    //   width: 140,
    //   editor: record =>
    //     !record?.get('instructionDocLineId') && <Lov onChange={() => handleSoLineChange(record)} />,
    //   renderer: ({ record }) => record?.get('soLineNumber'),
    // },
    {
      name: 'soLineNumber',
      width: 120,
    },
    {
      name: 'fromOnhandQty',
      width: 120,
    },
    {
      name: 'toIdentifyType',
      width: 120,
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   } if (value === 'MATERIAL_LOT' || value === '') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      // },
    },
    {
      name: 'toSiteLov',
      width: 140,
      renderer: ({ record }) => record?.get('toSiteCode'),
    },
    {
      name: 'toWareHouseLov',
      width: 140,
      editor: record =>
        canEdit &&
        record?.get('statusCode') !== 'CANCEL' && (
          <Lov onChange={val => handleWareHouseChange(val, record, 'toWareHouseLov')} />
        ),
      renderer: ({ record }) => record?.get('toWareHouseCode'),
    },
    {
      name: 'toLocatorLov',
      width: 140,
      editor: record =>
        canEdit &&
        record?.get('statusCode') !== 'CANCEL' && (
          <Lov onChange={val => handleLocatorChange(val, 'toLocatorLov', record)} />
        ),
      renderer: ({ record }) => record?.get('toLocatorCode'),
    },
    {
      name: 'toOnhandQty',
      width: 120,
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: ColumnAlign.center,
      editor: record =>
        !record?.get('instructionDocLineId') && <Switch onChange={handleToleranceChange} />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 140,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.get('toleranceFlag') === 'Y' && <Select onChange={handleToleranceTypeChange} />,
      // renderer: ({ record }) => record?.get('toleranceTypeDesc'),
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
      align: ColumnAlign.right,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.get('toleranceFlag') === 'Y' && <NumberField />,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
      align: ColumnAlign.right,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.get('toleranceFlag') === 'Y' && <NumberField />,
    },
  ];

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.sendReceiveDetail`).d('库存调拨维护')}
        backPath="/hwms/in-library/send-receive-doc-new/list"
        onBack={handleBack}
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              loading={changeSaveLoading}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            disabled={headerState !== 'RELEASED' || !permissionFlag}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.header`).d('头信息')}
            key="basicInfo"
            dataSet={formDs}
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.HEAD`,
              },
              <Form disabled={!canEdit} dataSet={formDs} columns={3} labelWidth={110}>
                <TextField disabled name="instructionDocNum" />
                <Select
                  name="instructionDocType"
                  disabled={lineLength !== 0 || !createPage}
                  onChange={(value, oldValue) => handleTypeChange(value, oldValue)}
                />
                <TextField disabled name="instructionDocStatus" />
                <Lov
                  name="fromSiteLov"
                  disabled={lineLength !== 0 || !createPage}
                  onChange={val => handleSiteChange(val, formDs, 'fromSiteLov')}
                />
                <Lov
                  name="fromWareHouseLov"
                  disabled={!createPage}
                  onChange={val => handleWareHouseChange(val, formDs!.current, 'fromWareHouseLov')}
                />
                <Lov name="fromLocatorLov" disabled={!createPage} />
                <Lov
                  name="toSiteLov"
                  disabled={lineLength !== 0 || !createPage}
                  onChange={val => handleSiteChange(val, formDs, 'toSiteLov')}
                />
                <Lov
                  name="toWareHouseLov"
                  disabled={!createPage}
                  onChange={val => handleWareHouseChange(val, formDs!.current, 'toWareHouseLov')}
                />
                <Lov name="toLocatorLov" disabled={!createPage} />
                <DateTimePicker name="demandTime" />
                <Lov
                  name="siteLov"
                  disabled={lineLength !== 0 || !createPage}
                  onChange={val => handleSiteChange(val, formDs, 'siteLov')}
                />
                <Select name='sourceSystem' disabled />
                <TextField name="remark" />
              </Form>,
            )}
          </Panel>
          <Panel
            header={
              <>
                {intl.get(`${modelPrompt}.title.line`).d('行信息')}
                <Popover
                  placement="topLeft"
                  content={intl
                    .get(`${modelPrompt}.line.tooltipMessage`)
                    .d(
                      '若您没有勾选的行上仓库的权限，该行将会为置灰状态，不能编辑，您可以前往人员仓库权限分配维护权限。',
                    )}
                >
                  <Icon
                    style={{ fontSize: 16, color: '#8c8c8c', marginBottom: 2, marginLeft: 2 }}
                    type="help"
                  />
                </Popover>
              </>
            }
            key="location"
            dataSet={tableDs}
          >
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.LINE`,
              },
              <Table
                filter={record => {
                  return record.status !== 'delete';
                }}
                dataSet={tableDs}
                highLightRow={false}
                columns={lineTableColumns}
              />,
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.inLibrary.sendReceiveDoc', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.HEAD`, `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.LINE`],
  // @ts-ignore
})(SendReceiveDetail));
