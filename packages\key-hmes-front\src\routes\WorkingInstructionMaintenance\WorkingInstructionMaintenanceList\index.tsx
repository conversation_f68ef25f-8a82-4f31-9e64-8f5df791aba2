/**
 * @Description: 作业指导书管理-列表页
 * @Author: <<EMAIL>>
 * @Date: 2023-07-24 09:21:14
 * @LastEditTime: 2023-07-26 19:43:08
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useCallback, useEffect } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { useDataSetEvent } from 'utils/hooks';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores';

const modelPrompt = 'hmes.workingInstructionMaintenance';

const WorkingInstructionMaintenanceList = (props) => {
  const {
    match: { path },
    tableDs,
    history,
  } = props;

  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    if (name === 'siteId') {
      record.set('materialLov', {});
    }
  });

  useEffect(() => {
    if (props?.location?.state?._back) {
      // 从新建/详情页返回到列表页
      tableDs.query(props.tableDs.currentPage);
      props.location.state._back = null;
    }
  }, [])

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'siteCode',
        width: 120,
      },
      {
        name: 'sopCode',
        width: 120,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hmes/working-instruction-maintenance/detail/${record!.get('sopHeaderId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'sopName',
        width: 120,
      },
      {
        name: 'remark',
      },
      {
        name: 'startDate',
        width: 120,
      },
      {
        name: 'endDate',
        width: 120,
      },
      {
        name: 'creationDate',
        width: 120,
      },
      {
        name: 'lastUpdateDate',
        width: 120,
      },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    history.push(`/hmes/working-instruction-maintenance/detail/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('作业指导书管理')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="zyzdsgl"
          customizedCode="zyzdsgl"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(WorkingInstructionMaintenanceList),
);
