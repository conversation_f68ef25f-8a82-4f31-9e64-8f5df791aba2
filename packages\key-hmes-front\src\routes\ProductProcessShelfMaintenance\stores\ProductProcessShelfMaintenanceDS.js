import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.product.processShelf.maintenance';

// const Host1 = `/key-ne-focus-mes-38283`;

const tableDS = (xFormDS) => {
  return {
    name: 'tableDS',
    primaryKey: 'materialOpExpirationId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
        lovCode: 'HME.PERMISSION_MATERIAL',
        labelWidth: 150,
        textField: 'materialCode',
        required: true,
        dynamicProps: {
          lovPara: () => {
            const siteId = xFormDS.current?.data.siteId;
            return {
              tenantId: getCurrentOrganizationId(),
              siteId,
            };
          },
        },
      },
      {
        name: 'materialCode',
        type: 'string',
        bind: 'materialObj.materialCode',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
        bind: 'materialObj.materialName',
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'materialSiteId',
        type: 'string',
        bind: 'materialObj.materialSiteId',
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        bind: 'materialObj.revisionCode',
        textField: 'revisionCode',
        valueField: 'revisionCode',
        lookupUrl: `${Host}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
        lookupAxiosConfig: ({ record }) => {
          const _params = record?.toData() || {};
          const siteId = xFormDS.current?.data.siteId;
          if (_params && _params.materialSiteId) {
            return {
              params: {
                materialSiteId: _params.materialSiteId,
                siteId,
              },
              transformResponse(data) {
                // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
                if (data instanceof Array) {
                  return data;
                }
                if (data.failed) {
                  return [];
                }
                const rows = JSON.parse(data);
                return rows;
              },
            };
          }
        },
        dynamicProps: {
          required: record => {
            const _params = record?.record.data || {};
            if (_params && _params.revisionFlag && _params.revisionFlag === 'Y') {
              return true;
            }
            return false;
          },
        },
      },
      {
        name: 'fromOperationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.fromOperationObj`).d('起始工艺'),
        lovCode: 'MT.OPERATION_CONVERSION',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        textField: 'operationName',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'fromOperationName',
        type: 'string',
        bind: 'fromOperationObj.operationName',
      },
      {
        name: 'fromOperationId',
        type: 'number',
        bind: 'fromOperationObj.operationId',
      },
      {
        name: 'toOperationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.toOperationObj`).d('截止工艺'),
        lovCode: 'MT.OPERATION_CONVERSION',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        textField: 'operationName',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'toOperationName',
        type: 'string',
        bind: 'toOperationObj.operationName',
      },
      {
        name: 'toOperationId',
        type: 'number',
        bind: 'toOperationObj.operationId',
      },
      {
        name: 'minExpiration',
        type: 'number',
        required: true,
        min: 0,
        label: intl.get(`${modelPrompt}.minExpiration`).d('保质期下限'),
      },
      {
        name: 'maxExpiration',
        type: 'number',
        required: true,
        min: 0,
        label: intl.get(`${modelPrompt}.maxExpiration`).d('保质期（小时）'),
      },
      {
        type: 'string',
        name: 'crossLineFlag',
        label: intl.get(`${modelPrompt}.crossLineFlag`).d('是否跨工段'),
        required: true,
        trueValue: 'Y',
        falseValue: 'N',
        // lookupCode: 'MT.APS.YES_NO',
        defaultValue: 'Y',
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        required: true,
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
      {
        name: 'transferExpirationFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.transferExpirationFlag`).d('转罐保质期标识'),
        trueValue: 'Y',
        falseValue: 'N',
      },
    ],
    queryFields: [
      {
        name: 'materialCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
        lovCode: 'HME.PERMISSION_MATERIAL',
        labelWidth: 150,
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialCode.materialId',
      },
      {
        name: 'startOperationCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.startOperationCode`).d('起始工艺'),
        lovCode: 'MT.OPERATION_CONVERSION',
        labelWidth: 150,
        ignore: 'always',
      },
      {
        name: 'fromOperationId',
        type: 'number',
        bind: 'startOperationCode.operationId',
      },
      {
        name: 'endOperationCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.endOperationCode`).d('截止工艺'),
        lovCode: 'MT.OPERATION_CONVERSION',
        labelWidth: 150,
        ignore: 'always',
      },
      {
        name: 'toOperationId',
        type: 'number',
        bind: 'endOperationCode.operationId',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-material-op-expirations/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const siteDS = () => {
  return {
    name: 'siteDS',
    paging: true,
    autoQuery: true,
    selection: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
      },
      {
        name: 'siteId',
        type: 'string',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-assemble-points/get/user/def/site`,
          method: 'GET',
        };
      },
    },
  };
};


export { tableDS, siteDS };
