.TraceabilityRepair {
  padding-top: 43px;
  height: 100%;
  background: rgba(56, 112, 143, 1);
  .TraceabilityHeadRepair {
    background: #3d8db2 !important;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    height: 43px;
    padding-top: 5px;
    .addButton{
      margin-top: 4px !important;
    }
    :global {
      .c7n-pro-field-label {
        color: white;
        font-size: 18px;
      }
      .c7n-pro-input-wrapper {
        background: #5a9ebd;
      }
      .c7n-pro-input {
        color: white !important;
      }
    }
  }
  .TraceabilityLinePaire {
    .c7n-spin-nested-loading {
      .c7n-table-tbody {
        .c7n-pro-table-row {
          background: #5a9ebd !important;
        }
      }
      .c7n-pro-table-content .c7n-pro-table-thead .dataAcquRepair{
        background: #5a9ebd !important;
        color: white !important;
      }
      .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-selection-column{
        background: #5a9ebd !important;
        color: white !important;
      }
      .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell{
        background: #5a9ebd !important;
        color: white !important;
      }
      .c7n-pro-table-content .c7n-pro-table-tbody .highLightRow{
        background: white !important;
        color: white !important;
      }

    }
    :global {
      .c7n-progress-text {
        color: white !important;
      }
    }
  }
  .highLightRow {
    background: #fff !important;
 }

}
