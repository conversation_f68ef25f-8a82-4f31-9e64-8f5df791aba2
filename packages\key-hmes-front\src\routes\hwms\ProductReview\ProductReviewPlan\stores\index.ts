/*
 * @Description: 产品审核计划-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-10-09 13:49:27
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-11-22 16:49:26
 */
import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.productReview.productReviewPlan';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'productRevPlanId',
  queryFields: [
    {
      name: 'productRevPlanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanCode`).d('产品审核计划编码'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'revYear',
      type: FieldType.year,
      label: intl.get(`${modelPrompt}.revYear`).d('年份'),
    },
    {
      name: 'productRevPlanStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_PLAN_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
  ],
  fields: [
    { name: 'productRevPlanId' },
    {
      name: 'productRevPlanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanCode`).d('产品审核计划编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'productRevPlanDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanDesc`).d('计划描述'),
    },
    {
      name: 'revYear',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revYear`).d('年份'),
    },
    {
      name: 'productRevPlanStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_PLAN_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const { revYear } = data;
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-plan/list/ui`,
        method: 'get',
        data: {
          ...data,
          revYear: revYear ? moment(revYear).format('YYYY') : undefined,
        },
      }
    },
  },
});

export { tableDS };
