/**
 * @Description: 事件对象类型维护 接口
 * @Author: <<EMAIL>>
 * @Date: 2022-10-19 09:53:45
 * @LastEditTime: 2022-10-19 15:56:49
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 事件对象类型维护-保存
export function saveEventObjectTypeConfig(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-object-type/save/ui`,
    method: 'POST',
  };
}

// 事件对象类型维护-删除
export function deleteEventObjectTypeConfig(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-object-type/remove/ui`,
    method: 'POST',
  };
}

// 事件对象类型维护-对象展示预览
export function getObjectTypeConfig(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-object-type/query-sql/ui`,
    method: 'GET',
  };
}

// 事件对象类型维护-展示列维护-保存
export function saveEventObjectTypeDrawerConfig(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-object-column/save/ui`,
    method: 'POST',
  };
}

// 事件对象类型维护-展示列维护-删除
export function deleteEventObjectTypeDrawerConfig(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-object-column/remove/ui`,
    method: 'POST',
  };
}
