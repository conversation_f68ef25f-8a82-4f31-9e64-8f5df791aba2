import React from 'react';
import { DataSet, Form, Lov, Modal, TextArea, TextField, Select } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import axios from 'axios';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';
interface CreateModalProps {
  formDs: DataSet;
  handleSubmit: (data: any, selectionType: any, btnType: any, record?) => void;
  selectionType: string;
  btnType: string;
}

const CreateDrawer: CreateModalProps = ({ formDs, handleSubmit, selectionType, btnType }) => {

  const onChangeData = async val => {
    if (!val) {
      formDs.current?.set('rootCauseEquipmentCode', undefined);
      formDs.current?.set('rootCauseEquipmentId', undefined);
    } else {
      // 选择工作单元后，自动带出设备，正常只有一个设备
      const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-nc-record/equipment/lov/ui?tenantId=${tenantId}&workcellId=${val.workcellId}`;
      const res: any = await axios.get(url);
      if (res) {
        const { content } = res;
        if (content && content.length === 1) {
          const { equipmentId, equipmentCode } = content[0];
          formDs.current?.set('rootCauseEquipmentCode', equipmentCode);
          formDs.current?.set('rootCauseEquipmentId', equipmentId);
        } else if (content.length > 1) {
          notification.error({
            message: intl.get(`${modelPrompt}.detail.equipmentCode`).d('工作单元关系多个设备，请检查！'),
          });
        } else if (content.length === 0) {
          notification.error({
            message: intl.get(`${modelPrompt}.detail.equipmentCode`).d('工作单元没有关系的设备，请检查！'),
          });
        } else {
          notification.error({});
        }
      } else {
        notification.error({});
      }
    }
  };

  Modal.open({
    maskClosable: false,
    destroyOnClose: true,
    title: intl.get(`${modelPrompt}.detail.title`).d('不良记录明细'),
    style: {
      width: '800px',
    },
    children: (
      <>
        <Form dataSet={formDs} columns={1} labelWidth={200}>
          {btnType !== 'equipment' && <Lov name="ncCodeLov" />}
          {btnType !== 'equipment' && <Select name="ncCodeStatus" />}
          {(btnType !== 'updateCode' || btnType === 'equipment') && (
            <TextField name="rootCauseOperationCode" />
          )}
          {(btnType !== 'updateCode' || btnType === 'equipment') && (
            <Lov name="rootCauseWorkcellLov" onChange={onChangeData} />
          )}
          {(btnType !== 'updateCode' || btnType === 'equipment') && (
            <Lov name="rootCauseEquipmentCodeLov" disabled />
          )}
          {btnType !== 'equipment' && <Lov name="responsibleUserLov" />}
          {btnType !== 'equipment' && <TextField name="responsibleApartment" />}
          {btnType !== 'equipment' && <TextArea name="remark" />}
        </Form>
      </>
    ),
    drawer: true,
    onOk: async () => {
      if (!(await formDs.current?.validate())) return false;
      const temp = formDs.toData();
      const ncCodeStatusDesc = formDs.current!.getField('ncCodeStatus')!.getText();
      if (selectionType === 'equipment') {
        handleSubmit(temp, selectionType, btnType || null);
      } else if (selectionType !== 'edit') {
        const data: Array<any> = [];
        // temp[0].ncCodeLov.forEach((item, index) => {
        //   const obj = {
        //     lineNumber: index + 1,
        //     ...temp[0],
        //     ncCodeStatusDesc,
        //     ncCodeLov: item,
        //     ncCodeId: item.ncCodeId,
        //     ncCodeDesc: item.ncCodeDesc,
        //     enclosure: uuid(),
        //     uuid: uuid(),
        //   };
        //   data.push(obj);
        // });
        const obj = {
          lineNumber: 1,
          ...temp[0],
          ncCodeStatusDesc,
          ncCodeLov: temp[0].ncCodeLov,
          ncCodeId: temp[0].ncCodeLov.ncCodeId,
          ncCodeDesc: temp[0].ncCodeLov.ncCodeDesc,
          enclosure: uuid(),
          uuid: uuid(),
        };
        data.push(obj);
        handleSubmit(data, selectionType, btnType || null);
      } else {
        handleSubmit(temp, selectionType, btnType || null);
      }
      return true;
    },
  });
};

export default CreateDrawer;
