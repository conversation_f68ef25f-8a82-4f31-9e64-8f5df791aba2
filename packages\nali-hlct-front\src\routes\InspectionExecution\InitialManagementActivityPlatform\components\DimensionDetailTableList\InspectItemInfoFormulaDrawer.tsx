/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-03-29 14:37:30
 * @LastEditTime: 2023-05-18 16:42:42
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect } from 'react';
import intl from 'utils/intl';
import { Form, Select, NumberField, Lov, Table } from 'choerodon-ui/pro';
import { LabelLayout, LabelAlign } from 'choerodon-ui/pro/lib/form/enum';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import InputValueComponent from '../InputValueComponent';
import { FetchRuleCodeDetailConfig } from '@/utils';

const modelPrompt = 'tarzan.initialManagementActivity';
const { Item } = Form;
const { Option } = Select;

const InspectItemInfoDrawer = props => {
  const {
    canEdit,
    tenantId,
    customizeForm,
    warnNumberDS,
    trueNumberDS,
    falseNumberDS,
    editTableFormDS,
    formDs,
    tableDS,
    formulaListTableDs,
  } = props;

  const { run: fetchRuleCodeDetail, loading: fetchRuleCodeDetailLoading } = useRequest(
    FetchRuleCodeDetailConfig(),
    {
      manual: true,
      needPromise: true,
    },
  );

  // 数值类型-预警值
  const [warnNumberList, setWarnNumberList] = useState([]);
  // 数值类型-符合值
  const [trueNumberList, setTrueNumberList] = useState([]);
  // 数值类型-不符合值
  const [falseNumberList, setFalseNumberList] = useState([]);

  useEffect(() => {
    // 保存初始数据
    const _warningValueList = handleValueList(editTableFormDS.current?.get('warningValueList'));
    const _trueValueList = handleValueList(editTableFormDS.current?.get('trueValueList'));
    const _falseValueList = handleValueList(editTableFormDS.current?.get('falseValueList'));

    formulaListTableDs.loadData(editTableFormDS.current.get('formulaList'));

    setWarnNumberList(_warningValueList);
    warnNumberDS.loadData(_warningValueList);
    setTrueNumberList(_trueValueList);
    trueNumberDS.loadData(_trueValueList);
    setFalseNumberList(_falseValueList);
    falseNumberDS.loadData(_falseValueList);

    const { formulaMode } = editTableFormDS.toData()[0];
    if (formulaMode === 'CURRENT_ITEM') {
      formulaListTableDs.getField('inspectItemObject')?.set('disabled', true);
    } else {
      formulaListTableDs.getField('inspectItemObject')?.set('disabled', false);
    }
  }, [editTableFormDS]);

  useEffect(() => {
    if (trueNumberList.length < 1 && warnNumberList.length > 0) {
      setWarnNumberList([]);
    }
  }, [trueNumberList]);

  const formulaLovChange = async value => {
    const { formulaMode } = editTableFormDS.toData()[0];
    const formDsData = formDs.toData()[0];
    if (value) {
      const { ruleCode } = value;
      const ruleCodeDetail = await fetchRuleCodeDetail({
        params: {
          ruleCode,
          tenantId,
        },
      });
      if (ruleCodeDetail && typeof ruleCodeDetail === 'object') {
        const newRuleCodeDetail = ruleCodeDetail.map(item => {
          return {
            fieldCode: item.fieldCode,
            fieldName: item.fieldName,
            isRequired: item.fieldCode === 'decimalNumber' ? 'N' : item.isRequired,
            inspectItemId: formulaMode === 'CURRENT_ITEM' ? formDsData.inspectItemId : undefined,
            inspectItemDesc:
              formulaMode === 'CURRENT_ITEM' ? formDsData.inspectItemDesc : undefined,
          };
        });
        editTableFormDS.current.set('formulaList', newRuleCodeDetail);
        formulaListTableDs.loadData(newRuleCodeDetail);
      } else {
        editTableFormDS.current.set('formulaList', undefined);
      }
    } else {
      editTableFormDS.current.set('formulaList', undefined);
    }
  };

  // 处理多值数据
  const handleValueList = list => {
    return (list || []).map(item => {
      if (item.valueType === 'single') {
        item.singleValued = item.dataValue;
      } else if (item.valueType === 'section') {
        item.multiValued = {
          start: item.leftValue,
          end: item.rightValue,
        };
      }
      return item;
    });
  };

  const selectionOptions = () => {
    const filterList: any = [];
    tableDS.toData().forEach(item => {
      if (item.dataType === 'VALUE') {
        if (
          item.inspectItemId === formDs.current?.get('inspectItemId') ||
          (!item.taskCategory && !formDs.current?.get('taskCategory')) ||
          item.taskCategory === formDs.current?.get('taskCategory')
        ) {
          filterList.push(item);
        }
      }
    });
    return filterList.map(item => {
      return <Option value={item.inspectItemId}>{item.inspectItemDesc}</Option>;
    });
  };

  const handleFormulaModeChange = value => {
    const formDsData = formDs.toData()[0];
    if (value === 'CURRENT_ITEM') {
      formulaListTableDs.getField('inspectItemObject')?.set('disabled', true);
      formulaListTableDs.forEach(record => {
        record.set('inspectItemId', formDsData.inspectItemId);
        record.set('inspectItemDesc', formDsData.inspectItemDesc);
      });
    } else {
      formulaListTableDs.getField('inspectItemObject')?.set('disabled', false);
    }
  };

  const tableColumns = [
    {
      name: 'fieldCode',
    },
    {
      name: 'fieldName',
    },
    {
      name: 'inspectItemObject',
      editor: () => canEdit && <Select>{selectionOptions()}</Select>,
    },
  ];

  return (
    <>
      {customizeForm(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_BASIC.ITEM_DRAWER`,
        },
        <Form
          dataSet={editTableFormDS}
          columns={2}
          labelWidth={112}
          disabled={!canEdit}
          labelLayout={LabelLayout.horizontal}
          labelAlign={LabelAlign.right}
        >
          <Lov name="uomLov" />
          <NumberField name="decimalNumber" />
          <Select name="processMode" />
          <Item
            name="trueValue"
            rowSpan={trueNumberList.length || 1}
            label={intl.get(`${modelPrompt}.model.trueValue`).d('符合值')}
            style={{ padding: 0 }}
          >
            <InputValueComponent
              {...{
                showStandard: true,
                canEdit: canEdit && falseNumberList.length < 1,
                numberDS: trueNumberDS,
                numberList: trueNumberList,
                setNumberList: setTrueNumberList,
              }}
            />
          </Item>
          <Item
            name="falseValue"
            rowSpan={falseNumberList.length || 1}
            label={intl.get(`${modelPrompt}.model.falseValue`).d('不符合值')}
            style={{ padding: 0 }}
          >
            <InputValueComponent
              {...{
                canEdit: canEdit && trueNumberList.length < 1,
                numberDS: falseNumberDS,
                numberList: falseNumberList,
                setNumberList: setFalseNumberList,
              }}
            />
          </Item>
          <Item
            name="earlyWarningValue"
            rowSpan={warnNumberList.length || 1}
            label={intl.get(`${modelPrompt}.model.earlyWarningValue`).d('预警值')}
            style={{ padding: 0 }}
          >
            <InputValueComponent
              {...{
                canEdit: canEdit && trueNumberList.length > 0,
                numberDS: warnNumberDS,
                numberList: warnNumberList,
                setNumberList: setWarnNumberList,
              }}
            />
          </Item>
          <Select name="formulaMode" onChange={handleFormulaModeChange} />
          <Select name="formulaDisplayPosition" />
          <Lov
            name="formulaLov"
            disabled={fetchRuleCodeDetailLoading}
            onChange={formulaLovChange}
          />
          <Select name="dimension" />
        </Form>,
      )}
      <Table
        dataSet={formulaListTableDs}
        columns={tableColumns}
        filter={record => {
          return record.get('fieldCode') !== 'decimalNumber';
        }}
      />
    </>
  );
};

export default InspectItemInfoDrawer;
