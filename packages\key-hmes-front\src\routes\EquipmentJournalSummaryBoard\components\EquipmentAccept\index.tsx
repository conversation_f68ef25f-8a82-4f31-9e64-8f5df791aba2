import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import request from 'utils/request';
import { BASIC } from '@/utils/config';
import DashboardCard from '../DashboardCard.jsx';

const tenantId = getCurrentOrganizationId();
// 验收情况分布
const url = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-ledger-summary/acceptance-status`;

const EquipmentAccept = ({isFullScreen, timers, assetSetId }) => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>(null);

  const fetchData = useCallback(async () => {
    if (assetSetId) {
      const res = await request(url, {
        method: 'GET',
        query: { assetSetId },
      });
      setData({
        haveAccepted: res?.acceptedStatusData ? res?.acceptedStatusData[0] : 0,
        notAccepted: res?.acceptedStatusData ? res?.acceptedStatusData[1] : 0,
      });
    }
  }, [assetSetId]);

  useEffect(()=>{
    let time;
    if( timers ) {
      time = setInterval(() => {
        fetchData();
      }, (timers)*60000)
    } else if( assetSetId ){
      fetchData();
    }
    return () => {
      clearInterval(time)
    }
  },[timers, assetSetId])

  const option: any = useMemo(() => {
    return {
      title:{
        top:'3%',
        text: '验收情况分布',
        left: 'center',
        textStyle: {
          fontWeight: 'bold', // 加粗
          color: '#00fff4',
        },
      },
      color:['#5470c6', '#ef6567'],
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          return `${params.name}: ${params.name === '已验收' ? data?.haveAccepted : data?.notAccepted}%`;
        },
      },
      legend: {
        orient: 'vertical',
        bottom: 'bottom',
        textStyle: {
          color: '#fff',
        },
      },
      series: [
        {
          name: '验收情况分布',
          type: 'pie',
          radius: '50%',
          data: [
            { value: data?.haveAccepted || 0, name: '已验收' },
            { value: data?.notAccepted ||0, name: '未验收' },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          label: {
            show: true,
            color:'#FFF',
            formatter: (params) => {
              return `${params.name}: ${params.name === '已验收' ? data?.haveAccepted : data?.notAccepted}%`;
            },
          },
        },
      ],
    };
  }, [data]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  return (
    <DashboardCard style={{ height: '100%' }}>
      {isFullScreen?
        (<div style={{ width: '100%', height: '100%' }} >
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>):
        <div style={{ width: '100%', height: '100%' }} >
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>
      }
    </DashboardCard>
  );
};
export default EquipmentAccept;
