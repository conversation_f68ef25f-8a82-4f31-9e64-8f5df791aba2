// 工单投料 接口

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();


// 获取工单下各组件及替代料
export function FetchWoFeeding(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-feeding/wo-component/or/substitute/list/ui`,
    method: 'GET',
  };
}

// 物料批扫描
export function ScanBarcode(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-feeding/material-lot/scan/ui`,
    method: 'GET',
  };
}

// 装配组件执行
export function AssembleDone(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-feeding/component/assemble/ui`,
    method: 'POST',
  };
}

// 撤回操作
export function RevocationDone(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-feeding/com/material-lot/revocation/ui`,
    method: 'POST',
  };
}

// 移除组件执行
export function RemoveDone(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-feeding/component/remove/ui`,
    method: 'POST',
  };
}
