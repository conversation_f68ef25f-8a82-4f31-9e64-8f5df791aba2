/**
 * @Description: 物料维护-站点分配-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-07-28 14:42:15
 * @LastEditTime: 2022-11-11 14:17:14
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.product.materialManager.model.materialManager';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'materialSiteId',
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE2',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      bind: 'siteLov.siteName',
    },
    {
      name: 'typeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.typeDesc`).d('站点类型'),
      bind: 'siteLov.typeDesc',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionFlag`).d('启用版本'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'productionVersionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionVersionFlag`).d('启用生产版本'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material-site/site/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };