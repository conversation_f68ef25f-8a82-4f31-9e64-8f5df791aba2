/*
 * @Description: ERP 属性
 * @Author: YinWQ
 * @Date: 2023-07-19 17:17:51
 * @LastEditors: YinWQ
 * @LastEditTime: 2023-07-21 09:54:35
 */
import React, { useMemo, useState, useEffect } from 'react';
import { Table, Lov, Select } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import { isNull } from 'lodash';
import { useRequest } from '@components/tarzan-hooks';
import { DeleteItems } from '../service'

const { Option } = Select;

const ERPInfoTab = props => {
  const { dataSet, erpDataSet, erpData, canEdit, path, siteData, isSiteIdsChange } = props;

  // 表格勾选数据
  const [tableSelectList, setTableSelectList] = useState([]);
  // 记录 erp 属性表格中填写的站点
  const [selectSiteList, setSelectSiteList] = useState([]);
  // 获取站点数据
  const [siteListData, setSiteListData] = useState([]);
  // 监听关系表格勾选数据
  const handleTableSelect = ({ dataSet }) => {
    setTableSelectList(dataSet.selected || []);
  };
  // console.log(selectSiteList)
  // 声明删除调用接口
  const { run: runDelete } = useRequest(DeleteItems(), { manual: true });

  // 添加事件监听 删除按钮禁用状态
  useEffect(() => {
    erpDataSet.addEventListener('select', handleTableSelect);
    erpDataSet.addEventListener('selectAll', handleTableSelect);
    erpDataSet.addEventListener('unselect', handleTableSelect);
    erpDataSet.addEventListener('unselectAll', handleTableSelect);
    return () => {
      erpDataSet.removeEventListener('select', handleTableSelect);
      erpDataSet.removeEventListener('selectAll', handleTableSelect);
      erpDataSet.removeEventListener('unselect', handleTableSelect);
      erpDataSet.removeEventListener('unselectAll', handleTableSelect);
    };
  }, []);

  // 给 erp 表格站点下拉框数据范围赋值
  useEffect(() => {
    const siteListDataTemp = siteData.filter(item =>
      dataSet.current.toData().siteIds.includes(item.siteId),
    );
    setSiteListData(siteListDataTemp);

  }, [siteData, canEdit, isSiteIdsChange, erpData]);

  // 给 erp 表格赋值 主要是处理站点名称显示格式
  useEffect(() => {
    if (siteData && erpData.length > 0) {
      const erpDataTemp = erpData.map(item => {
        const siteInfoItem = siteData.filter(siteItem => siteItem.siteId === item.siteId);
        const erpSiteName =
          siteInfoItem.length > 0
            ? `${siteInfoItem[0].siteTypeDesc}-${siteInfoItem[0].siteCode}-${siteInfoItem[0].siteName}`
            : '';
        return {
          ...item,
          siteName: erpSiteName,
        };
      });
      erpDataSet.loadData(erpDataTemp);
    } else {
      erpDataSet.loadData(erpData);
    }
    // 添加 erp 站点下拉框选择禁用列表
    if (erpData.length > 0) {
      const temp = erpData.map(item => item.siteId)
      setSelectSiteList(temp);
    } else {
      setSelectSiteList([]);
    }
  }, [erpData, siteData, canEdit]);

  // 行表按钮
  const buttons = [
    <Popconfirm
      title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
      onConfirm={() => handleBatchDelete()}
      okText={intl.get('tarzan.common.button.confirm').d('确认')}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
    >
      <PermissionButton
        type="c7n-pro"
        icon="delete_black-o"
        disabled={!canEdit || tableSelectList.length < 1}
        funcType="flat"
        shape="circle"
        size="small"
        permissionList={[
          {
            code: `dist.button.delete`,
            type: 'button',
            meaning: '详情页-删除按钮',
          },
        ]}
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
    </Popconfirm>,
    <PermissionButton
      type="c7n-pro"
      icon="add"
      disabled={!canEdit}
      onClick={() => handleAddEdit()}
      funcType="flat"
      shape="circle"
      size="small"
      permissionList={[
        {
          code: `${path}.button.edit`,
          type: 'button',
          meaning: '详情页-编辑新建删除复制按钮',
        },
      ]}
    >
      {intl.get('hzero.common.button.add').d('新增')}
    </PermissionButton>,
  ];

  // erp 表格删除按钮 （新增没有 id 直接删除选中数据 编辑时 将id整理成 列表调用后端接口成功后删除所有数据）
  const handleBatchDelete = async () => {
    const delDataList = erpDataSet.selected
      .filter(item => item.get('releationId'))
      .map(item => item.get('releationId'));
    if (delDataList.length > 0) {
      runDelete({
        params: delDataList,
        onSuccess: () => {
          erpDataSet.remove(erpDataSet.selected);
          const temp = erpDataSet.toData().map(item => item.siteId)
          setSelectSiteList(temp)
        },
      });
    } else {
      erpDataSet.remove(erpDataSet.selected);
      const temp = erpDataSet.toData().map(item => item.siteId);
      setSelectSiteList(temp);
    }
  };
  const handleAddEdit = () => {
    erpDataSet.create({}, 0);
  };

  // 处理站点为空时站点列表中可选数据
  const handleChangeSite = (e, record) => {
    if (isNull(e)) {
      const selectSiteListTemp = selectSiteList.filter(item => item !== record.get('siteId'));
      setSelectSiteList(selectSiteListTemp);
      record.set('siteId', null);
    }
  };

  // 选择站点时记录当前 erp 表格中所有选中的站点 ID
  const setCurrentSiteId = (siteId, record) => {
    record.set('siteId', siteId);
    // 计算选中的站点
    const currentErpTableData = erpDataSet.toData();
    const selectSiteListTemp = currentErpTableData.map(item => item.siteId)
    setSelectSiteList(selectSiteListTemp);
  }

  const erpTableColumns = useMemo(
    () => [
      {
        name: 'siteName',
        editor: record => {
          if (!canEdit) {
            return false;
          }
          return (
            <Select name="siteName" onChange={e => handleChangeSite(e, record)}>
              {siteListData.length > 0 &&
                siteListData.map(val => {
                  const { siteId, siteTypeDesc, siteCode, siteName } = val || {};
                  return (
                    <Option
                      disabled={selectSiteList.includes(siteId)}
                      value={`${siteTypeDesc}-${siteCode}-${siteName}`}
                      onClick={() => setCurrentSiteId(siteId, record)}
                    >
                      {`${siteTypeDesc}-${siteCode}-${siteName}`}
                    </Option>
                  );
                })}
            </Select>
          );
        },
      },
      {
        name: 'plantSiteObj',
        editor: () => {
          if (!canEdit) {
            return false;
          }
          return <Lov name="plantSiteObj" />;
        },
      },
      {
        name: 'subinv',
        editor: canEdit,
      },
    ],
    [siteListData, selectSiteList],
  );

  return (
    <Table
      dataSet={erpDataSet}
      columns={erpTableColumns}
      buttons={buttons}
      searchCode="ERPPropertyTabTable"
      customizedCode="ERPPropertyTabTable"
    />
  );
};
export default ERPInfoTab;
