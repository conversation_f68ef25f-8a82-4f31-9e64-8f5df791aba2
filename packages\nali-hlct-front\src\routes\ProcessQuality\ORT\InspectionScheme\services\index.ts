/**
 * @Description: ORT检验方案维护
 */
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { HZERO_IAM } from 'utils/config';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存
 * @function SaveVerification
 * @returns {object} fetch Promise
 */
export function SaveVerification(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ort-inspect-scheme/list/update`,
    method: 'POST',
  };
}

/**
 * 查询用户默认站点
 * @function GetDefaultSite
 * @returns {object} fetch Promise
 */
export function GetDefaultSite(): object {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/ui`,
    method: 'GET',
  };
}

/**
 * 查询用户所有角色
 * @function GetUserRole
 * @returns {object} fetch Promise
 */
export function GetUserRole(): object {
  return {
    url: `${HZERO_IAM}/hzero/v1/${tenantId}/member-roles/user-all-roles/${getCurrentUserId()}`,
    method: 'GET',
  };
}
