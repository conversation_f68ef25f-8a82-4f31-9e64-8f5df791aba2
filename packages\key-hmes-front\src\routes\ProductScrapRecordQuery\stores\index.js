import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import moment from 'moment';

const tenantId = getCurrentOrganizationId();
// const Host = `/yp-mes-38510`;
const modelPrompt = 'tarzan.hmes.productScrapRecordQuery';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'ncRecordId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      },
      {
        name: 'organizationCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.organizationCode`).d('产线编码'),
      },
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码标识'),
      },
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      },
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'ncCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCode`).d('不良代码编码'),
      },
      {
        name: 'ncDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncDescription`).d('不良代码名称'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('判定工艺'),
      },
      {
        name: 'qualityStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
      },
      {
        name: 'disposalFunctionDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalFunctionDescription`).d('报废状态'),
      },
      {
        name: 'disposalTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalTime`).d('报废时间'),
      },
      {
        name: 'scrapQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.scrapQty`).d('报废数量'),
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('操作人'),
      },
    ],
    queryFields: [
      {
        name: 'identifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.identifications`).d('条码号'),
      },
      {
        name: 'prodLineLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineLov`).d('产线编码'),
        lovCode: 'MT.MODEL.PRODLINE',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'prodLineCode',
        multiple: true,
      },
      {
        name: 'prodLineIds',
        bind: 'prodLineLov.prodLineId',
      },
      {
        name: 'equipmentLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.equipmentLov`).d('设备编码'),
        lovCode: 'HME.DATA_RECORD_EQUIPMENT',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              prodlineIds: record.get('prodLineIds'),
            }
          },
        },
        ignore: 'always',
        textField: 'equipmentCode',
        multiple: true,
      },
      {
        name: 'equipmentIds',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'materialLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.collectMaterialLov`).d('物料编码'),
        lovCode: 'MT.MATERIAL',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'materialCode',
        multiple: true,
      },
      {
        name: 'materialIds',
        bind: 'materialLov.materialId',
      },
      {
        name: 'defaultNcCodeLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.defaultNcCodeLov`).d('不良代码'),
        lovCode: 'MT.METHOD.NC_CODE',
        ignore: 'always',
        textField: 'ncCode',
        valueField: 'ncCodeId',
        lovPara: {
          tenantId,
        },
        multiple: true,
      },
      {
        name: 'ncCodeIds',
        bind: 'defaultNcCodeLov.ncCodeId',
      },
      {
        name: 'workOrderNumstr',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNums`).d('生产指令'),
      },
      {
        name: 'startTime',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.startTime`).d('时间开始'),
        max: 'endTime',
        dynamicProps: {
          min: ({ record }) => record?.get('endTime')?moment(record?.get('endTime')).subtract(3, 'M'):null,
        },
      },
      {
        name: 'endTime',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.endTime`).d('时间结束'),
        min: 'startTime',
        dynamicProps: {
          max: ({ record }) => record?.get('startTime')?moment(record?.get('startTime')).add(3, 'M'):null,
        },
      },
    ],
    transport: {
      read: ({ data }) => {
        const code = data.identifications;
        const code2 = data.workOrderNumstr;
        const dataParams = {
          ...data,
          processBarcodes: data.identifications ? code.split(',') : null,
          workOrderNums: data.workOrderNumstr ? code2.split(',') : null,
        };
        if (!code) {
          delete dataParams.processBarcodes;
        }
        if (!code2) {
          delete dataParams.workOrderNums;
        }
        return {
          url: `${Host}/v1/${tenantId}/hme-product-report/scrap/record/query/ui`,
          data: dataParams,
          method: 'POST',
        };
      },
    },
  };
};

const drawerDS = () => {
  return {
    name: 'drawerDS',
    primaryKey: 'ncCodeId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-product-report/detail/query/ui`,
          method: 'GET',
        };
      },
    },
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码标识'),
      },
      {
        name: 'ncCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCode`).d('不良代码编码'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('不良工艺'),
      },
      {
        name: 'ncStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncStatusDesc`).d('不良代码状态'),
      },
      {
        name: 'ncRecordTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordTime`).d('不良记录时间'),
      },
      {
        name: 'ncUserName',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncUserName`).d('不良记录人'),
      },
      {
        name: 'lastUpdateTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateTime`).d('不良关闭时间'),
      },
    ],
  };
};

export { tableDS, drawerDS };

