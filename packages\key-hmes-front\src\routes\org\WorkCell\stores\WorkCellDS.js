/**
 * @Description: 工作单元维护DS
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 16:54:28
 * @LastEditTime: 2023-05-18 11:33:09
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.model.org.workcell';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  selection: false,
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元短描述'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('工作单元长描述'),
    },
    {
      name: 'workcellType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellType`).d('工作单元类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=WORKCELL_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'workcellLocation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellLocation`).d('工作单元位置'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元短描述'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('工作单元长描述'),
    },
    {
      name: 'workcellTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellType`).d('工作单元类型'),
    },
    {
      name: 'workcellLocation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellLocation`).d('工作单元位置'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-workcell/query/ui`,
        method: 'get',
      };
    },
  },
});

const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'workcell',
      type: FieldType.object,
      required: true,
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元编码'),
      bind: 'workcell.workcellCode',
    },
    {
      name: 'workcellName',
      type: FieldType.intl,
      required: true,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元短描述'),
      bind: 'workcell.workcellName',
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.description`).d('工作单元长描述'),
      bind: 'workcell.description',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
      bind: 'workcell.enableFlag',
    },
    {
      name: 'workcellType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=WORKCELL_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.workcellType`).d('工作单元类型'),
      dynamicProps: {
        disabled({ record }) {
          return record.data.workcellId;
        },
      },
      bind: 'workcell.workcellType',
    },
    {
      name: 'workcellLocation',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.workcellLocation`).d('工作单元位置'),
      bind: 'workcell.workcellLocation',
    },
    {
      name: 'workcellId',
      type: FieldType.string,
      bind: 'workcell.workcellId',
    },
    {
      name: 'workcellManufacturing',
      type: FieldType.object,
    },
    {
      name: 'backwardShiftNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.backwardShiftNumber`).d('可向后操作班次数'),
      min: 0,
      max: 1000000,
      step: 1,
      bind: 'workcellManufacturing.backwardShiftNumber',
    },
    {
      name: 'forwardShiftNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.forwardShiftNumber`).d('可向前操作班次数'),
      min: 0,
      max: 1000000,
      step: 1,
      bind: 'workcellManufacturing.forwardShiftNumber',
    },
    {
      name: 'workcellId',
      type: FieldType.string,
      bind: 'workcellManufacturing.workcellId',
    },
  ],
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.model.domain.entity.MtModWorkcell';
      return {
        data: { workcellId: (record.data.workcell || {}).workcellId },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_MODEL}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-workcell/record/query/ui`,
        method: 'get',
        transformResponse: response => {
          const _response = getResponse(JSON.parse(response));
          if (_response && _response.rows) {
            const _item = {
              ..._response.rows,
              ..._response.rows.workcell,
            };
            return { rows: _item };
          }
          return { rows: {} };
        },
      };
    },
    submit: ({ data }) => {
      const {
        workcellManufacturing = {},
        _tls = {},
        workcellName,
        description,
        workcellLocation,
        ...others
      } = data[0];
      let { workcell = {} } = data[0];
      workcell = {
        ...workcell,
        ...others,
        _tls,
        workcellName,
        description,
        workcellLocation,
      };

      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-workcell/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.WORKCELL`,
        method: 'POST',
        data: {
          workcell,
          workcellManufacturing,
          workcellSchedule: {},
        },
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
        // transformResponse: (response) => {
        //   // TODO 这里这样写，dataset会报错【TypeError: Cannot read property '_status' of undefined】，但是不影响使用，后续需查看源码给出处理方法
        //   const res = getResponse(JSON.parse(response))
        //     ? [getResponse(JSON.parse(response))]
        //     : [JSON.parse(response)];
        //   return res;
        // },
      };
    },
  },
});

export { tableDS, detailDS };
