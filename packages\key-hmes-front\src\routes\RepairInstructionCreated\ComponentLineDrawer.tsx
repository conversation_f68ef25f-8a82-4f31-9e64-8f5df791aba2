/**
 * @Description: 装配清单 - 组件行编辑抽屉（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/8/11 16:17
 * @LastEditTime: 2022/8/11 16:17
 * @LastEditors: <EMAIL>
 */
import React, { FC } from 'react';
import {
  Switch,
  NumberField,
  Lov,
  Select,
  TextField,
  DateTimePicker,
  Form,
} from 'choerodon-ui/pro';
import { C7nFormItemSort } from '@components/tarzan-ui';
import { bomComponentTypeDs } from './stores/DetailDS';

interface ComponentLineDrawerProps {
  record: any;
  canEdit: Boolean;
}

export const ComponentLineDrawer: FC<ComponentLineDrawerProps> = props => {
  const { record, canEdit } = props;

  // 组件类型变化的回调
  const handleBomTypeChange = (val, record) => {
    if (val) {
      bomComponentTypeDs.toData().forEach((item: any) => {
        if (item.typeCode === val) {
          record.set('bomComponentTypeDesc', item.description);
        }
      });
    } else {
      record.set('bomComponentTypeDesc', undefined);
    }
  };

  // 损耗策略变化的回调
  const handleAttritionPolicyChange = (val, record) => {
    if (val === '1') {
      record.set('attritionChance', undefined);
    } else if (val === '2') {
      record.set('attritionQty', undefined);
    } else if (!val) {
      record.set('attritionChance', undefined);
      record.set('attritionQty', undefined);
    }
  };

  return (
    <Form labelWidth={112} record={record} columns={2} disabled={!canEdit}>
      <NumberField name="lineNumber" />
      <C7nFormItemSort name="materialLov" itemWidth={['70%', '30%']}>
        <Lov name="materialLov" />
        <Select name="revisionCode" noCache />
      </C7nFormItemSort>
      <TextField name="materialName" />
      <TextField name="uomName" />
      <NumberField name="unitQty" />
      <Select name="bomComponentType" onChange={val => handleBomTypeChange(val, record)} />
      <Select name="assembleMethod" />
      <Select name="attritionPolicy" onChange={val => handleAttritionPolicyChange(val, record)} />
      <NumberField name="attritionChance" />
      <NumberField name="attritionQty" />
      <DateTimePicker name="dateFrom" />
      <DateTimePicker name="dateTo" />
      <Lov name="issuedLocatorLov" />
      <TextField name="issuedLocatorName" />
      <Switch name="keyMaterialFlag" />
      <Switch name="assembleAsReqFlag" />
    </Form>
  );
};
