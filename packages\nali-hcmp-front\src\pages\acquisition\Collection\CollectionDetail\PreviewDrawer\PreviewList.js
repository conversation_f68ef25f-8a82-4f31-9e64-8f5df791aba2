/**
 * @Description: 数据项预览列表
 * @Author: <<EMAIL>>
 * @Date: 2021-04-28 15:54:13
 * @LastEditTime: 2021-04-29 18:27:13
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { List } from 'choerodon-ui';
import ListItemHeader from './ListItemHeader';
import ListItemContent from './ListItemContent';

const PreviewList = props => {
  const { dataSource, setItemValidateInfo } = props;
  return (
    <List
      split
      itemLayout="vertical"
      dataSource={dataSource}
      renderItem={item => (
        <List.Item>
          <List.Item.Meta title={<ListItemHeader rule={item} />} style={{ marginBottom: 0 }} />
          <ListItemContent rule={item} setItemValidateInfo={setItemValidateInfo} />
        </List.Item>
      )}
    />
  );
};

export default PreviewList;
