/*
 * @Author: Nanjiangqi
 * @Date: 2022-01-10 15:24:23
 * @LastEditors: Nanjiangqi
 * @LastEditTime: 22022-01-10 15:24:23
 * @Description:
 */
import React, { useEffect } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';

interface DetailModalProps {
  tableDs: DataSet;
  instructionDocLineId: number;
  instructionDocType: string;
  identifyType: string;
  customizeTable: any;
}

const CreateMaterial: React.FC<DetailModalProps> = ({
  tableDs,
  instructionDocLineId,
  instructionDocType,
  identifyType,
  customizeTable,
}) => {
  useEffect(() => {
    handleQuery();
  }, [instructionDocLineId]);

  const handleQuery = async () => {
    tableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
    tableDs.setQueryParameter('instructionDocType', instructionDocType);
    tableDs.setQueryParameter('identifyType', identifyType)
    await tableDs.query();
  };

  // 物料批的表格columns
  const columns: ColumnProps[] = [
    {
      name: 'materialIdentification',
      align: ColumnAlign.left,
      width: 150,
    },
    {
      name: 'materialLotStatusDesc',
      align: ColumnAlign.left,
      width: 90,
    },
    {
      name: 'identification',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'sumActualQty',
      align: ColumnAlign.right,
      width: 80,
    },
    {
      name: 'uomCode',
      align: ColumnAlign.left,
      width: 120,
    },
    {
      name: 'lot',
      align: ColumnAlign.left,
      width: 120,
    },
    {
      name: 'receiveLocatorCode',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'receiveData',
      align: ColumnAlign.center,
      width: 200,
    },
    {
      name: 'receiveBy',
      align: ColumnAlign.left,
      width: 100,
    },
    {
      name: 'putOnLocatorCode',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'putOnData',
      align: ColumnAlign.center,
      width: 200,
    },
    {
      name: 'putOnBy',
      align: ColumnAlign.left,
      width: 100,
    },
  ];

  // 物料明细
  const columns2: ColumnProps[] = [
    {
      name: 'materialCode',
      align: ColumnAlign.left,
      width: 120,
    },
    {
      name: 'materialName',
      align: ColumnAlign.left,
      width: 120,
    },
    {
      name: 'qualityStatusDesc',
      align: ColumnAlign.left,
      width: 90,
    },
    {
      name: 'sumActualQty',
      align: ColumnAlign.right,
      width: 80,
    },
    {
      name: 'uomCode',
      align: ColumnAlign.left,
      width: 120,
    },
    {
      name: 'lot',
      align: ColumnAlign.left,
      width: 120,
    },
    {
      name: 'receiveLocatorCode',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'receiveData',
      align: ColumnAlign.center,
      width: 200,
    },
    {
      name: 'receiveBy',
      align: ColumnAlign.left,
      width: 100,
    },
    {
      name: 'putOnLocatorCode',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'putOnData',
      align: ColumnAlign.center,
      width: 200,
    },
    {
      name: 'putOnBy',
      align: ColumnAlign.left,
      width: 100,
    },
  ];

  const columsFun = identifyType === 'MATERIAL_LOT' ? columns : columns2;

  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST_LINE_DETAIL.QUERY`,
    },
    <Table dataSet={tableDs} columns={columsFun} />,
  )
};

export default CreateMaterial;
