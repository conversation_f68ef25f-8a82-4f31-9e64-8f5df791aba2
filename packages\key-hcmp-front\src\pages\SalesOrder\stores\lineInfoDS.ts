import { Record } from 'choerodon-ui/dataset';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/dataset/data-set/enum';
import DataSet, { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import moment from 'moment';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { BASIC } from '@utils/config';

const intlPrefix = 'tarzan.aps.sales.managememt';
const tenantId = getCurrentOrganizationId();
const userId = getCurrentUserId();

const lineInfoDS: () => DataSetProps = () => ({
  dataKey: 'rows',
  selection: DataSetSelection.multiple,
  queryDataSet: new DataSet({
    fields: [
      {
        name: 'soLineNum',
        label: intl.get(`${intlPrefix}.line.soLineNum`).d('销售订单行号'),
        type: FieldType.string,
      },
      {
        name: 'siteCode',
        label: intl.get(`${intlPrefix}.line.siteCode`).d('发运站点编码'),
        type: FieldType.string,
      },
      {
        name: 'materialCode',
        label: intl.get(`${intlPrefix}.line.materialCode`).d('物料编码'),
        type: FieldType.string,
      },
      {
        name: 'lineType',
        label: intl.get(`${intlPrefix}.line.lineType`).d('行类型'),
        type: FieldType.string,
        lookupCode: 'SO_LINE_TYPE',
      },
      {
        name: 'lineStatus',
        label: intl.get(`${intlPrefix}.line.lineStatus`).d('行状态'),
        type: FieldType.string,
        lookupCode: 'SO_LINE_STATUS',
      },
      {
        name: 'scheduleShipDate',
        label: intl.get(`${intlPrefix}.line.scheduleShipDate`).d('计划发运日期'),
        type: FieldType.date,
        transformRequest: (value, record) => {
          return record.get('scheduleShipDate') ? moment(value).format('YYYY-MM-DD') : '';
        },
      },
      {
        name: 'scheduleArrivalDate',
        label: intl.get(`${intlPrefix}.line.scheduleArrivalDate`).d('计划到达日期'),
        type: FieldType.date,
        transformRequest: (value, record) => {
          return record.get('scheduleArrivalDate') ? moment(value).format('YYYY-MM-DD') : '';
        },
      },
      {
        name: 'actualShipDate',
        label: intl.get(`${intlPrefix}.line.actualShipDate`).d('实际发运日期'),
        type: FieldType.date,
        transformRequest: (value, record) => {
          return record.get('actualShipDate') ? moment(value).format('YYYY-MM-DD') : '';
        },
      },
      {
        name: 'actualArrivalDate',
        label: intl.get(`${intlPrefix}.line.actualArrivalDate`).d('实际交货日期'),
        type: FieldType.date,
        transformRequest: (value, record) => {
          return record.get('actualArrivalDate') ? moment(value).format('YYYY-MM-DD') : '';
        },
      },
      {
        name: 'remark',
        label: intl.get(`${intlPrefix}.line.actualArrivalDate`).d('行说明'),
        type: FieldType.string,
      },
    ],
  }),
  fields: [
    { name: 'soId' },
    { name: 'soLineId', type: FieldType.number },
    {
      name: 'soLineNum',
      label: intl.get(`${intlPrefix}.line.soLineNum`).d('销售订单行号'),
      type: FieldType.string,
    },
    {
      name: 'siteCode',
      label: intl.get(`${intlPrefix}.line.siteCode`).d('发运站点编码'),
      type: FieldType.object,
      lovCode: 'MT.APS.SITE',
      lovPara: {
        tenantId,
        userId,
        siteType: 'MANUFACTURING',
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'shipFromSiteId',
      label: intl.get(`${intlPrefix}.line.shipFromSiteId`).d('发运站点id'),
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('siteCode') && record.get('siteCode').siteId ? 'siteCode.siteId' : '';
        },
      },
    },
    {
      name: 'materialId',
      label: intl.get(`${intlPrefix}.line.materialId`).d('物料id'),
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('materialCode') && record.get('materialCode').materialId
            ? 'materialCode.materialId'
            : '';
        },
      },
    },
    {
      name: 'materialCode',
      label: intl.get(`${intlPrefix}.line.materialCode`).d('物料编码'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'MT.APS.MATERIAL.PERMISSION',
      required: true,
      lovPara: {
        tenantId,
        userId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return record && !record.get('siteCode');
        },
      },
      cascadeMap: { siteId: 'shipFromSiteId' },
    },
    {
      name: 'materialName',
      label: intl.get(`${intlPrefix}.line.materialName`).d('物料名称'),
      type: FieldType.string,
      ignore: FieldIgnore.always,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('materialCode') && record.get('materialCode').materialName
            ? 'materialCode.materialName'
            : '';
        },
      },
    },
    {
      name: 'revisionCode',
      label: intl.get(`${intlPrefix}.line.revisionCode`).d('物料版本'),
      type: FieldType.string,
      textField: 'revisionCode',
      valueField: 'revisionCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled({ record }) {
          return !(record.get('shipFromSiteId')&&record.get('materialId'));
        },
        lookupUrl({ record }) {
          if (record.get('shipFromSiteId') && record.get('materialId')) {
            return `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-order-lines/get/revision/code?shipFromSiteId=${record.get(
              'shipFromSiteId'
            )}&materialId=${record.get('materialId')}`

          }
        },
      },
    },
    {
      name: 'uomName',
      label: intl.get(`${intlPrefix}.line.uomName`).d('单位'),
      type: FieldType.string,
      ignore: FieldIgnore.always,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('materialCode') && record.get('materialCode').materialName
            ? 'materialCode.uomName'
            : '';
        },
      },
    },
    {
      name: 'uomId',
      label: intl.get(`${intlPrefix}.line.uomId`).d('单位id'),
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('materialCode') && record.get('materialCode').uomId
            ? 'materialCode.uomId'
            : '';
        },
      },
    },
    {
      name: 'lineType',
      label: intl.get(`${intlPrefix}.line.lineType`).d('行类型'),
      type: FieldType.string,
      required: true,
      lookupCode: 'SO_LINE_TYPE',
    },
    {
      name: 'lineStatus',
      label: intl.get(`${intlPrefix}.line.lineStatus`).d('行状态'),
      type: FieldType.string,
      defaultValue: 'USABLE',
      lookupCode: 'SO_LINE_STATUS',
    },
    {
      name: 'orderedQuantity',
      label: intl.get(`${intlPrefix}.line.orderedQuantity`).d('订购数量'),
      type: FieldType.number,
      required: true,
      min: 0,
      validator: (value, name, record: any) => {
        if (value <= 0) {
          return '订购数量必须大于0';
        }
        const r: Record = record as Record;
        if (
          r.get('shippedQuantity') !== undefined &&
          r.get('reservedQuantity') !== undefined &&
          r.get('cancelledQuantity') !== undefined
        ) {
          const result =
            r.get('shippedQuantity') + r.get('reservedQuantity') + r.get('cancelledQuantity');
          if (isNaN(Number(result))) {
            return '数据有误，非数字';
          }
          return value >= result ? true : '订购数量必须大于等于已发运数量与预留数量与取消数量的和';
        }
        return true;
      },
    },
    {
      name: 'shippedQuantity',
      label: intl.get(`${intlPrefix}.line.shippedQuantity`).d('已发运数量'),
      type: FieldType.number,
      min: 0,
      defaultValue: 0,
    },
    {
      name: 'reservedQuantity',
      label: intl.get(`${intlPrefix}.line.reservedQuantity`).d('预留数量'),
      type: FieldType.number,
      min: 0,
      defaultValue: 0,
    },
    {
      name: 'cancelledQuantity',
      label: intl.get(`${intlPrefix}.line.cancelledQuantity`).d('取消数量'),
      type: FieldType.number,
      min: 0,
      defaultValue: 0,
    },
    {
      name: 'scheduleShipDate',
      label: intl.get(`${intlPrefix}.line.scheduleShipDate`).d('计划发运日期'),
      type: FieldType.date,
    },
    {
      name: 'scheduleArrivalDate',
      label: intl.get(`${intlPrefix}.line.scheduleArrivalDate`).d('计划到达日期'),
      type: FieldType.date,
    },
    {
      name: 'actualShipDate',
      label: intl.get(`${intlPrefix}.line.actualShipDate`).d('实际发运日期'),
      type: FieldType.date,
    },
    {
      name: 'actualArrivalDate',
      label: intl.get(`${intlPrefix}.line.actualArrivalDate`).d('实际交货日期'),
      type: FieldType.date,
    },
    {
      name: 'contractNum',
      label: intl.get(`${intlPrefix}.line.contractNum`).d('合同编码'),
      type: FieldType.string,
    },
    {
      name: 'customerPoNum',
      label: intl.get(`${intlPrefix}.line.customerPoNum`).d('客户采购订单编码'),
      type: FieldType.string,
    },
    {
      name: 'customerPoLineNum',
      label: intl.get(`${intlPrefix}.line.customerPoLineNum`).d('客户采购订单行号'),
      type: FieldType.number,
    },
    {
      name: 'parentLineNum',
      label: intl.get(`${intlPrefix}.line.parentLineNum`).d('母件订单行号'),
      type: FieldType.string,
    },
    {
      name: 'remark',
      label: intl.get(`${intlPrefix}.line.remark`).d('行说明'),
      type: FieldType.string,
    },
    {
      name: 'freezeFlag',
      label: intl.get(`${intlPrefix}.line.freezeFlag`).d('信用冻结标识'),
      type: FieldType.string,
      lookupCode: 'MT.APS.YES_NO',
    },
    {
      name: 'freezeFlagMeaning',
      type: FieldType.string,
      ignore: FieldIgnore.always,
      defaultValue: '可用',
    },
    {
      name: 'locatorCode',
      label: intl.get(`${intlPrefix}.line.locatorCode`).d('发运库位编码'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.USER.SITE.LOCATOR',
      lovPara: {
        tenantId,
        userId,
      },
      cascadeMap: { siteId: 'shipFromSiteId' },
    },
    {
      name: 'shipLocatorId',
      label: intl.get(`${intlPrefix}.line.shipLocatorId`).d('发运库位id'),
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('locatorCode') && record.get('locatorCode').locatorId
            ? 'locatorCode.locatorId'
            : '';
        },
      },
    },
    {
      name: 'locatorName',
      label: intl.get(`${intlPrefix}.line.locatorName`).d('发运库位描述'),
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('locatorCode') && record.get('locatorCode').locatorName
            ? 'locatorCode.locatorName'
            : '';
        },
      },
    },
    {
      name: 'shipMethod',
      label: intl.get(`${intlPrefix}.line.shipMethod`).d('发运方式'),
      type: FieldType.string,
    },
    {
      name: 'packingInstructions',
      label: intl.get(`${intlPrefix}.line.packingInstructions`).d('包装需求'),
      type: FieldType.string,
    },
    {
      name: 'itemCategory',
      label: intl.get(`${intlPrefix}.line.itemCategory`).d('项目类别'),
      type: FieldType.string,
    },
    {
      name: 'reason',
      label: intl.get(`${intlPrefix}.line.reason`).d('原因'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-order-lines/list/for/detail/ui?soId=${
          data?.soId
        }`,
        data,
        method: 'POST',
        transformResponse: (response) => {
          try {
            const jsonData = JSON.parse(response);
            if (jsonData.success === false) {
              return {
                type: 'warn',
                failed: true,
                message: jsonData.message,
              };
            }
            return jsonData;

          } catch {
            /**/
          }
          return response;
        },
      };
    },
    update: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-order-lines/batch/ui?soId=${
          data[0].soId
        }`,
        data:data[0],
        method: 'POST',
        transformResponse: (response) => {
          try {
            const jsonData = JSON.parse(response);
            if (jsonData.success === false) {
              return {
                type: 'warn',
                failed: true,
                message: jsonData.message,
              };
            }
            return jsonData;

          } catch {
            /**/
          }
          return response;
        },
      };
    },
    create: ({ data }) => {
      return {
        data:data[0],
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-order-lines/batch/ui?soId=${
          data[0].soId
        }`,
        method: 'POST',
        transformResponse: (response) => {
          try {
            const jsonData = JSON.parse(response);
            if (jsonData.success === false) {
              return {
                type: 'warn',
                failed: true,
                message: jsonData.message,
              };
            }
            return jsonData;

          } catch {
            /**/
          }
          return response;
        },
      };
    },
  },
});

export default lineInfoDS;
