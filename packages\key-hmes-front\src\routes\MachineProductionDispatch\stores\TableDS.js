// import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
// import uuid from 'uuid/v4';
import moment from 'moment';

const Host = BASIC.HMES_BASIC;
// const Host = '/key-focus-mes-33275';

// const modelPrompt = 'tarzan.workshop.MachineProductionDispatch';
const tenantId = getCurrentOrganizationId();

// 待派工单
const tableDS = () => ({
  autoQuery: false,
  paging: false,
  selection: 'multiple',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-production-capacitys/wo/dispatch/query`,
        method: 'GET',
      };
    },
  },
  dataKey: 'content',
  // totalKey: 'totalElements',
  // primaryKey: 'kid',
  // autoLocateFirst: false,
  queryFields: [
    {
      name: 'materialObj',
      // textField: 'materialCode',
      type: 'object',
      ignore: 'always',
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      lovPara: {
        tenantId,
      },
      label: '物料',
    },
    {
      name: 'materialCode',
      bind: 'materialObj.materialCode',
    },
    {
      name: 'materialId',
      bind: 'materialObj.materialId',
    },
    {
      name: 'operationObj',
      // textField: 'workcellCode1',
      type: 'object',
      ignore: 'always',
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      label: '工艺',
    },
    {
      name: 'operationName',
      bind: 'operationObj.operationName',
    },
    {
      name: 'operationId',
      bind: 'operationObj.operationId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: '工单',
    },
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: '计划开始时间',
      max: 'planEndTime',
      defaultValue: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: '计划结束时间',
      min: 'planStartTime',
      defaultValue: moment()
        .add(7, 'days')
        .format('YYYY-MM-DD 23:59:59'),
      // defaultValue: moment(new Date(new Date() - 1000 * 60 * 60 * 24 * 7)).format(
      //   'YYYY-MM-DD 23:59:59',
      // ),
    },
    {
      name: 'stepName',
      type: FieldType.string,
      label: '识别码',
    },

    {
      name: 'statusList',
      type: FieldType.string,
      label: '状态',
      textField: 'meaning',
      valueField: 'value',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupCode: 'HME_DISPATCH_WO_STATUS',
      // lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     if (data instanceof Array) {
      //       return data;
      //     }
      //     const { rows } = JSON.parse(data);
      //     return rows;
      //   },
      // },
    },

    {
      name: 'customerObj',
      // textField: 'workcellCode2', customerName
      type: 'object',
      lovCode: 'MT.MODEL.CUSTOMER',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      label: '客户',
    },
    {
      name: 'customerId',
      bind: 'customerObj.customerId',
    },
    {
      name: 'customerCode',
      bind: 'customerObj.customerCode',
    },
    {
      name: 'statusException',
      type: FieldType.string,
      label: '准备状态',
      textField: 'meaning',
      valueField: 'value',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupCode: 'HME_DISPATCH_PREPARE_STATUS',
      // lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     if (data instanceof Array) {
      //       return data;
      //     }
      //     const { rows } = JSON.parse(data);
      //     return rows;
      //   },
      // },
    },
  ],
  fields: [
    {
      name: 'dto4',
      type: FieldType.string,
      label: '工单准备状态',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: '工单编码',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: '工艺编码',
    },
    {
      name: 'operationDescription',
      type: FieldType.string,
      label: '工艺描述',
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: '计划开始时间',
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: '计划结束时间',
    },
    {
      name: 'workOrderId',
      type: FieldType.string,
    },
    {
      name: 'stepName',
      type: FieldType.string,
      label: '识别码',
    },
    {
      name: 'statusMeaning',
      type: FieldType.string,
      label: '状态',
    },
    {
      name: 'dispatchQty',
      type: FieldType.string,
      label: '待派工数量',
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: '需求数量',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: '物料编码',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: '物料描述',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: '物料版本',
    },
    {
      name: 'model',
      type: FieldType.string,
      label: '型号',
    },
    {
      name: 'note',
      type: FieldType.string,
      label: '派工备注',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: '工单备注',
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: '客户',
    },
  ],
});

// 已派工单
const sendTableDS = () => ({
  autoQuery: false,
  paging: false,
  selection: 'multiple',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-production-capacitys/wo/dispatched/query`,
        method: 'GET',
      };
    },
  },
  dataKey: 'content',
  // totalKey: 'totalElements',
  // primaryKey: 'kid',
  // autoLocateFirst: false,
  queryFields: [
    {
      name: 'materialObj',
      type: 'object',
      ignore: 'always',
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      lovPara: {
        tenantId,
      },
      label: '物料',
    },
    {
      name: 'materialCode',
      bind: 'materialObj.materialCode',
    },
    {
      name: 'materialId',
      bind: 'materialObj.materialId',
    },
    {
      name: 'operationObj',
      type: 'object',
      ignore: 'always',
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      label: '工艺',
    },
    {
      name: 'operationName',
      bind: 'operationObj.operationName',
    },
    {
      name: 'operationId',
      bind: 'operationObj.operationId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: '工单',
    },
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: '计划开始时间',
      max: 'planEndTime',
      defaultValue: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: '计划结束时间',
      min: 'planStartTime',
      defaultValue: moment()
        .add(7, 'days')
        .format('YYYY-MM-DD 23:59:59'),
      // defaultValue: moment(new Date(new Date() - 1000 * 60 * 60 * 24 * 7)).format(
      //   'YYYY-MM-DD 23:59:59',
      // ),
    },
    {
      name: 'stepName',
      type: FieldType.string,
      label: '识别码',
    },

    {
      name: 'statusList',
      type: FieldType.string,
      label: '状态',
      textField: 'meaning',
      valueField: 'value',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupCode: 'HME_DISPATCH_WO_STATUS',
      // lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     if (data instanceof Array) {
      //       return data;
      //     }
      //     const { rows } = JSON.parse(data);
      //     return rows;
      //   },
      // },
    },

    {
      name: 'customerObj',
      // textField: 'workcellCode2', customerName
      type: 'object',
      lovCode: 'MT.MODEL.CUSTOMER',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      label: '客户',
    },
    {
      name: 'customerId',
      bind: 'customerObj.customerId',
    },
    {
      name: 'customerCode',
      bind: 'customerObj.customerCode',
    },
    {
      name: 'workOrderType1',
      type: FieldType.string,
      label: '准备状态',
      textField: 'meaning',
      valueField: 'value',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupCode: 'HME_DISPATCH_PREPARE_STATUS',
      // lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     if (data instanceof Array) {
      //       return data;
      //     }
      //     const { rows } = JSON.parse(data);
      //     return rows;
      //   },
      // },
    },
  ],
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: '工单编码',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: '工艺编码',
    },
    {
      name: 'operationDescription',
      type: FieldType.string,
      label: '工艺描述',
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: '计划开始时间',
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: '计划结束时间',
    },
    {
      name: 'stepName',
      type: FieldType.string,
      label: '识别码',
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: '需求数量',
    },
    {
      name: 'dispatchQty',
      type: FieldType.string,
      label: '待派工数量',
    },
    {
      name: 'dispatchedQty',
      type: FieldType.string,
      label: '已派工数量',
    },
    {
      name: 'processDispatchedQty',
      type: FieldType.string,
      label: '待发布数量',
    },
    {
      name: 'actionDispatchedQty',
      type: FieldType.string,
      label: '已发布数量',
    },
    {
      name: 'completedQtySumRate',
      type: FieldType.string,
      label: '任务生产进度',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: '物料编码',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: '物料描述',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: '物料版本',
    },

    {
      name: 'model',
      type: FieldType.string,
      label: '型号',
    },
    {
      name: 'note',
      type: FieldType.string,
      label: '派工备注',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: '工单备注',
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: '计划开始时间',
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: '计划结束时间',
    },
    {
      name: 'completedQty',
      type: FieldType.string,
      label: '完成数量',
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: '报废数量',
    },
    {
      name: 'actualStartDate',
      type: FieldType.string,
      label: '实绩开始时间',
    },
    {
      name: 'actualEndDate',
      type: FieldType.string,
      label: '实绩结束时间',
    },
  ],
});


// 工单详情
const formDS = () => ({
  autoQuery: false,
  autoCreate: false,
  // autoQueryAfterSubmit: false,
  // dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-production-capacitys/wo/info/query`,
        method: 'GET',
        // transformResponse: val => {
        //   const datas = JSON.parse(val);
        //   if (datas && datas.rows) {
        //     if (datas.rows.bomId === 0) {
        //       datas.rows.bomId = null;
        //     }
        //     if (datas.rows.routerId === 0) {
        //       datas.rows.routerId = null;
        //     }
        //   }
        //   return {
        //     ...datas,
        //   };
        // },
      };
    },
  },
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: '生产指令编码',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: '站点编码',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: '站点描述',
    },
    {
      name: 'workOrderType',
      type: FieldType.string,
      label: '生产指令类型',
    },
    {
      name: 'statusMeaning',
      type: FieldType.string,
      label: '状态',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: '备注',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: '物料编码',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: '物料描述',
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: '生产指令数量',
    },
    {
      name: 'customerCode',
      type: FieldType.string,
      label: '客户编码',
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: '计划开始时间',
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: '计划结束时间',
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: '生产线编码',
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: '生产线描述',
    },
    {
      name: 'bomName',
      type: FieldType.string,
      label: '装配清单/版本',
    },
    {
      name: 'routerName',
      type: FieldType.string,
      label: '工艺路线/版本',
    },
  ],
});

// 派工信息
const dispatchListDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-production-capacitys/wo/dispatch/info/query`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'operationName',
      type: FieldType.string,
      label: '工艺',
    },
    {
      name: 'stepName',
      type: FieldType.string,
      label: '识别码',
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: '需求数量',
    },
    {
      name: 'dispatchedQty',
      type: FieldType.string,
      label: '已派工数量',
    },
    {
      name: 'dispatchQty',
      type: FieldType.string,
      label: '可派工数量',
    },
    // {
    //   name: 'workcellName',
    //   type: FieldType.string,
    //   label: '派工工位',
    // },
    // {
    //   name: 'realName',
    //   type: FieldType.string,
    //   label: '派工人员',
    // },
  ],
});

// 投料进度
const bomListDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-production-capacitys/complete-set/info/ui`,
        method: 'GET',
      };
    },
  },
  autoLocateFirst: false,
  fields: [
    {
      name: 'bomComponentName',
      type: FieldType.string,
      label: '物料',
    },
    {
      name: 'inputQty',
      type: FieldType.string,
      label: '已投料数量',
    },
    {
      name: 'canInputQty',
      type: FieldType.string,
      label: '未投料套数/数量',
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: '报废套数/数量',
    },
    {
      name: 'inventoryQty',
      type: FieldType.string,
      label: '剩余库存信息',
    },
  ],
});

// 拆分调度待派工工单
const splitOrderDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'dto4',
      type: FieldType.string,
      label: '工单准备状态',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: '工单编码',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: '工艺编码',
    },
    {
      name: 'operationDescription',
      type: FieldType.string,
      label: '工艺描述',
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: '计划开始时间',
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: '计划结束时间',
    },
    {
      name: 'workOrderId',
      type: FieldType.string,
    },
    {
      name: 'stepName',
      type: FieldType.string,
      label: '识别码',
    },
    {
      name: 'statusMeaning',
      type: FieldType.string,
      label: '状态',
    },
    {
      name: 'dispatchQty',
      type: FieldType.string,
      label: '待派工数量',
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: '需求数量',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: '物料编码',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: '物料版本',
    },
    {
      name: 'model',
      type: FieldType.string,
      label: '型号',
    },
    {
      name: 'note',
      type: FieldType.string,
      label: '派工备注',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: '工单备注',
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: '客户',
    },
    {
      name: 'splitDispatchQty',
      type: FieldType.number,
      label: '分配数量',
      max: 'dispatchQty',
      min: 0,
    },
  ],
});

// 拆分调度工位
const splitWorkCellDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'stationName',
      type: FieldType.string,
      label: '工位编码',
    },
    {
      name: 'stationId',
      type: FieldType.string,
      label: '工位id',
    },
    {
      name: 'operationId',
      type: FieldType.string,
      label: '工艺id',
    },
    {
      name: 'sumDispatchQty',
      type: FieldType.number,
      label: '已派工',
    },
    {
      name: 'splitDispatchQty',
      type: FieldType.number,
      label: '分配数量',
      // max: 'dispatchQty',
      min: 0,
    },
  ],
});

// 机台负荷的工艺lov
const machinePlatformDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'operationObj',
      type: 'object',
      ignore: 'always',
      lovCode: 'HME.DISPATCH_OPERATION',
      label: '工艺',
      dynamicProps: {
        lovPara: ({ _, dataSet }) => {
          return {
            tenantId,
            prodLineId: dataSet.getState('dispatchProdLineId') || '',
            workcellId: dataSet.getState('dispatchWorkCellId') || '',
          };
        },
      },
    },
    {
      name: 'operationName',
      bind: 'operationObj.operationName',
    },
    {
      name: 'operationId',
      bind: 'operationObj.operationId',
    },
  ],
})

export {
  tableDS,
  formDS,
  bomListDS,
  dispatchListDS,
  sendTableDS,
  splitOrderDS,
  splitWorkCellDS,
  machinePlatformDS,
};
