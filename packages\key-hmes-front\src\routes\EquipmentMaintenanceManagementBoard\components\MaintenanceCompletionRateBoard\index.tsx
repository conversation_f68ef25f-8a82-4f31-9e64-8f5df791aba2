import React, { useRef, useCallback, useState, useMemo, useEffect } from 'react';
import DashboardCard from '../DashboardCard.jsx';
import request from 'utils/request';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { debounce } from 'lodash';
import * as echarts from 'echarts';

const tenantId = getCurrentOrganizationId();
const url = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-repair/dayFinish`;

const MaintenanceCompletionRateBoard = ({ isFullScreen, timers, assetLocationId }) => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>(null);

  const fetchData = useCallback(async () => {

    const query = assetLocationId ? { assetLocationId } : {};

    const res = await request(url, {
      method: 'GET',
      query,
    });

    let maintainUnfinished = 0;
    let maintainCompleted = 0;
    let maintainProcessing = 0;

    res.forEach(item => {
      if (item?.status === '待处理') {
        maintainUnfinished = item?.total || 0;
      }
      if (item?.status === '已完成') {
        maintainCompleted = item?.total || 0;
      }
      if (item?.status === '执行中') {
        maintainProcessing = item?.total || 0;
      }
    });


    // 更新数据状态
    setData({
      maintainUnfinished,
      maintainCompleted,
      maintainProcessing,
    });

  }, [assetLocationId]);


  const option: any = useMemo(() => {
    // 过滤掉没有数据（value为0）的项
    const filteredData = [
      { value: data?.maintainUnfinished || 0, name: '待处理' },
      { value: data?.maintainCompleted || 0, name: '已完成' },
      { value: data?.maintainProcessing || 0, name: '执行中' },
    ].filter(item => item.value > 0); // 只保留 value 大于 0 的项

    return {
      title: {
        top: '3%',
        text: '保养单日完成率',
        left: 'center',
        textStyle: {
          fontWeight: 'bold',
          color: '#00fff4',
        },
      },
      color: ['#5470c6', '#91cc75', '#fac858'],
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          return `${params.name}: ${params.value} (${params.percent}%)`;
        },
      },
      legend: {
        orient: 'vertical',
        bottom: 'bottom',
        textStyle: {
          color: '#fff',
        },
      },
      series: [
        {
          name: '保养单日完成率',
          type: 'pie',
          radius: ['40%', '70%'], // 设置环形图的内外半径
          data: filteredData, // 使用过滤后的数据
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          label: {
            show: true,
            position: 'inside', // 将标签放置在环形区域内
            color: '#FFF',
            formatter: (params) => `${params.percent}%`, // 只显示数据值
          },
          labelLine: {
            show: false, // 隐藏指向的引线
          },
        },
      ],
    };
  }, [data]);


  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  useEffect(() => {
    let time;
    if (timers) {
      time = setInterval(() => {
        fetchData();
      }, (timers) * 60000)
    } else if (assetLocationId) {
      fetchData();
    }
    return () => {
      clearInterval(time)
    }
  }, [timers, assetLocationId])

  useEffect(() => {
    fetchData();
  }, []);


  return (
    <DashboardCard style={{ height: '100%' }}>
      {isFullScreen ?
        (<div style={{ width: '100%', height: '100%' }} >
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>) :
        <div style={{ width: '100%', height: '100%' }} >
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>
      }
    </DashboardCard>
  );
};

export default MaintenanceCompletionRateBoard;