/**
 * @Description: 分层审核方案-主界面DS
 * @Author: <EMAIL>
 * @Date: 2023/8/17 10:28
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.layerReview.layerReviewScheme';
const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'layerReviewSchemeId',
  queryFields: [
    {
      name: 'layerReviewSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewSchemeCode`).d('分层审核方案编码'),
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      lovCode: 'YP_QIS.OPERATION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'layerReviewSchemeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewSchemeStatus`).d('状态'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_SCHEME_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'layerReviewSchemeVersion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewSchemeVersion`).d('版本'),
    },
    {
      name: 'currentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
  ],
  fields: [
    {
      name: 'layerReviewSchemeId',
      type: FieldType.number,
    },
    {
      name: 'layerReviewSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewSchemeCode`).d('分层审核方案编码'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'layerReviewSchemeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewSchemeStatus`).d('状态'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_SCHEME_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'layerReviewSchemeVersion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewSchemeVersion`).d('版本'),
    },
    {
      name: 'currentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-layer-review-scheme/header/ui`,
        method: 'GET',
      };
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'layerReviewItemId',
  fields: [
    {
      name: 'layerReviewItemId',
      type: FieldType.number,
    },
    {
      name: 'layerReviewItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewItemCode`).d('项目编码'),
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目内容'),
    },
    {
      name: 'projectClassify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectClassify`).d('项目分类'),
      lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM.PROJECT_FROM',
      lovPara: { tenantId },
    },
    {
      name: 'projectFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectFrom`).d('标准来源'),
    },
    {
      name: 'level',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.level`).d('适用层级'),
    },
    {
      name: 'levelL1Flay',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.L1`).d('L1'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      disabled: true,
    },
    {
      name: 'levelL2Flay',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.L2`).d('L2'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      disabled: true,
    },
    {
      name: 'levelL3Flay',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.L3`).d('L3'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      disabled: true,
    },
    {
      name: 'levelL4Flay',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.L4`).d('L4'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      disabled: true,
    },
    {
      label: intl.get(`${modelPrompt}.table.layerReviewItemFrequency`).d('频率'),
      name: 'layerReviewItemFrequency',
      type: FieldType.string,
      lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM_FREQUENCY',
      lovPara: { tenantId },
    },
    {
      label: intl.get(`${modelPrompt}.table.emergencyFlag`).d('紧急发布'),
      name: 'emergencyFlag',
      type: FieldType.string,
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-layer-review-scheme/line/ui`,
        method: 'GET',
      };
    },
  },
});

export { headDS, lineDS };
