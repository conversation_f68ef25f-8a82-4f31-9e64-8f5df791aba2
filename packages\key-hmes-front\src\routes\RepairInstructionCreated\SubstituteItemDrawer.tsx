/**
 * @Description: 装配清单 - 替代物料抽屉（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/7/14 17:53
 * @LastEditTime: 2022/7/14 17:53
 * @LastEditors: <EMAIL>
 */
import React, { FC, useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  Modal,
  NumberField,
  Select,
  Table,
  TextField,
} from 'choerodon-ui/pro';
import { Icon, Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import { runInAction } from 'mobx';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { C7nFormItemSort } from '@components/tarzan-ui';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
// @ts-ignore
import styles from './index.module.less';

interface SubstituteItemDrawerProps {
  modelPrompt: string;
  substituteDrawerDs: DataSet;
  canEdit: boolean;
  path: string;
}

export const SubstituteItemDrawer: FC<SubstituteItemDrawerProps> = props => {
  const {
    modelPrompt,
    substituteDrawerDs,
    canEdit,
    path,
  } = props;
  enum SortType {
    MixedDate = 'MixedDate',
    MixedString = 'MixedString',
    MixedNumber = 'MixedNumber',
  }
  const [sortRecord, setSortRecord] = useState({
    sortColumn: '',
    sortOrder: '',
    sortType: SortType.MixedNumber,
  });
  let _substituteItemDrawer; // 替代物料明细抽屉

  // 替代项抽屉-确认按钮回调
  const handleSaveSubstituteItem = async record => {
    const result = await record?.validate();
    if (result) {
      record.setState('isSubmit', true);
      record.setState('isCancel', false);
      record.init({ ...record.toData() });
      _substituteItemDrawer.close();
    }
  };

  // 替代项抽屉-取消按钮回调
  const handleCancelSubstituteItem = record => {
    record.setState('isSubmit', false);
    record.setState('isCancel', true);
    _substituteItemDrawer.close();
  };

  // 打开替代项抽屉
  const openSubstituteItemDrawer = (record, isNew) => {
    // 处理revisionFlag
    if (!record.get('revisionFlag') && record.get('revisionCode')) {
      record.set('revisionFlag', 'Y');
    }
    record.setState('isCancel', false);
    record.setState('isSubmit', false);
    _substituteItemDrawer = Modal.open({
      key: Modal.key(),
      title: !isNew
        ? intl.get(`${modelPrompt}.editSubituteItem`).d('编辑')
        : intl.get(`${modelPrompt}.createSubituteItem`).d('新建'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal',
      onClose: () =>
        record.getState('isSubmit')
          ? record.setState('isCancel', false)
          : record.setState('isCancel', true),
      afterClose: () => {
        if (record.getState('isCancel') && isNew) {
          substituteDrawerDs.remove(record);
        } else if (record.getState('isCancel') && !isNew) {
          record.reset();
        }
      },
      children: (
        <Form labelWidth={112} record={record} columns={1} disabled={!canEdit}>
          <C7nFormItemSort name="materialLov" itemWidth={['70%', '30%']}>
            <Lov name="materialLov" />
            <Select name="revisionCode" noCache />
          </C7nFormItemSort>
          <TextField name="materialName" />
          <NumberField name="substituteValue" />
          <NumberField name="substituteUsage" />
          <DateTimePicker name="dateFrom" />
          <DateTimePicker name="dateTo" />
        </Form>
      ),
      footer: !canEdit ? (
        <div style={{ float: 'right' }}>
          <Button onClick={() => handleCancelSubstituteItem(record)}>
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        </div>
      ) : (
        <div style={{ float: 'right' }}>
          <Button onClick={() => handleCancelSubstituteItem(record)}>
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            type={ButtonType.submit}
            color={ButtonColor.primary}
            onClick={() => handleSaveSubstituteItem(record)}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  const customSort = (a, b, sortType: SortType) => {
    switch (sortType) {
      case 'MixedDate':
        if (!a && !b) {
          return 0;
        }
        if (Date.parse(a) === Date.parse(b)) {
          return 0;
        }
        return new Date(a) > new Date(b) ? 1 : -1;
      case 'MixedNumber':
        return a - b;
      case 'MixedString':
        if (a === null) a = '';
        if (b === null) b = '';
        return a.localeCompare(b);
      default:
        return 0;
    }
  };

  useEffect(() => {
    // dataSet的records并不是原生js的数组，而是MobX的observable数组，参考mobx文档，有sort方法，但不会改变数组本身，而只是返回一个排序的拷贝
    const newRecords =
      !!sortRecord.sortOrder && sortRecord.sortOrder === 'asc'
        ? substituteDrawerDs.records.sort((a, b) =>
          customSort(
            a.get(sortRecord.sortColumn),
            b.get(sortRecord.sortColumn),
            sortRecord.sortType,
          ),
        )
        : substituteDrawerDs.records.sort((a, b) =>
          customSort(
            b.get(sortRecord.sortColumn),
            a.get(sortRecord.sortColumn),
            sortRecord.sortType,
          ),
        );

    runInAction(() => {
      substituteDrawerDs.records = newRecords;
    });
  }, [sortRecord]);

  const handleColumnSort = (name, sortType) => {
    setSortRecord({
      sortColumn: name,
      sortOrder:
        !sortRecord.sortOrder || name !== sortRecord.sortColumn
          ? 'asc'
          : sortRecord.sortOrder === 'asc'
            ? 'desc'
            : 'asc',
      sortType,
    });
  };

  const sortHeader = ({ name, title, sortType }) => {
    return (
      <div
        // className={`header-sort ${name === this.state.sort.sortColumn ? 'focus' : ''} `}
        onClick={() => handleColumnSort(name, sortType)}
      >
        <span>{title}</span>
        <Icon
          type={`${
            sortRecord.sortOrder === 'desc' && name === sortRecord.sortColumn
              ? 'arrow_downward'
              : 'arrow_upward'
          } `}
          className="sort-icon"
        />
      </div>
    );
  };

  // 替代物料表格列
  const substituteDrawerColumns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <PermissionButton
            type="c7n-pro"
            icon="add"
            disabled={!canEdit}
            onClick={() => openSubstituteItemDrawer(substituteDrawerDs.create({}, 0), true)}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        ),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            // @ts-ignore
            onConfirm={() => substituteDrawerDs.remove(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              disabled={!canEdit || record?.get('bomSubstituteId')}
              funcType="flat"
              shape="circle"
              size="small"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'materialCode',
        width: 200,
        renderer: ({ value, record }) => (
          <a onClick={() => openSubstituteItemDrawer(record, false)}>{value}</a>
        ),
      },
      {
        name: 'revisionCode',
        width: 150,
      },
      {
        name: 'materialName',
        width: 180,
      },
      {
        name: 'substituteValue',
        width: 130,
        header: sortHeader({
          name: 'substituteValue',
          title: intl.get(`${modelPrompt}.substituteValue`).d('替代值'),
          sortType: SortType.MixedNumber,
        }),
      },
      {
        name: 'substituteUsage',
        width: 130,
        header: sortHeader({
          name: 'substituteUsage',
          title: intl.get(`${modelPrompt}.substituteUsage`).d('替代用量'),
          sortType: SortType.MixedNumber,
        }),
      },
      {
        name: 'dateFrom',
        width: 180,
        align: ColumnAlign.center,
        header: sortHeader({
          name: 'dateFrom',
          title: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
          sortType: SortType.MixedDate,
        }),
      },
      {
        name: 'dateTo',
        width: 180,
        align: ColumnAlign.center,
        header: sortHeader({
          name: 'dateTo',
          title: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
          sortType: SortType.MixedDate,
        }),
      },
    ],
    [sortRecord],
  );

  // 替代物料表格查询条的回调
  const handleTableQuery = () => {
    const queryMaterialCode = substituteDrawerDs.queryDataSet?.current?.get('materialCode') || '';
    const queryMaterialName = substituteDrawerDs.queryDataSet?.current?.get('materialName') || '';
    substituteDrawerDs.forEach(record => {
      const currentMaterialCode = record.get('materialCode');
      const currentMaterialName = record.get('materialName');
      if (
        currentMaterialCode.includes(queryMaterialCode) &&
        currentMaterialName.includes(queryMaterialName)
      ) {
        record.setState('displayFlag', 'Y');
      } else {
        record.setState('displayFlag', 'N');
      }
    });
  };

  return (
    <Table
      key="uuid"
      className={styles['expand-table']}
      dataSet={substituteDrawerDs}
      columns={substituteDrawerColumns}
      queryBar={TableQueryBarType.bar}
      queryBarProps={{
        onQuery: handleTableQuery,
      }}
      filter={record => {
        return (
          record.status !== 'delete' &&
          (!record.getState('displayFlag') || record.getState('displayFlag') === 'Y')
        );
      }}
    />
  );
};
