/**
 * @Description: 分层审核方案-详情界面
 * @Author: <EMAIL>
 * @Date: 2023/8/17 15:21
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Button,
  CheckBox,
  DataSet,
  Form,
  Lov,
  Modal,
  Select,
  Switch,
  Table,
  TextField,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm, Icon, Badge } from 'choerodon-ui';
import notification from 'utils/notification';
import { Content, Header } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { drawerPropsC7n, TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import moment from 'moment';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';

import { detailDS, itemDS, reviewItemDS, tableDS } from '../stores/DetailDS';
import { SaveScheme, SaveSchemeItem } from '../services';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.layerReview.layerReviewScheme';

const LayerReviewSchemeDetail = props => {
  const {
    history,
    match: { path, params },
  } = props;
  const layerReviewSchemeId = params.layerReviewSchemeId;

  // pub路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  const [canEdit, setCanEdit] = useState(false);
  const [disabledFlag, setDisabledFlag] = useState(false);
  const [submitDisabledFlag, setSubmitDisabledFlag] = useState(true);
  const itemDs = useMemo(() => new DataSet(itemDS()), []);
  const reviewItemDs = useMemo(() => new DataSet(reviewItemDS()), []);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          itemInfo: tableDs,
          itemDs,
        },
      }),
    [],
  );
  // 保存审核方案
  const { run: saveScheme, loading: saveLoading } = useRequest(SaveScheme(), {
    manual: true,
  });
  // 保存审核项目
  const { run: saveSchemeItem, loading: saveItemLoading } = useRequest(SaveSchemeItem(), {
    manual: true,
  });

  useEffect(() => {
    if (layerReviewSchemeId === 'create') {
      // 新建时
      setCanEdit(true);
      return;
    }
    // 编辑时
    handleQueryDetail(layerReviewSchemeId);
  }, [layerReviewSchemeId]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('layerReviewSchemeId', id);
    detailDs.query().then(res => {
      const {
        rows: { layerReviewSchemeStatus, currentFlag },
      } = res;
      const _editFlag =
        ['NEW', 'REJECT', 'AMENDING'].includes(layerReviewSchemeStatus) ||
        (layerReviewSchemeStatus === 'PUBLISHED' && currentFlag === 'Y');
      setDisabledFlag(!_editFlag);
      setSubmitDisabledFlag(!['NEW', 'REJECT', 'AMENDING'].includes(layerReviewSchemeStatus));
    });
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = () => {
    if (layerReviewSchemeId === 'create') {
      history.push('/hwms/layer-review/layer-review-scheme/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(layerReviewSchemeId);
    }
  };

  const handleSave = async ({ submitFlag }) => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const { taskInfo, layerReviewSchemeVersion, ...others } = detailDs!.current!.toData();
    (taskInfo || []).forEach(item => {
      item.planEndTime = moment(item.planEndTime).format('YYYY-MM-DD');
    });
    // 自动升版逻辑
    let _verson = layerReviewSchemeVersion;
    if (detailDs.current?.get('layerReviewSchemeStatus') === 'PUBLISHED') {
      const regex = new RegExp(/^([A-Z])(\d+)$/);
      const match = _verson.match(regex);
      if (!match) {
        throw new Error("Invalid interval format");
      }
      _verson = `${match[1]}${parseInt(match[2], 10) + 1}`;
    }
    saveScheme({
      params: {
        ...others,
        layerReviewSchemeVersion: _verson,
        taskInfo,
      },
      queryParams: { submitFlag },
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        if (layerReviewSchemeId === 'create') {
          history.push(`/hwms/layer-review/layer-review-scheme/dist/${res}`);
        } else {
          handleQueryDetail(res);
        }
      },
    });
  };

  const handleSaveReviewItem = (dataSet, nextFlag = false) => {
    return new Promise(async resolve => {
      const valRes = await dataSet.validate();
      if (!valRes) {
        return resolve(false);
      }
      saveSchemeItem({
        params: {
          ...dataSet.current?.toData(),
          layerReviewItemStatus: 'Y',
        },
        onSuccess: res => {
          notification.success({});
          tableDs.create({ ...res }, 0);
          if (nextFlag) {
            dataSet.reset();
            reviewItemDs?.current?.set('operationIdList', detailDs.current?.get('operationId'));
            return resolve(false);
          }
          return resolve(true);
        },
        onFailed: () => {
          return resolve(false);
        },
      });
    });
  };

  const handleCreateReviewItem = modal => {
    modal.close();
    reviewItemDs?.current?.set('operationIdList', detailDs.current?.get('operationId'));
    Modal.open({
      ...drawerPropsC7n({
        ds: reviewItemDs,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.createReviewItem`).d('新建分层审核项目'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: () => handleSaveReviewItem(reviewItemDs),
      children: (
        <Form dataSet={reviewItemDs} columns={1}>
          <TextField name="layerReviewItemCode" />
          <TextField name="projectName" />
          <Select name="projectClassify" />
          <TextField name="projectFrom" />
          <Select name="levelFlays" />
          <Select name="layerReviewItemFrequency" />
        </Form>
      ),
      footer: (okBtn, cancelBtn) => {
        return [
          cancelBtn,
          <Button onClick={() => handleSaveReviewItem(reviewItemDs, true)}>
            {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并新建下一条')}
          </Button>,
          okBtn,
        ];
      },
    });
  };

  const handleAddLine = value => {
    if (!value?.length) {
      return;
    }
    const existItemCode = tableDs.map(record => record?.get('layerReviewItemCode'));
    value.forEach(item => {
      if (!existItemCode.includes(item.layerReviewItemCode)) {
        tableDs.create({ ...item }, 0);
      }
    });
    itemDs.reset();
  };

  const columns: any = useMemo(
    () => [
      {
        header: () => (
          <Lov
            dataSet={itemDs}
            name="layerReviewItemLov"
            noCache
            mode={ViewMode.button}
            clearButton={false}
            onChange={handleAddLine}
            size={Size.small}
            funcType={FuncType.flat}
            disabled={!canEdit || disabledFlag}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return [
                  <Button onClick={() => handleCreateReviewItem(modal)}>
                    {intl.get(`${modelPrompt}.button.createReviewItem`).d('新建分层审核项目')}
                  </Button>,
                  cancelBtn,
                  okBtn,
                ];
              },
            }}
          >
            <Icon type="add" />
          </Lov>
        ),
        name: 'add',
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => tableDs.remove(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <Button icon="remove" disabled={!canEdit || disabledFlag} funcType={FuncType.flat} />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      { name: 'layerReviewItemCode' },
      { name: 'projectName', editor: canEdit },
      {
        name: 'projectClassify',
        editor: canEdit,
      },
      {
        name: 'projectFrom',
        editor: canEdit,
      },
      {
        name: 'level',
        align: ColumnAlign.center,
        width: 400,
        renderer: ({ record }) => (
          <Form record={record} disabled useColon={false} labelWidth={40} columns={4}>
            <CheckBox name="levelL1Flay" />
            <CheckBox name="levelL2Flay" />
            <CheckBox name="levelL3Flay" />
            <CheckBox name="levelL4Flay" />
          </Form>
        ),
      },
      {
        name: 'layerReviewItemFrequency',
        editor: canEdit,
      },
      {
        name: 'emergencyFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={record!.getField('emergencyFlag')!.getText()}
            />
          );
        },
      },
    ],
    [canEdit],
  );

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || saveItemLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('分层审核方案')}
          backPath={pubFlag ? '' : '/hwms/layer-review/layer-review-scheme/list'}
        >
          {canEdit && !pubFlag && (
            <>
              <Button
                color={ButtonColor.primary}
                icon="save"
                onClick={() => handleSave({ submitFlag: 'N' })}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && !pubFlag && (
            <Button
              icon="edit-o"
              disabled={disabledFlag}
              color={ButtonColor.primary}
              onClick={handleEdit}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
          {!pubFlag && (
            <>
              <Button
                disabled={submitDisabledFlag}
                icon="send-o"
                loading={saveLoading}
                onClick={() => handleSave({ submitFlag: 'Y' })}
              >
                {intl.get(`${modelPrompt}.button.submit`).d('提交')}
              </Button>
              <Button
                disabled={submitDisabledFlag}
                icon="send-o"
                loading={saveLoading}
                onClick={() => handleSave({ submitFlag: 'F' })}
              >
                {intl.get(`${modelPrompt}.button.urgentPublish`).d('紧急发布')}
              </Button>
            </>
          )}
          <ApprovalInfoDrawer
            objectTypeList={['QIS_LAYER_REVIEW_SCHEME']}
            objectId={layerReviewSchemeId}
          />
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'problemDesc']}>
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.layerReviewSchemeInfo`).d('分层审核方案信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="layerReviewSchemeCode" />
                <Lov name="operationLov" />
                <Select name="layerReviewSchemeStatus" />
                <TextField name="layerReviewSchemeVersion" />
                <Switch name="currentFlag" />
              </Form>
            </Panel>
            <Panel
              key="problemDesc"
              header={intl.get(`${modelPrompt}.title.layerReviewItem`).d('分层审核项目')}
            >
              <Table dataSet={tableDs} columns={columns} />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(LayerReviewSchemeDetail);
