/*
 * @Description: 体系文件管理-高级检索
 * @Author: <<EMAIL>>
 * @Date: 2023-10-20 11:27:43
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-25 09:49:38
 */
import React, { useMemo, useState, useEffect } from 'react';
import { Content, Header } from 'components/Page';
import { Row, Col, DataSet, Form, TextField, Button, Table, SelectBox } from 'choerodon-ui/pro';
import { Tag, Menu } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { observer } from 'mobx-react';
import { openTab } from 'utils/menuTab';
import { HZERO_FILE } from 'utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import stickImg from '@/assets/icons/stick.svg';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import { searchFormDS, searchTableDS } from '../stores/FullTextSearchDS';
import {
  registerProblemDS,
  leadInfoDS,
  measureDS,
  changeObjectDS,
  reasonAnalysisDS,
  influenceRangeDS,
} from '../stores/ProblemDetailDS';
import { EsQuery } from '../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';
const tenantId = getCurrentOrganizationId();
const { Item } = Menu;

const SearchResultComponent = observer(({ dataSet }) => {
  const formField = [
    'problemCode',
    'unitName',
    'responsiblePersonName',
    'leadPersonRealName',
    'registerTime',
  ];
  // 问题类别数据源
  const problemCategoryDs = dataSet.getField('problemCategory')?.getOptions(dataSet.current) || [];

  const renderProblemStatusTag = record => {
    const value = record?.get('problemStatus');
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
      case 'RELEASED':
        className = 'green';
        break;
      case 'FOLLOWING':
        className = 'yellow';
        break;
      case 'PUBLISH':
        className = 'blue';
        break;
      case 'CLOSED':
      case 'FREEZE':
        className = 'gray';
        break;
      default:
        className = 'geekblue';
    }
    return (
      <Tag color={className} className={styles['problem-status']}>
        {record!.getField('problemStatus')!.getText()}
      </Tag>
    );
  };

  const handleJumpToDetail = id => {
    openTab({
      key: `/hwms/problem-management/problem-management-platform/dist/${id}`,
      title: intl.get(`${modelPrompt}.tabTitle.problemManagement`).d('问题管理平台'),
    });
  };

  const handlePreviewFile = fileItem => {
    window.open(
      ''
        .concat(HZERO_FILE, '/v1/')
        .concat(tenantId, '/file-preview/by-url?url=')
        .concat(encodeURIComponent(fileItem?.fileUrl), '&bucketName=qms&access_token=')
        .concat(getAccessToken()),
    );
  };

  return (
    <div className={styles['search-result']}>
      {dataSet.map(record => {
        const {
          qisEsProblem3s = [],
          qisEsProblem4s = [],
          qisEsProblem5s = [],
          qisEsProblem6s = [],
          qisEsProblem7s = [],
          qisEsProblem8s = [],
          qisEsProblem9s = [],
          ...others
        } = record?.toData();
        // 问题基本信息数据
        let problemBasicData;
        // 临时措施数据
        const tempMeasureList: any = [];
        // 长期措施数据
        const prepMeasureList: any = [];
        // 原因分析数据
        const reasonAnalyseList: any = [];
        // 影响范围数据
        const influenceRangeList: any = [];
        // 临时变更对象数据
        const tempChangeObjectList: any = [];
        // 长期变更对象数据
        const prepChangeObjectList: any = [];

        // 问题类别所属的Group
        const currentCategoryRecord = problemCategoryDs.find(
          _record => _record?.get('value') === record?.get('problemCategory'),
        );
        const currentGroupValue = currentCategoryRecord?.get('tag');
        const currentGroupMeaning = currentCategoryRecord?.get('description');

        if (currentGroupValue === 'QUALITY') {
          problemBasicData = {
            ...others,
            ...qisEsProblem4s[0],
            displayValue: `${others?.restHighInfo?.problemTitle || others.problemTitle}(${others
              ?.restHighInfo?.problemCode || others.problemCode})`,
            restHighInfo: {
              ...(others?.restHighInfo || {}),
              ...(qisEsProblem4s[0]?.restHighInfo || {}),
            },
          };
        } else if (currentGroupValue === 'MARKET') {
          problemBasicData = {
            ...others,
            ...qisEsProblem5s[0],
            displayValue: `${others?.restHighInfo?.problemTitle || others.problemTitle}(${others
              ?.restHighInfo?.problemCode || others.problemCode})`,
            restHighInfo: {
              ...(others?.restHighInfo || {}),
              ...(qisEsProblem5s[0]?.restHighInfo || {}),
            },
          };
        } else {
          problemBasicData = {
            ...others,
            ...qisEsProblem3s[0],
            displayValue: `${others?.restHighInfo?.problemTitle || others.problemTitle}(${others
              ?.restHighInfo?.problemCode || others.problemCode})`,
            restHighInfo: {
              ...(others?.restHighInfo || {}),
              ...(qisEsProblem3s[0]?.restHighInfo || {}),
            },
          };
        }

        (qisEsProblem6s || []).forEach(item => {
          if (item?.restHighInfo && Object.keys(item?.restHighInfo)?.length > 0) {
            reasonAnalyseList.push({ ...item });
          }
        });
        (qisEsProblem7s || []).forEach(item => {
          if (item?.restHighInfo && Object.keys(item?.restHighInfo)?.length > 0) {
            if (item.measureType === 'TEMP') {
              tempMeasureList.push({ ...item });
            } else {
              prepMeasureList.push({ ...item });
            }
          }
        });
        (qisEsProblem8s || []).forEach(item => {
          if (item?.restHighInfo && Object.keys(item?.restHighInfo)?.length > 0) {
            influenceRangeList.push({ ...item });
          }
        });
        (qisEsProblem9s || []).forEach(item => {
          if (item?.restHighInfo && Object.keys(item?.restHighInfo)?.length > 0) {
            if (item.measureType === 'TEMP') {
              tempChangeObjectList.push({ ...item });
            } else {
              prepChangeObjectList.push({ ...item });
            }
          }
        });

        const RenderComponent = ({ dataSource, title, itemTitle }) => {
          return (
            <div>
              {Boolean(dataSource?.length) && (
                <div className={styles['render-component']}>
                  <div className={styles['render-component-title']}>{title}</div>
                  {dataSource.map(dataItem => (
                    <div className={styles['render-component-item']}>
                      <div className={styles['render-component-item-title']}>
                        {dataItem[itemTitle] || ''}
                      </div>
                      {Object.keys(dataItem?.restHighInfo).map(restHighItem => {
                        if (restHighItem !== 'attachment') {
                          return (
                            <div className={styles['render-component-item-content']}>
                              <span className={styles['render-component-item-content-label']}>
                                {record?.getField(restHighItem).get('label')}:{' '}
                              </span>
                              <span
                                className={styles['render-component-item-content-right']}
                                dangerouslySetInnerHTML={{
                                  __html: dataItem?.restHighInfo[restHighItem],
                                }}
                              />
                            </div>
                          );
                        }
                        return (dataItem?.restHighInfo?.attachment || []).map(fileItem => (
                          <div className={styles['render-component-item-attachemnt']}>
                            <div className={styles['render-component-item-file-header']}>
                              <div
                                className={styles['render-component-item-file-header-name']}
                                dangerouslySetInnerHTML={{
                                  __html: fileItem?.fileName,
                                }}
                                onClick={() => handlePreviewFile(fileItem)}
                              />
                              <div
                                className={styles['render-component-item-read-source-doc']}
                                onClick={() => handlePreviewFile(fileItem)}
                              >
                                {intl.get(`${modelPrompt}.button.perviewSourceFile`).d('查看原文件')}
                              </div>
                            </div>
                            <div
                              className={styles['render-component-item-file-content']}
                              dangerouslySetInnerHTML={{
                                __html: fileItem?.content,
                              }}
                            />
                          </div>
                        ));
                      })}
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        };

        return (
          <div className={styles['search-result-item']} key={record?.get('problemId')}>
            <div className={styles['problem-header']}>
              <div
                className={styles['problem-title']}
                dangerouslySetInnerHTML={{
                  __html: problemBasicData.displayValue,
                }}
                onClick={() => handleJumpToDetail(record?.get('problemId'))}
              />
              <div className={styles['problem-header-tag-group']}>
                {renderProblemStatusTag(record)}
                <Tag color="blue" className={styles['problem-status']}>
                  {currentGroupMeaning || ''}
                </Tag>
              </div>
            </div>
            <RenderComponent
              dataSource={
                problemBasicData?.restHighInfo &&
                Object.keys(problemBasicData?.restHighInfo)?.length > 0
                  ? [problemBasicData]
                  : []
              }
              title={intl.get(`${modelPrompt}.area.problemBasicProps`).d('问题基础属性')}
              itemTitle=""
            />
            <RenderComponent
              dataSource={tempMeasureList}
              title={intl.get(`${modelPrompt}.area.tempMeasure`).d('临时措施')}
              itemTitle="measureDescription"
            />
            <RenderComponent
              dataSource={prepMeasureList}
              title={intl.get(`${modelPrompt}.area.perpMeasure`).d('长期措施')}
              itemTitle="measureDescription"
            />
            <RenderComponent dataSource={reasonAnalyseList} title={intl.get(`${modelPrompt}.area.reasonAnalysis`).d('原因分析')} itemTitle="reason" />
            <RenderComponent
              dataSource={reasonAnalyseList}
              title={intl.get(`${modelPrompt}.area.tempChangeObject`).d('临时变更对象')}
              itemTitle="changeObject"
            />
            <RenderComponent
              dataSource={reasonAnalyseList}
              title={intl.get(`${modelPrompt}.area.perpChangeObject`).d('长期变更对象')}
              itemTitle="changeObject"
            />
            <RenderComponent
              dataSource={influenceRangeList}
              title={intl.get(`${modelPrompt}.area.influenceRange`).d('影响范围')}
              itemTitle="influenceRange"
            />
            <div className={styles['problem-footer']}>
              {formField.map(name => {
                return (
                  <div className={styles['problem-footer-item']}>
                    <span className={styles['problem-footer-item-label']}>
                      {dataSet.getField(name)?.get('label')}:
                    </span>
                    <span className={styles['problem-footer-item-content']}>
                      {record?.get(name)}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
});

const FullTextSearchPage = () => {
  const searchFormDs = useMemo(() => new DataSet(searchFormDS()), []);
  const tableDs = useMemo(
    () =>
      new DataSet({
        ...searchTableDS(),
        fields: [
          ...(searchTableDS()?.fields || []),
          ...(registerProblemDS()?.fields || []),
          ...(leadInfoDS()?.fields || []),
          ...(measureDS()?.fields || []),
          ...(influenceRangeDS()?.fields || []),
          ...(reasonAnalysisDS()?.fields || []),
          ...(changeObjectDS()?.fields || []),
        ],
      }),
    [],
  );
  // 下一页主键
  // const [nextPageKey, setNextPageKey] = useState<number | null>(null);
  // 回到顶部按钮是否展示
  const [backTopDisplay, setBackDisplay] = useState<boolean>(false);

  const [selectedMenu, setSelectedMenu] = useState('ALL');

  const { run: esQuery, loading: esQueryLoading } = useRequest(EsQuery(), { manual: true });

  useEffect(() => {
    const _container = document.getElementsByClassName('page-content');
    if (!_container?.length) {
      return;
    }
    const scrollDom = _container[0];
    // 开启侦听器,监听页面滚动
    scrollDom.addEventListener('scroll', useFn);

    // 组件销毁时移除侦听器
    return () => {
      scrollDom.removeEventListener('scroll', useFn);
    };
  }, []);

  const useFn = () => {
    // 此处调用 加载更多函数
    isTouchBottom(handleLoadMore);
  };

  /**
   * 判断是否触底
   * 此函数进行判断是否触底
   * @param    handler  必填  判断后执行的回调函数
   * @returns  null
   */
  const isTouchBottom = handler => {
    const _container = document.getElementsByClassName('page-content');
    if (!_container?.length) {
      return;
    }
    const scrollDom = _container[0];

    if (scrollDom.scrollTop >= scrollDom.clientHeight) {
      setBackDisplay(true);
    }

    // (滚动可视区域高度 + 当前滚动位置 === 整个滚动高度) 时即为触底
    if (scrollDom.clientHeight + scrollDom.scrollTop >= scrollDom.scrollHeight - 20) {
      handler();
    }
  };

  /**
   * 加载更多
   * 此函数内进行接口请求等操作
   */
  const handleLoadMore = () => {
    // 因为nextPageKey如果存在state里，无法取到最新的值，暂时使用这种方法
    if (!tableDs?.getState('nextPageKey')) {
      return;
    }
    const loadingDiv: any = document.getElementById('loading-text');
    loadingDiv.style.display = 'block';
    handleQuery({ afterKey: tableDs?.getState('nextPageKey') });
  };

  const handleReset = () => {
    tableDs.setState('nextPageKey', undefined);
    searchFormDs.reset();
    handleQuery({});
  };

  const handleJumpToTop = () => {
    const _container = document.getElementsByClassName('page-content');
    if (_container?.length) {
      _container[0].scrollTo(_container[0].scrollTop, 0);
    }
  };

  const handleQuery: (afterKey?: any, period?: any) => void = ({
    afterKey = null,
    period = selectedMenu,
  }) => {
    // 获取“加载中”元素
    const loadingDiv: any = document.getElementById('loading-text');
    // 判断有无afterKey,如果有，需要缓存现有的DS
    const tableData = afterKey ? tableDs.toData() : [];
    // 展示“加载中”字样
    loadingDiv.style.display = 'block';
    // 查询参数组装
    const { queryContent, searchType, searchDomain } = searchFormDs.current?.toData();
    const queryParmas = tableDs.queryDataSet?.current?.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i]) || queryParmas[i]?.length === 0 || i === '__dirty') {
        delete queryParmas[i];
      }
    });
    esQuery({
      params: {
        queryContent,
        searchType,
        searchDomain: searchDomain === 'any' ? undefined : searchDomain,
        afterKey,
        registerTimeRange: period === 'ALL' ? undefined : period,
        exactlyQueryFlag: Object.keys(queryParmas)?.length || period !== 'ALL' ? 'Y' : 'N',
        ...queryParmas,
      },
      onSuccess: res => {
        const { content } = res || {};
        tableDs?.setState('nextPageKey', res?.afterKey);
        tableDs.loadData([...tableData, ...content]);
        // 隐藏“加载中”字样
        loadingDiv.style.display = 'none';
        // 清空afterKey的查询参数
        // tableDs.setQueryParameter('afterKey', undefined);
  
        if (!afterKey) {
          // 定位到顶部
          handleJumpToTop();
        }
      },
    })
  };

  const handleChangeTime = e => {
    const _value = e.key;
    setSelectedMenu(_value);
    handleQuery({ period: _value });
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={tableDs} spinning={esQueryLoading}>
        <Header title={intl.get(`${modelPrompt}.title.fullTextSearch`).d('全文检索')} />
        <Content className={styles['full-text-search-page']}>
          <Row className={styles['search-content']}>
            <Col span={20}>
              <Form dataSet={searchFormDs} columns={5}>
                <TextField name="queryContent" colSpan={5} onEnterDown={() => handleQuery({})} />
                <SelectBox name="searchDomain" colSpan={5} onChange={() => handleQuery({})} />
                <SelectBox name="searchType" colSpan={5} onChange={() => handleQuery({})} />
              </Form>
            </Col>
            <Col span={4} style={{ lineHeight: '36px', paddingLeft: '16px' }}>
              <Button onClick={() => handleReset()}>{intl.get(`${modelPrompt}.button.reset`).d('重置')}</Button>
              <Button color={ButtonColor.primary} onClick={() => handleQuery({})}>
                {intl.get(`${modelPrompt}.button.search`).d('检索')}
              </Button>
            </Col>
          </Row>
          <div className={styles['full-text-search-container']}>
            {/* <div className={styles['search-total-count']}>
            搜索到约
            <span className={styles['total-count-num']}>{paginationInfo.total}</span>
            项结果
          </div> */}
            <div className={styles['search-result-container']}>
              <div className={styles['full-text-search-container-left']}>
                <div className={styles['search-bar']}>
                  <Table
                    queryBar={TableQueryBarType.filterBar}
                    queryBarProps={{
                      fuzzyQuery: false,
                      onQuery: () => handleQuery({}),
                      onRefresh: () => handleQuery({}),
                      onReset: () => handleQuery({}),
                    }}
                    dataSet={tableDs}
                    columns={[]}
                    searchCode="systemDocumentManagement-searchCode"
                    className={styles['full-text-search-table']}
                    pagination={false}
                  />
                </div>
                <SearchResultComponent dataSet={tableDs} />
                <div className={styles['loading-text']} id="loading-text">
                  {intl.get(`${modelPrompt}.text.loading`).d('加载中...')}
                </div>
                {/* <div className={styles['search-pagination']}>
                <Pagination
                  showSizeChanger
                  showTotal
                  showPager
                  page={paginationInfo.currentPage}
                  pageSize={paginationInfo.pageSize}
                  total={paginationInfo.total}
                  onChange={handleChangePagination}
                />
              </div> */}
              </div>
              <div className={styles['full-text-search-container-right']}>
                <Menu
                  defaultSelectedKeys={['ALL']}
                  selectedKeys={[selectedMenu]}
                  mode="inline"
                  onClick={handleChangeTime}
                >
                  <Menu.Item disabled key="title">
                    <div className={styles['publish-time-title']}>{intl.get(`${modelPrompt}.searchMenu.byRegisterTime`).d('按登记时间')}</div>
                  </Menu.Item>
                  <Item key="DAYS">{intl.get(`${modelPrompt}.searchMenu.days`).d('一天内')}</Item>
                  <Item key="WEEKS">{intl.get(`${modelPrompt}.searchMenu.weeks`).d('一周内')}</Item>
                  <Item key="MONTHS">{intl.get(`${modelPrompt}.searchMenu.months`).d('一月内')}</Item>
                  <Item key="YEARS">{intl.get(`${modelPrompt}.searchMenu.years`).d('一年内')}</Item>
                  <Item key="ALL">{intl.get(`${modelPrompt}.searchMenu.all`).d('不限')}</Item>
                </Menu>
              </div>
            </div>
          </div>
          <div
            onClick={() => handleJumpToTop()}
            style={{ display: backTopDisplay ? '' : 'none' }}
            className={styles['back-top']}
          >
            <img src={stickImg} alt="" />
          </div>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(FullTextSearchPage);
