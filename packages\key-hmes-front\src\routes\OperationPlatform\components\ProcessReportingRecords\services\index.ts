/**
 * @Description: 工单报工卡片-services
 * @Author: <<EMAIL>>
 * @Date: 2023-07-27 19:20:19
 * @LastEditTime: 2023-08-02 18:59:40
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 获取工单想想信息
 * @function FetchWoDetail
 * @returns {object} fetch Promise
 */
export function FetchWoDetail(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/order/info/get`,
    method: 'POST',
  };
}

/**
 * 扫描物料批
 * @function ScanMaterialLot
 * @returns {object} fetch Promise
 */
export function ScanMaterialLot(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/scan/lot/verify `,
    method: 'POST',
  };
}

/**
 * 工单完工
 * @function CompleteWo
 * @returns {object} fetch Promise
 */
export function CompleteWo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/report/execute`,
    method: 'POST',
  };
}

/**
 * 报工退回
 * @function ReturnWo
 * @returns {object} fetch Promise
 */
export function ReturnWo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/report/execute/back`,
    method: 'POST',
  };
}


/**
 * 物料批查询
 * @function FetchMaterialLotQty
 * @returns {object} fetch Promise
 */
export function FetchMaterialLotQty(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/return/lot/verify`,
    method: 'POST',
  };
}
