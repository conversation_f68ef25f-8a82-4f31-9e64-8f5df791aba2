/**
 * @Description: 异常处理按钮弹窗
 * @author: ZCL<<EMAIL>>
 * @date: 2021-04-10 13:58:44
 * @version: 0.0.1
 * @copyright: Copyright (c) 2021, Hand
 */

import React, { Component } from 'react';
import { Modal, Row, Col, Form, Button, Input } from 'hzero-ui';
import { isFunction } from 'lodash';
import Lov from 'components/Lov';
import intl from 'utils/intl';
import { Bind } from 'lodash-decorators';
import styles from '../index.less';

// const { Option } = Select;

@Form.create({ fieldNameProp: null })
export default class ExceptionTipModal extends Component {
  constructor(props) {
    super(props);
    if (isFunction(props.threeRef)) {
      props.threeRef(this);
    }
  }

  componentDidMount() {}

  @Bind()
  hideTipModal() {
    this.props.hideTipModal();
  }

  @Bind()
  handleException() {
    const { type, handleTipException } = this.props;
    handleTipException(type);
  }

  @Bind()
  queryEmployee() {
    const { queryEmployee } = this.props;
    if (queryEmployee) {
      queryEmployee();
    }
  }

  render() {
    const {
      // abnormalTypeList,
      title,
      tipVisible,
      type,
      content,
      form,
      tenantId,
      closeExceptionLoading,
      responseExceptionLoading,
      modalType,
      exceptionRecord,
      enterSiteInfo = {},
      fetchVal,
    } = this.props;
    const formLayout = {
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
    };
    const { getFieldDecorator } = form;
    const DRAWER_FORM_ITEM_LAYOUT_MAX = {
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 15,
      },
    };
    return (
      <Modal
        title={title}
        visible={tipVisible}
        className={styles['exception-model']}
        style={{ paddingBottom: '0px' }}
        onCancel={() => this.hideTipModal()}
        footer={[
          <Button
            key="yes"
            style={{ marginLeft: '5px' }}
            type="primary"
            onClick={() => this.handleException()}
            loading={closeExceptionLoading || responseExceptionLoading}
          >
            {type !== 'response' ? '是' : '确认'}
          </Button>,
          <Button key="no" style={{ marginLeft: '5px' }} onClick={() => this.hideTipModal()}>
            {type !== 'response' ? '否' : '取消'}
          </Button>,
        ]}
      >
        <Form className={styles['exception-model-form']}>
          {type === 'response' && (
            <>
              <Row>
                <Col span={12}>
                  <Form.Item {...formLayout} label="异常项">
                    {exceptionRecord.exceptionName}
                  </Form.Item>
                </Col>
                {/* <Col span={12}>
                  <Form.Item {...formLayout} label="班次">
                    {exceptionRecord.shiftCode}
                  </Form.Item>
                </Col> */}
              </Row>
              <Row>
                <Col span={12}>
                  <Form.Item {...formLayout} label="工作单元">
                    {enterSiteInfo.workcellCode}
                  </Form.Item>
                </Col>
                {/* <Col span={12}>
                  <Form.Item {...formLayout} label="指令单">
                    {enterSiteInfo.taskOrderNum}
                  </Form.Item>
                </Col> */}
              </Row>
              <Row>
                <Col>
                  <Form.Item {...DRAWER_FORM_ITEM_LAYOUT_MAX} label="处理人">
                    {getFieldDecorator(
                      'exceptionUserId',
                      type === 'response'
                        ? {
                            rules: [
                              {
                                required: true,
                                message: intl.get('hzero.common.validation.notNull', {
                                  name: '处理人',
                                }),
                              },
                            ],
                            initialValue: fetchVal.userId ? fetchVal.userId : null,
                          }
                        : {
                            initialValue: fetchVal && fetchVal.realName ? fetchVal.realName : null,
                          },
                    )(
                      <Lov
                        code="HME.EXCEPTION_TYPE_PERSON"
                        queryParams={{ tenantId, exceptionType: modalType }}
                        textValue={fetchVal.realName}
                      />,
                    )}
                  </Form.Item>
                </Col>
                <Col>
                  <Form.Item>
                    <Button
                      style={{
                        display: 'none',
                      }}
                      htmlType="submit"
                      onClick={() => this.queryEmployee()}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}
          {type === 'cancel' && <p>是否确认取消【 {content} 】异常?</p>}
          {type === 'upgrade' && <p>是否将该异常升级至第【 {content} 】等级？</p>}
          {type === 'close' && (
            <>
              <p style={{ marginLeft: '15px', marginBottom: '10px' }}>
                是否确认关闭【 {content} 】异常？
              </p>
              <Row>
                <Col span={12}>
                  <Form.Item {...formLayout} label="异常项">
                    {exceptionRecord.exceptionName}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item {...formLayout} label="班次">
                    {exceptionRecord.shiftCode}
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={12}>
                  <Form.Item {...formLayout} label="工作单元">
                    {enterSiteInfo.workcellCode}
                  </Form.Item>
                </Col>
                {/* <Col span={12}>
                  <Form.Item {...formLayout} label="指令单">
                    {enterSiteInfo.taskOrderNum}
                  </Form.Item>
                </Col> */}
              </Row>
              {/* <Row>
                <Col>
                  <Form.Item {...DRAWER_FORM_ITEM_LAYOUT_MAX} label="异常类型">
                    {getFieldDecorator('actualExceptionType', {
                      initialValue: modalType,
                      rules: [
                        {
                          required: true,
                          message: intl.get('hzero.common.validation.notNull', {
                            name: '异常类型',
                          }),
                        },
                      ],
                    })(
                      <Select
                        allowClear
                        onChange={(value) => {
                          this.handleChange(value);
                        }}
                      >
                        {abnormalTypeList.map((e) => (
                          <Option key={e.typeCode} value={e.typeCode}>
                            {e.description}
                          </Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
              </Row> */}
              <Row>
                <Col>
                  <Form.Item {...DRAWER_FORM_ITEM_LAYOUT_MAX} label="异常原因">
                    {getFieldDecorator(
                      'reasionId',
                      // type === 'close'
                      //   ? {
                      //       rules: [
                      //         {
                      //           // required: true,
                      //           message: intl.get('hzero.common.validation.notNull', {
                      //             name: '异常原因',
                      //           }),
                      //         },
                      //       ],
                      //     }
                      //   : {}
                    )(
                      <Lov
                        code="HME.EXCEPTION_REASON"
                        queryParams={{ tenantId, exceptionType: modalType }}
                      />,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col>
                  <Form.Item {...DRAWER_FORM_ITEM_LAYOUT_MAX} label="异常描述">
                    {getFieldDecorator('attribute4', {})(<Input.TextArea />)}
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col>
                  <Form.Item {...DRAWER_FORM_ITEM_LAYOUT_MAX} label="责任部门">
                    {getFieldDecorator('attribute16', {
                      rules: [
                        {
                          required: true,
                          message: intl.get('hzero.common.validation.notNull', {
                            name: intl
                              .get(`hmes.operationPlatform.model.operationPlatform.attribute16`)
                              .d('责任部门'),
                          }),
                        },
                      ],
                    })(<Lov code="HME.RESPONSIBLE_DEPARTMENT" queryParams={{ tenantId }} />)}
                  </Form.Item>
                </Col>
              </Row>
              {/* {modalType === 'EQUIPMENT' && (
                <Row>
                  <Col>
                    <Form.Item {...DRAWER_FORM_ITEM_LAYOUT_MAX} label="设备编码">
                      {getFieldDecorator('equipmentId', {
                        initialValue: Number(exceptionRecord.equipmentId),
                        rules: [
                          {
                            required: true,
                            message: intl.get('hzero.common.validation.notNull', {
                              name: '设备编码',
                            }),
                          },
                        ],
                      })(
                        <Lov
                          code="HMES.ASSET"
                          queryParams={{ tenantId, workcellId: exceptionRecord.workcellId }}
                          textValue={exceptionRecord.equipmentCode}
                        />
                      )}
                    </Form.Item>
                  </Col>
                </Row>
              )}
              <Row>
                <Col>
                  <Form.Item {...DRAWER_FORM_ITEM_LAYOUT_MAX} label="关闭人">
                    {getFieldDecorator(
                      'closedBy',
                      {}
                    )(
                      <Lov
                        code="LOV_EMPLOYEE"
                        queryParams={{ tenantId }}
                        onChange={(value, records) => {
                          this.props.form.setFieldsValue({
                            closedBy: records.employeeId,
                          });
                        }}
                      />
                    )}
                  </Form.Item>
                </Col>
              </Row> */}
            </>
          )}
        </Form>
      </Modal>
    );
  }
}
