/* eslint-disable jsx-a11y/alt-text */
import React, { useState, useEffect, useMemo } from 'react';
import { DataSet, Table, Progress, Select } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import moment from 'moment';
import formatterCollections from 'utils/intl/formatterCollections';
import refresh from '@/assets/operationPlatformCard/refresh.svg';
import { tableDS, formDS } from './stores/SchedulingTasksDS';
import { useOperationPlatform } from '../../contextsStore';
import { CardLayout } from '../commonComponents';
import styles from './index.modules.less';

const tenantId = getCurrentOrganizationId();

const SchedulingTasks = props => {
  const tableDs = useMemo(
    () =>
      new DataSet({
        ...tableDS(),
      }),
    [],
  );
  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
      }),
    [],
  );
  const { enterInfo, workOrderData, dispatch } = useOperationPlatform();

  // const [cacheEoId, setCacheEoId] = useState(null); // 工单数据
  const [taskProgress, setTaskProgress] = useState(0); // 工单数据
  const [loading, setLoading] = useState(false);
  const [shiftList, setShiftList] = useState([]); // 班次列表


  useEffect(() => {
    // if (workOrderData?.eoId && cacheEoId !== workOrderData?.eoId) {
    // 成功登录工位后开始查询
    if (enterInfo?.workStationId) {
      // setCacheEoId(workOrderData?.eoId);
      handleQuery();
    }
    if(enterInfo?.calendarId){
      fetchShiftData();
    }
  }, [enterInfo]);

  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  // 查询树
  useEffect(() => {
    // if (workOrderData?.eoId && cacheEoId !== workOrderData?.eoId) {
    if (workOrderData?.eoId) {
      // setCacheEoId(workOrderData?.eoId);
      handleQuery();
    }
  }, [workOrderData]);

  // // 字体大小控制
  // useEffect(() => {
  //   document.getElementById('schedulingTasksTitle').style.fontSize = `${10 +
  //     props.newLayout?.filter(item => item.i === '7')[0]?.w}px`;
  // }, [props.newLayout]);

  const fetchShiftData = () => {
    request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/hme-calendar-shift/calendar-shift/query/for/ui`, {
      method: 'GET',
      query: {
        calendarId: enterInfo?.calendarId,
        shiftStartTime: moment(new Date()).startOf('month').format('YYYY-MM-DD 00:00:00'),
        shiftEndTime: moment(new Date()).endOf('month').format('YYYY-MM-DD 00:00:00'),
      },
    }).then(res => {
      if (res && !res.failed) {
        const newArr = res.map(item => {
          return {
            shiftDateCode: `${item.shiftDate}/${item.shiftCode}`,
            ...item,
          }
        });
        setShiftList(newArr)
      }
    });
  }
  /**
   * table查询
   */
  const handleQuery = (paramObj) => {
    const newObj = shiftList.find(item => item.shiftDateCode === paramObj);
    setLoading(true);
    const params = {
      workcellId: enterInfo?.workStationId,
      operationId: enterInfo?.selectOperation?.operationId,
      shiftCode: newObj?.shiftCode || enterInfo?.shiftCode,
      shiftDate: newObj?.shiftDate || enterInfo?.shiftDate,
      currentDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      shiftStartTime: newObj?.shiftStartTime || enterInfo?.shiftStartTime,
      shiftEndTime: newObj?.shiftEndTime || enterInfo?.shiftEndTime,
      // shiftTeamId
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-material-traces/scheduling-task/new/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        tableDs.loadData(res.schedulingInformationList || []);
        // let completedQtySum = 0;
        // let dispatchQtySum = 0;
        // res.forEach(item => {
        //   if (item.completedQty) {
        //     completedQtySum += item.completedQty;
        //   }
        //   if (item.sendWorkQty) {
        //     dispatchQtySum += item.sendWorkQty;
        //   }
        // });
        setLoading(false);
        // 总任务进度
        // setTaskProgress(completedQtySum / dispatchQtySum || 0);
        setTaskProgress((res.dutyLoad * 100) || 0);
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          message: `调度任务数据查询成功`,
          recordType: 'query',
        });
      } else {
        setLoading(false);
        notification.error({ message: res.message });
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'FAIL',
          message: `调度任务数据查询失败`,
          recordType: 'query',
        });
      }
    });
  };

  const handleReshOrder = (record) => {
    console.log(1111, record);
    dispatch({
      type: 'update',
      payload: {
        workOrderData: {
          ...record,
          workOrderDataType: 'SchedulingTasks',
        },
      },
    });
  }

  const columns = [
    // {
    //   renderer: ({ record }) => {
    //     if (record.data.status === 'COMPLETED') {
    //       return <Tag color="green-inverse">完成</Tag>;
    //     }
    //     return (
    //       <Tag color="#108ee9" onClick={() => props?.handleReshOrder(record.data)}>
    //         加工
    //       </Tag>
    //     );
    //   },
    //   width: 70,
    //   headerClassName: 'SchedulingTasks',
    // },
    {
      name: 'dispatchNumber',
      align: 'left',
      headerClassName: 'SchedulingTasks',
      renderer: ({ record }) => {
        if(Number(record.data.completedQty) < Number(record.data.sendWorkQty)){
          return <span style={{color: 'rgba(51, 241, 255, 1)', cursor: 'pointer'}} onClick={() => handleReshOrder(record.data)}>{record.data.dispatchNumber}</span>
        }
        return <span>{record.data.dispatchNumber}</span>
      },
    },
    {
      name: 'materialCode',
      width: 120,
      align: 'left',
      headerClassName: 'SchedulingTasks',
      renderer: ({ record }) => {
        return (
          <span>
            {record.data.materialCode}/{record.data.model}
          </span>
        );
      },
    },
    {
      name: 'materialName',
      width: 200,
      align: 'left',
      headerClassName: 'SchedulingTasks',
    },
    {
      name: 'workOrderNum',
      width: 140,
      align: 'left',
      headerClassName: 'SchedulingTasks',
    },
    {
      name: 'attributeDispatchQty',
      width: 220,
      align: 'left',
      headerClassName: 'SchedulingTasks',
      renderer: ({ record }) => {
        if(!record.data.completedQty || !record.data.sendWorkQty){
          return (
            <span>
              <span>{record.data.completedQty}</span>/{record.data.sendWorkQty}/{record.data.uomCode}
            </span>
          );
        }
        if(Number(record.data.completedQty) < Number(record.data.sendWorkQty)){
          return (
            <span>
              <span style={{color: 'rgba(51, 241, 255, 1)'}}>{record.data.completedQty}</span>/{record.data.sendWorkQty}/{record.data.uomCode}
            </span>
          );
        }
        return (
          <span>
            <span style={{color: 'rgba(100, 222, 163, 1)'}}>{record.data.completedQty}</span>/{record.data.sendWorkQty}/{record.data.uomCode}
          </span>
        );
      },
    },
    {
      name: 'priority',
      width: 100,
      align: 'right',
      headerClassName: 'SchedulingTasks',
    },
    {
      name: 'dispatchDate',
      headerClassName: 'SchedulingTasks',
      width: 150,
      align: 'center',
      renderer: ({ record }) => {
        return (
          <span>
            {moment(record.data.planStartTime).format('MM-DD HH:mm:ss')}~{moment(record.data.planEndTime).format('MM-DD HH:mm:ss')}
          </span>
        );
      },
    },
    // {
    //   name: 'dispatchStartDate',
    //   headerClassName: 'SchedulingTasks',
    //   width: 150,
    //   align: 'center',
    // },
    // {
    //   name: 'dispatchEndDate',
    //   headerClassName: 'SchedulingTasks',
    //   width: 150,
    //   align: 'center',
    // },
    // {
    //   name: 'dispatchQty',
    //   align: 'center',
    //   headerClassName: 'SchedulingTasks',
    //   renderer: ({ record }) => {
    //     return (
    //       <span>
    //         {record.data.dispatchQty}/{record.data.uomCode}
    //       </span>
    //     );
    //   },
    // },
  ];

  const searchMatcher = ({ record, text }) => {
    return record.get('value').indexOf(text) !== -1;
  }

  const handleChangeDom = (type) => {
    if(type === 'focus'){
      const popupContainer = document.querySelector('.c7n-pro-popup-container');
      if (popupContainer) {
        document.getElementById('operationPlatform')?.appendChild(popupContainer);
      }
    }
    if(type === 'blur'){
      const popupContainer = document.querySelector('.c7n-pro-popup-container');
      if (popupContainer) {
        document.querySelector('body')?.appendChild(popupContainer);
      }
    }
  }


  const handleChangeValue = (value) => {
    if(value){
      handleQuery(value);
    }
  }

  const handleRefresh = () => {
    handleQuery(formDs.current?.get('shiftDateCode'));
  }

  return (
    <CardLayout.Layout className={styles.schedulingTasks} spinning={loading}>
      <CardLayout.Header
        className='SchedulingTasksHead'
        title='调度任务'
        help={props?.cardUsage?.remark}
        addonAfter={
          <div className={styles.schedulingTasksRightHead}>
            <div style={{cursor: 'pointer'}} onClick={handleRefresh}><img src={refresh} alt='' /></div>
            <div className={styles.schedulingTasksProgress}>
              <div className={styles.schedulingTasksProgressText}>当前负荷</div>
              <Progress value={taskProgress} format={(percent) => `${percent}%`}/>
            </div>
            <div className={styles.schedulingTasksSelect}>
              <Select
                dataSet={formDs}
                placeholder='请输入班次日期或编号'
                name="shiftDateCode"
                searchable
                searchFieldInPopup
                searchFieldProps={{ multiple: false }}
                searchMatcher={searchMatcher}
                onChange={handleChangeValue}
                onFocus={() => handleChangeDom('focus')}
                onBlur={() => handleChangeDom('blur')}
              >
                {
                  shiftList.map(item => (
                    <Select.Option value={item.shiftDateCode} key={item.shiftDateCode}>
                      {item.shiftDateCode}
                    </Select.Option>
                  ))
                }
              </Select>
            </div>
          </div>
        }
      />
      <CardLayout.Content className='SchedulingTasksForm'>
        <Table
          dataSet={tableDs}
          columns={columns}
          customizedCode="SchedulingTasks"
          // autoHeight={{ type: 'minHeight', diff: 20 }}
          // footer={
          //   <div style={{ display: 'flex', marginTop: '20px' }}>
          //     <span style={{ color: 'white', width: '90px' }}>总任务进度</span>
          //     <Progress value={taskProgress} />
          //   </div>
          // }
        />
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(SchedulingTasks);
