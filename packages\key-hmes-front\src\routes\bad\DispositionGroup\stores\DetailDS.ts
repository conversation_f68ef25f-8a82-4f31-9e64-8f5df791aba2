/**
 * @Description: 处置组维护-详情页DS
 * @Author: <EMAIL>
 * @Date: 2023/2/7 10:42
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.method.bad.dispositionGroup';
const tenantId = getCurrentOrganizationId();

const formDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'dispositionGroupId',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-disposition-group/detail/ui`,
        method: 'GET',
      };
    },
    tls: ({ record }) => {
      const className = 'org.tarzan.method.domain.entity.MtDispositionGroup';
      return {
        data: { dispositionGroupId: record.get('dispositionGroupId') || '' },
        params: {
          fieldName: 'description',
          className,
        },
        url: `${BASIC.TARZAN_METHOD}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'dispositionGroupId',
      type: FieldType.number,
    },
    {
      name: 'dispositionGroup',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispositionGroupName`).d('处置组名称'),
      required: true,
    },
    {
      name: 'dispositionGroupDesc',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.dispositionGroupDesc`).d('处置组描述'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      ignore: FieldIgnore.always,
      required: true,
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
  ],
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'dispositionGroupMemberId',
  fields: [
    {
      name: 'dispositionFunctionId',
      type: FieldType.number,
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      required: true,
      min: 1,
      step: 1,
    },
    {
      name: 'dispositionFunctionLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dispositionFunctionName`).d('处置方法名称'),
      ignore: FieldIgnore.always,
      required: true,
      lovCode: 'MT.METHOD.DISPOSITION_FUNCTION',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'dispositionFunctionId',
      bind: 'dispositionFunctionLov.dispositionFunctionId',
    },
    {
      name: 'dispositionFunction',
      bind: 'dispositionFunctionLov.dispositionFunction',
    },
    {
      name: 'description',
      label: intl.get(`${modelPrompt}.dispositionFunctionDesc`).d('处置方法描述'),
      bind: 'dispositionFunctionLov.description',
    },
    {
      name: 'functionTypeDesc',
      label: intl.get(`${modelPrompt}.dispositionFunctionType`).d('方法类型'),
      bind: 'dispositionFunctionLov.functionTypeDesc',
    },
    {
      name: 'routerName',
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线'),
      bind: 'dispositionFunctionLov.routerName',
    },
  ],
});

export { formDS, lineDS };
