/**
 * @Description: 检验方案-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-06-15 11:37:54
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

// ${ BASIC.TARZAN_SAMPLING }


// 详情数据查询
export function fetchInspectSchemeTmp() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-scheme-tmp/detail/ui`,
    method: 'GET',
  };
}

// 详情数据查询
export function fetchInspectScheme() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/initial-managements/detail/ui`,
    method: 'GET',
  };
}

// 详情数据保存
export function saveInspectScheme() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/initial-managements/update/ui`,
    method: 'POST',
  };
}

// 获取检验业务类型列表可输入权限
export function fetchRuleDtlConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-ins-bus-type-rule-dtl/list/ui`,
    method: 'GET',
  };
}

// 根据物料和站点获取默认工艺路线
export function fetchMaterialLimitDefaultConfig() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-pfep-common/material-limit-default-get/for/ui`,
    method: 'POST',
  };
}

// 检验方案发布
export function inspectSchemePublishConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-scheme/publish/ui`,
    method: 'POST',
  };
}

// 检验方案发布确认
export function inspectSchemeChangePublishConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-scheme/change/publish/ui`,
    method: 'POST',
  };
}

// 检验项目详情
export function mtInspectItemDetailConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-item/detail/ui`,
    method: 'GET',
  };
}

// 检验项目详情
export function mtInspectSchemePageUiConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-scheme/page/ui`,
    method: 'POST',
  };
}

