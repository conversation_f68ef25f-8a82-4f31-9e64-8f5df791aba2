import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.receive.receiveReturn';
const tenantId = getCurrentOrganizationId();
// ${BASIC.HMES_BASIC}
const endUrl = '';
const HMES_BASIC = BASIC.HMES_BASIC;

const headerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'instructionDocId',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        // ${BASIC.CUSZ_CODE_BEFORE}
        url: `${HMES_BASIC}${endUrl}/v1/${tenantId}/receive-return-bill/page/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.HEAD`,
        method: 'POST',
      };
    },
  },
  queryFields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
    },
    {
      name: 'instructionDocObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('领退料类型'),
      lookupCode: 'MT.MATERIAL_REQUISITION_TYPE',
      valueField: 'value',
      textField: 'meaning',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'instructionDocType',
      bind: 'instructionDocObject.value',
    },
    {
      name: 'instructionType',
      bind: 'instructionDocObject.tag',
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lovPara: {
        tenantId,
      },
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lookupAxiosConfig: () => {
        return {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        };
      },
      dynamicProps: {
        lookupUrl: ({ record }) => {
          const type = record?.get('instructionType');
          if (type === 'PICK') {
            return `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_REQUISITION`;
          }
          if (type === 'RETURN') {
            return `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_REQUISITION_RETURN`;
          }
        },
        disabled: ({ record }) => {
          return !record.get('instructionDocType');
        },
      },
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'prodLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('生产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'prodLineId',
      type: FieldType.string,
      bind: 'prodLine.prodLineId',
    },
    {
      name: 'workOrder',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('来源工单号'),
      lovCode: 'MT.WORK_ORDER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workOrderId',
      bind: 'workOrder.workOrderId',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'demandTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandTimeFrom`).d('需求时间从'),
      max: 'demandTimeTo',
    },
    {
      name: 'demandTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandTimeTo`).d('需求时间至'),
      min: 'demandTimeFrom',
    },
    {
      name: 'printFlag',
      type: FieldType.string,
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`tarzan.common.printFlag`).d('打印标识'),
    },
    {
      name: 'userLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.creator`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      noCache: true,
      ignore: 'always',
      multiple: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'userIds',
      bind: 'userLov.id',
    },
    {
      name: 'materialLotCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCodes`).d('物料批'),
    },
    {
      name: 'containerCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCodes`).d('容器编码'),
    },
  ],
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('领退料类型'),
      lookupCode: 'MT.MATERIAL_REQUISITION_TYPE',
      valueField: 'value',
      textField: 'meaning',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_REQUISITION`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: () => {
        return {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        };
      },
    },
    {
      name: 'instructionDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'demandTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandTime`).d('需求时间'),
    },
    {
      name: 'printTimes',
      type: FieldType.string,
      label: intl.get(`tarzan.common.printTimes`).d('打印次数'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationBy`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

const lineTableDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: 'multiple',
    pageSize: 10,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    transport: {
      read: () => {
        return {
          // 采购订单列表 接口待替换
          // ${BASIC.CUSZ_CODE_BEFORE}
          url: `${HMES_BASIC}${endUrl}/v1/${tenantId}/receive-return-bill/line/query/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.LINE`,
          method: 'POST',
        };
      },
    },
    fields: [
      {
        name: 'lineNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      },
      {
        name: 'fromIdentifyType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.fromIdentifyType`).d('来源管理模式'),
      },
      {
        name: 'toIdentifyType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toIdentifyType`).d('目标管理模式'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.material`).d('物料'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'materialBomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialBomCode`).d('BOM号'),
      },
      {
        name: 'quantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.quantity`).d('制单数量'),
      },
      {
        name: 'minPackagingQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.minPackagingQty`).d('最小包装数量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'instructionStatusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.status`).d('状态'),
      },
      {
        name: 'sourceOrderNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.workOrderNum`).d('来源工单号'),
      },
      {
        name: 'productionLineCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.fromLineCode`).d('来源生产线'),
      },
      {
        name: 'receivedQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.receivedQty`).d('已领取数量'),
      },
      {
        name: 'signedQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.signedQty`).d('已签收数量'),
      },
      {
        name: 'returnedQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.returnedQty`).d('已退料数量'),
      },
      {
        name: 'fromLocatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.fromWarehouse`).d('来源仓库'),
        dynamicProps: {
          required: ({ record }) => {
            return record.get('fromLocatorRequiredFlag') === 'Y';
          },
        },
      },
      // {
      //   name: 'sourceSubLocatorCode',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.fromLocator`).d('来源库位'),
      // },
      {
        name: 'toLocatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toWarehouse`).d('目标仓库'),
        dynamicProps: {
          required: ({ record }) => {
            return record.get('toLocatorRequiredFlag') === 'Y';
          },
        },
      },
      {
        name: 'subLocatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.subLocatorCode`).d('目标库位'),
      },
      {
        name: 'targetLocation',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.targetLocation`).d('目标地点'),
      },
      {
        name: 'toleranceFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
      },
      {
        name: 'toleranceTypeDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceTypeDesc`).d('允差类型'),
      },
      {
        name: 'toleranceMaxValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差值'),
      },
      {
        name: 'toleranceMinValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差值'),
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
      {
        name: 'specifiedLot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.specifiedLot`).d('指定批次'),
      },
    ],
  };
};

export { headerTableDS, lineTableDS };
