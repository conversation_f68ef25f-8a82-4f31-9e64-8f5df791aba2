/**
 * @Description: 产线审核计划管理平台-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useState, useMemo } from 'react';
import {
  DataSet,
  Form,
  Lov,
  Select,
  Table,
  TextField,
  DateTimePicker,
  TextArea,
  Attachment,
  NumberField,
  Button,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Content, Header } from 'components/Page';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import { getCurrentOrganizationId, getCurrentUser } from 'hzero-front/lib/utils/utils';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ExpandCardC7n } from '@components/tarzan-ui';
import notification from 'utils/notification';
import {
  tableDetailDS,
  tableDetailLineDS,
} from '../stores/CommissionedExperimentalPlatformLineDetailDS';
import styles from './index.less';

const tenantId = getCurrentOrganizationId();
const currentUser = getCurrentUser();
const modelPrompt = 'key.hwms.front.CommissionedExperimentalPlatform';
// const statusList = ['NEW', 'REJECTED'];

const CommissionedExperimentalPlatformDetail = props => {
  const {
    match: {
      params: { id },
    },
    location: { state = {} },
  } = props;
  const { status } = state;

  const tableDetailDs = useMemo(() => new DataSet(tableDetailDS()), []);

  const tableDetailLineDs = useMemo(() => new DataSet(tableDetailLineDS()), []);

  const columnLine = useMemo(
    () => [
      {
        name: 'splitSampleCode',
        width: 180,
        onCell: columnsCell,
      },
      {
        name: 'inspectValue',
        width: 120,
        onCell: columnsCell,
        editor: record => {
          return record.getState('myEditing');
        },
      },
      {
        name: 'reinspectValue',
        width: 120,
        onCell: columnsCell,
        editor: record => record.getState('myEditing'),
      },
      {
        name: 'inspectUom',
        width: 120,
        onCell: columnsCell,
        editor: record => {
          return record.getState('myEditing');
        },
      },
      {
        name: 'remark',
        width: 120,
        onCell: columnsCell,
        editor: record => {
          return record.getState('myEditing');
        },
      },
      {
        name: 'enclosure',
        width: 120,
        onCell: columnsCell,
        editor: record => {
          return record.getState('myEditing');
        },
      },
    ],
    [],
  );

  const groups = useMemo(
    () => [
      {
        name: 'itemDesc',
        type: 'column',
        columnProps: {
          width: 120,
          onCell: columnsCell,
        },
      },
      {
        name: 'inspectMethod',
        type: 'column',
        columnProps: {
          width: 120,
          onCell: columnsCell,
        },
      },
      {
        name: 'inspectStandard',
        type: 'column',
        columnProps: {
          width: 150,
          onCell: columnsCell,
        },
      },
      {
        name: 'sampleQty',
        type: 'column',
        columnProps: {
          width: 100,
          onCell: columnsCell,
        },
      },
      {
        name: 'inspectUserName',
        type: 'column',
        columnProps: {
          width: 120,
          onCell: columnsCell,
        },
      },
    ],
    [],
  );

  const [editFlag, setEditFlag] = useState(false);

  const [disabledFlag, setDisabledFlag] = useState(false);

  const [lineBtnFlag, setLineBtnFlag] = useState(false);


  const [judege, setJudge] = useState(true);

  useEffect(() => {
    setDisabledFlag(true);
    setLineBtnFlag(true);
    queryDetail();
  }, [status]);
  // 保存
  const handSave = async () => {
    const { entrustApplyStatus, taskStatus, applyStatus, receiveSampleTime, startTime, endTime } = tableDetailDs.current.toData();
    if (entrustApplyStatus === 'COMPLETED' || entrustApplyStatus === 'CANCEL') {
      return notification.error({
        message: intl.get(`${modelPrompt}.message.selectMessage.one`).d('委托申请已完成或已取消，请检查！'),
      });
    }
    if(taskStatus !== 'COMPLETED' && applyStatus === 'NEW'){
      const inspectValueList = tableDetailLineDs.filter(record => record.get('inspectValue') === undefined);
      if(inspectValueList.length>0){
        notification.error({
          message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项'),
        });
        return false;
      }
    }
    if(taskStatus !== 'COMPLETED' && applyStatus === 'REJECT'){
      const reinspectValueList = tableDetailLineDs.filter(record => record.get('reinspectValue') === undefined);
      if(reinspectValueList.length>0){
        notification.error({
          message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项'),
        });
        return false;
      }
    }
    if (tableDetailLineDs.toData().length > 0) {
      setJudge(!judege);
      const res = await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-task/save`, {
        method: 'POST',
        body: tableDetailLineDs.toData().filter((ele, index) => {
          ele.itemSequence = index + 1;
          ele.receiveSampleTime = receiveSampleTime; // 收样时间
          ele.startTime = startTime; // 开始时间
          ele.endTime = endTime; // 结束时间
          // return ele._status !== 'delete';
          return ele;
        }),
      });
      if (res && !res.failed) {
        setJudge(!judege);
        notification.success({});
        setDisabledFlag(true);
        setLineBtnFlag(true);
        queryDetail();
      } else {
        return notification.error({ message: res.message });
      }
    } else {
      return notification.error({ 
        message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项'),
      });
    }
  };
  const handleValidate = async () => {
    const formValidate = await tableDetailDs.validate();
    const tableValidate = await tableDetailLineDs.validate();
    return formValidate && tableValidate;
  };

  // 保存提交
  const handleSaveSubmit = async () => {
    const flag = await handleValidate();
    const taskStatus = tableDetailDs.toData()[0].taskStatus;
    if (taskStatus === 'COMPLETED') {
      return notification.error({
        message: intl.get(`${modelPrompt}.message.selectMessage.two`).d('仅允许提交状态不为”关闭“的检验任务，请检查！'),
      });
    }
    const entrustApplyStatus = tableDetailDs.toData()[0].entrustApplyStatus;
    if (entrustApplyStatus === 'COMPLETED' || entrustApplyStatus === 'CANCEL') {
      return notification.error({
        message: intl.get(`${modelPrompt}.message.selectMessage.one`).d('委托申请已完成或已取消，请检查！'),
      });
    }
    if(tableDetailDs.toData()[0].taskStatus !== 'COMPLETED' && tableDetailDs.toData()[0].applyStatus === 'NEW'){
      const inspectValueList = tableDetailLineDs.filter(item => item.toData().inspectValue === undefined);
      if(inspectValueList.length>0){
        notification.error({
          message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项'),
        });
        return false;
      }
    }
    if(tableDetailDs.toData()[0].taskStatus !== 'COMPLETED' && tableDetailDs.toData()[0].applyStatus === 'REJECT'){
      const reinspectValueList = tableDetailLineDs.filter(item => item.toData().reinspectValue === undefined);
      if(reinspectValueList.length>0){
        notification.error({
          message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项'),
        });
        return false;
      }
    }
    if (flag && tableDetailLineDs.toData().length > 0) {
      const res = await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-task/save/submit`, {
        method: 'POST',
        body: tableDetailLineDs.toData().filter((ele, index) => {
          ele.itemSequence = index + 1;
          ele.receiveSampleTime = tableDetailDs.toData()[0].receiveSampleTime; // 收样时间
          ele.startTime = tableDetailDs.toData()[0].startTime; // 开始时间
          ele.endTime = tableDetailDs.toData()[0].endTime; // 结束时间
          return ele;
        }),
      });
      if (res && !res.failed) {
        notification.success({});
        setDisabledFlag(true);
        setLineBtnFlag(true);
        queryDetail();
      } else {
        return notification.error({ message: res.message });
      }
    } else {
      return notification.error({ 
        message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项'),
      });
    }
  };

  // 正常进入查询
  const queryDetail = () => {
    request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-apply/detail`, {
      method: 'GET',
      query: {
        taskNum: id,
      },
    }).then(res => {
      if (res && !res.failed) {
        tableDetailDs.loadData([res]);
        if (res.inspectUserId === currentUser.id && res?.taskStatus !== 'COMPLETED') {
          setEditFlag(false);
        } else {
          setEditFlag(true);
        }
        request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-task/detail`, {
          method: 'GET',
          query: {
            entrustTaskId: res.entrustTaskId,
          },
        }).then(res1 => {
          if (res1 && !res1.failed) {
            tableDetailLineDs.loadData(res1);
          } else {
            return notification.error({ message: res.message });
          }
        });
      } else {
        return notification.error({ message: res.message });
      }
    });
  };

  // 渲染单元格
  const columnsCell = () => {
    return {
      className: `MaterialStaginghighLightRow`,
    };
  };

  // 编辑
  const handEdit = () => {
    setDisabledFlag(!disabledFlag);
    setLineBtnFlag(!lineBtnFlag);
    tableDetailLineDs.all.forEach(v => {
      v.setState('myEditing', disabledFlag);
    });
  };

  const handCancel = () => {
    if (status === 'create' || status === 'module') {
      props.history.push('/hwms/commissioned-experimental-platform');
    } else {
      queryDetail();
    }
    setDisabledFlag(!disabledFlag);
    setLineBtnFlag(!lineBtnFlag);
    tableDetailLineDs.all.forEach(v => {
      v.setState('myEditing', disabledFlag);
    });
  };

  return (
    <div style={{ height: '100%' }} className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.lineDetail`).d('委托实验执行')}
        backPath="/hwms/commissioned-experimental-platform/list"
      >
        <>
          {!lineBtnFlag && (
            <>
              <Button
                icon="save"
                color={ButtonColor.primary}
                onClick={() => handleSaveSubmit()}
              >
                {intl.get(`${modelPrompt}.flims.NewProjectPlanApplication.button.submitSave`).d('保存并提交')}
              </Button>
              <Button
                icon="save"
                color={ButtonColor.primary}
                onClick={() => handSave()}
              >
                {intl.get('hzero.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={() => handCancel()}>
                {intl.get('hzero.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {lineBtnFlag && (
            <>
              <PermissionButton
                color={ButtonColor.primary}
                type="c7n-pro"
                permissionList={[
                  {
                    code: `${modelPrompt}.taskDist.button.edit`,
                    type: 'button',
                    meaning: '任务详情页-编辑按钮',
                  },
                ]}
                disabled={editFlag}
                onClick={() => handEdit()}
              >
                {intl.get('hzero.common.button.edit').d('编辑')}
              </PermissionButton>
            </>
          )}
          {/* <ApprovalInfoDrawer objectTypeList={['QIS_CXSH_LWS']} objectId={id} /> */}
        </>
      </Header>
      <Content>
        <ExpandCardC7n
          showExpandIcon
          title={intl.get(`${modelPrompt}.fydstms.common.model.message.basicInfo`).d('基础信息')}
        >
          <Form dataSet={tableDetailDs} columns={3} labelWidth={110} disabled={disabledFlag}>
            <TextField name="entrustApplyCode" />
            <Lov name="siteObj" />
            <Lov name="materialObj" />
            <TextField name="materialName" />
            <Lov name="unitObj" />
            <TextField name="entrustUserName" />
            <NumberField name="sampleQty" precision={0} />
            <TextField name="sampleUom" />
            <DateTimePicker name="expectCompleteTime" />
            <TextArea name="reason" newLine colSpan={3} />
            <Select name="sampleDisposalMethod" />
            <Attachment name="relatedFileUuid" />
            <TextArea name="remark" newLine colSpan={3} />
            <TextArea name="oaApplyDesc" newLine colSpan={3} />
            <DateTimePicker name="receiveSampleTime" />
            <DateTimePicker name="startTime" />
            <DateTimePicker name="endTime" />
            {/* <Attachment readOnly {...panelDetailEnclosure} /> */}
          </Form>
        </ExpandCardC7n>
        {/* <ExpandCardC7n
          showExpandIcon
          title={intl.get('fydstms.common.model.message.ListTable').d('产线审核要素')}
        > */}
        <div className={styles.commissionExperimentalPlatform}>
          <Table
            // customizable
            // customizedCode="CommissionedExperimentalPlatformTableDetailLine"
            groups={groups}
            dataSet={tableDetailLineDs}
            columns={columnLine}
            queryBar={TableQueryBarType.none}
            queryFieldsLimit={3}
            pagination={{ showPager: true }}
            queryBarProps={{ autoQueryAfterReset: false }}
            rowHeight={34}
            border
          />
        </div>
        {/* </ExpandCardC7n> */}
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: [
    'hzero.common',
    'key.hwms.front.CommissionedExperimentalPlatform',
  ],
})(CommissionedExperimentalPlatformDetail);
