import React, { useMemo, useCallback, useState } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Record } from 'choerodon-ui/dataset';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { headDS, toBeProcessedDS } from './stores';
import { useDataSetEvent } from 'utils/hooks';

const modelPrompt = 'modelPrompt_code';
const { Panel } = Collapse;

const WorkInProgressReportList = observer((props) => {
  const {
    // match: { path },
    headDs,
    toBeProcessedDs,
    // history,
    // location,
  } = props;

  // useEffect(() => {
  //   if (location?.state?._back) {
  //     // 详情页点取消跳转回来，query为空对象，会有state._back = -1
  //     headDs.query(headDs.currentPage);
  //   }
  // }, [location?.state]);

  useDataSetEvent(headDs.queryDataSet, 'update', ({ name, value }) => {
    if(name === 'productionLine'){
      headDs.queryDataSet.current.set('operation', null);
      if(value === null){
        headDs.loadData([])
      }
    }
  });

  useDataSetEvent(headDs, 'load', () => {
    setCurrentLineType('');
  });

  const [currentLineType, setCurrentLineType] = useState('');
  const [currentLineTitle, setCurrentLineTitle] = useState('');

  const makeLineTableQuery = useCallback(
    (type: string,record: Record) => {
      toBeProcessedDs.setQueryParameter('queryType', type);
      toBeProcessedDs.setQueryParameter('prodLineId', record.get('prodLineId'));
      toBeProcessedDs.setQueryParameter('prodLineCode', record.get('prodLineCode'));
      toBeProcessedDs.setQueryParameter('operationId', record.get('operationId'));
      toBeProcessedDs.setQueryParameter('workcellId', record.get('workcellId'));
      toBeProcessedDs.query();
    },
    [],
  )

  const handleQueryLine = useCallback(
    (type, record) => {
      setCurrentLineType(type)
      switch (type) {
        case 'WAITING': // 待加工
          setCurrentLineTitle('待加工列表')
          break;
        case 'WORKING': // 在制
          setCurrentLineTitle('工序在制列表')
          break;
        case 'INVENTORY': // 库存
          setCurrentLineTitle('工序库存列表')
          break;
        default:
          break;
      }
      makeLineTableQuery(type, record)
    },
    [],
  )

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'siteCode',
      },
      {
        name: 'areaCode',
      },
      { name: 'prodLineCode' },
      { name: 'workcellCode' },
      {
        name: 'waitProcessingQty',
        renderer: ({ value, record }) => {
          return (
            <a onClick={() => {handleQueryLine('WAITING', record)}}>
              {value}
            </a>
          )
        },
      },
      { name: 'operationName' },
      {
        name: 'wipQty',
        renderer: ({ value, record }) => {
          return (
            <a onClick={() => {handleQueryLine('WORKING', record)}}>
              {value}
            </a>
          )
        },
      },
      {
        name: 'inventoryQty',
        renderer: ({ value, record }) => {
          return (
            <a onClick={() => {handleQueryLine('INVENTORY', record)}}>
              {value}
            </a>
          )
        },
      },
    ];
  }, []);

  const toBeProcessedColumns: ColumnProps[] = useMemo(() => {
    let columns = [{}];
    switch (currentLineType) {
      case 'WAITING': // 待加工
        columns = [
          {
            name: 'identification',
          },
          {
            name: 'eoNum',
          },
          { name: 'workOrderNum' },
          { name: 'qty' },
          { name: 'creationDate' },
          {
            name: 'distanceToNow',
          },
          {
            name: 'createdByName',
          },
        ]
        break;
      case 'WORKING': // 在制
        columns = [
          {
            name: 'identification',
          },
          {
            name: 'eoNum',
          },
          { name: 'workOrderNum' },
          { name: 'qty', header: '数量' },
          { name: 'creationDate' },
          {
            name: 'distanceToNow',
          },
          {
            name: 'createdByName',
          },
        ]
        break;
      case 'INVENTORY': // 库存
        columns = [
          {
            name: 'identification',
          },
          {
            name: 'eoNum',
          },
          { name: 'workOrderNum' },
          { name: 'qty', header: '数量' },
          { name: 'creationDate' },
          {
            name: 'distanceToNow',
          },
          {
            name: 'createdByName',
          },
        ]
        break;
      default:
          break;
    }
    return columns;
  }, [currentLineType]);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('在制品报表')} />
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headDs}
          columns={headColumns}
          searchCode="page_searchCode"
          customizedCode="page_customizedCode"
        />
        <Collapse bordered={false} defaultActiveKey={['lineTable']}>
          {
            currentLineType && (
            <Panel
              key="lineTable"
              header={currentLineTitle}
            >
              <Table
                dataSet={toBeProcessedDs}
                columns={toBeProcessedColumns}
                customizedCode="WorkInProgressReportListLine1"
              />
            </Panel>
            )
          }
        </Collapse>
      </Content>
    </div>
  );
})

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const toBeProcessedDs = new DataSet(toBeProcessedDS());
      return {
        headDs,
        toBeProcessedDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(WorkInProgressReportList),
);
