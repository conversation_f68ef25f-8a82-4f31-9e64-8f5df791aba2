/**
 * @Description: 检验平台-检验项目行模式带对象
 * @Author: <<EMAIL>>
 * @Date: 2023-02-14 15:23:08
 * @LastEditTime: 2023-05-18 16:59:21
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect } from 'react';
import intl from 'utils/intl';
import {
  Col,
  DateTimePicker,
  Form,
  NumberField,
  Currency,
  Row,
  Select,
  Table,
  TextField,
  Lov,
  Attachment,
} from 'choerodon-ui/pro';
import { Popconfirm, Tag, Popover } from 'choerodon-ui';
import uuid from 'uuid/v4';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { Tooltip } from 'choerodon-ui/pro/lib/core/enum';
import { maxBy, isNumber } from 'lodash';
import { useDataSetEvent } from 'utils/hooks';
import { BASIC } from '@utils/config';
import NcRecordComponent from './NcRecordComponent';
import styles from './index.modules.less';

const { Option } = Select;
const { ItemGroup, Item } = Form;

const modelPrompt = 'tarzan.qms.inspectionPlatform';

const InspectItemRowObjComponent = props => {
  const {
    NcRecordDimension,
    canEdit,
    inspectInfoDS,
    inspectItemRowDS,
    inspectItemRowObjValueDS,
    inspectLovDS,
    customizeTable,
    cacheMinWidth,
    setCacheMinWidth,
    onAutoEnterDefaultValue,
    handleChangeValueColor,
    handleComputedQty,
    handleChangeInspectItem,
    handleItemScanInspectObj,
    handleChangeNgQty,
    handleBatchChangeData,
  } = props;

  // 是否为自定义抽样显示加减操作
  const [userDefinedFlag, setUserDefinedFlag] = useState(false);

  useEffect(() => {
    // const _samplingType = inspectItemRowDS.current?.get('samplingType');
    // setUserDefinedFlag(_samplingType === 'USER_DEFINED_SAMPLING');
    setUserDefinedFlag(true);
  }, [inspectItemRowDS.current]);

  const handleInspectValueUpdate = ({ dataSet, name }) => {
    const _dataType = inspectItemRowDS.current?.get('dataType');
    if (_dataType === 'CALCULATE_FORMULA') {
      inspectItemRowDS.current.set('taskLineObjects', dataSet.data || []);
    }
    if (name.indexOf('VALUE') !== -1) {
      if (inspectInfoDS.current?.get('resultDimension') === 'RECORD_SAMPLE_VALUE') {
        inspectItemRowDS.setState('resultDimension', 'RECORD_SAMPLE_VALUE');
        if (inspectItemRowDS.current?.get('samplingType') === 'USER_DEFINED_SAMPLING') {
          inspectItemRowDS.current.set('update', uuid());
        }
      } else {
        inspectItemRowDS.setState('resultDimension', null);
      }
    }
  };
  useDataSetEvent(inspectItemRowObjValueDS, 'update', handleInspectValueUpdate);

  // 切换当前检验项行
  const handleChangeCurrentLine = () => {
    const _dataType = inspectItemRowDS.current?.get('dataType');
    // const _samplingType = inspectItemRowDS.current?.get('samplingType');
    // setUserDefinedFlag(_samplingType === 'USER_DEFINED_SAMPLING');
    setUserDefinedFlag(true);
    if (_dataType === 'CALCULATE_FORMULA') {
      const _taskLineObjects = inspectItemRowDS.current.get('taskLineObjects') || [];
      inspectItemRowObjValueDS.loadData(_taskLineObjects);
    } else {
      let _dataQty = 0;
      if (
        inspectItemRowDS.current?.get('dataQtyDisposition') === 'DATA' ||
        inspectItemRowDS.current?.get('dataQtyDisposition') === 'SAMPLE'
      ) {
        _dataQty = 1;
      } else {
        _dataQty = Number(inspectItemRowDS.current?.get('dataQty') || 0);
      }

      if (_dataQty > 0) {
        setCacheMinWidth(_dataQty * 71 + 32);
      }
      // 当前检验对象存在时切换检验项当前行，自动添加对应数据
      // const _sourceObjectObj = inspectLovDS.current?.get('sourceObjectObj');
      // if (_sourceObjectObj?.inspectObjectId) {
      //   const _existCurObjFlag = inspectItemRowObjValueDS.records.find(
      //     item => item.get('sourceObjectCode') === _sourceObjectObj.sourceObjectCode,
      //   );
      //   if (!_existCurObjFlag) {
      //     // 判断是不是有值的行和抽样数相等
      //     const _emptyRecords = inspectItemRowObjValueDS.records.filter(
      //       item => !item.get('sourceObjectCode'),
      //     );
      //     if (_emptyRecords.length > 0) {
      //       _emptyRecords[0].set('inspectObjectId', _sourceObjectObj.inspectObjectId);
      //       _emptyRecords[0].init('sourceObjectCode', _sourceObjectObj.sourceObjectCode);
      //       _emptyRecords[0].set('sequence', _sourceObjectObj.sequence);
      //       onAutoEnterDefaultValue([_emptyRecords[0]]);
      //     } else if (_samplingType === 'USER_DEFINED_SAMPLING') {
      //       handleAdd(
      //         {
      //           inspectObjectId: _sourceObjectObj.inspectObjectId,
      //           sourceObjectCode: _sourceObjectObj.sourceObjectCode,
      //           sequence: _sourceObjectObj.sequence,
      //         },
      //         true,
      //       );
      //     }
      //   }
      // }
    }
  };

  // 设置录入值宽度
  // 抽样数量改变时自动计算录入值列宽
  const handleChangeInspectTextWidth = () => {
    const inspectBusinessType = inspectInfoDS.current.get('inspectBusinessType');
    const _samplingQtyCount = Number(inspectItemRowDS.current?.get('samplingQtyCount') || 0);
    let maxQtyRecord;
    if (inspectBusinessType === 'RATO-IQC' || inspectBusinessType === 'RATO-SQC') {
      maxQtyRecord = maxBy(inspectItemRowObjValueDS.records, record => {
        const _frequencyQtyCount = !isNaN(record?.get('recordFrequency'))
          ? Number(record?.get('recordFrequency') || 0)
          : 1;
        let _count = _samplingQtyCount;
        if (_frequencyQtyCount > 1) {
          _count = Number(_frequencyQtyCount) <= _count ? Number(_frequencyQtyCount) : _count;
        } else {
          _count = _count >= 1 ? 1 : _count;
        }
        return _count + Number(record.get('addQtyCount') || 0);
      });
    } else {
      maxQtyRecord = maxBy(
        inspectItemRowObjValueDS.records,
        record => _samplingQtyCount + Number(record.get('addQtyCount') || 0),
      );
    }

    const maxQty =
      Number(maxQtyRecord?.get('samplingQtyCount') || 0) +
      Number(maxQtyRecord.get('addQtyCount') || 0);
    if (maxQty > 2) {
      setCacheMinWidth(maxQty * 65 + (maxQty - 1) * 5 + 16);
    } else {
      setCacheMinWidth(180);
    }
    inspectItemRowObjValueDS.setState('maxSamplingQty', maxQty);
  };

  // 添加检验对象
  const handleAdd = (params = {}, selectFlag = false) => {
    const _fieldName = inspectItemRowDS.current?.get('fieldName');
    const _requiredFlag = inspectItemRowDS.current?.get('requiredFlag');
    const _dataType = inspectItemRowDS.current?.get('dataType');
    const newRecord = inspectItemRowObjValueDS.create({
      cacheInspectObjectId: uuid(),
      fieldName: _fieldName,
      requiredFlag: _requiredFlag,
      ...params,
    });
    inspectItemRowDS.current?.set('samplingQty', inspectItemRowObjValueDS.length);
    handleComputedQty(_fieldName, _dataType, true, true, false, false);
    if (selectFlag) {
      onAutoEnterDefaultValue([newRecord]);
    }
  };

  // 删除检验对象
  const handleDelete = record => {
    // 记录删除dtlId
    const _itemColUpdateCacheObj = inspectInfoDS.getState('itemColUpdateCacheObj') || {};
    const _inspectDocLineActDtlIds = _itemColUpdateCacheObj?.deleteDtlIds || [];
    const _dtlIds = record.get('dtlIds') || [];
    _dtlIds.forEach(dtlId => {
      _inspectDocLineActDtlIds.push(dtlId);
    });
    _itemColUpdateCacheObj.deleteDtlIds = _inspectDocLineActDtlIds;
    inspectInfoDS.setState('itemColUpdateCacheObj', _itemColUpdateCacheObj);

    if (userDefinedFlag) {
      inspectItemRowObjValueDS.remove(record);
      inspectItemRowDS.current?.set('samplingQty', inspectItemRowObjValueDS.length);
    } else {
      const _fieldName = inspectItemRowDS.current?.get('fieldName');
      const _requiredFlag = inspectItemRowDS.current?.get('requiredFlag');
      Object.keys(record.toData()).forEach(_key => {
        record.init(_key, null);
      });
      record.init('cacheInspectObjectId', uuid());
      record.init('fieldName', _fieldName);
      record.init('requiredFlag', _requiredFlag);
    }
    const _fieldName = inspectItemRowDS.current?.get('fieldName');
    const _dataType = inspectItemRowDS.current?.get('dataType');
    handleComputedQty(_fieldName, _dataType, true, true, false, false);
  };

  const attachmentProps: any = {
    bucketName: 'qms',
    bucketDirectory: 'inspection-platform-workshop',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const columns: ColumnProps[] = [
    {
      name: 'sequence',
      width: 60,
      align: ColumnAlign.center,
    },
    {
      name: 'inspectItemDesc',
      minWidth: 120,
      renderer: ({ value, record }) => {
        return (
          <span>
            {record?.get('requiredFlag') === 'Y' && <span style={{ color: 'red' }}>*&nbsp;</span>}
            {value}
          </span>
        );
      },
    },
    {
      name: 'inspectItemTypeDesc',
    },
    {
      name: 'inspectToolAndMethod',
    },
    {
      name: 'acceptStandard',
    },
    {
      name: 'samplingQty',
      minWidth: 150,
      align: ColumnAlign.left,
      renderer: ({ value, record }) => {
        if (!record) {
          return;
        }
        return (
          <span>
            {value}
            {/* &nbsp;{record?.get('uomName')} */}
          </span>
        );
      },
    },
    {
      name: 'valueRange',
      width: 180,
      title: intl.get(`${modelPrompt}.model.line.valueRange`).d('符合值/不符合值/预警值'),
      tooltip: Tooltip.none,
      renderer: ({ record }) => {
        return (
          <Popover
            placement="top"
            content={
              <div>
                {(record?.get('trueValues') || []).map(item => (
                  <div
                    className={styles['table-tooltip-tag']}
                    style={{ color: '#11d954', backgroundColor: '#E6FFEA' }}
                  >
                    {item}
                  </div>
                ))}
                {(record?.get('falseValues') || []).map(item => (
                  <div
                    className={styles['table-tooltip-tag']}
                    style={{ color: '#f23a50', backgroundColor: '#fff0f0' }}
                  >
                    {item}
                  </div>
                ))}
                {(record?.get('warningValues') || []).map(item => (
                  <div
                    className={styles['table-tooltip-tag']}
                    style={{ color: '#fbad00', backgroundColor: '#fffbe6' }}
                  >
                    {item}
                  </div>
                ))}
                &nbsp;{record?.get('uomName')}
              </div>
            }
            trigger="hover"
          >
            <div>
              {(record?.get('trueValues') || []).map(item => (
                <Tag color="green">{item}</Tag>
              ))}
              {(record?.get('falseValues') || []).map(item => (
                <Tag color="red">{item}</Tag>
              ))}
              {(record?.get('warningValues') || []).map(item => (
                <Tag color="yellow">{item}</Tag>
              ))}
              &nbsp;{record?.get('uomName')}
            </div>
          </Popover>
        );
      },
    },
    {
      name: 'okQty',
    },
    {
      name: 'ngQty',
      editor: record =>
        inspectInfoDS.getState('canEdit') && (
          <NumberField
            onChange={(newValue, oldValue) => handleChangeNgQty(newValue, oldValue, '', record)}
          />
        ),
    },
    {
      name: 'inspectResult',
      header: (...args: Array<any>) => {
        return (
          <a onClick={() => handleBatchChangeData('BATCH_RESULT')} style={{ cursor: 'pointer' }}>
            {args[2]}
          </a>
        );
      },
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        if (!record) {
          return '';
        }
        return (
          <Select
            record={record}
            name="inspectResult"
            readOnly={!inspectInfoDS.getState('canEdit')}
            style={{
              verticalAlign: 'baseline',
              backgroundColor:
                value === 'OK' ? 'rgb(230, 255, 234)' : value === 'NG' ? 'rgb(255, 240, 240)' : '',
            }}
          />
        );
      },
    },
    {
      name: 'sourceInspectValue',
      hidden: `${inspectInfoDS.current?.get('inspectTimes')}` === '1',
    },
    {
      name: 'inspectValueRecord',
    },
    {
      name: 'remark',
      editor: () => inspectInfoDS.getState('canEdit'),
      width: 120,
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      width: 380,
      renderer: ({ record }) => {
        return (
          <>
            <Attachment
              {...attachmentProps}
              record={record}
              name="enclosure"
              readOnly
              disabled={!inspectInfoDS.getState('canEdit')}
            />
            <Attachment
              {...attachmentProps}
              record={record}
              name="actEnclosure"
              disabled={!inspectInfoDS.getState('canEdit')}
            />
            <NcRecordComponent
              canEdit={inspectInfoDS.getState('canEdit')}
              type="text"
              visible={
                inspectInfoDS.current?.get('inspectNcRecordDimension') ===
                  NcRecordDimension.itemNc && !!record?.get('inspectTaskLineId')
              }
              customizeTable={customizeTable}
              queryParams={{
                inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
                inspectDocId: inspectInfoDS.current?.get('inspectDocId'),
                inspectTaskLineId: record?.get('inspectTaskLineId'),
                inspectItemId: record?.get('inspectItemId'),
                inspectDocLineId: record?.get('inspectDocLineId'),
                inspectItemDesc: record?.get('inspectItemDesc'),
                inspectNcRecordDimension: inspectInfoDS.current?.get('inspectNcRecordDimension'),
              }}
              style={{ marginLeft: '0.16rem' }}
            />
          </>
        );
      },
    },
  ];

  const objColumns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleAdd}
          disabled={!canEdit || !userDefinedFlag}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `inspectionPlatform.dist.button.line.add`,
              type: 'button',
              meaning: '详情页-添加删除行',
            },
          ]}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 50,
      renderer: ({ record }) => (
        <Popconfirm
          title={userDefinedFlag ? intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?') : intl.get(`tarzan.common.message.confirm.clear`).d('是否确认清空?')}
          onConfirm={() => handleDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
            disabled={!canEdit}
            permissionList={[
              {
                code: `inspectionPlatform.dist.button.line.add`,
                type: 'button',
                meaning: '详情页-添加删除行',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sourceObjectCode',
      width: 150,
      renderer: ({ value, record }) => {
        const _canEdit = inspectInfoDS.getState('canEdit');
        const _resultDimension = inspectInfoDS.current?.get('resultDimension');
        const _dataType = inspectItemRowDS.current?.get('dataType');
        if (record?.get('cacheInspectObjectId') && _resultDimension === 'RECORD_SAMPLE_VALUE') {
          return (
            <TextField
              record={record}
              name="sourceObjectCode"
              disabled={!_canEdit || _dataType === 'CALCULATE_FORMULA'}
              onChange={(val, oldValue) => handleChangeInspectItem(val, oldValue, record)}
              onEnterDown={event => handleItemScanInspectObj(event, record)}
              style={{ verticalAlign: 'baseline', width: '100%' }}
            />
          );
        }
        return value;
      },
    },
    {
      name: 'inspectValueRecord',
      width: cacheMinWidth,
      minWidth: 180,
      defaultWidth: cacheMinWidth,
      renderer: ({ record, dataSet }) => {
        const currentIndex = dataSet?.currentIndex || 0;
        if (!record) {
          return;
        }
        const _canEdit = inspectInfoDS.getState('canEdit');
        const _dataType = inspectItemRowDS.current?.get('dataType');
        const _decimalNumber = inspectItemRowDS.current?.get('decimalNumber');

        let _dataQty = 0;
        if (
          inspectItemRowDS.current?.get('dataQtyDisposition') === 'DATA' ||
          inspectItemRowDS.current?.get('dataQtyDisposition') === 'SAMPLE'
        ) {
          _dataQty = 1;
        } else {
          _dataQty = Number(inspectItemRowDS.current?.get('dataQty') || 0);
        }

        const _fieldName = inspectItemRowDS.current?.get('fieldName');
        const _addQtyCount = Number(record?.get('addQtyCount') || 0);
        let _count =
          _dataType === 'CALCULATE_FORMULA'
            ? Number(record.get(`${_fieldName}_FORMULA_COUNT`) || 0)
            : _dataQty;
        const _dataQtyValue: Array<any> = [];

        // 取最大的数
        const taskLineActDtls = record?.get('taskLineActDtls')?.length || 0;
        if (taskLineActDtls > _count) {
          _count = taskLineActDtls;
        }

        _count += _addQtyCount;

        for (let i = 0; i < _count; i++) {
          _dataQtyValue.push(`${_fieldName}_VALUE${i}`);
        }
        const _samplingQty = Number(inspectItemRowDS.current?.get('samplingQty') || 0);
        const _trueValues = inspectItemRowDS.current?.get('trueValues') || [];
        const _falseValues = inspectItemRowDS.current?.get('falseValues') || [];
        const _valueLists = inspectItemRowDS.current?.get('valueLists') || [];

        const _disabled = inspectItemRowDS.current?.get('dataType') === 'CALCULATE_FORMULA';
        // 计算列宽
        const _valueWidth = `${100 / _count}%`;
        return (
          <ItemGroup>
            {_dataQtyValue.map((valueName, index) => {
              if (_dataType === 'VALUE' && !_decimalNumber && _decimalNumber !== 0) {
                return (
                  <Item name={valueName}>
                    <NumberField
                      record={record}
                      name={valueName}
                      onEnterDown={() => {
                        if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                          return;
                        }
                        // 新增字段
                        // 获取之前的输入框是否为必输
                        const required = record?.getField(valueName)?.required;
                        const type = record?.getField(valueName)?.type;
                        record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                          name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                          type,
                          required,
                        });
                        record.set('addQtyCount', _addQtyCount + 1);
                        handleChangeInspectTextWidth();

                        // 聚焦
                        setTimeout(() => {
                          const elementInput = document.getElementsByName(
                            `${_fieldName}_VALUE${_dataQtyValue.length}`,
                          );
                          if (elementInput.length > 0) {
                            if (currentIndex < elementInput.length) {
                              elementInput[currentIndex].focus();
                            } else {
                              elementInput[0].focus();
                            }
                          }
                        }, 100);
                      }}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      required
                      className={
                        record?.get(`${valueName}_COLOR`)
                          ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${
                            styles['number-input-left']
                          } c7n-pro-input-number-required c7n-pro-input-number-required-colors`
                          : `${styles['number-input-left']} c7n-pro-input-number-required c7n-pro-input-number-required-colors`
                      }
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={(value, oldValue) => {
                        addInspectObject(value, record);
                        if (
                          !(
                            (value === undefined || value === null) &&
                            (oldValue === undefined || oldValue === null)
                          )
                        ) {
                          handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
                        }
                      }}
                    />
                  </Item>
                );
              }
              if (_dataType === 'VALUE' && isNumber(_decimalNumber) && _decimalNumber >= 0) {
                return (
                  <Item name={valueName}>
                    <Currency
                      record={record}
                      name={valueName}
                      precision={_decimalNumber > 6 ? 6 : _decimalNumber}
                      onEnterDown={() => {
                        if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                          return;
                        }
                        // 新增字段
                        // 获取之前的输入框是否为必输
                        const required = record?.getField(valueName)?.required;
                        const type = record?.getField(valueName)?.type;
                        record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                          name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                          type,
                          required,
                        });
                        record.set('addQtyCount', _addQtyCount + 1);
                        handleChangeInspectTextWidth();

                        // 聚焦
                        setTimeout(() => {
                          const elementInput = document.getElementsByName(
                            `${_fieldName}_VALUE${_dataQtyValue.length}`,
                          );
                          if (elementInput.length > 0) {
                            if (currentIndex < elementInput.length) {
                              elementInput[currentIndex].focus();
                            } else {
                              elementInput[0].focus();
                            }
                          }
                        }, 100);
                      }}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      className={
                        record?.get(`${valueName}_COLOR`)
                          ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${
                            styles['number-input-left']
                          } c7n-pro-input-number-required c7n-pro-input-number-required-colors`
                          : `${styles['number-input-left']} c7n-pro-input-number-required c7n-pro-input-number-required-colors`
                      }
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={(value, oldValue) => {
                        addInspectObject(value,record);
                        if (
                          !(
                            (value === undefined || value === null) &&
                            (oldValue === undefined || oldValue === null)
                          )
                        ) {
                          handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
                        }
                      }}
                    />
                  </Item>
                );
              }
              if (_dataType === 'DECISION_VALUE') {
                return (
                  <Item name={valueName}>
                    <Select
                      record={record}
                      name={valueName}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      required
                      className={
                        record?.get(`${valueName}_COLOR`)
                          ? `${
                            styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                          } c7n-pro-select-required c7n-pro-select-required-colors`
                          : 'c7n-pro-select-required c7n-pro-select-required-colors'
                      }
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={value => {
                        addInspectObject(value, record);
                        handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
                      }
                      }
                    >
                      {_trueValues.concat(_falseValues).map(item => (
                        <Option value={item} key={item}>
                          {item}
                        </Option>
                      ))}
                    </Select>
                  </Item>
                );
              }
              if (_dataType === 'TEXT') {
                return (
                  <Item name={valueName}>
                    <TextField
                      record={record}
                      name={valueName}
                      onEnterDown={() => {
                        if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                          return;
                        }
                        // 新增字段
                        // 获取之前的输入框是否为必输
                        const required = record?.getField(valueName)?.required;
                        const type = record?.getField(valueName)?.type;
                        record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                          name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                          type,
                          required,
                        });
                        record.set('addQtyCount', _addQtyCount + 1);
                        handleChangeInspectTextWidth();

                        // 聚焦
                        setTimeout(() => {
                          const elementInput = document.getElementsByName(
                            `${_fieldName}_VALUE${_dataQtyValue.length}`,
                          );
                          if (elementInput.length > 0) {
                            if (currentIndex < elementInput.length) {
                              elementInput[currentIndex].focus();
                            } else {
                              elementInput[0].focus();
                            }
                          }
                        }, 100);
                      }}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      className="c7n-pro-input-required c7n-pro-input-required-colors"
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={() => handleComputedQty(_fieldName, _dataType)}
                    />
                  </Item>
                );
              }
              if (_dataType === 'VALUE_LIST') {
                return (
                  <Item name={valueName}>
                    <Select
                      record={record}
                      name={valueName}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      className={
                        record?.get(`${valueName}_COLOR`)
                          ? `${
                            styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                          } c7n-pro-select-required c7n-pro-select-required-colors`
                          : 'c7n-pro-select-required c7n-pro-select-required-colors'
                      }
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={value => {
                        addInspectObject(value, record);
                        handleChangeValueColor(_fieldName, value, valueName, record, _dataType)
                      }
                      }
                    >
                      {_valueLists.map(item => (
                        <Option value={item} key={item}>
                          {item}
                        </Option>
                      ))}
                    </Select>
                  </Item>
                );
              }
              if (_dataType === 'DATE') {
                return (
                  <Item name={valueName}>
                    <DateTimePicker
                      record={record}
                      name={valueName}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      className="c7n-pro-calendar-picker-required c7n-pro-calendar-picker-required-colors"
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={(value) => {
                        addInspectObject(value, record);
                        handleComputedQty(_fieldName, _dataType)
                      }}
                    />
                  </Item>
                );
              }
              if (_dataType === 'CALCULATE_FORMULA') {
                const _formulaValue = record?.get(valueName);
                return (
                  (_formulaValue || _formulaValue === 0) && (
                    <Item name={valueName}>
                      <TextField
                        record={record}
                        name={valueName}
                        onEnterDown={() => {
                          if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                            return;
                          }
                          // 新增字段
                          // 获取之前的输入框是否为必输
                          const required = record?.getField(valueName)?.required;
                          const type = record?.getField(valueName)?.type;
                          record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                            name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            type,
                            required,
                          });
                          record.set('addQtyCount', _addQtyCount + 1);
                          handleChangeInspectTextWidth();

                          // 聚焦
                          setTimeout(() => {
                            const elementInput = document.getElementsByName(
                              `${_fieldName}_VALUE${_dataQtyValue.length}`,
                            );
                            if (elementInput.length > 0) {
                              if (currentIndex < elementInput.length) {
                                elementInput[currentIndex].focus();
                              } else {
                                elementInput[0].focus();
                              }
                            }
                          }, 100);
                        }}
                        onChange={(value)=>{addInspectObject(value, record);}}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? `${
                              styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                            } c7n-pro-input-required c7n-pro-input-required-colors `
                            : 'c7n-pro-input-required c7n-pro-input-required-colors '
                        }
                        disabled
                      />
                    </Item>
                  )
                );
              }
              return '';
            })}
          </ItemGroup>
        );
      },
    },
    {
      name: 'remark',
      editor: inspectInfoDS.getState('canEdit'),
      // editor: record => {
      //   // 存在检验对象时需先扫描检验对象才可录入
      //   const _resultDimension = inspectInfoDS.current?.get('resultDimension');
      //   const _dataType = inspectItemRowDS.current?.get('dataType');
      //   const _objectDisabled =
      //     _dataType !== 'CALCULATE_FORMULA' &&
      //     _resultDimension === 'RECORD_SAMPLE_VALUE' &&
      //     !record.get('inspectObjectId');
      //   return inspectInfoDS.getState('canEdit') && !_objectDisabled;
      // },
    },
  ];

  const addInspectObject = (value, record) => {
    const _sourceObjectObj = inspectLovDS.current?.get('sourceObjectObj');
    if (_sourceObjectObj?.inspectObjectId) {
      if (value) {
        const _emptyRecords = inspectItemRowObjValueDS.records.filter(
          item =>
            item.get('sourceObjectCode') === _sourceObjectObj.sourceObjectCode &&
            record.get('sourceObjectCode') !== _sourceObjectObj.sourceObjectCode,
        );
        if (_emptyRecords?.length === 0 || !_emptyRecords) {
          record.set('inspectObjectId', _sourceObjectObj.inspectObjectId);
          record.init('sourceObjectCode', _sourceObjectObj.sourceObjectCode);
        } else {
          record.set('inspectObjectId', null);
          record.init('sourceObjectCode', null);
        }
      }
    }
  };

  return (
    <>
      <Row>
        <Col span={16} />
        <Col span={8}>
          <Form
            dataSet={inspectLovDS}
            columns={1}
            labelWidth={90}
            labelLayout={LabelLayout.horizontal}
            disabled={!inspectInfoDS.getState('canEdit')}
          >
            <Lov name="sourceObjectObj" />
          </Form>
        </Col>
      </Row>
      <Row>
        <Col span={inspectInfoDS.current?.get('resultDimension') === "RECORD_SAMPLE_VALUE" ? 8: 16}>
          <div>
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.BLINE`,
              },
              <Table
                dataSet={inspectItemRowDS}
                columns={columns}
                highLightRow
                rowHeight={30}
                style={{ width: '98%', height: 600 }}
                onRow={() => {
                  return {
                    onClick: () => handleChangeCurrentLine(),
                  };
                }}
                customizedCode="jyptbl"
                virtual
                virtualCell
              />,
            )}
          </div>
        </Col>
        <Col span={inspectInfoDS.current?.get('resultDimension') === "RECORD_SAMPLE_VALUE" ? 16: 8}>
          {customizeTable(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.VALUE`,
            },
            <Table
              dataSet={inspectItemRowObjValueDS}
              columns={objColumns}
              rowHeight={30}
              customizedCode="jyptv"
              virtual
              virtualCell
              style={{ height: 600 }}
            />,
          )}
        </Col>
      </Row>
    </>
  );
};

export default InspectItemRowObjComponent;
