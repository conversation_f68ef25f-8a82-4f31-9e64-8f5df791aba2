import React, { useMemo, useRef } from 'react';
import { Table, DataSet, Form, Lov, NumberField, TextField, Button } from 'choerodon-ui/pro';
import { Collapse, Spin, Popconfirm } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { observer } from 'mobx-react';
import { useDataSetEvent } from 'utils/hooks';
import myInstance from '@utils/myAxios';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { useRequest } from '@/components/tarzan-hooks';
import {ColumnLock} from "choerodon-ui/pro/lib/table/enum";
import { detailTableDS, detailScanFormDS } from '../stores';
import { HandleLoad, GetDetailBarcodeInfo, HandleUnLoad } from '../services';

const modelPrompt = 'tarzan.hmes.containerLoadRegisterForB';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;
let printerModal;
// const { Option } = Select;

const PopConfirmButton = observer(
  ({
    dataSet,
    disabled = false,
    confirmCallback,
    confirmMessage,
    icon,
    funcType,
    color,
    buttonText,
  }) => (
    <Popconfirm
      title={confirmMessage}
      onConfirm={() => confirmCallback(dataSet)}
      okText={intl.get('tarzan.common.button.confirm').d('确认')}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
    >
      <Button
        disabled={!dataSet.selected?.length || disabled}
        icon={icon}
        funcType={funcType}
        color={color}
      >
        {buttonText}
      </Button>
    </Popconfirm>
  ),
);

const ContainerLoadRegisterForBDetail = props => {
  const {
    scanFormDs,
    tableDs,
    location: { state },
  } = props;

  const barcodeRef = useRef<any>(null);

  const { run: getDetailBarcodeInfo, loading: getGetDetailBarcodeInfoLoading } = useRequest(
    GetDetailBarcodeInfo(),
    {
      manual: true,
    },
  );
  const { run: handleLoad, loading: getHandleLoadLoading } = useRequest(HandleLoad(), {
    manual: true,
  });
  const { run: handleUnLoad, loading: getHandleUnLoadLoading } = useRequest(HandleUnLoad(), {
    manual: true,
  });

  useDataSetEvent(scanFormDs, 'update', ({ name, record }) => {
    if (name === 'containerTypeObj') {
      record.init('containerCode', null);
      record.init('attribute', null);
      record.init('barcode', null);
      tableDs.loadData([]);
    }
  });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        title: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
        width: 120,
        lock: ColumnLock.left,
        renderer: ({ record, dataSet }) => {
          if (dataSet && record) {
            return dataSet.totalCount - record?.index;
          }
        },
      },
      {
        name: 'materialCode',
      },
      {
        name: 'containerCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'modelCode',
      },
      {
        name: 'qty',
      },
    ];
  }, []);

  const handleQueryBarcode = () => {
    const _barcode = scanFormDs.current?.get('barcode')?.trim();

    getDetailBarcodeInfo({
      params: {
        ...scanFormDs.current.toData(),
        scanContainerCode: _barcode,
        subContainerList: tableDs.data.map(item => item.toData()),
      },
      onSuccess: res => {
        scanFormDs.current.set('qty', res?.qty);
        scanFormDs.current.set('topContainerId', res?.topContainerId);
        scanFormDs.current.set('topContainerCode', res?.topContainerCode);
        tableDs.loadData(res.subContainerList || []);
      },
    });
    barcodeRef?.current?.focus();
    barcodeRef?.current?.select();
  };

  const handleDelete = () => {
    const flag = tableDs.selected.every(item => !item.get('topContainerId'));
    if(!flag){
      notification.error({
        message: `存在容器已绑定托盘，无法删除！`,
      })
      return;
    }
    const qty = scanFormDs.current.get('qty');
    const selectQty = tableDs.selected.reduce((prev, record) => {
      const _value = prev + record.get('qty');
      return isNaN(_value) ? 0 : _value;
    }, 0);
    scanFormDs.current.set('qty', Number(qty) - selectQty);
    tableDs.selected.forEach(record => tableDs.remove(record));
  };

  // 装托按钮
  const handleMounting = () => {
    const _barcode = scanFormDs.current?.get('barcode')?.trim();
    handleLoad({
      params: {
        ...scanFormDs.current.toData(),
        scanContainerCode: _barcode,
        subContainerList: tableDs.data.map(item => item.toData()),
        locatorCode: state?.locatorCode,
        printer: state?.printer,
      },
      onSuccess: res => {
        notification.success({});
        scanFormDs.current.set('qty', res?.qty);
        scanFormDs.current.set('topContainerId', res?.topContainerId);
        scanFormDs.current.set('topContainerCode', res?.topContainerCode);
        // tableDs.loadData(res.subContainerList || []);
        tableDs.loadData([]);
      },
    });
  };

  // 卸托按钮
  const handleUnloading = () => {
    if (!tableDs.selected.length) {
      notification.warning({ message: '请先勾选数据！' });
      return;
    }
    handleUnLoad({
      params: tableDs.selected.map(item => item.toData()),
      onSuccess: res => {
        notification.success({});
        scanFormDs.current.set('qty', res?.qty);
        scanFormDs.current.set('topContainerId', res?.topContainerId);
        scanFormDs.current.set('topContainerCode', res?.topContainerCode);
        tableDs.loadData(res.subContainerList || []);
      },
    });
  };

  // const selectPrinter = (printerList) => {
  //   printerModal = Modal.open({
  //     key: Modal.key(),
  //     title: intl.get(`${modelPrompt}.info.printer`).d('请选择打印机'),
  //     destroyOnClose: true,
  //     closable: true,
  //     style: {
  //       width: 400,
  //     },
  //     children: (
  //       <React.Fragment>
  //         <Form>
  //           <Select
  //             clearButton={false}
  //             onChange={(value) => handlePrint(value)}
  //             placeholder={intl.get(`${modelPrompt}.info.printer`).d('请选择打印机')}
  //           >
  //             {printerList.map(i => (
  //               <Option key={i.value} value={i}>
  //                 {i.meaning}
  //               </Option>
  //             ))}
  //           </Select>
  //         </Form>
  //
  //       </React.Fragment>
  //     ),
  //     footer: null,
  //   });
  // };

  // 打印
  const handlePrint = () => {
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-to-b/box/barcode-print`;
    myInstance
      .post(url, {
        containerIdList: [scanFormDs.current?.get('topContainerId')],
        printer: {
          value: state?.printer || null,
        },
      })
      .then(res => {
        if (res.data.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.success.print`).d('打印成功!'),
          });
          tableDs.query();
          scanFormDs.current.set('qty', null);
          scanFormDs.current.set('topContainerId', null);
          scanFormDs.current.set('topContainerCode', null);
          tableDs.loadData([]);
          printerModal?.close();
        } else {
          // if (res.data.statusCode === "PRINTER_CHOOSE") {
          //   return selectPrinter(res.data.attr);
          // }
          printerModal?.close();
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.error.print`).d('打印失败!'),
          });
        }
      });
  };

  // const handleClearBoxCode = () => {
  //   scanFormDs.current.set('qty', null);
  //   scanFormDs.current.set('topContainerId', null);
  //   scanFormDs.current.set('topContainerCode', null);
  //   tableDs.loadData([]);
  // };


  return (
    <div className="hmes-style">
      <Spin
        spinning={getHandleLoadLoading || getGetDetailBarcodeInfoLoading || getHandleUnLoadLoading}
      >
        <Header
          title={intl.get(`${modelPrompt}.title.list`).d('装托卸托')}
          backPath="/hmes/container-load-register-for-b/list"
        >
          <Button color={ButtonColor.primary} onClick={handleUnloading} disabled={!tableDs.length}>
            {intl.get(`${modelPrompt}.button.unbind`).d('卸托')}
          </Button>
          <Button color={ButtonColor.primary} onClick={handleMounting} disabled={!tableDs.length}>
            {intl.get(`${modelPrompt}.button.packing`).d('装托')}
          </Button>
          <Button
            color={ButtonColor.primary}
            onClick={handlePrint}
            disabled={!scanFormDs.current?.get('topContainerCode')}
          >
            {intl.get(`${modelPrompt}.button.packing`).d('打印')}
          </Button>
          {/* <Button color={ButtonColor.primary} onClick={handleClearBoxCode} disabled={!scanFormDs.current?.get('topContainerCode')}>
            {intl.get(`${modelPrompt}.button.clear`).d('清除')}
          </Button> */}
        </Header>
        <Content>
          <Form dataSet={scanFormDs} columns={4}>
            <Lov name="containerTypeObj" />
          </Form>
          <Collapse bordered={false} defaultActiveKey={['barcodeInfo']}>
            <Panel
              key="barcodeInfo"
              header={intl.get(`${modelPrompt}.panel.barcodeInfo`).d('装载条码信息')}
            >
              <Form dataSet={scanFormDs} columns={3}>
                <TextField ref={barcodeRef} name="barcode" onEnterDown={handleQueryBarcode} />
                <TextField name="topContainerCode" newLine disabled />
                <NumberField name="qty" disabled />
              </Form>
              <Table
                buttons={[
                  <PopConfirmButton
                    dataSet={tableDs}
                    confirmCallback={handleDelete}
                    confirmMessage={intl
                      .get(`tarzan.common.message.confirm.delete`)
                      .d('是否确认删除?')}
                    icon="delete"
                    funcType={FuncType.flat}
                    color={ButtonColor.red}
                    buttonText={intl.get('tarzan.common.button.delete').d('删除')}
                    disabled={false}
                  />,
                ]}
                dataSet={tableDs}
                columns={columns}
                searchCode="containerLoadRegisterForBSearchTwo"
                customizedCode="containerLoadRegisterForBCustTwo"
                virtual
                style={{ height: 500 }}
              />
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const scanFormDs = new DataSet({ ...detailScanFormDS() });
      const tableDs = new DataSet({
        ...detailTableDS(),
      });
      return {
        scanFormDs,
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ContainerLoadRegisterForBDetail),
);
