import React, { FC, useMemo } from 'react';
import {
  Form,
  DataSet,
  Lov,
  TextField,
  Select,
  DateTimePicker,
  Output,
  NumberField,
  Switch,
  TimePicker,
  IntlField,
} from 'choerodon-ui/pro';
import { yesOrNoRender } from 'utils/renderer';
import CommonComponent from 'alm/components/CommonComponent';
import { Icon, Tooltip } from 'choerodon-ui';
import { Tooltip as _Tooltip } from 'choerodon-ui/pro/lib/core/enum';

import getLang from '../Langs';

interface formProps {
  detailDs: DataSet;
  editFlag: boolean;
  detail: objProps;
}
interface objProps {
  [key: string]: any;
}

const BasicForm: FC<formProps> = props => {
  const { detailDs, editFlag, detail } = props;
  const { cycleType, meterUom, planTimeRule } = detail;

  const planSchedBaseFilter = record => {
    return cycleType === 'TIME_CYCLE'
      ? ['STANDARD_HOUR', 'LAST_WORK_TIME'].includes(record.get('value'))
      : ['STANDARD_VALUE', 'LAST_WORK_VALUE'].includes(record.get('value'));
  };

  const handleStandardValueChange = value => {
    // @ts-ignore
    const { cycleInterval, planSchedBase } = detailDs.current.toData();
    if (planSchedBase === 'STANDARD_VALUE') {
      handleSetNextValue(cycleInterval, Number(value));
    }
  };

  const handleSetNextValue = (cycleInterval, value) => {
    if (detailDs.current) {
      if ((value || value === 0) && (cycleInterval || cycleInterval === 0)) {
        detailDs.current.set('nextWorkValue', cycleInterval + value);
      } else {
        detailDs.current.init('nextWorkValue');
      }
    }
  };

  const handleLastWorkValueChange = value => {
    // @ts-ignore
    const { cycleInterval, planSchedBase } = detailDs.current.toData();
    if (planSchedBase === 'LAST_WORK_VALUE') {
      handleSetNextValue(cycleInterval, Number(value));
    }
  };

  const handleCycleIntervalChange = value => {
    if (detailDs.current) {
      const { planSchedBase, standardValue, lastWorkValue } = detailDs.current.toData();
      if (value || value === 0) {
        if (planSchedBase === 'STANDARD_VALUE' && (standardValue || standardValue === 0)) {
          detailDs.current.set('nextWorkValue', Number(standardValue) + value);
        } else if (planSchedBase === 'LAST_WORK_VALUE' && (lastWorkValue || lastWorkValue === 0)) {
          detailDs.current.set('nextWorkValue', Number(lastWorkValue) + value);
        }
      } else {
        detailDs.current.init('nextWorkValue');
      }
    }
  };

  const handlePlanTimeRuleChange = () => {
    if (detailDs.current) {
      // 值有变化 就清空延迟时间及单位、 计划开始时间的值
      detailDs.current.set('delayTime', null);
      detailDs.current.set('planStartTime', null);
    }
  };

  const handlePlanSchedBase = value => {
    if (cycleType === 'METER_CYCLETRIGGE' && detailDs.current) {
      // 清空值则清空预计下次读数 更改值就重新计算预计下次读数
      if (value) {
        const { cycleInterval, lastWorkValue, standardValue } = detailDs.current.toData();
        if (
          value === 'LAST_WORK_VALUE' &&
          (cycleInterval || cycleInterval === 0) &&
          (lastWorkValue || lastWorkValue === 0)
        ) {
          detailDs.current.set('nextWorkValue', cycleInterval + Number(lastWorkValue));
        } else if (
          value === 'STANDARD_VALUE' &&
          (cycleInterval || cycleInterval === 0) &&
          (standardValue || standardValue === 0)
        ) {
          detailDs.current.set('nextWorkValue', cycleInterval + Number(standardValue));
        }
      } else {
        detailDs.current.set('nextWorkValue', null);
      }
    }
  };

  const delayTimeUomMeaning = useMemo(() => {
    // 延迟时间的单位
    if (planTimeRule === 'TRIGGER_TIME_DELAY_HOURS') {
      return getLang('HOUR');
    } if (planTimeRule === 'OTHER_TIME') {
      return getLang('DAY');
    }
    return null;
  }, [planTimeRule]);

  return (
    <>
      {editFlag ? (
        <Form dataSet={detailDs} columns={3} labelWidth={130}>
          <IntlField name="maintainPlanName" colSpan={2} />
          <TextField name="maintainPlanCode" disabled />
          <Select name="cycleType" />
          {cycleType === 'METER_CYCLETRIGGE' && <Lov name="meterLov" />}
          {cycleType === 'TIME_CYCLE' && (
            <NumberField
              name="cycleInterval"
              style={{ width: '100%', paddingRight: 8 }}
              addonAfterStyle={{
                border: 'none',
                padding: 0,
                width: 80,
              }}
              addonAfter={<Select name="cycleUom" />}
            />
          )}
          <Lov name="woTypeLov" />
          <Select
            name="planSchedBase"
            label={
              <Tooltip
                title={
                  cycleType === 'TIME_CYCLE'
                    ? getLang('PLAN_SCHED_BASE_TIME')
                    : getLang('PLAN_SCHED_BASE_VALUE')
                }
              >
                {getLang('PLAN_SCHED_BASE')}
                <Icon type="contact_support-o" style={{ fontSize: '16px' }} />
              </Tooltip>
            }
            optionsFilter={planSchedBaseFilter}
            onChange={handlePlanSchedBase}
          />
          {cycleType === 'METER_CYCLETRIGGE' && (
            <NumberField
              name="lastWorkValue"
              suffix={meterUom}
              onChange={handleLastWorkValueChange}
            />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && (
            <NumberField
              name="standardValue"
              suffix={meterUom}
              onChange={handleStandardValueChange}
            />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && (
            <NumberField
              name="cycleInterval"
              suffix={meterUom}
              onChange={handleCycleIntervalChange}
            />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && (
            <NumberField name="nextWorkValue" suffix={meterUom} />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && (
            <NumberField name="lastReadingValue" suffix={meterUom} />
          )}
          <DateTimePicker name="lastWorkTime" />
          {cycleType === 'TIME_CYCLE' && <DateTimePicker name="cycleStandardTime" />}
          <NumberField
            name="durationScheduled"
            style={{ width: '100%', paddingRight: 8 }}
            addonAfterStyle={{
              border: 'none',
              padding: 0,
              width: 80,
            }}
            addonAfter={<Select name="durationUom" />}
          />
          <Switch name="defaultJobFlag" />
          {cycleType === 'METER_CYCLETRIGGE' && (
            <Select name="planTimeRule" onChange={handlePlanTimeRuleChange} />
          )}
          {cycleType === 'METER_CYCLETRIGGE' &&
            ['OTHER_TIME', 'TRIGGER_TIME_DELAY_HOURS'].includes(planTimeRule) && (
            <NumberField name="delayTime" suffix={delayTimeUomMeaning} />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && ['OTHER_TIME'].includes(planTimeRule) && (
            <TimePicker name="planStartTime" />
          )}
          <Lov name="plannerGroupLov" newLine/>
          {/* <CommonComponent name="plannerGroupName" isEdit dataSet={detailDs} newLine /> */}
          <CommonComponent name="plannerName" isEdit dataSet={detailDs} />
          <Lov name="ownerGroupLov" newLine/>
          {/* <CommonComponent name="ownerGroupName" isEdit dataSet={detailDs} newLine /> */}
          <CommonComponent name="ownerName" isEdit dataSet={detailDs} />
          <DateTimePicker name="startDate" newLine />
          <DateTimePicker name="endDate" />
        </Form>
      ) : (
        <Form dataSet={detailDs} columns={3} labelWidth={130}>
          <Output name="maintainPlanName" colSpan={2} />
          <Output name="maintainPlanCode" />
          <Output name="cycleType" />
          {cycleType === 'METER_CYCLETRIGGE' && <Output name="meterName" />}
          {/* 因为Output没有必输标识 div会有 所以这里换用Output */}
          {cycleType === 'TIME_CYCLE' && (
            <Output
              name="cycleInterval"
              tooltip={_Tooltip.none}
              renderer={({ record, value }) => {
                return `${value} ${record?.get('cycleUomMeaning')}`;
              }}
            />
          )}
          <Output name="woTypeName" />
          <Output
            name="planSchedBase"
            label={
              <Tooltip
                title={
                  cycleType === 'TIME_CYCLE'
                    ? getLang('PLAN_SCHED_BASE_TIME')
                    : getLang('PLAN_SCHED_BASE_VALUE')
                }
              >
                {getLang('PLAN_SCHED_BASE')}
                <Icon type="contact_support-o" style={{ fontSize: '16px' }} />
              </Tooltip>
            }
          />
          {cycleType === 'METER_CYCLETRIGGE' && (
            <Output name="lastWorkValue" renderer={({ value }) => `${value} ${meterUom}`} />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && (
            <Output name="standardValue" renderer={({ value }) => `${value} ${meterUom}`} />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && (
            <Output name="cycleInterval" renderer={({ value }) => `${value} ${meterUom}`} />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && (
            <Output name="nextWorkValue" renderer={({ value }) => `${value} ${meterUom}`} />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && (
            <Output name="lastReadingValue" renderer={({ value }) => `${value} ${meterUom}`} />
          )}
          <Output name="lastWorkTime" />
          {cycleType === 'TIME_CYCLE' && <Output name="cycleStandardTime" />}
          <Output
            name="durationScheduled"
            tooltip={_Tooltip.none}
            renderer={({ record, value }) => {
              return `${value} ${record?.get('durationUomMeaning')}`;
            }}
          />
          <Output name="defaultJobFlag" renderer={({ text }) => yesOrNoRender(Number(text))} />
          {cycleType === 'METER_CYCLETRIGGE' && <Output name="planTimeRule" />}
          {cycleType === 'METER_CYCLETRIGGE' &&
            ['OTHER_TIME', 'TRIGGER_TIME_DELAY_HOURS'].includes(planTimeRule) && (
            <Output
              name="delayTime"
              renderer={({ value }) => `${value} ${delayTimeUomMeaning}`}
            />
          )}
          {cycleType === 'METER_CYCLETRIGGE' && ['OTHER_TIME'].includes(planTimeRule) && (
            <Output name="planStartTime" />
          )}
          <Output name="plannerGroupName" newLine />
          <Output name="plannerName" />
          <Output name="ownerGroupName" newLine />
          <Output name="ownerName" />
          <Output name="startDate" newLine />
          <Output name="endDate" />
        </Form>
      )}
    </>
  );
};

export default BasicForm;
