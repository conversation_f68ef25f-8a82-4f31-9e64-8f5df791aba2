/*
 * @Description: MSA分析-外观对标分析
 * @Author: <<EMAIL>>
 * @Date: 2024-01-30 18:56:24
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-01 11:29:29
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, Form, TextField, Lov, DataSet, Table, TextArea } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/es/table/Column';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { downloadFile } from '@services/api';
import { BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST } from '@/utils/constants';
import ExcelUpload, { ExcelUploadProps } from './components/ExcelUpload';
import { formDS } from '../stores/appearanceFormDS';
import { analyseResultDS } from '../stores/DetailDS';
import { QueryMsaTypeData, SaveMsaTypeData } from '../services';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

const Appearance = props => {
  const { msaStatus, currentUserFlag, msaTaskLineId } = props;
  const formDs = useMemo(() => new DataSet(formDS()), []);
  const tableDs = useMemo(
    () =>
      new DataSet({
        forceValidate: true,
        autoCreate: false,
        selection: false,
        paging: false,
        fields: [],
      }),
    [],
  );
  // 分析结果DS
  const analyseResultDs = useMemo(() => new DataSet(analyseResultDS()), []);
  // 处理后的列数据
  const [columnMap, setColumnMap] = useState<any>([]);
  // 处理后的表格数据
  const [tableData, setTableData] = useState<any>([]);
  // 当前数据是否有保存过，是否展示分析结果信息
  const [showAnalyseResult, setShowAnalyseResult] = useState<boolean>(false);
  // 表格对应的columns
  const [dynamicColumn, setDynamicColumn] = useState<ColumnProps[]>([]);
  // 查询任务数据
  const { run: queryMsaTypeData, loading: queryDataLoading } = useRequest(QueryMsaTypeData(), { manual: true, needPromise: true });
  // 保存任务数据
  const { run: saveMsaTypeData, loading: saveDataLoading } = useRequest(SaveMsaTypeData(), { manual: true });

  useEffect(() => {
    handleInitData();
  }, []);

  const handleInitData = async () => {
    const res = await queryMsaTypeData({ params: { msaTaskLineId }});
    if (res?.success) {
      handleFormatAsyncData(res?.rows);
    }
  };

  // 处理接口返回的数据，得到列的数据和DS的数据
  const handleFormatAsyncData = (dataSource, saveFlag = true) => {
    const {
      qisAppearanceInfo = {},
      msaResult = '',
      msaConclusion = '',
      tableInfo = [],
      measuredBy,
      measuredByName,
      sampleDescription,
    } = dataSource;
    // 列数据
    const _columnMap: any = [{
      name: `column_left`,
      measureDataColumn: 0,
      label: intl.get(`${modelPrompt}.column.sequence`).d('序号'),
    }];
    // 缺陷种类行数据
    const _defectTypeData: any = {
      type: 'defectType',
      column_left: intl.get(`${modelPrompt}.column.defectType`).d('缺陷种类'),
    };
    // 缺陷数量行数据
    const _defectQtyData: any = {
      type: 'defectQty',
      column_left: intl.get(`${modelPrompt}.column.defectQty`).d('缺陷数量'),
    };
    // 识别数量行数据
    const _identifyQtyData: any = {
      type: 'identifyQty',
      column_left: intl.get(`${modelPrompt}.column.identifyQty`).d('识别数量'),
    };
    // 缺陷识别率行数据
    const _defectRateData: any = {
      type: 'defectRate',
      column_left: intl.get(`${modelPrompt}.column.defectRate`).d('缺陷识别率'),
    };
    if (tableInfo?.length) {
      // 表里有数据
      setShowAnalyseResult(true);
      (tableInfo || []).forEach((columnItem) => {
        const { measureDataColumn, measureTableList } = columnItem;
        _columnMap.push({
          name: `column_${measureDataColumn}`,
          measureDataColumn,
          label: measureDataColumn,
          // 当前列是否已保存，如果已保存，不允许删除
          saveFlag,
        });
        (measureTableList || []).forEach((rowItem) => {
          const { measureDataRow, measureDataValue, measureValue } = rowItem;
          if (measureDataRow === 1) {
            _defectTypeData[`column_${measureDataColumn}`] = measureValue;
          } else if (measureDataRow === 2) {
            _defectQtyData[`column_${measureDataColumn}`] = measureDataValue;
          } else if (measureDataRow === 3)  {
            _identifyQtyData[`column_${measureDataColumn}`] = measureDataValue;
          }
        })
      });
      (qisAppearanceInfo?.defectRecognitionRateInfos || []).forEach((defectRateItem) => {
        const { measureDataColumn, defectRecognitionRate } = defectRateItem;
        _defectRateData[`column_${measureDataColumn}`] = defectRecognitionRate;
      });
    } else {
      // 表里没有数据，默认创建一条
      _columnMap.push({
        name: `column_1`,
        measureDataColumn: 1,
        label: 1,
        // 当前列是否已保存，如果已保存，不允许删除
        saveFlag,
      });
    }
    const _tableData = [_defectTypeData, _defectQtyData, _identifyQtyData, _defectRateData];

    // 当导入的列数小于现有列数时，需要将多的的列置为非必输
    const deleteColumn = tableDs.getState('deleteColumn') || [];
    if (columnMap?.length > _columnMap?.length) {
      for (let i = _columnMap.lastIndex + 1; i < columnMap?.length; i++) {
        if (!(deleteColumn || []).includes(columnMap[i].measureDataColumn)) {
          deleteColumn.push(columnMap[i].measureDataColumn);
        }
      }
    }
    tableDs.setState('deleteColumn', deleteColumn);

    setColumnMap(_columnMap);
    setTableData(_tableData);
    if (saveFlag) {
      formDs.loadData([{
        sampleDescription,
        measuredBy,
        measuredByName,
      }]);
    }
    analyseResultDs.loadData([{
      msaResult,
      msaConclusion,
    }]);
  };

  useEffect(() => {
    handleInitDsAndColumn({columnMap, tableData})
  }, [columnMap, tableData]);

  // 组装DS和column数据
  const handleInitDsAndColumn = ({ columnMap, tableData }) => {
    // 添加序号列Field
    if (!tableDs?.getField('column_left')) {
      tableDs.addField('column_left', {
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.column.sequence`).d('序号'),
      })
    }
    // 动态列数据
    const _dynamicColumn: ColumnProps[] = [{
      name: 'column_left',
      lock: ColumnLock.left,
      align: ColumnAlign.center,
      header: () => (
        <>
          <span>{intl.get(`${modelPrompt}.column.sequence`).d('序号')}</span>
          <PermissionButton
            type="c7n-pro"
            icon="add"
            funcType="flat"
            shape="circle"
            size="small"
            disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
            onClick={() => handleAddColumn()}
          />
        </>
      ),
    }];
    (columnMap || []).forEach((columnItem) => {
      const { name, label, measureDataColumn, saveFlag } = columnItem;
      if (name === 'column_left') {
        return;
      }

      if (!tableDs?.getField(columnItem.name)) {
        // 添加DS的Field
        tableDs.addField(name, {
          label,
          dynamicProps: {
            type: ({ record }) => record?.get('type') === 'defectType' || record?.get('type') === 'defectRate' ? FieldType.string : FieldType.number,
            required: ({ record, dataSet }) => {
              return record?.get('type') !== 'defectRate' && !(dataSet.getState('deleteColumn') || []).includes(measureDataColumn);
            },
            precision: ({ record }) => record?.get('type') !== 'defectRate' ? 0 : 100,
          },
        })
      } else if (!tableDs?.getField(columnItem.name)?.get('required')) {
        // 检查Field的必输性
        const _deleteColumn = tableDs.getState('deleteColumn') || [];
        const deleteIndex = _deleteColumn.findIndex((item) => item === measureDataColumn);
        _deleteColumn.splice(deleteIndex, 1);
        tableDs.setState('deleteColumn', _deleteColumn);
      }

      // 添加column
      _dynamicColumn.push({
        name,
        align: ColumnAlign.center,
        // @ts-ignore
        header: ({ title }) => {
          return (
            <>
              <span>{title}</span>
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => handleDeleteColumn(columnItem)}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <PermissionButton
                  type="c7n-pro"
                  icon="remove"
                  disabled={saveFlag || measureDataColumn === 1 || ['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </Popconfirm>
            </>
          )
        },
        editor: record => {
          if (['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag) {
            return false;
          }
          return record.get('type') !== 'defectRate';
        },
      })
    })
    tableDs.loadData(tableData);
    setDynamicColumn(_dynamicColumn);
  }

  const getColumnSequence = (columnItem) => {
    // 使用正则表达式匹配数字部分
    const matches = columnItem.name.match(/\d+/);
    return Number(matches[0]);
  }

  // 添加序号
  const handleAddColumn = () => {
    // 当前最大的序号
    const maxSequence = getColumnSequence(columnMap[columnMap?.length - 1]);
    // 当前的列数据
    const _columnMap = [ ...columnMap ];
    const columnName = `column_${maxSequence + 1}`
    _columnMap.push({
      name: columnName,
      measureDataColumn: maxSequence + 1,
      label: maxSequence + 1,
      // 当前列是否已保存，如果已保存，不允许删除
      saveFlag: false,
    })
    setColumnMap(_columnMap);
    setTableData(tableDs.toData());
  }

  // 删除序号
  const handleDeleteColumn = (columnItem) => {
    const { name, measureDataColumn } = columnItem;
    // 更新columnMap
    const _columnMap = [ ...columnMap ];
    const deleteIndex = _columnMap.findIndex((item) => item.name === name);
    _columnMap.splice(deleteIndex, 1);
    setColumnMap(_columnMap);
    setTableData(tableDs.toData());

    // 更新必输性
    const _deleteColumn = tableDs.getState('deleteColumn') || [];
    tableDs.setState('deleteColumn', [..._deleteColumn, measureDataColumn]);
  }

  // 模板下载
  const downloadTemp = async () => {
    await downloadFile({
      requestUrl: `/himp/v1/${tenantId}/template/QIS_MSA_IMPORT_APPEARANCE/excel`,
      queryParams: [{ name: 'tenantId', value: tenantId }],
      method: 'GET',
    });
  };

  const excelUploadProps: ExcelUploadProps = {
    url: `${API_HOST}${
      BASIC.TARZAN_SAMPLING
    }/v1/${tenantId}/qis-msa-analysis/import/ui?msaTaskLineId=${
      props.msaTaskLineId
    }&sampleDescription=${formDs.current?.get('sampleDescription') ||
      ''}&measuredBy=${formDs.current?.get('measuredBy') || ''}`,
    params: {},
    onSuccess: res => handleFormatAsyncData(
      { tableInfo: res.rows?.tableInfo || [] },
      false,
    ),
  };

  // 点击保存按钮的回调
  const handleSaveData = async () => {
    const tableValidateRes = await tableDs.validate();
    const formValidateRes = await formDs.validate();
    if (!tableValidateRes || !formValidateRes) {
      return;
    }
    saveMsaTypeData({
      params: handleFormatSaveData(),
      onSuccess: (res) => {
        // 处理数据
        handleFormatAsyncData(res);
        props.updateHeaderInfo(); // 更新头部
        notification.success({});
      },
    })
  };

  // 处理保存数据
  const handleFormatSaveData = () => {
    const formData = formDs.current?.toData();
    const tableData = tableDs.toData();
    const _newTableData: any = [];
    columnMap.forEach((columnItem, columnIndex) => {
      if (columnIndex === 0) {
        return;
      }
      const { name } = columnItem;
      const measureTableList: any = [];
      tableData.forEach((tableRowItem, tableRowIndex) => {
        if (tableRowIndex < 3) {
          measureTableList.push({
            measureDataRow: tableRowIndex + 1,
            measureDataColumn: columnIndex,
            measureValue: tableRowIndex === 0 ? tableRowItem[name]: undefined,
            measureDataValue: tableRowIndex === 0 ? undefined : tableRowItem[name],
          });
        }
      })
      _newTableData.push({
        measureDataColumn: columnIndex,
        measureTableList,
      });
    });
    return {
      msaTaskLineId: props.msaTaskLineId,
      ...formData,
      tableInfo: _newTableData,
    };
  }

  const RenderHelp = () => {
    const info = intl.get(`${modelPrompt}.apprarance.help`).d('1、存在识别率不为100%，则不合格；<br />2、识别率均为100%，则合格');
    const descriptionList = info.split('<br />');
    return (
      <div>
        {descriptionList.map(item => (
          <p>{item}</p>
        ))}
      </div>
    );
  };

  return (
    <div>
      <Button
        color={ButtonColor.primary}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
        loading={saveDataLoading || queryDataLoading}
        onClick={() => handleSaveData()}
      >
        {intl.get(`${modelPrompt}.button.save`).d('保存')}
      </Button>
      <Button onClick={downloadTemp} icon="get_app">
        {intl.get(`${modelPrompt}.templateDownload`).d('模板下载')}
      </Button>
      <ExcelUpload
        {...excelUploadProps}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      />
      <Form
        dataSet={formDs}
        columns={3}
        style={{ marginTop: 10 }}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        <TextField name="sampleDescription" />
        <Lov name="improveByObj" />
      </Form>
      <Table dataSet={tableDs} columns={dynamicColumn as any} />
      {showAnalyseResult && (
        <Form labelWidth={100} disabled dataSet={analyseResultDs}>
          <TextField name="msaResult" help={<RenderHelp />} />
          <TextArea name="msaConclusion" rows={7} />
        </Form>
      )}
    </div>
  );
};

export default Appearance;
