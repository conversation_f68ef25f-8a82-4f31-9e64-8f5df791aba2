/**
 * @Description: 新区域维护-详情页组件
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 13:19:18
 * @LastEditTime: 2021-07-12 10:45:03
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, IntlField } from 'choerodon-ui/pro';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.model.org.area';

const BasicInfoTab = props => {
  const { dataSet, canEdit, columns = 1, focus = true } = props;
  return (
    <Form
      disabled={!canEdit || focus}
      dataSet={dataSet}
      columns={columns}
      labelLayout="horizontal"
      labelWidth={112}
    >
      <IntlField
        name="country"
        newLine
        modalProps={{
          title: intl.get(`${modelPrompt}.country`).d('国家'),
        }}
      />
      <IntlField
        name="province"
        newLine
        modalProps={{
          title: intl.get(`${modelPrompt}.province`).d('省'),
        }}
      />
      <IntlField
        name="city"
        newLine
        modalProps={{
          title: intl.get(`${modelPrompt}.city`).d('城市'),
        }}
      />
      <IntlField
        name="county"
        newLine
        modalProps={{
          title: intl.get(`${modelPrompt}.county`).d('县'),
        }}
      />
      <IntlField
        name="address"
        newLine
        modalProps={{
          title: intl.get(`${modelPrompt}.address`).d('详细地址'),
        }}
        colSpan={columns >= 2 ? 2 : 1}
      />
    </Form>
  );
};

export default BasicInfoTab;
