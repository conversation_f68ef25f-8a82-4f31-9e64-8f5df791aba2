/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-10 15:39:34
 * @LastEditTime: 2023-07-10 15:46:09
 * @LastEditors: <<EMAIL>>
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 库存查询-查询库存日记账子节点数据
export function QueryCardList() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-job-platform-cards`,
    method: 'GET',
  };
}

// 卡片分类HME.CARD_TYPE
export function QueryCardType() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-job-platform-cards`,
    method: 'GET',
  };
}
