/**
 * 设备-多语言
 * <AUTHOR>
 * @date 2021-10-13
 * @version: 0.0.1
 * @copyright: Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLangs = key => {
  const PREFIX = 'alm.common';
  const MODELPREFIX = 'alm.common.model.assetLov';
  const LANGS = {
    ...getCommonLangs(),
    CHOOSE_ASSET: intl.get(`${PREFIX}.title.chooseAsset`).d('选择资产/设备'),
    // model
    ASSET_DESC: intl.get(`${MODELPREFIX}.assetDesc`).d('资产全称'),
    VISUAL_LABEL: intl.get(`${MODELPREFIX}.visualLabel`).d('资产标签'),
    ASSET_LOCATION: intl.get(`${MODELPREFIX}.assetLocation`).d('资产位置'),
    ONLY_ACT: intl.get(`${MODELPREFIX}.onlyAct`).d('仅展示与标准作业相关'),
  };
  return LANGS[key];
};

export default getLangs;
