import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.toolTypeMaintain';
const tenantId = getCurrentOrganizationId();
// const BASICTEST = '/tznq';量具

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  selection: false,
  queryFields: [
    {
      name: 'categoryCode',
      lookupCode: 'QIS.MS_TOOL_CTGR',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryCode`).d('类别描述'),
    },
    {
      name: 'speciesObj',
      lovCode: 'YP.QMS_YP_TYPE_TEST',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
      textField: 'speciesName',
    },
    {
      name: 'speciesCode',
      type: FieldType.string,
      bind: 'speciesObj.speciesCode',
    },
    {
      name: 'toolTypeId',
      bind: 'speciesObj.toolTypeId',
    },
    {
      name: 'verificationMethod',
      lookupCode: 'QIS.MS_VRFCT_METHOD',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
    },
    {
      name: 'enableFlag',
      lookupCode: 'MT.YES_NO',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'siteObj',
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      textField: 'siteName',
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObj.siteCode',
    },
  ],
  fields: [
    {
      name: 'siteObj',
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      textField: 'siteCode',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObj.siteCode',
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      bind: 'siteObj.siteName',
      required: true,
    },
    {
      name: 'categoryCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryCode`).d('类别编码'),
      lookupCode: 'QIS.MS_TOOL_CTGR',
      textField: 'value',
      required: true,
    },
    {
      name: 'categoryName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryName`).d('类别描述'),
      required: true,
    },
    {
      name: 'speciesCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.speciesCode`).d('种别编码'),
      required: true,
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
      required: true,
    },
    {
      name: 'verificationMethod',
      lookupCode: 'QIS.MS_VRFCT_METHOD',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
      required: true,
    },
    {
      name: 'verificationPeriod',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检定周期（月）'),
      required: true,
      min: 1,
      step: 1,
    },
    {
      name: 'reminderPeriod',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.reminderPeriod`).d('检定提醒周期（天）'),
      required: true,
      min: 1,
      step: 1,
    },
    {
      name: 'enableFlag',
      lookupCode: 'MT.YES_NO',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-types`,
      method: 'get',
    },
    submit:({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-types`,
        method: 'post',
        data,
      }
    },
  },
});

export {tableDS};
