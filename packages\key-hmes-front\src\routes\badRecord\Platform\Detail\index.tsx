import React, { useEffect, useMemo, useState, useRef } from 'react';
import {
  Button,
  DataSet,
  Form,
  Lov,
  Modal,
  Select,
  Table,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import { Collapse, Popconfirm, Tag } from 'choerodon-ui';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { useDataSetEvent } from 'utils/hooks';
import {
  // AttributeDrawer,
  C7nFormItemSort,
  Tarzan<PERSON><PERSON>,
  drawerPropsC7n,
} from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
// import uuid from 'uuid/v4';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import scanImg from '@/assets/icons/scan-o.svg';
import { ncRecordDS } from '../stores/detailRecordTypeDS';
import openBatchBarCodeModal from './BatchBarCodeModal';
import InputLovDS from '../stores/InputLovDS';
import { formDS, detailDS, ncDetailLineDS, scanFormDS } from '../stores/detailDS';
import { queryIdpValue } from '@/services/api';


import {
  LineDelete,
  FetchEoRelatedInfo,
  FetchMaterialLotRelatedInfo,
  SaveNcRecord,
  ScanMaterialLotRelatedInfo,
  ScanEoRelatedInfo,
  QueryRevision,
  FetchOperation,
  InitiateReview,
  QuickScrapping,
  ScrappingConfirm,
  ScrappingCancel,
  ScrappingCancelCheck,
  RecordCancel,
} from '../services';
import { fetchDefaultSite } from '../../../../services/api';

const { Option } = Select;
const { Panel } = Collapse;
const modelPrompt = 'tarzan.mes.event.badRecordPlatform';

const userInfo = getCurrentUser();

let containerCreateModal;

const BadRecordDetail = observer(props => {
  const {
    history,
    match: { params },
    // customizeForm,
    // customizeTable,
    // custConfig,
  } = props;
  const { id } = params as any;
  // 不良对象类型
  const [ncRecordType, setNcRecordType] = useState<string>('');
  // 版本
  const [revisionList, setRevisionList] = useState<any>([]);
  // 是否能编辑
  const [canEdit, setCanEdit] = useState<boolean>(id === 'create');
  // 折叠activeKey
  const [activeKey, setActiveKey] = useState<any>([
    'badRecordFormInfo',
    'badRecordLineInfo',
    'badRecordLineDetail',
  ]);
  const [status, setStatus] = useState(false);
  const materialLotsRef = useRef(null);
  const eosRef = useRef(null);

  const inputLovDS = new DataSet({ ...InputLovDS() });
  const formDs = new DataSet({ ...formDS() });
  // 不良记录明细Ds
  const ncDetailLineDs = useMemo(() => new DataSet(ncDetailLineDS()), []);
  const ncRecordDs = useMemo(() => new DataSet(ncRecordDS()), []); // 库存不良
  const scanFormDs = useMemo(() => new DataSet(scanFormDS()), []);
  const modalDs = useMemo(
    () =>
      new DataSet({
        autoQuery: false,
        autoCreate: true,
        forceValidate: true,
        fields: [
          {
            name: 'intendedDisposal',
            required: true,
            type: FieldType.string,
            lookupCode: 'MT.MES.NC_DISPOSAL_METHOD',
            label: intl.get(`${modelPrompt}.intendedDisposal`).d('处置方式'),
          },
          {
            name: 'operationLov',
            type: FieldType.object,
            label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
            lovCode: 'APEX_MES.OPERATION_NC_INCIDENT',
            ignore: FieldIgnore.always,
            dynamicProps: {
              lovCode: ({ record }) => {
                return record?.get('intendedDisposal') === 'REWORK_SOURCE' ? 'APEX_MES.OPERATION_NC_INCIDENT' : 'APEX_MES.OPERATION_ALL'
              },
              lovPara: ({ record }) => ({
                tenantId: getCurrentOrganizationId(),
                methodType: record.get('intendedDisposal'),
                workOrderId: ncRecordDs.current?.get('workOrderId'),
                eoIds: ncRecordDs.data.map(item => item?.get('eoId')),
              }),
              required: ({ record }) => record?.get('intendedDisposal') === 'CONCESSION_INTERCEPTION' || record?.get('intendedDisposal') === 'REWORK_SOURCE',
              disabled: ({ record }) => record?.get('intendedDisposal') !== 'CONCESSION_INTERCEPTION' && record?.get('intendedDisposal') !== 'REWORK_SOURCE',
            },
          },
          {
            name: 'operationId',
            bind: 'operationLov.operationId',
          },
          {
            name: 'operationName',
            bind: 'operationLov.operationName',
          },
          // {
          //   name: 'costCenterLov',
          //   type: FieldType.object,
          //   label: intl.get(`${modelPrompt}.costCenterLov`).d('成本中心'),
          //   lovCode: 'YP_WMS.MES.COST_CENTER',
          //   ignore: FieldIgnore.always,
          //   textField: 'description',
          //   required: true,
          // },
          // {
          //   name: 'costCenter',
          //   bind: 'costCenterLov.costcenterCode',
          // },
          // {
          //   name: 'description',
          //   bind: 'costCenterLov.description',
          // },
        ],
      }),
    [ncRecordDs],
  );

  // 详情界面Ds
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const { run: scanMaterialLotRelatedInfo, loading: scanMaterialLotLoading } = useRequest(
    ScanMaterialLotRelatedInfo(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: scanEoRelatedInfo, loading: scanEoLoading } = useRequest(ScanEoRelatedInfo(), {
    manual: true,
    needPromise: true,
  });
  // 获取物料批关联信息
  const { run: fetchMaterialLotRelatedInfo, loading: fetchMaterialLotLoading } = useRequest(
    FetchMaterialLotRelatedInfo(),
    {
      needPromise: true,
      manual: true,
    },
  );
  // 获取EO关联信息
  const { run: fetchEoRelatedInfo, loading: fetchEoLoading } = useRequest(FetchEoRelatedInfo(), {
    needPromise: true,
    manual: true,
  });
  // 保存
  const { run: saveNcRecord, loading: saveLoading } = useRequest(SaveNcRecord(), {
    manual: true,
  });

  // 记录行删除
  const { run: lineDelete, loading: lineDeleteLoading } = useRequest(LineDelete(), {
    manual: true,
    needPromise: true,
  });
  // 查物料版本
  const { run: queryRevision, loading: queryRevisionLoading } = useRequest(QueryRevision(), {
    manual: true,
    needPromise: true,
  });
  // 状态取消
  const { run: cancel, loading: cancelLoading } = useRequest(RecordCancel(), {
    manual: true,
    needPromise: true,
  });
  // 根据工位查询工艺和设备
  const { run: fetchOperation, loading: fetchOperationLoading } = useRequest(FetchOperation(), {
    manual: true,
    needPromise: true,
  });
  // 发起评审
  const { run: initiateReview, loading: initiateReviewLoading } = useRequest(InitiateReview(), {
    manual: true,
    needPromise: true,
  });
  // 快速报废
  const { run: quickScrapping, loading: quickScrappingLoading } = useRequest(QuickScrapping(), {
    manual: true,
    needPromise: true,
  });
  //  报废确认

  const { run: scrappingConfirm, loading: scrappingConfirmLoading } = useRequest(
    ScrappingConfirm(),
    {
      manual: true,
      needPromise: true,
    },
  );

  // 报废取消
  const { run: scrappingCancel, loading: scrappingCancelLoading } = useRequest(
    ScrappingCancel(),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 报废取消
  const { run: scrappingCancelCheck, loading: scrappingCancelCheckLoading } = useRequest(
    ScrappingCancelCheck(),
    {
      manual: true,
      needPromise: true,
    },
  );

  useEffect(() => {
    queryIdpValue("HME.ROLE_CONTROL_NC").then((res) => {
      if (res) {
        let currentAccord = false;
        (res || []).forEach((item) => {
          if (item.value === userInfo.currentRoleCode) {
            currentAccord = true;
          }
        })
        setStatus(currentAccord)
      } else {
        setStatus(false);
      }
    });
    if (id === 'create') {
      fetchDefaultSite().then(res => {
        if (res && res.success) {
          detailDs.current?.set('siteLov', res.rows);
          detailDs.current?.set('siteId', res.rows?.siteId);
          detailDs.current?.set('siteName', res.rows?.siteName);
          detailDs.current?.set('siteCode', res.rows?.siteCode);
        }
      });
      return;
    }
    handleQueryDetail(id);
  }, [id]);

  const handleQueryDetail = id => {
    // detailDs.setQueryParameter(
    //   'customizeUnitCode',
    //   `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.HEAD`,
    // );
    detailDs.setQueryParameter('ncIncidentId', id);
    detailDs.query().then(res => {
      const { ncRecordType, ncRecordList } = res || {};
      setNcRecordType(ncRecordType);
      ncRecordDs.setState('ncRecordType', ncRecordType);
      ncRecordDs.loadData(ncRecordList);
      scanFormDs.loadData([res]);
      ncRecordDs.current = ncRecordList[0];
      headerRowClick(ncRecordList[0]?.ncRecordId);
    });
  };
  const headerRowClick = async id => {
    ncDetailLineDs.setQueryParameter('ncRecordId', id);
    ncDetailLineDs.query();
  };

  // 监听不良记录明细表格勾选数据
  const handleUpdate = ({ name }) => {
    scanFormDs.loadData([{ ...detailDs.current?.toJSONData() }]);
    if (name === 'materialObj') {
      ncRecordDs.loadData([]);
      ncDetailLineDs.loadData([]);
    }
    if (name === 'ncRecordType') {
      ncRecordDs.loadData([]);
      ncDetailLineDs.loadData([]);
      // detailDs.current?.set('operationLov', undefined);
      detailDs.current?.set('operationId', undefined);
      detailDs.current?.set('operationName', undefined);
    }
    if (
      (['ncRecordType', 'siteLov', 'materialObj'].includes(name) &&
        detailDs.current?.get('ncRecordType') === 'EO_ALL_NC') ||
      (['ncRecordType', 'siteLov', 'materialObj'].includes(name) &&
        detailDs.current?.get('ncRecordType') === 'RM_NC')
    ) {
      scanFormDs.current?.set('materialLots', undefined);
      scanFormDs.current?.set('materialLotLov', undefined);
      scanFormDs.current?.set('eos', undefined);
      scanFormDs.current?.set('eoLov', undefined);
    }
    if (name === 'siteLov') {
      detailDs.current?.init('materialObj', undefined);
      detailDs.current?.init('revisionCode', undefined);
      ncRecordDs.loadData([]);
      ncDetailLineDs.loadData([]);
    }
  };
  useDataSetEvent(detailDs, 'update', handleUpdate);

  // 头保存
  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const params = {
      ...detailDs.toData()[0],
      ncRecordList: ncRecordDs.toData(),
    };
    saveNcRecord({
      params,
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        history.push(`/hmes/bad-record/platform/detail/${res}`);
        handleQueryDetail(res);
      },
    });
  };
  const handleChangeNcRecordType = (value, oldVal) => {
    if (oldVal && ncRecordDs.toData().length) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.clearData`)
              .d('不良记录信息/不良记录明细会清空，确定更换不良记录类型？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          setNcRecordType(value);
          ncRecordDs.setState('ncRecordType', ncRecordType);
          ncRecordDs.loadData([]);
          ncDetailLineDs.loadData([]);
          scanFormDs.loadData([]);
        } else {
          setNcRecordType(oldVal);
          ncRecordDs.setState('ncRecordType', ncRecordType);
          detailDs.current?.set('ncRecordType', oldVal);
        }
      });
    }
    detailDs.current?.set('materialObj', null);
    detailDs.current?.set('revisionCode', null);
    setNcRecordType(value);
    ncRecordDs.setState('ncRecordType', ncRecordType);
  };

  const renderTag = (value, record) => {
    switch (record.get('ncRecordStatus')) {
      case 'NEW':
        return <Tag color="green">{value}</Tag>;
      case 'RELEASED':
        return <Tag color="blue">{value}</Tag>;
      case 'COMPLETED':
        return <Tag color="red">{value}</Tag>;
      case 'CANCEL':
        return <Tag color="gray">{value}</Tag>;
      case 'WORKING':
        return <Tag color="volcano">{value}</Tag>;
      default:
        return null;
    }
  };

  const hanldeResetLine = (record) => {
    record.set('operationLov', null);
    record.set('operationId', null);
    record.set('operationName', null);
  }

  // 库存品材料不良/库存品自制件不良
  const inventoryTableColumns: Array<any> = [
    ncRecordType === 'RM_NC' && {
      name: 'materialLotCode',
      width: 200,
      lock: 'left',
    },
    ncRecordType === 'EO_ALL_NC' && {
      name: 'identification',
      width: 200,
      lock: 'left',
    },
    {
      name: 'ncRecordStatusDesc',
      width: 150,
      renderer: ({ value, record }) => renderTag(value, record),
    },
    {
      name: 'qty',
    },
    {
      name: 'uomName',
    },
    {
      name: 'intendedDisposal',
      width: 200,
      editor: record => {
        return canEdit && <Select name="intendedDisposal" onChange={() => { hanldeResetLine(record) }} />;
      },
    },
    {
      name: 'operationLov',
      width: 200,
      editor: () => {
        return canEdit && <Lov />;
      }
    },
    // {
    //   name: 'costCenterLov',
    //   width: 150,
    //   renderer: ({ record }) => record.get('costCenter'),
    //   editor: record => {
    //     return (
    //       detailDs.current?.get('ncIncidentStatus') === 'WORKING' &&
    //       record?.get('ncRecordStatus') === 'WORKING' && <Lov />
    //     );
    //   },
    // },
    {
      name: 'workNumOrderLov',
      width: 150,
      editor: record => {
        return (
          detailDs.current?.get('ncIncidentStatus') === 'WORKING' && detailDs.current?.get('ncRecordType') === 'RM_NC' &&
          record?.get('ncRecordStatus') === 'WORKING' && <Lov />
        );
      },
    },
    {
      name: 'scrapReason',
      width: 150,
      editor: record => {
        return (
          detailDs.current?.get('ncIncidentStatus') === 'WORKING' && detailDs.current?.get('ncRecordType') === 'RM_NC' &&
          record?.get('ncRecordStatus') === 'WORKING' && <Select />
        );
      },
    },
    // {
    //   name: 'workOrderNum',
    //   width: 150,
    // },
    {
      name: 'workOrderQty',
      width: 150,
    },
    {
      name: 'eoNum',
      width: 180,
    },
    {
      name: 'locatorName',
    },
    {
      name: 'containerCode',
      width: 180,
    },
    {
      name: 'prodLineName',
      width: 150,
    },
    {
      name: 'routerName',
      width: 150,
    },
    {
      name: 'operationName',
      width: 150,
    },
    {
      name: 'equipmentCode',
      width: 150,
    },
    {
      name: 'workCellName',
      width: 150,
    },
    {
      name: 'intendedDisposalName',
      width: 150,
    },
    {
      name: 'intendedDisposalTime',
      width: 150,
    },
    // {
    //   name: 'disposalWay',
    //   width: 150,
    // },

    // {
    //   name: 'reviewResult',
    //   width: 150,
    // },
    // {
    //   name: 'relatedUnitName',
    //   width: 150,
    // },
    // {
    //   name: 'reviewName',
    //   width: 150,
    // },
    // {
    //   name: 'reviewTime',
    //   width: 150,
    // },
    // {
    //   name: 'reviewRemark',
    //   width: 150,
    // },
  ];

  // 切换工位
  const handleSyncLineData = record => {
    // 查询设备和工艺
    fetchOperation({
      params: {
        workcellId: record?.get('rootCauseWorkcellId'),
      },
    }).then(res => {
      if (res && res.success) {
        record?.set('rootCauseEquipmentCode', res.rows?.equipmentCode);
        record?.set('rootCauseEquipmentId', res.rows?.equipmentId);
        record?.set('rootCauseOperationId', res.rows?.operationId);
        record?.set('rootCauseOperationCode', res.rows?.operationName);
      }
    });
  };

  const detailColumn: Array<any> = [
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      lock: 'left',
      width: 80,
    },
    {
      lock: 'left',
      name: 'ncCodeDesc',
    },
    {
      name: 'ncCodeStatusDesc',
      width: 150,
    },
    {
      name: 'rootCauseWorkcellLov',
      width: 180,
      renderer: ({ record }) => record.get('rootCauseWorkcellName'),
      editor: record =>
        record.getState('editing') && <Lov onChange={() => handleSyncLineData(record)} />,
    },
    {
      name: 'rootCauseEquipmentCode',
      renderer: ({ record }) => record.get('rootCauseEquipmentCode'),
      width: 180,
    },
    {
      name: 'rootCauseOperationCode',
      width: 150,
    },
    {
      name: 'responsibleUserLov',
      renderer: ({ record }) => record.get('responsibleUserName'),
      editor: record => record.getState('editing') && <Lov />,
    },
    {
      name: 'responsibleApartmentLov',
      width: 130,
      renderer: ({ record }) => record.get('unitName'),
      editor: record => record.getState('editing') && <Lov />,
    },
    {
      name: 'remark',
      editor: record => record.getState('editing') && <TextField />,
    },
    {
      name: 'enclosure',
      width: 150,
      editor: canEdit,
      align: ColumnAlign.center,
    },
    // {
    //   header: intl.get('tarzan.common.title.extendField').d('扩展属性'),
    //   name: 'attrColumn',
    //   width: 150,
    //   renderer: ({ record }) => (
    //     <AttributeDrawer
    //       serverCode={BASIC.HMES_BASIC}
    //       className="org.tarzan.mes.domain.entity.MtNcRecordDetail"
    //       kid={record?.get('ncRecordDetailId')}
    //       canEdit={canEdit && detailDs.current?.get('ncRecordStatus') === 'NEW'}
    //       disabled={!record?.get('ncRecordDetailId')}
    //       custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.LINE.ATTR`}
    //       custConfig={custConfig}
    //       type="text"
    //     />
    //   ),
    //   align: ColumnAlign.center,
    // },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      lock: 'right',
      align: 'center',
      hidden: detailDs.current?.get('ncIncidentStatus') !== 'NEW',
      width: 150,
      renderer: ({ record }) =>
        record.getState('editing') ? (
          <>
            <Button
              color={ButtonColor.primary}
              funcType={FuncType.flat}
              onClick={() => handleCancelLine(record)}
            >
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button color={ButtonColor.primary} funcType={FuncType.flat} onClick={handleSaveLine}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        ) : (
          <Button
            color={ButtonColor.primary}
            icon="edit-o"
            funcType={FuncType.flat}
            disabled={!canEdit || ncDetailLineDs.records.some(item => item.getState('editing'))}
            onClick={() => handleEdit(record)}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        ),
    },
  ];
  const handleEdit = record => {
    record.setState('editing', true);
  };
  const handleCancelLine = record => {
    record.reset();
    record.setState('editing', false);
  };
  const handleSaveLine = async () => {
    await ncDetailLineDs.submit();
    ncDetailLineDs.query();
  };
  const handleLineDelete = () => {
    lineDelete({
      params: ncRecordDs.selected.map(item => item.get('ncRecordId')),
    }).then(res => {
      if (res && res.success) {
        ncRecordDs.remove(ncRecordDs.selected);
        ncDetailLineDs.loadData([]);
        if (ncRecordDs.current) {
          headerRowClick(ncRecordDs.current?.get('ncRecordId'));
        }
        if (ncRecordDs.toData().length === 0) {
          detailDs.current?.set('materialObj', null);
          detailDs.current?.set('revisionCode', null);
        }
      }
    });
  };
  // 批量选择物料批
  const handleChangeMaterialLot = value => {
    if (!value) {
      return;
    }
    const valueList = value.filter(item => item.materialId === value[0].materialId);
    if (valueList?.length !== value?.length) {
      notification.error({ message: '选择条码对应的物料编码不一致，请检查！' });
      return;
    }
    fetchMaterialLotRelatedInfo({
      params: scanFormDs?.current?.get('ncRecordIdMaterialLot'),
    }).then(res => {
      if (res && res.success) {
        // 清空数据
        scanFormDs.current?.init('materialLotLov', undefined);
        scanFormDs.current?.init('ncRecordIdMaterialLot', undefined);
        const temp = ncRecordDs.toData();
        detailDs.current?.set('materialObj', {
          materialCode: res.rows[0]?.materialCode,
          materialId: res.rows[0]?.materialId,
          materialName: res.rows[0]?.materialName,
        });
        detailDs.current?.set('revisionCode', res.rows[0]?.revisionCode);
        ncRecordDs.loadData(
          temp.concat(uniqueArray(res.rows, ncRecordDs.toData(), 'materialLotCode')),
        );
        // 默认查询第一条
        headerRowClick(ncRecordDs.current?.get('ncRecordId'));
      }
    });
  };
  // 批量选择EO
  const handleChangeEo = value => {
    if (!value) {
      return;
    }
    const valueList = value.filter(item => item.materialId === value[0].materialId)
    if (valueList?.length !== value?.length) {
      notification.error({ message: '选择条码对应的物料编码不一致，请检查！' })
      return;
    }
    fetchEoRelatedInfo({
      params: scanFormDs?.current?.get('ncRecordIdEo'),
    }).then(res => {
      if (res && res.success) {
        scanFormDs.current?.init('eoLov', undefined);
        scanFormDs.current?.init('ncRecordIdEo', undefined);
        detailDs.current?.set('materialObj', {
          materialCode: res.rows[0]?.materialCode,
          materialId: res.rows[0]?.materialId,
          materialName: res.rows[0]?.materialName,
        });
        detailDs.current?.set('revisionCode', res.rows[0]?.revisionCode);
        const temp = ncRecordDs.toData();
        ncRecordDs.loadData(
          temp.concat(uniqueArray(res.rows, ncRecordDs.toData(), 'identification')),
        );
        // 默认查询第一条
        headerRowClick(ncRecordDs.current?.get('ncRecordId'));
      }
    });
  };
  // 去重
  const uniqueArray = (res, tableList, field) => {
    // tableList为原值 res为新增数据 field为去重字段
    const temp: any = [];
    res.forEach(i => {
      if (tableList.every((j: any) => j[field] !== i[field])) {
        temp.push(i);
      }
    });
    return temp;
  };
  // 取消-状态变更
  const clickMenu = async () => {
    cancel({
      params: [detailDs.current?.get('ncIncidentId')],
    }).then(res => {
      if (res && res.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        history.push('/hmes/bad-record/platform/list');
      }
    });
  };

  const cancelEdit = () => {
    if (id === 'create') {
      history.push('/hmes/bad-record/platform/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(id);
    }
  };
  // 批量输入物料批&eo弹框
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    openBatchBarCodeModal({
      inputLovDS,
      inputLovFlag,
      inputLovTitle,
      inputLovVisible,
      targetDS: detailDs,
      submit: handleScan,
    });
    inputLovDS.queryDataSet?.current?.set('code', '');
    inputLovDS.data = [];
    inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
  };
  // 物料批&eo扫描
  const handleScan = (inputLovFlag, str) => {
    if (inputLovFlag === 'materialLots') {
      scanMaterialLot(str);
    } else if (inputLovFlag === 'eos') {
      scanEo(str);
    }
  };
  const scanMaterialLot = (data) => {
    scanMaterialLotRelatedInfo({
      params: {
        ncRecordType: detailDs.current?.get('ncRecordType'),
        materialId: detailDs.current?.get('materialId'),
        revisionCode: detailDs.current?.get('revisionCode'),
        materialLots: scanFormDs.current?.get('materialLots') || data,
        siteId: detailDs.current?.get('siteId'),
      },
    }).then(res => {
      if (res && res.success) {
        scanFormDs.current?.init('materialLots', null);
        const temp = ncRecordDs.toData();
        detailDs.current?.set('materialObj', {
          materialCode: res.rows[0]?.materialCode,
          materialId: res.rows[0]?.materialId,
          materialName: res.rows[0]?.materialName,
        });
        detailDs.current?.set('revisionCode', res.rows[0]?.revisionCode);
        ncRecordDs.loadData(
          temp.concat(uniqueArray(res.rows, ncRecordDs.toData(), 'materialLotCode')),
        );
        // 默认查询第一条
        headerRowClick(ncRecordDs.current?.get('ncRecordId'));
      } else {
        scanFormDs.current?.set('materialLots', null)
      }
    }).finally(() => {
      materialLotsRef.current?.focus()
    });
  };
  const scanEo = async (data) => {
    const res = await scanEoRelatedInfo({
      params: {
        identifications: scanFormDs.current?.get('eos') || data,
        ncRecordType: detailDs.current?.get('ncRecordType'),
        materialId: detailDs.current?.get('materialId'),
        revisionCode: detailDs.current?.get('revisionCode'),
        siteId: detailDs.current?.get('siteId'),
        operationId: detailDs.current?.get('operationId'),
      },
    })
    if (res && res.success) {
      if (res && res?.rows.length > 0) {
        scanFormDs.current?.set('eos', null);
        const temp = ncRecordDs.toData();
        detailDs.current?.set('materialObj', {
          materialCode: res.rows[0]?.materialCode,
          materialId: res.rows[0]?.materialId,
          materialName: res.rows[0]?.materialName,
        });
        detailDs.current?.set('revisionCode', res.rows[0]?.revisionCode);
        ncRecordDs.loadData(
          temp.concat(uniqueArray(res.rows, ncRecordDs.toData(), 'identification')),
        );
        // 默认查询第一条
        headerRowClick(ncRecordDs.current?.get('ncRecordId'));
      }
    } else {
      scanFormDs.current?.set('eos', null)
    }
    eosRef.current?.focus()
  };
  const hanldeResetModal = () => {
    modalDs.current?.set('operationLov', null);
    modalDs.current?.set('operationId', null);
    modalDs.current?.set('operationName', null);
    if (modalDs.current?.get('intendedDisposal') === 'REWORK_SOURCE') {
      modalDs.current?.getField('operationLov')?.set('lovCode', 'APEX_MES.OPERATION_NC_INCIDENT');
    } else {
      modalDs.current?.getField('operationLov')?.set('lovCode', 'APEX_MES.OPERATION_ALL');
    }
  };
  // 批量处理按钮
  const handleBatchPreDisposal = () => {
    modalDs.current?.getField('intendedDisposal')?.set('required', true);
    // modalDs.current?.getField('costCenterLov')?.set('required', false);
    Modal.open({
      ...drawerPropsC7n({ modalDs }),
      drawer: false,
      title: intl.get(`${modelPrompt}.title.batchPreDisposal`).d('批量处置'),
      style: {
        width: 500,
      },
      children: (
        <Form labelWidth={112} dataSet={modalDs} columns={1} disabled={!canEdit}>
          <Select name="intendedDisposal" onChange={hanldeResetModal} />
          <Lov name="operationLov" />
        </Form>
      ),
      afterClose: () => {
        modalDs.loadData([]);
      },
      onOk: async () => {
        const flag = await modalDs.validate()
        if (!flag) { return false }
        handleDrawerConfirm('intendedDisposal')
      },
    });
  };
  // const handleBatchInputCostCenter = () => {
  //   modalDs.current?.getField('intendedDisposal')?.set('required', false);
  //   modalDs.current?.getField('costCenterLov')?.set('required', true);
  //   Modal.open({
  //     ...drawerPropsC7n({ modalDs }),
  //     drawer: false,
  //     title: intl.get(`${modelPrompt}.title.batchInputCostCenter`).d('批量录入成本中心'),
  //     style: {
  //       width: 500,
  //     },
  //     children: (
  //       <Form labelWidth={112} dataSet={modalDs} columns={1}>
  //         <Lov name="costCenterLov" />
  //       </Form>
  //     ),
  //     afterClose: () => {
  //       modalDs.loadData([]);
  //     },
  //     onOk: async () => handleDrawerConfirm('costCenterLov'),
  //   });
  // };
  const handleDrawerConfirm = type => {
    // if (await modalDs.validate(true)) {
    // const selectedRecords = ncRecordDs.data;
    const modalData: any = modalDs.toData();
    const intendedDisposal = modalData[0]?.intendedDisposal;
    const _tableData = ncRecordDs.toData()
    if (type === 'intendedDisposal') {
      // 不要直接在DS中操作，效率很低
      _tableData.forEach(item => {
        // @ts-ignore
        item.intendedDisposal = intendedDisposal;
        // @ts-ignore
        item.operationName = modalData[0]?.operationLov?.operationName;
        // @ts-ignore
        item.operationId = modalData[0]?.operationLov?.operationId;
      })
      ncRecordDs.loadData(_tableData)
    }
    // const costCenterLov = modalData[0]?.costCenterLov;
    // if (type === 'intendedDisposal') {
    //   selectedRecords.forEach(item => {
    //     item.set('intendedDisposal', intendedDisposal);
    //     item.set('operationName', modalData[0]?.operationLov?.operationName);
    //     item.set('operationId', modalData[0]?.operationLov?.operationId);
    //   });
    // }
    // else if (type === 'costCenterLov') {
    //   selectedRecords.forEach(item => {
    //     item.set('costCenterLov', costCenterLov);
    //     item.set('costCenter', costCenterLov.costcenterCode);
    //     item.set('description', costCenterLov.description);
    //   });
    // }
    // } else {
    //   return false;
    // }
  };
  const handleMaterialClick = async val => {
    setRevisionList([]);
    detailDs.current?.init('revisionCode', null);
    if (val) {
      queryRevision({
        params: {
          tenantId: getCurrentOrganizationId(),
          siteIds: detailDs.current?.get('siteId'),
          materialId: detailDs.current?.get('materialId'),
        },
      }).then(res => {
        if (res) {
          detailDs.getField('revisionCode')?.set('required', res?.rows.length > 0);
          setRevisionList(res?.rows);
        }
      });
    }
  };
  // 发起评审
  //   const handleInitiateReview = async () => {
  //     if (await detailDs.validate()) {
  //       // 校验欲处置结果
  //       if (ncRecordDs.toData().length < 1) {
  //         notification.warning({
  //           message: intl
  //             .get(`${modelPrompt}.error.noBadInfo`)
  //             .d(`当前不良事故下无不良信息，无法执行！`),
  //         });
  //         return;
  //       }
  //       const validateFlag = ncRecordDs.toData().every((item: any) => item?.intendedDisposal);
  //       if (!validateFlag) {
  //         const data: Array<any> = ncRecordDs.toData();
  //         const findIndex = ncRecordDs.toData().findIndex((item: any) => !item?.intendedDisposal);
  //         notification.warning({message: intl
  //           .get(`${modelPrompt}.error.material.Eo`)
  //           .d(`${ncRecordType === 'RM_NC' ? '物料批' : '执行作业'}
  //           ${
  //   ncRecordType === 'RM_NC' ? data[findIndex]?.materialLotCode : data[findIndex]?.eoNum
  // }所在不良记录单行内欲处置结果为空，无法发起审批!`),
  //         });
  //         return;
  //       }
  //       initiateReview({
  //         params: {
  //           ...detailDs.toData()[0],
  //           // ncRecordList: ncRecordDs.selected?.map(item => item.data),
  //           ncRecordList: ncRecordDs.toData(),
  //         },
  //       }).then(res => {
  //         if (res && res.success) {
  //           history.push('/hmes/bad-record/platform/list');
  //         }
  //       });
  //     }
  //   };
  // 快速报废
  //   const handleQuickScrapping = async () => {
  //     if (await detailDs.validate()) {
  //       if (ncRecordDs.toData().length < 1) {
  //         return notification.warning({
  //           message: intl
  //             .get(`${modelPrompt}.error.noBadInfo`)
  //             .d(`当前不良事故下无不良信息，无法执行！`),
  //         });
  //       }
  //       const validateFlag = ncRecordDs.toData().every((item: any) => item?.intendedDisposal);
  //       if (!validateFlag) {
  //         // 判断行上处置结果是否全部都有数据
  //         const data: Array<any> = ncRecordDs.toData();
  //         const findIndex = ncRecordDs.toData().findIndex((item: any) => !item?.intendedDisposal);
  //         notification.warning({message: intl
  //           .get(`${modelPrompt}.error.material.Eo`)
  //           .d(`${ncRecordType === 'RM_NC' ? '物料批' : '执行作业'}
  //           ${
  //   ncRecordType === 'RM_NC' ? data[findIndex]?.materialLotCode : data[findIndex]?.eoNum
  // }所在不良记录单行内欲处置结果为空，无法发起审批!`),
  //         });
  //         return;
  //       }
  //       quickScrapping({
  //         params: {
  //           ...detailDs.toData()[0],
  //           // ncRecordList: ncRecordDs.selected?.map(item => item.data),
  //           ncRecordList: ncRecordDs.toData(),
  //         },
  //       }).then(res => {
  //         if (res && res.success) {
  //           history.push('/hmes/bad-record/platform/list');
  //         }
  //       });
  //     }
  //   };
  // 报废确认
  const handleScrappingConfirm = async () => {
    if (await detailDs.validate()) {
      // 校验不良事故关联的所有WORKING不良记录是否均有“成本中心”

      // if (ncRecordDs.toData().length < 1) {
      //   return notification.warning({
      //     message: `当前不良事故下无不良信息，无法执行！`,
      //   });
      // }
      // if (ncRecordDs.selected.length < 1) {
      //   return notification.warning({
      //     message: `请至少选中一行运行状态的行执行！`,
      //   });
      // }
      // const selectList: any = ncRecordDs.selected?.map(item => item.data);
      // const filterList = selectList.filter(item => item.ncRecordStatus !== 'WORKING');
      // if (filterList.length > 0) {
      //   return notification.warning({
      //     message: `请选中全部为运行状态的行执行！`,
      //   });
      // }
      // const filterData = ncRecordDs
      //   .toData()
      //   .some((item: any) => (!item?.workOrderId || item?.scrapReason) && item?.ncRecordStatus === 'WORKING');
      // const validateFlag = ncRecordDs
      //   .toData()
      //   .every((item: any) => item?.costCenter && item?.ncRecordStatus === 'WORKING');
      if (ncRecordDs.selected.some((item: any) => !item.get('workOrderId') || !item?.get('scrapReason'))) {
        return notification.warning({
          message: intl
            .get(`${modelPrompt}.error.noScrapInfo`)
            .d(`部分不良记录内未填入工单和报废原因，请检查！`),
        });
        // return;
      }
      // const finalData = selectList.map(item => {
      //   return {
      //     ...item,
      //     costCenter: item.costCenterLov.costcenterCode,
      //   };
      // });
      scrappingConfirm({
        params: {
          ...detailDs.toData()[0],
          ncRecordList: ncRecordDs.selected?.map(item => item.toJSONData()),
        },
      }).then(res => {
        if (res && res.success) {
          history.push('/hmes/bad-record/platform/list');
        }
      });
    }
  };

  // 报废取消
  const handleScrappingCancel = async () => {
    if (await detailDs.validate()) {
      Modal.confirm({
        title: intl
          .get(`${modelPrompt}.error.scrappingCancel`)
          .d(`是否报废取消？`),
        onOk: () => {
          scrappingCancel({
            params: {
              ...detailDs.toData()[0],
              ncRecordList: ncRecordDs.selected?.map(item => item.toJSONData()),
            },
          }).then(res => {
            if (res && res.success) {
              scrappingCancelCheck({
                params: {
                  ncIncidentId: detailDs.current?.get('ncIncidentId'),
                },
              }).then(result => {
                if (result && result.success) {
                  history.push('/hmes/bad-record/platform/list');
                }
              })
            }
          });
        },
      });
    }
  };

  const handleScrapping = () => {
    containerCreateModal = Modal.open({
      key: 'createContainer',
      closable: true,
      title: '批量报废原因',
      destroyOnClose: true, // 关闭时是否销毁
      children: (
        <Form dataSet={formDs}>
          <Select name="scrapping" />
        </Form>
      ),
      cancelButton: false,
      onOk: () => {
        return handleToOkCreateInsp();
      },
      onCancel: () => {
        formDs.reset();
      },
    });
  }
  const handleToOkCreateInsp = async () => {
    const vaidate = await formDs.current?.validate(false, true);
    if (!vaidate) {
      notification.error({
        message: '请校验数据！',
      });
      return false;
    }
    const scrapping = formDs.current?.get('scrapping');
    const selectedRecordsData = ncRecordDs.selected?.map(item => ({
      ...item.toData(),
      scrapReason: scrapping,
    }));
    ncRecordDs.loadData(
      selectedRecordsData.concat(uniqueArray(ncRecordDs.toData(), selectedRecordsData, 'identification')),
    );
    containerCreateModal.close();
    formDs.reset();
  };
  const handleResetQueryProps = () => {
    const eoDs = scanFormDs.getField('eoLov')?.getOptions(scanFormDs.current);
    eoDs?.queryDataSet?.reset();
    const materialLotDs = scanFormDs.getField('materialLotLov')?.getOptions(scanFormDs.current);
    materialLotDs?.queryDataSet?.reset();
  }

  const handleEnterMaterialLots = (e, type) => {
    const value = e.target?.value?.trim();
    if (!value) {
      return;
    }
    handleScan(type, value)
  }

  return (
    <div className="hmes-style" style={{ height: '95%', overflow: 'auto' }}>
      <TarzanSpin
        dataSet={detailDs}
        spinning={
          saveLoading ||
          fetchMaterialLotLoading ||
          fetchEoLoading ||
          lineDeleteLoading ||
          queryRevisionLoading ||
          scanMaterialLotLoading ||
          scanEoLoading ||
          fetchOperationLoading ||
          initiateReviewLoading ||
          quickScrappingLoading ||
          scrappingConfirmLoading ||
          scrappingCancelLoading ||
          scrappingCancelCheckLoading ||
          cancelLoading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('不良记录平台')}
          backPath="/hmes/bad-record/platform/list"
        >
          {detailDs.current?.get('ncIncidentStatus') === 'NEW' && (
            <>
              {canEdit ? (
                <>
                  <Button
                    color={ButtonColor.primary}
                    icon="save"
                    loading={saveLoading}
                    onClick={() => handleSave()}
                  >
                    {intl.get('tarzan.common.button.save').d('保存')}
                  </Button>
                  <Button color={ButtonColor.default} onClick={cancelEdit}>
                    {intl.get('tarzan.common.button.cancel').d('取消')}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    color={ButtonColor.primary}
                    onClick={() => setCanEdit(true)}
                    loading={saveLoading}
                    icon="edit-o"
                  >
                    {intl.get('tarzan.common.button.edit').d('编辑')}
                  </Button>
                  {/* 取消状态 */}
                  <Button
                    disabled={detailDs.current?.get('ncIncidentStatus') !== 'NEW'}
                    onClick={clickMenu}
                    icon="cached"
                    color={ButtonColor.primary}
                  >
                    {intl.get(`tarzan.common.button.cancel`).d('取消')}
                  </Button>
                </>
              )}
              {/* <Button
                color={ButtonColor.primary}
                onClick={handleInitiateReview}
                disabled={detailDs.current?.get('ncIncidentStatus') !== 'NEW'}
              >
                {intl.get(`${modelPrompt}.button.initiateReview`).d('发起评审')}
              </Button>
              <Button
                color={ButtonColor.primary}
                disabled={detailDs.current?.get('ncIncidentStatus') !== 'NEW'}
                onClick={handleQuickScrapping}
              >
                {intl.get(`${modelPrompt}.button.quickScrapping`).d('快速报废')}
              </Button> */}
            </>
          )}

          {detailDs.current?.get('ncIncidentStatus') === 'WORKING' && detailDs.current?.get('ncRecordType') === 'RM_NC' && (
            <Button disabled={ncRecordDs.selected.length === 0
              || ncRecordDs.selected.some(item => item.get('ncRecordStatus') !== 'WORKING')} color={ButtonColor.primary} onClick={handleScrappingConfirm}>
              {intl.get(`${modelPrompt}.button.scrappingConfirm`).d('报废确认')}
            </Button>
          )}

          {/* {(detailDs.current?.get('ncIncidentStatus') === 'WORKING' || detailDs.current?.get('ncIncidentStatus') === 'COMPLETED') && (
            <Button disabled={ncRecordDs.selected.length === 0
            ||ncRecordDs.selected.some(item => item.get('reviewResult') !=='报废' && item.get('reviewResult') !=='快速报废')}
            color={ButtonColor.primary}
            onClick={handleScrappingCancel}
            >
              {intl.get(`${modelPrompt}.button.scrappingCancel`).d('报废取消')}
            </Button>
          )} */}
          {(detailDs.current?.get('ncRecordType') === 'EO_ALL_NC' && detailDs.current?.get('ncIncidentStatus') === 'COMPLETED') && (
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              onClick={handleScrappingCancel}
              disabled={ncRecordDs.selected.length === 0
                || ncRecordDs.selected.some(item => item.get('intendedDisposal') !== 'SCRAP')}
              permissionList={[{
                code: `button.scrappingCancel`,
                type: 'button',
                meaning: '详情页-报废取消按钮',
              },
              ]}
            >
              {intl.get(`${modelPrompt}.button.scrappingCancel`).d('报废取消')}
            </PermissionButton>
          )}
          {/* <AttributeDrawer
            serverCode={BASIC.HMES_BASIC}
            className="org.tarzan.mes.domain.entity.MtNcRecord"
            kid={id}
            canEdit={canEdit}
            disabled={id === 'create'}
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.ATTR`}
            custConfig={custConfig}
          /> */}
        </Header>
        <Content>
          <Collapse bordered={false} activeKey={activeKey} onChange={value => setActiveKey(value)}>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecordFormInfo`).d('不良单据信息')}
              key="badRecordFormInfo"
            >
              {/* {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.HEAD`,
                },

              )} */}
              <Form columns={3} labelWidth={112} dataSet={detailDs} disabled={!canEdit}>
                <TextField name="ncIncidentNum" />
                <Select
                  name="ncRecordType"
                  disabled={id !== 'create'}
                  onChange={handleChangeNcRecordType}
                />
                <Select name="ncIncidentStatus" disabled />
                <Lov name="siteLov" disabled={id !== 'create'} />
                <C7nFormItemSort disabled name="materialObj" itemWidth={['70%', '30%']}>
                  <Lov
                    name="materialObj"
                    onChange={handleMaterialClick}
                    disabled
                  />
                  <Select
                    name="revisionCode"
                    required={revisionList.length > 0}
                    disabled
                  >
                    {revisionList.map(item => {
                      return <Option value={item}>{item}</Option>;
                    })}
                  </Select>
                </C7nFormItemSort>
                {/* {ncRecordType === 'EO_ALL_NC' && <Lov name="operationLov" />} */}
                {/* {ncRecordType === 'EO_ALL_NC' && <Output name="description" />} */}
                {/* <Select name="stage" /> */}
                {/* <Select name="quantityCharacter" /> */}
                <TextArea name="ncIncidentDescirption" newLine />
                <TextArea name="ncIncidentReason" />
                <TextArea name="remark" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecordLineInfo`).d('不良记录信息')}
              key="badRecordLineInfo"
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '60%' }}>
                  {detailDs.current?.get('ncIncidentStatus') === 'NEW' && (
                    <Form
                      columns={2}
                      labelWidth={112}
                      dataSet={scanFormDs}
                      disabled={
                        !canEdit ||
                        (ncRecordType === 'EO_ALL_NC' && !detailDs.current?.get('siteId')) ||
                        (ncRecordType === 'RM_NC' && !detailDs.current?.get('siteId'))
                      }
                    >
                      {ncRecordType === 'RM_NC' && (
                        <>
                          <TextField
                            name="materialLots"
                            ref={materialLotsRef}
                            // readOnly
                            // clearButton={false}
                            placeholder="请扫描物料批"
                            onPaste={(e) => {
                              e.preventDefault();
                              scanFormDs.current!.set('materialLots', e.clipboardData.getData('Text').split(/[\r\s\n]/).filter(item => item.trim()).join(','))
                            }}
                            onEnterDown={(e) => handleEnterMaterialLots(e, 'materialLots')}
                            suffix={
                              <img
                                alt=""
                                style={{ width: '20px', paddingRight: '5px' }}
                                src={scanImg}
                              // onClick={() => onOpenInputModal(true, 'materialLots', '物料批编码')}
                              />
                            }
                          />
                          {
                            status && <Lov
                              name="materialLotLov"
                              onChange={handleChangeMaterialLot}
                              modalProps={{
                                onClose: handleResetQueryProps,
                              }}
                            />
                          }

                        </>
                      )}
                      {ncRecordType === 'EO_ALL_NC' && (
                        <>
                          <TextField
                            name="eos"
                            ref={eosRef}
                            // readOnly
                            // clearButton={false}
                            placeholder="请扫描执行作业"
                            onPaste={(e) => {
                              e.preventDefault();
                              scanFormDs.current!.set('eos', e.clipboardData.getData('Text').split(/[\s\n]/).filter(item => item.trim()).join(','))
                            }}
                            onEnterDown={(e) => handleEnterMaterialLots(e, 'eos')}
                            suffix={
                              <img
                                alt=""
                                style={{ width: '20px', paddingRight: '5px' }}
                                src={scanImg}
                              // onClick={() => onOpenInputModal(true, 'eos', '执行作业编码')}
                              />
                            }
                          />
                          {status && <Lov
                            name="eoLov"
                            onChange={handleChangeEo}
                            modalProps={{
                              onClose: handleResetQueryProps,
                            }}
                          />}
                        </>
                      )}
                    </Form>
                  )}
                </div>
                <div>
                  {/* {detailDs.current?.get('ncIncidentStatus') === 'WORKING' && (
                    <Button
                      funcType={FuncType.flat}
                      onClick={handleBatchInputCostCenter}
                      disabled={
                        ncRecordDs.selected.length === 0 ||
                        ncRecordDs.selected.some(item => item.get('ncRecordStatus') !== 'WORKING')
                      }
                    >
                      {intl.get(`${modelPrompt}.title.batchInputCostCenter`).d('批量录入成本中心')}
                    </Button>
                  )} */}
                  {detailDs.current?.get('ncIncidentStatus') === 'NEW' && (
                    <>
                      <Button
                        funcType={FuncType.flat}
                        onClick={handleBatchPreDisposal}
                        disabled={!canEdit}
                      >
                        {intl.get(`${modelPrompt}.title.batchPreDisposal`).d('批量处置')}
                      </Button>
                      <Popconfirm
                        title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                        onConfirm={() => handleLineDelete()}
                        okText={intl.get('tarzan.common.button.confirm').d('确认')}
                        cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                      >
                        <Button
                          icon="delete"
                          funcType={FuncType.flat}
                          disabled={!canEdit || !ncRecordDs.selected.length}
                        >
                          {intl.get(`tarzan.common.button.delete`).d('删除')}
                        </Button>
                      </Popconfirm>
                    </>
                  )}
                  {detailDs.current?.get('ncIncidentStatus') === 'WORKING' && (
                    <>
                      <Button
                        funcType={FuncType.flat}
                        onClick={handleScrapping}
                        disabled={
                          !ncRecordDs.selected.length ||
                          ncRecordDs
                            .toData()
                            .some(
                              (item: any) =>
                                !['SCRAP', 'QUICKLY_SCRAP'].includes(item.intendedDisposal),
                            )
                        }
                      >
                        {intl.get(`${modelPrompt}.title.batchPreDisposal`).d('批量报废原因')}
                      </Button>
                    </>
                  )}
                </div>
              </div>

              {!!ncRecordType && (
                <Table
                  dataSet={ncRecordDs}
                  columns={inventoryTableColumns}
                  highLightRow
                  rowNumber
                  style={{
                    height: ncRecordDs.toData().length === 0 ? 230 : 350,
                  }}
                  onRow={({ record }) => ({
                    onClick: () => headerRowClick(record.get('ncRecordId')),
                  })}
                />
              )}
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecordDetail`).d('不良记录明细')}
              key="badRecordLineDetail"
            >
              {/* {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.DETAIL`,
                },
              )} */}
              <Table dataSet={ncDetailLineDs} columns={detailColumn} />,
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.HEAD`,
      // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.DETAIL`,
      // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.ATTR`,
      // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.LINE.ATTR`,
    ],
  })(BadRecordDetail as any),
);
