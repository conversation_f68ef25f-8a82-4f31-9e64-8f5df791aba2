import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
// const BASIC = {
//   HMES_BASIC: '/aprs-mes-38283',
// };

/**
 * 获取容器装载信息
 * @function GetContainerInfo
 * @returns {object} fetch Promise
 */
export function GetEquipmentDetail(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/equipment/wkc/rel/query`,
    method: 'POST',
  };
}

/**
 * 获取容器装载信息
 * @function GetContainerInfo
 * @returns {object} fetch Promise
 */
export function GetContainerInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/load-register/container/scan/ui`,
    method: 'GET',
  };
}

/**
 * 获取物料批/EO装载信息
 * @function GetBarcodeInfo
 * @returns {object} fetch Promise
 */
export function GetBarcodeInfo(): object {
  // const API_HOST_EQUIP = process.env.API_HOST === 'http://*************:30880' ? 'http://*************:32150' : process.env.API_HOST === 'http://************:30080' ? 'http://************:32150' : 'http://*************:32150';
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/container-barcode/scan/ui`,
    // url: `${API_HOST_EQUIP}/mes-equip/v1/${tenantId}/mt-container/container-barcode/scan/ui`,
    method: 'GET',
  };
}

/**
 * 解绑物料批/EO
 * @function UnbindMaterialLotInfo
 * @returns {object} fetch Promise
 */
export function UnbindMaterialLotInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/box/unload/ui`,
    method: 'POST',
  };
}

/**
 * 打包物料批/EO
 * @function PackingMaterialLotInfo
 * @returns {object} fetch Promise
 */
export function PackingMaterialLotInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/load-register/execute/ui`,
    method: 'POST',
  };
}

/**
 * 查询当前用户默认的唯一设备
 * @function GetOnlyDefaultEquipmentInfo
 * @returns {object} fetch Promise
 */
export function GetOnlyDefaultEquipmentInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/user/equipment/info/get`,
    method: 'GET',
  };
}

export function GetDetailBarcodeInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/tray/scan/ui`,
    method: 'POST',
  };
}

export function HandleLoad(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/tray/load/ui`,
    method: 'POST',
  };
}

export function HandleUnLoad(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/tray/unload/ui`,
    method: 'POST',
  };
}
