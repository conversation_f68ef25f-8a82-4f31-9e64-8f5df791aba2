import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';

const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`;

const PostPoneHeaderDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: false,
  paging: false,
  selection: DataSetSelection.single,
  fields: [
    {
      type: FieldType.string,
      name: 'sequence',
      label: intl.get(`${modelPrompt}.table.sequence`).d('序号'),
    },
    {
      type: FieldType.string,
      name: 'amendDate',
      label: intl.get(`${modelPrompt}.table.amendDate`).d('延期日期'),
    },
    {
      type: FieldType.string,
      name: 'amendRemark',
      label: intl.get(`${modelPrompt}.table.amendRemark`).d('延期说明'),
    },
    {
      type: FieldType.string,
      name: 'reviewStatus',
      lookupCode:'YP.QIS.DELAY_END_APPLY_STATUS',
      label: intl.get(`${modelPrompt}.table.reviewStatus`).d('审批状态'),
    },
    {
      type: FieldType.string,
      name: 'createdByName',
      label: intl.get(`${modelPrompt}.table.createdByName`).d('发起人'),
    },
    {
      type: FieldType.string,
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.table.creationDate`).d('发起日期'),
    },
    {
      type: FieldType.string,
      name: 'enclosure',
      label: intl.get(`${modelPrompt}.table.enclosure`).d('附件'),
    },
    {
      type: FieldType.string,
      name: 'operationResult',
      label: intl.get(`${modelPrompt}.table.operationResult`).d('工艺评审结果'),
    },
    {
      type: FieldType.string,
      name: 'operationOpinion',
      label: intl.get(`${modelPrompt}.table.operationOpinion`).d('工艺评审意见'),
    },
    {
      type: FieldType.string,
      name: 'operationReviewByName',
      label: intl.get(`${modelPrompt}.table.operationReviewByName`).d('工艺评审人'),
    },
    {
      type: FieldType.string,
      name: 'operationReviewTime',
      label: intl.get(`${modelPrompt}.table.operationReviewTime`).d('工艺评审时间'),
    },
    {
      type: FieldType.string,
      name: 'qualityResult',
      label: intl.get(`${modelPrompt}.table.qualityResult`).d('质量评审结果'),
    },
    {
      type: FieldType.string,
      name: 'qualityOpinion',
      label: intl.get(`${modelPrompt}.table.qualityOpinion`).d('质量评审意见'),
    },
    {
      type: FieldType.string,
      name: 'qualityReviewByName',
      label: intl.get(`${modelPrompt}.table.qualityReviewByName`).d('质量评审人'),
    },
    {
      type: FieldType.string,
      name: 'qualityReviewTime',
      label: intl.get(`${modelPrompt}.table.qualityReviewTime`).d('质量评审时间'),
    },
    {
      type: FieldType.string,
      name: 'manufactureResult',
      label: intl.get(`${modelPrompt}.table.manufactureResult`).d('制造评审结果'),
    },
    {
      type: FieldType.string,
      name: 'manufactureOpinion',
      label: intl.get(`${modelPrompt}.table.manufactureOpinion`).d('制造评审意见'),
    },
    {
      type: FieldType.string,
      name: 'manufactureReviewByName',
      label: intl.get(`${modelPrompt}.table.manufactureReviewByName`).d('制造评审人'),
    },
    {
      type: FieldType.string,
      name: 'manufactureReviewTime',
      label: intl.get(`${modelPrompt}.table.manufactureReviewTime`).d('制造评审时间'),
    },
  ],
});

const PostPoneLineDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      type: FieldType.string,
      name: 'inspectItemCode',
      label: intl.get(`${modelPrompt}.table.inspectItemCode`).d('延期项目编码'),
    },
    {
      type: FieldType.string,
      name: 'inspectItemDesc',
      label: intl.get(`${modelPrompt}.table.inspectItemDesc`).d('延期项目描述'),
    },
    {
      type: FieldType.string,
      name: 'inspectBusinessTypeDesc',
      label: intl.get(`${modelPrompt}.table.inspectBusinessTypeDesc`).d('项目所属业务类型'),
    },
    {
      type: FieldType.string,
      name: 'equipmentName',
      label: intl.get(`${modelPrompt}.table.equipmentName`).d('设备'),
    },
  ],
});

export {
  PostPoneHeaderDS,
  PostPoneLineDS,
};