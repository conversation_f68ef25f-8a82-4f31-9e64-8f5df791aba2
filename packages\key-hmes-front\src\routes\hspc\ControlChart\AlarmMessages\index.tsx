/**
 * @Description: 控制控制图维护-报警信息配置
 * @Author: <<EMAIL>>
 * @Date: 2021-07-27 17:17:36
 * @LastEditTime: 2021-12-13 13:49:40
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState, useMemo } from 'react';
import { DataSet, Table, Button, Modal } from 'choerodon-ui/pro';
import { Badge, Popconfirm } from 'choerodon-ui';
import { ButtonColor, FuncType, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { updateTab } from 'utils/menuTab';
import { ExpandCardC7n } from 'hcm-components-front/lib/components/tarzan-ui';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import styles from './index.module.less';
import EditMessageDrawer from './EditMessageDrawer';
import { tableDS, messageInfoDS } from '../stores/AlarmMessageDS';
import {
  FetchControlChartAlarmMessageDetail,
  SaveControlChartAlarmMessage,
  DeleteControlChartAlarmMessage,
} from '../services';

const modelPrompt = 'tarzan.hspc.controlChartMaintain';

const AlarmMessage = props => {
  const { match } = props;
  const { id } = match.params;
  const [chartInfo, setChartInfo] = useState<any>();
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const messageInfoDs = useMemo(() => new DataSet(messageInfoDS()), []);
  let _editDrawer;

  const fetchControlChartAlarmMessageDetail = useRequest(FetchControlChartAlarmMessageDetail(), {
    manual: true,
  });
  const saveControlChartAlarmMessage = useRequest(SaveControlChartAlarmMessage(), { manual: true });
  const deleteControlChartAlarmMessage = useRequest(DeleteControlChartAlarmMessage(), {
    manual: true,
  });

  useEffect(() => {
    getPageDetail();
    updateTab({
      icon: '',
      title: intl.get(`${modelPrompt}.alarmMessage`).d('报警消息'),
      key: document.location.pathname,
      path: document.location.pathname,
      closable: true,
    });
  }, []);

  function getPageDetail() {
    fetchControlChartAlarmMessageDetail.run({
      params: {
        controlId: id,
      },
      onSuccess: res => {
        const { controlCode, controlDesc, dataList = [] } = res;
        tableDs.loadData(dataList);
        setChartInfo({ controlCode, controlDesc });
      },
    });
  }

  const handleEdit = (record?) => {
    let darwerTitle = intl.get(`${modelPrompt}.createMessageConfig`).d('新建报警消息配置');
    if (record) {
      // 编辑
      darwerTitle = intl.get(`${modelPrompt}.editMessageConfig`).d('编辑报警消息配置');
      const initData = record.toData();
      messageInfoDs.loadData([
        {
          ...initData,
        },
      ]);
    } else {
      // 新建
      messageInfoDs.loadData([
        {
          enableFlag: 'Y',
          messageType: 'EMAIL',
        },
      ]);
    }
    const DrawerProps = {
      infoDs: messageInfoDs,
    };
    _editDrawer = Modal.open({
      key: Modal.key(),
      title: darwerTitle,
      drawer: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: <EditMessageDrawer {...DrawerProps} />,
      footer: (
        <div style={{ float: 'right' }}>
          <Button onClick={handleCloseModal}>
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            loading={saveControlChartAlarmMessage.loading}
            onClick={handleSaveMessage}
            type={ButtonType.submit}
            color={ButtonColor.primary}
          >
            {intl.get('tarzan.common.button.save').d('保存')}
          </Button>
        </div>
      ),
    });
  };

  const handleSaveMessage = async () => {
    messageInfoDs!.current!.set({ nowDate: new Date().getTime() });
    const validate = await messageInfoDs!.current!.validate();
    if (!validate) {
      return;
    }
    const params = {
      ...messageInfoDs!.current!.toData(),
      controlId: id,
    };
    params.emailServerCode = params.emailServerCode || '';
    delete params.emailServerCodeObj;
    delete params.receiverGroupCodeObj;
    delete params.templateCodeObj;
    saveControlChartAlarmMessage.run({
      params,
      onSuccess: () => {
        notification.success({});
        getPageDetail();
        _editDrawer.close();
      },
    });
  };

  const handleCloseModal = () => {
    _editDrawer.close();
  };

  const handleDelete = async record => {
    deleteControlChartAlarmMessage.run({
      queryParams: {
        controlMessageId: record.get('controlMessageId'),
      },
      onSuccess: () => {
        notification.success({});
        getPageDetail();
      },
    });
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <Button
            icon="add"
            funcType={FuncType.flat}
            onClick={() => handleEdit()}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        width: 60,
        lock: ColumnLock.left,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              handleDelete(record);
            }}
          >
            <Button icon="remove" funcType={FuncType.flat} size={Size.small} />
          </Popconfirm>
        ),
      },
      {
        name: 'configureName',
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                handleEdit(record);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'messageType',
        align: ColumnAlign.center,
        width: 120,
      },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'receiverGroupCode',
      },
      {
        name: 'messageCommand',
        width: 180,
      },
    ],
    [],
  );

  return (
    <div className={`${styles['alarm-message']} hmes-style`}>
      <Header
        title={intl.get(`${modelPrompt}.alarmMessageConfiguration`).d('报警消息配置')}
        backPath="/hspc/control-chart/list"
      />
      <Content>
        <ExpandCardC7n
          showExpandIcon={false}
          differTitle={
            <div className={styles['card-title']}>
              <div className={styles['analys-code']}>
                {`${intl
                  .get(`${modelPrompt}.controlCode`)
                  .d('控制控制图编码')}: ${chartInfo?.controlCode || ''}`}
              </div>
              <div className={styles['analys-code']}>
                {`${intl
                  .get(`${modelPrompt}.controlDesc`)
                  .d('控制控制图描述')}: ${chartInfo?.controlDesc || ''}`}
              </div>
            </div>
          }
        >
          <Table dataSet={tableDs} columns={columns} />
        </ExpandCardC7n>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.hspc.controlChartMaintain', 'tarzan.common'],
})(AlarmMessage);
