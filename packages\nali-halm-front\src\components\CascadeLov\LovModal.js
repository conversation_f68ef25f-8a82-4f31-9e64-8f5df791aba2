import React from 'react';
import { Form, Spin, Tree } from 'hzero-ui';
import moment from 'moment';
import { isArray, isEmpty, isFunction, isUndefined, isNumber } from 'lodash';
import qs from 'querystring';
import { Bind } from 'lodash-decorators';
import { createPagination, getCurrentOrganizationId, getResponse } from 'utils/utils';
// import intl from 'utils/intl';
import { DEFAULT_DATETIME_FORMAT, DEFAULT_DATE_FORMAT } from 'utils/constants';
import { searchUnitCompanyLazyLoad, searchUnitDepartment } from 'alm/services//api';
import { mapTree } from 'alm/utils/treeUtils';

import './index.less';
import { queryLovData } from 'services/api';
import { notification } from 'choerodon-ui';

const defaultRowKey = 'lovId';

@Form.create({ fieldNameProp: null })
class LovModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedRows: [],
      list0: [],
      treeKeys: [],
      pagination: {},
      loading: false,
      expandedKeys: [],
      selectedKeys0: [],
      autoExpandParent: true,
      groupTree: [],
      currentRecord: {},
    };
  }

  setSateData(state) {
    if (this.mounted) {
      this.setState(state);
    }
  }

  componentDidMount() {
    this.mounted = true;
  }

  @Bind()
  loadOnFirstVisible() {
    const { delayLoadFlag } = this.props.lov;
    if (this.mounted && !delayLoadFlag) {
      this.queryData();
    }
  }

  componentWillUnmount() {
    this.mounted = false;
  }

  @Bind()
  onSelectChange(selectedRowKeys, selectedRows) {
    // TODO: 支持多选
    const record = selectedRows[0];
    this.props.onSelect(record);
    this.setState({
      selectedRows: [record],
    });
  }

  @Bind()
  handleRowClick(record) {
    this.selectRecord(record);
  }

  selectRecord(record) {
    this.props.onSelect(record);
    this.setState({
      selectedRows: [record],
    });
  }

  @Bind()
  handleRowDoubleClick(record) {
    this.selectRecord(record);
    this.props.onClose();
  }

  hideLoading() {
    this.setState({
      loading: false,
    });
  }

  /**
   * 列表数据查询接口
   * @param {Object} pagination 页码
   * @param {Number} lovIndex 当前所选层级的下一级索引
   * @param {*} nextQueryParam 当前所选节点的key值
   */
  @Bind()
  queryData(lovIndex, nextQueryParam) {
    const filter = this.props.form.getFieldsValue();
    const { lovList = [] } = this.props;
    // 如果是数字，说明查询下一级级联数据，否则是顶级
    const { queryUrl, lovCode, lovTypeCode, queryFields = [] } = isNumber(lovIndex)
      ? lovList[lovIndex]
      : lovList[0];
    let { queryParams = {} } = this.props;
    const { customDefineParams } = this.props;
    if (isNumber(lovIndex)) {
      const fieldName = lovList[lovIndex - 1].valueField;
      queryParams = {
        [fieldName]: nextQueryParam,
      };
      // customDefineParams 用于自定义修改查询参数 两个参数：lovCode 及 现有查询参数 返回新的参数
      if (isFunction(customDefineParams)) {
        queryParams = customDefineParams(lovCode, queryParams);
      }
    }
    let nowQueryParams = queryParams || {};
    if (isFunction(nowQueryParams)) {
      nowQueryParams = nowQueryParams();
    }
    const queryIndex = queryUrl.indexOf('?');
    let sourceQueryParams = {};
    if (queryIndex !== -1) {
      sourceQueryParams = qs.parse(queryUrl.substr(queryIndex + 1));
    }

    const formatFilter = { ...filter };
    queryFields.forEach(item => {
      if (item.dataType === 'DATE' || item.dataType === 'DATETIME') {
        if (filter[item.field]) {
          formatFilter[item.field] = moment(filter[item.field]).format(
            item.dataType === 'DATETIME' ? DEFAULT_DATETIME_FORMAT : DEFAULT_DATE_FORMAT
          );
        }
      }
    });
    const sourceParams = {
      ...formatFilter,
      ...sourceQueryParams,
      ...nowQueryParams,
    };
    const params =
      lovTypeCode !== 'URL'
        ? Object.assign(sourceParams, {
            lovCode,
          })
        : sourceParams;

    /**
     * 替换查询 Url 中的变量
     * @param {String} url
     * @param {Object} data
     */
    function getUrl(url, data) {
      let ret = url;
      const organizationRe = /\{organizationId\}|\{tenantId\}/g;
      Object.keys(data).map(key => {
        const re = new RegExp(`{${key}}`, 'g');
        ret = ret.replace(re, data[key]);
        return ret;
      });
      if (organizationRe.test(ret)) {
        ret = ret.replace(organizationRe, getCurrentOrganizationId());
      }
      const index = ret.indexOf('?'); // 查找是否有查询条件
      if (queryIndex !== -1) {
        ret = ret.substr(0, index);
      }
      return ret;
    }

    const url = getUrl(queryUrl, queryParams);

    this.setState(
      {
        loading: true,
      },
      () => {
        queryLovData(url, params)
          .then(res => {
            if (getResponse(res)) {
              if (isNumber(lovIndex)) {
                // 查询下一级列表数据
                this.nextDataFilter(res, lovIndex);
              } else {
                // modal打开时查询第一列数据
                this.dataFilter(res);
              }
            }
          })
          .then(() => {
            if (!isNumber(lovIndex)) {
              // 还需要将 Lov 的选中数据清空
              const { onSelect } = this.props;
              onSelect();
              this.setState({
                selectedRows: [],
              });
            }
          })
          .finally(() => {
            this.hideLoading();
          });
      }
    );
  }

  @Bind()
  formReset() {
    this.props.form.resetFields();
  }

  /**
   * 树 child 属性更改
   * @param {Array} list 原树结构数据
   * @param {String} childName 要替换的 childName
   */
  @Bind()
  setChildren = (data, childName) =>
    childName
      ? data.map(n => {
          const item = n;
          if (!isEmpty(n[childName])) {
            this.defineProperty(item, 'children', [{ ...n[childName] }]);
          }
          if (!isEmpty(item.children)) {
            item.children = this.setChildren(item.children);
          }
          return item;
        })
      : data;

  /**
   * 处理返回列表数据
   * @param {Object|Array} data - 返回的列表数据
   */
  @Bind()
  dataFilter(data) {
    const {
      lov: { valueField: rowkey = defaultRowKey, childrenFieldName },
    } = this.props;
    const isTree = isArray(data);
    const hasParams = !isEmpty(
      Object.values(this.props.form.getFieldsValue()).filter(e => e !== undefined && e !== '')
    );
    const list = isTree ? this.setChildren(data, childrenFieldName) : data.content;
    const pagination = !isTree && createPagination(data);

    const treeKeys = []; // 树状 key 列表
    if (isTree && hasParams) {
      /**
       * 遍历生成树列表
       * @param {*} treeList - 树列表数据
       */
      const flatKeys = treeList => {
        if (isArray(treeList.children) && !isEmpty(treeList.children)) {
          treeKeys.push(treeList[rowkey]);
          treeList.children.forEach(item => flatKeys(item));
        } else {
          treeKeys.push(treeList[rowkey]);
        }
      };

      list.forEach(item => flatKeys(item)); // 遍历生成 key 列表
    }

    this.setSateData({
      list0: list,
      treeKeys,
      pagination,
    });
  }

  /**
   * 处理下一级返回列表数据
   * @param {Object|Array} data - 返回的列表数据
   */
  @Bind()
  nextDataFilter(data, lovIndex) {
    const name = `list${lovIndex}`;
    const selectedName = `selectedKeys${lovIndex}`;
    this.setState({
      [name]: isArray(data) ? data : data.content,
      [selectedName]: [],
    });
  }

  @Bind()
  defineProperty(obj, property, value) {
    Object.defineProperty(obj, property, {
      value,
      writable: true,
      enumerable: false,
      configurable: true,
    });
  }

  /**
   * 访问对象由字符串指定的多层属性
   * @param {Object} obj 访问的对象
   * @param {String} str 属性字符串，如 'a.b.c.d'
   */
  @Bind()
  parseField(obj, str) {
    if (/[.]/g.test(str)) {
      const arr = str.split('.');
      const newObj = obj[arr[0]];
      const newStr = arr.slice(1).join('.');
      return this.parseField(newObj, newStr);
    }
    return obj[str];
  }

  @Bind()
  handleExpandSubLine(isExpand, record) {
    const { expandedRowKeys = [], list } = this.state;
    const {
      lov: { valueField: rowkey = defaultRowKey },
    } = this.props;
    const rowKeys = isExpand
      ? [...expandedRowKeys, record[rowkey]]
      : expandedRowKeys.filter(item => item !== record[rowkey]);
    const flag = ['C', 'G'].includes(record.unitTypeCode);
    this.setState({ expandedRowKeys: [...rowKeys], loading: flag });
    if (flag && isExpand) {
      Promise.all([
        this.handleSearchCompany(record[rowkey]),
        this.handleSearchDepartment(record[rowkey]),
      ]).then(res => {
        // eslint-disable-next-line no-shadow
        let message = '';
        this.setState({ loading: false });
        let resCo = res[0] || [];
        resCo = resCo.map(item => {
          return { ...item, children: [] };
        });
        const resDept = res[1] || [];
        if (isUndefined(resCo)) {
          this.setState({ loading: false });
          message = '公司级组织查询失败!';
          notification.warning({ message });
          return;
        }
        if (isUndefined(resDept)) {
          message = '部门级级组织查询失败!';
          notification.warning({ message });
          this.setState({ loading: false });
          return;
        }
        const newList = mapTree(list, newItem => {
          if (newItem[rowkey] === record[rowkey]) {
            return { ...newItem, children: [...resCo, ...resDept] };
          } else {
            return newItem;
          }
        });
        this.setState({ list: newList });
      });
    }
  }

  handleSearchCompany(rowkey) {
    return getResponse(
      searchUnitCompanyLazyLoad({ unitId: rowkey, tenantId: getCurrentOrganizationId() })
    );
  }

  handleSearchDepartment(rowkey) {
    return getResponse(
      searchUnitDepartment({ unitCompanyId: rowkey, tenantId: getCurrentOrganizationId() })
    );
  }

  @Bind()
  onTreeExpand(expandedKeys) {
    this.setState({
      expandedKeys,
      autoExpandParent: false,
    });
  }

  /**
   * 选择树节点触发
   * @param {Array} selectedKeys 选择节点的key集
   * @param {Object} info 当前所选节点
   * @param {Number} index 级联层级索引，第一级索引为0
   */
  @Bind()
  onTreeSelect(selectedKeys, info, index) {
    const { currentRecord = {} } = this.state;
    let tmp = {
      ...currentRecord,
    };
    const clearData = {};
    // 取消选择节点
    if (selectedKeys.length === 0) {
      // 清空当前级联层级之后的层级列表数据，以及选择的节点集
      for (let i = index; i < this.props.viewCodeList.length; i++) {
        clearData[`list${i + 1}`] = [];
        clearData[`selectedKeys${i}`] = [];
      }
      // 取消选择级联第一级则清空组件当前所选记录，以及第一级选择的节点集
      if (index === 0) {
        tmp = null;
        // clearData.selectedKeys0 = [];
      } else {
        // 否则置空当前层级之后的当前所选记录中对应的属性
        for (let i = index; i < this.props.lovList.length; i++) {
          const fieldId = this.props.lovList[i].valueField;
          const fieldName = this.props.lovList[i].displayField;
          tmp = {
            ...tmp,
            [fieldId]: '',
            [fieldName]: '',
          };
        }
      }
      this.setState(clearData);
    } else {
      // 选择节点
      // 添加当前层级选择的节点key
      this.setState({
        [`selectedKeys${index}`]: [selectedKeys[0]],
      });
      // 清空当前级联层级之后的层级列表数据，以及选择的节点集
      for (let i = index + 1; i < this.props.viewCodeList.length; i++) {
        clearData[`list${i}`] = [];
        clearData[`selectedKeys${i}`] = [];
      }
      // 在当前所选记录中添加所选节点的对应属性字段值
      const fieldId = this.props.lovList[index].valueField;
      const fieldName = this.props.lovList[index].displayField;
      tmp = {
        ...currentRecord,
        ...info.node.props.dataRef,
        [fieldId]: selectedKeys[0],
        [fieldName]: info.selectedNodes[0].props.title,
      };
      if (index < this.props.viewCodeList.length - 1) {
        this.props.onChangeLovList(index, selectedKeys[0]);
      }
    }
    this.setState(clearData);
    this.setState({
      currentRecord: tmp,
    });
    this.selectRecord(tmp);
  }

  @Bind()
  loop(data = [], index) {
    // lov配置信息未加载时
    if (this.props.lovList.length <= index) {
      return;
    }
    const key = this.props.lovList[index].valueField;
    const title = this.props.lovList[index].displayField;
    return data.map(item => {
      if (item.children && item.children.length > 0) {
        return (
          <Tree.TreeNode isLeaf={false} dataRef={item} key={item[key]} title={item[title]}>
            {this.loop(item.children, index)}
          </Tree.TreeNode>
        );
      }
      return <Tree.TreeNode isLeaf dataRef={item} key={item[key]} title={item[title]} />;
    });
  }

  render() {
    const { lovLoadLoading, viewCodeList = [] } = this.props;
    if (lovLoadLoading) {
      return <Spin spinning />;
    }
    return (
      <React.Fragment>
        <div className="cascade-lov-wrapper">
          <div className="cascade-lov-title">
            {this.props.columnsName.map((val, index) => {
              // eslint-disable-next-line react/no-array-index-key
              return <div key={index}>{val}</div>;
            })}
          </div>
          <div className="cascade-lov-content">
            {viewCodeList.map((val, index) => {
              return (
                // eslint-disable-next-line react/no-array-index-key
                <div key={index}>
                  <Tree
                    showLine
                    // switcherIcon={<DownOutlined />}
                    onExpand={this.onTreeExpand}
                    selectedKeys={this.state[`selectedKeys${index}`]}
                    onSelect={(selectedKeys, info) => this.onTreeSelect(selectedKeys, info, index)}
                  >
                    {this.loop(this.state[`list${index}`], index)}
                  </Tree>
                </div>
              );
            })}
          </div>
        </div>
      </React.Fragment>
    );
  }
}

export default LovModal;
