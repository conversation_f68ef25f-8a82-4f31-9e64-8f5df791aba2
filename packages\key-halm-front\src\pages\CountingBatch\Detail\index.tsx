import React, { useEffect, useState } from 'react';
import { Button, Form, DataSet, Output, Modal } from 'choerodon-ui/pro';
import { Tabs, Popconfirm, Spin } from 'choerodon-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/interface';
import moment from 'moment';
import { isUndefined, isEmpty, isNumber } from 'lodash'; // isEmpty
import { useDataSet } from 'utils/hooks';
import request from 'utils/request';
import notification from 'utils/notification';
import { getCurrentEmployee, queryPrimaryOrg } from 'alm/services/api';
import { HALM_ORI } from 'alm/utils/config';
import { handleNonPermissionErr } from 'alm/utils/response';
import { observer } from 'mobx-react-lite';
import {
  submitCountingTask,
  differenceMatch,
  closeCountingTask,
  fetchCacheProgress,
  cacheOfflinePkgInBatch,
  fetchCheckSplit,
} from 'alm/services/countingBatchsService';
import { queryOrgByEmployee } from 'alm/services/organizationService';
import { SplitTaskBtn, CuxProgress } from 'alm/components/Counting';
import CountingLines from 'alm/components/Counting/CountingLines';
import getLang from '../Langs';

import { detailDS, countingRangeListDS, countingTasksListDS } from '../Stores/detailDS';
import HeadForm from './HeadForm';
import CountingTaskTable from './CountingTaskTable';
import CountingRangeTable from './CountingRangeTable';
import { useIntervalCountingProgress } from '../hooks';

const tenantId = getCurrentOrganizationId();
// 检查盘点任务是否有
const validateUrl = `${HALM_ORI}/v1/${tenantId}/counting-task/no-count-person-task`;
export interface Detail {
  [key: string]: any;
}

const Index = props => {
  const {
    history,
    match: { params },
  } = props;
  const { id } = params;
  const isNew = isUndefined(id);

  // state
  const [editFlag, setEditFlag] = useState(false);
  const [batchStatus, setBatchStatus] = useState('DRAFT');
  const [releaseBtnShowFlag, setReleaseBtnShowFlag] = useState(0);
  const [differenceMatchedFlag, setDifferenceMatchedFlag] = useState(0);
  const [loading, setLoading] = useState(false);

  // 间隔获取拆分、离线包进度
  const {
    percent,
    setPercent,
    pendingFlag,
    setPendingFlag,
    progressDesc,
    setProgressDesc,
    progressInterval,
    handleClearInterval,
    getCntBatchSplitProgress,
  } = useIntervalCountingProgress();

  // 缓存ds
  const detailDs = useDataSet(() => new DataSet(detailDS()));
  const countingRangeListDs = useDataSet(() => new DataSet(countingRangeListDS()));
  const countingTasksListDs = useDataSet(() => new DataSet(countingTasksListDS()));
  const [permissionData, setPermissionData] = useState<Object>({});
  const headerProps = {
    backPath: '/actn/counting-batchs/list',
  };

  // 页面初始化
  useEffect(() => {
    const isEdit = history?.location?.state?.isEdit;
    if (isNew || isEdit) {
      setEditFlag(true);
    }
    if (isNew) {
      const param = { tenantId };
      getCurrentEmployee(param).then(res => {
        detailDs.create({
          batchStatus: 'DRAFT',
          planedStartDate: moment(),
          planedEndDate: moment().add(30, 'day'),
          creatorName: res.employeeName,
          creatorId: res.employeeId,
        });
        // queryOrgByEmployee({ tenantId, employeeId: res?.employeeId }).then(
        //   orgInfo => {
        //     detailDs?.current?.set('initiateOrgLov', {
        //       initiateOrgId: orgInfo?.unitId,
        //       initiateOrgName: orgInfo?.unitName,
        //       initiateOrgType: orgInfo?.orgType,
        //     });
        //   },
        // );
        queryPrimaryOrg(res.employeeId).then(orgInfo => {
          detailDs.current?.set({
            initiateOrgLov: {
              valueField: orgInfo?.unitId,
              textField: orgInfo?.unitName,
            },
            initiateOrgType: orgInfo?.orgType,
          });
        });
      });
    } else {
      getDetail(id);
      handleProgressShow();
    }
  }, []);

  /**
   * @description: 进入页面检查是否需要显示进度条：在拆分中或者生成离线包中显示
   * 检查盘点计划离线包是否在生成中，以及获取进度用的一个接口
   * 检测盘点计划是否拆分任务中，获取拆分进度接口非同一个
   * @return {*}
   */
  function handleProgressShow() {
    Promise.allSettled([
      fetchCheckSplit({
        tenantId,
        batchId: id,
      }),
      fetchCacheProgress({ batchId: id }),
    ]).then(results => {
      const res0: any = results[0];
      const res1: any = results[1];
      if (res0?.value) {
        setProgressDesc(getLang('CT_SPLIT_PROGRESS'));
        getCntBatchSplitProgress(id, () => getDetail(id));
      } else if (res1.value) {
        const res = res1.value;
        if (res?.processingFlag === 1 && res?.progress !== 100 && isNumber(res?.progress)) {
          setProgressDesc(getLang('OFFLINE_PROGRESS'));
          setPercent(Number.parseInt(res?.progress, 10));
          const getProgress = () => fetchCacheProgress({ batchId: id });
          progressInterval(getProgress, 2000);
        }
      }
    });
  }

  async function getDetail(batchId: number) {
    setLoading(true);
    try {
      detailDs.setQueryParameter('batchId', batchId);
      countingRangeListDs.setQueryParameter('batchId', batchId);
      countingTasksListDs.setQueryParameter('batchId', batchId);
      countingTasksListDs.setQueryParameter('speFlag', 1);
      countingTasksListDs.query();
      await detailDs.query();
      const newPermissionData =
        detailDs?.current?.get(['creatorId', 'initiateOrgId', 'initiateOrgType']) ?? {};
      setPermissionData(newPermissionData);
      countingTasksListDs.setState('permissionData', newPermissionData);
      countingRangeListDs.query();
      const detail = detailDs?.current?.toData();
      setBatchStatus(detail.batchStatus);
      setReleaseBtnShowFlag(detail.releaseBtnShowFlag);
      setDifferenceMatchedFlag(detail.differenceMatchedFlag);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }

  /**
   * 保存
   * 不允许保存空的盘点范围行
   */
  async function handleSave() {
    const validateFlag = await detailDs.validate();
    if (validateFlag) {
      const res = await detailDs.submit();
      if (res && res.success) {
        setEditFlag(false);
        if (isUndefined(id)) {
          history.push({
            pathname: `/actn/counting-batchs/detail/${res.content[0].batchId}`,
            state: {
              isEdit: true,
            },
          });
        } else {
          getDetail(res.content[0].batchId);
          history.replace(window.location.pathname, {});
        }
      }
    }
  }

  async function handleDelete() {
    const res = await detailDs.delete(detailDs.current, getLang('DELETE_ORDER'));
    if (res && res.success) {
      history.push(`/actn/counting-batchs/list`);
    }
  }

  function handleEdit() {
    setEditFlag(preState => {
      if (preState) {
        getDetail(id);
        history.replace(window.location.pathname, {});
      }
      return !preState;
    });
  }

  // 拆分盘点任务
  const autoStartCounting = splitFieldDs => {
    setProgressDesc(getLang('CT_SPLIT_PROGRESS'));
    splitFieldDs.current.setState('detail', detailDs?.current?.toData());
    // 盘点计划拆分任务接口调用完毕不能代表拆分任务完成(后端异步返回的感觉)，拆分任务完成需要根据接口进度判断
    setPendingFlag(true);
    splitFieldDs
      .submit()
      .then(res => {
        if (res && res.success) {
          getDetail(id);
          // 异步获取拆分进度
          getCntBatchSplitProgress(id, () => getDetail(id));
        } else {
          handleClearInterval();
        }
      })
      .catch(() => {
        handleClearInterval();
      });
  };

  // 校验是否能下达，返回为空字符串时校验通过
  const handleCheckCounting = async () => {
    const res = await request(validateUrl, {
      method: 'GET',
      params: {
        batchId: id,
      },
    }).catch(err => {
      getResponse(err);
    });
    if (res && res?.length) {
      Modal.warning(res);
      return false;
    } else {
      return true;
    }
  };

  /**
   * 下达盘点任务-盘点人员不能为空
   */
  const submit = async () => {
    const validateFlag = await handleCheckCounting();
    if (validateFlag) {
      Modal.confirm({
        children: getLang('CONFIRM_ORDER'),
        onOk: async () => {
          const res = await submitCountingTask(detailDs?.current?.toData()).catch(err => {
            return err;
          });
          if (isEmpty(res)) {
            notification.success({});
            getDetail(id);
          } else if (res?.failed) {
            handleNonPermissionErr(res);
          }
        },
      });
    }
  };

  const handleDifferenceMatch = () => {
    const param = {
      tenantId,
      differenceMatchedFlag: 1,
      ...detailDs?.current?.toData(),
    };
    differenceMatch(param).then(res => {
      if (isEmpty(res)) {
        notification.success({});
        getDetail(id);
      }
    });
  };

  const displaySaveBtn = editFlag ? { display: 'block' } : { display: 'none' };
  const displayBtn =
    editFlag || batchStatus !== 'DRAFT' ? { display: 'none' } : { display: 'block' };
  const displayOrderBtn =
    releaseBtnShowFlag && batchStatus === 'DRAFT' && !editFlag
      ? { display: 'block' }
      : { display: 'none' };

  const displayAutoCreate =
    batchStatus && batchStatus === 'DRAFT' && !editFlag
      ? { display: 'block' }
      : { display: 'none' };

  const displayDifferMatch =
    !editFlag && batchStatus === 'COMPLETED' && differenceMatchedFlag !== 1 // [已完成,且还未]
      ? { display: 'block' }
      : { display: 'none' };
  const displayCancelBtn = !isNew && editFlag ? { display: 'block' } : { display: 'none' };
  // 盘点计划已拆分时显示
  const displayOfflinePkgBtn =
    !isNew &&
    !editFlag &&
    countingTasksListDs?.totalCount > 0 &&
    batchStatus !== 'COMPLETED' &&
    batchStatus !== 'CLOSED'
      ? { display: 'block' }
      : { display: 'none' };

  const handleCloseCountingTask = () => {
    const data = {
      tenantId,
      data: detailDs?.current?.toData(),
    };
    closeCountingTask(data).then(res => {
      if (res) {
        notification.success({});
        getDetail(id);
      }
    });
  };

  // 生成离线包
  const handleGenerateOfflinePkg = async () => {
    setProgressDesc(getLang('OFFLINE_PROGRESS'));
    cacheOfflinePkgInBatch({
      batchId: id,
      tenantId,
      ...permissionData,
    })
      .then(res => {
        if (res.failed) {
          getResponse(res);
          handleClearInterval(); // 生成离线包失败清除定时器
        } else {
          notification.success({});
          getDetail(id);
        }
      })
      .catch(() => {
        handleClearInterval();
      });
    // 定时器获取离线包生成进度
    const getProgress = () => fetchCacheProgress({ batchId: id });
    progressInterval(getProgress, 2000);
  };

  const countingTaskTableProps = {
    history,
    batchStatus,
  };

  const headProps = {
    editFlag,
  };

  const getButtons = () => {
    const detail = detailDs?.current?.toData() || {};
    const { countingTypeId } = detail;
    const splitFieldProps = {
      countingTypeId,
      onSplitTask: autoStartCounting,
    };
    return (
      <>
        <Button style={displaySaveBtn} color={ButtonColor.primary} onClick={handleSave}>
          {getLang('SAVE')}
        </Button>
        <Button color={ButtonColor.primary} style={displayBtn} onClick={handleEdit}>
          {getLang('EDIT')}
        </Button>
        <Button color={ButtonColor.primary} style={displayOrderBtn} onClick={submit}>
          {getLang('ORDER')}
        </Button>
        <Button style={displayCancelBtn} onClick={handleEdit}>
          {getLang('CANCEL_EDIT')}
        </Button>
        <Button style={displayBtn} onClick={handleDelete}>
          {getLang('DELETE')}
        </Button>
        <SplitTaskBtn
          style={displayAutoCreate}
          text={getLang('AUTO_START')}
          code="COUNTING_BATCH"
          {...splitFieldProps}
        />
        <Button style={displayOfflinePkgBtn} onClick={handleGenerateOfflinePkg}>
          {getLang('GENER_OFFLINE_PKG')}
        </Button>
        <Button style={displayDifferMatch} onClick={handleDifferenceMatch}>
          {getLang('DIFF')}
        </Button>
        {batchStatus === 'INPRG' && !editFlag && (
          <Popconfirm
            placement="topRight"
            title={getLang('CLOSE_CONFIRM')}
            onConfirm={handleCloseCountingTask}
            okText={getLang('OK')}
            cancelText={getLang('CANCEL')}
          >
            <Button>{getLang('CLOSE_TASK')}</Button>
          </Popconfirm>
        )}
      </>
    );
  };

  return (
    <PageHeaderWrapper
      title={getLang('HEADER')}
      header={pendingFlag ? <CuxProgress lable={progressDesc} percent={percent} /> : getButtons()}
      headerProps={headerProps}
    >
      <Spin spinning={loading}>
        <HeadForm dataSet={detailDs} {...headProps} />
        {!isNew && (
          <Tabs defaultActiveKey="countingRange">
            <Tabs.TabPane key="countingRange" tab={getLang('CHECKLIST')}>
              <CountingRangeTable dataSet={countingRangeListDs} editFlag={editFlag} batchId={id} />
            </Tabs.TabPane>
            {!editFlag && (
              <Tabs.TabPane key="countingProgress" tab={getLang('COUNTING_PROGRESS')}>
                <Form dataSet={detailDs} columns={3}>
                  <Output name="batchTotalQty" />
                  <Output name="batchActualQty" />
                  <Output name="batchUnActualQty" />
                  <Output name="batchAmtActualQty" />
                  <Output name="batchProfitQty" />
                  <Output name="batchLossQty" />
                  <Output name="batchDiffQty" />
                </Form>
              </Tabs.TabPane>
            )}
            {!editFlag && (
              <Tabs.TabPane key="countingTask" tab={getLang('ASSESS_TEMPLATE')}>
                <CountingTaskTable dataSet={countingTasksListDs} {...countingTaskTableProps} />
              </Tabs.TabPane>
            )}
            {!editFlag && (
              <Tabs.TabPane key="countingLine" tab={getLang('COUNTING_LINES')}>
                <CountingLines
                  key={`batchId-${id}`}
                  batchId={id}
                  customizedCode="AORI.BATCH.COUNTING_LINES"
                  permissionData={permissionData}
                />
              </Tabs.TabPane>
            )}
          </Tabs>
        )}
      </Spin>
    </PageHeaderWrapper>
  );
};

export default formatterCollections({
  code: [
    'aatn.countingBatchs',
    'alm.common',
    'alm.component',
    'amdm.organization',
    'hiam.authority',
  ],
})(observer(Index));
