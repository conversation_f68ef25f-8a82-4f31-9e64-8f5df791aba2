/**
 * @Description: 正向追溯报表
 */

import React, { useMemo } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { isNil } from 'lodash';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC } from '@utils/config';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import ExcelExport from 'components/ExcelExport';
import {
  copperBackwardTraceabilityReportTableDS,
} from './stories';

const modelPrompt = 'tarzan.hmes.CopperBackwardTraceabilityReport';
const tenantId = getCurrentOrganizationId();

const CopperBackwardTraceabilityReport = observer(props => {
  const {
    copperBackwardTraceabilityReportTableDs,
  } = props;

  // 跳转物料批追溯功能
  const jumpToInspectDocMaintain = (type, record) => {
    let queryParameter = {};
    if (type === 'rowIdentification') {
      queryParameter = {
        materialLotCode: record.get('rowIdentification'),
        eoNum: null,
      };
    } else if (
      type === 'ckIdentification' ||
      type === 'fjIdentification' ||
      type === 'fqIdentification'
    ) {
      queryParameter = {
        materialLotCode: null,
        eoNum: record.get(type),
      };
    }
    props.history.push({
      key: new Date().getTime(),
      pathname: `/hwms/inspect-doc-maintain/list/${new Date().getTime()}`,
      state: queryParameter,
    });
  };

  const columnsOutput: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'correlationCode',
        width: 150,
      },
      {
        name: 'lot',
        width: 150,
      },
      {
        name: 'inspectDocNum',
      },
      {
        name: 'fqIdentification',
        renderer: ({ value, record }) => {
          return <a onClick={() => jumpToInspectDocMaintain('fqIdentification', record)}>{value}</a>;
        },
      },
      {
        name: 'sideFlag',
      },
      {
        name: 'tailFlag',
      },
      {
        name: 'fjIdentification',
        width: 150,
        renderer: ({ value, record }) => {
          return <a onClick={() => jumpToInspectDocMaintain('fjIdentification', record)}>{value}</a>;
        },
      },
      {
        name: 'ckIdentification',
        width: 150,
        renderer: ({ value, record }) => {
          return <a onClick={() => jumpToInspectDocMaintain('ckIdentification', record)}>{value}</a>;
        },
      },
      {
        name: 'rowIdentification',
        width: 150,
        renderer: ({ value, record }) => {
          return <a onClick={() => jumpToInspectDocMaintain('rowIdentification',record)}>{value}</a>;
        },
      },
      {
        name: 'materialName',
        width: 150,
      },
      {
        name: 'supplierName',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'lines',
        width: 120,
      },
      {
        name: 'fqPlating',
        width: 150,
      },
    ];
  }, []);

  // 处理导出按钮使用的查询参数
  const getExportQueryParams = () => {
    if (!copperBackwardTraceabilityReportTableDs.queryDataSet || !copperBackwardTraceabilityReportTableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = copperBackwardTraceabilityReportTableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return {
      ...queryParams,
    };
  };


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('镀铜-正向追溯查询')} >
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-trace-report/reverse/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            queryFieldsLimit: 8,
          }}
          dataSet={copperBackwardTraceabilityReportTableDs}
          columns={columnsOutput}
          searchCode="COPPERBACKWARDTRACEABILITYREPORTTABLE"
          customizedCode="COPPERBACKWARDTRACEABILITYREPORTTABLE"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.CopperBackwardTraceabilityReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const copperBackwardTraceabilityReportTableDs = new DataSet({
        ...copperBackwardTraceabilityReportTableDS(),
      });

      return {
        copperBackwardTraceabilityReportTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(CopperBackwardTraceabilityReport),
);
