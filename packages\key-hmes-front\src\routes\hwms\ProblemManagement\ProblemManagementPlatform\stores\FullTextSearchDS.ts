/*
 * @Description: 问题管理平台-全文检索DS
 * @Author: <<EMAIL>>
 * @Date: 2023-11-09 10:19:57
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-01 10:32:01
 */
import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';
const tenantId = getCurrentOrganizationId();

const searchFormDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'queryContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.queryContent`).d('检索内容'),
    },
    {
      name: 'searchType',
      type: FieldType.string,
      defaultValue: 'any',
      textField: 'meaning',
      valueField: 'value',
      options: new DataSet({
        autoCreate: true,
        selection: DataSetSelection.single,
        data: [
          { value: 'any', meaning: intl.get(`${modelPrompt}.includeAnyKeyword`).d('包含任意一个关键词') },
          // { value: 'all', meaning: intl.get(`${modelPrompt}.includeAllKeyword`).d('包含全部的关键词') },
          { value: 'perfect', meaning: intl.get(`${modelPrompt}.completeMatchingKeyword`).d('完全匹配关键字') },
        ],
      }),
    },
    {
      name: 'searchDomain',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.searchDomain`).d('搜索域'),
      defaultValue: 'any',
      textField: 'meaning',
      valueField: 'value',
      options: new DataSet({
        autoCreate: true,
        selection: DataSetSelection.single,
        data: [
          { value: 'any', meaning: intl.get(`${modelPrompt}.searchDomain.any`).d('不限') },
          { value: 'problem_base_index', meaning: intl.get(`${modelPrompt}.searchDomain.problemBasicInfo`).d('问题基本信息') },
          { value: 'problem_measure_temp_index', meaning: intl.get(`${modelPrompt}.searchDomain.tempMeasure`).d('临时措施') },
          { value: 'problem_reason_index', meaning: intl.get(`${modelPrompt}.searchDomain.reasonAnalysis`).d('原因分析') },
          { value: 'problem_measure_perp_index', meaning: intl.get(`${modelPrompt}.searchDomain.perpMeasure`).d('长期措施') },
          { value: 'qis_problem_influence_range_index', meaning: intl.get(`${modelPrompt}.searchDomain.influenceRange`).d('止呼待范围') },
          { value: 'problem_attachment_file', meaning: intl.get(`${modelPrompt}.searchDomain.attachment`).d('附件') },
        ],
      }),
    },
  ],
});

const searchTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'problemId',
  queryFields: [
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题标题'),
    },
    {
      name: 'problemStatusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_STATUS',
      textField: 'meaning',
      valueField: 'value',
      multiple: true,
    },
    {
      name: 'problemCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDescription`).d('问题描述'),
    },
    {
      name: 'registerPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registerPerson`).d('登记人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'registerPerson',
      bind: 'registerPersonLov.id',
    },
    {
      name: 'leadPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.leadPersonName`).d('跟进人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'leadPerson',
      bind: 'leadPersonLov.id',
    },
    {
      name: 'tempMeasureExeEnableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tempMeasureExeEnableFlag`).d('临时措施实施有效'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_ENABLE_FLAG',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'perpMeasureExeEnableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.perpMeasureExeEnableFlag`).d('长期措施实施有效'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_ENABLE_FLAG',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'rootReasonConfirmFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootReasonConfirmFlag`).d('根本原因确定'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.ROOT_REASON_CONFIRM_FLAG',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'influenceLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceLevel`).d('影响程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_INFLUENCE_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'registerTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.registerTimeFrom`).d('登记时间从'),
      max: 'registerTimeTo',
    },
    {
      name: 'registerTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.registerTimeTo`).d('登记时间至'),
      min: 'registerTimeFrom',
    },
    {
      name: 'proposePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.proposePersonName`).d('提出人'),
      ignore: FieldIgnore.always,
      textField: 'name',
      lovCode: 'YP.QIS.EMPLOYEE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'proposePerson',
      bind: 'proposePersonLov.employeeId',
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('主责人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'responsiblePerson',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsibleUnitLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleCompany`).d('主责科室'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      lovPara: { tenantId },
    },
    {
      name: 'unitId',
      bind: 'responsibleUnitLov.unitId',
    },
    {
      name: 'riskLight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.riskLight`).d('风险灯'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.RISK_LIGHT',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'problemApplyLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemApplyNum`).d('举手填报单'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS_PROBLEM_APPLY',
      lovPara: { tenantId },
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record.get('siteId'),
          queryFlag: 'Y',
        }),
      },
    },
    {
      name: 'problemRequestId',
      bind: 'problemApplyLov.problemApplyId',
    },
    {
      name: 'onlyForMeFalg',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onlyForMeFalg`).d('仅检索与我相关'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'onlyExtensionFalg',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onlyExtensionFalg`).d('是否延期'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROJECT_NAME',
    },
    {
      name: 'projectPhase',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectPhase`).d('项目阶段'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_PROJECT_PHASE',
    },
    {
      name: 'majorDivision1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision1`).d('主要区分1'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION1',
    },
    {
      name: 'majorDivision2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision2`).d('主要区分2'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION2',
    },
    {
      name: 'majorDivision3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision3`).d('主要区分3'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION3',
    },
  ],
  fields: [
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题标题'),
    },
    {
      name: 'riskLight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.riskLight`).d('风险灯'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.RISK_LIGHT',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'problemStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'problemCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDescription`).d('问题描述'),
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'influenceLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceLevel`).d('影响程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_INFLUENCE_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'proposePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposePersonName`).d('提出人'),
    },
    {
      name: 'proposePersonCompanyName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposePersonCompanyName`).d('提出部门'),
    },
    {
      name: 'proposeTimePeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposeTimePeriod`).d('提出时间'),
    },
    {
      name: 'registerPersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.registerPersonRealName`).d('登记人'),
    },
    {
      name: 'registerTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.registerTime`).d('登记时间'),
    },
    {
      name: 'leadPersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.leadPersonRealName`).d('跟进人'),
    },
    {
      name: 'solutionTool',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.solutionTool`).d('问题解决工具'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SOLUTION_TOOL',
      textField: 'meaning',
      valueField: 'value',
    },
    // {
    //   name: 'verificationLibraryFlag',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.verificationLibraryFlag`).d('纳入验证库'),
    // },
    {
      name: 'marketActEvaluationFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActEvaluationFlag`).d('生成市场活动评估'),
    },
    {
      name: 'tempMeasureExeEnableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tempMeasureExeEnableFlag`).d('临时措施实施有效'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_ENABLE_FLAG',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'perpMeasureExeEnableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.perpMeasureExeEnableFlag`).d('长期措施实施有效'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_ENABLE_FLAG',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'rootReasonConfirmFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootReasonConfirmFlag`).d('根本原因确定'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.ROOT_REASON_CONFIRM_FLAG',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'unitName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unitName`).d('主责科室'),
    },
    {
      name: 'responsiblePersonName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsiblePersonName`).d('主责人'),
    },
    {
      name: 'attribute1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.releasedTime`).d('下达时间'),
    },
    {
      name: 'attribute2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tempMeasureSubmitTime`).d('临时措施评价时间'),
    },
    {
      name: 'attribute3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.perpMeasureSubmitTime`).d('长期措施制定时间'),
    },
    {
      name: 'attribute4',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootMeasureSubmitTime`).d('根本原因评价时间'),
    },
    {
      name: 'problemApplyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemApplyCode`).d('举手填报单'),
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROJECT_NAME',
    },
    {
      name: 'projectPhase',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectPhase`).d('项目阶段'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_PROJECT_PHASE',
    },
    {
      name: 'majorDivision1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision1`).d('主要区分1'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION1',
    },
    {
      name: 'majorDivision2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision2`).d('主要区分2'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION2',
    },
    {
      name: 'majorDivision3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision3`).d('主要区分3'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION3',
    },
  ],
});

export { searchFormDS, searchTableDS };