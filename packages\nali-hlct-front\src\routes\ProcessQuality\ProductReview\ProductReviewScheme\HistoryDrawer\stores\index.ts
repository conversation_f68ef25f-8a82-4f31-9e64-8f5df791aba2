/**
 * @Description: 产品审核方案
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.ProductReviewScheme';

const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'productRevSchemeHisId',
  fields: [
    {
      name: 'productRevSchemeHisId', // id
    },
    {
      name: 'productRevSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeCode`).d('产品审核方案编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'productRevSchemeName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeName`).d('方案名称'),
    },
    {
      name: 'productRevSchemeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_SCHEME_STATUS',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工序编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序描述'),
    },
    {
      name: 'productDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productDate`).d('量产时间'),
    },
    {
      name: 'cusMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件编码'),
    },
    {
      name: 'cusMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialName`).d('客户零件描述'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('方案审批时间'),
    },
  ],
  transport: {
    read: ({data}) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-scheme/his-header/ui`,
        method: 'GET',
        data,
      };
    },
  },
});

export { headDS };

