/**
 * @Description: 检验方案维护-样式
 * @Author: <<EMAIL>>
 * @Date: 2023-01-19 11:13:24
 * @LastEditTime: 2023-02-10 17:08:53
 * @LastEditors: <<EMAIL>>
 */
.input-group-maintenance-value {
  .input-number {
    width: 100%;
    display: flex;

    :global {
      .number-label {
        width: 25px;
        height: 23px;
        margin-right: 10px;
        border: 1px solid #0003;
        background: #fff;
        color: #d9d9d9;
        text-align: center;
        padding: 0;
        margin-top: 3px;
      }
    }

    :global {
      .c7n-pro-radio-wrapper {
        margin-top: -6px;
        padding-left: 6px;
        padding-right: 5px;
        font-size: 18px;
        color: #333;

        .c7n-pro-radio-inner {
          visibility: hidden;
          position: relative;
          display: inline-block;
          vertical-align: middle;
          background-color: #fff;
          color: #fff;
          border: 0;
          border-radius: 50%;
          -webkit-transition: all 0.3s;
          transition: all 0.3s;
        }
      }
    }

    :global {
      .c7n-pro-input-number-wrapper {
        width: 112% !important;

        .c7n-pro-input-number {
          .c7n-pro-input-number-range-text {
            .c7n-pro-input-number-range-split {
              opacity: 0;
            }
          }

          .c7n-pro-input-number-range-text::after {
            content: ',';
            position: absolute;
            left: 50%;
            top: 18%;
          }
        }

        .c7n-pro-input-number-prefix {
          height: 26px;
          width: 26px;

          .IconFront {
            width: 26px;
            height: 28px;
            cursor: pointer;
            margin-top: 29px;
            padding-left: 10px;
            color: #29bece;
          }

          .IconDisFront {
            width: 26px;
            height: 28px;
            cursor: not-allowed;
            margin-top: 29px;
            padding-left: 10px;
            color: #d9d9d9;
          }
        }

        .c7n-pro-input-number-suffix {
          height: 26px;
          width: 26px;

          .IconFront {
            width: 26px;
            height: 28px;
            cursor: pointer;
            margin-top: 29px;
            padding-left: 10px;
            color: #29bece;
          }

          .IconDisFront {
            width: 26px;
            height: 28px;
            cursor: not-allowed;
            margin-top: 29px;
            padding-left: 10px;
            color: #d9d9d9;
          }
        }
      }

      .TipStyle {
        display: initial;
        margin-top: 28px;
        margin-left: 112px;
        position: absolute;
        color: #b5b5b5;
      }

      .addIcon {
        background-color: #29bece;
        color: #fff;
        border: 0.01rem solid #29bece;
        margin-top: 5px;
        width: 18px !important;
        height: 18px !important;
        margin-left: 8px;

        .icon-add {
          font-size: 14px;
          margin-left: -4px;
          margin-top: -2px;
        }
      }

      .c7n-btn-circle.c7n-btn-sm,
      .c7n-btn-circle-outline.c7n-btn-sm {
        width: 18px;
        height: 18px;
        padding: 0 2px 0 5px;
        font-size: 12px;
        border-radius: 50%;
      }

      .removeIcon {
        padding: 1;
        background-color: #defcff;
        color: #29bece;
        border: 0.01rem solid #29bece;
        margin-top: 5px;
        width: 18px !important;
        height: 18px !important;
        margin-left: 10px;

        .icon-remove {
          font-size: 14px;
          margin-left: -4px;
          margin-top: -2px;
        }

        .c7n-btn-circle.c7n-btn-sm,
        .c7n-btn-circle-outline.c7n-btn-sm {
          width: 18px;
          height: 18px;
          padding: 0 2px 0 5px;
          font-size: 12px;
          border-radius: 50%;
        }
      }
    }
  }

  .input-text {
    width: 100%;
    display: flex;

    :global {
      .c7n-pro-input-wrapper {
        width: 112% !important;
      }
    }

    :global {
      .addIcon {
        background-color: #29bece;
        color: #fff;
        border: 0.01rem solid #29bece;
        margin-top: 5px;
        width: 18px !important;
        height: 18px !important;
        margin-left: 8px;

        .icon-add {
          font-size: 14px;
          margin-left: -4px;
          margin-top: -2px;
        }
      }

      .c7n-btn-circle.c7n-btn-sm,
      .c7n-btn-circle-outline.c7n-btn-sm {
        width: 18px;
        height: 18px;
        padding: 0 2px 0 5px;
        font-size: 12px;
        border-radius: 50%;
      }

      .removeIcon {
        padding: 1;
        background-color: #defcff;
        color: #29bece;
        border: 0.01rem solid #29bece;
        margin-top: 5px;
        width: 18px !important;
        height: 18px !important;
        margin-left: 10px;

        .icon-remove {
          font-size: 14px;
          margin-left: -4px;
          margin-top: -2px;
        }

        .c7n-btn-circle.c7n-btn-sm,
        .c7n-btn-circle-outline.c7n-btn-sm {
          width: 18px;
          height: 18px;
          padding: 0 2px 0 5px;
          font-size: 12px;
          border-radius: 50%;
        }
      }
    }
  }

  .hcm-dataItem-group {
    display: flex;
    align-items: baseline;

    &-add-null {
      width: 27px;
      flex-shrink: 0;
      height: 25px;
      text-align: center;
      padding: 0;
      margin-top: 2px;
    }
  }

  .copy-radio {
    display: inline-block;
    width: 20px;
    text-align: center;
    font-size: 18px;
    line-height: 26px;
    cursor: pointer;
    color: #29bece;
  }

  .icon-add-text {
    position: relative;
    margin-left: 6px;
    color: #29bece;
    line-height: 28px;
  }

  .icon-right-box {
    display: flex;
    width: 50px;
    flex-shrink: 0;
    align-items: baseline;
  }
}

.checkBoxDisabled:hover {
  cursor: no-drop
}
