/**
 * @Description: 控制控制图-图形展示
 * @Author: <<EMAIL>>
 * @Date: 2021-11-24 17:27:42
 * @LastEditTime: 2022-11-08 15:29:45
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect } from 'react';
import { Spin } from 'choerodon-ui';
import { Content } from 'components/Page';
import { getDvaApp } from 'utils/iocUtils';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest, useInterval } from 'hcm-components-front/lib/components/tarzan-hooks';
import notification from 'utils/notification';
import request from 'utils/request';
import formatterCollections from 'utils/intl/formatterCollections';
import uuid from 'uuid/v4';
import intl from 'utils/intl';
import GraphicChart from '../components/GraphicChart';
import { FetchControlChartGraphicData } from '../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hspc.controlChartMaintain';
const tenantId = getCurrentOrganizationId();
let finalObj;
// let tempDataObj: any;
// let dataObj: any;
let indexFlag = false;

const ControlChartDisplay = props => {
  const { id } = props.match.params;

  const [chartUuid] = useState(uuid());
  const fetchControlChartGraphicData = useRequest(FetchControlChartGraphicData(), { manual: true });
  // const hideControl = useRequest(HideControl(), { manual: true });
  // const hideControlAll = useRequest(HideControlAll(), { manual: true });

  useEffect(() => {
    if (!id) {
      return;
    }
    getChartData(false);
  }, [id]);

  // useEffect(() => {
  //   if (!id) {
  //     return;
  //   }
  //   fetchControlChartGraphicData.run({
  //     params: {
  //       controlId: id,
  //     },
  //   });
  // }, [finalObj]);

  const getChartData = index => {
    if (index || index === 0) {
      if (index === 'hide') {
        // const paramObj = {
        //   controlId: Number(id),
        // };
        request(
          `/wr-tznl/v1/${tenantId}/hme-control-measure-record/show-hide-record/ui?controlId=${id}`,
          {
            method: 'POST',
            // params: {
            //   ...paramObj,
            // },
          },
        ).then(res => {
          if (res.success) {
            notification.success({});
            fetchControlChartGraphicData.run({
              params: {
                controlId: id,
              },
            });
          }
        });
        indexFlag = false;
      } else {
        indexFlag = true;
        const tempDataList = fetchControlChartGraphicData.data.data;
        const nowList = tempDataList?.filter(item => item.hideFlag !== 'Y');
        const nowObj = {
          data: {
            ...fetchControlChartGraphicData.data,
            data: nowList,
          },
        };
        const paramObj = {
          controlId: Number(id),
          subgroupIndex: nowObj.data.data[index].subgroupIndex,
        };
        request(
          `/wr-tznl/v1/${tenantId}/hme-control-measure-record/hide-record/ui?controlId=${id}&subgroupIndex=${paramObj.subgroupIndex}`,
          {
            method: 'POST',
            // params: {
            //   ...paramObj,
            // },
          },
        ).then(res => {
          if (res.success) {
            notification.success({});
            fetchControlChartGraphicData.run({
              params: {
                controlId: id,
              },
            });
          }
        });
      }
    } else {
      indexFlag = false;
      // tempDataObj = null;
      if (getDvaApp()._store.getState().global.activeTabKey !== props.match.url) {
        return;
      }
      fetchControlChartGraphicData.run({
        params: {
          controlId: id,
        },
      });
    }
  };

  // useInterval(() => {
  //   if (indexFlag === false) {
  //     getChartData(false);
  //   }
  // }, 10000);

  useInterval(getChartData, 10000);
  const filterList = fetchControlChartGraphicData?.data?.data;
  const finalList = filterList?.filter(item => item.hideFlag !== 'Y');
  finalObj = {
    data: {
      ...fetchControlChartGraphicData.data,
      data: finalList,
    },
  };
  return (
    <div className="hmes-style">
      <Spin spinning={fetchControlChartGraphicData.loading && !fetchControlChartGraphicData.data}>
        <Content style={{ height: '100%' }}>
          {fetchControlChartGraphicData?.data ? (
            <GraphicChart
              chartData={finalObj.data}
              chartUuid={chartUuid}
              getChangeData={getChartData}
              title={
                <div className={styles['chart-card-title']}>
                  <div className={styles['analys-code']}>
                    {`${intl
                      .get(`${modelPrompt}.analysisChartCode`)
                      .d('控制控制图编码')}: ${fetchControlChartGraphicData.data.controlCode ||
                      ''}`}
                  </div>
                  <div className={styles['analys-code']}>
                    {`${intl
                      .get(`${modelPrompt}.analysisChartDesc`)
                      .d('控制控制图描述')}: ${fetchControlChartGraphicData.data.controlDesc ||
                      ''}`}
                  </div>
                </div>
              }
            />
          ) : null}
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hspc.controlChartMaintain', 'tarzan.hspc.chartInfo', 'tarzan.common'],
})(ControlChartDisplay);
