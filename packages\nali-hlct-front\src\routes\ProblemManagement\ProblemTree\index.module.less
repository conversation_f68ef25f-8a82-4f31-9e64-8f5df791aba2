/**
 * RelationMaintain - 故障树-样式
 * @date: 2023-8-24
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

.btn {
  margin-left: 8px;
}

:global {
  .c7n-tree-show-line {
    .c7n-tree-list-holder-inner {
      > .tree-node {
        > .c7n-tree-indent {
          > .c7n-tree-indent-unit:nth-of-type(1) {
            opacity: 0;
          }
        }
      }
    }
  }
  .c7n-spin-nested-loading {
    flex-grow: 1;
  }

  .empty-node {
    opacity: 0;
    .c7n-tree-switcher,
    .c7n-tree-node-content-wrapper {
      cursor: default !important;
    }
  }
  .draggable-tree {
    .c7n-tree-title {
      line-height: 30px;
      &:hover {
        .tree-node {
          .tree-node-icon {
            display: inline-block;
          }
          .tree-node-icon-item {
            z-index: 999;
          }
        }
      }
      .tree-node {
        padding-left: 10px;
        display: inline-block;
        .tree-node-icon {
          display: none;
          .tree-node-icon-item {
            position: relative;
            top: -1px;
            padding: 0 4px;
            color: #394f51;
            &:hover {
              .node-info {
                display: block;
              }
            }
            .node-info {
              position: absolute;
              bottom: 100%;
              left: 6px;
              padding: 2px 0.5em;
              display: none;
              white-space: nowrap;
              background: #fff;
              border: 1px solid rgba(0, 0, 0, 0.1);
              text-align: center;
            }
          }
        }
        .c7n-tree-node-content-wrapper:hover {
          color: #29bece;
        }
      }
    }
  }
}
.loading-container {
  flex-grow: 1;
}

.modalControl {
  padding-bottom: 8px;
  display: flex;
  justify-content: flex-end;
  border-bottom: 1px solid rgb(240, 240, 240);
}

.dragDisableStyle{
  opacity: 0.3;
}
