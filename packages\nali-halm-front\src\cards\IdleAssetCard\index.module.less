.container {
  height: 100%;
  padding: 0 12px;
}
.label-container {
  position: absolute;
  top: 15px;
  left: 80px;
  display: grid;
  grid-gap: 7px;
  .label-item {
    display: grid;
    grid-gap: 8px;
    grid-template-columns: 100px 60px 1fr 20% 5px;
    width: 280px;
    color: #8f98aa;
    font-weight: 400;
    font-size: 12px;
    font-family: PingFangSC-Regular, serif;
    letter-spacing: 0;

    .value {
      color: #636569;
    }

    // 标签前面的虚线和圆点
    &::before {
      display: inline-block;
      height: 0;
      margin-top: 10px;
      border-top: 1px dashed;
      content: '';
    }

    &::after {
      display: inline-block;
      width: 4px;
      height: 4px;

      border-radius: 50%;
      transform: translate(-175px, 8px);
      content: '';
    }

    &-1::after {
      background-color: #5bdfdf;
    }
    &-2::after {
      background-color: #5bdfff;
    }
    &-3::after {
      background-color: #5bbeff;
    }
    &-4::after {
      background-color: #5b9dff;
    }
    &-5::after {
      background-color: #7c9dff;
    }
    &-6::after {
      background-color: #7ebfff;
    }
    &-7::after {
      background-color: #9edffe;
    }
  }
}

.customize-collapse {
  height: 100%;
  :global(.c7n-collapse-item) {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
  }
  :global(.c7n-collapse-content-box) {
    position: relative;
    top: 50%;
    left: 50%;
    width: 370px;
    padding-bottom: 0;
    transform: translate(-50%, -50%);
  }
  :global(.c7n-collapse-header) {
    padding: 12px 0 4px 8px !important;
    &::before {
      top: calc(50% - 0.07rem + 4px) !important;
    }
  }
}
