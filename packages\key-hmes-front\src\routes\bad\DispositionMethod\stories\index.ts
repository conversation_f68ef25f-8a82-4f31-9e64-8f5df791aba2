/**
 * @Description: 处置方式维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-02-07 14:00:2
 * @LastEditTime: 2023-03-08 14:17:38
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.bad.dispositionMethod.model';
const tenantId = getCurrentOrganizationId();

const TableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'dispositionFunctionId',
  queryFields: [
    {
      name: 'dispositionFunction',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispositionFunction`).d('处置方法名称'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('处置方法描述'),
    },
    {
      name: 'functionType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.functionType`).d('方法类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=FUNCTION_TYPE`,
      noCache: true,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'routerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.routerCode`).d('工艺路线'),
      lovCode: 'MT.METHOD.ROUTER_SITE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        routerTypes: 'NC',
      },
    },
    {
      name: 'routerId',
      type: FieldType.number,
      bind: 'routerLov.routerId',
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.initialFlag').d('初始化'),
      lookupCode: 'MT.INITIAL_FLAG',
    },
  ],
  fields: [
    {
      name: 'dispositionFunction',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispositionFunction`).d('处置方法名称'),
      required: true,
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.description`).d('处置方法描述'),
      required: true,
    },
    {
      name: 'functionType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.functionType`).d('方法类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=FUNCTION_TYPE`,
      noCache: true,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'routerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.routerCode`).d('工艺路线'),
      lovCode: 'MT.METHOD.ROUTER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => ({
          tenantId,
          routerTypes: 'NC',
        }),
        disabled: ({ record }) => !record || record?.get('functionType') !== 'REWORK_ROUTER',
      },
    },
    {
      name: 'routerId',
      type: FieldType.number,
      bind: 'routerLov.routerId',
    },
    {
      name: 'routerName',
      type: FieldType.string,
      bind: 'routerLov.routerName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.initialFlag').d('初始化'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.INITIAL_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      disabled: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-disposition-function/query/ui`,
        method: 'POST',
      };
    },
    destroy: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-disposition-function/delete/ui`,
        method: 'POST',
        data: data.map(item => item.dispositionFunctionId),
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.method.domain.entity.MtDispositionFunction';
      return {
        data: { dispositionFunctionId: record.get('dispositionFunctionId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_METHOD}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  events: {
    update: ({ record, name, value, oldValue }) => {
      if (name === 'functionType' && value !== oldValue && oldValue === 'REWORK_ROUTER') {
        record.set('routerLov', null);
      }
    },
  },
});

export { TableDS };
