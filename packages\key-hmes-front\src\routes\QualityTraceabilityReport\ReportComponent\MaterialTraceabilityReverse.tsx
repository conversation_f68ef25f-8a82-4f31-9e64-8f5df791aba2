import React, { useMemo, useCallback } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { useDataSetEvent } from 'utils/hooks';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import {  ColumnLock, TableMode, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores/MaterialTraceabilityReverseDS';

const modelPrompt = 'modelPrompt_code';

const MaterialTraceabilityReverse = () => {

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const queryValidate = ({ dataSet }) => {
    const _record = dataSet.queryDataSet.current;
    if (
      !_record.get('bindDateFrom') &&
      !_record.get('bindDateTo') &&
      !_record.get('workcellId') &&
      !_record.get('parentMaterialId') &&
      !_record.get('childMaterialId') &&
      !_record.get('materialLotCode') &&
      !_record.get('lot')
    ) {
      notification.error({
        message: '除站点外，请至少输入一个查询条件！',
      });
      return false
    }
  }

  useDataSetEvent(tableDs, 'query', queryValidate);

  const columns: ColumnProps[] = useMemo(
    () => [
      { name: 'materialCode', width: 240, lock: ColumnLock.left },
      { name: 'materialName', width: 150 },
      { name: 'componentTypeDesc', width: 150 },
      {
        name: 'materialLotCode',
        width: 150,
        renderer: ({ value, record }) => {
          if (record!.get('identifyType') === 'MATERIAL_LOT') {
            return value
          }
          return '';
        },
      },
      {
        name: 'lot',
        width: 150,
        renderer: ({ value, record }) => {
          if (record!.get('identifyType') === 'LOT') {
            return value
          }
          return '';
        },
      },
      { name: 'sumAssembleQty', width: 150 },
      { name: 'workcellCode', width: 150 },
      { name: 'equipmentCode', width: 150 },
      { name: 'equipmentName', width: 150 },
      { name: 'bindDate', width: 150 },
      { name: 'overStationDate', width: 150 },
      { name: 'overStationByName', width: 150 },
    ],
    [],
  );

  const nodeCover = useCallback(
    ({ record }) => {
      const nodeProps = { isLeaf: false };
      if (!record.get('childMaterialDtlList')?.length) {
        nodeProps.isLeaf = true;
      }
      return nodeProps;
    },
    [],
  )

  return (
    <div className="hmes-style">
      <Table
        mode={TableMode.tree}
        queryBar={TableQueryBarType.filterBar}
        queryBarProps={{
          fuzzyQuery: false,
          onQuery: () => false,
        }}
        queryFieldsLimit={8}
        dataSet={tableDs}
        columns={columns}
        onRow={nodeCover}
        defaultRowExpanded
      />
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(MaterialTraceabilityReverse);
