/**
 * @feature 新数据收集项维护
 * @date 2021-4-13
 * <AUTHOR> <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { Table, DataSet, Button, Switch } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExport';
import { observer } from 'mobx-react';
import { Button as PermissionButton } from 'components/Permission';
import { Badge } from 'hzero-ui';
import notification from 'utils/notification';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { getCurrentOrganizationId } from 'utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import { entranceDS } from './stories/EntranceDs';

const entrance = observer(props => {
  const [canEdit, setCanEdit] = useState(false)
  const {
    match: { path },
    customizeTable,
  } = props;

  useDataSetEvent(props.dataSet, 'query', () => {
    setCanEdit(false);
  });

  const columns = [
    {
      name: 'tagCode',
      align: 'left',
      width: 150,
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => {
              const id = record.data.tagId;
              props.history.push(`/hmes/acquisition/new-data-item-new/detail/${id}`);
            }}
          >
            {record.data.tagCode}
          </a>
        );
      },
    },
    {
      name: 'tagDescription',
      align: 'left',
      width: 200,
    },
    {
      name: 'remark',
      width: 200,
    },
    {
      name: 'collectionMethod',
      width: 130,
    },
    {
      name: 'enableFlag',
      align: 'center',
      width: 100,
      editor: record => record.getState('editing') && <Switch />,
      renderer: ({ record }) => (
        <Badge
          status={record.get('enableFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('enableFlag') === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'displayValueFlag',
      align: 'center',
      renderer: ({ record }) => (
        <Badge
          status={record.get('displayValueFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('displayValueFlag') === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
      width: 130,
    },
    {
      name: 'allowUpdateFlag',
      align: 'center',
      renderer: ({ record }) => (
        <Badge
          status={record.get('allowUpdateFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('allowUpdateFlag') === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
      width: 130,
    },
    {
      name: 'valueAllowMissing',
      align: 'center',
      renderer: ({ record }) => (
        <Badge
          status={record.get('valueAllowMissing') === 'Y' ? 'success' : 'error'}
          text={
            record.get('valueAllowMissing') === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
      width: 130,
    },
    { name: 'valueType'},
    { name: 'trueValue',
      renderer: ({ value, record }) => {
        if(record.get('valueType') === 'VALUE'){
          if(record.get('trueValueList')&&record.get('trueValueList').length){
            const temp = record.get('trueValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue&&<Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      }},
    { name: 'falseValue',
      renderer: ({ value, record }) => {
        if(record.get('valueType') === 'VALUE'){
          if(record.get('falseValueList')&&record.get('falseValueList').length){
            const temp = record.get('falseValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue&&<Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    // { name: 'minimumValue', align: 'right' },
    // { name: 'maximalValue', align: 'right' },
    { name: 'uomCode' },
    {
      name: 'valueList',
      width: 200,
      renderer: ({ record }) => {
        if (record.data.valueList) {
          const list = record.data.valueList.split(',');
          return (
            list.length &&
            list.map(item => {
              return (
                <Tag color="blue" key={item}>
                  {item}
                </Tag>
              );
            })
          );
        }
      },
    },
    // { name: 'mandatoryNum', align: 'right', width: 150 },
    // { name: 'optionalNum', align: 'right', width: 150 },
    { name: 'dateFormat', align: 'left', width: 150 },
    {
      name: 'specialRecordFlag',
      align: 'center',
      width: 150,
      renderer: ({ record }) => (
        <Badge
          status={record.get('specialRecordFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('specialRecordFlag') === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    { name: 'defaultNcCode', align: 'left', width: 150 },
    { name: 'originOperationName', align: 'left', width: 150 },
    {
      name: 'modifyFlag',
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
  ];

  const goDetail = () => {
    props.history.push(`/hmes/acquisition/new-data-item-new/detail/create`);
  };

  useEffect(() => {
    props.dataSet.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.TAG_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.TAG_LIST.LIST`);
    props.dataSet.query(props.dataSet.currentPage);
  }, []);

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MT.MES.TAG_ITEM`,
      title: intl.get('tarzan.hmes.acquisition.dataItem.import').d('数据收集项数据导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }
  const handleEdit = () => {
    setCanEdit(true);
    props.dataSet.records.forEach(item => item?.setState('editing', true))
  }

  const handleSave = async () => {
    const res = await props.dataSet.submit();
    if(res&&res.success){
      notification.success();
      await props.dataSet.query(props.dataSet.currentPage);
    }
  }
  const handleCancel = () => {
    props.dataSet.query(props.dataSet.currentPage);
  }
  const getExportQueryParams = () => {
    return {
      mtTagIds: props.dataSet.selected.map(item => item.data.tagId),
    }
  }

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get('tarzan.acquisition.dataItem.title.list').d('数据收集项维护')}
        header={
          <>

            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="add"
              onClick={goDetail}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.create').d('新建')}
            </PermissionButton>
            <Button color='primary' icon="daorucanshu" onClick={handleImport}>{intl.get('tarzan.common.button.import').d('导入')}</Button>
            <ExcelExport
              method="GET"
              requestUrl={`${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-tag/export/tag`}
              queryParams={getExportQueryParams}
              otherButtonProps={{disabled: props.dataSet.selected.length===0}}
              buttonText={intl.get(`tarzan.acquisition.dataItem..export`).d('导出')}
            />
            {
              canEdit?(
                <>
                  <Button color='primary' onClick={handleSave}>{intl.get('tarzan.common.button.save').d('保存')}</Button>
                  <Button onClick={handleCancel}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
                </>
              ):(
                <Button color='primary' icon="edit-o" onClick={handleEdit}>{intl.get('tarzan.common.button.edit').d('编辑')}</Button>
              )
            }

          </>
        }
      >
        <div className="tarzan-ui-remove">
          {customizeTable(
            {
              filterCode: `${BASIC.CUSZ_CODE_BEFORE}.TAG_LIST.QUERY`,
              code: `${BASIC.CUSZ_CODE_BEFORE}.TAG_LIST.LIST`,
            },
            <Table
              searchCode="sjsjxwh1"
              customizedCode="sjsjxwh1"
              queryBar="filterBar"
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={props.dataSet}
              columns={columns}
            />,
          )}
        </div>
      </PageHeaderWrapper>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.acquisition.dataItem', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...entranceDS(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.TAG_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.TAG_LIST.LIST`],
      // @ts-ignore
    })(entrance),
  ),
);
