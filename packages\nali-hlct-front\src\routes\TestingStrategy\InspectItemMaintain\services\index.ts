/**
 * @Description: 检验项目维护-service
 * @Author: <EMAIL>
 * @Date: 2023/1/9 16:37
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存检验项目
 * @function SaveMaterialPlan
 * @returns {object} fetch Promise
 */
export function SaveInspectItem(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-item/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_DETAIL.BASIC`,
    method: 'POST',
  };
}
