/**
 * @Description: 检验方案-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-05-18 16:38:12
 * @LastEditors: <<EMAIL>>

*/
import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Form, Lov, Modal, Table } from 'choerodon-ui/pro';
import { Badge, Collapse, Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import { openTab } from 'utils/menuTab';
import { drawerPropsC7n } from '@components/tarzan-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import notification from 'utils/notification';
import queryString from 'querystring';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { copyDS, listTableDS } from '../stores/InspectionSchemeDS';
import {
  dimensionTableDS,
  inspectBusTypeDS,
  inspectItemDS,
  inspectSchemeDS,
} from '../stores/HistoryDS';

import { inspectSchemeChangePublishConfig, inspectSchemePublishConfig } from '../services';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inspectionScheme';
const { Panel } = Collapse;

const InspectionSchemeList = props => {
  const {
    tableDs,
    copyDs,
    match: { path },
  } = props;

  // 检验方案发布
  const inspectSchemePublish = useRequest(inspectSchemePublishConfig(), {
    manual: true,
    needPromise: true,
  });
  // 检验方案发布确认
  const inspectSchemeChangePublish = useRequest(inspectSchemeChangePublishConfig(), {
    manual: true,
    needPromise: true,
  });

  const [selectedRecords, setSelectedRecords] = useState([]);
  const inspectItemDs = useMemo(() => new DataSet(inspectItemDS()), []);
  const dimensionTableDs = useMemo(
    () =>
      new DataSet({
        ...dimensionTableDS(),
        children: { items: inspectItemDs },
      }),
    [],
  );
  const inspectBusTypeDs = useMemo(
    () =>
      new DataSet({
        ...inspectBusTypeDS(),
        children: { dimensions: dimensionTableDs },
      }),
    [],
  );
  const inspectSchemeDs = useMemo(
    () =>
      new DataSet({
        ...inspectSchemeDS(),
        children: { inspectSchemeLines: inspectBusTypeDs },
      }),
    [],
  );

  useEffect(() => {
    tableDs.setQueryParameter(
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME.TABLE`,
    );
    if (tableDs?.currentPage) {
      tableDs.query(props.tableDs.currentPage);
    } else {
      tableDs.query();
    }
  }, []);

  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteObject':
        record.set('inspectSchemeObject', null);
        record.set('areaObject', null);
        record.set('prodLineObject', null);
        record.set('processObject', null);
        record.set('stationObject', null);
        record.set('inspectBusinessTypeObject', null);
        break;
      case 'inspectSchemeObjectTypeObject':
        record.set('inspectSchemeObject', null);
        record.set('revisionCode', null);
        break;
      default:
        break;
    }
  });

  useDataSetEvent(tableDs, 'batchSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  useDataSetEvent(tableDs, 'batchUnSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  const inspectSchemeColumns: ColumnProps[] = [
    {
      name: 'lastUpdateDate',
      width: 150,
      align: ColumnAlign.center,
      lock: ColumnLock.left,
    },
    {
      name: 'lastUpdatedByName',
      lock: ColumnLock.left,
      width: 150,
    },
    { name: 'operationTypeDesc' },
    {
      name: 'inspectSchemeCode',
      lock: ColumnLock.left,
      width: 150,
    },
    {
      name: 'siteName',
      minWidth: 160,
    },
    {
      name: 'currentFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.yes`).d('是')
              : intl?.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'inspectSchemeObjectTypeDesc',
      minWidth: 160,
    },
    {
      name: 'inspectSchemeObjectName',
      minWidth: 160,
    },
    {
      name: 'revisionCode',
      minWidth: 160,
    },
    {
      name: 'enableFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.enable`).d('启用')
              : intl?.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'statusDesc',
      renderer: ({ value, record }) => (
        <Tag color={record?.get('status') === 'UNPUBLISHED' ? 'gray' : 'green'}>{value}</Tag>
      ),
    },
    {
      name: 'documentNum',
    },
    { name: 'documentRevision' },
    { name: 'controlledNum' },
    {
      name: 'remark',
      minWidth: 220,
    },
    { name: 'creationDate', width: 150, align: ColumnAlign.center },
    { name: 'createdByName' },
    { name: 'enclosure', lock: ColumnLock.right },
  ];

  const inspectBusTypeColumns: ColumnProps[] = [
    {
      name: 'inspectBusinessTypeDesc',
    },
    {
      name: 'samplingDimension',
    },
    {
      name: 'samplingMethodDesc',
    },
  ];

  const dimensionTableColumns: ColumnProps[] = [
    {
      name: 'sequence',
      align: ColumnAlign.left,
    },
    { name: 'materialName' },
    { name: 'revisionCode' },
    { name: 'materialCategoryName' },
    { name: 'areaName' },
    { name: 'prodLineName' },
    { name: 'processWorkcellName' },
    { name: 'stationWorkcellName' },
    { name: 'equipmentName' },
    { name: 'operationName' },
    { name: 'supplierName' },
    { name: 'customerName' },
    { name: 'otherObject' },
  ];

  const getDataValueShow = (record, name) => {
    const _dataType = record?.get('dataType');
    const _valueData = record?.get(name) || [];
    const _dataShow = _valueData.length > 0 ? _valueData[0].dataValue : '';
    return ['CALCULATE_FORMULA', 'VALUE', 'VALUE_LIST'].includes(_dataType)
      ? _valueData.map(item => <Tag>{item.dataValue}</Tag>)
      : _dataShow;
  };

  const inspectItemColumns: ColumnProps[] = [
    { name: 'inspectItemCode', width: 150, lock: ColumnLock.left },
    {
      name: 'inspectItemDesc',
      width: 180,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectBasis',
    },
    {
      name: 'qualityCharacteristic',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectTool',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectMethod',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'requiredFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.yes`).d('是')
              : intl?.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'enableFlag',
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.enable`).d('启用')
              : intl?.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'technicalRequirement',
    },
    {
      name: 'remark',
    },
    {
      name: 'enterMethod',
      align: ColumnAlign.center,
    },
    {
      name: 'dataType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'uomName',
      align: ColumnAlign.center,
    },
    {
      name: 'decimalNumber',
    },
    {
      name: 'processMode',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'processModeDesc',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'valueLists',
      width: 200,
      renderer: ({ value }) => {
        if (value) {
          return (
            value?.length &&
            value.map(item => {
              return (
                <Tag className="hcm-tag-blue" key={item}>
                  {item}
                </Tag>
              );
            })
          );
        }
      },
    },

    {
      name: 'trueValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'trueValueList'),
    },
    {
      name: 'falseValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'falseValueList'),
    },
    {
      name: 'warningValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'warningValueList'),
    },
    {
      name: 'dataQty',
    },
    {
      name: 'samplingMethodDesc',
      align: ColumnAlign.center,
    },
    {
      name: 'ncCodeGroupDesc',
      align: ColumnAlign.center,
    },
    {
      name: 'employeePosition',
      width: 120,
    },
    {
      name: 'inspectFrequency',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
        if (inspectFrequencyShow) {
          inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
          inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
          return inspectFrequencyShow;
        }
        return value;
      },
    },
    {
      name: 'actionItem',
    },
    {
      name: 'sameGroupIdentification',
    },
    {
      name: 'destructiveExperimentFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.yes`).d('是')
              : intl?.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'outsourceFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.yes`).d('是')
              : intl?.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'enclosure',
      lock: ColumnLock.right,
    },
  ];

  const handleOpenHistoryDrawer = record => {
    inspectSchemeDs.loadData([]);
    inspectSchemeDs.setQueryParameter('inspectSchemeId', record?.get('inspectSchemeId'));
    inspectSchemeDs.query();

    Modal.open({
      ...drawerPropsC7n({
        ds: inspectSchemeDs,
        canEdit: false,
      }),
      title: intl?.get(`${modelPrompt}.title.queryHistory`).d('历史查询'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      afterClose: () => inspectSchemeDs.loadData([]),
      children: (
        <>
          <Table
            highLightRow
            dataSet={inspectSchemeDs}
            columns={inspectSchemeColumns}
            customizedCode="inspectScheme-drawer"
          />
          <Collapse
            bordered={false}
            defaultActiveKey={['inspectBusList', 'dimension', 'inspectItem']}
          >
            <Panel
              key="inspectBusList"
              header={intl?.get(`${modelPrompt}.title.inspectBusList`).d('检验业务类型列表')}
            >
              <Table
                highLightRow
                dataSet={inspectBusTypeDs}
                columns={inspectBusTypeColumns}
                customizedCode="inspectScheme-inspectBusList"
              />
            </Panel>
            <Panel
              key="dimension"
              header={intl?.get(`${modelPrompt}.title.dimension`).d('检验维度列表')}
            >
              <Table
                highLightRow
                dataSet={dimensionTableDs}
                columns={dimensionTableColumns}
                customizedCode="inspectScheme-dimension"
              />
            </Panel>
            <Panel
              key="inspectItem"
              header={intl?.get(`${modelPrompt}.title.inspectItem`).d('检验项目列表')}
            >
              <Table
                dataSet={inspectItemDs}
                columns={inspectItemColumns}
                customizedCode="inspectScheme-inspectItem"
              />
            </Panel>
          </Collapse>
        </>
      ),
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'inspectSchemeCode',
      lock: ColumnLock.left,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
      minWidth: 160,
    },
    {
      name: 'siteName',
      minWidth: 160,
    },
    {
      name: 'inspectSchemeObjectTypeDesc',
      minWidth: 160,
    },
    {
      name: 'inspectSchemeObjectName',
      minWidth: 160,
    },
    {
      name: 'inspectSchemeObjectCode',
      minWidth: 160,
    },
    {
      name: 'inspectBusinessTypeDesc',
      minWidth: 220,
      renderer: ({ value }) => (value || []).map(item => <Tag color="blue">{item}</Tag>),
    },
    {
      name: 'statusDesc',
      renderer: ({ value, record }) => (
        <Tag color={record?.get('status') === 'UNPUBLISHED' ? 'gray' : 'green'}>{value}</Tag>
      ),
    },
    {
      name: 'currentFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.yes`).d('是')
              : intl?.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'manufactureFlag',
      minWidth: 160,
    },
    {
      name: 'inspectSchemeTmpCode',
      minWidth: 160,
    },
    {
      name: 'documentNum',
    },
    {
      name: 'documentRevision',
    },
    {
      name: 'controlledNum',
    },
    {
      name: 'enableFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.enable`).d('启用')
              : intl?.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'remark',
      minWidth: 220,
    },
    {
      name: 'schemeCategory',
      width: 160,
    },
    {
      header: intl?.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      width: 100,
      renderer: ({ record }) => (
        <PermissionButton
          type="text"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          onClick={() => handleOpenHistoryDrawer(record)}
        >
          {intl?.get(`${modelPrompt}.operation.history`).d('历史查询')}
        </PermissionButton>
      ),
    },
  ];

  const handleEdit = record => {
    props.history.push(
      `/hwms/inspection-scheme-maintenance/detail/${record?.get('inspectSchemeId')}`,
    );
  };

  const handleCreate = () => {
    props.history.push(`/hwms/inspection-scheme-maintenance/detail/create`);
  };

  const handleRelease = async () => {
    const inspectSchemePublishRes = await inspectSchemePublish.run({
      params: selectedRecords.map((record: any) => {
        return record?.get('inspectSchemeId');
      }),
      onFailed: () => {
        return Promise.resolve(null);
      },
    });
    if (inspectSchemePublishRes?.success) {
      if (inspectSchemePublishRes?.rows?.message) {
        return Modal.confirm({
          title: intl?.get(`tarzan.common.title.tips`).d('提示'),
          children: <div>{inspectSchemePublishRes?.rows?.message}</div>,
        }).then(button => {
          if (button === 'ok') {
            return inspectSchemeChangePublish.run({
              params: {
                inspectSchemeInfo: inspectSchemePublishRes?.rows?.inspectSchemeInfo,
              },
              onSuccess: () => {
                // @ts-ignore
                notification.success();
                tableDs.batchUnSelect(selectedRecords);
                tableDs.query(tableDs.currentPage);
              },
            });
          }
          return Promise.resolve(null);
        });
      }
      // @ts-ignore
      notification.success();
      tableDs.batchUnSelect(selectedRecords);
      tableDs.query(tableDs.currentPage);
      return Promise.resolve(null);
    }
    return Promise.resolve(null);
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/MT.QMS.INSPECT_SCHEME',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const handleCopy = () => {
    // if (selectedRecords && selectedRecords.length === 1) {
    //   const searchData = {
    //     // @ts-ignore
    //     inspectSchemeId: selectedRecords[0]?.get('inspectSchemeId'),
    //     // @ts-ignore
    //     inspectSchemeCode: selectedRecords[0]?.get('inspectSchemeCode'),
    //   };
    //   copyDs.loadData([searchData]);
    // }
    Modal.open({
      key: Modal.key(),
      title: intl?.get(`${modelPrompt}.copyInspection`).d('复制检验方案'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onClose: copyReset,
      onOk: copyConfirm,
      children: (
        <Form dataSet={copyDs} columns={1}>
          <Lov name="inspectSchemeObject" />
          <Lov name="inspectSchemeTmpObject" />
        </Form>
      ),
    });
  };

  const copyReset = () => {
    copyDs.reset();
  };

  const copyConfirm = async () => {
    const formValidate = await copyDs.validate();
    if (formValidate) {
      if (copyDs.current?.get('inspectSchemeId')) {
        props.history.push(
          `/hwms/inspection-scheme-maintenance/detail/${copyDs.current?.get('inspectSchemeId')}copy`,
        );
        return Promise.resolve(true);
      }
      if (copyDs.current?.get('inspectSchemeTmpId')) {
        props.history.push(
          `/hwms/inspection-scheme-maintenance/detail/${copyDs.current?.get(
            'inspectSchemeTmpId',
          )}copyTmp`,
        );
        return Promise.resolve(true);
      }
      return Promise.resolve(true);
    }
    return Promise.resolve(false);
  };

  return (
    <div className="hmes-style">
      <Header title={intl?.get(`${modelPrompt}.InspectionSchemeMaintenance`).d('检验方案维护')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleCreate()}
        >
          {intl?.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `list.button.release`,
              type: 'button',
              meaning: '列表页-发布按钮',
            },
          ]}
          icon="send-o"
          disabled={selectedRecords.length === 0}
          onClick={handleRelease}
        >
          {intl?.get(`${modelPrompt}.button.release`).d('发布')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          icon="content_copy"
          onClick={handleCopy}
        >
          {intl?.get(`${modelPrompt}.button.copy`).d('复制')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl?.get(`tarzan.common.button.import`).d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          searchCode="jyfawh1"
          customizedCode="jyfawh1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            queryFieldsLimit: 8,
          }}
          filter={record => {
            return record?.get('dataType') !== 'CALCULATE_FORMULA';
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inspectionScheme', 'tarzan.common', 'tarzan.qms.inspectGroupMaintenance'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...listTableDS(),
      });
      const copyDs = new DataSet({
        ...copyDS(),
      });
      return {
        tableDs,
        copyDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME.TABLE`,
      ],
    })(InspectionSchemeList as any),
  ),
);
