/*
 * @Description: 问题积分表-services
 * @Author: <<EMAIL>>
 * @Date: 2023-11-30 13:47:56
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-11-30 18:53:04
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存
 * @function SaveProblemScore
 * @returns {object} fetch Promise
 */
export function SaveProblemScore(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem-scores/edit/save`,
    method: 'POST',
  };
}
