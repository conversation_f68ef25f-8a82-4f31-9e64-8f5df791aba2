/**
 * @Description: 问题横展平台-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-05-18 16:38:12
 * @LastEditors: <<EMAIL>>

*/
import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import withProps from 'utils/withProps';
import { listTableDS } from '../stores/TransverseExpandDS';

// const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.transverseExpand';

const InspectionSchemeList = props => {
  const { tableDs } = props;

  useEffect(() => {
    if (tableDs?.currentPage) {
      tableDs.query(props.tableDs.currentPage);
    } else {
      tableDs.query();
    }
  }, []);

  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteObject':
        record.set('inspectSchemeObject', null);
        record.set('areaObject', null);
        record.set('prodLineObject', null);
        record.set('processObject', null);
        record.set('stationObject', null);
        break;
      case 'inspectSchemeObjectTypeObject':
        record.set('inspectSchemeObject', null);
        record.set('revisionCode', null);
        break;
      default:
        break;
    }
  });

  const columns: ColumnProps[] = [
    {
      name: 'spreadListCode',
      lock: ColumnLock.left,
      width: 260,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'statusDesc',
    },
    {
      name: 'createMethodDesc',
    },
    {
      name: 'sourceProblemNum',
    },
    {
      name: 'severityLevel',
    },
    {
      name: 'registUserLov',
    },
    {
      name: 'registUserDapartmentName',
    },
    {
      name: 'registDate',
    },
    {
      name: 'siteName',
    },
    {
      name: 'prodLineName',
    },
    {
      name: 'leadUserDapartmentName',
    },
    {
      name: 'leadUserName',
    },
    // {
    //   name: 'registDateFrom',
    // },
    // {
    //   name: 'registDateTo',
    // },
    {
      name: 'followUpUserDapartmentName',
    },
    {
      name: 'followUpUserName',
    },
    {
      name: 'productLevel',
    },
    {
      name: 'spreadListTypeDesc',
    },
    {
      name: 'enclosure',
    },
  ];

  const handleEdit = record => {
    props.history.push(
      `/hwms/problem-management/transverse-expand/detail/${record.get('problemSpreadListId')}`,
    );
  };

  const handleCreate = () => {
    props.history.push(`/hwms/problem-management/transverse-expand/detail/create`);
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.InspectionSchemeMaintenance`).d('问题横展平台')}>
        <PermissionButton
          type="c7n-pro"
          // permissionList={[
          //   {
          //     code: `${path}.button.edit`,
          //     type: 'button',
          //     meaning: '列表页-编辑新建删除复制按钮',
          //   },
          // ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleCreate()}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          searchCode="hzwt1"
          customizedCode="hzwt1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.transverseExpand', 'tarzan.common', 'tarzan.qms.inspectGroupMaintenance'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...listTableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(InspectionSchemeList),
);
