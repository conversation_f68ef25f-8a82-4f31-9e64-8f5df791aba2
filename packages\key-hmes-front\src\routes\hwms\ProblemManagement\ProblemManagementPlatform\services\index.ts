/**
 * @Description: 问题管理平台-service
 * @Author: <EMAIL>
 * @Date: 2023/7/3 16:42
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
// const BASIC = {
//   TARZAN_SAMPLING: '/tznq-24175',
// };

/**
 * 用户信息Lov
 * @function QueryUserInfo
 * @returns {object} fetch Promise
 */
export function QueryUserInfo(): object {
  return {
    url: `/hpfm/v1/${tenantId}/lovs/sql/data`,
    method: 'GET',
  };
}

/**
 * 查询当前用户的员工信息
 * @function QueryUserInfo
 * @returns {object} fetch Promise
 */
export function QueryUserEmployeeInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/unit-position/by-user`,
    method: 'GET',
  };
}

/**
 * srm附件查询
 * @function QuerySrmEvidence
 * @returns {object} fetch Promise
 */
export function QuerySrmEvidence(): object {
  return {
    url: `http://***********:30080/hitf/v2p/public/rest/invoke/SFpFUk86RVNCX1RPX1NSTTpMSVNUX0ZJTEVTX0JZX1VVSUQ=`,
    // url: `/hfle/v1/${getCurrentOrganizationId()}/files/4d241e58f016b4ee09884ac45eaaa3bd6/file`,
    method: 'GET',
  };
}

/**
 * 员工信息Lov
 * @function QueryEmployeeInfo
 * @returns {object} fetch Promise
 */
export function QueryEmployeeInfo(): object {
  return {
    url: `/hpfm/v1/${tenantId}/employee-users/employee/batch`,
    method: 'GET',
  };
}

/**
 * 新建问题
 * @function SaveProblem
 * @returns {object} fetch Promise
 */
export function SaveProblem(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/base-save/ui`,
    method: 'POST',
  };
}

/**
 * 基于零部件信息新建问题
 * @function SaveProblem
 * @returns {object} fetch Promise
 */
export function SaveProblemByJudgement(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prianal-resjudg/save-problem/ui`,
    method: 'POST',
  };
}

/**
 * 基于分层审核任务项目明细新建问题
 * @function SaveProblemByLayerTskdtlItem
 * @returns {object} fetch Promise
 */
export function SaveProblemByLayerTskdtlItem(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-layer-revtsk-dtl/save-problem/ui`,
    method: 'POST',
  };
}

/**
 * 保存问题-具体信息
 * @function CancelInspectDoc
 * @returns {object} fetch Promise
 */
export function SaveProblemInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/category-save/ui`,
    method: 'POST',
  };
}

/**
 * 保存跟进人填写内容
 * @function CancelInspectDoc
 * @returns {object} fetch Promise
 */
export function SaveLeadAndResponsible(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/register-responsible/save/ui`,
    method: 'POST',
  };
}

/**
 * 第一步骤、第二步骤-提交
 * @function CancelInspectDoc
 * @returns {object} fetch Promise
 */
export function SubmitProblemInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/submit/ui`,
    method: 'POST',
  };
}

/**
 * 保存下次报告日期
 * @function SaveNextReportDate
 * @returns {object} fetch Promise
 */
export function SaveNextReportDate(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/next-report-date-save/ui`,
    method: 'POST',
  };
}

/**
 * 责任供应商-保存
 * @function SaveDutySupplier
 * @returns {object} fetch Promise
 */
export function SaveDutySupplier(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason-supplier/save/ui`,
    method: 'POST',
  };
}

/**
 * 临时措施、长期措施保存（责任人）
 * @function SaveMeasureInfo
 * @returns {object} fetch Promise
 */
export function SaveMeasureInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/measure/save/ui`,
    method: 'POST',
  };
}

/**
 * 临时措施、长期措施提交（责任人）
 * @function SubmitMeasureInfo
 * @returns {object} fetch Promise
 */
export function SubmitMeasureInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/measure/submit/ui`,
    method: 'POST',
  };
}

/**
 * 临时措施、长期措施保存（跟进人）
 * @function SaveMeasureEnable
 * @returns {object} fetch Promise
 */
export function SaveMeasureEnable(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/measure/lead-save/ui`,
    method: 'POST',
  };
}

/**
 * 临时措施、长期措施提交（跟进人）
 * @function SubmitMeasureEnable
 * @returns {object} fetch Promise
 */
export function SubmitMeasureEnable(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/measure/lead-submit/ui`,
    method: 'POST',
  };
}

/**
 * 临时措施、长期措施是否长期有效性提交（跟进人）
 * @function SubmitTotalEnable
 * @returns {object} fetch Promise
 */
export function SubmitTotalEnable(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/measure/measure-exe-enable/lead-submit/ui`,
    method: 'POST',
  };
}

/**
 * 变更对象保存
 * @function SaveChangeObject
 * @returns {object} fetch Promise
 */
export function SaveChangeObject(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/change-object/save/ui`,
    method: 'POST',
  };
}

/**
 * 8D报告保存
 * @function saveEnclosure8d
 * @returns {object} fetch Promise
 */
export function SaveEnclosure8d(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/measure/8d/save/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析保存（责任人）
 * @function SaveReasonInfo
 * @returns {object} fetch Promise
 */
export function SaveReasonInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason/save/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析提交（责任人）
 * @function SubmitReasonInfo
 * @returns {object} fetch Promise
 */
export function SubmitReasonInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason/submit/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析保存（跟进人）
 * @function SaveReasonEnable
 * @returns {object} fetch Promise
 */
export function SaveReasonEnable(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason/lead-save/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析提交（跟进人）
 * @function SubmitReasonEnable
 * @returns {object} fetch Promise
 */
export function SubmitReasonEnable(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason/lead-submit/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析是否找到根本原因（跟进人）
 * @function SubmitReasonTotalEnable
 * @returns {object} fetch Promise
 */
export function SubmitReasonTotalEnable(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason/root-reason/lead-submit/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析止呼待范围保存（责任人）
 * @function SaveInfluenceRange
 * @returns {object} fetch Promise
 */
export function SaveInfluenceRange(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/influence-range/save/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析变更市场活动评估（跟进人）
 * @function ChangeMarketFlag
 * @returns {object} fetch Promise
 */
export function ChangeMarketFlag(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason/market-act-evaluation-flag/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析问题冻结申请保存（跟进人或主责任人）
 * @function SaveFreezeInfo
 * @returns {object} fetch Promise
 */
export function SaveFreezeInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/freeze-apply/save/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析问题冻结申请提交（跟进人或主责任人）
 * @function SubmitFreezeInfo
 * @returns {object} fetch Promise
 */
export function SubmitFreezeInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/freeze-apply/submit/ui`,
    method: 'POST',
  };
}

/**
 * 原因分析问题解冻（跟进人或主责任人）
 * @function SubmitUnfreezeInfo
 * @returns {object} fetch Promise
 */
export function SubmitUnfreezeInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/unfreeze/ui`,
    method: 'POST',
  };
}

/**
 * 关闭申请（主责任人）
 * @function CloseProblem
 * @returns {object} fetch Promise
 */
export function CloseProblem(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/close-apply/ui`,
    method: 'POST',
  };
}

/**
 * 保存预关闭/延期信息
 * @function SavePreCloseInfo
 * @returns {object} fetch Promise
 */
export function SavePreCloseInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/pre-overtime/close-save/ui`,
    method: 'POST',
  };
}

/**
 * 提交预关闭/延期信息
 * @function SubmitPreCloseInfo
 * @returns {object} fetch Promise
 */
export function SubmitPreCloseInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/pre-overtime/close-submit/ui`,
    method: 'POST',
  };
}

/**
 * 保存评分（高级技术经理）
 * @function SaveEvaluation
 * @returns {object} fetch Promise
 */
export function SaveEvaluation(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/evaluation/save/ui`,
    method: 'POST',
  };
}

// 获取用户部门
export function getUserDepartmentConfig() {
  return {
    url: `/hpfm/v1/${tenantId}/employee-assigns`,
    method: 'GET',
  };
}

/**
 * 根据materialId获取物料批对应的物料相关信息
 * @function QueryMaterialLotInfo
 * @returns {object} fetch Promise
 */
export function QueryMaterialLotInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-qa-feedbacks/mt-material-ca`,
    method: 'POST',
  };
}

/**
 * 获取问题相关数据
 * @function FetchProblemAllData
 * @returns {object} fetch Promise
 */
export function FetchProblemAllData(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/problem-list/lov/ui`,
    method: 'GET',
  };
}

/**
 * 发送消息
 * @function SendWeChatMessage
 * @returns {object} fetch Promise
 */
export function SendWeChatMessage(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/message/send/we-chat`,
    method: 'POST',
  };
}

/**
 * 全文检索查询
 * @function EsQuery
 * @returns {object} fetch Promise
 */
export function EsQuery(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/es-page/ui`,
    method: 'GET',
  };
}
