/**
 * 文件上传
 * @date: 2019-08-07
 * @author: qzq <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2019, Hand
 * 参数如下:
 * @param bucketName 非必须，桶名,默认为moduleName
 * @param moduleName 必须，模块名称，和模块id一起来定位日志和文件，以该模块名作为bucketName上传文件
 * @param moduleId 必须，模块id，和模块名称一起来定位日志和文件
 * @param uploadId 非必须 用于在上传时获取uuid，用于上传，在工作单中使用
 * @param showDeleteFlag 非必须 用于控制上传按钮是否显示
 * @param uploadModule 非必须 用于uuid与buckName会变动时的上传
 * @param showContentFlag 非必须 用于说明是否在改组件上新增组件，如上传按钮旁边的select框
 * @param fileUploadLogList 非必须 用于获取除本身附件列表，例如获取资产分类的附件，格式如下
 * {
      fileUploadLogList: [
        {
          moduleName: "aafm-asset-set",
          moduleIdList: [
            690002 // id array
          ]
        },{
        moduleName: "aafm-equipment-asset",
        moduleIdList: [
          990001,540031
        ]}
    ]
  }
 * @param attribute 非必须，动态拓展列,格式如下
 * {
        title: [
          {
            name: '来源类型', // 必须，列名
            code: 'sourceType', // 必须，列代码
            width: 150, //非必须，列宽度，默认120
            index: 1, // 必须，列插入的位置
            isUrl: true, //非必须，该列数据是否是个url链接
          },
        ],
        data: [
          {
            name: '来源类型', // 必须，name必须与title中name保持一致
            code: 'sourceType', // 必须，code必须与title中code保持一致
            value: '设备关联附件', // 必须，该上传时赋予拓展列的值
            pathname: `/aafm/equipment-asset/detail/${assetId}`, // 如果title中设置了isUrl，该参数为跳转的地址
          },
        ],
 * }
 *  @param dispatch 非必须，父控件的dispatch，用于链接跳转，如果attribute中没有设置isUrl的列则不需要
 */
import React from 'react';
// import { Modal } from 'choerodon-ui';
import { Button, Modal } from 'choerodon-ui/pro';
import { isArray } from 'lodash';
import { Bind } from 'lodash-decorators';
import intl from 'utils/intl';
import moment from 'moment';
import {
  getResponse,
  getAttachmentUrl,
  getDateTimeFormat,
  getCurrentOrganizationId,
} from 'utils/utils';
import {
  deleteFileAndLog,
  createUploadLog,
  getCurrentEmployee,
  fetchFileBatch,
  fetchFileBatchForWoop,
  fetchFileLog,
  fetchFileLogForWoop,
} from '../../services/api';
import FileModal from './FileModal';
import styles from './index.module.less';

const modalKey = Modal.key();
const confirmModalKey = Modal.key();

class FileUpload extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      fileList: [],
      logList: [],
      attachmentUuid: '',
      employeeId: '',
      isCheckFile: ['CHECK', 'CHECK_BUTTON'].includes(props.type),
    };
    this.modalRef = React.createRef();
    this.fileModal = null;
  }

  /**
   * 总的查询文件列表
   * @param {boolean} flag 是否更新子组件state
   */
  @Bind()
  handleTotalSearch() {
    if (this.modalRef && this.modalRef.current) {
      this.modalRef.current.handleSetLoading();
    }
    const { moduleName, moduleId, fileUploadLogList = [], afterTheAttachChange } = this.props;
    const arr = [moduleId];
    const fileLogList = fileUploadLogList.concat([
      {
        moduleName,
        moduleIdList: arr,
      },
    ]);
    Promise.all([this.handleGetLogList(fileLogList), this.handleGetFileList(fileLogList)]).then(
      result => {
        const logList = result[0];
        const fileList = result[1];
        const tmpFileList = this.handleFileTime(fileList);
        const tmpLogList = this.handleLogTime(logList);
        this.setState({
          fileList: tmpFileList,
          logList: tmpLogList,
        });
        // 重写渲染子组件表格数据
        if (this.modalRef && this.modalRef.current) {
          this.modalRef.current.handleSetTableData({
            fileSource: tmpFileList,
            logSource: tmpLogList,
          });
        }

        // 附件变化后的操作，目前用于服务申请单
        if (afterTheAttachChange) {
          afterTheAttachChange();
        }
      }
    );
  }

  /** 附件时间转化 */
  @Bind()
  handleFileTime(list) {
    if (!isArray(list)) return [];
    const newList = list.map(item => {
      const date = item.uploadDate.split('-').join('/');
      const newItem = JSON.parse(JSON.stringify(item));
      newItem.uploadDate = new Date(date).getTime();
      return newItem;
    });
    newList.sort((a, b) => {
      return b.uploadDate - a.uploadDate;
    });
    const secList = newList.map(item => {
      const date = new Date(item.uploadDate);
      const newItem = JSON.parse(JSON.stringify(item));
      newItem.uploadDate = moment(date, getDateTimeFormat());
      return newItem;
    });
    return secList;
  }

  /** 日志时间转化 */
  @Bind()
  handleLogTime(list) {
    if (!isArray(list)) return [];
    const newList = list.map(item => {
      const date = item.operationDate.split('-').join('/');
      const newItem = JSON.parse(JSON.stringify(item));
      newItem.operationDate = new Date(date).getTime();
      return newItem;
    });
    newList.sort((a, b) => {
      return b.operationDate - a.operationDate;
    });
    const secList = newList.map(item => {
      const date = new Date(item.operationDate);
      const newItem = JSON.parse(JSON.stringify(item));
      newItem.operationDate = moment(date, getDateTimeFormat());
      return newItem;
    });
    return secList;
  }

  /** 获取总日志数据 */
  @Bind()
  handleGetLogList(totalFiles) {
    const { woId, woopId, parentTypeCode } = this.props;
    const { isCheckFile } = this.state;
    return new Promise(resolve => {
      getResponse(
         // 检查项类型的附件通过woopId获取
         isCheckFile ? fetchFileLogForWoop({
          woId,
          woopId: parentTypeCode !== 'WO' || woId === woopId ? null : woopId,
          parentTypeCode,
        }) : fetchFileLog({
          tenantId: getCurrentOrganizationId(),
          body: {
            fileUploadLogList: totalFiles,
          },
        })
      ).then(res => {
        resolve(res);
      });
    });
  }

  /** 获取总文件数据 */
  @Bind()
  handleGetFileList(totalFiles) {
    const { woId, woopId, parentTypeCode } = this.props;
    const { isCheckFile } = this.state;
    return new Promise(resolve => {
      getResponse(
        // 检查项类型的附件通过woopId获取
        isCheckFile ? fetchFileBatchForWoop({
          woId,
          woopId: parentTypeCode !== 'WO' || woId === woopId ? null : woopId,
          parentTypeCode,
        }) : fetchFileBatch({
          tenantId: getCurrentOrganizationId(),
          body: {
            fileUploadLogList: totalFiles,
          },
        })
      ).then(res => {
        resolve(res);
      });
    });
  }

  /**
   * 删除附件
   * @param {*} record
   */
  @Bind()
  handleFileDelete(record) {
    const { bucketName } = record;
    const { currentEmployee } = this.state;
    const params = {
      tenantId: getCurrentOrganizationId(),
      operatorId: currentEmployee.employeeId,
      status: 'D',
      moduleName: bucketName,
      moduleId: record.moduleId,
      fileUrl: record.fileUrl,
      fileKey: record.fileUrl.split(`${bucketName}/`)[1],
    };
    Modal.confirm({
      iconType: '',
      key: confirmModalKey,
      children: intl.get('alm.component.fileUpload.view.message.confirm.delete').d('是否删除附件'),
      onOk: () => {
        getResponse(deleteFileAndLog({ ...params })).then(res => {
          if (res) {
            // 刷新列表
            this.handleTotalSearch();
          }
        });
      },
    });
  }

  /**
   * 下载附件
   * @param {*} record
   */
  @Bind()
  handleFileModalDownload(record) {
    const { bucketName } = record;
    const assetId = record.moduleId;
    window.open(getAttachmentUrl(record.fileUrl, bucketName));
    // 插入日志
    this.addFileLog(
      { name: record.fileName, fileUrl: record.fileUrl, status: 'P' },
      bucketName,
      assetId
    );
  }

  /**
   * 打开文件模态窗
   */
  @Bind()
  handleFileModalOpen(e) {
    const { isCheckFile } = this.state;
    e.stopPropagation();

    const {
      beforeOpenModal,
    } = this.props;

    let title = intl.get(`alm.component.fileUpload.button.attachmentManagement`).d('附件管理');
    if (isCheckFile) {
      title = intl.get(`alm.component.fileUpload.button.checkAttachmentManagement`).d('检查项附件管理');
    }

    const funs = [getCurrentEmployee({ tenantId: getCurrentOrganizationId() })];
    if (beforeOpenModal) {
      funs.push(beforeOpenModal());
    }
    Promise.all(funs)
    .then(result => {
      this.setState({ currentEmployee: result[0], employeeId: result[0].employeeId }, () => {
        const fileProps = this.getModalContentProps();
        // 显示modal
        this.fileModal = Modal.open({
          key: modalKey,
          maskClosable: true, // 点击蒙层是否允许关闭
          keyboardClosable: true, // 按 esc 键是否允许关闭
          destroyOnClose: true, // 关闭时是否销毁
          style: {
            width: 700,
          },
          closable: true,
          drawer: true,
          title,
          children: <FileModal {...fileProps} ref={this.modalRef} />,
        });
      });
    });
  }

  @Bind()
  getModalContentProps() {
    const { fileList, logList, attachmentUuid, employeeId } = this.state;
    const {
      bucketName,
      moduleName,
      moduleId,
      uploadModule,
      attribute,
      dispatch,
      showContentFlag,
      showDeleteFlag,
      showUploadFlag,
      onfetchContent,
      style,
      ...otherProps
    } = this.props;
    return {
      bucketName: bucketName || moduleName,
      tenantId: getCurrentOrganizationId(),
      moduleName,
      moduleId,
      attachmentUuid: !showContentFlag ? `halm-${moduleName}-${moduleId}` : attachmentUuid,
      otherProps,
      fileSource: fileList,
      logSource: logList,
      dispatch,
      uploadModule,
      showDeleteFlag,
      showUploadFlag,
      onfetchContent,
      showContentFlag,
      attribute,
      employeeId,
      onSearch: this.handleTotalSearch,
      onChangeUuid: this.handleChangeUuid,
      onUploadSuccess: this.handleUploadSuccess,
      onUpdateSuccess: this.handleUpdateSuccess,
      onDelete: this.handleFileDelete,
      onDownload: this.handleFileModalDownload,
    };
  }

  @Bind()
  handleUpdateFileModal() {
    const { isCheckFile } = this.state;
    const fileProps = this.getModalContentProps();
    this.fileModal.update({
      children: <FileModal {...fileProps} ref={this.modalRef} />,
    });
    // 检查项类型的附件，切换下拉框时需要重新请求附件列表
    if (isCheckFile) {
      this.handleTotalSearch();
    }
  }

  /**
   * 上传成功触发事件
   */
  @Bind()
  handleUploadSuccess(result) {
    // 插入日志
    const { showContentFlag = false, uploadModule } = this.props;
    let name;
    let id;
    if (!showContentFlag) {
      const { moduleName, moduleId } = this.props;
      name = moduleName;
      id = moduleId;
    } else {
      const { uploadModuleName, uploadModuleId } = uploadModule;
      name = uploadModuleName;
      id = uploadModuleId;
    }
    this.addFileLog({ name: result.name, fileUrl: result.response, status: 'I' }, name, id);
  }

  /** 上传成功时 */
  /**
   * 插入日志并刷新
   * @param {name,fileUrl,status} data
   */
  @Bind()
  addFileLog(data, moduleName, moduleId) {
    const { collectionCode, attribute = [] } = this.props;
    const { currentEmployee } = this.state;
    getResponse(
      createUploadLog({
        tenantId: getCurrentOrganizationId(),
        moduleName,
        moduleId,
        collectionCode,
        status: data.status,
        fileUrl: data.fileUrl || '',
        operatorId: currentEmployee.employeeId,
        attribute: JSON.stringify(attribute.data),
        description:
          data.status === 'I'
            ? `新增"${data.name}"文档`
            : data.status === 'U'
            ? `更新"${data.name}"文档`
            : data.status === 'D'
            ? `删除"${data.name}"文档`
            : data.status === 'P'
            ? `下载"${data.name}"文档`
            : ``,
      })
    ).then(res => {
      if (res) {
        this.handleTotalSearch();
      }
    });
  }

  render() {
    const {
      icon = 'link',
      type,
      style = {},
      moduleId,
      disabled,
      uploadButtonName = { code: 'attachmentManagement', name: '附件管理' },
    } = this.props;
    const {
      isCheckFile,
    } = this.state;
    // Button只有icon无内容时，icon-only样式会覆盖disable样式导致按钮不会置灰
    if (disabled || (!moduleId && !isCheckFile)) {
      style.backgroundColor = '#f5f5f5';
      style.border = '1px solid #e6e6e6';
    }
    return (
      <React.Fragment>
        {['CHECK'].includes(type) ? (
          <a onClick={(events) => this.handleFileModalOpen(events)}>
            {intl
              .get(`alm.component.fileUpload.button.${uploadButtonName.code}`)
              .d(uploadButtonName.name)}
          </a>
      ): (
        <Button
          className={styles['file-upload-btn']}
          icon={icon}
          style={style}
          disabled={disabled || (!moduleId && !isCheckFile)}
          onClick={this.handleFileModalOpen}
        >
          {uploadButtonName.name.length > 0 &&
            intl
              .get(`alm.component.fileUpload.button.${uploadButtonName.code}`)
              .d(uploadButtonName.name)}
        </Button>
      )}
      </React.Fragment>
    );
  }
}
export default FileUpload;
