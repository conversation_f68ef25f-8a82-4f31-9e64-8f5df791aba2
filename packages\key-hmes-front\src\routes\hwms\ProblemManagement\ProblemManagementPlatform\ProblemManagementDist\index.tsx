/**
 * @Description: 问题管理平台-详情界面
 * @Author: <EMAIL>
 * @Date: 2023/7/3 17:09
 */
import React, { useState, useEffect, useMemo } from 'react';
import { DataSet, Modal, Form, TextArea, Lov } from 'choerodon-ui/pro';
import { Tooltip, Icon } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { getCurrentUser } from 'utils/utils';
import moment from 'moment';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import { DataSetStatus } from 'choerodon-ui/dataset/data-set/enum';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import { getProductWithPrecision } from '@/utils';
import ProblemDetailCard from './components/ProblemDetailCard';
import StepComponent from './components/StepComponent';
import UserRoleComponent from './components/UserRoleComponent';
import StepContentComponent from './StepContentComponent';
import { problemInfoDS, sendMessageDS } from '../stores/ProblemInfoDS';
import {
  registerProblemDS,
  leadInfoDS,
  distributeDS,
  supplierInfoDS,
  measureDS,
  changeObjectDS,
  reasonAnalysisDS,
  influenceRangeDS,
  freezeApplyDS,
  problemCloseDS,
  problemCloseTableDS,
  finallyScoreFormDS,
  finallyScoreTableDS,
  marketActDS,
  enclosure8dDS,
  preCloseDS,
  overtimeCloseTableDS,
} from '../stores/ProblemDetailDS';
import {
  SaveLeadAndResponsible,
  SaveProblemInfo,
  SubmitProblemInfo,
  SaveDutySupplier,
  SaveMeasureInfo,
  SubmitMeasureInfo,
  SaveMeasureEnable,
  SubmitMeasureEnable,
  SaveChangeObject,
  SubmitTotalEnable,
  SaveReasonInfo,
  SubmitReasonInfo,
  SaveReasonEnable,
  SubmitReasonEnable,
  SubmitReasonTotalEnable,
  SaveInfluenceRange,
  ChangeMarketFlag,
  SaveFreezeInfo,
  SubmitFreezeInfo,
  CloseProblem,
  SaveEvaluation,
  SaveProblem,
  SubmitUnfreezeInfo,
  QueryMaterialLotInfo,
  SaveEnclosure8d,
  FetchProblemAllData,
  SendWeChatMessage,
  SaveNextReportDate,
  SavePreCloseInfo,
  SubmitPreCloseInfo,
} from '../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';
const { id: currentUserId } = getCurrentUser();

const ProblemManagementDetail = props => {
  const {
    history,
    match: { params, path },
    location: { state },
  } = props;
  const problemId = params.problemId;

  // pub 路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  // 是否新建界面跳转
  const [createFlag, setCreateFlag] = useState<boolean>(false);
  // Form表单没有内置的loading,暂时手动控制
  const [loading, setLoading] = useState<boolean>(false);
  // 当前步骤
  const [currentStep, setCurrentStep] = useState<number>(0);
  // 问题类型
  // const [problemCategory, setProblemCategory] = useState('');
  // 问题类型属于哪种页面
  const [problemGroup, setProblemGroup] = useState('');
  // 问题状态
  const [problemStatus, setProblemStatus] = useState('');
  // srmFlag
  const [srmFlag, setSrmFlag] = useState<boolean>(false);
  // 当前步骤页的enableFlag
  const [enableFlag, setEnableFlag] = useState('');
  // 关闭申请按钮是否可点击
  const [closeBtnDisabled, setCloseBtnDisabled] = useState(false);
  // 是否存在长期措施提交
  const [perpSubmitFlag, setPerpSubmitFlag] = useState(false);
  // 总分
  const [totalScoreInfo, setTotalScoreInfo] = useState({
    influenceLevelScore: 0, // 影响程度
    severityLevelScore: 0, // 严重程度
    totalScore: 0, // 总分
  });
  // 当前登录人角色
  // 登记人: REGISTER_PERSON
  // 跟进人: LEAD_PERSON
  // 提出人: PROPOSE_PERSON
  // 责任人: RESPONSIBLE_PERSON
  // 主责任人: MAJOR_RESPONSIBLE_PERSON
  // 高级技术经理: SENIOR_TECHNICAL_MANAGER
  const [userRole, setUserRole] = useState<string[]>([]);
  const [delayDaysInfo, setDelayDaysInfo] = useState<any>(null);
  // 问题基础属性DS
  const problemInfoDs = useMemo(() => new DataSet(problemInfoDS()), []);
  // 发送消息DS
  const sendMessageDs = useMemo(() => new DataSet(sendMessageDS()), []);
  // 登记问题step-登记人DS
  const registerProblemDs = useMemo(() => new DataSet(registerProblemDS()), []);
  // 登记问题step-跟进人DS
  const leadProblemDs = useMemo(() => new DataSet(leadInfoDS()), []);
  // 分配责任人step-创建小组DS
  const distributeDs = useMemo(() => new DataSet(distributeDS()), []);
  // 分配责任人step-责任供应商DS
  const supplierInfoDs = useMemo(() => new DataSet(supplierInfoDS()), []);
  // 临时措施stepDS
  const tempMeasureDs = useMemo(() => new DataSet(measureDS()), []);
  // 临时措施-变更对象stepDS
  const tempChangeObjectDs = useMemo(() => new DataSet(changeObjectDS()), []);
  // 原因分析stepDS
  const reasonAnalysisDs = useMemo(() => new DataSet(reasonAnalysisDS()), []);
  // 原因分析-止呼待范围stepDS
  const influenceRangeDs = useMemo(() => new DataSet(influenceRangeDS()), []);
  // 原因分析-市场活动方案stepDS
  const marketActDs = useMemo(() => new DataSet(marketActDS()), []);
  // 原因分析-问题冻结申请stepDS
  const freezeApplyDs = useMemo(() => new DataSet(freezeApplyDS()), []);
  // 长期措施stepDS
  const prepMeasureDs = useMemo(() => new DataSet(measureDS()), []);
  // 分配责任人step-8D报告上传DS
  const enclosure8dDs = useMemo(() => new DataSet(enclosure8dDS()), []);
  // 长期措施-变更对象stepDS
  const prepChangeObjectDs = useMemo(() => new DataSet(changeObjectDS()), []);
  // 验证关闭-问题关闭step-FormDS
  const problemCloseDs = useMemo(() => new DataSet(problemCloseDS()), []);
  // 验证关闭-问题关闭step-其他信息表格DS
  const problemCloseTableDs = useMemo(() => new DataSet(problemCloseTableDS()), []);
  // 验证关闭-问题关闭step-预关闭信息DS
  const preCloseDs = useMemo(() => new DataSet(preCloseDS()), []);
  // 验证关闭-问题关闭step-延期关闭信息DS
  const overtimeCloseTableDs = useMemo(() => new DataSet(overtimeCloseTableDS()), []);
  // 验证关闭-最终评分Table stepDS
  const finallyScoreTableDs = useMemo(() => new DataSet(finallyScoreTableDS()), []);
  // 验证关闭-最终评分Form stepDS
  const finallyScoreFormDs = useMemo(
    () =>
      new DataSet({
        ...finallyScoreFormDS(),
        children: {
          responPersonInfo: finallyScoreTableDs,
        },
      }),
    [],
  );

  const { run: saveProblem, loading: saveLoading } = useRequest(SaveProblem(), {
    manual: true,
  });
  const { run: saveProblemInfo, loading: saveInfoLoading } = useRequest(SaveProblemInfo(), {
    manual: true,
  });
  const { run: submitProblemInfo, loading: submitInfoLoading } = useRequest(SubmitProblemInfo(), {
    manual: true,
  });
  const { run: saveDutySupplier, loading: saveSupplierLoading } = useRequest(SaveDutySupplier(), {
    manual: true,
  });
  const { run: saveNextReportDate, loading: saveNextReportDateLoading } = useRequest(SaveNextReportDate(), {
    manual: true,
  });
  const { run: savePersonList, loading: savePersonLoading } = useRequest(SaveLeadAndResponsible(), {
    manual: true,
  });
  const { run: saveMeasureInfo, loading: saveMeasureInfoLoading } = useRequest(SaveMeasureInfo(), {
    manual: true,
  });
  const { run: submitMeasureInfo, loading: submitMeasureInfoLoading } = useRequest(
    SubmitMeasureInfo(),
    {
      manual: true,
    },
  );
  const { run: saveMeasureEnable, loading: saveEnableLoading } = useRequest(SaveMeasureEnable(), {
    manual: true,
  });
  const { run: submitMeasureEnable, loading: submitEnableLoading } = useRequest(
    SubmitMeasureEnable(),
    {
      manual: true,
    },
  );
  const { run: submitTotalEnable, loading: submitTotalLoading } = useRequest(SubmitTotalEnable(), {
    manual: true,
  });
  const { run: saveChangeObject, loading: saveObjectLoading } = useRequest(SaveChangeObject(), {
    manual: true,
  });
  const { run: saveReasonInfo, loading: reasonSaveLoading } = useRequest(SaveReasonInfo(), {
    manual: true,
  });
  const { run: submitReasonInfo, loading: reasonSubmitLoading } = useRequest(SubmitReasonInfo(), {
    manual: true,
  });
  const { run: saveReasonEnable, loading: saveReasonEnableLoading } = useRequest(
    SaveReasonEnable(),
    {
      manual: true,
    },
  );
  const { run: submitReasonEnable, loading: submitReasonEnableLoading } = useRequest(
    SubmitReasonEnable(),
    {
      manual: true,
    },
  );
  const { run: submitReasonTotalEnable, loading: reasonTotalLoading } = useRequest(
    SubmitReasonTotalEnable(),
    {
      manual: true,
    },
  );
  const { run: saveInfluenceRange, loading: saveRangeLoading } = useRequest(SaveInfluenceRange(), {
    manual: true,
  });
  const { run: saveEnclosure8d, loading: save8DLoading } = useRequest(SaveEnclosure8d(), {
    manual: true,
  });
  const { run: changeMarketFlag, loading: changeMarketLoading } = useRequest(ChangeMarketFlag(), {
    manual: true,
  });
  const { run: saveFreezeInfo, loading: saveFreezeLoading } = useRequest(SaveFreezeInfo(), {
    manual: true,
  });
  const { run: submitFreezeInfo, loading: submitFreezeLoading } = useRequest(SubmitFreezeInfo(), {
    manual: true,
  });
  const { run: submitUnfreezeInfo, loading: submitUnfreezeLoading } = useRequest(
    SubmitUnfreezeInfo(),
    {
      manual: true,
    },
  );
  const { run: closeProblem, loading: closeProblemLoading } = useRequest(CloseProblem(), {
    manual: true,
  });
  const { run: saveEvaluation, loading: saveEvaluationLoading } = useRequest(SaveEvaluation(), {
    manual: true,
  });
  // 查询物料批信息
  const { run: queryMaterialLotInfo, loading: queryLoading } = useRequest(QueryMaterialLotInfo(), {
    manual: true,
  });
  // 获取问题数据
  const { run: getProblemAllData, loading: getProblemDataLoading } = useRequest(FetchProblemAllData(), {
    manual: true,
    needPromise: true,
  });
  // 发送消息
  const { run: sendWeChatMessage, loading: sendMessageLoading } = useRequest(SendWeChatMessage(), {
    manual: true,
    needPromise: true,
  });
  // 保存预关闭信息
  const { run: savePreCloseInfo, loading: savePreCloseLoading } = useRequest(SavePreCloseInfo(), {
    manual: true,
    needPromise: true,
  });
  // 提交预关闭信息
  const { run: submitPreCloseInfo, loading: submitPreCloseLoading } = useRequest(SubmitPreCloseInfo(), {
    manual: true,
    needPromise: true,
  });
  const { data: measureEnableList = [] } = useRequest({ lovCode: 'YP.QIS.MEASURE_ENABLE_FLAG' }); // 【措施有效性】值集
  const { data: reasonConfirmList = [] } = useRequest({
    lovCode: 'YP.QIS.ROOT_REASON_CONFIRM_FLAG',
  }); // 【是否找到根本原因】值集1

  useEffect(() => {
    setCreateFlag(state?.createFlag === 'Y');
    initData();
  }, [problemId, problemGroup]);

  const initData = () => {
    problemInfoDs.setQueryParameter('problemId', problemId);
    problemInfoDs.query().then(res => {
      // 记录当前的问题类别属于哪种问题
      const {
        problemCategory,
        problemStatus,
        registerPerson,
        proposePerson,
        leadPerson,
        responsibleFlag,
        principalPersonFlag,
        technicalManagerFlag,
        solutionTool,
        srmFlag,
        siteId,
        delayDaysDTO = {},
      } = res?.rows;
      setDelayDaysInfo(delayDaysDTO);
      // setProblemCategory(problemCategory);
      setProblemStatus(problemStatus);
      setSrmFlag(srmFlag === 'Y');

      // 问题解决工具为“深度”时附件必填
      reasonAnalysisDs.setState('solutionTool', solutionTool);
      prepMeasureDs.setState('solutionTool', solutionTool);
      enclosure8dDs.setState('solutionTool', solutionTool);

      // 记录问题所属的类别
      const problemCategoryDs =
        problemInfoDs.getField('problemCategory')?.getOptions(problemInfoDs.current) || [];
      const currentCategoryRecord = problemCategoryDs.find(
        _record => _record?.get('value') === problemCategory,
      );
      setProblemGroup(currentCategoryRecord?.get('tag'));
      problemInfoDs.setState('problemGroup', currentCategoryRecord?.get('tag'));
      registerProblemDs.setState('problemGroup', currentCategoryRecord?.get('tag'));
      leadProblemDs.setState('problemGroup', currentCategoryRecord?.get('tag'));

      // 责任供应商记录站点
      supplierInfoDs.setState('siteId', siteId);

      // 初始化当前员工角色信息
      const _userRoleList: string[] = [];
      if (Number(registerPerson) === Number(currentUserId)) {
        _userRoleList.push('REGISTER_PERSON');
      }
      if (Number(proposePerson) === Number(currentUserId)) {
        _userRoleList.push('PROPOSE_PERSON');
      }
      if (Number(leadPerson) === Number(currentUserId)) {
        _userRoleList.push('LEAD_PERSON');
      }
      if (responsibleFlag === 'Y') {
        _userRoleList.push('RESPONSIBLE_PERSON');
      }
      if (principalPersonFlag === 'Y') {
        _userRoleList.push('MAJOR_RESPONSIBLE_PERSON');
      }
      if (technicalManagerFlag === 'Y') {
        _userRoleList.push('SENIOR_TECHNICAL_MANAGER');
      }
      setUserRole(_userRoleList);
      problemInfoDs.setState('userRoleList', _userRoleList);
      // registerProblemDs.setState('userRoleList', _userRoleList);
      // leadProblemDs.setState('userRoleList', _userRoleList);
      distributeDs.setState('userRoleList', _userRoleList);
      finallyScoreFormDs.setState('userRoleList', _userRoleList);

      if (problemGroup) {
        handleChangeStep(currentStep, true);
      }
    });
  };

  // 查询第一个步骤页的数据
  const handleQueryRegister = () => {
    setLoading(true);
    registerProblemDs.setQueryParameter('problemId', problemId);
    registerProblemDs
      .query()
      .then(res => {
        setLoading(false);
        const {
          problemApplyId,
          prodLineId,
          prodLineName,
          workcellId,
          workcellName,
          equipmentId,
          equipmentName,
        } = state?.problemApplyInfo || {};
        const { rows } = res;
        registerProblemDs.current?.set(
          'productionLineId',
          problemApplyId ? prodLineId : rows.productionLineId,
        );
        registerProblemDs.current?.set(
          'prodLineName',
          problemApplyId ? prodLineName : rows.prodLineName,
        );
        registerProblemDs.current?.set('processId', problemApplyId ? workcellId : rows.processId);
        registerProblemDs.current?.set(
          'processWorkcellName',
          problemApplyId ? workcellName : rows.processWorkcellName,
        );
        registerProblemDs.current?.set(
          'equipmentId',
          problemApplyId ? equipmentId : rows.equipmentId,
        );
        registerProblemDs.current?.set(
          'equipmentName',
          problemApplyId ? equipmentName : rows.equipmentName,
        );
        leadProblemDs.loadData([
          {
            ...rows,
            problemStatus: problemInfoDs.current?.get('problemStatus'),
            productionLineId: problemApplyId ? prodLineId : rows.productionLineId,
            prodLineName: problemApplyId ? prodLineName : rows.prodLineName,
            equipmentId: problemApplyId ? equipmentId : rows.equipmentId,
            equipmentName: problemApplyId ? equipmentName : rows.equipmentName,
          },
        ]);
        registerProblemDs.setState('canEdit', createFlag);
        setCreateFlag(false);
        leadProblemDs.setState('canEdit', false);
        leadProblemDs.setState('nextReportDateEdit', false);
        // history.replace({ ...history.location, state: undefined });
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 查询分配责任人的数据
  const handleQueryDistribute = () => {
    distributeDs.setQueryParameter('problemId', problemId);
    distributeDs.query().then(() => {
      distributeDs.setState('userRoleList', userRole);
      distributeDs.setState('canEdit', false);
    });
    supplierInfoDs.setQueryParameter('problemId', problemId);
    supplierInfoDs.query().then(res => {
      const { supplierId, materialId } = res?.rows || {};
      if (!supplierId) {
        supplierInfoDs.current?.init('supplierLov', undefined);
      }
      if (!materialId) {
        supplierInfoDs.current?.init('materialLov', undefined);
      }
      supplierInfoDs.setState('canEdit', false);
    });
  };

  // 临时措施、长期措施数据查询
  const handleQueryMeasure = (measureDs, changeObjectDs, measureType) => {
    measureDs.setQueryParameter('problemId', problemId);
    measureDs.setQueryParameter('measureType', measureType);
    measureDs.query().then(res => {
      if (!res?.success) {
        return;
      }
      const {
        enclosure8d,
        measureInfo,
        changeObjectInfo,
        tempMeasureExeEnableFlag,
        perpMeasureExeEnableFlag,
        closeApplyFlag,
        influenceRangeInfo,
      } = res.rows;
      setCloseBtnDisabled(closeApplyFlag !== 'Y');
      measureDs.loadData(measureInfo || []);
      measureDs.setState('operationType', 'look');
      measureDs.setState('measureType', measureType);
      changeObjectDs.loadData(changeObjectInfo || []);
      changeObjectDs.setState('canEdit', false);
      influenceRangeDs.loadData(influenceRangeInfo || []);
      influenceRangeDs.setState('canEdit', false);
      if (measureType === 'PERP') {
        enclosure8dDs.loadData([{ enclosure8d }]);
        enclosure8dDs.setState('canEdit', false);
      }
      setEnableFlag(measureType === 'TEMP' ? tempMeasureExeEnableFlag : perpMeasureExeEnableFlag);
    });
  };

  const handleQueryCloseInfo = () => {
    problemCloseDs.setQueryParameter('problemId', problemId);
    overtimeCloseTableDs.status = DataSetStatus.loading;
    problemCloseTableDs.status = DataSetStatus.loading;
    problemCloseDs.query().then(res => {
      const {
        objectInfoList,
        perpMeasureFlag,
        preCloseInfo,
        overtimeDayAll,
        overtimeCloseList,
      } = res?.rows || {};
      // 预关闭信息数据回填
      preCloseDs.loadData([{ ...(preCloseInfo || {}) }]);
      preCloseDs.setState('canEdit', false);
      setPerpSubmitFlag(perpMeasureFlag === 'Y');
      // 延期关闭信息数据回填
      (overtimeCloseList || []).forEach((item, index) => item.sequence = (index + 1) * 10)
      overtimeCloseTableDs.setState('overtimeDayAll', overtimeDayAll);
      overtimeCloseTableDs.loadData([ ...(overtimeCloseList || []) ]);
      overtimeCloseTableDs.status = DataSetStatus.ready;
      // 其他信息表格数据回填
      problemCloseTableDs.loadData([...objectInfoList]);
      problemCloseTableDs.status = DataSetStatus.ready;
    });
    finallyScoreFormDs.setQueryParameter('problemId', problemId);
    finallyScoreFormDs.query().then(res => {
      const { scoringCoefficient } = res;
      finallyScoreFormDs.setState('canEdit', false);
      handleUpdateTotalScore(scoringCoefficient);
    });
  };

  const handleUpdateTotalScore = scoringCoefficient => {
    // 影响程度option数据源
    const influenceLevelDs =
      leadProblemDs.getField('influenceLevel')?.getOptions(leadProblemDs.current) || [];
    const currentInfluenceRecord = influenceLevelDs.find(
      _record => _record?.get('value') === leadProblemDs.current?.get('influenceLevel'),
    );
    const _influenceLevelScore: number = Number(currentInfluenceRecord?.get('tag')) || 0;
    // 严重程度option数据源
    const severityLevelDs =
      leadProblemDs.getField('severityLevel')?.getOptions(leadProblemDs.current) || [];
    const currentSeverityRecord = severityLevelDs.find(
      _record => _record?.get('value') === leadProblemDs.current?.get('severityLevel'),
    );
    const _severityLevelScore: number = Number(currentSeverityRecord?.get('tag')) || 0;
    const _scoringCoefficient: number = Number(scoringCoefficient) || 1;
    const finalScore = getProductWithPrecision(
      _influenceLevelScore + _severityLevelScore,
      _scoringCoefficient,
      2,
    );
    setTotalScoreInfo({
      influenceLevelScore: _influenceLevelScore, // 影响程度
      severityLevelScore: _severityLevelScore, // 严重程度
      totalScore: finalScore, // 总分
    });
  };

  // 原因分析数据查询
  const handleQueryReasonAnalysis = () => {
    reasonAnalysisDs.setQueryParameter('problemId', problemId);
    reasonAnalysisDs.query().then(res => {
      if (!res?.success) {
        return;
      }
      const {
        reasonInfo,
        rootReasonConfirmFlag,
        marketActEvaluationFlag,
        marketActEvaluationNum,
        closeApplyFlag,
      } = res.rows;
      setCloseBtnDisabled(closeApplyFlag !== 'Y');
      reasonAnalysisDs.loadData(reasonInfo || []);
      reasonAnalysisDs.setState('operationType', 'look');
      setEnableFlag(rootReasonConfirmFlag);
      marketActDs.loadData([
        {
          marketActEvaluationFlag,
          marketActEvaluationNum,
        },
      ]);
      freezeApplyDs.setQueryParameter('problemId', problemId);
      freezeApplyDs.query();
    });
  };

  const getEditStatus = value => {
    if (value === 0) {
      return registerProblemDs.getState('canEdit') || leadProblemDs.getState('canEdit');
    }
    if (value === 1) {
      return distributeDs.getState('canEdit');
    }
    if (value === 2) {
      return (
        tempChangeObjectDs.getState('canEdit') ||
        tempChangeObjectDs.getState('canEdit') ||
        influenceRangeDs.getState('canEdit')
      );
    }
    if (value === 3) {
      return (
        reasonAnalysisDs.getState('canEdit') ||
        marketActDs.getState('canEdit') ||
        freezeApplyDs.getState('canEdit')
      );
    }
    if (value === 4) {
      return prepMeasureDs.getState('canEdit') || prepChangeObjectDs.getState('canEdit');
    }
    if (value === 5) {
      return finallyScoreFormDs.getState('canEdit');
    }
  };

  const handleChangeStep = (value, initFlag) => {
    if (!initFlag && getEditStatus(currentStep)) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.changeStep`)
              .d('当前界面有未保存的数据，切换后会重置，是否确认切换？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          changeStepCallBack(value);
        }
      });
    } else {
      changeStepCallBack(value);
    }
  };

  const changeStepCallBack = value => {
    if (value === 0) {
      handleQueryRegister();
    } else if (value === 1) {
      handleQueryDistribute();
    } else if (value === 2) {
      handleQueryMeasure(tempMeasureDs, tempChangeObjectDs, 'TEMP');
    } else if (value === 3) {
      handleQueryReasonAnalysis();
    } else if (value === 4) {
      handleQueryMeasure(prepMeasureDs, prepChangeObjectDs, 'PERP');
    } else if (value === 5) {
      handleQueryCloseInfo();
    }
    setCurrentStep(value);
  };

  const handleSaveBasicInfo = dataSet => {
    return new Promise(async resolve => {
      const valRes = await dataSet.validate();
      if (!valRes) {
        return resolve(false);
      }
      const { proposeTimePeriodStart, proposeTimePeriodEnd, ...others } = dataSet.current?.toData();
      saveProblem({
        params: {
          ...others,
          proposeTimePeriod: `${proposeTimePeriodStart}～${proposeTimePeriodEnd}`,
        },
        onSuccess: () => {
          notification.success({});
          initData();
          return resolve(true);
        },
        onFailed: () => {
          return resolve(false);
        },
      });
    });
  };

  const validateRegisterAndLead = async operationType => {
    if (operationType === 'supplierInfo') {
      const valRes = await supplierInfoDs.validate();
      return !valRes;
    }
    const validateInfoRes = await problemInfoDs.current?.validate(true);
    const validateRegisterRes = await registerProblemDs.current?.validate(true);
    if (operationType === 'register') {
      return !validateInfoRes || !validateRegisterRes;
    }
    const leadProblemRes = await leadProblemDs.current?.validate(true);
    const distributeRes = await distributeDs.current?.validate(true);
    return (
      !validateInfoRes ||
      !validateRegisterRes ||
      !leadProblemRes ||
      (distributeDs?.length && !distributeRes)
    );
  };

  // 登记问题、分配责任人保存
  const handleSaveCategoryDetail = async (operationType, externalReportFlag) => {
    const validateRes = await validateRegisterAndLead(operationType);
    if (validateRes) {
      return;
    }
    let detailType;
    if (problemGroup === 'MARKET') {
      detailType = 'marketInfo';
    } else if (['QUALITY', 'MANUFACTURE'].includes(problemGroup)) {
      detailType = 'qualityInfo';
    } else {
      detailType = 'previewInfo';
    }
    const { proposeTimePeriodStart, proposeTimePeriodEnd, ...others } = problemInfoDs.current?.toData();
    const leadData = leadProblemDs.current?.toData();
    delete leadData?.problemStatus;
    if (operationType === 'register') {
      // 登记人填写
      saveProblemInfo({
        params: {
          baseInfo: {
            ...others,
            proposeTimePeriod: `${proposeTimePeriodStart}～${proposeTimePeriodEnd}`,
            siteId: registerProblemDs?.current?.get('siteId'),
          },
          [detailType]: registerProblemDs?.current?.toData(),
        },
        onSuccess: () => {
          notification.success({});
          initData();
        },
      });
    } else if (leadProblemDs.getState('canEdit') || distributeDs.getState('canEdit')) {
      // 跟进人填写-填写全部跟进人信息
      savePersonList({
        params: {
          baseInfo: {
            ...others,
            ...leadData,
            siteId: registerProblemDs?.current?.get('siteId'),
            proposeTimePeriod: `${proposeTimePeriodStart}～${proposeTimePeriodEnd}`,
            nextReportDate: undefined,
          },
          [detailType]: {
            ...registerProblemDs?.current?.toData(),
            ...leadData,
            nextReportDate: leadProblemDs.current?.get('nextReportDate') ? moment(leadProblemDs.current?.get('nextReportDate')).format('YYYY-MM-DD') : undefined,
            externalReportFlag:
              operationType === 'reportFlag'
                ? externalReportFlag
                : registerProblemDs?.current?.get('externalReportFlag'),
          },
          responsiblePersonList: distributeDs.toData(),
        },
        onSuccess: () => {
          notification.success({});
          initData();
        },
      });
    } else if (leadProblemDs.getState('nextReportDateEdit')) {
      // 跟进人填写-填写“下次报告日期”
      const nextReportDate = leadProblemDs.current?.get('nextReportDate') ? moment(leadProblemDs.current?.get('nextReportDate')).format('YYYY-MM-DD') : undefined;
      saveNextReportDate({
        params: {
          problemId,
          nextReportDate,
        },
        onSuccess: () => {
          notification.success({});
          initData();
        },
      })
    }
  };

  // 登记问题、分配责任人提交
  const handleSubmitProblemInfo = async operationType => {
    const validateRes = await validateRegisterAndLead(operationType);
    if (validateRes) {
      return;
    }
    if (operationType !== 'supplierInfo') {
      const responsiblePersonList = distributeDs.toData() || [];
      // SRM flag为false时,提交时需要校验是否创建了小组
      if (problemStatus === 'PUBLISH') {
        if (!responsiblePersonList?.length) {
          notification.error({
            message: intl
              .get(`${modelPrompt}.validation.responsiblePersonRequired`)
              .d('请组建小组并分配责任人！'),
          });
          return;
        }
        if (!responsiblePersonList?.find((item: any) => item?.principalPersonFlag === 'Y')) {
          notification.error({
            message: intl
              .get(`${modelPrompt}.validation.atLeastOneResponsiblePerson`)
              .d('至少要有一个主责人，请检查！'),
          });
          return;
        }
      }
    }

    let _problemStatus;
    if (problemInfoDs.current?.get('problemStatus') === 'NEW') {
      _problemStatus = 'PUBLISH';
    } else {
      _problemStatus = operationType === 'supplierInfo' ? 'SRM_HANDLE' : 'RELEASED';
    }
    submitProblemInfo({
      params: {
        problemId,
        problemStatus: _problemStatus,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 责任供应商保存
  const handleSaveSupplierInfo = async dataSet => {
    const valRes = await dataSet.validate();
    if (!valRes) {
      return;
    }
    saveDutySupplier({
      params: {
        ...dataSet.current?.toData(),
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 措施或原因校验时仅校验责任人为自己的记录
  const handleValidateMeasureOrReason = async (dataSet) => {
    const validateRecord = dataSet.filter(_record => Number(_record.get('responsiblePerson')) === Number(currentUserId));
    // 校验所有表单
    const normalValidate = await Promise.all(
      validateRecord.map(async _record => {
        const res = await _record.validate(true);
        return res;
      }),
    );

    // 汇总校验结果
    return normalValidate.every(val => val);
  };

  // 临时措施、长期措施保存
  const handleSaveMeasure = async (dataSet, measureType) => {
    const validateRes = await handleValidateMeasureOrReason(dataSet);
    if (!validateRes) {
      return;
    }
    const measureData: any = [];
    if (dataSet.getState('operationType') === 'edit') {
      dataSet.forEach(_record => {
        if (Number(_record.get('responsiblePerson')) === Number(currentUserId)) {
          measureData.push({ ..._record.toData() });
        }
      });
    } else {
      dataSet.forEach(_record => {
        if (_record.get('measureStatus') === 'EVALUATING' && _record.dirty) {
          measureData.push({ ..._record.toData() });
        }
      });
    }
    const _run = dataSet.getState('operationType') === 'edit' ? saveMeasureInfo : saveMeasureEnable;
    _run({
      params: {
        measureInfo: measureData,
        measureType,
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 临时措施、长期措施提交
  const handleSubmitMeasure = async (dataSet, measureType, operationType) => {
    const validateRes = await handleValidateMeasureOrReason(dataSet);
    if (!validateRes) {
      return;
    }
    const measureData: any = [];
    if (operationType === 'measure') {
      dataSet.forEach(_record => {
        if (
          Number(_record.get('responsiblePerson')) === Number(currentUserId) &&
          _record.get('measureStatus') === 'NEW'
        ) {
          measureData.push({ ..._record.toData() });
        }
      });
    } else {
      dataSet.forEach(_record => {
        if (_record.get('enableFlag')) {
          measureData.push({ ..._record.toData() });
        }
      });
    }
    const _run = operationType === 'measure' ? submitMeasureInfo : submitMeasureEnable;
    _run({
      params: {
        measureInfo: measureData,
        measureType,
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 临时措施、长期措施是否有效提交
  const handleChangeTotalEnable = async (value, measureType) => {
    const flagName =
      measureType === 'TEMP' ? 'tempMeasureExeEnableFlag' : 'perpMeasureExeEnableFlag';
    submitTotalEnable({
      params: {
        [flagName]: value,
        measureType,
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 变更对象保存
  const handleSaveChangeObject = async (dataSet, measureType) => {
    const validateRes = await dataSet.validate();
    if (!validateRes) {
      return;
    }
    saveChangeObject({
      params: {
        changeObjectInfo: dataSet.toData(),
        measureType,
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 原因分析保存
  const handleSaveReason = async dataSet => {
    const validateRes = await handleValidateMeasureOrReason(dataSet);
    if (!validateRes) {
      return;
    }
    const reasonData: any = [];
    if (dataSet.getState('operationType') === 'edit') {
      dataSet.forEach(_record => {
        if (Number(_record.get('responsiblePerson')) === Number(currentUserId)) {
          reasonData.push({ ..._record.toData() });
        }
      });
    } else {
      dataSet.forEach(_record => {
        if (_record.get('reasonStatus') === 'EVALUATING' && _record.dirty) {
          reasonData.push({ ..._record.toData() });
        }
      });
    }
    const _run = dataSet.getState('operationType') === 'edit' ? saveReasonInfo : saveReasonEnable;
    _run({
      params: {
        reasonInfo: reasonData,
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 原因分析提交
  const handleSubmitReason = async (dataSet, operationType) => {
    const validateRes = await handleValidateMeasureOrReason(dataSet);
    if (!validateRes) {
      return;
    }
    const reasonData: any = [];
    if (operationType === 'reason') {
      dataSet.forEach(_record => {
        if (
          Number(_record.get('responsiblePerson')) === Number(currentUserId) &&
          _record.get('reasonStatus') === 'NEW'
        ) {
          reasonData.push({ ..._record.toData() });
        }
      });
    } else {
      dataSet.forEach(_record => {
        if (_record.get('rationalityFlag')) {
          reasonData.push({ ..._record.toData() });
        }
      });
    }
    const _run = operationType === 'reason' ? submitReasonInfo : submitReasonEnable;
    _run({
      params: {
        reasonInfo: reasonData,
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 原因分析是否找到根本原因提交
  const handleChangeReasonEnable = async value => {
    submitReasonTotalEnable({
      params: {
        rootReasonConfirmFlag: value,
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 原因分析止呼待范围保存
  const handleSaveChangeRange = dataSet => {
    saveInfluenceRange({
      params: {
        influenceRangeInfo: dataSet.toData(),
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 8D报告保存
  const handleSaveEnclosure8d = dataSet => {
    saveEnclosure8d({
      params: {
        enclosure8d: dataSet.current.get('enclosure8d'),
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 原因分析市场活动评估保存
  const handleChangeMarketFlag = async value => {
    if (!reasonAnalysisDs?.length) {
      return notification.error({
        message: intl
          .get(`${modelPrompt}.validation.reason`)
          .d('至少存在一条原因后才能点击“开启”或“关闭”按钮！'),
      });
    }
    changeMarketFlag({
      params: {
        marketActEvaluationFlag: value,
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 问题冻结申请保存
  const handleSaveFreezeInfo = async dataSet => {
    const validateRes = await dataSet.validate();
    if (!validateRes) {
      return;
    }
    saveFreezeInfo({
      params: {
        ...dataSet.current?.toData(),
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
        freezeApplyDs.setState('canEdit', false);
      },
    });
  };

  // 问题冻结申请提交
  const handleSubmitFreezeInfo = async dataSet => {
    const validateRes = await dataSet.current.validate(true);
    if (!validateRes) {
      return;
    }
    submitFreezeInfo({
      params: {
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 问题解冻
  const handleSubmitUnfreezeInfo = async enableFlag => {
    submitUnfreezeInfo({
      params: {
        problemId,
        rootReasonConfirmFlag: enableFlag,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 评分保存
  const handleSaveEvaluation = dataSet => {
    const { responPersonInfo, ...others } = dataSet.current?.toData();
    let sumQty = 0;
    const _list: any = [];
    responPersonInfo.forEach(item => {
      _list.push({ ...item, scoreRatio: item?.scoreRatio / 100 });
      if (item?.scoreRatio) {
        sumQty += item?.scoreRatio;
      }
    });
    if (sumQty !== 100) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.validation.scoreRatio`)
          .d('分配系数总和需等于100%，请检查！'),
      });
      return;
    }
    saveEvaluation({
      params: {
        ...others,
        responPersonInfo: _list,
        problemId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 发起市场活动评估
  const handleStartMarketAct = () => {
    const { problemId, problemCode, problemTitle } = problemInfoDs.current?.toData();
    const {
      severityLevel,
      batteryPackModel,
      materialId,
      materialCode,
      materialName,
      itemCode,
      itemName,
      hostPlant,
      hostPlantName,
      vehicleModel,
      siteId,
      siteCode,
      siteName,
    } = registerProblemDs.current?.toData();
    const {
      responsiblePersonUnitCompanyId,
      responsiblePersonUnitCompanyName,
    } = leadProblemDs.current?.toData();
    history.push({
      pathname: `/hwms/market-quality/market-estimate-doc/dist/create`,
      state: {
        problemId,
        problemCode,
        problemTitle,
        severityLevel,
        batteryPackModel,
        siteId,
        siteCode,
        siteName,
        materialId,
        materialCode,
        materialName,
        itemCode,
        itemName,
        hostPlant,
        hostPlantName,
        vehicleModel,
        responsiblePersonUnitCompanyId,
        responsiblePersonUnitCompanyName,
      },
    });
  };

  // 纳入检证库
  const handleAddVerificationLibrary = () => {
    return new Promise(async resolve => {
      const res = await getProblemAllData({
        params: { problemCode: problemInfoDs.current?.get('problemCode') },
      });

      if (!res?.content?.length) {
        return resolve(false);
      }
      props.history.push({
        pathname: `/hwms/verification-library-management-platform/dist/create`,
        state: { ...res?.content[0] },
      });
      return resolve(true);
    });
  };

  // 问题警示
  const handleAddProblemWarning = () => {
    const {
      problemCategory,
      problemId,
      problemCode,
      problemDescription,
    } = problemInfoDs.current?.toData();
    const {
      siteId,
      siteCode,
      siteName,
      productionLineId,
      prodLineName,
    } = registerProblemDs.current?.toData();
    const { severityLevel } = leadProblemDs.current?.toData();
    history.push({
      pathname: '/hwms/problem-management/warning/detail/create',
      state: {
        problemCategory,
        problemId,
        problemCode,
        problemDescription,
        siteId,
        siteCode,
        siteName,
        productionLineId,
        prodLineName,
        severityLevel,
        sourcePage: 'problem-management-platform',
      },
    });
  };

  // 问题横展
  const handleAddProblemExpand = () => {
    const {
      problemCategory,
      problemId,
      problemCode,
      problemDescription,
    } = problemInfoDs.current?.toData();
    const {
      siteId,
      siteCode,
      siteName,
      productionLineId,
      prodLineName,
    } = registerProblemDs.current?.toData();
    const { severityLevel } = leadProblemDs.current?.toData();
    history.push({
      pathname: '/hwms/problem-management/transverse-expand/detail/create',
      state: {
        problemCategory,
        problemId,
        problemCode,
        problemDescription,
        siteId,
        siteCode,
        siteName,
        productionLineId,
        prodLineName,
        severityLevel,
        sourcePage: 'problem-management-platform',
      },
    });
  };

  // 问题复盘
  const handleAddProblemDuplicate = () => {
    const {
      problemId,
      problemCode,
      problemTitle,
    } = problemInfoDs.current?.toData();
    const {
      siteId,
      siteCode,
      siteName,
    } = registerProblemDs.current?.toData();
    history.push({
      pathname: '/hwms/problem-management/duplicate-problem/detail/create',
      state: {
        problemId,
        problemCode,
        problemTitle,
        siteId,
        siteCode,
        siteName,
        sourcePage: 'problem-management-platform',
      },
    });
  };

  // 预关闭信息保存
  const handleSavePreCloseInfo = async (dataSet, reviewType) => {
    const valRes = await dataSet.validate();
    if (!valRes) {
      return;
    }
    savePreCloseInfo({
      params: {
        ...dataSet.current?.toData(),
        problemId,
        reviewType,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  // 预关闭/延期关闭信息提交
  const handleSubmitPreCloseInfo = async (dataSet, problemOvertimeCloseId) => {
    const valRes = await dataSet.validate();
    if (!valRes) {
      return;
    }
    submitPreCloseInfo({
      queryParams: {
        problemOvertimeCloseId,
      },
      onSuccess: () => {
        notification.success({});
        initData();
      },
    });
  };

  const stepContentComponentProps = {
    history,
    registerProblemDs,
    leadProblemDs,
    problemGroup,
    distributeDs,
    supplierInfoDs,
    tempMeasureDs,
    tempChangeObjectDs,
    reasonAnalysisDs,
    influenceRangeDs,
    marketActDs,
    freezeApplyDs,
    prepMeasureDs,
    enclosure8dDs,
    prepChangeObjectDs,
    problemCloseDs,
    preCloseDs,
    overtimeCloseTableDs,
    problemCloseTableDs,
    finallyScoreFormDs,
    finallyScoreTableDs,
    currentStep,
    userRole,
    currentUserId,
    problemStatus,
    enableFlag,
    srmFlag,
    perpSubmitFlag,
    measureEnableList,
    reasonConfirmList,
    handleSaveCategoryDetail,
    handleSubmitProblemInfo,
    handleSaveSupplierInfo,
    handleSaveMeasure,
    handleSubmitMeasure,
    handleChangeTotalEnable,
    handleSaveChangeObject,
    handleSaveReason,
    handleSubmitReason,
    handleChangeReasonEnable,
    handleSaveChangeRange,
    handleSaveEnclosure8d,
    handleChangeMarketFlag,
    handleSaveFreezeInfo,
    handleSubmitFreezeInfo,
    handleSubmitUnfreezeInfo,
    totalScoreInfo,
    handleUpdateTotalScore,
    handleSaveEvaluation,
    handleStartMarketAct,
    queryMaterialLotInfo,
    handleAddVerificationLibrary,
    handleAddProblemWarning,
    handleAddProblemExpand,
    handleAddProblemDuplicate,
    handleSavePreCloseInfo,
    handleSubmitPreCloseInfo,
  };

  const handleClickClose = () => {
    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <p>{intl.get(`${modelPrompt}.info.closeProblem`).d('当前问题是否进行关闭申请？')}</p>
      ),
    }).then(button => {
      if (button === 'ok') {
        closeProblem({
          params: {
            problemId,
          },
          onSuccess: () => {
            notification.success({});
            initData();
          },
        });
      }
    });
  };

  const handleConfirmSendMessage = () => {
    return new Promise(async (resolve) => {
      const valRes = await sendMessageDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      const params = sendMessageDs.current?.toData();
      delete params.receiveUserLov;
      const res = await sendWeChatMessage({
        params: {
          problemId,
          ...params,
        },
      })
      if (res?.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.notification.sendMessageSuccess`).d('消息发送成功'),
        })
        return resolve(true);
      }
      return resolve(false);
    })
  }

  const handleSendMessage = () => {
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.sendMessage`).d('消息抄送'),
      destroyOnClose: true,
      style: {
        width: 420,
      },
      afterClose: () => sendMessageDs?.reset(),
      onOk: handleConfirmSendMessage,
      children: (
        <Form dataSet={sendMessageDs}>
          <Lov name="receiveUserLov" />
          <TextArea name="message" autoSize={{ minRows: 4, maxRows: 8 }} />
        </Form>
      ),
    });
  }

  const stepSourceList = useMemo(
    () => [
      intl.get(`${modelPrompt}.step.registerProblem`).d('登记问题'),
      intl.get(`${modelPrompt}.step.distribute`).d('分配责任人'),
      intl.get(`${modelPrompt}.step.tempMeasure`).d('临时措施'),
      intl.get(`${modelPrompt}.step.reasonAnalysis`).d('原因分析'),
      intl.get(`${modelPrompt}.step.prepMeasure`).d('长期措施'),
      intl.get(`${modelPrompt}.step.validateClose`).d('验证关闭'),
    ],
    [],
  );

  const getRemainDaysContent = () => {
    return (
      <div>
        <p>{intl.get(`${modelPrompt}.delayDays.reasonAnalyse`).d('原因分析')}: {delayDaysInfo?.causeAnalysisDays || 0}{intl.get(`${modelPrompt}.delayDays.days`).d('天')}</p>
        <p>{intl.get(`${modelPrompt}.delayDays.measureEnact`).d('对策制定')}: {delayDaysInfo?.measureEnactDays || 0}{intl.get(`${modelPrompt}.delayDays.days`).d('天')}</p>
        <p>{intl.get(`${modelPrompt}.delayDays.resultVerification`).d('效果验证')}: {delayDaysInfo?.resultVerificationDays || 0}{intl.get(`${modelPrompt}.delayDays.days`).d('天')}</p>
        <p>{intl.get(`${modelPrompt}.delayDays.verifyClosed`).d('问题关闭')}: {delayDaysInfo?.verifyClosedDays || 0}{intl.get(`${modelPrompt}.delayDays.days`).d('天')}</p>
      </div>
    )
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.detail`).d('问题详情')}
        backPath={pubFlag ? '' : '/hwms/problem-management/problem-management-platform/list'}
      >
        <ApprovalInfoDrawer
          objectTypeList={[
            'QIS_PROBLEM_FREEZE_APPRO',
            'QIS_PROBLEM_CLOSE_APPRO',
            'QIS_PROBLEM_DELAY_CLOSE_APPRO',
            'QIS_PROBLEM_PRE_CLOSE_APPRO',
          ]}
          objectId={problemId}
        />
        {!['FREEZE', 'CLOSING', 'CLOSED'].includes(problemStatus) &&
          [2, 3, 4].includes(currentStep) && (
          <PermissionButton
            type="c7n-pro"
            icon="close"
            color={ButtonColor.primary}
            permissionList={[
              {
                code: `problemManagementPlatform.dist.button.close`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            loading={closeProblemLoading}
            onClick={handleClickClose}
            disabled={closeBtnDisabled || !userRole.includes('MAJOR_RESPONSIBLE_PERSON')}
          >
            {intl.get(`${modelPrompt}.button.closeApply`).d('关闭申请')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `problemManagementPlatform.dist.button.sendMessage`,
              type: 'button',
              meaning: '详情页-消息抄送按钮',
            },
          ]}
          loading={closeProblemLoading}
          onClick={() => handleSendMessage()}
          disabled={(!userRole.includes('LEAD_PERSON') && !userRole.includes('RESPONSIBLE_PERSON')) || problemStatus === 'CLOSED'}
        >
          {intl.get(`${modelPrompt}.button.sendMessage`).d('消息抄送')}
        </PermissionButton>
        <UserRoleComponent userRole={userRole} />
        {!['DRAFT', 'NEW', 'PUBLISH'].includes(problemStatus) && (
          <Tooltip title={getRemainDaysContent()} placement="bottom" theme="dark">
            <Icon className={styles['delay-days-icon']} type="timer" style={{ fontSize: 24 }} />
          </Tooltip>
        )}
      </Header>
      <Content>
        <TarzanSpin
          dataSet={problemInfoDs}
          spinning={
            loading ||
            saveLoading ||
            saveInfoLoading ||
            submitInfoLoading ||
            saveSupplierLoading ||
            savePersonLoading ||
            saveMeasureInfoLoading ||
            submitMeasureInfoLoading ||
            saveObjectLoading ||
            saveEnableLoading ||
            submitEnableLoading ||
            submitTotalLoading ||
            reasonSaveLoading ||
            reasonSubmitLoading ||
            saveReasonEnableLoading ||
            submitReasonEnableLoading ||
            reasonTotalLoading ||
            saveRangeLoading ||
            changeMarketLoading ||
            saveFreezeLoading ||
            submitFreezeLoading ||
            submitUnfreezeLoading ||
            closeProblemLoading ||
            saveEnableLoading ||
            saveEvaluationLoading ||
            queryLoading ||
            save8DLoading ||
            getProblemDataLoading ||
            sendMessageLoading ||
            saveNextReportDateLoading ||
            savePreCloseLoading ||
            submitPreCloseLoading
          }
        >
          <ProblemDetailCard
            dataSet={problemInfoDs}
            handleSaveBasicInfo={handleSaveBasicInfo}
            userRole={userRole}
            problemStatus={problemStatus}
          />
          <StepComponent
            current={currentStep}
            onChange={value => handleChangeStep(value, false)}
            sourceList={stepSourceList}
          />
          <StepContentComponent {...stepContentComponentProps} />
        </TarzanSpin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(ProblemManagementDetail);
