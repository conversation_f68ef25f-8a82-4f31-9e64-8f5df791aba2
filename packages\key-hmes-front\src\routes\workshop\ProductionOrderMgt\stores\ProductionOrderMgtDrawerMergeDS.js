/**
 * @Description: 生产指令管理抽屉 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2022-03-31 19:48:12
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.productionOrderMgt';
const tenantId = getCurrentOrganizationId();

const mergeDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/wo/qty/ui`,
        method: 'GET',
      };
    },
    submit: ({ dataSet }) => {
      const {
        workOrderId,
        workOrderNum,
        targetworkOrderId,
        secondaryWorkOrderIds,
      } = dataSet.current.toData();
      const orderIds = {};
      if (`${targetworkOrderId}` === `${workOrderNum}`) {
        orderIds.targetWorkOrderNum = targetworkOrderId;
      } else {
        if (targetworkOrderId) {
          orderIds.targetWorkOrderNum = targetworkOrderId;
        }
        if (workOrderId) {
          orderIds.primaryWorkOrderId = workOrderId;
        }
      }
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/workOrder/merge/ui`,
        method: 'POST',
        data: {
          ...orderIds,
          secondaryWorkOrderIds,
        },
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
  fields: [
    // 基本属性
    {
      name: 'targetworkOrderId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetWorkOrderId`).d('目标编码'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryWorkOrderId`).d('主编码'),
      disabled: true,
    },
    {
      name: 'secondaryWorkOrders',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.secondaryWorkOrderIds`).d('副编码'),
      required: true,
      defaultValue: {},
      lovCode: 'MT.MES.WORK_ORDER',
      textField: 'workOrderNum',
      valueField: 'workOrderId',
      noCache: true,
      ignore: 'always',
      multiple: true,
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
            status: record.get('status'),
            workOrderId: record.get('workOrderId'),
          };
        },
      },
    },
    {
      name: 'secondaryWorkOrderIds',
      type: FieldType.string,
      bind: 'secondaryWorkOrders.workOrderId',
    },
    {
      name: 'secondaryWorkOrderNum',
      type: FieldType.string,
      bind: 'secondaryWorkOrders.workOrderNum',
    },
  ],
});

const mergeListDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'workOrderId',
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order-rel/wo/rel/merge/ui`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderNum`).d('WO编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialRevision`).d('物料版本'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('WO状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=WO_STATUS&type=workOrderStatusOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.qty`).d('数量'),
      required: true,
      min: 0,
      precision: 2,
      defaultValue: 0,
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mergeTime`).d('合并时间'),
    },
  ],
});

export { mergeDS, mergeListDS };
