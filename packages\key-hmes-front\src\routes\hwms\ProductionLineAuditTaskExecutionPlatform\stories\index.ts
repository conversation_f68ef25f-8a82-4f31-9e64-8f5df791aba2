import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.prodlineReview.prodlineReviewTask';
// const prefix = '/tznq-9307';

// 检验组列表
const ListTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'verificationId',
  queryFields: [
    {
      name: 'prodlineRevplanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodlineRevplanCode`).d('产线审核计划编码'),
    },
    {
      name: 'lineRevplanTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineRevplanTaskCode`).d('产线审核任务编码'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lookupCode: 'YP.QIS.PRODLINE_REVPLAN_E_STATUS',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'YP.QIS.SITE_NAME',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'responsibleDeptLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleDept`).d('责任部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'responsibleDeptId',
      bind: 'responsibleDeptLov.unitId',
    },
    {
      name: 'responsibleEmLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleEm`).d('责任人'),
      lovCode: 'YP.QIS.UNIT_USER',
      ignore: FieldIgnore.always,
      disabled: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'responsibleEmId',
      bind: 'responsibleEmLov.id',
    },
    {
      name: 'reviewType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('审核类型'),
      lookupCode: 'YP.QIS.REVIEW_TYPE',
    },
    {
      name: 'reviewStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStage`).d('审核阶段'),
      lookupCode: 'YP.QIS.REVIEW_STAGE',
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
      lovPara: { tenantId },
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'prodLineIdList',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'createdByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      lovCode: 'YP.QIS.UNIT_USER',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      bind: 'createdByLov.id',
    },
  ],
  fields: [
    {
      name: 'prodlineRevplanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodlineRevplanCode`).d('产线审核计划编码'),
    },
    {
      name: 'lineRevplanTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineRevplanTaskCode`).d('产线审核任务编码'),
    },
    {
      name: 'statusMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'projectNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectNum`).d('项目编码'),
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
      lovPara: { tenantId },
    },
    {
      name: 'prodLineNameList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
    },
    {
      name: 'reviewTypeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('审核类型'),
    },
    {
      name: 'reviewStageMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStage`).d('审核阶段'),
    },
    {
      name: 'responsibleDeptName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleDept`).d('责任部门'),
    },
    {
      name: 'responsibleEmName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleEm`).d('责任人'),
    },
    {
      name: 'scheFinishTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scheFinishTime`).d('计划完成时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-review-task-platform/list/query`,
        method: 'GET',
      };
    },
  },
});

// 检验组详情
const DetailFormDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'qisProdlineReviewTmpId',
  fields: [
    {
      name: 'prodlineRevplanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodlineRevplanCode`).d('产线审核计划编码'),
    },
    {
      name: 'lineRevplanTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineRevplanTaskCode`).d('产线审核任务编码'),
    },
    {
      name: 'statusMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
      lovPara: { tenantId },
    },
    {
      name: 'prodLineNameList',
      type: FieldType.string,
      multiple: true,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
    },
    {
      name: 'reviewTypeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('审核类型'),
    },
    {
      name: 'reviewStageMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStage`).d('审核阶段'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
});

// 检验组关联检验项
const DetailTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  forceValidate: true,
  paging: false,
  primaryKey: 'prodReviewTmpElementId',
  fields: [
    {
      name: 'elementNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.elementNum`).d('产线审核要素编码'),
    },
    {
      name: 'statusMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'reviewDimensionMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewDimension`).d('评审维度'),
    },
    {
      name: 'reviewItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewItem`).d('审核项目'),
    },
    {
      name: 'reviewContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewContent`).d('评审要素'),
    },
    {
      name: 'deliveryName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.deliveryName`).d('交付物名称'),
    },
    {
      name: 'deliveryTemplate',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.deliveryTemplate`).d('交付物模板'),
      bucketName: 'qms',
    },
    {
      name: 'responsibleDeptName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleDeptName`).d('责任部门'),
    },
    {
      name: 'responsibleEmName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleEmName`).d('责任人'),
    },
    {
      name: 'scheFinishTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scheFinishTime`).d('计划完成时间'),
    },
    {
      name: 'deliveryUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.deliveryUuid`).d('交付物'),
      required: true,
      bucketName: 'qms',
    },
    {
      name: 'applyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyFlag`).d('是否适用'),
      lookupCode: 'YP.QIS.Y_N',
    },
    {
      name: 'noApplyReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.noApplyReason`).d('不适用原因'),
      dynamicProps: {
        required: ({ record }) => record.get('applyFlag') === 'N',
      },
    },
    {
      name: 'actualFinishTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualFinishTime`).d('实际完成时间'),
    },
    {
      name: 'reviewResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewResult`).d('评审结果'),
    },
    {
      name: 'nonConTerm',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.nonConTerm`).d('不符合项'),
    },
    {
      name: 'releaseClass',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.releaseClass`).d('放行等级'),
    },
    {
      name: 'closeDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.closeDate`).d('审核关闭时间'),
    },
  ],
});

export { ListTableDS, DetailFormDS, DetailTableDS };
