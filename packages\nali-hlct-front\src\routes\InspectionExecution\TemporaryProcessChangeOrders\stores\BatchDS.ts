import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`;
const tenantId = getCurrentOrganizationId();


const BatchDS = (): DataSetProps => ({
  autoCreate: false,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectionItem`).d('检验项目'),
      name: 'inspectionItemObject',
      lovCode: 'MT.QMS.INSPECT_ITEM_INFO',
      multiple: true,
      lovPara: {
        tenantId,
        dataType: 'CALCULATE_FORMULA',
      },
    },
  ],
});


const inspectionItemBasisDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  autoLocateFirst: true,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'inspectBusinessType',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'inspectBusinessTypeDesc',
      bind: 'inspectBusinessTypeObject.inspectBusinessTypeDesc',
    },
    {
      name: 'inspectBusinessTypeRuleId',
      bind: 'inspectBusinessTypeObject.inspectBusinessTypeRuleId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectionDimension`).d('检验维度'),
      name: 'inspectionDimensionObject',
      multiple: true,
    },
    {
      name: 'inspectionDimension',
      bind: 'inspectionDimensionObject.value',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectionItem`).d('检验项目'),
      name: 'inspectionItemObject',
      lovCode: 'MT.QMS.INSPECT_ITEM_INFO',
      multiple: true,
      lovPara: { tenantId, dataType: 'CALCULATE_FORMULA' },
    },
  ],
});

const ExtensionDS = ():DataSetProps=>({
  autoCreate: true,
  fields: [
    {
      name: 'amendDate',
      
      required: true,
      type: FieldType.dateTime,
      min: new Date(),
      dynamicProps: {
        max:({dataSet})=>{
          return dataSet.getState('maxDate');
        },
        label:({dataSet})=> dataSet.getState('type')==='END'?
          intl.get(`${modelPrompt}.amendDates`).d('中止日期'):
          intl.get(`${modelPrompt}.amendDate`).d('延期日期'),
      },
    },
    {
      name: 'amendRemark',
      required: true,
      type: FieldType.string,
      dynamicProps: {
        label:({dataSet})=> dataSet.getState('type')==='END'?
          intl.get(`${modelPrompt}.amendRemarks`).d('中止说明'):
          intl.get(`${modelPrompt}.amendRemark`).d('延期说明'),
      },
    },
    {
      name: 'enclosure',
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      type: FieldType.string,
    },
  ],
});

export {
  BatchDS,
  inspectionItemBasisDS,
  ExtensionDS,
};