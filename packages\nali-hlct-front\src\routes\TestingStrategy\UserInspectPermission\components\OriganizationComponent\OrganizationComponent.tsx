/**
 * @Description: 用户权限-组织权限Tab页
 * @Author: <EMAIL>
 * @Date: 2023/3/29 20:28
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Row, Spin, DataSet } from 'choerodon-ui/pro';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import { observer } from 'mobx-react';
import OrganizationTree from './OrganizationTree';
import OrganizationTable from './OrganizationTable';
import { DistributeButton, RevokeButton } from './Buttons';
import { organizationTableDS } from '../../stores/detailTableDS';
import { UserTree } from '../../services';
import styles from './index.module.less';

export default observer(({ tableDs, listDs, canEdit, handleUpdateTabDisabled }) => {
  const siteId = tableDs.current?.get('siteId');
  const userId = tableDs.current?.get('userId');
  const permissionFlag = listDs.current?.get('permissionFlag');
  const organizationTableDs = useMemo(
    () =>
      new DataSet({
        ...organizationTableDS(canEdit && permissionFlag === 'Y'),
        data: listDs.selected?.length > 1 ? null : listDs.current?.get('organizationList'),
      }),
    [listDs.current?.id, listDs.selected?.length, canEdit, permissionFlag],
  );
  const [orgTreeData, setOrgTreeData] = useState([]);
  const [expendKey, setExpendKey] = useState([]);
  const getOrgTree = useRequest(UserTree(), { manual: true });

  useEffect(() => {
    if (!siteId) {
      return;
    }
    getOrgTree.run({
      params: {
        orgId: siteId,
        userId: tableDs.current?.get('userId'),
      },
      onSuccess: res => {
        setOrgTreeData(res);
        const expendKey: any = [];
        const getExpendList = nodes => {
          if (!nodes || !nodes?.length) {
            return;
          }
          nodes.forEach(item => {
            expendKey.push(String(item?.organizationId));
            if (item.subUserOrgRelList?.length) {
              getExpendList(item.subUserOrgRelList);
            }
          });
        };
        getExpendList(res);
        setExpendKey(expendKey || []);
      },
    });
  }, [siteId, userId]);

  return (
    <div className={styles.treeTableWrap}>
      <div className={styles.leftTree}>
        <Spin spinning={getOrgTree.loading}>
          <OrganizationTree
            ds={listDs}
            orgTreeConfig={orgTreeData || []}
            organizationTableDs={organizationTableDs}
            canEdit={canEdit && permissionFlag === 'Y'}
            expendKey={expendKey}
          />
        </Spin>
      </div>
      <div className={styles.centerButton}>
        <Row className={styles.centerButtonRow}>
          <DistributeButton
            ds={listDs}
            className={styles.centerButtonItem}
            organizationTableDs={organizationTableDs}
            canEdit={canEdit && permissionFlag === 'Y'}
            handleUpdateTabDisabled={handleUpdateTabDisabled}
          />
        </Row>
        <Row className={styles.centerButtonRow}>
          <RevokeButton
            ds={listDs}
            className={styles.centerButtonItem}
            organizationTableDs={organizationTableDs}
            canEdit={canEdit && permissionFlag === 'Y'}
            handleUpdateTabDisabled={handleUpdateTabDisabled}
          />
        </Row>
      </div>
      <div className={styles.rightTable}>
        <OrganizationTable
          ds={listDs}
          organizationTableDs={organizationTableDs}
          canEdit={canEdit && permissionFlag === 'Y'}
        />
      </div>
    </div>
  );
});
