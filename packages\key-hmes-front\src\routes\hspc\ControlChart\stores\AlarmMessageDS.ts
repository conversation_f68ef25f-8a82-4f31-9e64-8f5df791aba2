/**
 * @Description: 站点维护DS
 * @Author: <<EMAIL>>
 * @Date: 2021-02-02 15:48:05
 * @LastEditTime: 2021-12-13 18:17:31
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { HSPC_BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hspc.controlChartMaintain';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'configureName',
      label: intl.get(`${modelPrompt}.configureName`).d('配置名称'),
    },
    {
      name: 'messageType',
      label: intl.get(`${modelPrompt}.messageType`).d('发送类型'),
      lookupCode: 'MT.SPC.SEND_TYPE',
    },
    {
      name: 'emailServerCodeObj',
      ignore: FieldIgnore.always,
      noCache: true,
      label: intl.get(`${modelPrompt}.emailServerCode`).d('邮箱账户'),
      lovCode: 'MT.SPC.EMAIL_SERVER_CODE',
      type: FieldType.object,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => record.get('messageType') === 'EMAIL',
      },
    },
    {
      name: 'emailServerCode',
      bind: 'emailServerCodeObj.serverCode',
    },
    {
      name: 'enableFlag',
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
    {
      name: 'receiverGroupCode',
      label: intl.get(`${modelPrompt}.receiverGroupCode`).d('接收组'),
    },
    {
      name: 'messageCommand',
      label: intl.get(`${modelPrompt}.messageCommand`).d('配置命令'),
    },
  ],
});

const messageInfoDS: () => DataSetProps = () => ({
  autoCreate: true,
  selection: false,
  autoQuery: false,
  paging: false,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'configureName',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.configureName`).d('配置名称'),
    },
    {
      name: 'messageType',
      label: intl.get(`${modelPrompt}.messageType`).d('发送类型'),
      required: true,
      lookupCode: 'MT.SPC.SEND_TYPE',
    },
    {
      name: 'emailServerCodeObj',
      ignore: FieldIgnore.always,
      noCache: true,
      label: intl.get(`${modelPrompt}.emailServerCode`).d('邮箱账户'),
      lovCode: 'MT.SPC.EMAIL_SERVER_CODE',
      type: FieldType.object,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => record.get('messageType') === 'EMAIL',
        disabled: ({ record }) => record.get('messageType') !== 'EMAIL',
      },
    },
    {
      name: 'emailServerCode',
      bind: 'emailServerCodeObj.serverCode',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'templateCodeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.templateCode`).d('消息模板'),
      lovCode: 'MT.SPC.MESSAGE_TEMPLATE',
      noCache: true,
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'templateCode',
      bind: 'templateCodeObj.templateCode',
    },
    {
      name: 'receiverGroupCodeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.receiverGroupCode`).d('接收组'),
      lovCode: 'MT.SPC.RECEIVER_GROUP',
      ignore: FieldIgnore.always,
      noCache: true,
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'receiverGroupCode',
      bind: 'receiverGroupCodeObj.typeCode',
    },
    {
      name: 'messageCommand',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.messageCommand`).d('配置命令'),
    },
  ],
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.hspc.basic.domain.entity.Message';
      return {
        data: { messageId: record.data.messageId },
        params: { fieldName, className },
        url: `${HSPC_BASIC}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  events: {
    update({ record, name, value }) {
      if (name === 'messageType' && value !== 'EMAIL') {
        record.set('emailServerCodeObj', undefined);
      }
    },
  },
});

export { tableDS, messageInfoDS };
