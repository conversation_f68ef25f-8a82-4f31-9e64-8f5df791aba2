/**
 * @Description: 事件请求类型维护-service
 * @Author: <EMAIL>
 * @Date: 2022-09-06 15:49:50
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 事件请求类型维护-保存
export function SaveEventRequestType() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-request-type/save/ui`,
    method: 'POST',
  };
}
