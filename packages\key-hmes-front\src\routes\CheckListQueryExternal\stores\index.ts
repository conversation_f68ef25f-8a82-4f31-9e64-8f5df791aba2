/**
 * @Description: 检验单维护-列表页DS
 * @Author: <EMAIL>
 * @Date: 2023/2/13 10:47
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.hwms.CheckListQueryExternal';
const tenantId = getCurrentOrganizationId();

const headQueryDS: () => DataSetProps = () => ({
  fields: [
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单编码'),
      multiple: true,
    },
    {
      name: 'inspectDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('检验单状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSPECT_DOC_STATUS&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-bus-type-rule/lov`,
      textField: 'inspectBusinessTypeDesc',
      valueField: 'inspectBusinessType',
      dynamicProps: {
        disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          page: -1,
        }),
      },
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'inspectSchemeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_SCHEME',
      lovPara: { tenantId },
    },
    {
      name: 'inspectSchemeId',
      bind: 'inspectSchemeLov.inspectSchemeId',
    },
    {
      name: 'docCreateMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docCreateMethod`).d('单据创建方式'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.DOC_CREATE_METHOD',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'firstInspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstInspectResult`).d('初评结果'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.INSPECT_RESULT',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'lastInspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastInspectResult`).d('最终结果'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.INSPECT_RESULT',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'inspectTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTaskCode`).d('检验任务编码'),
    },
    {
      name: 'inspectTaskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTaskStatus`).d('检验任务状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSPECT_TASK_STATUS&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'inspectTaskType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTaskType`).d('检验任务类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSPECT_TASK_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'inspectInfoCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfoCode`).d('报检单编号'),
    },
    {
      name: 'inspectInfoUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectInfoUserName`).d('报检人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
    },
    {
      name: 'inspectInfoUserId',
      bind: 'inspectInfoUserLov.id',
    },
    {
      name: 'inspectInfoCreationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inspectInfoCreationDateFrom`).d('报检时间从'),
      max: 'inspectInfoCreationDateTo',
    },
    {
      name: 'inspectInfoCreationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inspectInfoCreationDateTo`).d('报检时间至'),
      min: 'inspectInfoCreationDateFrom',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'actualStartTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTimeFrom`).d('实际开始时间从'),
      max: 'actualStartTimeTo',
    },
    {
      name: 'actualStartTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTimeTo`).d('实际开始时间至'),
      min: 'actualStartTimeFrom',
    },
    {
      name: 'actualEndTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndTimeFrom`).d('实际结束时间从'),
      max: 'actualEndTimeTo',
    },
    {
      name: 'actualEndTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndTimeTo`).d('实际结束时间至'),
      min: 'actualEndTimeFrom',
    },
    // {
    //   name: 'reviewStatus',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reviewStatus`).d('审核状态'),
    //   lovPara: { tenantId },
    //   lookupCode: 'MT.QMS.REVIEW_STATUS',
    //   textField: 'meaning',
    //   valueField: 'value',
    // },
    {
      name: 'reviewUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reviewUserName`).d('审核人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
    },
    {
      name: 'reviewUserId',
      bind: 'reviewUserLov.id',
    },
    {
      name: 'materialLotCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eo`).d('执行作业'),
      // ignore: FieldIgnore.always,
      // lovCode: 'MT.EO',
      // lovPara: { tenantId },
    },
    // {
    //   name: 'eoNum',
    //   bind: 'eoLov.eoNum',
    // },
    {
      name: 'containerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.container`).d('容器'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.CONTAINER_MATERIALLOT',
      lovPara: { tenantId },
    },
    {
      name: 'containerId',
      bind: 'containerLov.containerId',
    },
    {
      name: 'materialLotCodes',
      bind: 'containerLov.materialLotCodes',
    },
    {
      name: 'eoNums',
      bind: 'containerLov.eoNums',
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('厂内批次'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('物料BOM'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        workcellType: 'PROCESS',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'processWorkcellId',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'stationWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.stationWorkcellName`).d('工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        workcellType: 'STATION',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'stationWorkcellId',
      bind: 'stationWorkcellLov.workcellId',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'areaLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaName`).d('区域'),
      lovCode: 'MT.MODEL.AREA',
      textField: 'areaName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'areaId',
      bind: 'areaLov.areaId',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'description',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      textField: 'locatorName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
  ],
  events: {
    update: ({ record, name }) => {
      if (name === 'materialLov' && record.get('materialLov')) {
        const { materialBom, model } = record.get('materialLov');
        if (materialBom) {
          record.set('bomCode', materialBom);
          record.getField('bomCode').set('disabled', true);
        }
        if (model) {
          record.set('model', model);
          record.getField('model').set('disabled', true);
        }
      }
      if (name === 'materialLov' && !record.get('materialLov')) {
        record.set('model', null);
        record.getField('model').set('disabled', false);
        record.set('bomCode', null);
        record.getField('bomCode').set('disabled', false);
      }
    },
  },
});

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectDocId',
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-inspect-doc/list/ui`,
        method: 'GET',
        data: {
          ...data,
          materialLotCodes:
            data?.containerId && !data?.materialLotCodes?.length ? [''] : data?.materialLotCodes,
          eoNums: data?.containerId && !data?.eoNums?.length ? [''] : data?.eoNums,
        },
      };
    },
  },
  fields: [
    {
      name: 'inspectDocId',
      type: FieldType.number,
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单编码'),
    },
    {
      name: 'inspectDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('检验单状态'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
    },
    {
      name: 'inspectSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
    },
    {
      name: 'docCreateMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docCreateMethod`).d('单据创建方式'),
    },
    {
      name: 'firstInspectResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstInspectResult`).d('初评结果'),
    },
    {
      name: 'lastInspectResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastInspectResult`).d('最终结果'),
    },
    {
      name: 'inspectInfoCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfoCode`).d('报检单编号'),
    },
    {
      name: 'inspectInfoUserNames',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfoUserName`).d('报检人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'actualStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('实际开始时间'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际结束时间'),
    },
    // {
    //   name: 'reviewStatusDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reviewStatus`).d('审核状态'),
    // },
    // {
    //   name: 'reviewUserName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reviewUserName`).d('审核人'),
    // },
    // {
    //   name: 'reviewTime',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reviewTime`).d('审核时间'),
    // },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('物料BOM'),
    },
    {
      name: 'disposalProposal',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalProposal`).d('处置建议'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'processWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序'),
    },
    {
      name: 'stationWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stationWorkcellName`).d('工位'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'areaName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaName`).d('区域'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备'),
    },
  ],
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectTaskId',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-inspect-doc/list-line/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'inspectTaskId',
      type: FieldType.number,
    },
    {
      name: 'inspectTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTaskCode`).d('检验任务编码'),
    },
    {
      name: 'inspectTaskStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTaskStatus`).d('检验任务状态'),
    },
    {
      name: 'inspectTaskTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTaskType`).d('检验任务类型'),
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResult`).d('检验结果'),
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检验员'),
    },
    {
      name: 'sourceTaskId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.recheckTaskFlag`).d('复检任务标识'),
    },
    {
      name: 'outsourceSupplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsourceSupplier`).d('委外供应商'),
    },
    {
      name: 'actualStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('实际开始时间'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际结束时间'),
    },
    {
      name: 'scrapQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapQty`).d('报废数'),
    },
    {
      name: 'okQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.okQty`).d('合格数'),
    },
    {
      name: 'ngQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngQty`).d('不合格数'),
    },
    {
      name: 'sourceTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceTaskCode`).d('来源检验任务'),
    },
  ],
});

const drawerDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectInfoDtlId',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-inspect-doc/list-detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          (rows?.content || [])?.forEach((item, index) => {
            item.sequence = index * 10 + 10;
          });
          return { rows };
        },
      };
    },
  },
  fields: [
    {
      name: 'inspectInfoDtlId',
      type: FieldType.number,
    },
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.sequence`).d('序号'),
    },
    {
      name: 'objectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.objectType`).d('报检对象类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSPECT_SCAN_OBJECT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'objectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.objectType`).d('报检对象类型'),
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.objectCode`).d('报检对象编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('报检对象标识'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
    },
    {
      name: 'identifyType',
      type: FieldType.string,
    },
    {
      name: 'maxQuantity',
      type: FieldType.number,
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.inspectInfo.objectQuantity`).d('报检对象数量'),
      dynamicProps: {
        required: ({ record }) => ['MAT', 'LOT'].includes(record.get('objectType')),
        max: ({ record }) => record.get('maxQuantity'),
      },
    },
    {
      name: 'uomId',
      type: FieldType.number,
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.uomName`).d('单位'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'locatorId',
      type: FieldType.number,
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.locatorName`).d('库位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.lot`).d('厂内批次'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.supplierLot`).d('供应商批次'),
    },
    {
      name: 'sourceObjectType',
      type: FieldType.string,
    },
    {
      name: 'sourceObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.sourceObjectType`).d('关联来源单据类型'),
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.sourceObjectCodeAndLine`).d('关联来源单据/行号'),
    },
    {
      name: 'sourceObjectLineCode',
      type: FieldType.string,
    },
    {
      name: 'inspectInfoCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfo.inspectInfoCode`).d('关联报检单'),
    },
    {
      name: 'deleteFlag',
      type: FieldType.string,
    },
  ],
});

export { headDS, lineDS, drawerDS, headQueryDS };
