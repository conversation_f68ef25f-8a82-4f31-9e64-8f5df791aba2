.pdf-container {
  width: 10px;
  height: 10px;
  overflow: auto;
  position: absolute;
  z-index: -1;
  top: 10;
  left: 10;
  #pdf {
    width: 842px;
    font-size: 14px;
    table {
      tr {
        border-left: 1px solid black;
        border-right: 1px solid black;
        &:first-child {
          td {
            border-top: 2px solid black;
          }
        }
        &:last-child {
          td {
            border-bottom: 2px solid black;
          }
        }
        td {
          width: calc(1 / 6 * 842px);
          border: 1px solid black;
        }
        .width-1 {
          width: calc(1 / 18 * 842px);
        }
        .width-2 {
          width: calc(3 / 18 * 842px);
        }
        .width-3 {
          width: calc(5 / 18 * 842px);
        }
        .width-4 {
          width: calc(9 / 18 * 842px);
        }
      }
    }
    .pdf-page-height {
      height: 568px;
    }
    .img-div {
      .line-number {
        height: 20px;
        line-height: 20px;
      }
      .img-container {
        max-width: 100%;
        text-align: center;
        margin-bottom: 10px;

        .image-element {
          max-width: 100%;
          max-height: 100%;
          height: auto;
          display: inline-block;
        }
      }
    }
  }
}
