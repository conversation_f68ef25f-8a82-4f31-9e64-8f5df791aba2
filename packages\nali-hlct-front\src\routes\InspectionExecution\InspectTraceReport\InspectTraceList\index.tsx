/**
 * @Description: 检验追溯报表-主界面
 * @Author: <EMAIL>
 * @Date: 2024/1/12 10:43
 */
import React, { FC, useMemo } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { Badge, Collapse, Tag } from 'choerodon-ui';
import { DataSet, Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { API_HOST, BASIC } from '@utils/config';
import { isNil } from 'lodash';
import { headDS, lineDS } from '../stores';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.hwms.inspectTraceReport';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

interface InspectTraceListProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
  customizeTable: any;
}

const InspectTraceList: FC<InspectTraceListProps> = props => {
  const {
    headDs,
    lineDs,
    history,
  } = props;

  useDataSetEvent(headDs.queryDataSet, 'update', ({ name, record }) => {
    if (name === 'traceObjectType') {
      record?.init('traceObjectCodeLov', undefined);
      record?.init('inspectBusinessTypeLov', undefined);
    }
  });

  // 头列表加载
  const resetHeaderDetail = () => {
  // 头加载时，行也全查询
    queryLineTable(null);
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = record => {
    const headQueryParam = headDs?.queryDataSet?.current;
    if (record?.get('inspectTaskId')) {
      lineDs.setQueryParameter('inspectDocId', record?.get('inspectDocId'));
      lineDs.setQueryParameter('inspectTaskId', record?.get('inspectTaskId'));
      lineDs.setQueryParameter('traceObjectType', headQueryParam?.get('traceObjectType'));
      lineDs.setQueryParameter('traceObjectCode', headQueryParam?.get('traceObjectCode'));
      lineDs.setQueryParameter('inspectBusinessTypeList', headQueryParam?.get('inspectBusinessTypeList'));
    } else {
      // 行全查
      lineDs.setQueryParameter('inspectDocId', 0);
      lineDs.setQueryParameter('inspectTaskId', 0);
      lineDs.setQueryParameter('traceObjectType', headQueryParam?.get('traceObjectType'));
      lineDs.setQueryParameter('traceObjectCode', headQueryParam?.get('traceObjectCode'));
      lineDs.setQueryParameter('inspectBusinessTypeList', headQueryParam?.get('inspectBusinessTypeList'));
    }
    lineDs.query();
  };

  const renderTag = (value, record) => {
    switch (record.get('inspectDocStatus')) {
      case 'NEW':
      case 'RELEASED':
        return <Tag color="green">{value}</Tag>;
      case 'INSPECTING':
      case 'REINSPECTING':
        return <Tag color="yellow">{value}</Tag>;
      case 'LAST_COMPLETED':
        return <Tag color="geekblue">{value}</Tag>;
      case 'FIRST_COMPLETED':
        return <Tag color="blue">{value}</Tag>;
      default:
        return <Tag color="gray">{value}</Tag>;
    }
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectTaskCode',
        lock: ColumnLock.left,
        width: 180,
      },
      { name: 'inspectBusinessTypeDesc', width: 150 },
      {
        name: 'inspectTaskStatusDesc',
        width: 150,
        renderer: ({ value, record }) => renderTag(value, record),
      },
      { name: 'inspectResultDesc', width: 150 },
      { name: 'inspectorName', width: 150 },
      { name: 'actualStartTime', width: 150, align: ColumnAlign.center },
      { name: 'actualEndTime', width: 150, align: ColumnAlign.center },
      {
        name: 'inspectDocNum',
        width: 200,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hwms/inspect-doc-maintain/dist/${record!.get('inspectDocId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'inspectDocStatusDesc',
        renderer: ({ value, record }) => renderTag(value, record),
      },
      { name: 'siteName' },
      { name: 'firstInspectResultDesc' },
      { name: 'lastInspectResultDesc' },
      {
        name: 'inspectInfoUserName',
        width: 180,
      },
      { name: 'disposalTypeDesc' },
      { name: 'dispFunctionDesc' },
      { name: 'dispFunctionQty' },
      { name: 'disposalUserName' },
      { name: 'disposalTime' },
      { name: 'reviewStatusDesc' },
      { name: 'reviewUserName' },
      { name: 'reviewTime', width: 150, align: ColumnAlign.center },
      { name: 'materialName' },
      { name: 'materialCode' },
      { name: 'revisionCode' },
      { name: 'customerName' },
      { name: 'supplierName' },
      { name: 'processWorkcellName' },
      { name: 'stationWorkcellName' },
      { name: 'operationName' },
      { name: 'areaName' },
      { name: 'prodLineName' },
      { name: 'locatorName' },
      { name: 'equipmentName' },
      {
        name: 'urgentFlag',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('urgentFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('urgentFlag') === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectItemDesc',
        width: 150,
        lock: ColumnLock.left,
      },
      { name: 'dataTypeDesc' },
      {
        name: 'trueValue',
        width: 200,
        renderer: ({ record, name }) => renderValueList(name, record),
      },
      {
        name: 'falseValue',
        width: 200,
        renderer: ({ record, name }) => renderValueList(name, record),
      },
      {
        name: 'warningValue',
        width: 200,
        renderer: ({ record, name }) => renderValueList(name, record),
      },
      {
        name: 'inspectValue',
        width: 150,
        renderer: ({ record }) => renderInspectValue(record),
      },
      {
        name: 'inspectResultDesc',
        renderer: ({ record, value }) => {
          switch (record?.get('inspectResult')) {
            case 'OK':
              return <Tag color="green">{value}</Tag>;
            case 'NG':
              return <Tag color="magenta">{value}</Tag>;
            default:
              return null;
          }
        },
      },
    ];
  }, []);

  const renderValueList = (name, record) => {
    const value = record.get(name);
    switch (record.get('dataType')) {
      case 'VALUE':
      case 'VALUE_LIST':
      case 'DECISION_VALUE':
        return (
          value?.length &&
          value[0] &&
          value.map(item => (
            <Tag className="hcm-tag-green" key={item}>
              {item}
            </Tag>
          ))
        );
      default:
        return value?.length && value[0].dataValue;
    }
  };

  const renderInspectValue = record => {
    const data = record?.get('inspectValueList');
    if (!data || !data?.length) {
      return null;
    }

    return data.map(item => {
      const formatInspectValue = item => {
        const res = item.inspectResult?.split(',');
        const val = item.inspectValue?.split(',');

        const valueColor = i => {
          switch (res[i]) {
            case 'OK':
              return 'rgba(32, 212, 137, 1)';
            case 'NG':
              return 'rgba(230, 46, 163, 1)';
            default:
              return 'rgba(0, 0, 0, 0.85)';
          }
        };

        return val.map((i, index) => {
          if (index === val?.length - 1) {
            return <span style={{ color: valueColor(index) }}>{i}</span>;
          }
          return <span style={{ color: valueColor(index) }}>{i},</span>;
        });
      };

      return (
        <Tag>
          {item?.inspectObjectCode && <span>{item?.inspectObjectCode}</span>}(
          {formatInspectValue(item)})
        </Tag>
      );
    });
  };

  const headerRowClick = record => {
    queryLineTable(record);
  };

  const getExportQueryParams = () => {
    if (!headDs.queryDataSet || !headDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = headDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('检验追溯报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/quality-trace/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content className={styles['inspection-platform']}>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            queryFieldsLimit: 8,
          }}
          dataSet={headDs}
          columns={[]}
          searchCode="InspectionPlatform"
          customizedCode="InspectionPlatform"
          className={styles['inspection-platform-search-table']}
        />
        <Collapse bordered={false} defaultActiveKey={['taskInfo']}>
          <Panel
            key="taskInfo"
            header={intl.get(`${modelPrompt}.title.docInfo`).d('检验单据信息')}
          >
            <Table
              queryBar={TableQueryBarType.none}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={headDs}
              columns={headColumns}
              style={{ height: 220 }}
              customizedCode="inspectTraceReport-listHeader"
              onRow={({ record }) => ({
                onClick: () => headerRowClick(record),
              })}
            />
          </Panel>
        </Collapse>
        <Collapse bordered={false} defaultActiveKey={['taskInfo']}>
          <Panel
            key="taskInfo"
            header={intl.get(`${modelPrompt}.title.projectInfo`).d('检验项目信息')}
          >
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              searchCode="inspectTraceReport-listLine"
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="inspectTraceReport-listLine"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_TRACE_LIST.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_TRACE_LIST.LIST`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_TRACE_LIST.TASK_LIST`,
      ],
    })(InspectTraceList as any),
  ),
);
