import React, { useMemo, useEffect } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Collapse, Tag, Popover, Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Tooltip } from 'choerodon-ui/pro/lib/core/enum';
import { useDataSetEvent } from 'utils/hooks';
import { tableDS, tableQueryDS, lineTableDS } from './stores';
import styles from './index.modules.less';

const modelPrompt = 'NcReviewManagementPlatform';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

const NcReviewManagementPlatform = (props) => {
  const {
    tableDs,
    lineTableDs,
    history,
  } = props;


  useEffect(() => {
    request(`/tznm/v1/${tenantId}/mt-mod-site/user-organization/site/lov/ui?page=0&size=1000`, {
      method: 'GET',
    }).then(res => {
      if (res && res.content && res.content.length === 1) {
        tableDs.queryDataSet.current?.set('siteLov',
          {
            siteId: res.content[0].siteId,
            siteCode: res.content[0].siteCode,
            siteName: res.content[0].siteName,
          });
      } else {
        tableDs.queryDataSet.current?.set('siteLov', {});
      }
    });
  }, [])

  useDataSetEvent(tableDs, 'load', () => {
    const id = tableDs.current?.get('ncReviewId');
    if (id) {
      handleClickRow(id);
    }
  })

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'ncReviewCode',
        width: 120,
      },
      {
        name: 'siteCode',
      },
      {
        name: 'siteName',
      },
      {
        name: 'approveStatusMeaning',
      },
      {
        name: 'inspectDocCode',
        renderer: ({ record, value }) => (<a onClick={() => { handleClick(record?.get('inspectDocId')) }}>{value}</a>),
      },
      {
        name: 'materialCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'extendMonth',
      },
      {
        name: 'supplyFlag',
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`${modelPrompt}.enable`).d('是')
                : intl.get(`${modelPrompt}.disable`).d('否')
            }
          />
        ),
      },
      {
        name: 'disposalProposal',
      },
      {
        name: 'disposalMethod',
      },
      {
        name: 'name',
      },
      {
        name: 'bomCode',
      },
      {
        name: 'model',
      },
      {
        name: 'lot',
      },
      {
        name: 'supplierCode',
      },
      {
        name: 'supplierName',
      },
      {
        name: 'defectiveRatePercent',
      },
      {
        name: 'applicantName',
      },
      {
        name: 'applicationDate',
      },
      {
        name: 'ncReason',
        width: 120,
      },
      {
        name: 'creatorName',
      },
      {
        name: 'creationDate',
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = [
    {
      name: 'orderSeq',
    },
    {
      name: 'sourceInspectItemDesc',
    },
    {
      name: 'inspectionStandard',
      tooltip: Tooltip.none,
      renderer: ({ record }) => {
        return (
          <Popover
            placement="top"
            content={
              <div>
                {(record?.get('trueValues') || []).map(item => (
                  <div
                    className={styles['table-tooltip-tag']}
                    style={{ color: '#11d954', backgroundColor: '#E6FFEA' }}
                  >
                    {item}
                  </div>
                ))}
                {(record?.get('falseValues') || []).map(item => (
                  <div
                    className={styles['table-tooltip-tag']}
                    style={{ color: '#f23a50', backgroundColor: '#fff0f0' }}
                  >
                    {item}
                  </div>
                ))}
                {(record?.get('warningValues') || []).map(item => (
                  <div
                    className={styles['table-tooltip-tag']}
                    style={{ color: '#fbad00', backgroundColor: '#fffbe6' }}
                  >
                    {item}
                  </div>
                ))}
              </div>
            }
            trigger="hover"
          >
            <div>
              {(record?.get('trueValues') || []).map(item => (
                <Tag color="green">{item}</Tag>
              ))}
              {(record?.get('falseValues') || []).map(item => (
                <Tag color="red">{item}</Tag>
              ))}
              {(record?.get('warningValues') || []).map(item => (
                <Tag color="yellow">{item}</Tag>
              ))}
            </div>
          </Popover>
        );
      },
    },
    {
      name: 'inspectValue',
    },
    {
      name: 'inspectResultMeaning',
    },
  ];

  const handleClick = (id) => {
    history.push(`/hwms/inspect-doc-maintain/dist/${id}`);
  }

  const handleClickRow = (id) => {
    lineTableDs.setQueryParameter('ncReviewId', id)
    lineTableDs.query();
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('不良评审单管理平台')} />
      <Content className={styles['ncReview-management-platform']}>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={[]}
          searchCode="NcReviewManagementPlatform"
          className={styles['ncReview-management-platform-search-table']}
        />
        <Collapse defaultActiveKey={['1','2']}>
          <Panel header={intl.get(`${modelPrompt}.informationAdverseRreviewSheet`).d('不良评审单信息')} key="1">
            <Table
              queryBar={TableQueryBarType.none}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              customizedCode="NcReviewManagementPlatform"
              dataSet={tableDs}
              columns={columns}
              onRow={({ record }) => ({
                onClick: () => {
                  handleClickRow(record.get('ncReviewId'));
                },
              })}
            />
          </Panel>
          <Panel header={intl.get(`${modelPrompt}.inspectionItemInformation`).d('检验项目信息')} key="2">
            <Table
              customizedCode="NcReviewManagementPlatformLine"
              dataSet={lineTableDs}
              columns={lineColumns}
            />
          </Panel>
        </Collapse>,
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableQueryDs = new DataSet({
        ...tableQueryDS(),
      })
      const tableDs = new DataSet({
        ...tableDS(),
        queryDataSet: tableQueryDs,
      });
      const lineTableDs = new DataSet({
        ...lineTableDS(),
      })
      return {
        tableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(NcReviewManagementPlatform),
);
