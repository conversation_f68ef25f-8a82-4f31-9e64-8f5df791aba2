import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { BASIC } from '@utils/config';
// eslint-disable-next-line import/named

const modelPrompt = 'tarzan.hmes.cellSortingLevelConfiguration';

const tenantId = getCurrentOrganizationId();
/**
 * 基础信息
 */
const baseInfoDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  paging: false,
  forceValidate: true,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovPara: { tenantId },
      lovCode: 'MT.MODEL.SITE',
      required: true,
      textField: 'siteCode',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
      bind: 'siteLov.siteName',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      lovCode: 'MT.MATERIAL',
      required: true,
      textField: 'materialCode',
      valueField: 'materialId',
      ignore: 'always',
      dynamicProps: {
        disabled: ({ record }) => !record.get('siteId'),
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      bind: 'materialLov.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
    },
    {
      name: 'levelType',
      label: intl.get(`${modelPrompt}.levelType`).d('等级类型'),
      type: FieldType.string,
      required: true,
      lookupCode: 'HME.LEVEL_TYPE',
    },
    {
      name: 'levelCode',
      label: intl.get(`${modelPrompt}.levelCode`).d('等级类型编码'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    // {
    //   name: 'minimumValue',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.minimumValue`).d('最小值'),
    //   dynamicProps: {
    //     disabled: ({ record }) => {
    //       return record.get('valueType') !== 'VALUE';
    //     },
    //   },
    // },
    // {
    //   name: 'maximalValue',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.maximalValue`).d('最大值'),
    //   dynamicProps: {
    //     disabled: ({ record }) => {
    //       return record.get('valueType') !== 'VALUE';
    //     },
    //   },
    // },
    {
      name: 'capacity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.capacity`).d('容量'),
    },
    {
      name: 'voltage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.voltage`).d('电压'),
    },
    {
      name: 'dcr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dcr`).d('DCR'),
    },
    {
      name: 'acr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.acr`).d('ACR'),
    },
    {
      name: 'k1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.k1`).d('K1值'),
    },
    {
      name: 'k2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.k2`).d('K2值'),
    },
    // {
    //   name: 'valueList',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
    //   multiple: ',',
    //   dynamicProps: {
    //     disabled: ({ record }) => {
    //       return record.get('valueType') !== 'VALUE_LIST';
    //     },
    //     required: ({ record }) => {
    //       return record.get('valueType') === 'VALUE_LIST';
    //     },
    //   },
    // },
  ],
  events: {
    update: ({ record, name }) => {
      if (name === 'siteLov'&&!record.get('siteLov')) {
        record.set('materialLov', null);
        record.set('materialId', null);
        record.set('materialCode', null);
        record.set('materialName', null);
      }
    },
  },
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-specified-level-configs/${data.levelConfigId}`,
        method: 'get',
      };
    },
  },
});

const numberListDS = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  dataKey: 'rows',
  fields: [
    { name: 'dataValue' },
    {
      name: 'multipleValue',
      dynamicProps: {
        range: ({ record }) => {
          return record.get('valueType') === 'section' ? ['leftValue', 'rightValue'] : false;
        },
      },
    },
    {
      name: 'leftChar',
      type: FieldType.string,
      defaultValue: '[',
    },
    {
      name: 'leftValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.leftValue';
          }
        },
      },
    },
    {
      name: 'rightChar',
      type: FieldType.string,
      defaultValue: ']',
    },
    {
      name: 'rightValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.rightValue';
          }
        },
      },
    },
    {
      name: 'valueType',
      type: FieldType.string,
      defaultValue: 'section',
    },
    {
      name: 'valueShow',
      type: FieldType.string,
    },
    {
      name: 'standard',
      type: FieldType.string,
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeLov`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: 'always',
      textField: 'ncCode',
      valueField: 'ncCodeId',
    },
    {
      name: 'ncCodeId',
      type: FieldType.number,
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      bind: 'ncCodeLov.ncCode',
    },
  ],
  events: {
    load: ({ dataSet }) => {
      if (!dataSet.length) {
        dataSet.loadData([{ leftChar: '(', rightChar: ')', valueType: 'single' }]);
        return;
      }
      dataSet.forEach(record => {
        if (record?.get('valueType') === 'section') {
          record?.set('multipleValue', {
            leftValue: record?.get('leftValue'),
            rightValue: record?.get('rightValue'),
          });
        } else {
          record?.set('multipleValue', record?.get('dataValue'));
        }
      });
    },
    update: ({ record, name }) => {
      switch (name) {
        case 'valueType':
        case 'leftValue':
        case 'rightValue':
        case 'leftChar':
        case 'rightChar':
        case 'multipleValue':
          handleUpdateRangeValue(record, name);
          break;
        default:
          break;
      }
    },
  },
});

const handleUpdateRangeValue = (record, name) => {
  if (record.get('valueType') === 'section') {
    if (!record.get('leftChar')) {
      record.set('leftChar', '(');
    }
    if (!record.get('rightChar')) {
      record.set('rightChar', ')');
    }
    const leftValue = record.get('leftValue');
    const rightValue = record.get('rightValue');
    const leftChar = record.get('leftChar') === '(' ? '<' : '≤';
    const rightChar = record.get('rightChar') === ')' ? '<' : '≤';
    record.set('valueShow', `${!isNaN(leftValue) ? leftValue : '-∞'}${leftChar}X${rightChar}${!isNaN(rightValue) ? rightValue : '+∞'}`);
  } else if (name === 'valueType') {
    record.set('multipleValue', undefined);
  }
};


export { baseInfoDS, numberListDS };
