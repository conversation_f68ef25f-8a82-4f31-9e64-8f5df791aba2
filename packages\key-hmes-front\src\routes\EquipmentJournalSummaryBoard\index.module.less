.screen-container {
  width: 100%;
  height: 100%;

  :global {
    .page-content-wrap {
      height: 100%;
      margin: 0;
    }

    .page-content {
      height: 100%;
      margin: 0;
    }

    .c7n-spin-nested-loading {
      height: 100%;
      .c7n-spin-container {
        height: 100%;
      }
    }
  }
}

.dashboard-container {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
  overflow: hidden;
  color: #fff;
  background: url('./assets/background.png') center center no-repeat;
  background-position: top;
  background-size: 100% 100%;
}

.dashboard-title {
  display: flex;
  width: 100%;
  height: 10vh;
  min-height: 80px;
  justify-content: space-around;
  .dashboard-title-left {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    justify-content: left;
    align-items: center;
    width: 50%;
    height: 80%;

    .dashboard-title-left-one {
      width: 8%;
      height: 80%;
      margin-left: 1vw;
      background-image: url('./assets/logo.png');
      background-repeat: no-repeat;
      background-size: 93% 100%;
    }

    .dashboard-title-left-two {
      width: 2%;
      height: 70%;
      background-image: url('./assets/splitLines.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .dashboard-title-left-three {
      width: 45%;
      position: relative;
      font-size: 40px;
      font-weight: 800;
      letter-spacing: 4px;
      line-height: 42.4px;
      color: #ffffff;
    }
  }

  .dashboard-title-right {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    justify-content: right;
    align-items: center;
    width: 50%;
    height: 80%;

    .dashboard-title-right-one {
      width: 20%;
      height: 60%;
      margin-top: 10px;
      display: flex;
      justify-content: end;
      align-items: center;
      background-image: url('./assets/searchBox.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .dashboard-title-right-one-text {
        width: 60%;
        font-size: 20px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.65);
        text-align: right;
      }

      .dashboard-title-left-one-dom {
        width: 40%;
        font-size: 1vw;
        font-weight: 600;
        text-align: center;
      }
    }

    .dashboard-title-right-two {
      width: 40%;
      height: 60%;
      margin-top: 8px;
      display: flex;
      justify-content: left;
      align-items: center;
      padding-left: 2%;
      background-image: url('./assets/searchBox.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .dashboard-title-right-two-text {
        width: 25%;
        margin-left: 14%;
        font-size: 20px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.65);
        text-align: center;
      }

      .dashboard-title-left-two-dom {
        width: 55%;
        margin-top: 0.9vw;
      }
    }

    .dashboard-title-right-three {
      width: 40%;
      height: 100%;
      display: flex;
      justify-content: space-around;
      align-items: center;

      .dashboard-title-right-three-time {
        width: 88%;
        text-align: center;
        .dashboard-title-right-three-time-text {
          text-shadow: rgba(0, 129, 255, 0.4) 0 0 21px, rgb(0, 129, 255) 0 0 21px;
          font-size: 1vw;
          font-weight: 600;
          text-align: center;
        }
      }

      .dashboard-title-right-three-home {
        width: 12%;
        height: 50%;
        background-image: url('./assets/homeIcon.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}

.dashboard-content {
  width: 100%;
  height: calc(100% - 10vh);

  .dashboard-row-up {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-wrap: wrap;
    width: 100%;
    height: 40%;

    .dashboard-item-left {
      width: 30%;
      height: 100%;

      .dashboard-item-right-pieChart {
        width: 100%;
        height: 80%;
      }
    }

    .dashboard-item-middle {
      width: 40%;
      height: 100%;
    }

    .dashboard-item-right {
      width: 30%;
      height: 100%;
    }

  }

  .dashboard-col-down {
    width: 100%;
    height: 58%;
    margin: 1% 0;
  }

}

.dashboard-card {
  width: 100%;
  height: 100%;
  padding: 5px;
  overflow: hidden;
  // border: 1px solid red;
  .dashboard-card-title {
    width: 100%;
  }

  .dashboard-card-content {
    width: 100%;
    height: 100%;
  }
}

.top-select {
  //width: 300px;
  height: 45px !important;

  :global {
    .c7n-pro-select-wrapper {
      //width: 200px;
      height: 40px !important;
      background: transparent !important;
      border: none !important;
    }

    .c7n-pro-select {
      height: 27px !important;
      color: #33c5ff !important;
      font-weight: 600 !important;
      line-height: 40px !important;
      outline: none !important;
      border: none !important;
      font-size: 18px !important;
    }

    .c7n-pro-select:focus {
      border: none !important;
      box-shadow: none !important;
    }

    .c7n-pro-select:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .c7n-pro-select-multiple-block {
      background: #154ea0;
    }

    input::-webkit-input-placeholder {
      padding-left: 10px !important;
      /* placeholder颜色 */
      color: #33c5ff !important;
      font-weight: 600 !important;
      /* placeholder字体大小 */
      font-size: 18px !important;
      line-height: 40px !important;
    }
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-suffix-button .c7n-pro-select-suffix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-prefix-button .c7n-pro-select-suffix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-suffix-button .c7n-pro-select-prefix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-prefix-button .c7n-pro-select-prefix .icon,
    .c7n-pro-calendar-picker-range-split,
    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-suffix-button .c7n-pro-calendar-picker-suffix .icon {
      font-size: 35px;
    }

    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-select-suffix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-select-prefix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-input-number-inner-button .icon,
    .c7n-pro-calendar-picker-range-split,
    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper label input,
    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-suffix-button .c7n-pro-calendar-picker-suffix .icon {
      color: #00FFFF;
    }

    .c7n-pro-calendar-picker-range-split::before {
      display: none;
    }

    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper label .c7n-pro-calendar-picker {
      border: 0;
    }
    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper label input {
      color: #33c5ff;
      font-weight: 600;
      font-size: 16px;
    }

  }
}

.rank-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  padding: 10px 10px 20px;

  .rank-item {
    display: flex;
    align-items: center;
    width: 50%;
    height: 20%;
    padding-right: 25px;

    .rank-item-sort {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 20px;
      color: #fff;
      font-size: 12px;
      background: #4c6277;
      border-radius: 50%;
    }

    .rank-item-name {
      flex-shrink: 0;
      width: 100px;
      padding: 0 4px 0;
      color: #fff;
      font-size: 14px;
    }

    .rank-item-progress {
      padding: 0 4px 0;
    }

    :global {
      .c7n-progress {
        padding: 3px !important;
        background: #264160 !important;
        border-radius: 50px !important;
      }

      .c7n-progress-inner {
        background: #264160 !important;

        .c7n-progress-bg {
          height: 8px !important;
        }
      }

      .c7n-progress-outer {
        display: flex;
        align-items: center;
      }

      .c7n-progress-line {
        font-size: 12px !important;
      }
    }

    .rank-item-quantity {
      flex-shrink: 0;
      width: 50px;
      padding: 0 5px 0;
      text-align: right;
    }

    .rank-item-percent {
      flex-shrink: 0;
      width: 30px;
      padding: 0 5px 0;
      text-align: right;
    }
  }
}

.sumary-list {
  display: flex;
  justify-content: space-around;
  height: 100px;

  .sumary-item-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #a6a6a6;
    font-weight: 600;
    font-size: 18px;
    background: url('./assets/numberSum.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }

  .sumary-item-2 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #47a5f2;
    font-weight: 600;
    font-size: 18px;
    background: url('./assets/numberSum.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }

  .sumary-item-3 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #f3be58;
    font-weight: 600;
    font-size: 18px;
    background: url('./assets/numberSum.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }

  .sumary-item-4 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #03f8f9;
    font-weight: 600;
    font-size: 18px;
    background: url('./assets/numberSum.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }
}

.my-scroll-board-2 {
  :global {
    .dv-scroll-board {
      .header {
        .header-item {
          height: 2.5vw !important;
          font-weight: bold !important;
          font-size: 12px !important;
          line-height: 2.5vw !important;
        }
      }

      .rows {
        height: auto !important;
        .row-item {
          font-size: 12px !important;
          // border: 1px solid #1e3e67;
        }
      }
    }
  }
}
.my-scroll-board-table {
  :global {
    .dv-scroll-board .header .header-item:nth-child(1) {
      //width: 90px !important;
    }
    .dv-scroll-board .rows .ceil:nth-child(1) {
      //width: 90px !important;
    }
    .dv-scroll-board {
      .rows {
        height: auto !important;
        .row-item {
          font-size: 14px !important;
          height: 45px !important;
          line-height: 45px !important;
        }
      }
    }

    .dv-scroll-board .rows .ceil {
      padding: 0 5px !important;
    }
  }
}

.checkSupplierText {
  display: block;
  margin-bottom: 20px; /* 留出足够的空间供背景图片显示 */
}

.checkSupplierImage {
  display: inline-block;
  width: 100px; /* 或者你想要的任何宽度 */
  height: 100px; /* 或者你想要的任何高度 */
  background-image: url('./assets/numberSum.png');
  background-size: cover; /* 背景图片覆盖整个span区域 */
  background-position: center; /* 图片居中显示 */
  background-repeat: no-repeat; /* 不重复背景图片 */
}

.dashboard-right-chart{
  margin: 2%;
  border-radius: 50%; /* 圆形结构 */
  background-image: url('./assets/numberSum.png');
  background-position: 50% 10%; /* 背景图片居中 */
  background-repeat: no-repeat; /* 不重复背景图片 */
  background-size: 59% 87%;
}

.dashboard-right-chart-full-screen{
  // margin:2%;
  border-radius: 50%; /* 圆形结构 */
  background-image: url('./assets/numberSum.png');
  // background-size: 70% 90%; /* 背景图片覆盖整个元素 */
  // background-repeat: no-repeat; /* 不重复背景图片 */
  // background-position: 50% 100%; /* 背景图片居中 */

  background-size: 61% 80%;
  background-repeat: no-repeat;
  background-position: 50% 25%;
}
