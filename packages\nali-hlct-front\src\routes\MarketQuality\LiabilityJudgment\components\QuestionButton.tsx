import React from 'react';
import { Button } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { observer } from "mobx-react";
import notification from 'utils/notification';

const modelPrompt = 'tarzan.hwms.LiabilityJudgment';

const questionManage = (lineDs, headDs, history) => {
  if (lineDs.selected?.length > 1) {
    notification.error({ message: intl.get(`${modelPrompt}.info.onlyOneQuestion`).d('触发问题只能选择一条行明细触发问题管理') });
    return false;
  }
  const headerData = headDs.current.toData();

  const data = lineDs.selected[0]?.toData();
  if (data.problemCreateFlag === 'Y') {
    history.push(`/hwms/problem-management/problem-management-platform/dist/${data.problemId}`)
  } else {
    history.push({
      key: new Date().getTime(),
      pathname: `/hwms/problem-management/problem-management-platform/list`,
      state: {
        stateType: 'create',
        problemId: data.problemId,
        siteName: headerData.siteName,
        siteId: headerData.siteId,
        theme: headerData.theme,
        prianalResjudgItemId: data.prianalResjudgItemId,
      },
    })
  }
};

const QuestionButton = observer(({lineDs, headDs, history}) => {
  return (
    <Button
      onClick={() => questionManage(lineDs, headDs, history)}
      disabled={!lineDs.selected.length}
    >
      {intl.get(`${modelPrompt}.question.manage`).d('触发问题管理')}
    </Button>
  )
});

export default QuestionButton;
