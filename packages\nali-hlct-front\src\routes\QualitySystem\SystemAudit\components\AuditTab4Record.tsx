/**
 * @Description: 体系审核管理维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import { drawerPropsC7n } from '@components/tarzan-ui';
import intl from 'utils/intl';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import {
  DataSet,
  Form,
  Button,
  Select,
  Table,
  DatePicker,
  Tooltip,
  TextField,
  Spin,
  TimePicker,
  Modal,
  Icon,
} from 'choerodon-ui/pro';
import { Popconfirm, Menu, Card } from 'choerodon-ui';
import { getCurrentUser } from 'utils/utils';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import moment from 'moment';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import styles from '../index.modules.less';
import ProblemCreateDraw from './ProblemCreateDraw';
import {
  auditRecordFormDS,
  auditRecordEnterTableDS,
  auditRecordFunctionTableDS,
} from '../stores/AuditTab4RecordDS';
import { problemInfoDS } from '../stores/AuditTab5ProblemDS';
import {
  fetchAuditRecordListConfig,
  fetchAuditRecordInfoConfig,
  saveAuditRecordInfoConfig,
  deleteAuditRecordInfoConfig,
  saveAuditProblemConfig,
  commitAuditRecordInfoConfig,
} from '../services';
import noDataImg from '@/assets/noData.png';

const modelPrompt = 'tarzan.systemAudit';

const AuditRecord = props => {
  const { id, canEdit, setCanEdit, activeKey, threeResList, readonly, pubFlag } = props;

  // 查询审核记录列表
  const fetchAuditRecordList = useRequest(fetchAuditRecordListConfig(), {
    manual: true,
    needPromise: true,
  });
  // 查询审核记录详情
  const fetchAuditRecordInfo = useRequest(fetchAuditRecordInfoConfig(), {
    manual: true,
    needPromise: true,
  });
  // 保存审核记录详情
  const saveAuditRecordInfo = useRequest(saveAuditRecordInfoConfig(), {
    manual: true,
    needPromise: true,
  });
  // 删除审核记录详情
  const deleteAuditRecordInfo = useRequest(deleteAuditRecordInfoConfig(), {
    manual: true,
    needPromise: true,
  });

  // 保存审核问题
  const saveAuditProblem = useRequest(saveAuditProblemConfig(), {
    manual: true,
    needPromise: true,
  });

  // 提交审核问题
  const commitAuditRecordInfo = useRequest(commitAuditRecordInfoConfig(), {
    manual: true,
    needPromise: true,
  });

  const [user] = useState(getCurrentUser()); // 用户详细信息

  const [usedScheduleIds, setUsedScheduleIds]: any = useState([]); // 已使用审核记录

  const [menuSwitch, setMenuSwitch] = useState(true); // 菜单开关

  const [recordList, setRecordList] = useState<Array<any>>([]); // 审核记录列表

  const [selectIndex, setSelectIndex] = useState<Array<any>>([]); // 选中的列表

  const modalDs = useMemo(() => new DataSet(problemInfoDS()), []);

  const inputDtlListDs = useMemo(() => new DataSet(auditRecordEnterTableDS()), []);
  const methodDtlListDs = useMemo(() => new DataSet(auditRecordFunctionTableDS()), []);
  const resourceDtlListDs = useMemo(() => new DataSet(auditRecordEnterTableDS()), []);
  const implementorDtlListDs = useMemo(() => new DataSet(auditRecordEnterTableDS()), []);
  const performanceDtlListDs = useMemo(() => new DataSet(auditRecordEnterTableDS()), []);
  const outputDtlListDs = useMemo(() => new DataSet(auditRecordEnterTableDS()), []);
  const riskChanceDtlListDs = useMemo(() => new DataSet(auditRecordEnterTableDS()), []);
  const formDs = useMemo(
    () =>
      new DataSet({
        ...auditRecordFormDS(),
        children: {
          inputDtlList: inputDtlListDs, // 1 流程输入
          methodDtlList: methodDtlListDs, // 2 方法
          resourceDtlList: resourceDtlListDs, // 3 资源
          implementorDtlList: implementorDtlListDs, // 4 实施者
          performanceDtlList: performanceDtlListDs, // 5 绩效管理
          outputDtlList: outputDtlListDs, // 6 流程输出
          riskChanceDtlList: riskChanceDtlListDs, // 7 风险&机会
        },
      }),
    [],
  );

  useEffect(() => {
    if (activeKey === 'AuditTab4Record') {
      queryRecordList('');
      setSelectIndex([]);
      formDs.setState('siteId', threeResList[0]?.rows?.siteId);
      formDs.setState('sysReviewPlanId', id);
    }
  }, [id, activeKey]);

  const getAuth = () => {
    let memberInfoId: any = [];
    if (threeResList[2] && threeResList[2].rows?.reviewScheduleList) {
      const reviewScheduleList = threeResList[2].rows.reviewScheduleList || [];
      reviewScheduleList.forEach(item => {
        memberInfoId = [...memberInfoId, ...(item.memberInfoId || [])];
      });
    }
    return memberInfoId.includes(user.id);
  };

  const getAuthLeader = () => {
    let memberInfoId: any = null;
    const scheduleId = formDs.current?.get('scheduleId');
    if (threeResList[2] && threeResList[2].rows?.reviewScheduleList) {
      const reviewScheduleList = threeResList[2].rows.reviewScheduleList || [];
      reviewScheduleList.forEach(item => {
        if (item.scheduleId === scheduleId && item.memberInfoId?.length > 0) {
          memberInfoId = item.memberInfoId[0];
        }
      });
    }
    return memberInfoId === user.id && formDs.current?.get('recordStatus') === 'NEW';
  };

  const getScheduleList = () => {
    if (threeResList[2] && threeResList[2].rows?.reviewScheduleList) {
      const reviewScheduleList = threeResList[2].rows.reviewScheduleList || [];
      formDs.current?.set('scheduleList', reviewScheduleList);
      return { reviewScheduleList };
    }
    return { reviewScheduleList: [] };
  };

  const handleCancel = () => {
    formDs.reset();
    setCanEdit(false);
    queryRecordList(`${selectIndex[0] === 'create' ? '' : selectIndex[0]}`);
  };

  const handleSave = async () => {
    const validateResult = await formDs.validate();
    if (!validateResult) {
      return;
    }
    const formData = getAllData();

    const res = await saveAuditRecordInfo.run({
      params: formData,
      queryParams: {
        sysReviewPlanId: id,
      },
    });

    if (res?.success) {
      // @ts-ignore
      notification.success();
      setCanEdit(false);
      queryRecordList(`${res?.rows}`);
    }
  };

  const getAllData = () => {
    const listData = {};
    [
      { name: 'inputDtlList', ds: inputDtlListDs },
      { name: 'methodDtlList', ds: methodDtlListDs },
      { name: 'resourceDtlList', ds: resourceDtlListDs },
      { name: 'implementorDtlList', ds: implementorDtlListDs },
      { name: 'performanceDtlList', ds: performanceDtlListDs },
      { name: 'outputDtlList', ds: outputDtlListDs },
      { name: 'riskChanceDtlList', ds: riskChanceDtlListDs },
    ].forEach(listItem => {
      const tableDataList: any = [];
      listItem.ds.forEach(record => {
        const tableData = record.toData();
        tableData.changeFlag = record.status === 'sync' ? 'N' : 'Y';
        tableData.sysReviewStandard = (tableData.sysReviewStandardIdList || []).join(',');
        tableDataList.push(tableData);
      });
      listData[listItem.name] = tableDataList;
    });
    const data = formDs.current?.toData();
    data.reviewDate = moment(data.reviewDate).format('YYYY-MM-DD');
    data.changeFlag = formDs.current?.status === 'sync' ? 'N' : 'Y';
    return {
      ...data,
      ...listData,
    };
  };

  const queryRecordList = async recordId => {
    const res = await fetchAuditRecordList.run({
      params: {
        sysReviewPlanId: id,
      },
      // queryParams: {
      //   sysReviewPlanId: id,
      // },
    });

    const recordIdList: any = [];

    const newRecordList = (res?.rows || []).map(item => {
      recordIdList.push(`${item.recordId}`);
      return {
        ...item,
        key: item.recordId,
      };
    });

    setRecordList(newRecordList);
    if (recordId) {
      selectListClick({ key: recordId });
    } else if (recordIdList[0]) {
      selectListClick({ key: recordIdList[0] });
    } else {
      selectListClick({ key: null });
    }
  };

  // 添加审核记录
  const addRecord = () => {
    setRecordList([
      ...recordList,
      {
        displayCode: `新建记录`,
        key: 'create',
      },
    ]);
    formDs.loadData([
      {
        ...getScheduleList(),
        recordStatus: 'NEW',
        inputDtlList: [],
        methodDtlList: [],
        resourceDtlList: [],
        implementorDtlList: [],
        performanceDtlList: [],
        outputDtlList: [],
        riskChanceDtlList: [],
      },
    ]);
    setSelectIndex(['create']);
    setCanEdit(true);
  };

  // 添加小组行
  const addRecordRow = tableProps => {
    const { dataSet } = tableProps;
    dataSet.create({});
  };

  // 删除小组行
  const deleteRecordRow = tableProps => {
    const { dataSet, record } = tableProps;
    dataSet.delete(record, false);
  };

  // 删除列表项
  const deleterAuditRecord = async () => {
    const res = await deleteAuditRecordInfo.run({
      queryParams: {
        sysReviewPlanId: id,
        recordId: formDs.current?.get('recordId'),
      },
    });

    if (res?.success) {
      // @ts-ignore
      notification.success();
      queryRecordList('');
    }
  };

  const columns1: ColumnProps[] = [
    {
      header: tableProps => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          funcType="flat"
          shape="circle"
          size="small"
          onClick={() => {
            addRecordRow(tableProps);
          }}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      hidden: !canEdit,
      renderer: tableProps => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecordRow(tableProps)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit}
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      title: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
      renderer: ({ record }) => record!.index + 1,
      width: 80,
      align: ColumnAlign.right,
      lock: ColumnLock.left,
    },
    {
      name: 'recordContent',
      editor: canEdit,
    },
    {
      name: 'conformLevel',
      editor: canEdit && <Select />,
    },
    {
      name: 'beRevperName',
      editor: canEdit,
    },
    {
      name: 'revperName',
      editor: canEdit,
    },
    {
      name: 'sysReviewStandardLov',
      editor: canEdit,
    },
    {
      name: 'sampleResult',
      editor: canEdit,
    },
    {
      name: 'problemAttri',
      editor: canEdit,
    },
  ];
  const columns2: ColumnProps[] = [
    {
      header: tableProps => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          funcType="flat"
          shape="circle"
          size="small"
          onClick={() => {
            addRecordRow(tableProps);
          }}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      hidden: !canEdit,
      renderer: tableProps => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecordRow(tableProps)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit}
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      title: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
      renderer: ({ record }) => record!.index + 1,
      width: 80,
      align: ColumnAlign.right,
      lock: ColumnLock.left,
    },
    {
      name: 'recordContent',
      editor: canEdit,
    },
    {
      name: 'conformLevel',
      editor: canEdit && <Select />,
    },
    {
      name: 'schemeFlag',
      editor: canEdit && <Select />,
    },
    {
      name: 'beRevperName',
      editor: canEdit,
    },
    {
      name: 'revperName',
      editor: canEdit,
    },
    {
      name: 'sysReviewStandardLov',
      editor: canEdit,
    },
    {
      name: 'schemeOutput',
      editor: canEdit,
    },
    {
      name: 'problemAttri',
      editor: canEdit,
    },
  ];

  const getRecordInfo = async key => {
    const res = await fetchAuditRecordInfo.run({
      params: {
        recordId: key,
      },
    });
    setSelectIndex([`${key}`]);
    if (res?.success) {
      // 这里要走查询
      formDs.loadData([{ ...getScheduleList(), ...res?.rows } || {}]);
    } else {
      formDs.loadData([{ ...getScheduleList() }]);
    }
  };

  const selectListClick = ({ key }) => {
    const _usedScheduleIds: any = [];
    recordList.forEach(item => {
      if (`${item.key}` !== `${key}`) {
        _usedScheduleIds.push(item.scheduleId);
      }
    });
    setUsedScheduleIds(_usedScheduleIds);
    if (key === 'add') {
      addRecord();
    } else if (key) {
      getRecordInfo(key);
    } else {
      setSelectIndex([]);
      formDs.loadData([
        {
          ...getScheduleList(),
          inputDtlList: [],
          methodDtlList: [],
          resourceDtlList: [],
          implementorDtlList: [],
          performanceDtlList: [],
          outputDtlList: [],
          riskChanceDtlList: [],
        },
      ]);
    }
  };

  const handleModalCancel = () => {
    modalDs.loadData([]);
  };

  const handleModalSave = async () => {
    const validateResult = await modalDs.validate();
    if (!validateResult) {
      return false;
    }

    const recordId = [
      ...inputDtlListDs.selected,
      ...methodDtlListDs.selected,
      ...resourceDtlListDs.selected,
      ...implementorDtlListDs.selected,
      ...performanceDtlListDs.selected,
      ...outputDtlListDs.selected,
      ...riskChanceDtlListDs.selected,
    ].map(record => {
      return record.get('recordDId');
    });

    const formData: any = modalDs.toData()[0];
    formData.proposeTimePeriod = `${formData?.proposeTimePeriod?.start}~${formData?.proposeTimePeriod?.end}`;
    const res = await saveAuditProblem.run({
      params: formData,
      queryParams: {
        sysReviewPlanId: id,
        recordId,
      },
    });
    if (res?.success) {
      // @ts-ignore
      notification.success();
      if (res?.rows) {
        props.history.push(
          `/hwms/problem-management/problem-management-platform/dist/${res?.rows}`,
        );
      }
    }
  };

  const recordSubmit = async () => {
    const res = await commitAuditRecordInfo.run({
      queryParams: {
        sysReviewPlanId: id,
        recordId: selectIndex[0],
      },
    });

    if (res?.success) {
      // @ts-ignore
      notification.success();
      selectListClick({ key: selectIndex[0] });
    }
  };

  // 生成问题
  const addProblem = () => {
    modalDs.loadData([
      {
        problemCategory: 'PREVIEW',
        sysReviewType: threeResList[0]?.rows?.sysReviewType,
        registerTime: new Date(),
        problemStatus: 'DRAFT',
        registerPerson: user.id,
        registerPersonRealName: user.realName,
        // proposePerson: user.id,
        // proposePersonRealName: user.realName,
      },
    ]);

    Modal.open({
      ...drawerPropsC7n({}),
      destroyOnClose: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.RegistrationProblem`).d('登记问题'),
      drawer: true,
      style: {
        width: '360px',
      },
      className: 'hmes-style-modal',
      children: <ProblemCreateDraw formDs={modalDs} />,
      onOk: handleModalSave,
      afterClose: handleModalCancel,
    });
  };

  return (
    <div className={styles.tabsBody}>
      <Spin
        spinning={
          fetchAuditRecordList.loading ||
          fetchAuditRecordInfo.loading ||
          saveAuditRecordInfo.loading ||
          deleteAuditRecordInfo.loading ||
          saveAuditProblem.loading
        }
      >
        <div className="audit-record">
          <div
            className="audit-record-list"
            style={{
              display: menuSwitch ? 'block' : 'none',
            }}
          >
            <Menu
              style={{ width: '100%' }}
              defaultOpenKeys={['sub1']}
              mode="inline"
              selectedKeys={selectIndex}
              onClick={selectListClick}
            >
              <Menu.Item disabled key="title">
                <div className="record-list-title">
                  {intl.get(`${modelPrompt}.auditRecordList`).d('审核记录列表')}
                </div>
              </Menu.Item>
              {recordList &&
                recordList.length > 0 &&
                recordList.map(item => (
                  <Menu.Item disabled={canEdit} key={item.key}>
                    <div className="record-list-item">
                      <Tooltip title={`${item.displayCode}`} placement="right">
                        <div className="record-list-item-text">{item.displayCode}</div>
                      </Tooltip>
                    </div>
                  </Menu.Item>
                ))}
              <Menu.Item disabled={canEdit || pubFlag || !getAuth()} key="add">
                <div className="record-list-add">
                  <Button disabled={canEdit || !getAuth()} funcType={FuncType.link} icon="add">
                    {
                      intl.get(`${modelPrompt}.addAuditRecord`).d('添加审核记录')
                    }
                  </Button>
                </div>
              </Menu.Item>
            </Menu>
          </div>

          {recordList?.length > 0 && (
            <div className="audit-record-detial">
              {!readonly && (
                <div className="control-row">
                  <div className="menu-switch-icon">
                    <div
                      onClick={() => {
                        setMenuSwitch(!menuSwitch);
                      }}
                    >
                      <Icon
                        type={menuSwitch ? 'caidanzhedie' : 'caidanzhankai'}
                        style={{ fontSize: 18 }}
                      />
                    </div>
                  </div>
                  <div>
                    {!canEdit && !pubFlag && (
                      <>
                        <Popconfirm
                          title={intl
                            .get(`tarzan.common.message.confirm.delete`)
                            .d('是否确认删除?')}
                          onConfirm={deleterAuditRecord}
                          okText={intl.get('tarzan.common.button.confirm').d('确认')}
                          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                        >
                          <Button disabled={!getAuthLeader()}>
                            {intl.get('hzero.common.button.delete').d('删除')}
                          </Button>
                        </Popconfirm>
                        <Button
                          disabled={!getAuth() || formDs.current?.get('recordStatus') !== 'NEW'}
                          color={ButtonColor.primary}
                          onClick={() => {
                            setCanEdit(true);
                          }}
                        >
                          {intl.get('hzero.common.button.edit').d('编辑')}
                        </Button>
                        <Button
                          disabled={
                            !getAuthLeader() ||
                            inputDtlListDs.length === 0 ||
                            methodDtlListDs.length === 0 ||
                            outputDtlListDs.length === 0
                          }
                          color={ButtonColor.primary}
                          onClick={recordSubmit}
                        >
                          {intl.get(`${modelPrompt}.auditRecordSubmit`).d('审核记录提交')}
                        </Button>
                        <Button
                          disabled={!getAuth()}
                          color={ButtonColor.primary}
                          onClick={addProblem}
                        >
                          {intl.get(`${modelPrompt}.createProblem`).d('生成问题')}
                        </Button>
                      </>
                    )}
                    {canEdit && !pubFlag && (
                      <>
                        <Button onClick={handleCancel}>
                          {intl.get('hzero.common.button.cancel').d('取消')}
                        </Button>
                        <Button color={ButtonColor.primary} onClick={handleSave}>
                          {intl.get('hzero.common.button.save').d('保存')}
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              )}
              <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
                <TextField name="recordCode" />
                <Select name="recordStatus" />
                <Select
                  name="scheduleCodeObject"
                  onOption={({ record }) => {
                    const memberInfo = (record.get('memberInfo') || '').split(',');
                    return {
                      disabled:
                        !memberInfo.includes(user.realName) ||
                        usedScheduleIds.includes(record.get('scheduleId')),
                    };
                  }}
                />
                <Select name="groupDescription" />
                <DatePicker name="reviewDate" />
                <TimePicker name="reviewTimeFrom" />
                <TimePicker name="reviewTimeTo" />
                <TextField name="beRevDepartmentName" />
                <TextField name="beRevContent" />
                <TextField name="reviewCor" />
                <TextField name="sysReviewStandardStr" />
              </Form>

              <Table
                header={intl.get(`${modelPrompt}.processInput`).d('流程输入')}
                customizedCode="shtxshjl1"
                className="audit-record-table"
                dataSet={inputDtlListDs}
                columns={columns1}
              ></Table>

              <Table
                header={intl.get(`${modelPrompt}.method`).d('方法')}
                customizedCode="shtxshjl2"
                className="audit-record-table"
                dataSet={methodDtlListDs}
                columns={columns2}
              ></Table>

              <Table
                header={intl.get(`${modelPrompt}.assets`).d('资源')}
                customizedCode="shtxshjl1"
                className="audit-record-table"
                dataSet={resourceDtlListDs}
                columns={columns1}
              ></Table>

              <Table
                header={intl.get(`${modelPrompt}.executor`).d('实施者')}
                customizedCode="shtxshjl1"
                className="audit-record-table"
                dataSet={implementorDtlListDs}
                columns={columns1}
              ></Table>
              <Table
                header={intl.get(`${modelPrompt}.performanceManagement`).d('绩效管理')}
                customizedCode="shtxshjl1"
                className="audit-record-table"
                dataSet={performanceDtlListDs}
                columns={columns1}
              ></Table>
              <Table
                header={intl.get(`${modelPrompt}.processOut`).d('流程输出')}
                customizedCode="shtxshjl1"
                className="audit-record-table"
                dataSet={outputDtlListDs}
                columns={columns1}
              ></Table>
              <Table
                header={intl.get(`${modelPrompt}.riskOpportunity`).d('风险机会')}
                customizedCode="shtxshjl1"
                className="audit-record-table"
                dataSet={riskChanceDtlListDs}
                columns={columns1}
              ></Table>
            </div>
          )}

          {(!recordList || !recordList.length) && (
            <div className="audit-record-detial">
              <div
                onClick={() => {
                  setMenuSwitch(!menuSwitch);
                }}
              >
                <Icon
                  type={menuSwitch ? 'caidanzhedie' : 'caidanzhankai'}
                  style={{ fontSize: 18 }}
                />
              </div>
              <Card className="empty-card" bordered={false}>
                <div>
                  <img src={noDataImg} alt={intl.get(`${modelPrompt}.noData`).d('暂无数据')} />
                </div>
                <div>{intl.get(`${modelPrompt}.noData`).d('暂无数据')}</div>
              </Card>
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
};

export default AuditRecord;
