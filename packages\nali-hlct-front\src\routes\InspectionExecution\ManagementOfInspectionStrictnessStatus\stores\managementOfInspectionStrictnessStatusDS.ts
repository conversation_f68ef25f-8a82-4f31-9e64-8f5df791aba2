/**
 * @Description: 检验严格度状态管理-表格DS
 * @Author: <<EMAIL>>
 * @Date: 2023-12-18
 * @LastEditTime: 2022-12-18
 * @LastEditors: <<EMAIL>>
 */
import { FieldType, FieldIgnore, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
// ${BASIC.TARZAN_SAMPLING}

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.qms.ManagementOfInspectionStrictnessStatus';

const tableDS: () => DataSetProps = () => ({
  forceValidate: true,
  autoQuery: true,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteObj.siteId',
    },
    {
      name: 'inspectBusinessTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      bind: 'inspectBusinessTypeObj.inspectBusinessType',
    },
    {
      name: 'beforeStrictness',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.beforeStrictness`).d('当前严格度'),
      lovPara: { tenantId },
      lookupCode: 'MT.SAMPLING.STRICTNESS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'afterStrictness',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.afterStrictness`).d('下次严格度'),
      lovPara: { tenantId },
      lookupCode: 'MT.SAMPLING.STRICTNESS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.MATERIAL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'materialName',
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCode',
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      type: FieldType.string,
    },
    {
      name: 'categoryLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料类别'),
      lovCode: 'MT.METHOD.MATERIAL_CATEGORY_SITES',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'materialCategoryId',
      type: FieldType.number,
      bind: 'categoryLov.categoryId',
      multiple: true,
    },
    {
      name: 'inspectSchemeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_SCHEME',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'inspectSchemeId',
      bind: 'inspectSchemeLov.inspectSchemeId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      name: 'supplierObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      textField: 'supplierName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierObject.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierObject.supplierName',
    },
    {
      name: 'inspectItemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectItemCode`).d('检验项目编码'),
      lovCode: 'MT.QMS.INSPECT_ITEM',
      ignore: FieldIgnore.always,
      lovPara: {
        dataType: 'CALCULATE_FORMULA',
        tenantId,
      },
    },
    {
      name: 'inspectItemId',
      bind: 'inspectItemLov.inspectItemId',
    },
    {
      name: 'createTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartTime`).d('创建时间从'),
      max: 'createTimeTo',
    },
    {
      name: 'createTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planEndTime`).d('创建时间至'),
      min: 'createTimeFrom',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      type: FieldType.string,
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
      type: FieldType.string,
    },
    {
      name: 'inspectBusinessTypeDesc',
      label: intl.get(`${modelPrompt}.inspectBusinessTypeDesc`).d('检验业务类型'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      type: FieldType.string,
    },
    {
      name: 'revisionCode',
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      type: FieldType.string,
    },
    {
      name: 'materialCategoryCode',
      label: intl.get(`${modelPrompt}.materialCategoryCode`).d('物料类别编码'),
      type: FieldType.string,
    },
    {
      name: 'materialCategoryDesc',
      label: intl.get(`${modelPrompt}.materialCategoryDesc`).d('物料类别描述'),
      type: FieldType.string,
    },
    {
      name: 'supplierCode',
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      type: FieldType.string,
    },
    {
      name: 'supplierName',
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
      type: FieldType.string,
    },
    {
      name: 'inspectSchemeCode',
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
      type: FieldType.string,
    },
    {
      name: 'inspectItemCode',
      label: intl.get(`${modelPrompt}.inspectItemCode`).d('检验项目编码'),
      type: FieldType.string,
    },
    {
      name: 'inspectItemDesc',
      label: intl.get(`${modelPrompt}.inspectItemDesc`).d('检验项目描述'),
      type: FieldType.string,
    },
    {
      name: 'beforeStrictness',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.beforeStrictness`).d('当前严格度'),
      lovPara: { tenantId },
      lookupCode: 'MT.SAMPLING.STRICTNESS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'afterStrictness',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.afterStrictness`).d('下次严格度'),
      lovPara: { tenantId },
      lookupCode: 'MT.SAMPLING.STRICTNESS',
      textField: 'meaning',
      valueField: 'value',
      required: true,
    },
    {
      name: 'acceptLot',
      label: intl.get(`${modelPrompt}.acceptLot`).d('累计报检批'),
      type: FieldType.string,
    },
    {
      name: 'okLot',
      label: intl.get(`${modelPrompt}.okLot`).d('下次严格度累计合格批'),
      type: FieldType.string,
    },
    {
      name: 'ngLot',
      label: intl.get(`${modelPrompt}.ngLot`).d('下次严格度累计不合格批'),
      type: FieldType.string,
    },
    {
      name: 'transferScore',
      label: intl.get(`${modelPrompt}.transferScore`).d('转移得分'),
      type: FieldType.number,
      min: 0,
    },
    {
      name: 'option',
      label: intl.get('tarzan.common.label.action').d('操作'),
    },
  ],
  record: {
    dynamicProps: {
      // 关闭类型的单据不可选择
      selectable: record => {
        return record?.getState('selectable');
      },
    },
  },
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-mat-splyr-strict/query/ui`,
      method: 'GET',
    },
  },
});
const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'inspectDocNum',
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单号'),
      type: FieldType.string,
    },
    {
      name: 'inspectResultDesc',
      label: intl.get(`${modelPrompt}.inspectResultDesc`).d('检验结果'),
      type: FieldType.string,
    },
    {
      name: 'beforeStrictness',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.beforeStrictness`).d('当前严格度'),
      lovPara: { tenantId },
      lookupCode: 'MT.SAMPLING.STRICTNESS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'afterStrictness',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.afterStrictness`).d('下次严格度'),
      lovPara: { tenantId },
      lookupCode: 'MT.SAMPLING.STRICTNESS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'acceptLot',
      label: intl.get(`${modelPrompt}.acceptLot`).d('累计报检批'),
      type: FieldType.string,
    },
    {
      name: 'okLot',
      label: intl.get(`${modelPrompt}.okLot`).d('下次严格度累计合格批'),
      type: FieldType.string,
    },
    {
      name: 'ngLot',
      label: intl.get(`${modelPrompt}.ngLot`).d('下次严格度累计不合格批'),
      type: FieldType.string,
    },
    {
      name: 'samplTransferRuleCode',
      label: intl.get(`${modelPrompt}.samplTransferRuleCode`).d('转移规则'),
      type: FieldType.string,
    },
    {
      name: 'samplTransferScoreCode',
      label: intl.get(`${modelPrompt}.samplTransferScoreCode`).d('转移得分规则'),
      type: FieldType.string,
    },
    {
      name: 'beforeTransferScore',
      label: intl.get(`${modelPrompt}.beforeTransferScore`).d('转移前得分'),
      type: FieldType.string,
    },
    {
      name: 'afterTransferScore',
      label: intl.get(`${modelPrompt}.afterTransferScore`).d('转移后得分'),
      type: FieldType.string,
    },
    {
      name: 'trxTransferScore',
      label: intl.get(`${modelPrompt}.trxTransferScore`).d('得分变化'),
      type: FieldType.string,
    },
    {
      name: 'inspectSchemeCode',
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案'),
      type: FieldType.string,
    },
    {
      name: 'operationType',
      label: intl.get(`${modelPrompt}.operationType`).d('操作类型'),
      type: FieldType.string,
    },
    {
      name: 'createdBy',
      label: intl.get(`${modelPrompt}.createdBy`).d('修改人'),
      type: FieldType.string,
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('修改时间'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-mat-splyr-strict/query/detail/ui`,
      method: 'GET',
    },
  },
});

export { tableDS, detailDS };
