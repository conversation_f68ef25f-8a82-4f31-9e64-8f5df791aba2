import React, { useMemo } from 'react';
import { Attachment, Form, DataSet, Table, Select, NumberField, Modal, TextField, DateTimePicker, Currency } from 'choerodon-ui/pro';
import { Badge, Tag } from 'choerodon-ui';
import { isNumber } from 'lodash';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { BASIC } from 'hcm-components-front/lib/utils/config';
import { AttributeDrawer } from 'hcm-components-front/lib/components/tarzan-ui';
import { taskLineDS } from '../stores/InspectTaskDtlDS';

const { Option } = Select;
const { ItemGroup, Item } = Form;
const modelPrompt = 'tarzan.hwms.CheckListQueryExternal';

const PanelTable = observer((props) => {
  const { record, customizeTable, custConfig, editing } = props;
  const taskLineDs = useMemo(
    () =>
      new DataSet({
        ...taskLineDS(),
        data: record?.get('docLineList'),
      }),
    [editing],
  );

  const renderValueList = (name, record) => {
    const value = record.get(name);
    switch (record.get('dataType')) {
      case 'VALUE':
      case 'VALUE_LIST':
      case 'CALCULATE_FORMULA':
        return (
          value?.length &&
          value[0].dataValue &&
          value.map(item => (
            <Tag className="hcm-tag-blue" key={item.dataValue}>
              {item.dataValue}
            </Tag>
          ))
        );
      default:
        return value?.length && value[0].dataValue;
    }
  };

  const renderInspectValue = record => {
    const data = record?.get('actDtlList');
    if (!data || !data?.length) {
      return null;
    }

    return data.map(item => {
      const formatInspectValue = item => {
        const res = item.inspectResult?.split(',');
        const val = item.inspectValue?.split(',');

        const valueColor = i => {
          switch (res[i]) {
            case 'OK':
              return 'rgba(32, 212, 137, 1)';
            case 'NG':
              return 'rgba(230, 46, 163, 1)';
            default:
              return 'rgba(0, 0, 0, 0.85)';
          }
        };

        return val.map((i, index) => {
          if (index === val?.length - 1) {
            // return <span style={{ color: valueColor(index) }}>{i}</span>;
            return <span style={{ color: 'rgba(0, 0, 0, 0.85)' }}>{i}</span>;
          }
          // return <span style={{ color: valueColor(index) }}>{i},</span>;
          return <span style={{ color: 'rgba(0, 0, 0, 0.85)' }}>{i},</span>;
        });
      };

      return (
        <Tag>
          {item?.inspectObjectCode && <span>{item?.inspectObjectCode}</span>}(
          {formatInspectValue(item)})
        </Tag>
      );
    });
  };

  // @ts-ignore
  const taskLineColumns: ColumnProps[] = [
    {
      name: 'sequence',
      width: 100,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemCode',
      width: 150,
      lock: ColumnLock.left,
      renderer: ({ record, value }) => <a onClick={() => { handleEditModel(record) }}>{value}</a>,
    },
    {
      name: 'inspectItemDesc',
      width: 150,
    },
    { name: 'inspectItemTypeDesc', width: 120 },
    { name: 'inspectGroupDesc', width: 120 },
    { name: 'itemSourceCreateDesc', width: 120 },
    { name: 'okQty' },
    { name: 'ngQty' },
    {
      name: 'inspectResultDesc',
      renderer: ({ record }) => {
        switch (record?.get('inspectResult')) {
          case 'OK':
            return <Tag color="green">{intl.get(`${modelPrompt}.ok`).d('合格')}</Tag>;
          case 'NG':
            return <Tag color="magenta">{intl.get(`${modelPrompt}.ng`).d('不合格')}</Tag>;
          default:
            return null;
        }
      },
    },
    {
      name: 'inspectValue',
      width: 150,
      renderer: ({ record }) => renderInspectValue(record),
    },
    { name: 'actRemark' },
    { name: 'inspectBasis' },
    { name: 'inspectMethodDesc' },
    { name: 'technicalRequirement' },
    { name: 'inspectToolDesc' },
    { name: 'qualityCharacteristicDesc' },
    { name: 'dataTypeDesc' },
    {
      name: 'trueValueList',
      width: 200,
      renderer: ({ record, name }) => renderValueList(name, record),
    },
    {
      name: 'falseValueList',
      width: 200,
      renderer: ({ record, name }) => renderValueList(name, record),
    },
    {
      name: 'warningValueList',
      width: 200,
      renderer: ({ record, name }) => renderValueList(name, record),
    },
    { name: 'defaultValue' },
    { name: 'dataQtyDispositionDesc', width: 120 },
    { name: 'uomName' },
    { name: 'decimalNumber' },
    { name: 'processModeDesc' },
    { name: 'enterMethodDesc', width: 150 },
    {
      name: 'requiredFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    { name: 'dataQty' },
    // { name: 'formula' },
    { name: 'samplingMethodDesc' },
    { name: 'samplingQty' },
    {
      name: 'acceptStandardAc',
      renderer: ({ record }) => {
        if (record?.get('acceptStandard')) {
          const data = JSON.parse(record?.get('acceptStandard'));
          return data?.ac;
        }
        return null;
      },
    },
    {
      name: 'acceptStandardRe',
      renderer: ({ record }) => {
        if (record?.get('acceptStandard')) {
          const data = JSON.parse(record?.get('acceptStandard'));
          return data?.re;
        }
        return null;
      },
    },
    {
      name: 'sameGroupIdentification',
      align: ColumnAlign.center,
      width: 120,
      // renderer: ({ value }) => (
      //   <Badge
      //     status={value === 'Y' ? 'success' : 'error'}
      //     text={
      //       value === 'Y'
      //         ? intl.get(`tarzan.common.label.enable`).d('启用')
      //         : intl.get(`tarzan.common.label.disable`).d('禁用')
      //     }
      //   />
      // ),
    },
    {
      name: 'destructiveExperimentFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'outsourceFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    { name: 'actionItem' },
    { name: 'employeePosition' },
    {
      name: 'inspectFrequencyDesc',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
        if (inspectFrequencyShow) {
          inspectFrequencyShow = value.replace('M', record?.get('m') || 'M');
          inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
          return inspectFrequencyShow;
        }
        return value;
      },
    },
    { name: 'ncGroupDesc' },
    { name: 'lineRemark' },
    { name: 'actEnclosure', lock: ColumnLock.right },
    { name: 'lineEnclosure', lock: ColumnLock.right },
    {
      name: 'attr',
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      title: intl.get(`${modelPrompt}.extend`).d('扩展属性'),
      renderer: ({ record }) => (
        <AttributeDrawer
          serverCode={BASIC.TARZAN_SAMPLING}
          className="org.tarzan.qms.domain.entity.MtInspectInfo"
          kid={record?.get('inspectItemId')}
          canEdit={false}
          disabled={!record?.get('inspectItemId')}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.LINE_ATTR`}
          custConfig={custConfig}
          type="text"
        />
      ),
    },
  ];

  const actEnclosureProps: any = {
    name: 'actEnclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const lineEnclosureProps: any = {
    name: 'lineEnclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const handleEditModel = record => {
    let _dataQty = 0;
    if (record.get('dataQtyDisposition') === 'DATA') {
      _dataQty = Number(record.get('dataQty') || 0);
    } else if (record.get('dataQtyDisposition') === 'SAMPLE') {
      _dataQty = Number(record.get('samplingQty') || 0);
    } else {
      _dataQty = Number(record.get('samplingQty') || 0) * Number(record.get('dataQty') || 0);
    }
    const _dataType = record?.get('dataType');
    const _fieldName = record?.get('fieldName');
    const _count =
      _dataType === 'CALCULATE_FORMULA'
        ? Number(record?.get('formulaCount') || 0)
        : _dataQty;

    const _dataQtyValue: Array<any> = [];
    for (let i = 0; i < _count; i++) {
      _dataQtyValue.push(`${_fieldName}_VALUE${i}`);
      taskLineDs.addField(`${_fieldName}_VALUE${i}`, {
        type: FieldType.string,
      });
    }
    const _trueValues = record?.get('trueValues') || [];
    const _falseValues = record?.get('falseValues') || [];
    const _valueLists = record?.get('valueLists') || [];
    const _decimalNumber = record?.get('decimalNumber') ? Number(record?.get('decimalNumber')) : record?.get('decimalNumber');

    Modal.open({
      destroyOnClose: true, // 关闭时是否销毁
      drawer: true,
      drawerBorder: false,
      style: {
        width: 560,
      },
      title: editing ? intl.get(`${modelPrompt}.edit`).d('编辑') : intl.get(`${modelPrompt}.detail`).d('详情'),
      okButton: editing,
      cancelButton: true,
      onCancel: () => {
        if (editing) {
          record.reset()
        }
        return true;
      },
      onOk: () => { handleModalOk(record) },
      children: (
        <Form record={record} columns={1} disabled={!editing}>
          <NumberField name="okQty" />
          <NumberField name="ngQty" />
          <Select name='inspectResult' />
          <ItemGroup label={intl.get(`${modelPrompt}.taskLine.inspectValue`).d('检测值')}>
            {_dataQtyValue.map((valueName) => {
              return (
                <>
                  {_dataType === 'VALUE' && !_decimalNumber && _decimalNumber !== 0 && (
                    <Item>
                      <NumberField
                        name={valueName}
                      />
                    </Item>
                  )}
                  {_dataType === 'VALUE' && isNumber(_decimalNumber) && _decimalNumber >= 0 && (
                    <Item>
                      <Currency
                        name={valueName}
                        record={record}
                        precision={_decimalNumber > 6 ? 6 : _decimalNumber}
                      />
                    </Item>
                  )}
                  {_dataType === 'DECISION_VALUE' && (
                    <Item>
                      <Select
                        name={valueName}
                      >
                        {_trueValues.concat(_falseValues).map(item => (
                          <Option value={item} key={item}>
                            {item}
                          </Option>
                        ))}
                      </Select>
                    </Item>
                  )}
                  {_dataType === 'TEXT' && (
                    <Item>
                      <TextField
                        name={valueName}
                      />
                    </Item>
                  )}
                  {_dataType === 'VALUE_LIST' && (
                    <Item>
                      <Select
                        name={valueName}
                      >
                        {_valueLists.map(item => (
                          <Option value={item} key={item}>
                            {item}
                          </Option>
                        ))}
                      </Select>
                    </Item>
                  )}
                  {_dataType === 'DATE' && (
                    <Item>
                      <DateTimePicker
                        name={valueName}
                      />
                    </Item>
                  )}
                  {_dataType === 'CALCULATE_FORMULA' && (
                    <Item>
                      <TextField
                        name={valueName}
                      />
                    </Item>
                  )}
                </>
              );
            })}
          </ItemGroup>
          <TextField name="actRemark" />
          <Attachment readOnly={!editing} {...actEnclosureProps} />
          <Attachment readOnly={!editing} {...lineEnclosureProps} />
        </Form>
      ),
    });
  };

  const handleModalOk = (_record) => {
    const data = _record.toData();
    let _dataQty = 0;
    if (data.dataQtyDisposition === 'DATA') {
      _dataQty = Number(data.dataQty || 0);
    } else if (data.dataQtyDisposition === 'SAMPLE') {
      _dataQty = Number(data.samplingQty || 0);
    } else {
      _dataQty = Number(data.samplingQty || 0) * Number(data.dataQty || 0);
    }
    const _dataType = data.dataType;
    const _count =
      _dataType === 'CALCULATE_FORMULA'
        ? Number(data.formulaCount || 0)
        : _dataQty;
    const _actDtlList: Array<any> = [];
    for (let i = 0; i < _count; i++) {
      if (data.actDtlList?.length) {
        _actDtlList.push({
          ...data?.actDtlList[i],
          inspectDocLineActDtlId: data?.actDtlList[i]?.inspectDocLineActDtlId,
          inspectValue: data[`INSPECT_VALUE${i}`],
        })
      } else {
        _actDtlList.push({
          inspectResult: '',
          inspectValue: data[`INSPECT_VALUE${i}`],
        })
      }
    }
    data.actDtlList = _actDtlList;
    _record.set('actRemark', data.actRemark)
    _record.set('ngQty', data.ngQty)
    _record.set('okQty', data.okQty)
    _record.set('inspectResult', data.inspectResult)
    _record.set('actEnclosure', data.actEnclosure)
    _record.set('lineEnclosure', data.lineEnclosure)
    _record.set('actDtlList', data.actDtlList)
    record.set('docLineList', taskLineDs.toData())
  }

  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.LINE_BASIC`,
    },
    <Table
      dataSet={taskLineDs}
      columns={taskLineColumns}
      highLightRow={false}
      customizedCode="inspectDocMaintain-panelTable"
    />,
  );
});

export default PanelTable;
