/**
 * @Description: MSA分析管理平台-一致性图表界面
 * @Author: <EMAIL>
 * @Date: 2023/8/24 15:42
 */
import React, { useState, useEffect, useMemo } from 'react';
import { Row, Col } from 'choerodon-ui';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import Table from './TableComponent';
import styles from '../MsaAnalysisManagementPlatformAnalysisDetail/index.modules.less';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';

const UniformityGraphic = ({ dataSoure, analyseResultDs }) => {
  const {
    consistenceTableInfo: {
      kappaTableInfo,
      measuredByTableInfo,
      allConsistentPercentStr,
      allConsistentQty,
      measuredByAllConsistentPercentStr,
      measuredByAllConsistentQty,
    },
  } = dataSoure;
  const [userList, setUserList] = useState<any>([]);
  const [dataMap, setDataMap] = useState<any>({});

  useEffect(() => {
    if (!kappaTableInfo?.length && !measuredByTableInfo?.length) {
      return;
    }
    handleInitTable();
  }, [kappaTableInfo?.length, measuredByTableInfo?.length]);

  const handleInitTable = () => {
    const _userList: any = [];
    const table1Data: any = [];
    const table2Data: any = [{}];
    const table3Data: any = [{}, {}];
    const table4Data: any = [{}, {}];
    const table5Data: any = [];

    (measuredByTableInfo || []).forEach(item => {
      const {
        measuredBy,
        measuredByName,
        consistentPercentStr,
        consistentQty,
        kappa,
        leakJudgmentPercentStr,
        measuredByConsistentPercentStr,
        measuredByConsistentQty,
        mistakeJudgmentPercentStr,
      } = item;
      _userList.push({ measuredBy, measuredByName });

      table1Data.push({ rowId: measuredBy, rowName: measuredByName, [measuredBy]: '-' });

      table2Data[0][measuredBy] = kappa;

      table2Data.forEach(item => {
        item.rowName = 'Kappa';
        item[measuredBy] = kappa;
      });

      table3Data.forEach((item, index) => {
        item.rowName = index
          ? intl.get(`${modelPrompt}.consistencyAndValidity`).d('一致性/有效性')
          : intl.get(`${modelPrompt}.consistentQty`).d('一致性零件数量');
        item[measuredBy] = index ? measuredByConsistentPercentStr : measuredByConsistentQty;
        item.all = index ? measuredByAllConsistentPercentStr : measuredByAllConsistentQty;
      });

      table4Data.forEach((item, index) => {
        item.rowName = index
          ? intl.get(`${modelPrompt}.consistencyAndValidity`).d('一致性/有效性')
          : intl.get(`${modelPrompt}.consistentQty`).d('一致性零件数量');
        item[measuredBy] = index ? consistentPercentStr : consistentQty;
        item.all = index ? allConsistentPercentStr : allConsistentQty;
      });

      table5Data.push({
        rowId: measuredBy,
        rowName: measuredByName,
        consistentPercentStr,
        leakJudgmentPercentStr,
        mistakeJudgmentPercentStr,
      });
    });
    (kappaTableInfo || []).forEach((item, index) => {
      const { measuredByColumn, humanKappa } = item;
      const rowIndex = Math.floor(index / (_userList?.length - 1));
      table1Data[rowIndex][measuredByColumn] = humanKappa;
    });

    table5Data.push({
      rowId: 'all',
      rowName: intl.get(`${modelPrompt}.systemConsistent`).d('系统的有效性'),
      consistentPercentStr: allConsistentPercentStr,
      leakJudgmentPercentStr: '-',
      mistakeJudgmentPercentStr: '-',
    });
    setUserList(_userList);
    setDataMap({
      table1Data,
      table2Data,
      table3Data,
      table4Data,
      table5Data,
    });
  };

  const table1Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: 'Kappa',
      },
      ...(userList || []).map(item => ({
        name: item?.measuredBy,
        title: item?.measuredByName,
      })),
    ],
    [userList?.length],
  );

  const table2Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: '',
      },
      ...(userList || []).map(item => ({
        name: item?.measuredBy,
        title: `${item?.measuredByName}${intl.get(`${modelPrompt}.andBasic`).d('与基准')}`,
      })),
    ],
    [userList?.length],
  );

  const table3Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: '',
      },
      ...(userList || []).map(item => ({
        name: item?.measuredBy,
        title: item?.measuredByName,
      })),
      {
        name: 'all',
        title: intl.get(`${modelPrompt}.allPerson`).d('所有评价人皆一致'),
      },
    ],
    [userList?.length],
  );

  const table4Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: '',
      },
      ...(userList || []).map(item => ({
        name: item?.measuredBy,
        title: item?.measuredByName,
      })),
      {
        name: 'all',
        title: intl.get(`${modelPrompt}.allPersonSameWithStandard`).d('所有评价人皆与标准一致'),
      },
    ],
    [userList?.length],
  );

  const table5Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: '',
      },
      {
        name: 'consistentPercentStr',
        title: intl.get(`${modelPrompt}.effectiveness`).d('有效性'),
      },
      {
        name: 'leakJudgmentPercentStr',
        title: intl.get(`${modelPrompt}.leakJudgmentPercentStr`).d('漏判率'),
      },
      {
        name: 'mistakeJudgmentPercentStr',
        title: intl.get(`${modelPrompt}.mistakeJudgmentPercentStr`).d('误判率'),
      },
    ],
    [],
  );

  return (
    <div>
      {Boolean(dataSoure?.tableInfo?.length) && (
        <>
          <br/>
          <br/>
          <h4>{intl.get(`${modelPrompt}.title.dataAnalyse`).d('数据分析')}</h4>
          <div className='containerBorder'>
            <Row>
              <Col span={12}>
                <Table
                  data={dataMap.table1Data}
                  columns={table1Column}
                  title={intl.get(`${modelPrompt}.personKappa`).d('评价人之间的Kappa系数')}
                />
                <Table
                  data={dataMap.table2Data}
                  columns={table2Column}
                  title={intl
                    .get(`${modelPrompt}.personKappaWithStandard`)
                    .d('评价人与基准之间的Kappa系数')}
                />
                <Table data={dataMap.table3Data} columns={table3Column} title={intl.get(`${modelPrompt}.personSelfConsistency`).d('评价人自我一致性')} />
                <Table
                  data={dataMap.table4Data}
                  columns={table4Column}
                  title={intl
                    .get(`${modelPrompt}.personSelfConsistencyAndStandard`)
                    .d('评价人自我一致且与基准一致性')}
                />
              </Col>
              <Col span={12}>
                <Table data={dataMap.table5Data} columns={table5Column} title="评价人能力汇总表" />
              </Col>
            </Row>
          </div>
          <br/>
          <h4>{intl.get(`${modelPrompt}.title.msaResult`).d('分析结果')}</h4>
          <br/>
          <div className={styles.resultContainer}>
            <table>
              <tbody>
                <tr>
                  <td> {intl.get(`${modelPrompt}.label.msaResult`).d('分析结果')}:</td>
                  <td>{analyseResultDs.current?.getField('msaResult')!.getText()}</td>
                </tr>
                <tr>
                  <td> {intl.get(`${modelPrompt}.label.msaConclusion`).d('分析结论')}: </td>
                  <td>
                    {analyseResultDs.current?.get('msaConclusion')}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
};

export default observer(UniformityGraphic);
