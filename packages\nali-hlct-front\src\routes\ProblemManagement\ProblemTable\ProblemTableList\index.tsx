import React, { useState, useCallback, useEffect } from 'react';
import { DataSet, Button, Spin } from 'choerodon-ui/pro';
import { Icon } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import RGL, { WidthProvider, Layout } from 'react-grid-layout';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import 'react-grid-layout/css/styles.css';
import { useRequest } from '@components/tarzan-hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentUserId } from 'utils/utils';
import notification from 'utils/notification';
import styles from './index.model.less';
import OverviewForm from '../components/OverviewForm';
import PersonalTodo from '../components/PersonalTodo';
import IndividualPoints from '../components/IndividualPoints';
import Underway from '../components/Underway';
import ClosedIssue from '../components/ClosedIssue';
import {
  OverviewAndTodoListDS,
  PersonalTodoDS,
  UnderwayDS,
  ClosedIssueDS,
} from '../stores/ProblemTableDS';
import {
  PersonalTodoQueryInfo,
  UnderwayQueryInfo,
  ClosedIssueQueryInfo,
  LayoutSaveInfo,
  LayoutQueryInfo,
} from '../services';

const modelPrompt = 'problem.table';
const ReactGridLayout = WidthProvider(RGL);

const initialLayouts: Layout[] = [
  { i: '1', x: 0, y: 0, w: 12, h: 9 },
  { i: '2', x: 0, y: 9, w: 8, h: 20 },
  { i: '3', x: 8, y: 9, w: 4, h: 20 },
  { i: '4', x: 0, y: 28, w: 12, h: 28 },
  { i: '5', x: 0, y: 56, w: 12, h: 28 },
];

const ProblemTableList = props => {
  const {
    match: { path },
    OverviewAndTodoListDs,
    PersonalTodoDs,
    UnderwayDs,
    ClosedIssueDs,
    history,
  } = props;

  const [layout, setLayout] = useState<Layout[]>([...initialLayouts]);
  const [editing, setEditing] = useState(false);

  const {
    run: personalTodoQueryInfo,
    loading: personalTodoLoading,
  } = useRequest(PersonalTodoQueryInfo(), { manual: true });
  const { run: underwayQueryInfo, loading: underwayLoading } = useRequest(UnderwayQueryInfo(), {
    manual: true,
  });
  const {
    run: closedIssueQueryInfo,
    loading: closedIssueLoading,
  } = useRequest(ClosedIssueQueryInfo(), { manual: true });
  const { run: layoutSaveInfo } = useRequest(LayoutSaveInfo(), { manual: true });
  const { run: layoutQueryInfo, loading: layoutQueryLoading } = useRequest(LayoutQueryInfo(), {
    manual: true,
  });

  useEffect(() => {
    OverviewAndTodoListDs.query()
    queryCard()
  }, [])

  const queryCard = () => {
    layoutQueryInfo({
      params: {
        userId: getCurrentUserId(),
      },
      onSuccess: res => {
        if (res.content.length) {
          const data: any[] = [];
          res.content.forEach(item => {
            data.push({
              i: `${item.cardId}`,
              x: item.cardX,
              y: item.cardY,
              w: item.cardW,
              h: item.cardH,
            });
          });
          setLayout(data);
        }
      },
    });
  };

  const handleEditLayout = () => {
    setEditing(true);
  };
  const handleCancelLayout = () => {
    setEditing(false);
    queryCard();
  };

  // layout 发生变化时，保存到state中
  const handleLayoutChange = useCallback((newLayout: Layout[]) => {
    setLayout(newLayout);
  }, []);

  const handleSaveLayout = () => {
    const params: any = [];
    layout.forEach(item =>
      params.push({
        cardId: item.i,
        cardX: item.x,
        cardY: item.y,
        cardW: item.w,
        cardH: item.h,
        userId: getCurrentUserId(),
        workcellId: 0,
      }),
    );
    layoutSaveInfo({
      params,
      onSuccess: res => {
        if (res) {
          const data: any[] = [];
          res.forEach(item => {
            data.push({
              i: `${item.cardId}`,
              x: item.cardX,
              y: item.cardY,
              w: item.cardW,
              h: item.cardH,
            });
          });
          setLayout(data);
          notification.success({ message: intl.get(`${modelPrompt}.layout.saved.successfully`).d('布局保存成功') });
          setEditing(false);
        }
      },
    });
  };

  return (
    <div className="hmes-style">
      <Spin spinning={layoutQueryLoading}>
        <Header title={intl.get(`${modelPrompt}.ProblemTableList`).d('问题管理工作台')}>
          {editing ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSaveLayout}>
                {intl.get(`${modelPrompt}.preservation`).d('保存')}
              </Button>
              <Button icon="remove_circle_outline" onClick={handleCancelLayout}>
                {intl.get(`${modelPrompt}.cancel`).d('取消')}
              </Button>
            </>
          ) : (
            <>
              <Button onClick={handleEditLayout}>
                <Icon type="settings-o" />
              </Button>
              <PermissionButton
                type="c7n-pro"
                onClick={() => {
                  history.push({
                    pathname: `/hwms/problem-management/problem-management-platform/list`,
                    state: {
                      stateType: 'create',
                    },
                  });
                }}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.create`).d('新建问题')}
              </PermissionButton>
            </>
          )}
        </Header>
        <Content style={{ backgroundColor: '#f4f5f7', padding: '0' }}>
          <ReactGridLayout
            width={100}
            style={{ position: 'relative' }}
            verticalCompact
            className={styles.gridLayoutContainer}
            cols={12} // 列的单位数。例如 6 就是把整个宽度分成 6 份
            isDraggable={editing} // 仅在编辑时可拖拽
            isResizable={editing} // 是否在编辑时可拖拽大小
            rowHeight={1} // 行单位高度
            layout={layout}
            margin={[12, 12]} // 设置每两个卡片之前的间距，分别对应x, y
            onLayoutChange={handleLayoutChange}
          >
            <div key="1">
              <OverviewForm history={history} ds={OverviewAndTodoListDs} />
            </div>
            <div key="2">
              <PersonalTodo
                history={history}
                ds={PersonalTodoDs}
                personalTodoQueryInfo={personalTodoQueryInfo}
                personalTodoLoading={personalTodoLoading}
              />
            </div>
            <div key="3">
              <IndividualPoints history={history} ds={OverviewAndTodoListDs} />
            </div>
            <div key="4">
              <Underway
                history={history}
                ds={UnderwayDs}
                underwayQueryInfo={underwayQueryInfo}
                underwayLoading={underwayLoading}
              />
            </div>
            <div key="5">
              <ClosedIssue
                ds={ClosedIssueDs}
                closedIssueQueryInfo={closedIssueQueryInfo}
                closedIssueLoading={closedIssueLoading}
                history={history}
              />
            </div>
          </ReactGridLayout>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const OverviewAndTodoListDs = new DataSet({
        ...OverviewAndTodoListDS(),
      });
      const PersonalTodoDs = new DataSet(PersonalTodoDS());
      const UnderwayDs = new DataSet(UnderwayDS());
      const ClosedIssueDs = new DataSet(ClosedIssueDS());
      return {
        OverviewAndTodoListDs,
        PersonalTodoDs,
        UnderwayDs,
        ClosedIssueDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ProblemTableList),
);
