/*
 * @Description: SIP文件管理-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-22 09:36:22
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-01-16 15:26:48
 */
import React, { useMemo, useState, useEffect, useRef } from 'react';
import {
  Table,
  DataSet,
  Button,
  Tabs,
  Select,
  Modal,
  Form,
  Lov,
  TextField,
  Attachment,
  DatePicker,
  TextArea,
} from 'choerodon-ui/pro';
import { Tree } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnLock } from 'choerodon-ui/pro/es/table/enum';
import intl from 'utils/intl';
import request from 'utils/request';
import { getResponse } from 'utils/utils';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import moment from 'moment';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import { useRequest } from '@components/tarzan-hooks';
import { drawerPropsC7n } from '@components/tarzan-ui';
import DragGrid from '../DragGrid';
import { ApprovalSIPFiles, SaveSIPFile, UpdateSIPFile } from '../services';
import { tableDS, historyDS } from '../stores';

const modelPrompt = 'tarzan.qms.inspectExecute.SIPFileManagement';
const TabPane = Tabs.TabPane;
const { DirectoryTree } = Tree;

const SIPFileList = props => {
  const { 
    centerTableDs,
    personTableDs,
    historyDs,
    history,
    location: { state },
  } = props;
  const [treeData, setTreeData] = useState([]); // 分类标签数据
  const [currentTab, setCurrentTab] = useState('center'); // 当前Tab页签
  const [selectIdList, setSelectIdList] = useState([]); // 记录表格勾选数据
  const [leftExpandedKeys, setLeftExpandedKeys] = useState<any>([]); // 文件中心展开的树节点
  const [leftSelectKeys, setLeftSelectKeys] = useState<any>([]); // 文件中心选中的树节点
  const [rightExpandedKeys, setRightExpandedKeys] = useState<any>([]); // 个人平台默认展开的树节点
  const [rightSelectKeys, setRightSelectKeys] = useState<any>([]); // 个人平台选中的树节点
  const leftTreeRef = useRef(null);
  const rightTreeRef = useRef(null);
  const { run: queryFirstLevel } = useRequest(
    { lovCode: 'YP.QIS.SIP_FILETYPE_FIRST_LEVEL' },
    { manual: true, needPromise: true },
  );
  const { run: querySecondLevel } = useRequest(
    { lovCode: 'YP.QIS.SIP_FILETYPE_SECOND_LEVEL' },
    { manual: true, needPromise: true },
  );

  useEffect(() => {
    if (Object.keys(props?.location?.state || {}).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      // tableDs.query(props.tableDs.currentPage);
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const {
      sipCode,
    } = state || {};
    centerTableDs.queryDataSet.current?.set('sipCode', sipCode);
    if (treeData?.length) {
      setCurrentTab('center');
      handleQueryTable('center', leftSelectKeys);
    } else {
      handleInitTree();
    }
    history.replace({ ...history.location, state: undefined });
  }, [props?.location?.state]);

  useEffect(() => {
    handleInitTree();
  }, []);

  const handleUpdateSelect = () => {
    const _selectIdList = personTableDs.selected.map(_record => _record?.get('sipId'));
    setSelectIdList(_selectIdList);
  };

  useDataSetEvent(personTableDs, 'batchSelect', handleUpdateSelect);
  useDataSetEvent(personTableDs, 'batchUnSelect', handleUpdateSelect);

  const handleInitTree = async () => {
    const firstLevelList = await queryFirstLevel({});
    const secondLevelList = await querySecondLevel({});
    if (firstLevelList?.length && secondLevelList?.length) {
      const _expendedKeys: string[] = [];
      const _selectKeys: string[] = [];
      const _tree = firstLevelList.map((firstItem, firstItemIndex) => {
        const currentKey = JSON.stringify({
          level: firstItemIndex,
          parent: null,
          key: firstItem.value,
        });
        _expendedKeys.push(currentKey);
        return {
          title: firstItem.meaning,
          key: currentKey,
          children: secondLevelList
            .filter(secondItem => secondItem.tag === firstItem.value)
            .map((secondItem, secondIndex) => {
              const _key = JSON.stringify({
                level: firstItemIndex,
                parent: firstItem.value,
                key: secondItem.value,
              });
              if (firstItemIndex === 0 && secondIndex === 0) {
                _selectKeys.push(_key);
              }
              return {
                title: secondItem.meaning,
                key: _key,
              };
            }),
        };
      });
      setTreeData(_tree);
      setLeftExpandedKeys(_expendedKeys);
      setLeftSelectKeys(_selectKeys);
      setRightExpandedKeys(_expendedKeys);
      setRightSelectKeys(_selectKeys);
      handleQueryTable(currentTab, _selectKeys);
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'sequence',
        renderer: ({ record }) => {
          const id = record?.index || 0;
          return id + 1;
        },
      },
      {
        name: 'siteName',
        width: 180,
      },
      {
        name: 'sipCode',
        width: 150,
        renderer: ({ value, record }) => {
          return <a onClick={() => handleEdit(record, 'edit')}>{value}</a>;
        },
      },
      {
        name: 'version',
      },
      {
        name: 'sipStatus',
      },
      {
        name: 'sipName',
        width: 150,
      },
      {
        name: 'sipType',
      },
      {
        name: 'authoredDptName',
      },
      {
        name: 'authoredByName',
      },
      {
        name: 'editDate',
      },
      {
        name: 'sipFileUuid',
      },
      {
        name: 'remarks',
      },
      {
        name: 'operation',
        width: 230,
        header: intl.get(`${modelPrompt}.operation`).d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          return (
            <>
              <Button
                funcType={FuncType.flat}
                disabled={
                  record?.get('sipStatus') !== 'EFFECTIVE' || record?.get('currentFlag') !== 'Y'
                }
                onClick={() => handleEdit(record, 'update')}
              >
                {intl.get(`${modelPrompt}.operation.update`).d('升版')}
              </Button>
              <ApprovalInfoDrawer
                objectTypeList={['QIS_SIP_FILE_LWS']}
                objectId={record?.get('sipId')}
                type="text"
              />
              <Button funcType={FuncType.flat} onClick={() => handleHistory(record)}>
                {intl.get(`${modelPrompt}.operation.history`).d('版本历史')}
              </Button>
            </>
          );
        },
      },
    ];
  }, [currentTab, leftSelectKeys, rightSelectKeys]);

  const handleHistory = record => {
    historyDs.setQueryParameter('sipCode', record.data.sipCode);
    historyDs.query();
    Modal.open({
      ...drawerPropsC7n({ canEdit: false, ds: historyDs }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.historyQuery`).d('历史查询'),
      style: {
        width: 1080,
      },
      children: <Table dataSet={historyDs} columns={historyColumns} />,
    });
  };

  const historyColumns: ColumnProps[] = [
    {
      name: 'sequence',
      renderer: ({ record }) => {
        const id = record?.index || 0;
        return id + 1;
      },
    },
    {
      name: 'version',
    },
    {
      name: 'sipStatus',
    },
    {
      name: 'authoredDptName',
    },
    {
      name: 'authoredByName',
    },
    {
      name: 'editDate',
    },
    {
      name: 'sipFileUuid',
    },
    {
      name: 'remarks',
    },
  ];

  // 附件配置
  const attachmentProps: any = {
    useChunk: true,
    name: 'sipFileUuid',
    bucketName: 'qms',
    bucketDirectory: 'sip-file-management',
    accept: [
      '.doc',
      '.ppt',
      '.docx',
      '.xlsx',
      '.xls',
      '.deb',
      '.txt',
      '.pdf',
      'image/*',
      '.mp4',
      '.webm',
      '.ogv',
      '.m3u8',
      '.flv',
      '.rtmp',
    ],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  // 确定
  const handleDrawerConfirm = async (record, dataSet, operationType) => {
    const validate = await record.validate(true);
    if (!validate) {
      return false;
    }
    const params = record.toData();
    const { url = '', method } = operationType === 'edit' ? SaveSIPFile() : UpdateSIPFile();
    return request(url, {
      method,
      body: {
        ...params,
        currentFlag: 'Y',
        editDate: moment(params.editDate).format('YYYY-MM-DD 00:00:00'),
      },
    }).then(res => {
      if (res && !res.failed) {
        notification.success({});
        dataSet.query();
        dataSet.clearCachedSelected();
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  // 打开抽屉的回调，其中operationType标识操作类型
  // operationType='edit'-新建/编辑
  // operationType='update'-升版
  const handleEdit = (record, operationType) => {
    const dataSet = currentTab === 'center' ? centerTableDs : personTableDs;
    const selectKey = currentTab === 'center' ? leftSelectKeys : rightSelectKeys;
    let currentRecord = record;
    if (!record) {
      currentRecord = dataSet.create({}, 0);
    }
    currentRecord.setState('operationType', operationType);
    if (operationType === 'update') {
      const version = currentRecord?.get('version').match(/([a-zA-Z]+)(\d+)/);
      currentRecord.set('version', `${version[1]}${Number(version[2]) + 1}`);
      currentRecord.set('sipStatus', 'AMENDING');
      currentRecord.set('sipFileUuid', null);
      currentRecord.set('responsLov', null);
      currentRecord.set('editDate', moment(moment().format('YYYY-MM-DD 00:00:00')));
      currentRecord.set('remarks', null);
    }
    Modal.open({
      ...drawerPropsC7n({
        canEdit:
          ['NEW', 'REJECTED', 'AMENDING'].includes(currentRecord?.get('sipStatus')) ||
          operationType === 'update',
        ds: dataSet,
      }),
      key: Modal.key(),
      title: !record
        ? intl.get(`${modelPrompt}.title.newFile`).d('新建SIP文件')
        : intl.get(`${modelPrompt}.title.editFile`).d('编辑SIP文件'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      footer: (okBtn, cancelBtn) => {
        return ['NEW', 'REJECTED', 'AMENDING'].includes(currentRecord?.get('sipStatus')) ||
          operationType === 'update'
          ? [cancelBtn, okBtn]
          : [cancelBtn];
      },
      onOk: () => handleDrawerConfirm(currentRecord, dataSet, operationType),
      children: (
        <Form labelWidth={112} record={currentRecord} columns={1}>
          <Lov name="siteLov" />
          <TextField name="sipCode" />
          <TextField name="version" />
          <Select name="sipStatus" />
          <TextField name="sipName" />
          <Select
            name="sipType"
            optionsFilter={record => {
              const tagList = getSecondList(selectKey);
              return tagList.includes(record?.get('tag'));
            }}
          />
          <Attachment {...attachmentProps} />
          <Lov name="departmentLov" />
          <Lov name="responsLov" />
          <DatePicker name="editDate" />
          <TextArea name="remarks" autoSize={{ minRows: 2, maxRows: 8 }} />
        </Form>
      ),
    });
  };

  const handleChangeTab = value => {
    setCurrentTab(value);
    const currentKey = value === 'center' ? leftSelectKeys : rightSelectKeys;
    handleQueryTable(value, currentKey);
  };

  const handleSelectKey = key => {
    if (currentTab === 'center') {
      setLeftSelectKeys(key);
    } else {
      setRightSelectKeys(key);
    }
    handleQueryTable(currentTab, key);
  };

  const handleQueryTable = (currentTab, currentKey = ['']) => {
    const dataSet = currentTab === 'center' ? centerTableDs : personTableDs;
    const { parent = '', key = '' } = JSON.parse(currentKey[0]);
    if (!parent) {
      dataSet.setQueryParameter('sipFirstType', key);
      dataSet.setQueryParameter('sipSecondType', undefined);
    } else {
      dataSet.setQueryParameter('sipFirstType', parent);
      dataSet.setQueryParameter('sipSecondType', key);
    }
    dataSet.query();
  };

  const getSecondList = selectKey => {
    const { parent, key } = JSON.parse(selectKey[0]);
    if (parent) {
      return [key];
    }
    const currentNode: any = treeData.find((item: any) => item.key === selectKey[0]) || {};
    return (currentNode.children || []).map(item => JSON.parse(item.key).key);
  };

  const handleApproval = () => {
    return new Promise(async (resolve) => {
      const validateFlag = personTableDs.selected.find(
        _record => !['NEW', 'REJECTED', 'AMENDING'].includes(_record?.get('sipStatus')),
      );
      if (validateFlag) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.validate.approval`)
            .d('只有新建、驳回、修订状态的文件可提交审批，请检查！'),
        });
        return resolve(false);
      }
      const { url = '', method } = ApprovalSIPFiles();
      const requestRes = await request(url, { method, body: selectIdList });
      const res = getResponse(requestRes);
      if (res) {
        notification.success({});
        await personTableDs.query();
        return resolve(true);
      }
      return resolve(false);
    })
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('SIP文件管理')}>
        {currentTab === 'person' && (
          <>
            <Button color={ButtonColor.primary} icon="add" onClick={() => handleEdit(null, 'edit')}>
              {intl.get('tarzan.common.button.create').d('新建')}
            </Button>
            <Button disabled={!selectIdList?.length} onClick={handleApproval}>
              {intl.get(`${modelPrompt}.button.approval`).d('提交审批')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Tabs activeKey={currentTab} onChange={handleChangeTab}>
          <TabPane tab={intl.get(`${modelPrompt}.tabPane.center`).d('文件中心')} key="center">
            <DragGrid
              article={
                <DirectoryTree
                  expandedKeys={leftExpandedKeys}
                  selectedKeys={leftSelectKeys}
                  onExpand={val => setLeftExpandedKeys(val)}
                  onSelect={handleSelectKey}
                  treeData={treeData}
                  ref={leftTreeRef}
                />
              }
              aside={
                <Table
                  searchCode="SIPFileManagement_searchCode"
                  queryBar={TableQueryBarType.filterBar}
                  queryBarProps={{
                    fuzzyQuery: false,
                  }}
                  dataSet={centerTableDs}
                  queryFields={{
                    sipType: (
                      <Select
                        name="sipType"
                        optionsFilter={record => {
                          const tagList = getSecondList(leftSelectKeys);
                          return tagList.includes(record?.get('tag'));
                        }}
                      />
                    ),
                  }}
                  columns={columns}
                  customizedCode="SIPFileManagement_customizedCode"
                />
              }
              asideShow
              key={currentTab}
            />
          </TabPane>
          <TabPane tab={intl.get(`${modelPrompt}.tabPane.person`).d('个人平台')} key="person">
            <DragGrid
              article={
                <DirectoryTree
                  expandedKeys={rightExpandedKeys}
                  selectedKeys={rightSelectKeys}
                  onExpand={val => setRightExpandedKeys(val)}
                  onSelect={handleSelectKey}
                  treeData={treeData}
                  ref={rightTreeRef}
                />
              }
              aside={
                <Table
                  searchCode="SIPFIleManagementList_searchCode"
                  queryBar={TableQueryBarType.filterBar}
                  queryBarProps={{
                    fuzzyQuery: false,
                  }}
                  queryFields={{
                    sipType: (
                      <Select
                        name="sipType"
                        optionsFilter={record => {
                          const tagList = getSecondList(rightSelectKeys);
                          return tagList.includes(record?.get('tag'));
                        }}
                      />
                    ),
                  }}
                  dataSet={personTableDs}
                  columns={columns}
                  customizedCode="SIPFIleManagementList_customizedCode"
                />
              }
              asideShow
              key={currentTab}
            />
          </TabPane>
        </Tabs>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      // 文件中心
      const centerTableDs = new DataSet({
        ...tableDS('center'),
        selection: false,
      });
      // 个人平台
      const personTableDs = new DataSet({
        ...tableDS('person'),
      });
      // 历史记录
      const historyDs = new DataSet({
        ...historyDS(),
      });
      return {
        centerTableDs,
        personTableDs,
        historyDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(SIPFileList),
);
