/**
 * @Description: 检验项目组维护明细界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:55:10
 * @LastEditTime: 2023-05-24 16:31:25
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import intl from 'utils/intl';
import { Table, Spin, Lov, TextArea, Button } from 'choerodon-ui/pro';
import { ColumnAlign, TableButtonType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { observer } from 'mobx-react';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';

const InspectItemTab = props => {
  const { canEdit, tableDs, loading } = props;

  const RenderDeleteButton = observer(({ dataSet, canEdit }) => {
    return (
      <>
        <Button
          icon="delete"
          funcType={FuncType.flat}
          color={ButtonColor.red}
          disabled={
            !canEdit ||
            !dataSet.selected.length ||
            !['NEW', 'REJECTED'].includes(dataSet.parent.current.get('verificationStatus'))
          }
          onClick={() => dataSet.remove(dataSet.selected)}
        >
          {intl.get('hzero.common.button.delete').d('删除')}
        </Button>
      </>
    );
  });

  const columns: ColumnProps[] = [
    {
      name: 'sequence',
      width: 80,
      align: ColumnAlign.center,
      renderer: ({ record }) => {
        return <span>{record && (record.index + 1) * 10}</span>;
      },
    },
    {
      name: 'taskContent',
      // width: 200,
      editor: () => canEdit && <TextArea name="taskContent" required />,
    },
    {
      name: 'departmentLov',
      width: 200,
      editor: () => canEdit && <Lov name="departmentLov" required />,
    },
  ];

  return (
    <Spin spinning={loading}>
      <Table
        dataSet={tableDs}
        columns={columns}
        buttons={[
          <RenderDeleteButton dataSet={tableDs} canEdit={canEdit} />,
          [TableButtonType.add, { disabled: !canEdit, icon: 'add' }],
        ]}
        highLightRow
      />
    </Spin>
  );
};

export default InspectItemTab;
