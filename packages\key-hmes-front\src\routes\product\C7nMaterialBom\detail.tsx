/**
 * @Description: 物料装配清单 - 详情页面（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/8/15 9:48
 * @LastEditTime: 2023-05-18 15:06:27
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import formatterCollections from 'utils/intl/formatterCollections';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { AssemblyDetail, AssemblyDetailProps } from '../../../components/C7nAssemblyCommon/AssemblyDetail';

const C7nMaterialBomDetail = props => {
  // 装配组件详情页入参
  const assemblyDetailProps: AssemblyDetailProps = {
    history: props.history,
    match: props.match,
    featureTitle: intl.get('tarzan.product.bom.title.methodBom').d('物料装配清单'), // 列表页标题title
    attributeServerCode: BASIC.TARZAN_METHOD, // 扩展属性也需要访问不同的服务
    uiServerCode: BASIC.TARZAN_METHOD, // 功能接口要访问的不同的服务
    typeGroup: 'METHOD_BOM_TYPE', // 查询条件与详情页中类型的值集code
    customizeForm: props.customizeForm,
    customizeTable: props.customizeTable,
    custConfig: props.custConfig,
    custCode: 'MATERIAL_BOM_DETAIL',
    pageType: 'materialBom',
  };

  return <AssemblyDetail {...assemblyDetailProps} />;
};

export default flow(
  formatterCollections({ code: ['tarzan.product.bom', 'tarzan.common'] }),
  withCustomize({
    unitCode:
      [
        `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_BOM_DETAIL.BASIC`,
        `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_BOM_DETAIL.COMP`,
        `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_BOM_DETAIL.COMP_DTL`,
        `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_BOM_DETAIL.BUTTON`,
        `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_BOM_DETAIL.BUTTON_COMP`,
      ],
  }),
)(C7nMaterialBomDetail);
