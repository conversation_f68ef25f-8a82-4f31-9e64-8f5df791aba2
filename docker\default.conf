user nginx;
worker_processes 1;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;


events {
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  log_format main '$remote_addr - $remote_user [$time_local] "$request" '
  '$status $body_bytes_sent "$http_referer" '
  '"$http_user_agent" "$http_x_forwarded_for"';

  access_log /var/log/nginx/access.log main;

  sendfile on;

  keepalive_timeout 65;

  gzip on;
  gzip_buffers 32 4k;
  gzip_comp_level 6;
  gzip_min_length 200;
  gzip_types text/css text/xml application/javascript;

  server {
    listen 80;
    server_name localhost;

    location \/[a-z.0-9]\.(js|css|gif|png|jpg)$ {
      expires 7d; # 开启 eTag 缓存
    }

    location /packages/ {
      root   /usr/share/nginx/html;
      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Headers X-Requested-With;
      add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
    }

    location / {
      root /usr/share/nginx/html;
      index index.html index.htm;
      error_page  404 =404 @fallback404; # 启动 Bowser 路由 配置
    }

    location @fallback404 {
      root /usr/share/nginx/html;
      rewrite ^(.*)$ /index.html break;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
      root /usr/share/nginx/html;
    }

  }
}
