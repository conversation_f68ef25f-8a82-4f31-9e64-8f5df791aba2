
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

// 查询站点和产品对应库位
export function fetchLocatorConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-teardown-head`,
    method: 'GET',
  };
}


export function fetchDetailInfoConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-teardown-task/detail-info/ui`,
    method: 'GET',
  };
}
