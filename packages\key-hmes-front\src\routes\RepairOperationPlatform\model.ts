import { Layout } from 'react-grid-layout';
import { getLayout, saveLayout, getEnableCards, updateEnabled } from './service';

export const namespace = '_Template80';

export interface ModelState {
  layout: Layout[];
  enabled: string[];
}

export default {
  namespace,
  state: {
    layout: [],
    enabled: [],
  },

  effects: {
    *fetchLayout(_: any, { call, put }: any) {
      const res = yield call(getLayout);
      if (res) {
        yield put({ type: 'updateState', payload: { layout: res.content } });
      }
      return res?.content;
    },
    *fetchEnableCards(_: any, { call, put }: any) {
      const enables = yield call(getEnableCards);
      if (enables && enables.length) {
        yield put({ type: 'updateState', payload: { enabled: enables } });
      }
      return enables;
    },

    *saveLayout({ payload }: any, { call, put }: any) {
      const { layout, enabled } = payload;
      const res = yield call(saveLayout, layout);
      const enables = yield call(updateEnabled, enabled);
      if (typeof enabled?.length !== 'undefined') {
        yield put({ type: 'updateState', payload: { enabled: enables } });
      }
      return res;
    },
  },

  reducers: {
    updateState(state: any, { payload }: any) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
