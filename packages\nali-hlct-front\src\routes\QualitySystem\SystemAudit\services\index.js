/**
 * @Description: 体系审核管理-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

// ${ BASIC.TARZAN_SAMPLING }

// 提交审批
export function commitOaSubmitConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/commit-oa-submit/ui`,
    method: 'POST',
  };
}

// 查询审核信息
export function fetchAuditInformationConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/plan-info/ui`,
    method: 'GET',
  };
}

// 审核信息保存
export function saveAuditInformationConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/plan-save/ui`,
    method: 'POST',
  };
}

// 查询审核小组
export function fetchAuditGroupConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/group-info/ui`,
    method: 'GET',
  };
}

// 保存审核小组
export function saveAuditGroupConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/group-save/ui`,
    method: 'POST',
  };
}

// 查询审核日程
export function fetchAuditScheduleConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/schedule-info/ui`,
    method: 'GET',
  };
}

// 保存审核日程
export function saveAuditScheduleConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/schedule-save/ui`,
    method: 'POST',
  };
}

// 查询审核记录列表
export function fetchAuditRecordListConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/record-list/ui`,
    method: 'GET',
  };
}

// 查询审核记录详情
export function fetchAuditRecordInfoConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/record-info/ui`,
    method: 'GET',
  };
}

// 保存审核记录详情
export function saveAuditRecordInfoConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/record-save/ui`,
    method: 'POST',
  };
}

// 删除审核记录详情
export function deleteAuditRecordInfoConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/record-delete/ui`,
    method: 'POST',
  };
}

// 提交审核记录
export function commitAuditRecordInfoConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/record-commit/ui`,
    method: 'POST',
  };
}

// 查询审核问题列表
export function fetchAuditProblemListConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/problem-list/ui`,
    method: 'GET',
  };
}

// 保存审核问题
export function saveAuditProblemConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/problem-save/ui`,
    method: 'POST',
  };
}

// 提交审核问题
export function commitAuditProblemConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/problem-commit/ui`,
    method: 'POST',
  };
}

// 查询审核问题基础信息
export function fetchAuditProblemBaseDetailConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/base-detail/ui`,
    method: 'GET',
  };
}

// 查询验证关闭
export function fetchEvaluationConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/evaluation/ui`,
    method: 'GET',
  };
}

// 查询审核关闭详情
export function fetchAuditCloseInfoConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/close-info/ui`,
    method: 'GET',
  };
}

// 保存审核关闭
export function saveAuditCloseConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/close-save/ui`,
    method: 'POST',
  };
}

// 提交审核关闭
export function submitAuditCloseConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/close-oa-submit/ui`,
    method: 'POST',
  };
}

// 查询审核额外信息
export function fetchCategoryDetailConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/category-detail/ui`,
    method: 'GET',
  };
}
