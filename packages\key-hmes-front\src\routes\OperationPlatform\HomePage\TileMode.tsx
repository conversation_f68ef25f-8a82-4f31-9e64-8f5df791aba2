import React, { cloneElement, useCallback } from 'react';
import RGL, { WidthProvider, Layout } from 'react-grid-layout';
import { Icon } from 'choerodon-ui';
import { Button } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useOperationPlatform, DispatchType } from '../contextsStore';
import styles from '../index.module.less';

const ReactGridLayout = WidthProvider(RGL);
const layoutContainerStyle = { position: 'relative' };

const TileMode = (props) => {
  const {
    width,
    editing,
    cards,
    spin,
    priorityLayout,
    nextPriority,
    handleCommonButton,
  } = props;

  const {
    dispatch,
    enabledCards,
    itemLayout,
    enabledButtonKeys,
    commonButton,
  } = useOperationPlatform();


  /**
   * 删除一个卡片
   * 暂存在组件内，只有点击确定之后才会保存到后端
   * 将初始列表存于temPool内，点击取消时重置使用
   */
  const handleDelete = (k: string) => {
    const currentCards = enabledCards.filter(c => c !== k);
    dispatch({
      type: DispatchType.update,
      payload: {
        enabledCards: currentCards,
      },
    });
  };

  // layout 发生变化时，保存到state中
  const handleLayoutChange = useCallback((newLayout: Layout[]) => {
    dispatch({
      type: DispatchType.update,
      payload: {
        itemLayout: newLayout,
      },
    });
  }, []);

  return (
    <div style={{height: '100%'}}>
      <ReactGridLayout
        width={width}
        style={layoutContainerStyle}
        className={`${styles.gridLayoutContainer} ${enabledButtonKeys.length ? styles.gridLayoutContainerHalf : ''}`}
        cols={12} // 列的单位数。例如 6 就是把整个宽度分成 6 份
        isDraggable={editing} // 仅在编辑时可拖拽
        isResizable={editing} // 是否在编辑时可拖拽大小
        rowHeight={100} // 行单位高度
        layout={itemLayout}
        margin={[12, 12]} // 设置每两个卡片之前的间距，分别对应x, y
        onLayoutChange={handleLayoutChange}
      >
        {cards.map(({ key, element }) => (
          <div
            key={key}
            className={`${styles['card-container']} ${editing ? styles['card-container-editing'] : ''}`}
            id="cardisId"
          >
            <div className={styles['card-lalala']}>
              <div
                className={`${styles['card-content']} hzero-draggable-card-content ${editing ? styles['editing-pointer-none'] : ''}`}
              >
                {cloneElement(element, {
                  newLayout: itemLayout,
                  spin,
                  priorityLayout,
                  nextPriority,
                })}
              </div>
              {editing ? (
                <Icon
                  type="close"
                  className={styles.closeBtn}
                  onClick={() => handleDelete(key)}
                />
              ) : null}
            </div>
          </div>
        ))}
      </ReactGridLayout>
      {/* 按钮-下方 */}
      <div
        className={styles.independentButtons}
        style={{ display: enabledButtonKeys.length ? 'flex' : 'none' }}
      >
        {commonButton.map(item => {
          if (enabledButtonKeys.includes(item.value)) {
            return (
              <Button
                color={ButtonColor.primary}
                onClick={() => {
                  handleCommonButton(item);
                }}
              >
                {item.meaning}
              </Button>
            );
          }
          return null;
        })}
      </div>
    </div>
  )
}

export default TileMode;
