/*
 * @Description: 条码信息查询报表-列表页
 * @Author: <<EMAIL>>
 * @Date: 2024-01-09 15:46:26
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-01-23 13:46:13
 */
import React, { useMemo, useState } from 'react';
import { Table, DataSet, Modal, Row, Col, Button, TextField, Icon } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { openTab } from 'utils/menuTab';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { observer } from 'mobx-react';
import { isNil } from 'lodash';
import queryString from 'query-string';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import ExcelExport from 'components/ExcelExportPro';
import { BASIC } from '@utils/config';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { drawerPropsC7n, overrideTableBar } from '@components/tarzan-ui';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import LovModal from '@/components/BatchInput/LovModal';
import { tableDS, historyDS, attributeDS } from '../stores';

const modelPrompt = 'tarzan.hmes.barcodeInfoQueryReport';
const tenantId = getCurrentOrganizationId();
const endUrl = "";

const BarcodeInfoQueryList = props => {
  const { tableDs, historyDs, eoAttributeDs, materialLotAttributeDs, history } = props;

  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
      // handleSearch()
    }
  };

  const historyColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      { name: 'eventId' },
      { name: 'eventTypeCode' },
      { name: 'eventTypeDesc' },
      { name: 'requestTypeId' },
      { name: 'requestTypeCode' },
      { name: 'requestTypeDesc' },
      { name: 'realName' },
    ];
  }, []);

  const handleQueryHistory = record => {
    historyDs.setQueryParameter('materialLotId', record?.get('materialLotId'));
    historyDs.setQueryParameter('eoId', record?.get('eoId'));
    historyDs.query();
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
        ds: historyDs,
      }),
      title: intl.get(`${modelPrompt}.title.historyInfo`).d('历史明细'),
      style: {
        width: 1080,
      },
      children: <Table dataSet={historyDs} columns={historyColumns} />,
    });
  };

  const attributeColumns: ColumnProps[] = useMemo(() => {
    return [{ name: 'attrMeaning' }, { name: 'attrValue' }];
  }, []);

  const handleQueryAttribute = record => {
    eoAttributeDs.setQueryParameter('materialLotId', record?.get('materialLotId'));
    eoAttributeDs.setQueryParameter('eoId', record?.get('eoId'));
    eoAttributeDs.query();
    materialLotAttributeDs.setQueryParameter('materialLotId', record?.get('materialLotId'));
    materialLotAttributeDs.setQueryParameter('eoId', record?.get('eoId'));
    materialLotAttributeDs.query();
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
        ds: historyDs,
      }),
      title: intl.get(`${modelPrompt}.title.historyInfo`).d('历史明细'),
      style: {
        width: 1080,
      },
      children: (
        <Row>
          <Col span={11}>
            <Table dataSet={eoAttributeDs} columns={attributeColumns} />
          </Col>
          <Col offset={2} span={11}>
            <Table dataSet={materialLotAttributeDs} columns={attributeColumns} />
          </Col>
        </Row>
      ),
    });
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'eoIdentification', minWidth: 240, lock: ColumnLock.left },
      { name: 'materialCode', minWidth: 150 },
      { name: 'materialName' },
      { name: 'eoQty' },
      { name: 'uomName' },
      {
        name: 'qualityStatus',
        width: 140,
        align: ColumnAlign.center,
        renderer: ({ record, name }) => record!.getField(name)!.getText(),
      },
      { name: 'specifiedLevel' },
      { name: 'containerCode' },
      {
        name: 'status',
        width: 140,
        align: ColumnAlign.center,
        renderer: ({ record, name }) => record!.getField(name)!.getText(),
      },
      { name: 'workcellCode' },
      { name: 'workcellName', minWidth: 150 },
      { name: 'routerStepStatus' },
      {
        name: 'wipStatus',
        width: 140,
        align: ColumnAlign.center,
        renderer: ({ record, name }) => record!.getField(name)!.getText(),
      },
      {
        name: 'workOrderNum',
        width: 180,
        renderer: ({ value }) => (
          <a
            onClick={() => {
              history.push({
                pathname: '/hmes/workorder-management-platform/list',
                state: { workOrderNum: value },
              })
            }}
          >
            {value}
          </a>
        ),
      },
      { name: 'prodLineCode' },
      { name: 'marking' },
      {
        name: 'ncIncidentNumNotClosed',
        width: 180,
        renderer: ({ record, value }) => (
          <a
            onClick={() => {
              history.push(`/hmes/bad-record/platform/detail/${record?.get('ncIncidentIdNotClosed')}`)
            }}
          >
            {value}
          </a>
        ),
      },
      { name: 'disposalFunctionDesc', minWidth: 150 },
      {
        name: 'reworkFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record, name }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={record!.getField(name)!.getText()}
          />
        ),
      },
      { name: 'operationName', minWidth: 150 },
      { name: 'endOperationName', minWidth: 150 },
      {
        name: 'interceptionFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record, name }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={record!.getField(name)!.getText()}
          />
        ),
      },
      { name: 'interceptionOperationName', minWidth: 150 },
      {
        name: 'degradeFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record, name }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={record!.getField(name)!.getText()}
          />
        ),
      },
      {
        name: 'actualStartTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'actualEndTime',
        align: ColumnAlign.center,
        width: 150,
      },
      { name: 'mtModLocatorCode' },
      { name: 'mtModLocatorName' },
      { name: 'lot' },
      { name: 'blueGlueCode' },
      { name: 'gbCode' },
      {
        name: 'operation',
        width: 150,
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        renderer: ({ record }) => (
          <span className="action-link">
            <a onClick={() => handleQueryHistory(record)}>
              {intl.get(`${modelPrompt}.button.historyDetail`).d('历史明细')}
            </a>
            <a onClick={() => handleQueryAttribute(record)}>
              {intl.get(`${modelPrompt}.button.attributeProps`).d('扩展属性')}
            </a>
          </span>
        ),
      },
    ];
  }, []);

  // 条码相关的跳转按钮禁用逻辑
  const advancedQueryDisabled = (() => {
    const { identificationList, workOrderNum, materialIdList } = tableDs.queryDataSet.current?.toData() || {};
    return !identificationList?.length && !workOrderNum && !materialIdList?.length;
  })();

  // 条码相关的跳转按钮禁用逻辑
  const barcodeRelatedBtnDisabled = (() => {
    return !tableDs.selected?.length;
  })();

  // 携带条码信息跳转相关功能的回调
  const jumpToBarcodeRelatedPage = targetUrl => {
    const identifications = tableDs.selected?.map(_record => _record?.get('eoIdentification'))?.join(',');
    history.push({
      pathname: targetUrl,
      state: { identifications },
    });
  };

  const handleAdvancedQuery = () => {
    const { identificationList, workOrderNum, containerLov } = tableDs.queryDataSet.current?.toData() || {};
    openTab({
      key: `/hmes/barcode-info-query-report/advanced-query/list`,
      title: intl.get(`${modelPrompt}.title.advancedQuery`).d('高级查询'),
      search: queryString.stringify({
        identificationList: (identificationList || []).join(','),
        workOrderNum: (workOrderNum || []).join(','),
        containerLov: JSON.stringify(containerLov),
      }),
    });
  };

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i]) || i.includes('Lov') || i === '__dirty') {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('条码信息查询报表')}>
        <Button
          color={ButtonColor.primary}
          disabled={advancedQueryDisabled}
          onClick={() => handleAdvancedQuery()}
        >
          {intl.get(`${modelPrompt}.button.advancedQuery`).d('高级查询')}
        </Button>
        <Button
          disabled={barcodeRelatedBtnDisabled}
          onClick={() => jumpToBarcodeRelatedPage('/hmes/product-processing-history-query-report')}
        >
          {intl.get(`${modelPrompt}.button.productProcessingHistory`).d('加工履历')}
        </Button>
        <Button
          disabled={barcodeRelatedBtnDisabled}
          onClick={() => jumpToBarcodeRelatedPage('/hmes/nc-query-record')}
        >
          {intl.get(`${modelPrompt}.button.ncRecord`).d('不良记录')}
        </Button>
        {/* <Button
          disabled={barcodeRelatedBtnDisabled}
          onClick={() => jumpToBarcodeRelatedPage('/hmes/workorder-management-platform/list')}
        >
          {intl.get(`${modelPrompt}.button.woManagement`).d('工单管理')}
        </Button> */}
        <Button
          disabled={barcodeRelatedBtnDisabled}
          onClick={() => jumpToBarcodeRelatedPage('/hmes/barcode-marking-binding')}
        >
          {intl.get(`${modelPrompt}.button.marking`).d('MARKING')}
        </Button>
        <Button
          disabled={barcodeRelatedBtnDisabled}
          onClick={() => jumpToBarcodeRelatedPage('/hmes/assembly-record-query')}
        >
          {intl.get(`${modelPrompt}.button.assemblyRecord`).d('装配记录')}
        </Button>
        <ExcelExport
          allBody
          // exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}${endUrl}/v1/${tenantId}/hme-identification/head/export`}
          method="post"
          // otherButtonProps={{
          //   disabled: barcodeRelatedBtnDisabled,
          // }}
          queryParams={getExportQueryParams}
          modalProps={{ drawer: false }}
        >
          {intl.get('htms.FrontConfig.button.export').d('导出')}
        </ExcelExport>
      </Header>
      <Content>
        <Table
          queryBar={overrideTableBar}
          queryFields={{
            identificationList: (
              <TextField
                name="identificationList"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(
                          true,
                          'identificationList',
                          intl.get(`${modelPrompt}.identification`).d('条码号'),
                        )
                      }
                    />
                  </div>
                }
              />
            ),
            workOrderNum: (
              <TextField
                name="workOrderNum"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(
                          true,
                          'workOrderNum',
                          intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
                        )
                      }
                    />
                  </div>
                }
              />
            ),
            lotNumList: (
              <TextField
                name="lotNumList"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(
                          true,
                          'lotNumList',
                          intl.get(`${modelPrompt}.lotNumList`).d('批次号'),
                        )
                      }
                    />
                  </div>
                }
              />
            ),
          }}
          dataSet={tableDs}
          columns={columns}
          customizedCode="barcodeInfoQueryReport_customizedCode"
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      const historyDs = new DataSet({
        ...historyDS(),
      });
      const eoAttributeDs = new DataSet({
        ...attributeDS('EO'),
      });
      const materialLotAttributeDs = new DataSet({
        ...attributeDS('MATERIAL_LOT'),
      });
      return {
        tableDs,
        historyDs,
        eoAttributeDs,
        materialLotAttributeDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(observer(BarcodeInfoQueryList)),
);
