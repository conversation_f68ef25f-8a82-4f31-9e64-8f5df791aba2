/**
 * @Description: 容器管理平台-装载明细抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-04-06 16:27:12
 * @LastEditTime: 2022-04-27 18:04:13
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableMode } from 'choerodon-ui/pro/lib/table/enum';
import axios from 'axios';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const tenantId = getCurrentOrganizationId();

// 这里面可以控制node结点的判断来实现是否展示为叶结点
function nodeCover({ record }) {
  const nodeProps = {
    ...record,
  };
  if (record.get('hasChildFlag') !== 'Y') {
    nodeProps.isLeaf = true;
  }
  return nodeProps;
}

export default ({ ds }) => {
  const handleLoadData = ({ record, dataSet }) => {
    return new Promise(resolve => {
      if (!record.data.children) {
        axios
          .get(
            `${
              BASIC.HWMS_BASIC
            }/v1/${tenantId}/mt-container-load-detail/query/ui?containerId=${record.get(
              'loadObjectId',
            )}`,
          )
          .then((res: any) => {
            dataSet.appendData(
              res.rows.map(item => {
                return {
                  ...item,
                  uuid: `${item.loadObjectId}${item.loadObjectType}`,
                  parentUuid: record.get('uuid'),
                };
              }),
            );
            resolve(true);
          })
          .catch(() => {
            resolve(true);
          });
      } else {
        resolve(true);
      }
    });
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'loadObjectTypeDesc',
        width: 300,
      },
      {
        name: 'loadObjectCode',
        minWidth: 180,
      },
      {
        name: 'loadQty',
        width: 120,
        renderer: ({ value, record }) => {
          if (record?.get('loadObjectType') === 'CONTAINER') {
            return '';
          } if (record?.get('loadObjectType') === 'MATERIAL_LOT') {
            return record?.get('primaryUomQty');
          }
          return value;

        },
      },
      {
        name: 'primaryUomCode',
        width: 120,
      },
      {
        name: 'loadSequence',
        width: 90,
        align: ColumnAlign.left,
      },
      {
        name: 'locationRow',
        width: 120,
      },
      {
        name: 'locationColumn',
        width: 120,
      },
      {
        name: 'materialCode',
        width: 120,
      },
      {
        name: 'revisionCode',
        width: 120,
      },
      {
        name: 'lot',
        width: 120,
      },
      {
        name: 'materialLotStatusDesc',
        width: 120,
      },
      {
        name: 'qualityStatusDesc',
        width: 120,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value, record }) =>
          ['MATERIAL_LOT', 'MATERIAL'].includes(record?.get('loadObjectType')) ? (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.enable`).d('启用')
                  : intl.get(`tarzan.common.label.disable`).d('禁用')
              }
            >
              {}
            </Badge>
          ) : (
            <></>
          ),
      },
    ];
  }, []);

  return (
    <Table
      mode={TableMode.tree}
      treeLoadData={handleLoadData}
      dataSet={ds}
      onRow={nodeCover}
      columns={columns}
    />
  );
};
