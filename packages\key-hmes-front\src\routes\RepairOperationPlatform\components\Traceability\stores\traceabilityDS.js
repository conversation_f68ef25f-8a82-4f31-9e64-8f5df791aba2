/*
 * @Author: ‘兰宁辉’ ‘<EMAIL>’
 * @Date: 2023-04-20 10:09:51
 * @LastEditors: ‘兰宁辉’ ‘<EMAIL>’
 * @LastEditTime: 2023-04-23 17:24:32
 * @FilePath: \guanjiansm\packages\key-hmes-front\src\routes\RepairOperationPlatform\components\DataAcquisition\stores\DataAcquisitionDS.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @Description: 数据收集组DS
 * @Author: <EMAIL>
 * @Date: 2023-03-14 10:19:56
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.materialPreventError';
const tenantId = getCurrentOrganizationId();

// 物料防呆防错详情ds
const tableDS = () => ({
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyCode`).d('物料'),
      // required: true,
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      // required: true,
    },
    {
      name: 'substituteGroup',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.substituteGroup`).d('替代组'),
      // required: true,
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('条码'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'assembleQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleQty`).d('装配数量'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-rework-station/input/list/ui`,
        method: 'POST',
      };
    },
  },
});

const addDs = () => ({
  fields: [
    {
      name: 'identificationSerch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identificationSerch`).d('在制标识'),
    },
    {
      name: 'number',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.number`).d('数量'),
      required: true,
    },
  ],
});

export { tableDS, addDs };
