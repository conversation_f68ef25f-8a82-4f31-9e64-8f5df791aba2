import React, { useMemo, useEffect, useRef } from 'react';
import { Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Content } from 'components/Page';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import './index.less';

const modelPrompt = 'problem.table';

const PersonalTodo = (props) => {
  let page = 0;
  let totalPages = 1;
  const {
    // match: { path },
    ds,
    personalTodoQueryInfo,
    personalTodoLoading,
    history,
  } = props;

  const tableRef = useRef<any>(null);

  useEffect(() => {
    loadData(0);
  }, [])

  const loadData = (page) => {
    if (page > 0) {
      const data = ds.toData()
      personalTodoQueryInfo({
        params: {
          page,
          size: 10,
        },
        onSuccess: res => {
          if (res) {
            totalPages = res.totalPages;
            const dataList = data.concat(res.content)
            ds.loadData(dataList)
          }
        },
      })
    } else {
      personalTodoQueryInfo({
        params: {
          page,
          size: 10,
        },
        onSuccess: res => {
          if (res) {
            totalPages = res.totalPages;
            ds.loadData(res.content)
          }
        },
      })
    }
  };

  const handleScroll = (e) => {
    const { scrollTop, clientHeight, scrollHeight } = e.target;
    if (scrollTop > 0 && Math.ceil(scrollTop + clientHeight) >= scrollHeight) {
      if (page < totalPages) {
        page += 1;
        loadData(page);
      }
    }
  };

  useEffect(() => {
    if (tableRef.current) {
      tableRef.current?.element?.addEventListener('scroll', handleScroll, true);
    }
    return () => tableRef.current?.element?.removeEventListener('scroll', handleScroll, true);
  }, [])

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'problemCode',
        renderer: ({ record, value }) => (
          <a onClick={() => { history.push(`/hwms/problem-management/problem-management-platform/dist/${record?.get('problemId')}`) }}>{value}</a>
        ),
      },
      {
        name: 'problemTitle',
      },
      {
        name: 'problemStatusDesc',
        renderer: ({ record, value }) => {
          switch (record?.get('problemStatus')) {
            case 'NEW':
              return <Badge color='blue' text={value} />
            case 'RELEASED':
              return <Badge color='orange' text={value} />
            case 'FOLLOWING':
              return <Badge color='yellow' text={value} />
            case 'PUBLISH':
              return <Badge color='gold' text={value} />
            case 'CLOSED':
              return <Badge color='green' text={value} />
            case 'FREEZE':
              return <Badge color='purple' text={value} />
            case 'CLOSING':
              return <Badge color='red' text={value} />
            case 'FREEZE_APPLY':
              return <Badge color='yellow' text={value} />
            case 'SRM_HANDLE':
              return <Badge color='cyan' text={value} />
            default:
              return <Badge color='gray' text={value} />;
          }
        },
      },
      {
        name: 'role',
      },
      {
        name: 'toDoList',
        width: 240,
      },
    ];
  }, []);

  // const handleAdd = useCallback(() => {
  //   history.push(`/page_route/create`);
  // }, []);

  return (
    <Content>
      <h2 style={{ fontWeight: '900' }}>{intl.get(`${modelPrompt}.personal_todo`).d('个人待办')}</h2>
      <Table
        dataSet={ds}
        columns={columns}
        ref={tableRef}
        virtual
        virtualCell
        style={{
          height: 'calc(100% - 70px)',
        }}
      />
      {personalTodoLoading ? (<p style={{ textAlign: "center", padding: '0', margin: '0' }}>{intl.get(`${modelPrompt}.loading`).d('加载中...')}</p>) : ''}
    </Content>
  );
}

export default PersonalTodo;
