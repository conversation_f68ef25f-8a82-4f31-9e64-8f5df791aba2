import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.mes.barcodeWorkstationBinding';
const tenantId = getCurrentOrganizationId();

const prefix = BASIC.HMES_BASIC;


export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  queryFields: [
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentLov`).d('设备编码'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      ignore: FieldIgnore.always,
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'workCellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workCellLov`).d('工位编码'),
      lovCode: 'HME.WORKCELL_SITE',
      ignore: FieldIgnore.always,
    },
    {
      name: 'workcellId',
      bind: 'workCellLov.workcellId',
    },
    {
      name: 'assemblePointLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.assemblePointLov`).d('装配点编码'),
      lovCode: 'HME.HME_ASSEMBLE_POINT',
      ignore: FieldIgnore.always,
    },
    {
      name: 'assemblePointId',
      bind: 'assemblePointLov.assemblePointId',
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
  ],
  fields: [
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工位名称'),
    },
    {
      name: 'assemblePointCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
    },
    {
      name: 'assemblePointName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assemblePointName`).d('装配点名称'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'materialLotSequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotSequence`).d('顺序'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'createdByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByRealName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByRealName`).d('最近更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最近更新时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${prefix}/v1/${tenantId}/hme-workcell-material-lots/query-by-condition`,
        method: 'GET',
      };
    },
  },
});
