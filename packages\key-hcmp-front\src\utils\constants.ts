import { featchMethodPost, featchMethodGet } from 'hcmp-front-common/lib/utils/myAxios';

export const BASE_SERVER = process.env.BASE_SERVER || '/key-focus-aps';

export const API_HOST = process.env.API_HOST || 'http://*************:30880';

export const featchDataPost = featchMethodPost(BASE_SERVER);

export const featchDataGet = featchMethodGet(BASE_SERVER);

export const BASIC = {
  TARZAN_COMMON: `${process.env.TARZAN_COMMON}`,
  TARZAN_METHOD: `${process.env.TARZAN_METHOD}`,
  TARZAN_METHODTZND: `${process.env.TARZAN_METHODTZND}`,
  APS_METHODTZND: `${process.env.APS_METHODTZND}`,
  APS_COMMON: `${process.env.APS_COMMON}`,
};
