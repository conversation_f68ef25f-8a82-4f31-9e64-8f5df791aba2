import React, { useMemo, useRef } from 'react';
import {
  DataSet,
  Table,
  Button,
  Modal,
  Form,
  TextArea,
  Output,
} from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { tableDS, detailFormDS } from './stores';

const modelPrompt = 'tarzan.hmes.additionalInformationRecording';

const AdditionalInformationRecording = () => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  const detailFormDs = useMemo(() => new DataSet(detailFormDS()), []); // 复制ds
  const modal = useRef(null); // 当前模态框

  const columns = [
    {
      name: 'interfaceName',
      width: 300,
    },
    // {
    //   name: 'interfaceUrl',
    //   width: 300,
    // },
    // {
    //   name: 'ip',
    //   width: 150,
    // },
    // {
    //   name: 'requestMethod',
    //   width: 100,
    // },
    {
      name: 'requestTime',
      width: 180,
    },
    // {
    //   name: 'userAgent',
    //   width: 250,
    // },
    {
      name: 'requestStatus',
      width: 150,
      renderer: ({ record }) => (
        <Badge
          status={record.get('requestStatus') === 'success' ? 'success' : 'error'}
          text={
            record.get('requestStatus') === 'success'
              ? intl.get(`tarzan.common.label.success`).d('成功')
              : intl.get(`tarzan.common.label.failure`).d('失败')
          }
        />
      ),
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      lock: 'right',
      align: 'center',
      width: 100,
      renderer: ({ record }) => {
        return (
          <>
            <Button
              onClick={() => handleDetail(record.get('inboundId'))}
              funcType='flat'>
              {intl.get('tarzan.common.button.detail').d('详情')}
            </Button>
          </>
        );
      },
    },
  ];

  const handleDetail = async (inboundId) => {
    detailFormDs.setQueryParameter('inboundId', inboundId);
    await detailFormDs.query();
    modal.current = Modal.open({
      key: `ModalKey`,
      title: intl.get(`${modelPrompt}.title.expand.modal`).d('详细信息'),
      style: { width: '80%' },
      destroyOnClose: false,
      closable: true,
      movable: true,
      okButton: false,
      children: _renderContent(),
    });
  }

  const _renderContent = () => (
    <Form dataSet={detailFormDs} columns={3} labelLayout="horizontal">
      <Output name="interfaceName" />
      {/* <Output name="interfaceUrl" /> */}
      {/* <Output name="ip" /> */}
      {/* <Output name="requestMethod"  /> */}
      <Output name="requestTime" />
      <Output name="responseTime"  />
      {/* <Output name="userAgent"  /> */}
      <Output
        name="requestStatus"
        renderer={({ record }) => (
          <Badge
            status={record.get('requestStatus') === 'success' ? 'success' : 'error'}
            text={
              record.get('requestStatus') === 'success'
                ? intl.get(`tarzan.common.label.success`).d('成功')
                : intl.get(`tarzan.common.label.failure`).d('失败')
            }
          />
        )} />
      {/* <TextArea name="requestHeaderParameter" newLine disabled colSpan={3}/> */}
      <TextArea name="requestBodyParameter" newLine disabled colSpan={3} />
      <TextArea name="responseContent" newLine disabled colSpan={3} />
      <TextArea name="stacktrace" newLine disabled colSpan={3} />
    </Form>
  );

  return (
    <div className='hmes-style'>
      <Header
        title={intl.get(`${modelPrompt}.title`).d('进出站信息补录')}
      >
      </Header>
      <Content>
        <Table
          searchCode="AdditionalInformationRecording"
          customizedCode="AdditionalInformationRecording"
          queryBar="filterBar"
          queryFieldsLimit={10}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          style={{height: 400}}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.AdditionalInformationRecording', 'tarzan.common'],
})(AdditionalInformationRecording);
