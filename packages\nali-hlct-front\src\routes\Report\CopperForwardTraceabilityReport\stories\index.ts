/**
 * @Description: 正向追溯报表
 */

import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hmes.CopperForwardTraceabilityReport';
const tenantId = getCurrentOrganizationId();

const copperForwardTraceabilityReportTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  forceValidate: true,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('基材卷号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
  ],
  fields: [
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'rowIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.rowIdentification`).d('物料批条码'),
    },
    {
      name: 'ckIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ckIdentification`).d('磁控卷号'),
    },
    {
      name: 'fjIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fjIdentification`).d('镀铜卷号'),
    },
    {
      name: 'fqIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fqIdentification`).d('分切卷号'),
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('分切送检单号'),
    },
    {
      name: 'sideFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sideFlag`).d('分切卷外侧A/B面'),
    },
    {
      name: 'tailFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tailFlag`).d('分切尾件对应电镀首尾'),
    },
    {
      name: 'fqPlating',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fqPlating`).d('分切对应电镀位置'),
    },
    {
      name: 'correlationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNumber`).d('订单号'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotNumber`).d('批次号'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-trace-report/list/ui`,
        method: 'GET',
      };
    },
  },
});


export {
  copperForwardTraceabilityReportTableDS,
};
