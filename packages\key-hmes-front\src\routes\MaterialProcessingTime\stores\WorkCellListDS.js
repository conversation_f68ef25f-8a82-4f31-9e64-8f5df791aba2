/**
 * @Description: BOM号基础数据维护 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2023-02-24 16:52:24
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.templete1';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-material-processing-times`,
        method: 'get',
        data: {
          ...data,
          rows: data.rows?.map(item => {
            return {
              ...item,
              uuid: item.id,
            };
          }),
        },
      };
    },
  },
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'uuid',
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'bomCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
      lovCode: 'HME.BOM_CODE',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'bomCode',
      bind: "bomCodeLov.bomCode",
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.siteName`).d('工厂描述'),
      bind: 'siteLov.siteName',
    },
    {
      name: 'bomCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
      lovCode: 'HME.BOM_CODE',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'bomCode',
      bind: "bomCodeLov.bomCode",
    },
    {
      name: 'attribute14',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedCode`).d('预留码'),
    },
    {
      name: 'attribute15',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.templateNo`).d('模板号'),
    },
    {
      name: 'attribute12',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.templateNo`).d('工艺识别码'),
    },
    {
      name: 'attribute13',
      type: FieldType.number,
      min: 0,
      label: intl.get(`${modelPrompt}.templateNo`).d('型号代码位数'),
    },
    {
      name: 'minHour',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.minHour`).d('高温静置时长下限'),
      min: 0,
      max: 'maxHour',
      validator: (value, name, record) => {
        if (record?.get('minHour') && record?.get('maxHour')) {
          if (record?.get('minHour') !== record?.get('maxHour')) {
            return true;
          }
          return intl.get(`${modelPrompt}.validation.durationUpperAndLowerLimits`).d('时长上下限，不可相等!');
        }
        return true
      },
      dynamicProps: {
        required: ({ record }) => !!record.get('maxHour'),
      },
    },
    {
      name: 'maxHour',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxHour`).d('高温静置时长上限'),
      min: 'minHour',
      pattern: /^(?!0$)/g,
      validator: (value, name, record) => {
        if (record?.get('minHour') && record?.get('maxHour')) {
          if (record?.get('minHour') !== record?.get('maxHour')) {
            return true;
          }
          return intl.get(`${modelPrompt}.validation.durationUpperAndLowerLimits`).d('时长上下限，不可相等!');
        }
        return true
      },
      dynamicProps: {
        required: ({ record }) => !!record.get('minHour'),
      },
    },
    {
      name: 'attribute2',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attribute2`).d('常温静置时长下限'),
      min: 0,
      max: 'attribute1',
      validator: (value, name, record) => {
        if (record?.get('attribute2') && record?.get('attribute1')) {
          if (record?.get('attribute2') !== record?.get('attribute1')) {
            return true;
          }
          return intl.get(`${modelPrompt}.validation.durationUpperAndLowerLimits`).d('时长上下限，不可相等!');
        }
        return true
      },
      dynamicProps: {
        required: ({ record }) => !!record.get('attribute1'),
      },
    },
    {
      name: 'attribute1',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attribute1`).d('常温静置时长上限'),
      min: 'attribute2',
      pattern: /^(?!0$)/g,
      validator: (value, name, record) => {
        if (record?.get('attribute2') && record?.get('attribute1')) {
          if (record?.get('attribute2') !== record?.get('attribute1')) {
            return true;
          }
          return intl.get(`${modelPrompt}.validation.durationUpperAndLowerLimits`).d('时长上下限，不可相等!');
        }
        return true
      },
      dynamicProps: {
        required: ({ record }) => !!record.get('attribute2'),
      },
    },
    {
      name: 'attribute4',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attribute4`).d('高温老化时长下限'),
      min: 0,
      max: 'attribute3',
      validator: (value, name, record) => {
        if (record?.get('attribute4') && record?.get('attribute3')) {
          if (record?.get('attribute4') !== record?.get('attribute3')) {
            return true;
          }
          return intl.get(`${modelPrompt}.validation.durationUpperAndLowerLimits`).d('时长上下限，不可相等!');
        }
        return true
      },
      dynamicProps: {
        required: ({ record }) => !!record.get('attribute3'),
      },
    },
    {
      name: 'attribute3',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attribute3`).d('高温老化时长上限'),
      min: 'attribute4',
      pattern: /^(?!0$)/g,
      validator: (value, name, record) => {
        if (record?.get('attribute4') && record?.get('attribute3')) {
          if (record?.get('attribute4') !== record?.get('attribute3')) {
            return true;
          }
          return intl.get(`${modelPrompt}.validation.durationUpperAndLowerLimits`).d('时长上下限，不可相等!');
        }
        return true
      },
      dynamicProps: {
        required: ({ record }) => !!record.get('attribute4'),
      },
    },
    {
      name: 'attribute6',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attribute6`).d('常温老化时长下限'),
      min: 0,
      max: 'attribute5',
      validator: (value, name, record) => {
        if (record?.get('attribute6') && record?.get('attribute5')) {
          if (record?.get('attribute6') !== record?.get('attribute5')) {
            return true;
          }
          return intl.get(`${modelPrompt}.validation.durationUpperAndLowerLimits`).d('时长上下限，不可相等!');
        }
        return true
      },
      dynamicProps: {
        required: ({ record }) => !!record.get('attribute5'),
      },
    },
    {
      name: 'attribute5',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attribute5`).d('常温老化时长上限'),
      min: 'attribute6',
      pattern: /^(?!0$)/g,
      validator: (value, name, record) => {
        if (record?.get('attribute6') && record?.get('attribute5')) {
          if (record?.get('attribute6') !== record?.get('attribute5')) {
            return true;
          }
          return intl.get(`${modelPrompt}.validation.durationUpperAndLowerLimits`).d('时长上下限，不可相等!');
        }
        return true
      },
      dynamicProps: {
        required: ({ record }) => !!record.get('attribute6'),
      },
    },
    {
      name: 'attribute7',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attribute7`).d('弹夹最大装载个数'),
      min: 0,
      pattern: /^(?!0$)[1-9][0-9]*$/g,
    },
    {
      name: 'attribute11',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attribute11`).d('万安时'),
      min: 0,
      pattern: /^(?!0$)[1-9][0-9]*$/g,
    },
    {
      name: 'attribute8',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute8`).d('是否分容'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'changeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.changeFlag`).d('是否变更'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'createdUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdUsername`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedUsername`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
});

export { tableDS };
