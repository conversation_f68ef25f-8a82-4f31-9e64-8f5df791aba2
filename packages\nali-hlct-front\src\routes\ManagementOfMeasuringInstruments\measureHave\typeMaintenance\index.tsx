/**
 * @Description: 量具型号维护
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21 09:29:43
 * @LastEditTime: 2023-06-21 16:56:17
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useState } from 'react';
import { Button, DataSet, Form, Modal, Table, TextField } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { openTab } from 'utils/menuTab';
import { getCurrentOrganizationId } from 'utils/utils';
import { Button as PermissionButton } from 'components/Permission';
import queryString from 'query-string';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import ExcelExport from 'components/ExcelExport';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import {
  ColumnAlign,
  ColumnLock,
  DragColumnAlign,
  TableQueryBarType,
} from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { drawerPropsC7n } from '@components/tarzan-ui';
import notification from 'utils/notification';
import request from 'utils/request';
import { observer } from 'mobx-react';
import { itemTypeDS, tableDS } from './stories/tableDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.qms.toolModelMaintain';

const typeMaintenance = props => {
  const { tableDs } = props;
  const [toolModelId, setToolModelId] = useState([]);
  const itemTypeDs = new DataSet(itemTypeDS());
  const handleAddLine = () => {
    let _maxSeq = 0;
    itemTypeDs?.forEach(_record => {
      if (_record?.get('sequenceNumber') > _maxSeq) {
        _maxSeq = _record?.get('sequenceNumber');
      }
    })

    itemTypeDs.create({
      sequenceNumber: _maxSeq + 10,
    });
  };

  const deleteRecord = record => {
    record?.set('deleteFlag', 'Y');
  };

  const columns: ColumnProps[] = [
    {
      name: 'siteObj',
      align: ColumnAlign.center,
      width: 120,
      editor: record => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'siteName',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'modelCode',
      align: ColumnAlign.center,
      width: 120,
      editor: record => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'modelName',
      align: ColumnAlign.center,
      width: 120,
      editor: record => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'speciesObj',
      align: ColumnAlign.center,
      width: 120,
      editor: record => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'categoryName',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'modelRange',
      align: ColumnAlign.center,
      width: 120,
      editor: record => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'resolution',
      align: ColumnAlign.center,
      width: 120,
      editor: record => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'enableFlag',
      align: ColumnAlign.center,
      width: 120,
      editor: record => record.status === 'add' || record?.getState('editing'),
    },
    {
      name: 'graphUuid',
      align: ColumnAlign.center,
      width: 120,
      editor: record => record.status === 'add' || record?.getState('editing'),
    },
    {
      title: intl.get(`${modelPrompt}.operation`).d('操作'),
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ record, dataSet }) =>
        record?.status === 'add' || record?.getState('editing') ? (
          <>
            <a
              onClick={() => {
                if (record.status === 'add') {
                  dataSet?.remove(record);
                } else {
                  record.reset();
                  record.setState('editing', false);
                }
              }}
            >
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </a>
            <a
              style={{ marginLeft: '15px' }}
              onClick={() => {
                dataSet?.submit().then(res => {
                  if (res) {
                    dataSet.query();
                    record.setState('editing', false);
                  }
                });
              }}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </a>
          </>
        ) : (
          <>
            <a
              style={{
                marginRight:'15px',
              }}
              onClick={() => {
                record?.setState('editing', true);
                // @ts-ignore
                record.set('operationType', 'YP_QIS_MS_MODEL_MODEL_EDIT');
              }}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </a>
            <a onClick={() => handleModal(record)}>
              {intl.get(`${modelPrompt}.button.inspectItem`).d('检测项目')}
            </a>
          </>
        ),
      lock: ColumnLock.right,
    },
  ];

  const handleBatchExport = () => {
    openTab({
      key: `/hmes/measure-have/comment-import/YP.QIS_MS_TOOL_MODEL`,
      title: 'hzero.common.title.import',
      search: queryString.stringify({
        action: intl.get(`tarzan.common.button.import`).d('导入'),
      }),
    });
  };

  const handleBatchExportType = () => {
    openTab({
      key: `/hmes/measure-have/comment-import/YP.QIS_MS_TOOL_INSPECT_REL_IMP`,
      title: 'hzero.common.title.import',
      search: queryString.stringify({
        action: intl.get(`${modelPrompt}.button.import`).d('量具型号与检测项目导入'),
      }),
    });
  };

  const ModalChildrenComponent = observer(({ tableDs, itemTypeDs, record }) => {
    const [canEdit, setCanEdit] = useState(false);
    const itemColumns: ColumnProps[] = useMemo(
      () => [
        {
          header: () => (
            <PermissionButton
              type="c7n-pro"
              icon="add"
              onClick={handleAddLine}
              funcType="flat"
              shape="circle"
              size="small"
              disabled={!canEdit}
            />
          ),
          align: ColumnAlign.center,
          width: 80,
          renderer: ({ record }) => (
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => deleteRecord(record)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <PermissionButton
                type="c7n-pro"
                icon="remove"
                funcType="flat"
                shape="circle"
                size="small"
                disabled={!canEdit}
              />
            </Popconfirm>
          ),
          lock: ColumnLock.left,
        },
        {
          name: 'sequenceNumber',
          width: 60,
          align: ColumnAlign.left,
        },
        {
          name: 'inspectDescription',
          align: ColumnAlign.center,
          editor: canEdit,
        },
        {
          name: 'dataType',
          align: ColumnAlign.center,
          editor: canEdit,
        },
        {
          name: 'uomCodeLov',
          align: ColumnAlign.center,
          editor: canEdit,
        },
        {
          name: 'enableFlag',
          align: ColumnAlign.center,
          editor: canEdit,
        },
      ],
      [canEdit],
    );

    const handleEdit = () => setCanEdit(pre => !pre);

    const handleCancel = () => {
      setCanEdit(false);
    };

    const handleSave = record => {
      return new Promise(async(resolve) => {
        const data = itemTypeDs.toData();
        const validate = await itemTypeDs.validate();
        if (!validate) {
          return resolve(false);
        }
        return request(
          `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-inspect-rel/inspectRelSave/${record.data.toolModelId}`,
          {
            method: 'POST',
            data,
          },
        ).then(res => {
          if (res && res.failed) {
            notification.warning({
              message: res.message,
            });
            return resolve(false);
          }
          tableDs.query();
          setCanEdit(false);
          notification.success({});
          return resolve(true);
        });
      })
    };

    return (
      <>
        <Form dataSet={tableDs} columns={2}>
          <TextField label={intl.get(`${modelPrompt}.modelCode`).d('量具型号编码')} name="modelCode" disabled></TextField>
          <TextField label={intl.get(`${modelPrompt}.modelName`).d('量具型号描述')} name="modelName" disabled></TextField>
          <TextField label={intl.get(`${modelPrompt}.speciesName`).d('种别描述')} name="speciesName" disabled></TextField>
          <TextField label={intl.get(`${modelPrompt}.categoryName`).d('类别描述')} name="categoryName" disabled></TextField>
        </Form>
        {canEdit && <Button onClick={() => handleSave(record)}>{intl.get(`${modelPrompt}.button.save`).d('保存')}</Button>}
        {canEdit && <Button onClick={handleCancel}>{intl.get(`${modelPrompt}.button.cancel`).d('取消')}</Button>}
        {!canEdit && <Button onClick={handleEdit}>{intl.get(`${modelPrompt}.button.edit`).d('编辑')}</Button>}
        <Table
          dataSet={itemTypeDs}
          columns={itemColumns}
          dragColumnAlign={DragColumnAlign.left}
          rowDraggable={canEdit}
          onDragEnd={dataSet => {
            dataSet.forEach(_record => {
              _record.set('sequenceNumber', (_record.index + 1) * 10);
            });
          }}
          filter={record => record?.get('deleteFlag') !== 'Y'}
        />
      </>
    );
  });
  const handleSave2 = record => {
    return new Promise(async (resolve) => {
      const data = itemTypeDs.toData();
      const validate = await itemTypeDs.validate();
      if (!validate) {
        return resolve(false);
      }
      return request(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-inspect-rel/inspectRelSave/${record.data.toolModelId}`,
        {
          method: 'POST',
          data,
        },
      ).then(res => {
        if (res && res.failed) {
          notification.warning({
            message: res.message,
          });
          return resolve(false);
        }
        tableDs.query();
        notification.success({});
        return resolve(true);
      });
    })
  };
  const handleModal = record => {
    itemTypeDs.setQueryParameter('toolModelId', record.data.toolModelId);
    const _toolModelId = record.data.toolModelId;
    setToolModelId(_toolModelId);
    itemTypeDs.query();
    Modal.open({
      ...drawerPropsC7n({ ds: itemTypeDs }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.relationMaintain`).d('量具型号与检测项目关系维护'),
      style: {
        width: 880,
      },
      children: (
        <ModalChildrenComponent tableDs={tableDs} itemTypeDs={itemTypeDs} record={record} />
      ),
      onOk: () => handleSave2(record),
    });
  };

  const handleAdd = () => {
    tableDs.create({ operationType: 'YP_QIS_MS_MODEL_MODEL_SAVE' }, 0);
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('量具型号维护')}>
        <ExcelExport
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-models/export`}
          method="get"
          otherButtonProps={{ type: 'primary' }}
          queryParams={tableDs.toData()}
        >
          {intl.get('tarzan.common.button.export').d('导出')}
        </ExcelExport>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `htms.common.button.export`,
              type: 'button',
              meaning: '导入',
            },
          ]}
          onClick={handleBatchExport}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `tarzan.hwms.inspectionInfoManagement.new`,
              type: 'button',
              meaning: '新建',
            },
          ]}
          onClick={handleAdd}
        >
          {intl.get(`${modelPrompt}.button.create`).d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `tarzan.hwms.inspectionInfoManagement.typeAndTestingItems`,
              type: 'button',
              meaning: '量具型号与检测项目导入',
            },
          ]}
          onClick={handleBatchExportType}
        >
          {intl.get(`${modelPrompt}.button.toolModelAndItemImport`).d('量具型号与检测项目导入')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          searchCode="typeMaintenance"
          customizedCode="typeMaintenance"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(() => {
    const tableDs = new DataSet(tableDS());
    return {
      tableDs,
    };
  })(typeMaintenance),
);
