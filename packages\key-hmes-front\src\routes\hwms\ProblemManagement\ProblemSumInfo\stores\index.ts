/**
 * @Description: 问题管理监控看板-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-11-01 09:47:52
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-11-28 11:14:25
 */
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

const modelPrompt = 'tarzan.problemManagement.problemSumInfo';
const tenantId = getCurrentOrganizationId();

const problemDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content',
  queryFields: [
    {
      name: 'problemStatusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_STATUS',
      textField: 'meaning',
      valueField: 'value',
      multiple: true,
    },
    {
      name: 'problemCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'registerPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registerPerson`).d('登记人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'registerPersonList',
      bind: 'registerPersonLov.id',
    },
    {
      name: 'registerTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.registerTimeFrom`).d('登记时间从'),
      max: 'registerTimeTo',
    },
    {
      name: 'registerTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.registerTimeTo`).d('登记时间至'),
      min: 'registerTimeFrom',
    },
    {
      name: 'leadPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.leadPersonName`).d('跟进人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'leadPersonList',
      bind: 'leadPersonLov.id',
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('主责人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'responsiblePersonList',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsibleUnitLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleCompany`).d('主责科室'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'responsibleDepartmentIdList',
      bind: 'responsibleUnitLov.unitId',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'influenceLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceLevel`).d('影响程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_INFLUENCE_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'proposePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.proposePersonName`).d('提出人'),
      ignore: FieldIgnore.always,
      textField: 'name',
      lovCode: 'YP.QIS.EMPLOYEE_POSITION',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'proposePersonList',
      bind: 'proposePersonLov.employeeId',
    },
    {
      name: 'riskLightList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.riskLight`).d('风险灯'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.RISK_LIGHT',
      textField: 'meaning',
      valueField: 'value',
      multiple: true,
    },
    {
      name: 'projectNameList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROJECT_NAME',
      multiple: true,
    },
    {
      name: 'projectPhaseList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectPhase`).d('项目阶段'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_PROJECT_PHASE',
      multiple: true,
    },
    {
      name: 'majorDivision1List',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision1`).d('主要区分1'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION1',
      multiple: true,
    },
    {
      name: 'majorDivision2List',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision2`).d('主要区分2'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION2',
      multiple: true,
    },
    {
      name: 'majorDivision3List',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision3`).d('主要区分3'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION3',
      multiple: true,
    },
  ],
  fields: [
    {
      name: 'problemCode',
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
      type: FieldType.string,
    },
    {
      name: 'problemTitle',
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
      type: FieldType.string,
    },
    {
      name: 'proposeDepartment',
      label: intl.get(`${modelPrompt}.proposeDepartment`).d('提出部门'),
      type: FieldType.string,
    },
    {
      name: 'registerTime',
      label: intl.get(`${modelPrompt}.registerTime`).d('登记时间'),
      type: FieldType.dateTime,
    },
    {
      name: 'leadPersonName',
      label: intl.get(`${modelPrompt}.leadPersonName`).d('跟进人'),
      type: FieldType.string,
    },
    {
      name: 'responsiblePersonName',
      label: intl.get(`${modelPrompt}.responsiblePersonName`).d('主责人'),
      type: FieldType.string,
    },
    {
      name: 'responsibleDepartment',
      label: intl.get(`${modelPrompt}.responsibleDepartment`).d('主责科室'),
      type: FieldType.string,
    },
    {
      name: 'problemCategory',
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      type: FieldType.string,
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'severityLevel',
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      type: FieldType.string,
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'influenceLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceLevel`).d('影响程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_INFLUENCE_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'schedule',
      label: intl.get(`${modelPrompt}.schedule`).d('进度'),
      type: FieldType.string,
    },
    {
      name: 'resultVerification',
      type: FieldType.string,
    },
    {
      name: 'causeAnalysis',
      type: FieldType.string,
    },
    {
      name: 'measureEnact',
      type: FieldType.string,
    },
    {
      name: 'verifyClosed',
      type: FieldType.string,
    },
    {
      name: 'nextReportDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.nextReportDate`).d('下次报告日期'),
    },
  ],
});

const typeDimensionDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'typeDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.typeDimension`).d('分类维度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.TYPE_DIMENSION',
      textField: 'meaning',
      valueField: 'value',
      defaultValue: 'SEVERITY_LEVEL',
    },
  ],
});

export { problemDS, typeDimensionDS };
