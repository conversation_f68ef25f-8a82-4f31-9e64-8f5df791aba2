/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-25 09:36:27
 * @LastEditTime: 2023-07-26 20:20:16
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import PDF from 'react-pdf-js';
// import worker from './pdf.worker';
import pdfjsWorker from "pdfjs-dist-2.1.266/build/pdf.worker.entry";
import { Spin } from 'choerodon-ui';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import { HZERO_FILE } from 'utils/config';

interface PreviewProps {
  suffix: string,
  url: string,
  width?: number,
  height?: number,
}

const fileExts = ['.xlsx', '.xls', '.pdf', '.doc', '.docx', '.pptx', '.ppt', '.txt'];
const imgExts = ['.jpg', '.jpeg', '.bmp', '.webp', '.png', '.gif', '.svg'];
const videoExts = ['.wmv', '.mpg', '.mpeg', '.mov', '.rm', '.swf', '.flv', '.mp4'];

const extsMap = new Map([
  ['file', fileExts],
  ['img', imgExts],
  ['video', videoExts],
])

const Preview = (props: PreviewProps) => {
  const { suffix, url, width, height } = props;

  const [fileType, setFileType] = useState<string>('');
  const [pages, setPages] = useState({ page: 1, allPages: 1 })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!suffix) {
      return;
    }
    let _fileType = '';
    extsMap.forEach((value, key) => {
      if (value.includes(suffix)) {
        _fileType = key;
      }
    })
    setFileType(_fileType);
    if (_fileType !== 'file') {
      setLoading(false)
    }
  }, [suffix])

  const getAllPages = useCallback(
    (pageNums) => {
      if (pageNums) {
        setPages((prev) => ({ ...prev, allPages: pageNums }))
        setLoading(false)
      }
    },
    [],
  )

  const _returnDiv = useMemo(() => {
    switch (fileType) {
      case 'file':
        return (
          <PDF
            file={`${HZERO_FILE}/v1/${getCurrentOrganizationId()}/file-preview/by-url?url=${url}&bucketName=key-hmes&storageCode=DEFAULT&access_token=${getAccessToken()}`}
            onDocumentComplete={getAllPages}
            page={pages.page}
            scale={1}
            workerSrc={pdfjsWorker}
            // workerSrc={worker}
            // workerSrc="http://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.1.266/pdf.worker.js"
          />
        );
      case 'img':
        return <img style={{ maxWidth: '100%' }} src={url} alt=''></img>
      case 'video':
        return <video style={{ maxWidth: '100%' }} src={url} muted autoPlay loop />
      default:
        return '';
    }
  }, [fileType, url, pages])

  const nextPage = useCallback(
    (type) => {
      const currentPage = pages.page;
      if (type === 'next' && currentPage !== pages.allPages) {
        setPages((prev) => ({ ...prev, page: currentPage + 1 }))
      }
      if (type === 'up' && currentPage !== 1) {
        setPages((prev) => ({ ...prev, page: currentPage - 1 }))
      }
    },
    [pages],
  )


  return (
    <Spin spinning={loading}>
      <div
        style={{
          width,
          height,
          overflow: 'auto',
          position: 'relative',
        }}
      >
        {
          fileType === 'file' && (
            <div
              style={{
                display: 'inline-flex',
                float: 'right',
              }}
            >
              <div style={{ cursor: 'pointer' }} onClick={() => nextPage('up')}>上一页</div>
              &nbsp;
              <span>
                {pages.page}/{pages.allPages}页
              </span>
              &nbsp;
              <div style={{ cursor: 'pointer' }} onClick={() => nextPage('next')}>下一页</div>
            </div>
          )
        }
        {_returnDiv}
      </div>
    </Spin>
  )
};

export default Preview;
