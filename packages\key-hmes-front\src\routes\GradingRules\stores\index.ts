import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content', // 列表数据在接口返回json中的相对路径
  totalKey: 'totalElements',
  primaryKey: 'gradingRuleId', // 表格唯一性主键
  queryFields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialIds',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'tagLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagCode`).d('采集项'),
      lovCode: 'HME_MT_TAG',
      lovPara: {
        tenantId,
      },
      multiple: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'tagIds',
      bind: 'tagLov.tagId',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('工厂描述'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'tagCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCode`).d('采集项编码'),
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('采集项描述'),
    },
    {
      name: 'ruleSort',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ruleSort`).d('优先级'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'createdUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdUsername`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedUsername`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const _data = data;
      if (data.tagIds.length) {
        _data.tagIds = data.tagIds.join(',');
      } else {
        delete data.tagIds;
      }
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-grading-rules`,
        method: 'GET',
        data: _data,
      };
    },
  },
});

export { tableDS };
