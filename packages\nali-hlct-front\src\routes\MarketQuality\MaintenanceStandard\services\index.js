/**
 * RelationMaintain - 维修标准管理-接口
 * @date: 2023-9-7
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';
// const endUrl = '-43335';

// 保存
export function maintainFileSaveConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-maintain-file/save/ui`,
    method: 'POST',
  };
}
// 提交
export function maintainFileSubmitConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-maintain-file/submit/ui`,
    method: 'POST',
  };
}

/**
 * 根据materialId获取物料相关信息
 * @function QueryMaterialInfo
 * @returns {object} fetch Promise
 */
export function QueryMaterialInfoConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-qa-feedbacks/mt-material-ca`,
    method: 'POST',
  };
}
