/**
 * @Description:检验方案维护-详情-总览
 * @Author: <<EMAIL>>
 * @Date: 2023-01-20 11:31:01
 * @LastEditTime: 2023-06-15 15:18:18
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState, useImperativeHandle, forwardRef, useMemo } from 'react';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { CheckBox, DataSet, Table, Modal } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import styles from '../index.modules.less';
import { DetailTableDS } from '../components/stores';
import InspectItemDrawer from '../components/InspectItemDrawer/InspectItemDrawer';

const StatisticsTable = (
  {
    inspectSchemeLines,
    selectOptionFormat,
    canEdit,
    coverInspectSchemeLines,
  },
  ref: any,
) => {
  useImperativeHandle(ref, () => ({
    updateTable,
  }));

  const [columns, setColumns] = useState<Array<any>>([]);
  const tableDs = useMemo(() => {
    return new DataSet({
      primaryKey: 'inspectItemCode',
      selection: false,
      autoCreate: false,
      paging: false,
      fields: [
        {
          name: 'inspectItemCode',
        },
        {
          name: 'inspectItemDesc',
        },
      ],
    });
  }, []);
  const modelPrompt = 'tarzan.inspectionScheme';

  useEffect(() => {
    formatColumns();
  }, [inspectSchemeLines, canEdit]);

  const formatColumns = () => {
    const inspectItemCodeMap = {};
    const columnsMap: Array<any> = [
      {
        type: FieldType.string,
        title: intl?.get(`${modelPrompt}.inspectItemCode`).d('检验项目编码'),
        name: 'inspectItemCode',
        renderer: ({ value }) => {
          return (
            <a
              onClick={() => {
                openInspectItemDrawer(value);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        type: FieldType.string,
        title: intl?.get(`${modelPrompt}.inspectItemDesc`).d('检验项目描述'),
        name: 'inspectItemDesc',
      },
    ];
    inspectSchemeLines.forEach(tabItem => {
      const { inspectionItemBasisDs, dimensions } = tabItem;

      let inspectBusinessType;
      let inspectBusinessTypeDesc;
      let editFlag;

      if (inspectionItemBasisDs) {
        inspectionItemBasisDs.forEach(basisDsRecord => {
          inspectBusinessType = basisDsRecord?.get('inspectBusinessType');
          inspectBusinessTypeDesc = basisDsRecord?.get('inspectBusinessTypeDesc');
          editFlag = basisDsRecord?.get('editFlag');
        });
      }

      if (!canEdit || (canEdit && editFlag === 'Y')) {
        columnsMap.push({
          name: inspectBusinessType,
          title: inspectBusinessTypeDesc,
          align: 'center',
          renderer: ({ record }) => {
            return (
              <CheckBox
                className={styles.checkBoxDisabled}
                disabled={!record?.get(inspectBusinessType)}
                checked={record?.get(inspectBusinessType)}
              />
            );
          },
        });
      }

      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        if (itemsDs) {
          itemsDs.forEach(basisDsRecord => {
            const dataType = basisDsRecord?.get('dataType');
            const inspectItemId = basisDsRecord?.get('inspectItemId');
            const inspectItemCode = basisDsRecord?.get('inspectItemCode');
            const inspectItemDesc = basisDsRecord?.get('inspectItemDesc');
            if (dataType !== 'CALCULATE_FORMULA' && (!canEdit || (canEdit && editFlag === 'Y'))) {
              if (inspectItemId && !inspectItemCodeMap[inspectItemId]) {
                inspectItemCodeMap[inspectItemId] = {
                  inspectItemId,
                  inspectItemCode,
                  inspectItemDesc,
                };
              }
              if (inspectItemId && inspectItemCodeMap[inspectItemId]) {
                inspectItemCodeMap[inspectItemId][inspectBusinessType] = true;
              }
            }
          });
        }
      });
    });

    const inspectItemId = Object.keys(inspectItemCodeMap);
    const inspectItemList = inspectItemId.map(id => {
      return {
        ...inspectItemCodeMap[id],
      };
    });

    setColumns(columnsMap);
    tableDs.loadData(inspectItemList);
  };

  const updateTable = () => {
    formatColumns();
  };

  const openInspectItemDrawer = inspectItemCode => {
    const inspectItemDrawerDs = new DataSet(DetailTableDS({ canSelect: canEdit }));
    const newTableData: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      // tab层数据
      const { dimensions, inspectionItemBasisDs } = item;
      // 遍历检验项目维度
      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        // 提取维度下的表格
        itemsDs.toData().forEach(recordData => {
          if (recordData.inspectItemCode === inspectItemCode) {
            newTableData.push({
              ...recordData,
              inspectBusinessTypeDesc: inspectionItemBasisDs.current?.get('inspectBusinessTypeDesc'),
              inspectionDimension: selectOptionFormat(dimensionsItem),
              canSelect: recordData.dataType !== 'CALCULATE_FORMULA',
            });
          }
        });
      });
    });

    inspectItemDrawerDs.loadData(newTableData);

    Modal.open({
      ...drawerPropsC7n({
        canEdit,
        ds: inspectItemDrawerDs,
      }),
      key: Modal.key(),
      title: intl?.get(`${modelPrompt}.inspectSchemeAllocationDetails`).d('检验项目分配明细'),
      destroyOnClose: true,
      style: {
        width: 1440,
      },
      footer: (okBtn, cancelBtn) => {
        return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
      },
      onOk: () => {
        coverInspectSchemeLines(inspectItemDrawerDs.toData());
      },
      children: (
        <InspectItemDrawer
          tableDs={inspectItemDrawerDs}
          canEdit={canEdit}
          
        />
      ),
    });
  };

  return <Table dataSet={tableDs} columns={columns} virtual virtualCell style={{ height: 800 }} />;
};
export default forwardRef(StatisticsTable);
