import React from 'react';
import { <PERSON>, But<PERSON>, Lov, Select, Switch } from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
import intl from 'utils/intl';
// import notification from 'utils/notification';

const modelPrompt = 'tarzan.hmes.materialPreventError';

export default ({
  singleAssObjectDs,
  record: propsRecord,
  // handleDrawer,
  canEdit,
}) => {
  const typeName = {
    OPERATION: intl.get(`${modelPrompt}.OPERATION`).d('工艺'),
  };

  // useEffect(() => {
  //   if (singleAssObjectDs.toData() && singleAssObjectDs.toData().length > 0) {
  //     const cacheSelected = { OPERATION: true };
  //     setSelected(cacheSelected);
  //     const nowData = singleAssObjectDs.toData().map(item => {
  //       return {
  //         ...item,
  //         objectType: 'OPERATION',
  //       };
  //     });
  //     singleAssObjectDs.loadData(nowData);
  //   } else {
  //     setSelected([]);
  //     singleAssObjectDs.loadData([]);
  //   }
  // }, []);

  // const selectObjTypeDiv = type => {
  //   if (!canEdit) {
  //     return;
  //   }
  //   if (selected[type]) {
  //     // 已选过的类型，再次点击为删除行
  //     const newList = singleAssObjectDs.filter(record => record.get('objectType') !== type);
  //     const newSelected = {
  //       ...selectedProxy.selected,
  //       [type]: false,
  //     };
  //     singleAssObjectDs.loadData(newList);
  //     setSelected(newSelected);
  //   } else {
  //     // 新增行
  //     const newRow = { uuid: uuid(), objectType: type };
  //     singleAssObjectDs.create(newRow);
  //     setSelected({
  //       ...selectedProxy.selected,
  //       [type]: true,
  //     });
  //   }
  // };

  // const changeObject = async (lovRecords, record) => {
  //   if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
  //     record.getField('objectRevision').set('required', true);
  //     record.set('revisionFlag', 'Y');
  //     record.set('operationRevision', lovRecords.revisionCode);
  //   } else {
  //     record.getField('objectRevision').set('required', false);
  //     record.set('objectRevision', '');
  //     record.set('revisionFlag', 'N');
  //   }
  // };
  const handleCreateDetail = () => {
    singleAssObjectDs.create({
      processCtrlConfigId: propsRecord.toData()?.processCtrlConfigId,
      processCtrlConfigLId: propsRecord.toData()?.processCtrlConfigLId,
    });
  }

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!canEdit}
          onClick={() => handleCreateDetail()}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      renderer: ({ record }) => {
        // const objType = record.get('objectType');
        return (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              // selectObjTypeDiv(objType);
              singleAssObjectDs.remove(record)
            }}
          >
            <Button disabled={!canEdit} funcType="flat" icon="remove" shape="circle" size="small" />
          </Popconfirm>
        );
      },
      lock: 'left',
    },
    {
      name: 'objectType',
      width: 120,
      editor: () => canEdit &&<Select />,
      renderer: ({ record }) => typeName[record.get('objectType')],
    },
    {
      name: 'objectLov',
      width: 180,
      renderer: ({ record }) => record.get('operationName'),
      editor: () => canEdit && <Lov />,
    },
    {
      name: 'operationRevision',
      width: 120,
      renderer: ({ record }) => record.get('operationRevision'),
    },
    {
      name: 'operationDesc',
    },
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      editor: canEdit && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  return (
    <Table dataSet={singleAssObjectDs} columns={columns} />
  );
};
