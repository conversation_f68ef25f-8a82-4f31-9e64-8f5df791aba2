/**
 * @Description: 分层审核任务-主界面
 * @Author: <EMAIL>
 * @Date: 2023/8/18 9:56
 */
import React, { FC, useCallback, useMemo } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { Badge, Collapse, Tag } from 'choerodon-ui';
import { Button, DataSet, Table } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { headDS, lineDS } from '../stores';

const modelPrompt = 'tarzan.layerReview.layerReviewTask';
const { Panel } = Collapse;

interface layerReviewTaskProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
}

const layerReviewTaskList: FC<layerReviewTaskProps> = props => {
  const { headDs, lineDs, history } = props;

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('layerReviewTaskId'));
    } else {
      queryLineTable(null);
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = layerReviewTaskId => {
    if (layerReviewTaskId) {
      lineDs.setQueryParameter('layerReviewTaskId', layerReviewTaskId);
    } else {
      lineDs.setQueryParameter('layerReviewTaskId', 0);
    }
    lineDs.query();
  };

  const renderStatusTag = (value, record, name) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'EXECUTING':
        className = 'orange';
        break;
      case 'TIMEOUT_CLOSED':
        className = 'red';
        break;
      case 'CLOSED':
      case 'NORMAL_CLOSED':
        className = 'green';
        break;
      case 'NEW':
        className = 'blue';
        break;
      default:
        className = 'gray';
    }
    return <Tag color={className}>{record!.getField(name)!.getText()}</Tag>;
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'layerReviewTaskCode',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/layer-review/layer-review-task/dist/${record!.get('layerReviewTaskId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'layerReviewTaskStatus',
        renderer: ({ value, record }) => renderStatusTag(value, record, 'layerReviewTaskStatus'),
      },
      { name: 'layerReviewLevel' },
      { name: 'siteName' },
      { name: 'productLineName' },
      { name: 'operationName' },
      {
        name: 'requestFinishTime',
        width: 150,
        align: ColumnAlign.center,
      },
    ];
  }, []);

  const lineColumns: any = useMemo(
    () => [
      { name: 'layerRevtskDtlCode', width: 180, lock: ColumnLock.left },
      { name: 'equimentCode' },
      { name: 'equimentName' },
      {
        name: 'layerRevtskDtlStatus',
        renderer: ({ value, record }) => renderStatusTag(value, record, 'layerRevtskDtlStatus'),
      },
      {
        name: 'closeTime',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'abnormalFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
      },
      { name: 'abnormalCloseReason' },
    ],
    [],
  );

  const handleAdd = useCallback(() => {
    history.push(`/hwms/layer-review/layer-review-task/dist/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('分层审核任务')}>
        <Button color={ButtonColor.primary} icon="add" onClick={handleAdd}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headDs}
          columns={headColumns}
          searchCode="layerReviewTask1"
          customizedCode="layerReviewTask-listHeader"
          onRow={({ record }) => ({
            onClick: () => queryLineTable(record?.get('layerReviewTaskId')),
          })}
        />
        <Collapse bordered={false} defaultActiveKey={['line']}>
          <Panel key="line" header={intl.get(`${modelPrompt}.title.line`).d('分层审核任务明细')}>
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="layerReviewTask-listLine"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(layerReviewTaskList as any),
);
