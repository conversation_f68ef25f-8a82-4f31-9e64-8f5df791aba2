/**
 * @Description: 委托实验平台-表格DS
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'key.hwms.front.CommissionedExperimentalPlatform';

const tableDS = () => ({
  autoQuery: true,
  selection: 'multiple',
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'entrustApplyCode',
      label: intl.get(`${modelPrompt}.entrustApplyCode`).d('申请单编码'),
      type: FieldType.string,
    },
    {
      name: 'entrustApplyStatusList',
      label: intl.get(`${modelPrompt}.entrustApplyStatus`).d('申请单状态'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.ENTRUST_APPLY_STATUS',
      multiple: true,
      defaultValue: ['NEW', 'RELEASED', 'INSPECTING', 'COMPLETED'],
    },
    {
      name: 'siteObj',
      label: intl.get(`${modelPrompt}.siteObj`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      type: FieldType.object,
      textField: 'siteName',
      valueField: 'siteId',
      lovPara: { tenantId },
    },
    {
      name: 'siteId',
      bind: 'siteObj.siteId',
      type: FieldType.string,
    },
    {
      name: 'oaApplyStatus',
      label: intl.get(`${modelPrompt}.oaApplyStatus`).d('OA审批状态'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.ENTRUST_OA_APPLY_STATUS',
    },
    {
      name: 'unitObj',
      label: intl.get(`${modelPrompt}.unitObj`).d('委托专业'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: 'always',
      type: FieldType.object,
      textField: 'unitName',
      valueField: 'unitId',
      lovPara: { tenantId },
    },
    {
      name: 'entrustUnitId',
      bind: 'unitObj.unitId',
      type: FieldType.string,
    },
    {
      name: 'userObj',
      label: intl.get(`${modelPrompt}.userObj`).d('委托人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: 'always',
      type: FieldType.object,
      textField: 'realName',
      valueField: 'userId',
      lovPara: { tenantId },
    },
    {
      name: 'entrustUserId',
      bind: 'userObj.userId',
      type: FieldType.string,
    },
    {
      name: 'inspectApproveStatus',
      label: intl.get(`${modelPrompt}.inspectApproveStatus`).d('检验结果审核状态'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.ENTRUST_INSPECT_STATUS',
    },
    {
      name: 'inspectApproveUserObj',
      label: intl.get(`${modelPrompt}.inspectApproveUserObj`).d('审核人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: 'always',
      type: FieldType.object,
      textField: 'realName',
      valueField: 'userId',
      lovPara: { tenantId },
    },
    {
      name: 'inspectApproveUserId',
      bind: 'inspectApproveUserObj.userId',
      type: FieldType.string,
    },
    {
      name: 'materialObj',
      label: intl.get(`${modelPrompt}.materialObj`).d('物料名称'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      ignore: 'always',
      type: FieldType.object,
      textField: 'materialName',
      valueField: 'materialId',
      lovPara: { tenantId },
    },
    {
      name: 'materialId',
      bind: 'materialObj.materialId',
      type: FieldType.string,
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'entrustTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.entrustTimeFrom`).d('委托时间从'),
      max: 'entrustTimeTo',
    },
    {
      name: 'entrustTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.entrustTimeTo`).d('委托时间至'),
      min: 'entrustTimeFrom',
    },
  ],
  fields: [
    {
      name: 'entrustApplyCode',
      label: intl.get(`${modelPrompt}.entrustApplyCode`).d('委托申请单编码'),
      type: FieldType.string,
    },
    {
      name: 'siteCode',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      type: FieldType.string,
    },
    {
      name: 'entrustApplyStatusDesc',
      label: intl.get(`${modelPrompt}.entrustApplyStatusDesc`).d('申请单状态'),
      type: FieldType.string,
    },

    {
      name: 'oaApplyStatusDesc',
      label: intl.get(`${modelPrompt}.oaApplyStatusDesc`).d('OA审批状态'),
      type: FieldType.string,
    },
    {
      name: 'oaApplyDesc',
      label: intl.get(`${modelPrompt}.oaApplyDesc`).d('OA审批意见'),
      type: FieldType.string,
    },
    {
      name: 'entrustUnitName',
      label: intl.get(`${modelPrompt}.entrustUnitName`).d('委托专业'),
      type: FieldType.string,
    },
    {
      name: 'entrustUserName',
      label: intl.get(`${modelPrompt}.entrustUserName`).d('委托人'),
      type: FieldType.string,
    },
    {
      name: 'entrustTime',
      label: intl.get(`${modelPrompt}.entrustTime`).d('委托时间'),
      type: FieldType.string,
    },
    {
      name: 'sampleQty',
      label: intl.get(`${modelPrompt}.sampleQty`).d('样本数量'),
      type: FieldType.string,
    },
    {
      name: 'sampleUom',
      label: intl.get(`${modelPrompt}.sampleUom`).d('样本单位'),
      type: FieldType.string,
    },
    {
      name: 'expectCompleteTime',
      label: intl.get(`${modelPrompt}.expectCompleteTime`).d('期望完成日期'),
      type: FieldType.string,
    },
    {
      name: 'sampleDisposalMethodDesc',
      label: intl.get(`${modelPrompt}.sampleDisposalMethodDesc`).d('测试后样本处理'),
      type: FieldType.string,
    },
    {
      name: 'inspectApproveStatusDesc',
      label: intl.get(`${modelPrompt}.inspectApproveStatusDesc`).d('检验结果审核状态'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      type: FieldType.string,
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      type: FieldType.string,
    },
    {
      name: 'lastUpdateDate',
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      type: FieldType.string,
    },
    {
      name: 'lastUpdatedByName',
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
      type: FieldType.string,
    },
    {
      name: 'reason',
      label: intl.get(`${modelPrompt}.reason`).d('实验原因'),
      type: FieldType.string,
    },
    {
      name: 'remark',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      type: FieldType.string,
    },
    // {
    //   name: 'a',
    //   label: intl.get(`${modelPrompt}.a`).d('相关文件'),
    //   type: FieldType.string,
    // },
    {
      name: 'relatedFileUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.relatedFileUuid`).d('相关文件'),
      // bucketDirectory: 'inspection-platform',
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-apply/ui/list`,
      method: 'GET',
    },
  },
});
const tableLineDS = () => ({
  autoQuery: false,
  selection: 'multiple',
  dataKey: 'content',
  totalKey: 'totalElements',
  fields: [
    {
      name: 'taskNum',
      label: intl.get(`${modelPrompt}.taskNum`).d('检验任务编码'),
      type: FieldType.string,
    },
    {
      name: 'taskStatusDesc',
      label: intl.get(`${modelPrompt}.taskStatusDesc`).d('检验状态'),
      type: FieldType.string,
    },
    {
      name: 'applyStatusDesc',
      label: intl.get(`${modelPrompt}.applyStatusDesc`).d('检验结果审核状态'),
      type: FieldType.string,
    },
    {
      name: 'applyUserName',
      label: intl.get(`${modelPrompt}.applyUserName`).d('审核人'),
      type: FieldType.string,
    },
    {
      name: 'applyTime',
      label: intl.get(`${modelPrompt}.applyTime`).d('审核时间'),
      type: FieldType.string,
    },
    {
      name: 'inspectUserName',
      label: intl.get(`${modelPrompt}.inspectUserName`).d('检验员'),
      type: FieldType.string,
    },
    {
      name: 'receiveSampleTime',
      label: intl.get(`${modelPrompt}.receiveSampleTime`).d('收样时间'),
      type: FieldType.string,
    },
    {
      name: 'startTime',
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
      type: FieldType.string,
    },
    {
      name: 'endTime',
      label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
      type: FieldType.string,
    },
    {
      name: 'applyOpinion',
      label: intl.get(`${modelPrompt}.applyOpinion`).d('审核意见'),
      type: FieldType.string,
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      type: FieldType.string,
    },
    {
      name: 'lastUpdatedByName',
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
      type: FieldType.string,
    },
    {
      name: 'lastUpdateDate',
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-task/ui/list`,
      method: 'GET',
    },
  },
});
export { tableDS, tableLineDS }
