import React, { useRef, useCallback, useState, useMemo, useEffect } from 'react';
import DashboardCard from '../DashboardCard.jsx';
import request from 'utils/request';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { debounce } from 'lodash';
import * as echarts from 'echarts';

const tenantId = getCurrentOrganizationId();
const url = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-repair/monthFinish`;

const MonthlyMaintenanceCompletionTrendChart = ({ isFullScreen, timers, assetLocationId }) => {
    const chartRef = useRef(null);
    const [data, setData] = useState<any>(null);

    const fetchData = useCallback(async () => {

        const query = assetLocationId ? { assetLocationId } : {};

        const res = await request(url, {
            method: 'GET',
            query,
        });

        const formattedData = res.map(item => ({
            month: item.month,  // 获取月份
            total: parseInt(item.total, 10),  // 获取total并转换为整数
        }));

        setData(formattedData);

    }, [assetLocationId]);


    const option: any = useMemo(() => {
        const months = data?.map(item => item.month);  // 提取月份
        const totals = data?.map(item => item.total);  // 提取对应的total

        return {
            title: {
                top: '3%',
                text: '月度保养单日完成数趋势图',
                left: 'center',
                textStyle: {
                    fontWeight: 'bold',
                    color: '#00fff4',
                },
            },
            tooltip: {
                trigger: 'axis',
            },
            legend: {
                bottom: 'bottom',
                textStyle: {
                    color: '#fff',
                },
            },
            grid: {
                bottom: '7%',
                containLabel: true,
            },
            xAxis: {
                type: 'category',
                axisLine: { // 控制轴线样式
                    lineStyle: {
                        color: '#fff', // 设置轴线颜色
                    },
                },
                axisLabel: { // 控制轴标签样式
                    interval: 0, // 显示所有标签
                    rotate: 45, // 旋转30度
                    textStyle: {
                        color: '#fff', // 设置轴标签文字颜色
                    },
                },
                data: months,  // 使用月份数据填充x轴
            },
            yAxis: {
                axisTick: { show: true },// 坐标刻度是否显示
                splitLine: { show: false }, // 是否显示背景分隔线
                axisLine: { show: true }, // 控制轴线
                axisLabel: { // 控制轴标签样式
                    textStyle: {
                        color: '#fff', // 设置轴标签文字颜色
                    },
                },
            },
            series: [
                {
                    name: '完成数',
                    type: 'line',
                    stack: 'Total',
                    data: totals,  // 使用total数据填充y轴
                },
            ],
        };
    }, [data]);

    useEffect(() => {
        if (!chartRef.current) return;
        // 初始化echarts实例
        const myChart = echarts.init(chartRef.current);
        myChart.setOption(option);

        const handleResize = debounce(() => {
            myChart.resize();
        }, 200);

        const observer = new ResizeObserver(() => {
            handleResize();
        });
        observer.observe(chartRef.current);

        return () => {
            observer.disconnect();
        };
    }, [option]);

    // 初始化数据查询
    useEffect(() => {
        fetchData();
    }, []);

    // 定时更新查询
    useEffect(() => {
        let time;
        if (timers) {
            time = setInterval(() => {
                fetchData();
            }, (timers) * 60000)
        } else if (assetLocationId) {
            fetchData();
        }
        return () => {
            clearInterval(time)
        }
    }, [timers, assetLocationId]);


    return (
        <DashboardCard style={{ height: '100%' }}>
            {isFullScreen ?
                (<div style={{ width: '100%', height: '100%' }} >
                    <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
                </div>) :
                <div style={{ width: '100%', height: '100%' }} >
                    <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
                </div>
            }
        </DashboardCard>
    );
};

export default MonthlyMaintenanceCompletionTrendChart;