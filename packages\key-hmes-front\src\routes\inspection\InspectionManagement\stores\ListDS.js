import intl from 'utils/intl';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import {DataSet} from "choerodon-ui/pro";

const modelPrompt = 'tarzan.hmes.inspection.inspection-management';
const tenantId = getCurrentOrganizationId();

const statusOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui`,
        method: 'GET',
        params: { statusGroup: 'INSPECT_REQUEST_STATUS', tenantId },
      };
    },
  },
});

const searchFormDS = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectBusinessTypeRuleId',
  queryFields: [
    {
      name: 'inspectRequestCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestCode`).d('报检请求编码'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.MODEL.SITE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'inspectRequestStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestStatus`).d('状态'),
      options: statusOptionDs,
      textField: 'description',
      valueField: 'statusCode',
    },
    {
      name: 'businessObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      lovCode: 'APEX_WMS.COMMON.BUSINESS_TYPE',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
    },
    {
      name: 'businessType',
      bind: 'businessObj.typeCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.QMS.INSPECT_BUS_TYPE_RULE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'inspectBusinessType',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      ignore: FieldIgnore.always,
      multiple: true,
      lovCode: 'APEX_WMS.METHOD.MATERIAL.PERMISSION',
      textField:'materialName',
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },

    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'sourceObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源单据类型'),
      lookupCode: 'APEX_WMS.SOURCE_OBJECT_TYPE',
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectCode`).d('来源单据号'),
    },
    {
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('报检时间从'),
      name: 'inspectReqCreationDateFrom',
      type: FieldType.date,
      max: 'inspectReqCreationDateEnd',
    },
    {
      label: intl.get(`${modelPrompt}.creationDateTo`).d('报检时间至'),
      name: 'inspectReqCreationDateEnd',
      type: FieldType.date,
      min: 'inspectReqCreationDateFrom',
    },
    {
      name: 'inspectInfoUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.applicant`).d('报检人'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'inspectReqUserId',
      bind: 'inspectInfoUserLov.id',
    },
    {
      name: 'materialLot',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLot`).d('物料批'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.MATERIAL_LOT',
      textField: 'materialLotCode',
      multiple: true,
      lovPara: { tenantId },
    },
    {
      name: 'materialLotCodes',
      bind: 'materialLot.materialLotCode',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inspect-requests/list/ui`,
        method: 'GET',
      };
    },
  },
});

const headerTableDS = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectBusinessTypeRuleId',
  queryFields: [
    {
      name: 'inspectRequestCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestCode`).d('报检请求编码'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.MODEL.SITE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'inspectRequestStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestStatus`).d('状态'),
      options: statusOptionDs,
      textField: 'description',
      valueField: 'statusCode',
    },
    {
      name: 'businessObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      lovCode: 'APEX_WMS.COMMON.BUSINESS_TYPE',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
    },
    {
      name: 'businessType',
      bind: 'businessObj.typeCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.QMS.INSPECT_BUS_TYPE_RULE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'inspectBusinessType',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      ignore: FieldIgnore.always,
      multiple: true,
      lovCode: 'APEX_WMS.METHOD.MATERIAL.PERMISSION',
      textField:'materialName',
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },

    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'sourceObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源单据类型'),
      lookupCode: 'APEX_WMS.SOURCE_OBJECT_TYPE',
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectCode`).d('来源单据号'),
    },
    {
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('报检时间从'),
      name: 'inspectReqCreationDateFrom',
      type: FieldType.date,
      max: 'inspectReqCreationDateEnd',
    },
    {
      label: intl.get(`${modelPrompt}.creationDateTo`).d('报检时间至'),
      name: 'inspectReqCreationDateEnd',
      type: FieldType.date,
      min: 'inspectReqCreationDateFrom',
    },
    {
      name: 'inspectInfoUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.applicant`).d('报检人'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'inspectReqUserId',
      bind: 'inspectInfoUserLov.id',
    },
    {
      name: 'materialLot',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLot`).d('物料批'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.MATERIAL_LOT',
      textField: 'materialLotCode',
      multiple: true,
      lovPara: { tenantId },
    },
    {
      name: 'materialLotCodes',
      bind: 'materialLot.materialLotCode',
    },
  ],
  fields: [
    {
      name: 'inspectRequestCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestCode`).d('报检请求编码'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'businessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
    },
    {
      name: 'inspectRequestStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestStatus`).d('状态'),
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
      lookupCode: 'APEX_WMS.FLAG_YN',
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('报检数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'sourceObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源单据类型'),
    },
    {
      name: 'sourceNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceNumber`).d('来源单据编码#行号'),
    },
    {
      name: 'sourceObjectLineCode',
      type: FieldType.string,
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单号'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('场内批次'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'inspectReqUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqUserName`).d('报检人'),
    },
    {
      name: 'inspectReqCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCreationDate`).d('报检时间'),
    },
    {
      name: 'okQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.okQty`).d('合格数'),
    },
    {
      name: 'ngQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngQty`).d('不合格数'),
    },
    {
      name: 'scrapQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapQty`).d('报废数'),
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检验人'),
    },
    {
      name: 'inspectReqCompleteUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCompleteUserName`).d('处置完成人'),
    },
    {
      name: 'inspectReqCompleteDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCompleteDate`).d('处置完成时间'),
    },
    {
      name: 'inspectReqCancelUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCancelUserName`).d('报检取消人'),
    },
    {
      name: 'inspectReqCancelDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCancelDate`).d('报检取消时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inspect-requests/list/ui`,
        method: 'GET',
      };
    },
  },
  record: {
    dynamicProps: {
      // LAST_COMPLETED才可点击
      selectable: record => ['LAST_COMPLETED'].includes(record?.get('inspectRequestStatus')),
    },
  },
});

const headerTableTwoDS = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectRequestWaitId',
  queryFields: [
    {
      name: 'inspectRequestCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestCode`).d('报检请求编码'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.MODEL.SITE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'inspectRequestStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestStatus`).d('状态'),
      options: statusOptionDs,
      textField: 'description',
      valueField: 'statusCode',
    },
    {
      name: 'businessObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      lovCode: 'APEX_WMS.COMMON.BUSINESS_TYPE',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
    },
    {
      name: 'businessType',
      bind: 'businessObj.typeCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.QMS.INSPECT_BUS_TYPE_RULE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'inspectBusinessType',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      ignore: FieldIgnore.always,
      multiple: true,
      lovCode: 'APEX_WMS.METHOD.MATERIAL.PERMISSION',
      textField:'materialName',
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },

    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'sourceObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源单据类型'),
      lookupCode: 'APEX_WMS.SOURCE_OBJECT_TYPE',
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectCode`).d('来源单据号'),
    },
    {
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('报检时间从'),
      name: 'inspectReqCreationDateFrom',
      type: FieldType.date,
      max: 'inspectReqCreationDateEnd',
    },
    {
      label: intl.get(`${modelPrompt}.creationDateTo`).d('报检时间至'),
      name: 'inspectReqCreationDateEnd',
      type: FieldType.date,
      min: 'inspectReqCreationDateFrom',
    },
    {
      name: 'inspectInfoUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.applicant`).d('报检人'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'inspectReqUserId',
      bind: 'inspectInfoUserLov.id',
    },
    {
      name: 'materialLot',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLot`).d('物料批'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.MATERIAL_LOT',
      textField: 'materialLotCode',
      multiple: true,
      lovPara: { tenantId },
    },
    {
      name: 'materialLotCodes',
      bind: 'materialLot.materialLotCode',
    },
  ],
  fields: [
    {
      name: 'inspectRequestCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestCode`).d('报检请求编码'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'businessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
    },
    {
      name: 'inspectRequestStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestStatus`).d('状态'),
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
      lookupCode: 'APEX_WMS.FLAG_YN',
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('报检数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'sourceObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源单据类型'),
    },
    {
      name: 'sourceNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceNumber`).d('来源单据编码#行号'),
    },
    {
      name: 'sourceObjectLineCode',
      type: FieldType.string,
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单号'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('场内批次'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'inspectReqUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqUserName`).d('报检人'),
    },
    {
      name: 'inspectReqCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCreationDate`).d('报检时间'),
    },
    {
      name: 'okQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.okQty`).d('合格数'),
    },
    {
      name: 'ngQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngQty`).d('不合格数'),
    },
    {
      name: 'scrapQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapQty`).d('报废数'),
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检验人'),
    },
    {
      name: 'inspectReqCompleteUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCompleteUserName`).d('处置完成人'),
    },
    {
      name: 'inspectReqCompleteDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCompleteDate`).d('处置完成时间'),
    },
    {
      name: 'inspectReqCancelUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCancelUserName`).d('报检取消人'),
    },
    {
      name: 'inspectReqCancelDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCancelDate`).d('报检取消时间'),
    },
    {
      name: 'dealStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealStatus`).d('处理状态编码'),
    },
    {
      name: 'dealStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealStatusDesc`).d('处理状态'),
    },
    {
      name: 'dealMessage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealMessage`).d('处理消息'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inspect-request-wait/list/ui`,
        method: 'GET',
      };
    },
  },
});

const detailDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspReqDisposalWaitId',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inspect-request-wait/disposal/wait/list/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'objectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectType`).d('报检对象类型'),
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestCode`).d('报检对象编码'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.count`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'disposalFunctionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalFunction`).d('处置方法'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('场内批次'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.count`).d('数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'disposalUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalUserName`).d('处置意见给出人'),
    },
    {
      name: 'disposalTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalTime`).d('处置意见给出时间'),
    },      {
      name: 'disposalApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalApartment`).d('人员所属部门'),
    },      {
      name: 'degradeLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel`).d('降级等级'),
    },      {
      name: 'degradeMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeMaterialName`).d('降级物料'),
    },
    {
      name: 'degradeRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeRevisionCode`).d('降级物料版本'),
    },      {
      name: 'disposalExecuteUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalExecuteUserName`).d('处置执行人'),
    },      {
      name: 'disposalExecuteTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalExecuteTime`).d('处置执行时间'),
    },
    {
      name: 'disposalExecuteDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalExecuteDocNum`).d('处置单据'),
    },
    {
      name: 'finalExecuteObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.finalExecuteObjectCode`).d('最终执行物料批'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

export { headerTableDS, headerTableTwoDS, searchFormDS, detailDS };
