/**
 * @Description: 抽样方式-接口
 * @Author: <<EMAIL>>
 * @Date: 2022-12-27 16:23:37
 * @LastEditTime: 2023-05-18 16:37:04
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

// 详情数据查询
export function fetchsamplingModeDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}//mt-sampling-method-maintenance/detail/query`,
    method: 'GET',
  };
}

// 详情数据保存
export function savesamplingModeDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-sampling-method-maintenance/update?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_DETAIL.BASIC`,
    method: 'POST',
  };
}
