/* eslint-disable no-undef */
import React, { useState, useMemo } from 'react';
import {
  DataSet,
  Table,
  Button,
  Row,
  Col,
  Select,
  Lov,
  TextField,
  Form,
  Icon,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import { getResponse } from '@utils/utils';
import intl from 'utils/intl';
import { Upload } from 'choerodon-ui';
import { tableDS } from './stores/TableDS';
import { API_HOST, Host } from '@/utils/config';
import { bind, outbound, inbound, download, inputData, blueGlueCodeCheck } from './services'
import InputLovDS from '../../stores/InputLovDS';
import LovModal from "../ProductBatchProcessCancellation/LovModal";

const modelPrompt = 'tarzan.hmes.InformationSupplementaryRecording';

const tenantId = getCurrentOrganizationId();

const InformationSupplementaryRecording = observer(() => {

  const [importLoading, setImportLoading] = useState(false);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);
  const [hasTableData, setHasTableData] = useState(false);

  const columns = [
    {
      name: 'lineNumber',
    },
    {
      name: 'status',
    },
    {
      name: 'message',
      width: 150,
    },
    {
      name: 'siteCode',
    },
    {
      name: 'equipmentCode',
    },
    {
      name: 'workcellCode',
    },
    {
      name: 'processBarcode',
    },
    {
      name: 'qty',
    },
    {
      name: 'gbCode',
    },
    {
      name: 'containerCode',
    },
    // {
    //   name: 'row',
    // },
    // {
    //   name: 'column',
    // },
    // {
    //   name: 'rawMaterialBarcode',
    // },
    {
      name: 'inputQty',
    },
    {
      name: 'assemblyPoint',
    },
    {
      name: 'assemblyGroup',
    },
    {
      name: 'userName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'wipStatus',
    },
    {
      name: 'currentWorkcellCode',
    },
    {
      name: 'currentProcessCode',
    },
    {
      name: 'currentStepStatus',
    },
    {
      name: 'qualityStatusMeaning',
    },
    {
      name: 'splitQuantity',
    },
    {
      name: 'firstProcessFlag',
    },
    {
      name: 'gears',
    },
  ];
  const uploadFile = info => {
    setImportLoading(false);
    if (info.success) {
      tableDs.query();
    } else if (info.message) {
      notification.error({
        message: info.message,
        description: '',
      });
    }
  };
  // const beforeUpload = () => {
    // if (!['.csv', '.xlsx', '.xls'].some(child => file.name.indexOf(child) >= 0)) {
    //   notification.warning({
    //     description: '',
    //     message: intl.get(`${modelPrompt}.notification.import.validate`).d('上传文本格式不正确'),
    //   });
    //   return false;
    // }
  //   setImportLoading(true);
  //   return true;
  // };
  const url = `${API_HOST}${Host}/v1/${tenantId}/hme-entry-exit-info/excel/import/ui`;
  const uploadProps = {
    name: 'file',
    // beforeUpload,
    showUploadList: false,
    headers: {
      authorization: `Bearer ${getAccessToken()}`,
    },
    accept: ['.csv', '.xlsx', '.xls'],
    action: url,
    onSuccess: uploadFile,
    showUploadBtn: false,
    data: {},
  };

  const handleBind = async () => {
    await bind({
      lineNumbers: tableDs.selected?.map(record => record.data.lineNumber),
    });
    tableDs.query();
  }
  const handleBatchEntry = async() => {
    await inbound({
      lineNumbers: tableDs.selected?.map(record => record.data.lineNumber),
    });
    tableDs.query();
  }
  const handleBatchOutbound = async () => {
    await outbound({
      lineNumbers: tableDs.selected?.map(record => record.data.lineNumber),
    });
    tableDs.query();
  }
  const handleInput = async () => {
    await inputData({
      lineNumbers: tableDs.selected?.map(record => record.data.lineNumber),
    });
    tableDs.query();
  }
  const handleExport = async () => {
    // 请求后台
    const result = await download();
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
          notification.error({ message: jsonData.message });
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], {
          type: 'vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        const url = window.URL.createObjectURL(file);
        const a = document.createElement('a');
        a.href = url;
        a.download = decodeURIComponent('进出站信息补录平台.xlsx');
        document.body.appendChild(a);
        a.click();
        setTimeout(() => {
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
        }, 1000);
      }
    }
  };

  const handleBlueGlueCodeCheck = async () => {
    const res = await blueGlueCodeCheck({
      lineNumbers: tableDs.selected?.map(record => record.data.lineNumber),
    });
    if (res?.message) {
      notification.error({ message: res.message });
    }
    tableDs.query();
  }

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleSearch()
    }
  }
  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const toggleForm = () => {
    setExpandForm(!expandForm);
  }

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="processBarcodes"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'processBarcodes', '产品条码', queryDataSet)}
                    />
                  </div>
                }
              />
              <Lov name="workCellLov" />
              <Lov name="operationLov" />
              {expandForm && (
                <>
                  <Select name="currentStepStatusCode" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div
              style={{
                flexShrink: 0,
                display: 'flex',
                alignItems: 'center',
                marginTop: '7px',
              }}
            >
              <Button funcType="link" icon={
                expandForm? 'expand_less':'expand_more'
              } onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  }
  const handleSearch = async () => {
    tableDs.query().then((res) => {
      setHasTableData(res?.content?.length)
    })
  }

  return (
    <div className='hmes-style'>
      <Header title={intl.get(`${modelPrompt}.title`).d('进出站信息补录平台')}>
        <>
          <Button
            onClick={handleInput}
            icon="edit"
            color="primary"
            disabled={!hasTableData}
          >
            {intl.get(`${modelPrompt}.button.data.input`).d('产品进出站')}
          </Button>
          <Button
            onClick={handleBatchOutbound}
            icon="edit"
            color="primary"
            disabled={!hasTableData}
          >
            {intl.get(`${modelPrompt}.button.data.batchOutbound`).d('批量出站')}
          </Button>
          <Button
            onClick={handleBatchEntry}
            icon="edit"
            color="primary"
            disabled={!hasTableData}
          >
            {intl.get(`${modelPrompt}.button.data.batchEntry`).d('批量进站')}
          </Button>

          <Button
            onClick={handleBind}
            color="primary"
            disabled={!hasTableData}
          >
            {intl.get(`tarzan.common.button.bind`).d('绑定')}
          </Button>
          <Button
            type="c7n-pro"
            icon="get_app"
            onClick={handleExport}
          >
            {intl.get(`${modelPrompt}.button.download.importTempalte`).d('导入模板获取')}
          </Button>
          <span style={{ margin: '-4px 0 0 8px' }}>
            <Upload {...uploadProps}>
              <Button
                type="c7n-pro"
                color='primary'
                icon="add"
                loading={importLoading}
              >
                {intl.get(`${modelPrompt}.button.data.import`).d('数据上传')}
              </Button>
            </Upload>
          </span>
          <Button
            onClick={handleBlueGlueCodeCheck}
            color="primary"
            disabled={!hasTableData}
          >
            {intl.get(`${modelPrompt}.blueGlueCodeCheck`).d('胶带码校验')}
          </Button>
        </>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={4}
          searchCode="InformationSupplementaryRecording"
          customizedCode="InformationSupplementaryRecording"
        />
      </Content>
      <LovModal {...lovModalProps} />
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.InformationSupplementaryRecording', 'tarzan.common'],
})(InformationSupplementaryRecording);
