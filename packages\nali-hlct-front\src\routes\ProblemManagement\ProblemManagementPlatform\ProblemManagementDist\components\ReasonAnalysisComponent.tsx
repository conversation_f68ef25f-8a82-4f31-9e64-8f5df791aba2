/**
 * @Description: 问题管理平台-原因分析页面组件1
 * @Author: <EMAIL>
 * @Date: 2023/7/6 14:36
 */
import React, { useMemo } from 'react';
import { Badge, Collapse, Icon, Tag } from 'choerodon-ui';
import intl from 'utils/intl';
import {
  Attachment,
  Button,
  DateTimePicker,
  Dropdown,
  Form,
  Menu,
  Select,
  Table,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { ColumnAlign } from 'choerodon-ui/pro/es/table/enum';
import { observer } from 'mobx-react';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { RecordStatus } from 'choerodon-ui/pro/lib/data-set/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import AttachmentComponent from './AttachmentComponent';
import styles from '../index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';

const ReasonAnalysisComponent = ({
  dataSet,
  freezeApplyDs,
  marketActDs,
  currentUserId,
  userRole,
  problemStatus,
  enableFlag,
  srmFlag,
  handleSaveReason,
  handleSubmitReason,
  handleChangeReasonEnable,
  handleChangeMarketFlag,
  handleSaveFreezeInfo,
  handleSubmitFreezeInfo,
  handleSubmitUnfreezeInfo,
  optionList,
  handleStartMarketAct,
}) => {
  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'problem-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const renderReasonStatusTag = (value, record) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
        className = 'green';
        break;
      case 'EVALUATING':
        className = 'yellow';
        break;
      default:
        className = 'blue';
    }
    return <Tag color={className}>{record!.getField('reasonStatus')!.getText()}</Tag>;
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      { name: 'sequence', align: ColumnAlign.left, width: 70 },
      {
        name: 'reasonType',
        width: 140,
        editor: record =>
          record.dataSet.getState('operationType') === 'edit' &&
          record.get('reasonStatus') === 'NEW' &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      {
        name: 'reason',
        editor: record =>
          record.dataSet.getState('operationType') === 'edit' &&
          record.get('reasonStatus') === 'NEW' &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)) && <TextArea name="reason" autoSize={{ minRows: 2, maxRows: 8 }} />,
      },
      { name: 'responsiblePersonLov', width: 80 },
      {
        name: 'recordTime',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'rationalityFlag',
        width: 100,
        align: ColumnAlign.center,
        editor: record =>
          record.dataSet.getState('operationType') === 'review' &&
          userRole.includes('LEAD_PERSON') &&
          record.get('reasonStatus') === 'EVALUATING' && !!record.get('rationalityFlag') || record.dataSet.getState('operationType') === 'Z' && userRole.includes('RESPONSIBLE_PERSON') && record.get('reasonStatus') === 'EVALUATING',
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`${modelPrompt}.label.rational`).d('合理')
                  : intl.get(`${modelPrompt}.label.unreasonable`).d('不合理')
              }
            />
          );
        },
      },
      {
        name: 'reasonStatus',
        width: 90,
        renderer: ({ record, value }) => renderReasonStatusTag(value, record),
      },
      {
        name: 'enclosure',
        width: 150,
        align: ColumnAlign.center,
        editor: record =>
          record.dataSet.getState('operationType') === 'edit' &&
          record.get('reasonStatus') === 'NEW' &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)) && (
            <Attachment {...attachmentProps} />
          ),
        renderer: ({ record }) => (
          <AttachmentComponent srmFlag={srmFlag} record={record} name="enclosure" />
        ),
      },
    ],
    [currentUserId, userRole?.length],
  );

  // 最终有效性是否可编辑
  const getDisabled = (dataSet, srmFlag) => {
    const evaluatedFlag = dataSet.find(_record => _record.get('reasonStatus') === 'EVALUATED');
    return !evaluatedFlag || srmFlag;
  };

  // 最终有效性按钮样式
  const getStyle = (dataSet, enableFlag, problemStatus, srmFlag) => {
    const enableStyle = {
      color: 'rgb(17, 217, 84)',
      borderColor: 'rgb(17, 217, 84)',
      backgroundColor: 'rgb(230, 255, 234)',
    };
    const disEnableStyle = {
      color: 'rgb(242, 58, 80)',
      borderColor: 'rgb(242, 58, 80)',
      backgroundColor: 'rgb(255, 240, 240)',
    };
    const emptyStyle = {
      color: 'rgb(47, 84, 235)',
      borderColor: 'rgb(47, 84, 235)',
      backgroundColor: 'rgb(240, 248, 255)',
    };
    const disableStyle = {
      opacity: 0.5,
      cursor: 'not-allowed',
    };
    const disabled = getDisabled(dataSet, srmFlag);

    if (disabled || !['RELEASED', 'FOLLOWING'].includes(problemStatus)) {
      if (enableFlag) {
        return enableFlag === 'Y'
          ? { ...enableStyle, ...disableStyle }
          : { ...disEnableStyle, ...disableStyle };
      }
      return { ...emptyStyle, ...disableStyle };
    }
    if (enableFlag) {
      return enableFlag === 'Y' ? enableStyle : disEnableStyle;
    }
    return emptyStyle;
  };

  const handleCancelAnalysis = () => {
    dataSet.reset();
    dataSet.setState('operationType', 'look');
  };

  const renderTextContent = enableFlag => {
    if (enableFlag) {
      return enableFlag === 'Y'
        ? intl.get(`${modelPrompt}.title.findReason`).d('找到根本原因')
        : intl.get(`${modelPrompt}.title.disFindReason`).d('未找到根本原因');
    }
    return intl.get(`${modelPrompt}.title.whetherFindReason`).d('是否找到根本原因');
  };

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      {optionList.map(item => {
        return (
          <Menu.Item key={item.value}>
            <a
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => handleChangeEnable(item.value)}
            >
              {item.meaning}
            </a>
          </Menu.Item>
        );
      })}
    </Menu>
  );

  const handleChangeEnable = value => {
    handleChangeReasonEnable(value);
  };

  const RenderAnalysisButtonGroup = observer(
    ({ dataSet, userRole, problemStatus, enableFlag, srmFlag }) => {
      if (['edit', 'review', 'Z'].includes(dataSet.getState('operationType'))) {
        return (
          <>
            {dataSet.getState('operationType') === 'edit' && (
              <>
                <Button
                  icon="delete"
                  funcType={FuncType.flat}
                  color={ButtonColor.red}
                  disabled={!dataSet.selected.length}
                  onClick={() => dataSet.remove(dataSet.selected)}
                >
                  {intl.get(`${modelPrompt}.button.delete`).d('删除')}
                </Button>
                <Button
                  icon="playlist_add"
                  funcType={FuncType.flat}
                  onClick={() => dataSet.create({}, 0)}
                >
                  {intl.get(`${modelPrompt}.button.add`).d('新增')}
                </Button>
              </>
            )}
            <Button icon="close" funcType={FuncType.flat} onClick={handleCancelAnalysis}>
              {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
            </Button>
            <Button icon="save" funcType={FuncType.flat} onClick={() => handleSaveReason(dataSet)}>
              {intl.get(`${modelPrompt}.button.save`).d('保存')}
            </Button>
          </>
        );
      }

      return (
        <>
          <Dropdown
            overlay={menu}
            disabled={
              getDisabled(dataSet, srmFlag) ||
              !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
              !userRole.includes('LEAD_PERSON')
            }
          >
            <div
              className={styles['enable-flag-button']}
              style={getStyle(dataSet, enableFlag, problemStatus, srmFlag)}
            >
              {enableFlag && <Icon type={enableFlag === 'Y' ? 'thumb_up' : 'thumb_down'} />}
              <span>{renderTextContent(enableFlag)}</span>
            </div>
          </Dropdown>
          {userRole.includes('LEAD_PERSON') && (
            <>
              <Button
                icon="send-o"
                funcType={FuncType.flat}
                disabled={!['RELEASED', 'FOLLOWING'].includes(problemStatus) || srmFlag}
                onClick={() => handleSubmitReason(dataSet, 'review')}
              >
                {intl.get(`${modelPrompt}.button.reviewSubmit`).d('审核提交')}
              </Button>
              <Button
                icon="rate_review1"
                funcType={FuncType.flat}
                disabled={
                  !userRole.includes('LEAD_PERSON') || problemStatus !== 'FOLLOWING' || srmFlag
                }
                onClick={() => dataSet.setState('operationType', 'review')}
              >
                {intl.get(`${modelPrompt}.button.reviewG`).d('跟进人评价')}
              </Button>
            </>
          )}
          {userRole.includes('RESPONSIBLE_PERSON') && (
            <>
              <Button
                icon="send-o"
                funcType={FuncType.flat}
                disabled={!['RELEASED', 'FOLLOWING'].includes(problemStatus) || srmFlag}
                onClick={() => handleSubmitReason(dataSet, 'reason')}
              >
                {intl.get(`${modelPrompt}.button.reasonSubmit`).d('原因提交')}
              </Button>
              <Button
                icon="edit-o"
                funcType={FuncType.flat}
                disabled={
                  !userRole.includes('RESPONSIBLE_PERSON') ||
                  !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
                  srmFlag
                }
                onClick={() => dataSet.setState('operationType', 'edit')}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
              <Button
                icon="rate_review1"
                funcType={FuncType.flat}
                disabled={
                  !userRole.includes('MAJOR_RESPONSIBLE_PERSON') || problemStatus !== 'FOLLOWING' || srmFlag
                }
                onClick={() => dataSet.setState('operationType', 'Z')}
              >
                {intl.get(`${modelPrompt}.button.reviewZ`).d('主责人评价')}
              </Button>
            </>
          )}
        </>
      );
    },
  );

  const RenderMarketButtonGroup = observer(({ dataSet, problemStatus, userRole, srmFlag }) => {
    return (
      <>
        <Button
          funcType={FuncType.flat}
          disabled={
            !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
            !userRole.includes('LEAD_PERSON') ||
            srmFlag
          }
          onClick={() => handleChangeMarketFlag('Y')}
        >
          {intl.get(`${modelPrompt}.button.open`).d('开启')}
        </Button>
        <Button
          funcType={FuncType.flat}
          disabled={
            !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
            !userRole.includes('LEAD_PERSON') ||
            srmFlag
          }
          onClick={() => handleChangeMarketFlag('N')}
        >
          {intl.get(`${modelPrompt}.button.close`).d('关闭')}
        </Button>
        <Button
          funcType={FuncType.flat}
          onClick={handleStartMarketAct}
          disabled={
            !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
            !userRole.includes('LEAD_PERSON') ||
            srmFlag ||
            dataSet.current?.get('marketActEvaluationFlag') !== 'Y'
          }
        >
          {intl.get(`${modelPrompt}.button.startMarketAct`).d('发起市场活动')}
        </Button>
      </>
    );
  });

  const handleCancelFreeze = () => {
    freezeApplyDs.reset();
    freezeApplyDs.setState('canEdit', false);
  };

  const RenderFreezeButtonGroup = observer(({ dataSet, problemStatus, userRole, srmFlag }) => {
    if (dataSet.getState('canEdit')) {
      return (
        <>
          <Button icon="close" funcType={FuncType.flat} onClick={handleCancelFreeze}>
            {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
          </Button>
          <Button
            icon="save"
            funcType={FuncType.flat}
            onClick={() => handleSaveFreezeInfo(dataSet)}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </Button>
        </>
      );
    }

    return (
      <>
        <Button
          funcType={FuncType.flat}
          disabled={
            problemStatus !== 'FREEZE' ||
            !(userRole.includes('LEAD_PERSON') || userRole.includes('MAJOR_RESPONSIBLE_PERSON')) ||
            srmFlag
          }
          onClick={() => handleSubmitUnfreezeInfo(enableFlag)}
        >
          {intl.get(`${modelPrompt}.button.thaw`).d('解冻')}
        </Button>
        <Button
          icon="send-o"
          funcType={FuncType.flat}
          disabled={
            !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
            !(userRole.includes('LEAD_PERSON') || userRole.includes('MAJOR_RESPONSIBLE_PERSON')) ||
            srmFlag
          }
          onClick={() => handleSubmitFreezeInfo(dataSet)}
        >
          {intl.get(`${modelPrompt}.button.submit`).d('提交')}
        </Button>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={
            !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
            !(userRole.includes('LEAD_PERSON') || userRole.includes('MAJOR_RESPONSIBLE_PERSON')) ||
            srmFlag
          }
          onClick={() => dataSet.setState('canEdit', true)}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </Button>
      </>
    );
  });

  const freezeAttachmentProps: any = {
    name: 'freezeEnclosure',
    bucketName: 'qms',
    bucketDirectory: 'problem-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  return (
    <Collapse
      bordered={false}
      defaultActiveKey={['reasonAnalysis', 'market', 'freezeApply']}
      collapsible="icon"
      className={styles['collapse-style']}
    >
      <Panel
        key="reasonAnalysis"
        header={intl.get(`${modelPrompt}.title.reasonAnalysis`).d('原因分析')}
        extra={
          <RenderAnalysisButtonGroup
            dataSet={dataSet}
            userRole={userRole}
            problemStatus={problemStatus}
            enableFlag={enableFlag}
            srmFlag={srmFlag}
          />
        }
      >
        <Table dataSet={dataSet} columns={columns} rowHeight="auto" />
      </Panel>
      <Panel
        key="market"
        header={intl.get(`${modelPrompt}.title.market`).d('市场活动评估')}
        extra={
          <RenderMarketButtonGroup
            dataSet={marketActDs}
            userRole={userRole}
            problemStatus={problemStatus}
            srmFlag={srmFlag}
          />
        }
      >
        <Form dataSet={marketActDs} columns={3}>
          <Select name="marketActEvaluationFlag" />
          <TextField name="marketActEvaluationNum" />
        </Form>
      </Panel>
      <Panel
        key="freezeApply"
        header={intl.get(`${modelPrompt}.title.freezeApply`).d('问题冻结申请')}
        extra={
          <RenderFreezeButtonGroup
            dataSet={freezeApplyDs}
            userRole={userRole}
            problemStatus={problemStatus}
            srmFlag={srmFlag}
          />
        }
      >
        <Form
          dataSet={freezeApplyDs}
          columns={3}
          disabled={
            !userRole.includes('LEAD_PERSON') && !userRole.includes('MAJOR_RESPONSIBLE_PERSON')
          }
        >
          <TextField name="freezeReason" colSpan={3} />
          <Select name="observePeriod" />
          <DateTimePicker name="freezeTime" />
          <Attachment {...freezeAttachmentProps} />
        </Form>
      </Panel>
    </Collapse>
  );
};
export default ReasonAnalysisComponent;
