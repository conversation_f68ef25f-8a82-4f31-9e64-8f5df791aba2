/**
 * @feature 成品条码注册功能-头行DS
 * @date 2021-12-28
 * <AUTHOR>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.inbound.barCodeRegistration';
const tenantId = getCurrentOrganizationId();

const headerTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.single,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'workOrderId',
  queryFields: [
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'APEX_WMS.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteObj.siteId',
    },
    {
      name: 'workOrderObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
      lovCode: 'APEX_WMS.WORK_ORDER_NUM',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      bind: 'workOrderObj.workOrderNum',
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'APEX_WMS.METHOD.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialObj.materialCode',
    },
    {
      name: 'materialLotCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCodes`).d('条码编码'),
    },
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      max: 'planEndTime',
      label: intl.get(`${modelPrompt}.planStartTimeFrom`).d('计划开始时间从'),
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      min: 'planStartTime',
      label: intl.get(`${modelPrompt}.planStartTimeTo`).d('计划开始时间至'),
    },
    {
      name: 'noPrintDataFlag',
      type: FieldType.string,
      lookupCode: 'APEX_WMS.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      label: intl.get(`${modelPrompt}.noPrintDataFlag`).d('查看未打印数据'),
    },
    {
      name: 'noGenerateBarcodeFlag',
      type: FieldType.string,
      lookupCode: 'APEX_WMS.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      label: intl.get(`${modelPrompt}.noGenerateBarcodeFlag`).d('查看未生成条码数据'),
    },
  ],
  fields: [
    {
      name: 'workOrderId',
      type: FieldType.number,
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'statusCode',
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.quantity`).d('生产指令数量'),
    },
    {
      name: 'sumQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumQty`).d('已创建条码数量'),
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
    },
    {
      name: 'canCreateBarcodeQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.createdQty`).d('可创建条码数量'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNumber`).d('销售订单号'),
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
    },
    {
      name: 'storageQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.storageQty`).d('已入库数量'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const { materialLotCodes, ...others } = data;
      const _materialLotCodes = materialLotCodes
        ? data.materialLotCodes.replace(/\s+/g, ',')
        : undefined;
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-product-barcode-register/head/list/ui`,
        method: 'GET',
        data: {
          ...others,
          materialLotCodes: _materialLotCodes,
        },
      };
    },
  },
});

const lineTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: '',
  queryFields: [
    {
      name: 'materialLotCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('条码编码'),
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('条码状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'printTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'createdDateFrom',
      type: FieldType.dateTime,
      max: 'createdDateTo',
      label: intl.get(`${modelPrompt}.createdDateFrom`).d('创建时间从'),
    },
    {
      name: 'createdDateTo',
      type: FieldType.dateTime,
      min: 'createdDateFrom',
      label: intl.get(`${modelPrompt}.createdDateTo`).d('创建时间至'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
  ],
  fields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('条码编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('条码状态'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'printTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'createdDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdDate`).d('创建时间'),
    },
    {
      name: 'productionDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const { materialLotCodes, ...others } = data;
      const _materialLotCodes = materialLotCodes
        ? data.materialLotCodes.replace(/\s+/g, ',')
        : undefined;
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-product-barcode-register/line/list/ui`,
        method: 'GET',
        data: {
          ...others,
          materialLotCodes: _materialLotCodes,
        },
      };
    },
  },
});

export { headerTableDS, lineTableDS };
