import React from 'react';
import { Modal, Form, Checkbox, Col, Row } from 'hzero-ui';
import { Bind } from 'lodash-decorators';
import intl from 'utils/intl';
import { SEARCH_FORM_ROW_LAYOUT, FORM_COL_2_LAYOUT } from 'utils/constants';

const FormItem = Form.Item;
const modelPrompt = 'tarzan.hspc.chartInfo';

@Form.create()
class ChartSettingModal extends React.PureComponent {
  @Bind()
  handleOK() {
    const { getChartSettingData, onCancel, form } = this.props;
    form.validateFieldsAndScroll((err, fieldsValue) => {
      if (!err) {
        getChartSettingData(fieldsValue);
        onCancel();
      }
    });
  }

  @Bind()
  handleCancel() {
    const { onCancel } = this.props;
    onCancel();
  }

  render() {
    const { form, title, loading, initData } = this.props;
    const { getFieldDecorator } = form;
    const { showCL, showCLSIGMA, showSL, showCLTitle, showXAttr, hideFlag } = initData;
    const formItemLayout = {
      labelCol: { span: 16 },
      wrapperCol: { span: 8 },
    };

    return (
      <Modal
        destroyOnClose
        title={title}
        width={600}
        visible={this.props.modalVisible}
        confirmLoading={loading}
        onCancel={this.handleCancel}
        onOk={this.handleOK}
        okText={intl.get('tarzan.common.button.confirm').d('确定')}
        cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
      >
        <Form className="chartSettingForm" style={{ width: '100%' }}>
          <Row {...SEARCH_FORM_ROW_LAYOUT}>
            <Col {...FORM_COL_2_LAYOUT}>
              <FormItem
                {...formItemLayout}
                label={intl.get(`${modelPrompt}.displayControlLimit`).d('显示控制限')}
              >
                {getFieldDecorator('showCL', {
                  initialValue: showCL,
                })(<Checkbox />)}
              </FormItem>
            </Col>
            <Col {...FORM_COL_2_LAYOUT}>
              <FormItem
                {...formItemLayout}
                label={intl.get(`${modelPrompt}.displayPartitionLine`).d('显示分区线')}
              >
                {getFieldDecorator('showCLSIGMA', {
                  initialValue: showCLSIGMA,
                })(<Checkbox />)}
              </FormItem>
            </Col>
          </Row>
          <Row {...SEARCH_FORM_ROW_LAYOUT}>
            <Col {...FORM_COL_2_LAYOUT}>
              <FormItem
                {...formItemLayout}
                label={intl.get(`${modelPrompt}.displaySpecLimit`).d('显示规格限')}
              >
                {getFieldDecorator('showSL', {
                  initialValue: showSL,
                })(<Checkbox />)}
              </FormItem>
            </Col>
            <Col {...FORM_COL_2_LAYOUT}>
              <FormItem
                {...formItemLayout}
                label={intl.get(`${modelPrompt}.displayChartTitle`).d('显示控制图标题')}
              >
                {getFieldDecorator('showCLTitle', {
                  initialValue: showCLTitle,
                })(<Checkbox />)}
              </FormItem>
            </Col>
          </Row>
          <Row {...SEARCH_FORM_ROW_LAYOUT}>
            <Col {...FORM_COL_2_LAYOUT}>
              <FormItem
                {...formItemLayout}
                label={intl
                  .get(`${modelPrompt}.XAxisShowsSampleProperties`)
                  .d('X轴刻度显示样本属性')}
              >
                {getFieldDecorator('showXAttr', {
                  initialValue: showXAttr,
                })(<Checkbox />)}
              </FormItem>
            </Col>
            <Col {...FORM_COL_2_LAYOUT}>
              <FormItem
                {...formItemLayout}
                label={intl
                  .get(`${modelPrompt}.cancelHidden`)
                  .d('取消隐藏')}
              >
                {getFieldDecorator('hideFlag', {
                  initialValue: false,
                })(<Checkbox />)}
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
}

export default ChartSettingModal;
