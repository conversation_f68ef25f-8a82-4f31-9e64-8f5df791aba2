/**
 * @Description: 体系审核管理-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>

*/
import React, { useEffect, useState } from 'react';
import { DataSet, Table, Tabs } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { TabsType } from 'choerodon-ui/lib/tabs/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { useDataSetEvent } from 'utils/hooks';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import { listTableDS } from '../stores/SystemAuditListDS';
import { auditInformationDS } from '../stores/AuditTab1InformationDS';
import { auditGroupFormDS, auditGroupTableDS } from '../stores/AuditTab2GroupDS';
import { auditScheduleFormDS, auditScheduleTableDS } from '../stores/AuditTab3ScheduleDS';

import AuditTab1Information from '../components/AuditTab1Information';
import AuditTab2Group from '../components/AuditTab2Group';
import AuditTab3Schedule from '../components/AuditTab3Schedule';

import {
  fetchAuditInformationConfig,
  fetchAuditGroupConfig,
  fetchAuditScheduleConfig,
} from '../services';

const modelPrompt = 'tarzan.systemAudit';
const { TabPane } = Tabs;

const tabsMap = [
  {
    number: 1,
    key: 'AuditTab1Information',
    titleKey: 'AuditTab1Information',
    titleDesc: intl.get(`${modelPrompt}.AuditTab1Information`).d('审核信息'),
  },
  {
    number: 2,
    key: 'AuditTab2Group',
    titleKey: 'AuditTab2Group',
    titleDesc: intl.get(`${modelPrompt}.AuditTab2Group`).d('审核小组'),
  },
  {
    number: 3,
    key: 'AuditTab3Schedule',
    titleKey: 'AuditTab3Schedule',
    titleDesc: intl.get(`${modelPrompt}.AuditTab3Schedule`).d('审核日程'),
  },
];

const SystemAuditList = props => {
  const {
    tableDs,
    auditInformationDs,
    auditScheduleFormDs,
    auditScheduleTableDs,
    match: { path },
  } = props;

  const [id, setId]: any = useState();

  const [activeKey, setActiveKey] = useState('AuditTab1Information');

  const [focusRowKey, setFocusRowKey] = useState();

  // tabs 列表
  const [tabsList, setTabsList]: any = useState([]);

  // 审核小组的数据
  const [groupDataList, setGroupDataList]: any = useState([]);

  // 查询审核信息
  const fetchAuditInformation = useRequest(fetchAuditInformationConfig(), {
    manual: true,
    needPromise: true,
  });
  // 查询审核小组
  const fetchAuditGroup = useRequest(fetchAuditGroupConfig(), { manual: true, needPromise: true });
  // 查询审核日程
  const fetchAuditSchedule = useRequest(fetchAuditScheduleConfig(), {
    manual: true,
    needPromise: true,
  });

  const handleQueryFirstLine = queryProps => {
    queryLineDetail(queryProps?.dataSet?.current);
  };

  useDataSetEvent(tableDs, 'load', handleQueryFirstLine);

  useEffect(() => {
    setTabsList(tabsMap.slice(0, 3));
    if (tableDs) {
      tableDs.query(props.tableDs.currentPage);
    } else {
      tableDs.query();
    }
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'sysReviewPlanCode',
      lock: ColumnLock.left,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
      width: 220,
    },
    {
      name: 'sysReviewTitle',
    },
    {
      name: 'sysReviewPlanStatus',
    },
    {
      name: 'siteLov',
      width: 220,
    },
    {
      name: 'scheduleDateStart',
    },
    {
      name: 'scheduleDateEnd',
    },
    {
      name: 'reviewDateStart',
    },
    {
      name: 'reviewDateEnd',
    },
  ];

  const handleEdit = record => {
    props.history.push(`/hwms/system-audit/detail/${record.get('sysReviewPlanId')}`);
  };

  const handleCreate = () => {
    props.history.push(`/hwms/system-audit/detail/create`);
  };

  const headerRowClick = record => {
    queryLineDetail(record);
  };

  const queryLineDetail = record => {
    setActiveKey('AuditTab1Information');
    const key = record?.get('sysReviewPlanId');
    setFocusRowKey(key);
    getAuditInformation(key);
    getAuditGroup(key);
    getAuditSchedule(key);
  };

  const componentsProps: any = {
    id,
    activeKey,
    readonly: true,
  };

  const getAuditInformation = async key => {
    if (key) {
      setId(key);
      // 判断单据当前进度
      const _res = await fetchAuditInformation.run({
        params: { sysReviewPlanId: key },
      });
      auditInformationDs.loadData([_res?.rows || {}]);
    } else {
      auditInformationDs.loadData([{}]);
    }
  };
  const getAuditGroup = async key => {
    if (key) {
      // 判断单据当前进度
      const _res = await fetchAuditGroup.run({
        params: { sysReviewPlanId: key },
      });
      setGroupDataList(
        (_res?.rows || []).map(item => {
          const { groupMem, ...data } = item;
          const groupFormDs = new DataSet(auditGroupFormDS());
          const groupTableDs = new DataSet(auditGroupTableDS());
          groupFormDs.loadData([data || {}]);
          groupTableDs.loadData(groupMem || []);
          return {
            formDs: groupFormDs,
            tableDs: groupTableDs,
          };
        }),
      );
    } else {
      setGroupDataList([]);
    }
  };

  const getAuditSchedule = async key => {
    if (key) {
      // 判断单据当前进度
      const _res = await fetchAuditSchedule.run({
        params: { sysReviewPlanId: key },
      });
      if (_res?.success) {
        const { reviewScheduleList, ...data } = _res?.rows || {};
        auditScheduleFormDs.loadData([data || {}]);
        auditScheduleTableDs.loadData(
          reviewScheduleList.map(item => {
            const { scheduleTimeFrom, scheduleTimeTo, ...other } = item;
            return {
              ...other,
              scheduleTime: {
                scheduleTimeFrom,
                scheduleTimeTo,
              },
              memberInfo: (item.memberInfo || '').split(','),
              beRevDeptPer: (item.beRevDeptPer.toString() || '').split(','),
              beRevDeptPerName: (item.beRevDeptPerName || '').split(','),
            };
          }) || [],
        );
      } else {
        auditScheduleFormDs.loadData([{}]);
        auditScheduleTableDs.loadData([]);
      }
    } else {
      auditScheduleFormDs.loadData([{}]);
      auditScheduleTableDs.loadData([]);
    }
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.SystemAuditMaintenance`).d('体系审核管理维护')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.create`,
              type: 'button',
              meaning: '列表页-新建',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleCreate()}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          style={{ height: 300 }}
          searchCode="txshgl1"
          customizedCode="txshgl1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
        />
        <Tabs
          type={TabsType.card}
          defaultActiveKey="AuditTab1Information"
          activeKey={activeKey}
          onChange={tabkey => {
            setActiveKey(tabkey);
          }}
        >
          {focusRowKey &&
            tabsList &&
            tabsList.length &&
            tabsList.map(item => (
              <TabPane
                title={intl.get(`${modelPrompt}.${item.titleKey}`).d(item.titleDesc)}
                key={item.key}
              >
                {item.key === 'AuditTab1Information' && (
                  <AuditTab1Information
                    {...componentsProps}
                    auditInformationDs={auditInformationDs}
                    fetchAuditInformation={fetchAuditInformation}
                  />
                )}
                {item.key === 'AuditTab2Group' && (
                  <AuditTab2Group {...componentsProps} groupDataList={groupDataList} />
                )}
                {item.key === 'AuditTab3Schedule' && (
                  <AuditTab3Schedule
                    {...componentsProps}
                    auditScheduleFormDs={auditScheduleFormDs}
                    auditScheduleTableDs={auditScheduleTableDs}
                  />
                )}
              </TabPane>
            ))}
        </Tabs>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...listTableDS(),
      });
      const auditInformationDs = new DataSet({
        ...auditInformationDS(),
      });
      const auditScheduleFormDs = new DataSet({
        ...auditScheduleFormDS(),
      });
      const auditScheduleTableDs = new DataSet({
        ...auditScheduleTableDS(),
      });
      return {
        tableDs,
        auditInformationDs,
        auditScheduleFormDs,
        auditScheduleTableDs,
      };
    },
    { cacheState: true, keepOriginDataSet: true },
  )(SystemAuditList),
);
