import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const isJSON = str => {
  try {
    if (JSON.parse(str) instanceof Object) {
      return true;
    }
  } catch {
    return false;
  }
};

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content', // 列表数据在接口返回json中的相对路径
  totalKey: 'rows.totalElements',
  primaryKey: 'materialId', // 表格唯一性主键
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'productionLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.productionLine`).d('生产线'),
      lovCode: 'HME.PERMISSION_PROD_LINE',
      noCache: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      // dynamicProps: {
      //   lovPara: ({ record }) => {
      //     const siteId = record.get('siteId');
      //     return {
      //       tenantId,
      //       siteId,
      //     };
      //   },
      // },
    },
    {
      name: 'prodLineId',
      bind: 'productionLine.prodLineId',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'stationWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.stationWorkcellName`).d('工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        userFlag: 'Y',
      },
      // dynamicProps: {
      //   lovPara: ({ record }) => ({
      //     tenantId,
      //     siteId: record?.get('siteId'),
      //   }),
      // },
    },
    {
      name: 'workcellId',
      bind: 'stationWorkcellObj.workcellId',
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.componentMaterialId`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      // dynamicProps: {
      //   lovPara: ({ record }) => ({
      //     tenantId,
      //     siteId: record?.get('siteId'),
      //   }),
      // },
      textField: 'materialName',
    },
    {
      name: 'materialId',
      bind: 'materialObj.materialId',
    },
    {
      name: 'startDate',
      type: FieldType.date,
      max: 'endDate',
      label: intl.get(`${modelPrompt}.startDate`).d('开始时间'),
      // defaultValue: moment(new Date()).format('YYYY-MM-DD'),
      // defaultValue: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      name: 'endDate',
      type: FieldType.date,
      min: 'startDate',
      label: intl.get(`${modelPrompt}.endDate`).d('结束时间'),
      // defaultValue: moment(new Date()).format('YYYY-MM-DD'),
    },
    {
      name: 'shift',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.shift`).d('班次'),
      lookupUrl: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/hme-calendar-shift/shift/ui`,
      valueField: 'shiftDateCode',
      textField: 'shiftDateCode',
      // @ts-ignore
      noCache: true,
      ignore: FieldIgnore.always,
      lookupAxiosConfig: ({ record }) => {
        const _params = record?.toData() || {};
        return {
          params: {
            siteId: _params.siteId,
            prodLineId: _params.prodLineId,
            startDate: _params.startDate,
            endDate: _params.endDate,
          },
          transformResponse(data) {
            // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
            if (data instanceof Array) {
              return data;
            }
            if (isJSON(data)) {
              const rows = JSON.parse(data);
              return rows;
            }
            return [];
          },
        };
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(record.get('endDate') && record.get('startDate'));
        },
      },
    },
    {
      name: 'shiftDate',
      bind: 'shift.shiftDate',
    },
    {
      name: 'shiftCode',
      bind: 'shift.shiftCode',
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('生产指令状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      multiple: true,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/list/ui?statusGroup=WO_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if(data){
            if (data instanceof Array || data.content instanceof Array) {
              return data || data.content;
            }
            const list = JSON.parse(data);
            return list.rows;
          }
        },
      },
    },
  ],
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线描述'),
    },
    {
      name: 'woStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woStatus`).d('生产指令状态'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
    },
    {
      name: 'opDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.opDescription`).d('工艺描述'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工位描述'),
    },
    {
      name: 'reportDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.reportDate`).d('报工日期'),
    },
    {
      name: 'shiftDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftDesc`).d('班次编码'),
    },
    {
      name: 'woQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woQty`).d('生产指令数量'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('报工数量'),
    },
    {
      name: 'materialCodeRevision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCodeRevision`).d('物料/版本'),
    },
    // {
    //   name: 'version',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.version`).d('版本'),
    // },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
