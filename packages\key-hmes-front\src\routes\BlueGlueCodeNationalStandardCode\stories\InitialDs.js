// import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.BlueGlueCodeNationalStandardCode';
// const prefix = '/yp-mes-38546'

const initialDs = () => ({
  autoQuery: false,
  primaryKey: 'lineNumber',
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'identificationList',
      type: 'string',
      label: intl.get(`${modelPrompt}.identificationList`).d('胶带码'),
      multiple: true,
    },
    {
      name: 'blueGlueCodeList',
      type: 'string',
      label: intl.get(`${modelPrompt}.blueGlueCodeList`).d('极耳码'),
      multiple: true,
    },
    {
      name: 'positiveTopCodeList',
      type: 'string',
      label: intl.get(`${modelPrompt}.positiveTopCodeList`).d('封装码'),
      multiple: true,
    },
  ],
  fields: [
    {
      name: 'identification',
      type: 'string',
      label: intl.get(`${modelPrompt}.identification`).d('胶带码'),
    },
    {
      name: 'blueGlueCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.blueGlueCode`).d('极耳码'),
    },
    {
      name: 'positiveTopCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.positiveTopCode`).d('封装码'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-identification-blue-code-query/list/query`,
        method: 'POST',
        data: {
          ...data,
        },
      };
    },
  },
});


export { initialDs };
