/*
 * @Description: 索赔管理-详情页
 * @Author: <<EMAIL>>
 * @Date: 2023-09-21 16:26:53
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-25 15:02:22
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Table,
  Button,
  Form,
  Lov,
  NumberField,
  TextArea,
  TextField,
  Select,
  Attachment,
  DatePicker,
} from 'choerodon-ui/pro';
import { getCurrentUser } from 'utils/utils';
import { Collapse, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { Header, Content } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import intl from 'utils/intl';
import moment from 'moment';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { detailDS, tableDS } from '../stores/DetailDS';
import { SaveClaimDoc, QueryMaterialInfo } from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.qms.marketActivity.claimDocManagement';
const currentUserInfo = getCurrentUser();

const ClaimDocDetail = props => {
  const {
    history,
    match: { params },
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState(false);
  const [disabledFlag, setDisabledFlag] = useState(false);
  const [roleList, setRoleList] = useState<string[]>([]);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          lineInfoList: tableDs,
        },
      }),
    [],
  );
  const { run: saveClaimDoc, loading: saveLoading } = useRequest(SaveClaimDoc(), {
    manual: true,
    needPromise: true,
  });
  // 查询物料批信息
  const { run: queryMaterialInfo, loading: queryLoading } = useRequest(QueryMaterialInfo(), {
    manual: true,
  });
  const { run: queryRoleList, loading: roleListLoading } = useRequest(
    { lovCode: 'YP.QIS.CLAIM_EDIT' },
    {
      manual: true,
      needPromise: true,
    },
  );

  useEffect(() => {
    if (kid === 'create') {
      // 新建时
      setCanEdit(true);
      return;
    }
    // 编辑时
    handleInitPage();
  }, [kid, roleList?.length]);

  const handleInitPage = () => {
    if (!roleList?.length) {
      handleInitRoleList();
    }
    detailDs.setQueryParameter('claimDocId', kid);
    detailDs.query().then(res => {
      const { createdBy } = res?.rows || {};
      if (roleList?.length) {
        setDisabledFlag(
          createdBy !== currentUserInfo.id && !roleList.includes(currentUserInfo.currentRoleCode),
        );
      }
    });
  };

  const handleInitRoleList = async () => {
    const res = await queryRoleList({});
    if (res?.length) {
      const _roleList = res.map(item => item.value);
      setRoleList(_roleList);
    }
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(() => {
    if (kid === 'create') {
      history.push('/hwms/market-quality/claim-doc-management/list');
    } else {
      handleInitPage();
    }
  }, []);

  const getEnableFlag = (validFrom, validTo) => {
    const _validateFrom = moment(validFrom).format('X');
    let _validateTo;
    if (validTo) {
      _validateTo = moment(validTo).format('X');
    }
    const _currentDate = moment(new Date()).format('X');
    if (_validateTo) {
      return _validateFrom <= _currentDate && _currentDate <= _validateTo ? 'Y' : 'N';
    }
    return _validateFrom <= _currentDate ? 'Y' : 'N';
  };

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const { validFrom, validTo, lineInfoList, ...others } = detailDs!.current!.toData();
    const _lineInfoList = lineInfoList.filter(item => !item.deleteFlag);
    const res = await saveClaimDoc({
      params: {
        ...others,
        validFrom,
        validTo,
        enableFlag: getEnableFlag(validFrom, validTo),
        lineInfoList: _lineInfoList,
      },
    });
    if (res && res.success) {
      notification.success({});
      setCanEdit(false);
      if (kid === 'create') {
        history.push(`/hwms/market-quality/claim-doc-management/dist/${res.rows}`);
      } else {
        handleInitPage();
      }
      return true;
    }
    return false;
  };

  const handleAddLine = () => {
    let maxSeq = 0;
    tableDs.forEach(_record => {
      if (_record?.get('sequence') > maxSeq) {
        maxSeq = _record?.get('sequence');
      }
    });
    tableDs.create({ sequence: maxSeq + 10 });
  };

  const handleDeleteLine = record => {
    if (record?.get('claimLineId')) {
      record?.set('deleteFlag', true);
    } else {
      tableDs.remove(record);
    }
    // 已经存在表里的行，序号不变，其他序号递增
    let _maxSeq = 0;
    tableDs.forEach(_record => {
      if (_record?.get('sequence') > _maxSeq && _record?.get('claimLineId')) {
        _maxSeq = _record?.get('sequence');
      } else {
        _record.set('sequence', _maxSeq + 10);
        _maxSeq += 10;
      }
    });
  };

  const columns: any = useMemo(
    () => [
      {
        header: () => (
          <Button
            icon="add"
            disabled={!canEdit}
            onClick={handleAddLine}
            funcType={FuncType.flat}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        lock: ColumnLock.left,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDeleteLine(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <Button icon="remove" disabled={!canEdit || disabledFlag} funcType={FuncType.flat} />
          </Popconfirm>
        ),
      },
      { name: 'sequence', align: ColumnAlign.left },
      {
        name: 'vehicalModel',
        editor: () => canEdit && <Select searchable searchFieldInPopup />,
      },
      {
        name: 'cusMaterialCode',
        editor: canEdit,
      },
      {
        name: 'cusMaterialName',
        editor: canEdit,
      },
      {
        name: 'materialLov',
        editor: record =>
          canEdit && <Lov onChange={value => handleChangeMaterial(value, record)} />,
      },
      {
        name: 'materialName',
        editor: canEdit,
      },
      { name: 'batteryModel' },
    ],
    [canEdit],
  );

  const handleChangeMaterial = (value, record) => {
    if (value?.materialId) {
      queryMaterialInfo({
        params: value?.materialId,
        onSuccess: res => {
          record.set('batteryModel', res);
        },
      });
    } else {
      record.set('batteryModel', undefined);
    }
  };

  const enclosureProps: any = {
    name: 'dutyEnclosure',
    bucketName: 'qms',
    bucketDirectory: 'market-Activity-doc',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || queryLoading || roleListLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('索赔管理')}
          backPath="/hwms/market-quality/claim-doc-management/list"
        >
          {canEdit ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <Button
              icon="edit-o"
              disabled={disabledFlag}
              color={ButtonColor.primary}
              onClick={handleEdit}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'vahicalAndPackType']}>
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.basicInfo`).d('基础信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="claimDocNum" />
                <Lov name="siteLov" />
                <TextField name="hostPlant" />
                <TextField name="title" />
                <NumberField name="aResponsibilityRatio" suffix="%" />
                <NumberField name="bResponsibilityRatio" suffix="%" />
                <Attachment {...enclosureProps} />
                <Select name="enableFlag" />
                <Lov name="createPersonLov" />
                <TextArea
                  name="remarks"
                  maxLength={1000}
                  colSpan={3}
                  autoSize={{ minRows: 2, maxRows: 8 }}
                />
                <DatePicker name="validFrom" />
                <DatePicker name="validTo" />
              </Form>
            </Panel>
            <Panel
              key="vahicalAndPackType"
              header={intl.get(`${modelPrompt}.title.vahicalAndPackType`).d('车型与电池包型号')}
            >
              <Table
                dataSet={tableDs}
                columns={columns}
                filter={record => !record?.get('deleteFlag')}
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(ClaimDocDetail);
