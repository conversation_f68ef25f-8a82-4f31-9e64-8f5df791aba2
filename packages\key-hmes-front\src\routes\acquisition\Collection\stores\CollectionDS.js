/**
 * @Description: 数据收集组DS
 * @Author: <<EMAIL>>
 * @Date: 2021-03-01 10:47:01
 * @LastEditTime: 2022-11-08 14:56:18
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import uuid from 'uuid/v4';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.acquisition.collection';
const tenantId = getCurrentOrganizationId();

const objectLovCode = {
  MATERIAL: 'MT.METHOD.BOM_MATERIAL',
  OPERATION: 'MT.METHOD.OPERATION',
  ROUTER: 'MT.METHOD.ROUTER_SITE',
  ROUTER_STEP: 'MT.MES.ROUTER_STEP',
  'MT.WO_ROUTER': 'MT.WO_ROUTER',
  'MT.ROUTER_STEP': 'MT.ROUTER_STEP',
  WORKCELL: 'MT.MODEL.WORKCELL_SITE',
  NC_CODE: 'MT.NC_CODE',
  BOM: 'MT.MES.BOM_BASIC',
  BOM_COMPONENT: 'MT.MES.BOM_COMPONENT',
  SUBSTEP: 'MT.METHOD.SUBSTEP',
  WORK_ORDER: 'MT.WORK_ORDER',
  EO: 'MT.EO',
  MATERIAL_CATEGORY: 'MT.METHOD.MATERIAL_CATEGORY_SITES',
  MATERIAL_LOT: 'MT.MATERIAL_LOT',
  'APEX_MES.USER_EQUIP_WKC_REL': 'APEX_MES.USER_EQUIP_WKC_REL',
};

const objectIdBind = {
  MATERIAL: 'objectLov.materialId',
  OPERATION: 'objectLov.operationId',
  ROUTER: 'objectLov.routerId',
  ROUTER_STEP: 'objectLov.routerStepId',
  'MT.WO_ROUTER': 'objectLov.routerId',
  'MT.ROUTER_STEP': 'objectLov.routerStepId',
  WORKCELL: 'objectLov.workcellId',
  NC_CODE: 'objectLov.ncCodeId',
  BOM: 'objectLov.bomId',
  BOM_COMPONENT: 'objectLov.bomComponentId',
  SUBSTEP: 'objectLov.substepId',
  WORK_ORDER: 'objectLov.workOrderId',
  EO: 'objectLov.eoId',
  MATERIAL_CATEGORY: 'objectLov.materialCategoryId',
  MATERIAL_LOT: 'objectLov.materialLotId',
  "APEX_MES.USER_EQUIP_WKC_REL": 'objectLov.equipmentId',
};

const objectCodeBind = {
  MATERIAL: 'objectLov.materialCode',
  OPERATION: 'objectLov.operationName',
  ROUTER: 'objectLov.routerName',
  ROUTER_STEP: 'objectLov.stepName',
  'MT.WO_ROUTER': 'objectLov.routerName',
  'MT.ROUTER_STEP': 'objectLov.stepName',
  WORKCELL: 'objectLov.workcellCode',
  NC_CODE: 'objectLov.ncCode',
  BOM: 'objectLov.bomName',
  BOM_COMPONENT: 'objectLov.bomComponentMaterialCode',
  SUBSTEP: 'objectLov.substepName',
  WORK_ORDER: 'objectLov.workOrderNum',
  EO: 'objectLov.eoNum',
  MATERIAL_CATEGORY: 'objectLov.categoryCode',
  MATERIAL_LOT: 'objectLov.materialLotCode',
  "APEX_MES.USER_EQUIP_WKC_REL": 'objectLov.equipmentCode',
};

const objectDescBind = {
  MATERIAL: 'objectLov.materialName',
  OPERATION: 'objectLov.description',
  ROUTER: 'objectLov.description',
  ROUTER_STEP: 'objectLov.description',
  'MT.WO_ROUTER': 'objectLov.description',
  'MT.ROUTER_STEP': 'objectLov.description',
  WORKCELL: 'objectLov.workcellName',
  NC_CODE: 'objectLov.description',
  BOM: 'objectLov.description',
  BOM_COMPONENT: 'objectLov.bomComponentMaterialDesc',
  SUBSTEP: 'objectLov.description',
  WORK_ORDER: '',
  EO: '',
  MATERIAL_CATEGORY: 'objectLov.description',
  MATERIAL_LOT: '',
  'APEX_MES.USER_EQUIP_WKC_REL': 'objectLov.equipmentName',
};

const objectRevisionBind = {
  MATERIAL: '',
  OPERATION: 'objectLov.revision',
  ROUTER: 'objectLov.revision',
  ROUTER_STEP: '',
  'MT.WO_ROUTER': 'objectLov.revision',
  'MT.ROUTER_STEP': '',
  WORKCELL: '',
  NC_CODE: '',
  BOM: 'objectLov.revision',
  BOM_COMPONENT: '',
  SUBSTEP: '',
  WORK_ORDER: '',
  EO: '',
  MATERIAL_CATEGORY: '',
  MATERIAL_LOT: '',
  'APEX_MES.USER_EQUIP_WKC_REL': '',
};

const siteOptionDs = new DataSet({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-component/user/distribution/site/list/ui`,
        method: 'GET',
        params: { tenantId },
      };
    },
  },
});

const materialRevisionOptionDs = new DataSet({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
        method: 'GET',
        params: { tenantId },
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          let firstlyQueryData = [];
          const { rows } = JSON.parse(data);
          if (rows instanceof Array) {
            firstlyQueryData = rows.map(item => {
              return {
                kid: uuid(),
                description: item,
              };
            });
          }
          return firstlyQueryData;
        },
      };
    },
  },
});

const tableDS = () => ({
  autoQuery: false,
  autoCreate: true,
  primaryKey: 'tagGroupId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: 'multiple',
  queryFields: [
    {
      name: 'tagGroupCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupCode`).d('数据收集组编码'),
    },
    {
      name: 'tagGroupDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupDescription`).d('数据收集组描述'),
    },
    {
      name: 'status',
      type: FieldType.string,
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=TAG_GROUP_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.collectEquipmentLov`).d('关联设备'),
      lovCode: 'APEX_MES.USER_EQUIP_WKC_REL',
      ignore: 'always',
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.collectMaterialLov`).d('关联物料'),
      lovCode: 'MT.MATERIAL',
      lovPara: { tenantId },
      ignore: 'always',
      textField: 'materialCode',
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'tagGroupBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupBusinessType`).d('收集组业务类型'),
      lookupCode: 'HME.TAG_GROUP_BUSINESS_TYPE',
    },
    {
      name: 'tagLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagLov`).d('数据项'),
      lovCode: 'MT.TAG',
      lovPara: { tenantId },
      ignore: 'always',
    },
    {
      name: 'tagId',
      bind: 'tagLov.tagId',
    },
  ],
  fields: [
    {
      name: 'tagGroupCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupCode`).d('数据收集组编码'),
    },
    {
      name: 'tagGroupDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupDescription`).d('数据收集组描述'),
    },
    {
      name: 'missingNcCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.missingNcCode`).d('缺失值不良代码'),
    },
    {
      name: 'status',
      type: FieldType.string,
      lookupCode: 'TAG_GROUP_STATUS',
      textField: 'description',
      valueField: 'statusCode',
      required: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=TAG_GROUP_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'tagGroupTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupType`).d('数据收集组类型'),
    },
    {
      name: 'businessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
    },
    {
      name: 'collectionTimeControlDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.collectionTimeControl`).d('数据收集时点'),
    },
    {
      name: 'defaultNcCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defaultNcCode`).d('默认不良代码'),
    },
    {
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      name: 'lastUpdateDate',
      type: FieldType.string,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag-group/list/ui`,
        method: 'get',
      };
    },
    submit: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag-group/batch/edit`,
        method: 'POST',
      };
    },
  },
});

const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'tagGroupCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.detailTagGroupCode`).d('收集组编码'),
      required: true,
      bind: 'tagGroupInfo.tagGroupCode',
    },
    {
      name: 'tagGroupDescription',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.detailTagGroupDescription`).d('收集组描述'),
      bind: 'tagGroupInfo.tagGroupDescription',
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      required: true,
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=TAG_GROUP_STATUS`,
      bind: 'tagGroupInfo.status',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteIds',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.siteIds`).d('站点'),
      required: true,
      options: siteOptionDs,
      textField: 'siteCode',
      valueField: 'siteId',
      bind: 'tagGroupInfo.siteIds',
      multiple: true,
    },
    {
      name: 'businessType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_GROUP_BUSINESS_TYPE`,
      bind: 'tagGroupInfo.businessType',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      // defaultValue: 'TIMING_DISTRIBUTION',
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      required: true,
    },
    {
      name: 'tagGroupType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_GROUP_TYPE`,
      bind: 'tagGroupInfo.tagGroupType',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.tagGroupTypeShort`).d('收集组类型'),
      required: true,
    },
    {
      name: 'sourceGroupLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceGroup`).d('源数据收集组'),
      lovCode: 'MT.TAG_GROUP',
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return record.get('tagGroupType') !== 'DISTRIBUTION';
        },
      },
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'sourceGroupId',
      type: FieldType.number,
      bind: 'tagGroupInfo.sourceGroupId',
    },
    {
      name: 'sourceGroupCode',
      type: FieldType.string,
      bind: 'tagGroupInfo.sourceGroupCode',
    },
    {
      name: 'collectionTimeControl',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_GROUP_COLLECTION_TIME`,
      bind: 'tagGroupInfo.collectionTimeControl',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      // defaultValue: 'TIMING_DISTRIBUTION',
      label: intl.get(`${modelPrompt}.collectionTimeControl`).d('数据收集时点'),
      required: true,
    },
    {
      name: 'missingNcCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.missingNcCodeLov`).d('缺失值不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'missingNcCodeId',
      type: FieldType.number,
      bind: 'missingNcCodeLov.ncCodeId',
    },
    {
      name: 'missingNcCode',
      type: FieldType.string,
      bind: 'missingNcCodeLov.ncCode',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag-group/detail/ui`,
        method: 'get',
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.mes.domain.entity.MtTagGroup';
      return {
        data: { tagGroupId: record.get('tagGroupInfo').tagGroupId },
        params: { fieldName, className },
        url: `${BASIC.HMES_BASIC}//v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
});

const dataItemTableDS = () => ({
  primaryKey: 'tagGroupAssignId',
  autoCreate: false,
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'serialNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
      required: true,
    },
    {
      name: 'tagLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagCode`).d('数据项编码'),
      lovCode: 'MT.TAG',
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'tagId',
      type: FieldType.number,
      bind: 'tagLov.tagId',
    },
    {
      name: 'tagCode',
      type: FieldType.string,
      bind: 'tagLov.tagCode',
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('数据项描述'),
      bind: 'tagLov.tagDescription',
      disabled: true,
    },
    {
      name: 'allowUpdateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.allowUpdateFlag`).d('是否允许更新'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'valueAllowMissing',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueAllowMissing`).d('允许缺失值'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      bind: 'tagLov.enableFlag',
      trueValue: 'Y',
      falseValue: 'N',
      disabled: true,
    },
    {
      name: 'displayValueFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.displayValueFlag`).d('是否显示标准值'),
      trueValue: 'Y',
      falseValue: 'N',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('tagLov');
        },
      },
    },
    { name: 'valueType', type: FieldType.string, bind: 'tagLov.valueType' },
    {
      name: 'valueTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueTypeDesc`).d('数据类型'),
      bind: 'tagLov.valueTypeDesc',
      disabled: true,
    },
    {
      name: 'collectionMethod',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_COLLECTION_METHOD`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
      label: intl.get(`${modelPrompt}.collectionMethod`).d('数据收集方式'),
      dynamicProps: {
        disabled({ record }) {
          return !record.get('tagLov');
        },
        required({ record }) {
          return record.get('tagLov') && record.get('tagLov').tagId;
        },
      },
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
      dynamicProps: {
        disabled({ record }) {
          return !record.get('tagLov') || record.get('valueType') !== 'DECISION_VALUE';
        },
        // required({ record }) {
        //   return record.get('valueType') === 'DECISION_VALUE';
        // },
      },
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
      dynamicProps: {
        disabled({ record }) {
          return !record.get('tagLov') || record.get('valueType') !== 'DECISION_VALUE';
        },
        // required({ record }) {
        //   return record.get('valueType') === 'DECISION_VALUE';
        // },
      },
    },
    {
      name: 'uomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.uomCode`).d('计量单位'),
      lovCode: 'MT.COMMON.UOM',
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('tagLov') || record.get('valueType') !== 'VALUE';
        },
      },
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'uomId',
      type: FieldType.number,
      bind: 'uomLov.uomId',
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      bind: 'uomLov.uomCode',
    },
    {
      name: 'valueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
      // multiple: ',',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('tagLov') || record.get('valueType') !== 'VALUE_LIST';
        },
        required({ record }) {
          return record.get('valueType') === 'VALUE_LIST';
        },
      },
    },
    {
      name: 'dateFormat',
      type: FieldType.string,
      lookupCode: 'MT.MES_DATE_FORMAT',
      textField: 'meaning',
      valueField: 'value',
      label: intl.get(`${modelPrompt}.dateFormat`).d('日期格式'),
    },
    {
      name: 'defaultNcCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defaultNcCodeLov`).d('默认不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !['DECISION_VALUE', 'VALUE'].includes(record.get('valueType'));
        },
      },
      textField: 'ncCode',
      valueField: 'ncCodeId',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'defaultNcCodeId',
      type: FieldType.number,
      bind: 'defaultNcCodeLov.ncCodeId',
    },
    {
      name: 'defaultNcCode',
      type: FieldType.string,
      bind: 'defaultNcCodeLov.ncCode',
      label: intl.get(`${modelPrompt}.defaultNcCodeLov`).d('默认不良代码'),
    },
    {
      name: 'specialRecordFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.specialRecordFlag`).d('特殊采集标识'),
    },
    {
      name: 'originOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.originOperationLov`).d('特殊参数来源工艺'),
      lovCode: 'MT.APS.OPERATION.URL',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'originOperationId',
      bind: 'originOperationLov.operationId',
    },
    {
      name: 'originOperationName',
      label: intl.get(`${modelPrompt}.originOperationName`).d('特殊参数来源工艺'),
      bind: 'originOperationLov.operationName',
    },
    {
      name: 'modifyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modifyFlag`).d('是否变更'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

const assObjectsDS = () => ({
  primaryKey: 'tagGroupObjectId',
  autoCreate: false,
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'lineNumber',
      label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
      type: FieldType.number,
    },
    {
      name: 'objectList',
      label: intl.get(`${modelPrompt}.assObjArray`).d('关联对象组合'),
      type: FieldType.object,
    },
  ],
});

const singleAssObjectDS = () => ({
  primaryKey: 'uuid',
  autoCreate: true,
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'parentSiteIds',
      type: FieldType.object,
    },
    {
      name: 'objectType',
      label: intl.get(`${modelPrompt}.objectType`).d('关联对象类型'),
      type: FieldType.string,
    },
    {
      name: 'objectLov',
      label: intl.get(`${modelPrompt}.objectCode`).d('关联对象编码'),
      type: FieldType.object,
      lovCode: '',
      required: true,
      dynamicProps: {
        lovCode: ({ record }) => {
          const objectType = record.get('objectType');
          return objectLovCode[objectType];
        },
        lovPara({ record }) {
          const queryPara = {
            tenantId,
          };
          const siteIds = record.get('parentSiteIds') || [];
          switch (record.get('objectType')) {
            case 'MT.WO_ROUTER':
            case 'BOM':
            case 'MATERIAL_CATEGORY':
            case 'MATERIAL':
              queryPara.siteIds = siteIds.join(',');
              break;
            case 'WORKCELL':
              queryPara.siteIds = siteIds.join(',');
              queryPara.userFlag = 'Y';
              break;
            case 'NC_CODE':
            case 'WORK_ORDER':
            case 'EO':
            case 'MATERIAL_LOT':
              queryPara.enableFlag = 'Y';
              if (siteIds.length > 1) {
                queryPara.siteId = 'null';
              } else if (siteIds.length === 1) {
                // eslint-disable-next-line prefer-destructuring
                queryPara.siteId = siteIds[0];
              }
              break;
            case 'MT.ROUTER_STEP':
              queryPara.routerId = record.get('selectedRouterId');
              break;
            case 'BOM_COMPONENT':
              queryPara.bomId = record.get('selectedBomId');
              break;
            default:
              // OPERATION   SUBSTEP
              break;
          }
          return queryPara;
        },
      },
    },
    {
      name: 'objectId',
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectIdBind[objectType];
        },
      },
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectCodeBind[objectType];
        },
      },
    },
    {
      name: 'objectDesc',
      label: intl.get(`${modelPrompt}.objectDesc`).d('关联对象描述'),
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectDescBind[objectType];
        },
      },
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'objectLov.revisionFlag',
    },
    {
      name: 'objectRevision',
      label: intl.get(`${modelPrompt}.objectRevision`).d('关联对象版本'),
      type: FieldType.string,
      required: false,
      textField: 'description',
      valueField: 'description',
      options: materialRevisionOptionDs,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('revisionFlag') === 'Y';
        },
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectRevisionBind[objectType];
        },
      },
    },
  ],
});

const copyDS = () => ({
  autoQuery: false,
  autoCreate: false,
  autoQueryAfterSubmit: false,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'tagGroupCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetTagGroupCode`).d('目标收集组编码'),
      required: true,
    },
    {
      name: 'tagGroupDescription',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.targetTagGroupDescription`).d('目标收集组描述'),
    },
  ],
  transport: {
    submit: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag-group/copy/ui`,
        method: 'POST',
        data: data[0],
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'tarzan.tag.domain.entity.MtTagGroup';
      return {
        data: { tagGroupId: record.data.tagGroupId },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_MODEL}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
});

const numberListDS = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  dataKey: 'rows',
  fields: [
    { name: 'dataValue' },
    {
      name: 'multipleValue',
      dynamicProps: {
        range: ({ record }) => {
          return record.get('valueType') === 'section' ? ['leftValue', 'rightValue'] : false;
        },
      },
    },
    {
      name: 'leftChar',
      type: FieldType.string,
      defaultValue: '[',
    },
    {
      name: 'leftValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.leftValue';
          }
        },
      },
    },
    {
      name: 'rightChar',
      type: FieldType.string,
      defaultValue: ']',
    },
    {
      name: 'rightValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.rightValue';
          }
        },
      },
    },
    {
      name: 'valueType',
      type: FieldType.string,
      defaultValue: 'single',
    },
    {
      name: 'valueShow',
      type: FieldType.string,
    },
    {
      name: 'standard',
      type: FieldType.string,
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeLov`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: 'always',
    },
    {
      name: 'ncCodeId',
      type: FieldType.string,
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      bind: 'ncCodeLov.ncCode',
    },
  ],
  events: {
    load: ({ dataSet }) => {
      if (!dataSet.length) {
        dataSet.loadData([{ leftChar: '(', rightChar: ')', valueType: 'single' }]);
        return;
      }
      dataSet.forEach(record => {
        if (record?.get('valueType') === 'section') {
          record?.set('multipleValue', {
            leftValue: record?.get('leftValue'),
            rightValue: record?.get('rightValue'),
          });
        } else {
          record?.set('multipleValue', record?.get('dataValue'));
        }
      });
    },
    update: ({ record, name }) => {
      switch (name) {
        case 'valueType':
        case 'leftValue':
        case 'rightValue':
        case 'leftChar':
        case 'rightChar':
        case 'multipleValue':
          handleUpdateRangeValue(record, name);
          break;
        default:
          break;
      }
    },
  },
});

const historicalQueryDS = () => ({
  autoCreate: false,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  queryFields: [
    {
      name: 'startDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.stratDate`).d('创建时间从'),
    },
    {
      name: 'endDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endDate`).d('创建时间至'),
    },
  ],
  fields: [
    {
      name: 'tagGroupCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupCode`).d('数据收集组编码'),
    },
    {
      name: 'tagGroupDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupDescription`).d('数据收集组描述'),
    },
    {
      name: 'serialNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
    },
    {
      name: 'objectList',
      label: intl.get(`${modelPrompt}.assObjArray`).d('关联对象组合'),
      type: FieldType.object,
    },
    {
      name: 'tagGroupType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupType`).d('数据收集组类型'),
    },
    {
      name: 'collectionMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.collectionMethod`).d('数据收集方式'),
      lookupCode: 'TAG_COLLECTION_METHOD',
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_COLLECTION_METHOD`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'valueAllowMissing',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueAllowMissing`).d('允许缺失值'),
      lookupCode: 'MT.FLAG',
    },
    {
      name: 'allowUpdateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.allowUpdateFlag`).d('是否允许更新字段'),
      lookupCode: 'MT.FLAG',
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
    },
    {
      name: 'displayValueFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.displayValueFlag`).d('是否显示标准值'),
      lookupCode: 'MT.FLAG',
    },
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'businessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      lookupCode: 'HME.TAG_GROUP_BUSINESS_TYPE',
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lookupCode: 'TAG_GROUP_STATUS',
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=TAG_GROUP_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'collectionTimeControl',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.collectionTimeControl`).d('数据收集时点'),
      lookupCode: 'TAG_GROUP_COLLECTION_TIME',
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_GROUP_COLLECTION_TIME`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'missingNcCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.missingNcCode`).d('缺失值对应的不良代码'),
    },
    {
      name: 'specialRecordFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialRecordFlag`).d('特殊采集标识'),
      lookupCode: 'MT.FLAG',
    },
    {
      name: 'originOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.originOperationName`).d('特殊参数来源工艺'),
    },
    {
      name: 'modifyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modifyFlag`).d('是否变更'),
    },
    {
      name: 'defaultNcCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defaultNcCode`).d('默认不良代码'),
    },
    {
      name: 'uomDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomDesc`).d('单位描述'),
    },
  ],
  transport: {
    read: ({ dataSet }) => {
      if (dataSet.getState('historicalKey') === 'homepage') {
        return {
          url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-tag-group-his/query/ui`,
          method: 'get',
        };
      } if (dataSet.getState('historicalKey') === 'dataItemInfo') {
        return {
          url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-tag-group-his/assign/query/ui`,
          method: 'get',
        };
      }
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-tag-group-his/object/query/ui`,
        method: 'get',
      }
    },
  },
})

const handleUpdateRangeValue = (record, name) => {
  if (record.get('valueType') === 'section') {
    if (!record.get('leftChar')) {
      record.set('leftChar', '(');
    }
    if (!record.get('rightChar')) {
      record.set('rightChar', ')');
    }
    const leftValue = record.get('leftValue') || '';
    const rightValue = record.get('rightValue') || '';
    const leftChar = record.get('leftChar') === '(' ? '<' : '≤';
    const rightChar = record.get('rightChar') === ')' ? '<' : '≤';
    record.set('valueShow', `${leftValue}${leftChar}X${rightChar}${rightValue}`);
  } else if (name === 'valueType') {
    record.set('multipleValue', undefined);
  }
};

export {
  tableDS,
  detailDS,
  dataItemTableDS,
  assObjectsDS,
  singleAssObjectDS,
  copyDS,
  siteOptionDs,
  materialRevisionOptionDs,
  numberListDS,
  historicalQueryDS,
};
