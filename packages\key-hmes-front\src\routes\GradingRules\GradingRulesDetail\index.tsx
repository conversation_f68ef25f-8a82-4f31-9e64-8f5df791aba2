
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  NumberField,
  Switch,
  TextField,
  Select,
  Table,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/board/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { detailDS, tableDS } from '../stores/DetailDS';
import { GetGradingRulesDetail, SaveGradingRules } from '../services';
import RangeField from './components/RangeField';

const { Panel } = Collapse;
const modelPrompt = 'modelPrompt_code';

const GradingRulesDetail = (props) => {
  const {
    history,
    match: { path, params },
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState(false);
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const { run: getGradingRulesDetail, loading: getLoading } = useRequest(GetGradingRulesDetail(), { manual: true, needPromise: true });
  const { run: saveGradingRules, loading: saveLoading } = useRequest(SaveGradingRules(), { manual: true, needPromise: true });

  useDataSetEvent(detailDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialLov':
        record.set('productionVersionLov', {});
        record.set('defaultBomLov', {});
        record.set('defaultRouterLov', {});
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    if (kid === 'create') {
      // 新建时
      setCanEdit(true);
      return;
    }
    getGradingRulesDetail({
      params: {
        gradingRuleId: kid,
      },
      onSuccess: (res) => {
        detailDs.loadData([res])
        tableDs.loadData(res.leavlList || [])
      },
    })
    // 编辑时
    // detailDs.setQueryParameter('kid', kid);
    // detailDs.query();
  }, [kid]);

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(
    () => {
      if (kid === 'create') {
        history.push('/hmes/grading-rules/list')
      } else {
        setCanEdit(false);
        detailDs.query();
      }
    },
    [],
  );

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    const validateFlag1 = await tableDs.validate();
    if (!validateFlag || !validateFlag1) {
      return false;
    }
    return saveGradingRules({
      params: {
        ...detailDs.current!.toData(),
        leavlList: tableDs.toData(),
      },
      onSuccess: (res) => {
        notification.success({});
        setCanEdit(false);
        if (kid === 'create') {
          history.push(`/hmes/grading-rules/detail/${res.gradingRuleId}`);
          return;
        }
        getGradingRulesDetail({
          params: {
            gradingRuleId: res.gradingRuleId,
          },
          onSuccess: (res) => {
            detailDs.loadData([res])
            tableDs.loadData(res.leavlList || [])
          },
        })
      },
    });
  }


  const columns: ColumnProps[] = [{
    header: (
      <Button
        icon="add"
        disabled={!canEdit}
        funcType={FuncType.flat}
        size={Size.small}
        onClick={() => tableDs.create({}, 0)}
      />
    ),
    name: 'editColumn',
    align: ColumnAlign.center,
    width: 70,
    renderer: ({ record }) => (
      <Button
        icon="remove"
        disabled={!canEdit}
        funcType={FuncType.flat}
        size={Size.small}
        onClick={() => { tableDs.remove(record!) }}
      />
    ),
  },
  {
    name: 'ruleLevel',
    editor: record => canEdit && <Select record={record} name="ruleLevel" />,
  },
  {
    name: 'datavalue',
    renderer: ({ value }) => {
      return value
    },
    editor: (record) => canEdit && (
      <RangeField
        name="datavalue"
        record={record}
        disabled={!canEdit}
      />
    ),
  }];

  return (
    <div className='hmes-style'>
      <TarzanSpin dataSet={detailDs} spinning={getLoading || saveLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.list`).d('分档规则维护')}
          backPath='/hmes/grading-rules/list'
        >
          {canEdit ? (
            <>
              <Button
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button
                icon="close"
                onClick={handleCancel}
              >
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'basic', 'range',
            ]}
            trigger="icon"
          >
            <Panel
              key="basic"
              header={intl.get(`${modelPrompt}.title.basic`).d('基础信息')}
            >
              <Form dataSet={detailDs} columns={4} disabled={!canEdit} labelWidth={112}>
                <Lov name="siteLov" />
                <TextField name="siteName" />
                <Lov name="materialLov" />
                <TextField name="materialName" />
                <Lov name="tagLov" />
                <TextField name="tagDescription" />
                <NumberField name="ruleSort" />
                <Switch name="enableFlag" />
              </Form>
            </Panel>
            <Panel
              key="range"
              header={intl.get(`${modelPrompt}.title.range`).d('参数范围')}
            >
              <Table
                dataSet={tableDs}
                columns={columns}
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  )
};


export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(GradingRulesDetail);
