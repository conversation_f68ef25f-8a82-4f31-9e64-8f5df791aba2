// import { Host } from '@/utils/config';
import { BASIC } from '@/utils/config';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';


// const Host = '/yp-mes-33202'
const Host = `${
  BASIC.HMES_BASIC
}`
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.barcodeMarkingBinding';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'identificationMarkingId',
    paging: true,
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'identificationLov',
        type: 'object',
        lovCode: 'HME.IDENTIFICATION_MARKING',
        required: true,
        ignore: 'always',
        label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      },
      {
        name: 'identificationId',
        bind: 'identificationLov.identificationId',
      },
      {
        name: 'identification',
        bind: 'identificationLov.identification',
      },
      {
        name: 'identificationType',
        bind: 'identificationLov.identificationType',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'identificationLov.materialName',
      },
      {
        name: 'markingCodeLov',
        type: 'object',
        lovCode: 'HME.MARKING_CODE',
        required: true,
        ignore: 'always',
        lovPara: { tenantId },
        label: intl.get(`${modelPrompt}.markingCodeLov`).d('标记编码'),
      },
      {
        name: 'markingCode',
        bind: 'markingCodeLov.markingCode',
      },
      {
        name: 'markingId',
        bind: 'markingCodeLov.markingId',
      },
      {
        name: 'statusMeaning',
        type: 'string',
        bind: 'markingCodeLov.statusMeaning',
        label: intl.get(`${modelPrompt}.statusMeaning`).d('标记状态'),
      },
      {
        name: 'sourceWayMeaning',
        type: 'string',
        defaultValue: '新建',
        label: intl.get(`${modelPrompt}.sourceWayMeaning`).d('标记来源'),
      },
      {
        name: 'sourceIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.sourceIdentification`).d('来源条码号'),
      },
      {
        name: 'sourceMaterialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.sourceMaterialName`).d('来源物料描述'),
      },
      {
        name: 'originalIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.originalIdentification`).d('首次来源条码标识'),
      },
      {
        name: 'originalMaterialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.originalMaterialName`).d('原始物料描述'),
      },
      {
        name: 'typeMeaning',
        bind: 'markingCodeLov.typeMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.typeMeaning`).d('标记类型'),
      },
      {
        name: 'markingContentMeaning',
        bind: 'markingCodeLov.markingContentMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingContentMeaning`).d('标记内容'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        required: true,
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
        bind: 'markingCodeLov.enableFlag',
        dynamicProps:{
          disabled:({record})=>{
            return record.get('enableFlag')==='Y'
          }
        },
      },
      {
        name: 'expirationDate',
        type: 'string',
        bind: 'markingCodeLov.expirationDate',
        label: intl.get(`${modelPrompt}.expirationDate`).d('有效期'),
      },
      {
        name: 'applyReason',
        bind: 'markingCodeLov.applyReason',
        type: 'string',
        label: intl.get(`${modelPrompt}.applyReason`).d('申请原因'),
      },
      {
        name: 'description',
        bind: 'markingCodeLov.description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('拦截工艺'),
      },
      {
        name: 'interceptionDisposalWay',
        type: 'string',
        bind: 'markingCodeLov.interceptionDisposalWay',
        label: intl.get(`${modelPrompt}.interceptionDisposalWay`).d('拦截处置方法'),
      },
      {
        name: 'disposalResult',
        type: 'string',
        bind: 'markingCodeLov.disposalResult',
        label: intl.get(`${modelPrompt}.disposalResult`).d('处置结果'),
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('操作人'),
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    queryFields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      },
      {
        name: 'markingCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingCode`).d('标记编码'),
      },
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('拦截工艺'),
        lovCode: 'HME.INTERCEPT_OPERATION_LOV',
        ignore: 'always',
        textField: 'value',
      },
      {
        name: 'operationName',
        bind: 'operationObj.value',
      },
      {
        name: 'type',
        type: 'string',
        label: intl.get(`${modelPrompt}.markType`).d('标记类型'),
        lookupCode: 'HME.MARKING_TYPE',
      },
      {
        name: 'traceLevelList',
        type: 'string',
        label: intl.get(`${modelPrompt}.traceLevel`).d('追溯层级'),
        lookupCode: 'HME.TRACE_LEVEL',
        multiple: true,
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'MT.ENABLE_FLAG',
        defaultValue: 'Y',
      },
      {
        name: 'sourceWay',
        type: 'string',
        label: intl.get(`${modelPrompt}.sourceWay`).d('标记来源'),
        lookupCode: 'HME.MARKING_SOURCE',
      },
      {
        name: 'sourceIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.sourceIdentification`).d('来源条码号'),
      },
      {
        name: 'originalIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.originalIdentification`).d('首次来源条码标识'),
      },
      {
        name: 'userLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.userName`).d('操作人'),
        lovCode: "MT.USER.ORG",
        lovPara: { tenantId },
        textField: 'realName',
      },
      {
        name: 'userId',
        bind: 'userLov.id',
      },
      {
        name: 'loginName',
        bind: 'userLov.loginName',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-identification-markings/query`,
          method: 'POST',
        };
      },
      submit: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-identification-markings/update`,
          method: 'POST',
        };
      },
    },
  };
};

const detailDS = () => {
  return {
    paging: false,
    autoQuery: false,
    selection: false,
    autoCreate: true,
    fields: [
      {
        name: 'siteLov',
        type: 'object',
        required: true,
        lovCode: 'MT.APS.SITE.URL',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'siteCode',
        bind: 'siteLov.siteCode',
      },
      {
        name: 'siteId',
        bind: 'siteLov.siteId',
      },
      {
        name: 'markingCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingCode`).d('标记编码'),
      },
      {
        name: 'status',
        type: 'string',
        defaultValue: 'NEW',
        label: intl.get(`${modelPrompt}.status`).d('标记状态'),
        lookupCode: 'HME.MARKING_STATUS',
      },
      {
        name: 'type',
        type: 'string',
        required: true,
        label: intl.get(`${modelPrompt}.markType`).d('标记类型'),
        lookupCode: 'HME.MARKING_TYPE',
      },
      {
        name: 'markingContent',
        required: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.markingContent`).d('标记内容'),
        lookupCode: 'HME.MARKING_CONTENT',
      },
      {
        name: 'enableFlag',
        required: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'HME.MARKING_ENABLE',
        defaultValue: 'VAILD',
      },{
        name: 'expirationDate',
        required: true,
        type: 'dateTime',
        format: 'YYYY-MM-DD HH:mm:ss',
        min: moment(),
        label: intl.get(`${modelPrompt}.expirationDate`).d('有效期'),
      },
      {
        name: 'applyReason',
        type: 'string',
        required: true,
        label: intl.get(`${modelPrompt}.applyReason`).d('申请原因'),
      },
      {
        name: 'applyBasis',
        type: 'attachment',
        required: true,
        label: intl.get(`${modelPrompt}.applyBasis`).d('申请依据'),
      },
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('拦截工艺'),
        lovCode: 'HME.INTERCEPT_OPERATION_LOV',
        ignore: 'always',
        textField: 'value',
        dynamicProps: {
          required: ({record}) => {
            return record?.get('type') === 'INTERCEPT'
          },
          disabled: ({record}) => {
            return record?.get('type') !== 'INTERCEPT'
          },
        },
      },
      {
        name: 'interceptionOperationName',
        bind: 'operationObj.value',
      },
      {
        name: 'interceptionDisposalWay',
        type: 'string',
        label: intl.get(`${modelPrompt}.interceptionDisposalWay`).d('拦截处置方法'),
        dynamicProps: {
          required: ({record}) => {
            return record?.get('type') === 'INTERCEPT'
          },
          disabled: ({record}) => {
            return record?.get('type') !== 'INTERCEPT'
          },
        },
      },
      {
        name: 'disposalResult',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalResult`).d('处置结果'),
      },
      {
        name: 'attachments',
        type: 'attachment',
        label: intl.get(`${modelPrompt}.attachments`).d('附件'),
      },
    ],
    events: {
      update: ({ record, name, value })  => {
        if(name === 'type'&&value !== 'INTERCEPT'){
          record?.set('operationObj', null)
          record?.set('interceptionDisposalWay', null)
        }
      },
    },
  }
}

export { tableDS, detailDS };
