/**
 * @Description: 问题横展平台-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-06-30 10:38:58
 * @LastEditTime: 2023-06-30 10:38:58
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import moment from 'moment';

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.transverseExpand';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

// 列表-ds
const listTableDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: false,
  cacheSelection: true,
  primaryKey: 'problemSpreadListId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: ({ data }) => {
      const {
        registDateFrom,
        registDateTo,
        ...others
      } = data;
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/list/query/for/ui`,
        method: 'get',
        data: {
          ...others,
          registDateFrom: registDateFrom ? moment(registDateFrom).format('YYYY-MM-DD') : undefined,
          registDateTo: registDateTo ? moment(registDateTo).format('YYYY-MM-DD') : undefined,
        },
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadListCode`).d('横展编号'),
      name: 'spreadListCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadStatus`).d('横展状态'),
      name: 'status',
      lookupCode: 'YP.QIS.PROBLEM_SPREAD_LIST_STATUS',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('横展来源'),
      name: 'createMethod',
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      lovPara: { tenantId },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceProblem`).d('问题编号'),
      name: 'sourceProblemLov',
      lovCode: 'SOURCE_PROBLEM_NUM',
    },
    {
      name: 'sourceProblemNum',
      bind: 'sourceProblemLov.sourceProblemNum',
    },
    {
      name: 'sourceProblemId',
      bind: 'sourceProblemLov.sourceProblemId',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('问题严重程度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registUser`).d('登记人'),
      name: 'registUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'registUserId',
      bind: 'registUserLov.id',
    },
    {
      name: 'registUserName',
      bind: 'registUserLov.realName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registUserDapartment`).d('登记部门'),
      name: 'registUserDapartmentLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'registUserDapartmentId',
      bind: 'registUserDapartmentLov.unitId',
    },
    {
      name: 'registUserDapartmentName',
      bind: 'registUserDapartmentLov.unitName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerSiteName`).d('触发横展站点名称'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerProdLineName`).d('触发横展产线名称'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transverseExpandLeadUser`).d('横展主导人'),
      name: 'leadUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'leadUserId',
      bind: 'leadUserLov.id',
    },
    {
      name: 'leadUserName',
      bind: 'leadUserLov.realName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.leadUserDapartment`).d('横展主导部门'),
      name: 'leadUserDapartmentLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'leadUserDapartmentId',
      bind: 'leadUserDapartmentLov.unitId',
    },
    {
      name: 'leadUserDapartmentName',
      bind: 'leadUserDapartmentLov.unitName',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.registDateFrom`).d('登记时间从'),
      name: 'registDateFrom',
      max: 'registDateTo',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.registDateTo`).d('登记时间至'),
      name: 'registDateTo',
      min: 'registDateFrom',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.followUpUserDapartment`).d('横展跟进人部门'),
      name: 'followUpUserDapartmentLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'followUpUserDapartmentId',
      bind: 'followUpUserDapartmentLov.unitId',
    },
    {
      name: 'followUpUserDapartmentName',
      bind: 'followUpUserDapartmentLov.unitName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transverseExpandFollowUpUser`).d('横展跟进人'),
      name: 'followUpUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'followUpUserId',
      bind: 'followUpUserLov.id',
    },
    {
      name: 'followUpUserName',
      bind: 'followUpUserLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productLevel`).d('产品层级'),
      name: 'productLevel',
      lookupCode: 'YP.QIS.PRODUCT_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadListType`).d('横展类型'),
      name: 'spreadListType',
      lookupCode: 'YP.QIS.SPREAD_LIST_TYPE',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadListCode`).d('横展编号'),
      name: 'spreadListCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadStatus`).d('横展状态'),
      name: 'status',
      lookupCode: 'YP.QIS.PROBLEM_SPREAD_LIST_STATUS',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadStatus`).d('横展状态'),
      name: 'statusDesc',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('横展来源'),
      name: 'createMethod',
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      lovPara: { tenantId },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethodDesc`).d('横展来源'),
      name: 'createMethodDesc',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceProblem`).d('问题编号'),
      name: 'sourceProblemNum',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('问题严重程度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registUser`).d('登记人'),
      name: 'registUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'registUserId',
      bind: 'registUserLov.id',
    },
    {
      name: 'registUserName',
      bind: 'registUserLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.registUserDapartment`).d('登记部门'),
      name: 'registUserDapartmentName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerSiteName`).d('触发横展站点名称'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.triggerSiteName`).d('触发横展站点名称'),
      bind: 'siteLov.siteName',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerProdLineName`).d('触发横展产线名称'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineName',
      label: intl.get(`${modelPrompt}.triggerProdLineName`).d('触发横展产线名称'),
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transverseExpandLeadUser`).d('横展主导人'),
      name: 'leadUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'leadUserId',
      bind: 'leadUserLov.id',
    },
    {
      name: 'leadUserName',
      label: intl.get(`${modelPrompt}.transverseExpandLeadUser`).d('横展主导人'),
      bind: 'leadUserLov.realName',
    },
    {
      name: 'leadUserDapartmentName',
      label: intl.get(`${modelPrompt}.leadUserDapartment`).d('横展主导部门'),
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.registDate`).d('登记时间'),
      name: 'registDate',
    },
    {
      name: 'followUpUserDapartmentName',
      label: intl.get(`${modelPrompt}.followUpUserDapartment`).d('横展跟进人部门'),
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transverseExpandFollowUpUser`).d('横展跟进人'),
      name: 'followUpUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'followUpUserId',
      bind: 'followUpUserLov.id',
    },
    {
      name: 'followUpUserName',
      label: intl.get(`${modelPrompt}.transverseExpandFollowUpUser`).d('横展跟进人'),
      bind: 'followUpUserLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productLevel`).d('产品层级'),
      name: 'productLevel',
      lookupCode: 'YP.QIS.PRODUCT_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadListType`).d('横展类型'),
      name: 'spreadListType',
      lookupCode: 'YP.QIS.SPREAD_LIST_TYPE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadListTypeDesc`).d('横展类型'),
      name: 'spreadListTypeDesc',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      readOnly: true,
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planStartDate`).d('计划开始时间'),
      name: 'planStartDate',
      max: 'planEndDate',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planEndDate`).d('计划结束时间'),
      name: 'planEndDate',
      min: 'planStartDate',
    },
  ],
});

// 详情-问题横展平台信息ds
const detailFormDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadListCode`).d('横展编号'),
      name: 'spreadListCode',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadStatus`).d('横展状态'),
      name: 'status',
      lookupCode: 'YP.QIS.PROBLEM_SPREAD_LIST_STATUS',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('横展来源'),
      name: 'createMethod',
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      lovPara: { tenantId },
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceProblem`).d('问题编号'),
      name: 'sourceProblemLov',
      lovCode: 'SOURCE_PROBLEM_NUM',
      required: true,
      computedProps: {
        disabled: ({ record }) => !record?.get('createMethod'),
        lovPara: ({ record }) => ({
          problemCategory: record?.get('createMethod'),
          tenantId,
        }),
      },
    },
    {
      name: 'sourceProblemNum',
      bind: 'sourceProblemLov.problemCode',
    },
    {
      name: 'sourceProblemId',
      bind: 'sourceProblemLov.problemId',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('问题严重程度'),
      disabled: true,
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
      bind: 'sourceProblemLov.severityLevel',
    },
    {
      name: 'severityLevelTab',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevelTab`).d('问题严重程度标记'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL_TAB',
      lovPara: { tenantId },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transverseExpandRegistUser`).d('横展登记人'),
      name: 'registUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
      disabled: true,
    },
    {
      name: 'registUserId',
      bind: 'registUserLov.id',
    },
    {
      name: 'registUserName',
      bind: 'registUserLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transverseExpandregistUserDapartment`).d('横展登记部门'),
      name: 'registUserDapartmentName',
      disabled: true,
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.transverseExpandRegistDate`).d('横展登记时间'),
      name: 'registDate',
      required: true,
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerSiteName`).d('触发横展站点名称'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.triggerProdLineName`).d('触发横展产线名称'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.leadUserDapartment`).d('横展主导部门'),
      name: 'leadUserDapartmentName',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transverseExpandLeadUser`).d('横展主导人'),
      name: 'leadUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'leadUserId',
      bind: 'leadUserLov.id',
    },
    {
      name: 'leadUserName',
      bind: 'leadUserLov.realName',
    },

    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productLevel`).d('产品层级'),
      name: 'productLevel',
      lookupCode: 'YP.QIS.PRODUCT_LEVEL',
      lovPara: { tenantId },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadListType`).d('横展类型'),
      name: 'spreadListType',
      lookupCode: 'YP.QIS.SPREAD_LIST_TYPE',
      computedProps: {
        required: ({ dataSet }) => {
          return (dataSet.getState('authList') || []).includes('DETAIL');
        },
      },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transverseExpandFollowUpUser`).d('横展跟进人'),
      name: 'followUpUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      computedProps: {
        required: ({ dataSet }) => {
          return (dataSet.getState('authList') || []).includes('DETAIL');
        },
      },
    },
    {
      name: 'followUpUserId',
      bind: 'followUpUserLov.id',
    },
    {
      name: 'followUpUserName',
      bind: 'followUpUserLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.followUpUserDapartment`).d('横展跟进人部门'),
      name: 'followUpUserDapartmentName',
      disabled: true,
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      name: 'enclosure',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spreadListContent`).d('横展内容'),
      name: 'spreadListContent',
      computedProps: {
        required: ({ dataSet }) => {
          return (dataSet.getState('authList') || []).includes('DETAIL');
        },
      },
      maxLength: 999,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.implementationStandard`).d('横展措施落地标准'),
      name: 'implementationStandard',
      computedProps: {
        required: ({ dataSet }) => {
          return (dataSet.getState('authList') || []).includes('DETAIL');
        },
      },
      maxLength: 999,
    },
  ],
});

// 详情-新增问题横展平台的DS
const detailTableDS = (formDs): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: DataSetSelection.multiple,
  fields: [
    {
      name: 'problemSpreadListId',
    },
    {
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: () => ['OPERATION', 'EQUIPMENT', 'CHECK'].includes(formDs.current?.get('spreadListType')),
      },
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processName`).d('工序名称'),
      name: 'processLov',
      ignore: FieldIgnore.always,
      lovCode: 'OPERATION_LIMIT_PROCESS_GET',
      textField: 'processName',
      lovPara: { tenantId },
      dynamicProps: {
        required: () => ['OPERATION', 'EQUIPMENT', 'CHECK'].includes(formDs.current?.get('spreadListType')),
      },
      multiple: true,
    },
    {
      name: 'processId',
      bind: 'processLov.processId',
    },
    {
      name: 'processDesc',
      bind: 'processLov.processName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      name: 'equipmentLov',
      ignore: FieldIgnore.always,
      lovCode: 'PROCESSID_LIMIT_EQUIPMENT_GET',
      textField: 'equipmentName',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: () => ['OPERATION', 'EQUIPMENT', 'CHECK'].includes(formDs.current?.get('spreadListType')),
      },
      multiple: true,
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'equipmentDesc',
      bind: 'equipmentLov.equipmentName',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: () => ['OPERATION', 'EQUIPMENT', 'CHECK'].includes(formDs.current?.get('spreadListType')),
        required: () => ['DESIGN', 'SUPPLIER'].includes(formDs.current?.get('spreadListType')),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibilityDepartment`).d('责任部门'),
      name: 'responsibilityDepartmentName',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibilityUser`).d('责任人'),
      name: 'responsibilityUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'responsibilityUserId',
      bind: 'responsibilityUserLov.id',
    },
    {
      name: 'responsibilityUserName',
      bind: 'responsibilityUserLov.realName',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planStartDate`).d('计划开始时间'),
      name: 'planStartDate',
      max: 'planEndDate',
      required: true,
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planEndDate`).d('计划完成时间'),
      name: 'planEndDate',
      min: 'planStartDate',
      required: true,
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际完成时间'),
      name: 'actualEndTime',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      name: 'status',
      lookupCode: 'YP.QIS.PROBLEM_SPREAD_TASK_STATUS',
      disabled: true,
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.evidence`).d('证据'),
      name: 'evidence',
      computedProps: {
        disabled: ({ dataSet, record }) => {
          return (
            (!(dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE') ||
            record.get('responsibilityUserId') !== dataSet.getState('userId')) ||
            !['TO_EXECUTE', 'AUDIT_REJECT', 'ASSESS_REJECT'].includes(record.get('status'))
          );
        },
        readOnly: ({ dataSet, record }) => {
          return (
            (!(dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE') ||
            record.get('responsibilityUserId') !== dataSet.getState('userId')) ||
            !['TO_EXECUTE', 'AUDIT_REJECT', 'ASSESS_REJECT'].includes(record.get('status'))
          );
        },
        required: ({ dataSet, record }) => {
          return (
            (dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE_REQUIRED') &&
            record.get('responsibilityUserId') !== dataSet.getState('userId')
          );
        },
      },
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      name: 'enclosure',
      computedProps: {
        disabled: ({ dataSet, record }) => {
          return (
            (!(dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE') ||
            record.get('responsibilityUserId') !== dataSet.getState('userId')) ||
            !['TO_EXECUTE', 'AUDIT_REJECT', 'ASSESS_REJECT'].includes(record.get('status'))
          );
        },
        readOnly: ({ dataSet, record }) => {
          return (
            (!(dataSet.getState('authList') || []).includes('TABLE_ENCLOSURE') ||
            record.get('responsibilityUserId') !== dataSet.getState('userId')) ||
            !['TO_EXECUTE', 'AUDIT_REJECT', 'ASSESS_REJECT'].includes(record.get('status'))
          );
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.result`).d('跟进人评价'),
      name: 'result',
      lookupCode: 'YP.QIS.PROBLEM_TASK_RESULT',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('取消理由'),
      name: 'reason',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remindFlag`).d('启用提醒'),
      name: 'remindFlag',
      lookupCode: 'MT.FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => !['NEW', 'AUDIT_REJECT', 'TO_EXECUTE'].includes(record.get('status')),
      },
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => {
        return record.get('selectAuth');
      },
    },
  },
});

// 详情-问题横展平台信息ds
const modalDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消理由'),
      name: 'cancelReason',
      computedProps: {
        required: ({ dataSet }) => {
          return dataSet.getState('modalType') === 'cancelReason';
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
      name: 'rejectReason',
      computedProps: {
        required: ({ dataSet }) => {
          return dataSet.getState('modalType') === 'rejectReason';
        },
      },
    },
  ],
});

export { listTableDS, detailFormDS, detailTableDS, modalDS };
