/**
 * @Description: 检验项目组维护-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 15:59:30
 * @LastEditTime: 2023-05-18 16:50:58
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

// 根据种别描述查询对应的校准信息
export function FetchVerificationDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/edit/query/ui`,
    method: 'GET',
  };
}

