/**
 * @Description: 模板打印按钮-与打印模板配置配套使用
 * @Author: <<EMAIL>>
 * @Date: 2023-07-31 16:13:12
 * @LastEditTime: 2023-08-12 17:09:14
 * @LastEditors: <<EMAIL>>
 */
import React, { useCallback, useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useModal, Radio, DataSet, Form, Select, Modal } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { uniq } from 'lodash';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { useRequest } from '../../tarzan-hooks';
import PrintElement from '../PrintButton/PrintElement';
import { FetchPrintTemplate, FetchTemplate, RecordPrintRecord, fetchReportTemplate, printPDFBlob, printDirect } from './services';
import noData from '@/assets/icons/noData.png'
import styles from './index.module.less';

const C7NModal = Modal;

const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

interface PrintButtonProps {
  ref?: any,
  style?: any,
  icon: string,
  // 打印按钮编码
  printButtonCode: string,
  disabled: boolean,
  // 权限按钮配置
  permissionList?: any[],
  // 打印时使用的参数
  printParams: {
    [key: string]: any,
    // 前端指令打印的时候，需要用数据替换打印指令模版字段
    printData: object[],
  }
  // 勾选的数据，筛选打印模板
  selectData: object[],
  // 记录打印次数时使用的参数，不传则不记录
  printRecordParams?: {
    printObjectIds: any,
    printObjectType: string,
  }
  // 打印按钮text
  name?: string,
  // 记录打印次数时的回调函数
  printCallback?: () => any,
  // 前端通过浏览器直接连接打印机，打印的方法，目前没有项目实现过这个功能后续增加
  onFrontPrintFunc: () => any,
}

interface PrintTemplateProps {
  customerId: undefined;
  supplierId: undefined;
  siteId: undefined;
  materialId: undefined;
  materialCategoryId: undefined;
  docCategory: undefined;
  docType: undefined;
  printerList: any;
  workcellId: any;
  parameter?: string[],
  printChannel: "FR" | 'H0' | 'REPORT' | 'BARTENDER' | 'PDF' | 'DIRECT' | 'INSTRUCT-FRONT' | 'INSTRUCT-JAVA',
  printChannelPath: string,
  // printChannelPath: "http://***********:8087/webroot",
  printTemplateCode: string,
  printTemplateUuid: string,
  // printTemplateCode: "STANDARD/materialLot.cpt",
  printTemplateGroup: string,
  printTemplateName: string,
}

interface PrintTemplateGroupProps {
  printTemplateGroup: string,
  printTemplateList: PrintTemplateProps[];
}

interface PrintTemplateResponse {
  description: any,
  printButtonCode: string,
  printTemplateGroupList?: (PrintTemplateGroupProps[]) | null,
}

const _initialData: PrintTemplateResponse = {
  description: null,
  printButtonCode: '',
  printTemplateGroupList: null,
}

const PrintButton = observer(forwardRef((props: PrintButtonProps, ref) => {
  const {
    style,
    printButtonCode,
    disabled,
    permissionList = [],
    printParams,
    selectData,
    printRecordParams,
    name,
    icon,
    printCallback,
    onFrontPrintFunc,
  } = props;

  const Modal = useModal();
  const modalRef = useRef<any>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<PrintTemplateProps>();
  const { data: printTemplate, error: templateError }: { data: PrintTemplateResponse, error: string | null }
    = useRequest(FetchPrintTemplate(printButtonCode), { initialData: _initialData });
  const fetchTemplate = useRequest(FetchTemplate(), { manual: true, needPromise: true });
  const recordPrintRecord = useRequest(RecordPrintRecord(), { manual: true, needPromise: true });

  useEffect(() => {
    if (!selectedTemplate || !modalRef.current) {
      return;
    }
    modalRef.current.update({
      okProps: { disabled: false },
      onOk: () => {
        return doPrint(selectedTemplate);
      },
    })
  }, [selectedTemplate])

  useImperativeHandle(
    ref,
    () => ({
      print: () => {
        handleClickPrintBtn();
      },
    }),
  )

  const handleClickPrintBtn = useCallback(() => {
    const _printTemplateGroupList = printTemplate?.printTemplateGroupList || [];
    if (_printTemplateGroupList.length === 1 && _printTemplateGroupList[0].printTemplateList.length === 1) {
      // 只有一条数据
      doPrint(_printTemplateGroupList[0].printTemplateList[0]);
      return;
    }
    setSelectedTemplate(undefined);
    let printTemplateGroupList = [] as any;
    if(selectData?.length){
    // 根据勾选的数据进行模板筛选(勾选数据条件一致，勾选的数据找模板，模板id为空或不为空和勾选数据id一致（找到模板id不为空和为空的，取不为空的）)
      if(selectData.some(e=>e?.data?.workcellId !==selectData[0]?.data?.workcellId ||
       e?.data?.customerId !==selectData[0]?.data?.customerId ||
       e?.data?.supplierId !==selectData[0]?.data?.supplierId ||
       e?.data?.siteId !==selectData[0]?.data?.siteId ||
       e?.data?.materialId !==selectData[0]?.data?.materialId||
       e?.data?.materialCategoryId !==selectData[0]?.data?.materialCategoryId ||
       e?.data?.docCategory !==selectData[0]?.data?.docCategory ||
       e?.data?.docType !==selectData[0]?.data?.docType,
      )){
        return;
      }
      const newPrintTemplateGroupList = [] as any;
      _printTemplateGroupList.forEach(templateGroup => {
        const tempPrintTemplateList =[];
        templateGroup.printTemplateList.forEach(item => {
          if(
            ([0, undefined, null].includes(item?.workcellId) || item?.workcellId === selectData[0]?.data?.workcellId)&&
            ([0, undefined, null].includes(item?.customerId) || item?.customerId === selectData[0]?.data?.customerId)&&
            ([0, undefined, null].includes(item?.supplierId) || item?.supplierId === selectData[0]?.data?.supplierId)&&
            ([0, undefined, null].includes(item?.siteId) || item?.siteId === selectData[0]?.data?.siteId) &&
            ([0, undefined, null].includes(item?.materialId) || item?.materialId === selectData[0]?.data?.materialId)&&
            ([0, undefined, null].includes(item?.materialCategoryId) || item?.materialCategoryId === selectData[0]?.data?.materialCategoryId)&&
            (['', undefined, null].includes(item?.docCategory) || item?.docCategory === selectData[0]?.data?.docCategory)&&
            (['', undefined, null].includes(item?.docType) || item?.docType === selectData[0]?.data?.docType)&&
            item.printChannel !== 'PDA'
          ){
            tempPrintTemplateList.push({...item});
          }
        })
        if(tempPrintTemplateList.length){
          newPrintTemplateGroupList.push({
            printTemplateGroup:templateGroup.printTemplateGroup,
            printTemplateList:tempPrintTemplateList,
          })
        }
      });
      printTemplateGroupList=newPrintTemplateGroupList;
    } else {
      printTemplateGroupList=_printTemplateGroupList;
    }
    // 根据勾选的数据进行模板筛选 end
    modalRef.current = Modal.open({
      title: intl.get('tarzan.common.button.printSelect').d('打印模板选择'),
      children: (
      // <div>
      //   {
      //     !!_printTemplateGroupList.length && (
      //       <Collapse collapsible="icon" bordered={false} defaultActiveKey={_printTemplateGroupList.map(item => item.printTemplateGroup)}>
      //         {
      //           _printTemplateGroupList.map(templateGroup => {
      //             // 接口返回的全部模板
      //             return (
      //               <Panel key={templateGroup.printTemplateGroup} header={templateGroup.printTemplateGroup}>
      //                 {
      //                 // 过滤掉便携式打印
      //                   templateGroup.printTemplateList.filter(e => e.printChannel !== "PDA").map((template) => {
      //                     return (
      //                       <Radio name="printTemplateRadio" mode="button" onChange={() => { setSelectedTemplate(template) }}>{template.printTemplateName}</Radio>
      //                     )
      //                   })
      //                 }
      //               </Panel>
      //             )
      //           })

        //         }
        //       </Collapse>
        //     )
        //   }
        //   {
        //     !_printTemplateGroupList.length && (
        //       <div className={styles['nodata-contariner']}>
        //         <img src={noData} alt='nodata' />
        //         <p>{intl.get('tarzan.common.info.noData').d('暂无数据')}</p>
        //       </div>
        //     )
        //   }
        // </div>
        <div>
          {
            !!printTemplateGroupList.length && (
              <Collapse collapsible="icon" bordered={false} defaultActiveKey={printTemplateGroupList.map(item => item.printTemplateGroup)}>
                {
                  printTemplateGroupList.map(templateGroup => {
                    // 接口返回的全部模板
                    return (
                      <Panel key={templateGroup.printTemplateGroup} header={templateGroup.printTemplateGroup}>
                        {
                        // 过滤掉便携式打印
                          templateGroup.printTemplateList.filter(e => e.printChannel !== "PDA").map((template) => {
                            return (
                              <Radio name="printTemplateRadio" mode="button" onChange={() => { setSelectedTemplate(template) }}>{template.printTemplateName}</Radio>
                            )
                          })
                        }
                      </Panel>
                    )
                  })

                }
              </Collapse>
            )
          }
          {
            !printTemplateGroupList.length && (
              <div className={styles['nodata-contariner']}>
                <img src={noData} alt='nodata' />
                <p>{intl.get('tarzan.common.info.noData').d('暂无数据')}</p>
              </div>
            )
          }
        </div>
      ),
      okText: intl.get('tarzan.common.button.print').d('打印'),
      okProps: { disabled: true },
      autoCenter: true,
      style: {
        width: 680,
      },
    });
  }, [Modal, printTemplate, printParams, selectData, printRecordParams]);

  const doPrint = useCallback(
    (template: PrintTemplateProps) => {
      switch (template.printChannel) {
        case 'FR':
          return doFRPrint(template);
        case 'REPORT':
          return doReportPrint(template);
        case 'H0':
          return doH0Print(template);
        case "BARTENDER":
          return doBartenderPrint(template);
        case "PDF":
          return doPDFPrint(template);
        case "DIRECT":
          return doDirectPrint(template);
        case "INSTRUCT-FRONT":
          return doInstructFrontPrint(template);
        case "INSTRUCT-JAVA":
          return doInstructJAVAPrint(template);
        default:
      }
    },
    [printParams, printRecordParams],
  );

  // 帆软打印
  const doFRPrint = useCallback(
    (template: PrintTemplateProps) => {
      // 初始化帆软
      return initFRPlug(template).then(() => {
        // 帆软打印
        FRPrint(template);
      });
    },
    [printParams, printRecordParams],
  )

  /**
   * 初始化帆软打印资源
   */
  const initFRPlug = useCallback(
    (template: PrintTemplateProps) => {
      return new Promise<void>((resolve, reject) => {
        try {
          // @ts-ignore
          if (window.FR) {
            // 已注册过帆软打印插件，不再重复注册
            resolve();
            return;
          }
          const jsUrlList = [
            `${template.printChannelPath}/decision/view/report?op=emb&resource=finereport.js`,
            `${template.printChannelPath}/decision/view/report?op=resource&resource=/com/fr/web/core/js/socket.io.js`,
            `${template.printChannelPath}/decision/view/report?op=resource&resource=/com/fr/web/core/js/jquery.watermark.js`,
          ];
          const cssUrl = `${template.printChannelPath}/decision/view/report?op=emb&resource=finereport.css`;
          for (let i = 0; i < jsUrlList.length; i++) {
            const element = jsUrlList[i];
            const script = document.createElement('script');
            script.src = element;
            script.async = true;
            document.body.appendChild(script);
          }
          const link = document.createElement('link');
          link.href = cssUrl;
          link.rel = 'stylesheet';
          link.type = 'text/css';
          document.body.appendChild(link);
          setTimeout(() => {
            resolve()
          }, 1000);
        } catch (error) {
          reject(error)
        }
      })
    },
    [],
  );

  const FRPrint = selectItem => {
    // @ts-ignore
    if (!window.FR) {
      notification.error({
        message: intl.get('tarzan.common.message.initFRPrintFailed').d('打印插件初始化失败'),
      });
      return;
    }
    const printurl = `${selectItem.printChannelPath}/decision/view/report`;

    delete printParams.printData;

    const _printParams = {
      reportlet: selectItem.printTemplateCode,
      ...printParams,
    };

    const reportlets = JSON.stringify([_printParams]);
    // @ts-ignore
    window.FR.doURLPrint({
      printUrl: printurl,
      isPopUp: false, // 是否弹出设置窗口 true/false 弹出/不弹出，零客户端打印不弹出，本地打印弹出
      data: {
        reportlets, // 需要打印的模板列表
      },
      printType: 0, // 打印类型，0为零客户端打印，1为本地打印，暂时默认客户端打印
      // 以下为零客户端打印的参数，仅当 printType 为 0 时生效
      ieQuietPrint: true, // IE静默打印设置 true为静默，false为不静默
      // 以下为本地打印的参数，仅当 printType 为 1 时生效
      printerName: 'Microsoft Print to PDF', // 打印机名
      pageType: 0, // 打印页码类型：0：所有页，1：当前页，2：指定页
      // pageIndex: '1-3', // 页码范围。当 pageType 为 2 时有效
      // copy: 1, // 打印份数
    });
    recordPrintTimes();
    setSelectedTemplate(undefined);
  };

  // 标签打印
  const doH0Print = useCallback(
    (template: PrintTemplateProps) => {
      return fetchTemplate.run({
        params: {
          tenantId,
          labelTemplateCode: template.printTemplateCode,
          labelTenantId: tenantId,
          ...printParams,
        },
      }).then(res => {
        if (res && !res?.failed && res?.label) {
          const pdfDiv = document.createElement('div');
          pdfDiv.id = 'hello';
          pdfDiv.style.paddingBottom = '20px';
          pdfDiv.style.overflow = 'overflow';
          pdfDiv.innerHTML = res.label.replace(/↵/gm, ''); // 去掉回车换行;
          PrintElement({
            content: pdfDiv,
          });
          setSelectedTemplate(undefined);
          recordPrintTimes();
          return true;
        }
        return false;
      })
    },
    [printParams, printRecordParams],
  )

  // 报表打印
  const doReportPrint = useCallback(
    async (template: PrintTemplateProps) => {
      console.log('printParams==', printParams);
      const params = {
        templateUuid: template.printTemplateUuid,
        ...printParams,
      };
      const res = await fetchReportTemplate(params);
      if (res && !res?.failed) {
        if (res.type === 'application/json') {
          const file = new FileReader();
          file.readAsText(res, 'utf-8');
          file.onload = () => {
            if (typeof file.result === 'string') {
              const message = JSON.parse(file.result);
              return notification.error({ message: message.message });
            }
          };
        } else {
          const file = new Blob([res], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(file);
          const newWindow = window.open(fileURL, 'newwindow');
          if (newWindow) {
            newWindow.print();
            setSelectedTemplate(undefined);
            recordPrintTimes();
            return true;
          }
          notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
          return false;
        }
      }
      return false;
    },
    [printParams, printRecordParams],
  )

  // bartender打印
  const doBartenderPrint = useCallback((template: PrintTemplateProps) => {
    return printDirect(template.printChannelPath, {
      printParams,
      printTemplateCode: template.printTemplateCode,
    }).then(res => {
      if (res.failed || res.success === false) {
        notification.warning({ description: res.message });
        return false;
      }
      modalRef.current.close();
    });
  }, []);

  // PDF打印
  const doPDFPrint = useCallback((template: PrintTemplateProps) => {
    return printPDFBlob(template.printChannelPath, {
      printParams,
      printTemplateCode: template.printTemplateCode,
      printerList: template.printerList,
    }).then(res => {
      if (res) {
        if (res.type === 'application/json') {
          const fileReader = new FileReader();
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result);
            // 普通对象，读取信息
            getResponse(jsonData);
          };
          fileReader.readAsText(res);
        } else {
          const file = new Blob([res], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(file);
          const newwindow = window.open(fileURL, 'newwindow');
          if (newwindow) {
            newwindow.print();
            notification.success({
              message: '打印成功',
            });
          } else {
            notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
          }
        }
      }
    });
  }, [printParams]);

  // 后端直连打印
  const doDirectPrint = useCallback((template: PrintTemplateProps) => {
    return handleCheckPrint(template, (printer) => {
      return printDirect(template.printChannelPath, {
        printParams,
        printTemplateCode: template.printTemplateCode,
        printer,
      }).then(res => {
        if (res.failed || res.success === false) {
          notification.warning({ description: res.message });
          return false;
        }
        modalRef.current.close();
      });
    });
  }, [printParams]);

  // 前端指令打印, 数据在前端已经查询出来，直接填充指令即可，将需要打印的数据放在printPrams.printData里，为数组
  const doInstructFrontPrint = useCallback((template: PrintTemplateProps) => {
    const { printData } = printParams;
    const { printInstruction } = template
    if(!printData || printData.length <= 0) {
      return notification.warning({ description: intl.get('tarzan.common.message.printNoSelect').d('当前没有需要打印的数据，请检查！')})
    }
    if(printInstruction && printInstruction.instructionContent) {
      const regStrArr = uniq([ ...printInstruction.instructionContent.matchAll('\\$\\{(.+?)\\}') ].map(e => e[0]));
      const printData =  printData.map(data => {
        let replacePrintCommand = "";
        regStrArr.forEach(e => {
          replacePrintCommand = printInstruction.instructionContent.replaceAll(e, data[e.slice(2, e.length - 1)])
        });
        return replacePrintCommand;
      });

      // 前端直联打印机的接口逻辑TODO
      onFrontPrintFunc(printData);

    } else {
      return notification.warning({ description: intl.get('tarzan.common.message.printNoInstruction').d('当前打印按钮没有维护指令模版，请检查！')})
    }

  }, [printParams]);

  // 后端指令打印
  const doInstructJAVAPrint = useCallback((template: PrintTemplateProps) => {
    return handleCheckPrint(template, (printer) => {
      return printDirect(template.printChannelPath, {
        printParams,
        printTemplateCode: template.printTemplateCode,
        printer,
      }).then(res => {
        if (res.failed || res.success === false) {
          notification.warning({ description: res.message });
          return false;
        }
        modalRef.current.close();
      });
    });
  }, []);

  const recordPrintTimes = () => {
    if (!printRecordParams) {
      return;
    }
    recordPrintRecord.run({
      params: {
        ...printRecordParams,
        printTimes: 1,
      },
      onSuccess: () => {
        if (printCallback) {
          printCallback()
        }
      },
    })
  }

  // 判断维护在系统的打印机数量
  const handleCheckPrint = useCallback((template, printFuncCallback) => {
    const { printerList } = template;
    if (printerList.length <= 0) {
      notification.warning({ description: intl.get('tarzan.common.message.printerNoSelect').d('当前系统未维护打印机，请检查！')})
      return null;
    }
    if(printerList.length === 1) {
      return printFuncCallback(printerList[0]);
    }
    if (printerList.length >= 1) {
      const printerSelectDs = new DataSet({
        fields: [{
          name: "printer",
          type: 'object',
          required: true,
          options: new DataSet({
            data: printerList,
          }),
        }],
      });
      const modalKey = C7NModal.key();
      C7NModal.open({
        key: modalKey,
        title: intl.get('tarzan.common.message.selectPrinter').d('请选择打印机'),
        className: 'hcmp-modal',
        destroyOnClose: false,
        drawer: false,
        closable: true,
        children: (
          <Form dataSet={printerSelectDs}>
            <Select name="printer"></Select>
          </Form>
        ),
        onOk: async () => {
          const flag = await printerSelectDs.validate();
          if(flag) {
            return printFuncCallback(printerSelectDs.current.get('printer'));
          }
          return false;
        },
      });
      return false;
    }
  }, [printTemplate]);

  return (
    <PermissionButton
      type="c7n-pro"
      disabled={disabled || templateError}
      permissionList={permissionList}
      onClick={handleClickPrintBtn}
      style={style}
      icon={icon}
    >
      {name || intl.get('tarzan.common.button.print').d('打印')}
    </PermissionButton>
  )
}))

export default PrintButton;
