import React, { useEffect, useState } from 'react';
import styles from './index.module.less';

const Table = ({ data, columns, title = '' }) => {
  const [columnDataMap, setColumnDataMap] = useState<any>({});
  const [columnNameList, setColumnNameList] = useState<any>([]);

  useEffect(() => {
    const _columnDataMap: any = {};
    const _columnNameList: any = [];
    (columns || []).forEach(item => {
      _columnDataMap[item.name] = item;
      _columnNameList.push(item.name);
    });
    setColumnDataMap(_columnDataMap);
    setColumnNameList(_columnNameList);
  }, [columns?.length]);

  return (
    <div className={styles['table-component-container']}>
      <div className={styles['table-title']}>{title}</div>
      <table className={styles['table-component']}>
        <thead>
          <tr className={`${styles['table-row']} ${styles['table-row-head']}`}>
            {(columnNameList || []).map(item => (
              <th key={`thead-${item}`} className={styles['table-cell']}>
                {columnDataMap[item].title}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {(data || []).map((rowItem, rowIndex) => (
            <tr className={styles['table-row']} key={`tbody-row-${String(rowIndex)}`}>
              {(columnNameList || []).map((colNameItem, colIndex) => (
                <td
                  className={styles['table-cell']}
                  key={`tbody-row-${String(rowIndex)}-${String(colIndex)}`}
                  id={`tbody-row-${String(rowIndex)}-${String(colIndex)}`}
                >
                  {rowItem[colNameItem]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default Table;
