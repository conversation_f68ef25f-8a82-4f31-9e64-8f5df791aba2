import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();
const endUrl = ``;

// 表格头信息
const TableHeadDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  data: [{
    msaCode: '111',
    inspectItemId: '1',
  }],
  queryFields: [
    {
      name: 'msaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaCode`).d('MSA编号'),
    },
    {
      name: 'msaStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaStatus`).d('状态'),
      lookupCode: 'YP.QIS.MSA_STATUS',
    },
    {
      name: 'msaType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaType`).d('MSA类型'),
      lookupCode: 'YP.QIS.MSA_TYPE',
    },
    {
      name: 'projectStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
      lookupCode: 'YP.QIS.PROJECT_STAGE',
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'toolModelIdObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toolModelIdObj`).d('量具型号'),
      lovCode: 'YP.QMS_TOOL_MODEL',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'toolModelId',
      type: FieldType.string,
      bind: 'toolModelIdObj.toolModelId',
    },
    {
      name: 'measureToolNumObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toolModelIdObj`).d('量具编码'),
      lovCode: 'YP.QIS.ALL_MT_TOOL',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'msToolManageId',
      bind: 'msToolManageLov.msToolManageId',
    },
    {
      name: 'measureToolNum',
      type: FieldType.string,
      bind: 'measureToolNumObj.toolCode',
    },
    {
      name: 'specialCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialCharacteristic`).d('特殊特性标识'),
      lookupCode: 'YP.QIS.SPECIAL_CHARACTERISTIC_FLAG',
    },
    {
      name: 'analyzedByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.analyzedByObj`).d('分析人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'analyzedBy',
      bind: 'analyzedByObj.userId',
    },
    {
      name: 'assistantByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.assistantByObj`).d('协助人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'assistantBy',
      bind: 'assistantByObj.userId',
    },
    {
      name: 'planStartTimeFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planStartTimeFrom`).d('预计开始时间从'),
      max: 'planStartTimeTo',
    },
    {
      name: 'planStartTimeTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planStartTimeTo`).d('预计开始时间至'),
      min: 'planStartTimeFrom',
    },
    {
      name: 'planEndTimeFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planEndTimeFrom`).d('预计结束时间从'),
      max: 'planEndTimeTo',
    },
    {
      name: 'planEndTimeTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planEndTimeTo`).d('预计结束时间至'),
      min: 'planEndTimeFrom',
    },
    {
      name: 'actualStartTimeFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.actualStartTimeFrom`).d('实际开始时间从'),
      max: 'actualStartTimeTo',
    },
    {
      name: 'actualStartTimeTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.actualStartTimeTo`).d('实际开始时间至'),
      min: 'actualStartTimeFrom',
    },
    {
      name: 'actualFinishTimeFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.actualFinishTimeFrom`).d('实际结束时间从'),
      max: 'actualFinishTimeTo',
    },
    {
      name: 'actualFinishTimeTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.actualFinishTimeTo`).d('实际结束时间至'),
      min: 'actualFinishTimeFrom',
    },
    {
      name: 'msaResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaResult`).d('分析结果'),
      lookupCode: 'YP.QIS.MSA_RESULT',
    },
    {
      name: 'sourceMsaTaskIdObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceMsaTaskIdObj`).d('来源MSA'),
      lovCode: 'YP.QIS.MSA_CODE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'sourceMsaTaskId',
      bind: 'sourceMsaTaskIdObj.sourceMsaTaskId',
    },
    {
      name: 'siteObj',
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      textField: 'siteName',
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteObj.siteId',
    },
  ],
  fields: [
    {
      name: 'msaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaCode`).d('MSA编号'),
    },
    {
      name: 'msaStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaStatus`).d('状态'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'msaType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaType`).d('MSA类型'),
    },
    {
      name: 'projectStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'specialCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialCharacteristic`).d('特殊特性标识'),
      lookupCode: 'YP.QIS.SPECIAL_CHARACTERISTIC_FLAG',
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('量具型号编码'),
    },
    {
      name: 'modelName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelName`).d('量具型号描述'),
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesName`).d('量具种别描述'),
    },
    {
      name: 'msaAnalysisMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaAnalysisMethod`).d('MSA分析方法'),
    },
    {
      name: 'toolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编号'),
    },
    {
      name: 'prodlineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodlineName`).d('产线'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序'),
    },
    {
      name: 'onlineFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onlineFlag`).d('是否在线'),
    },
    {
      name: 'analyzedName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analyzedName`).d('分析人'),
    },
    {
      name: 'assistantName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assistantName`).d('协助人'),
    },
    {
      name: 'completeTimeLimit',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completeTimeLimit`).d('完成时限'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('预计开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('预计结束时间'),
    },
    {
      name: 'actualStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('实际开始时间'),
    },
    {
      name: 'actualFinishTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualFinishTime`).d('实际结束时间'),
    },
    {
      name: 'msaResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaResult`).d('分析结果'),
    },
    {
      name: 'sourceMsaTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceMsaTaskCode`).d('来源MSA'),
    },
    {
      name: 'improveName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.improveName`).d('改善负责人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'creationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationName`).d('创建人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/a`,
        method: 'GET',
      };
    },
  },
});

// 表格行信息
const TableLineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'msaAnalysisMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaAnalysisMethod`).d('MSA分析方法'),
    },
    {
      name: 'msaResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaResult`).d('分析结果'),
    },
    {
      name: 'msaConclusion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaConclusion`).d('分析结论'),
    },
    {
      name: 'analyzeDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analyzeDate`).d('分析时间'),
    },
    {
      name: 'analyzeStatus',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.analyzeStatus`).d('状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/b`,
        method: 'GET',
      };
    },
  },
});

export { TableHeadDS, TableLineDS };
