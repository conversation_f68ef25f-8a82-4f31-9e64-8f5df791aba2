/*
 * @Description: 质量反馈单-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-13 10:45:27
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-12 13:44:50
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.qualityFeedbackPlatform';
const tenantId = getCurrentOrganizationId();
const endUrl = "";

const headDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: true,
  selection: DataSetSelection.multiple,
  dataKey: 'content', // 列表数据在接口返回json中的相对路径
  totalKey: 'totalElements',
  primaryKey: 'feedbackId', // 表格唯一性主键
  queryFields: [
    {
      name: 'feedbackNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.feedbackNum`).d('质量反馈单编号'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      textField: 'siteName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'theme',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.theme`).d('主题'),
    },
    {
      name: 'warnNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnNum`).d('告警单编号'),
    },
    {
      name: 'serviceNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceNum`).d('维修工单编号'),
    },
    {
      name: 'claimNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.claimNum`).d('质保索赔单编号'),
    },
    {
      name: 'dataSource',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataSource`).d('数据来源'),
    },
    {
      name: 'batteryNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryNum`).d('电池包编码'),
    },
    {
      name: 'batteryModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryModel`).d('电池包型号'),
    },
    {
      name: 'hostPlantLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.hostPlant`).d('主机厂'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
    },
    {
      name: 'hostPlant',
      bind: 'hostPlantLov.customerId',
    },
    {
      name: 'vinNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vinNum`).d('VIN号'),
    },
    {
      name: 'vehicleModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('车型'),
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      lovPara: { tenantId },
    },
    {
      name: 'productionStartDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionStartDate`).d('车辆生产时间从'),
      max: 'productionEndDate',
    },
    {
      name: 'productionEndDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionEndDate`).d('车辆生产时间至'),
      min: 'productionStartDate',
    },
    {
      name: 'soldStartDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.soldStartDate`).d('销售时间从'),
      max: 'soldEndDate',
    },
    {
      name: 'soldEndDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.soldEndDate`).d('销售时间至'),
      min: 'soldStartDate',
    },
    {
      name: 'faultStartDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.faultStartDate`).d('故障时间从'),
      max: 'faultEndDate',
    },
    {
      name: 'faultEndDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.faultEndDate`).d('故障时间至'),
      min: 'faultStartDate',
    },
    {
      name: 'maintainStartDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.maintainStartDate`).d('维修时间从'),
      max: 'maintainEndDate',
    },
    {
      name: 'maintainEndDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.maintainEndDate`).d('维修时间至'),
      min: 'maintainStartDate',
    },
    {
      name: 'faultMileage',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.faultMileage`).d('故障里程'),
    },
    {
      name: 'serviceSitusNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceSitusNum`).d('维修网点编号'),
    },
    {
      name: 'serviceSitusName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceSitusName`).d('维修网点名称'),
    },
    {
      name: 'shopNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shopNum`).d('4S店编号'),
    },
    {
      name: 'shopName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shopName`).d('4S店名称'),
    },
    {
      name: 'faultLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultLevel`).d('故障等级'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QA_FEEDBACK_FAULT_LEVEL',
    },
    {
      name: 'creationStartDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationStartDate`).d('记录时间从'),
      max: 'creationEndDate',
    },
    {
      name: 'creationEndDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationEndDate`).d('记录时间至'),
      min: 'creationStartDate',
    },
    {
      name: 'createdLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'createdBy',
      bind: 'createdLov.id',
    },
  ],
  fields: [
    {
      name: 'feedbackNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.feedbackNum`).d('质量反馈单编号'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'theme',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.theme`).d('主题'),
    },
    {
      name: 'warnNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnNum`).d('告警单编号'),
    },
    {
      name: 'serviceNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceNum`).d('维修工单编号'),
    },
    {
      name: 'claimNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.claimNum`).d('质保索赔单编号'),
    },
    {
      name: 'dataSource',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataSource`).d('数据来源'),
    },
    {
      name: 'batteryNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryNum`).d('电池包编码'),
    },
    {
      name: 'batteryModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryModel`).d('电池包型号'),
    },
    {
      name: 'hostPlantName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.hostPlant`).d('主机厂'),
    },
    {
      name: 'vinNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vinNum`).d('VIN号'),
    },
    {
      name: 'vehicleModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('车型'),
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      lovPara: { tenantId },
    },
    {
      name: 'productionDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionDateFrom`).d('车辆生产时间'),
    },
    {
      name: 'soldDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soldDate`).d('销售时间'),
    },
    {
      name: 'faultDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultDate`).d('故障时间'),
    },
    {
      name: 'maintainDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainDate`).d('维修时间'),
    },
    {
      name: 'faultMileage',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.faultMileage`).d('故障里程'),
    },
    {
      name: 'serviceSitusNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceSitusNum`).d('维修网点编号'),
    },
    {
      name: 'serviceSitusName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceSitusName`).d('维修网点名称'),
    },
    {
      name: 'shopNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shopNum`).d('4S店编号'),
    },
    {
      name: 'shopName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shopName`).d('4S店名称'),
    },
    {
      name: 'faultDec',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultDec`).d('故障描述'),
    },
    {
      name: 'faultReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultReason`).d('故障原因'),
    },
    {
      name: 'faultMeasure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultMeasure`).d('维修措施'),
    },
    {
      name: 'faultLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultLevel`).d('故障等级'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QA_FEEDBACK_FAULT_LEVEL',
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('记录时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'operation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operation`).d('操作'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-qa-feedbacks/query`,
        method: 'GET',
      };
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content', // 列表数据在接口返回json中的相对路径
  totalKey: 'rows.totalElements',
  primaryKey: 'feedbackItemId', // 表格唯一性主键
  fields: [
    {
      name: 'feedbackItemNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.feedbackItemNum`).d('序号'),
    },
    {
      name: 'partsCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partsCode`).d('零部件编号'),
    },
    {
      name: 'partsName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partsName`).d('零部件名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('因湃零部件编号'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('因湃零部件名称'),
    },
    {
      name: 'primaryUnitFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUnitFlag`).d('主因件'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.quantity`).d('数量'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-qa-feedbacks/detail`,
        method: 'GET',
      };
    },
  },
});

const maintainItemDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content', // 列表数据在接口返回json中的相对路径
  totalKey: 'rows.totalElements',
  primaryKey: 'feedbackProjectId', // 表格唯一性主键
  fields: [
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'maintainItems',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainItems`).d('维修项目'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-qa-feedback-projects/detail`,
        method: 'GET',
      };
    },
  },
});

export { headDS, lineDS, maintainItemDS };