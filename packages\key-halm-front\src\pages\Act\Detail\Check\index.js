// 标准作业物料、维保计划物料共用
import React, { Component } from 'react';
import { DataSet, Button, Table, Modal } from 'choerodon-ui/pro';
import { Collapse, Spin, Icon, Tag } from 'choerodon-ui';
import { observer } from 'mobx-react';
import notification from 'utils/notification';
import request from 'utils/request';
import { Bind } from 'lodash-decorators';
import { getCurrentOrganizationId } from 'utils/utils';
import classNames from 'classnames';
import { HALM_MTC } from 'alm/utils/config';
import { omit } from 'lodash';

import ChecklistEditModal from 'alm/components/ChecklistEditModal';
import { saveOptions } from 'alm//components/ChecklistEditModal/api';
import getLangs from '../../Langs';
import { actTableDs, woopTableDs } from '../../Stores/checkDs';

import styles from './index.module.less';

const organizationId = getCurrentOrganizationId();

const saveUrl = `${HALM_MTC}/v1/${organizationId}/act-checklists`;
const queryChildUrl = `${HALM_MTC}/v1/${organizationId}/act-checklists/list/c7n-tree`;

@observer
export default class Check extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.actTableDs = new DataSet(actTableDs(this.props.headerTypeCode)); // 标准作业检查项
    this.woopTableDs = new DataSet(woopTableDs(this.props.headerTypeCode)); // 任务检查项

    this.editModalRef = React.createRef();
  }

  componentDidMount() {
    const { headerId, headerTypeCode } = this.props;
    this.actTableDs.setQueryParameter('headerId', headerId);
    this.actTableDs.setQueryParameter('headerTypeCode', headerTypeCode);
    this.actTableDs.setQueryParameter('parentId', headerId);
    this.actTableDs.query();
    this.woopTableDs.setQueryParameter('headerId', headerId);
    this.woopTableDs.setQueryParameter('headerTypeCode', headerTypeCode);
    this.woopTableDs.query();

    if (this.props.onRef) {
      this.props.onRef(this);
    }
  }

  @Bind
  refresh() {
    this.actTableDs.query();
    this.woopTableDs.query();
  }

  // 点击展开
  @Bind
  async handleExpand(expanded, record, type) {
    // 判断节点是否异步加载子结点
    if (expanded && record.get('childFlag') && !record.children) {
      record.setState('loadding', true);
      request(queryChildUrl, {
        method: 'GET',
        query: {
          parentChecklistId: record.get('checklistId'),
        },
      }).then(res => {
        const recordsChildren = res.content;

        if (type === 'ACT') {
          this.actTableDs.data = [...this.actTableDs.toData(), ...recordsChildren];
        } else if (type === 'ACT_OP') {
          this.woopTableDs.data = [...this.woopTableDs.toData(), ...recordsChildren];
        }
        record.setState('loadding', false);
      });
    }
  }

  // icon 渲染问题， 首先判断record的值和自定义状态来判断出叶节点和父节点进行不同的渲染
  expandicon({ prefixCls, expanded, expandable, record, onExpand }) {
    if (!record.get('childFlag')) {
      // 子结点渲染
      return <span style={{ paddingLeft: '0.18rem' }} />;
    }
    if (record.getState('loadding') === true) {
      // 自定义状态渲染
      return <Spin tip="loding" delay={200} size="small" />;
    }
    const iconPrefixCls = `${prefixCls}-expand-icon`;
    const classString = classNames(iconPrefixCls, {
      [`${iconPrefixCls}-expanded`]: expanded,
    });
    return (
      <Icon
        type="baseline-arrow_right"
        className={classString}
        onClick={onExpand}
        tabIndex={expandable ? 0 : -1}
      />
    );
  }

  @Bind
  handleExpandActChecklist(expanded, record) {
    this.handleExpand(expanded, record, 'ACT');
  }

  @Bind
  handleExpandWoopChecklist(expanded, record) {
    this.handleExpand(expanded, record, 'ACT_OP');
  }

  @Bind
  handleDelete(record, type = 'ACT') {
    Modal.confirm({
      title: getLangs('TITLE_DELETE_CONFIRM'),
      children: getLangs('DELETE_CONFIRM'),
      onOk: () => {
        request(`${HALM_MTC}/v1/${organizationId}/act-checklists`, {
          method: 'DELETE',
          body: { ...record, tenantId: organizationId },
        }).then(res => {
          if (res && !res.failed) {
            notification.success();
            if (type === 'ACT') {
              this.actTableDs.query();
            } else if (type === 'ACT_OP') {
              this.woopTableDs.query();
            }
          } else {
            notification.warning({ message: res.message });
          }
        });
      },
    });
  }

  /**
   * 编辑
   */
  @Bind()
  handleEdit(record) {
    this.openEditModal(false, true, record);
  }

  /**
   * 查看
   */
  @Bind()
  handleView(record) {
    this.openEditModal(false, false, record);
  }

  @Bind
  handleCreate() {
    const { headerId, header, headerTypeCode } = this.props;
    this.openEditModal(true, false, {
      parentTypeCode: headerTypeCode, // 标准作业
      parentId: headerId,
      parentName: headerTypeCode === 'ACT' ? header.actName : header.maintainPlanName,
    });
  }

  // 打开 检查项编辑/查看modal
  @Bind
  openEditModal(isNew, isEdit = false, currentData = {}) {
    const { headerId, header, headerTypeCode } = this.props;

    const modalProps = {
      isNew,
      editFlag: isEdit,
      compParentId: headerId,
      compParentName: headerTypeCode === 'ACT' ? header.actName : header.maintainPlanName,
      compParentType: headerTypeCode,
      tenantId: organizationId,
      dataSource: currentData,
      maintSiteIds: [header.maintSiteId],
    };

    this._editModal = Modal.open({
      key: 'editModal',
      destroyOnClose: true,
      maskClosable: true,
      closable: true,
      drawer: true,
      style: {
        width: 700,
      },
      title: getLangs('TITLE_CHECK'),
      children: <ChecklistEditModal {...modalProps} ref={this.editModalRef} />,
      footer: this.getFooter(isNew, isEdit),
    });
  }

  getFooter(isNew, editFlag) {
    return isNew || editFlag
      ? [
        <Button key="cancel" onClick={this.handleCancel}>
          {getLangs('CANCEL')}
        </Button>,
        <Button key="submit" color="primary" onClick={this.handleSave}>
          {getLangs('SAVE')}
        </Button>,
        ]
      : [
        <Button key="back" onClick={this.handleCancel}>
          {getLangs('BACK')}
        </Button>,
        ];
  }

  @Bind
  handleCancel() {
    this._editModal.close();
  }

  /**
   * 保存检查项
   */
  @Bind()
  async handleSave() {
    const result = await this.editModalRef?.current?.detailDs.current.validate(true);
    if (result) {
      const { headerId, headerTypeCode } = this.props;
      const detail = this.editModalRef.current.detailDs.current.toData();
      const trueValue = this.editModalRef.current.trueNumberDs.toData();
      const alarmFlag = detail.isThereAlarm
      const { columnTypeCode, listValueCode } = detail;
      const isOk = columnTypeCode === 'LISTOFVALUE' ? true : await this.editModalRef?.current?.optionsTableDs.validate();
      const optionsList = this.editModalRef?.current?.optionsTableDs?.records?.map(i => i.toData());

      request(saveUrl, {
        method: 'POST',
        body: {
          ...omit(detail, 'parentName'),
          ...omit(detail, 'standardReference'),
          standardReferenceList: trueValue,
          tenantId: organizationId,
          headerId,
          headerTypeCode,
        },
      }).then(async res => {
        if (res && !res.failed) {
          if (columnTypeCode === 'YESORNO' || (columnTypeCode === 'LISTOFVALUE' && listValueCode) ||  alarmFlag!== null || alarmFlag!== undefined) {
            if (isOk || alarmFlag!== null || alarmFlag!== undefined) {
              saveOptions({
                list: alarmFlag && columnTypeCode === 'VALUE' ? [{alarmFlag}] : optionsList,
                checklistId: res.checklistId,
                compParentType: headerTypeCode,
              }).then(res1 => {
                if (res1 && !res1.failed) {
                  notification.success();
                  // 刷新列表
                  this.actTableDs.query();
                  this.woopTableDs.query();
                  this._editModal.close();
                } else {
                  notification.warning({
                    message: res1.message,
                  });
                }
              });
            }
          } else {
            notification.success();
            // 刷新列表
            this.actTableDs.query();
            this.woopTableDs.query();
            this._editModal.close();
          }
        } else {
          notification.warning({
            message: res.message,
          });
        }
      });
    }
  }

  getCols(type = 'act') {
    const { headerTypeCode } = this.props;
    const commonCols = [
      {
        name: 'checklistName',
      },
      {
        name: 'businessScenarioMeaning',
      },
      {
        name: 'methodCode',
        renderer: ({ value }) => value ?? '-',
      },
      {
        name: 'standardReference',
        renderer: ({ value }) => {
          if (value) {
            const data = JSON.parse(value);
            return data.map(item => (<Tag>{item}</Tag>))
          }
          return '-';
        },
      },
      {
        name: 'columnTypeMeaning',
        width: 150,
      },
    ];
    const woopCols =
      headerTypeCode === 'ACT'
        ? [
            {
              name: 'activityOpNumber',
              width: 80,
            },
            {
              name: 'parentName',
            },
          ]
        : [
            {
              name: 'activityOpNumber',
              width: 80,
            },
          ];
    return type === 'act'
      ? commonCols.concat([
          {
            header: getLangs('OPTION'),
            width: 110,
            renderer: ({ record }) => {
              const data = record.toData();
              return (
                <span className="action-link">
                  <a onClick={() => this.handleEdit(data)}>{getLangs('EDIT')}</a>
                  <a onClick={() => this.handleDelete(data, 'ACT')}>{getLangs('DELETE')}</a>
                </span>
              );
            },
          },
        ])
      : woopCols.concat(commonCols).concat([
          {
            header: getLangs('OPTION'),
            width: 110,
            renderer: ({ record }) => {
              const data = record.toData();
              return (
                <span className="action-link">
                  <a onClick={() => this.handleEdit(data)} style={{ marginRight: 8 }}>
                    {getLangs('EDIT')}
                  </a>
                  <a onClick={() => this.handleDelete(data, 'ACT_OP')}>{getLangs('DELETE')}</a>
                </span>
              );
            },
          },
        ]);
  }

  get actColumns() {
    return this.getCols();
  }

  get woopColumns() {
    return this.getCols('woop');
  }

  render() {
    const { headerTypeCode } = this.props;
    const isAct = headerTypeCode === 'ACT';
    return (
      <>
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button icon="add" onClick={this.handleCreate}>
            {getLangs('CREATE')}
          </Button>
        </div>
        <Collapse bordered={false} defaultActiveKey={['A', 'B']}>
          <Collapse.Panel key="A" header={getLangs('RECEIPT_CHECK')}>
            <Table
              key="actCheckList"
              customizedCode="AORI.ACT.CHECK_LIST"
              dataSet={this.actTableDs}
              columns={this.actColumns}
              mode="tree"
              className={styles['halm-tree-table']}
              expandIcon={this.expandicon}
              onExpand={this.handleExpandActChecklist}
              expandedRowRenderer={() => false}
            />
          </Collapse.Panel>
          <Collapse.Panel key="B" header={getLangs('PANEL_WOOP_CHECK')}>
            <Table
              key="actWoopCheckList"
              customizedCode="AORI.ACT.WOOP_CHECK_LIST"
              dataSet={this.woopTableDs}
              columns={this.woopColumns}
              mode="tree"
              queryBarProps={{
                fuzzyQuery: false,
              }}
              queryBar={isAct ? 'filterBar' : 'none'}
              className={styles['halm-tree-table']}
              expandIcon={this.expandicon}
              onExpand={this.handleExpandWoopChecklist}
              expandedRowRenderer={() => false}
              expandIconColumnIndex={isAct ? 2 : 1}
            />
          </Collapse.Panel>
        </Collapse>
      </>
    );
  }
}
