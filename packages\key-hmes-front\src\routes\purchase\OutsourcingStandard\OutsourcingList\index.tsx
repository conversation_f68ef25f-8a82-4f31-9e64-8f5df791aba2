/**
 * @Description: 外协管理平台
 * @Author: <<EMAIL>>
 * @Date: 2022-01-13 14:36:48
 * @LastEditTime: 2023-05-30 09:53:34
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { DataSet, Table, Modal, Button, Dropdown } from 'choerodon-ui/pro';
import { Menu, Badge, Collapse } from 'choerodon-ui';
// import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { Action } from 'choerodon-ui/pro/lib/trigger/enum';
import {
  ColumnAlign,
  TableColumnTooltip,
  ColumnLock,
  TableQueryBarType,
} from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import notification from 'utils/notification';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { getResponse } from '@utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC, API_HOST } from '@utils/config';
import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import { OutsourcingList, LineDs, DetailDs } from '../stores/OutsourcingListDS';
import styles from './index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.hmes.purchase.outsourcingManage';

let modalBarCode;

// 校验是否可以创建补料单
export function SupplementCheck() {
  return {
    url: `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wms-out-source/out-source/Init/ui`,
    method: 'GET',
  };
}

const PurchaseList = props => {
  const {
    purchaseListDS,
    lineDs,
    detailDs,
    match: { path },
    customizeTable,
  } = props;

  // 判断头搜索条件切换
  const [instructionDocType, setInstructionDocType] = useState();

  const [tableChoice, setTableChoice] = useState(''); // 头表格选择的条数

  const [printIds, setPrintIds] = useState<any>([]); // 头表格选择的id

  const [selectedStatus, setSelectedStatus] = useState('');

  const supplementCheck = useRequest(SupplementCheck(), {
    manual: true,
  });

  // 创建成功返回页面时刷新页面
  useEffect(() => {
    if (props?.location?.state?.queryFlag && props?.location?.state?.queryFlag === true) {
      setTableChoice('');
      setPrintIds([]);
      purchaseListDS.batchUnSelect(purchaseListDS.selected);
      purchaseListDS.clearCachedSelected();
      setSelectedStatus('');
      purchaseListDS.query(props.purchaseListDS.currentPage);
    } else {
      handleTableChange();
    }
  }, []);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  useEffect(() => {
    function processDataSetListener(flag) {
      if (props.purchaseListDS.queryDataSet) {
        const handler = flag
          ? props.purchaseListDS.queryDataSet.addEventListener
          : props.purchaseListDS.queryDataSet.removeEventListener;
        handler.call(props.purchaseListDS.queryDataSet, 'update', handleQueryDataSetUpdate);
      }
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  // 如果供应商改变，那么供应商地点编码清空
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (!data.supplierId) {
      record.set('supplierSiteLov', {});
    }
    if (!data.instructionDocType || data.instructionDocType !== instructionDocType) {
      setInstructionDocType(data.instructionDocType);
      record.set('instructionDocStatus', null);
    }
  };

  const listener = flag => {
    // 列表交互监听
    if (purchaseListDS) {
      const handler = flag ? purchaseListDS.addEventListener : purchaseListDS.removeEventListener;
      // 列表加载事件
      handler.call(purchaseListDS, 'load', resetHeaderDetail);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    handleTableChange();
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      const { instructionDocId } = dataSet?.current?.toData();
      handleLineQuery(instructionDocId);
    } else {
      lineDs.loadData([]);
    }
  };

  // 查询行表格
  const handleLineQuery = instructionDocId => {
    lineDs.setQueryParameter('instructionDocId', instructionDocId);
    lineDs.query();
  };

  // 点击头表格行
  const handleRowClick = record => {
    const { instructionDocId } = record.toData();
    lineDs.select(record);
    handleLineQuery(instructionDocId);
  };

  // 条码明细
  const handleBarCode = record => {
    detailDs.setQueryParameter('instructionDocLineId', record.toData().instructionDocLineId);
    detailDs.query();
    modalBarCode = Modal.open({
      title: intl.get(`${modelPrompt}.materialBatch.details`).d('物料批明细'),
      className: 'hmes-style-modal',
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      children: customizeTable(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST_MATERIAL_LOT.QUERY`,
        },
        <Table dataSet={detailDs} highLightRow={false} columns={detailColumns} />,
      ),
      footer: (
        <Button onClick={() => modalBarCode.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 点击'创建外协发料单'按钮回调
  const handleAdd = () => {
    props.history.push(`/hmes/purchase/outsourcing-manage-standard/outsourcing-doc/create`);
  };

  // 创建外协退料单
  const handleCreate = () => {
    props.history.push(`/hmes/purchase/outsourcing-manage-standard/outsourcing-returns/create`);
  };

  // 创建外协补料单
  const handleCreateBill = () => {
    if (purchaseListDS.selected.length > 1) {
      notification.error({
        message: intl.get(`${modelPrompt}.outsourcing.onePiece.data`).d('仅能勾选一条数据。'),
      });
    } else {
      const { instructionDocId } = purchaseListDS.selected[0].data;
      supplementCheck.run({
        params: {
          instructionDocId,
        },
        onSuccess: () => {
          props.history.push(`/hmes/purchase/outsourcing-manage-standard/supplement/${instructionDocId}`);
        },
      });
    }
  };

  // 状态变更
  const clickMenu = async ({ key }) => {
    const instructionDocIds = purchaseListDS?.selected?.map(
      item => item?.toData()?.instructionDocId,
    );

    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-out-source/out-source/status/modify/ui`, {
      method: 'POST',
      body: {
        instructionDocIds,
        instructionDocStatus: key,
      },
    }).then(_res => {
      const res = getResponse(_res);
      if (res?.success) {
        purchaseListDS.batchUnSelect(purchaseListDS.selected);
        purchaseListDS.clearCachedSelected();
        setTableChoice('');
        setPrintIds([]);
        setSelectedStatus('');
        purchaseListDS.query(props.purchaseListDS.currentPage);
      }
    });
  };

  // 选择
  const handleTableChange = () => {
    const _selectedStatus: string[] = [];
    const _instructionDocType: string[] = [];
    const _printIds: string[] = [];
    purchaseListDS.selected.forEach(item => {
      _printIds.push(item.data.instructionDocId);
      const { instructionDocStatus, instructionDocType: newInstructionDocType } = item?.data || {};
      if (_selectedStatus.indexOf(instructionDocStatus || 'CANCEL') === -1) {
        _selectedStatus.push(instructionDocStatus || 'CANCEL');
      }
      _instructionDocType.push(newInstructionDocType);
    });

    setPrintIds(_printIds);

    if (_instructionDocType.length === 1) {
      setTableChoice(_instructionDocType[0]);
    } else {
      setTableChoice('');
    }

    if (_selectedStatus.length === 1) {
      setSelectedStatus(_selectedStatus[0]);
    } else {
      setSelectedStatus('');
    }
  };

  const columns: ColumnProps[] = [
    {
      name: 'instructionDocNum',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'supplierName',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'supplierSiteName',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'instructionDocTypeDesc',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'sourceSystem',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'instructionDocStatusDesc',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'poNumber',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            props.history.push({
              pathname: `/hmes/purchase/order-management-standard/list`,
              query: { poNumber: record?.get('poNumber') },
            });
          }}
        >
          {value}
        </a>
      ),
    },
    /* {
      name: 'outSourceReturnFlagMeaning',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'replenishmentNum',
      tooltip: TableColumnTooltip.overflow,
      align: ColumnAlign.center,
    }, */

    {
      name: 'reasonMeaning',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'demandTime',
      width: 150,
      align: ColumnAlign.center,
    },
    {
      name: 'receivingAdress',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'remark',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'printTimes',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'realName',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'createdDate',
      width: 150,
      align: ColumnAlign.center,
    },
  ];

  const lineColumns: ColumnProps[] = [
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      lock: ColumnLock.left,
      tooltip: TableColumnTooltip.overflow,
      width: 80,
    },
    {
      name: 'materialCode',
      align: ColumnAlign.left,
      lock: ColumnLock.left,
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'revisionCode',
      align: ColumnAlign.left,
      lock: ColumnLock.left,
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'materialName',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'siteCode',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'instructionStatusDesc',
      tooltip: TableColumnTooltip.overflow,
      width: 100,
    },
    {
      name: 'quantity',
      align: ColumnAlign.right,
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'actualQty',
      align: ColumnAlign.right,
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'usedOverDeliveryQty',
      align: ColumnAlign.right,
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'uomCode',
      tooltip: TableColumnTooltip.overflow,
      width: 120,
    },
    {
      name: 'locatorCode',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'poNumber',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'poLineNum',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'toleranceFlag',
      renderer: ({ record }) => (
        <Badge
          status={record?.get('toleranceFlag') === 'Y' ? 'success' : 'error'}
          text={
            record?.get('toleranceFlag') === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
      align: ColumnAlign.center,
    },
    {
      name: 'toleranceTypeDesc',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'toleranceMaxValue',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'toleranceMinValue',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'poLineId',
      lock: ColumnLock.right,
      width: 140,
      align: ColumnAlign.center,
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => (
        <PermissionButton
          type="text"
          onClick={() => handleBarCode(record)}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.detail`).d('明细')}
        </PermissionButton>
      ),
    },
  ];

  const detailColumns: ColumnProps[] = [
    {
      name: 'identification',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'materialLotStatusDesc',
      align: ColumnAlign.left,
      width: 120,
    },
    {
      name: 'containerCode',
    },
    {
      name: 'actualQty',
    },
    {
      name: 'uomCode',
      tooltip: TableColumnTooltip.overflow,
      width: 120,
    },
    {
      name: 'lot',
    },
    {
      name: 'locatorCode',
    },
    {
      name: 'realName',
    },
    {
      name: 'lastUpdateDate',
      align: ColumnAlign.center,
      width: 200,
    },
  ];

  const onFieldEnterDown = () => {
    purchaseListDS.query(props.purchaseListDS.currentPage);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.outsourcing.management.platform`).d('外协管理平台')}>
        {/* <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
        >
          {intl.get('tarzan.common.button.print').d('打印')}
        </PermissionButton> */}
        <PermissionButton
          icon="add"
          type="c7n-pro"
          color={ButtonColor.primary}
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.create.outsourcingOrder`).d('创建外协发料单')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="plus"
          onClick={handleCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.create.materialReturn`).d('创建外协退料单')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="plus"
          onClick={handleCreateBill}
          disabled={tableChoice !== 'OUTSOURCING_RETURN_DOC'}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.create.replenishmentSheet`).d('创建外协补料单')}
        </PermissionButton>
        <Dropdown
          overlay={
            <Menu onClick={clickMenu} className={styles['split-menu']}>
              {selectedStatus === 'RELEASED' && (
                <Menu.Item key="CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
                  </a>
                </Menu.Item>
              )}
              {selectedStatus !== 'RELEASED' && (
                <Menu.Item key="CLOSED">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.close`).d('关闭')}
                  </a>
                </Menu.Item>
              )}
            </Menu>
          }
          trigger={[Action.click]}
          disabled={['RELEASED', 'PROCESSING', 'COMPLETED'].indexOf(selectedStatus) === -1}
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={['RELEASED', 'PROCESSING', 'COMPLETED'].indexOf(selectedStatus) === -1}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        {/* <FRPrintButton
          kid="OUTSOURCING_MANAGEMENT_PLATFORM"
          queryParams={printIds}
          disabled={!(printIds.length > 0)}
          printObjectType="INSTRUCTION_DOC"
        /> */}
        <TemplatePrintButton
          disabled={!printIds.length}
          printButtonCode='OUTSOURCING_MANAGEMENT_PLATFORM'
          printParams={{ docId: printIds.join(',') }}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST.HEAD`,
          },
          <Table
            searchCode="wxglpt1"
            customizedCode="wxglpt1"
            dataSet={purchaseListDS}
            columns={columns}
            highLightRow
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            // @ts-ignore
            onChange={handleTableChange}
            onRow={({ record }) => {
              return {
                onClick: () => {
                  handleRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineDs}
          >
            {lineDs && (
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST.LINE`,
                },
                <Table
                  customizedCode="wxglpt2"
                  className={styles['expand-table']}
                  dataSet={lineDs}
                  highLightRow={false}
                  columns={lineColumns}
                />,
              )
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.hmes.purchase.outsourcingManage', 'tarzan.common'] }),
  withProps(
    () => {
      const purchaseListDS = new DataSet({ ...OutsourcingList() });
      const lineDs = new DataSet({ ...LineDs() });
      const detailDs = new DataSet({ ...DetailDs() });
      return {
        purchaseListDS,
        lineDs,
        detailDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST_MATERIAL_LOT.QUERY`,
    ],
  }),
)(PurchaseList);
