import React, { useEffect, useState, useCallback } from 'react';
import axios from 'axios';
import { Tooltip, Collapse } from 'choerodon-ui';

import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_ATN } from 'alm/utils/config';

import styles from './index.module.less';

interface AssetQuantity {
  allAssetAmount: number;
  stockedAndIdleAmount: number;
  scrappedAmount: number;
  stockedAndIdleValue: number;
  allAssetValue: number;
  yearlyNewAsset: number;
}
const organizationId = getCurrentOrganizationId();
const url = `${HALM_ATN}/v1/${organizationId}/asset-cards/asset-count-display`;

const AssetQuantityCard = () => {
  const [data, setData] = useState<AssetQuantity | undefined>(undefined);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = useCallback(async () => {
    const res = await axios.get<any, AssetQuantity>(url);
    setData(res);
  }, []);

  // 缩小字体适应容器宽度
  useEffect(() => {
    const pList = document.getElementsByClassName('value-span');
    for (let index = 0; index < pList.length; index++) {
      const element = pList[index];
      if (element instanceof HTMLElement) {
        let fontSize = 24;
        element.style.fontSize = `${fontSize}px`;
        const parent = element.parentNode;
        if (parent instanceof HTMLElement) {
          const parentWidth = parent.offsetWidth - 12;
          while (fontSize > 12 && element.offsetWidth > parentWidth) {
            fontSize--;
            element.style.fontSize = `${fontSize}px`;
          }
        }
      }
    }
  }, [data]);

  return (
    <div className={styles.container}>
      <Collapse
        bordered={false}
        expandIconPosition="right"
        defaultActiveKey={['A', 'B']}
        trigger="icon"
        className={styles['customize-collapse']}
      >
        <Collapse.Panel key="A" showArrow={false} header="资产概况">
          <div className={styles['grid-container']}>
            <GridItem title="资产总数" value={data?.allAssetAmount} />
            <GridItem valueFlag title="资产总价值" value={data?.allAssetValue} />
            <GridItem title="年度新增资产" value={data?.yearlyNewAsset} />
            <GridItem title="闲置资产总数" value={data?.stockedAndIdleAmount} />
            <GridItem valueFlag title="闲置资产总价值" value={data?.stockedAndIdleValue} />
            <GridItem title="资产报废数量" value={data?.scrappedAmount} />
          </div>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
interface GridItemProps {
  title: string;
  value: number | undefined;
  valueFlag?: boolean;
}
const GridItem = ({ title, value, valueFlag }: GridItemProps) => {
  const formattedValue = useCallback((v: number) => {
    if (v > 10000) {
      return `${Number((v / 10000).toFixed(2)).toLocaleString('en-US')}`;
    } else {
      return `${v.toLocaleString('en-US')}`;
    }
  }, []);

  const formattedTitle = useCallback(
    (v: number) => {
      if (v > 10000) {
        return valueFlag ? `${title}（万元）` : `${title}（万）`;
      } else {
        return `${title}`;
      }
    },
    [valueFlag]
  );

  return (
    <div className={styles['grid-item']}>
      <h1>{formattedTitle(value ?? 0)}</h1>
      <Tooltip title={formattedValue(value ?? 0)}>
        <div className={styles.value}>
          <span className="value-span">{formattedValue(value ?? 0)}</span>
        </div>
      </Tooltip>
    </div>
  );
};
export default AssetQuantityCard;
