import React, { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import {
  DataSet,
  Table,
  Icon,
  Modal,
  TextField,
  Spin,
  Select,
  Form,
  Output,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';
import withProps from 'utils/withProps';
import { Header, Content } from 'components/Page';
import { closeTab } from 'utils/menuTab';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { BASIC } from '@utils/config';
import {
  queryDs,
  DispatchedOrderDs,
  WillDispatchOrderDs,
  orderInfoDs,
  tableKittingDs,
  historyDs,
  eoDS,
} from './store/index';
import WillDispatchOrder from './components/WillDispatchOrder';
import DispatchedOrder from './components/DispatchedOrder';
import DragGrid from './components/DragGrid';
import CreateBeforehandEoDrawer from './components/CreateBeforehandEoDrawer';
// import OrderInfo from './components/OrderInfo';
// import myInstance from '@/utils/myAxios';
import './index.less';

const tenantId = getCurrentOrganizationId();
// const yongjun = '-30711';
const yongjun = '';
let modalEnter;
let modalTab;
let modalCache;
let KittingModal;
let EoModal;
// let orderModal;

const TransportationLineMaintenance = withProps(
  () => {
    const queryDateDs = new DataSet(queryDs());
    const DispatchedOrderData = new DataSet(DispatchedOrderDs());
    const WillDispatchOrderDsData = new DataSet(WillDispatchOrderDs());
    const orderInfoData = new DataSet(orderInfoDs());
    const tableKittingDsData = new DataSet(tableKittingDs());
    const historyDataDs = new DataSet(historyDs());
    const eoDs = new DataSet(eoDS());
    return {
      queryDateDs,
      DispatchedOrderData,
      WillDispatchOrderDsData,
      orderInfoData,
      tableKittingDsData,
      historyDataDs,
      eoDs,
    };
  },
  { cacheState: true },
)(props => {
  const {
    queryDateDs,
    DispatchedOrderData,
    WillDispatchOrderDsData,
    orderInfoData,
    tableKittingDsData,
    historyDataDs,
    eoDs,
    match: { path },
    history,
  } = props;

  const remakeDs = useMemo(
    () =>
      new DataSet({
        // ...detailDs(),
        fields: [
          {
            name: 'inspectionTaskNum',
            type: 'string',
            label: '备注',
            required: true,
          },
          {
            name: 'inspectionTab',
            type: 'object',
            label: '标识',
            lookupCode: 'HME.WO_TAG_COLOR',
            required: true,
          },
        ],
      }),
    [remakeDs],
  );

  const [workOrderInfoFlag, setWorkOrderInfoFlag] = useState(false); // 工单信息是否显示的标识
  const [willDispatchDataSelect, setWillDispatchDataSelect] = useState([]); // 待派工单选择数据
  const [willDispatchDataSelectTag, setWillDispatchDataSelectTag] = useState([]); // 待派工单选择数据的标签
  const [dispatchDataSelectTag, setDispatchDataSelectTag] = useState([]); // 已派工单选择数据的标签
  const [dispatchedDataSelect, setDispatchedDataSelect] = useState([]); // 已派工单选择数据
  const [leftWoInfoList, setLeftWoInfoList] = useState([]); // 待派工单缓存数据
  const [rightWoInfoList, setRightWoInfoList] = useState([]); // 已派工单缓存数据
  const [willDispatchSpin, setWillDispatchSpin] = useState(false); // 待派工单spin
  const [isShowOrder, setIsShowOrder] = useState(false); // 是否显示工单下方信息的标识
  const [orderInfo, setOrderInfo] = useState(null); // 记录当前点击的工单编码信息
  const [dispatchedSpin, setDispatchedSpin] = useState(false); // 已派工单spin
  const [orderSpin, setOrderSpin] = useState(false); // 工单详细信息spin
  const [bomId, setBomId] = useState(); // 记录跳转的BOM编码的bomid
  const [routerId, setRouterId] = useState(); // 记录跳转的工艺路线的routerid
  const [eoDrawerVisible, setEoDrawerVisible] = useState(false);
  const eoDrawerVisibleRef = useRef();
  const routerWkc = useRef();
  const bomnWkc = useRef();
  useEffect(() => {
    routerWkc.current = routerId;
  }, [routerId]);
  useEffect(() => {
    bomnWkc.current = bomId;
  }, [bomId]);
  useEffect(() => {
    eoDrawerVisibleRef.current = eoDrawerVisible;
  }, [eoDrawerVisible]);

  // 查询默认产线并赋值
  const queryProdlineData = async () => {
    const res = await request(
      `${BASIC.TARZAN_MODEL}${yongjun}/v1/${tenantId}/hme-mod-prod-line/permission/prod-line/ui?page=0&size=10`,
      {
        method: 'POST',
        body: {},
      },
    );
    if (getResponse(res)) {
      const list = [{}];
      if (res.content.length === 0) {
        return;
      }
      if (res.content.length > 1) {
        list.push(res.content.filter(v => v.defaultOrganizationFlag === 'Y')[0]);
      }
      if (list.length <= 1) {
        list.push(res.content[0]);
      }
      // if(list.length < 1){
      //   list.push(res.content[0]); // 防止错误数据，此时让它默认为第一条数据
      // }
      queryDateDs.queryDataSet?.current.set('site', list[list.length - 1]);
      // queryDateDs.query(queryDateDs.currentPage);
      queryDateDs.query(queryDateDs.currentPage);
    } else {
      setOrderSpin(false);
    }
  };

  useEffect(() => {
    // 回到此页面后，关掉之前跳转过的界面
    props.history.listen(location => {
      if (props.location.pathname === location.pathname) {
        closeTab('/hmes/inventory/query');
        closeTab(`/hmes/product/assembly-list/dist/${bomnWkc.current}`);
        closeTab(`/hmes/new/process/routes/dist/${routerWkc.current}`);
      }
    });
    queryProdlineData();
    // 添加选中监听事件
    WillDispatchOrderDsData.addEventListener('select', handleWillDispatchDataSelect);
    WillDispatchOrderDsData.addEventListener('unSelect', handleWillDispatchDataSelect);
    WillDispatchOrderDsData.addEventListener('selectAll', handleWillDispatchDataSelect);
    WillDispatchOrderDsData.addEventListener('unSelectAll', handleWillDispatchDataSelect);
    DispatchedOrderData.addEventListener('select', handleDispatchedDataSelect);
    DispatchedOrderData.addEventListener('unSelect', handleDispatchedDataSelect);
    DispatchedOrderData.addEventListener('selectAll', handleDispatchedDataSelect);
    DispatchedOrderData.addEventListener('unSelectAll', handleDispatchedDataSelect);
    queryDateDs.addEventListener('load', data => queryAllStatisticsData(data));
  }, []);

  const queryAllStatisticsData = data => {
    if (data.dataSet.toData().length > 0) {
      const res = data.dataSet.toData()[0];
      setWillDispatchSpin(false);
      setDispatchedSpin(false);
      WillDispatchOrderDsData.loadData(res.leftWoInfoList || []);
      DispatchedOrderData.loadData(res.rightWoInfoList || []);
      setLeftWoInfoList(res.leftWoInfoList || []);
      setRightWoInfoList(res.rightWoInfoList || []);
      setWillDispatchDataSelect([]);
      setDispatchedDataSelect([]);
      setWillDispatchDataSelectTag([]);
      setDispatchDataSelectTag([]);
      if (res.noPriorityWoList && res.noPriorityWoList.length > 0) {
        modalCache = Modal.open({
          title: `工单：${res.noPriorityWoList.join(',')}优先级为空，是否需要赋值？`,
          destroyOnClose: true,
          // closable: true,
          onOk: () => cacheQuery(res),
        });
      }
    }
    onChangeAsides();
  };

  const cacheQuery = res => {
    // queryDateDs.queryDataSet?.current.set('priorityFlag', 'Y');
    // queryDateDs.queryDataSet?.current.set('noPriorityWoList', res.noPriorityWoList);
    modalCache.close();
    queryDateDs.setQueryParameter('priorityFlag', 'Y')
    queryDateDs.setQueryParameter('noPriorityWoList', res.noPriorityWoList)
    queryDateDs.query(queryDateDs.currentPage);
  };

  // 跳转制造工艺路线
  const handlePushRouterTab = record => {
    if (record) {
      setRouterId(record.routerId);
      history.push(`/hmes/new/manufacture-process/routes-c7n/dist/${record.routerId}`);
    }
  };

  // 跳转制造装配清单
  const handlePushBomTab = record => {
    if (record) {
      setBomId(record.bomId);
      history.push(`/hmes/product/manufacture-list/dist/${record.bomId}`);
    }
  };
  // 跳转物料装配清单
  const handlePushOrderTab = record => {
    if (record) {
      setBomId(record.bomId);
      props.history.push({
        key: new Date().getTime(),
        pathname: `/hmes/workshop/execute-operation-management/list`,
        query: {
          workOrderNum: record.workOrderNum,
          workOrderId: record.workOrderId,
          // siteId: value.siteId,
          // siteCode: value.siteCode,
          // locatorId: value.locatorList.map((v) => v.locatorId),
          // locatorCode: value.locatorList.map((v) => v.locatorCode),
          // materialId: value.materialList.map((v) => v.materialId),
          // materialCode: value.materialList.map((v) => v.materialCode),
        },
      });
    }
  };

  // 待派工单选择数据监听
  const handleWillDispatchDataSelect = () => {
    if (eoDrawerVisibleRef.current && WillDispatchOrderDsData.selected.length > 0) {
      WillDispatchOrderDsData.data.forEach(v => {
        v.selectable = true;
        if (WillDispatchOrderDsData.selected[0].data.workOrderId !== v.data.workOrderId) {
          v.selectable = false;
        }
      });
    } else {
      WillDispatchOrderDsData.data.forEach(v => {
        v.selectable = true;
      });
    }
    setWillDispatchDataSelect(WillDispatchOrderDsData.selected);
    setWillDispatchDataSelectTag(WillDispatchOrderDsData.selected.map(v => v.data.tag));
  };
  // 已派工单选择数据监听
  const handleDispatchedDataSelect = () => {
    setDispatchedDataSelect(DispatchedOrderData.selected);
    setDispatchDataSelectTag(DispatchedOrderData.selected.map(v => v.data.tag));
  };

  // 查询工单详细信息
  const handleSearchOrder = async value => {
    if (value) {
      // 此处逻辑是用于第二次点击同一个工单编码时关闭工单详细信息
      if (orderInfo === value.workOrderId) {
        setIsShowOrder(false);
      } else {
        setIsShowOrder(true);
      }
      setOrderSpin(true);
      const res = await request(
        `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/wo/detail/ui`,
        {
          method: 'GET',
          query: { workOrderId: value.workOrderId },
        },
      );
      if (getResponse(res)) {
        setWorkOrderInfoFlag(true);
        // 此处逻辑是用于第二次点击同一个工单编码时关闭工单详细信息，同时考虑到会第三次点击到相同的工单
        if (orderInfo === value.workOrderId) {
          setOrderInfo(null);
        } else {
          setOrderInfo(value.workOrderId);
        }
        orderInfoData.loadData([res]);
        setOrderSpin(false);
      } else {
        setOrderSpin(false);
      }
    }
  };
  // 查询工单详细信息
  // const handleSearchOrder = async value => {
  //   if (value) {
  //     const orderInfoProps = {
  //       dataSet: orderInfoData,
  //       changeSaveLoading: false,
  //       handleSearchKitting,
  //       handlePushRouterTab,
  //       handlePushBomTab,
  //       handlePushOrderTab,
  //       orderSpin,
  //       value,
  //       history,
  //     };
  //     orderModal = Modal.open({
  //       title: '工单投料进度查看',
  //       destroyOnClose: false,
  //       footer: null,
  //       closable: true,
  //       mask: false,
  //       maskClosable: true,
  //       // drawer: true,
  //       maskClassName: 'mask-class-name',
  //       style: {
  //         width: '80%',
  //       },
  //       children: (<OrderInfo {...orderInfoProps} />),
  //     });
  //   }
  // };

  // 待派工单新增备注
  const onFetchRemake = async flag => {
    if (remakeDs.toData().length === 0 || !remakeDs.toData()[0].inspectionTaskNum) {
      notification.error({ message: '请输入备注信息！' });
      return false;
    } else {
      // setWillDispatchSpin(true);
      const _list = [];
      if (flag === 'will') {
        willDispatchDataSelect.forEach(v => {
          _list.push({
            workOrderId: v.data.workOrderId,
            remarkList: [...(v.data.remarkList || []), remakeDs.toData()[0].inspectionTaskNum],
          });
        });
      } else {
        dispatchedDataSelect.forEach(v => {
          _list.push({
            workOrderId: v.data.workOrderId,
            remarkList: [...(v.data.remarkList || []), remakeDs.toData()[0].inspectionTaskNum],
          });
        });
      }
      const params = {
        workOrderIdList:
          flag === 'will'
            ? willDispatchDataSelect.map(v => v.data.workOrderId)
            : dispatchedDataSelect.map(v => v.data.workOrderId),
        remarkInfoList: _list,
      };
      const res = await request(
        `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/remark/ui`,
        {
          method: 'POST',
          body: params,
        },
      );
      if (getResponse(res)) {
        notification.success();
        queryDateDs.query(queryDateDs.currentPage);
        modalEnter.close();
      } else {
        setWillDispatchSpin(false);
        setDispatchedSpin(false);
      }
    }
  };
  // 待派工单关闭
  const handleCloseOrder = async () => {
    if (willDispatchDataSelect.length === 0) {
      notification.error({ message: '请选择数据！' });
    } else {
      // const params = {
      //   workOrderIdList: willDispatchDataSelect.map((v)=>v.data.workOrderId),
      // };
      setWillDispatchSpin(true);
      const res = await request(
        `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/close/ui`,
        {
          method: 'POST',
          body: willDispatchDataSelect.map(v => v.data.workOrderId),
        },
      );
      if (getResponse(res)) {
        notification.success();
        queryDateDs.query(queryDateDs.currentPage);
        modalEnter.close();
      } else {
        setWillDispatchSpin(false);
      }
    }
  };
  // 待派工单删除备注
  const handleCloseTag = async (value, v) => {
    const _current = JSON.parse(JSON.stringify(value.remarkList));
    _current.splice(v, 1);
    const _list = [];
    _list.push({
      workOrderId: value.workOrderId,
      remarkList: _current,
    });
    const params = {
      workOrderIdList: [value.workOrderId],
      remarkInfoList: _list,
    };
    const res = await request(
      `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/remark/ui`,
      {
        method: 'POST',
        body: params,
      },
    );
    if (getResponse(res)) {
      notification.success();
      queryDateDs.query(queryDateDs.currentPage);
    } else {
      queryDateDs.query(queryDateDs.currentPage);
    }
  };
  // 已派工单暂挂
  const handlePending = async () => {
    const data = dispatchedDataSelect.map(v => v.data.status);
    if (data.includes('HOLD')) {
      return notification.error({ message: '不为下达作业状态的工单，不允许进行暂挂操作！' });
    }
    if (dispatchedDataSelect.length === 0) {
      notification.error({ message: '请选择数据！' });
    } else {
      setDispatchedSpin(true);
      const res = await request(
        `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/hold/ui`,
        {
          method: 'POST',
          body: dispatchedDataSelect.map(v => v.data.workOrderId),
        },
      );
      if (getResponse(res)) {
        notification.success();
        queryDateDs.query(queryDateDs.currentPage);
      } else {
        setDispatchedSpin(false);
      }
    }
  };
  // 已派工单暂挂取消
  const handlePendingCancel = async () => {
    const data = dispatchedDataSelect.map(v => v.data.status);
    if (data.includes('EORELEASED')) {
      return notification.error({ message: '不为暂挂状态的工单，不允许进行暂挂取消操作！' });
    }
    if (dispatchedDataSelect.length === 0) {
      notification.error({ message: '请选择数据！' });
    } else {
      setDispatchedSpin(true);
      const res = await request(
        `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/hold-cancal/ui`,
        {
          method: 'POST',
          body: dispatchedDataSelect.map(v => v.data.workOrderId),
        },
      );
      if (getResponse(res)) {
        notification.success();
        queryDateDs.query(queryDateDs.currentPage);
      } else {
        setDispatchedSpin(false);
      }
    }
  };
  // 待派工单新增标识
  const onFetchTab = async flag => {
    if (remakeDs.toData().length === 0 || !remakeDs.toData()[0].inspectionTab) {
      notification.error({ message: '请选择标识信息！' });
      return false;
    } else {
      const params = {
        workOrderIdList:
          flag === 'will'
            ? willDispatchDataSelect.map(v => v.data.workOrderId)
            : dispatchedDataSelect.map(v => v.data.workOrderId),
        tag: remakeDs.toData()[0].inspectionTab.value,
        tagDesc: remakeDs.toData()[0].inspectionTab.meaning,
      };
      setWillDispatchSpin(true);
      setDispatchedSpin(true);
      const res = await request(
        `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-assembly-dispatch/remark/ui`,
        {
          method: 'POST',
          body: params,
        },
      );
      if (getResponse(res)) {
        notification.success();
        queryDateDs.query(queryDateDs.currentPage);
        modalTab.close();
      } else {
        setWillDispatchSpin(false);
        setDispatchedSpin(false);
      }
    }
  };

  // 齐套数量弹框表格展示
  const handleSearchKitting = async value => {
    if (value) {
      setOrderSpin(true);
      const res = await request(
        `${BASIC.HMES_BASIC
        }${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/complete-set/info/ui?workOrderId=${value.workOrderId
        }&prodLineId=${queryDateDs.queryDataSet?.toData()[0]?.prodLineId}`,
        {
          method: 'GET',
          query: value.workOrderId,
        },
      );
      if (getResponse(res)) {
        setOrderSpin(false);
        tableKittingDsData.loadData(res);
        const columns = [
          {
            name: 'bomComponentName',
          },
          {
            name: 'inputQty',
          },
          {
            name: 'canInputQty',
          },
          {
            name: 'scrappedQty',
          },
          {
            name: 'inventoryQty',
            style: {
              color: 'blue',
            },
            renderer: ({ value, record }) => {
              return (
                <a
                  onClick={() => {
                    handlePushQuery(record.data);
                  }}
                >
                  {value || <Icon type="redo" />}
                </a>
              );
            },
          },
        ];
        KittingModal = Modal.open({
          title: '工单投料进度查看',
          destroyOnClose: true,
          footer: null,
          closable: true,
          style: {
            width: 720,
          },
          children: (
            <Table
              pagination={false}
              spin={{ size: 'large', spinning: false }}
              dataSet={tableKittingDsData}
              columns={columns}
            />
          ),
        });
      } else {
        setOrderSpin(false);
      }
    }
  };
  // 工单历史弹框表格展示
  const handleFetchHistory = async value => {
    if (value) {
      // setOrderSpin(true);
      const res = await request(
        `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/wo-his/info/ui?workOrderId=${value.workOrderId}`,
        {
          method: 'GET',
          query: value.workOrderId,
        },
      );
      if (getResponse(res)) {
        setOrderSpin(false);
        historyDataDs.loadData(res);
        const columns = [
          {
            name: 'eventType',
          },
          {
            name: 'eventTime',
          },
          {
            name: 'eventBy',
          },
          {
            name: 'workOrderNum',
          },
          {
            name: 'workOrderType',
          },
          {
            name: 'siteCode',
          },
          {
            name: 'siteName',
          },
          {
            name: 'prodLineCode',
          },
          {
            name: 'prodLineName',
          },
          {
            name: 'makeOrderNum',
          },
          {
            name: 'productionVersion',
          },
          {
            name: 'materialCode',
          },
          {
            name: 'materialName',
          },
          {
            name: 'revisionCode',
          },
          {
            name: 'qty',
          },
          {
            name: 'rexQty',
          },
          {
            name: 'uomCode',
          },
          {
            name: 'status',
          },
          {
            name: 'planStartTime',
          },
          {
            name: 'planEndTime',
          },
          {
            name: 'locatorCode',
          },
          {
            name: 'locatorName',
          },
          {
            name: 'bomName',
          },
          {
            name: 'routerName',
          },
        ];
        Modal.open({
          title: '工单历史查看',
          destroyOnClose: true,
          footer: null,
          closable: true,
          style: {
            width: 720,
          },
          children: (
            <Table
              pagination={false}
              spin={{ size: 'large', spinning: false }}
              dataSet={historyDataDs}
              columns={columns}
            />
          ),
        });
      } else {
        setOrderSpin(false);
      }
    }
  };

  // 齐套数量跳转库存查询
  const handlePushQuery = value => {
    if (value) {
      props.history.push({
        key: new Date().getTime(),
        pathname: `/hmes/inventory/query`,
        query: {
          siteId: value.siteId,
          siteCode: value.siteCode,
          locatorId: value.locatorList.map(v => v.locatorId),
          locatorCode: value.locatorList.map(v => v.locatorCode),
          materialId: value.materialList.map(v => v.materialId),
          materialCode: value.materialList.map(v => v.materialCode),
        },
      });
      KittingModal.close();
    }
  };

  // 待派工单新增备注modal框
  const handelAddRemark = flag => {
    if (flag === 'will' && willDispatchDataSelect.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    if (flag === 'dis' && dispatchedDataSelect.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    let _title = '';
    if (flag === 'will') {
      _title = '待派工单新增备注';
    }else{
      _title = '已派工单新增备注';
    }
    modalEnter = Modal.open({
      title: _title,
      destroyOnClose: true,
      closable: true,
      children: (
        <TextField
          name="inspectionTaskNum"
          placeholder="备注信息"
          dataSet={remakeDs}
          required
          clearButton
          onEnterDown={e => onFetchRemake(e.target.value)}
          colSpan={1}
          style={{ width: '100%' }}
        />
      ),
      onOk: () => onFetchRemake(flag),
    });
  };
  // 待派工单删除标记
  const handelDelTag = async flag => {
    if (flag === 'will' && willDispatchDataSelect.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    if (flag === 'dis' && dispatchedDataSelect.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    const params = {
      workOrderIdList:
        flag === 'will'
          ? willDispatchDataSelect.map(v => v.data.workOrderId)
          : dispatchedDataSelect.map(v => v.data.workOrderId),
      tag: 'DELETE',
    };
    setWillDispatchSpin(true);
    setDispatchedSpin(true);
    const res = await request(
      `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/remark/ui`,
      {
        method: 'POST',
        body: params,
      },
    );
    if (getResponse(res)) {
      notification.success();
      queryDateDs.query(queryDateDs.currentPage);
    } else {
      setWillDispatchSpin(false);
      setDispatchedSpin(false);
    }
  };

  // 待派工单新增标识modal框
  const handelAddtab = flag => {
    if (flag === 'will' && willDispatchDataSelect.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    if (flag === 'dis' && dispatchedDataSelect.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    let _title = '';
    if (flag === 'will') {
      _title = '待派工单新增标识';
    }else{
      _title = '已派工单新增标识';
    }
    modalTab = Modal.open({
      title: _title,
      destroyOnClose: true,
      closable: true,
      children: (
        <Select
          name="inspectionTab"
          placeholder="标识信息"
          dataSet={remakeDs}
          required
          clearButton
          colSpan={1}
          style={{ width: '100%' }}
        />
      ),
      onOk: () => onFetchTab(flag),
    });
  };

  // 待派工单下达
  const onChangeAsides = async () => {
    const res = await request(
      `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/is/popup/ui`,
      {
        method: 'GET',
        query: {
          prodLineId: queryDateDs.queryDataSet?.toData()[0]?.prodLineId,
        },
      },
    );
    setEoDrawerVisible(res);
  };

  const onFetchChangeAsides = async (isModal = true) => {
    if (willDispatchDataSelect.length === 0) {
      return notification.error({ message: '请选择数据！' });
    }
    if (isModal === true) { // 弹窗
      EoModal = Modal.open({
        title: '执行作业创建',
        destroyOnClose: true,
        closable: true,
        style: {
          width: 600,
        },
        children: (
          <CreateBeforehandEoDrawer eoDs={eoDs} qty={willDispatchDataSelect.map(v => Number(v.data.qty))} />
        ),
        onOk: () => {
          // onFetchChangeAsides(false);
          onFetchChange()
        },
      });
      return
    } else {
      onFetchChange()
    }
  }

  const onFetchChange = async () => {
    const data = {
      workOrderIdList: willDispatchDataSelect.map(v => v.data.workOrderId),
      totalQty: eoDs.current?.get('totalQty'),
      eoQty: eoDs.current?.get('eoQty'),
      mantissaDeal: eoDs.current?.get('mantissaDeal'),
    };
    setWillDispatchSpin(true);
    const res = await request(
      `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/released/ui`,
      {
        method: 'POST',
        body: data,
      },
    );
    if (getResponse(res)) {
      notification.success();
      queryDateDs.query(queryDateDs.currentPage);
      if (modalEnter) modalEnter.close();
      if (EoModal) EoModal.close();
      eoDs.loadData([])
    } else {
      setWillDispatchSpin(false);
    }
  }

  // 派工撤回
  const onChangeWithdraw = async () => {
    if (dispatchedSpin) {
      return;
    }
    if (dispatchedDataSelect.length === 0) {
      return notification.error({ message: '请选择数据！' });
    }
    setDispatchedSpin(true);
    const res = await request(
      `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/wo/dispatch/withdraw`,
      {
        method: 'POST',
        body: dispatchedDataSelect.map(v => v.data.workOrderId),
      },
    );
    if (getResponse(res)) {
      notification.success();
      queryDateDs.query(queryDateDs.currentPage);
    } else {
      setDispatchedSpin(false);
    }
  }

  // 待派工单表格排序
  const handleDragEndWill = async value => {
    if (value) {
      setWillDispatchSpin(true);
      const res = await request(
        `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/priority/ui`,
        {
          method: 'POST',
          body: value,
        },
      );
      if (getResponse(res)) {
        notification.success();
        queryDateDs.query(queryDateDs.currentPage);
      } else {
        setWillDispatchSpin(false);
      }
    }
  };
  // 已派工单表格排序
  const handleDragEndEd = async value => {
    if (value) {
      const list = [];
      DispatchedOrderData.toData().forEach(v => {
        if (v.actualStartDate) {
          list.push(v.workOrderId);
        }
      });
      setDispatchedSpin(true);
      const res = await request(
        `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-wo-assembly-dispatch/priority/ui`,
        {
          method: 'POST',
          body: { ...value, workOrderIdList: list },
        },
      );
      if (getResponse(res)) {
        notification.success();
        queryDateDs.query(queryDateDs.currentPage);
      } else {
        setDispatchedSpin(false);
        queryDateDs.query(queryDateDs.currentPage);
      }
    }
  };

  const dispatchedProps = {
    dataSet: DispatchedOrderData,
    rightWoInfoList,
    handlePending,
    handlePendingCancel,
    handleDragEndEd,
    handleSearchOrder,
    handleFetchHistory,
    handelDelTag,
    handelAddRemark,
    handelAddtab,
    dispatchedDataSelect,
    dispatchDataSelectTag,
    path,
    dispatchedSpin,
  };
  const willDispatchProps = {
    dataSet: WillDispatchOrderDsData,
    leftWoInfoList,
    willDispatchDataSelectTag,
    handleSearchOrder,
    handelAddRemark,
    handelDelTag,
    handelAddtab,
    handleCloseOrder,
    handleDragEndWill,
    handleFetchHistory,
    handleCloseTag,
    path,
    willDispatchSpin,
  };

  // const orderInfoProps = {
  //   dataSet: orderInfoData,
  //   handleSearchKitting,
  //   handlePushRouterTab,
  //   handlePushBomTab,
  //   handlePushOrderTab,
  //   orderSpin,
  //   history,
  // };

  return (
    <Fragment>
      <Header title="装配工单派工"></Header>
      <Content style={{ padding: 2 }}>
        <Table
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          // buttons={['query']}
          dataSet={queryDateDs}
          columns={[]}
          searchCode="InspectionPlatform"
          className="assemblyOrderDispatch-platform-search-table"
        />
        <DragGrid
          article={
            <Spin spinning={false}>
              <WillDispatchOrder {...willDispatchProps} />
            </Spin>
          }
          aside={<DispatchedOrder {...dispatchedProps} />}
          asideShow
          onChangeAsides={() => { onFetchChangeAsides(eoDrawerVisibleRef.current) }}
          onChangeWithdraw={onChangeWithdraw}
        />
        {/* {workOrderInfoFlag && isShowOrder && <OrderInfo {...orderInfoProps} />} */}
        {workOrderInfoFlag && isShowOrder && (
          <div className='orderInfo'>
            <Spin spinning={orderSpin}>
              <Icon onClick={() => { setIsShowOrder(false); }} type="close" />
              <Form
                style={{ paddingLeft: '20px' }}
                dataSet={orderInfoData}
                columns={4}
                labelAlign="left"
                labelLayout="horizontal"
                labelWidth="auto"
              >
                <Output
                  name="workOrderNum"
                  colSpan={1}
                  renderer={({ value, record }) => (
                    <a onClick={() => handlePushOrderTab(record.data)}>{value}</a>
                  )}
                />
                <Output name="workOrderType" colSpan={1} />
                <Output name="materialCode" colSpan={1} />
                <Output name="materialName" colSpan={1} />
                <Output
                  name="bomName"
                  colSpan={1}
                  renderer={({ value, record }) => (
                    <a onClick={() => handlePushBomTab(record.data)}>{value}</a>
                  )}
                />
                <Output name="prodLine" colSpan={1} />
                <Output name="customer" colSpan={1} />
                <Output name="planStartTime" colSpan={1} />
                <Output name="planEndTime" colSpan={1} />
                <Output
                  name="routerName"
                  colSpan={1}
                  renderer={({ value, record }) => (
                    <a onClick={() => handlePushRouterTab(record.data)}>{value}</a>
                  )}
                />
                <Output name="qty" colSpan={1} />
                <Output
                  name="completeSetQty"
                  colSpan={1}
                  renderer={({ value, record }) => (
                    <a onClick={() => handleSearchKitting(record.data)}>{value}</a>
                  )}
                />
                <Output name="scrappedQty" colSpan={1} />
                <Output name="completedQty" colSpan={1} />
              </Form>
            </Spin>
          </div>
        )}
      </Content>
    </Fragment>
  );
});
export default formatterCollections()(TransportationLineMaintenance);
