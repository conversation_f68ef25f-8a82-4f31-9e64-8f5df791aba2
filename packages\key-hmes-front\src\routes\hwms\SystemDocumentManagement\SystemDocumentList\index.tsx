/*
 * @Description: 质量体系文件管理-列表页代码重构
 * @Author: <<EMAIL>>
 * @Date: 2023-11-02 14:24:14
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-19 20:53:28
 */
import React, { useMemo, useState, useEffect } from 'react';
import { Table, DataSet, Modal, Attachment, Form, TextArea, Lov, Button, Dropdown } from 'choerodon-ui/pro';
import { Menu, Row, Col, Radio, Icon } from 'choerodon-ui';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { getCurrentUser, getCurrentOrganizationId } from 'utils/utils';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { queryMapIdpValue } from '@services/api';
import { useDataSetEvent } from 'utils/hooks';
import moment from 'moment';
import { observer } from 'mobx-react';
import { openTab } from 'utils/menuTab';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import { BASIC } from '@utils/config';
import EditDrawerComponent from './EditDrawerComponent';
import UpdateDrawerComponent from './UpdateDrawerComponent';
import {
  tableDS,
  historyDS,
  approveViewDS,
  examineDS,
  cancelReasonDS,
  updateDS,
  releateForthFileDS,
  relatedFileDS,
} from '../stores';
import {
  SaveFile,
  StagingFile,
  CancelFile,
  UpdateFile,
  ValidatePublishFile,
  PublishFile,
  DiscardFile,
  GetUserRole,
  ExportSystemFile,
} from '../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.qms.systemDocumentManagement';
const userInfo = getCurrentUser();
const tenantId = getCurrentOrganizationId();
let _modal;

const SystemDocumentList = props => {
  const {
    match: { path },
    tableDs,
    // 升版Ds
    updateDs,
    // 审批历史Ds
    approveViewDs,
    // 历史查看Ds
    historyDs,
    // 取消Ds
    cancelReasonDs,
    // 提交/废弃Ds
    examineDs,
    // 当前文件详情Ds
    fileRecordDs,
    // 关联四级文件DS
    releateForthFileDs,
    // 相关文件DS
    relatedFileDs,
    history,
  } = props;
  // 当前视图
  const [currentMode, setCurrentMode] = useState('LEVEL');
  // 侧边栏数据
  const [lovData, setLovData] = useState<any>({});
  // 管理员权限数据
  const [adminFlag, setAdminFlag] = useState<boolean>([]);
  // 当前层级
  const [currentLevel, setCurrentLevel] = useState<any>([]);
  // 当前过程
  const [currentProcess, setCurrentProcess] = useState<any>([]);
  // 当前选中的文件信息
  const [selectedList, setSelectedList] = useState([]);
  // 保存文件
  const saveFile = useRequest(SaveFile(), { manual: true, needPromise: true });
  // 暂存文件
  const stagingFile = useRequest(StagingFile(), { manual: true, needPromise: true });
  // 文件升版
  const updateFile = useRequest(UpdateFile(), { manual: true, needPromise: true });
  // 取消文件
  const cancelFile = useRequest(CancelFile(), { manual: true, needPromise: true });
  // 发布文件校验
  const validatePublishFile = useRequest(ValidatePublishFile(), {
    manual: true,
    needPromise: true,
  });
  // 发布文件
  const publishFile = useRequest(PublishFile(), { manual: true, needPromise: true });
  // 废弃文件
  const discardFile = useRequest(DiscardFile(), { manual: true, needPromise: true });
  // 文件导出
  const { run: exportSystemFile, loading: exportLoading } = useRequest(ExportSystemFile(), { manual: true, needPromise: true });
  const getAdminRole = useRequest({ lovCode: 'YP.QIS.SYSTEM_FILE_ADMIN' }, { manual: true, needPromise: true });
  const getUserRole = useRequest(GetUserRole(), { manual: true, needPromise: true });

  useEffect(() => {
    tableDs.queryDataSet.setState('currentMode', 'LEVEL');
    if (!Object.keys(lovData)?.length) {
      handleInitLovData();
    }
    handleInitCreateRole();
  }, []);

  const handleInitLovData = () => {
    queryMapIdpValue({
      levelList: 'YP.QIS.FILE_LEVEL',
      processList: 'YP.QIS.AFFLIATED_PROCESS',
    }).then(res => {
      if (res && !res.failed) {
        const { levelList, processList } = res || {};
        setLovData({
          levelList,
          processList,
        });
      }
    });
  };

  const handleInitCreateRole = async () => {
    const res = await getAdminRole.run({});
    const currentRoleRes = await getUserRole.run({});
    const adminRoleList = (res || []).map((item) => item.value);
    const currentRoleList = (currentRoleRes || []).map((item) => item.code);
    const adminFlag = adminRoleList.find((item) => currentRoleList.includes(item));
    setAdminFlag(Boolean(adminFlag));
    tableDs.setState('adminFlag', adminFlag);
    fileRecordDs.setState('adminFlag', adminFlag);
  };

  const handleUpdateSelect = ({ dataSet }) => {
    const _selectedList = dataSet.selected.map(_record => ({
      ..._record?.toData(),
    }));
    setSelectedList(_selectedList);
  };

  useDataSetEvent(tableDs, 'batchSelect', handleUpdateSelect);
  useDataSetEvent(tableDs, 'batchUnSelect', handleUpdateSelect);
  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, value }) => {
    if (currentMode === 'LEVEL' && name === 'fileLevel') {
      setCurrentLevel(value ? [value] : []);
    } else if (currentMode === 'PROCESS' && name === 'affliatedProcess') {
      setCurrentProcess(value ? [value] : []);
    }
  })

  const approveColumns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'seq',
        width: 60,
        align: ColumnAlign.left,
        renderer: ({ record }) => (record?.index || 0) + 1,
      },
      {
        name: 'fileCode',
      },
      {
        name: 'fileName',
      },
      {
        name: 'version',
      },
      {
        name: 'operationTypeMeaning',
      },
      {
        name: 'approvedByName',
      },
      {
        name: 'approveResultMeaning',
      },
      {
        name: 'approvedReason',
      },
      {
        name: 'approvedDate',
        align: ColumnAlign.center,
        width: 150,
      },
    ],
    [],
  );

  // 查看审批历史
  const handleApproveView = record => {
    approveViewDs.setQueryParameter('fileId', record?.get('fileId'));
    approveViewDs.query();
    Modal.open({
      ...drawerPropsC7n({
        ds: approveViewDs,
        canEdit: false,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.approvalHistory`).d('审批历史查询'),
      style: {
        width: 1080,
      },
      children: <Table dataSet={approveViewDs} columns={approveColumns} />,
    });
  };

  const getAttachmentViewMode = record => {
    const { fileLevel, editedBy } = record.toData();
    if (fileLevel === 'FORTH') {
      return 'popup';
    }

    if (['FIRST', 'SECOND', 'THIRD'].includes(fileLevel)) {
      if (editedBy === userInfo.id || adminFlag) {
        return 'popup';
      }
      return 'none';
    }

    return 'popup';
  };

  const getAttachmentProps: any = record => ({
    name: 'uuid',
    bucketName: 'qms',
    record,
    max: 1,
    bucketDirectory: 'system-document-management',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    disabled: true,
    readOnly: true,
    viewMode: getAttachmentViewMode(record),
  });

  const historyColumns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'seq',
        width: 60,
        align: ColumnAlign.left,
        renderer: ({ record }) => (record?.index || 0) + 1,
      },
      {
        name: 'fileCode',
      },
      {
        name: 'fileName',
      },
      {
        name: 'version',
      },
      {
        name: 'editedDepartmentName',
      },
      {
        name: 'editedDepartmentParentName',
      },
      {
        name: 'editedByRealName',
      },
      {
        name: 'editedDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'publishDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'fileStatus',
      },
      {
        name: 'affliatedProcess',
      },
      {
        name: 'lastUpdateDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'lastUpdatedByRealName',
      },
      {
        name: 'operationTypeMeaning',
      },
      {
        name: 'uuid',
        width: 220,
        renderer: ({ record }) => <Attachment {...getAttachmentProps(record)} />,
      },
    ],
    [],
  );

  // 历史查看
  const handleHistory = record => {
    historyDs.setQueryParameter('fileId', record.data.fileId);
    historyDs.query();
    Modal.open({
      ...drawerPropsC7n({
        ds: historyDs,
        canEdit: false,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.historyQuery`).d('历史查询'),
      style: {
        width: 1080,
      },
      children: <Table dataSet={historyDs} columns={historyColumns} />,
    });
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'sequence',
        width: 60,
        align: ColumnAlign.left,
        renderer: ({ record }) => (record?.index || 0) + 1,
      },
      {
        name: 'siteCode',
      },
      {
        name: 'fileCode',
        width: 150,
      },
      {
        name: 'fileName',
        width: 150,
        renderer: ({ value, record }) => {
          return <a onClick={() => handleOpenEditDrawer(record)}>{value}</a>;
        },
      },
      {
        name: 'fileLevel',
      },
      {
        name: 'version',
      },
      {
        name: 'editedDepartmentName',
        width: 150,
      },
      {
        name: 'editedDepartmentParentName',
        width: 150,
      },
      {
        name: 'editedByRealName',
      },
      {
        name: 'editedDate',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'publishDate',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'fileStatus',
      },
      {
        name: 'affliatedProcess',
        width: 150,
      },
      {
        name: 'cancelReason',
        width: 180,
      },
      {
        name: 'abandonReason',
        width: 180,
      },
      {
        name: 'operation',
        width: 280,
        header: intl.get(`${modelPrompt}.column.operation`).d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          return (
            <>
              <Button funcType={FuncType.flat} onClick={() => handleApproveView(record)}>
                {intl.get(`${modelPrompt}.operation.approvalHistory`).d('审批历史')}
              </Button>
              <Button
                funcType={FuncType.flat}
                onClick={() => {
                  history.push(`/hwms/system-document-management/detail/${record?.get('fileId')}`);
                }}
              >
                {intl.get(`${modelPrompt}.operation.filePreview`).d('预览')}
              </Button>
              <Button funcType={FuncType.flat} onClick={() => handleHistory(record)}>
                {intl.get(`${modelPrompt}.operation.history`).d('历史查看')}
              </Button>
              <Button
                funcType={FuncType.flat}
                onClick={() => handleOpenUpdateDrawer(record)}
                disabled={
                  record?.get('fileStatus') !== 'PUBLISHED' || record?.get('currentFlag') !== 'Y'
                }
              >
                {intl.get(`${modelPrompt}.operation.update`).d('升版')}
              </Button>
            </>
          );
        },
      },
    ];
  }, []);

  const handleChangeMode = e => {
    const value = e.target.value;
    setCurrentMode(value);
    tableDs.queryDataSet.setState('currentMode', value);
    if (value === 'LEVEL') {
      tableDs.queryDataSet.setState('currentSelectKey', currentLevel[0]);
      tableDs.queryDataSet.current?.set('fileLevel', currentLevel[0]);
      tableDs.queryDataSet.current?.set('affliatedProcess', undefined);
    } else {
      tableDs.queryDataSet.setState('currentSelectKey', currentProcess[0]);
      tableDs.queryDataSet.current?.set('fileLevel', undefined);
      tableDs.queryDataSet.current?.set('affliatedProcess', currentProcess[0]);
    }
    tableDs.query();
  };

  const handleSaveFile = (record, stagingFlag) => {
    return new Promise(async resolve => {
      const valRes = await record.validate(true);
      const releateRes = await releateForthFileDs.validate();
      if (!valRes || !releateRes) {
        return resolve(false);
      }
      const _request = stagingFlag ? stagingFile : saveFile;
      const res = await _request.run({
        params: {
          ...record?.toData(),
          subFileInfo: releateForthFileDs.toJSONData(),
        },
      });
      if (!res?.message) {
        notification.success({});
        handleQueryCurrentRecord(res);
      }
      return resolve(false);
    });
  };

  const handleQueryCurrentRecord = fileId => {
    if (!fileId) {
      return;
    }
    fileRecordDs.setQueryParameter('fileId', fileId);
    fileRecordDs.query().then(res => {
      const { editedDepartmentParent, subFileInfo, relatedFileList } = res || {};
      fileRecordDs.current?.set('originEditedDepartmentParent', editedDepartmentParent);
      subFileInfo?.forEach((item, index) => {
        item.sequence = index * 10 + 10;
      });
      releateForthFileDs.loadData([...subFileInfo || []]);
      relatedFileDs.loadData([...relatedFileList || []]);
    });
  };

  const handleOpenEditDrawer: (record?: any) => void = (record = null) => {
    handleQueryCurrentRecord(record?.get('fileId'));
    _modal = Modal.open({
      ...drawerPropsC7n({
        ds: fileRecordDs,
        canEdit: true,
      }),
      key: Modal.key(),
      title: record
        ? intl.get('hzero.common.button.edit').d('编辑')
        : intl.get('hzero.common.button.create').d('新建'),
      destroyOnClose: true,
      style: {
        width: 720,
      },
      onClose: () => {
        fileRecordDs.loadData([]);
        releateForthFileDs.loadData([]);
        relatedFileDs.loadData([]);
        tableDs.query(tableDs.currentPage);
      },
      children: (
        <EditDrawerComponent
          dataSet={fileRecordDs}
          adminFlag={adminFlag}
          releateForthFileDs={releateForthFileDs}
          relatedFileDs={relatedFileDs}
          editModalHandleCancel={editModalHandleCancel}
        />
      ),
      footer: <ModalFooter dataSet={fileRecordDs} />,
    });
  };

  const ModalFooter = observer(({ dataSet }) => {
    const btnList: any = [
      <Button onClick={() => _modal?.close()}>
        {intl.get('hzero.common.button.cancel').d('取消')}
      </Button>,
    ];
    if (['STAGING', 'UNPUBLISHED', 'PUBLISHED'].includes(dataSet.current?.get('fileStatus'))) {
      if (dataSet.current?.get('fileStatus') === 'STAGING') {
        btnList.push(
          <Button onClick={() => handleSaveFile(fileRecordDs.current, true)}>
            {intl.get(`${modelPrompt}.button.staging`).d('暂存')}
          </Button>,
        );
      }
      btnList.push(
        <Button
          color={ButtonColor.primary}
          onClick={() => handleSaveFile(fileRecordDs.current, false)}
        >
          {intl.get('hzero.common.button.ok').d('确定')}
        </Button>,
      );
    }
    return btnList;
  });

  // 文件升版
  const handleUpdateFile = record => {
    return new Promise(async resolve => {
      const valRes = await record.validate(true);
      if (!valRes) {
        return resolve(false);
      }
      const res = await updateFile.run({
        params: {
          ...record?.toData(),
        },
      });
      if (!res?.message) {
        notification.success({});
        tableDs.query(tableDs.currentPage);
        return resolve(true);
      }
      return resolve(false);
    });
  };

  // 打开升版弹窗
  const handleOpenUpdateDrawer: (record: any) => void = record => {
    const _record = updateDs.create({
      ...record.toData(),
      originEditedDepartmentParent: record?.get('editedDepartmentParent'),
      editedBy: userInfo.id,
      editedByRealName: userInfo.realName,
      editedDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      uuid: undefined,
    });
    Modal.open({
      ...drawerPropsC7n({
        ds: updateDs,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.update`).d('升版'),
      destroyOnClose: true,
      style: {
        width: 420,
      },
      onOk: () => handleUpdateFile(_record),
      children: <UpdateDrawerComponent record={_record} adminFlag={adminFlag} />,
    });
  };

  const handleSelectMenu = value => {
    if (currentMode === 'LEVEL') {
      tableDs.queryDataSet.current?.set('fileLevel', value[0]);
    } else {
      tableDs.queryDataSet.current?.set('affliatedProcess', value[0]);
    }
  };

  // 取消-确定按钮的回调
  const handleCancelReasonOk = () => {
    return new Promise(async resolve => {
      const valRes = await cancelReasonDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      const selectIds = selectedList.map((item: any) => item?.fileId);
      const res = await cancelFile.run({
        params: {
          fileId: selectIds,
          cancelReason: cancelReasonDs.current?.get('cancelReason'),
        },
      });
      if (res && res.success) {
        notification.success({});
        tableDs.query();
        return resolve(true);
      }
      return resolve(false);
    });
  };

  const handleCancel = () => {
    let _statusDisabledFlag = false;
    let _editDyDisabledFlag = false;
    selectedList.forEach((item: any) => {
      if (!_statusDisabledFlag && !['STAGING', 'UNPUBLISHED'].includes(item?.fileStatus)) {
        _statusDisabledFlag = true;
      }
      if (!_editDyDisabledFlag && item?.editedBy !== userInfo.id) {
        _editDyDisabledFlag = true;
      }
    });
    if (_statusDisabledFlag) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.cancelStatusError`)
          .d('仅允许对暂存与待发布状态的文件进行取消，请检查!'),
      });
      return;
    }
    if (_editDyDisabledFlag) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.cancelPersonError`)
          .d('只有对应编制人可以取消其新建的文件，请检查!'),
      });
      return;
    }
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.cancel`).d('取消文件'),
      destroyOnClose: true,
      style: {
        width: 420,
      },
      afterClose: () => cancelReasonDs?.reset(),
      onOk: handleCancelReasonOk,
      children: (
        <Form dataSet={cancelReasonDs}>
          <TextArea name="cancelReason" autoSize={{ minRows: 2, maxRows: 8 }} />
        </Form>
      ),
    });
  };

  const editModalHandleCancelReasonOk = (record: Record) => {
    return new Promise(async resolve => {
      const valRes = await cancelReasonDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      const res = await cancelFile.run({
        params: {
          fileId: [record.get('fileId')],
          cancelReason: cancelReasonDs.current?.get('cancelReason'),
        },
      });
      if (res && res.success) {
        notification.success({});
        record.init('fileStatus', 'CANCEL')
        return resolve(true);
      }
      return resolve(false);
    });
  };

  /**
   * 给编辑抽屉使用
   * 文件状态为暂存STAGING、待发布UNPUBLISHED时，可点击垃圾桶取消文件（具体逻辑同主界面取消逻辑）
   * @param record Record
   */
  const editModalHandleCancel = (record: Record) => {
    if (record.get('editedBy') !== userInfo.id) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.cancelPersonError`)
          .d('只有对应编制人可以取消其新建的文件，请检查!'),
      });
      return;
    }
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.cancel`).d('取消文件'),
      destroyOnClose: true,
      style: {
        width: 420,
      },
      afterClose: () => cancelReasonDs?.reset(),
      onOk: () => editModalHandleCancelReasonOk(record),
      children: (
        <Form dataSet={cancelReasonDs}>
          <TextArea name="cancelReason" autoSize={{ minRows: 2, maxRows: 8 }} />
        </Form>
      ),
    });
  }

  // 发布申请
  const handlePublishApply = () => {
    return new Promise(async resolve => {
      let _statusDisabledFlag = false;
      let _editDyDisabledFlag = false;
      selectedList.forEach((item: any) => {
        if (!_statusDisabledFlag && item?.fileStatus !== 'UNPUBLISHED') {
          _statusDisabledFlag = true;
        }
        if (
          !_editDyDisabledFlag &&
          item?.editedBy !== userInfo.id &&
          !adminFlag
        ) {
          _editDyDisabledFlag = true;
        }
      });
      if (_statusDisabledFlag) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.publishStatusError`)
            .d('待发布文件才可以提交发布申请，请检查！'),
        });
        return resolve(false);
      }
      if (_editDyDisabledFlag) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.publishPersonError`)
            .d('只有对应编制人或管理员可以提交发布申请，请检查！'),
        });
        return resolve(false);
      }
      const validateRes = await validatePublishFile.run({
        params: {
          qisSystemFiles: selectedList,
        },
      });
      if (validateRes && validateRes?.message) {
        return resolve(false);
      }
      if (validateRes && !validateRes.status) {
        handleExamineValidateModal(validateRes.errMessage);
      } else {
        handlePublishApplyConfirm('PUBLISH');
      }
      return resolve(true);
    });
  };

  // 发布申请校验弹窗
  const handleExamineValidateModal = result => {
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.publishTooltip`).d('发布提示'),
      style: {
        width: 420,
      },
      children: (
        <>
          {result.map(i => (
            <p>{i}?</p>
          ))}
        </>
      ),
      onOk: () => handlePublishApplyConfirm('PUBLISH'),
    });
  };

  // 发布申请确认回调
  const handlePublishApplyConfirm = flag => {
    Modal.open({
      key: Modal.key(),
      title:
        flag === 'PUBLISH'
          ? intl.get(`${modelPrompt}.title.publishApply`).d('发布申请')
          : intl.get(`${modelPrompt}.title.discardApply`).d('废弃申请'),
      style: {
        width: 420,
      },
      afterClose: () => examineDs.reset(),
      children: (
        <Form labelWidth={112} dataSet={examineDs} columns={1}>
          <Lov name="departmentLov" />
          <Lov name="responsLov" />
          <TextArea name="applicationReason" autoSize={{ minRows: 2, maxRows: 8 }} />
        </Form>
      ),
      onOk: () => handleApplyConfirm(flag),
      okText: intl.get(`${modelPrompt}.button.submit`).d('提交申请'),
    });
  };

  // 发布/废弃申请的确认回调
  const handleApplyConfirm = flag => {
    return new Promise(async resolve => {
      const validate = await examineDs.validate();
      if (!validate) {
        return resolve(false);
      }
      const _run = flag === 'PUBLISH' ? publishFile : discardFile;
      const res = await _run.run({
        params: {
          ...examineDs.current?.toData(),
          qisSystemFiles: selectedList,
        },
      });
      if (res && res?.message) {
        return resolve(false);
      }
      if (res?.rows && res?.rows?.length) {
        res.rows.forEach(item => {
          const arr = item.split(':');
          if (arr[0] === 'S') {
            notification.success({
              message: arr[1] || intl.get(`${modelPrompt}.message.success`).d('操作成功'),
            });
          } else {
            notification.error({
              message: arr[1] || intl.get(`${modelPrompt}.message.error`).d('操作失败'),
            });
          }
        });
      } else {
        notification.success({});
      }
      tableDs.query();
      return resolve(true);
    });
  };

  // 废弃申请
  const handleDiscardApply = () => {
    return new Promise(async resolve => {
      let _statusDisabledFlag = false;
      let _editDyDisabledFlag = false;
      selectedList.forEach((item: any) => {
        if (!_statusDisabledFlag && item?.fileStatus !== 'PUBLISHED') {
          _statusDisabledFlag = true;
        }
        if (
          !_editDyDisabledFlag &&
          item?.editedBy !== userInfo.id &&
          !adminFlag
        ) {
          _editDyDisabledFlag = true;
        }
      });
      if (_statusDisabledFlag) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.discardStatusError`)
            .d('已发布的数据才可以发起废弃申请，请检查！'),
        });
        return resolve(false);
      }
      if (_editDyDisabledFlag) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.discardPersonError`)
            .d('只有对应编制人或管理员可以发起废弃申请，请检查！'),
        });
        return resolve(false);
      }
      handlePublishApplyConfirm('DISCARD');
      return resolve(true);
    });
  };

  const handleFullTextSearch = () => {
    openTab({
      key: `/hwms/system-document-management/full-text-search`,
      title: intl.get(`${modelPrompt}.title.full-text`).d('体系文件管理-全文检索'),
    });
  };

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item key="0">
        <ExcelExport
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/export/for/ui/0`}
          method="get"
          buttonText={intl.get(`${modelPrompt}.button.exportNewRevision`).d('仅最新版本')}
          otherButtonProps={{
            type: 'text',
          }}
        />
      </Menu.Item>
      <Menu.Item key="1">
        <ExcelExport
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/export/for/ui/1`}
          method="get"
          buttonText={intl.get(`${modelPrompt}.button.exportHistoryRevision`).d('含历史版本')}
          otherButtonProps={{
            type: 'text',
          }}
        />
      </Menu.Item>
    </Menu>
  );

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('体系文件管理')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleOpenEditDrawer()}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.destory`,
              type: 'button',
              meaning: '列表页-废弃申请按钮',
            },
          ]}
          disabled={!selectedList?.length}
          onClick={() => handleDiscardApply()}
        >
          {intl.get(`${modelPrompt}.button.destory`).d('废弃申请')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.complish`,
              type: 'button',
              meaning: '列表页-发布申请按钮',
            },
          ]}
          disabled={!selectedList?.length}
          onClick={() => handlePublishApply()}
        >
          {intl.get(`${modelPrompt}.button.complish`).d('发布申请')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.cancel`,
              type: 'button',
              meaning: '列表页-取消按钮',
            },
          ]}
          disabled={!selectedList?.length}
          onClick={() => handleCancel()}
        >
          {intl.get('hzero.common.button.cancel').d('取消')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.hightCheck`,
              type: 'button',
              meaning: '列表页-高级检索按钮',
            },
          ]}
          onClick={() => handleFullTextSearch()}
        >
          {intl.get(`${modelPrompt}.button.hightCheck`).d('高级检索')}
        </PermissionButton>
        <Dropdown
          overlay={menu}
          placement={Placements.bottomRight}
        >
          <PermissionButton
            type="c7n-pro"
            loading={exportLoading}
            permissionList={[
              {
                code: `${modelPrompt}.list.button.export`,
                type: 'button',
                meaning: '列表页-导出按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.export').d('导出')}
          </PermissionButton>
        </Dropdown>
      </Header>
      <Content>
        <Row>
          <Col span={3} className={styles['left-menu']}>
            <Radio.Group
              onChange={handleChangeMode}
              value={currentMode}
              style={{ marginBottom: 8 }}
            >
              <Radio.Button value="LEVEL">
                {intl.get(`${modelPrompt}.radio.level`).d('层级视图')}
              </Radio.Button>
              <Radio.Button value="PROCESS">
                {intl.get(`${modelPrompt}.radio.process`).d('过程视图')}
              </Radio.Button>
            </Radio.Group>
            {currentMode === 'LEVEL' && (
              <Menu selectedKeys={currentLevel} onSelect={(key) => handleSelectMenu(key?.selectedKeys || [])} mode="inline">
                {(lovData?.levelList || []).map(item => (
                  <Menu.Item key={item.value}>
                    <Icon type="pie_chart_outlined" />
                    <span>{item.meaning}</span>
                  </Menu.Item>
                ))}
              </Menu>
            )}
            {currentMode === 'PROCESS' && (
              <Menu selectedKeys={currentProcess} onSelect={(key) => handleSelectMenu(key?.selectedKeys || [])} mode="inline">
                {(lovData?.processList || []).map(item => (
                  <Menu.Item key={item.value}>
                    <Icon type="pie_chart_outlined" />
                    {item.meaning}
                  </Menu.Item>
                ))}
              </Menu>
            )}
          </Col>
          <Col span={21} className={styles['right-content']}>
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={tableDs}
              columns={columns}
              searchCode="systemDocumentManagement_searchCode"
              customizedCode="systemDocumentManagement_customizedCode"
            />
          </Col>
        </Row>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(true),
      });
      const historyDs = new DataSet({
        ...historyDS(),
      });
      const updateDs = new DataSet({
        ...updateDS(),
      });
      const approveViewDs = new DataSet({
        ...approveViewDS(),
      });
      const examineDs = new DataSet({
        ...examineDS(),
      });
      const cancelReasonDs = new DataSet({
        ...cancelReasonDS(),
      });
      const fileRecordDs = new DataSet({
        ...tableDS(false),
        queryFields: [],
      });
      const releateForthFileDs = new DataSet({
        ...releateForthFileDS(),
      });
      const relatedFileDs = new DataSet({
        ...relatedFileDS(),
      });
      return {
        tableDs,
        updateDs,
        historyDs,
        approveViewDs,
        examineDs,
        cancelReasonDs,
        fileRecordDs,
        releateForthFileDs,
        relatedFileDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(SystemDocumentList),
);
