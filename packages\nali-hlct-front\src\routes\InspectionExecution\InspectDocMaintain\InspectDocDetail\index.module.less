.panel-title-code {
  font-weight: 400;
  margin-right: 30px;
}

.panel-title-item {
  margin-left: 10px;
  margin-right: 10px;
}

.panel-header-detail {
  background: rgba(0, 0, 0, 0.03);;
}

.accept-standard {
  :global {
    tbody > tr > td > label {
      padding: 0!important;
    }
    tbody > tr > td > div {
      padding: 0!important;
    }
  }
}

.panel-item {
  :global {
    .c7n-collapse-content .c7n-collapse-content-box {
      padding-top: 0 !important;
    }
  }
}

.source-doc-title {
  :global {
    .c7n-collapse-header > .c7n-collapse-extra {
      float: none !important;
      font-weight: normal;
      margin-left: 20px;
    }
    .c7n-alert >.c7n-alert-content >.c7n-alert-message {
      color: #0840f8;
    }
  }
}

.login-scan-form {
  :global {
    label .c7n-pro-input-suffix img {
      width: 18px;
    }
  }
}
