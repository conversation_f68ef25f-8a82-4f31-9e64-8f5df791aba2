// 加工件（工单）DS
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hmes.ProcessWorkorderMachinedPart';
const tenantId = getCurrentOrganizationId();

const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'workOrderObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.workOrderObj`).d('工单'),
      lovCode: 'HME_WORK_LOV',
      textField: 'workOrderNum',
      valueField: 'workOrderId',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workOrderNum',
      label: intl.get(`${modelPrompt}.model.execute.workOrderObj`).d('生产工单'),
      type: FieldType.string,
      bind: 'workOrderObj.workOrderNum',
    },
    {
      name: 'workOrderId',
      type: FieldType.string,
      bind: 'workOrderObj.workOrderId',
    },
    // {
    //   name: 'identificationField',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.identificationField`).d('在制标识'),
    // },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      // required: true,
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      // required: true,
    },
    {
      name: 'opProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.opProcess`).d('工序进度'),
    },
    {
      name: 'customerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerDesc`).d('客户信息'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('工单备注'),
    },
    {
      name: 'standardBeat',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.standardBeat`).d('标准节拍'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('在制标识'),
    },
    {
      name: 'pitStopDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.pitStopDate`).d('进站时间'),
    },
    {
      name: 'currentProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentProcess`).d('当前工序'),
    },
    {
      name: 'nextProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.nextProcess`).d('下一工序'),
    },
    {
      name: 'processedRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('调度备注'),
    },
    {
      name: 'processedTime',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.processedTime`).d('加工时长'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
    },
  ],
});

const workOrderLovDS = () => ({
  selection: 'single',
  autoQuery: false,
  autoCreate: false,
  paging: true,
  dataKey: 'content',
  queryFields: [
    {
      name: 'workOrderNum',
      labelWidth: 100,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'materialCode',
      labelWidth: 150,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    }
  ],
  fields: [
    {
      name: 'materialId',
    },
    {
      name: 'materialInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialInfo`).d('物料编码/版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'plantTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.plantTime`).d('计划开始时间-结束时间'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'woRenderQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woRenderQty`).d('完工/工单数量'),
    },
    {
      name: 'completedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completedQty`).d('工单完工数量'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('工单数量'),
    },
    {
      name: 'workOrderId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-eo-sum-results/work-order-lov/ui`,
        method: 'POST',
      };
    }
  },
});

export { detailDS, workOrderLovDS };
