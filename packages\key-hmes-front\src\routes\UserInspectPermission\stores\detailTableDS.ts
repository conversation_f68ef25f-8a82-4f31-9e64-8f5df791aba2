/**
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-27
 * @description 用户检验权限维护详情
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.userInspectPermission';
const tenantId = getCurrentOrganizationId();


const detailTableDS = (): DataSetProps => ({
  autoQuery: false,
  dataKey: 'rows',
  totalKey: 'rows',
  autoCreate: true,
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-user-inspect-permission/detail/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'userLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.userName`).d('员工账号'),
      lovCode: 'MT.USER.ORG',
      lovPara: { tenantId },
      required: true,
      ignore: FieldIgnore.always,
      textField: 'realName',
      valueField: 'id',
    },
    {
      name: 'userId',
      bind: 'userLov.id',
    },
    {
      name: 'userName',
      bind: 'userLov.realName',
    },
    {
      name: 'loginName',
      bind: 'userLov.loginName',
    },
    {
      name: 'userDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userDesc`).d('员工姓名'),
      bind: 'userLov.realName',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.ORGANIZATION',
      textField: 'organizationDesc',
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteType: 'MANUFACTURING',
            organizationType: 'SITE',
            userId: record.get('userId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('userLov');
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.organizationId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.organizationDesc',
    },
  ],
});

const listDS = (): DataSetProps => ({
  autoQuery: false,
  autoLocateFirst: true,
  selection: false,
  dataKey: 'rows',
  paging: false,
  primaryKey: 'inspectBusinessType',
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessTypeDesc`).d('检验业务类型'),
      name: 'inspectBusinessTypeDesc',
    },
    {
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.permissionFlag`).d('是否有权限'),
      trueValue: 'Y',
      falseValue: 'N',
      name: 'permissionFlag',
    },
    {
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.createFlag`).d('创建权限'),
      trueValue: 'Y',
      falseValue: 'N',
      name: 'createFlag',
    },
    {
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.editFlag`).d('编辑检验方案权限'),
      trueValue: 'Y',
      falseValue: 'N',
      name: 'editFlag',
    },
    {
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.excuteFlag`).d('执行权限'),
      trueValue: 'Y',
      falseValue: 'N',
      name: 'excuteFlag',
    },
    {
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.inspectPermissionFlag`).d('初检权限'),
      trueValue: 'Y',
      falseValue: 'N',
      name: 'inspectPermissionFlag',
    },
    {
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.reinspectPermissionFlag`).d('复检权限'),
      trueValue: 'Y',
      falseValue: 'N',
      name: 'reinspectPermissionFlag',
    },
  ],
  record: {
    dynamicProps: {
      // 编辑状态才可选
      selectable: record => {
        const { dataSet } = record;
        const editing = record.getState('editing');
        const recordData = record.toData();
        const { materialList, materialCategoryList, organizationList, taskList } = recordData;

        const unDisabledTypeList: any = [];
        const focusRecordData =
          dataSet.selected
            .find(
              _record =>
                _record.get('materialList')?.length ||
                _record.get('materialCategoryList')?.length ||
                _record.get('organizationList')?.length ||
                _record.get('taskList')?.length,
            )
            ?.toData() || {};

        if (
          (focusRecordData.materialList && focusRecordData.materialList.length > 0) ||
          (focusRecordData.materialCategoryList && focusRecordData.materialCategoryList.length > 0)
        ) {
          unDisabledTypeList.push('materialList');
        }
        if (focusRecordData.organizationList && focusRecordData.organizationList.length > 0) {
          unDisabledTypeList.push('organizationList');
          unDisabledTypeList.push(focusRecordData.organizationList[0].organizationType);
        }
        if (focusRecordData.taskList && focusRecordData.taskList.length > 0) {
          unDisabledTypeList.push('taskList');
        }

        const listList: any = [];
        if (
          (materialList && materialList.length > 0) ||
          (materialCategoryList && materialCategoryList.length > 0)
        ) {
          listList.push('materialList');
        }
        if (organizationList && organizationList.length > 0) {
          listList.push('organizationList');
          listList.push(organizationList[0].organizationType);
        }
        if (taskList && taskList.length > 0) {
          listList.push('taskList');
        }

        if (!editing) {
          return false;
        }
        if (
          unDisabledTypeList &&
          unDisabledTypeList.length > 0 &&
          !listList.every(item => unDisabledTypeList.includes(item))
        ) {
          return false;
        }
        if (editing && record.get('permissionFlag') !== 'Y') {
          return false;
        }
        return true;
      },
      disabled: record => {
        const { dataSet } = record;
        const selectedTypeList: any = [];
        const editing = record.getState('editing');
        dataSet.selected.forEach(selectRecord => {
          selectedTypeList.push(selectRecord.get('inspectBusinessType'));
        });
        const inspectBusinessType = record.get('inspectBusinessType');
        if (!editing) {
          return false;
        }
        if (selectedTypeList.length === 0) {
          return false;
        }
        if (selectedTypeList.includes(inspectBusinessType)) {
          return false;
        }
        return true;
      },
    },
  },
  events: {
    update: ({ record, name, value }) => {
      switch (name) {
        case 'permissionFlag':
          record.set('createFlag', value);
          record.set('editFlag', value);
          record.set('executeFlag', value);
          record.set('inspectPermissionFlag', value);
          record.set('reinspectPermissionFlag', value);
          record.setState('editing', value === 'Y');
          break;
        case 'createFlag':
          if (
            record.get('createFlag') === 'N' &&
            record.get('editFlag') === 'N' &&
            record.get('executeFlag') === 'N'
          ) {
            record.set('permissionFlag', 'N');
          }
          break;
        case 'editFlag':
          if (
            record.get('createFlag') === 'N' &&
            record.get('editFlag') === 'N' &&
            record.get('executeFlag') === 'N'
          ) {
            record.set('permissionFlag', 'N');
          }
          break;
        case 'executeFlag':
          record.set('inspectPermissionFlag', value);
          record.set('reinspectPermissionFlag', value);
          if (
            record.get('createFlag') === 'N' &&
            record.get('editFlag') === 'N' &&
            record.get('executeFlag') === 'N'
          ) {
            record.set('permissionFlag', 'N');
          }
          break;
        case 'inspectPermissionFlag':
          if (
            record.get('inspectPermissionFlag') === 'N' &&
            record.get('reinspectPermissionFlag') === 'N'
          ) {
            record.set('executeFlag', 'N');
          }
          break;
        case 'reinspectPermissionFlag':
          if (
            record.get('inspectPermissionFlag') === 'N' &&
            record.get('reinspectPermissionFlag') === 'N'
          ) {
            record.set('executeFlag', 'N');
          }
          break;
        default:
          break;
      }
    },
    batchSelect: ({ dataSet, records }) => {
      const focusRecord = dataSet.selected.find(
        _record =>
          _record.get('materialList')?.length ||
          _record.get('materialCategoryList')?.length ||
          _record.get('organizationList')?.length ||
          _record.get('taskList')?.length,
      );
      records.forEach(record => {
        record.init('expandedKeys', []);
        record.init('checkedKeys', []);
      });
      dataSet.locate(focusRecord?.index || 0);
    },
    batchUnselect: ({ dataSet, records }) => {
      const focusRecord = dataSet.selected.find(
        _record =>
          _record.get('materialList')?.length ||
          _record.get('materialCategoryList')?.length ||
          _record.get('organizationList')?.length ||
          _record.get('taskList')?.length,
      );
      records.forEach(record => {
        record.init('expandedKeys', []);
        record.init('checkedKeys', []);
      });
      dataSet.locate(focusRecord?.index || 0);
    },
  },
});

const materialCategoryDS = (): DataSetProps => ({
  selection: false,
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCategoryCode`).d('物料类别编码'),
      name: 'materialCategoryCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCategoryDesc`).d('物料类别描述'),
      name: 'materialCategoryDesc',
    },
    {
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.permissionFlag`).d('是否有权限'),
      trueValue: 'Y',
      falseValue: 'N',
      name: 'permissionFlag',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.taskCategory`).d('任务类别'),
      name: 'taskCategoryLov',
      lovCode: 'MT.QMS.TASK_CATEGORY',
      ignore: FieldIgnore.always,
      textField: 'taskCategory',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => !record.get('siteId'),
      },
    },
    {
      name: 'taskCategory',
      bind: 'taskCategoryLov.taskCategory',
    },
    {
      name: 'taskCategoryDesc',
      bind: 'taskCategoryLov.taskCategory',
    },
  ],
});

const materialDS = (): DataSetProps => ({
  selection: false,
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      name: 'materialCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      name: 'materialName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryCodeOfMaterial`).d('物料所属类别编码'),
      name: 'materialCategoryCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryDescOfMaterial`).d('物料所属类别描述'),
      name: 'materialCategoryDesc',
    },
    {
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.permissionFlag`).d('是否有权限'),
      trueValue: 'Y',
      falseValue: 'N',
      name: 'permissionFlag',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.taskCategory`).d('任务类别'),
      name: 'taskCategoryLov',
      lovCode: 'MT.QMS.TASK_CATEGORY',
      textField: 'taskCategory',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => !record.get('siteId'),
      },
    },
    {
      name: 'taskCategory',
      bind: 'taskCategoryLov.taskCategory',
    },
    {
      name: 'taskCategoryDesc',
      bind: 'taskCategoryLov.taskCategory',
    },
  ],
});

const taskDS = (): DataSetProps => ({
  selection: false,
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskCategory`).d('任务类别'),
      name: 'taskCategory',
    },
    {
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.permissionFlag`).d('是否有权限'),
      trueValue: 'Y',
      falseValue: 'N',
      name: 'permissionFlag',
    },
  ],
});

const queryDS = (): DataSetProps => ({
  autoCreate: true,
  fields: [
    {
      name: 'siteId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.addMaterialCategoryLov`).d('批量新增物料类别'),
      labelWidth: '120',
      lovCode: 'MT.METHOD.MATERIAL_CATEGORY',
      name: 'addMatsLov',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => !record.get('siteId'),
      },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.addMaterialLov`).d('批量新增物料'),
      name: 'addMatLov',
      multiple: true,
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => !record.get('siteId'),
      },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.addTaskLov`).d('批量新增任务类别'),
      name: 'addTaskLov',
      multiple: true,
      labelWidth: '120',
      lovCode: 'MT.QMS.TASK_CATEGORY',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => !record.get('siteId'),
      },
    },
  ],
});

const organizationTableDS: (canEdit: boolean) => DataSetProps = canEdit => ({
  selection: DataSetSelection.multiple,
  paging: false,
  fields: [
    {
      name: 'organizationId',
      type: FieldType.number,
    },
    {
      name: 'organizationRelId',
      type: FieldType.number,
    },
    {
      name: 'organizationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationType`).d('组织类型'),
      lookupCode: 'MT.MODEL.USER_ORGANIZATION_TYPE',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'organizationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationCode`).d('组织编码'),
    },
  ],
  record: {
    selectable: canEdit,
  },
});

export {
  detailTableDS,
  materialCategoryDS,
  materialDS,
  listDS,
  taskDS,
  queryDS,
  organizationTableDS,
};
