/**
 * @Description: 新区域维护-详情页组件
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 13:19:18
 * @LastEditTime: 2021-02-18 13:19:18
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, NumberField, DateTimePicker, Select, Lov } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { isNull } from 'lodash';

const modelPrompt = 'tarzan.model.org.area';

const PlanInfoTab = props => {
  const { dataSet, canEdit, columns = 1, disabledFollow, focus = true } = props;

  // 改为两位小数
  const handleChangeNumber = (field, value) => {
    const data = {};
    if (!isNull(value)) {
      data[field] = value.toFixed(2);
      dataSet.current.set(data);
    }
  };

  return (
    <Form
      disabled={!canEdit || focus}
      dataSet={dataSet}
      columns={columns}
      labelLayout="horizontal"
      labelWidth={112}
    >
      <DateTimePicker name="planStartTime" />
      <NumberField
        name="releaseTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('releaseTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <Select name="forceCompleteCycle" />
      <NumberField
        name="demandTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('demandTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <NumberField
        name="orderTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('orderTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <Select name="schedulingNode" />
      <NumberField
        name="fixTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('fixTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <Select name="schedulingAlgorithm" />
      <NumberField
        name="delayTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('delayTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.hour`).d('小时')}`
            : ''
        }
      />
      <Lov name="followAreaLov" placeholder=" " disabled={disabledFollow} noCache />
      <NumberField
        name="rollingPeriod"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('rollingPeriod', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <NumberField
        name="forwardPlanningTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('forwardPlanningTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <Select name="orgSelectionAlgorithm" />
    </Form>
  );
};

export default PlanInfoTab;
