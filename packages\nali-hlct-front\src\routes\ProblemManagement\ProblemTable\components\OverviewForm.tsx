import React from 'react';
import { Content } from 'components/Page';
import icon1 from '@/assets/pictureSvg/111.svg';
import icon2 from '@/assets/pictureSvg/222.svg';
import icon3 from '@/assets/pictureSvg/333.svg';
import icon4 from '@/assets/pictureSvg/444.svg';
import icon5 from '@/assets/pictureSvg/555.svg';
import intl from 'utils/intl';
import './index.less';

const modelPrompt = 'problem.table';

const OverviewForm = props => {
  const { history, ds } = props;
  const handleJumpToList = (state = {}) => {
    history.push({
      pathname: `/hwms/problem-management/problem-management-platform/list`,
      state: {
        stateType: 'query',
        ...state,
      },
    });
  };

  return (
    <div className="hmes-style">
      <Content className="overview_form_content">
        <div className="content_container">
          <div
            onClick={() =>
              handleJumpToList({ problemStatus: 'NEW,DRAFT,PUBLISH', onlyForMeFalg: 'Y' })
            }
            style={{ cursor: 'pointer' }}
          >
            <img src={icon1} alt="" />
          </div>
          <div className="title_container">
            <h1 className="title_h1">{ds.current.get('createdCount') || 0}</h1>
            <span className="title_span">{intl.get(`${modelPrompt}.created`).d('已创建')}</span>
          </div>
        </div>
        <div className="content_container" style={{ borderLeft: '1px solid #f4f5f7' }}>
          <div
            onClick={() =>
              handleJumpToList({
                problemStatus: 'RELEASED,FOLLOWING,CLOSING,FREEZE_APPLY,SRM_HANDLE',
                onlyForMeFalg: 'Y',
                onlyExtensionFalg: 'N',
              })
            }
            style={{ cursor: 'pointer' }}
          >
            <img src={icon2} alt="" />
          </div>
          <div className="title_container">
            <h1 className="title_h1">{ds.current.get('processingCount') || 0}</h1>
            <span className="title_span">
              {intl.get(`${modelPrompt}.being.processed`).d('处理中')}
            </span>
          </div>
        </div>
        <div className="content_container" style={{ borderLeft: '1px solid #f4f5f7' }}>
          <div
            onClick={() =>
              handleJumpToList({
                problemStatus: 'RELEASED,FOLLOWING,CLOSING,FREEZE_APPLY,SRM_HANDLE',
                onlyForMeFalg: 'Y',
                onlyExtensionFalg: 'Y',
              })
            }
            style={{ cursor: 'pointer' }}
          >
            <img src={icon3} alt="" />
          </div>
          <div className="title_container">
            <h1 className="title_h1">{ds.current.get('overdueCount') || 0}</h1>
            <span className="title_span">{intl.get(`${modelPrompt}.overdue`).d('已逾期')}</span>
          </div>
        </div>
        <div className="content_container" style={{ borderLeft: '1px solid #f4f5f7' }}>
          <div
            onClick={() => handleJumpToList({ problemStatus: 'CLOSED', onlyForMeFalg: 'Y' })}
            style={{ cursor: 'pointer' }}
          >
            <img src={icon4} alt="" />
          </div>
          <div className="title_container">
            <h1 className="title_h1">{ds.current.get('closedCount') || 0}</h1>
            <span className="title_span">{intl.get(`${modelPrompt}.closed`).d('已关闭')}</span>
          </div>
        </div>
        <div className="content_container" style={{ borderLeft: '1px solid #f4f5f7' }}>
          <div
            onClick={() => handleJumpToList({ problemStatus: 'FREEZE', onlyForMeFalg: 'Y' })}
            style={{ cursor: 'pointer' }}
          >
            <img src={icon5} alt="" />
          </div>
          <div className="title_container">
            <h1 className="title_h1">{ds.current.get('frozenCount') || 0}</h1>
            <span className="title_span">{intl.get(`${modelPrompt}.frozen`).d('已冻结')}</span>
          </div>
        </div>
      </Content>
    </div>
  );
};

export default OverviewForm;
