/**
 * @Description: 量具检定平台-主界面
 */

import React, { useState } from 'react';
import {Button, DataSet,Modal} from 'choerodon-ui/pro';
import { Tabs } from 'choerodon-ui';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import {ButtonColor} from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import {useDataSetEvent} from "utils/hooks";
import notification from 'utils/notification';
import {observer} from 'mobx-react';
import PrintElement from '@components/tarzan-ui/PrintButton/PrintElement';
import myInstance from "@utils/myAxios";
import { API_HOST } from '@/utils/constants';
import { BASIC } from '@utils/config';
import ApplyTab from './ApplyTab';
import InspectTab from "./InspectTab";
import { headDS, lineDS, inspectDS } from '../stories';

const modelPrompt = 'tarzan.inspectExecute.MeasureHavePlatform';

const tenantId = getCurrentOrganizationId();

const TabPane = Tabs.TabPane;

const MeterManagementList = observer(props => {
  const {
    headDs,
    lineDs,
    inspectDs,
    history,
    match: { params },
  } = props;
  const tab = params.tab || '1';

  const [tabKey, setTabKey] = useState(tab);
  const [selectedRecords, setSelectedRecords] = useState<Array<any>>([]);

  useDataSetEvent(headDs, 'select', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
  });
  useDataSetEvent(headDs, 'selectAll', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
  });
  useDataSetEvent(headDs, 'unselect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
  });
  useDataSetEvent(headDs, 'unselectAll', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
  });

  const changeTab = key => {
    setTabKey(key);
  };


  const cancelApply = () => {
    const selected = headDs.selected.map(item => {
      return item.get('applicationDocId');
    });
    myInstance
      .post(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/application/cancel/ui`,
        selected,
      )
      .then(res => {
        if (res.data.success) {
          notification.success({});
          headDs.query(headDs.currentPage);
        } else {
          notification.error({
            message: res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
          });
        }
      });
  };


  const createApply = () => {
    history.push(`/hmes/measure-have/platform/dist/create`);
  };

  const getHtml = (OKList) => {
    const url = `${API_HOST}${BASIC.HRPT_COMMON}/v1/${tenantId}/label-prints/view/html?labelTemplateCode=QIS_MS_PLATFORM_LABEL_OK&organizationId=${tenantId}&inspectDocIdList=${OKList.join(',')}`;
    myInstance.get(url).then(res => {
      if (res.statusText === 'OK' && (res.data || {}).label) {
        // @ts-ignore
        document.getElementById('pdf').innerHTML = res.data.label.replace(/↵/gm, ''); // 去掉回车换行;
      } else {
        notification.error({
          message: res.data.message,
          description: '',
        });
      }
    });
  };
  const getHtml2 = (NGList) => {
    const url = `${API_HOST}${BASIC.HRPT_COMMON}/v1/${tenantId}/label-prints/view/html?labelTemplateCode=QIS_MS_PLATFORM_LABEL_NG&organizationId=${tenantId}&inspectDocIdList=${NGList.join(',')}`;
    myInstance.get(url).then(res => {
      if (res.statusText === 'OK' && (res.data || {}).label) {
        // @ts-ignore
        document.getElementById('pdf2').innerHTML = res.data.label.replace(/↵/gm, ''); // 去掉回车换行;
      } else {
        notification.error({
          message: res.data.message,
          description: '',
        });
      }
    });
  };


  const print = async () => {
    const printDomRef = React.createRef(null);
    const printDomRef2 = React.createRef(null);
    const OKList = [];
    const NGList = [];
    inspectDs.selected.map((item) => {
      if (item.get('inspectResult') === 'OK') {
        OKList.push(item.get('inspectDocId'))
      } else {
        NGList.push(item.get('inspectDocId'))
      }
    })
    const html = OKList.length ? await getHtml(OKList) : '';
    const html2 = NGList.length ? await getHtml2(NGList) : '';

    // @ts-ignore
    await Modal.open({
      drawer: true,
      maskClosable: true,
      key: 'ModalKey',
      destroyOnClose: true,
      closable: true,
      style: {
        width: 720,
      },
      title: intl.get('tarzan.common.title.preview').d('预览'),
      children: (
        <div id="pdfContainer">
          <div
            id="pdf"
            style={{ paddingBottom: 20, overflow: 'hidden' }}
            // @ts-ignore
            ref={printDomRef}
            // @ts-ignore
            dangerouslySetInnerHTML={{ __html: html }}
          />
          <div
            id="pdf2"
            style={{ paddingBottom: 20, overflow: 'hidden' }}
            // @ts-ignore
            ref={printDomRef2}
            // @ts-ignore
            dangerouslySetInnerHTML={{ __html: html2 }}
          />
        </div>
      ),
      footer: () => (
        <div>
          <Button
            icon="print"
            onClick={() => {
              PrintElement({
                content: document.getElementById('pdfContainer'),
              });
            }}
          >
            {intl.get('tarzan.common.button.print').d('打印')}
          </Button>
        </div>
      ),
    });
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.name`).d('量具检定平台')}>
        {
          tabKey === '1' && (
            <>
              <Button
                icon="add"
                color={ButtonColor.primary}
                onClick={createApply}
              >
                {intl.get(`${modelPrompt}.button.create`).d('新建申请')}
              </Button>
              <Button
                disabled={!selectedRecords.length}
                icon="cancel"
                onClick={cancelApply}
              >
                {intl.get(`${modelPrompt}.button.cancelApply`).d('取消申请')}
              </Button>
            </>
          )

        }
        {tabKey === '2' && (
          <>
            <Button
              disabled={!inspectDs.selected.length}
              onClick={print}
            >
              {intl.get(`${modelPrompt}.button.tagPrint`).d('标签打印')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Tabs defaultActiveKey={tabKey} style={{ backgroundColor: '#ffff' }} onChange={changeTab}>
          <TabPane tab={intl.get(`${modelPrompt}.title.apply`).d('检定申请')} key="1">
            <ApplyTab headDs={headDs} lineDs={lineDs} history={history}/>
          </TabPane>
          <TabPane tab={intl.get(`${modelPrompt}.title.number`).d('检定单')} key="2">
            <InspectTab inspectDs={inspectDs} history={history}/>
          </TabPane>
        </Tabs>
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [
    'tarzan.common',
    modelPrompt,
  ],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      const inspectDs = new DataSet(inspectDS());
      return {
        headDs,
        lineDs,
        inspectDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MeterManagementList),
);
