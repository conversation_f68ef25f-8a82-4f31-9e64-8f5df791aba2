/**
 * @feature 产品加工参数补录
 */
import React, { useEffect, useState } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { observer } from 'mobx-react';
import withProps from 'utils/withProps';
import { initialDs } from './stories/InitialDs';
import CommonButton from './CommonButton';


const modelPrompt = 'tarzan.inventory.initial.model';

/**
 * 头行结构的表单示例
 */
const Initial = observer(props => {
  const [deleteFlag, setState] = useState(true);
  const [panelTableColumns, setPanelTableColumns] = useState([
    { name: 'seq', renderer: ({ record }) => <span>{record.index + 1}</span> },
    { name: 'status' },
    { name: 'message' },
    { name: 'siteCode' },
    { name: 'equipmentCode' },
    { name: 'equipmentName' },
    { name: 'materialCode' },
    { name: 'revisionCode' },
    { name: 'materialName' },
    { name: 'workcellCode' },
    { name: 'processBarcode' },
    { name: 'recordDate' },
    { name: 'userName' },
  ]);


  useEffect(() => {
    if (props.dataSet) {
      props.dataSet.addEventListener('load', () => {
        const dataAll = props.dataSet.toData();
        const titleList = dataAll.length > 0 ? dataAll[0].columnsList : [];
        const columnsList = dataAll.length > 0 ? dataAll[0].titleList : [];
        if (dataAll.length > 0 && titleList && titleList.length > 0) {
          const dyComlums = [
            {
              title: '序号',
              name: 'seq',
              renderer: ({ record }) => <span>{record.index + 1}</span>,
            },
          ];
          const dyComlumsTemp = [];
          titleList.forEach((item, index) => {
            if (item !== '行号') {
              dyComlumsTemp.push({ title: item, name: columnsList[index] })
            }
          });
          const finalColums = dyComlums.concat(dyComlumsTemp)
          finalColums.forEach(ele1 => {
            props.dataSet.addField(ele1.name, {
              name: ele1.name,
              label: ele1.title,
            });
          });
          setPanelTableColumns(finalColums);
        } else {
          setPanelTableColumns([
            {
              name: 'seq',
              renderer: ({ record }) => <span>{record.index + 1}</span>,
            },
            { name: 'status' },
            { name: 'message' },
            { name: 'siteCode' },
            { name: 'equipmentCode' },
            { name: 'equipmentName' },
            { name: 'materialCode' },
            { name: 'revisionCode' },
            { name: 'materialName' },
            { name: 'workcellCode' },
            { name: 'processBarcode' },
            { name: 'recordDate' },
            { name: 'userName' },
          ]);
        }
      });
    }
  }, []);

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl
          .get(`${modelPrompt}.ProductProcessingParametersSupplement`)
          .d('产品加工参数补录')}
        header={
          <CommonButton dataSet={props.dataSet} deleteFlag={deleteFlag} setState={setState} />
        }
      >
        <Table
          bordered={false}
          dataSet={props.dataSet}
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          searchCode="ProductProcessingParametersSupplement"
          customizedCode="ProductProcessingParametersSupplement"
          columns={panelTableColumns}
        />
        ,
      </PageHeaderWrapper>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.product.processing.parameters.supplement', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...initialDs(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Initial),
);
