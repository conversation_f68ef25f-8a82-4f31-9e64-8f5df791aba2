import React, { useState } from 'react';
import intl from 'utils/intl';
import { Button, DataSet, Table, Modal, Col, Switch, Spin } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Content, Header } from 'components/Page';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, ColumnAlign } from 'choerodon-ui/pro/lib/table/interface';
import { Upload } from 'choerodon-ui';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { HZERO_FILE } from 'utils/config';
import uuid from 'uuid/v4';
import FileItem from '@/components/FileItem';
import request from 'utils/request';
import notification from 'utils/notification';

import { getAccessToken, getCurrentOrganizationId } from 'utils/utils';
import { tableDS } from './stores/index';

const modelPrompt = 'tarzan.rohs-manage';
const dom = props => {
  const [loading, setLoading] = useState(false);
  const { tableDs } = props;
  const url = `${HZERO_FILE}/v1/${getCurrentOrganizationId()}/files/attachment/multipart`;
  const uuidNew = uuid();

  const uploadFile = () => {
    setLoading(false);
    notification.success({ message: '上传成功' });
    // 判断是否是新增uuid 是则执行保存逻辑
    if (!tableDs?.current?.get('filePath')) {
      tableDs?.current?.set('filePath', uuidNew);
      tableDs?.current?.set('uploadTime', new Date());
      // 数据保存
      handleSave();
    }
  };

  const beforeUpload = () => {
    setLoading(true);
    return true;
  };

  const onError = () => {
    setLoading(false);
    notification.error({ message: '上传失败' });
  };

  const uploadPorps = {
    name: 'file',
    showUploadList: false,
    headers: {
      authorization: `Bearer ${getAccessToken()}`,
    },
    action: url,
    onSuccess: uploadFile,
    beforeUpload,
    onError: onError,
    showUploadBtn: false,
    data: {
      organizationId: getCurrentOrganizationId(),
      attachmentUUID: tableDs?.current?.get('filePath') || uuidNew,
      bucketName: 'file-mes',
    },
  };

  const columns: ColumnProps[] = [
    {
      name: 'materialLov',
      editor: record => record.status === 'add' || record.getState('editing'),
    },
    {
      name: 'materialName',
    },
    {
      name: 'supplierLov',
      editor: record => record.status === 'add' || record.getState('editing'),
    },
    {
      name: 'supplierName',
    },
    {
      name: 'roHsFlag',
      renderer: ({ record }) => {
        return record?.get('filePath') ? '是' : '否';
      },
    },
    {
      name: 'endTime',
      editor: record => record.status === 'add' || record.getState('editing'),
    },
    {
      name: 'uploadTime',
      width: 160,
    },
    {
      name: 'enabledFlag',
      editor: record => (record.status === 'add' || record.getState('editing')) && <Switch />,
      renderer: ({ value }) => {
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        );
      },
    },
    {
      header: intl.get('hzero.common.button.uuid').d('附件'),
      width: 200,
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a onClick={() => previewCov(record)}>
              {intl.get('hzero.common.button.query.uuid').d('查看')}
            </a>
            <Upload disabled={!(record?.status === 'add' || record?.getState('editing'))} className="custom-upload" {...uploadPorps}>
              <Button funcType={FuncType.flat} color={ButtonColor.primary}>
                上传
              </Button>
            </Upload>
            <a onClick={() => handleDeleteUUid(record)}>
              {intl.get('hzero.common.button.delete.uuid').d('删除')}
            </a>
          </span>
        );
      },
    },
    {
      header: intl.get('hzero.common.button.action').d('操作'),
      width: 100,
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      renderer: ({ record }) => {
        return record?.status === 'add' ? (
          <span className="action-link">
            <a onClick={() => handleClearLine(record)}>
              {intl.get('hzero.common.button.clean').d('清除')}
            </a>
            <a onClick={() => handleSaveLine(record)}>
              {intl.get('hzero.common.button.save').d('保存')}
            </a>
          </span>
        ) : record?.getState('editing') ? (
          <span className="action-link">
            <a onClick={() => handleCancelLine(record)}>
              {intl.get('hzero.common.button.cancel').d('取消')}
            </a>
            <a onClick={() => handleSaveLine(record)}>
              {intl.get('hzero.common.button.save').d('保存')}
            </a>
          </span>
        ) : (
          <span className="action-link">
            <a onClick={() => handleEditLine(record)}>
              {intl.get('hzero.common.button.edit').d('编辑')}
            </a>
          </span>
        );
      },
    },
  ];

  /**
   * 删除附件
   */
  const handleDeleteUUid = async record => {
    const deleteData = record.toData();
    const res = await request(`${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/mt-rohs`, {
      method: 'POST',
      body: {
        ...deleteData,
        filePath: null,
        uploadTime: null,
      },
    });
    if (res && !res.failed) {
      tableDs.query();
      return notification.success({ message: '删除成功' });
    } else {
      return notification.error({ message: res.message });
    }
  };

  /**
   * 编辑
   */
  const handleEditLine = record => {
    // 将其他的记录编辑或者新增的数据重置
    for (let index = 0; index < tableDs.records.length; index++) {
      const record = tableDs.records[index];
      if (record?.status === 'add') {
        handleClearLine(record);
      }

      if (record?.getState('editing')) {
        handleCancelLine(record);
      }
    }
    record.setState('editing', true);
  };

  /**
   * 取消
   */
  const handleCancelLine = record => {
    record.reset();
    record.setState('editing', false);
  };

  /**
   * 删除新增行
   */
  const handleClearLine = record => {
    tableDs.remove(record);
  };

  /**
   * 保存
   */
  const handleSaveLine = async record => {
    const validate = await record.validate();
    if (validate) {
      const res = await tableDs.submit();
      if (res && !res.failed) {
        tableDs.query();
      }
    }
  };

  // 附件保存
  const handleSave = async () => {
    const saveData = tableDs!.current!.toData();
    const res = await request(`${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/mt-rohs`, {
      method: 'POST',
      body: saveData,
    });
    if (res && !res.failed) {
      tableDs.query();
      return notification.success({ message: '上传成功' });
    } else {
      return notification.error({ message: res.message });
    }
  };

  // 数据预览
  const previewCov = async record => {
    const uuid = record?.get('filePath');
    const fileList: any[] = [];
    if (uuid) {
      // 根据uuid获取对应的文件
      const fileArray = await request(`/hfle/v1/${getCurrentOrganizationId()}/files/${uuid}/file`, {
        method: 'GET',
        query: {
          uuid,
        },
      });

      for (let index = 0; index < fileArray.length; index++) {
        const file = fileArray[index];
        fileList.push({
          fileName: file.fileName,
          fileUrl: file.fileUrl,
          mediaType: file.fileType,
        });
      }
    }
    Modal.open({
      title: 'ROHS文件查看',
      closable: true,
      children: (
        <div className="kb-doc-attachment">
          <div className="kb-doc-attachment-list">
            {fileList.map(i => (
              <Col span={10} offset={1}>
                <FileItem {...i} mode="view" bucketName="file-mes" />
              </Col>
            ))}
          </div>
        </div>
      ),
      footer: false,
    });
  };

  return (
    <div>
      <Spin spinning={loading}>
        <Header title={intl.get(`${modelPrompt}.rohs.manage`).d('RoHS文件管理')}>
          <Button
            icon="add"
            color={ButtonColor.primary}
            onClick={() => {
              // 判断是否已经有更新或新增的数据 有则不进行新增
              if (
                tableDs.records.some(
                  record => record.status === 'add' || record.getState('editing'),
                )
              ) {
                return;
              }
              tableDs.create({}, 0);
            }}
          >
            {intl.get('hzero.common.button.create').d('新建')}
          </Button>
        </Header>
        <Content>
          <Table dataSet={tableDs} columns={columns} />
        </Content>
      </Spin>
    </div>
  );
};
export default withProps(
  () => {
    const tableDs = new DataSet({
      ...tableDS(),
    });
    return {
      tableDs,
    };
  },
  { cacheState: true, keepOriginDataSet: true },
)(dom);
