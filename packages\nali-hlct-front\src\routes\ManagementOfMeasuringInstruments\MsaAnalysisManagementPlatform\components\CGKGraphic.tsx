/**
 * @Description: MSA分析管理平台-CGK图表界面
 * @Author: <EMAIL>
 * @Date: 2023/8/24 15:42
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, TextField, TextArea } from 'choerodon-ui/pro';
import { Collapse, Row, Col } from 'choerodon-ui';
import echarts from 'echarts';
import {
  max as lodashMax,
  min as lodashMin,
  floor as lodashFloor,
  ceil as lodashCeil,
} from 'lodash';
import intl from 'utils/intl';
import Table from './TableComponent';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const { Panel } = Collapse;
let cgkChart; // cgk图chart

const colorList = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
];

const CGKGraphic = ({ dataSoure, analyseResultDs }) => {
  const [pageInit, setPageInit] = useState(false);
  const cgkChartRef = useRef(null);

  useEffect(() => {
    if (!dataSoure.cgkInfo?.measureValueList?.length) {
      return;
    }
    if (!pageInit) {
      cgkChart = echarts.init(cgkChartRef.current);
      window.onresize = () => {
        setTimeout(() => {
          cgkChart.resize();
        }, 300);
      };
    }
    handleInitCGKChart(dataSoure);
  }, [dataSoure?.standardValue, dataSoure?.tolerance, dataSoure.cgkInfo?.measureValueList]);

  const getRulePercision = (dataRule: any) => {
    const { centerLine, upperControlLimit, lowerControlLimit } = dataRule;
    const data = [centerLine, upperControlLimit, lowerControlLimit];
    let maxDigits;
    data.forEach((item: number) => {
      const _digits = item.toString().split('.')[1]?.length || 0;
      if (_digits > maxDigits || !maxDigits) {
        maxDigits = _digits;
      }
    });
    return maxDigits;
  };

  const handleInitCGKChart = (dataSource) => {
    const {
      xTickLabelList,
      yDataList,
      maxValue,
      minValue,
      centerLine,
      upperControlLimit,
      lowerControlLimit,
    } = handleFormatCGKData(dataSource);
    const formatLabel = {
      formatter: value => {
        return `${value.name}:${value.data.yAxis}`;
      },
      color: '#000',
      fontSize: 10,
    };
    const option = {
      tooltip: {
        trigger: 'axis',
      },
      color: colorList,
      xAxis: {
        type: 'category',
        data: xTickLabelList,
        axisTick: {
          show: true,
          alignWithLabel: true,
        },
        // splitLine: {
        //   show: true,
        //   lineStyle: {
        //     color: 'rgba(0,0,0,0.03)',
        //   },
        // },
      },
      yAxis: {
        type: 'value',
        max: maxValue,
        min: minValue,
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.03)',
          },
        },
      },
      series: [
        {
          name: intl.get(`${modelPrompt}.title.measureValue`).d('测量值'),
          type: 'line',
          symbol: 'circle',
          symbolSize: 6,
          hoverAnimation: false,
          color: '#1890ff',
          markLine: {
            silent: true,
            symbol: 'none',
            precision: getRulePercision({ centerLine, upperControlLimit, lowerControlLimit}),
            data: [
              {
                name: 'CL',
                yAxis: lodashFloor(centerLine, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#b8b8b8',
                  width: 1.2,
                },
              },
              {
                name: 'UCL',
                yAxis: lodashFloor(upperControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
              {
                name: 'LCL',
                yAxis: lodashFloor(lowerControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
            ],
          },
          data: yDataList,
        },
      ],
    };
    cgkChart.setOption(option, true);
    // 初始化chart事件绑定
    if (!pageInit) {
      setPageInit(true);
    }
  };

  const handleFormatCGKData = (dataSource) => {
    const { cgkInfo: { measureValueList, upLimit, lowLimit }, standardValue = 0 } = dataSource;
    const xTickLabelList: any[] = []; // x轴坐标名称
    const yDataList: any = []; // 数据运行图数据
    let maxValue = Math.max(upLimit, lowLimit);
    let minValue = Math.min(upLimit, lowLimit);
    (measureValueList || []).forEach(colItem => {
      const { measureDataRow, measureDataValue } = colItem;
      if (maxValue < measureDataValue || !maxValue) {
        maxValue = measureDataValue;
      }
      if (minValue > measureDataValue || !minValue) {
        minValue = measureDataValue;
      }
      xTickLabelList.push(measureDataRow);
      yDataList.push(measureDataValue);
    });
    return {
      xTickLabelList,
      yDataList,
      maxValue: (maxValue - minValue) * 0.1 + maxValue,
      minValue: minValue - (maxValue - minValue) * 0.1,
      centerLine: standardValue,
      upperControlLimit: upLimit,
      lowerControlLimit: lowLimit,
    };
  };

  const bisaColumn = [
    {
      name: 'standardValue',
      title: intl.get(`${modelPrompt}.standardValue`).d('基准值'),
    },
    {
      name: 'biasValue',
      title: intl.get(`${modelPrompt}.biasValue`).d('偏倚'),
    },
    {
      name: 'cgValue',
      title: intl.get(`${modelPrompt}.cgValue`).d('Cg'),
    },
    {
      name: 'mean',
      title: intl.get(`${modelPrompt}.mean`).d('均值'),
    },
    {
      name: 'statisticalValue',
      title: intl.get(`${modelPrompt}.statisticalValue`).d('T'),
    },
    {
      name: 'cgkValue',
      title: intl.get(`${modelPrompt}.cgkValue`).d('Cgk'),
    },
    {
      name: 'stdDev',
      title: intl.get(`${modelPrompt}.stdDev`).d('标准差'),
    },
    {
      name: 'piValue',
      title: intl.get(`${modelPrompt}.piValue`).d('P值'),
    },
    {
      name: 'variationRepeatabilityValue',
      title: intl.get(`${modelPrompt}.variationRepeatabilityValue`).d('%变异(重复性)'),
    },
    {
      name: 'variationValue',
      title: intl.get(`${modelPrompt}.variationValue`).d('6*标准差(SV)'),
    },
    {
      name: 'tolerance',
      title: intl.get(`${modelPrompt}.tolerance`).d('公差'),
    },
    {
      name: 'variationRepeatabilityBiasValue',
      title: intl.get(`${modelPrompt}.variationRepeatabilityBiasValue`).d('%变异(重复性和偏倚)'),
    },
  ];

  const RenderHelp = () => {
    const info = intl.get(`${modelPrompt}.cgk.help`).d('1、当Cgk>=1.33，则合格；<br />2、否则，不合格');
    const descriptionList = info.split('<br />');
    return (
      <div>
        {descriptionList.map(item => (
          <p>{item}</p>
        ))}
      </div>
    );
  };

  return (
    <div>
      {Boolean( dataSoure.cgkInfo?.measureValueList?.length) && (
        <Collapse bordered={false} defaultActiveKey={['analyseContent']}>
          <Panel key="analyseContent" header={intl.get(`${modelPrompt}.analyseContent`).d('分析内容')}>
            <div
              id="cgk-chart"
              ref={cgkChartRef}
              style={{
                height: '300px',
              }}
            />
            <Table data={[{ ...dataSoure.cgkInfo }]} columns={bisaColumn} />
            <Form labelWidth={100} disabled dataSet={analyseResultDs}>
              <TextField name="msaResult" help={<RenderHelp />} />
              <TextArea name="msaConclusion" rows={7} />
            </Form>
          </Panel>
        </Collapse>
      )}
    </div>
  );
};

export default CGKGraphic;
