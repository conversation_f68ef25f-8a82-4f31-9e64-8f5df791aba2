/**
 * @Description: 产品审核方案-详情
 */
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Button, DataSet, DateTimePicker, Form, Lov, Modal, Select, Switch, Table, TextField} from 'choerodon-ui/pro';
import {Badge, Collapse, Icon, Popconfirm} from 'choerodon-ui';
import notification from 'utils/notification';
import {Button as PermissionButton} from 'components/Permission';
import {Content, Header} from 'components/Page';
import {ButtonColor, FuncType} from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import {drawerPropsC7n, TarzanSpin} from '@components/tarzan-ui';
import {useRequest} from '@components/tarzan-hooks';
import {FieldType} from "choerodon-ui/pro/lib/data-set/enum";
import {getCurrentOrganizationId} from "utils/utils";
import { useMemoizedFn } from 'ahooks';
import {ViewMode} from "choerodon-ui/pro/lib/lov/enum";
import {Size} from "choerodon-ui/pro/lib/core/enum";
import {ColumnAlign, ColumnLock} from "choerodon-ui/pro/lib/table/enum";
import HistoryDrawer from '../HistoryDrawer';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import {detailDS, reviewItemDS, taskDS} from '../stores/DetailDS';
import {GetDefaultSite, SaveSchemeItem, SaveVerification, SubmitVerification} from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.ProductReviewScheme';
const tenantId = getCurrentOrganizationId();

const TaskDetail = props => {
  const {
    history,
    match: { params, path },
  } = props;
  const productRevSchemeId = params.id;
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);

  const createButtonDs = useMemo(() => {
    return new DataSet({
      selection: false,
      autoCreate: false,
      paging: false,
      fields: [
        {
          name: 'toolLov',
          type: FieldType.object,
          lovCode: 'YP.QIS.PRODUCT_REVIEW_ITEM',
          // @ts-ignore
          noCache: true,
          lovPara: {
            tenantId,
          },
          multiple: true,
          required: true,
        },
      ],
    });
  }, []);

  const [tableArr, setTableArr] = useState([]);
  const [tableDsArr, setTableDsArr] = useState([]);
  const [productReviewItemTypeArr, setProductReviewItemTypeArr] = useState([]);
  const [canEdit, setCanEdit] = useState(false);
  const reviewItemDs = useMemo(() => new DataSet(reviewItemDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );

  const { run: saveVerification, loading: saveLoading } = useRequest(SaveVerification(), {
    manual: true,
  });

  const { run: submitVerification, loading: submitLoading } = useRequest(SubmitVerification(), {
    manual: true,
  });

  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });

  // 保存审核项目
  const { run: saveSchemeItem, loading: saveItemLoading } = useRequest(SaveSchemeItem(), {
    manual: true,
  });

  useEffect(() => {
    if (productRevSchemeId === 'create') {
      // 新建时
      setCanEdit(true);
      getDefaultSite({
        onSuccess: res => {
          if (res?.siteId) {
            detailDs.current?.set('siteLov', res);
          }
        },
      });
      return;
    }
    // 编辑时
    handleQueryDetail(productRevSchemeId);
  }, [productRevSchemeId]);

  // 详情页初始化数据
  const handleQueryDetail = id => {
    detailDs.setQueryParameter('productRevSchemeId', id);
    detailDs.query().then(res => {
      const { rows: { groupInfo } } = res;
      const arr:any = [];
      const productReviewItemTypeList = [];
      const dsArr = [];
      groupInfo.forEach((item:any) => {
        arr.push({
          productReviewItemType: item.productReviewItemType,
          productReviewItemTypeDesc: item.productReviewItemTypeDesc,
        });
        // @ts-ignore
        productReviewItemTypeList.push(item.productReviewItemType)
        // @ts-ignore
        dsArr.push(new DataSet(taskDS()))
      })
      groupInfo.forEach((item, index) => {
        // @ts-ignore
        dsArr[index].loadData(item.dtlInfo)
      })
      setTableDsArr(dsArr);
      setTableArr(arr);
      setProductReviewItemTypeArr(productReviewItemTypeList)
    });
  };

  const handleEdit = () => {
    setCanEdit(true);
  };

  const handleCancel = useCallback(() => {
    if (productRevSchemeId === 'create') {
      history.push('/hwms/product-review/product-review-scheme/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(productRevSchemeId);
    }
  }, []);

  const handleAddLine = (value, type) => {
    if (!value?.length) {
      return;
    }
    const dsArr = [...tableDsArr];
    value.forEach(item => {
      // @ts-ignore
      const data =  dsArr[productReviewItemTypeArr.indexOf(type)].toData();
      const result = data.find(({ productReviewItemId }) => productReviewItemId === item.productReviewItemId);
      if (!result) {
        // @ts-ignore
        dsArr[productReviewItemTypeArr.indexOf(type)].create({
          ...item,
        }, 0);
      }
    });
  };

  const handleSaveReviewItem = (dataSet, nextFlag = false) => {
    return new Promise(async resolve => {
      const valRes = await dataSet.validate();
      if (!valRes) {
        return resolve(false);
      }
      const resultData = dataSet.current?.toData();
      saveSchemeItem({
        params: {
          ...resultData,
        },
        onSuccess: res => {
          notification.success({});
          handleChangeLov([{...res}]);
          // if (!productReviewItemTypeArr.length) {
          //   handleChangeLov([{...res}]);
          // } else {
          //   const dsArr = [...tableDsArr];
          //   // @ts-ignore
          //   dsArr[productReviewItemTypeArr.indexOf(resultData.productReviewItemType)].create({
          //     ...res,
          //   }, 0)
          //   setTableDsArr(dsArr);
          // }
          if (nextFlag) {
            dataSet.reset();
            reviewItemDs?.current?.set('type', resultData.type);
            if (resultData.type){
              reviewItemDs?.current?.set('productReviewItemType', resultData.type);
            }
            return resolve(false);
          }
          return resolve(true);
        },
        onFailed: () => {
          return resolve(false);
        },
      });
    });
  };

  const handleCreateReviewItem = (modal, type) => {
    modal.close();
    reviewItemDs?.current?.set('operationIdList', detailDs.current?.get('operationId'));
    reviewItemDs?.current?.set('type', type);
    if (type) {
      reviewItemDs?.current?.set('productReviewItemType', type);
    }
    Modal.open({
      ...drawerPropsC7n({
        ds: reviewItemDs,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.createReviewItem`).d('产品审核项目新建'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: () => handleSaveReviewItem(reviewItemDs),
      children: (
        <Form dataSet={reviewItemDs} columns={1}>
          <TextField name="productReviewItemCode" />
          <Select name="productReviewItemType" searchable />
          <TextField name="productReviewItemDesc" />
          <Select name="productReviewMethod" />
          <TextField name="specRequire" />
          <Select name="reviewFrequency" />
          <Select name="weightCoefficient" />
          <TextField name="remark" />
          <Switch name="recordDataFlag" />
          <Switch name="fromOrtFlag" />
          <Switch name="enableFlag" />
        </Form>
      ),
      footer: (okBtn, cancelBtn) => {
        return [
          cancelBtn,
          <Button onClick={() => handleSaveReviewItem(reviewItemDs, true)}>
            {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并新建下一条')}
          </Button>,
          okBtn,
        ];
      },
    });
  };

  const taskColumns: any = (ds, type) => [
    {
      header: () => (
        <Lov
          dataSet={new DataSet({
            selection: false,
            autoCreate: false,
            paging: false,
            fields: [
              {
                name: 'toolLov',
                type: FieldType.object,
                lovCode: 'YP.QIS.PRODUCT_REVIEW_ITEM',
                // @ts-ignore
                noCache: true,
                lovPara: {
                  tenantId,
                  productReviewItemType: type,
                },
                multiple: true,
                required: true,
              },
            ],
          })}
          name="toolLov"
          noCache
          mode={ViewMode.button}
          clearButton={false}
          onChange={(value) => handleAddLine(value, type)}
          size={Size.small}
          funcType={FuncType.flat}
          disabled={!canEdit}
          modalProps={{
            footer: (okBtn, cancelBtn, modal) => {
              return [
                <Button onClick={() => handleCreateReviewItem(modal, type)}>
                  {intl.get(`${modelPrompt}.button.createReviewItem`).d('新建产品审核项目')}
                </Button>,
                cancelBtn,
                okBtn,
              ];
            },
          }}
        >
          <Icon type="add" />
        </Lov>
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => ds.remove(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button icon="remove" disabled={!canEdit} funcType={FuncType.flat} />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'productReviewItemCode',
      width: 160,
    },
    {
      name: 'productReviewItemDesc',
      editor: canEdit,
    },
    {
      name: 'productReviewMethod',
      editor: canEdit,
    },
    {
      name: 'specRequire',
      editor: canEdit,
    },
    {
      name: 'reviewFrequency',
      editor: canEdit,
    },
    {
      name: 'weightCoefficient',
      editor: canEdit,
    },
    {
      name: 'remark',
      editor: canEdit,
    },
    {
      name: 'recordDataFlag',
      editor: canEdit && <Switch />,
      width: 160,
      renderer: ({ value }) => {
        if (!value) {
          return;
        }
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        );
      },
    },
    {
      name: 'fromOrtFlag',
      editor: canEdit && <Switch />,
      width: 160,
      renderer: ({ value }) => {
        if (!value) {
          return;
        }
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        );
      },
    },
  ];

  // @ts-ignore
  const handleSave = async (submitFlag) => {
    const validateFlag = await detailDs.validate();
    const allResultList = await Promise.all(
      tableDsArr.map(async (itemDs) => {
        // @ts-ignore
        const res = await itemDs.validate();
        return res;
      }),
    );
    const allResult = allResultList.every(val => val);
    if (!validateFlag && !allResult) {
      return false;
    }
    let dtlInfo:any = [];
    tableDsArr.map( (itemDs:any) => dtlInfo = [...dtlInfo, ...itemDs.toData()])
    if (submitFlag === 'N') {
      saveVerification({
        params: {
          ...detailDs.toData()[0],
          dtlInfo,
        },
        onSuccess: res => {
          notification.success({});
          setCanEdit(false);
          setTableArr([]);
          setTableDsArr([]);
          setProductReviewItemTypeArr([]);
          if (productRevSchemeId === 'create') {
            history.push(`/hwms/product-review/product-review-scheme/${res}`);
          } else {
            handleQueryDetail(res);
          }
        },
      });
    } else {
      submitVerification({
        params: {
          ...detailDs.toData()[0],
          dtlInfo,
        },
        onSuccess: res => {
          notification.success({});
          setCanEdit(false);
          setTableArr([]);
          setTableDsArr([]);
          setProductReviewItemTypeArr([]);
          if (productRevSchemeId === 'create') {
            history.push(`/hwms/product-review/product-review-scheme/${res}`);
          } else {
            handleQueryDetail(res);
          }
        },
      });
    }

  };

  const handleChangeLov = useMemoizedFn((value) => {
    const arr:any = [...tableArr];
    const productReviewItemTypeList = [...productReviewItemTypeArr];
    const dsArr = [...tableDsArr];
    let data = []
    dsArr.forEach((item:any) => {
      // @ts-ignore
      data = [...data, ...item.toData()]
    });

    value.forEach((item:any) => {
      // @ts-ignore
      if (!productReviewItemTypeList.includes(item.productReviewItemType)) {
        arr.push({
          productReviewItemType: item.productReviewItemType,
          productReviewItemTypeDesc: item.productReviewItemTypeDesc,
        });
        // @ts-ignore
        productReviewItemTypeList.push(item.productReviewItemType)
        // @ts-ignore
        dsArr.push(new DataSet(taskDS()))
      }
    })
    value.forEach(item => {
      // 去重
      const result = data.find(({ productReviewItemId }) => productReviewItemId === item.productReviewItemId);
      if (!result) {
        // @ts-ignore
        dsArr[productReviewItemTypeList.indexOf(item.productReviewItemType)].create({
          ...item,
        })
      }
    })
    setTableDsArr(dsArr);
    setTableArr(arr);
    setProductReviewItemTypeArr(productReviewItemTypeList)
  });

  const changeSiteLov = () => {
    // @ts-ignore
    detailDs.current.init('processWorkcellLov', null);
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || siteLoading || saveItemLoading || submitLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('产品审核方案')}
          backPath={pubFlag ? '' : '/hwms/product-review/product-review-scheme/list'}
        >
          {
            !pubFlag && (
              canEdit ? (
                <>
                  <Button
                    color={ButtonColor.primary}
                    icon="save"
                    onClick={() => handleSave('N')}
                  >
                    {intl.get('tarzan.common.button.save').d('保存')}
                  </Button>
                  <Button icon="close" onClick={handleCancel}>
                    {intl.get('tarzan.common.button.cancel').d('取消')}
                  </Button>
                </>
              ) : (
                <>
                  <PermissionButton
                    type="c7n-pro"
                    color={ButtonColor.primary}
                    onClick={handleEdit}
                    disabled={['AMENDING_IN_APPROVAL', 'IN_APPROVAL'].includes(detailDs.current?.get('productRevSchemeStatus'))}
                  >
                    {intl.get('hzero.common.button.edit').d('编辑')}
                  </PermissionButton>
                  <PermissionButton
                    type="c7n-pro"
                    onClick={() => handleSave('Y')}
                    disabled={!['NEW', 'AMENDING', 'REJECTED', 'AMENDING_REJECTED'].includes(detailDs.current?.get('productRevSchemeStatus'))}
                    permissionList={[
                      {
                        code: 'ProductReviewScheme.button.submit',
                        type: 'button',
                        meaning: '产品审核方案-提交按钮',
                      },
                    ]}
                  >
                    {intl.get(`${modelPrompt}.button.release`).d('提交')}
                  </PermissionButton>
                </>
              )
            )
          }
          {
            // @ts-ignore
            productRevSchemeId !== 'create' && <HistoryDrawer objectId={productRevSchemeId} type="button" />
          }
          {
            productRevSchemeId !== 'create' && <ApprovalInfoDrawer
              objectTypeList={['QIS_PRODUCT_REV_SCHEME']}
              objectId={productRevSchemeId}
            />
          }
        </Header>
        <Content>
          <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
            <TextField name="productRevSchemeCode" />
            <Lov name="siteLov" onChange={changeSiteLov}/>
            <TextField name="productRevSchemeName" />
            <Select name='productRevSchemeStatus' />
            <Lov name="materialLov" />
            <TextField name="materialName" />
            <Lov name="processWorkcellLov"/>
            <TextField name="workcellName" />
            <DateTimePicker name="productDate" />
            <TextField name="cusMaterialCode" />
            <TextField name="cusMaterialName" />
          </Form>
          <br/>
          <Lov
            dataSet={createButtonDs}
            name="toolLov"
            disabled={!canEdit}
            // @ts-ignore
            mode="button"
            // @ts-ignore
            funcType='flat'
            clearButton={false}
            onChange={handleChangeLov}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return [
                  // @ts-ignore
                  <Button onClick={() => handleCreateReviewItem(modal)}>
                    {intl.get(`${modelPrompt}.button.createReviewItem`).d('新建产品审核项目')}
                  </Button>,
                  cancelBtn,
                  okBtn,
                ];
              },
            }}
          >
            <Icon type="add" />
          </Lov>
          <br/>
          <br/>
          <Collapse
            bordered={false}
            activeKey={[...productReviewItemTypeArr]}
          >
            {(tableArr || []).map((item, index) =>
              (
                // @ts-ignore
                <Panel key={item.productReviewItemType} header={item.productReviewItemTypeDesc}>
                  <Table
                    dataSet={tableDsArr[index]}
                    // @ts-ignore
                    columns={taskColumns(tableDsArr[index], item.productReviewItemType)}
                  />
                </Panel>
              ),
            )}
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(TaskDetail);
