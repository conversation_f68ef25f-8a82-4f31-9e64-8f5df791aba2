import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: true,
  selection: DataSetSelection.single,
  dataKey: 'content', // 列表数据在接口返回json中的相对路径
  totalKey: 'totalElements',
  primaryKey: 'formulaId', // 表格唯一性主键
  queryFields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('成品物料'),
      lovCode: 'APEX_MES.MATERIAL_SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'bomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.bomCode`).d('装配清单'),
      lovCode: 'APEX_MES.MT_BOM_SITE_MATERIAL',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            bomType: 'MATERIAL',
            materialId: record.get('materialId'),
          };
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'bomId',
      bind: 'bomLov.bomId',
    },
    {
      name: 'bomName',
      bind: 'bomLov.bomName',
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('成品物料'),
      lovCode: 'APEX_MES.MATERIAL_SITE',
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => !record.get('siteId'),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialDesc',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
    },
    {
      name: 'bomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.bomCode`).d('装配清单'),
      lovCode: 'APEX_MES.MT_BOM_SITE_MATERIAL',
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
          };
        },
        disabled: ({ record }) => !record.get('siteId') || !record.get('materialId'),
      },
    },
    {
      name: 'assemblyChecklist',
      bind: 'bomLov.bomId',
    },
    {
      name: 'assemblyChecklistDesc',
      bind: 'bomLov.bomName',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-formulas`,
        method: 'GET',
      };
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'content', // 列表数据在接口返回json中的相对路径
  totalKey: 'totalElements',
  primaryKey: 'assemblyChecklistId', // 表格唯一性主键
  fields: [
    {
      name: 'lineNumber',
      label: intl.get(`${modelPrompt}.lineNumber`).d('序号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
    },
    {
      name: 'bom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bom`).d('装配清单'),
    },
    {
      name: 'proportion',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.proportion`).d('比例'),
    },
    {
      name: 'tolerance',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.tolerance`).d('允差'),
      min: 0,
      computedProps: {
        max: ({ record }) => {
          if (record.get('toleranceType') === 'KG') {
            return null;
          }
          return 100;
        },
      },
    },
    {
      name: 'toleranceType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
      lookupCode: 'HME.TOLERANCE_TYPE',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assembly-checklists`,
        method: 'GET',
      };
    },
  },
});

export { headDS, lineDS };
