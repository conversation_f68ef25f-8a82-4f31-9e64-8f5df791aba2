/**
 * @Description: 控制控制图-新建页-定义控制图信息
 * @Author: <<EMAIL>>
 * @Date: 2021-11-29 13:52:27
 * @LastEditTime: 2022-11-23 14:40:10
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo } from 'react';
import { Form, TextField, Select, NumberField, Lov } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import { FetchDefaultJudgementGroup } from '../../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hspc.controlChartMaintain';

const DefineControlChartInfo = props => {
  const {
    analysisChartCode,
    controlChartInfoDs,
    mainControlChartDetailInfoDs,
    secondaryControlChartDetailInfoDs,
    tableStructure,
  } = props;

  const fetchDefaultJudgementGroup = useRequest(FetchDefaultJudgementGroup(), {
    manual: true,
  });

  useEffect(() => {
    if (analysisChartCode !== 'new') {
      return;
    }
    if (tableStructure.selectedSampleType === 'MEASURE') {
      const lineNumber = Object.keys(tableStructure[tableStructure.selectedSampleType]).length;
      if (lineNumber === 2) {
        controlChartInfoDs!.current!.init('subgroupSize', undefined);
        controlChartInfoDs.current.getField('subgroupSize').set('disabled', false);
      } else {
        controlChartInfoDs!.current!.set('subgroupSize', lineNumber - 1);
        controlChartInfoDs.current.getField('subgroupSize').set('disabled', true);
      }
    } else {
      controlChartInfoDs!.current!.set('subgroupSize', 1);
      controlChartInfoDs.current.getField('subgroupSize').set('disabled', true);
    }
    fetchDefaultJudgementGroup.run({
      onSuccess: res => {
        if (res) {
          mainControlChartDetailInfoDs!.current!.set('judgementGroup', res);
          if (tableStructure.selectedSampleType === 'MEASURE') {
            secondaryControlChartDetailInfoDs!.current!.set('judgementGroup', res);
          }
        }
      },
    });
  }, [tableStructure.selectedSampleType]);

  const analysisChartTypeFilter = record => {
    const type = record.get('tag') as string;
    if (
      tableStructure.selectedSampleType === 'MEASURE' &&
      Object.keys(tableStructure[tableStructure.selectedSampleType]).length === 2
    ) {
      return type.toUpperCase() === tableStructure.selectedSampleType;
    }
    return (
      type.toUpperCase() === tableStructure.selectedSampleType && record.get('value') !== 'X-MR'
    );
  };

  const handleChangeChartType = value => {
    if (value === 'X-MR') {
      controlChartInfoDs!.current!.set('subgroupSize', 1);
      controlChartInfoDs.current.getField('subgroupSize').set('disabled', true);
    } else if (Object.keys(tableStructure[tableStructure.selectedSampleType]).length === 2) {
      controlChartInfoDs.current.getField('subgroupSize').set('disabled', false);
    }
  };

  const formItemDisabled = useMemo(() => analysisChartCode !== 'new', [analysisChartCode]);

  const handleChange = (value) => {
    if (value) {
      mainControlChartDetailInfoDs.setState('required', true);
      secondaryControlChartDetailInfoDs.setState('required', true);
    } else {
      mainControlChartDetailInfoDs.setState('required', false);
      secondaryControlChartDetailInfoDs.setState('required', false);
    }
  }

  return (
    <div className="hmes-style">
      <Collapse
        bordered={false}
        defaultActiveKey={[
          'controlChartInfo',
          'mainControlChartDetails',
          'secondaryControlChartDetails',
        ]}
      >
        <Panel
          header={intl.get(`${modelPrompt}.controlChartInfo`).d('控制图信息')}
          key="controlChartInfo"
          dataSet={controlChartInfoDs}
        >
          <Form labelWidth={112} dataSet={controlChartInfoDs} columns={3}>
            <Select
              name="chartType"
              onChange={handleChangeChartType}
              optionsFilter={analysisChartTypeFilter}
              disabled={formItemDisabled}
            />
            <NumberField name="subgroupSize" disabled={formItemDisabled} />
            <NumberField name="maxPlotPoints" />
            <TextField name="chartTitle" />
            <Select name="xTickLabel" />
            <TextField name='attribute3' onChange={handleChange}/>
          </Form>
        </Panel>
        <Panel
          header={intl.get(`${modelPrompt}.mainControlChartDetails`).d('主要控制图详细信息')}
          key="mainControlChartDetails"
          dataSet={mainControlChartDetailInfoDs}
        >
          <Form labelWidth={112} dataSet={mainControlChartDetailInfoDs} columns={3}>
            <NumberField name="upperControlLimit" disabled={formItemDisabled} />
            <NumberField name="upperSpecLimit" />
            <Lov name="judgementGroup" />
            <NumberField name="centerLine" disabled={formItemDisabled} />
            <NumberField name="specTarget" />
            <TextField name="xAxisLabel" />
            <NumberField name="lowerControlLimit" disabled={formItemDisabled} />
            <NumberField name="lowerSpecLimit" />
            <TextField name="yAxisLabel" />
          </Form>
        </Panel>
        {tableStructure.selectedSampleType === 'MEASURE' ? (
          <Panel
            header={intl.get(`${modelPrompt}.secondaryControlChartDetails`).d('次要控制图详细信息')}
            key="secondaryControlChartDetails"
            dataSet={secondaryControlChartDetailInfoDs}
          >
            <Form labelWidth={112} dataSet={secondaryControlChartDetailInfoDs} columns={3}>
              <NumberField name="upperControlLimit" disabled={formItemDisabled} />
              <NumberField name="upperSpecLimit" />
              <Lov name="judgementGroup" />
              <NumberField name="centerLine" disabled={formItemDisabled} />
              <NumberField name="specTarget" />
              <TextField name="xAxisLabel" />
              <NumberField name="lowerControlLimit" disabled={formItemDisabled} />
              <NumberField name="lowerSpecLimit" />
              <TextField name="yAxisLabel" />
            </Form>
          </Panel>
        ) : null}
      </Collapse>
    </div>
  );
};

export default DefineControlChartInfo;
