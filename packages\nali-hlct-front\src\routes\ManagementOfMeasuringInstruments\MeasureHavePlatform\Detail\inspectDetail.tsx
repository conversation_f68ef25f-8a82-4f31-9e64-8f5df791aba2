/**
 * @Description: 量具检定平台-详情界面
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  TextField,
  Select,
  DateTimePicker,
  Table,
  Attachment,
  Modal,
  TextArea,
  NumberField,
} from 'choerodon-ui/pro';
import moment from 'moment';
import { Collapse, Menu, Tag, Divider, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import { API_HOST, HZERO_FILE } from 'utils/config';
import { getAccessToken } from 'utils/utils';
import PrintElement from '@components/tarzan-ui/PrintButton/PrintElement';

import { Header, Content } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { observer } from 'mobx-react';
import { ColumnAlign, TableButtonType } from 'choerodon-ui/pro/lib/table/enum';
import { Button as PermissionButton } from 'components/Permission';
import myInstance from '@utils/myAxios';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { getProductWithPrecision } from '@/utils';
import { useRequest } from '@components/tarzan-hooks';
import { FieldType } from 'choerodon-ui/dataset/data-set/enum';
import axios from 'axios';
import { outerDetailDS, insideDetailDS, recordDS } from '../stories/InspectDetailDS';
import { rejectReasonDS } from '../stories/DetailDS';
import { InternalRefresh, ClaimInspectDoc, ConfirmClaimInspectDoc } from '../services';
import styles from './index.module.less';

const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;
const modelPrompt = 'tarzan.inspectExecute.MeasureHavePlatform';
const userInfo = getCurrentUser();

const InspectDetail = props => {
  const {
    history,
    match: { params },
  } = props;
  const inspectDocId = params.inspectDocId;
  const verificationMethod = params.verificationMethod;

  const [canEdit, setCanEdit] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState(0);
  const [buttonStatus, setButtonStatus] = useState(false);
  const [status, setStatus] = useState('NEW');
  const [inspectResult, setInspectResult] = useState<string>();
  const [editButton, setEditButton] = useState(false);
  const [tableDsArr, setTableDsArr] = useState<any>([]);
  const recordDs = useMemo(() => new DataSet(recordDS()), []);
  const rejectReasonDs = useMemo(() => new DataSet(rejectReasonDS()), []);

  const [lineInfo, setLineInfoList] = useState<Array<any>>([]);

  // 刷新检测项目列表
  const { run: handleInternalRefresh, loading: refreshLoading } = useRequest(InternalRefresh(), {
    manual: true,
  });
  // 领取检定单
  const { run: claimInspectDoc } = useRequest(ClaimInspectDoc(), {
    manual: true,
    needPromise: true,
  });
  // 确认领取检定单
  const { run: confirmClaimInspectDoc } = useRequest(ConfirmClaimInspectDoc(), {
    manual: true,
    needPromise: true,
  });

  const outerDetailDs = useMemo(
    () =>
      new DataSet({
        ...outerDetailDS(),
        children: {
          taskInfo: recordDs,
        },
      }),
    [],
  );
  const insideDetailDs = useMemo(
    () =>
      new DataSet({
        ...insideDetailDS(),
        children: {
          taskInfo: recordDs,
        },
      }),
    [],
  );

  useDataSetEvent(outerDetailDs, 'update', ({ name }) => {
    switch (name) {
      case '':
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    // 编辑时
    handleQueryDetail(inspectDocId);
  }, [inspectDocId]);

  const handleQueryDetail = async id => {
    // 外检
    if (verificationMethod === 'WJ') {
      outerDetailDs.setQueryParameter('inspectDocId', id);
      outerDetailDs.query().then(res => {
        const { rows } = res;
        setStatus(rows?.inspectDocStatus);
        setInspectResult(rows?.inspectResult);
        setEditButton(false);
        if (!rows?.inspectorId) {
          outerDetailDs.current?.set('inspectorId', rows?.createdBy);
          outerDetailDs.current?.set('inspectorName', rows?.createdByName);
          if (['RELEASED'].includes(rows?.inspectDocStatus) && rows?.createdBy !== Number(userInfo.id)) {
            setEditButton(true);
          }
        }
        if (
          (['RELEASED'].includes(rows?.inspectDocStatus) && rows?.inspectorId !== Number(userInfo.id)) ||
          ['COMPLETED', 'TO_APPROVAL', 'TO_CLAIM', 'TO_CONFIRM'].includes(rows?.inspectDocStatus)
        ) {
          setEditButton(true);
        }
      });
    } else {
      insideDetailDs.setQueryParameter('inspectDocId', id);
      insideDetailDs.query().then(res => {
        const { rows } = res;
        setStatus(rows?.inspectDocStatus);
        setInspectResult(rows?.inspectResult);
        setLineInfoList(rows?.lineInfo);
        setEditButton(false);
        if (!rows?.inspectorId) {
          insideDetailDs.current?.set('inspectorId', rows?.applicationCreatedBy);
          insideDetailDs.current?.set('inspectorName', rows?.applicationCreatedByName);
          if (
            ['RELEASED'].includes(rows?.inspectDocStatus) &&
            rows?.applicationCreatedBy !== Number(userInfo.id)
          ) {
            setEditButton(true);
          }
        }
        if (
          (['RELEASED'].includes(rows?.inspectDocStatus) && rows?.inspectorId !== Number(userInfo.id)) ||
          ['COMPLETED', 'TO_APPROVAL', 'TO_CLAIM', 'TO_CONFIRM'].includes(rows?.inspectDocStatus)
        ) {
          setEditButton(true);
        }
        const arr = [];
        rows?.lineInfo.forEach(item => {
          // @ts-ignore
          return arr.push(new DataSet(recordDS(item.dataType, rows.siteId)));
        });
        // @ts-ignore
        for (let i = 0; i < arr.length; i++) {
          const data = rows?.lineInfo[i].actInfo;
          // debugger
          data.forEach(item => {
            item.toolLov = item.msToolManageInfo;
            if (rows?.lineInfo[i].dataType === 'VALUE' && item.standardValue && item.actualValue) {
              item.deviation = (item.standardValue - item.actualValue).toFixed(
                maxDecimalPlaces(item.standardValue, item.actualValue),
              );
            }
          });

          // @ts-ignore
          arr[i].loadData([...data]);
        }
        // @ts-ignore
        setTableDsArr([...arr]);
      });
    }
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
    setButtonStatus(true);
    const ds = verificationMethod === 'WJ' ? outerDetailDs : insideDetailDs;
    if (!ds.current?.get('completionTime')) {
      ds.current?.set('completionTime', moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
    }
  }, []);

  const handleCancel = useCallback(() => {
    if (inspectDocId === 'create') {
      history.push('/hmes/measure-have/platform/list');
    } else {
      setCanEdit(false);
      setButtonStatus(false);
      handleQueryDetail(inspectDocId);
    }
  }, []);

  const handleRenderValue = ({ record, name, dataSet }) => {
    const toolList = record?.get('toolLov');
    const optionDs = dataSet?.getField('usingStatus')?.getOptions(record);

    if (toolList?.length) {
      return toolList.map(item => {
        if (name === 'enclosure') {
          return <Attachment value={item[name]} viewMode="popup" bucketName="qms" readOnly />;
        }
        if (name === 'usingStatus') {
          const optionRecord = optionDs?.find(_record => _record?.get('value') === item[name]);
          return (
            <div key={item.modelCode} className={styles['table-cell']}>
              {optionRecord?.get('meaning')}
            </div>
          );
        }
        if (name === 'verificationExpDate') {
          return (
            <div key={item.modelCode} className={styles['table-cell']}>
              {item[name] ? moment(item[name]).format('YYYY-MM-DD') : ' '}
            </div>
          );
        }
        return (
          <div key={item.modelCode} className={styles['table-cell']}>
            {item[name] || ' '}
          </div>
        );
      });
    }
    return '';
  };

  const taskColumns: any = useMemo(
    () => [
      { name: 'sequenceNumber' },
      {
        name: 'toolLov',
        width: 180,
        align: ColumnAlign.center,
        editor: canEdit,
      },
      {
        name: 'speciesName',
        width: 180,
        align: ColumnAlign.center,
        renderer: handleRenderValue,
      },
      {
        name: 'verificationExpDate',
        width: 180,
        align: ColumnAlign.center,
        renderer: handleRenderValue,
      },
      {
        name: 'modelName',
        width: 180,
        align: ColumnAlign.center,
        renderer: handleRenderValue,
      },
      {
        name: 'usingStatus',
        width: 180,
        align: ColumnAlign.center,
        renderer: handleRenderValue,
      },
      {
        name: 'modelRange',
        width: 180,
        align: ColumnAlign.center,
        renderer: handleRenderValue,
      },
      {
        name: 'resolution',
        width: 180,
        align: ColumnAlign.center,
        renderer: handleRenderValue,
      },
      {
        name: 'enclosure',
        width: 180,
        align: ColumnAlign.center,
        renderer: handleRenderValue,
      },
      {
        name: 'standardValue',
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <TextField onChange={val => standardValueChange(val, record)} />,
      },
      {
        name: 'actualValue',
        width: 150,
        align: ColumnAlign.center,
        editor: record => canEdit && <TextField onChange={val => actualValueChange(val, record)} />,
      },
      {
        name: 'deviationStr', // 只读
        align: ColumnAlign.center,
      },
      {
        name: 'lowerLimitPercent',
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <NumberField onChange={() => handleUpdateValue(record)} suffix="%" />,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return `${value}%`;
        },
      },
      {
        name: 'lowerLimitValue',
        align: ColumnAlign.center,
        editor: record => canEdit && <NumberField onChange={() => handleUpdateValue(record)} />,
      },
      {
        name: 'upperLimitPercent',
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <NumberField onChange={() => handleUpdateValue(record)} suffix="%" />,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return `${value}%`;
        },
      },
      {
        name: 'upperLimitValue',
        align: ColumnAlign.center,
        editor: record => canEdit && <NumberField onChange={() => handleUpdateValue(record)} />,
      },
      {
        name: 'inspectDate',
        align: ColumnAlign.center,
        editor: canEdit,
      },
      {
        name: 'inspectResult',
        align: ColumnAlign.center,
        editor: canEdit && <Select onChange={() => handleUpdateInspectResult()} />,
      },
      {
        name: 'remark',
        align: ColumnAlign.center,
        editor: canEdit,
      },
    ],
    [canEdit],
  );

  const standardValueChange = (val, record) => {
    if (record.get('actualValue') && val) {
      const deviation = (val - record.get('actualValue')).toFixed(
        maxDecimalPlaces(val, record.get('actualValue'))
      );
      record.set('deviation', deviation);
    }
    handleUpdateValue(record);
  };

  const actualValueChange = (val, record) => {
    if (record.get('standardValue') && val) {
      const deviation = (record.get('standardValue') - val).toFixed(
        maxDecimalPlaces(val, record.get('standardValue')),
      );
      record.set('deviation', deviation);
    }
    handleUpdateValue(record);
  };

  const handleUpdateValue = record => {
    const {
      standardValue,
      actualValue,
      lowerLimitValue,
      upperLimitValue,
      lowerLimitPercent,
      upperLimitPercent,
    } = record?.toData();
    let _lowerLimitValue = lowerLimitValue;
    let _upperLimitValue = upperLimitValue;
    // 更新下限值
    if (standardValue && lowerLimitPercent) {
      _lowerLimitValue = getProductWithPrecision(
        standardValue,
        getProductWithPrecision(100, lowerLimitPercent, 6, 'subtraction') / 100,
      );
      record?.set('lowerLimitValue', _lowerLimitValue);
    }
    // 更新上限值
    if (standardValue && upperLimitPercent) {
      _upperLimitValue = getProductWithPrecision(
        standardValue,
        getProductWithPrecision(100, upperLimitPercent, 6, 'add') / 100,
      );
      record?.set('upperLimitValue', _upperLimitValue);
    }
    // 更新检验结果
    if (actualValue && _lowerLimitValue && _upperLimitValue) {
      if (actualValue >= _lowerLimitValue && actualValue <= _upperLimitValue) {
        record.set('inspectResult', 'OK');
      } else {
        record.set('inspectResult', 'NG');
      }
      // 更新检验单检验结果
      handleUpdateInspectResult();
    }
  };

  const handleUpdateInspectResult = () => {
    if (verificationMethod === 'WJ') {
      return;
    }
    let _inspectResult = 'OK';
    if (insideDetailDs.current?.get('appearanceResult') === 'NG') {
      _inspectResult = 'NG';
    }
    if (_inspectResult !== 'NG') {
      for (let i = 0; i < tableDsArr.length; i++) {
        const ngRecord = tableDsArr[i]?.find(_record => _record.get('inspectResult') === 'NG');
        if (ngRecord) {
          _inspectResult = 'NG';
          break;
        }
      }
    }
    insideDetailDs.current?.set('inspectResult', _inspectResult);
    setInspectResult(_inspectResult);
  };

  const handleChangeResult = () => {
    const ds = verificationMethod === 'WJ' ? outerDetailDs : insideDetailDs;
    ds.current?.set('servicedFlag', undefined);
  };

  // @ts-ignore
  const handleSave = async ({ submitFlag }) => {
    let validateFlag: boolean;
    let headData: {};
    if (submitFlag === 'Y' && verificationMethod !== 'WJ') {
      const existRecordFlag = tableDsArr.find(dsItem => dsItem?.length > 0);
      if (!existRecordFlag) {
        notification.warning({
          message: intl.get(`${modelPrompt}.warning.mustHaveOneData`).d('请至少对一项检测项目填写检定记录！'),
        });
        return false;
      }
    }
    // 外检
    if (verificationMethod === 'WJ') {
      // @ts-ignore
      outerDetailDs.current.set({ nowDate: new Date().getTime() });
      if (submitFlag === 'Y') {
        validateFlag = await outerDetailDs.validate();
        if (!validateFlag) {
          return false;
        }
      }
      headData = outerDetailDs.toData()[0];
      myInstance
        .post(
          `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/external-save/ui?submitFlag=${submitFlag}`,
          {
            ...headData,
          },
        )
        .then(res => {
          if (res.data.success) {
            notification.success({});
            setCanEdit(false);
            setButtonStatus(false);
            handleQueryDetail(inspectDocId);
          } else {
            notification.error({
              message:
                res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
        });
    } else {
      // @ts-ignore
      insideDetailDs.current.set({ nowDate: new Date().getTime() });
      if (submitFlag === 'Y') {
        validateFlag = await insideDetailDs.validate();
      }
      headData = insideDetailDs.toData()[0];
      const actInfo = [];
      for (const dsItem of tableDsArr) {
        if (submitFlag === 'Y') {
          // @ts-ignore
          // eslint-disable-next-line no-await-in-loop
          const result = await dsItem.validate();
          if (!result) {
            validateFlag = false;
            return false;
          }
        }

        // @ts-ignore
        dsItem.forEach(record => {
          if (record.status !== 'sync') {
            // @ts-ignore
            actInfo.push({
              ...record.toData(),
              changeFlag: 'Y',
              inspectDate: moment(record.toData().inspectDate).format('YYYY-MM-DD'),
            });
          } else {
            // @ts-ignore
            actInfo.push({
              ...record.toData(),
              inspectDate: moment(record.toData().inspectDate).format('YYYY-MM-DD'),
            });
          }
        });
      }
      if (submitFlag === 'Y') {
        if (!validateFlag) {
          return false;
        }
      }
      myInstance
        .post(
          `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/internal-save/ui?submitFlag=${submitFlag}`,
          {
            actInfo,
            ...headData,
          },
        )
        .then(res => {
          if (res.data.success) {
            notification.success({});
            setCanEdit(false);
            setButtonStatus(false);
            handleQueryDetail(inspectDocId);
          } else {
            notification.error({
              message:
                res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
        });
    }
  };

  const RenderDeleteButton = observer(({ dataSet, canEdit }) => {
    return (
      <>
        <Button
          icon="delete"
          funcType={FuncType.flat}
          color={ButtonColor.red}
          disabled={!canEdit || !['NEW', 'REJECTED'].includes(status)}
          onClick={() => dataSet.remove(dataSet.selected)}
        >
          {intl.get(`${modelPrompt}.button.delete`).d('删除')}
        </Button>
      </>
    );
  });

  const print = async () => {
    // @ts-ignore
    const printDomRef = React.createRef(null);
    const html = await getHtml();

    // @ts-ignore
    await Modal.open({
      drawer: true,
      maskClosable: true,
      key: 'ModalKey',
      destroyOnClose: true,
      closable: true,
      style: {
        width: 720,
      },
      title: intl.get('tarzan.common.title.preview').d('预览'),
      children: (
        <div
          id="pdf"
          style={{ paddingBottom: 20, overflow: 'hidden' }}
          // @ts-ignore
          ref={printDomRef}
          // @ts-ignore
          dangerouslySetInnerHTML={{ __html: html }}
        />
      ),
      footer: () => (
        <div>
          <Button
            icon="print"
            onClick={() => {
              PrintElement({
                content: document.getElementById('pdf'),
              });
            }}
          >
            {intl.get('tarzan.common.button.print').d('打印')}
          </Button>
        </div>
      ),
    });
  };

  const getHtml = () => {
    const code = inspectResult === 'OK' ? 'QIS_MS_PLATFORM_LABEL_OK' : 'QIS_MS_PLATFORM_LABEL_NG';
    const url = `${API_HOST}${BASIC.HRPT_COMMON}/v1/${tenantId}/label-prints/view/html?labelTemplateCode=${code}&organizationId=${tenantId}&inspectDocIdList=${inspectDocId}`;
    myInstance.get(url).then(res => {
      if (res.statusText === 'OK' && (res.data || {}).label) {
        // @ts-ignore
        document.getElementById('pdf').innerHTML = res.data.label.replace(/↵/gm, ''); // 去掉回车换行;
      } else {
        notification.error({
          message: res.data.message,
          description: '',
        });
      }
    });
  };

  const transmit = async () => {
    let validateFlag = false;
    let inspectorId = 0;
    // 外检
    if (verificationMethod === 'WJ') {
      // @ts-ignore
      outerDetailDs.current.set({ nowDate: new Date().getTime() });
      validateFlag = await outerDetailDs.current.validate();
      // @ts-ignore
      inspectorId = outerDetailDs.toData()[0].inspectorId;
    } else {
      // @ts-ignore
      insideDetailDs.current.set({ nowDate: new Date().getTime() });
      validateFlag = await insideDetailDs.validate();
      // @ts-ignore
      inspectorId = insideDetailDs.toData()[0].inspectorId;
    }
    if (!validateFlag) {
      return false;
    }
    myInstance
      .post(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/release/ui`, {
        inspectDocId,
        inspectorId,
      })
      .then(res => {
        if (res.data.success) {
          notification.success({});
          setCanEdit(false);
          setButtonStatus(false);
          handleQueryDetail(inspectDocId);
        } else {
          notification.error({
            message: res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
          });
        }
      });
  };

  // 审批通过
  const handleApproval = () => {
    // 外检
    if (verificationMethod === 'WJ') {
      myInstance
        .post(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/approve-pass/ui`, {
          inspectDocId,
          internalFlag: 'N',
        })
        .then(res => {
          if (res.data.success) {
            notification.success({});
            setCanEdit(false);
            setButtonStatus(false);
            handleQueryDetail(inspectDocId);
          } else {
            notification.error({
              message:
                res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
        });
    } else {
      myInstance
        .post(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/approve-pass/ui`, {
          inspectDocId,
          internalFlag: 'Y',
          // @ts-ignore
          inspectReportUuid: insideDetailDs.toData()[0].inspectReportUuid,
        })
        .then(res => {
          if (res.data.success) {
            notification.success({});
            setCanEdit(false);
            setButtonStatus(false);
            handleQueryDetail(inspectDocId);
          } else {
            notification.error({
              message:
                res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
        });
    }
  };

  const handelReject = () => {
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.approveReject`).d('审批驳回'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: rejectReasonOk,
      children: (
        <Form dataSet={rejectReasonDs} columns={1}>
          <TextArea name="rejectReason" colSpan={3} />
        </Form>
      ),
    });
  };

  const handleClaimDoc = () => {
    const ds = verificationMethod === 'NJ' ? insideDetailDs : outerDetailDs;
    if (ds.current?.get('responId') !== Number(userInfo.id)) {
      return notification.error({
        message: intl
          .get(`${modelPrompt}.error.claimRoleError`)
          .d('只有量具责任人可以点击领取按钮，请检查！'),
      });
    }
    return new Promise(async resolve => {
      const res = await claimInspectDoc({
        params: {
          inspectDocId,
          servicedFlag: ds.current?.get('servicedFlag'),
        },
      });
      if (res.success) {
        notification.success({});
        handleQueryDetail(inspectDocId);
        return resolve(true);
      }
      return resolve(false);
    });
  };

  const handleConfirmClaimDoc = () => {
    const ds = verificationMethod === 'NJ' ? insideDetailDs : outerDetailDs;
    if (ds.current?.get('gaugerFlag') !== 'Y') {
      return notification.error({
        message: intl
          .get(`${modelPrompt}.error.claimConfirmRoleError`)
          .d('只有计量员可以点击领取确认按钮，请检查！'),
      });
    }
    return new Promise(async resolve => {
      const res = await confirmClaimInspectDoc({
        params: {
          inspectDocId,
          servicedFlag: ds.current?.get('servicedFlag'),
        },
      });
      if (res.success) {
        notification.success({});
        handleQueryDetail(inspectDocId);
        return resolve(true);
      }
      return resolve(false);
    });
  };

  const handlePreviewReport = () => {
    window.open(
      ''
        .concat(API_HOST, '/hrpt/v1/')
        .concat(tenantId, '/reports/export/QIS_MS_PLATFORM_REPORT/PRINT?access_token=')
        .concat(getAccessToken(), '&organizationId=')
        .concat(tenantId, '&inspectDocIdList=')
        .concat(inspectDocId),
    );
  };

  const handleLookReport = () => {
    const attachmentUUID =
      verificationMethod === 'WJ'
        ? outerDetailDs?.current?.get('inspectReportUuid')
        : insideDetailDs?.current?.get('inspectReportUuid');
    if (!attachmentUUID) {
      return notification.error({
        message: intl.get(`${modelPrompt}.inspectReportUuid.error`).d('未找到检定报告，请检查！'),
      });
    }
    myInstance
      .get(
        `/hfle/v1/${tenantId}/files/${attachmentUUID}/file?attachmentUUID=${attachmentUUID}&bucketName=qms`,
      )
      .then(res => {
        if (res.data?.length) {
          window.open(
            ''
              .concat(HZERO_FILE, '/v1/')
              .concat(tenantId, '/file-preview/by-url?url=')
              .concat(encodeURIComponent(res.data[0]?.fileUrl), '&bucketName=qms&access_token=')
              .concat(getAccessToken()),
          );
        }
      });
  };

  const rejectReasonOk = () => {
    myInstance
      .post(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/approve-rejected/ui`, {
        inspectDocId,
        // @ts-ignore
        rejectReason: rejectReasonDs.toData()[0].rejectReason,
      })
      .then(res => {
        if (res.data.success) {
          notification.success({});
          setCanEdit(false);
          setButtonStatus(false);
          rejectReasonDs.reset();
          handleQueryDetail(inspectDocId);
        } else {
          notification.error({
            message: res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
          });
        }
      });
  };

  const selectListClick = ({ key }) => {
    setSelectedKeys(key);
  };

  const handleRefresh = () => {
    handleInternalRefresh({
      queryParams: { inspectDocId },
      onSuccess: () => {
        handleQueryDetail(inspectDocId);
      },
    });
  };

  const cancelHaveDs = new DataSet({
    fields: [
      {
        name: 'cancelReason',
        type: FieldType.string,
        required: true,
        label: intl.get(`${modelPrompt}.cancelReason`).d('取消原因'),
      },
    ],
  });

  const handlePreviewCancel = () => {
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.cancelReasonOrd`).d('取消检定单'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: async () => {
        const validate = await cancelHaveDs.validate()
        if (validate) {
          const url = `${API_HOST}${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/doc-cancel/ui`;
          let applicationId;
          const { cancelReason } = cancelHaveDs.toData()[0] || {};
          if (verificationMethod === 'WJ') {
            const { applicationDocId } = outerDetailDs.toData()[0];
            applicationId = applicationDocId;
          } else {
            const { applicationDocId } = insideDetailDs.toData()[0];
            applicationId = applicationDocId;
          }
          const params = {
            inspectDocId,
            cancelReason,
            applicationDocId: applicationId,
          };
          const res: any = await axios.post(url, params);
          if (res && res.success) {
            notification.success({});
            cancelHaveDs.loadData([]);
            if (verificationMethod === 'WJ') {
              outerDetailDs.query();
            } else {
              insideDetailDs.query();
            }
            setStatus('CANCEL');
          } else {
            notification.error({
              message: res.message,
            });
            return false;
          }
        } else {
          return false
        }
      },
      children: (
        <Form dataSet={cancelHaveDs} autoFocus columns={1}>
          <TextArea name="cancelReason" autoFocus colSpan={3} />
        </Form>
      ),
    });
  };

  const maxDecimalPlaces = (num1, num2) => {
    const decimalPlaces1 = (num1.toString().split('.')[1] || '').length;
    const decimalPlaces2 = (num2.toString().split('.')[1] || '').length;
    return Math.max(decimalPlaces1, decimalPlaces2);
  }

  // @ts-ignore
  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={outerDetailDs || insideDetailDs}>
        <Header
          title={intl.get(`${modelPrompt}.title.docDist`).d('量具检定平台/检定单详情')}
          backPath="/hmes/measure-have/platform/list/2"
        >
          {buttonStatus ? (
            <>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button
                disabled={!['RELEASED', 'REJECTED'].includes(status)}
                color={ButtonColor.primary}
                icon="save"
                onClick={() => handleSave({ submitFlag: 'N' })}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button
                disabled={!['RELEASED', 'REJECTED'].includes(status)}
                color={ButtonColor.primary}
                onClick={() => handleSave({ submitFlag: 'Y' })}
              >
                {intl.get(`${modelPrompt}.button.complete`).d('检定完成')}
              </Button>
            </>
          ) : (
            <Button
              icon="edit-o"
              color={ButtonColor.primary}
              disabled={editButton || status === 'CANCEL'}
              onClick={handleEdit}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
          {['NEW'].includes(status) && buttonStatus && (
            <Button
              disabled={!['NEW', 'REJECTED'].includes(status)}
              color={ButtonColor.primary}
              onClick={transmit}
            >
              {intl.get(`${modelPrompt}.button.release`).d('下达')}
            </Button>
          )}
          {['TO_APPROVAL'].includes(status) && (
            <>
              <PermissionButton
                disabled={!['TO_APPROVAL'].includes(status)}
                type="c7n-pro"
                permissionList={[
                  {
                    code: `MeasureHavePlatform.button.inspectPass`,
                    type: 'button',
                    meaning: '检验单详情页-审核通过',
                  },
                ]}
                color={ButtonColor.green}
                onClick={handleApproval}
              >
                {intl.get(`${modelPrompt}.button.approvePass`).d('审核通过')}
              </PermissionButton>
              <PermissionButton
                disabled={!['TO_APPROVAL'].includes(status)}
                type="c7n-pro"
                permissionList={[
                  {
                    code: `MeasureHavePlatform.button.inspectReject`,
                    type: 'button',
                    meaning: '检验单详情页-审核驳回',
                  },
                ]}
                color={ButtonColor.red}
                onClick={handelReject}
              >
                {intl.get(`${modelPrompt}.button.approveReject`).d('审核驳回')}
              </PermissionButton>
            </>
          )}
          {status === 'TO_CLAIM' && (
            <PermissionButton
              disabled={status !== 'TO_CLAIM'}
              type="c7n-pro"
              permissionList={[
                {
                  code: `MeasureHavePlatform.dist.button.claim`,
                  type: 'button',
                  meaning: '检验单详情页-领取按钮',
                },
              ]}
              onClick={handleClaimDoc}
            >
              {intl.get(`${modelPrompt}.button.claim`).d('领取')}
            </PermissionButton>
          )}
          {status === 'TO_CONFIRM' && (
            <PermissionButton
              disabled={status !== 'TO_CONFIRM'}
              type="c7n-pro"
              // permissionList={[
              //   {
              //     code: `MeasureHavePlatform.dist.button.claimConfirm`,
              //     type: 'button',
              //     meaning: '检验单详情页-领取确认按钮',
              //   },
              // ]}
              onClick={handleConfirmClaimDoc}
            >
              {intl.get(`${modelPrompt}.button.claim`).d('领取确认')}
            </PermissionButton>
          )}
          {['COMPLETED'].includes(status) && (
            <Button onClick={print}>
              {intl.get(`${modelPrompt}.button.tagPrint`).d('标签打印')}
            </Button>
          )}
          {status === 'COMPLETED' && verificationMethod === 'NJ' && (
            <PermissionButton
              disabled={!['COMPLETED'].includes(status)}
              type="c7n-pro"
              permissionList={[
                {
                  code: `MeasureHavePlatform.button.report`,
                  type: 'button',
                  meaning: '检验单详情页-查看报告',
                },
              ]}
              onClick={handleLookReport}
            >
              {intl.get(`${modelPrompt}.button.report`).d('查看报告')}
            </PermissionButton>
          )}
          {status === 'TO_APPROVAL' && verificationMethod === 'NJ' && (
            <PermissionButton
              disabled={!['TO_APPROVAL'].includes(status)}
              type="c7n-pro"
              permissionList={[
                {
                  code: `MeasureHavePlatform.button.reportPreview`,
                  type: 'button',
                  meaning: '检验单详情页-检定报告预览',
                },
              ]}
              onClick={handlePreviewReport}
            >
              {intl.get(`${modelPrompt}.button.reportPreview`).d('检定报告预览')}
            </PermissionButton>
          )}
          {(outerDetailDs.current?.get('inspectorId') ||
            insideDetailDs.current?.get('inspectorId')) === Number(userInfo.id) && (
              <PermissionButton
                disabled={!['NEW', 'RELEASED'].includes(status)}
                type="c7n-pro"
                permissionList={[
                  {
                    code: `MeasureHavePlatform.button.reportCancel`,
                    type: 'button',
                    meaning: '检验单详情页-取消检定单',
                  },
                ]}
                onClick={handlePreviewCancel}
              >
                {intl.get(`${modelPrompt}.button.reportCancel`).d('取消检定单')}
              </PermissionButton>
            )}
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['taskInfo']}>
            <Panel key="taskInfo" header={intl.get(`${modelPrompt}.title.headInfo`).d('头数据')}>
              {// 外检
                verificationMethod === 'WJ' && (
                  <Form dataSet={outerDetailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                    <TextField name="inspectDocNum" />
                    <Select name="inspectDocStatus" />
                    <Lov name="siteLov" />
                    <TextField name="applicationCreatedByName" />
                    <TextField name="departmentName" />
                    <Select name="docType" />
                    <TextField name="applicationDocNum" />
                    <DateTimePicker name="applicationCreationDate" />
                    <TextField name="responName" />
                    <TextField name="toolCode" />
                    <TextField name="speciesName" />
                    <TextField name="modelCode" />
                    <Select name="verificationMethod" />
                    <TextField name="verificationPeriod" />
                    <Select name="verificationAgency" />
                    <TextField name="createdByName" />
                    <DateTimePicker name="creationDate" />
                    <DateTimePicker name="completionTime" />
                    <TextField name="reviewByName" />
                    <DateTimePicker name="reviewDate" />
                    <TextField name="rejectReason" />
                    <Lov name="inspectorObj" />
                    <Select name="inspectPlace" />
                    <DateTimePicker name="deliveryTime" />
                    <Select name="inspectResult" onChange={handleChangeResult} />
                    <TextField name="ngReason" />
                    <Select name="servicedFlag" />
                    <TextField name="remark" />
                    <Select name="docType" />
                    <DateTimePicker name="creationDate" />
                    <TextField name="createdByName" />
                    <TextField name="departmentName" />
                    <TextField name="reportCode" />
                    <TextField name="cancelReason" disabled />
                    <TextField name="cancelDate" disabled />
                    <Attachment name="inspectReportUuid" newLine />
                    <Attachment name="returnedUuid" />
                    <Attachment name="overtimeFileUuid" newLine />
                    <Attachment name="ncFileUuid" newLine />
                  </Form>
                )}
              {// 内检
                verificationMethod === 'NJ' && (
                  <Form dataSet={insideDetailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                    <TextField name="inspectDocNum" />
                    <Select name="inspectDocStatus" />
                    <Lov name="siteLov" />
                    <TextField name="applicationCreatedByName" />
                    <TextField name="departmentName" />
                    <Select name="docType" />
                    <TextField name="applicationDocNum" />
                    <DateTimePicker name="applicationCreationDate" />
                    <TextField name="responName" />
                    <TextField name="toolCode" />
                    <TextField name="speciesName" />
                    <TextField name="modelCode" />
                    <Select name="verificationMethod" />
                    <TextField name="verificationPeriod" />
                    <Select name="verificationAgency" />
                    <TextField name="createdByName" />
                    <DateTimePicker name="creationDate" />
                    <DateTimePicker name="completionTime" />
                    <Lov name="inspectorObj" />
                    <Select name="inspectPlace" />
                    <Select name="inspectResult" onChange={handleChangeResult} />
                    <Select name="appearanceResult" onChange={handleUpdateInspectResult} />
                    <TextField name="ngReason" />
                    <Select name="servicedFlag" />
                    <TextField name="temperature" />
                    <TextField name="humidness" />
                    <TextField name="remark" />
                    <TextField name="reviewByName" />
                    <DateTimePicker name="reviewDate" />
                    <TextField name="rejectReason" />
                    <TextField name="cancelReason" disabled />
                    <TextField name="cancelDate" disabled />
                    <Attachment name="overtimeFileUuid" newLine />
                    <Attachment name="ncFileUuid" newLine />
                  </Form>
                )}
            </Panel>
          </Collapse>
          {verificationMethod === 'NJ' && <Divider />}
          {// 内检
            verificationMethod === 'NJ' && (
              <div className={styles.auditRecord}>
                <div
                  className={styles['audit-record-list']}
                  style={{
                    display: 'block',
                  }}
                >
                  <Menu
                    style={{ width: '100%' }}
                    defaultOpenKeys={['sub1']}
                    mode="inline"
                    // @ts-ignore
                    selectedKeys={selectedKeys}
                    onClick={selectListClick}
                  >
                    <Menu.Item disabled key="title">
                      <div className={styles['record-list-title']}>
                        <span>{intl.get(`${modelPrompt}.inspectItemList`).d('检测项目列表')}</span>
                        <Popconfirm
                          title={intl.get(`${modelPrompt}.confirm.refresh`).d('是否确认刷新?')}
                          onConfirm={() => handleRefresh()}
                          okText={intl.get('tarzan.common.button.confirm').d('确认')}
                          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                        >
                          <Button
                            icon="autorenew"
                            loading={refreshLoading}
                            funcType={FuncType.flat}
                            disabled={!['RELEASED', 'REJECTED'].includes(status) || !canEdit}
                          />
                        </Popconfirm>
                      </div>
                    </Menu.Item>
                    {lineInfo &&
                      lineInfo.length > 0 &&
                      lineInfo.map((item, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <Menu.Item key={index}>
                          <div className={styles['record-list-item']}>
                            <div className={styles['record-list-item-text']}>
                              {`${index + 1} `}
                              {item.inspectDescription}{' '}
                            </div>
                            <div className={styles['record-list-item-info']}>
                              <div className="left">
                                {item.dataType === 'VALUE' ? (
                                  <Tag color="green">数值</Tag>
                                ) : (
                                  <Tag color="blue">判定值</Tag>
                                )}
                              </div>
                              {item.uomCode && <div className="right">单位：{item.uomCode}</div>}
                            </div>
                          </div>
                        </Menu.Item>
                      ))}
                  </Menu>
                </div>
                <div className={styles['audit-record-detial']}>
                  <Collapse bordered={false} defaultActiveKey={['taskInfo']}>
                    <Panel
                      key="taskInfo"
                      header={intl.get(`${modelPrompt}.title.verificationRecord`).d('检定记录')}
                    >
                      {tableDsArr.length && (
                        <Table
                          buttons={[
                            // <RenderDeleteButton dataSet={tableDsArr[selectedKeys]} canEdit={!canEdit} />,
                            <Button
                              icon="delete"
                              funcType={FuncType.flat}
                              color={ButtonColor.red}
                              disabled={!canEdit || !['REJECTED', 'RELEASED'].includes(status)}
                              onClick={() =>
                                tableDsArr[selectedKeys].remove(tableDsArr[selectedKeys].selected)
                              }
                            >
                              {intl.get(`${modelPrompt}.button.delete`).d('删除')}
                            </Button>,
                            [
                              TableButtonType.add,
                              {
                                disabled: !canEdit || !['REJECTED', 'RELEASED'].includes(status),
                                // @ts-ignore
                                onClick: () =>
                                  tableDsArr[selectedKeys].create({
                                    inspectLineId: lineInfo[selectedKeys].inspectLineId,
                                  }),
                              },
                            ],
                          ]}
                          rowHeight="auto"
                          dataSet={tableDsArr[selectedKeys]}
                          columns={taskColumns}
                        />
                      )}
                    </Panel>
                  </Collapse>
                </div>
              </div>
            )}
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(InspectDetail);
