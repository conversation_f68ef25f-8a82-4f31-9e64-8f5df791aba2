/**
 * @Description: 装配清单 - 参考点抽屉（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/7/29 16:15
 * @LastEditTime: 2022/7/29 16:15
 * @LastEditors: <EMAIL>
 */
import React, { FC, useMemo } from 'react';
import { DataSet, Table, Switch } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { Badge, Popconfirm } from 'choerodon-ui';

interface ReferencePointDrawerProps {
  referencePointDrawerDs: DataSet;
  canEdit: boolean;
  path: string;
}

export const ReferencePointDrawer: FC<ReferencePointDrawerProps> = props => {
  const {
    referencePointDrawerDs,
    canEdit,
    path,
  } = props;

  // 新增参考点的回调
  const handleAddReferencePoint = () => {
    let maxLineNumber = 0;
    referencePointDrawerDs.toData().forEach((lineItem: any) => {
      if (lineItem.lineNumber > maxLineNumber) {
        maxLineNumber = lineItem.lineNumber;
      }
    });
    const record = referencePointDrawerDs.create(
      {
        uuid: uuid(),
        lineNumber: parseInt(String(maxLineNumber / 10), 10) * 10 + 10,
      },
      0,
    );
    record.setState('editing', true);
  };

  // 取消时，将未保存的行数据删除或回退
  const deleteRecord = record => {
    if (record.status === 'add') {
      referencePointDrawerDs.remove(record);
    } else {
      record.reset();
      record.setState('editing', false);
    }
  };

  // 保存参考点的回调
  const handleSave = async record => {
    const validateResult = await record?.validate();
    if (validateResult) {
      record.init({ ...record.toData() });
      // record.status = 'sync';
      record.setState('editing', false);
    }
  };

  // 参考点表格列
  const referencePointDrawerColumns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <PermissionButton
            type="c7n-pro"
            icon="add"
            disabled={!canEdit}
            onClick={handleAddReferencePoint}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        ),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteRecord(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              disabled={record?.get('bomReferencePointId')}
              funcType="flat"
              shape="circle"
              size="small"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'lineNumber',
        width: 150,
        align: ColumnAlign.left,
        editor: record => record.getState('editing'),
      },
      {
        name: 'referencePoint',
        width: 250,
        editor: record => record.getState('editing'),
      },
      {
        name: 'qty',
        width: 150,
        editor: record => record.getState('editing'),
      },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        editor: record => record.getState('editing') && <Switch />,
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        width: 200,
        renderer: ({ record }) => {
          return (
            <span className="action-link">
              {record?.getState('editing') ? (
                <>
                  <a onClick={() => deleteRecord(record)}>
                    {intl.get('tarzan.common.button.cancel').d('取消')}
                  </a>
                  <a onClick={() => handleSave(record)}>
                    {intl.get('tarzan.common.button.save').d('保存')}
                  </a>
                </>
              ) : (
                <a disabled={!canEdit} onClick={() => record?.setState('editing', true)}>
                  {intl.get('tarzan.common.button.edit').d('编辑')}
                </a>
              )}
            </span>
          );
        },
      },
    ],
    [],
  );

  return (
    <Table
      key="uuid"
      dataSet={referencePointDrawerDs}
      columns={referencePointDrawerColumns}
      filter={record => {
        return record.status !== 'delete';
      }}
    />
  );
};
