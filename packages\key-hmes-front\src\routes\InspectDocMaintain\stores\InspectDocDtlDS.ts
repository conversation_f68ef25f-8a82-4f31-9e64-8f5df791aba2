/**
 * @Description: 检验单维护-详情页 检验单及报检信息DS
 * @Author: <EMAIL>
 * @Date: 2023/1/9 16:37
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldTrim } from 'choerodon-ui/dataset/data-set/enum';
import uuid from 'uuid/v4';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment/moment';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.hwms.inspectDocMaintain';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const inspectDocDtlDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          rows?.inspectInfoList?.forEach((item, index) => {
            item.sequence = index * 10 + 10;
            item.soNumContent = item.soLineNum
              ? `${item.soNumber}/${item.soLineNum}`
              : item.soNumber;
            item.poNumberAndLine = item.poLineNum
              ? `${item.poNumber}/${item.poLineNum}`
              : item.poNumber;
          });
          rows?.inspectInfoDtlList?.forEach((item, index) => {
            item.sequence = index * 10 + 10;
          });
          return {
            ...rows?.baseInfo,
            inspectInfoList:
              rows?.inspectInfoList?.length > 0
                ? rows.inspectInfoList.map(item => {
                  const newItem = { ...item };
                  if (item.soNumber && item.soLineNum) {
                    newItem.soNumContent = `${item.soNumber}/${item.soLineNum}`;
                  } else {
                    newItem.soNumContent = item.soNumber || item.soLineNum || null;
                  }
                  if (item.poNumber && item.poLineNum) {
                    newItem.poNumberAndLine = `${item.poNumber}/${item.poLineNum}`;
                  } else {
                    newItem.poNumberAndLine = item.poNumber || item.poLineNum || null;
                  }
                  return newItem;
                })
                : rows?.inspectInfoList,
            inspectInfoDtlList: rows?.inspectInfoDtlList,
          };
        },
      };
    },
  },
  fields: [
    {
      name: 'inspectDocId',
      type: FieldType.number,
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单编码'),
      disabled: true,
    },
    {
      name: 'inspectDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('检验单状态'),
      disabled: true,
      defaultValue: 'NEW',
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSPECT_DOC_STATUS&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      ignore: FieldIgnore.always,
      required: true,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      required: true,
      lookupCode: 'APEX_QMS.INSPECT_BUS_TYPE_DOC_CREATE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId') || !record?.get('siteId'),
        // lovPara: ({ record }) => ({
        //   tenantId,
        //   siteId: record?.get('siteId'),
        // }),
      },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialRevision`).d('物料/版本'),
      lovCode: 'MT.QMS.MATERIAL',
      textField: 'materialName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record.get('siteId'),
          inspectBusinessType: record.get('inspectBusinessType'),
        }),
        disabled: ({ record }) =>
          !record.get('siteId') ||
          record?.get('inspectDocId') ||
          !record.get('inspectBusinessType'),
        required: ({ record }) => {
          if (!['IPQC-F', 'IPQC-M', 'IPQC-L', 'EXC-QC', 'LQC-P'].includes(record?.get('inspectBusinessType'))) {
            return !(
              record.get('materialId') ||
              record.get('customerId') ||
              record.get('supplierId') ||
              record.get('processWorkcellId') ||
              record.get('stationWorkcellId') ||
              record.get('operationId') ||
              record.get('areaId') ||
              record.get('prodLineId') ||
              record.get('equipmentId')
            );
          }
          return true;

        },
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      bind: 'materialLov.materialName',
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      bind: 'materialLov.materialCode',
      disabled: true,
    },
    {
      name: 'model',
      bind: 'materialLov.model',
      disabled: true,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
    },
    {
      name: 'bomCode',
      disabled: true,
      bind: 'materialLov.bomCode',
      label: intl.get(`${modelPrompt}.bomCode`).d('物料BOM'),
    },
    {
      name: 'revisionFlag',
      bind: 'materialLov.revisionFlag',
    },
    {
      name: 'identifyType',
      bind: 'materialLov.identifyType',
    },
    {
      name: 'revisionCode',
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      bind: 'materialLov.currentRevisionCode',
      textField: 'description',
      valueField: 'description',
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: item?.kid || uuid(),
                  description: item?.description || item,
                };
              });
            }
            if (record) {
              if (firstlyQueryData.length > 0) {
                if (!record?.get('revisionCode')) {
                  record?.init('revisionCode', firstlyQueryData[0].description);
                }
              } else {
                record?.init('revisionCode');
              }
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record?.get('materialId') ||
            record?.get('revisionFlag') !== 'Y' ||
            record?.get('inspectDocId')
          );
        },
        required({ record }) {
          return record?.get('revisionFlag') === 'Y' && record?.get('materialId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')],
            materialId: record?.get('materialId') || undefined,
          };
        },
      },
    },
    {
      name: 'originalTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.originalType`).d('原型号'),
      lovCode: 'MT.QMS.MATERIAL',
      textField: 'materialName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record.get('siteId'),
          inspectBusinessType: record.get('inspectBusinessType'),
        }),
        required: ({ record }) => record?.get('inspectBusinessType') === 'EXC-QC',
      },
    },
    {
      name: 'originalTypeId',
      bind: 'originalTypeLov.materialId',
    },
    {
      name: 'originalTypeDesc',
      bind: 'originalTypeLov.materialName',
    },
    {
      name: 'originalType',
      bind: 'originalTypeLov.materialCode',
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
        required: ({ record }) => {
          return !(
            record.get('materialId') ||
            record.get('customerId') ||
            record.get('supplierId') ||
            record.get('processWorkcellId') ||
            record.get('stationWorkcellId') ||
            record.get('operationId') ||
            record.get('areaId') ||
            record.get('prodLineId') ||
            record.get('equipmentId')
          );
        },
      },
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'customerName',
      bind: 'customerLov.customerName',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
        required: ({ record }) => {
          return !(
            record.get('materialId') ||
            record.get('customerId') ||
            record.get('supplierId') ||
            record.get('processWorkcellId') ||
            record.get('stationWorkcellId') ||
            record.get('operationId') ||
            record.get('areaId') ||
            record.get('prodLineId') ||
            record.get('equipmentId')
          );
        },
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序'),
      lovCode: 'HME.MODEL.WORKCELL',
      textField: 'workcellName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          workcellType: 'PROCESS',
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) =>
          record?.get('inspectDocId') ||
          !record?.get('siteId') ||
          !record?.get('inspectBusinessType'),
        required: ({ record }) => {
          if (!['IPQC-F', 'IPQC-M', 'IPQC-L', 'EXC-QC', 'LQC-P'].includes(record?.get('inspectBusinessType'))) {
            return !(
              record.get('materialId') ||
              record.get('customerId') ||
              record.get('supplierId') ||
              record.get('processWorkcellId') ||
              record.get('stationWorkcellId') ||
              record.get('operationId') ||
              record.get('areaId') ||
              record.get('prodLineId') ||
              record.get('equipmentId')
            );
          }
          return true;

        },
      },
    },
    {
      name: 'processWorkcellId',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'processWorkcellName',
      bind: 'processWorkcellLov.workcellName',
    },
    {
      name: 'stationWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.stationWorkcellName`).d('工位'),
      lovCode: 'HME.MODEL.WORKCELL',
      textField: 'workcellName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          workcellType: 'STATION',
          siteId: record?.get('siteId'),
          processWorkcellId: record?.get('processWorkcellId'),
        }),
        disabled: ({ record }) =>
          record?.get('inspectDocId') ||
          !record?.get('siteId') ||
          !record?.get('inspectBusinessType'),
        required: ({ record }) => {
          if (!['IPQC-F', 'IPQC-M', 'IPQC-L', 'EXC-QC', 'LQC-P'].includes(record?.get('inspectBusinessType'))) {
            return !(
              record.get('materialId') ||
              record.get('customerId') ||
              record.get('supplierId') ||
              record.get('processWorkcellId') ||
              record.get('stationWorkcellId') ||
              record.get('operationId') ||
              record.get('areaId') ||
              record.get('prodLineId') ||
              record.get('equipmentId')
            );
          }
          return true;

        },
      },
    },
    {
      name: 'stationWorkcellId',
      bind: 'stationWorkcellLov.workcellId',
    },
    {
      name: 'stationWorkcellName',
      bind: 'stationWorkcellLov.workcellName',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
        required: ({ record }) => {
          return !(
            record.get('materialId') ||
            record.get('customerId') ||
            record.get('supplierId') ||
            record.get('processWorkcellId') ||
            record.get('stationWorkcellId') ||
            record.get('operationId') ||
            record.get('areaId') ||
            record.get('prodLineId') ||
            record.get('equipmentId')
          );
        },
      },
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    {
      name: 'areaLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaName`).d('区域'),
      lovCode: 'MT.MODEL.AREA',
      textField: 'areaName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) =>
          record?.get('inspectDocId') ||
          !record?.get('siteId') ||
          !record?.get('inspectBusinessType'),
        required: ({ record }) => {
          return !(
            record.get('materialId') ||
            record.get('customerId') ||
            record.get('supplierId') ||
            record.get('processWorkcellId') ||
            record.get('stationWorkcellId') ||
            record.get('operationId') ||
            record.get('areaId') ||
            record.get('prodLineId') ||
            record.get('equipmentId')
          );
        },
      },
    },
    {
      name: 'areaId',
      bind: 'areaLov.areaId',
    },
    {
      name: 'areaName',
      bind: 'areaLov.areaName',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) =>
          record?.get('inspectDocId') ||
          !record?.get('siteId') ||
          !record?.get('inspectBusinessType'),
        required: ({ record }) => {
          return !(
            record.get('materialId') ||
            record.get('customerId') ||
            record.get('supplierId') ||
            record.get('processWorkcellId') ||
            record.get('stationWorkcellId') ||
            record.get('operationId') ||
            record.get('areaId') ||
            record.get('prodLineId') ||
            record.get('equipmentId')
          );
        },
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      textField: 'locatorName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorName',
      bind: 'locatorLov.locatorName',
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
        required: ({ record }) => {
          if (record?.get('inspectBusinessType') === 'EXC-QC') {
            return true;
          }
          return !(
            record.get('materialId') ||
            record.get('customerId') ||
            record.get('supplierId') ||
            record.get('processWorkcellId') ||
            record.get('stationWorkcellId') ||
            record.get('operationId') ||
            record.get('areaId') ||
            record.get('prodLineId') ||
            record.get('equipmentId')
          );
        },
      },
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'equipmentCode',
      bind: 'equipmentLov.equipmentCode',
    },
    {
      name: 'inspectSumQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.inspectSumQty`).d('报检总数'),
      min: 0,
      precision: 6,
      nonStrictStep: true,
      disabled: true,
    },
    {
      name: 'uomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
      lovCode: 'MT.COMMON.UOM',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'uomName',
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'uomId',
      bind: 'uomLov.uomId',
    },
    {
      name: 'uomName',
      bind: 'uomLov.uomName',
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'docCreateMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docCreateMethod`).d('单据创建方式'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.DOC_CREATE_METHOD',
      defaultValue: 'MES_CREATE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'inspectorUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检验员'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
        // required: ({ record }) => !record?.get('inspectDocId'),
      },
    },
    {
      name: 'inspectorUserId',
      bind: 'inspectorUserLov.id',
    },
    {
      name: 'inspectorUserName',
      bind: 'inspectorUserLov.realName',
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      disabled: true,
    },
    {
      name: 'shiftTeamLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.shiftTeamCode`).d('班组'),
      ignore: FieldIgnore.always,
      textField: 'shiftTeamCode',
      lovCode: 'MT.SHIFTTEAM',
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'shiftTeamId',
      bind: 'shiftTeamLov.shiftTeamId',
    },
    {
      name: 'shiftTeamCode',
      bind: 'shiftTeamLov.shiftTeamCode',
    },
    {
      name: 'shiftDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.shiftDate`).d('班次日期'),
      format: 'YYYY-MM-DD',
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
      // lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-work-platform/shift-get/for/ui`,
      valueField: 'shiftInfo',
      textField: 'shiftInfo',
      lookupAxiosConfig: ({ record }) => {
        if (record?.get('shiftTeamId')) {
          return {
            params: {
              shiftTeamId: record?.get('shiftTeamId'),
              date: moment(record?.get('shiftDate')).format('YYYY-MM-DD HH:mm:ss'),
            },
            url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-work-platform/shift-get/for/ui`,
            method: 'GET',
            transformResponse(data) {
              if (!data) {
                return;
              }
              // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
              if (data instanceof Array) {
                return data;
              }
              const { rows } = JSON.parse(data);
              return rows;
            },
          };
        }
        return { url: undefined };

        // return {
        //   params: {
        //     shiftTeamId: record?.get('shiftTeamId'),
        //     date: moment(record?.get('shiftDate')).format('YYYY-MM-DD HH:mm:ss'),
        //   },
        //   transformResponse(data) {
        //     if (!data) {
        //       return;
        //     }
        //     // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
        //     if (data instanceof Array) {
        //       return data;
        //     }
        //     const { rows } = JSON.parse(data);
        //     return rows;
        //   },
        // };
      },
      dynamicProps: {
        disabled: ({ record }) =>
          !record.get('shiftTeamId') || !record.get('shiftDate') || record?.get('inspectDocId'),
      },
    },
    {
      name: 'editFlag', // 编辑界面特定字段是否可编辑的标识
      type: FieldType.string,
      defaultValue: 'Y',
    },
    {
      name: 'inspectSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'samplingMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'strictnessDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strictness`).d('抽样严格度'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'samplingDimensionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingDimension`).d('抽样维度'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'samplingQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampling.qty`).d('检验对象个数'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'acceptStandard',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.acceptStandard`).d('接收准则'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'ac',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.acceptStandard.ac`).d('Ac='),
      min: 1,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 're',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.frequencyParams.re`).d('Re='),
      min: 1,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'actualStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('实际开始时间'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualEndTIme`).d('实际结束时间'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'okQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.okQty`).d('合格品数'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => record?.get('editFlag') !== 'Y',
      },
    },
    {
      name: 'ngQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngQty`).d('不合格品数'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => record?.get('editFlag') !== 'Y',
      },
    },
    {
      name: 'scrapQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapQty`).d('报废品数'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => record?.get('editFlag') !== 'Y',
      },
    },
    {
      name: 'firstInspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstInspectResult`).d('初评结果'),
      lookupCode: 'MT.QMS.INSPECT_RESULT',
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record }) => record?.get('editFlag') !== 'Y',
        required: ({ record }) => record?.get('editFlag') === 'Y' && record?.get('inspectDocId'),
      },
    },
    {
      name: 'firstInspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstInspectorName`).d('初评记录人'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'firstInspectDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstInspectDate`).d('初评时间'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'lastInspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastInspectResult`).d('最终结果'),
      lookupCode: 'MT.QMS.INSPECT_RESULT',
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record }) => record?.get('editFlag') !== 'Y',
        required: ({ record }) => record?.get('editFlag') === 'Y' && record?.get('inspectDocId'),
      },
    },
    {
      name: 'lastInspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastInspectorName`).d('最终记录人'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'lastInspectDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastInspectDate`).d('最终时间'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('inspectDocId'),
      },
    },
  ],
  events: {
    update: ({ name, record }) => {
      switch (name) {
        case 'shiftTeamLov':
        case 'shiftDate':
          record.init('shiftCode');
          break;
        case 'materialLov':
          if (record?.get('materialLov')) {
            record.init('uomLov', {
              uomId: record?.get('materialLov')?.primaryUomId,
              uomName: record?.get('materialLov')?.primaryUomName,
            });
          } else {
            record.init('uomLov', undefined);
          }
          break;
        default:
          break;
      }
    },
  },
});

const sourceDocDS: () => DataSetProps = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  forceValidate: true,
  dataKey: 'rows',
  selection: false,
  paging: false,
  fields: [
    {
      name: 'barcodeSwitch',
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sourceDoc.sequence`).d('序号'),
      defaultValue: 10,
    },
    {
      name: 'sourceObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceDoc.sourceObjectType`).d('来源单据类型'),
      lovPara: { tenantId },
      lookupCode: 'MT.SOURCE_OBJECT_TYPE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('barcodeSwitch');
        },
      },
    },
    {
      name: 'sourceObjectLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceDoc.sourceObjectCode`).d('来源单据编码'),
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record }) => {
          if (record.get('barcodeSwitch')) {
            return false;
          }
          return record.get('sourceObjectType');
        },
        disabled: ({ record, dataSet }) =>
          !record.get('sourceObjectType') ||
          !dataSet.parent?.current?.get('siteId') ||
          record.get('barcodeSwitch'),
        lovCode: ({ record,dataSet }) => {
          if (['MES_CREATE', 'MANUAL'].includes(dataSet.getState('docCreateMethod'))) {
            switch (record.get('sourceObjectType')) {
              case 'WO':
                return 'MT.WORK_ORDER';
              case 'DELIVERY_DOC':
              case 'OUTSOURCE_DOC':
              case 'OUTSOURCING_RETURN_DOC':
              case 'SO_RETURN_DOC':
              case 'SO_DELIVERY_DOC':
              case 'TRANSFER_DOC':
                return 'MT.MES.INSTRUCTION_DOC2';
              default:
                return 'MT.CONTAINER';
            }
          }
          switch (record.get('sourceObjectType')) {
            case 'WO':
              return 'MT.WMS.WORK_ORDER';
            case 'DELIVERY_DOC':
            case 'OUTSOURCE_DOC':
            case 'OUTSOURCING_RETURN_DOC':
            case 'SO_RETURN_DOC':
            case 'SO_DELIVERY_DOC':
            case 'TRANSFER_DOC':
              return 'MT.WMS.INSTRUCTION_DOC2';
            default:
              return 'MT.WMS.CONTAINER';
          }
        },
        lovPara: ({ record, dataSet }) => {
          switch (record.get('sourceObjectType')) {
            case 'WO':
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
              };
            case 'DELIVERY_DOC':
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
                instructionDocTypes: ['DELIVERY_DOC'],
              };
            case 'OUTSOURCE_DOC':
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
                instructionDocTypes: ['OUTSOURCE_DOC'],
              };
            case 'OUTSOURCING_RETURN_DOC':
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
                instructionDocTypes: ['OUTSOURCING_RETURN_DOC'],
              };
            case 'SO_RETURN_DOC':
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
                instructionDocTypes: ['SO_RETURN_DOC'],
              };
            case 'SO_DELIVERY_DOC':
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
                instructionDocTypes: ['SO_DELIVERY_DOC'],
              };
            case 'TRANSFER_DOC':
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
                instructionDocTypes: [
                  'RECEIVE_EXECUTE_IN_SITE',
                  'SEND_EXECUTE_IN_SITE',
                  'SEND_RECEIVE_EXECUTE_IN_SITE',
                  'RECEIVE_EXECUTE_OVER_SITE',
                  'SEND_EXECUTE_OVER_SITE',
                  'SEND_RECEIVE_EXECUTE_OVER_SITE',
                ],
              };
            default:
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
              };
          }
        },
      },
    },
    {
      name: 'sourceObjectId',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('sourceObjectType')) {
            case 'WO':
              return 'sourceObjectLov.workOrderId';
            case 'DELIVERY_DOC':
            case 'OUTSOURCE_DOC':
            case 'OUTSOURCING_RETURN_DOC':
            case 'SO_RETURN_DOC':
            case 'SO_DELIVERY_DOC':
            case 'TRANSFER_DOC':
              return 'sourceObjectLov.instructionDocId';
            default:
              return 'sourceObjectLov.containerId';
          }
        },
      },
    },
    {
      name: 'sourceObjectCode',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('sourceObjectType')) {
            case 'WO':
              return 'sourceObjectLov.workOrderNum';
            case 'DELIVERY_DOC':
            case 'OUTSOURCE_DOC':
            case 'OUTSOURCING_RETURN_DOC':
            case 'SO_RETURN_DOC':
            case 'SO_DELIVERY_DOC':
            case 'TRANSFER_DOC':
              return 'sourceObjectLov.instructionDocNum';
            default:
              return 'sourceObjectLov.containerCode';
          }
        },
        disabled: ({ record }) => {
          return record.get('barcodeSwitch');
        },
      },
    },
    {
      name: 'sourceObjectLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceDoc.sourceObjectLine`).d('行号'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MES.INSTRUCTION_DOC_LINE',
      textField: 'lineNumber',
      valueField: 'instructionDocLineId',
      dynamicProps: {
        disabled: ({ record }) =>
          !record.get('sourceObjectId') ||
          ['WO', 'CONTAINER'].includes(record.get('sourceObjectType')) ||
          record.get('barcodeSwitch'),
        lovPara: ({ record }) => {
          return {
            tenantId,
            instructionDocId: record.get('sourceObjectId'),
          };
        },
      },
    },
    {
      name: 'sourceObjectLineId',
      bind: 'sourceObjectLineLov.instructionDocLineId',
    },
    {
      name: 'sourceObjectLineCode',
      bind: 'sourceObjectLineLov.lineNumber',
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sourceDoc.sourceObjectQty`).d('数量'),
      defaultValue: 0,
      min: 0,
      step: 1,
      precision: 6,
      nonStrictStep: true,
      validator: value => {
        if (value === 0) {
          return intl
            .get(`${modelPrompt}.validation.sourceObjectQtyMoreThanZero`)
            .d('数量必须大于0!');
        }
      },
    },
    {
      name: 'workOrderLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceDoc.workOrderCode`).d('工单编号'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.WORK_ORDER',
      dynamicProps: {
        disabled: ({ record, dataSet }) =>
          record.get('poLineId') || record.get('soId') || !dataSet.parent?.current?.get('siteId'),
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
      },
    },
    {
      name: 'workOrderId',
      bind: 'workOrderLov.workOrderId',
    },
    {
      name: 'woNumber',
      bind: 'workOrderLov.workOrderNum',
    },
    {
      name: 'soLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceDoc.soNumAndSoLine`).d('销售订单编号/行号'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MES.SO_LINE',
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record, dataSet }) =>
          record.get('poLineId') ||
          record.get('workOrderId') ||
          !dataSet.parent?.current?.get('siteId'),
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
      },
    },
    {
      name: 'soId',
      bind: 'soLineLov.soId',
    },
    {
      name: 'soNumber',
      bind: 'soLineLov.soNumber',
    },
    {
      name: 'soLineId',
      bind: 'soLineLov.soLineId',
    },
    {
      name: 'soLineNum',
      bind: 'soLineLov.soLineNum',
    },
    {
      name: 'soNumContent',
      bind: 'soLineLov.soNumContent',
    },
    {
      name: 'poLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceDoc.poNumAndPoLine`).d('采购订单编号/行号'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MES.PO_LINE',
      dynamicProps: {
        disabled: ({ record, dataSet }) =>
          record.get('soId') ||
          record.get('workOrderId') ||
          !dataSet.parent?.current?.get('siteId'),
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
      },
    },
    {
      name: 'poLineId',
      bind: 'poLineLov.poLineId',
    },
    {
      name: 'poNumber',
      bind: 'poLineLov.poNumber',
    },
    {
      name: 'poLineNum',
      bind: 'poLineLov.poLineNumber',
    },
    {
      name: 'poNumberAndLine',
      bind: 'poLineLov.poNumberAndLine',
    },
    {
      name: 'inspectInfoCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceDoc.inspectInfoCode`).d('报检编码'),
    },
    {
      name: 'inspectInfoUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceDoc.inspectInfoUser`).d('报检人'),
    },
    {
      name: 'inspectInfoCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceDoc.inspectInfoCreationDate`).d('报检时间'),
    },
  ],
});

const scanFormDS: () => DataSetProps = () => ({
  autoCreate: true,
  forceValidate: true,
  fields: [
    {
      name: 'siteId',
      type: FieldType.number,
    },
    {
      name: 'materialId',
      type: FieldType.number,
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
    },
    {
      name: 'objectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectObjectType`).d('报检对象类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSPECT_SCAN_OBJECT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'scanInspectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scanInspectCode`).d('报检对象'),
      trim: FieldTrim.both,
      dynamicProps: {
        disabled: ({ record }) => !record?.get('siteId') || !record?.get('inspectBusinessType'),
      },
    },
    {
      name: 'scanInspectEoLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.scanInspectObj`).d('报检对象'),
      ignore: FieldIgnore.always,
      // lovCode: 'MT.MES.EOMATERIALLOT',
      dynamicProps: {
        lovCode: ({ dataSet }) => {
          if (dataSet.getState('docCreateMethod') === 'LES_CREATE') {
            return 'MT.WMS.EOMATERIALLOT';
          }
          return 'MT.MES.EOMATERIALLOT';
        },
        lovPara: ({ record, dataSet }) => {
          const workOrderIds: any = [];
          const containerIds: any = [];
          const instructionDocIds: any = [];
          const lineNumbers: any = [];
          const sourceDocDs = dataSet.parent?.children.inspectInfoList;
          sourceDocDs?.forEach(sourceRecord => {
            const sourceRecordData = sourceRecord.toData();
            const sourceObjectLov = sourceRecordData.sourceObjectLov || {};
            const sourceObjectType = sourceRecordData.sourceObjectType;
            if (sourceObjectType === 'WO' && sourceObjectLov?.workOrderId) {
              workOrderIds.push(sourceObjectLov?.workOrderId);
            }
            if (sourceObjectType === 'CONTAINER' && sourceObjectLov?.containerId) {
              containerIds.push(sourceObjectLov?.containerId);
            }
            if (['WO', 'CONTAINER'].indexOf(sourceObjectType) === -1) {
              if (sourceObjectLov?.instructionDocId) {
                instructionDocIds.push(sourceObjectLov?.instructionDocId);
              }
              if (sourceObjectLov?.lineNumber) {
                lineNumbers.push(sourceObjectLov?.lineNumber);
              }
            }
          });
          return {
            tenantId,
            siteId: record?.get('siteId'),
            materialId: record?.get('materialId'),
            revisionCode: record?.get('revisionCode'),
            inspectBusinessType: record?.get('inspectBusinessType'),
            objectType: record?.get('objectType'),
            workOrderIds,
            containerIds,
            instructionDocIds,
            lineNumbers,
          };
        },
        disabled: ({ record }) => !record?.get('siteId') || !record?.get('inspectBusinessType'),
      },
    },
    {
      name: 'scanInspectEoLovBatch',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.scanInspectObj`).d('报检对象'),
      ignore: FieldIgnore.always,
      // lovCode: 'MT.MES.EOMATERIALLOT',
      dynamicProps: {
        lovCode: ({ dataSet }) => {
          if (dataSet.getState('docCreateMethod') === 'LES_CREATE') {
            return 'MT.WMS.EOMATERIALLOT';
          }
          return 'MT.MES.EOMATERIALLOT';
        },
        lovPara: ({ record, dataSet }) => {
          const workOrderIds: any = [];
          const containerIds: any = [];
          const instructionDocIds: any = [];
          const lineNumbers: any = [];
          const sourceDocDs = dataSet.parent?.children.inspectInfoList;
          sourceDocDs?.forEach(sourceRecord => {
            const sourceRecordData = sourceRecord.toData();
            const sourceObjectLov = sourceRecordData.sourceObjectLov || {};
            const sourceObjectType = sourceRecordData.sourceObjectType;
            if (sourceObjectType === 'WO' && sourceObjectLov?.workOrderId) {
              workOrderIds.push(sourceObjectLov?.workOrderId);
            }
            if (sourceObjectType === 'CONTAINER' && sourceObjectLov?.containerId) {
              containerIds.push(sourceObjectLov?.containerId);
            }
            if (['WO', 'CONTAINER'].indexOf(sourceObjectType) === -1) {
              if (sourceObjectLov?.instructionDocId) {
                instructionDocIds.push(sourceObjectLov?.instructionDocId);
              }
              if (sourceObjectLov?.lineNumber) {
                lineNumbers.push(sourceObjectLov?.lineNumber);
              }
            }
          });
          return {
            tenantId,
            siteId: record?.get('siteId'),
            materialId: record?.get('materialId'),
            revisionCode: record?.get('revisionCode'),
            inspectBusinessType: record?.get('inspectBusinessType'),
            objectType: record?.get('objectType'),
            workOrderIds,
            containerIds,
            instructionDocIds,
            lineNumbers,
          };
        },
        disabled: ({ record }) => !record?.get('siteId') || !record?.get('inspectBusinessType'),
      },
      multiple: true,
    },
    {
      name: 'scanInspectMatLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.scanInspectObj`).d('报检对象'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MES.EOMATERIALLOT2',
      lovPara: { tenantId },
      dynamicProps: {
        lovCode: ({ dataSet }) => {
          if (dataSet.getState('docCreateMethod') === 'LES_CREATE') {
            return 'MT.WMS.EOMATERIALLOT2';
          }
          return 'MT.MES.EOMATERIALLOT2';
        },
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          materialId: record?.get('materialId'),
          revisionCode: record?.get('revisionCode'),
          inspectBusinessType: record?.get('inspectBusinessType'),
          objectType: record?.get('objectType'),
        }),
        disabled: ({ record }) => !record?.get('siteId') || !record?.get('inspectBusinessType'),
      },
    },
  ],
});

export { inspectDocDtlDS, sourceDocDS, scanFormDS };
