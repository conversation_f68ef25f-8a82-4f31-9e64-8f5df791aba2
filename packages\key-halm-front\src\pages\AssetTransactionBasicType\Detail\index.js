/**
 * index - 资产事务处理类型明细页
 * @date: 2020-03-05
 * @author: zong<PERSON> <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component } from 'react';
import { Bind } from 'lodash-decorators';
import { observer } from 'mobx-react';
import { DataSet, Button, Menu, Dropdown, Tooltip, Icon } from 'choerodon-ui/pro';
import { Spin, Skeleton, Tag, Col, Row } from 'choerodon-ui';
import { isUndefined, split, isArray } from 'lodash';
import queryString from 'querystring';
import { Header, Content } from 'components/Page';
import notification from 'utils/notification';
import intl from 'utils/intl';
import classNames from 'classnames';
import FileUpload from 'alm/components/FileUpload';
import ApprovalHistoryBtn from 'alm/components/ApprovalHistoryBtn';
import { statusColors } from 'alm/utils/constants';
import { setDSFields, parseFields } from 'alm/utils/dynamicFieldRender';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { getCurrentEmployee, getLovList } from 'alm/services/workOrderService';
import { queryOrgByEmployee } from 'alm/services/organizationService';
import {
  saveAssetTranLine,
  tpHeaderWithdraw,
  tpHeaderSubmit,
} from 'alm/services/assetTransacionBasicTypeService';
import { handleNonPermissionErr } from 'alm/utils/response';
import { typeDS, detailDS, detailLineTableDS, dynamicFieldsConfDS } from '../Stores/DetailDS';
import { getPageConfig } from '../assetUtils';
import { getModuleDetailTitle } from '../transactionInfoConfig';
import InfoExhibit from './InfoExhibit';
import styles from './index.less';

const organizationId = getCurrentOrganizationId();
@observer
class AssetTransactionTypesDetail extends Component {
  form;

  /**
   * state初始化
   * @param {props} props -参数
   */
  constructor(props) {
    super(props);
    const {
      match: {
        params: { id },
      },
    } = props;
    const isNew = isUndefined(id);
    this.typeDS = new DataSet(typeDS()); // 事务类型
    this.detailFormDS = new DataSet(detailDS([])); // 详情头
    // 资产事务行Table与资产事务行侧弹窗表单公用一个ds
    this.detailLineTableDS = new DataSet({ ...detailLineTableDS([]), paging: !isNew }); // 详情行
    this.dynamicHeadFieldsConfDS = new DataSet(dynamicFieldsConfDS()); // 头动态字段定义

    this.state = {
      pageConfig: {}, // 页面配置
      editFlag: false, // 是否编辑状态
      detailLoading: false, // 详情加载
      headerDynamicProps: {}, // 单据头动态字段信息
      lineDynamicProps: [], // 单据行动态字段信息
      recipientsScopeList: [], // 领用对象
      moduleSource: null,
      buttonTitle: '',
    };
    this.infoRef = React.createRef();
  }

  componentDidMount() {
    const {
      match: { url },
      location: { pathname, state = {} },
    } = this.props;
    const { isEdit = false } = state;
    let splitUrl = '/aatn/asset-transaction-basic-type/';
    if (url.indexOf('execute-handle') > -1) {
      splitUrl = '/aatn/asset-transaction-basic-type/execute-handle/';
    }
    // 通过pathname去分割出 tstype
    const tsType = split(split(pathname, splitUrl)[1], '/')[0];
    const pageConfig = getPageConfig(tsType);
    this.setState({ pageConfig, editFlag: isEdit }, async () => {
      await this.init();
      this.handleAddAssetByEqpInOut();
    });
    const path = window.location.pathname;
    if (path.includes('change')) {
      this.setState({
        buttonTitle: intl.get(`${prompt}.submit`).d('提交'),
      })
    } else {
      this.setState({
        buttonTitle: intl.get(`${prompt}.OaSubmit`).d('OA提交'),
      })
    }
  }

  /**
   * @description: 设备出入库创建报废带出设备信息
   * @param {*}
   * @return {*}
   */
  @Bind()
  async handleAddAssetByEqpInOut() {
    const {
      match: { params },
      location: { state = {} },
    } = this.props;
    const { id } = params;
    const { eqpAsset } = state;
    if (eqpAsset && this.detailLineTableDS?.data?.length === 0) {
      this.setState({
        moduleSource: 'EQP_IN_OUT',
      });
      const transactionTypeId = this.detailFormDS.current.get('transactionTypeId');
      // 出入库创建报废
      this.dynamicLineFieldsConfDS = new DataSet(dynamicFieldsConfDS()); // 资产事务行关联的动态字段
      this.dynamicLineFieldsConfDS.setQueryParameter('scopeId', transactionTypeId);
      this.dynamicLineFieldsConfDS.setQueryParameter('scopeCode', 'ASSET_TRANSACTION_LINE');
      const result = await this.dynamicLineFieldsConfDS.query();
      const { attrField = [] } = result;
      this.detailLineTableDS.loadData([
        {
          lineNum: 1,
          lineId: 'EQP',
          objectId: eqpAsset.assetId,
          objectNum: eqpAsset.assetNum,
          objectDesc: eqpAsset.assetDesc || eqpAsset.assetName,
          objectType: 'ASSET',
          requisitionsNumber: 1,
          transactionTypeId,
          changeHeaderId: id,
          tenantId: organizationId,
          processStatus: 'DRAFT',
          processStatusMeaning: '拟定',
          attrField,
        },
      ]);
    }
  }

  /**
   * 初始化
   */
  @Bind()
  async init() {
    const {
      match: { params },
    } = this.props;
    const { id } = params;
    if (!isUndefined(id)) {
      await this.handleSearch();
    } else {
      this.setState({ editFlag: true });
      this.getTypeData();
      this.handInitCreatedData();
      const res = await this.handleSearchHeadFieldsConf();
      this.handleSetExtraFields(res);
    }
    // 用于控制根据资产事务类型行编辑表单字段名显示
    const { basicCode } = this.state?.pageConfig;
    this.detailFormDS.setState('basicCode', basicCode);
    this.detailLineTableDS.setState('basicCode', basicCode);
  }

  handInitCreatedData() {
    const { location = {} } = this.props;
    const { state = {} } = location;
    const { transaction = {}, assets = [], moduleSource, assetConfig } = state;
    this.detailFormDS.current.set('codeRule', transaction.codeRule);
    this.detailFormDS.current.set('codeRuleName', transaction.codeRuleName);
    this.detailFormDS.current.set('disposalMethodsScopeList', transaction.disposalMethodsScopeList);
    this.detailFormDS.current.set('currencyCode', 'CNY');
    this.detailFormDS.current.set('currencyCodeMeaning', '人民币');
    // 带出负责人、负责组织、公司
    getCurrentEmployee({ tenantId: organizationId }).then(res => {
      this.detailFormDS?.current?.set({
        principalPersonId: res?.employeeId,
        principalPersonIdMeaning: res?.employeeName,
      });
      getLovList({lovCode: 'APEX.DEFAULE_EMPLOYEE_UNIT',tenantId: organizationId, employeeId: res?.employeeId}).then(res => {
        this.detailFormDS?.current?.set({
          unitCompanyName: res?.content[0].unitCompanyName,
          unitCompanyId: res?.content[0].unitCompanyId,
        });
      })
      queryOrgByEmployee({ tenantId: organizationId, employeeId: res?.employeeId }).then(
        orgInfo => {
          this.detailFormDS?.current?.set({
            usingOrgId: orgInfo?.unitId,
            usingOrgName: orgInfo?.unitName,
            usingOrgType: orgInfo?.orgType,
          });
        }
      );
    });
    // 如果路由参数有assets就带出资产行
    const extraConfig = {
      moduleSource,
      assetConfig,
    };
    this.infoRef.current.handleCreateAsset(
      this.detailLineTableDS,
      assets,
      transaction?.transactionTypeId,
      extraConfig
    );
  }

  @Bind()
  async getTypeData() {
    const {
      location: {
        state: { transaction = {} },
      },
    } = this.props;
    this.typeDS.setQueryParameter('id', transaction.transactionTypeId);
    const typeData = await this.typeDS.query();
    this.setState({
      recipientsScopeList: typeData.recipientsScopeList || [],
    });
  }

  @Bind()
  async handleSearch() {
    this.setState({ detailLoading: true });
    const {
      match: { params },
    } = this.props;
    const { id } = params;
    this.detailFormDS.setQueryParameter('changeHeaderId', id);
    this.detailLineTableDS.setState('changeHeaderId', id);
    try {
      const res = await this.detailFormDS.query();
      // 提前获取类型是的领用对象控制行按钮的显示
      this.typeDS.setQueryParameter('id', res.transactionTypeId);
      const typeData = await this.typeDS.query();
      this.setState({
        recipientsScopeList: typeData.recipientsScopeList || [],
      });
      await this.detailLineTableDS.query();
      if (res) {
        const { attrField = [] } = res;
        this.handleSetExtraFields(attrField);
        this.detailLineTableDS.setState('typeData', typeData);
      }
    } catch (error) {
      this.detailFormDS.loadData([{}]);
      console.log(error);
    }
    this.setState({ detailLoading: false });
  }

  /**
   * 查询单据头动态字段信息
   * @returns res [] 字段定义
   */
  async handleSearchHeadFieldsConf() {
    const {
      match: { params },
      location: { state = {}, search },
    } = this.props;
    const { id } = params;
    const { transaction = {}, moduleSource, taskId } = state;
    const searchItem = queryString.parse(search.substring(1)) || {};
    // 来自
    if (moduleSource === 'countingLine' && isUndefined(id)) {
      this.dynamicHeadFieldsConfDS.setQueryParameter('taskId', taskId);
    }
    this.dynamicHeadFieldsConfDS.setQueryParameter(
      'scopeId',
      transaction.transactionTypeId || searchItem.transactionTypeId
    );
    this.dynamicHeadFieldsConfDS.setQueryParameter('scopeCode', 'ASSET_TRANSACTION_HEAD');
    const res = await this.dynamicHeadFieldsConfDS.query();
    const { attrField = [] } = res;
    return attrField;
  }

  @Bind()
  handleOrgPartnerLovOk(name, meaning, record) {
    this.detailFormDS.current.set(name, record.value);
    this.detailFormDS.current.set(meaning, record.meaning);
  }

  /**
   * 设置附加字段面板的弹性字段
   * @param data
   */
  @Bind()
  handleSetExtraFields(data = []) {
    const {
      match: { params },
      location: { state = {} },
    } = this.props;
    const { id } = params;
    const isNew = isUndefined(id);
    const { transaction = {}, defaultValues = {} } = state;
    const dynamicFields = setDSFields(
      data,
      'fieldName',
      'aliasFieldCode',
      'typeCode',
      isNew ? 'defaultValue' : 'value',
      '',
      'form',
      true,
      false, // 不显示原值
      {
        handleOrgPartnerLovOk: this.handleOrgPartnerLovOk,
      }
    );
    const {
      dsFields,
      formDisplayRender,
      outputDisplayRender,
      fieldData,
      resultData,
    } = dynamicFields;
    const formData = this.detailFormDS.current.toData();
    this.detailFormDS = new DataSet(detailDS(dsFields));
    const initData = {
      transactionTypeId: transaction.transactionTypeId,
      transactionTypeIdMeaning: transaction.transactionTypeName,
    };
    this.detailFormDS.loadData([
      {
        ...initData,
        ...defaultValues,
        ...formData,
        ...resultData,
      },
    ]);
    const headerDynamicProps = {
      formDisplayRender,
      outputDisplayRender,
      fieldData,
      dsFields,
    };
    this.setState({ headerDynamicProps, detailLoading: false });
  }

  /**
   * 处理事务类型
   */
  @Bind()
  async handleTransactionChange() {
    const transactionTypeLov = this.detailFormDS.current.get('transactionTypeIdLov');
    this.getTemplateData(transactionTypeLov.transactionTypeId, transactionTypeLov);
  }

  /**
   * 处理编辑按钮
   */
  @Bind()
  handleEdit() {
    const { editFlag } = this.state;
    this.setState({ editFlag: !editFlag });
    if (editFlag) {
      this.detailFormDS.current.reset(); // 关闭则清除编辑
      this.detailLineTableDS.reset();
    }
  }

  @Bind()
  async handleSaveAssetByEqpInOut() {
    const { moduleSource } = this.state;
    if (moduleSource === 'EQP_IN_OUT') {
      let tmpRec = null;
      this.detailLineTableDS.records.forEach(record => {
        if (record?.get('lineId') === 'EQP') {
          tmpRec = record;
        }
      });
      const tpChangeLines = {
        tenantId: organizationId,
        data: [{ ...tmpRec.toJSONData(), lineId: null }],
      };
      const headData = this.detailFormDS?.current.toData() ?? {};
      const params = { ...headData, tpChangeLines };
      const res = await saveAssetTranLine(params);
      const lineId = isArray(res) ? res[0]?.lineId : null;
      const arr = this.detailLineTableDS.lineDynamicProps;
      for (let index = 0; index < arr.length; index++) {
        if (arr[index]?.lineId === 'EQP') {
          arr[index].lineId = lineId;
          break;
        }
      }
      tmpRec.set('lineId', lineId);
      return res;
    }
  }

  /**
   * 保存
   */
  @Bind()
  async handleSave() {
    const {
      history,
      match: { params },
      location: { state = {} },
    } = this.props;
    const { id } = params;
    const { asset } = state;
    const { pageConfig, headerDynamicProps } = this.state;
    // 判断是否有修改
    const flag = await this.detailFormDS.validate();
    if (flag) {
      const illegalArray = await this.lineTableValidate();
      if (illegalArray.length === 0) {
        // 出入库带出的设备需要手动先保存
        const saveLineRes = await this.handleSaveAssetByEqpInOut();
        getResponse(saveLineRes);
        if (saveLineRes?.failed) {
          return;
        }
        this.detailFormDS.setState('changeHeaderId', id);
        // if (!isUndefined(id)) {
        //   this.detailFormDS.tpChangeLines = this.detailLineTableDS.toData();
        //   this.detailFormDS.tpChangeLinesTemplateData = this.detailLineTableDS.templateData;
        // }
        // 保存单据头动态字段
        const extraFieldsData = parseFields(
          headerDynamicProps.fieldData,
          this.detailFormDS.current.toData()
        );
        this.detailFormDS.current.set('attrField', extraFieldsData);

        // 行数据处理
        const tpChangeLines = this.detailLineTableDS.toData().map(i => {
          if (this.detailLineTableDS.lineDynamicProps.length > 0) {
            const currentDynamicProps = this.detailLineTableDS.lineDynamicProps.find(
              l => l.objectId === i.objectId
            );
            const { dynamicFields } = currentDynamicProps;
            const lineExtraFieldsData = parseFields(dynamicFields.fieldData, i);
            return { ...i, attrField: lineExtraFieldsData };
          } else {
            return i;
          }
        });

        // 处理目标资产状态
        const newLines = this.handleLineNewAssetStatus(tpChangeLines);

        this.detailFormDS.current.set('tpChangeLines', newLines);
        const res = await this.detailFormDS.submit();

        if (res && res.success) {
          if (isUndefined(id)) {
            const { transactionTypeId } = res.content[0];
            const newId = res.content[0].changeHeaderId;
            history.push({
              pathname: `/aatn/asset-transaction-basic-type/${pageConfig.routeType}/detail/${transactionTypeId}/${newId}`,
              state: {
                eqpAsset: asset, // 出入库新建保存后展示资产
              },
            });
          } else {
            this.setState({ editFlag: false });
            await this.handleSearch();
            this.detailLineTableDS.query();
          }
        }
      } else {
        const message = `存在第${illegalArray.join(
          '、'
        )}行事务行信息未填写完整，请完善行信息再保存！`;
        notification.warning({ message });
      }
    }
  }

  /**
   * 为了处理新建行后,没有点击编辑,导致保存行时不存在目标资产状态
   * @param {*} lines
   */
  @Bind()
  handleLineNewAssetStatus(lines) {
    let newLines = lines;
    const typeDetail = this.typeDS.current ? this.typeDS.current.toData() : {};
    const { statusUpdateFlag, targetAssetStatusId, targetAssetStatusName } = typeDetail;
    // 目标资产状态是否可编辑
    if (statusUpdateFlag) {
      if (!isUndefined(targetAssetStatusId)) {
        newLines = lines.map(record => {
          if (isUndefined(record.newObjectStatusId)) {
            return {
              ...record,
              newObjectStatusId: targetAssetStatusId,
              newAssetStatusName: targetAssetStatusName,
            };
          }
          return record;
        });
      }
    }
    return newLines;
  }

  /**
   * 校验行数据是否都填写完整
   * @param {*} status
   * @returns boolean 是否填写完整
   */
  @Bind()
  async lineTableValidate() {
    const { records } = this.detailLineTableDS;
    const illegalArray = []; // 判断是否有校验不通过的行数据
    await Promise.all(
      records.map(async (record, index) => {
        const recordValidateFlag = await record.validate(true);
        if (!recordValidateFlag) {
          illegalArray.push(index + 1);
        }
        return record;
      })
    );
    return illegalArray;
  }

  /**
   * 状态提交
   * @param {*} id
   */
  @Bind()
  async handleStatus(status) {
    this.setState({ detailLoading: true });
    try {
      const flag = await this.detailFormDS.validate();
      if (flag || status === 'DRAFT') {
        this.detailFormDS.current.set('processStatus', status);
        this.detailFormDS.current.set('tpChangeLines', this.detailLineTableDS.toData());
        if (status === 'DRAFT') {
          await this.detailFormDS.forceSubmit();
        } else {
          await this.detailFormDS.submit();
        }
        this.handleSearch();
      } else {
        notification.error();
      }
    } catch (e) {
      notification.error();
    } finally {
      this.setState({ detailLoading: false });
    }
  }

  @Bind()
  async handleSubmit() {
    try {
      this.setState({ detailLoading: true });
      const res = await tpHeaderSubmit({ ...this.detailFormDS.current.toData(), businessType: 'SCRAP' });
      handleNonPermissionErr(res);
      if (res) {
        this.handleSearch();
      }
      this.setState({ detailLoading: false });
    } catch (error) {
      this.setState({ detailLoading: false });
    }
  }

  @Bind()
  async handleWithdraw() {
    try {
      this.setState({ detailLoading: true });
      const detail = this.detailFormDS.current.toData();
      if (detail.processStatus === 'APPROVING') {
        const res = await tpHeaderWithdraw(detail);
        handleNonPermissionErr(res);
        if (res) {
          this.handleSearch();
        }
        this.setState({ detailLoading: false });
      } else {
        // 不开启工作流的撤回
        this.handleStatus('DRAFT');
      }
    } catch (error) {
      this.setState({ detailLoading: false });
    }
  }

  /**
   * 左侧表格内容点击
   * @param {string} id - id
   */
  @Bind()
  handleGotoDetail(changeHeaderId, tsTypeId) {
    const { history } = this.props;
    const { pageConfig } = this.state;
    const path = `/aatn/asset-transaction-basic-type/${pageConfig.routeType}/detail/${tsTypeId}/${changeHeaderId}`;
    history.push(path);
    this.componentDidMount();
  }

  /**
   * 表格列
   */
  get columns() {
    const {
      match: { params },
    } = this.props;
    const { id } = params;
    const isNew = isUndefined(id); // 当前是否是新增
    return [
      { name: 'sequenceNum', width: 100, editor: true },
      { name: 'dynamicFieldLov', width: 160, editor: true },
      { name: 'fieldTypeCode', width: 160, editor: true },
      { name: 'fieldName', width: 160, editor: true },
      { name: 'attrFieldLov', width: 160, editor: true },
      { name: 'operatorTypeCode', width: 160, editor: true },
      {
        header: intl.get('hzero.common.button.action').d('操作'),
        width: 100,
        align: 'center',
        command: () => {
          return isNew ? ['delete'] : ['edit', 'delete'];
        },
      },
    ];
  }

  render() {
    const prompt = 'aatn.assetTransactionBasicType';
    const {
      editFlag,
      pageConfig,
      detailLoading,
      headerDynamicProps,
      lineDynamicProps,
      recipientsScopeList,
    } = this.state;
    const { basicCode, routeType } = pageConfig;
    const {
      match: { url, params },
      history,
    } = this.props;
    const { tsTypeId, id } = params;
    const isNew = isUndefined(id); // 当前是否是新增

    const currentTranscation = this.detailFormDS.current.toData();
    const {
      changeNum,
      titleOverview,
      transactionTypeIdMeaning,
      processStatus,
      processStatusMeaning,
      workflowFlag,
      wkInstanceId,
    } = currentTranscation;

    const infoProps = {
      // 右侧信息属性
      ref: this.infoRef,
      isNew,
      editFlag,
      pageConfig,
      processStatus,
      headerDynamicProps,
      lineDynamicProps,
      organizationId,
      basicCode,
      recipientsScopeList,
      typeDS: this.typeDS,
      infoDS: this.detailFormDS,
      infoTableDS: this.detailLineTableDS,
      changeHeaderId: id,
      transactionTypeId: tsTypeId,
      onRefresh: this.handleSearch,
    };

    const backUrl =
      url.indexOf('execute-handle') > -1
        ? `/aatn/asset-transaction-basic-type/execute-handle/${routeType}/list`
        : `/aatn/asset-transaction-basic-type/${routeType}/list`;

    const assetFileUploadAttribute = {
      data: [
        {
          name: intl.get(`${prompt}.model.assetTransactionBasicType.sourceType`).d('来源类型'),
          code: 'sourceType',
          value: '资产事务处理单',
        },
        {
          name: intl.get(`${prompt}.model.assetTransactionBasicType.sourceNumber`).d('来源单据号'),
          code: 'sourceNumber',
          value: id,
          pathname: `/aatn/asset-transaction-basic-type/${routeType}/detail/${tsTypeId}/${id}`,
        },
      ],
    };

    const displaySubmitFlag =
      isNew || editFlag
        ? { display: 'none' }
        : ['REJECTED', 'DRAFT'].includes(processStatus)
        ? { display: 'block' }
        : { display: 'none' };
    const displayDropFlag =
      isNew || editFlag
        ? { display: 'none' }
        : ['REJECTED', 'DRAFT'].includes(processStatus)
        ? { display: 'block', marginLeft: 0, width: 30, padding: 0 }
        : { display: 'none' };
    const displayEditFlag =
      isNew || editFlag || !['REJECTED', 'DRAFT'].includes(processStatus)
        ? { display: 'none' }
        : { display: 'block' };
    const displayCloseBtn = isNew || !editFlag ? { display: 'none' } : { display: 'block' };
    const displayFlagBtn = isNew || editFlag ? { display: 'block' } : { display: 'none' };
    const displayReturnFlag = ['PENDING', 'APPROVING'].includes(processStatus)
      ? { display: 'block' }
      : { display: 'none' };

    const displayApprovalHistoryBtn =
      workflowFlag && wkInstanceId && !isNew && !editFlag
        ? { display: 'block' }
        : { display: 'none' };

    const approvalHistoryProps = {
      wkInstanceId,
      history,
    };
    const menu = (
      <Menu>
        <Menu.Item>
          <span onClick={() => this.handleStatus('CANCEL')}>
            {' '}
            {intl.get('hzero.common.button.cancel').d('取消')}{' '}
          </span>
        </Menu.Item>
      </Menu>
    );
    return (
      <React.Fragment>
        <Header title={getModuleDetailTitle(basicCode)} backPath={backUrl}>
          <ApprovalHistoryBtn style={displayApprovalHistoryBtn} {...approvalHistoryProps} />
          <Dropdown overlay={menu}>
            <Button style={displayDropFlag} className={classNames(styles['drop-btn'])}>
              <Icon type="arrow_drop_down" style={{ fontSize: 14 }} />
            </Button>
          </Dropdown>
          <Button
            icon="submit"
            color="primary"
            style={displaySubmitFlag}
            loading={detailLoading}
            onClick={this.handleSubmit}
          >
            { this.state.buttonTitle }
          </Button>
          <Button
            color="primary"
            icon="save"
            style={displayFlagBtn}
            loading={detailLoading}
            onClick={this.handleSave}
          >
            {intl.get(`hzero.common.button.save`).d('保存')}
          </Button>
          <Button icon="edit" color="primary" style={displayEditFlag} onClick={this.handleEdit}>
            {intl.get('hzero.common.button.edit').d('编辑')}
          </Button>
          <Button icon="close" style={displayCloseBtn} onClick={this.handleEdit}>
            {intl.get('hzero.common.button.close').d('取消')}
          </Button>
          {/* <Button
            icon="submit"
            color="red"
            style={displayReturnFlag}
            loading={detailLoading}
            onClick={this.handleWithdraw}
          >
            {intl.get(`${prompt}.button.recall`).d('撤回')}
          </Button> */}
          {!isUndefined(id) && (
            <FileUpload
              uploadButtonName={{ code: 'asset-attachment-management', name: '' }}
              moduleName="aatn-asset-change-header"
              moduleId={id}
              showDeleteFlag={processStatus === 'DRAFT'}
              attribute={assetFileUploadAttribute}
              // dispatch={dispatch} // 用于做页面跳转
            />
          )}
        </Header>

        {!isNew && (
          <>
            <div className={styles['header-divide']} />
            <div className={styles['right-top']}>
              <Skeleton active loading={detailLoading}>
                <div className={styles['right-top-header']}>
                  <div className={styles['right-top-title']}>
                    <div className={styles['right-top-name']}>
                      <Tooltip title={titleOverview}>{titleOverview}</Tooltip>
                    </div>
                    <div className={styles['right-top-status']}>
                      {
                        <Tag
                          style={{
                            color:
                              (statusColors[processStatus] &&
                                statusColors[processStatus].fontColor) ||
                              '#000',
                            border: 0,
                          }}
                          color={
                            (statusColors[processStatus] && statusColors[processStatus].bgColor) ||
                            '#fff'
                          }
                        >
                          {processStatusMeaning}
                        </Tag>
                      }
                    </div>
                  </div>
                  <div className={styles['right-top-number']}>{changeNum}</div>
                </div>
                <div className={styles['right-top-bottom']}>
                  <Col span={6}>
                    <Row>
                      <Col span={10} style={{ textAlign: 'right' }}>
                        <span className={styles['text-over-flow']}>
                          {intl
                            .get(`${prompt}.model.assetTransactionBasicType.changeNum`)
                            .d('事务处理单编号')}
                          ：
                        </span>
                      </Col>
                      <Col span={14}>
                        <span className={styles['text-over-flow']} style={{ fontWeight: 'bold' }}>
                          {changeNum}
                        </span>
                      </Col>
                    </Row>
                  </Col>
                  <Col span={6}>
                    <Row>
                      <Col span={10} style={{ textAlign: 'right' }}>
                        <span className={styles['text-over-flow']}>
                          {intl
                            .get(`${prompt}.model.assetTransactionBasicType.titleOverview`)
                            .d('标题概述')}
                          ：
                        </span>
                      </Col>
                      <Col span={14}>
                        <span className={styles['text-over-flow']} style={{ fontWeight: 'bold' }}>
                          {titleOverview}
                        </span>
                      </Col>
                    </Row>
                  </Col>
                  <Col span={6}>
                    <Row>
                      <Col span={10} style={{ textAlign: 'right' }}>
                        <span className={styles['text-over-flow']}>
                          {intl
                            .get(`${prompt}.model.assetTransactionBasicType.transactionType`)
                            .d('资产事务类型')}
                          ：
                        </span>
                      </Col>
                      <Col span={14}>
                        <span className={styles['text-over-flow']} style={{ fontWeight: 'bold' }}>
                          {transactionTypeIdMeaning}
                        </span>
                      </Col>
                    </Row>
                  </Col>
                  <Col span={6}>
                    <Row>
                      <Col span={10} style={{ textAlign: 'right' }}>
                        <span className={styles['text-over-flow']}>
                          {intl
                            .get(`${prompt}.model.assetTransactionBasicType.processStatus`)
                            .d('处理状态')}
                          ：
                        </span>
                      </Col>
                      <Col span={14}>
                        <span className={styles['text-over-flow']} style={{ fontWeight: 'bold' }}>
                          {processStatusMeaning}
                        </span>
                      </Col>
                    </Row>
                  </Col>
                </div>
              </Skeleton>
            </div>
          </>
        )}

        <Content>
          <Spin spinning={isUndefined(id) ? false : detailLoading}>
            <InfoExhibit {...infoProps} />
          </Spin>
        </Content>
      </React.Fragment>
    );
  }
}
export default AssetTransactionTypesDetail;
