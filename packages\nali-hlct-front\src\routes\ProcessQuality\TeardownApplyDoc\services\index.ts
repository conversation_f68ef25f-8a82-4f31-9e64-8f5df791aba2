/**
 * @Description: 拆解申请单-service
 * @Author: <EMAIL>
 * @Date: 2023/8/14 15:03
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const endurl = '';

/**
 * 撤销拆解申请单
 * @function CancelTeardownApplyDoc
 * @returns {object} fetch Promise
 */
export function CancelTeardownApplyDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endurl}/v1/${tenantId}/qis-teardown-apply/cancel/ui`,
    method: 'POST',
  };
}

/**
 * 根据materialLotId获取电芯条码相关信息
 * @function QueryMaterialLotInfo
 * @returns {object} fetch Promise
 */
export function QueryMaterialLotInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endurl}/v1/${tenantId}/qis-teardown-apply/baseInfo/ui`,
    method: 'GET',
  };
}

/**
 * 保存拆解申请单
 * @function SaveTeardownApplyDoc
 * @returns {object} fetch Promise
 */
export function SaveTeardownApplyDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endurl}/v1/${tenantId}/qis-teardown-apply/save/ui`,
    method: 'POST',
  };
}

/**
 * 更新拆解申请单
 * @function UpdateTeardownApplyDoc
 * @returns {object} fetch Promise
 */
export function UpdateTeardownApplyDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endurl}/v1/${tenantId}/qis-teardown-apply/update/ui`,
    method: 'POST',
  };
}

/**
 * 提交拆解申请单
 * @function SubmitTeardownApplyDoc
 * @returns {object} fetch Promise
 */
export function SubmitTeardownApplyDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endurl}/v1/${tenantId}/qis-teardown-apply/submit/ui`,
    method: 'POST',
  };
}
