/**
 * @Description: 行拖拽
 * @Author: <<EMAIL>>
 * @Date: 2021-09-14 15:41:22
 * @LastEditTime: 2021-09-23 14:25:24
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect } from 'react';
import DragContainer from './dragContainer';

const DragComponents = ({ list, handleChildListChange, questionTuid, canEdit }) => {
  useEffect(() => {
    setPreviewList([...list]);
  }, [list]);

  const [previewList, setPreviewList] = useState(list);
  const handlePreviewList = _list => {
    setPreviewList([..._list]);
    handleChildListChange(questionTuid, [..._list]);
  };

  const handlePreviewListClick = id => {
    const _list = [];
    previewList.forEach(item => {
      const _item = { ...item };
      if (id === item.questionTuid) {
        _item.value = item.value === 'ASC' ? 'DESC' : 'ASC';
      }
      _list.push(_item);
    });
    setPreviewList([..._list]);
    handleChildListChange(questionTuid, [..._list]);
  };

  return (
    <DragContainer
      handlePreviewList={handlePreviewList}
      previewList={previewList}
      handlePreviewListClick={handlePreviewListClick}
      canEdit={canEdit}
    />
  );
};

export default DragComponents;
