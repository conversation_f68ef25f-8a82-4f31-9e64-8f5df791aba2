/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-12-14
 * @description 执行执行规则维护-详情页头DS
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { DataSet } from 'choerodon-ui/pro';
import { instructionCreateModeOptionDs, instructionDocExeStrategyOptionDs } from './listDs';

const modelPrompt = 'tarzan.commonConfig.orderExecuteRule';
const tenantId = getCurrentOrganizationId();

const onPassageLocatorTypeOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'LOCATOR_TYPE', tenantId },
      };
    },
  },
});

const accountCategoryOptions = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/limit-group-list/combo-box/ui`,
        method: 'GET',
        params: { typeGroupList: ["ACCOUNT_CATEGORY_C","ACCOUNT_CATEGORY_O"].toString(), tenantId },
      };
    },
  },
});

const onPassageLocatorDirectionOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'LOCATOR_DIRECTION', tenantId },
      };
    },
  },
});

const detailDS = () => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  autoCreate: true,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'businessObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('指令单据类型'),
      lovCode: 'APEX_WMS.INSTRUCTION_DOC_TYPE',
      required: true,
      textField: 'value',
      ignore: 'always',
      lovPara: {
        tenantId,
        typeGroup: 'INSTRUCTION_DOC_TYPE',
      },
    },
    {
      name: 'instructionDocType',
      bind: 'businessObj.value',
    },
    {
      name: 'description',
      label: intl.get(`${modelPrompt}.assembleGroupDesc`).d('单据类型描述'),
      bind: 'businessObj.meaning',
    },
    {
      name: 'typeDescription',
      label: intl.get(`${modelPrompt}.assembleGroupDesc`).d('单据类型描述'),
      bind: 'businessObj.meaning',
    },
    // {
    //   name: 'typeDescription',
    //   bind: 'description',
    // },
    {
      name: 'statusGroupObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.statusGroup`).d('单据状态值集'),
      lovCode: 'APEX_WMS.INSTRUCTION_DOC_STATUS',
      lovPara: {
        tenantId,
        defaultGroupCodePre: 'INSTRUCTION_DOC_STATUS',
      },
      textField: 'inputGroupCodePre',
      valueField: 'statusGroup',
    },
    {
      name: 'statusGroup',
      bind: 'statusGroupObj.statusGroup',
    },
    {
      name: 'instructionDocExeStrategy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocExeStrategy`).d('单据执行策略'),
      options: instructionDocExeStrategyOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      dynamicProps: {
        options: ({ record }) => {
          if (record.get('instructionDocType') === 'NO_INSTRUCTION_DOC') {
            return new DataSet({
              data: [
                {
                  typeCode: 'No_Exe_Strategy',
                  description: intl.get(`${modelPrompt}.NoExeStrategy`).d('无执行策略'),
                },
              ],
            });
          }
          return instructionDocExeStrategyOptionDs;

        },
        disabled: ({ record }) => {
          return record.get('instructionDocType') === 'NO_INSTRUCTION_DOC';
        },
      },
    },
    {
      name: 'instructionCreateMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionCreateMode`).d('指令创建模式'),
      options: instructionCreateModeOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('instructionDocExeStrategy') !== 'TWO_STEP';
        },
        required: ({ record }) => {
          return record.get('instructionDocExeStrategy') === 'TWO_STEP';
        },
      },
    },
    // {
    //   name: 'onPassageLocatorType',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.onPassageLocatorType`).d('在途库位类型'),
    //   options: onPassageLocatorTypeOptionDs,
    //   textField: 'description',
    //   valueField: 'typeCode',
    //   dynamicProps: {
    //     disabled: ({ record }) => {
    //       return record.get('instructionDocExeStrategy') !== 'TWO_STEP';
    //     },
    //     // required: ({ record }) => {
    //     //   return record.get('instructionDocExeStrategy') === 'TWO_STEP';
    //     // },
    //   },
    // },
    // {
    //   name: 'onPassageLocatorDirection',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.onPassageLocatorDirection`).d('在途库位方向'),
    //   options: onPassageLocatorDirectionOptionDs,
    //   textField: 'description',
    //   valueField: 'typeCode',
    //   dynamicProps: {
    //     disabled: ({ record }) => {
    //       return record.get('instructionDocExeStrategy') !== 'TWO_STEP';
    //     },
    //     required: ({ record }) => {
    //       return record.get('instructionDocExeStrategy') === 'TWO_STEP';
    //     },
    //   },
    // },
    {
      name: 'fromLocatorRequiredFlag',
      label: intl.get(`${modelPrompt}.fromLocatorRequired`).d('来源库位必输'),
      type: FieldType.string,
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'toLocatorRequiredFlag',
      label: intl.get(`${modelPrompt}.toLocatorRequired`).d('目标库位必输'),
      type: FieldType.string,
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'initialFlag',
      label: intl.get(`${modelPrompt}.initialFlag`).d('初始化'),
      type: FieldType.string,
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'accountCategoryObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.accountCategory`).d('结算类别'),
      options: accountCategoryOptions,
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'accountCategory',
      bind: 'accountCategoryObj.typeCode',
    },
    {
      name: 'accountCategoryDesc',
      bind: 'accountCategoryObj.description',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-instruction-doc-exe-rules/query/ui`,
        method: 'post',
        transformResponse: data => {
          const { rows } = JSON.parse(data);
          return rows?.content;
        },
      };
    },
  },
});

export { detailDS, onPassageLocatorTypeOptionDs, onPassageLocatorDirectionOptionDs };
