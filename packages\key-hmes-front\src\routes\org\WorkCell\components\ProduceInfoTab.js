/**
 * @Description: 站点维护-生产属性Tab
 * @Author: <<EMAIL>>
 * @Date: 2021-02-04 11:54:54
 * @LastEditTime: 2021-02-04 12:00:15
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, NumberField } from 'choerodon-ui/pro';

const ProduceInfoTab = props => {
  const { ds, canEdit, columns = 1 } = props;
  return (
    <Form
      disabled={!canEdit}
      dataSet={ds}
      columns={columns}
      labelLayout="horizontal"
      labelWidth={112}
    >
      <NumberField name="backwardShiftNumber" />
      <NumberField name="forwardShiftNumber" />
    </Form>
  );
};

export default ProduceInfoTab;
