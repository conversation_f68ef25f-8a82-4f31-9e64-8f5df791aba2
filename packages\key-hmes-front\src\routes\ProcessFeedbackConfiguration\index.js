import React, { useState, useMemo, useEffect } from 'react';
import { DataSet, Table, Button, Lov, Select, Spin } from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import request from 'utils/request';
import moment from 'moment';
import intl from 'utils/intl';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';

import { tableDS, assObjectsDS } from './stores/ProcessFeedbackConfigurationDS';
import LineList from './LineList';
import { Host } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.ProcessFeedbackConfiguration';
// const Host = `/key-ne-focus-mes-38510`;

const tenantId = getCurrentOrganizationId();
const realName = getCurrentUser().realName;
const lastUpdateDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');

const ProcessFeedbackConfiguration = () => {
  const [loading, setLoading] = useState(false);
  const [disabledFlag, setDisabledFlag] = useState(false);
  const [selectedRow, setSelectedRow] = useState([]);

  const assObjectsDs = useMemo(() => new DataSet(assObjectsDS()), []);
  const tableDs = useMemo(
    () =>
      new DataSet({
        ...tableDS(),
        events: {
          load: () => {
            assObjectsDs.loadData([]);
          },
        },
      }),
    [],
  );

  useEffect(() => {
    tableDs.query();
    // queryBasicData();
    tableDs.addEventListener('select', handleDataSetSelect);
    tableDs.addEventListener('unSelect', handleDataSetSelect);
  }, []);

  const handleDataSetSelect = () => {
    setSelectedRow(tableDs.selected);
    const selectedLength = tableDs.selected;
    if (selectedLength.length > 0) {
      const createlist = tableDs.toJSONData().map(item => item._status === 'create');
      if (createlist.includes(true)) {
        assObjectsDs.loadData([]);
        tableDs.unSelectAll();
        // setCreatFlag(true);
        notification.error({ message: intl
          .get(`${modelPrompt}.error.save.head`)
          .d(`请先保存头`) });
      } else {
        assObjectsDs.setQueryParameter(
          'operationTagConfigId',
          selectedLength[0].data.operationTagConfigId,
        );
        assObjectsDs.query();
      }
    } else {
      setSelectedRow([]);
      assObjectsDs.loadData([]);
      setDisabledFlag(false);
    }
  };

  // 保存
  const handelSave = async () => {
    await tableDs.validate().then(valiResult => {
      assObjectsDs.validate().then(valiResult2 => {
        if (valiResult && valiResult2) {
          const tableList = tableDs.toJSONData();
          const assObjectsData = assObjectsDs.toJSONData();
          let listData = [];
          let ids = [];
          if (tableList.length || assObjectsData.length) {
            if (tableList.length > 0) {
              ids = tableList.map(item => item.operationTagConfigId);
              listData = tableList.map(item => {
                return {
                  ...item,
                  tagConfigLineList: [],
                };
              });
            }
            if (assObjectsData.length > 0) {
              if (tableList.length > 0) {
                if (ids.length && ids.includes(selectedRow[0].data.operationTagConfigId)) {
                  listData = tableList.map(item => {
                    return {
                      ...item,
                      tagConfigLineList:
                        item.operationTagConfigId === selectedRow[0].data.operationTagConfigId
                          ? assObjectsData
                          : [],
                    };
                  });
                } else {
                  tableList.push({ ...selectedRow[0].data, tagConfigLineList: assObjectsData });
                  listData = tableList.map(item => {
                    return {
                      ...item,
                    };
                  });
                }
              } else {
                listData = [
                  {
                    ...selectedRow[0].data,
                    tagConfigLineList: assObjectsData,
                  },
                ];
              }
            }
            setLoading(true);
            request(`${Host}/v1/${tenantId}/hme-operation-tag-configs/save/ui`, {
              method: 'post',
              body: listData,
            }).then(res => {
              setLoading(false);
              if (res && !res.failed) {
                notification.success();
                setDisabledFlag(false);
                setSelectedRow([]);
                tableDs.query();
                assObjectsDs.loadData([]);
              } else {
                notification.error({ message: res.message });
              }
            });
          } else {
            return notification.warning({
              message: intl
                .get(`${modelPrompt}.error.createEdit.data`)
                .d(`请先新建或修改数据`),
              placement: 'bottomRight',
            });
          }
        } else {
          return notification.warning({
            message: intl
              .get(`${modelPrompt}.error.required`)
              .d(`请填写必填项`),
            placement: 'bottomRight',
          });
        }
      });
    });
  };

  // 编辑按钮
  const handelEdit = () => {
    tableDs.records.forEach(item => {
      item.setState('editing', true);
    });
    setDisabledFlag(true);
  };

  // 取消按钮
  const handelCancel = () => {
    tableDs.records.forEach(item => {
      item.setState('editing', false);
    });
    tableDs.query();
    setDisabledFlag(false);
  };

  // lov变化事件
  const changeObject = (lovRecords, record) => {
    if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
      record.getField('revisionCode').set('required', true);
      record.set('revisionFlag', 'Y');
      record.set('revisionCode', lovRecords.revisionCode);
    } else {
      record.set('revisionCode', '');
      record.set('revisionFlag', 'N');
      record.getField('revisionCode').set('required', false);
    }
  };

  // lov变化事件
  const changeSite = record => {
    record.set('materialObj', '');
    record.set('materialName', '');
    record.set('revisionCode', '');
    record.set('prodLineObj', '');
    record.set('prodLineName', '');
  };

  // 版本下拉框变化事件
  const changeVersion = record => {
    if (record.data.revisionFlag === 'Y') {
      record.getField('revisionCode').set('required', true);
    }
  };

  const handleCreate = () => {
    if (tableDs.selected.length > 0) {
      assObjectsDs.loadData([]);
      tableDs.unSelectAll();
    }
    tableDs.create({ operationTagConfigId: '', realName, lastUpdateDate });
    tableDs.current.setState('editing', true);
    setDisabledFlag(true);
  };

  const handleCreateLine = () => {
    if (tableDs.selected.length) {
      assObjectsDs.create({
        operationTagConfigId: tableDs.selected[0].data.operationTagConfigId,
        operationTagConfigLineId: '',
        realName,
        lastUpdateDate,
      });
    } else {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.error.check.head`)
          .d(`请先勾选头数据`),
        placement: 'bottomRight',
      });
    }
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          onClick={() => handleCreate()}
          funcType="flat"
          // shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      lock: 'left',
      renderer: ({ record }) => {
        return (
          record.get('operationTagConfigId') === '' && (
            <Popconfirm
              title= {intl
                .get(`${modelPrompt}.error.delete`)
                .d(`是否确认删除？`)}
              onConfirm={() => {
                tableDs.remove(record);
                if (tableDs.toJSONData().length === 0) {
                  setDisabledFlag(false);
                }
              }}
            >
              <Button funcType="flat" icon="remove" shape="circle" size="small" />
            </Popconfirm>
          )
        );
      },
    },
    // 站点
    {
      name: 'siteObj',
      align: 'left',
      renderer: ({ record }) => record.get('siteCode'),
      editor: record => {
        return (
          record.getState('editing') && (
            <Lov dataSet={tableDs} name="siteObj" onChange={() => changeSite(record)} />
          )
        );
      },
    },
    // 物料编码
    {
      name: 'materialObj',
      align: 'left',
      renderer: ({ record }) => record.get('materialCode'),
      editor: record => {
        return (
          record.getState('editing') && (
            <Lov
              dataSet={tableDs}
              name="materialObj"
              onChange={lovRecords => changeObject(lovRecords, record)}
            />
          )
        );
      },
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
    },
    // 物料版本
    {
      name: 'revisionCode',
      align: 'left',
      editor: record => {
        return (
          record.getState('editing') &&
          record.get('revisionFlag') === 'Y' && (
            <Select name="revisionCode" dataSet={tableDs} onChange={() => changeVersion(record)} />
          )
        );
      },
    },
    // 工艺名称
    {
      name: 'operationObj',
      align: 'left',
      renderer: ({ record }) => record.get('operationName'),
      editor: record => {
        return record.getState('editing') && <Lov dataSet={tableDs} name="operationObj" />;
      },
    },
    // 工艺描述
    {
      name: 'description',
      align: 'left',
      renderer: ({ record }) => record.get('description'),
    },
    // 产线
    {
      name: 'prodLineObj',
      align: 'left',
      renderer: ({ record }) => record.get('prodLineCode'),
      editor: record => {
        return record.getState('editing') && <Lov dataSet={tableDs} name="prodLineObj" />;
      },
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      editor: record => {
        return record.getState('editing') && <Select dataSet={tableDs} name="enableFlag" />;
      },
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} 
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }>
          {}
        </Badge>
      ),
    },
    // 更新人
    {
      name: 'realName',
      align: 'left',
      renderer: ({ record }) => record.get('realName'),
    },
    // 更新时间
    {
      name: 'lastUpdateDate',
      align: 'left',
      renderer: ({ record }) => record.get('lastUpdateDate'),
    },
  ];
  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MT.MES.OPERATION_TAG_CONFIG`,
      title: intl.get('tarzan.hmes.EquipmentGroupMaintenance.import').d('工序反馈参数项配置导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('工序反馈参数项配置')}>
        {!disabledFlag && (
          <>
            <Button onClick={handelEdit} style={{ marginRight: 15 }} icon="edit" color="primary">
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Button onClick={handleImport} style={{ marginRight: 15 }} color="primary" icon="daorucanshu">
              {intl.get('tarzan.common.button.repeat').d('导入')}
            </Button></>

        )}
        {disabledFlag && (
          <>
            <Button onClick={handelCancel}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
            <Button onClick={handelSave} style={{ marginRight: 15 }} icon="save" color="primary">
              {intl.get('tarzan.common.button.edit').d('保存')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Table
            dataSet={tableDs}
            columns={columns}
            style={{ height: 400 }}
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            queryFieldsLimit={4}
            searchCode="ProcessFeedbackConfiguration"
            customizedCode="ProcessFeedbackConfiguration"
          />
          <LineList
            assObjectsDs={assObjectsDs}
            canEdit={disabledFlag}
            handleCreateLine={handleCreateLine}
          />
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.EquipmentGroupMaintenance', 'tarzan.common'],
})(ProcessFeedbackConfiguration);
