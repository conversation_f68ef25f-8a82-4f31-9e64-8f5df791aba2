/**
 * 工单-检查项table （工单的检查项tab、工单任务的检查项tab共用）
 * @since 2021-01-15
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */

import React, { Component } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Button, Modal, Table } from 'choerodon-ui/pro';
import { Popconfirm, Icon } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';

import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { HALM_MTC } from 'alm/utils/config';
import request from 'utils/request';

import ChecklistEditModal from 'alm/components/ChecklistEditModal';
import { handleRecord } from 'alm/utils/checklistUtils';
import { saveOptions } from 'alm//components/ChecklistEditModal/api';
import CheckFileUploadButton from '../../FileUploadButton/check';
import { tableDs } from './Stores/listDs.js';

const organizationId = getCurrentOrganizationId();

const viewPrompt = 'amtc.woChecklists.view.message';

const saveUrl = `${HALM_MTC}/v1/${organizationId}/wo-checklists`;
const deleteUrl = `${HALM_MTC}/v1/${organizationId}/wo-checklists`;
// 交换检查项顺序
const changeItemSeqUrl = `${HALM_MTC}/v1/${organizationId}/wo-checklists/batch-update-seq`;
@observer
class WoChecklistList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      childrenFlag: false, // 存在子级
    };

    this.tableDs = new DataSet(tableDs());
    this.tableDs.handleSetState = this.handleSetState;

    this.editFormRef = React.createRef();
  }

  @Bind
  handleSearch() {
    const { parentId, parentType } = this.props;
    this.tableDs.setQueryParameter('parentTypeCode', parentType);
    this.tableDs.setQueryParameter('parentId', parentId);
    this.tableDs.query();
  }

  /**
   * 刷新列表数据
   */
  @Bind()
  handleRefresh() {
    this.tableDs.query();
  }

  @Bind()
  handleSetState(records) {
    this.setState({
      ...records,
    });
  }

  /**
   * 删除
   */
  @Bind()
  handleDeleteLine(record) {
    request(deleteUrl, {
      method: 'DELETE',
      body: record,
    }).then(res => {
      if (res && res.failed) {
        notification.warning({ message: res.message });
      } else {
        notification.success();
        // 刷新列表
        this.handleRefresh();
      }
    });
  }

  //  ---------  检查项新增/编辑/查看  start -----------

  // 打开 检查项编辑/查看modal
  @Bind
  openEditModal(isNew, editFlag, currentData = {}) {
    const { parentId, parentName, parentType, status, maintSiteId } = this.props;
    // 控制部分字段在 新建 DRAFT APPROVED 下才可编辑
    const statusFlag = isNew || status === 'DRAFT' || status === 'APPROVED';
    const woChecklistEditProps = {
      isNew,
      editFlag,
      compParentId: parentId, // woopId
      compParentName: parentName,
      compParentType: parentType, // 'WOOP'
      statusFlag,
      tenantId: organizationId,
      dataSource: currentData,
      maintSiteIds: [maintSiteId],
    };

    this._editModal = Modal.open({
      key: 'editModal',
      destroyOnClose: true,
      closable: true,
      drawer: true,
      style: {
        width: 700,
      },
      title: intl.get(`${viewPrompt}.title.checkList`).d('检查项'),
      children: <ChecklistEditModal {...woChecklistEditProps} ref={this.editFormRef} />,
      footer: this.getFooter(isNew, editFlag),
    });
  }

  /**
   * 新增
   */
  @Bind()
  handleCreateWoChecklist() {
    const { parentId, parentName, parentType } = this.props;
    this.openEditModal(true, false, {
      parentTypeCode: parentType, // 'WOOP'
      parentId,
      parentName,
    });
  }

  /**
   * 编辑
   */
  @Bind()
  handleEditLine(record) {
    this.openEditModal(false, true, record);
  }

  /**
   * 查看
   */
  @Bind()
  handleViewLine(record) {
    this.openEditModal(false, false, record);
  }

  getFooter(isNew, editFlag) {
    return isNew || editFlag
      ? [
        <Button key="ok" color="primary" onClick={this.handleOk}>
          {intl.get(`hzero.common.button.ok`).d('确认')}
        </Button>,
        <Button
          key="cancel"
          onClick={() => {
            this._editModal.close();
          }}
        >
          {intl.get('hzero.common.button.cancel').d('取消')}
        </Button>,
      ]
      : [
        <Button
          key="close"
          onClick={() => {
            this._editModal.close();
          }}
        >
          {intl.get(`hzero.common.button.close`).d('关闭')}
        </Button>,
      ];
  }

  /**
   * 确认按钮：保存数据并刷新检查项列表、隐藏编辑表单
   */
  @Bind
  async handleOk() {
    const formRef = this.editFormRef?.current;
    if (formRef) {
      const result = await formRef.detailDs.current.validate();
      if (result) {
        const detail = formRef.detailDs.current.toData();
        const trueValue = formRef.trueNumberDs.toData();
        const { columnTypeCode, listValueCode } = detail;
        const isOk = columnTypeCode === 'LISTOFVALUE' ? true : await formRef?.optionsTableDs.validate();
        const optionsList = formRef?.optionsTableDs?.records?.map(i => i.toData());

        request(saveUrl, {
          method: 'POST',
          body: {
            tenantId: organizationId,
            ...detail,
            woId: this.props.woId,
            standardReferenceList: trueValue,
          },
        }).then(async res => {
          if (res && !res.failed) {
            if (
              columnTypeCode === 'YESORNO' ||
              (columnTypeCode === 'LISTOFVALUE' && listValueCode)
            ) {
              if (isOk) {
                saveOptions({
                  list: optionsList,
                  checklistId: res.checklistId,
                  compParentType: this.props.parentType,
                }).then(res1 => {
                  if (res1 && !res1.failed) {
                    notification.success();
                    // 刷新列表
                    this.handleSearch();
                    this._editModal.close();
                  } else {
                    notification.warning({
                      message: res1.message,
                    });
                  }
                });
              }
            } else {
              notification.success();
              // 刷新列表
              this.handleSearch();
              this._editModal.close();
            }
          } else {
            notification.warning({
              message: res.message,
            });
          }
        });
      }
    }
  }

  //  ---------  检查项新增/编辑/查看  end -----------

  // ------------ 记录实际值modal start ----------------
  /**
   * 打开记录实际值model
   */
  @Bind()
  handleActualValueModal(record) {
    handleRecord(record, {
      sourceType: 'WO',
      recordOkCallback: this.handleRecordOk,
    });
  }

  // 将记录检查项实际值 及 检查项创建仪表点拆分为两步了 先记录再创建 所以无论创建仪表点成功与否 都需要刷新数据
  @Bind()
  handleRecordOk(record, res) {
    // 刷新列表
    this.handleRefresh();
    if (res && res.failed) {
      notification.warning({ message: res.message });
      return Promise.reject();
    }
    notification.success();

  }
  // ----------- 记录实际值modal end -------------

  //   ----------  检查项上下移动功能 start ---------------

  /**
   * 移动检查项
   * @param {*} record 当前对象
   * @param {*} flag 向上或向下移动
   */
  handleMove(record, flag, parentChecklistId = null) {
    const dataList = this.tableDs.toData();
    const { checklistId } = record;
    let data = [];

    const parentsArr = []; // 父级
    const childsArr = []; // 子级
    dataList.forEach(i => {
      if (!i.parentChecklistId) {
        parentsArr.push(i);
      } else {
        childsArr.push(i);
      }
    });

    if (parentChecklistId) {
      // 是子级
      // 找到所在父级的所有子级
      const childs = childsArr.filter(
        child => child.parentChecklistId === record.parentChecklistId,
      );
      data = this.handleGetNewData(childs, checklistId, flag);
    } else {
      // 是父级
      data = this.handleGetNewData(parentsArr, checklistId, flag);
    }

    this.handleChangeItemSeq(data);
  }

  @Bind()
  handleGetNewData(arr, checklistId, flag) {
    // 自己的数据
    let selfData = {};
    // 交换对象的位置
    let targetIndex = null;
    arr.forEach((i, index) => {
      if (i.checklistId === checklistId) {
        selfData = i;
        if (flag === 'up') {
          targetIndex = index - 1;
        } else {
          targetIndex = index + 1;
        }
      }
    });
    // 交换对象的数据
    const targetData = arr.filter((_, index) => index === targetIndex)[0];

    // 交换 itemSeq
    return [
      { ...selfData, itemSeq: targetData.itemSeq },
      { ...targetData, itemSeq: selfData.itemSeq },
    ];
  }

  /**
   * 调用交换序号的接口
   * @param {*} array 数组
   */
  handleChangeItemSeq(array) {
    request(changeItemSeqUrl, {
      method: 'POST',
      body: array,
    }).then(res => {
      if (res && !res.failed) {
        notification.success();
        // 刷新列表
        this.handleRefresh();
      } else {
        notification.error({
          message: res.message,
        });
      }
    });
  }

  //   ----------  检查项上下移动功能 end ---------------

  // 字段 actValue 的渲染
  renderActValue(value, record) {
    let returnVal = '';
    switch (record.columnTypeCode) {
      case 'YESORNO':
      case 'LISTOFVALUE':
        returnVal = record.actValueMeaning;
        break;
      default:
        returnVal = value;
        break;
    }
    return returnVal;
  }

  get columns() {
    const {
      woId,
      parentId,
      viewFlag,
      viewCheckItemBtn,
      editCheckItemBtn,
      recordCheckItemBtn,
      deleteCheckItemBtn,
      sortCheckItemFlag,
    } = this.props; // 工单或者任务的状态
    const { childrenFlag } = this.state;
    const columns = [
      {
        name: 'checklistName',
        width: 150,
      },
      {
        name: 'businessScenarioMeaning',
        width: 150,
      },
      {
        name: 'columnTypeMeaning',
        width: 120,
      },
      {
        name: 'actValue',
        width: 150,
        // lock: 'right',
        renderer: ({ text, record }) => this.renderActValue(text, record.toData()),
      },
      {
        name: 'description',
        width: 150,
        // lock: 'right',
      },
      !viewFlag && {
        header: intl.get('hzero.common.button.action').d('操作'),
        name: 'action',
        width: 200,
        lock: 'right',
        align: 'center',
        renderer: ({ record }) => {

          const data = record.toData();
          const btns = [];

          const FileUploadButtonProps = {
            type: 'CHECK',
            showContentFlag: true,
            woId,
            moduleId: parentId,
            parentTypeCode: 'WO',
          };
          btns.push(
            <CheckFileUploadButton {...FileUploadButtonProps} />,
          );

          if (viewCheckItemBtn?.show) {
            btns.push(
              <a onClick={() => this.handleViewLine(data, true)}>
                {intl.get('hzero.common.button.view').d('查看')}
              </a>,
            );
          }
          if (editCheckItemBtn?.show) {
            btns.push(
              <a onClick={() => this.handleEditLine(data, true)}>
                {intl.get('hzero.common.button.edit').d('编辑')}
              </a>,
            );
          }

          if (recordCheckItemBtn?.show && data.columnTypeCode !== 'INDEX') {
            btns.push(
              <a onClick={() => this.handleActualValueModal(data)}>
                {intl.get(`${viewPrompt}.button.record`).d('记录')}
              </a>,
            );
          }
          if (deleteCheckItemBtn?.show) {
            btns.push(
              <Popconfirm
                title={intl.get(`${viewPrompt}.confirmDelete`).d('是否删除该记录?')}
                placement="topRight"
                onConfirm={() => this.handleDeleteLine(data)}
                okText={intl.get('hzero.common.status.yes').d('是')}
                cancelText={intl.get('hzero.common.status.no').d('否')}
              >
                <a>{intl.get('hzero.common.button.delete').d('删除')}</a>
              </Popconfirm>,
            );
          }
          return <span className="action-link">{btns}</span>;
        },
      },
      // 只在拟定状态显示排序
      !viewFlag &&
        sortCheckItemFlag && {
        header: intl.get(`${viewPrompt}.column.sort`).d('排序'),
        name: 'sort',
        width: childrenFlag ? 200 : 100,
        lock: 'right',
        align: 'center',
        renderer: ({ record: originRecord }) => {
          const record = originRecord.toData();
          return (
            <React.Fragment>
              {/* 子级的箭头 */}
              <span
                style={{ visibility: record.parentChecklistId ? 'hidden' : 'visible' }}
                className="action-link"
              >
                <a disabled={record.firstFlag}>
                  <Icon type="arrow_upward" onClick={() => this.handleMove(record, 'up')} />
                </a>
                <a disabled={record.lastFlag}>
                  <Icon type="arrow_downward" onClick={() => this.handleMove(record, 'down')} />
                </a>
              </span>
              {/* 父级的箭头 */}
              <span
                style={{
                  display: childrenFlag ? 'inline' : 'none',
                  visibility: record.parentChecklistId ? 'visible' : 'hidden',
                }}
                className="action-link"
              >
                <a disabled={record.firstFlag}>
                  <Icon
                    type="arrow_upward"
                    onClick={() => this.handleMove(record, 'up', record.parentChecklistId)}
                  />
                </a>
                <a disabled={record.lastFlag}>
                  <Icon
                    type="arrow_downward"
                    onClick={() => this.handleMove(record, 'down', record.parentChecklistId)}
                  />
                </a>
              </span>
            </React.Fragment>
          );
        },
      },
    ];
    return columns;
  }

  render() {
    const {
      addCheckItemBtn: { show = true },
    } = this.props;

    return (
      <React.Fragment>
        {show && (
          <div style={{ textAlign: 'right', marginBottom: 8 }}>
            <Button icon="add" onClick={this.handleCreateWoChecklist}>
              {intl.get(`hzero.common.button.add`).d('新增')}
            </Button>
          </div>
        )}
        <Table
          key="woChecklist"
          customizedCode="AORI.WORK_ORDER.WO_CHECKLIST"
          dataSet={this.tableDs}
          columns={this.columns}
          mode="tree"
        />
      </React.Fragment>
    );
  }
}
export default WoChecklistList;
