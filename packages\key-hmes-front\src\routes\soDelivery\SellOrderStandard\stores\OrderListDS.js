import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'hzero-front/lib/utils/notification';

const modelPrompt = 'tarzan.hmes.purchase.sellOrder';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: 'single',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'soId',
  autoLocateFirst: true,
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-so-head/head/query`,
        method: 'GET',
        data: {
          ...data,
          revisionCode:
            (data.revisionCode && data.revisionCode.length) > 0
              ? data.revisionCode.join(',')
              : undefined,
          operationType: 'SO_DOC',
        },
      };
    },
  },
  queryFields: [
    {
      name: 'soNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNum`).d('销售订单'),
    },
    {
      name: 'supplier',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customer`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'customerId',
      type: FieldType.string,
      bind: 'supplier.customerId',
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.shipFromSiteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      textField: 'materialCode',
      valueField: 'materialId',
      computedProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'revisionCodes',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      multiple: true,
    },
    {
      name: 'locator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.shipLocatorCode`).d('发运库位'),
      lovCode: 'MT.MODEL.USER.SITE.LOCATOR',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      computedProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'shipLocatorId',
      type: FieldType.string,
      bind: 'locator.locatorId',
    },
    {
      name: 'salesPerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.salesPerson`).d('销售员'),
    },
    {
      name: 'soType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soType`).d('销售订单类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-instruction-doc/operation-type/limit/doc/type/list?operationType=SO_DOC`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'customerSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerSite`).d('客户地点'),
      // lovCode: 'MT.MODEL.SUPPLIER_SITE',
      lovCode: 'MT.MODEL.CUSTOMER_SITE',
      textField: 'description',
      valueField: 'customerSiteId',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('customerId');
        },
        lovPara({ record }) {
          return {
            tenantId,
            customerId: record.get('customerId'),
          };
        },
      },
    },
    {
      name: 'customerSiteId',
      type: FieldType.string,
      bind: 'customerSiteLov.customerSiteId',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
  ],
  fields: [
    {
      name: 'soNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNum`).d('销售订单'),
    },
    {
      name: 'soStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('销售订单状态'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shipFromSiteCode`).d('站点'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户名称'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerSiteCode`).d('客户地点名称'),
    },
    {
      name: 'shipToName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shipTo`).d('收货方'),
    },
    {
      name: 'shipToSiteDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shipToSiteDesc`).d('收货地址'),
    },
    {
      name: 'salesPerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.salesPerson`).d('销售员'),
    },
    {
      name: 'soType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soType`).d('销售订单类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-instruction-doc/operation-type/limit/doc/type/list?operationType=SO_DOC`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },

    {
      name: 'freezeFlag',
      type: FieldType.string,
      // trueValue: 'Y',
      // falseValue: 'N',
      label: intl.get(`${modelPrompt}.freezeFlag`).d('信用冻结标识'),
    },
    {
      name: 'currencyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currencyCode`).d('币种'),
    },
    {
      name: 'soCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soCategoryDesc`).d('业务类型'),
    },
    {
      name: 'salesChannel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.salesChannel`).d('销售渠道'),
    },
    {
      name: 'commercialTerm',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.commercialTerm`).d('国际贸易条件'),
    },
    {
      name: 'paymentTerm',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paymentTerm`).d('付款条件'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.Remark`).d('备注'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
});

const lineTableDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    selection: 'multiple',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    cacheSelection: true,
    primaryKey: 'soLineId',
    transport: {
      read: event => {
        const data = {};
        const _data = event?.dataSet?.queryDataSet?.toData()[0];
        if (_data?.site?.siteId) {
          data.siteId = _data.site.siteId;
        }
        return {
          // 采购订单列表 接口待替换
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-so-head/line/query`,
          method: 'GET',
          params: {
            ...event.params,
            ...data,
          },
          transformResponse: val => {
            const datas = JSON.parse(val);
            if (datas && !datas.success) {
              if (datas.message) {
                notification.error({ message: datas.message });
              }
              return {
                rows: [],
              };
            }
            return {
              ...datas,
            };
          },
        };
      },
    },
    queryFields: [
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
      },
      {
        name: 'shipLocatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.shipLocatorCode`).d('发运库位'),
      },
      {
        name: 'scheduleShipDateFrom',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.scheduleShipDateFrom`).d('计划发运日期从'),
        max: 'scheduleShipDateTo',
      },
      {
        name: 'scheduleShipDateTo',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.scheduleShipDateTo`).d('计划发运日期至'),
        min: 'scheduleShipDateFrom',
      },
    ],
    fields: [
      {
        name: 'soLineNum',
        type: FieldType.string,
      },
      {
        name: 'manageMode',
        type: FieldType.string,
      },
      {
        name: 'lineType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineType`).d('行类型'),
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=SO_LINE_TYPE`,
        textField: 'description',
        valueField: 'typeCode',
        required: true,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'lineStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineStatus`).d('行状态'),
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=SO_LINE_STATUS`,
        textField: 'description',
        valueField: 'statusCode',
        defaultValue: 'USABLE',
        required: true,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'materialCode',
        type: FieldType.string,
      },
      {
        name: 'materialName',
        type: FieldType.string,
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
      },
      {
        name: 'materialTypeName',
        type: FieldType.string,
      },
      {
        name: 'uomCode',
        type: FieldType.string,
      },
      {
        name: 'orderedQuantity',
        type: FieldType.number,
      },
      {
        name: 'processedOrdered',
        type: FieldType.number,
      },
      {
        name: 'unitPrice',
        type: FieldType.string,
      },
      {
        name: 'shipFromSiteCode',
        type: FieldType.string,
      },
      {
        name: 'shipLocatorCode',
        type: FieldType.string,
      },
      {
        name: 'shipMethod',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.shipMethod`).d('发运方式'),
      },
      {
        name: 'quantityReceived',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.quantityReceived`).d('已备货数量'),
      },
      {
        name: 'quantityDelivered',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.quantityDelivered`).d('已发运数量'),
      },
      {
        name: 'reservedQuantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.reservedQuantity`).d('预留数量'),
      },
      {
        name: 'cancelledQuantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.cancelledQuantity`).d('取消数量'),
      },
      {
        name: 'scheduleShipDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.scheduleShipDate`).d('计划发运日期'),
      },
      {
        name: 'scheduleArrivalDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.scheduleArrivalDate`).d('计划到达日期'),
      },
      {
        name: 'packingInstructions',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.packingInstructions`).d('包装需求'),
      },
      {
        name: 'itemCategory',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.itemCategory`).d('项目类别'),
        lookupCode: 'MT.ITEM_CATEGORY',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'freezeFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.freezeFlag`).d('信用冻结标识'),
      },
      {
        name: 'contractNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.contractNum`).d('合同编码'),
      },
      {
        name: 'customerPoNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.customerPoNum`).d('客户采购订单编码'),
      },
      {
        name: 'customerPoLineNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.customerPoLineNum`).d('客户采购订单行号'),
      },
      {
        name: 'parentLineNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.parentLineNum`).d('母件订单行号'),
      },
      {
        name: 'reason',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.reason`).d('原因'),
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineRemark`).d('行备注'),
      },
      {
        name: 'decimalNumber',
        type: FieldType.number,
      },
    ],
  };
};

export { headerTableDS, lineTableDS };
