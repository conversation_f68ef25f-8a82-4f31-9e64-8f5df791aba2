// 容器装载详情
import React, { useState, useEffect, useMemo } from 'react';
import { Button, DataSet, Table, Modal, Tooltip } from 'choerodon-ui/pro';
import { Icon } from 'hzero-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { observer } from 'mobx-react';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import cardSvg from '@/assets/icons/operation.svg';
import { useOperationPlatform, DispatchType } from '../../contextsStore';
import { tableDs, failedFormDS, failedTableDS } from './stores/ContainerLoadingDetailDS';
import { CardLayout } from '../commonComponents';
import FailedModal from './FailedModal';
import styles from './index.modules.less';

const tenantId = getCurrentOrganizationId();
let qureyFailedModal;
const ContainerLoadingDetails = observer(props => {
  const { dispatch, containerDetail, enterInfo } = useOperationPlatform();
  const tableDataSet = useMemo(
    () =>
      new DataSet({
        ...tableDs(),
      }),
    [],
  );
  const failedFormDs = useMemo(
    () =>
      new DataSet({
        ...failedFormDS(),
      }),
    [],
  );

  const failedTableDs = useMemo(
    () =>
      new DataSet({
        ...failedTableDS(),
      }),
    [],
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    tableDataSet.loadData(containerDetail.containerLoadDetailList || []);
  }, [containerDetail]);

  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  useEffect(() => {
    queryPackingLevel();
  }, []);

  const queryPackingLevel = () => {
    const requestUrl = `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=PACKING_LEVEL`;
    request(requestUrl, {
      method: 'GET',
    }).then(res => {
      if (res && !res.failed) {
        tableDataSet.setState('packingLevelList', res.rows);
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  // 容器卡片查询容器信息
  const queryContainerDetail = () => {
    const params = {
      // @ts-ignore
      containerCode: containerDetail.containerCode,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-encasement/container/scan/ui`, {
      method: 'GET',
      query: params,
    }).then(async res => {
      if (res && res.success) {
        dispatch({
          type: DispatchType.update,
          payload: {
            containerDetail: res.rows,
          },
        });
        const {
          containerId,
          containerCode,
          containerTypeDescription,
          capacityQty,
          sumLoadQty,
        } = res.rows;
        failedFormDs.loadData([
          { containerId, containerCode, containerTypeDescription, qty: capacityQty - sumLoadQty },
        ]);
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  const handleUnload = () => {
    const { containerId, containerCode } = containerDetail;
    const data = tableDataSet.selected.map(i => {
      return {
        ...i.toData(),
        containerId,
      };
    });
    const requestUrl = `${
      BASIC.HMES_BASIC
    }/v1/${getCurrentOrganizationId()}/hme-container-encasement/container/unload/ui`;
    request(requestUrl, {
      method: 'POST',
      body: {
        containerUnloadObjectList: data,
      },
    }).then(res => {
      if (res && !res.failed) {
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          message: `条码${data
            .map(i => i.loadObjectCode)
            .join('、')}从容器${containerCode}卸载成功`,
        });
        notification.success({
          message: `条码${data
            .map(i => i.loadObjectCode)
            .join('、')}从容器${containerCode}卸载成功`,
        });
        queryContainerDetail();
      } else {
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'FAIL',
          message: `条码${data
            .map(i => i.loadObjectCode)
            .join('、')}从容器${containerCode}卸载失败，原因为${res.message}`,
        });
        notification.error({
          message: `条码${data
            .map(i => i.loadObjectCode)
            .join('、')}从容器${containerCode}卸载失败，原因为${res.message}`,
        });
      }
    });
  };
  const getDescription = value => {
    return tableDataSet.getState('packingLevelList')?.filter((i: any) => i.typeCode === value)[0]
      ?.description;
  };
  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        align: ColumnAlign.left,
      },
      {
        name: 'materialCode',
        align: ColumnAlign.center,
      },
      {
        name: 'loadQty',
        align: ColumnAlign.center,
        width: 70,
      },
      {
        name: 'qualityStatusDesc',
        align: ColumnAlign.center,
        width: 80,
      },
      {
        name: 'loadTime',
        align: ColumnAlign.left,
        width: 140,
        renderer: ({ value }) => (
          <Tooltip title={value}>{value.slice(value.indexOf(' '), value.length)}</Tooltip>
        ),
      },
    ];
  }, [tableDs]);

  // 重装/卸载/装载当前容器
  const handleExecute = (record, type) => {
    const data = { ...record.toData(), containerId: failedFormDs.current?.get('containerId') };
    const requestUrl = `${
      BASIC.HMES_BASIC
    }/v1/${getCurrentOrganizationId()}/hme-container-encasement/pre-load/update/ui`;
    return request(requestUrl, {
      method: 'POST',
      body: {
        ...data,
        opType: type,
      },
    }).then(res => {
      if (res && !res.failed) {
        if (type === 'STOWAGE') {
          if(!res.message){
            props.handleAddRecords({
              cardId: props.cardId,
              messageType: 'SUCCESS',
              message: `条码${data.loadObjectCode}装载到容器${failedFormDs.current?.get(
                'containerCode',
              )}成功`,
            });
            notification.success({
              message: `条码${data.loadObjectCode}装载到容器${failedFormDs.current?.get(
                'containerCode',
              )}成功`,
            });
          }else{
            props.handleAddRecords({
              cardId: props.cardId,
              messageType: 'FAIL',
              message: `条码${data.loadObjectCode}过站成功，条码${data.loadObjectCode}装载到容器${failedFormDs.current?.get(
                'containerCode',
              )}失败,失败原因: ${res.message}`,
            });
            notification.error({
              message: `条码${data.loadObjectCode}过站成功，条码${data.loadObjectCode}装载到容器${failedFormDs.current?.get(
                'containerCode',
              )}失败,失败原因: ${res.message}`,
            });
          }
        }
        queryData();
        queryContainerDetail();
      } else if (type === 'STOWAGE') {
        record.isCurrent = true;
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'FAIL',
          message: `条码${data.loadObjectCode}装载到容器${failedFormDs.current?.get(
            'containerCode',
          )}失败,原因为:${res.message}`,
        });
        notification.error({
          message: `条码${data.loadObjectCode}装载到容器${failedFormDs.current?.get(
            'containerCode',
          )}失败,原因为:${res.message}`,
        });
      }
    });
  };

  // 查询
  const queryData = () => {
    failedTableDs.setQueryParameter('workcellId', enterInfo.workStationId);
    failedTableDs.setQueryParameter(
      'containerLoadType',
      enterInfo.selectOperation?.containerLoadType,
    );
    failedTableDs.setQueryParameter('prodLineId', enterInfo.productionLineId);
    failedTableDs.query(failedTableDs.currentPage);
  };

  const handleChangeStep = value => {
    failedTableDs.setQueryParameter('stepCompleteFlag', value);
  };

  const queryFailedList = () => {
    const {
      containerId,
      containerCode,
      containerTypeDescription,
      capacityQty,
      sumLoadQty,
    } = containerDetail;
    failedFormDs.loadData([
      { containerId, containerCode, containerTypeDescription, qty: capacityQty - sumLoadQty },
    ]);
    failedTableDs.setQueryParameter('stepCompleteFlag', 'N');
    queryData();
    const failedModalProps = {
      handleExecute,
      failedFormDs,
      failedTableDs,
      enterInfo,
      containerDetail,
      handleChangeStep,
    };
    qureyFailedModal = Modal.open({
      header: (
        <div className={styles.c7nProModalheader}>
          <img src={cardSvg} alt="" />
          <div className={styles.c7nProModalTitle}>未装箱查询</div>
          <div
            className={styles.c7nProModalTitle}
            style={{ position: 'absolute', right: 30, cursor: 'pointer' }}
          >
            <Icon
              onClick={() => {
                qureyFailedModal.close();
              }}
              type="close"
            />
          </div>
          <div className={styles.c7nProModalTitle} style={{ position: 'absolute', right: 60 }}>
            <Button color={ButtonColor.primary} onClick={queryData}>
              刷新
            </Button>
          </div>
        </div>
      ),
      key: Modal.key(),
      destroyOnClose: true,
      closable: true,
      mask: true,
      style: { width: '80%' },
      contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
      className: styles.numberModal,
      children: <FailedModal {...failedModalProps} />,
      footer: null,
    });
  };

  return (
    <CardLayout.Layout spinning={loading} className={styles.containerLoadingDetail}>
      <CardLayout.Header
        className="ContainerLoadingDetailsHead"
        title="容器装载明细"
        addonAfter={
          <div>
            <Button color={ButtonColor.primary} onClick={queryFailedList}>
              未装查询
            </Button>
            <Button
              disabled={tableDataSet.selected.length === 0}
              className={styles.orangeButton}
              onClick={handleUnload}
            >
              卸载
            </Button>
          </div>
        }
      />
      <CardLayout.Content style={{ position: 'relative' }} className="ContainerLoadingDetailsForm">
        <Table
          dataSet={tableDataSet}
          columns={columns}
          onRow={({ record }) => {
            if (record && record.data && record.get('loadObjectType') === 'MATERIAL_LOT') {
              return {
                className: styles.materialLotCodeTag,
              };
            }
            if (record && record.data && record.get('loadObjectType') === 'EO') {
              return {
                className: styles.eoTag,
              };
            }
            if (record && record.data && record.get('loadObjectType') === 'CONTAINER') {
              return {
                className: styles.containerTag,
              };
            }
            return {};
          }}
        />
      </CardLayout.Content>
    </CardLayout.Layout>
  );
});

export default formatterCollections({ code: ['model.org.monitor'] })(ContainerLoadingDetails);
