# Default values for api-gateway.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: registry.cn-hangzhou.aliyuncs.com/{gitlabGroup}/tarzan-front

# preJob:
#   preConfig:
#     mysql:
#       host: localhost
#       port: 3306
#       username: choerodon
#       password: 123456
#       dbname: iam_service

service:
  enable: false
  type: ClusterIP
  port: 80
  name: tarzan-front

ingress:
  enable: false

env:
  open:
    BUILD_BASE_PATH: /
    BUILD_PUBLIC_URL: /
    BUILD_WEBSOCKET_HOST: http://*************:30880/hpfm/sock-js
    BUILD_CLIENT_ID: localhost
    BUILD_API_HOST: http://*************:30880
    BUILD_FILE_VIEW_HOST: http://************:8012
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: true
    BUILD_PLATFORM_VERSION: SAAS
    BUILD_IM_WEBSOCKET_HOST: ws://dev.hzero.org.46:9876
    BUILD_MULTIPLE_SKIN_ENABLE: false
    BUILD_TOP_MENU_LABELS: HZERO_MENU
    BUILD_TOP_MENU_UNION_LABEL: false
    BUILD_INVALID_TIME: 120
    BUILD_CUSTOMIZE_ICON_NAME: customize-icon
    BUILD_HISTORY_ENABLE: false
    BUILD_SVG_ICON_ENABLE: true

logs:
  parser: nginx

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources,such as Minikube. If you do want to specify resources,uncomment the following
  # lines,adjust them as necessary,and remove the curly braces after 'resources:'.
  limits:
    # cpu: 100m
    # memory: 2Gi
  requests:
    # cpu: 100m
    # memory: 1Gi

