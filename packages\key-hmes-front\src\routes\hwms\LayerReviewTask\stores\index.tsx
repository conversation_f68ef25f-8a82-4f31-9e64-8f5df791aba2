/**
 * @Description: 分层审核任务-主界面DS
 * @Author: <EMAIL>
 * @Date: 2023/8/18 10:03
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.layerReview.layerReviewTask';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  // primaryKey: 'layerReviewTaskId',
  queryFields: [
    {
      name: 'layerReviewTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewTaskCode`).d('分层审核任务编码'),
    },
    {
      name: 'layerReviewTaskStatusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewTaskStatus`).d('分层审核任务状态'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_TASK_STATUS',
      lovPara: { tenantId },
      multiple: true,
      defaultValue: ['NEW', 'EXECUTING'],
    },
    {
      name: 'layerReviewLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewLevel`).d('层级'),
      lookupCode: 'YP.QIS.LEVEL',
      lovPara: { tenantId },
      defaultValue: 'L1',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'productLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'requestFinishTimeBegin',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.requestFinishTimeBegin`).d('要求完成时间从'),
      max: 'requestFinishTimeEnd',
      dynamicProps: {
        defaultValue: () => {
          const currentDate = new Date();
          currentDate.setHours(0, 0, 0, 0);
          return moment(currentDate).format('YYYY-MM-DD HH:mm:ss');
        },
      },
    },
    {
      name: 'requestFinishTimeEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.requestFinishTimeEnd`).d('要求完成时间至'),
      min: 'requestFinishTimeBegin',
      dynamicProps: {
        defaultValue: () => {
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 15);
          currentDate.setHours(0, 0, 0, 0);
          return moment(currentDate).format('YYYY-MM-DD HH:mm:ss');
        },
      },
    },
    {
      name: 'layerRevtskDtlCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerRevTskDtlCode`).d('分层审核任务号'),
    },
    {
      name: 'closeTimeBegin',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.closeTimeBegin`).d('任务关闭时间从'),
      max: 'closeTimeEnd',
    },
    {
      name: 'closeTimeEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.closeTimeEnd`).d('任务关闭时间至'),
      min: 'closeTimeBegin',
    },
  ],
  fields: [
    {
      name: 'layerReviewTaskId',
      type: FieldType.number,
    },
    {
      name: 'layerReviewTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewTaskCode`).d('分层审核任务编码'),
    },
    {
      name: 'layerReviewTaskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewTaskStatus`).d('分层审核任务状态'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_TASK_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'layerReviewLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewLevel`).d('层级'),
      lookupCode: 'YP.QIS.LEVEL',
      lovPara: { tenantId },
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'productLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productLineName`).d('产线'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'requestFinishTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.requestFinishTime`).d('要求完成时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-review-task/ui`,
        method: 'GET',
      };
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'layerRevtskDtlId',
  fields: [
    {
      name: 'layerRevtskDtlId',
      type: FieldType.number,
    },
    {
      name: 'layerRevtskDtlCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerRevTskDtlCode`).d('分层审核任务分号'),
    },
    {
      name: 'equimentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equimentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
    {
      name: 'layerRevtskDtlStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerRevTskDtlStatus`).d('状态'),
      lookupCode: 'YP.QIS.LAYER_REVTSK_DTL_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'closeTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.closeTime`).d('关闭时间'),
    },
    {
      name: 'abnormalFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.abnormalFlag`).d('非正常生产标识'),
    },
    {
      name: 'abnormalCloseReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.abnormalCloseReason`).d('任务异常关闭原因'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const { layerReviewTaskId } = data;
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-revtsk-dtl/${layerReviewTaskId}/ui`,
        method: 'GET',
      };
    },
  },
});

export { headDS, lineDS };
