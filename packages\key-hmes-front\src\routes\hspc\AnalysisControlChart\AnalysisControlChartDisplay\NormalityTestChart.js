/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2022-12-01 16:43:56
 * @LastEditTime: 2023-05-30 15:28:09
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
// import { Chart, Geom, Axis, View } from 'bizcharts';
import { Chart, Geom, Axis, View } from 'bizcharts-dnd-3.5.8';
import DataSet from '@antv/data-set';

const NormalityTestChart = props => {
  const viewWidth = document?.getElementById('cpkChartLeft')?.clientWidth || 100;
  const {
    points = [],
    line = [],
    extent = [],
    ticks = [],
    min = 0,
    max = 0,
    getG2Paint,
    yValues,
  } = props;
  const dv = new DataSet.View().source(line).transform({
    type: 'regression',
    method: 'linear',
    fields: ['xAbscissa', 'zOrdinates'],
    // bandwidth: 0.01,
    extent,
    as: ['xAbscissa', 'zOrdinates'],
  });
  const scale = {
    xAbscissa: {
      alias: '测试',
      min,
      max,
    },
    zOrdinates: {
      tickCount: 13,
      nice: false,
      ticks,
      alias: 'Y', // 别名
    },
    yOrdinates: {
      alias: 'Y', // 别名
    },
  };
  return (
    <Chart
      animate={false}
      placeholder
      onGetG2Instance={getG2Paint}
      height={360}
      width={viewWidth}
      data={points}
      padding={[30, 60, 80]}
      scale={scale}
      forceFit
    >
      {/* <Tooltip
        visible
        showTitle
        showContent
        shared
      /> */}
      <Axis name="xAbscissa" />
      <Axis name="yOrdinates" visible={false} />
      <Geom
        position="xAbscissa*yOrdinates"
        type="point"
        size={2}
        shape="circle"
        style={{
          fillOpacity: 1,
        }}
      />
      <View data={dv}>
        <Axis
          name="zOrdinates"
          label={{
            formatter(text, item, index) {
              const arr = yValues;
              return `${arr[index]}`;
            },
          }}
        />
        <Geom position="xAbscissa*zOrdinates" type="line" size={2} color="red" />
      </View>
    </Chart>
  );
};
export default NormalityTestChart;
