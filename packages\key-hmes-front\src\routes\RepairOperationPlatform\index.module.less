.operationPlatformEnterRepair {
  display: contents;
  :global {
    .page-head {
      background-color: #38708f !important;
    }
    .page-container {
      border-radius: 0px !important;
    }
    .page-content-wrap {
      margin: 0 !important;
    }
    .page-head.page-head .page-head-title {
      color: rgba(1, 225, 239, 1) !important;
      padding-left: 50% !important;
      position: absolute !important;
      font-size: 20 !important;
      font-weight: 700 !important;
    }
  }
}
.card-container {
  padding: 0px;
  // position: relative;
  background-color: #fff;
  // box-shadow: 10px 10px 10px #ebebeb;
  display: flex;
  flex-direction: column;
  &-editing {
    border: 1px dashed #979797;
  }
  .card-lalala {
    // overflow-y: auto;
    /* flex-grow: 1;
    flex-shrink: 1;
    overflow: hidden; */
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .card-content {
    flex-grow: 1;
    width: 100%;
    height: 100%;
    overflow: auto;
  }
  .card-close-icon {
    position: absolute;
    pointer-events: all;
    right: 8px;
    top: 8px;
    width: 20px;
    height: 20px;
    transition: opacity 0.2s;
    cursor: pointer;
    z-index: 3;
    &:hover {
      opacity: 0.9;
    }
  }
}
.gridLayoutContainer {
  .dragCard {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    visibility: visible;
    cursor: move;
  }
  .closeBtn {
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 16px;
    cursor: pointer;
  }
  .boxShadow {
    border-radius: 2px;
    transition: 0.5s;
    box-shadow: rgba(0, 34, 77, 0.1) 0 1px 3px;
  }
  .boxShadow:hover {
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);
  }
  :global {
    .react-grid-item {
      overflow: hidden;
      background-color: #fff;
      display: flex;
      flex-direction: column;
    }
    .react-grid-item.react-grid-placeholder {
      background-color: #909090;
      z-index: 0;
    }
    .ant-card-body {
      overflow: auto;
      flex: auto;
    }
    .react-grid-item.react-draggable {
      border: 1px dashed #999;
      padding: 1px;
    }
  }
}
