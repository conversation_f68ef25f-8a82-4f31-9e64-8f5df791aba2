/**
 * @Description: 检验不良记录
 * @Author: <<EMAIL>>
 * @Date: 2023-03-07 10:29:47
 * @LastEditTime: 2023-05-18 16:59:16
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import moment from 'moment/moment';
import { Table, Modal, DataSet, Select, Lov } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { getCurrentUser } from 'utils/utils';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { NcRecordDS } from '../stories/DetailDS';
import { SaveNcRecord, NcDefaultType } from '../services';

export interface NcRecordComponentProps {
  type?: 'button' | 'text';
  text?: string;
  canEdit?: boolean;
  disabled?: boolean;
  visible?: boolean;
  customizeTable?: any;
  queryParams?: any;
  path?: string;
  [key: string]: any;
}

const modelPrompt = 'tarzan.qms.inspectionPlatform';
const userInfo = getCurrentUser();

const NcRecordComponent: React.FC<NcRecordComponentProps> = props => {
  const {
    type = 'button',
    text,
    canEdit = true,
    disabled = false,
    visible = true,
    customizeTable,
    queryParams = {},
    path,
    ...othersProps
  } = props;

  // 检验不良记录
  const ncRecordDS = useMemo(() => new DataSet({ ...NcRecordDS() }), []);

  // 检验不良记录-查询
  const saveNcRecord = useRequest(SaveNcRecord(), {
    manual: true,
    needPromise: true,
  });

  // 检验默认值-查询
  const ncDefaultType = useRequest(NcDefaultType(), {
    manual: true,
    needPromise: true,
  });

  // 检验不良记录-打开弹窗
  const handleOpenDrawer = async () => {
    ncDefaultType.run({
      params: {
        inspectTaskId: queryParams.inspectTaskId,
      },
      onSuccess: res => {
        ncRecordDS.setState('defaultType', res.value);
      },
    });
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
        ds: ncRecordDS,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.ncRecord`).d('不良记录'),
      destroyOnClose: true,
      drawer: true,
      style: {
        width: '70%',
      },
      onOk: handleSaveNcRecord,
      children: (
        <>
          {customizeTable(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.NCRECORD`,
            },
            <Table dataSet={ncRecordDS} columns={columns} highLightRow customizedCode="jyptnr" />,
          )}
        </>
      ),
    });

    await handleFetchNcRecord();
  };

  // 检验不良记录-查询
  const handleFetchNcRecord = async () => {
    ncRecordDS.setState('deleteNcRecordIds', []);
    ncRecordDS.queryParameter = {
      customizeUnitCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.NCRECORD`,
      ...queryParams,
    };
    await ncRecordDS.query().then(res => {
      if (res && res.success) {
        const _baseInfo = res.baseInfo || {};
        ncRecordDS.setState('baseInfo', {
          ..._baseInfo,
          ...queryParams,
          operationId: _baseInfo.operationId || null,
          workcellId: _baseInfo.workcellId || null,
          locatorId: _baseInfo.locatorId || null,
          equipmentId: _baseInfo.equipmentId || null,
        });
      }
    });
  };

  // 检验不良记录-保存
  const handleSaveNcRecord = async () => {
    if (ncRecordDS.status === 'loading') {
      return Promise.resolve(false);
    }
    const validFlag = await ncRecordDS.validate();
    if (!validFlag) {
      return Promise.resolve(false);
    }
    const res = await saveNcRecord.run({
      params: {
        deleteNcRecordIds: ncRecordDS?.getState('deleteNcRecordIds'),
        lines: ncRecordDS.toJSONData(),
      },
    });
    if (res && res.success) {
      notification.success({});
      await handleFetchNcRecord();
    }

    return Promise.resolve(false);
  };

  // 添加不良记录
  const handleAdd = () => {
    let ncRecordType = ncRecordDS.getState('defaultType');
    ncRecordDS.forEach((record, index) => {
      if (index === 0) {
        ncRecordType = record.get('ncRecordType');
      }
    });
    const baseInfo = ncRecordDS.getState('baseInfo');
    ncRecordDS.create({
      ...baseInfo,
      ncRecordUserId: userInfo?.id,
      ncRecordUserName: userInfo?.realName,
      ncRecordTime: new Date(),
      ncRecordType,
    });
  };

  // 删除不良记录
  const handleDelete = record => {
    const inspectDocNcRecordId = record.get('inspectDocNcRecordId');
    if (inspectDocNcRecordId) {
      // 缓存删除ID
      const _deleteNcRecordIds = ncRecordDS.getState('deleteNcRecordIds') || [];
      _deleteNcRecordIds.push(inspectDocNcRecordId);
      ncRecordDS.setState('deleteNcRecordIds', _deleteNcRecordIds);
    }
    ncRecordDS.remove(record);
  };

  const handleNcRecordTypeChange = val => {
    ncRecordDS.forEach(record => {
      record.set('ncRecordType', val);
    });
  };

  const handleNcCodeChange = (val, record) => {
    record.set('defectLevel', null);
    if (val) {
      record.set('defectLevel', val.defectLevel);
    }
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleAdd}
          disabled={!canEdit}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `inspectionPlatform.dist.button.line.add`,
              type: 'button',
              meaning: '详情页-添加删除行',
            },
          ]}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 50,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
            disabled={!canEdit}
            permissionList={[
              {
                code: `inspectionPlatform.dist.button.line.add`,
                type: 'button',
                meaning: '详情页-添加删除行',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemDesc',
      hidden: ['TASK_NC', 'OBJECT_NC'].includes(queryParams?.inspectNcRecordDimension),
    },
    {
      name: 'sourceObjectCode',
      hidden: ['TASK_NC', 'ITEM_NC'].includes(queryParams?.inspectNcRecordDimension),
    },
    {
      name: 'ncRecordType',
      editor: canEdit && (
        <Select
          optionsFilter={record => {
            return record.get('value') !== 'EO_MATERIAL_NC';
          }}
          onChange={handleNcRecordTypeChange}
        />
      ),
      width: 120,
    },
    {
      name: 'ncCodeObj',
      editor: record =>
        canEdit && (
          <Lov
            onChange={value => {
              handleNcCodeChange(value, record);
            }}
          />
        ),
      width: 120,
    },
    {
      name: 'qty',
      editor: canEdit,
    },
    {
      name: 'uomName',
    },
    {
      name: 'defectLevel',
      editor: canEdit,
    },
    {
      name: 'ncRecordTime',
      renderer: ({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value),
      width: 140,
    },
    {
      name: 'ncRecordUserName',
    },
    {
      name: 'operationObj',
      editor: canEdit,
      width: 120,
    },
    {
      name: 'workcellObj',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'locatorObj',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'equipmentObj',
      editor: canEdit,
      width: 130,
    },
    // {
    //   name: 'componentMaterialObj',
    //   editor: canEdit,
    //   width: 130,
    // },
    // {
    //   name: 'componentRevisionCode',
    //   editor: canEdit,
    //   width: 130,
    // },
    // {
    //   name: 'defectLocation',
    //   editor: canEdit,
    // },
    {
      name: 'referenceArea',
      editor: canEdit,
    },
    {
      name: 'referencePoint',
      editor: canEdit,
    },
    {
      name: 'defectReason',
      editor: canEdit,
    },
    // {
    //   name: 'interceptWorkcellObj',
    //   editor: canEdit,
    //   width: 130,
    // },
    // {
    //   name: 'interceptOperationObj',
    //   editor: canEdit,
    //   width: 130,
    // },
    {
      name: 'rootCauseWorkcellObj',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'rootCauseOperationObj',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'rootCauseEquipmentObj',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'responsibleUserObj',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'responsibleApartment',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'shiftTeamCode',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'shiftDate',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'shiftCode',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'containerCode',
      editor: canEdit,
      width: 130,
    },
    {
      name: 'remark',
      editor: canEdit,
    },
    {
      name: 'enclosure',
      editor: canEdit,
    },
  ];

  return (
    <>
      {visible && (
        <>
          {type === 'text' ? (
            <span className="action-link">
              <PermissionButton
                type="text"
                disabled={disabled}
                onClick={handleOpenDrawer}
                permissionList={[
                  {
                    code: `inspectionPlatform.dist.button.ncRecord`,
                    type: 'button',
                    meaning: '详情页-不良记录按钮',
                  },
                ]}
                {...othersProps}
              >
                {text || intl.get(`${modelPrompt}.button.ncRecord`).d('不良记录')}
              </PermissionButton>
            </span>
          ) : (
            <PermissionButton
              type="c7n-pro"
              disabled={disabled}
              onClick={handleOpenDrawer}
              permissionList={[
                {
                  code: `inspectionPlatform.dist.button.ncRecord`,
                  type: 'button',
                  meaning: '详情页-不良记录按钮',
                },
              ]}
              {...othersProps}
            >
              {text || intl.get(`${modelPrompt}.button.ncRecord`).d('不良记录')}
            </PermissionButton>
          )}
        </>
      )}
    </>
  );
};

export default NcRecordComponent;
