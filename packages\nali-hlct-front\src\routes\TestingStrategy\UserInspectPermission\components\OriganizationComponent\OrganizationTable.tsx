/**
 * @Description: 用户权限编辑抽屉-右侧表格
 * @Author: <<EMAIL>>
 * @Date: 2022-10-12 19:08:08
 * @LastEditTime: 2022-10-17 14:03:23
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from "utils/intl";
import styles from './index.module.less';

const modelPrompt = 'tarzan.userInspectPermission';

interface OrganizationTableProps {
  ds: DataSet;
  organizationTableDs: DataSet;
  canEdit: boolean;
}

const OrganizationTable = observer((props: OrganizationTableProps) => {
  const { organizationTableDs, canEdit } = props;

  const organizationColumns: ColumnProps[] = useMemo(() => [
    {
      name: 'organizationType',
      title: intl.get(`${modelPrompt}.organizationType`).d('组织类型'),
    },
    {
      name: 'organizationCode',
      title: intl.get(`${modelPrompt}.organizationCode`).d('组织编码'),
    },
  ],
  [],
  );

  return (
    <>
      <Table
        pagination={false}
        className={styles.tableToolHide}
        dataSet={organizationTableDs}
        columns={organizationColumns}
        onRow={() => ({
          disabled: () => !canEdit,
        })}
      />
    </>
  );
});

export default OrganizationTable;
