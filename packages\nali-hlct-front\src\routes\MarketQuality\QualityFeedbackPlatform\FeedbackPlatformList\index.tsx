/*
 * @Description: 质量反馈单-列表页
 * @Author: <<EMAIL>>
 * @Date: 2023-09-13 11:36:07
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-12 14:49:35
 */
import React, { useMemo, useCallback, useEffect } from 'react';
import { Table, DataSet, Select, Button, Modal } from 'choerodon-ui/pro';
import { Badge, Collapse } from 'choerodon-ui';
import { Record } from 'choerodon-ui/dataset';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useDataSetEvent } from 'utils/hooks';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { headDS, lineDS, maintainItemDS } from '../stores';

const modelPrompt = 'tarzan.hwms.qualityFeedbackPlatform';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

const FeedbackPlatformList = props => {
  const {
    // match: { path },
    headDs,
    lineDs,
    maintainItemDs,
    history,
    // location,
  } = props;

  useDataSetEvent(headDs, 'load', ({ dataSet }) => {
    if (!dataSet.length) {
      // 查询出来没有数据
      lineDs.loadData([]);
      return;
    }
    feedbackLineQuery(dataSet.current);
  });

  useEffect(() => {
    headDs.query(headDs.currentPage);
  }, []);

  const feedbackLineQuery = useCallback((record: Record) => {
    lineDs.setQueryParameter('feedbackId', record.get('feedbackId'));
    lineDs.query();
  }, []);

  const headerRowClick = useCallback(record => {
    feedbackLineQuery(record);
  }, []);

  const maintainItemcolumns : ColumnProps[] = useMemo(() => [
    { name: 'sequence' },
    { name: 'maintainItems' },
  ], []);

  const handleQueryMaintainItems = record => {
    maintainItemDs.setQueryParameter('feedbackId', record?.get('feedbackId'));
    maintainItemDs.query();
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
        ds: maintainItemDs,
      }),
      title: intl.get(`${modelPrompt}.title.maintainItems`).d('维修项目'),
      style: {
        width: 360,
      },
      children: <Table dataSet={maintainItemDs} columns={maintainItemcolumns} />,
    });
  }

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'feedbackNum',
        width: 180,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/market-quality/quality-feedback-platform/dist/${record!.get(
                    'feedbackId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'siteName', width: 180 },
      { name: 'theme' },
      { name: 'warnNum' },
      { name: 'serviceNum', width: 180 },
      { name: 'claimNum', width: 180 },
      { name: 'dataSource' },
      { name: 'batteryNum', width: 180 },
      { name: 'batteryModel' },
      { name: 'hostPlantName' },
      { name: 'vinNum' },
      { name: 'vehicleModel' },
      { name: 'productionDate', width: 150, align: ColumnAlign.center },
      { name: 'soldDate', width: 150, align: ColumnAlign.center },
      { name: 'faultDate', width: 150, align: ColumnAlign.center },
      { name: 'maintainDate', width: 150, align: ColumnAlign.center },
      { name: 'faultMileage' },
      { name: 'serviceSitusNum', width: 150 },
      { name: 'serviceSitusName', width: 150 },
      { name: 'shopNum' },
      { name: 'shopName' },
      { name: 'faultDec', width: 180 },
      { name: 'faultReason' },
      { name: 'faultMeasure' },
      { name: 'faultLevel' },
      { name: 'creationDate', width: 150, align: ColumnAlign.center },
      { name: 'createdByName' },
      {
        name: 'operation',
        width: 150,
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        renderer: ({ record }) => (
          <span className="action-link">
            <a onClick={() => handleQueryMaintainItems(record)}>
              {intl.get(`${modelPrompt}.button.maintainItems`).d('维修项目')}
            </a>
          </span>
        ),
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'feedbackItemNum' },
      { name: 'partsCode' },
      { name: 'partsName' },
      { name: 'materialCode' },
      { name: 'materialName' },
      {
        name: 'primaryUnitFlag',
        align: ColumnAlign.center,
        renderer: ({ value, record }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={record!.getField('primaryUnitFlag')!.getText()}
            />
          );
        },
      },
      { name: 'quantity' },
    ],
    [],
  );

  const handleAdd = useCallback(() => {
    history.push(`/hwms/market-quality/quality-feedback-platform/dist/create`);
  }, []);

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/YP.QIS_QA_FEEDBACK',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('质量反馈平台')}>
        <Button color={ButtonColor.primary} icon="add" onClick={handleAdd}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <Button icon="file_upload" onClick={goImport}>
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
          queryFields={{
            vehicleModel: <Select name="vehicleModel" searchable searchFieldInPopup />,
          }}
          dataSet={headDs}
          columns={headColumns}
          searchCode="feedbackPlatformList_head_searchCode"
          customizedCode="feedbackPlatformList_head_customizedCode"
        />
        <Collapse bordered={false} defaultActiveKey={['lineTable']}>
          <Panel
            key="lineTable"
            header={intl.get(`${modelPrompt}.title.lineTable`).d('零部件信息')}
          >
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="feedbackPlatformList_line_customizedCode"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      const maintainItemDs = new DataSet(maintainItemDS());
      return {
        headDs,
        lineDs,
        maintainItemDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(FeedbackPlatformList),
);
