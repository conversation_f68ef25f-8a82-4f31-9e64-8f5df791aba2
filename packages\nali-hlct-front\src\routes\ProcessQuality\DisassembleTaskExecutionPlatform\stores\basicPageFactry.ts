import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentUser, getCurrentOrganizationId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';


const modelPrompt = 'tarzan.qms.disassembleTaskExecutionPlatform';
const tenantId = getCurrentOrganizationId();

const basicPageFactry = () =>
  new DataSet({
    autoQuery: false,
    autoCreate: true,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskNum`).d('拆解任务编码'),
        name: 'teardownTaskNum',
        type: FieldType.string,
      },
      {
        name: 'materialLotType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotType`).d('电芯类型'),
        lookupCode: 'YP_QIS_TEARDOWN_BARCODE_TYPE',
        lovPara: { tenantId },
        defaultValue: 'OWN',
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownApplyNum`).d('申请编码'),
        name: 'teardownApplyNum',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.materialLotCodeObj`).d('电芯条码'),
        name: 'materialLotCodeObj',
        lovCode: 'YP_MES.MES.MATERIAL_LOT',
        ignore: FieldIgnore.always,
        type: FieldType.object,
        textField:'materialLotCode',
        dynamicProps: {
          disabled: ({ dataSet, record }) => {
            return !(
              record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
            );
          },
          required: ({ dataSet, record }) => {
            return record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
          },
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.model`).d('电芯型号'),
        name: 'model',
        type: FieldType.string,
      },
      {
        name: 'materialId',
        bind: 'materialLotCodeObj.materialId',
      },
      {
        name: 'materialLotCode',
        type: FieldType.string,
        bind: 'materialLotCodeObj.materialLotCode',
      },
      {
        label: intl.get(`${modelPrompt}.table.materialCode`).d('物料编码'),
        name: 'materialCode',
        bind: 'materialLotCodeObj.materialCode',
      },
      {
        label: intl.get(`${modelPrompt}.table.materialName`).d('物料名称'),
        name: 'materialName',
        bind: 'materialLotCodeObj.materialName',
      },
      {
        name: 'siteId',
        bind: 'materialLotCodeObj.siteId',
      },
      {
        label: intl.get(`${modelPrompt}.table.siteCode`).d('站点'),
        name: 'siteCode',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.prodLineCode`).d('产线'),
        name: 'prodLineCode',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.productFormCode`).d('产品形式'),
        name: 'productFormCode',
        lookupCode: 'YP.QIS.PRODUCT_FORM',
        type: FieldType.string,
        dynamicProps: {
          disabled: ({ dataSet, record }) => {
            return !(
              record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
            );
          },
          required: ({ dataSet, record }) => {
            return record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
          },
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskType`).d('拆解类型'),
        name: 'teardownTaskType',
        lookupCode: 'YP.QIS.TEARDOWN_TASK_TYPE',
        type: FieldType.string,
        defaultValue: 'MANUAL',
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownReason`).d('拆解原因'),
        name: 'teardownReason',
        type: FieldType.string,
        dynamicProps: {
          disabled: ({ dataSet, record }) => {
            return !(
              record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
            );
          },
          required: ({ dataSet, record }) => {
            return record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
          },
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.clientName`).d('委托人'),
        name: 'clientName',
        type: FieldType.string,
      },
      {
        name: 'clientId',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.sampleDeliveryTime`).d('送样时间'),
        name: 'sampleDeliveryTime',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownPersonName`).d('拆解员'),
        name: 'teardownPersonName',
        type: FieldType.string,
        defaultValue: getCurrentUser().realName,
      },
      {
        name: 'teardownPersonId',
        defaultValue: getCurrentUser().id,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskTime`).d('拆解时间'),
        name: 'teardownTaskTime',
        type: FieldType.dateTime,
      },
      {
        label: intl.get(`${modelPrompt}.table.electricVoltage`).d('电量'),
        name: 'electricVoltage',
        type: FieldType.number,
        min: 0,
        step: 1,
        dynamicProps: {
          disabled: ({ dataSet, record }) => {
            return !(
              record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
            );
          },
          required: ({ dataSet, record }) => {
            return record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
          },
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.operationObj`).d('来源工序编码'),
        name: 'operationObj',
        type: FieldType.object,
        lovCode: 'MT.METHOD.OPERATION',
        ignore: FieldIgnore.always,
        dynamicProps: {
          disabled: ({ dataSet, record }) => {
            return !(
              record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
            );
          },
          required: ({ dataSet, record }) => {
            return record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode);
          },
        },
      },
      {
        name: 'operationId',
        bind: 'operationObj.operationId',
      },
      {
        name: 'operationName',
        bind: 'operationObj.operationName',
      },
      {
        label: intl.get(`${modelPrompt}.table.operationDesc`).d('来源工序描述'),
        name: 'operationDesc',
        bind: 'operationObj.description',
        disabled: true,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskStatus`).d('拆解任务状态'),
        name: 'teardownTaskStatus',
        lookupCode: 'YP.QIS.TEARDOWN_TASK_STATUS',
        type: FieldType.string,
        defaultValue: 'NEW',
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownStage`).d('阶段'),
        name: 'teardownStage',
        lookupCode: 'YP.QIS.NC_REPORT_STAGE',
        type: FieldType.string,
        dynamicProps: {
          disabled: ({ dataSet, record }) => {
            return !(
              record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
            );
          },
          required: ({ dataSet, record }) => {
            return record.get('teardownTaskType') === 'MANUAL' &&
              ['NEW', 'SPILTING'].includes(record.get('teardownTaskStatus')) &&
              dataSet.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode)
          },
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskScore`).d('拆解评分'),
        name: 'teardownTaskScore',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskResult`).d('审核结果'),
        name: 'teardownTaskResult',
        lookupCode: 'YP.QIS_TASK_REVIEW_RESULT',
        type: FieldType.string,
        dynamicProps: {
          disabled: ({ dataSet, record }) => !(
            dataSet.getState('reviewRoles')?.includes(getCurrentUser().currentRoleCode) &&
            record.get('teardownTaskStatus') === 'REVIEWING'
          ),
          required: ({ dataSet, record }) =>
            dataSet.getState('reviewRoles')?.includes(getCurrentUser().currentRoleCode) &&
            record.get('teardownTaskStatus') === 'REVIEWING',
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.judgeReason`).d('判断理由'),
        name: 'judgeReason',
        type: FieldType.string,
        dynamicProps: {
          disabled: ({ dataSet, record }) => !(
            dataSet.getState('reviewRoles')?.includes(getCurrentUser().currentRoleCode) &&
            record.get('teardownTaskStatus') === 'REVIEWING'
          ),
          required: ({ dataSet, record }) =>
            dataSet.getState('reviewRoles')?.includes(getCurrentUser().currentRoleCode) &&
            record.get('teardownTaskStatus') === 'REVIEWING' && record?.get('teardownTaskResult') === 'NG',
        },
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownReviewedName`).d('审核人'),
        name: 'teardownReviewedName',
        type: FieldType.string,
      },
      {
        name: 'teardownReviewedBy',
        type: FieldType.number,
      },
      {
        label: intl.get(`${modelPrompt}.table.cancelReason`).d('取消原因'),
        name: 'cancelReason',
        type: FieldType.string,
        disabled: true,
      },
    ],
  });

export default basicPageFactry;