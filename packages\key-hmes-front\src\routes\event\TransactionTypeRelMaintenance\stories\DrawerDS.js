import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.event.transaction.type.rel';

const tenantId = getCurrentOrganizationId();

const drawerDS = () => ({
  primaryKey: 'eventTransTypeRelId',
  autoCreate: true,
  fields: [
    {
      name: 'eventTypeCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型'),
      lovCode: `${BASIC.WMS_LOV_CODE_BEFORE}.EVENT_TYPE`,
      lovPara: {
        tenantId,
      },
      textField: 'eventTypeCode',
      valueField: 'eventTypeId',
      required: true,
    },
    {
      name: 'eventTypeId',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('事件类型描述'),
      disabled: true,
    },
    {
      name: 'businessTypeCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessTypeCode`).d('业务类型'),
      lovCode: 'APEX_WMS.COMMON.BUSINESS_TYPE',
      lovPara: {
        tenantId,
      },
      textField: 'typeCode',
      valueField: 'genTypeId',
    },
    {
      name: 'genTypeId',
    },
    {
      name: 'businessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessTypeDesc`).d('业务类型描述'),
    },
    {
      name: 'transCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transCategory`).d('事务类别'),
      required: true,
      lovPara: { tenantId },
      lookupCode: 'APEX_WMS.MES.TRANS_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'erpSystemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.erpSystemType`).d('ERP系统名称'),
      required: true,
      lovPara: { tenantId },
      lookupCode: 'APEX_WMS.EVENT_TRANS_ERP_NAME',
      textField: 'value',
      valueField: 'value',
    },
    {
      name: 'transTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transTypeCode`).d('ERP事务类型'),
      required: true,
    },
    {
      name: 'transCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transCode`).d('SAP事务代码'),
      lookupCode: 'APEX_WMS.EVENT_TRANS_ERP_TRANS_CODE',
      textField: 'value',
      valueField: 'value',
      lovPara: { tenantId },
      dynamicProps: ({ record }) => {
        return {
          disabled: record.get('erpSystemType') === 'EBS',
        };
      },
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialFlag`).d('初始化标识'),
      textField: 'meaning',
      valueField: 'value',
      lovPara: { tenantId },
      lookupCode: 'APEX_WMS.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      initialValue: 'N',
      disabled: true,
    },
    {
      name: 'transferMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferMethod`).d('事务传输方式'),
      required: true,
      lovPara: { tenantId },
      lookupCode: 'APEX_WMS.MES.TRANSFER_METHOD',
    },
  ],
  transport: {},
});

export { drawerDS };
