.lov-modal {
  word-break: break-all;

  .ant-modal-title {
    font-size: 18px;
  }

  .ant-modal-header {
    padding: 16px 16px 0;
    border-bottom: none;
  }

  .ant-modal-footer {
    border-top: none;
    padding: 0 16px 16px;
    margin-top: 16px;
  }

  .ant-form-item {
    display: flex;
    margin-bottom: 0;
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .lov-modal-btn-container {
    flex-shrink: 0;
    display: flex;
    height: 40px;
    align-items: center;
  }
}

.lov-input {
  .ant-input-group-wrapper {
    vertical-align: middle;
  }

  .ant-input {
    // 两个 icon 的位置
    padding-right: 48px !important;
    transition: padding 0s, background-color 0.3s, border 0.3s;
  }

  .ant-input-suffix {
    > .lov-clear {
      margin-right: 4px;
      display: none;
    }

    // LOV 查询icon颜色和DatePicker等组件的icon颜色一样
    .anticon-search:before {
      color: rgba(0, 0, 0, 0.25);
    }
  }
}

.lov-suffix {
  .ant-input-suffix {
    > .anticon {
      // ICON
    }

    > .lov-clear {
      display: none;
      cursor: pointer;
    }
  }

  &:hover {
    .ant-input-suffix {
      > .lov-clear {
        display: inline-block;
      }
    }
  }
}

.ant-form-item-required .lov-suffix {
  &:hover {
    .ant-input-suffix {
      display: block;
    }
  }
}

.cascade-lov-wrapper {
  border: 1px solid #eee;
  .cascade-lov-title {
    display: flex;
    border-bottom: 1px solid #eee;
    font-weight: 700;
    background-color: #f5f5f5;
    div {
      flex: 1;
      border-right: 1px solid #eee;
      padding: 5px;
    }
    div:last-of-type {
      border-right: none;
    }
  }
  .cascade-lov-content {
    display: flex;
    height: 450px;
    overflow: hidden;
    div {
      flex: 1;
      border-right: 1px solid #eee;
      padding: 5px;
      overflow: auto;
    }
    div:last-of-type {
      border-right: none;
    }
  }
}
