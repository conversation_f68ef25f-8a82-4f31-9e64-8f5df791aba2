/*
 * @Description: 输入工位谈框
 * @Version: 0.0.1
 * @Autor: <EMAIL>
 * @Date: 2020-06-19 15:37:20
 */
import React, { Component } from 'react';
import { Modal, Form, Spin, Button, Row, Col, Input } from 'hzero-ui';
import { Bind } from 'lodash-decorators';
import intl from 'utils/intl';
import Lov from 'components/Lov';
import { closeTab } from 'utils/menuTab';
import styles from '../index.less';

const prefixModel = `hmes.operationPlatform.model.operationPlatform`;

@Form.create({ fieldNameProp: null })
export default class EnterModal extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  // 输入工位并回车
  @Bind()
  enterSite() {
    const { enterSite, form } = this.props;
    if (enterSite) {
      form.validateFields((err, values) => {
        if (!err) {
          // 如果验证成功,则执行enterSite
          enterSite(values);
        }
      });
    }
  }

  // 关闭输入框
  @Bind()
  handleCloseTab() {
    const { workcellId } = this.props;
    closeTab(`/hmes/exception-handling-platform/${workcellId}`);
  }

  render() {
    const {
      form: { getFieldDecorator, setFieldsValue },
      loading,
      visible,
      enterModalLoading, // 工位输入请求状态
      tenantId,
      localSaveWorkCell,
    } = this.props;
    const DRAWER_FORM_ITEM_LAYOUT_MAX = {
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 18,
      },
    };
    return (
      <Modal
        destroyOnClose
        width={400}
        title={intl.get('hmes.operationPlatform.view.message.title').d('异常处理平台')}
        visible={visible}
        footer={[
          <Button
            key="yes"
            style={{ marginLeft: '5px' }}
            type="primary"
            onClick={() => this.enterSite()}
            loading={enterModalLoading}
          >
            确认
          </Button>,
          <Button key="no" style={{ marginLeft: '5px' }} onClick={() => this.handleCloseTab()}>
            取消
          </Button>,
        ]}
        onCancel={this.handleCloseTab}
        wrapClassName={styles['enter-modal']}
      >
        <Spin spinning={loading || enterModalLoading}>
          <Form>
            <Row>
              <Col span={20}>
                <Form.Item {...DRAWER_FORM_ITEM_LAYOUT_MAX} label="工作单元">
                  {getFieldDecorator('workcellId', {
                    rules: [
                      {
                        required: true,
                        message: intl.get('hzero.common.validation.notNull', {
                          name: intl.get(`${prefixModel}.workcellId`).d('工作单元'),
                        }),
                      },
                    ],
                  })(
                    <Lov
                      code="HME.USER_WORKCELL"
                      disabled={enterModalLoading}
                      placeholder="请选择工位"
                      queryParams={{ tenantId }}
                      onChange={(value, ele) => {
                        localSaveWorkCell(ele);
                        setFieldsValue({
                          taskOrderId: null,
                          workcellCode: ele.workcellCode,
                        });
                      }}
                    />
                  )}
                </Form.Item>
                <Form.Item style={{ display: 'none' }}>
                  {getFieldDecorator('workcellCode', {})(<Input disabled />)}
                </Form.Item>
              </Col>
            </Row>
            {/* <Row>
              <Col span={20}>
                <Form.Item {...DRAWER_FORM_ITEM_LAYOUT_MAX} label="指令单">
                  {getFieldDecorator('taskOrderId', {
                    rules: [
                      {
                        required: true,
                        message: intl.get('hzero.common.validation.notNull', {
                          name: intl.get(`${prefixModel}.taskOrderId`).d('指令单'),
                        }),
                      },
                    ],
                  })(
                    <Lov
                      code="HME.TASK_ORDER_NUM"
                      disabled={!getFieldValue('workcellId')}
                      placeholder="请选择指令单"
                      queryParams={{ tenantId, workcellId: getFieldValue('workcellId') }}
                      onChange={(value, ele) => {
                        setFieldsValue({
                          taskOrderNum: ele.taskOrderNum,
                        });
                      }}
                    />
                  )}
                </Form.Item>
                <Form.Item style={{ display: 'none' }}>
                  {getFieldDecorator('taskOrderNum', {})(<Input disabled />)}
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item>
                  <Button
                    style={{
                      display: 'none',
                    }}
                    htmlType="submit"
                    onClick={() => this.enterSite()}
                  />
                </Form.Item>
              </Col>
            </Row> */}
          </Form>
        </Spin>
      </Modal>
    );
  }
}
