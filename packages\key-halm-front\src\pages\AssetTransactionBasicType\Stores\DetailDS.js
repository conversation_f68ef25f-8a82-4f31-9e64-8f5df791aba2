/**
 * 五种资产事务+领用单
 * @since 2020-03-10
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2020, Hand
 */
import { isNull, isUndefined, isNil } from 'lodash';
import intl from 'utils/intl';
import { setDSFields } from 'alm/utils/dynamicFieldRender';
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { isJSON } from 'alm/utils/utils';

const organizationId = getCurrentOrganizationId();
const promptCode = 'aatn.assetTransactionBasicType.model.assetTransactionBasicType';

/**
 * 设置附加字段面板的弹性字段
 * @param data
 */
function handleSetExtraFieldsLine(dataSet, syncFlag) {
  const lineDynamicProps = [];
  dataSet.forEach((i, index) => {
    const dynamicFields = setDSFields(
      i.get('attrField'),
      'fieldName',
      'aliasFieldCode',
      'typeCode',
      'value',
      '',
      undefined,
      undefined,
      undefined,
      {
        handleOrgPartnerLovOk: (name, meaning, record) =>
          handleOrgPartnerLovOk(dataSet, name, meaning, record),
      }
    );
    const { resultData } = dynamicFields;
    if (index === 0) {
      dynamicFields.dsFields.forEach(d => dataSet.addField(d.name, d));
    }
    if (syncFlag) {
      i.init({ dynamicFields, ...i.toData(), ...resultData });
    } else {
      i.set({ dynamicFields, ...i.toData(), ...resultData });
    }
    lineDynamicProps.push(i.toData());
  });
  // eslint-disable-next-line no-param-reassign
  dataSet.lineDynamicProps = lineDynamicProps;
}

function handleOrgPartnerLovOk(dataSet, name, meaning, record) {
  dataSet.current.set(name, record.value);
  dataSet.current.set(meaning, record.meaning);
}

/**
 * 获取行模板
 */
function getTemplateLineDS() {
  return {
    autoQuery: false,
    dataKey: 'content',
    transport: {
      read: ({ params, data }) => {
        const url = `${HALM_ATN}/v1/${organizationId}/tp-type-fields/change-template-line/${data.transactionTypeId}`;
        return {
          data: { ...data, organizationId },
          params,
          url,
          method: 'GET',
        };
      },
    },
  };
}

/**
 * 获取事务类型的详情
 */
function typeDS() {
  return {
    autoQuery: false,
    fields: [
      {
        name: 'transactionTypeId',
        type: 'number',
      },
      {
        name: 'codeRule',
        type: 'string',
        label: intl.get(`${promptCode}.codeRuleName`).d('编号规则'),
      },
      {
        name: 'workflowOptionCode',
        type: 'string',
        label: intl.get(`${promptCode}.workflowOptionCode`).d('启用工作流选项'),
      },
      {
        name: 'procdefKey',
        type: 'string',
        label: intl.get(`${promptCode}.procdefKey`).d('流程名称'),
      },
    ],
    transport: {
      read: config => {
        const { params } = config;
        let { data } = config;
        data = {
          organizationId,
          ...data,
        };
        const url = `${HALM_ATN}/v1/${organizationId}/transaction-type/${data.id}`;
        return {
          data,
          params,
          url,
          method: 'GET',
        };
      },
    },
  };
}

/**
 * 获取事务id的目标状态信息
 */
function getTransactionTargetStatusDS() {
  return {
    autoQuery: false,
    dataKey: 'content',
    fields: [],
    transport: {
      read: config => {
        const { params } = config;
        let { data } = config;
        data = {
          organizationId,
          ...data,
        };
        const url = `${HALM_ATN}/v1/${organizationId}/transaction-type/getStatusRule/${data.id}`;
        return {
          data,
          params,
          url,
          method: 'GET',
        };
      },
    },
  };
}

/**
 * 新建、详情表单数据区域
 */
function detailDS(fields) {
  return {
    selection: false,
    autoCreate: true,
    autoQuery: false,
    fields: [
      {
        name: 'changeNum',
        type: 'string',
        label: intl.get(`${promptCode}.changeNum`).d('事务处理单编号'),
        maxLength: 40,
        dynamicProps: {
          disabled: ({ record }) => {
            return !isNull(record.get('codeRule')) && !isUndefined(record.get('codeRule'));
          },
          required: ({ record }) => {
            return isNull(record.get('codeRule')) || isUndefined(record.get('codeRule'));
          },
        },
      },
      {
        name: 'titleOverview',
        type: 'string',
        label: intl.get(`${promptCode}.titleOverview`).d('标题概述'),
        maxLength: 40,
        required: true,
      },
      {
        name: 'transactionTypeLov',
        type: 'object',
        label: intl.get(`${promptCode}.transactionType`).d('资产事务类型'),
        lovCode: 'AAFM.TRANSACTION_TYPES',
        lovPara: {
          enabledFlag: 1,
          tenantId: organizationId,
        },
        ignore: 'always',
        disabled: true,
        required: true,
      },
      {
        name: 'transactionTypeId',
        type: 'number',
        bind: 'transactionTypeLov.transactionTypeId',
      },
      {
        name: 'transactionTypeIdMeaning',
        type: 'string',
        label: intl.get(`${promptCode}.transactionType`).d('资产事务类型'),
        bind: 'transactionTypeLov.transactionTypeName',
      },
      {
        name: 'processStatus',
        type: 'string',
        defaultValue: 'DRAFT',
        label: intl.get(`${promptCode}.processStatus`).d('处理状态'),
        lookupCode: 'HALM.APPROVE_STATUS',
        disabled: true,
      },
      {
        name: 'processStatusMeaning',
        type: 'string',
        label: intl.get(`${promptCode}.processStatus`).d('处理状态'),
      },
      {
        name: 'principalPersonLov',
        type: 'object',
        lovCode: 'HALM.EMPLOYEE',
        required: true,
        lovPara: {
          tenantId: organizationId,
        },
        ignore: 'always',
        dynamicProps: {
          label: ({ dataSet }) => {
            const basicCode = dataSet?.getState('basicCode');
            if (basicCode === 'SCRAP') {
              return intl.get(`${promptCode}.scrapPrincipalPerson`).d('申请人');
            } else {
              return intl.get(`${promptCode}.principalPerson`).d('负责人');
            }
          },
        },
      },
      {
        name: 'principalPersonId',
        type: 'number',
        bind: 'principalPersonLov.employeeId',
      },
      {
        name: 'principalPersonIdMeaning',
        type: 'string',
        label: intl.get(`${promptCode}.principalPerson`).d('负责人'),
        bind: 'principalPersonLov.employeeName',
      },
      {
        name: 'disposalMethod',
        type: 'string',
        label: intl.get(`${promptCode}.disposalMethod`).d('处置方式'),
        lookupCode: 'HALM.DISPOSAL_METHOD',
      },
      {
        name: 'typeOfExpense',
        type: 'string',
        label: intl.get(`${promptCode}.typeOfExpense`).d('费用类型'),
        lookupCode: 'HALM.TYPE_OF_EXPENSE',
      },
      {
        name: 'disposalMoney',
        type: 'number',
        label: intl.get(`${promptCode}.disposalMoney`).d('处置金额'),
        min: 0,
      },
      {
        name: 'currencyLov',
        type: 'object',
        lovCode: 'HALM.CURRENCY',
        lovPara: { organizationId },
        label: intl.get(`${promptCode}.currency`).d('币种'),
        noCache: true,
        ignore: 'always',
      },
      {
        name: 'currencyCode',
        type: 'string',
        bind: 'currencyLov.currencyCode',
      },
      {
        name: 'currencyCodeMeaning',
        type: 'string',
        bind: 'currencyLov.currencyName',
        label: '币种',
      },
      {
        name: 'applyPersonLov',
        type: 'object',
        lovCode: 'HALM.EMPLOYEE',
        label: '申请人员',
        noCache: true,
        ignore: 'always',
        dynamicProps: {
          required: ({ dataSet }) => dataSet?.getState('basicCode') === 'IDLE',
        },
        lovPara: {
          tenantId: organizationId,
        },
      },
      {
        name: 'applyPersonId',
        type: 'number',
        bind: 'applyPersonLov.employeeId',
      },
      {
        name: 'applyPersonName',
        type: 'string',
        bind: 'applyPersonLov.employeeName',
      },
      {
        name: 'usingOrgName',
        type: 'string',
        required: true,
        label: intl.get(`${promptCode}.usingOrg`).d('申请人部门'),
      },
      {
        name: 'applicantCompany',
        type: 'object',
        label: intl.get(`${promptCode}.applicantCompany`).d('申请人公司'),
        lovCode: 'APEX.ALM_UNIT',
        lovPara:{
          tenantId: organizationId,
        },
        ignore: 'always',
        textField: 'unitCompanyName',
      },
      {
        name: 'unitCompanyId',
        bind: 'applicantCompany.unitCompanyId',
      },
      {
        name: 'unitCompanyName',
        bind: 'applicantCompany.unitCompanyName',
      },
      {
        name: 'idleDate',
        type: 'date',
        label: intl.get(`${promptCode}.idleDate`).d('闲置日期'),
      },
      {
        name: 'idleReason',
        type: 'string',
        lookupCode: 'HALM.IDLE_REASON',
        label: intl.get(`${promptCode}.idleReason`).d('闲置原因'),
      },
      {
        name: 'scrapReason',
        type: 'string',
        label: intl.get(`${promptCode}.scrapReason`).d('报废原因'),
        maxLength: 100,
      },
      ...fields,
    ],
    dataToJSON: 'all',
    transport: {
      read: config => {
        const { params } = config;
        let { data } = config;
        data = {
          organizationId,
          ...data,
        };
        const url = `${HALM_ATN}/v1/${organizationId}/tp-change-headers/${data.changeHeaderId}`;
        return {
          data,
          params,
          url,
          method: 'GET',
        };
      },
      submit: ({ params, data }) => {
        const temp = data[0];
        const url = `${HALM_ATN}/v1/${organizationId}/tp-change-headers`;
        return {
          url,
          data: { ...temp, headerAndLineSaveFlag: isNil(temp?.changeHeaderId) ? 1 : 0 },
          params,
          method: isNil(temp?.changeHeaderId) ? 'POST' : 'PUT',
        };
      },
    },
  };
}

/**
 * 事务行信息表格
 */
function detailLineTableDS(fields) {
  return {
    selection: false,
    autoQuery: false,
    pageSize: 10,
    dataKey: 'content',
    modifiedCheck: false,
    fields: [
      {
        name: 'lineNum',
        type: 'string',
        label: intl.get(`${promptCode}.lineNum`).d('编号'),
      },
      {
        name: 'objectNum',
        type: 'string',
        computedProps: {
          label: ({ dataSet }) => {
            return dataSet?.getState('basicCode') === 'ACQUISITION'
              ? intl.get(`${promptCode}.objectNum`).d('对象编号')
              : intl.get(`${promptCode}.assetNum`).d('资产设备编号');
          },
        },
      },
      {
        name: 'objectDesc',
        type: 'string',
        computedProps: {
          label: ({ dataSet }) => {
            return dataSet?.getState('basicCode') === 'ACQUISITION'
              ? intl.get(`${promptCode}.objectName`).d('对象名称')
              : intl.get(`${promptCode}.assetDesc`).d('资产全称');
          },
        },
      },
      {
        name: 'typeOfExpense',
        type: 'string',
        label: intl.get(`${promptCode}.typeOfExpense`).d('费用类型'),
        lookupCode: 'HALM.TYPE_OF_EXPENSE',
      },
      {
        name: 'disposalMoney',
        type: 'number',
        label: intl.get(`${promptCode}.disposalMoney`).d('处置金额'),
        min: 0,
      },
      {
        name: 'disposalMethod',
        type: 'string',
        label: intl.get(`${promptCode}.disposalMethod`).d('处置方式'),
        lookupCode: 'HALM.DISPOSAL_METHOD',
      },
      // 行上没有显示该字段
      {
        name: 'applyPersonLov',
        type: 'object',
        lovCode: 'HALM.EMPLOYEE',
        label: intl.get(`${promptCode}.applyPerson`).d('申请人员'),
        noCache: true,
        ignore: 'always',
        lovPara: {
          tenantId: organizationId,
        },
      },
      {
        name: 'applyPersonId',
        type: 'number',
        bind: 'applyPersonLov.employeeId',
      },
      {
        name: 'applyPersonName',
        type: 'string',
        bind: 'applyPersonLov.employeeName',
      },
      {
        name: 'idleDate',
        type: 'date',
        label: intl.get(`${promptCode}.idleDate`).d('闲置日期'),
      },
      {
        name: 'idleReason',
        type: 'string',
        lookupCode: 'HALM.IDLE_REASON',
        label: intl.get(`${promptCode}.idleReason`).d('闲置原因'),
      },
      {
        name: 'requisitionsNumber',
        type: 'number',
        label: intl.get(`${promptCode}.needQuantity`).d('需求数量'),
        defaultValue: 1,
        required: true,
        maxLength: 20,
        dynamicProps: {
          required: ({ dataSet, record }) => {
            const typeData = dataSet.getState('typeData');
            const objectType = record.get('objectType');
            return typeData?.basicTypeCode === 'ACQUISITION' && objectType !== 'ASSET';
          },
        },
      },
      {
        label: intl.get(`${promptCode}.receiptObjType`).d('领用对象类型'),
        name: 'objectType',
        lookupCode: 'HALM.RECIPIENTS',
      },
      {
        label: intl.get(`${promptCode}.demandDate`).d('需求日期'),
        name: 'demandDate',
        type: 'date',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${promptCode}.description`).d('变更内容'),
      },
      {
        name: 'processStatus',
        type: 'string',
        lookupCode: 'AATN.RETURN_HLINE_STATUS',
        label: intl.get(`${promptCode}.processStatus`).d('处理状态'),
      },
      {
        name: 'processStatusMeaning',
        type: 'string',
        label: intl.get(`${promptCode}.processStatus`).d('处理状态'),
      },
      {
        name: 'changeLineId',
        type: 'number',
        label: intl.get(`${promptCode}.changeLineId`).d('id'),
      },
      {
        name: 'oldAssetStatus',
        type: 'object',
        label: intl.get(`${promptCode}.oldAssetStatus`).d('原资产状态'),
        lovCode: 'AAFM.ASSET_STATUS',
        ignore: 'always',
      },
      {
        name: 'oldObjectStatusId',
        type: 'number',
        bind: 'oldAssetStatus.assetStatusId',
      },
      {
        name: 'oldAssetStatusName',
        type: 'string',
        label: intl.get(`${promptCode}.oldAssetStatus`).d('原资产状态'),
        bind: 'oldAssetStatus.assetStatusName',
      },
      {
        name: 'newAssetStatus',
        type: 'object',
        lovCode: 'AAFM.ASSET_STATUS',
        lovPara: {},
        label: intl.get(`${promptCode}.newAssetStatus`).d('目标资产状态'),
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ dataSet }) => {
            const typeData = dataSet.getState('typeData');
            const jsonFlag = isJSON(typeData?.targetAssetStatusScope);
            const tmpScope = jsonFlag
              ? JSON.parse(typeData?.targetAssetStatusScope).join(',')
              : typeData?.targetAssetStatusScope;
            return {
              targetAssetStatusScope: tmpScope,
            };
          },
        },
      },
      {
        name: 'newObjectStatusId',
        type: 'number',
        bind: 'newAssetStatus.assetStatusId',
      },
      {
        name: 'newAssetStatusName',
        type: 'string',
        label: intl.get(`${promptCode}.newAssetStatus`).d('目标资产状态'),
        bind: 'newAssetStatus.assetStatusName',
      },
      {
        name: 'financialNum',
        type: 'string',
        label: intl.get(`${promptCode}.financialNum`).d('固定资产编号'),
      },
      {
        name: 'attributeValue', // 属性组
        type: 'string',
      },
      ...fields,
    ],
    events: {
      load: ({ dataSet }) => {
        handleSetExtraFieldsLine(dataSet, true);
      },
    },
    transport: {
      read: ({ dataSet, data, params }) => {
        const url = `${HALM_ATN}/v1/${organizationId}/tp-change-lines/list/${dataSet.getState(
          'changeHeaderId'
        )}`;
        return {
          data: {
            ...data,
            organizationId,
          },
          params,
          url,
          method: 'GET',
        };
      },
      destroy: ({ data, params, dataSet }) => {
        const temp = data[0];
        const { lineId } = temp;
        const headData = dataSet.getState('headData') ?? {};
        return {
          url: `${HALM_ATN}/v1/${organizationId}/tp-change-lines/${lineId}`,
          data: headData,
          params,
          method: 'DELETE',
        };
      },
    },
  };
}

function dynamicFieldsConfDS() {
  return {
    transport: {
      read: ({ data, params }) => {
        const { taskId } = data;
        const url = taskId
          ? `${HALM_ATN}/v1/${organizationId}/field-confs/tp/auto`
          : `${HALM_ATN}/v1/${organizationId}/field-confs`;
        return {
          data,
          params,
          url,
          method: 'GET',
        };
      },
    },
  };
}

export {
  typeDS,
  getTemplateLineDS,
  getTransactionTargetStatusDS,
  detailDS,
  detailLineTableDS,
  dynamicFieldsConfDS,
  handleSetExtraFieldsLine,
};
