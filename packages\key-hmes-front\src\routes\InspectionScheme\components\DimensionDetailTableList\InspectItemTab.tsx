/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-02-02 15:18:39
 * @LastEditTime: 2023-06-13 15:42:45
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import uuid from 'uuid/v4';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { DataSet, Table, Modal, Lov, Icon, Button } from 'choerodon-ui/pro';
import { Badge, Popconfirm, Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { drawerPropsC7n } from '@components/tarzan-ui';
import notification from 'utils/notification';
import { ColumnAlign, ColumnLock, DragColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import InspectItemInfoDrawer from './InspectItemInfoDrawer';
import { NumberDS, batchDS, formulaListTableDS, DetailTableDS } from '../stores';
import { isJSONString } from '@/utils';

const modelPrompt = 'tarzan.qms.inspectGroupMaintenance';
const tenantId = getCurrentOrganizationId();

const _ignoreKeys = [
  'creationDate',
  'createdBy',
  'lastUpdateDate',
  'lastUpdatedBy',
  'objectVersionNumber',
  '_token',
  'inspectGroupItemId',
  'sequence',
];

const InspectItemTab = props => {
  const {
    path,
    tableDS,
    formDs,
    canEdit,
    statisticsTableUpdate,
    getNewUuid,
    handleCreateInspection,
    dimensionsItem,
    item,
    selectOptionValue,
  } = props;

  // 数值类型-预警值DS
  const warnNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  // 数值类型-符合值DS
  const trueNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  // 数值类型-不符合值DS
  const falseNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  //  单独编辑表单
  const editTableFormDS = useMemo(() => new DataSet(DetailTableDS({ drawer: true })), []);
  // 公式参数列表
  const formulaListTableDs = useMemo(() => new DataSet(formulaListTableDS()), []);

  // 批量增加检验项目ds
  const batchDs = useMemo(() => new DataSet({ ...batchDS() }), []);

  // 行删除
  const handleDelete = deleteRecord => {
    const deleteId = deleteRecord?.get('inspectItemId');
    const deleteRecordList: any = [];
    tableDS.forEach(record => {
      const recordData = record.toData();
      const {
        dataType,
        formulaSourceId,
        formulaMode,
        formulaDisplayPosition,
        formulaId,
        formulaCode,
        formulaName,
        dimension,
        formulaList,
      } = recordData;
      if (formulaSourceId === deleteId) {
        deleteRecordList.push(record);
      }
      if (dataType === 'CALCULATE_FORMULA') {
        if (formulaList?.length > 0) {
          const newFormulaList = formulaList.map(item => {
            return {
              ...item,
              // @ts-ignore
              inspectItemId: deleteId === item.inspectItemId ? null : item.inspectItemId,
              // @ts-ignore
              inspectItemDesc: deleteId === item.inspectItemId ? null : item.inspectItemDesc,
              inspectItemObject: {
                value: deleteId === item.inspectItemId ? null : item.inspectItemId,
                meaning: deleteId === item.inspectItemId ? null : item.inspectItemDesc,
              },
            };
          });

          record.set(
            'formula',
            JSON.stringify({
              formulaSourceId,
              formulaMode,
              formulaDisplayPosition,
              formulaId,
              formulaCode,
              formulaName,
              dimension,
              formulaList: newFormulaList,
            }),
          );

          record.set('formulaList', newFormulaList);
        }
      }
    });
    tableDS.delete([deleteRecord, ...deleteRecordList], false);
    setTimeout(() => {
      handleDragEnd();
    }, 10);

    statisticsTableUpdate();
  };

  const handleAdd = value => {
    if (!value) {
      return;
    }
    const inspectionItemBasisDsData = formDs.toData()[0];

    // 已有数据转为对象进行对比
    const inDsCodeMap: Array<any> = [];
    let itemsSimpleIndex = 0;

    let sequence = 1;
    tableDS.forEach((recordItem, index) => {
      const recordItemData = recordItem.toData();
      inDsCodeMap.push(recordItemData.inspectItemCode);
      if (recordItemData?.sequence > sequence) {
        sequence = recordItemData.sequence;
      }
      if (recordItemData.dataType !== 'CALCULATE_FORMULA' && index > itemsSimpleIndex) {
        itemsSimpleIndex = index;
      }
    });
    sequence = (Math.floor(sequence / 10) + 1) * 10;

    // 取得的value值还需要再处理
    value.forEach(valueItem => {
      const _valueItem = { ...valueItem };
      if (_valueItem.creationDate) {
        delete _valueItem.creationDate;
      }

      _valueItem.requiredFlag = _valueItem.requiredFlag || 'Y';
      _valueItem.destructiveExperimentFlag = _valueItem.destructiveExperimentFlag || 'N';
      _valueItem.outsourceFlag = _valueItem.outsourceFlag || 'N';
      _valueItem.samplingMethodId = _valueItem.samplingMethodId
        ? _valueItem.samplingMethodId
        : null;

      const formula = isJSONString(_valueItem.formula || '');
      if (formula) {
        const {
          formulaSourceId,
          formulaMode,
          formulaDisplayPosition,
          formulaId,
          formulaCode,
          formulaName,
          dimension,
          formulaList,
        } = formula;
        _valueItem.formulaSourceId = formulaSourceId;
        _valueItem.formulaMode = formulaMode;
        _valueItem.formulaDisplayPosition = formulaDisplayPosition;
        _valueItem.formulaId = formulaId;
        _valueItem.formulaCode = formulaCode;
        _valueItem.formulaName = formulaName;
        _valueItem.dimension = dimension;
        _valueItem.formulaList = formulaList;
      } else {
        _valueItem.formulaSourceId = null;
        _valueItem.formulaMode = null;
        _valueItem.formulaDisplayPosition = null;
        _valueItem.formulaId = null;
        _valueItem.formulaCode = null;
        _valueItem.formulaName = null;
        _valueItem.dimension = null;
        _valueItem.formulaList = null;
      }

      if (['TEXT', 'DECISION_VALUE'].includes(_valueItem.dataType)) {
        _valueItem.trueValue =
          (_valueItem.trueValueList || []).length > 0
            ? _valueItem.trueValueList[0].dataValue
            : null;
        _valueItem.falseValue =
          (_valueItem.falseValueList || []).length > 0
            ? _valueItem.falseValueList[0].dataValue
            : null;
      }
      if (_valueItem.dataType === 'VALUE_LIST') {
        _valueItem.trueValue =
          (_valueItem.trueValueList || []).length > 0
            ? _valueItem.trueValueList.map(trueItem => trueItem.dataValue)
            : null;
        _valueItem.falseValue =
          (_valueItem.falseValueList || []).length > 0
            ? _valueItem.falseValueList.map(falseItem => falseItem.dataValue)
            : null;
      }
      if (inDsCodeMap.indexOf(valueItem.inspectItemCode) === -1) {
        const newRecord = tableDS.create(
          {
            ..._valueItem,
            inspectionItemRowUuid: uuid(),
            sequence,
          },
          itemsSimpleIndex + 1,
        );
        itemsSimpleIndex++;

        if (!newRecord?.get('samplingMethodId') && inspectionItemBasisDsData.samplingMethodId) {
          newRecord.init('samplingMethodCode', inspectionItemBasisDsData.samplingMethodCode);
          newRecord.init('samplingMethodDesc', inspectionItemBasisDsData.samplingMethodDesc);
          newRecord.init('samplingMethodId', inspectionItemBasisDsData.samplingMethodId);
        }
        newRecord.init('samplingDimension', inspectionItemBasisDsData.samplingDimension);

        sequence += 10;
        getNewUuid(newRecord);
        // @ts-ignore
        batchDs.current.set('inspectionItemObject', null);
      }
    });
    // @ts-ignore
    notification.success();
    statisticsTableUpdate();
  };

  // 行新增
  const handleEdit = record => {
    const recordData = record.toData();
    editTableFormDS.loadData([{ ...recordData, isNewRow: false }]);

    const inspectItemDrawerProps = {
      canEdit,
      _ignoreKeys,
      tenantId,
      warnNumberDS,
      trueNumberDS,
      falseNumberDS,
      editTableFormDS,
      formDs,
      tableDS,
      formulaListTableDs,
      statisticsTableUpdate,
    };

    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      destroyOnClose: true,
      key: Modal.key(),
      title: intl?.get(`${modelPrompt}.title.itemEdit`).d('编辑检验项目'),
      drawer: true,
      style: {
        width: '1080px',
      },
      className: 'hmes-style-modal',
      children: <InspectItemInfoDrawer {...inspectItemDrawerProps} />,
      onOk: () => {
        return handleInspectItemDrawerSubmit();
      },
      footer: (okBtn, cancelBtn) => {
        return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
      },
    });
  };

  // 确定保存检验项目
  const handleInspectItemDrawerSubmit = async () => {
    // 检验项目字段校验
    let validateResult;
    const record = editTableFormDS.current;

    if (!record) {
      return false;
    }

    let validValueTypeFlag = true;
    // 数值类型的符合值、不符合值必输校验
    const dataType = record?.get('dataType');

    validateResult = await formulaListTableDs.validate();
    if (!validateResult) {
      return false;
    }
    validateResult = await editTableFormDS.validate();
    if (!validateResult) {
      return false;
    }

    if (['CALCULATE_FORMULA', 'VALUE'].includes(dataType)) {
      if (trueNumberDS.length < 1 && falseNumberDS.length < 1) {
        notification.error({
          message: intl
            ?.get(`${modelPrompt}.message.inputTrueOrFalseValue`)
            .d('请输入符合值或不符合值'),
        });
        validValueTypeFlag = false;
      } else {
        const _targeDS = trueNumberDS.length > 0 ? trueNumberDS : falseNumberDS;
        const _newData = _targeDS.records.filter(
          item =>
            item?.get('singleValued') ||
            (item?.get('multiValued') &&
              (item?.get('multiValued')?.start || item?.get('multiValued')?.end)),
        );
        if (_newData.length < 1) {
          notification.error({
            message:
              trueNumberDS.length > 0
                ? intl?.get(`${modelPrompt}.message.inputTrueValue`).d('请输入符合值')
                : intl?.get(`${modelPrompt}.message.inputFalseValue`).d('请输入不符合值'),
          });
          validValueTypeFlag = false;
        }
      }
    }
    if (!validValueTypeFlag) {
      return false;
    }

    const { sequence, inspectItemId, inspectGroupItemId } = record.toData();
    const sameSerialNumberFlag = tableDS
      .toData()
      .some(item => item.sequence === sequence && item.inspectGroupItemId !== inspectGroupItemId);
    const sameInspectItemFlag = tableDS
      .toData()
      .some(
        item =>
          item.inspectItemId === inspectItemId && item.inspectGroupItemId !== inspectGroupItemId,
      );
    if (sameSerialNumberFlag) {
      notification.error({
        message: intl?.get(`${modelPrompt}.message.sameSerialNumber`).d('当前序号已存在'),
      });
      return false;
    }
    if (sameInspectItemFlag) {
      notification.error({
        message: intl?.get(`${modelPrompt}.message.sameInspectItemFlag`).d('当前检验项目已选用'),
      });
      return false;
    }

    let _trueValueList: any = [];
    let _falseValueList: any = [];
    let _warningValueList: any = [];

    // 如果数据类型为数值类型，需要做赋值处理
    if (['CALCULATE_FORMULA', 'VALUE'].includes(dataType)) {
      const _valueHandle = targetDS => {
        return targetDS
          .toData()
          .filter(
            (item: any) =>
              item.singleValued ||
              (item.multiValued && (item.multiValued?.start || item.multiValued?.end)),
          )
          .map((item: any) => {
            if (
              item.valueType !== 'single' &&
              (!item.multiValued?.start || !item.multiValued?.end)
            ) {
              if (!item.multiValued?.start) {
                item.multiValued.start = '-∞';
                item.leftValue = '-∞';
              }
              if (!item.multiValued?.end) {
                item.multiValued.end = '+∞';
                item.rightValue = '+∞';
              }
              item.dataValue = `${item.leftChar}${item.multiValued.start},${item.multiValued.end}${item.rightChar}`;
              const _leftCharTip = item.leftChar === '(' ? '<' : '≤';
              const _rightCharTip = item.rightChar === ')' ? '<' : '≤';
              item.valueShow = `${item.multiValued.start}${_leftCharTip}X${_rightCharTip}${item.multiValued.end}`;
            }
            return item;
          });
      };

      _trueValueList = _valueHandle(trueNumberDS);
      _falseValueList = _valueHandle(falseNumberDS);
      _warningValueList = _valueHandle(warnNumberDS);
    } else if (dataType === 'VALUE_LIST') {
      _trueValueList = (record?.get('trueValue') || []).map(item => ({
        dataValue: item,
      }));
      _falseValueList = (record?.get('falseValue') || []).map(item => ({
        dataValue: item,
      }));
    } else {
      _trueValueList = record?.get('trueValue')
        ? [
          {
            dataValue: record?.get('trueValue'),
          },
        ]
        : [];
      _falseValueList = record?.get('falseValue')
        ? [
          {
            dataValue: record?.get('falseValue'),
          },
        ]
        : [];
    }

    if (dataType === 'CALCULATE_FORMULA') {
      record.set(
        'formula',
        JSON.stringify({
          formulaSourceId: record?.get('formulaSourceId'),
          formulaMode: record?.get('formulaMode'),
          formulaDisplayPosition: record?.get('formulaDisplayPosition'),
          formulaId: record?.get('formulaId'),
          formulaCode: record?.get('formulaCode'),
          formulaName: record?.get('formulaName'),
          dimension: record?.get('dimension'),
          formulaList: (formulaListTableDs.toData() || []).map((formulaListItem: any) => {
            if (formulaListItem) {
              return {
                fieldCode: formulaListItem.fieldCode,
                fieldName: formulaListItem.fieldName,
                inspectItemId: formulaListItem.inspectItemId,
                inspectItemDesc: formulaListItem.inspectItemDesc,
                isRequired: formulaListItem.isRequired,
              };
            }
            return {};
          }),
        }),
      );
      record.set('formulaList', formulaListTableDs.toData());
    } else {
      record.set('formula', '');
      record.set('formulaList', []);
    }

    record.set('trueValueList', _trueValueList);
    record.set('falseValueList', _falseValueList);
    record.set('warningValueList', _warningValueList);

    const recordData = record.toData();
    if (recordData.isNewRow) {
      tableDS.create({
        ...recordData,
        isNewRow: false,
      });
    } else {
      tableDS.forEach(focusRecord => {
        if (focusRecord?.get('inspectionItemRowUuid') === recordData.inspectionItemRowUuid) {
          Object.keys(recordData).forEach(recordDataKey => {
            focusRecord.set(recordDataKey, recordData[recordDataKey]);
          });
          focusRecord.set('trueValue', null);
          focusRecord.set('falseValue', null);
          focusRecord.set('trueValue', recordData.trueValue);
          focusRecord.set('falseValue', recordData.falseValue);
        }
      });
    }

    statisticsTableUpdate();

    // 添加一个判断数据类型和任务类别的对象, 用来判断是否清除对应计算公式的关联关系
    // 若计算公式的formulaSourceId 的任务类别跟绑定的检验对象类型不同 或者绑定的检验对象不是数据类型 清除 formulaList 里绑定的id
    const tableRecordMap = {};

    const parentsMap = {};
    tableDS.forEach(subRecord => {
      const subRecordData = subRecord.toData();
      tableRecordMap[subRecordData.inspectItemId] = {
        dataType: subRecordData.dataType,
        taskCategory: subRecordData.taskCategory,
      };
      const _inspectionItemRowUuid = subRecordData.inspectionItemRowUuid;
      if (!parentsMap[_inspectionItemRowUuid]) {
        parentsMap[_inspectionItemRowUuid] = {
          front: subRecordData.sequence,
          after: 1,
        };
      }
    });

    tableDS.forEach(subRecord => {
      const subRecordData = subRecord.toData();
      const _formulaSourceId = subRecordData.formulaSourceId;
      const _formulaList = subRecordData.formulaList || [];

      if (subRecord?.get('dataType') === 'CALCULATE_FORMULA') {
        if (`${_formulaSourceId}` === `${recordData.inspectItemId}`) {
          subRecord.set('requiredFlag', recordData.requiredFlag);
        }
        if (_formulaSourceId && parentsMap[_formulaSourceId]) {
          subRecord.set(
            'sequence',
            parentsMap[_formulaSourceId].front + parentsMap[_formulaSourceId].after,
          );
          parentsMap[_formulaSourceId].after += 1;
        }

        if (_formulaSourceId) {
          subRecord.set('taskCategory', tableRecordMap[_formulaSourceId]?.taskCategory || '');
        }

        _formulaList.forEach(formulaListItem => {
          if (
            !tableRecordMap[formulaListItem.inspectItemId] ||
            tableRecordMap[formulaListItem.inspectItemId].dataType !== 'VALUE' ||
            ((tableRecordMap[formulaListItem.inspectItemId].taskCategory ||
              tableRecordMap[_formulaSourceId].taskCategory) &&
              tableRecordMap[formulaListItem.inspectItemId].taskCategory !==
                tableRecordMap[_formulaSourceId].taskCategory)
          ) {
            formulaListItem.inspectItemId = undefined;
            formulaListItem.inspectItemDesc = undefined;
            formulaListItem.inspectItemObject = undefined;
          }
        });

        subRecord.set('formulaList', _formulaList);

        subRecord.set(
          'formula',
          JSON.stringify({
            formulaSourceId: subRecord?.get('formulaSourceId'),
            formulaMode: subRecord?.get('formulaMode'),
            formulaDisplayPosition: subRecord?.get('formulaDisplayPosition'),
            formulaId: subRecord?.get('formulaId'),
            formulaCode: subRecord?.get('formulaCode'),
            formulaName: subRecord?.get('formulaName'),
            dimension: subRecord?.get('dimension'),
            formulaList: (_formulaList || []).map((formulaListItem: any) => {
              if (formulaListItem) {
                return {
                  fieldCode: formulaListItem.fieldCode,
                  fieldName: formulaListItem.fieldName,
                  inspectItemId: formulaListItem.inspectItemId,
                  inspectItemDesc: formulaListItem.inspectItemDesc,
                  isRequired: formulaListItem.isRequired,
                };
              }
              return {};
            }),
          }),
        );
      }
    });
  };

  const getDataValueShow = (record, name) => {
    const _dataType = record?.get('dataType');
    const _valueData = record?.get(name) || [];
    const _dataShow = _valueData.length > 0 ? _valueData[0].dataValue : '';
    return ['CALCULATE_FORMULA', 'VALUE', 'VALUE_LIST'].includes(_dataType)
      ? _valueData.map(item => <Tag>{item.dataValue}</Tag>)
      : _dataShow;
  };

  const handleDragEnd = () => {
    let _sequence = 10;
    const parentsMap = {};
    tableDS.forEach(record => {
      const _inspectItemId = record?.get('inspectItemId');
      if (record?.get('dataType') !== 'CALCULATE_FORMULA') {
        record.set('sequence', _sequence);
      }

      if (!parentsMap[_inspectItemId]) {
        parentsMap[_inspectItemId] = {
          front: _sequence,
          after: 1,
        };
      }

      if (record?.get('dataType') !== 'CALCULATE_FORMULA') {
        _sequence += 10;
      }
    });

    tableDS.forEach(record => {
      const _formulaSourceId = record?.get('formulaSourceId');

      if (
        record?.get('dataType') === 'CALCULATE_FORMULA' &&
        _formulaSourceId &&
        parentsMap[_formulaSourceId]
      ) {
        record.set(
          'sequence',
          // @ts-ignore
          parentsMap[_formulaSourceId].front + parentsMap[_formulaSourceId].after,
        );
        parentsMap[_formulaSourceId].after += 1;
      }
    });
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <Lov
          dataSet={batchDs}
          name="inspectionItemObject"
          noCache
          // @ts-ignore
          mode="button"
          clearButton={false}
          onChange={value => {
            handleAdd(value);
          }}
          size={Size.small}
          funcType={FuncType.flat}
          disabled={!canEdit}
          modalProps={{
            footer: (okBtn, cancelBtn, modal) => {
              return [
                <Button
                  onClick={() => {
                    formDs.current.set('inspectionDimensionObject', [
                      {
                        value: selectOptionValue(dimensionsItem),
                      },
                    ]);
                    handleCreateInspection(modal, item);
                  }}
                >
                  {intl?.get(`tarzan.inspectionScheme.inspectSchemeObjectCreate`).d('新建检验项目')}
                </Button>,
                cancelBtn,
                okBtn,
              ];
            },
          }}
          tableProps={{
            queryFieldsLimit: 10,
          }}
        >
          <Icon type="add" />
        </Lov>
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl?.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDelete(record)}
          okText={intl?.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl?.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      width: 80,
      align: ColumnAlign.center,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemLov',
      width: 200,
      renderer: ({ record }) => (
        <a onClick={() => handleEdit(record)}>{record?.get('inspectItemCode')}</a>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemDesc',
      width: 200,
    },
    {
      name: 'inspectItemType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'trueValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'trueValueList'),
    },
    {
      name: 'falseValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'falseValueList'),
    },
    {
      name: 'earlyWarningValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'warningValueList'),
    },
    {
      name: 'defaultValue',
    },
    {
      name: 'dataQtyDisposition',
      width: 120,
    },
    {
      name: 'uomLov',
      align: ColumnAlign.center,
    },
    {
      name: 'dataQty',
    },
    // {
    //   name: 'formulaLov',
    // },
    // { name: 'dimension' },
    {
      name: 'samplingMethodLov',
      align: ColumnAlign.center,
    },
    {
      name: 'requiredFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.yes`).d('是')
              : intl?.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'inspectGroupDesc',
      width: 120,
    },
    {
      name: 'taskCategory',
      width: 120,
    },
    {
      name: 'inspectBasis',
    },
    {
      name: 'inspectMethod',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'technicalRequirement',
    },
    {
      name: 'inspectTool',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'qualityCharacteristic',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'dataType',
      align: ColumnAlign.center,
      width: 120,
    },

    {
      name: 'processMode',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'enterMethod',
      align: ColumnAlign.center,
    },
    {
      name: 'decimalNumber',
    },

    {
      name: 'sameGroupIdentification',
    },
    {
      name: 'destructiveExperimentFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.yes`).d('是')
              : intl?.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'outsourceFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl?.get(`tarzan.common.label.yes`).d('是')
              : intl?.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'actionItem',
    },
    {
      name: 'employeePosition',
      width: 120,
    },
    {
      name: 'inspectFrequencyDesc',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
        if (inspectFrequencyShow) {
          inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
          inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
          return inspectFrequencyShow;
        }
        return value;
      },
    },
    {
      name: 'ncCodeGroupLov',
      align: ColumnAlign.center,
    },
    {
      name: 'remark',
    },
    {
      name: 'enclosure',
      width: 160,
    },
  ];

  return (
    <Table
      filter={record => {
        return record?.get('dataType') !== 'CALCULATE_FORMULA';
      }}
      rowDraggable={canEdit}
      dragColumnAlign={DragColumnAlign.left}
      onDragEnd={handleDragEnd}
      dataSet={tableDS}
      columns={columns}
      highLightRow
      virtual
      virtualCell
      style={{ height: 300 }}
    />
  );
};

export default InspectItemTab;
