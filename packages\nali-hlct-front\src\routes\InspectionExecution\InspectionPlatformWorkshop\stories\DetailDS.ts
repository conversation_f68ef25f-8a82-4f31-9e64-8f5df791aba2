/**
 * @Description: 检验平台详情DS
 * @Author: <<EMAIL>>
 * @Date: 2023-02-10 17:46:46
 * @LastEditTime: 2023-05-25 17:21:12
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import moment from 'moment';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.qms.inspectionPlatform';
const tenantId = getCurrentOrganizationId();

// 单据信息
const DocInfoDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  primaryKey: 'inspectTaskId',
  fields: [
    // 单据基本信息
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.inspectDocNum`).d('检验单编码'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.inspectBusinessType`).d('检验业务类型'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.siteCode`).d('站点'),
    },
    {
      name: 'inspectDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.inspectDocStatusDesc`).d('状态'),
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.urgentFlag`).d('加急标识'),
    },
    {
      name: 'sourceObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.sourceObjectType`).d('来源单据类型'),
    },
    {
      name: 'sourceObjectAndLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.sourceObjectAndLineCode`).d('来源单据编码/行号'),
    },
    {
      name: 'inspectInfoUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.inspectInfoUser`).d('报检人'),
    },
    {
      name: 'inspectInfoCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.inspectInfoCreationDate`).d('报检时间'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.materialName`).d('物料'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.materialVersion`).d('物料版本'),
    },
    {
      name: 'inspectSumQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.doc.inspectSumQty`).d('报检总数'),
    },
    {
      name: 'samplingQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.doc.samplingQty`).d('抽样数'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.uomName`).d('单位'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.supplierName`).d('供应商'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.customerName`).d('客户'),
    },
    {
      name: 'areaName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.areaName`).d('区域'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.prodLineName`).d('产线'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.operationName`).d('工艺'),
    },
    {
      name: 'processWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.processWorkcellName`).d('工序'),
    },
    {
      name: 'stationWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.stationWorkcellName`).d('工位'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.equipment`).d('设备'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.locatorName`).d('库位'),
    },
    {
      name: 'docCreateMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.docCreateMethod`).d('单据创建方式'),
    },
    {
      name: 'acceptStandard',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.acceptStandard`).d('接收准测AC/RE'),
    },
    {
      name: 'samplingMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.samplingMethod`).d('抽样方式'),
    },
    {
      name: 'samplingDimensionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.samplingDimension`).d('抽样维度'),
    },
    {
      name: 'strictnessDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.samplingStrictness`).d('抽样严格度'),
    },
    {
      name: 'inspectSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.inspectSchemeCode`).d('检验方案描述'),
    },
    {
      name: 'reviewStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.reviewStatus`).d('审核状态'),
    },
    {
      name: 'reviewUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.reviewUser`).d('审核人'),
    },
    {
      name: 'reviewTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.reviewTime`).d('审核时间'),
    },
    // 单据实绩信息
    {
      name: 'okQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.doc.okQty`).d('合格品数'),
    },
    {
      name: 'ngQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.doc.ngQty`).d('不合格品数'),
    },
    {
      name: 'scrapQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.doc.scrapProductQty`).d('报废品数'),
    },
    {
      name: 'firstInspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.firstInspectResult`).d('初评结果'),
      lookupCode: 'MT.QMS.INSPECT_RESULT',
    },
    {
      name: 'firstInspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.firstInspector`).d('初评记录人'),
    },
    {
      name: 'firstInspectDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.firstInspectDate`).d('初评时间'),
    },
    {
      name: 'lastInspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.lastInspectResult`).d('最终结果'),
      lookupCode: 'MT.QMS.INSPECT_RESULT',
    },
    {
      name: 'lastInspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.lastInspector`).d('最终记录人'),
    },
    {
      name: 'lastInspectDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.lastInspectDate`).d('最终时间'),
    },
    {
      name: 'actualStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.actualStartTime`).d('实际开始时间'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.actualEndTime`).d('实际结束时间'),
    },
    {
      name: 'validity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.doc.validity`).d('RoHS有效期'),
    },
  ],
});

// 检验对象
const InspectObjDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  paging: false,
  forceValidate: true,
  primaryKey: 'inspectObjectId',
  fields: [
    {
      name: 'sourceObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.obj.inspectObjectType`).d('报检对象类型'),
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.obj.inspectionObjectType`).d('报检对象编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.obj.materialName`).d('物料'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.obj.qty`).d('数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.obj.uomName`).d('单位'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.obj.supplierLot`).d('供应商批次'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.obj.lot`).d('厂内批次'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.obj.locatorName`).d('库位'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.obj.qualityStatus`).d('质量状态'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      noCache: true,
      valueField: 'statusCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'ngQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.obj.ngQty`).d('不合格品数'),
      min: 0,
      precision: 6,
    },
    {
      name: 'addNgQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.obj.addNgQty`).d('新增不合格品数'),
      precision: 6,
    },
    {
      name: 'scrapQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.obj.scrapProductQty`).d('报废品数'),
      min: 0,
      precision: 6,
    },
    {
      name: 'okQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.obj.okQty`).d('合格品数'),
    },
    {
      name: 'inspectFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.obj.actualInspectFlag`).d('实际检验标识'),
    },
    // {
    //   name: 'marking',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.obj.marking`).d('marking'),
    //   lookupCode: 'QMS_MARKING',
    // },
  ],
  events: {
    update: ({ name, record, value, oldValue }) => {
      if (['ngQty', 'scrapQty'].includes(name) && value !== oldValue) {
        const _qty = Number(record.get('qty') || 0);
        const _okQty =
          _qty - Number(record.get('ngQty') || 0) - Number(record.get('scrapQty') || 0);
        const _newOkQty = parseFloat(`${_okQty.toFixed(6)}`);
        record.set('okQty', _newOkQty);
        if (Number(_newOkQty || 0) === _qty) {
          record.set('qualityStatus', 'OK');
        } else {
          record.set('qualityStatus', 'NG');
        }
      }
    },
  },
});

// 检验任务
const InspectInfoDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  forceValidate: true,
  primaryKey: 'inspectTaskId',
  fields: [
    {
      name: 'dischargeWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.dischargeWorkcellId`).d('排出工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          workcellType: 'STATION',
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'dischargeWorkcellId',
      type: FieldType.string,
      bind: 'dischargeWorkcellObj.workcellId',
    },
    {
      name: 'dischargeWorkcellName',
      type: FieldType.string,
      bind: 'dischargeWorkcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'interceptWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.interceptWorkcellId`).d('拦截工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'interceptWorkcellId',
      type: FieldType.string,
      bind: 'interceptWorkcellObj.workcellId',
    },
    {
      name: 'interceptWorkcellName',
      type: FieldType.string,
      bind: 'interceptWorkcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'overInterceptOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.overInterceptOperation`).d('跨工单拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          operationStatus: 'USABLE',
        }),
      },
      textField: 'operationName',
    },
    {
      name: 'overInterceptOperationId',
      type: FieldType.string,
      bind: 'overInterceptOperationObj.operationId',
    },
    {
      name: 'overInterceptOperationName',
      type: FieldType.string,
      bind: 'overInterceptOperationObj.operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'concessionInterceptOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.concessionInterceptOperation`).d('让步拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          operationStatus: 'USABLE',
        }),
      },
      textField: 'operationName',
    },
    {
      name: 'concessionInterceptOperationId',
      type: FieldType.string,
      bind: 'concessionInterceptOperationObj.operationId',
    },
    {
      name: 'concessionInterceptOperationName',
      type: FieldType.string,
      bind: 'concessionInterceptOperationObj.operationName',
      ignore: FieldIgnore.always,
    },
    // 检验任务信息
    {
      name: 'inspectTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.inspectTaskCode`).d('检验任务编码'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.inspectBusinessType`).d('检验业务类型'),
    },
    {
      name: 'taskCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.taskCategory`).d('任务类别'),
    },
    {
      name: 'inspectTaskStatus',
      type: FieldType.string,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSPECT_TASK_STATUS`,
      noCache: true,
      valueField: 'statusCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'inspectTaskStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.inspectTaskStatus`).d('检验任务状态'),
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.urgentFlag`).d('加急标识'),
    },
    {
      // 来源检验任务ID
      name: 'sourceTaskId',
      type: FieldType.string,
    },
    {
      name: 'sourceTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.sourceTaskCode`).d('来源检验任务'),
    },
    {
      name: 'sourceInspectResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.sourceInspectResultDesc`).d('检验结果'),
    },
    {
      name: 'sourceOkQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.task.sourceOkQty`).d('合格品数'),
    },
    {
      name: 'sourceNgQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.task.sourceNgQty`).d('不合格品数'),
    },
    {
      name: 'sourceScrapQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.task.sourceScrapQty`).d('报废数'),
    },
    // 检验记录信息
    {
      name: 'actualStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.actualStartTime`).d('实际开始时间'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.actualEndTime`).d('实际结束时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.inspectRemark`).d('检验备注'),
      // dynamicProps: {
      //   required: ({ record }) =>
      //     (record.get('disposalType') === 'PART' || record?.get('disposalType') === 'ALL') &&
      //     record?.get('curDispFunction') === 'PASS',
      // },
    },
    {
      // 总数量
      name: 'inspectSumQty',
      type: FieldType.number,
    },
    {
      name: 'ngQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.task.ngQty`).d('不合格品数'),
      min: 0,
      precision: 6,
    },
    {
      name: 'addNgQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.task.addNgQty`).d('新增不合格品数'),
      precision: 6,
    },
    {
      name: 'scrapQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.task.scrapProductQty`).d('报废品数'),
      min: 0,
      precision: 6,
    },
    {
      name: 'okQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.task.okQty`).d('合格品数'),
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.inspectResult`).d('检验结果'),
      lookupCode: 'MT.QMS.INSPECT_RESULT',
      required: true,
    },
    {
      name: 'coaAttachment',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.task.coaAttachment`).d('COA'),
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform-workshop',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.task.taskEnclosure`).d('任务附件'),
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform-workshop',
    },
    {
      name: 'programmeEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.task.programmeEnclosure`).d('方案附件'),
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform-workshop',
    },
    // 处置相关逻辑
    {
      // 处置是否可编辑标识
      name: 'editDisposalFlag',
      type: FieldType.string,
    },
    {
      name: 'disposalType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.disposalType`).d('处置结论'),
      lookupCode: 'MT.DISPOSAL_TYPE',
      dynamicProps: {
        disabled: ({ record }) =>
          (record?.get('editDisposalFlag') !== 'Y' ||
            record?.get('inspectTaskStatus') !== 'INSPECTING') &&
          record?.get('showButtonFlag') === 'Y',
        required: ({ record, dataSet }) =>
          dataSet.getState('editDisposal') ||
          (record?.get('editDisposalFlag') === 'Y' &&
            record?.get('inspectTaskStatus') === 'INSPECTING') ||
          record?.get('inspectTaskCount') <= 1 ||
          record?.get('inspectResult') === 'NG',
      },
    },
    {
      name: 'curDispFunctionId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.dispositionFunction`).d('处置方式'),
      valueField: 'dispositionFunctionId',
      textField: 'description',
      dynamicProps: {
        required: ({ record, dataSet }) =>
          dataSet.getState('editDisposal') && record?.get('disposalType') === 'ALL',
      },
    },
    {
      // 处置备注
      name: 'disposalRemark',
      type: FieldType.string,
    },
    {
      // 处置方法编码
      name: 'dispositionFunction',
      type: FieldType.string,
    },
    {
      name: 'reworkOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.task.reworkOperationName`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      textField: 'operationName',
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          operationStatus: 'USABLE',
        }),
      },
    },
    {
      name: 'reworkOperationId',
      type: FieldType.string,
      bind: 'reworkOperationObj.operationId',
    },
    {
      name: 'reworkOperationName',
      type: FieldType.string,
      bind: 'reworkOperationObj.operationName',
      ignore: FieldIgnore.always,
    },
    // {
    //   name: 'reworkStepName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.task.reworkStepName`).d('步骤识别码'),
    // },
    {
      name: 'routerStepObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.task.routerStep`).d('让步拦截步骤'),
      lovCode: 'MT.ROUTER_STEP',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'description',
    },
    {
      name: 'routerStepId',
      bind: 'routerStepObj.routerStepId',
    },
    {
      name: 'routerStepDesc',
      bind: 'routerStepObj.description',
    },

    {
      name: 'reworkWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.task.reworkWorkcellName`).d('工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          workcellType: 'PROCESS',
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'reworkWorkcellId',
      type: FieldType.string,
      bind: 'reworkWorkcellObj.workcellId',
    },
    {
      name: 'reworkWorkcellName',
      type: FieldType.string,
      bind: 'reworkWorkcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'reworkRouterObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.task.reworkRouterName`).d('返修工艺路线'),
      lovCode: 'MT.METHOD.ROUTER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          routerType: 'NC',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'routerName',
    },
    {
      name: 'reworkRouterId',
      type: FieldType.string,
      bind: 'reworkRouterObj.routerId',
    },
    {
      name: 'reworkRouterName',
      type: FieldType.string,
      bind: 'reworkRouterObj.routerName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'degradeMaterialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.task.degradeMaterialName`).d('降级物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
        disabled: ({ record }) => {
          return record.get('degradeLevel');
        },
      },
      textField: 'materialName',
    },
    {
      name: 'degradeMaterialId',
      type: FieldType.string,
      bind: 'degradeMaterialObj.materialId',
    },
    {
      name: 'degradeMaterialName',
      type: FieldType.string,
      bind: 'degradeMaterialObj.materialName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'degradeMaterialObj.revisionFlag',
      ignore: FieldIgnore.always,
    },
    {
      name: 'degradeRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.degradeRevisionCode`).d('降级物料版本'),
      noCache: true,
      valueField: 'revisionCode',
      textField: 'revisionCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return (rows || []).map(item => ({
            revisionCode: item,
          }));
        },
      },
      dynamicProps: {
        required: ({ record }) => record?.get('revisionFlag') === 'Y',
        disabled: ({ record }) => record?.get('revisionFlag') !== 'Y' || record.get('degradeLevel'),
        lookupUrl: ({ record }) => {
          const siteId = record?.get('siteId');
          const materialId = record?.get('degradeMaterialId');
          const revisionFlag = record?.get('revisionFlag');
          if (!!siteId && !!materialId && revisionFlag === 'Y') {
            return `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-task/item/nc/revision/code/ui?siteId=${siteId}&materialId=${materialId}`;
          }
          return '';
        },
      },
    },
    {
      name: 'degradeLevel',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_LEVEL',
      label: intl.get(`${modelPrompt}.task.degradeLevel`).d('降级等级'),
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
        disabled: ({ record }) => {
          return record.get('degradeMaterialObj');
        },
      },
    },
    {
      name: 'degradeDegree',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_DEGREE',
      label: intl.get(`${modelPrompt}.task.degradeDegree`).d('降级程度'),
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
        disabled: ({ record }) => {
          return record.get('degradeMaterialObj');
        },
      },
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
    },
    {
      name: 'stationWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stationWorkcellName`).d('工位'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备'),
    },
    {
      name: 'baseLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.baseLot`).d('基膜卷号'),
    },
    {
      name: 'marking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.marking`).d('MARKING'),
      lookupCode: 'QMS_MARKING',
    },
  ],
  events: {
    update: ({ dataSet, record, name, value, oldValue }) => {
      if (['ngQty', 'scrapQty'].includes(name) && value !== oldValue) {
        const okQty =
          Number(record.get('inspectSumQty') || 0) -
          Number(record.get('ngQty') || 0) -
          Number(record.get('scrapQty') || 0);
        record.set('okQty', parseFloat(`${okQty.toFixed(6)}`));
        dataSet.setState('docChangeFlag', 'Y');
      }
      if (name === 'degradeMaterialObj') {
        record.set('degradeRevisionCode', null);
      }
    },
  },
});

// 检验项行模式
const InspectItemRowDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  forceValidate: true,
  paging: false,
  primaryKey: 'inspectDocLineId',
  fields: [
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.sequence`).d('序号'),
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.inspectItemDesc`).d('检验项目描述'),
    },
    {
      name: 'inspectItemTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.inspectItemType`).d('类型'),
    },
    {
      name: 'inspectToolAndMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.inspectToolAndMethod`).d('检验工具/方法'),
    },
    {
      name: 'qualityCharacteristicDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'acceptStandard',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.acceptStandard`).d('AC/RE'),
    },
    {
      name: 'samplingQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.line.model.samplingQty`).d('抽样数量'),
      min: 1,
      step: 1,
      dynamicProps: {
        required: ({ record }) => record?.get('dataType') !== 'CALCULATE_FORMULA',
      },
    },
    {
      name: 'trueValues',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.trueValue`).d('符合值'),
    },
    {
      name: 'falseValues',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.falseValue`).d('不符合值'),
    },
    {
      name: 'warningValues',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.warningValue`).d('预警值'),
    },
    {
      name: 'okQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.line.model.lineOkQty`).d('OK数'),
      min: 0,
      step: 1,
    },
    {
      name: 'ngQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.line.model.lineNgQty`).d('NG数'),
      min: 0,
      step: 1,
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.determine`).d('判定'),
      lookupCode: 'MT.QMS.INSPECT_RESULT',
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet.getState('resultDimension') !== 'UNQUALIFIED_INSPECTION_TASK' &&
          record?.get('requiredFlag') === 'Y',
      },
    },
    {
      name: 'sourceInspectValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.originalInspectionRecord`).d('原始检验记录'),
    },
    {
      name: 'inspectValueRecord',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.inspectValueRecord`).d('检验记录'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.remark`).d('备注'),
    },
    {
      // 实绩附件
      name: 'actEnclosure',
      type: FieldType.attachment,
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform-workshop',
    },
    {
      // 检验项目附件
      name: 'enclosure',
      type: FieldType.attachment,
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform-workshop',
    },
  ],
});

// 检验项带对象的行模式-对象录值
const InspectItemRowObjValueDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  forceValidate: true,
  primaryKey: 'cacheInspectObjectId',
  fields: [
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.sequence`).d('序号'),
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.inspectObject`).d('检验对象'),
      // dynamicProps: {
      //   required: ({ dataSet, record }) =>
      //     dataSet.getState('resultDimension') === 'RECORD_SAMPLE_VALUE' &&
      //     record?.get('requiredFlag') === 'Y' &&
      //     record?.get('dataType') !== 'CALCULATE_FORMULA',
      // },
    },
    {
      name: 'inspectValueRecord',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.inspectValueRecord`).d('检验记录'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.inspectExplain`).d('检验说明'),
    },
  ],
});

// 检验项列模式
const InspectItemColDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  forceValidate: true,
  paging: false,
  primaryKey: 'inspectItemKey',
  fields: [
    {
      name: 'inspectItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.inspectItem`).d('检验项目'),
      // dynamicProps: {
      //   defaultValidationMessages: ({ record }) => {
      //     if (record?.get('cacheInspectObjectId')) {
      //       return {
      //         valueMissing: intl
      //           .get(`${modelPrompt}.message.requiredInspectObject`)
      //           .d('请输入检验对象'),
      //       };
      //     }
      //   },
      // },
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => !!record?.get('cacheInspectObjectId'),
    },
  },
});

// 新增质检项Lov/复制检验值Lov/当前检验对象Lov
const InspectLovDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'inspectItemObj',
      type: FieldType.object,
      lovCode: 'MT.QMS.INSPECT_ITEM_INFO',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          inspectDocId: dataSet.getState('inspectDocId'),
        }),
      },
    },
    {
      name: 'inspectItemId',
      type: FieldType.string,
      bind: 'inspectItemObj.inspectItemId',
    },
    {
      name: 'inspectDocObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.lov.inspectDoc`).d('检验单'),
      lovCode: 'MT.QMS.INSPECT_SOURCE_DOC',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          inspectDocId: dataSet.getState('inspectDocId'),
        }),
        required: ({ record }) => record?.get('operationType') === 'DOC_COPY',
      },
    },
    {
      name: 'inspectDocId',
      type: FieldType.string,
      bind: 'inspectDocObj.inspectDocId',
    },
    {
      name: 'sourceObjectObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.lov.curInspectObjectCode`).d('当前检验对象'),
      lovCode: 'MT.QMS.INSPECT_TASK_OBJECT',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          inspectTaskId: dataSet.getState('inspectTaskId'),
        }),
      },
    },
    {
      name: 'inspectObjectId',
      type: FieldType.string,
      bind: 'sourceObjectObj.inspectObjectId',
    },
    {
      name: 'samplingQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.line.model.samplingQty`).d('抽样数量'),
      min: 1,
      step: 1,
      dynamicProps: {
        required: ({ record }) => record?.get('operationType') === 'BATCH_SAMPLING',
      },
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.line.model.determine`).d('判定'),
      lookupCode: 'MT.QMS.INSPECT_RESULT',
      dynamicProps: {
        required: ({ record }) => record?.get('operationType') === 'BATCH_RESULT',
      },
    },
  ],
});

// 检验不良记录
const NcRecordDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  forceValidate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  primaryKey: 'inspectDocNcRecordId',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-task/item/nc/ui`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          const rows = datas?.rows || {};
          return {
            ...datas,
            rows: rows.lines || [],
            baseInfo: {
              ...rows,
              lines: null,
            },
          };
        },
      };
    },
  },
  fields: [
    {
      // 不良记录维度
      name: 'inspectNcRecordDimension',
      type: FieldType.string,
    },
    {
      // 任务ID
      name: 'taskId',
      type: FieldType.string,
    },
    {
      // 项目ID
      name: 'inspectItemId',
      type: FieldType.string,
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.inspectItemDesc`).d('检验项目'),
    },
    {
      // 检验对象ID
      name: 'inspectObjectId',
      type: FieldType.string,
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.sourceObjectCode`).d('检验对象'),
    },
    {
      name: 'ncRecordType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.ncRecordType`).d('不良记录类型'),
      lookupCode: 'MT.NC_RECORD_TYPE',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.index !== 0;
        },
      },
    },
    {
      name: 'ncCodeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.ncCodeId`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      required: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.getState('baseInfo')?.siteId,
          ncGroupIds: dataSet.getState('baseInfo')?.ncGroupIds,
        }),
      },
      textField: 'description',
    },
    {
      name: 'ncCodeId',
      type: FieldType.string,
      bind: 'ncCodeObj.ncCodeId',
    },
    {
      name: 'ncCodeDesc',
      type: FieldType.string,
      bind: 'ncCodeObj.description',
      ignore: FieldIgnore.always,
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.ncRecord.qty`).d('不良数量'),
      validator: value => {
        if ((value && value <= 0) || value === 0) {
          return intl.get(`${modelPrompt}.ncRecord.validateQty`).d('不良数量必须大于0');
        }
        return true;
      },
    },
    {
      // 单位ID
      name: 'uomId',
      type: FieldType.string,
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.uomName`).d('单位'),
    },
    {
      name: 'defectLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.defectLevel`).d('缺陷等级'),
      lookupCode: 'MT.METHOD.NC_DEFECT_LEVEL',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('ncCodeObj');
        },
      },
    },
    {
      name: 'ncRecordTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.ncRecordTime`).d('不良发生时间'),
    },
    {
      // 不良记录人ID
      name: 'ncRecordUserId',
      type: FieldType.string,
    },
    {
      name: 'ncRecordUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.ncRecordUserName`).d('不良记录人'),
    },
    {
      name: 'operationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.operationId`).d('不良发生工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          operationStatus: 'USABLE',
        }),
      },
      textField: 'operationName',
    },
    {
      name: 'operationId',
      type: FieldType.string,
      bind: 'operationObj.operationId',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      bind: 'operationObj.operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'workcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.workcellId`).d('不良发现工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'workcellId',
      type: FieldType.string,
      bind: 'workcellObj.workcellId',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      bind: 'workcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'locatorObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.locatorId`).d('不良发现库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'locatorName',
    },
    {
      name: 'locatorId',
      type: FieldType.string,
      bind: 'locatorObj.locatorId',
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      bind: 'locatorObj.locatorName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'equipmentObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.equipmentId`).d('不良发现设备'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      textField: 'equipmentCode',
    },
    {
      name: 'equipmentId',
      type: FieldType.string,
      bind: 'equipmentObj.equipmentId',
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      bind: 'equipmentObj.equipmentCode',
      ignore: FieldIgnore.always,
    },
    {
      name: 'componentMaterialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.componentMaterialId`).d('组件物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'materialName',
    },
    {
      name: 'componentMaterialId',
      type: FieldType.string,
      bind: 'componentMaterialObj.materialId',
    },
    {
      name: 'componentMaterialName',
      type: FieldType.string,
      bind: 'componentMaterialObj.materialName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'componentMaterialObj.revisionFlag',
      ignore: FieldIgnore.always,
    },
    {
      name: 'componentRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.componentRevisionCode`).d('组件物料版本'),
      noCache: true,
      valueField: 'revisionCode',
      textField: 'revisionCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return (rows || []).map(item => ({
            revisionCode: item,
          }));
        },
      },
      dynamicProps: {
        required: ({ record }) => record?.get('revisionFlag') === 'Y',
        disabled: ({ record }) => record?.get('revisionFlag') !== 'Y',
        lookupUrl: ({ dataSet, record }) => {
          const siteId = dataSet.getState('baseInfo')?.siteId;
          const materialId = record?.get('componentMaterialId');
          const revisionFlag = record?.get('revisionFlag');
          if (!!siteId && !!materialId && revisionFlag === 'Y') {
            return `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-task/item/nc/revision/code/ui?siteId=${siteId}&materialId=${materialId}`;
          }
          return '';
        },
      },
    },
    {
      name: 'defectLocation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.defectLocation`).d('缺陷位置'),
    },
    {
      name: 'defectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.defectReason`).d('不良原因'),
    },
    {
      name: 'interceptWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.interceptWorkcellId`).d('拦截工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'interceptWorkcellId',
      type: FieldType.string,
      bind: 'interceptWorkcellObj.workcellId',
    },
    {
      name: 'interceptWorkcellName',
      type: FieldType.string,
      bind: 'interceptWorkcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'dischargeWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.dischargeWorkcellId`).d('排出工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          workcellType: 'STATION',
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'dischargeWorkcellId',
      type: FieldType.string,
      bind: 'dischargeWorkcellObj.workcellId',
    },
    {
      name: 'dischargeWorkcellName',
      type: FieldType.string,
      bind: 'dischargeWorkcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'interceptOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.interceptOperationId`).d('拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          operationStatus: 'USABLE',
        }),
      },
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      textField: 'operationName',
    },
    {
      name: 'interceptOperationId',
      type: FieldType.string,
      bind: 'interceptOperationObj.operationId',
    },
    {
      name: 'interceptOperationName',
      type: FieldType.string,
      bind: 'interceptOperationObj.operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'rootCauseWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.rootCauseWorkcellId`).d('不良产生工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'rootCauseWorkcellId',
      type: FieldType.string,
      bind: 'rootCauseWorkcellObj.workcellId',
    },
    {
      name: 'rootCauseWorkcellName',
      type: FieldType.string,
      bind: 'rootCauseWorkcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'rootCauseOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.rootCauseOperationId`).d('不良产生工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          operationStatus: 'USABLE',
        }),
      },
      textField: 'operationName',
    },
    {
      name: 'rootCauseOperationId',
      type: FieldType.string,
      bind: 'rootCauseOperationObj.operationId',
    },
    {
      name: 'rootCauseOperationName',
      type: FieldType.string,
      bind: 'rootCauseOperationObj.operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'rootCauseEquipmentObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.rootCauseEquipmentId`).d('不良产生设备'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      textField: 'equipmentCode',
    },
    {
      name: 'rootCauseEquipmentId',
      type: FieldType.string,
      bind: 'rootCauseEquipmentObj.equipmentId',
    },
    {
      name: 'rootCauseEquipmentCode',
      type: FieldType.string,
      bind: 'rootCauseEquipmentObj.equipmentCode',
      ignore: FieldIgnore.always,
    },
    {
      name: 'responsibleUserObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.responsibleUserId`).d('问题责任人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'realName',
    },
    {
      name: 'responsibleUserId',
      type: FieldType.string,
      bind: 'responsibleUserObj.id',
    },
    {
      name: 'responsibleUserName',
      type: FieldType.string,
      bind: 'responsibleUserObj.realName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'responsibleApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.responsibleApartment`).d('问题责任部门'),
    },
    {
      name: 'shiftTeamCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.shiftTeamCode`).d('班组编码'),
    },
    {
      name: 'shiftDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.ncRecord.shiftDate`).d('班次日期'),
      transformRequest: value => {
        return value ? moment(value).format('YYYY-MM-DD') : value;
      },
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.shiftCode`).d('班次编码'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.containerCode`).d('容器编码'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.remark`).d('备注'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.ncRecord.enclosure`).d('附件'),
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform-workshop',
    },
    {
      name: 'referenceArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.referenceArea`).d('参考区域'),
    },
    {
      name: 'referencePoint',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.referencePoint`).d('参考点'),
    },
  ],
  events: {
    update: ({ record, name }) => {
      if (name === 'componentMaterialObj') {
        record.set('componentRevisionCode', null);
      }
    },
  },
});

// 处置方法数据源
const DisposalModeDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  primaryKey: 'dispositionFunctionId',
  fields: [
    {
      // 处置方式ID
      name: 'dispositionFunctionId',
      type: FieldType.string,
    },
    {
      // 处置方式编码
      name: 'dispositionFunction',
      type: FieldType.string,
    },
    {
      // 处置方式描述
      name: 'description',
      type: FieldType.string,
    },
  ],
});

// 部分处置
const PartDisposalDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  primaryKey: 'disposalObjectId',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-task/item/part/disposal/ui`,
        method: 'GET',
        transformResponse: data => {
          if (Array.isArray(data)) {
            return data;
          }
          const datas = JSON.parse(data);
          // 处理接口报错
          if (datas && !datas.success) {
            notification.error({
              message: datas.message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
            return {
              rows: [],
            };
          }
          return datas.rows;
        },
      };
    },
  },
  fields: [
    {
      name: 'dischargeWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.dischargeWorkcellId`).d('排出工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          workcellType: 'STATION',
        }),
        disabled: ({ dataSet, record }) => {
          const disposalTypeMap = dataSet.getState('disposalTypeMap') || {};
          let objId;
          Object.keys(disposalTypeMap).forEach(key => {
            if (disposalTypeMap[key].dispositionFunction === 'EO_DISCHARGE') {
              objId = key;
            }
          });
          return !record.get(objId);
        },
      },
      textField: 'workcellName',
    },
    {
      name: 'dischargeWorkcellId',
      type: FieldType.string,
      bind: 'dischargeWorkcellObj.workcellId',
    },
    {
      name: 'dischargeWorkcellName',
      type: FieldType.string,
      bind: 'dischargeWorkcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'interceptWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.interceptWorkcellId`).d('拦截工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
        disabled: ({ dataSet, record }) => {
          const disposalTypeMap = dataSet.getState('disposalTypeMap') || {};
          let objId;
          Object.keys(disposalTypeMap).forEach(key => {
            if (disposalTypeMap[key].dispositionFunction === 'CONCESSION_INTERCEPTION') {
              objId = key;
            }
          });
          return !record.get(objId);
        },
      },
      textField: 'workcellName',
    },
    {
      name: 'interceptWorkcellId',
      type: FieldType.string,
      bind: 'interceptWorkcellObj.workcellId',
    },
    {
      name: 'interceptWorkcellName',
      type: FieldType.string,
      bind: 'interceptWorkcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'overInterceptOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.overInterceptOperation`).d('跨工单拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      textField: 'operationName',
      dynamicProps: {
        disabled: ({ dataSet, record }) => {
          const disposalTypeMap = dataSet.getState('disposalTypeMap') || {};
          let objId;
          Object.keys(disposalTypeMap).forEach(key => {
            if (disposalTypeMap[key].dispositionFunction === 'CROSS_WO_INTERCEPT') {
              objId = key;
            }
          });
          return !record.get(objId);
        },
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          operationStatus: 'USABLE',
        }),
      },
    },
    {
      name: 'overInterceptOperationId',
      type: FieldType.string,
      bind: 'overInterceptOperationObj.operationId',
    },
    {
      name: 'overInterceptOperationName',
      type: FieldType.string,
      bind: 'overInterceptOperationObj.operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'concessionInterceptOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.concessionInterceptOperation`).d('让步拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      textField: 'operationName',
      dynamicProps: {
        disabled: ({ dataSet, record }) => {
          const disposalTypeMap = dataSet.getState('disposalTypeMap') || {};
          let objId;
          Object.keys(disposalTypeMap).forEach(key => {
            if (disposalTypeMap[key].dispositionFunction === 'CONCESSION_INTERCEPTION') {
              objId = key;
            }
          });
          return !record.get(objId);
        },
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          operationStatus: 'USABLE',
        }),
      },
    },
    {
      name: 'concessionInterceptOperationId',
      type: FieldType.string,
      bind: 'concessionInterceptOperationObj.operationId',
    },
    {
      name: 'concessionInterceptOperationName',
      type: FieldType.string,
      bind: 'concessionInterceptOperationObj.operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'disposalObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposal.disposalObjectType`).d('处置对象类型'),
      lookupCode: 'MT.QMS.DISPOSAL_OBJECT_TYPE',
    },
    {
      // 处置对象ID
      name: 'disposalObjectId',
      type: FieldType.string,
    },
    {
      name: 'disposalObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposal.disposalObjectCode`).d('处置对象'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'baseLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.baseLot`).d('基膜卷号'),
    },
    {
      name: 'disposalObjectQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.disposal.disposalObjectQty`).d('处置对象数量'),
    },
    {
      // 处置方式REWORK_SOURCE相关字段
      name: 'reworkSourceFlag',
      type: FieldType.boolean,
    },
    {
      name: 'reworkOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.disposal.reworkOperationName`).d('返修工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('baseInfo')?.siteId,
          operationStatus: 'USABLE',
        }),
      },
      textField: 'operationName',
    },
    {
      name: 'reworkOperationId',
      type: FieldType.string,
      bind: 'reworkOperationObj.operationId',
    },
    {
      name: 'reworkOperationName',
      type: FieldType.string,
      bind: 'reworkOperationObj.operationName',
      ignore: FieldIgnore.always,
    },
    // {
    //   name: 'reworkStepName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.disposal.reworkStepName`).d('返修步骤识别码'),
    // },
    {
      name: 'routerStepObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.task.routerStep`).d('让步拦截步骤'),
      lovCode: 'MT.ROUTER_STEP',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'description',
    },
    {
      name: 'routerStepId',
      bind: 'routerStepObj.routerStepId',
    },
    {
      name: 'routerStepDesc',
      bind: 'routerStepObj.description',
    },
    {
      name: 'reworkWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.disposal.reworkWorkcellName`).d('返修工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          workcellType: 'PROCESS',
          siteId: dataSet.getState('baseInfo')?.siteId,
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'reworkWorkcellId',
      type: FieldType.string,
      bind: 'reworkWorkcellObj.workcellId',
    },
    {
      name: 'reworkWorkcellName',
      type: FieldType.string,
      bind: 'reworkWorkcellObj.workcellName',
      ignore: FieldIgnore.always,
    },
    {
      // 处置方式REWORK_ROUTER相关字段
      name: 'reworkRouterFlag',
      type: FieldType.boolean,
    },
    {
      name: 'reworkRouterObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.disposal.reworkRouterName`).d('返修工艺路线'),
      lovCode: 'MT.METHOD.ROUTER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          routerType: 'NC',
          siteId: dataSet.getState('siteId'),
        }),
      },
      textField: 'routerName',
    },
    {
      name: 'reworkRouterId',
      type: FieldType.string,
      bind: 'reworkRouterObj.routerId',
    },
    {
      name: 'reworkRouterName',
      type: FieldType.string,
      bind: 'reworkRouterObj.routerName',
      ignore: FieldIgnore.always,
    },
    {
      // 处置方式DOWNGRADE相关字段
      name: 'downgradeFlag',
      type: FieldType.boolean,
    },
    {
      name: 'degradeMaterialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.disposal.degradeMaterialName`).d('降级物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: dataSet.getState('siteId'),
        }),
        disabled: ({ record }) => {
          return record.get('degradeLevel');
        },
      },
      textField: 'materialName',
    },
    {
      name: 'degradeMaterialId',
      type: FieldType.string,
      bind: 'degradeMaterialObj.materialId',
    },
    {
      name: 'degradeMaterialName',
      type: FieldType.string,
      bind: 'degradeMaterialObj.materialName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'degradeMaterialObj.revisionFlag',
      ignore: FieldIgnore.always,
    },
    {
      name: 'degradeRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposal.degradeRevisionCode`).d('降级物料版本'),
      noCache: true,
      valueField: 'revisionCode',
      textField: 'revisionCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return (rows || []).map(item => ({
            revisionCode: item,
          }));
        },
      },
      dynamicProps: {
        required: ({ record }) => record?.get('revisionFlag') === 'Y',
        disabled: ({ record }) => record?.get('revisionFlag') !== 'Y' || record.get('degradeLevel'),
        lookupUrl: ({ dataSet, record }) => {
          const siteId = dataSet.getState('siteId');
          const materialId = record?.get('degradeMaterialId');
          const revisionFlag = record?.get('revisionFlag');
          if (!!siteId && !!materialId && revisionFlag === 'Y') {
            return `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-task/item/nc/revision/code/ui?siteId=${siteId}&materialId=${materialId}`;
          }
          return '';
        },
      },
    },
    {
      name: 'degradeLevel',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_LEVEL',
      label: intl.get(`${modelPrompt}.disposal.degradeLevel`).d('降级等级'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('degradeMaterialObj');
        },
      },
    },
    {
      name: 'degradeDegree',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_DEGREE',
      label: intl.get(`${modelPrompt}.disposal.degradeDegree`).d('降级程度'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('degradeMaterialObj');
        },
      },
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposal.uomName`).d('单位'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposal.remark`).d('备注'),
      // dynamicProps: {
      //   required: ({ record }) => {
      //     return record.get("disposalType") === "PART";
      //   },
      // },
    },
    {
      name: 'gatherQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.disposal.inspectSumQty`).d('报检总数'),
    },
  ],
  events: {
    update: ({ dataSet, record, name, value, oldValue }) => {
      if (name === 'degradeMaterialObj') {
        record.set('degradeRevisionCode', null);
      }
      const _passIdName = dataSet.getState('passId');
      if (name.indexOf('DISPOSAL_') !== -1 && value !== oldValue && name !== _passIdName) {
        const disposalTypeMap = dataSet.getState('disposalTypeMap') || {};
        const _disposalTypeInfo = disposalTypeMap[name] || {};
        const dispositionFunction = _disposalTypeInfo?.dispositionFunction;
        if (dispositionFunction === 'REWORK_SOURCE') {
          if (value) {
            record.set('reworkSourceFlag', true);
          } else {
            record.set('reworkSourceFlag', false);
            record.set('reworkOperationObj', null);
            record.set('reworkWorkcellObj', null);
          }
        } else if (dispositionFunction === 'REWORK_ROUTER') {
          if (value) {
            record.set('reworkRouterFlag', true);
          } else {
            record.set('reworkRouterFlag', false);
            record.set('reworkRouterObj', null);
          }
        } else if (dispositionFunction === 'DEGRADE') {
          if (value) {
            record.set('downgradeFlag', true);
          } else {
            record.set('downgradeFlag', false);
            record.set('degradeMaterialObj', null);
            record.set('degradeRevisionCode', null);
            record.set('degradeLevel', null);
          }
        } else if (dispositionFunction === 'CONCESSION_INTERCEPTION') {
          if (value) {
            record.set('concessionInterceptionFlag', true);
          } else {
            record.set('concessionInterceptionFlag', false);
            record.set('concessionInterceptOperationObj', null);
            record.set('routerStepObj', null);
            record.set('interceptWorkcellObj', null);
          }
        } else if (dispositionFunction === 'EO_DISCHARGE') {
          if (value) {
            record.set('eoDischargeFlag', true);
          } else {
            record.set('eoDischargeFlag', false);
            record.set('dischargeWorkcellObj', null);
          }
        } else if (dispositionFunction === 'CROSS_WO_INTERCEPT') {
          if (value) {
            record.set('crossWoInterceptFlag', true);
          } else {
            record.set('crossWoInterceptFlag', false);
            record.set('overInterceptOperationObj', null);
          }
        }
        const _dValue = Number(oldValue || 0) - Number(value || 0);

        // 计算通过数
        if (_passIdName) {
          const _passQty = Number(record.get(_passIdName) || 0) + _dValue;
          record.set(_passIdName, parseFloat(`${_passQty.toFixed(6)}`));
        }

        // 计算对应处置方法的总数
        const _sumQty = Number(_disposalTypeInfo?.sumQty || 0) - _dValue;
        disposalTypeMap[name] = {
          ..._disposalTypeInfo,
          sumQty: parseFloat(`${_sumQty.toFixed(6)}`),
        };

        // 计算通过总数
        const _firstRecord = dataSet.get(0);
        if (_firstRecord) {
          const _gatherQty = Number(_firstRecord.get('gatherQty') || 0) + _dValue;
          _firstRecord.init('gatherQty', parseFloat(`${_gatherQty.toFixed(6)}`));
        }
      }
    },
  },
});

export {
  DocInfoDS,
  InspectObjDS,
  InspectInfoDS,
  InspectItemRowDS,
  InspectItemRowObjValueDS,
  InspectItemColDS,
  InspectLovDS,
  NcRecordDS,
  DisposalModeDS,
  PartDisposalDS,
};
