/**
 * @Description: 消息维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-07-14 15:39:32
 * @LastEditTime: 2022-10-09 13:53:28
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.process.unitWork.model.unitWork';
const tenantId = getCurrentOrganizationId();

const tableDS = (): DataSetProps => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'operationWkcDispatchRelId',
  queryFields: [
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺名称'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      type: FieldType.number,
      bind: 'operationLov.operationId',
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workCellCode`).d('工作单元编码'),
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'workcellId',
      type: FieldType.number,
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'stepName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stepName`).d('工艺别名'),
    },
  ],
  fields: [
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺名称'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('工艺描述'),
      bind: 'operationLov.description',
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workCellCode`).d('工作单元编码'),
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'workcellId',
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'workcellCode',
      bind: 'workcellLov.workcellCode',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('WKC描述'),
      bind: 'workcellLov.workcellName',
    },
    {
      name: 'priority',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.priority`).d('优先级'),
      step: 1,
      min: 0,
    },
    {
      name: 'stepName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stepName`).d('工艺别名'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation-wkc-dispatch-rel/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
