/**
 * @Description: 不良记录单管理平台 -主界面
 * @Author: <EMAIL>
 * @Date: 2023/3/7 13:55
 */
import React, { FC, useMemo, useState } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { Tag } from 'choerodon-ui';
import { DataSet, Table, Modal } from 'choerodon-ui/pro';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import { headDS, lineDS } from '../stores';
import { CancelNcReportDoc } from '../services';

const modelPrompt = 'tarzan.hwms.ncReportDocMaintainNew';

const tenantId = getCurrentOrganizationId();

interface NcReportListProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
  customizeTable: any;
  customizeForm: any;
}

interface SelectInfoProps {
  ncReportIds: Array<number>;
  ncReportStatus: Array<string>;
  reviewType: Array<string>;
  ncReviewStatus: Array<string>;
}

const NcReportList: FC<NcReportListProps> = props => {
  const {
    match: { path },
    headDs,
    lineDs,
    history,
    customizeTable,
  } = props;
  const [selectInfo, setSelectInfo] = useState<SelectInfoProps>({
    ncReportIds: [],
    ncReportStatus: [],
    reviewType: [],
    ncReviewStatus: [],
  });
  const { run: canNcReportDoc } = useRequest(CancelNcReportDoc(), { manual: true, needPromise: true });

  const handleUpdateSelect = () => {
    const _ncReportIds: Array<number> = [];
    const _ncReportStatus: Array<string> = [];
    const _reviewType: Array<string> = [];
    const _ncReviewStatus: Array<string> = [];
    headDs.selected.forEach(_record => {
      _ncReportIds.push(_record.get('ncReportId'));
      if (!_ncReportStatus.includes(_record.get('ncReportStatus'))) {
        _ncReportStatus.push(_record.get('ncReportStatus'));
      }
      if (!_reviewType.includes(_record.get('reviewType'))) {
        _reviewType.push(_record.get('reviewType'));
      }
      if (!_ncReviewStatus.includes(_record.get('ncReviewStatus'))) {
        _ncReviewStatus.push(_record.get('ncReviewStatus'));
      }
    });
    setSelectInfo({
      ncReportIds: _ncReportIds,
      ncReportStatus: _ncReportStatus,
      reviewType: _reviewType,
      ncReviewStatus: _ncReviewStatus,
    });
  };

  useDataSetEvent(headDs, 'batchSelect', handleUpdateSelect);
  useDataSetEvent(headDs, 'batchUnselect', handleUpdateSelect);

  const resetHeaderBeforeDetail = ({ data }) => {
    data.map(i => {
      if (i && i.flex) {
        i.recorderPersonDesc = i.flex.recorderPersonDesc;
        i.supplierCode = i.flex.supplierCode;
        i.supplierName = i.flex.supplierName;
        i.locatorCode = i.flex.locatorCode;
        i.locatorName = i.flex.locatorName;
        i.operationName = i.flex.operationName;
        i.operationDesc = i.flex.operationDesc;
        i.productionLineCode = i.flex.productionLineCode;
        i.productionLineName = i.flex.productionLineName;
      }
      return i;
    });
  };

  useDataSetEvent(headDs, 'beforeLoad', resetHeaderBeforeDetail);

  const queryLineTable = ncReportId => {
    if (ncReportId) {
      lineDs.setQueryParameter('ncReportId', ncReportId);
    } else {
      lineDs.setQueryParameter('ncReportId', 0);
    }
    lineDs.query();
  };

  const renderNcReportTag = (value, record) => {
    switch (record.get('ncReportStatus')) {
      case 'NEW':
        return <Tag color="green">{value}</Tag>;
      case 'NG_JUDGE':
        return <Tag color="blue">{value}</Tag>;
      case 'NG_REASON':
        return <Tag color="blue">{value}</Tag>;
      case 'NG_DISPOSAL':
        return <Tag color="blue">{value}</Tag>;
      case 'NG_REVIEWING':
        return <Tag color="blue">{value}</Tag>;
      case 'REVIEWED':
        return <Tag color="blue">{value}</Tag>;
      case 'CANCEL':
        return <Tag color="gray">{value}</Tag>;
      default:
        return null;
    }
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'ncReportNum',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hwms/ncReport-doc-maintain-new/dist/${record!.get('ncReportId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'siteName' },
      {
        name: 'ncReportStatusDesc',
        width: 130,
        renderer: ({ value, record }) => renderNcReportTag(value, record),
      },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'revisionCode' },
      { name: 'sourceDocNum' },
      { name: 'inspectBusinessTypeDesc', width: 150 },
      { name: 'supplierCode' },
      { name: 'supplierName' },
      { name: 'locatorCode' },
      { name: 'locatorName' },
      { name: 'operationName' },
      { name: 'operationDesc' },
      { name: 'productionLineCode' },
      { name: 'productionLineName' },
      { name: 'recorderPersonDesc' },
      { name: 'creationDate', align: ColumnAlign.center, width: 150 },
      { name: 'createMethodDesc' },
    ];
  }, []);

  const headerRowClick = record => {
    queryLineTable(record?.get('ncReportId'));
  };

  const handleCancel = () => {
    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <p>
          {intl
            .get(`${modelPrompt}.info.cancelNcReport`)
            .d('是否确认取消？')}
        </p>
      ),
      onOk: handleCancelCallback,
    });
  };

  const handleCancelCallback = () => {
    return new Promise(async (resolve) => {
      const data = (headDs.selected || []).map((_record) => ({
        createMethod: _record?.get('createMethod'),
        ncIncidentNum: _record?.get('ncIncidentNum'),
        ncReportId: _record?.get('ncReportId'),
        ncReportNum: _record?.get('ncReportNum'),
        siteId: _record?.get('siteId'),
      }))
      const res = await canNcReportDoc({
        params: data,
      });
      if (res && res.success) {
        (res?.rows || []).forEach((item) => {
          const { code, message, ncIncidentNum } = item || {};
          if (code === "1") {
            notification.error({ message: `${ncIncidentNum}: ${message}` });
          } else {
            notification.success({
              message: `${ncIncidentNum} ${intl.get(`${modelPrompt}.info.cancelSuccess`).d('取消成功')}`,
            });
          }
        })
        headDs.query(headDs.currentPage);
        return resolve(true)
      }
      notification.error({ message: res.message });
      return resolve(false);
    })
  };

  const handleJumpProblemPlatform = () => {
    history.push({
      pathname: `/hwms/problem-management/problem-management-platform/list`,
      state: {
        stateType: 'create',
      },
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.check`).d('不良评审单管理平台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          disabled={selectInfo.ncReportIds?.length !== 1}
          onClick={handleJumpProblemPlatform}
          permissionList={[
            {
              code: `${path}.button.`,
              type: 'button',
              meaning: '问题创建按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.problemCreate').d('生成问题')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          onClick={handleCancel}
          permissionList={[
            {
              code: `${path}.button.cancel`,
              type: 'button',
              meaning: '列表页-取消按钮',
            },
          ]}
          disabled={
            selectInfo.ncReportIds.length === 0 ||
            selectInfo.ncReportStatus.some(item => item !== 'NEW')
          }
        >
          {intl.get('tarzan.common.button.cancel').d('取消')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={headDs}
            columns={headColumns}
            searchCode="ncReportDocMaintain1"
            customizedCode="ncReportDocMaintain-listHeader"
            onRow={({ record }) => ({
              onClick: () => headerRowClick(record),
            })}
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.LIST`,
        `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.LINE.LIST`,
      ],
    })(NcReportList as any),
  ),
);
