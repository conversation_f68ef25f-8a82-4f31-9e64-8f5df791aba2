import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`;
const userInfo = getCurrentUser();

const BasicMessageDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.string,
      name: 'temporaryPermitNum',
      label: intl.get(`${modelPrompt}.table.temporaryPermitNum`).d('变更单号'),
      disabled: true,
    },
    {
      type: FieldType.string,
      name: 'temporaryPermitStatus',
      lookupCode: 'YP.QIS.TEMP_PERMIT_DOC_STATUS',
      label: intl.get(`${modelPrompt}.table.temporaryPermitStatus`).d('状态'),
      disabled: true,
    },
    {
      type: FieldType.object,
      name: 'siteObj',
      required: true,
      lovCode: 'MT.MODEL.SITE',
      valueField: 'siteId',
      textField: 'siteCode',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.table.siteObj`).d('站点'),
    },
    {
      name: 'siteId',
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteObj.siteCode',
    },
    {
      type: FieldType.string,
      name: 'createdByName',
      disabled: true,
      defaultValue: userInfo.realName,
      label: intl.get(`${modelPrompt}.table.createdByName`).d('发起人'),
    },
    {
      type: FieldType.object,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      name: 'unitNameObj',
      textField: 'unitName',
      valueField: 'unitId',
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.table.unitNameObj`).d('发起专业'),
      lovPara:{
        tenantId,
      },
    },
    {
      name: 'temporaryPermitDeptId',
      bind: 'unitNameObj.unitId',
    },
    {
      name: 'temporaryPermitDeptName',
      bind: 'unitNameObj.unitName',
    },
    {
      type: FieldType.string,
      name: 'inspectBusinessTypeList',
      lookupCode: 'YP.QIS.TEMP_PERMIT_BUS_TYPE',
      multiple: true,
      required: true,
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.table.inspectBusinessTypeList`).d('检验业务类型'),
    },
    {
      type: FieldType.object,
      name: 'materialCodeObj',
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      valueField: 'materialId',
      textField: 'materialCode',
      required: true,
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.table.materialCodeObj`).d('物料编码'),
    },
    {
      name: 'materialCode',
      bind: 'materialCodeObj.materialCode',
    },
    {
      name: 'materialId',
      bind: 'materialCodeObj.materialId',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.table.materialName`).d('物料描述'),
      disabled: true,
      bind: 'materialCodeObj.materialName',
    },
    {
      type: FieldType.object,
      name: 'prodLineCodeObj',
      lovCode: 'MT.MODEL.PRODLINE',
      valueField: 'prodLineId',
      textField:'prodLineCode',
      lovPara:{
        tenantId,
      },
      required:true,
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.table.prodLineCodeObj`).d('产线编码'),
    },
    {
      name:'prodLineId',
      bind:'prodLineCodeObj.prodLineId',
    },
    {
      name:'prodLineCode',
      bind:'prodLineCodeObj.prodLineCode',
    },
    {
      name:'prodLineName',
      disabled: true,
      label: intl.get(`${modelPrompt}.table.prodLineName`).d('产线描述'),
      bind:'prodLineCodeObj.prodLineName',
    },
    {
      type: FieldType.object,
      name: 'operationNameObj',
      lovCode: 'MT.METHOD.OPERATION',
      valueField: 'operationId',
      textField:'operationName',
      lovPara:{
        tenantId,
      },
      required:true,
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.table.prodLineCodeObj`).d('工艺编码'),
    },
    {
      name:'operationId',
      bind:'operationNameObj.operationId',
    },
    {
      name:'operationName',
      bind:'operationNameObj.operationName',
    },
    {
      name:'operationDescription',
      disabled: true,
      label: intl.get(`${modelPrompt}.table.operationDescription`).d('工艺描述'),
      bind:'operationNameObj.description',
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.EQUIPMENT_SCOPE',
      required: true,
      name: 'equipmentScope',
      label: intl.get(`${modelPrompt}.table.equipmentScope`).d('设备范围'),
    },
    {
      type: FieldType.object,
      name: 'equipmentNameObj',
      label: intl.get(`${modelPrompt}.table.equipmentNameObj`).d('设备名称'),
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps:{
        required:({record})=>record.get('equipmentScope')==='SELECT',
        disabled:({record})=>record.get('equipmentScope')!=='SELECT',
        lovCode:({record})=>record.get('equipmentScope')==='SELECT'?'MT.MODEL.EQUIPMENT': '',
        lovPara: ({record})=>({
          tenantId,
          equipmentScope: record.get('equipmentScope'),
          organizationIds: record.get('prodLineId'),
        }),
      },
    },
    {
      name:'equipmentIdList',
      bind: 'equipmentNameObj.equipmentId',
    },
    {
      name:'equipmentCodeList',
      bind: 'equipmentNameObj.equipmentCode',
    },
    {
      name: 'guideBookFlag',
      label: intl.get(`${modelPrompt}.table.guideBookFlag`).d('是否有要领书'),
      type:FieldType.string,
      defaultValue: 'N',
      falseValue: 'N',
      trueValue: 'Y',
    },
    {
      type: FieldType.dateTime,
      name: 'scheduleStartDate',
      required: true,
      label: intl.get(`${modelPrompt}.table.scheduleStartDate`).d('发起日'),
    },
    {
      type: FieldType.dateTime,
      name: 'scheduleEndDate',
      min: 'scheduleStartDate',
      required: true,
      label: intl.get(`${modelPrompt}.table.scheduleEndDate`).d('到期日'),
      dynamicProps:{
        max: ({record,dataSet})=> {
          return new Date(+record.get('scheduleStartDate')+dataSet.getState('configurationValue')) || undefined
        },
      },
    },
    {
      type: FieldType.dateTime,
      name: 'creationDate',
      required: true,
      disabled: true,
      defaultValue: new Date(),
      label: intl.get(`${modelPrompt}.table.creationDate`).d('创建日'),
    },
    {
      type: FieldType.string,
      name: 'impactDesc',
      required: true,
      label: intl.get(`${modelPrompt}.table.impactDesc`).d('暂允对品质的影响'),
    },
    {
      type: FieldType.string,
      name: 'inspectSchemeCode',
      label: intl.get(`${modelPrompt}.table.inspectSchemeCode`).d('检验方案编码'),
    },
    {
      type: FieldType.string,
      name: 'enclosure',
      label: intl.get(`${modelPrompt}.table.enclosure`).d('附件'),
    },
  ],
});



export {
  BasicMessageDS,
};