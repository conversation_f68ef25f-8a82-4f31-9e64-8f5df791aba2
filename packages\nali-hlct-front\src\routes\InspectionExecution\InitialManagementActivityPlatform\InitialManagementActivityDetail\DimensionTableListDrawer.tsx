/**
 * @Description: 检验方案维护-详情-新增维度抽屉
 * @Author: <<EMAIL>>
 * @Date: 2023-01-09 17:48:04
 * @LastEditTime: 2023-05-18 16:41:35
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useState } from 'react';
import { Table, Lov, Select, TextField } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { Button as PermissionButton } from 'components/Permission';
import uuid from 'uuid/v4';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { fetchRuleDtlConfig, fetchMaterialLimitDefaultConfig } from '../services';

const disabledListMapWork = {
  areaObject: 'prodLineObject,processObject,stationObject',
  prodLineObject: 'areaObject,processObject,stationObject',
  processObject: 'areaObject,prodLineObject,stationObject',
  stationObject: 'areaObject,prodLineObject,processObject',
};

const disabledListMapRelationship = {
  supplierObject: 'customerObject',
  customerObject: 'supplierObject',
};

const DimensionTableList = props => {
  const { formDs, inspectionItemBasisDs, tableDs, canEdit, path, customizeTable } = props;

  // 检验业务类型列表可输入权限
  const [ruleDtl, setRuleDtl] = useState({});
  const [materialLimitDefault, setMaterialLimitDefault] = useState({});

  // 获取请选择检验业务类型列表
  const fetchRuleDtl = useRequest(fetchRuleDtlConfig(), {
    manual: true,
  });

  // 根据物料和站点获取默认工艺路线
  const fetchMaterialLimitDefault = useRequest(fetchMaterialLimitDefaultConfig(), {
    manual: true,
  });

  useEffect(() => {
    tableDs.forEach(record => {
      if (record.get('areaId')) {
        record.set('disabledListWork', disabledListMapWork.areaObject);
      }
      if (record.get('prodLineId')) {
        record.set('disabledListWork', disabledListMapWork.prodLineObject);
      }
      if (record.get('processWorkcellId')) {
        record.set('disabledListWork', disabledListMapWork.processObject);
      }
      if (record.get('stationWorkcellId')) {
        record.set('disabledListWork', disabledListMapWork.stationObject);
      }

      if (record.get('supplierId')) {
        record.set('disabledListMapRelationship', disabledListMapRelationship.supplierObject);
      }
      if (record.get('customerId')) {
        record.set('disabledListMapRelationship', disabledListMapRelationship.customerObject);
      }
    });
    fetchRuleDtl.run({
      params: {
        inspectBusinessType: inspectionItemBasisDs.current.get('inspectBusinessType'),
        siteId: formDs.current.get('siteId'),
      },
      onSuccess: res => {
        setRuleDtl(res || {});
        tableDs.forEach(record => {
          record.set('ruleDtl', res || {});
        });
      },
    });

    if (formDs.current.get('inspectSchemeObjectType') === 'MATERIAL') {
      fetchMaterialLimitDefault.run({
        params: {
          materialId: formDs.current.get('inspectSchemeObjectId'),
          siteId: formDs.current.get('siteId'),
        },
        onSuccess: res => {
          setMaterialLimitDefault(res || {});
        },
      });
    }
  }, []);

  const tableLovChangeWork = (value, record, recordName) => {
    if (value) {
      record.set('disabledListWork', disabledListMapWork[recordName]);
    } else {
      record.set('disabledListWork', null);
    }
  };

  const tableLovChangeRelationship = (value, record, recordName) => {
    if (value) {
      record.set('disabledListMapRelationship', disabledListMapRelationship[recordName]);
    } else {
      record.set('disabledListMapRelationship', null);
    }
  };

  const handleAddLine = () => {
    const formDsData: any = formDs.toData()[0] || {};
    let newRow: any = {
      uuid: uuid(),
      sequence: tableDs.length * 10 + 10,
      siteId: formDsData.siteId || null,

      ruleDtl,
    };

    if (formDsData.inspectSchemeObjectType === 'MATERIAL') {
      newRow = {
        ...newRow,
        materialId: formDsData.inspectSchemeObjectId || null,
        materialCode: formDsData.inspectSchemeObjectCode || null,
        materialName: formDsData.inspectSchemeObjectName || null,
        revisionCode: formDsData.revisionCode || null,
      };
    } else {
      newRow = {
        ...newRow,
        materialCategoryId: formDsData.inspectSchemeObjectId || null,
        categoryCode: formDsData.inspectSchemeObjectCode || null,
        materialCategoryName: formDsData.inspectSchemeObjectName || null,
        revisionCode: formDsData.revisionCode || null,
      };
    }

    tableDs.create(newRow);
  };
  const deleteRecord = record => {
    tableDs.delete(record, false);

    setTimeout(() => {
      tableResetSequence();
    }, 0);
  };

  const tableResetSequence = () => {
    tableDs.forEach((item, index) => {
      item.init('sequence', index * 10 + 10);
    });
  };

  const fillInRouter = (record, name) => {
    const lovDs = tableDs.getField(name).getOptions(record);
    lovDs.queryDataSet.forEach(_record => {
      // @ts-ignore
      _record.set('routerId', materialLimitDefault.routerId);
      // @ts-ignore
      _record.set('routerName', materialLimitDefault.routerName);
    });
  };

  const columnsM: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          onClick={handleAddLine}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit || record?.index === 0}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
    },
    {
      name: 'schemeStage',
      editor: record =>
        formDs.getState('inspectBusinessType') && (
          <Select onChange={value => {
            tableLovChangeWork(value, record, 'schemeStage');
          }}
          />
        ),
    },
    {
      name: 'initialActivityDocNum',
      editor: record => (<TextField onChange={value => {
        tableLovChangeWork(value, record, 'initialActivityDocNum');
      }} />),
    },
    {
      name: 'validDateFrom',
      editor: record => (<TextField onChange={value => {
        tableLovChangeWork(value, record, 'validDateFrom');
      }} />),
    },
    {
      name: 'validDateTo',
      editor: record => (<TextField onChange={value => {
        tableLovChangeWork(value, record, 'validDateTo');
      }} />),
    },
    {
      name: 'materialName',
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'areaObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeWork(value, record, 'areaObject');
            }}
          />
        ),
    },
    {
      name: 'prodLineObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeWork(value, record, 'prodLineObject');
            }}
          />
        ),
    },
    {
      name: 'processObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeWork(value, record, 'processObject');
            }}
            onFocus={() => {
              setTimeout(() => {
                fillInRouter(record, 'processObject');
              }, 100);
            }}
          />
        ),
    },
    {
      name: 'stationObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeWork(value, record, 'stationObject');
            }}
            onFocus={() => {
              setTimeout(() => {
                fillInRouter(record, 'stationObject');
              }, 100);
            }}
          />
        ),
    },
    {
      name: 'equipmentObject',
      editor: () => canEdit && <Lov autoSelectSingle={false} />,
    },
    {
      name: 'operationObject',
      editor: () => canEdit && <Lov autoSelectSingle={false} />,
    },

    {
      name: 'supplierObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeRelationship(value, record, 'supplierObject');
            }}
          />
        ),
    },
    {
      name: 'customerObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeRelationship(value, record, 'customerObject');
            }}
          />
        ),
    },
  ];

  const columnsC: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          onClick={handleAddLine}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit || record?.index === 0}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
    },
    {
      name: 'schemeStage',
      editor: record =>
        formDs.getState('inspectBusinessType') && (
          <Select onChange={value => {
            tableLovChangeWork(value, record, 'schemeStage');
          }}
          />
        ),
    },
    {
      name: 'initialActivityDocNum',
    },
    {
      name: 'validDateFrom',
    },
    {
      name: 'validDateTo',
    },
    {
      name: 'materialCategoryName',
      width: 120,
    },
    {
      name: 'areaObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeWork(value, record, 'areaObject');
            }}
          />
        ),
    },
    {
      name: 'prodLineObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeWork(value, record, 'prodLineObject');
            }}
          />
        ),
    },
    {
      name: 'processObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeWork(value, record, 'processObject');
            }}
            onFocus={() => {
              setTimeout(() => {
                fillInRouter(record, 'processObject');
              }, 100);
            }}
          />
        ),
    },
    {
      name: 'stationObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeWork(value, record, 'stationObject');
            }}
            onFocus={() => {
              setTimeout(() => {
                fillInRouter(record, 'stationObject');
              }, 100);
            }}
          />
        ),
    },
    {
      name: 'equipmentObject',
      editor: () => canEdit && <Lov autoSelectSingle={false} />,
    },
    {
      name: 'operationObject',
      editor: () => canEdit && <Lov autoSelectSingle={false} />,
    },

    {
      name: 'supplierObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeRelationship(value, record, 'supplierObject');
            }}
          />
        ),
    },
    {
      name: 'customerObject',
      editor: record =>
        canEdit && (
          <Lov
            autoSelectSingle={false}
            onChange={value => {
              tableLovChangeRelationship(value, record, 'customerObject');
            }}
          />
        ),
    },
  ];

  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_BASIC.DMS_DETAIL`,
    },
    <Table
      dataSet={tableDs}
      columns={formDs.current.get('inspectSchemeObjectType') === 'MATERIAL' ? columnsM : columnsC}
    />,
  );
};
export default DimensionTableList;
