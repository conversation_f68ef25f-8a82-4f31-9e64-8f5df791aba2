/*
 * @Description: 人员角色组件
 * @Author: <<EMAIL>>
 * @Date: 2023-10-27 14:16:13
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-15 14:55:23
 */
import React from 'react';
import intl from 'utils/intl';
import { Tooltip } from 'choerodon-ui';
import styles from '../index.module.less';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';

export default ({ userRole }) => {
  const roleList = [
    {
      code: 'REGISTER_PERSON',
      name: intl.get(`${modelPrompt}.role.registerPerson`).d('登记人'),
      displayName: intl.get(`${modelPrompt}.displayName.registerPerson`).d('登'),
      description: intl.get(`${modelPrompt}.roleDesc.registrationBasicInformation`).d('登记问题基本信息'),
    },
    {
      code: 'LEAD_PERSON',
      name: intl.get(`${modelPrompt}.role.leadPerson`).d('跟进人'),
      displayName: intl.get(`${modelPrompt}.displayName.leadPerson`).d('跟'),
      description: intl
        .get(`${modelPrompt}.roleDesc.leadPerson`)
        .d(
          '1.补充问题信息；2.创建问题小组；<br />3.对措施和原因进行评价；4.小组成员评分；<br />5.冻结问题',
        ),
    },
    {
      code: 'RESPONSIBLE_PERSON',
      name: intl.get(`${modelPrompt}.role.responsiblePerson`).d('责任人'),
      displayName: intl.get(`${modelPrompt}.displayName.responsiblePerson`).d('责'),
      description: intl.get(`${modelPrompt}.roleDesc.fillInTheMeasuresAndReasons`).d('填写措施和原因'),
    },
    {
      code: 'MAJOR_RESPONSIBLE_PERSON',
      name: intl.get(`${modelPrompt}.role.principalResponsiblePerson`).d('主责人'),
      displayName: intl.get(`${modelPrompt}.displayName.majorResponsiblePerson`).d('主'),
      description: intl
        .get(`${modelPrompt}.roleDesc.majorResponsiblePerson`)
        .d('1.填写措施和原因；2.关闭问题；<br />3.冻结问题'),
    },
  ];

  const getRoleContent = info => {
    const { name, description } = info;
    const descriptionList = description.split('<br />');
    return (
      <div>
        <p>{name}</p>
        {descriptionList.map(item => (
          <p>{item}</p>
        ))}
      </div>
    );
  };

  return (
    <div className={styles['user-role-component']}>
      {roleList.map(roleInfo => (
        <Tooltip title={getRoleContent(roleInfo)} placement="bottom" theme="dark">
          <div
            className={
              userRole.some(item => item === roleInfo.code)
                ? `${styles['user-role-item']} ${styles['user-role-item-selected']}`
                : styles['user-role-item']
            }
          >
            {roleInfo.displayName}
          </div>
        </Tooltip>
      ))}
    </div>
  );
};
