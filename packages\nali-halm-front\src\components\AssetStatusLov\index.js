/**
 * 资产状态组件
 * @date: 2021/03/09
 * <AUTHOR>
 * @copyright Copyright (c) 2020, Hand
 */
import React from 'react';
import { Icon } from 'choerodon-ui';
import { TextField, TextArea, Modal, Table, DataSet, Button, Select } from 'choerodon-ui/pro';

import { isFunction, isUndefined, toLower } from 'lodash';
import { Bind, Throttle } from 'lodash-decorators';
import notification from 'utils/notification';
import intl from 'utils/intl';

import getLang from './Langs';
import styles from './index.module.less';
import { dataDS } from './AssetStatusDS';

export default class AssetStatusLov extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.dataDS = new DataSet(dataDS());
    this.offReasonDS = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'content',
          type: 'string',
          defaultValue: '',
        },
        {
          name: 'offType',
          type: 'string',
          lookupCode: 'HALM.OFF_TYPE',
          required: true,
        },
      ],
    });
  }

  /**
   * 初始化tableColumns
   */
  @Bind()
  initTableColumns() {
    const columns = {};
    this.dataDS.forEach(i => {
      if (isUndefined(i.get('assetStatusId'))) {
        columns[i.get('code')] = [{ name: 'assetStatusName' }];
      }
    });
    return columns;
  }

  /**
   * 初始化需要的dataSet
   * @param {dataSet} header 从list获取的头状态
   * @returns dataSets
   */
  initTableDataSet(header) {
    const dataSets = {};
    header.forEach(i => {
      dataSets[i.get('code')] = new DataSet({
        autoQuery: false,
        selection: 'single',
        paging: false,
        primaryKey: 'assetStatusId',
        fields: [
          {
            name: 'assetStatusName',
            type: 'string',
            label: i.get('assetStatusName'),
          },
        ],
      });
    });
    return dataSets;
  }

  /**
   * 初始化table数据
   * @param {object} dataSets 多个dataSet对象,以code为对象名
   */
  initTableData(dataSets) {
    Object.keys(dataSets).forEach(i => {
      this.dataDS.forEach(x => {
        if (x.get('parentCode') === i) {
          dataSets[i].create({ ...x.toData() });
        }
      });
    });
  }

  /**
   * 打开资产状态Lov时需要勾选已选数据
   * @param {object} values 传入的值
   * @param {object} dataSets 多个dataSet对象,以code为对象名
   */
  initTableSelected(values = {}, dataSets) {
    Object.keys(dataSets).forEach(i => {
      dataSets[i].forEach(x => {
        if (
          !isUndefined(values[toLower(i)]) &&
          Number(x.get('assetStatusId'), 10) === Number(values[toLower(i)], 10)
        ) {
          dataSets[i].select(x);
        }
      });
    });
  }

  /**
   * 点击搜索图标
   */
  @Bind()
  @Throttle(2000)
  async onSearchBtnClick() {
    const {
      value,
      disabled,
      record,
      meterFlag,
      handleOk,
      ds,
      valueField = 'multiStatusDTO',
    } = this.props;
    if (disabled) return;
    // 这是因为在动态字段里处理了该组件 而动态字段的组件只在setDSFields调用那一刻渲染 导致该组件接收的record value会一直是组件生成那一刻的
    // 为了解决这个问题 增加了ds 与 valueField 字段
    const _record = ds ? ds.current : record;
    const _value = ds ? ds.current.toData()[valueField] : value;
    if (_record.get('assetSetId')) {
      this.dataDS.setQueryParameter('assetSetId', _record.get('assetSetId'));
    }
    if (_record.get('assetLocationId')) {
      this.dataDS.setQueryParameter('assetLocationId', _record.get('assetLocationId'));
    }
    this.dataDS.setQueryParameter('lovFlag', 1);
    await this.dataDS.query();
    const header = this.dataDS.filter(i => isUndefined(i.get('assetStatusId')));
    const columns = this.initTableColumns();
    const dataSets = this.initTableDataSet(header);
    this.initTableData(dataSets);
    this.initTableSelected(_value, dataSets);

    const statusModal = Modal.open({
      key: 'assetStatusLovModal',
      title: getLang('ASSET_STATUS'),
      closable: true,
      style: {
        width: '7.5rem',
        minHeight: '3.5rem',
      },
      footer: [
        <Button onClick={() => statusModal.close()}>{getLang('CANCEL')}</Button>,
        <Button
          color="primary"
          onClick={() =>
            this.handleModalClick({ dataSets, meterFlag, handleOk, record: _record, statusModal })
          }
        >
          {getLang('SURE')}
        </Button>,
      ],
      children: (
        <div style={{ display: 'flex' }}>
          {header.map((i, index) => {
            return (
              <Table
                style={
                  index === 0
                    ? { marginRight: '-1px' }
                    : index === header.length - 1
                    ? { marginLeft: '-1px' }
                    : {}
                }
                selectedHighLightRow={false}
                alwaysShowRowBox
                dataSet={dataSets[i.get('code')]}
                columns={columns[i.get('code')]}
                queryFieldsLimit={1}
              />
            );
          })}
        </div>
      ),
    });
  }

  handleModalClick({ dataSets, meterFlag, handleOk, record, statusModal }) {
    const statusObj = {};
    const statusNames = [];
    Object.keys(dataSets).forEach(i => {
      if (dataSets[i].length > 0 && dataSets[i].selected.length > 0) {
        statusObj[toLower(i)] = Number(dataSets[i].selected[0].get('assetStatusId'), 10);
        statusNames.push(dataSets[i].selected[0].get('assetStatusName'));
      }
    });
    if (dataSets.MANAGE.selected.length > 0) {
      if (isFunction(handleOk)) {
        handleOk(statusObj, statusNames.join(','));
      }
    } else {
      notification.error({
        message: intl.get('alm.common.view.message.pleaseSelectManage').d('请选择管理状态'),
      });
      return Promise.reject();
    }
    const selected = dataSets.PHYSICS.selected.map(i => i.toData());
    const initData = record.getPristineValue('multiStatusDTO');
    if (
      !isUndefined(record.toData()._token) &&
      meterFlag &&
      selected.length > 0 &&
      (selected[0].assetStatusCode === 'OFF' ||
        selected[0].assetStatusCode === 'ALM.Maintain.OFF') &&
      Number(selected[0].assetStatusId, 10) !== initData.physics &&
      record.get('meterDescFlag')
    ) {
      this.handleOpenoffReason(record, statusModal);
    } else {
      record.set('meterReadingDesc', undefined);
      if (statusModal) {
        statusModal.close();
      }
    }
  }

  handleOpenoffReason(record, statusModal) {
    return new Promise(resolve => {
      Modal.open({
        key: 'offReasonModal',
        title: getLang('ASSET_STATUS'),
        closable: true,
        children: (
          <>
            <div className={styles['off-type']}>{getLang('OFF_TYPE')}</div>
            <Select name="offType" cols={100} dataSet={this.offReasonDS} />
            <div>{getLang('OFF_REASON')}</div>
            <TextArea name="content" cols={100} dataSet={this.offReasonDS} />
          </>
        ),
        onOk: async () => {
          record.set('meterReadingDesc', this.offReasonDS.current.get('content'));
          record.set('offType', this.offReasonDS.current.get('offType'));
          const flag = await this.offReasonDS.validate();
          if (statusModal && flag) {
            statusModal.close();
            return resolve();
          } else {
            return false;
          }
        },
      });
    });
  }

  /**
   * @param {String} value - 输入框内的值
   */
  @Bind()
  getText(value) {
    const { handleOk } = this.props;
    if (value === null) {
      handleOk('', '');
    }
  }

  render() {
    const { name, disabled = false } = this.props;
    const suffix = <Icon type="LOV-o" onClick={this.onSearchBtnClick} />;
    return (
      <React.Fragment>
        <TextField
          className="c7n-pro-cus-lov"
          name={name}
          clearButton
          suffix={suffix}
          style={{ width: '100%' }}
          disabled={disabled}
          restrict="\n"
          onDoubleClick={this.onSearchBtnClick}
          onChange={e => this.getText(e)}
        />
      </React.Fragment>
    );
  }
}
