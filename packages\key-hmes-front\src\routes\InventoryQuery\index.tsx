import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from './stores';

const modelPrompt = 'inventoryQuery';

const InventoryQuery = (props) => {
  const {
    tableDs,
  } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'index',
        align: ColumnAlign.center,
      },
      {
        name: 'locatorName',
      },
      {
        name: 'materialCategory',
      },
      {
        name: 'customCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'materialCode',
      },
      {
        name: 'bomCode',
      },
      {
        name: 'model',
      },
      {
        name: 'materialQuality',
      },
      {
        name: 'onHandQty',
      },
      {
        name: 'inventoryQuantity',
        width: 150,
      },
      {
        name: 'outboundQuantity',
        width: 150,
      },
      {
        name: 'primaryUomCode',
      },
      {
        name: 'lot',
        width: 180,
      },
      {
        name: 'grade',
      },
      {
        name: 'property',
      },
    ];
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('库存查询报表')} />
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="inventoryQuery"
          customizedCode="inventoryQuery"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(InventoryQuery),
);
