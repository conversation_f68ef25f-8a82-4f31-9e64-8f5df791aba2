/**
 * @Description: 控制控制图-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-11-25 11:15:16
 * @LastEditTime: 2022-06-16 16:55:51
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useEffect } from 'react';
import {
  Button,
  Form,
  TextField,
  Switch,
  DataSet,
  Select,
  NumberField,
  Lov,
} from 'choerodon-ui/pro';
import { Spin, Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import {
  controlChartDS,
  controlChartInfoDS,
  controlChartDetailInfoDS,
  sampleDataFilteringRulesDS,
  groupBatchRuleDS,
} from '../stores/ControlChartDS';
import { FetchControlChartDetail, SaveControlChartDetail } from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hspc.controlChartMaintain';

const JudgementRuleGroupDetail = (props: any) => {
  const { id } = props.match.params;
  const {
    match: { path },
    // history,
  } = props;

  const [canEdit, setCanEdit] = useState(false);

  const controlChartDs = useMemo(() => new DataSet({ ...controlChartDS() }), []);
  const controlChartInfoDs = useMemo(() => new DataSet({ ...controlChartInfoDS() }), []);
  const mainControlChartDetailInfoDs = useMemo(
    () => new DataSet({ ...controlChartDetailInfoDS() }),
    [],
  );
  const secondaryControlChartDetailInfoDs = useMemo(
    () => new DataSet({ ...controlChartDetailInfoDS() }),
    [],
  );
  const sampleDataFilteringRulesDs = useMemo(
    () => new DataSet({ ...sampleDataFilteringRulesDS() }),
    [],
  );
  const groupBatchRuleDs = useMemo(() => new DataSet({ ...groupBatchRuleDS() }), []);

  const fetchControlChartDetail = useRequest(FetchControlChartDetail(), { manual: true });
  const saveControlChartDetail = useRequest(SaveControlChartDetail(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    pageQuery();
  }, [id]);

  const pageQuery = () => {
    fetchControlChartDetail.run({
      params: {
        controlId: id,
      },
      onSuccess: res => {
        const {
          // form1
          controlId,
          controlCode,
          controlDesc,
          accessType,
          serviceCode,
          enableFlag,
          // form2
          chartType,
          subgroupSize,
          maxPlotPoints,
          chartTitle,
          xTickLabel,
          // form3
          mainChartConfigure,
          // form4
          secondaryChartConfigure,
          // form5
          samplingType,
          timeSampling,
          timeSamplingNumber,
          dataUpperLimit,
          samplingPosition,
          isometricSampling,
          isometricSamplingNumber,
          dataLowerLimit,
          // form6
          groupBatchRule,
          groupBatchRuleDesc,
          timeGroupBatch,
          sampleGroupBatch,
          unqualifiedGroupBatch,
        } = res;
        controlChartDs!.current!.init({
          controlId,
          controlCode,
          controlDesc,
          accessType,
          serviceCode,
          enableFlag,
        });
        controlChartInfoDs!.current!.init({
          chartType,
          subgroupSize,
          maxPlotPoints,
          chartTitle,
          xTickLabel,
        });
        mainControlChartDetailInfoDs!.current!.init({
          ...mainChartConfigure,
        });
        if (secondaryChartConfigure) {
          secondaryControlChartDetailInfoDs!.current!.init({
            ...secondaryChartConfigure,
          });
        }
        sampleDataFilteringRulesDs!.current!.init({
          samplingType,
          timeSampling,
          timeSamplingNumber,
          dataUpperLimit,
          samplingPosition,
          isometricSampling,
          isometricSamplingNumber,
          dataLowerLimit,
        });
        groupBatchRuleDs!.current!.init({
          groupBatchRule,
          groupBatchRuleDesc,
          timeGroupBatch,
          sampleGroupBatch,
          unqualifiedGroupBatch,
        });
      },
    });
  };

  const handleSave = async () => {
    const saveParams = {
      ...controlChartDs!.current!.toData(),
      ...controlChartInfoDs!.current!.toData(),
      ...sampleDataFilteringRulesDs!.current!.toData(),
      ...groupBatchRuleDs!.current!.toData(),
      mainChartConfigure: { ...mainControlChartDetailInfoDs!.current!.toData() },
    };
    if (fetchControlChartDetail.data.secondaryChartConfigure) {
      saveParams.secondaryChartConfigure = {
        ...secondaryControlChartDetailInfoDs!.current!.toData(),
      };
    }
    await saveControlChartDetail.run({
      params: saveParams,
      onSuccess: () => {
        notification.success({});
        setCanEdit(false);
        pageQuery();
      },
    });
  };

  const handleCancel = () => {
    controlChartDs!.current!.reset();
    controlChartInfoDs!.current!.reset();
    mainControlChartDetailInfoDs!.current!.reset();
    secondaryControlChartDetailInfoDs!.current!.reset();
    sampleDataFilteringRulesDs!.current!.reset();
    groupBatchRuleDs!.current!.reset();
    setCanEdit(prev => !prev);
  };

  const changeSamplingType = val => {
    if (val === 'TIME') {
      sampleDataFilteringRulesDs!.current!.init('isometricSamplingNumber');
      sampleDataFilteringRulesDs!.current!.init('isometricSampling');
    } else if (val === 'ISOMETRIC') {
      sampleDataFilteringRulesDs!.current!.init('timeSamplingNumber');
      sampleDataFilteringRulesDs!.current!.init('timeSampling');
    } else {
      sampleDataFilteringRulesDs!.current!.init('isometricSamplingNumber');
      sampleDataFilteringRulesDs!.current!.init('isometricSampling');
      sampleDataFilteringRulesDs!.current!.init('timeSamplingNumber');
      sampleDataFilteringRulesDs!.current!.init('timeSampling');
    }
  };

  const changeGroupBatchRule = val => {
    if (val === 'TIME_GROUP_BATCH') {
      groupBatchRuleDs!.current!.init('sampleGroupBatch');
      groupBatchRuleDs!.current!.init('unqualifiedGroupBatch');
    } else if (val === 'sampleGroupBatch') {
      groupBatchRuleDs!.current!.init('timeGroupBatch');
      groupBatchRuleDs!.current!.init('unqualifiedGroupBatch');
    } else if (val === 'unqualifiedGroupBatch') {
      groupBatchRuleDs!.current!.init('timeGroupBatch');
      groupBatchRuleDs!.current!.init('sampleGroupBatch');
    } else {
      groupBatchRuleDs!.current!.init('timeGroupBatch');
      groupBatchRuleDs!.current!.init('sampleGroupBatch');
      groupBatchRuleDs!.current!.init('unqualifiedGroupBatch');
    }
  };

  return (
    <div className="hmes-style">
      <Spin spinning={fetchControlChartDetail.loading}>
        <Header
          title={intl.get(`${modelPrompt}.editControlChart`).d('编辑控制控制图')}
          backPath="/hspc/control-chart/list"
        >
          {canEdit && (
            <>
              <PermissionButton
                type="c7n-pro"
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </PermissionButton>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && (
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              onClick={() => {
                setCanEdit(prev => !prev);
              }}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'controlChartCard',
              'controlChartInfo',
              'mainControlChartDetails',
              'secondaryControlChartDetails',
              'sampleDataFilteringRules',
              'groupBatchRuleInfo',
            ]}
          >
            <Panel
              header={intl.get(`${modelPrompt}.controlChartCard`).d('控制控制图')}
              key="controlChartCard"
              dataSet={controlChartDs}
            >
              <Form disabled={!canEdit} labelWidth={112} dataSet={controlChartDs} columns={3}>
                <TextField name="controlCode" disabled />
                <TextField name="controlDesc" />
                <Select name="accessType" disabled />
                <TextField name="serviceCode" />
                <Switch name="enableFlag" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.controlChartInfo`).d('控制图信息')}
              key="controlChartInfo"
              dataSet={controlChartInfoDs}
            >
              <Form disabled={!canEdit} labelWidth={112} dataSet={controlChartInfoDs} columns={3}>
                <Select name="chartType" disabled />
                <NumberField name="subgroupSize" disabled />
                <NumberField name="maxPlotPoints" />
                <TextField name="chartTitle" />
                <Select name="xTickLabel" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.mainControlChartDetails`).d('主要控制图详细信息')}
              key="mainControlChartDetails"
              dataSet={mainControlChartDetailInfoDs}
            >
              <Form
                disabled={!canEdit}
                labelWidth={112}
                dataSet={mainControlChartDetailInfoDs}
                columns={3}
              >
                <NumberField name="upperControlLimit" disabled />
                <NumberField name="upperSpecLimit" />
                <Lov name="judgementGroup" />
                <NumberField name="centerLine" disabled />
                <NumberField name="specTarget" />
                <TextField name="xAxisLabel" />
                <NumberField name="lowerControlLimit" disabled />
                <NumberField name="lowerSpecLimit" />
                <TextField name="yAxisLabel" />
              </Form>
            </Panel>
            {fetchControlChartDetail.data?.secondaryChartConfigure ? (
              <Panel
                header={intl
                  .get(`${modelPrompt}.secondaryControlChartDetails`)
                  .d('次要控制图详细信息')}
                key="secondaryControlChartDetails"
                dataSet={secondaryControlChartDetailInfoDs}
              >
                <Form
                  disabled={!canEdit}
                  labelWidth={112}
                  dataSet={secondaryControlChartDetailInfoDs}
                  columns={3}
                >
                  <NumberField name="upperControlLimit" disabled />
                  <NumberField name="upperSpecLimit" />
                  <Lov name="judgementGroup" />
                  <NumberField name="centerLine" disabled />
                  <NumberField name="specTarget" />
                  <TextField name="xAxisLabel" />
                  <NumberField name="lowerControlLimit" disabled />
                  <NumberField name="lowerSpecLimit" />
                  <TextField name="yAxisLabel" />
                </Form>
              </Panel>
            ) : null}
            <Panel
              header={intl.get(`${modelPrompt}.sampleDataFilteringRules`).d('样本数据筛选规则')}
              key="sampleDataFilteringRules"
              dataSet={sampleDataFilteringRulesDs}
            >
              <Form
                disabled={!canEdit}
                labelWidth={112}
                dataSet={sampleDataFilteringRulesDs}
                columns={3}
              >
                <Select name="samplingType" onChange={changeSamplingType} />
                <Select name="samplingPosition" />
                <NumberField name="dataUpperLimit" />
                <NumberField
                  name="timeSampling"
                  addonAfter={intl.get(`${modelPrompt}.second`).d('秒')}
                />
                <NumberField name="timeSamplingNumber" />
                <NumberField name="dataLowerLimit" />
                <NumberField
                  name="isometricSampling"
                  addonAfter={intl.get(`${modelPrompt}.individual`).d('个')}
                />
                <NumberField name="isometricSamplingNumber" />
              </Form>
            </Panel>
            {fetchControlChartDetail.data?.secondaryChartConfigure ? null : (
              <Panel
                header={intl.get(`${modelPrompt}.groupBatchRuleInfo`).d('组批规则信息')}
                key="groupBatchRuleInfo"
                dataSet={groupBatchRuleDs}
              >
                <Form disabled={!canEdit} labelWidth={112} dataSet={groupBatchRuleDs} columns={3}>
                  <Select name="groupBatchRule" onChange={changeGroupBatchRule} />
                  <NumberField
                    name="timeGroupBatch"
                    addonAfter={intl.get(`${modelPrompt}.second`).d('秒')}
                  />
                  <NumberField
                    name="sampleGroupBatch"
                    addonAfter={intl.get(`${modelPrompt}.individual`).d('个')}
                  />
                  <NumberField
                    name="unqualifiedGroupBatch"
                    addonAfter={intl.get(`${modelPrompt}.individual`).d('个')}
                  />
                </Form>
              </Panel>
            )}
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hspc.controlChartMaintain', 'tarzan.hspc.chartInfo', 'tarzan.common'],
})(JudgementRuleGroupDetail);
