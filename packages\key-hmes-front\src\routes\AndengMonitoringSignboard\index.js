import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Form, Lov, Row, Col, Button, Modal, Table } from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import { HZERO_PLATFORM } from '@/utils/config';

import { searchDS, tableDS, timeDS } from './stories';
import styles from './index.module.less';
import { QueryProfiles } from './services';

let timer = null;
const modelPrompt = 'tarzan.hmes.andengMonitoringSignboard';

const AndengMonitoringSignboard = observer(() => {
  const { run: queryProfiles } = useRequest(QueryProfiles(), {
    needPromise: true,
    manual: true,
  });
  const queryProfilesDetail = useRequest({ method: 'GET' }, { manual: true, needPromise: true }); // 异常处理
  // const containerRef = useRef(null);

  const timeDs = useMemo(() => new DataSet(timeDS()), []);
  const searchDs = useMemo(() => new DataSet(searchDS()), []);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const [dataList, setDataList] = useState([]);
  const [full, setFull] = useState(false);

  useDataSetEvent(searchDs.queryDataSet, 'update', () => {
    if (searchDs.queryDataSet.toJSONData()[0]?.classCode) {
      handleQuery();
    } else {
      setDataList([]);
    }
  });
  useEffect(() => {
    queryRefreshTime();
    document.addEventListener('webkitfullscreenchange', escFunction); /* Chrome, Safari and Opera */
    document.addEventListener('mozfullscreenchange', escFunction); /* Firefox */
    document.addEventListener('fullscreenchange', escFunction); /* Standard syntax */
    document.addEventListener('msfullscreenchange', escFunction); /* IE / Edge */
    return () => {
      clear();
      // 销毁时清除监听
      document.removeEventListener('webkitfullscreenchange', escFunction);
      document.removeEventListener('mozfullscreenchange', escFunction);
      document.removeEventListener('fullscreenchange', escFunction);
      document.removeEventListener('MSFullscreenChange', escFunction);
    };
  }, []);
  const escFunction = () => {
    setFull(prevFill => !prevFill);
  };
  const queryRefreshTime = () => {
    queryProfiles({
      params: {
        profileName: 'HME.EQUIPMENT_STATUS_BOARD',
      },
    }).then(res => {
      if (res && res.content && res.content.length) {
        queryProfilesDetail
          .run({
            tempUrl: `${HZERO_PLATFORM}/v1/${getCurrentOrganizationId()}/profiles/${
              res.content[0].profileId
            }`,
          })
          .then(resp => {
            if (resp && resp.profileValueDTOList && resp.profileValueDTOList.length) {
              timeDs.current.set('time', Number(resp.profileValueDTOList[0].value));

              initTimer(Number(resp.profileValueDTOList[0].value));
            }
          });
      }
    });
  };
  const clear = () => {
    clearInterval(timer);
  };
  const initTimer = time => {
    clear();
    if (time) {
      timer = setInterval(() => {
        handleQuery();
      }, [time]);
    }
  };
  const handleQuery = () => {
    setDataList([]);
    if (!searchDs.queryDataSet.toJSONData()[0]?.classCode) {
      searchDs.queryDataSet.reset();
    }
    searchDs.query().then(res => {
      if (res.success) {
        setDataList(res.rows);
        initTimer(timeDs.current?.data?.time);
      }
    });
  };

  const columns = useMemo(() => {
    return [
      {
        name: 'type',
      },
      {
        name: 'statusName',
      },
      {
        name: 'statusCode',
      },
    ];
  }, []);

  const handleShowModal = async (item, equip) => {
    tableDs.loadData([]);
    tableDs.setQueryParameter('equipmentId', equip.equipmentId);
    tableDs.query();
    Modal.open({
      closable: true,
      key: Modal.key(),
      title: equip.equipmentName,
      drawer: false,
      style: {
        width: 720,
      },
      okButton: false,
      children: (
        <div style={{ zIndex: 999 }}>
          <Table dataSet={tableDs} columns={columns} />
        </div>
      ),
    });
  };

  const renderEquipList = () => {
    return dataList.map(item => {
      return (
        <div className={styles.content}>
          <p className={full ? styles['equip-headFull'] : styles['equip-head']}>{item.topic}</p>
          <div className={styles['equip-content']}>
            {item.equipList.length &&
              item.equipList.map(equip => {
                return (
                  <>
                    <div
                      onClick={() => handleShowModal(item, equip)}
                      style={{ background: equip.color }}
                      className={full ? styles['content-itemFull'] : styles['content-item']}
                    >
                      {equip.equipmentName}
                    </div>
                  </>
                );
              })}
          </div>
        </div>
      );
    });
  };
  const handleReset = () => {
    searchDs.queryDataSet.reset();
    searchDs.loadData([]);
    setDataList([]);
  };
  const handleShowAll = () => {
    handleReset();
    handleQuery();
  };

  const handleScreen = () => {
    if (full) {
      document.exitFullscreen();
    } else {
      document.documentElement.requestFullscreen();
    }
  };

  return (
    <div className="hmes-style" style={{ width: '100%', height: '100%' }}>
      <TarzanSpin dataSet={searchDs}>
        {!full && <Header title={intl.get(`${modelPrompt}.title`).d('安灯监控看版')}></Header>}
        <Content>
          {!full && (
            <Row gutter={24} style={{ display: 'flex', alignItems: 'center' }}>
              <Col span={6}>
                <Form columns={1} dataSet={searchDs.queryDataSet}>
                  <Lov name="shiftCodeLov" />
                </Form>
              </Col>
              <Col span={2}>
                <Button color="primary" onClick={() => handleShowAll()}>
                  {intl.get(`${modelPrompt}.showAll`).d('全部展示')}
                </Button>
              </Col>
              <Col span={2}>
                <Button onClick={handleReset}>
                  {intl.get('tarzan.common.button.reset').d('重置')}
                </Button>
              </Col>
              <Col span={2}>
                <Button onClick={handleScreen}>
                  {intl.get('tarzan.common.button.fullScreen').d('全屏')}
                </Button>
              </Col>
            </Row>
          )}
          <div style={{ overflow: 'auto' }}>
            {/* <div ref={containerRef} style={{ overflow: 'auto' }}> */}
            {full && (
              <Row>
                <Col>
                  <Button
                    onClick={handleScreen}
                    style={{ float: 'right', marginRight: '10px' }}
                    color="primary"
                  >
                    {intl.get('tarzan.common.button.fullScreen').d('退出全屏')}
                  </Button>
                </Col>
              </Row>
            )}
            {renderEquipList()}
          </div>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.FeedTankCapacityAdjustment', 'tarzan.common'],
})(AndengMonitoringSignboard);
