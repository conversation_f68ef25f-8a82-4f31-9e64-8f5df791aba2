/**
 * @feature PageHeaderWrapper
 */
import React from 'react';
import ExcelExport from 'components/ExcelExport';
import { DataSet, Table, Button, Row, Col, Select, Form } from 'choerodon-ui/pro';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { isNil, uniq } from 'lodash';
import { observer } from 'mobx-react';
import withProps from 'utils/withProps';
import { initialDs } from './stories/InitialDs';

const modelPrompt = 'tarzan.BlueGlueCodeNationalStandardCode';

/**
 * 头行结构的表单示例
 */
const BlueGlueCodeNationalStandardCode = observer(props => {

  const handleOnSearch = (value, oldValue, dataListName) => {
    const flag = value ? value.every(e => oldValue.includes(e)) : false;
    if (value && value.length > 0 && !flag) {
      const newList = [].concat(...value.map(e => e.split(/[ ]+/)));
      const uniqueList = uniq(oldValue.concat(newList));
      props?.dataSet.queryDataSet.current.set(`${dataListName}`, uniqueList)
    }
  };

  const renderBar = ({ queryFields, buttons, dataSet, queryDataSet }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <Select
                name="identificationList"
                multiple
                combo
                onChange={(value, oldValue) => handleOnSearch(value, oldValue, 'identificationList')}
              />
              <Select
                name="blueGlueCodeList"
                multiple
                combo
                onChange={(value, oldValue) => handleOnSearch(value, oldValue, 'blueGlueCodeList')}
              />
              <Select
                name="positiveTopCodeList"
                multiple
                combo
                onChange={(value, oldValue) => handleOnSearch(value, oldValue, 'positiveTopCodeList')}
              />
            </Form>
          </Col>
          <Col span={6}>
            <div
              style={{
                flexShrink: 0,
                display: 'flex',
                alignItems: 'center',
                marginTop: '0.04rem',
              }}
            >
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button
                onClick={() => {
                  dataSet.query();
                }}
                color='primary'
              >
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
  };

  const getExportQueryParams = () => {
    const queryParams = props?.dataSet.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])|| i.includes('Lov') || i === '__dirty') {
        delete queryParams[i];
      }
    });
    return queryParams;
  };

  const panelTableColumns = [{
    name: 'identification',
  },{
    name: 'blueGlueCode',
  },{
    name: 'positiveTopCode',
  }]

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get(`${modelPrompt}.title`).d('封装码与胶带码关系')}
        header={
          <ExcelExport
            method="POST"
            allBody
            otherButtonProps={{ disabled: props?.dataSet.records.length===0 }}
            requestUrl={`${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-identification-blue-code-query/list/export`}
            queryParams={getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        }
      >
        <Table
          searchCode="BatchBarcodeTraceabilityReport"
          customizedCode="BatchBarcodeTraceabilityReport"
          queryBar={renderBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={props.dataSet}
          columns={panelTableColumns}
        />
      </PageHeaderWrapper>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.BlueGlueCodeNationalStandardCode', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...initialDs(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(BlueGlueCodeNationalStandardCode),
);
