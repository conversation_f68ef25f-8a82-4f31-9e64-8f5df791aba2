/**
 * @Description: 问题管理平台-列表页DS
 * @Author: <EMAIL>
 * @Date: 2023/7/3 13:48
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'problemId',
  queryFields: [
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
    },
    {
      name: 'problemStatusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_STATUS',
      textField: 'meaning',
      valueField: 'value',
      multiple: true,
    },
    {
      name: 'problemCategoryList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
      multiple: true,
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDescription`).d('问题描述'),
    },
    {
      name: 'registerPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registerPerson`).d('登记人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'registerPersonList',
      bind: 'registerPersonLov.id',
    },
    {
      name: 'leadPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.leadPersonName`).d('跟进人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'leadPersonList',
      bind: 'leadPersonLov.id',
    },
    {
      name: 'tempMeasureExeEnableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tempMeasureExeEnableFlag`).d('临时措施实施有效'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_QUERY_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'perpMeasureExeEnableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.perpMeasureExeEnableFlag`).d('长期措施实施有效'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_QUERY_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'rootReasonConfirmFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootReasonConfirmFlag`).d('根本原因确定'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.ROOT_REASON_QUERY_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'influenceLevelList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceLevel`).d('影响程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_INFLUENCE_LEVEL',
      textField: 'meaning',
      valueField: 'value',
      multiple: true,
    },
    {
      name: 'severityLevelList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
      multiple: true,
    },
    {
      name: 'registerTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.registerTimeFrom`).d('登记时间从'),
      max: 'registerTimeTo',
    },
    {
      name: 'registerTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.registerTimeTo`).d('登记时间至'),
      min: 'registerTimeFrom',
    },
    {
      name: 'proposePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.proposePersonName`).d('提出人'),
      ignore: FieldIgnore.always,
      textField: 'name',
      lovCode: 'YP.QIS.EMPLOYEE_POSITION',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'proposePersonList',
      bind: 'proposePersonLov.employeeId',
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.principalResponsiblePerson`).d('主责人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'responsiblePersonList',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsibleUnitLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleCompany`).d('主责科室'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'responsibleDepartmentIdList',
      bind: 'responsibleUnitLov.unitId',
    },
    {
      name: 'riskLightList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.riskLight`).d('风险灯'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.RISK_LIGHT',
      textField: 'meaning',
      valueField: 'value',
      multiple: true,
    },
    {
      name: 'problemApplyLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemApplyNum`).d('举手填报单'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS_PROBLEM_APPLY',
      lovPara: { tenantId },
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          queryFlag: 'Y',
        }),
      },
    },
    {
      name: 'problemRequestId',
      bind: 'problemApplyLov.problemApplyId',
    },
    {
      name: 'onlyForMeFalg',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onlyForMeFalg`).d('仅检索与我相关'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'onlyExtensionFalg',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onlyExtensionFalg`).d('是否延期'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'projectNameList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROJECT_NAME',
      multiple: true,
    },
    {
      name: 'projectPhaseList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectPhase`).d('项目阶段'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_PROJECT_PHASE',
      multiple: true,
    },
    {
      name: 'majorDivision1List',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision1`).d('主要区分1'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION1',
      multiple: true,
    },
    {
      name: 'majorDivision2List',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision2`).d('主要区分2'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION2',
      multiple: true,
    },
    {
      name: 'majorDivision3List',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision3`).d('主要区分3'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION3',
      multiple: true,
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineName`).d('发现产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name:'productionLineIdList',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processWorkcellCode`).d('发现工作单元'),
      lovCode: 'MT.MODEL.WORKCELL_SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'processIdList',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'materialIdList',
      bind: 'materialLov.materialId',
    },
    {
      name: 'ncTypeList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncTypeList`).d('不良类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_NC_TYPE',
      multiple: true,
    },
    {
      name: 'qualityProblemTypeList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityProblemTypeList`).d('质量/制造问题类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_TYPE',
      multiple: true,
    },
    {
      name: 'previewProblemTypeList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.previewProblemTypeList`).d('审核问题类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_TYPE',
      multiple: true,
    },
    {
      name: 'marketProblemTypeList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketProblemTypeList`).d('市场问题类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MARKET_PROBLEM_TYPE',
      multiple: true,
    },
    {
      name: 'qualityProblemFindingMethodList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityProblemFindingMethodList`).d('质量/制造问题发现方式'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_FINDING_METHOD',
      transformRequest: value => [ value ],
    },
    {
      name: 'reviewProblemFindingMethodList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewProblemFindingMethodList`).d('审核问题发现方式'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_FINDING_METHOD',
      transformRequest: value => [ value ],
    },
  ],
  fields: [
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
    },
    {
      name: 'riskLight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.riskLight`).d('风险灯'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.RISK_LIGHT',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'problemStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'problemCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDescription`).d('问题描述'),
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'influenceLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceLevel`).d('影响程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_INFLUENCE_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'proposePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposePersonName`).d('提出人'),
    },
    {
      name: 'proposePersonCompanyName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposePersonCompanyName`).d('提出部门'),
    },
    {
      name: 'proposeTimePeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposeTime`).d('提出时间'),
    },
    {
      name: 'registerPersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.registerPersonRealName`).d('登记人'),
    },
    {
      name: 'registerTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.registerTime`).d('登记时间'),
    },
    {
      name: 'leadPersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.leadPersonRealName`).d('跟进人'),
    },
    {
      name: 'solutionTool',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.solutionTool`).d('问题解决工具'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SOLUTION_TOOL',
      textField: 'meaning',
      valueField: 'value',
    },
    // {
    //   name: 'verificationLibraryFlag',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.verificationLibraryFlag`).d('纳入验证库'),
    // },
    {
      name: 'marketActEvaluationFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.generateMarketActEvaluation`).d('生成市场活动评估'),
    },
    {
      name: 'tempMeasureExeEnableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tempMeasureExeEnableFlag`).d('临时措施实施有效'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_QUERY_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'perpMeasureExeEnableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.perpMeasureExeEnableFlag`).d('长期措施实施有效'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_QUERY_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'rootReasonConfirmFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootReasonConfirmFlag`).d('根本原因确定'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.ROOT_REASON_QUERY_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'unitName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unitName`).d('主责科室'),
    },
    {
      name: 'responsiblePersonName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.principalResponsiblePerson`).d('主责人'),
    },
    {
      name: 'attribute1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.releasedTime`).d('下达时间'),
    },
    {
      name: 'attribute2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tempMeasureSubmitTime`).d('临时措施评价时间'),
    },
    {
      name: 'attribute3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.perpMeasureSubmitTime`).d('长期措施制定时间'),
    },
    {
      name: 'attribute4',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootMeasureSubmitTime`).d('根本原因评价时间'),
    },
    {
      name: 'problemApplyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemApplyCode`).d('举手填报单'),
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROJECT_NAME',
    },
    {
      name: 'projectPhase',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectPhase`).d('项目阶段'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_PROJECT_PHASE',
    },
    {
      name: 'majorDivision1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision1`).d('主要区分1'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION1',
    },
    {
      name: 'majorDivision2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision2`).d('主要区分2'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION2',
    },
    {
      name: 'majorDivision3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision3`).d('主要区分3'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION3',
    },
    {
      name: 'productionLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('发现产线'),
    },
    {
      name: 'processName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processWorkcellCode`).d('发现工作单元'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
    },
    {
      name: 'ncType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncTypeList`).d('不良类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_NC_TYPE',
    },
    {
      name: 'qualityProblemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityProblemTypeList`).d('质量/制造问题类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_TYPE',
    },
    {
      name: 'previewProblemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.previewProblemTypeList`).d('审核问题类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_TYPE',
    },
    {
      name: 'marketProblemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketProblemTypeList`).d('市场问题类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MARKET_PROBLEM_TYPE',
    },
    {
      name: 'qualityProblemFindingMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityProblemFindingMethod`).d('"质量/制造问题发现方式'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_FINDING_METHOD',
    },
    {
      name: 'previewProblemFindingMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.previewProblemFindingMethod`).d('审核问题发现方式'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_FINDING_METHOD',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/page/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
