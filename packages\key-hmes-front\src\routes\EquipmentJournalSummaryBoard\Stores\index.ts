import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.wms.DeviceStatusMonitoringBoard';
const tenantId = getCurrentOrganizationId();

export const TopFilterDS = (): DataSetProps => ({
  autoCreate: true,
  fields: [
    {
      name: 'assetSetLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.plantCode`).d('资产分类'),
      lookupUrl: `${BASIC.API_PREFIX}/v1/${tenantId}/asset-ledger-summary/get-asset-class`,
      lovPara: { tenantId, page: 0, size: 10000 },
      textField: 'assetSetName',
      valueField: 'assetSetId',
      required: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const rows = JSON.parse(data);
          const content: any = [];
          (rows || []).forEach((item) => {
            content.push({
              ...item,
            });
          });
          return content;
        },
      },
    },
    {
      name: "assetSetId",
      bind: 'assetSetLov.assetSetId',
    },
  ],
});
