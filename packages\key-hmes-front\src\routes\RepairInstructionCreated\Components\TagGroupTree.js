/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable no-console */
import React, { Fragment, useEffect } from 'react';
import { Button } from 'choerodon-ui/pro';
import { Tree } from 'hzero-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';


const serveCode = BASIC.TARZAN_METHOD;
const tenantId = getCurrentOrganizationId();
const TagGroupTree = props => {
  const {
    getDataCollectionGroup,
    onCheck,
    renderTreeItems,
    currentList,
    currentKey,
    canCreate,
    editStatus,
    routerId,
    formDs,
  } = props;


  useEffect(() => {
    console.log('stationValue', currentKey);
  }, [props]);

  useEffect(() => {
    if(currentList[0]?._status === 'create'){
      request(`${serveCode}/v1/${tenantId}/hme-repair-task-create/tag/group/query`, {
        method: 'GET',
        query: {
          routerId,
          materialId: formDs.current.get('materialId'),
          revisionCode: formDs.current.get('revisionCode'),
          opId: currentList[0]?.operationId,
        },
      }).then((ok) => {
        if(ok && !ok.failed){
          console.log("res", ok);
          currentList[0].tagGroupList = ok.tagGroupList || [];
        }else{
          return notification.error({
            message: ok.message,
          });
        }
      })
    }
  }, []);


  return (
    <Fragment>
      <div>
        <span style={{ fontSize: 14 }}>{currentList[0].description}</span>
        <Button
          key="add"
          icon="add"
          color="primary"
          funcType="flat"
          style={{ float: 'right' }}
          disabled={canCreate || !editStatus}
          onClick={() => getDataCollectionGroup(currentList)}
        >
          新增
        </Button>
      </div>
      <br />
      <Tree
        defaultExpandAll
        showLine
        checkable
        disabled={canCreate || !editStatus}
        onCheck={(checkedKeys, { checkedNodes }) =>
          onCheck(checkedKeys, { checkedNodes }, currentList[0]?.routerStepId)
        }
        checkedKeys={currentKey}
        // loading={fetchEquipmentLoading}
      >
        {renderTreeItems(currentList[0].tagGroupList, currentList[0].routerStepId)}
      </Tree>
    </Fragment>
  );
};
export default formatterCollections()(TagGroupTree);
