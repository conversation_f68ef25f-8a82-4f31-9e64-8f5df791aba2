import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import moment from 'moment';

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;
const modelPrompt = 'tarzan.receive.productProcessingHistoryQueryReport';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'traceRelId',
    paging: true,
    autoQuery: false,
    selection: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
      },
      {
        name: 'eoQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.eoQty`).d('数量'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'workOrderCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderCode`).d('工单编码'),
      },
      {
        name: 'sequence',
        type: 'string',
        label: intl.get(`${modelPrompt}.sequence`).d('工序号'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
      },
      {
        name: 'operationDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDesc`).d('工艺描述'),
      },
      {
        name: 'wipStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.wipStatusDesc`).d('移动类型'),
      },
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('移动数量'),
      },
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备描述'),
      },
      {
        name: 'workcellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
      },
      {
        name: 'workcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellName`).d('工位描述'),
      },
      {
        name: 'reworkFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.reworkFlag`).d('是否返修'),
      },
      {
        name: 'shiftCodeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.shiftCodeDesc`).d('班次'),
      },
      {
        name: 'shiftDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.shiftDate`).d('日期'),
      },
      {
        name: 'createdByRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.createdByRealName`).d('操作人'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('移动时间'),
      },
      {
        name: 'prodLineCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线描述'),
      },
    ],
    queryFields: [

      {
        name: 'identifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.identifications`).d('产品条码'),
      },
      {
        name: 'workcellLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.workcellLov`).d('工位编码'),
        ignore: 'always',
        lovCode: 'HME.WORKCELL_CODE',
      },
      {
        name: 'organizationId',
        bind: 'workcellLov.organizationId',
      },
      {
        name: 'operationLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.workcellLov`).d('工艺编码'),
        ignore: 'always',
        lovCode: 'MT.METHOD.OPERATION',
      },
      {
        name: 'operationId',
        bind: 'operationLov.operationId',
      },
      {
        name: 'equipmentLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.equipmentLov`).d('设备编码'),
        ignore: 'always',
        lovCode: 'MT.MODEL.EQUIPMENT',
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'shiftCode',
        lookupCode: 'HME.HANDOVER_TIME',
        label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
        type: 'string',
      },
      {
        name: 'dateFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.timeFrom`).d('开始时间从'),
        dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
        max: 'dateTo',
        dynamicProps: {
          min: ({ record }) => {
            return moment(record?.get('dateTo')?record?.get('dateTo'):new Date()).subtract(6,'months')
          },
        },
      },
      {
        name: 'dateTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.timeTo`).d('开始时间至'),
        dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
        min: 'dateFrom',
        dynamicProps: {
          max: ({ record }) => {
            return moment(record?.get('dateFrom')?record?.get('dateFrom'): new Date()).add(6,'months')
          },
        },
      },
      {
        name: 'moveType',
        type: 'string',
        lookupCode: 'HME.STEP_MOVE_TYPE',
        label: intl.get(`${modelPrompt}.moveType`).d('移动类型'),
      },
      {
        name: 'enableFlag',
        lookupCode: 'MT.FLAG',
        label: intl.get(`${modelPrompt}.enableFlag`).d('是否返修'),
      },
      {
        name: 'timeInfo',
        type: 'date',
        label: intl.get(`${modelPrompt}.timeInfo`).d('日期'),
      },
      {
        name: 'materialLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
        ignore: 'always',
        lovCode: 'HME.PERMISSION_MATERIAL',
      },
      {
        name: 'materialId',
        bind: 'materialLov.materialId',
      },
      {
        name: 'loginName',
        type: 'string',
        label: intl.get(`${modelPrompt}.loginName`).d('操作人'),
      },
      {
        name: 'workOrderCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderCode`).d('工单编码'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-product-step-actual/query/list`,
          method: 'GET',
        };
      },
    },
  };
};



export {tableDS};
