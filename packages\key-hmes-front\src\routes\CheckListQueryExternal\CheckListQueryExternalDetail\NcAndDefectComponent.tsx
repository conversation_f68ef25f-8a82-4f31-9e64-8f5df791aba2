/**
 * @Description: 检验单维护-详情页 不良与处置记录组件
 * @Author: <EMAIL>
 * @Date: 2023/3/2 15:06
 */
import React from 'react';
import {
  Table,
  Tabs,
  Form,
  Select,
  Output,
  TextField,
  SelectBox,
  Lov,
  NumberField,
} from 'choerodon-ui/pro';
import { Collapse, Badge } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from 'utils/intl';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { AttributeDrawer } from 'hcm-components-front/lib/components/tarzan-ui';
import { BASIC } from 'hcm-components-front/lib/utils/config';
import { observer } from 'mobx-react';

const { Panel } = Collapse;
const { TabPane } = Tabs;
const modelPrompt = 'tarzan.hwms.CheckListQueryExternal';

const NcAndDefectComponent = props => {
  const {
    customizeForm,
    customizeTable,
    custConfig,
    ncCodeDs,
    defectRecordsDs,
    canEdit,
    defectLineDss,
    functionList,
  } = props;
  // @ts-ignore

  const ncRecordColumns: ColumnProps[] = [
    { name: 'inspectNcRecordDimensionDesc', width: 150 },
    { name: 'inspectTaskCode', width: 150 },
    { name: 'inspectItemCode', width: 150 },
    { name: 'inspectObjectCode', width: 150 },
    { name: 'ncRecordTypeDesc', width: 150 },
    { name: 'qty' },
    { name: 'uomName' },
    { name: 'ncRecordTime', width: 150 },
    { name: 'ncRecordUserName' },
    { name: 'operationName', width: 150 },
    { name: 'workcellName', width: 150 },
    { name: 'locatorName', width: 150 },
    { name: 'equipmentName', width: 150 },
    { name: 'ncCodeCode', width: 150 },
    { name: 'scrapDetail', width: 150 },
    { name: 'defectLevelDesc', width: 150 },
    {
      name: 'componentMaterialName',
      width: 150,
      renderer: ({ value, record }) => {
        if (record?.get('componentRevisionCode')) {
          return `${value}/${record?.get('componentRevisionCode')}`;
        }
        return value;
      },
    },
    { name: 'defectLocation' },
    { name: 'defectReason' },
    { name: 'interceptWorkcellName', width: 150 },
    { name: 'interceptOperationName', width: 150 },
    { name: 'rootCauseWorkcellName', width: 150 },
    { name: 'rootCauseOperationName', width: 150 },
    { name: 'rootCauseEquipmentCode', width: 150 },
    { name: 'responsibleUserName', width: 150 },
    { name: 'responsibleApartment', width: 150 },
    { name: 'shiftTeamCode' },
    { name: 'shiftDate' },
    { name: 'shiftCode' },
    { name: 'containerCode' },
    { name: 'remark' },
    { name: 'enclosure' },
    {
      name: 'attr',
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      title: intl.get('common.platform.sysTools.extend').d('扩展属性'),
      renderer: ({ record }) => (
        <AttributeDrawer
          serverCode={BASIC.TARZAN_SAMPLING}
          className="org.tarzan.qms.domain.entity.MtInspectDocNcRecord"
          kid={record?.get('inspectDocNcRecordId')}
          canEdit={false}
          disabled={!record?.get('inspectDocNcRecordId')}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.NC_RECORD_ATTR`}
          custConfig={custConfig}
          type="text"
        />
      ),
    },
  ];

  const RenderTabPane = observer(({ record, defectLineDs }) => {
    // @ts-ignore
    const panelTableColumns = [
      { name: 'sequence' },
      { name: 'disposalObjectTypeDesc' },
      {
        name: 'disposalObjectCode',
        renderer: ({ record }) => {
          if (record?.get('disposalObjectType') === 'MATERIAL') {
            return record?.get('degradeMaterialName');
          }
          return record?.get('disposalObjectCode');
        },
      },
      {
        name: 'identification',
        width: 180,
      },
      { name: 'disposalObjectQty' },

      ...functionList.map(functionItem => {
        return {
          name: functionItem.dispositionFunction,
          hidden: !(record.get('dispositionType') === 'PART'),
          editor: canEdit && functionItem.dispositionFunction !== 'PASS' && (
            <NumberField onChange={numberInputChange} />
          ),
        };
      }),
      { name: 'uomName' },
      {
        name: 'reworkRouterLov',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'REWORK_ROUTER') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      {
        name: 'degradeMaterialObj',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'DEGRADE') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      {
        name: 'degradeRevisionCode',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'DEGRADE') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      {
        name: 'degradeLevel',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'DEGRADE') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      {
        name: 'operationLov',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'REWORK_SOURCE') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      {
        name: 'processWorkcellLov',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'REWORK_SOURCE') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      {
        name: 'concessionInterceptOperationObj',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'CONCESSION_INTERCEPTION') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      {
        name: 'interceptWorkcellObj',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'CONCESSION_INTERCEPTION') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      {
        name: 'dischargeWorkcellObj',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'EO_DISCHARGE') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      {
        name: 'overInterceptOperationObj',
        hidden: !(
          functionList.find(item => item.dispositionFunction === 'CROSS_WO_INTERCEPT') &&
          record.get('dispositionType') === 'PART'
        ),
        editor: canEdit,
      },
      // { name: 'reworkStepName' },
      { name: 'disposalExecuteTime' },
      { name: 'disposalExecuteUserName' },
      { name: 'remark' },
      {
        name: 'attr',
        lock: ColumnLock.right,
        align: ColumnAlign.center,
        title: intl.get('common.platform.sysTools.extend').d('扩展属性'),
        renderer: ({ record }) => (
          <AttributeDrawer
            serverCode={BASIC.TARZAN_SAMPLING}
            className="org.tarzan.qms.domain.entity.MtInspectDocDispDtl"
            kid={record?.get('inspectDocDisposalDtlId')}
            canEdit={false}
            disabled={!record?.get('inspectDocDisposalDtlId')}
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL_LINE_ATTR`}
            custConfig={custConfig}
            type="text"
          />
        ),
      },
    ];

    return (
      <>
        {customizeForm(
          {
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL`,
          },
          <Form record={record} disabled={!canEdit} columns={3}>
            <TextField name="dispositionUserName" disabled />
            <TextField name="dispositionTime" disabled />
            <TextField name="dispositionApartment" />
            <TextField name="remark" />
            {/* <Output name="displayQty" /> */}
            <SelectBox name="dispositionType" onChange={dispositionTypeChange} />

            {record.get('dispositionType') === 'ALL' && (
              <>
                <Select name="dispositionFunctionObject" />
                {record.get('dispositionFunction') === 'REWORK_SOURCE' && (
                  <>
                    <Lov name="operationLov" />
                    <Lov name="processWorkcellLov" />
                  </>
                )}
                {record.get('dispositionFunction') === 'REWORK_ROUTER' && (
                  <>
                    <Lov name="reworkRouterLov" />
                  </>
                )}
                {record.get('dispositionFunction') === 'DEGRADE' && (
                  <>
                    <Lov name="degradeMaterialObj" />
                    <Select name="degradeRevisionCode" />
                    <TextField name="degradeLevel" />
                  </>
                )}
                {record.get('dispositionFunction') === 'CONCESSION_INTERCEPTION' && (
                  <>
                    <Lov name="concessionInterceptOperationObj" />
                    <Lov name="interceptWorkcellObj" />
                    {/* <Lov name="routerStepObj" /> */}
                  </>
                )}
                {record.get('dispositionFunction') === 'EO_DISCHARGE' && (
                  <>
                    <Lov name="dischargeWorkcellObj" />
                  </>
                )}
                {record.get('dispositionFunction') === 'CROSS_WO_INTERCEPT' && (
                  <>
                    <Lov name="overInterceptOperationObj" />
                  </>
                )}
              </>
            )}

            {record.get('dispositionType') === 'PART' && (
              <Output
                name="inspectSumQty"
                disabled
                label={<DisplayLabel />}
                renderer={({ value }) => {
                  if (record.get('qtyList')) {
                    return `${value}/${record.get('qtyList').join('/')}`;
                  }
                  const dispositionFunctionQtyList = record.get('dispositionFunctionQtyList');
                  if (dispositionFunctionQtyList) {
                    const dispositionFunctionQtyListMap = {};
                    dispositionFunctionQtyList.forEach(item => {
                      dispositionFunctionQtyListMap[item.disposalFunction] = item.qty;
                    });
                    return `${value}/${functionList
                      .map(
                        functionItem =>
                          dispositionFunctionQtyListMap[functionItem.dispositionFunction] || 0,
                      )
                      .join('/')}`;
                  }
                  return value;
                }}
              />
            )}

            <AttributeDrawer
              serverCode={BASIC.TARZAN_SAMPLING}
              className="org.tarzan.qms.domain.entity.MtInspectDocDisposal"
              kid={record?.get('inspectDocDisposalId')}
              canEdit={false}
              disabled={!record?.get('inspectDocDisposalId')}
              custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL_ATTR`}
              custConfig={custConfig}
            />
          </Form>,
        )}
        {customizeTable(
          {
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL_LINE`,
          },
          <Table
            dataSet={defectLineDs}
            columns={panelTableColumns}
            customizedCode="inspectDocMaintain-defectRecordTable"
          />,
        )}
      </>
    );
  });

  const DisplayLabel = () => {
    const text = intl.get(`${modelPrompt}.number`).d('数');

    const sumQtyText = intl.get(`${modelPrompt}.defectForm.inspectSumQty`).d('报检总数');

    return (
      <>
        {[sumQtyText, ...(functionList || []).map(item => `${item.description}${text}`)].join(`/`)}
      </>
    );
  };

  const dispositionTypeChange = () => {
    numberInputChange();
  };

  const numberInputChange = () => {
    const functionMap: any = [];
    functionList.forEach(functionItem => {
      functionMap.push(functionItem.dispositionFunction);
    });

    // ds 组 存的不同 tab的 列表数据
    defectLineDss.forEach((ds, dsIndex) => {
      // lineValues 只是用来给 tab的表单显示 报检总数及动态列数字的
      const lineValues: any = [];
      ds.forEach(record => {
        // sum 记录的是当前 列表行的 不通过总数
        let sum = 0;
        // disposalObjectQty 是处置总数
        const disposalObjectQty = record.get('disposalObjectQty') || 0;
        // passIndex 用来记录 PASS的索引 有可能又 PASS  有可能没有
        let passIndex = -1;
        functionMap.forEach((key, index) => {
          const value = record.get(key) || 0;
          if (key !== 'PASS') {
            sum += value;
            lineValues[index] = (lineValues[index] || 0) + value;
          } else {
            passIndex = index;
          }
        });
        if (passIndex > -1) {
          lineValues[passIndex] = (lineValues[passIndex] || 0) + disposalObjectQty - sum;
        }
        record.set('PASS', disposalObjectQty - sum);
      });
      // defectRecordsDs  是 tab的表单数据 他的每一项对应一个 defectLineDss数组里的 ds 用index 一一对应
      defectRecordsDs.forEach((defectRecordsItem, subIndex) => {
        if (dsIndex === subIndex) {
          defectRecordsItem.set('qtyList', lineValues);
        }
      });
    });
  };

  return (
    <Collapse defaultActiveKey={['ncRecord', 'defectRecord']}>
      <Panel header={intl.get(`${modelPrompt}.title.ncRecord`).d('不良记录')} key="ncRecord">
        {customizeTable(
          {
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.NC_RECORD`,
          },
          <Table
            dataSet={ncCodeDs}
            columns={ncRecordColumns}
            customizedCode="inspectDocMaintain-ncRecordTable"
          />,
        )}
      </Panel>
      <Panel
        header={intl.get(`${modelPrompt}.title.defectRecord`).d('处置记录')}
        key="defectRecord"
      >
        <Tabs defaultActiveKey="1">
          {defectRecordsDs.length &&
            defectRecordsDs.map((record, index) => (
              <TabPane
                tab={
                  intl.get(`${modelPrompt}.title.inspectTimes`).d('检验次数') +
                  record.get('inspectTimes')
                }
                key={record.get('inspectTimes')}
              >
                <RenderTabPane record={record} defectLineDs={defectLineDss[index]} />
              </TabPane>
            ))}
        </Tabs>
      </Panel>
    </Collapse>
  );
};

export default NcAndDefectComponent;
