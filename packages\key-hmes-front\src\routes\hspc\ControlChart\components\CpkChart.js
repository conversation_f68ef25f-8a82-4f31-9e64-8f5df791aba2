import React from 'react';
import { Chart, Geom, Axis, Tooltip, Guide, View } from 'bizcharts';
import DataSet from '@antv/data-set';
import { get as chainGet, max as lodashMax, min as lodashMin } from 'lodash';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.hspc.chartInfo';
const { Line } = Guide;
export default class CpkChart extends React.Component {
  render() {
    const viewWidth = document?.getElementById('cpkChartLeft')?.clientWidth || 100;

    const { cpkChartInfo = {} } = this.props;

    const data = [];
    for (let i = 0; i < cpkChartInfo.allValueList.length; i++) {
      const obj = {};
      obj.value = cpkChartInfo.allValueList[i];
      data.push(obj);
    }
    const ds = new DataSet();
    const dv = ds.createView().source(data);
    const a = dv.transform({
      type: 'bin.histogram',
      field: 'value',
      binWidth: cpkChartInfo.distanceXi,
      as: ['value', 'count'],
    });
    const lineData = chainGet(cpkChartInfo, 'cpkChartDataVo', []).reduce((prev, next) => {
      const middle1 = {
        xCoordinate: Number(next.xLeftCoordinate),
        yDottedGraph: Number(next.yLeftDottedGraph),
        ySolidGraph: Number(next.yLeftSolidGraph),
      };
      const middle2 = {
        xCoordinate: Number(next.xCoordinate),
        yDottedGraph: Number(next.yDottedGraph),
        ySolidGraph: Number(next.ySolidGraph),
      };
      prev.push(middle1);
      prev.push(middle2);
      return prev;
    }, []);
    if (cpkChartInfo.cpkChartDataVo.length >= a.rows.length) {
      const { cpkChartDataVo } = cpkChartInfo;
      for (let i = 0; i < cpkChartDataVo.length; i++) {
        cpkChartDataVo[i].value = a.rows[i] ? a.rows[i].value : '';
        cpkChartDataVo[i].count = a.rows[i] ? a.rows[i].count : '';
        cpkChartDataVo[i].yDottedGraph = Number(cpkChartDataVo[i].yDottedGraph);
        cpkChartDataVo[i].ySolidGraph = Number(cpkChartDataVo[i].ySolidGraph);
      }
      a.rows = [...cpkChartInfo.cpkChartDataVo];
    } else if (cpkChartInfo.cpkChartDataVo.length < a.rows.length) {
      const { cpkChartDataVo } = cpkChartInfo;
      const { rows } = a;
      for (let i = 0; i < rows.length; i++) {
        for (let y = 0; y < cpkChartDataVo.length; y++) {
          rows[i].xCoordinate = y.xCoordinate;
          rows[i].yDottedGraph = Number(y.yDottedGraph);
          rows[i].ySolidGraph = Number(y.ySolidGraph);
        }
      }
    }

    const countArr = [];
    const valueArr = [];
    const xCoordinateArr = [];
    const ySolidGraphArr = [];
    const yDottedGraphArr = [];
    if (a.rows.length >= 1) {
      a.rows.forEach(item => {
        if (item.count) {
          countArr.push(item.count);
        }
        if (item.value.length >= 1) {
          valueArr.push(item.value[0], item.value[1]);
        }
        if (item.xCoordinate) {
          xCoordinateArr.push(item.xCoordinate);
        }
        if (item.ySolidGraph) {
          ySolidGraphArr.push(Number(item.ySolidGraph));
        }
        if (item.yDottedGraph) {
          yDottedGraphArr.push(Number(item.yDottedGraph));
        }
      });
    }
    const maxSampleValues = lodashMax(cpkChartInfo.allValueList);
    const minSampleValues = lodashMin(cpkChartInfo.allValueList);
    const entiretyUcl = cpkChartInfo.lowerControlLimit;
    const entiretyLcl = cpkChartInfo.upperControlLimit;
    // count的最大高度
    const maxCount = lodashMax(countArr);
    // 直方图value的最大最小值
    const maxValue = valueArr.sort()[valueArr.length - 1];
    const minValue = valueArr.sort()[0];
    // 曲线xCoordinate的最大最小值
    const maxXCoordinate = lodashMax(xCoordinateArr);
    const minXCoordinate = lodashMin(xCoordinateArr);
    // 上下阈值
    const maxLimit = cpkChartInfo.upperSpecLimit || lodashMax([maxSampleValues, entiretyUcl]);
    const minLimit = cpkChartInfo.lowerSpecLimit || lodashMin([minSampleValues, entiretyLcl]);
    // 曲线图的高度
    const maxSolid = lodashMax(ySolidGraphArr);
    const maxDotted = lodashMax(yDottedGraphArr);
    const minEntire = cpkChartInfo.lowerControlLimit;
    const maxEntire = cpkChartInfo.upperControlLimit;
    const cols = {
      value: {
        tickInterval: cpkChartInfo.distanceXi,
        min: lodashMin([minLimit, minXCoordinate, minValue, minEntire]),
        max: lodashMax([maxLimit, maxXCoordinate, maxValue, maxEntire]),
      },
      xCoordinate: {
        tickInterval: cpkChartInfo.distanceXi,
        min: lodashMin([minLimit, minXCoordinate, minValue, minEntire]),
        max: lodashMax([maxLimit, maxXCoordinate, maxValue, maxEntire]),
      },
      count: {
        max: maxCount * 1.4,
      },
      ySolidGraph: {
        max: maxSolid * 1.4,
      },
      yDottedGraph: {
        max: maxDotted * 1.4,
      },
    };
    // 仅当lineData的yDottedGraph不全为0，并且四舍五入三十位小数不为0的时候才显示虚线
    const powData = 10 ** 40;
    const showYDottedGraph = lineData.every(item => {
      return item.yDottedGraph === 0 || Math.round(item.yDottedGraph * powData) / powData === 0;
    });
    // 仅当lineData的ySolidGraph不全为0，并且四舍五入三十位小数不为0的时候才显示实线
    const showYSolidGraph = lineData.every(item => {
      return item.ySolidGraph === 0 || Math.round(item.ySolidGraph * powData) / powData === 0;
    });

    return (
      <>
        <Chart
          placeholder
          height={550}
          width={viewWidth}
          padding={[30, 30, 50, 30]}
          data={a}
          scale={cols}
          forceFit
        >
          <Axis name="count" visible={false} />
          {/* <Axis name="value" visible={false} /> */}
          <Tooltip />
          <Geom
            type="interval"
            position="value*count"
            tooltip={[
              'value*count',
              (value, count) => {
                return {
                  title: `${chainGet(value, '[0]', 0).toFixed(2)}-${chainGet(
                    value,
                    '[1]',
                    0,
                  ).toFixed(2)}`,
                  name: intl.get(`${modelPrompt}.frequency`).d('频数'),
                  value: Number(count).toFixed(0),
                };
              },
            ]}
          />
          <View data={lineData}>
            {!showYSolidGraph && <Axis name="ySolidGraph" grid={null} visible={false} />}
            {!showYDottedGraph && <Axis name="yDottedGraph" grid={null} visible={false} />}
            {!showYSolidGraph && (
              <Geom
                type="line"
                position="xCoordinate*ySolidGraph"
                size={1}
                color="rgba(51,51,51,1)"
                shape="smooth"
                tooltip={false}
              />
            )}
            {!showYDottedGraph && (
              <Geom
                type="line"
                position="xCoordinate*yDottedGraph"
                size={1}
                color="rgba(51,51,51,1)"
                shape="smooth"
                style={{
                  lineDash: [4, 4],
                }}
                tooltip={false}
              />
            )}
          </View>
          <Guide>
            {cpkChartInfo.lowerSpecLimit && (
              <Line
                top
                start={{ value: cpkChartInfo.lowerSpecLimit, count: 0 }}
                end={{ value: cpkChartInfo.lowerSpecLimit, count: maxCount * 1.4 }}
                lineStyle={{
                  stroke: 'green',
                  lineDash: [5, 1],
                  lineWidth: 0.5,
                }}
                text={{
                  position: 'end', // 'start' | 'center' | 'end' | '39%' | 0.5 文本的显示位置
                  autoRotate: false, // {boolean} 是否沿线的角度排布，默认为 true
                  style: {
                    fill: 'green',
                  }, // {object}文本图形样式配置,https://bizcharts.net/products/bizCharts/api/graphic#文本属性
                  offsetX: -25, // {number} x 方向的偏移量
                  offsetY: -5, // {number} y 方向的偏移量
                  content: intl.get(`${modelPrompt}.lowerSpecLimit`).d('规格下限'), // {string} 文本的内容
                }}
              />
            )}
            <Line
              top
              start={{ value: cpkChartInfo.specTarget, count: 0 }}
              end={{ value: cpkChartInfo.specTarget, count: maxCount * 1.4 }}
              lineStyle={{
                stroke: 'green',
                lineDash: [5, 1],
                lineWidth: 0.5,
              }}
              text={{
                position: 'end', // 'start' | 'center' | 'end' | '39%' | 0.5 文本的显示位置
                autoRotate: false, // {boolean} 是否沿线的角度排布，默认为 true
                style: {
                  fill: 'green',
                }, // {object}文本图形样式配置,https://bizcharts.net/products/bizCharts/api/graphic#文本属性
                offsetX: -20, // {number} x 方向的偏移量
                offsetY: -5, // {number} y 方向的偏移量
                content: intl.get(`${modelPrompt}.specTarget`).d('目标值'), // {string} 文本的内容
              }}
            />
            {cpkChartInfo.upperSpecLimit && (
              <Line
                top
                start={{ value: cpkChartInfo.upperSpecLimit, count: 0 }}
                end={{ value: cpkChartInfo.upperSpecLimit, count: maxCount * 1.4 }}
                lineStyle={{
                  stroke: 'green',
                  lineDash: [5, 1],
                  lineWidth: 0.5,
                }}
                text={{
                  position: 'end', // 'start' | 'center' | 'end' | '39%' | 0.5 文本的显示位置
                  autoRotate: false, // {boolean} 是否沿线的角度排布，默认为 true
                  style: {
                    fill: 'green',
                  }, // {object}文本图形样式配置,https://bizcharts.net/products/bizCharts/api/graphic#文本属性
                  offsetX: -25, // {number} x 方向的偏移量
                  offsetY: -5, // {number} y 方向的偏移量
                  content: intl.get(`${modelPrompt}.upperSpecLimit`).d('规格上限'), // {string} 文本的内容
                }}
              />
            )}
            <div>
              <Line
                top
                start={{
                  value: cpkChartInfo.centerLine,
                  count: 0,
                }}
                end={{
                  value: cpkChartInfo.centerLine,
                  count: maxCount * 1.2,
                }}
                lineStyle={{
                  stroke: 'red',
                  lineDash: [5, 1],
                  lineWidth: 0.5,
                }}
                text={{
                  position: 'end', // 'start' | 'center' | 'end' | '39%' | 0.5 文本的显示位置
                  autoRotate: false, // {boolean} 是否沿线的角度排布，默认为 true
                  style: {
                    fill: 'red',
                  }, // {object}文本图形样式配置,https://bizcharts.net/products/bizCharts/api/graphic#文本属性
                  offsetX: -18, // {number} x 方向的偏移量
                  offsetY: -5, // {number} y 方向的偏移量
                  content: intl.get(`${modelPrompt}.centerLine`).d('中心线'), // {string} 文本的内容
                }}
              />
              <Line
                top
                start={{
                  value: cpkChartInfo.upperControlLimit,
                  count: 0,
                }}
                end={{
                  value: cpkChartInfo.upperControlLimit,
                  count: maxCount * 1.2,
                }}
                lineStyle={{
                  stroke: 'red',
                  lineDash: [5, 1],
                  lineWidth: 0.5,
                }}
                text={{
                  position: 'end', // 'start' | 'center' | 'end' | '39%' | 0.5 文本的显示位置
                  autoRotate: false, // {boolean} 是否沿线的角度排布，默认为 true
                  style: {
                    fill: 'red',
                  }, // {object}文本图形样式配置,https://bizcharts.net/products/bizCharts/api/graphic#文本属性
                  offsetX: -25, // {number} x 方向的偏移量
                  offsetY: -5, // {number} y 方向的偏移量
                  content: intl.get(`${modelPrompt}.upperControlLimit`).d('控制上限'), // {string} 文本的内容
                }}
              />
              <Line
                top
                start={{
                  value: cpkChartInfo.lowerControlLimit,
                  count: 0,
                }}
                end={{
                  value: cpkChartInfo.lowerControlLimit,
                  count: maxCount * 1.2,
                }}
                lineStyle={{
                  stroke: 'red',
                  lineDash: [5, 1],
                  lineWidth: 0.5,
                }}
                text={{
                  position: 'end', // 'start' | 'center' | 'end' | '39%' | 0.5 文本的显示位置
                  autoRotate: false, // {boolean} 是否沿线的角度排布，默认为 true
                  style: {
                    fill: 'red',
                  }, // {object}文本图形样式配置,https://bizcharts.net/products/bizCharts/api/graphic#文本属性
                  offsetX: -25, // {number} x 方向的偏移量
                  offsetY: -5, // {number} y 方向的偏移量
                  content: intl.get(`${modelPrompt}.lowerControlLimit`).d('控制下限'), // {string} 文本的内容
                }}
              />
            </div>
          </Guide>
        </Chart>
      </>
    );
  }
}
