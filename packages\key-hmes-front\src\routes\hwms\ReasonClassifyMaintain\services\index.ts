/*
 * @Description: 原因分类维护-services
 * @Author: <<EMAIL>>
 * @Date: 2023-09-11 15:04:12
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-14 11:35:33
 */
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

/**
 * 用户信息Lov
 * @function SaveReasonClassify
 * @returns {object} fetch Promise
 */
export function SaveReasonClassify(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-reason/update/ui`,
    method: 'POST',
  };
}
