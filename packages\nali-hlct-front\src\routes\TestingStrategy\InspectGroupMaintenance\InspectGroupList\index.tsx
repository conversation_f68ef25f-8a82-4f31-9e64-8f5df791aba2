/**
 * @Description: 检验项目组维护查询界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:29:43
 * @LastEditTime: 2023-05-24 16:56:17
 * @LastEditors: <<EMAIL>>
 */

import React, { useCallback, useEffect, useMemo } from 'react';
import { DataSet, Modal, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Badge, Collapse, Tag } from 'choerodon-ui';
import queryString from 'querystring';
import { Content, Header } from 'components/Page';
import { openTab } from 'utils/menuTab';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { DetailTableDS, historyDS, ListTableDS } from '../stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.qms.inspectGroupMaintenance';
const { Panel } = Collapse;

const InspectGroupList = props => {
  const {
    match: { path },
    tableDS,
    history,
    customizeTable,
  } = props;

  const historyDtlDs = useMemo(
    () =>
      new DataSet({
        ...DetailTableDS(),
        selection: false,
      }),
    [],
  );
  const historyDs = useMemo(
    () =>
      new DataSet({
        ...historyDS(),
        children: {
          inspectGroupItemDTOList: historyDtlDs,
        },
      }),
    [],
  );

  useEffect(() => {
    tableDS.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.LIST`,
    );

    if (tableDS?.currentPage) {
      tableDS.query(props.tableDS.currentPage);
    } else {
      tableDS.query();
    }
  }, []);

  const handleAdd = useCallback(() => {
    history.push({
      pathname: '/hwms/inspect-group-maintenance/dist/create',
    });
  }, []);

  const handleImport = () => {
    openTab({
      key: '/himp/commentImport/MT.QMS.INSPECT_GROUP',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'inspectGroupCode',
      width: 240,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(
                `/hwms/inspect-group-maintenance/dist/${record?.get('inspectGroupId')}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'inspectGroupDesc',
    },
    {
      name: 'inspectGroupTypeDesc',
      align: ColumnAlign.center,
    },
    {
      name: 'enableFlag',
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      width: 100,
      renderer: ({ record }) => (
        <a onClick={() => handleOpenHistoryDrawer(record)}>
          {intl.get(`${modelPrompt}.operation.history`).d('历史查询')}
        </a>
      ),
    },
  ];

  const historyColumns: ColumnProps[] = [
    {
      name: 'lastUpdateDate',
      width: 150,
      align: ColumnAlign.center,
      lock: ColumnLock.left,
    },
    {
      name: 'lastUpdatedByName',
      lock: ColumnLock.left,
      width: 150,
    },
    {
      name: 'operationTypeDesc',
    },
    {
      name: 'inspectGroupCode',
      width: 150,
    },
    {
      name: 'inspectGroupDesc',
      width: 150,
    },
    {
      name: 'inspectGroupTypeDesc',
      align: ColumnAlign.center,
    },
    {
      name: 'remark',
    },
    {
      name: 'enableFlag',
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    { name: 'creationDate', width: 150, align: ColumnAlign.center },
    { name: 'createdByName' },
  ];

  const getDataValueShow = (record, name) => {
    const _dataType = record?.get('dataType');
    const _valueData = record?.get(name) || [];
    const _dataShow = _valueData.length > 0 ? _valueData[0].dataValue : '';
    return ['CALCULATE_FORMULA', 'VALUE', 'VALUE_LIST'].includes(_dataType)
      ? _valueData.map(item => <Tag>{item.dataValue}</Tag>)
      : _dataShow;
  };

  const historyLineColumns: ColumnProps[] = [
    {
      name: 'inspectItemCode',
      width: 150,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemDesc',
      width: 150,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectBasis',
    },
    {
      name: 'qualityCharacteristic',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectTool',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectMethod',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'requiredFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'enableFlag',
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'technicalRequirement',
    },
    {
      name: 'remark',
    },
    {
      name: 'enterMethod',
      align: ColumnAlign.center,
    },
    {
      name: 'dataType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'uomLov',
      align: ColumnAlign.center,
    },
    {
      name: 'decimalNumber',
    },
    {
      name: 'processMode',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'valueLists',
      width: 200,
      renderer: ({ value }) => {
        if (value) {
          return (
            value?.length &&
            value.map(item => {
              return (
                <Tag className="hcm-tag-blue" key={item}>
                  {item}
                </Tag>
              );
            })
          );
        }
      },
    },
    {
      name: 'trueValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'trueValueList'),
    },
    {
      name: 'falseValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'falseValueList'),
    },
    {
      name: 'warningValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'warningValueList'),
    },
    {
      name: 'dataQty',
    },
    {
      name: 'samplingMethodDesc',
      align: ColumnAlign.center,
    },
    {
      name: 'ncCodeGroupDesc',
      align: ColumnAlign.center,
    },
    {
      name: 'employeePosition',
      width: 120,
    },
    {
      name: 'inspectFrequencyDesc',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
        if (inspectFrequencyShow) {
          inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
          inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
          return inspectFrequencyShow;
        }
        return value;
      },
    },
    {
      name: 'actionItem',
    },
    {
      name: 'sameGroupIdentification',
    },
    {
      name: 'destructiveExperimentFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'outsourceFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'enclosure',
      lock: ColumnLock.right,
    },
  ];

  const handleOpenHistoryDrawer = record => {
    historyDs.loadData([]);
    historyDs.setQueryParameter('inspectGroupId', record?.get('inspectGroupId'));
    historyDs.query();

    Modal.open({
      ...drawerPropsC7n({
        ds: historyDs,
        canEdit: false,
      }),
      title: intl.get(`${modelPrompt}.title.queryHistory`).d('历史查询'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      children: (
        <>
          {customizeTable(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.HISTORY`,
            },
            <Table
              dataSet={historyDs}
              columns={historyColumns}
              customizedCode="inspectGroupMaintain-historyHead"
            />,
          )}
          <Collapse bordered={false} defaultActiveKey={['historyDetail']}>
            <Panel
              key="historyDetail"
              header={intl.get(`${modelPrompt}.title.historyDetail`).d('检验项目信息')}
            >
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.ITEM_HISTORY`,
                },
                <Table
                  dataSet={historyDtlDs}
                  columns={historyLineColumns}
                  customizedCode="inspectGroupMaintain-historyLine"
                />,
              )}
            </Panel>
          </Collapse>
        </>
      ),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('检验项目组维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
          icon="file_upload"
          onClick={handleImport}
        >
          {intl.get('tarzan.common.button.import').d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            filter={record => {
              return record.get('dataType') !== 'CALCULATE_FORMULA';
            }}
            dataSet={tableDS}
            columns={columns}
            searchCode="InspectGroup"
            customizedCode="InspectGroup"
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.qms.inspectGroupMaintenance', 'tarzan.common', 'tarzan.qms.inspectGroupMaintenance.model'],
})(
  withProps(
    () => {
      const tableDS = new DataSet({
        ...ListTableDS(),
      });
      return {
        tableDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.LIST`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.HISTORY`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.ITEM_HISTORY`,
      ],
      // @ts-ignore
    })(InspectGroupList),
  ),
);
