/**
 * @Description: 检证任务执行平台-详情界面
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  TextField,
  Select,
  DateTimePicker,
  Table,
  TextArea,
  Attachment,
  Modal,
} from 'choerodon-ui/pro';
import { Badge, Collapse } from 'choerodon-ui';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { useDataSetEvent } from 'utils/hooks';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import myInstance from '@utils/myAxios';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign } from 'choerodon-ui/pro/es/table/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import {
  detailDS,
  problemReasonDS,
  measureDS,
  taskDS,
  validateTaskDS,
  transferDS,
} from '../stores/DetailDS';
import { SaveVerification, GetDefaultSite } from '../services';
import styles from './index.module.less';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';

const tenantId = getCurrentOrganizationId();

const { Panel } = Collapse;
const modelPrompt = 'tarzan.inspectExecute.taskExePlatform';

const TaskDetail = props => {
  const {
    history,
    match: { params, path },
  } = props;
  const verificationTaskGroupId = params.verificationTaskGroupId;
  let _transferDrawer;

  // pub路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  const [canEdit, setCanEdit] = useState(false);
  const [buttonStatus, setButtonStatus] = useState(false);
  const [status, setStatus] = useState('NEW');
  const problemReasonDs = useMemo(() => new DataSet(problemReasonDS()), []);
  const measureDs = useMemo(() => new DataSet(measureDS()), []);
  const taskDs = useMemo(() => new DataSet(taskDS()), []);
  const validateTaskDs = useMemo(() => new DataSet(validateTaskDS()), []);
  const transferDs = useMemo(() => new DataSet(transferDS()), []);
  const [selectedRecords, setSelectedRecords] = useState<Array<any>>([]);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          problemReasonInfo: problemReasonDs,
          measureInfo: measureDs,
          taskInfo: taskDs,
        },
      }),
    [],
  );
  const { run: saveVerification, loading: saveLoading } = useRequest(SaveVerification(), {
    manual: true,
  });
  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });

  useDataSetEvent(detailDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialLov':
        record.set('productionVersionLov', {});
        record.set('defaultBomLov', {});
        record.set('defaultRouterLov', {});
        break;
      default:
        break;
    }
  });
  useDataSetEvent(taskDs, 'select', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
  });
  useDataSetEvent(taskDs, 'selectAll', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
  });
  useDataSetEvent(taskDs, 'unselect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
  });
  useDataSetEvent(taskDs, 'unselectAll', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
  });

  useEffect(() => {
    if (verificationTaskGroupId === 'create') {
      // 新建时
      setCanEdit(true);
      getDefaultSite({
        onSuccess: res => {
          if (res?.siteId) {
            detailDs.current?.set('siteLov', res);
          }
        },
      });
      return;
    }
    // 编辑时
    handleQueryDetail(verificationTaskGroupId);
  }, [verificationTaskGroupId]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('verificationTaskGroupId', id);
    detailDs.query().then(res => {
      const { rows } = res;
      setStatus(rows?.taskGroupStatus);
      validateTaskDs.loadData([rows]);
      transferDs.loadData([rows]);
    });
    setSelectedRecords([]);
  };

  const handleEdit = useCallback(() => {
    setButtonStatus(true);
  }, []);

  const handleCancel = useCallback(() => {
    if (verificationTaskGroupId === 'create') {
      history.push('/hwms/inspect-execute/task-exe-platform/list');
    } else {
      setCanEdit(false);
      setButtonStatus(false);
      handleQueryDetail(verificationTaskGroupId);
    }
  }, []);

  const problemReasonColumns: any = useMemo(
    () => [
      { name: 'sequence', width: 100 },
      { name: 'occur', editor: canEdit },
      { name: 'escape', editor: canEdit },
    ],
    [canEdit],
  );

  const measureColumns: any = useMemo(
    () => [
      { name: 'sequence', width: 100 },
      { name: 'measure', editor: canEdit },
    ],
    [canEdit],
  );

  const taskColumns: any = useMemo(
    () => [
      { name: 'sequence', width: 100 },
      { name: 'taskCode' },
      { name: 'taskContent' },
      { name: 'planEndTime', align: ColumnAlign.center, width: 150 },
      {
        name: 'applyFlag',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
        editor: buttonStatus,
      },
      { name: 'taskResult', editor: buttonStatus },
      { name: 'noApplyReason', editor: buttonStatus },
      { name: 'enclosure', editor: buttonStatus },
    ],
    [buttonStatus],
  );

  // @ts-ignore
  const handleSave = async ({ submitFlag }) => {
    // @ts-ignore
    taskDs.current.set({ nowDate: new Date().getTime() });
    const validateFlag = await detailDs.validate();
    const taskDsValidate = await taskDs.validate();
    if (!validateFlag || !taskDsValidate) {
      return false;
    }
    const voList = [];
    taskDs.forEach(record => {
      if (record.status !== 'sync') {
        // @ts-ignore
        voList.push(record.toData());
      }
    });

    saveVerification({
      params: voList,
      queryParams: { submitFlag, verificationTaskGroupId },
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        setButtonStatus(false);
        handleQueryDetail(res);
      },
    });
  };

  const transferFunOnOk = async () => {
    const validateFlag = await transferDs.validate();
    if (!validateFlag) {
      return false;
    }

    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <p>
          {intl
            .get(`${modelPrompt}.info.changeStep`)
            .d(
              '是否确认转交责任人？确认转交后，将会对详情界面进行刷新，请确认相关内容已保存。点击确定，继续走下一步逻辑；点击取消，关闭提示弹窗。',
            )}
        </p>
      ),
    }).then(button => {
      if (button === 'ok') {
        buttonConfirm();
      }
    });
    return false;
  };

  const buttonConfirm = () => {
    const selected = taskDs.selected.map(item => {
      return item.get('verificationTaskId');
    });
    myInstance
      .post(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-platform-execute/transmit/ui`,
        {
          // @ts-ignore
          nextResponsibleUserId: transferDs.toData()[0].transferResponsibleUserId,
          verificationTaskIds: selected,
          verificationTaskGroupId,
        },
      )
      .then(res => {
        if (res.data.success) {
          _transferDrawer.close();
          notification.success({});
          handleQueryDetail(verificationTaskGroupId);
        } else {
          notification.error({
            message: res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
          });
        }
      });
  };

  const transferFun = () => {
    _transferDrawer = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.transferResponsiblePerson`).d('转交责任人'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: transferFunOnOk,
      children: (
        <Form dataSet={transferDs} columns={1}>
          <Lov name="currentResponsibleUserLov" newLine disabled={!canEdit} />
          <Lov
            name="transferResponsibleUserLov"
            newLine
            onChange={changeTransferResponsibleUserLov}
          />
        </Form>
      ),
    });
  };

  const changeTransferResponsibleUserLov = item => {
    const data = transferDs.toData()[0];
    // @ts-ignore
    if (data?.responsibleUserId === item.id) {
      notification.error({
        message: intl.get(`${modelPrompt}.cannot.same`).d('当前责任人与转交责任人不能相同'),
      });
      transferDs.current?.set('transferResponsibleUserLov', {});
    }
  };

  const attachmentProps: any = {
    name: 'verificationUuid',
    bucketName: 'qms',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    disabled: !canEdit,
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || siteLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('检证任务执行平台')}
          backPath={pubFlag ? '' : '/hwms/inspect-execute/task-exe-platform/list'}
        >
          {buttonStatus && !pubFlag && (
            <>
              <Button
                disabled={!['NEW', 'REJECTED'].includes(status)}
                color={ButtonColor.primary}
                icon="save"
                onClick={() => handleSave({ submitFlag: 'N' })}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!buttonStatus && !pubFlag && (
            <Button
              icon="edit-o"
              disabled={!['NEW', 'REJECTED'].includes(status)}
              color={ButtonColor.primary}
              onClick={handleEdit}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
          {
            !pubFlag &&
            <Button
              disabled={!['NEW', 'REJECTED'].includes(status)}
              color={ButtonColor.primary}
              icon="save"
              onClick={() => handleSave({ submitFlag: 'Y' })}
            >
              {intl.get(`${modelPrompt}.button.saneAndSubmit`).d('保存并提交审批')}
            </Button>
          }
          <ApprovalInfoDrawer
            objectTypeList={['QIS_JZRW_LWS']}
            objectId={verificationTaskGroupId}
          />
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'basicInfo',
              'problemDesc',
              'problemReason',
              'problemMeasure',
              'taskInfo',
            ]}
          >
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.basicInfo`).d('检证信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="verificationCode" />
                <Lov name="siteLov" />
                <Select name="verificationStatus" />
                <Select name="projectName" />
                <Select name="verificationType" />
                <Select name="verificationFrom" />
                <Select name="verificationPeriod" />
                <Select name="productType" />
                <Lov name="materialLov" />
                <TextField name="materialName" />
                <Select name="itemGroupDesc" />
                <Select name="failureMode" />
                <Select name="applyArea" />
                <Select name="applicableProductLine" />
                <Select name="applicableChemicalSystem" />
                <Select name="applicableProductStructure" />
                <Select name="createMethod" />
                <Lov name="sourceTempLov" />
                <Lov name="createPersonLov" />
                <DateTimePicker name="creationDate" />
              </Form>
            </Panel>
            <Panel
              key="problemDesc"
              header={intl.get(`${modelPrompt}.title.problemDesc`).d('关联问题')}
            >
              <Form dataSet={detailDs} columns={3} labelWidth={112}>
                <Lov name="problemLov" disabled={!canEdit} />
                <Lov name="fromProcessLov" disabled={!canEdit} />
                <Select name="responsibleArea" disabled={!canEdit} />
                <TextArea name="problemDescription" colSpan={2} disabled={!canEdit} />
                <Attachment {...attachmentProps} />
              </Form>
              <Table dataSet={problemReasonDs} columns={problemReasonColumns} />
              <Table dataSet={measureDs} columns={measureColumns} />
            </Panel>
            <Panel key="taskInfo" header={intl.get(`${modelPrompt}.title.taskInfo`).d('检证任务')}>
              {!pubFlag && (
                <Button
                  icon="edit-o"
                  className={styles['flow-content']}
                  color={ButtonColor.primary}
                  onClick={transferFun}
                  disabled={!selectedRecords.length}
                >
                  {intl.get(`${modelPrompt}.transferResponsiblePerson`).d('转交责任人')}
                </Button>
              )}
              <Form dataSet={validateTaskDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="taskGroupCode" />
                <Select name="taskGroupStatus" />
                <TextField name="actualEndTime" />
                <TextField name="responsibleUserName" />
                <TextField name="departmentName" />
                <TextField name="lastResponsibleUserName" />
              </Form>
              <Table dataSet={taskDs} columns={taskColumns} />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common','hzero.common'],
})(TaskDetail);
