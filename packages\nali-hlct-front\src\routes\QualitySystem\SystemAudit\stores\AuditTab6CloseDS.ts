/**
 * @Description: 体系审核管理维护-审核关闭-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

const modelPrompt = 'tarzan.systemAudit';

// 详情-审核记录表单
const auditCloseFormDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewSummary`).d('审核总结'),
      name: 'reviewSummary',
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.reviewReportUuid`).d('上传报告'),
      name: 'reviewReportUuid',
    },
  ],
});

export { auditCloseFormDS };
