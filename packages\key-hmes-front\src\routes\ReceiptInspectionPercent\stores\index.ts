import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'receiptInspectionPercent';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  queryFields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: {
        tenantId,
      },
      multiple: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialIds',
      type: FieldType.string,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCodes',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
    },
    {
      name: 'timePeriod',
      type: FieldType.string,
      lookupCode: 'APEX.QMS_TIME_PERIOD',
      defaultValue: 'MONTH',
      label: intl.get(`${modelPrompt}.timePeriod`).d('时间周期'),
      required: true,
    },
    {
      name: 'queryYears',
      type: FieldType.date,
      multiple: true,
      label: intl.get(`${modelPrompt}.queryYears`).d('查询年份'),
      defaultValue: new Date().getFullYear(),
      dynamicProps: {
        disabled: ({ record }) => record?.get('timePeriod') === 'WEEK',
        required: ({ record }) => !(record?.get('timePeriod') === 'WEEK'),
      },
    },
    {
      name: 'startTime',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.startTime`).d('查询开始时间'),
      max: 'endTime',
      dynamicProps: {
        disabled: ({ record }) => !(record?.get('timePeriod') === 'WEEK'),
        required: ({ record }) => record?.get('timePeriod') === 'WEEK',
      },
    },
    {
      name: 'endTime',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.endTime`).d('查询结束时间'),
      min: 'startTime',
      dynamicProps: {
        disabled: ({ record }) => !(record?.get('timePeriod') === 'WEEK'),
        required: ({ record }) => record?.get('timePeriod') === 'WEEK',
      },
    },
  ],
  fields: [
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'period',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.period`).d('周期'),
    },
  ],
});

const footTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'period',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.period`).d('周期'),
    },
  ],
});

export { tableDS, footTableDS };
