/**
 * @Description: SA/专项检证平台-详情界面
 * @Author: <EMAIL>
 * @Date: 2023/8/7 15:55
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Attachment,
  Button,
  DataSet,
  DatePicker,
  DateTimePicker,
  Form,
  Lov,
  Modal,
  Select,
  Table,
  TextField,
} from 'choerodon-ui/pro';
import { Badge, Collapse, Tag } from 'choerodon-ui';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import { Content, Header } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign } from 'choerodon-ui/pro/es/table/enum';
import { observer } from 'mobx-react';
import { ColumnLock, DragColumnAlign, TableButtonType } from 'choerodon-ui/pro/lib/table/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import moment from 'moment';
import { detailDS, measureDS, problemReasonDS, taskDS, batchEditDS } from '../stores/DetailDS';
import { CopyEnclosure, GetDefaultSite, SaveVerification } from '../services';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.inspectExecute.saVerificationPlatform';

const SaVerificationDetail = props => {
  const {
    history,
    match: { path, params },
  } = props;
  const verificationId = params.verificationId;

  // pub 路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  const [canEdit, setCanEdit] = useState(false);
  const [status, setStatus] = useState('NEW');
  const problemReasonDs = useMemo(() => new DataSet(problemReasonDS()), []);
  const measureDs = useMemo(() => new DataSet(measureDS()), []);
  const taskDs = useMemo(() => new DataSet(taskDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          problemReasonInfo: problemReasonDs,
          measureInfo: measureDs,
          taskInfo: taskDs,
        },
      }),
    [],
  );
  const batchEditDs = useMemo(() => new DataSet(batchEditDS()), []);
  const { run: saveVerification, loading: saveLoading } = useRequest(SaveVerification(), {
    manual: true,
  });
  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });
  const { run: copyEnclosure, loading: copyEnclosureLoading } = useRequest(CopyEnclosure(), {
    manual: true,
    needPromise: true,
  });

  useDataSetEvent(detailDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialLov':
        record.set('productionVersionLov', {});
        record.set('defaultBomLov', {});
        record.set('defaultRouterLov', {});
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    if (verificationId === 'create') {
      // 新建时
      setCanEdit(true);
      getDefaultSite({
        onSuccess: res => {
          if (res?.siteId) {
            detailDs.current?.set('siteLov', res);
          }
        },
      });
      return;
    }
    // 编辑时
    handleQueryDetail(verificationId);
  }, [verificationId]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('verificationId', id);
    detailDs.query().then(res => {
      const { rows } = res;
      setStatus(rows?.verificationStatus);
    });
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = () => {
    if (verificationId === 'create') {
      history.push('/hwms/inspect-execute/sa-verification-platform/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(verificationId);
    }
  };

  const handleCopyEnclosure = async uuid => {
    if (!uuid) {
      detailDs.current?.set('verificationUuid', undefined);
      return;
    }
    const res = await copyEnclosure({
      params: {
        uuidList: [uuid],
      },
    });
    if (res && Object.keys(res).length) {
      detailDs.current?.set('verificationUuid', res[uuid]);
    }
  };

  const handleSave = async ({ submitFlag }) => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const { taskInfo, ...others } = detailDs!.current!.toData();
    (taskInfo || []).forEach(item => {
      item.planEndTime = moment(item.planEndTime).format('YYYY-MM-DD');
    });
    saveVerification({
      params: { ...others, taskInfo },
      queryParams: { submitFlag },
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        if (verificationId === 'create') {
          history.push(`/hwms/inspect-execute/sa-verification-platform/dist/${res}`);
        } else {
          handleQueryDetail(res);
        }
      },
    });
  };

  const problemReasonColumns: any = useMemo(
    () => [
      { name: 'sequence', width: 100, align: ColumnAlign.left },
      { name: 'occur', editor: canEdit },
      { name: 'escape', editor: canEdit },
    ],
    [canEdit],
  );

  const measureColumns: any = useMemo(
    () => [
      { name: 'sequence', width: 100, align: ColumnAlign.left },
      { name: 'measure', editor: canEdit },
    ],
    [canEdit],
  );

  const renderStatusTag = (value, record, name) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'IN_APPROVAL':
        className = 'orange';
        break;
      case 'REJECTED':
        className = 'red';
        break;
      case 'RELEASED':
        className = 'purple';
        break;
      case 'COMPLETED':
        className = 'green';
        break;
      case 'NEW':
        className = 'blue';
        break;
      default:
        className = 'gray';
    }
    return <Tag color={className}>{record!.getField(name)!.getText()}</Tag>;
  };

  const handleChangePerson = (record, value) => {
    if (record.get('departmentId')) {
      return;
    }
    record.set('departmentLov', {
      unitId: value.unitId,
      unitName: value.unitName,
    });
  };

  const taskColumns: any = useMemo(
    () => [
      { name: 'sequence', width: 100, align: ColumnAlign.left, lock: ColumnLock.left },
      !canEdit && { name: 'taskCode', width: 150 },
      !canEdit && {
        name: 'taskStatus',
        width: 130,
        align: ColumnAlign.center,
        renderer: ({ value, record }) => renderStatusTag(value, record, 'taskStatus'),
      },
      { name: 'taskContent', editor: canEdit },
      { name: 'departmentLov', editor: canEdit },
      {
        name: 'responsiblePersonLov',
        editor: record => canEdit && <Lov onChange={value => handleChangePerson(record, value)} />,
      },
      !canEdit && !pubFlag && { name: 'lastResponsibleUserName' },
      { name: 'planEndTime', align: ColumnAlign.center, width: 150, editor: canEdit },
      // !canEdit && { name: 'taskResult' },
      // !canEdit && { name: 'resultJudgeDate', align: ColumnAlign.center, width: 150 },
      !canEdit && !pubFlag && {
        name: 'applyFlag',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
      },
      !canEdit && !pubFlag  && { name: 'taskResult' },
      !canEdit && !pubFlag  && { name: 'noApplyReason' },
      !canEdit && !pubFlag  && { name: 'enclosure' },
      !canEdit && !pubFlag  && { name: 'actualEndTime', align: ColumnAlign.center, width: 150 },
    ],
    [canEdit, pubFlag],
  );

  const RenderDeleteButton = observer(({ dataSet, canEdit }) => {
    return (
      <>
        <Button
          icon="delete"
          funcType={FuncType.flat}
          color={ButtonColor.red}
          disabled={!canEdit || !dataSet.selected.length || !['NEW', 'REJECTED'].includes(status)}
          onClick={() => dataSet.remove(dataSet.selected)}
        >
          {intl.get(`${modelPrompt}.button.delete`).d('删除')}
        </Button>
      </>
    );
  });

  const handleBatchEditConfirm = () => {
    return new Promise(async resolve => {
      const valRes = await batchEditDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      taskDs.selected.forEach(record => {
        record.set('responsiblePersonLov', batchEditDs.current?.get('responsiblePersonLov'));
        record.set('planEndTime', batchEditDs.current?.get('planEndTime'));
        record.set('departmentLov', {
          unitId: batchEditDs.current?.get('unitId'),
          unitName: batchEditDs.current?.get('unitName'),
        });
      });
      taskDs.unSelectAll();
      batchEditDs.reset();
      return resolve(true);
    });
  };

  const handleBatchEdit = () => {
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.copyInspection`).d('复制检验方案'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onClose: () => batchEditDs.reset(),
      onOk: () => handleBatchEditConfirm(),
      children: (
        <Form dataSet={batchEditDs} columns={1}>
          <Lov name="responsiblePersonLov" />
          <DatePicker name="planEndTime" />
        </Form>
      ),
    });
  };

  const RenderBatchEditButton = observer(({ dataSet, canEdit }) => {
    return (
      <>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={!canEdit || !dataSet.selected.length || !['NEW', 'REJECTED'].includes(status)}
          onClick={handleBatchEdit}
        >
          {intl.get(`${modelPrompt}.button.batchEdit`).d('批量编辑')}
        </Button>
      </>
    );
  });

  const handleChangeSourceTemp = (value, oldValue) => {
    if (!value?.verificationId) {
      return;
    }
    if (oldValue?.verificationId) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.changeTempData`)
              .d('选择的检证模板数据会覆盖之前已经选择的数据，是否确认切换？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          handleUpdateTemp(value);
        } else {
          detailDs.current?.set('sourceTempLov', oldValue);
        }
      });
    } else {
      handleUpdateTemp(value);
    }
  };

  const handleChangeProblem = (value, oldValue) => {
    if (!value?.problemId) {
      return;
    }
    if (oldValue?.problemId) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.changeTempData`)
              .d('选择的问题数据会覆盖之前已经选择的数据，是否确认切换？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          handleUpdateProblem(value);
        } else {
          detailDs.current?.set('problemLov', oldValue);
        }
      });
    } else {
      handleUpdateProblem(value);
    }
  };

  const handleUpdateTemp = value => {
    delete value.createdBy;
    delete value.createdByName;
    delete value.creationDate;
    delete value.verificationStatus;
    const { problemReasonInfo, measureInfo, taskInfo, verificationUuid } = value;
    (problemReasonInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
    (measureInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
    (taskInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
    detailDs.loadData([
      {
        ...detailDs.current?.toData(),
        ...value,
        createMethod: 'TEMP_IMPORT',
        problemReasonInfo,
        measureInfo,
        taskInfo,
        verificationId: verificationId === 'create' ? undefined : verificationId,
        verificationCode: detailDs.current?.get('verificationCode'),
      },
    ]);
    handleCopyEnclosure(verificationUuid);
  };

  const handleUpdateProblem = value => {
    detailDs.current?.set('problemLov', value);
    const {
      materialId,
      materialCode,
      materialName,
      itemGroupDesc,
      problemCategory,
      processId,
      processName,
      problemDescription,
      problemReasonInfo,
      measureInfo,
    } = value;
    detailDs.current?.set('materialLov', {
      materialId,
      materialCode,
      materialName,
      itemGroupDesc,
    });
    detailDs.current?.set('fromProcessLov', {
      workcellId: processId,
      workcellName: processName,
    });
    // detailDs.current?.set('createMethod', 'PROBLEM_IMPORT');
    detailDs.current?.set('responsibleArea', problemCategory);
    detailDs.current?.set('problemDescription', problemDescription);
    (problemReasonInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
    (measureInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
    problemReasonDs.loadData(problemReasonInfo);
    measureDs.loadData(measureInfo);
    handleCopyEnclosure(value.uuid);
  };

  const attachmentProps: any = {
    name: 'verificationUuid',
    bucketName: 'qms',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const handleChangeSite = (_, oldValue) => {
    if (oldValue?.siteId) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.clearProblemData`)
              .d('将清空问题相关带入的内容，确定更换站点？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          handleUpdateProblem({});
        } else {
          detailDs.current?.set('siteLov', oldValue);
        }
      });
    }
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || siteLoading || copyEnclosureLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('SA/专项检证平台')}
          backPath={pubFlag ? '' : '/hwms/inspect-execute/sa-verification-platform/list'}
        >
          {canEdit && !pubFlag && (
            <>
              <Button
                color={ButtonColor.primary}
                icon="save"
                onClick={() => handleSave({ submitFlag: 'N' })}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && !pubFlag && (
            <Button
              icon="edit-o"
              disabled={!['NEW', 'REJECTED'].includes(status)}
              color={ButtonColor.primary}
              onClick={handleEdit}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
          {!pubFlag && (
            <Button
              disabled={!['NEW', 'REJECTED'].includes(status)}
              icon="send-o"
              onClick={() => handleSave({ submitFlag: 'Y' })}
            >
              {intl.get(`${modelPrompt}.button.saneAndSubmit`).d('保存并提交审批')}
            </Button>
          )}
          <ApprovalInfoDrawer objectTypeList={['QIS_JZ_LWS']} objectId={verificationId} />
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'taskInfo']}>
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.basicInfo`).d('检证信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="verificationCode" />
                <Lov name="sourceTempLov" onChange={handleChangeSourceTemp} />
                <Lov name="siteLov" onChange={handleChangeSite} />
                <Select name="verificationStatus" />
                <Select name="projectName" />
                <Select name="verificationType" />
                <Select name="verificationFrom" />
                <Select name="verificationPeriod" />
                <Select name="productType" />
                <Lov name="materialLov" />
                <TextField name="materialName" />
                <Select name="itemGroupDesc" />
                <Select name="failureMode" />
                <Select name="applyArea" />
                <Select name="applicableProductLine" />
                <Select name="applicableChemicalSystem" />
                <Select name="applicableProductStructure" />
                <Select name="createMethod" />
                <Lov name="createPersonLov" />
                <DateTimePicker name="creationDate" />
              </Form>
            </Panel>
            <Panel
              key="relatedProblem"
              header={intl.get(`${modelPrompt}.title.relatedProblem`).d('关联问题')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <Lov name="problemLov" onChange={handleChangeProblem} />
                <Lov name="fromProcessLov" />
                <Select name="responsibleArea" />
                <TextField name="problemDescription" />
                <Attachment {...attachmentProps} />
              </Form>
              <Table
                buttons={[
                  <RenderDeleteButton dataSet={problemReasonDs} canEdit={canEdit} />,
                  [
                    TableButtonType.add,
                    {
                      disabled: !canEdit || !['NEW', 'REJECTED'].includes(status),
                      onClick: () => problemReasonDs.create(),
                    },
                  ],
                ]}
                dataSet={problemReasonDs}
                columns={problemReasonColumns}
                customizedCode="saVerificationPlatformDetail-problemReason"
              />
              <Table
                buttons={[
                  <RenderDeleteButton dataSet={measureDs} canEdit={canEdit} />,
                  [
                    TableButtonType.add,
                    {
                      disabled: !canEdit || !['NEW', 'REJECTED'].includes(status),
                      onClick: () => measureDs.create(),
                    },
                  ],
                ]}
                dataSet={measureDs}
                columns={measureColumns}
                customizedCode="saVerificationPlatformDetail-measure"
              />
            </Panel>
            <Panel key="taskInfo" header={intl.get(`${modelPrompt}.title.taskInfo`).d('检证任务')}>
              <Table
                buttons={[
                  <RenderBatchEditButton dataSet={taskDs} canEdit={canEdit} />,
                  <RenderDeleteButton dataSet={taskDs} canEdit={canEdit} />,
                  [
                    TableButtonType.add,
                    {
                      disabled: !canEdit || !['NEW', 'REJECTED'].includes(status),
                      onClick: () => taskDs.create(),
                    },
                  ],
                ]}
                dataSet={taskDs}
                columns={taskColumns}
                dragColumnAlign={DragColumnAlign.left}
                rowDraggable={canEdit}
                onDragEnd={dataSet => {
                  dataSet.forEach(_record => {
                    _record.set('sequence', (_record.index + 1) * 10);
                  });
                }}
                customizedCode="saVerificationPlatformDetail-taskTable"
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(SaVerificationDetail);
