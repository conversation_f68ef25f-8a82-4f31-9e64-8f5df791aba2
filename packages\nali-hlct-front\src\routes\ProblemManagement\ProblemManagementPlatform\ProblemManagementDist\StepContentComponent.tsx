/**
 * @Description: 问题管理平台-步骤内容页面组件
 * @Author: <EMAIL>
 * @Date: 2023/7/7 15:03
 */
import React from 'react';
import RegisterProblemComponent from './components/RegisterProblemComponent';
import DistributeComponent from './components/DistributeComponent';
import MeasureComponent from './components/MeasureComponent';
import ReasonAnalysisComponent from './components/ReasonAnalysisComponent';
import ValidateCloseComponent from './components/ValidateCloseComponent';

export default props => {
  const {
    history,
    registerProblemDs,
    leadProblemDs,
    distributeDs,
    supplierInfoDs,
    tempMeasureDs,
    tempChangeObjectDs,
    reasonAnalysisDs,
    influenceRangeDs,
    marketActDs,
    freezeApplyDs,
    prepMeasureDs,
    enclosure8dDs,
    prepChangeObjectDs,
    problemCloseDs,
    preCloseDs,
    overtimeCloseTableDs,
    problemCloseTableDs,
    finallyScoreFormDs,
    finallyScoreTableDs,
    currentStep,
    problemGroup,
    userRole,
    measureEnableList,
    reasonConfirmList,
    currentUserId,
    problemStatus,
    enableFlag,
    srmFlag,
    perpSubmitFlag,
    handleSaveCategoryDetail,
    handleSubmitProblemInfo,
    handleSaveSupplierInfo,
    handleSaveMeasure,
    handleSubmitMeasure,
    handleChangeTotalEnable,
    handleSaveChangeObject,
    handleSaveReason,
    handleSubmitReason,
    handleChangeReasonEnable,
    handleSaveChangeRange,
    handleChangeMarketFlag,
    handleSaveFreezeInfo,
    handleSubmitFreezeInfo,
    handleSubmitUnfreezeInfo,
    totalScoreInfo,
    handleUpdateTotalScore,
    handleSaveEvaluation,
    handleStartMarketAct,
    queryMaterialLotInfo,
    handleSaveEnclosure8d,
    handleAddVerificationLibrary,
    handleAddProblemWarning,
    handleAddProblemExpand,
    handleAddProblemDuplicate,
    handleSavePreCloseInfo,
    handleSubmitPreCloseInfo,
  } = props;

  const renderStepContent = value => {
    switch (value) {
      case 0:
        return (
          <RegisterProblemComponent
            dataSet={registerProblemDs}
            leadProblemDs={leadProblemDs}
            problemGroup={problemGroup}
            userRole={userRole}
            problemStatus={problemStatus}
            handleSaveCategoryDetail={handleSaveCategoryDetail}
            handleSubmitProblemInfo={handleSubmitProblemInfo}
            queryMaterialLotInfo={queryMaterialLotInfo}
          />
        );
      case 1:
        return (
          <DistributeComponent
            dataSet={distributeDs}
            supplierInfoDs={supplierInfoDs}
            userRole={userRole}
            problemStatus={problemStatus}
            handleSaveCategoryDetail={handleSaveCategoryDetail}
            handleSubmitProblemInfo={handleSubmitProblemInfo}
            handleSaveSupplierInfo={handleSaveSupplierInfo}
            srmFlag={srmFlag}
          />
        );
      case 2:
        return (
          <MeasureComponent
            dataSet={tempMeasureDs}
            type="TEMP"
            changeObjectDs={tempChangeObjectDs}
            currentUserId={currentUserId}
            userRole={userRole}
            srmFlag={srmFlag}
            problemStatus={problemStatus}
            enableFlag={enableFlag}
            handleSaveMeasure={handleSaveMeasure}
            handleSubmitMeasure={handleSubmitMeasure}
            handleChangeTotalEnable={handleChangeTotalEnable}
            handleSaveChangeObject={handleSaveChangeObject}
            optionList={measureEnableList}
            influenceRangeDs={influenceRangeDs}
            handleSaveChangeRange={handleSaveChangeRange}
          />
        );
      case 3:
        return (
          <ReasonAnalysisComponent
            dataSet={reasonAnalysisDs}
            marketActDs={marketActDs}
            freezeApplyDs={freezeApplyDs}
            currentUserId={currentUserId}
            userRole={userRole}
            problemStatus={problemStatus}
            enableFlag={enableFlag}
            srmFlag={srmFlag}
            handleSaveReason={handleSaveReason}
            handleSubmitReason={handleSubmitReason}
            handleChangeReasonEnable={handleChangeReasonEnable}
            handleChangeMarketFlag={handleChangeMarketFlag}
            handleSaveFreezeInfo={handleSaveFreezeInfo}
            handleSubmitUnfreezeInfo={handleSubmitUnfreezeInfo}
            handleSubmitFreezeInfo={handleSubmitFreezeInfo}
            optionList={reasonConfirmList}
            handleStartMarketAct={handleStartMarketAct}
          />
        );
      case 4:
        return (
          <MeasureComponent
            dataSet={prepMeasureDs}
            type="PERP"
            changeObjectDs={prepChangeObjectDs}
            currentUserId={currentUserId}
            userRole={userRole}
            problemStatus={problemStatus}
            enableFlag={enableFlag}
            srmFlag={srmFlag}
            handleSaveMeasure={handleSaveMeasure}
            handleSubmitMeasure={handleSubmitMeasure}
            handleChangeTotalEnable={handleChangeTotalEnable}
            handleSaveChangeObject={handleSaveChangeObject}
            optionList={measureEnableList}
            influenceRangeDs={influenceRangeDs}
            enclosure8dDs={enclosure8dDs}
            handleSaveChangeRange={handleSaveChangeRange}
            handleSaveEnclosure8d={handleSaveEnclosure8d}
          />
        );
      default:
        return (
          <ValidateCloseComponent
            problemCloseDs={problemCloseDs}
            preCloseDs={preCloseDs}
            overtimeCloseTableDs={overtimeCloseTableDs}
            problemCloseTableDs={problemCloseTableDs}
            finallyScoreFormDs={finallyScoreFormDs}
            finallyScoreTableDs={finallyScoreTableDs}
            history={history}
            totalScoreInfo={totalScoreInfo}
            handleUpdateTotalScore={handleUpdateTotalScore}
            userRole={userRole}
            problemStatus={problemStatus}
            handleSaveEvaluation={handleSaveEvaluation}
            handleAddVerificationLibrary={handleAddVerificationLibrary}
            handleAddProblemWarning={handleAddProblemWarning}
            handleAddProblemExpand={handleAddProblemExpand}
            handleAddProblemDuplicate={handleAddProblemDuplicate}
            handleSavePreCloseInfo={handleSavePreCloseInfo}
            handleSubmitPreCloseInfo={handleSubmitPreCloseInfo}
            perpSubmitFlag={perpSubmitFlag}
          />
        );
    }
  };

  return renderStepContent(currentStep);
};
