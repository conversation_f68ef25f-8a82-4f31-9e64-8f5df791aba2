/**
 * @Description: 拆解申请单-详情页DS
 * @Author: <EMAIL>
 * @Date: 2023/8/14 14:09
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.teardownApplyDoc';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();
const endurl = '';

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  primaryKey: 'teardownApplyId',
  fields: [
    {
      name: 'teardownApplyNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownApplyNum`).d('申请编码'),
      disabled: true,
    },
    {
      name: 'materialLotType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotType`).d('电芯类型'),
      lookupCode: 'YP_QIS_TEARDOWN_BARCODE_TYPE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'materialLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('电芯条码'),
      lovCode: 'YP_WMS.MES.MATERIAL_LOT',
      textField: 'materialLotCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      computedProps: {
        required: ({ record }) => record?.get('materialLotType') === 'OWN',
        disabled: ({ record }) => record?.get('materialLotType') !== 'OWN',
      },
    },
    {
      name: 'materialLotId',
      bind: 'materialLotLov.materialLotId',
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('电芯条码'),
      bind: 'materialLotLov.materialLotCode',
      computedProps: {
        required: ({ record }) => record?.get('materialLotType') === 'OUTSIDE',
        disabled: ({ record }) => record?.get('materialLotType') !== 'OUTSIDE',
      },
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('电芯型号'),
      bind: 'materialLotLov.model',
      computedProps: {
        // required: ({ record }) => record?.get('materialLotType') === 'OUTSIDE',
        disabled: ({ record }) => record?.get('materialLotType') !== 'OUTSIDE',
      },
    },
    {
      name: 'materialId',
      bind: 'materialLotLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      bind: 'materialLotLov.materialCode',
      // disabled: true,
      computedProps: {
        // required: ({ record }) => record?.get('materialLotType') === 'OUTSIDE',
        disabled: ({ record }) => record?.get('materialLotType') !== 'OUTSIDE',
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      bind: 'materialLotLov.materialName',
      // disabled: true,
      computedProps: {
        // required: ({ record }) => record?.get('materialLotType') === 'OUTSIDE',
        disabled: ({ record }) => record?.get('materialLotType') !== 'OUTSIDE',
      },
    },
    {
      name: 'productFormCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productFormCode`).d('产品形式'),
      lookupCode: 'YP.QIS.PRODUCT_FORM',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        required: ({ record }) => record?.get('materialLotType') === 'OUTSIDE',
        disabled: ({ record }) => record?.get('materialLotType') !== 'OUTSIDE',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'prodlineId',
      bind: 'materialLotLov.prodlineId',
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线'),
      bind: 'materialLotLov.prodLineCode',
      disabled: true,
    },
    {
      name: 'electricVoltage',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.electricVoltage`).d('电量'),
      min: 0,
      step: 1,
      max: 100,
      computedProps: {
        required: ({ record }) => record?.get('materialLotType') === 'OWN',
      },
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('来源工序编码'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'operationName',
      computedProps: {
        required: ({ record }) => record?.get('materialLotType') === 'OWN',
      },
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    {
      label: intl.get(`${modelPrompt}.table.operationDesc`).d('来源工序描述'),
      name: 'operationDesc',
      bind: 'operationLov.description',
      disabled: true,
    },
    {
      name: 'teardownReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownReason`).d('拆解原因'),
      required: true,
    },
    {
      name: 'createPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createPerson`).d('委托人'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      valueField: 'realName',
      disabled: true,
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      bind: 'createPersonLov.id',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      bind: 'createPersonLov.realName',
    },
    {
      name: 'sampleDeliveryTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sampleDeliveryTime`).d('送样时间'),
      required: true,
    },
    {
      name: 'teardownApplyStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownApplyStatus`).d('状态'),
      lookupCode: 'YP.QIS.APPLY_STATUS',
      lovPara: { tenantId },
      defaultValue: 'NEW',
      disabled: true,
    },
    {
      name: 'teardownStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownStage`).d('阶段'),
      lookupCode: 'YP.QIS.NC_REPORT_STAGE',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => record?.get('materialLotType') === 'OWN',
      },
    },
    {
      name: 'teardownTaskId',
    },
    {
      name: 'teardownTaskNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownTaskNum`).d('拆解任务编码'),
      disabled: true,
    },
    {
      name: 'teardownRemarks',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownRemarks`).d('拆解要求说明'),
      required: true,
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
    },
  ],
  transport: {
    read: ({ data }) => {
      const { teardownApplyId } = data;
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endurl}/v1/${tenantId}/qis-teardown-apply/${teardownApplyId}/ui`,
        method: 'GET',
      };
    },
  },
});

export { detailDS };
