/**
 * @Description: 产线审核计划管理平台-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import React, { useState } from 'react';
import {
  DataSet,
  Table,
  Lov,
  Menu,
  Dropdown,
  Modal,
  Form,
  TextArea,
} from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ExpandCardC7n } from '@components/tarzan-ui';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import { tableDS, tableLineDS, lovDS } from '../stores/ProductionLineAuditPlanManagementPlatformTableDS';
import styles from './index.less';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.prodlineReview.prodlineReviewPlan';

const ProductionLineAuditPlanManagementPlatformTable = observer(props => {
  const {
    tableDs,
    tableLineDs,
    lovDs,
  } = props;
  const [headSelected, setHeadSelected] = useState([]);


  const handleDataSetSelect = () => {
    const data = tableDs.selected.map((ele) => ele.toData());
    if (data.length > 0) {
      setHeadSelected(tableDs.selected)
    } else {
      tableLineDs.loadData([]);
      setHeadSelected([])
    }
  };
  useDataSetEvent(tableDs, 'select', handleDataSetSelect);
  useDataSetEvent(tableDs, 'selectAll', handleDataSetSelect);
  useDataSetEvent(tableDs, 'unselect', handleDataSetSelect);
  useDataSetEvent(tableDs, 'unselectAll', handleDataSetSelect);

  /** @type {ColumnProps}  行列 */
  const columns = [
    {
      name: 'prodlineRevplanCode', width: 120, renderer: ({ record }) => {
        return <a onClick={() => jumpToDetail(record)}>{record.get('prodlineRevplanCode')}</a>;
      },
    },
    { name: 'statusName', width: 120 },
    { name: 'siteName', width: 120 },
    { name: 'prodlineReviewTmpNum', width: 120 },
    { name: 'reviewTypeName', width: 120 },
    { name: 'reviewStageName', width: 120 },
    { name: 'projectName', width: 120 },
    {
      name: 'prodLineNameList',
      width: 120,
      renderer: ({ value }) => {
        if (!value?.length) {
          return;
        }
        return value.map((item) => <Tag>{item}</Tag>);
      },
    },
    { name: 'scheFinishTime', width: 120 },
    { name: 'closeDate', width: 120 },
    { name: 'oneQty', width: 120 },
    { name: 'twoQty', width: 120 },
    { name: 'threeQty', width: 120 },
    { name: 'fourQty', width: 120 },
    { name: 'creationDate', width: 120 },
    { name: 'creationByName', width: 120 },
  ];
  const columnLine = [
    { name: 'elementNum', width: 120 },
    { name: 'statusName', width: 120 },
    { name: 'reviewDimensionName', width: 120 },
    { name: 'reviewItem', width: 120 },
    { name: 'reviewContent', width: 120 },
    { name: 'deliveryName', width: 120 },
    {
      name: 'deliveryTemplate', width: 120,
    },
    {
      name: 'deliveryUuid', width: 120,
    },
    { name: 'responsibleDeptName', width: 120 },
    { name: 'responsibleEmName', width: 120 },
    { name: 'scheFinishTime', width: 120 },
    { name: 'applyFlag', width: 120 },
    { name: 'noApplyReason', width: 120 },
    { name: 'autualFinishTime', width: 120 },
    { name: 'reviewResult', width: 120 },
    { name: 'nonConTerm', width: 120 },
    { name: 'releaseClass', width: 120 },
    { name: 'closeDate', width: 120 },
  ];
  // 导出查询条件
  const handleQuerySearchForm = () => {
    return {
      ...tableDs.queryDataSet.current.toData(),
      prodlineRevplanIdList: headSelected.map((record) => record?.get('prodlineRevplanId')),
    };
  };

  const jumpToDetail = (record) => {
    props.history.push({
      pathname: `/hwms/production-line-audit-plan-management-platform/create/${record.get('prodlineRevplanId')}`,
    });
  };
  // 提交
  const tableSubmit = async () => {
    const data = tableDs.selected.map((ele) => ele.get('prodlineRevplanId'));
    await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/submit/for/ui`, {
      method: 'POST',
      body: data,
    }).then((res) => {
      if (res && !res.failed) {
        notification.success({});
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
    });
  };
  // 审批同意
  const tableApplicantAgree = async () => {
    const data = tableDs.selected.map((ele) => ele.get('prodlineRevplanId'));
    await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/examinea/approve/for/ui`, {
      method: 'POST',
      body: {
        prodlineRevplanIds: data,
        approveStatus: 'AGREE',
      },
    }).then((res) => {
      if (res && !res.failed) {
        notification.success({});
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
    });
  };
  // 审批拒绝
  const tableSubmitReject = async () => {
    const data = tableDs.selected.map((ele) => ele.get('prodlineRevplanId'));
    if (data.length > 1) {
      return notification.error({
        message: intl.get(`${modelPrompt}.notification.selectOneData`).d('不允许批量驳回，驳回只能选择单条数据'),
      });
    }
    const approvalDs = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'rejectReason',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
          required: true,
        },
      ],
    });
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.rejectReason`).d('驳回原因'),
      children: (
        <>
          <Form dataSet={approvalDs} columns={1}>
            <TextArea name="rejectReason" />
          </Form>
        </>
      ),
      onOk: async () => {
        const vFlag = await approvalDs.validate();
        if (!vFlag) {
          return false;
        }
        const rejectReason = approvalDs.current.get('rejectReason');
        await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/examinea/approve/for/ui`, {
          method: 'POST',
          body: {
            prodlineRevplanIds: data,
            approveStatus: 'REJECTED',
            rejectReason,
          },
        }).then((res) => {
          if (res && !res.failed) {
            notification.success({});
            tableDs.query();
          } else {
            notification.error({ message: res.message });
            return false
          }
        });
      },
    });
  };

  const changeModule = (e) => {
    if (e) {
      props.history.push({
        pathname: `/hwms/production-line-audit-plan-management-platform/create/create`,
        state: {
          status: 'module',
          prodlineReviewTmpId: e.prodlineReviewTmpId,
        },
      });
    }
  }
  const menu = () => {
    return (
      <Menu>
        <Menu.Item>
          <a onClick={() => tableApplicantAgree()}>
            {intl.get(`${modelPrompt}.menuItem.accept`).d('同意')}
          </a>
        </Menu.Item>
        <Menu.Item>
          <a onClick={() => tableSubmitReject()}>
            {intl.get(`${modelPrompt}.menuItem.reject`).d('驳回')}
          </a>
        </Menu.Item>
      </Menu>
    );
  };

  const headerRowClick = (record) => {
    tableLineDs.setQueryParameter('prodlineRevplanId', record?.get('prodlineRevplanId'));
    tableLineDs.query();
  };

  const handleSearch = () => {
    tableLineDs.loadData([]);
  };
  useDataSetEvent(tableDs, 'query', handleSearch);
  return (
    <div style={{ height: '100%' }} className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('产线审核计划管理平台')}>
        <>
          <Dropdown overlay={menu()} type='primary' disabled={
            headSelected.length < 1 ||
          headSelected.find((item) => item.get('status') !== 'IN_APPROVAL')
          }>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              permissionList={[
                {
                  code: `button.check`,
                  type: 'button',
                  meaning: '审批按钮',
                },
              ]}
              disabled={
                headSelected.length < 1 ||
                headSelected.find((item) => item.get('status') !== 'IN_APPROVAL')
              }
            >
              {intl.get(`${modelPrompt}.button.review`).d('审批')}
            </PermissionButton>
          </Dropdown>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            permissionList={[
              {
                code: `button.submit`,
                type: 'button',
                meaning: '提交按钮',
              },
            ]}
            disabled={
              headSelected.length < 1 ||
              headSelected.find((item) => item.get('status') !== 'NEW' && item.get('status') !== 'REJECTED')
            }
            onClick={() => tableSubmit()}
          >
            {intl.get(`${modelPrompt}.button.submit`).d('提交')}
          </PermissionButton>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            permissionList={[
              {
                code: `button.create`,
                type: 'button',
                meaning: '新建按钮',
              },
            ]}
            onClick={() => {
              props.history.push({
                pathname: `/hwms/production-line-audit-plan-management-platform/create/create`,
                state: {
                  status: 'create',
                },
              });
            }}
          >
            {intl.get(`${modelPrompt}.button.create`).d('新建')}
          </PermissionButton>
          <ExcelExport
            method="GET"
            buttonText={intl.get('tarzan.common.button.export').d('导出')}
            requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/export/for/ui`} // 路径
            queryParams={handleQuerySearchForm}
            otherButtonProps={{
              icon: null,
              style: {
                color: '#ffffff',
                backgroundColor: "#0840f8",
              },
            }}
          />
          <PermissionButton
            type="c7n-pro"
            style={{
              border: 'none',
              backgroundColor: 'white',
            }}
            permissionList={[
              {
                code: `button.import`,
                type: 'button',
                meaning: '审核模板导入',
              },
            ]}
            className={styles['lov-btn-permissbutton']}
          >
            <Lov
              dataSet={lovDs}
              name="prodlineReviewTmpNumObj"
              noCache
              className='lov'
              // @ts-ignore
              mode="button"
              clearButton={false}
              onChange={(e) => changeModule(e)}
              tableProps={{
                queryFieldsLimit: 10,
              }}
            >
              {intl.get(`${modelPrompt}.button.tamplateImport`).d('审核模板导入')}
            </Lov>
          </PermissionButton>
        </>
      </Header>
      <Content>
        <Table
          customizedCode="ProductionLineAuditPlanManagementPlatformTable"
          searchCode="ProductionLineAuditPlanManagementPlatformTable"
          queryBar={TableQueryBarType.filterBar}
          dataSet={tableDs}
          columns={columns}
          queryBarProps={{ onQuery: handleSearch, fuzzyQuery: false }}
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
        />
        <ExpandCardC7n
          showExpandIcon
          title={intl.get(`${modelPrompt}.title.prodlineReviewElement`).d('产线审核要素')}
        >
          <Table
            customizable
            customizedCode="ProductionLineAuditPlanManagementPlatformTableLine"
            dataSet={tableLineDs}
            columns={columnLine}
            queryBar={TableQueryBarType.none}
            queryFieldsLimit={3}
            pagination={{ showPager: true }}
            queryBarProps={{ autoQueryAfterReset: false }}
            rowHeight={34}
            border
          />
        </ExpandCardC7n>
      </Content>
    </div >
  )
});
export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(withProps(
  () => {
    const tableDs = new DataSet({
      ...tableDS(),
    });
    const tableLineDs = new DataSet({
      ...tableLineDS(),
    });
    const lovDs = new DataSet({
      ...lovDS(),
    });
    return {
      tableDs,
      tableLineDs,
      lovDs,
    };
  },
  { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
)(ProductionLineAuditPlanManagementPlatformTable));
