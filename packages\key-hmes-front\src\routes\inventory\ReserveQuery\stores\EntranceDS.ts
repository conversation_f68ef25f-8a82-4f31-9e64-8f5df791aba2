/**
 * @Description: 库存预留日记账查询（重构） - 入口页DS
 * @Author: <EMAIL>
 * @Date: 2022/8/16 13:57
 * @LastEditTime: 2022/8/16 13:57
 * @LastEditors: <EMAIL>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import { getCurrentSiteInfo } from '@utils/utils';

const modelPrompt = 'tarzan.inventory.reserveQuery.model.reserveQueryst';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): DataSetProps => ({
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'APEX_WMS.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        defaultValue: () => {
          const  siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo }
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'startTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
      max: 'endTime',
      required: true,
    },
    {
      name: 'endTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
      min: 'startTime',
      required: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialId`).d('物料'),
      lovCode: 'APEX_WMS.METHOD.MATERIAL.PERMISSION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            enableFlag: 'Y',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      multiple: true,
    },
    {
      name: 'orgLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.orgId`).d('库位'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR_BY_ORG',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')],
            type: 'LOCATOR',
            queryLocatorCategoryList: ['AREA'],
            locatorCategoryList: ['AREA', 'INVENTORY'],
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'orgId',
      bind: 'orgLov.locatorId',
    },
    {
      name: 'lotCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次'),
      multiple: true,
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'holdType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.holdType`).d('预留类型'),
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=INVENTORY&typeGroup=HOLD_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'orderType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderType`).d('预留指令类型'),
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=INVENTORY&typeGroup=RESERVE_OBJECT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'orderCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderId`).d('预留指令编码'),
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
      textField: 'description',
      valueField: 'typeCode',
      options: new DataSet({
        autoQuery: true,
        dataKey: 'rows',
        paging: false,
        transport: {
          read: () => {
            return {
              url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
              method: 'GET',
              params: { tenantId },
              transformResponse: val => {
                const data = JSON.parse(val);
                data.rows.push({
                  description: intl.get(`tarzan.common.ownerType`).d('自有'),
                  typeCode: 'ALL',
                  typeGroup: 'OWNER_TYPE',
                });
                return {
                  ...data,
                };
              },
            };
          },
        },
      }),
    },
    {
      name: 'ownerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerId`).d('所有者查询'),
      lovCode: 'APEX_WMS.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'APEX_WMS.MODEL.CUSTOMER';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'APEX_WMS.MODEL.SUPPLIER';
            case 'OI':
              return `${BASIC.WMS_LOV_CODE_BEFORE}.MES.SO_LINE`;
            default:
              return 'APEX_WMS.MES.EMPTY';
          }
        },
        textField({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'customerCode';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'supplierCode';
            case 'OI':
              return 'soNumContent';
            default:
              return 'noData';
          }
        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerLov.customerId',
      dynamicProps: {
        bind({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'ownerLov.customerId';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'ownerLov.supplierId';
            case 'OI':
              return 'ownerLov.soLineId';
            default:
              return 'ownerLov.customerId';
          }
        },
      },
    },
  ],
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'eventTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTime`).d('预留变化时间'),
    },
    {
      name: 'changeQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.changeQuantity`).d('预留变化数量'),
    },
    {
      name: 'holdQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.holdQuantity`).d('变化后数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uom`).d('单位'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorDesc`).d('库位描述'),
    },
    {
      name: 'lotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次号'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
    {
      name: 'ownerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerDesc`).d('所有者描述'),
    },
    {
      name: 'holdTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.holdTypeDesc`).d('预留类型'),
    },
    {
      name: 'orderTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderTypeDesc`).d('预留指令类型'),
    },
    {
      name: 'orderCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderId`).d('预留指令编码'),
    },
    {
      name: 'eventType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventType`).d('事件类型'),
    },
    {
      name: 'eventTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型描述'),
    },
    {
      name: 'eventRequestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventRequestTypeCode`).d('事件请求类型'),
    },

    {
      name: 'eventRequestTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventRequestTypeDesc`).d('事件请求类型描述'),
    },
    {
      name: 'eventId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventId`).d('事件主键'),
    },
    {
      name: 'eventByUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventByUserName`).d('操作人'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inv-onhand-hold-journal/property/list/ui`,
        method: 'POST',
        data: {
          ...data,
          orgType: data.orgId ? 'LOCATOR' : 'SITE',
          orgId: data.orgId ? data.orgId : data.siteId,
          lotCodes: data.lotCodes || undefined,
          orderCode: data.orderCode || undefined,
        },
      };
    },
  },
});

export { entranceDS };
