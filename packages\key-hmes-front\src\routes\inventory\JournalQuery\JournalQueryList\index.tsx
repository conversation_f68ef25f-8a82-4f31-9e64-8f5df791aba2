/**
 * @Description: 库存日记账查询 - 入口页
 * @Author: <EMAIL>
 * @Date: 2022/7/5 13:57
 * @LastEditTime: 2023-02-01 15:54:28
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo, useRef } from 'react';
import {
  DataSet,
  Table,
  Modal,
  DatePicker,
  Button,
} from 'choerodon-ui/pro';
import { isNil } from 'lodash';
import { ViewMode } from 'choerodon-ui/pro/lib/date-picker/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import moment from 'moment';
import { Content, Header } from 'components/Page';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import ExcelExport from 'components/ExcelExport';
import { entranceDS, detailDS, documentDS} from '../stores/EntranceDS';
import DetailDrawer from './DetailDrawer';
import DocumentDrawer from './DocumentDrawer';

const modelPrompt = 'tarzan.inventory.journalQuery.model.journalQuery';
const tenantId = getCurrentOrganizationId();

const JournalQuery = () => {

  const dataSet = new DataSet({ ...entranceDS() });
  // @ts-ignore
  const detailDs = useMemo(() => new DataSet({ ...detailDS() }), []);
  // @ts-ignore
  const documentDs = useMemo(() => new DataSet({ ...documentDS() }), []);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (dataSet) {
      // @ts-ignore
      const handerQuery = flag ? dataSet.queryDataSet.addEventListener : dataSet.queryDataSet.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', {});
    }
    if (name === 'siteLov') {
      record.set('materialLov', {});
      record.set('orgLov', {});
    }
  };

  const columns: ColumnProps[] = [
    { name: 'materialCode', lock: ColumnLock.left, width: 150 },
    { name: 'materialDesc', lock: ColumnLock.left, width: 160 },
    { name: 'revisionCode', lock: ColumnLock.left, width: 120 },
    { name: 'bomCode', lock: ColumnLock.left, width: 120 },
    { name: 'model', lock: ColumnLock.left, width: 120 },
    { name: 'brand', lock: ColumnLock.left, width: 120 },
    { name: 'eventTime', align: ColumnAlign.center, width: 150 },
    { name: 'changeQuantity', width: 130 },
    { name: 'uomCode' },
    { name: 'onhandQuantity', width: 130 },
    { name: 'areaLocatorCode', width: 120 },
    { name: 'areaLocatorDesc', width: 160 },
    { name: 'locatorCode', width: 120 },
    { name: 'locatorDesc', width: 160 },
    { name: 'lotCode' },
    { name: 'qualityStatusDesc', width: 120 },
    {
      name: 'ownerTypeDesc',
      width: 110,
      renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
    },
    { name: 'ownerCode', width: 100 },
    { name: 'ownerDesc', width: 160 },
    { name: 'eventType', width: 210 },
    { name: 'eventTypeDesc', width: 120 },
    { name: 'eventRequestTypeCode', width: 120 },
    { name: 'eventRequestTypeDesc', width: 150 },
    { name: 'eventRequestId' },
    { name: 'eventId' },
    { name: 'eventByUserName' },
    { name: 'costcenterDesc' },
    { name: 'remark' },
    { name: 'applyEquipment' },
    { name: 'applyPerson' },
    { name: 'purpose' },
    { name: 'toLocator' },
    {
      lock: ColumnLock.right,
      width: 180,
      align: ColumnAlign.center,
      title: intl.get(`${modelPrompt}.column.operation`).d('操作列'),
      renderer: ({ record }) => (
        <>
          <a
            style={{ marginLeft: '12px' }}
            onClick={() => openDetailModal(record)}
          >
            {intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细')}
          </a>
          <a
            style={{ marginLeft: '12px' }}
            onClick={() => openDocumentModal(record)}
          >
            {intl.get(`${modelPrompt}.documentDetail`).d('单据明细')}
          </a>
        </>
      ),
    },
  ];

  const openDetailModal = record => {
    detailDs.setQueryParameter('invJournalId', record.get('journalId'));
    detailDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细')}</div>
        </div>
      ),
      destroyOnClose: true,
      children: <DetailDrawer ds={detailDs} />,
    });
  };

  const openDocumentModal = record => {
    documentDs.setQueryParameter('invJournalId', record.get('journalId'));
    documentDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.receiptsDetail`).d('单据明细')}</div>
        </div>
      ),
      destroyOnClose: true,
      children: <DocumentDrawer ds={documentDs} />,
    });
  };

  const handleRecentMinute = async (count) => {
    const nowTime = new Date().getTime();
    const oneMins = moment(nowTime).subtract(count, 'minutes').format('YYYY-MM-DD HH:mm:ss')

    setTimeout(() => {
      const objArr = document.getElementsByClassName('c7n-pro-popup c7n-pro-popup-wrapper c7n-pro-calendar-picker-popup c7n-pro-calendar-picker-popup-datetime c7n-pro-calendar-picker-popup-placement-bottomLeft');
      // @ts-ignore
      objArr.forEach(item => {
        item.setAttribute('hidden', true);
        // @ts-ignore
        inputRef.current.focus();
        // @ts-ignore
        dataSet.queryDataSet.current.set('startTime', oneMins);
        // @ts-ignore
        dataSet.queryDataSet.current.set('endTime', moment(nowTime));
      })
    }, 0);
  };

  const onFieldEnterDown = () => {
    dataSet.query(dataSet.currentPage);
  }

  // 处理导出按钮使用的查询参数
  const getExportQueryParams = () => {
    if (!dataSet.queryDataSet || !dataSet.queryDataSet.current) {
      return {};
    }
    const queryParams = dataSet.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return {
      ...queryParams,
      orgType: queryParams.orgId ? 'LOCATOR' : 'SITE',
      orgId: queryParams.orgId ? queryParams.orgId : queryParams.siteId,
      lotCodes: queryParams.lotCodes || undefined,
    };
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.inventory.journalQuery.view.title.journalQuery')
          .d('库存日记账查询-WMS')}
      >
        <ExcelExport
          exportAsync
          method="GET"
          requestUrl={`${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inv-journal-export/export/list/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="kcrjzcx1"
          customizedCode="kcrjzcx1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
          queryFields={{
            'startTime': <DatePicker
              mode={ViewMode.dateTime}
              renderExtraFooter={() => {
                return (
                  <div style={{ float: 'left', paddingLeft: '0.03rem' }}>
                    <Button funcType={FuncType.flat} onClick={() => handleRecentMinute(1)}>{intl.get(`${modelPrompt}.oneMinute`).d('近一分钟')}</Button>
                    <Button funcType={FuncType.flat} onClick={() => handleRecentMinute(3)}>{intl.get(`${modelPrompt}.threeMinute`).d('近三分钟')}</Button>
                  </div>
                )
              }}
              extraFooterPlacement='top'
            />,
            'endTime': <DatePicker
              mode={ViewMode.dateTime}
              renderExtraFooter={() => {
                return (
                  <div style={{ float: 'left', paddingLeft: '0.03rem' }}>
                    <Button funcType={FuncType.flat} onClick={() => handleRecentMinute(1)}>{intl.get(`${modelPrompt}.oneMinute`).d('近一分钟')}</Button>
                    <Button funcType={FuncType.flat} onClick={() => handleRecentMinute(3)}>{intl.get(`${modelPrompt}.threeMinute`).d('近三分钟')}</Button>
                  </div>
                )
              }}
              extraFooterPlacement='top'
            />,
          }}
          dataSet={dataSet}
          columns={columns}
          highLightRow
        />
        <input ref={inputRef} style={{ opacity: 0.0 }} />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inventory.journalQuery', 'tarzan.common'],
})(JournalQuery);
