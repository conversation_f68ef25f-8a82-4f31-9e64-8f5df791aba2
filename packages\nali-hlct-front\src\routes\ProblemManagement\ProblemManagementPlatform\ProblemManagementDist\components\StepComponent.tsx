/**
 * @Description: 问题管理平台-步骤条Step
 * @Author: <EMAIL>
 * @Date: 2023/7/17 19:16
 */
import React from 'react';
import styles from '../index.module.less';

export default ({ current, onChange, sourceList }) => {
  return (
    <div className={styles['steps-content']}>
      {sourceList.map((item, index) => (
        <div
          key={String(index)}
          className={`${styles.step} ${current === index ? styles.active : ''}`}
          onClick={() => onChange(index)}
        >
          <div className={styles['step-sequence']}>{index + 1}</div>
          <div className={styles['step-title']}>{item}</div>
        </div>
      ))}
    </div>
  );
};
