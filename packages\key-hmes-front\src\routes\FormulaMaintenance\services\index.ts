import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 保存头
export function SaveHeadLine() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-formulas`,
    method: 'POST',
  };
}

// 删除头
export function DeleteHeadLine() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-formulas`,
    method: 'DELETE',
  };
}

// 保存行
export function SaveLineList() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assembly-checklists`,
    method: 'POST',
  };
}
