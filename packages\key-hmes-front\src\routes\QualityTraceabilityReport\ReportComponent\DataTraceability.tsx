import React, { useMemo, useCallback } from 'react';
import { Table, DataSet, Attachment } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { useDataSetEvent } from 'utils/hooks';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableMode, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores/DataTraceabilityDS';

const modelPrompt = 'modelPrompt_code';

const DataTraceability = () => {

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const queryValidate = ({ dataSet }) => {
    const _record = dataSet.queryDataSet.current;
    if (
      !_record.get('siteId') &&
      !_record.get('identification') &&
      !_record.get('recordDateFrom') &&
      !_record.get('recordDateTo') &&
      !_record.get('workcellId') &&
      !_record.get('tagGroupId') &&
      !_record.get('inspectionResult') &&
      !_record.get('materialId')
    ) {
      notification.error({
        message: '请至少输入一个查询条件！',
      });
      return false
    }
  }

  useDataSetEvent(tableDs, 'query', queryValidate);

  const attachmentProps: any = {
    bucketName: 'mes',
    bucketDirectory: 'technology-maintenance',
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    readOnly: true,
    sortable: false,
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      { name: 'materialCode', width: 150, lock: ColumnLock.left },
      { name: 'materialName', width: 150 },
      {
        name: 'identification',
        width: 150,
      },
      { name: 'workcellName', width: 180 },
      { name: 'recordDate', width: 150 },
      { name: 'tagDescription', width: 150 },
      {
        name: 'tagValue',
        width: 150,
        renderer: ({ value, record }) => {
          if (record!.get('valueType') === 'ENCLOSURE') {
            return <Attachment record={record!} name='tagValue' {...attachmentProps} />
          }
          return value;
        },
      },
      {
        name: 'tagCalculateResult',
        width: 150,
        renderer: ({ value }) => {
          if (value === 'OK') {
            return <Tag className='hcm-tag-green'>{value}</Tag>
          }
          return <Tag className='hcm-tag-red'>{value}</Tag>
        },
      },
      { name: 'equipmentCode', width: 150 },
      { name: 'equipmentName', width: 150 },
      { name: 'statusDesc', width: 150 },
      { name: 'inStockStatus', width: 150 },
      { name: 'userName', width: 150 },
    ],
    [],
  );

  const nodeCover = useCallback(
    ({ record }) => {
      const nodeProps = { isLeaf: false };
      if (!record.get('childMaterialDtlList')?.length) {
        nodeProps.isLeaf = true;
      }
      return nodeProps;
    },
    [],
  )

  return (
    <div className="hmes-style">
      <Table
        mode={TableMode.tree}
        queryBar={TableQueryBarType.filterBar}
        queryBarProps={{
          fuzzyQuery: false,
          onQuery: () => false,
        }}
        queryFieldsLimit={8}
        dataSet={tableDs}
        columns={columns}
        onRow={nodeCover}
      />
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(DataTraceability);
