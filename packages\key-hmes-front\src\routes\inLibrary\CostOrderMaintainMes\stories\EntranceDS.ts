/**
 * @Description: 成本中心内部订单维护 - 入口页面DS
 * @Author: <EMAIL>
 * @Date: 2022/6/9 11:30
 * @LastEditTime: 2023-05-18 16:35:37
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.inLibrary.costOrderMaintainMes';
const tenantId = getCurrentOrganizationId();

// 成本中心对应的结算类别下拉框数据源
const accountCategoryCOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ACCOUNT_CATEGORY_C`,
        method: 'GET',
      };
    },
  },
});

// 内部订单对应的结算类别下拉框数据源
const accountCategoryOOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ACCOUNT_CATEGORY_O`,
        method: 'GET',
      };
    },
  },
});

const listDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'costcenterCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.costCenterCode`).d('成本中心编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'accountType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountsType`).d('结算类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ACCOUNT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'accountCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountsCategory`).d('结算类别'),
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        options: ({ record }) => {
          if (record.get('accountType')) {
            if (record.get('accountType') === 'INTERNAL_ORDER') {
              // 返回内部订单对应的类别DS
              return accountCategoryOOptionDs;
            }
            if (record.get('accountType') === 'COST_CENTER') {
              // 返回成本中心对应的类别DS
              return accountCategoryCOptionDs;
            }
          } else {
            // 返回全部的类别DS
            const accountCategoryOList: any = accountCategoryOOptionDs.toData() || [];
            const accountCategoryCList: any = accountCategoryCOptionDs.toData() || [];
            return new DataSet({
              data: [...accountCategoryOList, ...accountCategoryCList],
            });
          }
        },
      },
    },
    {
      name: 'dateFromTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
      max: 'dateEndTo',
    },
    {
      name: 'dateEndTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateEnd`).d('失效时间'),
      min: 'dateFromTo',
    },
    {
      name: 'createdByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      type: FieldType.number,
      bind: 'createdByLov.id',
    },
  ],
  fields: [
    {
      name: 'costCenterId',
      type: FieldType.number,
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'costcenterCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.costCenterCode`).d('成本中心编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'accountTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountsType`).d('结算类型'),
    },
    {
      name: 'accountCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountsCategory`).d('结算类别'),
    },
    {
      name: 'dateFromTo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateFromTo`).d('生效时间'),
    },
    {
      name: 'dateEndTo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateEndTo`).d('失效时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-costcenter-order/query?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.COST_CENTER_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.COST_CENTER_LIST.LIST`,
        method: 'GET',
      };
    },
  },
});

const drawerDS = (): DataSetProps => ({
  autoCreate: true,
  autoLocateFirst: true,
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('costCenterId');
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'costcenterCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.costCenterCode`).d('成本中心编码'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('costCenterId');
        },
      },
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
      required: true,
    },
    {
      name: 'accountType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountsType`).d('结算类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ACCOUNT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      name: 'accountCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountsCategory`).d('结算类别'),
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      dynamicProps: {
        options: ({ record }) => {
          if (record.get('accountType')) {
            if (record.get('accountType') === 'INTERNAL_ORDER') {
              // 返回内部订单对应的类别DS
              return accountCategoryOOptionDs;
            }
            if (record.get('accountType') === 'COST_CENTER') {
              // 返回成本中心对应的类别DS
              return accountCategoryCOptionDs;
            }
          } else {
            // 返回全部的类别DS
            const accountCategoryOList: any = accountCategoryOOptionDs.toData() || [];
            const accountCategoryCList: any = accountCategoryCOptionDs.toData() || [];
            return new DataSet({
              data: [...accountCategoryOList, ...accountCategoryCList],
            });
          }
        },
      },
    },
    {
      name: 'dateFromTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFromTo`).d('生效时间'),
      max: 'dateEndTo',
      required: true,
    },
    {
      name: 'dateEndTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateEndTo`).d('失效时间'),
      min: 'dateFromTo',
    },
    {
      name: 'costCenterId',
    },
  ],
});

export { listDS, drawerDS };
