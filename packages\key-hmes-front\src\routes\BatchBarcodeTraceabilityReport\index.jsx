/**
 * @feature PageHeaderWrapper
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table, Button, Row, Col, TextField, Form, Icon, Select } from 'choerodon-ui/pro';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { isNil } from 'lodash';
import { observer } from 'mobx-react';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import { initialDs } from './stories/InitialDs';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

const modelPrompt = 'tarzan.inventory.initial.model';

/**
 * 头行结构的表单示例
 */
const Initial = observer(props => {
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [isExport, setIsExport] = useState(true);
  const [panelTableColumns, setPanelTableColumns] = useState([
    // { name: 'seq' },
    // { name: 'status' },
    // { name: 'message' },
    // { name: 'siteCode' },
    // { name: 'equipmentCode' },
    // { name: 'equipmentName' },
  ]);

  useEffect(() => {
    if (props.dataSet) {
      props.dataSet.addEventListener('load', () => {
        if (props.dataSet?.current?.toData().message) {
          notification.error({
            message: props.dataSet?.current?.toData().message,
          });
          setPanelTableColumns([]);
          props.dataSet.loadData([])
          return;
        }
        const dataAll = props.dataSet.toData();
        const titleList = dataAll.length > 0 ? dataAll[0].columnsList : [];
        const columnsList = dataAll.length > 0 ? dataAll[0].titleList : [];
        if (dataAll.length > 0 && titleList && titleList.length > 0) {
          const dyComlumsTemp = [];
          titleList.forEach((item, index) => {
            dyComlumsTemp.push({ label: item, name: columnsList[index] });
            props.dataSet.addField(columnsList[index], {
              name: columnsList[index],
              label: item,
            });
          });
          setPanelTableColumns(dyComlumsTemp);
        } else {
          setPanelTableColumns([
            // { name: 'seq' },
            // { name: 'status' },
            // { name: 'message' },
            // { name: 'siteCode' },
            // { name: 'equipmentCode' },
            // { name: 'equipmentName' },
          ]);
        }
      });
    }
  }, []);

  useEffect(() => {
    if (props.dataSet) {
      props.dataSet.queryDataSet.addEventListener('update', () => {
        const {
          processBarcodeListStr,
          orderType,
          identificationLevel,
        } = props.dataSet?.queryDataSet?.toJSONData()[0];
        setIsExport(!processBarcodeListStr || !orderType || !identificationLevel)
      });
    }
  }, [props.dataSet.queryDataSet]);

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Col span={18}>
            <Form columns={4} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="processBarcodeListStr"
                onChange={handleBarCode}
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'processBarcodeListStr', '原材料条码', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <Select name="orderType" onChange={handleLevel} />
              <TextField name="identificationLevel" disabled multiple />
              <TextField name="traceLevel" disabled multiple />
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                  setIsExport(true)
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleBarCode = e => {
    if (!e) {
      props.dataSet?.queryDataSet.current.set('identificationLevel', []);
      props.dataSet?.queryDataSet.current.set('traceLevel', []);
    }
  };
  const handleLevel = e => {
    if (!e) {
      props.dataSet?.queryDataSet.current.set('identificationLevel', []);
      props.dataSet?.queryDataSet.current.set('traceLevel', []);
    } else {
      handleFill();
    }
  };
  const handleFill = () => {
    const { processBarcodeListStr, orderType } = props.dataSet?.queryDataSet?.toJSONData()[0];
    if (orderType && processBarcodeListStr) {
      return request(
        `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-trace-rel-priors/level/query`,
        {
          method: 'POST',
          body: {
            processBarcodeList: processBarcodeListStr ? processBarcodeListStr.split(',') : [],
            orderType,
          },
        },
      ).then(res => {
        if (res?.success) {
          const selectList = res.rows;
          if (selectList.length > 0) {
            props.dataSet?.queryDataSet.current.set('identificationLevel', [selectList[0].meaning]);
            props.dataSet?.queryDataSet.current.set(
              'traceLevel',
              selectList.map(item => item.meaning),
            );
          }
        } else {
          props.dataSet?.queryDataSet.current.set('identificationLevel', []);
          props.dataSet?.queryDataSet.current.set(
            'traceLevel',
            [],
          );
          props.dataSet.loadData([]);
          notification.error({
            message: res?.message,
          });
        }
      });
    }
  };
  const handleSearch = async () => {
    const {
      processBarcodeListStr,
      orderType,
      identificationLevel,
    } = props.dataSet?.queryDataSet?.toJSONData()[0];
    if (!processBarcodeListStr || !orderType || !identificationLevel) {
      notification.error({
        message: intl.get(`${modelPrompt}.pleaseEnterQueryCriteria`).d('请输入查询条件'),
      });
      return;
    }
    props.dataSet?.queryDataSet.current.set('processBarcodeList', processBarcodeListStr.split(','));
    props.dataSet.query();
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleFill();
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: props.dataSet,
    onOpenInputModal,
  };

  const getExportQueryParams = () => {
    const queryParams = props.dataSet.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-trace-rel-priors/export/ui`;
    request(url, {
      method: 'POST',
      responseType: 'blob',
      body: {...queryParams, traceLevel: null},
    }).then(res => {
      const file = new Blob([res], { type: 'application/vnd.ms-excel' }); // 解析文件
      const fileURL = URL.createObjectURL(file); // 生成文件地址
      const fileName = '批量追溯条码报表.xls';
      const elink = document.createElement('a'); // 生成a标签
      elink.download = fileName; // a标签下载名
      elink.style.display = 'none'; // 设置a标签隐藏，不在页面展示
      elink.href = fileURL; // 设置a标签下载地址
      document.body.appendChild(elink); // 在body中添加a标签
      elink.click();  // 手动调用a标签点击下载属性
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink); // 在body中删除a标签
    });
  };

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get(`${modelPrompt}.BatchBarcodeTraceabilityReport`).d('批量追溯条码报表')}
        header={
          <Button
            onClick={getExportQueryParams}
            disabled={isExport}
          >
            {intl.get(`tarzan.acquisition.dataItem..export`).d('导出')}
          </Button>
        }
      >
        <Table
          searchCode="BatchBarcodeTraceabilityReport"
          customizedCode="BatchBarcodeTraceabilityReport"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={props.dataSet}
          columns={panelTableColumns}
        />
        <LovModal {...lovModalProps} />
      </PageHeaderWrapper>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.product.processing.parameters.supplement', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...initialDs(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Initial),
);
