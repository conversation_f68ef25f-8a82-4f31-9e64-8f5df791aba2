/*
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2024-02-02 14:43:00
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-02-06 16:53:56
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, Form, TextField, Lov, DataSet, Table, TextArea, NumberField } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/es/table/Column';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { downloadFile } from '@services/api';
import { BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST } from '@/utils/constants';
import ExcelUpload, { ExcelUploadProps } from './components/ExcelUpload';
import { formDS, tableDS } from '../stores/sizeAppearanceDS';
import { analyseResultDS } from '../stores/DetailDS';
import { QueryMsaTypeData, SaveMsaTypeData } from '../services';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

const SizeAppearance = props => {
  const { msaStatus, currentUserFlag, msaTaskLineId } = props;
  // 一行有几组数据
  const [ columnGroupQty ] = useState(2);
  // 当前数据是否有保存过，是否展示分析结果信息
  const [showAnalyseResult, setShowAnalyseResult] = useState<boolean>(false);
  const formDs = useMemo(() => new DataSet(formDS()), []);
  const tableDs = useMemo(() => new DataSet(tableDS(columnGroupQty)), [columnGroupQty]);
  // 分析结果DS
  const analyseResultDs = useMemo(() => new DataSet(analyseResultDS()), []);
  // 查询任务数据
  const { run: queryMsaTypeData, loading: queryDataLoading } = useRequest(QueryMsaTypeData(), { manual: true, needPromise: true });
  // 保存任务数据
  const { run: saveMsaTypeData, loading: saveDataLoading } = useRequest(SaveMsaTypeData(), { manual: true });

  useEffect(() => {
    handleInitData();
  }, []);

  const handleInitData = async () => {
    const res = await queryMsaTypeData({ params: { msaTaskLineId }});
    if (res?.success) {
      handleFormatAsyncData(res?.rows);
    }
  };

  // 处理接口返回的数据，得到DS的数据
  const handleFormatAsyncData = (dataSource, saveFlag = true) => {
    const {
      sizeAppearanceInfo = {},
      msaResult = '',
      msaConclusion = '',
      measuredBy,
      measuredByName,
      sampleDescription,
      tolerance,
    } = dataSource;
    const { measureValueList, absoluteValueErrorList } = sizeAppearanceInfo;
    if (measureValueList?.length) {
      // 表里有数据
      setShowAnalyseResult(true);
      const _tableData: any = [];
      (measureValueList || []).forEach((columnItem) => {
        const { measureDataRow, measureDataValue, standardValue } = columnItem;
        // 当前数据应该在第一列还是第二列
        const columnIndex: number = (measureDataRow - 1) % columnGroupQty;
        // 当前数据在第几行
        const rowIndex: number = Math.floor((measureDataRow - 1) / columnGroupQty);
        const currentData = {
          [`sequence_${columnIndex}`]: measureDataRow,
          [`measureDataValue_${columnIndex}`]: measureDataValue,
          [`standardValue_${columnIndex}`]: standardValue,
          [`saveFlag_${columnIndex}`]: saveFlag,
        };
        if (columnIndex === 0) {
          _tableData.push(currentData);
        } else {
          _tableData[rowIndex] = {
            ..._tableData[rowIndex],
            ...currentData,
          }
        }
      });
      if ((measureValueList || [])?.length % columnGroupQty !== 0) {
        const addColumnQty = columnGroupQty - (measureValueList || [])?.length % columnGroupQty;
        // 要添加的列序号从多少开始
        let startRow = (measureValueList || [])?.length + 1;
        for (let i = 0; i < addColumnQty; i++) {
          // 当前数据应该在第一列还是第二列
          const columnIndex: number = (startRow - 1) % columnGroupQty;
          // 当前数据在第几行
          const rowIndex: number = Math.floor((startRow - 1) / columnGroupQty);
          _tableData[rowIndex] = {
            ..._tableData[rowIndex],
            [`sequence_${columnIndex}`]: startRow,
            [`saveFlag_${columnIndex}`]: false,
          };
          // 序号+1
          startRow++;
        }
      }
      (absoluteValueErrorList || []).forEach((absoluteErrorItem, absoluteErrorIndex) => {
        // 当前数据应该在第一列还是第二列
        const columnIndex: number = absoluteErrorIndex % columnGroupQty;
        // 当前数据在第几行
        const rowIndex: number = Math.floor(absoluteErrorIndex / columnGroupQty);
        _tableData[rowIndex] = {
          ..._tableData[rowIndex],
          [`absoluteErrorValue_${columnIndex}`]: absoluteErrorItem,
        };
      });
      tableDs.loadData(_tableData);
    } else {
      const createItem = {};
      for (let i = 0; i < columnGroupQty; i++) {
        createItem[`sequence_${i}`] = 1 + i;
        createItem[`saveFlag_${i}`] = false;
      }
      tableDs.create(createItem);
    }
    if (saveFlag) {
      formDs.loadData([{
        sampleDescription,
        measuredBy,
        measuredByName,
        tolerance,
      }]);
    }
    analyseResultDs.loadData([{
      msaResult,
      msaConclusion,
    }]);
  };

  const getColumnsGroup = (columnIndex) => ([
    {
      name: `sequence_${columnIndex}`,
      align: ColumnAlign.center,
    },
    {
      name: `measureDataValue_${columnIndex}`,
      align: ColumnAlign.center,
      editor: () => !(['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag),
    },
    {
      name: `standardValue_${columnIndex}`,
      align: ColumnAlign.center,
      editor: () => !(['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag),
    },
    {
      name: `absoluteErrorValue_${columnIndex}`,
    },
  ]);

  const handleAdd = () => {
    let maxSequence = 0;
    tableDs.forEach((_record) => {
      if (_record?.get(`sequence_${columnGroupQty - 1}`) > maxSequence) {
        maxSequence = _record?.get(`sequence_${columnGroupQty - 1}`);
      }
    });
    const createItem = {};
    for (let i = 0; i < columnGroupQty; i++) {
      createItem[`sequence_${i}`] = maxSequence + 1 + i;
      createItem[`saveFlag_${i}`] = false;
    }
    tableDs.create(createItem);
  };

  const handleDelete = (record) => {
    tableDs.remove(record);
  };

  const columns: any = useMemo(() => {
    let _columnGroup: ColumnProps[] = [];
    for (let i = 0; i < columnGroupQty; i++) {
      _columnGroup = [
        ..._columnGroup,
        ...getColumnsGroup(i),
      ];
    }
    const getDisabledDelete = (record, _columnGroupQty) => {
      let disabledFlag = true;
      for (let i = 0; i < _columnGroupQty; i++) {
        if (!record?.get(`saveFlag_${i}`)) {
          disabledFlag = false;
        }
      }
      return disabledFlag;
    }
    return [
      {
        header: () => (
          <Button
            icon="add"
            funcType={FuncType.flat}
            onClick={handleAdd}
            size={Size.small}
            disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
          />
        ),
        align: ColumnAlign.center,
        width: 60,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDelete(record)}
          >
            <Button
              icon="remove"
              funcType={FuncType.flat}
              size={Size.small}
              disabled={getDisabledDelete(record, columnGroupQty) || ['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
            />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      ..._columnGroup,
    ];
  }, [msaStatus, currentUserFlag, columnGroupQty]);

  // 模板下载
  const downloadTemp = async () => {
    await downloadFile({
      requestUrl: `/himp/v1/${tenantId}/template/QIS_MSA_IMPORT_SIZE/excel`,
      queryParams: [{ name: 'tenantId', value: tenantId }],
      method: 'GET',
    });
  };

  const excelUploadProps: ExcelUploadProps = {
    url: `${API_HOST}${
      BASIC.TARZAN_SAMPLING
    }/v1/${tenantId}/qis-msa-analysis/import/ui?msaTaskLineId=${
      props.msaTaskLineId
    }&sampleDescription=${formDs.current?.get('sampleDescription') ||
      ''}&measuredBy=${formDs.current?.get('measuredBy') || ''}`,
    params: {},
    onSuccess: res => handleFormatAsyncData(
      res.rows || {},
      false,
    ),
  };

  // 点击保存按钮的回调
  const handleSaveData = async () => {
    const tableValidateRes = await tableDs.validate();
    const formValidateRes = await formDs.validate();
    if (!tableValidateRes || !formValidateRes) {
      return;
    }
    saveMsaTypeData({
      params: handleFormatSaveData(),
      onSuccess: (res) => {
        // 处理数据
        handleFormatAsyncData(res);
        props.updateHeaderInfo(); // 更新头部
        notification.success({});
      },
    })
  };

  const handleFormatRow = (rowData, _columnGroupQty, _maxSeq) => {
    const _newData: any = [];
    for (let i = 0; i < _columnGroupQty ; i++) {
      if (rowData[`measureDataValue_${i}`] && rowData[`standardValue_${i}`]) {
        _newData.push({
          measureDataColumn: 1,
          measureDataRow: _maxSeq + 1 + i,
          measureDataValue: rowData[`measureDataValue_${i}`],
          standardValue: rowData[`standardValue_${i}`],
        });
      }
    }
    return _newData;
  }

  // 处理保存数据
  const handleFormatSaveData = () => {
    const formData = formDs.current?.toData();
    const tableData = tableDs.toData();
    let _newTableData: any = [];
    let currentMaxSequence = 0;
    tableData.forEach((item) => {
      const _rowData = handleFormatRow(item, columnGroupQty, currentMaxSequence);
      _newTableData = [
        ..._newTableData,
        ..._rowData,
      ];
      currentMaxSequence += _rowData?.length;
    });
    return {
      msaTaskLineId: props.msaTaskLineId,
      ...formData,
      tableInfo: [{ measureTableList: _newTableData }],
    };
  }

  const RenderHelp = () => {
    const info = intl.get(`${modelPrompt}.sizeAppearance.help`).d('1、存在误差绝对值>公差/10，则不合格<br />2、所有数据的误差绝对值<=公差/10，则合格');
    const descriptionList = info.split('<br />');
    return (
      <div>
        {descriptionList.map(item => (
          <p>{item}</p>
        ))}
      </div>
    );
  };

  return (
    <div>
      <Button
        color={ButtonColor.primary}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
        loading={saveDataLoading || queryDataLoading}
        onClick={() => handleSaveData()}
      >
        {intl.get(`${modelPrompt}.button.save`).d('保存')}
      </Button>
      <Button onClick={downloadTemp} icon="get_app">
        {intl.get(`${modelPrompt}.templateDownload`).d('模板下载')}
      </Button>
      <ExcelUpload
        {...excelUploadProps}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      />
      <Form
        dataSet={formDs}
        columns={3}
        style={{ marginTop: 10 }}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        <NumberField name="tolerance" />
        <TextField name="sampleDescription" />
        <Lov name="improveByObj" />
      </Form>
      <Table dataSet={tableDs} columns={columns} />
      {showAnalyseResult && (
        <Form labelWidth={100} disabled dataSet={analyseResultDs}>
          <TextField name="msaResult" help={<RenderHelp />} />
          <TextArea name="msaConclusion" rows={7} />
        </Form>
      )}
    </div>
  );
};

export default SizeAppearance;
