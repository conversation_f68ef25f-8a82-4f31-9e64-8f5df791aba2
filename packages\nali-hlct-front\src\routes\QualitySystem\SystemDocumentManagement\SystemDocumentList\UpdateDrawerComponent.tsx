/*
 * @Description: 升版抽屉组件
 * @Author: <<EMAIL>>
 * @Date: 2023-11-03 15:16:04
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-11-06 20:18:29
 */
import React from 'react';
import { Form, TextField, Select, Lov, DatePicker, Attachment } from 'choerodon-ui/pro';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';

export default (props) => {
  const { record } = props;

  const attachmentProps: any = {
    name: 'uuid',
    bucketName: 'qms',
    max: 1,
    bucketDirectory: 'system-document-management',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', '.visio', '.project'],
    labelLayout: LabelLayout.none,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const handleUpdateProcessType = value => {
    if (value) {
      if (value.indexOf('S') !== -1) {
        record.set('processType', 'SP');
      }
      if (value.indexOf('C') !== -1) {
        record.set('processType', 'COP');
      }
      if (value.indexOf('M') !== -1) {
        record.set('processType', 'MP');
      }
    } else {
      record.set('processType', '');
    }
  };

  return (
    <Form record={record}>
      <TextField name="fileCode" />
      <TextField name="fileName" />
      <Select name="fileLevel" />
      <Select name="processType" />
      <Lov name="departmentLov" />
      <TextField name="editedDepartmentParentName" />
      <Lov name="responsLov" />
      <DatePicker name="editedDate" />
      <Select name="affliatedProcess" onChange={handleUpdateProcessType} />
      <Attachment { ...attachmentProps } />
      <Select name="upgradeType" />
      <Lov name="levelLimitLov" />
      <Lov name="relatedFileList" />
    </Form>
  )
}