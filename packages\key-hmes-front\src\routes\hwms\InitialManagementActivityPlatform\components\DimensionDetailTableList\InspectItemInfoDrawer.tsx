/**
 * @Description: 检验项目组维护明细界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:55:10
 * @LastEditTime: 2023-06-05 17:34:32
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect } from 'react';
import intl from 'utils/intl';
import {
  Form,
  TextField,
  Select,
  Switch,
  NumberField,
  Lov,
  Row,
  Col,
  Attachment,
} from 'choerodon-ui/pro';
import axios from 'axios';
import { LabelLayout, LabelAlign, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import myInstance from '@utils/myAxios';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { FetchRuleCodeDetailConfig, isJSONString } from '@/utils';
import InputValueComponent from '../InputValueComponent';

const modelPrompt = 'tarzan.initialManagementActivity';
const { Item } = Form;
const { Option } = Select;

const InspectItemInfoDrawer = props => {
  const {
    canEdit,
    tenantId,
    customizeForm,
    warnNumberDS,
    trueNumberDS,
    falseNumberDS,
    editTableFormDS,
    formDs,
    tableDS,
    formulaListTableDs,
    inspectBusinessType,
    schemeType,
  } = props;

  const { run: fetchRuleCodeDetail, loading: fetchRuleCodeDetailLoading } = useRequest(
    FetchRuleCodeDetailConfig(),
    {
      manual: true,
      needPromise: true,
    },
  );

  const [createData, setCreateData] = useState({});

  // 数据类型
  const [dataType, setDataType] = useState(
    editTableFormDS.current && editTableFormDS.current.get('dataType')
      ? editTableFormDS.current.get('dataType')
      : null,
  );

  // 值列表
  const [valueLists, setValueList] = useState(
    editTableFormDS.current && editTableFormDS.current.get('valueLists')
      ? editTableFormDS.current.get('valueLists')
      : [],
  );

  // 数值类型-预警值
  const [warnNumberList, setWarnNumberList] = useState([]);
  // 数值类型-符合值
  const [trueNumberList, setTrueNumberList] = useState([]);
  // 数值类型-不符合值
  const [falseNumberList, setFalseNumberList] = useState([]);

  const [list, setList] = useState([]);
  const [dataList, setDataList] = useState([]);
  useEffect(() => {
    if (schemeType === 'CONTROL_PLAN') {
      editTableFormDS.setState('required', true);
    } else {
      editTableFormDS.setState('required', false);
    }
    if (inspectBusinessType === 'RELEASE_MES') {
      editTableFormDS.setState('inspectBusinessType', true)
    } else {
      editTableFormDS.setState('inspectBusinessType', false)
    }
    axios({
      url: `/hpfm/v1/${tenantId}/lovs/value/batch?typeGroup=YP.QIS.SUB_ITEM_TYPE`,
      method: 'get',
    }).then(res => {
      if (res) {
        const { typeGroup } = res;
        setList(typeGroup);
      }
    })
  }, [])

  useEffect(() => {
    // 保存初始数据
    const tableData = editTableFormDS.toData()[0];
    const {
      isNewRow,
      inspectionItemRowUuid,
      inspectObjectDimensionId,
      inspectObjectDmsItemId,
      sequence,
    } = tableData;
    setCreateData({
      isNewRow,
      inspectionItemRowUuid,
      inspectObjectDimensionId,
      inspectObjectDmsItemId,
      sequence,
    });

    const _warningValueList = handleValueList(editTableFormDS.current?.get('warningValueList'));
    const _trueValueList = handleValueList(editTableFormDS.current?.get('trueValueList'));
    const _falseValueList = handleValueList(editTableFormDS.current?.get('falseValueList'));

    formulaListTableDs.loadData(editTableFormDS.current.get('formulaList'));

    setWarnNumberList(_warningValueList);
    warnNumberDS.loadData(_warningValueList);
    setTrueNumberList(_trueValueList);
    trueNumberDS.loadData(_trueValueList);
    setFalseNumberList(_falseValueList);
    falseNumberDS.loadData(_falseValueList);
  }, [editTableFormDS]);

  useEffect(() => {
    if (trueNumberList.length < 1 && warnNumberList.length > 0) {
      setWarnNumberList([]);
    }
  }, [trueNumberList]);

  const formulaLovChange = async value => {
    if (value) {
      const { ruleCode } = value;
      const ruleCodeDetail = await fetchRuleCodeDetail({
        params: {
          ruleCode,
          tenantId,
        },
      });
      if (ruleCodeDetail && typeof ruleCodeDetail === 'object') {
        const newRuleCodeDetail = ruleCodeDetail.map(item => {
          return {
            fieldCode: item.fieldCode,
            fieldName: item.fieldName,
            isRequired: item.fieldCode === 'decimalNumber' ? 'N' : item.isRequired,
          };
        });
        editTableFormDS.current.set('formulaList', newRuleCodeDetail);
        formulaListTableDs.loadData(newRuleCodeDetail);
      } else {
        editTableFormDS.current.set('formulaList', undefined);
      }
    } else {
      editTableFormDS.current.set('formulaList', undefined);
    }
  };

  // 处理多值数据
  const handleValueList = list => {
    return (list || []).map(item => {
      if (item.valueType === 'single') {
        item.singleValued = item.dataValue;
      } else if (item.valueType === 'section') {
        item.multiValued = {
          start: item.leftValue,
          end: item.rightValue,
        };
      }
      return item;
    });
  };

  // 带出检验项目信息
  const handleChangeInspectItem = value => {
    const inspectionItemBasisDsData = formDs.toData()[0];
    if (!value || typeof value !== 'object') {
      editTableFormDS.loadData([]);
      formulaListTableDs.loadData([]);
      return;
    }

    value.requiredFlag = value.requiredFlag || 'Y';
    value.destructiveExperimentFlag = value.destructiveExperimentFlag || 'N';
    value.outsourceFlag = value.outsourceFlag || 'N';

    if (['TEXT', 'DECISION_VALUE'].includes(value.dataType)) {
      value.trueValue =
        (value.trueValueList || []).length > 0 ? value.trueValueList[0].dataValue : null;
      value.falseValue =
        (value.falseValueList || []).length > 0 ? value.falseValueList[0].dataValue : null;
    }
    if (value.dataType === 'VALUE_LIST') {
      value.trueValue =
        (value.trueValueList || []).length > 0
          ? value.trueValueList.map(trueItem => trueItem.dataValue)
          : null;
      value.falseValue =
        (value.falseValueList || []).length > 0
          ? value.falseValueList.map(falseItem => falseItem.dataValue)
          : null;
    }
    if (['CALCULATE_FORMULA'].includes(value.dataType)) {
      const formula = isJSONString(value.formula || '');
      if (formula) {
        const {
          formulaMode,
          formulaDisplayPosition,
          formulaSourceId,
          formulaId,
          formulaCode,
          formulaName,
          dimension,
          formulaList,
        } = formula;
        value.formulaSourceId = formulaSourceId;
        value.formulaMode = formulaMode;
        value.formulaDisplayPosition = formulaDisplayPosition;
        value.formulaId = formulaId;
        value.formulaCode = formulaCode;
        value.formulaName = formulaName;
        value.dimension = dimension;
        value.formulaList = formulaList;
      } else {
        value.formulaSourceId = null;
        value.formulaMode = null;
        value.formulaDisplayPosition = null;
        value.formulaId = null;
        value.formulaCode = null;
        value.formulaName = null;
        value.dimension = null;
        value.formulaList = null;
      }
    }
    formulaListTableDs.loadData(value.formulaList || []);

    value.samplingMethodCode = inspectionItemBasisDsData.samplingMethodCode;
    value.samplingMethodDesc = inspectionItemBasisDsData.samplingMethodDesc;
    value.samplingMethodId = inspectionItemBasisDsData.samplingMethodId;
    value.samplingDimension = inspectionItemBasisDsData.samplingDimension;

    setWarnNumberList(value.warningValueList || []);
    warnNumberDS.loadData(value.warningValueList || []);
    setTrueNumberList(value.trueValueList || []);
    trueNumberDS.loadData(value.trueValueList || []);
    setFalseNumberList(value.falseValueList || []);
    falseNumberDS.loadData(value.falseValueList || []);
    const _dataType = value.dataType;
    setDataType(_dataType);

    if (['TEXT', 'DECISION_VALUE'].includes(_dataType)) {
      editTableFormDS.current.set(
        'trueValue',
        value.trueValueList?.length > 0 ? value.trueValueList[0].dataValue : null,
      );
      editTableFormDS.current.set(
        'falseValue',
        value.falseValueList?.length > 0 ? value.falseValueList[0].dataValue : null,
      );
    } else if (_dataType === 'VALUE_LIST') {
      editTableFormDS.current.set(
        'trueValue',
        value.trueValueList?.length > 0
          ? value.trueValueList.map(trueItem => trueItem.dataValue)
          : null,
      );
      editTableFormDS.current.set(
        'falseValue',
        value.falseValueList?.length > 0
          ? value.falseValueList.map(falseItem => falseItem.dataValue)
          : null,
      );
    }

    setValueList(value.valueLists || []);
    editTableFormDS.loadData([{ ...value, ...createData }]);
    if (value.enclosure) {
      // 复制附件uuid
      myInstance
        .post(`hfle/v1/${tenantId}/files/copy-file`, { uuidList: [value.enclosure] })
        .then(res => {
          if (res && res.data && res.data[value.enclosure]) {
            editTableFormDS.current.set('enclosure', res.data[value.enclosure]);
          }
        });
    }
  };

  // 数据类型变化时清空相关数据
  const handleChangeDataType = (value, oldValue) => {
    formulaListTableDs.loadData([]);
    editTableFormDS.current.set('formulaList', null);
    editTableFormDS.current.set('formulaId', null);
    editTableFormDS.current.set('formulaCode', null);
    editTableFormDS.current.set('formulaName', null);
    editTableFormDS.current.set('dimension', null);
    editTableFormDS.current.set('uomLov', null);
    editTableFormDS.current.set('decimalNumber', null);
    editTableFormDS.current.set('processMode', null);
    editTableFormDS.current.init('valueLists', null);
    editTableFormDS.current.init('trueValue', null);
    editTableFormDS.current.init('falseValue', null);
    editTableFormDS.current.set('earlyWarningValue', null);
    editTableFormDS.current.init('trueValueList', []);
    editTableFormDS.current.init('falseValueList', []);
    editTableFormDS.current.set('warningValueList', []);
    if (value === 'CALCULATE_FORMULA') {
      editTableFormDS.current.init('enterMethod', 'AUTOMATIC_COLLECTION');
      editTableFormDS.current.init('samplingMethodLov', undefined);
      editTableFormDS.current.init('employeePosition', undefined);
      editTableFormDS.current.init('frequencyParams', undefined);
      editTableFormDS.current.init('sameGroupIdentification', undefined);
      editTableFormDS.current.init('outsourceFlag', 'N');
      editTableFormDS.current.init('destructiveExperimentFlag', 'N');
      editTableFormDS.current.init('inspectFrequencyObject', null);
      editTableFormDS.current.set('m', null);
      editTableFormDS.current.set('n', null);
    }
    if (oldValue === 'CALCULATE_FORMULA') {
      editTableFormDS.current.init('samplingMethodLov', formDs.current.get('samplingMethodLov'));
    }
    const deleteRecord: any = [];
    tableDS.forEach(record => {
      if (record.get('formulaSourceId') === editTableFormDS.current.get('inspectItemId')) {
        deleteRecord.push(record);
      }
    });
    tableDS.delete(deleteRecord, false);
    setWarnNumberList([]);
    warnNumberDS.loadData([]);
    setTrueNumberList([]);
    trueNumberDS.loadData([]);
    setFalseNumberList([]);
    falseNumberDS.loadData([]);
    setDataType(value);
  };

  const handleChangeValueList = value => {
    editTableFormDS.current.init('trueValue', null);
    editTableFormDS.current.init('falseValue', null);
    setValueList(value);
  };

  // 检验频率处理列表展示值
  const handleChangeFrequency = () => {
    editTableFormDS.current.set('m', null);
    editTableFormDS.current.set('n', null);
  };

  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    readOnly: !canEdit,
  };

  const handleChang = (value) => {
    const dataList = list.filter((val) => (value === val.tag))
    if (value === 'SIZE' || value === 'WELDING') {
      setDataList(dataList)
    } else {
      setDataList(list);
    }
  }

  const getStatus = dataList.map((ele) => (
    <Option key={ele.orderSeq} value={ele.value}>
      {ele.meaning}
    </Option>
  ));

  return (
    <>
      {customizeForm(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_BASIC.ITEM_DRAWER`,
        },
        <Form
          dataSet={editTableFormDS}
          columns={3}
          labelWidth={112}
          disabled={!canEdit}
          labelLayout={LabelLayout.horizontal}
          labelAlign={LabelAlign.right}
        >
          <NumberField name="sequence" />
          <Lov name="inspectItemLov" onChange={handleChangeInspectItem} clearButton={false} />
          <TextField name="inspectItemDesc" />
          <Select name="inspectItemType" onChange={(value) => { handleChang(value) }} />
          <Select name='subItemType'>{getStatus}</Select>
          <Select name='featureType' />
          <Select name='itemPattern' />
          <TextField name="inspectGroupDesc" />
          <TextField name="taskCategory" />
          <TextField name="inspectBasis" />
          <Select name="qualityCharacteristic" />
          <Select name="inspectTool" />
          <Select name="inspectMethod" />
          <Switch
            name="requiredFlag"
            label={intl.get(`${modelPrompt}.label.requiredFlag`).d('必填项目')}
          />
          <TextField name="technicalRequirement" />
          <Select name="enterMethod" />
          <Select
            name="dataType"
            optionsFilter={record => {
              return record.get('value') !== 'CALCULATE_FORMULA';
            }}
            onChange={handleChangeDataType}
          />
          {['VALUE', 'CALCULATE_FORMULA'].includes(dataType) && (
            <>
              <Lov name="uomLov" />
              <NumberField name="decimalNumber" />
              <Select name="processMode" />
              <Item
                name="trueValue"
                rowSpan={trueNumberList.length || 1}
                label={intl.get(`${modelPrompt}.model.trueValue`).d('符合值')}
                style={{ padding: 0 }}
              >
                <InputValueComponent
                  {...{
                    showStandard: true,
                    canEdit: canEdit && falseNumberList.length < 1,
                    numberDS: trueNumberDS,
                    numberList: trueNumberList,
                    setNumberList: setTrueNumberList,
                  }}
                />
              </Item>
              <Item
                name="falseValue"
                rowSpan={falseNumberList.length || 1}
                label={intl.get(`${modelPrompt}.model.falseValue`).d('不符合值')}
                style={{ padding: 0 }}
              >
                <InputValueComponent
                  {...{
                    canEdit: canEdit && trueNumberList.length < 1,
                    numberDS: falseNumberDS,
                    numberList: falseNumberList,
                    setNumberList: setFalseNumberList,
                  }}
                />
              </Item>
              <Item
                name="earlyWarningValue"
                rowSpan={warnNumberList.length || 1}
                label={intl.get(`${modelPrompt}.model.earlyWarningValue`).d('预警值')}
                style={{ padding: 0 }}
              >
                <InputValueComponent
                  {...{
                    canEdit: canEdit && trueNumberList.length > 0,
                    numberDS: warnNumberDS,
                    numberList: warnNumberList,
                    setNumberList: setWarnNumberList,
                  }}
                />
              </Item>
            </>
          )}
          {dataType === 'CALCULATE_FORMULA' && (
            <>
              <Lov
                name="formulaLov"
                disabled={fetchRuleCodeDetailLoading}
                onChange={formulaLovChange}
              />
              <Select name="dimension" />
            </>
          )}
          {dataType === 'VALUE_LIST' && (
            <>
              <TextField name="valueLists" multiple onChange={handleChangeValueList} />
              <Select name="trueValue" multiple>
                {(valueLists || []).map(item => (
                  <Option value={item} key={item}>
                    {item}
                  </Option>
                ))}
              </Select>
              <Select name="falseValue" multiple>
                {(valueLists || []).map(item => (
                  <Option value={item} key={item}>
                    {item}
                  </Option>
                ))}
              </Select>
            </>
          )}
          {['TEXT', 'DECISION_VALUE'].includes(dataType) && (
            <>
              <TextField name="trueValue" />
              <TextField name="falseValue" />
            </>
          )}
          <NumberField name="dataQty" />
          <Lov name="samplingMethodLov" />
          <Lov name="ncCodeGroupLov" />
          <Lov name='ncCodeObj'/>
          <TextField name="employeePosition" />
          <Select name="inspectFrequencyObject" onChange={handleChangeFrequency} />
          <Item name="m" label={intl.get(`${modelPrompt}.label.frequencyParameter`).d('频率参数')}>
            <Row>
              <Col span={12} style={{ textAlign: 'left', lineHeight: 2.3 }}>
                <span>M=</span>
                <NumberField name="m" style={{ width: 'calc(90% - 16px)' }} />
              </Col>
              <Col span={12} style={{ textAlign: 'right', lineHeight: 2.3 }}>
                <span>N=</span>
                <NumberField name="n" style={{ width: 'calc(90% - 16px)' }} />
              </Col>
            </Row>
          </Item>
          <TextField name="actionItem" />
          <TextField name="sameGroupIdentification" />
          <Switch name="outsourceFlag" />
          <Switch name='inspectFlag' />
          <Switch name='spcReleaseFlag' />
          <Switch name='srmReleaseFlag' />
          <Switch name="destructiveExperimentFlag" />
          <TextField name="remark" />
          <TextField name='dataStorage' />
          <TextField name='controlMethod' />
          <Attachment {...attachmentProps} />
        </Form>,
      )}
      {/* {dataType === 'VALUE' && (
        <FormulaList
          canEdit={canEdit}
          tenantId={tenantId}
          customizeForm={customizeForm}
          formDs={editTableFormDS}
          tableDS={tableDS}
          statisticsTableUpdate={statisticsTableUpdate}
        />
      )} */}
    </>
  );
};

export default InspectItemInfoDrawer;
