import React, { useMemo, useRef, useState } from 'react';
import { Upload } from 'choerodon-ui/pro';
import { UploadProps } from 'choerodon-ui/pro/lib/upload/Upload';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { getAccessToken } from 'utils/utils';
import { getResponse } from '@utils/utils';
import './index.modules.less';

const accessToken = getAccessToken();

export interface ExcelUploadProps {
  url: string;
  path?: string;
  params?: object;
  disabled?: boolean;
  onSuccess?: (res) => void;
}

const ExcelUpload = (props: ExcelUploadProps) => {
  const { url, params, disabled = false, onSuccess }: ExcelUploadProps = props;
  const upload = useRef(null);
  const [loadingFlag, setLoadingFlag] = useState(false);

  const uploadProps = useMemo(() => {
    const _uploadProps: UploadProps = {
      headers: {
        contentType: 'multipart/form-data',
        Authorization: `bearer ${accessToken}`,
      },
      action: url,
      multiple: false,
      accept: ['.xlsx'],
      uploadImmediately: true,
      showUploadList: false,
      data: params,
      beforeUpload: () => {
        setLoadingFlag(true);
        return true;
      },
      onUploadSuccess: response => {
        let res: any;
        try {
          res = getResponse(JSON.parse(response));
        } catch (e) {
          throw new Error();
        } finally {
          setLoadingFlag(false);
          if (res.success && onSuccess) {
            onSuccess(res);
          }
        }
      },
      onUploadError: response => {
        notification.error({ message: response });
      },
    };
    return _uploadProps;
  }, [url, params, accessToken, onSuccess]);

  const handleBatchExport = () => {
    // @ts-ignore
    upload.current!.handleWrapperBtnClick();
  };

  return (
    <div className="msa-excel-upload">
      <Upload ref={upload} {...uploadProps} />
      <PermissionButton
        type="c7n-pro"
        className="batchExport"
        disabled={disabled}
        loading={loadingFlag}
        onClick={handleBatchExport}
      >
        {intl.get('tarzan.common.button.import').d('导入')}
      </PermissionButton>
    </div>
  );
};

export default ExcelUpload;
