.container {
  height: 100%;
  padding: 0 12px;
}
.chartLegends {
  position: fixed;
  top: 46px;
  right: ~'max(calc(50% - 120px), 110px)';
  span {
    margin: 8px;
  }
  .value {
    color: #4b94ff;
    font-weight: 700;
    font-size: 12px;
    font-family: D-DINExp-Bold, serif;
  }
  .amount {
    color: #f9c246;
    font-weight: 700;
    font-size: 12px;
    font-family: D-DINExp-Bold, serif;
  }
}

.customize-collapse {
  height: 100%;
  :global(.c7n-collapse-item) {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
  }
  :global(.c7n-collapse-content-box) {
    height: 100%;
  }
  :global(.c7n-collapse-header) {
    padding: 12px 0 4px 8px !important;
    &::before {
      top: calc(50% - 0.07rem + 4px) !important;
    }
  }
}
