.grid-container {
  display: grid;
  grid-template-rows: 50% 50%;
  grid-template-columns: 30% 40% 30%;
  height: 100%;
  .grid-item {
    display: grid;
    grid-template-rows: 50px auto 1fr;
    text-align: center;
    h1 {
      margin: auto;
      color: #333;
      font-weight: 500;
      font-size: 14px;
      font-family: PingFangSC-Medium, sans-serif;
    }
    .value {
      overflow: hidden;
      color: #4786fe;
      font-weight: 700;
      font-family: D-DINExp-Bold, sans-serif;
      line-height: 40px;
      text-overflow: ellipsis;
    }
  }
}

.container {
  height: 100%;
  padding: 0 12px;
}

.customize-collapse {
  height: 100%;
  :global(.c7n-collapse-item) {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
  }
  :global(.c7n-collapse-content-box) {
    height: 100%;
    padding-bottom: 0;
  }
  :global(.c7n-collapse-header) {
    padding: 12px 0 4px 8px !important;

    &::before {
      top: calc(50% - 0.07rem + 4px) !important;
    }
  }
}
