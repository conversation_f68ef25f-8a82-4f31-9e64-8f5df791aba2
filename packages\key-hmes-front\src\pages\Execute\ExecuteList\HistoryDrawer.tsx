/**
 * @Description: 物料批管理平台-列表页-历史抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-01-26 00:37:15
 * @LastEditTime: 2022-07-04 17:46:32
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';

export default ({ ds }) => {
  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'eventId',
        align: ColumnAlign.left,
        width: 150,
      },
      {
        name: 'eventTypeCode',
        width: 150,
      },
      {
        name: 'eventTypeDescription',
        width: 150,
      },
      {
        name: 'workcellName',
      },
      {
        name: 'eventRequestId',
        width: 150,
      },
      {
        name: 'requestTypeCode',
        width: 150,
      },
      {
        name: 'requestTypeDescription',
        width: 150,
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'realName',
        width: 150,
      },
      {
        name: 'qualityStatus',
        width: 120,
      },
      {
        name: 'eoNum',
        width: 150,
      },
      {
        name: 'status',
        width: 150,
      },
      {
        name: 'specifiedLevel',
      },
      {
        name: 'reworkStartFlag',
        width: 150,
      },
      {
        name: 'reworkWorkcell',
        width: 150,
      },
      {
        name: 'reworkOperationName',
        width: 150,
      },
      {
        name: 'reworkEndOperationName',
        width: 150,
      },
      {
        name: 'concesInterOperationName',
        width: 150,
      },
      {
        name: 'disposalFunctionCode',
        width: 150,
      },
      {
        name: 'degradeLevel',
      },
      {
        name: 'traceLevel',
      },
      {
        name: 'concessiveInterceptionOp',
        width: 150,
      },
    ],
    [],
  );

  return (
    <>
      <Table customizedCode="wlpglpt3" dataSet={ds} columns={columns} />
    </>
  );
};
