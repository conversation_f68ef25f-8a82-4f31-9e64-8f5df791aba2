/**
 * @Description: 产品审核方案-历史记录动态表格
 */

import React, {useMemo, useState} from 'react';
import { DataSet, Table, Spin} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock} from 'choerodon-ui/pro/lib/table/enum';
import {Badge, Collapse} from 'choerodon-ui';
import {useRequest} from "@components/tarzan-hooks";
import { GetLineInfo} from '../services';

import {taskDS} from "../stores/DetailDS";

const { Panel } = Collapse;

export interface ApprovalDrawerProps {
  type?: 'button' | 'text';
  objectTypeList: String[];
  objectId: string;
  disabled?: boolean;
  headDs: DataSet,
}

const TableContainer: React.FC<ApprovalDrawerProps> = ({
  headDs,
}) => {

  const [tableArr, setTableArr] = useState([]);
  const [tableDsArr, setTableDsArr] = useState([]);
  const [productReviewItemTypeArr, setProductReviewItemTypeArr] = useState([]);
  const { run: getLineInfo, loading: getLineInfoLoading } = useRequest(GetLineInfo(), {
    manual: true,
  });

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'productRevSchemeCode',
        lock: ColumnLock.left,
        width: 180,
      },
      {
        name: 'siteName',
        width: 130,
      },
      { name: 'productRevSchemeName', width: 180 },
      { name: 'productRevSchemeStatus', width: 180 },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'workcellCode', width: 150 },
      { name: 'workcellName', width: 150 },
      { name: 'productDate', width: 150 },
      { name: 'cusMaterialCode', width: 150 },
      { name: 'cusMaterialName', width: 150 },
      { name: 'lastUpdateDate', width: 150 },
    ];
  }, []);

  const queryLineTable = (id) => {
    setTableDsArr([]);
    setTableArr([]);
    setProductReviewItemTypeArr([])
    getLineInfo({
      params:{
        productRevSchemeHisId: id,
      },
      onSuccess: res => {
        const arr:any = [];
        const productReviewItemTypeList = [];
        const dsArr = [];
        res.forEach((item:any) => {
          arr.push({
            productReviewItemType: item.productReviewItemType,
            productReviewItemTypeDesc: item.productReviewItemTypeDesc,
          });
          // @ts-ignore
          productReviewItemTypeList.push(item.productReviewItemType)
          // @ts-ignore
          dsArr.push(new DataSet(taskDS()))
        })
        res.forEach((item, index) => {
          // @ts-ignore
          dsArr[index].loadData(item.dtlInfo)
        })
        setTableDsArr(dsArr);
        setTableArr(arr);
        setProductReviewItemTypeArr(productReviewItemTypeList)
      },
    });
  };

  const taskColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'productReviewItemCode',
        width: 160,
      },
      {
        name: 'productReviewItemDesc',
      },
      {
        name: 'productReviewMethod',
      },
      {
        name: 'specRequire',
      },
      {
        name: 'reviewFrequency',
      },
      {
        name: 'weightCoefficient',
        width: 160,
      },
      {
        name: 'remark',
      },
      {
        name: 'recordDataFlag',
        width: 160,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
      {
        name: 'fromOrtFlag',
        width: 160,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
    ];
  }, []);

  return (
    <Spin spinning={getLineInfoLoading}>
      <Table dataSet={headDs} columns={headColumns}
        onRow={({ record }) => ({
          onClick: () => queryLineTable(record?.get('productRevSchemeHisId')),
        })}
        searchCode="ProductReviewScheme2"
        customizedCode="ProductReviewScheme2-his"
      />
      <Collapse
        bordered={false}
        activeKey={[...productReviewItemTypeArr]}
      >
        {(tableArr || []).map((item, index) =>
          (
            // @ts-ignore
            <Panel key={item.productReviewItemType} header={item.productReviewItemTypeDesc}>
              <Table
                dataSet={tableDsArr[index]}
                columns={taskColumns}
              />
            </Panel>
          ),
        )}
      </Collapse>
    </Spin>
  );
};

export default TableContainer;
