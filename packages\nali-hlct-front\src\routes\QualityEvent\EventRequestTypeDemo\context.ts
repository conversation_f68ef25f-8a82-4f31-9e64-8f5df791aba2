import { createContext, ReactNode } from 'react';
import { ColumnProps } from "choerodon-ui/pro/lib/table/Column";
import { eventRequestTypeCus } from '@/hcmMesCus';

export interface ContextFcProps {
  /**
   * 如需要实现页面打印逻辑，可扩展该函数 - 按钮点击逻辑及数据获取，可实现为函数式组件
   * @returns 按钮组件
   */
  cusPagePrintButton?: () => ReactNode;
  /**
   * 如需要添加table的columns，可使用该方法
   * @returns 按钮组件
   */
  cusTableColumns?: ColumnProps[];
  /**
   * 返回列表页Table DataSet实例构造对象
   * @param dsProps 原dsProps对象
   * @returns 新ds Props对象构建方法
   */
  tableDsPropsBefHook?: (dsProps: any) => Function;
}

export const ContextFc: ContextFcProps = {
};

const context = createContext<ContextFcProps>({ ...ContextFc, ...eventRequestTypeCus });

export const ContextConsumer = context.Consumer;
