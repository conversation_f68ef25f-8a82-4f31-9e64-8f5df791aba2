import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Form, Lov, Table, TextField } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { Popconfirm } from 'choerodon-ui';
import uuid from 'uuid/v4';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import myInstance from '@utils/myAxios';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { useMemoizedFn } from 'ahooks';
import { downloadFile } from '@services/api';
import { API_HOST } from '@/utils/constants';
import ExcelUpload, { ExcelUploadProps } from './components/ExcelUpload';

import { formDS } from '../stores/uniformity';
import { analyseResultDS } from '../stores/DetailDS';
import UniformityGraphic from '../components/UniformityGraphic';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

interface GraphicDataProps {
  consistenceTableInfo: object;
  standardValueList: any;
  msaResult: string; // 分析结果
  msaConclusion: string; // 分析结论
  tableInfo: [];
}

// 默认数据
const apiDataDefault = [
  {
    measureTableList: [
      {
        measureDataColumn: 1,
        measureDataRow: 1,
        measureDataValue: '',
      },
    ],
  },
  // {
  //   measureTableList: [
  //     {
  //       measureDataColumn: 2,
  //       measureDataRow: 1,
  //       measureDataValue: '',
  //     },
  //   ],
  // },
];

const Uniformity = props => {
  const { msaStatus, currentUserFlag, checkLocation } = props;

  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
      }),
    [],
  );
  // 分析结果DS
  const analyseResultDs = useMemo(() => new DataSet(analyseResultDS()), []);
  const [graphicData, setGraphicData] = useState<GraphicDataProps>({
    consistenceTableInfo: {},
    standardValueList: [],
    msaResult: '', // 分析结果
    msaConclusion: '', // 分析结论
    tableInfo: [],
  });

  const templateData = [
    {
      measureDataRow: 1,
      measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
      uuid: uuid(),
      type: 'measureDataValue',
    },
  ];

  const [apiData, setApiData] = useState([]);
  const [standardValueApiData, setStandardValueApiData]: any = useState([]);
  const [defaultData, setDefaultData] = useState(templateData); // 有默认数据时初始化
  const [havaSaveData, setHaveSaveData] = useState(false);
  const [originDefaultField, setOriginDefaultField] = useState([]);

  const [defaultField, setDefaultField] = useState([
    {
      name: 'measureDataRow',
      type: FieldType.string,
    },
    {
      name: 'measureDataColumnUser1',
      required: true,
      type: FieldType.object,
      lovCode: 'YP.QIS.UNIT_USER',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'measuredBy1',
      bind: 'measureDataColumnUser1.id',
    },
    {
      name: 'measuredByName1',
      bind: 'measureDataColumnUser1.realName',
    },
    {
      name: 'measureDataColumnUser1-1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.permissionType`).d('权限类型'),
      lookupCode: 'YP.QIS.MSA_CONSISTENCY_VALUE',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    // {
    //   name: 'measureDataColumnUser1-2',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.permissionType`).d('权限类型'),
    //   lookupCode: 'YP.QIS.MSA_CONSISTENCY_VALUE',
    //   required: true,
    //   lovPara: {
    //     tenantId,
    //   },
    // },
    {
      name: 'standardValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardValue`).d('基准值'),
      lookupCode: 'YP.QIS.MSA_CONSISTENCY_VALUE',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'codeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.codeFlag`).d('代码'),
    },
  ]);

  const userDs = useMemo(
    () =>
      new DataSet({
        forceValidate: true,
        autoCreate: false,
        selection: false,
        paging: false,
        primaryKey: 'uuid',
        data: defaultData,
        fields: defaultField,
      }),
    [],
  );

  const [currentColumns, setCurrentColumns] = useState([
    {
      name: 'measureDataRow',
      header: () => (
        <div className={styles['cell-uniformity']}>
          <div>
            <span>{intl.get(`${modelPrompt}.measuredBy`).d('测量人')}</span>
            <PermissionButton
              type="c7n-pro"
              icon="add"
              onClick={addColumn}
              funcType="flat"
              shape="circle"
              size="small"
            />
          </div>
          <div>
            <span>{intl.get(`${modelPrompt}.partNum`).d('零件编号')}</span>
            <PermissionButton
              type="c7n-pro"
              icon="add"
              onClick={addMeasuredValue}
              funcType="flat"
              shape="circle"
              size="small"
            />
          </div>
        </div>
      ),
      width: 150,
      lock: ColumnLock.left,
      align: ColumnAlign.center,
      renderer: ({ value, dataSet, record }) => {
        if (value) {
          return (
            <>
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => dataSet?.remove(record)}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <PermissionButton
                  type="c7n-pro"
                  disabled={Number(value) === 1} // 测量值最少需要一行数据
                  icon="remove"
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </Popconfirm>
              <span>{value}</span>
            </>
          );
        }
      },
    },
    {
      name: 'measureDataColumnUser1',
      editor: false,
      align: ColumnAlign.center,
      header: () => (
        <>
          <Lov
            name="measureDataColumnUser1"
            dataSet={userDs}
            onChange={val => changeUser(val, 1)}
          />
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteColumn('measureDataColumnUser1')}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              funcType="flat"
              shape="circle"
              size="small"
            />
          </Popconfirm>
        </>
      ),
      children: [
        {
          name: 'measureDataColumnUser1-1',
          editor: true,
          align: ColumnAlign.center,
          header: () => <>1</>,
        },
        // {
        //   name: 'measureDataColumnUser1-2',
        //   editor: true,
        //   align: ColumnAlign.center,
        //   header: () => <>2</>,
        // },
      ],
    },
    {
      name: 'standardValue',
      editor: false,
      align: ColumnAlign.center,
      header: () => <>{intl.get(`${modelPrompt}.standard`).d('基准')}</>,
      children: [
        {
          name: 'standardValue',
          editor: true,
          align: ColumnAlign.center,
          header: () => <></>,
        },
      ],
    },
    {
      name: 'codeFlag',
      editor: false,
      align: ColumnAlign.center,
      header: () => <>{intl.get(`${modelPrompt}.codeFlag`).d('代码')}</>,
      children: [
        {
          name: 'codeFlag',
          editor: true,
          align: ColumnAlign.center,
          header: () => <></>,
          renderer: ({ value }) => {
            return <span style={{ color: value === 'X' ? 'red' : 'null' }}>{value}</span>;
          },
        },
      ],
    },
  ]); // 实际columns

  useEffect(() => {
    myInstance
      .get(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/info/ui?msaTaskLineId=${props.msaTaskLineId}`,
      )
      .then(res => {
        if (res.data.success) {
          // 处理数据
          formDs.current?.set('checkLocation', checkLocation);
          const { sampleDescription } = res.data.rows;
          if (sampleDescription) formDs.current?.set('sampleDescription', sampleDescription);
          if ((res.data.rows.tableInfo || []).length) {
            setHaveSaveData(true);
            setApiData(res.data.rows.tableInfo);
            setStandardValueApiData(res.data.rows.standardValueList);
          } else {
            setHaveSaveData(false);
            setApiData(apiDataDefault);
            setStandardValueApiData([
              {
                codeFlag: '',
                standardValue: '',
                measureDataRow: 1,
              },
            ]);
          }
          handleUpdateChartData(res.data.rows || {});
        } else {
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          });
        }
      });
  }, []);

  const handleUpdateChartData = dataSource => {
    const {
      tableInfo = [],
      consistenceTableInfo = {},
      standardValueList = [],
      msaResult = '',
      msaConclusion = '',
    } = dataSource;
    setGraphicData({
      tableInfo,
      consistenceTableInfo,
      standardValueList,
      msaResult, // 分析结果
      msaConclusion, // 分析结论
    });
    analyseResultDs.loadData([{
      msaResult,
      msaConclusion,
    }])
  };

  const uniqueFunc = (arr, uniId) => {
    const res = new Map();
    return arr.filter(item => !res.has(item[uniId]) && res.set(item[uniId], 1));
  };

  useEffect(() => {
    if (apiData.length) {
      const measureByObjList = uniqueFunc(apiData, 'measuredBy');

      // 一组人下有几列
      const count = apiData.length / measureByObjList.length;

      let breakArr: any = [];
      apiData.forEach((item: any) => {
        breakArr = [...breakArr, ...item.measureTableList];
      });

      const transformDataResult = [...standardValueApiData];

      transformDataResult.forEach((row: any) => {
        row.uuid = uuid();
        row.type = 'measureDataValue';
        measureByObjList.forEach((user, index) => {
          row[`measuredBy${index + 1}`] = user.measuredBy;
          row[`measuredByName${index + 1}`] = user.measuredByName;

          // 找出一个测量人的数据
          const userArr = [];

          breakArr.forEach((item: any) => {
            if (item.measureDataRow === row.measureDataRow && user.measuredBy === item.measuredBy) {
              userArr.push(item);
            }
          });
          userArr.forEach((item: any, userArrIndex) => {
            row[`measureDataColumnUser${index + 1}-${userArrIndex + 1}`] = item.measureDataValue;
          });
        });
      });
      console.log();

      let feild = [
        {
          name: 'measureDataRow',
          type: FieldType.string,
        },
      ];
      measureByObjList.map((userObj, index) => {
        const userName = `measureDataColumnUser${index + 1}`;
        feild = [
          ...feild,
          {
            name: userName,
            required: true,
            type: FieldType.object,
            lovCode: 'YP.QIS.UNIT_USER',
            ignore: FieldIgnore.always,
            lovPara: { tenantId },
            textField: 'realName',
          },
          {
            name: `measuredBy${index + 1}`,
            bind: `${userName}.id`,
          },
          {
            name: `measuredByName${index + 1}`,
            bind: `${userName}.realName`,
          },
        ];
        for (let i = 0; i < count; i++) {
          feild.push({
            // name: 'measureDataColumnUser1-2',
            name: `measureDataColumnUser${index + 1}-${i + 1}`,
            type: FieldType.string,
            label: intl.get(`${modelPrompt}.permissionType`).d('权限类型'),
            lookupCode: 'YP.QIS.MSA_CONSISTENCY_VALUE',
            required: true,
            lovPara: {
              tenantId,
            },
          });
        }
      });
      feild.push({
        name: 'standardValue',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.standardValue`).d('基准值'),
        lookupCode: 'YP.QIS.MSA_CONSISTENCY_VALUE',
        required: true,
        lovPara: {
          tenantId,
        },
      });

      feild.push({
        name: 'codeFlag',
        label: intl.get(`${modelPrompt}.codeFlag`).d('代码'),
      });
      const column = [
        {
          name: 'measureDataRow',
          header: () => (
            <div className={styles['cell-uniformity']}>
              <div>
                <span>{intl.get(`${modelPrompt}.measuredBy`).d('测量人')}</span>
                <PermissionButton
                  type="c7n-pro"
                  icon="add"
                  disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
                  onClick={addColumn}
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </div>
              <div>
                <span>{intl.get(`${modelPrompt}.partNum`).d('零件编号')}</span>
                <PermissionButton
                  type="c7n-pro"
                  icon="add"
                  disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
                  onClick={addMeasuredValue}
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </div>
            </div>
          ),
          width: 150,
          lock: ColumnLock.left,
          align: ColumnAlign.center,
          renderer: ({ value, dataSet, record }) => {
            if (value) {
              return (
                <>
                  <Popconfirm
                    title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                    onConfirm={() => dataSet?.remove(record)}
                    okText={intl.get('tarzan.common.button.confirm').d('确认')}
                    cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                  >
                    <PermissionButton
                      type="c7n-pro"
                      disabled={Number(value) === 1 || Number(value) <= transformDataResult.length} // 测量值最少需要一行数据
                      icon="remove"
                      funcType="flat"
                      shape="circle"
                      size="small"
                    />
                  </Popconfirm>
                  <span>{value}</span>
                </>
              );
            }
          },
        },
      ];
      measureByObjList.map((userObj, index) => {
        const children: any = [];
        for (let i = 0; i < count; i++) {
          children.push({
            name: `measureDataColumnUser${index + 1}-${i + 1}`,
            editor: () => {
              return !['IMPROVING', 'COMPLETED'].includes(msaStatus) && currentUserFlag;
            },
            align: ColumnAlign.center,
            header: () => <>{i + 1}</>,
          });
        }

        column.push({
          name: `measureDataColumnUser${index + 1}`,
          editor: () => {
            return !['IMPROVING', 'COMPLETED'].includes(msaStatus) && currentUserFlag;
          },
          align: ColumnAlign.center,
          header: () => (
            <>
              <Lov
                name={`measureDataColumnUser${index + 1}`}
                dataSet={userDs}
                disabled={havaSaveData || ['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
                onChange={val => changeUser(val, index + 1)}
              />
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => deleteColumn(`measureDataColumnUser${index + 1}`)}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <PermissionButton
                  disabled={havaSaveData || Number(index) === 0}
                  type="c7n-pro"
                  icon="remove"
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </Popconfirm>
            </>
          ),
          children,
        });
      });
      column.push({
        name: 'standardValue',
        editor: false,
        align: ColumnAlign.center,
        header: () => <>{intl.get(`${modelPrompt}.standard`).d('基准')}</>,
        children: [
          {
            name: 'standardValue',
            editor: () => {
              return !['IMPROVING', 'COMPLETED'].includes(msaStatus) && currentUserFlag;
            },
            align: ColumnAlign.center,
            header: () => <></>,
          },
        ],
      });
      column.push({
        name: 'codeFlag',
        editor: false,
        header: () => <>{intl.get(`${modelPrompt}.codeFlag`).d('代码')}</>,
        children: [
          {
            name: 'codeFlag',
            editor: false,
            align: ColumnAlign.center,
            header: () => <></>,
            renderer: ({ value }) => {
              return <span style={{ color: value === 'X' ? 'red' : 'null' }}>{value}</span>;
            },
          },
        ],
      });
      setCurrentColumns(column);
      feild.forEach((item: any) => {
        userDs.addField(item.name, {
          ...item,
        });
      });
      setDefaultField(feild);
      setOriginDefaultField(feild);
      setDefaultData(transformDataResult);
      userDs.loadData(transformDataResult);
    }
  }, [apiData, standardValueApiData, havaSaveData, msaStatus, currentUserFlag]);

  const changeUser = (val, keyIndex) => {
    const data = userDs.toData();
    data.forEach(item => {
      item[`measuredBy${keyIndex}`] = val.id;
      item[`measuredByName${keyIndex}`] = val.realName;
    });
    userDs.loadData(data);
  };

  const deleteColumn = name => {
    setCurrentColumns(prevColumns => prevColumns.filter(item => item.name !== name));
  };

  const addColumn = useMemoizedFn(async () => {
    const names = defaultField.map(item => item.name);
    const UserNumberArr = names.filter(
      word => word.indexOf('-') === -1 && word.indexOf('measureDataColumnUser') !== -1,
    );
    const maxUserNumber = Number(
      UserNumberArr[UserNumberArr.length - 1].replace('measureDataColumnUser', ''),
    );
    const maxOneUserNumber = names.filter(word => word.indexOf(`measureDataColumnUser1-`) !== -1)
      .length;

    const childrenFields: any = [];
    for (let i = 0; i < maxOneUserNumber; i++) {
      childrenFields.push({
        name: `measureDataColumnUser${maxUserNumber + 1}-${i + 1}`,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.permissionType`).d('权限类型'),
        lookupCode: 'YP.QIS.MSA_CONSISTENCY_VALUE',
        required: true,
        lovPara: {
          tenantId,
        },
      });
    }

    const newFields = [
      {
        name: `measureDataColumnUser${maxUserNumber + 1}`,
        required: true,
        type: FieldType.object,
        lovCode: 'YP.QIS.UNIT_USER',
        ignore: FieldIgnore.always,
        lovPara: { tenantId },
        textField: 'realName',
      },
      {
        name: `measuredBy${maxUserNumber + 1}`,
        bind: `measureDataColumnUser${maxUserNumber + 1}.id`,
      },
      {
        name: `measuredByName${maxUserNumber + 1}`,
        bind: `measureDataColumnUser${maxUserNumber + 1}.realName`,
      },
      ...childrenFields,
    ];

    newFields.forEach((item: any) => {
      userDs.addField(item.name, {
        ...item,
      });
    });

    setDefaultField([...defaultField, ...newFields]);

    const children: any = [];
    for (let i = 0; i < maxOneUserNumber; i++) {
      children.push({
        name: `measureDataColumnUser${maxUserNumber + 1}-${i + 1}`,
        editor: true,
        align: ColumnAlign.center,
        header: () => <>{i + 1}</>,
      });
    }
    const newColumnItem = {
      name: `measureDataColumnUser${maxUserNumber + 1}`,
      editor: false,
      align: ColumnAlign.center,
      header: () => (
        <>
          <Lov
            name={`measureDataColumnUser${maxUserNumber + 1}`}
            dataSet={userDs}
            disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
            onChange={val => changeUser(val, maxUserNumber + 1)}
          />
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteColumn(`measureDataColumnUser${maxUserNumber + 1}`)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              funcType="flat"
              shape="circle"
              size="small"
            />
          </Popconfirm>
        </>
      ),
      children,
    };

    const newColumns = [...currentColumns];
    newColumns.splice(newColumns.length - 2, 0, {
      ...newColumnItem,
    });
    setCurrentColumns([...newColumns]);
  });

  const addMeasuredValue = useMemoizedFn(() => {
    const data = userDs.toData();
    let max = 0;
    data.forEach((item: any) => {
      if (Number(item.measureDataRow) > max) {
        max = Number(item.measureDataRow);
      }
    });
    const templateData = userDs.current.toData();
    userDs.create({
      type: 'measureDataValue',
      ...templateData,
      uuid: uuid(),
      measureDataRow: max + 1,
    });
  });

  // 删除测量次数
  const deleteMeasureCount = async () => {
    const orginOneUserNumber = originDefaultField
      .map(item => item.name)
      .filter(word => word.indexOf(`measureDataColumnUser1-`) !== -1).length;

    const names = defaultField.map(item => item.name);
    const UserNumberArr = names.filter(
      word => word.indexOf('-') === -1 && word.indexOf('measureDataColumnUser') !== -1,
    );
    const maxUserNumber = Number(
      UserNumberArr[UserNumberArr.length - 1].replace('measureDataColumnUser', ''),
    );
    const maxOneUserNumber = names.filter(word => word.indexOf(`measureDataColumnUser1-`) !== -1)
      .length;
    // 默认数量不可删除
    if (orginOneUserNumber === 1 && maxOneUserNumber === 1) {
      notification.error({
        message: intl.get(`${modelPrompt}.atLeastOneCountForPerson`).d('一个测量人最少有一个测量次数'),
      });
      return false;
    }
    if (orginOneUserNumber >= maxOneUserNumber) {
      notification.error({
        message: intl.get(`${modelPrompt}.deleteSavedData`).d('已保存的数据不允许删除'),
      });
      return false;
    }

    const newDefaultField = [];

    const deleteItem = [];
    for (let i = 0; i < maxUserNumber; i++) {
      deleteItem.push(`measureDataColumnUser${i + 1}-${maxOneUserNumber}`);
      userDs.getField(`measureDataColumnUser${i + 1}-${maxOneUserNumber}`)?.set('required', false);
      userDs.getField(`measureDataColumnUser${i + 1}-${maxOneUserNumber}`)?.set('ignore', 'always');
    }
    defaultField.forEach(item => {
      if (!deleteItem.includes(item.name)) {
        newDefaultField.push(item);
      }
    });
    setDefaultField(newDefaultField);

    const newColumns = [...currentColumns];
    newColumns.forEach(item => {
      if (item.name.includes('measureDataColumnUser')) {
        item.children.pop();
      }
    });
    setCurrentColumns(newColumns);
  };

  // 添加测量次数
  const addMeasureCount = () => {
    const names = defaultField.map(item => item.name);
    const UserNumberArr = names.filter(
      word => word.indexOf('-') === -1 && word.indexOf('measureDataColumnUser') !== -1,
    );
    const maxUserNumber = Number(
      UserNumberArr[UserNumberArr.length - 1].replace('measureDataColumnUser', ''),
    );
    const maxOneUserNumber = names.filter(word => word.indexOf(`measureDataColumnUser1-`) !== -1)
      .length;

    const newDefaultField = [...defaultField];
    for (let i = 0; i < maxUserNumber; i++) {
      userDs.addField(`measureDataColumnUser${i + 1}-${maxOneUserNumber + 1}`, {
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.permissionType`).d('权限类型'),
        lookupCode: 'YP.QIS.MSA_CONSISTENCY_VALUE',
        required: true,
        lovPara: {
          tenantId,
        },
      });
      newDefaultField.push({
        name: `measureDataColumnUser${i + 1}-${maxOneUserNumber + 1}`,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.permissionType`).d('权限类型'),
        lookupCode: 'YP.QIS.MSA_CONSISTENCY_VALUE',
        required: true,
        lovPara: {
          tenantId,
        },
      });
    }
    setDefaultField(newDefaultField);

    const newColumns = [...currentColumns];
    newColumns.forEach(item => {
      if (item.name.includes('measureDataColumnUser')) {
        item.children.push({
          name: `${item.name}-${maxOneUserNumber + 1}`,
          editor: true,
          align: ColumnAlign.center,
          header: () => <>{maxOneUserNumber + 1}</>,
        });
      }
    });
    setCurrentColumns(newColumns);
  };

  const handleSave = async () => {
    // @ts-ignore
    const validateResult = await userDs.validate();
    if (!validateResult) {
      return;
    }

    const originData = userDs.toData();

    let columsKeyArr = [];
    let userArr = [];
    const standardValueList: any = []; // 基准值列表
    originData.forEach((item: any, index) => {
      standardValueList.push({
        measureDataRow: item.measureDataRow,
        standardValue: item.standardValue,
      });
      if (index === 0) {
        columsKeyArr = Object.keys(item).filter(word => word.indexOf('-') !== -1);
        userArr = Object.keys(item).filter(
          word => word.indexOf('-') === -1 && word.indexOf('measureDataColumnUser') !== -1,
        );
      }
    });

    // 计算出去基准值之外，存在多少列数据。

    const resultData: Array<any> = []; // 最终的数据
    userArr.forEach(userkey => {
      columsKeyArr.forEach((columsKey, index) => {
        const measureTableList: Array<any> = [];
        let measuredBy = '';
        let measuredByName = '';
        originData.forEach((item: any) => {
          if (columsKey.includes(userkey)) {
            // debugger
            measureTableList.push({
              measureDataRow: item.measureDataRow,
              // measureDataColumn: index + 1,
              measureDataColumn: columsKey.split('-')[1],
              measureDataValue: item[columsKey],
              measuredBy: item[`measuredBy${userkey.split('User')[1]}`], // 方便调试
              measuredByName: item[`measuredByName${userkey.split('User')[1]}`],
            });
            if (!measuredBy) {
              measuredBy = item[`measuredBy${userkey.split('User')[1]}`];
              measuredByName = item[`measuredByName${userkey.split('User')[1]}`];
            }
          }
        });
        if (measuredBy) {
          resultData.push({
            measureTableList,
            measuredBy, // 接口需要外层的人员信息
            measuredByName,
          });
        }
      });
    });

    const formData = formDs.toData();
    myInstance
      .post(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/save/ui`, {
        msaTaskLineId: props.msaTaskLineId,
        ...formData[0],
        standardValueList,
        tableInfo: resultData,
      })
      .then(res => {
        if (res.data.success) {
          setApiData(res.data.rows.tableInfo);
          setHaveSaveData(true);
          setStandardValueApiData(res.data.rows.standardValueList);
          props.updateHeaderInfo(); // 更新头部
          handleUpdateChartData(res.data.rows || {});
          // 处理数据
          notification.success({});
        } else {
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          });
        }
      });
  };

  // 模板下载
  const downloadTemp = async () => {
    await downloadFile({
      requestUrl: `/himp/v1/${tenantId}/template/YP.QIS_MSA_IMPORT_CONSISTENCY/excel`,
      queryParams: [{ name: 'tenantId', value: tenantId }],
      method: 'GET',
    });
  };

  const handleUploadSuccess = res => {
    setApiData(res.rows.tableInfo);
    setHaveSaveData(false); // 导入可修改
    setStandardValueApiData(res.rows.standardValueList);
    notification.success({
      message: intl.get(`${modelPrompt}.notification.importSuccess`).d('导入成功'),
    });
  };

  const excelUploadProps: ExcelUploadProps = {
    url: `${API_HOST}${
      BASIC.TARZAN_SAMPLING
    }/v1/${tenantId}/qis-msa-analysis/import/ui?msaTaskLineId=${
      props.msaTaskLineId
    }&sampleDescription=${formDs.current?.get('sampleDescription')}`,
    params: {},
    onSuccess: res => handleUploadSuccess(res),
  };

  return (
    <div>
      <Button
        color={ButtonColor.primary}
        onClick={handleSave}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        {intl.get(`${modelPrompt}.button.save`).d('保存')}
      </Button>
      <Button onClick={downloadTemp} icon="get_app">
        {intl.get(`${modelPrompt}.templateDownload`).d('模板下载')}
      </Button>
      <ExcelUpload
        {...excelUploadProps}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      />
      <Form
        dataSet={formDs}
        columns={3}
        style={{ marginTop: 10 }}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        <TextField name="checkLocation" />
        <TextField name="sampleDescription" />
      </Form>
      <Table
        buttons={[
          <>
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={deleteMeasureCount}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <Button
                funcType={FuncType.flat}
                color={ButtonColor.blue}
                disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
              >
                {intl.get(`${modelPrompt}.button.deleteMeasureCount`).d('删除测量次数')}
              </Button>
            </Popconfirm>
            <Button
              funcType={FuncType.flat}
              color={ButtonColor.blue}
              onClick={addMeasureCount}
              disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
            >
              {intl.get(`${modelPrompt}.button.addMeasureCount`).d('添加测量次数')}
            </Button>
          </>,
        ]}
        columnDraggable
        columnTitleEditable
        aggregation={false}
        border
        dataSet={userDs}
        columns={currentColumns as any}
      />
      <UniformityGraphic dataSoure={graphicData} analyseResultDs={analyseResultDs} />
    </div>
  );
};

export default Uniformity;
