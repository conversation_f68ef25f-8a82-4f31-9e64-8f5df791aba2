/**
 * @Description: 检验项目维护-详情页DS
 * @Author: <EMAIL>
 * @Date: 2023/1/9 16:37
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { isJSONString } from '@/utils';

const modelPrompt = 'tarzan.initialManagementActivity';
const tenantId = getCurrentOrganizationId();

const detailDS: (requiredField, validateType) => DataSetProps = (requiredField, validateType) => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    // 基础信息
    {
      name: 'inspectItemId',
      type: FieldType.number,
    },
    {
      name: 'inspectItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemCode`).d('检验项目编码'),
      maxLength: 255,
      dynamicProps: {
        disabled: ({ record }) => record.get('inspectItemId'),
      },
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.inspectItemDesc`).d('检验项目描述'),
      required: true,
      maxLength: 255,
      dynamicProps: {
        disabled: () => validateType === 'InspectionPlatform',
      },
    },
    {
      name: 'inspectItemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemType`).d('检验项目类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSPECT_ITEM_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'subItemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subItemType`).d('检验项目次级类型'),
      lookupCode: 'YP.QIS.SUB_ITEM_TYPE',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ record }) => !record.get('inspectItemType'),
      },
    },
    {
      name: 'featureType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.featureType`).d('特性类型'),
      lookupCode: 'YP.QIS.ITEM_CHARACTER',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) => (dataSet.getState('required')),
      },
    },
    {
      name: 'itemPattern',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemPattern`).d('项目分类'),
      lookupCode: 'YP.QIS.ITEM_PATTERN',
      defaultValue: 'STANDARD',
      disabled:true,
    },
    {
      name: 'inspectBasis',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBasis`).d('检验依据'),
      maxLength: 255,
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
      lookupCode: 'MT.QMS.QUALITY_CHARACTERISTIC_TYPE',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'inspectTool',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTool`).d('检验工具'),
      lookupCode: 'MT.QMS.INSPECT_TOOL',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'inspectMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('检验方法'),
      lookupCode: 'MT.QMS.INSPECT_METHOD',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'requiredFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requiredFlag`).d('必填项目'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      dynamicProps: {
        disabled: () => validateType === 'InspectionPlatform',
      },
    },
    {
      name: 'technicalRequirement',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.technicalRequirement`).d('技术要求'),
      maxLength: 1000,
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosureList`).d('附件'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      maxLength: 1000,
    },
    {
      name: 'dataStorage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataStorage`).d('数据存储'),
      // maxLength: 1000,
    },
    {
      name: 'controlMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlMethod`).d('控制方法'),
    },

    // 数据信息
    {
      name: 'enterMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterMethod`).d('录入方式'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_COLLECTION_METHOD`,
      defaultValue: 'MANUAL_COLLECTION',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        required: () => {
          return requiredField.indexOf('enterMethod') > -1;
        },
        disabled: ({ record }) => record.get('dataType') === 'CALCULATE_FORMULA',
      },
    },
    {
      name: 'dataType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataType`).d('数据类型'),
      lookupCode: 'MT.QMS.INSPECT_ITEM_DATA_TYPE',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      required: true,
    },
    {
      name: 'uomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'uomName',
      dynamicProps: {
        disabled: ({ record }) => !['CALCULATE_FORMULA', 'VALUE'].includes(record.get('dataType')),
      },
    },
    {
      name: 'uomId',
      bind: 'uomLov.uomId',
    },
    {
      name: 'uomName',
      bind: 'uomLov.uomName',
    },

    {
      name: 'formulaLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.formula`).d('计算公式'),
      lovCode: 'HRES.RULE.LIST',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'ruleName',
      valueField: 'ruleId',
      dynamicProps: {
        disabled: ({ record }) => record.get('dataType') !== 'CALCULATE_FORMULA',
        required: ({ record }) => record.get('dataType') === 'CALCULATE_FORMULA',
      },
    },
    {
      name: 'formulaId',
      bind: 'formulaLov.ruleId',
    },
    {
      name: 'formulaCode',
      bind: 'formulaLov.ruleCode',
    },
    {
      name: 'formulaName',
      bind: 'formulaLov.ruleName',
    },

    {
      name: 'formulaList',
    },

    {
      name: 'dimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.formulaDimension`).d('计算维度'),
      lookupCode: 'MT.QMS.CALCULATE_DIMENSION',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ record }) => record.get('dataType') !== 'CALCULATE_FORMULA',
        required: ({ record }) => record.get('dataType') === 'CALCULATE_FORMULA',
      },
    },

    {
      name: 'decimalNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.decimalNumber`).d('小数位数'),
      min: 0,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => !['CALCULATE_FORMULA', 'VALUE'].includes(record.get('dataType')),
      },
    },
    {
      name: 'processMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processMode`).d('尾数处理'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=DECIMAL_PROCESS_MODE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => !['CALCULATE_FORMULA', 'VALUE'].includes(record.get('dataType')),
      },
    },
    {
      name: 'valueLists',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
      multiple: true,
      dynamicProps: {
        disabled: ({ record }) => record.get('dataType') !== 'VALUE_LIST',
        required: ({ record }) => {
          const dataType = record.get('dataType');
          if (dataType === 'VALUE_LIST') {
            if (validateType === 'InspectionScheme') {
              return true;
            }
            return false;
          }
          return false;
        },
      },
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
      dynamicProps: {
        disabled: ({ record }) => {
          switch (record.get('dataType')) {
            case 'VALUE_LIST':
              return (
                !record.get('valueLists') ||
                !record.get('valueLists').length ||
                (!['InspectionPlatform', 'InspectionPlatformCreateItem'].includes(validateType) &&
                  record.get('falseValue'))
              );
            case 'TEXT':
              return record.get('falseValue');
            case 'DECISION_VALUE':
              return false;
            default:
              return true;
          }
        },
        required: ({ record }) => {
          const dataType = record.get('dataType');
          if (dataType === 'DECISION_VALUE') {
            if (
              ['InspectionScheme', 'InspectionPlatform', 'InspectionPlatformCreateItem'].includes(
                validateType,
              )
            ) {
              return true;
            }
            return record.get('falseValue');
          }
          if (dataType === 'VALUE_LIST') {
            if (validateType === 'InspectionScheme') {
              if (!record.get('falseValue') && !record.get('trueValue')) {
                return true;
              }
              return !record.get('falseValue');
            }
            if (['InspectionPlatform', 'InspectionPlatformCreateItem'].includes(validateType)) {
              return true;
            }
            return false;
          }
          return false;
        },
      },
    },
    { name: 'trueValueList' },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
      dynamicProps: {
        disabled: ({ record }) => {
          switch (record.get('dataType')) {
            case 'VALUE_LIST':
              return (
                !record.get('valueLists') ||
                !record.get('valueLists').length ||
                (!['InspectionPlatform', 'InspectionPlatformCreateItem'].includes(validateType) &&
                  record.get('trueValue'))
              );
            case 'TEXT':
              return record.get('trueValue');
            case 'DECISION_VALUE':
              return false;
            default:
              return true;
          }
        },
        required: ({ record }) => {
          const dataType = record.get('dataType');

          if (dataType === 'DECISION_VALUE') {
            if (
              ['InspectionScheme', 'InspectionPlatform', 'InspectionPlatformCreateItem'].includes(
                validateType,
              )
            ) {
              return true;
            }
            return record.get('trueValue');
          }
          if (dataType === 'VALUE_LIST') {
            if (validateType === 'InspectionScheme') {
              if (!record.get('falseValue') && !record.get('trueValue')) {
                return true;
              }
              return !record.get('trueValue');
            }
            if (['InspectionPlatform', 'InspectionPlatformCreateItem'].includes(validateType)) {
              return true;
            }
            return false;
          }
          return false;
        },
      },
    },
    { name: 'falseValueList' },
    {
      name: 'warningValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warningValue`).d('预警值'),
      dynamicProps: {
        disabled: ({ record }) =>
          !['CALCULATE_FORMULA', 'VALUE'].includes(record.get('dataType')) ||
          (record.get('dataType') === 'VALUE' && !record.get('trueValue')),
      },
    },
    { name: 'warningValueList' },

    // 其他信息
    {
      name: 'dataQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.dataQty`).d('记录值个数'),
      min: 1,
      step: 1,
      defaultValue: 1,
      dynamicProps: {
        required: () => {
          return requiredField.indexOf('dataQty') > -1;
        },
      },
    },
    {
      name: 'samplingMethodLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
      lovCode: 'MT.QMS.SAMPLING_METHOD',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => ['CALCULATE_FORMULA'].includes(record.get('dataType')),
        required: ({ record }) => {
          return (
            !['CALCULATE_FORMULA'].includes(record.get('dataType')) &&
            requiredField.indexOf('samplingMethodLov') > -1
          );
        },
      },
    },
    {
      name: 'samplingMethodId',
      bind: 'samplingMethodLov.samplingMethodId',
    },
    {
      name: 'samplingMethodDesc',
      bind: 'samplingMethodLov.samplingMethodDesc',
    },
    {
      name: 'ncCodeGroupLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeGroup`).d('不良代码组'),
      lovCode: 'MT.NC_GROUP',
      textField: 'description',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps:{
        disabled: ({ dataSet }) => (!dataSet.getState('inspectBusinessType')),
        required:({dataSet})=>(dataSet.getState('inspectBusinessType')),
      },
    },
    {
      name: 'ncCodeGroupId',
      bind: 'ncCodeGroupLov.ncGroupId',
    },
    {
      name: 'ncCodeGroupDesc',
      bind: 'ncCodeGroupLov.description',
    },
    {
      name: 'ncCodeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: FieldIgnore.always,
      dynamicProps:{
        disabled: ({ record, dataSet }) => (!record.get('ncCodeGroupLov') || !dataSet.getState('inspectBusinessType')),
        required: ({ dataSet }) => (dataSet.getState('inspectBusinessType')),
        lovPara: ({ record }) => ({
          tenantId,
          ncGroupIds: record?.get('ncCodeGroupId'),
        }),
      },
    },
    {
      name: 'ncCode',
      bind: 'ncCodeObj.ncCode',
    },
    {
      name: 'ncCodeId',
      bind: 'ncCodeObj.ncCodeId',
    },
    {
      name: 'employeePosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.employeePosition`).d('检测人员岗位'),
      dynamicProps: {
        disabled: ({ record }) => ['CALCULATE_FORMULA'].includes(record.get('dataType')),
      },
    },
    {
      name: 'inspectFrequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectFrequency`).d('检测频率'),
      lookupCode: 'MT.QMS.INSPECT_FREQUENCY',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ record }) => ['CALCULATE_FORMULA'].includes(record.get('dataType')),
      },
    },
    {
      name: 'frequencyParams',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.frequencyParams`).d('频率参数'),
      dynamicProps: {
        disabled: ({ record }) => ['CALCULATE_FORMULA'].includes(record.get('dataType')),
      },
    },
    {
      name: 'm',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.frequencyParams.m`).d('M='),
      min: 1,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => !record.get('inspectFrequency'),
        required: ({ record }) => record.get('inspectFrequency'),
      },
    },
    {
      name: 'n',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.frequencyParams.n`).d('N='),
      min: 1,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => !record.get('inspectFrequency'),
        required: ({ record }) => record.get('inspectFrequency'),
      },
    },
    {
      name: 'actionItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actionItem`).d('行动项'),
    },
    {
      name: 'sameGroupIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sameGroupIdentification`).d('同组标识'),
      dynamicProps: {
        disabled: ({ record }) => ['CALCULATE_FORMULA'].includes(record.get('dataType')),
      },
    },
    {
      name: 'outsourceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsourceFlag`).d('委外检验标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => ['CALCULATE_FORMULA'].includes(record.get('dataType')),
      },
    },
    {
      name: 'inspectFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectFlag`).d('检验标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      required: true,
    },
    {
      name: 'spcReleaseFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.spcReleaseFlag`).d('传输SPC标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      required: true,
    },
    {
      name: 'srmReleaseFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.srmReleaseFlag`).d('下发到SRM标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      required: true,
    },
    {
      name: 'destructiveExperimentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.destructiveExperimentFlag`).d('破坏性实验标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => ['CALCULATE_FORMULA'].includes(record.get('dataType')),
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-item/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const data = JSON.parse(val);
          if (data?.rows?.formula) {
            const formula = isJSONString(data.rows.formula);
            if (formula) {
              const { formulaId, formulaCode, formulaName, dimension, formulaList } = formula;
              data.rows.formulaId = formulaId;
              data.rows.formulaCode = formulaCode;
              data.rows.formulaName = formulaName;
              data.rows.dimension = dimension;
              data.rows.formulaList = formulaList;
            }
          }
          return {
            ...data,
          };
        },
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.qms.domain.entity.MtInspectItem';
      return {
        data: { inspectItemId: record.get('inspectItemId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_SAMPLING}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
});

const numberListDS: () => DataSetProps = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  dataKey: 'rows',
  fields: [
    { name: 'dataValue' },
    {
      name: 'multipleValue',
      dynamicProps: {
        range: ({ record }) => {
          return record.get('valueType') === 'section' ? ['leftValue', 'rightValue'] : false;
        },
      },
    },
    {
      name: 'leftChar',
      type: FieldType.string,
      defaultValue: '[',
    },
    {
      name: 'leftValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.leftValue';
          }
        },
      },
    },
    {
      name: 'rightChar',
      type: FieldType.string,
      defaultValue: ']',
    },
    {
      name: 'rightValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.rightValue';
          }
        },
      },
    },
    {
      name: 'valueType',
      type: FieldType.string,
      defaultValue: 'single',
    },
    {
      name: 'valueShow',
      type: FieldType.string,
    },
    {
      name: 'standard',
      type: FieldType.string,
    },
  ],
  events: {
    load: ({ dataSet }) => {
      if (!dataSet.length) {
        dataSet.loadData([{ leftChar: '(', rightChar: ')', valueType: 'single' }]);
        return;
      }
      dataSet.forEach(record => {
        if (record?.get('valueType') === 'section') {
          record?.set('multipleValue', {
            leftValue: record?.get('leftValue'),
            rightValue: record?.get('rightValue'),
          });
        } else {
          record?.set('multipleValue', record?.get('dataValue'));
        }
      });
    },
    update: ({ record, name }) => {
      switch (name) {
        case 'valueType':
        case 'leftValue':
        case 'rightValue':
        case 'leftChar':
        case 'rightChar':
        case 'multipleValue':
          handleUpdateRangeValue(record, name);
          break;
        default:
          break;
      }
    },
  },
});

const tableDS: () => DataSetProps = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  dataKey: 'rows',
  selection: false,
  paging: false,
  fields: [
    { name: 'fieldCode', label: intl.get(`${modelPrompt}.formulaParams`).d('公式参数') },
    { name: 'fieldName', label: intl.get(`${modelPrompt}.formulaParamsDesc`).d('公式参数描述') },
    {
      name: 'inspectItemLov',
      label: intl.get(`${modelPrompt}.associatedItems`).d('关联项目'),
    },
    {
      name: 'isRequired',
    },
  ],
});

const handleUpdateRangeValue = (record, name) => {
  if (record.get('valueType') === 'section') {
    if (!record.get('leftChar')) {
      record.set('leftChar', '(');
    }
    if (!record.get('rightChar')) {
      record.set('rightChar', ')');
    }
    const regPos = /^[+-]?[0-9]+.?[0-9]*/;
    const leftValue = regPos.test(record.get('leftValue')) ? record.get('leftValue') : '-∞';
    const rightValue = regPos.test(record.get('rightValue')) ? record.get('rightValue') : '+∞';
    const leftChar = record.get('leftChar') === '(' ? '<' : '≤';
    const rightChar = record.get('rightChar') === ')' ? '<' : '≤';
    record.set('valueShow', `${leftValue}${leftChar}X${rightChar}${rightValue}`);
  } else if (name === 'valueType') {
    record.set('multipleValue', undefined);
  }
};

export { detailDS, numberListDS, tableDS };
