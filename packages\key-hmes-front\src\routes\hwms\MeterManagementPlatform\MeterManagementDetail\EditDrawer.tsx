/**
 * @Description: 新建编辑-抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-03-01 19:39:13
 * @LastEditTime: 2021-09-13 14:19:16
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, Select, TextField, Lov, NumberField } from 'choerodon-ui/pro';

export default ({ ds, permissionDetail }) => {

  const handleChangeModel = value => {
    if (!value) {
      ds.current?.set('speciesLov', undefined);
      ds.current?.set('verificationMethod', undefined);
      ds.current?.set('reminderPeriod', undefined);
      ds.current?.set('verificationPeriod', undefined);
      return;
    }
    const {
      speciesCode,
      speciesName,
      verificationMethod,
      reminderPeriod,
      verificationPeriod,
    } = value;
    ds.current?.set('speciesLov', {
      speciesCode,
      speciesName,
    });
    ds.current?.set('verificationMethod', verificationMethod);
    ds.current?.set('reminderPeriod', reminderPeriod);
    ds.current?.set('verificationPeriod', verificationPeriod);
  };

  return (
    <Form labelWidth={132} dataSet={ds} columns={2} disabled={!permissionDetail?.approve}>
      <Lov name="siteLov" />
      <TextField name="siteName" />
      <TextField name="toolCode" />
      <Select name="categoryCode" />
      <Lov name="modelLov" onChange={handleChangeModel} />
      <Lov name="speciesLov" />
      <TextField name="range" />
      <TextField name="resolution" />
      <TextField name="assetNum" />
      <Lov name="userObj" />
      <Lov name="departmentLov" />
      <Lov name="responsibleUserObj" />
      <Lov name="prodObj" />
      <Lov name="processFromLov" />
      <Select name="otherPosition" />
      <TextField name="manufacturingNum" />
      <TextField name="manufacturer" />
      <Select name="verificationStatus" />
      <Select name="verificationMethod" />
      <NumberField name="reminderPeriod" precision={0} />
      <Select name="verificationAgency" />
      <NumberField name="verificationPeriod" precision={0} />
      <TextField name="remark" />
      <Select name="usingStatus" />
    </Form>
  );
};
