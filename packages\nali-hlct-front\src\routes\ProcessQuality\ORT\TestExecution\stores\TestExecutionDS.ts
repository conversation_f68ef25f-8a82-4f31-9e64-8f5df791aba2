/**
 * @Description: ORT测试执行-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-20 10:38:58
 * @LastEditTime: 2023-09-20 10:38:58
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.ort.testExecution';
const tenantId = getCurrentOrganizationId();
let endUrl;
endUrl = '-37685';
endUrl = '';

// 列表-ds
const headerDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  primaryKey: 'ortInspectTaskId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-inspect-task/list/ui`,
        method: 'get',
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('申请单号'),
      name: 'inspectDocNum',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskStatus`).d('状态'),
      name: 'taskStatus',
      lookupCode: 'YP.QIS.ORT_INSPECT_TASK_STATUS',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectType`).d('申请分类'),
      name: 'inspectType',
      lookupCode: 'YP.QIS.ORT_INSPECT_TYPE',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      name: 'materialLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.MATERIAL',
      valueField: 'materialId',
      textField: 'materialCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      bind: 'materialLov.materialName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('委托人'),
      name: 'createdByLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      bind: 'createdByLov.userId',
    },
    {
      name: 'createdName',
      bind: 'createdByLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
      name: 'projectStage',
      lookupCode: 'YP.QIS.ORT_PROJECT_STAGE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgencyDegree`).d('紧急程度'),
      name: 'urgencyDegree',
      lookupCode: 'YP.QIS.ORT_URGENCY_DEGREE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleType`).d('样品类型'),
      name: 'sampleType',
      lookupCode: 'YP.QIS.ORT_SAMPLE_TYPE',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.internalProductFlag`).d('是否厂内电芯'),
      name: 'internalProductFlag',
      lookupCode: 'YP.QIS.Y_N',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      valueField: 'prodLineId',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sampleReceiveBy`).d('委托人'),
      name: 'sampleReceiveByLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'sampleReceiveBy',
      bind: 'sampleReceiveByLov.userId',
    },
    {
      name: 'sampleReceiveName',
      bind: 'sampleReceiveByLov.realName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sampleReceiveBy`).d('收样人'),
      name: 'sampleReceiveByLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'sampleReceiveBy',
      bind: 'sampleReceiveByLov.userId',
    },
    {
      name: 'createdName',
      bind: 'sampleReceiveByLov.realName',
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sampleReceiveDateFrom`).d('收样开始时间'),
      name: 'sampleReceiveDateFrom',
      max: 'sampleReceiveDateTo',
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sampleReceiveDateTo`).d('收样结束时间'),
      name: 'sampleReceiveDateTo',
      min: 'sampleReceiveDateFrom',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('申请单号'),
      name: 'inspectDocNum',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskStatus`).d('状态'),
      name: 'taskStatus',
      lookupCode: 'YP.QIS.ORT_INSPECT_TASK_STATUS',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteLov',
      lovCode: 'MT.MODEL.SITE',
      valueField: 'siteId',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
      disabled: true,
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectType`).d('申请分类'),
      name: 'inspectType',
      lookupCode: 'YP.QIS.ORT_INSPECT_TYPE',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      name: 'materialLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.MATERIAL',
      valueField: 'materialId',
      textField: 'materialCode',
      disabled: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'productType',
      label: intl.get(`${modelPrompt}.productType`).d('产品规格'),
      bind: 'materialLov.productType',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('委托人'),
      name: 'createdByLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      disabled: true,
    },
    {
      name: 'createdBy',
      bind: 'createdByLov.userId',
    },
    {
      name: 'createdName',
      bind: 'createdByLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.department`).d('委托部门'),
      name: 'department',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectPurpose`).d('申请目的'),
      name: 'inspectPurpose',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('申请备注'),
      name: 'remark',
      disabled: true,
    },
    {
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleQty`).d('送样数量'),
      name: 'sampleQty',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleType`).d('样品类型'),
      name: 'sampleType',
      lookupCode: 'YP.QIS.ORT_SAMPLE_TYPE',
      disabled: true,
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.expectCompleteTime`).d('期望完成时间'),
      name: 'expectCompleteTime',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgencyDegree`).d('紧急程度'),
      name: 'urgencyDegree',
      lookupCode: 'YP.QIS.ORT_URGENCY_DEGREE',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
      name: 'projectStage',
      lookupCode: 'YP.QIS.ORT_PROJECT_STAGE',
      disabled: true,
    },

    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.internalProductFlag`).d('是否厂内电芯'),
      name: 'internalProductFlag',
      lookupCode: 'YP.QIS.Y_N',
      trueValue: 'Y',
      falseValue: 'N',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingMonth`).d('送样抽样月份'),
      name: 'samplingMonth',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      valueField: 'prodLineId',
      textField: 'prodLineName',
      lovPara: {
        tenantId,
      },
      disabled: true,
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ratedCapacity`).d('额定容量'),
      name: 'ratedCapacity',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResultDemand`).d('测试结果要求'),
      name: 'inspectResultDemand',
      lookupCode: 'YP.QIS.ORT_INSPECT_RESULT_DEMAND',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.withoutMaterialCode`).d('外场物料编码'),
      name: 'withoutMaterialCode',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sampleReceiveBy`).d('收样人'),
      name: 'sampleReceiveByLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      disabled: true,
    },
    {
      name: 'sampleReceiveBy',
      bind: 'sampleReceiveByLov.userId',
    },
    {
      name: 'sampleReceiveName',
      bind: 'sampleReceiveByLov.realName',
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sampleReceiveDate`).d('收样时间'),
      name: 'sampleReceiveDate',
      disabled: true,
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('测试开始时间'),
      name: 'actualStartTime',
      max: 'actualEndTime',
      disabled: true,
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('测试结束时间'),
      name: 'actualEndTime',
      min: 'actualStartTime',
      disabled: true,
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('测试报告'),
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      max: 1,
      dynamicProps: {
        disabled: ({ record }) => ['CANCEL', 'NEW', 'COMPLETED'].includes(record?.get('taskStatus')),
      },
    },
  ],
});

// 详情-ORT测试执行信息ds
const childrenDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: DataSetSelection.multiple,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItem`).d('测试项目名称'),
      name: 'inspectItem',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('测试方法'),
      name: 'inspectMethod',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardRequirement`).d('标准要求'),
      name: 'standardRequirement',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectFrequency`).d('测试频率'),
      name: 'inspectFrequency',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsourceFlag`).d('委外标识'),
      name: 'outsourceFlag',
      lookupCode: 'YP.QIS.Y_N',
      trueValue: 'Y',
      falseValue: 'N',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectQty`).d('测试数量'),
      name: 'inspectQty',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.exSampleSolveMethod`).d('预期样本处理'),
      name: 'exSampleSolveMethod',
      lookupCode: 'YP.QIS.ORT_SAMPLE_SAVE_METHOD',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.acSampleSolveMethod`).d('实际样本处理'),
      name: 'acSampleSolveMethod',
      lookupCode: 'YP.QIS.ORT_SAMPLE_SAVE_METHOD',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspector`).d('实验员'),
      name: 'inspectorLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      valueField: 'userId',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      disabled: true,
    },
    {
      name: 'inspector',
      bind: 'inspectorLov.userId',
    },
    {
      name: 'inspectorName',
      bind: 'inspectorLov.realName',
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('测试开始时间'),
      name: 'actualStartTime',
      max: 'actualEndTime',
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => {
          return dataSet.parent?.current?.get('taskStatus') === 'COMPLETED';
        },
      },
    },
    {
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('测试结束时间'),
      name: 'actualEndTime',
      min: 'actualStartTime',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectObject`).d('电芯条码'),
      name: 'inspectObject',
    },
  ],
});

const barCodeDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: DataSetSelection.multiple,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectObject`).d('电芯条码'),
      name: 'inspectObject',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResult`).d('测试结果'),
      name: 'inspectResult',
      lookupCode: 'YP.QIS.ORT_ITEM_INSPECT_RESULT',
      trueValue: 'Y',
      falseValue: 'N',
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => {
          return dataSet.parent?.parent?.current?.get('taskStatus') === 'COMPLETED';
        },
      },
    },
  ],
});

export { headerDS, childrenDS, barCodeDS };
