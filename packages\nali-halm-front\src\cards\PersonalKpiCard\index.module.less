.container {
  height: 100%;
  padding: 0 12px;
}
.query-bar {
  position: fixed;
  top: 8px;
  right: 12px;
  display: grid;
  grid-gap: 12px;
  grid-template-columns: 1fr 90px;
  width: 50%;
}
.customize-collapse {
  height: 100%;
  :global(.c7n-collapse-item) {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
  }
  :global(.c7n-collapse-content-box) {
    height: 100%;
  }
  :global(.c7n-collapse-header) {
    padding: 12px 0 4px 8px !important;
    &::before {
      top: calc(50% - 0.07rem + 4px) !important;
    }
  }
}
