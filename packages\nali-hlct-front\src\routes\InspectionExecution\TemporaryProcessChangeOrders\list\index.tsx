import React, { useEffect, useState } from 'react';
import { DataSet, Spin, Table, Button } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import request from 'utils/request';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import ListPageDS from '../stores/ListPageDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`;

const ListPage = props => {
  const {
    tableDs,
  } = props;

  const [selectedRecords, setSelectedRecords] = useState<Array<any>>([]); // 选中的行信息
  const [loading, setLoading] = useState(false); // 加载动画
  const [cancelFlag, setCancelFlag] = useState(true); // 取消按钮禁用标识

  useEffect(() => {
    if (tableDs?.currentPage) {
      tableDs.query(tableDs.currentPage);
      tableDs.unSelectAll();
    } else {
      tableDs.query();
      tableDs.unSelectAll();
    }
  }, []);

  const handleInspectObjTableSelect = ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
    setCancelFlag(dataSet.selected.filter(record=>!['NEW','AMENDING','REJECT'].includes(record.get('temporaryPermitStatus'))).length>0)
  };
  useDataSetEvent(tableDs, 'select', handleInspectObjTableSelect);
  useDataSetEvent(tableDs, 'selectAll', handleInspectObjTableSelect);
  useDataSetEvent(tableDs, 'unselect', handleInspectObjTableSelect);
  useDataSetEvent(tableDs, 'unselectAll', handleInspectObjTableSelect);

  const columns: ColumnProps[] = [
    {
      name: 'temporaryPermitNum',
      lock: ColumnLock.left,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleToDetail(record);
          }}
        >
          {value}
        </a>
      ),
      minWidth: 160,
    },
    {
      name: 'temporaryPermitStatus',
      minWidth: 160,
    },
    {
      name: 'materialCode',
      minWidth: 120,
    },
    {
      name: 'prodLineCode',
      minWidth: 120,
    },
    {
      name: 'operationName',
      minWidth: 160,
    },
    {
      name: 'equipmentName',
      minWidth: 160,
    },
    { 
      name: 'inspectSchemeCode',
      minWidth: 120,
    },
    {
      name: 'temporaryPermitDeptName',
      minWidth: 180,
    },
    {
      name: 'createdByName',
      minWidth: 100,
    },
    {
      name: 'creationDate',
      minWidth: 180,
    },
    {
      name: 'scheduleStartDate',
      minWidth: 180,
    },
    {
      name: 'scheduleEndDate',
      minWidth: 180,
    },
  ];

  /**
   * @description 跳转到详情页
   * @param record 当前行信息
   */
  const handleToDetail = record => {
    props.history.push(
      `/hwms/temporary-process-change-orders/detail/${record?.get('temporaryPermitDocId')}`,
    );
  };

  /**
   * @description 新建信息
   */
  const handleCreate = () => {
    props.history.push(`/hwms/temporary-process-change-orders/detail/create`);
  };

  /**
   * @description 取消按钮
   */
  const handleCancel = () => {
    setLoading(true);
    request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/cancel/ui`, {
      method: 'POST',
      data: selectedRecords.map(item => item.get('temporaryPermitDocId')),
    }).then(res => {
      if (res.failed) {
        notification.error({
          message: `${res.message}` || intl.get(`${modelPrompt}.error`).d('操作失败'),
        });
      } else {
        notification.success({
          message: intl.get(`${modelPrompt}.success`).d('操作成功'),
        });
        tableDs.unSelectAll();
        tableDs.query(props.tableDs.currentPage);
      }
      setLoading(false);
    });
  };

  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        <Header title={intl.get(`${modelPrompt}.title`).d('临时工艺变更单')}>
          <Button
            color={ButtonColor.primary}
            icon="add"
            onClick={() => handleCreate()}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </Button>
          <Button
            color={ButtonColor.primary}
            disabled={selectedRecords.length === 0 || cancelFlag}
            onClick={() => handleCancel()}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
        </Header>
        <Content>
          <Table
            searchCode="temporaryProcessChangeOrdersIndex"
            customizedCode="temporaryProcessChangeOrdersIndex"
            showCachedSelection
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
          />
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [
    'tarzan.common',
    'hzero.common',
    'tarzan.qms.temporaryProcessChangeOrders',
  ],
})(
  withProps(
    () => {
      const tableDs = new DataSet(ListPageDS());
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ListPage),
);
