import intl from 'utils/intl';

const prefix = 'alm.component.fileUpload.view';

function fileDS(fields = []) {
  return {
    selection: false,
    paging: false,
    pageSize: 10,
    fields: [
      {
        label: intl.get(`${prefix}.fileName`).d('名称'),
        name: 'fileName',
      },
      {
        label: intl.get(`${prefix}.uploadDate`).d('上传日期'),
        name: 'uploadDate',
      },
      {
        label: intl.get(`${prefix}.fileSize`).d('文件大小'),
        name: 'fileSize',
      },
      {
        label: intl.get(`${prefix}.uploaderName`).d('上传者'),
        name: 'uploaderName',
      },
      ...fields,
    ],
    transport: {},
    events: {},
  };
}

function logDS() {
  return {
    selection: false,
    paging: false,
    pageSize: 10,
    fields: [
      {
        label: intl.get(`${prefix}.fileName`).d('名称'),
        name: 'fileName',
      },
      {
        label: intl.get(`${prefix}.operationDate`).d('操作日期'),
        name: 'operationDate',
      },
      {
        label: intl.get(`${prefix}.operatorName`).d('操作人'),
        name: 'operatorName',
      },
      {
        label: intl.get(`${prefix}.description`).d('备注'),
        name: 'description',
      },
    ],
  };
}

export { fileDS, logDS };
