import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.receive.miscellaneousMes';
const tenantId = getCurrentOrganizationId();

// const endUrl = '-30607';
const endUrl = '';

const headerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'instructionDocId',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/wms-miscellaneous/page/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.HEAD`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
    },
    {
      name: 'instructionDocTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lovCode: 'MT.MISCELLANEOUS',
      valueField: 'value',
      textField: 'meaning',
      ignore: 'always',
      lovPara: {
        tenantId,
        description: 'noSCRAP',
      },
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      bind: 'instructionDocTypeObj.value',
    },
    {
      name: 'tag',
      type: FieldType.string,
      ignore: 'always',
      bind: 'instructionDocTypeObj.tag',
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
      lovPara: {
        tenantId,
      },
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: () => {
        return {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        };
      },
      dynamicProps: {
        lookupUrl: ({ record }) => {
          const type = record?.get('tag');
          if (type === 'PICK') {
            return `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_COST_CENTER_PICKING_DOC`;
          }
          return `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_COST_CENTER_RETURN_DOC`;
        },
        disabled: ({ record }) => {
          return !record?.get('instructionDocType');
        },
      },
    },
    {
      name: 'accountType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountType`).d('结算类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ACCOUNT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'costcenter',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.costcenter`).d('成本中心'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.COST_CENTER`,
      noCache: true,
      ignore: 'always',
      lovPara: { tenantId, accountType: 'COST_CENTER', enableFlag: 'Y' },
    },
    {
      name: 'costcenterId',
      bind: 'costcenter.costcenterId',
    },
    {
      name: 'internalOrder',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.internalOrder`).d('内部订单'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.INTERNAL_ORDER`,
      noCache: true,
      ignore: 'always',
      lovPara: { tenantId, accountType: 'INTERNAL_ORDER', enableFlag: 'Y' },
    },
    {
      name: 'internalOrderId',
      bind: 'internalOrder.costcenterId',
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'warehouse',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouse`).d('仓库'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: record?.get('siteId'),
            locatorCategoryList: ['AREA'],
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
      },
    },
    {
      name: 'warehouseId',
      type: FieldType.string,
      bind: 'warehouse.locatorId',
    },
    {
      name: 'locator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locator`).d('库位'),
      lovCode: 'MT.MODEL.SUB_LOCATOR',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorIds: record?.get('warehouseId'),
          };
        },
        disabled: ({ record }) => {
          return !record?.get('warehouseId');
        },
      },
    },
    {
      name: 'locatorId',
      type: FieldType.string,
      bind: 'locator.locatorId',
    },
    {
      name: 'user',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      noCache: true,
      ignore: 'always',
      textField: 'realName',
      valueField: 'id',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'userId',
      type: FieldType.string,
      bind: 'user.id',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'printFlag',
      type: FieldType.string,
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`tarzan.common.printFlag`).d('打印标识'),
    },
  ],
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
    },
    {
      name: 'instructionDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'accountTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountType`).d('结算类型'),
    },
    {
      name: 'costcenterCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.costcenter`).d('成本中心'),
    },
    {
      name: 'costcenterCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.costcenterCategory`).d('成本中心类型'),
    },
    {
      name: 'internalOrderCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.internalOrder`).d('内部订单'),
    },
    {
      name: 'internalOrderCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.internalOrderCategory`).d('内部订单类型'),
    },
    {
      name: 'printTimes',
      type: FieldType.string,
      label: intl.get(`tarzan.common.printTimes`).d('打印次数'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'applyPerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyPerson`).d('出/入库人'),
    },
    {
      name: 'applyReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyReason`).d('出/入库原因'),
    },
    {
      name: 'applyEquipment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyEquipment`).d('设备'),
    },
    {
      name: 'scrapReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapReason`).d('报废原因'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
    {
      name: 'purpose',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.purpose`).d('领料用途'),
    },
  ],
});

const lineTableDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    transport: {
      read: () => {
        return {
          // 采购订单列表 接口待替换
          url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/wms-miscellaneous/line/query/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.LINE`,
          method: 'GET',
        };
      },
    },
    fields: [
      {
        name: 'lineNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      },
      {
        name: 'identifyType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
        lookupCode: 'MT.APS.GEN_TYPE_URL',
        lovPara: {
          typeGroup: 'IDENTITY_TYPE',
          tenantId: getCurrentOrganizationId(),
          userId: getCurrentUserId(),
        },
        valueField: 'typecode',
        textField: 'description',
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.material`).d('物料'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialRevisionCode`).d('物料版本'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'model',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.model`).d('规格'),
      },
      {
        name: 'bomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
      },
      {
        name: 'quantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.quantity`).d('需求数量'),
      },
      {
        name: 'executedQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.locatorQty`).d('执行数量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'instructionStatusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.status`).d('状态'),
      },
      {
        name: 'soNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.soNumber`).d('销售订单'),
      },
      {
        name: 'soLineNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
      },
      {
        name: 'warehouseCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.warehouse`).d('仓库'),
      },
      {
        name: 'locatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.locator`).d('库位'),
      },
      {
        name: 'toleranceFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
      },
      {
        name: 'toleranceTypeDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
      },
      {
        name: 'toleranceMaxValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMax`).d('上允差值'),
      },
      {
        name: 'toleranceMinValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMin`).d('下允差值'),
      },
      {
        name: 'realName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.sourceInsCreatedBy`).d('执行人'),
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.sourceInsCreateDate`).d('执行时间'),
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
  };
};

export { headerTableDS, lineTableDS };
