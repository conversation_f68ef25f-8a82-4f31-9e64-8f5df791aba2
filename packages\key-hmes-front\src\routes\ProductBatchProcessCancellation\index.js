import React, { useMemo, useState } from 'react';
import { Button, DataSet, Table,Lov, Row, Col, Icon, Select, Form, TextField } from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { tableDS } from './stores';
import { revoke } from './services';
import InputLovDS from '../../stores/InputLovDS';
import LovModal from './LovModal';

const modelPrompt = 'tarzan.hmes.productBatchProcessCancellation';
const ProductBatchProcessCancellation = observer(() => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const columns = [
    {
      name: 'identification',
      width: 200,
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'eoStatusDesc',
    },
    {
      name: 'qualityStatusDesc',
    },
    {
      name: 'eoNum',
    },
    {
      name: 'workOrderNum',
    },
    {
      name: 'routerStepDesc',
    },
    {
      name: 'wipStatusDesc',
    },
  ];
  const handleRevoke = async () => {
    const res = await revoke(tableDs.selected.map(item => item.data));
    if(res&&!res.success){
      notification.error({
        message: res.message,
      });
    }else{
      notification.success();
      tableDs.query();
    }
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleSearch()
    }
  }
  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };
  const toggleForm = () => {
    setExpandForm(!expandForm);
  }
  const renderQueryBar = ({ buttons, queryDataSet, queryFields, dataSet }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identifications', '产品条码', queryDataSet)}
                    />
                  </div>
                }
              />
              <Select name="status" />
              <Lov name="operationLov"/>
              {expandForm && (
                <>
                  <Select name="eoStepStatus" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div
              style={{
                flexShrink: 0,
                display: 'flex',
                alignItems: 'center',
                marginTop: '7px',
              }}
            >
              <Button funcType="link" icon={
                expandForm? 'expand_less':'expand_more'
              } onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  }
  const handleSearch = async () => {
    tableDs.query();
  }
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('产品批量工序撤销')}>
        <Button
          disabled={tableDs.toData().length === 0 || tableDs.selected.length === 0}
          onClick={handleRevoke}
          color="primary"
        >
          {intl.get(`${modelPrompt}.button.batchRevocation`).d('批量撤销')}
        </Button>
      </Header>
      <Content>
        <Table
          searchCode="ProductBatchProcessCancellation"
          customizedCode="ProductBatchProcessCancellation"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
          queryBar={renderQueryBar}
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.productBatchProcessCancellation', 'tarzan.common'],
})(ProductBatchProcessCancellation);
