import React, { useMemo } from 'react';
import intl from "utils/intl";
import DashboardCard from '../DashboardCard.jsx';
import ScrollBoard from '../ScrollBoard.jsx';
import styles from '../../index.module.less';

const modelPrompt = 'tarzan.wms.DeviceStatusMonitoringBoard';

const EquipmentInfo = ({data}) => {
  const tableData = [];
  if (data.length) {
    data.forEach((val) => {
      const {
        assetNum,
        assetName,
        assetIdentification,
        locationName,
        personCharge,
        installationPosition,
        assetStatusName,
        acceptedStatus,
        confirmedStatus,
      } = val;
      tableData.push([
        assetNum,
        assetName,
        assetIdentification,
        locationName,
        personCharge,
        installationPosition,
        assetStatusName,
        acceptedStatus,
        confirmedStatus,
      ]);
    });
  }
  const config = useMemo(
    () => ({
      header: [
        intl.get(`${modelPrompt}.field.assetNum`).d('设备编号'),
        intl.get(`${modelPrompt}.field.assetName`).d('设备名称'),
        intl.get(`${modelPrompt}.field.assetIdentification`).d('设备现场标识'),
        intl.get(`${modelPrompt}.field.locationName`).d('资产位置'),
        intl.get(`${modelPrompt}.field.personCharge`).d('负责人'),
        intl.get(`${modelPrompt}.field.installationPosition`).d('安装位置'),
        intl.get(`${modelPrompt}.field.assetStatusName`).d('设备状态'),
        intl.get(`${modelPrompt}.field.acceptedStatus`).d('验收状态'),
        intl.get(`${modelPrompt}.field.confirmedStatus`).d('确认状态'),
      ],
      data: tableData,
      rowNum: 8,
      align: ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'],
      oddRowBGC: 'rgba(22,66,127,0.3)',
      headerBGC: 'rgb(3, 157, 206,0.3)',
      evenRowBGC: 'rgba(3,28,60, 0.3)',
      headerHeight: 40,
      // columnWidth: [150, 150, 150, 150, 120, 150, 120, 120, 100],
    }),
    [tableData],
  );
  return (
    <DashboardCard style={{ height: '100%' }} >
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-scroll-board-table']}>
          <ScrollBoard config={config} style={{ width: '100%', height: '100%',marginLeft: '0px 10px' }} />
        </div>
      </div>
    </DashboardCard>
  );
};

export default EquipmentInfo;
