// import moment from 'moment';
import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.emergencyTask';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/emergency-task/list`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows',
  totalKey: 'rows',
  autoLocateFirst: false,
  fields: [
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.inspectBusinessTypeDesc`).d('任务类型'),
    },
    {
      name: 'inspectInfoCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.inspectInfoCreationDate`).d('报检时间'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.materialName`).d('物料描述'),
    },
    {
      name: 'supplierNameAlt',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.supplierNameAlt`).d('供应商'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.siteName`).d('基地名称'),
    },
  ],
});

const checkTableDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/check-supplier/list`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows',
  totalKey: 'rows',
  autoLocateFirst: false,
  fields: [
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.supplierName`).d('供应商'),
    },
    {
      name: 'ngQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.ngQty`).d('不良数据'),
    },
    {
      name: 'inspectQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.inspectQty`).d('报检总数'),
    },
    {
      name: 'inspectRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.inspectRate`).d('批数不良率'),
    },
    {
      name: 'ncReportDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.ncReportDesc`).d('不良描述'),
    },
  ],
});

const ngTableDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/ng-report/info`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows',
  totalKey: 'rows',
  autoLocateFirst: false,
  fields: [
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.actualEndTime`).d('发生时间'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.supplierName`).d('供应商'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.materialName`).d('物料描述'),
    },
    {
      name: 'ncReportDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.ncReportDesc`).d('不良描述'),
    },
  ],
});

// 获取当前日期对应的当月第几周
// const currentWeekOfMonth = () => {
//   // 获取当前时间转字符串并用字符串方法split转数组，获取当前时间年、月、日
//   const currentTimeArr = moment().month("YYYY-MM-DD").format('YYYY-MM-DD').split('-')
//   // 当前日期年
//   const year = currentTimeArr[0];
//   // 当前日期月
//   const month = currentTimeArr[1];
//   // 当前日期日
//   const day = currentTimeArr[2];
//   // 获取本月有多少天
//   const monthDay = moment(moment().month("YYYY-MM").format('YYYY-MM'), "YYYY-MM").daysInMonth()
//   // 创建一个新数组，用来接收本月所有周未的日，如果本月最后一天不是周日那也算是周未
//   const monthWeekend:any = [];
//   // 如果本月的最后一天不是周日那也算作一周，push进数组
//   if (moment(moment(`${year}-${month}-${monthDay} 00:00:00`).format()).day() !==  0) {
//     monthWeekend.push(monthDay);
//   }
//   let indexWeek;
//   // 循环遍历当前月的每一天
//   for (let i = 1; i <= monthDay; i++) {
//     // 获取每一天是周几
//     const week = moment(moment(`${year}-${month}-${i} 00:00:00`).format()).day()
//     // 如果当前i==今天，就看之前的周末数组里有几个值就是第几周
//     if (day === i) {
//       indexWeek = monthWeekend.length;
//     } else {
//       // 如果当前i不是今天并且当前i是周末，那就push进周末数组
//       // eslint-disable-next-line no-lonely-if
//       if (week === 0) {
//         monthWeekend.push(i);
//       }
//     }
//   }
//   // 返回当前日期是本月的第几周
//   return indexWeek;
// };

const filterDS = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
      ignore: FieldIgnore.always,
      multiple: true,
      lovCode: 'MT.MATERIAL',
      textField: 'materialName',
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },
    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
  ],
});

export { tableDS, checkTableDS, ngTableDS, filterDS };
