/**
 * @Description: 新库位维护-详情页组件
 * @Author: <<EMAIL>>
 * @Date: 2021-03-05 14:14:48
 * @LastEditTime: 2021-03-05 14:14:48
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, NumberField, Lov, TextField } from 'choerodon-ui/pro';
import { isNull } from 'lodash';

const BasicInfoTab = props => {
  const { dataSet, canEdit, columns = 1, focus = true } = props;
  // 改为六位小数
  const handleChangeNumber = (field, value) => {
    const data = {};
    if (!isNull(value)) {
      data[field] = value.toFixed(6);
      dataSet.current.set(data);
    }
  };

  // 重量单位lov改变时
  const handleWeightUom = record => {
    if (isNull(record)) {
      dataSet.current.set('weightUomDesc', undefined);
    } else {
      const { uomName } = record;
      dataSet.current.set('weightUomDesc', uomName);
    }
  };

  // 尺寸单位lov改变时
  const handleSizeUom = record => {
    if (isNull(record)) {
      dataSet.current.set('sizeUomDesc', undefined);
    } else {
      const { uomName } = record;
      dataSet.current.set('sizeUomDesc', uomName);
    }
  };

  return (
    <Form
      disabled={!canEdit || focus}
      dataSet={dataSet}
      columns={columns}
      labelLayout="horizontal"
      labelWidth={112}
    >
      <NumberField
        name="maxWeight"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('maxWeight', value);
        }}
        renderer={({ value }) => (value ? `${value.toFixed(6)}` : '')}
      />
      <Lov name="weightUomCodeLov" placeholder=" " noCache onChange={handleWeightUom} />
      <TextField name="weightUomDesc" disabled />
      <NumberField
        name="maxCapacity"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('maxCapacity', value);
        }}
        renderer={({ value }) => (value ? `${value.toFixed(6)}` : '')}
      />
      <Lov name="sizeUomCodeLov" placeholder=" " noCache onChange={handleSizeUom} />
      <TextField name="sizeUomDesc" disabled />
      <NumberField
        name="length"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('length', value);
        }}
        renderer={({ value }) => (value ? `${value.toFixed(6)}` : '')}
      />
      <NumberField
        name="width"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('width', value);
        }}
        renderer={({ value }) => (value ? `${value.toFixed(6)}` : '')}
      />
      <NumberField
        name="height"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('height', value);
        }}
        renderer={({ value }) => (value ? `${value.toFixed(6)}` : '')}
      />
    </Form>
  );
};

export default BasicInfoTab;
