/**
 * @Description: 检验方案模板维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-05-25 09:54:41
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import uuid from 'uuid/v4';
import myInstance from '@utils/myAxios';
import {
  DataSet,
  Button,
  Form,
  TextField,
  Select,
  Switch,
  Lov,
  Attachment,
  Spin,
  Modal,
} from 'choerodon-ui/pro';
import { Collapse, Icon, Tabs } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { drawerPropsC7n } from '@components/tarzan-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { isJSONString } from '@/utils';
import {
  fetchInspectSchemeTmp,
  saveInspectSchemeTmp,
  checkInspectSchemeTmpConfig,
  inspectSchemeTmpConfig,
  checkAllInspectSchemeTmpConfig,
  mtInspectItemDetailConfig,
  fetchRuleDtlConfig,
  InstantiationInfo,
} from '../services';
import {
  detailFormDS,
  inspectionItemBasisDS,
  dimensionTableDS,
  instantiationFormDS,
  instantiationTableDS,
  copyBusinessTypeDS,
} from '../stores/InspectionSchemeTemplateDS';
import { DetailTableDS } from '../components/stores';
import DimensionTableListDrawer from './DimensionTableListDrawer';
import InstantiationDrawer from './InstantiationDrawer';
import DimensionDetailTableList from '../components/DimensionDetailTableList/InspectItemTab';
import StatisticsTable from './StatisticsTable';
import DetailComponent from '@/routes/TestingStrategy/InspectItemMaintain/components/DetailComponent';
import CollapsePanelTitle from '../components/CollapsePanelTitle';
import TabsTabPaneTitle from '../components/TabsTabPaneTitle';
import AddActiveTabDimensionButton from '../components/AddActiveTabDimensionButton';

let batchAddInspectionDimensionModal;
let instantiationModal;

const tenantId = getCurrentOrganizationId();

const customizeUnitCode = `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.DETAIL,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.LINE_DETAIL,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.DMS_DETAIL,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.ITEM_DETAIL,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.ITEM_DRAWER`;

const _newIgnoreKeys = [
  'creationDate',
  'createdBy',
  'lastUpdateDate',
  'lastUpdatedBy',
  'objectVersionNumber',
  '_token',
  'inspectGroupItemId',
  'sequence',
  'inspectItemLov',
  'inspectItemId',
  'inspectItemCode',
  'inspectItemDesc',
  'enclosure',
  'inspectFrequencyDesc',
];

const modelPrompt = 'tarzan.inspectionSchemeTemplate';
const { Panel } = Collapse;
const { TabPane } = Tabs;
const { Option } = Select;

const InspectionSchemeTable = props => {
  const {
    match: {
      path,
      params: { id },
    },
    customizeForm,
    customizeTable,
    custConfig,
  } = props;

  const statisticsTableRef = useRef<any>(null);

  const childRef = useRef<any>();

  const [titleMap, setTitleMap] = useState({});

  const titleMapOri = useMemo(() => {
    return {
      areaId: {
        valueKey: 'areaName',
        text: intl.get(`${modelPrompt}.area`).d('区域'),
      },
      prodLineId: {
        valueKey: 'prodLineName',
        text: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      },
      processWorkcellId: {
        valueKey: 'processWorkcellName',
        text: intl.get(`${modelPrompt}.process`).d('工序'),
      },
      stationWorkcellId: {
        valueKey: 'stationWorkcellName',
        text: intl.get(`${modelPrompt}.station`).d('工位'),
      },
      equipmentId: {
        valueKey: 'equipmentName',
        text: intl.get(`${modelPrompt}.equipment`).d('设备'),
      },
      operationId: {
        valueKey: 'operationName',
        text: intl.get(`${modelPrompt}.operation`).d('工艺'),
      },
      supplierId: {
        valueKey: 'supplierName',
        text: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      },
      customerId: {
        valueKey: 'customerName',
        text: intl.get(`${modelPrompt}.customer`).d('客户'),
      },
    };
  }, []);

  // 编辑状态
  const [canEdit, setCanEdit] = useState(false);

  const [pageIdObject, setPageIdObject] = useState({ id: null, isCopy: false });

  // 生成新tabkey
  const [newTabIndex, setNewTabIndex] = useState(100);
  // 当前tabkey
  const [activeKey, setActiveKey] = useState('');
  // tab及数据列表
  const [inspectSchemeLines, setInspectSchemeLines] = useState<Array<any>>([]);

  // 维度折叠面板数据
  const [collapseActiveKeys, setCollapseActiveKeys] = useState({});

  // 获取请选择检验业务类型列表
  const fetchRuleDtl = useRequest(fetchRuleDtlConfig(), {
    manual: true,
    needPromise: true,
  });

  const queryDetail = useRequest(fetchInspectSchemeTmp(), {
    // 详情页数据查询
    manual: true,
  });

  const saveDetail = useRequest(saveInspectSchemeTmp(), {
    // 详情页数据保存
    manual: true,
    needPromise: true,
  });

  // 检查实例化
  const checkInspectSchemeTmp = useRequest(checkInspectSchemeTmpConfig(), {
    manual: true,
    needPromise: true,
  });
  // 确认实例化
  const inspectSchemeTmp = useRequest(inspectSchemeTmpConfig(), {
    manual: true,
    needPromise: true,
  });
  // 实例化必输值检查
  const checkAllInspectScheme = useRequest(checkAllInspectSchemeTmpConfig(), {
    manual: true,
    needPromise: true,
  });

  const instantiationInfo = useRequest(InstantiationInfo(), {
    manual: true,
    needPromise: true,
  });

  // 检验项目详情
  const mtInspectItemDetail = useRequest(mtInspectItemDetailConfig(), {
    manual: true,
    needPromise: true,
  });

  const formDs = useMemo(() => new DataSet(detailFormDS()), []);
  const dimensionTableDs = useMemo(() => new DataSet(dimensionTableDS()), []);
  const instantiationFormDs = useMemo(() => new DataSet(instantiationFormDS()), []);
  const instantiationTableDs = useMemo(() => new DataSet(instantiationTableDS()), []);
  const copyBusinessTypeDs = useMemo(() => new DataSet(copyBusinessTypeDS()), []);

  // 添加检验方案信息表单监听
  useEffect(() => {
    function processDataSetListener(flag) {
      const handler = flag ? formDs.addEventListener : formDs.removeEventListener;
      handler.call(formDs, 'update', handleformDsUpdate);
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  // 初始化页面
  useEffect(() => {
    let isCopy = false;
    let newId = id;
    if (newId.indexOf('copy') > -1) {
      isCopy = true;
      newId = id.split('copy')[0];
    }
    setPageIdObject({
      id: newId,
      isCopy,
    });
  }, [id]);

  // 初始化页面
  useEffect(() => {
    if (pageIdObject.id === 'create') {
      pageReset();
      setTimeout(() => {
        setCanEdit(true);
        addTabs('new');
      }, 100);
      return;
    }
    if (pageIdObject.id && pageIdObject.id !== 'create') {
      initPageData();
    }
    formatTitleMap();
  }, [pageIdObject]);

  // 基础表单监听事件
  const handleformDsUpdate = ({ name, value }) => {
    switch (name) {
      case 'siteObject':
        handleFormDsValueChange('siteObject', value);
        handleResetDimensionsDs(false);
        break;
      case 'inspectSchemeObjectTypeObject':
        handleInspectSchemeObjectTypeObjectChange();
        handleResetDimensionsDs(false);
        break;
      default:
        break;
    }
  };

  const formatTitleMap = () => {
    const codeConfigFields =
      custConfig[`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.DMS_DETAIL`]?.fields;
    const newTitleMap = {
      ...titleMapOri,
    };
    codeConfigFields.forEach(item => {
      newTitleMap[item.fieldCode] = {
        valueKey: item.fieldCode,
        text: item.fieldName,
      };
    });

    setTitleMap(newTitleMap);
  };

  const pageReset = () => {
    setActiveKey(`${newTabIndex + 1}`);
    setNewTabIndex(newTabIndex + 1);
    setInspectSchemeLines([]);
    formDs.loadData([{ enableFlag: 'Y' }]);
    dimensionTableDs.loadData([]);
  };

  // 初始化页面
  const initPageData = () => {
    setCanEdit(!!pageIdObject.isCopy);
    queryDetail.run({
      params: {
        customizeUnitCode,
        inspectSchemeTmpId: pageIdObject.id,
      },
      onSuccess: res => {
        const enclosureRecordList: Array<any> = [];
        const { inspectSchemeLines, ...other } = res;
        if (pageIdObject.isCopy) {
          delete other.inspectSchemeTmpId;
          delete other.inspectSchemeTmpCode;
        }
        // 检验方案信息加载
        formDs.loadData([other]);

        // 复制复制附件
        if (pageIdObject.isCopy) {
          formDs.forEach(record => {
            if (record.get('enclosure')) {
              enclosureRecordList.push(record);
            }
          });
        }
        // 初始化tab单元

        let _newTabIndex = newTabIndex;
        const _activeKey = newTabIndex + 1;
        // 保存所有tab的检验业务类型, 用做tab的检验业务类型过滤条件
        const allInspectBusinessType: Array<any> = [];

        // 保存维度折叠面板key
        const initCollapseActiveKeys: any = {};
        const newInspectSchemeLines = inspectSchemeLines.map(item => {
          // 更新tab最新key
          _newTabIndex += 1;
          const { dimensions, ...linesOther } = item;
          // tab基础信息ds
          const inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());
          inspectionItemBasisDs.loadData([linesOther]);
          if (linesOther.inspectBusinessType) {
            allInspectBusinessType.push(linesOther.inspectBusinessType);
          }

          return {
            key: `${_newTabIndex}`,
            inspectionItemBasisDs,
            // tab下维度信息读取
            dimensions: dimensions.map(dimensionsItem => {
              const { items, ...dimensionsItemother } = dimensionsItem;
              const itemsDs = new DataSet(DetailTableDS({}));
              itemsDs.loadData(
                items.map(itemsItem => {
                  const _valueItem = { ...itemsItem };
                  if (['TEXT', 'DECISION_VALUE'].includes(_valueItem.dataType)) {
                    _valueItem.trueValue =
                      (_valueItem.trueValueList || []).length > 0
                        ? _valueItem.trueValueList[0].dataValue
                        : null;
                    _valueItem.falseValue =
                      (_valueItem.falseValueList || []).length > 0
                        ? _valueItem.falseValueList[0].dataValue
                        : null;
                  }
                  if (_valueItem.dataType === 'VALUE_LIST') {
                    _valueItem.trueValue =
                      (_valueItem.trueValueList || []).length > 0
                        ? _valueItem.trueValueList.map(trueItem => trueItem.dataValue)
                        : null;
                    _valueItem.falseValue =
                      (_valueItem.falseValueList || []).length > 0
                        ? _valueItem.falseValueList.map(falseItem => falseItem.dataValue)
                        : null;
                  }

                  if (['CALCULATE_FORMULA'].includes(_valueItem.dataType)) {
                    const formula = isJSONString(_valueItem.formula || '');
                    if (formula) {
                      const {
                        formulaId,
                        formulaCode,
                        formulaName,
                        dimension,
                        formulaList,
                      } = formula;
                      _valueItem.formulaId = formulaId;
                      _valueItem.formulaCode = formulaCode;
                      _valueItem.formulaName = formulaName;
                      _valueItem.dimension = dimension;
                      _valueItem.formulaList = formulaList;
                    } else {
                      _valueItem.formulaId = null;
                      _valueItem.formulaCode = null;
                      _valueItem.formulaName = null;
                      _valueItem.dimension = null;
                      _valueItem.formulaList = null;
                    }
                  }

                  return {
                    ..._valueItem,
                    inspectionItemRowUuid: itemsItem.inspectObjectItemTmpId,
                    samplingDimension: linesOther.samplingDimension,
                  };
                }),
              );
              // 复制复制附件
              if (pageIdObject.isCopy) {
                itemsDs.forEach(record => {
                  if (record.get('enclosure')) {
                    enclosureRecordList.push(record);
                  }
                });
              }

              // 维度折叠面板id插入
              if (initCollapseActiveKeys[`${_newTabIndex}`]) {
                initCollapseActiveKeys[`${_newTabIndex}`].push(
                  `${dimensionsItemother.inspectObjectDmsTmpId}`,
                );
              } else {
                initCollapseActiveKeys[`${_newTabIndex}`] = [
                  `${dimensionsItemother.inspectObjectDmsTmpId}`,
                ];
              }

              const dimensionDetailDs = new DataSet(dimensionTableDS());
              dimensionDetailDs.loadData([
                {
                  ...dimensionsItemother,
                  uuid: dimensionsItemother.inspectObjectDmsTmpId,
                  siteId: other.siteId,
                  siteCode: other.siteCode,
                  revisionCode: other.revisionCode,
                },
              ]);
              const newDimensionsItem = {
                itemsDs,
                dimensionDetailDs,
              };
              return newDimensionsItem;
            }),
          };
        });

        setCollapseActiveKeys(initCollapseActiveKeys);
        setNewTabIndex(_newTabIndex);

        newInspectSchemeLines.forEach(item => {
          item.inspectionItemBasisDs.forEach(record => {
            record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
            record.set('inspectSchemeObjectType', other.inspectSchemeObjectType);
            record.set('siteId', other.siteId);
          });
        });

        setInspectSchemeLines(newInspectSchemeLines);

        enclosureRecordList.forEach(record => {
          getNewUuid(record);
        });

        // 设置当前tabkey
        setActiveKey(`${_activeKey}`);
      },
    });
  };

  // 校验所有ds
  const validateAllDs = async () => {
    // 不需要弹窗提示表单列表
    const validateDsListNormal: Array<any> = [];
    // 需要弹窗提示表单列表
    const validateDsListNotice: Array<any> = [];

    // 整理所有ds插入对应列表

    // 维度检验信息

    const dimensionsValidateList = [];

    // 检验方案信息不需要
    validateDsListNormal.push(formDs);
    inspectSchemeLines.forEach(tabPaneItem => {
      const { inspectionItemBasisDs, dimensions } = tabPaneItem;
      if (inspectionItemBasisDs) {
        // 检验项目tab基础信息 不需要
        validateDsListNormal.push(inspectionItemBasisDs);
      }
      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        const itemUuids = [];

        if (itemsDs) {
          // 检验项目维度列表 将所有tab下的维度列表record 提取插入临时数组 并记录对应报错信息
          itemsDs.toData().forEach((tableRecord: any) => {
            // @ts-ignore
            itemUuids.push(tableRecord.inspectItemId);
          });
          itemsDs.forEach(record => {
            const tableRecord = record.toData();
            if (tableRecord.dataType === 'CALCULATE_FORMULA') {
              const formulaList = tableRecord.formulaList || [];
              if (formulaList?.length > 0) {
                formulaList.forEach(formulaListItem => {
                  if (formulaListItem.isRequired === 'Y' && !formulaListItem.inspectItemId) {
                    //         message: `-${erroeRecordCode}`,
                    // message: `-${erroeRecordCode}`,

                    dimensionsValidateList.push(
                      // @ts-ignore
                      `${intl
                        .get(`${modelPrompt}.inspectBusinessType`)
                        .d('检验业务类型')}【${inspectionItemBasisDs.current.get(
                        'inspectBusinessTypeDesc',
                      )}】- ${intl
                        .get(`${modelPrompt}.inspectionDimension`)
                        .d('检验维度')}【${selectOptionFormat(dimensionsItem)}】- ${intl
                        .get(`${modelPrompt}.inspectionItem`)
                        .d('检验项目')}【${record.get('inspectItemDesc')}】${intl
                        .get(`${modelPrompt}.message.needBindInspectItem`)
                        .d('数据类型为计算公式时需要关联检验项目')}`,
                    );
                  }
                  // @ts-ignore
                  if (
                    formulaListItem.inspectItemId &&
                    // @ts-ignore
                    !itemUuids.includes(formulaListItem.inspectItemId)
                  ) {
                    dimensionsValidateList.push(
                      // @ts-ignore
                      `${intl
                        .get(`${modelPrompt}.inspectBusinessType`)
                        .d('检验业务类型')}【${inspectionItemBasisDs.current.get(
                        'inspectBusinessTypeDesc',
                      )}】- ${intl
                        .get(`${modelPrompt}.inspectionDimension`)
                        .d('检验维度')}【${selectOptionFormat(dimensionsItem)}】- ${intl
                        .get(`${modelPrompt}.inspectionItem`)
                        .d('检验项目')}【${record.get('inspectItemDesc')}】${intl
                        .get(`${modelPrompt}.message.matchBindInspectItem`)
                        .d('关联的检验项目未找到')}`,
                    );
                  }
                });
              }

              validateDsListNotice.push({
                record,
                title: `${intl
                  .get(`${modelPrompt}.inspectBusinessType`)
                  .d('检验业务类型')}【${inspectionItemBasisDs.current.get(
                  'inspectBusinessTypeDesc',
                )}】- ${intl
                  .get(`${modelPrompt}.inspectionDimension`)
                  .d('检验维度')}【${selectOptionFormat(dimensionsItem)}】- ${intl
                  .get(`${modelPrompt}.inspectionItem`)
                  .d('检验项目')}【${record.get('inspectItemDesc')}】${intl
                  .get(`${modelPrompt}.submitError`)
                  .d('必输字段未维护，请检查！')}`,
              });
            }
          });
        }
      });
    });

    // 校验无需弹窗的表单
    const normalValidate = await Promise.all(
      validateDsListNormal.map(async validateDsListItem => {
        const itemValidate = await validateDsListItem.validate();
        return itemValidate;
      }),
    );

    // 校验需要弹窗的表单
    const noticeValidate = await Promise.all(
      validateDsListNotice.map(async validateDsListItem => {
        const itemValidate = await validateDsListItem.record.validate('all');
        return { itemValidate, title: validateDsListItem.title };
      }),
    );

    // 汇总校验结果
    const normalResult = normalValidate.every(val => val);

    // 校验不通过的信息提取一条并提示
    let valmessage;
    const noticeResult = noticeValidate.every(val => {
      if (!val.itemValidate) {
        valmessage = val.title;
      }
      return val.itemValidate;
    });

    if (!noticeResult) {
      notification.error({
        message: `${valmessage}`,
      });
    }

    if (dimensionsValidateList.length > 0) {
      notification.error({
        message: `${dimensionsValidateList[0]}`,
      });
    }

    // 返回校验结果
    return normalResult && noticeResult && dimensionsValidateList.length === 0;
  };

  // 组合数据
  const getAllData = () => {
    const params: any = formDs.toData()[0] || {};

    if (params.siteObject) {
      delete params.siteObject;
    }
    if (params.inspectSchemeObjectTypeObject) {
      delete params.inspectSchemeObjectTypeObject;
    }

    const _inspectSchemeLines = inspectSchemeLines.map(item => {
      // 更新tab最新key
      const { inspectionItemBasisDs, dimensions, ...linesOther } = item;
      return {
        ...linesOther,
        ...inspectionItemBasisDs.toData()[0],
        // tab下维度信息读取
        dimensions: dimensions.map(dimensionsItem => {
          const { itemsDs, dimensionDetailDs } = dimensionsItem;
          return {
            ...dimensionDetailDs.toData()[0],
            items: itemsDs.toData().map(itemsDsDataItem => {
              const _itemsDsDataItem = { ...itemsDsDataItem };
              _itemsDsDataItem.trueValueList = itemsDsDataItem.trueValueList || [];
              _itemsDsDataItem.falseValueList = itemsDsDataItem.falseValueList || [];
              _itemsDsDataItem.warningValueList = itemsDsDataItem.warningValueList || [];
              return _itemsDsDataItem;
            }),
          };
        }),
      };
    });

    params.inspectSchemeLines = _inspectSchemeLines;
    return params;
  };

  // 保存
  const handleSave = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      return;
    }

    const params = getAllData();

    saveDetail.run({
      queryParams: {
        customizeUnitCode,
      },
      params,
      onSuccess: res => {
        // @ts-ignore
        notification.success();
        if (pageIdObject.id === 'create' || pageIdObject.isCopy) {
          props.history.push(`/hwms/inspection-scheme-template-maintenance/detail/${res}`);
        } else {
          initPageData();
        }
      },
    });
  };

  // 保存并新建下一条
  const handleSaveAndNext = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      return;
    }

    const params = getAllData();

    saveDetail.run({
      queryParams: {
        customizeUnitCode,
      },
      params,
      onSuccess: () => {
        // @ts-ignore
        notification.success();
        if (pageIdObject.id === 'create') {
          pageReset();
          setTimeout(() => {
            setCanEdit(true);
            addTabs('new');
          }, 100);
        } else {
          props.history.push(`/hwms/inspection-scheme-template-maintenance/detail/create`);
        }
      },
    });
  };

  // 另存
  const handleSaveAnother = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      return;
    }

    const enclosureRecordList: Array<any> = [];

    formDs.forEach(record => {
      if (record.get('enclosure')) {
        enclosureRecordList.push(record);
      }
    });

    inspectSchemeLines.forEach(item => {
      const { dimensions } = item;
      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        itemsDs.forEach(record => {
          if (record.get('enclosure')) {
            enclosureRecordList.push(record);
          }
        });
      });
    });

    await Promise.all(
      enclosureRecordList.map(async record => {
        await getNewUuid(record);
        return true;
      }),
    );

    const params = getAllData();
    if (params.inspectSchemeTmpId) {
      delete params.inspectSchemeTmpId;
    }
    if (params.inspectSchemeTmpCode) {
      delete params.inspectSchemeTmpCode;
    }

    saveDetail.run({
      queryParams: {
        customizeUnitCode,
      },
      params,
      onSuccess: res => {
        // @ts-ignore
        notification.success();
        props.history.push(`/hwms/inspection-scheme-template-maintenance/detail/${res}`);
      },
    });
  };

  // 取消
  const handleCancel = () => {
    if (pageIdObject.id === 'create' || pageIdObject.isCopy) {
      props.history.push('/hwms/inspection-scheme-template-maintenance/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  // 切换tab
  const handleChangeTab = newActiveKey => {
    setActiveKey(newActiveKey);
  };

  // 增加tab
  const addTabs = value => {
    const allInspectBusinessType: Array<any> = [];
    const formDsData: any = formDs.toData()[0] || {};

    if (value !== 'new') {
      inspectSchemeLines.forEach(item => {
        item.inspectionItemBasisDs.forEach(record => {
          if (record.get('inspectBusinessType')) {
            allInspectBusinessType.push(record.get('inspectBusinessType'));
          }
        });
      });
    }

    const inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());
    inspectionItemBasisDs.loadData([
      {
        inspectBusinessTypeObjectIgnore: allInspectBusinessType.join(','),
        inspectSchemeObjectType: formDsData.inspectSchemeObjectType,
        siteId: formDsData.siteId,
      },
    ]);

    const dimensionDetailDs = new DataSet(dimensionTableDS());
    const newDsData: any = {
      uuid: uuid(),
      sequence: 10,
      siteId: formDsData.siteId,
    };

    dimensionDetailDs.loadData([newDsData]);
    const itemsDs = new DataSet(DetailTableDS({}));

    const newCollapseActiveKeys = { ...collapseActiveKeys };
    newCollapseActiveKeys[`${newTabIndex + 1}`] = [`${newDsData.uuid}`];
    setCollapseActiveKeys(newCollapseActiveKeys);

    setInspectSchemeLines([
      ...(value !== 'new' ? inspectSchemeLines : []),
      {
        key: `${newTabIndex + 1}`,
        inspectionItemBasisDs,
        dimensions: [
          {
            itemsDs,
            dimensionDetailDs,
          },
        ],
      },
    ]);

    setActiveKey(`${newTabIndex + 1}`);
    setNewTabIndex(newTabIndex + 1);
  };

  // 删除tab
  const removeTabs = targetKey => {
    let _activeIndex = 0;
    const _tabPanes = inspectSchemeLines.filter((item, index) => {
      if (item.key === targetKey) {
        _activeIndex = index;
      }
      return item.key !== targetKey;
    });
    if (_tabPanes.length === 0) {
      setActiveKey('');
    } else if (_activeIndex > _tabPanes.length - 1) {
      setActiveKey(`${_tabPanes[_tabPanes.length - 1].key}`);
    } else {
      setActiveKey(`${_tabPanes[_activeIndex].key}`);
    }
    setInspectSchemeLines(_tabPanes);
  };

  const onTabsEdit = (targetKey, action) => {
    if (action === 'add') {
      addTabs(null);
    } else if (action === 'remove') {
      removeTabs(targetKey);
    }
  };

  // 新增维护
  const addActiveTabDimensionHandleOk = () => {
    const _tabPanes: Array<any> = [];
    const dimensionTableDsData = dimensionTableDs.toData();
    const duplicateList: Array<any> = [];
    const compareList = dimensionTableDsData.map((item: any) => {
      const valueString = `materialId${item.materialId || ''}+areaId${item.areaId ||
        ''}+prodLineId${item.prodLineId || ''}+processWorkcellId${item.processWorkcellId ||
        ''}+stationWorkcellId${item.stationWorkcellId || ''}+equipmentId${item.equipmentId ||
        ''}+operationId${item.operationId || ''}+supplierId${item.supplierId ||
        ''}+customerId${item.customerId || ''}+otherObject${item.otherObject || ''}+
      `;
      if (duplicateList.indexOf(valueString) === -1) {
        duplicateList.push(valueString);
      }
      return valueString;
    });
    if (duplicateList.length !== compareList.length) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.repeatNotice`).d('请勿输入完全相同的检验维度'),
      });
      return false;
    }

    const newCollapseActiveKeys = { ...collapseActiveKeys };

    inspectSchemeLines.forEach(item => {
      if (activeKey === item.key) {
        const newDimensions: Array<any> = [];
        const _dimensions = item.dimensions;
        dimensionTableDsData.forEach((dimensionTableDsDataItem: any) => {
          const newDimensionsItem: any = {};
          _dimensions.forEach(dimensionsItem => {
            if (
              dimensionTableDsDataItem.uuid === dimensionsItem.dimensionDetailDs.current.get('uuid')
            ) {
              newDimensionsItem.itemsDs = dimensionsItem.itemsDs;
              newDimensionsItem.dimensionDetailDs = dimensionsItem.dimensionDetailDs;
              newDimensionsItem.dimensionDetailDs.loadData([dimensionTableDsDataItem]);
            }
          });
          if (!newDimensionsItem.itemsDs) {
            newDimensionsItem.itemsDs = new DataSet(DetailTableDS({}));
          }
          if (!newDimensionsItem.dimensionDetailDs) {
            const dimensionDetailDs = new DataSet(dimensionTableDS());
            dimensionDetailDs.loadData([dimensionTableDsDataItem]);
            newDimensionsItem.dimensionDetailDs = dimensionDetailDs;
            newCollapseActiveKeys[item.key].push(`${dimensionTableDsDataItem.uuid}`);
          }
          newDimensions.push(newDimensionsItem);
        });
        item.dimensions = newDimensions;
        item.inspectionItemBasisDs.forEach(record => {
          record.init('inspectionDimensionObject', null);
          record.init('inspectionItemObject', null);
        });
      }
      _tabPanes.push(item);
    });
    setCollapseActiveKeys(newCollapseActiveKeys);
    setInspectSchemeLines(_tabPanes);
    dimensionTableDs.loadData([]);
  };

  // 检验维度tab点击事件和新增检验维度
  const addActiveTabDimension = type => {
    const _dimensions: Array<any> = [];
    let inspectionItemBasisDs;

    inspectSchemeLines.forEach(item => {
      if (activeKey === item.key) {
        inspectionItemBasisDs = item.inspectionItemBasisDs;
        item.dimensions.forEach(dimensionsItem => {
          const { dimensionDetailDs } = dimensionsItem;
          _dimensions.push(dimensionDetailDs.toData()[0]);
        });
      }
    });

    dimensionTableDs.loadData(_dimensions);
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title:
        type === 'new'
          ? intl.get(`${modelPrompt}.addInspectionDimension`).d('新增检验维度')
          : intl.get(`${modelPrompt}.editInspectionDimension`).d('编辑检验维度'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      footer: (okBtn, cancelBtn) => {
        return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
      },
      onOk: addActiveTabDimensionHandleOk,
      children: (
        <DimensionTableListDrawer
          customizeTable={customizeTable}
          modelPrompt={modelPrompt}
          formDs={formDs}
          inspectionItemBasisDs={inspectionItemBasisDs}
          tableDs={dimensionTableDs}
          canEdit={canEdit}
          path={path}
        />
      ),
    });
  };

  const handleCopyBusinessTypeCopyDone = () => {
    const {
      originInspectBusinessType,
      targetInspectBusinessType,
      targetInspectBusinessTypeDesc,
    }: any = copyBusinessTypeDs.toData()[0];

    const allInspectBusinessType: Array<any> = [targetInspectBusinessType];

    const inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());

    const dimensions: any = [];

    const newItemDimensionsUuid: any = [];

    inspectSchemeLines.forEach(item => {
      const inspectionItemBasisDsData = item.inspectionItemBasisDs.toData()[0];
      item.inspectionItemBasisDs.forEach(record => {
        if (record.get('inspectBusinessType')) {
          allInspectBusinessType.push(record.get('inspectBusinessType'));
        }
      });

      if (inspectionItemBasisDsData.inspectBusinessType === originInspectBusinessType) {
        inspectionItemBasisDs.loadData([
          {
            ...inspectionItemBasisDsData,
            inspectBusinessTypeObject: {
              inspectBusinessType: targetInspectBusinessType,
              inspectBusinessTypeDesc: targetInspectBusinessTypeDesc,
            },
            inspectBusinessType: targetInspectBusinessType,
            inspectBusinessTypeDesc: targetInspectBusinessTypeDesc,
            inspectSchemeTmpId: null,
            inspectSchmLineTmpId: null,
          },
        ]);

        if (item?.dimensions?.length > 0) {
          item.dimensions.forEach(dimensionsItem => {
            let _itemsDsData = dimensionsItem.itemsDs.toData();
            const _itemsDsDataIdMap: any = {};
            _itemsDsData.forEach(itemsDsItem => {
              _itemsDsDataIdMap[itemsDsItem.inspectObjectItemTmpId] = uuid();
            });
            _itemsDsData = _itemsDsData.map(itemsDsItem => {
              const oldId = itemsDsItem.inspectObjectItemTmpId;
              const newItemsDsItem = { ...itemsDsItem };
              const { formulaId, formulaCode, formulaName, dimension, formulaList } = itemsDsItem;
              if (itemsDsItem.dataType === 'CALCULATE_FORMULA') {
                const newFormulaList = (formulaList || []).map((formulaListItem: any) => {
                  if (formulaListItem) {
                    return {
                      fieldCode: formulaListItem.fieldCode,
                      fieldName: formulaListItem.fieldName,
                      inspectItemId: _itemsDsDataIdMap[formulaListItem.inspectItemId],
                      inspectItemDesc: formulaListItem.inspectItemDesc,
                      isRequired: formulaListItem.isRequired,
                    };
                  }
                  return {};
                });
                newItemsDsItem.formulaList = newFormulaList;
                newItemsDsItem.formula = JSON.stringify({
                  formulaId,
                  formulaCode,
                  formulaName,
                  dimension,
                  formulaList: newFormulaList,
                });
              }
              return {
                ...newItemsDsItem,
                inspectObjectDmsTmpId: null,
                inspectSchmLineTmpId: null,
                inspectObjectItemTmpId: null,
                inspectionItemRowUuid: _itemsDsDataIdMap[oldId],
              };
            });
            const _dimensionDetailDsData = dimensionsItem.dimensionDetailDs.toData()[0];
            const itemsDs = new DataSet(DetailTableDS({}));
            const dimensionDetailDs = new DataSet(dimensionTableDS());
            const newUuid = uuid();
            newItemDimensionsUuid.push(`${newUuid}`);
            dimensions.push({
              itemsDs: itemsDs.loadData(_itemsDsData),
              dimensionDetailDs: dimensionDetailDs.loadData([
                {
                  ..._dimensionDetailDsData,
                  inspectObjectDmsTmpId: null,
                  uuid: newUuid,
                },
              ]),
            });
          });
        }
      }
    });

    inspectSchemeLines.forEach(item => {
      item.inspectionItemBasisDs.forEach(record => {
        record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
      });
    });

    inspectionItemBasisDs.forEach(record => {
      record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
    });

    setInspectSchemeLines([
      ...inspectSchemeLines,
      {
        key: `${newTabIndex + 1}`,
        inspectionItemBasisDs,
        dimensions,
      },
    ]);

    const newCollapseActiveKeys = { ...collapseActiveKeys };
    newCollapseActiveKeys[`${newTabIndex + 1}`] = [...newItemDimensionsUuid];
    setCollapseActiveKeys(newCollapseActiveKeys);

    setActiveKey(`${newTabIndex + 1}`);
    setNewTabIndex(newTabIndex + 1);
  };

  const handleCopyBusinessType = async () => {
    const originResult = await fetchRuleDtl.run({
      params: {
        inspectBusinessType: copyBusinessTypeDs.current?.get('originInspectBusinessType'),
        siteId: copyBusinessTypeDs.current?.get('siteId'),
      },
    });
    const targetResult = await fetchRuleDtl.run({
      params: {
        inspectBusinessType: copyBusinessTypeDs.current?.get('targetInspectBusinessType'),
        siteId: copyBusinessTypeDs.current?.get('siteId'),
      },
    });

    if (typeof originResult?.rows === 'object' && typeof targetResult?.rows === 'object') {
      const originResultJsonString = JSON.stringify(originResult);
      const targetResultJsonString = JSON.stringify(targetResult);
      if (originResultJsonString === targetResultJsonString) {
        handleCopyBusinessTypeCopyDone();
        return true;
      }
      notification.error({
        message: intl
          .get(`${modelPrompt}.copyBusinessTypeError`)
          .d('来源和目标的检验维度不一致，不能复制！'),
      });
      return false;
    }
  };

  const copyBusinessType = () => {
    const originInspectBusinessTypes: any = [];
    inspectSchemeLines.forEach(tabItem => {
      const {
        inspectBusinessType,
        inspectBusinessTypeDesc,
      } = tabItem.inspectionItemBasisDs.toData()[0];
      if (inspectBusinessType && inspectBusinessTypeDesc) {
        // @ts-ignore
        originInspectBusinessTypes.push({
          inspectBusinessType,
          inspectBusinessTypeDesc,
        });
      }

      copyBusinessTypeDs.loadData([
        {
          siteId: formDs.current?.get('siteId'),
          inspectBusinessTypes: originInspectBusinessTypes.map(item => {
            return item.inspectBusinessType;
          }),
        },
      ]);
    });

    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.copyBusinessType`).d('复制检验业务类型'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: handleCopyBusinessType,
      children: (
        <Form dataSet={copyBusinessTypeDs} columns={1} labelWidth={112} disabled={!canEdit}>
          <Select name="originInspectBusinessTypeObject" colSpan={2}>
            {originInspectBusinessTypes.map(originInspectBusinessType => (
              <Option value={originInspectBusinessType.inspectBusinessType}>
                {originInspectBusinessType.inspectBusinessTypeDesc}
              </Option>
            ))}
          </Select>
          <Lov autoSelectSingle={false} noCache name="targetInspectBusinessTypeObject" />
        </Form>
      ),
    });
  };

  const batchAddInspectionDimension = () => {
    inspectSchemeLines.forEach(item => {
      if (activeKey === item.key) {
        item.inspectionItemBasisDs.current.set('inspectionDimensionObject', []);
        batchAddInspectionDimensionModal = Modal.open({
          key: Modal.key(),
          title: intl.get(`${modelPrompt}.batchAddInspectionDimension`).d('批量新增检验项目'),
          destroyOnClose: true,
          style: {
            width: 360,
          },
          okCancel: false,
          okText: intl.get(`${modelPrompt}.close`).d('关闭'),
          children: (
            <Form
              dataSet={item.inspectionItemBasisDs}
              columns={1}
              labelWidth={112}
              disabled={!canEdit}
            >
              <Select name="inspectionDimensionObject" colSpan={2}>
                {item.dimensions &&
                  item.dimensions.map(dimensionsItem => (
                    <Option value={selectOptionValue(dimensionsItem)}>
                      {selectOptionFormat(dimensionsItem)}
                    </Option>
                  ))}
              </Select>
              <Lov
                autoSelectSingle={false}
                name="inspectionItemObject"
                noCache
                onChange={value => {
                  handleInspectionItemObjectChange(value, item, false);
                }}
                modalProps={{
                  footer: (okBtn, cancelBtn, modal) => {
                    return [
                      <Button
                        onClick={() => {
                          handleCreateInspection(modal, item);
                        }}
                      >
                        {intl.get(`${modelPrompt}.inspectSchemeObjectCreate`).d('新建检验项目')}
                      </Button>,
                      cancelBtn,
                      okBtn,
                    ];
                  },
                }}
                tableProps={{
                  queryFieldsLimit: 10,
                }}
              />
            </Form>
          ),
        });
      }
    });
  };

  // 检验方案信息变更事件
  const handleFormDsValueChange = (name, value) => {
    const _tabPanes: any = [];
    inspectSchemeLines.forEach(item => {
      if (item.dimensions) {
        item.dimensions.forEach(dimensionsItem => {
          const { dimensionDetailDs } = dimensionsItem;
          dimensionDetailDs.forEach(record => {
            if (name === 'siteObject') {
              record.set('siteId', value?.siteId || null);
              record.set('materialCode', null);
              record.set('materialName', null);
              record.set('materialId', null);
              record.set('revisionCode', null);
              record.set('areaObject', null);
              record.set('prodLineObject', null);
              record.set('processObject', null);
              record.set('stationObject', null);
              record.set('equipmentObject', null);
              record.set('operationObject', null);
              record.set('supplierObject', null);
              record.set('customerObject', null);
              record.set('otherObject', null);
            }
          });
        });
      }
      if (name === 'siteObject') {
        item.dimensions = [item.dimensions[0]];
        item.inspectionItemBasisDs.forEach(record => {
          record.init('inspectBusinessTypeObject', null);
          record.init('siteId', value?.siteId || null);
          record.init('inspectBusinessTypeObjectIgnore', '');
        });
      }
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 检验对象类型修改触发
  const handleInspectSchemeObjectTypeObjectChange = () => {
    const formDsData: any = formDs.toData()[0] || {};
    const _tabPanes: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      const _dimensions: Array<any> = [];

      if (item.dimensions && item.dimensions.length > 0) {
        const { uuid: oldUuid } = item.dimensions[0].dimensionDetailDs.toData()[0];
        const dimensionDetailDs = new DataSet(dimensionTableDS());
        const newDsData: any = {
          uuid: oldUuid,
          sequence: 10,
          siteId: formDsData.siteId,
        };

        dimensionDetailDs.loadData([newDsData]);
        const _item = {
          itemsDs: item.dimensions[0].itemsDs,
          dimensionDetailDs,
        };
        _dimensions.push(_item);
      }
      item.dimensions = _dimensions;
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 重置 tab下Dimensions里的inspectionItemBasisDs数据
  const handleResetDimensionsDs = keepModal => {
    const _tabPanes: Array<any> = [];
    const formDsData: any = formDs.toData()[0];
    inspectSchemeLines.forEach(item => {
      if (item.inspectionItemBasisDs) {
        item.inspectionItemBasisDs.forEach(record => {
          if (!keepModal) {
            record.init('inspectionDimensionObject', null);
          }
          record.init('inspectionItemObject', null);
          record.init('inspectSchemeObjectType', formDsData.inspectSchemeObjectType);
        });
      }
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 维度排序
  const sortList = (type, index) => {
    const _tabPanes: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      if (item.key === activeKey) {
        const _dimensions = item.dimensions;
        if (type === 'up' && index > 0) {
          const spliceItem = _dimensions.splice(index, 1);
          _dimensions.splice(index - 1, 0, spliceItem[0]);
        }
        if (type === 'down' && index < _dimensions.length) {
          const spliceItem = _dimensions.splice(index, 1);
          _dimensions.splice(index + 1, 0, spliceItem[0]);
        }
        _dimensions.forEach((dimensionsItem, dimensionsItemIndex) => {
          const { dimensionDetailDs } = dimensionsItem;
          dimensionDetailDs.forEach(record => {
            record.set('sequence', dimensionsItemIndex * 10 + 10);
          });
        });
        item.dimensions = _dimensions;
        _tabPanes.push(item);
        item.inspectionItemBasisDs.forEach(record => {
          record.init('inspectionDimensionObject', null);
          record.init('inspectionItemObject', null);
        });
      } else {
        _tabPanes.push(item);
      }
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 附件配置
  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-item-maintain',
    accept: [
      '.doc',
      '.ppt',
      '.docx',
      '.xlsx',
      '.xls',
      '.deb',
      '.txt',
      '.pdf',
      'image/*',
      'video/*',
    ],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  // 检验维度值
  const selectOptionValue = dimensionsItem => {
    const { dimensionDetailDs } = dimensionsItem;
    const dimensionsItemData = dimensionDetailDs.toData()[0] || {};
    return dimensionsItemData.sequence;
  };

  // 检验维度和维度tabtitle格式化
  const selectOptionFormat = dimensionsItem => {
    const { dimensionDetailDs } = dimensionsItem;
    const dimensionsItemData = dimensionDetailDs.toData()[0] || {};
    const inspectSchemeObjectTypeDesc =
      formDs.current?.get('inspectSchemeObjectTypeDesc') ||
      intl.get(`${modelPrompt}.inspectSchemeObjectType`).d('检验对象类型');

    const inspectSchemeObjectType = formDs.current?.get('inspectSchemeObjectType');

    let frontText = '';
    const titleMapKeys = Object.keys(titleMap);
    titleMapKeys.forEach(key => {
      if (dimensionsItemData[key]) {
        frontText += ` + ${titleMap[key].text}${dimensionsItemData[titleMap[key].valueKey]}`;
      }
    });

    if (inspectSchemeObjectType === 'MATERIAL' || inspectSchemeObjectType === 'OPERATION') {
      return `${inspectSchemeObjectTypeDesc}${frontText}`;
    }
    return `${inspectSchemeObjectTypeDesc}`;
  };

  // 新建检验维度选中值后触发
  const handleInspectionItemObjectChange = (value, tabItem, keepModal) => {
    if (!value) {
      return;
    }

    tabItem.dimensions.forEach(dimensionsItem => {
      const { dimensionDetailDs } = dimensionsItem;
      const dimensionsItemData = dimensionDetailDs.toData()[0] || {};

      const inspectionItemBasisDsData = tabItem.inspectionItemBasisDs.toData()[0];
      if (inspectionItemBasisDsData.inspectionDimension.indexOf(dimensionsItemData.sequence) > -1) {
        const { itemsDs } = dimensionsItem;

        // 已有数据转为对象进行对比
        const inDsCodeMap: Array<any> = [];
        let sequence = 1;
        itemsDs.forEach(recordItem => {
          const recordItemData = recordItem.toData();
          inDsCodeMap.push(recordItemData.inspectItemCode);
          if (recordItemData?.sequence > sequence) {
            sequence = recordItemData.sequence;
          }
        });
        sequence = (Math.floor(sequence / 10) + 1) * 10;
        // 取得的value值还需要再处理
        value.forEach(valueItem => {
          const _valueItem = { ...valueItem };

          _valueItem.requiredFlag = _valueItem.requiredFlag || 'Y';
          _valueItem.destructiveExperimentFlag = _valueItem.destructiveExperimentFlag || 'N';
          _valueItem.outsourceFlag = _valueItem.outsourceFlag || 'N';

          if (['TEXT', 'DECISION_VALUE'].includes(_valueItem.dataType)) {
            _valueItem.trueValue =
              (_valueItem.trueValueList || []).length > 0
                ? _valueItem.trueValueList[0].dataValue
                : null;
            _valueItem.falseValue =
              (_valueItem.falseValueList || []).length > 0
                ? _valueItem.falseValueList[0].dataValue
                : null;
          }
          if (_valueItem.dataType === 'VALUE_LIST') {
            _valueItem.trueValue =
              (_valueItem.trueValueList || []).length > 0
                ? _valueItem.trueValueList.map(trueItem => trueItem.dataValue)
                : null;
            _valueItem.falseValue =
              (_valueItem.falseValueList || []).length > 0
                ? _valueItem.falseValueList.map(falseItem => falseItem.dataValue)
                : null;
          }
          if (inDsCodeMap.indexOf(valueItem.inspectItemCode) === -1) {
            const newRecord = itemsDs.create({
              ..._valueItem,
              inspectionItemRowUuid: uuid(),
              sequence,
            });
            if (!newRecord.get('samplingMethodId')) {
              newRecord.init('samplingMethodCode', inspectionItemBasisDsData.samplingMethodCode);
              newRecord.init('samplingMethodDesc', inspectionItemBasisDsData.samplingMethodDesc);
              newRecord.init('samplingMethodId', inspectionItemBasisDsData.samplingMethodId);
            }
            newRecord.init('samplingDimension', inspectionItemBasisDsData.samplingDimension);

            sequence += 10;
            getNewUuid(newRecord);
          }
        });
      }
      // batchAddInspectionDimensionModal.close();
    });
    handleResetDimensionsDs(keepModal);
    // @ts-ignore
    notification.success();
    upDateStatisticsTableRef();
  };

  // tab下检验业务类型修改
  const handleInspectBusinessTypeObjectChange = key => {
    const allInspectBusinessType: Array<any> = [];
    inspectSchemeLines.forEach(item => {
      item.inspectionItemBasisDs.forEach(record => {
        if (record.get('inspectBusinessType')) {
          allInspectBusinessType.push(record.get('inspectBusinessType'));
        }
      });
    });

    const _tabPanes: any = [];

    inspectSchemeLines.forEach(item => {
      if (item.key === key) {
        if (item.dimensions) {
          item.dimensions.forEach(dimensionsItem => {
            const { dimensionDetailDs } = dimensionsItem;
            dimensionDetailDs.forEach(record => {
              record.set('areaObject', null);
              record.set('prodLineObject', null);
              record.set('processObject', null);
              record.set('stationObject', null);
              record.set('equipmentObject', null);
              record.set('operationObject', null);
              record.set('supplierObject', null);
              record.set('customerObject', null);
              record.set('otherObject', null);
            });
          });
        }
        item.inspectionItemBasisDs.current.set(
          'inspectBusinessTypeObjectIgnore',
          allInspectBusinessType,
        );
        item.inspectionItemBasisDs.forEach(record => {
          record.set('inspectBusinessTypeObjectIgnore', allInspectBusinessType.join(','));
        });
        item.dimensions = [item.dimensions[0]];
      }
      _tabPanes.push(item);
    });
    setInspectSchemeLines(_tabPanes);
  };

  // 抽样方式变更
  const handleSamplingMethodLovChange = (value, dimensions) => {
    dimensions.forEach(dimensionsItem => {
      const { itemsDs } = dimensionsItem;
      itemsDs.forEach(record => {
        record.set('samplingMethodLov', value);
      });
    });
  };

  // 抽样维度变更
  const handleSamplingDimensionChange = (value, changeDs, dimensions) => {
    changeDs.forEach(record => {
      record.set('samplingMethodLov', null);
    });
    dimensions.forEach(dimensionsItem => {
      const { itemsDs } = dimensionsItem;
      itemsDs.forEach(record => {
        record.set('samplingDimension', value);
        record.set('samplingMethodLov', null);
      });
    });
  };

  // 分配检验维度后复制附件并获得uuid
  const getNewUuid = async record => {
    const enclosure = record.get('enclosure');
    if (!enclosure) {
      return Promise.resolve(true);
    }
    await myInstance
      .post(`hfle/v1/${tenantId}/files/copy-file`, { uuidList: [enclosure] })
      .then(res => {
        if (res && res.data && res.data[enclosure]) {
          record.init('enclosure', res.data[enclosure]);
        }
      });
    return Promise.resolve(true);
  };

  const getInspectItemDetail = async (id, item) => {
    const inspectItemDetail = await mtInspectItemDetail.run({
      params: { inspectItemId: id },
    });
    if (inspectItemDetail?.success && inspectItemDetail?.rows) {
      handleInspectionItemObjectChange([inspectItemDetail.rows], item, 'keep');
    }
  };

  // 新建检验项目点击确定的回调函数
  const handleCreateInspectionCallback = async item => {
    const { success, rows } = await childRef.current?.submit(false);
    if (success) {
      getInspectItemDetail(rows, item);
      if (batchAddInspectionDimensionModal) {
        batchAddInspectionDimensionModal.close();
      }
      return Promise.resolve(true);
    }
    return Promise.resolve(false);
  };

  // 新建检验项目点击确定的回调函数
  const handleCreateInspectionCallbackKeep = async item => {
    const { success, rows } = await childRef.current?.submit(true);
    if (success) {
      getInspectItemDetail(rows, item);
      return Promise.resolve(false);
    }
    return Promise.resolve(false);
  };

  const handleCreateInspection = (_modal, item) => {
    _modal.close();
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.inspectSchemeObjectCreate`).d('新建检验项目'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      onOk: () => {
        return handleCreateInspectionCallback(item);
      },
      footer: (okBtn, cancelBtn) => {
        return [
          cancelBtn,
          <Button
            onClick={() => {
              return handleCreateInspectionCallbackKeep(item);
            }}
          >
            {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并新建下一条')}
          </Button>,
          okBtn,
        ];
      },
      children: (
        <DetailComponent
          ref={childRef}
          canEdit={canEdit}
          kid="create"
          column={3}
          customizeForm={customizeForm}
          requiredField={[]}
          operationType="SCHEME_TMP_FUNC_CREATE"
        />
      ),
    });
  };

  // 折叠面板样式i
  const expandIconButton = panelProps => {
    if (panelProps.isActive) {
      return <Icon type="expand_less" />;
    }
    return <Icon type="expand_more" />;
  };

  const upDateStatisticsTableRef = () => {
    if (statisticsTableRef.current) {
      statisticsTableRef.current.updateTable();
    }
  };

  const coverInspectSchemeLines = newData => {
    const inspectItemDrawerDsDataMap: any = {};
    newData.forEach((item: any) => {
      const { inspectionItemRowUuid } = item;
      inspectItemDrawerDsDataMap[inspectionItemRowUuid] = { ...item };
    });

    inspectSchemeLines.forEach(tabPaneItem => {
      const { dimensions } = tabPaneItem;
      dimensions.forEach(dimensionsItem => {
        const { itemsDs } = dimensionsItem;
        if (itemsDs) {
          // 检验项目维度列表 将所有tab下的维度列表record 提取插入临时数组 并记录对应报错信息
          itemsDs.forEach(record => {
            const inspectionItemRowUuid = record.get('inspectionItemRowUuid');
            if (inspectItemDrawerDsDataMap[inspectionItemRowUuid]) {
              const newData = inspectItemDrawerDsDataMap[inspectionItemRowUuid];
              const keys = Object.keys(newData) || [];
              keys.forEach(key => {
                if (_newIgnoreKeys.indexOf(key) === -1) {
                  record.set(key, newData[key]);
                }
              });
            }
          });
        }
      });
    });
  };

  // handleChangeCollapse
  const handleChangeCollapse = (value, key) => {
    const newCollapseActiveKeys = { ...collapseActiveKeys };
    if (newCollapseActiveKeys[key]) {
      newCollapseActiveKeys[key] = value;
    }
    setCollapseActiveKeys(newCollapseActiveKeys);
  };

  // 维度展开key
  const getCollapseActiveKey = key => {
    return collapseActiveKeys[key];
  };

  // 当前维度key
  const getPanelActiveKey = dimensionsItem => {
    let key = '';
    if (dimensionsItem?.dimensionDetailDs) {
      dimensionsItem?.dimensionDetailDs.forEach(record => {
        if (record.get('uuid')) {
          key = `${record.get('uuid')}`;
        }
      });
    }
    return key;
  };

  const handleInstantiationCallback = async releaseFlag => {
    instantiationModal.update({
      footer: () => {
        return instantiationButton(true);
      },
    });
    const formDsData: any = instantiationFormDs.toData()[0];
    if (instantiationTableDs.length === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.instantiationNotice`).d('请添加实例化对象'),
      });
      instantiationModal.update({
        footer: () => {
          return instantiationButton(false);
        },
      });
      return false;
    }
    const tableValidate = await instantiationTableDs.validate();

    const materialInfo: any = [];
    instantiationTableDs.forEach(record => {
      if (record.get('fullUpdate')) {
        const {
          fullUpdate,
          inspectSchemeObject,
          inspectSchemeObjectId,
          revisionCode,
        } = record.toData();
        materialInfo.push({
          fullUpdate,
          inspectSchemeObjectCode: inspectSchemeObject,
          inspectSchemeObjectId,
          revisionCode,
        });
      }
    });

    if (materialInfo.length === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.fullUpdateNotice`).d('请选择实例化方式'),
      });
      instantiationModal.update({
        footer: () => {
          return instantiationButton(false);
        },
      });
      return false;
    }

    if (!tableValidate) {
      instantiationModal.update({
        footer: () => {
          return instantiationButton(false);
        },
      });
      return false;
    }

    const params = {
      releaseFlag,
      inspectSchemeTmpId: formDsData.inspectSchemeTmpId,
      materialInfo,
      siteId: formDsData.siteId,
    };

    if (formDsData.inspectSchemeObjectType === 'MATERIAL') {
      const checkInspectSchemeTmpRes = await checkInspectSchemeTmp.run({
        params,
      });
      if (checkInspectSchemeTmpRes?.success) {
        if (checkInspectSchemeTmpRes?.rows) {
          let confirmResult;
          await Modal.confirm({
            title: intl.get(`tarzan.common.title.tips`).d('提示'),
            children: <div>{checkInspectSchemeTmpRes?.rows}</div>,
          }).then(button => {
            confirmResult = button;
          });
          if (confirmResult === 'ok') {
            return inspectSchemeTmpConfirm(params);
          }
          instantiationModal.update({
            footer: () => {
              return instantiationButton(false);
            },
          });
          return false;
        }
        return inspectSchemeTmpConfirm(params);
      }
      instantiationModal.update({
        footer: () => {
          return instantiationButton(false);
        },
      });
      return false;
    }
    return inspectSchemeTmpConfirm(params);
  };

  const inspectSchemeTmpConfirm = params => {
    return inspectSchemeTmp
      .run({
        params,
      })
      .then(res => {
        if (res?.success) {
          // @ts-ignore
          notification.success();
          instantiationFormDs.loadData([]);
          instantiationTableDs.loadData([]);
          instantiationModal.close();
        }
        instantiationModal.update({
          footer: () => {
            return instantiationButton(false);
          },
        });
        return false;
      })
      .catch(() => {
        instantiationModal.update({
          footer: () => {
            return instantiationButton(false);
          },
        });
        return false;
      });
  };

  const instantiationButton = loading => [
    <Button
      loading={loading}
      onClick={() => {
        handleInstantiationCallback('Y');
      }}
    >
      {intl.get(`${modelPrompt}.button.confirmReleaseFlag`).d('确定并发布')}
    </Button>,
    <Button
      color={ButtonColor.primary}
      loading={loading}
      onClick={() => {
        handleInstantiationCallback(undefined);
      }}
    >
      {intl.get('tarzan.common.button.confirm').d('确认')}
    </Button>,
  ];

  // 实例化
  const handleInstantiation = async () => {
    const checkAllInspectSchemeResult = await checkAllInspectScheme.run({
      params: {
        inspectSchemeTmpId: pageIdObject.id,
      },
    });
    const instantiationInfoResult = await instantiationInfo.run({
      params: {
        dinspectSchemeTmpId: pageIdObject.id,
        siteId: formDs.current?.get('siteId'),
      },
    });

    if (checkAllInspectSchemeResult.success && instantiationInfoResult.success) {
      if (checkAllInspectSchemeResult.rows && checkAllInspectSchemeResult.rows.length === 0) {
        instantiationTableDs.loadData(instantiationInfoResult.rows.content);
        const formDsData: any = formDs.toData()[0];
        instantiationFormDs.loadData([
          {
            inspectSchemeTmpId: formDsData.inspectSchemeTmpId,
            inspectSchemeObjectType: formDsData.inspectSchemeObjectType,
            siteId: formDsData.siteId,
            siteCode: formDsData.siteCode,
            siteName: formDsData.siteName,
          },
        ]);

        instantiationModal = Modal.open({
          ...drawerPropsC7n({
            canEdit: !canEdit,
          }),
          key: Modal.key(),
          title: intl.get(`${modelPrompt}.button.instantiation`).d('实例化'),
          destroyOnClose: true,
          style: {
            width: 720,
          },
          footer: () => {
            return instantiationButton(false);
          },
          children: (
            <InstantiationDrawer formDs={instantiationFormDs} tableDs={instantiationTableDs} />
          ),
        });
        return Promise.resolve();
      }
      if (checkAllInspectSchemeResult.rows && checkAllInspectSchemeResult.rows.length > 0) {
        Modal.warning({
          destroyOnClose: true,
          key: Modal.key(),
          title: intl.get(`tarzan.common.title.tips`).d('提示'),
          children: (
            <>
              {checkAllInspectSchemeResult.rows.map(item => (
                <p>{item}</p>
              ))}
            </>
          ),
        });

        return Promise.resolve();
      }
      // @ts-ignore
      notification.error();
      return Promise.resolve();
    }
    return Promise.resolve();
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.InspectionSchemeMaintenance`).d('检验方案模板维护')}
        backPath="/hwms/inspection-scheme-template-maintenance/list"
      >
        {canEdit && (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button onClick={handleSaveAndNext}>
              {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并新建下一条')}
            </Button>
            <Button
              disabled={pageIdObject.id === 'create' || pageIdObject.isCopy}
              onClick={handleSaveAnother}
            >
              {intl.get(`${modelPrompt}.button.saveAs`).d('另存为')}
            </Button>
          </>
        )}
        {!canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              onClick={() => {
                setCanEdit(prev => !prev);
              }}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              onClick={handleInstantiation}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.instantiation`).d('实例化')}
            </PermissionButton>
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={queryDetail.loading || saveDetail.loading}>
          <Collapse collapsible="icon" bordered={false} defaultActiveKey={['panel1', 'panel2']}>
            <Panel
              header={intl
                .get(`${modelPrompt}.inspectSchemeTmpInformationTab`)
                .d('检验方案模板信息')}
              key="panel1"
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.DETAIL`,
                },
                <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
                  <TextField name="inspectSchemeTmpCode" />
                  <Lov name="siteObject" />
                  <Switch name="enableFlag" />
                  <Select name="inspectSchemeObjectTypeObject" />
                  <TextField name="remark" />
                  <Attachment {...attachmentProps} />
                </Form>,
              )}
            </Panel>
            <Panel
              header={intl
                .get(`${modelPrompt}.inspectSchemeProjectDistributionTab`)
                .d('检验项目分配')}
              key="panel2"
            >
              <Tabs
                onChange={handleChangeTab}
                activeKey={activeKey}
                // @ts-ignore
                type={canEdit ? 'editable-card' : 'card'}
                onEdit={onTabsEdit}
                tabBarExtraContent={
                  <AddActiveTabDimensionButton
                    addInspectionDimension={addActiveTabDimension}
                    batchAddInspectionDimension={batchAddInspectionDimension}
                    copyBusinessType={copyBusinessType}
                    activeKey={activeKey}
                    canEdit={canEdit}
                    ds={formDs}
                    inspectSchemeLines={inspectSchemeLines}
                  />
                }
              >
                {inspectSchemeLines.map(item => (
                  <TabPane
                    tab={<TabsTabPaneTitle ds={item.inspectionItemBasisDs} />}
                    key={item.key}
                    forceRender
                  >
                    <>
                      {customizeForm(
                        {
                          code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.LINE_DETAIL`,
                        },
                        <Form
                          dataSet={item.inspectionItemBasisDs}
                          columns={3}
                          labelWidth={112}
                          disabled={!canEdit}
                        >
                          <Lov
                            autoSelectSingle={false}
                            name="inspectBusinessTypeObject"
                            onChange={() => {
                              handleInspectBusinessTypeObjectChange(item.key);
                            }}
                          />
                          <Select
                            name="samplingDimension"
                            onChange={value => {
                              handleSamplingDimensionChange(
                                value,
                                item.inspectionItemBasisDs,
                                item.dimensions,
                              );
                            }}
                          />
                          <Lov
                            name="samplingMethodLov"
                            onChange={value => {
                              handleSamplingMethodLovChange(value, item.dimensions);
                            }}
                          />
                        </Form>,
                      )}
                      {item.dimensions && (
                        <div
                          style={{
                            paddingLeft: '12px',
                          }}
                        >
                          <Collapse
                            collapsible="icon"
                            expandIcon={expandIconButton}
                            activeKey={getCollapseActiveKey(item.key)}
                            onChange={value => {
                              handleChangeCollapse(value, item.key);
                            }}
                          >
                            {(item.dimensions || []).map((dimensionsItem, dimensionsItemIndex) => {
                              return (
                                <Panel
                                  header={
                                    <CollapsePanelTitle
                                      ds={formDs}
                                      itemDs={item.inspectionItemBasisDs}
                                      index={dimensionsItemIndex}
                                      sortList={sortList}
                                      title={selectOptionFormat(dimensionsItem)}
                                      clickTitleCallback={addActiveTabDimension}
                                      activeKey={activeKey}
                                      canEdit={canEdit}
                                    />
                                  }
                                  key={getPanelActiveKey(dimensionsItem)}
                                  dataSet={dimensionsItem.itemsDs}
                                >
                                  <DimensionDetailTableList
                                    getNewUuid={getNewUuid}
                                    formDs={item.inspectionItemBasisDs}
                                    tableDS={dimensionsItem.itemsDs}
                                    path={path}
                                    canEdit={canEdit}
                                    customizeTable={customizeTable}
                                    customizeForm={customizeForm}
                                    statisticsTableUpdate={upDateStatisticsTableRef}
                                    item={item}
                                    dimensionsItem={dimensionsItem}
                                    selectOptionValue={selectOptionValue}
                                    selectOptionFormat={selectOptionFormat}
                                    handleCreateInspection={handleCreateInspection}
                                  />
                                </Panel>
                              );
                            })}
                          </Collapse>
                        </div>
                      )}
                    </>
                  </TabPane>
                ))}
              </Tabs>
            </Panel>
            <Panel
              header={intl
                .get(`${modelPrompt}.inspectSchemeProjectDistributionAllTab`)
                .d('检验项目分配总览')}
              key="panel3"
            >
              <StatisticsTable
                inspectSchemeLines={inspectSchemeLines}
                selectOptionFormat={selectOptionFormat}
                canEdit={canEdit}
                customizeTable={customizeTable}
                customizeForm={customizeForm}
                ref={statisticsTableRef}
                coverInspectSchemeLines={coverInspectSchemeLines}
              />
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [
    modelPrompt,
    'tarzan.qms.inspectGroupMaintenance',
    'tarzan.inspectionSchemeTemplate',
    'tarzan.hwms.inspectItemMaintain',
    'tarzan.common',
  ],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.LINE_DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.DMS_DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.ITEM_DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.ITEM_DRAWER`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_TMP_BASIC.ATTR`,
    ],
  })(InspectionSchemeTable as any),
);
