/**
 * @Description: 检验任务执行平台-详情界面DS
 */
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import moment from 'moment';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();
const modelPrompt = 'tarzan.inspectExecute.MeasureHavePlatform';

const detailDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'applicationDocId',
  fields: [
    {
      name: 'applicationDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationDocNum`).d('申请单号'),
      disabled: true,
    },
    {
      name: 'applicationDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_STATUS',
      lovPara: { tenantId },
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'siteName',
      lovPara: { tenantId },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'docType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
      lookupCode: 'YP.QIS.MS_APPLICATION_DOC_TYPE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('申请时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
      disabled: true,
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('申请人'),
      disabled: true,
      defaultValue: userInfo.realName,
    },
    {
      name: 'createdBy',
      defaultValue: userInfo.id,
    },
    {
      name: 'departmentName',
      type: FieldType.string,
      disabled: true,
      label: intl.get(`${modelPrompt}.departmentName`).d('申请部门'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'reviewByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewByName`).d('审批人'),
    },
    {
      name: 'reviewDate',
      label: intl.get(`${modelPrompt}.reviewDate`).d('审批时间'),
      type: FieldType.string,
    },
    {
      name: 'rejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/application/info/ui`,
        method: 'GET',
      };
    },
  },
});




const taskDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'toolLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编号'),
      lovCode: 'YP.QIS.MS_TOOL_DETAIL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet}) => ({
          tenantId,
          responId: userInfo.id,
          // @ts-ignore
          siteId: dataSet.parent.current.get('siteId'),
          docType: dataSet.parent?.current?.get('docType'),
        }),
      },
      required: true,
    },
    {
      name: 'toolCode',
      bind: 'toolLov.toolCode',
    },
    {
      name: 'speciesName',
      bind: 'toolLov.speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
    },
    {
      name: 'msToolManageId',
      bind: 'toolLov.msToolManageId',
    },
    // {
    //   name: 'msToolManageId',
    //   bind: 'toolLov.msToolManageId',
    // },
    {
      name: 'modelCode',
      bind: 'toolLov.modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
    },
    {
      name: 'modelName',
      bind: 'toolLov.modelName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelName`).d('型号描述'),
    },
    {
      name: 'lastVerificationDate',
      bind: 'toolLov.lastVerificationDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.lastVerificationDate`).d('上次检定日期'),
    },
    {
      name: 'verificationPeriod',
      bind: 'toolLov.verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检定周期'),
    },
    {
      name: 'usingStatus',
      bind: 'toolLov.usingStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingStatus`).d('量具使用状态'),
      lookupCode: 'QIS.MS_TOOL_USING_STATUS',
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('检定状态'),
      lookupCode: 'QIS.MS_TOOL_VRFCT_STATUS',
    },
    {
      name: 'verificationMethod',
      bind: 'toolLov.verificationMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
      lookupCode: 'QIS.MS_VRFCT_METHOD',
    },
    {
      name: 'prodLineName',
      bind: 'toolLov.prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
    },
    {
      name: 'processName',
      bind: 'toolLov.processName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processName`).d('工序'),
    },
    {
      name: 'otherPosition',
      bind: 'toolLov.otherPosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherPosition`).d('其他位置'),
      lookupCode: 'YP.QIS.MANAGE_OTHER_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'manufacturingNum',
      bind: 'toolLov.manufacturingNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manufacturingNum`).d('出厂编号'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('测量异常附件'),
      bucketName: 'qms',
      dynamicProps: {
        required: ({ dataSet }) => {
          return dataSet.parent?.current?.get('docType') === '2';
        },
      },
    },
  ],
});

const rejectReasonDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'rejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
    },
  ],
});

export { detailDS, taskDS, rejectReasonDS };
