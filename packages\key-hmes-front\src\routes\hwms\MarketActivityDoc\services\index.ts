/*
 * @Description: 市场活动单-service
 * @Author: <<EMAIL>>
 * @Date: 2023-09-18 18:04:36
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-19 21:50:01
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

/**
 * 保存市场活动单
 * @function SaveMarketActivity
 * @returns {object} fetch Promise
 */
export function SaveMarketActivity(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-activitys/save/ui`,
    method: 'POST',
  };
}

/**
 * 保存进度编辑信息
 * @function SaveProgressInfo
 * @returns {object} fetch Promise
 */
export function SaveProgressInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-activitys/progress/editing`,
    method: 'POST',
  };
}

/**
 * 审核市场活动单
 * @function ReviewMarketActivity
 * @returns {object} fetch Promise
 */
export function ReviewMarketActivity(marketActivityId = undefined) {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-activitys/send-message/oa?marketActivityId=${marketActivityId}`,
    method: 'POST',
  };
}

/**
 * 查询当前用户的员工信息
 * @function QueryUserInfo
 * @returns {object} fetch Promise
 */
export function QueryUserEmployeeInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/unit-position/by-user`,
    method: 'GET',
  };
}
