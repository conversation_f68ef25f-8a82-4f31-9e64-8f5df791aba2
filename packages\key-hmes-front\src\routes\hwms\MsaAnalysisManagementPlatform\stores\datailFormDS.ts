import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
// import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

const headFormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  fields: [
    {
      name: 'msaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaCode`).d('MSA编号'),
      disabled: true,
    },
    {
      name: 'msaStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaStatus`).d('状态'),
      lookupCode: 'YP.QIS.MSA_STATUS',
      defaultValue: 'NEW',
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'msaType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaType`).d('MSA类型'),
      lookupCode: 'YP.QIS.MSA_TYPE',
      defaultValue: '1',
      disabled: true,
    },
    {
      name: 'modelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model`).d('量具型号'),
      lovCode: 'YP.QIS.MS_TOOL_MSA_REL',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'modelCode',
      dynamicProps: {
        required: ({ record }) => (record.get('msaStatus') === "NEW"),
        disabled: ({ record }) => (record.get('msaStatus') !== 'NEW'),
      },
    },
    {
      name: 'modelCode',
      bind: 'modelLov.modelCode',
    },
    {
      name: 'modelId',
      bind: 'modelLov.modelId',
    },
    {
      name: 'modelName',
      bind: 'modelLov.modelName',
      disabled: true,
      label: intl.get(`${modelPrompt}.modelName`).d('量具型号名称'),
    },
    {
      name: 'qualityCharacteristic',
      bind: 'modelLov.qualityCharacteristic',
      disabled: true,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'speciesCode',
      bind: 'modelLov.speciesCode',
    },
    {
      name: 'speciesName',
      bind: 'modelLov.speciesName',
      disabled: true,
      label: intl.get(`${modelPrompt}.measuringToolSpecies`).d('量具种别'),
    },
    {
      name: 'msaAnalysisMethod',
      bind: 'modelLov.msaAnalysisMethod',
    },
    {
      name: 'msaAnalysisMethodDesc',
      bind: 'modelLov.msaAnalysisMethodDesc',
      disabled: true,
      label: intl.get(`${modelPrompt}.msaAnalysisMethod`).d('MSA分析方法'),
      multiple: ',',
    },
    {
      name: 'completeTimeLimit',
      disabled: true,
      bind: 'modelLov.completeTimeLimit',
      label: intl.get(`${modelPrompt}.completeTimeLimit`).d('完成时限'),
    },
    {
      name: 'toolCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编码'),
      lovCode: 'YP.QIS.ALL_MT_TOOL',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'toolCode',
      dynamicProps: {
        required: ({ record }) => (record.get('msaStatus') === 'NEW'),
        disabled: ({ record }) => (record.get('msaStatus') !== 'NEW'),
        lovPara: ({ record }) => ({
          tenantId,
          modelId: record?.get('modelId'),
        }),
      },
    },
    {
      name: 'toolCode',
      bind: 'toolCodeLov.toolCode',
    },
    {
      name: 'msToolManageId',
      bind: 'toolCodeLov.msToolManageId',
    },
    {
      name: 'processLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'processId',
      bind: 'processLov.workcellId',
    },
    {
      name: 'workcellName',
      bind: 'processLov.workcellName',
    },
    {
      name: 'responName',
      label: intl.get(`${modelPrompt}.responName`).d('量具责任人'),
      disabled: true,
      bind: 'toolCodeLov.responName',
    },
    {
      name: 'responId',
      bind: 'toolCodeLov.responId',
    },
    {
      name: 'onlineFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onlineFlag`).d('是否在线'),
      lookupCode: 'YP.QIS.ONLINE_FLAG',
      dynamicProps: {
        required: ({ record }) => (record.get('msaStatus') === 'NEW'),
        disabled: ({ record }) => (record.get('msaStatus') !== 'NEW'),
      },
    },
    {
      name: 'specialCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialCharacteristicsOrNot`).d('是否特殊特性'),
      lookupCode: 'YP.QIS.SPECIAL_CHARACTERISTIC_FLAG',
      dynamicProps: {
        required: ({ record }) => (record.get('msaStatus') === 'NEW'),
        disabled: ({ record }) => (record.get('msaStatus') !== 'NEW'),
      },
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
      dynamicProps: {
        disabled: ({ record }) => (record.get('msaStatus') !== 'NEW'),
      },
    },
    {
      name: 'projectStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
      lookupCode: 'YP.QIS.PROJECT_STAGE',
      dynamicProps: {
        required: ({ record }) => (record.get('msaStatus') === 'NEW'),
        disabled: ({ record }) => (record.get('msaStatus') !== 'NEW'),
      },
    },
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planstartTime`).d('计划开始时间'),
      dynamicProps: {
        required: ({ record }) => (record.get('msaStatus') === 'NEW'),
        disabled: ({ record }) => (record.get('msaStatus') !== 'NEW'),
      },
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
      disabled: true,
    },
    {
      name: 'analyzedByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.analyzeBy`).d('MSA分析人'),
      lovCode: 'YP.QIS.UNIT_USER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'realName',
      dynamicProps: {
        required: ({ record }) => (record.get('msaStatus') === 'NEW'),
        disabled: ({ record }) => (record.get('msaStatus') !== 'NEW'),
      },
    },
    {
      name: 'analyzedBy',
      bind: 'analyzedByLov.id',
    },
    {
      name: 'analyzedByDesc',
      bind: 'analyzedByLov.realName',
    },
    {
      name: 'assistantByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.assistantBy`).d('协助人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      dynamicProps: {
        disabled: ({ record }) => (record.get('msaStatus') !== 'NEW'),
      },
    },
    {
      name: 'assistantBy',
      bind: 'assistantByLov.userId',
    },
    {
      name: 'assistantByDesc',
      bind: 'assistantByLov.realName',
    },
    {
      name: 'sipFileUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.sipFileUuid`).d('SIP附件'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'sourceMsaTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceMsaTaskCode`).d('来源MSA编码'),
      disabled: true,
    },
    {
      name: 'improveLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.improveBy`).d('改善负责人'),
      lovCode: 'YP.QIS.UNIT_USER',
      ignore: FieldIgnore.always,
      textField: 'realName',
      dynamicProps: {
        disabled: ({ record }) => (record.get('msaStatus') !== 'TO_IMPROVE'),
        required: ({ record }) => (record.get('msaStatus') === 'TO_IMPROVE'),
      },
    },
    {
      name: 'improveBy',
      bind: 'improveLov.id',
    },
    {
      name: 'improveByDesc',
      bind: 'improveLov.realName',
    },
    {
      name: 'improvementFileUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.improvementFileUuid`).d('改善措施附件'),
    },
    {
      name: 'cause',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cause`).d('原因分析'),
      dynamicProps: {
        disabled: ({ record }) => (record.get('msaStatus') !== 'TO_IMPROVE'),
      },
    },
    {
      name: 'effectVerification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.effectVerification`).d('效果验证'),
      dynamicProps: {
        disabled: ({ record }) => (record.get('msaStatus') !== 'TO_IMPROVE'),
      },
    },
  ],
});

export { headFormDS };
