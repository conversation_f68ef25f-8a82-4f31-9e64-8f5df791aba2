/**
 * @Description: 成本中心内部订单维护 - 列表页
 * @Author: <EMAIL>
 * @Date: 2022/6/9 11:30
 * @LastEditTime: 2023-05-18 16:35:34
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useEffect, useState } from 'react';
import {
  Button,
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  Modal,
  Select,
  Table,
  TextField,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { AttributeDrawer } from '@components/tarzan-ui';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import { drawerDS, listDS } from '../stories/EntranceDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inLibrary.costOrderMaintain';

const CostOrderList = props => {
  const {
    listDs,
    // match: { path },
    customizeForm,
    customizeTable,
  } = props;
  let _createDrawer;
  let drawerDs: DataSet = useMemo(() => new DataSet(drawerDS()), []);
  const [currentType, setCurrentType] = useState(undefined); // 记录之前的结算类别
  const { run: handleSave } = useRequest(
    {
      url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-costcenter-order/insert?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.COST_CENTER_LIST.CREATE`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (listDs.queryDataSet) {
      const handler = flag
        ? listDs.queryDataSet.addEventListener
        : listDs.queryDataSet.removeEventListener;
      handler.call(listDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    if (record.get('accountType') && record.get('accountType') !== currentType) {
      record!.init('accountCategory', undefined);
      setCurrentType(record.get('accountType'));
    }
  };

  // 订单维护抽屉中-保存
  const saveCostOrder = async () => {
    drawerDs.current!.set({ nowData: new Date() });
    const validate = await drawerDs.validate();
    if (validate) {
      _createDrawer.update({
        footer: handleFooter(true),
      });
      return handleSave({
        params: {
          ...drawerDs.toData()[0],
        },
      }).then(res => {
        if (res && res.success) {
          notification.success({});
          _createDrawer.update({
            footer: handleFooter(false),
          });
          _createDrawer.close();
          listDs.query();
        } else {
          _createDrawer.update({
            footer: handleFooter(false),
          });
        }
      });
    }
  };

  // 控制抽屉footer的更新
  const handleFooter = (val: boolean) => {
    return (
      <>
        <div style={{ float: 'right' }}>
          <Button onClick={() => _createDrawer.close()}>
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            onClick={saveCostOrder}
            type={ButtonType.submit}
            color={ButtonColor.primary}
            loading={val}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      </>
    );
  };

  // 当结算类型变化时的回调
  const handleAccountTypeChange = val => {
    if (val) {
      drawerDs!.current!.init('accountCategory', undefined);
    }
  };

  // 点击“新增”按钮的回调
  const openCreateDrawer = (data?) => {
    if (data) {
      drawerDs.loadData([data])
    } else {
      drawerDs = new DataSet(drawerDS());
    }
    _createDrawer = Modal.open({
      key: Modal.key(),
      title: data ? intl.get(`${modelPrompt}.title.editDrawer`).d('编辑成本中心维护') : intl.get(`${modelPrompt}.title.newDrawer`).d('新增成本中心维护'),
      drawer: true,
      closable: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal',
      children: customizeForm(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.COST_CENTER_LIST.CREATE`,
        },
        <Form dataSet={drawerDs} columns={1} labelWidth={112}>
          <Lov name="siteLov" />
          <TextField name="costcenterCode" />
          <TextField name="description" />
          <Select name="accountType" onChange={val => handleAccountTypeChange(val)} />
          <Select name="accountCategory" noCache />
          <DateTimePicker name="dateFromTo" />
          <DateTimePicker name="dateEndTo" />
        </Form>,
      ),
      footer: handleFooter(false),
      onClose: () => drawerDs.reset(),
    });
  };

  const tableColumns: ColumnProps[] = [
    {
      name: 'siteCode',
      width: 170,
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              openCreateDrawer(record?.toData())
            }}
          >
            {value}
          </a>
        );
      },
    },
    { name: 'costcenterCode', width: 170 },
    { name: 'description', width: 150 },
    { name: 'accountTypeDesc' },
    { name: 'accountCategoryDesc', width: 150 },
    {
      name: 'dateFromTo',
      width: 150,
      align: ColumnAlign.center,
    },
    {
      name: 'dateEndTo',
      width: 150,
      align: ColumnAlign.center,
    },
    { name: 'createdByName', width: 150 },
    {
      name: 'attrColumn',
      lock: ColumnLock.right,
      width: 150,
      align: ColumnAlign.center,
      title: intl.get(`${modelPrompt}.option`).d('扩展属性'),
      renderer: ({ record }) => (
        <AttributeDrawer
          serverCode={BASIC.HWMS_BASIC}
          className="org.tarzan.mes.domain.entity.MtCostcenter"
          kid={record?.get('costCenterId')}
          type="text"
          canEdit
        />
      ),
    },
  ];

  const onFieldEnterDown = () => {
    listDs.query(props.listDs.currentPage);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.costOrderMaintain`).d('成本中心维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => openCreateDrawer()}
        // permissionList={[
        //   {
        //     code: `${path}.button.create`,
        //     type: 'button',
        //     meaning: '列表页-新建按钮',
        //   },
        // ]}
        >
          {intl.get(`${modelPrompt}.button.create`).d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.COST_CENTER_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.COST_CENTER_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            dataSet={listDs}
            columns={tableColumns}
            searchCode="cbzxwh"
            customizedCode="cbzxwh"
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.inLibrary.costOrderMaintain', 'tarzan.common'] }),
  withProps(
    () => {
      const listDs = new DataSet({ ...listDS() });
      return {
        listDs,
      };
    },
    {},
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.COST_CENTER_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.COST_CENTER_LIST.LIST`,
      `${BASIC.CUSZ_CODE_BEFORE}.COST_CENTER_LIST.CREATE`,
    ],
  }),
)(CostOrderList);
