.automatic-access-modal {
  display: flex;
  width: 100%;

  .left-part {
    width: 50%;
  }

  .right-part {
    width: 50%;
  }

  .row-line {
    width: 100%;
    padding: 10px 5px;
    display: flex;
    .col-left {
      width: 60px;
      flex-shrink: 0;
      flex-grow: 0;
    }
    
    .col-right {
      position: relative;
      width: calc(100% - 60px);
      flex-grow: 0;
      flex-shrink: 0;
      padding-left: 8px;
      text-align: left;
      :global(.c7n-pro-field-wrapper) {
        padding: 0;
      }
    }
    .col-left-form {
      padding-right: 10px;
      width: 115px;
      flex-shrink: 0;
      flex-grow: 0;
      text-align: right;
    }
    .col-right-form {
      width: calc(100% - 115px);
      flex-grow: 0;
      flex-shrink: 0;
      padding-left: 8px;
      text-align: left;
      :global(.c7n-pro-field-wrapper) {
        padding: 0;
      }
    }
  }

  .row-line-table {
    padding: 10px 5px;
    max-width: 520px;
    overflow-x: auto;

    .access-table {
      margin: 0 auto;

      td {
        height: 35px;
        min-width: 90px;
        max-width: 200px;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 24px;
        padding: 0 5px;
        font-size: 12px;
        border: 1px solid #ccc;
        text-align: center;
      }

      .deep-td {
        background: #a3dfe6;
        border: 2px solid #6ecfdc;
      }

      .shallow-td {
        background: #d4f6fa;
        border: 2px solid #6ecfdc;
      }
    }
  }

  .code-copy-icon {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 13px;
    color: #8c8c8c;
    cursor: pointer;
    z-index: 10;

    &:hover {
      color: #0bc4d3;
    }
  }
}
