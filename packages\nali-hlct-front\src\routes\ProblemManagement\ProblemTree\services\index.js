/**
 * RelationMaintain - 故障树-接口
 * @date: 2023-8-24
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';
// const endUrl = '-43335';

// 故障树详情
export function fetchAllTreeConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-failure-tree-events/all/tree-event/ui`,
    method: 'GET',
  };
}
// 新增下级及关系
export function saveSubordinateRelationshipConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-failure-tree-events/create/subordinate-relationship/ui`,
    method: 'POST',
  };
}

// 新增关系
export function createSubordinateRelationshipConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-failure-tree-events/create/ui`,
    method: 'POST',
  };
}

// 黏贴下级及关系
export function copySubordinateRelationshipConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-failure-tree-events/copy/ui`,
    method: 'POST',
  };
}

// 删除下级及关系
export function deleteSubordinateRelationshipConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-failure-tree-events/delete/ui`,
    method: 'POST',
  };
}

// 删除自身及关系
export function deleteSelfAndRelationshipConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-failure-tree-events/delete-and-current/ui`,
    method: 'POST',
  };
}

// 剪切下级及关系
export function cutSubordinateRelationshipConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-failure-tree-events/cut/ui`,
    method: 'POST',
  };
}

// 更新详情
export function updateTreeEventConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-failure-tree-events/update/treeEvent/ui`,
    method: 'POST',
  };
}

// 获取详情
export function fetchTreeEventConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-failure-tree-events/info/tree-event/ui`,
    method: 'GET',
  };
}

