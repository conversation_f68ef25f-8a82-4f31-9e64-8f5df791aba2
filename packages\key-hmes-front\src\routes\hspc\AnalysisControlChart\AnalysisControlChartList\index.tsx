/**
 * @Description: 分析控制图-列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-11-15 14:29:27
 * @LastEditTime: 2023-04-03 16:51:57
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo } from 'react';
import { DataSet, Table, Modal, Dropdown, Icon } from 'choerodon-ui/pro';
import { Badge, Tag, Popconfirm, Menu } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExport';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { openTab } from 'utils/menuTab';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores/SubjectMaintainDS';
import EditDrawer from './EditDrawer';

const modelPrompt = 'tarzan.hspc.analysisControlChartMaintain';
const tenantId = getCurrentOrganizationId();

const AnalysisControlChartList = (props: any) => {
  const {
    tableDs,
    match: { path },
  } = props;

  useEffect(() => {
    tableDs.query(props.tableDs.currentPage);
  }, []);

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'serialNumber',
        align: ColumnAlign.center,
        width: 80,
        title: intl.get('tarzan.common.label.serialNumber').d('序号'),
        lock: ColumnLock.left,
        renderer: ({ record }) => {
          if (record) {
            return record?.index + 1;
          }
        },
      },
      {
        name: 'analysisCode',
        width: 240,
        lock: ColumnLock.left,
        renderer: ({ value }) => {
          return <a onClick={handleEdit}>{value}</a>;
        },
      },
      {
        name: 'analysisDesc',
        width: 480,
      },
      {
        name: 'enableFlag',
        width: 100,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'isStable',
        width: 100,
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          let calssName = 'green';
          let text = intl.get(`${modelPrompt}.stable`).d('稳态');
          if (value === 'N') {
            calssName = 'red';
            text = intl.get(`${modelPrompt}.abnormal`).d('失控');
          }
          return value && <Tag color={calssName}>{text}</Tag>;
        },
      },
      {
        name: 'subjectCode',
        width: 200,
      },
      {
        name: 'subjectDesc',
        width: 200,
      },
      {
        name: 'subjectDataTitle',
        width: 200,
      },
      {
        name: 'chartTypeDesc',
        width: 160,
      },
      {
        name: 'analysisDimensionDesc',
        width: 200,
      },
      {
        name: 'subgroupSize',
        width: 100,
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        width: 260,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          return (
            <span className="action-link">
              <a onClick={() => handleClickToEnterDetailPage(record)}>
                {intl.get(`${modelPrompt}.graphicDisplay`).d('图形展示')}
              </a>
              {/* <a onClick={() => handleClickToEnterDetailPageNew(record)}>
                {intl.get(`${modelPrompt}.graphicDisplayNew`).d('新图形展示')}
              </a> */}
              <Dropdown
                overlay={
                  <Menu>
                    {/* <Menu.Item key="show">
                      <PermissionButton
                        type="text"
                        onClick={() => handleClickToEnterDetailPage(record)}
                      >
                        {intl.get(`${modelPrompt}.graphicDisplay`).d('图形展示')}
                      </PermissionButton>
                    </Menu.Item> */}
                    {record!.get('isStable') === 'Y' ? (
                      <Menu.Item key="create">
                        <PermissionButton
                          permissionList={[
                            {
                              code: `${path}.button.edit`,
                              type: 'button',
                              meaning: '列表页-编辑新建删除复制按钮',
                            },
                          ]}
                          type="text"
                          onClick={() => handleCreateControlChart(record)}
                        >
                          {intl.get(`${modelPrompt}.generateControlChart`).d('生成控制控制图')}
                        </PermissionButton>
                      </Menu.Item>
                    ) : null}
                    <Menu.Item key="delete">
                      <Popconfirm
                        title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                        onConfirm={() => handleDelete(record)}
                      >
                        <PermissionButton
                          permissionList={[
                            {
                              code: `${path}.button.edit`,
                              type: 'button',
                              meaning: '列表页-编辑新建删除复制按钮',
                            },
                          ]}
                          type="text"
                        >
                          {intl.get('tarzan.common.button.delete').d('删除')}
                        </PermissionButton>
                      </Popconfirm>
                    </Menu.Item>
                  </Menu>
                }
              >
                <a>
                  {intl.get(`${modelPrompt}.more`).d('更多操作')}
                  <Icon type="expand_more" />
                </a>
              </Dropdown>
            </span>
          );
        },
      },
    ],
    [],
  );

  // const handleClickToEnterDetailPageNew = record => {
  //   props.history.push({
  //     pathname: `/hspc/analysis-control-chart/new/display/`,
  //     query: { analysisId: record.get('analysisId'), sampleType: record.get('sampleType') },
  //   });
  // };

  const handleClickToEnterDetailPage = record => {
    const url = `/hspc/analysis-control-chart/display/${record.get('sampleType')}/${record.get(
      'analysisId',
    )}`;
    openTab({
      icon: '',
      title: intl.get(`${modelPrompt}.graphicDisplay`).d('图形展示'),
      key: url,
      path: url,
      closable: true,
    });
  };

  const handleEdit = () => {
    Modal.open({
      ...drawerPropsC7n({ ds: tableDs }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.createSubject`).d('新建课题'),
      style: {
        width: 360,
      },
      children: <EditDrawer ds={tableDs} />,
      onOk: handleEditConfirm,
    });
  };

  const handleEditConfirm = async () => {
    const res = await tableDs.submit();
    let returenFlag = false;
    tableDs.query(tableDs.currentPage);
    if (res && res.rows && res.rows.content.length > 0 && res.rows.content[0].success) {
      returenFlag = true;
      notification.success({});
    }
    return returenFlag;
  };

  const handleDelete = record => {
    // 设为false时不弹确认直接删除
    tableDs.delete([record], false).then(res => {
      tableDs.query(tableDs.currentPage);
      if (res && res.rows && res.rows.content.length > 0 && res.rows.content[0].success) {
        notification.success({});
      }
    });
  };

  const handleCreateControlChart = record => {
    props.history.push(`/hspc/control-chart/create-page/${record.get('analysisCode')}`);
  };

  const getExportQueryParams = () => {
    // const { selected } = tableDs;
    const queryParams = tableDs.queryDataSet?.toData()[0] || {};
    return {
      ...queryParams,
    };
    // if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
    //   return {};
    // }
    // return {
    //   id: selected.map(item => item.get('id')),
    // };
  };

  const handleCreate = () => {
    props.history.push('/hspc/analysis-control-chart/create-page/create');
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.analysisControlChartMaintain`).d('分析控制图维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <ExcelExport
          method="GET"
          requestUrl={`${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="fxkzt1"
          customizedCode="fxkzt1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hspc.analysisControlChartMaintain', 'tarzan.hspc.chartInfo', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(AnalysisControlChartList),
);
