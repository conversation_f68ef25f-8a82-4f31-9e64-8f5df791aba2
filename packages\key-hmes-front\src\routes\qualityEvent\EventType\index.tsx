/**
 * @Description: 事件类型维护
 * @Author: <<EMAIL>>
 * @Date: 2022-11-03 09:36:07
 * @LastEditTime: 2023-01-04 14:59:30
 * @LastEditors: <<EMAIL>>
 */

import React, { FC, useMemo } from 'react';
import { RouteComponentProps } from 'react-router';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import {
  DataSet,
  Table,
  TextField,
  Switch,
  IntlField,
  Modal,
  Form,
} from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from './stories/EventTypeDs';
import { saveEventTypeConfig } from './services';

import TableDrawer from './TableDrawer';

const modelPrompt = 'tarzan.event.type.model.type';

const EventType: FC<RouteComponentProps> = ({ match: { path } }) => {
  const saveEventType = useRequest(saveEventTypeConfig(), {
    manual: true,
    needPromise: true,
  });

  const tableDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  const eventTypeDrawerDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  const handleSave = async () => {
    const validate = await eventTypeDrawerDs.validate();
    if (!validate) {
      return;
    }

    const recordData = eventTypeDrawerDs.toData()[0];

    return saveEventType
      .run({
        params: recordData,
        onSuccess: () => {
          // @ts-ignore
          notification.success();
          tableDs.query(tableDs.currentPage);
        },
      })
      .then(res => {
        if (!res?.success) {
          return Promise.reject();
        }
      });
  };

  // 编辑新建drawer
  const handleEventTypeDrawerShow = data => {
    eventTypeDrawerDs.loadData([data]);
    Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.event.objectType.title.objectQuerySQL').d('对象语句查询'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal',
      onOk: handleSave,
      children: (
        <Form dataSet={eventTypeDrawerDs} columns={1}>
          <TextField name="eventTypeCode" />
          <IntlField
            name="description"
            modalProps={{ title: intl.get(`${modelPrompt}.description`).d('事件类型描述') }}
          />
          <Switch name="enableFlag" />
          <Switch name="defaultEventTypeFlag" disabled />
        </Form>
      ),
    });
  };
  const handleTableDrawerShow = record => {
    const eventTypeId = record.get('eventTypeId');
    Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.event.type.title.object').d('对象类型维护'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      // @ts-ignore
      children: <TableDrawer eventTypeId={eventTypeId} path={path} />,
      footer: (okBtn, cancelBtn) => {
        return [cancelBtn];
      },
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'eventTypeCode',
      width: 300,
      renderer: ({ record, value }) => {
        return (
          <PermissionButton
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
            className="hcm-permission-btn"
            type="text"
            onClick={() => {
              handleEventTypeDrawerShow(record?.toData());
            }}
          >
            {value}
          </PermissionButton>
        );
      },
    },
    {
      name: 'description',
      width: 200,
    },
    {
      name: 'enableFlag',
      width: 90,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'defaultEventTypeFlag',
      width: 90,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'operator',
      width: 180,
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <PermissionButton
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          className="hcm-permission-btn"
          type="text"
          onClick={() => {
            handleTableDrawerShow(record);
          }}
        >
          {intl.get(`${modelPrompt}.operatorRow`).d('对象类型')}
        </PermissionButton>
      ),
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.event.type.title.list').d('事件类型维护')}>
        <PermissionButton
          type="c7n-pro"
          icon="add"
          color={ButtonColor.primary}
          onClick={() => {
            handleEventTypeDrawerShow({
              enableFlag: 'Y',
              defaultEventTypeFlag: 'N',
              eventTypeId: '',
            });
          }}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          searchCode="sjlxwh1"
          customizedCode="sjlxwh1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.event.type', 'tarzan.common'],
})(EventType);
