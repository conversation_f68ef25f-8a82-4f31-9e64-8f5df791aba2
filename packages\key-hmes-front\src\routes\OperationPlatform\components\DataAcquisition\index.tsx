/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-17 10:55:14
 * @LastEditTime: 2023-07-20 11:01:31
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useEffect } from 'react';
import { observer } from 'mobx-react';
import { openTab } from 'utils/menuTab';
import { Table, DataSet, TextField, NumberField, Select, Attachment } from 'choerodon-ui/pro';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { isNil } from 'lodash';
import { Record } from 'choerodon-ui/dataset';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useRequest } from '@components/tarzan-hooks';
import { CardLayout } from '../commonComponents';
import { useOperationPlatform } from '../../contextsStore';
import { tableDS } from './stores';
import { FetchDataCollectionList, SaveDataCollectionList } from './services';
import styles from './index.module.less';

interface TagValueInputProps {
  record: Record;
  name: string;
  onEnterDown: (record: Record) => void;
}

const TagValueInput = observer((props: TagValueInputProps) => {
  const { record, name, onEnterDown } = props;

  const renderDiv = useMemo(() => {
    switch (record.get('valueType')) {
      case 'VALUE':
        return <NumberField record={record} name={name} onEnterDown={() => { onEnterDown(record) }} style={{ width: '100%' }} />;
      case 'DECISION_VALUE':

        return (
          // <Select
          //   record={record}
          //   name={name}
          //   style={{ width: '100%' }}
          //   onChange={() => { onEnterDown(record) }}
          //   getPopupContainer={() => document.getElementById('operationPlatform') || document.body}
          // >
          //   <Select.Option value={record.get('trueValue')}>{record.get('trueValue')}</Select.Option>
          //   <Select.Option value={record.get('falseValue')}>{record.get('falseValue')}</Select.Option>
          // </Select>
          <div className={`${styles.decisionValue} ${record?.get('valueAllowMissing') !== 'Y' ? styles.requiredValue : ''}`}>
            <div className={record.get(name) === record.get('trueValue') ? styles.trueTag : ''} onClick={() => {handleChangeRadio(record, name, 'trueValue', onEnterDown)}}>{record.get('trueValue')}</div>
            <div className={record.get(name) === record.get('falseValue') ? styles.falseTag : ''}onClick={() => {handleChangeRadio(record, name, 'falseValue', onEnterDown)}}>{record.get('falseValue')}</div>
          </div>
        );
      case 'ENCLOSURE':
        return <Attachment
          record={record}
          name={name}
          labelLayout={LabelLayout.none}
          viewMode='popup'
          sortable={false}
        />;
      case 'TEXT':
        return <TextField record={record} name={name} onChange={() => { onEnterDown(record) }} style={{ width: '100%' }} />;
      case 'VALUE_LIST':
        return (
          <Select
            record={record}
            combo
            name={name}
            style={{ width: '100%' }}
            onChange={() => { onEnterDown(record) }}
            getPopupContainer={() => document.getElementById('operationPlatform') || document.body}
          >
            {(record.get('valueList') || '').split(',').map(item => <Select.Option value={item}>{item}</Select.Option>)}
          </Select>
        );
      default:
        return <></>;
    }
  }, [])

  return renderDiv;
})

const handleChangeRadio = (record: Record, name: string, value: string, onEnterDown) => {
  record.set(name, record.get(value));
  onEnterDown(record)
};

const DataAcquisition = observer((props) => {
  const { workOrderData, enterInfo } = useOperationPlatform();

  const { run: fetchDataCollectionList, loading: queryLoading } = useRequest(FetchDataCollectionList(), {
    manual: true,
    needPromise: true,
  });
  const { run: saveDataCollectionList, loading: saveLoading } = useRequest(SaveDataCollectionList(), {
    manual: true,
    needPromise: true,
  });

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  useEffect(() => {
    if (!workOrderData?.routerStepId || !workOrderData?.eoId || workOrderData?.dataCollectionShowFlag !== 'Y') {
      tableDs.loadData([]);
      return;
    }
    handleRefresh();
  }, [workOrderData, workOrderData]);

  const handleRefresh = () => {
    if (!workOrderData?.routerStepId || !workOrderData?.eoId || workOrderData?.dataCollectionShowFlag !== 'Y') {
      return;
    }
    fetchDataCollectionList({
      params: {
        eoId: workOrderData?.eoId,
        operationId: enterInfo?.selectOperation?.operationId,
        routerStepId: workOrderData?.routerStepId,
        workcellId: enterInfo?.workStationId,
      },
    }).then(res => {
      if (!res || res.message) {
        tableDs.loadData([]);
        return;
      }
      tableDs.loadData(res)
      tableDs.current = undefined;
    })
  }

  const validateValueCompliant = (record: Record) => {
    const {
      valueType,
      // valueAllowMissing,
      minimumValue,
      maximalValue,
      valueList,
      trueValue,
      // falseValue,
      tagValue,
    } = record.toData();
    if (['ENCLOSURE'].includes(valueType)) {
      // 附件类型的采集项，不做合规性校验，需要用户手动修改
      return;
    }
    if (isNil(tagValue)) {
      // 当清空录入值时，清空合规列
      record.set('tagCalculateResult', null);
      return;
    }
    let _tagCalculateResult: "OK" | "NG" | '' = 'OK';
    switch (valueType) {
      case 'VALUE':
        // 数值类型的判断
        if(!minimumValue && !maximalValue && tagValue) {
          _tagCalculateResult = 'OK';
        }
        if (minimumValue >= 0 && Number(tagValue) < Number(minimumValue)) {
          _tagCalculateResult = 'NG';
        }
        if (maximalValue >= 0 && Number(tagValue) > Number(maximalValue)) {
          _tagCalculateResult = 'NG';
        }
        break;
      case 'DECISION_VALUE':
        if (tagValue !== trueValue) {
          _tagCalculateResult = 'NG';
        }
        break;
      case 'VALUE_LIST':
        if (!(valueList || '').split(',').includes(tagValue)) {
          _tagCalculateResult = 'NG';
        }
        break;
      case 'TEXT':
        if(tagValue) {
          _tagCalculateResult = 'OK';
        }
        break;
      default:
        break;
    }
    record.set('tagCalculateResult', _tagCalculateResult);
  }


  const handleSave = async (record: Record) => {
    validateValueCompliant(record);
    const flag = await record.validate(true, true);
    if (!flag) {
      return;
    }
    saveDataCollectionList({
      params: {
        ...record.toData(),
        eoId: workOrderData?.eoId,
        workcellId: enterInfo?.workStationId,
        operationId: enterInfo?.selectOperation?.operationId,
        routerStepId: workOrderData?.routerStepId,
      },
    }).then(res => {
      if (!res || res.message) {
        return;
      }
      record.init('dataRecordId', res);
      record.init('tagValue', record.get('tagValue'));
      record.init('tagCalculateResult', record.get('tagCalculateResult'));
      tableDs.next();
      handleRefresh();
    })
  }

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'lineNumber',
        width: 70,
        align: ColumnAlign.right,
        renderer: ({ dataSet, record }) => {
          return (dataSet?.indexOf(record!) || 0) + 1;
        },
      },
      {
        name: 'tagColumn',
        title: <span style={{marginLeft: '16px'}}>数据项</span>,
        renderer: ({ record }) => {
          return record?.get('tagDescription') || record?.get('tagCode')
        },
      },
      {
        name: 'valueTypeDesc',
        width: 100,
      },
      // {
      //   name: 'minimumValue',
      //   width: 80,
      // },
      // {
      //   name: 'maximalValue',
      //   width: 80,
      // },
      {
        name: 'valueList',
        width: 120,
        renderer: ({ record }) => {
          switch (record?.get('valueType')) {
            case 'VALUE':
              if(!record?.get('minimumValue') && !record?.get('maximalValue')){
                return '';
              }
              return `${record?.get('minimumValue') >= 0 ? record?.get('minimumValue') : ''}~${record?.get('maximalValue') >= 0 ? record?.get('maximalValue') : ''}`;
            case 'DECISION_VALUE':
              if(!record?.get('trueValue') && !record?.get('falseValue')){
                return '';
              }
              return `${record?.get('trueValue') || ''}/${record?.get('falseValue') || ''}`;
            case 'VALUE_LIST':
              return (record?.get('valueList') || '');
            default:
              return <></>;
          }
        },
      },
      {
        name: 'tagValue',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ record }) => {
          return <TagValueInput record={record!} onEnterDown={handleSave} name='tagValue' />;
        },
      },
      {
        name: 'tagCalculateResult',
        align: ColumnAlign.center,
        renderer: ({ record }) => {
          // return <Select
          //   record={record!}
          //   name="tagCalculateResult"
          //   onChange={() => { handleSave(record!) }}
          //   getPopupContainer={() => document.getElementById('operationPlatform') || document.body}
          // />;
          if(record?.get('tagCalculateResult') === 'OK') {
            return <div className={styles.colButtonOk}>OK</div>
          }
          if(record?.get('tagCalculateResult') === 'NG') {
            return <div className={styles.colButtonNg}>NG</div>
          }
          return null;
        },
      },
    ];
  }, [tableDs, workOrderData, enterInfo]);

  const handleCreate = () => {
    if (!workOrderData?.routerStepId || !workOrderData?.eoId) {
      return;
    }
    openTab({
      title: '不良记录平台',
      key: `/hmes/bad-record/platform/detail/create`,
      path: `/hmes/bad-record/platform/detail/create`,
      closable: true,
      state: {
        toDefault: 'Y',
        siteId: enterInfo?.siteId, // 站点
        siteName: enterInfo?.siteName,
        workcellId: enterInfo?.workStationId, // 工作单元
        workcellName: enterInfo?.workStationName,
        eoId: workOrderData?.eoId, // 执行作业编码
        eoNum: workOrderData?.eoNum,
        routerId: workOrderData?.routerId, // 工艺路线
        routerName: workOrderData?.routerName,
        routerStepId: workOrderData?.routerStepId, // 步骤
        routerStepDesc: workOrderData?.routerStepName,
        processesFlag:'Y',// 是否从工序跳转不良记录
      },
    });
  }

  return (
    <CardLayout.Layout
      spinning={queryLoading || saveLoading}
      className={styles.dataQcquisition}
    >
      <CardLayout.Header
        className='DataAcquisitionHead'
        title="数据采集"
        help={props?.cardUsage?.remark}
        addonAfter={
          <div className={styles.topRight}>
            <div className={styles.buttonRed} onClick={handleCreate}>
              不良创建
            </div>
          </div>
        }
      />
      <CardLayout.Content className='DataAcquisitionForm'>
        <Table id={styles.tableInnerField} dataSet={tableDs} columns={columns} customizedCode='DataAcquisition' style={{ height: 'calc(100% - 16px)' }} renderEmpty={() => <></>} rowHeight={30} />
      </CardLayout.Content>
    </CardLayout.Layout>
  )
})

export default DataAcquisition;
