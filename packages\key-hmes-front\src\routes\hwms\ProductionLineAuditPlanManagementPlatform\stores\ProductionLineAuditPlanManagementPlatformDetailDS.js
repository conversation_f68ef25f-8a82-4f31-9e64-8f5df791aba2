/**
 * @Description: 产线审核计划管理平台-详情DS
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.prodlineReview.prodlineReviewPlan';

const tableDetailDS = () => ({
  autoQuery: false,
  selection: false,
  forceValidate: true,
  fields: [
    {
      name: 'prodlineRevplanCode',
      label: intl.get(`${modelPrompt}.prodlineRevplanCode`).d('产线审核计划编码'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      type: FieldType.string,
      disabled: true,
      lookupCode: 'YP.QIS.PRODLINE_REVPLAN_STATUS',
    },
    {
      name: 'siteObj',
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      type: FieldType.object,
      textField: 'siteName',
      valueField: 'siteId',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteObj.siteId',
      type: FieldType.string,
    },
    {
      name: 'siteName',
      bind: 'siteObj.siteName',
      type: FieldType.string,
    },
    {
      name: 'prodlineReviewTmpNumObj',
      label: intl.get(`${modelPrompt}.prodlineReviewTmpNumObj`).d('产线审核模板编码'),
      lovCode: 'YP.QIS.PRODLINE_REVIEW_TMP',
      ignore: 'always',
      type: FieldType.object,
      textField: 'prodlineReviewTmpNum',
      valueField: 'prodlineReviewTmpId',
      lovPara: { tenantId },
    },
    {
      name: 'prodlineReviewTmpId',
      bind: 'prodlineReviewTmpNumObj.prodlineReviewTmpId',
      type: FieldType.string,
    },
    {
      name: 'prodlineReviewTmpNum',
      bind: 'prodlineReviewTmpNumObj.prodlineReviewTmpNum',
      type: FieldType.string,
    },
    {
      name: 'reviewType',
      label: intl.get(`${modelPrompt}.reviewType`).d('审核类型'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.REVIEW_TYPE',
      required: true,
    },
    {
      name: 'reviewStage',
      label: intl.get(`${modelPrompt}.reviewStage`).d('审核阶段'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.REVIEW_STAGE',
      required: true,
    },
    {
      name: 'prodObj',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      ignore: 'always',
      type: FieldType.object,
      multiple: true,
      textField: 'prodLineName',
      valueField: 'prodLineId',
      dynamicProps: {
        disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
      required: true,
    },
    {
      name: 'prodLineIdList',
      bind: 'prodObj.prodLineId',
      type: FieldType.string,
    },
    {
      name: 'prodLineNameList',
      bind: 'prodObj.prodLineName',
      type: FieldType.string,
    },
    {
      name: 'projectName',
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      type: FieldType.string,
      required: true,
      lookupCode: 'YP.QIS.PROJECT_NAME',
      lovPara: { tenantId },
    },
    {
      name: 'closeDate',
      label: intl.get(`${modelPrompt}.closeDate`).d('审核关闭时间'),
      type: FieldType.date,
      disabled: true,
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      type: FieldType.date,
      disabled: true,
    },
    {
      name: 'creationByName',
      label: intl.get(`${modelPrompt}.creationByName`).d('创建人'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'rejectReason',
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'reviewByName',
      label: intl.get(`${modelPrompt}.reviewByName`).d('审批人'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'reviewDate',
      label: intl.get(`${modelPrompt}.reviewDate`).d('审批时间'),
      type: FieldType.date,
      disabled: true,
    },
  ],
});
const tableDetailLineDS = () => ({
  autoQuery: false,
  selection: "multiple",
  forceValidate: true,
  fields: [
    {
      name: 'elementNum',
      label: intl.get(`${modelPrompt}.elementNum`).d('产线审核要素编码'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      type: FieldType.string,
      disabled: true,
      lookupCode: 'YP.QIS.PRODLINE_REVPLAN_STATUS',
    },
    {
      name: 'reviewDimension',
      label: intl.get(`${modelPrompt}.reviewDimension`).d('评审维度'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.REVIEW_DIMENSION',
      required: true,
    },
    {
      name: 'reviewItem',
      label: intl.get(`${modelPrompt}.reviewItem`).d('审核项目'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'reviewContent',
      label: intl.get(`${modelPrompt}.reviewContent`).d('评审要素'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'deliveryName',
      label: intl.get(`${modelPrompt}.deliveryName`).d('交付物名称'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'deliveryTemplate',
      label: intl.get(`${modelPrompt}.deliveryTemplate`).d('交付物模板'),
      type: FieldType.attachment,
      bucketName: 'qms',
    },
    {
      name: 'deliveryUuid',
      label: intl.get(`${modelPrompt}.deliveryUuid`).d('交付物'),
      type: FieldType.attachment,
      disabled: true,
      bucketName: 'qms',
    },
    {
      name: 'responseObj',
      label: intl.get(`${modelPrompt}.responseObj`).d('责任部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: 'always',
      type: FieldType.object,
      textField: 'unitName',
      valueField: 'responsibleDeptId',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'responsibleDeptId',
      bind: 'responseObj.unitId',
      type: FieldType.string,
    },
    {
      name: 'unitName',
      bind: 'responseObj.unitName',
      type: FieldType.string,
    },
    {
      name: 'responseUserObj',
      label: intl.get(`${modelPrompt}.responseUserObj`).d('责任人'),
      lovCode: 'HPFM.UNIT_LIMIT_EMPLOYEE',
      ignore: 'always',
      type: FieldType.object,
      textField: 'realName',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('responsibleDeptId');
        },
        lovPara: ({ record }) => {
          const unitId = record.get('responsibleDeptId');
          return {
            tenantId,
            unitId,
          };
        },
      },
    },
    {
      name: 'responsibleEmId',
      bind: 'responseUserObj.id',
      type: FieldType.string,
    },
    {
      name: 'responsibleEmName',
      bind: 'responseUserObj.realName',
      type: FieldType.string,
    },
    {
      name: 'scheFinishTime',
      label: intl.get(`${modelPrompt}.scheFinishTime`).d('计划完成时间'),
      type: FieldType.date,
      required: true,
    },
    {
      name: 'applyFlag',
      label: intl.get(`${modelPrompt}.applyFlag`).d('是否适用'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'noApplyReason',
      label: intl.get(`${modelPrompt}.noApplyReason`).d('不适用原因'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'autualFinishTime',
      label: intl.get(`${modelPrompt}.autualFinishTime`).d('实际完成时间'),
      type: FieldType.date,
      disabled: true,
    },
    {
      name: 'reviewResult',
      label: intl.get(`${modelPrompt}.reviewResult`).d('评审结果'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'nonConTerm',
      label: intl.get(`${modelPrompt}.nonConTerm`).d('不符合项'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'releaseClass',
      label: intl.get(`${modelPrompt}.releaseClass`).d('放行等级'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'closeDate',
      label: intl.get(`${modelPrompt}.closeDate`).d('审核关闭时间'),
      type: FieldType.date,
      disabled: true,
    },
  ],
});
export { tableDetailDS, tableDetailLineDS };
