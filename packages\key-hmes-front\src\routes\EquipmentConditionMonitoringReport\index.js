import React, { useMemo, useEffect } from 'react';
import { DataSet, Table, Modal } from 'choerodon-ui/pro';

import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import moment from 'moment';
import notification from 'utils/notification';
import { tableDS, drawerDS, drawerCodeDS } from './stores';

const modelPrompt = 'tarzan.hmes.EquipmentConditionMonitoringReport';

const EquipmentConditionMonitoringReport = observer(() => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  const drawerDs = useMemo(() => new DataSet(drawerDS()), []); // 复制ds
  const drawerCodeDs = useMemo(() => new DataSet(drawerCodeDS()), []); // 复制ds

  useEffect(() => {
    tableDs.addEventListener('query', () => {
      const { startTime, endTime } = tableDs?.queryDataSet?.toJSONData()[0];
      // console.log('moment',moment(endTime).diff(startTime, 'days'))
      if (moment(endTime).diff(startTime, 'days') > 3) {
        notification.error({
          message: '查找时间间隔不能超过3天！',
        });
        tableDs.loadData([]);
        return false;
      }
    });
  }, []);

  const openNcDrawer = async record => {
    drawerDs.setQueryParameter('equipmentStatusId', record.data.equipmentStatusId);
    drawerDs.query();
    Modal.open({
      closable: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.ncRecordDetail`).d('故障代码明细'),
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table dataSet={drawerDs} columns={drawerColumns} style={{ height: 400 }} />,
    });
  };

  const openBarCodeDrawer = async record => {
    drawerCodeDs.setQueryParameter('equipmentStatusId', record.data.equipmentStatusId);
    drawerCodeDs.query();
    Modal.open({
      closable: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.barCodeDetail`).d('产品条码明细'),
      drawer: true,
      style: {
        width: 720,
      },
      children: (
        <Table dataSet={drawerCodeDs} columns={drawerCodeColumns} style={{ height: 400 }} />
      ),
    });
  };

  const drawerColumns = [
    {
      name: 'evalItemCode',
      width: 170,
    },
    {
      name: 'evalItemDesc',
      width: 170,
    },
  ];

  const drawerCodeColumns = [
    {
      name: 'processBarcode',
      width: 170,
    },
  ];

  const columns = [
    {
      name: 'equipmentCode',
      width: 170,
    },
    {
      name: 'equipmentName',
      width: 170,
    },
    {
      name: 'equipmentStatusCodeDesc',
      width: 150,
    },
    {
      name: 'equipmentLocationName',
      width: 150,
    },
    {
      header: '故障代码',
      align: 'center',
      // lock: 'left',
      renderer: ({ record }) => {
        if (record.get('equipmentStatusCodeDesc') === '故障') {
          return (
            <span className="action-link">
              <a
                onClick={() => {
                  openNcDrawer(record);
                }}
              >
                {intl.get(`${modelPrompt}.detail`).d('明细')}
              </a>
            </span>
          );
        }
      },
    },
    {
      name: 'startTime',
      width: 150,
    },
    {
      name: 'endTime',
      width: 150,
    },
    {
      name: 'continueTime',
      width: 150,
    },
    {
      header: '产品条码',
      align: 'center',
      // lock: 'left',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => {
                openBarCodeDrawer(record);
              }}
            >
              {intl.get(`${modelPrompt}.detail`).d('明细')}
            </a>
          </span>
        );
      },
    },
    {
      name: 'loginName',
      width: 150,
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('设备状态监控报表')}></Header>
      <Content>
        <Table
          queryFieldsLimit={3}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryBar={TableQueryBarType.filterBar}
          style={{ height: 400 }}
          dataSet={tableDs}
          columns={columns}
          searchCode="EquipmentConditionMonitoringReport"
          customizedCode="EquipmentConditionMonitoringReport"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.EquipmentConditionMonitoringReport', 'tarzan.common'],
})(EquipmentConditionMonitoringReport);
