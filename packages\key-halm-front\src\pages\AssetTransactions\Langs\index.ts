/*
 * @Description: 资产事务多语言
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-03-25 15:47:12
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLang = key => {
  const PREFIX = 'aatn.assetTransactionBasicType';
  const MODEL_PREFIX = 'aatn.assetTransactionBasicType.model.assetTransactionBasicType';
  const LANGS = {
    ...getCommonLangs(),

    // title
    ASSET_TRANSFER: intl.get(`${PREFIX}.title.assetTransferTitle`).d('资产调拨转移'),
    ASSET_SCRAP: intl.get(`${PREFIX}.title.assetScrapTitle`).d('资产报废'),

    TRANSFER_DETAIL: intl.get(`${PREFIX}.title.assetTransferDetailTitle`).d('资产调拨转移明细'),

    TRANS_LINE: intl
      .get(`aatn.assetTransactionBasicType.panel.transactionLines`)
      .d('事务处理行信息'),

    // message
    EQP_STORAGE_EMPTY: intl
      .get(`${PREFIX}.view.message.eqpStorageEmpty`)
      .d('暂无内容，请先输入上方过滤条件'),
    PLEASE_SEL_PENDING: intl
      .get(`${PREFIX}.view.message.pleaseSelPending`)
      .d('只能取消待处理状态的行'),
    PLEASE_SEL_PROCESSING: intl
      .get(`${PREFIX}.view.message.pleaseSelPending`)
      .d('只能直接完成处理中状态的行'),

    TRANSFER_IN_INFO: intl.get(`${PREFIX}.view.message.transferInInfo`).d('调入信息'),
    TRANSFER_OUT_INFO: intl.get(`${PREFIX}.view.message.transferOutInfo`).d('调出信息'),

    // 文本信息

    // btn
    CHOOSE_ASSET: intl.get(`aatn.assetTransactionBasicType.button.multiAdd`).d('选择资产'),
    SAVE_AND_ADD: intl.get(`aatn.assetTransactionBasicType.button.saveAndAdd`).d('保存并添加资产'),
    RECALL: intl.get(`aatn.assetTransactionBasicType.button.recall`).d('撤回'),

    // model
    ASSET_ATTACHMENT_MANAGEMENT: intl
      .get(`${MODEL_PREFIX}.asset-attachment-management`)
      .d('附件管理'),
    SOURCE_TYPE: intl
      .get(`aatn.assetTransactionBasicType.model.assetTransactionBasicType.sourceType`)
      .d('来源类型'),

    SOURCE_NUMBER: intl
      .get(`aatn.assetTransactionBasicType.model.assetTransactionBasicType.sourceNumber`)
      .d('来源单据号'),
    ICON: intl.get(`${MODEL_PREFIX}.icon`).d('图标'),
    ASSET_TRANSACTION_TYPE: intl
      .get(`aatn.assetTransactionBasicType.modal.assetTransactionBasicType`)
      .d('资产事务处理类型'),
    TRANSACTION_LINES: intl
      .get('aatn.assetTransactionBasicType.modal.transactionLines')
      .d('事务处理行'),
    ASSET_OR_EQUIPMENT: intl
      .get(`aatn.assetTransactionBasicType.modal.assetsOrEquipment`)
      .d('设备/资产'),
    CHANGE_NUM: intl.get(`${MODEL_PREFIX}.changeNum`).d('事务处理单编号'),
    TITLE_OVERVIEW: intl.get(`${MODEL_PREFIX}.titleOverview`).d('标题概述'),
    TRANSACTION_TYPE: intl.get(`${MODEL_PREFIX}.transactionType`).d('资产事务类型'),
    PROCESS_STATUS: intl.get(`${MODEL_PREFIX}.processStatus`).d('处理状态'),
    PRINCIPAL_PERSON: intl.get(`${MODEL_PREFIX}.principalPerson`).d('负责人'),
    LINE_NUM: intl.get(`${MODEL_PREFIX}.lineNum`).d('编号'),
    OBJECT_NUM: intl.get(`${MODEL_PREFIX}.objectNum`).d('对象编号'),
    OBJECT_NAME: intl.get(`${MODEL_PREFIX}.objectName`).d('对象名称'),
    ASSET_EQP_NUM: intl.get(`${MODEL_PREFIX}.assetNum`).d('设备/资产编号'),
    ASSET_DESC: intl.get(`${MODEL_PREFIX}.assetDesc`).d('资产全称'),
    CHANGE_CONTENT: intl.get(`${MODEL_PREFIX}.description`).d('变更内容'),
    OLD_ASSET_STATUS: intl.get(`${MODEL_PREFIX}.oldAssetStatus`).d('原资产状态'),
    NEW_ASSET_STATUS: intl.get(`${MODEL_PREFIX}.newAssetStatus`).d('目标资产状态'),
    FINANCIAL_NUM: intl.get(`${MODEL_PREFIX}.financialNum`).d('固定资产编号'),
    NEED_QUANTITY: intl.get(`${MODEL_PREFIX}.needQuantity`).d('需求数量'),
    RECEIPT_OBJ_TYPE: intl.get(`${MODEL_PREFIX}.receiptObjType`).d('领用对象类型'),
    DEMAND_DATE: intl.get(`${MODEL_PREFIX}.demandDate`).d('需求日期'),
    DEVICE_STATUS: intl.get(`${MODEL_PREFIX}.deviceStatus`).d('设备状态'),
    USING_ORG: intl.get(`${MODEL_PREFIX}.usingOrg`).d('负责组织'),

    // 调拨
    TRANSFER_IN_ORG: intl.get(`${MODEL_PREFIX}.transferInOrg`).d('调入使用组织'),
    TRANSFER_OUT_ORG: intl.get(`${MODEL_PREFIX}.transferOutOrg`).d('调出使用组织'),
    TRANSFER_IN_MAN: intl.get(`${MODEL_PREFIX}.transferInMan`).d('调入资产管理员'),
    TRANSFER_OUT_MAN: intl.get(`${MODEL_PREFIX}.transferOutMan`).d('调出资产管理员'),
    DISPATCH_USER: intl.get(`${MODEL_PREFIX}.dispatchUser`).d('使用人'),
    PREVIOUS_USER: intl.get(`${MODEL_PREFIX}.previousUser`).d('原使用人'),
    DISPATCH_ASSET_LOCATION: intl.get(`${MODEL_PREFIX}.dispatchAssetLocation`).d('资产位置'),
    PREVIOUS_ASSET_LOCATION: intl.get(`${MODEL_PREFIX}.previousAssetLocation`).d('原资产位置'),
    REASON_TRANSFER: intl.get(`${MODEL_PREFIX}.reason`).d('调拨原因'),
    SUBMIT_OA: intl.get(`${MODEL_PREFIX}.sumbitOa`).d('OA提交'),
  };

  return LANGS[key];
};

export default getLang;
