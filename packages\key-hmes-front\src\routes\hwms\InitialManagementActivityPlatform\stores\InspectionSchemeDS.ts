/**
 * @Description: 检验方案维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-06-15 14:16:55
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import uuid from 'uuid/v4';

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.initialManagementActivity';
const tenantId = getCurrentOrganizationId();
const endUrl = '';
// 列表-ds
const listTableDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  primaryKey: 'inspectSchemeId',
  dataKey: 'content',
  totalKey: 'totalElements',
  modifiedCheck: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/initial-managements/list/ui`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialManagementNum`).d('活动编码'),
      name: 'initialManagementNum',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialManagementDesc`).d('活动说明'),
      name: 'initialManagementDesc',
    },
    {
      name: 'initialManagementStatus',
      lookupCode: 'YP.QIS.INITIAI_MANAGE_STATUS',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialManagementStatus`).d('活动状态'),
    },
    {
      name: 'actObject',
      lookupCode: 'YP.QIS.ACT_OBJECT',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actObject`).d('活动对象'),
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      name: 'supplierObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      // multiple: true,
    },
    {
      name: 'supplierId',
      bind: 'supplierObject.supplierId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      // multiple: true,
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineObject.prodLineId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      name: 'operationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      // multiple: true,
    },
    {
      name: 'operationId',
      bind: 'operationObject.operationId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipment`).d('设备'),
      name: 'equipmentObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'equipmentId',
      bind: 'equipmentObject.equipmentId',
    },
    {
      name: 'equipmentName',
      bind: 'equipmentObject.equipmentName',
    },
    {
      name: 'inspectorUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectorName`).d('统筹人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'inspectorUserLov.id',
    },
    {
      name: 'scheduleStartDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.scheduleStartDateFrom`).d('计划开始时间从'),
      max: 'scheduleStartDateTo',
    },
    {
      name: 'scheduleStartDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.scheduleStartDateTo`).d('计划开始时间至'),
      min: 'scheduleStartDateFrom',
    },
    {
      name: 'scheduleEndDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.scheduleEndDateFrom`).d('预计完成时间从'),
      max: 'scheduleEndDateTo',
    },
    {
      name: 'scheduleEndDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.scheduleEndDateTo`).d('预计完成时间至'),
      min: 'scheduleEndDateFrom',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      name: 'initialManagementNum',
      label: intl.get(`${modelPrompt}.initialManagementNum`).d('活动编号'),
    },
    {
      type: FieldType.string,
      name: 'initialManagementDesc',
      label: intl.get(`${modelPrompt}.initialManagementDesc`).d('活动说明'),
    },
    {
      name: 'initialManagementStatus',
      lookupCode: 'YP.QIS.INITIAI_MANAGE_STATUS',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialManagementStatus`).d('状态'),
    },
    {
      name: 'createdByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorName`).d('统筹人'),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建日期'),
      name: 'creationDate',
    },
    {
      type: FieldType.string,
      name: 'siteCode',
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂编码'),
    },
    {
      type: FieldType.string,
      name: 'model',
      label: intl.get(`${modelPrompt}.model`).d('产品名称'),
    },
    {
      type: FieldType.string,
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      type: FieldType.string,
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      type: FieldType.string,
      name: 'phaseDesc',
      lookupCode: 'YP.QIS.REVIEW_STAGE',
      label: intl.get(`${modelPrompt}.phase`).d('项目阶段'),
    },
    {
      type: FieldType.string,
      name: 'scheduleStartDate',
      label: intl.get(`${modelPrompt}.scheduleStartDate`).d('计划开始时间'),
    },
    {
      type: FieldType.string,
      name: 'scheduleEndDate',
      label: intl.get(`${modelPrompt}.scheduleEndDate`).d('预计完成时间'),
    },
    {
      name: 'actInitiationReason',
      lookupCode: 'YP.QIS.ACT_INITIATION_REASON',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actInitiationReason`).d('活动启动原因'),
    },
    {
      name: 'actObject',
      lookupCode: 'YP.QIS.ACT_OBJECT',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actObject`).d('活动对象'),
    },
    // {
    //   type: FieldType.string,
    //   name: 'materialCode',
    //   label: intl.get(`${modelPrompt}.materialCode`).d('物料号'),
    // },
  ],
  record: {
    dynamicProps: {
      selectable: record => record.get('initialManagementStatus') === 'NEW' || record.get('initialManagementStatus') === 'REJECTED',
    },
  },
});

// 详情-检验方案信息ds
const detailFormDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    // 活动基本信息
    {
      type: FieldType.string,
      name: 'initialManagementNum',
      label: intl.get(`${modelPrompt}.initialManagementNum`).d('活动编号'),
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialManagementDesc`).d('活动说明'),
      required: true,
      name: 'initialManagementDesc',
    },
    {
      name: 'initialManagementStatus',
      lookupCode: 'YP.QIS.INITIAI_MANAGE_STATUS',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialManagementStatus`).d('状态'),
      disabled: true,
    },
    {
      name: 'createdBy',
      type: FieldType.number,
    },
    {
      name: 'createdByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorName`).d('统筹人'),
      disabled: true,
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建日期'),
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂编码'),
      name: 'siteObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
      required: true,
      // computedProps: {
      //   disabled: ({ record }) => {
      //     return record.get('inspectSchemeId');
      //   },
      // },
    },
    {
      name: 'siteId',
      bind: 'siteObject.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteObject.siteCode',
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.siteName`).d('工厂描述'),
      bind: 'siteObject.siteName',
      disabled: true,
    },
    {
      name: 'materialPartsLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件号'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerPn',
      bind: 'materialPartsLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialPartsLov.materialCode',
    },
    {
      type: FieldType.string,
      name: 'phase',
      lookupCode: 'YP.QIS.REVIEW_STAGE',
      required: true,
      label: intl.get(`${modelPrompt}.phase`).d('项目阶段'),
    },
    {
      type: FieldType.string,
      name: 'model',
      required: true,
      label: intl.get(`${modelPrompt}.model`).d('产品名称'),
    },
    // {
    //   name: 'materialNameLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.materialNameLov`).d('内部零件号'),
    //   lovCode: 'MT.METHOD.MATERIAL',
    //   textField: 'materialName',
    //   lovPara: { tenantId },
    //   ignore: FieldIgnore.always,
    // },
    // {
    //   name: 'materialNameId',
    //   bind: 'materialNameLov.materialId',
    // },
    // {
    //   name: 'materialInsideName',
    //   label: intl.get(`${modelPrompt}.materialNameLov`).d('物料描述'),
    //   bind: 'materialNameLov.materialName',
    //   disabled: true,
    // },
    {
      name: 'scheduleStartDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.scheduleStartDate`).d('计划开始时间'),
      max: 'scheduleEndDate',
      required: true,
    },
    {
      name: 'scheduleEndDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.scheduleEndDate`).d('预计完成时间'),
      min: 'scheduleStartDate',
      required: true,
    },
    {
      name: 'actualStartDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartDate`).d('实际开始时间'),
      max: 'actualEndDate',
      disabled: true,
    },
    {
      name: 'actualEndDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndDate`).d('实际结束时间'),
      min: 'actualStartDate',
      disabled: true,
    },

    // 活动启动原因
    {
      name: 'actInitiationReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actInitiationReason`).d('活动启动原因'),
      lookupCode: 'YP.QIS.ACT_INITIATION_REASON',
      required: true,
    },
    {
      name: 'actInitiationRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actInitiationRemark`).d('启动条件补充说明'),
      required: true,
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      // max: 9,
    },
    // 活动内容
    {
      name: 'actObject',
      lookupCode: 'YP.QIS.ACT_OBJECT',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actObject`).d('活动对象'),
      required: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineCode',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => ({
          tenantId,
          //   siteId: record?.get('siteId'),
        }),
        required: ({ record }) => {
          return record.get('actObject') === 'PROCESS_CQGL';
        },
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineCode',
      bind: 'prodLineLov.prodLineCode',
    },
    {
      name: 'prodLineName',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名'),
      bind: 'prodLineLov.prodLineName',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
      name: 'customerObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerCode',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        lovPara: () => ({
          tenantId,
          //   siteId: record?.get('siteId'),
        }),
        // required: ({ record }) => {
        //   return record.get('actObject') === 'PRODUCT_COGL';
        // },
      },
    },
    {
      name: 'customerId',
      bind: 'customerObject.customerId',
    },
    {
      name: 'customerCode',
      bind: 'customerObject.customerCode',
    },
    {
      name: 'customerName',
      label: intl.get(`${modelPrompt}.customerName`).d('客户名称'),
      bind: 'customerObject.customerName',
      disabled: true,
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationCode`).d('工艺编号'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      textField: 'operationName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => ({
          tenantId,
          //   siteId: record?.get('siteId'),
        }),
        required: ({ record }) => {
          return record.get('actObject') === 'PROCESS_CQGL';
        },
      },
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationCode',
      bind: 'operationLov.operationCode',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
      label: intl.get(`${modelPrompt}.operationName`).d('工艺名称'),
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      name: 'supplierObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      textField: 'supplierCode',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        lovPara: () => ({
          tenantId,
          //   siteId: record?.get('siteId'),
        }),
        required: ({ record }) => {
          return record.get('actObject') === 'PARTS_COGL';
        },
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierObject.supplierId',
    },
    {
      name: 'supplierCode',
      bind: 'supplierObject.supplierCode',
    },
    {
      name: 'supplierName',
      bind: 'supplierObject.supplierName',
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationAllEquipmentFlag`).d('是否工艺下全部设备'),
      name: 'operationAllEquipmentFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.YN_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      // disabled: true,
      dynamicProps:{
        required: ({record})=>record.get('actObject')==='PROCESS_CQGL',
        disabled: ({record})=>record.get('actObject')!=='PROCESS_CQGL',
        defaultValue: ({record})=>record.get('actObject')==='PROCESS_CQGL'?'Y': null,
      },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备号'),
      name: 'equipmentObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentName',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('operationAllEquipmentFlag') === 'N';
        },
        multiple: ({ record }) => {
          return record.get('operationAllEquipmentFlag') === 'N'?',': false;
        },
        disabled: ({ record }) => {
          return record.get('operationAllEquipmentFlag') !== 'N';
        },
      },
    },
    {
      name: 'equipmentId',
      bind: 'equipmentObject.equipmentId',
    },
    {
      name: 'equipmentCode',
      bind: 'equipmentObject.equipmentCode',
    },
    {
      name: 'equipmentName',
      bind: 'equipmentObject.equipmentName',
      disabled: true,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
    {
      name: 'equipmentIdList',
    },
    {
      name: 'equipmentIdOriginList',
    },
    {
      name: 'equipmentNameList',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessType',
      lookupCode: 'YP.QIS.ZC_INSPECT_BUSINESS_TYPE',
      multiple: true,
      dynamicProps: {
        lovPara: () => ({
          tenantId,
          //   siteId: record?.get('siteId'),
        }),
        required: ({ record }) => {
          return record.get('actObject') === 'PROCESS_CQGL';
        },
        disabled: ({ record }) => {
          return ['PRODUCT_COGL', 'PARTS_COGL'].includes(record.get('actObject'));
        },
      },
    },
    {
      name: 'inspectSchemeCode',
      label: intl.get(`${modelPrompt}.inspectScheme`).d('检验方案'),
      type: FieldType.string,
      disabled: true,
    },


    {
      name: 'launchApplicationInformation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.launchApplicationInformation`).d('启动评审信息'),
      // disabled: true,
    },
    {
      name: 'actReport',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actReport`).d('活动报告'),
      // disabled: true,
    },
    {
      name: 'actReportEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.actReportEnclosure`).d('附件'),
      // max: 9,
    },
    {
      name: 'exitApplicationInformation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.exitApplicationInformation`).d('退出评审信息'),
      // disabled: true,
    },
    {
      name: 'schemeType',
      lookupCode: 'YP.QIS.SCHEME_TYPE',
      type: FieldType.string,
      // required: true,
      label: intl.get(`${modelPrompt}.schemeType`).d('检验方案类型'),
    },
    {
      name: 'schemeVersion',
      type: FieldType.string,
      // required: true,
      label: intl.get(`${modelPrompt}.schemeVersion`).d('检验方案版本'),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      name: 'currentFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeObjectType`).d('检验对象类型'),
      name: 'inspectSchemeObjectTypeObject',
      lookupCode: 'MT.QMS.INSPECT_SCHEME_OBJECT_TYPE',
      lovPara: {
        tenantId,
      },
      // required: true,
      computedProps: {
        disabled: ({ record }) => {
          return record.get('inspectSchemeId');
        },
      },
    },
    {
      name: 'inspectSchemeObjectType',
      bind: 'inspectSchemeObjectTypeObject.value',
    },
    {
      name: 'inspectSchemeObjectTypeDesc',
      bind: 'inspectSchemeObjectTypeObject.meaning',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeObject`).d('检验对象'),
      name: 'inspectSchemeObject',
      ignore: FieldIgnore.always,
      // required: true,
      computedProps: {
        lovCode: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'MT.METHOD.MATERIAL_CATEGORY';
            case 'MATERIAL':
              return 'MT.MATERIAL';
            case 'WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROCESS_WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROD_LINE':
              return 'MT.MODEL.PRODLINE';
            case 'AREA':
              return 'MT.MODEL.AREA';
            default:
              return 'MT.MATERIAL';
          }
        },
        lovPara: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            case 'MATERIAL':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            case 'WORKCELL':
              return {
                tenantId,
                siteId: record.get('siteId'),
                workcellType: 'STATION',
              };
            case 'PROCESS_WORKCELL':
              return {
                tenantId,
                siteId: record.get('siteId'),
                workcellType: 'PROCESS',
              };
            case 'PROD_LINE':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            case 'AREA':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            default:
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
          }
        },
        textField: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'description';
            case 'MATERIAL':
              return 'materialName';
            case 'WORKCELL':
              return 'workcellName';
            case 'PROCESS_WORKCELL':
              return 'workcellName';
            case 'PROD_LINE':
              return 'prodLineName';
            case 'AREA':
              return 'areaName';
            default:
              return 'materialName';
          }
        },
        valueField: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'materialCategoryId';
            case 'MATERIAL':
              return 'materialId';
            case 'WORKCELL':
              return 'workcellId';
            case 'PROCESS_WORKCELL':
              return 'workcellId';
            case 'PROD_LINE':
              return 'prodLineId';
            case 'AREA':
              return 'areaId';
            default:
              return 'materialId';
          }
        },
        disabled: ({ record }) => {
          return (
            !record.get('inspectSchemeObjectType') ||
            !record.get('siteId') ||
            record.get('inspectSchemeId')
          );
        },
      },
    },
    {
      name: 'inspectSchemeObjectId',
      computedProps: {
        bind: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'inspectSchemeObject.materialCategoryId';
            case 'MATERIAL':
              return 'inspectSchemeObject.materialId';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellId';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellId';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineId';
            case 'AREA':
              return 'inspectSchemeObject.areaId';
            default:
              return 'inspectSchemeObject.materialId';
          }
        },
      },
    },
    {
      name: 'inspectSchemeObjectCode',
      label: intl.get(`${modelPrompt}.inspectSchemeObjectCode`).d('检验对象编码'),
      computedProps: {
        bind: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'inspectSchemeObject.categoryCode';
            case 'MATERIAL':
              return 'inspectSchemeObject.materialCode';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellCode';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellCode';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineCode';
            case 'AREA':
              return 'inspectSchemeObject.areaCode';
            default:
              return 'inspectSchemeObject.materialCode';
          }
        },
      },
      disabled: true,
    },
    {
      name: 'inspectSchemeObjectName',
      computedProps: {
        bind: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'inspectSchemeObject.description';
            case 'MATERIAL':
              return 'inspectSchemeObject.materialName';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellName';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellName';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineName';
            case 'AREA':
              return 'inspectSchemeObject.areaName';
            default:
              return 'inspectSchemeObject.materialName';
          }
        },
      },
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'inspectSchemeObject.revisionFlag',
    },
    {
      // 物料版本
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      name: 'revisionCode',
      textField: 'description',
      valueField: 'description',
      // @ts-ignore
      noCache: true,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: item?.kid ? item.kid : uuid(),
                  description: item?.description ? item?.description : item,
                };
              });
            }
            if (record && firstlyQueryData.length === 1 && !record?.get('revisionCode')) {
              record?.set('revisionCode', firstlyQueryData[0].description);
            }
            return firstlyQueryData;
          },
        };
      },
      computedProps: {
        lookupUrl: ({ record }) => {
          if (
            !(record.get('inspectSchemeObjectType') === 'MATERIAL') ||
            !record.get('inspectSchemeObjectId') ||
            !record.get('siteId') ||
            !(record.get('revisionFlag') === 'Y')
          ) {
            return;
          }
          return `${
            BASIC.TARZAN_METHOD
          }${endUrl}/v1/${tenantId}/mt-material/site-material/limit/lov/ui?materialId=${record.get(
            'inspectSchemeObjectId',
          )}&siteIds=${record.get('siteId')}`;
        },
        disabled: ({ record }) => {
          return (
            !(record.get('inspectSchemeObjectType') === 'MATERIAL') ||
            !record.get('inspectSchemeObjectId') ||
            !record.get('siteId') ||
            !(record.get('revisionFlag') === 'Y') ||
            record.get('inspectSchemeId')
          );
        },
        required: ({ record }) => {
          return (
            record.get('inspectSchemeObjectType') === 'MATERIAL' &&
            record.get('inspectSchemeObjectId') &&
            record.get('siteId') &&
            record.get('revisionFlag') === 'Y'
          );
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.documentNum`).d('文件号'),
      name: 'documentNum',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.documentRevision`).d('文件版本号'),
      name: 'documentRevision',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlledNum`).d('受控号'),
      name: 'controlledNum',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      name: 'status',
      lookupUrl: `${BASIC.TARZAN_COMMON}${endUrl}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSPECT_SCHEME_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      name: 'remark',
    },
  ],
});

// 详情-行ds
const detailTableDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: false,
  fields: [
    // {
    //   name: 'responsibleDeptLov',
    //   type: FieldType.object,
    //   label: '部门',
    //   required: true,
    //   lovCode: 'YP.QIS.COMPANY_UNIT',
    //   ignore: FieldIgnore.always,
    //   textField: 'unitName',
    //   lovPara: {
    //     tenantId,
    //   },
    // },
    {
      name: 'sequence',
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'unitId',
      bind: 'responsibleEmLov.unitId',
    },
    {
      name: 'unitName',
      label: intl.get(`${modelPrompt}.unit`).d('部门'),
      type: FieldType.string,
      bind: 'responsibleEmLov.unitName',
      disabled: true,
    },
    {
      name: 'responsibleEmLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleEm`).d('姓名'),
      required: true,
      lovCode: 'HPFM.EMPLOYEE',
      textField: 'name',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'employeeId',
      bind: 'responsibleEmLov.employeeId',
    },
    {
      name: 'name',
      bind: 'responsibleEmLov.name',
      disabled: true,
    },
    {
      name: 'employeeNum',
      label: intl.get(`${modelPrompt}.employeeNum`).d('工号'),
      bind: 'responsibleEmLov.employeeNum',
      disabled: true,
    },
    {
      name: 'positionName',
      label: intl.get(`${modelPrompt}.positionName`).d('岗位'),
      bind: 'responsibleEmLov.positionName',
      disabled: true,
    },
    {
      name: 'email',
      label: intl.get(`${modelPrompt}.email`).d('电子邮箱'),
      bind: 'responsibleEmLov.email',
      disabled: true,
    },
    {
      name: 'mobile',
      label: intl.get(`${modelPrompt}.mobile`).d('电话号码'),
      bind: 'responsibleEmLov.mobile',
      disabled: true,
    },
    {
      name: 'roleAssign',
      label: intl.get(`${modelPrompt}.roleAssign`).d('角色分工'),
      required: true,
    },
  ],
});

// 退出提交ds
const submitFormDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: false,
  fields: [
    {
      name: 'actReport',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.actReport`).d('活动报告'),
    },
    {
      name: 'actReportEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.actReportEnclosure`).d('附件'),
      // max: 9,
    },
  ],
});

// 详情-检验项目tab的基础信息ds
const inspectionItemBasisDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  autoLocateFirst: true,
  fields: [
    { name: 'siteId' },
    { name: 'inspectBusinessTypeObjectIgnore', type: FieldType.string },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      required: true,
      computedProps: {
        lovPara: ({ record }) => {
          let inspectBusinessTypes = record.get('inspectBusinessTypeObjectIgnore') || null;

          if (inspectBusinessTypes && inspectBusinessTypes !== '') {
            inspectBusinessTypes = (record.get('inspectBusinessTypeObjectIgnore') || '').split(',');
          }
          return {
            tenantId,
            notInInspectBusinessTypes: inspectBusinessTypes,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'inspectBusinessType',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'inspectBusinessTypeDesc',
      bind: 'inspectBusinessTypeObject.inspectBusinessTypeDesc',
    },
    {
      name: 'inspectBusinessTypeRuleId',
      bind: 'inspectBusinessTypeObject.inspectBusinessTypeRuleId',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingDimension`).d('抽样维度'),
      name: 'samplingDimension',
      lookupCode: 'MT.QMS.SAMPLING_DIMENSION',
      defaultValue: 'INSPECT_ITEM_SAMPLING',
      // required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
      name: 'samplingMethodLov',
      lovCode: 'MT.QMS.SAMPLING_METHOD',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'samplingMethodDesc',
      // computedProps: {
      //   required: ({ record }) => {
      //     return record.get('samplingDimension') === 'ALL_SAMPLING';
      //   },
      // },
    },
    {
      name: 'samplingMethodId',
      bind: 'samplingMethodLov.samplingMethodId',
    },
    {
      name: 'samplingMethodDesc',
      type: FieldType.string,
      bind: 'samplingMethodLov.samplingMethodDesc',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectionDimension`).d('检验维度'),
      name: 'inspectionDimensionObject',
      multiple: true,
      computedProps: {
        // disabled: ({ record }) => {
        //   return !record?.get('inspectSchemeObjectType') || !record?.get('inspectSchemeObjectId');
        // },
      },
    },
    {
      name: 'inspectionDimension',
      bind: 'inspectionDimensionObject.value',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectionItem`).d('检验项目'),
      name: 'inspectionItemObject',
      lovCode: 'MT.QMS.INSPECT_ITEM_INFO',
      multiple: true,
      lovPara: { tenantId, dataType: 'CALCULATE_FORMULA' },
      // @ts-ignore
      noCache: true,
      computedProps: {
        // disabled: ({ record }) => {
        //   return record.get('inspectionDimensionObject').length === 0;
        // },
      },
    },
  ],
});

// 详情-新增检验维度的DS
const dimensionTableDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.object,
      name: 'ruleDtl',
    },
    {
      type: FieldType.string,
      name: 'disabledListWork',
    },
    {
      type: FieldType.string,
      name: 'disabledListMapRelationship',
    },
    {
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
    },
    {
      name: 'schemeStage',
      lookupCode: 'YP.QIS.SCHEME_STAGE',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.schemeStage`).d('导入类型'),
    },
    {
      name: 'initialActivityDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialActivityDocNum`).d('初期管理活动方案编号'),
      disabled: true,
    },
    {
      name: 'validDateFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.initialValidDateFrom`).d('初期管理活动有效期自'),
      disabled: true,
    },
    {
      name: 'validDateTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.initialValidDateTo`).d('初期管理活动有效期至'),
      disabled: true,
    },

    // 从formDs传入的定值
    {
      name: 'siteId',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      name: 'materialCode',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      name: 'materialName',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      name: 'materialId',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      name: 'revisionCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCategory`).d('物料类别'),
      name: 'categoryCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCategoryName`).d('物料类别描述'),
      name: 'materialCategoryName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.area`).d('区域'),
      name: 'areaObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.AREA',
      textField: 'areaName',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            (record.get('disabledListWork') || '').split(',').indexOf('areaObject') > -1 ||
            (record.get('ruleDtl') || {}).areaFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'areaId',
      bind: 'areaObject.areaId',
    },
    {
      name: 'areaCode',
      bind: 'areaObject.areaCode',
    },
    {
      name: 'areaName',
      bind: 'areaObject.areaName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            (record.get('disabledListWork') || '').split(',').indexOf('prodLineObject') > -1 ||
            (record.get('ruleDtl') || {}).prodLineFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineObject.prodLineId',
    },
    {
      name: 'prodLineCode',
      bind: 'prodLineObject.prodLineCode',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineObject.prodLineName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.process`).d('工序'),
      name: 'processObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.ROUTER_WORKCELL',
      textField: 'workcellName',
      optionsProps: dsProps => {
        const { queryFields, ...other } = dsProps;
        const newQueryFields = [
          {
            type: FieldType.object,
            label: intl.get(`${modelPrompt}.router`).d('工艺路线'),
            name: 'routerObject',
            ignore: FieldIgnore.always,
            lovCode: 'MT.METHOD.ROUTER',
            lovPara: { tenantId },
            textField: 'routerName',
            valueField: 'routerId',
          },
          {
            name: 'routerId',
            bind: 'routerObject.routerId',
          },
          {
            name: 'routerName',
            bind: 'routerObject.routerName',
          },
        ];
        (queryFields || []).forEach((item: any) => {
          if (item.name !== 'routerId') {
            newQueryFields.push(item);
          }
        });
        return {
          ...other,
          queryFields: newQueryFields,
        };
      },
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            workcellType: 'PROCESS',
          };
        },
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            (record.get('disabledListWork') || '').split(',').indexOf('processObject') > -1 ||
            (record.get('ruleDtl') || {}).processWorkcellFlag !== 'Y'
          );
        },
      },
    },

    {
      name: 'processWorkcellId',
      bind: 'processObject.workcellId',
    },
    {
      name: 'processWorkcellName',
      bind: 'processObject.workcellName',
    },
    {
      name: 'processWorkcellCode',
      bind: 'processObject.workcellCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.station`).d('工位'),
      name: 'stationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.ROUTER_STATION',
      textField: 'workcellName',
      optionsProps: dsProps => {
        const { queryFields, ...other } = dsProps;
        const newQueryFields = [
          {
            type: FieldType.object,
            label: intl.get(`${modelPrompt}.router`).d('工艺路线'),
            name: 'routerObject',
            ignore: FieldIgnore.always,
            lovCode: 'MT.METHOD.ROUTER',
            lovPara: { tenantId },
            textField: 'routerName',
            valueField: 'routerId',
          },
          {
            name: 'routerId',
            bind: 'routerObject.routerId',
          },
          {
            name: 'routerName',
            bind: 'routerObject.routerName',
          },
        ];
        (queryFields || []).forEach((item: any) => {
          if (item.name !== 'routerId') {
            newQueryFields.push(item);
          }
        });
        return {
          ...other,
          queryFields: newQueryFields,
        };
      },
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            workcellType: 'STATION',
          };
        },
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            (record.get('disabledListWork') || '').split(',').indexOf('stationObject') > -1 ||
            (record.get('ruleDtl') || {}).stationWorkcellFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'stationWorkcellId',
      bind: 'stationObject.workcellId',
    },
    {
      name: 'stationWorkcellCode',
      bind: 'stationObject.workcellCode',
    },
    {
      name: 'stationWorkcellName',
      bind: 'stationObject.workcellName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipment`).d('设备'),
      name: 'equipmentObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentName',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return (record.get('ruleDtl') || {}).equipmentFlag !== 'Y';
        },
      },
    },
    {
      name: 'equipmentId',
      bind: 'equipmentObject.equipmentId',
    },
    {
      name: 'equipmentName',
      bind: 'equipmentObject.equipmentName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      name: 'operationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.ROUTER_OPERATION',
      textField: 'operationName',
      lovPara: {
        tenantId,
      },
      optionsProps: dsProps => {
        const { queryFields, ...other } = dsProps;
        const newQueryFields = [
          {
            type: FieldType.object,
            label: intl.get(`${modelPrompt}.router`).d('工艺路线'),
            name: 'routerObject',
            ignore: FieldIgnore.always,
            lovCode: 'MT.METHOD.ROUTER',
            lovPara: { tenantId },
            textField: 'routerName',
            valueField: 'routerId',
          },
          {
            name: 'routerId',
            bind: 'routerObject.routerId',
          },
          {
            name: 'routerName',
            bind: 'routerObject.routerName',
          },
        ];
        (queryFields || []).forEach((item: any) => {
          if (item.name !== 'routerId') {
            newQueryFields.push(item);
          }
        });
        return {
          ...other,
          queryFields: newQueryFields,
        };
      },
      computedProps: {
        disabled: ({ record }) => {
          return (record.get('ruleDtl') || {}).operationFlag !== 'Y';
        },
      },
    },
    {
      name: 'operationId',
      bind: 'operationObject.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationObject.operationName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      name: 'supplierObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      textField: 'supplierName',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return (
            (record.get('disabledListMapRelationship') || '').split(',').indexOf('supplierObject') >
              -1 || (record.get('ruleDtl') || {}).supplierFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierObject.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierObject.supplierName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customer`).d('客户'),
      name: 'customerObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return (
            (record.get('disabledListMapRelationship') || '').split(',').indexOf('customerObject') >
              -1 || (record.get('ruleDtl') || {}).customerFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'customerId',
      bind: 'customerObject.customerId',
    },
    {
      name: 'customerName',
      bind: 'customerObject.customerName',
    },

    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherObject`).d('其他对象'),
      name: 'otherObject',
      computedProps: {
        disabled: ({ record }) => {
          return (record.get('ruleDtl') || {}).otherObjectFlag !== 'Y';
        },
      },
    },
  ],
});

const copyDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceInspectScheme`).d('来源检验方案'),
      name: 'inspectSchemeObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_SCHEME',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return record.get('inspectSchemeTmpObject');
        },
        required: ({ record }) => {
          return !record.get('inspectSchemeTmpObject');
        },
      },
    },
    {
      name: 'inspectSchemeId',
      bind: 'inspectSchemeObject.inspectSchemeId',
    },
    {
      name: 'inspectSchemeCode',
      bind: 'inspectSchemeObject.inspectSchemeCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeTemp`).d('检验方案模板'),
      name: 'inspectSchemeTmpObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_SCHEME_TMP',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return record.get('inspectSchemeObject');
        },
        required: ({ record }) => {
          return !record.get('inspectSchemeObject');
        },
      },
    },
    {
      name: 'inspectSchemeTmpId',
      bind: 'inspectSchemeTmpObject.inspectSchemeTmpId',
    },
  ],
});

// 复制检验业务tab
const copyBusinessTypeDS: () => DataSetProps = () => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.originInspectBusinessType`).d('来源检验业务类型'),
      name: 'originInspectBusinessTypeObject',
      required: true,
    },
    {
      name: 'originInspectBusinessType',
      bind: 'originInspectBusinessTypeObject.value',
    },
    {
      name: 'originInspectBusinessTypeDesc',
      bind: 'originInspectBusinessTypeObject.meaning',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetInspectBusinessType`).d('目标检验业务类型'),
      name: 'targetInspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.PERMISSION.INSPECT_BUS_TYPE_RULE_EDIT',
      required: true,
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            notInInspectBusinessTypes: record.get('inspectBusinessTypes'),
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'targetInspectBusinessType',
      bind: 'targetInspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'targetInspectBusinessTypeDesc',
      bind: 'targetInspectBusinessTypeObject.inspectBusinessTypeDesc',
    },
  ],
});

export {
  listTableDS,
  detailFormDS,
  inspectionItemBasisDS,
  dimensionTableDS,
  copyDS,
  copyBusinessTypeDS,
  detailTableDS,
  submitFormDS,
};
