/* eslint-disable no-unused-expressions */
// @ts-nocheck
/**
 * @Description: 工序作业平台
 * @Author: <<EMAIL>>
 * @Date: 2023-2-26 18:50:17
 */
import React, { useEffect, useState } from 'react';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
// import { closeTab } from 'utils/menuTab';
import { Divider } from 'choerodon-ui';
import { DataSet, Form, Modal, TextField, Output, Radio, Spin } from 'choerodon-ui/pro';
import { Icon } from 'hzero-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import request from 'utils/request';
import { queryMapIdpValue } from '../../../services/api';
import './index.module.less';
import { enterModalDS } from './stores';

let enterModal;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.OperationPlatformEnter';
// 工序作业平台
const OperationPlatformEnter = props => {
  const {
    enterModalDs, // 登录工位ds
    history,
    location: { state },
  } = props;

  const [fetchLoading, setFetchLoading] = useState(false); // 工位信息
  const [loginWkcInfo, setLoginWkcInfo] = useState({}); // 工位信息

  useEffect(() => {
    console.log(state);
    // 查询维度
    queryMapIdpValue({
      cardUsageList: 'HME_CARD_USAGE',
    }).then(res => {
      if (res && !res.failed) {
        window.localStorage.setItem('cardUsageList', JSON.stringify(res.cardUsageList));
      }
    });
    openEnterModal();
  }, []);

  // 打开工位登录弹框
  const openEnterModal = flag => {
    // enterModalDs.reset();
    enterModal = Modal.open({
      key: Modal.key(),
      title: '工位登录',
      destroyOnClose: true,
      closable: false,
      mask: false,
      style: { width: '450px' },
      contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
      className: 'operationPlatformEnterModal',
      children: (
        <Spin spinning={fetchLoading || false}>
          <Form
            className="enterModalForm"
            dataSet={enterModalDs}
            labelLayout="placeholder"
            labelWidth={80}
          >
            <TextField
              name="workStationCode"
              onEnterDown={e => onFetchWorkStation(e.target.value)}
              style={{ width: '80%' }}
              suffix={<Icon type="scan" style={{ fontSize: 14, color: 'white' }} />}
            />
          </Form>
          <Form
            className="enterModalForm"
            dataSet={enterModalDs}
            labelLayout="horizontal"
            columns={3}
            labelWidth="auto"
          >
            <Output name="workStationName" colSpan={6} />
            <Output label="工艺" colSpan={6} />
          </Form>
        </Spin>
      ),
      okProps: {
        loading: fetchLoading,
        style: {
          background: '#1b7efc',
        },
      },
      cancelProps: {
        loading: fetchLoading,
        style: {
          background: '#50819c',
          color: 'white',
        },
      },
      onOk: async () => {
        const enterModalData = enterModalDs.toData()[0];
        if (enterModalData && enterModalData.operationName && enterModalData.workStationCode) {
          if (enterModalDs?.toData()[0]?.operationList?.length === 1) {
            // 跳转工序作业平台
            history.push({
              pathname: `/hmes/repair-operation-platform/list`,
              state: {
                ...enterModalDs.toData()[0],
                selectOperation: enterModalDs?.toData()[0]?.operationList[0],
                cardUsageList: JSON.parse(window.localStorage.getItem('cardUsageList')),
              },
            });
          } else {
            const params = {
              workStationCode: enterModalData.workStationCode,
              operationFlag: 'Y',
              operation: enterModalDs
                ?.toData()[0]
                ?.operationList?.filter(
                  item => item.operationName === enterModalDs?.toData()[0]?.operationName,
                )[0],
            };
            return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-station`, {
              method: 'POST',
              body: params,
            }).then(res => {
              if (res && !res.failed) {
                // 跳转工序作业平台
                history.push({
                  pathname: `/hmes/repair-operation-platform/list`,
                  state: {
                    ...res,
                    selectOperation: res.operationList[0],
                    cardUsageList: JSON.parse(window.localStorage.getItem('cardUsageList')),
                  },
                });
              } else {
                notification.error({ message: res.message });
                return false;
              }
            });
          }
        } else {
          notification.error({ message: '请检查扫描工位和选择工艺' });
          return false;
        }
      },
      onCancel: () => {
        enterModal.close();
        if (flag !== 'change') {
          // closeTab(`/hmes/operation-platform`);
          history.push(`/workplace`);
        }
      },
    });
  };

  const onFetchWorkStation = value => {
    const params = {
      workStationCode: value,
    };
    setFetchLoading(true);
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-station`, {
      method: 'POST',
      body: params,
    }).then(res => {
      setFetchLoading(false);
      if (res && !res.failed) {
        if (res?.operationList?.length === 1) {
          enterModalDs.loadData([{ ...res, operationName: res.operationList[0].operationName }]);
          setLoginWkcInfo({
            ...res,
          });
        } else {
          enterModalDs.loadData([res]);
          setLoginWkcInfo({
            ...res,
          });
        }
        enterModal.update({
          children: (
            <Spin spinning={fetchLoading || false}>
              <Form
                className="enterModalForm"
                dataSet={enterModalDs}
                labelLayout="placeholder"
                labelWidth={80}
              >
                <TextField
                  name="workStationCode"
                  onEnterDown={e => onFetchWorkStation(e.target.value)}
                  style={{ width: '80%' }}
                  suffix={<Icon type="scan" style={{ fontSize: 14, color: 'white' }} />}
                />
              </Form>
              <Form
                className="enterModalForm"
                dataSet={enterModalDs}
                labelLayout="horizontal"
                columns={3}
                labelWidth="auto"
              >
                <Output name="workStationName" colSpan={6} />
                {res &&
                  res.operationList &&
                  res.operationList[0] &&
                  res.operationList.map((item, index) => {
                    return (
                      <Radio
                        dataSet={enterModalDs}
                        name="operationName"
                        label={index === 0 ? '工艺' : null}
                        value={item.operationName}
                        mode="button"
                        newLine={index % 3 === 0}
                      >
                        {item.description}
                      </Radio>
                    );
                  })}
              </Form>
            </Spin>
          ),
        });
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  return (
    <div className="operationPlatformEnter">
      <Header title={intl.get(`${modelPrompt}.title`).d('工序作业平台')}>
        {loginWkcInfo && loginWkcInfo.workStationName && (
          <div
            style={{
              display: 'flex',
              width: '20%',
              justifyContent: 'space-between',
              color: '#01e1ef',
            }}
          >
            <div>
              <span style={{ marginRight: '15px' }}>
                <Icon type="user" />
                &nbsp;人员&nbsp;-&nbsp;{loginWkcInfo.userName}
              </span>
              <Divider type="vertical" style={{ background: '#01e1ef' }} />
              <span style={{ marginLeft: '15px' }}>
                <Icon type="solution" />
                &nbsp;工位&nbsp;-&nbsp;{loginWkcInfo.workStationName}
              </span>
              {/* <span style={{ marginLeft: '20px' }}>班次：{loginWkcInfo.shiftCode}</span> */}
            </div>
          </div>
        )}
      </Header>
      <Content style={{ backgroundColor: '#2a445e', borderRadius: '0px' }} />
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.workBench', 'tarzan.common'],
})(
  withProps(
    () => {
      const enterModalDs = new DataSet({
        ...enterModalDS(),
      });
      return {
        enterModalDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(OperationPlatformEnter),
);
