/**
 * @Description: 区域维护汇总
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 14:18:05
 * @LastEditTime: 2022-11-09 14:37:39
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.model.org.area';

export default () => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${
          BASIC.TARZAN_MODEL
        }/v1/${getCurrentOrganizationId()}/mt-mod-area/limit-property/list/ui`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'areaId',
  fields: [
    {
      name: 'areaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaCode`).d('区域编码'),
    },
    {
      name: 'areaName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaName`).d('区域短描述'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('区域长描述'),
    },
    {
      name: 'areaType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaType`).d('区域类型'),
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=ORGANIZATION_REL_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'areaCategory',
      type: FieldType.string,
      textField: 'description',
      valueField: 'value',
      label: intl.get(`${modelPrompt}.areaCategory`).d('区域类别'),
      lookupCode: 'MT.AREA_CATEGORY',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
  ],
  queryFields: [
    {
      name: 'areaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaCode`).d('区域编码'),
    },
    {
      name: 'areaName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaName`).d('区域短描述'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('区域长描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});
