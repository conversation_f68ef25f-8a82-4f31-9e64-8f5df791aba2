/**
 * @Description: 检验追溯报表-主界面
 * @Author: <EMAIL>
 * @Date: 2024/1/12 10:43
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.inspectDocMaintain';
const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection:false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectTaskId',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/quality-trace/doc-info`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'traceObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.traceObjectType`).d('追溯对象类型'),
      lookupCode:'MT.QMS.REVIEW_OBJECT_TYPE',
      required: true,
      defaultValue:'MATERIAL_LOT',
    },
    {
      name: 'traceObjectCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceObjectCode`).d('追溯对象编码'),
      ignore: FieldIgnore.always,
      required: true,
      // lovCode: 'MT.MES.MAT_LOT_MATERIAL_LOT',
      dynamicProps: {
        disabled: ({ record }) => !record?.get('traceObjectType'),
        lovCode: ({ record }) =>  record?.get('traceObjectType')==='MATERIAL_LOT'?'MT.MATERIAL_LOT':'MT.EO',
        lovPara: ({ record }) => ({
          tenantId,
          traceObjectType: record?.get('traceObjectType'),
        }),
      },
    },
    {
      name: 'materialLotCode',
      bind: 'traceObjectCodeLov.materialLotCode',
      ignore: FieldIgnore.always,
    },
    {
      name: 'eoNum',
      bind: 'traceObjectCodeLov.eoNum',
      ignore: FieldIgnore.always,
    },
    {
      name: 'traceObjectCode',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if(record?.get('traceObjectType')==='MATERIAL_LOT'){
            return 'materialLotCode';
          }
          return 'eoNum';
        },
      },
    },
    {
      name: 'inspectBusinessTypeLov',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      multiple: true,
      dynamicProps: {
        disabled: ({ record }) => !record?.get('traceObjectType'),
        lovPara: () => ({
          tenantId,
          resultDimension: "RECORD_SAMPLE_VALUE",
        }),
      },
    },
    {
      name: 'inspectBusinessTypeList',
      type: FieldType.string,
      bind: 'inspectBusinessTypeLov.inspectBusinessType',
      multiple:true,
    },
  ],
  fields: [
    {
      name: 'inspectDocId',
      type: FieldType.number,
    },
    {
      name: 'inspectTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTaskCode`).d('检验任务编码'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
    },
    {
      name: 'inspectTaskStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTaskStatusDesc`).d('检验任务状态'),
    },
    {
      name: 'inspectResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResultDesc`).d('检验任务结果'),
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检验员'),
    },
    {
      name: 'actualStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('实际开始时间'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际结束时间'),
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单编码'),
    },
    {
      name: 'inspectDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocStatus`).d('检验单状态'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'firstInspectResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstInspectResult`).d('初评结果'),
    },
    {
      name: 'lastInspectResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastInspectResultDesc`).d('最终结果'),
    },
    {
      name: 'inspectInfoUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfoUserName`).d('报检人'),
    },
    {
      name: 'disposalTypeDesc',
      label: intl.get(`${modelPrompt}.disposalTypeDesc`).d('处置类型'),
    },
    {
      name: 'dispFunctionDesc',
      label: intl.get(`${modelPrompt}.dispFunctionDesc`).d('处置方式'),
    },
    {
      name: 'dispFunctionQty',
      label: intl.get(`${modelPrompt}.dispFunctionQty`).d('处置数量'),
    },
    {
      name: 'disposalUserName',
      label: intl.get(`${modelPrompt}.disposalUserName`).d('处置人'),
    },
    {
      name: 'disposalTime',
      label: intl.get(`${modelPrompt}.disposalTime`).d('处置时间'),
    },
    {
      name: 'reviewStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStatus`).d('审核状态'),
    },
    {
      name: 'reviewUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewUserName`).d('审核人'),
    },
    {
      name: 'reviewTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewTime`).d('审核时间'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'processWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序'),
    },
    {
      name: 'stationWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stationWorkcellName`).d('工位'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'areaName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaName`).d('区域'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备'),
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
    },
  ],
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectDocId',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/quality-trace/doc/info/item/line/for/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'inspectDocId',
      type: FieldType.number,
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectItemDesc`).d('检验项目描述'),
    },
    {
      name: 'dataTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.dataTypeDesc`).d('数据类型'),
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.trueValue`).d('符合值'),
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.falseValue`).d('不符合值'),
    },
    {
      name: 'warningValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.warningValue`).d('预警值'),
    },
    {
      name: 'inspectValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectValue`).d('检测值'),
    },
    {
      name: 'inspectResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectResultDesc`).d('检测结果'),
    },
  ],
});


export { headDS, lineDS };
