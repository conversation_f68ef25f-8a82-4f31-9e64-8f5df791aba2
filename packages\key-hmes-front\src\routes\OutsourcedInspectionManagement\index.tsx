import React, { useMemo, useState,useEffect } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Tabs } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC } from '@utils/config';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { openTab } from 'utils/menuTab';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import queryString from 'querystring';
import { productionDS, materialInformationDS, equipmentInformationDS, batteryPerformanceDS } from './stores';

const modelPrompt = 'OutsourcedInspectionManagement';
const tenantId = getCurrentOrganizationId();
const { TabPane } = Tabs;

const OutsourcedInspectionManagement = (props) => {
  const {
    productionDs,
    materialInformationDs,
    equipmentInformationDs,
    batteryPerformanceDs,
  } = props;

  const [tabKey, setTabKey] = useState('production')
  const tabKeyTable = {
    production: productionDs,
    materialInformation: materialInformationDs,
    equipmentInformation: equipmentInformationDs,
    batteryPerformance: batteryPerformanceDs,
  };
  const importCode = {
    production: 'APRS.OUT_INSPECT_PRODUCT',
    materialInformation: 'APRS.OUT_INSPECT_MATERIAL',
    equipmentInformation: 'APRS.OUT_INSPECT_EQUIPMENT',
    batteryPerformance: 'APRS.OUT_INSPECT_CELL',
  };

  const exportUrl = {
    production: '/aprs-outsourcing-inspect-product/export',
    materialInformation: '/aprs-outsourcing-inspect-material/export',
    equipmentInformation: '/aprs-outsourcing-inspect-equipment/export',
    batteryPerformance: '/aprs-outsourcing-inspect-cell/export',
  };

  const productionDsColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'cellModel',
      },
      {
        name: 'orderNumber',
      },
      {
        name: 'smallBatchNo',
      },
      {
        name: 'quantity',
      },
      {
        name: 'windInputQuantity',
        width: 120,
      },
      {
        name: 'windOutputQuantity',
        width: 120,
      },
      {
        name: 'windDefectiveQuantity',
        width: 120,
      },
      {
        name: 'windYieldRate',
        width: 120,
      },
      {
        name: 'windOperator',
        width: 120,
      },
      {
        name: 'windEquipmentLineNo',
        width: 120,
      },
      {
        name: 'windStartTime',
        width: 120,
      },
      {
        name: 'windEndTime',
        width: 120,
      },
      {
        name: 'packageInputQuantity',
        width: 120,
      },
      {
        name: 'packageOutputQuantity',
        width: 120,
      },
      {
        name: 'packageDefectiveQuantity',
        width: 120,
      },
      {
        name: 'packageYieldRate',
        width: 120,
      },
      {
        name: 'packageOperator',
        width: 120,
      },
      {
        name: 'packageEquipmentLineNo',
        width: 120,
      },
      {
        name: 'packageStartTime',
        width: 120,
      },
      {
        name: 'packageEndTime',
        width: 120,
      },
      {
        name: 'injectionInputQuantity',
        width: 120,
      },
      {
        width: 120,
        name: 'injectionOutputQuantity',
      },
      {
        name: 'injectionDefectiveQuantity',
        width: 120,
      },
      {
        name: 'injectionYieldRate',
        width: 120,
      },
      {
        name: 'injectionOperator',
        width: 120,
      },
      {
        name: 'injectionEquipmentLineNo',
        width: 120,
      },
      {
        name: 'injectionStartTime',
        width: 120,
      },
      {
        name: 'injectionEndTime',
        width: 120,
      },
      {
        name: 'transformInputQuantity',
        width: 120,
      },
      {
        name: 'transformOutputQuantity',
        width: 120,
      },
      {
        name: 'transformDefectiveQuantity',
        width: 120,
      },
      {
        width: 120,
        name: 'transformYieldRate',
      },
      {
        name: 'transformOperator',
        width: 120,
      },
      {
        name: 'transformEquipmentLineNo',
        width: 120,
      },
      {
        name: 'transformStartTime',
        width: 120,
      },
      {
        name: 'transformEndTime',
        width: 120,
      },
      {
        name: 'combinationInputQuantity',
        width: 120,
      },
      {
        name: 'combinationOutputQuantity',
        width: 120,
      },
      {
        name: 'combinationDefectiveQuantity',
        width: 120,
      },
      {
        name: 'combinationYieldRate',
        width: 120,
      },
      {
        name: 'combinationOperator',
        width: 120,
      },
      {
        name: 'combinationEquipmentLineNo',
        width: 120,
      },
      {
        name: 'combinationStartTime',
        width: 120,
      },
      {
        name: 'combinationEndTime',
        width: 120,
      },
      {
        name: 'sortInputQuantity',
        width: 120,
      },
      {
        name: 'sortOutputQuantity',
        width: 120,
      },
      {
        name: 'sortDefectiveQuantity',
        width: 120,
      },
      {
        name: 'sortYieldRate',
        width: 120,
      },
      {
        name: 'sortOperator',
        width: 120,
      },
      {
        name: 'sortEquipmentLineNo',
        width: 120,
      },
      {
        name: 'sortStartTime',
        width: 120,
      },
      {
        name: 'sortEndTime',
        width: 120,
      },
    ];
  }, []);

  const materialColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'cellModel',
      },
      {
        name: 'cellBatchNo',
      },
      {
        name: 'orderNumber',
      },
      {
        name: 'quantity',
      },
      {
        name: 'processWorkcell',
      },
      {
        name: 'materialCode',
      },
      {
        name: 'materialBomCode',
      },
      {
        name: 'rawMaterialName',
      },
      {
        name: 'rawMaterialBatchNo',
      },
      {
        name: 'inputQuantity',
      },
      {
        name: 'startFeedTime',
      },
      {
        name: 'operator',
      },
    ];
  }, []);

  const equipmentColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'orderNumber',
      },
      {
        name: 'materialBomCode',
      },
      {
        name: 'processRoute',
      },
      {
        name: 'processWorkcell',
      },
      {
        name: 'lineBodyNumber',
      },
      {
        name: 'equipmentCode',
      },
      {
        name: 'equipmentName',
      },
    ];
  }, []);

  const batteryPerformanceColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'cellNumber',
      },
      {
        name: 'injectionVolume',
      },
      {
        name: 'netVolume',
      },
      {
        name: 'capacity',
      },
      {
        name: 'ocv1',
      },
      {
        name: 'ir1',
      },
      {
        name: 'ocv1TestTime',
      },
      {
        name: 'ocv2',
      },
      {
        name: 'ir2',
      },
      {
        name: 'ocv2TestTime',
      },

      {
        name: 'kvalue',
      },
      {
        name: 'sortingInternalResistance',
      },
      {
        name: 'sortingVoltage',
      },
      {
        name: 'shellVoltage',
      },
      {
        name: 'thickness',
      },
      {
        name: 'width',
      },
      {
        name: 'positivePoleGlueHeight',
      },
      {
        name: 'negativePoleGlueHeight',
      },
      {
        name: 'positivePoleDistanceEdge',
      },
      {
        name: 'negativePoleDistanceEdge',
      },
      {
        name: 'forkPlateNumber',
      },
      {
        name: 'packageNo',
      },
      {
        name: 'forkPlateQuantity',
      },
      {
        name: 'packagingDate',
      },
    ];
  }, []);

  const goImport = () => {
    openTab({
      key: `/himp/commentImport/${importCode[tabKey]}`,
      title: intl.get(`${modelPrompt}.OutsourcedInspectionManagementImport`).d('委外检验数据导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tabKeyTable[tabKey].queryDataSet || !tabKeyTable[tabKey].queryDataSet.current) {
      return {};
    }
    const queryParmas = tabKeyTable[tabKey].queryDataSet.current.toData();
    return queryParmas;
  };

  const callback = (key) => {
    setTabKey(key)
    tabKeyTable[key].query();
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('委外检验数据管理')}>
        <PermissionButton
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
        >
          {intl.get(`${modelPrompt}.button.data.confirm`).d('导入')}
        </PermissionButton>
        <ExcelExport
          allBody
          // exportAsync
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}${exportUrl[tabKey]}`}
          method="GET"
          queryParams={getExportQueryParams}
        >
          {intl.get('htms.FrontConfig.button.export').d('导出')}
        </ExcelExport>
      </Header>
      <Content>
        <Tabs defaultActiveKey={tabKey} onChange={callback}>
          <TabPane tab={intl.get(`${modelPrompt}.production`).d('每批生产数')} key="production">
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={productionDs}
              columns={productionDsColumns}
              searchCode="production"
              customizedCode="production"
            />
          </TabPane>
          <TabPane tab={intl.get(`${modelPrompt}.materialInformation`).d('材料信息')} key="materialInformation">
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={materialInformationDs}
              columns={materialColumns}
              searchCode="materialInformation"
              customizedCode="materialInformation"
            />
          </TabPane>
          <TabPane tab={intl.get(`${modelPrompt}.equipmentInformation`).d('设备信息')} key="equipmentInformation">
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={equipmentInformationDs}
              columns={equipmentColumns}
              searchCode="equipmentInformation"
              customizedCode="equipmentInformation"
            />
          </TabPane>
          <TabPane tab={intl.get(`${modelPrompt}.batteryPerformance`).d('电池性能数据')} key="batteryPerformance">
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={batteryPerformanceDs}
              columns={batteryPerformanceColumns}
              searchCode="batteryPerformance"
              customizedCode="batteryPerformance"
            />
          </TabPane>
        </Tabs>,
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const productionDs = new DataSet({
        ...productionDS(),
      });
      const materialInformationDs = new DataSet({
        ...materialInformationDS(),
      });
      const equipmentInformationDs = new DataSet({
        ...equipmentInformationDS(),
      });
      const batteryPerformanceDs = new DataSet({
        ...batteryPerformanceDS(),
      });
      return {
        productionDs,
        materialInformationDs,
        equipmentInformationDs,
        batteryPerformanceDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(OutsourcedInspectionManagement),
);
