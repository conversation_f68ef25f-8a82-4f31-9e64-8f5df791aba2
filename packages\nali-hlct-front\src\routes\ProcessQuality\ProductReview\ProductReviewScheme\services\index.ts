/**
 * @Description: 产品审核方案
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存
 * @function SaveVerification
 * @returns {object} fetch Promise
 */
export function SaveVerification(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-scheme/save/ui?submitFlag=N`,
    method: 'POST',
  };
}
export function SubmitVerification(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-scheme/save/ui?submitFlag=Y`,
    method: 'POST',
  };
}

/**
 * 查询用户默认站点
 * @function GetDefaultSite
 * @returns {object} fetch Promise
 */
export function GetDefaultSite(): object {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/ui`,
    method: 'GET',
  };
}

/**
 * 根据materialId获取物料批对应的物料相关信息
 * @function QueryMaterialLotInfo
 * @returns {object} fetch Promise
 */
export function QueryMaterialLotInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-qa-feedbacks/mt-material-ca`,
    method: 'POST',
  };
}

export function SaveSchemeItem(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-review-item/save/ui`,
    method: 'POST',
  };
}

export function GetLineInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-scheme/his-line/ui`,
    method: 'GET',
  };
}
