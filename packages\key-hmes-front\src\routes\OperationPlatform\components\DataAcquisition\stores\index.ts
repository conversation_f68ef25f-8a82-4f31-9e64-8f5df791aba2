/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-17 11:31:28
 * @LastEditTime: 2023-07-20 10:37:38
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

const modelPrompt = 'modelPrompt_code';

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  forceValidate: true,
  key: 'tagId',
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('序号'),
    },
    {
      name: 'tagColumn',
      label: intl.get(`${modelPrompt}.tagColumn`).d('数据项'),
    },
    {
      name: 'tagCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCode`).d('数据项编码'),
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('数据项描述'),
    },
    {
      name: 'valueType',
    },
    {
      name: 'valueTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueType`).d('类型'),
    },
    {
      name: 'minimumValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.minimumValue`).d('下限'),
    },
    {
      name: 'maximalValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maximalValue`).d('上限'),
    },
    {
      name: 'valueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueList`).d('判定标准'),
    },
    {
      name: 'valueAllowMissing',
    },
    {
      name: 'tagValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagValue`).d('采集值'),
      computedProps: {
        required: ({ record }) => {
          return record.get('valueAllowMissing') !== 'Y';
        },
        type: ({ record }) => {
          if (record.get('valueType') === 'VALUE') {
            return FieldType.number;
          }
          return FieldType.string;
        },
      },
    },
    {
      name: 'tagCalculateResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCalculateResult`).d('是否合规'),
      lookupCode: 'MTDP.DATA_CALCULATE_RESULT',
      // required: true,
      // computedProps: {
      //   required: ({ record }) => {
      //     return record.get('tagValue');
      //   },
      //   disabled: ({ record }) => {
      //     return !['ENCLOSURE', 'TEXT'].includes(record.get('valueType'));
      //   },
      // },
    },
  ],
});

export { tableDS };
