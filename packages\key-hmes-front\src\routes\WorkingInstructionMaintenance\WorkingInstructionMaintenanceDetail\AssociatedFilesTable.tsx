/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-24 10:42:49
 * @LastEditTime: 2023-07-26 20:27:27
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useCallback, useRef, useEffect } from 'react';
import intl from 'utils/intl';
import { Table, Button, Modal } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import notification from 'utils/notification';
import moment from 'moment';
import { getCurrentUser } from 'utils/utils';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import {
  UploadFile,
  UploadFilePayload,
  DeleteFile,
} from '../services';
import Preview from './Preview';

const modelPrompt = 'hmes.workingInstructionMaintenance';

function validateModelFile(file: File) {
  const sizeLimimt = 30 * 1024 * 1024;
  const legalExts = ['.xlsx', '.xls', '.pdf', '.doc', '.docx', '.pptx', '.ppt', '.txt', '.wmv', '.mpg', '.mpeg', '.mov', '.rm', '.swf', '.flv', '.mp4', '.jpg', '.jpeg', '.bmp', '.webp', '.png', '.gif', '.svg'];
  if (file.size > sizeLimimt) {
    notification.error({ message: '文件尺寸过大' });
    return false;
  }
  const name = file.name.toLocaleLowerCase();
  if (!legalExts.some((ext) => name.endsWith(ext))) {
    notification.error({ message: '文件类型不正确' });
    return false;
  }
  return true;
}

/**
 * 将对象转为formData
 * @param {*} object
 * @return {*}
 */
export function obj2FormData(object) {
  const formData = new FormData();
  Object.keys(object).forEach(key => {
    const value = object[key];
    if (Array.isArray(value)) {
      value.forEach((subValue, i) => {
        formData.append(`${key}[${i}]`, subValue);
      });
    } else {
      formData.append(key, object[key]);
    }
  });
  return formData;
}

export default ({ attachmentUUID, ds, canEdit, setFileUploadLoading }) => {

  const inputModelRef = useRef<HTMLInputElement>(null);
  const uploadFile = useRequest(UploadFile(), { manual: true, needPromise: true });
  const deleteFile = useRequest(DeleteFile(), { manual: true, needPromise: true });

  useEffect(() => {
    setFileUploadLoading(uploadFile.loading || deleteFile.loading)
  }, [uploadFile.loading, deleteFile.loading])

  // const previewFile = useRequest(PreviewFile(), { manual: true, needPromise: true });

  // useEffect(() => {
  // const _previewParams: PreviewFilePayload = {
  //   bucketName: 'key-hmes',
  //   // url: `http://172.22.3.197:9000/hz-key-hmes/working-instruction-maintenance/8/DEFAULT/33af308350524b5f8e2067eacef34952@初盘执行多语言.xlsx`,
  //   // url: `http://172.22.3.197:9000/hz-key-hmes/working-instruction-maintenance/8/DEFAULT/d6e82f6f98cf416e835fa4ac188bce45@以组件的思维看待产品前端的设计.pdf`,
  //   url: `http://172.22.3.197:9000/hz-key-hmes/working-instruction-maintenance/8/DEFAULT/2d81f10e704048c097e798ef87335c00@hcme-功能设计_实物转移_新.doc`,
  //   storageCode: "DEFAULT",
  // };
  // previewFile.run({
  //   params: _previewParams,
  // }).then(res => {
  //   setContent(res)
  //   return;
  //   const blob = new Blob([res], { type: 'application/pdf' })
  //   // const file = new File([blob], '以组件的思维看待产品前端的设计.pdf', { type: 'application/pdf;charset:utf-8' })
  //   // console.log(blob);
  //   // const testUrl = window.URL.createObjectURL(blob);
  //   // console.log(testUrl);
  //   // window.open(testUrl)
  //   // setContent(res)
  //   // setContent(testUrl)
  //   // console.log(file);
  //   const reader = new FileReader();
  //   reader.readAsDataURL(blob);
  //   reader.addEventListener('load', (e) => {
  //     const base64 = e.target?.result as string;
  //     // console.log(base64);
  //     setContent(base64)
  //   })
  // })
  // }, [])

  const filterMaxNumber = () => {
    if (!ds.data.length) {
      return 0;
    }
    const lineNumberList: number[] = ds.map(record => {
      return record.get('lineNumber');
    })
    return lineNumberList.sort((a, b) => b - a)[0];
  }

  const onUploadModel = () => {
    const _files = inputModelRef.current?.files;
    if (!_files) {
      return;
    }
    if (_files.length > 1) {
      console.error('只能上传一个');
    }
    if (validateModelFile(_files[0])) {
      const _params: UploadFilePayload = {
        bucketName: 'key-hmes',
        // attachmentUUID: '88281dfc3a0d443b6b0ded5f2f9817f1a',
        attachmentUUID,
        file: _files[0],
        directory: 'working-instruction-maintenance',
        fileName: _files[0].name,
      }
      uploadFile.run({
        params: obj2FormData(_params),
      }).then(res => {
        const _splitArr = _files[0].name.split('.');
        ds.create({
          lineNumber: filterMaxNumber() + 10,
          fileName: _files[0].name,
          exts: `.${_splitArr[_splitArr.length - 1]}`,
          creationDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          createdByName: getCurrentUser().realName,
          fileUrl: res,
        }, 0)
      })
    }
  }

  const handleAdd = useCallback(
    () => {
      inputModelRef.current?.click();
    },
    [inputModelRef.current],
  );

  const handleRemove = useCallback(
    (record) => {
      deleteFile.run({
        params: [record.get('fileUrl')],
      }).then(() => {
        ds.remove(record);
      })
    },
    [],
  );

  const handlePreviewFile = (record) => {
    const extList = record?.get('fileName').split('.')

    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
      }),
      drawer: false,
      title: intl.get(`${modelPrompt}.title.preview`).d('预览'),
      destroyOnClose: true,
      style: {
        width: 720,
      },
      children: (
        <Preview
          url={record.get('fileUrl')}
          suffix={extList.length ? `.${extList[extList.length - 1]}` : ''}
        // suffix={record.get('exts')}
        />
      ),
    })
  }

  const handleDownload = useCallback(
    (record) => {
      fetch(record.get('fileUrl')).then(res => res.blob().then(blob => {
        const a = document.createElement('a');
        const url = window.URL.createObjectURL(blob);
        const filename = record.get('fileName');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
      }));
    },
    [],
  )


  const columns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <Button
            icon="add"
            disabled={!canEdit}
            funcType={FuncType.flat}
            onClick={handleAdd}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        width: 70,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              handleRemove(record!)
            }}
          >
            <Button icon="remove" disabled={!canEdit} funcType={FuncType.flat} size={Size.small} />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'lineNumber',
        width: 100,
      },
      {
        name: 'fileName',
      },
      {
        name: 'exts',
        width: 100,
        renderer: ({ record }) => {
          const extList = record?.get('fileName').split('.')
          return extList.length ? `${extList[extList.length - 1]}` : '';
        },
      },
      {
        name: 'creationDate',
        width: 140,
      },
      {
        name: 'createdByName',
        width: 140,
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        name: 'attrColumn',
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 230,
        renderer: ({ record }) => (
          <span className="action-link">
            <a onClick={() => handlePreviewFile(record!)}>
              {intl.get(`${modelPrompt}.action.preview`).d('预览')}
            </a>
            <a onClick={() => handleDownload(record!)}>
              {intl.get(`${modelPrompt}.action.download`).d('下载')}
            </a>
          </span>
        ),
      },
    ],
    [canEdit],
  );

  return (
    <>
      <Table
        dataSet={ds}
        columns={columns}
        filter={record => {
          return record.status !== 'delete';
        }}
      />
      <input
        style={{ display: 'none' }}
        type="file"
        ref={inputModelRef}
        onChange={onUploadModel}
      />
    </>
  );
};
