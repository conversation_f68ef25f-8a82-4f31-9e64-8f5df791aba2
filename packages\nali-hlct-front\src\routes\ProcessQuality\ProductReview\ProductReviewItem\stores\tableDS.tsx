/*
 * @Description: 产品审核项目-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-28 10:26:20
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-09 10:34:19
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.productReview.productReviewItem';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  selection: false,
  forceValidate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'productReviewItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemCode`).d('产品审核项目编码'),
    },
    {
      name: 'productReviewItemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemType`).d('项目类别'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_ITEM_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'productReviewItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemDesc`).d('项目描述'),
    },
    {
      name: 'productReviewMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewMethod`).d('测试方法'),
      lookupCode: 'MT.QMS.INSPECT_METHOD',
      lovPara: { tenantId },
    },
    {
      name: 'specRequire',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specRequire`).d('规格要求'),
    },
    {
      name: 'reviewFrequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewFrequency`).d('测试频率'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_FREQUENCY',
      lovPara: { tenantId },
    },
    {
      name: 'weightCoefficient',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.weightCoefficient`).d('特征加权系数'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_WEIGHT_COEFFICIENT',
      lovPara: { tenantId },
    },
    {
      name: 'recordDataFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recordDataFlag`).d('是否记录测试数据'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'fromOrtFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromOrtFlag`).d('是否来源于ORT'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('是否启用'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
  ],
  fields: [
    {
      name: 'productReviewItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemCode`).d('产品审核项目编码'),
    },
    {
      name: 'productReviewItemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemType`).d('项目类别'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_ITEM_TYPE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'productReviewItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemDesc`).d('项目描述'),
      required: true,
    },
    {
      name: 'productReviewMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewMethod`).d('测试方法'),
      lookupCode: 'MT.QMS.INSPECT_METHOD',
      lovPara: { tenantId },
    },
    {
      name: 'specRequire',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specRequire`).d('规格要求'),
    },
    {
      name: 'reviewFrequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewFrequency`).d('测试频率'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_FREQUENCY',
      lovPara: { tenantId },
    },
    {
      name: 'weightCoefficient',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.weightCoefficient`).d('特征加权系数'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_WEIGHT_COEFFICIENT',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'recordDataFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.recordDataFlag`).d('是否记录测试数据'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'fromOrtFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.fromOrtFlag`).d('是否来源于ORT'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.enableFlag`).d('是否启用'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-review-item/header/ui`,
      method: 'get',
    },
  },
});

export { tableDS };
