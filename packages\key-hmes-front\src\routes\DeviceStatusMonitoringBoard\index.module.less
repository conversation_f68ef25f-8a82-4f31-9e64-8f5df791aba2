.screen-container {
  width: 100%;
  height: 100%;

  :global {
    .page-content-wrap {
      height: 100%;
      margin: 0;
    }

    .page-content {
      height: 100%;
      margin: 0;
    }

    .c7n-spin-nested-loading {
      height: 100%;
      .c7n-spin-container {
        height: 100%;
      }
    }
  }
}

.dashboard-container {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
  overflow: hidden;
  color: #fff;
  background: url('./assets/background.png') center center no-repeat;
  background-position: top;
  background-size: 100% 100%;
}

.dashboard-title {
  display: flex;
  width: 100%;
  height: 10vh;
  min-height: 80px;
  justify-content: space-around;
  .dashboard-title-left {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    justify-content: left;
    align-items: center;
    width: 50%;
    height: 80%;
  }

  .dashboard-title-left-one {
    width: 8%;
    height: 80%;
    margin-left: 1vw;
    background-image: url('./assets/logo.png');
    background-repeat: no-repeat;
    background-size: 93% 100%;
  }
  .dashboard-title-left-two {
    width: 2%;
    height: 70%;
    background-image: url('./assets/splitLines.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .dashboard-title-left-three {
    width: 45%;
    position: relative;
    font-size: 40px;
    font-weight: 800;
    letter-spacing: 4px;
    line-height: 42.4px;
    color: #ffffff;
  }

  .dashboard-title-right {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    justify-content: right;
    align-items: center;
    width: 50%;
    height: 80%;
  }
  .dashboard-title-right-one {
    width: 50%;
    height: 60%;
    margin-top: 10px;
    display: flex;
    justify-content: end;
    align-items: center;
    background-image: url('./assets/searchBox.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .dashboard-title-right-one-text {
    width: 25%;
    font-size: 20px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.65);
    text-align: center;
  }
  .dashboard-title-left-one-dom {
    width: 53%;
    height: 80%;
    margin-top: 0.9vw;
  }
  .dashboard-title-right-two {
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  .dashboard-title-right-two-time {
    width: 70%;
    .dashboard-title-right-two-time-text {
      text-shadow: rgba(0, 129, 255, 0.4) 0 0 21px, rgb(0, 129, 255) 0 0 21px;
      font-size: 1vw;
      font-weight: 600;
      text-align: center;
    }
  }
  .dashboard-title-right-two-home {
    width: 10%;
    height: 50%;
    background-image: url('./assets/homeIcon.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.dashboard-content {
  width: 100%;
  height: calc(100% - 10vh);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-wrap: wrap;

  .dashboard-item-left {
    width: 67%;
    height: 100%;
    margin-left: 1.5vw;
    margin-right: 1.5vw;
    ::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
  }

  .class-main-title {
    width: 40%;
    height: 5%;
    display: flex;
    justify-content: left;
    align-items: center;
    .class-main-title-icon {
      width: 9%;
      height: 100%;
      background-image: url('./assets/deviceStatusIco.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .class-main-title-text {
      width: 91%;
      height: 100%;
      padding-top: 0.44vw;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: 2.4px;
      text-shadow: 0px -3px 6px rgba(0, 102, 255, 0.6);
      text-align: left;
      vertical-align: middle;
    }
  }

  //设备状态
  .class-main {
    width: 100%;
    height: 90%;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(6, 14.1%);
    scroll-behavior: smooth;
    gap: 35px;
  }
  .class-main-item {
    background-image: url('./assets/mainItemCard.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .class-main-item-name {
      height: 36%;
      opacity: 1;
      font-size: 18px;
      font-weight: 500;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      position: relative;
      overflow: hidden;
      width: 68%;
      box-sizing: border-box;
    }

    .class-main-item-name.animated::after {
      content: attr(data-text);
      position: absolute;
      left: 0;
      white-space: nowrap;
      padding: 10px;
      animation: slideLeftRight 10s linear infinite;
    }
    
    @keyframes slideLeftRight {
      0% {
        transform: translateX(0);
      }
      50% {
        transform: translateX(calc(-100% + 100px));
      }
      100% {
        transform: translateX(0);
      }
    }

    .class-main-item-code {
      height: 30%;
      opacity: 1;
      font-size: 16px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 21.95px;
      color: rgba(255, 255, 255, 1);
      text-align: left;
      vertical-align: top;
      display: -webkit-box;
      -webkit-box-pack: start;
      -webkit-box-align: center;
      padding-left: 0.1rem;
      margin-top: 6px;
    }

    .class-main-item-status {
      height: 15%;
      display: flex;
      -webkit-box-pack: start;
      -webkit-box-align: center;
      padding-left: 0.1rem;
      align-items: center;

      .class-main-item-status-badge {
        border: 8px solid;
        display: inline-block;
        text-align: center;
        border-radius: 50%;
        margin-right: 0.05rem;
      }
      .class-main-item-status-meaning {
        font-size: 16px;
        text-align: center;
      }
    }
  }

  .dashboard-item-right {
    width: 30%;
    height: 100%;
  }

  .dashboard-item-right-one {
    width: 100%;
    height: 35%;
    .dashboard-item-right-one-title {
      width: 100%;
      height: 2vw;
      background-image: url('./assets/backgroundTitle.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .dashboard-item-right-one-title-text {
        width: 90%;
        height: 100%;
        padding-top: 0.4vw;
        padding-left: 2.2vw;
        font-size: 20px;
        font-weight: 600;
        letter-spacing: 1px;
        line-height: 26px;
        color: #ffffff;
        text-align: left;
        vertical-align: middle;
      }
    }
    .dashboard-item-right-one-content {
      width: 100%;
      height: 85%;
    }
  }

  .dashboard-item-right-two {
    width: 100%;
    height: 25%;
    .dashboard-item-right-two-title {
      width: 100%;
      height: 2vw;
      background-image: url('./assets/backgroundTitle.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .dashboard-item-right-two-title-text {
        width: 90%;
        height: 100%;
        padding-top: 0.4vw;
        padding-left: 2.2vw;
        font-size: 20px;
        font-weight: 600;
        letter-spacing: 1px;
        line-height: 26px;
        color: #ffffff;
        text-align: left;
        vertical-align: middle;
      }
    }
    .dashboard-item-right-two-content {
      width: 90%;
      height: 63%;
      margin: 1vw;
      background-image: url('./assets/equementOee.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-around;
      align-items: center;

      .dashboard-item-right-two-content-item {
        width: 25%;
        height: 100%;

        .dashboard-item-right-two-content-text {
          height: 40%;
          font-size: 14px;
          font-weight: 400;
          letter-spacing: 0;
          color: rgba(255, 255, 255, 0.7);
          display: -webkit-box;
          -webkit-box-pack: center;
          -webkit-box-align: center;
        }

        .dashboard-item-right-two-content-number {
          height: 60%;
          width: 100%;
          font-size: 24px;
          font-weight: 700;
          letter-spacing: 0;
          color: #59b2ff;
          display: -webkit-box;
          -webkit-box-pack: center;
          -webkit-box-align: start;
          position: relative;
          .dashboard-item-right-two-content-number-text {
            margin-left: 10%;
            font-size: 20px;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.8);
            display: -webkit-box;
            -webkit-box-pack: center;
            -webkit-box-align: center;
            position: absolute;
            right: -6%;
          }
        }
      }
    }
  }

  .dashboard-item-right-three {
    width: 100%;
    height: 35%;
    .dashboard-item-right-three-title {
      width: 90%;
      height: 2vw;
      background-image: url('./assets/backgroundTitle.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .dashboard-item-right-three-title-text {
        width: 90%;
        height: 100%;
        padding-top: 0.4vw;
        padding-left: 2.2vw;
        font-size: 20px;
        font-weight: 600;
        letter-spacing: 1px;
        line-height: 26px;
        color: #ffffff;
        text-align: left;
        vertical-align: middle;
      }
    }
    .dashboard-item-right-three-content {
      width: 95%;
      height: 90%;
      .dashboard-item-right-three-content-flag {
        width: 100%;
        height: 20%;
        margin: 1.5vw 0;
        background-image: url('./assets/equipmentFlag.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: end;
        align-items: baseline;
      }
      .dashboard-item-right-three-content-person {
        width: 100%;
        height: 20%;
        margin: 1.5vw 0;
        background-image: url('./assets/equementPerson.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: end;
        align-items: baseline;
      }
      .dashboard-item-right-three-content-local {
        width: 100%;
        height: 20%;
        margin: 1.5vw 0;
        background-image: url('./assets/equementLocal.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: end;
        align-items: baseline;
      }
      .dashboard-item-right-three-content-name {
        width: 30%;
        font-size: 16px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 21.95px;
        color: rgba(255, 255, 255, 1);
        vertical-align: top;
      }
      .dashboard-item-right-three-content-value {
        width: 40%;
        opacity: 1;
        text-shadow: 0px 0px 21px rgba(0, 129, 255, 0.4), 0px 0px 21px rgba(0, 129, 255, 1);
        /** 文本1 */
        font-size: 24px;
        font-weight: 500;
        letter-spacing: 0px;
        line-height: 32.93px;
        color: rgba(112, 112, 112, 1);
        vertical-align: top;
      }
    }
  }
}

.dashboard-card {
  width: 100%;
  height: 100%;
  padding: 5px;
  overflow: hidden;
  // border: 1px solid red;
  .dashboard-card-title {
    width: 100%;
  }

  .dashboard-card-content {
    width: 100%;
    height: 100%;
  }
}

.top-select {
  width: 300px;
  height: 45px !important;

  :global {
    .c7n-pro-select-wrapper {
      width: 200px;
      height: 40px !important;
      background: transparent !important;
      border: none !important;
    }

    .c7n-pro-select {
      height: 27px !important;
      color: #33c5ff !important;
      font-weight: 600 !important;
      line-height: 40px !important;
      outline: none !important;
      border: none !important;
      font-size: 18px !important;
    }

    .c7n-pro-select:focus {
      border: none !important;
      box-shadow: none !important;
    }

    .c7n-pro-select:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .c7n-pro-select-multiple-block {
      background: #154ea0;
    }

    input::-webkit-input-placeholder {
      padding-left: 10px !important;
      /* placeholder颜色 */
      color: #33c5ff !important;
      font-weight: 600 !important;
      /* placeholder字体大小 */
      font-size: 18px !important;
      line-height: 40px !important;
    }
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-suffix-button .c7n-pro-select-suffix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-prefix-button .c7n-pro-select-suffix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-suffix-button .c7n-pro-select-prefix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-prefix-button .c7n-pro-select-prefix .icon,
    .c7n-pro-calendar-picker-range-split,
    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-suffix-button .c7n-pro-calendar-picker-suffix .icon {
      font-size: 35px;
    }

    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-select-suffix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-select-prefix .icon,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-input-number-inner-button .icon,
    .c7n-pro-calendar-picker-range-split,
    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper label input,
    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-suffix-button .c7n-pro-calendar-picker-suffix .icon {
      color: #00FFFF;
    }

    .c7n-pro-calendar-picker-range-split::before {
      display: none;
    }

    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper label .c7n-pro-calendar-picker {
      border: 0;
    }
    .c7n-pro-calendar-picker-wrapper.c7n-pro-calendar-picker-wrapper label input {
      color: #33c5ff;
      font-weight: 600;
      font-size: 16px;
    }

  }
}

.rank-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  padding: 10px 10px 20px;

  .rank-item {
    display: flex;
    align-items: center;
    width: 50%;
    height: 20%;
    padding-right: 25px;

    .rank-item-sort {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 20px;
      color: #fff;
      font-size: 12px;
      background: #4c6277;
      border-radius: 50%;
    }

    .rank-item-name {
      flex-shrink: 0;
      width: 100px;
      padding: 0 4px 0;
      color: #fff;
      font-size: 14px;
    }

    .rank-item-progress {
      padding: 0 4px 0;
    }

    :global {
      .c7n-progress {
        padding: 3px !important;
        background: #264160 !important;
        border-radius: 50px !important;
      }

      .c7n-progress-inner {
        background: #264160 !important;

        .c7n-progress-bg {
          height: 8px !important;
        }
      }

      .c7n-progress-outer {
        display: flex;
        align-items: center;
      }

      .c7n-progress-line {
        font-size: 12px !important;
      }
    }

    .rank-item-quantity {
      flex-shrink: 0;
      width: 50px;
      padding: 0 5px 0;
      text-align: right;
    }

    .rank-item-percent {
      flex-shrink: 0;
      width: 30px;
      padding: 0 5px 0;
      text-align: right;
    }
  }
}

.my-scroll-board-2 {
  :global {
    .dv-scroll-board {
      .header {
        .header-item {
          height: 2.5vw !important;
          font-weight: bold !important;
          font-size: 12px !important;
          line-height: 2.5vw !important;
        }
      }

      .rows {
        height: auto !important;
        .row-item {
          font-size: 12px !important;
          // border: 1px solid #1e3e67;
        }
      }
    }
  }
}
.my-scroll-board-title {
  width: 100%;
  height: 7%;
  opacity: 1;
  /** 文本1 */
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 28.96px;
  color: rgba(0, 255, 244, 1);
  text-align: center;
  vertical-align: top;
  margin-top: 1%;
}
.my-scroll-board-table {
  :global {
    .dv-scroll-board .header .header-item:nth-child(1) {
      //width: 90px !important;
    }
    .dv-scroll-board .rows .ceil:nth-child(1) {
      //width: 90px !important;
    }
    .dv-scroll-board {
      .rows {
        height: auto !important;
        .row-item {
          font-size: 14px !important;
          height: 45px !important;
          line-height: 45px !important;
        }
      }
    }

    .dv-scroll-board .rows .ceil {
      padding: 0 5px !important;
    }
  }
}

.checkSupplierText {
  display: block;
  margin-bottom: 20px; /* 留出足够的空间供背景图片显示 */
}

.checkSupplierImage {
  display: inline-block;
  width: 100px; /* 或者你想要的任何宽度 */
  height: 100px; /* 或者你想要的任何高度 */
}

.dashboard-right-chart{
  margin: 2%;
  border-radius: 50%; /* 圆形结构 */
  background-image: url('./assets/ring.png');
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: 46% 85%;
}

.dashboard-right-chart-full-screen{
  border-radius: 50%; /* 圆形结构 */
  background-image: url('./assets/ring.png');
  background-size: 61% 80%;
  background-repeat: no-repeat;
  background-position: 50% 25%;
}