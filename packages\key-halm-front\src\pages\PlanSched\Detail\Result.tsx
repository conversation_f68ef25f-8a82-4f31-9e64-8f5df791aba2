import React, { useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Button, Table, Modal } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { TableColumnTooltip, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { yesOrNoRender } from 'utils/renderer';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/interface';
import request from 'utils/request';
import notification from 'utils/notification';
import { statusColors } from 'alm/utils/constants';
import { HALM_ORI } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

import getLang from '../Langs';
import { createWo, updateResult, skip } from '../api';

import releaseImg from '../assets/release.svg';
import releaseDisImg from '../assets/release-dis.svg';
import skipImg from '../assets/skip.svg';
import skipDisImg from '../assets/skip-dis.svg';

const organizationId = getCurrentOrganizationId();

function Result(props) {
  const { header, resultTableDs: tableDs, editRecord, setEditRecord } = props;
  const { schedId, cycleTypeCode, schedStartDate } = header;

  useEffect(() => {
    if (schedId) {
      tableDs.setQueryParameter('planSchedId', schedId);
      tableDs.cycleTypeCode = cycleTypeCode;
      tableDs.queryDataSet.current?.set('planStartTime', { planStartTimeFrom: schedStartDate });
      tableDs.query();
    }
  }, [schedId]);

  // 下达 （创建工单）
  const handleRelease = () => {
    if (editRecord) {
      Modal.confirm({
        key: Modal.key(),
        title: getLang('NOTICE'),
        children: getLang('RELEASE_TEXT'),
        onOk: () => {
          handleCancel(editRecord);
          handleCreateWo();
        },
      });
    } else {
      handleCreateWo();
    }
  };

  const handleCreateWo = () => {
    createWo({
      resultIdList: tableDs.selected.map(i => i.get('resultId')),
    }).then(res => {
      if (res === false) {
        notification.error({
          message: getLang('RELEASE_ERROR'),
        });
        // 刷预测结果列表
        tableDs.query();
      } else if (res && !res.failed) {
        queryAfterOpSucc();
      } else {
        notification.error({
          message: res.message,
        });
      }
    });
  };

  // 结果行操作成功后查询
  const queryAfterOpSucc = () => {
    notification.success({});
    tableDs.clearCachedSelected();
    tableDs.unSelectAll();
    // 重新查询
    tableDs.query(tableDs.currentPage);
  };

  const handleDelete = record => {
    Modal.confirm({
      key: Modal.key(),
      title: getLang('RESULT_DEL_CONFIRM'),
      children: getLang('RESULT_DEL_TEXT'),
      onOk: async () => {
        await tableDs.delete(record, false);
      },
    });
  };

  const handleEdit = record => {
    record.setState('editing', true);
    setEditRecord(record);
  };

  // 取消行编辑
  const handleCancel = record => {
    record.setState('editing', false);
    record.reset();
    setEditRecord(null);
  };

  const handleSave = async record => {
    if (await record.validate(true)) {
      updateResult(record.toData()).then(res => {
        if (res && !res.failed) {
          record.setState('editing', false);
          setEditRecord(null);
          tableDs.query();
        } else {
          notification.error({
            message: res.message,
          });
        }
      });
    }
  };

  const handleSkip = () => {
    Modal.confirm({
      key: Modal.key(),
      title: getLang('NOTICE'),
      children: getLang('SKIP_FIRST_TEXT'),
      onOk: () => {
        if (editRecord) {
          Modal.confirm({
            key: Modal.key(),
            title: getLang('NOTICE'),
            children: getLang('SKIP_TEXT'),
            onOk: () => {
              handleCancel(editRecord);
              skipResult();
            },
          });
        } else {
          skipResult();
        }
      },
    });
  };

  const handleDeletes = () => {
    Modal.confirm({
      key: Modal.key(),
      title: getLang('RESULT_DEL_CONFIRM'),
      children: getLang('RESULT_DEL_TEXT_LIST'),
      onOk: handleDeleteList,
    });
  };

  const handleDeleteList = async() => {
    const resultIds = tableDs.selected.map(record => record.get('resultId'));
    request(`${HALM_ORI}/v1/${organizationId}/plan-sched-results/batch/delete`, {
      method: 'POST',
      body: resultIds,
    }).then(res => {
      if (res) {
        tableDs.query();
      } else {
        notification.error({
          message: res?.message,
        });
      }
    })
  }

  const skipResult = () => {
    skip(tableDs.selected.map(i => i.get('resultId'))).then(res => {
      if (res && res.failed) {
        if (res.code === 'alm.error.ori.check_plan_status') {
          Modal.confirm({
            title: getLang('NOTICE'),
            children: res.message,
            footer: okBtn => <>{okBtn}</>,
          });
        } else {
          notification.error({
            message: res.message,
          });
        }
      } else {
        queryAfterOpSucc();
      }
    });
  };

  const handleToPlanDetail = id => {
    props.history.push(`/aori/maintain-plans/detail/${id}`);
  };

  const handleToWoDetail = (id, woNum) => {
    props.history.push({
      pathname: `/amtc/work-order/detail/${id}/${woNum}`,
    });
  };

  const timeColumns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'maintainPlanName',
        tooltip: TableColumnTooltip.overflow,
        width: 250,
        renderer: ({ value, record }) => {
          return <a onClick={() => handleToPlanDetail(record?.get('maintainPlanId'))}>{value}</a>;
        },
      },
      {
        name: 'maintainPlanCode',
        tooltip: TableColumnTooltip.overflow,
        width: 150,
      },
      {
        name: 'woTypeName',
        tooltip: TableColumnTooltip.overflow,
        width: 150,
      },
      {
        name: 'woNum',
        tooltip: TableColumnTooltip.overflow,
        width: 150,
        renderer: ({ value, record }) => {
          return <a onClick={() => handleToWoDetail(record?.get('woId'), value)}>{value}</a>;
        },
      },
      {
        name: 'reminderTime',
        width: 150,
      },
      {
        name: 'standardTime',
        width: 150,
      },
      {
        name: 'planStartTime',
        width: 150,
        editor: record => record.getState('editing'),
      },
      {
        name: 'forecastTime',
        width: 150,
      },
      {
        name: 'planStatus',
        align: ColumnAlign.center,
        renderer: ({ value, text }) => {
          const newValue = `${value}_FORECAST`;
          return (
            <Tag
              style={{ color: statusColors[newValue]?.fontColor, border: 0 }}
              color={statusColors[newValue]?.bgColor}
            >
              {text}
            </Tag>
          );
        },
      },
      {
        name: 'planStopTime',
        width: 150,
        hidden: true,
      },
      {
        name: 'assetDescAndLabel',
        tooltip: TableColumnTooltip.overflow,
        width: 250,
        hidden: true,
      },
      {
        name: 'locationNameAndCode',
        tooltip: TableColumnTooltip.overflow,
        width: 250,
        hidden: true,
      },
      {
        name: 'cycleInterval',
        tooltip: TableColumnTooltip.overflow,
        width: 80,
        hidden: true,
      },
      {
        name: 'cycleUomMeaning',
        tooltip: TableColumnTooltip.overflow,
        width: 100,
        hidden: true,
      },
      { name: 'editFlag', renderer: ({ value }) => yesOrNoRender(value), hidden: true },
      {
        header: getLang('OPTION'),
        width: 100,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          const resultId = record?.get('resultId');
          const isEditing = record?.getState('editing');
          const planStatus = record?.get('planStatus');
          let disOthers = false;
          // 一行处于编辑 其余行按钮置灰
          if (editRecord) {
            disOthers = resultId !== editRecord.get('resultId');
          }
          const dis = {
            disabled: disOthers || !['DRAFT', 'SKIP'].includes(planStatus), // 仅待下达 跳过可删除、编辑
          };
          return (
            <span className="action-link">
              {!isEditing && (
                <a onClick={() => handleDelete(record)} {...dis}>
                  {getLang('DELETE')}
                </a>
              )}
              {!isEditing && (
                <a onClick={() => handleEdit(record)} {...dis}>
                  {getLang('EDIT')}
                </a>
              )}
              {isEditing && <a onClick={() => handleCancel(record)}>{getLang('CANCEL')}</a>}
              {isEditing && <a onClick={() => handleSave(record)}>{getLang('SAVE')}</a>}
            </span>
          );
        },
      },
    ],
    [editRecord],
  );

  const meterColumns: ColumnProps[] = useMemo(() => {
    const cols = [...timeColumns];
    cols.splice(
      4,
      2,
      {
        name: 'reminderValue',
        tooltip: TableColumnTooltip.overflow,
        width: 120,
      },
      {
        name: 'currentValue',
        tooltip: TableColumnTooltip.overflow,
        width: 120,
        editor: record => record.getState('editing'),
      },
      {
        name: 'standardValue',
        tooltip: TableColumnTooltip.overflow,
        width: 120,
      },
    );
    cols.push({
      name: 'meterName',
      tooltip: TableColumnTooltip.overflow,
      width: 120,
      hidden: true,
    });
    return cols;
  }, [editRecord]);

  const releaseButton = (
    <Button onClick={handleRelease} key="release" disabled={tableDs.selected.length === 0}>
      <img
        src={releaseImg}
        alt="release"
        style={{
          marginRight: '4px',
          display: tableDs.selected.length > 0 ? 'inline-block' : 'none',
        }}
      />
      <img
        src={releaseDisImg}
        alt="releaseDis"
        style={{
          marginRight: '4px',
          display: tableDs.selected.length > 0 ? 'none' : 'inline-block',
        }}
      />
      {getLang('RELEASE')}
    </Button>
  );

  const skipButton = (
    <Button onClick={handleSkip} key="skip" disabled={tableDs.selected.length === 0}>
      <img
        src={skipImg}
        alt="release"
        style={{
          marginRight: '4px',
          display: tableDs.selected.length > 0 ? 'inline-block' : 'none',
        }}
      />
      <img
        src={skipDisImg}
        alt="releaseDis"
        style={{
          marginRight: '4px',
          display: tableDs.selected.length > 0 ? 'none' : 'inline-block',
        }}
      />
      {getLang('SKIP')}
    </Button>
  );

  const deleteButton = (
    <Button onClick={handleDeletes} disabled={tableDs.selected.length === 0} icon="delete_black-o">
      批量删除
    </Button>
  );

  const buttons = [skipButton, deleteButton ,releaseButton];

  return (
    <>
      <Table
        key="planSchedResultList"
        dataSet={tableDs}
        columns={cycleTypeCode === 'TIME_CYCLE' ? timeColumns : meterColumns}
        customizable
        customizedCode={
          cycleTypeCode === 'TIME_CYCLE'
            ? 'AORI.PALN_SCHED.RESULT_LIST_TIME'
            : 'AORI.PALN_SCHED.RESULT_LIST_METER'
        }
        buttons={buttons}
        queryBar={TableQueryBarType.filterBar}
        searchCode="AORI.PALN_SCHED.RESULT_LIST"
        queryBarProps={{
          fuzzyQueryPlaceholder: getLang('KEYWORD_PLACEHOLDER'),
          queryFieldsLimit: 4,
        }}
      />
    </>
  );
}

export default observer(Result);
