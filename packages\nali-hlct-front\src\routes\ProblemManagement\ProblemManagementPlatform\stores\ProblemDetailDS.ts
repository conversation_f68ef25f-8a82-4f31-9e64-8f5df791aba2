/**
 * @Description: 问题管理平台-详情页DS
 * @Author: <EMAIL>
 * @Date: 2023/7/4 12:42
 */
import intl from 'utils/intl';
import {
  DataSetSelection,
  FieldIgnore,
  FieldType,
  RecordStatus,
} from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import notification from 'utils/notification';
import moment from 'moment';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

// 市场问题 problemGroup = 'MARKET';
// 审核问题 problemGroup = 'PREVIEW';
// 质量问题 problemGroup = 'QUALITY';
// 制造问题 problemGroup = 'MANUFACTURE';

const registerProblemDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    // 质量问题 problemGroup = 'QUALITY';
    // 制造问题 problemGroup = 'MANUFACTURE';
    {
      name: 'ncReportNum',
      type: FieldType.object,
      lovCode: '	QIS.REVIEWED.NC.REPORT',
      label: intl.get(`${modelPrompt}.ncReportNum`).d('不良评审单号'),
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record, dataSet }) => {
          if (
            ['QUALITY', 'PROCESS', 'R&D'].includes(dataSet.getState('problemGroup')) &&
            dataSet.getState('canEdit')
          ) {
            return false;
          } else {
            return true;
          }
        },
      },
    },
    {
      name: 'ncReportId',
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('ncReportNum') && record.get('ncReportNum').id) {
            return 'ncReportNum.id';
          }
        },
      },
    },
    {
      name: 'inspectDocNum',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单号'),
      lovCode: 'QIS.COMPLETED.INSPECT.DOC',
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record, dataSet }) => {
          if (
            ['QUALITY', 'PROCESS', 'R&D'].includes(dataSet.getState('problemGroup')) &&
            dataSet.getState('canEdit')
          ) {
            return false;
          } else {
            return true;
          }
        },
      },
    },
    {
      name: 'inspectDocId',
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('inspectDocNum') && record.get('inspectDocNum').id) {
            return 'inspectDocNum.id';
          }
        },
      },
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次'),
      lookupCode: 'HME.HANDOVER_TIME',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ dataSet }) =>
          ['QUALITY', 'MANUFACTURE'].includes(dataSet.getState('problemGroup')) &&
          dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'frequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.frequency`).d('新发/再发'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_FREQUENCY',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'findingMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.findingMethod`).d('问题发现方式'),
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        lookupCode: ({ dataSet }) => {
          if (['QUALITY', 'MANUFACTURE'].includes(dataSet.getState('problemGroup'))) {
            return 'YP.QIS.QUALITY_PROBLEM_FINDING_METHOD';
          }
          return 'YP.QIS.PRIVIEW_PROBLEM_FINDING_METHOD';
        },
        required: ({ dataSet }) =>
          ['QUALITY', 'PREVIEW'].includes(dataSet.getState('problemGroup')) &&
          dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'problemCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
      disabled: true,
    },
    {
      name: 'productMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productMode`).d('试制/量产'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_PRODUCT_MODE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) =>
          ['QUALITY', 'MANUFACTURE'].includes(dataSet.getState('problemGroup')) &&
          dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'problemStatus',
      type: FieldType.string,
    },
    {
      name: 'trialProductionLine',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trialProductionLine`).d('试制线'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_FINDING_METHOD',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        // required: ({ record, dataSet }) => record?.get('productMode') === 'TRIAL' && dataSet.getState('researchDevelop') !== 'R&D',
        // 修改 新建&&研发&&试制/量产===试制时，试制线为必输
        required: ({ record, dataSet }) =>
          dataSet.getState('problemStatus') === 'NEW' &&
          record?.get('problemCategory') === 'R&D' &&
          record?.get('productMode') === 'TRIAL',
        disabled: ({ record, dataSet }) =>
          record?.get('productMode') !== 'TRIAL' || !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'siteName',
      required: true,
      dynamicProps: {
        label: ({ dataSet }) => {
          if (dataSet.getState('problemGroup') === 'MARKET') {
            return intl.get(`${modelPrompt}.siteName`).d('站点');
          }
          return intl.get(`${modelPrompt}.factory`).d('发现站点');
        },
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      bind: 'siteLov.siteName',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineName`).d('发现产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      ignore: FieldIgnore.always,
      textField: 'prodLineName',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        // 去掉了试制/量产=OUTPUT_JC原来为必输的限制
        required: ({ record, dataSet }) => {
          if (
            dataSet.getState('problemGroup') === 'PREVIEW' &&
            record.get('problemType') === 'OTHER'
          ) {
            return false;
          } else if (dataSet.getState('researchDevelop') !== 'R&D') {
            return (
              (['QUALITY', 'MANUFACTURE'].includes(dataSet.getState('problemGroup')) &&
                ['OUTPUT_CELL', 'OUTPUT_MP'].includes(record?.get('productMode'))) ||
              dataSet.getState('problemGroup') === 'PREVIEW'
            );
          } else {
            return (
              ['QUALITY', 'MANUFACTURE'].includes(dataSet.getState('problemGroup')) &&
              ['OUTPUT_CELL', 'OUTPUT_MP'].includes(record?.get('productMode'))
            );
          }
        },
        disabled: ({ record, dataSet }) => !record?.get('siteId') || !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'productionLineId',
      type: FieldType.number,
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processWorkcellCode`).d('发现工作单元'),
      lovCode: 'MT.MODEL.WORKCELL_SITE',
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteIds: [record?.get('siteId')],
        }),
        required: ({ record, dataSet }) => {
          if (
            dataSet.getState('problemGroup') === 'PREVIEW' &&
            record.get('problemType') === 'OTHER'
          ) {
            return false;
          } else if (dataSet.getState('researchDevelop') !== 'R&D') {
            return (
              (['QUALITY', 'MANUFACTURE'].includes(dataSet.getState('problemGroup')) &&
                ['OUTPUT_CELL', 'OUTPUT_MP'].includes(record?.get('productMode'))) ||
              dataSet.getState('problemGroup') === 'PREVIEW'
            );
          } else {
            return (
              ['QUALITY', 'MANUFACTURE'].includes(dataSet.getState('problemGroup')) &&
              ['OUTPUT_CELL', 'OUTPUT_MP'].includes(record?.get('productMode'))
            );
          }
        },
        disabled: ({ record, dataSet }) => !record?.get('siteId') || !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'processId',
      type: FieldType.number,
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'processWorkcellName',
      type: FieldType.string,
      bind: 'processWorkcellLov.workcellName',
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('发现设备编号'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      lovPara: {
        tenantId,
      },
      textField: 'equipmentName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        // required: ({ record, dataSet }) =>
        //   (dataSet.getState('problemGroup') === 'QUALITY' &&
        //     record?.get('productMode') === 'OUTPUT') ||
        //   dataSet.getState('problemGroup') === 'PREVIEW',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'locatorCode',
      type: FieldType.object,
      lovCode: 'MT.MODEL.LOCATOR',
      label: intl.get(`${modelPrompt}.locatorCode`).d('发现库位'),
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ dataSet }) => {
          if (
            ['QUALITY', 'PROCESS', 'R&D'].includes(dataSet.getState('problemGroup')) &&
            dataSet.getState('canEdit')
          ) {
            return false;
          } else {
            return true;
          }
        },
      },
    },
    {
      name: 'locatorId',
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('locatorCode') && record.get('locatorCode').locatorId) {
            return 'locatorCode.locatorId';
          }
        },
      },
    },
    {
      name: 'equipmentId',
      type: FieldType.number,
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      bind: 'equipmentLov.equipmentName',
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROJECT_NAME',
      dynamicProps: {
        required: ({ dataSet }) =>
          ['QUALITY', 'PREVIEW', 'MANUFACTURE'].includes(dataSet.getState('problemGroup')) &&
          dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'projectPhase',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectPhase`).d('项目阶段'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_PROJECT_PHASE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) =>
          ['QUALITY', 'PREVIEW'].includes(dataSet.getState('problemGroup')) &&
          dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialNumber`).d('物料号'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        label: ({ dataSet }) => {
          if (dataSet.getState('problemGroup') === 'QUALITY') {
            return intl.get(`${modelPrompt}.materialNumber`).d('物料号');
          }
          return intl.get(`${modelPrompt}.materialCode`).d('物料编码');
        },
        required: ({ record, dataSet }) =>
          dataSet.getState('problemGroup') === 'QUALITY' &&
          record?.get('problemCategory') === 'INCOMING' &&
          dataSet.getState('researchDevelop') !== 'R&D',
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record, dataSet }) => !record?.get('siteId') || !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'itemGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCategory`).d('物料组'),
      bind: 'materialLov.itemGroupDesc',
      disabled: true,
    },
    {
      name: 'traceBarcodeList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.traceBarcode`).d('追溯条码号'),
      multiple: true,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'ncType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncType`).d('不良分类'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_NC_TYPE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) =>
          dataSet.getState('problemGroup') === 'QUALITY' &&
          dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      multiple: true,
      lovCode: 'MT.METHOD.NC_CODE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ dataSet }) =>
          dataSet.getState('problemGroup') === 'QUALITY' &&
          dataSet.getState('researchDevelop') !== 'R&D',
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'ncCodeIds',
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCodes',
      bind: 'ncCodeLov.ncCode',
    },
    {
      name: 'ncCodeDescriptions',
      bind: 'ncCodeLov.description',
      multiple: true,
      label: intl.get(`${modelPrompt}.ncCodeDescription`).d('不良代码描述'),
      disabled: true,
    },
    {
      name: 'ncQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.ncQuantity`).d('不良数量'),
      dynamicProps: {
        required: ({ dataSet }) =>
          dataSet.getState('problemGroup') === 'QUALITY' &&
          dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'qualityProblemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityProblemType`).d('问题类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_TYPE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) =>
          dataSet.getState('problemGroup') === 'QUALITY' &&
          dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        label: ({ dataSet }) => {
          if (['QUALITY', 'MANUFACTURE'].includes(dataSet.getState('problemGroup'))) {
            return intl.get(`${modelPrompt}.responsibleSupplier`).d('责任供应商');
          }
          if (dataSet.getState('problemGroup') === 'MARKET') {
            return intl.get(`${modelPrompt}.supplierName`).d('供应商名称');
          }
          return '';
        },
        required: ({ record, dataSet }) =>
          dataSet.getState('problemGroup') === 'QUALITY' &&
          record?.get('problemCategory') === 'INCOMING' &&
          dataSet.getState('researchDevelop') !== 'R&D',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'dutySupplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'qisProblemRequestCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qisProblemRequestCode`).d('举手单号'),
      disabled: true,
    },

    // 市场问题新建界面
    {
      name: 'maintainReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainReason`).d('维修排查原因'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'maintainMeasure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainMeasure`).d('维修措施'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'batteryMatLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.batteryPackNum`).d('电池包编码'),
      lovCode: 'YP_MES.MES.MATERIAL_LOT',
      lovPara: { tenantId },
      textField: 'materialLotCode',
      valueField: 'materialLotCode',
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'batteryPackId',
      bind: 'batteryMatLotLov.materialLotId',
    },
    {
      name: 'batteryPackNum',
      bind: 'batteryMatLotLov.materialLotCode',
    },
    {
      name: 'batteryPackModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryPackModel`).d('电池包型号'),
      bind: 'batteryMatLotLov.batteryMatLotModel',
      disabled: true,
    },
    {
      name: 'hostPlantLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.hostPlant`).d('主机厂'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'hostPlant',
      bind: 'hostPlantLov.customerId',
    },
    {
      name: 'hostPlantName',
      bind: 'hostPlantLov.customerName',
    },
    {
      name: 'vehicleModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('车型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'projectPhase',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectPhase`).d('项目阶段'),
      lookupCode: 'YP.QIS.PROBLEM_PROJECT_PHASE',
    },
    {
      name: 'vinNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vinNum`).d('VIN号'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'vehicleProductionTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.vehicleProductionTime`).d('车辆生产时间'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'salesTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.salesTime`).d('销售时间'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'faultTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultTime`).d('故障时间'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'maintainTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainTime`).d('维修时间'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'faultMileage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultMileage`).d('故障里程'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'maintainShopCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainShopCode`).d('维修网点编码'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'maintainShopName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainShopName`).d('维修网点名称'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'faultLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultLevel`).d('故障等级'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QA_FEEDBACK_FAULT_LEVEL',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'majorFaultMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorFaultMode`).d('主故障模式'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_FAULT_MODE',
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'faultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultDesc`).d('故障描述'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'ware',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ware`).d('软件/硬件'),
      lookupCode: 'YP.QIS.WARE',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'softwareVersion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.softwareVersion`).d('软件版本'),
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'qualityFeedbackDocLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.feedbackDocNum`).d('质量反馈单'),
      lovCode: 'YP.QIS.QA_FEEDBACK_LIST',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'feedbackId',
      bind: 'qualityFeedbackDocLov.feedbackId',
    },
    {
      name: 'feedbackNum',
      bind: 'qualityFeedbackDocLov.feedbackNum',
    },
    {
      name: 'itemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemCode`).d('客户零件号'),
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'itemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemName`).d('客户零件名称'),
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'majorDivision1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision1`).d('主要区分1'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION1',
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'majorDivision2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision2`).d('主要区分2'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION2',
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'majorDivision3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision3`).d('主要区分3'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MAJOR_DIVISION3',
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'attribute2',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attribute2`).d('不良数量'),
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'marketProblemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketProblemType`).d('问题类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MARKET_PROBLEM_TYPE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'MARKET',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'alarmDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.alarmDocNum`).d('告警单编号'),
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'reworkWoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkWoNum`).d('维修工单编号'),
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'warrantyClaimDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warrantyClaimDocNum`).d('质保索赔单编号'),
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'dataSource',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataSource`).d('数据来源'),
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },

    // 审核问题详细信息
    {
      name: 'problemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemType`).d('问题类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_TYPE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'PREVIEW',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'sysReviewStandardLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sysReviewStandard`).d('不符规章标准'),
      lovCode: 'YP.QIS.SYS_REVIEW_STANDARD',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        // required: ({ dataSet }) => dataSet.getState('problemGroup') === 'PREVIEW',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'unqualifiedFileIds',
      bind: 'sysReviewStandardLov.uniqueCode',
    },
    {
      name: 'unqualifiedFileNames',
      bind: 'sysReviewStandardLov.fileName',
    },
    {
      name: 'conformityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.conformityLevel`).d('符合度等级'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CONFORITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'PREVIEW',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'problemProperty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemProperty`).d('问题属性'),
      lookupCode: 'YP.QIS.PREVIEW_PROBLEM_PROPERTY',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('problemGroup') === 'PREVIEW',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'externalReportFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.externalReportFlag`).d('外审报告体现'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_EXTERIAL_REPORT_FLAG',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'sysReviewPlanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewPlanCode`).d('审核计划编号'),
      disabled: true,
    },
    {
      name: 'influenceRange',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceRange`).d('问题影响范围'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PRIVIEW_PROBLEM_INFLUENCE_RANGE',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
  ],
  transport: {
    read: ({ dataSet }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/category-detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          let detailType;
          if (dataSet?.getState('problemGroup') === 'MARKET') {
            detailType = 'marketInfo';
          } else if (['QUALITY', 'MANUFACTURE'].includes(dataSet?.getState('problemGroup'))) {
            detailType = 'qualityInfo';
          } else {
            detailType = 'previewInfo';
          }
          return { rows: rows[detailType] };
        },
      };
    },
  },
  events: {
    update: ({ name, record }) => {
      if (name === 'productMode') {
        record.init('trialProductionLine', undefined);
      }
      if (name === 'siteLov') {
        record.init('prodLineLov', undefined);
        record.init('processWorkcellLov', undefined);
        record.init('materialLov', undefined);
      }
    },
  },
});

const leadInfoDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'influenceLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.influenceLevel`).d('影响程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_INFLUENCE_LEVEL',
      textField: 'meaning',
      valueField: 'value',
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'responsiblePersonUnitCompanyName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsiblePersonUnitCompanyName`).d('整改责任科室'),
      disabled: true,
    },
    {
      name: 'solutionTool',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.solutionTool`).d('问题解决工具'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SOLUTION_TOOL',
      textField: 'meaning',
      valueField: 'value',
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'marketProblemType',
      type: FieldType.string,
    },
    {
      name: 'repeatFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.repeatFlag`).d('是否重复问题'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_REPEAT_FLAG',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet.getState('problemGroup') === 'MARKET' &&
          record?.get('problemStatus') === 'PUBLISH',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'mergeProblemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.mergeProblemCode`).d('并入问题编号'),
      lovCode: 'YP.QMS.PROBLEM',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record, dataSet }) =>
          dataSet.getState('problemGroup') === 'MARKET' &&
          record.get('repeatFlag') === 'Y' &&
          record?.get('problemStatus') === 'PUBLISH',
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'mergeProblemId',
      bind: 'mergeProblemLov.problemId',
    },
    {
      name: 'mergeProblemCode',
      bind: 'mergeProblemLov.problemCode',
    },
    {
      name: 'nextReportDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.nextReportDate`).d('下次报告日期'),
      dynamicProps: {
        disabled: ({ dataSet }) =>
          !dataSet.getState('canEdit') && !dataSet.getState('nextReportDateEdit'),
      },
    },
  ],
});

const distributeDS: () => DataSetProps = () => ({
  selection: DataSetSelection.multiple,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      textField: 'realName',
      lovPara: { tenantId },
      required: true,
      dynamicProps: {
        disabled: ({ dataSet, record }) => {
          if (dataSet.getState('principalFlag') && record.get('principalPersonFlag') === 'Y') {
            return true;
          }
        },
      },
    },
    {
      name: 'responsiblePerson',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsiblePersonRealName',
      bind: 'responsiblePersonLov.realName',
    },
    {
      name: 'unitCompanyName',
      bind: 'responsiblePersonLov.unitName',
      label: intl.get(`${modelPrompt}.apartment`).d('所属部门'),
    },
    {
      name: 'positionName',
      bind: 'responsiblePersonLov.positionName',
      label: intl.get(`${modelPrompt}.position`).d('岗位'),
    },
    {
      name: 'phone',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.phone`).d('联系方式'),
      bind: 'responsiblePersonLov.mobile',
    },
    {
      name: 'principalPersonFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.principalPersonFlag`).d('主责任人标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: ({ dataSet, record }) => {
          if (dataSet.getState('principalFlag') && record.get('principalPersonFlag') === 'Y') {
            return true;
          }
        },
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/responsible-person/ui`,
        method: 'GET',
      };
    },
  },
  record: {
    dynamicProps: {
      selectable: record =>
        record.dataSet.getState('canEdit') &&
        (record.dataSet.getState('userRoleList').includes('LEAD_PERSON') ||
          record.dataSet.getState('userRoleList').includes('MAJOR_RESPONSIBLE_PERSON')),
    },
  },
});

const enclosure8dDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'enclosure8d',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.enclosure8d`).d('8D报告上传'),
      min: 1,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
        readOnly: ({ dataSet }) => !dataSet.getState('canEdit'),
        required: ({ dataSet }) => dataSet.getState('solutionTool') === 'DEPTH',
      },
    },
  ],
});

const supplierInfoDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'siteId',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleSupplier`).d('责任供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: { tenantId, userId: userInfo.id },
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.causeCode`).d('原因件编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.getState('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.causeDescription`).d('原因件描述'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'reasonNcQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.reasonNcQty`).d('不良数量'),
      min: 1,
      precision: 0,
      step: 1,
      dynamicProps: {
        disabled: ({ dataSet, record }) =>
          !dataSet.getState('canEdit') || record?.get('supplierLotList')?.length,
        required: ({ record }) => !record?.get('supplierLotList')?.length,
      },
    },
    {
      name: 'supplierLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
      lovCode: 'YP.QIS.PROBLEM_SUPPLIER_LOT',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        disabled: ({ dataSet, record }) =>
          !dataSet.getState('canEdit') ||
          record?.get('reasonNcQty') ||
          !record?.get('supplierId') ||
          !record?.get('materialId'),
        required: ({ record }) => !record?.get('reasonNcQty'),
        lovPara: ({ record, dataSet }) => ({
          siteId: dataSet.getState('siteId'),
          supplierId: record?.get('supplierId'),
          materialId: record?.get('materialId'),
        }),
      },
    },
    {
      name: 'supplierLotList',
      bind: 'supplierLotLov.supplierLot',
    },
    {
      name: 'ppmFlag',
      type: FieldType.string,
      lookupCode: 'MT.APS.YES_NO',
      label: intl.get(`${modelPrompt}.ppmFlag`).d('是否计入绩效'),
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.getState('canEdit'),
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason-supplier/get/ui`,
        method: 'GET',
      };
    },
  },
});

// operationType： edit-措施编辑； review-评价有效性编辑； look-查看；
const measureDS: () => DataSetProps = () => ({
  selection: DataSetSelection.multiple,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'measureDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureDescription`).d('措施描述'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet?.getState('operationType') === 'edit',
      },
    },
    {
      name: 'measureVerification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureVerification`).d('措施验证'),
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet?.getState('operationType') === 'look' && record?.get('measureStatus') === 'NEW',
      },
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      textField: 'realName',
      disabled: true,
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'responsiblePerson',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsiblePersonRealName',
      bind: 'responsiblePersonLov.realName',
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成时间'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet?.getState('operationType') === 'edit',
      },
    },
    {
      name: 'actualEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际完成时间'),
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet?.getState('operationType') === 'look' && record?.get('measureStatus') === 'NEW',
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_ENABLE_FLAG',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'feedbackRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.feedbackRemark`).d('反馈备注'),
    },
    {
      name: 'measureStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureStatus`).d('措施状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MEASURE_STATUS',
      textField: 'meaning',
      valueField: 'value',
      defaultValue: 'NEW',
    },
    {
      name: 'evidence',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet.getState('measureType') === 'PERP' &&
          dataSet.getState('solutionTool') === 'DEPTH' &&
          record?.get('measureStatus') === 'NEW' &&
          dataSet?.getState('operationType') === 'look',
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/measure-change-object/ui`,
        method: 'GET',
      };
    },
  },
  record: {
    dynamicProps: {
      selectable: record =>
        (record.status === RecordStatus.add || record.get('responsiblePerson') === userInfo.id) &&
        record.get('measureStatus') === 'NEW' &&
        ['edit', 'review'].includes(record.dataSet.getState('operationType')),
    },
  },
});

const changeObjectDS: () => DataSetProps = () => ({
  selection: DataSetSelection.multiple,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'objectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.changeObjectType`).d('变更对象类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.CHANGE_OBJECT_TYPE',
      textField: 'meaning',
      valueField: 'value',
      required: true,
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.changeObjectCode`).d('变更对象编码'),
      required: true,
    },
    {
      name: 'objectDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectDescription`).d('变更对象说明'),
      required: true,
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'responsiblePerson',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsiblePersonRealName',
      bind: 'responsiblePersonLov.realName',
    },
    {
      name: 'recordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.recordTime`).d('记录时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
  ],
  record: {
    dynamicProps: {
      selectable: record =>
        (record.status === RecordStatus.add || record.get('responsiblePerson') === userInfo.id) &&
        record.dataSet.getState('canEdit'),
    },
  },
});

// operationType： edit-措施编辑； review-评价有效性编辑； look-查看；
const reasonAnalysisDS: () => DataSetProps = () => ({
  selection: DataSetSelection.multiple,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'reasonStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonStatus`).d('原因状态'),
      lookupCode: 'YP.QIS.REASON_STATUS',
      textField: 'meaning',
      valueField: 'value',
      defaultValue: 'NEW',
    },
    {
      name: 'reasonType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonType`).d('原因类型'),
      lookupCode: 'YP.QIS.PROBLEM_REASON_TYPE',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ dataSet }) => dataSet?.getState('operationType') === 'edit',
      },
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
      dynamicProps: {
        required: ({ dataSet }) => dataSet?.getState('operationType') === 'edit',
      },
    },
    // {
    //   name: 'outflowReason',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.outflowReason`).d('流出原因'),
    //   dynamicProps: {
    //     required: ({ dataSet }) => dataSet?.getState('operationType') === 'edit',
    //   },
    // },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'responsiblePerson',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsiblePersonRealName',
      bind: 'responsiblePersonLov.realName',
    },
    {
      name: 'recordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.recordTime`).d('记录时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'rationalityFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rationalityFlag`).d('合理性'),
      lookupCode: 'YP.QIS_REASONABLE',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      dynamicProps: {
        required: ({ dataSet, record }) =>
          dataSet.getState('solutionTool') === 'DEPTH' &&
          record?.get('reasonStatus') === 'NEW' &&
          dataSet?.getState('operationType') === 'look',
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/reason-influence-range/ui`,
        method: 'GET',
      };
    },
  },
  record: {
    dynamicProps: {
      selectable: record =>
        (record.status === RecordStatus.add || record.get('responsiblePerson') === userInfo.id) &&
        record.get('reasonStatus') === 'NEW' &&
        ['edit', 'review'].includes(record.dataSet.getState('operationType')),
    },
  },
});

const influenceRangeDS: () => DataSetProps = () => ({
  selection: DataSetSelection.multiple,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record }) => !record.get('manufacturingProcess') && !record.get('marketplace'),
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'manufacturingProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manufacturingProcess`).d('制程'),
      dynamicProps: {
        required: ({ record }) => !record.get('supplierId') && !record.get('marketplace'),
      },
    },
    {
      name: 'marketplace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketplace`).d('市场'),
      dynamicProps: {
        required: ({ record }) => !record.get('manufacturingProcess') && !record.get('supplierId'),
      },
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'responsiblePerson',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsiblePersonRealName',
      bind: 'responsiblePersonLov.realName',
    },
    {
      name: 'recordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.recordTime`).d('记录时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
  ],
  record: {
    dynamicProps: {
      selectable: record =>
        (record.status === RecordStatus.add || record.get('responsiblePerson') === userInfo.id) &&
        record.dataSet.getState('canEdit'),
    },
  },
});

const marketActDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'marketActEvaluationFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActEvaluationFlag`).d('是否活动评估'),
      lookupCode: 'YP.QIS.Y_N',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'marketActEvaluationNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActEvaluationCode`).d('活动评估编码'),
      disabled: true,
    },
  ],
});

const freezeApplyDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'freezeReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeReason`).d('冻结原因'),
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet?.getState('canEdit'),
      },
    },
    {
      name: 'observePeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.observePeriod`).d('观察时段'),
      required: true,
      lookupCode: 'YP.QMS.OBSERVE_PERIOD',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet?.getState('canEdit'),
      },
    },
    {
      name: 'freezeTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.freezeTime`).d('冻结时间'),
      disabled: true,
    },
    {
      name: 'freezeEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.freezeEnclosure`).d('附件'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet?.getState('canEdit'),
      },
    },
    {
      name: 'freezePreviewStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezePreviewStatus`).d('审批状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_FREEZE_PRIVIEW_STATUS',
      textField: 'meaning',
      valueField: 'value',
      disabled: true,
    },
    {
      name: 'freezePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezePersonRealName`).d('冻结人'),
      disabled: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/freeze-apply/ui`,
        method: 'GET',
      };
    },
  },
});

const problemCloseDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'verifyClosedInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verifyClosedInfo`).d('验证关闭内容'),
      disabled: true,
    },
    {
      name: 'verifyClosedTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.verifyClosedTime`).d('验证关闭时间'),
      disabled: true,
    },
    {
      name: 'verifyPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.verifyPerson`).d('验证关闭人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'verifyClosedPerson',
      bind: 'verifyPersonLov.id',
    },
    {
      name: 'verifyClosedPersonRealName',
      bind: 'verifyPersonLov.realName',
    },
    {
      name: 'tempMeasureExeEnableFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.tempMeasureExeEnableFlag`).d('临时措施实施且有效'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'perpMeasureExeEnableFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.perpMeasureExeEnableFlag`).d('长期措施实施且有效'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'rootReasonConfirmFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.rootReasonConfirmFlag`).d('根本原因确认'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'sumOvertimeDay',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumOvertimeDay`).d('累计延期天数'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/verification-close/ui`,
        method: 'GET',
      };
    },
  },
});

const preCloseDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'overtimeDay',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.overtimeDay`).d('预关闭期限（天）'),
      min: 1,
      precision: 0,
      step: 1,
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet?.getState('canEdit'),
      },
    },
    {
      name: 'requestReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestReason`).d('申请理由'),
      required: true,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet?.getState('canEdit'),
      },
    },
    {
      name: 'reviewStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStatus`).d('审批状态'),
      lookupCode: 'YP.QIS.PROBLEM_CLOSE_REVIEW_STATUS',
      lovPara: { tenantId },
      defaultValue: 'NEW',
      disabled: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('申请时间'),
      disabled: true,
    },
    {
      name: 'createdPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.preClose.applyPerson`).d('申请人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      textField: 'realName',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'createdBy',
      bind: 'createdPersonLov.id',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      bind: 'createdPersonLov.realName',
    },
  ],
});

const overtimeCloseTableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'overtimeDay',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.overtimeDay`).d('预关闭期限（天）'),
      min: 1,
      precision: 0,
      step: 1,
      required: true,
    },
    {
      name: 'requestReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestReason`).d('申请理由'),
      required: true,
    },
    {
      name: 'reviewStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStatus`).d('审批状态'),
      lookupCode: 'YP.QIS.PROBLEM_CLOSE_REVIEW_STATUS',
      lovPara: { tenantId },
      defaultValue: 'NEW',
      disabled: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.overtimeClose.creationDate`).d('提交时间'),
      disabled: true,
    },
    {
      name: 'createdPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.overtimeClose.applyPerson`).d('提交人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      textField: 'realName',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'createdBy',
      bind: 'createdPersonLov.id',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      bind: 'createdPersonLov.realName',
    },
  ],
});

const problemCloseTableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'objectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectType`).d('对象类型'),
      lookupCode: 'YP.QIS.PROBLEM_OBJECT_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'objectId',
      type: FieldType.number,
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectCode`).d('对象编码/名称'),
    },
    {
      name: 'objectStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectStatus`).d('对象状态'),
      lovPara: { tenantId },
      dynamicProps: {
        lookupCode: ({ record }) => {
          const objectType = record?.get('objectType');
          switch (objectType) {
            case 'VERIFICATION':
              return 'YP.QIS.VERIFICATION_STATUS';
            case 'REPLAY':
              return 'YP.QIS.PROBLEM_REPLAY_STATUS';
            case 'SPREAD':
              return 'YP.QIS.PROBLEM_SPREAD_LIST_STATUS';
            case 'WARN':
              return 'YP.QIS.PROPLEM_WARN_TASK_STATUS';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'createdByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByDesc`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
});

const finallyScoreFormDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'scoringCoefficient',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.scoringCoefficient`).d('评分系数'),
      defaultValue: 1,
      min: 0,
      dynamicProps: {
        disabled: ({ dataSet }) =>
          !dataSet?.getState('canEdit') ||
          !dataSet.getState('userRoleList').includes('SENIOR_TECHNICAL_MANAGER'),
      },
    },
    {
      name: 'coefficientConfirmBasis',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.coefficientConfirmBasis`).d('确定依据'),
      dynamicProps: {
        disabled: ({ dataSet }) =>
          !dataSet?.getState('canEdit') ||
          !dataSet.getState('userRoleList').includes('SENIOR_TECHNICAL_MANAGER'),
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/evaluation/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          const { responPersonInfo, scoringCoefficient } = rows;
          responPersonInfo.forEach(item => {
            item.scoreRatio *= 100;
          });
          return {
            ...rows,
            responPersonInfo,
            scoringCoefficient: scoringCoefficient || 1,
          };
        },
      };
    },
  },
});

const finallyScoreTableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'responsiblePerson',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsiblePersonRealName',
      bind: 'responsiblePersonLov.realName',
    },
    {
      name: 'unitCompanyName',
      bind: 'responsiblePersonLov.unitName',
      label: intl.get(`${modelPrompt}.apartment`).d('所属部门'),
    },
    {
      name: 'positionName',
      bind: 'responsiblePersonLov.positionName',
      label: intl.get(`${modelPrompt}.position`).d('岗位'),
    },
    {
      name: 'phone',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.phone`).d('联系方式'),
      bind: 'responsiblePersonLov.mobile',
    },
    {
      name: 'scoreRatio',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.scoreRatio`).d('分配比例'),
      min: 0,
      dynamicProps: {
        max: ({ dataSet, record }) => {
          let sumQty = 0;
          dataSet.forEach(_record => {
            if (_record.get('scoreRatio') && _record.id !== record.id) {
              sumQty += _record.get('scoreRatio');
            }
          });
          return 100 - sumQty;
        },
      },
    },
    {
      name: 'score',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.score`).d('最终得分'),
    },
  ],
});

export {
  registerProblemDS,
  leadInfoDS,
  distributeDS,
  supplierInfoDS,
  measureDS,
  changeObjectDS,
  reasonAnalysisDS,
  influenceRangeDS,
  marketActDS,
  freezeApplyDS,
  problemCloseDS,
  preCloseDS,
  overtimeCloseTableDS,
  problemCloseTableDS,
  finallyScoreFormDS,
  finallyScoreTableDS,
  enclosure8dDS,
};
