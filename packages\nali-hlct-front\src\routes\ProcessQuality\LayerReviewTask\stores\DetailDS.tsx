/**
 * @Description: 分层审核任务-详情界面DS
 * @Author: <EMAIL>
 * @Date: 2023/8/21 14:47
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.layerReview.layerReviewTask';
const tenantId = getCurrentOrganizationId();
const currentUserId = getCurrentUserId();
const endUrl = '';

const detailDS: () => DataSetProps = () => ({
  autoCreate: true,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'layerReviewTaskId',
  fields: [
    {
      name: 'layerReviewTaskId',
      type: FieldType.number,
    },
    {
      name: 'layerReviewTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewTaskCode`).d('分层审核任务编码'),
    },
    {
      name: 'layerReviewTaskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewTaskStatus`).d('分层审核任务状态'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_TASK_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'layerReviewLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewLevel`).d('层级'),
      lookupCode: 'YP.QIS.LEVEL',
      lovPara: { tenantId },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'productLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'productLineName',
      bind: 'prodLineLov.prodLineName',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      lovCode: 'YP_QIS.OPERATION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    {
      name: 'requestFinishTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.requestFinishTime`).d('要求完成时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const { layerReviewTaskId } = data;
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-review-task/${layerReviewTaskId}/ui`,
        method: 'GET',
      };
    },
  },
});

const lineFormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  forceValidate: true,
  dataKey: 'rows',
  primaryKey: 'layerRevtskDtlId',
  fields: [
    {
      name: 'layerRevtskDtlId',
      type: FieldType.number,
    },
    {
      name: 'layerRevtskDtlCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerRevtskDtlCode`).d('分层审核任务分号'),
    },
    {
      name: 'equimentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equimentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
    {
      name: 'layerRevtskDtlStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerRevtskDtlStatus`).d('状态'),
      lookupCode: 'YP.QIS.LAYER_REVTSK_DTL_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'abnormalFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.abnormalFlag`).d('非正常生产标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'abnormalCloseReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.abnormalCloseReason`).d('任务异常关闭原因'),
      dynamicProps: {
        required: ({ record }) => record.get('abnormalFlag') === 'Y',
        disabled: ({ record }) => record.get('abnormalFlag') !== 'Y',
      },
    },
    {
      name: 'closeTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.closeTime`).d('关闭时间'),
      disabled: true,
    },
  ],
});

const lineTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  forceValidate: true,
  dataKey: 'rows',
  primaryKey: 'layerReviewItemId',
  fields: [
    {
      name: 'layerReviewItemId',
      type: FieldType.number,
    },
    {
      name: 'layerReviewItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewItemCode`).d('分层审核项目编码'),
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('分层审核项目内容'),
    },
    {
      name: 'projectFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectFrom`).d('标准来源'),
    },
    {
      name: 'projectClassify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectClassify`).d('项目分类'),
      lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM.PROJECT_FROM',
      lovPara: { tenantId },
    },
    {
      name: 'reviewPersonLov',
      label: intl.get(`${modelPrompt}.reviewPerson`).d('审核员'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: { tenantId },
    },
    {
      name: 'reviewById',
      bind: 'reviewPersonLov.id',
    },
    {
      name: 'reviewByName',
      bind: 'reviewPersonLov.realName',
    },
    {
      name: 'beReviewedLov',
      label: intl.get(`${modelPrompt}.beReviewedLov`).d('应审人员'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: { tenantId },
    },
    {
      name: 'beReviewedId',
      bind: 'beReviewedLov.id',
    },
    {
      name: 'beReviewedName',
      bind: 'beReviewedLov.realName',
    },
    {
      name: 'reviewMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewMethod`).d('检查方式'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_TASK_REVIEW_METHOD',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ dataSet }) => dataSet.parent?.current?.get('abnormalFlag') !== 'Y',
        disabled: ({ dataSet }) => dataSet.parent?.current?.get('abnormalFlag') === 'Y',
      },
    },
    {
      name: 'reviewResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewResult`).d('审核结果'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_TASK_REVIEW_RESULT',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ dataSet }) => dataSet.parent?.current?.get('abnormalFlag') !== 'Y',
        disabled: ({ dataSet }) => dataSet.parent?.current?.get('abnormalFlag') === 'Y',
      },
    },
    {
      name: 'nextVerify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.nextVerify`).d('下次审核验证'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_TASK_NEXTVERIFY',
      lovPara: { tenantId },
    },
    {
      name: 'problemManFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.problemManFlag`).d('是否接入问题管理'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: ({ dataSet, record }) =>
          dataSet.parent?.current?.get('abnormalFlag') === 'Y' ||
          record?.get('reviewResult') !== 'NG',
      },
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemDec',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDec`).d('问题登记'),
      dynamicProps: {
        required: ({ record }) =>
          record?.get('problemManFlag') !== 'Y' && record?.get('reviewResult') === 'NG',
      },
    },
    {
      name: 'problemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemType`).d('问题类型'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_TASK_PROBLEM_TYPE',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) =>
          record?.get('problemManFlag') !== 'Y' && record?.get('reviewResult') === 'NG',
      },
    },
    {
      name: 'problemResPersonLov',
      label: intl.get(`${modelPrompt}.problemResPersonLov`).d('问题责任人'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) =>
          record?.get('problemManFlag') !== 'Y' && record?.get('reviewResult') === 'NG',
      },
    },
    {
      name: 'problemResPersonId',
      bind: 'problemResPersonLov.id',
    },
    {
      name: 'problemResPersonName',
      bind: 'problemResPersonLov.realName',
    },
    {
      name: 'correctiveFinishTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.correctiveFinishTime`).d('整改完成时间'),
      dynamicProps: {
        required: ({ record, dataSet }) =>
          dataSet.parent?.current?.get('layerRevtskDtlStatus') === 'CORRECTING' && record?.get('problemResPersonId') === currentUserId,
      },
    },
  ],
});

export { detailDS, lineFormDS, lineTableDS };
