/**
 * @Description:ORT测试执行-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-09-20 10:38:58
 * @LastEditTime: 2023-09-20 10:38:58
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
let endUrl;
endUrl = '-37685';
endUrl = '';

// 明细查询
export function fetchInspectTaskActConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-inspect-task-act/act/id/all`,
    method: 'GET',
  };
}

// 行明细查询
export function fetchInspectTaskActLineConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-inspect-task-line/line/list/ui`,
    method: 'GET',
  };
}

// 确认收样
export function confirmReceiptConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-inspect-task/confirm/receipt`,
    method: 'POST',
  };
}

// 取消收样
export function cancelTestConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-inspect-task/cancel/test`,
    method: 'POST',
  };
}

// 扫描条码
export function scanConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-inspect-task-act/scan/cell`,
    method: 'POST',
  };
}

// 保存
export function saveConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-inspect-task-act/update/all`,
    method: 'POST',
  };
}

// 提交
export function submitConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-inspect-task-act/submit/limit`,
    method: 'POST',
  };
}

// 获取用户部门
export function getUserDepartmentConfig() {
  return {
    url: `/hpfm/v1/${tenantId}/employee-assigns`,
    method: 'GET',
  };
}

/**
 * 根据materialId获取物料相关信息
 * @function QueryMaterialInfo
 * @returns {object} fetch Promise
 */
export function QueryMaterialInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-qa-feedbacks/mt-material-ca`,
    method: 'POST',
  };
}


/**
 * 查询用户默认站点
 * @function GetDefaultSite
 * @returns {object} fetch Promise
 */
export function GetDefaultSite() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-user-inspect-permission/default/site/ui`,
    method: 'GET',
  };
}
