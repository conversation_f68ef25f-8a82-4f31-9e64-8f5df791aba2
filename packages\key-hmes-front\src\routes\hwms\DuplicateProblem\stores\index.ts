import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.duplicateProblem';

// 列表页
const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'problemReplayId',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem-replay/ui`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'problemReplayCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemReplayCode`).d('复盘编号'),
    },
    {
      name: 'problemReplayStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemReplayStatus`).d('复盘状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PROBLEM_REPLAY_STATUS',
    },

    {
      name: 'problemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
      lovCode: 'YP.QIS.PROBLEM_INFO',
      ignore: FieldIgnore.always,
      textField: 'problemCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'problemId',
      bind: 'problemLov.problemId',
    },

    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },

    {
      name: 'createdLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.created`).d('发起人'),
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      bind: 'createdLov.id',
    },

    {
      name: 'unitLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.unit`).d('主责部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'principalUserDeptId',
      bind: 'unitLov.unitId',
    },

    {
      name: 'principalUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.principalUser`).d('主责人'),
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'principalUserId',
      bind: 'principalUserLov.id',
    },

    {
      name: 'createTimeBegin',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.createTimeBegin`).d('创建时间从'),
      max: 'createTimeEnd',
    },
    {
      name: 'createTimeEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.createTimeEnd`).d('创建时间至'),
      min: 'createTimeBegin',
    },
    {
      name: 'planEndTimeBegin',
      label: intl.get(`${modelPrompt}.planEndTimeBegin`).d('计划完成时间从'),
      type: FieldType.dateTime,
      max: 'planEndTimeEnd',
    },
    {
      name: 'planEndTimeEnd',
      label: intl.get(`${modelPrompt}.planEndTimeEnd`).d('计划完成时间至'),
      type: FieldType.dateTime,
      min: 'planEndTimeBegin',
    },
    {
      name: 'startReason',
      label: intl.get(`${modelPrompt}.startReason`).d('启动原因'),
      type: FieldType.string,
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PROBLEM_REPLAY_START_REASON',
    },
  ],
  fields: [
    {
      name: 'problemReplayCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemReplayCode`).d('复盘编号'),
    },
    {
      name: 'problemReplayStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemReplayStatus`).d('复盘状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PROBLEM_REPLAY_STATUS',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编号'),
    },

    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('发起人'),
    },
    {
      name: 'startReason',
      label: intl.get(`${modelPrompt}.startReason`).d('启动原因'),
      type: FieldType.string,
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PROBLEM_REPLAY_START_REASON',
    },
    {
      name: 'principalUserDept',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.principalUserDept`).d('主责部门'),
    },
    {
      name: 'principalUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.principalUserName`).d('主责人'),
    },
    {
      name: 'background',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.background`).d('背景说明'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成时间'),
    },
  ],
});

// 基础信息
const detailBasicDS: () => DataSetProps = () => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'problemReplayCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemReplayCode`).d('复盘编号'),
      disabled: true,
    },
    {
      name: 'problemReplayStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemReplayStatus`).d('复盘状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PROBLEM_REPLAY_STATUS',
      disabled: true,
    },
    {
      name: 'createdLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.created`).d('发起人'),
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      disabled: true,
    },
    {
      name: 'createdBy',
      bind: 'createdLov.id',
    },
    {
      name: 'createdByName',
      bind: 'createdLov.realName',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'principalUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.principalUser`).d('主责人'),
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'principalUserId',
      bind: 'principalUserLov.id',
    },
    {
      name: 'principalUserName',
      bind: 'principalUserLov.realName',
    },
    {
      name: 'principalUserDept',
      label: intl.get(`${modelPrompt}.unit`).d('主责部门'),
      bind: 'principalUserLov.unitName',
      disabled: true,
    },
    {
      name: 'problemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
      lovCode: 'YP.QIS.PROBLEM_INFO',
      ignore: FieldIgnore.always,
      textField: 'problemCode',
      lovPara: {
        tenantId,
      },
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'problemId',
      bind: 'problemLov.problemId',
    },
    {
      name: 'problemCode',
      bind: 'problemLov.problemCode',
    },
    {
      name: 'problemTitle',
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
      bind: 'problemLov.problemTitle',
      disabled: true,
    },

    {
      name: 'planEndTime',
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成时间'),
      type: FieldType.dateTime,
      required: true,
    },
    {
      name: 'startReason',
      label: intl.get(`${modelPrompt}.startReason`).d('启动原因'),
      type: FieldType.string,
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PROBLEM_REPLAY_START_REASON',
      required: true,
    },
    {
      name: 'enclosure',
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      type: FieldType.attachment,
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    },
    {
      name: 'background',
      label: intl.get(`${modelPrompt}.background`).d('背景说明'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'summarize',
      label: intl.get(`${modelPrompt}.summarize`).d('复盘总结'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'summarizeEnclosure',
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      type: FieldType.attachment,
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
      disabled: true,
    },
  ],
  events: {
    update: ({ record, name }) => {
      if (name === 'siteLov') {
        record.init('problemLov', null);
      }
    },
  },
});

// 事件轴
const eventAxisDS: () => DataSetProps = () => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      disabled: true,
    },
    {
      name: 'occurTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.occurTime`).d('时间'),
      required: true,
    },
    {
      name: 'stepDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stepDesc`).d('本次事件步骤描述'),
      required: true,
    },
    {
      name: 'specification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specification`).d('规范'),
      required: true,
    },
    {
      name: 'usedToDo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usedToDo`).d('以往如何做'),
      required: true,
    },
    {
      name: 'difference',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.difference`).d('不同点'),
      required: true,
    },
    {
      name: 'differenceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.differenceFlag`).d('是否关键不同点'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'responsiblePerson',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsiblePersonName',
      bind: 'responsiblePersonLov.realName',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => record.dataSet.getState('canSelect'),
    },
  },
  events: {
    update: ({ record, name, value }) => {
      if (name === 'responsiblePersonLov') {
        const idList: any = [];
        const valueList: any = [];
        (value || []).forEach(item => {
          if (!idList.includes(item.id)) {
            idList.push(item.id);
            valueList.push({
              ...item,
            });
          }
        });
        record.set('responsiblePersonLov', valueList);
      }
    },
  },
});

// 原因分析及改进
const improvementDS: () => DataSetProps = () => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'sequence',
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      disabled: true,
    },
    {
      name: 'problemReplayEventId',
    },
    {
      name: 'problemReplayEventSequence',
      label: intl.get(`${modelPrompt}.problemReplayEventCode`).d('关联事件'),
      disabled: true,
    },
    {
      name: 'responsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      ignore: FieldIgnore.always,
      textField: 'realName',
      disabled: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'responsibleUserId',
      bind: 'responsibleUserLov.id',
    },
    {
      name: 'responsibleUserName',
      bind: 'responsibleUserLov.realName',
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因分析'),
      required: true,
    },
    {
      name: 'replayAnalysisType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.replayAnalysisType`).d('分类'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.PROBLEM_REPLAY_ANALYSIS_TYPE',
      required: true,
    },
    {
      name: 'reasonRecordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.reasonRecordTime`).d('原因记录时间'),
      disabled: true,
    },
    {
      name: 'measure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measure`).d('改进措施'),
    },
    {
      name: 'measureRecordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.measureRecordTime`).d('措施记录时间'),
      disabled: true,
    },
    {
      name: 'measureEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => record.dataSet.getState('canSelect'),
    },
  },
  events: {
    update: ({ record, name, value }) => {
      if (name === 'measure') {
        record.set('measureRecordTime', value ? new Date() : null);
      }
    },
  },
});

export { tableDS, detailBasicDS, eventAxisDS, improvementDS };
