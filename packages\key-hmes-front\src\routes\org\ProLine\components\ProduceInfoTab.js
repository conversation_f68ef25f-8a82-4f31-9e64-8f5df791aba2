/**
 * @Description: 站点维护-生产属性Tab
 * @Author: <<EMAIL>>
 * @Date: 2021-02-04 11:54:54
 * @LastEditTime: 2022-06-13 17:39:48
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect } from 'react';
import { withRouter } from 'dva/router';
import { Form, Select, Lov, TextField, Table } from 'choerodon-ui/pro';
import { Popconfirm, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';

const modelPrompt = 'tarzan.model.org.prodLine';
const { Panel } = Collapse;

const ProduceInfoTab = props => {
  const {
    ds,
    tableDs,
    canEdit,
    columns = 1,
    defaultMethodvalue,
    focus = true,
    match: { path },
  } = props;

  const [dispatchMethodvalue, setDispatchMethodvalue] = useState(
    (ds.get('prodLineManufacturing') || {}).dispatchMethod,
  );

  useEffect(() => {
    setDispatchMethodvalue(defaultMethodvalue);
  }, [defaultMethodvalue]);

  const handleAddRow = () => {
    const record = tableDs.create({ enableFlag: 'Y' }, 0);
    record.set('prodLineCode', ((ds.toData()[0] || {}).productionLine || {}).prodLineCode);
  };

  const deleteRecord = record => {
    tableDs.remove(record);
  };

  const column = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          funcType="flat"
          onClick={handleAddRow}
          shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          okText={intl.get('tarzan.common.button.confirm').d('确定')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          onConfirm={() => deleteRecord(record)}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit || record.status === 'delete'}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'prodLineCode',
    },
    {
      name: 'operation',
      width: 200,
      editor: () =>
        canEdit && <Lov noCache dataSet={tableDs} placeholder=" " name="assembleGroupCode" />,
    },
    {
      name: 'description',
    },
    {
      name: 'revision',
    },
  ];

  return (
    <>
      <Form
        disabled={!canEdit || focus}
        dataSet={ds}
        columns={columns}
        labelLayout="horizontal"
        labelWidth={112}
      >
        <Lov name="issuedLocator" />
        <Lov name="completionLocator" />
        <Lov name="inventoryLocator" />
        <TextField name="issuedLocatorName" />
        <TextField name="completionLocatorName" />
        <TextField name="inventoryLocatorName" />
        <Select
          name="dispatchMethod"
          onChange={value => {
            setDispatchMethodvalue(value);
            tableDs.removeAll();
          }}
        />
        {/* SPECIAL_OPERATION */}
      </Form>
      {dispatchMethodvalue === 'SPECIAL_OPERATION' && (
        <Collapse bordered={false} defaultActiveKey={['setDispatchOperation']}>
          <Panel
            header={intl.get(`${modelPrompt}.setDispatchOperation`).d('指定调度工艺')}
            key="setDispatchOperation"
            dataSet={tableDs}
          >
            <Table
              canEdit={!canEdit}
              dataSet={tableDs}
              columns={column}
              filter={record => {
                return record.status !== 'delete';
              }}
            />
          </Panel>
        </Collapse>
      )}
    </>
  );
};

export default withRouter(ProduceInfoTab);
