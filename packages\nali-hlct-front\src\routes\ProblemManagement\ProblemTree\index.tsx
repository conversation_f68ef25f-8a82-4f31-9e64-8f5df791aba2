/**
 * RelationMaintain - 故障树
 * @date: 2023-8-24
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Header, Content } from 'components/Page';
import { drawerPropsC7n } from '@components/tarzan-ui';
import {} from 'choerodon-ui';
import { DataSet, Form, TextField, Tree, Spin, Modal, Button } from 'choerodon-ui/pro';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import { LabelLayout } from 'choerodon-ui/pro/es/form/enum';
import { getCurrentUser } from 'utils/utils';
import styles from './index.module.less';
import DetailDraw from './detailDraw';
import TreeNodeIcon from './TreeNodeIcon';

import {
  fetchAllTreeConfig,
  createSubordinateRelationshipConfig,
  copySubordinateRelationshipConfig,
  deleteSubordinateRelationshipConfig,
  cutSubordinateRelationshipConfig,
} from './services';
import { treeDS, searchDS, addSubRelationDS, addSubRelationTableDS } from './stores/ProblemTreeDS';

let detailmodal;

const modelPrompt = 'tarzan.qms.problemTree';

const typeList = ['FAILURE_REASON', 'MIDDLE_FAILURE', 'FAILURE_MODE'];

// 企业 站点 区域 生产线 工作单元
const failureTreeTypeMap = {
  SITE: [],
  CLASS: ['addSub'],
  FAILURE_MODE: ['add', 'addSub', 'paste'],
  MIDDLE_FAILURE: ['add', 'addSub', 'copy', 'paste', 'delete'],
  FAILURE_REASON: ['copy', 'delete'],
};

function ProblemTree(props) {
  const {
    history: {
      location: { pathname, state },
    },
    match: { path },
  } = props;

  // 审核信息保存
  const fetchAllTree = useRequest(fetchAllTreeConfig(), {
    manual: true,
    needPromise: true,
  });

  // 新增关系
  const createSubordinateRelationship = useRequest(createSubordinateRelationshipConfig(), {
    manual: true,
    needPromise: true,
  });
  // 复制关系
  const copySubordinateRelationship = useRequest(copySubordinateRelationshipConfig(), {
    manual: true,
    needPromise: true,
  });
  // 删除
  const deleteSubordinateRelationship = useRequest(deleteSubordinateRelationshipConfig(), {
    manual: true,
    needPromise: true,
  });
  // 剪切
  const cutSubordinateRelationship = useRequest(cutSubordinateRelationshipConfig(), {
    manual: true,
    needPromise: true,
  });

  const [user] = useState(getCurrentUser()); // 用户详细信息

  useEffect(() => {
    getAllTreeData('init');
    treeDs.setState('user', user);
  }, []);

  useEffect(() => {
    window.addEventListener('resize', onResize);
    sethalfContainerHeight(window.innerHeight - 140);
    return () => {
      window.removeEventListener('resize', onResize);
    };
  }, []);

  useEffect(() => {
    if (Object.keys(props?.location?.state || {}).length === 0) {
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const { searchValue } = state || {};
    searchDs.current?.set('searchValue', searchValue);
    searchTree();
  }, [props?.location?.state]);

  const onResize = () => {
    if (pathname === path) {
      sethalfContainerHeight(window.innerHeight - 140);
    }
  };

  const treeDs = useMemo(() => new DataSet(treeDS()), []);
  const searchDs = useMemo(() => new DataSet(searchDS()), []);
  const addSubRelationTableDs = useMemo(() => new DataSet(addSubRelationTableDS()), []);
  const addSubRelationDs = useMemo(
    () =>
      new DataSet({
        ...addSubRelationDS(),
        children: {
          relatedProblem: addSubRelationTableDs,
        },
      }),
    [],
  );

  const [loading, setloading] = useState(false);
  const [drawRecord, setDrawRecord]: any = useState(null);
  const [cacheExpandKeys, setCacheExpandKeys]: any = useState([]); // 缓存的展开层级key
  const [autoExpandParent, setautoExpandParent] = useState(true); // 是否自动展开父节点
  const [halfContainerHeight, sethalfContainerHeight] = useState(window.innerHeight); // 是否自动展开父节点
  const [searchValue, setsearchValue] = useState(''); // 模糊搜索值

  const [copyRecord, setCopyRecord]: any = useState(null); // 复制的组织节点信息 新关系复用

  const [searchRows, setSearchRows] = useState(0); // 模糊搜索匹配条数
  const [iconType, setIconType] = useState(true); // 全部展开/收起按钮状态

  const getAllTreeData = async type => {
    setloading(true);

    const res = await fetchAllTree.run({});
    initTreeData(res?.rows || [], type);
  };

  useEffect(() => {
    if (cacheExpandKeys.length > 0) {
      getAllTreeData(null);
    }
  }, [cacheExpandKeys]);

  const initTreeData = (dataList, type) => {
    const _cacheExpandKeys = [...cacheExpandKeys];
    const _dataList = dataList.map(item => {
      return {
        ...item,
        failureTreeName: item.siteName,
        failureTreeCode: item.siteName,
        path: `${item.siteId}`,
        parentOrganizationId: null,
        parentPath: null,
        failureTreeId: `${item.siteId}`,
        failureTreeType: 'SITE',
        failureTreeEventDTO2List: (item.failureTreeEventDTO2List || []).map(classItem => {
          return {
            ...classItem,
            path: `${classItem.failureTreeClass}`,
            parentPath: `${item.siteId}`,
            parentOrganizationId: `${item.siteId}`,
            failureTreeType: 'CLASS',
            failureTreeId: `${classItem.failureTreeClass}`,
            failureTreeName: `${classItem.failureTreeClassDesc} - ${classItem.failureTreeClass}`,
            failureTreeEventDTO2List: (classItem.failureTreeEventDTO2List || []).map(topItem => {
              return {
                ...topItem,
                parentOrganizationId: `${classItem.failureTreeClass}`,
                parentPath: `${classItem.failureTreeClass}`,
              };
            }),
          };
        }),
      };
    });

    const formatDataListLoop = (list, siteId) => {
      const newList: any = [];
      let subList: any = [];
      list.forEach((item: any) => {
        const { failureTreeEventDTO2List, ...other } = item;
        if (siteId) {
          other.siteId = siteId;
        }
        if (!['SITE', 'CLASS'].includes(other.failureTreeType)) {
          if (other.failureTreeCode) {
            other.failureTreeName = `${other.failureTreeName} - ${other.failureTreeCode}`;
          }
        }
        newList.push({
          ...other,
          failureTreeId: `${other.failureTreeId}`,
          expand: type === 'init' ? true : _cacheExpandKeys.includes(item.path),
          subSum: (failureTreeEventDTO2List || []).length,
          subCodeList: (failureTreeEventDTO2List || []).map(subItem => {
            return subItem.failureTreeCode;
          }),
        });
        subList = [
          ...subList,
          ...formatDataListLoop(failureTreeEventDTO2List || [], other.siteId || siteId),
        ];
      });
      return [...newList, ...subList];
    };

    const _formatDataListLoop = formatDataListLoop(_dataList, null);

    setautoExpandParent(type === 'init');
    treeDs.loadData(_formatDataListLoop);

    setloading(false);
  };

  // 搜索目标树
  const searchTree = () => {
    if (loading) {
      return;
    }

    const value = searchDs.current?.get('searchValue') || '';
    setsearchValue(value);

    // 开始查询
    setSearchRows(0);
    setloading(true);
    setautoExpandParent(true);

    let sum = 0;

    treeDs.forEach(record => {
      const failureTreeName = record.get('failureTreeName');
      if (failureTreeName.toUpperCase().indexOf(value.toUpperCase()) > -1) {
        sum++;
        record.set('expand', true);
      } else {
        record.set('expand', false);
      }
    });

    setTimeout(() => {
      setloading(false);
      setSearchRows(value === '' ? 0 : sum);
    }, 200);
  };

  // 展开所有节点
  const expandAll = () => {
    treeDs.forEach(record => {
      record.set('expand', !iconType);
    });
    setIconType(!iconType);
  };

  const resetTextField = () => {
    searchDs.current?.set('searchValue', null);
    searchTree();
  };

  // 展开收起节点
  const onExpand = key => {
    setautoExpandParent(false);
    treeDs.forEach(record => {
      record.set('expand', key.includes(record.get('path')));
    });

    setIconType(key.length === treeDs.length);
  };

  // 拖拽事件
  const onDrop = async info => {
    const { dragNode, node } = info;
    // dragNode 拖动的node
    // node 目标node
    const targetRecord = node.record;
    const moveRecord = dragNode.record;

    // 判断是否拖动合法 与treeNode 拖动禁用的样式判断逻辑相同
    let dragDisableStyle;
    if (moveRecord?.get('failureTreeId')) {
      if (moveRecord.get('userId') === user.id) {
        if (!['TOP', 'MIDDLE', 'BOTTOM'].includes(moveRecord?.get('tag'))) {
          dragDisableStyle = true;
        } else if (['TOP'].includes(moveRecord?.get('tag'))) {
          if (['CLASS'].includes(targetRecord?.get('failureTreeType'))) {
            dragDisableStyle = false;
          } else {
            dragDisableStyle = true;
          }
        } else if (['TOP', 'MIDDLE'].includes(targetRecord?.get('tag'))) {
          dragDisableStyle = false;
        } else {
          dragDisableStyle = true;
        }
      } else {
        dragDisableStyle = true;
      }
    } else {
      dragDisableStyle = false;
    }

    if (dragDisableStyle) {
      return;
    }

    setloading(true);
    const params: any = {};

    if (['CLASS'].includes(targetRecord?.get('failureTreeType'))) {
      params.failureClass = targetRecord.get('failureTreeClass');
      params.currentFailureTreeId = moveRecord.get('failureTreeId');
      params.currentPath = moveRecord.get('path');
    } else {
      params.parentFailureTreeId = targetRecord.get('failureTreeId');
      params.parentPath = targetRecord.get('path');
      params.currentFailureTreeId = moveRecord.get('failureTreeId');
      params.currentPath = moveRecord.get('path');
    }
    const res = await cutSubordinateRelationship.run({
      params,
    });

    if (res?.success) {
      notification.success({});
      setCopyRecord(null);
      // 展开项缓存变更后会触发查询树详情
      setCacheExpandKeys([...handleCacheExpandKeys(), targetRecord.get('path')]);
    } else {
      setloading(false);
    }
  };

  const onDragStart = e => {
    setDrawRecord(e?.node?.record?.toData());
  };

  const onDragEnd = () => {
    setDrawRecord(null);
  };

  // 拖拽时展开进入的项
  const onDragEnter = e => {
    const {
      node: { record },
    } = e;
    record.set('expand', true);
  };

  // treeNode 点击事件
  const treeOnclick = (e, node) => {
    if (typeList.includes(node.record.get('failureTreeType'))) {
      handleAddSub(node.record, 'edit');
    }
  };

  const iconClick = (e, record, value) => {
    // 判断事件类型
    // 普通新建 Lov
    if (e === 'addRelation') {
      handleAdd(record, value);
      return;
    }
    // 新增下级关系 Lov
    if (e === 'addSub') {
      handleAddSub(record, 'create');
      return;
    }
    // 删除关系
    if (e === 'delete') {
      handleDelete(record);
      return;
    }
    // 复制关系
    if (e === 'copy') {
      notification.success({ message: intl.get(`${modelPrompt}.copySuccess`).d('复制成功') });
      setCopyRecord(record.toData());
      return;
    }
    // 黏贴关系
    if (e === 'paste') {
      pasteRelation(record);
    }
  };

  // 粘贴树关系
  const pasteRelation = async record => {
    setloading(true);
    const params: any = {};
    params.parentFailureTreeId = record.get('failureTreeId');
    params.parentPath = record.get('path');
    params.currentFailureTreeId = copyRecord?.failureTreeId;
    params.currentPath = copyRecord.path;
    const res = await copySubordinateRelationship.run({
      params,
    });

    if (res?.success) {
      notification.success({});
      setCopyRecord(null);
      // 展开项缓存变更后会触发查询树详情
      setCacheExpandKeys([...handleCacheExpandKeys(), record.get('path')]);
    } else {
      setloading(false);
    }
  };

  const handleAdd = async (record, value) => {
    if (value?.length > 0) {
      setloading(true);
      const params: any = {};
      params.parentFailureTreeId = record.get('failureTreeId');
      params.parentPath = record.get('path');
      params.sequence = record.get('subSum') * 10 + 10;
      params.organizationIds = value.map(item => {
        return item.failureEventId;
      });
      record.init('addRelation', null);
      const res = await createSubordinateRelationship.run({
        params,
      });

      if (res?.success) {
        notification.success({});
        // 展开项缓存变更后会触发查询树详情
        setCacheExpandKeys([...handleCacheExpandKeys(), record.get('path')]);
      } else {
        setloading(false);
      }
    }
  };

  const handleDelete = async record => {
    setloading(true);
    const params: any = {};
    params.parentFailureTreeId = record.get('parentFailureTreeId');
    params.currentFailureTreeId = record.get('failureTreeId');
    params.currentPath = record.get('path');
    const res = await deleteSubordinateRelationship.run({
      params,
    });

    if (res?.success) {
      notification.success({});
      // 展开项缓存变更后会触发查询树详情
      setCacheExpandKeys([...handleCacheExpandKeys()]);
    } else {
      setloading(false);
    }
  };

  const openProblemDetail = problemId => {
    props.history.push(`/hwms/problem-management/problem-management-platform/dist/${problemId}`);
    detailmodal.close();
  };

  const handleAddSub = (record, type) => {
    const data = record.toData();
    addSubRelationDs.setState('siteId', record.get('siteId'));

    detailmodal = Modal.open({
      ...drawerPropsC7n({}),
      destroyOnClose: true,
      key: Modal.key(),
      title:
        type === 'create'
          ? intl.get(`${modelPrompt}.addSubRelation`).d('新建下级及关系')
          : intl.get(`${modelPrompt}.editEvent`).d('编辑事件'),
      style: {
        width: '720px',
      },
      footer: null,
      maskClosable: true,
      children: (
        <DetailDraw
          type={type}
          addSubRelationDs={addSubRelationDs}
          addSubRelationTableDs={addSubRelationTableDs}
          data={data}
          callBack={handleAddSubSaveCallBack}
          closeModal={closeModal}
          setloading={setloading}
          loading={loading}
          user={user}
          openProblemDetail={openProblemDetail}
        />
      ),
    });
  };

  const closeModal = () => {
    detailmodal.close();
  };

  const handleAddSubSaveCallBack = async key => {
    // 展开项缓存变更后会触发查询树详情
    setCacheExpandKeys([...handleCacheExpandKeys(), key]);
  };

  const handleCacheExpandKeys = () => {
    const _cacheExpandKeys: any = [];
    treeDs.forEach(record => {
      if (record.get('expand')) {
        _cacheExpandKeys.push(record.get('path'));
      }
    });
    return _cacheExpandKeys;
  };

  const iconProps = {
    callback: iconClick,
    failureTreeTypeMap,
    copyRecord,
    setCopyRecord,
    user,
  };

  const nodeRenderer = nodeElement => {
    const { record } = nodeElement;
    const key = record.get('path');

    let dragDisableStyle;

    if (drawRecord) {
      if (drawRecord.userId === user.id) {
        if (!['TOP', 'MIDDLE', 'BOTTOM'].includes(drawRecord?.tag)) {
          dragDisableStyle = true;
        } else if (['TOP'].includes(drawRecord?.tag)) {
          if (['CLASS'].includes(record?.get('failureTreeType'))) {
            dragDisableStyle = false;
          } else {
            dragDisableStyle = true;
          }
        } else if (['TOP', 'MIDDLE'].includes(record?.get('tag'))) {
          dragDisableStyle = false;
        } else {
          dragDisableStyle = true;
        }
      } else {
        dragDisableStyle = true;
      }
    } else {
      dragDisableStyle = false;
    }

    let title: any = [];
    const _failureTreeName = record.get('failureTreeName');
    const failureTreeName = (_failureTreeName || '').split('');
    const searchValueLength = searchValue.length;
    if (searchValue && _failureTreeName.toUpperCase().indexOf(searchValue.toUpperCase()) > -1) {
      const stringArr = _failureTreeName.toUpperCase().split(searchValue.toUpperCase());
      title = stringArr.map((stringArrItem, index) => (
        <span
          className={dragDisableStyle ? styles.dragDisableStyle : ''}
          key={`${stringArr.length - index}light${key}`}
        >
          {(failureTreeName.splice(0, stringArrItem.length) || []).join('')}
          {index < stringArr.length - 1 && (
            <span style={{ color: '#f02b2b', fontSize: '14px' }}>
              {(failureTreeName.splice(0, searchValueLength) || []).join('')}
            </span>
          )}
        </span>
      ));
    } else {
      title.push(
        <span className={dragDisableStyle ? styles.dragDisableStyle : ''} key={key}>
          {_failureTreeName}
        </span>,
      );
    }
    title.push(
      <div className="tree-node">
        <TreeNodeIcon record={record} {...iconProps} />
      </div>,
    );
    return title;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.problemTree`).d('故障树')} />
      <Content className={styles['content-inner']}>
        <div>
          <Form dataSet={searchDs} columns={3} labelLayout={LabelLayout.float}>
            <TextField
              name="searchValue"
              suffix={
                searchRows === 0
                  ? ''
                  : `${searchRows}${' '}${
                    searchRows > 1
                      ? intl.get(`${modelPrompt}.organizationSearchUnits`).d('条')
                      : intl.get(`${modelPrompt}.organizationSearchUnit`).d('条')
                  }`
              }
            />
            <div>
              <Button
                className={styles.btn}
                icon={iconType ? 'expand_more' : 'expand_less'}
                onClick={expandAll}
                key="expand_more"
              >
                {intl.get('tarzan.common.button.open').d('全部展开')}
              </Button>
              <Button className={styles.btn} icon="refresh" onClick={resetTextField}>
                {intl.get(`tarzan.common.button.reset`).d('重置')}
              </Button>
              <Button className={styles.btn} icon="search" onClick={searchTree}>
                {intl.get(`tarzan.common.button.search`).d('查询')}
              </Button>
            </div>
          </Form>
        </div>
        <Spin spinning={loading}>
          {halfContainerHeight && halfContainerHeight > 0 && (
            <Tree
              dataSet={treeDs}
              renderer={nodeRenderer}
              className="draggable-tree"
              draggable
              showLine
              height={halfContainerHeight}
              autoExpandParent={autoExpandParent}
              onClick={treeOnclick}
              onExpand={onExpand}
              onDrop={onDrop}
              selectable={false}
              onDragStart={onDragStart}
              onDragEnd={onDragEnd}
              onDragEnter={onDragEnter}
            />
          )}
        </Spin>
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [
    'tarzan.qms.problemTree',
    'tarzan.common',
    'hzero.common',
  ],
})(ProblemTree);
