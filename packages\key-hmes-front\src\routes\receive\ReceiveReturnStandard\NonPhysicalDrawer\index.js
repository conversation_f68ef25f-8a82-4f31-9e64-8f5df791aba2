/**
 * @Description: 创建送货单抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-12-13 10:31:13
 * @LastEditTime: 2023-05-18 15:33:33
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { pickingDetailsDS, receivingDetailsDS, returnDetailsDS } from '../stores/NonPhysicalDS';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.receive.receiveReturn';
const MaterialLotDrawer = props => {
  const { record, customizeTable } = props;
  const { instructionDocLineId, instructionDocType, instructionId, docTypeTag } = record;
  const pickingDetailsDs = useMemo(() => {
    return new DataSet(pickingDetailsDS());
  }, []);
  const receivingDetailsDs = useMemo(() => {
    return new DataSet(receivingDetailsDS());
  }, []);
  const returnDetailsDs = useMemo(() => {
    return new DataSet(returnDetailsDS());
  }, []);

  useEffect(() => {
    pickingDetailsDs.queryParameter = {
      instructionId,
      instructionDocType,
      instructionDocLineId,
      detailCategory: 'PICK',
    };
    returnDetailsDs.queryParameter = {
      instructionId,
      instructionDocType,
      instructionDocLineId,
      detailCategory: 'RETURN',
    };
    receivingDetailsDs.queryParameter = {
      instructionId,
      instructionDocType,
      instructionDocLineId,
      detailCategory: 'RECEIVE',
    };
    if (docTypeTag === 'PICK') {
      pickingDetailsDs.query();
      receivingDetailsDs.query();
    } else {
      returnDetailsDs.query();
    }
  }, [instructionDocLineId, instructionDocType, docTypeTag]);

  // 组件息表配置
  const pickColumns = [
    {
      name: 'materialCode',
      width: 180,
    },
    {
      name: 'qualityStatus',
      width: 120,
    },
    {
      name: 'sumActualQty',
      width: 140,
    },
    {
      name: 'uomCode',
      width: 60,
    },
    {
      name: 'lot',
      width: 140,
    },
    {
      name: 'receiveLocatorCode',
      width: 120,
    },
    {
      name: 'receiveDate',
      width: 120,
    },
    {
      name: 'receiveBy',
      width: 120,
    },
  ];

  const receiveColumns = [
    {
      name: 'materialCode',
      width: 180,
    },
    {
      name: 'qualityStatus',
      width: 120,
    },
    {
      name: 'sumActualQty',
      width: 140,
    },
    {
      name: 'uomCode',
      width: 60,
    },
    {
      name: 'lot',
      width: 140,
    },
    {
      name: 'signedLocatorCode',
      width: 120,
    },
    {
      name: 'signedDate',
      width: 120,
    },
    {
      name: 'signedBy',
      width: 120,
    },
  ];

  const returnColumns = [
    {
      name: 'materialCode',
      width: 180,
    },
    {
      name: 'qualityStatus',
      width: 120,
    },
    {
      name: 'sumActualQty',
      width: 140,
    },
    {
      name: 'uomCode',
      width: 60,
    },
    {
      name: 'lot',
      width: 140,
    },
    {
      name: 'returnLocatorCode',
      width: 120,
    },
    {
      name: 'returnDate',
      width: 120,
    },
    {
      name: 'returnName',
      width: 120,
    },
  ];

  return (
    <>
      {docTypeTag === 'PICK' ? (
        <>
          <Collapse bordered={false} defaultActiveKey={['sendDetail']}>
            <Panel
              header={intl.get(`${modelPrompt}.picking.detail`).d('拣配明细')}
              key="sendDetail"
            >
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
                },
                <Table dataSet={pickingDetailsDs} columns={pickColumns} />,
              )}
            </Panel>
          </Collapse>
          <Collapse bordered={false} defaultActiveKey={['receiveDetail']}>
            <Panel
              header={intl.get(`${modelPrompt}.receive.detail`).d('接收明细')}
              key="receiveDetail"
            >
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
                },
                <Table dataSet={receivingDetailsDs} columns={receiveColumns} />,
              )}
            </Panel>
          </Collapse>
        </>
      ) : (
        <Collapse bordered={false} defaultActiveKey={['sendDetail']}>
          <Panel header={intl.get(`${modelPrompt}.return.detail`).d('退料明细')} key="sendDetail">
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
              },
              <Table dataSet={returnDetailsDs} columns={returnColumns} />,
            )}
          </Panel>
        </Collapse>
      )}
    </>
  );
};

export default MaterialLotDrawer;
