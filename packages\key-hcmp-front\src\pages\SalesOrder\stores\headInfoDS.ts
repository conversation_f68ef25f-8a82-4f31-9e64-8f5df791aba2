import { FieldIgnore, FieldType } from 'choerodon-ui/dataset/data-set/enum';
import DataSet, { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { BASIC } from '@utils/config';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';


const tenantId = getCurrentOrganizationId();
const userId = getCurrentUserId();
const intlPrefix = 'tarzan.aps.sales.managememt';

const headInfoDS: () => DataSetProps = () => ({
  autoQuery: true,
  dataKey: 'rows.content',
  selection: false,
  totalKey: 'rows.totalElements',
  queryDataSet: new DataSet({
    fields: [
      {
        name: 'soNumberList',
        label: intl.get(`${intlPrefix}.head.soNumberList`).d('销售订单编码'),
        type: FieldType.string,
      },
      {
        name: 'siteCode',
        label: intl.get(`${intlPrefix}.head.siteCode`).d('站点编码'),
        type: FieldType.object,
        lovCode: 'MT.APS.SITE',
        lovPara: {
          userId,
          tenantId,
          siteType: 'MANUFACTURING',
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'siteId',
        type: FieldType.number,
        bind: 'siteCode.siteId',
      },
      {
        name: 'customerCodeList',
        label: intl.get(`${intlPrefix}.head.customerCodeList`).d('客户编码'),
        type: FieldType.string,
      },
      {
        name: 'soTypeList',
        label: intl.get(`${intlPrefix}.head.soTypeList`).d('销售订单类型'),
        type: FieldType.string,
        lookupCode: 'SO_LINE_TYPE',
      },
      {
        name: 'soStatusList',
        label: intl.get(`${intlPrefix}.head.soStatusList`).d('销售订单状态'),
        type: FieldType.string,
        lookupCode: 'SO_LINE_STATUS',
      },
      {
        name: 'customerSiteDescList',
        label: intl.get(`${intlPrefix}.head.customerSiteDescList`).d('客户地点'),
        type: FieldType.string,
      },
    ],
  }),
  fields: [
    {
      name: 'soId',
      label: intl.get(`${intlPrefix}.head.soId`).d('销售订单id'),
      type: FieldType.number,
    },
    {
      name: 'soNumber',
      label: intl.get(`${intlPrefix}.head.soNumber`).d('销售订单编码'),
      type: FieldType.string,
    },
    {
      name: 'siteCode',
      label: intl.get(`${intlPrefix}.head.siteCode`).d('站点编码'),
      type: FieldType.object,
      required: true,
      lovCode: 'MT.APS.SITE',
      lovPara: {
        tenantId,
        userId,
        siteType: 'MANUFACTURING',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('siteCode') && record.get('siteCode').siteId ? 'siteCode.siteId' : '';
        },
      },
    },
    {
      name: 'customerCode',
      label: intl.get(`${intlPrefix}.head.customerCode`).d('客户编码'),
      type: FieldType.object,
      lovCode: 'MT.APS.CUSTOMER',
      lovPara: {
        tenantId,
        userId,
      },
      ignore: FieldIgnore.always,
      required: true,
      textField: 'customerCode',
    },
    {
      name: 'customerId',
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('customerCode') && record.get('customerCode').customerId
            ? 'customerCode.customerId'
            : '';
        },
      },
    },
    {
      name: 'customerNameAlt',
      label: intl.get(`${intlPrefix}.head.customerNameAlt.alt`).d('客户简称'),
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('customerCode') && record.get('customerCode').customerId
            ? 'customerCode.customerNameAlt'
            : '';
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerName',
      label: intl.get(`${intlPrefix}.head.customerName`).d('客户名称'),
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('customerCode') && record.get('customerCode').customerId
            ? 'customerCode.customerName'
            : '';
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerSiteCode',
      label: intl.get(`${intlPrefix}.head.customerSiteCode`).d('客户地点编码'),
      type: FieldType.object,
      lovCode: 'MT.APS.CUSTOMER_SITE',
      required: true,
      lovPara: {
        tenantId,
        userId,
      },
      textField: 'customerSiteCode',
      cascadeMap: { customerId: 'customerId' },
    },
    {
      name: 'customerSiteName',
      label: intl.get(`${intlPrefix}.head.customerSiteName`).d('客户地点名称'),
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('customerSiteCode') && record.get('customerSiteCode').description
            ? 'customerSiteCode.description'
            : '';
        },
      },
    },
    {
      name: 'customerSiteId',
      label: intl.get(`${intlPrefix}.head.customerSiteId`).d('客户地点id'),
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('customerSiteCode') && record.get('customerSiteCode').customerSiteId
            ? 'customerSiteCode.customerSiteId'
            : '';
        },
      },
    },
    {
      name: 'soType',
      label: intl.get(`${intlPrefix}.head.soType`).d('销售订单类型'),
      type: FieldType.string,
      required: true,
      lookupCode: 'SO_LINE_TYPE',
    },
    {
      name: 'soStatus',
      label: intl.get(`${intlPrefix}.head.soStatus`).d('销售订单状态'),
      type: FieldType.string,
      defaultValue: 'USABLE',
      lookupCode: 'SO_LINE_STATUS',
    },
    {
      name: 'shipToCode',
      label: intl.get(`${intlPrefix}.head.shipToCode`).d('收货方客户编码'),
      type: FieldType.object,
      lovCode: 'MT.APS.CUSTOMER',
      lovPara: { tenantId, userId },
      ignore: FieldIgnore.always,
      textField: 'customerCode',
    },
    {
      name: 'shipToId',
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('shipToCode') && record.get('shipToCode').customerId
            ? 'shipToCode.customerId'
            : '';
        },
      },
    },
    {
      name: 'shipToSiteDesc',
      label: intl.get(`${intlPrefix}.head.shipToSiteDesc`).d('收货方地址'),
      type: FieldType.string,
    },
    {
      name: 'salesPerson',
      label: intl.get(`${intlPrefix}.head.salesPerson`).d('销售员'),
      type: FieldType.string,
    },
    {
      name: 'freezeFlag',
      label: intl.get(`${intlPrefix}.head.freezeFlag`).d('信用冻结标识'),
      type: FieldType.string,
      lookupCode: 'MT.APS.YES_NO',
      lovPara: { tenantId, userId },
    },
    { name: 'freezeFlagMeaning', type: FieldType.string, ignore: FieldIgnore.always },
    {
      name: 'remark',
      label: intl.get(`${intlPrefix}.head.remark`).d('订单说明'),
      type: FieldType.string,
    },
    {
      name: 'currencyCode',
      label: intl.get(`${intlPrefix}.head.currencyCode`).d('币种'),
      type: FieldType.string,
    },
    {
      name: 'soCategory',
      label: intl.get(`${intlPrefix}.head.soCategory`).d('业务类别'),
      type: FieldType.string,
    },
    {
      name: 'salesChannel',
      label: intl.get(`${intlPrefix}.head.salesChannel`).d('销售渠道'),
      type: FieldType.string,
    },
    {
      name: 'commercialTerm',
      label: intl.get(`${intlPrefix}.head.commercialTerm`).d('国际贸易条件'),
      type: FieldType.string,
    },
    {
      name: 'paymentTerm',
      label: intl.get(`${intlPrefix}.head.paymentTerm`).d('付款条件'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-orders/head/list/for/ui`,
        data,
        method: 'POST',
        transformResponse: (value) => {
          let listData: any = {};
          try {
            listData = JSON.parse(value);
          } catch (err) {
            listData = {
              message: err,
            };
          }
          if (!listData.success) {
            return {
              ...listData,
              failed: true,
            };
          }
          return listData;

        },
      };
    },
    submit: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-orders/head/save/for/ui`,
        data: data[0],
        method: 'POST',
        transformResponse: (response) => {
          try {
            const jsonData = JSON.parse(response);
            if (jsonData.success === false) {
              return {
                type: 'warn',
                failed: true,
                message: jsonData.message,
              };
            }
            return jsonData;

          } catch {
            /**/
          }
          return response;
        },
      };
    },
    update: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-orders/head/save/for/ui`,
        data: data[0],
        method: 'POST',
        transformResponse: (response) => {
          try {
            const jsonData = JSON.parse(response);
            if (jsonData.success === false) {
              return {
                type: 'warn',
                failed: true,
                message: jsonData.message,
              };
            }
            return jsonData;

          } catch {
            /**/
          }
          return response;
        },
      };
    },
    create: ({ data }) => {
      return {
        data: data[0],
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-orders/head/save/for/ui`,
        method: 'POST',
        transformResponse: (response) => {
          try {
            const jsonData = JSON.parse(response);
            if (jsonData.success === false) {
              return {
                type: 'warn',
                failed: true,
                message: jsonData.message,
              };
            }
            return jsonData;

          } catch {
            /**/
          }
          return response;
        },
      };
    },
  },
});
export default headInfoDS;
