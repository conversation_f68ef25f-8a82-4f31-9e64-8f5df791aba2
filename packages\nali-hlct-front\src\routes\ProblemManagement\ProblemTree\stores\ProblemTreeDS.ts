/**
 * RelationMaintain - 故障树-DS
 * @date: 2023-8-24
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.qms.problemTree';
const tenantId = getCurrentOrganizationId();

// 列表-ds
const treeDS = (): DataSetProps => ({
  primaryKey: 'path',
  parentField: 'parentPath',
  expandField: 'expand',
  idField: 'path',
  fields: [
    {
      name: 'expand',
      type: FieldType.boolean,
    },
    {
      name: 'path',
      type: FieldType.string,
    },
    {
      name: 'parentPath',
    },
    {
      name: 'failureTreeName',
    },
    {
      name: 'failureTreeId',
    },
    {
      name: 'parentOrganizationId',
    },
    {
      name: 'failureTreeCode',
    },
    {
      name: 'failureTreeType',
    },
    {
      name: 'failureTreeEventDTO2List',
    },
    {
      type: FieldType.object,
      name: 'addRelation',
      lovCode: 'YP.QIS.FAILURE_TREE_EVENT',
      dynamicProps: {
        lovPara: ({ dataSet, record }) => {
          let filterTagType;
          if (record.get('failureTreeType') === 'FAILURE_MODE') {
            filterTagType = 'TOP';
          }
          if (record.get('failureTreeType') === 'MIDDLE_FAILURE') {
            filterTagType = 'MIDDLE';
          }
          if (record.get('failureTreeType') === 'FAILURE_REASON') {
            filterTagType = 'BOTTOM';
          }
          return {
            userId: dataSet.getState('user').id,
            filterPath: record.get('path'),
            filterFailureTreeId: record.get('failureTreeId'),
            filterTagType,
            tenantId,
          };
        },
      },
      multiple: true,
    },
  ],
});

// 列表-ds
const searchDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.search`).d('搜索'),
      name: 'searchValue',
    },
  ],
});

// 新建下级及关系
const addSubRelationDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureTreeName`).d('事件名称'),
      name: 'failureTreeName',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureTreeType`).d('事件类型'),
      name: 'failureTreeType',
      lookupCode: 'YP.QIS.FAILURE_TREE_EVENT_TYPE',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureTreeCode`).d('事件编码'),
      name: 'failureTreeCode',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编号'),
      name: 'relatedProblem',
      lovCode: 'YP.QIS.PROBLEM_INFO',
      textField: 'problemTitle',
      valueField: 'problemId',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            siteId: dataSet.getState('siteId'),
            tenantId,
          };
        },
        disabled: ({ record }) => {
          return record.get('failureTreeType') !== 'FAILURE_REASON';
        },
      },
      multiple: true,
    },
    {
      name: 'problemId',
      bind: 'relatedProblem.problemId',
    },
    {
      name: 'problemTitle',
      bind: 'relatedProblem.problemTitle',
    },
    {
      name: 'mainDepartment',
      bind: 'relatedProblem.mainDepartment',
    },
    {
      name: 'problemCode',
      bind: 'relatedProblem.problemCode',
    },
    {
      name: 'problemCategory',
      bind: 'relatedProblem.problemCategory',
    },
  ],
  events: {
    update: ({ dataSet, record, name, value }) => {
      if (name === 'relatedProblem') {
        dataSet.children.relatedProblem.loadData(value || []);
      }
      if (name === 'failureTreeType') {
        record.set('relatedProblem', null);
      }
    },
  },
});

// 新建下级及关系表格
const addSubRelationTableDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'problemId',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编号'),
      name: 'problemCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
      name: 'problemTitle',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      name: 'problemCategory',
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      lovPara: {
        tenantId,
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mainDepartment`).d('主责部门'),
      name: 'mainDepartment',
    },
  ],
});

export { treeDS, searchDS, addSubRelationDS, addSubRelationTableDS };
