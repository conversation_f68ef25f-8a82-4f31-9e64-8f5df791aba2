/**
 * @Description: 消息处理查询
 * @Author: <<EMAIL>>
 * @Date: 2022-01-29 20:26:37
 * @LastEditTime: 2023-08-22 15:58:44
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useEffect, useState } from 'react';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import { DataSet, Modal, Select, Table, CodeArea } from 'choerodon-ui/pro';
import JSONFormatter from 'choerodon-ui/pro/lib/code-area/formatters/JSONFormatter';
import { Tag } from 'choerodon-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { Tooltip } from 'choerodon-ui/pro/lib/core/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { useRequest } from '@components/tarzan-hooks';
import OverrideTableBarC7n from './components/OverrideTableBarC7n';
import { tableDS, servicesDS } from './stores/MessageProcessingDS';
import { ResendMessage, FetchMessageContent } from './services';
import styles from './index.module.less';

const { Option } = Select;
const modelPrompt = 'tarzan.message.message.messageProcessing';
type MessageType = 'send' | 'receive';

const calcMessageDetailUrl = (services, messageType) => {
  if (messageType === 'send') {
    return `/${services}/v1/${getCurrentOrganizationId()}/mt-message-receive-records/detail/ui`;
  }
  return `/${services}/v1/${getCurrentOrganizationId()}/mt-message-send-records/detail/ui`;
};

const Arrow = observer(({ ds }) => {
  return (
    <span>&nbsp;{ds.current?.get('messageType') === 'send' ? '-->' : '<--'}&nbsp;</span>
  )
});

const MessageProcessingList = props => {
  const {
    match: { path },
  } = props;

  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const servicesDs = useMemo(() => new DataSet(servicesDS()), []);

  const [selectedLineIds, setSelectedLineIds] = useState<number[]>([]);
  const resendMessage = useRequest(ResendMessage(''), { manual: true });
  const fetchMessageContent = useRequest(FetchMessageContent('', ''), { manual: true });
  const fetchMessageDetail = useRequest({ method: 'POST' }, { manual: true, needPromise: true }); // 获取拼接数据
  const ReconsumeMessage = useRequest({ method: 'POST' }, { manual: true, needPromise: true }); // 异常处理

  const [addedColumns, setAddedColumns] = useState<ColumnProps[]>([])

  useEffect(() => {
    tableDs.setState('messageType', 'send');
  }, [])

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  const listener = flag => {
    // 列表行选择交互监听
    if (tableDs) {
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(tableDs, 'batchSelect', handleLineTableChange);
      handler.call(tableDs, 'batchUnSelect', handleLineTableChange);
      handler.call(tableDs, 'query', handleLineTableChange);
      handler.call(tableDs, 'load', ({ dataSet }) => splicedColumn(dataSet.toData()));
    }
  };

  const handleLineTableChange = ({ dataSet }) => {
    const _setSelectedLineIds: number[] = [];
    setTimeout(() => {
      dataSet.selected.forEach(item => {
        _setSelectedLineIds.push(item.toData().globalMessageId);
      });
      setSelectedLineIds(_setSelectedLineIds);
    }, 0);
  };

  const handleShowContentDetail = record => {
    fetchMessageContent.run({
      params: [record.get('globalMessageId')],
      // {
      //   globalMessageId: record.get('globalMessageId'),
      // },
      tempUrl: FetchMessageContent(servicesDs!.current!.get('sendServicesCode'), tableDs.getState('messageType')).url,
      onSuccess: res => {
        Modal.open({
          key: Modal.key(),
          title: record.get('topic'),
          drawer: true,
          style: {
            width: 1080,
          },
          children: (
            <CodeArea
              readOnly
              className={styles['code-area']}
              style={{
                height: 'auto',
                minHeight: 600,
                maxHeight: 780,
              }}
              value={(res[0] || {}).content}
              formatter={JSONFormatter}
            />
          ),
          okButton: false,
          cancelText: intl.get('tarzan.common.button.back').d('返回'),
        });
      },
    });
  };

  const basicColumns: ColumnProps[] = useMemo(
    () => {
      const _columns: ColumnProps[] = [
        {
          name: 'topic',
          width: 240,
          renderer: ({ value, record }) => (
            <a onClick={() => handleShowContentDetail(record)}>{value}</a>
          ),
        },
        {
          name: 'dealStatus',
          width: 120,
          align: ColumnAlign.center,
          renderer: ({ value, record }) => {
            let calssName = 'red';
            if (['SEND_SUCCESS', 'CONSUME_SUCCESS', 'RE_SEND_SUCCESS'].includes(value)) {
              calssName = 'green';
            } else if (value === 'UN_SEND') {
              calssName = 'orange';
            }
            return value && <Tag color={calssName}>{record!.getField('dealStatus')!.getText() || value}</Tag>;
          },
        },
        {
          name: 'dealTime',
          width: 180,
          align: ColumnAlign.center,
        },
        {
          name: 'dealMessage',
          width: 240,
          tooltip: Tooltip.overflow,
        },
        {
          name: 'originalMessageId',
          width: 120,
          tooltip: Tooltip.overflow,
        },
      ];
      if (tableDs.getState('messageType') === 'receive') {
        // 消费者
        _columns.splice(3, 0, ...[{
          header: '处理消息时长(ms)',
          name: `dealTimeCost`,
          width: 120,
          tooltip: Tooltip.overflow,
        },
        {
          header: '重复消费次数',
          name: `reDealTimes`,
          width: 120,
          tooltip: Tooltip.overflow,
        }])
      }
      return _columns;
    },
    [tableDs.getState('messageType')],
  );

  const handleReExecute = () => {
    if (tableDs.getState('messageType') !== 'send') {
      notification.error({
        message: '消费者类型不允许重新执行',
      });
      return;
    }
    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <div>
          {intl
            .get(`${modelPrompt}.deleteConfirm`, {
              number: selectedLineIds.length,
            })
            .d(`是否确认重新执行选中的${selectedLineIds.length}条数据?`)}
        </div>
      ),
    }).then(button => {
      if (button === 'ok') {
        resendMessage.run({
          params: selectedLineIds,
          tempUrl: ResendMessage(servicesDs!.current!.get('sendServicesCode')).url,
          onSuccess: () => {
            notification.success({});
            setSelectedLineIds([]);
            tableDs.batchUnSelect(tableDs.selected);
          },
        });
      }
    });
  };

  const handleExceptionHandle = async () => {
    const api = tableDs.selected[0].get('messageServer') || servicesDs.current?.get('sendServicesCode');
    ReconsumeMessage.run({
      tempUrl: `/${api}/v1/${getCurrentOrganizationId()}/mt-message-receive-records/re-consume/ui`,
      params: tableDs.selected.map(item => item.get('globalMessageId')),
    }).then(() => {
      tableDs.query();
    })
  }

  const handleChangeSendServices = value => {
    tableDs.setQueryParameter('services', value);
    if (!value) {
      tableDs.loadData([]);
      return;
    }
    tableDs.query()
  };

  const handleChangeReceiveServices = () => {
    splicedColumn(tableDs.toData());
  };

  const handleChangeMessageType = (value: MessageType) => {
    tableDs.setState('messageType', value);
  }

  const splicedColumn = (tableList) => {
    const _globalMessageIdArr = tableList.map(item => item.globalMessageId);
    const _receiveServicesCodeArr = toJS(servicesDs.current?.get('receiveServicesCode'));
    if (_globalMessageIdArr.length && _receiveServicesCodeArr.length) {
      _receiveServicesCodeArr.forEach(service => {
        fetchMessageDetail.run({
          tempUrl: calcMessageDetailUrl(service, tableDs.getState('messageType')),
          params: _globalMessageIdArr,
        }).then((res) => {
          tableDs.forEach(item => {
            const _id = item.get('globalMessageId');
            const _newAppend = res.rows.filter(it => it.globalMessageId === _id)
            if (_newAppend.length) {
              Object.keys(_newAppend[0]).forEach(i => {
                item.init(`${service}${i}`, _newAppend[0][i]);
              });
            }
          })
        })
      });
    }
    if (_receiveServicesCodeArr.length) {
      const _addedColumns: ColumnProps[] = [];
      _receiveServicesCodeArr.forEach(item => {
        const _addedColumn: ColumnProps = {
          // @ts-ignore
          header: toJS(servicesDs.getField('sendServicesCode')?.lookup || []).filter(it => it.value === item)[0].description,
          name: `${item}Header`,
          width: 840,
          children: [
            {
              header: '执行状态',
              name: `${item}dealStatus`,
              width: 120,
              renderer: ({ value }) => {
                let calssName = 'red';
                if (['SEND_SUCCESS', 'CONSUME_SUCCESS', 'RE_SEND_SUCCESS'].includes(value)) {
                  calssName = 'green';
                } else if (value === 'UN_SEND') {
                  calssName = 'orange';
                }
                const statusMap = {}
                // @ts-ignore
                toJS(tableDs.getField('dealStatus')?.lookup || []).forEach((statusItem: any) => {
                  if (statusItem.value && !statusMap[statusItem.value]) {
                    statusMap[statusItem.value] = statusItem.meaning
                  }
                })
                return value && <Tag color={calssName}>{statusMap[value] || value}</Tag>;
              },
            },
            {
              header: '消息处理时间',
              name: `${item}dealTime`,
              width: 180,
              align: ColumnAlign.center,
            },
            {
              header: '异常信息',
              name: `${item}dealMessage`,
              width: 240,
              align: ColumnAlign.center,
            },
            {
              header: '处理消息时长(ms)',
              name: `${item}dealTimeCost`,
              width: 120,
              tooltip: Tooltip.overflow,
            },
            {
              header: '重复消费次数',
              name: `${item}reDealTimes`,
              width: 120,
              tooltip: Tooltip.overflow,
            },
            {
              header: '关联来源消息ID',
              name: `${item}originalMessageId`,
              width: 120,
              tooltip: Tooltip.overflow,
            },
          ],
        };
        if (tableDs.getState('messageType') === 'receive') {
          _addedColumn.children!.splice(3, 2);
        }
        _addedColumns.push(_addedColumn)
      })
      setAddedColumns(_addedColumns);
    } else {
      setAddedColumns([]);
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.messageProcessingQuery`).d('消息处理查询')}>
        <Select
          style={{ width: 140 }}
          dataSet={servicesDs}
          name="receiveServicesCode"
          onChange={handleChangeReceiveServices}
        />
        <Arrow ds={servicesDs} />
        <Select
          style={{ width: 120 }}
          dataSet={servicesDs}
          name="sendServicesCode"
          onChange={handleChangeSendServices}
        />
        <Select
          dataSet={servicesDs}
          name="messageType"
          style={{ width: 80, marginLeft: '8px' }}
          clearButton={false}
          onChange={handleChangeMessageType}
        >
          <Option value="send">生产者</Option>
          <Option value="receive">消费者</Option>
        </Select>
        <PermissionButton
          type="c7n-pro"
          onClick={handleReExecute}
          disabled={!selectedLineIds.length || servicesDs.current?.get('messageType') !== 'send'}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.reExecute`).d('重新执行')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          onClick={handleExceptionHandle}
          disabled={!selectedLineIds.length || servicesDs.current?.get('messageType') !== 'receive'}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.handleExecute`).d('异常处理')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={basicColumns.concat(addedColumns)}
          queryBar={args => {
            return OverrideTableBarC7n({
              ...args,
              // @ts-ignore
              queryValidateDs: servicesDs,
              // queryDisabled: servicesDs.current.get('messageType') || servicesDs.current.get('sendServicesCode') || servicesDs.current.get('receiveServicesCode'),
            });
          }}
          // onRow={({ record }) => {
          //   return {
          //     className:
          //       record!.get('overTimeFlag') === 'Y' ? styles['hcm-message-overtime-line'] : null,
          //   };
          // }}
          selectedHighLightRow={false}
          highLightRow={false}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.message.message.messageProcessing', 'tarzan.common'],
})(MessageProcessingList);
