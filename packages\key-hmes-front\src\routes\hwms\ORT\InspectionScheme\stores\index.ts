/**
 * @Description: ORT检验方案维护-主界面DS
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ort.InspectionScheme';

const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'schemeId',
  queryFields: [
    {
      name: 'inspectSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
    },
    {
      name: 'inspectSchemeName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSchemeName`).d('检验方案名称'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialName',
      lovPara: { tenantId },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reinspectPermissionFlag`).d('是否生效'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      name: 'inspectSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
    },
    {
      name: 'inspectSchemeName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSchemeName`).d('检验方案名称'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'enableFlag',
      lookupCode: 'MT.YES_NO',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('是否生效'),
    },
    {
      name: 'enableDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableDate`).d('生效时间'),
    },
    {
      name: 'disableDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disableDate`).d('失效时间'),
    },
    {
      name: 'createdName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdName`).d('创建人'),
    },
  ],
  transport: {
    read: ({data}) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ort-inspect-scheme/list/ui`,
        method: 'GET',
        data,
      };
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'taskId',
  fields: [
    {
      name: 'sequence',
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'inspectItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItem`).d('测试项目名称'),
    },
    {
      name: 'inspectMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('测试方法'),
    },
    {
      name: 'standardRequirement',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardRequirement`).d('标准要求'),
    },
    {
      name: 'inspectFrequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectFrequency`).d('测试频率'),
    },
    {
      name: 'outsourceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsourceFlag`).d('委外标识'),
      lookupCode: 'YP.QIS.Y_N',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ort-inspect-scheme-line/line/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { headDS, lineDS };
