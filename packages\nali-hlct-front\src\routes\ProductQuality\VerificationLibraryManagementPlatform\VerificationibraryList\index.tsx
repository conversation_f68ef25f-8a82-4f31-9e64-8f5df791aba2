/**
 * @Description: 检验项目组维护查询界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:29:43
 * @LastEditTime: 2023-05-24 16:56:17
 * @LastEditors: <<EMAIL>>
 */

import React, { useCallback, useEffect, useState } from 'react';
import { DataSet, Dropdown, Form, Lov, Menu, Modal, Table, TextArea } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import request from 'utils/request';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { approveDS, ListTableDS, modalDS } from '../stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.qms.verificationLibraryManagementPlatform';

const VerificationibraryList = props => {
  const { tableDS, approvalDs, modalDs, history } = props;

  // 表格勾选数据
  const [tableSelectList, setTableSelectList] = useState(Array);
  const [tableSelectStatus, setTableSelectStatus] = useState([]);

  const handleTableSelect = ({ dataSet }) => {
    setTableSelectList(dataSet.selected || []);
    const statusList = dataSet.selected.map(item => item.data.verificationStatus);
    setTableSelectStatus(statusList);
  };

  useDataSetEvent(tableDS, 'select', handleTableSelect);
  useDataSetEvent(tableDS, 'selectAll', handleTableSelect);
  useDataSetEvent(tableDS, 'unselect', handleTableSelect);
  useDataSetEvent(tableDS, 'unselectAll', handleTableSelect);

  useEffect(() => {
    tableDS.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.LIST`,
    );

    if (tableDS?.currentPage) {
      tableDS.query(props.tableDS.currentPage);
    } else {
      tableDS.query();
    }
  }, []);

  const handleAdd = useCallback(() => {
    history.push({
      pathname: '/hwms/verification-library-management-platform/dist/create',
    });
  }, []);

  const handleUnenabled = () => {
    const params = tableDS.selected.map(item => item.data.verificationId);
    return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/expired/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        notification.success({});
        tableDS.query();
        setTableSelectList([]);
        setTableSelectStatus([]);
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  const handleSubmit = () => {
    const params = tableDS.selected.map(item => item.data.verificationId);
    return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/submit/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        notification.success({});
        tableDS.query();
        setTableSelectList([]);
        setTableSelectStatus([]);
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  const handleApprove = () => {
    const params = tableDS.selected.map(item => item.data.verificationId);
    return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/approval/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        notification.success({});
        tableDS.query();
        setTableSelectList([]);
        setTableSelectStatus([]);
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  const handleSelectLov = value => {
    if (!value) {
      return;
    }
    props.history.push({
      pathname: `/hwms/verification-library-management-platform/dist/create`,
      state: { ...value },
    });
  };

  const handleunApprove = () => {
    if (tableSelectList.length > 1) {
      notification.error({
        message: intl.get(`${modelPrompt}.onlySelectOne`).d(`不允许批量驳回，驳回只能选择1条数据`),
      });
    } else {
      approvalDs.create({});
      const modal = Modal.open({
        title: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
        destroyOnClose: true,
        children: (
          <Form dataSet={approvalDs}>
            <TextArea name="rejectReason" required />,
          </Form>
        ),
        // drawer: true,
        onOk: async () => {
          if (!approvalDs.toData()[0].rejectReason) {
            notification.error({
              message: intl.get(`${modelPrompt}.pleaseEnter.rejectReason`).d('请输入驳回原因'),
            });
            return false;
          }
          const params = {
            verificationId: tableDS.selected[0].data.verificationId,
            rejectReason: approvalDs.toData()[0].rejectReason,
          };
          return request(
            `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/reject/ui`,
            {
              method: 'POST',
              body: params,
            },
          ).then(res => {
            if (res && !res.failed) {
              notification.success({});
              tableDS.query();
              modal.close();
              setTableSelectList([]);
              setTableSelectStatus([]);
            } else {
              notification.error({ message: res.message });
              return false;
            }
          });
        },
      });
    }
  };

  const columns: ColumnProps[] = [
    {
      name: 'siteName',
      align: ColumnAlign.center,
    },
    {
      name: 'verificationCode',
      align: ColumnAlign.center,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(
                `/hwms/verification-library-management-platform/dist/${record?.get(
                  'verificationId',
                )}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'verificationStatus',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'materialCode',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'materialName',
      width: 120,
      align: ColumnAlign.center,
    },
    // {
    //   name: 'itemGroup',
    //   width: 120,
    //   // align: ColumnAlign.center,
    // },
    {
      name: 'verificationFromMeaning',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationTypeMeaning',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'productType',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationPeriodMeaning',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'problemCode',
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              if (!record?.get('problemId')) {
                return;
              }
              props.history.push(
                `/hwms/problem-management/problem-management-platform/dist/${record?.get(
                  'problemId',
                )}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'fromProcessName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'startupDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'expireDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'failureModeMeaning',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'creationDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'createdByName',
      width: 120,
      align: ColumnAlign.center,
    },
    // {
    //   name: 'rejectIdName',
    //   width: 120,
    //   align: ColumnAlign.center,
    // },
    {
      name: 'rejectReason',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'reviewByName',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewDate',
      align: ColumnAlign.center,
    },
    // {
    //   name: 'rejectTime',
    //   width: 120,
    //   align: ColumnAlign.center,
    // },
  ];

  const menu = (
    <Menu>
      <Menu.Item
        disabled={
          tableSelectList.length === 0 || tableSelectStatus.some(item => item !== 'IN_APPROVAL')
        }
      >
        <a target="_blank" rel="noopener noreferrer" onClick={handleApprove}>
          {intl.get(`${modelPrompt}.agree`).d('同意')}
        </a>
      </Menu.Item>
      <Menu.Item
        disabled={
          tableSelectList.length === 0 || tableSelectStatus.some(item => item !== 'IN_APPROVAL')
        }
      >
        <a target="_blank" rel="noopener noreferrer" onClick={handleunApprove}>
          {intl.get(`${modelPrompt}.reject`).d('驳回')}
        </a>
      </Menu.Item>
    </Menu>
  );
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.verification.management`).d("检证库管理")}>
        {/* {user.currentRoleLabels && user.currentRoleLabels.includes('YP.QMS.PROQ_SENIOR_MANAGER') && ( */}
        <>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            // permissionList={[
            //   {
            //     code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.unenable`,
            //     type: 'button',
            //     meaning: '失效',
            //   },
            // ]}
            disabled={
              tableSelectList.length === 0 || tableSelectStatus.some(item => item !== 'COMPLETED')
            }
            onClick={handleUnenabled}
          >
            {intl.get(`${modelPrompt}.failure`).d('失效')}
          </PermissionButton>
          <Dropdown overlay={menu}>
            <PermissionButton
              disabled={
                tableSelectList.length === 0 ||
                tableSelectStatus.some(item => item !== 'IN_APPROVAL')
              }
              type="c7n-pro"
              color={ButtonColor.primary}
            // permissionList={[
            //   {
            //     code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.check`,
            //     type: 'button',
            //     meaning: '审批按钮',
            //   },
            // ]}
            >
              {intl.get(`${modelPrompt}.approve`).d('审批')}
            </PermissionButton>
          </Dropdown>
        </>
        {/* )} */}
        {/* {user.currentRoleLabels && user.currentRoleLabels.includes('YP.QMS.PROQ_ENGINNER') && ( */}
        <>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            // permissionList={[
            //   {
            //     code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.submit`,
            //     type: 'button',
            //     meaning: '提交按钮',
            //   },
            // ]}
            disabled={
              tableSelectList.length === 0 ||
              (tableSelectStatus.some(item => item !== 'NEW') &&
                tableSelectStatus.some(item => item !== 'REJECTED'))
            }
            onClick={handleSubmit}
          >
            {intl.get(`${modelPrompt}.submit`).d('提交')}
          </PermissionButton>
          <Lov
            dataSet={modalDs}
            name="problemLov"
            mode={ViewMode.button}
            clearButton={false}
            icon="file_upload"
            onChange={handleSelectLov}
          >
            {intl.get(`${modelPrompt}.problem.management.import`).d('问题管理导入')}
          </Lov>
          {/* <PermissionButton */}
          {/*  type="c7n-pro" */}
          {/*  color={ButtonColor.primary} */}
          {/*  permissionList={[ */}
          {/*    { */}
          {/*      code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.import`, */}
          {/*      type: 'button', */}
          {/*      meaning: '问题管理导入按钮', */}
          {/*    }, */}
          {/*  ]} */}
          {/*  icon="file_upload" */}
          {/*  onClick={handleProblemImport} */}
          {/* > */}
          {/*  问题管理导入 */}
          {/* </PermissionButton> */}
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={handleAdd}
          // permissionList={[
          //   {
          //     code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.new`,
          //     type: 'button',
          //     meaning: '新建按钮',
          //   },
          // ]}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
        </>
        {/* )} */}
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDS}
          columns={columns}
          searchCode="VerificationLibraryManagementPlatform"
          customizedCode="VerificationLibraryManagementPlatform"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [
    'tarzan.qms.inspectGroupMaintenance',
    'tarzan.common',
    'hzero.common',
    'tarzan.qms.inspectGroupMaintenance.model',
    'tarzan.qms.verificationLibraryManagementPlatform',
  ],
})(
  withProps(
    () => {
      const tableDS = new DataSet({
        ...ListTableDS(),
      });
      const approvalDs = new DataSet({
        ...approveDS(),
      });
      const modalDs = new DataSet({
        ...modalDS(),
      });
      return {
        tableDS,
        approvalDs,
        modalDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(VerificationibraryList),
);
