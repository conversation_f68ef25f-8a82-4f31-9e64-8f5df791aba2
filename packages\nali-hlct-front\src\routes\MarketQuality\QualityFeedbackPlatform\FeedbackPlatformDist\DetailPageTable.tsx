/*
 * @Description: 质量反馈单-详情页行表
 * @Author: <<EMAIL>>
 * @Date: 2023-09-13 15:17:46
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-11 16:27:53
 */
import React, { useMemo, useCallback } from 'react';
import intl from 'utils/intl';
import { Table, Button, Switch } from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';

export default ({ ds, canEdit }) => {
  const handleAdd = useCallback(() => {
    let _maxSeq = 0;
    ds.forEach(_record => {
      if (_record?.get('feedbackItemNum') > _maxSeq) {
        _maxSeq = _record?.get('feedbackItemNum');
      }
    });
    ds.create({ feedbackItemNum: _maxSeq + 10 });
  }, [ds]);

  const handleDeleteLine = record => {
    if (record?.get('feedbackItemId')) {
      record?.set('deleteFlag', true);
    } else {
      ds.remove(record);
    }
    // 已经存在表里的行，序号不变，其他序号递增
    let _maxSeq = 0;
    ds.forEach(_record => {
      if (_record?.get('feedbackItemNum') > _maxSeq && _record?.get('feedbackItemId')) {
        _maxSeq = _record?.get('feedbackItemNum');
      } else {
        _record.set('feedbackItemNum', _maxSeq + 10);
        _maxSeq += 10;
      }
    });
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <Button
            icon="add"
            disabled={!canEdit}
            funcType={FuncType.flat}
            onClick={handleAdd}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        width: 60,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDeleteLine(record)}
          >
            <Button icon="remove" disabled={!canEdit} funcType={FuncType.flat} size={Size.small} />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      { name: 'feedbackItemNum', align: ColumnAlign.left },
      {
        name: 'partsCode',
        editor: () => canEdit,
      },
      {
        name: 'partsName',
        editor: () => canEdit,
      },
      {
        name: 'materialLov',
        editor: () => canEdit,
      },
      {
        name: 'materialName',
      },
      {
        name: 'primaryUnitFlag',
        editor: () => canEdit && <Switch name="primaryUnitFlag" />,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
      {
        name: 'quantity',
        editor: () => canEdit,
      },
    ],
    [canEdit],
  );

  return <Table dataSet={ds} columns={columns} filter={record => !record?.get('deleteFlag')} />;
};
