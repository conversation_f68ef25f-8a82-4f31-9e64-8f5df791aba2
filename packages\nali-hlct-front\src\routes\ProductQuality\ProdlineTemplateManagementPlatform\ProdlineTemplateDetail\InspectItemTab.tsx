/**
 * @Description: 检验项目组维护明细界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:55:10
 * @LastEditTime: 2023-05-24 16:31:25
 * @LastEditors: <<EMAIL>>
 */

import React, { useState } from 'react';
import intl from 'utils/intl';
import { Table, Spin, Lov, TextField, Select, Attachment } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
// import notification from 'utils/notification';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
// import { getCurrentOrganizationId } from 'utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { Buttons } from 'choerodon-ui/pro/lib/table/Table';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';

// const modelPrompt = 'tarzan.qms.inspectGroupMaintenance';
// const tenantId = getCurrentOrganizationId();

const InspectItemTab = props => {
  const { path, canEdit, tableDS, loading } = props;

  // enum EditType {
  //   add = 'add',
  //   edit = 'edit',
  // }

  // 表格勾选数据
  const [tableSelectList, setTableSelectList] = useState([]);

  // 监听关系表格勾选数据
  const handleTableSelect = ({ dataSet }) => {
    setTableSelectList(dataSet.selected || []);
  };
  useDataSetEvent(tableDS, 'select', handleTableSelect);
  useDataSetEvent(tableDS, 'selectAll', handleTableSelect);
  useDataSetEvent(tableDS, 'unselect', handleTableSelect);
  useDataSetEvent(tableDS, 'unselectAll', handleTableSelect);

  // 行勾选删除
  const handleBatchDelete = () => {
    if ((tableDS.selected || []).length < 1) {
      return;
    }
    // if ((tableDS.selected || []).length === tableDS.toData().length) {
    //   notification.error({
    //     message: `不可全部删除，最少保留一条数据`,
    //   });
    //   return;
    // }
    tableDS.remove(tableDS.selected);

    setTableSelectList([]);
  };

  // 行新增或编辑
  const handleAddEdit = () => {
    tableDS.create({});
  };

  // 附件配置
  const attachmentProps: any = {
    name: 'deliveryTemplate',
    bucketName: 'qms',
    bucketDirectory: 'prodline_template_management_platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const buttons: Buttons[] = [
    <Popconfirm
      title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
      onConfirm={() => handleBatchDelete()}
      okText={intl.get('tarzan.common.button.confirm').d('确认')}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
    >
      <PermissionButton
        type="c7n-pro"
        icon="delete_black-o"
        disabled={!canEdit || tableSelectList.length < 1}
        funcType="flat"
        shape="circle"
        size="small"
        permissionList={[
          {
            code: `dist.button.delete`,
            type: 'button',
            meaning: '详情页-删除按钮',
          },
        ]}
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
    </Popconfirm>,
    <PermissionButton
      type="c7n-pro"
      icon="add"
      disabled={!canEdit}
      // onClick={() => handleAddEdit(EditType.add, false)}
      onClick={() => handleAddEdit()}
      funcType="flat"
      shape="circle"
      size="small"
      permissionList={[
        {
          code: `${path}.button.edit`,
          type: 'button',
          meaning: '详情页-编辑新建删除复制按钮',
        },
      ]}
    >
      {intl.get('hzero.common.button.add').d('新增')}
    </PermissionButton>,
  ];

  const columns: ColumnProps[] = [
    {
      name: 'elementNum',
      align: ColumnAlign.center,
      renderer: ({ record }) => {
        return <span>{record && (record.index + 1) * 10}</span>;
      },
    },
    {
      name: 'reviewDimension',
      editor: () => canEdit && <Select name="reviewDimension" required />,
    },
    {
      name: 'reviewItem',
      editor: () =>
        canEdit && <TextField name="reviewItem" maxLength={255} showLengthInfo required />,
    },
    {
      name: 'reviewContent',
      editor: () => canEdit && <TextField name="reviewContent" maxLength={255} showLengthInfo required />,
    },
    {
      name: 'deliveryName',
      editor: () => canEdit && <TextField name="deliveryName" maxLength={100} showLengthInfo required />,
    },
    {
      name: 'deliveryTemplate',
      editor: () => canEdit && <Attachment {...attachmentProps} />,
    },
    {
      name: 'departmentLov',
      editor: () => canEdit && <Lov name="departmentLov" required />,
    },
  ];

  return (
    <Spin spinning={loading}>
      <Table dataSet={tableDS} columns={columns} buttons={buttons} highLightRow />
    </Spin>
  );
};

export default InspectItemTab;
