/**
 * @Description: 不良代码组维护详情DS
 * @Author: <<EMAIL>>
 * @Date: 2022-07-12 10:39:28
 * @LastEditTime: 2023-05-11 14:56:09
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from "hzero-front/src/utils/notification";

const modelPrompt = 'tarzan.badCode.defectGroup.model.defectGroup';
const tenantId = getCurrentOrganizationId();

const formDS = () => ({
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows.mtNcGroup',
  selection: false,
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-nc-group/detail/ui`,
        method: 'get',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            })
          }
          return {
            ...rows?.ncGroup,
            ncCodeList: rows?.ncCodeList,
            mtNcValidOperationList: rows?.mtNcValidOperationList,
          };
        },
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.method.domain.entity.MtNcCode';
      return {
        data: { ncGroupId: record.get('ncGroupId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_METHOD}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteId`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('ncGroupId');
        },
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'site.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      bind: 'site.siteName',
    },
    {
      name: 'ncGroupCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncGroupCode`).d('不良代码组编码'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('ncGroupId');
        },
      },
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.ncGroupDesc`).d('不良代码组描述'),
      required: true,
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'validAtAllOperations',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.validAtAllOperations`).d('对所有工艺有效'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
  ],
});

const ncCodeDS = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'ncGroupNcRelId',
      type: FieldType.number,
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      min: 0,
      step: 1,
      required: true,
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: 'always',
      required: true,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          const _excludeNcCodeIds = [];
          dataSet.forEach(_record => {
            if (_record.get('ncCodeId')) {
              _excludeNcCodeIds.push(_record.get('ncCodeId'));
            }
          })
          return {
            tenantId,
            siteId: dataSet.parent.current.get('siteId'),
            excludeNcCodeIds: _excludeNcCodeIds,
          };
        },
      },
    },
    {
      name: 'ncCodeId',
      type: FieldType.number,
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      bind: 'ncCodeLov.ncCode',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncDesc`).d('不良代码描述'),
      bind: 'ncCodeLov.description',
    },
    {
      name: 'scrapDetail',
      type: FieldType.string,
      bind: 'ncCodeLov.scrapDetail',
      label: intl.get(`${modelPrompt}.scrapDetail`).d('不良报废明细'),
    },
    {
      name: 'ncTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncType`).d('不良代码类型'),
      bind: 'ncCodeLov.ncTypeDesc',
    },
  ],
});

const technologyDS = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'ncValidOperationId',
      type: FieldType.number,
    },
    {
      name: 'operation',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺编码'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: 'always',
      required: true,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          const _excludeOperationIds = [];
          dataSet.forEach(_record => {
            if (_record.get('operationId')) {
              _excludeOperationIds.push(_record.get('operationId'));
            }
          })
          return {
            tenantId,
            excludeOperationIds: _excludeOperationIds,
          }
        },
      },
    },
    {
      name: 'operationId',
      type: FieldType.number,
      bind: 'operation.operationId',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      bind: 'operation.operationName',
    },
    {
      name: 'operationDesc',
      type: FieldType.string,
      bind: 'operation.operationDesc',
    },
    {
      name: 'dispositionGroupObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dispositionGroup`).d('处置组'),
      lovCode: 'MT.DISPOSITION_GROUP',
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteId: dataSet.parent.current.get('siteId'),
          };
        },
      },
    },
    {
      name: 'dispositionGroup',
      type: FieldType.string,
      bind: 'dispositionGroupObject.dispositionGroup',
    },
    {
      name: 'dispositionGroupId',
      type: FieldType.number,
      bind: 'dispositionGroupObject.dispositionGroupId',
    },
  ],
});

export { formDS, ncCodeDS, technologyDS };
