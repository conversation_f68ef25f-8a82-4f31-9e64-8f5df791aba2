/*
 * @Description: 市场活动单-详情界面
 * @Author: <<EMAIL>>
 * @Date: 2023-09-18 18:04:36
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-04 13:36:25
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  NumberField,
  TextField,
  Select,
  Attachment,
  DateTimePicker,
  TextArea,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { getCurrentUserId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import { detailDS } from '../stores/DetailDS';
import { SaveMarketActivity, SaveProgressInfo, ReviewMarketActivity, QueryUserEmployeeInfo } from '../services';

const { Panel } = Collapse;
const userId = getCurrentUserId();
const modelPrompt = 'tarzan.qms.marketActivity.marketActivityDoc';

const MarketActivityDetail = props => {
  const {
    history,
    match: { params, path },
  } = props;
  const kid = params.id;

  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);

  const [canEdit, setCanEdit] = useState<boolean>(false);
  const [marketActivityStatus, setStatus] = useState<string>('NEW');
  const [createdBy, setCreatedBy] = useState<number | null>(null);
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const { run: saveMarketActivity, loading: saveLoading } = useRequest(SaveMarketActivity(), {
    manual: true,
    needPromise: true,
  });
  const { run: saveProgressInfo, loading: saveProgressLoading } = useRequest(SaveProgressInfo(), {
    manual: true,
    needPromise: true,
  });
  const { run: reviewMarketActivity, loading: reviewLoading } = useRequest(ReviewMarketActivity(), {
    manual: true,
  });
  const { run: queryUserEmployeeInfo } = useRequest(QueryUserEmployeeInfo(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if (kid === 'create') {
      // 新建时
      setCanEdit(true);
      setStatus('NEW');
      handleQueryEmployee();
      return;
    }
    // 编辑时
    handleQueryDetail();
  }, [kid]);

  const handleQueryEmployee = () => {
    queryUserEmployeeInfo({
      queryParams: { userId },
    }).then(res => {
      if (res?.success) {
        detailDs.current?.set('createdBy', userId);
        detailDs.current?.set('createdPerson', res?.rows?.name);
      }
    });
  };

  const handleQueryDetail = (marketActivityId = kid) => {
    detailDs.setQueryParameter('marketActivityId', marketActivityId);
    detailDs.query().then(res => {
      const { marketActivityStatus, createdBy } = res?.rows || {};
      setStatus(marketActivityStatus);
      setCreatedBy(createdBy);
    });
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(() => {
    if (kid === 'create') {
      history.push('/hwms/market-quality/market-Activity-doc/list');
    } else {
      setCanEdit(false);
      handleQueryDetail();
    }
  }, []);

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const data = detailDs!.current!.toData();
    const _run = marketActivityStatus === 'NEW' ? saveMarketActivity : saveProgressInfo;
    const res = await _run({ params: data });
    if (res && res.success) {
      notification.success({});
      setCanEdit(false);
      handleQueryDetail();
      return true;
    }
    return false;
  };

  // 点击审批按钮的回调
  const handleReview = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    reviewMarketActivity({
      tempUrl: ReviewMarketActivity(kid)?.url || '',
      onSuccess: () => {
        notification.success({});
        handleQueryDetail();
      },
    });
  };

  const enclosureProps: any = {
    bucketName: 'qms',
    bucketDirectory: 'market-Activity-doc',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || saveProgressLoading || reviewLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('市场活动单')}
          backPath={pubFlag ? '' : '/hwms/market-quality/market-activity-doc/list'}

        >
          {canEdit && ['NEW', 'EXECUTING'].includes(marketActivityStatus) && !pubFlag && (
            <>
              <Button
                color={ButtonColor.primary}
                loading={saveLoading}
                icon="save"
                onClick={handleSave}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && marketActivityStatus === 'NEW' && !pubFlag && (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              disabled={createdBy !== userId}
              permissionList={[
                {
                  code: `${modelPrompt}.dist.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
          {!canEdit && marketActivityStatus === 'EXECUTING' && !pubFlag && (
            <PermissionButton
              type="c7n-pro"
              icon="send-o"
              onClick={handleEdit}
              loading={saveProgressLoading}
              disabled={marketActivityStatus !== 'EXECUTING'}
              permissionList={[
                {
                  code: `${modelPrompt}.dist.button.degreeEdit`,
                  type: 'button',
                  meaning: '详情页-进度编辑按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.degreeEdit`).d('进度编辑')}
            </PermissionButton>
          )}
          {
            !pubFlag && <PermissionButton
              type="c7n-pro"
              icon="check"
              disabled={marketActivityStatus !== 'NEW' || createdBy !== userId}
              loading={reviewLoading}
              onClick={handleReview}
              permissionList={[
                {
                  code: `${modelPrompt}.dist.button.review`,
                  type: 'button',
                  meaning: '详情页-提交审批按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.review`).d('提交审批')}
            </PermissionButton>
          }
          <ApprovalInfoDrawer objectTypeList={['QIS_SCHD_LWS']} objectId={kid} />
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basic', 'activityInfo', 'costDetail']}>
            <Panel key="basic" header={intl.get(`${modelPrompt}.title.basic`).d('基本信息')}>
              <Form dataSet={detailDs} columns={3} labelWidth={112}>
                <TextField name="marketActivityCode" />
                <Select name="marketActivityStatus" />
                <TextField name="marketEstimateCode" />
                <Lov name="siteLov" />
                <Lov name="problemLov" disabled={!canEdit || marketActivityStatus !== 'NEW'} />
                <TextField name="problemTitle" />
                <TextField name="createdByName" />
                <DateTimePicker name="creationDate" />
                <DateTimePicker name="startTime" />
                <NumberField
                  name="completionDegree"
                  disabled={!canEdit || marketActivityStatus !== 'EXECUTING'}
                  suffix="%"
                />
                <DateTimePicker name="endTime" />
              </Form>
            </Panel>
            <Panel
              key="activityInfo"
              header={intl.get(`${modelPrompt}.title.activityInfo`).d('填案信息')}
            >
              <Form
                dataSet={detailDs}
                columns={3}
                disabled={!canEdit || marketActivityStatus !== 'NEW'}
                labelWidth={112}
              >
                <Lov name="applyDepartmentLov" />
                <TextField name="faultPhenomenon" />
                <TextField name="occurrence" />
                <Lov name="materialLov" />
                <TextField name="ypItemName" />
                <Lov name="customerLov" />
                <TextField name="itemCode" />
                <TextField name="itemName" />
                <Lov name="responsibleDepartmentLov" />
                <Select name="vehicleModel" searchable searchFieldInPopup />
                <TextField name="batteryPackModel" />
                <Select name="severityLevel" />
                <NumberField name="objectQty" />
                <Attachment name="objectRange" {...enclosureProps} />
                <Attachment name="enclosure" {...enclosureProps} />
                <TextArea name="reason" colSpan={3} autoSize={{ minRows: 2, maxRows: 8 }} />
                <TextArea
                  name="activityOverview"
                  colSpan={3}
                  autoSize={{ minRows: 2, maxRows: 8 }}
                />
              </Form>
            </Panel>
            <Panel
              key="costDetail"
              header={intl.get(`${modelPrompt}.title.costDetail`).d('费用测算明细')}
            >
              <Form
                dataSet={detailDs}
                columns={3}
                disabled={!canEdit || marketActivityStatus !== 'NEW'}
                labelWidth={112}
              >
                <NumberField name="materialCost" />
                <NumberField name="manageCost" />
                <NumberField name="laborCost" />
                <NumberField name="singleUnitCost" />
                <NumberField name="measurementSumCost" />
              </Form>
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [
    modelPrompt,
    'tarzan.common',
  ],
})(MarketActivityDetail);
