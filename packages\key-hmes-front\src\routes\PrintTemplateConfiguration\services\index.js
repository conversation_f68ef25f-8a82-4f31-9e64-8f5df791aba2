/*
 * @Description: 打印模板配置界面调用后端接口
 * @Author: YinWQ
 * @Date: 2023-07-18 11:24:55
 * @LastEditors: YinWQ
 * @LastEditTime: 2023-07-18 15:24:12
 */
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const API = `${BASIC.TARZAN_COMMON}/v1/${getCurrentOrganizationId()}`;
// const API = `/focus-tznc-17823/v1/${getCurrentOrganizationId()}`;
/**
 * @Description: 查询详情界面信息
 * @param[in] params {printFunctionId: 'string'}
 * @return
 * @Author: YinWQ
 * @Date: 2023-07-18 14:36:55
 */
export async function detailQuery(params) {
  return request(`${API}/mt-print-functions/detail/ui`, {
    method: 'GET',
    query: params,
  });
}

/**
 * @Description: 保存打印模板
 * @param[in] params
 * @return
 * @Author: YinWQ
 * @Date: 2023-07-18 14:38:41
 */
export async function detailSave(params) {
  return request(`${API}/mt-print-functions/save/ui`, {
    method: 'POST',
    body: params,
  });
}

/**
 * @Description: h0可选模板列表
 * @param[in] params
 * @return
 * @Author: YinWQ
 * @Date: 2023-07-18 14:40:14
 */
export async function queryTemplateList(params) {
  return request(`${API}/mt-print-functions/selectable/h0/template/list/ui`, {
    method: 'GET',
    query: params,
  });
}
