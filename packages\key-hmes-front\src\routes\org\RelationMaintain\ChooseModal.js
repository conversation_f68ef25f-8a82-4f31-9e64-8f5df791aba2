/**
 * ChooseModal - 新增类型选择
 * @date: 2021-1-19
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import React from 'react';
import { connect } from 'dva';
import { Modal } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';

import styles from './index.module.less';

import iconAreaType from '@/assets/icon_area_type.svg';
import iconAreaTypeHover from '@/assets/icon_area_type_hover.svg';
import iconLineType from '@/assets/icon_line_type.svg';
import iconLineTypeHover from '@/assets/icon_line_type_hover.svg';

const modelPrompt = 'tarzan.model.org.relation';

function ChooseModal(props) {
  const { callback, dispatch, chooseModalShow } = props;

  const chooseModalSwitch = () => {
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        chooseModalShow: false,
      },
    });
  };

  const chooseType = type => {
    if (!type) {
      return;
    }
    callback(type);
    chooseModalSwitch();
  };

  return (
    <Modal
      title={intl.get(`${modelPrompt}.chooseSubordinateType`).d('选择下级类型')}
      visible={chooseModalShow}
      className={styles['choose-type-modal']}
      footer={null}
      onCancel={chooseModalSwitch}
    >
      <div className={styles['choose-container']}>
        <div
          className={styles['choose-item']}
          onClick={() => {
            chooseType('SITE');
          }}
        >
          <img className={styles['choose-type-modal-img']} src={iconAreaType} alt="" />
          <img className={styles['choose-type-modal-img-hover']} src={iconAreaTypeHover} alt="" />
          <div className={styles['choose-type-modal-text']}>
            {intl.get(`${modelPrompt}.area`).d('区域')}
          </div>
        </div>
        <div
          className={styles['choose-item']}
          onClick={() => {
            chooseType('AREA');
          }}
        >
          <img className={styles['choose-type-modal-img']} src={iconLineType} alt="" />
          <img className={styles['choose-type-modal-img-hover']} src={iconLineTypeHover} alt="" />
          <div className={styles['choose-type-modal-text']}>
            {intl.get(`${modelPrompt}.prodLine`).d('生产线')}
          </div>
        </div>
      </div>
    </Modal>
  );
}

export default formatterCollections({
  code: ['tarzan.model.org.relation', 'tarzan.common'],
})(
  connect(({ relationMaintain }) => {
    return relationMaintain;
  })(ChooseModal),
);
