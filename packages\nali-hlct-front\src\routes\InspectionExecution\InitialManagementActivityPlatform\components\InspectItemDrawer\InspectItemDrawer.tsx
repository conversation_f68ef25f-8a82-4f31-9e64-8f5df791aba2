/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-02-07 13:52:00
 * @LastEditTime: 2023-05-18 16:41:45
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useMemo } from 'react';
import notification from 'utils/notification';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { DataSet, Table, Button, Modal } from 'choerodon-ui/pro';
import { Badge, Tag } from 'choerodon-ui';
import { useDataSetEvent } from 'utils/hooks';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import InspectItemBatchEditDrawer from './InspectItemBatchEditDrawer';
import { DetailTableDS, NumberDS, formulaListTableDS } from '../stores';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.initialManagementActivity';

const _ignoreKeys = [
  'creationDate',
  'createdBy',
  'lastUpdateDate',
  'lastUpdatedBy',
  'objectVersionNumber',
  '_token',
  'inspectGroupItemId',
  'sequence',
  'inspectItemLov',
  'inspectItemId',
  'inspectItemCode',
  'inspectItemDesc',
  'enclosure',
  'inspectFrequencyDesc',
];

const InspectItemDrawer = ({ tableDs, canEdit, customizeTable, customizeForm }) => {
  const [selectedRecords, setSelectedRecords] = useState<Array<any>>([]);

  const editTableFormDS = useMemo(() => new DataSet(DetailTableDS({})), []);
  const warnNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  const trueNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  const falseNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  // 公式参数列表
  const formulaListTableDs = useMemo(() => new DataSet(formulaListTableDS()), []);

  useDataSetEvent(tableDs, 'batchSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  useDataSetEvent(tableDs, 'batchUnSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  const getDataValueShow = (record, name) => {
    const _dataType = record?.get('dataType');
    const _valueData = record?.get(name) || [];
    const _dataShow = _valueData.length > 0 ? _valueData[0].dataValue : '';
    return ['VALUE', 'VALUE_LIST'].includes(_dataType)
      ? _valueData.map(item => <Tag>{item.dataValue}</Tag>)
      : _dataShow;
  };

  // 确定保存检验项目
  const handleInspectItemDrawerSubmit = async () => {
    const record = editTableFormDS.current;

    if (!record) {
      return false;
    }

    // 检验项目字段校验
    let validValueTypeFlag = true;
    // 数值类型的符合值、不符合值必输校验
    const dataType = record.get('dataType');

    const validateResult = await editTableFormDS.validate();
    if (!validateResult) {
      return false;
    }

    if (['CALCULATE_FORMULA', 'VALUE'].includes(dataType)) {
      if (trueNumberDS.length < 1 && falseNumberDS.length < 1) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.message.inputTrueOrFalseValue`)
            .d('请输入符合值或不符合值'),
        });
        validValueTypeFlag = false;
      } else {
        const _targeDS = trueNumberDS.length > 0 ? trueNumberDS : falseNumberDS;
        const _newData = _targeDS.records.filter(
          item =>
            item.get('singleValued') ||
            (item.get('multiValued') &&
              (item.get('multiValued')?.start || item.get('multiValued')?.end)),
        );
        if (_newData.length < 1) {
          notification.error({
            message:
              trueNumberDS.length > 0
                ? intl.get(`${modelPrompt}.message.inputTrueValue`).d('请输入符合值')
                : intl.get(`${modelPrompt}.message.inputFalseValue`).d('请输入不符合值'),
          });
          validValueTypeFlag = false;
        }
      }
    }
    if (!validValueTypeFlag) {
      return false;
    }

    let _trueValueList: any = [];
    let _falseValueList: any = [];
    let _warningValueList: any = [];
    // 如果数据类型为数值类型，需要做赋值处理
    if (['CALCULATE_FORMULA', 'VALUE'].includes(dataType)) {
      const _valueHandle = targetDS => {
        return targetDS
          .toData()
          .filter(
            (item: any) =>
              item.singleValued ||
              (item.multiValued && (item.multiValued?.start || item.multiValued?.end)),
          )
          .map((item: any) => {
            if (
              item.valueType !== 'single' &&
              (!item.multiValued?.start || !item.multiValued?.end)
            ) {
              if (!item.multiValued?.start) {
                item.multiValued.start = '-∞';
                item.leftValue = '-∞';
              }
              if (!item.multiValued?.end) {
                item.multiValued.end = '+∞';
                item.rightValue = '+∞';
              }
              item.dataValue = `${item.leftChar}${item.multiValued.start},${item.multiValued.end}${item.rightChar}`;
              const _leftCharTip = item.leftChar === '(' ? '<' : '≤';
              const _rightCharTip = item.rightChar === ')' ? '<' : '≤';
              item.valueShow = `${item.multiValued.start}${_leftCharTip}X${_rightCharTip}${item.multiValued.end}`;
            }
            return item;
          });
      };

      _trueValueList = _valueHandle(trueNumberDS);
      _falseValueList = _valueHandle(falseNumberDS);
      _warningValueList = _valueHandle(warnNumberDS);
    } else if (dataType === 'VALUE_LIST') {
      _trueValueList = (record.get('trueValue') || []).map(item => ({
        dataValue: item,
      }));
      _falseValueList = (record.get('falseValue') || []).map(item => ({
        dataValue: item,
      }));
    } else {
      _trueValueList = record.get('trueValue')
        ? [
          {
            dataValue: record.get('trueValue'),
          },
        ]
        : [];
      _falseValueList = record.get('falseValue')
        ? [
          {
            dataValue: record.get('falseValue'),
          },
        ]
        : [];
    }

    if (dataType === 'CALCULATE_FORMULA') {
      record.set(
        'formula',
        JSON.stringify({
          formulaSourceId: record.get('formulaSourceId'),
          formulaMode: record.get('formulaMode'),
          formulaDisplayPosition: record.get('formulaDisplayPosition'),
          formulaId: record.get('formulaId'),
          formulaCode: record.get('formulaCode'),
          formulaName: record.get('formulaName'),
          dimension: record.get('dimension'),
          formulaList: (formulaListTableDs.toData() || []).map((formulaListItem: any) => {
            if (formulaListItem) {
              return {
                fieldCode: formulaListItem.fieldCode,
                fieldName: formulaListItem.fieldName,
                inspectItemId: formulaListItem.inspectItemId,
                inspectItemDesc: formulaListItem.inspectItemDesc,
                isRequired: formulaListItem.isRequired,
              };
            }
            return {};
          }),
        }),
      );
      record.set('formulaList', formulaListTableDs.toData());
    } else {
      record.set('formula', '');
      record.set('formulaList', []);
    }

    record.set('trueValueList', _trueValueList);
    record.set('falseValueList', _falseValueList);
    record.set('warningValueList', _warningValueList);

    if (_falseValueList.length > 0) {
      record.set('warningValueList', []);
    } else if (_warningValueList.length > 0) {
      record.set('warningValueList', _warningValueList);
    } else if (record.get('earlyWarningValue_select')) {
      record.set('warningValueList', []);
    }

    // 批量编辑处理数据

    // ds fields 项
    const _fields = editTableFormDS.fields || [];
    // 忽略更新的项
    const _newIgnoreKeys = _ignoreKeys.concat([
      'selectList',
      'inspectItemLov',
      'inspectItemId',
      'inspectItemCode',
      'inspectItemDesc',
      'enclosure',
      'inspectFrequencyDesc',
    ]);
    // 特殊处理的项
    const _listField = ['trueValueList', 'falseValueList', 'warningValueList'];
    // 与dataType相关的需要处理的项
    const _aboutDataTypeField = [
      'uomLov',
      'decimalNumber',
      'processMode',
      'valueLists',
      'trueValue',
      'falseValue',
      'earlyWarningValue',
      'trueValueList',
      'falseValueList',
      'warningValueList',
      'formulaLov',
      'formulaList',
      'dimension',
    ];

    const { selectList } = record.toData();
    const selectListKeys = Object.keys(selectList || {});

    // 遍历选中的需要批量更新的项
    selectedRecords.forEach(item => {
      // 在 ds fields中遍历
      _fields.forEach(field => {
        // 如果不在忽略列表中
        if (field && field.name && !_newIgnoreKeys.includes(field.name)) {
          // 先判断是否dataType有值
          if (record?.get('dataType')) {
            // 如果在dataType关联字段中
            if (_aboutDataTypeField.includes(field.name)) {
              // 直接更新该字段
              item.set(field.name, record.get(field.name));
              // 不在dataType关联字段中, 有值的话更新
            } else if (record.get(field.name)) {
              item.set(field.name, record.get(field.name));
            }
            // 如果dataType无值, 该字段有值, 又是不需要特殊处理的项 直接更新该字段
          } else if (record.get(field.name) && !_listField.includes(field.name)) {
            item.set(field.name, record.get(field.name));
          }

          selectListKeys.forEach(key => {
            if (selectList[key]) {
              item.set(key, null);
            }
          });
        }
      });
    });
  };

  // 行新增或编辑
  const openEditDrawer = () => {
    editTableFormDS.getField('sequence')?.set('required', false);
    editTableFormDS.getField('inspectItemLov')?.set('required', false);
    editTableFormDS.getField('dataType')?.set('required', false);
    editTableFormDS.getField('enterMethod')?.set('required', false);
    editTableFormDS.getField('dataQty')?.set('required', false);
    editTableFormDS.getField('samplingMethodLov')?.set('required', false);
    editTableFormDS.create({
      inspectItemLov: {},
      requiredFlag: 'Y',
      destructiveExperimentFlag: 'N',
      outsourceFlag: 'N',
    });

    const inspectItemDrawerProps = {
      canEdit,
      _ignoreKeys,
      tenantId,
      customizeForm,
      warnNumberDS,
      trueNumberDS,
      falseNumberDS,
      editTableFormDS,
      tableDS: editTableFormDS,
      formulaListTableDs,
    };

    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title: (
        <>
          <span>{intl.get(`${modelPrompt}.itemBatchEdit`).d('批量编辑检验项目')}</span>
          <span style={{ float: 'right', marginRight: 50 }}>
            <span>{(selectedRecords || []).length}</span>
            {intl.get(`${modelPrompt}.title.itemResetSelect`).d('项已选中')}
          </span>
        </>
      ),
      drawer: true,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: '1080px',
      },
      className: 'hmes-style-modal',
      children: <InspectItemBatchEditDrawer {...inspectItemDrawerProps} />,
      onOk: () => {
        return handleInspectItemDrawerSubmit();
      },
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'inspectBusinessTypeDesc',
      title: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      width: 140,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectionDimension',
      title: intl.get(`${modelPrompt}.inspectionDimension`).d('检验维度'),
      width: 140,
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      width: 60,
      align: ColumnAlign.center,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemLov',
      width: 200,
      // renderer: ({ record }) => (
      //   <a onClick={() => handleAdd(record)}>{record?.get('inspectItemCode')}</a>
      // ),
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemDesc',
      width: 200,
    },
    {
      name: 'inspectItemType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectGroupDesc',
      width: 120,
    },
    {
      name: 'taskCategory',
      width: 120,
    },
    {
      name: 'inspectBasis',
    },
    {
      name: 'inspectMethod',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'technicalRequirement',
    },
    {
      name: 'inspectTool',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'qualityCharacteristic',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'dataType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'trueValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'trueValueList'),
    },
    {
      name: 'falseValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'falseValueList'),
    },
    {
      name: 'earlyWarningValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'warningValueList'),
    },
    {
      name: 'uomLov',
      align: ColumnAlign.center,
    },
    {
      name: 'processMode',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'enterMethod',
      align: ColumnAlign.center,
    },
    {
      name: 'decimalNumber',
    },
    {
      name: 'requiredFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'dataQty',
    },
    {
      name: 'samplingMethodLov',
      align: ColumnAlign.center,
    },
    {
      name: 'sameGroupIdentification',
    },
    {
      name: 'destructiveExperimentFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'outsourceFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'actionItem',
    },
    {
      name: 'employeePosition',
      width: 120,
    },
    {
      name: 'inspectFrequency',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
        if (inspectFrequencyShow) {
          inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
          inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
          return inspectFrequencyShow;
        }
        return value;
      },
    },
    {
      name: 'ncCodeGroupLov',
      align: ColumnAlign.center,
    },
    {
      name: 'enclosure',
    },
  ];

  const tableButton = (
    <Button
      disabled={!selectedRecords || selectedRecords.length === 0}
      color={ButtonColor.primary}
      onClick={openEditDrawer}
    >
      {intl.get('hzero.common.button.batchEdit').d('批量编辑')}
    </Button>
  );

  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_SCHEME_BASIC.ITEM_DETAIL`,
    },
    <Table
      filter={record => {
        return record.get('dataType') !== 'CALCULATE_FORMULA';
      }}
      dataSet={tableDs}
      columns={columns}
      highLightRow
      buttons={canEdit ? [tableButton] : []}
    />,
  );
};
export default InspectItemDrawer;
