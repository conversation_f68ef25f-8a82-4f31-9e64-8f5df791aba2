import React, { useMemo } from 'react';
import DashboardCard from '../DashboardCard.jsx';
import ScrollBoard from '../ScrollBoard.jsx';
import styles from '../../index.module.less';

const NgReport = ({data}) => {
  const tableData = [];
  if (data.length) {
    data.forEach((val) => {
      const {
        inspectDate,
        supplierCode,
        bomCode,
        model,
        inspectSumQty,
      } = val;
      tableData.push([
        `<Tooltip title='${inspectDate}'><span>${inspectDate}</span></Tooltip>`,
        `<Tooltip title='${supplierCode}'><span>${supplierCode}</span></Tooltip>`,
        `<Tooltip title='${bomCode}'><span>${bomCode}</span></Tooltip>`,
        `<Tooltip title='${model}'><span>${model}</span></Tooltip>`,
        `<Tooltip title='${inspectSumQty}'><span>${inspectSumQty}</span></Tooltip>`,
      ]);
    });
  }
  const config = useMemo(
    () => ({
      header: ['检验日期','供应商代码', 'BOM', '规格', '数量'],
      data: tableData,
      rowNum: 7,
      align: ['center', 'center'],
      oddRowBGC: 'rgba(22,66,127,0.3)',
      headerBGC: 'rgb(3, 157, 206,0.3)',
      evenRowBGC: 'rgba(3,28,60, 0.3)',
      headerHeight: 40,
      columnWidth: [150, 120, 90, 120, 90],
    }),
    [tableData],
  );
  return (
    <DashboardCard style={{ height: '100%' }} >
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-scroll-board-title']}>
          来料检验合格信息
        </div>
        <div className={styles['my-scroll-board-table']}>
          <ScrollBoard config={config} style={{ width: '100%', height: '100%',marginLeft: '0px 10px' }} />
        </div>
      </div>
    </DashboardCard>
  );
};

export default NgReport;
