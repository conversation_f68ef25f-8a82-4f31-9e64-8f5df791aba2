/**
 * @Description: 作业指导书管理-列表页-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-07-24 09:27:19
 * @LastEditTime: 2023-07-26 10:22:48
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'hmes.workingInstructionMaintenance';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'sopId',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'sopCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sopCode`).d('工艺文件编码'),
    },
    {
      name: 'sopName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sopName`).d('工艺文件名称'),
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationCode`).d('工艺编码'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      type: FieldType.number,
      bind: 'operationLov.operationId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'HME.SITE_MATERIAL',
      lovPara: {
        tenantId,
      },
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId') || null,
          };
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'categoryLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料类别'),
      lovCode: 'MT.METHOD.MATERIAL_CATEGORY_SITES',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'categoryId',
      type: FieldType.number,
      bind: 'categoryLov.categoryId',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'sopCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sopCode`).d('工艺文件编码'),
    },
    {
      name: 'sopName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sopName`).d('工艺文件名称'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'startDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startDate`).d('生效时间'),
    },
    {
      name: 'endDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endDate`).d('失效时间'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-sop-header`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
