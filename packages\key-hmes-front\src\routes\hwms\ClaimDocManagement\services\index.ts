/*
 * @Description: 索赔管理-services
 * @Author: <<EMAIL>>
 * @Date: 2023-09-21 17:00:00
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-25 09:46:02
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存索赔单
 * @function SaveClaimDoc
 * @returns {object} fetch Promise
 */
export function SaveClaimDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-claim-doc/save/ui`,
    method: 'POST',
  };
}

/**
 * 根据materialId获取物料相关信息
 * @function QueryMaterialInfo
 * @returns {object} fetch Promise
 */
export function QueryMaterialInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-qa-feedbacks/mt-material-ca`,
    method: 'POST',
  };
}
