import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Spin } from 'choerodon-ui';
import { DataSet, Table, Modal } from 'choerodon-ui/pro';
import Upload from 'components/Upload/UploadButton'; // ! 这个组件已不再维护了
import intl from 'utils/intl';
import { HALM_PFM } from 'alm/utils/config';
import { Bind } from 'lodash-decorators';
import { routerRedux } from 'dva/router';
import { getAttachmentUrl } from 'utils/utils';
import { dateTimeRender, operatorRender } from 'utils/renderer';
import request from 'utils/request';
import { isString } from 'lodash';
import notification from 'utils/notification';
import { fileDS, logDS } from './fileUploadDS';

import closeImg from '@/components/UploadPanel/image/close.png';

const prefix = 'alm.component.fileUpload.view';
class FileModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };
    this.fileDS = new DataSet({
      ...fileDS(this.handleGetAttributeFields()),
      data: props.fileSource,
    });
    this.logDS = new DataSet({ ...logDS(), data: props.logSource });
  }

  componentWillMount() {
    this.setState({
      loading: true,
    });
    this.props.onSearch();
  }

  /** 获取图片 */
  @Bind()
  onFetchName(val, record) {
    if (record.fileType === 'image/jpeg' || record.fileType === 'image/png' || record.mediaType === 'pic') {
      return (
        <a onClick={() => this.onShowImg(record)}>{`${val}(已下载${record.downloadTimes}次)`}</a>
      );
    } else if (record.mediaType === 'video') {
      return <a onClick={() => this.onShowVideo(record)}>{`${val}(已下载${record.downloadTimes}次)`}</a>;
    } else {
      return <span>{`${val}(已下载${record.downloadTimes}次)`}</span>;
    }
  }

  // 打开视频预览
  @Bind
  onShowVideo(record) {
    const vProps = {
      width: 800,
      height: 450,
      controls: true,
      autoPlay: true,
      loop: true,
      src: record.fileUrl,
    };
    const m = Modal.open({
      header: null,
      maskClosable: true,
      destroyOnClose: true,
      closable: true,
      contentStyle: {
        padding: 0,
        width: 800,
        height: 450,
      },
      bodyStyle: {
        padding: 0,
        width: 800,
        height: 450,
        overflow: 'hidden',
      },
      children: (
        <div className="video-wrapper">
          <video {...vProps}>
            <track default kind="captions" />
            您的浏览器不支持Video标签。
          </video>
          <img
            src={closeImg}
            alt=""
            onClick={() => {
              m.close();
            }}
          />
        </div>
      ),
      footer: null,
    });
  }

  /** 打开图片显示 */
  @Bind()
  onShowImg(record) {
    const { bucketName, fileUrl } = record;
    const imgUrl = getAttachmentUrl(fileUrl, bucketName);
    Modal.open({
      key: Modal.key(),
      footer: false,
      closable: true,
      resizable: true,
      children: <img src={imgUrl} alt="图片" style={{ width: '100%' }} />,
    });
  }

  // 视频文件获取时长
  @Bind()
  getVideoInfo(file) {
    return new Promise(resolve => {
      let mediaStr = '';
      if (['video/mp4', 'video/webm'].includes(file.type)) {
        const video = document.createElement('video');
        const url = URL.createObjectURL(file);
        video.src = url;
        video.addEventListener('loadedmetadata', function () {
          mediaStr += `&mediaType=video&fileDuration=${encodeURI(
            `${Math.round(video.duration)} s`
          )}`;
          // 可以在这里销毁 URL 对象
          URL.revokeObjectURL(url);
          resolve(mediaStr);
        });
      } else {
        resolve(mediaStr);
      }
    });
  }

  // 客制化上传方法获取视频文件的时长并且保存
  @Bind()
  handleAction({ file, onProgress, onSuccess, onError }) {
    this.getVideoInfo(file).then(mediaStr => {
      const controller = new AbortController();
      const { signal } = controller;
      const {
        employeeId,
        attribute,
        moduleName,
        tenantId,
        showContentFlag,
        uploadModule,
        otherProps,
      } = this.props;
      let { bucketName, attachmentUuid, moduleId = [] } = this.props;
      if (showContentFlag) {
        const { uploadModuleName, uploadModuleId } = uploadModule;
        bucketName = uploadModuleName;
        attachmentUuid = `halm-${uploadModuleName}-${uploadModuleId}`;
        moduleId = uploadModuleId;
      }
      const base = typeof attribute === 'undefined' ? null : JSON.stringify(attribute.data);
      const attr = encodeURI(base);
      const str = `collectionCode=${otherProps.collectionCode}&uploaderId=${employeeId}&operatorId=${employeeId}&bucketName=${bucketName}&status=I&moduleName=${moduleName}&moduleId=${moduleId}&attachmentUuid=${attachmentUuid}&attribute=${attr}`;
      const action = `${HALM_PFM}/v2/${tenantId}/file-upload-logs/create-upload?${str}${mediaStr}`;
      const data = new FormData();
      data.append('file', file, file.name);
      request(
        action,
        {
          processData: false, // 不会将 data 参数序列化字符串
          method: 'POST',
          type: 'FORM',
          body: data,
          responseType: 'text',
          signal, // 用于控制 取消 请求
          onProgress: onProgress ? e => onProgress(e, file) : null,
        },
        {
          beforeCatch: err => {
            if (err.name === 'AbortError') {
              // 隐藏掉 取消上传的 fetch 报错
            } else {
              throw err;
            }
          },
        }
      ).then(res => {
        if (isString(res)) {
          // 成功
          onSuccess(res);
        } else if (res) {
          if (res.failed) {
            onError(res);
            notification.autoNotification(res?.type, res?.message, res?.requestMessage);
          }
        }
      });
      return {
        abort: () => {
          controller.abort();
        },
      };
    });
  }

  /** 控制上传的效果 */
  @Bind()
  onUploadShow() {
    const {
      moduleName,
      showDeleteFlag,
      uploadModule,
      showContentFlag,
      tenantId,
      employeeId = [],
      onSearch,
      attribute,
      otherProps,
    } = this.props;
    let { bucketName, attachmentUuid, moduleId = [] } = this.props;
    if (showContentFlag) {
      const { uploadModuleName, uploadModuleId } = uploadModule;
      bucketName = uploadModuleName;
      attachmentUuid = `halm-${uploadModuleName}-${uploadModuleId}`;
      moduleId = uploadModuleId;
    }
    if (showDeleteFlag === false) {
      return null;
    } else {
      const base = typeof attribute === 'undefined' ? null : JSON.stringify(attribute.data);
      const attr = encodeURI(base);
      const str = `collectionCode=${otherProps.collectionCode}&uploaderId=${employeeId}&operatorId=${employeeId}&bucketName=${bucketName}&status=I&moduleName=${moduleName}&moduleId=${moduleId}&attachmentUuid=${attachmentUuid}&attribute=${attr}`;
      const tmp = otherProps;
      // 是否客制化请求
      if (otherProps?.customRequestFlag) {
        tmp.customRequest = this.handleAction;
      }
      return (
        <Upload
          action={`${HALM_PFM}/v2/${tenantId}/file-upload-logs/create-upload?${str}`}
          showUploadList={false}
          onUploadSuccess={onSearch}
          {...tmp}
        >
          <Button icon="upload">{intl.get('hzero.common.button.upload').d('上传')}</Button>
        </Upload>
      );
    }
  }

  @Bind()
  handleSetLoading() {
    this.setState({ loading: true });
  }

  @Bind()
  handleSetTableData(data) {
    const { attribute = {} } = this.props;
    if (attribute.title) {
      //  处理动态列的显示
      const dynamicFields = attribute.title.map(i => i.code); // 动态字段

      const fileSource = data.fileSource.map(file => {
        const fileAttribute = file.attribute ? JSON.parse(file.attribute) : []; // 接口返回的动态字段相关信息

        // 拼凑接口返回的该file的所有动态字段数据
        const dynamicFieldsValueObj = dynamicFields.reduce((acc, curr) => {
          const obj = fileAttribute && fileAttribute.find(j => j.code === curr); // 接口返回的curr动态字段的信息
          return {
            ...acc,
            [curr]: obj?.value,
          };
        }, {});
        return {
          ...file,
          ...dynamicFieldsValueObj,
        };
      });
      this.fileDS.loadData(fileSource);
    } else {
      this.fileDS.loadData(data.fileSource);
    }
    this.logDS.loadData(data.logSource);
    this.setState({ loading: false });
  }

  get fileColumns() {
    const { onDelete, onDownload, showDeleteFlag = true, attribute = {}, dispatch } = this.props;
    const fileColumns = [
      {
        name: 'fileName',
        renderer: ({ value, record }) => this.onFetchName(value, record.data),
        width: 250,
      },
      {
        name: 'uploadDate',
        renderer: ({ value }) => dateTimeRender(value),
        width: 150,
      },
      { name: 'fileSize', renderer: ({ value }) => <span>{`${Math.ceil(value / 1000)}KB`}</span> },
      { name: 'uploaderName' },
      {
        header: intl.get('hzero.common.table.column.option').d('操作'),
        lock: 'right',
        width: 120,
        renderer: rowDS => {
          const { record } = rowDS;
          const operators = [
            {
              key: 'download',
              ele: (
                <a onClick={() => onDownload(record.data)}>
                  {intl.get('hzero.common.button.download').d('下载')}
                </a>
              ),
              len: 2,
              title: intl.get('hzero.common.button.download').d('下载'),
            },
          ];
          if (showDeleteFlag) {
            operators.push({
              key: 'delete',
              ele: (
                <a onClick={() => onDelete(record.data)}>
                  {intl.get('hzero.common.button.delete').d('删除')}
                </a>
              ),
              len: 2,
              title: intl.get('hzero.common.button.delete').d('删除'),
            });
          }
          return operatorRender(operators);
        },
      },
    ];
    // 插入动态扩展列
    if (attribute.title) {
      attribute.title.forEach(item => {
        const column = {
          name: item.code,
          renderer: ({ value, record }) =>
            item.isUrl ? (
              <span>
                <a
                  onClick={() => {
                    dispatch(
                      routerRedux.push({
                        pathname: record.data[`${item.code}_pathname`],
                      })
                    );
                  }}
                >
                  {value}
                </a>
              </span>
            ) : (
              <span>{value}</span>
            ),
          width: item.width || 120,
        };
        fileColumns.splice(item.index, 0, column);
      });
    }
    return fileColumns;
  }

  get logColumns() {
    return [
      {
        name: 'fileName',
        tooltip: 'overflow',
      },
      {
        name: 'operationDate',
        renderer: ({ value }) => dateTimeRender(value),
      },
      {
        name: 'operatorName',
        width: 120,
      },
      {
        name: 'description',
        width: 220,
        tooltip: 'overflow',
      },
    ];
  }

  // 获取动态字段
  @Bind()
  handleGetAttributeFields() {
    const { attribute = {} } = this.props;
    const fields = [];
    // 插入动态扩展列
    if (attribute.title) {
      attribute.title.forEach(item => {
        const field = {
          label: intl.get(`${prefix}.${item.code}`).d(item.name),
          name: item.code,
        };
        fields.push(field);
      });
    }
    return fields;
  }

  render() {
    const { loading } = this.state;
    const { showUploadFlag = true, showContentFlag = false } = this.props;
    return (
      <React.Fragment>
        <Spin spinning={loading}>
          <Tabs>
            <Tabs.TabPane key="file" tab={intl.get(`${prefix}.file`).d('附件')}>
              <Row style={{ marginBottom: 8 }}>
                {showUploadFlag && <Col span={4}>{this.onUploadShow()}</Col>}
                {showContentFlag ? this.props.onfetchContent() : null}
              </Row>
              <Table dataSet={this.fileDS} columns={this.fileColumns} />
            </Tabs.TabPane>
            <Tabs.TabPane key="log" tab={intl.get(`${prefix}.log`).d('附件日志')}>
              <Table dataSet={this.logDS} columns={this.logColumns} />
            </Tabs.TabPane>
          </Tabs>
        </Spin>
      </React.Fragment>
    );
  }
}

export default FileModal;
