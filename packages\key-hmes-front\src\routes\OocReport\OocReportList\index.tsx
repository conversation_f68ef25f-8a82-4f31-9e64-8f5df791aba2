/**
 * @Description: ooc统计报表
 * @Author: <<EMAIL>>
 * @Date: 2022-11-28 16:46:20
 * @LastEditTime: 2022-11-30 15:29:49
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { openTab } from 'utils/menuTab';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC } from 'hcm-components-front/lib/utils/config';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Badge, Tag } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExport';
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { isNil } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import withProps from 'utils/withProps';
import { tableDS } from '../stores/OocReportDS';

const statusMap = {
  NORMAL: 'green',
  PROCESSED: 'green',
  ABNORMAL: 'red',
  PROCESSING: 'geekblue',
  UNPROCESSED: 'orange',
};

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.hspc.OocReport';

const OocList = (props: any) => {
  const { tableDs } = props;

  useEffect(() => {
    tableDs.query(props.tableDs.currentPage);
  }, []);

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'controlCode',
        width: 120,
      },
      {
        name: 'controlDesc',
        width: 120,
      },
      {
        name: 'chartDetailTypeDesc',
        align: ColumnAlign.center,
        width: 120,
      },
      {
        name: 'oocStatus',
        align: ColumnAlign.center,
        renderer: ({ value, record }) => (
          <Badge status={statusMap[value] || 'green'} text={record?.get('oocStatusDesc')} />
        ),
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 160,
      },
      {
        name: 'judgementDesc',
        width: 160,
      },
      {
        name: 'dataValue',
        width: 160,
      },
      {
        name: 'upperControlLimit',
        renderer: ({ value }) => {
          return value > 0 ? value - 0 || value : value;
        },
      },
      {
        name: 'lowerControlLimit',
        renderer: ({ value }) => {
          return value > 0 ? value - 0 || value : value;
        },
      },
      {
        name: 'sampleTime',
        align: ColumnAlign.center,
        width: 160,
      },
      {
        name: 'siteDesc',
        renderer: ({ value }) => {
          return tagList(value);
        },
        width: 120,
      },
      {
        name: 'inspectBusinessTypeDesc',
        width: 120,
      },
      {
        name: 'inspectSchemeCode',
        width: 120,
      },
      {
        name: 'inspectItemDesc',
        width: 120,
      },
      {
        name: 'supplierName',
        width: 120,
      },
      {
        name: 'customerName',
        width: 120,
      },
      {
        name: 'areaDesc',
        renderer: ({ value }) => {
          return tagList(value);
        },
        width: 120,
      },
      {
        name: 'prodLineDesc',
        renderer: ({ value }) => {
          return tagList(value);
        },
        width: 120,
      },
      {
        name: 'workcellDesc',
        renderer: ({ value }) => {
          return tagList(value);
        },
        width: 120,
      },
      {
        name: 'materialDesc',
        renderer: ({ value }) => {
          return tagList(value);
        },
        width: 120,
      },
      {
        name: 'operationDesc',
        renderer: ({ value }) => {
          return tagList(value);
        },
        width: 120,
      },
      {
        name: 'tagDesc',
        renderer: ({ value }) => {
          return tagList(value);
        },
        width: 120,
      },
      {
        name: 'shiftTeamDesc',
        renderer: ({ value }) => {
          return tagList(value);
        },
      },
      {
        name: 'chartTypeDesc',
        renderer: ({ value }) => {
          return tagList(value);
        },
      },
      {
        name: 'abnormalGroup',
      },
      {
        name: 'abnormalType',
        editor: record => record.get('oocStatus') !== 'PROCESSED' && record.getState('editing'),
      },
      {
        name: 'abnormalAnalysis',
        width: 180,
        editor: record => record.get('oocStatus') !== 'PROCESSED' && record.getState('editing'),
      },
      {
        name: 'tempAction',
        editor: record => record.get('oocStatus') !== 'PROCESSED' && record.getState('editing'),
      },
      {
        name: 'permanentAction',
        editor: record => record.get('oocStatus') !== 'PROCESSED' && record.getState('editing'),
      },
      {
        name: 'remark',
        width: 160,
        editor: record => record.getState('editing'),
      },
      {
        name: 'remarkProcessBy',
      },
      {
        name: 'processDate',
        align: ColumnAlign.center,
        width: 160,
      },
      {
        title: intl.get('tarzan.common.label.action').d('操作'),
        name: 'action',
        width: 150,
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          return (
            <>
              {
                record?.getState('editing') ? (
                  <>
                    <a onClick={handleSave}>
                      {intl.get(`${modelPrompt}.save`).d('保存')}
                    </a>
                    <a style={{ 'marginLeft': '20px' }} onClick={() => { record.setState('editing', false); record.reset(); }}>
                      {intl.get(`${modelPrompt}.cancel`).d('取消')}
                    </a>
                  </>
                ) : (
                  <>
                    <a
                      onClick={
                        () => {
                          handleJump(record);
                        }
                      }
                    >
                      {intl.get(`${modelPrompt}.jumpTitle`).d('控制控制图')}
                    </a>
                    <a style={{ 'marginLeft': '20px' }} onClick={() => { record?.setState('editing', true) }}>
                      {intl.get(`${modelPrompt}.edit`).d('编辑')}
                    </a>
                  </>
                )
              }
            </>
          );
        },
      },
    ],
    [],
  );

  // 调用保存接口
  const handleSave = () => {
    tableDs.submit().then(res => {
      const { success, message } = res.rows.content[0]
      if (success) {
        tableDs.query(props.tableDs.currentPage)
      } else {
        notification.error({ message })
      }
    })
  }

  const tagList = list => {
    if (list) {
      return list?.split(',').map(item => <Tag color="blue">{item}</Tag>);
    }
    return null;
  };

  const handleJump = record => {
    const url = `/hspc/control-chart/history-chart/${record.get('controlId')}`;
    openTab({
      icon: '',
      title: intl.get(`${modelPrompt}.historyGraphicDisplay`).d('历史图形展示'),
      key: url,
      path: url,
      closable: true,
    });
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    if (queryParmas?.processObjectList) {
      delete queryParmas.processObjectList;
    }
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.OocReportTitle`).d('OOC统计报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.TARZAN_HSPC}/v1/${tenantId}/ooc/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="ooctjbb1"
          customizedCode="ooctjbb1"
          dataSet={tableDs}
          columns={columns}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hspc.OocReport', 'tarzan.common'],
})(
  withProps(() => {
    const tableDs = new DataSet({
      ...tableDS(),
    });
    return {
      tableDs,
    };
  })(OocList),
);

