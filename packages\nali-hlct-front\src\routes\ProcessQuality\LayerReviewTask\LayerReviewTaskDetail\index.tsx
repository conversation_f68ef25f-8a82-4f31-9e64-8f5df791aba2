/**
 * @Description: 分层审核任务-详情界面
 * @Author: <EMAIL>
 * @Date: 2023/8/21 14:46
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  Select,
  Switch,
  Table,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { Collapse, Tag, Checkbox, Badge, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { Content, Header } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import uuid from 'uuid/v4';
import { getCurrentUserId } from 'utils/utils';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { openTab } from 'hzero-front/lib/utils/menuTab';
import { observer } from 'mobx-react';

import { detailDS, lineFormDS, lineTableDS } from '../stores/DetailDS';
import { SaveReviewTask, SubmitSchemeTask, QueryLastReview } from '../services';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.layerReview.layerReviewTask';
const currentUserId = getCurrentUserId();

const layerReviewTaskDetail = props => {
  const {
    history,
    match: { params },
  } = props;
  const layerReviewTaskId = params.layerReviewTaskId;

  const [canEdit, setCanEdit] = useState(false);
  const [activeKey, setActiveKey] = useState<any>(['basicInfo']);
  const [status, setStatus] = useState('NEW');
  const [selectedValue, setSelectedValue] = useState<number[]>([]);
  // 拆解位置列表 每元素 包含了表单和表格两个 DS
  const [tableList, setTableList]: any = useState([]);
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  // 保存审核方案
  const { run: saveReviewTask, loading: saveLoading } = useRequest(SaveReviewTask(), {
    manual: true,
  });
  // 保存审核项目
  const { run: queryLastReview, loading: queryLastReviewLoading } = useRequest(
    QueryLastReview(layerReviewTaskId),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 上次审核查看
  const { run: submitSchemeTask, loading: submitLoading } = useRequest(SubmitSchemeTask(), {
    manual: true,
  });

  useEffect(() => {
    if (layerReviewTaskId === 'create') {
      // 新建时
      setCanEdit(true);
      setTableList([]);
      return;
    }
    // 编辑时
    handleQueryDetail(layerReviewTaskId);
  }, [layerReviewTaskId]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('layerReviewTaskId', id);
    detailDs.query().then(res => {
      const { layerReviewTaskStatus, qisLayerRevtskDtlList = [] } = res?.rows;
      setStatus(layerReviewTaskStatus);

      const _activeKey = ['basicInfo'];
      const _tableList: any = [];
      qisLayerRevtskDtlList.forEach(item => {
        const lineTableDs = new DataSet(lineTableDS());
        const lineFormDs = new DataSet({
          ...lineFormDS(),
          children: {
            qisLayerTskdtlItemList: lineTableDs,
          },
          data: [
            {
              ...item,
              abnormalFlag: item?.abnormalFlag || 'N',
            },
          ],
        });
        _tableList.push({ lineFormDs, lineTableDs, uuid: uuid() });
        if (['NEW', 'EXECUTING', 'CORRECTING'].includes(item?.layerRevtskDtlStatus)) {
          _activeKey.push(item.layerRevtskDtlCode);
        }
      });
      setActiveKey(_activeKey);
      setTableList(_tableList);
    });
  };

  const handleEdit = () => {
    if (!selectedValue.length) {
      return notification.error({
        message: intl.get(`${modelPrompt}.error.pleaseSelectOneData`).d('至少勾选一条数据！'),
      })
    }
    // 选中的状态列表
    const selectStatusList: string[] = [];
    tableList.forEach((item) => {
      const { layerRevtskDtlId, layerRevtskDtlStatus } = item.lineFormDs.current?.toData();
      if (selectedValue.includes(layerRevtskDtlId) && !selectStatusList.includes(layerRevtskDtlStatus)) {
        selectStatusList.push(layerRevtskDtlStatus);
      }
    })
    if (selectStatusList.every((item) => ['NEW', 'EXECUTING'].includes(item)) || selectStatusList.every((item) => item === 'CORRECTING')) {
      setCanEdit(true);
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.error.splitNewAndCorrectingEdit`).d('新建/审核中的任务分号和整改中的数据请分开进行编辑'),
      })
    }
  };

  const handleCancel = () => {
    if (layerReviewTaskId === 'create') {
      history.push('/hwms/layer-review/layer-review-task/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(layerReviewTaskId);
    }
  };

  const handleSave = async (submitFlag = false) => {
    if (submitFlag) {
      const valRes = await handleValidate();
      if (!valRes) {
        return;
      }
      // 非编辑状态点击提交需要限制 1.必须勾选至少一条任务分号  2.只有新建审核中状态的数据允许提交
      if (!selectedValue.length) {
        return notification.error({
          message: intl.get(`${modelPrompt}.error.pleaseSelectOneData`).d('至少勾选一条数据提交！'),
        })
      }
      // 选中的状态列表
      const selectStatusList: string[] = [];
      tableList.forEach((item) => {
        const { layerRevtskDtlId, layerRevtskDtlStatus } = item.lineFormDs.current?.toData();
        if (selectedValue.includes(layerRevtskDtlId) && !selectStatusList.includes(layerRevtskDtlStatus)) {
          selectStatusList.push(layerRevtskDtlStatus);
        }
      })
      if (!selectStatusList.every((item) => ['NEW', 'EXECUTING'].includes(item))) {
        return notification.error({
          message: intl.get(`${modelPrompt}.error.submitStatusError`).d('只有新建、审核中的务分号允许提交，请检查！'),
        })
      }
    }
    const _data: any = [];
    tableList.forEach(item => {
      const dataSet = item.lineFormDs;
      if (!['NEW', 'EXECUTING', 'CORRECTING'].includes(dataSet.current?.get('layerRevtskDtlStatus'))) {
        return;
      }
      if (
        selectedValue.length &&
        selectedValue.includes(dataSet.current?.get('layerRevtskDtlId'))
      ) {
        const { qisLayerTskdtlItemList, ...others } = dataSet.current?.toData();
        qisLayerTskdtlItemList.forEach(i => (i.reviewById = i.reviewById || currentUserId));
        _data.push({
          ...others,
          qisLayerTskdtlItemList,
        });
      } else if (!selectedValue.length) {
        const { qisLayerTskdtlItemList, ...others } = dataSet.current?.toData();
        qisLayerTskdtlItemList.forEach(i => (i.reviewById = i.reviewById || currentUserId));
        _data.push({
          ...others,
          qisLayerTskdtlItemList,
        });
      }
    });
    saveReviewTask({
      params: _data,
      onSuccess: () => {
        notification.success({});
        if (submitFlag) {
          handleSubmit(false);
        } else {
          setCanEdit(false);
          setSelectedValue([]);
          handleQueryDetail(layerReviewTaskId);
        }
      },
    });
  };

  const handleValidate = async () => {
    // 校验所有表单
    const normalValidate = await Promise.all(
      tableList.map(async item => {
        const dataSet = item.lineFormDs;
        if (
          ((selectedValue.length &&
            selectedValue.includes(dataSet.current?.get('layerRevtskDtlId'))) ||
            !selectedValue.length) &&
          ['NEW', 'EXECUTING'].includes(dataSet.current?.get('layerRevtskDtlStatus'))
        ) {
          const itemFormValidate = await item.lineFormDs.validate();
          const itemTableValidate = await item.lineTableDs.validate();
          return itemFormValidate && itemTableValidate;
        }
        return true;
      }),
    );

    // 汇总校验结果
    return normalValidate.every(val => val);
  };

  const handleSubmit = async (validateFlag = true) => {
    if (validateFlag) {
      const valRes = await handleValidate();
      if (!valRes) {
        return;
      }
      // 非编辑状态点击提交需要限制 1.必须勾选至少一条任务分号  2.只有新建审核中状态的数据允许提交
      if (!selectedValue.length) {
        return notification.error({
          message: intl.get(`${modelPrompt}.error.pleaseSelectOneData`).d('至少勾选一条数据提交！'),
        })
      }
      // 选中的状态列表
      const selectStatusList: string[] = [];
      tableList.forEach((item) => {
        const { layerRevtskDtlId, layerRevtskDtlStatus } = item.lineFormDs.current?.toData();
        if (selectedValue.includes(layerRevtskDtlId) && !selectStatusList.includes(layerRevtskDtlStatus)) {
          selectStatusList.push(layerRevtskDtlStatus);
        }
      })
      if (!selectStatusList.every((item) => ['NEW', 'EXECUTING'].includes(item))) {
        return notification.error({
          message: intl.get(`${modelPrompt}.error.submitStatusError`).d('只有新建、审核中的务分号允许提交，请检查！'),
        })
      }
    }

    let _data: any = [];
    if (selectedValue.length) {
      _data = [...selectedValue];
    } else {
      tableList.forEach(item => {
        const dataSet = item.lineFormDs;
        if (!['NEW', 'EXECUTING'].includes(dataSet.current?.get('layerRevtskDtlStatus'))) {
          return;
        }
        _data.push(dataSet.current?.get('layerRevtskDtlId'));
      });
    }
    submitSchemeTask({
      params: _data,
      onSuccess: () => {
        notification.success({});
        setSelectedValue([]);
        setCanEdit(false);
        handleQueryDetail(layerReviewTaskId);
      },
    });
  };

  const handleClickPreview = () => {
    return new Promise(async (resolve) => {
      const res = await queryLastReview({
        params: { layerReviewTaskId },
      });
      const _layerReviewTaskId = res.rows?.layerReviewTaskId;
      if (_layerReviewTaskId) {
        const url = `/hwms/layer-review/layer-review-task/dist/${_layerReviewTaskId}`;
        openTab({
          icon: '',
          title: intl.get(`${modelPrompt}.layerReviewTask`).d('分层审核任务'),
          key: url,
          path: url,
          closable: true,
        });
        return resolve(true);
      }
      notification.warning({
        message: intl.get(`${modelPrompt}.cannot.find.lastTask`).d('未找到上次审核任务信息'),
      });
      return resolve(false);
    })
  };

  const handleChangeProblemFlag = record => {
    record.set('problemCode', undefined);
    record.set('problemDec', undefined);
    record.set('problemType', undefined);
    record.set('problemResPersonLov', undefined);
    record.set('correctiveFinishTime', undefined);
  };

  const handleJumpToProblem = (problemId, layerTskdtlItemId = undefined) => {
    if (problemId) {
      history.push(`/hwms/problem-management/problem-management-platform/dist/${problemId}`);
    } else {
      history.push({
        key: new Date().getTime(),
        pathname: `/hwms/problem-management/problem-management-platform/list`,
        state: {
          stateType: 'create',
          layerTskdtlItemId,
        },
      })
    }
    
  }

  const handleRenderProblemCode = record => {
    const { problemManFlag, problemCode, problemId, layerTskdtlItemId } = record.toData();
    if (problemManFlag !== 'Y') {
      return '';
    }
    if (problemCode && problemId) {
      return (
        <a onClick={() => handleJumpToProblem(problemId)}>{problemCode}</a>
      )
    }
    return (
      <Button funcType={FuncType.flat} disabled={!['NEW', 'EXECUTING'].includes(status)} onClick={() => handleJumpToProblem(null, layerTskdtlItemId)}>
        {intl.get(`${modelPrompt}.generateProblem`).d('生成问题')}
      </Button>
    )
  };

  const handleChangeReviewResult = record => {
    record.set('problemManFlag', 'N');
    handleChangeProblemFlag(record);
  };

  const lineTableCol: any = useMemo(
    () => [
      { name: 'layerReviewItemCode', width: 160 },
      { name: 'projectName', width: 160 },
      { name: 'projectFrom' },
      { name: 'projectClassify' },
      { name: 'reviewPersonLov' },
      {
        name: 'beReviewedLov',
        editor: record =>
          canEdit &&
          ['NEW', 'EXECUTING'].includes(
            record.dataSet.parent.current?.get('layerRevtskDtlStatus'),
          ) &&
          (!selectedValue.length ||
            selectedValue.includes(record.dataSet.parent.current?.get('layerRevtskDtlId'))),
      },
      {
        name: 'reviewMethod',
        width: 160,
        editor: record =>
          canEdit &&
          ['NEW', 'EXECUTING'].includes(
            record.dataSet.parent.current?.get('layerRevtskDtlStatus'),
          ) &&
          (!selectedValue.length ||
            selectedValue.includes(record.dataSet.parent.current?.get('layerRevtskDtlId'))),
      },
      {
        name: 'reviewResult',
        editor: record =>
          canEdit &&
          ['NEW', 'EXECUTING'].includes(
            record.dataSet.parent.current?.get('layerRevtskDtlStatus'),
          ) &&
          (!selectedValue.length ||
            selectedValue.includes(record.dataSet.parent.current?.get('layerRevtskDtlId'))) && (
            <Select onChange={() => handleChangeReviewResult(record)} />
          ),
      },
      { name: 'nextVerify', width: 160 },
      {
        name: 'problemManFlag',
        width: 160,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
        editor: record =>
          canEdit &&
          ['NEW', 'EXECUTING'].includes(
            record.dataSet.parent.current?.get('layerRevtskDtlStatus'),
          ) &&
          (!selectedValue.length ||
            selectedValue.includes(record.dataSet.parent.current?.get('layerRevtskDtlId'))) && (
            <Switch onChange={() => handleChangeProblemFlag(record)} />
          ),
      },
      { 
        name: 'problemCode',
        width: 150,
        renderer: ({ record }) => handleRenderProblemCode(record),
      },
      {
        name: 'problemDec',
        width: 250,
        editor: record =>
          canEdit &&
          ['NEW', 'EXECUTING'].includes(
            record.dataSet.parent.current?.get('layerRevtskDtlStatus'),
          ) &&
          (!selectedValue.length ||
            selectedValue.includes(record.dataSet.parent.current?.get('layerRevtskDtlId'))) &&
          record?.get('problemManFlag') !== 'Y' &&
          record?.get('reviewResult') === 'NG' && (
            <TextArea autoSize={{ minRows: 1, maxRows: 8 }} />
          ),
      },
      {
        name: 'problemType',
        editor: record =>
          canEdit &&
          ['NEW', 'EXECUTING'].includes(
            record.dataSet.parent.current?.get('layerRevtskDtlStatus'),
          ) &&
          (!selectedValue.length ||
            selectedValue.includes(record.dataSet.parent.current?.get('layerRevtskDtlId'))) &&
          record?.get('problemManFlag') !== 'Y' &&
          record?.get('reviewResult') === 'NG',
        width: 180,
      },
      {
        name: 'problemResPersonLov',
        editor: record =>
          canEdit &&
          ['NEW', 'EXECUTING'].includes(
            record.dataSet.parent.current?.get('layerRevtskDtlStatus'),
          ) &&
          (!selectedValue.length ||
            selectedValue.includes(record.dataSet.parent.current?.get('layerRevtskDtlId'))) &&
          record?.get('problemManFlag') !== 'Y' &&
          record?.get('reviewResult') === 'NG',
      },
      {
        name: 'correctiveFinishTime',
        width: 150,
        editor: record =>
          canEdit &&
          record.dataSet.parent.current?.get('layerRevtskDtlStatus') === 'CORRECTING' &&
          (!selectedValue.length ||
            selectedValue.includes(record.dataSet.parent.current?.get('layerRevtskDtlId'))) &&
          record.get('problemResPersonId') === currentUserId,
      },
    ],
    [canEdit, selectedValue.length, currentUserId, status],
  );

  const handleSelect = props => {
    const { value, checked } = props.target;
    const currentValue = [...selectedValue];
    if (checked) {
      currentValue.push(value);
    } else {
      currentValue.splice(currentValue.indexOf(value), 1);
    }
    setSelectedValue(currentValue);
  };

  const handleChangeAbnormalFlag = item => {
    const formDs = item.lineFormDs;
    const lineDs = item.lineTableDs;
    formDs.current.set('abnormalCloseReason', '');
    lineDs.forEach(_record => {
      _record.set('reviewMethod', undefined);
      _record.set('reviewResult', undefined);
      _record.set('problemManFlag', 'N');
      handleChangeProblemFlag(_record);
    });
  };

  const CollapsePanelTitle = observer(({ dataSet, selectedValue }) => {
    const renderStatusTag = record => {
      const { layerRevtskDtlStatus } = record?.toData() || {};
      const text = record?.getField('layerRevtskDtlStatus')!.getText() || '';

      switch (layerRevtskDtlStatus) {
        case 'EXECUTING':
          return <Tag color="orange">{text}</Tag>;
        case 'NORMAL_CLOSED':
          return <Tag color="green">{text}</Tag>;
        case 'NEW':
          return <Tag color="blue">{text}</Tag>;
        case 'TIMEOUT_CLOSED':
        case 'CORRECT_TIMEOUT_CLOSED':
          return <Tag color="red">{text}</Tag>;
        case 'CORRECTING':
          return <Tag color="purple">{text}</Tag>;
        default:
          return <Tag color="cyan">{text}</Tag>;
      }
    };

    return (
      <span>
        <Checkbox
          disabled={
            canEdit || !['NEW', 'EXECUTING', 'CORRECTING'].includes(dataSet.current?.get('layerRevtskDtlStatus'))
          }
          value={dataSet.current?.get('layerRevtskDtlId')}
          checked={selectedValue.indexOf(dataSet.current?.get('layerRevtskDtlId')) !== -1}
          onChange={handleSelect}
        />
        <span className={styles['panel-title-item']}>
          {intl.get(`${modelPrompt}.panelItem.layerRevtskDtlCode`).d('审核任务分号：')}
          {dataSet.current?.get('layerRevtskDtlCode')}
        </span>
        <span className={styles['panel-title-item']}>
          {intl.get(`${modelPrompt}.panelItem.equipment`).d('设备：')}
          {dataSet.current?.get('equimentCode')}/{dataSet.current?.get('equimentName')}
        </span>
        {renderStatusTag(dataSet.current)}
      </span>
    );
  });

  return (
    <div className="hmes-style">
      <TarzanSpin
        dataSet={detailDs}
        spinning={saveLoading || submitLoading || queryLastReviewLoading}
      >
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('分层审核任务')}
          backPath="/hwms/layer-review/layer-review-task/list"
        >
          {canEdit ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave()}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <Button
              icon="edit-o"
              disabled={!['NEW', 'EXECUTING', 'CORRECTING'].includes(status)}
              color={ButtonColor.primary}
              onClick={handleEdit}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
          <Button
            disabled={!['NEW', 'EXECUTING', 'CORRECTING'].includes(status)}
            icon="send-o"
            onClick={() => {
              if (canEdit) {
                handleSave(true);
              } else {
                handleSubmit();
              }
            }}
          >
            {intl.get(`${modelPrompt}.button.submit`).d('提交')}
          </Button>
          {/* 暂时注释以下逻辑，这块逻辑后续可能用到 */}
          {/* {!canEdit && Boolean(selectedValue.length) && (
            <Button
              icon="edit-o"
              disabled={!['NEW', 'EXECUTING', 'CORRECTING'].includes(status)}
              color={ButtonColor.primary}
              onClick={handleEdit}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )} */}
          {/* {!canEdit && !selectedValue.length && (
            <Popconfirm
              title={intl
                .get(`${modelPrompt}.confirm.submit`)
                .d('未勾选审核任务分号，所有新建、审核中、整改中的审核任务分号都将进入编辑模式,是否确认?')}
              onConfirm={handleEdit}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <Button
                icon="edit-o"
                disabled={!['NEW', 'EXECUTING', 'CORRECTING'].includes(status)}
                color={ButtonColor.primary}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
            </Popconfirm>
          )} */}
          {/* {selectedValue.length ? (
            <Button
              disabled={!['NEW', 'EXECUTING'].includes(status)}
              icon="send-o"
              onClick={() => {
                if (canEdit) {
                  handleSave(true);
                } else {
                  handleSubmit();
                }
              }}
            >
              {intl.get(`${modelPrompt}.button.submit`).d('提交')}
            </Button>
          ) : (
            <Popconfirm
              title={intl
                .get(`${modelPrompt}.confirm.submitAll`)
                .d('是否提交所有新建，审核中的审核任务分号?')}
              onConfirm={() => {
                if (canEdit) {
                  handleSave(true);
                } else {
                  handleSubmit();
                }
              }}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <Button disabled={!['NEW', 'EXECUTING'].includes(status)} icon="send-o">
                {intl.get(`${modelPrompt}.button.submit`).d('提交')}
              </Button>
            </Popconfirm>
          )} */}
          <Button onClick={handleClickPreview} loading={queryLastReviewLoading}>
            {intl.get(`${modelPrompt}.button.previewReview`).d('上次审核查看')}
          </Button>
        </Header>
        <Content>
          <Collapse
            collapsible="icon"
            bordered={false}
            activeKey={activeKey}
            onChange={tabKey => setActiveKey(tabKey)}
          >
            <Panel key="basicInfo" header={intl.get(`${modelPrompt}.title.baseInfo`).d('基础信息')}>
              <Form dataSet={detailDs} columns={3} disabled labelWidth={112}>
                <TextField name="layerReviewTaskCode" />
                <Select name="layerReviewTaskStatus" />
                <Select name="layerReviewLevel" />
                <Lov name="siteLov" />
                <Lov name="prodLineLov" />
                <Lov name="operationLov" />
                <DateTimePicker name="requestFinishTime" />
              </Form>
            </Panel>
            {tableList.length &&
              tableList.map(item => (
                <Panel
                  key={item.lineFormDs.current?.get('layerRevtskDtlCode')}
                  header={
                    <CollapsePanelTitle dataSet={item.lineFormDs} selectedValue={selectedValue} />
                  }
                >
                  <Form
                    dataSet={item.lineFormDs}
                    columns={3}
                    labelWidth={112}
                    disabled={
                      !canEdit ||
                      !['NEW', 'EXECUTING'].includes(
                        item.lineFormDs.current?.get('layerRevtskDtlStatus'),
                      ) ||
                      !(
                        !selectedValue.length ||
                        selectedValue.includes(item.lineFormDs.current?.get('layerRevtskDtlId'))
                      )
                    }
                  >
                    <Switch name="abnormalFlag" onChange={() => handleChangeAbnormalFlag(item)} />
                    <TextArea name="abnormalCloseReason" autoSize={{ minRows: 1, maxRows: 8 }} />
                    <DateTimePicker name="closeTime" />
                  </Form>
                  <Table dataSet={item.lineTableDs} columns={lineTableCol} />
                </Panel>
              ))}
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(layerReviewTaskDetail);
