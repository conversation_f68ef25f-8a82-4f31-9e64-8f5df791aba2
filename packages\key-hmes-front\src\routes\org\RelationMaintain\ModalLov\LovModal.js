import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import {
  Button,
  Col,
  Form,
  Input,
  Row,
  Spin,
  Table,
  InputNumber,
  DatePicker,
  Select,
} from 'hzero-ui';
import moment from 'moment';
import { isArray, isEmpty, isFunction } from 'lodash';
import qs from 'querystring';

import {
  createPagination,
  getCurrentOrganizationId,
  getResponse,
  getDateFormat,
  getDateTimeFormat,
} from 'utils/utils';
import intl from 'utils/intl';
import { DEFAULT_DATETIME_FORMAT, DEFAULT_DATE_FORMAT } from 'utils/constants';

import './index.module.less';
import { queryLovData } from './api';

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    sm: { span: 8 },
  },
  wrapperCol: {
    sm: { span: 14 },
  },
};

const defaultRowKey = 'lovId';

const LovModal = (props, ref) => {
  const [selfList, setSelfList] = useState([]);
  const [treeKeys, setTreeKeys] = useState([]);
  const [pagination, setPagination] = useState({});
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);

  const {
    form,
    lov,
    onSelect = () => {},
    queryParams = {},
    ldpData = {},
    lovLoadLoading,
    width,
    queryInputProps = {},
  } = props;
  const {
    valueField: rowkey = defaultRowKey,
    delayLoadFlag,
    queryUrl,
    pageSize,
    lovCode,
    lovTypeCode,
    queryFields = [],
    childrenFieldName,
    tableFields = [],
  } = lov;
  const { getFieldDecorator } = form;

  useEffect(() => {
    const initKey = [];
    if (selectedRowKeys) {
      initKey.push(...selectedRowKeys);
    }
    setSelectedRowKeys([...new Set(initKey)]);
  }, []);

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    loadOnFirstVisible: () => {
      if (!delayLoadFlag) {
        queryData({});
      }
    },
    loadBeforeSetDefaultValue:
      (ref,
      () => {
        const formData = {};
        queryFields.forEach(item => {
          const { field, require, sourceCode, dataType } = item;
          if (field && require && sourceCode && dataType) {
            formData[field] = ((ldpData[sourceCode] || [])[0] || {}).value;
          } else {
            formData[field] = undefined;
          }
        });
        form.setFieldsValue(formData);
      }),
  }));

  const onSelectChange = (_selectedRowKeys, _selectedRows) => {
    const newRecord = [];

    const keysArr = [];
    //  和新数据取并
    const arr = selectedRows.concat(_selectedRows);
    arr.forEach(item => {
      if (_selectedRowKeys.includes(item[rowkey]) && !keysArr.includes(item[rowkey])) {
        newRecord.push(item);
        keysArr.push(item[rowkey]);
      }
    });

    onSelect(newRecord);
    setSelectedRowKeys(_selectedRowKeys);
    setSelectedRows(newRecord);
  };

  const hideLoading = () => {
    setLoading(false);
  };

  const queryData = (_pagination = {}) => {
    form.validateFields((error, filter) => {
      if (error) {
        return;
      }
      let nowQueryParams = queryParams || {};
      if (isFunction(nowQueryParams)) {
        nowQueryParams = nowQueryParams();
      }
      const queryIndex = queryUrl.indexOf('?');
      let sourceQueryParams = {};
      if (queryIndex !== -1) {
        sourceQueryParams = qs.parse(queryUrl.substr(queryIndex + 1));
      }

      const formatFilter = { ...filter };
      queryFields.forEach(item => {
        if (item.dataType === 'DATE' || item.dataType === 'DATETIME') {
          if (filter[item.field]) {
            formatFilter[item.field] = moment(filter[item.field]).format(
              item.dataType === 'DATETIME' ? DEFAULT_DATETIME_FORMAT : DEFAULT_DATE_FORMAT,
            );
          }
        }
      });
      const sourceParams = {
        ...formatFilter,
        page: _pagination.current - 1 || 0,
        size: _pagination.pageSize || pageSize,
        ...sourceQueryParams,
        ...nowQueryParams,
      };
      const params =
        lovTypeCode !== 'URL'
          ? Object.assign(sourceParams, {
            lovCode,
          })
          : sourceParams;

      /**
       * 替换查询 Url 中的变量
       * @param {String} urls
       * @param {Object} data
       */
      function getUrl(urls, data) {
        let ret = urls;
        const organizationRe = /\{organizationId\}|\{tenantId\}/g;
        Object.keys(data).map(key => {
          const re = new RegExp(`{${key}}`, 'g');
          ret = ret.replace(re, data[key]);
          return ret;
        });
        if (organizationRe.test(ret)) {
          ret = ret.replace(organizationRe, getCurrentOrganizationId());
        }
        const index = ret.indexOf('?'); // 查找是否有查询条件
        if (queryIndex !== -1) {
          ret = ret.substr(0, index);
        }
        return ret;
      }

      const url = getUrl(queryUrl, queryParams);

      setLoading(true);
      queryLovData(url, params)
        .then(res => {
          if (getResponse(res)) {
            dataFilter(res);
          }
        })
        .finally(() => {
          hideLoading();
        });
    });
  };

  const formReset = () => {
    form.resetFields();
  };

  /**
   * 树 child 属性更改
   * @param {Array} list 原树结构数据
   * @param {String} childName 要替换的 childName
   */
  const setChildren = (data, childName) =>
    childName
      ? data.map(n => {
        const item = n;
        if (!isEmpty(n[childName])) {
          defineProperty(item, 'children', [{ ...n[childName] }]);
        }
        if (!isEmpty(item.children)) {
          item.children = setChildren(item.children);
        }
        return item;
      })
      : data;

  /**
   * 处理返回列表数据
   * @param {Object|Array} data - 返回的列表数据
   */
  const dataFilter = data => {
    const isTreeOfData = isArray(data);
    const hasParams = !isEmpty(
      Object.values(form.getFieldsValue()).filter(e => e !== undefined && e !== ''),
    );
    const list = isTreeOfData ? setChildren(data, childrenFieldName) : data.content;
    const _pagination = !isTreeOfData && createPagination(data);

    const _treeKeys = []; // 树状 key 列表
    if (isTreeOfData && hasParams) {
      /**
       * 遍历生成树列表
       * @param {*} treeList - 树列表数据
       */
      const flatKeys = treeList => {
        if (isArray(treeList.children) && !isEmpty(treeList.children)) {
          _treeKeys.push(treeList[rowkey]);
          treeList.children.forEach(item => flatKeys(item));
        } else {
          _treeKeys.push(treeList[rowkey]);
        }
      };

      list.forEach(item => flatKeys(item)); // 遍历生成 key 列表
    }

    setSelfList(list);
    setTreeKeys(_treeKeys);
    setPagination(_pagination);
  };

  const defineProperty = (obj, property, value) => {
    Object.defineProperty(obj, property, {
      value,
      writable: true,
      enumerable: false,
      configurable: true,
    });
  };

  /**
   * 访问对象由字符串指定的多层属性
   * @param {Object} obj 访问的对象
   * @param {String} str 属性字符串，如 'a.b.c.d'
   */
  const parseField = (obj, str) => {
    if (/[.]/g.test(str)) {
      const arr = str.split('.');
      const newObj = obj[arr[0]];
      const newStr = arr.slice(1).join('.');
      return parseField(newObj, newStr);
    }
    return obj[str];
  };

  if (lovLoadLoading) {
    return <Spin spinning />;
  }

  const isTree = isArray(selfList);
  const rowSelection = {
    selectedRowKeys,
    selectedRows,
    onChange: onSelectChange,
  };
  const tableProps = {
    loading,
    rowSelection,
    pagination,
    bordered: false,
    dataSource: selfList,
    columns: tableFields,
    onChange: queryData,
  };

  const treeProps = isTree
    ? {
      uncontrolled: true,
      expandedRowKeys: treeKeys,
    }
    : {};

  // 查询条件表单
  const span = queryFields.length <= 1 || width <= 400 ? 24 : 12;
  const queryInput = queryFields.map((queryItem = {}) => {
    const valueListData = ldpData[queryItem.sourceCode] || [];
    switch (queryItem.dataType) {
      case 'INT':
        return (
          <Col span={span} key={queryItem.field}>
            <FormItem {...formItemLayout} label={queryItem.label}>
              {getFieldDecorator(queryItem.field, {
                rules: [
                  {
                    required: queryItem.require || false,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: queryItem.label,
                    }),
                  },
                ],
              })(<InputNumber style={{ width: '100%' }} onPressEnter={queryData} />)}
            </FormItem>
          </Col>
        );
      case 'DATE':
        return (
          <Col span={span} key={queryItem.field}>
            <FormItem {...formItemLayout} label={queryItem.label}>
              {getFieldDecorator(queryItem.field, {
                rules: [
                  {
                    required: queryItem.require || false,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: queryItem.label,
                    }),
                  },
                ],
              })(<DatePicker style={{ width: '100%' }} placeholder="" format={getDateFormat()} />)}
            </FormItem>
          </Col>
        );
      case 'DATETIME':
        return (
          <Col span={span} key={queryItem.field}>
            <FormItem {...formItemLayout} label={queryItem.label}>
              {getFieldDecorator(queryItem.field, {
                rules: [
                  {
                    required: queryItem.require || false,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: queryItem.label,
                    }),
                  },
                ],
              })(
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder=""
                  showTime={{ format: DEFAULT_DATETIME_FORMAT }}
                  format={getDateTimeFormat()}
                />,
              )}
            </FormItem>
          </Col>
        );
      case 'SELECT':
        return queryItem.sourceCode === 'multiple' ? (
          <Col span={span} key={queryItem.field}>
            <FormItem {...formItemLayout} label={queryItem.label}>
              {getFieldDecorator(queryItem.field, {
                rules: [
                  {
                    required: queryItem.require || false,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: queryItem.label,
                    }),
                  },
                ],
              })(
                <Select
                  mode="tags"
                  style={{ width: '100%' }}
                  tokenSeparators={[',', ';']}
                  dropdownStyle={{ display: 'none' }}
                  allowClear
                />,
              )}
            </FormItem>
          </Col>
        ) : (
          <Col span={span} key={queryItem.field}>
            <FormItem {...formItemLayout} label={queryItem.label}>
              {getFieldDecorator(queryItem.field, {
                rules: [
                  {
                    required: queryItem.require || false,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: queryItem.label,
                    }),
                  },
                ],
              })(
                <Select allowClear style={{ width: '100%' }}>
                  {valueListData.map(item => (
                    <Select.Option value={item.value} key={item.value}>
                      {item.meaning}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
        );
      default:
        return (
          <Col span={span} key={queryItem.field}>
            <FormItem {...formItemLayout} label={queryItem.label}>
              {getFieldDecorator(queryItem.field, {
                rules: [
                  {
                    required: queryItem.require || false,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: queryItem.label,
                    }),
                  },
                ],
              })(<Input {...queryInputProps} dbc2sbc={false} />)}
            </FormItem>
          </Col>
        );
    }
  });

  return (
    <Form>
      {queryFields.length > 0 ? (
        <div style={{ display: 'flex', marginBottom: '10px', alignItems: 'flex-start' }}>
          <Row style={{ flex: 'auto' }}>{queryInput}</Row>
          <div className="lov-modal-btn-container">
            <Button onClick={formReset} style={{ marginRight: 8 }}>
              {intl.get('hzero.common.button.reset').d('重置')}
            </Button>
            <Button type="primary" htmlType="submit" onClick={queryData}>
              {intl.get('hzero.common.button.search').d('查询')}
            </Button>
          </div>
        </div>
      ) : null}
      <Table
        resizable={false}
        rowKey={record => parseField(record, rowkey)}
        {...tableProps}
        {...treeProps}
      />
    </Form>
  );
};

export default forwardRef(LovModal);
