import React, { useEffect, useState, useMemo } from 'react';
import {
  Button,
  DataSet,
  Table,
  Form,
  Spin,
  TextField,
  Row,
  Col,
  Modal,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import request from 'utils/request';
import {
  getCurrentOrganizationId,
} from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import {Collapse} from "choerodon-ui";
import { BASIC } from '@utils/config';
import {
  containerQueryDS,
  containerInfoDS,
  loadDS,
  unLoadDS,
} from './stores/ContainerLoadAndUnloadMesDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.ContainerLoadAndUnloadMes';
const { Panel } = Collapse;
let TableHeight = 500;

const ContainerLoadAndUnloadMes = () => {
  const [loading, setLoading] = useState(false);
  const [loadButton, setLoadingButton] = useState(false);
  const [unLoadButton, setUnLoadButton] = useState(false);
  const [originAlways, setOriginAlways] = useState(false);
  const [unloadFlag, setUnloadFlag] = useState('N');
  // const [operateItem, setOperateItem] = useState({});
  const [loadedEoList, setLoadedEoList] = useState([]);
  const [selectedLoadRow, setSelectedLoadRow] = useState([]); // 待装载选中
  const [selectedUnLoadRow, setSelectedUnLoadRow] = useState([]); // 已装载选中
  const containerQueryDs = useMemo(() => new DataSet(containerQueryDS()), []); // 容器查询DS
  const containerInfoDs = useMemo(() => new DataSet(containerInfoDS()), []); // 容器信息DS
  const loadDs = useMemo(() => new DataSet(loadDS(containerInfoDs)), []); // 待装载DS
  const unLoadDs = useMemo(() => new DataSet(unLoadDS(containerInfoDs)), []); // 已装载DS

  useEffect(() => {
    TableHeight = window.screen.height - 700;
    queryUserCache()
  }, []);

  useEffect(() => {
    // 待装载添加选中监听事件
    loadDs.addEventListener('select', handleLoadDataSetSelect);
    loadDs.addEventListener('unSelect', handleLoadDataSetSelect);
    loadDs.addEventListener('selectAll', handleLoadDataSetSelect);
    loadDs.addEventListener('unSelectAll', handleLoadDataSetSelect);
    loadDs.addEventListener('load', () => setLoadingButton(!!loadDs.toData().length));
    // 已装载添加选中监听事件
    unLoadDs.addEventListener('select', handleUnLoadDataSetSelect);
    unLoadDs.addEventListener('unSelect', handleUnLoadDataSetSelect);
    unLoadDs.addEventListener('selectAll', handleUnLoadDataSetSelect);
    unLoadDs.addEventListener('unSelectAll', handleUnLoadDataSetSelect);
    unLoadDs.addEventListener('unSelectAll', handleUnLoadDataSetSelect);
    unLoadDs.addEventListener('load', () => setUnLoadButton(!!unLoadDs.toData().length));
  }, []);

  // 查缓存
  const queryUserCache = () => {
    setLoading(true);
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/cache-all-detail/app`, {
      method: 'GET',
      params: {},
    }).then(async res => {
      if (res && res.success) {
        if (res.rows?.headContainer && res.rows?.lineContainerList) {
          const { headContainer, lineContainerList, materialLotList } =
            res.rows;
          containerQueryDs.current?.set('identification', headContainer.identification);
          containerInfoDs.loadData([headContainer])
          loadDs.loadData([
            ...lineContainerList,
            ...materialLotList,
            ...res.rows.eoList,
          ])
          // 有缓存的容器需要查容器对应下已装载的对象
          await getHasContainer(headContainer.containerId);
        }
      } else if (res?.message)  {
        notification.error({message: res.message});
      }
      setLoading(false);
    });
  }

  // 待装载
  const loadColumns = [
    {
      name: 'identification',
    },
    {
      name: 'qualityStatusDesc',
    },
    {
      name: 'loadObjectType',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'qty',
    },
    {
      name: 'primaryUomCode',
    },
    {
      name: 'soNumSoLinNum',
    },
  ];

  // 已装载
  const unLoadColumns = [
    {
      name: 'identification',
    },
    {
      name: 'qualityStatusDesc',
    },
    {
      name: 'loadObjectType',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'qty',
    },
    {
      name: 'primaryUomCode',
    },
    {
      name: 'soNumSoLinNum',
      render: (record) => (
        <span>{record.get('soNumber')}/{record.get('soLineNum')}</span>
      ),
    },
  ];

  // 获取勾选的待装载对象
  const handleLoadDataSetSelect = () => {
    setSelectedLoadRow(loadDs.selected);
  };

  // 获取勾选的已装载对象
  const handleUnLoadDataSetSelect = () => {
    setSelectedUnLoadRow(unLoadDs.selected);
  };

  // 获取当前容器的已装载对象list
  const getHasContainer = async(containerId) => {
    const params = {
      containerId,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/loaded-object/app`, {
      method: 'GET',
      params: { ...params },
    }).then(async res => {
      if (res && res.success) {
        setLoading(false);
        const hasList = [
          ...res.rows.loadedContainerList,
          ...res.rows.loadedMaterialLotList,
          ...res.rows.loadedEoList,
        ];
        unLoadDs.loadData([...hasList]);
        setLoadedEoList([...res.rows.loadedEoList])
      } else {
        unLoadDs.loadData([]);
        notification.error({message: res.message});
      }
      setLoading(false);
    });
  }

  // 处理混合装载对象的显示
  const getMixedFlag = (containerDetail) => {
    const { mixedMaterialFlag, mixedEoFlag, mixedWoFlag, mixedOwnerFlag } =
      containerDetail;
    let str = "";
    if (mixedMaterialFlag === "N") {
      str = "同物料";
    }
    if (mixedEoFlag === "N") {
      str = `${str  }同执行作业 `;
    }
    if (mixedWoFlag === "N") {
      str = `${str  }同生产指令 `;
    }
    if (mixedOwnerFlag === "N") {
      str = `${str  }同所有权 `;
    }
    if (str === "") {
      str = "无限制";
    }
    return str;
  }

  // 扫描容器
  const handleContainerQueryDs = () => {
    const value = containerQueryDs.current?.get('identification');
    if (value) {
      const params = {
        identification: value,
      };
      setLoading(true);
      containerInfoDs.loadData([]);
      loadDs.loadData([]);
      unLoadDs.loadData([]);
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/detail/app`, {
        method: 'GET',
        params: { ...params },
      }).then(async res => {
        if (res && res.success) {
          setLoading(false);
          const containerInfo = {
            ...res.rows,
            mixedLoadObject: getMixedFlag(res.rows),
          }
          containerInfoDs.loadData([containerInfo]);
          await getHasContainer(containerInfo?.containerId);
        } else {
          notification.error({message: res.message});
        }
        setLoading(false);
      });
    } else {
      containerInfoDs.loadData([]);
      loadDs.queryDataSet.loadData([]);
      loadDs.loadData([]);
      unLoadDs.queryDataSet.loadData([]);
      unLoadDs.loadData([]);
    }
  }

  // 扫描的装载对象在已装载中，做卸载；
  const deleteUnloadItem = async (item) => {
    const params = {
      containerId: containerInfoDs.current?.get('containerId'),
      loadObjectId: item.loadObjectId,
      loadObjectType: item.loadObjectType,
    };
    setLoading(true);
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/unload/app`, {
      method: 'POST',
      body: { ...params },
    }).then(res => {
      if (res.success) {
        notification.success();
        getHasContainer(containerInfoDs.current?.get('containerId'));
      } else if (res.message) {
        notification.error({ message: res.message });
      }
      setLoading(false);
    });
  }

  // 处理待装载对象的list
  const handleData = (list) => {
    const arr = [];
    list.forEach((i) => {
      arr.push({
        loadObjectId: i.loadObjectId,
        loadObjectType: i.loadObjectType,
        qty: i.loadObjectType === "EO" ? i.scanQty : "",
      });
    });
    return arr;
  }

  // 扫描待装载对象
  const operateBarCode = async (type, para) => {
    const barCode = type === 'UNLOAD' ? unLoadDs.queryDataSet.current?.get('identification') : loadDs.queryDataSet.current?.get('identification');
    const loadingList = loadDs.toData();
    const params = {
      containerId: containerInfoDs.current?.get('containerId'),
      identification: barCode,
      scannedLoadObjectList: handleData(loadingList),
      unloadFlag: para ? "Y" : unloadFlag,
      containerIdentification: containerInfoDs.current?.get('identification'),
    };
    setLoading(true);
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/unloaded-object/app`, {
      method: 'POST',
      body: { ...params },
    }).then(res => {
      if (res.success) {
        notification.success();
        const resData = res.rows;
        const loadObjectIdList = [];
        loadingList.forEach((i) => {
          loadObjectIdList.push(i.loadObjectId);
        });
        if (loadObjectIdList.indexOf(resData.loadObjectId) === -1) {
          if (resData.objectType === "MATERIAL_LOT") {
            loadingList.unshift(resData.materialLot);
            loadDs.loadData([...loadingList]);
          }
          if (resData.objectType === "CONTAINER") {
            loadingList.unshift(resData.container);
            loadDs.loadData([...loadingList]);
          }
          if (resData.eo) {
            loadingList.unshift(resData.eo);
            loadDs.loadData([...loadingList]);
          }
        }
      }  else if (
        res.statusCode === "MATERIAL_LOT_UNBIND_CONTAINER" ||
        res.statusCode === "CONTAINER_UNBIND_CONTAINER"
      ) {
        Modal.confirm({
          children: res.message,
          onOk: () => handleQueryMaterialLot(type, res.statusCode),
          onClose: () => setLoading(false),
        });
      } else if (res.message) {
        notification.error({ message: res.message });
      }
      setLoading(false);
    });
  }

  // 扫描装载对象
  const handleQueryMaterialLot = async (type, para) => {
    const barCode = type === 'UNLOAD' ? unLoadDs.queryDataSet.current?.get('identification') : loadDs.queryDataSet.current?.get('identification');
    const hasList = unLoadDs.toData();
    if (barCode) {
      if (hasList.length) {
        const hasIdentificationArr = [];
        hasList.forEach((i) => {
          hasIdentificationArr.push(i.identification);
        });
        let status = true;
        loadedEoList.forEach((i) => {
          if (i.identification === barCode.trim()) {
            if (i.executedqty < i.qty) {
              status = false;
            }
          }
        });
        // 若存在于已装载列表里
        if (
          hasIdentificationArr.indexOf(barCode) !== -1 &&
          status
        ) {
          let item = {};
          hasList.forEach((i) => {
            if (i.identification === barCode) {
              // setOperateItem({...i})
              item = {...i};
            }
          });

          if (originAlways) {
            await deleteUnloadItem(item);
          } else {
            Modal.confirm({
              children: intl
                .get('unload.tips')
                .d('已装载，是否卸载？'),
              onOk: () => deleteUnloadItem(item),
            });
          }
        } else {
          await operateBarCode(type, para);
        }
      } else {
        await operateBarCode(type, para);
      }
    }
  }

  // 已装载查询
  const renderUnloadQueryBar = ({ queryDataSet }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Col span={24}>
            <Form columns={2} dataSet={queryDataSet} labelWidth={110}>
              <TextField name="identification" onEnterDown={() => handleQueryMaterialLot('UNLOAD')} />
            </Form>
          </Col>
        </Row>
      );
    }
    return null;
  };

  // 待装载查询
  const renderLoadQueryBar = ({ queryDataSet }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Col span={24}>
            <Form columns={2} dataSet={queryDataSet} labelWidth={110}>
              <TextField name="identification" onEnterDown={() => handleQueryMaterialLot('LOAD')} />
            </Form>
          </Col>
        </Row>
      );
    }
    return null;
  };

  const delBarcodes = async(identificationList) => {
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-scan-cache/barcode/cache/del`, {
      method: 'POST',
      body: identificationList,
    }).then(async res => {
      if (!(res && res.success)) {
        notification.error({message: res.message});
      }
    });
  }

  // 对待装载的数据执行装载
  const handleLoad = () => {
    let identificationList = [containerInfoDs.current?.get('identification')];
    const loadingList = loadDs.toData();
    const containerLoadList = [];
    loadingList.forEach((i) => {
      containerLoadList.push({
        currentContainerId: i.currentContainerId,
        loadObjectId: i.loadObjectId,
        loadObjectType: i.loadObjectType,
        unloadFlag: i.unloadFlag,
      });
      if (i.loadObjectType === "CONTAINER") {
        identificationList = [...identificationList, i.identification];
        if (i.identificationList?.length) {
          identificationList = [
            ...identificationList,
            ...i.identificationList,
          ];
        }
      } else if (i.loadObjectType === "MATERIAL_LOT") {
        identificationList = [...identificationList, i.identification];
      } else if (i.loadObjectType === "EO") {
        identificationList = [...identificationList, i.identification];
      }
    });
    const params = {
      containerId: containerInfoDs.current?.get('containerId'),
      identificationList,
      containerLoadList,
    };
    setLoading(true);
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/batch/load/app`, {
      method: 'POST',
      body: { ...params },
    }).then(async res => {
      if (res.success) {
        await delBarcodes(identificationList);
        await getHasContainer(containerInfoDs.current?.get('containerId'))
        notification.success();
        loadDs.loadData([]);
      } else if (res.message) {
        notification.error({message: res.message});
      }
      setLoading(false);
    });
  };

  // 对已装载的数据进行卸载
  const handleUnload = async () => {
    setLoading(true);
    if (!selectedUnLoadRow.length) {
      // 一键卸载
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/all-object/unload/app`, {
        method: 'POST',
        body: containerInfoDs.current?.get('containerId'),
      }).then(res => {
        if (res.success) {
          notification.success();
          getHasContainer(containerInfoDs.current?.get('containerId'))
        } else if (res.message) {
          notification.error({ message: res.message });
        }
      });
    } else {
      await deleteUnloadItem(selectedUnLoadRow[0].toData());
    }
    setLoading(false);
  };

  const handleDeleteLoading = () => {
    const deleteList = selectedLoadRow.map((ele) => ele.toData());
    const deleteObj = selectedLoadRow.map((ele) => ele.get('identification'));
    Modal.confirm({
      children: `确认删除${deleteObj.join(',')}?`,
      onOk: () => {
        // 1.删除列表
        const newList = loadDs.toData().filter((i) => !deleteObj.includes(i.identification));
        loadDs.loadData([...newList]);
        // 2.删除缓存
        let deleteItem = [];
        if (!newList?.length) {
          deleteItem.push(containerInfoDs.current.get('identification'));
        }
        deleteList.forEach((item) => {
          if (item.loadObjectType === "CONTAINER") {
            deleteItem.push(item.identification);
            if (item.identificationList?.length) {
              deleteItem = [...deleteItem, ...item.identificationList];
            }
          } else {
            deleteItem.push(item.identification);
          }
        })
        setSelectedLoadRow([]);
        delBarcodes(deleteItem);
      },
      onClose: () => {},
    });
  }

  const clearCache = async () => {
    let identificationList = [containerInfoDs.current?.get('identification')];
    const loadingList = loadDs.toData();
    (loadingList || []).forEach((i) => {
      if (i.loadObjectType === "CONTAINER") {
        identificationList = [...identificationList, i.identification];
        if (i.identificationList?.length) {
          identificationList = [
            ...identificationList,
            ...i.identificationList,
          ];
        }
      } else if (i.loadObjectType === "MATERIAL_LOT") {
        identificationList = [...identificationList, i.identification];
      } else {
        identificationList = [...identificationList, i.identification];
      }
    });
    await delBarcodes(identificationList);
    await handleContainerQueryDs();
  }

  return (
    <React.Fragment>
      <Header title={intl.get(`${modelPrompt}.title`).d('容器装卸-MES')} >
        <Button
          onClick={() => clearCache()}
          style={{
            float: "right",
            marginRight: 15,
          }}
          color="primary"
          disabled={!loadButton}
        >
          {intl.get(`${modelPrompt}.button.delete`).d('删除')}
        </Button>
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['ContainerInfo', 'ContainerLoad', 'ContainerUnload']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.containerInfo`).d('容器信息')}
            key="ContainerInfo"
          >
            <Form
              dataSet={containerQueryDs}
              columns={4}
              labelLayout="horizontal"
              labelWidth={110}
              style={{
                marginBottom: '1vw',
              }}
            >
              <TextField name="identification" onEnterDown={() => handleContainerQueryDs()} />
            </Form>
            <Form
              dataSet={containerInfoDs}
              columns={4}
              labelLayout="horizontal"
              labelWidth={110}
            >
              <TextField disabled name="locatorCode" />
              <TextField disabled name="packingLevelDesc" />
              <TextField disabled name="mixedLoadObject" />
              <TextField disabled name="capacityQty" />
              <TextField disabled name="maxLoadWeight" />
              <TextField disabled name="currentContainerIdentification" />
              <TextField disabled name="topContainerIdentification" />
            </Form>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.line.ContainerLoad`).d('待装载')}
            key="ContainerLoad"
            dataSet={loadDs}
            style={{
              width: '46%',
              position: 'absolute',
              left: '2%',
              height: '65%',
            }}
          >
            <Spin spinning={loading}>
              <Table
                searchCode="ContainerLoad"
                customizedCode="ContainerLoad"
                queryBar={renderLoadQueryBar}
                dataSet={loadDs}
                columns={loadColumns}
                style={{ height: TableHeight }}
              />
            </Spin>
            <Button
              onClick={handleLoad}
              style={{
                float: "right",
                marginRight: 15,
              }}
              color="primary"
              disabled={!loadButton}
            >
              {intl.get(`${modelPrompt}.button.load`).d('装载')}
            </Button>
            <Button
              onClick={() => handleDeleteLoading()}
              style={{
                float: "right",
                marginRight: 15,
              }}
              color="primary"
              disabled={!selectedLoadRow.length}
            >
              {intl.get(`${modelPrompt}.button.delete`).d('删除')}
            </Button>
          </Panel>
          <Panel
            style={{
              width: '46%',
              position: 'absolute',
              right: '2%',
              height: '65%',
            }}
            header={intl.get(`${modelPrompt}.line.ContainerUnload`).d('已装载')}
            key="ContainerUnload"
            dataSet={unLoadDs}
          >
            <Spin spinning={loading}>
              <Table
                searchCode="ContainerUnload"
                customizedCode="ContainerUnload"
                queryBar={renderUnloadQueryBar}
                dataSet={unLoadDs}
                columns={unLoadColumns}
                style={{ height: TableHeight }}
              />
            </Spin>
            <Button
              onClick={handleUnload}
              style={{
                float: "right",
                marginRight: 15,
              }}
              color="primary"
              disabled={!unLoadButton}
            >
              {intl.get(`${modelPrompt}.button.unLoad`).d('卸载')}
            </Button>
          </Panel>
        </Collapse>
      </Content>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.ContainerLoadAndUnloadMes', 'tarzan.common'],
})(ContainerLoadAndUnloadMes);
