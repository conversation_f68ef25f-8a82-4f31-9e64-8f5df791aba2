/**
 * @Description: 检验平台-检验对象明细
 * @Author: <<EMAIL>>
 * @Date: 2023-02-14 09:12:16
 * @LastEditTime: 2023-05-18 17:04:38
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import intl from 'utils/intl';
import {Select, Table} from 'choerodon-ui/pro';
import { Badge, Tag } from 'choerodon-ui';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC } from '@utils/config';
import NcRecordComponent from './NcRecordComponent';

const modelPrompt = 'tarzan.qms.inspectionPlatform';

const InspectObjComponent = props => {
  const { canEdit, inspectObjDS, customizeTable, inspectInfoDS } = props;

  // 质量状态变更
  const handleChangeQualityStatus = (newValue, oldValue, record) => {
    if (newValue !== oldValue) {
      const _qty = Number(record.get('qty') || 0);
      if (newValue === 'OK') {
        record.set('ngQty', 0);
        record.set('scrapQty', 0);
        record.set('okQty', _qty);
      } else if (newValue === 'NG') {
        record.set('ngQty', _qty);
        record.set('scrapQty', 0);
        record.set('okQty', 0);
      }
    }
  };

  const columns: ColumnProps[] = [
    {
      name: 'sourceObjectTypeDesc',
      width: 160,
      hidden: !!inspectObjDS.getState('matOrLotFlag'),
    },
    {
      name: 'sourceObjectCode',
      width: 160,
      hidden: !!inspectObjDS.getState('matOrLotFlag'),
    },
    {
      name: 'qty',
    },
    {
      name: 'uomName',
    },
    {
      name: 'qualityStatus',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, text, record }) => {
        if (!record) {
          return '';
        }
        const _canEdit = canEdit &&
          record &&
          record.get('inspectFlag') === 'Y' &&
          ['RELEASED', 'INSPECTING', 'REINSPECTING'].includes(record.get('inspectDocStatus'));
        if (_canEdit) {
          return (
            <Select
              record={record}
              name="qualityStatus"
              optionsFilter={optRecord => ['OK', 'NG'].includes(optRecord?.get('statusCode'))}
              onChange={(newValue, oldValue) => handleChangeQualityStatus(newValue, oldValue, record)}
              style={{
                verticalAlign: 'baseline',
                backgroundColor:
                  value === 'OK'
                    ? 'rgb(230, 255, 234)'
                    : value === 'NG'
                      ? 'rgb(255, 240, 240)'
                      : '',
              }}
            />
          );
        }
        return text ? (
          <Tag
            color={
              value === 'OK'
                ? 'green'
                : value === 'NG'
                  ? 'red'
                  : 'blue'
            }
          >
            {text}
          </Tag>
        ) : text;
      },
    },
    {
      name: 'ngQty',
      editor: record =>
        canEdit &&
        record &&
        record.get('inspectFlag') === 'Y' &&
        ['RELEASED', 'INSPECTING', 'REINSPECTING'].includes(record.get('inspectDocStatus')),
    },
    {
      name: 'scrapQty',
      editor: record =>
        canEdit &&
        record &&
        record.get('inspectFlag') === 'Y' &&
        ['RELEASED', 'INSPECTING', 'REINSPECTING'].includes(record.get('inspectDocStatus')),
    },
    {
      name: 'okQty',
    },
    {
      name: 'inspectFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'supplierLot',
    },
    {
      name: 'lot',
    },
    {
      name: 'locatorName',
    },
    {
      name: 'option',
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      title: intl.get(`${modelPrompt}.title.option`).d('操作'),
      renderer: ({record}) => {
        return (<NcRecordComponent
          canEdit={canEdit}
          type="text"
          disabled={
            inspectInfoDS.current?.get('inspectTaskStatus') !== 'INSPECTING' ||
            record?.get('qualityStatus') !== 'NG' ||
            inspectInfoDS.current?.get('inspectNcRecordDimension') !== 'OBJECT_NC'
          }
          customizeTable={customizeTable}
          queryParams={{
            inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
            inspectDocId: inspectInfoDS.current?.get('inspectDocId'),
            inspectNcRecordDimension: inspectInfoDS.current?.get('inspectNcRecordDimension'),
            inspectObjectId : record?.get('inspectObjectId'),

          }}
          style={{ verticalAlign: 'top', padding: '0px 8px' }}
        />)
      },
    },
  ];

  return (
    <>
      {customizeTable(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_OBJECT.LIST`,
        },
        <Table
          dataSet={inspectObjDS}
          columns={columns}
          customizedCode="jyptdo"
        />,
      )}
    </>
  );
};

export default InspectObjComponent;
