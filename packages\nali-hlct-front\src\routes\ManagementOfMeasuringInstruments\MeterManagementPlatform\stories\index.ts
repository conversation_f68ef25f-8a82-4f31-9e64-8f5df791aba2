/**
 * @Description: 检验项目组维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-01-10 16:54:12
 * @LastEditTime: 2023-05-18 16:49:50
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.meterManagementPlatform';
const tenantId = getCurrentOrganizationId();

// 量具汇总
const allTabledDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'verificationId',
  queryFields: [
    {
      name: 'categoryCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryCode`).d('类别描述'),
      lookupCode: 'QIS.MS_TOOL_CTGR',
    },
    {
      name: 'speciesLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.speciesCode`).d('种别描述'),
      lovCode: 'YP.QMS_YP_TYPE_TEST',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'speciesCode',
      bind: 'speciesLov.speciesCode',
    },
    {
      name: 'speciesDesc',
      bind: 'speciesLov.speciesName',
    },
    {
      name: 'modelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toolModelCode`).d('型号编码'),
      lovCode: 'YP.QMS_TOOL_MODEL',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'modelId',
      type: FieldType.number,
      bind: 'modelLov.toolModelId',
    },
    {
      name: 'usingStatusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingStatus`).d('使用状态'),
      lookupCode: 'QIS.MS_TOOL_USING_STATUS',
      lovPara: { tenantId },
      multiple: true,
      defaultValue: [
        'PREPARE_FOR_INSPECT',
        'USING',
        'PREPARE_FOR_RECEIVE',
        'INSPECTING',
        'PENDING',
        'SERVICING',
      ],
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('检定状态'),
      lookupCode: 'QIS.MS_TOOL_VRFCT_STATUS',
    },
    {
      name: 'responsibleUserObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUser`).d('责任人'),
      lovCode: 'HIAM.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'realName',
    },
    {
      name: 'responId',
      type: FieldType.number,
      bind: 'responsibleUserObj.id',
    },
    {
      name: 'responName',
      type: FieldType.string,
      bind: 'responsibleUserObj.realName',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
    },
    {
      name: 'categoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryDesc`).d('类别描述'),
    },
    {
      name: 'speciesDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesDesc`).d('种别描述'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
    },
    {
      name: 'usingStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingStatus`).d('使用状态'),
    },
    {
      name: 'verificationStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('检定状态'),
    },
    {
      name: 'responName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleUser`).d('责任人'),
    },
    {
      name: 'range',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.range`).d('量程'),
    },
    {
      name: 'resolution',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.resolution`).d('分辨力'),
    },
    {
      name: 'manageCount',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.manageCount`).d('数量'),
    },
    {
      name: 'verificationPeriod',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检定周期（月）'),
    },
    {
      name: 'verificationMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/ms-tool-manages/group-list/ui`,
        method: 'GET',
      };
    },
  },
});

// 量具明细
const detailTabledDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'msToolManageId',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      bind: 'siteLov.siteName',
    },
    {
      name: 'toolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编号'),
    },
    {
      name: 'categoryCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryCode`).d('类别描述'),
      lookupCode: 'QIS.MS_TOOL_CTGR',
    },
    {
      name: 'speciesLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.speciesCode`).d('种别描述'),
      lovCode: 'YP.QMS_YP_TYPE_TEST',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'speciesCode',
      bind: 'speciesLov.speciesCode',
    },
    {
      name: 'speciesDesc',
      bind: 'speciesLov.speciesName',
    },
    {
      name: 'modelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
      lovCode: 'YP.QMS_TOOL_MODEL',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'modelId',
      bind: 'modelLov.toolModelId',
    },
    {
      name: 'modelCode',
      bind: 'modelLov.modelCode',
    },
    {
      name: 'usingStatusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingStatus`).d('使用状态'),
      lookupCode: 'QIS.MS_TOOL_USING_STATUS',
      lovPara: { tenantId },
      multiple: true,
      defaultValue: [
        'PREPARE_FOR_INSPECT',
        'USING',
        'PREPARE_FOR_RECEIVE',
        'INSPECTING',
        'PENDING',
        'SERVICING',
      ],
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('检定状态'),
      lookupCode: 'QIS.MS_TOOL_VRFCT_STATUS',
    },
    {
      name: 'userObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.userName`).d('使用人'),
      lovCode: 'LOV_EMPLOYEE',
      multiple: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'name',
    },
    {
      name: 'userId',
      type: FieldType.string,
      multiple: ',',
      bind: 'userObj.employeeId',
    },
    {
      name: 'userName',
      type: FieldType.string,
      multiple: ',',
      bind: 'userObj.name',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('使用部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'usingDepartmentId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'usingDepartmentDesc',
      bind: 'departmentLov.unitName',
      type: FieldType.string,
    },
    {
      name: 'verificationMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
      lookupCode: 'QIS.MS_VRFCT_METHOD',
    },
    {
      name: 'verificationAgency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationAgency`).d('检验机构'),
      lookupCode: 'YP.QIS.MS_INSPECT_PLACE',
      lovPara: { tenantId },
    },
    {
      name: 'responsibleUserObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUser`).d('责任人'),
      lovCode: 'HIAM.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'realName',
    },
    {
      name: 'responId',
      type: FieldType.number,
      bind: 'responsibleUserObj.id',
    },
    {
      name: 'responName',
      type: FieldType.string,
      bind: 'responsibleUserObj.realName',
    },
    {
      name: 'prodObj',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      lovCode: 'MT.MODEL.PRODLINE',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      textField: 'prodLineName',
      valueField: 'prodLineId',
      lovPara: { tenantId },
    },
    {
      name: 'prodLineId',
      bind: 'prodObj.prodLineId',
      type: FieldType.string,
    },
    {
      name: 'prodLineName',
      bind: 'prodObj.prodLineName',
      type: FieldType.string,
    },
    {
      name: 'processFromLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processName`).d('工序名称'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'processId',
      bind: 'processFromLov.workcellId',
    },
    {
      name: 'processName',
      bind: 'processFromLov.workcellName',
    },
    {
      name: 'manufacturer',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manufacturer`).d('制造商'),
    },
    {
      name: 'otherPosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherPosition`).d('其他位置信息'),
      lookupCode: 'YP.QIS.MANAGE_OTHER_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'lastVerificationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastVerificationDateFrom`).d('上次检定日期从'),
      max: 'lastVerificationDateTo',
    },
    {
      name: 'lastVerificationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastVerificationDateTo`).d('上次检定日期至'),
      min: 'lastVerificationDateFrom',
    },
    {
      name: 'verificationExpDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.verificationExpDateFrom`).d('检定过期日期从'),
      max: 'verificationExpDateTo',
    },
    {
      name: 'verificationExpDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.verificationExpDateTo`).d('检定过期日期至'),
      min: 'verificationExpDateFrom',
    },
    {
      name: 'manufacturingNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manufacturingNum`).d('出厂编号'),
    },
    {
      name: 'lastVerificationResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastVerificationResult`).d('上次检定结果'),
      lookupCode: 'QIS.MS_TOOL_VRFCT_RESULT',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFroms`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTos`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'createPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createBy`).d('创建人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
    },
    {
      name: 'createBy',
      bind: 'createPersonLov.id',
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      required: true,
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      bind: 'siteLov.siteName',
      disabled: true,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
    },
    {
      name: 'toolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编号'),
      disabled: true,
    },
    {
      name: 'categoryCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryDesc`).d('类别描述'),
      lookupCode: 'QIS.MS_TOOL_CTGR',
      bind: 'modelLov.categoryCode',
      disabled: true,
    },
    {
      name: 'speciesLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.speciesDesc`).d('种别描述'),
      lovCode: 'YP.QMS_YP_TYPE_TEST',
      ignore: FieldIgnore.always,
      required: true,
      disabled: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'speciesCode',
      type: FieldType.string,
      bind: 'speciesLov.speciesCode',
    },
    {
      name: 'speciesDesc',
      bind: 'speciesLov.speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesDesc`).d('种别描述'),
    },
    {
      name: 'modelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
      lovCode: 'YP.QMS_TOOL_MODEL',
      ignore: FieldIgnore.always,
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'modelId',
      type: FieldType.number,
      bind: 'modelLov.toolModelId',
    },
    {
      name: 'modelCode',
      bind: 'modelLov.modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
    },
    {
      name: 'range',
      bind: 'modelLov.modelRange',
      label: intl.get(`${modelPrompt}.range`).d('量程'),
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'resolution',
      bind: 'modelLov.resolution',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.resolution`).d('分辨力'),
      disabled: true,
    },
    {
      name: 'assetNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assetNum`).d('固定资产编号'),
      maxLength: 100,
    },
    {
      name: 'userObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.userName`).d('使用人'),
      lovCode: 'LOV_EMPLOYEE',
      ignore: FieldIgnore.always,
      multiple: true,
      lovPara: {
        tenantId,
      },
      textField: 'name',
    },
    {
      name: 'userId',
      multiple: ',',
      // transformResponse:(value)=>value?.split(',').filter(Boolean),
      bind: 'userObj.employeeId',
    },
    {
      name: 'userNum',
      multiple: ',',
      // transformResponse:(value)=>value?.split(',').filter(Boolean),
      bind: 'userObj.employeeNum',
    },
    {
      name: 'userName',
      multiple: ',',
      // transformResponse:(value)=>value?.split(',').filter(Boolean),
      label: intl.get(`${modelPrompt}.userName`).d('使用人'),
      bind: 'userObj.name',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('使用部门'),
      required: true,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'usingDepartmentId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'usingDepartmentDesc',
      bind: 'departmentLov.unitName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingDepartment`).d('使用部门'),
    },
    {
      name: 'responsibleUserObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUser`).d('责任人'),
      lovCode: 'HIAM.USER.ORG',
      ignore: FieldIgnore.always,
      required: true,
      lovPara: {
        tenantId,
      },
      textField: 'realName',
    },
    {
      name: 'responId',
      type: FieldType.number,
      bind: 'responsibleUserObj.id',
    },
    {
      name: 'responName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleUser`).d('责任人'),
      bind: 'responsibleUserObj.realName',
    },
    {
      name: 'prodObj',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      lovCode: 'MT.MODEL.PRODLINE',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      textField: 'prodLineName',
      valueField: 'prodLineId',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => !record?.get('otherPosition'),
        disabled: ({ record }) => record?.get('otherPosition'),
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodObj.prodLineId',
      type: FieldType.string,
    },
    {
      name: 'prodLineName',
      bind: 'prodObj.prodLineName',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      type: FieldType.string,
    },
    {
      name: 'processFromLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processName`).d('工序名称'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => !record?.get('otherPosition'),
        disabled: ({ record }) => record?.get('otherPosition'),
      },
    },
    {
      name: 'processId',
      bind: 'processFromLov.workcellId',
    },
    {
      name: 'processName',
      bind: 'processFromLov.workcellName',
      label: intl.get(`${modelPrompt}.processName`).d('工序名称'),
    },
    {
      name: 'otherPosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherPosition`).d('其他位置信息'),
      lookupCode: 'YP.QIS.MANAGE_OTHER_POSITION',
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record }) => record?.get('prodLineId') || record?.get('processId'),
        required: ({ record }) => !record?.get('prodLineId') && !record?.get('processId'),
      },
    },
    {
      name: 'manufacturingNum',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.manufacturingNum`).d('出厂编号'),
    },
    {
      name: 'manufacturer',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.manufacturer`).d('制造商'),
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('检定状态'),
      lookupCode: 'QIS.MS_TOOL_VRFCT_STATUS',
    },
    {
      name: 'verificationMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
      lookupCode: 'QIS.MS_VRFCT_METHOD',
      required: true,
    },
    {
      name: 'reminderPeriod',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.reminderPeriod`).d('检定提醒周期（天）'),
      min: 1,
      required: true,
    },
    {
      name: 'verificationAgency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationAgency`).d('检定/校准机构'),
      lookupCode: 'YP.QIS.MS_INSPECT_PLACE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'verificationPeriod',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检定周期（月）'),
      min: 1,
      required: true,
    },
    {
      name: 'lastVerificationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastVerificationDate`).d('上次检定日期'),
    },
    {
      name: 'lastVerificationResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastVerificationResult`).d('上次检定结果'),
    },
    {
      name: 'verificationExpDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationExpDate`).d('检定过期日期'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedBy`).d('最后更新人'),
    },
    {
      name: 'scrappedDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrappedDate`).d('报废日期'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'usingStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingStatus`).d('使用状态'),
      lookupCode: 'QIS.MS_TOOL_USING_STATUS',
      required: true,
    },
    {
      name: 'reportCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reportCode`).d('报告编号'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('检定报告'),
      bucketName: 'qms',
    },
  ],
  transport: {
    read: () => {
      return {
        // url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/list/ui`,
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/ms-tool-manages/list/ui`,
        method: 'GET',
        // transformResponse: val => {
        //   const { content, ...others } = JSON.parse(val);
        //   console.log("🚀 ~ file: index.ts:793 ~ JSON.parse(val):", JSON.parse(val))
        //   return {
        //     ...others,
        //     content: content?.map((item) => {
        //       const { userName, userNum } = item;
        //       const _userName = userName.split(',').filter((i) => i).join(',');
        //       console.log("🚀 ~ file: index.ts:799 ~ content:content?.map ~ _userName:", _userName)
        //       const _userNum = userNum.split(',').filter((i) => i).join(',');
        //       console.log("🚀 ~ file: index.ts:801 ~ content:content?.map ~ _userNum:", _userNum)
        //       return {
        //         ...item,
        //         userName: _userName,
        //         userNum: _userNum,
        //       }
        //     }),
        //   };
        // },
      };
    },
    submit: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/ms-tool-manages/update/ui`,
        method: 'POST',
        data,
      };
    },
  },
});

// 履历查看
const curriculumDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'verificationId',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'operationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationType`).d('履历事件'),
      lookupCode: 'QIS.MS_TOOL_MANAGE_HIS_TYPE',
    },
    {
      name: 'usingStatusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingStatus`).d('使用状态'),
      lookupCode: 'QIS.MS_TOOL_USING_STATUS',
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('检定状态'),
      lookupCode: 'QIS.MS_TOOL_VRFCT_STATUS',
    },
    {
      name: 'operationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('操作人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'realName',
    },
    {
      name: 'operatioId',
      type: FieldType.string,
      bind: 'operationObj.id',
    },
    {
      name: 'operatioName',
      type: FieldType.string,
      bind: 'operationObj.realName',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('操作时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('操作时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'userObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.userName`).d('使用人'),
      lovCode: 'LOV_EMPLOYEE',
      multiple: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'name',
    },
    {
      name: 'userId',
      type: FieldType.string,
      multiple: ',',
      bind: 'userObj.employeeId',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('使用部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'usingDepartmentId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'usingDepartmentDesc',
      bind: 'departmentLov.unitName',
      type: FieldType.string,
    },
    {
      name: 'responsibleUserObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUserObj`).d('责任人'),
      lovCode: 'HIAM.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'realName',
    },
    {
      name: 'responId',
      type: FieldType.number,
      bind: 'responsibleUserObj.id',
    },
    {
      name: 'responName',
      type: FieldType.string,
      bind: 'responsibleUserObj.realName',
    },
    {
      name: 'verificationMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
      lookupCode: 'QIS.MS_VRFCT_METHOD',
    },
    {
      name: 'verificationAgency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationAgency`).d('检验机构'),
      lookupCode: 'YP.QIS.MS_INSPECT_PLACE',
      lovPara: { tenantId },
    },
    {
      name: 'prodObj',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      lovCode: 'MT.MODEL.PRODLINE',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      textField: 'prodLineName',
      valueField: 'prodLineId',
      lovPara: { tenantId },
    },
    {
      name: 'prodLineId',
      bind: 'prodObj.prodLineId',
      type: FieldType.string,
    },
    {
      name: 'prodLineName',
      bind: 'prodObj.prodLineName',
      type: FieldType.string,
    },
    {
      name: 'processFromLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processName`).d('工序名称'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'processId',
      bind: 'processFromLov.workcellId',
    },
    {
      name: 'processName',
      bind: 'processFromLov.workcellName',
    },
    {
      name: 'otherPosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherPosition`).d('其他位置信息'),
      lookupCode: 'YP.QIS.MANAGE_OTHER_POSITION',
      lovPara: { tenantId },
    },
  ],
  fields: [
    {
      name: 'toolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编号'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
    },
    {
      name: 'categoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryDesc`).d('类别描述'),
    },
    {
      name: 'speciesDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesDesc`).d('种别描述'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
    },
    {
      name: 'range',
      label: intl.get(`${modelPrompt}.range`).d('量程'),
      type: FieldType.string,
    },
    {
      name: 'resolution',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.resolution`).d('分辨力'),
    },
    {
      name: 'assetNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assetNum`).d('固定资产编号'),
    },
    {
      name: 'usingStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingStatus`).d('使用状态'),
    },
    {
      name: 'userName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userName`).d('使用人'),
    },
    {
      name: 'usingDepartmentDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.usingDepartment`).d('使用部门'),
    },
    {
      name: 'responName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responName`).d('责任人'),
    },
    {
      name: 'prodLineName',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      type: FieldType.string,
    },
    {
      name: 'processName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processName`).d('工序名称'),
    },
    {
      name: 'otherPosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherPosition`).d('其他位置信息'),
      lookupCode: 'YP.QIS.MANAGE_OTHER_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'manufacturingNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manufacturingNum`).d('出厂编号'),
    },
    {
      name: 'manufacturer',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manufacturer`).d('制造商'),
    },
    // {
    //   name: 'verStatus',
    //   type: FieldType.string,
    //   label: '检定状态',
    //   // lookupCode: 'QIS.MS_TOOL_VRFCT_STATUS',
    // },
    {
      name: 'verificationMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationMethod`).d('校准类别'),
    },
    {
      name: 'reminderPeriod',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.reminderPeriod`).d('检定提醒周期（天）'),
    },
    {
      name: 'verificationAgency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationAgency`).d('检定/校准机构'),
      lookupCode: 'YP.QIS.MS_INSPECT_PLACE',
      lovPara: { tenantId },
    },
    {
      name: 'verificationPeriod',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检定周期（月）'),
    },
    {
      name: 'operationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationType`).d('履历事件'),
      lookupCode: 'QIS.MS_TOOL_MANAGE_HIS_TYPE',
    },
    {
      name: 'lastVerificationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastVerificationDate`).d('上次检定日期'),
    },
    {
      name: 'createdByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('操作人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('操作时间'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'sourceDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceDocType`).d('业务单据类型'),
      lookupCode: 'YP.QIS.MS_SOURCE_DOC_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'sourceDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceDocNum`).d('业务单据'),
    },
    {
      name: 'reportCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reportCode`).d('报告编号'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('检定报告'),
      bucketName: 'qms',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/ms-tool-manages/his-list/ui`,
        method: 'GET',
      };
    },
  },
});

export { allTabledDS, detailTabledDS, curriculumDS };
