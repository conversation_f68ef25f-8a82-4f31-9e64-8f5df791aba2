/**
 * @Description: SA/专项检证平台-详情界面DS
 * @Author: <EMAIL>
 * @Date: 2023/8/7 16:01
 */
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import moment from 'moment';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();
const modelPrompt = 'tarzan.inspectExecute.saVerificationPlatform';

const detailDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'verificationId',
  forceValidate: true,
  // dataToJSON: DataToJSON.all,
  fields: [
    {
      name: 'verificationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationCode`).d('检证项目编码'),
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'siteName',
      lovPara: { tenantId },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('状态'),
      lookupCode: 'YP.QIS.SA_VERIFICATION_STATUS',
      lovPara: { tenantId },
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.PROJECT_NAME',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'verificationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationType`).d('检证类型'),
      lookupCode: 'YP.QIS.VERIFICATION_TYPE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'verificationFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationFrom`).d('检证来源'),
      lookupCode: 'YP.QIS.VERIFICATION_FROM',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检证阶段'),
      lookupCode: 'YP.QIS.VERIFICATION_PERIOD',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
      lookupCode: 'YP.QIS.PRODUCT_TYPE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialCode',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      disabled: true,
      bind: 'materialLov.materialName',
    },
    {
      name: 'itemGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemGroup`).d('产品对象'),
      disabled: true,
      bind: 'materialLov.itemGroupDesc',
    },
    {
      name: 'failureMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureMode`).d('失效模式'),
      lookupCode: 'YP.QIS.FAILURE_MODE',
      lovPara: { tenantId },
    },
    {
      name: 'applyArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyArea`).d('运用业务'),
      lookupCode: 'YP.QIS.APPLY_AREA',
      lovPara: { tenantId },
      multiple: ',',
    },
    {
      name: 'applicableProductLine',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicableProductLine`).d('适用产品线'),
      lookupCode: 'YP.QIS.APPLICABLE_PRODUCT_LINE',
      lovPara: { tenantId },
      multiple: ',',
    },
    {
      name: 'applicableChemicalSystem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicableChemicalSystem`).d('适用化学体系'),
      lookupCode: 'YP.QIS.APPLICABLE_CHEMICAL_SYSTEM',
      lovPara: { tenantId },
      multiple: ',',
    },
    {
      name: 'applicableProductStructure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicableProductStructure`).d('适用产品结构'),
      lookupCode: 'YP.QIS.APPLICABLE_PRODUCT_STRUCTURE',
      lovPara: { tenantId },
      multiple: ',',
    },
    {
      name: 'createMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('创建方式'),
      lookupCode: 'YP.QIS.VERIFICATION_CREATE_METHOD',
      lovPara: { tenantId },
      defaultValue: 'MANUAL',
      disabled: true,
    },
    {
      name: 'sourceTempLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceTempCode`).d('检证模板编码'),
      lovCode: 'YP.QIS.VERIFICATION_TEMP_CODE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'sourceTempId',
      bind: 'sourceTempLov.verificationId',
    },
    {
      name: 'sourceTempCode',
      bind: 'sourceTempLov.verificationCode',
    },
    {
      name: 'createPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createPerson`).d('创建人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      disabled: true,
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      bind: 'createPersonLov.id',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      bind: 'createPersonLov.realName',
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
      disabled: true,
    },

    {
      name: 'problemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemCode`).d('关联问题编号'),
      lovCode: 'YP.QIS.PROBLEM_LIST',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'problemId',
      bind: 'problemLov.problemId',
    },
    {
      name: 'problemCode',
      bind: 'problemLov.problemCode',
    },
    {
      name: 'fromProcessLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromProcessCode`).d('问题归属工序'),
      lovCode: 'MT.MODEL.WORKCELL_SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      dynamicProps: {
        // disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({
          tenantId,
          siteIds: [record?.get('siteId')],
        }),
      },
    },
    {
      name: 'fromProcessId',
      bind: 'fromProcessLov.workcellId',
    },
    {
      name: 'fromProcessName',
      bind: 'fromProcessLov.workcellName',
    },
    {
      name: 'responsibleArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleArea`).d('问题责任领域'),
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      lovPara: { tenantId },
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDescription`).d('问题描述'),
    },
    {
      name: 'verificationUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.verificationUuid`).d('相关照片'),
      bucketName: 'qms',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-platform/info/ui`,
        method: 'GET',
      };
    },
  },
});

const problemReasonDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  forceValidate: true,
  // dataToJSON: DataToJSON.all,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      dynamicProps: {
        defaultValue: ({ dataSet }) => {
          let maxNum = 0;
          dataSet.forEach(_record => {
            if (_record?.get('sequence') > maxNum) {
              maxNum = _record?.get('sequence');
            }
          });
          return maxNum + 10;
        },
      },
    },
    {
      name: 'occur',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.occur`).d('发生原因'),
      required: true,
    },
    {
      name: 'escape',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.escape`).d('逃逸原因'),
      required: true,
    },
  ],
});

const measureDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  forceValidate: true,
  // dataToJSON: DataToJSON.all,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      dynamicProps: {
        defaultValue: ({ dataSet }) => {
          let maxNum = 0;
          dataSet.forEach(_record => {
            if (_record?.get('sequence') > maxNum) {
              maxNum = _record?.get('sequence');
            }
          });
          return maxNum + 10;
        },
      },
    },
    {
      name: 'measure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measure`).d('措施'),
      required: true,
    },
  ],
});

const taskDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  forceValidate: true,
  // dataToJSON: DataToJSON.all,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      dynamicProps: {
        defaultValue: ({ dataSet }) => {
          let maxNum = 0;
          dataSet.forEach(_record => {
            if (_record?.get('sequence') > maxNum) {
              maxNum = _record?.get('sequence');
            }
          });
          return maxNum + 10;
        },
      },
    },
    {
      name: 'taskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskCode`).d('任务编码'),
    },
    {
      name: 'taskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskStatus`).d('状态'),
      lookupCode: 'YP.QIS.VERIFICATION_TASK_STATUS',
    },
    {
      name: 'taskContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskContent`).d('检证内容'),
      required: true,
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('责任部门'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'departmentId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'departmentName',
      bind: 'departmentLov.unitName',
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      ignore: FieldIgnore.always,
      lovCode: 'HPFM.UNIT_LIMIT_EMPLOYEE',
      textField: 'realName',
      lovPara: { tenantId },
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            unitId: record.get('departmentId'),
          };
        },
      },
    },
    {
      name: 'responsibleUserId',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsibleUserName',
      bind: 'responsiblePersonLov.realName',
    },
    {
      name: 'lastResponsibleUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastResponsibleUserName`).d('上一责任人'),
    },
    {
      name: 'planEndTime',
      type: FieldType.date,
      format: 'YYYY-MM-DD',
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成时间'),
      required: true,
    },
    {
      name: 'taskResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskResult`).d('评审结果'),
    },
    {
      name: 'resultJudgeDate',
      type: FieldType.dateTime,
      format: 'YYYY-MM-DD HH:mm:ss',
      label: intl.get(`${modelPrompt}.resultJudgeDate`).d('结果判定日期'),
    },
    {
      name: 'applyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyFlag`).d('是否适用'),
    },
    {
      name: 'taskResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyFlag`).d('检证结果'),
    },
    {
      name: 'noApplyReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.noApplyReason`).d('不适用原因'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
    },
    {
      name: 'actualEndTime',
      type: FieldType.dateTime,
      format: 'YYYY-MM-DD HH:mm:ss',
      label: intl.get(`${modelPrompt}.actualEndTime`).d('实际结束时间'),
    },
  ],
});

const batchEditDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsiblePerson`).d('责任人'),
      ignore: FieldIgnore.always,
      lovCode: 'HPFM.UNIT_LIMIT_EMPLOYEE',
      textField: 'realName',
      lovPara: { tenantId },
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            unitId: record.get('departmentId'),
          };
        },
      },
    },
    {
      name: 'responsibleUserId',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'responsibleUserName',
      bind: 'responsiblePersonLov.realName',
    },
    {
      name: 'unitId',
      bind: 'responsiblePersonLov.unitId',
    },
    {
      name: 'unitName',
      bind: 'responsiblePersonLov.unitName',
    },
    {
      name: 'planEndTime',
      type: FieldType.date,
      format: 'YYYY-MM-DD',
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划完成时间'),
      required: true,
    },
  ],
});

export { detailDS, problemReasonDS, measureDS, taskDS, batchEditDS };
