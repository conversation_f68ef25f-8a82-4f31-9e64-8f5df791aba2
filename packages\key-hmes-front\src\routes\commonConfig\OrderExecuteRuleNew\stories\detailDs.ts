/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-12-14
 * @description 执行执行规则维护-详情页头DS
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSet } from 'choerodon-ui/pro';
import {
  strategyOptionsDs,
  typeOptionsDs,
  onPassageLocatorTypeOptionDs,
  instructionCreateModeOptionDs,
  instructionDocExeStrategyOptionDs,
  accountCategoryOptionsDs,
  enableFlagDs,
  yesNoDs,
} from './optionsDs';

const modelPrompt = 'tarzan.commonConfig.orderExecuteRule';
const tenantId = getCurrentOrganizationId();

const detailDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  autoCreate: true,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'businessObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('指令单据类型'),
      lovCode: 'APEX_WMS.INSTRUCTION_DOC_TYPE',
      required: true,
      textField: 'value',
      lovPara: {
        tenantId,
        typeGroup: 'INSTRUCTION_DOC_TYPE',
      },
    },
    {
      name: 'instructionDocType',
      bind: 'businessObj.value',
    },
    {
      name: 'typeDescription',
      bind: 'businessObj.meaning',
    },
    {
      name: 'description',
      label: intl.get(`${modelPrompt}.assembleGroupDesc`).d('单据类型描述'),
      bind: 'businessObj.meaning',
      disabled: true,
    },
    {
      name: 'statusGroupObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.statusGroup`).d('单据状态组'),
      lovCode: 'APEX_WMS.INSTRUCTION_DOC_STATUS',
      lovPara: {
        tenantId,
        defaultGroupCodePre: 'INSTRUCTION_DOC_STATUS',
      },
      textField: 'inputGroupCodePre',
      valueField: 'statusGroup',
    },
    {
      name: 'statusGroup',
      bind: 'statusGroupObj.statusGroup',
    },
    {
      name: 'instructionDocExeStrategy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocExeStrategy`).d('单据执行策略'),
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      dynamicProps: {
        options: ({ record }) => {
          if (record.get('instructionDocType') === 'NO_INSTRUCTION_DOC') {
            return new DataSet({
              data: [
                {
                  typeCode: 'No_Exe_Strategy',
                  description: intl.get(`${modelPrompt}.NoExeStrategy`).d('无执行策略'),
                },
              ],
            });
          }
          return instructionDocExeStrategyOptionDs;
        },
        disabled: ({ record }) => {
          return (
            !record.get('instructionDocType') ||
            record.get('instructionDocType') === 'NO_INSTRUCTION_DOC'
          );
        },
      },
    },
    {
      name: 'instructionCreateMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionCreateMode`).d('指令创建模式'),
      options: instructionCreateModeOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('instructionDocExeStrategy') !== 'TWO_STEP';
        },
        required: ({ record }) => {
          return record.get('instructionDocExeStrategy') === 'TWO_STEP';
        },
      },
    },
    {
      name: 'permissionConfig',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.permissionConfig`).d('权限配置'),
      lookupCode: 'WMS.PERMISSION_CONFIG',
      lovPara: {
        tenantId,
      },
      textField: 'meaning',
      valueField: 'value',
      required: true,
    },
    {
      name: 'operableFunctionDocObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operableFunctionDoc`).d('可操作单据功能'),
      lovCode: 'APEX_WMS.OPERABLE_FUNCTION',
      lovPara: {
        tenantId,
        tag: 'PC',
      },
      multiple: true,
    },
    {
      name: 'operableFunctionDoc',
      bind: 'operableFunctionDocObj.value',
    },
    {
      name: 'operableFunctionDocDesc',
      bind: 'operableFunctionDocObj.meaning',
    },
    {
      name: 'fromLocatorRequiredFlag',
      label: intl.get(`${modelPrompt}.fromLocatorRequired`).d('来源库位必输'),
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'toLocatorRequiredFlag',
      label: intl.get(`${modelPrompt}.toLocatorRequired`).d('目标库位必输'),
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'accountCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountCategory`).d('结算类别'),
      options: accountCategoryOptionsDs,
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'docInitialFlag',
      label: intl.get(`${modelPrompt}.docInitialFlag`).d('初始化'),
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      disabled: true,
    },
  ],
});

const detailListDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  fields: [
    {
      name: 'instructionObj',
      type: FieldType.object,
      lookupCode: 'WMS.INSTRUCTION_MOVE_TYPE',
      label: intl.get(`${modelPrompt}.instructionType`).d('移动类型'),
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    {
      name: 'instructionType',
      bind: 'instructionObj.value',
    },
    {
      name: 'instructionTypeDesc',
      bind: 'instructionObj.meaning',
    },
    {
      name: 'moveType',
      bind: 'instructionObj.tag',
    },
    {
      name: 'businessObj',
      type: FieldType.object,
      lovCode: 'APEX_WMS.COMMON.BUSINESS_TYPE',
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    {
      name: 'businessType',
      bind: 'businessObj.typeCode',
    },
    {
      name: 'businessTypeDesc',
      bind: 'businessObj.description',
    },
    {
      name: 'instructionExeStrategy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionExeStrategy`).d('指令执行策略'),
      options: strategyOptionsDs,
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    {
      name: 'operableFunctionInsListObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operableFunctionDoc`).d('可操作指令功能'),
      lovCode: 'APEX_WMS.OPERABLE_FUNCTION',
      lovPara: {
        tenantId,
        tag: 'PDA',
      },
      multiple: true,
    },
    {
      name: 'operableFunctionInsList',
      bind: 'operableFunctionInsListObj.value',
    },
    {
      name: 'operableFunctionInsDescList',
      bind: 'operableFunctionInsListObj.meaning',
    },
    {
      name: 'interactiveMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.interactiveMode`).d('交互模式'),
      lookupCode: 'WMS.INTERACTIVE_MODE',
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    {
      name: 'inspectionNodeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectionNodeFlag`).d('报检标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'addStrategyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addStrategyFlag`).d('寻址策略标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'addStrategyCheck',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addStrategyCheck`).d('拣取寻址结果'),
      lookupCode: 'WMS.CHECK_MODE',
      dynamicProps: {
        required: ({ dataSet, record }) => {
          return (
            record.get('addStrategyFlag') === 'Y' &&
            (dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
              dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP')
          );
        },
      },
    },
    {
      name: 'instructionDetailCheck',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDetailCheck`).d('指令明细'),
      lookupCode: 'WMS.CHECK_MODE',
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    {
      name: 'onPassageLocatorDirection',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onPassageLocatorDirection`).d('在途货位方向'),
      lookupCode: 'APEX_WMS.ON_PASSAGE_LOCATOR_DIRECTION_NEW',
      valueField: 'value',
      textField: 'meaning',
      dynamicProps: {
        disabled: ({ dataSet }) => {
          if (dataSet.parent?.current?.get('instructionDocExeStrategy') === 'ONE_STEP') {
            return true;
          }
          if (dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP') {
            return dataSet.getState('type') === 'TWO_STEP';
          }
        },
        required: ({ dataSet }) => {
          if (dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP') {
            return dataSet.getState('type') === 'ONE_STEP';
          }
          return false
        },
      },
    },
    {
      name: 'onPassageLocatorType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetLocatorType`).d('目标货位类型'),
      options: onPassageLocatorTypeOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      multiple: true,
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'toleranceType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlagType`).d('允差类型'),
      options: typeOptionsDs,
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('toleranceFlag') !== 'Y';
        },
        required: ({ dataSet, record }) => {
          return (
            record.get('toleranceFlag') === 'Y' &&
            (dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
              dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP')
          );
        },
      },
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差值'),
      min: 0,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('toleranceType') ||
            ['OVER_MATERIAL_LOT'].includes(record.get('toleranceType'))
          );
        },
        required: ({ dataSet, record }) => {
          return (
            ['PERCENTAGE', 'NUMBER'].includes(record.get('toleranceType')) &&
            (dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
              dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP')
          );
        },
      },
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差值'),
      min: 0,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('toleranceType') ||
            ['OVER_MATERIAL_LOT'].includes(record.get('toleranceType'))
          );
        },
        required: ({ dataSet, record }) => {
          return (
            ['PERCENTAGE', 'NUMBER'].includes(record.get('toleranceType')) &&
            (dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
              dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP')
          );
        },
      },
    },
    {
      name: 'transferFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.transferFlag`).d('生成事务'),
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.initialFlag`).d('初始化'),
      disabled: true,
    },

    // 指令相关 up

    // 条码相关 down
    {
      name: 'materialLotEnableFlagList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotEnableFlagList`).d('有效性'),
      options: enableFlagDs,
      valueField: 'value',
      textField: 'meaning',
      trueValue: 'Y',
      falseValue: 'N',
      multiple: true,
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    // 条码相关 down
    {
      name: 'materialLotScanOperationList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotScanOperationList`).d('物料扫描操作列表'),
      lookupCode: 'WMS.MATERIAL_LOT_SCAN_OPERATION',
      multiple: true,
    },
    {
      name: 'qualityStatusCheckList',
      type: FieldType.string,
      lookupCode: 'APEX_WMS.LES.QUALITY_STATUS_CHECK',
      label: intl.get(`${modelPrompt}.qualityStatusCheckList`).d('质量状态'),
      multiple: true,
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'revisionCheck',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCheck`).d('物料版本'),
      lookupCode: 'WMS.CHECK_MODE',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    {
      name: 'freezeFlagCheckList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlagCheckList`).d('冻结标识'),
      options: yesNoDs,
      valueField: 'value',
      textField: 'meaning',
      trueValue: 'Y',
      falseValue: 'N',
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    {
      name: 'stocktakeFlagList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeFlagList`).d('盘点标识'),
      options: yesNoDs,
      valueField: 'value',
      textField: 'meaning',
      trueValue: 'Y',
      falseValue: 'N',
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    {
      name: 'materialLotStatusCheckList',
      type: FieldType.string,
      lookupCode: 'WMS.MATERIAL_LOT_STATUS',
      label: intl.get(`${modelPrompt}.materialLotStatusCheckList`).d('状态'),
      multiple: true,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ dataSet }) => {
          return (
            dataSet.parent?.current?.get('instructionDocExeStrategy') ===
              dataSet.getState('type') ||
            dataSet.parent?.current?.get('instructionDocExeStrategy') === 'TWO_STEP'
          );
        },
      },
    },
    {
      name: 'executedMaterialLotStatus',
      type: FieldType.string,
      lookupCode: 'WMS.MATERIAL_LOT_STATUS',
      label: intl.get(`${modelPrompt}.executedMaterialLotStatus`).d('执行后状态'),
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'disabledUnloadFlag',
      type: FieldType.string,
      lookupCode: 'APEX_WMS.YES_NO',
      label: intl.get(`${modelPrompt}.disabledUnloadFlag`).d('失效卸载'),
      lovPara: {
        tenantId,
      },
      defaultValue: "N",
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('moveType') !== 'SENT';
        },
        required: ({ record }) => {
          return record?.get('moveType') === 'SENT';
        },
      },
    },
    {
      name: 'disabledQtyUpdateFlag',
      type: FieldType.string,
      lookupCode: 'APEX_WMS.YES_NO',
      label: intl.get(`${modelPrompt}.disabledQtyUpdateFlag`).d('失效更新数量'),
      lovPara: {
        tenantId,
      },
      defaultValue: "N",
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('moveType') !== 'SENT';
        },
        required: ({ record }) => {
          return record?.get('moveType') === 'SENT';
        },
      },
    },
  ],
  events: {
    update: ({ record, name }) => {
      switch (name) {
        case 'toleranceFlag':
          record.init('toleranceType', null);
          record.init('toleranceMaxValue', null);
          record.init('toleranceMinValue', null);
          break;
        case 'toleranceType':
          record.init('toleranceMaxValue', null);
          record.init('toleranceMinValue', null);
          break;
        case 'onPassageLocatorType':
          record.init('onPassageLocatorObj', null);
          break;
        default:
          break;
      }
    },
  },
});

export { detailDS, detailListDS };
