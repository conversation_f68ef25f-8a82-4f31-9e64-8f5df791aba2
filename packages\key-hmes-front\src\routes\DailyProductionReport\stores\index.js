import { Host } from '@/utils/config';
// import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import moment from 'moment';
import { getCurrentOrganizationId } from 'utils/utils';

// const tenantId = getCurrentOrganizationId();
// const Host = `/yp-mes-38510`;
const modelPrompt = 'tarzan.hmes.DailyProductionReport';

const searchDS = () => {
  return {
    forceValidate: true,
    name: 'searchDS',
    autoCreate: true,
    events: {
      update: ({ record, name }) => {
        if(name === 'prodLineGroup'){
          record.set('prodLineLov', null)
          record.set('prodLineId', null)
          record.set('prodLineCode', null)
        }
      },
    },
    fields: [
      {
        name: 'prodLineGroup',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineGroup`).d('生产线组'),
        // lookupCode: 'HME.PRODUCTION_DAILY_DISPLAY_OPERATION',
        lookupUrl: `${Host}/v1/${getCurrentOrganizationId()}/hme-product-report/prod/group/list`,
        textField: 'meaning',
        valueField: 'value',
        lookupAxiosConfig: {
          transformResponse(data) {
            // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            const list = []
            rows.forEach(item => {
              const obj = {}
              obj.value = item
              obj.meaning = item
              list.push(obj)
            })
            return list;
          },
        },
        dynamicProps: {
          required: ({ record }) => {
            return !record?.get('prodLineLov')
          },
        },
      },
      {
        name: 'prodLineLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineLov`).d('生产产线'),
        lovCode: 'HME.PRODUCTION_DAILY_PRODUCTION_LINE',
        textField: 'prodLineCode',
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              prodLineGroup: record?.get('prodLineGroup'),
            }
          },
          required: ({ record }) => {
            return !record?.get('prodLineGroup')
          },
        },
      },
      {
        name: 'prodLineId',
        bind: 'prodLineLov.prodLineId',
      },
      {
        name: 'prodLineCode',
        bind: 'prodLineLov.prodLineCode',
      },
      {
        name: 'dateStr',
        type: 'date',
        required: true,
        label: intl.get(`${modelPrompt}.date`).d('日期'),
        max: moment().format('YYYY-MM-DD'),
      },
      {
        name: 'shiftCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.shiftCode`).d('班次'),
        lookupCode: 'HME.PRODUCTION_DAILY_SHIFT',
        required: true,
      },
    ],
  }
}
const headFormDS = () => {
  return {
    name: 'headFormDS',
    autoCreate: true,
    forceValidate: true,
    fields: [
      {
        name: 'prodLineGroup',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineGroup`).d('产线组'),
      },
      {
        name: 'prodLineCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineCode`).d('生产产线'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('生产产线'),
      },
      {
        name: 'date',
        type: 'string',
        label: intl.get(`${modelPrompt}.date`).d('日期'),
      },
      {
        name: 'shiftCodeMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.shiftCodeMeaning`).d('班次'),
      },
      {
        name: 'plannedAttendance',
        type: 'number',
        min: 0,
        precision: 0,
        required: true,
        label: intl.get(`${modelPrompt}.plannedAttendance`).d('应出勤人数'),
      },
      {
        name: 'actualAttendance',
        type: 'number',
        required: true,
        min: 0,
        precision: 0,
        label: intl.get(`${modelPrompt}.actualAttendance`).d('实际出勤人数'),
      },
      {
        name: 'packPlannedAttendance',
        type: 'number',
        min: 0,
        precision: 0,
        label: intl.get(`${modelPrompt}.packPlannedAttendance`).d('PACK应出勤人数'),
      },
      {
        name: 'packActualAttendance',
        type: 'number',
        min: 0,
        precision: 0,
        label: intl.get(`${modelPrompt}.packActualAttendance`).d('PACK实际出勤人数'),
      },
      {
        name: 'totalDuration',
        required: true,
        type: 'number',
        label: intl.get(`${modelPrompt}.totalDuration`).d('总开时'),
        min: 0,
      },
      {
        name: 'beat',
        required: true,
        type: 'number',
        label: intl.get(`${modelPrompt}.beat`).d('生产节拍'),
        min: 0,
      },
      {
        name: 'remark',
        type: 'string',
        label: intl.get(`${modelPrompt}.remark`).d('补充事项'),
      },
    ],
  }
}
const headTableDS = () => {
  return {
    name: 'headTableDS',
    paging: false,
    autoQuery: false,
    selection: false,
    forceValidate: true,
    fields: [
      {
        name: 'workCellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workCellName`).d('工序'),
      },
    ],
  }
}
// 上午
const lineTableADS = () => {
  return {
    name: 'lineTableADS',
    primaryKey: 'uuid',
    paging: false,
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'operationId',
        label: intl.get(`${modelPrompt}.description`).d('工序'),
        type: 'string',
        textField: 'description',
        valueField: 'operationId',
      },
      {
        name: 'description',
        type: 'string',
      },
      {
        name: 'problemType',
        label: intl.get(`${modelPrompt}.problemType`).d('类型'),
        type: 'string',
        lookupCode: 'HME.PROBLEM_POINT_TYPE',
      },
      {
        name: 'phenomenon',
        type: 'string',
        label: intl.get(`${modelPrompt}.phenomenon`).d('现象/问题点'),
      },
      {
        name: 'timeQuantumDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.timeQuantumDesc`).d('时间段/(上午)'),
      },
      {
        name: 'startTime',
        label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
        type: 'time',
        max: 'endTime',
      },
      {
        name: 'endTime',
        label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
        type: 'time',
        min: 'startTime',
      },
      {
        name: 'totalTime',
        label: intl.get(`${modelPrompt}.totalTime`).d('合计停止(分钟)'),
      },
      {
        name: 'reason',
        label: intl.get(`${modelPrompt}.reason`).d('原因'),
        type: 'string',
      },
      {
        name: 'countermeasure',
        label: intl.get(`${modelPrompt}.countermeasure`).d('对策'),
        type: 'string',
      },
    ],
    events: {
      update: ({ record, name})  => {
        if(name === 'startTime'||name==='endTime'){
          const startTime = record?.get('startTime')
          const endTime = record?.get('endTime')
          if(startTime&&endTime){
            const total = moment(endTime).diff(moment(startTime), 'seconds')
            record.set('totalTime',accDiv( total, 60))
          }
        }
      },
    },
  };
};
// 下午
const lineTableBDS = () => {
  return {
    name: 'lineTableBDS',
    primaryKey: 'uuid',
    paging: false,
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'operationId',
        label: intl.get(`${modelPrompt}.description`).d('工序'),
        type: 'string',
        textField: 'description',
        valueField: 'operationId',
      },
      {
        name: 'description',
        type: 'string',
      },
      {
        name: 'problemType',
        label: intl.get(`${modelPrompt}.problemType`).d('类型'),
        type: 'string',
        lookupCode: 'HME.PROBLEM_POINT_TYPE',
      },
      {
        name: 'phenomenon',
        type: 'string',
        label: intl.get(`${modelPrompt}.phenomenon`).d('现象/问题点'),
      },
      {
        name: 'timeQuantumDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.timeQuantumDesc`).d('时间段/(下午)'),
      },
      {
        name: 'startTime',
        label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
        type: 'time',
        max: 'endTime',
      },
      {
        name: 'endTime',
        label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
        type: 'time',
        min: 'startTime',
      },
      {
        name: 'totalTime',
        label: intl.get(`${modelPrompt}.totalTime`).d('合计停止(分钟)'),
      },
      {
        name: 'reason',
        label: intl.get(`${modelPrompt}.reason`).d('原因'),
        type: 'string',
      },
      {
        name: 'countermeasure',
        label: intl.get(`${modelPrompt}.countermeasure`).d('对策'),
        type: 'string',
      },
    ],
    events: {
      update: ({ record, name})  => {
        if(name === 'startTime'||name==='endTime'){
          const startTime = record?.get('startTime')
          const endTime = record?.get('endTime')
          if(startTime&&endTime){
            const total = moment(endTime).diff(moment(startTime), 'seconds')
            record.set('totalTime',accDiv( total, 60))
          }
        }
      },
    },
  };
};
// 加班
const lineTableCDS = () => {
  return {
    name: 'lineTableCDS',
    primaryKey: 'uuid',
    paging: false,
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'operationId',
        label: intl.get(`${modelPrompt}.description`).d('工序'),
        type: 'string',
        textField: 'description',
        valueField: 'operationId',
      },
      {
        name: 'description',
        type: 'string',
      },
      {
        name: 'problemType',
        label: intl.get(`${modelPrompt}.problemType`).d('类型'),
        type: 'string',
        lookupCode: 'HME.PROBLEM_POINT_TYPE',
      },
      {
        name: 'phenomenon',
        type: 'string',
        label: intl.get(`${modelPrompt}.phenomenon`).d('现象/问题点'),
      },
      {
        name: 'timeQuantumDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.timeQuantumDesc`).d('时间段/(加班)'),
      },
      {
        name: 'startTime',
        label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
        type: 'time',
        max: 'endTime',
      },
      {
        name: 'endTime',
        label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
        type: 'time',
        min: 'startTime',
      },
      {
        name: 'totalTime',
        label: intl.get(`${modelPrompt}.totalTime`).d('合计停止(分钟)'),
      },
      {
        name: 'reason',
        label: intl.get(`${modelPrompt}.reason`).d('原因'),
        type: 'string',
      },
      {
        name: 'countermeasure',
        label: intl.get(`${modelPrompt}.countermeasure`).d('对策'),
        type: 'string',
      },
    ],
    events: {
      update: ({ record, name})  => {
        if(name === 'startTime'||name==='endTime'){
          const startTime = record?.get('startTime')
          const endTime = record?.get('endTime')
          if(startTime&&endTime){
            const total = moment(endTime).diff(moment(startTime), 'seconds')
            record.set('totalTime',accDiv( total, 60))
          }
        }
      },
    },
  };
};
function accDiv(arg1,arg2){
  let t1=0; let t2=0;
  try{t1=arg1.toString().split(".")[1].length}catch(e){}
  try{t2=arg2.toString().split(".")[1].length}catch(e){}
  const r1=Number(arg1.toString().replace(".",""))
  const r2=Number(arg2.toString().replace(".",""))
  // eslint-disable-next-line no-restricted-properties
  return accMul((r1/r2), Math.pow(10,t2-t1));
}

function accMul(arg1,arg2)
{
  let m=0; const s1=arg1.toString(); const s2=arg2.toString();
  try{m+=s1.split(".")[1].length}catch(e){}
  try{m+=s2.split(".")[1].length}catch(e){}
  // eslint-disable-next-line no-restricted-properties
  return Number(s1.replace(".",""))*Number(s2.replace(".",""))/Math.pow(10,m)
}

export { headTableDS, lineTableADS,lineTableBDS,lineTableCDS, headFormDS, searchDS };
