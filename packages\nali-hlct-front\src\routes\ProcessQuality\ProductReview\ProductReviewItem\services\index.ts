/*
 * @Description: 产品审核项目-services
 * @Author: <<EMAIL>>
 * @Date: 2023-09-28 10:29:09
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-28 11:22:31
 */
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

/**
 * 保存产品审核项目
 * @function SaveProductItem
 * @returns {object} fetch Promise
 */
export function SaveProductItem(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-product-review-item/batch-save/ui`,
    method: 'POST',
  };
}