/*
 * @Description: 索赔管理 -列表页
 * @Author: <<EMAIL>>
 * @Date: 2023-09-21 16:14:20
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-02-02 10:06:31
 */
import React, { useMemo, useCallback } from 'react';
import { Table, DataSet, Button, DatePicker } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores';

const modelPrompt = 'tarzan.qms.marketActivity.claimDocManagement';

const ClaimDocList = props => {
  const { tableDs, history } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'claimDocNum',
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/market-quality/claim-doc-management/dist/${record!.get('claimDocId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'siteName', width: 180 },
      { name: 'hostPlant' },
      { name: 'title', width: 230 },
      {
        name: 'aResponsibilityRatio',
        width: 180,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return `${value}%`;
        },
      },
      {
        name: 'bResponsibilityRatio',
        width: 180,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return `${value}%`;
        },
      },
      { name: 'dutyEnclosure', width: 180 },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={record!.getField('enableFlag')!.getText()}
          />
        ),
      },
      { name: 'validFrom', align: ColumnAlign.center, width: 150 },
      { name: 'validTo', align: ColumnAlign.center, width: 150 },
      { name: 'createdByName' },
      { name: 'remarks', width: 230 },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    history.push(`/hwms/market-quality/claim-doc-management/dist/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('索赔管理')}>
        <Button color={ButtonColor.primary} icon="add" onClick={handleAdd}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFields={{
            validFromFrom: <DatePicker name="validFromFrom" />,
            validFromTo: <DatePicker name="validFromTo" />,
            validToFrom: <DatePicker name="validToFrom" />,
            validToTo: <DatePicker name="validToTo" />,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="claimDocManagement_searchCode"
          customizedCode="claimDocManagement_customizedCode"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ClaimDocList),
);
