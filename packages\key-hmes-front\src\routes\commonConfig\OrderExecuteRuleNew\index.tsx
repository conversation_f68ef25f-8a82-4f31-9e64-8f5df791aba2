/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-12-14
 * @description 执行执行规则维护-列表页
 */
import React, { FC, useEffect, useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Badge, Popconfirm } from 'hzero-ui';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Tag } from 'choerodon-ui';
import { BASIC } from '@/utils/config';
import myInstance from '@utils/myAxios';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { observer } from 'mobx-react';
import { listDS } from './stories/listDs';

const modelPrompt = 'tarzan.commonConfig.orderExecuteRule';

const tenantId = getCurrentOrganizationId();

interface OrderExecuteRuleProps extends RouteComponentProps {
  tableDs: DataSet;
  customizeTable: any;
}

const OrderExecuteRule: FC<OrderExecuteRuleProps> = props => {
  const {
    match: { path },
    tableDs,
    history,
    customizeTable,
  } = props;

  useEffect(() => {
    if (tableDs?.currentPage) {
      tableDs.query(tableDs.currentPage);
    }
  }, []);

  const tagType = record => {
    const businessOperationSequence = record.get('businessOperationSequence') || 0;
    const listIndex = businessOperationSequence % 9;
    const colorList = [
      'gold-inverse',
      'cyan-inverse',
      'lime-inverse',
      'green-inverse',
      'blue-inverse',
      'geekblue-inverse',
      'purple-inverse',
      'dark-inverse',
      'gray-inverse',
    ];

    return colorList[listIndex];
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'instructionDocType',
        lock: ColumnLock.left,
        width: 280,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hmes/commonConfig/order-execute-rule-new/detail/${record!.get(
                    'instructionDocFuncExeRuleId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'typeDescription', align: ColumnAlign.left },
      {
        name: 'instructionDocExeStrategy',
        renderer: ({ value }) => {
          if (value === 'ONE_STEP') {
            return <Tag color="orange">{intl.get(`${modelPrompt}.oenStep`).d('一步法')}</Tag>;
          }
          if (value === 'TWO_STEP') {
            return <Tag color="blue">{intl.get(`${modelPrompt}.twoStep`).d('两步法')}</Tag>;
          }
          if (value === 'No_Exe_Strategy') {
            return (
              <Tag color="green">{intl.get(`${modelPrompt}.NoExeStrategy`).d('无执行策略')}</Tag>
            );
          }
          return '';
        },
        align: ColumnAlign.left,
      },
      {
        name: 'instructionCreateMode',
        width: 120,
        align: ColumnAlign.left,
      },
      {
        name: 'permissionConfigDesc',
        width: 120,
      },
      {
        name: 'businessOperationObj',
        width: 120,
      },
      { name: 'statusGroup', width: 400, align: ColumnAlign.left },
      {
        name: 'fromLocatorRequiredFlag',
        width: 140,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('fromLocatorRequiredFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('fromLocatorRequiredFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        align: ColumnAlign.center,
      },
      {
        name: 'toLocatorRequiredFlag',
        width: 140,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('toLocatorRequiredFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('toLocatorRequiredFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        align: ColumnAlign.center,
      },
      {
        name: 'accountCategoryDesc',
        width: 120,
        align: ColumnAlign.center,
      },
      {
        name: 'initialFlag',
        renderer: ({ record }) => (
          <Badge
            status={record!.get('initialFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('initialFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        align: ColumnAlign.center,
      },
    ];
  }, []);

  const goDetail = () => {
    history.push(`/hmes/commonConfig/order-execute-rule-new/detail/create`);
  };

  // 删除选中的数据
  const deleteSelectData = () => {
    const url = `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-ins-doc-func-exe-rules/delete/ui`;
    const data: any = [];
    tableDs.selected.forEach(record => {
      data.push(record.get('instructionDocFuncExeRuleId'));
    });
    myInstance.post(url, data).then(res => {
      if (res.data.success) {
        notification.success({});
        // 删除成功后重查列表
        tableDs.query();
      } else if (res.data.message) {
        notification.error({
          description: res.data.message,
        });
      }
    });
  };

  const onFieldEnterDown = () => {
    tableDs.query(props.tableDs.currentPage);
  };

  const DeleteButton = observer(({ ds }) => {
    return (
      <Popconfirm
        title={intl
          .get(`${modelPrompt}.confirm.delete`, {
            count: ds.selected.length,
          })
          .d(`总计${ds.selected.length}条数据，是否确认删除?`)}
        onConfirm={deleteSelectData}
      >
        <PermissionButton
          type="c7n-pro"
          icon="delete_black-o"
          disabled={!(ds.selected?.length > 0)}
          permissionList={[
            {
              code: `tarzan${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.delete').d('删除')}
        </PermissionButton>
      </Popconfirm>
    );
  });

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.order-execute-rule-new`).d('指令执行规则维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={goDetail}
          permissionList={[
            {
              code: `tarzan${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <DeleteButton ds={tableDs} />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.LIST`,
          },
          <Table
            searchCode="zlzxgzwh"
            customizedCode="zlzxgzwh"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            dataSet={tableDs}
            columns={columns as ColumnProps[]}
            queryFieldsLimit={3}
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.commonConfig.orderExecuteRule', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs: DataSet = new DataSet({
        ...listDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, keepOriginDataSet: true },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_LIST.LIST`,
    ],
  }),
)(OrderExecuteRule);
