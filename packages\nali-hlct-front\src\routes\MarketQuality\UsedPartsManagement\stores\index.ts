import intl from 'utils/intl';
import { FieldIgnore, DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.oldManagement.oldPartManagement';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'oldPartId',
  queryFields: [
    {
      name: 'docNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docNum`).d('申请单号'),
    },
    {
      name: 'oldPartStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldPartStatus`).d('状态'),
      lookupCode: 'YP.QIS.OLD_PART_STATUS',
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('电池产品类型'),
      lookupCode: 'YP.QIS.OLD_PARTS_PRODUCT_TYPE',
    },
    {
      name: 'batteryType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryType`).d('电池类型'),
      lookupCode: 'YP.QIS.BATTERY_TYPE',
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('产品编码'),
    },
    {
      name: 'customerMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerMaterialCode`).d('客户零件号'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'receiveByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.receiveBy`).d('接收人'),
      lovCode: 'YP.QIS.USER.ORG',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'receiveBy',
      type: FieldType.number,
      bind: 'receiveByLov.userId',
    },
  ],
  fields: [
    {
      name: 'docNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docNum`).d('申请单号'),
    },
    {
      name: 'oldPartStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldPartStatus`).d('状态'),
      lookupCode: 'YP.QIS.OLD_PART_STATUS',
    },
    {
      name: 'oldPartStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldPartStatus`).d('状态'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('电池产品类型'),
      lookupCode: 'YP.QIS.OLD_PARTS_PRODUCT_TYPE',
    },
    {
      name: 'batteryType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryType`).d('电池类型'),
      lookupCode: 'YP.QIS.BATTERY_TYPE',
    },
    {
      name: 'customerMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerMaterialCode`).d('客户零件号'),
    },
    {
      name: 'customerMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerMaterialName`).d('客户零件名称'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('产品编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'receiveDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.receiveDate`).d('接收时间'),
    },
    {
      name: 'receiveByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveBy`).d('接收人'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResult`).d('检测结果'),
      lookupCode: 'YP.QIS.OLD_PART_INSPECT_RESULT',
    },
    {
      name: 'outStorageDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.outStorageDate`).d('出库时间'),
    },
    {
      name: 'outStorageByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outStorageBy`).d('出库人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-old-part-management/page/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          const data = (rows?.content || [])?.map(item => {
            const { recordInfoMap } = item;
            return {
              ...item,
              ...recordInfoMap,
            };
          });
          return data;
        },
      };
    },
  },
});

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'YP.QIS.SITE_NAME',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ dataSet }) => dataSet.getState('editing'),
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'docNum',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.backApplyNum`).d('返回申请单号'),
      dynamicProps: {
        disabled: ({ dataSet }) => dataSet.getState('editing'),
      },
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('电池产品类型'),
      lookupCode: 'YP.QIS.OLD_PARTS_PRODUCT_TYPE',
      required: true,
    },
    {
      name: 'batteryType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryType`).d('电池类型'),
      lookupCode: 'YP.QIS.BATTERY_TYPE',
      required: true,
    },
    {
      name: 'customerMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerMaterialCode`).d('客户零件号'),
      required: true,
    },
    {
      name: 'customerMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerMaterialName`).d('客户零件名称'),
      required: true,
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('产品编码'),
      dynamicProps: {
        disabled: ({ record }) =>
          ['PARTS_POWER', 'PARTS_ENERGY'].includes(record.get('productType')) ||
          !record.get('productType'),
        required: ({ record }) =>
          !['PARTS_POWER', 'PARTS_ENERGY'].includes(record.get('productType')),
      },
    },
    {
      name: 'materialLov',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.object,
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      bind: 'materialLov.materialName',
      disabled: true,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
      required: true,
      precision: 6,
    },
  ],
});

export { tableDS, detailDS };
