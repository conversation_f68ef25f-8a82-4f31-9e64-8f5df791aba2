/**
 * @Description: 计量型图形，数据点信息
 * @Author: <<EMAIL>>
 * @Date: 2021-06-01 20:15:16
 * @LastEditTime: 2022-06-14 16:42:41
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { Row, Col, Modal, Button } from 'hzero-ui';
import { Tag, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import ModalTable from './ModalTable';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hspc.chartInfo';
let hideButtonFlag = false;

const MeasureTypeModal = props => {
  const {
    onCancel,
    onHidden,
    measureModalVisible,
    subDataInfo,
    subgroupBar,
    subgroupR,
    subgroupSigma,
    subgroupMe,
    subgroupMax,
    subgroupMin,
    clickIndex,
    mainChartOoc,
    secondaryChartOoc,
    // hideButtonFlag,
    dataPositionIndex,
    chartData,
  } = props;
  if (clickIndex === 0) {
    if (
      chartData.data[dataPositionIndex].mainChartOoc &&
      chartData.data[dataPositionIndex].mainChartOoc.length > 0
    ) {
      hideButtonFlag = true;
    } else {
      hideButtonFlag = false;
    }
  }
  if (clickIndex === 1) {
    if (
      chartData.data[dataPositionIndex].secondaryChartOoc &&
      chartData.data[dataPositionIndex].secondaryChartOoc.length > 0
    ) {
      hideButtonFlag = true;
    } else {
      hideButtonFlag = false;
    }
  }

  return (
    <Modal
      destroyOnClose
      title={intl.get(`${modelPrompt}.dataPointDetails`).d('数据点详细信息')}
      width={660}
      visible={measureModalVisible} // 模态对话框是否可见
      onCancel={onCancel}
      onOk={onCancel}
      // okText={intl.get('tarzan.common.button.confirm').d('确定')}
      // cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
      footer={
        hideButtonFlag
          ? [
            <Button key="submit" type="primary" onClick={onHidden}>
                隐藏
            </Button>,
            <Button key="back" onClick={onCancel}>
                取消
            </Button>,
            <Button key="submit" type="primary" onClick={onCancel}>
                确定
            </Button>,
          ]
          : [
            <Button key="back" onClick={onCancel}>
                取消
            </Button>,
            <Button key="submit" type="primary" onClick={onCancel}>
                确定
            </Button>,
          ]
      }
    >
      {subDataInfo &&
        subDataInfo.length !== 0 &&
        subDataInfo.map(item => (
          // <div style={{ margin: '0 6px 8px 0' }}>
          <Tag color="blue">
            {item.dataValue}，{item.dataColumnBatch || null}
          </Tag>
          // </div>
        ))}
      <Row gutter={16} style={{ marginTop: 20 }}>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.average`).d('平均值')}:
            </Col>
            <Col span={12} offset={2}>
              {subgroupBar}
            </Col>
          </Row>
        </Col>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.range`).d('极差值')}:
            </Col>
            <Col span={12} offset={2}>
              {subgroupR}
            </Col>
          </Row>
        </Col>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.sigma`).d('标准差')}:
            </Col>
            <Col span={12} offset={2}>
              {subgroupSigma}
            </Col>
          </Row>
        </Col>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.median`).d('中位数')}:
            </Col>
            <Col span={12} offset={2}>
              {subgroupMe}
            </Col>
          </Row>
        </Col>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.max`).d('最大值')}:
            </Col>
            <Col span={12} offset={2}>
              {subgroupMax}
            </Col>
          </Row>
        </Col>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.min`).d('最小值')}:
            </Col>
            <Col span={12} offset={2}>
              {subgroupMin}
            </Col>
          </Row>
        </Col>
      </Row>
      <Collapse bordered={false} defaultActiveKey={['outOfControlInformation']}>
        <Panel
          header={intl.get(`${modelPrompt}.outOfControlInformation`).d('失控信息')}
          key="outOfControlInformation"
        >
          <ModalTable dataSource={clickIndex ? secondaryChartOoc : mainChartOoc} />
        </Panel>
      </Collapse>
    </Modal>
  );
};

export default MeasureTypeModal;
