/*
 * @Description: 单据ds
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-03-28 14:40:46
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */
import { isNull, isUndefined, isNil } from 'lodash';
import { HALM_ATN } from 'alm/utils/config';
import { isJSON } from 'alm/utils/utils';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps, DataToJSON } from 'choerodon-ui/pro/lib/data-set/interface';

import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();

// 单据基础信息DS
function detailDS(): DataSetProps {
  return {
    selection: false,
    autoCreate: true,
    autoQuery: false,
    fields: [
      {
        name: 'changeNum',
        type: FieldType.string,
        label: getLang('CHANGE_NUM'),
        maxLength: 40,
        dynamicProps: {
          disabled: ({ record }) => {
            return !isNull(record.get('codeRule')) && !isUndefined(record.get('codeRule'));
          },
          required: ({ record }) => {
            return isNull(record.get('codeRule')) || isUndefined(record.get('codeRule'));
          },
        },
      },
      {
        name: 'titleOverview',
        type: FieldType.string,
        label: getLang('TITLE_OVERVIEW'),
        maxLength: 40,
        required: true,
      },
      {
        name: 'transactionTypeLov',
        type: FieldType.object,
        label: getLang('TRANSACTION_TYPE'),
        lovCode: 'AAFM.TRANSACTION_TYPES',
        lovPara: {
          enabledFlag: 1,
          tenantId: organizationId,
        },
        ignore: FieldIgnore.always,
        disabled: true,
        required: true,
      },
      {
        name: 'transactionTypeId',
        type: FieldType.number,
        bind: 'transactionTypeLov.transactionTypeId',
      },
      {
        name: 'transactionTypeIdMeaning',
        type: FieldType.string,
        label: getLang('TRANSACTION_TYPE'),
        bind: 'transactionTypeLov.transactionTypeName',
      },
      {
        name: 'reason',
        type: FieldType.string,
        label: getLang('REASON_TRANSFER'),
      },
      {
        name: 'processStatus',
        type: FieldType.string,
        defaultValue: 'DRAFT',
        label: getLang('PROCESS_STATUS'),
        lookupCode: 'HALM.APPROVE_STATUS',
        disabled: true,
      },
      {
        name: 'processStatusMeaning',
        type: FieldType.string,
        label: getLang('PROCESS_STATUS'),
      },
      {
        name: 'principalPersonLov',
        type: FieldType.object,
        label: getLang('PRINCIPAL_PERSON'),
        lovCode: 'HALM.EMPLOYEE',
        required: true,
        lovPara: {
          tenantId: organizationId,
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'principalPersonId',
        type: FieldType.number,
        bind: 'principalPersonLov.employeeId',
      },
      {
        name: 'principalPersonIdMeaning',
        type: FieldType.string,
        label: getLang('PRINCIPAL_PERSON'),
        bind: 'principalPersonLov.employeeName',
      },
      {
        name: 'usingOrgName',
        type: FieldType.string,
        required: true,
        label: getLang('USING_ORG'),
      },
    ],
    dataToJSON: DataToJSON.all,
    transport: {
      read: config => {
        const { params } = config;
        let { data } = config;
        data = {
          organizationId,
          ...data,
        };
        const url = `${HALM_ATN}/v1/${organizationId}/tp-change-headers/${data.changeHeaderId}`;
        return {
          data,
          params,
          url,
          method: 'GET',
        };
      },
      submit: ({ params, data }) => {
        const temp = data[0];
        const url = `${HALM_ATN}/v1/${organizationId}/tp-change-headers`;
        return {
          url,
          data: { ...temp, headerAndLineSaveFlag: isNil(temp?.changeHeaderId) ? 1 : 0 },
          params,
          method: isNil(temp?.changeHeaderId) ? 'POST' : 'PUT',
        };
      },
    },
    events: {
      loadFailed({ dataSet }) {
        dataSet.loadData([{}]);
      },
    },
  };
}

// 资产事务行
function lineDS(): DataSetProps {
  return {
    selection: false,
    autoQuery: false,
    pageSize: 10,
    modifiedCheck: false, // 关闭翻页时数据修改提示
    autoQueryAfterSubmit: false, // ! 这里设置为false是因为行查询接口比较慢，行数据删除的时候界面要等好一会才会移除，但是行又不会显示loading状态，所以不开启提交后自动删除
    fields: [
      {
        name: 'lineNum',
        type: FieldType.string,
        label: getLang('LINE_NUM'),
      },
      {
        name: 'objectNum',
        type: FieldType.string,
        computedProps: {
          label: ({ dataSet }) => {
            return dataSet?.getState('basicCode') === 'ACQUISITION'
              ? getLang('OBJECT_NUM')
              : getLang('ASSET_EQP_NUM');
          },
        },
      },
      {
        name: 'objectDesc',
        type: FieldType.string,
        computedProps: {
          label: ({ dataSet }) => {
            return dataSet?.getState('basicCode') === 'ACQUISITION'
              ? getLang('OBJECT_NAME')
              : getLang('ASSET_DESC');
          },
        },
      },
      {
        name: 'description',
        type: FieldType.string,
        label: getLang('CHANGE_CONTENT'),
      },
      {
        name: 'processStatus',
        type: FieldType.string,
        lookupCode: 'AATN.RETURN_HLINE_STATUS',
        label: getLang('PROCESS_STATUS'),
      },
      {
        name: 'processStatusMeaning',
        type: FieldType.string,
        label: getLang('PROCESS_STATUS'),
      },
      {
        name: 'changeLineId',
        type: FieldType.number,
      },
      {
        name: 'oldAssetStatus',
        type: FieldType.object,
        label: getLang('OLD_ASSET_STATUS'),
        lovCode: 'AAFM.ASSET_STATUS',
        ignore: FieldIgnore.always,
      },
      {
        name: 'oldObjectStatusId',
        type: FieldType.number,
        bind: 'oldAssetStatus.assetStatusId',
      },
      {
        name: 'oldAssetStatusName',
        type: FieldType.string,
        label: getLang('OLD_ASSET_STATUS'),
        bind: 'oldAssetStatus.assetStatusName',
      },
      {
        name: 'newAssetStatus',
        type: FieldType.object,
        lovCode: 'AAFM.ASSET_STATUS',
        lovPara: {},
        label: getLang('NEW_ASSET_STATUS'),
        ignore: FieldIgnore.always,
        dynamicProps: {
          lovPara: ({ dataSet }) => {
            // typeData类型详情接口：返回的数据格式不一致 "[120037,120155,120040,120279]" 或者 "120037,120155,120040,120279"
            const typeData = dataSet.getState('typeData');
            const jsonFlag = isJSON(typeData?.targetAssetStatusScope);
            const tmpScope = jsonFlag
              ? JSON.parse(typeData?.targetAssetStatusScope).join(',')
              : typeData?.targetAssetStatusScope;
            return {
              targetAssetStatusScope: tmpScope,
            };
          },
        },
      },
      {
        name: 'newObjectStatusId',
        type: FieldType.number,
        bind: 'newAssetStatus.assetStatusId',
      },
      {
        name: 'newAssetStatusName',
        type: FieldType.string,
        label: getLang('NEW_ASSET_STATUS'),
        bind: 'newAssetStatus.assetStatusName',
      },
      {
        name: 'financialNum',
        type: FieldType.string,
        label: getLang('FINANCIAL_NUM'),
      },
      {
        name: 'attributeValue', // 属性组
        type: FieldType.string,
      },
      // ! 资产领用单界面显示的字段
      {
        name: 'requisitionsNumber',
        type: FieldType.number,
        label: getLang('NEED_QUANTITY'),
        defaultValue: 1,
        required: true,
        maxLength: 20,
        dynamicProps: {
          required: ({ dataSet, record }) => {
            const typeData = dataSet.getState('typeData');
            const objectType = record.get('objectType');
            return typeData?.basicTypeCode === 'ACQUISITION' && objectType !== 'ASSET';
          },
        },
      },
      {
        label: getLang('RECEIPT_OBJ_TYPE'),
        name: 'objectType',
        lookupCode: 'HALM.RECIPIENTS',
      },
      {
        label: getLang('DEMAND_DATE'),
        name: 'demandDate',
        type: FieldType.date,
      },
    ],
    events: {
      // load: ({ dataSet }) => {
      //   handleSetExtraFieldsLine(dataSet);
      // },
    },
    transport: {
      read: ({ data }) => {
        const url = `${HALM_ATN}/v1/${organizationId}/tp-change-lines/list/${data.changeHeaderId}`;
        return {
          url,
          method: 'GET',
        };
      },
      destroy: ({ data, dataSet }) => {
        const { lineId } = data[0];
        const headData = dataSet?.getState('headData') ?? {};
        return {
          url: `${HALM_ATN}/v1/${organizationId}/tp-change-lines/${lineId}`,
          data: headData,
          method: 'DELETE',
        };
      },
    },
  };
}

function assetTransactionTypeDS(): DataSetProps {
  return {
    autoQuery: false,
    transport: {
      read: ({ data }) => {
        const url = `${HALM_ATN}/v1/${organizationId}/transaction-type/${data.transactionTypeId}`;
        return {
          url,
          method: 'GET',
        };
      },
    },
  };
}
export { detailDS, lineDS, assetTransactionTypeDS };
