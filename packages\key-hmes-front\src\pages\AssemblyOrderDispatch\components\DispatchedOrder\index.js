/* eslint-disable jsx-a11y/alt-text */
import React, { Fragment, useState, useRef, useEffect } from 'react';
import { Table, Icon, Progress, Spin, Tooltip, Modal, Select } from 'choerodon-ui/pro';
import { Popconfirm, Tag, Popover } from 'choerodon-ui';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import { isEmpty, uniqBy } from 'lodash';
import arrowheadGreen from '@/assets/icons/arrowhead_green.png';
import arrowheadRed from '@/assets/icons/arrowhead_red.png';
import bookGreen from '@/assets/icons/book_green.png';
import boxGreen from '@/assets/icons/box_green.png';
import timesGreen from '@/assets/icons/times_green.png';
import computeGreen from '@/assets/icons/compute_green.png';
import formatterCollections from 'utils/intl/formatterCollections';
import './index.module.less';
// import styles from './index.modules.less';

const modelPrompt = 'common.hmes.apiMonitor';
// const { Panel } = Collapse;
const { Option } = Select;

const MonitorCard = props => {
  const {
    dataSet,
    rightWoInfoList,
    handlePendingCancel,
    handlePending,
    dispatchedSpin,
    handleDragEndEd,
    handleSearchOrder,
    handleFetchHistory,
    handelAddRemark,
    handelAddtab,
    handelDelTag,
    dispatchDataSelectTag,
  } = props;

  const [selectTag, setSelectTag] = useState([]); // 标签筛选选择数据

  const selectRef = useRef();
  useEffect(() => {
    selectRef.current = selectTag;
  }, [selectTag]);

  const handlePendCal = () => {
    if (handlePendingCancel) {
      handlePendingCancel();
    }
  };
  const handlePend = () => {
    if (handlePending) {
      handlePending();
    }
  };

  const handleSearch = record => {
    if (handleSearchOrder) {
      handleSearchOrder(record.data);
    }
  };

  const handleFetch = value => {
    if (handleFetchHistory) {
      handleFetchHistory(value);
    }
  };

  const handelRemark = () => {
    if (handelAddRemark) {
      handelAddRemark('dis');
    }
  };

  const handeltab = () => {
    if (handelAddtab) {
      handelAddtab('dis');
    }
  };

  const delTag = () => {
    if (handelDelTag) {
      handelDelTag('dis');
    }
  };

  const changeTag = value => {
    if (value) {
      setSelectTag(value);
    }
  };

  const handleFilter = () => {
    const data = uniqBy(rightWoInfoList, 'tag').filter(v => v.tag);
    if (data.length > 0) {
      Modal.open({
        title: '标签筛选',
        destroyOnClose: true,
        closable: true,
        children: (
          <Select
            placeholder="请选择筛选标签"
            required
            clearButton
            multiple
            onChange={changeTag}
            style={{ width: '100%' }}
          >
            {data.map(item => (
              <Option key={item.workOrderId} value={item.tag}>
                {item.tag}-{item.tagDesc}
              </Option>
            ))}
          </Select>
        ),
        onOk: onFetchRemake,
      });
    }
  };

  const onFetchRemake = () => {
    if (selectRef.current.length > 0) {
      const data = [];
      selectRef.current.forEach(item => {
        rightWoInfoList.forEach(v => {
          if (v.tag === item) {
            data.push(v);
          }
        });
      });
      dataSet.loadData(data);
    }
  };

  // 头table的columns
  const columns = [
    {
      name: 'workOrderNum',
      width: 150,
      header: record => (
        <span>
          <Icon onClick={handleFilter} type="filter_alt" />
          &nbsp;
          {record.title}
        </span>
      ),
      renderer: ({ value, record }) => {
        // return <a onClick={() => handleSearch(record)}>{value}</a>;
        return (
          <div>
            {record.get('tag') && <Icon style={{ color: record.get('tag') }} type="emoji_flags" />}
            {
              record.get('unBackFlag') === 'Y' ? (
                <Popover content='已执行首步骤排队，不可派工撤回！'>
                  <a
                    style={{ color: '#f9b737' }}
                    onClick={() => handleSearch(record)}
                  >
                    {value}
                  </a>
                </Popover>
              ) : (
                <a
                  style={{ color: record.data.status === 'HOLD' ? '#8c8c8c' : '' }}
                  onClick={() => handleSearch(record)}
                >
                  {value}
                </a>
              )
            }
          </div>
        );
      },
    },
    {
      name: 'statusDesc',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'customerCode',
    },
    {
      name: 'customerName',
    },
    {
      name: 'customerNameAlt',
    },
    {
      name: 'qty',
    },
    {
      name: 'taskProcess',
      width: 220,
      renderer: ({ value }) => (
        <div>
          <span style={{ display: 'inline-block', width: 30 }}>{value}</span>
          <Progress value={Number(value)} />
        </div>
      ),
    },
    {
      name: 'dto5',
      width: 150,
      renderer: ({ record }) => {
        return (
          <div>
            <Tooltip theme="dark" placement="top" title="执行异常">
              <img
                style={{ marginRight: 10 }}
                src={record.data.workOrderStatus !== 'RELEASED' ? arrowheadGreen : arrowheadRed}
              ></img>
            </Tooltip>
            <Tooltip theme="dark" placement="top" title="紧急变更">
              <img style={{ marginRight: 10 }} src={bookGreen}></img>
            </Tooltip>
            <Tooltip theme="dark" placement="top" title="物料异常">
              <img style={{ marginRight: 10 }} src={boxGreen}></img>
            </Tooltip>
            <Tooltip theme="dark" placement="top" title="进度异常">
              <img style={{ marginRight: 10 }} src={timesGreen}></img>
            </Tooltip>
            <Tooltip theme="dark" placement="top" title="设备异常">
              <img src={computeGreen}></img>
            </Tooltip>
          </div>
        );
      },
    },
    {
      name: 'remark',
    },
    {
      name: 'taskRemark',
      width: 100,
      renderer: ({ record }) => {
        return record.data.remarkList?.map(v => {
          return <Tag onClick={() => handleFetch(record.data)}>{v}</Tag>;
        });
      },
    },
    {
      name: 'planStartTime',
      width: 150,
    },
    {
      name: 'planEndTime',
      width: 150,
    },
    {
      name: 'woHistory',
      renderer: ({ record }) => {
        return <a onClick={() => handleFetch(record.data)}>工单历史</a>;
      },
    },
    {
      name: 'completedQty',
    },
    {
      name: 'scrappedQty',
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'actualStartDate',
      width: 150,
    },
    {
      name: 'actualEndDate',
      width: 150,
    },
  ];

  const handleDragEnd = async (dataSet, _, resultDrag) => {
    const { source = {}, destination = {} } = resultDrag;
    if (!isEmpty(destination) && source.index !== destination.index) {
      const capacityList = [];
      capacityList.push({
        changeWo: dataSet.data[destination.index].data,
        downWo: dataSet.data[destination.index + 1] ? dataSet.data[destination.index + 1].data : {},
        upWo: dataSet.data[destination.index - 1] ? dataSet.data[destination.index - 1].data : {},
      });
      if (handleDragEndEd) {
        handleDragEndEd(capacityList[0]);
      }
    }
  };

  return (
    <Fragment>
      <Spin spinning={dispatchedSpin}>
        <Header
          key="basicInfo"
          className="DispatchedOrderTitle"
          title={
            <div style={{ position: 'relative' }}>
              <span>{intl.get('tarzan.aps.statistics.checked').d('已派工单')}&nbsp;&nbsp;</span>
              <span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 32,
                  width: 100,
                  fontSize: 12,
                  zIndex: 10,
                }}
              >
                {intl.get('tarzan.aps.statistics.total').d('已派工单总数：')}
                {dataSet.toData().length}
              </span>
              <span className="WillDispatchOrder_title"></span>
            </div>
          }
        >
          <span className="DispatchedOrder_title">
            <a funcType="raised" color="blue" onClick={handelRemark}>
              {intl.get(`${modelPrompt}.api.name`).d('备注')}
            </a>
            <a
              hidden={
                dispatchDataSelectTag.length === 0 ||
                dispatchDataSelectTag.includes(null) ||
                dispatchDataSelectTag.includes('')
              }
              funcType="raised"
              color="blue"
              onClick={delTag}
            >
              {intl.get(`${modelPrompt}.api.delete`).d('删除标记')}
            </a>
            <a funcType="raised" color="yellow" onClick={handeltab}>
              {intl.get(`${modelPrompt}.api.name`).d('标记')}
            </a>
            <Popconfirm
              title={intl
                .get(`tarzan.common.message.confirm.handlePendCal`)
                .d(' 确认将选中工单恢复为暂挂前状态?')}
              onConfirm={handlePendCal}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <a funcType="raised" color="blue">
                {intl.get(`${modelPrompt}.api.pendcal`).d('暂挂取消')}
              </a>
            </Popconfirm>
            <Popconfirm
              title={intl
                .get(`tarzan.common.message.confirm.handlePend`)
                .d('确认将选中工单更新为状态暂挂状态?')}
              onConfirm={handlePend}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <a>{intl.get(`${modelPrompt}.api.pend`).d('暂挂')}</a>
            </Popconfirm>
          </span>
        </Header>
        <Content>
          {/* <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}> */}

          <Table
            pagination={false}
            spin={{ size: 'large', spinning: false }}
            dataSet={dataSet}
            rowDraggable
            dragDropContextProps={{
              onDragEnd: (dataSet, columns, resultDrag) => {
                handleDragEnd(dataSet, columns, resultDrag);
              },
            }}
            style={{ height: document.body.clientHeight - 300 }}
            customizedCode="DispatchedOrder"
            columns={columns}
          />
        </Content>
      </Spin>
    </Fragment>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(MonitorCard);
