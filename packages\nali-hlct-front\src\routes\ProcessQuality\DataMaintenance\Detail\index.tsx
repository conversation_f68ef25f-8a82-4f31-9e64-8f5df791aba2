import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Table, Modal, Form, TextField, Select, Lov, Button, Icon } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { Collapse, Popconfirm, Tag } from 'choerodon-ui';
import { ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnAlign } from 'choerodon-ui/pro/es/table/enum';
import { C7nFormItemSort } from '@components/tarzan-ui';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import {
  detailBasicDS,
  defectiveContentDS,
  defectFormDS,
  viewDS,
} from '../stores/DataMaintenanceDS';

import { queryUnifyIdpValue } from '@/services/api';

import { saveTeardownConfig, fetchTeardownConfig, updateTeardownConfig } from '../services';

const modelPrompt = 'tarzan.hlct.dataMaintenance';

const { Panel } = Collapse;

const DataMaintenaceDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    history,
  } = props;

  // 是否可编辑
  const [canEdit, setCanEdit] = useState(id === 'create');

  // 拆解位置列表 每元素 包含了表单和表格两个 DS
  const [tableList, setTableList]: any = useState([]);
  // 缺陷等级值含义
  const [ncLevelMap, setNcLevelMap] = useState({});
  // 拆解位置折叠面板key
  const [activeKey, setActiveKey]: any = useState([]);
  const detailBasicDs = useMemo(() => new DataSet(detailBasicDS()), []);
  const viewDs = useMemo(() => new DataSet(viewDS()), []);

  // 保存产品拆解基础数据
  const saveTeardown = useRequest(saveTeardownConfig(), {
    manual: true,
    needPromise: true,
  });
  // 更新产品拆解基础数据
  const updateTeardown = useRequest(updateTeardownConfig(), {
    manual: true,
    needPromise: true,
  });

  // 查询产品拆解基础数据信息
  const fetchTeardown = useRequest(fetchTeardownConfig(), {
    manual: true,
    needPromise: true,
  });

  // 页面初始化
  useEffect(() => {
    // 获取不良等级 独立值集信息
    getNcLevel();
    if (id === 'create') {
      setTableList([]);
      setCanEdit(true);
      detailBasicDs.loadData([{}]);
    } else {
      initPage(id);
    }
  }, [id]);

  // 获取不良等级 独立值集信息
  const getNcLevel = () => {
    queryUnifyIdpValue('YP.QIS.TEARDOWN_NC_LEVEL').then(res => {
      const _ncLevelMap = {};
      if (res?.length > 0) {
        res.forEach(item => {
          _ncLevelMap[item.value] = item;
        });
      }
      setNcLevelMap(_ncLevelMap);
    });
  };

  // 加载详情
  const initPage = async key => {
    setCanEdit(false);
    setTableList([]);
    detailBasicDs.loadData([{}]);

    const res = await fetchTeardown.run({
      tempUrl: fetchTeardownConfig(key).url,
    });

    const _tableList: any = [];
    const _activeKey: any = [];
    if (res) {
      const { qisTeardownLocationList, ...other } = res;
      detailBasicDs.loadData([other]);
      qisTeardownLocationList.forEach(item => {
        const defectiveContentDs = new DataSet(defectiveContentDS());
        const formDs = new DataSet({
          ...defectFormDS(),
          children: {
            qisTeardownNcList: defectiveContentDs || [],
          },
        });
        const _uuid = uuid();
        formDs.loadData([{ ...item }]);
        _tableList.push({ formDs, defectiveContentDs, uuid: _uuid });
        _activeKey.push(_uuid);
      });
    }
    setTableList(_tableList);
    setActiveKey(_activeKey);
  };

  // 拆解位置列表新增行
  const handleAddLine = rendererProps => {
    const { dataSet } = rendererProps;
    dataSet.create({});
  };

  // 拆解位置列表删除
  const deleteRecord = rendererProps => {
    const { record, dataSet } = rendererProps;
    dataSet.remove(record);
  };

  // 缺陷等级详情行添加
  const handleAddLineView = () => {
    viewDs.create({});
  };

  // 缺陷等级详情行删除
  const deleteRecordView = rendererProps => {
    const targetRecord = rendererProps?.record;
    viewDs.remove(targetRecord);
  };

  // 增加缺陷内容卡片
  const handleAddTable = () => {
    const defectiveContentDs = new DataSet(defectiveContentDS());
    const formDs = new DataSet({
      ...defectFormDS(),
      children: {
        qisTeardownNcList: defectiveContentDs,
      },
    });
    formDs.loadData([
      {
        qisTeardownNcList: [],
      },
    ]);
    const _uuid = uuid();
    setTableList([...tableList, { formDs, defectiveContentDs, uuid: _uuid }]);
    setActiveKey([...activeKey, _uuid]);
  };

  // 删除缺陷内容
  const handleDelete = focusIndex => {
    const _tableList: any = [];
    tableList.forEach((item, index) => {
      if (`${focusIndex}` !== `${index}`) _tableList.push(item);
    });
    setTableList(_tableList);
  };

  // 显示权限等级详情modal
  const handleView = record => {
    const _qisTeardownNcLevelList = record.get('qisTeardownNcLevelList') || [];
    viewDs.loadData(_qisTeardownNcLevelList);
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.title.lvAndGrade`).d('缺陷等级与评分'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      children: <Table dataSet={viewDs} columns={viewColumns} />,
      footer: (okBtn, cancelBtn) => {
        return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
      },
      onOk: async () => {
        return handleOk(record);
      },
    });
  };

  // 权限等级详情确定
  const handleOk = async record => {
    const validateResult = await viewDs.validate();
    if (!validateResult) {
      return false;
    }

    const qisTeardownNcLevelList = viewDs.toData();
    if (qisTeardownNcLevelList.length === 0) {
      record.set('qisTeardownNcLevelList', null);
    } else {
      record.set('qisTeardownNcLevelList', qisTeardownNcLevelList);
    }
  };

  // 权限等级详情列表配置
  const viewColumns: ColumnProps[] = [
    {
      name: 'option',
      hidden: !canEdit,
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleAddLineView}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: rendererProps => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecordView(rendererProps)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'teardownNcLevelCode',
      editor: canEdit,
    },
    {
      name: 'teardownNcScore',
      editor: canEdit,
    },
  ];

  // 缺陷内容列表配置
  const columns: ColumnProps[] = [
    {
      hidden: !canEdit,
      header: rendererProps => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={() => handleAddLine(rendererProps)}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: rendererProps => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(rendererProps)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'seq',
      width: 60,
      align: ColumnAlign.left,
      renderer: ({ record }) => {
        return (record?.index || 0) * 10 + 10;
      },
    },
    {
      name: 'ncCodeIdLov',
      editor: canEdit,
    },
    {
      name: 'description',
    },
    {
      name: 'qisTeardownNcLevelList',
      renderer: ({ value }) => {
        return (value || []).map(item => (
          <Tag>
            {ncLevelMap[item.teardownNcLevelCode]?.meaning}/{item.teardownNcScore}
          </Tag>
        ));
      },
    },
    {
      name: '',
      header: intl.get(`${modelPrompt}..teable.header.qisTeardownNcLevelList`).d('缺陷等级'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      renderer: ({ record }) => {
        return (
          <div>
            <PermissionButton
              permissionList={[
                {
                  code: `${path}.button.view `,
                  type: 'button',
                  meaning: '列表页-查看',
                },
              ]}
              type="text"
              onClick={() => handleView(record)}
            >
              {intl.get(`${modelPrompt}.button.look`).d('查看')}
            </PermissionButton>
          </div>
        );
      },
    },
  ];

  const handleChangeSite = () => {
    detailBasicDs.current?.init('sipFileLov', undefined);
  };

  // 校验所有ds
  const validateAllDs = async () => {
    // 记录是否未添加缺陷内容
    let haveEmptyPosition = true;
    // 记录缺陷内容列表是否为添加行
    let haveEmptyPositionTable = true;

    // 所有表单校验集合
    const validateDsListNormal: any = [];

    if (tableList.length === 0) {
      haveEmptyPosition = false;
    }

    validateDsListNormal.push(detailBasicDs);
    tableList.forEach(panelItem => {
      const { formDs, defectiveContentDs } = panelItem;
      validateDsListNormal.push(formDs);
      validateDsListNormal.push(defectiveContentDs);
      if (defectiveContentDs.length === 0) {
        haveEmptyPositionTable = false;
      }
    });

    // 校验所有表单
    const normalValidate = await Promise.all(
      validateDsListNormal.map(async validateDsListItem => {
        const itemValidate = await validateDsListItem.validate();
        return itemValidate;
      }),
    );

    // 汇总校验结果
    const normalResult = normalValidate.every(val => val);

    if (!haveEmptyPosition) {
      notification.error({
        message: intl.get(`${modelPrompt}.haveEmptyPosition`).d(`请添加拆解位置`),
      });
    }

    if (!haveEmptyPositionTable) {
      notification.error({
        message: intl.get(`${modelPrompt}.haveEmptyPositionTable`).d(`请添加缺陷数据`),
      });
    }

    // 返回校验结果
    return normalResult && haveEmptyPosition && haveEmptyPositionTable;
  };

  const handleSave = async () => {
    const pageValidate = await validateAllDs();

    if (!pageValidate) {
      return;
    }

    const detailBasicInfo = detailBasicDs.toData()[0];
    const qisTeardownLocationList: any = [];
    tableList.forEach(item => {
      qisTeardownLocationList.push(item.formDs.toData()[0]);
    });

    const params = {
      ...detailBasicInfo,
      qisTeardownLocationList,
    };

    let res;

    if (id === 'create') {
      res = await saveTeardown.run({
        params,
      });
    } else {
      res = await updateTeardown.run({
        params,
      });
    }

    if (res?.teardownHeadId) {
      notification.success({});
      if (id === 'create') {
        props.history.push(
          `/hwms/product-disassembly-data-maintenance/detail/${res.teardownHeadId}`,
        );
      } else {
        initPage(id);
      }
    }
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push(`/hwms/product-disassembly-data-maintenance/list`);
    } else {
      initPage(id);
    }
  };

  const handleEdit = () => {
    setCanEdit(true);
  };

  const handleChangePanel = value => {
    setActiveKey(value);
  };

  const handleJump = () => {
    history.push({
      pathname: '/hwms/sip-file-manage/list',
      state: { sipCode: detailBasicDs.current?.get('sipCode') },
    });
  };

  const getColor = (name, canEdit) => {
    if (!detailBasicDs.current?.get(name) || canEdit) {
      return `rgba(0,0,0,0.25)`;
    }
    return '#29bece';
  };

  return (
    <div className="hmes-style" style={{ height: '100%' }}>
      <Header
        title={intl.get(`${modelPrompt}.title`).d('产品拆解基础数据维护')}
        backPath="/hwms/product-disassembly-data-maintenance/list"
      >
        {canEdit ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <>
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          </>
        )}
      </Header>
      <Content>
        <Collapse
          collapsible="icon"
          bordered={false}
          defaultActiveKey={['panel1', 'panel2', 'panel3']}
        >
          <Panel header={intl.get(`${modelPrompt}.basicInfo`).d('基础信息')} key="panel1">
            <Form dataSet={detailBasicDs} columns={3} labelWidth={110} disabled={!canEdit}>
              <Lov name="siteNameLov" onChange={handleChangeSite} />
              <Select name="productFormCode" />
              <C7nFormItemSort name="sipFileLov" itemWidth={['92%', '7%']}>
                <Lov name="sipFileLov" />
                <Icon
                  type="link2"
                  // @ts-ignore
                  itemType="link"
                  onClick={handleJump}
                  style={{ color: getColor('sipCode', canEdit) }}
                  iconDisabled
                />
              </C7nFormItemSort>
            </Form>
          </Panel>

          <Panel
            header={intl.get(`${modelPrompt}.defectiveContent`).d('缺陷内容')}
            key="panel2"
            extra={
              canEdit && (
                <PermissionButton
                  funcType="flat"
                  style={{
                    marginLeft: 450,
                  }}
                  onClick={handleAddTable}
                >
                  {intl.get(`${modelPrompt}.addPosition`).d('新增拆解位置')}
                </PermissionButton>
              )
            }
          >
            <Collapse
              activeKey={activeKey}
              collapsible="icon"
              bordered={false}
              style={{ marginLeft: 30 }}
              onChange={handleChangePanel}
            >
              {tableList?.map((item, index) => (
                <Panel
                  header={intl.get(`${modelPrompt}.defectiveContent`).d('拆解位置')}
                  key={item.uuid}
                  style={{ marginTop: 50 }}
                  extra={
                    canEdit && (
                      <Popconfirm
                        title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                        onConfirm={() => handleDelete(index)}
                        okText={intl.get('tarzan.common.button.confirm').d('确认')}
                        cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                      >
                        <PermissionButton
                          type="c7n-pro"
                          icon="remove_circle_outline"
                          funcType="flat"
                          shape="circle"
                        />
                      </Popconfirm>
                    )
                  }
                >
                  <Form dataSet={item.formDs} columns={3} disabled={!canEdit}>
                    <TextField name="teardownLocationDesc" />
                  </Form>
                  <Table dataSet={item.defectiveContentDs} columns={columns} />
                </Panel>
              ))}
            </Collapse>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hlct.dataMaintenance', 'tarzan.common'],
})(DataMaintenaceDetail);
