import React, { FC, useEffect, useMemo } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge, Tag, Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { TableHeadDS, TableLineDS } from '../stores';

const modelPrompt = 'tarzan.hwms.msaAnalysisManagementPlatform';
const { Panel } = Collapse;

interface MsaAnalysisManagementPlatformListProps extends RouteComponentProps {
  tableHeadDs: DataSet;
  tableLineDs: DataSet;
}

const MsaAnalysisManagementPlatformList: FC<MsaAnalysisManagementPlatformListProps> = ({
  match: { path },
  tableHeadDs,
  tableLineDs,
  history,
}) => {
  useEffect(() => {
    tableHeadDs.query();
  }, []);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'msaCode',
        width: 150,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hwms/msa-analysis-management/platform/detail?primaryId=${record!.get('inspectItemId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'msaStatus',
        renderer: ({ value }) => {
          return (
            <Tag className="hcm-tag-blue" key={value}>
              {value}
            </Tag>
          );
        },
      },
      { name: 'siteName' },
      { name: 'msaType' },
      { name: 'projectStage' },
      { name: 'projectName' },
      { name: 'qualityCharacteristic' },
      { name: 'specialCharacteristic' },
      { name: 'modelCode' },
      { name: 'modelName' },
      { name: 'speciesName' },
      {
        name: 'msaAnalysisMethod',
        width: 200,
        renderer: ({ value }) => {
          if (value?.length) {
            return value.map(item => {
              return (
                <Tag className="hcm-tag-blue" key={item}>
                  {item}
                </Tag>
              );
            });
          }
        },
      },
      { name: 'toolCode' },
      { name: 'prodlineName' },
      { name: 'workcellName' },
      { name: 'onlineFlag' },
      { name: 'analyzedName' },
      { name: 'assistantName' },
      { name: 'completeTimeLimit' },
      { name: 'planStartTime' },
      { name: 'planEndTime' },
      { name: 'actualStartTime' },
      { name: 'actualFinishTime' },
      { name: 'msaResult' },
      { name: 'sourceMsaTaskCode' },
      { name: 'improveName' },
      { name: 'creationDate' },
      { name: 'creationName' },
      {
        title: '操作',
        width: 160,
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        renderer: ({ record }) => (
          <>
            <a
              onClick={() => {
                history.push(`/hwms/msa-analysis-management/platform/analysisDetail?primaryId=${record!.get('inspectItemId')}`);
              }}
            >
              {intl.get('hzero.common.button.analysisDetail').d('分析详情')}
            </a>
            <a
              style={{ marginLeft: 10 }}
              onClick={() => {
                history.push(`/hwms/inspect-item-maintenance/dist/${record!.get('inspectItemId')}`);
              }}
            >
              {intl.get('hzero.common.button.sipAttachment').d('SIP附件')}
            </a>
          </>
        ),
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'msaAnalysisMethod',
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          return (
            <Tag className="hcm-tag-blue" key={value}>
              {value}
            </Tag>
          );
        },
      },
      { name: 'msaResult', align: ColumnAlign.center },
      { name: 'msaConclusion', align: ColumnAlign.center },
      { name: 'analyzeDate', align: ColumnAlign.center },
      {
        name: 'analyzeStatus',
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.analyze`).d('已分析')
                : intl.get(`tarzan.common.label.noAnalyze`).d('未分析')
            }
          />
        ),
      },
    ];
  }, []);

  const handleAdd = () => {
    history.push(`/hwms/msa-analysis-management/platform/create`);
  };

  return (
    <div className="hmes-style">
      <Header title="MSA分析管理平台">
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.create.msa`,
              type: 'button',
              meaning: '列表页-编辑新建按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.createMsa').d('新建MSA任务')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.cancel.msa`,
              type: 'button',
              meaning: '列表页-取消按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.cancelMsa').d('取消MSA任务')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableHeadDs}
          columns={columns}
          searchCode="MsaAnalysisManagementPlatform"
          customizedCode="MsaAnalysisManagementPlatform"
          onRow={({ record }) => ({
            onClick: () => {
              tableLineDs.setQueryParameter('a', record.get('a'));
              tableLineDs.query();
            },
          })}
        />
        <Collapse bordered={false} defaultActiveKey={['lineInfo']}>
          <Panel
            key="lineInfo"
            header={intl.get(`${modelPrompt}.title.msaAnalysisContent`).d('MSA分析内容')}
          >
            <Table
              queryBar={TableQueryBarType.none}
              dataSet={tableLineDs}
              columns={lineColumns}
              pagination={false}
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableHeadDs = new DataSet({
        ...TableHeadDS(),
      });
      const tableLineDs = new DataSet({
        ...TableLineDS(),
      });
      return {
        tableHeadDs,
        tableLineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MsaAnalysisManagementPlatformList as any),
);
