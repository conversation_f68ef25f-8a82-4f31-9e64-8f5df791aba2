import React, { useState, useEffect } from 'react';
import { Table, TextField, Select, Attachment } from 'choerodon-ui/pro';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';

const InspectItemTab = props => {
  const { canEdit, tableDS, tableStatus, reviewType } = props;
  const initColumns: ColumnProps[] = [
    {
      name: 'elementNum',
      align: ColumnAlign.center,
    },
    {
      name: 'statusMeaning',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewDimensionMeaning',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewItem',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewContent',
      align: ColumnAlign.center,
    },
    {
      name: 'deliveryName',
      align: ColumnAlign.center,
    },
    {
      name: 'deliveryTemplate',
      align: ColumnAlign.center,
    },
    {
      name: 'responsibleDeptName',
      align: ColumnAlign.center,
    },
    {
      name: 'responsibleEmName',
      align: ColumnAlign.center,
    },
    {
      name: 'scheFinishTime',
      align: ColumnAlign.center,
    },
    {
      name: 'deliveryUuid',
      align: ColumnAlign.center,
      editor: () => canEdit && <Attachment {...attachmentProps} />,
    },
  ];
  const [columns, setColumns] = useState<ColumnProps[]>([
    ...initColumns,
    {
      name: 'applyFlag',
      editor: () => canEdit && <Select name="applyFlag" required />,
    },
    {
      name: 'noApplyReason',
      editor: () =>
        canEdit && <TextField name="noApplyReason" maxLength={255} showLengthInfo required />,
    },
    {
      name: 'actualFinishTime',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewResult',
      align: ColumnAlign.center,
    },
    {
      name: 'nonConTerm',
      align: ColumnAlign.center,
    },
    {
      name: 'releaseClass',
      align: ColumnAlign.center,
    },
    {
      name: 'closeDate',
      align: ColumnAlign.center,
    },
  ]);

  useEffect(() => {
    const data: ColumnProps[] = initColumns;

    if (reviewType === 'PRODLINE_RELEASE') {
      data.push({
        name: 'applyFlag',
        editor: () => canEdit && <Select name="applyFlag" required />,
      });
      data.push({
        name: 'noApplyReason',
        editor: () =>
          canEdit && <TextField name="noApplyReason" maxLength={255} showLengthInfo required />,
      });
      tableDS.getField('applyFlag').set('required', true);
    } else {
      tableDS.getField('applyFlag').set('required', false);
      tableDS.getField('noApplyReason').set('required', false);
    }

    data.push({
      name: 'actualFinishTime',
      align: ColumnAlign.center,
    });
    data.push({
      name: 'reviewResult',
      align: ColumnAlign.center,
    });
    data.push({
      name: 'nonConTerm',
      align: ColumnAlign.center,
    });
    if (reviewType === 'PRODLINE_RELEASE') {
      data.push({
        name: 'releaseClass',
        align: ColumnAlign.center,
      });
    }
    if (tableStatus === 'COMPLETED') {
      data.push({
        name: 'closeDate',
        align: ColumnAlign.center,
      });
    }
    setColumns(data);
  }, [canEdit, reviewType]);

  // 附件配置
  const attachmentProps: any = {
    name: 'deliveryUuid',
    bucketName: 'qms',
    bucketDirectory: 'prodline_template_management_platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  return (
    <Table
      dataSet={tableDS}
      customizable
      customizedCode="customized"
      columns={columns}
      highLightRow
      pagination={false}
    />
  );
};

export default InspectItemTab;
