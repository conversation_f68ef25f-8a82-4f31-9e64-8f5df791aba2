/**
 * @Description: 体系审核管理维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.systemAudit';
const tenantId = getCurrentOrganizationId();

// 详情-审核小组表单
const auditGroupFormDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.groupNumber`).d('小组序号'),
      name: 'groupNumber',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.groupDescription`).d('小组名称'),
      name: 'groupDescription',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.groupLeader`).d('组长'),
      name: 'groupLeaderLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'groupLeader',
      bind: 'groupLeaderLov.id',
    },
    {
      name: 'groupLeaderName',
      bind: 'groupLeaderLov.realName',
    },
  ],
});

// 详情-审核小组列表
const auditGroupTableDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.groupUser`).d('小组成员'),
      name: 'groupUserLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'groupMemId',
      bind: 'groupUserLov.userId',
    },
    {
      name: 'groupMemName',
      bind: 'groupUserLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userWork`).d('工号'),
      name: 'groupMemEmployeeName',
      bind: 'groupUserLov.employeeNum',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.phone`).d('联系方式'),
      name: 'groupMemPhone',
      bind: 'groupUserLov.phone',
      disabled: true,
    },

    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dapartment`).d('所属部门'),
      name: 'departmentName',
      bind: 'groupUserLov.unitName',
      disabled: true,
    },
    {
      name: 'departmentId',
      bind: 'groupUserLov.unitId',
    },
    
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.position`).d('岗位'),
      name: 'positionName',
      bind: 'groupUserLov.positionName',
      disabled: true,
    },
    {
      name: 'positionId',
      bind: 'groupUserLov.positionId',
    },
  ],
});

export { auditGroupFormDS, auditGroupTableDS };
