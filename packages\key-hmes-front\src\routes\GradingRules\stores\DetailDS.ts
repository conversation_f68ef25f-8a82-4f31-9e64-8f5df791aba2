import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('工厂描述'),
      bind: 'siteLov.siteName',
      readOnly: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
      readOnly: true,
    },
    {
      name: 'tagLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagCode`).d('采集项'),
      lovCode: 'HME_MT_TAG',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'tagId',
      bind: 'tagLov.tagId',
    },
    {
      name: 'tagCode',
      bind: 'tagLov.tagCode',
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('采集项描述'),
      bind: 'tagLov.tagDescription',
      readOnly: true,
    },
    {
      name: 'ruleSort',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.ruleSort`).d('顺序'),
      min: 0,
      precision: 0,
      required: true,
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

const tableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'ruleLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ruleLevel`).d('等级'),
      lookupCode: 'APEX_MES.GRADING_RULE_LEVEL',
    },
    {
      name: 'datavalue',
      label: intl.get(`${modelPrompt}.datavalue`).d('符合值'),
    },
    {
      name: 'multipleValue',
      range: ['leftvalue', 'rightvalue'],
    },
    {
      name: 'leftchar',
      type: FieldType.string,
      defaultValue: '[',
    },
    {
      name: 'leftvalue',
      type: FieldType.number,
      bind: 'multipleValue.leftvalue',
      defaultValue: null,
    },
    {
      name: 'rightchar',
      type: FieldType.string,
      defaultValue: ']',
    },
    {
      name: 'rightvalue',
      type: FieldType.number,
      bind: 'multipleValue.rightvalue',
      defaultValue: null,
    },
  ],
});

export { detailDS, tableDS };
