.cell-num-container {

  :global{
    .c7n-pro-input-number-wrapper{
      margin-left: -8px;
      width: 50%;
      float: left;
    }
  }
}

.cell-num-container2 {

  span {
    width: 50%;
    margin-left: 10px;
    margin-right: 10px;
  }
}

.cell-measure-desc {

  div{
    display: inline-block;
    width: 50%;
  }
}

.cell-group-lov {

  :global{
    .c7n-pro-select-wrapper {
      width: 100px;
    }
  }
}

.cell-uniformity {
  height: 100%;
  div{
    overflow: hidden;
    height: 50%;
    line-height: initial;
  }
  div:first-child{
    border-bottom: 1px solid rgb(240, 240, 240);
    margin-bottom: 4px;
  }
}

.pdf-container {
  //width: 10px;
  //height: 10px;
  overflow: auto;
  position: absolute;
  //z-index: -1;
  top: 10;
  left: 10;
  #pdf {
    width: 842px;
    font-size: 14px;
    .pdf-page-height {
      img {
        z-index: 100000;
      }
      div {
        display: inline-block;
        margin-left: 150px;
        margin-bottom: 15px;

        h3 {
          display: inline-block;
        }
      }

      p {
        top: 10px;
        text-align: center;

        span {
          margin-left: 80px;
          margin-right: 80px;
        }
      }

      table {
        tr {
          border-left: 1px solid black;
          border-right: 1px solid black;
          &:first-child {
            td {
              border-top: 2px solid black;
            }
          }
          &:last-child {
            td {
              border-bottom: 2px solid black;
            }
          }
          td {
            width: calc(1 / 6 * 820px);
            border: 1px solid black;
          }
        }
      }
    }
    .img-div{
      height: 284px;
    }
  }
}

.dataAnayContainer {
  width: 820px;
  table {
    margin-left: 10px;
    tr {
      td{
        text-align: center;
        border: 1px solid black;
        width: calc(1 / 5 * 800px);
      }
    }
  }
}

.containerBorder {
  width: 820px;

  :global{
    .c7n-pro-table-cell{
      padding: 0!important;
    }
    .c7n-pro-table-cell-inner{
      padding: 0!important;
    }
  }
}

.resultContainer{
  width: 820px;
  table {
    margin-left: 10px;
    tr {
      td{
        padding: 10px;
        border: 1px solid black;
      }
      td:first-child {
        text-align: center;
        width: calc(1 / 5 * 800px);
      }
      td:last-child {
        width: calc(4 / 5 * 800px);
      }
    }
  }
}
.containerDataBorder{
  width: 820px;

  :global{
    .c7n-pro-table-tbody{
      .c7n-pro-table-row:first-child{

        span{
          height: 0.56rem!important;
          white-space: pre-wrap;
        }
      }
    }
    .c7n-pro-table-cell{
      padding: 0!important;
    }
    .c7n-pro-table-cell-inner{
      padding: 0!important;
    }
  }

}
.linearTable {
  width: 820px;

  :global{
    .c7n-pro-table-cell{
      padding: 0!important;
    }
    .c7n-pro-table-cell-inner{
      padding: 0!important;
    }
  }
}

.grrTable{
  width: 820px;

  :global{
    .c7n-pro-table-cell{
      padding: 0!important;
    }
    .c7n-pro-table-cell-inner{
      padding: 0!important;
    }
  }
}

.appearanceTable{
  width: 820px;

  :global{
    .c7n-pro-table-cell{
      padding: 0!important;
    }
    .c7n-pro-table-cell-inner{
      padding: 0!important;
      border: none !important;
    }
    .c7n-pro-table-cell-inner-required {
      border: none !important;
    }
  }
}

.sizeAappearanceTable{
  width: 820px;

  :global{
    .c7n-pro-table-cell{
      padding: 0!important;
    }
    .c7n-pro-table-cell-inner{
      padding: 0!important;
      border: none !important;
    }
    .c7n-pro-table-cell-inner-required {
      border: none !important;
    }
  }
}
