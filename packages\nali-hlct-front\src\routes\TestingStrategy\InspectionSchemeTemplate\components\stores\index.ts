/**
 * @Description: 检验项目组维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-01-10 16:54:12
 * @LastEditTime: 2023-04-20 16:29:40
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.inspectGroupMaintenance.model';
const tenantId = getCurrentOrganizationId();

// 检验组关联检验项
const DetailTableDS: (config) => DataSetProps = (config = {}) => ({
  autoQuery: false,
  autoCreate: false,
  selection: config?.canSelect ? DataSetSelection.multiple : false,
  paging: false,
  primaryKey: 'inspectGroupItemId',
  fields: [
    {
      name: 'selectList',
      type: FieldType.object,
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      min: 1,
      step: 1,
      required: true,
      disabled: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return (record.get('selectList') || {}).sequence;
        },
      },
    },
    {
      name: 'inspectItemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectItemCode`).d('检验项目编码'),
      lovCode: 'MT.QMS.INSPECT_ITEM',
      ignore: FieldIgnore.always,
      required: true,
      lovPara: {
        dataType: 'CALCULATE_FORMULA',
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (record.get('selectList') || {}).inspectItemLov;
        },
      },
    },
    {
      name: 'inspectItemId',
      bind: 'inspectItemLov.inspectItemId',
    },
    {
      name: 'inspectItemCode',
      bind: 'inspectItemLov.inspectItemCode',
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemDesc`).d('检验项目描述'),
      bind: 'inspectItemLov.inspectItemDesc',
      disabled: true,
    },
    {
      name: 'inspectItemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemType`).d('检验项目类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSPECT_ITEM_TYPE`,
      noCache: true,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).inspectItemType;
        },
      },
    },
    {
      name: 'inspectGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectGroupDesc`).d('检验项目组'),
      disabled: true,
    },
    {
      name: 'dataQtyDisposition',
      type: FieldType.string,
      lookupCode: 'MT.QMS_DATA_QTY_DISPOSITION',
      label: intl.get(`${modelPrompt}.dataQtyDisposition`).d('记录值个数配置'),
    },
    {
      name: 'taskCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskCategory`).d('任务类别'),
    },
    {
      name: 'inspectBasis',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBasis`).d('检验依据'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).inspectBasis;
        },
      },
    },
    {
      name: 'inspectMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('检验方法'),
      lookupCode: 'MT.QMS.INSPECT_METHOD',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).inspectMethod;
        },
      },
    },
    {
      name: 'technicalRequirement',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.technicalRequirement`).d('技术要求'),
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') || (record.get('selectList') || {}).technicalRequirement
          );
        },
      },
    },
    {
      name: 'inspectTool',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTool`).d('检验工具'),
      lookupCode: 'MT.QMS.INSPECT_TOOL',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).inspectTool;
        },
      },
    },
    {
      name: 'dataType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataType`).d('数据类型'),
      lookupCode: 'MT.QMS.INSPECT_ITEM_DATA_TYPE',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).dataType;
        },
      },
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') ||
            (['VALUE', 'VALUE_LIST'].includes(record.get('dataType')) && record.get('falseValue'))
          );
        },
      },
    },
    {
      // 多值数组-符合值
      name: 'trueValueList',
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') ||
            (['VALUE', 'VALUE_LIST'].includes(record.get('dataType')) && record.get('trueValue'))
          );
        },
      },
    },
    {
      // 多值数组-不符合值
      name: 'falseValueList',
    },
    {
      name: 'earlyWarningValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.earlyWarningValue`).d('预警值'),
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') ||
            record.get('dataType') !== 'VALUE' ||
            !record.get('trueValue') ||
            (record.get('selectList') || {}).earlyWarningValue
          );
        },
      },
    },
    {
      // 多值数组-预警值
      name: 'warningValueList',
    },
    {
      name: 'uomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      lovCode: 'MT.COMMON.UOM',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'uomName',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).uomName;
        },
      },
    },
    {
      name: 'uomId',
      bind: 'uomLov.uomId',
    },
    {
      name: 'uomName',
      bind: 'uomLov.uomName',
    },
    {
      name: 'processMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processMode`).d('尾数处理'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=DECIMAL_PROCESS_MODE`,
      noCache: true,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).processMode;
        },
      },
    },
    {
      name: 'enterMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterMethod`).d('录入方式'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_COLLECTION_METHOD`,
      noCache: true,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') ||
            record.get('dataType') === 'CALCULATE_FORMULA' ||
            (record.get('selectList') || {}).enterMethod
          );
        },
      },
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.decimalNumber`).d('小数位数'),
      min: 0,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).decimalNumber;
        },
      },
    },
    {
      name: 'requiredFlag',
      label: intl.get(`${modelPrompt}.requiredFlag`).d('必填项目标识'),
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).requiredFlag;
        },
      },
    },
    {
      name: 'dataQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.dataQty`).d('记录值个数'),
      min: 1,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).dataQty;
        },
      },
    },

    { name: 'formula' },

    {
      name: 'formulaLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.formula`).d('计算公式'),
      lovCode: 'HRES.RULE.LIST',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'ruleName',
      valueField: 'ruleId',
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            record.get('dataType') !== 'CALCULATE_FORMULA' ||
            (record.get('selectList') || {}).formulaLov
          );
        },
        required: ({ record }) => {
          return record.get('dataType') === 'CALCULATE_FORMULA';
        },
      },
    },
    {
      name: 'formulaId',
      bind: 'formulaLov.ruleId',
    },
    {
      name: 'formulaCode',
      bind: 'formulaLov.ruleCode',
    },
    {
      name: 'formulaName',
      bind: 'formulaLov.ruleName',
    },

    {
      name: 'formulaList',
    },

    {
      name: 'dimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.formulaDimension`).d('计算维度'),
      lookupCode: 'MT.QMS.CALCULATE_DIMENSION',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            record.get('dataType') !== 'CALCULATE_FORMULA' ||
            (record.get('selectList') || {}).dimension
          );
        },
        required: ({ record }) => {
          return record.get('dataType') === 'CALCULATE_FORMULA';
        },
      },
    },

    {
      name: 'samplingMethodLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
      lovCode: 'MT.QMS.SAMPLING_METHOD',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'samplingMethodDesc',
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') ||
            record.get('dataType') === 'CALCULATE_FORMULA' ||
            record.get('samplingDimension') === 'ALL_SAMPLING' ||
            (record.get('selectList') || {}).samplingMethodLov
          );
        },
      },
    },
    {
      name: 'samplingMethodId',
      bind: 'samplingMethodLov.samplingMethodId',
    },
    {
      name: 'samplingMethodDesc',
      bind: 'samplingMethodLov.samplingMethodDesc',
    },
    {
      name: 'sameGroupIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sameGroupIdentification`).d('同组标识'),
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') ||
            record.get('dataType') === 'CALCULATE_FORMULA' ||
            (record.get('selectList') || {}).sameGroupIdentification
          );
        },
      },
    },
    {
      name: 'destructiveExperimentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.destructiveExperimentFlag`).d('破坏性检验标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') ||
            (record.get('selectList') || {}).destructiveExperimentFlag
          );
        },
      },
    },
    {
      name: 'outsourceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsourceFlag`).d('委外检验标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).outsourceFlag;
        },
      },
    },
    {
      name: 'actionItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actionItem`).d('行动项'),
      dynamicProps: {
        disabled: ({ record }) =>
          !record.get('inspectItemLov') || (record.get('selectList') || {}).actionItem,
      },
    },
    {
      name: 'employeePosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.employeePosition`).d('检测人员岗位'),
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') ||
            record.get('dataType') === 'CALCULATE_FORMULA' ||
            (record.get('selectList') || {}).employeePosition
          );
        },
      },
    },
    {
      name: 'inspectFrequencyObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectFrequency`).d('检测频率'),
      lookupCode: 'MT.QMS.INSPECT_FREQUENCY',
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            record.get('dataType') === 'CALCULATE_FORMULA' ||
            !record.get('inspectItemLov') ||
            (record.get('selectList') || {}).inspectFrequency
          );
        },
      },
    },
    {
      name: 'inspectFrequency',
      bind: 'inspectFrequencyObject.value',
    },
    {
      name: 'inspectFrequencyDesc',
      label: intl.get(`${modelPrompt}.inspectFrequency`).d('检测频率'),
      bind: 'inspectFrequencyObject.meaning',
    },
    {
      name: 'ncCodeGroupLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeGroup`).d('不良代码组'),
      lovCode: 'MT.NC_GROUP',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'description',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).description;
        },
      },
    },
    {
      name: 'ncCodeGroupId',
      bind: 'ncCodeGroupLov.ncGroupId',
    },
    {
      name: 'ncCodeGroupDesc',
      bind: 'ncCodeGroupLov.description',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.attachment`).d('附件'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).enclosure;
        },
      },
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
      lookupCode: 'MT.QMS.QUALITY_CHARACTERISTIC_TYPE',
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('inspectItemLov') || (record.get('selectList') || {}).qualityCharacteristic
          );
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      maxLength: 1000,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).remark;
        },
      },
    },
    {
      // 频率参数M值
      name: 'm',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.m`).d('频率参数M'),
      min: 1,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectFrequency') || (record.get('selectList') || {}).m;
        },
        required: ({ record }) => record.get('inspectFrequency'),
      },
    },
    {
      // 频率参数N值
      name: 'n',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.n`).d('频率参数N'),
      min: 1,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectFrequency') || (record.get('selectList') || {}).n;
        },
        required: ({ record }) => record.get('inspectFrequency'),
      },
    },
    {
      name: 'valueLists',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueLists`).d('值列表'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectItemLov') || (record.get('selectList') || {}).valueLists;
        },
        required: ({ record }) => {
          return record.get('dataType') === 'VALUE_LIST';
        },
      },
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => {
        return record.get('canSelect');
      },
    },
  },
});

const formulaListTableDS: () => DataSetProps = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  forceValidate: true,
  dataKey: 'rows',
  selection: false,
  paging: false,
  fields: [
    { name: 'fieldCode', label: intl.get(`${modelPrompt}.formulaParams`).d('公式参数') },
    { name: 'fieldName', label: intl.get(`${modelPrompt}.formulaParamsDesc`).d('公式参数描述') },
    {
      name: 'inspectItemObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.associatedItems`).d('关联项目'),
      dynamicProps: {
        required: ({ record }) => {
          return record.get('isRequired') === 'Y';
        },
      },
    },
    { name: 'isRequired' },
    {
      name: 'inspectItemId',
      bind: 'inspectItemObject.value',
    },
    {
      name: 'inspectItemDesc',
      bind: 'inspectItemObject.meaning',
    },
  ],
});

const NumberDS: () => DataSetProps = () => ({
  autoCreate: false,
  fields: [
    {
      name: 'singleValued',
      type: FieldType.string,
    },
    {
      name: 'multiValued',
      type: FieldType.string,
      range: ['start', 'end'],
    },
    {
      // 单值或多值：single/section
      name: 'valueType',
      type: FieldType.string,
    },
    {
      // 1<x<2
      name: 'valueShow',
      type: FieldType.string,
    },
    {
      // (1,2)
      name: 'dataValue',
      type: FieldType.string,
    },
    {
      name: 'leftValue',
      type: FieldType.string,
    },
    {
      name: 'leftChar',
      type: FieldType.string,
    },
    {
      name: 'rightChar',
      type: FieldType.string,
    },
    {
      name: 'rightValue',
      type: FieldType.string,
    },
    {
      name: 'standard',
      type: FieldType.string,
    },
  ],
});

const batchDS: () => DataSetProps = () => ({
  autoCreate: false,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectionItem`).d('检验项目'),
      name: 'inspectionItemObject',
      lovCode: 'MT.QMS.INSPECT_ITEM_INFO',
      multiple: true,
      noCache: true,
      lovPara: { tenantId, dataType: 'CALCULATE_FORMULA' },
    },
  ],
});

export { DetailTableDS, NumberDS, batchDS, formulaListTableDS };
