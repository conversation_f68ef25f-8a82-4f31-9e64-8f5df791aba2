import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import uuid from 'uuid/v4';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hmes.purchase.sellOrder';

const tenantId = getCurrentOrganizationId();

const detailHeaderDS = (): DataSetProps => ({
  autoQuery: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'soId',
      type: FieldType.number,
    },
    {
      name: 'customerSiteId',
      type: FieldType.number,
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNum`).d('销售订单'),
    },
    {
      name: 'customerObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customer`).d('客户名称'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      type: FieldType.number,
      bind: 'customerObj.customerId',
    },
    {
      name: 'customerName',
      type: FieldType.string,
      bind: 'customerObj.customerName',
    },
    {
      name: 'customerSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerSiteCode`).d('客户地点名称'),
      lovCode: 'MT.MODEL.CUSTOMER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('customerId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            customerId: record.get('customerId'),
          };
        },
      },
    },
    {
      name: 'customerSiteId',
      bind: 'customerSiteLov.customerSiteId',
    },
    {
      name: 'customerSiteName',
      bind: 'customerSiteLov.customerSiteName',
    },
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.shipFromSiteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObj.siteCode',
    },
    {
      name: 'shipToObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.shipTo`).d('收货方'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'shipToId',
      type: FieldType.number,
      bind: 'shipToObj.customerId',
    },
    {
      name: 'shipToName',
      type: FieldType.string,
      bind: 'shipToObj.customerName',
    },
    {
      name: 'shipToSiteDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shipToSiteDesc`).d('收货地址'),
    },
    {
      name: 'salesPerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.salesPerson`).d('销售员'),
    },
    {
      name: 'soType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soType`).d('销售订单类型'),
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-instruction-doc/operation-type/limit/doc/type/list?operationType=SO_DOC`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'soStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('销售订单状态'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=SO_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      required: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'freezeFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('信用冻结标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('订单说明'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-delivery-platform/instruction-doc/head/detail/for/ui`,
        method: 'GET',
      };
    },
  },
});

const detailLineDS = (): DataSetProps => ({
  selection: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'soLineNum',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.soLineNum`).d('行号'),
    },
    {
      name: 'lineType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineType`).d('行类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=SO_LINE_TYPE`,
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'lineStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineStatus`).d('行状态'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=SO_LINE_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'USABLE',
      required: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.BOM_MATERIAL',
      lovPara: {
        tenantId,
      },
      required: true,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteIds: dataSet.parent?.current?.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialObj.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialObj.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'materialObj.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
      bind: 'materialObj.currentRevisionCode',
      textField: 'description',
      valueField: 'description',
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: uuid(),
                  description: item,
                };
              });
            }
            if (record) {
              if (firstlyQueryData.length > 0) {
                if (!record?.get('revisionCode')) {
                  // eslint-disable-next-line no-unused-expressions
                  record?.init('revisionCode', firstlyQueryData[0].description);
                }
              } else {
                // eslint-disable-next-line no-unused-expressions
                record?.init('revisionCode', null);
              }
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('materialId');
        },
        required({ record }) {
          return record?.get('revisionFlag') === 'Y' && record?.get('materialId');
        },
        lovPara: ({ record, dataSet }) => {
          return {
            tenantId,
            siteIds: dataSet.parent?.current?.get('siteId') || undefined,
            materialId: record?.get('materialId') || undefined,
            kid: record?.get('kid') || undefined,
          };
        },
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialObj.materialName',
    },
    {
      name: 'orderedQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.orderedQuantity`).d('数量'),
      // min: 1,
      required: true,
      dynamicProps: {
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
      },
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
      bind: 'materialObj.decimalNumber',
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      bind: 'materialObj.uomCode',
    },
    {
      name: 'uomId',
      bind: 'materialObj.uomId',
    },
    {
      name: 'siteCode',
      label: intl.get(`${modelPrompt}.shipFromSiteCode`).d('站点'),
      type: FieldType.string,
    },
    {
      name: 'shipFromSiteId',
      type: FieldType.number,
    },
    {
      name: 'shipLocator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.shipLocatorCode`).d('发运库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteId: dataSet.parent?.current?.get('siteId') || undefined,
          };
        },
      },
    },
    {
      name: 'shipLocatorCode',
      type: FieldType.string,
      bind: 'shipLocator.locatorCode',
    },
    {
      name: 'shipLocatorId',
      type: FieldType.number,
      bind: 'shipLocator.locatorId',
    },
    {
      name: 'shipMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shipMethod`).d('发运方式'),
    },
    {
      name: 'scheduleShipDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.scheduleShipDate`).d('计划发运日期'),
    },
    {
      name: 'scheduleArrivalDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.scheduleArrivalDate`).d('计划到达日期'),
    },
    {
      name: 'packingInstructions',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packingInstructions`).d('包装需求'),
    },
    {
      name: 'itemCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemCategory`).d('项目类别'),
      lookupCode: 'MT.ITEM_CATEGORY',
      lovPara: { tenantId },
      valueField: 'value',
      textField: 'meaning',
    },
    {
      name: 'lineFreezeFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('信用冻结标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'contractNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contractNum`).d('合同编码'),
    },
    {
      name: 'customerPoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerPoNum`).d('客户采购订单编码'),
    },
    {
      name: 'customerPoLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerPoLineNum`).d('客户采购订单行号'),
    },
    {
      name: 'parentLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.parentLineNum`).d('母件订单行号'),
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
    },
    {
      name: 'lineRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineRemark`).d('行备注'),
    },
  ],
});

export { detailHeaderDS, detailLineDS };
