/* eslint-disable no-else-return */
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import moment from 'moment';

const modelPrompt = 'fydstms.field.view.table.fields';


// const Host = '/ty-mes-39945';
const ikun = '';
// const ikun = '-30711'

const tenantId = getCurrentOrganizationId();

const detailDs = () => ({
  autoQuery: false,
  // dataKey: 'content',
  // totalKey: 'totalElements',
  fields: [
    {
      name: 'processCardNum',
      label: intl.get(`${modelPrompt}.processCardNum`).d('卡号'),
      type: FieldType.string,
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('单据状态'),
      type: FieldType.string,
      lookupCode: 'HME.SPARED_DOC_STATUS',
      textField: 'meaning',
      valueField: 'value',
    },
    // {
    //   name: 'docType',
    //   label: intl.get(`${modelPrompt}.docType`).d('内径宽度'),
    //   type: FieldType.string,
    // },
    // {
    //   name: 'docType',
    //   label: intl.get(`${modelPrompt}.docType`).d('坯料数量'),
    //   type: FieldType.string,
    // },
    // {
    //   name: 'docType',
    //   label: intl.get(`${modelPrompt}.docType`).d('外径偏差'),
    //   type: FieldType.string,
    // },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.site`).d('站点'),
      lovCode: 'HME.PROCESS_CARD_SITE_LOV',
      noCache: true,
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'remark',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      type: FieldType.string,
    },
    {
      name: 'routerName',
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${ikun}/v1/${tenantId}/hme-process-card-headers/detail/info/ui`,
        // url: `/key-focus-tznd/v1/8/mt-bom/list/ui`,
        method: 'GET',
      };
    },
  },
});

const creatEODs = () => ({
  autoQuery: false,
  primaryKey: 'orderNumber',
  // dataKey: 'createEoList',
  // paging: false,
  fields: [
    {
      name: 'workOrderNum',
      label: intl.get('hzero.common.model.workOrderNum').d('工单号'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      type: FieldType.string,
    },
    {
      name: 'qty',
      label: intl.get(`${modelPrompt}.qty`).d('指令数量'),
      type: FieldType.number,
    },
    {
      name: 'canReleaseQty',
      label: intl.get(`${modelPrompt}.canReleaseQty`).d('可下达数量'),
      type: FieldType.number,
    },
    {
      name: 'planStartTime',
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
      type: FieldType.string,
    },
    {
      name: 'releaseQty',
      label: intl.get(`${modelPrompt}.releaseQty`).d('下达数'),
      type: FieldType.number,
      required: true,
      max: 'canReleaseQty',
      min: 0,
    },
    {
      name: 'unitEoQty',
      label: intl.get(`${modelPrompt}.unitEoQty`).d('单位EO数'),
      type: FieldType.number,
      required: true,
      min: 0,
    },
    {
      name: 'genEoQty',
      label: intl.get(`${modelPrompt}.genEoQty`).d('生成个数'),
      type: FieldType.number,
    },
    {
      name: 'dealFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('尾数处理'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/list/ui?typeGroup=EO_CREAT_MANTISSA_DEAL`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if(data){
            if (data instanceof Array || data.content instanceof Array) {
              return data || data.content;
            }else{
              const list = JSON.parse(data);
              return list.rows;
            }
          }
        },
      },
      dynamicProps: {
        required({ record }) {
          return record.get('releaseQty') % record.get('unitEoQty') !== 0;
        },
        disabled({ record }) {
          return record.get('releaseQty') % record.get('unitEoQty') === 0;
        },
      },
    },
  ],
  queryFields: [
    {
      name: 'workOrder',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.site`).d('工单'),
      lovCode: 'HME.PROCESS_CARD_WO_LOV',
      noCache: true,
      valueField: 'workOrderId',
      ignore: 'always',
      // // required: true,
      // lovPara: {
      //   siteId: window.localStorage.getItem('siteInfo') || null,
      // },
      dynamicProps: {
        lovPara() {
          return {
            siteId: window.localStorage.getItem('siteInfo') || null,
            statusFlag: 'Y',
          }
        },
      },
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      bind: 'workOrder.workOrderNum',
    },
    {
      name: 'workOrderId',
      type: FieldType.string,
      bind: 'workOrder.workOrderId',
    },
    {
      name: 'planStartFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartFrom`).d('计划开始时间从'),
      // max: 'planStartTo',
      defaultValue: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      required: true,
    },
    {
      name: 'prodLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.prodLine`).d('产线'),
      lovCode: 'MT.METHOD.ROUTER_OPERATION',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      type: FieldType.string,
      bind: 'prodLine.prodLineId',
    },
    {
      name: 'planStartTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartTo`).d('计划开始时间至'),
      min: 'planStartFrom',
    },
    {
      name: 'statusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('工单类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/list/ui?typeGroup=WO_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if(data){
            if (data instanceof Array || data.content instanceof Array) {
              return data || data.content;
            }else{
              const list = JSON.parse(data);
              return list.rows;
            }
          }
        },
      },
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.operation`).d('物料'),
      lovCode: 'HME.SITE_MATERIAL',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara() {
          return {
            siteId: window.localStorage.getItem('siteInfo') || null,
          }
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('工单状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/list/ui?statusGroup=WO_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if(data){
            if (data instanceof Array || data.content instanceof Array) {
              return data || data.content;
            }else{
              const list = JSON.parse(data);
              return list.rows;
            }
          }
        },
      },
    },
  ],
  // dataKey: 'lineList',
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}${ikun}/v1/${tenantId}/hme-process-card-headers/create/wo/list/ui`,
        method: 'POST',
        data: {
          ...data,
          flag: 'Y',
          siteId: window.localStorage.getItem('siteInfo') || null,
        },
      };
    },
  },
});
const relevanceEODs = () => ({
  autoQuery: false,
  primaryKey: 'orderNumber',
  // paging: false,
  // dataKey: 'assignEoList',
  fields: [
    {
      name: 'eoNum',
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
      type: FieldType.string,
    },
    {
      name: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单号'),
      type: FieldType.string,
    },
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.identification`).d('执行作业标识'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      type: FieldType.string,
    },
    {
      name: 'qty',
      label: intl.get(`${modelPrompt}.qty`).d('执行作业数量'),
      type: FieldType.string,
    },
  ],
  queryFields: [
    {
      name: ' workOrder',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.site`).d('工单'),
      lovCode: 'HME.PROCESS_CARD_WO_LOV',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara() {
          return {
            siteId: window.localStorage.getItem('siteInfo') || null,
            statusFlag: 'Y',
          }
        },
      },
    },
    {
      name: 'workOrderId',
      type: FieldType.string,
      bind: ' workOrder.workOrderId',
    },
    {
      name: 'planStartFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartFrom`).d('计划开始时间从'),
      // max: 'planStartTo',
      required: true,
      defaultValue: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      name: 'prodLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.prodLine`).d('产线'),
      lovCode: 'MT.METHOD.ROUTER_OPERATION',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      type: FieldType.string,
      bind: 'prodLine.prodLineId',
    },
    {
      name: 'planStartTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartTo`).d('计划开始时间至'),
      min: 'planStartFrom',
    },
    {
      name: 'statusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('工单类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/list/ui?typeGroup=WO_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if(data){
            if (data instanceof Array || data.content instanceof Array) {
              return data || data.content;
            }else{
              const list = JSON.parse(data);
              return list.rows;
            }

          }
        },
      },
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.operation`).d('物料'),
      lovCode: 'HME.SITE_MATERIAL',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara() {
          return {
            siteId: window.localStorage.getItem('siteInfo') || null,
          }
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.identification`).d('执行作业标识'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}${ikun}/v1/${tenantId}/hme-process-card-headers/assign/eo/list/ui`,
        method: 'POST',
        data: {
          ...data,
          siteId: window.localStorage.getItem('siteInfo') || null,
          flag: 'N',
        },
      };
    },
  },
});
const transferDs = () => ({
  autoQuery: false,
  primaryKey: 'orderNumber',
  paging: false,
  fields: [
    {
      name: 'createFrom',
      label: intl.get('hzero.common.model.orderSeq').d('来源'),
      type: FieldType.string,
    },
    {
      name: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单号'),
      type: FieldType.string,
    },
    {
      name: 'eoNum',
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
      type: FieldType.string,
    },
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.identification`).d('执行作业标识'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      type: FieldType.string,
    },
    {
      name: 'qty',
      label: intl.get(`${modelPrompt}.qty`).d('WO数量'),
      type: FieldType.number,
    },
    {
      name: 'releaseQty',
      label: intl.get(`${modelPrompt}.releaseQty`).d('下达数'),
      type: FieldType.number,
    },
    {
      name: 'unitEoQty',
      label: intl.get(`${modelPrompt}.unitEoQty`).d('单位EO数'),
      type: FieldType.number,
    },
    {
      name: 'genEoQty',
      label: intl.get(`${modelPrompt}.genEoQty`).d('生成个数'),
      type: FieldType.number,
    },
    {
      name: 'dealFlag',
      label: intl.get(`${modelPrompt}.dealFlag`).d('尾数处理'),
      type: FieldType.string,
    },
  ],
  // dataKey: 'lineList',
  // data: [
  //   {serialNum: 1111, sumQyt: 10},
  //   {serialNum: 11112, sumQyt: 10},
  //   {serialNum: 11113, sumQyt: 104},
  // ],
});
const transferDetailDs = () => ({
  autoQuery: false,
  primaryKey: 'orderNumber',
  paging: false,
  fields: [
    {
      name: 'createFrom',
      label: intl.get('hzero.common.model.orderSeq').d('来源'),
      type: FieldType.string,
    },
    {
      name: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单号'),
      type: FieldType.string,
    },
    {
      name: 'eoNum',
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
      type: FieldType.string,
    },
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.identification`).d('执行作业标识'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      type: FieldType.string,
    },
    {
      name: 'qty',
      label: intl.get(`${modelPrompt}.qty`).d('WO数量'),
      type: FieldType.number,
    },
    {
      name: 'releaseQty',
      label: intl.get(`${modelPrompt}.releaseQty`).d('下达数'),
      type: FieldType.number,
    },
    {
      name: 'unitEoQty',
      label: intl.get(`${modelPrompt}.unitEoQty`).d('单位EO数'),
      type: FieldType.number,
    },
    {
      name: 'genEoQty',
      label: intl.get(`${modelPrompt}.genEoQty`).d('生成个数'),
      type: FieldType.number,
    },
    {
      name: 'dealFlag',
      label: intl.get(`${modelPrompt}.dealFlag`).d('尾数处理'),
      type: FieldType.string,
    },
  ],
  // dataKey: 'lineList',
  // data: [
  //   {serialNum: 1111, sumQyt: 10},
  //   {serialNum: 11112, sumQyt: 10},
  //   {serialNum: 11113, sumQyt: 104},
  // ],
});
const splitDs = () => ({
  autoQuery: false,
  primaryKey: 'orderNumber',
  paging: false,
  fields: [
    {
      name: 'createFrom',
      label: intl.get('hzero.common.model.orderSeq').d('来源'),
      type: FieldType.string,
    },
    {
      name: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单号'),
      type: FieldType.string,
    },
    {
      name: 'eoNum',
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
      type: FieldType.string,
    },
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.identification`).d('执行作业标识'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      type: FieldType.string,
    },
    {
      name: 'qty',
      label: intl.get(`${modelPrompt}.qty`).d('WO数量'),
      type: FieldType.number,
    },
    {
      name: 'releaseQty',
      label: intl.get(`${modelPrompt}.releaseQty`).d('下达数'),
      type: FieldType.number,
    },
    {
      name: 'unitEoQty',
      label: intl.get(`${modelPrompt}.unitEoQty`).d('EO数量'),
      type: FieldType.number,
    },
    {
      name: 'groupCode',
      label: intl.get(`${modelPrompt}.groupCode`).d('组号'),
      type: FieldType.string,
    },
    {
      name: 'splitQty',
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      type: FieldType.number,
      required: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${ikun}/v1/${tenantId}/hme-process-card-headers/split/list/ui`,
        method: 'GET',
      };
    },
  },
});
const splitHistoryDs = () => ({
  autoQuery: false,
  primaryKey: 'orderNumber',
  paging: false,
  selection: 'false',
  fields: [
    {
      name: 'processCardHeaderNum',
      label: intl.get('hzero.common.model.orderSeq').d('卡号'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${ikun}/v1/${tenantId}/hme-process-card-headers/split/history/ui`,
        method: 'GET',
      };
    },
  },
});
const allocationDs = () => ({
  autoQuery: false,
  primaryKey: 'objectNum',
  selection: false,
  paging: false,
  fields: [
    {
      name: 'num',
      type: FieldType.number,
    },
    {
      name: 'objectNum',
      type: FieldType.string,
    },
    {
      name: 'name2',
      type: FieldType.string,
    },
  ],
});
const stepDs = () => ({
  autoQuery: false,
  primaryKey: 'orderNumber',
  selection: false,
  fields: [
    {
      name: 'materType',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.router`).d('物料/制造'),
      dynamicProps: {
        disabled({record}) {
          return record.get('routerId');
        },
      },
    },
    {
      name: 'router',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.router`).d('基准工艺路线'),
      noCache: true,
      ignore: 'always',
      required: true,
      dynamicProps: {
        disabled({record}) {
          return !record.get('materType');
        },
        lovCode: ({ record }) => {
          const materType = record.get('materType');
          if (materType === 'manufacture') {
            return 'HME.PROCESS_CARD_ROUTER_LOV';
          } else  {
            return 'HME.PROCESS_CARD_ROUTER_LOV_M';
          }
        },
        lovPara({record}) {
          return {
            makeRouterFlag: record.get('materType') === 'manufacture' ? 'Y' : 'N',
          }
        },
      },
    },
    {
      name: 'routerId',
      type: FieldType.string,
      bind: 'router.routerId',
    },
    {
      name: 'datumFromService',
      type: FieldType.string,
      bind: 'router.datumFromService',
    },
    {
      name: 'operation',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.router`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      noCache: true,
      ignore: 'always',
      required: true,
    },
    {
      name: 'operationId',
      type: FieldType.string,
      bind: 'operation.operationId',
    },
    // {
    //   name: 'datumFromService',
    //   type: FieldType.string,
    //   bind: 'operation.datumFromService',
    // },
  ],

});

export { detailDs, creatEODs, relevanceEODs, transferDs, allocationDs, stepDs, splitDs, splitHistoryDs, transferDetailDs };
