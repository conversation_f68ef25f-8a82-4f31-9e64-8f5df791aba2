/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2022-04-21 11:03:11
 * @LastEditTime: 2023-02-24 16:47:12
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { openTab } from 'utils/menuTab';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { DataSet, Table, Lov, Switch, Button } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { Button as PermissionButton } from 'components/Permission';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC, API_HOST } from '@utils/config';
import queryString from 'querystring';
import uuid from 'uuid/v4';

import { tableDS } from './stores/WorkCellListDS';

const modelPrompt = 'tarzan.equipment.workCell';
const _urlEnd = '';

// 根据库位获取现有量的接口
export function SaveData() {
  return {
    url: `${API_HOST}${BASIC.TARZAN_MODEL}${_urlEnd}/v1/${getCurrentOrganizationId()}/equipments/update`,
    method: 'POST',
  };
}

const WorkCellList = props => {
  const {
    dataSet,
    match: { path },
  } = props;

  useEffect(() => {
    dataSet.query(props.dataSet.currentPage);
    window.ds = dataSet;
  }, []);

  const saveData = useRequest(SaveData(), {
    manual: true,
  });

  const orderCreate = () => {
    dataSet.create({ enableFlag: 'Y', lineState: 'add', uuid: uuid() }, 0);
  };

  // 保存
  const handleSave = async record => {
    const validateRecord = await record.validate('all', true);
    if (validateRecord) {
      const { equipment, workcell, ...data } = record.toData() || {};
      saveData.run({
        params: data,
        onSuccess: res => {
          if(res&&res.workcellId){
            dataSet.query();
          }
        },
      });
    }
  };

  // 取消
  const handleCancel = record => {
    if (record.get('lineState') === 'add') {
      dataSet.delete(record, false);
    } else {
      record.reset();
    }
  };

  const columns = [
    {
      name: 'equipment',
      lock: 'left',
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <Lov dataSet={record} name='equipment' />,
      renderer: ({ record }) => {
        return record.get('equipmentCode');
      },
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'workcell',
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <Lov />,
      renderer: ({ record }) => {
        return record.get('workcellCode');
      },
    },
    {
      name: 'workcellName',
    },
    {
      name: 'enableFlag',
      width: 180,
      align: 'center',
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <Switch />,
      renderer: ({ value }) => {
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        );
      },
    },
    {
      name: 'option',
      fixed: 'right',
      lock: 'right',
      width: 180,
      align: 'center',
      title: intl.get('tarzan.common.label.action').d('操作'),
      renderer: ({ record }) => optionRender(record),
    },
  ];

  // 操作列渲染
  const optionRender = record => {
    if (['editing', 'add'].indexOf(record.get('lineState')) > -1) {
      return (
        <>
          <PermissionButton
            type="text"
            onClick={() => handleCancel(record)}
            style={{ marginRight: '8px' }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </PermissionButton>
          <PermissionButton
            type="text"
            onClick={() => handleSave(record)}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.save').d('保存')}
          </PermissionButton>
        </>
      );
    }
    return (
      <>
        <PermissionButton
          type="text"
          onClick={() => record.set('lineState', 'editing')}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </PermissionButton>
      </>
    );

  };
  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MT.MES.EQUIPMENT_WKC`,
      title: intl.get(`${modelPrompt}.title`).d('设备与工作单元维护'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('设备与工作单元维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => orderCreate()}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button color="primary" onClick={handleImport}>
            {intl.get('tarzan.common.button.import').d('导入')}
          </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          searchCode='sbygzdywh1'
          customizedCode='sbygzdywh1'
          dataSet={dataSet}
          columns={columns}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.equipment.workCell', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...tableDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(WorkCellList),
);
