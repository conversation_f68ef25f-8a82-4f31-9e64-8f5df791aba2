import React, { useMemo, useState, useEffect } from 'react';
import { Button, DataSet,Spin, Table, Col, Row, Form,Switch,  TextField, Lov, Icon, Select } from 'choerodon-ui/pro';
import { Badge, Popconfirm } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
// import { routerRedux } from 'dva/router';
import { useRequest } from '@components/tarzan-hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { observer } from 'mobx-react';
import queryString from 'querystring';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { openTab } from 'utils/menuTab';
import ExcelExport from 'components/ExcelExportPro';
import { BASIC } from '@utils/config';
import { useDataSetEvent } from 'utils/hooks';
import { tableDS } from './stories';
import LovModal from "../ProductBatchProcessCancellation/LovModal";
import InputLovDS from '../../stores/InputLovDS';
import {UnBind, QueryInfo} from './services/index';


const modelPrompt = 'tarzan.hmes.barcodeMarkingBinding';
const BarcodeMarkingBinding = observer(props => {
  const {
    location: { state },
    history,
  } = props;
  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  useEffect(() => {
    if (state?.identifications) {
      tableDs.queryDataSet?.loadData([{ identification: state?.identifications }]);
      tableDs.query(tableDs.currentPage);
      history.replace({ ...history.location, state: undefined });
    }
  }, [history.location.state]);

  const { run: unBind, loading: unBindLoading } = useRequest(UnBind(), {
    manual: true,
    needPromise: true,
  });
  const { run: queryInfo, loading: queryInfoLoading } = useRequest(QueryInfo(), {
    manual: true,
    needPromise: true,
  });
  const handleUpdate = () => {
    const {
      enableFlag,
      identification,
      markingCode,
      operationName,
      originalIdentification,
      traceLevelList,
      type,
    } = tableDs.queryDataSet.toJSONData()[0]
    if(!enableFlag&&!identification&&!markingCode&&!operationName&&!originalIdentification&&!traceLevelList.length&&!type){
      tableDs.loadData([]);
    }
  }
  useDataSetEvent(tableDs.queryDataSet, 'update', handleUpdate);


  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const toggleForm = () => {
    setExpandForm(!expandForm);
  }

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}
          style={{
            display: 'flex',
            alignItems: 'flex-start',
          }}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identification"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identification', '条码号', queryDataSet)}
                    />
                  </div>
                }
              />
              <TextField
                name="markingCode"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'markingCode', '标记编码', queryDataSet)}
                    />
                  </div>
                }
              />
              <Lov name="operationObj" />
              {expandForm && (
                <>
                  <Select name="type" />
                  <Select name="traceLevelList" />
                  <Select name="enableFlag" />
                  <Select name="sourceWay" />
                  <TextField
                    name="sourceIdentification"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          onClick={() => onOpenInputModal(true, 'sourceIdentification', '来源条码号', queryDataSet)}
                        />
                      </div>
                    }
                  />
                  <TextField
                    name="originalIdentification"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          onClick={() => onOpenInputModal(true, 'originalIdentification', '首次来源条码标识', queryDataSet)}
                        />
                      </div>
                    }
                  />
                  <Lov name="userLov" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button funcType="link" icon={
                expandForm? 'expand_less':'expand_more'
              } onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  }
  const handleSearch = async () => {
    const {
      enableFlag,
      identification,
      markingCode,
      operationName,
      originalIdentification,
      traceLevelList,
      type,
    } = tableDs.queryDataSet.toJSONData()[0]
    if(!enableFlag&&!identification&&!markingCode&&!operationName&&!originalIdentification&&!traceLevelList.length&&!type){
      notification.error({
        message: intl.get(`${modelPrompt}.searchValidate`).d('请至少输入一个搜索条件!'),
      });
    }else{
      tableDs.query()
    }
  }
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
    }
  }

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };
  const handleChangeLov = async (val) => {
    if(val){
      const res = await queryInfo({
        params: {
          markingId: val.markingId,
        },
      })
      if(res&&res.success){
        tableDs.current.set('markingCodeLov', {
          ...res.rows,
          ...val,
        })
      }
    }else{
      tableDs.current.set('markingCodeLov', null)
    }
  }

  const columns = [
    // 站点
    {
      name: 'identificationLov',
      align: 'left',
      width: 200,
      editor: record => record?.getState('editing')&&record.status === 'add',
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'markingCodeLov',
      width: 200,
      editor: record => record?.getState('editing')&&<Lov onChange={handleChangeLov}/>,
    },
    {
      name: 'statusMeaning',
    },
    {
      name: 'sourceWayMeaning',
    },
    {
      name: 'sourceIdentification',
      width:150
    },
    {
      name: 'sourceMaterialName',
      width: 150,
    },
    {
      name: 'originalIdentification',
      width: 200,
    },
    {
      name: 'originalMaterialName',
      width: 150,
    },
    {
      name: 'typeMeaning',
    },
    {
      name: 'markingContentMeaning',
    },
    {
      name: 'enableFlag',
      width: 100,
      editor: record => record.getState('editing') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'expirationDate',
      width:150
    },
    {
      name: 'applyReason',
      width:150
    },
    {
      name: 'description',
      width:150
    },
    {
      name: 'interceptionDisposalWay',
      width:150
    },
    {
      name: 'disposalResult',
    },
    {
      name: 'realName',
    },
    {
      name: 'lastUpdateDate',
      width:150
    },
    {
      width: 150,
      align: 'center',
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      renderer: ({ record }) =>
        record.getState('editing') ? (
          <>
            <Button color="primary" funcType="flat" onClick={() => handleEdit(record, false)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button color="primary" funcType="flat" onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        ) : (
          <Button color="primary" funcType="flat" disabled={tableDs.records.some(item => item?.getState('editing'))} onClick={() => handleEdit(record, true)}>
            {intl.get('tarzan.common.button.edit').d('编辑')}

          </Button>
        ),
      lock: 'right',
    },
  ];
  const handleCreate = () => {
    tableDs.create({
      realName: getCurrentUser().realName,
    },0);
    tableDs.current.setState('editing', true);
  };

  const  handleSave = async (record) => {
    if(await record.validate()){
      await tableDs.submit()
      const {
        enableFlag,
        identification,
        markingCode,
        operationName,
        originalIdentification,
        traceLevelList,
        type,
      } = tableDs.queryDataSet.toJSONData()[0]
      if(!enableFlag&&!identification&&!markingCode&&!operationName&&!originalIdentification&&!traceLevelList.length&&!type){
        tableDs.loadData([]);
      }else{
        await tableDs.query()
      }
    }
  }
  const  handleEdit = (record, flag) => {
    record.setState('editing', flag);
    if(record.status === 'add'){
      tableDs.remove(record)
    }else{
      record.reset()
    }
  }

  const handleUnbind = async () => {
    const res = await unBind({
      params: tableDs.selected.map(item => item.data),
    })
    if(res&&!res.success){
      notification.success({
        message: res.message,
      });
    }else{
      notification.success({
        message: intl.get(`${modelPrompt}.unbindSuccess`).d('解绑成功!'),
      });
      tableDs.query()
    }
  }

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/YP.MES.IDENTIFICATION_MARKING`,
      title: intl.get(`${modelPrompt}.title`).d('条码标记绑定'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }

  const getExportQueryParams = () => {
    if(tableDs.selected.length>0){
      return {
        identificationMarkingIds: tableDs.selected.map(item => item.get('identificationMarkingId')),
      }
    }
    return {
      ...tableDs.queryDataSet.toJSONData()[0],
    };
  }

  return (
    <div className="hmes-style">
      <Spin spinning={queryInfoLoading||unBindLoading}>
        <Header title={intl.get(`${modelPrompt}.title`).d('条码标记绑定')}>
          <ExcelExport
            method="POST"
            allBody
            requestUrl={`${
              BASIC.HMES_BASIC
            }/v1/${getCurrentOrganizationId()}/hme-identification-markings/export`}
            queryParams={getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
            otherButtonProps={{ disabled: !tableDs.toData().length }}
            modalProps={{ drawer: false }}
          />
          <Button color="primary" onClick={handleImport}>
            {intl.get('tarzan.common.button.import').d('导入')}
          </Button>
          <Popconfirm title={intl.get(`${modelPrompt}.unBindConfirm`).d('是否确认解绑？')} onConfirm={handleUnbind}
            okText={intl.get(`${modelPrompt}.confirm`).d('确认')} cancelText={intl.get(`${modelPrompt}.cancel`).d('取消')}>
            <Button disabled={tableDs.selected.length === 0||
          tableDs.selected.some(item => item?.status === 'add')}  style={{ marginRight: 15 }} color="primary">
              {intl.get('tarzan.common.button.unbind').d('解绑')}
            </Button>
          </Popconfirm>
          <Button disabled={tableDs.records.some(item => item?.getState('editing'))} onClick={handleCreate} style={{ marginRight: 15 }} icon="add" color="primary">
            {intl.get('tarzan.common.button.create').d('新建')}
          </Button>
        </Header>
        <Content>
          <Table
            dataSet={tableDs}
            columns={columns}
            queryFieldsLimit={3}
            searchCode="BarcodeMarkingBinding"
            customizedCode="BarcodeMarkingBinding"
            queryBar={renderQueryBar}
          />
        </Content>
        <LovModal {...lovModalProps} />
      </Spin>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.barcodeMarkingBinding', 'tarzan.common'],
})(BarcodeMarkingBinding);
