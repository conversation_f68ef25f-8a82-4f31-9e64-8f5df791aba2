/**
 * @Description: 产品审核方案-主界面
 */
import React, { FC, useMemo, useCallback } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { headDS } from '../stores';
import HistoryDrawer from '../HistoryDrawer';


const modelPrompt = 'tarzan.hwms.ProductReviewScheme';

interface TaskListProps extends RouteComponentProps {
  headDs: any;
}

const InspectionSchemeList: FC<TaskListProps> = props => {
  const { headDs,  history } = props;

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'productRevSchemeCode',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/product-review/product-review-scheme/${record!.get(
                    'productRevSchemeId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'siteName', width: 160 },
      { name: 'productRevSchemeName', width: 180 },
      { name: 'productRevSchemeStatus' },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'workcellCode', width: 120 },
      { name: 'workcellName' },
      { name: 'productDate', width: 120, align: ColumnAlign.center },
      { name: 'cusMaterialCode', width: 120 },
      { name: 'cusMaterialName', width: 120 },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 150,
        renderer: ({ record }) => (
          // @ts-ignore
          <HistoryDrawer objectId={record?.get('productRevSchemeId')} type="text" />
        ),
      },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    history.push(`/hwms/product-review/product-review-scheme/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('产品审核方案')}>
        <Button color={ButtonColor.primary} icon="add" onClick={handleAdd}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headDs}
          columns={headColumns}
          searchCode="ProductReviewScheme1"
          customizedCode="ProductReviewScheme-listHeader"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      return {
        headDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(InspectionSchemeList as any),
);
