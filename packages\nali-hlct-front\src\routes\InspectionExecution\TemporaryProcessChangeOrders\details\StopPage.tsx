import React from 'react';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Table, Attachment } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';

const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`;

const StopPage = ({ stopDs, stopLineDs }) => {

  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'qms.temporaryProcessChangeOrders.stop',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    readOnly: true,
  };

  const stopColumns: ColumnProps[] = [
    { name: 'sequence', minWidth: 100 },
    { name: 'amendDate', minWidth: 180 },
    { name: 'amendRemark', minWidth: 180 },
    { name: 'reviewStatus', minWidth: 150 },
    { name: 'createdByName', minWidth: 150 },
    { name: 'creationDate', minWidth: 180 },
    { 
      name: 'enclosure', 
      minWidth: 150,
      renderer:({record})=><Attachment {...attachmentProps} record={record} />,
    },
    { name: 'operationResult', minWidth: 180 },
    { name: 'operationOpinion', minWidth: 120 },
    { name: 'operationReviewByName', minWidth: 120 },
    { name: 'operationReviewTime', minWidth: 180 },
    { name: 'qualityResult', minWidth: 120 },
    { name: 'qualityOpinion', minWidth: 120 },
    { name: 'qualityReviewByName', minWidth: 120 },
    { name: 'qualityReviewTime', minWidth: 180 },
    { name: 'manufactureResult', minWidth: 180 },
    { name: 'manufactureOpinion', minWidth: 120 },
    { name: 'manufactureReviewByName', minWidth: 120 },
    { name: 'manufactureReviewTime', minWidth: 180 },
  ];

  const stopLineColumns: ColumnProps[] = [
    { name: 'inspectItemCode', minWidth: 180 },
    { name: 'inspectItemDesc', minWidth: 180 },
    { name: 'inspectBusinessTypeDesc', minWidth: 180 },
    { name: 'equipmentName', minWidth: 150 },
  ];

  return (
    <>
      <Table
        searchCode="temporaryProcessChangeOrdersStopHeader"
        customizedCode="temporaryProcessChangeOrdersStopHader"
        dataSet={stopDs}
        columns={stopColumns}
      />
      <Collapse defaultActiveKey={['StopMessage']}>
        <Collapse.Panel
          header={intl.get(`${modelPrompt}.stop.panel.StopMessage`).d('中止项目信息')}
          key='StopMessage'
        >
          <Table
            searchCode="temporaryProcessChangeOrdersStopLine"
            customizedCode="temporaryProcessChangeOrdersStopLine"
            dataSet={stopLineDs}
            columns={stopLineColumns}
          />
        </Collapse.Panel>
      </Collapse>
    </>
  )
};

export default StopPage;