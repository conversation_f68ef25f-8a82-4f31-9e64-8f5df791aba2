import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('执行作业'),
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeLov`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => !record.get('siteId'),
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'ncCodeId',
      type: FieldType.number,
      bind: 'ncCodeLov.ncCodeId',
    },
    // {
    //   name: 'ncCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
    // },
  ],
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('执行作业'),
    },
    {
      name: 'ncCodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码'),
    },
    {
      name: 'ncStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncStartTime`).d('绑定时间'),
    },
    {
      name: 'ncStartUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncStartUserName`).d('操作人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-trace-report/nc/record/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
