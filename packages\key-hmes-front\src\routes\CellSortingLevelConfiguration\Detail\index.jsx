
import React, { useState, useRef } from 'react';
import { But<PERSON> } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { BASIC } from '@utils/config';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import Detail from './detail';

const index = props => {
  const {
    // match: { path },
    customizeForm,
  } = props;
  const { id, type } = props.match.params;
  const [canEdit, setCanEdit] = useState(id === 'create');
  const childRef = useRef();

  const handleSave = async () => {
    const { success, levelConfigId } = await childRef.current.submit();
    if (success) {
      setCanEdit(prev => !prev);
      props.history.push(`/hmes/cell-sorting-level-configuration/detail/${levelConfigId}`);
      childRef.current.detailQuery(levelConfigId);
    }
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/cell-sorting-level-configuration/list');
    } else {
      setCanEdit(prev => !prev);
      childRef.current.detailQuery(id);
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get('tarzan.hmes.cellSortingLevelConfiguration.title.list').d('电芯分选等级配置')}
        backPath="/hmes/cell-sorting-level-configuration/list"
      >
        {canEdit ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <Button
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        )}
      </Header>
      <Content>
        <Detail
          customizeForm={customizeForm}
          canEdit={canEdit}
          ref={childRef}
          id={id}
          type={type}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.cellSortingLevelConfiguration', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.TAG_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.TAG_DETAIL.ATTR`],
  })(index),
);
