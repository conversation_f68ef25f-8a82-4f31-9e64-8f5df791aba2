/**
 * @Description: 创建物料批drawer
 * @Author: <EMAIL>
 * @Date: 2022/4/20 11:17
 * @LastEditTime: 2023-05-18 16:15:31
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect } from 'react';
import intl from 'utils/intl';
import { Collapse } from 'choerodon-ui';
import { DataSet, DateTimePicker, Form, NumberField, Table, TextField } from 'choerodon-ui/pro';
import { BASIC } from '@/utils/config';
import styles from './index.module.less';

const { Panel } = Collapse;


const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';

const CreateMaterial = ({
  headDs,
  tableDs,
  instructionDocLineId,
  batchList,
  customizeTable,
}) => {
  useEffect(() => {
    handleQuery();
  }, [headDs]);

  const handleQuery = async () => {
    tableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
    await tableDs.query();
  };

  // 物料的表格columns
  const columns = [
    {
      name: 'identification',
      align: 'left',
      width: 150,
      renderer: ({ record, value }) => {
        const { materialLotId } = record?.toData();
        return (
          <div>
            {batchList.indexOf(materialLotId) > -1 && (
              <div className={styles['icon-new']}>
                <div className={styles['icon-new-inner']}>
                  <div className={styles['icon-new-inner-text']}>NEW</div>
                </div>
              </div>
            )}
            {value}
          </div>
        );
      },
    },
    {
      name: 'primaryUomQty',
      align: 'right',
      width: 80,
    },
    {
      name: 'primaryUomCode',
      align: 'left',
      width: 80,
    },
    {
      name: 'materialLotStatusDesc',
      align: 'left',
      width: 100,
    },
    {
      name: 'productionDate',
      align: 'center',
      width: 150,
    },
    {
      name: 'lot',
      align: 'left',
      width: 80,
    },
    {
      name: 'printTimes',
      align: 'left',
      width: 80,
    },
    {
      name: 'createdByName',
      align: 'left',
      width: 100,
    },
    {
      name: 'creationDate',
      align: 'center',
      width: 150,
    },
  ];

  const Div = props => {
    return (
      <div data-name={props.name} className={styles['line-left']}>
        <TextField name="instructionDocNum" style={{ width: '80%' }} disabled />
        <TextField
          style={{ width: `calc(100% - 80%)` }}
          name="lineNumber"
          className={styles['line-right']}
          disabled
        />
      </div>
    );
  };

  const onMaterialQty = (record, val) => {
    if (val && val !== 0) {
      const data = tableDs.toData();
      const qty = data.reduce((pre, cur) => {
        return pre + cur.primaryUomQty;
      }, 0);
      record.set('materialSheets', Math.floor((record.get('orderedQuantity') - qty) / val));
    } else {
      record.set('materialSheets', null);
    }
  };

  return (
    <div className="hmes-style">
      <Form dataSet={headDs} labelLayout={'horizontal'} labelWidth={112} columns={2}>
        <Div name="instructionDocNum" />
        <TextField disabled name="materialCode" />
        <TextField disabled name="revisionCode" />
        <TextField disabled name="materialName" />
        <TextField disabled name="materialLotStatusDesc" />
        <TextField disabled name="primaryUomCode" />
        <TextField disabled name="orderedQuantity" />
        <TextField disabled name="createdQuantity" />
        <NumberField name="materialLotQty" onChange={(val)=>onMaterialQty(headDs.current,val)}/>
        <NumberField name="materialSheets" precision={0} />
        <DateTimePicker name="productionDate" />
        <TextField name="lot" />
      </Form>
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.material.information`).d('物料批信息')}
          key="basicInfo"
          dataSet={tableDs}
        >
          {customizeTable(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_MATERIAL_LOT.CREATE`,
            },
            <Table dataSet={tableDs} columns={columns} />,
          )}
        </Panel>
      </Collapse>
    </div>
  );
};
export default CreateMaterial;
