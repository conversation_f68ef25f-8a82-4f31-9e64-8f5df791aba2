/**
 * @Description: 数据收集组维护-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-03-01 10:47:34
 * @LastEditTime: 2023-05-23 16:50:39
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useEffect } from 'react';
import {
  Button,
  Form,
  TextField,
  DataSet,
  Lov,
  Select,
  IntlField,
  Spin,
  Modal,
  Table,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Tabs, Collapse, Tag } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { AttributeDrawer } from '@components/tarzan-ui';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';
import {
  detailDS,
  dataItemTableDS,
  assObjectsDS,
  siteOptionDs,
  copyDS,
  // numberListDS,
  historicalQueryDS,
} from '../stores/CollectionDS';
import DataItemInfoTab from './DataItemInfoTab';
import AssociatedObjectsTab from './AssociatedObjectsTab';
import CopyDrawer from './CopyDrawer';
import PreviewDrawer from './PreviewDrawer';
import ChannelDrawer from './ChannelDrawer';
// import { baseInfoDS } from '../../NewDataItem/stories/BaseInfoDs';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.acquisition.collection';
const tenantId = getCurrentOrganizationId();

const { TabPane } = Tabs;

const CollectionDetail = props => {

  const typeName = {
    MATERIAL: intl.get(`${modelPrompt}.MATERIAL`).d('物料'),
    OPERATION: intl.get(`${modelPrompt}.OPERATION`).d('工艺'),
    ROUTER: intl.get(`${modelPrompt}.ROUTER`).d('工艺路线'),
    ROUTER_STEP: intl.get(`${modelPrompt}.ROUTER_STEP`).d('工艺路线步骤'),
    WORKCELL: intl.get(`${modelPrompt}.WORKCELL`).d('工作单元'),
    NC_CODE: intl.get(`${modelPrompt}.NC_CODE`).d('NC代码'),
    BOM: intl.get(`${modelPrompt}.BOM`).d('装配清单'),
    BOM_COMPONENT: intl.get(`${modelPrompt}.BOM_COMPONENT`).d('装配清单组件'),
    SUBSTEP: intl.get(`${modelPrompt}.SUBSTEP`).d('子步骤'),
    WORK_ORDER: intl.get(`${modelPrompt}.WORK_ORDER`).d('生产指令'),
    EO: intl.get(`${modelPrompt}.EO`).d('执行作业'),
    MATERIAL_CATEGORY: intl.get(`${modelPrompt}.MATERIAL_CATEGORY`).d('物料类别'),
    MATERIAL_LOT: intl.get(`${modelPrompt}.MATERIAL_LOT`).d('物料批'),
    'MT.WO_ROUTER': intl.get(`${modelPrompt}.MT.WO_ROUTER`).d('工艺路线'),
    'MT.ROUTER_STEP': intl.get(`${modelPrompt}.MT.ROUTER_STEP`).d('工艺路线步骤'),
    'APEX_MES.USER_EQUIP_WKC_REL': intl.get(`${modelPrompt}.APEX_MES.USER_EQUIP_WKC_REL`).d('设备'),
  };

  const { history, custConfig, customizeForm } = props;
  const { id } = props.match.params;
  const { path } = props.match;
  const [canEdit, setCanEdit] = useState(false);
  const [tableHeight, setTableHeight] = useState(300);
  const [activeKey, setActiveKey] = useState('dataItemInfo');
  const dataItemTableDs = useMemo(() => new DataSet(dataItemTableDS()), []);
  const copyDs = useMemo(() => new DataSet(copyDS()), []);
  const assObjectsDs = useMemo(() => new DataSet(assObjectsDS()), []); // 对象分配表格的DS，由于要转的数据格式过多，不关联detailDs做children
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          tagGroupAssignTagList: dataItemTableDs,
        },
      }),
    [],
  );

  const historicalQueryDs = useMemo(() => new DataSet(historicalQueryDS()),[])
  // const numberListDs = useMemo(
  //   () =>
  //     new DataSet({
  //       ...numberListDS(),
  //     }),
  //   [id],
  // );
  // const trueNumberDs = useMemo(
  //   () =>
  //     new DataSet({
  //       ...numberListDS(),
  //     }),
  //   [id],
  // );
  // const falseNumberDs = useMemo(
  //   () =>
  //     new DataSet({
  //       ...numberListDS(),
  //     }),
  //   [id],
  // );
  let _copyDrawer;
  let _channelDrawer;

  useEffect(() => {
    setTableHeight(window.innerHeight - 470 > 300 ? window.innerHeight - 470 : 300);
    siteOptionDs.query(1, {
      tagGroupId: props.match.params.id === 'create' ? null : props.match.params.id,
    });
    if (props.match.params.id === 'create') {
      setCanEdit(true);
      return;
    }
    queryDetail(props.match.params.id);

  }, [props.match.params.id]);

  useEffect(() => {
    // detailDs.addEventListener('load', () => {
    //   // handleValidate()
    // });
  }, [])

  // const handleValidate = () => {
  //   if(dataItemTableDs.toData().some(item => item.valueAllowMissing === 'N')){
  //     detailDs.getField('missingNcCodeLov')?.set('required', true);
  //   }else{
  //     detailDs.getField('missingNcCodeLov')?.set('required', false);
  //   }
  // };
  const queryDetail = async kid => {
    detailDs.setQueryParameter('tagGroupId', kid);
    detailDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_DETAIL.BASIC`);
    const res = await detailDs.query();
    if (res && res.success) {
      const { tagGroupObjectList, tagGroupInfo } = res.rows;
      const { sourceGroupId, sourceGroupCode, missingNcCodeId, missingNcCode } = tagGroupInfo;
      const sourceGroupLov = {
        tagGroupId: sourceGroupId,
        tagGroupCode: sourceGroupCode,
      };

      const missingNcCodeLov = {
        missingNcCodeId,
        missingNcCode,
      }
      const assObjDsList = (tagGroupObjectList || []).map((item, index) => {
        return {
          ...item,
          lineNumber: index + 1,
        };
      });
      detailDs.current.set('sourceGroupLov', sourceGroupLov);
      detailDs.current.set('missingNcCodeLov', missingNcCodeLov);
      detailDs.current.set('missingNcCodeId', missingNcCodeId);
      detailDs.current.set('missingNcCode', missingNcCode);
      assObjectsDs.loadData(assObjDsList);
    }
  };

  const handleCancel = () => {
    if (id === 'create') {
      history.push('/hmes/acquisition/data-collection-new/list');
      return;
    }
    queryDetail(id);
    setCanEdit(prev => !prev);
  };

  const handleSave = async () => {
    const validate = await detailDs.validate();
    if (!validate) {
      return;
    }
    detailDs.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验
    const deleteAssObjectsDsList = assObjectsDs.destroyed.map(item => ({
      ...item.toData(),
      flag: 'D',
    }));
    const tagGroupObjectList = assObjectsDs.toData().map(item => {
      const obj = {
        tagGroupObjectId: item.tagGroupObjectId,
      };
      (item.objectList || []).forEach(it => {
        if (it.objectType === 'MATERIAL') {
          obj.materialId = it.objectId;
          obj.materialCode = it.objectCode;
          obj.materialName = it.objectDecs;
          obj.revisionCode = it.objectRevision;
        } else if (it.objectType === 'OPERATION') {
          obj.operationId = it.objectId;
          obj.operationName = it.objectCode;
          obj.operationDesc = it.objectDecs;
          obj.operationRevision = it.objectRevision;
        } else if (it.objectType === 'MT.WO_ROUTER') {
          obj.routerId = it.objectId;
          obj.routerName = it.objectCode;
          obj.routerDesc = it.objectDecs;
          obj.routerRevision = it.objectRevision;
        } else if (it.objectType === 'MT.ROUTER_STEP') {
          obj.routerStepId = it.objectId;
          obj.routerStepName = it.objectCode;
          obj.routerStepDesc = it.objectDecs;
        } else if (it.objectType === 'WORKCELL') {
          obj.workcellId = it.objectId;
          obj.workcellCode = it.objectCode;
          obj.workcellName = it.objectDecs;
        } else if (it.objectType === 'NC_CODE') {
          obj.ncCodeId = it.objectId;
          obj.ncCode = it.objectCode;
          obj.ncCodeDesc = it.objectDecs;
        } else if (it.objectType === 'BOM') {
          obj.bomId = it.objectId;
          obj.bomName = it.objectCode;
          obj.bomDesc = it.objectDecs;
          obj.bomRevision = it.objectRevision;
        } else if (it.objectType === 'BOM_COMPONENT') {
          obj.bomComponentId = it.objectId;
          obj.bomComponentMaterialCode = it.objectCode;
          obj.bomComponentMaterialName = it.objectDecs;
        } else if (it.objectType === 'SUBSTEP') {
          obj.substepId = it.objectId;
          obj.substepName = it.objectCode;
          obj.substepDes = it.objectDecs;
        } else if (it.objectType === 'WORK_ORDER') {
          obj.workOrderId = it.objectId;
          obj.workOrderNum = it.objectCode;
        } else if (it.objectType === 'EO') {
          obj.eoId = it.objectId;
          obj.eoNum = it.objectCode;
        } else if (it.objectType === 'MATERIAL_CATEGORY') {
          obj.materialCategoryId = it.objectId;
          obj.materialCategoryCode = it.objectCode;
          obj.materialCategoryDes = it.objectDecs;
        } else if(it.objectType === 'APEX_MES.USER_EQUIP_WKC_REL'){
          obj.equipmentId = it.objectId;
          obj.equipmentCode = it.objectCode;
        } else {
          // MATERIAL_LOT
          obj.materialLotId = it.objectId;
          obj.materialLotCode = it.objectCode;
        }
      });
      return obj;
    });
    const deleteTagGroupAssignTagList = dataItemTableDs.destroyed.map(item => ({
      ...item.toData(),
      flag: 'D',
    }));
    const requestData = {
      tagGroupInfo: {
        ...detailDs.current.get('tagGroupInfo'),
        missingNcCodeId: detailDs.current.toJSONData().missingNcCodeId,
        missingNcCode: detailDs.current.toJSONData().missingNcCode,
        _tls: detailDs.current.toData()._tls,
      },
      tagGroupAssignTagList: dataItemTableDs.toData().concat(deleteTagGroupAssignTagList),
      tagGroupObjectList: tagGroupObjectList.concat(deleteAssObjectsDsList),
    };
    // ${BASIC.HMES_BASIC}
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag-group/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_DETAIL.BASIC`,
      {
        method: 'POST',
        body: requestData,
      },
    );
    if (getResponse(res)) {
      notification.success();
      const newKid = res.rows;
      setCanEdit(prev => !prev);
      if (id === 'create') {
        history.push(`/hmes/acquisition/data-collection-new/detail/${newKid}`);
        return;
      }
      queryDetail(newKid);
    }
  };

  const handleChangeTab = newActiveKey => {
    setActiveKey(newActiveKey);
  };

  const handleChangeTagGroupType = () => {
    detailDs.current.set('sourceGroupLov', {});
  };

  const handleChangeSourceGroupLov = value => {
    if (value) {
      detailDs.current.set('sourceGroupId', value.tagGroupId);
      detailDs.current.set('sourceGroupCode', value.tagGroupCode);
    } else {
      detailDs.current.set('sourceGroupId', 0);
      detailDs.current.set('sourceGroupCode', null);
    }
  };

  const handleChangeMissNcCode = value => {
    if (value) {
      detailDs.current.set('missingNcCodeId', value.ncCodeId);
      detailDs.current.set('missingNcCode', value.ncCode);
    } else {
      detailDs.current.set('missingNcCodeId', null);
      detailDs.current.set('missingNcCode', null);
    }
  }

  const copyTagGroup = async () => {
    const validate = await copyDs.validate();
    if (!validate) {
      return;
    }
    copyDs.submit().then(res => {
      if (res) {
        const newKid = res.content[0].rows;
        _copyDrawer.close();
        history.push(`/hmes/acquisition/data-collection-new/detail/${newKid}`);
      }
    });
  };

  const handlePreview = () => {
    const cardDetail = {
      ...detailDs.current.toData(),
    };
    const dataSource = dataItemTableDs.toData();
    _copyDrawer = Modal.open({
      closable: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.collectionPreview`).d('数据采集实绩预览'),
      drawer: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: <PreviewDrawer dataSource={dataSource} cardDetail={cardDetail} />,
      footer: (
        <Button
          style={{ float: 'right' }}
          onClick={() => {
            _copyDrawer.close();
          }}
        >
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  const handleChannel = () => {
    _channelDrawer = Modal.open({
      closable: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.channelDrawerTitle`).d('分发渠道'),
      drawer: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: <ChannelDrawer match={props.match} />,
      footer: (
        <div>
          <Button
            style={{ float: 'right' }}
            onClick={() => {
              _channelDrawer.close();
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
        </div>
      ),
    });
  };

  const handleCopy = () => {
    copyDs.loadData([{ sourceTagGroupId: id }]);
    _copyDrawer = Modal.open({
      closable: true,
      key: Modal.key(),
      title: intl.get('tarzan.common.button.copy').d('复制'),
      drawer: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal',
      children: <CopyDrawer record={copyDs.current} />,
      footer: (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              copyDs.remove(copyDs.current);
              _copyDrawer.close();
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            type="submit"
            onClick={() => {
              copyTagGroup();
            }}
            color={ButtonColor.primary}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  const columns = [
    { name: 'tagGroupCode' },
    { name: 'tagGroupDescription' },
    { name: 'tagGroupType' },
    { name: 'businessType' },
    { name: 'status' },
    { name: 'collectionTimeControl' },
    { name: 'createdByName' },
    {
      name: 'creationDate',
      width: 150,
    },
    {
      name: 'lastUpdateDate',
      width: 150,
    },
    {
      name: 'missingNcCode',
      width: 160,
    },
  ];

  const dataItemInfoColumns = [
    { name: 'tagGroupCode' },
    { name: 'tagGroupDescription'},
    { name: 'serialNumber'},
    {
      name: 'collectionMethod',
      width: 150,
    },
    { name: 'valueAllowMissing'},
    {
      name: 'allowUpdateFlag',
      width: 150,
    },
    {
      name: 'trueValue',
      renderer: ({ value, record }) => {
        if (record.get('valueType') === 'VALUE') {
          if (record.get('trueValueList') && record.get('trueValueList').length) {
            const temp = record.get('trueValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue && <Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {
      name: 'falseValue',
      renderer: ({ value, record }) => {
        if (record.get('valueType') === 'VALUE') {
          if (record.get('falseValueList') && record.get('falseValueList').length) {
            const temp = record.get('falseValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue && <Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {
      name: 'displayValueFlag',
      width: 150,
    },
    { name: 'eventId'},
    { name: 'createdByName'},
    {
      name: 'creationDate',
      width: 150,
    },
    {
      name: 'lastUpdateDate',
      width: 150,
    },
    {
      name: 'specialRecordFlag',
      width: 150,
    },
    {
      name: 'defaultNcCode',
      width: 150,
    },
    { name: 'uomDesc'},
  ];

  const assObjectsColumns = [
    {
      name: 'tagGroupCode',
      width: 150,
    },
    { name: 'tagGroupDescription' },
    {
      name: 'objectList',
      renderer: ({ record }) => {
        return (record.get('objectList') || []).length && (record.get('objectList') || []).map(item => (
          <Tag key={item.objectId} color="blue">
            {`${typeName[item.objectType]}：${item.objectDesc ? item.objectDesc : item.objectCode}`}
          </Tag>
        ));
      },
    },
    { name: 'createdByName' },
    { name: 'creationDate' },
    { name: 'lastUpdateDate' },
  ];

  const handleHistoricalQuery = (type) => {
    historicalQueryDs.setState('historicalKey', type)
    historicalQueryDs.setQueryParameter('tagGroupId', id)
    historicalQueryDs.query()
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.HistoricalQuery`).d('历史查询'),
      drawer: true,
      style: {
        width: 900,
      },
      cancelButton: false,
      okButton: false,
      maskClosable: true,
      closable: true,
      children: (
        <Table
          searchCode="sjsjz"
          customizedCode="sjsjz"
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={historicalQueryDs}
          columns={type === 'homepage' ? columns : (type === 'dataItemInfo' ? dataItemInfoColumns : assObjectsColumns)}
        />
      ),
    });
  }

  const childProps = {
    canEdit,
    tableHeight,
    // handleValidate,
  };

  const operations = <PermissionButton
    type="c7n-pro"
    style={{ marginLeft: '8px' }}
    onClick={() => { handleHistoricalQuery(activeKey) }}
    disabled={id === 'create'}
  >
    {intl.get(`${modelPrompt}.historicalQuery`).d('历史查询')}
  </PermissionButton>

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.hmes.acquisition.collection.collectionGroupMaintenance')
          .d('数据收集组维护')}
        backPath="/hmes/acquisition/data-collection-new/list"
      >
        {canEdit && (
          <>
            <Button
              style={{ marginLeft: '8px' }}
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button style={{ marginLeft: '8px' }} icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            style={{ marginLeft: '8px' }}
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          style={{ marginLeft: '8px' }}
          icon="content_copy-o"
          disabled={id === 'create' || canEdit}
          onClick={handleCopy}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.copy').d('复制')}
        </PermissionButton>
        <AttributeDrawer
          serverCode={BASIC.HMES_BASIC}
          style={{ marginLeft: '8px' }}
          className="org.tarzan.mes.domain.entity.MtTagGroup"
          kid={id}
          canEdit={canEdit}
          disabled={id === 'create'}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_DETAIL.ATTR`}
          custConfig={custConfig}
        />
        <PermissionButton
          type="c7n-pro"
          style={{ marginLeft: '8px' }}
          icon="preview"
          onClick={handlePreview}
        >
          {intl.get('tarzan.common.button.preview').d('预览')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          style={{ marginLeft: '8px' }}
          onClick={handleChannel}
          disabled={id === 'create'}
        >
          {intl.get(`${modelPrompt}.channelDrawerTitle`).d('分发渠道')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          style={{ marginLeft: '8px' }}
          onClick={() => { handleHistoricalQuery('homepage') }}
          disabled={id === 'create'}
        >
          {intl.get(`${modelPrompt}.historicalQuery`).d('历史查询')}
        </PermissionButton>
      </Header>
      <Content>
        <Spin dataSet={detailDs}>
          <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
            <Panel
              header={intl.get(`${modelPrompt}.baseInfo`).d('基础信息')}
              key="basicInfo"
              dataSet={detailDs}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_DETAIL.BASIC`,
                },
                <Form disabled={!canEdit} dataSet={detailDs} columns={3}>
                  <TextField name="tagGroupCode" />
                  <IntlField
                    name="tagGroupDescription"
                    modalProps={{
                      title: intl.get(`${modelPrompt}.tagGroupDescription`).d('数据收集组描述'),
                    }}
                  />
                  <Select name="status" />
                  <Select
                    name="siteIds"
                    colSpan={2}
                    onOption={({ dataSet, record }) => {
                      const currentSiteId = record.toData().siteId;
                      const disabled = dataSet
                        .toData()
                        .some(item => item.siteId === currentSiteId && !item.permissionFlag);
                      return { disabled };
                    }}
                  />
                  <Select name="businessType" />
                  <Select name="tagGroupType" onChange={handleChangeTagGroupType} />
                  <Select name="collectionTimeControl" />
                  <Lov name="sourceGroupLov" onChange={handleChangeSourceGroupLov} />
                  <Lov name="missingNcCodeLov" onChange={handleChangeMissNcCode} />
                </Form>,
              )}
            </Panel>
          </Collapse>
          <Tabs onChange={handleChangeTab} activeKey={activeKey} tabBarExtraContent={operations}>
            <TabPane
              tab={intl.get(`${modelPrompt}.dataItemInfo`).d('数据项信息')}
              key="dataItemInfo"
              forceRender
            >
              <DataItemInfoTab
                {...childProps}
                dataItemTableDs={dataItemTableDs}
              />
            </TabPane>
            <TabPane
              tab={intl.get(`${modelPrompt}.assObjects`).d('关联对象')}
              key="assObjects"
              forceRender
            >
              <AssociatedObjectsTab
                {...childProps}
                assObjectsDs={assObjectsDs}
                detailDs={detailDs}
              />
            </TabPane>
          </Tabs>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.acquisition.collection', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_DETAIL.ATTR`, `${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_DETAIL.BASIC`],
  })(CollectionDetail),
);
