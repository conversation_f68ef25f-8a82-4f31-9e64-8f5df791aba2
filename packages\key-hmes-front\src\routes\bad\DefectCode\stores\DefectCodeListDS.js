/**
 * @Description: 不良代码维护列表DS
 * @Author: <<EMAIL>>
 * @Date: 2022-07-12 10:00:25
 * @LastEditTime: 2023-02-06 10:35:29
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.badCode.defectCode.model.defectCode';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: true,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-nc-code/list/ui`,
        method: 'get',
      };
    },
  },
  queryFields: [
    {
      name: 'ncCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码描述'),
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteId`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'ncType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?stateType=ncTypeList&typeGroup=NC_TYPE&module=NC_CODE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.ncType`).d('不良代码类型'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'scrapDetail',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapDetail`).d('不良报废明细'),
    },
  ],
  fields: [
    {
      name: 'ncCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码描述'),
    },
    {
      name: 'scrapDetail',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapDetail`).d('不良报废明细'),
    },
    {
      name: 'ncType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?stateType=ncTypeList&typeGroup=NC_TYPE&module=NC_CODE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.ncType`).d('不良代码类型'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
    },
    {
      name: 'ncGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncGroupDesc`).d('不良代码组'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
});

export { tableDS };
