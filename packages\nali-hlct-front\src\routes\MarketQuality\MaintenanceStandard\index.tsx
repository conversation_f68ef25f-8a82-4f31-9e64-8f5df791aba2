/**
 * RelationMaintain - 维修标准管理
 * @date: 2023-9-7
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import React, { useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { DataSet, Table, Modal, Select } from 'choerodon-ui/pro';
import { Tabs } from 'choerodon-ui';
import withProps from 'utils/withProps';
import { observer } from 'mobx-react';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnLock, ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import intl from 'utils/intl';
import { getCurrentUser } from 'utils/utils';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import DetailDraw from './detailDraw';

import { maintainFileSubmitConfig } from './services';
import { listTableManualDS, listTableProgrammeDS } from './stores/MaintenanceStandardDS';

const TabPane = Tabs.TabPane;
let detailmodal;

const modelPrompt = 'tarzan.qms.maintenanceStandard';

function MaintenanceStandard(props) {
  const {
    tableManualDs,
    tableProgrammeDs,
    match: { path },
  } = props;

  // 提交维修手册
  const maintainFileSubmit = useRequest(maintainFileSubmitConfig(), {
    manual: true,
    needPromise: true,
    throttleInterval: 0,
  });

  const [user] = useState(getCurrentUser()); // 用户详细信息

  useEffect(() => {
    if (tableManualDs) {
      tableManualDs.query(props.tableManualDs.currentPage);
    } else {
      tableManualDs.query();
    }
    if (tableProgrammeDs) {
      tableProgrammeDs.query(props.tableProgrammeDs.currentPage);
    } else {
      tableProgrammeDs.query();
    }
  }, []);

  const [activeTab, setActiveTab]: any = useState('manual'); // 当前tab

  const handleActiveTabChange = value => {
    setActiveTab(value);
  };

  const handleOpenModal = (record, type) => {
    const data = type === 'edit' ? record.toData() : {};

    detailmodal = Modal.open({
      ...drawerPropsC7n({}),
      destroyOnClose: true,
      key: Modal.key(),
      title: `${
        type === 'create'
          ? intl.get(`${modelPrompt}.add`).d('新建')
          : intl.get(`${modelPrompt}.edit`).d('编辑')
      }${
        activeTab === 'manual'
          ? intl.get(`${modelPrompt}.manual`).d('维修手册')
          : intl.get(`${modelPrompt}.programme`).d('维修方案')
      }`,
      style: {
        width: '720px',
      },
      footer: null,
      maskClosable: true,
      children: (
        <DetailDraw
          type={type}
          activeTab={activeTab}
          data={data}
          callBack={handleAddSubSaveCallBack}
          closeModal={closeModal}
          user={user}
        />
      ),
    });
  };

  const closeModal = () => {
    detailmodal.close();
  };

  const handleAddSubSaveCallBack = async () => {
    if (activeTab === 'manual') {
      tableManualDs.query(props.tableManualDs.currentPage);
    } else {
      tableProgrammeDs.query(props.tableProgrammeDs.currentPage);
    }
  };

  const handleEdit = record => {
    handleOpenModal(record, 'edit');
  };

  const handleCreate = () => {
    handleOpenModal(null, 'create');
  };
  const handleSubmit = async () => {
    let list = [];
    if (activeTab === 'manual') {
      list = tableManualDs.selected.map(item => {
        return item.get('maintainFileId');
      });
    }
    if (activeTab === 'programme') {
      list = tableProgrammeDs.selected.map(item => {
        return item.get('maintainFileId');
      });
    }

    const allResult = await Promise.all(
      list.map(async (maintainFileId: any) => {
        const res = await maintainFileSubmit.run({
          queryParams: {
            maintainFileId,
          },
        });
        return res?.success;
      }),
    );

    if (allResult.every(value => value)) {
      notification.success({});
    }

    if (activeTab === 'manual') {
      tableManualDs.query(props.tableManualDs.currentPage);
      tableManualDs.batchUnSelect(tableManualDs.selected);
    } else {
      tableProgrammeDs.query(props.tableProgrammeDs.currentPage);
      tableProgrammeDs.batchUnSelect(tableProgrammeDs.selected);
    }
  };

  const tableManualColumns: ColumnProps[] = [
    {
      name: 'maintainFileCode',
      lock: ColumnLock.left,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
      width: 220,
    },
    {
      name: 'maintainFileName',
      width: 180,
    },
    {
      name: 'maintainFileStatus',
    },
    {
      name: 'batteryNumLov',
      width: 120,
    },
    {
      name: 'batteryModel',
    },
    {
      name: 'vehicleModel',
    },
    {
      name: 'startIntlCode',
    },
    {
      name: 'endIntlCode',
    },
    {
      name: 'replaceMaintainFileLov',
      width: 120,
    },
    {
      name: 'maintainFileUuid',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'createdBylov',
    },
    {
      name: 'approvalInfo',
      title: intl.get(`${modelPrompt}.approvalInfo`).d('审批信息'),
      width: 140,
      align: ColumnAlign.center,
      renderer: ({ record }) => {
        return (
          <ApprovalInfoDrawer
            type="text"
            objectTypeList={['QIS_WXSC_LWS']}
            objectId={record?.get('maintainFileId')}
          />
        );
      },
    },
  ];

  const tableProgrammeColumns: ColumnProps[] = [
    {
      name: 'maintainFileCode',
      lock: ColumnLock.left,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
      width: 220,
    },
    {
      name: 'maintainFileStatus',
    },
    {
      name: 'batteryNumLov',
      width: 120,
    },
    {
      name: 'batteryModel',
    },
    {
      name: 'vehicleModel',
    },
    {
      name: 'faultDescription',
    },
    {
      name: 'faultTime',
    },
    {
      name: 'faultReason',
    },
    {
      name: 'checkScheme',
    },
    {
      name: 'maintainScheme',
    },
    {
      name: 'maintainFileUuid',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'createdBylov',
    },
    {
      name: 'approvalInfo',
      title: intl.get(`${modelPrompt}.approvalInfo`).d('审批信息'),
      width: 140,
      align: ColumnAlign.center,
      renderer: ({ record }) => {
        return (
          <ApprovalInfoDrawer
            type="text"
            objectTypeList={['QIS_WXSC_LWS']}
            objectId={record?.get('maintainFileId')}
          />
        );
      },
    },
  ];

  const SubmitButton = observer(buttonProps => {
    const submitAuth = () => {
      let statusOk = false;
      if (buttonProps.activeTab === 'manual' && buttonProps.tableManualDs.selected.length > 0) {
        buttonProps.tableManualDs.selected.forEach(record => {
          if (!['NEW', 'REJECTED'].includes(record.get('maintainFileStatus'))) {
            statusOk = true;
          }
        });
        return statusOk;
      }
      if (
        buttonProps.activeTab === 'programme' &&
        buttonProps.tableProgrammeDs.selected.length > 0
      ) {
        buttonProps.tableProgrammeDs.selected.forEach(record => {
          if (!['NEW', 'REJECTED'].includes(record.get('maintainFileStatus'))) {
            statusOk = true;
          }
        });
        return statusOk;
      }
      return true;
    };

    return (
      <PermissionButton
        type="c7n-pro"
        disabled={submitAuth()}
        permissionList={[
          {
            code: `${path}.button.submit`,
            type: 'button',
            meaning: '列表页-提交',
          },
        ]}
        onClick={handleSubmit}
      >
        {intl.get('tarzan.common.button.submit').d('提交')}
      </PermissionButton>
    );
  });

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.maintenanceStandard`).d('维修标准管理')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.create`,
              type: 'button',
              meaning: '列表页-新建',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <SubmitButton
          activeTab={activeTab}
          tableManualDs={tableManualDs}
          tableProgrammeDs={tableProgrammeDs}
        />
      </Header>
      <Content>
        <Tabs activeKey={activeTab} onChange={handleActiveTabChange}>
          <TabPane 
            tab={intl.get(`${modelPrompt}.serviceManual`).d('维修手册')} 
            key="manual"
          ></TabPane>
          <TabPane 
            tab={intl.get(`${modelPrompt}.maintenancePlan`).d('维修方案')} 
            key="programme"
          ></TabPane>
        </Tabs>
        <div
          style={{
            display: activeTab === 'manual' ? 'block' : 'none',
          }}
        >
          <Table
            queryFields={{
              vehicleModel: <Select searchable />,
            }}
            searchCode="wxbzgl1"
            customizedCode="wxbzgl1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableManualDs}
            columns={tableManualColumns}
          />
        </div>
        <div
          style={{
            display: activeTab === 'programme' ? 'block' : 'none',
          }}
        >
          <Table
            queryFields={{
              vehicleModel: <Select searchable />,
            }}
            searchCode="wxbzgl2"
            customizedCode="wxbzgl2"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableProgrammeDs}
            columns={tableProgrammeColumns}
          />
        </div>
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.qms.maintenanceStandard', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableManualDs = new DataSet({
        ...listTableManualDS(),
      });
      const tableProgrammeDs = new DataSet({
        ...listTableProgrammeDS(),
      });
      return {
        tableManualDs,
        tableProgrammeDs,
      };
    },
    { cacheState: true, keepOriginDataSet: true },
  )(MaintenanceStandard),
);
