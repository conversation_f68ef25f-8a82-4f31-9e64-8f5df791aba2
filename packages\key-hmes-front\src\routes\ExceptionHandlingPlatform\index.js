import React, { Component, Fragment } from 'react';
import { Row, Spin } from 'hzero-ui';
import { connect } from 'dva';
import qs from 'querystring';
import { isEmpty } from 'lodash';

import { Header, Content } from 'components/Page';
import { Bind } from 'lodash-decorators';
import notification from 'utils/notification';
import { getCurrentUser, getCurrentOrganizationId } from 'utils/utils';
// import intl from 'utils/intl';
import { closeTab } from 'utils/menuTab';
import moment from 'moment';
import { DEFAULT_DATETIME_FORMAT } from 'utils/constants';

import styles from './index.less';
import ExceptionDrawer from './Drawer/ExceptionDrawer';
import AddExceptionModal from './Drawer/AddExceptionModal';
import CardList from './Component/CardList';
import EnterModal from './Component/EnterModal';
import HandQueryModal from './Component/HandQueryModal';

@connect(({ exceptionHandlingPlatform, loading }) => ({
  tenantId: getCurrentOrganizationId(),
  exceptionHandlingPlatform,
  loading,
  closeExceptionLoading: loading.effects['exceptionHandlingPlatform/closeException'] || false,
  enterModalLoading: loading.effects['exceptionHandlingPlatform/enterSite'] || false,
  responseExceptionLoading: loading.effects['exceptionHandlingPlatform/responseException'] || false,
  createExceptionRecordLoading: loading.effects['exceptionHandlingPlatform/createExceptionRecord'],
}))
export default class ExceptionHandlingPlatform extends Component {
  form;

  constructor(props) {
    super(props);
    const { workcellId } = this.props.match.params;
    this.state = {
      exceptioListVisible: false,
      exceptionModal: false, // 异常提交model
      modalType: '', // 模态框类型
      // eslint-disable-next-line react/no-unused-state
      visible: false,
      fetchVal: {},
      spinning: false,
      exceptionLabelListDetail: {}, // 按钮的明细内容
      hmeSignInOutRecordDTO1List: [], // 出勤人员
      workcellId,
      operationTabFlag: false,
      enterSiteInfo: {}, // 登入信息
      handQueryVisible: false,
      nowQtyLoading: false,
      defaultResponsible: {}, // 默认责任人
      routerParam: null,
    };
  }

  componentDidMount() {
    const { dispatch } = this.props;
    // dispatch({
    //   type: 'exceptionHandlingPlatform/getSiteList',
    //   payload: {},
    // });
    const { workcellId } = this.props.match.params;
    // const { loginWkcInfo } = this.props.location.state;
    if (workcellId === 'menu' && isEmpty(this.props.location?.state?.loginWkcInfo)) {
      this.setState({ visible: true });
    }
    if(!isEmpty(this.props.location?.state?.loginWkcInfo)){
      const obj = {
        workcellId: this.props.location?.state?.loginWkcInfo.workStationId,
        workcellCode: this.props.location?.state?.loginWkcInfo.workStationCode,
        workcellName: this.props.location?.state?.loginWkcInfo.workStationName,
      }
      this.localSaveWorkCell(obj);
      this.enterSite(obj);
    }

    dispatch({
      type: 'exceptionHandlingPlatform/querySelect',
      payload: {
        exceptionResult: 'HME.EXC_CONTYPE',
        abnormalTypeList: 'HME.EXCEPTION_TYPE',
      },
    });
  }

  componentWillReceiveProps(nextProps) {
    const routerParam = qs.parse(nextProps.location.search.substr(1));
    if (!isEmpty(routerParam)) {
      if (this.state.operationTabFlag !== routerParam.operationTabFlag) {
        clearInterval(this.refreshUpgrade);
        window.speechSynthesis.cancel();
        // if (!routerParam.taskOrderId || !routerParam.taskOrderNum || !routerParam.workcellCode) {
        this.enterSiteUpgrade({
          taskOrderId: routerParam.taskOrderId,
          taskOrderNum: routerParam.taskOrderNum,
          workcellCode: routerParam.workcellCode,
          // workcellId: routerParam.workcellId,
        });
        this.setState({ operationTabFlag: routerParam.operationTabFlag, routerParam });
      }
      if (
        this.state.routerParam &&
        this.state.routerParam.taskOrderId !== routerParam.taskOrderId
      ) {
        clearInterval(this.refreshUpgrade);
        window.speechSynthesis.cancel();
        this.enterSiteUpgrade({
          taskOrderId: routerParam.taskOrderId,
          taskOrderNum: routerParam.taskOrderNum,
          workcellCode: routerParam.workcellCode,
          // workcellId: routerParam.workcellId,
        });
        this.setState({ operationTabFlag: routerParam.operationTabFlag, routerParam });
      }
      // }
    }
  }

  componentWillUnmount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'exceptionHandlingPlatform/updateState',
      payload: {
        exceptionList: [],
        equipmentInfo: {}, // 设备信息
        exceptionRecord: {}, // 历史记录
        materialInfo: {}, // 物料信息
        exceptionStatus: '', // 异常信息状态
        exceptionResultList: [], // 异常处理结果类型
        solvedResult: '', // 异常处理结果
      },
    });
    clearInterval(this.refreshUpgrade);
    window.speechSynthesis.cancel();
  }

  /**
   * @description: 模态框隐藏-新增异常/异常清单
   * @param {Boolean} exceptionModal 新增异常模态框
   * @param {Boolean} exceptioListVisible 异常清单
   * @param {String} type 点击的类型
   * @param {String} data 异常消息记录
   */
  @Bind()
  showModal(exceptionModal, exceptioListVisible, type) {
    const { dispatch } = this.props;
    this.setState({ exceptionModal, exceptioListVisible, modalType: type });
    if (!exceptioListVisible) {
      dispatch({
        type: 'exceptionHandlingPlatform/updateState',
        payload: {
          exceptionStatus: '',
        },
      });
    }
    if (exceptioListVisible) {
      const leftDrawer = document.getElementById('leftDrawer');
      const rightDrawer = document.getElementById('rightDrawer');
      if (leftDrawer.offsetHeight > rightDrawer.offsetHeight) {
        rightDrawer.style.height = `${leftDrawer.offsetHeight}px`;
      } else {
        leftDrawer.style.height = `${rightDrawer.offsetHeight}px`;
      }
    }
  }

  @Bind()
  localSaveWorkCell(object) {
    const { dispatch } = this.props;
    dispatch({
      type: 'exceptionHandlingPlatform/updateState',
      payload: {
        localWorkCellObj: object,
      },
    });
  }

  /**
   * @description: 异常创建
   * @param {type} type 类型（人员、设备、物料、工艺质量、环境）
   * @param {Object} exceptionLabelListDetail 异常明细，即每个按钮的内容
   */
  @Bind()
  submitException(type, exceptionLabelListDetail, hmeSignInOutRecordDTO1List) {
    this.setState({
      exceptionModal: true,
      modalType: type,
      exceptionLabelListDetail,
      hmeSignInOutRecordDTO1List,
    });
  }

  /**
   * @description: 输入工位
   * @param {String} values 工位编码
   */
  @Bind()
  enterSite(values) {
    this.setState({ spinning: true });
    const { dispatch } = this.props;
    dispatch({
      type: 'exceptionHandlingPlatform/enterSite',
      payload: {
        ...values,
      },
    }).then(res => {
      if (res && res.failed) {
        notification.error({ description: `${res.exception ? res.exception : res.message}` });
      } else {
        this.setState({ visible: false, spinning: false, enterSiteInfo: values });
      }
    });
  }

  /**
   * @description: 输入工位并定时跑升级
   * @param {String} values 工位编码
   */
  @Bind()
  enterSiteUpgrade(values) {
    this.setState({ spinning: true });
    const { dispatch } = this.props;
    dispatch({
      type: 'exceptionHandlingPlatform/enterSite',
      payload: {
        ...values,
      },
    }).then(res => {
      if (res && res.failed) {
        notification.error({ description: `${res.exception ? res.exception : res.message}` });
      } else {
        this.setState({ visible: false, spinning: false, enterSiteInfo: values });
        this.refreshUpgrade = setInterval(() => {
          dispatch({
            type: 'exceptionHandlingPlatform/refreshUpgrade',
            payload: {
              workcellId: res[0].workcellId,
            },
          }).then(upgradeRes => {
            if (upgradeRes && upgradeRes[0]) {
              upgradeRes.forEach(msg => {
                this.speckText(msg);
              });
            }
          });
        }, 600000);
      }
    });
  }

  /**
   * @description: 扫描设备
   * @param {Object} values form参数
   * @param {String} modalType 异常类型：物料或设备
   */
  @Bind()
  enterEquipment(values, modalType) {
    const { dispatch } = this.props;
    dispatch({
      type:
        modalType === 'EQUIPMENT'
          ? 'exceptionHandlingPlatform/enterEquipment'
          : 'exceptionHandlingPlatform/enterMaterial',
      payload: {
        ...values,
      },
    }).then(res => {
      if (res) {
        notification.success();
      }
    });
  }

  /**
   * @description: 创建异常记录
   * @param {Object} values form参数
   */
  @Bind()
  createExceptionRecord(values, exceptionLabelListDetail, modalType) {
    this.setState({
      fetchVal: values,
    });
    const {
      dispatch,
      exceptionHandlingPlatform: {
        equipmentInfo = {},
        materialInfo = {},
        exceptionList = [],
        employName = '',
        materialCode = '',
      },
    } = this.props;
    const modalTypeDes = exceptionList.filter(item => item.exceptionType === modalType)[0]
      .exceptionTypeMeaning;
    // eslint-disable-next-line no-unused-vars
    const asyncProps = {
      exceptionWkcRecordId: exceptionLabelListDetail.exceptionId,
      exceptionType: modalTypeDes,
      exceptionCode: exceptionLabelListDetail.exceptionCode,
      exceptionName: exceptionLabelListDetail.exceptionName,
      exceptionRemark: values.exceptionRemark,
      workcellName: exceptionLabelListDetail.workcellName,
      initiator: getCurrentUser(),
      currentTime: moment().format(DEFAULT_DATETIME_FORMAT),
    };
    dispatch({
      type: 'exceptionHandlingPlatform/createExceptionRecord',
      payload: {
        ...exceptionLabelListDetail,
        taskOrderId: this.state.enterSiteInfo.taskOrderId,
        equipmentId: equipmentInfo.equipmentId,
        ...values,
        materialId: materialInfo.materialId,
        userId: getCurrentUser().id,
        attribute9: employName,
        materialCode: materialCode || null,
        defaultUserId: this.state.defaultResponsible.userId,
      },
    }).then(res => {
      if (res) {
        notification.success();
        if (res.msg) {
          this.speckText(res.msg);
        }
        this.showModal(false, false, '');
        this.setState({ spinning: true });
        dispatch({
          type: 'exceptionHandlingPlatform/enterSite',
          payload: {
            ...this.state.enterSiteInfo,
          },
        }).then(() => {
          this.setState({ spinning: false });
        });
        dispatch({
          type: 'exceptionHandlingPlatform/updateState',
          payload: {
            enterEquipment: {},
            materialInfo: {},
            materialCode: '',
          },
        });

        // // 调用接口查询数据
        // dispatch({
        //   type: 'exceptionHandlingPlatform/fetchLineList',
        //   payload: {
        //     exceptionId: exceptionLabelListDetail.exceptionId,
        //   },
        // }).then((resReturn) => {
        // if (resReturn) {
        // const data = resReturn;
        // for (let i = 0; i < data.content.length; i++) {
        // dispatch({
        //   type: 'exceptionHandlingPlatform/fetchPositionData',
        //   payload: {
        //     positionId: data.content[i].respondPositionId,
        //   },
        // }).then((resPositionData) => {
        //   if (resPositionData) {
        //     const dataRes = resPositionData.content;
        //     for (let j = 0; j < dataRes.length; j++) {
        //       dataRes[j].realName = dataRes[j].name;
        //     }
        //     data.content[i].approvedBy = dataRes;
        //   }
        // // 判断是否是最后一条数据 是则继续
        // if (i === data.content.length - 1) {
        //   asyncProps.exceptions = data.content;
        //   asyncProps.initiator.mobile = asyncProps.initiator.phone;
        //   asyncProps.initiator.userId = getCurrentUserId();

        // // 查询用户对应的员工信息
        // dispatch({
        //   type: 'exceptionHandlingPlatform/fetchUserData',
        //   payload: {
        //     userId: getCurrentUserId(),
        //   },
        // }).then((employeeData) => {
        // if (employeeData) {
        // asyncProps.initiator.employeeCode = employeeData.employeeNum;
        // 最终发送异步操作请求
        // dispatch({
        //   type: 'exceptionHandlingPlatform/asyncSetData',
        //   payload: asyncProps,
        // }).then((result) => {
        //   if (!result.success) {
        //     notification.error({ message: result.message });
        //   }
        // });
        // }
        // });
        // }
        // });
        // }
        // }
        // });
      }
    });
  }

  // 语音播报
  @Bind
  speckText(str) {
    const msg = new SpeechSynthesisUtterance(str);
    msg.rate = 0.8;
    msg.lang = 'zh-CN'; // msg.lang='en-US';
    window.speechSynthesis.speak(msg);
  }

  /**
   * 异常处理按钮请求
   * @param {String} type - 操作类型
   */
  @Bind()
  handleTipException(type) {
    const urlType = ['cancel', 'close'].includes(type) ? 'closeException' : 'responseException';
    const {
      dispatch,
      exceptionHandlingPlatform: { exceptionRecord, solvedResult },
    } = this.props;
    this.myThreeRef.threeLov.props.form.validateFields((err, fieldsValue) => {
      if (!err) {
        dispatch({
          type: `exceptionHandlingPlatform/${urlType}`,
          payload: {
            ...exceptionRecord,
            ...fieldsValue,
            attribute1: solvedResult,
            tag: ['upgrade', 'cancel'].includes(type) ? 1 : 2,
            userId: getCurrentUser().id,
          },
        }).then(res => {
          if (res) {
            notification.success();
            this.showModal(false, false, '');
            this.setState({ spinning: true });
            dispatch({
              type: 'exceptionHandlingPlatform/enterSite',
              payload: {
                ...this.state.enterSiteInfo,
              },
            }).then(() => {
              this.setState({ spinning: false });
            });
            dispatch({
              type: 'exceptionHandlingPlatform/updateState',
              payload: {
                enterEquipment: {},
                materialInfo: {},
              },
            });
          }
        });
      }
    });
  }

  /**
   * 扫描工号
   */
  @Bind()
  queryEmployee() {
    const { dispatch, tenantId } = this.props;
    const fieldsValue = this.myThreeRef.threeLov.props.form.getFieldsValue() || {};
    const { employeeNum } = fieldsValue;
    dispatch({
      type: 'exceptionHandlingPlatform/queryEmployee',
      payload: {
        tenantId,
        employeeNum,
      },
    }).then(res => {
      if (res) {
        this.myThreeRef.threeLov.props.form.setFieldsValue({
          employName: res.employeeName,
        });
        notification.success();
      } else {
        this.myThreeRef.threeLov.props.form.setFieldsValue({
          employName: null,
        });
      }
    });
  }

  // 现有量获取
  @Bind()
  getNowQty(params) {
    this.setState({ nowQtyLoading: true });
    const { dispatch } = this.props;
    dispatch({
      type: 'exceptionHandlingPlatform/getNowQty',
      payload: {
        siteCode: params.siteCode ? params.siteCode : '',
        materialCode: params.materialCode ? params.materialCode : '',
        warehouseCode: params.warehouseCode ? params.warehouseCode : '',
      },
    }).then(res => {
      if (res) {
        notification.success();
        this.setState({ nowQtyLoading: false });
      }
    });
  }

  /**
   * 传递表单对象
   * @param {Object} ref - FilterForm对象
   */
  @Bind()
  handleBindRef(ref = {}) {
    this.form = (ref.props || {}).form;
    this.myThreeRef = ref || {};
  }

  /**
   * 异常处理结果变更
   * @param {String} val - 变更值
   */
  @Bind()
  changeSolvedResult(val) {
    const { dispatch } = this.props;
    dispatch({
      type: 'exceptionHandlingPlatform/updateState',
      payload: {
        solvedResult: val,
      },
    });
  }

  /**
   * 跳转至异常信息查看报表
   * @param {Object} params - 跳转至异常信息查看报表
   */
  @Bind()
  handleToExport(params) {
    closeTab('/hmes/abnormal-report');
    setTimeout(() => {
      const { history } = this.props;
      history.push({
        pathname: '/hmes/abnormal-report',
      });
    }, 100);

    window.localStorage.setItem('exceptionToExport', JSON.stringify(params));
  }

  /**
   * 显示现有量
   */
  handleHandQuerySearch = () => {
    this.setState({
      handQueryVisible: true,
    });
  };

  /**
   * 隐藏现有量
   */
  hideHandQuery = () => {
    this.setState({
      handQueryVisible: false,
    });
  };

  /**
   * 查询默认责任人
   */
  @Bind()
  queryDefaultResponsible() {
    const { dispatch } = this.props;
    const { exceptionLabelListDetail } = this.state;
    dispatch({
      type: 'exceptionHandlingPlatform/queryDefaultResponsible',
      payload: {
        exceptionId: exceptionLabelListDetail.exceptionId,
        exceptionGroupId: exceptionLabelListDetail.exceptionGroupId,
      },
    }).then(res => {
      if (res) {
        this.setState({ defaultResponsible: res });
      }
    });
  }

  render() {
    const {
      exceptionHandlingPlatform: {
        exceptionList = [],
        abnormalTypeList = [],
        exceptionRecord = {},
        exceptionStatus = '',
        exceptionResultList = [],
        solvedResult,
        localWorkCellObj = {},
      },
      closeExceptionLoading,
      responseExceptionLoading,
      createExceptionRecordLoading,
      enterModalLoading,
      tenantId,
    } = this.props;
    const {
      routerParam,
      exceptioListVisible,
      exceptionModal,
      modalType,
      spinning,
      exceptionLabelListDetail,
      enterSiteInfo,
      hmeSignInOutRecordDTO1List,
      handQueryVisible,
      nowQtyLoading = false,
      defaultResponsible,
      fetchVal,
    } = this.state;
    const enterModalProps = {
      visible: this.state.visible,
      loading: false,
      workcellId: this.state.workcellId,
      enterSite: this.enterSiteUpgrade,
      enterModalLoading,
      tenantId,
      localSaveWorkCell: this.localSaveWorkCell,
    };
    const drawerProps = {
      modalType,
      fetchVal,
      exceptioListVisible,
      exceptionRecord,
      closeExceptionLoading,
      responseExceptionLoading,
      exceptionStatus,
      exceptionResultList,
      solvedResult,
      tenantId,
      abnormalTypeList,
      enterSiteInfo,
      changeSolvedResult: this.changeSolvedResult,
      hideModal: this.showModal,
      // closeException: this.closeException,
      handleTipException: this.handleTipException,
      queryEmployee: this.queryEmployee,
      onRef: this.handleBindRef,
    };
    const addExcProps = {
      modalType,
      exceptionModal,
      exceptionLabelListDetail,
      createExceptionRecordLoading,
      hmeSignInOutRecordDTO1List,
      enterSiteInfo,
      defaultResponsible,
      hideModal: this.showModal,
      handleOk: this.createExceptionRecord,
      enterEquipment: this.enterEquipment,
      queryDefaultResponsible: this.queryDefaultResponsible,
    };
    const handQueryProps = {
      visible: handQueryVisible,
      handlecancel: this.hideHandQuery,
      getNowQty: this.getNowQty,
      nowQtyLoading,
    };
    return (
      <Fragment>
        <Header title="异常处理平台">
          {/* <span>指令单：{enterSiteInfo && enterSiteInfo.taskOrderNum}</span> */}
          <span style={{ marginRight: '20px' }}>
            工作单元：
            {!this.state.visible &&
              ((routerParam && routerParam.workcellName) || localWorkCellObj.workcellName)}
          </span>

          {/* <Button
            className={styles['exception-handling-platform-btn-pad']}
            type="primary"
            onClick={this.handleHandQuerySearch}
          >
            {intl.get('hmes.operationPlatform.view.message.handQuery.search').d('现有量查询')}
          </Button> */}
        </Header>
        <Content style={{ padding: '0px', backgroundColor: 'transparent', margin: '7px 7px 16px' }}>
          <Spin spinning={spinning}>
            <Row className={styles['exception-handling-platform']}>
              {exceptionList.map(item => {
                return (
                  <CardList
                    showModal={this.showModal}
                    {...item}
                    submitException={this.submitException}
                    handleToExport={this.handleToExport}
                  />
                );
              })}
            </Row>
          </Spin>
          {exceptioListVisible && <ExceptionDrawer {...drawerProps} />}
          {exceptionModal && <AddExceptionModal {...addExcProps} />}
          <EnterModal {...enterModalProps} />
          <HandQueryModal {...handQueryProps} />
        </Content>
      </Fragment>
    );
  }
}
