import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'hzero-front/lib/utils/notification';

const modelPrompt = 'tarzan.hmes.purchase.order';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: 'single',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'poHeaderId',
  autoLocateFirst: true,
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-po-header/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.LIST`,
        method: 'GET',
        data: {
          ...data,
          revisionCode:
            (data.revisionCode && data.revisionCode.length) > 0
              ? data.revisionCode.join(',')
              : undefined,
        },
      };
    },
  },
  queryFields: [
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单'),
    },
    {
      name: 'supplier',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierId',
      type: FieldType.string,
      bind: 'supplier.supplierId',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      textField: 'materialCode',
      valueField: 'materialId',
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
      multiple: true,
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'locator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.receiveLocator`).d('接收库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'receiveLocatorId',
      type: FieldType.string,
      bind: 'locator.locatorId',
    },
    {
      name: 'buyerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.buyerCode`).d('采购员'),
    },
    {
      name: 'poCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poCategory`).d('采购订单类别'),
      lovPara: { tenantId },
      lookupCode: 'MT.PO_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
      noCache: true,
    },
    {
      name: 'poOrderType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poOrderType`).d('采购订单类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=PO_ORDER_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'supplierSite',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierSite`).d('供应商地点'),
      lovCode: 'MT.MODEL.SUPPLIER_SITE',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('supplierId');
        },
        lovPara({ record }) {
          return {
            tenantId,
            supplierId: record.get('supplierId'),
          };
        },
      },
    },
    {
      name: 'supplierSiteId',
      type: FieldType.string,
      bind: 'supplierSite.supplierSiteId',
    },
    {
      name: 'transferSite',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transferSite`).d('转储站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'transferSiteId',
      type: FieldType.string,
      bind: 'transferSite.siteId',
    },
    {
      name: 'materialCategory',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCategory`).d('物料类别'),
      lovCode: 'MT.METHOD.MATERIAL_CATEGORY',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialCategoryId',
      type: FieldType.string,
      bind: 'materialCategory.materialCategoryId',
    },
    {
      name: 'poCreateDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.poCreateDateFrom`).d('创建时间从'),
      max: 'poCreateDateTo',
    },
    {
      name: 'poCreateDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.poCreateDateTo`).d('创建时间至'),
      min: 'poCreateDateFrom',
    },
    {
      name: 'receivingAddress',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receivingAddress`).d('收货地址'),
    },
    {
      name: 'demandDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandDateFrom`).d('需要日期从'),
      max: 'demandDateTo',
    },
    {
      name: 'demandDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandDateTo`).d('需要日期至'),
      min: 'demandDateFrom',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('备注'),
    },
    {
      name: 'approvedFlag',
      type: FieldType.boolean,
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      label: intl.get(`${modelPrompt}.approvedFlag`).d('审批标识'),
    },
    {
      name: 'deleteFlag',
      type: FieldType.boolean,
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      label: intl.get(`${modelPrompt}.deleteHeaderFlag`).d('头删除标识'),
    },
    {
      name: 'closedFlag',
      type: FieldType.boolean,
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      label: intl.get(`${modelPrompt}.closedFlag`).d('关闭标识'),
    },
    // new
    {
      name: 'returnFlag',
      type: FieldType.boolean,
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      label: intl.get(`${modelPrompt}.returnFlag`).d('退货标识'),
    },
  ],
  fields: [
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商名称'),
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteCode`).d('供应商地点名称'),
    },
    {
      name: 'buyerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.buyerCode`).d('采购员'),
    },
    {
      name: 'approvedFlag',
      type: FieldType.boolean,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.approvedFlag`).d('审批标识'),
    },
    {
      name: 'deleteFlag',
      type: FieldType.boolean,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.deleteFlag`).d('删除标识'),
    },
    {
      name: 'returnFlag',
      type: FieldType.boolean,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.returnFlag`).d('退货行标识'),
    },
    {
      name: 'closedFlag',
      type: FieldType.boolean,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.closedFlag`).d('关闭标识'),
    },
    {
      name: 'poCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poCategory`).d('采购订单类别'),
      lookupCode: 'MT.PO_CATEGORY',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      noCache: true,
    },
    {
      name: 'poOrderType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poOrderType`).d('采购订单类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=PO_ORDER_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'currencyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currencyCode`).d('币种'),
    },
    {
      name: 'transferSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferSite`).d('转储站点'),
    },
    {
      name: 'receivingAddress',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receivingAddress`).d('收货地址'),
    },
    {
      name: 'poCreateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poCreateDate`).d('创建时间'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('备注'),
    },
  ],
});

const lineTableDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    selection: 'multiple',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    cacheSelection: true,
    primaryKey: 'poLineId',
    transport: {
      read: event => {
        const data = {};
        const _data = event?.dataSet?.queryDataSet?.toData()[0];
        if (_data?.site?.siteId) {
          data.siteId = _data.site.siteId;
        }
        return {
          // 采购订单列表 接口待替换
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-po-line/limit-head/line/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.LINE`,
          method: 'GET',
          params: {
            ...event.params,
            ...data,
            operationType: 'PO_DOC',
          },
          transformResponse: val => {
            const datas = JSON.parse(val);
            if (datas && !datas.success) {
              if (datas.message) {
                notification.error({ message: datas.message });
              }
              return {
                rows: [],
              };
            }
            return {
              ...datas,
            };
          },
        };
      },
    },
    fields: [
      {
        name: 'lineNum',
        type: FieldType.string,
      },
      {
        name: 'identifyType',
        lookupCode: 'MT.APS.GEN_TYPE_URL',
        lovPara: {
          typeGroup: 'IDENTITY_TYPE',
          tenantId: getCurrentOrganizationId(),
          userId: getCurrentUserId(),
        },
        valueField: 'typecode',
        textField: 'description',
        type: FieldType.string,
      },
      {
        name: 'lineType',
        type: FieldType.string,
        textField: 'description',
        valueField: 'typeCode',
        noCache: true,
        lovPara: { tenantId },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=PO_LINE_TYPE`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'materialCode',
        type: FieldType.string,
      },
      {
        name: 'materialName',
        type: FieldType.string,
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
      },
      {
        name: 'materialTypeName',
        type: FieldType.string,
      },
      {
        name: 'uomCode',
        type: FieldType.string,
      },
      {
        name: 'quantityOrdered',
        type: FieldType.number,
      },
      {
        name: 'processedOrdered',
        type: FieldType.number,
      },
      {
        name: 'actualQuantity',
        type: FieldType.number,
      },
      {
        name: 'unitPrice',
        type: FieldType.string,
      },
      {
        name: 'demandDate',
        type: FieldType.string,
      },
      {
        name: 'siteCode',
        type: FieldType.string,
      },
      {
        name: 'receiveLocatorCode',
        type: FieldType.string,
      },
      {
        name: 'quantityReceived',
        type: FieldType.string,
      },
      {
        name: 'completeFlag',
        type: FieldType.boolean,
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'returnFlag',
        type: FieldType.boolean,
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'deleteFlag',
        type: FieldType.boolean,
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'consignedFlag',
        type: FieldType.boolean,
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'releaseNum',
        type: FieldType.string,
      },
      {
        name: 'shipmentNum',
        type: FieldType.string,
      },
      {
        name: 'receivingType',
        type: FieldType.string,
        lovPara: { tenantId },
        lookupCode: 'MT.PO_RECEIVING_TYPE',
      },
      {
        name: 'expirationDate',
        type: FieldType.string,
      },
      {
        name: 'poNumber',
        type: FieldType.string,
      },
      {
        name: 'soNum',
        type: FieldType.string,
      },
      {
        name: 'soLineNum',
        type: FieldType.string,
      },
      {
        name: 'lineDescription',
        type: FieldType.string,
      },
      {
        name: 'quantityDelivered',
        type: FieldType.string,
      },
      {
        name: 'option',
        type: FieldType.string,
      },
    ],
  };
};

export { headerTableDS, lineTableDS };
