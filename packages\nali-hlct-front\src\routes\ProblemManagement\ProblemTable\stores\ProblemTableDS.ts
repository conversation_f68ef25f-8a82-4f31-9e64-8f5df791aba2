import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'problem.table';
const tenantId = getCurrentOrganizationId();

const OverviewAndTodoListDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'createdCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdCount`).d('已创建数量'),
    },
    {
      name: 'overdueCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overdueCount`).d('已逾期数量'),
    },
    {
      name: 'processingCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processingCount`).d('处理中数量'),
    },
    {
      name: 'closedCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.closedCount`).d('已关闭数量'),
    },
    {
      name: 'frozenCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.frozenCount`).d('已冻结数量'),
    },
    {
      name: 'monthScore',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.monthScore`).d('月度得分'),
    },
    {
      name: 'monthRank',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.monthRank`).d('月度排名'),
    },
    {
      name: 'quarterScore',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quarterScore`).d('季度得分'),
    },
    {
      name: 'quarterRank',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quarterRank`).d('季度排名'),
    },
    {
      name: 'yearScore',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.yearScore`).d('年度得分'),
    },
    {
      name: 'yearRank',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.yearRank`).d('年度排名'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/all/page-ui`,
        method: 'GET',
      };
    },
  },
});

const PersonalTodoDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content',
  fields: [
    {
      name: 'problemCode',
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
      type: FieldType.string,
    },
    {
      name: 'problemTitle',
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
      type: FieldType.string,
    },
    {
      name: 'problemStatusDesc',
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      type: FieldType.string,
    },
    {
      name: 'problemStatus',
      type: FieldType.string,
    },
    {
      name: 'role',
      label: intl.get(`${modelPrompt}.role`).d('担任角色'),
      type: FieldType.string,
    },
    {
      name: 'toDoList',
      label: intl.get(`${modelPrompt}.toDoList`).d('待办事项'),
      type: FieldType.string,
    },
  ],
});

const UnderwayDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content',
  fields: [
    {
      name: 'problemCode',
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
      type: FieldType.string,
    },
    {
      name: 'problemTitle',
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
      type: FieldType.string,
    },
    {
      name: 'problemStatusDesc',
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      type: FieldType.string,
    },
    {
      name: 'problemStatus',
      type: FieldType.string,
    },
    {
      name: 'proposePersonName',
      label: intl.get(`${modelPrompt}.proposePersonName`).d('提出人'),
      type: FieldType.string,
    },
    {
      name: 'proposeDepartment',
      label: intl.get(`${modelPrompt}.proposeDepartment`).d('提出部门'),
      type: FieldType.string,
    },
    {
      name: 'registerTime',
      label: intl.get(`${modelPrompt}.registerTime`).d('登记时间'),
      type: FieldType.dateTime,
    },
    {
      name: 'leadPersonName',
      label: intl.get(`${modelPrompt}.leadPersonName`).d('跟进人'),
      type: FieldType.string,
    },
    {
      name: 'responsiblePersonName',
      label: intl.get(`${modelPrompt}.responsiblePersonName`).d('主责人'),
      type: FieldType.string,
    },
    {
      name: 'responsibleDepartment',
      label: intl.get(`${modelPrompt}.responsibleDepartment`).d('主责部门'),
      type: FieldType.string,
    },
    {
      name: 'problemCategory',
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      type: FieldType.string,
    },
    {
      name: 'severityLevel',
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      type: FieldType.string,
    },
    {
      name: 'riskLight',
      label: intl.get(`${modelPrompt}.riskLight`).d('风险灯'),
      type: FieldType.string,
    },
    {
      name: 'schedule',
      label: intl.get(`${modelPrompt}.schedule`).d('进度'),
      type: FieldType.string,
    },
    {
      name: 'resultVerification',
      type: FieldType.string,
    },
    {
      name: 'causeAnalysis',
      type: FieldType.string,
    },
    {
      name: 'measureEnact',
      type: FieldType.string,
    },
    {
      name: 'verifyClosed',
      type: FieldType.string,
    },
  ],
})

const ClosedIssueDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'problemId',
    },
    {
      name: 'problemCode',
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
      type: FieldType.string,
    },
    {
      name: 'problemTitle',
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
      type: FieldType.string,
    },
    {
      name: 'proposePersonName',
      label: intl.get(`${modelPrompt}.proposePerson`).d('提出人'),
      type: FieldType.string,
    },
    {
      name: 'proposeDepartment',
      label: intl.get(`${modelPrompt}.proposeDepartment`).d('提出部门'),
      type: FieldType.string,
    },
    {
      name: 'registerTime',
      label: intl.get(`${modelPrompt}.registerTime`).d('登记时间'),
      type: FieldType.dateTime,
    },
    {
      name: 'leadPersonName',
      label: intl.get(`${modelPrompt}.leadPersonName`).d('跟进人'),
      type: FieldType.string,
    },
    {
      name: 'responsiblePersonName',
      label: intl.get(`${modelPrompt}.responsiblePersonName`).d('主责人'),
      type: FieldType.string,
    },
    {
      name: 'responsibleDepartment',
      label: intl.get(`${modelPrompt}.responsibleDepartment`).d('主责部门'),
      type: FieldType.string,
    },
    {
      name: 'problemCategory',
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
    },
    {
      name: 'severityLevel',
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      type: FieldType.string,
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
    },
    {
      name: 'score',
      label: intl.get(`${modelPrompt}.score`).d('个人得分'),
      type: FieldType.string,
    },
  ],
});

export { OverviewAndTodoListDS, PersonalTodoDS, UnderwayDS, ClosedIssueDS };