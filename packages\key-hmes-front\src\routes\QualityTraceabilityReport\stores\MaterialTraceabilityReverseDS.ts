import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { Record } from 'choerodon-ui/dataset';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

function filterDisabled(type: 'mod1' | 'mod2', record: Record) {
  if (type === 'mod2') {
    // 模式二根据模式一的数据是否有值进行判断是否禁用
    if (
      record.get('bindDateFrom') ||
      record.get('bindDateTo') ||
      record.get('workcellId') ||
      record.get('parentMaterialId') ||
      record.get('childMaterialId')
    ) {
      return true;
    }
    return false;
  }
  // 模式一
  if (record.get('materialLotCode') || record.get('lot')) {
    return true;
  }
  return false;
}

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows', // 列表数据在接口返回json中的相对路径
  primaryKey: 'materialLotId', // 表格唯一性主键
  parentField: 'parentEoId',
  childrenField: 'childMaterialDtlList',
  paging: false,
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'bindDateFrom',
      type: FieldType.dateTime,
      max: 'bindDateTo',
      label: intl.get(`${modelPrompt}.bindDateFrom`).d('绑定时间从'),
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod1', record);
        },
        required: ({ record }) => {
          return !filterDisabled('mod1', record);
        },
      },
    },
    {
      name: 'bindDateTo',
      type: FieldType.dateTime,
      min: 'bindDateFrom',
      label: intl.get(`${modelPrompt}.bindDateTo`).d('绑定时间至'),
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod1', record);
        },
        required: ({ record }) => {
          return !filterDisabled('mod1', record);
        },
      },
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: {
        tenantId,
        workcellType: 'STATION',
      },
      ignore: FieldIgnore.always,
      multiple: true,
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod1', record);
        },
      },
    },
    {
      name: 'workcellIds',
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'parentMaterialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellCode`).d('父物料编码'),
      lovCode: 'MT.MATERIAL.PERMISSION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod1', record);
        },
      },
    },
    {
      name: 'parentMaterialId',
      type: FieldType.number,
      bind: 'parentMaterialLov.materialId',
    },
    {
      name: 'childMaterialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellCode`).d('子物料编码'),
      lovCode: 'MT.MATERIAL.PERMISSION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod1', record);
        },
      },
    },
    {
      name: 'childMaterialId',
      type: FieldType.number,
      bind: 'childMaterialLov.materialId',
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('子物料批'),
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod2', record);
        },
      },
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次号'),
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod2', record);
        },
      },
    },
  ],
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'componentTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentTypeDesc`).d('组件类型描述'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次号'),
    },
    {
      name: 'sumAssembleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumAssembleQty`).d('汇总数量'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
    {
      name: 'bindDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.bindDate`).d('绑定时间'),
    },
    {
      name: 'overStationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.overStationDate`).d('过站时间'),
    },
    {
      name: 'overStationByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overStationByName`).d('过站操作员'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const newData = {
        ...data,
        workcellIds: (data.workcellIds || []).join(','),
      };
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-trace-report/material/reverse/ui`,
        method: 'GET',
        data: newData,
      };
    },
  },
});

export { tableDS };
