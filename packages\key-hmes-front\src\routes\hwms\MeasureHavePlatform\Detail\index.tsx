/**
 * @Description: 量具检定平台-详情界面
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  TextField,
  Select,
  DateTimePicker,
  Table, Modal, TextArea,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';

import { useDataSetEvent } from 'utils/hooks';
import { Header, Content } from 'components/Page';
import {ButtonColor, FuncType} from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import {observer} from "mobx-react";
import myInstance from "@utils/myAxios";
import {BASIC} from "@utils/config";
import {getCurrentOrganizationId, getCurrentUser} from 'utils/utils';

import { FieldType } from "choerodon-ui/pro/lib/data-set/enum";
import { SaveVerification, GetDefaultSite } from '../services';
import {
  detailDS,
  taskDS,
  rejectReasonDS,
} from '../stories/DetailDS';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const { Panel } = Collapse;
const modelPrompt = 'tarzan.inspectExecute.MeasureHavePlatform';

const TaskDetail = props => {
  const {
    history,
    match: { params },
  } = props;
  const applicationDocId = params.id;

  const [canEdit, setCanEdit] = useState(false);
  const [docType, setDocType] = useState('');
  const [buttonStatus, setButtonStatus] = useState(false);
  const [status, setStatus] = useState('NEW');
  const [createdBy, setCreatedBy] = useState('')
  const taskDs = useMemo(() => new DataSet(taskDS()), []);
  const rejectReasonDs = useMemo(() => new DataSet(rejectReasonDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          lineInfo: taskDs,
        },
      }),
    [],
  );
  const [currentSiteId, setCurrentSiteId] = useState('');

  const createButtonDs = useMemo(() => {
    return new DataSet({
      selection: false,
      autoCreate: false,
      paging: false,
      fields: [
        {
          name: 'toolLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.model`).d('量具编号'),
          lovCode: 'YP.QIS.MS_TOOL_DETAIL',
          noCache: true,
          lovPara: {
            tenantId,
            siteId: currentSiteId,
            docType,
          },
          multiple: true,
          required: true,
        },
      ],
    });
  }, [currentSiteId, userInfo, docType]);
  const { run: saveVerification, loading: saveLoading } = useRequest(SaveVerification(), {
    manual: true,
  });
  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });

  useDataSetEvent(detailDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialLov':
        record.set('productionVersionLov', {});
        record.set('defaultBomLov', {});
        record.set('defaultRouterLov', {});
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    if (applicationDocId === 'create') {
      // 新建时
      setCanEdit(true);
      getDefaultSite({
        onSuccess: res => {
          if (res?.siteId) {
            detailDs.current?.set('siteLov', res);
            setCurrentSiteId(res?.siteId)
          }
        },
      });
      return;
    }
    // 编辑时
    handleQueryDetail(applicationDocId);
  }, [applicationDocId]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('applicationDocId', id);
    detailDs.query().then(res => {
      const { rows } = res;
      setStatus(rows?.applicationDocStatus);
      setDocType(rows?.docType);
      setCurrentSiteId(rows?.siteId)
      setCreatedBy(rows?.createdBy)
    });
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
    setButtonStatus(true);
  }, []);

  const handleCancel = useCallback(() => {
    if (applicationDocId === 'create') {
      history.push('/hmes/measure-have/platform/list');
    } else {
      setCanEdit(false);
      setButtonStatus(false);
      handleQueryDetail(applicationDocId);
    }
  }, []);

  // 取消申请
  const cancelApply = () => {
    myInstance
      .post(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/application/cancel/ui`,
        [applicationDocId],
      )
      .then(res => {
        if (res.data.success) {
          notification.success({});
          setCanEdit(false);
          setButtonStatus(false);
          handleQueryDetail(applicationDocId)
        } else {
          notification.error({
            message: res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
          });
        }
      });
  };

  const handelReject = () => {
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.approveReject`).d('审批驳回'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: rejectReasonOk,
      children: (
        <Form dataSet={rejectReasonDs} columns={1}>
          <TextArea name="rejectReason" colSpan={3} />
        </Form>
      ),
    });
  };
  const rejectReasonOk = () => {
    myInstance
      .post(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/application/approve-rejected/ui`,
        {
          "applicationDocId": applicationDocId,
          // @ts-ignore
          "rejectReason": rejectReasonDs.toData()[0].rejectReason,
        },
      )
      .then(res => {
        if (res.data.success) {
          notification.success({});
          setCanEdit(false);
          setButtonStatus(false);
          rejectReasonDs.reset();
          handleQueryDetail(applicationDocId)
        } else {
          notification.error({
            message: res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
          });
        }
      });
  }

  const taskColumns: any = useMemo(
    () => [
      {
        name: 'toolLov',
        width: 130,
        editor: canEdit,
      },
      { name: 'speciesName' },
      { name: 'modelCode' },
      { name: 'modelName' },
      { name: 'lastVerificationDate' },
      { name: 'verificationPeriod' },
      { name: 'usingStatus' },
      { name: 'verificationStatus' },
      { name: 'verificationMethod' },
      { name: 'prodLineName' },
      { name: 'processName' },
      { name: 'otherPosition' },
      { name: 'manufacturingNum' },
      { name: 'enclosure', width: 150, editor: canEdit },
    ],
    [canEdit],
  );

  // @ts-ignore
  const handleSave = async ({ submitFlag }) => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const taskDsData = taskDs.toData()
    if (taskDsData.length < 1 ) {
      notification.error({
        message: intl.get(`${modelPrompt}.modelMessage.null`).d('请维护量具清单'),
      });
      return false;
    }
    // @ts-ignore
    const msToolManageIds = [...new Set(taskDsData.map(item => item.msToolManageId))]
    // @ts-ignore
    if (taskDsData.length !== msToolManageIds.length) {
      notification.error({
        message: intl.get(`${modelPrompt}.error.modelDataRepeat`).d('量具清单不能重复维护'),
      });
      return false
    }
    if (submitFlag === 'Y' && taskDsData.find((item: any) => item.usingStatus === 'USING' && item.verificationStatus === 'OK')) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.submit`)
              .d('存在正常使用且检定合格的量具，是否确认提交?')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          handleSaveCallback({ submitFlag });
        }
      });
    } else {
      handleSaveCallback({ submitFlag });
    }
  };

  const handleSaveCallback = ({ submitFlag }) => {
    const lineData = taskDs.map((_record) => {
      if (_record.status !== 'sync') {
        return {
          ..._record.toData(),
          changeFlag: 'Y',
        };
      }
      return {
        ..._record.toData(),
      };
    })
    saveVerification({
      params: {
        ...detailDs.toData()[0],
        lineInfo: lineData,
      },
      queryParams: { submitFlag },
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        setButtonStatus(false);
        if (applicationDocId === 'create') {
          history.push(`/hmes/measure-have/platform/dist/${res}`);
        } else {
          handleQueryDetail(res);
        }
      },
    });
  };

  // 审批通过
  const handleApproval = () => {
    myInstance
      .post(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/application/approve-pass/ui?applicationDocId=${applicationDocId}`)
      .then(res => {
        if (res.data.success) {
          notification.success({});
          setCanEdit(false);
          setButtonStatus(false);
          handleQueryDetail(applicationDocId)
        } else {
          notification.error({
            message: res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
          });
        }
      });
  };

  const RenderDeleteButton = observer(({ dataSet, canEdit }) => {
    return (
      <>
        <Button
          icon="delete"
          funcType={FuncType.flat}
          color={ButtonColor.red}
          disabled={!canEdit || !dataSet.selected.length || !['NEW', 'REJECTED'].includes(status)}
          onClick={() => dataSet.remove(dataSet.selected)}
        >
          {intl.get(`${modelPrompt}.button.delete`).d('删除')}
        </Button>
      </>
    );
  });

  const handleChangeLov = (value) => {
    (value || []).forEach(item => {
      taskDs.create({
        ...item,
        enclosure: undefined,
      })
    })
    createButtonDs.current?.set('toolLov', undefined)
  };

  const handleChangeDocType = (value, oldVal) => {
    if (oldVal && taskDs?.length) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.clearLineData`)
              .d('变更单据类型会清空量具清单，是否确认？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          setDocType(value);
          taskDs.loadData([]);
        } else {
          detailDs.current?.set('docType', oldVal);
        }
      });
    } else {
      setDocType(value);
    }
  }

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || siteLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.applyDist`).d('量具检定平台/申请单详情')}
          backPath="/hmes/measure-have/platform/list/1"
        >
          {
            ['TO_APPROVAL'].includes(status) &&
            <>
              <PermissionButton
                disabled={!['TO_APPROVAL'].includes(status)}
                type="c7n-pro"
                permissionList={[
                  {
                    code: `MeasureHavePlatform.button.applyPass`,
                    type: 'button',
                    meaning: '申请单详情页-审核通过',
                  },
                ]}
                color={ButtonColor.green}
                onClick={handleApproval}
              >
                {intl.get(`${modelPrompt}.button.approvePass`).d('审核通过')}
              </PermissionButton>
              <PermissionButton
                disabled={!['TO_APPROVAL'].includes(status)}
                type="c7n-pro"
                permissionList={[
                  {
                    code: `MeasureHavePlatform.button.applyReject`,
                    type: 'button',
                    meaning: '申请单详情页-审核驳回',
                  },
                ]}
                color={ButtonColor.red}
                onClick={handelReject}
              >
                {intl.get(`${modelPrompt}.button.approveReject`).d('审核驳回')}
              </PermissionButton>
            </>
          }
          {buttonStatus ? (
            <>
              <Button
                disabled={!['NEW', 'REJECTED'].includes(status)}
                color={ButtonColor.primary}
                icon="save"
                onClick={() => handleSave({ submitFlag: 'N' })}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button
                onClick={handleCancel}
              >
                {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
              </Button>
            </>
          ) : (
            canEdit ? (
              <>
                <Button
                  color={ButtonColor.primary}
                  icon="save"
                  onClick={() => handleSave({ submitFlag: 'N' })}
                >
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
                <Button icon="close" onClick={handleCancel}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
              </>
            ) : (
              <Button
                icon="edit-o"
                // @ts-ignore
                disabled={!['NEW', 'REJECTED'].includes(status) || Number(userInfo.id) !== createdBy}
                color={ButtonColor.primary}
                onClick={handleEdit}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
            )
          )}
          {
            applicationDocId !== 'create' && <Button
              // @ts-ignore
              disabled={!['NEW', 'REJECTED'].includes(status) || Number(userInfo.id) !== createdBy}
              color={ButtonColor.primary}
              icon="save"
              onClick={() => handleSave({ submitFlag: 'Y' })}
            >
              {intl.get(`${modelPrompt}.button.submit`).d('提交')}
            </Button>
          }
          <Button
          // @ts-ignore
            disabled={!['NEW', 'REJECTED'].includes(status) || Number(userInfo.id) !== createdBy}
            icon="close"
            onClick={cancelApply}
          >
            {intl.get(`${modelPrompt}.button.cancelApply`).d('取消申请')}
          </Button>
        </Header>
        <Content>
          <Form dataSet={detailDs} columns={4} disabled={!canEdit} labelWidth={112}>
            <TextField name="applicationDocNum" />
            <Select name="applicationDocStatus" />
            <Lov name="siteLov" />
            <Select name="docType" onChange={handleChangeDocType} />
            <DateTimePicker name="creationDate" />
            <TextField name="createdByName" />
            <TextField name="departmentName" />
            <TextField name="remark" />
            {
              !['NEW'].includes(status) &&
              <>
                <TextField name="reviewByName" />
                <DateTimePicker name="reviewDate" />
                <TextField name="rejectReason" />
              </>
            }
          </Form>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'taskInfo',
            ]}
          >
            <Panel key="taskInfo" header={intl.get(`${modelPrompt}.title.modelInfo`).d('量具清单')}>
              <Table
                buttons={[
                  <>
                    <RenderDeleteButton dataSet={taskDs} canEdit={canEdit} />
                    <Lov
                      dataSet={createButtonDs}
                      name="toolLov"
                      disabled={!canEdit || !docType}
                      // @ts-ignore
                      mode="button"
                      placeholder={intl.get(`${modelPrompt}.placeholder.add`).d('新增')}
                      clearButton={false}
                      onChange={handleChangeLov}
                    />
                  </>,
                ]}
                dataSet={taskDs}
                columns={taskColumns}
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(TaskDetail);
