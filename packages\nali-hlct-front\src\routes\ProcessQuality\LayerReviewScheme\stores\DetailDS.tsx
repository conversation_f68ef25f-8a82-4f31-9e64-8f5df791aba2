/**
 * @Description: 分层审核方案-详情界面DS
 * @Author: <EMAIL>
 * @Date: 2023/8/17 14:24
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.layerReview.layerReviewScheme';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  autoCreate: true,
  selection: false,
  primaryKey: 'layerReviewSchemeId',
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'layerReviewSchemeId',
      type: FieldType.number,
    },
    {
      name: 'layerReviewSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewSchemeCode`).d('分层审核方案编码'),
      disabled: true,
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      lovCode: 'YP_QIS.OPERATION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        disabled: ({ dataSet }) => Boolean(dataSet.children?.itemInfo?.length),
      }
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      bind: 'operationLov.operationName',
    },
    {
      name: 'layerReviewSchemeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewSchemeStatus`).d('状态'),
      lookupCode: 'YP.QIS.LAYER_REVIEW_SCHEME_STATUS',
      lovPara: { tenantId },
      defaultValue: 'NEW',
      disabled: true,
    },
    {
      name: 'layerReviewSchemeVersion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewSchemeVersion`).d('版本'),
      disabled: true,
      defaultValue: 'V1',
    },
    {
      name: 'currentFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      disabled: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-layer-review-scheme/info/ui`,
        method: 'GET',
      };
    },
  },
});

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'layerReviewItemId',
  fields: [
    {
      name: 'layerReviewItemId',
      type: FieldType.number,
    },
    {
      name: 'layerReviewItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewItemCode`).d('项目编码'),
      disabled: true,
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目内容'),
      required: true,
    },
    {
      name: 'projectClassify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectClassify`).d('项目分类'),
      lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM.PROJECT_FROM',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'projectFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectFrom`).d('标准来源'),
      required: true,
    },
    {
      name: 'level',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.level`).d('适用层级'),
    },
    {
      name: 'levelL1Flay',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.L1`).d('L1'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'levelL2Flay',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.L2`).d('L2'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'levelL3Flay',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.L3`).d('L3'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'levelL4Flay',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.L4`).d('L4'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      label: intl.get(`${modelPrompt}.table.layerReviewItemFrequency`).d('频率'),
      name: 'layerReviewItemFrequency',
      type: FieldType.string,
      lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM_FREQUENCY',
      lovPara: { tenantId },
      required: true,
    },
    {
      label: intl.get(`${modelPrompt}.table.emergencyFlag`).d('紧急发布'),
      name: 'emergencyFlag',
      type: FieldType.string,
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
  ],
});

const itemDS: () => DataSetProps = () => ({
  autoCreate: false,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.item`).d('项目编码'),
      name: 'layerReviewItemLov',
      lovCode: 'YP.QIS.LAYER_REVIEW_ITEM',
      multiple: true,
      noCache: true,
      lovPara: { tenantId, layerReviewItemStatus: 'Y' },
      computedProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          layerReviewItemStatus: 'Y',
          operationId: dataSet.parent?.current?.get('operationId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('operationId'),
      }
    },
  ],
});

const reviewItemDS: () => DataSetProps = () => ({
  autoCreate: true,
  dataKey: 'rows',
  primaryKey: 'layerReviewItemId',
  fields: [
    {
      name: 'layerReviewItemId',
      type: FieldType.number,
    },
    {
      name: 'layerReviewItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewItemCode`).d('项目编码'),
      disabled: true,
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目内容'),
      required: true,
    },
    {
      name: 'projectClassify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectClassify`).d('项目分类'),
      lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM.PROJECT_FROM',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'projectFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectFrom`).d('标准来源'),
      required: true,
    },
    {
      name: 'levelFlays',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.level`).d('适用层级'),
      multiple: true,
      required: true,
      lookupCode: 'YP.QIS.LEVEL',
      lovPara: { tenantId },
    },
    {
      label: intl.get(`${modelPrompt}.table.layerReviewItemFrequency`).d('频率'),
      name: 'layerReviewItemFrequency',
      type: FieldType.string,
      lookupCode: 'YP_QIS.LAYER_REVIEW_ITEM_FREQUENCY',
      lovPara: { tenantId },
      required: true,
    },
  ],
});

export { detailDS, tableDS, itemDS, reviewItemDS };
