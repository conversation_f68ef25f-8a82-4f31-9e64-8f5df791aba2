/**
 * @Description: 工序作业平台
 * @Author: <<EMAIL>>
 * @Date: 2023-2-26 18:50:17
 */
import { FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

// 登录工位ds
const enterModalDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    // {
    //   name: 'workStationLov',
    //   type: FieldType.object,
    //   label: '工位编码',
    //   lovCode: 'HME.USER_WORKCELL',
    //   ignore: FieldIgnore.always,
    //   lovPara: {
    //     tenantId: getCurrentOrganizationId(),
    //   },
    // },
    {
      name: 'workStationCode',
      type: FieldType.string,
      label: '工位编码',
      // bind: 'workStationLov.workcellCode',
    },
    // {
    //   name: 'workStationName',
    //   type: FieldType.string,
    //   label: '工位',
    //   bind: 'workStationLov.workcellName',
    // },
    {
      name: 'operationName',
      type: FieldType.string,
    },
  ],
});

const workStationLovDS: () => DataSetProps = () => ({
  selection: DataSetSelection.single,
  autoQuery: false,
  autoCreate: false,
  paging: true,
  dataKey: 'content',
  queryFields: [
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: '工位编码',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: '工位名称',
    }
  ],
  fields: [
    {
      name: 'workcellName',
      type: FieldType.string,
      label: '工位名称',
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: '工位编码',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-shift-workbench/lov/workcell`,
        method: 'GET',
      };
    }
  },
});

export { enterModalDS, workStationLovDS };
