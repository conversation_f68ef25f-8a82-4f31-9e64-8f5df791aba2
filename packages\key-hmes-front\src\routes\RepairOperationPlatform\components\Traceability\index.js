/* eslint-disable no-else-return */
import React, { useState, useEffect, useMemo, useImperativeHandle, useRef } from 'react';
import {
  Form,
  TextField,
  Button,
  DataSet,
  Icon,
  Modal,
  Row,
  Col,
  Table,
  NumberField,
  Spin,
} from 'choerodon-ui/pro';

import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import './index.modules.less';
import { tableDS, addDs } from './stores/traceabilityDS';

// const modelPrompt = 'common.hmes.OperationPlatform';
const tenantId = getCurrentOrganizationId();
const Traceability = props => {
  useImperativeHandle(props.onRef, () => {
    return {
      handleQuery,
    };
  });
  const tableDs = useMemo(
    () =>
      new DataSet({
        ...tableDS(),
      }),
    [],
  );
  const addButton = useMemo(
    () =>
      new DataSet({
        ...addDs(),
      }),
    [],
  );

  const [loading, setLoading] = useState(false); // 条形图及table的loading


  const [processInfo, setProcessInfo] = useState({}); // 内容区域的高度

  const tableRef = useRef(); // table的ref

  useEffect(() => {
    tableRef.current = processInfo;
  }, [processInfo]);

  useEffect(() => {
    // handleQuery();
  }, []);

  // /**
  //  * 获取table区域的高度
  //  */
  // useEffect(() => {
  //   const offSetHeight = (tableRef || {}).current.clientHeight || 0;
  //   setHeight(offSetHeight - 72.45 || 170);
  // }, [props.newLayout]);

  /**
   * 条形图及table的查询(使用的同一个接口)
   */
  const handleQuery = (value) => {
    setProcessInfo(value);
    queryData(value);
  };

  const queryData = (value) => {
    tableDs.setQueryParameter('operationId', props.loginWkcInfo?.selectOperation?.operationId);
    tableDs.setQueryParameter('selectStepId', value?.currentRouterStepId);
    tableDs.setQueryParameter('workCellId', props.loginWkcInfo?.workStationId);
    tableDs.setQueryParameter('shiftCode', props.loginWkcInfo?.shiftCode);
    tableDs.setQueryParameter('shiftDate', props.loginWkcInfo?.shiftDate);
    tableDs.setQueryParameter('identification', value.identification);
    tableDs.query();
  }

  const handleResult = (value, record) => {
    if(value){
      record.set('assembleQty', value)
    }
  };

  const columnStyle = {
    backgroundColor: '#5a9ebd',
    // color: 'white',
  };

  const columns = [
    {
      name: 'materialCode',
      headerClassName: 'dataAcquRepair',
      onCell: columnsCell,
    },
    {
      name: 'materialName',
      headerClassName: 'dataAcquRepair',
      // onCell: ({dataSet, record, column}) => {
      //   console.log(dataSet, record, column);
      // },
    },
    {
      name: 'substituteGroup',
      headerClassName: 'dataAcquRepair',
      onCell: columnsCell,
    },
    {
      name: 'materialLotCode',
      headerClassName: 'dataAcquRepair',
      onCell: columnsCell,
    },
    {
      name: 'qty',
      headerClassName: 'dataAcquRepair',
      onCell: columnsCell,
      style: columnStyle,
      // editor: true,
    },
    {
      name: 'assembleQty',
      headerClassName: 'dataAcquRepair',
      onCell: columnsCell,
      renderer: ({ record }) => {
        return (
          <NumberField onChange={e => handleResult(e, record)} />
        );
      },
    },
    {
      name: 'lot',
      headerClassName: 'dataAcquRepair',
      onCell: columnsCell,
    },
    {
      name: 'supplierCode',
      headerClassName: 'dataAcquRepair',
      onCell: columnsCell,
    },
    {
      name: 'locatorCode',
      headerClassName: 'dataAcquRepair',
      onCell: columnsCell,
    },
  ];

  const columnsCell = ({record}) => {
    return {
      className: record.record.data.flag === 'Y' ? 'highLightRow' : '',
    };
  };

  // 扫描在制品
  const onFetchProcessed = (value, flag) => {
    if (value) {
      setLoading(true);
      const params = {
        barcode: value,
        workcellId: props.loginWkcInfo?.workStationId,
        routerStepId: tableRef.current?.currentRouterStepId,
        releaseFlag: flag,
      };
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-rework-station/input/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          if(res.barcodeList.length > 0){
            Modal.open({
              title: '是否解绑？',
              destroyOnClose: true,
              // closable: true,
              onOk: () => onFetchProcessed(res.barcode, 'Y'),
            });
          }else{
            queryData(processInfo);
          }
          setLoading(false);
        } else {
          notification.error({ message: res.message });
          setLoading(false);
        }
      });
    } else {
      setLoading(false);
    }
  };

  // 拆解
  const handleDisassembly = () => {
    if (tableDs.selected.length === 0) {
      return notification.error({ message: '请先选择数据！' });
    }
    Modal.open({
      title: '拆解',
      destroyOnClose: true,
      // closable: true,
      children: (
        <Form
          dataSet={addButton}
          labelAlign="left"
          labelWidth="auto"
          labelLayout="placeholder"
          columns={1}
        >
          <NumberField
            id="identification"
            name="number"
            onChange={value => (value ? null : onFetchProcessed(null))}
            suffix={<Icon type="scan" style={{ fontSize: 14, color: 'white' }} />}
          />
        </Form>
      ),
      onOk: () => handleConfirm('y'),
      okText: '报废',
      cancelText: '退库',
      oncancel: () => handleConfirm('y'),
    });
  };

  // 投料
  const handleFeeding = () => {
    if (tableDs.selected.length === 0) {
      return notification.error({ message: '请先选择数据！' });
    }
    const data = tableDs.selected.map((v) => v.data.assembleQty);
    if(data.includes(undefined)){
      return notification.error({ message: '请输入数量！' });
    }
  };

  const handleConfirm = () => {
    if (addButton.toData().length === 0 || !addButton.toData()[0].number) {
      notification.error({ message: '请输入数量！' });
      return false;
    }
    return new Promise(async (resolve, reject) => {
      const params = {
        hmeNcRecord: addButton.toData()[0].number,
      };
      return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-rework-station/eo-scan/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          notification.success({ message: '保存成功！' });
          props.tableDataSet.query();
          resolve();
        } else {
          notification.error({ message: '保存失败！' });
          resolve(false);
        }
      })
        .catch(() => {
          reject();
        });
    });
  };

  return (
    <div className="TraceabilityRepair">
      <div className="TraceabilityHeadRepair">
        <Row style={{ display: 'flex' }}>
          <Col span={7}>
            <span
              style={{
                fontSize: '16px',
                color: 'white',
                position: 'absolute',
                marginTop: '6px',
                marginLeft: '15px',
              }}
            >
              追溯(投/退料)
            </span>
          </Col>
          <Col span={7}>
            <Form
              // dataSet={detailDs}
              labelAlign="left"
              labelWidth="auto"
              labelLayout="placeholder"
              columns={1}
            >
              <TextField
                id="identification"
                name="identificationSerch"
                onEnterDown={e => onFetchProcessed(e.target.value)}
                clearButton
                suffix={<Icon type="scan" style={{ fontSize: 14, color: 'white' }} />}
              />
            </Form>
          </Col>
          <Col
            span={7}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Button
              style={{
                marginTop: '4px',
                marginLeft: '10px',
                background: 'green',
                border: 'green',
              }}
              color="primary"
              onClick={handleFeeding}
              disabled={loading}
            >
              投料
            </Button>
            <Button
              style={{
                marginTop: '4px',
                marginLeft: '10px',
                background: 'red',
                border: 'red',
              }}
              color="primary"
              onClick={handleDisassembly}
              disabled={loading}
            >
              拆解
            </Button>
          </Col>
        </Row>
      </div>
      <div>
        <Spin spinning={loading}>
          <Table
          // rowNumber
            style={{ backgroundColor: '#5a9ebd' }}
            className="TraceabilityLinePaire"
            // highLightRow
            // selectedHighLightRow
            dataSet={tableDs}
            columns={columns}
          />
        </Spin>

      </div>
    </div>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(Traceability);
