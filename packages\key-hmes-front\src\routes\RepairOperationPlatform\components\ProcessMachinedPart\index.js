// 加工件（在制品标识）
import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  Form,
  TextField,
  Button,
  DataSet,
  TextArea,
  Row,
  Col,
  Modal,
  Table,
  Spin,
} from 'choerodon-ui/pro';
import { Icon } from 'hzero-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import NumberCharts from './App';
import './index.modules.less';
import { detailDS, tableDs } from './stores/MachinedPartDS';

const tenantId = getCurrentOrganizationId();
let numInterval;// 加工时长定时器

const MachinedPartCard = props => {
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const tableDataSet = useMemo(
    () =>
      new DataSet({
        ...tableDs(),
      }),
    [],
  );

  const [workOrderData, setWorkOrderData] = useState({}); // 工单数据

  const [loading, setLoading] = useState(false); // 工单loading

  const divRef = useRef(); // 用来获取内容部分的宽高

  const columns = [
    {
      name: 'stepName',
    },
    {
      name: 'sequence',
    },
  ];

  // 字体大小控制
  useEffect(() => {
    clearInterval(numInterval);
    document.getElementById('processMachinedPartTitle').style.fontSize = `${10 +
      props.newLayout?.filter(item => item.i === '4')[0]?.w}px`;
  }, [props.newLayout]);

  const handleConfirm = (value) => {
    if (tableDataSet.selected.length === 0) {
      notification.error({ message: '请选择工艺' });
      return false;
    }
    onFetchProcessed(value);
    return true;
  };

  const transferInfo = value => {
    if (props.transInfomation) {
      props.transInfomation(value);
    }
  };

  // 扫描在制品
  const onFetchProcessed = value => {
    // transferInfo([{name: 2223423, tagCode: 'iudhqiuweif', tagFlag: 'Y'},{name: 2223423, tagCode: 'iudhqiuweif'},{name: 2223423, tagCode: 'iudhqiuweif'}])
    if (value) {
      setLoading(true);
      const params = {
        identification: value,
        workCellId: props.loginWkcInfo?.workStationId,
        shiftCode: props.loginWkcInfo?.shiftCode,
        shiftDate: props.loginWkcInfo?.shiftDate,
        operationId: props.loginWkcInfo?.selectOperation?.operationId,
        selectStepId: tableDataSet.selected.length > 0
          ? tableDataSet.selected[0].data.routerStepId
          : null,
        // operationName: props.loginWkcInfo?.selectOperation?.operationName,
        // operationDesc: props.loginWkcInfo?.selectOperation?.operationDesc,
        // scanFlag: 'EO',
      };
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-rework-station/eo-scan/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          setLoading(false);
          if (res.selectList.length > 0) {
            tableDataSet.loadData(res.selectList);
            Modal.open({
              title: '工艺选择',
              destroyOnClose: true,
              // closable: true,
              children: (
                <Table dataSet={tableDataSet} columns={columns} searchCode="InspectionPlatform" />
              ),
              onOk: () => handleConfirm(value),
              onCancel: () => {
                tableDataSet.unSelectAll();
              },
            });
          } else {
            if (res.duration || res.duration === 0) {
              let newTime = null;
              numInterval = setInterval(() => {
                detailDs.current.set('duration', (newTime || res.duration) + 1);
                newTime = (newTime || res.duration) + 1;
              }, 1000);
            }
            detailDs.loadData([res]);
            setWorkOrderData(res);
            transferInfo(res);
            detailDs.loadData([res]);
            setWorkOrderData(res);
          }
        } else {

          notification.error({ message: res.message });
          setLoading(false);
        }
      });
    } else {
      setLoading(false);
    }
  };

  // 加工完成
  const processCompleted = flag => {
    setLoading(true);
    const params = {
      workCellId: props.loginWkcInfo?.workStationId,
      shiftCode: props.loginWkcInfo?.shiftCode,
      shiftDate: props.loginWkcInfo?.shiftDate,
      operationId: props.loginWkcInfo?.selectOperation?.operationId,
      eoId: workOrderData?.eoId,
      // routerStepId: workOrderData?.eoId,
      identification: workOrderData?.identification,
      currentRouterStepId: workOrderData?.currentRouterStepId,
      completeFlag: flag,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-rework-station/process/complete/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        notification.success();
        setWorkOrderData({});
        detailDs.loadData([]);
        setLoading(false);
      } else {
        notification.error({ message: res.message });
        setLoading(false);
      }
    });
  };

  return (
    <div ref={divRef} className="processMachinedPartRepair">
      <div className="processMachinedPartHeadRepair">
        <Row>
          <Col span={7}>
            <span
              id="processMachinedPartTitle"
              style={{
                fontSize: '16px',
                color: 'white',
                position: 'absolute',
                marginTop: '6px',
                marginLeft: '15px',
              }}
            >
              返修件
              {/* <Tooltip title={props?.cardUsage?.meaning} theme="light">
                <Icon style={{ marginLeft: '3px' }} type="question-circle-o" />
              </Tooltip> */}
            </span>
          </Col>
          <Col span={8}>
            <Form
              dataSet={detailDs}
              labelAlign="left"
              labelWidth="auto"
              labelLayout="placeholder"
              columns={1}
            >
              <TextField
                id="identification"
                name="identificationSerch"
                disabled={loading}
                onEnterDown={e => onFetchProcessed(e.target.value)}
                onChange={value => (value ? null : onFetchProcessed(null))}
                suffix={<Icon type="scan" style={{ fontSize: 14, color: 'white' }} />}
              />
            </Form>
          </Col>
          <Col
            span={4}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Button
              style={{
                marginTop: '5px',
                marginLeft: '5px',
              }}
              color="primary"
              onClick={() => processCompleted('Y')}
              disabled={!workOrderData?.eoId || loading}
            >
              返修完成
            </Button>
            <Button
              style={{
                marginTop: '5px',
                marginLeft: '5px',
              }}
              color="primary"
              onClick={() => processCompleted('N')}
              disabled={!workOrderData?.eoId || loading}
            >
              加工完成
            </Button>
          </Col>
        </Row>
      </div>
      <div className="processMachinedPartLineRepair">
        <Spin spinning={loading}>
          <Form
            dataSet={detailDs}
            labelAlign="left"
            labelWidth="auto"
            labelLayout="horizontal"
            columns={2}
            style={{ padding: '0px 5px 0px 5px' }}
          >
            <TextField name="materaialInfo" disabled colSpan={2} />
            <TextField name="workOrderNum" disabled colSpan={1} />
            <TextField name="customerInfo" disabled colSpan={1} />
            <TextArea name="workOrderRemark" newLine disabled rowSpan={2} />
            <TextArea
              name="standardTime"
              renderer={({ value }) =>
                value && (
                  <span style={{ color: '#01e1ef', fontSize: '30px', position: 'absolute' }}>
                    {value}秒
                  </span>
                )
              }
              disabled
              rowSpan={2}
            />
            <TextField name="identification" disabled />
            <TextField name="inTime" disabled />
            <TextField name="currentOperationInfo" disabled />
            <TextField name="upOperationInfo" disabled />
            <TextArea name="remark" disabled rowSpan={2} />
            <TextArea
              name="duration"
              renderer={({ value }) =>
                value && (
                  <span style={{ color: '#01e1ef', fontSize: '30px', position: 'absolute' }}>
                    {value}秒
                  </span>
                )
              }
              disabled
              rowSpan={2}
            />
          </Form>
          <NumberCharts data={workOrderData} />
        </Spin>
      </div>
    </div>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(MachinedPartCard);
