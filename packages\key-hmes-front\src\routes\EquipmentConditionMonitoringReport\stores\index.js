import { Host } from '@/utils/config';
import moment from 'moment';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;
const modelPrompt = 'tarzan.receive.scrapBarcodeGeneration';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'equipmentStatusId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      },
      {
        name: 'equipmentStatusCodeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentStatusCodeDesc`).d('设备状态'),
      },
      {
        name: 'equipmentLocationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentLocationName`).d('设备位置'),
      },
      {
        name: 'startTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
      },
      {
        name: 'endTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
      },
      {
        name: 'continueTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.continueTime`).d('状态持续时长'),
      },
      {
        name: 'loginName',
        type: 'string',
        label: intl.get(`${modelPrompt}.loginName`).d('操作员'),
      },
    ],
    queryFields: [
      {
        name: 'equipmentLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.collectEquipmentLov`).d('设备编码'),
        lovCode: 'MT.MODEL.EQUIPMENT',
        ignore: 'always',
        required: true,
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'startTime',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.startTime`).d('开始时间从'),
        max: 'endTime',
        required: true,
        defaultValue: moment().subtract(3, 'days'),
      },
      {
        name: 'endTime',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.endTime`).d('开始时间至'),
        min: 'startTime',
        required: true,
        defaultValue: new Date(),
      },
      {
        name: 'equipmentStatusCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentStatusCode`).d('设备状态'),
        lookupCode: 'HME.EQUIPMENT_STATUS_CODE',
      },
      {
        name: 'lampFailureObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.lampFailureObj`).d('故障代码'),
        lovCode: 'HME.LAMP_FAILURE_CODE',
        lovPara: { tenantId },
        ignore: 'always',
      },
      {
        name: 'lampFailureId',
        bind: 'lampFailureObj.lampFailureId',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-equipment-status/watch/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const drawerDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'lampFailureId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'evalItemCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.evalItemCode`).d('故障代码'),
      },
      {
        name: 'evalItemDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.evalItemDesc`).d('故障描述'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-equipment-status/lamp/failure/detail/query/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const drawerCodeDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'equipmentStatusDtlId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'processBarcode',
        type: 'string',
        label: intl.get(`${modelPrompt}.processBarcode`).d('产品条码'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-equipment-status/detail/query/ui`,
          method: 'GET',
        };
      },
    },
  };
};

export { tableDS, drawerDS, drawerCodeDS };
