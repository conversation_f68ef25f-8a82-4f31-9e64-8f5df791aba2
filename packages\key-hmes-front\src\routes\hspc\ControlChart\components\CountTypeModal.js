/**
 * @Description: 计数型图形，数据点信息
 * @Author: <<EMAIL>>
 * @Date: 2021-06-02 10:52:33
 * @LastEditTime: 2022-06-14 16:44:19
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { Row, Col, Modal, Button } from 'hzero-ui';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import ModalTable from './ModalTable';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hspc.chartInfo';
let hideButtonFlag = false

const CountTypeModal = props => {
  const {
    onCancel,
    onHidden,
    countModalVisible,
    sampleCount,
    dataColumnBatch,
    unqualifiedQuantity,
    unqualifiedPercent,
    mainChartOoc,
    // hideButtonFlag,
    clickIndex,
    chartData,
    dataPositionIndex,
  } = props;

  if (clickIndex === 0) {
    if (
      chartData.data[dataPositionIndex].mainChartOoc &&
      chartData.data[dataPositionIndex].mainChartOoc.length > 0
    ) {
      hideButtonFlag = true;
    } else {
      hideButtonFlag = false;
    }
  }
  if (clickIndex === 1) {
    if (
      chartData.data[dataPositionIndex].secondaryChartOoc &&
      chartData.data[dataPositionIndex].secondaryChartOoc.length > 0
    ) {
      hideButtonFlag = true;
    } else {
      hideButtonFlag = false;
    }
  }

  return (
    <Modal
      destroyOnClose
      title={intl.get(`${modelPrompt}.dataPointDetails`).d('数据点详细信息')}
      width={660}
      visible={countModalVisible} // 模态对话框是否可见
      onCancel={onCancel}
      onOk={onCancel}
      // okText={intl.get('tarzan.common.button.confirm').d('确定')}
      // cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
      footer={
        hideButtonFlag
          ? [
            <Button key="submit" type="primary" onClick={onHidden}>
                隐藏
            </Button>,
            <Button key="back" onClick={onCancel}>
                取消
            </Button>,
            <Button key="submit" type="primary" onClick={onCancel}>
                确定
            </Button>,
          ]
          : [
            <Button key="back" onClick={onCancel}>
                取消
            </Button>,
            <Button key="submit" type="primary" onClick={onCancel}>
                确定
            </Button>,
          ]
      }
    >
      <Row gutter={16} style={{ marginTop: 20 }}>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.sampleData`).d('样本数据')}:
            </Col>
            <Col span={12} offset={2}>
              {sampleCount}
            </Col>
          </Row>
        </Col>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.sampleAttr`).d('样本属性')}:
            </Col>
            <Col span={12} offset={2}>
              {dataColumnBatch}
            </Col>
          </Row>
        </Col>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.unqualifiedQuantity`).d('不合格品数')}:
            </Col>
            <Col span={12} offset={2}>
              {unqualifiedQuantity}
            </Col>
          </Row>
        </Col>
        <Col className="gutter-row" span={12} style={{ marginBottom: 16 }}>
          <Row>
            <Col span={10} style={{ textAlign: 'right' }}>
              {intl.get(`${modelPrompt}.p/u`).d('不合格率/单位缺陷数')}:
            </Col>
            <Col span={12} offset={2}>
              {unqualifiedPercent}
            </Col>
          </Row>
        </Col>
      </Row>
      <Collapse bordered={false} defaultActiveKey={['outOfControlInformation']}>
        <Panel
          header={intl.get(`${modelPrompt}.outOfControlInformation`).d('失控信息')}
          key="outOfControlInformation"
        >
          <ModalTable dataSource={mainChartOoc} />
        </Panel>
      </Collapse>
    </Modal>
  );
};

export default CountTypeModal;
