/**
 * @Description: 分层审核计划-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-08-16 11:31:24
 * @LastEditTime: 2023-08-16 11:31:24
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import moment from 'moment/moment';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hierarchicalAuditPlan';
const tenantId = getCurrentOrganizationId();

// 列表-ds
const listTableDS = (): DataSetProps => ({
  autoLocateFirst: true,
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: false,
  cacheSelection: true,
  primaryKey: 'sysReviewPlanId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: ({ data }) => {
      const _data = { ...data };
      if (data.planYear) {
        _data.planYear = moment(data.planYear).format('YYYY');
      }
      if (data.planMonth) {
        _data.planMonth = moment(data.planMonth).format('MM');
      }
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}//qis-layer-review-plan/ui`,
        method: 'get',
        data: _data,
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewPlanCode`).d('分层审核计划编码'),
      name: 'layerReviewPlanCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
      ignore: FieldIgnore.always,
    },
    {
      type: FieldType.year,
      label: intl.get(`${modelPrompt}.planYear`).d('年份'),
      name: 'planYear',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planMonth`).d('月份'),
      name: 'planMonth',
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.LAYER_REVIEW_PLAN_STATUS',
      name: 'layerReviewPlanStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
  ],
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerReviewPlanCode`).d('分层审核计划编码'),
      name: 'layerReviewPlanCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteLov',
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      type: FieldType.year,
      label: intl.get(`${modelPrompt}.planYear`).d('年份'),
      name: 'planYear',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planMonth`).d('月份'),
      name: 'planMonth',
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.LAYER_REVIEW_PLAN_STATUS',
      name: 'layerReviewPlanStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
  ],
});

export { listTableDS };
