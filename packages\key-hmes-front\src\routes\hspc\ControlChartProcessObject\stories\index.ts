/**
 * @Description: 控制图过程对象维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 15:24:51
 * @LastEditTime: 2022-08-17 11:05:10
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { jsonString2object } from '../utils';

const modelPrompt = 'tarzan.hspc.controlChartProcessObject';
const tenantId = getCurrentOrganizationId();

const controlChartTableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'controlId',
  queryFields: [
    {
      name: 'controlCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlCode`).d('控制控制图编码'),
    },
    {
      name: 'controlDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlDesc`).d('控制控制图描述'),
    },
    {
      name: 'processObjectList',
      type: FieldType.object,
      lovCode: 'MT.SPC.CONTROL_PROCESS_OBJECT',
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.processObjectLov`).d('过程对象'),
      multiple: true,
      textField: 'objectTypeDescAndCode',
    },
  ],
  fields: [
    {
      name: 'controlCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlCode`).d('控制控制图编码'),
    },
    {
      name: 'controlDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlDesc`).d('控制控制图描述'),
    },
    {
      name: 'processObjectDTOList',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processObjectDTOList`).d('关联过程对象'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control-process-objects/list/ui`,
        method: 'POST',
      };
    },
  },
});

const analysisControlChartTableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'analysisId',
  queryFields: [
    {
      name: 'analysisCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisCode`).d('分析控制图编码'),
    },
    {
      name: 'analysisDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisDesc`).d('分析控制图描述'),
    },
    {
      name: 'processObjectList',
      type: FieldType.object,
      lovCode: 'MT.SPC.ANALYSIS_PROCESS_OBJECT',
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.processObjectLov`).d('过程对象'),
      multiple: true,
      textField: 'objectTypeDescAndCode',
    },
  ],
  fields: [
    {
      name: 'analysisCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisCode`).d('分析控制图编码'),
    },
    {
      name: 'analysisDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisDesc`).d('分析控制图描述'),
    },
    {
      name: 'processObjectDTOList',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processObjectDTOList`).d('关联过程对象'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis-process-objects/list/ui`,
        method: 'POST',
      };
    },
  },
});

const chartProcessObjectDS: () => DataSetProps = () => ({
  autoQuery: false,
  forceValidate: true,
  fields: [
    {
      name: 'controlChartLov',
      type: FieldType.object,
      lovCode: 'MT.SPC.CONTROL_CHART',
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.controlChartLov`).d('选择控制控制图'),
      computedProps: {
        required({ record }) {
          return !record.get('analysisId');
        },
      },
    },
    {
      name: 'controlId',
      bind: 'controlChartLov.controlId',
    },
    {
      name: 'controlCode',
      bind: 'controlChartLov.controlCode',
    },
    {
      name: 'analysisChartLov',
      type: FieldType.object,
      lovCode: 'MT.SPC.ANALYSIS_CHART',
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.analysisChartLov`).d('选择分析控制图'),
      computedProps: {
        required({ record }) {
          return !record.get('controlId');
        },
      },
    },
    {
      name: 'analysisId',
      bind: 'analysisChartLov.analysisId',
    },
    {
      name: 'analysisCode',
      bind: 'analysisChartLov.analysisCode',
    },
  ],
});

const chartProcessObjectTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  selection: false,
  forceValidate: true,
  fields: [
    {
      name: 'objectType',
      type: FieldType.string,
      lookupCode: 'MT.SPC.PROCESS_OBJECT_TYPE',
      label: intl.get(`${modelPrompt}.objectType`).d('过程对象类型'),
      required: true,
    },
    {
      name: 'inputType',
      type: FieldType.string,
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectCode`).d('过程对象编码'),
      computedProps: {
        lovCode({ record }) {
          if (record.get('inputType') === 'URL'||record.get('inputType') === 'SQL') {
            return jsonString2object(record.getField('objectType').getLookupData().tag, 'lovCode');
          }
          return undefined;
        },
        type({ record }) {
          if (record.get('inputType') === 'URL' ||record.get('inputType') === 'SQL') {
            return FieldType.object;
          }
          return FieldType.string;
        },
        lookupCode({ record }) {
          if (record.get('inputType') === 'IDP') {
            return jsonString2object(record.getField('objectType').getLookupData().tag, 'lovCode');
          }
          return undefined;
        },
        lookupUrl({ record, dataSet }) {
          if (record.get('inputType') === 'EMPTY') {
            const _chartType = dataSet.getState('chartType');
            if (_chartType === 'control') {
              return `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control-process-objects/fuzzy/ui?objectType=${record.get('objectType')}`;
            }
            return `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis-process-objects/fuzzy/ui?objectType=${record.get('objectType')}`;
          }
          return undefined;
        },
        textField({ record }) {
          if (record.get('inputType') === 'EMPTY') {
            return 'objectCode';
          }
          return undefined;
        },
        valueField({ record }) {
          if (record.get('inputType') === 'EMPTY') {
            return 'objectCode';
          }
          return undefined;
        },
        lookupAxiosConfig({ record }) {
          if (record.get('inputType') === 'EMPTY') {
            return {
              transformResponse(data) {
                // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
                if (data instanceof Array) {
                  return data;
                }
                const { rows } = JSON.parse(data);
                return rows;
              },
            }
          }
          return undefined;
        },
        disabled({ record }) {
          return !record.get('inputType');
        },
        required({ record }) {
          return record.get('inputType');
        },
      },
    },
    {
      name: 'objectDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectDesc`).d('过程对象说明'),
      computedProps: {
        disabled({ record }) {
          return record.get('inputType') !== 'EMPTY';
        },
        required({ record }) {
          return record.get('inputType') === 'EMPTY';
        },
      },
    },
  ],
});

const ProcessObjectTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  selection: false,
  forceValidate: true,
  fields: [
    {
      name: 'objectType',
      type: FieldType.string,
      lookupCode: 'MT.SPC.PROCESS_OBJECT_TYPE',
      label: intl.get(`${modelPrompt}.objectType`).d('过程对象类型'),
      required: true,
    },
    {
      name: 'objectCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.objectCode`).d('过程对象编码'),
      computedProps: {
        lovCode({ dataSet }) {
          return dataSet.getState('chartType') === 'control' ? 'MT.SPC.CONTROL_PROCESS_OBJECT' : 'MT.SPC.ANALYSIS_PROCESS_OBJECT';
        },
        lovPara({ record }) {
          return {
            tenantId,
            objectType: record.get('objectType'),
          };
        },
        disabled({ record }) {
          return !record.get('objectType');
        },
        required({ record }) {
          return record.get('objectType');
        },
      },
    },
    {
      name: 'objectCode',
      bind: 'objectCodeLov.objectCode',
    },
    {
      name: 'objectDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectDesc`).d('过程对象说明'),
      bind: 'objectCodeLov.objectDesc',
      disabled: true,
    },
    {
      name: 'newObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newObjectCode`).d('新过程对象编码'),
      required: true,
    },
    {
      name: 'newObjectDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newObjectDesc`).d('新过程对象说明'),
      required: true,
    },
  ],
});

export {
  controlChartTableDS,
  analysisControlChartTableDS,
  chartProcessObjectDS,
  chartProcessObjectTableDS,
  ProcessObjectTableDS,
};
