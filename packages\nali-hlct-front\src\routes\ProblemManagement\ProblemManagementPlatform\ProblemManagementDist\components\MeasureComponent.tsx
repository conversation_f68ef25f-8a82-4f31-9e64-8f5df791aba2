/**
 * @Description: 问题管理平台-临时措施 长期措施页面组件
 * @Author: <EMAIL>
 * @Date: 2023/7/6 13:38
 */
import React, { useMemo } from 'react';
import { Badge, Collapse, Icon, Tag } from 'choerodon-ui';
import intl from 'utils/intl';
import { Attachment, Button, Dropdown, Menu, Table, Form, TextArea } from 'choerodon-ui/pro';
import { ColumnAlign } from 'choerodon-ui/pro/es/table/enum';
import { observer } from 'mobx-react';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { RecordStatus } from 'choerodon-ui/pro/lib/data-set/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import AttachmentComponent from './AttachmentComponent';
import styles from '../index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';

const MeasureComponent = ({
  dataSet,
  type,
  changeObjectDs,
  currentUserId,
  userRole,
  srmFlag,
  problemStatus,
  enableFlag,
  handleSaveMeasure,
  handleSubmitMeasure,
  handleChangeTotalEnable,
  handleSaveChangeObject,
  optionList,
  influenceRangeDs,
  handleSaveChangeRange,
  enclosure8dDs = undefined,
  handleSaveEnclosure8d = () => { },
}) => {
  const attachmentProps: any = {
    name: 'evidence',
    bucketName: 'qms',
    bucketDirectory: 'problem-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const attachment8dProps: any = {
    name: 'enclosure8d',
    bucketName: 'qms',
    bucketDirectory: 'problem-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const renderMeasureStatusTag = (value, record) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
        className = 'green';
        break;
      case 'EVALUATING':
        className = 'yellow';
        break;
      default:
        className = 'blue';
    }
    return <Tag color={className}>{record!.getField('measureStatus')!.getText()}</Tag>;
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      { name: 'sequence', align: ColumnAlign.left, width: 70 },
      {
        name: 'measureDescription',
        editor: record =>
          record.dataSet.getState('operationType') === 'edit' &&
          record.get('measureStatus') === 'NEW' &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)) &&
          <TextArea name="measureDescription" autoSize={{ minRows: 2, maxRows: 8 }} />,
      },
      {
        name: 'measureVerification',
        editor: record =>
          record.dataSet.getState('operationType') === 'edit' &&
          record.get('measureStatus') === 'NEW' &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)) &&
          <TextArea name="measureVerification" autoSize={{ minRows: 2, maxRows: 8 }} />,
      },
      {
        name: 'responsiblePersonLov',
        width: 150,
        editor: record =>
          record.dataSet.getState('operationType') === 'edit' &&
          record.get('measureStatus') === 'NEW' &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      {
        name: 'planEndTime',
        width: 150,
        align: ColumnAlign.center,
        editor: record =>
          record.dataSet.getState('operationType') === 'edit' &&
          record.get('measureStatus') === 'NEW' &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      {
        name: 'actualEndTime',
        width: 150,
        align: ColumnAlign.center,
        editor: record =>
          record.dataSet.getState('operationType') === 'edit' &&
          record.get('measureStatus') === 'NEW' &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      {
        name: 'enableFlag',
        width: 100,
        align: ColumnAlign.center,
        editor: record =>
          record.dataSet.getState('operationType') === 'review' &&
          userRole.includes('LEAD_PERSON') &&
          record.get('measureStatus') === 'EVALUATING' && !!record.get('enableFlag') || record.dataSet.getState('operationType') === 'Z' &&
          userRole.includes('RESPONSIBLE_PERSON') &&
          record.get('measureStatus') === 'EVALUATING',
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`${modelPrompt}.label.effective`).d('有效')
                  : intl.get(`${modelPrompt}.label.inVain`).d('无效')
              }
            />
          );
        },
      },
      {
        name: 'feedbackRemark',
        width: 100,
        align: ColumnAlign.center,
        editor: record =>
          record.dataSet.getState('operationType') === 'review' &&
          userRole.includes('LEAD_PERSON') &&
          record.get('measureStatus') === 'EVALUATING' && !!record.get('enableFlag') || record.dataSet.getState('operationType') === 'Z' &&
          userRole.includes('RESPONSIBLE_PERSON') &&
          record.get('measureStatus') === 'EVALUATING',
      },
      {
        name: 'measureStatus',
        width: 90,
        renderer: ({ record, value }) => renderMeasureStatusTag(value, record),
      },
      {
        name: 'evidence',
        width: 150,
        align: ColumnAlign.center,
        editor: record =>
          record.dataSet.getState('operationType') === 'edit' &&
          record.get('measureStatus') === 'NEW' &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)) && (
            <Attachment {...attachmentProps} />
          ),
        renderer: ({ record }) => (
          <AttachmentComponent srmFlag={srmFlag} record={record} name="evidence" />
        ),
      },
    ],
    [type, currentUserId, userRole?.length, srmFlag],
  );

  const changeObjectColumns = useMemo(
    () => [
      { name: 'sequence', align: ColumnAlign.left, width: 70 },
      {
        name: 'objectType',
        width: 130,
        editor: record =>
          record.dataSet.getState('canEdit') &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      {
        name: 'objectCode',
        width: 200,
        editor: record =>
          record.dataSet.getState('canEdit') &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      {
        name: 'objectDescription',
        editor: record =>
          record.dataSet.getState('canEdit') &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      { name: 'responsiblePersonLov', width: 80 },
      { name: 'recordTime', align: ColumnAlign.center, width: 150 },
    ],
    [type],
  );

  const influenceRangeColumns = useMemo(
    () => [
      { name: 'sequence', align: ColumnAlign.left, width: 70 },
      {
        name: 'supplierLov',
        width: 150,
        editor: record =>
          record.dataSet.getState('canEdit') &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      {
        name: 'manufacturingProcess',
        editor: record =>
          record.dataSet.getState('canEdit') &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      {
        name: 'marketplace',
        editor: record =>
          record.dataSet.getState('canEdit') &&
          (record.status === RecordStatus.add ||
            record.get('responsiblePerson') === Number(currentUserId)),
      },
      { name: 'responsiblePersonLov', width: 80 },
      { name: 'recordTime', align: ColumnAlign.center, width: 150 },
    ],
    [],
  );

  // 最终有效性是否可编辑
  const getDisabled = (dataSet, srmFlag) => {
    const evaluatedFlag = dataSet.find(_record => _record.get('measureStatus') === 'EVALUATED');
    return !evaluatedFlag || srmFlag;
  };

  // 最终有效性按钮样式
  const getStyle = (dataSet, enableFlag, problemStatus, srmFlag) => {
    const enableStyle = {
      color: 'rgb(17, 217, 84)',
      borderColor: 'rgb(17, 217, 84)',
      backgroundColor: 'rgb(230, 255, 234)',
    };
    const disEnableStyle = {
      color: 'rgb(242, 58, 80)',
      borderColor: 'rgb(242, 58, 80)',
      backgroundColor: 'rgb(255, 240, 240)',
    };
    const emptyStyle = {
      color: 'rgb(47, 84, 235)',
      borderColor: 'rgb(47, 84, 235)',
      backgroundColor: 'rgb(240, 248, 255)',
    };
    const disableStyle = {
      opacity: 0.5,
      cursor: 'not-allowed',
    };
    const disabled = getDisabled(dataSet, srmFlag);

    if (disabled || !['RELEASED', 'FOLLOWING'].includes(problemStatus)) {
      if (enableFlag) {
        return enableFlag === 'Y'
          ? { ...enableStyle, ...disableStyle }
          : { ...disEnableStyle, ...disableStyle };
      }
      return { ...emptyStyle, ...disableStyle };
    }
    if (enableFlag) {
      return enableFlag === 'Y' ? enableStyle : disEnableStyle;
    }
    return emptyStyle;
  };

  const handleCancelMeasure = () => {
    dataSet.reset();
    dataSet.setState('operationType', 'look');
  };

  const handleChangeEnable = value => {
    handleChangeTotalEnable(value, type);
  };

  const renderTextContent = (type, enableFlag) => {
    if (type === 'TEMP') {
      if (enableFlag) {
        return enableFlag === 'Y'
          ? intl.get(`${modelPrompt}.title.tempEnable`).d('临时措施有效')
          : intl.get(`${modelPrompt}.title.tempDisable`).d('临时措施无效');
      }
      return intl.get(`${modelPrompt}.title.isTempEnable`).d('临时措施是否有效');
    }
    if (enableFlag) {
      return enableFlag === 'Y'
        ? intl.get(`${modelPrompt}.title.perpEnable`).d('长期措施有效')
        : intl.get(`${modelPrompt}.title.perpDisable`).d('长期措施无效');
    }
    return intl.get(`${modelPrompt}.title.isPerpEnable`).d('长期措施是否有效');
  };

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      {optionList.map(item => {
        return (
          <Menu.Item key={item.value}>
            <a
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => handleChangeEnable(item.value)}
            >
              {item.meaning}
            </a>
          </Menu.Item>
        );
      })}
    </Menu>
  );

  const RenderMeasureButtonGroup = observer(
    ({ dataSet, userRole, problemStatus, enableFlag, type, srmFlag }) => {
      if (['edit', 'review', 'Z'].includes(dataSet.getState('operationType'))) {
        return (
          <>
            {dataSet.getState('operationType') === 'edit' && (
              <>
                <Button
                  icon="delete"
                  funcType={FuncType.flat}
                  color={ButtonColor.red}
                  disabled={!dataSet.selected.length}
                  onClick={() => dataSet.remove(dataSet.selected)}
                >
                  {intl.get(`${modelPrompt}.button.delete`).d('删除')}
                </Button>
                <Button
                  icon="playlist_add"
                  funcType={FuncType.flat}
                  onClick={() => dataSet.create({}, 0)}
                >
                  {intl.get(`${modelPrompt}.button.add`).d('新增')}
                </Button>
              </>
            )}
            <Button icon="close" funcType={FuncType.flat} onClick={handleCancelMeasure}>
              {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
            </Button>
            <Button
              icon="save"
              funcType={FuncType.flat}
              onClick={() => handleSaveMeasure(dataSet, type)}
            >
              {intl.get(`${modelPrompt}.button.save`).d('保存')}
            </Button>
          </>
        );
      }

      return (
        <>
          <Dropdown
            overlay={menu}
            disabled={
              getDisabled(dataSet, srmFlag) ||
              !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
              !userRole.includes('LEAD_PERSON')
            }
          >
            <div
              className={styles['enable-flag-button']}
              style={getStyle(dataSet, enableFlag, problemStatus, srmFlag)}
            >
              {enableFlag && <Icon type={enableFlag === 'Y' ? 'thumb_up' : 'thumb_down'} />}
              <span>{renderTextContent(type, enableFlag)}</span>
            </div>
          </Dropdown>
          {userRole.includes('LEAD_PERSON') && (
            <>
              <Button
                icon="send-o"
                funcType={FuncType.flat}
                disabled={!['RELEASED', 'FOLLOWING'].includes(problemStatus) || srmFlag}
                onClick={() => handleSubmitMeasure(dataSet, type, 'review')}
              >
                {intl.get(`${modelPrompt}.button.evaluationSubmission`).d('评价提交')}
              </Button>
              <Button
                icon="rate_review1"
                funcType={FuncType.flat}
                disabled={!userRole.includes('LEAD_PERSON') || problemStatus !== 'FOLLOWING' || srmFlag}
                onClick={() => dataSet.setState('operationType', 'review')}
              >
                {intl.get(`${modelPrompt}.button.reviewG`).d('跟进人评价')}
              </Button>
            </>
          )}
          {userRole.includes('RESPONSIBLE_PERSON') && (
            <>
              <Button
                icon="send-o"
                funcType={FuncType.flat}
                disabled={!['RELEASED', 'FOLLOWING'].includes(problemStatus) || srmFlag}
                onClick={() => handleSubmitMeasure(dataSet, type, 'measure')}
              >
                {intl.get(`${modelPrompt}.button.measureSubmit`).d('措施提交')}
              </Button>
              <Button
                icon="edit-o"
                funcType={FuncType.flat}
                disabled={
                  !userRole.includes('RESPONSIBLE_PERSON') ||
                  !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
                  srmFlag
                }
                onClick={() => dataSet.setState('operationType', 'edit')}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
              <Button
                icon="rate_review1"
                funcType={FuncType.flat}
                disabled={!userRole.includes('MAJOR_RESPONSIBLE_PERSON') || problemStatus !== 'FOLLOWING' || srmFlag}
                onClick={() => dataSet.setState('operationType', 'Z')}
              >
                {intl.get(`${modelPrompt}.button.reviewZ`).d('主责人评价')}
              </Button>
            </>
          )}
        </>
      );
    },
  );

  const handleCancelObject = () => {
    changeObjectDs.reset();
    changeObjectDs.setState('canEdit', false);
  };

  const RenderObjectListButtonGroup = observer(({ dataSet, problemStatus, type, userRole, srmFlag }) => {
    if (dataSet.getState('canEdit')) {
      return (
        <>
          <Button
            icon="delete"
            funcType={FuncType.flat}
            color={ButtonColor.red}
            disabled={!dataSet.selected.length}
            onClick={() => dataSet.remove(dataSet.selected)}
          >
            {intl.get(`${modelPrompt}.button.delete`).d('删除')}
          </Button>
          <Button
            icon="playlist_add"
            funcType={FuncType.flat}
            onClick={() => dataSet.create({}, 0)}
          >
            {intl.get(`${modelPrompt}.button.add`).d('新增')}
          </Button>
          <Button icon="close" funcType={FuncType.flat} onClick={handleCancelObject}>
            {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
          </Button>
          <Button
            icon="save"
            funcType={FuncType.flat}
            onClick={() => handleSaveChangeObject(dataSet, type)}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </Button>
        </>
      );
    }

    return (
      <>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={
            !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
            !userRole.includes('RESPONSIBLE_PERSON') ||
            srmFlag
          }
          onClick={() => dataSet.setState('canEdit', true)}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </Button>
      </>
    );
  });

  const handleCancelRange = () => {
    influenceRangeDs.reset();
    influenceRangeDs.setState('canEdit', false);
  };

  const RenderRangeButtonGroup = observer(({ dataSet, problemStatus, userRole, srmFlag }) => {
    if (dataSet.getState('canEdit')) {
      return (
        <>
          <Button
            icon="delete"
            funcType={FuncType.flat}
            color={ButtonColor.red}
            disabled={!dataSet.selected.length}
            onClick={() => dataSet.remove(dataSet.selected)}
          >
            {intl.get(`${modelPrompt}.button.delete`).d('删除')}
          </Button>
          <Button
            icon="playlist_add"
            funcType={FuncType.flat}
            onClick={() => dataSet.create({}, 0)}
          >
            {intl.get(`${modelPrompt}.button.add`).d('新增')}
          </Button>
          <Button icon="close" funcType={FuncType.flat} onClick={handleCancelRange}>
            {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
          </Button>
          <Button
            icon="save"
            funcType={FuncType.flat}
            onClick={() => handleSaveChangeRange(dataSet)}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </Button>
        </>
      );
    }

    return (
      <>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={
            !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
            !userRole.includes('RESPONSIBLE_PERSON') ||
            srmFlag
          }
          onClick={() => dataSet.setState('canEdit', true)}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </Button>
      </>
    );
  });

  const RenderEnclosureButtonGroup = observer(({ dataSet, problemStatus, userRole }) => {
    const handleCancel = () => {
      dataSet.reset();
      dataSet.setState('canEdit', false);
    };

    if (dataSet.getState('canEdit')) {
      return (
        <>
          <Button icon="close" funcType={FuncType.flat} onClick={handleCancel}>
            {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
          </Button>
          <Button
            icon="save"
            funcType={FuncType.flat}
            onClick={() => handleSaveEnclosure8d(dataSet)}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </Button>
        </>
      );
    }

    return (
      <>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={
            !['RELEASED', 'FOLLOWING'].includes(problemStatus) ||
            !userRole.includes('MAJOR_RESPONSIBLE_PERSON')
          }
          onClick={() => dataSet.setState('canEdit', true)}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </Button>
      </>
    );
  });

  return (
    <Collapse
      bordered={false}
      defaultActiveKey={['measure', 'changeObjectList', 'influenceRange', 'enclosure8d']}
      collapsible="icon"
      className={styles['collapse-style']}
    >
      <Panel
        key="measure"
        header={
          type === 'PERP'
            ? intl.get(`${modelPrompt}.title.prepMeasure`).d('长期措施')
            : intl.get(`${modelPrompt}.title.tempMeasure`).d('临时措施')
        }
        extra={
          <RenderMeasureButtonGroup
            dataSet={dataSet}
            userRole={userRole}
            problemStatus={problemStatus}
            enableFlag={enableFlag}
            type={type}
            srmFlag={srmFlag}
          />
        }
      >
        <Table dataSet={dataSet} columns={columns} rowHeight="auto" />
      </Panel>
      {type === 'TEMP' && (
        <Panel
          key="influenceRange"
          header={intl.get(`${modelPrompt}.title.influenceRange`).d('止呼待范围')}
          extra={
            <RenderRangeButtonGroup
              dataSet={influenceRangeDs}
              userRole={userRole}
              problemStatus={problemStatus}
              srmFlag={srmFlag}
            />
          }
        >
          <Table dataSet={influenceRangeDs} columns={influenceRangeColumns} />
        </Panel>
      )}
      {type === 'PERP' && (
        <Panel
          key="enclosure8d"
          header={intl.get(`${modelPrompt}.title.enclosure8d`).d('验证报告')}
          extra={
            <RenderEnclosureButtonGroup
              dataSet={enclosure8dDs}
              userRole={userRole}
              problemStatus={problemStatus}
              srmFlag={srmFlag}
            />
          }
        >
          <Form dataSet={enclosure8dDs} columns={3}>
            <Attachment {...attachment8dProps} />
          </Form>
        </Panel>
      )}
      <Panel
        key="changeObjectList"
        header={intl.get(`${modelPrompt}.title.changeObjectList`).d('变更对象列表')}
        extra={
          <RenderObjectListButtonGroup
            dataSet={changeObjectDs}
            userRole={userRole}
            problemStatus={problemStatus}
            type={type}
            srmFlag={srmFlag}
          />
        }
      >
        <Table dataSet={changeObjectDs} columns={changeObjectColumns} />
      </Panel>
    </Collapse>
  );
};
export default MeasureComponent;
