import React, { useState, useMemo, useEffect } from 'react';
import { DataSet, Table, Button, Lov, Select, Spin, NumberField, Switch } from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import intl from 'utils/intl';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { tableDS, siteDS } from './stores/ProductProcessShelfMaintenanceDS';
import { Host } from '@/utils/config';

// const Host = `/key-ne-focus-mes-38283`;

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.hmes.product.processShelf.maintenance';

const ProductProcessShelfMaintenance = () => {
  const [loading, setLoading] = useState(false);
  const [disabledFlag, setDisabledFlag] = useState(false);

  const siteDs = useMemo(() => new DataSet(siteDS()), []);
  const tableDs = useMemo(() => new DataSet(tableDS(siteDs)), []);

  useEffect(() => {
    tableDs.query();
    siteDs.query();
  }, []);

  // 保存
  const handelSave = async () => {
    await tableDs.validate().then(valiResult => {
      if (valiResult) {
        const listData = tableDs.toJSONData();
        if (listData.some(item => item.fromOperationId === item.toOperationId)) {
          return notification.warning({
            message: intl
              .get(`${modelPrompt}.error.start.operation`)
              .d(`数据中存在起始工艺等于截止工艺的数据，请检查`),
            placement: 'bottomRight',
          });
        }
        if (listData.some(item => Number(item.maxExpiration) <= Number(item.minExpiration))) {
          return notification.warning({
            message: intl
              .get(`${modelPrompt}.error.maxExpiration.check`)
              .d(`数据中存在保质期小于或等于保质期下限的数据，请检查`),
            placement: 'bottomRight',
          });
        }
        setLoading(true);
        request(`${Host}/v1/${tenantId}/hme-material-op-expirations/save/ui`, {
          method: 'post',
          body: listData,
        }).then(res => {
          setLoading(false);
          if (res && !res.failed) {
            notification.success();
            setDisabledFlag(false);
            tableDs.query();
          } else {
            notification.error({ message: res.message });
          }
        });
      } else {
        return notification.warning({
          message: intl
            .get(`${modelPrompt}.error.required`)
            .d(`请填写必填项`),
          placement: 'bottomRight',
        });
      }
    });
  };

  // 编辑按钮
  const handelEdit = () => {
    tableDs.records.forEach(item => {
      item.setState('editing', true);
    });
    setDisabledFlag(true);
  };

  // 取消按钮
  const handelCancel = () => {
    tableDs.records.forEach(item => {
      item.setState('editing', false);
    });
    tableDs.query();
    setDisabledFlag(false);
  };

  // lov变化事件
  const changeObject = (lovRecords, record) => {
    if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
      record.getField('revisionCode').set('required', true);
      record.set('revisionFlag', 'Y');
      record.set('revisionCode', lovRecords.revisionCode);
    } else {
      record.set('revisionCode', '');
      record.set('revisionFlag', 'N');
      record.getField('revisionCode').set('required', false);
    }
  };

  // 版本下拉框变化事件
  const changeVersion = record => {
    if (record.data.revisionFlag === 'Y') {
      record.getField('revisionCode').set('required', true);
    }
  };

  const handleCreate = () => {
    if (tableDs.selected.length > 0) {
      tableDs.unSelectAll();
    }
    tableDs.create({ operationTagConfigId: '' });
    tableDs.current.setState('editing', true);
    setDisabledFlag(true);
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          onClick={() => handleCreate()}
          funcType="flat"
          // shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      lock: 'left',
      renderer: ({ record }) => {
        return (
          record.get('operationTagConfigId') === '' && (
            <Popconfirm
              title= {intl
                .get(`${modelPrompt}.error.delete`)
                .d(`是否确认删除？`)}
              onConfirm={() => {
                tableDs.remove(record);
                if (tableDs.toJSONData().length === 0) {
                  setDisabledFlag(false);
                }
              }}
            >
              <Button funcType="flat" icon="remove" shape="circle" size="small" />
            </Popconfirm>
          )
        );
      },
    },
    // 物料编码
    {
      name: 'materialObj',
      align: 'left',
      renderer: ({ record }) => record.get('materialCode'),
      editor: record => {
        return (
          record.getState('editing') && (
            <Lov
              dataSet={tableDs}
              name="materialObj"
              onChange={lovRecords => changeObject(lovRecords, record)}
            />
          )
        );
      },
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
    },
    // 物料版本
    {
      name: 'revisionCode',
      align: 'left',
      editor: record => {
        return (
          record.getState('editing') &&
          record.get('revisionFlag') === 'Y' && (
            <Select name="revisionCode" dataSet={tableDs} onChange={() => changeVersion(record)} />
          )
        );
      },
    },
    // 起始工艺
    {
      name: 'fromOperationObj',
      align: 'left',
      renderer: ({ record }) => record.get('fromOperationName'),
      editor: record => {
        return record.getState('editing') && <Lov dataSet={tableDs} name="fromOperationObj" />;
      },
    },
    // 截止工艺
    {
      name: 'toOperationObj',
      align: 'left',
      renderer: ({ record }) => record.get('toOperationName'),
      editor: record => {
        return record.getState('editing') && <Lov dataSet={tableDs} name="toOperationObj" />;
      },
    },
    // 保质期下限
    {
      name: 'minExpiration',
      align: 'left',
      editor: record => {
        return record.getState('editing') && <NumberField name="minExpiration" dataSet={tableDs} />;
      },
    },
    // 保质期（小时）
    {
      name: 'maxExpiration',
      align: 'left',
      editor: record => {
        return record.getState('editing') && <NumberField name="maxExpiration" dataSet={tableDs} />;
      },
    },
    // 是否跨工段
    {
      name: 'crossLineFlag',
      width: 100,
      editor: record =>
        record.getState('editing') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    // {
    //   name: 'crossLineFlag',
    //   width: 120,
    //   align: 'left',
    //   editor: record => {
    //     return record.getState('editing') && <Select dataSet={tableDs} name="crossLineFlag" />;
    //   },
    //   renderer: ({ value }) => <span>{value === 'Y' ? '是' : '否'}</span>,
    // },
    // 有效性
    {
      name: 'enableFlag',
      width: 100,
      editor: record =>
        record.getState('editing') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'transferExpirationFlag',
      width: 100,
      editor: record =>
        record.getState('editing') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
  ];

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MT.MES.MATERIAL_OP_EXPIRATION`,
      title: intl.get('tarzan.hmes.ProductProcessShelfMaintenance.import').d('产品工艺保质期维护导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('产品工艺保质期维护')}>
        {!disabledFlag && (
          <>
            <Button onClick={handelEdit} style={{ marginRight: 15 }} icon="edit" color="primary">
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Button onClick={handleImport} style={{ marginRight: 15 }} color="primary" icon="daorucanshu">
              {intl.get('tarzan.common.button.repeat').d('导入')}
            </Button>
          </>
        )}
        {disabledFlag && (
          <>
            <Button onClick={handelCancel}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
            <Button onClick={handelSave} style={{ marginRight: 15 }} icon="save" color="primary">
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Table
            dataSet={tableDs}
            columns={columns}
            style={{ height: 400 }}
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            queryFieldsLimit={4}
            searchCode="ProductProcessShelfMaintenance"
            customizedCode="ProductProcessShelfMaintenance"
          />
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.ProductProcessShelfMaintenance', 'tarzan.common'],
})(ProductProcessShelfMaintenance);
