/* eslint-disable jsx-a11y/alt-text */
import React, { Fragment, useState, useRef, useEffect } from 'react';
import { Table, Icon, Spin, Modal, Select, Tooltip } from 'choerodon-ui/pro';
import { Popconfirm, Tag } from 'choerodon-ui';
import { isEmpty, uniqBy } from 'lodash';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import FileGreen from '@/assets/icons/file_green.png';
import FileRed from '@/assets/icons/file_red.png';
import BoxGreen from '@/assets/icons/box_green.png';
import PersonnelGreen from '@/assets/icons/personnel_green.png';
import formatterCollections from 'utils/intl/formatterCollections';
import './index.module.less';

const modelPrompt = 'common.hmes.apiMonitor';
const { Option } = Select;

const MonitorCard = props => {
  const {
    dataSet,
    leftWoInfoList,
    handleSearchOrder,
    handelAddRemark,
    handelAddtab,
    handleCloseOrder,
    willDispatchSpin,
    handleDragEndWill,
    handleFetchHistory,
    handleCloseTag,
    handelDelTag,
    willDispatchDataSelectTag,
    // path,
  } = props;

  const [selectTag, setSelectTag] = useState([]); // 标签筛选选择数据

  const selectRef = useRef();
  useEffect(() => {
    selectRef.current = selectTag;
  }, [selectTag]);
  // useEffect(() => {

  // }, []);

  /**
   * 点击条形图行或者头table行的时候
   * @param {*} data
   * @param {*} val
   */
  const handleSearch = record => {
    if (handleSearchOrder) {
      handleSearchOrder(record.data);
    }
  };

  const delTag = () => {
    if (handelDelTag) {
      handelDelTag('will');
    }
  };
  const handelRemark = () => {
    if (handelAddRemark) {
      handelAddRemark('will');
    }
  };
  const handeltab = () => {
    if (handelAddtab) {
      handelAddtab('will');
    }
  };
  const handleClose = () => {
    if (handleCloseOrder) {
      handleCloseOrder();
    }
  };
  const handleFetch = value => {
    if (handleFetchHistory) {
      handleFetchHistory(value);
    }
  };
  const handleDel = (value, i) => {
    if (handleCloseTag) {
      handleCloseTag(value, i);
    }
  };

  const handleFilter = () => {
    const data = uniqBy(leftWoInfoList, 'tag').filter(v => v.tag);
    if (data.length > 0) {
      Modal.open({
        title: '标签筛选',
        destroyOnClose: true,
        closable: true,
        children: (
          <Select
            placeholder="请选择筛选标签"
            required
            clearButton
            multiple
            onChange={changeTag}
            style={{ width: '100%' }}
          >
            {data.map(item => (
              <Option key={item.workOrderId} value={item.tag}>
                {item.tag}-{item.tagDesc}
              </Option>
            ))}
          </Select>
        ),
        onOk: onFetchRemake,
      });
    }
  };

  const changeTag = value => {
    if (value) {
      setSelectTag(value);
    }
  };

  const onFetchRemake = () => {
    if (selectRef.current.length > 0) {
      const data = [];
      selectRef.current.forEach(item => {
        leftWoInfoList.forEach(v => {
          if (v.tag === item) {
            data.push(v);
          }
        });
      });
      dataSet.loadData(data);
    }
  };

  // 头table的columns
  const columns = [
    {
      name: 'dto4',
      align: 'center',
      renderer: ({ record }) => {
        return (
          <div>
            <Tooltip theme="dark" placement="top" title="工单状态">
              <img src={record.data.status === 'RELEASED' ? FileGreen : FileRed}></img>
            </Tooltip>
            <Tooltip theme="dark" placement="top" title="物料异常">
              <img style={{ margin: '0 10px' }} src={BoxGreen}></img>
            </Tooltip>
            <Tooltip theme="dark" placement="top" title="人员异常">
              <img src={PersonnelGreen}></img>
            </Tooltip>
          </div>
        );
      },
    },
    {
      name: 'workOrderNum',
      width: 150,
      header: record => (
        <span>
          <Icon onClick={handleFilter} type="filter_alt" />
          &nbsp;
          {record.title}
        </span>
      ),
      renderer: ({ value, record }) => {
        return (
          <div>
            {record.get('tag') && (
              <Tooltip theme="dark" placement="top" title={record.get('tagDesc')}>
                <Icon style={{ color: record.get('tag') }} type="emoji_flags" />
              </Tooltip>
            )}
            <a onClick={() => handleSearch(record)}>{value}</a>
          </div>
        );
      },
    },
    {
      name: 'statusDesc',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'customerCode',
    },
    {
      name: 'customerName',
    },
    {
      name: 'customerNameAlt',
    },
    {
      name: 'qty',
    },
    {
      name: 'remark',
    },
    {
      name: 'taskRemark',
      renderer: ({ record }) => {
        return record.data.remarkList?.map((v, i) => {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <Tag key={i} closable afterClose={() => handleDel(record.data, i)}>
              {v}
            </Tag>
          );
        });
      },
    },
    {
      name: 'planStartTime',
      width: 150,
    },
    {
      name: 'planEndTime',
      width: 150,
    },
    {
      name: 'woHistory',
      renderer: ({ record }) => {
        return <a onClick={() => handleFetch(record.data)}>工单历史</a>;
      },
    },
    {
      name: 'releasedQty',
    },
    {
      name: 'revisionCode',
    },
  ];

  const handleDragEnd = async (dataSet, _, resultDrag) => {
    const { source = {}, destination = {} } = resultDrag;
    if (!isEmpty(destination) && source.index !== destination.index) {
      const capacityList = [];
      capacityList.push({
        changeWo: dataSet.data[destination.index].data,
        downWo: dataSet.data[destination.index + 1] ? dataSet.data[destination.index + 1].data : {},
        upWo: dataSet.data[destination.index - 1] ? dataSet.data[destination.index - 1].data : {},
      });
      if (handleDragEndWill) {
        handleDragEndWill(capacityList[0]);
      }
    }
  };

  return (
    <Fragment>
      <Spin spinning={willDispatchSpin}>
        <Header
          key="basicInfo"
          className="WillDispatchOrderTitle"
          title={
            <div style={{ position: 'relative' }}>
              <span>{intl.get('tarzan.aps.statistics.checked').d('待派工单')}&nbsp;&nbsp;</span>
              <span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 32,
                  width: 100,
                  fontSize: 12,
                  zIndex: 10,
                }}
              >
                {intl.get('tarzan.aps.statistics.total').d('待派工单总数：')}
                {dataSet.toData().length}
              </span>
            </div>
          }
        >
          <span className="WillDispatchOrder_title">
            <a funcType="raised" color="blue" onClick={handelRemark}>
              {intl.get(`${modelPrompt}.api.name`).d('备注')}
            </a>
            <a
              hidden={
                willDispatchDataSelectTag.length === 0 ||
                willDispatchDataSelectTag.includes(null) ||
                willDispatchDataSelectTag.includes('')
              }
              funcType="raised"
              color="blue"
              onClick={delTag}
            >
              {intl.get(`${modelPrompt}.api.delete`).d('删除标记')}
            </a>
            <a funcType="raised" color="yellow" onClick={handeltab}>
              {intl.get(`${modelPrompt}.api.name`).d('标记')}
            </a>

            <Popconfirm
              title={intl
                .get(`tarzan.common.message.confirm.handleClose`)
                .d('确认将选中工单更新为关闭状态?')}
              onConfirm={handleClose}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <PermissionButton
                type="text"
                // color="primary"
                permissionList={[
                  {
                    code: `assemblyOrderDispatch.button.close`,
                    type: 'button',
                    meaning: '装配工单派工-关闭',
                  },
                ]}
              >
                {intl.get('tarzan.aps.common.button.close').d('关闭')}
              </PermissionButton>
            </Popconfirm>
          </span>
        </Header>
        <Content>
          <Table
            pagination={false}
            spin={{ size: 'large', spinning: false }}
            dataSet={dataSet}
            rowDraggable
            dragDropContextProps={{
              onDragEnd: (dataSet, columns, resultDrag) => {
                handleDragEnd(dataSet, columns, resultDrag);
              },
            }}
            // style={{ height: '4.5rem' }}
            style={{ height: document.body.clientHeight - 300 }}
            customizedCode="WillDispatchOrder"
            columns={columns}
            className='WillDispatchOrder'
          />
        </Content>
      </Spin>
    </Fragment>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(MonitorCard);
