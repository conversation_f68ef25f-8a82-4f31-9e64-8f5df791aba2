/**
 * @Description: 检验单维护-service
 * @Author: <EMAIL>
 * @Date: 2023/1/9 16:37
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 批量合格
 * @function batchApprovalInfo
 * @returns {object} fetch Promise
 */
export function batchApprovalInfo(): object{
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/batch/approval`,
    method: 'POST',
  }
}


/**
 * 保存检验项目
 * @function SaveInspectDoc
 * @returns {object} fetch Promise
 */
export function SaveInspectDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/update/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.BASIC_HIS`,
    method: 'POST',
  };
}

/**
 * 取消检验单
 * @function CancelInspectDoc
 * @returns {object} fetch Promise
 */
export function CancelInspectDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/cancel/ui`,
    method: 'POST',
  };
}

/**
 * 完成检验单
 * @function CompleteInspectDoc
 * @returns {object} fetch Promise
 */
export function CompleteInspectDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/complete/ui`,
    method: 'GET',
  };
}

/**
 * 复制检验单
 * @function CopyInspectDoc
 * @returns {object} fetch Promise
 */
export function CopyInspectDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/copy/ui`,
    method: 'GET',
  };
}

/**
 * 获取报检对象明细 - 生产工单
 * @function GetWODetail
 * @returns {object} fetch Promise
 */
export function GetWODetail(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/wo/limit/property/list`,
    method: 'GET',
  };
}

/**
 * 获取报检对象明细 - 容器
 * @function GetContainerDetail
 * @returns {object} fetch Promise
 */
export function GetContainerDetail(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/query-eo-material/ui`,
    method: 'GET',
  };
}

/**
 * 获取报检对象明细 - 其他
 * @function GetOtherDetail
 * @returns {object} fetch Promise
 */
export function GetOtherDetail(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-instruction-doc/materialLot-limit-docLine/ui`,
    method: 'GET',
  };
}

/**
 * 查询用户默认站点
 * @function GetDefaultSite
 * @returns {object} fetch Promise
 */
export function GetDefaultSite(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-user-inspect-permission/default/site/ui`,
    method: 'GET',
  };
}

/**
 * 扫描值集接口
 * @function ScanInspectObject
 * @returns {object} fetch Promise
 */
export function ScanInspectObject(): object {
  return {
    lovCode: 'MT.MES.EOMATERIALLOT',
  };
}

/**
 * 子页面提交
 * @function SubmitInspect
 * @returns {object} fetch Promise
 */
export function SubmitInspect(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/submit`,
    method: 'POST',
  };
}

/**
 * 子页面处置审核
 * @function AuditReview
 * @returns {object} fetch Promise
 */
export function AuditReview(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/disposal/review`,
    method: 'POST',
  };
}

/**
 * 子页面增加tabs
 * @function GetInspectTimes
 * @returns {object} fetch Promise
 */
export function GetInspectTimes(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/inspect/times/get`,
    method: 'GET',
  };
}

/**
 * 查询doc的处置方法
 * @function GetFunctionList
 * @returns {object} fetch Promise
 */
export function GetFunctionList(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/all/disposition/function/get`,
    method: 'GET',
  };
}
