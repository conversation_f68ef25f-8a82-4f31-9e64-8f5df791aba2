/*
 * @Description: 产品审核计划-services
 * @Author: <<EMAIL>>
 * @Date: 2023-10-09 15:29:48
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-10 13:57:22
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存产品审核计划
 * @function SaveProductRevPlan
 * @returns {object} fetch Promise
 */
export function SaveProductRevPlan(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-plan/save/ui`,
    method: 'POST',
  };
}

/**
 * 提交产品审核计划
 * @function SubmitProductRevPlan
 * @returns {object} fetch Promise
 */
export function SubmitProductRevPlan(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-plan/submit/ui`,
    method: 'POST',
  };
}