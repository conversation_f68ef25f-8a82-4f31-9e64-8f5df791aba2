import React, { useMemo, useCallback, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Lov, NumberField, Select, Button, Spin } from 'choerodon-ui/pro';
import notification from 'utils/notification';
import { Collapse } from 'choerodon-ui';
import { Record } from 'choerodon-ui/dataset';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useDataSetEvent } from 'utils/hooks';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import useRequest from './useRequest';
import { SaveHeadLine, DeleteHeadLine, SaveLineList } from './services';
import { headDS, lineDS } from './stores';

const modelPrompt = 'modelPrompt_code';
const { Panel } = Collapse;

const FormulaMaintenancePage = observer((props) => {
  const {
    headDs,
    lineDs,
  } = props;

  const { run: saveHeadLine, loading: saveHeadLineLoading } = useRequest(SaveHeadLine(), { manual: true, needPromise: true });
  const { run: deleteHeadLine, loading: deleteHeadLineLoading } = useRequest(DeleteHeadLine(), { manual: true, needPromise: true });
  const { run: saveLineList, loading: saveLineListLoading } = useRequest(SaveLineList(), { manual: true, needPromise: true });

  useDataSetEvent(headDs.queryDataSet!, 'update', ({ name, record }) => {
    if (name === 'materialLov') {
      record.set('bomLov', null)
    }
  })

  useDataSetEvent(headDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteLov':
        record.init('materialLov', null)
        break;
      case 'materialLov':
        record.init('bomLov', null)
        break;
      default:
        break;
    }
  })

  useDataSetEvent(headDs, 'load', ({ dataSet }) => {
    if (!dataSet.length) {
      // 查询出来没有数据
      lineDs.loadData([]);
      return;
    }
    headDs.select(headDs.data[0])
    // makeLineTableQuery(dataSet.current);
  })

  useEffect(() => {
    headDs.query();
    // headDs.currentPage
  }, [])

  useEffect(() => {
    if (!headDs.selected[0]) {
      return;
    }
    if (headDs.selected[0]?.status === 'add') {
      lineDs.loadData([]);
    } else {
      makeLineTableQuery(headDs.selected[0])
    }
  }, [headDs.selected])


  const makeLineTableQuery = useCallback(
    (record: Record) => {
      lineDs.setQueryParameter('formulaId', record.get('formulaId'));
      lineDs.query();
    },
    [],
  )

  const headerRowClick = useCallback(
    (record) => {
      // makeLineTableQuery(record)
      headDs.select(record)
    },
    [],
  )

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'siteLov',
        renderer: ({ record }) => {
          if (record?.status === 'add') {
            return <Lov record={record} name='siteLov' />
          }
          return record?.get('siteCode')
        },
      },
      {
        name: 'materialLov',
        renderer: ({ record }) => {
          if (record?.status === 'add') {
            return <Lov record={record} name='materialLov' />
          }
          return record?.get('materialCode')
        },
      },
      { name: 'materialDesc' },
      {
        name: 'bomLov',
        renderer: ({ record }) => {
          if (record?.status === 'add') {
            return <Lov record={record} name='bomLov' />
          }
          return record?.get('bomName')
        },
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'lineNumber',
        renderer: ({ record }) => {
          return lineDs.indexOf(record) + 1
        },
      },
      { name: 'materialCode' },
      { name: 'materialDesc' },
      { name: 'bom' },
      {
        name: 'proportion',
        renderer: ({ record }) => {
          return <NumberField record={record!} name='proportion' />
        },
      },
      {
        name: 'tolerance',
        renderer: ({ record }) => {
          return <NumberField record={record!} name='tolerance' formatter={(value) => `${value}${record!.get('toleranceType') || ''}`} />
        },
      },
      {
        name: 'toleranceType',
        renderer: ({ record }) => {
          return <Select record={record!} name='toleranceType' />
        },
      },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    const _record = headDs.create({}, 0);
    headDs.select(_record)
  }, []);

  const handleSaveHead = () => {

    saveHeadLine({
      params: [headDs.selected[0].toData()],
      // onSuccess: () => {
      //   headDs.query();
      // },
    }).then(res => {
      if (!res?.failed) {
        notification.success({});
        headDs.query();
      }
    })
  }

  const handleDeleteHead = () => {
    if (headDs.selected[0].status === 'add') {
      // 新建未保存数据
      headDs.remove(headDs.selected[0])
    } else {
      deleteHeadLine({
        params: [{
          formulaId: headDs.selected[0].get('formulaId'),
        }],
      }).then(res => {
        if (!res?.failed) {
          notification.success({});
          headDs.query();
        }
      })
    }
  }

  const handleSaveLine = () => {
    saveLineList({
      params: lineDs.map(item => item.toData()),
    }).then(res => {
      if (!res?.failed) {
        notification.success({});
        if (res && res?.length > 0) {
          lineDs.setQueryParameter('formulaId', headDs.selected[0].get('formulaId'));
          lineDs.query();
        }
      }
    })
  }

  return (
    <div className="hmes-style">
      <Spin spinning={saveHeadLineLoading || deleteHeadLineLoading || saveLineListLoading}></Spin>
      <Header title={intl.get(`${modelPrompt}.title.list`).d('配方维护')}>
        <Button
          color={ButtonColor.primary}
          onClick={handleDeleteHead}
          disabled={!headDs.selected.length}
          icon="delete"
        >
          删除
        </Button>
        <Button
          color={ButtonColor.primary}
          onClick={handleSaveHead}
          disabled={headDs.selected[0]?.status !== 'add'}
          icon="save"
        >
          保存
        </Button>
        <Button
          color={ButtonColor.primary}
          onClick={handleAdd}
          disabled={headDs.selected[0]?.status === 'add'}
          icon="add"
        >
          新建
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
          dataSet={headDs}
          columns={headColumns}
          searchCode="page_searchCode"
          customizedCode="page_customizedCode"
        />
        <Collapse bordered={false} defaultActiveKey={['lineTable']}>
          <Panel
            key="lineTable"
            header={intl.get(`${modelPrompt}.title.lineTable`).d('行表格')}
            extra={
              <Button
                funcType={FuncType.link}
                color={ButtonColor.primary}
                disabled={!lineDs.length}
                onClick={handleSaveLine}
              >
                保存
              </Button>
            }
          >
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="page_lineTable_customizedCode"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
})

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(FormulaMaintenancePage),
);
