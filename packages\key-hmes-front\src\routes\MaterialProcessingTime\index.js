/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2022-04-21 11:03:11
 * @LastEditTime: 2023-02-24 16:47:12
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import notification from 'utils/notification';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { DataSet, Table, Lov, Switch, NumberField, Button, TextField } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { Button as PermissionButton } from 'components/Permission';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC, API_HOST } from '@utils/config';
import uuid from 'uuid/v4';

import { tableDS } from './stores/WorkCellListDS';

const modelPrompt = 'tarzan.templete';

export function SaveData() {
  return {
    url: `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-material-processing-times`,
    method: 'POST',
  };
}

export function EquipUpdate() {
  return {
    url: `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-material-processing-times/equip/update`,
    method: 'POST',
  };
}

export function MaterialUpdate() {
  return {
    url: `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-material-processing-times/material/update`,
    method: 'POST',
  };
}

const MaterialProcessingTime = props => {
  const {
    dataSet,
    match: { path },
  } = props;

  useEffect(() => {
    dataSet.query(props.dataSet.currentPage);
  }, []);

  const saveData = useRequest(SaveData(), {
    manual: true,
    needPromise: true,
  });
  // 下发设备
  const equipUpdate = useRequest(EquipUpdate(), {
    manual: true,
    needPromise: true,
  });
  // 物料变更
  const materialUpdate = useRequest(MaterialUpdate(), {
    manual: true,
    needPromise: true,
  });

  const orderCreate = () => {
    dataSet.create({ enableFlag: 'Y', lineState: 'add', uuid: uuid() }, 0);
  };

  // 保存
  const handleSave = async record => {
    const validateRecord = await record.validate('all', true);
    if (validateRecord) {
      const { equipment, workcell, ...data } = record.toData() || {};
      saveData.run({
        params: [data],
        onSuccess: (res) => {
          notification.success({});
          record.reset();
          setTimeout(() => {
            initRecord(record, {
              ...res[0],
              uuid: data.uuid,
            });
          }, 0);
        },
      });
    }
  };

  const initRecord = (record, data) => {
    const resKeys = Object.keys(data) || [];
    resKeys.forEach(item => {
      record.init(item, data[item]);
    });
    record.init('lineState', '');
  };

  // 取消
  const handleCancel = record => {
    if (record.get('lineState') === 'add') {
      dataSet.delete(record, false);
    } else {
      record.reset();
    }
  };

  const columns = [
    {
      name: 'siteLov',
      width: 180,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <Lov record={record} name='siteLov' onChange={() => { record.init('modelLov', null) }} />,
      renderer: ({ record }) => {
        return record.get('siteCode');
      },
    },
    {
      name: 'siteName',
      width: 180,
    },
    {
      name: 'bomCodeLov',
      width: 180,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <Lov record={record} name='bomCodeLov' />,
      renderer: ({ record }) => {
        return record.get('bomCode');
      },
    },
    {
      name: 'attribute14',
      width: 120,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <TextField record={record} name='attribute14' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute15',
      width: 120,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <TextField record={record} name='attribute15' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute12',
      width: 120,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <TextField record={record} name='attribute12' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute13',
      width: 120,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <TextField record={record} name='attribute13' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'minHour',
      width: 150,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='minHour' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'maxHour',
      width: 150,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='maxHour' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute2',
      width: 150,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='attribute2' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute1',
      width: 150,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='attribute1' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute4',
      width: 150,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='attribute4' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute3',
      width: 150,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='attribute3' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute6',
      width: 150,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='attribute6' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute5',
      width: 150,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='attribute5' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute7',
      width: 120,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='attribute7' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute11',
      width: 120,
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <NumberField record={record} name='attribute11' />,
      renderer: ({ value }) => {
        return value;
      },
    },
    {
      name: 'attribute8',
      width: 160,
      align: 'center',
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <Switch />,
      renderer: ({ value }) => {
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        );
      },
    },
    {
      name: 'enableFlag',
      width: 120,
      align: 'center',
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <Switch />,
      renderer: ({ value }) => {
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        );
      },
    },
    {
      name: 'changeFlag',
      width: 100,
      align: 'center',
      editor: record => ['editing', 'add'].indexOf(record.get('lineState')) > -1 && <Switch />,
      renderer: ({ value }) => {
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        );
      },
    },
    { name: 'createdUsername', width: 120 },
    { name: 'creationDate', width: 120 },
    { name: 'lastUpdatedUsername', width: 120 },
    { name: 'lastUpdateDate', width: 120 },
    {
      name: 'option',
      fixed: 'right',
      lock: 'right',
      width: 180,
      align: 'center',
      title: intl.get('tarzan.common.label.action').d('操作'),
      renderer: ({ record }) => optionRender(record),
    },
  ];

  // 操作列渲染
  const optionRender = record => {
    if (['editing', 'add'].indexOf(record.get('lineState')) > -1) {
      return (
        <>
          <PermissionButton
            type="text"
            onClick={() => handleCancel(record)}
            style={{ marginRight: '8px' }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </PermissionButton>
          <PermissionButton
            type="text"
            onClick={() => handleSave(record)}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.save').d('保存')}
          </PermissionButton>
        </>
      );
    }
    return (
      <>
        <PermissionButton
          type="text"
          onClick={() => record.set('lineState', 'editing')}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </PermissionButton>
      </>
    );

  };

  const handleEquipUpdate = () => {
    const params = dataSet.toData().map(item => item.materialProcessingTimeId);
    equipUpdate.run({
      params,
    }).then(res => {
      if (res?.success) {
        notification.success({});
        dataSet.query(props.dataSet.currentPage);
      }
    });
  }

  const handleMaterialUpdate = () => {
    const params = dataSet.toData().map(item => item.materialProcessingTimeId);
    materialUpdate.run({
      params,
    }).then(res => {
      if (res?.success) {
        notification.success({});
        dataSet.query(props.dataSet.currentPage);
      }
    });
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('BOM号基础数据维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => orderCreate()}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button
          color={ButtonColor.primary}
          onClick={handleEquipUpdate}
        >
          下发设备
        </Button>
        <Button
          color={ButtonColor.primary}
          onClick={handleMaterialUpdate}
        >
          物料变更
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          searchCode='sbygzdywh1'
          customizedCode='sbygzdywh1'
          dataSet={dataSet}
          columns={columns}
          style={{
            height: 400,
          }}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.equipment.workCell', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...tableDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MaterialProcessingTime),
);
