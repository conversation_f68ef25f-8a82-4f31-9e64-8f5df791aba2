/**
 * @Description: 不良记录单管理平台 -详情界面
 * @Author: <EMAIL>
 * @Date: 2023/3/9 14:37
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  Modal,
  NumberField,
  Select,
  Table,
  TextField,
  Attachment,
  Dropdown,
  Menu,
} from 'choerodon-ui/pro';
import { Collapse, Tag } from 'choerodon-ui';
import notification from 'utils/notification';
import { Content, Header } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import intl from 'utils/intl';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { <PERSON><PERSON><PERSON>, C7nFormItemSort } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import moment from 'moment/moment';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import request from 'utils/request';
import { BASIC } from 'hcm-components-front/lib/utils/config';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import { observer } from 'mobx-react';
import {
  detailDS,
  disposalDS,
  lineDtlDS,
  ncRecordLineDS,
  flexDS,
} from '../stores/NcReportDocDtlDS';
import {
  BatchReview,
  QueryEoOrMaterialLot,
  SaveAndSubmitNcReportDoc,
  SaveNcReportDoc,
  DirectDisposal,
  QueryInspectDocLine,
  ChangeFlowType,
} from '../services';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.ncReportDocMaintainNew';
const userInfo = getCurrentUser();
const tenantId = getCurrentOrganizationId();
let roleList: any = [];
let userRoleList: any = [];
let newDisposalRoleList: any = [];

// interface DynamicDisplayFlagProps {
//   reworkRouterFlag: boolean; // dispositionFunction=REWORK_ROUTER
//   reworkSourceFlag: boolean; // dispositionFunction=REWORK_SOURCE/REWORK_ANY
//   degradeFlag: boolean; // dispositionFunction=DEGRADE
//   eoDischargeFlag: boolean; // dispositionFunction=EO_DISCHARGE
//   crossWoFlag: boolean; // dispositionFunction=CROSS_WO_INTERCEPT
//   concessionFlag: boolean; // dispositionFunction=CONCESSION_INTERCEPTION
// }

const NcReportDetail = props => {
  const {
    history,
    match: { params },
    customizeForm,
    customizeTable,
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState(kid === 'create');
  const [activeKey, setActiveKey] = useState<any>(['baseInfo', 'ncRecordLine']);
  const [disposalType, setDisposalType] = useState<string>('ALL'); // 处置类型
  const [ncReportStatus, setNcReportStatus] = useState<string>('NEW'); // 不良记录单状态
  const [createMethod, setCreateMethod] = useState<string>(''); // 创建方式
  const [ncReviewStatus, setNcReviewStatus] = useState<string>(''); // 审核状
  const [dispositionFunction, setDispositionFunction] = useState(''); // 处置方法（整体处置）
  const [dispositionGroupId, setDispositionGroupId] = useState<number>(); // 处置组id
  const [flowTypeOption, setFlowTypeOption] = useState<any[]>([]); // 流程类型数据源
  const [flowType, setFlowType] = useState<string | null>(null); // 流程类型
  // const [roleList, setRoleList] = useState<any[]>([]); // 跟进人角色
  // const [userRoleList, setUserRoleList] = useState<any[]>([]); // 登陆人角色
  const [editDisabled, setEditDisabled] = useState(false); // 处置方法（整体处置）
  // const [dynamicDisplayFlag, setDynamicDisplayFlag] = useState<DynamicDisplayFlagProps>({
  //   reworkRouterFlag: false,
  //   reworkSourceFlag: false,
  //   degradeFlag: false,
  //   eoDischargeFlag: false,
  //   crossWoFlag: false,
  //   concessionFlag: false,
  // });
  const lineDetailDs = useMemo(() => new DataSet(lineDtlDS()), []);
  const flexDs = useMemo(() => new DataSet(flexDS()), []);
  const ncRecordLineDs = useMemo(
    () =>
      new DataSet({
        ...ncRecordLineDS(),
        children: { ncReportLineDtlList: lineDetailDs },
      }),
    [],
  );
  const batchEditDs = useMemo(
    () =>
      new DataSet({
        ...disposalDS(),
        autoCreate: true,
      }),
    [],
  );
  const disposalDs = useMemo(() => new DataSet(disposalDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          ncReportLineList: ncRecordLineDs,
          disposalList: disposalDs,
          batchEditDs,
        },
      }),
    [],
  );
  const { loading: saveLoading } = useRequest(SaveNcReportDoc(), {
    manual: true,
  });
  const { loading: saveAndSubmitLoading } = useRequest(SaveAndSubmitNcReportDoc(), {
    manual: true,
  });
  const { loading: reviewLoading } = useRequest(BatchReview(), {
    manual: true,
  });
  const { loading: directDisposalLoading } = useRequest(DirectDisposal(), {
    manual: true,
  });
  const { run: queryInspectDocLine, loading: queryInspectDocLoading } = useRequest(
    QueryInspectDocLine(),
    {
      manual: true,
    },
  );
  const { run: changeFlowType, loading: changeFlowTypeLoading } = useRequest(
    ChangeFlowType(),
    {
      manual: true,
    },
  );
  const { loading: queryEOLoading } = useRequest(QueryEoOrMaterialLot(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if (kid === 'create') {
      return;
    }
    request(
      `/hpfm/v1/${tenantId}/lovs/value/batch?roleGroup=YP.QIS.NC_REPORT_EDIT&&userRoleGroup=YP.QIS.NC_REPORT_RECORDER&&flowType=YP.QIS.NC_FLOW_TYPE&newDisposalRole=YP.QIS.NEW_FLOW_DISPOSAL`,
      {
        method: 'GET',
        // query: params,
      },
    ).then(res => {
      if (res && !res.failed) {
        roleList = res.userRoleGroup;
        userRoleList = res.roleGroup;
        newDisposalRoleList = res.newDisposalRole;
        // setRoleList(res.userRoleGroup); // 登记人
        // setUserRoleList(res.roleGroup); // 跟进人
        setFlowTypeOption(res.flowType);
        handleQueryDetail(kid);
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  }, [kid]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('ncReportId', id);
    detailDs.query().then(res => {
      const {
        disposalType,
        ncReportStatus,
        ncReviewStatus,
        createMethod,
        disposalList,
        flowType: originFlowType,
        siteId,
        flex,
        dispositionGroupId,
        ncReportLineList,
        partNcReportLineDisposalList,
      } = res;
      setCreateMethod(createMethod);
      setDispositionGroupId(dispositionGroupId);
      let flowType;
      // 为兼容之前的数据，当前状态不为'NEW'且没有flowType时，走旧流程逻辑
      if (originFlowType) {
        flowType = originFlowType;
      } else if (ncReportStatus !== 'NEW' && !originFlowType) {
        flowType = 'OLD';
      }
      setFlowType(flowType);
      flexDs.setState('ncReportStatus', ncReportStatus);
      flexDs.setState('flowType', flowType);
      handleChangeStyle(flex?.materialProductStyle, true);
      // const disposalType = 'ALL';
      const tempRoleList = roleList.map(item => item.value);
      const tempUserRoleList = userRoleList.map(item => item.value);
      const tempNewDisposalRoleList = newDisposalRoleList.map(item => item.value);
      if (partNcReportLineDisposalList?.length) {
        const disposalDsData = partNcReportLineDisposalList.map(item => {
          const disposalFunction =
            item?.disposalFunctionList?.find(i => i.qty !== null) ||
            item?.disposalFunctionList[0] ||
            {};
          // onDisposalFunChange({
          //   dispositionFunctionId: disposalFunction.disposalFunctionId || null,
          //   dispositionFunction: disposalFunction.dispositionFunction || null,
          //   description: disposalFunction.dispositionFunctionDesc || null,
          //   functionType: disposalFunction.functionType || null,
          // });
          return {
            ...item,
            dispositionGroupId,
            disposalFunctionId: disposalFunction.disposalFunctionId || null,
            dispositionFunction: disposalFunction.dispositionFunction || null,
            dispositionFunctionDesc: disposalFunction.dispositionFunctionDesc || null,
            functionType: disposalFunction.functionType || null,
            [disposalFunction.dispositionFunction]: disposalFunction.qty || null,
            qty: disposalFunction.qty || null,
          };
        });
        disposalDs.loadData(disposalDsData);
      }
      if (flex) {
        flexDs.loadData([
          {
            ...flex,
            siteId,
          },
        ]);
      } else {
        flexDs.loadData([
          {
            siteId,
          },
        ]);
      }
      if (ncReportStatus === 'NEW') {
        // 不合格登记状态--不合格登记，不良明细
        if (flowType && tempRoleList.includes(userInfo.currentRoleCode)) {
          if (flex && flex.recorderPerson && Number(flex.recorderPerson) !== Number(userInfo.id)) {
            // 登陆人不等于当前用户
            setEditDisabled(true);
          } else {
            setEditDisabled(false);
          }
        } else {
          setEditDisabled(true);
        }
      }
      if (ncReportStatus === 'NG_JUDGE') {
        // 不合格判定状态--不合格判定
        if (flowType && tempUserRoleList.includes(userInfo.currentRoleCode)) {
          // 质量工程师角色不包含当前用户角色
          if (flex && flex.followUpPerson && Number(flex.followUpPerson) !== Number(userInfo.id)) {
            // 登陆人不等于当前用户
            setEditDisabled(true);
          } else {
            setEditDisabled(false);
          }
        } else {
          setEditDisabled(true);
        }
      }
      if (ncReportStatus === 'NG_REASON') {
        // 不合格原因状态--不合格判定--不良原因描述
        if (flowType && flex.responsiblePersonDesc && flex.responsiblePersonDesc !== userInfo.realName) {
          // 当前用户不等于主责人
          setEditDisabled(true);
        } else {
          setEditDisabled(false);
        }
      }
      if (ncReportStatus === 'NG_DISPOSAL') {
        // 不合格处置状态--处置组，判断依据
        if (flowType === 'OLD' && flex.followUpPerson && Number(flex.followUpPerson) !== Number(userInfo.id)) {
          // 当前用户不等于跟进人
          setEditDisabled(true);
        } else if (flowType === 'NEW' && !tempNewDisposalRoleList.includes(userInfo.currentRoleCode)) {
          setEditDisabled(true);
        } else {
          setEditDisabled(false);
        }
        // 当前状态为不合格处置，处置类型为整体处置时，拦截工艺和返修工艺值集需要传入不良对象的identification
        if (disposalType === 'ALL') {
          const _identificationList = (ncReportLineList || [])
            .map(item => item.ncObjectCode)
            .join(',');
          disposalDs.setState('identificationList', _identificationList);
          // 整体处置时，需要给disposalDs传入处置组Id
          if (!disposalList?.length) {
            disposalDs.loadData([
              {
                dispositionGroupId,
                disposalUserLov: {
                  id: userInfo.id,
                  realName: userInfo.realName,
                },
                disposalTime: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
              },
            ]);
          } else {
            disposalDs.current?.set('dispositionGroupId', dispositionGroupId);
          }
        }
        if (!partNcReportLineDisposalList?.length && disposalType === 'PART') {
          initDisposalDs(disposalType, dispositionGroupId);
        }
      }
      if (
        ncReportStatus === 'NG_REVIEWING' ||
        ncReportStatus === 'REVIEWED' ||
        ncReportStatus === 'CANCEL'
      ) {
        // 不合格审批状态
        setEditDisabled(true);
      }
      setDisposalType(disposalType);
      // handleResetDisposal();
      if (disposalType) {
        setActiveKey(['baseInfo', 'ncRecordLine', 'ncDetermine', disposalType, 'judgmentBasis']);
      }
      if (disposalType === 'ALL' && disposalList?.length) {
        setDispositionFunction(disposalList[0].dispositionFunction);
      }
      setNcReportStatus(ncReportStatus);
      setNcReviewStatus(ncReviewStatus);
      if (disposalType === 'ALL') {
        // handleCreateQmsLine();
        if (ncReportLineList.length) {
          const typeList = ncReportLineList.filter(item => item.ncObjectType === 'INSPECT_DOC');
          if (!typeList.length) {
            handleChangeDisposalType('ALL', createMethod, dispositionGroupId);
          }
        }
      }

      // 同步处置行上的optionList
      (ncRecordLineDs || []).forEach(_record => handleSyncOptionList(_record, false));
    });
  };

  const handleInitDisposalInfo = _record => {
    _record?.init('interceptOperationLov', undefined);
    _record?.init('degradeLevel', undefined);
    _record?.init('reworkOperationLov', undefined);
    _record?.init('relatedUnitLov', undefined);
    _record?.init('overTime', undefined);
  };

  const handleCancel = () => {
    if (kid === 'create') {
      history.push('/hwms/ncReport-doc-maintain-new/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(kid);
    }
  };

  const handleSaveAndSubmit = async () => {
    const _reviewType = detailDs.current?.get('reviewType');
    if (
      _reviewType === 'NO_REVIEW' &&
      (!ncRecordLineDs.length ||
        (disposalType === 'ALL' && !disposalDs.current?.get('disposalFunctionId')) ||
        disposalType === 'NO')
    ) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.disposalFunRequired`)
          .d(`保存并提交时评审规则类型为【无需审核】场景下需存在行且每个行需给出处置意见！`),
      });
      return;
    }
    await handleSave(false, true);
  };

  const handleValidate = async () => {
    const detailValidate = await detailDs.validate(true, true);
    const lineValidate = await ncRecordLineDs.validate();
    const disposalValidate = await disposalDs.validate();
    return detailValidate && lineValidate && disposalValidate;
  };

  const handleSave = async (createFlag = false, submitFlag = false) => {
    // 处理保存数据
    const data = detailDs.current?.toData();
    const { ncReportLineList, disposalType, disposalList } = data;
    // 处理班次日期保存格式
    ncReportLineList.forEach(i => (i.shiftDate = i.shiftDate?.split(' ')[0] || undefined));
    // 处理处置数据保存格式
    const disposalData = disposalList;
    if (disposalType === 'PART') {
      disposalData.forEach(item => {
        const dispositionFunctionLov = item.dispositionFunctionLov || {};
        item.disposalFunctionList = [
          {
            disposalFunctionId: dispositionFunctionLov.dispositionFunctionId,
            dispositionFunction: dispositionFunctionLov.dispositionFunction,
            dispositionFunctionDesc: dispositionFunctionLov.functionTypeDesc,
            qty: item.qty || 0,
          },
        ];
      });
    }
    let follow = null;
    let followDesc = '';
    if (ncReportStatus !== 'NEW') {
      follow = data.flex && data.flex.followUpPerson ? data.flex.followUpPerson : userInfo.id;
      followDesc =
        data.flex && data.flex.followUpPersonDesc
          ? data.flex.followUpPersonDesc
          : userInfo.realName;
    }
    const params = {
      ...data,
      disposalList: undefined,
      disposalType: disposalType === 'NO' ? undefined : disposalType,
      ncReportLineList,
      allNcReportLineDisposal:
        disposalType === 'ALL' && disposalData.length > 0 ? disposalData[0] : {},
      partNcReportLineDisposalList:
        disposalType === 'PART' && disposalData.length > 0 ? disposalData : [],
      _innerMap: {
        ...flexDs?.current?.toData(),
        recorderPerson:
          data.flex && data.flex.recorderPerson ? data.flex.recorderPerson : userInfo.id,
        recorderPersonDesc:
          data.flex && data.flex.recorderPersonDesc
            ? data.flex.recorderPersonDesc
            : userInfo.realName,
        followUpPerson: follow,
        followUpPersonDesc: followDesc,
      },
    };
    if (ncReportStatus === 'NG_DISPOSAL') {
      if (disposalType === 'ALL' && !params.allNcReportLineDisposal.disposalFunctionId) {
        return notification.error({
          message: intl.get(`${modelPrompt}.please.select.disposal.method`).d('请选择处置方法！'),
        });
      }
      if (
        disposalType === 'PART' &&
        !params.partNcReportLineDisposalList?.every(i => i.disposalFunctionId)
      ) {
        return notification.error({
          message: intl.get(`${modelPrompt}.please.select.disposal.method`).d('请选择处置方法！'),
        });
      }
    }
    if (submitFlag) {
      const validateFlag = await handleValidate();
      if (!validateFlag) {
        return false;
      }
      let validateError = false;
      if (ncReportStatus === 'NEW') {
        if (flowType === 'OLD') {
          validateError =
            !params._innerMap.quantityCharacter ||
            !params._innerMap.ncType ||
            !params._innerMap.fristNcLevel ||
            !params._innerMap.ncStartUserId ||
            !params._innerMap.ncStartTime ||
            !params._innerMap.stage ||
            !params._innerMap.traceInfo ||
            !params._innerMap.ncRerortDescription ||
            !params._innerMap.traceFlag ||
            !params._innerMap.areaId;
        } else {
          validateError =
            !params._innerMap.ncStartUserId ||
            !params._innerMap.ncStartTime ||
            !params._innerMap.ncRerortDescription ||
            !params._innerMap.areaId ||
            !params._innerMap.ncReason ||
            !params._innerMap.ncRerortDescription;
        }
      }
      if (ncReportStatus === 'NG_JUDGE') {
        if (flowType === 'OLD') {
          validateError =
            !params._innerMap.finalNcLevel ||
            !params._innerMap.responsiblePerson ||
            !params._innerMap.unit;
          if (params._innerMap.incomingReasonFlag && params._innerMap.incomingReasonFlag === 'Y') {
            validateError =
              !params._innerMap.reasonMaterialId ||
              !params._innerMap.reasonBarcodeQty ||
              !params._innerMap.reasonSupplierId;
          }
        }
      }
      if (ncReportStatus === 'NG_REASON') {
        validateError = !params._innerMap.ncReason;
      }
      if (ncReportStatus === 'NG_DISPOSAL') {
        if (flowType === 'OLD') {
          validateError =
            !params._innerMap.reviewFlag ||
            !params._innerMap.customerFlag ||
            !params._innerMap.materialProductStyle;
        } else {
          validateError =
            !params._innerMap.incomingReasonFlag ||
            !params._innerMap.reviewFlag ||
            !params._innerMap.customerFlag ||
            !params._innerMap.materialProductStyle;
          if (params._innerMap.incomingReasonFlag && params._innerMap.incomingReasonFlag === 'Y') {
            validateError =
              !params._innerMap.reasonMaterialId ||
              !params._innerMap.reasonBarcodeQty ||
              !params._innerMap.reasonSupplierId;
          }
        }
      }
      if (validateError) {
        return notification.error({ message: '有必填项未输入' });
      }
      // 保存并提交
      return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/save-submit/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && res.success) {
          notification.success({});
          setCanEdit(false);
          history.push(`/hwms/ncReport-doc-maintain-new/dist/${res.rows}`);
          handleQueryDetail(res.rows);
        } else {
          notification.error({ message: res.message });
          return false;
        }
      });
      // saveAndSubmitNcReportDoc({
      //   params,
      //   onSuccess: res => {
      //     notification.success({});
      //     setCanEdit(false);
      //     history.push(`/hwms/ncReport-doc-maintain-new/dist/${res}`);
      //     handleQueryDetail(res);
      //   },
      // });
    }
    // 保存
    return request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.LINE,${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.DETAIL`,
      {
        method: 'POST',
        body: params,
      },
    ).then(res => {
      if (res && res.success) {
        notification.success({});
        if (createFlag) {
          if (kid === 'create') {
            detailDs.reset();
            lineDetailDs.loadData([]);
            disposalDs.loadData([]);
            flexDs.loadData([]);
          } else {
            history.push(`/hwms/ncReport-doc-maintain-new/dist/create`);
            detailDs.loadData([
              {
                disposalType: 'NO',
                ncReportStatus: 'NEW',
                createMethod: 'MANUAL',
                ncReportType: 'NC_REPORT',
              },
            ]);
          }
          setDisposalType('NO');
          setNcReportStatus('NEW');
          // setNcReviewStatus('');
          setCreateMethod('');
        } else {
          setCanEdit(false);
          history.push(`/hwms/ncReport-doc-maintain-new/dist/${res.rows}`);
          handleQueryDetail(res.rows);
        }
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  const handleChangeDetail = (oldVal, fieldName) => {
    if (
      !ncRecordLineDs.length ||
      (!ncRecordLineDs.current?.get('ncRecordType') && !ncRecordLineDs.current?.get('ncRecordType'))
    ) {
      return;
    }
    const content = detailDs.current?.getField(fieldName)?.get('label');
    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <p>
          {intl
            .get(`${modelPrompt}.info.clearLineData`)
            .d(`不良记录单行、整体/部分处置信息会清空，确定更换`)}
          {content}?
        </p>
      ),
    }).then(button => {
      if (button === 'ok') {
        ncRecordLineDs.loadData([]);
        setDisposalType('NO');
        handleResetDisposal();
      } else {
        detailDs.current?.set(fieldName, oldVal);
      }
    });
  };

  const handleChangeBusRule = value => {
    if (value) {
      // 切换业务类型规则是，清空处置行上的数量
      if (disposalDs.length) {
        disposalDs.forEach(_record => {
          _record.set('dispositionFunctionLov', null);
          handleInitDisposalInfo(_record);
        });
      }
      const optionDs = detailDs.getField('inspectBusinessType')?.getOptions(detailDs.current);
      const currentRecord = optionDs?.find(
        _record => _record?.get('inspectBusinessType') === value,
      );
      detailDs.current?.set('reviewType', currentRecord?.get('reviewType'));
    }
  };

  const handleResetDisposal = () => {
    const _disposalType = detailDs.current?.get('disposalType');
    if (_disposalType === 'PART' || !_disposalType) {
      disposalDs.loadData([]);
    } else {
      disposalDs.loadData([
        {
          dispositionGroupId,
          disposalUserLov: {
            id: userInfo.id,
            realName: userInfo.realName,
          },
          disposalTime: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
        },
      ]);
    }
  };

  const handleChangeDisposalType = (value, createMethod, groupId = dispositionGroupId) => {
    setDisposalType(value);
    if (value) {
      // 展开处置对应的折叠面板
      setActiveKey(['baseInfo', 'ncRecordLine', 'ncDetermine', value, 'judgmentBasis']);
    }
    // 切换处置类型时，处置组旧数据需要清空
    handleResetDisposal();
    // 没有行明细时不做处理
    if (!ncRecordLineDs.length) {
      return;
    }
    if (createMethod === 'QMS') {
      if (value === 'ALL') {
        // 当不良记录单头创建方式=QMS，处置类型选择了整体处置时需自动生成不良对象类型=INSPECT_DOC的行
        handleCreateQmsLine();
      } else {
        // 当不良记录单头创建方式=QMS，处置类型切换了整体处置外的其余类型时需删除不良对象类型=INSPECT_DOC的行
        ncRecordLineDs.forEach(_record => {
          if (_record.get('ncObjectType') === 'INSPECT_DOC') {
            ncRecordLineDs.remove(_record);
          }
        });
      }
    }
    initDisposalDs(value, groupId);
  };

  const initDisposalDs = (value: string, dispositionGroupId: number | undefined) => {
    if (value === 'PART') {
      disposalDs.remove(disposalDs.current);
      // 根据不良记录行上的信息初始化处置DS
      ncRecordLineDs.forEach(_record => {
        const recordTypeRecord = getNcRecordTypeDesc(_record.get('ncRecordType'), _record);
        const objectTypeRecord = getNcObjectTypeDesc(_record.get('ncObjectType'), _record);
        disposalDs.create({
          dispositionGroupId,
          ncRecordType: _record.get('ncRecordType'),
          ncRecordTypeDesc: recordTypeRecord?.get('meaning'),
          ncObjectType: _record.get('ncObjectType'),
          ncObjectTypeDesc: objectTypeRecord?.get('meaning'),
          ncObjectId: _record.get('ncObjectId'),
          ncObjectCode: _record.get('ncObjectCode'),
          ncObjectRevisionCode: _record.get('revisionCode'),
          firstDisposalResult: _record.get('firstDisposalResult'),
          firstDisposalResultDesc: _record.get('firstDisposalResultDesc'),
          qty: _record.get('qty') || 0,
          // PASS: _record.get('ncRecordType') !== 'EO_MATERIAL_NC' ? _record.get('qty') : 0,
          uomId: _record.get('uomId'),
          uomName: _record.get('uomName'),
          optionList: getOptionList(_record.toData().ncReportLineDtlList),
        });
      });
    } else {
      disposalDs.current?.set('dispositionGroupId', dispositionGroupId);
    }
  };

  // 自动生成不良对象类型=INSPECT_DOC的行
  const handleCreateQmsLine = () => {
    queryInspectDocLine({
      params: { inspectDocIds: ncRecordLineDs.map(_record => _record.get('sourceDocId')) },
      onSuccess: res => {
        (res || []).forEach(item => {
          ncRecordLineDs.create(
            {
              ncRecordType: ncRecordLineDs.current?.get('ncRecordType'),
              ncObjectType: 'INSPECT_DOC',
              ncObjectId: item.inspectDocId,
              ncObjectCode: item.inspectDocNum,
              qty: item.inspectSumQty,
              uomLov: item.uomId ? { uomId: item.uomId, uomName: item.uomName } : undefined,
              supplierLov: { supplierId: item.supplierId, supplierName: item.supplierName },
              customerLov: { customerId: item.customerId, customerName: item.customerName },
              locatorLov: { locatorId: item.locatorId, locatorName: item.locatorName },
              equipmentLov: { equipmentId: item.equipmentId, equipmentCode: item.equipmentCode },
              operationLov: { operationId: item.operationId, operationName: item.operationName },
            },
            0,
          );
        });
      },
    });
  };

  const findPartDisposalRecords = (ncRecordType, ncObjectType, ncObjectLov) => {
    const getNcObjectCode = type => {
      switch (type) {
        case 'MATERIAL_LOT':
          return ncObjectLov?.materialLotCode;
        case 'MATERIAL':
          return ncObjectLov?.materialName;
        case 'EO':
          return ncObjectLov?.eoNum;
        default:
          return undefined;
      }
    };
    return disposalDs.filter(
      _record =>
        _record.get('ncRecordType') === ncRecordType &&
        _record.get('ncObjectType') === ncObjectType &&
        _record.get('ncObjectCode') === getNcObjectCode(ncObjectType),
    );
  };

  const getNcRecordTypeDesc = (value, record) => {
    // 拿到类型对应的OptionDs
    const optionDs = ncRecordLineDs.getField('ncRecordType')?.getOptions(record);
    return optionDs?.find(_rec => _rec.get('value') === value);
  };

  const getNcObjectTypeDesc = (value, record) => {
    // 拿到类型对应的OptionDs
    const optionDs = ncRecordLineDs.getField('ncObjectType')?.getOptions(record);
    return optionDs?.find(_rec => _rec.get('value') === value);
  };

  const ncRecordLineColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'ncObjectType',
        lock: ColumnLock.left,
        width: 120,
      },
      {
        name: 'ncObjectCode',
        width: 140,
      },
      {
        name: 'qty',
      },
      {
        name: 'ncCode',
        width: 200,
        renderer: ({ record }) => {
          const ncCodeList = record?.get('ncCode')?.split(',') || [];
          const ncDescList = record?.get('ncDesc')?.split(',') || [];
          if (!ncCodeList?.length) {
            return;
          }
          return ncCodeList.map((item, index) => {
            return ncDescList[index] ? (
              <Tag>
                {item}({ncDescList[index]})
              </Tag>
            ) : (
              <Tag>{item}</Tag>
            );
          });
        },
      },
      {
        name: 'lot',
      },
      {
        name: 'workOrderNum',
      },
    ];
  }, [canEdit, disposalType, createMethod, kid]);

  // 更新处置上的下拉数据源
  const handleSyncOptionList = (record, currentFlag) => {
    const disRecords = findPartDisposalRecords(
      record?.get('ncRecordType'),
      record?.get('ncObjectType'),
      record?.get('ncObjectLov'),
    );
    const lineDtlList = currentFlag ? lineDetailDs.toData() : record.toData().ncReportLineDtlList;
    const _newOptionList = getOptionList(lineDtlList);
    (disRecords || []).forEach(_record => {
      _record.set('optionList', _newOptionList);
      // 检查处置所选的实物是否都在_newOptionList中,不在的话清空
      if (
        _record.get('componentMaterialLotCode') &&
        _newOptionList.findIndex(
          i => i.componentMaterialLotCode === _record.get('componentMaterialLotCode'),
        ) === -1
      ) {
        _record.init('componentMaterialLotCode', undefined);
        _record.init('PASS', 0);
      }
    });
  };

  // 拼接返回optionList的displayValue
  const getOptionValue = item => {
    const labelText = !['MAT', 'LOT'].includes(item.identifyType)
      ? intl.get(`${modelPrompt}.option.materialLot`).d('物料批')
      : intl.get(`${modelPrompt}.option.material`).d('物料');
    const materialText = item.componentRevisionCode
      ? `${item.componentMaterialName}/${item.componentRevisionCode}`
      : item.componentMaterialName;

    return !['MAT', 'LOT'].includes(item.identifyType)
      ? `${labelText}: ${item.componentMaterialLotCode}`
      : `${labelText}: ${materialText}`;
  };

  const getOptionList = data => {
    // 行明细去重，作为实物数据源
    const _optionList: any = [];
    (data || []).forEach(item => {
      const _index = _optionList.findIndex(
        (i: any) => i.componentMaterialLotCode === item.componentMaterialLotCode,
      );
      if (_index === -1) {
        _optionList.push({
          ...item,
          displayValue: getOptionValue(item),
        });
      }
    });
    return _optionList;
  };

  // const handleChangeMaterialLotCode = (value, record) => {
  //   const currentData: any = (record.get('optionList') || []).find(
  //     (i: any) => i.componentMaterialLotCode === value,
  //   );
  //   record.set('PASS', currentData?.sumAssembleQty || 0);
  //   record.set('uomId', currentData?.uomId || undefined);
  //   record.set('uomName', currentData?.uomName || undefined);
  // };

  // ncReportStatus=NEW时详情页所有字段均可编辑，同新建页面
  // ncReportStatus=HANDLE且ncReviewStatus=空或UNREVIEW或REJECT时详情页仅允许处置字段编辑
  // ncReportStatus=COMPLETED/CANCEL时详情页所有字段均不可编辑；
  // ncReportStatus=HANDLE且ncReviewStatus=REVIEWING审核中、REVIEW已审核时所详情页有字段均不可编辑；
  const partDisposalColumns: any = useMemo(() => {
    const tempNewDisposalRoleList = newDisposalRoleList.map(item => item.value);
    const disposalEditFlag =
      canEdit &&
      (flowType === 'OLD' && ncReportStatus === 'NG_DISPOSAL' && Number(userInfo.id) === Number(flexDs.current?.toData().followUpPerson)
      || flowType === 'NEW' && tempNewDisposalRoleList.includes(userInfo.currentRoleCode));
    // (!['COMPLETED', 'CANCEL'].includes(ncReportStatus) ||
    //   (ncReportStatus === 'HANDLE' && !['REVIEWING', 'REVIEW'].includes(ncReviewStatus)));
    return [
      // {
      //   name: 'editColumn',
      //   align: ColumnAlign.center,
      //   lock: ColumnLock.left,
      //   width: 80,
      //   renderer: ({ record }) => (
      //     <Popconfirm
      //       title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
      //       onConfirm={() => disposalDs.remove(record)}
      //       okText={intl.get('tarzan.common.button.confirm').d('确认')}
      //       cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
      //     >
      //       <PermissionButton
      //         type="c7n-pro"
      //         icon="remove"
      //         disabled={!disposalEditFlag || record.get('ncRecordType') !== 'EO_MATERIAL_NC'}
      //         // disabled={!disposalEditFlag}
      //         funcType="flat"
      //         shape="circle"
      //         size="small"
      //         permissionList={[
      //           {
      //             code: `dist.button.editPartDisposal`,
      //             type: 'button',
      //             meaning: '详情页-删除部分处置行按钮',
      //           },
      //         ]}
      //       />
      //     </Popconfirm>
      //   ),
      // },
      // { name: 'ncRecordTypeDesc', width: 150 },
      { name: 'ncObjectTypeDesc', width: 150 },
      {
        name: 'ncObjectCode',
        width: 150,
        renderer: ({ record }) =>
          record?.get('ncObjectRevisionCode')
            ? `${record?.get('ncObjectCode')}/${record?.get('ncObjectRevisionCode')}`
            : record?.get('ncObjectCode'),
      },
      {
        name: 'firstDisposalResultDesc',
        width: 130,
        editor: () => false,
      },
      {
        name: 'dispositionFunctionLov',
        editor: record =>
          disposalEditFlag && <Lov onChange={() => handleInitDisposalInfo(record)} />,
      },
      {
        name: 'reworkOperationLov',
        editor: record =>
          disposalEditFlag &&
          ['REWORK_SOURCE', 'CROSS_WO_REWORK'].includes(record.get('dispositionFunction')) && (
            <Lov />
          ),
      },
      {
        name: 'degradeLevel',
        editor: record =>
          disposalEditFlag && record.get('dispositionFunction') === 'DEGRADE' && <Select />,
      },
      {
        name: 'interceptOperationLov',
        editor: record =>
          disposalEditFlag &&
          ['CONCESSION_INTERCEPTION', 'CROSS_WO_INTERCEPT'].includes(
            record.get('dispositionFunction'),
          ) && <Lov />,
      },
      {
        name: 'relatedUnitLov',
        editor: record =>
          disposalEditFlag && record.get('dispositionFunction') === 'SCRAP' && <Lov />,
      },
      {
        name: 'overTime',
        width: 150,
        editor: record =>
          disposalEditFlag &&
          record.get('dispositionFunction') === 'OVER_TIME_RELEASE' && <DateTimePicker />,
      },
      {
        name: 'remark',
        editor: () => disposalEditFlag && <TextField />,
      },
      {
        name: 'disposalUserLov',
        width: 150,
        editor: () => disposalEditFlag && <Lov />,
      },
      {
        name: 'disposalTime',
        width: 150,
        editor: () => disposalEditFlag && <DateTimePicker />,
      },
    ];
  }, [canEdit, ncReportStatus, ncReviewStatus, flowType, newDisposalRoleList?.length]);

  const handleChangeFunction = val => {
    setDispositionFunction(val?.dispositionFunction);
    disposalDs.current?.init('dischargeWorkcellLov', undefined);
    disposalDs.current?.init('reworkRouterLov', undefined);
    disposalDs.current?.init('degradeLevel', undefined);
  };

  const handleChangeUser = value => {
    if (!value) {
      flexDs.current?.set('unitObj', undefined);
    } else {
      const unitDs = flexDs.getField('unitObj')?.getOptions(flexDs.current);
      unitDs?.setQueryParameter('id', value.id);
      unitDs?.query().then(res => {
        if (res?.content?.length === 1) {
          flexDs.current?.set('unitObj', res?.content[0]);
        } else {
          flexDs.current?.set('unitObj', undefined);
        }
      });
    }
  };

  const handleBatchEditConfirm = () => {
    return new Promise(async resolve => {
      const valRes = await batchEditDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      disposalDs.selected.forEach(record => {
        const {
          dispositionFunctionLov,
          functionTypeDesc,
          reworkOperationLov = undefined,
          degradeLevel = undefined,
          interceptOperationLov = undefined,
          relatedUnitLov = undefined,
          overTime = undefined,
          remark = undefined,
          disposalUserLov = undefined,
          disposalTime = undefined,
        } = batchEditDs.current?.toData();
        record.init('dispositionFunctionLov', dispositionFunctionLov);
        record.init('functionTypeDesc', functionTypeDesc);
        record.init('reworkOperationLov', reworkOperationLov);
        record.init('degradeLevel', degradeLevel);
        record.init('interceptOperationLov', interceptOperationLov);
        record.init('relatedUnitLov', relatedUnitLov);
        record.init('overTime', overTime);
        record.init('remark', remark);
        record.init('disposalUserLov', disposalUserLov);
        record.init('disposalTime', disposalTime);
      });
      disposalDs.unSelectAll();
      batchEditDs.reset();
      return resolve(true);
    });
  };

  const handleBatchEdit = () => {
    // 批量编辑标识
    batchEditDs.setState('batchEditFlag', true);
    // 传入处置组id(设置了autoCreate为true没生效，暂时这样处理)
    if (!batchEditDs?.length) {
      batchEditDs.create({ dispositionGroupId });
    } else {
      batchEditDs.current?.init('dispositionGroupId', dispositionGroupId);
    }
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.copyInspection`).d('复制检验方案'),
      destroyOnClose: true,
      style: {
        width: 720,
      },
      onClose: () => {
        batchEditDs.reset();
        disposalDs.unSelectAll();
        batchEditDs.setState('batchEditFlag', false);
      },
      onOk: () => handleBatchEditConfirm(),
      children: (
        <Form dataSet={batchEditDs} columns={2}>
          <Lov name="dispositionFunctionLov" onChange={handleChangeFunction} />
          <Lov name="reworkOperationLov" />
          <Select name="degradeLevel" />
          <Lov name="interceptOperationLov" />
          <Lov name="relatedUnitLov" />
          <DateTimePicker name="overTime" />
          <Lov name="disposalUserLov" />
          <DateTimePicker name="disposalTime" />
          <TextField name="remark" colSpan={2} />
        </Form>
      ),
    });
  };

  const RenderBatchEditButton = observer(({ dataSet, canEdit }) => {
    return (
      <>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={!canEdit || !dataSet.selected.length || ncReportStatus !== 'NG_DISPOSAL'}
          onClick={handleBatchEdit}
        >
          {intl.get(`${modelPrompt}.button.batchEdit`).d('批量编辑')}
        </Button>
      </>
    );
  });

  // 附件配置
  const attachmentProps: any = {
    name: 'disposalBasisEnclosure',
    bucketName: 'qms',
    bucketDirectory: 'ncreport-doc-maintain',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const handleChangeFlowType = ({ key }) => {
    changeFlowType({
      params: {
        ncReportId: kid,
        flowType: key,
      },
      onSuccess: () => {
        handleQueryDetail(kid);
      },
    })
  };

  const handleChangeStyle = (value, initFlag) => {
    let dispositionGroupCode;
    if (!value) {
      dispositionGroupCode = null;
    } else if (value === 'SELF') {
      dispositionGroupCode = 'SELF_DISPOSAL';
    } else {
      dispositionGroupCode = 'PURCHASE_DISPOSAL';
    }
    if (!initFlag) {
      disposalDs.forEach((_record) => {
        _record.set('dispositionFunctionLov', null);
      })
    }
    disposalDs.setState('dispositionGroupCode', dispositionGroupCode);
    batchEditDs.setState('dispositionGroupCode', dispositionGroupCode);
  };

  const flowTypeMenu = useMemo(() => (
    <Menu className={styles['split-menu']} style={{ width: '100px' }} onClick={handleChangeFlowType}>
      {flowTypeOption.map(item => (
        <Menu.Item key={item.value}>{item.meaning}</Menu.Item>
      ))}
    </Menu>
  ), [flowTypeOption?.length]);

  const handleChangeIncomingFlag = () => {
    flexDs.current?.set('ncReasonLov', undefined);
    flexDs.current?.set('reasonBarcodeQty', undefined);
    flexDs.current?.set('reasonSupplierObj', undefined);
  }

  return (
    <div className="hmes-style" style={{ overflow: 'auto' }}>
      <TarzanSpin
        dataSet={detailDs}
        spinning={
          saveLoading ||
          // queryLoading ||
          saveAndSubmitLoading ||
          reviewLoading ||
          queryEOLoading ||
          directDisposalLoading ||
          queryInspectDocLoading ||
          changeFlowTypeLoading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('不良评审单管理平台')}
          backPath="/hwms/ncReport-doc-maintain-new/list"
        >
          {canEdit ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave(false)}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              {/* <Button icon="save" onClick={() => handleSave(true)}>
                {intl.get(`tarzan.common.button.saveAndCreate`).d('保存并新建下一条')}
              </Button> */}
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button icon="save" onClick={handleSaveAndSubmit}>
                {intl.get(`${modelPrompt}.button.savesubmit`).d('保存并提交')}
              </Button>
            </>
          ) : (
            <>
              <Button
                icon="edit-o"
                color={ButtonColor.primary}
                onClick={() => setCanEdit(true)}
                disabled={editDisabled}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
              <Button icon="save" onClick={handleSaveAndSubmit} disabled={editDisabled}>
                {intl.get(`${modelPrompt}.button.submit`).d('提交')}
              </Button>
              <Dropdown
                overlay={flowTypeMenu}
                disabled={ncReportStatus !== 'NEW' || Boolean(flowType)}
                placement={Placements.bottomRight}
              >
                <Button
                  loading={reviewLoading}
                  disabled={ncReportStatus !== 'NEW' || Boolean(flowType)}
                >
                  {intl.get(`${modelPrompt}.button.flowType`).d('流程类型')}
                </Button>
              </Dropdown>
            </>
          )}
          <ApprovalInfoDrawer objectTypeList={['QIS_NC_REPORT']} objectId={kid} />
        </Header>
        <Content>
          <Collapse bordered={false} activeKey={activeKey} onChange={val => setActiveKey(val)}>
            <Panel
              key="baseInfo"
              header={intl.get(`${modelPrompt}.title.baseInfo`).d('不合格登记')}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.HEAD`,
                },
                <Form columns={3} disabled={!canEdit || ncReportStatus !== 'NEW'} labelWidth={112}>
                  <Lov
                    name="siteLov"
                    onChange={(_, oldValue) => handleChangeDetail(oldValue, 'siteLov')}
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.site`).d('站点')}
                  />
                  <TextField
                    name="ncReportNum"
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.ncReportNum`).d('不良记录单编码')}
                  />
                  <Select
                    name="ncReportStatus"
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.ncReportStatus`).d('不良记录单状态')}
                  />
                  <TextField
                    name="sourceDocNum"
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.sourceDocNum`).d('来源检验单')}
                  />
                  <Select
                    name="inspectBusinessType"
                    onChange={value => handleChangeBusRule(value)}
                    noCache
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.inspectBusinessType`).d('检验业务类型')}
                  />
                  <Select name="flowType" dataSet={detailDs} label={intl.get(`${modelPrompt}.flowType`).d('流程类型')} />
                  <Lov name="noticeLov" dataSet={flexDs} label="发现人" newLine />
                  <Lov name="areaObject" dataSet={flexDs} label="发现区域" />
                  <DateTimePicker name="ncStartTime" dataSet={flexDs} label="发现时间" />
                  <TextField name="recorderPersonDesc" dataSet={flexDs} label="登记人" />
                  <Select name="createMethod" dataSet={detailDs} label="创建方式" />
                  <TextField name="creationDate" dataSet={detailDs} label="创建时间" />
                  <C7nFormItemSort
                    name="materialLov"
                    itemWidth={['70%', '30%']}
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.materialLov`).d('物料')}
                  >
                    <Lov
                      name="materialLov"
                      dataSet={detailDs}
                      label={intl.get(`${modelPrompt}.form.materialLov`).d('物料')}
                      onChange={(_, oldVal) => handleChangeDetail(oldVal, 'materialLov')}
                    />
                    <Select
                      name="revisionCode"
                      dataSet={detailDs}
                      onChange={(_, oldVal) => handleChangeDetail(oldVal, 'revisionCode')}
                    />
                  </C7nFormItemSort>
                  <TextField
                    name="materialName"
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.materialName`).d('物料描述')}
                  />
                  <Lov
                    name="supplierObject"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.supplierObject`).d('供应商编码')}
                  />
                  <Lov
                    name="locatorLov"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.locatorLov`).d('库位编码')}
                  />
                  <TextField
                    name="locatorName"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.locatorName`).d('库位名称')}
                  />
                  <TextField
                    name="supplierName"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.supplierName`).d('供应商名称')}
                  />
                  <Lov
                    name="prodLineObject"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.prodLineObject`).d('产线编码')}
                  />
                  <TextField
                    name="productionLineName"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.productionLineName`).d('产线名称')}
                  />
                  <Select
                    name="stage"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.stage`).d('阶段')}
                  />
                  <Lov
                    name="operationObject"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.operationObject`).d('不良工序编码')}
                  />
                  <TextField
                    name="operationDesc"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.operationDesc`).d('不良工序描述')}
                  />
                  <Select
                    name="ncType"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.ncType`).d('不良分类')}
                  />
                  <Select
                    name="quantityCharacter"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.quantityCharacter`).d('数量特性')}
                  />
                  <NumberField
                    name="ngQty"
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.ngQty`).d('不合格数量')}
                  />
                  <TextField
                    name="uomCode"
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.uomCode`).d('单位')}
                  />
                  <Select
                    name="fristNcLevel"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.fristNcLevel`).d('不良等级初判')}
                  />
                  <Select
                    name="disposalType"
                    dataSet={detailDs}
                    label={intl.get(`${modelPrompt}.form.disposalType`).d('处置类型')}
                    onChange={value => handleChangeDisposalType(value, createMethod)}
                    onOption={({ record }) => {
                      const filterLine = ncRecordLineDs.filter(
                        _record => _record.get('ncRecordType') === 'EO_MATERIAL_NC',
                      );
                      return {
                        disabled: record.get('value') === 'ALL' && !!filterLine?.length,
                      };
                    }}
                  />
                  <Select
                    name="traceFlag"
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.traceFlag`).d('是否发起追溯')}
                  />
                  <TextField
                    name="traceInfo"
                    colSpan={3}
                    rowSpan={2}
                    newLine
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.traceInfo`).d('追溯备注')}
                  />
                  <TextField
                    name="ncRerortDescription"
                    colSpan={3}
                    rowSpan={2}
                    newLine
                    dataSet={flexDs}
                    label={intl.get(`${modelPrompt}.form.ncRerortDescription`).d('不良详细描述')}
                  />
                </Form>,
              )}
            </Panel>
            <Panel
              key="ncRecordLine"
              header={intl.get(`${modelPrompt}.title.ncRecordLine`).d('不良明细')}
            >
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.LINE`,
                },
                <Table
                  dataSet={ncRecordLineDs}
                  columns={ncRecordLineColumns}
                  customizedCode="ncReportDocMaintain-detailLine"
                  filter={record => {
                    if (createMethod === 'QMS') {
                      return disposalType === 'ALL'
                        ? record.get('ncObjectType') === 'INSPECT_DOC'
                        : record.get('ncObjectType') !== 'INSPECT_DOC';
                    }
                    return true;
                  }}
                />,
              )}
            </Panel>
            <Panel
              key="ncDetermine"
              header={intl.get(`${modelPrompt}.title.ncDetermine`).d('不合格判定')}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.HEAD`,
                },
                <Form
                  dataSet={flexDs}
                  columns={3}
                  disabled={
                    !canEdit
                    || (flowType === 'OLD' && ncReportStatus !== 'NG_JUDGE')
                    || (flowType === 'NEW' && ncReportStatus !== 'NG_DISPOSAL')
                  }
                  labelWidth={112}
                >
                  <Select name="finalNcLevel" />
                  <TextField name="followUpPersonDesc" />
                  <Lov name="responsibleUserObj" onChange={handleChangeUser} />
                  <Lov name="unitObj" />
                  <Select name="incomingReasonFlag" noCache onChange={handleChangeIncomingFlag} />
                  <Lov name="ncReasonLov" />
                  <TextField name="reasonMaterialName" />
                  <NumberField name="reasonBarcodeQty" />
                  <TextField name="reasonBarcodeUomCode" />
                  <Lov name="reasonSupplierObj" />
                  <TextField name="reasonSupplierName" />
                </Form>,
              )}
              <Form
                dataSet={flexDs}
                columns={3}
                disabled={
                  !canEdit
                  || (flowType === 'OLD' && ncReportStatus !== 'NG_REASON')
                  || (flowType === 'NEW' && ncReportStatus !== 'NEW')
                }
                labelWidth={112}
              >
                <TextField
                  name="ncReason"
                  colSpan={3}
                  rowSpan={2}
                  newLine
                />
              </Form>
            </Panel>
            {disposalType === 'ALL' && (
              <Panel key="ALL" header={intl.get(`${modelPrompt}.title.allDisposal`).d('整体处置')}>
                <Form
                  dataSet={flexDs}
                  columns={3}
                  disabled={!canEdit || ncReportStatus !== 'NG_DISPOSAL'}
                >
                  <TextField name="disposalBasis" colSpan={2} />
                  <Attachment {...attachmentProps} />
                  <Select name="reviewFlag" />
                  <Select name="customerFlag" />
                  <Select name="materialProductStyle" onChange={(value) => handleChangeStyle(value, false)} />
                </Form>
                <Form
                  dataSet={disposalDs}
                  columns={3}
                  disabled={!canEdit || ncReportStatus !== 'NG_DISPOSAL'}
                >
                  <Lov name="dispositionFunctionLov" onChange={handleChangeFunction} />
                  <TextField name="functionTypeDesc" />
                  {dispositionFunction === 'CONCESSION_INTERCEPTION' && (
                    <Lov name="interceptOperationLov" />
                  )}
                  {dispositionFunction === 'REWORK_SOURCE' && <Lov name="reworkOperationLov" />}
                  {dispositionFunction === 'DEGRADE' && <Select name="degradeLevel" />}
                  <TextField name="remark" />
                  <Lov name="disposalUserLov" />
                  <DateTimePicker name="disposalTime" />
                  {dispositionFunction === 'OVER_TIME_RELEASE' && (
                    <DateTimePicker name="overTime" />
                  )}
                  <TextField name="firstDisposalResultDesc" disabled />
                  {dispositionFunction === 'SCRAP' && <Lov name="relatedUnitLov" />}
                </Form>
              </Panel>
            )}
            {disposalType === 'PART' && (
              <Panel
                key="PART"
                header={intl.get(`${modelPrompt}.title.partDisposal`).d('对象处置')}
              >
                <Form
                  dataSet={flexDs}
                  columns={3}
                  disabled={!canEdit || ncReportStatus !== 'NG_DISPOSAL'}
                  labelWidth={112}
                >
                  <TextField name="disposalBasis" colSpan={2} />
                  <Attachment {...attachmentProps} />
                  <Select name="reviewFlag" />
                  <Select name="customerFlag" />
                  <Select name="materialProductStyle" onChange={(value) => handleChangeStyle(value, false)} />
                </Form>
                <Table
                  buttons={[<RenderBatchEditButton dataSet={disposalDs} canEdit={canEdit} />]}
                  dataSet={disposalDs}
                  columns={partDisposalColumns}
                  customizedCode="ncReportDocMaintain-partDisposal"
                />
              </Panel>
            )}
            <Panel
              key="judgmentBasis"
              header={intl.get(`${modelPrompt}.title.judgmentBasis`).d('判断依据')}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.HEAD`,
                },
                <Form
                  dataSet={flexDs}
                  columns={3}
                  disabled={!canEdit || ncReportStatus !== 'NG_DISPOSAL'}
                  labelWidth={112}
                >
                  <TextField name="operationResult" />
                  <TextField name="operationOpinion" />
                  <TextField name="manufactureResult" />
                  <TextField name="manufactureOpinion" />
                  <TextField name="qualityResult" />
                  <TextField name="qualityOpinion" />
                </Form>,
              )}
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.DETAIL`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.ATTR1`,
      `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.ATTR2`,
    ],
  })(NcReportDetail as any),
);
