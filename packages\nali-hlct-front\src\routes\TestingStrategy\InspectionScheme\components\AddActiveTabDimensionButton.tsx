/**
 * @Description: 检验方案维护-按钮组件
 * @Author: <<EMAIL>>
 * @Date: 2023-01-18 10:18:05
 * @LastEditTime: 2023-03-09 15:42:31
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { DataSet, Button } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.inspectionScheme';

const AddActiveTabDimensionButton = observer(
  ({
    ds,
    addInspectionDimension,
    copyBusinessType,
    batchAddInspectionDimension,
    activeKey,
    canEdit,
    inspectSchemeLines,
  }: {
    ds: DataSet;
    addInspectionDimension;
    copyBusinessType;
    batchAddInspectionDimension;
    activeKey;
    canEdit;
    inspectSchemeLines;
  }) => {
    // 判断当前tab是否选中 检验类型
    const hasInspectBusinessType = () => {
      let flag = true;
      inspectSchemeLines.forEach(item => {
        if (activeKey === item.key) {
          item.inspectionItemBasisDs.forEach(record => {
            if (record?.get('inspectBusinessType')) {
              flag = false;
            }
          });
        }
      });

      return flag;
    };

    // 判断是否有tab选中了检验类型
    const inspectBusinessTypeSum = () => {
      let flag = false;
      inspectSchemeLines.forEach(item => {
        item.inspectionItemBasisDs.forEach(record => {
          if (record?.get('inspectBusinessType')) {
            flag = true;
          }
        });
      });

      return !flag;
    };
    return (
      <>
        {' '}
        <Button
          icon="copy"
          onClick={() => {
            copyBusinessType();
          }}
          disabled={
            inspectSchemeLines.length === 0 ||
            activeKey === '' ||
            !canEdit ||
            inspectBusinessTypeSum()
          }
        >
          {intl?.get(`${modelPrompt}.copyBusinessType`).d('复制检验业务类型')}
        </Button>
        <Button
          icon="add"
          onClick={() => {
            batchAddInspectionDimension();
          }}
          disabled={
            inspectSchemeLines.length === 0 ||
            activeKey === '' ||
            !canEdit ||
            !(
              ['MATERIAL_CATEGORY', 'MATERIAL', 'OPERATION'].includes(
                ds.current?.get('inspectSchemeObjectType'),
              ) && !!ds.current?.get('inspectSchemeObjectId')
            ) ||
            hasInspectBusinessType()
          }
        >
          {intl?.get(`${modelPrompt}.batchAddInspectionDimension`).d('批量新增检验项目')}
        </Button>
        <Button
          icon="add"
          onClick={() => {
            addInspectionDimension('new');
          }}
          disabled={
            inspectSchemeLines.length === 0 ||
            activeKey === '' ||
            !canEdit ||
            !(
              ['MATERIAL_CATEGORY', 'MATERIAL', 'OPERATION'].includes(
                ds.current?.get('inspectSchemeObjectType'),
              ) && !!ds.current?.get('inspectSchemeObjectId')
            ) ||
            hasInspectBusinessType()
          }
        >
          {intl?.get(`${modelPrompt}.addInspectionDimension`).d('新增检验维度')}
        </Button>
      </>
    );
  },
);

export default AddActiveTabDimensionButton;
