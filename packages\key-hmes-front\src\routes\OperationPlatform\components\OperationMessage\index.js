/**
 * @Description: 操作信息卡片
 * @Author: <<EMAIL>>
 * @Date: 2023-07-11 09:39:58
 * @LastEditTime: 2023-07-11 14:02:06
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useRef, useCallback } from 'react';
import { Row, Col, Tooltip } from 'choerodon-ui/pro';
import { Timeline } from 'choerodon-ui';
import { debounce } from 'lodash';
import { Result } from 'choerodon-ui';
import moment from 'moment';
import formatterCollections from 'utils/intl/formatterCollections';
import {  useOperationPlatform } from '../../contextsStore';
import { CardLayout } from '../commonComponents';
import speechSynthesis from '@/assets/operationPlatformCard/speechSynthesis.png';
import speechSynthesisnone from '@/assets/operationPlatformCard/speechSynthesisnone.png';
import retract from '@/assets/operationPlatformCard/retract.svg';
import styles from './index.modules.less';

const DataAcquisition = (props) => {
  const synth = window.speechSynthesis;
  const message = new SpeechSynthesisUtterance();
  const { operationRecords, enterInfo, synthRead, dispatch } = useOperationPlatform();
  const tileLine = useRef(null);
  // console.log('operationRecords', operationRecords);
  useEffect(() => {
    document.querySelector('#OperationRecordsTimelineScor').scrollTop = 0;
  }, [operationRecords]);

  const voicePlaybackSuccess = useCallback(
    debounce((text) => {
      message.text = text;
      message.lang = 'zh';
      message.rate = 1; // 语速设置，数字越大越快
      synth.speak(message);
    }, 500),
    [],
  )

  const voicePlaybackFailed = useCallback(
    debounce((text) => {
      message.text = text;
      message.lang = 'zh';
      message.rate = 1; // 语速设置，数字越大越快
      synth.speak(message);
    }, 200),
    [],
  )

  useEffect(() => {
    if(!synthRead){
      return;
    }
    if (!operationRecords[operationRecords.length-1]?.messageType) {
      return;
    }
    if (operationRecords[operationRecords.length-1]?.messageType === 'SUCCESS') {
      voicePlaybackSuccess(`操作成功`);
    } else {
      voicePlaybackFailed(`操作失败`);
    }
  }, [operationRecords, synthRead])

  const handleCahngeStatus = () => {
    dispatch({
      type: 'update',
      payload: {
        synthRead: !synthRead,
      },
    })
  }

  const hanldeExpande = (id) => {
    const timelineDom = document.querySelector(`#${id}`).style.display;
    if(timelineDom === 'block' || !timelineDom){
      document.querySelector(`#${id}`).style.display = 'none';
    }else{
      document.querySelector(`#${id}`).style.display = 'block';
    }
  }

  const handleJumpEventQuery = (endTime) => {
    const currentYear = moment().year();
    const newStr = currentYear + endTime.substring(2);
    const startTime = moment(new Date(newStr)).subtract(3, 'second').format('YYYY-MM-DD HH:mm:ss');
    props.history.push({
      pathname: `/hmes/event/query`,
      state: {
        endTime,
        startTime,
      }
    });
  }

  return (
    <CardLayout.Layout>
      <CardLayout.Header
        className='OperationRecordsHead'
        title='操作信息'
        addonAfter={
          <div style={{cursor: 'pointer'}} onClick={handleCahngeStatus}>
            {
              synthRead === true && <img src={speechSynthesis} alt='' />
            }
            {
              synthRead === false && <img src={speechSynthesisnone} alt='' />
            }
          </div>
        }
      />
      <CardLayout.Content className='OperationRecordsForm' style={{overflow: 'hidden'}}>
        <div className={styles.operationMessageTitleRight} onClick={() => {hanldeExpande(`${styles.MessagePromptResultContent}`)}} style={{display: operationRecords.length > 0 ? 'flex' : 'none'}}>
          {/* <span>收起</span> */}
          <img src={retract} alt='' />
        </div>
        <div id={styles.MessagePromptResultContent} className={styles.MessagePromptResult}>
          {
            operationRecords[operationRecords.length-1]?.messageType === 'SUCCESS' && (
              <Result
                className={styles.MessagePromptResultContentSuccess}
                status='success'
                title='操作成功'
                // subTitle={operationRecords[operationRecords.length-1].message}
              />
            )
          }
          {
            operationRecords[operationRecords.length-1]?.messageType === 'FAIL' && (
              <Result
                className={styles.MessagePromptResultContentError}
                status='error'
                title='操作失败'
                // subTitle={operationRecords[operationRecords.length-1].message}
              />
            )
          }
        </div>
        <div className={styles.operationMessageTitle} style={{display: operationRecords.length > 0 ? 'flex' : 'none'}}>
          <div>操作日志：</div>
          <div className={styles.operationMessageTitleRight} onClick={() => {hanldeExpande('OperationRecordsTimelineScor')}}>
            {/* <span>收起</span> */}
            <img src={retract} alt='' />
          </div>
        </div>
        <Timeline
          id='OperationRecordsTimelineScor'
          ref={tileLine}
          className={styles.OperationRecordsTimeline}
          reverse
        >
          {
            operationRecords.map(item => {
              return (
                <Timeline.Item key={item.uuid} color={item.messageType === 'SUCCESS' ? 'green' : 'red'}>
                  <Row>
                    <Col span={6}>
                      <Tooltip placement="topLeft" title={item.creationDate}>
                        <span style={{textDecoration: 'underline'}} onClick={() => {handleJumpEventQuery(item.creationDate)}}>{item.creationDate}</span>
                      </Tooltip>
                    </Col>
                    <Col span={4}>
                      <Tooltip placement="topLeft" title={item.creationDate}>
                        {enterInfo.userName}
                      </Tooltip>
                    </Col>
                    <Col span={14}>
                      <Tooltip placement="topLeft" title={item.cardName}>
                        {item.cardName}-
                        <span style={{fontWeight: 700}}>{item.message.slice(0, item.message.length-2)}</span>
                        {
                          item.messageType === 'SUCCESS' && (
                            <span style={{color: 'rgba(100, 222, 163, 1)', fontWeight: 700}}>成功</span>
                          )
                        }
                        {
                          item.messageType === 'FAIL' && (
                            <span style={{color: 'rgba(255, 77, 79, 1)', fontWeight: 700}}>失败</span>
                          )
                        }
                      </Tooltip>
                    </Col>
                  </Row>
                </Timeline.Item>
              )
            })
          }
        </Timeline>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(DataAcquisition);
