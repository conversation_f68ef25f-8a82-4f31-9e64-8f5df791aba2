/**
 * @Description: 事件类型维护 DS
 * @Author: <<EMAIL>>
 * @Date: 2022-11-03 09:36:07
 * @LastEditTime: 2023-07-19 14:28:05
 * @LastEditors: <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.event.type.model.type';

const tableDS = (): DataSetProps => ({
  autoQuery: true,
  autoCreate: true,
  pageSize: 10,
  selection: false,
  primaryKey: 'eventTypeId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-type/property/list/ui`,
        method: 'GET',
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.mes.domain.entity.MtEventType';
      return {
        data: { eventTypeId: record.get('eventTypeId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_SAMPLING}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  queryFields: [
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('事件类型描述'),
    },
  ],
  fields: [
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
      required: true,
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.description`).d('事件类型描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'defaultEventTypeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defaultEventTypeFlag`).d('初始化'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.INITIAL_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'operator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operator`).d('事件影响对象类型'),
    },
  ],
});

const tableDrawerDS = (eventTypeId): DataSetProps => ({
  autoQuery: true,
  autoCreate: true,
  pageSize: 10,
  selection: false,
  primaryKey: 'relId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-object-type-rel/limit-id/property/list/ui`,
        params: { eventTypeId },
        method: 'GET',
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.mes.domain.entity.MtEventObjectColumn';
      return {
        data: { objectColumnId: record.get('objectColumnId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_SAMPLING}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'objectTypeObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.objectTypeCode`).d('对象类型编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.QMS.OBJECT_TYPE`,
      required: true,
      lovPara: {
        tenantId,
        eventId: eventTypeId,
      },
    },
    {
      name: 'objectTypeId',
      bind: 'objectTypeObject.objectTypeId',
    },
    {
      name: 'objectTypeCode',
      bind: 'objectTypeObject.objectTypeCode',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectDescription`).d('对象类型描述'),
      disabled: true,
      bind: 'objectTypeObject.description',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'operator',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.action').d('操作'),
    },
  ],
});

export { tableDS, tableDrawerDS };
