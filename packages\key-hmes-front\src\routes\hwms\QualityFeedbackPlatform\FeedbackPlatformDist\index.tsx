/*
 * @Description: 质量反馈单-详情页
 * @Author: <<EMAIL>>
 * @Date: 2023-09-13 14:14:50
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-11-01 11:03:39
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  NumberField,
  TextField,
  Select,
  DateTimePicker,
  TextArea,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { getCurrentUser } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import DetailPageTable from './DetailPageTable';
import { detailDS, detailLineDS } from '../stores/DetailDS';
import { SaveFeedbackDoc, QueryMaterialLotInfo } from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.qualityFeedbackPlatform';
const { id, loginName } = getCurrentUser();

const FeedbackPlatformDetail = props => {
  const {
    history,
    match: { params },
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState(false);
  const [adminUserList, setAdminUserList] = useState<string[] | null>(null);
  const [disableFlag, setDisableFlag] = useState<boolean>(kid !== 'create');
  const detailLineDs = useMemo(() => new DataSet(detailLineDS()), []);
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const { run: saveFeedbackDoc, loading: saveLoading } = useRequest(SaveFeedbackDoc(), {
    manual: true,
    needPromise: true,
  });
  // 查询物料批信息
  const { run: queryMaterialLotInfo, loading: queryLoading } = useRequest(QueryMaterialLotInfo(), {
    manual: true,
  });
  const { run: queryAdminList, loading: queryAdminLoading } = useRequest(
    { lovCode: 'YP.QIS.AFTER_SALE_ADMIN_USER' },
    { manual: true },
  );

  useEffect(() => {
    if (kid === 'create') {
      // 新建时
      setCanEdit(true);
      return;
    }
    // 编辑时
    handleInitDetail();
  }, [kid]);

  const handleInitDetail = (feedbackId = kid) => {
    detailDs.setQueryParameter('feedbackId', feedbackId);
    detailDs.query().then(res => {
      const { createdBy, siteId } = res?.rows || {};
      if (!adminUserList) {
        // 只有单据创建人，或者值集 YP.QIS.AFTER_SALE_ADMIN_USER中值是QA_FEEDBACK的记录行的 标记列 的子账户（loginName）的人员可以点击编辑按钮
        queryAdminList({
          onSuccess: adminRes => {
            const _feedbackAdmin: string[] =
              (adminRes || []).find(item => item.value === 'QA_FEEDBACK')?.tag?.split(',') || [];
            setAdminUserList(_feedbackAdmin);
            const _disabledFlag = createdBy !== id && !_feedbackAdmin.includes(loginName);
            setDisableFlag(_disabledFlag);
          },
        });
      } else {
        const _disabledFlag = createdBy !== id && !adminUserList.includes(loginName);
        setDisableFlag(_disabledFlag);
      }
      detailLineDs.setQueryParameter('feedbackId', feedbackId);
      detailLineDs.query().then(() => {
        detailLineDs.setState('siteId', siteId);
      });
    });
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(() => {
    if (kid === 'create') {
      history.push('/hwms/market-quality/quality-feedback-platform/list');
    } else {
      setCanEdit(false);
      handleInitDetail();
    }
  }, []);

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const detailData = detailDs!.current!.toData();
    const lineData = detailLineDs!.toData();
    const res = await saveFeedbackDoc({
      params: {
        ...detailData,
        qisQaFeedbackItemList: lineData,
      },
    });
    if (res && res.success) {
      notification.success({});
      setCanEdit(false);
      if (kid === 'create') {
        history.push(`/hwms/market-quality/quality-feedback-platform/dist/${res.rows.feedbackId}`);
      } else {
        handleInitDetail();
      }
      return true;
    }
    return false;
  };

  const handleChangeSite = val => {
    detailLineDs.setState('siteId', val?.siteId || null);
  };

  const handleChangeMaterialLot = value => {
    if (value?.materialId) {
      queryMaterialLotInfo({
        params: value?.materialId,
        onSuccess: res => {
          // const { batteryMatLotProductType } = res;
          detailDs.current?.set('batteryMatLotProductType', res);
        },
      });
    } else {
      detailDs.current?.set('batteryMatLotProductType', undefined);
    }
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || queryAdminLoading || queryLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('质量反馈单详情')}
          backPath="/hwms/market-quality/quality-feedback-platform/list"
        >
          {canEdit && !disableFlag && (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && !disableFlag && (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              // permissionList={[
              //   {
              //     code: `${path}.button.edit`,
              //     type: 'button',
              //     meaning: '详情页-编辑新建删除复制按钮',
              //   },
              // ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={['sourceInfo', 'basicInfo', 'feedbackItemInfo']}
          >
            <Panel
              key="sourceInfo"
              header={intl.get(`${modelPrompt}.title.sourceInfo`).d('来源信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="feedbackNum" />
                <Lov name="siteLov" onChange={handleChangeSite} />
                <TextArea name="theme" rowSpan={2} autoSize={{ minRows: 3 }} />
                <TextField name="warnNum" />
                <TextField name="serviceNum" />
                <TextField name="claimNum" newLine />
                <TextField name="dataSource" />
              </Form>
            </Panel>
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.basicInfo`).d('基本信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <Lov name="batteryMatLotLov" onChange={handleChangeMaterialLot} />
                <TextField name="batteryMatLotProductType" />
                <Lov name="hostPlantLov" />
                <TextField name="vinNum" />
                <Select name="vehicleModel" searchable searchFieldInPopup />
                <DateTimePicker name="productionDate" />
                <DateTimePicker name="soldDate" />
                <DateTimePicker name="faultDate" />
                <DateTimePicker name="maintainDate" />
                <NumberField name="faultMileage" />
                <TextField name="serviceSitusNum" />
                <TextField name="serviceSitusName" />
                <TextArea name="faultDec" autoSize={{ minRows: 1, maxRows: 8 }} />
                <TextArea name="faultReason" autoSize={{ minRows: 1, maxRows: 8 }} />
                <TextArea name="faultMeasure" autoSize={{ minRows: 1, maxRows: 8 }} />
                <Select name="faultLevel" />
                <DateTimePicker name="creationDate" />
                <Lov name="createdLov" />
              </Form>
            </Panel>
            <Panel
              key="feedbackItemInfo"
              header={intl.get(`${modelPrompt}.title.feedbackItemInfo`).d('零部件信息')}
            >
              <DetailPageTable ds={detailLineDs} canEdit={canEdit} />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(FeedbackPlatformDetail);
