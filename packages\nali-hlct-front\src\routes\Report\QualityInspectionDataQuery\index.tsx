/**
 * @Description: 质量检验数据查询报表
 */

import React, { useMemo, useState } from 'react';
import { useDataSetEvent } from 'utils/hooks';
import { observer } from 'mobx-react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { isNil } from 'lodash';
import request from 'hzero-front/lib/utils/request';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC } from '@utils/config';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useRequest } from '@components/tarzan-hooks';
import {
  qualityInspectionDataTableDS,
} from './stories';

const modelPrompt = 'tarzan.hmes.QualityInspectionDataQuery';
const tenantId = getCurrentOrganizationId();

const QualityInspectionDataQuery = observer(props => {
  const {
    qualityInspectionDataTableDs,
  } = props;

  const { run: getMixId } = useRequest(
    {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/report/query/inspectDoc`,
      method: 'GET',
    },
    {
      manual: true,
      showNotification: false,
      needPromise: true,
    },
  );

  const [qualityInspectionDataColumns, setQualityInspectionDataColumns] = useState<any>([]);

  const columnsOutput: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'sequence',
        width: 90,
        renderer: ({ record }: any) => {
          return record.index + 1;
        },
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'inspectInfoCreationDate',
        width: 150,
      },
      {
        name: 'actualEndTime',
        width: 150,
      },
      {
        name: 'ncReportLastUpdateDate',
        width: 150,
      },
      {
        name: 'inspectBusinessType',
        width: 150,
      },
      {
        name: 'workcellName',
        width: 150,
      },
      {
        name: 'equipmentName',
        width: 150,
      },
      {
        name: 'inspectDocNum',
        width: 200,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                props.history.push(`/hwms/inspect-doc-maintain/dist/${record?.get('inspectDocId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'model',
        width: 150,
      },
      {
        name: 'baseMaterialCode',
        width: 150,
      },
      {
        name: 'mixMaterialLotCode',
        width: 150,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => handelJump(value, record)}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'objectCode',
        width: 200,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                props.history.push(`/hwms/inspect-doc-maintain/dist/${record?.get('inspectDocId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'lastInspectResult',
        width: 150,
      },
      {
        name: 'firstInspectorName',
        width: 150,
      },
      {
        name: 'reviewUserName',
        width: 150,
      },
      {
        name: 'description',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
    ];
  }, []);

  useDataSetEvent(qualityInspectionDataTableDs, 'load', async () => {
    const allThisPageDate = await qualityInspectionDataTableDs?.toData()[0]?.dynamicInspectItemList;
    const dateColumns: any = [];
    (allThisPageDate || []).forEach(item => {
      dateColumns.push(
        {
          title: item.inspectItemName,
          renderer: ({ record }) => {
            const showValue = record.get('dynamicInspectItemList').filter((ele) => ele.inspectItemId === item.inspectItemId)[0];
            return (
              <>
                <span>{showValue?.inspectValue || null}</span>
              </>
            );
          },
        },
      );
      setQualityInspectionDataColumns([...dateColumns]);
    });
  });

  const handelJump = (value, record) => {
    getMixId({
      params: {
        finishTime: record.get('ncReportLastUpdateDate'),
        code: value,
      },
    }).then((res) => {
      if (res.rows && res.success) {
        props.history.push(`/hwms/inspect-doc-maintain/dist/${res.rows.inspectDocId}`);
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  const onHandleExport = async () => {
    const flag = await qualityInspectionDataTableDs.queryDataSet.current.validate(false, true);
    if (!flag) {
      return;
    }
    const queryParmas = qualityInspectionDataTableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    delete queryParmas.siteLov;
    delete queryParmas.materialLov;
    delete queryParmas.equipmentLov;
    delete queryParmas.processWorkcellLov;
    const res = await request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/inspect/report/export/ui`,
      {
        method: 'POST',
        body: queryParmas,
        responseType: 'blob',
      },
    );
    if (res.type === 'application/json') {
      const file = new FileReader();
      file.readAsText(res, 'utf-8');
      file.onload = () => {
        if (typeof file.result === 'string') {
          const message = JSON.parse(file.result);
          return notification.error({ message: message.message });
        }
      };
    } else {
      const elink = document.createElement('a');
      elink.download = `质量检验数据查询报表.xlsx`;
      elink.style.display = 'none';
      const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click();
      document.body.removeChild(elink);
    }
  };


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('质量检验数据查询报表')} >
        <Button color={ButtonColor.primary} onClick={onHandleExport} >
          {intl.get(`${modelPrompt}.export`).d('导出')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            queryFieldsLimit: 8,
          }}
          dataSet={qualityInspectionDataTableDs}
          columns={[...columnsOutput, ...qualityInspectionDataColumns]}
          searchCode="QUALITYINSPECTIONDATATABLE"
          customizedCode="QUALITYINSPECTIONDATATABLE"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.QualityInspectionDataQuery', 'tarzan.common'],
})(
  withProps(
    () => {
      const qualityInspectionDataTableDs = new DataSet({
        ...qualityInspectionDataTableDS(),
      });

      return {
        qualityInspectionDataTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(QualityInspectionDataQuery),
);
