/**
 * @Description: 一次解析责任判定-主界面
 */
import React, { FC, useMemo, useCallback } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { Collapse } from 'choerodon-ui';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import QuestionButton from '../components/QuestionButton';
import { headDS, lineDS } from '../stores';

const modelPrompt = 'tarzan.hwms.LiabilityJudgment';
const { Panel } = Collapse;

interface TaskListProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
}

const InspectionSchemeList: FC<TaskListProps> = props => {
  const { headDs, lineDs, history } = props;
  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('prianalResjudgId'));
    } else {
      queryLineTable(null);
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = prianalResjudgId => {
    lineDs.loadData([]);
    if (prianalResjudgId) {
      lineDs.setQueryParameter('prianalResjudgId', prianalResjudgId);
      lineDs.query();
    }
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'prianalResjudgCode',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/market-quality/liability-judgment/${record!.get(
                    'prianalResjudgId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'siteName', width: 160 },
      { name: 'feedbackNum', width: 180 },
      { name: 'theme' },
      { name: 'batteryMatLotCode' },
      { name: 'batteryMatLotModel' },
      { name: 'qualityProblemFlay', width: 120 },
      { name: 'severityLevel' },
      { name: 'analResDivision' },
      { name: 'accidentalTag' },
      { name: 'frequency' },
      { name: 'responsibleDeptName' },
      { name: 'responsibleEmName' },
      { name: 'reasonClassify' },
      { name: 'reason' },
      { name: 'reasonCode' },
      {
        name: 'bResponsibilityRatio',
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return `${value}%`;
        },
      },
      {
        name: 'aResponsibilityRatio',
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return `${value}%`;
        },
      },
      { name: 'creationDate', align: ColumnAlign.center, width: 150 },
      { name: 'lastUpdateDate', align: ColumnAlign.center, width: 150 },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'prianalResjudgItemNum', lock: ColumnLock.left },
      { name: 'partsName' },
      { name: 'partsCode', align: ColumnAlign.center, width: 150 },
      {
        name: 'ypPartsCode',
        align: ColumnAlign.center,
        width: 100,
      },
      { name: 'ypPartsName' },
      { name: 'primaryUnitFlag' },
      { name: 'majorFaultMode' },
      { name: 'majorDivision1' },
      { name: 'majorDivision2'},
      { name: 'majorDivision3' },
      { name: 'description', width: 150 },
      { name: 'ware' },
      { name: 'softwareVersion' },
      { name: 'problemCreateFlag', width: 150 },
      {
        name: 'problemCode',
        width: 120,
        renderer: ({ value, record }) => {
          if (!value) {
            return;
          }
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/problem-management/problem-management-platform/dist/${record!.get(
                    'problemId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
    ],
    [],
  );

  const handleAdd = useCallback(() => {
    history.push(`/hwms/market-quality/liability-judgment/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('一次解析责任判定')}>
        <Button color={ButtonColor.primary} icon="add" onClick={handleAdd}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <QuestionButton lineDs={lineDs} headDs={headDs} history={history} />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headDs}
          columns={headColumns}
          searchCode="InspectionScheme1"
          customizedCode="InspectionScheme-listHeader"
          onRow={({ record }) => ({
            onClick: () => queryLineTable(record?.get('prianalResjudgId')),
          })}
        />
        <Collapse bordered={false} defaultActiveKey={['line']}>
          <Panel key="line" header={intl.get(`${modelPrompt}.title.partsInfo`).d('零部件信息')}>
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="InspectionScheme-listLine"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(InspectionSchemeList as any),
);
