/**
 * @Description: 检验方案模板-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-02-15 10:49:38
 * @LastEditTime: 2023-04-03 16:22:36
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSet } from 'choerodon-ui/pro';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import uuid from 'uuid/v4';

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.inspectionSchemeTemplate';
const tenantId = getCurrentOrganizationId();

// 列表-ds
const listTableDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: false,
  cacheSelection: true,
  primaryKey: 'inspectSchemeTmpId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-scheme-tmp/page/ui`,
        method: 'post',
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSchemeTmpCode`).d('检验方案模板编码'),
      name: 'inspectSchemeTmpCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      bind: 'siteObject.siteId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeTmpObjectType`).d('检验模板对象类型'),
      name: 'inspectSchemeObjectTypeObject',
      lookupCode: 'MT.QMS.USER_INSPECT_OBJECT_TYPE_TMP',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'inspectSchemeObjectType',
      bind: 'inspectSchemeObjectTypeObject.value',
    },
    {
      name: 'inspectSchemeObjectTypeDesc',
      bind: 'inspectSchemeObjectTypeObject.meaning',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      // lovPara: {
      //   tenantId,
      // },
      computedProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
      multiple: true,
    },
    {
      name: 'inspectBusinessTypes',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      name: 'inspectSchemeTmpCode',
      label: intl.get(`${modelPrompt}.inspectSchemeTmpCode`).d('检验方案模板编码'),
    },
    {
      type: FieldType.string,
      name: 'siteName',
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSchemeTmpObjectType`).d('检验模板对象类型'),
      name: 'inspectSchemeObjectTypeDesc',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeDesc',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      name: 'remark',
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => record.get('status') === 'UNPUBLISHED',
    },
  },
});

// 详情-检验方案信息ds
const detailFormDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.string,
      name: 'inspectSchemeTmpCode',
      label: intl.get(`${modelPrompt}.inspectSchemeTmpCode`).d('检验方案模板编码'),
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteObject.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteObject.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteObject.siteName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeObjectType`).d('检验对象类型'),
      name: 'inspectSchemeObjectTypeObject',
      lookupCode: 'MT.QMS.USER_INSPECT_OBJECT_TYPE_TMP',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'inspectSchemeObjectType',
      bind: 'inspectSchemeObjectTypeObject.value',
    },
    {
      name: 'inspectSchemeObjectTypeDesc',
      bind: 'inspectSchemeObjectTypeObject.meaning',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      // max: 9,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      name: 'remark',
    },
  ],
});

// 详情-检验项目tab的基础信息ds
const inspectionItemBasisDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    { name: 'siteId' },
    { name: 'inspectBusinessTypeObjectIgnore', type: FieldType.string },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          let inspectBusinessTypes = record.get('inspectBusinessTypeObjectIgnore') || null;

          if (inspectBusinessTypes && inspectBusinessTypes !== '') {
            inspectBusinessTypes = (record.get('inspectBusinessTypeObjectIgnore') || '').split(',');
          }
          return {
            tenantId,
            inspectBusinessTypes,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'inspectBusinessType',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'inspectBusinessTypeDesc',
      bind: 'inspectBusinessTypeObject.inspectBusinessTypeDesc',
    },
    {
      name: 'inspectBusinessTypeRuleId',
      bind: 'inspectBusinessTypeObject.inspectBusinessTypeRuleId',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingDimension`).d('抽样维度'),
      name: 'samplingDimension',
      lookupCode: 'MT.QMS.SAMPLING_DIMENSION',
      defaultValue: 'INSPECT_ITEM_SAMPLING',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
      name: 'samplingMethodLov',
      lovCode: 'MT.QMS.SAMPLING_METHOD',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'samplingMethodDesc',
      dynamicProps: {},
    },
    {
      name: 'samplingMethodId',
      bind: 'samplingMethodLov.samplingMethodId',
    },
    {
      name: 'samplingMethodDesc',
      type: FieldType.string,
      bind: 'samplingMethodLov.samplingMethodDesc',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectionDimension`).d('检验维度'),
      name: 'inspectionDimensionObject',
      multiple: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('inspectSchemeObjectType');
        },
      },
    },
    {
      name: 'inspectionDimension',
      bind: 'inspectionDimensionObject.value',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectionItem`).d('检验项目'),
      name: 'inspectionItemObject',
      lovCode: 'MT.QMS.INSPECT_ITEM_INFO',
      multiple: true,
      lovPara: { tenantId, dataType: 'CALCULATE_FORMULA' },
      // @ts-ignore
      noCache: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('inspectionDimensionObject').length === 0;
        },
      },
    },
  ],
});

// 详情-新增检验维度的DS
const dimensionTableDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.object,
      name: 'ruleDtl',
    },
    {
      type: FieldType.string,
      name: 'disabledListWork',
    },
    {
      type: FieldType.string,
      name: 'disabledListMapRelationship',
    },
    {
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
    },

    // 从formDs传入的定值
    {
      type: FieldType.object,
      name: 'siteId',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      name: 'materialCode',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      name: 'materialName',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      name: 'materialId',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      name: 'revisionCode',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.area`).d('区域'),
      name: 'areaObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.AREA',
      textField: 'areaName',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            (record.get('disabledListWork') || '').split(',').indexOf('areaObject') > -1 ||
            (record.get('ruleDtl') || {}).areaFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'areaId',
      bind: 'areaObject.areaId',
    },
    {
      name: 'areaCode',
      bind: 'areaObject.areaCode',
    },
    {
      name: 'areaName',
      bind: 'areaObject.areaName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            (record.get('disabledListWork') || '').split(',').indexOf('prodLineObject') > -1 ||
            (record.get('ruleDtl') || {}).prodLineFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineObject.prodLineId',
    },
    {
      name: 'prodLineCode',
      bind: 'prodLineObject.prodLineCode',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineObject.prodLineName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.process`).d('工序'),
      name: 'processObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.ROUTER_WORKCELL',
      textField: 'workcellName',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            workcellType: 'PROCESS',
          };
        },
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            (record.get('disabledListWork') || '').split(',').indexOf('processObject') > -1 ||
            (record.get('ruleDtl') || {}).processWorkcellFlag !== 'Y'
          );
        },
      },
    },

    {
      name: 'processWorkcellId',
      bind: 'processObject.workcellId',
    },
    {
      name: 'processWorkcellName',
      bind: 'processObject.workcellName',
    },
    {
      name: 'processWorkcellCode',
      bind: 'processObject.workcellCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.station`).d('工位'),
      name: 'stationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.ROUTER_STATION',
      textField: 'workcellName',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            workcellType: 'STATION',
          };
        },
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            (record.get('disabledListWork') || '').split(',').indexOf('stationObject') > -1 ||
            (record.get('ruleDtl') || {}).stationWorkcellFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'stationWorkcellId',
      bind: 'stationObject.workcellId',
    },
    {
      name: 'stationWorkcellCode',
      bind: 'stationObject.workcellCode',
    },
    {
      name: 'stationWorkcellName',
      bind: 'stationObject.workcellName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipment`).d('设备'),
      name: 'equipmentObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentName',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (record.get('ruleDtl') || {}).equipmentFlag !== 'Y';
        },
      },
    },
    {
      name: 'equipmentId',
      bind: 'equipmentObject.equipmentId',
    },
    {
      name: 'equipmentName',
      bind: 'equipmentObject.equipmentName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      name: 'operationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.ROUTER_OPERATION',
      textField: 'description',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (record.get('ruleDtl') || {}).operationFlag !== 'Y';
        },
      },
    },
    {
      name: 'operationId',
      bind: 'operationObject.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationObject.operationName',
    },
    {
      name: 'operationDesc',
      bind: 'operationObject.description',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      name: 'supplierObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      textField: 'supplierName',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            (record.get('disabledListMapRelationship') || '').split(',').indexOf('supplierObject') >
            -1 || (record.get('ruleDtl') || {}).supplierFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierObject.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierObject.supplierName',
    },

    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customer`).d('客户'),
      name: 'customerObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            (record.get('disabledListMapRelationship') || '').split(',').indexOf('customerObject') >
            -1 || (record.get('ruleDtl') || {}).customerFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'customerId',
      bind: 'customerObject.customerId',
    },
    {
      name: 'customerName',
      bind: 'customerObject.customerName',
    },

    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherObject`).d('其他对象'),
      name: 'otherObject',
      dynamicProps: {
        disabled: ({ record }) => {
          return (record.get('ruleDtl') || {}).otherObjectFlag !== 'Y';
        },
      },
    },
  ],
});

const copyDS: () => DataSetProps = () => ({
  forceValidate: true,
  autoCreate: false,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeTmplt`).d('检验方案模板'),
      name: 'inspectSchemeTmpObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_SCHEME_TMP',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'inspectSchemeTmpCode',
      bind: 'inspectSchemeTmpObject.inspectSchemeTmpCode',
    },
    {
      name: 'inspectSchemeTmpId',
      bind: 'inspectSchemeTmpObject.inspectSchemeTmpId',
    },
  ],
});

const instantiationFormDS: () => DataSetProps = () => ({
  forceValidate: true,
  autoCreate: false,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      disabled: true,
    },
    {
      name: 'siteId',
      bind: 'siteObject.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteObject.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteObject.siteName',
    },
    {
      name: 'inspectSchemeObjectType',
    },
    {
      type: FieldType.object,
      name: 'inspectSchemeObject',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        label: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return intl.get(`${modelPrompt}.material`).d('物料');
            case 'WORKCELL':
              return intl.get(`${modelPrompt}.workcell`).d('工位');
            case 'PROCESS_WORKCELL':
              return intl.get(`${modelPrompt}.process`).d('工序');
            case 'PROD_LINE':
              return intl.get(`${modelPrompt}.prodLine`).d('产线');
            case 'AREA':
              return intl.get(`${modelPrompt}.area`).d('区域');
            case 'OPERATION':
              return intl?.get(`${modelPrompt}.operation`).d('工艺');
            default:
              return intl.get(`${modelPrompt}.material`).d('物料');
          }
        },
        lovCode: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'MT.METHOD.MATERIAL_SITE_CATEGORY';
            case 'WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROCESS_WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROD_LINE':
              return 'MT.MODEL.PRODLINE';
            case 'AREA':
              return 'MT.MODEL.AREA';
            case 'OPERATION':
              return 'MT.METHOD.ROUTER_OPERATION';
            default:
              return 'MT.METHOD.MATERIAL_SITE_CATEGORY';
          }
        },
        lovPara: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return {
                tenantId,
                siteId: record.get('siteId'),
                enableFlag: 'Y',
              };
            case 'WORKCELL':
              return {
                tenantId,
                siteId: record.get('siteId'),
                workcellType: 'STATION',
              };
            case 'PROCESS_WORKCELL':
              return {
                tenantId,
                siteId: record.get('siteId'),
                workcellType: 'PROCESS',
              };
            case 'PROD_LINE':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            case 'AREA':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            case 'OPERATION':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            default:
              return {
                tenantId,
                siteId: record.get('siteId'),
                enableFlag: 'Y',
              };
          }
        },
        textField: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'materialName';
            case 'WORKCELL':
              return 'workcellName';
            case 'PROCESS_WORKCELL':
              return 'workcellName';
            case 'PROD_LINE':
              return 'prodLineName';
            case 'AREA':
              return 'areaName';
            case 'OPERATION':
              return 'description'
            default:
              return 'materialName';
          }
        },
        valueField: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'materialId';
            case 'WORKCELL':
              return 'workcellId';
            case 'PROCESS_WORKCELL':
              return 'workcellId';
            case 'PROD_LINE':
              return 'prodLineId';
            case 'AREA':
              return 'areaId';
            case 'OPERATION':
              return 'operationId'
            default:
              return 'materialId';
          }
        },
        disabled: ({ record }) => {
          return !record.get('inspectSchemeObjectType') || !record.get('siteId');
        },
      },
    },
    {
      name: 'inspectSchemeObjectId',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'inspectSchemeObject.materialId';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellId';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellId';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineId';
            case 'AREA':
              return 'inspectSchemeObject.areaId';
            case 'OPERATION':
              return 'inspectSchemeObject.operationId';
            default:
              return 'inspectSchemeObject.materialId';
          }
        },
      },
    },
    {
      name: 'inspectSchemeObjectCode',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'inspectSchemeObject.materialCode';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellCode';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellCode';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineCode';
            case 'AREA':
              return 'inspectSchemeObject.areaCode';
            case 'OPERATION':
              return 'inspectSchemeObject.operationName'
            default:
              return 'inspectSchemeObject.materialCode';
          }
        },
      },
    },
    {
      name: 'inspectSchemeObjectName',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'inspectSchemeObject.materialName';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellName';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellName';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineName';
            case 'AREA':
              return 'inspectSchemeObject.areaName';
            case 'OPERATION':
              return 'inspectSchemeObject.description'
            default:
              return 'inspectSchemeObject.materialName';
          }
        },
      },
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'inspectSchemeObject.revisionFlag',
    },
    {
      // 物料版本
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      name: 'revisionCode',
      textField: 'description',
      valueField: 'description',
      // @ts-ignore
      noCache: true,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: item?.kid ? item.kid : uuid(),
                  description: item?.description ? item?.description : item,
                };
              });
            }
            if (record && firstlyQueryData.length === 1 && !record?.get('revisionCode')) {
              record?.init('revisionCode', firstlyQueryData[0].description);
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        lookupUrl: ({ record }) => {
          if (
            !(record.get('inspectSchemeObjectType') === 'MATERIAL') ||
            !record.get('inspectSchemeObjectId') ||
            !record.get('siteId') ||
            !(record.get('revisionFlag') === 'Y')
          ) {
            return;
          }
          return `${BASIC.TARZAN_METHOD
            }/v1/${tenantId}/mt-material/site-material/limit/lov/ui?materialId=${record.get(
              'inspectSchemeObjectId',
            )}&siteIds=${record.get('siteId')}`;
        },
        disabled: ({ record }) => {
          return (
            !(record.get('inspectSchemeObjectType') === 'MATERIAL') ||
            !record.get('inspectSchemeObjectId') ||
            !record.get('siteId') ||
            !(record.get('revisionFlag') === 'Y')
          );
        },
        required: ({ record }) => {
          return (
            record.get('inspectSchemeObjectType') === 'MATERIAL' &&
            record.get('inspectSchemeObjectId') &&
            record.get('siteId') &&
            record.get('revisionFlag') === 'Y'
          );
        },
      },
    },
  ],
});

const instantiationTableDS: () => DataSetProps = () => ({
  forceValidate: true,
  autoCreate: false,
  primaryKey: 'materialId',
  selection: DataSetSelection.multiple,
  fields: [
    {
      name: 'inspectSchemeObjectType',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeObject`).d('检验对象'),
      name: 'inspectSchemeObjectLov',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'MT.METHOD.MATERIAL_SITE_CATEGORY';
            case 'WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROCESS_WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROD_LINE':
              return 'MT.MODEL.PRODLINE';
            case 'AREA':
              return 'MT.MODEL.AREA';
            case 'OPERATION':
              return 'MT.METHOD.ROUTER_OPERATION';
            default:
              return 'MT.METHOD.MATERIAL_SITE_CATEGORY';
          }
        },
        lovPara: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return {
                tenantId,
                siteId: record.get('siteId'),
                enableFlag: 'Y',
              };
            case 'WORKCELL':
              return {
                tenantId,
                siteId: record.get('siteId'),
                workcellType: 'STATION',
              };
            case 'PROCESS_WORKCELL':
              return {
                tenantId,
                siteId: record.get('siteId'),
                workcellType: 'PROCESS',
              };
            case 'PROD_LINE':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            case 'AREA':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            case 'OPERATION':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            default:
              return {
                tenantId,
                siteId: record.get('siteId'),
                enableFlag: 'Y',
              };
          }
        },
        textField: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'materialName';
            case 'WORKCELL':
              return 'workcellName';
            case 'PROCESS_WORKCELL':
              return 'workcellName';
            case 'PROD_LINE':
              return 'prodLineName';
            case 'AREA':
              return 'areaName';
            case 'OPERATION':
              return 'description'
            default:
              return 'materialName';
          }
        },
        valueField: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'materialId';
            case 'WORKCELL':
              return 'workcellId';
            case 'PROCESS_WORKCELL':
              return 'workcellId';
            case 'PROD_LINE':
              return 'prodLineId';
            case 'AREA':
              return 'areaId';
            case 'OPERATION':
              return 'operationId'
            default:
              return 'materialId';
          }
        },
        disabled: ({ record }) => {
          return !record.get('inspectSchemeObjectType') || !record.get('siteId');
        },
      },
    },
    {
      name: 'inspectSchemeObjectId',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'inspectSchemeObjectLov.materialId';
            case 'WORKCELL':
              return 'inspectSchemeObjectLov.workcellId';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObjectLov.workcellId';
            case 'PROD_LINE':
              return 'inspectSchemeObjectLov.prodLineId';
            case 'AREA':
              return 'inspectSchemeObjectLov.areaId';
            case 'OPERATION':
              return 'inspectSchemeObjectLov.operationId';
            default:
              return 'inspectSchemeObjectLov.materialId';
          }
        },
      },
    },
    {
      name: 'inspectSchemeObject',
      label: intl.get(`${modelPrompt}.inspectSchemeObject`).d('检验对象'),
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'inspectSchemeObjectLov.materialCode';
            case 'WORKCELL':
              return 'inspectSchemeObjectLov.workcellCode';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObjectLov.workcellCode';
            case 'PROD_LINE':
              return 'inspectSchemeObjectLov.prodLineCode';
            case 'AREA':
              return 'inspectSchemeObjectLov.areaCode';
            case 'OPERATION':
              return 'inspectSchemeObjectLov.operationName';
            default:
              return 'inspectSchemeObjectLov.materialCode';
          }
        },
      },
    },
    {
      name: 'inspectSchemeObjectDesc',
      label: intl.get(`${modelPrompt}.inspectSchemeObjectDesc`).d('检验对象描述'),
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('inspectSchemeObjectType')) {
            case 'MATERIAL':
              return 'inspectSchemeObjectLov.materialName';
            case 'WORKCELL':
              return 'inspectSchemeObjectLov.workcellName';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObjectLov.workcellName';
            case 'PROD_LINE':
              return 'inspectSchemeObjectLov.prodLineName';
            case 'AREA':
              return 'inspectSchemeObjectLov.areaName';
            case 'OPERATION':
              return 'inspectSchemeObjectLov.description'
            default:
              return 'inspectSchemeObjectLov.materialName';
          }
        },
      },
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'inspectSchemeObjectLov.revisionFlag',
    },
    {
      // 物料版本
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      name: 'revisionCode',
      textField: 'description',
      valueField: 'description',
      // @ts-ignore
      noCache: true,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: item?.kid ? item.kid : uuid(),
                  description: item?.description ? item?.description : item,
                };
              });
            }
            if (record && firstlyQueryData.length === 1 && !record?.get('revisionCode')) {
              record?.init('revisionCode', firstlyQueryData[0].description);
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        lookupUrl: ({ record }) => {
          if (
            !(record.get('inspectSchemeObjectType') === 'MATERIAL') ||
            !record.get('inspectSchemeObjectId') ||
            !record.get('siteId') ||
            !(record.get('revisionFlag') === 'Y')
          ) {
            return;
          }
          return `${BASIC.TARZAN_METHOD
            }/v1/${tenantId}/mt-material/site-material/limit/lov/ui?materialId=${record.get(
              'inspectSchemeObjectId',
            )}&siteIds=${record.get('siteId')}`;
        },
        disabled: ({ record }) => {
          return (
            !(record.get('inspectSchemeObjectType') === 'MATERIAL') ||
            !record.get('inspectSchemeObjectId') ||
            !record.get('siteId') ||
            !(record.get('revisionFlag') === 'Y')
          );
        },
        required: ({ record }) => {
          return (
            record.get('inspectSchemeObjectType') === 'MATERIAL' &&
            record.get('inspectSchemeObjectId') &&
            record.get('siteId') &&
            record.get('revisionFlag') === 'Y'
          );
        },
      },
    },
    {
      name: 'fullUpdate',
      label: intl.get(`${modelPrompt}.fullUpdateColumn`).d('创建模式'),
      type: FieldType.string,
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`${modelPrompt}.fullUpdate`).d('全量更新') },
          { value: 'N', key: intl.get(`${modelPrompt}.incrementalUpdate`).d('增量更新') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
});
// 复制检验业务tab
const copyBusinessTypeDS: () => DataSetProps = () => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.originInspectBusinessType`).d('来源检验业务类型'),
      name: 'originInspectBusinessTypeObject',
    },
    {
      name: 'originInspectBusinessType',
      bind: 'originInspectBusinessTypeObject.value',
    },
    {
      name: 'originInspectBusinessTypeDesc',
      bind: 'originInspectBusinessTypeObject.meaning',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetInspectBusinessType`).d('目标检验业务类型'),
      name: 'targetInspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            inspectBusinessTypes: record.get('inspectBusinessTypes'),
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'targetInspectBusinessType',
      bind: 'targetInspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'targetInspectBusinessTypeDesc',
      bind: 'targetInspectBusinessTypeObject.inspectBusinessTypeDesc',
    },
  ],
});

export {
  listTableDS,
  detailFormDS,
  inspectionItemBasisDS,
  dimensionTableDS,
  copyDS,
  copyBusinessTypeDS,
  instantiationFormDS,
  instantiationTableDS,
};
