import React, { FC } from 'react';
import moment from 'moment';
import {
  Form,
  DataSet,
  Lov,
  TextField,
  Select,
  DateTimePicker,
  Output,
  NumberField,
  Switch,
  IntlField,
} from 'choerodon-ui/pro';
import { yesOrNoRender } from 'utils/renderer';
import { Icon, Tooltip } from 'choerodon-ui';
import { Tooltip as _Tooltip } from 'choerodon-ui/pro/lib/core/enum';

import getLang from '../Langs';

interface FormProps {
  detailDs: DataSet;
  editFlag: boolean;
  isNew: boolean;
  header: {
    [key: string]: any;
  };
}

const BasicForm: FC<FormProps> = props => {
  const { detailDs, editFlag, isNew, header } = props;
  const { cycleFlag, schedStartDate, planDuringDay, cycleTypeCode } = header;
  const handleCycleFlagChange = val => {
    if (cycleTypeCode === 'TIME_CYCLE') {
      if (!val) {
        // 清空预测间隔及单位的值
        detailDs.current?.set('schedInterval', null);
        detailDs.current?.set('schedIntervalUom', null);
      } else {
        detailDs.current?.set('schedIntervalUom', 'DAY');
      }
    }
  };

  const handleStartDateChange = val => {
    if (val && planDuringDay) {
      // 计划区间有值则带出结束日期
      setEndDate(planDuringDay, val);
    }
  };
  const handleDuringDayChange = val => {
    if (val && schedStartDate) {
      // 开始时间有值则带出结束日期
      setEndDate(val, schedStartDate);
    }
  };

  const setEndDate = (_planDuringDay, _schedStartDate) => {
    const dayStr = String(_planDuringDay);
    if (dayStr && dayStr.includes('.')) {
      const fullDay = Number(dayStr.split('.')[0]); // 完整天数
      const min = Number(`0.${dayStr.split('.')[1]}`); // 小数点后的天数 0.xx天

      const v = moment(_schedStartDate)
        .add(fullDay, 'days')
        .add(min * 24 * 60, 'minutes');
      detailDs.current?.set('schedEndDate', v);
    } else {
      detailDs.current?.set('schedEndDate', moment(_schedStartDate).add(_planDuringDay, 'days'));
    }
  };

  const handleEndDateChange = val => {
    if (val && schedStartDate) {
      // 开始时间有值则带出计划区间
      const d1 = Date.parse(val); // 返回该日期与 1970 年 1 月 1 日午夜之间相差的毫秒数
      const d2 = Date.parse(schedStartDate);
      const time = Math.abs(d1 - d2);
      const _planDuringDay = (time / (1000 * 3600 * 24)).toFixed(2); // 四舍五入保留两位

      detailDs.current?.set('planDuringDay', _planDuringDay);
    }
    if (!val) {
      // 清空区间
      detailDs.current?.set('planDuringDay', null);
    }
  };

  const hanldeCycleTypeChange = () => {
    detailDs.current?.set('schedStartDate', moment().startOf('day').set('hours', 8).set('minutes', 30));
    detailDs.current?.set('planDuringDay', null);
    detailDs.current?.set('schedEndDate', null);
    detailDs.current?.set('cycleFlag', 0);
    detailDs.current?.set('schedInterval', null);
    detailDs.current?.set('schedIntervalUom', null);
    detailDs.current?.set('catchTime', null);
  };
  return (
    <>
      {editFlag ? (
        <Form dataSet={detailDs} columns={3}>
          <Lov name="maintSiteLov" disabled={!isNew} />
          <IntlField name="schedName" />
          <TextField name="schedCode" />
          <Select name="cycleTypeCode" disabled={!isNew} onChange={hanldeCycleTypeChange} />
          {cycleTypeCode === 'TIME_CYCLE' && (
            <DateTimePicker name="schedStartDate" onChange={handleStartDateChange} />
          )}
          {cycleTypeCode === 'TIME_CYCLE' && (
            <NumberField
              name="planDuringDay"
              suffix={getLang('DAY')}
              onChange={handleDuringDayChange}
            />
          )}
          {cycleTypeCode === 'TIME_CYCLE' && (
            <DateTimePicker name="schedEndDate" onChange={handleEndDateChange} />
          )}
          {cycleTypeCode === 'METER_CYCLETRIGGE' && (
            <NumberField
              name="catchTime"
              label={
                <Tooltip title={getLang('CATCH_TIME_INFO')}>
                  {getLang('CATCH_TIME')}
                  <Icon type="contact_support-o" style={{ fontSize: '16px' }} />
                </Tooltip>
              }
            />
          )}
          {cycleTypeCode && (
            <Switch
              name="cycleFlag"
              onChange={handleCycleFlagChange}
              label={
                <Tooltip title={getLang('CYCLE_FLAG_INFO')}>
                  {getLang('CYCLE_FLAG')}
                  <Icon type="contact_support-o" style={{ fontSize: '16px' }} />
                </Tooltip>
              }
            />
          )}
          {cycleFlag && cycleTypeCode === 'TIME_CYCLE' && (
            <NumberField
              name="schedInterval"
              style={{ width: '100%', paddingRight: 8 }}
              label={
                <Tooltip title={getLang('SCHED_INTERVAL_INFO')}>
                  {getLang('SCHED_INTERVAL')}
                  <Icon type="contact_support-o" style={{ fontSize: '16px' }} />
                </Tooltip>
              }
              addonAfterStyle={{
                border: 'none',
                padding: 0,
                width: 80,
              }}
              addonAfter={<Select name="schedIntervalUom" />}
            />
          )}
          {!isNew && <DateTimePicker name="lastPlanTime" disabled />}
        </Form>
      ) : (
        <Form dataSet={detailDs} columns={3}>
          <Output name="maintSiteName" />
          <Output name="schedName" />
          <Output name="schedCode" />
          <Output name="cycleTypeCode" />
          {cycleTypeCode === 'TIME_CYCLE' && <Output name="schedStartDate" />}
          {cycleTypeCode === 'TIME_CYCLE' && (
            <Output name="planDuringDay" renderer={({ text }) => `${text} ${getLang('DAY')}`} />
          )}
          {cycleTypeCode === 'TIME_CYCLE' && <Output name="schedEndDate" />}
          {cycleTypeCode === 'METER_CYCLETRIGGE' && <Output name="catchTime" />}
          <Output
            name="cycleFlag"
            renderer={({ text }) => yesOrNoRender(Number(text))}
            label={
              <Tooltip title={getLang('CYCLE_FLAG_INFO')}>
                {getLang('CYCLE_FLAG')}
                <Icon type="contact_support-o" style={{ fontSize: '16px' }} />
              </Tooltip>
            }
          />
          {cycleFlag && cycleTypeCode === 'TIME_CYCLE' && (
            <Output
              name="schedInterval"
              tooltip={_Tooltip.none}
              renderer={({ record, value }) => {
                return `${value} ${record?.get('schedIntervalUomMeaning')}`;
              }}
              label={
                <Tooltip title={getLang('SCHED_INTERVAL_INFO')}>
                  {getLang('SCHED_INTERVAL')}
                  <Icon type="contact_support-o" style={{ fontSize: '16px' }} />
                </Tooltip>
              }
            />
          )}
          <Output name="lastPlanTime" />
        </Form>
      )}
    </>
  );
};

export default BasicForm;
