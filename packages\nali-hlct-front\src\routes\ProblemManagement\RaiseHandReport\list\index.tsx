import React, { FC, useEffect } from 'react';
import { RouteComponentProps } from 'react-router';
import { Button, DataSet, Table } from 'choerodon-ui/pro';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { useDataSetEvent } from 'utils/hooks';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import listPageFactory from '../stores/listPageFactory';

interface RaiseHandReportProps extends RouteComponentProps {
  tableDs: DataSet;
  customizeTable: any;
}

const modelPrompt = 'tarzan.qms.raiseHandReport';

const RaiseHandReport: FC<RaiseHandReportProps> = ({ tableDs, customizeTable, history }) => {
  useEffect(() => {
    tableDs.query();
  }, []);

  // 切换查询树类型时，进行查询
  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteName':
        record.set('prodLineName', null);
        record.set('operationDescription', null);
        break;
      default:
        break;
    }
  });

  const handleToDetails = code => {
    history.push(`/hwms/raise-hand-report/detail/${code}`);
  };

  const columns: ColumnProps[] = [
    {
      name: 'problemApplyCode',
      minWidth: 160,
      renderer: ({ value }) => <a onClick={() => handleToDetails(value)}>{value}</a>,
    },
    {
      name: 'problemTitle',
      minWidth: 150,
    },
    {
      name: 'problemApplyStatus',
      minWidth: 150,
    },
    {
      name: 'siteObj',
      minWidth: 180,
    },
    {
      name: 'prodLine',
      minWidth: 150,
    },
    {
      name: 'operation',
      minWidth: 190,
    },
    {
      name: 'equipment',
      minWidth: 150,
    },
    {
      name: 'apply',
      minWidth: 150,
    },
    {
      name: 'applyByDept',
      minWidth: 150,
    },
    {
      name: 'creationDate',
      minWidth: 150,
    },
    {
      name: 'reviewResult',
      minWidth: 150,
    },
    {
      name: 'reviewByName',
      minWidth: 150,
    },
    {
      name: 'reviewTime',
      minWidth: 150,
    },
  ];

  return (
    // 替换产品类名
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.raiseHand`).d('举手申请')}>
        <Button
          color={ButtonColor.primary}
          onClick={() => history.push(`/hwms/raise-hand-report/detail/create`)}
        >
          {intl.get('hzero.common.button.create').d('新建')}
        </Button>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.RAISE_HAND_REPORT.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.RAISE_HAND_REPORT.TABLE`,
          },
          <Table
            dataSet={tableDs}
            columns={columns}
            searchCode="raiseHand"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            customizable
            customizedCode="column-group"
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.qms.raiseHandReport', 'tarzan.common','hzero.common'],
})(
  withProps(
    () => {
      const tableDs = listPageFactory();
      return {
        tableDs,
      };
    },
    { cacheState: true },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.RAISE.HAND.REPORT.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.RAISE.HAND.REPORT.TABLE`,
      ],
    })(RaiseHandReport as any),
  ),
);
