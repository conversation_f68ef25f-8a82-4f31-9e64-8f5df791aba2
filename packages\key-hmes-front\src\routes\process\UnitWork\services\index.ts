/**
 * @Description: 工艺与工作单元关系维护-services
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 15:31:32
 * @LastEditTime: 2022-10-09 10:52:45
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 工艺与工作单元关系维护-删除关系
export function DeleteItems() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation-wkc-dispatch-rel/delete/ui`,
    method: 'POST',
  };
}

// 工艺与工作单元关系维护-保存关系
export function SaveItem() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation-wkc-dispatch-rel/save/ui`,
    method: 'POST',
  };
}
