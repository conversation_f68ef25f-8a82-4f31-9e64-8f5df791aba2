.container {
  height: 100%;
  padding: 0 12px;
}
.query-bar {
  position: fixed;
  top: 8px;
  right: 12px;
  width: 85px;
}
.legend {
  position: fixed;
  top: 52px;
  width: calc(100% - 40px);
  text-align: center;
  span {
    color: #4f4f4f;
    font-weight: 400;
    font-size: 10px;
    font-family: PingFangSC-Regular, sans-serif;
    letter-spacing: 0;
  }
}
.customize-collapse {
  height: 100%;
  :global(.c7n-collapse-item) {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
  }
  :global(.c7n-collapse-content-box) {
    height: 100%;
  }
  :global(.c7n-collapse-header) {
    padding: 12px 0 4px 8px !important;
    &::before {
      top: calc(50% - 0.07rem + 4px) !important;
    }
  }
}
