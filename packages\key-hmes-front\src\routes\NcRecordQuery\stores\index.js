import { Host, BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;
const modelPrompt = 'tarzan.hmes.NcRecordQuery';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'ncRecordDetailId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码标识'),
      },
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      },
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'ncRecodeTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecodeTypeDesc`).d('不良记录类型'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'disposalFunctionDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalFunctionDescription`).d('处置方法'),
      },
      {
        name: 'ncCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCode`).d('不良代码编码'),
      },
      {
        name: 'ncCodeName',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCodeName`).d('不良代码名称'),
      },
      {
        name: 'ncCodeStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCodeStatusDesc`).d('不良代码状态'),
      },
      {
        name: 'workcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellName`).d('不良产生工作单元'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('不良产生设备'),
      },
      {
        name: 'operationDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDesc`).d('不良产生工艺'),
      },
      {
        name: 'organizationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.organizationName`).d('不良产生产线'),
      },
      {
        name: 'ncRecordTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordTime`).d('不良产生时间'),
      },
      {
        name: 'ncRecordUserIdRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordUserIdRealName`).d('不良记录人'),
      },
      {
        name: 'ncRecordClosedTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordClosedTime`).d('不良关闭时间'),
      },
      {
        name: 'ncRecordClosedRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordClosedRealName`).d('不良关闭人'),
      },
    ],
    queryFields: [
      {
        name: 'ncIncidentStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncIncidentStatus`).d('不良记录状态'),
        textField: 'description',
        valueField: 'statusCode',
        multiple: true,
        lovPara: { tenantId },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_RECORD_STATUS`,
        lookupAxiosConfig: {
          transformResponse(data) {
            // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'identificationstr',
        type: 'string',
        label: intl.get(`${modelPrompt}.identificationstr`).d('条码号'),
      },
      {
        name: 'defaultNcCodeLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.defaultNcCodeLov`).d('不良代码'),
        lovCode: 'MT.METHOD.NC_CODE',
        ignore: 'always',
        textField: 'ncCode',
        valueField: 'ncCodeId',
        lovPara: {
          tenantId,
        },
        multiple: true,
      },
      {
        name: 'ncCodeIds',
        bind: 'defaultNcCodeLov.ncCodeId',
      },
      {
        name: 'dateFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.dateFrom`).d('记录开始时间'),
        max: 'dateTo',
        dynamicProps: {
          min: ({ record }) => record?.get('dateTo')?moment(record?.get('dateTo')).subtract(1, 'M'):null,
        },
      },
      {
        name: 'dateTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.endTime`).d('记录结束时间'),
        min: 'dateFrom',
        dynamicProps: {
          max: ({ record }) => record?.get('dateFrom')?moment(record?.get('dateFrom')).add(1, 'M'):null,
        },
      },
      {
        name: 'workCellLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元'),
        lovCode: 'MT.MODEL.WORKCELL',
        noCache: true,
        ignore: 'always',
        lovPara: {
          tenantId,
        },
        multiple: true,
      },
      {
        name: 'workcellIds',
        bind: 'workCellLov.workcellId',
      },
      {
        name: 'equipmentLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.collectEquipmentLov`).d('设备'),
        lovCode: 'MT.MODEL.EQUIPMENT',
        ignore: 'always',
        multiple: true,
      },
      {
        name: 'equipmentIds',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('工艺'),
        lovCode: 'MT.OPERATION',
        ignore: 'always',
        lovPara: { tenantId },
        multiple: true,
      },
      {
        name: 'operationIds',
        bind: 'operationObj.operationId',
      },
      {
        name: 'prodLineObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('生产线'),
        lovCode: 'MT.MODEL.PRODLINE',
        ignore: 'always',
        lovPara: { tenantId },
        multiple: true,
      },
      {
        name: 'prodLineIdList',
        bind: 'prodLineObj.prodLineId',
      },
    ],
    transport: {
      read: ({ data }) => {
        const code = data.identificationstr;
        const dataParams = {
          ...data,
          identifications: data.identificationstr ? code.split(',') : null,
        };
        if (!code) {
          delete dataParams.identifications;
        }
        return {
          url: `${Host}/v1/${tenantId}/hme-nc-record/list/record-query`,
          data: dataParams,
          method: 'POST',
        };
      },
    },
  };
};

export { tableDS };
