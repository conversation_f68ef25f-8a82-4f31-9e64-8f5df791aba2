import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.wms.DeviceStatusMonitoringBoard';
const tenantId = getCurrentOrganizationId();

export const TopFilterDS = (): DataSetProps => ({
  autoCreate: true,
  fields: [
    {
      name: 'equipmentLocationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentLocation`).d('设备位置'),
      lovCode: "AMDM.LOCATIONS",
      textField: 'locationName',
      valueField: 'assetLocationId',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      // required: true,
    },
    {
      name: "equipmentLocationId",
      bind: 'equipmentLocationLov.assetLocationId',
    },
  ],
});
