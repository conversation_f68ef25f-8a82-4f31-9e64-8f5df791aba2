/**
 * 杂项工作台-入口文件
 * @date 2023-1-3
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState,useMemo } from 'react';
import { DataSet, Table, Dropdown, Modal, Button } from 'choerodon-ui/pro';
import { <PERSON><PERSON>, <PERSON>u, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import moment from 'moment';
import { useRequest } from '@components/tarzan-hooks';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
// import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
import { BASIC } from '@/utils/config';
import notification from 'utils/notification';
import myInstance from '@utils/myAxios';
import { TemplatePrintButton } from '../../../components/tarzan-ui';
import { headerTableDS, lineTableDS } from './stores/ListDS';
import { headDS, tableDS } from './stores/MaterialBatchDS';
// import { drawerTableDS } from './stores/DrawerTableDS';
import CreateMaterial from './CreateMaterial';
import styles from './index.module.less';
import MaterialLotDrawer from './MaterialLotDrawer';
import AppointMaterialLotPage from './AppointMaterialLot';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.miscellaneous';
let modalMaterial; // 创建物料批的modal
// let modalMaterialDetail; // 物料批明细的modal

let modalAssembly;

// const lugeUrl = '-24175';
const lugeUrl = '';

const Order = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;
  // 判断头搜索条件切换
  const [siteId, setSiteId] = useState();
  const [selectedStatus, setSelectedStatus] = useState(undefined);
  const [printIds, setPrintIds] = useState([]); // 头表格选择的id
  // 动态列实际columns
  const headDs = useMemo(() => new DataSet(headDS()), []); // 创建物料批-头ds
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 创建物料批-行ds
  // const drawerTableDs = useMemo(() => new DataSet(drawerTableDS()), []);
  // 保存上一次创建成功后的物料批
  const [batchListsArr, setBatchListsArr] = useState([]);
  // 头选中行instructionDocType
  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const { run: handleEstablishMaterial, loading: changeEstablishLoading } = useRequest(
    {
      url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/miscellaneous/material-lot-create/ui`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  useEffect(() => {
    if (tableDs) {
      tableDs.addEventListener('batchSelect', handleSelect);
      tableDs.addEventListener('batchUnSelect', handleSelect);
    }
    return () => {
      if (tableDs) {
        tableDs.removeEventListener('batchSelect', handleSelect);
        tableDs.removeEventListener('batchUnSelect', handleSelect);
      }
    };
  }, []);

  const handleSelect = () => {
    const selected = tableDs.selected.map(item => {
      return item.get('materialLotId');
    });
    modalMaterial.update({
      title: createModalTitle(selected),
    });
  };

  // 物料批弹窗上的删除按钮，用来删除与指令的关系
  const handleDelete = (selected) => {
    // @ts-ignore
    const instructionDocLineId = headDs.toData()[0].instructionDocLineId;
    const data = {
      instructionDocLineId,
      materialLotIdList: selected,
    };
    const url = `${BASIC.HWMS_BASIC}/v1/${getCurrentOrganizationId()}/miscellaneous/material-lot-remove/ui`;
    return myInstance.post(url, data).then(async res => {
      if (res?.data?.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        headDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
        const list = await headDs.query();
        if (list?.success) {
          // 新建物料批成功后更新弹框里的头，和行列表
          tableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
          await tableDs.query();
          headDs.current.set('productionDate', moment().format('YYYY-MM-DD HH:mm:ss'));
          toggleOkDisabled(instructionDocLineId, batchListsArr);
        } else {
          notification.error({
            message: list?.data?.message,
          });
        }
      } else {
        notification.error({
          message: res?.data?.message,
        });
      }
    });
  };

  const createModalTitle = (selected = []) => {
    return (
      <div>
        <span style={{ fontSize: '14px' }}>
          {intl.get(`${modelPrompt}.create.materialBatch`).d('创建物料批')}
        </span>
        <div
          style={{
            float: 'right',
            display: 'flex',
            flexDirection: 'row-reverse',
            alignItems: 'center',
            marginRight: '0.3rem',
          }}
        >
          <PermissionButton
            style={{
              marginLeft: '0.1rem',
              marginRight: '0.2rem',
            }}
            icon="save"
            type="c7n-pro"
            color={ButtonColor.primary}
            loading={changeEstablishLoading}
            onClick={() => handleEstablish(lineTableDs?.current?.get('instructionDocLineId'))}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.create.establish`).d('创建')}
          </PermissionButton>
          <PermissionButton
            style={{
              marginLeft: '0.1rem',
              marginRight: '0.2rem',
            }}
            icon="delete"
            type="c7n-pro"
            color={ButtonColor.default}
            onClick={() => handleDelete(selected)}
            disabled={selected.length === 0}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.delete').d('删除')}
          </PermissionButton>
          {/* <FRPrintButton
            kid="MATERIAL_LOT_PRINT" // 使用打印组件的功能的唯一标识，业务提供
            queryParams={selected} // 查询数据
            disabled={selected.length === 0}
            printObjectType="MATERIAL_LOT"
          /> */}
          <TemplatePrintButton
            disabled={!selected.length}
            printButtonCode='MISCELLANEOU_IN'
            printParams={{ materialLotId: selected.join(',') }}
          />
        </div>
      </div>
    );
  };

  // 创建物料批
  const handleCreateMaterial = async record => {
    const instructionDocLineId = record?.get('instructionDocLineId');
    headDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
    const res = await headDs.query();
    headDs.current.set('productionDate', moment().format('YYYY-MM-DD HH:mm:ss'));
    if (res.success) {
      modalMaterial = Modal.open({
        title: createModalTitle([]),
        className: 'hmes-style-modal',
        maskClosable: true,
        destroyOnClose: true,
        drawer: true,
        closable: true,
        onClose: () => {
          tableDs.batchUnSelect(tableDs.selected);
        },
        style: {
          width: 720,
        },
        children: (
          <CreateMaterial
            headDs={headDs}
            tableDs={tableDs}
            instructionDocLineId={instructionDocLineId}
            batchList={[]}
            customizeTable={customizeTable}
          />
        ),
        footer: (
          <Button onClick={() => modalMaterial.close()}>
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        ),
      });
    } else {
      notification.error({
        message: res?.message,
      });
    }
  };

  // 物料批创建
  const handleEstablish = async instructionDocLineId => {
    const validate = await headDs.validate();
    if (validate) {
      const data = {
        ...headDs?.current?.toData(),
      };
      return handleEstablishMaterial({
        params: data,
      }).then(async res => {
        if (res?.success) {
          const batchLists = res?.rows;
          // 保存新生成的物料批，删除物料批的时候会用到
          setBatchListsArr(batchLists);
          headDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
          const list = await headDs.query();
          if (list?.success) {
            notification.success({});
            // 新建物料批成功后更新弹框里的头，和行列表
            tableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
            await tableDs.query();
            headDs.current.set('productionDate', moment().format('YYYY-MM-DD HH:mm:ss'));
            toggleOkDisabled(instructionDocLineId, batchLists);
          } else {
            notification.error({
              message: list?.data?.message,
            });
          }
        }
      });
    }
  };

  // 更新物料批
  const toggleOkDisabled = (instructionDocLineId, batchLists) => {
    modalMaterial.update({
      children: (
        <CreateMaterial
          headDs={headDs}
          tableDs={tableDs}
          instructionDocLineId={instructionDocLineId}
          batchList={batchLists}
          customizeTable={customizeTable}
        />
      ),
    });
  };

  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    if (props?.history?.action === 'PUSH') {
      headerTableDs.query(props.headerTableDs.currentPage);
      handleLineTableChange({
        dataSet: headerTableDs,
      });
    }
  }, []);

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.queryDataSet.addEventListener
        : headerTableDs.queryDataSet.removeEventListener;
      handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
      handler.call(headerTableDs, 'batchSelect', handleLineTableChange);
      handler.call(headerTableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (data.siteId !== siteId) {
      setSiteId(data.siteId);
    }
    if (!data.instructionDocTypeObj) {
      record.set('instructionDocStatus', null);
    }
    if (!data.site) {
      record.set('warehouse', null);
    }
    if (!data.warehouse) {
      record.set('locator', null);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 列表刷新清除头单选状态
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable();
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    const _selectedStatus = [];
    const _printIds = [];
    const completedList = ['1_PROCESSING', '1_COMPLETED', '2_PROCESSING', 'COMPLETED'];
    dataSet.selected.forEach(item => {
      const instructionDocStatus = item?.data?.instructionDocStatus;
      _printIds.push(item?.data?.instructionDocId);
      if (completedList.indexOf(instructionDocStatus) > -1) {
        if (_selectedStatus.indexOf('COMPLETED') === -1) {
          _selectedStatus.push('COMPLETED');
        }
      } else if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    setSelectedStatus(_selectedStatus.length === 1 ? _selectedStatus[0] : undefined);
    setPrintIds(_printIds);
  };

  // 行列表数据查询
  const queryLineTable = data => {
    lineTableDs.setQueryParameter('instructionDocId', data?.instructionDocId);
    lineTableDs.setQueryParameter('instructionDocType', data?.instructionDocType);
    lineTableDs.setQueryParameter('workOrderId', data?.workOrderId);
    lineTableDs.setQueryParameter('prodLineId', data?.prodLineId);
    lineTableDs.setQueryParameter('materialId', data?.materialId);
    lineTableDs.setQueryParameter('revisionCode', data?.revisionCode);
    lineTableDs.query();
  };

  // 操作列渲染
  const optionRender = record => (
    <>
      <a
        style={{ marginRight: '8px' }}
        onClick={() => {
          handleMaterialLotDetail(record);
        }}
      >
        {intl.get(`${modelPrompt}.detail`).d('明细')}
      </a>
      <a
        style={{ marginRight: '8px' }}
        disabled={!(headerTableDs?.current?.get('instructionDocTypeTag') === 'RETURN' && record?.toData().identifyType === "MATERIAL_LOT")}
        onClick={() => handleCreateMaterial(record)}
      >
        {intl.get(`${modelPrompt}.create.materialBatch`).d('创建')}
      </a>
      <a
        disabled={!(!['CANCEL', 'CLOSED', 'COMPLETED', '1_COMPLETED'].includes(record?.toData().instructionStatus) && ["MATERIAL_LOT", ''].includes(record?.toData().identifyType))}
        onClick={() => handleAppointMaterialLot(record)}
      >
        {intl.get(`${modelPrompt}.create.materialLotAppoint`).d('指定')}
      </a>
      <PermissionButton
        type="text"
        disabled={
          record.get('instructionStatus') !== 'RELEASED' || record.get('permissionFlag') !== 'Y'
        }
        onClick={() => handleCancel(record)}
        permissionList={[
          {
            code: `${path}.button.edit`,
            type: 'button',
            meaning: '列表页-编辑新建删除复制按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.line.button.cancel`).d('行取消')}
      </PermissionButton>
    </>
  );

  // 组件信息
  const handleMaterialLotDetail = async record => {
    const recordDetail = record?.toData() || {};
    modalAssembly = Modal.open({
      title:
        recordDetail.identifyType === 'MATERIAL_LOT'
          ? intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细')
          : intl.get(`${modelPrompt}.materialDetail`).d('物料明细'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <MaterialLotDrawer
          record={{
            identifyType: recordDetail.identifyType,
            instructionId: recordDetail.instructionId,
            instructionDocType: headerTableDs.current.get('instructionDocType'),
          }}
          customizeTable={customizeTable}
        />
      ),
      footer: (
        <Button onClick={() => modalAssembly.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 打开指定物料批页面
  const handleAppointMaterialLot = async record => {
    const lineIndex=record.index;
    lineTableDs.setQueryParameter('instructionDocId', headerTableDs?.current?.toData()?.instructionDocId);
    lineTableDs.setQueryParameter('instructionDocType', headerTableDs?.current?.toData()?.instructionDocType);
    lineTableDs.setQueryParameter('workOrderId', headerTableDs?.current?.toData()?.workOrderId);
    lineTableDs.setQueryParameter('prodLineId', headerTableDs?.current?.toData()?.prodLineId);
    lineTableDs.setQueryParameter('materialId', headerTableDs?.current?.toData()?.materialId);
    lineTableDs.setQueryParameter('revisionCode', headerTableDs?.current?.toData()?.revisionCode);
    let data = []
    lineTableDs.query().then(res=>{
      data=res.rows.content
      lineTableDs.loadData(data);
      if(!(!['CANCEL', 'CLOSED', 'COMPLETED'].includes(data[lineIndex].instructionStatus) && ["MATERIAL_LOT", ''].includes(data[lineIndex].identifyType))){
        return false;
      }
      const recordData = record.toData();
      const appointProps = {
        instructionDocId: headerTableDs?.current?.toData().instructionDocId,
        instructionDocType: headerTableDs?.current?.toData().instructionDocType,
        instructionId: recordData.instructionId,
        instructionDocLineId: recordData.instructionDocLineId,
        materialId: recordData.materialId,
        revisionCode: recordData.revisionCode,
        siteId: headerTableDs.current.toData().siteId,
        locatorId: recordData.locatorId || recordData.warehouseId,
        ownerType: recordData.fromOwnerType,
        ownerId: recordData.fromOwnerId,
        toleranceFlag: recordData.toleranceFlag,
        toleranceType: recordData.toleranceType,
        toleranceMaxValue: recordData.toleranceMaxValue,
        toleranceMinValue: recordData.toleranceMinValue,
        quantity: recordData.quantity,
      }
      if (appointProps.instructionId) {
        Modal.open({
          title: intl.get(`${modelPrompt}.AppointMaterialLotPage`).d('指定物料批'),
          key: Modal.key(),
          className: 'hmes-style-modal',
          style: {
            width: 1000,
          },
          maskClosable: true,
          destroyOnClose: true,
          drawer: true,
          closable: true,
          okButton: false,
          cancelButton: false,
          children: (
            <AppointMaterialLotPage
              appointProps={appointProps}
            // customizeTable={customizeTable}
            />
          ),
        });
      }
    })
  };

  // 行取消
  const handleCancel = async record => {
    const _index = headerTableDs.currentIndex;
    return request(`${BASIC.HWMS_BASIC}${lugeUrl}/v1/${tenantId}/miscellaneous/line/cancel/ui`, {
      method: 'POST',
      body: {
        instructionId: record.get('instructionId'),
        businessType: 'CANCEL',
      },
    }).then(res => {
      if (res?.success) {
        headerTableDs.query(props.headerTableDs.currentPage).then(() => {
          headerTableDs.locate(_index);
        });
        lineTableDs.query(props.lineTableDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'instructionDocNum',
      width: 150,
      renderer: ({ record, value }) => {
        if (record.data.instructionDocStatus === 'RELEASED') {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/hwms/in-library/miscellaneous/detail/${record.data.instructionDocId}`,
                );
              }}
            >
              {value}
            </a>
          );
        }
        return value;
      },
      lock: 'left',
    },
    {
      name: 'instructionDocTypeDesc',
      width: 150,
    },
    { name: 'sourceSystem' },
    {
      name: 'instructionDocStatusDesc',
      width: 80,
    },
    {
      name: 'siteCode',
      width: 120,
    },
    {
      name: 'accountTypeDesc',
      width: 80,
    },
    {
      name: 'costcenterCode',
      width: 150,
    },
    {
      name: 'costcenterCategoryDesc',
      width: 100,
    },
    {
      name: 'internalOrderCode',
      width: 150,
    },
    {
      name: 'internalOrderCategoryDesc',
      width: 100,
    },
    {
      name: 'remark',
      width: 120,
    },
    {
      name: 'printTimes',
      width: 100,
    },
    {
      name: 'realName',
      width: 120,
    },
    {
      name: 'creationDate',
      align: 'center',
      width: 150,
    },
    {
      name: 'applyPerson',
      align: 'center',
      width: 150,
    },
    {
      name: 'applyReason',
      align: 'center',
      width: 150,
    },
    {
      name: 'applyEquipment',
      align: 'center',
      width: 150,
    },
    {
      name: 'scrapReason',
      align: 'center',
      width: 150,
    },
    {
      name: 'purpose',
      align: 'center',
      width: 150,
    },
  ];

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'lineNumber',
      width: 60,
      lock: 'left',
    },
    {
      name: 'identifyType',
      width: 120,
      lock: 'left',
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   } if (value === 'MATERIAL_LOT' || value === '') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      // },
    },
    {
      name: 'materialCode',
      width: 140,
      lock: 'left',
    },
    {
      name: 'revisionCode',
      width: 80,
      lock: 'left',
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'model',
      width: 140,
    },
    {
      name: 'bomCode',
      width: 140,
    },
    {
      name: 'quantity',
      width: 100,
    },
    {
      name: 'executedQty',
      width: 80,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'instructionStatusDesc',
      width: 80,
    },
    {
      name: 'soNumber',
      width: 140,
    },
    {
      name: 'soLineNum',
      width: 100,
    },
    {
      name: 'warehouseCode',
      width: 140,
    },
    {
      name: 'locatorCode',
      width: 140,
    },
    {
      name: 'toleranceFlag',
      width: 80,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceTypeDesc',
      width: 100,
    },
    {
      name: 'toleranceMaxValue',
      width: 80,
    },
    {
      name: 'toleranceMinValue',
      width: 80,
    },
    {
      name: 'remark',
      width: 140,
    },
    {
      name: 'option',
      fixed: 'right',
      lock: 'right',
      width: 180,
      align: 'center',
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => optionRender(record),
    },
  ];

  const headerRowClick = record => {
    queryLineTable(record?.toData());
  };

  const clickMenu = async ({ key }) => {
    const instructionDocIds =
      headerTableDs?.selected?.map(item => {
        return item?.get('instructionDocId');
      }) || [];

    return request(`${BASIC.HWMS_BASIC}${lugeUrl}/v1/${tenantId}/miscellaneous/state-change/ui`, {
      method: 'POST',
      body: {
        instructionDocIds,
        instructionDocStatus: key,
      },
    }).then(res => {
      if (res?.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        headerTableDs.clearCachedSelected();
        setSelectedStatus(undefined);
        setPrintIds([]);
        headerTableDs.query(props.headerTableDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  const createDelivery = () => {
    props.history.push(`/hwms/in-library/miscellaneous/detail/create`);
  };

  const onFieldEnterDown = () => {
    headerTableDs.query(props.headerTableDs.currentPage);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.miscellaneous`).d('杂项工作台-WMS')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={createDelivery}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.createDelivery`).d('创建单据')}
        </PermissionButton>
        <Dropdown
          overlay={
            <Menu onClick={clickMenu} className={styles['split-menu']}>
              {selectedStatus === 'RELEASED' && (
                <Menu.Item key="CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
                  </a>
                </Menu.Item>
              )}
              {(selectedStatus === 'COMPLETED' ||
                selectedStatus === 'PROCESSING' ||
                selectedStatus === '1_COMPLETED' ||
                selectedStatus === '1_PROCESSING' ||
                selectedStatus === '2_PROCESSING')
                && (
                  <Menu.Item key="CLOSED">
                    <a target="_blank" rel="noopener noreferrer">
                      {intl.get(`${modelPrompt}.button.close`).d('关闭')}
                    </a>
                  </Menu.Item>
                )}
            </Menu>
          }
          trigger={['click']}
          disabled={
            [
              'RELEASED',
              'COMPLETED',
              'PROCESSING',
              '1_COMPLETED',
              '1_PROCESSING',
              '2_PROCESSING',
            ].indexOf(selectedStatus) === -1
          }
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={
              [
                'RELEASED',
                'COMPLETED',
                'PROCESSING',
                '1_COMPLETED',
                '1_PROCESSING',
                '2_PROCESSING',
              ].indexOf(selectedStatus) === -1
            }
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        {/* <FRPrintButton
          kid="MISCELLANEOUS_WORKBENCH"
          queryParams={printIds}
          disabled={!(printIds.length > 0)}
          printObjectType="INSTRUCTION_DOC"
        /> */}
        <TemplatePrintButton
          disabled={!printIds.length}
          printButtonCode='MISCELLANEOUS'
          printParams={{ docId: printIds.join(',') }}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.HEAD`,
          },
          <Table
            searchCode="zxgzt1"
            customizedCode="zxgzt1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            {lineTableDs && (
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.LINE`,
                },
                <Table
                  customizedCode="zxgzt2"
                  className={styles['expand-table']}
                  dataSet={lineTableDs}
                  highLightRow={false}
                  columns={lineTableColumns}
                />,
              )
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.receive.miscellaneous', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_MATERIAL_LOT.QUERY`,
    ],
  }),
)(Order);
