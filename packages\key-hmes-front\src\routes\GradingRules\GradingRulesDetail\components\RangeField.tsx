
import React from 'react';
import { NumberField, Tooltip } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { isNil } from 'lodash';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hwms.inspectItemMaintain';

const RangeField = observer((props) => {
  const {
    disabled,
    record,
    name,
  } = props;

  // 值更新
  const updateDataValue = () => {
    let _val: string | null = null;
    _val = `${record.get('leftchar')}${isNil(record.get('leftvalue')) ? '-∞' : record.get('leftvalue')},${isNil(record.get(
      'rightvalue',
    )) ? '+∞' : record.get('rightvalue')}${record.get('rightchar')}`;
    record.set(name, _val);
  };

  // 切换区间开闭的回调
  const handleChangeChar = (record, btnName) => {
    switch (record.get(btnName)) {
      case '(':
        record.set(btnName, '[');
        break;
      case '[':
        record.set(btnName, '(');
        break;
      case ')':
        record.set(btnName, ']');
        break;
      default:
        record.set(btnName, ')');
    }
    updateDataValue();
  };

  return (
    // <div className={styles['range-field-inner']}>
    <NumberField
      name="multipleValue"
      record={record}
      disabled={disabled}
      style={{ width: '100%' }}
      className={styles['range-field-inner']}
      onChange={updateDataValue}
      prefix={
        (!disabled ? (
          <Tooltip
            title={
              record.get('rightchar') === '('
                ? intl.get(`${modelPrompt}.contain`).d('点击切换为包含')
                : intl.get(`${modelPrompt}.not.contain`).d('点击切换为不包含')
            }
          >
            <div
              className={styles['char-style']}
              onClick={() => handleChangeChar(record, 'leftchar')}
            >
              {record.get('leftchar')}
            </div>
          </Tooltip>
        ) : (
          <div>{record.get('leftchar')}</div>
        ))
      }
      suffix={
        (!disabled ? (
          <Tooltip
            title={
              record.get('rightchar') === ')'
                ? intl.get(`${modelPrompt}.contain`).d('点击切换为包含')
                : intl.get(`${modelPrompt}.not.contain`).d('点击切换为不包含')
            }
          >
            <div
              className={styles['char-style']}
              onClick={() => handleChangeChar(record, 'rightchar')}
            >
              {record.get('rightchar')}
            </div>
          </Tooltip>
        ) : (
          <div>{record.get('rightchar')}</div>
        ))
      }
    />
    // </div>
  )
})

export default RangeField;
