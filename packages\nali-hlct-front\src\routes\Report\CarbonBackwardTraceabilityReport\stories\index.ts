/**
 * @Description: 正向追溯报表
 */

import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hmes.CarbonBackwardTraceabilityReport';
const tenantId = getCurrentOrganizationId();

const carbonBackwardTraceabilityReportTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  forceValidate: true,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'correlationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNumber`).d('订单号'),
    },
  ],
  fields: [
    {
      name: 'correlationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNumber`).d('订单号'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotNumber`).d('批次号'),
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('分切送检单号'),
    },
    {
      name: 'fqIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fqIdentification`).d('分切卷号'),
    },
    {
      name: 'fjIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fjIdentification`).d('复卷卷号'),
    },
    {
      name: 'ckIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ckIdentification`).d('涂炭卷号'),
    },
    {
      name: 'rowIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.rowIdentification`).d('物料批条码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'lines',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lines`).d('线别'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-trace-report/tt/reverse/list/ui`,
        method: 'GET',
      };
    },
  },
});


export {
  carbonBackwardTraceabilityReportTableDS,
};
