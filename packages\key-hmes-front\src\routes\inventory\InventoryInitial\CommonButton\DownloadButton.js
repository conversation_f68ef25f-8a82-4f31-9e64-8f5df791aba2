import React from 'react';
import { Button as PermissionButton } from 'components/Permission';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import intl from 'utils/intl';
import { BASIC, API_HOST } from '@/utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.inventory.initial.model';

const DownloadButton = ({ path }) => {
  const download = () => {
    const elink = document.createElement('a');
    elink.style.display = 'none';
    elink.href = `${API_HOST}${
      BASIC.HWMS_BASIC
    }/v1/${tenantId}/mt-material-lot/download/model-attr/ui?access_token=${getAccessToken()}`;
    document.body.appendChild(elink);
    elink.click();
    document.body.removeChild(elink);
  };
  return (
    <PermissionButton
      type="c7n-pro"
      icon="get_app"
      onClick={download}
      permissionList={[
        {
          code: `${path}.button.tempalte`,
          type: 'button',
          meaning: '列表页-模板下载按钮',
        },
      ]}
    >
      {intl.get(`${modelPrompt}.button.download.tempalte`).d('模板下载')}
    </PermissionButton>
  );
};

export default DownloadButton;
