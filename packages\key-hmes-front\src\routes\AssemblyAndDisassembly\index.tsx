/**
 * @Description: 组装拆卸
 */
import React, {useEffect, useState, useMemo} from 'react';
import {
  Button,
  DataSet,
  Form,
  Lov,
  Modal,
  NumberField,
  Select,
  Table,
} from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { Content, Header } from 'components/Page';
import {ColumnAlign, ColumnLock, TableQueryBarType} from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { Collapse, Popconfirm} from "choerodon-ui";
import {ButtonColor, FuncType} from "choerodon-ui/pro/lib/button/enum";
import {Size} from "choerodon-ui/pro/lib/core/enum";
import {BASIC} from "@utils/config";
import {getCurrentOrganizationId} from 'utils/utils';
import myInstance from "@utils/myAxios";
import notification from "utils/notification";
import { entranceDS, lineDS } from './stores/EntranceDS';


const modelPrompt = 'tarzan.AssemblyAndDisassembly';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;
const { Option } = Select;
let printerModal;

const AssemblyAndDisassembly = () => {

  const [selectedMaterialLotIds, setSelectedMaterialLotIds] = useState<any>([]);
  const [selectedAllNum, setSelectedAllNum] = useState<any>(0);

  const entranceDs = useMemo(() => new DataSet(entranceDS()), []);
  const lineDs = useMemo(() => new DataSet(lineDS()), []);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (entranceDs) {
      const handler = flag ? entranceDs.addEventListener : entranceDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(entranceDs, 'select', handleDataSetSelectUpdate);
      handler.call(entranceDs, 'unSelect', handleDataSetSelectUpdate);
      handler.call(entranceDs, 'selectAll', handleDataSetSelectUpdate);
      handler.call(entranceDs, 'unSelectAll', handleDataSetSelectUpdate);
    }
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    let _selectedAllNum: number = 0;
    const _materialLotIds: string[] = [];
    entranceDs.selected.forEach(item => {
      const { materialLotId, qty } = item.toData();
      _materialLotIds.push(materialLotId);
      _selectedAllNum = Number(_selectedAllNum) + Number(qty);
    });
    setSelectedMaterialLotIds(_materialLotIds);
    setSelectedAllNum(_selectedAllNum);
    if (!lineDs.current?.toData().length) {
      lineDs.loadData([]);
    }
  };

  const handleAdd = () => {
    const listData = lineDs.toData();
    let maxNumber = 0;
    listData.forEach(item => {
      const { lineNumber } = item as any;
      if (lineNumber) {
        if (lineNumber > maxNumber) {
          maxNumber = lineNumber;
        }
      }
    });
    lineDs.create(
      {
        selectedAllNum,
        lineNumber: parseInt(String(maxNumber / 10), 10) * 10 + 10,
      },
      0,
    );
  };

  const columns: ColumnProps[] = [
    { name: 'materialLotCode', width: 150 },
    { name: 'bomCode', width: 150 },
    { name: 'modelCode', width: 150 },
    { name: 'materialCode' },
    { name: 'materialName' },
    { name: 'qty', width: 150 },
    { name: 'locatorCode', width: 150 },
    { name: 'uomCode', width: 150 },
    { name: 'lot', width: 150 },
    { name: 'enableFlagMeaning', width: 150 },
    { name: 'qualityStatusMeaning' },
  ];

  const lineColumns: ColumnProps[] = [
    {
      header: () => (
        <Button
          icon="add"
          funcType={FuncType.flat}
          onClick={handleAdd}
          size={Size.small}
          disabled={!selectedMaterialLotIds.length}
        />
      ),
      align: ColumnAlign.center,
      width: 70,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => record && lineDs.remove(record)}
        >
          <Button
            icon="remove"
            disabled={record!.get('instructionDocLineId')}
            funcType={FuncType.flat}
            size={Size.small}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'materialLov',
      width: 180,
      renderer: ({ record }) => record!.get('materialCode'),
      editor: record =>
        !record!.get('instructionDocLineId') && (
          <Lov
            name="materialLov"
          />
        ),
    },
    {
      name: 'materialName',
      width: 180,
    },
    {
      name: 'bomCode',
      width: 150,
    },
    {
      name: 'modelCode',
      width: 120,
    },
    {
      name: 'qty',
      width: 150,
      editor: record => !record!.get('instructionDocLineId') && <NumberField />,
    },
    {
      name: 'materialLotCode',
      width: 150,
    },
  ];

  const selectPrinter = (printerList) => {
    printerModal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.info.printer`).d('请选择打印机'),
      destroyOnClose: true,
      closable: true,
      style: {
        width: 400,
      },
      children: (
        <React.Fragment>
          <Form>
            <Select
              clearButton={false}
              onChange={(value) => handlePrint(value)}
              placeholder={intl.get(`${modelPrompt}.info.printer`).d('请选择打印机')}
            >
              {printerList.map(i => (
                <Option key={i.value} value={i}>
                  {i.meaning}
                </Option>
              ))}
            </Select>
          </Form>

        </React.Fragment>
      ),
      footer: null,
    });
  };

  const handlePrint = async (printer) => {
    const validateFlag = await lineDs.current?.validate();
    if (lineDs.records.length && validateFlag) {
      const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-disassemble/barcode-print/ui`;
      const lineList = lineDs.toData();
      const params = {
        printer,
        printList: lineList,
      };
      myInstance
        .post(url, params)
        .then(res => {
          if (res.data.success) {
            notification.success({
              message: intl.get(`${modelPrompt}.success.print`).d('打印成功!'),
            });
            printerModal?.close();
          } else {
            if (res.data.statusCode === "PRINTER_CHOOSE") {
              return selectPrinter(res.data.attr);
            }
            printerModal?.close();
            notification.error({
              message: res.data.message || intl.get(`${modelPrompt}.error.print`).d('打印失败!'),
            });
          }
        });
    }
  };

  const handleSubmit = async (type) => {
    const validateFlag = await lineDs.current?.validate();
    if (lineDs.records.length && validateFlag) {
      const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-disassemble/submit/all/ui`;
      const headSelectList = entranceDs.selected?.map((ele) => ele.toData());
      const lineList = lineDs?.toData();
      const params = {
        erpFlag: type === 'ERP' ? "Y" : "N",
        barcodeList: headSelectList,
        todoCreateList: lineList,
      };
      myInstance
        .post(url, params)
        .then(res => {
          if (res.data.success) {
            notification.success({
              message: intl.get(`${modelPrompt}.success.submit`).d('提交成功!'),
            });
            lineDs.loadData(res.data.rows)
          } else {
            notification.error({
              message: res.data.message || intl.get(`${modelPrompt}.error.submit`).d('提交失败!'),
            });
          }
        });
    }
  };

  const onFieldEnterDown = () => {
    entranceDs.query(entranceDs.currentPage);
  }


  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.AssemblyAndDisassembly.view.title')
          .d('组装拆卸')}
      />
      <Content>
        <Table
          customizedCode="AssemblyAndDisassembly"
          queryBar={TableQueryBarType.professionalBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
          dataSet={entranceDs}
          columns={columns}
          highLightRow
          style={{ height: 330 }}
          virtual
          virtualCell
        />
        <Collapse bordered={false} defaultActiveKey={['AssemblyInformation']}>
          <Panel
            header={intl.get(`${modelPrompt}.Assembly.information`).d('组装信息')}
            key="AssemblyInformation"
            dataSet={lineDs}
          >
            <Table
              customizedCode="AssemblyLine"
              dataSet={lineDs}
              highLightRow={false}
              columns={lineColumns}
              style={{ height: 230 }}
              virtual
              virtualCell
            />
          </Panel>
        </Collapse>
        <div>
          <Button
            style={{
              float: "right",
              marginRight: 15,
            }}
            color={ButtonColor.primary}
            onClick={() => handlePrint(null)}
          >
            {intl.get(`${modelPrompt}.button.print`).d('打印')}
          </Button>
          <Button
            style={{
              float: "right",
              marginRight: 15,
            }}
            color={ButtonColor.primary}
            onClick={() => handleSubmit('ERP')}
          >
            {intl.get(`${modelPrompt}.button.submit.erp`).d('提交(ERP)')}
          </Button>
          <Button
            style={{
              float: "right",
              marginRight: 15,
            }}
            color={ButtonColor.primary}
            onClick={() => handleSubmit('MES')}
          >
            {intl.get(`${modelPrompt}.button.submit.mes`).d('提交(MES)')}
          </Button>
        </div>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.AssemblyAndDisassembly', 'tarzan.common'],
})(AssemblyAndDisassembly);
