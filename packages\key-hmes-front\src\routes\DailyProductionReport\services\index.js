import { getCurrentOrganizationId } from 'utils/utils';
import { Host } from '@/utils/config';

const API = `${Host}`;
// const API =  '/yp-mes-25223';

export function QueryData() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-product-report/query/for/ui`,
    method: 'GET',
  };
}

export function SaveData() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-product-report/save/for/ui`,
    method: 'post',
  };
}
export function ExportExcel() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-product-report/export/for/ui`,
    method: 'GET',
    responseType: 'blob',
  };
}
