import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Collapse } from 'choerodon-ui';
// import { Spin } from 'choerodon-ui/pro/lib';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import ReactEchartsCore from 'echarts-for-react/lib/core';
import axios from 'axios';
import { HALM_ATN } from 'alm/utils/config';
import styles from './index.module.less';

const organizationId = getCurrentOrganizationId();
const url = `${HALM_ATN}/v1/${organizationId}/asset-cards/asset-dept-display`;

// 标签字符串改为4个字节一换行
const LabelFormatter = (value: string) => {
  let res = '';
  let realLength = 0;
  let subStart = 0;
  const valLength = value.length;
  for (let i = 0; i < valLength; i++) {
    let temp = '';
    const charCode = value.charCodeAt(i);
    let charLength = 0;
    if (charCode >= 0 && charCode <= 128) {
      charLength = 1;
    } else {
      charLength = 2;
    }
    if (realLength + charLength > 4) {
      realLength = charLength;
      if (subStart > 0) {
        temp = `${value.substring(subStart, i - 1)}...`;
        res += temp;
        return res;
      } else {
        temp = `${value.substring(subStart, i)}\n`;
        res += temp;
        subStart = i;
      }
    } else {
      realLength += charLength;
    }
  }
  const temp = `${value.substring(subStart, valLength)}`;
  res += temp;
  return res;
};
const totalValueFormatter = (value: number) => {
  if (value > 10000) {
    return `${(value / 10000).toFixed(2)}`;
  } else {
    return `${value}`;
  }
};

const AssetUsageCard = () => {
  const [data, setData] = useState<any>(undefined);
  useEffect(() => {
    fetchData();
  }, []);
  const fetchData = useCallback(async () => {
    const res = await axios.get<any, any>(url);
    setData(res);
  }, []);
  const renderTooltip = useCallback(params => {
    const name = `<span style="color:#282828;font-weight:bold;">${params[0].axisValueLabel}</span><br/>`;
    const icon1 = `<span style="margin: 8px;height:12px;width:12px;display:inline-block;vertical-align:middle;position:relative;top:-1px;background-color:#4B94FF"></span>`;
    const value1 = `<span style="color:#333333;"> ${params[0].seriesName}：${params[0].value}（万元）</span><br/>`;
    const icon2 = `<span style="margin: 8px;height:12px;width:12px;display:inline-block;vertical-align:middle;position:relative;top:-1px;background-color:#F9C246"></span>`;
    const value2 = `<span style="color:#333333;">  ${params[1].seriesName}：${params[1].value} </span>`;
    return name + icon1 + value1 + icon2 + value2;
  }, []);
  const option = useMemo(() => {
    return {
      grid: {
        top: 60,
        left: 15,
        right: 10,
        bottom: 16,
        containLabel: true,
      },
      legend: {
        show: true,
        top: 0,
        right: 0,
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          height: 12,
          fontSize: 12,
          padding: [3, 0, 0, 0],
        },
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: '#FFF',
        extraCssText: 'box-shadow: 0 2px 10px 5px rgba(124,133,155,0.10);',
        formatter: params => renderTooltip(params),
      },
      xAxis: [
        {
          type: 'category',
          axisTick: {
            length: 3,
            alignWithLabel: true,
          },
          data: data?.deptCardDetailDTOS.map(i => i.usingOrgName),
          axisLabel: {
            interval: 0,
            color: '#A9A9A9',
            formatter: LabelFormatter,
          },
          axisLine: {
            lineStyle: {
              color: '#CCCCCC',
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '单位：万元',
          splitLine: {
            show: false,
          },
          axisTick: { show: false },
          axisLine: {
            lineStyle: {
              color: '#CCCCCC',
            },
          },
          nameTextStyle: {
            color: '#A9A9A9',
          },
          axisLabel: {
            color: '#A9A9A9',
          },
          position: 'left',
        },
        {
          type: 'value',
          name: '数量',
          splitLine: {
            show: false,
          },
          axisTick: { show: false },
          axisLine: {
            lineStyle: {
              color: '#CCCCCC',
            },
          },
          axisLabel: {
            color: '#A9A9A9',
          },
          position: 'right',
        },
      ],
      series: [
        {
          name: '金额',
          type: 'bar',
          barGap: 0,
          barWidth: '8',
          itemStyle: {
            color: '#4B94FF',
          },
          data: data?.deptCardDetailDTOS.map(i => (i.assetValue / 10000).toFixed(2)),
        },
        {
          name: '数量',
          type: 'bar',
          yAxisIndex: 1,
          barWidth: '8',
          itemStyle: {
            color: '#F9C246',
          },
          data: data?.deptCardDetailDTOS.map(i => i.assetAmount),
        },
      ],
      dataZoom: {
        realtime: true,
        height: 10,
        start: 0,
        end: 25,
        bottom: 0,
      },
    };
  }, [data]);
  return (
    <div className={styles.container}>
      <Collapse
        bordered={false}
        expandIconPosition="right"
        defaultActiveKey={['A']}
        trigger="icon"
        className={styles['customize-collapse']}
      >
        <Collapse.Panel key="A" showArrow={false} header="各部门资产使用情况">
          <div className={styles.chartLegends}>
            <span>
              {`资产总值${data?.allAssetValue > 10000 ? '(万元)' : ''}`}
              <span className={styles.value}>{totalValueFormatter(data?.allAssetValue)}</span>
            </span>
            <span>
              资产总数 <span className={styles.amount}>{data?.allAssetAmount}</span>
            </span>
          </div>
          <ReactEchartsCore
            echarts={echarts}
            option={option}
            notMerge
            lazyUpdate
            style={{
              height: '100%',
            }}
          />
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default AssetUsageCard;
