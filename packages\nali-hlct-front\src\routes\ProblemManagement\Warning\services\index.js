/**
 * @Description:问题警示平台-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-06-15 11:37:54
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

// 详情数据查询
export function fetchWarning() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/detail/query/for/ui`,
    method: 'GET',
  };
}

// 详情数据保存
export function saveWarning() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/save/for/ui`,
    method: 'POST',
  };
}

// 整单提交
export function warningSubmitConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/submit/for/ui`,
    method: 'POST',
  };
}

// 整单发布
export function warningPublishConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/published/for/ui`,
    method: 'POST',
  };
}

// 整单关闭
export function warningClosedConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/closed/for/ui`,
    method: 'POST',
  };
}

// 行提交
export function warningLineSubmitConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/task/submit/for/ui`,
    method: 'POST',
  };
}
// 行取消
export function warningLineCancelConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/cancel/for/ui`,
    method: 'POST',
  };
}

// 行评论
export function warningLineEvaluateConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/follow/up/evaluate/for/ui`,
    method: 'POST',
  };
}

// 行审核
export function warningLineReviewConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/review/for/ui`,
    method: 'POST',
  };
}

// 获取用户部门
export function getUserDepartmentConfig() {
  return {
    url: `/hpfm/v1/${tenantId}/employee-assigns`,
    method: 'GET',
  };
}

// 变更启用提醒
export function ChangeRemindFlagConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-warn-list/remind-flag-update/ui`,
    method: 'POST',
  };
}
