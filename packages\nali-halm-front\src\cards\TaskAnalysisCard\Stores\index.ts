import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const queryDS: () => DataSetProps = () => {
  return {
    autoCreate: true,
    fields: [
      {
        name: 'dateUnitCode',
        type: FieldType.string,
        lookupCode: 'HALM.DATE_UNIT',
        defaultValue: 'DAY',
      },
    ],
  };
};
export default queryDS;
