/**
 * @Description: 检验平台-详情界面
 * @Author: <<EMAIL>>
 * @Date: 2023-02-13 11:20:33
 * @LastEditTime: 2023-06-14 10:53:51
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { observer } from 'mobx-react';
import {
  Attachment,
  Button,
  Currency,
  DataSet,
  DateTimePicker,
  Dropdown,
  Form,
  Lov,
  Menu,
  Modal,
  NumberField,
  Select,
  Spin,
  Table,
  TextField,
} from 'choerodon-ui/pro';
import { Tabs, Tag, Popover } from 'choerodon-ui';
import { findKey, isNumber, isUndefined, maxBy, omit } from 'lodash';
import moment from 'moment';
import uuid from 'uuid/v4';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { Action } from 'choerodon-ui/es/trigger/enum';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentUser } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import {
  DisposalModeDS,
  DocInfoDS,
  InspectInfoDS,
  InspectItemColDS,
  InspectItemRowDS,
  InspectItemRowObjValueDS,
  InspectLovDS,
  InspectObjDS,
} from '../stories/DetailDS';
import {
  AddInspectObj,
  CopyInspectObj,
  FetchDocInfo,
  FetchInspectInfo,
  FetchSamplingQty,
  GetCalculateFormula,
  NcReview,
  NcReviewSubmit,
  SaveInspectInfo,
  ScanBarcode,
  ScanInspectObj,
  StartInspectInfo,
  SubmitInspectInfo,
  ItemDetailDelete,
  EditingSaveInfo,
} from '../services';
import DocInfoComponent from './DocInfoComponent';
import InspectObjComponent from './InspectObjComponent';
import InspectInfoComponent from './InspectInfoComponent';
import NcRecordComponent from './NcRecordComponent';
import DetailComponent from '@/routes/InspectItemMaintain/components/DetailComponent';
import styles from './index.modules.less';
import TabInfoBox from './TabInfoBox';

const modelPrompt = 'tarzan.qms.inspectionPlatform';
const userInfo = getCurrentUser();
const { TabPane } = Tabs;
const { Option } = Select;
const { ItemGroup, Item } = Form;

const InspectionPlatformDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    history,
    docInfoDS,
    inspectObjDS,
    inspectInfoDS,
    inspectItemRowDS,
    inspectItemRowObjValueDS,
    inspectItemColDS,
    inspectDeleteItemColDS,
    customizeTable,
    customizeForm,
  } = props;

  const childRef = useRef<any>();
  enum ModeType {
    col = 'COL_MODE',
    row = 'ROW_MODE',
    rowObj = 'ROW_OBJ_MODE',
  }
  enum NcRecordDimension {
    taskNc = 'TASK_NC',
    itemNc = 'ITEM_NC',
  }
  enum FormulaDimension {
    sameObject = 'SAME_OBJECT',
    sameSequence = 'SAME_SERIAL_NUMBER',
    sameItem = 'SAME_ITEM',
  }

  // 新增检验项
  const inspectLovDS = useMemo(() => new DataSet({ ...InspectLovDS() }), []);
  // 处置方法
  const disposalModeDS = useMemo(() => new DataSet({ ...DisposalModeDS() }), []);

  // 是否可编辑
  const [canEdit, setCanEdit] = useState(false);

  // 是否处置状态
  const [disposalStatus, setDisposalStatus] = useState(false);
  // tab键
  const [tabKey, setTabKey] = useState('INSPECT_INFO');

  const [showButtonFlag, setShowButtonFlag] = useState(false);
  // 检验对象明细表格勾选数据
  const [tabInspectObjSelectList, setTabInspectObjSelectList] = useState([]);
  // 合格数校验标识
  const [minOkQtyFlag, setMinOkQtyFlag] = useState(false);
  // 检验项列模式主键值
  const [itemColKeyCount, setItemColKeyCount] = useState(1);
  // 检验项列模式展示字段
  const [itemColColumns, setItemColColumns] = useState([]);
  // 检验项列模式勾选数据
  const [itemColSelectList, setItemColSelectList] = useState([]);
  // 列模式表格触发渲染
  const [colShowFlag, setColShowFlag] = useState(true);
  // 检验项行模式勾选数据
  const [itemRowSelectList, setItemRowSelectList] = useState([]);
  // 存储新建检验项目行相关信息
  const [cacheAddLineItemInfo, setCacheAddLineItemInfo] = useState({});
  // 带对象行模式动态宽度
  const [cacheMinWidth, setCacheMinWidth] = useState(180);
  // 处置结论
  const [disposalType, setDisposalType] = useState(null);
  // 处置结论含义
  const [disposalTypeDesc, setDisposalTypeDesc] = useState('');
  // 处置方式
  const [disposalMode, setDisposalMode] = useState(null);
  // 取消和修改保存按钮控制
  const [editingBtn, setEditingBtn] = useState(false);

  const [inspectResultNow, setInspectResultNow] = useState('');

  // 监听检验对象明细表格勾选数据
  const handleInspectObjTableSelect = ({ dataSet }) => {
    setTabInspectObjSelectList(dataSet.selected || []);
  };
  useDataSetEvent(inspectObjDS, 'select', handleInspectObjTableSelect);
  useDataSetEvent(inspectObjDS, 'selectAll', handleInspectObjTableSelect);
  useDataSetEvent(inspectObjDS, 'unselect', handleInspectObjTableSelect);
  useDataSetEvent(inspectObjDS, 'unselectAll', handleInspectObjTableSelect);
  // 监听检验项列模式勾选数据
  const handleInspectColTableSelect = ({ dataSet }) => {
    setItemColSelectList(dataSet.selected || []);
  };
  useDataSetEvent(inspectItemColDS, 'select', handleInspectColTableSelect);
  useDataSetEvent(inspectItemColDS, 'selectAll', handleInspectColTableSelect);
  useDataSetEvent(inspectItemColDS, 'unselect', handleInspectColTableSelect);
  useDataSetEvent(inspectItemColDS, 'unselectAll', handleInspectColTableSelect);
  // 监听检验项行模式勾选数据
  const handleInspectRowTableSelect = ({ dataSet }) => {
    setItemRowSelectList(dataSet.selected || []);
  };
  useDataSetEvent(inspectItemRowDS, 'select', handleInspectRowTableSelect);
  useDataSetEvent(inspectItemRowDS, 'selectAll', handleInspectRowTableSelect);
  useDataSetEvent(inspectItemRowDS, 'unselect', handleInspectRowTableSelect);
  useDataSetEvent(inspectItemRowDS, 'unselectAll', handleInspectRowTableSelect);
  // 监听检验项记录值修改
  const handleInspectInfoUpdate = ({ dataSet, name, value, oldValue }) => {
    if(name === 'inspectResult') {
      setInspectResultNow(value);
    }
    if (
      dataSet.getState('docChangeFlag') !== 'Y' &&
      inspectInfoDS.current?.get('startInspectingFlag') !== 'Y' &&
      ['ngQty', 'scrapQty', 'inspectResult'].includes(name)
    ) {
      dataSet.setState('docChangeFlag', 'Y');
    }
    // 任务状态为下达录入值时调用开始检验
    if (
      name !== 'startInspectingFlag' &&
      inspectInfoDS.current?.get('inspectTaskStatus') === 'RELEASED' &&
      inspectInfoDS.current?.get('startInspectingFlag') !== 'Y' &&
      inspectInfoDS.getState('lineChangeFlag') !== 'Y'
    ) {
      handleItemStartInspect();
    }
    // 检验结果变化时计算整单合格或不合格的数量
    if (name === 'inspectResult' && value !== oldValue && value) {
      onComputedInspectObjData('TASK', null, []);
    }
  };
  useDataSetEvent(inspectInfoDS, 'update', handleInspectInfoUpdate);
  // 监听输入框修改触发开始检验调用
  const handleEnterDSUpdate = ({ record, name, value, oldValue }) => {
    // 任务状态为下达录入值时调用开始检验
    if (
      inspectInfoDS.current?.get('inspectTaskStatus') === 'RELEASED' &&
      inspectInfoDS.current?.get('startInspectingFlag') !== 'Y' &&
      inspectInfoDS.getState('lineChangeFlag') !== 'Y'
    ) {
      handleItemStartInspect();
    }
    // 计算检验任务结果值
    if (name === 'inspectResult') {
      const _modeType = inspectInfoDS.getState('modeType');
      if (_modeType !== ModeType.col) {
        handleChangeInspectResult(value, oldValue, record);
      }
    }
  };
  useDataSetEvent(inspectItemColDS, 'update', handleEnterDSUpdate);
  useDataSetEvent(inspectItemRowDS, 'update', handleEnterDSUpdate);
  useDataSetEvent(inspectItemRowObjValueDS, 'update', handleEnterDSUpdate);

  // 单据信息查询
  const { run: fetchDocInfo, loading: fetchDocInfoLoading } = useRequest(FetchDocInfo(), {
    manual: true,
    needPromise: true,
  });
  // 添加检验对象
  const { run: addInspectObj, loading: addInspectObjLoading } = useRequest(AddInspectObj(), {
    manual: true,
    needPromise: true,
  });
  // 查询检验项信息
  const { run: fetchInspectInfo, loading: fetchInspectInfoLoading } = useRequest(
    FetchInspectInfo(),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 开始检验
  const { run: startInspectInfo, loading: startInspectInfoLoading } = useRequest(
    StartInspectInfo(),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 保存
  const { run: saveInspectInfo, loading: saveInspectInfoLoading } = useRequest(SaveInspectInfo(), {
    manual: true,
    needPromise: true,
  });
  // 修改保存
  const { run: editingSaveInfo, loading: editingSaveLoading } = useRequest(EditingSaveInfo(), {
    manual: true,
    needPromise: true,
  });
  // 保存并提交
  const { run: submitInspectInfo, loading: submitInspectInfoLoading } = useRequest(
    SubmitInspectInfo(),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 删除检验对象明细
  const { run: itemDetailDelete, loading: itemDetailDeleteLoading } = useRequest(
    ItemDetailDelete(),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 获取条码或单据信息
  const { run: scanBarcode, loading: scanBarcodeLoading } = useRequest(ScanBarcode(), {
    manual: true,
    needPromise: true,
  });
  // 扫描检验对象传输
  const { run: scanInspectObj, loading: scanInspectObjLoading } = useRequest(ScanInspectObj(), {
    manual: true,
    needPromise: true,
  });
  // 复制检验值
  const { run: copyInspectObj, loading: copyInspectObjLoading } = useRequest(CopyInspectObj(), {
    manual: true,
    needPromise: true,
  });
  // 获取抽样数量
  const { run: fetchSamplingQty, loading: fetchSamplingQtyLoading } = useRequest(
    FetchSamplingQty(),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 获取计算公式所得值
  const { run: getCalculateFormula, loading: getCalculateFormulaLoading } = useRequest(
    GetCalculateFormula(),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 发起不良审批
  const { run: ncReview, loading: ncReviewLoading } = useRequest(NcReview(), {
    manual: true,
    needPromise: true,
  });
  // 发起不良处置
  const { run: ncReviewSubmit, loading: ncReviewSubmitLoading } = useRequest(NcReviewSubmit(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if (id === 'create') {
      return;
    }
    initPageData();
  }, [id]);

  // 触发列模式表格头变化重新渲染
  useEffect(() => {
    if (!colShowFlag) {
      setColShowFlag(true);
    }
  }, [colShowFlag]);

  // 处置结论变更获取含义
  useEffect(() => {
    if (disposalType === 'PART') {
      const _disposalTypeList = inspectInfoDS.getField('disposalType')?.options?.records || [];
      let _disposalTypeDesc: string = disposalType;
      if (_disposalTypeList.length > 0) {
        const _disposalTypeInfo = _disposalTypeList.filter(
          record => record.get('value') === disposalType,
        );
        if (_disposalTypeInfo.length > 0) {
          _disposalTypeDesc = _disposalTypeInfo[0].get('meaning');
        }
      } else if (inspectInfoDS.current?.get('disposalTypeDesc')) {
        _disposalTypeDesc = inspectInfoDS.current?.get('disposalTypeDesc');
      }
      setDisposalTypeDesc(_disposalTypeDesc);
    } else {
      setDisposalTypeDesc('');
    }
  }, [disposalType]);

  // 获取查询数据
  const initPageData = async () => {
    return fetchInspectInfo({
      params: {
        customizeUnitCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.HEAD`,
        inspectTaskId: id,
      },
      onSuccess: res => {
        const {
          docLines = [],
          docLineObjects = [],
          objectList = [],
          disposalData = {},
          ...head
        } = res;
        setShowButtonFlag(res.showButtonFlag);
        const _newDisposalType =
          head.editDisposalFlag === 'Y' &&
          !['INSPECTING', 'COMPLETED'].includes(head.inspectTaskStatus)
            ? null
            : head.disposalType || 'ALL';
        inspectInfoDS.loadData([
          {
            ...(head || {}),
            ...(disposalData || {}),
            initInspectResult: head?.inspectResult,
            reworkOperationId: disposalData?.reworkOperationId || null,
            reworkWorkcellId: disposalData?.reworkWorkcellId || null,
            reworkRouterId: disposalData?.reworkRouterId || null,
            degradeMaterialId: disposalData?.degradeMaterialId || null,
            disposalData,
            cacheDisposalType: head.disposalType,
            disposalType: _newDisposalType,
            remark: head.remark,
          },
        ]);
        setInspectResultNow(head?.inspectResult);
        docLines?.forEach(_docLine => {
          _docLine.maxSequence = Math.max(
            ...((_docLine?.taskLineObjects || [{ sequence: 0 }])?.map(item => {
              return item?.sequence || 0;
            }) || [0]),
          );
        });

        // 处置相关
        disposalModeDS.loadData(head.dispFunctionList || []);
        setDisposalType(_newDisposalType);
        setDisposalMode(head.curDispFunction);
        inspectInfoDS.setState('partDisposalFlag', 'Y');
        inspectInfoDS.setState('baseInfo', head);

        // LOV弹窗传参字段设置
        inspectLovDS.setState('inspectDocId', head?.inspectDocId);
        inspectLovDS.setState('inspectTaskId', head?.inspectTaskId);
        // 待领取及取消的任务不可编辑
        if (['CANCEL', 'COMPLETED'].includes(head.inspectTaskStatus)) {
          setCanEdit(false);
          inspectInfoDS.setState('canEdit', false);
        } else if (!head?.inspectorId || `${userInfo?.id}` === `${head?.inspectorId}`) {
          setCanEdit(true);
          inspectInfoDS.setState('canEdit', true);
        } else if (head.oneselfFlag === 'N') {
          setCanEdit(true);
          inspectInfoDS.setState('canEdit', true);
        } else {
          setCanEdit(false);
          inspectInfoDS.setState('canEdit', false);
        }

        // 行列模式检验项处理
        const _operatingFlag = inspectInfoDS.current?.get('operatingFlag');
        const _resultFlag = inspectInfoDS.current?.get('resultFlag');
        // 控制自动计算任务字段时不要调用开始检验
        inspectInfoDS.setState('lineChangeFlag', 'Y');
        if (_operatingFlag === 'Y') {
          // 列模式
          inspectInfoDS.setState('modeType', ModeType.col);
          onHandleColData(docLines || [], docLineObjects || []);
        } else if (_resultFlag === 'Y') {
          // 带对象行模式
          inspectInfoDS.setState('modeType', ModeType.rowObj);
          onHandleRowObjData(docLines || []);
        } else {
          // 不带对象行模式
          inspectInfoDS.setState('modeType', ModeType.row);
          onHandleRowData(docLines || []);
        }

        // 处理检验对象明细数据
        inspectObjDS.loadData(objectList || []);
        let _sumObjectQty = 0;
        (objectList || [])
          .filter(item => item.inspectFlag === 'Y')
          .forEach(item => {
            _sumObjectQty += Number(item.qty || 0);
          });
        inspectObjDS.setState('sumObjectQty', _sumObjectQty);

        // 下达和检验中的任务无结果时默认为合格
        if (['INSPECTING'].includes(head.inspectTaskStatus) && !head.inspectResult) {
          inspectInfoDS.current?.init('inspectResult', 'OK');
          setInspectResultNow('OK');
        }

        inspectInfoDS.setState('lineChangeFlag', 'N');
      },
    });
  };

  // 行列模式-数据清空
  const onHandleColReset = () => {
    setItemColKeyCount(1);
    setItemColColumns([]);
    setItemColSelectList([]);
    setItemRowSelectList([]);
    setCacheAddLineItemInfo({});
    inspectInfoDS.setState('itemColUpdateCacheObj', null);
    inspectInfoDS.setState('docChangeFlag', 'N');
    inspectInfoDS.setState('partDisposalFlag', 'N');
    docInfoDS.setState('queryFlag', 'N');
    inspectObjDS.setState('sumObjectQty', 0);
    inspectInfoDS.setState('cacheFormulaObj', {
      formulaObj: {},
      itemIdFieldNameObj: {},
      userDefinedSamplingFlag: 'N',
    });
    const _modeType = inspectInfoDS.getState('modeType');
    if (_modeType === ModeType.col) {
      inspectItemColDS.setState('cacheItemData', {});
      inspectItemColDS.setState('itemColDtlObj', {});
    } else if (_modeType === ModeType.row) {
      inspectItemRowDS.setState('modeType', null);
    }
    inspectInfoDS.setState('modeType', '');
    inspectLovDS.reset();
  };

  // 行模式DS添加检测值字段
  const onDSAddInspectValueField = (fieldName, targetDS, lineRequiredFlag, maxQty, minQty = 0) => {
    for (let i = minQty; i < maxQty; i++) {
      if (!targetDS.getField(`${fieldName}_VALUE${i}`)) {
        targetDS.addField(`${fieldName}_VALUE${i}`, {
          name: `${fieldName}_VALUE${i}`,
          type: FieldType.string,
          label: `${intl.get(`${modelPrompt}.model.line.inspectValue`).d('检测值')}`,
          dynamicProps: {
            required: ({ record, dataSet }) => {
              const _disabled = !!record?.get(`${fieldName}_DISABLED`);
              let _objRequiredFlag = true;
              const _modeType = inspectInfoDS.getState('modeType');
              if (_modeType === ModeType.rowObj) {
                _objRequiredFlag = record?.get('inspectObjectId');
              }
              return (
                record &&
                !['UNQUALIFIED_INSPECTION_TASK', 'UNQUALIFIED_ITEMS'].includes(
                  dataSet.getState('resultDimension'),
                ) &&
                !_disabled &&
                record.get('fieldName') === fieldName &&
                lineRequiredFlag === 'Y' &&
                _objRequiredFlag &&
                record.get('dataType') !== 'CALCULATE_FORMULA'
              );
            },
          },
        });
      }
    }
  };

  // 行模式(不带检验对象)-处理数据
  const onHandleRowData = docLines => {
    inspectItemRowDS.setState('modeType', ModeType.row);
    const _resultDimension = inspectInfoDS.current?.get('resultDimension');

    let _ngQty = 0;
    let _okQty = 0;
    let _maxSamplingQty = 0;

    // 计算公式关联检验项行
    // ITEM_ID_检验项ID -> 影响检验项ID数组
    const _formulaObj: any = {};
    // 检验项ID -> fieldName
    const _itemIdFieldNameObj: any = {};
    // 抽样数量是否存在可编辑
    let _userDefinedSamplingFlag = false;

    // 自设主键用于动态录入值
    let _itemColKeyCount = 1;
    const newDocLines = docLines.map(lineItem => {
      const _fieldName = `ITEM${_itemColKeyCount}`;
      _itemColKeyCount++;

      const _dataType = lineItem.dataType;

      let _samplingQty = 0;
      if (lineItem?.dataQtyDisposition === 'DATA') {
        _samplingQty = Number(lineItem.dataQty || 0);
      } else if (lineItem?.dataQtyDisposition === 'SAMPLE') {
        _samplingQty = Number(lineItem.samplingQty || 0);
      } else {
        _samplingQty = Number(lineItem.samplingQty || 0) * Number(lineItem.dataQty || 0);
      }

      const _falseValues = lineItem.falseValues || [];
      const _warningValues = lineItem.warningValues || [];
      const _trueValues = lineItem.trueValues || [];
      const _requiredFlag = lineItem.requiredFlag;
      lineItem.samplingQtyCount = _samplingQty;
      _maxSamplingQty = _samplingQty > _maxSamplingQty ? _samplingQty : _maxSamplingQty;

      const _inspectResult = lineItem.inspectResult;
      if (_inspectResult === 'NG') {
        _ngQty++;
      } else if (_inspectResult === 'OK') {
        _okQty++;
      }

      if (_dataType === 'CALCULATE_FORMULA') {
        (lineItem.formulaJson?.formulaList || []).forEach(formulaItem => {
          if (formulaItem.inspectItemId) {
            const _targetInspectItemIds = _formulaObj[`ITEM_ID_${formulaItem.inspectItemId}`] || [];
            if (!_targetInspectItemIds.includes(lineItem.inspectItemId)) {
              _targetInspectItemIds.push(lineItem.inspectItemId);
              _formulaObj[`ITEM_ID_${formulaItem.inspectItemId}`] = _targetInspectItemIds;
            }
          }
        });
      }
      _itemIdFieldNameObj[`ITEM_ID_${lineItem.inspectItemId}`] = _fieldName;

      // 存储录入值原始数据
      const _taskLineActDtlObj: any = {};
      let _maxSeq = 0;

      // DS添加检测值字段
      onDSAddInspectValueField(_fieldName, inspectItemRowDS, _requiredFlag, _samplingQty);

      // 拼接原始检验记录
      let _sourceInspectValue = '';
      (lineItem.sourceValues || []).forEach(sourceItem => {
        const { sourceInspectValue } = sourceItem;
        if (sourceInspectValue) {
          _sourceInspectValue += `${sourceInspectValue},`;
        }
      });
      if (_sourceInspectValue.length > 0) {
        _sourceInspectValue = _sourceInspectValue.substring(0, _sourceInspectValue.length - 1);
      }

      let _index = 0;

      // 计算公式数量
      let _formulaCount = 0;
      (lineItem.taskLineObjects || []).forEach(objItem => {
        (objItem.taskLineActDtls || []).forEach(dtlItem => {
          // 处理录入值
          lineItem[`${_fieldName}_VALUE${_index}`] = dtlItem.inspectValue;
          lineItem[`${_fieldName}_VALUE${_index}_ID`] = dtlItem.inspectDocLineActDtlId;
          _taskLineActDtlObj[dtlItem.inspectDocLineActDtlId] = dtlItem;
          _maxSeq =
            _maxSeq > Number(dtlItem.objectEnterValueSequence || 0)
              ? _maxSeq
              : Number(dtlItem.objectEnterValueSequence || 0);

          // 查询数据颜色处理
          if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(_dataType)) {
            lineItem[`${_fieldName}_VALUE${_index}_COLOR`] = onGetValueColor(
              dtlItem.inspectValue,
              _dataType,
              _falseValues,
              _warningValues,
              _trueValues,
            );
          }
          _index++;

          // 计算公式计数
          if (_dataType === 'CALCULATE_FORMULA') {
            _formulaCount++;
          }
        });
      });

      // 计算公式数量赋值
      if (_dataType === 'CALCULATE_FORMULA') {
        lineItem.formulaCount = _formulaCount;
      }

      // 抽样数量是否存在可编辑
      if (!_userDefinedSamplingFlag && lineItem.samplingType === 'USER_DEFINED_SAMPLING') {
        _userDefinedSamplingFlag = true;
      }

      _maxSeq++;
      return {
        ...lineItem,
        sourceInspectValue: _sourceInspectValue,
        fieldName: _fieldName,
        cacheMaxSeq: _maxSeq,
        cacheTaskLineActDtlObj: _taskLineActDtlObj,
        maxSamplingQty: lineItem.samplingQty,
      };
    });

    // 计算列宽
    if (_maxSamplingQty > 2) {
      inspectItemRowDS.setState('maxSamplingQty', _maxSamplingQty);
      setCacheMinWidth(_maxSamplingQty * 65 + (_maxSamplingQty - 1) * 5 + 16);
    }

    setItemColKeyCount(_itemColKeyCount);
    inspectItemRowDS.loadData(newDocLines);

    // 设置必输需要的判定字段
    inspectItemRowDS.setState('resultDimension', _resultDimension);
    inspectInfoDS.setState('cacheFormulaObj', {
      formulaObj: _formulaObj,
      itemIdFieldNameObj: _itemIdFieldNameObj,
      userDefinedSamplingFlag: _userDefinedSamplingFlag ? 'Y' : 'N',
    });

    // 计算任务结果值
    if (
      !inspectInfoDS.current?.get('inspectResult') &&
      ['INSPECTING'].includes(inspectInfoDS.current?.get('inspectTaskStatus'))
    ) {
      inspectInfoDS.current?.init('inspectResult', _ngQty > 0 ? 'NG' : _okQty > 0 ? 'OK' : null);
    }

    // 计算合格数和不合格数
    inspectItemRowDS.records.forEach(record => {
      const { okQty, ngQty, samplingQty } = record.data;
      const _newOkQty =
        okQty || okQty === 0 ? okQty : Number(samplingQty || 0) - Number(ngQty || 0);
      record.init('okQty', _newOkQty);
      record.init(
        'ngQty',
        ngQty || ngQty === 0 ? ngQty : Number(samplingQty || 0) - Number(_newOkQty || 0),
      );
    });
  };

  // 行模式(带检验对象)-处理数据
  const onHandleRowObjData = docLines => {
    const _resultDimension = inspectInfoDS.current?.get('resultDimension');

    let _ngQty = 0;
    let _okQty = 0;

    // 计算公式关联检验项行
    // ITEM_ID_检验项ID -> 影响检验项ID数组
    const _formulaObj: any = {};
    // 检验项ID -> fieldName
    const _itemIdFieldNameObj: any = {};
    // 抽样数量是否可编辑
    let _userDefinedSamplingFlag = false;

    // 自设主键用于动态录入值
    let _itemColKeyCount = 1;
    const newDocLines = docLines.map((lineItem, lineIndex) => {
      const _fieldName = `ITEM${_itemColKeyCount}`;
      _itemColKeyCount++;

      const _dataType = lineItem.dataType;
      const _dataQtyDisposition = lineItem.dataQtyDisposition;
      let _dataQty = 0;
      if (_dataQtyDisposition === 'DATA' || _dataQtyDisposition === 'SAMPLE') {
        _dataQty = 1;
      } else {
        _dataQty = Number(lineItem.dataQty || 0);
      }

      const _falseValues = lineItem.falseValues || [];
      const _warningValues = lineItem.warningValues || [];
      const _trueValues = lineItem.trueValues || [];
      const _requiredFlag = lineItem.requiredFlag;

      const _inspectResult = lineItem.inspectResult;
      if (_inspectResult === 'NG') {
        _ngQty++;
      } else if (_inspectResult === 'OK') {
        _okQty++;
      }

      if (_dataType === 'CALCULATE_FORMULA') {
        (lineItem.formulaJson?.formulaList || []).forEach(formulaItem => {
          if (formulaItem.inspectItemId) {
            const _targetInspectItemIds = _formulaObj[`ITEM_ID_${formulaItem.inspectItemId}`] || [];
            if (!_targetInspectItemIds.includes(lineItem.inspectItemId)) {
              _targetInspectItemIds.push(lineItem.inspectItemId);
              _formulaObj[`ITEM_ID_${formulaItem.inspectItemId}`] = _targetInspectItemIds;
            }
          }
        });
      }
      _itemIdFieldNameObj[`ITEM_ID_${lineItem.inspectItemId}`] = _fieldName;

      // DS添加检测值字段
      onDSAddInspectValueField(_fieldName, inspectItemRowObjValueDS, _requiredFlag, _dataQty);

      // 如果检验对象数量少于抽样数则自动添加，不为计算公式时才自动添加
      // 不为自定义抽样时才添加
      if (_dataType !== 'CALCULATE_FORMULA' && lineItem.samplingType !== 'USER_DEFINED_SAMPLING') {
        const _samplingQty = Number(lineItem.samplingQty || 0);
        const _lens = (lineItem.taskLineObjects || []).length;
        if (_lens < 1) {
          lineItem.taskLineObjects = [];
        }
        if (_lens < _samplingQty) {
          for (let i = _lens; i < _samplingQty; i++) {
            lineItem.taskLineObjects.push({});
          }
        }
      }

      // 拼接原始检验记录
      let _sourceInspectValue = '';
      (lineItem.sourceValues || []).forEach(sourceItem => {
        const { sourceInspectValue } = sourceItem;
        if (sourceInspectValue) {
          _sourceInspectValue += `${sourceInspectValue},`;
        }
      });
      if (_sourceInspectValue.length > 0) {
        _sourceInspectValue = _sourceInspectValue.substring(0, _sourceInspectValue.length - 1);
      }

      let _maxFormulaCount = 0;
      lineItem.taskLineObjects = (lineItem.taskLineObjects || []).map(objItem => {
        const _dtlIds: Array<any> = [];

        // 计算公式数量
        let _formulaCount = 0;

        (objItem.taskLineActDtls || []).forEach((dtlItem, index) => {
          _dtlIds.push(dtlItem.inspectDocLineActDtlId);

          // 处理录入值
          objItem[`${_fieldName}_VALUE${index}`] = dtlItem.inspectValue;
          objItem[`${_fieldName}_VALUE${index}_ID`] = dtlItem.inspectDocLineActDtlId;
          objItem.remark = dtlItem.remark;

          // 查询数据颜色处理
          if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(_dataType)) {
            objItem[`${_fieldName}_VALUE${index}_COLOR`] = onGetValueColor(
              dtlItem.inspectValue,
              _dataType,
              _falseValues,
              _warningValues,
              _trueValues,
            );
          }

          // 计算公式计数
          if (_dataType === 'CALCULATE_FORMULA') {
            _formulaCount++;
          }
        });

        // 计算公式数量赋值
        if (_dataType === 'CALCULATE_FORMULA') {
          objItem[`${_fieldName}_FORMULA_COUNT`] = _formulaCount;
          _maxFormulaCount = _maxFormulaCount > _formulaCount ? _maxFormulaCount : _formulaCount;
        }

        return {
          ...objItem,
          cacheInspectObjectId: uuid(),
          dtlIds: _dtlIds,
          fieldName: _fieldName,
          requiredFlag: _requiredFlag,
          dataType: _dataType,
        };
      });

      // 计算列宽
      if (lineIndex === 0) {
        const _count = _dataType === 'CALCULATE_FORMULA' ? _maxFormulaCount : Number(_dataQty || 0);
        if (_count > 2) {
          const _countWidth = _count * 65 + (_count - 1) * 5 + 16;
          setCacheMinWidth(_countWidth);
        }
      }
      if (_dataType === 'CALCULATE_FORMULA') {
        lineItem._maxFormulaCount = _maxFormulaCount;
      }

      // 抽样数量是否存在可编辑
      if (!_userDefinedSamplingFlag && lineItem.samplingType === 'USER_DEFINED_SAMPLING') {
        _userDefinedSamplingFlag = true;
      }

      return {
        ...lineItem,
        sourceInspectValue: _sourceInspectValue,
        fieldName: _fieldName,
      };
    });
    setItemColKeyCount(_itemColKeyCount);
    inspectItemRowDS.loadData(newDocLines);
    inspectInfoDS.setState('cacheFormulaObj', {
      formulaObj: _formulaObj,
      itemIdFieldNameObj: _itemIdFieldNameObj,
      userDefinedSamplingFlag: _userDefinedSamplingFlag ? 'Y' : 'N',
    });

    // 设置必输需要的判定字段
    inspectItemRowDS.setState('resultDimension', _resultDimension);
    inspectItemRowObjValueDS.setState('resultDimension', _resultDimension);

    // 录入值禁用处理
    inspectItemRowDS.records.forEach(record => {
      const _fieldName = record.get('fieldName');
      const _dataType = record.get('dataType');
      handleComputedQty(_fieldName, _dataType, true, false, record, false);
    });

    // 计算任务结果值
    if (
      !inspectInfoDS.current?.get('inspectResult') &&
      ['INSPECTING'].includes(inspectInfoDS.current?.get('inspectTaskStatus'))
    ) {
      inspectInfoDS.current?.init('inspectResult', _ngQty > 0 ? 'NG' : _okQty > 0 ? 'OK' : null);
    }

    // 计算合格数和不合格数
    inspectItemRowDS.records.forEach(record => {
      const { okQty, ngQty, samplingQty } = record.data;
      const _newOkQty =
        okQty || okQty === 0 ? okQty : Number(samplingQty || 0) - Number(ngQty || 0);
      record.init('okQty', _newOkQty);
      record.init(
        'ngQty',
        ngQty || ngQty === 0 ? ngQty : Number(samplingQty || 0) - Number(_newOkQty || 0),
      );
    });
  };

  // 列模式-处理数据
  const onHandleColData = (docLines, docLineObjects) => {
    const _resultDimension = inspectInfoDS.current?.get('resultDimension');

    // 展示的列
    const _addFields: Array<any> = [];
    const _itemColColumns: any = [
      {
        name: 'inspectItem',
        width: 150,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          if (inspectInfoDS.getState('lineChangeFlag') === 'Y') {
            return '';
          }
          const _canEdit = inspectInfoDS.getState('canEdit');
          if (record?.get('cacheInspectObjectId') && _resultDimension === 'RECORD_SAMPLE_VALUE') {
            return (
              <TextField
                record={record}
                name="inspectItem"
                disabled={!_canEdit}
                onChange={(val, oldValue) => handleChangeInspectItem(val, oldValue, record)}
                onEnterDown={event => handleItemScanInspectObj(event, record)}
                style={{ verticalAlign: 'baseline' }}
              />
            );
          }
          const _inspectItemKey = record?.get('inspectItemKey');
          if (['inspectResult'].includes(_inspectItemKey)) {
            // 批量编辑结论
            return (
              <a
                onClick={() => handleBatchChangeData('BATCH_RESULT')}
                style={{ cursor: 'pointer' }}
              >
                {value}
              </a>
            );
          }
          return value;
        },
      },
    ];

    // 自设主键
    let _itemColKeyCount = 1;
    // 检验行或项目信息
    const _cacheItemData: any = {};
    // 检验行对应自设主键
    const _docLineIdKeyObj: any = {};
    // 检验项行ID对应数据用于查询颜色处理
    const _docLineDataObj: any = {};

    let _ngQty = 0;

    // 计算公式关联检验项行
    // ITEM_ID_检验项ID -> 影响检验项ID数组
    const _formulaObj: any = {};
    // 检验项ID -> fieldName
    const _itemIdFieldNameObj: any = {};
    // 不为自定义抽样的列
    const _noUserSamplingFields: string[] = [];

    const _sourceTaskId = inspectInfoDS.current?.get('sourceTaskId');

    // 处理界面渲染和DS的数据
    docLines.forEach(item => {
      // 唯一列名
      const fieldName = `ITEM${_itemColKeyCount}`;
      _addFields.push(fieldName);

      // DS添加数据及缓存数据
      const _lineData = onHandleCacheData(fieldName, item);
      _cacheItemData[fieldName] = _lineData;
      _docLineDataObj[item.inspectDocLineId] = {
        dataType: _lineData.dataType,
        falseValues: _lineData.falseValues,
        warningValues: _lineData.warningValues,
        trueValues: _lineData.trueValues,
      };

      if (_lineData.dataType === 'CALCULATE_FORMULA') {
        (_lineData.formulaJson?.formulaList || []).forEach(formulaItem => {
          if (formulaItem.inspectItemId) {
            const _targetInspectItemIds = _formulaObj[`ITEM_ID_${formulaItem.inspectItemId}`] || [];
            if (!_targetInspectItemIds.includes(item.inspectItemId)) {
              _targetInspectItemIds.push(item.inspectItemId);
              _formulaObj[`ITEM_ID_${formulaItem.inspectItemId}`] = _targetInspectItemIds;
            }
          }
        });
      }
      _itemIdFieldNameObj[`ITEM_ID_${item.inspectItemId}`] = fieldName;

      let _dataQty = 0;
      if (item.dataQtyDisposition === 'DATA' || item.dataQtyDisposition === 'SAMPLE') {
        _dataQty = 1;
      } else {
        _dataQty = Number(item.dataQty || 0);
      }

      // 计算列宽
      let _minWidth = 180;
      let _count =
        _lineData.dataType === 'CALCULATE_FORMULA' ? Number(_dataQty || 3) : Number(_dataQty || 0);
      if (_sourceTaskId && _sourceTaskId !== 0 && _sourceTaskId !== '0') {
        _count++;
      }
      if (_count > 2) {
        const _countWidth = _count * 65 + (_count - 1) * 5 + 16;
        if (_countWidth > _minWidth) {
          _minWidth = _countWidth;
        }
      }
      // 渲染动态单元格
      const _columnInfo = onHandleRenderCell(fieldName);
      _itemColColumns.push({
        ..._columnInfo,
        width: _minWidth,
        minWidth: 180,
      });

      _docLineIdKeyObj[item.inspectDocLineId] = fieldName;

      _itemColKeyCount++;

      if (item.inspectResult === 'NG') {
        _ngQty++;
      }

      // 不为自定义抽样数量的数据需要判断是否禁用录入值
      if (item.samplingType !== 'USER_DEFINED_SAMPLING') {
        _noUserSamplingFields.push(fieldName);
      }
    });
    setItemColKeyCount(_itemColKeyCount);
    setItemColColumns(_itemColColumns);
    inspectItemColDS.setState('cacheItemData', _cacheItemData);
    inspectInfoDS.setState('cacheFormulaObj', {
      formulaObj: _formulaObj,
      itemIdFieldNameObj: _itemIdFieldNameObj,
      userDefinedSamplingFlag: 'N',
    });

    // 处理项目行数据
    const _rowItems = [
      {
        inspectItemKey: 'inspectItemTypeDesc',
        inspectItem: intl.get(`${modelPrompt}.model.line.inspectItemType`).d('类型'),
      },
      {
        inspectItemKey: 'inspectToolAndMethod',
        inspectItem: intl.get(`${modelPrompt}.model.line.inspectToolAndMethod`).d('检验工具/方法'),
      },
      {
        inspectItemKey: 'acceptStandard',
        inspectItem: intl.get(`${modelPrompt}.model.line.acceptStandard`).d('AC/RE'),
      },
      {
        inspectItemKey: 'samplingQty',
        inspectItem: intl.get(`${modelPrompt}.model.line.samplingQty`).d('抽样数量'),
      },
      {
        inspectItemKey: 'valueRange',
        inspectItem: intl.get(`${modelPrompt}.model.line.valueRange`).d('符合值/不符合值/预警值'),
      },
      {
        inspectItemKey: 'okAndNg',
        inspectItem: intl.get(`${modelPrompt}.model.line.okAndNg`).d('OK/NG数'),
      },
      {
        inspectItemKey: 'inspectResult',
        inspectItem: intl.get(`${modelPrompt}.model.line.determine`).d('判定'),
      },
      {
        inspectItemKey: 'operation',
        inspectItem: intl.get('tarzan.common.label.action').d('操作'),
      },
    ];
    _rowItems.forEach(_rowItem => {
      const { inspectItemKey } = _rowItem;
      _addFields.forEach((item, index) => {
        if (inspectItemKey === 'okAndNg') {
          _rowItem[`${item}_OK`] = docLines[index].okQty;
          _rowItem[`${item}_NG`] = docLines[index].ngQty;
        } else {
          _rowItem[item] = docLines[index][inspectItemKey];
          // 处理编辑字段逻辑
          if (inspectItemKey === 'inspectResult') {
            _rowItem[`${item}_RESULT`] = docLines[index][inspectItemKey];
          }
          if (inspectItemKey === 'actEnclosure') {
            _rowItem[`${item}_ENCLOSURE`] = docLines[index].enclosure;
          }
        }
      });
    });
    inspectItemColDS.loadData(_rowItems);
    // 处理行对象数据
    if ((docLineObjects || []).length < 1 && docLines.length > 0) {
      // 没有行对象数据则默认添加一条
      const _cacheId = uuid();
      const _addInfo: any = {
        cacheInspectObjectId: _cacheId,
        inspectItemKey: `OBJ-${_cacheId}`,
        sequence: 1,
      };
      if (_resultDimension !== 'RECORD_SAMPLE_VALUE') {
        _addInfo.inspectItem = '1';
      }
      docLineObjects.push(_addInfo);
    }

    // 用于存储实际录入值的信息
    const _itemColDtlObj: any = {};
    docLineObjects.forEach(obj => {
      const {
        inspectTaskObjectActId,
        inspectObjectId,
        sourceObjectCode,
        sequence,
        taskObjectLines,
      } = obj;
      const _cacheId = uuid();
      const _addObj: any = {
        cacheInspectObjectId: _cacheId,
        inspectItemKey: `OBJ-${_cacheId}`,
        inspectItem: _resultDimension === 'RECORD_SAMPLE_VALUE' ? sourceObjectCode : sequence,
        inspectTaskObjectActId,
        inspectObjectId,
        sourceObjectCode,
        sequence,
      };

      // 处理录入值数据
      const _dtlIds: Array<any> = [];

      // act
      (taskObjectLines || []).forEach(objLine => {
        const { inspectDocLineId, taskLineActDtls } = objLine;
        const _fieldName = _docLineIdKeyObj[inspectDocLineId];
        const _docLineData = _docLineDataObj[inspectDocLineId] || {};
        const _dataType = _docLineData.dataType;

        // 计算公式数量
        let _formulaCount = 0;

        // actDtl
        _addObj[`${_fieldName}_taskLineActDtlsLength`] = (taskLineActDtls || []).length;
        (taskLineActDtls || []).forEach((dtlItem, index) => {
          _addObj[`${_fieldName}_VALUE${index}`] = dtlItem.inspectValue;
          _itemColDtlObj[`KEY${_addObj.cacheInspectObjectId}_${_fieldName}_VALUE${index}`] = {
            ...dtlItem,
          };
          _dtlIds.push(dtlItem.inspectDocLineActDtlId);

          // 存储录入值最大序号
          let _maxSeq = Number(
            _itemColDtlObj[`KEY${_addObj.cacheInspectObjectId}_${_fieldName}_MAXSEQ`] || 1,
          );
          if (_maxSeq < Number(dtlItem.objectEnterValueSequence || 0)) {
            _maxSeq = dtlItem.objectEnterValueSequence;
          }
          _itemColDtlObj[`KEY${_addObj.cacheInspectObjectId}_${_fieldName}_MAXSEQ`] = _maxSeq;

          // 查询数据颜色处理
          if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(_dataType)) {
            _addObj[`${_fieldName}_VALUE${index}_COLOR`] = onGetValueColor(
              dtlItem.inspectValue,
              _dataType,
              _docLineData.falseValues,
              _docLineData.warningValues,
              _docLineData.trueValues,
            );
          }

          // 计算公式计数
          if (_dataType === 'CALCULATE_FORMULA') {
            _formulaCount++;
          }
        });

        // 计算公式数量赋值
        if (_dataType === 'CALCULATE_FORMULA') {
          _addObj[`${_fieldName}_FORMULA_COUNT`] = _formulaCount;
        }
      });
      _addObj.dtlIds = _dtlIds;
      _rowItems.push(_addObj);
      inspectItemColDS.appendData([_addObj]);
    });
    // 检验项列模式录入数据dltId
    inspectItemColDS.setState('itemColDtlObj', _itemColDtlObj);

    // 不为自定义抽样的列录入值禁用处理
    _noUserSamplingFields.forEach(fieldName => {
      const _fieldInfo = _cacheItemData[fieldName] || {};
      handleChangeSamplingQty(fieldName, _fieldInfo?.samplingQty || 0, false);
    });

    // 自动计算检验任务不合格数
    const _taskNgQty = inspectInfoDS.current?.get('ngQty');
    if (!_taskNgQty && _taskNgQty !== 0 && `${_taskNgQty}` !== `${_ngQty}`) {
      if (
        !inspectInfoDS.current?.get('inspectResult') &&
        ['INSPECTING'].includes(inspectInfoDS.current?.get('inspectTaskStatus'))
      ) {
        inspectInfoDS.current?.init('inspectResult', _ngQty > 0 ? 'NG' : 'OK');
      }
      inspectInfoDS.setState('docChangeFlag', 'N');
    }

    // 计算合格数和不合格数
    const _okAndNgRecord = inspectItemColDS.find(
      record => record.get('inspectItemKey') === 'okAndNg',
    );
    if (_okAndNgRecord) {
      for (let i = 1; i < _itemColColumns.length; i++) {
        const _fieldName = _itemColColumns[i].name;
        const _cacheInfo = _cacheItemData[_fieldName];
        const { okQty, ngQty, samplingQty } = _cacheInfo;
        const _newOkQty =
          okQty || okQty === 0 ? okQty : Number(samplingQty || 0) - Number(ngQty || 0);
        _okAndNgRecord.init(`${_fieldName}_OK`, _newOkQty);
        _okAndNgRecord.init(
          `${_fieldName}_NG`,
          ngQty || ngQty === 0 ? ngQty : Number(samplingQty || 0) - Number(_newOkQty || 0),
        );
      }
    }
    inspectItemColDS.setState('resultDimension', _resultDimension);
    inspectInfoDS.setState('resultDimension', _resultDimension);
  };

  // 列模式-DS添加数据及缓存数据
  const onHandleCacheData = (fieldName, item) => {
    let count = 0;
    if (item?.dataQtyDisposition === 'DATA' || item?.dataQtyDisposition === 'SAMPLE') {
      count = 1;
    } else {
      count = Number(item.dataQty || 0);
    }

    // DS添加字段
    inspectItemColDS.addField(fieldName, {
      name: fieldName,
      type: FieldType.string,
      label: item.inspectItemDesc,
    });

    // DS添加用于判断的下拉数据源的字段
    inspectItemColDS.addField(`${fieldName}_RESULT`, {
      name: `${fieldName}_RESULT`,
      type: FieldType.string,
      label: `${intl.get(`${modelPrompt}.model.line.determine`).d('判定')}`,
      lookupCode: 'MT.QMS.INSPECT_RESULT',
      dynamicProps: {
        required: ({ dataSet, record }) => {
          const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
          const _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};
          const _requiredFlag = _cacheInfo?.requiredFlag;

          return (
            _cacheInfo?.inspectItemId &&
            dataSet.getState('resultDimension') !== 'UNQUALIFIED_INSPECTION_TASK' &&
            record?.get('inspectItemKey') === 'inspectResult' &&
            _requiredFlag === 'Y' &&
            ['RECORD_SAMPLE_VALUE', 'RECORD_VALUE', 'UNQUALIFIED_ITEMS'].includes(
              dataSet.getState('resultDimension'),
            )
          );
        },
      },
    });

    // DS添加用于合格不合格数的数据源的字段
    inspectItemColDS.addField(`${fieldName}_OK`, {
      name: `${fieldName}_OK`,
      type: FieldType.number,
      label: `${intl.get(`${modelPrompt}.model.line.lineOkQty`).d('OK数')}`,
      min: 0,
      step: 1,
    });
    inspectItemColDS.addField(`${fieldName}_NG`, {
      name: `${fieldName}_NG`,
      type: FieldType.number,
      label: `${intl.get(`${modelPrompt}.model.line.lineNgQty`).d('NG数')}`,
      min: 0,
      step: 1,
    });

    // DS添加dataQty同数量的输入值字段
    if (Number(count || 0) > 0) {
      for (let i = 0; i < count; i++) {
        inspectItemColDS.addField(`${fieldName}_VALUE${i}`, {
          name: `${fieldName}_VALUE${i}`,
          type: FieldType.string,
          label: `${intl.get(`${modelPrompt}.model.line.inspectValue`).d('检测值')}`,
          dynamicProps: {
            required: ({ record, dataSet }) => {
              const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
              const _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};
              const _samplingQty = Number(_cacheInfo?.newSamplingQty || 0);
              const _samplingType = _cacheInfo?.samplingType;
              const _requiredFlag = _cacheInfo?.requiredFlag;
              const _disabled = !!record?.get(`${fieldName}_DISABLED`);

              return (
                _cacheInfo?.inspectItemId &&
                !['UNQUALIFIED_INSPECTION_TASK', 'UNQUALIFIED_ITEMS'].includes(
                  dataSet.getState('resultDimension'),
                ) &&
                !!record?.get('cacheInspectObjectId') &&
                !((_samplingType !== 'USER_DEFINED_SAMPLING' && _samplingQty === 0) || _disabled) &&
                _requiredFlag === 'Y' &&
                _cacheInfo?.dataType !== 'CALCULATE_FORMULA' &&
                ['RECORD_VALUE'].includes(dataSet.getState('resultDimension')) &&
                !record?.get(`${fieldName}_DISABLED`)
              );
            },
          },
        });
      }
    }

    return {
      ...item,
      newSamplingQty: item.samplingQty,
    };
  };

  // 列模式-渲染动态单元格
  const onHandleRenderCell = fieldName => {
    // 附件配置信息
    const _attachmentProps: any = {
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform-workshop',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
      showValidation: ShowValidation.newLine,
      viewMode: 'popup',
    };

    return {
      name: fieldName,
      header: () => {
        const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
        const _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};
        const _label = inspectItemColDS.getField(fieldName)?.get('label');
        if (_cacheInfo?.requiredFlag === 'Y') {
          return (
            <span>
              <span style={{ color: 'red' }}>*</span>&nbsp;
              {_label}
            </span>
          );
        }
        return _label;
      },
      renderer: ({ value, record, dataSet }) => {
        const leftLength = 8; // 固定显示行数 按需求调整
        const currentIndex = dataSet?.currentIndex - leftLength || 0;
        if (inspectInfoDS.getState('lineChangeFlag') === 'Y') {
          return '';
        }
        const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
        const _canEdit = inspectInfoDS.getState('canEdit');
        const _inspectItemKey = record?.get('inspectItemKey');
        const _curValue = record?.get(fieldName);
        const _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};
        const _dataType = _cacheInfo?.dataType;
        const _samplingQty = Number(_cacheInfo?.newSamplingQty || 0);
        const _samplingType = _cacheInfo?.samplingType;
        if (_inspectItemKey === 'samplingQty') {
          return (
            <span>
              {_curValue}
              {/* &nbsp;{_cacheInfo?.uomName} */}
            </span>
          );
        }
        if (_inspectItemKey === 'okAndNg') {
          return (
            <ItemGroup>
              <Item name={`${fieldName}_OK`}>
                <NumberField
                  record={record}
                  name={`${fieldName}_OK`}
                  disabled
                  style={{ verticalAlign: 'baseline', width: 80 }}
                />
              </Item>
              <div style={{ display: 'inline-block', textAlign: 'center', width: 6 }}>/</div>
              <Item name={`${fieldName}_NG`}>
                <NumberField
                  record={record}
                  name={`${fieldName}_NG`}
                  disabled={!_canEdit}
                  style={{ verticalAlign: 'baseline', width: 80 }}
                  onChange={(newValue, oldValue) =>
                    handleChangeNgQty(newValue, oldValue, fieldName, record)
                  }
                />
              </Item>
            </ItemGroup>
          );
        }
        if (_inspectItemKey === 'valueRange' && _dataType) {
          return (
            <Popover
              placement="top"
              content={
                <div>
                  {(_cacheInfo?.trueValues || []).map(item => (
                    <div
                      className={styles['table-tooltip-tag']}
                      style={{ color: '#11d954', backgroundColor: '#E6FFEA' }}
                    >
                      {item}
                    </div>
                  ))}
                  {(_cacheInfo?.falseValues || []).map(item => (
                    <div
                      className={styles['table-tooltip-tag']}
                      style={{ color: '#f23a50', backgroundColor: '#fff0f0' }}
                    >
                      {item}
                    </div>
                  ))}
                  {(_cacheInfo?.warningValues || []).map(item => (
                    <div
                      className={styles['table-tooltip-tag']}
                      style={{ color: '#fbad00', backgroundColor: '#fffbe6' }}
                    >
                      {item}
                    </div>
                  ))}
                  &nbsp;{_cacheInfo?.uomName}
                </div>
              }
              trigger="hover"
            >
              <div>
                {(_cacheInfo?.trueValues || []).map(item => (
                  <Tag color="green">{item}</Tag>
                ))}
                {(_cacheInfo?.falseValues || []).map(item => (
                  <Tag color="red">{item}</Tag>
                ))}
                {(_cacheInfo?.warningValues || []).map(item => (
                  <Tag color="yellow">{item}</Tag>
                ))}
                &nbsp;{_cacheInfo?.uomName}
              </div>
            </Popover>
          );
        }
        if (_inspectItemKey === 'inspectResult') {
          return (
            <Select
              record={record}
              name={`${fieldName}_RESULT`}
              readOnly={!_canEdit}
              style={{
                verticalAlign: 'baseline',
                backgroundColor:
                  record?.get(`${fieldName}_RESULT`) === 'OK'
                    ? 'rgb(230, 255, 234)'
                    : record?.get(`${fieldName}_RESULT`) === 'NG'
                      ? 'rgb(255, 240, 240)'
                      : '',
              }}
              onChange={(newValue, oldValue) =>
                handleChangeInspectResult(newValue, oldValue, record)
              }
            />
          );
        }
        if (_inspectItemKey === 'operation') {
          return (
            <>
              <Attachment
                record={record}
                name={`${fieldName}_ENCLOSURE`}
                readOnly
                disabled={!_canEdit}
                {..._attachmentProps}
              />
              <Attachment
                record={record}
                name={fieldName}
                disabled={!_canEdit}
                {..._attachmentProps}
                className={styles['table-col-attachment']}
              />
              <NcRecordComponent
                canEdit={_canEdit}
                type="text"
                visible={
                  inspectInfoDS.current?.get('inspectNcRecordDimension') ===
                    NcRecordDimension.itemNc && !!_cacheInfo?.inspectTaskLineId
                }
                customizeTable={customizeTable}
                queryParams={{
                  inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
                  inspectDocId: inspectInfoDS.current?.get('inspectDocId'),
                  inspectTaskLineId: _cacheInfo?.inspectTaskLineId,
                  inspectItemId: _cacheInfo?.inspectItemId,
                  inspectDocLineId: _cacheInfo?.inspectDocLineId,
                  inspectNcRecordDimension: inspectInfoDS.current?.get('inspectNcRecordDimension'),
                }}
                style={{ verticalAlign: 'top', padding: '0px 8px' }}
              />
            </>
          );
        }

        let _dataQty = 0;
        if (
          _cacheInfo?.dataQtyDisposition === 'DATA' ||
          _cacheInfo?.dataQtyDisposition === 'SAMPLE'
        ) {
          _dataQty = 1;
        } else {
          _dataQty = Number(_cacheInfo?.dataQty || 0);
        }

        if (record?.get('cacheInspectObjectId')) {
          const _addQtyCount = Number(record.get(`${fieldName}_addQtyCount`) || 0);
          let _count =
            _dataType === 'CALCULATE_FORMULA'
              ? Number(record.get(`${fieldName}_FORMULA_COUNT`) || 0)
              : Number(_dataQty || 0);

          const initLength = record.get(`${fieldName}_taskLineActDtlsLength`) || 0;

          if (_count < initLength) {
            _count = initLength;
          }
          _count += _addQtyCount;
          const _dataQtyValue: Array<any> = [];

          for (let i = 0; i < _count; i++) {
            _dataQtyValue.push(`${fieldName}_VALUE${i}`);
          }
          // 存在检验对象时需先扫描检验对象才可录入
          const _resultDimension = inspectInfoDS.current?.get('resultDimension');
          const _sourceValues = _cacheInfo?.sourceValues || [];
          const _filterSourceValues = _sourceValues.filter(item =>
            _resultDimension === 'RECORD_SAMPLE_VALUE'
              ? `${item.inspectObjectId}` === `${record.get('inspectObjectId')}`
              : `${item.sequence}` === `${record.get('sequence')}`,
          );
          const _sourceValue =
            _filterSourceValues.length > 0 ? _filterSourceValues[0].sourceInspectValue : null;
          record.init(`${fieldName}_SOURCE`, _sourceValue);
          const _decimalNumber = _cacheInfo?.decimalNumber;

          // 计算列宽
          let _widthCount =
            _dataType === 'CALCULATE_FORMULA' ? Number(_dataQty || 3) : Number(_dataQty || 0);
          const _sourceTaskId = inspectInfoDS.current?.get('sourceTaskId');
          if (_sourceTaskId && _sourceTaskId !== 0 && _sourceTaskId !== '0') {
            _widthCount++;
          }
          const _valueWidth = `${100 / _widthCount}%`;
          return (
            <ItemGroup>
              <Item name={`${fieldName}_SOURCE`}>
                {_sourceTaskId && _sourceValue && (
                  <TextField
                    record={record}
                    value={_sourceValue}
                    disabled
                    style={{
                      marginRight: 2,
                      width: _valueWidth,
                      verticalAlign: 'baseline',
                    }}
                  />
                )}
              </Item>
              {_dataQtyValue.map((valueName, index) => {
                if (_dataType === 'VALUE' && !_decimalNumber && _decimalNumber !== 0) {
                  return (
                    <Item name={valueName}>
                      <NumberField
                        record={record}
                        onEnterDown={() => {
                          if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                            return;
                          }
                          // 新增字段
                          // 获取之前的输入框是否为必输
                          const required = record?.getField(valueName)?.required;
                          const type = record?.getField(valueName)?.type;
                          record.addField(`${fieldName}_VALUE${_dataQtyValue.length}`, {
                            name: `${fieldName}_VALUE${_dataQtyValue.length}`,
                            type,
                            required,
                          });
                          record.set(`${fieldName}_addQtyCount`, _addQtyCount + 1);

                          // 聚焦
                          setTimeout(() => {
                            const elementInput = document.getElementsByName(
                              `${fieldName}_VALUE${_dataQtyValue.length}`,
                            );
                            if (elementInput.length > 0) {
                              if (currentIndex < elementInput.length) {
                                elementInput[currentIndex].focus();
                              } else {
                                elementInput[0].focus();
                              }
                            }
                          }, 100);
                        }}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${
                              styles['number-input-left']
                            } c7n-pro-input-number-required c7n-pro-input-number-required-colors`
                            : `${styles['number-input-left']} c7n-pro-input-number-required c7n-pro-input-number-required-colors`
                        }
                        disabled={
                          !_canEdit ||
                          // _disabled ||
                          (_samplingType !== 'USER_DEFINED_SAMPLING' && _samplingQty === 0) ||
                          !!record?.get(`${fieldName}_DISABLED`)
                        }
                        onChange={(value, oldValue) => {
                          if (
                            !(
                              (value === undefined || value === null) &&
                              (oldValue === undefined || oldValue === null)
                            )
                          ) {
                            handleChangeValueColor(fieldName, value, valueName, record, _dataType);
                          }
                        }}
                      />
                    </Item>
                  );
                }
                if (_dataType === 'VALUE' && isNumber(_decimalNumber) && _decimalNumber >= 0) {
                  return (
                    <Item name={valueName}>
                      <Currency
                        record={record}
                        onEnterDown={() => {
                          if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                            return;
                          }
                          // 新增字段
                          // 获取之前的输入框是否为必输
                          const required = record?.getField(valueName)?.required;
                          const type = record?.getField(valueName)?.type;
                          record.addField(`${fieldName}_VALUE${_dataQtyValue.length}`, {
                            name: `${fieldName}_VALUE${_dataQtyValue.length}`,
                            type,
                            required,
                          });
                          record.set(`${fieldName}_addQtyCount`, _addQtyCount + 1);

                          // 聚焦
                          setTimeout(() => {
                            const elementInput = document.getElementsByName(
                              `${fieldName}_VALUE${_dataQtyValue.length}`,
                            );
                            if (elementInput.length > 0) {
                              if (currentIndex < elementInput.length) {
                                elementInput[currentIndex].focus();
                              } else {
                                elementInput[0].focus();
                              }
                            }
                          }, 100);
                        }}
                        name={valueName}
                        precision={_decimalNumber > 6 ? 6 : _decimalNumber}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${
                              styles['number-input-left']
                            } c7n-pro-input-number-required c7n-pro-input-number-required-colors`
                            : `${styles['number-input-left']} c7n-pro-input-number-required c7n-pro-input-number-required-colors`
                        }
                        disabled={
                          !_canEdit ||
                          // _disabled ||
                          (_samplingType !== 'USER_DEFINED_SAMPLING' && _samplingQty === 0) ||
                          !!record?.get(`${fieldName}_DISABLED`)
                        }
                        onChange={(value, oldValue) => {
                          if (
                            !(
                              (value === undefined || value === null) &&
                              (oldValue === undefined || oldValue === null)
                            )
                          ) {
                            handleChangeValueColor(fieldName, value, valueName, record, _dataType);
                          }
                        }}
                      />
                    </Item>
                  );
                }
                if (_dataType === 'DECISION_VALUE') {
                  return (
                    <Item name={valueName}>
                      <Select
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? `${
                              styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                            } c7n-pro-select-required c7n-pro-select-required-colors`
                            : 'c7n-pro-select-required c7n-pro-select-required-colors'
                        }
                        disabled={
                          !_canEdit ||
                          // _disabled ||
                          (_samplingType !== 'USER_DEFINED_SAMPLING' && _samplingQty === 0) ||
                          !!record?.get(`${fieldName}_DISABLED`)
                        }
                        onChange={value =>
                          handleChangeValueColor(fieldName, value, valueName, record, _dataType)
                        }
                      >
                        {(_cacheInfo?.trueValues || [])
                          .concat(_cacheInfo?.falseValues || [])
                          .map(item => (
                            <Option value={item} key={item}>
                              {item}
                            </Option>
                          ))}
                      </Select>
                    </Item>
                  );
                }
                if (_dataType === 'TEXT') {
                  return (
                    <Item name={valueName}>
                      <TextField
                        record={record}
                        onEnterDown={() => {
                          if (inspectInfoDS.current?.get('enterFlag') !== 'Y') {
                            return;
                          }
                          // 新增字段
                          // 获取之前的输入框是否为必输
                          const required = record?.getField(valueName)?.required;
                          const type = record?.getField(valueName)?.type;
                          record.addField(`${fieldName}_VALUE${_dataQtyValue.length}`, {
                            name: `${fieldName}_VALUE${_dataQtyValue.length}`,
                            type,
                            required,
                          });
                          record.set(`${fieldName}_addQtyCount`, _addQtyCount + 1);

                          // 聚焦
                          setTimeout(() => {
                            const elementInput = document.getElementsByName(
                              `${fieldName}_VALUE${_dataQtyValue.length}`,
                            );
                            if (elementInput.length > 0) {
                              if (currentIndex < elementInput.length) {
                                elementInput[currentIndex].focus();
                              } else {
                                elementInput[0].focus();
                              }
                            }
                          }, 100);
                        }}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className="c7n-pro-input-required c7n-pro-input-required-colors "
                        disabled={
                          !_canEdit ||
                          // _disabled ||
                          (_samplingType !== 'USER_DEFINED_SAMPLING' && _samplingQty === 0) ||
                          !!record?.get(`${fieldName}_DISABLED`)
                        }
                        onChange={() => handleComputedQty(fieldName, _dataType)}
                      />
                    </Item>
                  );
                }
                if (_dataType === 'VALUE_LIST') {
                  return (
                    <Item name={valueName}>
                      <Select
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? `${
                              styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                            } c7n-pro-select-required c7n-pro-select-required-colors`
                            : 'c7n-pro-select-required c7n-pro-select-required-colors'
                        }
                        disabled={
                          !_canEdit ||
                          // _disabled ||
                          (_samplingType !== 'USER_DEFINED_SAMPLING' && _samplingQty === 0) ||
                          !!record?.get(`${fieldName}_DISABLED`)
                        }
                        onChange={value =>
                          handleChangeValueColor(fieldName, value, valueName, record, _dataType)
                        }
                      >
                        {(_cacheInfo?.valueLists || []).map(item => (
                          <Option value={item} key={item}>
                            {item}
                          </Option>
                        ))}
                      </Select>
                    </Item>
                  );
                }
                if (_dataType === 'DATE') {
                  return (
                    <Item name={valueName}>
                      <DateTimePicker
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className="c7n-pro-calendar-picker-required c7n-pro-calendar-picker-required-colors"
                        disabled={
                          !_canEdit ||
                          // _disabled ||
                          (_samplingType !== 'USER_DEFINED_SAMPLING' && _samplingQty === 0) ||
                          !!record?.get(`${fieldName}_DISABLED`)
                        }
                        onChange={() => handleComputedQty(fieldName, _dataType)}
                      />
                    </Item>
                  );
                }
                if (_dataType === 'CALCULATE_FORMULA') {
                  const _formulaValue = record?.get(valueName);
                  return (
                    (_formulaValue || _formulaValue === 0) && (
                      <Item name={valueName}>
                        <TextField
                          record={record}
                          name={valueName}
                          style={{
                            width: _valueWidth,
                            verticalAlign: 'baseline',
                            marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                          }}
                          className={
                            record?.get(`${valueName}_COLOR`)
                              ? `${
                                styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]
                              } c7n-pro-input-required c7n-pro-input-required-colors`
                              : 'c7n-pro-input-required c7n-pro-input-required-colors'
                          }
                          disabled
                        />
                      </Item>
                    )
                  );
                }
                return '';
              })}
            </ItemGroup>
          );
        }
        return value;
      },
      tooltip: 'none',
    };
  };

  // 行列模式-检验行判定变化
  const handleChangeInspectResult = (newValue, oldValue, record) => {
    const _modeType = inspectInfoDS.getState('modeType');
    if (newValue !== oldValue) {
      if (_modeType === ModeType.col) {
        // 列模式需要计算不合格数
        let _ngQty = 0;
        let _okQty = 0;
        const _data = record.data || {};
        Object.keys(_data).forEach(key => {
          if (key.indexOf('_RESULT') !== -1) {
            if (_data[key] === 'NG') {
              _ngQty++;
            } else if (_data[key] === 'OK') {
              _okQty++;
            }
          }
        });
        inspectInfoDS.current?.set('inspectResult', _ngQty > 0 ? 'NG' : _okQty > 0 ? 'OK' : null);
      } else if (newValue === 'NG') {
        inspectInfoDS.current?.set('inspectResult', 'NG');
      } else {
        const ngRecordFlag = inspectItemRowDS.records.find(
          item => item.get('inspectResult') === 'NG',
        );
        if (ngRecordFlag) {
          inspectInfoDS.current?.set('inspectResult', 'NG');
        } else {
          const okRecordFlag = inspectItemRowDS.records.find(
            item => item.get('inspectResult') === 'OK',
          );
          inspectInfoDS.current?.set('inspectResult', okRecordFlag ? 'OK' : null);
        }
      }
      docInfoDS.setState('docChangeFlag', 'N');
    }
  };

  // 行列模式-不合格数修改
  const handleChangeNgQty = (newValue, oldValue, fieldName, ngRecord) => {
    if (newValue !== oldValue) {
      const _modeType = inspectInfoDS.getState('modeType');
      if (_modeType === ModeType.col) {
        const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
        const _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};
        const _samplingQty = _cacheInfo?.newSamplingQty;
        ngRecord.set(`${fieldName}_OK`, Number(_samplingQty || 0) - newValue < 0 ? 0 : Number(_samplingQty || 0) - newValue);
        // 计算判定结果
        const _recordResult = inspectItemColDS.find(
          record => record.get('inspectItemKey') === 'inspectResult',
        );
        if (_recordResult) {
          const _acceptStandard = _cacheInfo?.acceptStandard;
          // 解析RE值
          let _re = 1;
          if (_acceptStandard) {
            const _acAndReArr = _acceptStandard.split('/');
            if (_acAndReArr.length > 1) {
              _re = _acAndReArr[1];
            }
          }
          const _oldResult = _recordResult.get(`${fieldName}_RESULT`);
          const _newResult = _re ? (newValue >= Number(_re || 1) ? 'NG' : 'OK') : 'OK';
          _recordResult?.set(`${fieldName}_RESULT`, _newResult);
          handleChangeInspectResult(_newResult, _oldResult, _recordResult);
        }
      } else {
        const _samplingQty = ngRecord.get('samplingQty');
        ngRecord.set('okQty', Number(_samplingQty || 0) - newValue < 0 ? 0 : Number(_samplingQty || 0) - newValue);
        const _acceptStandard = ngRecord.get('acceptStandard');
        // 解析RE值
        let _re = 1;
        if (_acceptStandard) {
          const _acAndReArr = _acceptStandard.split('/');
          if (_acAndReArr.length > 1) {
            _re = _acAndReArr[1];
          }
        }
        const _oldResult = ngRecord.get('inspectResult');
        const _newResult = _re ? (newValue >= Number(_re || 1) ? 'NG' : 'OK') : 'OK';
        ngRecord.set('inspectResult', _newResult);
        handleChangeInspectResult(_newResult, _oldResult, ngRecord);
      }
    }
  };

  // 行列模式-批量编辑抽样数量或检验结果
  const handleBatchChangeData = operationType => {
    const _canEdit = inspectInfoDS.getState('canEdit');
    const _cacheFormulaObj = inspectInfoDS.getState('cacheFormulaObj') || {};
    if (
      _canEdit &&
      (operationType !== 'BATCH_SAMPLING' || _cacheFormulaObj.userDefinedSamplingFlag === 'Y')
    ) {
      inspectLovDS.current?.set('operationType', operationType);
      Modal.open({
        ...drawerPropsC7n({
          _canEdit,
          ds: inspectLovDS,
        }),
        key: Modal.key(),
        title:
          operationType === 'BATCH_SAMPLING'
            ? intl.get(`${modelPrompt}.title.batchEditSamplingQty`).d('请输入抽样数量')
            : intl.get(`${modelPrompt}.title.batchEditResult`).d('请输入检验结果'),
        drawer: false,
        destroyOnClose: true,
        closable: true,
        maskClosable: true,
        style: {
          width: 500,
        },
        contentStyle: {
          width: 500,
        },
        className: 'hmes-style-modal',
        children: (
          <Form dataSet={inspectLovDS} columns={1} labelWidth={80} style={{ marginRight: 20 }}>
            {operationType === 'BATCH_SAMPLING' ? (
              <NumberField name="samplingQty" />
            ) : (
              <Select name="inspectResult" />
            )}
          </Form>
        ),
        onOk: () => handleOkBatchChangeData(operationType),
      });
    }
  };

  // 行列模式-批量编辑抽样数量或检验结果
  const handleOkBatchChangeData = async operationType => {
    const validFlag = await inspectLovDS.validate();
    if (!validFlag) {
      return Promise.resolve(false);
    }

    const _modeType = inspectInfoDS.getState('modeType');
    const _value =
      operationType === 'BATCH_SAMPLING'
        ? inspectLovDS.current?.get('samplingQty')
        : inspectLovDS.current?.get('inspectResult');
    if (_modeType === ModeType.col) {
      const _targetRecord = inspectItemColDS.find(
        record =>
          record.get('inspectItemKey') ===
          (operationType === 'BATCH_SAMPLING' ? 'samplingQty' : 'inspectResult'),
      );
      if (_targetRecord) {
        const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
        const _data = _targetRecord.data || {};
        Object.keys(_data).forEach(key => {
          let _flag = key.indexOf('ITEM') !== -1 && key.indexOf('_') === -1;
          if (operationType !== 'BATCH_SAMPLING') {
            _flag = _flag || key.indexOf('_RESULT') !== -1;
          }
          if (_flag && `${_data[key]}` !== `${_value}`) {
            // 当为抽样数量时需要判断是否可编辑
            if (operationType === 'BATCH_SAMPLING') {
              const _cacheInfo = _cacheItemData ? _cacheItemData[key] : {};
              if (_cacheInfo?.samplingType === 'USER_DEFINED_SAMPLING') {
                _targetRecord.set(key, _value);
                handleChangeSamplingQty(key, _value);
              }
            } else {
              _targetRecord.set(key, _value);
            }
          }
        });
        if (operationType !== 'BATCH_SAMPLING') {
          handleChangeInspectResult(_value, '', _targetRecord);
        }
      }
    } else {
      inspectItemRowDS.records.forEach(record => {
        if (
          _modeType === ModeType.row &&
          operationType === 'BATCH_SAMPLING' &&
          record.get('samplingType') === 'USER_DEFINED_SAMPLING' &&
          `${_value}` !== `${record.get('samplingType')}`
        ) {
          const _dataQty = Number(record.get('dataQty') || 0);
          record.set('samplingQty', _value);
          record.set('samplingQtyCount', _dataQty * Number(_value || 0));
          handleComputedQty(record.get('fieldName'), record.get('dataType'), true);
        } else if (operationType !== 'BATCH_SAMPLING') {
          record.set('inspectResult', _value);
        }
      });
    }

    inspectLovDS.current?.set('operationType', null);
    inspectLovDS.reset();
  };

  // 列模式-抽样数量变化
  const handleChangeSamplingQty = (fieldName, value, resultFlag = true) => {
    const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
    const _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};
    _cacheInfo.newSamplingQty = value;
    _cacheItemData[fieldName] = _cacheInfo;
    inspectItemColDS.setState('cacheItemData', _cacheItemData);
    handleComputedQty(fieldName, _cacheInfo?.dataType, true, resultFlag, false, false);
  };

  // 行列模式-录入值变化颜色处理
  const handleChangeValueColor = (fieldName, value, valueName, record, dataType) => {
    let falseValues: Array<any>;
    let warningValues: Array<any>;
    let trueValues: Array<any>;

    const _modeType = inspectInfoDS.getState('modeType');
    if (_modeType === ModeType.col) {
      const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
      const _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};
      falseValues = _cacheInfo?.falseValues;
      warningValues = _cacheInfo?.warningValues;
      trueValues = _cacheInfo?.trueValues;
    } else {
      falseValues = inspectItemRowDS.current?.get('falseValues');
      warningValues = inspectItemRowDS.current?.get('warningValues');
      trueValues = inspectItemRowDS.current?.get('trueValues');
    }

    if (value || value === 0) {
      const _color = onGetValueColor(value, dataType, falseValues, warningValues, trueValues);
      record.set(`${valueName}_COLOR`, _color);
    } else {
      record.set(`${valueName}_COLOR`, null);
    }

    handleComputedQty(fieldName, dataType, false, true, false, true, valueName);
  };

  // 行列模式-录入值颜色判断
  const onGetValueColor = (value, dataType, falseValues, warningValues, trueValues) => {
    if (!value && value !== 0) {
      return null;
    }
    let _color = 'green';
    let _falseValueFlag = false;
    let _warningValueFlag = false;
    let _trueValueFlag = false;

    if ((falseValues || []).length > 0) {
      if (['VALUE', 'CALCULATE_FORMULA'].includes(dataType)) {
        _falseValueFlag = onCheckValueArrayMethod(falseValues, value);
      } else {
        falseValues.forEach(item => {
          if (!_falseValueFlag && `${item}` === `${value}`) {
            _falseValueFlag = true;
          }
        });
      }
    } else if ((trueValues || []).length > 0) {
      if (['VALUE', 'CALCULATE_FORMULA'].includes(dataType)) {
        _trueValueFlag = onCheckValueArrayMethod(trueValues, value, true);
      } else {
        trueValues.forEach(item => {
          if (!_trueValueFlag && `${item}` === `${value}`) {
            _trueValueFlag = true;
          }
        });
      }
    }
    if (
      !_falseValueFlag &&
      (warningValues || []).length > 0 &&
      ['VALUE', 'CALCULATE_FORMULA'].includes(dataType)
    ) {
      _warningValueFlag = onCheckValueArrayMethod(warningValues, value);
    }
    if (_falseValueFlag) {
      // 在不符合值中
      _color = 'red';
    } else if (_warningValueFlag) {
      // 在预警值中
      _color = 'yellow';
    }
    if (_warningValueFlag) {
      // 在预警值中
      _color = 'yellow';
    } else if ((falseValues || []).length > 0) {
      _color = _falseValueFlag ? 'red' : 'green';
    } else if ((trueValues || []).length > 0) {
      _color = _trueValueFlag ? 'green' : 'red';
    }
    return _color;
  };

  // 行列模式-录入值合格数不合格数计算及交互
  const handleComputedQty = (
    fieldName, // 列唯一键
    dataType, // 检验项类型
    samplingFlag = false, // 抽样数小于已录入行数时，是否清空行
    resultFlag = true, // 检验项结果是否根据合格数和不合格数计算
    lineRecord = false, // 当前行
    formulaFlag = true, // 触发计算公式
    valueName = '', // 不带对象的行模式当前录入框
    delInspectObjectId = null, // 用于清空检验对象触发时的处理
  ) => {
    let _lineRecord: Record = lineRecord || inspectItemRowDS.current;

    const _modeType = inspectInfoDS.getState('modeType');
    let _ngQty = 0;
    let _dataQty = 0;
    let _samplingQty = 0;
    let _samplingQtyCount = 0;
    let _samplingType;
    let _dataType = '';
    let _targetRecords: Array<any> = [];
    let _acceptStandard;
    let _requiredFlag = 'N';
    let _curInspectItemId;
    let _cacheItemData: any = {};
    let _cacheInfo: any = {};

    if (_modeType === ModeType.col) {
      _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
      _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};

      if (
        _cacheInfo?.dataQtyDisposition === 'DATA' ||
        _cacheInfo?.dataQtyDisposition === 'SAMPLE'
      ) {
        _dataQty = 1;
        _samplingQty = 1;
      } else {
        _dataQty = Number(_cacheInfo?.dataQty || 0);
        _samplingQty = Number(_cacheInfo?.newSamplingQty || 0);
      }

      _samplingType = _cacheInfo?.samplingType;
      _targetRecords = inspectItemColDS.records || [];
      _acceptStandard = _cacheInfo?.acceptStandard;
      _curInspectItemId = _cacheInfo?.inspectItemId;
      _dataType = _cacheInfo?.dataType;
      _lineRecord = inspectItemColDS.current;
    } else {
      if (_cacheInfo?.dataQtyDisposition === 'DATA') {
        _dataQty = Number(_lineRecord?.get('dataQty') || 0);
        _samplingQty = 1;
      } else if (_cacheInfo?.dataQtyDisposition === 'SAMPLE') {
        _dataQty = 1;
        _samplingQty = Number(_lineRecord?.get('samplingQty') || 0);
      } else {
        _dataQty = Number(_lineRecord?.get('dataQty') || 0);
        _samplingQty = Number(_lineRecord?.get('samplingQty') || 0);
      }

      if (_modeType === ModeType.row) {
        _dataQty += Number(_lineRecord?.get('addQtyCount') || 0);
      }

      _samplingType = _lineRecord?.get('samplingType');
      _dataType = _lineRecord?.get('dataType');
      _acceptStandard = _lineRecord?.get('acceptStandard');
      _requiredFlag = _lineRecord?.get('requiredFlag');
      _curInspectItemId = _lineRecord?.get('inspectItemId');
      if (_modeType === ModeType.rowObj) {
        _targetRecords = _lineRecord?.getCascadeRecords('taskLineObjects') || [];
        _samplingQtyCount = _samplingQty;
      } else {
        _samplingQtyCount = Number(_lineRecord?.get('samplingQtyCount') || 0);
      }
      _samplingQtyCount += Number(_lineRecord?.get('addQtyCount') || 0);
    }

    if (_modeType !== ModeType.row) {
      // 获取当前检验项不同行的所有录入值：对象->值
      const _dataObjInfo: any = {};
      let _dataObjInfoCount = 0;
      const _objRecords: Array<any> = [];
      let _inspectValueRecord = '';
      _targetRecords.forEach(record => {
        if (_modeType === ModeType.rowObj) {
          // 判断对应的输入框个数
          if (record?.get('taskLineActDtls')?.length > 0) {
            if (_dataQty < record?.get('taskLineActDtls')?.length) {
              _dataQty = record?.get('taskLineActDtls')?.length;
            }
          }
          _dataQty += Number(record?.get('addQtyCount') || 0);
          _dataQty = _dataQty === 0 ? 1 : _dataQty;
        } else {
          const initLength = record.get(`${fieldName}_taskLineActDtlsLength`) || 0;
          const _addQtyCount = Number(record.get(`${fieldName}_addQtyCount`) || 0);

          if (_dataQty < initLength) {
            _dataQty = initLength;
          }

          _dataQty += _addQtyCount;
        }
        if (record.get('cacheInspectObjectId') && record.status !== 'delete') {
          _objRecords.push(record);
          const _dataObjList: Array<any> = [];
          // 不合格标识
          let _ngFlag = false;
          for (let i = 0; i < _dataQty; i++) {
            const _value = record?.get(`${fieldName}_VALUE${i}`);
            if (_value || _value === 0) {
              _dataObjList.push(_value);
              if (_modeType === ModeType.rowObj) {
                if (_dataType === 'DATE') {
                  _inspectValueRecord += `${moment(_value).format('YYYY-MM-DD HH:mm:ss')},`;
                } else {
                  _inspectValueRecord += `${_value},`;
                }
              }
            }
            if (!_ngFlag && record?.get(`${fieldName}_VALUE${i}_COLOR`) === 'red') {
              _ngFlag = true;
            }
          }
          if (_dataObjList.length > 0) {
            _dataObjInfo[record.get('cacheInspectObjectId')] = _dataObjList;
            _dataObjInfoCount++;
          }
          if (_ngFlag) {
            _ngQty++;
          }
        }
      });

      // 设置行模式录入记录
      if (_inspectValueRecord.length > 0) {
        _inspectValueRecord = _inspectValueRecord.substring(0, _inspectValueRecord.length - 1);
        _lineRecord?.init('inspectValueRecord', _inspectValueRecord);
      } else {
        _lineRecord?.init('inspectValueRecord', null);
      }

      // 自定义抽样时设置抽样数
      if (_samplingType === 'USER_DEFINED_SAMPLING') {
        // 自定义抽样同步抽样数
        if (_modeType === ModeType.col) {
          _samplingQty = _dataObjInfoCount;
          const _recordSamplingQty = inspectItemColDS.find(
            record => record.get('inspectItemKey') === 'samplingQty',
          );
          _recordSamplingQty?.set(fieldName, _samplingQty);
          _cacheItemData[fieldName] = {
            ..._cacheInfo,
            newSamplingQty: _samplingQty,
          };
          inspectItemColDS.setState('cacheItemData', _cacheItemData);
        }
      }

      if (_dataType !== 'CALCULATE_FORMULA' && _samplingType !== 'USER_DEFINED_SAMPLING') {
        // 录入值的行数与抽样数相等时其他行不可输入
        if (_dataObjInfoCount === _samplingQty) {
          _objRecords.forEach(record => {
            record.init(
              `${fieldName}_DISABLED`,
              (_dataObjInfo[record.get('cacheInspectObjectId')] || []).length < 1,
            );
          });
        } else {
          _targetRecords.forEach(record => {
            if (_modeType === ModeType.rowObj) {
              // 判断对应的输入框个数
              if (record?.get('taskLineActDtls')?.length > 0) {
                if (_dataQty < record?.get('taskLineActDtls')?.length) {
                  _dataQty = record?.get('taskLineActDtls')?.length;
                }
              }
              _dataQty += Number(record?.get('addQtyCount') || 0);
            }
            if (record.get('cacheInspectObjectId')) {
              record.init(`${fieldName}_DISABLED`, false);
              if (samplingFlag && _dataObjInfoCount > _samplingQty) {
                // 判断是否清空的数据中存在不符合值
                let _itemNgFlag = false;
                for (let i = 0; i < _dataQty; i++) {
                  if (record.get(`${fieldName}_VALUE${i}`)) {
                    if (!_itemNgFlag) {
                      const _color = record.get(`${fieldName}_VALUE${i}_COLOR`);
                      if (_color === 'red') {
                        _itemNgFlag = true;
                      }
                    }
                    record.set(`${fieldName}_VALUE${i}`, null);
                    record.set(`${fieldName}_VALUE${i}_COLOR`, null);
                  }
                }
                if (_itemNgFlag) {
                  _ngQty--;
                }
              }
            }
          });
        }
      }
    } else if (samplingFlag) {
      // 行模式抽样数量变化
      const _maxSamplingQty = Number(_lineRecord?.get('maxSamplingQty') || 0);
      if (_maxSamplingQty < _samplingQtyCount) {
        // 当抽样数变大时需要新加DS字段
        onDSAddInspectValueField(
          fieldName,
          inspectItemRowDS,
          _requiredFlag,
          _samplingQtyCount,
          _maxSamplingQty,
        );
        _ngQty = Number(_lineRecord?.get('ngQty') || 0);
        _lineRecord?.set('maxSamplingQty', _samplingQtyCount);
      } else {
        // 当抽样数变小时需要清空多余数据
        for (let i = _samplingQtyCount; i < _maxSamplingQty; i++) {
          _lineRecord.set(`${fieldName}_VALUE${i}`, null);
          _lineRecord.set(`${fieldName}_VALUE${i}_COLOR`, null);
        }
        if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(dataType)) {
          for (let i = 0; i < _samplingQtyCount; i++) {
            if (_lineRecord.get(`${fieldName}_VALUE${i}_COLOR`) === 'red') {
              _ngQty++;
            }
          }
        }
      }
    } else if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(dataType)) {
      for (let i = 0; i < _samplingQtyCount; i++) {
        if (_lineRecord.get(`${fieldName}_VALUE${i}_COLOR`) === 'red') {
          _ngQty++;
        }
      }
    }

    if (resultFlag) {
      // 解析RE值
      let _re = 1;
      if (_acceptStandard) {
        const _acAndReArr = _acceptStandard.split('/');
        if (_acAndReArr.length > 1) {
          _re = _acAndReArr[1];
        }
      }

      let _oldInspectResult;
      let _newInspectResult;
      let _inspectResultRecord;
      // 合格数不合格数和检验结果变化
      if (_modeType === ModeType.col) {
        const _record = inspectItemColDS.find(record => record.get('inspectItemKey') === 'okAndNg');
        _record?.init(`${fieldName}_NG`, _ngQty);
        _record?.init(`${fieldName}_OK`, _samplingQty - _ngQty < 0 ? 0 : _samplingQty - _ngQty);
        const _recordResult = inspectItemColDS.find(
          record => record.get('inspectItemKey') === 'inspectResult',
        );
        _oldInspectResult = _recordResult?.get(`${fieldName}_RESULT`);
        _newInspectResult = _re ? (_ngQty >= Number(_re || 1) ? 'NG' : 'OK') : 'OK';
        _inspectResultRecord = _recordResult;
        _recordResult?.set(`${fieldName}_RESULT`, _newInspectResult);
      } else {
        _lineRecord?.set('ngQty', _ngQty);
        _lineRecord?.set('okQty', _samplingQtyCount - _ngQty < 0 ? 0 : _samplingQtyCount - _ngQty);
        _oldInspectResult = _lineRecord?.get('inspectResult');
        _newInspectResult = _re ? (_ngQty >= Number(_re || 1) ? 'NG' : 'OK') : 'OK';
        _inspectResultRecord = _lineRecord;
        _lineRecord?.set('inspectResult', _newInspectResult);
      }
      handleChangeInspectResult(_newInspectResult, _oldInspectResult, _inspectResultRecord);
    }

    if (formulaFlag && dataType !== 'CALCULATE_FORMULA') {
      onComputedCalculateFormula(
        _modeType,
        _curInspectItemId,
        _modeType === ModeType.rowObj ? inspectItemRowObjValueDS.current : _lineRecord,
        valueName,
        delInspectObjectId,
      );
    }
  };

  // 行列模式-计算公式
  const onComputedCalculateFormula = async (
    modeType,
    curInspectItemId,
    curRecord,
    valueName,
    delInspectObjectId = null,
  ) => {
    // 缓存的计算公式相关数据
    const _cacheFormulaObj = inspectInfoDS.getState('cacheFormulaObj') || {};
    const _formulaObj = _cacheFormulaObj.formulaObj || {};
    const _itemIdFieldNameObj = _cacheFormulaObj.itemIdFieldNameObj || {};

    // 列模式缓存项信息
    const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};

    // 调用计算公式接口的传参
    const _params: Array<any> = [];

    // 受该检验项影响的项ID集合
    const _targetInspectItemIds = _formulaObj[`ITEM_ID_${curInspectItemId}`] || [];
    if (_targetInspectItemIds.length < 1) {
      return false;
    }

    // 行模式计算公式项ID -> Record
    const _calculateFormulaRecordInfo: any = {};
    // 列模式存在按项目计算时获取所有录入值所在行
    let _cacheInspectObjectList: Array<any> = [];

    // 检验项列模式删除检验对象或新增检验项的缓存数据
    const _itemColUpdateCacheObj = inspectInfoDS.getState('itemColUpdateCacheObj') || {};
    const _inspectTaskObjectActIds = _itemColUpdateCacheObj?.deleteActIds || [];
    const _inspectDocLineActDtlIds = _itemColUpdateCacheObj?.deleteDtlIds || [];
    // 列模式缓存dtl信息
    const _itemColDtlObj = inspectItemColDS.getState('itemColDtlObj') || {};

    _targetInspectItemIds.forEach(formulaItemId => {
      let _formulaItemRecord: any;
      // 获取受影响的计算公式类型检验项的公式信息
      let _formulaInfo: any;
      if (modeType === ModeType.col) {
        const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${formulaItemId}`];
        const _cacheItemInfo = _cacheItemData[_fieldName] || {};
        _formulaInfo = _cacheItemInfo.formulaJson || {};
        // 项目相关信息
        _formulaInfo.decimalNumber = _cacheItemInfo.decimalNumber;
        _formulaInfo.processMode = _cacheItemInfo.processMode;
        _formulaInfo.dataQty = Number(_cacheItemInfo.dataQty || 0);
        _formulaInfo.fieldName = _cacheItemInfo.fieldName;
      } else {
        _formulaItemRecord = inspectItemRowDS.find(
          record => record.get('inspectItemId') === formulaItemId,
        );
        _formulaInfo = _formulaItemRecord?.get('formulaJson') || {};
        // 项目相关信息
        _formulaInfo.decimalNumber = _formulaItemRecord?.get('decimalNumber');
        _formulaInfo.processMode = _formulaItemRecord?.get('processMode');
        _formulaInfo.dataQty = Number(_formulaItemRecord?.get('dataQty') || 0);
        _formulaInfo.fieldName = _formulaItemRecord?.get('fieldName');
      }

      const {
        formulaCode,
        dimension,
        decimalNumber,
        processMode,
        formulaList,
        fieldName,
      } = _formulaInfo;
      if (
        _formulaInfo?.formulaCode &&
        (formulaList || []).length > 0 &&
        (modeType !== ModeType.row || dimension !== FormulaDimension.sameObject)
      ) {
        // 与公式相关的录入值传参
        const _formulaList: Array<any> = [];

        if (_cacheInspectObjectList.length < 1 && dimension === FormulaDimension.sameItem) {
          _cacheInspectObjectList = inspectItemColDS.records.filter(
            record => record.get('cacheInspectObjectId') && record.status !== 'delete',
          );
        }
        const _inspectObjectId = curRecord.get('inspectObjectId') || delInspectObjectId;
        const _sequence = curRecord.get('sequence');

        // 存在不为空的值的标识
        let _addFlag = false;
        formulaList.forEach(formulaItem => {
          const { inspectItemId, isRequired, fieldCode } = formulaItem;
          if (!inspectItemId) {
            _formulaList.push(formulaItem);
          } else {
            const _inspectValue: Array<any> = [];

            if (modeType === ModeType.col) {
              // 如果为列模式并且为按对象或序号则直接取当前行的数据
              const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${inspectItemId}`];
              const _cacheItemInfo = _cacheItemData[_fieldName] || {};
              const _dataQty = Number(_cacheItemInfo.dataQty || 0);
              // 根据计算维护获取录入值
              let _valueFlag = false;
              if (
                (dimension === FormulaDimension.sameObject && _inspectObjectId) ||
                (dimension === FormulaDimension.sameSequence && _sequence)
              ) {
                // 按相同对象或相同序号的维护
                _valueFlag = onForArrayAddValue(_dataQty, curRecord, _fieldName, _inspectValue);
              } else if (dimension === FormulaDimension.sameItem) {
                // 需要获取项下所有值
                _cacheInspectObjectList.forEach(_objRecord => {
                  const _objValueFlag = onForArrayAddValue(
                    _dataQty,
                    _objRecord,
                    _fieldName,
                    _inspectValue,
                  );
                  if (!_valueFlag) {
                    _valueFlag = _objValueFlag;
                  }
                });
              }
              if (_valueFlag) {
                _addFlag = true;
              }
              _formulaList.push({
                inspectItemId,
                isRequired,
                fieldCode,
                inspectObjectId:
                  dimension === FormulaDimension.sameObject ? _inspectObjectId : null,
                objectInspectSequence: dimension === FormulaDimension.sameItem ? null : _sequence,
                inspectValue: _inspectValue,
              });
            } else if (modeType === ModeType.rowObj) {
              // 根据计算维护获取录入值
              const _lineFormulaItemRecord = inspectItemRowDS.find(
                record => record.get('inspectItemId') === inspectItemId,
              );
              const _objRecords =
                _lineFormulaItemRecord?.getCascadeRecords('taskLineObjects') || [];
              if (_objRecords.length > 0) {
                const _dataQty = Number(_lineFormulaItemRecord.get('dataQty') || 0);
                const _fieldName = _lineFormulaItemRecord.get('fieldName');
                let _valueFlag = false;
                if (
                  (dimension === FormulaDimension.sameObject && _inspectObjectId) ||
                  (dimension === FormulaDimension.sameSequence && _sequence)
                ) {
                  // 按相同对象或相同序号的维护
                  const _objRecord = _objRecords.find(record =>
                    dimension === FormulaDimension.sameObject
                      ? record.get('inspectObjectId') === _inspectObjectId
                      : record.get('sequence') === _sequence,
                  );
                  if (_objRecord) {
                    _valueFlag = onForArrayAddValue(
                      _dataQty,
                      _objRecord,
                      _fieldName,
                      _inspectValue,
                    );
                  }
                } else if (dimension === FormulaDimension.sameItem) {
                  // 需要获取项下所有值
                  _objRecords.forEach(_objRecord => {
                    const _objValueFlag = onForArrayAddValue(
                      _dataQty,
                      _objRecord,
                      _fieldName,
                      _inspectValue,
                    );
                    if (!_valueFlag) {
                      _valueFlag = _objValueFlag;
                    }
                  });
                }
                if (_valueFlag) {
                  _addFlag = true;
                }
                _formulaList.push({
                  inspectItemId,
                  isRequired,
                  fieldCode,
                  inspectObjectId:
                    dimension === FormulaDimension.sameObject ? _inspectObjectId : null,
                  objectInspectSequence: dimension === FormulaDimension.sameItem ? null : _sequence,
                  inspectValue: _inspectValue,
                });
              }
            } else if (modeType === ModeType.row) {
              // 根据计算维护获取录入值
              const _lineFormulaItemRecord = inspectItemRowDS.find(
                record => record.get('inspectItemId') === inspectItemId,
              );
              if (_lineFormulaItemRecord) {
                const _fieldName = _lineFormulaItemRecord.get('fieldName');
                if (dimension === FormulaDimension.sameSequence) {
                  const _sequence = Number(valueName.split('_VALUE')[1] || 0);
                  const _value = _lineFormulaItemRecord.get(`${_fieldName}_VALUE${_sequence}`);
                  if (_value || _value === 0) {
                    _inspectValue.push(_value);
                    _addFlag = true;
                    _formulaList.push({
                      inspectItemId,
                      isRequired,
                      fieldCode,
                      inspectObjectId: null,
                      objectInspectSequence: _sequence + 1,
                      inspectValue: _inspectValue,
                    });
                  }
                } else if (dimension === FormulaDimension.sameItem) {
                  let _valueFlag = false;
                  const _samplingQtyCount = Number(
                    _lineFormulaItemRecord.get('samplingQtyCount') || 0,
                  );
                  _valueFlag = onForArrayAddValue(
                    _samplingQtyCount,
                    _lineFormulaItemRecord,
                    _fieldName,
                    _inspectValue,
                  );
                  if (_valueFlag) {
                    _addFlag = true;
                  }
                  _formulaList.push({
                    inspectItemId,
                    isRequired,
                    fieldCode,
                    inspectObjectId: null,
                    objectInspectSequence: null,
                    inspectValue: _inspectValue,
                  });
                }
              }
            }
          }
        });

        // 封装传参，未传参的数据需清空对应检验项计算结果
        if (_addFlag) {
          _params.push({
            formulaItemId,
            formulaCode,
            dimension,
            decimalNumber,
            processMode,
            formulaList: _formulaList,
          });
          _calculateFormulaRecordInfo[`ITEM_ID_${formulaItemId}`] = _formulaItemRecord;
        } else if (modeType === ModeType.col) {
          const _cacheInspectObjectId = curRecord.get('cacheInspectObjectId');
          const _formulaCount = Number(curRecord.get(`${fieldName}_FORMULA_COUNT`) || 0);
          // 后台删除act和dtl的Id
          for (let i = 0; i < _formulaCount; i++) {
            const _itemColKey = `KEY${_cacheInspectObjectId}_${fieldName}_VALUE${i}`;
            const _cacheDtlInfo = _itemColDtlObj[_itemColKey] || {};
            const _dtlId = _cacheDtlInfo?.inspectDocLineActDtlId;
            if (_dtlId && !_inspectDocLineActDtlIds.includes(_dtlId)) {
              _inspectDocLineActDtlIds.push(_dtlId);
              _itemColDtlObj[_itemColKey] = {};
            }
          }
          if ([FormulaDimension.sameObject, FormulaDimension.sameSequence].includes(dimension)) {
            for (let i = 0; i < _formulaCount; i++) {
              curRecord.set(`${fieldName}_VALUE${i}`, null);
              curRecord.set(`${fieldName}_VALUE${i}_COLOR`, null);
            }
            curRecord.set(`${fieldName}_FORMULA_COUNT`, 0);
          } else if (dimension === FormulaDimension.sameItem) {
            _cacheInspectObjectList.forEach(_objRecord => {
              const _formulaCount = Number(_objRecord.get(`${fieldName}_FORMULA_COUNT`) || 0);
              for (let i = 0; i < _formulaCount; i++) {
                _objRecord.set(`${fieldName}_VALUE${i}`, null);
                _objRecord.set(`${fieldName}_VALUE${i}_COLOR`, null);
              }
              _objRecord.set(`${fieldName}_FORMULA_COUNT`, 0);
            });
          }
        } else if (modeType === ModeType.rowObj) {
          const _taskLineObjects = _formulaItemRecord.get('taskLineObjects') || [];
          if (_taskLineObjects.length > 0) {
            if (
              (dimension === FormulaDimension.sameObject && _inspectObjectId) ||
              (dimension === FormulaDimension.sameSequence && _sequence)
            ) {
              const _taskLineObject = _taskLineObjects.find(record =>
                dimension === FormulaDimension.sameObject
                  ? `${record.inspectObjectId}` === `${_inspectObjectId}`
                  : `${record.sequence}` === `${_sequence}`,
              );
              if (_taskLineObject && _taskLineObject.cacheInspectObjectId) {
                const _newTaskLineObjects = _taskLineObjects.filter(
                  record => record.cacheInspectObjectId !== _taskLineObject.cacheInspectObjectId,
                );
                // 后台删除act和dtl的Id
                if (
                  _taskLineObject.inspectTaskObjectActId &&
                  !_inspectTaskObjectActIds.includes(_taskLineObject.inspectTaskObjectActId)
                ) {
                  _inspectTaskObjectActIds.push(_taskLineObject.inspectTaskObjectActId);
                  (_taskLineObject.dtlIds || []).forEach(dtlId => {
                    if (dtlId && !_inspectDocLineActDtlIds.includes(dtlId)) {
                      _inspectDocLineActDtlIds.push(dtlId);
                    }
                  });
                  _taskLineObject.inspectTaskObjectActId = null;
                  _taskLineObject.dtlIds = [];
                }
                _newTaskLineObjects.splice(Number(_sequence || 1) - 1, 1);
                _formulaItemRecord.set('taskLineObjects', _newTaskLineObjects);
                _formulaItemRecord.status = 'update';
              }
            } else if (dimension === FormulaDimension.sameItem) {
              _formulaItemRecord.set('taskLineObjects', []);
              _formulaItemRecord.status = 'update';
              // 后台删除act和dtl的Id
              _taskLineObjects.forEach(objItem => {
                if (
                  objItem.inspectTaskObjectActId &&
                  !_inspectTaskObjectActIds.includes(objItem.inspectTaskObjectActId)
                ) {
                  _inspectTaskObjectActIds.push(objItem.inspectTaskObjectActId);
                  (objItem.dtlIds || []).forEach(dtlId => {
                    if (dtlId && !_inspectDocLineActDtlIds.includes(dtlId)) {
                      _inspectDocLineActDtlIds.push(dtlId);
                    }
                  });
                  objItem.inspectTaskObjectActId = null;
                  objItem.dtlIds = [];
                }
              });
            }
          }
        } else if (modeType === ModeType.row) {
          if (dimension === FormulaDimension.sameSequence) {
            const _sequence = Number(valueName.split('_VALUE')[1] || 0);
            _formulaItemRecord.set(`${fieldName}_VALUE${_sequence}`, null);
            _formulaItemRecord.set(`${fieldName}_VALUE${_sequence}_COLOR`, null);
          } else if (dimension === FormulaDimension.sameItem) {
            const _formulaCount = Number(_formulaItemRecord.get('formulaCount') || 0);
            for (let i = 0; i < _formulaCount; i++) {
              _formulaItemRecord.set(`${fieldName}_VALUE${i}`, null);
              _formulaItemRecord.set(`${fieldName}_VALUE${i}_COLOR`, null);
            }
          }
        }
      }
    });

    if (_params.length > 0) {
      const res = await getCalculateFormula({
        params: _params,
      });
      if (res && res.success) {
        const data = res.rows || [];
        if (modeType === ModeType.col) {
          _params.forEach(item => {
            const { formulaItemId, dimension } = item;
            const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${formulaItemId}`];
            const _resultList = data.filter(result => result.formulaItemId === formulaItemId);
            const _cacheInspectObjectId = curRecord.get('cacheInspectObjectId');

            const _formulaCount = Number(curRecord.get(`${_fieldName}_FORMULA_COUNT`) || 0);
            // 后台删除act和dtl的Id
            for (let i = 0; i < _formulaCount; i++) {
              const _itemColKey = `KEY${_cacheInspectObjectId}_${_fieldName}_VALUE${i}`;
              const _cacheDtlInfo = _itemColDtlObj[_itemColKey] || {};
              const _dtlId = _cacheDtlInfo?.inspectDocLineActDtlId;
              if (_dtlId && !_inspectDocLineActDtlIds.includes(_dtlId)) {
                _inspectDocLineActDtlIds.push(_dtlId);
                _itemColDtlObj[_itemColKey] = {};
              }
            }

            const _cacheItemInfo = _cacheItemData[_fieldName] || {};
            const _falseValues = _cacheItemInfo.falseValues || [];
            const _warningValues = _cacheItemInfo.warningValues || [];
            const _trueValues = _cacheItemInfo.trueValues || [];

            const _resultLen = _resultList.length;
            if ([FormulaDimension.sameObject, FormulaDimension.sameSequence].includes(dimension)) {
              const _maxCount = _formulaCount >= _resultLen ? _formulaCount : _resultLen;
              for (let i = 0; i < _maxCount; i++) {
                const _newValueName = `${_fieldName}_VALUE${i}`;
                if (i >= _resultLen) {
                  curRecord.set(_newValueName, null);
                  curRecord.set(`${_newValueName}_COLOR`, null);
                } else {
                  const _newValue = _resultList[i].calculationResult;
                  curRecord.set(_newValueName, _newValue);
                  if (_newValue || _newValue === 0) {
                    const _color = onGetValueColor(
                      _newValue,
                      'CALCULATE_FORMULA',
                      _falseValues,
                      _warningValues,
                      _trueValues,
                    );
                    curRecord.set(`${valueName}_COLOR`, _color);
                  } else {
                    curRecord.set(`${valueName}_COLOR`, null);
                  }
                }
              }
              curRecord.set(`${_fieldName}_FORMULA_COUNT`, _resultList.length);
            } else if (dimension === FormulaDimension.sameItem) {
              _cacheInspectObjectList.forEach((_inspectObjectRecord, index) => {
                const _formulaCount = Number(
                  _inspectObjectRecord.get(`${_fieldName}_FORMULA_COUNT`) || 0,
                );
                if (index === 0 && _resultLen > 0) {
                  const _maxCount = _formulaCount >= _resultLen ? _formulaCount : _resultLen;
                  for (let i = 0; i < _maxCount; i++) {
                    const _newValueName = `${_fieldName}_VALUE${i}`;
                    if (i >= _resultLen) {
                      _inspectObjectRecord.set(_newValueName, null);
                      _inspectObjectRecord.set(`${_newValueName}_COLOR`, null);
                    } else {
                      const _newValue = _resultList[i].calculationResult;
                      _inspectObjectRecord.set(_newValueName, _newValue);
                      if (_newValue || _newValue === 0) {
                        const _color = onGetValueColor(
                          _newValue,
                          'CALCULATE_FORMULA',
                          _falseValues,
                          _warningValues,
                          _trueValues,
                        );
                        _inspectObjectRecord.set(`${valueName}_COLOR`, _color);
                      } else {
                        _inspectObjectRecord.set(`${valueName}_COLOR`, null);
                      }
                    }
                  }
                  _inspectObjectRecord.set(`${_fieldName}_FORMULA_COUNT`, _resultLen);
                } else {
                  for (let i = 0; i < _formulaCount; i++) {
                    const _newValueName = `${_fieldName}_VALUE${i}`;
                    _inspectObjectRecord.set(_newValueName, null);
                    _inspectObjectRecord.set(`${_newValueName}_COLOR`, null);
                  }
                  _inspectObjectRecord.set(`${_fieldName}_FORMULA_COUNT`, 0);
                }
              });
            }
          });
        } else if (modeType === ModeType.rowObj) {
          _params.forEach(item => {
            const { formulaItemId, dimension } = item;
            const formulaList = (item.formulaList || []).filter(d => d.inspectItemId);
            const _formulaItemRecord = _calculateFormulaRecordInfo[`ITEM_ID_${formulaItemId}`];
            if (_formulaItemRecord) {
              const _taskLineObjects = _formulaItemRecord.get('taskLineObjects') || [];
              const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${formulaItemId}`];
              const _resultList = data.filter(result => result.formulaItemId === formulaItemId);
              const _dataType = _formulaItemRecord.get('dataType');
              const _falseValues = _formulaItemRecord.get('falseValues');
              const _warningValues = _formulaItemRecord.get('warningValues');
              const _trueValues = _formulaItemRecord.get('trueValues');

              const _resultLen = _resultList.length;
              if (
                [FormulaDimension.sameObject, FormulaDimension.sameSequence].includes(dimension)
              ) {
                const { inspectObjectId, objectInspectSequence } = formulaList[0];
                const _taskLineObject = _taskLineObjects.find(record =>
                  dimension === FormulaDimension.sameObject
                    ? `${record.inspectObjectId}` === `${inspectObjectId}`
                    : `${record.sequence}` === `${objectInspectSequence}`,
                );
                let _newTaskLineObjects = _taskLineObjects;
                if (_taskLineObject && _taskLineObject.cacheInspectObjectId) {
                  _newTaskLineObjects = _taskLineObjects.filter(
                    record => record.cacheInspectObjectId !== _taskLineObject.cacheInspectObjectId,
                  );
                  // 后台删除act和dtl的Id
                  if (
                    _taskLineObject.inspectTaskObjectActId &&
                    !_inspectTaskObjectActIds.includes(_taskLineObject.inspectTaskObjectActId)
                  ) {
                    _inspectTaskObjectActIds.push(_taskLineObject.inspectTaskObjectActId);
                    (_taskLineObject.dtlIds || []).forEach(dtlId => {
                      if (dtlId && !_inspectDocLineActDtlIds.includes(dtlId)) {
                        _inspectDocLineActDtlIds.push(dtlId);
                      }
                    });
                    _taskLineObject.inspectTaskObjectActId = null;
                    _taskLineObject.dtlIds = [];
                  }
                  // 目标对象存在：如果没有返回结果则清空该行，如果有返回值则重新赋值
                  if (_resultLen > 0) {
                    const _formulaCount = Number(
                      _taskLineObject[`${_fieldName}_FORMULA_COUNT`] || 0,
                    );
                    const _maxCount = _formulaCount >= _resultLen ? _formulaCount : _resultLen;
                    for (let i = 0; i < _maxCount; i++) {
                      if (i >= _resultLen) {
                        const _newValueName = `${_fieldName}_VALUE${i}`;
                        _taskLineObject[_newValueName] = null;
                        _taskLineObject[`${_fieldName}_VALUE${i}_ID`] = null;
                        _taskLineObject[`${_newValueName}_COLOR`] = null;
                      } else {
                        const _newValue = _resultList[i].calculationResult;
                        _taskLineObject[`${_fieldName}_VALUE${i}`] = _newValue;
                        if (_newValue || _newValue === 0) {
                          _taskLineObject[`${_fieldName}_VALUE${i}_COLOR`] = onGetValueColor(
                            _newValue,
                            _dataType,
                            _falseValues,
                            _warningValues,
                            _trueValues,
                          );
                        } else {
                          _taskLineObject[`${_fieldName}_VALUE${i}_COLOR`] = null;
                        }
                      }
                    }
                    _taskLineObject[`${_fieldName}_FORMULA_COUNT`] = _resultLen;
                    _newTaskLineObjects.splice(
                      Number(objectInspectSequence || 1) - 1,
                      0,
                      _taskLineObject,
                    );
                  }
                  _formulaItemRecord.set('taskLineObjects', _newTaskLineObjects);
                  _formulaItemRecord.status = 'update';
                } else if (_resultLen > 0) {
                  // 目标对象不存在但有返回结果时则自动添加一行对象
                  const _addObjInfo = {
                    cacheInspectObjectId: uuid(),
                    fieldName: _fieldName,
                    dataType: _dataType,
                    inspectObjectId,
                    sourceObjectCode:
                      dimension === FormulaDimension.sameObject
                        ? curRecord.get('sourceObjectCode')
                        : null,
                    sequence: objectInspectSequence,
                  };
                  _addObjInfo[`${_fieldName}_FORMULA_COUNT`] = _resultLen;
                  _resultList.forEach((result, index) => {
                    const _newValue = result.calculationResult;
                    _addObjInfo[`${_fieldName}_VALUE${index}`] = _newValue;
                    if (_newValue || _newValue === 0) {
                      _addObjInfo[`${_fieldName}_VALUE${index}_COLOR`] = onGetValueColor(
                        _newValue,
                        _dataType,
                        _falseValues,
                        _warningValues,
                        _trueValues,
                      );
                    } else {
                      _addObjInfo[`${_fieldName}_VALUE${index}_COLOR`] = null;
                    }
                  });
                  _taskLineObjects.push(_addObjInfo);
                  _formulaItemRecord.set('taskLineObjects', _taskLineObjects);
                  _formulaItemRecord.status = 'update';
                }
              } else if (dimension === FormulaDimension.sameItem) {
                // 后台删除act和dtl的Id
                _taskLineObjects.forEach(objItem => {
                  if (
                    objItem.inspectTaskObjectActId &&
                    !_inspectTaskObjectActIds.includes(objItem.inspectTaskObjectActId)
                  ) {
                    _inspectTaskObjectActIds.push(objItem.inspectTaskObjectActId);
                    (objItem.dtlIds || []).forEach(dtlId => {
                      if (dtlId && !_inspectDocLineActDtlIds.includes(dtlId)) {
                        _inspectDocLineActDtlIds.push(dtlId);
                      }
                    });
                    objItem.inspectTaskObjectActId = null;
                    objItem.dtlIds = [];
                  }
                });
                const _newTaskLineObjects: Array<any> = [];
                if (_resultLen > 0) {
                  const _addObjInfo = {
                    cacheInspectObjectId: uuid(),
                    fieldName: _fieldName,
                    dataType: _dataType,
                    inspectObjectId: null,
                    sequence: 1,
                  };
                  _addObjInfo[`${_fieldName}_FORMULA_COUNT`] = _resultLen;
                  _resultList.forEach((result, index) => {
                    const _newValue = result.calculationResult;
                    _addObjInfo[`${_fieldName}_VALUE${index}`] = _newValue;
                    if (_newValue || _newValue === 0) {
                      _addObjInfo[`${_fieldName}_VALUE${index}_COLOR`] = onGetValueColor(
                        _newValue,
                        _dataType,
                        _falseValues,
                        _warningValues,
                        _trueValues,
                      );
                    } else {
                      _addObjInfo[`${_fieldName}_VALUE${index}_COLOR`] = null;
                    }
                    _newTaskLineObjects.push(_addObjInfo);
                  });
                }
                _formulaItemRecord.set('taskLineObjects', _newTaskLineObjects);
                _formulaItemRecord.status = 'update';
              }
            }
          });
        } else if (modeType === ModeType.row) {
          _params.forEach(item => {
            const { formulaItemId, dimension } = item;
            const formulaList = (item.formulaList || []).filter(d => d.inspectItemId);
            const _formulaItemRecord = _calculateFormulaRecordInfo[`ITEM_ID_${formulaItemId}`];
            if (_formulaItemRecord) {
              const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${formulaItemId}`];
              const _resultList = data.filter(result => result.formulaItemId === formulaItemId);
              const _formulaCount = _formulaItemRecord.get('formulaCount');
              const _falseValues = _formulaItemRecord.get('falseValues');
              const _warningValues = _formulaItemRecord.get('warningValues');
              const _trueValues = _formulaItemRecord.get('trueValues');

              const _resultLen = _resultList.length;
              if (dimension === FormulaDimension.sameSequence) {
                const { objectInspectSequence } = formulaList[0];
                const _sequence = Number(objectInspectSequence || 1);
                const _newValueName = `${_fieldName}_VALUE${_sequence - 1}`;
                if (_resultLen > 0) {
                  const _newValue = _resultList[0].calculationResult;
                  _formulaItemRecord.set(_newValueName, _newValue);
                  const _color = onGetValueColor(
                    _newValue,
                    'CALCULATE_FORMULA',
                    _falseValues,
                    _warningValues,
                    _trueValues,
                  );
                  _formulaItemRecord.set(`${_newValueName}_COLOR`, _color);
                  if (_formulaCount < _sequence) {
                    _formulaItemRecord.set('formulaCount', _sequence);
                  }
                } else {
                  _formulaItemRecord.set(_newValueName, null);
                  _formulaItemRecord.set(`${_newValueName}_COLOR`, null);
                }
              } else if (dimension === FormulaDimension.sameItem) {
                const _maxCount = _formulaCount >= _resultLen ? _formulaCount : _resultLen;
                for (let i = 0; i < _maxCount; i++) {
                  const _newValueName = `${_fieldName}_VALUE${i}`;
                  if (i >= _resultLen) {
                    _formulaItemRecord.set(_newValueName, null);
                    _formulaItemRecord.set(`${_newValueName}_COLOR`, null);
                  } else {
                    const _newValue = _resultList[i].calculationResult;
                    _formulaItemRecord.set(_newValueName, _newValue);
                    const _color = onGetValueColor(
                      _newValue,
                      'CALCULATE_FORMULA',
                      _falseValues,
                      _warningValues,
                      _trueValues,
                    );
                    _formulaItemRecord.set(`${_newValueName}_COLOR`, _color);
                  }
                }
                _formulaItemRecord.set('formulaCount', _maxCount);
              }
            }
          });
        }
      }
    }

    inspectInfoDS.setState('itemColUpdateCacheObj', {
      deleteActIds: _inspectTaskObjectActIds,
      deleteDtlIds: Array.from(new Set(_inspectDocLineActDtlIds)),
    });
    inspectItemColDS.setState('itemColDtlObj', _itemColDtlObj);
  };

  // 计算公式数据添加
  const onForArrayAddValue = (maxQty, record, fieldName, list) => {
    let _valueFlag = false;
    for (let i = 0; i < maxQty; i++) {
      const _value = record.get(`${fieldName}_VALUE${i}`);
      list.push(_value);
      if (!_valueFlag && (_value || _value === 0)) {
        _valueFlag = true;
      }
    }
    return _valueFlag;
  };

  // 行列模式-数值类型区间判断
  const onCheckValueArrayMethod = (checkList, value, trueFlag = false) => {
    let _checkFlag = false;
    checkList.forEach(item => {
      if (!_checkFlag) {
        if (item.indexOf(',') === -1) {
          // 单值
          _checkFlag = `${value}` === `${Number(item || 0)}`;
        } else {
          // 范围值
          let _valueRange = item;
          if (trueFlag) {
            // 符合值需要去掉标准值
            const _rangeArr = item.split('/');
            if (_rangeArr.length > 0) {
              _valueRange = _rangeArr[0];
            }
          }
          const _valueArr = _valueRange.substr(1, _valueRange.length - 2)?.split(',');
          if (_valueArr.length === 2) {
            const _leftChar = _valueRange.substr(0, 1);
            const _rightChar = _valueRange.substr(_valueRange.length - 1, 1);
            let _leftValue = _valueArr[0];
            let _rightValue = _valueArr[1];

            // 只有左侧在范围内才判断右边是否在
            let _leftFlag = true;
            const _reg = /^(-?\d+)(\.\d+)?$/;
            const newValue = Number(value || 0);
            if (_reg.test(_leftValue)) {
              _leftValue = Number(_leftValue || 0);
              if (_leftChar === '(') {
                _leftFlag = _leftValue < newValue;
              } else {
                _leftFlag = _leftValue <= newValue;
              }
            }
            if (_leftFlag) {
              if (_reg.test(_rightValue)) {
                _rightValue = Number(_rightValue || 0);
                if (_rightChar === ')') {
                  _checkFlag = _rightValue > newValue;
                } else {
                  _checkFlag = _rightValue >= newValue;
                }
              } else {
                _checkFlag = true;
              }
            }
          }
        }
      }
    });
    return _checkFlag;
  };

  // 切换tab
  const handleChangeActiveKey = async activeKey => {
    let _validFlag = true;
    if (tabKey === 'INSPECT_OBJ') {
      _validFlag = await inspectObjDS.validate();
      if (_validFlag && inspectInfoDS.current.get('inspectTaskStatus') !== 'COMPLETED') {
        onComputedInspectObjData('OBJECT', null, []);
      }
      if (_validFlag) {
        inspectObjDS.unSelectAll();
      }
    }
    if (!_validFlag) {
      return;
    }
    setTabKey(activeKey);
    if (activeKey === 'DOC_INFO') {
      if (docInfoDS.getState('queryFlag') !== 'Y') {
        fetchDocInfo({
          params: {
            inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
            customizeUnitCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_DOC.BASIC,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_DOC.ACT`,
          },
          onSuccess: res => {
            docInfoDS.loadData([res]);
            docInfoDS.setState('queryFlag', 'Y');
          },
        });
      }
    } else if (activeKey === 'INSPECT_OBJ') {
      // 判断是否存在MAT或LOT的检验对象
      const _matOrLotFlag = inspectObjDS.find(record =>
        ['MAT', 'LOT'].includes(record.get('sourceObjectType')),
      );
      inspectObjDS.setState('matOrLotFlag', _matOrLotFlag);
    }
  };

  // 检验对象明细Tab-添加检验对象
  const handleAddInspectObj = () => {
    const inspectObjectIds = tabInspectObjSelectList.map((record: Record) =>
      record.get('inspectObjectId'),
    );
    return addInspectObj({
      params: inspectObjectIds,
      queryParams: {
        inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
      },
      onSuccess: res => {
        notification.success({});
        inspectObjDS.unSelectAll();
        onComputedInspectObjData('UPDATE', null, res || []);
      },
    });
  };

  const handleDeleteInspectObj = () => {
    const inspectObjectIdList: any = [];
    inspectObjDS.selected.forEach(record => {
      inspectObjectIdList.push(record.get('inspectObjectId'))
    })
    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <div>
          {intl
            .get(`${modelPrompt}.message.deleteConfirm`)
            .d('是否进行删除请确认')}
        </div>
      ),
      onOk: () => {
        // 替换成删除接口
        return itemDetailDelete({
          params: {
            inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
            inspectObjectIdList,
          },
          onSuccess: async () => {
            notification.success({});
            await initPageData();
          },
        });
      },
    })
  }

  // 检验对象明细Tab-计算检验对象数据
  const onComputedInspectObjData = (handleType, delInspectObjectId, updateInspectObjList) => {
    // 是否更新检验任务数量
    let _updateFlag = false;
    // 任务下检验对象总数
    let _sumObjectQty = inspectObjDS.getState('sumObjectQty') || 0;
    // 检验任务结果
    const _inspectResult = inspectInfoDS.current.get('inspectResult');

    if (handleType === 'DELETE' && delInspectObjectId) {
      // 删除检验对象
      const _delObjRecord = inspectObjDS.find(
        record => record.get('inspectObjectId') === delInspectObjectId,
      );
      if (_delObjRecord) {
        const _qty = Number(_delObjRecord.get('qty') || 0);
        _sumObjectQty -= _qty;
        _delObjRecord.set('inspectTaskObjectActId', null);
        _delObjRecord.init('inspectFlag', 'N');
        _delObjRecord.init('okQty', _qty);
        _delObjRecord.init('ngQty', null);
        _delObjRecord.init('scrapQty', null);
        // 在检验项目中缓存删除的标识，用于不可勾选去添加检验对象
        _delObjRecord.set('_cahceDelFlag', 'Y');
        _updateFlag = true;
      }
    } else if (handleType === 'UPDATE' && updateInspectObjList.length > 0) {
      // 添加检验对象
      updateInspectObjList.forEach(item => {
        const _updateObjRecord = inspectObjDS.find(
          record => record.get('inspectObjectId') === item.inspectObjectId,
        );
        _updateObjRecord.set('inspectTaskObjectActId', item.inspectTaskObjectActId);
        _updateObjRecord.set('inspectFlag', 'Y');
        const _qty = Number(_updateObjRecord.get('qty') || 0);
        _sumObjectQty += _qty;
        // 赋值数量
        _updateObjRecord.set('okQty', _inspectResult === 'NG' ? 0 : _qty);
        _updateObjRecord.set('ngQty', _inspectResult === 'NG' ? _qty : 0);
        _updateObjRecord.set('scrapQty', 0);
      });
      _updateFlag = true;
    } else if (handleType === 'SCAN' && updateInspectObjList.length === 1) {
      // 扫描检验对象
      const _qty = Number(updateInspectObjList[0].qty || 0);
      const _updateObjRecord = inspectObjDS.find(
        record => record.get('inspectObjectId') === updateInspectObjList[0].inspectObjectId,
      );
      if (_updateObjRecord) {
        const _inspectFlag = _updateObjRecord.get('inspectFlag');
        if (_inspectFlag !== 'Y') {
          _updateObjRecord.set(
            'inspectTaskObjectActId',
            updateInspectObjList[0].inspectTaskObjectActId,
          );
          _updateObjRecord.set('inspectFlag', 'Y');
          // 赋值数量
          _updateObjRecord.set('okQty', _inspectResult === 'NG' ? 0 : _qty);
          _updateObjRecord.set('ngQty', _inspectResult === 'NG' ? _qty : 0);
          _updateObjRecord.set('scrapQty', 0);
          // 清除删除缓存标识
          _updateObjRecord.set('_cahceDelFlag', 'N');
          _updateFlag = true;
        }
      } else {
        // 赋值数量
        updateInspectObjList[0].okQty = _inspectResult === 'NG' ? 0 : _qty;
        updateInspectObjList[0].ngQty = _inspectResult === 'NG' ? _qty : 0;
        updateInspectObjList[0].scrapQty = 0;
        inspectObjDS.create(updateInspectObjList[0]);
        _updateFlag = true;
      }
      if (_updateFlag) {
        _sumObjectQty += _qty;
      }
    } else if (handleType === 'TASK') {
      // 检验任务整单合格或不合格
      const _inspectSumQty = Number(inspectInfoDS.current?.get('inspectSumQty') || 0);
      if (_inspectResult === 'OK') {
        inspectInfoDS.current?.set('ngQty', 0);
        inspectInfoDS.current?.set('scrapQty', 0);
        inspectInfoDS.current?.set('okQty', _inspectSumQty);
        inspectObjDS.records
          .filter(record => record.get('inspectFlag') === 'Y')
          .forEach(record => {
            record.set('ngQty', 0);
            record.set('scrapQty', 0);
            record.set('okQty', Number(record.get('qty') || 0));
          });
      } else if (_inspectResult === 'NG') {
        const _sumObjectQty = inspectObjDS.getState('sumObjectQty') || 0;
        inspectInfoDS.current?.set('ngQty', _sumObjectQty);
        inspectInfoDS.current?.set('scrapQty', 0);
        inspectInfoDS.current?.set('okQty', _inspectSumQty - _sumObjectQty);
        inspectObjDS.records
          .filter(record => record.get('inspectFlag') === 'Y')
          .forEach(record => {
            record.set('ngQty', Number(record.get('qty') || 0));
            record.set('scrapQty', 0);
            record.set('okQty', 0);
          });
      }
    } else if (
      handleType === 'OBJECT' &&
      inspectInfoDS.current.get('inspectTaskStatus') !== 'RELEASED'
    ) {
      // 检验对象变更数量时切换tab计算任务数量
      let _sumNgQty = 0;
      let _sumScrapQty = 0;
      inspectObjDS.records
        .filter(record => record.get('inspectFlag') === 'Y')
        .forEach(record => {
          _sumNgQty += Number(record.get('ngQty') || 0);
          _sumScrapQty += Number(record.get('scrapQty') || 0);
        });
      inspectInfoDS.current?.set('ngQty', _sumNgQty);
      inspectInfoDS.current?.set('scrapQty', _sumScrapQty);
      const _inspectSumQty = Number(inspectInfoDS.current.get('inspectSumQty') || 0);
      const _sumOkQty = _inspectSumQty - _sumNgQty - _sumScrapQty;
      inspectInfoDS.current?.set('okQty', parseFloat(`${_sumOkQty.toFixed(6)}`));
    }

    if (_updateFlag) {
      inspectObjDS.setState('sumObjectQty', _sumObjectQty);
      // 更新任务数量
      onComputedInspectObjData('OBJECT', null, []);
    }
  };

  // 检验项目Tab-开始检验
  const handleItemStartInspect = (btnClickFlag = false) => {
    inspectInfoDS.current.set('startInspectingFlag', 'Y');
    return startInspectInfo({
      params: [inspectInfoDS.current?.get('inspectTaskId')],
      onSuccess: res => {
        inspectInfoDS.current.set('startInspectingFlag', 'N');
        inspectInfoDS.current.set('inspectTaskStatus', 'INSPECTING');
        docInfoDS.setState('queryFlag', 'N');
        notification.success({});
        const _inspectTaskStatusList =
          inspectInfoDS.getField('inspectTaskStatus')?.options?.records || [];
        let _inspectTaskStatusDesc = '检验中';
        if (_inspectTaskStatusList.length > 0) {
          const _inspectTaskStatusInfo = _inspectTaskStatusList.filter(
            record => record.get('statusCode') === 'INSPECTING',
          );
          if (_inspectTaskStatusInfo.length > 0) {
            _inspectTaskStatusDesc = _inspectTaskStatusInfo[0].get('description');
          }
        }
        inspectInfoDS.current.set('inspectTaskStatusDesc', _inspectTaskStatusDesc);
        inspectInfoDS.current.set(
          'actualStartTime',
          (res || []).length > 0 ? res[0].actualStartTime : '',
        );
        const _cacheDisposalType = inspectInfoDS.current.get('cacheDisposalType');
        inspectInfoDS.current.set('disposalType', _cacheDisposalType);
        setDisposalType(_cacheDisposalType);

        const _newDisposalType =
          inspectInfoDS.current.get('editDisposalFlag') === 'Y' &&
          !['INSPECTING', 'COMPLETED'].includes(inspectInfoDS.current.get('inspectTaskStatus'))
            ? null
            : inspectInfoDS.current.get('disposalType') || 'ALL';
        inspectInfoDS.current.set('disposalType', _newDisposalType);

        // 处置相关
        setDisposalType(_newDisposalType);

        // 当不为点击开始检验按钮时获取项
        const allFieldNames: any = [];
        if (!btnClickFlag && inspectInfoDS.getState('modeType') === ModeType.col) {
          const _recordResult = inspectItemColDS.find(
            record => record.get('inspectItemKey') === 'inspectResult',
          );
          if (_recordResult) {
            // 获取检验项
            Object.keys(_recordResult.data || {}).forEach(key => {
              if (key.indexOf('ITEM') !== -1 && key.indexOf('_RESULT') !== -1) {
                allFieldNames.push({
                  name: key.replace('_RESULT', ''),
                });
              }
            });
          }
        }
        onAutoEnterDefaultValue([], allFieldNames);
      },
      onFailed: () => {
        inspectInfoDS.current.set('startInspectingFlag', 'N');
      },
    });
  };

  // 处理稽差和小数位数
  const onHandleDecimalNumber = (
    rangeValueMap,
    defaultValue,
    dataType,
    inspectItemCode,
    decimalNumber,
  ) => {
    let newValue: any = defaultValue;
    let rangeFlag: boolean = false;
    if (dataType === 'VALUE') {
      newValue = Number(newValue || 0);
      let _rangeDecimalNum: number | null = null;
      if (inspectItemCode && rangeValueMap && rangeValueMap.has(inspectItemCode)) {
        // 计算稽差
        const _rangeValue = rangeValueMap.get(inspectItemCode);
        if (_rangeValue !== 0) {
          let _valLens = 0;
          const _valArr = `${newValue}`.split('.');
          if (_valArr.length > 1) {
            _valLens = _valArr[1].length;
          }
          let _rangeLens = 0;
          const _rangeArr = `${_rangeValue}`.split('.');
          if (_rangeArr.length > 1) {
            _rangeLens = _rangeArr[1].length;
          }
          _rangeDecimalNum = _valLens > _rangeLens ? _valLens : _rangeLens;
          newValue += _rangeValue;
          rangeFlag = true;
        }
      }
      // 处理小数位
      if ((decimalNumber || decimalNumber === 0) && isNumber(decimalNumber)) {
        newValue = newValue.toFixed(decimalNumber);
      } else if (_rangeDecimalNum || _rangeDecimalNum === 0) {
        newValue = parseFloat(newValue.toFixed(_rangeDecimalNum));
      }
    }
    return {
      newValue,
      rangeFlag,
    };
  };

  // 填充默认值
  const onAutoEnterDefaultValue = (addRecords: any = null, addFieldNames: any = null) => {
    const _modeType = inspectInfoDS.getState('modeType');
    const rangeValueMap = inspectInfoDS.getState('rangeValueMap');
    if (_modeType === ModeType.col) {
      const records: any =
        (addRecords || []).length > 0
          ? addRecords
          : inspectItemColDS.records.filter(
            record =>
              record.status !== 'delete' &&
                record.get('cacheInspectObjectId') &&
                record.get('inspectItem'),
          );
      const _itemColColumns: any =
        (addFieldNames || []).length > 0 ? addFieldNames : itemColColumns.slice(1);
      if (records.length > 0 && _itemColColumns.length > 0) {
        // 获取检验项的录入值字段
        const _fieldNameList: Array<any> = [];
        const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
        _itemColColumns.forEach(itemColumns => {
          const { name } = itemColumns;
          const _cacheInfo = _cacheItemData ? _cacheItemData[name] : {};
          const _dataQty = Number(_cacheInfo?.dataQty || 0);
          let defaultValue = _cacheInfo?.defaultValue;
          if (
            _cacheInfo?.dataType !== 'CALCULATE_FORMULA' &&
            (defaultValue || defaultValue === 0) &&
            _dataQty > 0
          ) {
            defaultValue = onHandleDecimalNumber(
              rangeValueMap,
              defaultValue,
              _cacheInfo?.dataType,
              _cacheInfo?.inspectItemCode,
              _cacheInfo?.decimalNumber,
            ).newValue;

            _fieldNameList.push({
              fieldName: name,
              samplingQtyCount: _dataQty,
              defaultValue,
              dataType: _cacheInfo.dataType,
              falseValues: _cacheInfo.falseValues,
              warningValues: _cacheInfo.warningValues,
              trueValues: _cacheInfo.trueValues,
              samplingType: _cacheInfo.samplingType,
              samplingQty: Number(_cacheInfo.newSamplingQty || 0),
            });
          }
        });
        if (_fieldNameList.length > 0) {
          const _resultDimension = inspectInfoDS.current?.get('resultDimension');
          records.forEach(record => {
            _fieldNameList.forEach(item => {
              const {
                fieldName,
                samplingQtyCount,
                defaultValue,
                dataType,
                falseValues,
                warningValues,
                trueValues,
                samplingType,
                samplingQty,
              } = item;
              // 判断是否为可编辑录入框
              const _objectDisabled =
                _resultDimension === 'RECORD_SAMPLE_VALUE' && !record.get('inspectObjectId');
              const _disabled =
                !!record?.get(`${fieldName}_DISABLED`) ||
                _objectDisabled ||
                (samplingType !== 'USER_DEFINED_SAMPLING' && samplingQty === 0);
              if (!_disabled) {
                // 获取颜色
                let _color: any = null;
                if (
                  ['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(dataType)
                ) {
                  _color = onGetValueColor(
                    defaultValue,
                    dataType,
                    falseValues,
                    warningValues,
                    trueValues,
                  );
                }
                // 赋值以及颜色
                for (let i = 0; i < samplingQtyCount; i++) {
                  const valueName = `${fieldName}_VALUE${i}`;
                  record.set(valueName, defaultValue);
                  record.set(`${valueName}_COLOR`, _color);
                  handleComputedQty(fieldName, dataType, false, true, false, true, valueName);
                }
              }
            });
          });
        }
      }
    } else if (_modeType === ModeType.row) {
      const records: any = (addRecords || []).length > 0 ? addRecords : inspectItemRowDS.records;
      if (records.length > 0) {
        records.forEach(record => {
          const fieldName = record.get('fieldName');
          let defaultValue = record.get('defaultValue');
          const dataType = record.get('dataType');
          const inspectItemCode = record.get('inspectItemCode');
          const decimalNumber = record.get('decimalNumber');
          const samplingQtyCount = Number(record.get('samplingQtyCount') || 0);
          if (
            dataType !== 'CALCULATE_FORMULA' &&
            (defaultValue || defaultValue === 0) &&
            samplingQtyCount > 0
          ) {
            defaultValue = onHandleDecimalNumber(
              rangeValueMap,
              defaultValue,
              dataType,
              inspectItemCode,
              decimalNumber,
            ).newValue;

            // 获取颜色
            let _color: any = null;
            if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(dataType)) {
              const falseValues = record.get('falseValues');
              const warningValues = record.get('warningValues');
              const trueValues = record.get('trueValues');
              _color = onGetValueColor(
                defaultValue,
                dataType,
                falseValues,
                warningValues,
                trueValues,
              );
            }
            for (let i = 0; i < samplingQtyCount; i++) {
              const valueName = `${fieldName}_VALUE${i}`;
              record.set(valueName, defaultValue);
              record.set(`${valueName}_COLOR`, _color);
              handleComputedQty(fieldName, dataType, false, true, record, true, valueName);
            }
          }
        });
      }
    } else if (_modeType === ModeType.rowObj && (addRecords || []).length > 0) {
      const lineRecord = inspectItemRowDS.current;
      const fieldName = lineRecord.get('fieldName');
      let defaultValue = lineRecord.get('defaultValue');
      const dataType = lineRecord.get('dataType');
      const inspectItemCode = lineRecord.get('inspectItemCode');
      const decimalNumber = lineRecord.get('decimalNumber');
      const samplingQtyCount = Number(lineRecord.get('dataQty') || 0);
      if (
        dataType !== 'CALCULATE_FORMULA' &&
        (defaultValue || defaultValue === 0) &&
        samplingQtyCount > 0
      ) {
        defaultValue = onHandleDecimalNumber(
          rangeValueMap,
          defaultValue,
          dataType,
          inspectItemCode,
          decimalNumber,
        ).newValue;

        // 获取颜色
        let _color: any = null;
        if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(dataType)) {
          const falseValues = lineRecord.get('falseValues');
          const warningValues = lineRecord.get('warningValues');
          const trueValues = lineRecord.get('trueValues');
          _color = onGetValueColor(defaultValue, dataType, falseValues, warningValues, trueValues);
        }
        const records: any =
          typeof addRecords[0] !== 'object'
            ? inspectItemRowObjValueDS.records.filter(record =>
              addRecords.includes(record.get('cacheInspectObjectId')),
            )
            : addRecords;
        records.forEach(record => {
          for (let i = 0; i < samplingQtyCount; i++) {
            const valueName = `${fieldName}_VALUE${i}`;
            record.set(valueName, defaultValue);
            record.set(`${valueName}_COLOR`, _color);
            handleComputedQty(fieldName, dataType);
          }
        });
      }
    }
  };

  // 检验项目Tab-保存并提交
  const handleItemSubmitInspect = async () => {
    const params = await onHandleValidSaveData(true);
    if (!params) {
      return;
    }
    Modal.confirm({
      title: intl
        .get(`${modelPrompt}.message.submitInspectTip`)
        .d(`提交后将不能再更改数据，是否继续？`),
      onOk: () => {
        return submitInspectInfo({
          params,
          onSuccess: async () => {
            notification.success({});
            onHandleColReset();
            await initPageData();
          },
        });
      },
    });
  };

  // 检验项目Tab-保存
  const handleItemSaveInspect = async () => {
    const params = await onHandleValidSaveData(false);
    if (!params) {
      return;
    }
    return saveInspectInfo({
      params,
      onSuccess: async () => {
        notification.success({});
        onHandleColReset();
        await initPageData();
      },
    });
  };

  // 检验项目Tab-保存或提交校验及数据处理
  const onHandleValidSaveData = async validFlag => {
    if (validFlag) {
      const _taskInfoValid = await inspectInfoDS.validate();
      if (!_taskInfoValid) {
        return false;
      }
    }

    // 校验任务合格数是否小于0
    if (Number(inspectInfoDS.current?.get('okQty') || 0) < 0) {
      setMinOkQtyFlag(true);
      notification.error({
        message: intl.get(`${modelPrompt}.message.validTaskMinOkQty`).d('合格数不能小于0'),
      });
      return false;
    }
    setMinOkQtyFlag(false);

    const _modeType = inspectInfoDS.getState('modeType');
    // 列模式校验
    if (_modeType === ModeType.col) {
      return onHandleColValidSaveData(validFlag);
    }
    // 带对象行模式校验
    if (_modeType === ModeType.rowObj) {
      return onHandleRowObjValidSaveData(validFlag);
    }
    // 不带对象行模式校验
    if (_modeType === ModeType.row) {
      return onHandleRowValidSaveData(validFlag);
    }
  };

  // 检验项目Tab-保存或提交头数据
  const onHandleHeadInfo = () => {
    const _headInfo = inspectInfoDS.current?.toData() || {};
    const objectSaveList = inspectObjDS.toData().filter(item => item.inspectFlag === 'Y');
    const {
      disposalType,
      curDispFunctionId,
      curDispFunction,
      reworkOperationId,
      reworkStepName,
      reworkWorkcellId,
      reworkRouterId,
      degradeMaterialId,
      degradeRevisionCode,
      degradeLevel,
      disposalData,
      ...head
    } = _headInfo || {};
    return {
      ...head,
      ngQty: Number(_headInfo.ngQty || 0),
      scrapQty: Number(_headInfo.scrapQty || 0),
      okQty: Number(_headInfo.okQty || 0),
      objectSaveList,
      disposalType,
      curDispFunctionId: disposalType === 'ALL' ? curDispFunctionId : null,
      curDispFunction: disposalType === 'ALL' ? curDispFunction : null,
      disposalData: {
        ...disposalData,
        reworkOperationId:
          disposalType === 'ALL' && curDispFunctionId && curDispFunction === 'REWORK_SOURCE'
            ? reworkOperationId
            : null,
        reworkStepName:
          disposalType === 'ALL' && curDispFunctionId && curDispFunction === 'REWORK_SOURCE'
            ? reworkStepName
            : null,
        reworkWorkcellId:
          disposalType === 'ALL' && curDispFunctionId && curDispFunction === 'REWORK_SOURCE'
            ? reworkWorkcellId
            : null,
        reworkRouterId:
          disposalType === 'ALL' && curDispFunctionId && curDispFunction === 'REWORK_ROUTER'
            ? reworkRouterId
            : null,
        degradeMaterialId:
          disposalType === 'ALL' && curDispFunctionId && curDispFunction === 'DEGRADE'
            ? degradeMaterialId
            : null,
        degradeRevisionCode:
          disposalType === 'ALL' && curDispFunctionId && curDispFunction === 'DEGRADE'
            ? degradeRevisionCode
            : null,
        degradeLevel:
          disposalType === 'ALL' && curDispFunctionId && curDispFunction === 'DEGRADE'
            ? degradeLevel
            : null,
      },
    };
  };

  // 检验项目Tab-保存或提交校验及数据处理-不带对象行模式
  const onHandleRowValidSaveData = async validFlag => {
    if (validFlag) {
      // 过滤掉多余的必输输入框

      for (let index = 0; index < inspectItemRowDS.records.length; index++) {
        const record = inspectItemRowDS.records[index];
        const keys = Object.keys(record.data);

        const _dataType = record?.get('dataType');
        const _samplingQtyCount = Number(record?.get('samplingQtyCount') || 0);

        const _addQtyCount = Number(record?.get('addQtyCount') || 0);

        const _fieldName = record?.get('fieldName');
        let _count =
          _dataType === 'CALCULATE_FORMULA'
            ? Number(record?.get('formulaCount') || 0)
            : _samplingQtyCount;

        // 取最大的数
        let taskLineObjects = 0;
        record?.get('taskLineObjects')?.forEach(taskLineObjectItem => {
          if (taskLineObjectItem.taskLineActDtls && taskLineObjectItem.taskLineActDtls.length > 0) {
            taskLineObjects += taskLineObjectItem.taskLineActDtls.length;
          } else {
            taskLineObjects++;
          }
        });

        if (taskLineObjects > _count) {
          _count = taskLineObjects;
        }

        _count += _addQtyCount;

        let inputList = keys.filter(e => e.includes(`_VALUE`));
        inputList = inputList.filter(e => e.split('_').length === 2);
        for (let index = 0; index < inputList.length; index++) {
          const element = inputList[index];
          const firstData = element.split(`_VALUE`)[0];
          if (firstData === _fieldName) {
            const indexNum = element.split(`${_fieldName}_VALUE`)[1];
            if (Number(indexNum) > _count - 1) {
              record.getField(element)?.set('required', false);
            }
          } else {
            record.getField(element)?.set('required', false);
          }
        }
      }

      const _rowItemValidFlag = await inspectItemRowDS.validate();
      if (!_rowItemValidFlag) {
        notification.error({
          message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项'),
        });
        return false;
      }

      // 校验计算公式检验项是否必输
      const _resultDimension = inspectInfoDS.current?.get('resultDimension');
      if (!['UNQUALIFIED_INSPECTION_TASK', 'UNQUALIFIED_ITEMS'].includes(_resultDimension)) {
        const _formulaRecords = inspectItemRowDS.records.filter(
          record =>
            record.get('requiredFlag') === 'Y' && record.get('dataType') === 'CALCULATE_FORMULA',
        );
        if (_formulaRecords.length > 0) {
          let _requiredItemValidLineIndex = null;
          const _requiredItemValidFlag = _formulaRecords.some(record => {
            const _formulaCount = Number(record.get('formulaCount') || 0);
            if (_formulaCount > 0) {
              const _fieldName = record?.get('fieldName');
              for (let i = 0; i < _formulaCount; i++) {
                const _value = record.get(`${_fieldName}_VALUE${i}`);
                if (_value || _value === 0) {
                  return false;
                }
              }
            }
            _requiredItemValidLineIndex = record.index;
            return true;
          });
          if (_requiredItemValidFlag) {
            notification.error({
              message: intl
                .get(`${modelPrompt}.message.validRequiredItem`)
                .d('存在必输检验项目检测值未录入完整，不允许提交，请检查！'),
            });
            await inspectItemRowDS.locate(_requiredItemValidLineIndex);
            return false;
          }
        }
      }
    }

    const _params: any = onHandleHeadInfo();
    const _lineList = inspectItemRowDS.toData();
    if (_lineList.length === 0) {
      return _params;
    }

    // 处理删除录入值数据
    const _deleteDtlIds: Array<any> = [];
    // 处理行数据变更
    _params.docLines = _lineList.map(lineItem => {
      const _taskLineActDtls: Array<any> = [];
      let _samplingQty =
        lineItem.dataType === 'CALCULATE_FORMULA'
          ? Number(lineItem.formulaCount || 0)
          : Number(lineItem.samplingQtyCount || 0);

      const _addQtyCount = Number(lineItem.addQtyCount || 0);

      // 取最大的数
      let taskLineObjects = 0;
      lineItem.taskLineObjects?.forEach(taskLineObjectItem => {
        if (taskLineObjectItem.taskLineActDtls && taskLineObjectItem.taskLineActDtls.length > 0) {
          taskLineObjects += taskLineObjectItem.taskLineActDtls.length;
        } else {
          taskLineObjects++;
        }
      });

      if (taskLineObjects > _samplingQty) {
        _samplingQty = taskLineObjects;
      }

      _samplingQty += _addQtyCount;
      const _fieldName = lineItem.fieldName;
      const _dataType = lineItem.dataType;
      // dtl原数据
      const _cacheTaskLineActDtlObj = lineItem.cacheTaskLineActDtlObj || {};

      let _cacheSequence = 0;
      let _cacheMaxSeq = lineItem.cacheMaxSeq || 1;
      for (let i = 0; i < _samplingQty; i++) {
        const _inspectValue = lineItem[`${_fieldName}_VALUE${i}`];
        const _inspectDocLineActDtlId = lineItem[`${_fieldName}_VALUE${i}_ID`];
        if (_inspectDocLineActDtlId) {
          const _baseInfo = _cacheTaskLineActDtlObj[_inspectDocLineActDtlId];
          if (_inspectValue !== _baseInfo.inspectValue) {
            if (_dataType === 'CALCULATE_FORMULA' && !_inspectValue && _inspectValue !== 0) {
              // 计算公式空数据删除
              _deleteDtlIds.push(_inspectDocLineActDtlId);
            } else {
              _taskLineActDtls.push({
                ..._baseInfo,
                inspectValue: _inspectValue,
                inspectResult: _inspectValue
                  ? lineItem[`${_fieldName}_VALUE${i}_COLOR`] === 'red'
                    ? 'NG'
                    : 'OK'
                  : null,
              });
            }
          }
          _cacheSequence = _baseInfo.sequence;
        } else if (_inspectValue) {
          _cacheSequence++;
          _taskLineActDtls.push({
            objectEnterValueSequence: _cacheMaxSeq,
            inspectValue: _inspectValue,
            inspectResult: _inspectValue
              ? lineItem[`${_fieldName}_VALUE${i}_COLOR`] === 'red'
                ? 'NG'
                : 'OK'
              : null,
            sequence: _cacheSequence,
          });
          _cacheMaxSeq++;
        }
      }

      const _maxSamplingQty = Number(lineItem.maxSamplingQty || 0);
      if (_maxSamplingQty > _samplingQty) {
        // 是否是已存在的dtl
        let _existActDtlIdFlag = false;
        for (let i = _samplingQty; i < _maxSamplingQty; i++) {
          if (!_existActDtlIdFlag) {
            const _inspectDocLineActDtlId = lineItem[`${_fieldName}_VALUE${i}_ID`];
            if (_inspectDocLineActDtlId) {
              _deleteDtlIds.push(_inspectDocLineActDtlId);
            } else {
              _existActDtlIdFlag = true;
            }
          }
        }
      }

      let _cacheAddLine = {};
      if (!lineItem.inspectDocLineId && lineItem.inspectItemId) {
        _cacheAddLine = cacheAddLineItemInfo[`ADD_${lineItem.inspectItemId}`] || {};
      }
      const _lineItem = omit(lineItem, ['cacheMaxSeq', 'cacheTaskLineActDtlObj', 'maxSamplingQty']);
      return {
        ..._cacheAddLine,
        ..._lineItem,
        taskLineActDtls: _taskLineActDtls,
      };
    });

    _params.inspectDocLineActDtlIds = Array.from(new Set(_deleteDtlIds));

    return _params;
  };

  // 检验项目Tab-保存或提交校验及数据处理-带对象行模式
  const onHandleRowObjValidSaveData = async validFlag => {
    const _resultDimension = inspectInfoDS.current?.get('resultDimension');
    if (validFlag) {
      const _rowItemValidFlag = await inspectItemRowDS.validate();
      const flag = inspectItemRowDS.toData().every(item => {
        return item.taskLineObjects.every(obj => {
          return Object.entries(obj).some(([key, value]) => {
            return key.endsWith('VALUE0') && value;
          });
        });
      });
      if (!_rowItemValidFlag || !flag) {
        notification.error({
          message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项'),
        });
        return false;
      }

      if (_resultDimension !== 'UNQUALIFIED_INSPECTION_TASK') {
        // 校验检验对象是否扫描
        let _scanObjectValidFlag = false;
        // 校验是否存在必输检验项目无检验对象
        let _requiredItemValidFlag = false;
        let _requiredItemValidLineIndex = 0;

        inspectItemRowDS.records.forEach((lineRecord, index) => {
          const _requiredFlag = lineRecord.get('requiredFlag');
          const _dataType = lineRecord.get('dataType');
          const _fieldName = lineRecord.get('fieldName');
          if (_dataType !== 'CALCULATE_FORMULA') {
            if (
              _requiredFlag === 'Y' &&
              (!_requiredItemValidFlag ||
                (_resultDimension === 'RECORD_SAMPLE_VALUE' && !_scanObjectValidFlag))
            ) {
              const _childRecords = lineRecord.getCascadeRecords('taskLineObjects') || [];
              if (_resultDimension === 'RECORD_SAMPLE_VALUE' && _childRecords.length > 0) {
                _childRecords.forEach(record => {
                  if (!_scanObjectValidFlag && !record.get('inspectObjectId')) {
                    _scanObjectValidFlag = true;
                  }
                });
              } else if (_requiredFlag === 'Y' && _childRecords.length < 1) {
                if (!_requiredItemValidFlag) {
                  _requiredItemValidLineIndex = index;
                }
                _requiredItemValidFlag = true;
              }
            }
          } else if (_requiredFlag === 'Y' && !_requiredItemValidFlag && !_scanObjectValidFlag) {
            // 计算公式检验项必输校验是否有录入值
            const _childRecords = lineRecord.getCascadeRecords('taskLineObjects') || [];
            if (_childRecords.length < 1) {
              _requiredItemValidFlag = true;
            } else {
              _requiredItemValidFlag = _childRecords.some(record => {
                const _formulaCount = Number(record.get(`${_fieldName}_FORMULA_COUNT`) || 0);
                if (_formulaCount > 0) {
                  for (let i = 0; i < _formulaCount; i++) {
                    const _value = record.get(`${_fieldName}_VALUE${i}`);
                    if (_value || _value === 0) {
                      return false;
                    }
                  }
                }
                return true;
              });
            }
            _requiredItemValidLineIndex = index;
          }
        });
        if (_requiredItemValidFlag) {
          notification.error({
            message: intl
              .get(`${modelPrompt}.message.validRequiredItem`)
              .d('存在必输检验项目检测值未录入完整，不允许提交，请检查！'),
          });
          await inspectItemRowDS.locate(_requiredItemValidLineIndex);
          return false;
        }
      }
    }
    if (_resultDimension === 'RECORD_SAMPLE_VALUE') {
      // 校验是否存在扫描的检验项未录入检测值
      let _requiredValueValidFlag = false;
      let _requiredItemValidLineIndex = 0;
      inspectItemRowDS.records.forEach((lineRecord, index) => {
        if (!_requiredValueValidFlag && lineRecord.dirty) {
          const _dataType = lineRecord.get('dataType');
          if (_dataType !== 'CALCULATE_FORMULA') {
            const _childRecords = lineRecord.getCascadeRecords('taskLineObjects') || [];
            _childRecords.forEach(record => {
              if (!_requiredValueValidFlag) {
                const data = record.data || {};
                if (data.inspectObjectId) {
                  Object.keys(data).forEach(key => {
                    if (
                      !_requiredValueValidFlag &&
                      key.indexOf(`${data.fieldName}_`) !== -1 &&
                      key.indexOf('_VALUE') !== -1 &&
                      key.indexOf('_COLOR') === -1 &&
                      !data[key]
                    ) {
                      _requiredValueValidFlag = true;
                      _requiredItemValidLineIndex = index;
                    }
                  });
                }
              }
            });
          }
        }
      });
      if (_requiredValueValidFlag) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.message.requiredValue`)
            .d('存在已扫描检验对象未录入检测值，请检查'),
        });
        await inspectItemRowDS.locate(_requiredItemValidLineIndex);
        return false;
      }
    }

    // 检验项列模式删除检验对象或新增检验项的缓存数据
    const _itemColUpdateCacheObj = inspectInfoDS.getState('itemColUpdateCacheObj') || {};

    const _inspectTaskObjectActIds = _itemColUpdateCacheObj?.deleteActIds || [];
    const _inspectDocLineActDtlIds = _itemColUpdateCacheObj?.deleteDtlIds || [];

    const _params: any = {
      ...onHandleHeadInfo(),
      inspectDocLineActDtlIds: Array.from(new Set(_inspectDocLineActDtlIds)),
    };
    const _lineList = inspectItemRowDS.toData();
    if (_lineList.length === 0) {
      return _params;
    }

    // 处理行数据变更
    _params.docLines = _lineList.map(lineItem => {
      let _dataQty = Number(lineItem.dataQty || 0);
      const _taskLineActDtls: Array<any> = [];
      let _taskLineObjects = lineItem.taskLineObjects || [];
      if (lineItem.dataType === 'CALCULATE_FORMULA') {
        _taskLineObjects =
          inspectItemRowDS
            .find(record => record.get('fieldName') === lineItem.fieldName)
            ?.get('taskLineObjects') || [];
      }

      const _newSamplingQty = Number(lineItem.samplingQty || 0);

      _taskLineObjects.forEach((valueItem, sequenceIndex) => {
        const _addQtyCount = Number(valueItem.addQtyCount || 0);

        if (lineItem.dataType === 'CALCULATE_FORMULA' && !valueItem.fieldName) {
          valueItem = valueItem.data;
        }

        const _fieldName = valueItem.fieldName;
        let _maxSeq = 0;
        const _inspectObjectId = valueItem.inspectObjectId;

        if (lineItem.dataType === 'CALCULATE_FORMULA') {
          _dataQty = Number(valueItem[`${_fieldName}_FORMULA_COUNT`] || 0);
        }

        // 判断对应的输入框个数
        if (valueItem?.taskLineActDtls?.length > 0) {
          if (_dataQty < valueItem?.taskLineActDtls?.length) {
            _dataQty = valueItem?.taskLineActDtls?.length;
          }
        }

        _dataQty += _addQtyCount;

        let _cacheMaxSeq = lineItem.cacheMaxSeq || 1;

        for (let i = 0; i < _dataQty; i++) {
          const _inspectValue = valueItem[`${_fieldName}_VALUE${i}`];
          const _inspectDocLineActDtlId = valueItem[`${_fieldName}_VALUE${i}_ID`];
          let _baseInfo: any = {};
          if (_inspectDocLineActDtlId) {
            const _baseList = (valueItem.taskLineActDtls || []).filter(
              item => item.inspectDocLineActDtlId === _inspectDocLineActDtlId,
            );
            if (_baseList.length > 0) {
              _baseInfo = _baseList[0];
            }
          }

          // 点击保存但是清空检验对象时需要删除dtl
          if (
            _resultDimension === 'RECORD_SAMPLE_VALUE' &&
            !_inspectObjectId &&
            lineItem.dataType !== 'CALCULATE_FORMULA'
          ) {
            if (
              _inspectDocLineActDtlId &&
              !_inspectDocLineActDtlIds.includes(_inspectDocLineActDtlId)
            ) {
              _inspectDocLineActDtlIds.push(_inspectDocLineActDtlId);
            }
            _taskLineActDtls.push({
              ..._baseInfo,
              inspectValue: _inspectValue,
              inspectResult: _inspectValue
                ? valueItem[`${_fieldName}_VALUE${i}_COLOR`] === 'red'
                  ? 'NG'
                  : 'OK'
                : null,
              inspectObjectId: valueItem.inspectObjectId,
              remark: valueItem.remark,
              sequence: sequenceIndex + 1,
              objectEnterValueSequence: _cacheMaxSeq,
            });
            _cacheMaxSeq++;
          } else {
            if (_baseInfo.objectEnterValueSequence) {
              _maxSeq =
                _maxSeq > Number(_baseInfo.objectEnterValueSequence || 0)
                  ? _maxSeq
                  : Number(_baseInfo.objectEnterValueSequence || 0);
            } else {
              _maxSeq++;
              _baseInfo.objectEnterValueSequence = _maxSeq;
            }
            if (
              lineItem.dataType === 'CALCULATE_FORMULA' &&
              _baseInfo.inspectDocLineActDtlId &&
              _inspectDocLineActDtlIds.includes(_baseInfo.inspectDocLineActDtlId)
            ) {
              _baseInfo.inspectDocLineActDtlId = null;
            }
            _taskLineActDtls.push({
              ..._baseInfo,
              inspectValue: _inspectValue,
              inspectResult: _inspectValue
                ? valueItem[`${_fieldName}_VALUE${i}_COLOR`] === 'red'
                  ? 'NG'
                  : 'OK'
                : null,
              inspectObjectId: valueItem.inspectObjectId,
              remark: valueItem.remark,
              sequence: sequenceIndex + 1,
              objectEnterValueSequence: _cacheMaxSeq,
            });
            _cacheMaxSeq++;
          }
        }
        // 点击保存但是清空检验对象时需要删除act
        if (_resultDimension === 'RECORD_SAMPLE_VALUE' && !_inspectObjectId) {
          if (valueItem.inspectTaskObjectActId) {
            _inspectTaskObjectActIds.push(valueItem.inspectTaskObjectActId);
            (valueItem.dtlIds || []).forEach(dtlId => {
              if (dtlId && !_inspectDocLineActDtlIds.includes(dtlId)) {
                _inspectDocLineActDtlIds.push(dtlId);
              }
            });
            valueItem.inspectTaskObjectActId = null;
            valueItem.dtlIds = [];
          }
          if (lineItem.samplingType === 'USER_DEFINED_SAMPLING') {
            // _newSamplingQty--;
          }
        }
      });

      let _cacheAddLine = {};
      if (!lineItem.inspectDocLineId && lineItem.inspectItemId) {
        _cacheAddLine = cacheAddLineItemInfo[`ADD_${lineItem.inspectItemId}`] || {};
      }
      if (
        lineItem.samplingType === 'USER_DEFINED_SAMPLING' &&
        lineItem.samplingQty !== _newSamplingQty
      ) {
        lineItem.samplingQty = _newSamplingQty;
        lineItem.okQty = _newSamplingQty - Number(lineItem.ngQty || 0);
      }
      const _lineItem = omit(lineItem, ['taskLineObjects']);
      return {
        ..._cacheAddLine,
        ..._lineItem,
        taskLineActDtls: _taskLineActDtls,
      };
    });
    _params?.docLines?.forEach(_docLines => {
      if (_params.resultDimension === 'RECORD_SAMPLE_VALUE') {
        let maxSequence = _docLines.maxSequence || 0;
        _docLines?.taskLineActDtls?.forEach(_taskLineActDtls => {
          if (!_taskLineActDtls.sequence && _taskLineActDtls.objectEnterValueSequence === 1) {
            _taskLineActDtls.sequence = maxSequence + 1;
            maxSequence++;
          } else if (!_taskLineActDtls.sequence) {
            _taskLineActDtls.sequence = maxSequence;
          }
          if (!_taskLineActDtls.inspectObjectId) {
            _taskLineActDtls.inspectDocLineActDtlId = 0;
            _taskLineActDtls.inspectObjectId = 0;
          }
        });
      }
    });
    return {
      ..._params,
      inspectDocLineActDtlIds: Array.from(new Set(_inspectDocLineActDtlIds)),
    };
  };

  // 检验项目Tab-保存或提交校验及数据处理-列模式
  const onHandleColValidSaveData = async validFlag => {
    const _resultDimension = inspectInfoDS.current?.get('resultDimension');

    if (validFlag) {
      const _colItemValidFlag = await inspectItemColDS.validate();
      const flag = inspectItemColDS
        .toData()
        .filter(item => item.cacheInspectObjectId)
        .every(obj => {
          return Object.entries(obj).every(([key]) => {
            if (key.endsWith('DISABLED') && !obj[key]) {
              return obj[`${key.split('_')[0]}_VALUE0`];
            }
            return true;
          });
        });
      if (!_colItemValidFlag || !flag) {
        notification.error({
          message: intl.get(`${modelPrompt}.message.required`).d('请输入必输项'),
        });
        return false;
      }

      // 校验检验对象是否扫描
      if (!['UNQUALIFIED_INSPECTION_TASK', 'UNQUALIFIED_ITEMS'].includes(_resultDimension)) {
        let _objectCount = 0;
        let _scanObjectValidFlag = false;
        const _cacheInspectObjectRecords = inspectItemColDS.records.filter(
          record => record.status !== 'delete' && record.get('cacheInspectObjectId'),
        );
        _cacheInspectObjectRecords.forEach(record => {
          _objectCount++;
          if (
            _resultDimension === 'RECORD_SAMPLE_VALUE' &&
            !_scanObjectValidFlag &&
            !record.get('inspectObjectId')
          ) {
            _scanObjectValidFlag = true;
          }
        });
        // if (_scanObjectValidFlag) {
        //   notification.error({
        //     message: intl.get(`${modelPrompt}.message.scanObject`).d('请扫描检验对象'),
        //   });
        //   return false;
        // }

        // 校验是否存在必输检验项目录入对象不足抽样数
        let _requiredItemValidFlag = false;
        const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
        Object.keys(_cacheItemData).forEach(key => {
          if (!_requiredItemValidFlag) {
            const _cacheItemInfo = _cacheItemData[key] || {};
            if (_cacheItemInfo.dataType !== 'CALCULATE_FORMULA') {
              if (
                _cacheItemInfo.requiredFlag === 'Y' &&
                Number(_cacheItemInfo.newSamplingQty || 0) > _objectCount
              ) {
                _requiredItemValidFlag = true;
              }
            } else if (_cacheItemInfo.requiredFlag === 'Y') {
              // 计算公式检验项必输校验是否有录入值
              _requiredItemValidFlag = !_cacheInspectObjectRecords.some(record => {
                const _formulaCount = Number(record.get(`${key}_FORMULA_COUNT`) || 0);
                if (_formulaCount > 0) {
                  for (let i = 0; i < _formulaCount; i++) {
                    const _value = record.get(`${key}_VALUE${i}`);
                    if (_value || _value === 0) {
                      return true;
                    }
                  }
                }
                return false;
              });
            }
          }
        });
        if (_requiredItemValidFlag) {
          notification.error({
            message: intl
              .get(`${modelPrompt}.message.validRequiredItem`)
              .d('存在必输检验项目检测值未录入完整，不允许提交，请检查！'),
          });
          return false;
        }
      }
    }

    // 检验项列模式删除检验对象或新增检验项的缓存数据
    const _itemColUpdateCacheObj = inspectInfoDS.getState('itemColUpdateCacheObj') || {};

    const _inspectTaskObjectActIds = _itemColUpdateCacheObj?.deleteActIds || [];
    const _inspectDocLineActDtlIds = _itemColUpdateCacheObj?.deleteDtlIds || [];

    const _params: any = {
      ...onHandleHeadInfo(),
      inspectTaskObjectActIds: _inspectTaskObjectActIds,
      inspectDocLineActDtlIds: Array.from(new Set(_inspectDocLineActDtlIds)),
    };
    if (itemColColumns.length === 1 || inspectItemColDS.toData().length === 0) {
      return _params;
    }

    // 检验项相关信息
    const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
    // 变更检验项行
    const _lineInfoObj: any = {};

    // 检验项自设主键->ITEM-ID
    const _fieldNames = itemColColumns
      .filter((item: any) => item.name !== 'inspectItem')
      .map((item: any) => item.name);
    const _allFieldName = _fieldNames;
    _fieldNames.forEach(field => {
      _allFieldName.push(`${field}_RESULT`);
      const _cacheInfo = _cacheItemData ? _cacheItemData[field] : {};
      for (let i = 0; i < Number(_cacheInfo?.dataQty || 0); i++) {
        _allFieldName.push(`${field}_VALUE${i}`);
      }
      _allFieldName.push(`${field}_OK`);
      _allFieldName.push(`${field}_NG`);
    });

    const _itemColDtlObj = inspectItemColDS.getState('itemColDtlObj') || {};

    inspectItemColDS.toData().forEach(itemObj => {
      if (itemObj.dtlIds?.length > 0) {
        (itemObj.dtlIds || []).forEach(dtlId => {
          _inspectDocLineActDtlIds.push(dtlId);
        });
      }
      // 点击保存但是清空检验对象时需要删除act
      if (
        _resultDimension === 'RECORD_SAMPLE_VALUE' &&
        !itemObj.inspectObjectId &&
        itemObj.inspectTaskObjectActId
      ) {
        _inspectTaskObjectActIds.push(itemObj.inspectTaskObjectActId);

        itemObj.inspectTaskObjectActId = null;
        itemObj.dtlIds = [];
      } else {
        const { inspectItemKey } = itemObj;
        Object.keys(itemObj).forEach(key => {
          if (
            _allFieldName.includes(key) ||
            (key.indexOf('_VALUE') !== -1 &&
              key.indexOf('_COLOR') === -1 &&
              _fieldNames.includes(key.split('_VALUE')[0]))
          ) {
            // 字段名
            const _fieldName = ['samplingQty', 'actEnclosure'].includes(inspectItemKey)
              ? key
              : inspectItemKey === 'inspectResult'
                ? key.split('_RESULT')[0]
                : inspectItemKey === 'okAndNg'
                  ? key.indexOf('_OK') !== -1
                    ? key.split('_OK')[0]
                    : key.split('_NG')[0]
                  : key.split('_VALUE')[0];
            const _cacheInfo = _cacheItemData ? _cacheItemData[_fieldName] : {};
            const _lineInfo = _lineInfoObj[_fieldName] || {
              inspectItemId: _cacheInfo?.inspectItemId,
              inspectDocLineId: _cacheInfo?.inspectDocLineId,
              inspectDocLineActId: _cacheInfo?.inspectDocLineActId,
              samplingQty: _cacheInfo?.samplingQty,
              samplingType: _cacheInfo?.samplingType,
              acceptStandard: _cacheInfo?.acceptStandard,
              inspectResult: _cacheInfo?.inspectResult,
              sequence: _cacheInfo?.sequence,
              okQty: _cacheInfo?.okQty,
              ngQty: _cacheInfo?.ngQty,
              actEnclosure: _cacheInfo?.actEnclosure,
              actualStartTime: _cacheInfo?.actualStartTime,
              actualEndTime: _cacheInfo?.actualEndTime,
              remark: _cacheInfo?.remark,
            };
            if (itemObj.inspectItemKey === 'samplingQty') {
              // 抽样数量初始值没值时才可编辑
              if (_cacheInfo?.samplingType === 'USER_DEFINED_SAMPLING' && itemObj[key]) {
                _lineInfo.samplingQty = itemObj[key];
              }
            } else if (itemObj.inspectItemKey === 'actEnclosure') {
              // 附件ID变化
              if (_cacheInfo?.actEnclosure !== itemObj[key]) {
                _lineInfo.actEnclosure = itemObj[key];
              }
            } else if (itemObj.inspectItemKey === 'inspectResult') {
              // 判定值变化
              if (_cacheInfo?.inspectResult !== itemObj[key]) {
                _lineInfo.inspectResult = itemObj[key];
              }
            } else if (itemObj.inspectItemKey === 'okAndNg') {
              // 合格数和不合格数
              if (key.indexOf('_OK') !== -1 && `${_cacheInfo?.okQty}` !== `${itemObj[key]}`) {
                _lineInfo.okQty = itemObj[key];
              } else if (
                key.indexOf('_NG') !== -1 &&
                `${_cacheInfo?.ngQty}` !== `${itemObj[key]}`
              ) {
                _lineInfo.ngQty = itemObj[key];
              }
            } else if (itemObj.cacheInspectObjectId) {
              const _dtlKey = `KEY${itemObj.cacheInspectObjectId}_${key}`;
              const _cacheDtlInfo = _itemColDtlObj[_dtlKey] || {};
              // 点击保存但是清空检验对象时需要删除dtl
              if (_resultDimension === 'RECORD_SAMPLE_VALUE' && !itemObj.inspectObjectId) {
                if (_cacheDtlInfo.inspectDocLineActDtlId) {
                  _inspectDocLineActDtlIds.push(_cacheDtlInfo.inspectDocLineActDtlId);
                }
              } else if (
                _cacheDtlInfo &&
                _cacheDtlInfo.inspectDocLineActDtlId &&
                _cacheDtlInfo.objectEnterValueSequence
              ) {
                _itemColDtlObj[`KEY${itemObj.cacheInspectObjectId}_${_fieldName}_MAXSEQ`] =
                  Number(_cacheDtlInfo.objectEnterValueSequence || 0) + 1;
              }
              // 新增或更新的录入值
              const _taskLineActDtls: Array<any> =
                (_lineInfo.taskLineActDtls || []).length > 0 ? _lineInfo.taskLineActDtls : [];
              const _dtlInfo: any = {
                ..._cacheDtlInfo,
                inspectObjectId: itemObj.inspectObjectId,
                sequence: itemObj.sequence,
                inspectValue: itemObj[key],
                inspectResult: null,
              };
              // 序号
              if (!_cacheDtlInfo.objectEnterValueSequence) {
                let _maxSeq =
                  _itemColDtlObj[`KEY${itemObj.cacheInspectObjectId}_${_fieldName}_MAXSEQ`] || 1;
                _dtlInfo.objectEnterValueSequence = _maxSeq;
                _maxSeq++;
                _itemColDtlObj[`KEY${itemObj.cacheInspectObjectId}_${_fieldName}_MAXSEQ`] = _maxSeq;
              }
              // 结果
              if (_dtlInfo.inspectValue) {
                const _color = itemObj[`${key}_COLOR`];
                _dtlInfo.inspectResult = _color === 'red' ? 'NG' : 'OK';
              }

              _dtlInfo.inspectDocLineActDtlId = undefined;

              _taskLineActDtls.push(_dtlInfo);
              _lineInfo.taskLineActDtls = _taskLineActDtls;
            }
            // }
            // 有变化才添加
            _lineInfoObj[_fieldName] = _lineInfo;
          }
        });
      }
    });

    // 转换格式
    const _docLines: Array<any> = [];
    Object.keys(_lineInfoObj).forEach(key => {
      const _lineInfo = _lineInfoObj[key];
      if (!_lineInfo.inspectDocLineId && _lineInfo.inspectItemId) {
        // 增加新建检验项行的传参
        const _cacheAddLine = cacheAddLineItemInfo[`ADD_${_lineInfo.inspectItemId}`] || {};
        _docLines.push({
          ..._cacheAddLine,
          ..._lineInfo,
          taskLineActDtls: _lineInfo.taskLineActDtls?.map(
            (_taskLineActDtl, _taskLineActDtlIndex) => {
              return {
                ..._taskLineActDtl,
                objectEnterValueSequence: _taskLineActDtlIndex,
              };
            },
          ),
        });
      } else {
        _docLines.push({
          ..._lineInfo,
          taskLineActDtls: _lineInfo.taskLineActDtls?.map(
            (_taskLineActDtl, _taskLineActDtlIndex) => {
              return {
                ..._taskLineActDtl,
                objectEnterValueSequence: _taskLineActDtlIndex,
              };
            },
          ),
        });
      }
    });
    _params.docLines = _docLines;
    return {
      ..._params,
      inspectTaskObjectActIds: _inspectTaskObjectActIds,
      inspectDocLineActDtlIds: Array.from(new Set(_inspectDocLineActDtlIds)),
    };
  };

  // 检验项目Tab-重置录入值
  const handleItemResetValue = () => {
    const _modeType = inspectInfoDS.getState('modeType');
    if (_modeType === ModeType.col) {
      itemColSelectList.forEach((record: Record) => {
        let _newValue = null;
        if (isUndefined(record.get('inspectItem'))) {
          _newValue = record.get('inspectItem');
        }
        handleChangeInspectItem(_newValue, record.get('inspectItem'), record);
      });
      onDisabledInspectValue(true);
    } else {
      itemRowSelectList.forEach((lineRecord: Record) => {
        const _fieldName = lineRecord.get('fieldName');
        const _samplingQty = Number(lineRecord.get('samplingQty') || 0);
        const _dataQty = Number(lineRecord.get('dataQty') || 0);
        if (_modeType === ModeType.rowObj) {
          const _childRecords = lineRecord.getCascadeRecords('taskLineObjects') || [];
          if (_childRecords.length > 0) {
            _childRecords.forEach(record => {
              record.set('inspectObjectId', null);
              record.set('sourceObjectCode', null);
              record.set('remark', null);
              for (let i = 0; i < _dataQty; i++) {
                record.set(`${_fieldName}_VALUE${i}`, null);
                record.set(`${_fieldName}_VALUE${i}_COLOR`, null);
              }
            });
          }
        } else {
          for (let i = 0; i < _samplingQty; i++) {
            lineRecord.set(`${_fieldName}_VALUE${i}`, null);
          }
        }
        lineRecord.set('ngQty', 0);
        lineRecord.set('okQty', _samplingQty);
        lineRecord.set('inspectResult', null);
      });
    }
  };

  // 检验项目Tab-添加检验对象
  const handleItemAddInspectObj = () => {
    // 校验是否未添加检验项行
    if (itemColColumns.length === 1) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.addInspectLine`).d('请先新增检验项'),
      });
      return;
    }

    const _cacheId = uuid();
    const _records = inspectItemColDS.records.filter(
      record => record.status !== 'delete' && record.get('cacheInspectObjectId'),
    );

    let _maxSeq = 1;
    if (_records.length > 0) {
      _maxSeq = Number(maxBy(_records, record => record.get('sequence'))?.get('sequence') || 0) + 1;
    }
    const _addInfo: any = {
      cacheInspectObjectId: _cacheId,
      inspectItemKey: `OBJ-${_cacheId}`,
      sequence: _maxSeq,
    };
    if (inspectInfoDS.current?.get('resultDimension') !== 'RECORD_SAMPLE_VALUE') {
      _addInfo.inspectItem = _maxSeq;
    }
    const newRecord = inspectItemColDS.create(_addInfo);

    // 录入值禁用处理
    onDisabledInspectValue();
    if (inspectInfoDS.current?.get('resultDimension') !== 'RECORD_SAMPLE_VALUE') {
      onAutoEnterDefaultValue([newRecord]);
    }
  };

  // 检验项目Tab-删除检验对象
  const handleItemDeleteInspectObj = () => {
    // 删除的时候由于可能存在对象为空的情况，所以需要dtlId也需要传
    const _deleteActIds: Array<any> = [];
    const _deleteDtlIds: Array<any> = [];
    itemColSelectList.forEach((record: Record) => {
      if (record.status !== 'add') {
        if (record?.get('inspectTaskObjectActId')) {
          _deleteActIds.push(record?.get('inspectTaskObjectActId'));
        }
        (record.get('dtlIds') || []).forEach(dtlId => {
          _deleteDtlIds.push(dtlId);
        });
      }
    });
    inspectItemColDS.remove(itemColSelectList);
    setItemColSelectList([]);
    if (_deleteActIds.length > 0 || _deleteDtlIds.length > 0) {
      const _itemColUpdateCacheObj = inspectInfoDS.getState('itemColUpdateCacheObj') || {};
      // 删除的objId
      if (_deleteActIds.length > 0) {
        if ((_itemColUpdateCacheObj?.deleteActIds || []).length > 0) {
          _itemColUpdateCacheObj.deleteActIds = _itemColUpdateCacheObj.deleteActIds.concat(
            _deleteActIds,
          );
        } else {
          _itemColUpdateCacheObj.deleteActIds = _deleteActIds;
        }
      }
      // 删除的dtlId
      if (_deleteDtlIds.length > 0) {
        if ((_itemColUpdateCacheObj?.deleteDtlIds || []).length > 0) {
          _itemColUpdateCacheObj.deleteDtlIds = _itemColUpdateCacheObj.deleteDtlIds.concat(
            _deleteDtlIds,
          );
        } else {
          _itemColUpdateCacheObj.deleteDtlIds = _deleteDtlIds;
        }
      }
      inspectInfoDS.setState('itemColUpdateCacheObj', _itemColUpdateCacheObj);
    }

    // 录入值禁用处理
    onDisabledInspectValue();
  };

  // 检验项目Tab-录入值禁用处理
  const onDisabledInspectValue = (resultFlag = false) => {
    const _fieldNames = itemColColumns
      .filter((item: any) => item.name !== 'inspectItem')
      .map((item: any) => item.name);
    const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
    _fieldNames.forEach(fieldName => {
      const _fieldInfo = _cacheItemData[fieldName] || {};
      if (_fieldInfo.samplingType !== 'USER_DEFINED_SAMPLING') {
        handleChangeSamplingQty(fieldName, _fieldInfo?.newSamplingQty || 0, resultFlag);
      }
    });
  };

  // 检验项目Tab-检验对象变更时清空对象ID和Code
  const handleChangeInspectItem = (val, oldValue, record) => {
    if (val !== oldValue && record) {
      record.set('inspectObjectId', null);
      const _delInspectObjectId = record.get('inspectObjectId');
      onComputedInspectObjData('DELETE', _delInspectObjectId, []);
    }
  };

  // 检验项目Tab-扫描检验对象
  const handleItemScanInspectObj = (event, record) => {
    const barcode = event?.target?.value.trim();
    if (!barcode && barcode !== 0) {
      return;
    }

    const _modeType = inspectInfoDS.getState('modeType');

    // 校验检验对象是否已存在
    let sameValidFlag = false;
    if (_modeType === ModeType.col) {
      sameValidFlag = inspectItemColDS.records.find(
        item => item.status !== 'delete' && item.get('sourceObjectCode') === barcode,
      );
    } else if (_modeType === ModeType.rowObj) {
      sameValidFlag =
        inspectItemRowObjValueDS.records.filter(
          item => item.status !== 'delete' && item.get('sourceObjectCode') === barcode,
        ).length > 1;
    }

    if (sameValidFlag) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.message.scanSameInspectObject`)
          .d('当前检验对象已存在，请检查！'),
      });
      return;
    }

    // 先调用mes服务扫描物料批或EO的接口
    scanBarcode({
      params: {
        barcode,
      },
      onSuccess: barcodeInfo => {
        handleItemScanInspectObjForTrans(barcode, record, {
          ...barcodeInfo,
          barcode,
          inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
          scanFlag: 'N',
          inspectDocLineActDtlIds: Array.from(new Set(record.get('dtlIds') || [])),
          inspectTaskObjectActId: record.get('inspectTaskObjectActId'),
          sequence: record.get('sequence'),
        });
      },
    });
  };

  // 检验项目Tab-扫描检验对象传输
  const handleItemScanInspectObjForTrans = (barcode, record, params) => {
    scanInspectObj({
      params,
      onSuccess: res => {
        if (res?.scanFlag === 'Y') {
          Modal.confirm({
            title: intl
              .get(`${modelPrompt}.message.scanInspectObjTip`, {
                barcode,
              })
              .d(`您扫描的条码${barcode}不在报检条码中，是否继续添加？`),
            onOk: () => {
              handleItemScanInspectObjForTrans(barcode, record, {
                ...params,
                inspectObjectId: res.inspectObjectId,
                sourceObjectCode: res.sourceObjectCode,
                scanFlag: 'Y',
              });
            },
            onCancel: () => {
              record.set('inspectItem', null);
              record.set('sourceObjectCode', null);
            },
          });
        } else {
          const { inspectObjectId, sourceObjectCode, inspectTaskObjectActId, sequence } = res || {};
          record.set('inspectObjectId', inspectObjectId);
          record.set('inspectTaskObjectActId', inspectTaskObjectActId);
          record.set('sequence', sequence);
          const _modeType = inspectInfoDS.getState('modeType');
          if (_modeType === ModeType.col) {
            record.init('inspectItem', sourceObjectCode);
            record.set('sourceObjectCode', sourceObjectCode);
          } else if (_modeType === ModeType.rowObj) {
            record.init('sourceObjectCode', sourceObjectCode);
          }

          const _itemColUpdateCacheObj = inspectInfoDS.getState('itemColUpdateCacheObj') || {};
          let _deleteActIds = _itemColUpdateCacheObj?.deleteActIds || [];
          if (_deleteActIds.length > 0) {
            const _deleteActIdsLen = _deleteActIds.length;
            _deleteActIds = _deleteActIds.filter(item => item !== inspectTaskObjectActId);
            if (_deleteActIds.length !== _deleteActIdsLen) {
              _itemColUpdateCacheObj.deleteActIds = _deleteActIds;
              inspectInfoDS.setState('itemColUpdateCacheObj', _itemColUpdateCacheObj);
            }
          }

          notification.success({});
          onComputedInspectObjData('SCAN', null, [res.taskObject || {}]);
        }
      },
    });
  };

  // 检验项目Tab-新增检验项行校验
  const handleItemAddValidInspectItem = record => {
    if (!record) {
      return;
    }
    // 判断是否存在已存在检验项
    let _addInspectValidFlag = true;
    const _modeType = inspectInfoDS.getState('modeType');
    if (_modeType === ModeType.col) {
      const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
      Object.keys(_cacheItemData).forEach(key => {
        if (
          _addInspectValidFlag &&
          _cacheItemData[key]?.inspectItemId === record.get('inspectItemId')
        ) {
          _addInspectValidFlag = false;
        }
      });
    } else {
      inspectItemRowDS.records.forEach(item => {
        if (_addInspectValidFlag && item.get('inspectItemId') === record.get('inspectItemId')) {
          _addInspectValidFlag = false;
        }
      });
    }

    if (!_addInspectValidFlag) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.message.sameInspectItemFlag`)
          .d('存在检验项目已选用，请检查！'),
      });
      return false;
    }
  };

  // 检验项目Tab-新增检验项行
  const handleItemAddInspectItem = value => {
    if (!value) {
      return;
    }

    handleCreateInspection(false, handleItemCanAddInspectItem, {
      initData: value,
      validateType: 'InspectionPlatform',
      needDefault: true,
    });

    // 清空lov数据
    inspectLovDS.current?.init('inspectItemObj', null);
    inspectLovDS.current?.init('inspectDocObj', null);
  };

  // 新增项目Tab-编辑后添加检验行
  const handleItemCanAddInspectItem = async () => {
    const { success, rows, details } = await childRef.current?.submit(false);
    if (!success) {
      return Promise.resolve(false);
    }

    // 如果选了抽样方式，则调用获取抽样数量的接口
    if (rows?.samplingMethodId) {
      const res = await fetchSamplingQty({
        params: {
          samplingMethodId: rows.samplingMethodId,
          lotSize: inspectInfoDS.current?.get('inspectSumQty'),
          strictness: inspectInfoDS.current?.get('strictness'),
        },
      });
      if (res && res.success) {
        notification.success({});
        const _acceptStandard = res.rows?.acceptStandard || {};
        const _ac = _acceptStandard?.ac;
        const _re = _acceptStandard?.re;
        handleItemOkAddInspectItem([
          {
            ...rows,
            samplingQty: res.rows?.sampleSize,
            samplingType: res.rows?.samplingType,
            acceptStandard: _ac || _re ? `${_ac}/${_re}` : '',
            defaultValue: details.defaultValue,
            dataQtyDisposition: details.dataQtyDisposition,
          },
        ]);
        return Promise.resolve(true);
      }
      return Promise.resolve(false);
    }
    handleItemOkAddInspectItem([rows]);
  };

  const handleItemOkAddInspectItem = value => {
    const _modeType = inspectInfoDS.getState('modeType');

    const _cacheFormulaObj = inspectInfoDS.getState('cacheFormulaObj') || {};
    let _userDefinedSamplingFlag = _cacheFormulaObj.userDefinedSamplingFlag === 'Y';

    // 新增的行
    const addRecords: any = [];

    if (_modeType === ModeType.col) {
      handleItemAddColInspectItem(value);
    } else if (_modeType === ModeType.rowObj) {
      let _maxSequence =
        Number(
          maxBy(inspectItemRowDS.records, record => record.get('sequence'))?.get('sequence') || 0,
        ) + 10;
      let _itemColKeyCount = itemColKeyCount;
      const _cacheAddLineItemInfo = cacheAddLineItemInfo || {};
      value.forEach(item => {
        if (item.samplingType === 'USER_DEFINED_SAMPLING') {
          item.samplingQty = Number(item.samplingQty || 0);
        }
        // 缓存检验项行信息
        if (item.inspectItemId && !_cacheAddLineItemInfo[`ADD_${item.inspectItemId}`]) {
          _cacheAddLineItemInfo[`ADD_${item.inspectItemId}`] = item;
        }

        const _fieldName = `ITEM${_itemColKeyCount}`;
        _itemColKeyCount++;

        const { okQty, ngQty } = item;

        let _samplingQtyCount = 0;
        if (item.dataQtyDisposition === 'DATA' || item.dataQtyDisposition === 'SAMPLE') {
          _samplingQtyCount = 1;
        } else {
          _samplingQtyCount = Number(item.dataQty || 0);
        }

        item.samplingQtyCount = _samplingQtyCount;
        const _newOkQty = okQty || okQty === 0 ? okQty : _samplingQtyCount - Number(ngQty || 0);
        const _newNgQty = ngQty || ngQty === 0 ? ngQty : _samplingQtyCount - Number(_newOkQty || 0);
        const _addItemInfo = {
          ...item,
          inspectDocLineId: null,
          inspectDocLineActId: null,
          trueValues: (item.trueValueList || []).map(trueItem => trueItem.dataValue),
          falseValues: (item.falseValueList || []).map(trueItem => trueItem.dataValue),
          warningValues: (item.warningValueList || []).map(trueItem => trueItem.dataValue),
          inspectResult: null,
          actEnclosure: null,
          sequence: _maxSequence,
          actualStartTime: null,
          actualEndTime: null,
          fieldName: _fieldName,
          inspectToolAndMethod:
            item.inspectToolDesc || item.inspectMethodDesc
              ? `${item.inspectToolDesc || ''}/${item.inspectMethodDesc || ''}`
              : '',
          okQty: _newOkQty,
          ngQty: _newNgQty,
        };

        // DS添加检测值字段
        onDSAddInspectValueField(
          _fieldName,
          inspectItemRowObjValueDS,
          item.requiredFlag,
          _samplingQtyCount,
        );
        const _sourceObjectObj = inspectLovDS.current?.get('sourceObjectObj');
        if (_samplingQtyCount > 0) {
          const _addTaskLineObjects: Array<any> = [];
          for (let i = 0; i < _samplingQtyCount; i++) {
            const cacheInspectObjectId = uuid();
            let _addObjectInfo: any = {
              cacheInspectObjectId,
              fieldName: _fieldName,
              requiredFlag: item.requiredFlag,
            };
            if (i === 0 && _sourceObjectObj?.inspectObjectId) {
              _addObjectInfo = {
                ..._addObjectInfo,
                inspectObjectId: _sourceObjectObj.inspectObjectId,
                sourceObjectCode: _sourceObjectObj.sourceObjectCode,
                sequence: _sourceObjectObj.sequence,
              };
              addRecords.push(cacheInspectObjectId);
            }
            _addTaskLineObjects.push(_addObjectInfo);
          }
          _addItemInfo.taskLineObjects = _addTaskLineObjects;
        } else if (
          item.samplingType === 'USER_DEFINED_SAMPLING' &&
          _sourceObjectObj?.inspectObjectId
        ) {
          const cacheInspectObjectId = uuid();
          _addItemInfo.taskLineObjects = [
            {
              cacheInspectObjectId,
              fieldName: _fieldName,
              requiredFlag: item.requiredFlag,
              inspectObjectId: _sourceObjectObj.inspectObjectId,
              sourceObjectCode: _sourceObjectObj.sourceObjectCode,
              sequence: _sourceObjectObj.sequence,
            },
          ];
          _addItemInfo.samplingQty = 1;
          _addItemInfo.okQty = 1;
          addRecords.push(cacheInspectObjectId);
        }

        inspectItemRowDS.create(_addItemInfo);
        _maxSequence += 10;

        // 抽样数量是否存在可编辑
        if (!_userDefinedSamplingFlag && item.samplingType === 'USER_DEFINED_SAMPLING') {
          _userDefinedSamplingFlag = true;
        }
      });
      setItemColKeyCount(_itemColKeyCount);
      setCacheAddLineItemInfo(_cacheAddLineItemInfo);
    } else {
      let _maxSequence =
        Number(
          maxBy(inspectItemRowDS.records, record => record.get('sequence'))?.get('sequence') || 0,
        ) + 10;
      let _itemColKeyCount = itemColKeyCount;
      const _cacheAddLineItemInfo = cacheAddLineItemInfo || {};
      value.forEach(item => {
        if (item.samplingType === 'USER_DEFINED_SAMPLING') {
          item.samplingQty = Number(item.samplingQty || 0);
        }
        // 缓存检验项行信息
        if (item.inspectItemId && !_cacheAddLineItemInfo[`ADD_${item.inspectItemId}`]) {
          _cacheAddLineItemInfo[`ADD_${item.inspectItemId}`] = item;
        }

        const _fieldName = `ITEM${_itemColKeyCount}`;
        _itemColKeyCount++;

        const { okQty, ngQty } = item;

        let _samplingQtyCount = 0;
        if (item.dataQtyDisposition === 'DATA') {
          _samplingQtyCount = Number(item.dataQty || 0);
        } else if (item.dataQtyDisposition === 'SAMPLE') {
          _samplingQtyCount = Number(item.samplingQty || 0);
        } else {
          _samplingQtyCount = Number(item.samplingQty || 0) * Number(item.dataQty || 0);
        }
        item.samplingQtyCount = _samplingQtyCount;
        const _newOkQty = okQty || okQty === 0 ? okQty : _samplingQtyCount - Number(ngQty || 0);
        const _newNgQty = ngQty || ngQty === 0 ? ngQty : _samplingQtyCount - Number(_newOkQty || 0);
        const _addItemInfo = {
          ...item,
          inspectDocLineId: null,
          inspectDocLineActId: null,
          trueValues: (item.trueValueList || []).map(trueItem => trueItem.dataValue),
          falseValues: (item.falseValueList || []).map(trueItem => trueItem.dataValue),
          warningValues: (item.warningValueList || []).map(trueItem => trueItem.dataValue),
          inspectResult: null,
          actEnclosure: null,
          sequence: _maxSequence,
          actualStartTime: null,
          actualEndTime: null,
          fieldName: _fieldName,
          inspectToolAndMethod:
            item.inspectToolDesc || item.inspectMethodDesc
              ? `${item.inspectToolDesc || ''}/${item.inspectMethodDesc || ''}`
              : '',
          okQty: _newOkQty,
          ngQty: _newNgQty,
        };

        // DS添加检测值字段
        onDSAddInspectValueField(
          _fieldName,
          inspectItemRowDS,
          item.requiredFlag,
          _samplingQtyCount,
        );

        const newRecord = inspectItemRowDS.create(_addItemInfo);
        if (_modeType === ModeType.row) {
          addRecords.push(newRecord);
        }
        _maxSequence += 10;

        // 抽样数量是否存在可编辑
        if (!_userDefinedSamplingFlag && item.samplingType === 'USER_DEFINED_SAMPLING') {
          _userDefinedSamplingFlag = true;
        }
      });
      setItemColKeyCount(_itemColKeyCount);
      setCacheAddLineItemInfo(_cacheAddLineItemInfo);
    }

    inspectInfoDS.setState('cacheFormulaObj', {
      ..._cacheFormulaObj,
      userDefinedSamplingFlag: _userDefinedSamplingFlag ? 'Y' : 'N',
    });
    setColShowFlag(false);

    if (addRecords.length > 0) {
      onAutoEnterDefaultValue(addRecords);
    }
  };

  // 检验项目Tab-新增检验项行-列模式处理
  const handleItemAddColInspectItem = value => {
    const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
    const _itemColColumns: any = itemColColumns || [];
    const _inspectItemKeys = [
      'inspectItemTypeDesc',
      'inspectToolAndMethod',
      'qualityCharacteristicDesc',
      'acceptStandard',
      'samplingQty',
      'okAndNg',
      'inspectResult',
      'actEnclosure',
    ];
    const _records = inspectItemColDS.records || [];
    let _itemColKeyCount = itemColKeyCount;

    // 获取检验项行最大序号sequence
    let maxSequence = 0;
    Object.keys(_cacheItemData).forEach(key => {
      const _value = _cacheItemData[key] || {};
      if (Number(_value.sequence || 0) > maxSequence) {
        maxSequence = Number(_value.sequence || 0);
      }
    });
    maxSequence += 10;

    const addFieldNames: any = [];
    if (value.length > 0) {
      const _cacheAddLineItemInfo = cacheAddLineItemInfo || {};
      value.forEach(item => {
        if (item.samplingType === 'USER_DEFINED_SAMPLING') {
          item.samplingQty = Number(item.samplingQty || 0);
        }
        // 缓存检验项行信息
        if (item.inspectItemId && !_cacheAddLineItemInfo[`ADD_${item.inspectItemId}`]) {
          _cacheAddLineItemInfo[`ADD_${item.inspectItemId}`] = item;
        }

        // 唯一列名
        const _fieldName = `ITEM${_itemColKeyCount}`;
        addFieldNames.push({
          name: _fieldName,
        });

        // DS添加数据及缓存数据
        _cacheItemData[_fieldName] = {
          ...onHandleCacheData(_fieldName, item),
          inspectDocLineId: null,
          inspectDocLineActId: null,
          trueValues: (item.trueValueList || []).map(trueItem => trueItem.dataValue),
          falseValues: (item.falseValueList || []).map(trueItem => trueItem.dataValue),
          warningValues: (item.warningValueList || []).map(trueItem => trueItem.dataValue),
          inspectResult: null,
          actEnclosure: null,
          sequence: maxSequence,
          actualStartTime: null,
          actualEndTime: null,
        };

        maxSequence += 10;

        // 渲染动态单元格
        const _columnInfo = onHandleRenderCell(_fieldName);
        _itemColColumns.push(_columnInfo);

        // 处理行数据
        _records.forEach(record => {
          const _inspectItemKey = record.get('inspectItemKey');
          if (_inspectItemKeys.includes(_inspectItemKey)) {
            if (_inspectItemKey === 'okAndNg') {
              const { okQty, ngQty, samplingQty } = item;
              const _newOkQty =
                okQty || okQty === 0 ? okQty : Number(samplingQty || 0) - Number(ngQty || 0);
              record.init(`${_fieldName}_OK`, _newOkQty);
              record.init(
                `${_fieldName}_NG`,
                ngQty || ngQty === 0 ? ngQty : Number(samplingQty || 0) - Number(_newOkQty || 0),
              );
            } else if (_inspectItemKey === 'inspectToolAndMethod') {
              record.set(
                _fieldName,
                item.inspectToolDesc || item.inspectMethodDesc
                  ? `${item.inspectToolDesc || ''}/${item.inspectMethodDesc || ''}`
                  : '',
              );
            } else if (['actEnclosure', 'inspectResult'].includes(_inspectItemKey)) {
              record.set(_fieldName, null);
              if (_inspectItemKey === 'actEnclosure') {
                record.set(`${_fieldName}_ENCLOSURE`, item.enclosure);
              }
            } else {
              record.set(_fieldName, item[_inspectItemKey]);
            }
          }
        });

        _itemColKeyCount++;
      });
      setItemColKeyCount(_itemColKeyCount);
      setCacheAddLineItemInfo(_cacheAddLineItemInfo);

      onAutoEnterDefaultValue(null, addFieldNames);
    }
    inspectItemColDS.setState('cacheItemData', _cacheItemData);
    setItemColColumns(_itemColColumns);
  };

  // 新增项目Tab-Lov新增检验项目
  const handleCreateInspection = (_modal, callback, ...args) => {
    if (_modal) {
      _modal.close();
    }
    const _extendArgs = (args || []).length > 0 ? args[0] : {};
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.createInspectItem`).d('新建检验项目'),
      destroyOnClose: true,
      drawer: true,
      style: {
        width: 1080,
      },
      onOk: callback,
      children: (
        <DetailComponent
          ref={childRef}
          canEdit
          kid="create"
          column={3}
          customizeForm={customizeForm}
          requiredField={['enterMethod', 'dataQty'].concat(
            inspectInfoDS.current?.get('samplingDimension') === 'INSPECT_ITEM_SAMPLING'
              ? ['samplingMethodLov']
              : [],
          )}
          {..._extendArgs}
          operationType="PLATFORM_FUNC_CREATE"
        />
      ),
    });
  };

  // 新增项目Tab-新建检验项目点击确定的回调函数
  const handleCreateInspectionCallback = async () => {
    const { success } = await childRef.current?.submit(false);
    if (success) {
      notification.success({});
      return Promise.resolve(true);
    }
    return Promise.resolve(false);
  };

  // 检验项目Tab-复制检验值
  const handleItemCopyInspectObj = () => {
    inspectLovDS.current?.set('operationType', 'DOC_COPY');
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
        ds: inspectLovDS,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.copyItemInspectObj`).d('选择复制来源'),
      drawer: false,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: 500,
      },
      contentStyle: {
        width: 500,
      },
      className: 'hmes-style-modal',
      children: (
        <Form dataSet={inspectLovDS} columns={1} labelWidth={80} style={{ marginRight: 20 }}>
          <Lov name="inspectDocObj" />
        </Form>
      ),
      onOk: () => handleItemOkCopyInspectObj(),
    });
  };

  // 检验项目Tab-确定复制检验值
  const handleItemOkCopyInspectObj = async () => {
    const validFlag = await inspectLovDS.validate();
    if (!validFlag) {
      return Promise.resolve(false);
    }
    return copyInspectObj({
      params: {
        inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
        inspectDocId: inspectInfoDS.current?.get('inspectDocId'),
        sourceInspectDocId: inspectLovDS.current?.get('inspectDocId'),
      },
      onSuccess: async () => {
        notification.success({});
        inspectLovDS.reset();
        onHandleColReset();
        await initPageData();
      },
    });
  };

  // 检验项目Tab-删除检验项
  const handleItemDeleteInspectLine = () => {
    const _modeType = inspectInfoDS.getState('modeType');
    if (_modeType === ModeType.col) {
      const _cacheItemData = inspectItemColDS.getState('cacheItemData');
      const _cacheAddItemList: Array<any> = [];
      Object.keys(_cacheItemData).forEach(key => {
        const { inspectDocLineId, sequence, inspectItemDesc, requiredFlag } = _cacheItemData[key];
        if (!inspectDocLineId) {
          _cacheAddItemList.push({
            inspectDocLineId: uuid(),
            fieldName: key,
            sequence,
            inspectItemDesc,
            requiredFlag,
          });
        }
      });
      inspectDeleteItemColDS.loadData(_cacheAddItemList);
      Modal.open({
        ...drawerPropsC7n({
          ds: inspectDeleteItemColDS,
        }),
        key: Modal.key(),
        title: intl.get(`${modelPrompt}.title.selectDeleteItem`).d('请选择删除的检验项'),
        drawer: false,
        destroyOnClose: true,
        closable: true,
        maskClosable: true,
        className: 'hmes-style-modal',
        children: <Table dataSet={inspectDeleteItemColDS} columns={colDeleteItemColumns} />,
        onOk: () => handleItemOkDeleteInspectLine(),
      });
    } else {
      const addInspectItemList = itemRowSelectList.filter(
        (record: Record) => record.status === 'add',
      );
      inspectItemRowDS.remove(addInspectItemList);
      inspectItemRowDS.unSelectAll();
      const ngRecordFlag = inspectItemRowDS.records.find(
        item => item.get('inspectResult') === 'NG',
      );
      if (ngRecordFlag) {
        inspectInfoDS.current?.set('inspectResult', 'NG');
      } else {
        const okRecordFlag = inspectItemRowDS.records.find(
          item => item.get('inspectResult') === 'OK',
        );
        inspectInfoDS.current?.set('inspectResult', okRecordFlag ? 'OK' : null);
      }
      notification.success({});
    }
  };

  // 检验项目Tab-列模式弹窗界面删除检验项
  const handleItemOkDeleteInspectLine = () => {
    const _selectDeleteItemList = inspectDeleteItemColDS.selected;
    if (_selectDeleteItemList.length < 1) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.selectDeleteItem`).d('请选择需要删除的检验项'),
      });
      return Promise.resolve(false);
    }
    const _deleteFieldNames = _selectDeleteItemList.map(record => record.get('fieldName'));
    const _itemColColumns = itemColColumns.filter(
      (item: any) => !_deleteFieldNames.includes(item.name),
    );
    setItemColColumns(_itemColColumns);
    const _cacheItemData = inspectItemColDS.getState('cacheItemData');
    const _newCacheItemData = omit(_cacheItemData, _deleteFieldNames);
    inspectItemColDS.setState('cacheItemData', _newCacheItemData);
    notification.success({});
  };

  // 检验项目Tab-列模式弹窗界面列表显示字段
  const colDeleteItemColumns: ColumnProps[] = [
    {
      name: 'sequence',
      width: 100,
      align: ColumnAlign.center,
    },
    {
      name: 'inspectItemDesc',
      minWidth: 120,
      renderer: ({ value, record }) => {
        return (
          <span>
            {record?.get('requiredFlag') === 'Y' && <span style={{ color: 'red' }}>*&nbsp;</span>}
            {value}
          </span>
        );
      },
    },
  ];

  // 单据信息
  const docInfoProps = {
    docInfoDS,
    history,
    customizeForm,
  };

  // 检验项目信息
  const inspectInfoProps = {
    setMinOkQtyFlag,
    canEdit,
    ModeType,
    NcRecordDimension,
    path,
    inspectInfoDS,
    inspectItemRowDS,
    inspectItemRowObjValueDS,
    inspectItemColDS,
    inspectLovDS,
    disposalModeDS,
    customizeForm,
    customizeTable,
    itemColColumns,
    colShowFlag,
    minOkQtyFlag,
    cacheMinWidth,
    disposalType,
    disposalTypeDesc,
    disposalMode,
    onAutoEnterDefaultValue,
    setCacheMinWidth,
    setDisposalType,
    setDisposalMode,
    handleItemAddInspectObj,
    handleChangeValueColor,
    handleComputedQty,
    handleChangeInspectItem,
    handleItemScanInspectObj,
    handleChangeNgQty,
    handleBatchChangeData,
    disposalStatus,
    inspectObjDS,
    inspectResultNow,
  };

  // 检验对象明细
  const inspectObjProps = {
    canEdit,
    inspectObjDS,
    customizeTable,
    inspectInfoDS,
  };

  // 检验不良记录
  const ncRecordProps = {
    canEdit,
    visible: inspectInfoDS.current?.get('inspectNcRecordDimension') === NcRecordDimension.taskNc,
    customizeTable,
    queryParams: {
      inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
      inspectDocId: inspectInfoDS.current?.get('inspectDocId'),
      inspectNcRecordDimension: inspectInfoDS.current?.get('inspectNcRecordDimension'),
    },
  };

  const handleNGExamineApprove = () => {
    ncReview({
      params: {
        inspectDocId: inspectInfoDS.current?.get('inspectDocId'),
        workFlowCode: inspectInfoDS.current?.get('workFlowCode'),
      },
      onSuccess: () => {
        notification.success({});
        initPageData();
      },
    });
  };

  const handleNGExamineDisposalMenu = () => {
    setCanEdit(true);
    setDisposalStatus(true);
    inspectInfoDS.setState('editDisposal', true);
  };

  const handleNcReviewSubmit = async () => {
    const params = await onHandleValidSaveData(true);
    ncReviewSubmit({
      params,
      onSuccess: () => {
        notification.success({});
        setDisposalStatus(false);
        setCanEdit(false);
        inspectInfoDS.setState('editDisposal', false);
        initPageData();
      },
    });
  };

  const handleNcReviewSubmitCancel = () => {
    setDisposalStatus(false);
    setCanEdit(false);
    inspectInfoDS.setState('editDisposal', false);
    initPageData();
  };

  const clickDisposalApprovingMenu = ({ key }) => {
    switch (key) {
      case 'APPROVING':
        handleNGExamineApprove();
        break;
      case 'DISPOSAL':
        handleNGExamineDisposalMenu();
        break;
      default:
        break;
    }
  };

  const handleEditingSave = async () => {
    const params = await onHandleValidSaveData(false);
    if (!params) {
      return;
    }
    return editingSaveInfo({
      params,
      onSuccess: async () => {
        notification.success({});
        onHandleColReset();
        setEditingBtn(false);
        setCanEdit(false);
        inspectInfoDS.setState('canEdit', false);
        await initPageData();
      },
    });
  };

  const InspectObjButtons = observer(({ds}: {
    ds: DataSet;
  }) => {
    let addDisabled = false
    let deleteDisabled = false
    if (!(ds?.selected?.length > 0)) {
      addDisabled = true
      deleteDisabled = true
    }
    ds.selected.forEach(record => {
      if (record.get('inspectFlag') !== 'N' || record.get('_cahceDelFlag') === 'Y') {
        addDisabled = true
      }
      if (record.get('handworkFlag') !== 'Y') {
        deleteDisabled = true
      }
    })
    return (
      <>
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit || addDisabled}
          onClick={handleAddInspectObj}
          permissionList={[
            {
              code: `inspectionPlatform.dist.button.addInspectObj`,
              type: 'button',
              meaning: '详情页-添加检验对象',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.addInspectObj`).d('添加检验对象')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="delete"
          disabled={!canEdit || deleteDisabled}
          onClick={handleDeleteInspectObj}
          permissionList={[
            {
              code: `inspectionPlatform.dist.button.addInspectObj`,
              type: 'button',
              meaning: '详情页-添加检验对象',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.deleteInspectObj`).d('删除检验对象')}
        </PermissionButton>
      </>
    )
  })

  const fixBtnAuth = () => {
    const lastInspectResult= inspectInfoDS.current.get('lastInspectResult')
    const inspectDocStatus= inspectInfoDS.current.get('inspectDocStatus')
    const reviewStatus= inspectInfoDS.current.get('reviewStatus')

    if (lastInspectResult === 'OK') {
      return inspectDocStatus !== 'LAST_COMPLETED'
    }
    if (lastInspectResult === 'NG') {
      if (['FIRST_COMPLETED', 'LAST_COMPLETED'].includes(inspectDocStatus)) {
        return !(reviewStatus === 'REVIEWED' || !reviewStatus)
      }
      return true
    }
    return true
  }

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title`).d('检验平台')}
        backPath="/hwms/inspection-platform-workshop/list"
      >
        {tabKey === 'INSPECT_OBJ' && !disposalStatus && !editingBtn && (
          <>
            <InspectObjButtons ds={inspectObjDS} />
          </>
        )}
        {tabKey === 'INSPECT_INFO' && !disposalStatus && !editingBtn && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              disabled={!canEdit}
              loading={submitInspectInfoLoading}
              onClick={handleItemSubmitInspect}
              permissionList={[
                {
                  code: `inspectionPlatform.dist.button.saveSubmit`,
                  type: 'button',
                  meaning: '详情页-保存并提交',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.submit`).d('保存并提交')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              disabled={!canEdit}
              onClick={handleItemSaveInspect}
              permissionList={[
                {
                  code: `inspectionPlatform.dist.button.save`,
                  type: 'button',
                  meaning: '详情页-保存',
                },
              ]}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton
              disabled={!canEdit || inspectInfoDS.current?.get('inspectTaskStatus') !== 'RELEASED'}
              // loading={startInspectInfoLoading}
              onClick={() => handleItemStartInspect(true)}
              permissionList={[
                {
                  code: `inspectionPlatform.dist.button.startInspect`,
                  type: 'button',
                  meaning: '详情页-开始检验',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.startInspect`).d('开始检验')}
            </PermissionButton>

            <Dropdown
              overlay={
                <Menu className={styles['split-menu']} style={{ width: '120px' }}>
                  <Menu.Item key="ADD">
                    <PermissionButton
                      type="c7n-pro"
                      funcType="flat"
                      disabled={!canEdit}
                      permissionList={[
                        {
                          code: `inspectionPlatform.dist.button.add`,
                          type: 'button',
                          meaning: '详情页-新增检验项',
                        },
                      ]}
                      style={{ padding: 0, border: 'none' }}
                    >
                      <Lov
                        funcType={FuncType.flat}
                        dataSet={inspectLovDS}
                        name="inspectItemObj"
                        disabled={!canEdit}
                        onChange={handleItemAddInspectItem}
                        onBeforeSelect={handleItemAddValidInspectItem}
                        mode={ViewMode.button}
                        clearButton={false}
                        placeholder={intl
                          .get(`${modelPrompt}.button.addInspectItem`)
                          .d('新增检验项')}
                        modalProps={{
                          footer: (okBtn, cancelBtn, modal) => {
                            return [
                              <Button
                                onClick={() => {
                                  handleCreateInspection(modal, handleCreateInspectionCallback, {
                                    validateType: 'InspectionPlatformCreateItem',
                                  });
                                }}
                              >
                                {intl
                                  .get(`${modelPrompt}.button.createInspectItem`)
                                  .d('新建检验项目')}
                              </Button>,
                              cancelBtn,
                              okBtn,
                            ];
                          },
                        }}
                      />
                    </PermissionButton>
                  </Menu.Item>
                  <Menu.Item key="DELETE">
                    <PermissionButton
                      type="c7n-pro"
                      funcType="flat"
                      disabled={
                        !canEdit ||
                        !(inspectInfoDS.getState('modeType') === ModeType.col
                          ? findKey(
                            inspectItemColDS.getState('cacheItemData') || {},
                            item => !item.inspectDocLineId,
                          )
                          : (itemRowSelectList || []).find(
                            (record: Record) => record.status === 'add',
                          ))
                      }
                      onClick={handleItemDeleteInspectLine}
                      permissionList={[
                        {
                          code: `inspectionPlatform.dist.button.deleteItemInspectLine`,
                          type: 'button',
                          meaning: '详情页-删除检验项',
                        },
                      ]}
                    >
                      {intl.get(`${modelPrompt}.button.deleteItemInspectLine`).d('删除检验项')}
                    </PermissionButton>
                  </Menu.Item>
                  <Menu.Item key="COPY">
                    <PermissionButton
                      type="c7n-pro"
                      funcType="flat"
                      disabled={!canEdit || inspectInfoDS.current?.get('docOneTaskFlag') !== 'Y'}
                      loading={copyInspectObjLoading}
                      onClick={handleItemCopyInspectObj}
                      permissionList={[
                        {
                          code: `inspectionPlatform.dist.button.copy`,
                          type: 'button',
                          meaning: '详情页-复制检验值',
                        },
                      ]}
                    >
                      {intl.get(`${modelPrompt}.button.copyItemInspectObj`).d('复制检验值')}
                    </PermissionButton>
                  </Menu.Item>
                  <Menu.Item key="RESET">
                    <PermissionButton
                      type="c7n-pro"
                      funcType="flat"
                      disabled={
                        !canEdit || (itemColSelectList.length < 1 && itemRowSelectList.length < 1)
                      }
                      onClick={handleItemResetValue}
                      permissionList={[
                        {
                          code: `inspectionPlatform.dist.button.reset`,
                          type: 'button',
                          meaning: '详情页-重置录入值',
                        },
                      ]}
                    >
                      {intl.get(`${modelPrompt}.button.resetValue`).d('重置录入值')}
                    </PermissionButton>
                  </Menu.Item>
                  {inspectInfoDS.getState('modeType') === ModeType.col && (
                    <Menu.Item key="DELETECOL">
                      <PermissionButton
                        type="c7n-pro"
                        funcType="flat"
                        disabled={!canEdit || itemColSelectList.length < 1}
                        onClick={handleItemDeleteInspectObj}
                        permissionList={[
                          {
                            code: `inspectionPlatform.dist.button.deleteItemInspectObject`,
                            type: 'button',
                            meaning: '详情页-删除检验对象',
                          },
                        ]}
                      >
                        {intl.get(`${modelPrompt}.button.deleteItemInspectObj`).d('删除检验对象')}
                      </PermissionButton>
                    </Menu.Item>
                  )}
                </Menu>
              }
              trigger={[Action.click]}
            >
              <PermissionButton
                type="c7n-pro"
                permissionList={[
                  {
                    code: `inspectionPlatform.dist.button.operate`,
                    type: 'button',
                    meaning: '详情页-检验操作',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.inspectOperate`).d('检验操作')}
              </PermissionButton>
            </Dropdown>

            <NcRecordComponent {...ncRecordProps} />

            <Dropdown
              overlay={
                <Menu
                  onClick={clickDisposalApprovingMenu}
                  className={styles['split-menu']}
                  style={{ minWidth: '100px' }}
                >
                  <Menu.Item key="APPROVING">
                    <a>{intl.get(`${modelPrompt}.button.NGExamineApprove`).d('发起不良审批')}</a>
                  </Menu.Item>
                  <Menu.Item key="DISPOSAL">
                    <a>{intl.get(`${modelPrompt}.button.disposal`).d('直接处置')}</a>
                  </Menu.Item>
                </Menu>
              }
              trigger={[Action.click]}
            >
              <PermissionButton
                type="c7n-pro"
                disabled={
                  !showButtonFlag ||
                  ['REVIEWED'].includes(inspectInfoDS.current?.get('reviewStatus'))
                }
                permissionList={[
                  {
                    code: `inspectionPlatform.dist.button.disposalApproving`,
                    type: 'button',
                    meaning: '详情页-处置审核按钮',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.disposalApproving`).d('处置审核按钮')}
              </PermissionButton>
            </Dropdown>
          </>
        )}

        {disposalStatus && !editingBtn && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              loading={ncReviewSubmitLoading}
              onClick={handleNcReviewSubmit}
            >
              {intl.get(`${modelPrompt}.button.justSubmit`).d('提交')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              loading={ncReviewSubmitLoading}
              onClick={handleNcReviewSubmitCancel}
            >
              {intl.get(`tarzan.common.button.cancel`).d('取消')}
            </PermissionButton>
          </>
        )}
        {['INSPECT_INFO'].includes(tabKey) && !editingBtn && inspectInfoDS.current?.get('inspectTaskStatus') === 'COMPLETED' &&  (
          <Button
            disabled={fixBtnAuth()}
            onClick={() => {
              setEditingBtn(true);
              setCanEdit(true);
              inspectInfoDS.setState('canEdit', true);
            }}
          >
            {intl.get(`${modelPrompt}.editing`).d('修改')}
          </Button>
        )}
        {['INSPECT_INFO'].includes(tabKey) && editingBtn && inspectInfoDS.current?.get('inspectTaskStatus') === 'COMPLETED' &&  (
          <>
            <Button
              onClick={() => {
                initPageData();
                setEditingBtn(false);
                setCanEdit(false);
                inspectInfoDS.setState('canEdit', false);
              }}
            >
              {intl.get(`${modelPrompt}.cancel`).d('取消')}
            </Button>
            <Button onClick={handleEditingSave} loading={editingSaveLoading}>
              {intl.get(`${modelPrompt}.editingSave`).d('修改保存')}
            </Button>
          </>
        )}
      </Header>
      <Content className={styles['inspection-platform-detail']}>
        <Spin
          spinning={
            fetchDocInfoLoading ||
            addInspectObjLoading ||
            fetchInspectInfoLoading ||
            startInspectInfoLoading ||
            submitInspectInfoLoading ||
            saveInspectInfoLoading ||
            scanBarcodeLoading ||
            scanInspectObjLoading ||
            copyInspectObjLoading ||
            fetchSamplingQtyLoading ||
            getCalculateFormulaLoading ||
            ncReviewLoading ||
            ncReviewSubmitLoading
          }
        >
          <Tabs
            activeKey={tabKey}
            onChange={handleChangeActiveKey}
            tabBarExtraContent={<TabInfoBox activeKey={tabKey} inspectInfoDS={inspectInfoDS} />}
          >
            <TabPane tab={intl.get(`${modelPrompt}.tab.docInfo`).d('单据信息')} key="DOC_INFO">
              <DocInfoComponent {...docInfoProps} />
            </TabPane>
            <TabPane
              tab={intl.get(`${modelPrompt}.tab.inspectInfo`).d('检验项目信息')}
              key="INSPECT_INFO"
            >
              <InspectInfoComponent {...inspectInfoProps} />
            </TabPane>
            <TabPane
              tab={intl.get(`${modelPrompt}.tab.inspectObjectDetail`).d('检验对象明细')}
              key="INSPECT_OBJ"
            >
              <InspectObjComponent {...inspectObjProps} />
            </TabPane>
          </Tabs>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.qms.inspectionPlatform', 'tarzan.hwms.inspectItemMaintain', 'tarzan.common'],
})(
  withProps(
    () => {
      // 单据信息
      const docInfoDS = new DataSet({
        ...DocInfoDS(),
      });
      // 检验对象明细
      const inspectObjDS = new DataSet({
        ...InspectObjDS(),
      });
      // 检验任务信息
      const inspectInfoDS = new DataSet({
        ...InspectInfoDS(),
      });
      // 检验项行模式
      const inspectItemRowObjValueDS = new DataSet({
        ...InspectItemRowObjValueDS(),
      });
      const inspectItemRowDS = new DataSet({
        ...InspectItemRowDS(),
        children: {
          taskLineObjects: inspectItemRowObjValueDS,
        },
      });
      // 用于添加数据
      const addInspectItemObjValueDS = new DataSet({
        ...InspectItemRowObjValueDS(),
      });
      // 检验项列模式
      const inspectItemColDS = new DataSet({
        ...InspectItemColDS(),
      });
      // 检验项列模式删除的检验项
      const inspectDeleteItemColDS = new DataSet({
        ...InspectItemRowDS(),
      });
      return {
        docInfoDS,
        inspectObjDS,
        inspectInfoDS,
        inspectItemRowDS,
        inspectItemRowObjValueDS,
        addInspectItemObjValueDS,
        inspectItemColDS,
        inspectDeleteItemColDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_DOC.BASIC`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_DOC.ACT`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_OBJECT.LIST`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.HEAD`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.QLINE`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.BLINE`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.VALUE`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.NCRECORD`,
      ],
      // @ts-ignore
    })(InspectionPlatformDetail),
  ),
);
