/**
 * @Description: 库存查询-mes - 列表页
 * @Author: <EMAIL>
 * @Date: 2022/7/6 15:40
 * @LastEditTime: 2022-11-21 13:37:41
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Table, Modal, Button } from 'choerodon-ui/pro';
import { isNil } from 'lodash';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import { ColumnLock, TableQueryBarType} from 'choerodon-ui/pro/lib/table/enum';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC } from '@/utils/config';
import { useDataSetEvent } from 'utils/hooks';
import {getCurrentOrganizationId} from "utils/utils";
import ExcelExport from 'components/ExcelExport';
import { getCurrentSiteInfo } from "@utils/utils";
import { entranceDS } from '../stores/EntranceDS';
import { drawerHeadDS, drawerTableDS } from '../stores/ReserveDrawerDS';
import ReserveDrawer from './ReserveDrawer';
import './index.modules.less';

const modelPrompt = 'tarzan.inventory.query.mes.model.query';
const tenantId = getCurrentOrganizationId();


const Query = props => {
  const {
    dataSet,
  } = props;
  let _modal;

  const [dynamicColumns, setDynamicColumns] = useState<ColumnProps[]>([
    { name: 'wareHouseCode', lock: ColumnLock.left, width: 160 },
    { name: 'locatorCode', lock: ColumnLock.left, width: 160 },
    { name: 'wareHouseName', width: 160 },
    { name: 'locatorName', width: 160 },
    { name: 'materialCode', width: 150 },
    { name: 'materialDesc', width: 220 },
    { name: 'revisionCode', width: 120 },
    { name: 'lotCode', width: 90 },
    { name: 'qualityStatusDesc', width: 120 },
    {
      name: 'materialBomCode',
    },
    {
      name: 'materialBrand',
    },
    {
      name: 'materialModel',
    },
    {
      name: 'productionDate',
    },
    {
      name: 'grade',
    },
    {
      name: 'property',
    },
    {
      name: 'onhandQty',
      renderer: ({ value, record }) => (
        <a onClick={() => jumpToMaterialLotTraceability(record)}>{value}</a>
      ),
    },
    { name: 'availableQty' },
    {
      name: 'holdQty',
      renderer: ({ value, record }) =>
        value === 0 || record?.get('childFlag') === 'Y' ? (
          value
        ) : (
          <a onClick={() => showReserveStockDetails(record)}>{value}</a>
        ),
    },
    { name: 'uomCode' },
    {
      name: 'ownerTypeDesc',
      renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
    },
    {
      name: 'ownerCode',
      renderer: ({ value, record }) => {
        if (record?.get('ownerTypeDesc')) {
          return value || record?.get('ownerId');
        }
      },
    },
  ]);

  const drawerHeadDs = useMemo(() => new DataSet(drawerHeadDS()), []);
  const drawerTableDs = useMemo(() => new DataSet(drawerTableDS()), []);

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.query).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      // dataSet.query(dataSet.currentPage);
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const {
      siteId,
      siteCode,
      materialId,
      materialCode,
      revisionCode,
      lotCode,
      qualityStatus,
      ownerType,
      ownerId,
      ownerCode,
      locatorId,
      locatorCode,
    } = props?.location?.query || {};
    const queryParams = {
      siteLov: siteId ? { siteId, siteCode } : undefined, // 回显站点
      materialId: materialId && materialId.length ? [materialId][0] : undefined,
      materialIds: materialId && materialId.length ? [materialId][0] : undefined,
      materialCode: materialCode && materialCode.length ? [materialCode][0] : undefined,
      materialLov: materialId ? { materialId, materialCode } : undefined,
      revisionCodes: revisionCode && revisionCode.length ? [revisionCode] : undefined,
      locatorId: locatorId && locatorId.length ? [locatorId][0] : undefined,
      locatorIds: locatorId && locatorId.length ? [locatorId][0] : undefined,
      locatorCode: locatorCode && locatorCode.length ? [locatorCode][0] : undefined,
      lotCodes: lotCode ? [lotCode] : undefined,
      qualityStatus,
      ownerType,
      ownerLov: ownerId
        ? { soLineId: ownerId,
          customerId: ownerId,
          soNumContent: ownerCode,
          supplierCode: ownerCode,
          customerCode: ownerCode,
        } : undefined,
      locatorLov: locatorId ? { locatorId, locatorCode } : null,
    };
    setTimeout(() => {
      dataSet.queryDataSet.loadData([queryParams]);
      dataSet.query();
    }, 200);
  }, [props?.location?.query, props?.location?.state]);

  // 根据汇总维度，来设置表格列的显示
  useDataSetEvent(dataSet, 'beforeLoad', async () => {
    if (!dataSet.queryDataSet || !dataSet.queryDataSet.current) {
      return {};
    }
    const queryParams = dataSet.queryDataSet?.current?.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    const dynamicColumn: ColumnProps[] = [
      { name: 'wareHouseCode', lock: ColumnLock.left, width: 160 },
      queryParams.visibility.includes('locatorFlag') && { name: 'locatorCode', lock: ColumnLock.left, width: 160 },
      { name: 'wareHouseName', width: 160 },
      queryParams.visibility.includes('locatorFlag') && { name: 'locatorName', width: 160 },
      { name: 'materialCode', width: 150 },
      { name: 'materialDesc', width: 220 },
      queryParams.visibility.includes('revisionCodeFlag') && { name: 'revisionCode', width: 120 },
      queryParams.visibility.includes('lotCodeFlag') && { name: 'lotCode', width: 90 },
      queryParams.visibility.includes('statusFlag') && { name: 'qualityStatusDesc', width: 120 },
      {
        name: 'materialBomCode',
      },
      {
        name: 'materialBrand',
      },
      {
        name: 'materialModel',
      },
      {
        name: 'productionDate',
      },
      {
        name: 'grade',
      },
      {
        name: 'property',
      },
      {
        name: 'onhandQty',
        renderer: ({ value, record }) => (
          <a onClick={() => jumpToMaterialLotTraceability(record)}>{value}</a>
        ),
      },
      { name: 'availableQty' },
      {
        name: 'holdQty',
        renderer: ({ value, record }) =>
          value === 0 || record?.get('childFlag') === 'Y' ? (
            value
          ) : (
            <a onClick={() => showReserveStockDetails(record)}>{value}</a>
          ),
      },
      { name: 'uomCode' },
      queryParams.visibility.includes('ownerTypeFlag') && {
        name: 'ownerTypeDesc',
        renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
      },
      queryParams.visibility.includes('ownerCodeFlag') && {
        name: 'ownerCode',
        renderer: ({ value, record }) => {
          if (record?.get('ownerTypeDesc')) {
            return value || record?.get('ownerId');
          }
        },
      },
    ];
    setDynamicColumns(dynamicColumn);
  });

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (dataSet) {
      const handlerQuery = flag
        ? dataSet.queryDataSet.addEventListener
        : dataSet.queryDataSet.removeEventListener;
      // 查询条件更新时操作
      handlerQuery.call(dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = async ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', null);
    }
    if (name === 'siteLov') {
      record.set('locatorLov', null);
      record.set('materialLov', null);
    }
    if (name === 'visibility') {
      const visibility = record.get('visibility');
      const newQueryFields: any = [
        {
          name: 'siteLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
          lovCode: 'MT.MODEL.SITE',
          lovPara: { tenantId },
          ignore: FieldIgnore.always,
          required: true,
          dynamicProps: {
            defaultValue: () => {
              const  siteInfo = getCurrentSiteInfo();
              if (siteInfo.siteId) {
                return { ...siteInfo }
              }
              return undefined;
            },
          },
        },
        {
          name: 'siteId',
          bind: 'siteLov.siteId',
        },
        {
          name: 'areaLocatorLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库编码'),
          lovCode: 'MT.MODEL.LOCATOR',
          ignore: FieldIgnore.always,
          multiple: true,
          dynamicProps: {
            lovPara: ({ record }) => {
              return {
                tenantId,
                siteId: record.get('siteId'),
                locatorCategories: 'AREA',
              };
            },
            disabled: ({ record }) => {
              return !record.get('siteId');
            },
          },
        },
        {
          name: 'wareHouseIds',
          bind: 'areaLocatorLov.locatorId',
        },
        {
          name: 'wareHouseCodes',
          bind: 'areaLocatorLov.locatorCode',
        },
        visibility.includes('locatorFlag') && {
          name: 'locatorLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.locatorLovCode`).d('货位编码'),
          lovCode: 'MT.MODEL.SUB_LOCATOR',
          multiple: true,
          ignore: FieldIgnore.always,
          dynamicProps: {
            lovPara: ({ record }) => {
              return {
                tenantId,
                siteId: record.get('siteId'),
                locatorCategories: ["INVENTORY", "LOCATION"],
                locatorIds: [record.get('wareHouseIds')],
              };
            },
            disabled: ({ record }) => {
              return !record.get('siteId');
            },
          },
        },
        visibility.includes('locatorFlag') && {
          name: 'locatorIds',
          bind: 'locatorLov.locatorId',
        },
        visibility.includes('locatorFlag') && {
          name: 'locatorCode',
          bind: 'locatorLov.locatorCode',
        },
        {
          name: 'materialLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.materialId`).d('物料'),
          lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
          multiple: true,
          ignore: FieldIgnore.always,
          dynamicProps: {
            lovPara: ({ record }) => {
              return {
                tenantId,
                siteId: record.get('siteId'),
                enableFlag: 'Y',
              };
            },
            disabled: ({ record }) => {
              return !record.get('siteId');
            },
          },
        },
        {
          name: 'materialIds',
          bind: 'materialLov.materialId',
        },
        {
          name: 'materialCode',
          bind: 'materialLov.materialCode',
        },
        visibility.includes('revisionCodeFlag') && {
          name: 'revisionCodes',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
          multiple: true,
        },
        visibility.includes('lotCodeFlag') && {
          name: 'lotCodes',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.lotCode`).d('批次'),
          multiple: true,
        },
        visibility.includes('statusFlag') && {
          name: 'qualityStatus',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
          textField: 'description',
          valueField: 'statusCode',
          lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
          lookupAxiosConfig: {
            transformResponse(data) {
              // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
              if (data instanceof Array) {
                return data;
              }
              const { rows } = JSON.parse(data);
              return rows;
            },
          },
        },
        {
          name: 'holdFlag',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.holdFlag`).d('预留库存'),
          options: new DataSet({
            data: [
              { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
              { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
            ],
          }),
          textField: 'key',
          valueField: 'value',
        },
        visibility.includes('ownerTypeFlag') && {
          name: 'ownerType',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
          textField: 'description',
          valueField: 'typeCode',
          options: new DataSet({
            autoQuery: true,
            dataKey: 'rows',
            paging: false,
            transport: {
              read: () => {
                return {
                  url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
                  method: 'GET',
                  params: { tenantId },
                  transformResponse: val => {
                    const data = JSON.parse(val);
                    data.rows.push({
                      description: intl.get(`tarzan.common.ownerType`).d('自有'),
                      typeCode: 'ALL',
                      typeGroup: 'OWNER_TYPE',
                    });
                    return {
                      ...data,
                    };
                  },
                };
              },
            },
          }),
        },
        visibility.includes('ownerCodeFlag') && {
          name: 'ownerLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
          lovCode: 'MT.MODEL.CUSTOMER',
          lovPara: {
            tenantId,
          },
          ignore: FieldIgnore.always,
          dynamicProps: {
            lovCode({ record }) {
              switch (record.get('ownerType')) {
                case 'CI':
                case 'IIC':
                  return 'MT.MODEL.CUSTOMER';
                case 'SI':
                case 'IIS':
                case 'OD':
                  return 'MT.MODEL.SUPPLIER';
                case 'OI':
                  return 'MT.MES.SO_LINE';
                default:
                  return 'MT.MES.EMPTY';
              }
            },
            textField({ record }) {
              switch (record.get('ownerType')) {
                case 'CI':
                case 'IIC':
                  return 'customerCode';
                case 'SI':
                case 'IIS':
                case 'OD':
                  return 'supplierCode';
                case 'OI':
                  return 'soNumContent';
                default:
                  return 'noData';
              }
            },
            disabled({ record }) {
              return !['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
            },
          },
        },
        visibility.includes('ownerCodeFlag') && {
          name: 'ownerId',
          type: FieldType.number,
          bind: 'ownerLov.customerId',
          dynamicProps: {
            bind({ record }) {
              switch (record.get('ownerType')) {
                case 'CI':
                case 'IIC':
                  return 'ownerLov.customerId';
                case 'SI':
                case 'IIS':
                case 'OD':
                  return 'ownerLov.supplierId';
                case 'OI':
                  return 'ownerLov.soLineId';
                default:
                  return 'ownerLov.customerId';
              }
            },
          },
        },
        {
          name: 'visibility',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.visibility`).d('汇总维度'),
          options: new DataSet({
            data: [
              {
                typeCode: 'locatorFlag',
                description: intl.get(`${modelPrompt}.locatorLovCode`).d('货位编码'),
              },
              {
                typeCode: 'revisionCodeFlag',
                description: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
              },
              {
                typeCode: 'lotCodeFlag',
                description: intl.get(`${modelPrompt}.lotCode`).d('批次'),
              },
              {
                typeCode: 'statusFlag',
                description: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
              },
              {
                typeCode: 'ownerCodeFlag',
                description: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
              },
              {
                typeCode: 'ownerTypeFlag',
                description: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
              },
            ],
            selection: DataSetSelection.multiple,
            autoQuery: true,
          }),
          defaultValue: [
            'locatorFlag',
            'revisionCodeFlag',
            'lotCodeFlag',
            'statusFlag',
            'ownerCodeFlag',
            'ownerTypeFlag',
          ],
          textField: 'description',
          valueField: 'typeCode',
          multiple: true,
        },
      ];
      dataSet.queryDataSet = await new DataSet({
        fields: [...newQueryFields],
      });
      dataSet.queryDataSet.getField('visibility').set('defaultValue', visibility);
      const isValidates = await dataSet.validate(true);
      if (isValidates) {
        dataSet.query();
        const handlerQuery = dataSet.queryDataSet.addEventListener;
        handlerQuery.call(dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
      }
    }
  };

  // 跳转物料批追溯功能
  const jumpToMaterialLotTraceability = record => {
    const { locatorCategory, locatorCategoryDesc, locatorCode, locatorId, locatorName, ...others } = record.toData();
    const queryParameter = {
      ...others,
      locatorInfo: {
        locatorCategory,
        locatorCategoryDesc,
        locatorCode,
        locatorId,
        locatorName,
      },
    };
    props.history.push({
      key: new Date().getTime(),
      pathname: `/apex-hmes/product/material-lot-traceability/list/${new Date().getTime()}`,
      state: queryParameter,
    });
  };

  // 查看预留库存详情抽屉
  const showReserveStockDetails = record => {
    drawerHeadDs.loadData([record.toData()]);
    drawerTableDs.queryDataSet?.loadData([{ ...record.toData() }]);
    drawerTableDs.query();
    _modal = Modal.open({
      title: intl.get('tarzan.inventory.query.mes.title.details').d('预留详细信息'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      maskClosable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: <ReserveDrawer headDs={drawerHeadDs} tableDs={drawerTableDs} />,
      footer: (
        <Button onClick={() => _modal.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  const onFieldEnterDown = () => {
    dataSet.query(props.dataSet.currentPage);
  }

  // 处理导出按钮使用的查询参数
  const getExportQueryParams = () => {
    if (!dataSet.queryDataSet || !dataSet.queryDataSet.current) {
      return {};
    }
    const queryParams = dataSet.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return {
      ...queryParams,
      locatorFlag: queryParams.visibility.includes('locatorFlag'),
      revisionCodeFlag: queryParams.visibility.includes('revisionCodeFlag'),
      statusFlag: queryParams.visibility.includes('statusFlag'),
      ownerCodeFlag: queryParams.visibility.includes('ownerCodeFlag'),
      ownerTypeFlag: queryParams.visibility.includes('ownerTypeFlag'),
      lotCodeFlag: queryParams.visibility.includes('lotCodeFlag'),
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.inventory.query.mes.view.title.query').d('库存查询-MES')}>
        <ExcelExport
          exportAsync
          method="GET"
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inv-onhand-quantity/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="inventoryQueryMes"
          customizedCode="inventoryQueryMes"
          queryFieldsLimit={11}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
          dataSet={dataSet}
          columns={[...dynamicColumns]}
          highLightRow
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inventory.query.mes', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...entranceDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Query),
);
