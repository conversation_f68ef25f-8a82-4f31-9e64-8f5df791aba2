import React, { useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';

export default ({ ds }) => {
  const columns: ColumnProps[] = useMemo(
    () => [
      { name: 'instructionDocNum' },
      { name: 'instructionDocTypeDesc' },
      { name: 'lineNumber' },
      { name: 'businessTypeDesc' },
      { name: 'instructionStatusDesc' },
      { name: 'poNumber' },
      { name: 'materialCode' },
      { name: 'revisionCode' },
      { name: 'uomCode' },
      { name: 'trxActualQty' },
      { name: 'fromSiteCode' },
      { name: 'toSiteCode' },
      { name: 'fromWarehouseCode' },
      { name: 'fromLocatorCode' },
      { name: 'toWarehouseCode' },
      { name: 'toLocatorCode' },
      { name: 'fromOwnerTypeDesc' },
      { name: 'fromOwnerCode' },
      { name: 'toOwnerTypeDesc' },
      { name: 'toOwnerCode' },
    ],
    [],
  );

  return (
    <>
      <Table
        customizedCode="kcrjzwlpmx1"
        dataSet={ds}
        queryBar={TableQueryBarType.none}
        columns={columns}
        virtual
        virtualCell
      />
    </>
  );
};
