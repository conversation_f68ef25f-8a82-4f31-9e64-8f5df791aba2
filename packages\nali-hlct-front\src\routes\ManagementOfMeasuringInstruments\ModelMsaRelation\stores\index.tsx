/**
 * @Description: 量具型号与MSA分析质量特性关系维护-主界面DS
 * @Author: <EMAIL>
 * @Date: 2023/8/16 14:30
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
// import notification from 'utils/notification';

const modelPrompt = 'tarzan.modelManagement.modelMsaRelation';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'modelMsaRelId',
  queryFields: [
    {
      name: 'modelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.modelName`).d('量具型号'),
      lovCode: 'YP.QMS_TOOL_MODEL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'modelId',
      bind: 'modelLov.toolModelId',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      lookupCode: 'YP.QIS.Y_N',
      lovPara: { tenantId },
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'modelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.modelCode`).d('量具型号编码'),
      lovCode: 'YP.QMS_TOOL_MODEL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record.get('siteId'),
        }),
        disabled: ({ record }) => !record.get('siteId'),
      },
    },
    {
      name: 'modelId',
      bind: 'modelLov.toolModelId',
    },
    {
      name: 'modelCode',
      bind: 'modelLov.modelCode',
    },
    {
      name: 'modelName',
      label: intl.get(`${modelPrompt}.modelName`).d('量具型号名称'),
      bind: 'modelLov.modelName',
      required: true,
      disabled: true,
    },
    {
      name: 'speciesCode',
      bind: 'modelLov.speciesCode',
    },
    {
      name: 'speciesName',
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
      bind: 'modelLov.speciesName',
      required: true,
      disabled: true,
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
      required: true,
    },
    {
      name: 'msaAnalysisMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaAnalysisMethod`).d('MSA分析方法'),
      lookupCode: 'YP.QIS.MSA_ANALYSIS_METHOD',
      lovPara: { tenantId },
      required: true,
      multiple: ',',
    },
    {
      name: 'completeTimeLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.completeTimeLimit`).d('完成时间限制（天）'),
      required: true,
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      required: true,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'fileUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.fileUuid`).d('测量方法附件'),
      bucketName: 'qms',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-model-msa-rel/relQuery`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
