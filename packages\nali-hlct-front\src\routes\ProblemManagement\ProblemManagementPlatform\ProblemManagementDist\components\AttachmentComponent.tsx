/**
 * @Description: 问题管理平台-SRM系统附件查看
 * @Author: <EMAIL>
 * @Date: 2023/7/27 18:58
 */
import React, { useMemo, useState } from 'react';
import intl from 'utils/intl';
import { Attachment, Button, Dropdown, Menu } from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { useRequest } from '@components/tarzan-hooks';
import { QuerySrmEvidence } from '../../services';
import styles from '../index.module.less';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';

export default observer(({ srmFlag, record, name }) => {
  const [attachmentList, setAttachmentList] = useState([]);
  const { run: querySrmEvidence, loading: queryEvidenceLoading } = useRequest(QuerySrmEvidence(), {
    manual: true,
    needPromise: true,
  });

  const attachmentProps: any = {
    name,
    bucketName: 'qms',
    bucketDirectory: 'problem-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    readOnly: true,
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const handleQueryAttachment = async hidden => {
    if (hidden || !record?.get(name)) {
      return;
    }
    const res = await querySrmEvidence({
      params: {
        // attachmentUUID: '42e765cd1d65774933a002328468260993',
        attachmentUUID: record?.get(name),
      },
    });
    setAttachmentList(res);
  };

  const menu = useMemo(() => {
    return (
      <Menu className={styles['split-menu']} style={{ minWidth: '3rem' }}>
        {attachmentList?.length > 0 &&
          attachmentList.map((item: any, index) => (
            <Menu.Item key={String(index)}>
              <a
                className={styles['attachment-menu']}
                target="_blank"
                rel="noopener noreferrer"
                href={item.fileKey}
              >
                <span className={styles['attachment-file-name']}>{item.fileName}</span>
                <Button icon="get_app" color={ButtonColor.primary} funcType={FuncType.link} />
              </a>
            </Menu.Item>
          ))}
        {!attachmentList?.length && (
          <Menu.Item>
            <div>{intl.get(`${modelPrompt}.attachment.empty`).d('暂无附件')}</div>
          </Menu.Item>
        )}
      </Menu>
    );
  }, [attachmentList.length]);

  if (srmFlag) {
    return (
      <Dropdown overlay={menu} onHiddenChange={handleQueryAttachment}>
        <Button icon="attach_file" funcType={FuncType.flat} loading={queryEvidenceLoading}>
          {intl.get(`${modelPrompt}.attachment`).d('查看附件')}
        </Button>
      </Dropdown>
    );
  }
  return <Attachment {...attachmentProps} record={record} />;
});
