import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, parseParameters } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const prefix = `${BASIC.HMES_BASIC}`;
// const prefix = '/rt-mes-33083';

export async function fetchList(params) {
  const newParams = parseParameters(params);
  return request(`${prefix}/v1/${tenantId}/hme-work-cell-details-report/exception-report-list`, {
    method: 'GET',
    query: newParams,
  });
}

export async function searchList(params) {
  const newParams = parseParameters(params);
  return request(
    `${prefix}/v1/${tenantId}/hme-exception-handle-platform/query/exception/history/ui`,
    {
      method: 'GET',
      query: newParams,
    },
  );
}
