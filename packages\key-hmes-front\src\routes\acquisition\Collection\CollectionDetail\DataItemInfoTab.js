/**
 * @Description: 数据项信息-Tab页
 * @Author: <<EMAIL>>
 * @Date: 2021-01-25 10:23:52
 * @LastEditTime: 2023-05-23 15:21:32
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo } from 'react';
import { Table, Button, Modal, DataSet } from 'choerodon-ui/pro';
import { Badge, Spin, Tag, Popconfirm } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import myInstance from '@utils/myAxios';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC, API_HOST } from '@utils/config';
import { isUndefined, isNumber } from 'lodash';
import { SortColumnC7n } from './TableSortColumnC7n';
import DataItemInfoDrawer from './DataItemInfoDrawer';

import {
  // detailDS,
  numberListDS,
} from '../stores/CollectionDS';
// import { baseInfoDS } from '../../NewDataItem/stories/BaseInfoDs';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

const loadDataKeys = [
  'tagId',
  'tagCode',
  'tagDescription',
  'remark',
  'enableFlag',
  'displayValueFlag',
  'valueType',
  'valueTypeDesc',
  'collectionMethod',
  'collectionMethodDesc',
  'trueValue',
  'falseValue',
  // 'minimumValue',
  // 'maximalValue',
  'unit',
  'valueList',
  'valueAllowMissing',
  // 'mandatoryNum',
  // 'optionalNum',
  'apiId',
  'uomCode',
  'uomId',
  'uomDesc',
  'apiName',
  'allowUpdateFlag',
  'dateFormat',
  'specialRecordFlag',
  'trueValueList',
  'falseValueList',
  'defaultNcCodeId',
  'defaultNcCode',
];

const DataItemInfoTab = props => {
  const { canEdit, dataItemTableDs, tableHeight} = props;
  // const { canEdit, dataItemTableDs, tableHeight, handleValidate} = props;
  const [synchronousLoading, setSynchronousLoading] = useState(false);
  const trueNumberDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [],
  );
  const falseNumberDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [],
  );
  let _dataItemDrawer;

  const handleChangeLov = (data1, data2) => {
    trueNumberDs.loadData(data1)
    falseNumberDs.loadData(data2)
  }

  const dataItemDrawerClose = record => {
    if (!record) {
      dataItemTableDs.remove(dataItemTableDs.current);
    } else if (!record.get('tagGroupAssignId')) {
      dataItemTableDs.remove(record);
    } else {
      // record.reset();
      record.restore();
    }
    _dataItemDrawer.close();
  };

  const handleIntersection = () => {
    let flag = false
    const trueData = trueNumberDs.toData()
    const falseData = falseNumberDs.toData()
    for(let i=0;i<falseData.length;i++){
      const itemF = falseData[i]
      if(itemF.valueType === 'single'){
        if(trueData.some(x =>x.dataValue&&x.valueType==='single'&& Number(x.dataValue)=== Number(itemF.dataValue))){
          flag = true;
          break;
        }
        const arr = trueData.filter(m => m.valueType === 'section')
        if(arr.length){
          const leftTemp = arr.map(m =>isUndefined(m.multipleValue.leftValue)?undefined:Number(m.multipleValue.leftValue))
          const rightTemp = arr.map(m => isUndefined(m.multipleValue.rightValue)?undefined:Number(m.multipleValue.rightValue))
          const leftMin = leftTemp.reduce((x, y) => x < y ? x : y)
          const rightMax = rightTemp.reduce((x, y) => x > y ? x : y)
          // 不存在负无穷
          if(leftTemp.every(m => isNumber(m))&&rightTemp.every(m => isNumber(m))){
            if(itemF.dataValue>leftMin&&itemF.dataValue<rightMax){
              flag = true;
              break;
            }
          }
          // 负无穷
          if(leftTemp.some(m => isUndefined(m))){
            if(itemF.dataValue<rightMax){
              flag = true;
              break;
            }
          }
          // 正无穷
          if(rightTemp.some(m => isUndefined(m))){
            if(itemF.dataValue>leftMin){
              flag = true;
              break;
            }
          }
        }
      }else if(itemF.valueType === 'section'){
        const leftValue = isUndefined(itemF.multipleValue.leftValue)?undefined:Number(itemF.multipleValue.leftValue)
        const rightValue = isUndefined(itemF.multipleValue.rightValue)?undefined:Number(itemF.multipleValue.rightValue)
        const arrSingle = trueData.filter(m => m.valueType === 'single'&&m.dataValue)
        const arrSection = trueData.filter(m => m.valueType === 'section')
        // 符合值中的左侧值与右侧值
        if(arrSection.length){
          const leftTemp = arrSection.map(m => isUndefined(m.multipleValue.leftValue)?undefined:Number(m.multipleValue.leftValue))
          const rightTemp = arrSection.map(m => isUndefined(m.multipleValue.rightValue)?undefined:Number(m.multipleValue.rightValue))
          const leftMin = leftTemp.reduce((x, y) => x < y ? x : y)
          const rightMax = rightTemp.reduce((x, y) => x > y ? x : y)
          if(rightValue&&leftValue){
            if(arrSingle.length){
              if(arrSingle.some(x =>x.dataValue&& x.dataValue>leftValue&&x.dataValue<rightValue)){
                flag = true;
                break;
              }
            }
            if(leftTemp.every(m => isNumber(m))&&rightTemp.every(m => isNumber(m))){
              if(leftValue>leftMin&&leftValue<rightMax||rightValue>leftMin&&rightValue<rightMax||(leftValue<leftMin)&&(rightValue>rightMax)){
                flag = true;
                break;
              }
            }
            // 负无穷
            if(leftTemp.some(m => isUndefined(m))){
              if(leftValue<rightMax){
                flag = true;
                break;
              }
            }
            // 正无穷
            if(rightTemp.some(m => isUndefined(m))){
              if(rightValue>leftMin){
                flag = true;
                break;
              }
            }
          }else if(isUndefined(rightValue)||isUndefined(leftValue)){
            if(arrSingle.length){
              if(arrSingle.some(x =>x.dataValue&&Number(x.dataValue)<rightValue)||arrSingle.some(x =>x.dataValue&&Number(x.dataValue)>leftValue)){
                flag = true;
                break;
              }
            }
            if(leftTemp.every(m => isNumber(m))&&rightTemp.every(m => isNumber(m))){
              if(isUndefined(rightValue)&&leftValue<rightMax){
                flag = true;
                break;
              }
              if(isUndefined(leftValue)&&rightValue>leftMin){
                flag = true;
                break;
              }
            }
            // 负无穷
            if(leftTemp.some(m => isUndefined(m))){
              if(isUndefined(rightValue)&&leftValue<rightMax){
                flag = true;
                break;
              }
              if(isUndefined(leftValue)){
                flag = true;
                break;
              }
            }
            // 正无穷
            if(rightTemp.some(m => isUndefined(m))){
              if(isUndefined(rightValue)){
                flag = true;
                break;
              }
              if(isUndefined(leftValue)&&rightValue>leftMin){
                flag = true;
                break;
              }
            }
          }
        }else if(arrSingle.length){
          if(rightValue&&leftValue){
            if(arrSingle.some(x =>x.dataValue&& Number(x.dataValue)>leftValue&&Number(x.dataValue)<rightValue)){
              flag = true;
              break;
            }
          }else if(isUndefined(rightValue)||isUndefined(leftValue)){
            if(arrSingle.length){
              if(arrSingle.some(x =>x.dataValue&&Number(x.dataValue)>leftValue)||arrSingle.some(x =>x.dataValue&&Number(x.dataValue)<rightValue)){
                flag = true;
                break;
              }
            }
          }
        }
      }
    }
    return flag;
  }


  const dataItemDrawerSubmit = async record => {
    if(record.get('valueType') === 'VALUE'){
      if(handleIntersection()){
        return notification.error({
          message: intl
            .get(`${modelPrompt}.error.trueVlueList.falseValueList`)
            .d(`符合值与不符合值存在交集，请检查！`),
        });
      }
      if(!await falseNumberDs?.validate())return
    }
    const validate = await record.validate(true);
    if (!validate) {
      return;
    }
    const {
      serialNumber,
      tagId,
      tagGroupAssignId,
    } = record.toData();
    const sameSerialNumberFlag = dataItemTableDs
      .toData()
      .some(
        item => item.serialNumber === serialNumber && item.tagGroupAssignId !== tagGroupAssignId,
      );
    const sameTagFlag = dataItemTableDs
      .toData()
      .some(item => item.tagId === tagId && item.tagGroupAssignId !== tagGroupAssignId);
    if (sameSerialNumberFlag) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.sameSerialNumber`).d('当前序号已存在'),
      });
      return;
    }
    if (sameTagFlag) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.sameTag`).d('当前数据项已选用'),
      });
      return;
    }
    _dataItemDrawer.close();
    record.set('trueValueList', trueNumberDs.toData())
    record.set('falseValueList', falseNumberDs.toData())
    // handleValidate()
  };

  const dataItemModify = record => {
    if (!record) {
      const newLineNumber =
        (
          dataItemTableDs.toData().sort((a, b) => b.serialNumber - a.serialNumber)[0] || {
            serialNumber: 0,
          }
        ).serialNumber || 0;
      dataItemTableDs.create({
        tagGroupAssignId: -Date.parse(new Date()),
        serialNumber: parseInt(newLineNumber / 10, 10) * 10 + 10,
      });
      dataItemTableDs.current.init('tagLov', null);
    }
    dataItemTableDs.current.save();

    if(record){
      trueNumberDs.loadData(record.toData().trueValueList||[])
      falseNumberDs.loadData(record.toData().falseValueList||[])
    }else{
      trueNumberDs.reset()
      falseNumberDs.reset()
    }
    _dataItemDrawer = Modal.open({
      key: Modal.key(),
      title: record
        ? intl.get(`${modelPrompt}.dataItemEdit`).d('数据项编辑')
        : intl.get(`${modelPrompt}.dataItemCreate`).d('新建数据项'),
      drawer: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: <DataItemInfoDrawer
      // edit 为true时 不可编辑
        edit={!canEdit||record?.get('qisFlag') === 'Y'}
        record={record || dataItemTableDs.current}
        falseNumberDs={falseNumberDs}
        trueNumberDs={trueNumberDs}
        handleChangeLov={handleChangeLov}
      />,
      footer: !canEdit ? (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              dataItemDrawerClose(record);
            }}
          >
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        </div>
      ) : (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              dataItemDrawerClose(record);
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            type="submit"
            onClick={() => {
              dataItemDrawerSubmit(record || dataItemTableDs.current);
            }}
            color={ButtonColor.primary}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  const synchronous = record => {
    setSynchronousLoading(true);
    const url = `${API_HOST}${
      BASIC.HMES_BASIC
    }/v1/${getCurrentOrganizationId()}/mt-tag/detail/ui?tagId=${record.get('tagId')}`;
    myInstance.get(url).then(res => {
      const validateRes = getResponse(res.data);
      if (validateRes && validateRes.success && validateRes.rows) {
        loadDataKeys.forEach(key => {
          record.set(key, validateRes.rows[key] || null);
        });
        record.set('valueListDesc', validateRes.rows.valueList || null);
      }
      setSynchronousLoading(false);
      notification.success();
    });
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!canEdit}
          onClick={() => dataItemModify()}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      name: 'editColumn',
      align: 'center',
      width: 70,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => {
            dataItemTableDs.remove(record);
            // handleValidate()
          }}
        >
          <Button disabled={!canEdit||record?.get('qisFlag') === 'Y'} funcType="flat" icon="remove" shape="circle" size="small" />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      header: SortColumnC7n({
        name: 'serialNumber',
        title: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
        ds: dataItemTableDs,
      }),
      name: 'serialNumber',
      align: 'left',
      width: 120,
    },
    {
      name: 'tagLov',
      width: 180,
      renderer: ({ record }) => (
        <a
          onClick={() => {
            dataItemModify(record);
          }}
        >
          {record.get('tagCode')}
        </a>
      ),
      filter: true,
    },
    {
      name: 'tagDescription',
      width: 180,
      filter: true,
    },
    {
      name: 'enableFlag',
      align: 'center',
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'displayValueFlag',
      align: 'center',
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'allowUpdateFlag',
      align: 'center',
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'valueAllowMissing',
      align: 'center',
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    { name: 'valueTypeDesc', width: 120, filter: true },
    { name: 'collectionMethod', width: 120 },
    {
      name: 'trueValue',
      width: 200,
      renderer: ({ value, record }) => {
        if(record.get('valueType') === 'VALUE'){
          if(record.get('trueValueList')&&record.get('trueValueList').length){
            const temp = record.get('trueValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue&&<Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    { name: 'falseValue', width: 200,
      renderer: ({ value, record }) => {
        if(record.get('valueType') === 'VALUE'){
          if(record.get('falseValueList')&&record.get('falseValueList').length){
            const temp = record.get('falseValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue&&<Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    // { name: 'minimumValue', width: 120 },
    // { name: 'maximalValue', width: 120 },
    { name: 'uomLov', width: 120 },
    {
      name: 'valueList',
      width: 240,
      renderer: ({ record }) => {
        const valueList = record.get('valueListDesc') || record.get('valueList') || [];
        const valueList1 = typeof valueList === 'string' ? valueList.split(',') : valueList;
        return (
          valueList1.length &&
          valueList1.map(item => (
            <Tag key={item} color="blue">
              {item}
            </Tag>
          ))
        );
      },
    },
    { name: 'dateFormat', width: 120 },
    { name: 'defaultNcCode', width: 120 },
    {
      name: 'specialRecordFlag',
      align: 'center',
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    { name: 'originOperationName', align: 'left', width: 150 },
    {
      name: 'modifyFlag',
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      name: 'siteCode',
      align: 'center',
      width: 120,
      lock: 'right',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => {
                synchronous(record);
              }}
              disabled={!canEdit || record?.get('qisFlag') === 'Y'}
            >
              {intl.get(`${modelPrompt}.synchronizeData`).d('同步数据')}
            </a>
          </span>
        );
      },
    },

  ];

  return (
    <Spin spinning={synchronousLoading} delay={300}>
      <Table dataSet={dataItemTableDs} columns={columns} virtual style={{ height: tableHeight }} />
    </Spin>
  );
};

export default DataItemInfoTab;
