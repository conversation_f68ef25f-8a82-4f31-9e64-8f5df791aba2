/**
 * @Description: 问题管理平台-新建问题DS
 * @Author: <EMAIL>
 * @Date: 2023/7/4 12:43
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType, RecordStatus } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();
// const BASIC = {
//   TARZAN_SAMPLING: '/tznq-24175',
// };

const problemInfoDS: () => DataSetProps = () => ({
  autoCreate: true,
  selection: false,
  dataKey: 'rows',
  paging: false,
  primaryKey: 'problemId',
  fields: [
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCategory`).d('问题类别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_CATEGORY',
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        required: ({ record }) => !record?.get('problemId'),
        disabled: ({ record }) => record?.get('problemId'),
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'siteName',
      dynamicProps: {
        required: ({ record }) => record?.status === RecordStatus.add,
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      bind: 'siteLov.siteName',
    },
    {
      name: 'problemApplyLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemApplyNum`).d('举手填报单'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS_PROBLEM_APPLY',
      textField: 'problemApplyCode',
      lovPara: { tenantId },
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record.get('siteId'),
          queryFlag: 'N',
        }),
        disabled: ({ record }) => !record.get('siteId'),
      },
    },
    {
      name: 'problemRequestId',
      bind: 'problemApplyLov.problemApplyId',
    },
    {
      name: 'problemApplyCode',
      label: intl.get(`${modelPrompt}.problemApplyNum`).d('举手填报单'),
      bind: 'problemApplyLov.problemApplyCode',
    },
    {
      name: 'problemStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemStatus`).d('问题状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_STATUS',
      textField: 'meaning',
      valueField: 'value',
      disabled: true,
      defaultValue: 'NEW',
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题标题'),
      dynamicProps: {
        required: ({ record }) => !record?.get('problemId'),
      },
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.detailDescription`).d('详细描述'),
      required: true,
    },
    {
      name: 'leadPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.leadPerson`).d('跟进人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      textField: 'realName',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'leadPerson',
      bind: 'leadPersonLov.id',
    },
    {
      name: 'leadPersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.leadPerson`).d('跟进人'),
      bind: 'leadPersonLov.realName',
    },
    {
      name: 'registerPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.registerPerson`).d('登记人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      textField: 'realName',
      disabled: true,
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'registerPerson',
      bind: 'registerPersonLov.id',
    },
    {
      name: 'registerPersonRealName',
      bind: 'registerPersonLov.realName',
    },
    {
      name: 'registerPersonCompanyName',
      type: FieldType.string,
      bind: 'registerPersonLov.unitName',
      disabled: true,
      label: intl.get(`${modelPrompt}.registerPersonApartment`).d('登记人部门'),
    },
    {
      name: 'registerTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.registerTime`).d('登记时间'),
    },
    {
      name: 'proposePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.proposePerson`).d('提出人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.EMPLOYEE_POSITION',
      lovPara: { tenantId },
      textField: 'name',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => record?.get('problemId'),
      },
    },
    {
      name: 'proposePerson',
      bind: 'proposePersonLov.employeeId',
    },
    {
      name: 'proposePersonRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposePerson`).d('提出人'),
      bind: 'proposePersonLov.name',
    },
    {
      name: 'proposePersonCompanyName',
      type: FieldType.string,
      disabled: true,
      bind: 'proposePersonLov.unitName',
      label: intl.get(`${modelPrompt}.proposePersonCompanyName`).d('提出部门'),
    },
    {
      name: 'proposeTimePeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.proposeTimePeriod`).d('问题发生时段'),
    },
    {
      name: 'proposeTimePeriodStart',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.proposeTimePeriodStart`).d('问题发生时段从'),
      dynamicProps: {
        required: ({ record }) => !record?.get('problemId'),
        disabled: ({ record }) => record?.get('problemId'),
      },
      max: 'proposeTimePeriodEnd',
    },
    {
      name: 'proposeTimePeriodEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.proposeTimePeriodEnd`).d('问题发生时段至'),
      dynamicProps: {
        required: ({ record }) => !record?.get('problemId'),
        disabled: ({ record }) => record?.get('problemId'),
      },
      min: 'proposeTimePeriodStart',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/base-detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          const _proposeTimePeriod = rows?.proposeTimePeriod.split('～');
          return {
            rows: {
              ...rows,
              proposeTimePeriodStart: _proposeTimePeriod[0],
              proposeTimePeriodEnd: _proposeTimePeriod[1],
            },
          };
        },
      };
    },
  },
  events: {
    update: ({ record, name }) => {
      if (name === 'siteLov') {
        record.set('problemApplyLov', undefined);
      }
    },
  },
});

const sendMessageDS = () => ({
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'receiveUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.receiveUser`).d('消息接收人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.EMPLOYEE_POSITION',
      lovPara: { tenantId },
      textField: 'name',
      required: true,
      multiple: true,
    },
    {
      name: 'receiveIdList',
      bind: 'receiveUserLov.employeeId',
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.messageContent`).d('消息内容'),
      required: true,
    },
  ],
});

export { problemInfoDS, sendMessageDS };
