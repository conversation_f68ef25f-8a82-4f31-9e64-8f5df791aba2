/*
 * @Description: 市场活动评估单-主界面DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-15 10:05:27
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-04 14:01:22
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.marketActive.marketEstimateDoc';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'marketEstimateId',
  queryFields: [
    {
      name: 'marketEstimateCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketEstimateCode`).d('评估单编号'),
    },
    {
      name: 'marketEstimateStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketEstimateStatus`).d('状态'),
      lookupCode: 'YP.QIS.MARKET_ESTIMATE_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'marketEstimateResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketEstimateResult`).d('评估结果'),
      lookupCode: 'YP.QIS.MARKET_ESTIMATE_RESULT',
      lovPara: { tenantId },
    },
    {
      name: 'faultPhenomenon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultPhenomenon`).d('故障现象'),
    },
    {
      name: 'occurrence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.occurrence`).d('发生情况'),
    },
    {
      name: 'batteryPackModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryPackModel`).d('电池包型号'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'itemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemCode`).d('客户零件号'),
    },
    {
      name: 'itemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemName`).d('客户零件名称'),
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('重要度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
    },
    {
      name: 'createdPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdPerson`).d('创建人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'createdPersonLov.id',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'reviewPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reviewPerson`).d('评估人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'reviewBy',
      bind: 'reviewPersonLov.id',
    },
    {
      name: 'reviewTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.reviewTimeFrom`).d('评估时间从'),
      max: 'reviewTimeTo',
    },
    {
      name: 'reviewTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.reviewTimeTo`).d('评估时间至'),
      min: 'reviewTimeFrom',
    },
  ],
  fields: [
    {
      name: 'marketEstimateCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketEstimateCode`).d('评估单编号'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'marketEstimateStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketEstimateStatus`).d('状态'),
      lookupCode: 'YP.QIS.MARKET_ESTIMATE_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'marketEstimateResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketEstimateResult`).d('评估结果'),
      lookupCode: 'YP.QIS.MARKET_ESTIMATE_RESULT',
      lovPara: { tenantId },
    },
    {
      name: 'responsibleDepartmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleDepartmentName`).d('责任部门'),
    },
    {
      name: 'faultPhenomenon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultPhenomenon`).d('故障现象'),
    },
    {
      name: 'occurrence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.occurrence`).d('发生情况'),
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('重要度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
    },
    {
      name: 'batteryPackModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryPackModel`).d('电池包型号'),
    },
    {
      name: 'ypItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ypItemCode`).d('物料编码'),
    },
    {
      name: 'ypItemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ypItemName`).d('物料描述'),
    },
    {
      name: 'itemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemCode`).d('客户零件号'),
    },
    {
      name: 'itemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemName`).d('客户零件名称'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题描述'),
    },
    {
      name: 'applyDepartmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyDepartmentName`).d('申请部门'),
    },
    {
      name: 'vehicleModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('车型'),
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('提案理由'),
    },
    {
      name: 'objectQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectQty`).d('对象数量'),
    },
    {
      name: 'objectRange',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.objectRange`).d('对象范围'),
      bucketName: 'qms',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
    },
    {
      name: 'reviewInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewInfo`).d('评估信息'),
    },
    {
      name: 'reviewByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewByName`).d('评估人'),
    },
    {
      name: 'reviewTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewTime`).d('评估时间'),
    },
    {
      name: 'reviewEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.reviewEnclosure`).d('评估附件'),
      bucketName: 'qms',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-estimates/page/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };