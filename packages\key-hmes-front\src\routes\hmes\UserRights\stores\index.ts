/**
 * @Description: 物料计划属性维护-列表页-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-08 14:35:24
 * @LastEditTime: 2022-11-11 14:17:13
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.model.hmes.userRights';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'kid',
  queryFields: [
    {
      name: 'userName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userName`).d('员工账号'),
    },
    {
      name: 'userDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userDesc`).d('员工姓名'),
    },
    {
      name: 'organizationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationType`).d('组织类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=USER_ORGANIZATION_TYPE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'organizationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationCode`).d('组织编码'),
    },
    {
      name: 'organizationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationDesc`).d('组织描述'),
    },
    {
      name: 'defaultOrganizationFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defaultOrganizationFlag`).d('默认状态'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
  ],
  fields: [
    {
      name: 'userLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.userName`).d('员工账号'),
    },
    {
      name: 'userId',
      bind: 'userLov.userId',
    },
    {
      name: 'userName',
      bind: 'userLov.userName',
    },
    {
      name: 'userDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userDesc`).d('员工姓名'),
    },
    {
      name: 'organizationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationType`).d('组织类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=USER_ORGANIZATION_TYPE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'organizationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationCode`).d('组织编码'),
    },
    {
      name: 'organizationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationDesc`).d('组织描述'),
    },
    {
      name: 'defaultOrganizationFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defaultOrganizationFlag`).d('默认状态'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/property/list/ui`,
        method: 'GET',
      };
    },
  },
});

const allotTypeList = () => {
  return new DataSet({
    data: [
      {
        typeCode: 'Organization',
        description: intl.get(`${modelPrompt}.rightsOrganizationMaintenance`).d('组织权限维护'),
      },
      {
        typeCode: 'Location',
        description: intl.get(`${modelPrompt}.rightsLocationMaintenance`).d('库位权限维护'),
      },
    ],
  });
};

const searchDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'userLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.userName`).d('员工账号'),
      lovCode: "MT.USER.ORG",
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'userId',
      bind: 'userLov.id',
    },
    {
      name: 'userName',
      bind: 'userLov.loginName',
    },
    {
      name: 'userDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userDesc`).d('员工姓名'),
      bind: 'userLov.realName',
    },
    {
      name: 'organizationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.drawerOrganizationType`).d('组织类型'),
      lookupCode: 'MT.MODEL.USER_ORGANIZATION_TYPE',
      textField: 'meaning',
      valueField: 'value',
      computedProps: {
        lookupCode: ({ record }) => {
          if (record.get('allotType') === 'Organization') {
            return 'MT.MODEL.USER_ORGANIZATION_TYPE';
          }
          return 'MT.MODEL.USER_LOCATOR';
        },
        disabled({ record }) {
          return !record.get('userId');
        },
      },
    },
    {
      name: 'organizationLov',
      type: FieldType.object,
      lovCode: 'MT.MODEL.USER_ORG_LOCTOR',
      label: intl.get(`${modelPrompt}.calendarOrganizationId`).d('组织编码'),
      textField: 'organizationCode',
      valueField: 'organizationId',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            organizationType: record.get('organizationType'),
          }
        },
        disabled: ({ record }) => {
          return !record.get('organizationType');
        },
      },
    },
    {
      name: 'organizationId',
      bind: 'organizationLov.organizationId',
    },
    {
      name: 'organizationCode',
      bind: 'organizationLov.organizationCode',
    },
    {
      name: 'allotType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      defaultValue: 'Organization',
      options: allotTypeList(),
      computedProps: {
        disabled({ record }) {
          return !record.get('userId');
        },
      },
    },
    {
      // 控制树展开
      name: 'expandedKeys',
      type: FieldType.object,
      defaultValue: [],
    },
    {
      // 树展选中
      name: 'checkedKeys',
      type: FieldType.object,
      defaultValue: [],
    },
  ],
});

const orgTableDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  paging: false,
  fields: [
    {
      name: 'organizationId',
      type: FieldType.string,
    },
    {
      name: 'organizationCode',
      type: FieldType.string,
    },
    {
      name: 'defaultOrganizationFlag',
      type: FieldType.string,
    },
  ],
});

export { tableDS, searchDS, orgTableDS };
