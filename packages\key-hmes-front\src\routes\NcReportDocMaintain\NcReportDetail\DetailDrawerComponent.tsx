/**
 * @Description: 主界面-行明细抽屉
 * @Author: <EMAIL>
 * @Date: 2023/3/13 16:12
 */
import React, { useState } from 'react';
import {
  Button,
  Form,
  Lov,
  NumberField,
  Output,
  Select,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { AttributeDrawer, C7nFormItemSort } from '@components/tarzan-ui';
import { observer } from 'mobx-react';
import { useDataSetEvent } from 'utils/hooks';
import { BASIC } from 'hcm-components-front/lib/utils/config';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.ncReportDocMaintain';

export default observer(props => {
  const { lineDtlDs, canEdit, customizeForm, custConfig, record } = props;
  const ncRecordType = record.get('ncRecordType');
  const [activeKey, setActiveKey] = useState<any>(
    lineDtlDs.map(_rec => String(_rec.get('lineNumber'))),
  );

  useDataSetEvent(lineDtlDs, 'create', ({ record }) =>
    setActiveKey([String(record.get('lineNumber'))]),
  );

  const deleteRecord = record => lineDtlDs.remove(record);

  const handleNcCodeChange = (val, record) => {
    record.set('defectLevel', null);
    if (val) {
      record.set('defectLevel', val.defectLevel);
    }
  };

  return (
    <Collapse activeKey={activeKey} onChange={val => setActiveKey(val)}>
      {lineDtlDs.length &&
        lineDtlDs.map(_record => (
          <Panel
            key={String(_record.get('lineNumber'))}
            header={intl.get(`${modelPrompt}.title.sequence`).d('序号') + _record.get('lineNumber')}
            extra={
              <>
                <AttributeDrawer
                  serverCode={BASIC.TARZAN_SAMPLING}
                  className="org.tarzan.qms.domain.entity.MtNcReportLineDtl"
                  kid={_record?.get('ncReportLineDtlId')}
                  canEdit={canEdit}
                  disabled={!_record?.get('ncReportLineDtlId')}
                  custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.ATTR2`}
                  custConfig={custConfig}
                  type="text"
                />
                <Popconfirm
                  title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                  onConfirm={() => deleteRecord(_record)}
                  okText={intl.get('tarzan.common.button.confirm').d('确认')}
                  cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                >
                  <Button icon="delete" funcType={FuncType.flat} disabled={!canEdit}>
                    {intl.get('tarzan.common.button.delete').d('删除')}
                  </Button>
                </Popconfirm>
              </>
            }
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.DETAIL`,
              },
              <Form record={_record} columns={3} labelWidth={112} disabled={!canEdit}>
                <Lov
                  name="ncCodeLov"
                  onChange={e => {
                    handleNcCodeChange(e, _record);
                  }}
                />
                <Output name="ncCodeDesc" />
                {ncRecordType === 'EO_MATERIAL_NC' && (
                  <C7nFormItemSort name="componentMaterialLov" itemWidth={['70%', '30%']}>
                    <Lov name="componentMaterialLov" />
                    <Select name="componentRevisionCode" />
                  </C7nFormItemSort>
                )}
                {ncRecordType === 'EO_MATERIAL_NC' && <Lov name="componentMaterialLotLov" />}
                {ncRecordType === 'EO_MATERIAL_NC' && <NumberField name="sumAssembleQty" />}
                <Lov name="rootCauseOperationLov" />
                <Lov name="rootCauseWorkcellLov" />
                <Lov name="rootCauseEquipmentLov" />
                <Select name="defectLevel" />
                <NumberField name="defectQty" />
                <Lov name="uomLov" />
                <TextField name="defectLocation" />
                <Lov name="responsibleUserLov" />
                <TextField name="responsibleApartment" />
                <Lov name="ncRecordUserLov" />
                <Output name="ncRecordTime" />
                {ncRecordType === 'EO_MATERIAL_NC' && <TextField name="referenceArea" />}
                {ncRecordType === 'EO_MATERIAL_NC' && <TextField name="referencePoint" />}
                <TextArea name="defectReason" rowSpan={3} colSpan={3} newLine />
                <TextArea name="remark" rowSpan={3} colSpan={3} newLine />
              </Form>,
            )}
          </Panel>
        ))}
    </Collapse>
  );
});
