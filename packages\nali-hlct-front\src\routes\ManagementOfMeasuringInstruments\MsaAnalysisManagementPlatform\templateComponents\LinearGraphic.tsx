/**
 * @Description: MSA分析管理平台-线性图表界面
 * @Author: <EMAIL>
 * @Date: 2023/8/24 15:42
 */
import React, {useState, useEffect, useRef} from 'react';
import echarts from 'echarts';
import {
  max as lodashMax,
  min as lodashMin,
  floor as lodashFloor,
  ceil as lodashCeil,
} from 'lodash';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import Table from "@/routes/ManagementOfMeasuringInstruments/MsaAnalysisManagementPlatform/templateComponents/TableComponent";
import styles
  from "@/routes/ManagementOfMeasuringInstruments/MsaAnalysisManagementPlatform/MsaAnalysisManagementPlatformAnalysisDetail/index.modules.less";

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
let linearChart; // 线性图chart

const colorList = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
];

const LinearGraphic = ({ dataSoure, analyseResultDs }) => {
  const [pageInit, setPageInit] = useState(false);
  const linearChartRef = useRef(null);

  useEffect(() => {
    if (!dataSoure.linearChartInfo?.length) {
      return;
    }
    if (!pageInit) {
      linearChart = echarts.init(linearChartRef.current);
      window.onresize = () => {
        setTimeout(() => {
          linearChart.resize();
        }, 300);
      };
    }
    handleInitLinearChart(dataSoure);
  }, [dataSoure.linearChartInfo?.length]);

  const handleInitLinearChart = dataSource => {
    const {
      xTickLabelList,
      yDataList,
      yLowerDataList,
      yUpperDataList,
      maxValue,
      minValue,
      legendList,
    } = handleFormatLinearData(dataSource);
    const option = {
      tooltip: {
        trigger: 'axis',
      },
      color: colorList,
      legend: {
        top: '10%',
        left: 'center',
        data: legendList,
      },
      xAxis: {
        type: 'category',
        data: xTickLabelList,
        axisTick: {
          show: true,
          alignWithLabel: true,
        },
        // splitLine: {
        //   show: true,
        //   lineStyle: {
        //     color: 'rgba(0,0,0,0.03)',
        //   },
        // },
      },
      yAxis: {
        type: 'value',
        max: lodashCeil(lodashMax([maxValue, 0]) * 100) / 100,
        min: lodashFloor(lodashMin([minValue, 0]) * 100) / 100,
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.03)',
          },
        },
      },
      series: [
        {
          name: legendList[0],
          type: 'line',
          data: yUpperDataList,
          symbolSize: 8,
          smooth: true,
        },
        {
          name: legendList[1],
          type: 'line',
          data: yDataList,
          symbolSize: 8,
          smooth: true,
          markLine: {
            silent: true,
            symbol: 'none',
            data: [
              {
                name: 'y = 0',
                yAxis: 0,
                label: {
                  formatter: 'y = 0',
                  color: '#000',
                  fontSize: 10,
                },
                lineStyle: {
                  color: colorList[3],
                  width: 1.2,
                },
              },
            ],
          },
        },
        {
          name: legendList[2],
          type: 'line',
          data: yLowerDataList,
          symbolSize: 8,
          smooth: true,
        },
      ],
    };
    linearChart.setOption(option, true);
    // 初始化chart事件绑定
    if (!pageInit) {
      setPageInit(true);
    }
  };

  const handleFormatLinearData = dataSource => {
    const {linearChartInfo} = dataSource;
    const xTickLabelList: any[] = []; // x轴坐标名称
    const yDataList: any = []; // 拟合直线数据
    const yLowerDataList: any = []; // 置信区间下限数据
    const yUpperDataList: any = []; // 置信区间上限数据
    const legendList: string[] = ['置信区间上限', '拟合直线', '置信区间下限']; // 图例数据
    let maxValue;
    let minValue;
    (linearChartInfo || []).forEach(colItem => {
      const {xInfo, yInfo, yLowerInfo, yUpperInfo} = colItem;
      const _maxValue = Math.max(yInfo, yLowerInfo, yUpperInfo);
      const _minValue = Math.min(yInfo, yLowerInfo, yUpperInfo);
      if (maxValue < _maxValue || !maxValue) {
        maxValue = _maxValue;
      }
      if (minValue > _minValue || !minValue) {
        minValue = _minValue;
      }
      xTickLabelList.push(xInfo);
      yDataList.push(yInfo);
      yLowerDataList.push(yLowerInfo);
      yUpperDataList.push(yUpperInfo);
    });
    return {
      xTickLabelList,
      yDataList,
      yLowerDataList,
      yUpperDataList,
      maxValue,
      minValue,
      legendList,
    };
  };

  const bisaColumn = [
    {
      name: 'ev',
      title: intl.get(`${modelPrompt}.label.ev`).d('重复性EV'),
    },
    {
      name: 'evPercent',
      title: intl.get(`${modelPrompt}.label.evPercent`).d('EV%'),
    },
    {
      name: 'fitCurve',
      title: intl.get(`${modelPrompt}.label.fitCurve`).d('拟合直线'),
    },
    {
      name: 't',
      title: intl.get(`${modelPrompt}.label.t`).d('临界值'),
    },
    {
      name: 'ta',
      title: intl.get(`${modelPrompt}.label.ta`).d('斜率统计值'),
    },
    {
      name: 'tb',
      title: intl.get(`${modelPrompt}.label.tb`).d('截距统计值'),
    },
  ];

  return (
    <div>
      {Boolean(dataSoure?.tableInfo?.length) && (
        <>
          <br/>
          <br/>
          <h4>{intl.get(`${modelPrompt}.title.dataAnalyse`).d('数据分析')}</h4>
          <div className='containerBorder'>
            <Table data={[{...dataSoure.linearTableInfo}]} columns={bisaColumn}/>
          </div>
          <br/>
          <br/>
          <h4>{intl.get(`${modelPrompt}.title.msaResult`).d('分析结果')}</h4>
          <br/>
          <div className={styles.resultContainer}>
            <table>
              <tbody>
                <tr>
                  <td> {intl.get(`${modelPrompt}.label.msaResult`).d('分析结果')}:</td>
                  <td>{analyseResultDs.current?.getField('msaResult')!.getText()}</td>
                </tr>
                <tr>
                  <td> {intl.get(`${modelPrompt}.label.msaConclusion`).d('分析结论')}: </td>
                  <td>{dataSoure.msaConclusion}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <br/>
          <h4>{intl.get(`${modelPrompt}.title.analyseGraphic`).d('分析图')}</h4>
          <div
            id="linear-chart"
            ref={linearChartRef}
            style={{
              height: '300px',
            }}
          />
        </>
      )}
    </div>
  );
};

export default observer(LinearGraphic);
