import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  routes: [
    {
      path: '/aps/opportunityOrderManagement',
      authorized: true,
      routes: [
        {
          title: '机会订单管理',
          path: '/aps/opportunityOrderManagement/list',
          component: './OpportunityOrder/list/listPage',
        },
        {
          title: '绑定销售订单',
          path: '/aps/opportunityOrderManagement/bind/:ooLineId',
          component: './SalesOrderBinding/list/listPage',
        },
      ],
    },
    {
      path: '/aps/demand',
      title: '主需求管理',
      authorized: true,
      routes: [
        {
          path: '/aps/demand/list',
          component: './DemandManagement/list/listPage',
        },
        {
          path: '/aps/demand/split/:id',
          component: './DemandManagement/split/splitPage',
        },
      ],
    },
    {
      path: '/aps/demand-supply-matching',
      title: '主需求供需匹配结果',
      authorized: true,
      routes: [
        {
          path: '/aps/demand-supply-matching/list',
          component: './SupplyMatching/list/completeSet',
        },
        {
          path: '/aps/demand-supply-matching/detail/:parameter',
          component: './SupplyMatching/list/completeSet',
        },
      ],
    },
    {
      path: '/aps/salesOrderManagement',
      title: '销售订单管理',
      authorized: true,
      component: './SalesOrder/list/listPage',
    },
    {
      path: '/aps/efficiencs',
      title: '生产能力维护',
      authorized: true,
      routes: [
        {
          path: '/aps/efficiencs/list',
          component: '../pages/Efficiency/list/ListPage.js',
        },
        {
          path: '/aps/efficiencs/import/:code',
          component: '../pages/himp/CommentImport.js',
        },
      ],
    },
    {
      title: '月度需求计划',
      priority:10000,
      path: '/mltp/monthly-demand-planning',
      authorized: true,
      component: '../pages/MonthPlan/List/MonthPlanList',
    },
    {
      title: '数据收集组维护',
      priority:10000,
      path: '/hmes/acquisition/new-data-item',
      routes: [
        {
          priority:10000,
          path: '/hmes/acquisition/new-data-item/list',
          component: '../pages/acquisition/NewDataItem',
        },
        {
          priority:10000,
          path: '/hmes/acquisition/new-data-item/detail/:id',
          component: '../pages/acquisition/NewDataItem/Detail',
        },
      ],
    },
    // {
    //   title: '生产指令管理',
    //   priority:10000,
    //   path: '/hmes/workshop/production-order-mgt',
    //   routes: [
    //     {
    //       priority:10000,
    //       path: '/hmes/workshop/production-order-mgt/list',
    //       component: '../pages/workshop/ProductionOrderMgt/ProductionOrderMgtList',
    //     },
    //     {
    //       priority:10000,
    //       path: '/hmes/workshop/production-order-mgt/detail/:id',
    //       component: '../pages/workshop/ProductionOrderMgt/ProductionOrderMgtDetail',
    //     },
    //   ],
    // },
    {
      title: '事件查询',
      priority:10000,
      path: '/hmes/event/query',
      component: '../pages/event/EventQuery',
    },
    {
      title: '数据收集组维护',
      priority:10000,
      path: '/hmes/acquisition/data-collection',
      routes: [
        {
          priority:10000,
          path: '/hmes/acquisition/data-collection/list',
          component: '../pages/acquisition/Collection/CollectionList',
        },
        {
          priority:10000,
          path: '/hmes/acquisition/data-collection/detail/:id',
          component: '../pages/acquisition/Collection/CollectionDetail',
        },
      ],
    },
    // {
    //   path: '/aps/supply-balance',
    //   title: '供需平衡',
    //   authorized: true,
    //   priority: 1000,
    //   routes: [
    //     {
    //       path: '/aps/supply-balance/list',
    //       priority: 1000,
    //       component: './SupplyBalance/list/ListPage',
    //     },
    //     {
    //       path: '/aps/supply-balance/detail/create',
    //       priority: 1000,
    //       component: './SupplyBalance/detail/DetailPage',
    //     },
    //     {
    //       path: '/aps/supply-balance/detail/edit/:id',
    //       priority: 1000,
    //       component: './SupplyBalance/detail/DetailPage',
    //     },
    //   ],
    // },
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
        },
      },
    ],
  ],
});
