import axios from 'axios';
import { namespace } from './model';

export async function getLayout() {
  const url = `/_api/${namespace}/layout`;
  return axios.get(url);
}

export async function saveLayout(layout: any) {
  const url = `/_api/${namespace}/layout`;
  return axios.post(url, { layout });
}

export async function getEnableCards() {
  const url = `/_api/${namespace}/enabled`;
  return axios.get(url);
}

export async function updateEnabled(enabled: string[]) {
  const url = `/_api/${namespace}/enabled`;
  return axios.post(url, {
    enabled,
  });
}
