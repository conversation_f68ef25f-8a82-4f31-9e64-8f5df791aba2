/**
 * @Description: CPK对比图
 * @Author: <<EMAIL>>
 * @Date: 2022-04-19 16:07:43
 * @LastEditTime: 2022-12-02 16:50:18
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Button, Form, DataSet, Row, Col, DateTimePicker, Select, Lov } from 'choerodon-ui/pro';
import { Spin, Tag } from 'choerodon-ui';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import echarts from 'echarts';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { FetchChartDetail } from './services';
import { detailDS } from './stores/CPKComparisonChartDS';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hspc.CPKComparisonChart';

const option: any = {
  color: [
    'rgba(252,235,188,0.8)',
    'rgba(246,192,34,0.8)',
    'rgba(174,242,215,0.8)',
    'rgba(168,189,253,0.8)',
    'rgba(83,119,235,0.8)',
    'rgba(213,134,253,0.8)',
    'rgba(181,192,206,0.8)',
    'rgba(53,79,132,0.8)',
  ],
  legend: {
    selected: {
      pp: false,
      ppk: false,
      ppl: false,
      ppu: false,
      cp: false,
      cpl: false,
      cpu: false,
    },
  },
  tooltip: {},
  toolbox: {
    show: true,
    feature: {
      mark: { show: true },
      saveAsImage: { show: true, title: ' ' },
    },
  },
  dataset: {
    source: [],
  },
  xAxis: { type: 'category' },
  yAxis: {},
  series: [
    { type: 'bar', itemStyle: { normal: { label: { show: true, position: 'top' } } } },
    { type: 'bar', itemStyle: { normal: { label: { show: true, position: 'top' } } } },
    { type: 'bar', itemStyle: { normal: { label: { show: true, position: 'top' } } } },
    { type: 'bar', itemStyle: { normal: { label: { show: true, position: 'top' } } } },
    { type: 'bar', itemStyle: { normal: { label: { show: true, position: 'top' } } } },
    { type: 'bar', itemStyle: { normal: { label: { show: true, position: 'top' } } } },
    { type: 'bar', itemStyle: { normal: { label: { show: true, position: 'top' } } } },
    { type: 'bar', itemStyle: { normal: { label: { show: true, position: 'top' } } } },
  ],
  dataZoom: [
    {
      realtime: true,
      startValue: 0,
      minValueSpan: 1,
      xAxisIndex: [0],
    },
  ],
};

const JudgementRuleGroupDetail = () => {
  const chartRef = useRef();
  const chart = useRef<any>();
  const detailDs = useMemo(() => new DataSet({ ...detailDS() }), []);

  const [tagList, setTagList] = useState<any[]>([]);

  const fetchChartDetail = useRequest(FetchChartDetail(), { manual: true });

  useEffect(() => {
    chart.current = echarts.init(chartRef.current);
    window.onresize = () => {
      setTimeout(() => {
        chart.current.resize();
      }, 300);
    };
  }, []);

  useEffect(() => {
    function processDataSetListener(flag) {
      const handler = flag ? detailDs.addEventListener : detailDs.removeEventListener;
      handler.call(detailDs, 'update', handleUpdateControlLov);
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  const handleUpdateControlLov = ({ name, value }) => {
    if (name === 'controlLov') {
      setTagList(value || []);
    }
  };

  const keyDownClick = async e => {
    if (e.keyCode === 13) {
      clickHandler();
    }
  };

  const initChartData = (chartData: any[]) => {
    const _source: any[] = [];
    if (!chartData.length) {
      chart.current.dispose();
      return;
    }
    chartData.forEach(item => {
      if (!_source[0]) {
        _source.push(['product', 'pp', 'ppk', 'ppl', 'ppu', 'cp', 'cpk', 'cpl', 'cpu']);
      }
      const { pp, ppk, ppl, ppu } = item.cpkCalculateResult.entiretyInfo;
      const { cp, cpk, cpl, cpu } = item.cpkCalculateResult.potentialInfo;
      let _key = '';
      switch (detailDs!.current!.get('xName')) {
        case 'CONTROL_CODE':
          _key = 'controlCode';
          break;
        case 'CONTROL_DESC':
          _key = 'controlDesc';
          break;
        case 'CHART_TITLE':
          _key = 'chartTitle';
          break;
        default:
          _key = 'controlCode';
          break;
      }
      _source.push([item[_key], pp, ppk, ppl, ppu, cp, cpk, cpl, cpu]);
    });
    option.dataset.source = _source;
    if (chart.current.isDisposed()) {
      chart.current = echarts.init(chartRef.current);
    }
    chart.current.setOption(option, true);
  };

  const clickHandler = async () => {
    const flag = await detailDs.validate();
    if (!flag) {
      return;
    }
    const {
      dateFrom,
      dateTo,
      controlLov,
      chartType,
      processObjectId,
    } = detailDs!.current!.toData();
    fetchChartDetail.run({
      params: {
        dateFrom,
        dateTo,
        chartType,
        processObjectId,
        controlIds: controlLov.map(item => item.controlId || item.analysisId),
      },
      onSuccess: res => {
        initChartData(res);
      },
    });
  };

  const loadEmptyData = () => {
    detailDs!.current!.reset();
    setTagList([]);
  };

  const handleTypeChange = value => {
    detailDs.current!.init('controlLov', null);
    detailDs.current!.init('processObject', null);
    if (value === 'CONTROL') {
      detailDs.current!.init(
        'dateFrom',
        new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      );
      detailDs.current!.init('dateTo', new Date());
    }
    if (value === 'ANALYSIS') {
      detailDs.current!.init('dateFrom', null);
      detailDs.current!.init('dateTo', null);
    }
    setTagList([]);
  };

  const handleCloseTag = tagInfo => {
    const keyFlag = detailDs!.current!.get('chartType') === 'CONTROL';
    const controlChartInfoList = (detailDs!.current!.get('controlLov') || []).filter(
      item =>
        item[keyFlag ? 'controlId' : 'analysisId'] !==
        tagInfo[keyFlag ? 'controlId' : 'analysisId'],
    );
    detailDs.current!.set('controlLov', controlChartInfoList);
  };

  return (
    <div className="hmes-style">
      <Spin spinning={fetchChartDetail.loading}>
        <Header title={intl.get(`${modelPrompt}.title`).d('CPK对比图')} />
        <Content>
          <Row>
            <Col span={18}>
              <Form
                columns={3}
                labelWidth={121}
                onKeyDown={keyDownClick}
                dataSet={detailDs}
                className={styles['query-form']}
              >
                <Select name="chartType" onChange={handleTypeChange} clearButton={false} />
                <DateTimePicker name="dateFrom" />
                <DateTimePicker name="dateTo" />
                <Select name="xName" />
                <Lov name="processObject" />
                <br />
                <Form.Item colSpan={3} name="controlLov" className={styles['query-tag-list']}>
                  <div>
                    {tagList.map(item => {
                      return (
                        <Tag
                          className={styles['query-tag']}
                          key={item.controlId || item.analysisId}
                          closable
                          afterClose={() => handleCloseTag(item)}
                        >
                          {item.controlCode || item.analysisCode}
                        </Tag>
                      );
                    })}
                    <Lov
                      dataSet={detailDs}
                      name="controlLov"
                      mode={ViewMode.button}
                      clearButton={false}
                      autoSelectSingle={false}
                      className={styles['tag-lov']}
                      noCache
                    >
                      +
                    </Lov>
                  </div>
                </Form.Item>
              </Form>
            </Col>
            <Col span={6}>
              <Form
                columns={1}
                labelWidth={0}
                onKeyDown={keyDownClick}
                dataSet={detailDs}
                className={styles['query-form']}
              >
                <div style={{ padding: 3 }}>
                  <Button onClick={loadEmptyData}>
                    {intl.get('tarzan.common.button.reset').d('重置')}
                  </Button>
                  <Button
                    onClick={clickHandler}
                    color={ButtonColor.primary}
                    type={ButtonType.submit}
                  >
                    {intl.get('tarzan.common.button.search').d('查询')}
                  </Button>
                </div>
              </Form>
            </Col>
          </Row>
          <div
            id="historical-graphic-chart"
            // @ts-ignore
            ref={chartRef}
            style={{
              marginTop: '24px',
              height: '500px',
            }}
          />
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hspc.CPKComparisonChart', 'tarzan.common'],
})(JudgementRuleGroupDetail);
