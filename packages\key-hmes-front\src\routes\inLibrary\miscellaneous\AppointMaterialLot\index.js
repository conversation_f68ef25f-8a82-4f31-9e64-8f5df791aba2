/**
 * 杂项工作台-指定物料批页面
 * @date 2023-07-28
 * <AUTHOR> <<EMAIL>>
 */
import { BASIC } from '@/utils/config';
import { Collapse } from 'choerodon-ui';
import { Button, DataSet, Table } from 'choerodon-ui/pro';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import React, { useEffect, useState } from 'react';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import withProps from 'utils/withProps';
import { headerBindTableDS, lineBindTableDS } from '../stores/AppointMaterialLotDS';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.miscellaneous';

// const lugeUrl = '-30607';
const lugeUrl = '';

const AppointMaterialLotPage = props => {
  const {
    headerBindTableDs,
    lineBindTableDs,
    appointProps,
  } = props;

  const [headerSaveDisabled, setHeaderSaveDisabled] = useState(false); // 头保存disabled
  const [lineSaveDisabled, setLineSaveDisabled] = useState(false); // 行保存disabled


  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (headerBindTableDs) {
      const handler = flag ? headerBindTableDs.addEventListener : headerBindTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerBindTableDs, 'batchSelect', handleHeaderSelectUpdate);
      handler.call(headerBindTableDs, 'batchUnSelect', handleHeaderSelectUpdate);
    }
    if (lineBindTableDs) {
      const line = flag ? lineBindTableDs.addEventListener : lineBindTableDs.removeEventListener;
      // 头选中和撤销选中事件
      line.call(lineBindTableDs, 'batchSelect', handleLineSelectUpdate);
      line.call(lineBindTableDs, 'batchUnSelect', handleLineSelectUpdate);
    }
  };

  // 头勾选
  const handleHeaderSelectUpdate = () => {
    setHeaderSaveDisabled(headerBindTableDs.selected.length);
  };

  // 行勾选
  const handleLineSelectUpdate = () => {
    setLineSaveDisabled(lineBindTableDs.selected.length);
  };

  // 根据id查询
  useEffect(() => {
    lineBindTableDs.loadData([]);
    lineBindTableDs.queryDataSet.loadData([]);
    headerBindTableDs.loadData([]);
    headerBindTableDs.queryDataSet.loadData([]);
    setTimeout(() => {
      queryDetail();
    }, 10);
  }, []);

  const queryDetail = () => {
    // 行默认查询条件
    lineBindTableDs.setQueryParameter('functionName', 'miscellaneous');
    lineBindTableDs.setQueryParameter('instructionDocId', appointProps.instructionDocId);
    lineBindTableDs.setQueryParameter('instructionDocType', appointProps.instructionDocType);
    lineBindTableDs.setQueryParameter('instructionId', appointProps.instructionId);
    lineBindTableDs.setQueryParameter('instructionDocLineId', appointProps.instructionDocLineId);
    // 头默认查询条件
    headerBindTableDs.setQueryParameter('functionName', 'miscellaneous');
    headerBindTableDs.setQueryParameter('instructionDocId', appointProps.instructionDocId);
    headerBindTableDs.setQueryParameter('instructionDocType', appointProps.instructionDocType);
    headerBindTableDs.setQueryParameter('instructionId', appointProps.instructionId);
    headerBindTableDs.setQueryParameter('instructionDocLineId', appointProps.instructionDocLineId);
    headerBindTableDs.setQueryParameter('materialId', appointProps.materialId);
    headerBindTableDs.setQueryParameter('revisionCode', appointProps.revisionCode);
    headerBindTableDs.setQueryParameter('siteId', appointProps.siteId);
    headerBindTableDs.setQueryParameter('locatorId', appointProps.locatorId);
    headerBindTableDs.queryDataSet.loadData([{
      ownerType: appointProps.ownerType || null,
      ownerId: appointProps.ownerId || null,
    }]);
    lineBindTableDs.query();
    headerBindTableDs.query();
    setHeaderSaveDisabled(false);
    setLineSaveDisabled(false);
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'identification',
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'primaryUomQty',
    },
    {
      name: 'primaryUomCode',
    },
    {
      name: 'locatorCode',
    },
    {
      name: 'lot',
    },
    {
      name: 'qualityStatusDesc',
    },
    {
      name: 'enableFlag',
    },
    {
      name: 'materialLotStatus',
    },
    {
      name: 'productionDate',
    },
    {
      name: 'expirationDate',
    },
    {
      name: 'extendedShelfLifeTimes',
    },
    {
      name: 'supplierLot',
    },
    {
      name: 'supplierCode',
    },
    {
      name: 'supplierDesc',
    },
    {
      name: 'supplierSiteCode',
    },
    {
      name: 'supplierSiteDesc',
    },
    {
      name: 'customerCode',
    },
    {
      name: 'customerDesc',
    },
    {
      name: 'customerSiteCode',
    },
    {
      name: 'customerSiteDesc',
    },
    {
      name: 'reservedFlag',
    },
    {
      name: 'reservedObjectTypeDesc',
    },
    {
      name: 'reservedObjectCode',
    },
    {
      name: 'createReasonDesc',
    },
    {
      name: 'inLocatorTime',
    },
    {
      name: 'inSiteTime',
    },
    {
      name: 'ownerTypeDesc',
    },
    {
      name: 'ownerCode',
    },
    {
      name: 'ownerDesc',
    },
    {
      name: 'createdUsername',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'lastUpdatedUsername',
    },
    {
      name: 'lastUpdateDate',
    },
  ];

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'materialLotCode',
      width: 150,
    },
    {
      name: 'primaryUomQty',
      width: 150,
    },
    {
      name: 'primaryUomCode',
      width: 150,
    },
    {
      name: 'lot',
      width: 150,
    },
    {
      name: 'materialLotStatus',
      width: 150,
    },

  ];

  // 头保存
  const handleBindMaterial = () => {
    const materialLotIdList =
      headerBindTableDs?.selected?.map(item => {
        return item?.get('materialLotId');
      }) || [];
    return request(`${BASIC.HWMS_BASIC}${lugeUrl}/v1/${tenantId}/mt-product-delivery-platform/line/detail-save/for/ui`, {
      method: 'POST',
      body: {
        instructionId: appointProps.instructionId,
        instructionDocLineId: appointProps.instructionDocLineId,
        toleranceFlag: appointProps.toleranceFlag,
        toleranceType: appointProps.toleranceType,
        toleranceMaxValue: appointProps.toleranceMaxValue,
        toleranceMinValue: appointProps.toleranceMinValue,
        quantity: appointProps.quantity,
        functionName: 'miscellaneous',
        materialLotIdList,
      },
    }).then(res => {
      if (res?.success) {
        queryDetail();
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  // 行保存
  const handleCancelMaterial = () => {
    const instructionDetailIdList =
      lineBindTableDs?.selected?.map(item => {
        return item?.get('instructionDetailId');
      }) || [];
    return request(`${BASIC.HWMS_BASIC}${lugeUrl}/v1/${tenantId}/mt-product-delivery-platform/delete/ui`, {
      method: 'POST',
      body: instructionDetailIdList,
    }).then(res => {
      if (res?.success) {
        queryDetail();
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  const headerTableButtons = [
    <Button
      disabled={!headerSaveDisabled}
      onClick={() => {
        return handleBindMaterial();
      }}
      style={headerSaveDisabled ? {
        color: 'rgb(255, 255, 255)',
        backgroundColor: 'rgb(8, 64, 248)',
        border: '0.01rem solid rgb(8, 64, 248)',
      } : {
        color: 'rgb(140, 140, 140)',
        backgroundColor: 'rgb(245, 245, 245)',
        borderColor: '0.01rem solid rgb(230, 230, 230)',
      }}
    >
      {intl.get('hzero.common.button.save').d('保存')}
    </Button>,
  ]

  const lineTableButtons = [
    <Button
      disabled={!lineSaveDisabled}
      onClick={() => {
        return handleCancelMaterial();
      }}
      style={lineSaveDisabled ? {
        color: 'rgb(255, 255, 255)',
        backgroundColor: 'rgb(8, 64, 248)',
        border: '0.01rem solid rgb(8, 64, 248)',
      } : {
        color: 'rgb(140, 140, 140)',
        backgroundColor: 'rgb(245, 245, 245)',
        borderColor: '0.01rem solid rgb(230, 230, 230)',
      }}
    >
      {intl.get(`${modelPrompt}.button.cancelAppoint`).d('取消指定')}
    </Button>,
  ]

  return (
    <div>
      <Table
        searchCode="miscellaneousAppointOne"
        customizedCode="miscellaneousAppointOne"
        queryBar={TableQueryBarType.filterBar}
        queryBarProps={{
          fuzzyQuery: false,
        }}
        dataSet={headerBindTableDs}
        columns={headerTableColumns}
        buttons={headerTableButtons}
        highLightRow={false}
      />
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.line.bindMaterialLot`).d('已指定物料批')}
          key="basicInfo"
          dataSet={lineBindTableDs}
        >
          {lineBindTableDs && (
            <Table
              searchCode="miscellaneousAppointTwo"
              customizedCode="miscellaneousAppointTwo"
              dataSet={lineBindTableDs}
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              highLightRow={false}
              buttons={lineTableButtons}
              columns={lineTableColumns}
            />
          )}
        </Panel>
      </Collapse>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.receive.miscellaneous', 'tarzan.common'],
})(
  withProps(
    () => {
      const headerBindTableDs = new DataSet({ ...headerBindTableDS() });
      const lineBindTableDs = new DataSet({ ...lineBindTableDS() });
      return {
        headerBindTableDs,
        lineBindTableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(AppointMaterialLotPage),
);
