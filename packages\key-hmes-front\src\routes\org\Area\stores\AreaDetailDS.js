/**
 * @Description: 区域维护详情
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 14:18:05
 * @LastEditTime: 2023-05-18 11:29:20
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.model.org.area';

const DetailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'nowDate',
      type: FieldType.number,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}/mt-mod-area/property/ui`,
        method: 'GET',
      };
    },
    submit: ({ dataSet }) => {
      const { mtModAreaScheduleDTOS, basicsAtttribtesDS } = dataSet.toData()[0];
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { mtModAreaScheduleDTO, ...other } = basicsAtttribtesDS[0];
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}/mt-mod-area/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.AREA_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.AREA`,
        data: {
          ...other,
          mtModAreaScheduleDTO: mtModAreaScheduleDTOS[0],
        },
        method: 'POST',
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
});

const BasicsAtttribtesDS = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  lang: getCurrentLanguage(),
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.model.domain.entity.MtModArea';
      return {
        data: { areaId: record.data.areaId },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_MODEL}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'areaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaCode`).d('区域编码'),
      required: true,
    },
    {
      name: 'areaType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaType`).d('区域类型'),
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=ORGANIZATION_REL_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      name: 'areaCategory',
      type: FieldType.string,
      textField: 'description',
      valueField: 'value',
      label: intl.get(`${modelPrompt}.areaCategory`).d('区域类别'),
      lookupCode: 'MT.AREA_CATEGORY',
    },
    {
      name: 'areaName',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.areaName`).d('区域短描述'),
      required: true,
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.description`).d('区域长描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'country',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.country`).d('国家'),
    },
    {
      name: 'province',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.province`).d('省'),
    },
    {
      name: 'city',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.city`).d('城市'),
    },
    {
      name: 'county',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.county`).d('县'),
    },
    {
      name: 'address',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.address`).d('详细地址'),
    },
  ],
});

const PlanAttributesDS = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  lang: getCurrentLanguage(),
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.model.domain.entity.MtModArea';
      return {
        data: { areaId: record.data.areaId },
        params: { className, fieldName },
        url: `${BASIC.TARZAN_MODEL}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划滚动开始时间'),
    },
    {
      name: 'releaseTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.releaseTimeFence`).d('下达时间栏'),
      min: 0,
    },
    {
      name: 'forceCompleteCycle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.phaseType`).d('强制完成周期'),
      lookupCode: 'MT.FORCE_COMPLETE_CYCLE',
    },
    {
      name: 'demandTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.demandTimeFence`).d('需求时间栏'),
      min: 0,
    },
    {
      name: 'orderTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.orderTimeFence`).d('提前顶层下达时间'),
      min: 0,
    },
    {
      name: 'schedulingNode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planningBase`).d('排程节点'),
      lookupCode: 'MT.SCHEDULING_NODE',
    },
    {
      name: 'fixTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.fixTimeFence`).d('固定时间栏'),
      min: 0,
    },
    {
      name: 'schedulingAlgorithm',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.basicAlgorithm`).d('排程策略'),
      lookupCode: 'MT.BASIC_ALGORITHM',
    },
    {
      name: 'delayTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.delayTimeFence`).d('实际延迟时间'),
      min: 0,
    },
    {
      name: 'frozenTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.frozenTimeFence`).d('冻结时间栏'),
      min: 0,
    },
    {
      name: 'followAreaLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.followAreaCode`).d('跟随区域'),
      lovCode: 'MT.MODEL.AREA',
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId: getCurrentOrganizationId(),
            siteType:
              dataSet.parent && dataSet.parent.current && dataSet.parent.current.get('areaType'),
            areaId:
              dataSet.parent && dataSet.parent.current && dataSet.parent.current.get('areaId'),
            userFlag: 'Y',
          };
        },
      },
    },
    {
      name: 'followAreaId',
      type: FieldType.number,
      bind: 'followAreaLov.areaId',
    },
    {
      name: 'followAreaCode',
      type: FieldType.string,
      bind: 'followAreaLov.areaCode',
    },
    {
      name: 'rollingPeriod',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.rollingPeriod`).d('计划滚动周期'),
      min: 0,
    },
    {
      name: 'forwardPlanningTimeFence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.forwardPlanningTimeFence`).d('顺排时间栏'),
      min: 0,
    },
    {
      name: 'orgSelectionAlgorithm',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineRule`).d('资源选择策略'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=PROD_LINE_RULE`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
  ],
});

export { DetailDS, BasicsAtttribtesDS, PlanAttributesDS };
