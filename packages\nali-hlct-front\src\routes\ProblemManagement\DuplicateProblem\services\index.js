/**
 * @Description: 体系审核管理-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';
// const endUrl = '-43335';

// ${ BASIC.TARZAN_SAMPLING }

// 问题复盘详情
export function fetchProblemReplayConfig(id) {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay/detail/${id}/ui`,
    method: 'GET',
  };
}

// 问题复盘新建
export function savePproblemReplayConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay/save/ui`,
    method: 'POST',
  };
}

// 问题复盘更新
export function updatePproblemReplayConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay/update/ui`,
    method: 'POST',
  };
}

// 问题复盘下达
export function issuedPproblemReplayConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay/issued/ui`,
    method: 'POST',
  };
}

// 问题复盘发布
export function publishPproblemReplayConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay/publish/ui`,
    method: 'POST',
  };
}

// 问题复盘-原因分析及措施改进-新建
export function savePproblemReplayAnalysisConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay-analysis/save/ui`,
    method: 'POST',
  };
}

// 问题复盘-原因分析及措施改进-更新
export function updatePproblemReplayAnalysisConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay-analysis/update/ui`,
    method: 'POST',
  };
}

// 问题复盘-事件轴-新建
export function savePproblemReplayEventConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay-event/save/ui`,
    method: 'POST',
  };
}

// 问题复盘-事件轴-更新
export function updatePproblemReplayEventConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay-event/update/ui`,
    method: 'POST',
  };
}
// 问题复盘-提交审核
export function submitPproblemReplayConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-replay/submit/ui`,
    method: 'POST',
  };
}


