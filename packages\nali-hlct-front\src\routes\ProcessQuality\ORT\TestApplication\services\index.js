/**
 * @Description:ORT测试申请-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-09-18 10:38:58
 * @LastEditTime: 2023-09-18 10:38:58
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

// 获取用户部门
export function getUserDepartmentConfig() {
  return {
    url: `/hpfm/v1/${tenantId}/employee-assigns`,
    method: 'GET',
  };
}

// 取消申请
export function cancelApplyConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-apply/cancel/apply`,
    method: 'POST',
  };
}

// 保存
export function saveApplyConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-apply/save/ui`,
    method: 'POST',
  };
}

// 保存
export function submitApplyConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-apply/submit/ui`,
    method: 'POST',
  };
}

// 详情
export function fetchApplyDetailConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-ort-apply/detail/list/ui`,
    method: 'GET',
  };
}

/**
 * 根据materialId获取物料相关信息
 * @function QueryMaterialInfo
 * @returns {object} fetch Promise
 */
export function QueryMaterialInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-qa-feedbacks/mt-material-ca`,
    method: 'POST',
  };
}

/**
 * 查询用户默认站点
 * @function GetDefaultSite
 * @returns {object} fetch Promise
 */
export function GetDefaultSite() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-user-inspect-permission/default/site/ui`,
    method: 'GET',
  };
}
