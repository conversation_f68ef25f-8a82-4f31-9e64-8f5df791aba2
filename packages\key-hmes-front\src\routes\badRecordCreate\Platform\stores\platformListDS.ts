/**
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-15
 * @description 不良记录平台表
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment/moment';

const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  autoLocateFirst: true,
  multiple: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    // {
    //   name: 'ncRecordNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncRecordNum`).d('不良记录编码'),
    // },
    // {
    //   name: 'ncRecordStatus',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncRecordStatus`).d('不良记录状态'),
    //   textField: 'description',
    //   valueField: 'statusCode',
    //   lovPara: { tenantId },
    //   lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_RECORD_STATUS`,
    //   lookupAxiosConfig: {
    //     transformResponse(data) {
    //       // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
    //       if (data instanceof Array) {
    //         return data;
    //       }
    //       const { rows } = JSON.parse(data);
    //       return rows;
    //     },
    //   },
    // },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordStatus`).d('不良记录状态'),
      name: 'ncRecordStatusList',
      textField: 'description',
      valueField: 'statusCode',
      multiple: ',',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_RECORD_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'eoLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eo`).d('条码'),
      lovCode: 'YP_MES.MES.EO_OK',
      lovPara: { tenantId,queryFlag: 'Y'},
      ignore: FieldIgnore.always,

    },
    {
      name: 'eoId',
      bind: 'eoLov.eoId',
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLot`).d('物料批'),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
      name: 'ncRecordType',
      lookupCode: 'HME.NC_RECORD_TYPE',
      valueField: 'value',
      textField: 'meaning',
      lovPara: { tenantId },
    },
    // {
    //   name: 'ncReviewStatus',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncReviewStatusDesc`).d('审核状态'),
    //   lookupCode: 'MT.MES.REVIEW_STATUS',
    //   lovPara: { tenantId },
    // },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('产品物料'),
      lovCode: 'MT.MATERIAL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'materialName',
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'componentMaterialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.componentMaterial`).d('组件物料'),
      lovCode: 'MT.MATERIAL',
      lovPara: { tenantId },
      textField: 'materialName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'componentMaterialId',
      bind: 'componentMaterialLov.materialId',
    },

    {
      name: 'containerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器'),
      lovCode: 'YP_MES.CONTAINER',
      lovPara: { tenantId },
      valueField: 'description',
      ignore: FieldIgnore.always,
    },
    {
      name: 'containerId',
      bind: 'containerLov.containerId',
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      lovCode: 'MT.NC_CODE',
      textField: 'description',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'ncCodeId',
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      lovPara: { tenantId },
      textField: 'prodLineName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      multiple: true,
      label: intl.get(`${modelPrompt}.ncWorkcellName`).d('不良发现工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'workcellIds',
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncOperationName`).d('不良发现工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      textField: 'operationName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'routerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRouterDesc`).d('不良发现工艺路线'),
      lovCode: 'YP_MES.MES.ROUTER',
      textField: 'routerName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'routerId',
      bind: 'routerLov.routerId',
    },
    // {
    //   name: 'routerStepDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncRouterStepDesc`).d('不良发现步骤'),
    // },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncEquipmentCode`).d('不良发现设备'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      lovPara: { tenantId },
      textField: 'equipmentName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncLocatorName`).d('不良发现库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: { tenantId },
      textField: 'locatorName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'rootCauseWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.rootCauseWorkcellName`).d('不良产生工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'rootCauseWorkcellId',
      bind: 'rootCauseWorkcellLov.workcellId',
    },
    {
      name: 'rootCauseOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.rootCauseOperationCode`).d('不良产生工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      textField: 'operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'rootCauseOperationId',
      bind: 'rootCauseOperationLov.operationId',
    },
    {
      name: 'rootCauseEquipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.rootCauseEquipmentCode`).d('不良产生设备'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      lovPara: { tenantId },
      textField: 'equipmentName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'rootCauseEquipmentId',
      bind: 'rootCauseEquipmentLov.equipmentId',
    },

    {
      name: 'ncStartTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncStartTimeFrom`).d('不良发生时间从'),
      max: 'ncStartTimeTo',
    },
    {
      name: 'ncStartTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncStartTimeTo`).d('不良发生时间至'),
      min: 'ncStartTimeFrom',
    },
    {
      name: 'ncStartUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncStartUserName`).d('不良记录人'),
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'ncStartUserId',
      bind: 'ncStartUserLov.id',
    },
    {
      name: 'shiftTeamLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.shiftTeamName`).d('班组'),
      lovCode: 'MT.SHIFTTEAM',
      lovPara: { tenantId },
      textField: 'shiftTeamName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'shiftTeamId',
      bind: 'shiftTeamLov.shiftTeamId',
    },
    {
      name: 'shiftDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.shiftDate`).d('班次日期'),
      format: 'YYYY-MM-DD',
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
    },
    {
      name: 'ncCloseTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncCloseTimeFrom`).d('不良关闭日期自'),
      max: 'ncCloseTimeTo',
    },
    {
      name: 'ncCloseTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncCloseTimeTo`).d('不良关闭日期至'),
      min: 'ncCloseTimeFrom',
    },
    {
      name: 'ncCloseUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCloseUserName`).d('不良关闭人'),
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'ncCloseUserId',
      bind: 'ncCloseUserLov.id',
    },
  ],
  fields: [
    {
      name: 'ncRecordStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordStatus`).d('不良记录状态'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
    },
    {
      name: 'ncRecordTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('产品物料'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('不良数量'),
    },
    // {
    //   name: 'ncCodeAndDefectQty',
    //   label: intl.get(`${modelPrompt}.ncCodeAndDefectQty`).d('不良代码及缺陷数'),
    // },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('工艺描述'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备'),
    },
    {
      name: 'routerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'ncStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncStartTime`).d('不良发生时间'),
    },
    {
      name: 'ncStartUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncStartUserName`).d('不良记录人'),
    },
    {
      name: 'ncCloseTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCloseTime`).d('不良关闭日期'),
    },
    {
      name: 'ncCloseUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCloseUserName`).d('不良关闭人'),
    },
    {
      name: 'ncRecordNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordNum`).d('不良记录编码'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        data: {
          ...data,
          shiftDate: data.shiftDate ? moment(data.shiftDate).format('yyyy-MM-DD') : undefined,
        },
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-nc-record/platform/head/query/ui`,
        method: 'GET',
      };
    },
  },
});

const lineTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.serialNumber').d('序号'),
    },
    {
      name: 'ncCodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码'),
    },
    {
      name: 'ncCodeStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeStatusDesc`).d('不良代码状态'),
    },
    {
      name: 'rootCauseWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootCauseWorkcellName`).d('不良产生工作单元'),
    },
    {
      name: 'rootCauseEquipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootCauseEquipmentCode`).d('不良产生设备'),
    },
    {
      name: 'rootCauseOperationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootCauseOperationCode`).d('不良产生工艺'),
    },
    {
      name: 'responsibleUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('不良责任人'),
    },
    {
      name: 'responsibleApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleApartment`).d('不良责任部门'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-nc-record/platform/line/query/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS, lineTableDS };
