.problem-sum-info {
  .problem-sum-info-query-bar {
    :global(>:not(:first-child)) {
      display: none;
    }
  }

  .title-component {
    font-size: 16px;
    font-weight: 600;
    line-height: 28px;
    margin-left: 10px;
    display: flex;

    .title-component-title {
      flex-grow: 1;
    }
    
    .title-component-form {
      width: 50%;
      margin-right: 20px;
    }
  }

  .schedule_container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 7px;
    .schedule_status {
      width: 15px;
      height: 15px;
      border-radius: 50%;
      background-color: #11d954;
    }
    .schedule_course {
      width: 20px;
      height: 3px;
      background-color: #e8e8e8;
    }
  }
}