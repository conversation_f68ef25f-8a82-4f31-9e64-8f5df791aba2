import { extendParentConfig } from '@hzerojs/plugin-micro';

// const path = require('path');

export default extendParentConfig({
  webpack5: {},
  devServer: {
    port: 8000, // 为了微前端本地启动，和主模块microService配置的端口对应
  },
  routes: [
    // 所有路由外部整体包裹一个routes
    /**
     * <==============   ALM-系统配置  =================>
     */
    /**
     * 默认规则
     */
    {
      path: '/apfm/frameworks',
      routes: [
        // 默认规则详情
        {
          path: '/apfm/frameworks/detail',
          component: '@/pages/FrameWork',
        },
        // 默认规则创建
        {
          path: '/apfm/frameworks/create',
          component: '@/pages/FrameWork',
        },
      ],
    },
    /**
     *  移动端首页配置
     */
    {
      path: '/amtc/character-home-show',
      routes: [
        {
          path: '/amtc/character-home-show/list',
          component: '@/pages/CharacterHomeShow',
        },
        {
          path: '/amtc/character-home-show/detail/:id',
          component: '@/pages/CharacterHomeShow/Detail',
        },
        {
          path: '/amtc/character-home-show/create',
          component: '@/pages/CharacterHomeShow/Detail',
        },
      ],
    },
    /**
     *  字段定义
     */
    {
      path: '/apfm/field-definition',
      component: '@/pages/FieldDefinition',
    },
    /**
     * 服务区域
     */
    {
      path: '/amdm/maint-sites',
      routes: [
        // 服务区域列表
        {
          path: '/amdm/maint-sites/list',
          models: ['@/models/maintSites'],
          component: '@/pages/MaintSites/List/MaintSiteListPage',
        },
        // 服务区域详情
        {
          path: '/amdm/maint-sites/detail/:maintSiteId',
          models: ['@/models/maintSites', '@/models/frameWork'],
          component: '@/pages/MaintSites/Detail/MaintSiteDetailPage',
        },
        // 服务区域创建
        {
          path: '/amdm/maint-sites/create',
          models: ['@/models/maintSites', '@/models/frameWork'],
          component: '@/pages/MaintSites/Detail/MaintSiteDetailPage',
        },
      ],
    },
    /**
     * 内部组织
     */
    {
      path: '/amdm/organization',
      routes: [
        // 组织列表
        {
          path: '/amdm/organization/list',
          models: ['@/models/organization'],
          component: '@/pages/Organization/List/OrganizationListPage',
        },
        // 组织创建
        {
          path: '/amdm/organization/create',
          models: ['@/models/organization'],
          component: '@/pages/Organization/Detail/OrganizationDetailPage',
        },
        // 组织详情
        {
          path: '/amdm/organization/detail/:id',
          models: ['@/models/organization'],
          component: '@/pages/Organization/Detail/OrganizationDetailPage',
        },
      ],
    },
    /**
     * 库房
     */
    {
      path: '/ammt/stock',
      routes: [
        // 库房列表
        {
          path: '/ammt/stock/list',
          component: '@/pages/Stock',
        },
        // 库房创建
        {
          path: '/ammt/stock/create',
          component: '@/pages/Stock/Detail',
        },
        // 库房详情
        {
          path: '/ammt/stock/detail/:id/:flag',
          component: '@/pages/Stock/Detail',
        },
        {
          path: '/ammt/stock/create-sub/:parentId',
          component: '@/pages/Stock/Detail',
        },
      ],
    },
    // 库存货位
    {
      path: '/ammt/locator',
      routes: [
        {
          path: '/ammt/locator/list',
          models: ['@/models/locator'],
          component: '@/pages/Locator',
        },
      ],
    },
    /**
     *  租户初始化
     */
    {
      path: '/apfm/tenant-init',
      component: '@/pages/TenantInit',
    },
    /**
     * 位置类型
     */
    {
      path: '/amdm/asset-locations-types',
      component: '@/pages/LocationType',
    },
    /**
     * 资产模板
     */
    {
      path: '/aori/asset-template',
      routes: [
        {
          path: '/aori/asset-template/list',
          component: '@/pages/AssetTemplate/List',
        },
        {
          path: '/aori/asset-template/detail/:code',
          component: '@/pages/AssetTemplate/Detail',
        },
        {
          path: '/aori/asset-template/create',
          component: '@/pages/AssetTemplate/Detail',
        },
      ],
    },
    /**
     * 资产分类层级
     */
    {
      path: '/aori/asset-class-level',
      component: '@/pages/AssetClassLevel',
    },
    /**
     * 资产分类
     */
    {
      path: '/aori/assetClass',
      routes: [
        {
          path: '/aori/assetClass/list',
          models: ['@/models/frameWork'],
          component: '@/pages/AssetClass/List',
        },
        {
          path: '/aori/assetClass/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/AssetClass/Detail',
        },
        {
          path: '/aori/assetClass/create',
          models: ['@/models/frameWork'],
          component: '@/pages/AssetClass/Detail',
        },
      ],
    },
    /**
     * 资产专业管理
     */
    {
      path: '/aafm/asset-specialty',
      priority: 10,
      // 资产专业管理列表
      routes: [
        {
          path: '/aafm/asset-specialty/list',
          models: ['@/models/assetSpecialty', '@/models/frameWork'],
          component: '@/pages/AssetSpecialty',
          priority: 10,
        },
        // 资产专业管理详情
        {
          path: '/aafm/asset-specialty/detail/:assetSpecialtyId',
          models: ['@/models/assetSpecialty', '@/models/frameWork'],
          component: '@/pages/AssetSpecialty/Detail',
          priority: 10,
        },
        // 资产专业管理创建
        {
          path: '/aafm/asset-specialty/create',
          models: ['@/models/assetSpecialty', '@/models/frameWork'],
          component: '@/pages/AssetSpecialty/Detail',
          priority: 10,
        },
      ],
    },
    /**
     * 资产状态
     */
    {
      path: '/aafm/asset-status',
      routes: [
        {
          path: '/aafm/asset-status/list',
          component: '@/pages/AssetStatus',
        },
        {
          path: '/aafm/asset-status/detail/:id',
          component: '@/pages/AssetStatus/Detail',
        },
        {
          path: '/aafm/asset-status/create',
          component: '@/pages/AssetStatus/Detail',
        },
      ],
    },
    /**
     * 固定资产折旧规则
     */
    {
      path: '/aafm/depreciation-rule',
      // 固定资产折旧规则列表
      routes: [
        {
          path: '/aafm/depreciation-rule/list',
          models: ['@/models/depreciationRule'],
          component: '@/pages/DepreciationRule',
        },
        // 固定资产折旧规则创建
        {
          path: '/aafm/depreciation-rule/create',
          models: ['@/models/depreciationRule'],
          component: '@/pages/DepreciationRule',
        },
        // 固定资产折旧规则详情
        {
          path: '/aafm/depreciation-rule/detail/:id',
          models: ['@/models/depreciationRule'],
          component: '@/pages/DepreciationRule',
        },
      ],
    },
    /**
     * 固定资产类别
     */
    {
      path: '/afam/fa-category',
      component: '@/pages/FaCategory',
    },
    /**
     * 资产事务处理类型
     */
    {
      path: '/aafm/asset-transaction-type',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aafm/asset-transaction-type/list',
          component: '@/pages/AssetTransactionTypes',
        },
        {
          path: '/aafm/asset-transaction-type/detail/:id/:flag',
          component: '@/pages/AssetTransactionTypes/Detail',
        },
        // 资产事务处理类型创建
        {
          path: '/aafm/asset-transaction-type/create',
          component: '@/pages/AssetTransactionTypes/Detail',
        },
      ],
    },
    /**
     * 动态字段
     */
    {
      path: '/aafm/asset-dynamic-columns',
      // 动态字段列表
      routes: [
        {
          path: '/aafm/asset-dynamic-columns/list',
          component: '@/pages/AssetDynamicColumns',
        },
      ],
    },
    /**
     * 资产盘点类型
     */
    {
      path: '/actn/counting-type',
      routes: [
        // 资产盘点类型列表
        {
          path: '/actn/counting-type/list',
          models: ['@/models/countingType'],
          component: '@/pages/CountingType/List/CountingTypeListPage',
        },
        // 资产盘点类型创建
        {
          path: '/actn/counting-type/create',
          models: ['@/models/countingType'],
          component: '@/pages/CountingType/Detail/CountingTypeDetailPage',
        },
        // 资产盘点类型详情
        {
          path: '/actn/counting-type/detail/:id',
          models: ['@/models/countingType'],
          component: '@/pages/CountingType/Detail/CountingTypeDetailPage',
        },
      ],
    },
    /**
     * 盘点任务模板
     */
    {
      path: '/actn/counting-task-tmpls',
      routes: [
        // 盘点任务模板列表
        {
          path: '/actn/counting-task-tmpls/list',
          models: ['@/models/countingTaskTmpls'],
          component: '@/pages/CountingTaskTmpls/List/CountingTaskTmplsListPage',
        },
        // 盘点任务模板详情
        {
          path: '/actn/counting-task-tmpls/detail/:id',
          models: ['@/models/countingTaskTmpls'],
          component: '@/pages/CountingTaskTmpls/Detail/CountingTaskTmplsDetailPage',
        },
        // 盘点任务模板创建
        {
          path: '/actn/counting-task-tmpls/create',
          models: ['@/models/countingTaskTmpls'],
          component: '@/pages/CountingTaskTmpls/Detail/CountingTaskTmplsDetailPage',
        },
      ],
    },
    /**
     * 物料类别
     */
    {
      path: '/ammt/materials-category',
      routes: [
        // 物料类别列表
        {
          path: '/ammt/materials-category/list',
          models: ['@/models/materialsCategory'],
          component: '@/pages/MaterialsCategory',
        },
      ],
    },
    /**
     * 物料事务类型
     */
    {
      path: '/ammt/material_ts_type',
      routes: [
        // 物料事务类型列表
        {
          path: '/ammt/material_ts_type/list',
          models: ['@/models/materialTsType'],
          component: '@/pages/MaterialTsType',
        },
        // 物料事务类型创建
        {
          path: '/ammt/material_ts_type/create',
          models: ['@/models/materialTsType'],
          component: '@/pages/MaterialTsType/Detail',
        },
        // 物料事务类型详情
        {
          path: '/ammt/material_ts_type/detail/:id',
          models: ['@/models/materialTsType'],
          component: '@/pages/MaterialTsType/Detail',
        },
      ],
    },
    /**
     * 服务申请单类型
     */
    {
      path: '/amtc/sr-type',
      routes: [
        // 服务申请单类型列表
        {
          path: '/amtc/sr-type/list',
          models: ['@/models/srType'],
          component: '@/pages/SrType',
        },
        // 服务申请单类型创建
        {
          path: '/amtc/sr-type/create',
          models: ['@/models/srType'],
          component: '@/pages/SrType/Detail',
        },
        // 服务申请单类型详情
        {
          path: '/amtc/sr-type/detail/:id',
          models: ['@/models/srType'],
          component: '@/pages/SrType/Detail',
        },
        {
          path: '/amtc/sr-type/create-sub/:id',
          models: ['@/models/srType'],
          component: '@/pages/SrType/Detail',
        },
      ],
    },
    /**
     * 工单类型
     */
    {
      path: '/amtc/wo-type',
      routes: [
        // 工单类型列表
        {
          path: '/amtc/wo-type/list',
          models: ['@/models/woType'],
          component: '@/pages/WoType',
        },
        // 工单类型创建
        {
          path: '/amtc/wo-type/create',
          models: ['@/models/woType'],
          component: '@/pages/WoType/Detail',
        },
        // 工单类型详情
        {
          path: '/amtc/wo-type/detail/:id',
          models: ['@/models/woType', '@/models/inspectList'],
          component: '@/pages/WoType/Detail',
        },
        {
          path: '/amtc/wo-type/create-sub/:id',
          models: ['@/models/woType'],
          component: '@/pages/WoType/Detail',
        },
        {
          path: '/amtc/wo-type/:from/:parentId/detail/:id',
          models: ['@/models/inspectList'],
          component: '@/pages/InspectList/Detail',
        },
        {
          path: '/amtc/wo-type/:from/:parentId/create',
          models: ['@/models/inspectList'],
          component: '@/pages/InspectList/Detail',
        },
        {
          path: '/amtc/wo-type/:from/:parentId/create-sub/:parentChecklistId',
          models: ['@/models/inspectList'],
          component: '@/pages/InspectList/Detail',
        },
      ],
    },
    /**
     * 工单升级规则
     */
    {
      path: '/amtc/wo-upgrade-rule',
      routes: [
        // 工单处理升级规则列表
        {
          path: '/amtc/wo-upgrade-rule/list',
          models: ['@/models/woUpgradeRule'],
          component: '@/pages/WoUpgradeRule',
        },
        // 工单处理升级规则创建
        {
          path: '/amtc/wo-upgrade-rule/create',
          models: ['@/models/woUpgradeRule'],
          component: '@/pages/WoUpgradeRule/Detail',
        },
        // 工单处理升级规则编辑
        {
          path: '/amtc/wo-upgrade-rule/detail/:id',
          models: ['@/models/woUpgradeRule'],
          component: '@/pages/WoUpgradeRule/Detail',
        },
      ],
    },
    /**
     * 优先级
     */
    {
      path: '/amtc/priority',
      models: ['@/models/priority'],
      component: '@/pages/Priority',
    },
    /**
     * 验收模板
     */
    {
      path: '/amtc/evaluate-temp',
      routes: [
        {
          // 列表页面
          path: '/amtc/evaluate-temp/list',
          component: '@/pages/EvaluateTemp',
        },
        // 新建页面
        {
          path: '/amtc/evaluate-temp/create',
          component: '@/pages/EvaluateTemp/Detail',
        },
        // 明细页面
        {
          path: '/amtc/evaluate-temp/detail/:id',
          component: '@/pages/EvaluateTemp/Detail',
        },
      ],
    },
    /**
     *  仪表点类型
     */
    {
      path: '/amtr/meter-types',
      routes: [
        {
          path: '/amtr/meter-types/list',
          component: '@/pages/MeterTypes',
        },
        {
          path: '/amtr/meter-types/detail/:id',
          component: '@/pages/MeterTypes/Detail',
        },
        {
          path: '/amtr/meter-types/create',
          component: '@/pages/MeterTypes/Detail',
        },
      ],
    },
    /**
     * 委外申请类型
     */
    {
      path: '/amtc/sub-type',
      component: '@/pages/SubType',
    },
    /**
     * 项目类型
     */
    {
      path: '/appm/project-type',
      models: ['@/models/projectType'],
      component: '@/pages/ProjectType',
    },
    /**
     * 项目状态
     */
    {
      path: '/appm/project-status',
      models: ['@/models/projectStatus'],
      component: '@/pages/ProjectStatus',
    },
    /**
     * 项目角色
     */
    {
      path: '/appm/project-role',
      component: '@/pages/ProjectRole',
    },
    /**
     * 项目属性组
     */
    {
      path: '/appm/attribute-set',
      routes: [
        // 项目属性组列表
        {
          path: '/appm/attribute-set/list',
          models: ['@/models/proAttributeSet'],
          component: '@/pages/ProAttributeSet',
        },
        // 项目属性组创建
        {
          path: '/appm/attribute-set/create',
          models: ['@/models/proAttributeSet'],
          component: '@/pages/ProAttributeSet/Detail',
        },
        // 项目属性组详情
        {
          path: '/appm/attribute-set/detail/:id',
          models: ['@/models/proAttributeSet'],
          component: '@/pages/ProAttributeSet/Detail',
        },
      ],
    },
    /**
     * 采购订单类型
     */
    {
      path: '/aori/po-type',
      routes: [
        // 采购订单类型列表
        {
          path: '/aori/po-type/list',
          component: '@/pages/POType',
        },
        // 采购订单类型详情
        {
          path: '/aori/po-type/detail/:id/:num',
          component: '@/pages/POType/Detail',
        },
        // 采购订单类型新建
        {
          path: '/aori/po-type/create',
          component: '@/pages/POType/Detail',
        },
      ],
    },
    /**
     * 验收单类型
     */
    {
      path: '/arcv/acceptance-type',
      // 验收单类型列表
      routes: [
        {
          path: '/arcv/acceptance-type/list',
          component: '@/pages/AcceptanceType',
        },
        // 验收单类型详情
        {
          path: '/arcv/acceptance-type/detail/:acceptanceTypeId',
          component: '@/pages/AcceptanceType/Detail',
        },
        // 验收单类型创建
        {
          path: '/arcv/acceptance-type/create',
          component: '@/pages/AcceptanceType/Detail',
        },
      ],
    },
    /**
     * 验收方式
     */
    {
      path: '/arcv/acceptance-mode',
      // 验收方式列表
      routes: [
        {
          path: '/arcv/acceptance-mode/list',
          component: '@/pages/AcceptanceMode',
        },
        // 验收方式详情
        {
          path: '/arcv/acceptance-mode/detail/:acceptanceWayId',
          component: '@/pages/AcceptanceMode/Detail',
        },
        // 验收方式创建
        {
          path: '/arcv/acceptance-mode/create',
          component: '@/pages/AcceptanceMode/Detail',
        },
      ],
    },
    /**
     * 消息通知配置
     */
    {
      path: '/amtc/msg-setting',
      routes: [
        {
          path: '/amtc/msg-setting/list',
          component: '@/pages/MsgSetting',
        },
        {
          path: '/amtc/msg-setting/detail/:id',
          component: '@/pages/MsgSetting/Detail',
        },
        {
          path: '/amtc/msg-setting/create',
          component: '@/pages/MsgSetting/Detail',
        },
      ],
    },
    /**
     * 采购申请类型
     */
    {
      path: '/aori/pr-type',
      routes: [
        {
          path: '/aori/pr-type/list',
          component: '@/pages/PRType',
        },
        {
          path: '/aori/pr-type/create',
          component: '@/pages/PRType/Detail',
        },
        {
          path: '/aori/pr-type/detail/:id',
          component: '@/pages/PRType/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-主数据  =================>
     */
    /**
     * 合作伙伴
     */
    {
      path: '/amdm/partner',
      routes: [
        // 列表
        {
          path: '/amdm/partner/list',
          component: '@/pages/Partner/List/PartnerListPage',
        },
        // 创建
        {
          path: '/amdm/partner/create',
          component: '@/pages/Partner/Detail/PartnerDetailPage',
        },
        // 详情
        {
          path: '/amdm/partner/detail/:id',
          component: '@/pages/Partner/Detail/PartnerDetailPage',
        },
      ],
    },
    /**
     * 位置
     */
    {
      path: '/amdm/location',
      routes: [
        // 位置列表
        {
          path: '/amdm/location/list',
          models: ['@/models/frameWork'],
          component: '@/pages/Location',
        },
        // 位置创建
        {
          path: '/amdm/location/create',
          models: ['@/models/frameWork'],
          component: '@/pages/Location/Detail',
        },
        // 位置详情
        {
          path: '/amdm/location/detail/:id/:flag',
          models: ['@/models/frameWork'],
          component: '@/pages/Location/Detail',
        },
        {
          path: '/amdm/location/create-sub',
          models: ['@/models/frameWork'],
          component: '@/pages/Location/Detail',
        },
      ],
    },
    /**
     * 工作中心
     */
    {
      path: '/amtc/work-center',
      routes: [
        // 工作中心列表
        {
          path: '/amtc/work-center/list',
          models: ['@/models/workCenter'],
          component: '@/pages/WorkCenter',
        },
        // 工作中心创建
        {
          path: '/amtc/work-center/create',
          models: ['@/models/workCenter', '@/models/frameWork'],
          component: '@/pages/WorkCenter/Detail',
        },
        // 工作中心详情
        {
          path: '/amtc/work-center/detail/:id',
          models: ['@/models/workCenter', '@/models/frameWork'],
          component: '@/pages/WorkCenter/Detail',
        },
      ],
    },
    /**
     * 标准作业
     */
    {
      path: '/amtc/act',
      routes: [
        // 标准作业列表
        {
          path: '/amtc/act/list',
          models: ['@/models/frameWork'],
          component: '@/pages/Act',
        },
        // 导入
        {
          authorized: true,
          path: '/ammt/act/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
        // 标准作业创建
        {
          path: '/amtc/act/create',
          models: ['@/models/frameWork'],
          component: '@/pages/Act/Detail',
        },
        // 标准作业详情
        {
          path: '/amtc/act/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/Act/Detail',
        },
      ],
    },
    /**
     * 故障体系
     */
    {
      path: '/rc/rc-systems',
      routes: [
        // 故障缺陷列表
        {
          path: '/rc/rc-systems/list',
          models: ['@/models/rcSystems'],
          component: '@/pages/RcSystems',
        },
        // 故障缺陷创建
        {
          path: '/rc/rc-systems/create',
          models: ['@/models/rcSystems'],
          component: '@/pages/RcSystems/Detail',
        },
        // 故障缺陷详情
        {
          path: '/rc/rc-systems/detail/:id',
          models: ['@/models/rcSystems'],
          component: '@/pages/RcSystems/Detail',
        },
      ],
    },
    /**
     * 故障字典
     */
    {
      path: '/rc/rc-assesment',
      routes: [
        // 故障缺陷评估项列表
        {
          path: '/rc/rc-assesment/list',
          models: ['@/models/rcAssesment'],
          component: '@/pages/RcAssesment',
        },
        // 故障缺陷评估项创建
        {
          path: '/rc/rc-assesment/create',
          models: ['@/models/rcAssesment'],
          component: '@/pages/RcAssesment/Detail',
        },
        // 故障缺陷评估项详情
        {
          path: '/rc/rc-assesment/detail/:id',
          models: ['@/models/rcAssesment'],
          component: '@/pages/RcAssesment/Detail',
        },
      ],
    },
    // Mttr
    {
      authorized: true,
      path: '/aori/production-shutdown/mttr',
      key: '/aori/production-shutdown/mttr',
      models: ['@/models/frameWork'],
      component: '@/pages/ProductionShutdown/Mttr',
    },
    // Mtbf
    {
      authorized: true,
      path: '/aori/production-shutdown/mtbf',
      key: '/aori/production-shutdown/mtbf',
      models: ['@/models/frameWork'],
      component: '@/pages/ProductionShutdown/Mtbf',
    },
    /**
     * <==============   ALM-设备/资产  =================>
     */
    /**
     * 资产报表
     */
    {
      path: '/aori/asset-report',
      component: '@/pages/AssetReport',
    },
    /**
     * 闲置资产库
     */
    {
      path: '/aori/idle-assets',
      component: '@/pages/IdleAsset',
    },
    /**
     * 资产工作台
     */
    {
      path: '/aatn/asset-work-bench',
      // 动态字段列表
      routes: [
        {
          path: '/aatn/asset-work-bench/list',
          component: '@/pages/AssetWorkBench',
        },
        {
          path: '/aatn/asset-work-bench/detail/:id',
          component: '@/pages/AssetWorkBench/Detail',
        },
        {
          path: '/aatn/asset-work-bench/custom-inventory',
          component: '@/pages/AssetWorkBench/CustomInventory',
        },
      ],
    },
    /**
     * 设备/资产
     */
    {
      path: '/aafm/equipment-asset',
      // 设备/资产列表
      routes: [
        {
          path: '/aafm/equipment-asset/list',
          models: ['@/models/frameWork'],
          component: '@/pages/EquipmentAsset/List',
        },
        // 设备/资产详情
        {
          path: '/aafm/equipment-asset/detail/:assetId',
          models: ['@/models/equipmentAsset', '@/models/frameWork'],
          component: '@/pages/EquipmentAsset/Detail',
        },
        // 设备/资产创建
        {
          path: '/aafm/equipment-asset/create',
          models: ['@/models/equipmentAsset', '@/models/frameWork'],
          component: '@/pages/EquipmentAsset/Detail',
        },
        // 设备/资产导入
        {
          authorized: true,
          path: '/aafm/equipment-asset/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * BOM结构清单
     */
    {
      path: '/amtc/bom',
      routes: [
        // BOM结构清单列表
        {
          path: '/amtc/bom/list',
          models: ['@/models/bom'],
          component: '@/pages/Bom/List',
        },
        // BOM结构清单创建
        {
          path: '/amtc/bom/create',
          models: ['@/models/bom'],
          component: '@/pages/Bom/Detail',
        },
        // BOM结构清单详情
        {
          path: '/amtc/bom/detail/:id',
          models: ['@/models/bom'],
          component: '@/pages/Bom/Detail',
        },
        // BOM结构清单详情
        {
          path: '/amtc/bom/createChild/:parentId/:parentName',
          models: ['@/models/bom'],
          component: '@/pages/Bom/Detail',
        },
        // 导入BOM结构清单
        {
          authorized: true,
          path: '/amtc/bom/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 设备/资产变更记录
     */
    {
      path: '/aafm/change-logs',
      component: '@/pages/ChangeLogs',
    },
    /**
     * 导览工作台
     */
    {
      path: '/agwb/guide-workbench',
      component: '@/pages/GuidedWorkbench',
    },
    /**
     * 定检工作台
     */
    {
      path: '/aori/regular-check-workbench',
      priority: 10,
      routes: [
        // 定检工作台列表
        {
          path: '/aori/regular-check-workbench/list',
          component: '@/pages/RegularCheckWorkbench',
          models: ['@/models/frameWork'],
          priority: 10,
        },
        // 定检工作台结果更新
        {
          path: '/aori/regular-check-workbench/results-update',
          component: '@/pages/RegularCheckWorkbench/ResultsUpdate',
          priority: 10,
        },
        // 定检工作台批量更新
        {
          path: '/aori/regular-check-workbench/batch-update',
          component: '@/pages/RegularCheckWorkbench/BatchUpdate',
          priority: 10,
        },
        // 定检更新结果查看
        {
          path: '/aori/regular-check-workbench/results-view',
          component: '@/pages/RegularCheckWorkbench/ResultsView',
          priority: 10,
        },
      ],
    },
    // 定检通知
    {
      path: '/aori/regular-check-notice',
      routes: [
        {
          path: '/aori/regular-check-notice/list',
          component: '@/pages/RegularCheckNotice',
        },
      ],
    },
    /**
     * 标准资产
     */
    {
      path: '/aori/standard-asset',
      // 标准资产列表
      routes: [
        {
          path: '/aori/standard-asset/list',
          component: '@/pages/StandardAsset',
        },
        // 标准资产详情
        {
          path: '/aori/standard-asset/detail/:id',
          component: '@/pages/StandardAsset/Detail',
        },
        // 标准资产创建
        {
          path: '/aori/standard-asset/create',
          component: '@/pages/StandardAsset/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/aori/standard-asset/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    // 工艺装备
    {
      path: '/aori/tooling',
      routes: [
        {
          path: '/aori/tooling/list',
          component: '@/pages/Tooling',
        },
        {
          path: '/aori/tooling/detail/:id',
          component: '@/pages/Tooling/Detail',
        },
        {
          path: '/aori/tooling/create',
          component: '@/pages/Tooling/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/aori/tooling/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    // 设备出入库平台
    {
      path: '/aori/eqp-storage-platform',
      // title: '设备出入库平台',
      // authorized: true,
      component: '@/pages/EqpStoragePlatform',
    },
    // /**
    //  * <==============   ALM-资产验收  =================>
    //  */

    // /**
    //  * 采购订单
    //  */
    {
      path: '/aori/purchase-order',
      routes: [
        // 采购订单列表
        {
          path: '/aori/purchase-order/list',
          component: '@/pages/PurchaseOrder',
        },
        // 采购订单创建
        {
          path: '/aori/purchase-order/create',
          component: '@/pages/PurchaseOrder/Detail',
        },
        // 采购订单详情
        {
          path: '/aori/purchase-order/detail/:id/:num',
          component: '@/pages/PurchaseOrder/Detail',
        },
      ],
    },
    /**
     * 交付清单行
     */
    {
      path: '/arcv/delivery-list',
      // 交付清单行列表
      routes: [
        {
          path: '/arcv/delivery-list/list',
          component: '@/pages/DeliveryList',
        },
        // 交付清单行详情
        {
          path: '/arcv/delivery-list/detail/:id',
          component: '@/pages/DeliveryList/Detail',
        },
        // 交付清单行创建
        {
          path: '/arcv/delivery-list/create',
          component: '@/pages/DeliveryList/Detail',
        },
      ],
    },
    /**
     * 验收单
     */
    {
      path: '/arcv/acceptance',
      // 验收单列表
      routes: [
        {
          path: '/arcv/acceptance/list',
          component: '@/pages/Acceptance',
        },
        // 验收单详情
        {
          path: '/arcv/acceptance/detail/:id',
          component: '@/pages/Acceptance/Detail',
        },
        // 验收单创建
        {
          path: '/arcv/acceptance/create',
          component: '@/pages/Acceptance/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-资产跟踪  =================>
     */
    /**
     * 五种资产事务处理-新   资产处置
     */
    {
      path: '/aatn/asset-transaction-basic-type/dispose',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/dispose/list',
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/dispose/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/dispose/create',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/dispose/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/dispose/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactionBasicType/Detail',
    },
    /**
     * 五种资产事务处理-新   资产变更
     */
    {
      path: '/aatn/asset-transaction-basic-type/change',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/change/list',
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/change/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/change/create',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/change/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/change/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactionBasicType/Detail',
    },
    /**
     * 五种资产事务处理-新   资产借出归还
     */
    {
      path: '/aatn/asset-transaction-basic-type/handover',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/handover/list',
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/handover/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/handover/create',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    // 资产领用
    {
      path: '/aatn/asset-transaction-basic-type/receipt',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/receipt/list',
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/receipt/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/receipt/create',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/handover/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/handover/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactionBasicType/Detail',
    },
    /**
     * 五种资产事务处理-新   资产报废
     */
    {
      path: '/aatn/asset-transaction-basic-type/scrap',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/scrap/list',
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/scrap/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/scrap/create',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/scrap/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/scrap/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactionBasicType/Detail',
    },
    /**
     * 五种资产事务处理-新   资产调拨
     */
    {
      path: '/aatn/asset-transaction-basic-type/transfer',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/transfer/list',
          component: '@/pages/AssetTransactions/Transfer/List',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/transfer/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactions/Transfer/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/transfer/create',
          component: '@/pages/AssetTransactions/Transfer/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/transfer/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactions/Transfer/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/transfer/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactions/Transfer/Detail',
    },
    /**
     * 资产闲置
     */
    {
      path: '/aatn/asset-transaction-basic-type/idle',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/idle/list',
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/idle/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/idle/create',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/idle/detail/:tsTypeId/:id',
      models: [],
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },

    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/idle/detail/:tsTypeId/:id',
      component: '@/pages/AssetTransactionBasicType/Detail',
    },

    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/dispose',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/dispose/list',
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/dispose/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/change',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/change/list',
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/change/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/handover',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/handover/list',
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/handover/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/scrap',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/scrap/list',
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/scrap/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/transfer',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/transfer/list',
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/transfer/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactions/Transfer/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/idle',
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/idle/list',
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/idle/detail/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },

    // 资产事务-审批表单
    {
      authorized: true,
      path: '/pub/asset-transaction',
      key: '/pub/asset-transaction',
      routes: [
        // 调拨转移审批表单
        {
          authorized: true,
          path: '/pub/asset-transaction/transfer/:id',
          component: '@/pages/AssetTransactions/Transfer/ApprovalForm',
        },
        {
          authorized: true,
          path: '/pub/asset-transaction/:tsTypeId/:id',
          component: '@/pages/AssetTransactionBasicType/ApprovalForm',
        },
      ],
    },
    /**
     * <==============   ALM-资产盘点  =================>
     */
    /**
     * 资产盘点计划
     */
    {
      path: '/actn/counting-batchs',
      authorized: true,
      routes: [
        {
          path: '/actn/counting-batchs/list',
          component: '@/pages/CountingBatch',
        },
        {
          path: '/actn/counting-batchs/create',
          component: '@/pages/CountingBatch/Detail',
        },
        {
          path: '/actn/counting-batchs/detail/:id',
          component: '@/pages/CountingBatch/Detail',
        },
      ],
    },
    /**
     * 盘点任务
     */
    {
      path: '/actn/counting-tasks',
      routes: [
        // 盘点任务列表
        {
          path: '/actn/counting-tasks/list',
          models: ['@/models/countingTasks'],
          component: '@/pages/CountingTask',
        },
        // 盘点任务详情
        {
          path: '/actn/counting-tasks/detail/:id',
          models: ['@/models/countingTasks', '@/models/countingLines'],
          component: '@/pages/CountingTask/Detail',
        },
        // 盘点任务创建
        {
          path: '/actn/counting-tasks/create',
          models: ['@/models/countingTasks'],
          component: '@/pages/CountingTask/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-固定资产  =================>
     */
    /**
     * 固定资产
     */
    {
      path: '/afam/fixed-assets',
      // 固定资产列表
      routes: [
        {
          path: '/afam/fixed-assets/list',
          component: '@/pages/FixedAssets',
        },
        // 固定资产详情
        {
          path: '/afam/fixed-assets/detail/:id',
          component: '@/pages/FixedAssets/Detail',
        },
        // 固定资产创建
        {
          path: '/afam/fixed-assets/create',
          component: '@/pages/FixedAssets/Detail',
        },
      ],
    },
    /**
     * 固定资产账簿
     */
    {
      path: '/aafm/fa-account-book',
      routes: [
        // 固定资产账簿列表
        {
          path: '/aafm/fa-account-book/list',
          models: ['@/models/faAccountBook'],
          component: '@/pages/FaAccountBook',
        },
        // 新建
        {
          path: '/aafm/fa-account-book/create',
          models: ['@/models/faAccountBook'],
          component: '@/pages/FaAccountBook/Detail',
        },
        // 详情查询id
        {
          path: '/aafm/fa-account-book/detail/:id',
          models: ['@/models/faAccountBook'],
          component: '@/pages/FaAccountBook/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-维修/维保  =================>
     */
    /**
     * 计划调度中心
     */
    {
      path: '/aori/schedule-center',
      models: ['@/models/frameWork'],
      component: '@/pages/ScheduleCenter',
    },
    /**
     * 服务中心
     */
    {
      path: '/amtc/service-center',
      routes: [
        // 服务中心列表
        {
          path: '/amtc/service-center/list',
          models: ['@/models/serviceApply'],
          component: '@/pages/ServiceCenter',
        },
        // 服务中心创建
        {
          path: '/amtc/service-center/create',
          models: ['@/models/serviceApply', '@/models/frameWork', '@/models/woMalfunction'],
          component: '@/pages/ServiceCenter/Detail',
        },
        // 服务中心详情
        {
          path: '/amtc/service-center/Detail/:srId',
          models: ['@/models/serviceApply', '@/models/frameWork', '@/models/woMalfunction'],
          component: '@/pages/ServiceCenter/Detail',
        },
      ],
    },
    /**
     * 我的服务申请
     */
    {
      path: '/amtc/service-apply-current',
      routes: [
        // 我的服务申请列表
        {
          path: '/amtc/service-apply-current/list',
          component: '@/pages/ServiceApplyCurrent',
        },
        // 我的服务申请新建
        {
          path: '/amtc/service-apply-current/create',
          models: ['@/models/serviceApply', '@/models/woMalfunction', '@/models/frameWork'],
          component: '@/pages/ServiceApplyCurrent/Detail',
        },
        // 我的服务申请编辑
        {
          path: '/amtc/service-apply-current/detail/:srId',
          models: ['@/models/serviceApply', '@/models/woMalfunction', '@/models/frameWork'],
          component: '@/pages/ServiceApplyCurrent/Detail',
        },
      ],
    },
    // 服务申请单-审批表单
    {
      authorized: true,
      path: '/pub/service-apply',
      key: '/pub/service-apply',
      routes: [
        {
          authorized: true,
          path: '/pub/service-apply/:id',
          component: '@/pages/ServiceApplyCurrent/ApprovalForm',
        },
      ],
    },
    /**
     * 工作单
     */
    {
      path: '/amtc/work-order',
      routes: [
        // 工作单列表
        {
          path: '/amtc/work-order/list',
          models: ['@/models/frameWork'],
          component: '@/pages/WorkOrder',
        },
        // 工作单创建
        {
          path: '/amtc/work-order/create',
          models: ['@/models/frameWork'],
          component: '@/pages/WorkOrder/Detail',
        },
        // 工作单详情
        {
          path: '/amtc/work-order/detail/:woId/:woNum',
          models: ['@/models/frameWork'],
          component: '@/pages/WorkOrder/Detail',
        },
      ],
    },
    // 工作单-审批表单
    {
      authorized: true,
      path: '/pub/workorder',
      key: '/pub/workorder',
      routes: [
        {
          authorized: true,
          path: '/pub/workorder/:woId',
          models: ['@/models/frameWork'],
          component: '@/pages/WorkOrder/ApprovalForm',
        },
      ],
    },
    /**
     * 委外申请
     */
    {
      path: '/amtc/sub-requisition',
      routes: [
        // 委外申请列表
        {
          path: '/amtc/sub-requisition/list',
          models: ['@/models/frameWork'],
          component: '@/pages/SubRequisition',
        },
        // 委外申请新建
        {
          path: '/amtc/sub-requisition/create',
          models: [
            '@/models/frameWork',
            '@/models/serviceApply',
            '@/models/equipmentAsset',
            '@/models/location',
          ],
          component: '@/pages/SubRequisition/Detail',
        },
        // 委外申请详情
        {
          path: '/amtc/sub-requisition/Detail/:woId',
          models: ['@/models/frameWork', '@/models/serviceApply'],
          component: '@/pages/SubRequisition/Detail',
        },
      ],
    },
    // 委外申请-审批表单
    {
      authorized: true,
      path: '/pub/sub-requisition',
      key: '/pub/sub-requisition',
      routes: [
        {
          authorized: true,
          path: '/pub/sub-requisition/:id',
          models: ['@/models/frameWork', '@/models/serviceApply'],
          component: '@/pages/SubRequisition/ApprovalForm',
        },
      ],
    },
    /**
     *  仪表点
     */
    {
      path: '/amtr/meters',
      routes: [
        {
          path: '/amtr/meters/list',
          component: '@/pages/Meters',
        },
        {
          path: '/amtr/meters/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/Meters/Detail',
        },
        {
          path: '/amtr/meters/create',
          models: ['@/models/frameWork'],
          component: '@/pages/Meters/Detail',
        },
        // 导入读数
        {
          authorized: true,
          path: '/amtr/meters/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 告警工作台
     */
    {
      path: '/aori/alarm-record',
      routes: [
        {
          path: '/aori/alarm-record/list',
          component: '@/pages/AlarmRecord',
        },
      ],
    },
    /**
     * 维保作业项
     */
    {
      path: '/amtc/maintain-operation',
      routes: [
        // 维保作业项列表
        {
          path: '/amtc/maintain-operation/list',
          component: '@/pages/MaintainOperation/List',
        },
        // 创建
        {
          path: '/amtc/maintain-operation/create',
          models: ['@/models/frameWork'],
          component: '@/pages/MaintainOperation/Detail',
        },
        // 详情
        {
          path: '/amtc/maintain-operation/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/MaintainOperation/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/amtc/maintain-operation/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 维保规则
     */
    {
      path: '/amtc/strategy-rule',
      routes: [
        // 策略规则列表
        {
          path: '/amtc/strategy-rule/list',
          component: '@/pages/MaintainRule/List',
        },
        // 策略规则创建
        {
          path: '/amtc/strategy-rule/create',
          component: '@/pages/MaintainRule/Detail',
        },
        // 策略规则详情
        {
          path: '/amtc/strategy-rule/detail/:id',
          component: '@/pages/MaintainRule/Detail',
        },
        // 策略规则导入
        {
          authorized: true,
          path: '/amtc/strategy-rule/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 维保周期
     */
    {
      path: '/amtc/maintain-cycle',
      routes: [
        // 点巡检预测列表
        {
          path: '/amtc/maintain-cycle/list',
          component: '@/pages/MaintainCycle/List',
        },
        // 创建
        {
          path: '/amtc/maintain-cycle/create',
          component: '@/pages/MaintainCycle/Detail',
        },
        // 详情
        {
          path: '/amtc/maintain-cycle/detail/:id',
          component: '@/pages/MaintainCycle/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/amtc/maintain-cycle/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 点巡检预测
     */
    {
      path: '/amtc/pmf',
      routes: [
        // 点巡检预测列表
        {
          path: '/amtc/pmf/list',
          component: '@/pages/PMForecasts',
        },
        // 创建
        {
          path: '/amtc/pmf/create',
          component: '@/pages/PMForecasts/Detail',
        },
        // 详情
        {
          path: '/amtc/pmf/detail/:id',
          component: '@/pages/PMForecasts/Detail',
        },
      ],
    },
    /**
     * 维保计划
     */
    {
      path: '/aori/maintain-plans',
      routes: [
        {
          path: '/aori/maintain-plans/list',
          component: '@/pages/MaintainPlans',
        },
        {
          path: '/aori/maintain-plans/create',
          models: ['@/models/frameWork'],
          component: '@/pages/MaintainPlans/Detail',
        },
        {
          path: '/aori/maintain-plans/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/MaintainPlans/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/aori/maintain-plans/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 计划预测
     */
    {
      path: '/aori/plan-sched',
      routes: [
        {
          path: '/aori/plan-sched/list',
          component: '@/pages/PlanSched',
        },
        {
          path: '/aori/plan-sched/create',
          component: '@/pages/PlanSched/Detail',
        },
        {
          path: '/aori/plan-sched/detail/:id',
          component: '@/pages/PlanSched/Detail',
        },
      ],
    },
    /**
     * 策略场景切换
     */
    {
      path: '/amtc/policy-scene-switch',
      routes: [
        // 策略场景切换列表
        {
          path: '/amtc/policy-scene-switch/list',
          component: '@/pages/PolicySceneSwitch',
        },
        // 历史查询
        {
          path: '/amtc/policy-scene-switch/history',
          component: '@/pages/PolicySceneSwitch/History',
        },
        // 详情查询id
        {
          path: '/amtc/policy-scene-switch/detail/:id',
          component: '@/pages/PolicySceneSwitch/Detail',
        },
        // 详情查询
        {
          path: '/amtc/policy-scene-switch/detail/query',
          component: '@/pages/PolicySceneSwitch/Detail',
        },
      ],
    },
    /**
     * 维保策略场景
     */
    {
      path: '/amtc/strategies-scene',
      models: ['@/models/strategiesScene'],
      component: '@/pages/StrategiesScene',
    },
    /**
     * <==============   ALM-库存物料  =================>
     */
    /**
     * 仓库工作台
     */
    {
      path: '/ammt/material_ts_Multi',
      routes: [
        // 仓库工作台创建
        {
          path: '/ammt/material_ts_Multi/create',
          models: ['@/models/materialTsMulti'],
          component: '@/pages/MaterialTsMulti/Create',
        },
        // 仓库工作台列表
        {
          path: '/ammt/material_ts_Multi/list',
          models: ['@/models/materialTsMulti'],
          component: '@/pages/MaterialTsMulti/List',
        },
        // 物料凭证详情
        {
          path: '/ammt/material_ts_Multi/detail/:id',
          models: ['@/models/materialTsMulti', '@/models/materialTsType'],
          component: '@/pages/MaterialTsMulti/Detail',
        },
      ],
    },
    /**
     * 库存历史变动
     */
    {
      path: '/ammt/trans-history',
      routes: [
        // 库存历史变动列表
        {
          path: '/ammt/trans-history/list',
          models: ['@/models/transHistory'],
          component: '@/pages/TransHistory',
        },
      ],
    },
    /**
     * 库存现有量
     */
    {
      path: '/ammt/onhand-quantity',
      routes: [
        // 现有量列表
        {
          path: '/ammt/onhand-quantity/list',
          component: '@/pages/OnHandQuantity',
        },
      ],
    },
    /**
     * 物料
     */
    {
      path: '/ammt/materials',
      routes: [
        // 物料列表
        {
          path: '/ammt/materials/list',
          component: '@/pages/Materials',
        },
        // 物料创建
        {
          path: '/ammt/materials/create',
          component: '@/pages/Materials/Detail',
        },
        // 物料详情
        {
          path: '/ammt/materials/detail/:id',
          component: '@/pages/Materials/Detail',
        },
        // 物料导入
        {
          authorized: true,
          path: '/ammt/materials/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 物料申请单
     */
    {
      path: '/ammt/item-requisition',
      routes: [
        // 物料申请单列表
        {
          path: '/ammt/item-requisition/list',
          component: '@/pages/ItemRequisition',
        },
        // 物料申请单创建
        {
          path: '/ammt/item-requisition/create',
          component: '@/pages/ItemRequisition/Detail',
        },
        // 物料申请单详情
        {
          path: '/ammt/item-requisition/detail/:id',
          component: '@/pages/ItemRequisition/Detail',
        },
      ],
    },
    // 物料申请单-审批表单
    {
      authorized: true,
      path: '/pub/item-requisition',
      key: '/pub/item-requisition',
      routes: [
        {
          authorized: true,
          path: '/pub/item-requisition/:id',
          component: '@/pages/ItemRequisition/ApprovalForm',
        },
      ],
    },
    /**
     * 物料采购建议
     */
    {
      path: '/amtc/material-purchase-adv',
      routes: [
        {
          path: '/amtc/material-purchase-adv/list',
          component: '@/pages/MaterialPurchaseAdv',
        },
      ],
    },
    /**
     * <==============   ALM-项目管理  =================>
     */
    /**
     * 项目工作台
     */
    {
      path: '/appm/pro-basic-info',
      routes: [
        // 项目基础信息列表
        {
          path: '/appm/pro-basic-info/list',
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo',
        },
        // 项目基础信息创建
        {
          path: '/appm/pro-basic-info/create',
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo/Detail',
        },
        // 项目基础信息创建-复制自项目
        {
          path: '/appm/pro-basic-info/create-from/project/:id',
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo/Detail',
        },
        // 项目基础信息创建-复制自模板
        {
          path: '/appm/pro-basic-info/template/project/:id',
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo/Detail',
        },
        // 项目基础信息详情
        {
          path: '/appm/pro-basic-info/detail/:id',
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo/Detail',
        },
        // wbs计划维护
        {
          path: '/appm/pro-basic-info/wbs/:wbsHeaderId',
          models: ['@/models/wbsPlanMaintain'],
          component: '@/pages/WBSPlanMaintain',
        },
      ],
    },
    /**
     * 项目进度
     */
    {
      path: '/appm/project-schedule',
      models: ['@/models/projectSchedule'],
      component: '@/pages/ProjectSchedule',
    },
    /**
     * 项目模板
     */
    {
      path: '/appm/project-template',
      routes: [
        // 项目模板列表
        {
          path: '/appm/project-template/list',
          models: ['@/models/projectTemplate'],
          component: '@/pages/ProjectTemplate',
        },
        {
          path: '/appm/project-template/detail/:id',
          models: ['@/models/projectTemplate'],
          component: '@/pages/ProjectTemplate/Detail',
        },
        {
          path: '/appm/project-template/new-detail/:id',
          models: ['@/models/projectTemplate'],
          component: '@/pages/ProjectTemplate/Detail',
        },
        {
          path: '/appm/project-template/create',
          models: ['@/models/projectTemplate'],
          component: '@/pages/ProjectTemplate/Detail',
        },
        // WBS结构模板
        {
          path: '/appm/project-template/task/:proTemplateId',
          models: ['@/models/taskTemplate'],
          component: '@/pages/TaskTemplate',
        },
      ],
    },
    /**
     * <==============   ALM-服务协议  =================>
     */
    // 服务协议类型
    {
      path: '/amtc/service-agreemt-type',
      routes: [
        {
          path: '/amtc/service-agreemt-type/list',
          component: '@/pages/ServiceAgreemtType',
        },
        {
          path: '/amtc/service-agreemt-type/create',
          component: '@/pages/ServiceAgreemtType/Detail',
        },
        {
          path: '/amtc/service-agreemt-type/detail/:agreementTypeId',
          component: '@/pages/ServiceAgreemtType/Detail',
        },
      ],
    },
    // 服务协议
    {
      path: '/amtc/service-agreemt',
      routes: [
        {
          path: '/amtc/service-agreemt/list',
          component: '@/pages/ServiceAgreemt',
        },
        {
          path: '/amtc/service-agreemt/create',
          component: '@/pages/ServiceAgreemt/Detail',
        },
        {
          path: '/amtc/service-agreemt/detail/:serviceAgreementId',
          component: '@/pages/ServiceAgreemt/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-点巡检  =================>
     */
    /**
     * 路线
     */
    {
      path: '/aori/route',
      routes: [
        {
          path: '/aori/route/list',
          component: '@/pages/Route',
        },
        {
          path: '/aori/route/create',
          component: '@/pages/Route/Detail',
        },
        {
          path: '/aori/route/detail/:id',
          component: '@/pages/Route/Detail',
        },
      ],
    },
    /**
     * 点位
     */
    {
      path: '/aori/point-info',
      routes: [
        {
          path: '/aori/point-info',
          component: '@/pages/PointInfo',
        },
        {
          authorized: true,
          path: '/aori/point/data-import/:code',
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 标准检查组
     */
    {
      path: '/aori/checklist-group',
      routes: [
        {
          path: '/aori/checklist-group/list',
          component: '@/pages/ChecklistGroup',
        },
        {
          path: '/aori/checklist-group/create',
          component: '@/pages/ChecklistGroup/Detail',
        },
        {
          path: '/aori/checklist-group/detail/:id',
          component: '@/pages/ChecklistGroup/Detail',
        },
      ],
    },
    /**
     * 点巡检类型
     */
    {
      path: '/aori/psc-type',
      routes: [
        // 点巡检类型列表
        {
          path: '/aori/psc-type/list',
          models: ['@/models/woType'],
          component: '@/pages/PscType',
        },
        // 点巡检类型创建
        {
          path: '/aori/psc-type/create',
          models: ['@/models/woType'],
          component: '@/pages/PscType/Detail',
        },
        // 点巡检类型详情
        {
          path: '/aori/psc-type/detail/:id',
          models: ['@/models/woType', '@/models/inspectList'],
          component: '@/pages/PscType/Detail',
        },
      ],
    },
    /**
     * 点巡检单
     */
    {
      path: '/aori/patrol-spot-check',
      routes: [
        {
          path: '/aori/patrol-spot-check/list',
          models: ['@/models/frameWork'],
          component: '@/pages/PatrolSpotCheck',
        },
        {
          path: '/aori/patrol-spot-check/create',
          models: ['@/models/frameWork'],
          component: '@/pages/PatrolSpotCheck/Detail',
        },
        {
          path: '/aori/patrol-spot-check/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/PatrolSpotCheck/Detail',
        },
      ],
    },
    /**
     * 点巡检计划
     */
    {
      path: '/aori/psc-plan',
      routes: [
        {
          path: '/aori/psc-plan/list',
          component: '@/pages/PscPlan',
        },
        {
          path: '/aori/psc-plan/create',
          models: ['@/models/frameWork'],
          component: '@/pages/PscPlan/Detail',
        },
        {
          path: '/aori/psc-plan/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/PscPlan/Detail',
        },
      ],
    },

    /**
     * <==============   ALM-采购申请  =================>
     */
    /**
     * 采购申请
     */
    {
      path: '/aori/pr',
      routes: [
        {
          path: '/aori/pr/list',
          models: ['@/models/frameWork'],
          component: '@/pages/PROrder',
        },
        {
          path: '/aori/pr/create',
          models: ['@/models/frameWork'],
          component: '@/pages/PROrder/Detail',
        },
        {
          path: '/aori/pr/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/PROrder/Detail',
        },
      ],
    },
    // 采购申请-审批表单
    {
      authorized: true,
      path: '/pub/pr',
      key: '/pub//pr',
      routes: [
        {
          authorized: true,
          path: '/pub/pr/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/PROrder/ApprovalForm',
        },
      ],
    },
    /**
     * 权限
     */
    {
      path: '/aori/permission-config',
      routes: [
        {
          path: '/aori/permission-config/list',
          component: '@/pages/Permission',
        },
        {
          path: '/aori/permission-config/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/Permission/Detail',
        },
      ],
    },
    /**
     * 权限检查
     */
    {
      path: '/aori/permission-check',
      component: '@/pages/PermissionCheck',
    },
    // 安灯故障字典
    {
      path: '/aori/andon-rc-assesment',
      priority: 10,
      routes: [
        // 安灯故障字典列表
        {
          path: '/aori/andon-rc-assesment/list',
          component: '@/pages/AndonRcAssesment',
          priority: 10,
        },
      ],
    },
  ],

  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          // components: 'hzero-front/lib/components',
          // utils: 'hzero-front/lib/utils',
          // services: 'hzero-front/lib/services',
          // '@': './src',
          alm: './src',
          // halmComponents: 'halm-front/lib/components',
          // halmUtils: 'halm-front/lib/utils',
          // halmServices: 'halm-front/lib/services',
        },
      },
    ],
  ],

  presets: [
    // require.resolve('@hzerojs/preset-hzero'),根目录下已经配置过了
  ],
  hzeroMicro: {
    microConfig: {
      // registerRegex: '(\\/halm)|(\\/orderPerfomace)|(\\/pub/halm)',
      // 原先在.hzerorc.json使用的是"initLoad": true,现在使用的模块加载规则：当匹配到对应的路由后，才加载对应的模块。
      // 主模块下microServices下registerRegex优先级最高
    },
    mfExposes: {
      '_hzero_layout_cards:ALM.ALARM_STATISTICS_CARD': '@/cards/AlarmStatisticsCard',
      '_hzero_layout_cards:ALM.ASSET_HANDING_CARD': '@/cards/AssetHandingCard',
      '_hzero_layout_cards:ALM.ASSET_QUANTITY_CARD': '@/cards/AssetQuantityCard',
      '_hzero_layout_cards:ALM.ASSET_REPORT_CARD': '@/cards/AssetReportCard',
      '_hzero_layout_cards:ALM.ASSET_REPORT_ONE_CARD': '@/cards/AssetReportOneCard',
      '_hzero_layout_cards:ALM.ASSET_REPORT_TWO_CARD': '@/cards/AssetReportTwoCard',
      '_hzero_layout_cards:ALM.ASSET_USAGE_CARD': '@/cards/AssetUsageCard',
      '_hzero_layout_cards:ALM.FREE_ASSETS_CARD': '@/cards/FreeAssetsCard',
      '_hzero_layout_cards:ALM.IDLE_ASSET_CARD': '@/cards/IdleAssetCard',
      '_hzero_layout_cards:ALM.PERSONAL_KPI_CARD': '@/cards/PersonalKpiCard',
      '_hzero_layout_cards:ALM.QUICK_OPERA_CARD': '@/cards/QuickOperaCard',
      '_hzero_layout_cards:ALM.RUN_STATE_CARD': '@/cards/RunStateCard',
      '_hzero_layout_cards:ALM.TASK_ANALYSIS_CARD': '@/cards/TaskAnalysisCard',
      '_hzero_layout_cards:ALM.TODO_ORDER_CARD': '@/cards/TodoOrderCard',
      '_hzero_layout_cards:ALM.TODO_TABLE_CARD': '@/cards/TodoTableCard',
      '_hzero_layout_cards:ALM.WARRANTY_COUNT_CARD': '@/cards/WarrantyCountCard',
    },
  },
});
