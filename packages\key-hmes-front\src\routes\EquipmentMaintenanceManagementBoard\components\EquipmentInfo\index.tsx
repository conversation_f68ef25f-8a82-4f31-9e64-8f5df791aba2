import React, { useMemo } from 'react';
import DashboardCard from '../../components/DashboardCard';
import ScrollBoard from '../ScrollBoard';
import styles from '../../index.module.less';
import intl from "utils/intl";

const modelPrompt = 'tarzan.wms.EquipmentMaintenanceManagementBoard';

const EquipmentInfo = ({ info }) => {

    const tableData: any[] = [];
    if (info && info.length) {
        info.forEach((val) => {
            const {
                woNum,
                assetName,
                createdName,
                workcenterName,
                name,
                creationDate,
            } = val;
            tableData.push([
                woNum,
                assetName,
                createdName,
                workcenterName,
                name,
                creationDate,
            ]);
        });
    }

    const config = useMemo(
        () => ({
            header: [
                intl.get(`${modelPrompt}.field.woNum`).d('单号'),
                intl.get(`${modelPrompt}.field.assetName`).d('设备名称'),
                intl.get(`${modelPrompt}.field.createdName`).d('创建人'),
                intl.get(`${modelPrompt}.field.workcenterName`).d('负责人组'),
                intl.get(`${modelPrompt}.field.name`).d('负责人'),
                intl.get(`${modelPrompt}.field.creationDate`).d('故障开始时间'),
            ],
            data: tableData,
            rowNum: 8,
            align: ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'],
            oddRowBGC: 'rgba(22,66,127,0.3)',
            headerBGC: 'rgb(3, 157, 206,0.3)',
            evenRowBGC: 'rgba(3,28,60, 0.3)',
            headerHeight: 40,
        }),
        [tableData],
    );
    

    return (
        <DashboardCard style={{ height: '100%' }} >
            <div style={{ width: '100%', height: '100%' }}>
                <div className={styles['my-scroll-board-table']}>
                    <ScrollBoard config={config} style={{ width: '100%', height: '100%', marginLeft: '0px 10px' }} />
                </div>
            </div>
        </DashboardCard>
    );
};

export default EquipmentInfo;