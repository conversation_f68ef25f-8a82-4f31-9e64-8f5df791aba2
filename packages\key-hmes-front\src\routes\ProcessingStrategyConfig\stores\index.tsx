// 加工策略配置
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ProcessingStrategyConfigNew';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  pageSize: 10,
  modifiedCheck: false,
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation-cas`,
        method: 'GET',
      };
    },
    destroy: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/hme-esop-heads`,
        method: 'DELETE',
      };
    },
  },
  queryFields: [
    {
      name: 'operationName',
      label: intl.get(`${modelPrompt}.model`).d('工艺'),
      type: FieldType.string,
    },
    {
      name: 'inType',
      label: intl.get(`${modelPrompt}.inType`).d('进站策略'),
      lookupCode: 'HME.IN_TYPE',
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'outType',
      label: intl.get(`${modelPrompt}.outType`).d('出站策略'),
      lookupCode: 'HME.OUT_TYPE',
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'signalSource',
      label: intl.get(`${modelPrompt}.signalSource`).d('联动信号来源'),
      lookupCode: 'HME.LINKAGE_SIGNAL_SOURCE',
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'containerLoadType',
      label: intl.get(`${modelPrompt}.signalSource`).d('装箱类型'),
      lookupCode: 'HME.CONTAINER_LOAD_TYPE',
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'containerLoadConfig',
      label: intl.get(`${modelPrompt}.signalSource`).d('装箱配置'),
      lookupCode: 'HME.CONTAINER_LOAD_CONFIG',
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
    },
  ],
  fields: [
    {
      name: 'operation',
      label: intl.get(`${modelPrompt}.model`).d('工艺'),
      lovCode: 'HME.OPERATION_LOV',
      lovPara: {
        tenantId,
      },
      type: FieldType.object,
      required: true,
      textField: 'operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      type: FieldType.string,
      bind: 'operation.operationId',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      bind: 'operation.operationName',
    },
    {
      name: 'inType',
      label: intl.get(`${modelPrompt}.inType`).d('进站策略'),
      lookupCode: 'HME.IN_TYPE',
      required: true,
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'outType',
      label: intl.get(`${modelPrompt}.outType`).d('出站策略'),
      lookupCode: 'HME.OUT_TYPE',
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('inType') || record.get('inType') === 'CROSS';
        },
        required({ record }) {
          return record.get('inType') !== 'CROSS';
        },
      },
    },
    {
      name: 'signalSource',
      label: intl.get(`${modelPrompt}.signalSource`).d('联动信号来源'),
      lookupCode: 'HME.LINKAGE_SIGNAL_SOURCE',
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled({ record }) {
          return (
            !record.get('outType') ||
            record.get('outType') === 'MANUAL_CROSS' ||
            record.get('outType') === 'MANUAL_OUT'
          );
        },
        required({ record }) {
          return record.get('outType') === 'LINKED_CROSS' || record.get('outType') === 'LINKED_OUT';
        },
      },
    },
    {
      name: 'containerLoadType',
      label: intl.get(`${modelPrompt}.signalSource`).d('装箱类型'),
      lookupCode: 'HME.CONTAINER_LOAD_TYPE',
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'containerLoadConfig',
      label: intl.get(`${modelPrompt}.signalSource`).d('装箱配置'),
      lookupCode: 'HME.CONTAINER_LOAD_CONFIG',
      type: FieldType.string,
      textField: 'meaning',
      valueField: 'value',
    },
  ],
});

export { tableDS };
