/*
 * @Description: 产品审核计划-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-10-09 14:05:07
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-10 15:23:19
 */
import React, { useMemo, useCallback } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores';

const modelPrompt = 'tarzan.qms.productReview.productReviewPlan';

const ProductReviewPlanList = props => {
  const { tableDs, history } = props;

  const renderStatusTag = (value, record) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
        className = 'blue';
        break;
      case 'PUBLISHED':
        className = 'green';
        break;
      case 'IN_APPROVAL':
        className = 'orange';
        break;
      case 'REJECTED':
        className = 'red';
        break;
      default:
        className = 'gray';
    }
    return <Tag color={className}>{record!.getField('productRevPlanStatus')!.getText()}</Tag>;
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'productRevPlanCode',
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/product-review/product-review-plan/dist/${record!.get(
                    'productRevPlanId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'siteName',
      },
      {
        name: 'productRevPlanDesc',
      },
      {
        name: 'revYear',
      },
      {
        name: 'productRevPlanStatus',
        align: ColumnAlign.center,
        renderer: ({ value, record }) => renderStatusTag(value, record),
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 150,
      },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    history.push(`/hwms/product-review/product-review-plan/dist/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('产品审核计划')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          // permissionList={[
          //   {
          //     code: `${modelPrompt}.list.button.create`,
          //     type: 'button',
          //     meaning: '列表页-新建按钮',
          //   },
          // ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="productReviewPlan_searchCode"
          customizedCode="productReviewPlan_customizedCode"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ProductReviewPlanList),
);
