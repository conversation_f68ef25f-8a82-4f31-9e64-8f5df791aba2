/*
 * @Description: 质量体系文件管理-全文检索DS
 * @Author: <<EMAIL>>
 * @Date: 2023-10-20 11:38:14
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-19 20:47:34
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import DataSet, { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.systemDocumentManagement';
const tenantId = getCurrentOrganizationId();

const searchFormDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'queryContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.queryContent`).d('检索内容'),
    },
    {
      name: 'searchType',
      type: FieldType.string,
      defaultValue: 'any',
      options: new DataSet({
        autoCreate: true,
        data: [
          { value: 'any', meaning: intl.get(`${modelPrompt}.  `).d('包含任意一个关键词') },
          // { value: 'all', meaning: '包含全部的关键词' },
          { value: 'perfect', meaning: intl.get(`${modelPrompt}.match.keywordExactly`).d('完全匹配关键字') },
        ],
        fields: [
          { name: 'value' },
          { name: 'meaning' },
        ],
      }),
    },
  ],
});

const searchTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'fileId',
  queryFields: [
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编号'),
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件名称'),
    },
    {
      name: 'fileLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileLevel`).d('文件级别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_LEVEL',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.editedDepartment`).d('编制专业'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'editedDepartment',
      type: FieldType.number,
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsibleDeptCode',
      bind: 'departmentLov.unitCode',
    },
    {
      name: 'editedDepartmentName',
      bind: 'departmentLov.unitName',
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.editedBy`).d('编制人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: { tenantId },
    },
    {
      name: 'editedBy',
      bind: 'responsLov.id',
    },
    {
      name: 'fileStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileStatus`).d('文件状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_STATUS',
      defaultValue: 'PUBLISHED',
    },
    {
      name: 'editedDateFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.editedDateFrom`).d('编制日期从'),
      max: 'editedDateTo',
    },
    {
      name: 'editedDateTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.editedDateTo`).d('编制日期至'),
      min: 'editedDateFrom',
    },
    {
      name: 'publishDateFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.publishDateFrom`).d('发布日期从'),
      max: 'publishDateTo',
    },
    {
      name: 'publishDateTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.publishDateTo`).d('发布日期至'),
      min: 'publishDateFrom',
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.affliatedProcess`).d('所属过程'),
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
    },
    {
      name: 'currentFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.currentFlag`).d('是否最新'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
  fields: [
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编码'),
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件名称'),
    },
    {
      name: 'fileContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileContent`).d('文件内容'),
    },
    {
      name: 'fileStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileStatus`).d('文件状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_STATUS',
    },
    {
      name: 'editedDepartmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedDepartment`).d('编制专业'),
    },
    {
      name: 'editedDepartmentParentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedDepartmentParentName`).d('编制部门'),
    },
    {
      name: 'editedByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedBy`).d('编制人'),
    },
    {
      name: 'editedDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedDate`).d('编制日期'),
    },
    {
      name: 'publishDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.publishDate`).d('发布日期'),
    },
    {
      name: 'version',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.version`).d('版本号'),
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.affliatedProcess`).d('所属过程'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
    },
    {
      name: 'fileLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileLevel`).d('文件级别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_LEVEL',
    },
    {
      name: 'qisEsFileList',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/es-list/for/ui`,
        method: 'GET',
      };
    },
  },
});

export { searchFormDS, searchTableDS };
