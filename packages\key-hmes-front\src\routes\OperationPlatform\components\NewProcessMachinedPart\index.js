/* eslint-disable jsx-a11y/alt-text */
// 新加工件
import React, { useState, useEffect, useMemo, useRef } from 'react';
import { TextField, Button, DataSet, Modal, Table, Form, Output, Tooltip } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import moment from 'moment';
import position from '@/assets/operationPlatformCard/position.png';
import arrowRight from '@/assets/operationPlatformCard/arrow-right.png';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import onSite from '@/assets/operationPlatformCard/onSite.svg';
import codePrint from '@/assets/operationPlatformCard/codePrint.svg';
// import { TemplatePrintButton } from '../../../../components/tarzan-ui';
// import NumberCharts from './App';
import { useOperationPlatform } from '../../contextsStore';
import { useResizeObserver } from '../../useResizeObserver';
import { detailDS, tableDs } from './stores/MachinedPartDS';
import { CardLayout } from '../commonComponents';
import MachinedPartPrint from '../commonComponents/MachinedPartPrint';
import OnSiteProduction from '../commonComponents/OnSiteProduction';

import { QueryCardList } from '../../services';
import styles from './index.modules.less';

const tenantId = getCurrentOrganizationId();

const MachinedPartCard = props => {
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );

  const tableDataSet = useMemo(
    () =>
      new DataSet({
        ...tableDs(),
      }),
    [],
  );

  const timer = useRef(null);
  const [time, setTime] = useState(0);
  const { enterInfo, workOrderData, dispatch, containerDetail, cardMode } = useOperationPlatform();
  const [printModalShow, setPrintModalShow] = useState(false); // 是否展示打印弹框
  const [siteModalShow, setSiteModalShow] = useState(false); // 是否展示站内弹框
  const [loading, setLoading] = useState(false); // 工单loading
  // const [computingTime, setComputingTime] = useState(false); // 是否计算时间
  // const [renderProps, setRenderProps] = useState([]);
  const [formColumns, setFormColumns] = useState(1); // 工单信息列数
  const [trendsNum, setTrendsNum] = useState('');// 趋势数量
  const trendsNumRef = useRef(trendsNum);

  const { run: queryCardList, loading: queryCardListLoading } = useRequest(QueryCardList(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    trendsNumRef.current = trendsNum;
  }, [trendsNum])

  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if(size.width <= 480){
      setFormColumns(1);
    }else if(size.width > 480 && size.width <= 780){
      setFormColumns(2);
    }else if(size.width > 780 && size.width <= 1090){
      setFormColumns(3);
    }else{
      setFormColumns(4);
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('.ProcessWorkorderMachinedPartForm'));

  // useEffect(() => {
  //   queryCardList({
  //     params: {
  //       cardId: '19',
  //     },
  //   }).then(res => {
  //     if (res?.length) {
  //       try {
  //         const cardConfig = Object.entries(JSON.parse(res[0].cardConfiguration));
  //         setRenderProps(cardConfig);
  //       } catch (error) {
  //         notification.error({ message: 'cardConfiguration字段配置错误！' });
  //       }
  //     }
  //   });
  // }, []);

  // 清空数据
  const cleanData = () => {
    dispatch({
      type: 'update',
      payload: {
        workOrderData: {},
      },
    })
    detailDs.loadData([]);
    // setComputingTime(false);
  };

  const columns = [
    {
      name: 'operationName',
    },
    {
      name: 'nextOperationName',
    },
    {
      name: 'routerStepName',
    },
  ];

  const handleConfirm = value => {
    if (tableDataSet.selected.length === 0) {
      notification.error({ message: '请选择工艺' });
      return false;
    }
    onFetchProcessed(value);
    return true;
  };

  // 扫描在制品
  const onFetchProcessed = value => {
    // setComputingTime(false);
    if (timer.current) {
      clearInterval(timer.current);
      setTime(0);
    }
    if (value) {
      setLoading(true);
      const params = {
        identification: value,
        workcellId: enterInfo?.workStationId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        workcellCode: enterInfo?.workStationCode,
        workcellName: enterInfo?.workStationName,
        productionLineId: enterInfo?.productionLineId,
        productionLineCode: enterInfo?.productionLineCode,
        productionLineName: enterInfo?.productionLineName,
        operationId: enterInfo?.selectOperation?.operationId,
        operationName: enterInfo?.selectOperation?.operationName,
        operationDesc: enterInfo?.selectOperation?.description,
        dto3: tableDataSet.selected.length > 0
          ? tableDataSet.selected[0].data
          : null,
        containerInfo: {
          containerId: containerDetail?.containerId,
          containerCode: containerDetail?.containerCode,
          containerTypeId: containerDetail?.containerTypeId,
          capacityQty: containerDetail?.capacityQty,
          personalCapacityQty: containerDetail?.personalCapacityQty,
        },
      };
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-working-part-new/eo-scan/ui`, {
        // request(`/key-27643/v1/${tenantId}/hme-working-part-new/eo-scan/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          setTime(res.processedTime || 0)
          timer.current = setInterval(() => {
            setTime((prev) => prev + 1);
          }, 1000)
          detailDs.loadData([res]);
          // props.changeEoData({ ...res, ...res.workOrderCardVO });
          dispatch({
            type: 'update',
            payload: {
              workOrderData: res,
            },
          });
          setLoading(false);
          setTrendsNum('')
          if(!!res?.containerLoadErrorMsg){
            notification.warning({
              message: res?.containerLoadErrorMsg,
            });
          }
          if (res.popoverOrNot === 'Y' && res.partNewDTO3List.length > 0) {
            tableDataSet.loadData(res.partNewDTO3List);
            Modal.open({
              title: '工艺选择',
              destroyOnClose: true,
              // closable: true,
              contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
              className: styles.NewProcessMachinedPartModal,
              children: (
                <Table dataSet={tableDataSet} columns={columns} searchCode="InspectionPlatform" />
              ),
              onOk: () => handleConfirm(value),
              onCancel: () => {
                tableDataSet.unSelectAll();
              },
            });
          } else {
            // if (res.pitStopFlag === 'Y') {
            //   setComputingTime(true);
            // }
            props.handleAddRecords({
              cardId: props.cardId,
              messageType: 'SUCCESS',
              message: `扫描在制品${res.identification}成功`,
            });
            props.nextPriority();
            if (res.inType === 'INSPECT' || res.inType === 'IN') {
              // props.changeEoData({ ...res, cardWorkpiece: 'Y' });
              dispatch({
                type: 'update',
                payload: {
                  workOrderData: { ...res, cardWorkpiece: 'Y' },
                },
              })
            }
          }
        } else {
          // cleanData();
          setLoading(false);
          notification.error({ message: res.message });
          setTimeout(() => {
            document
              .querySelector(
                `#operationPlatformInput${
                  props.priorityLayout?.filter(item => item.i === '19')[0]?.priority
                }`,
              )
              .focus();
            document
              .querySelector(
                `#operationPlatformInput${
                  props.priorityLayout?.filter(item => item.i === '19')[0]?.priority
                }`,
              )
              .select();
          }, 100);
          props.handleAddRecords({
            cardId: props.cardId,
            messageType: 'FAIL',
            message: `扫描在制品${value}失败`,
          });
        }
      });
    } else {
      cleanData();
    }
  };

  // 进出站
  const processCompleted = value => {
    if (loading) {
      return;
    }
    // props.changeSpin(true);
    setLoading(true);
    const params = {
      workcellId: enterInfo?.workStationId,
      shiftCode: enterInfo?.shiftCode,
      shiftDate: enterInfo?.shiftDate,
      workcellCode: enterInfo?.workStationCode,
      workcellName: enterInfo?.workStationName,
      productionLineId: enterInfo?.productionLineId,
      productionLineCode: enterInfo?.productionLineCode,
      productionLineName: enterInfo?.productionLineName,
      operationId: enterInfo?.selectOperation?.operationId,
      operationName: enterInfo?.selectOperation?.operationName,
      operationDesc: enterInfo?.selectOperation?.description,
      eoId: workOrderData?.eoId,
      identification: workOrderData?.identification,
      workOrderId: workOrderData?.workOrderId,
      materialId: workOrderData?.materialId,
      routerStepId: workOrderData?.routerStepId,
      currentProcessId: workOrderData?.currentProcessId,
      nextStepId:
        tableDataSet.selected.length > 0
          ? tableDataSet.selected[0].data.routerStepId
          : workOrderData?.firstStepPitStop === 'Y'
            ? null
            : workOrderData?.nextStepId,
      dto3: tableDataSet.selected.length > 0 ? tableDataSet.selected[0].data : null,
      executeType: value,
      containerInfo: containerDetail || {},
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-working-part-new/execute/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        if (res.popoverOrNot === 'Y' && res.partNewDTO3List.length > 0) {
          tableDataSet.loadData(res.partNewDTO3List);
          Modal.open({
            title: '工艺选择',
            destroyOnClose: true,
            // closable: true,
            contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
            className: styles.NewProcessMachinedPartModal,
            children: (
              <Table dataSet={tableDataSet} columns={columns} searchCode="InspectionPlatform" />
            ),
            onOk: () => handleConfirm(value),
            onCancel: () => {
              tableDataSet.unSelectAll();
            },
          });
        } else {
          notification.success();
          setLoading(false);
          dispatch({
            type: 'update',
            payload: {
              workOrderData: {
                containerRefreshFlag: res.containerRefreshFlag,
                completedQty: workOrderData.completedQty + (trendsNumRef.current ? Number(trendsNumRef.current) : 0),
              },
            },
          })
          if(!!res?.containerLoadErrorMsg){
            notification.warning({
              message: res?.containerLoadErrorMsg,
            });
          }
          setTrendsNum(`+${res.eoQty}`)
          // setComputingTime(false);
          props.handleAddRecords({
            cardId: props.cardId,
            messageType: 'SUCCESS',
            message: `在制品${workOrderData?.identification}${
              value === 'OUTBOUND' ? '出站' : '过站'
            }成功`,
          });
        }
      } else {
        notification.error({ message: res.message });
        setLoading(false);
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'FAIL',
          message: `在制品${workOrderData?.identification}${
            value === 'OUTBOUND' ? '出站' : '过站'
          }失败`,
        });
      }
    });
  };

  // 退回
  const processReturn = () => {
    const params = {
      workcellId: enterInfo?.workStationId,
      shiftCode: enterInfo?.shiftCode,
      shiftDate: enterInfo?.shiftDate,
      operationId: enterInfo?.selectOperation?.operationId,
      eoId: workOrderData?.eoId,
      routerStepId: workOrderData?.routerStepId,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/step/return/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        notification.success();
        // onFetchProcessed(workOrderData?.identification);
        // dispatch({
        //   type: 'update',
        //   payload: {
        //     workOrderData: {
        //       ...workOrderData,
        //       cardClear: 'Y',
        //     },
        //   },
        // })
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          message: `退回${workOrderData.workOrderNum}成功`,
        });
      } else {
        notification.error({ message: res.message });
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'FAIL',
          message: `退回${workOrderData.workOrderNum}失败`,
        });
      }
    });
  };

  const getTimes = t => {
    if (!t) {
      return;
    }
    let h = parseInt(String((t / 60 / 60) % 24), 10);
    let m = parseInt(String((t / 60) % 60), 10);
    let s = parseInt(String(t % 60), 10);
    // 三元表达式 补零 如果小于10 则在前边进行补零 如果大于10 则不需要补零
    if (t < 60) {
      s = s < 10 ? `0${s}` : s;
      return `${s}秒`;
    }
    if (t >= 60 && t < 3600) {
      m = m < 10 ? `0${m}` : m;
      s = s < 10 ? `0${s}` : s;
      return `${m}分${s}秒`;
    }
    h = h < 10 ? `0${h}` : h;
    m = m < 10 ? `0${m}` : m;
    s = s < 10 ? `0${s}` : s;
    return `${h}时${m}分${s}秒`;
  };

  const handleOpenPrint = async () => {
    if(!workOrderData.workOrderId || printModalShow){
      return;
    }
    setPrintModalShow(true)
  }

  const handleCloseStepModal = () => {
    setPrintModalShow(false)
  }

  const machinedPartPrintProps = {
    workOrderData: workOrderData,
    enterInfo: enterInfo,
    handleCloseStepModal,
    printModalShow,
    contentClassName: 'ProcessWorkorderMachinedPartHead',
  }

  const handleOpenSite = async () => {
    setSiteModalShow(true)
  }
  const handleCloseSiteModal = () => {
    setSiteModalShow(false)
  }

  const onSiteProductionProps = {
    catdType: 'NewProcessMachinedPart',
    enterInfo: enterInfo,
    handleCloseSiteModal,
    siteModalShow,
    contentClassName: 'ProcessWorkorderMachinedPartHead',
    onFetchProcessed,
  }

  return (
    <div style={{width: '100%', height: '100%'}}>
      <CardLayout.Layout spinning={queryCardListLoading || loading}>
        <CardLayout.Header
          className='ProcessWorkorderMachinedPartHead'
          title="新加工件"
          help={props?.cardUsage?.remark}
          content={
            <TextField
              dataSet={detailDs}
              placeholder="请扫描EO"
              id={`operationPlatformInput${
                props.priorityLayout?.filter(item => item.i === '19')[0]?.priority
              }`}
              name="identificationField"
              onEnterDown={e => onFetchProcessed(e.target.value)}
              onChange={value => (value ? null : onFetchProcessed(null))}
              prefix={<img src={scanIcon} alt='' style={{height: '19px'}}/>}
            />
          }
          addonAfter={
            <>
              {workOrderData.workOrderId && (
                <Button
                  onClick={processReturn}
                  disabled={!workOrderData?.eoId}
                  style={{ background: 'rgba(255, 182, 1, 1)', borderColor: 'rgba(255, 182, 1, 1)' }}
                >
                  退回
                </Button>
              )}
              {workOrderData.workOrderId && workOrderData?.outType === 'MANUAL_OUT' && (
                <Button
                  color="primary"
                  // hidden={workOrderData?.outType !== 'MANUAL_OUT'}
                  onClick={() => processCompleted('OUTBOUND')}
                  disabled={!workOrderData?.eoId}
                >
                  出站
                </Button>
              )}
              {workOrderData.workOrderId && workOrderData?.outType === 'MANUAL_CROSS' && (
                <Button
                  color="primary"
                  onClick={() => processCompleted('OVER_STATION')}
                  // hidden={workOrderData?.outType !== 'MANUAL_CROSS'}
                  disabled={!workOrderData?.eoId}
                >
                  过站
                </Button>
              )}
            </>
          }
        />
        <CardLayout.Content className='ProcessWorkorderMachinedPartForm'>
          <div
            style={{
              display: workOrderData.currentProcess || workOrderData.nextProcess ? 'block' : 'none',
            }}
            className={styles.customTitle}
          >
            &nbsp;&nbsp;
            <img src={position} alt="" />
            <span style={{ color: 'rgba(51, 241, 255, 1)' }}> {workOrderData.currentProcess}</span>
            &nbsp;&nbsp;
            <img src={arrowRight} alt="" />
            &nbsp;&nbsp;
            <span style={{ color: 'rgba(255, 255, 255, 0.85)' }}>{workOrderData.nextProcess}</span>
          </div>
          {/* <CardCustomizeForm
            renderProps={renderProps}
            responseData={workOrderData}
            computingTime={computingTime}
          /> */}
          <Form dataSet={detailDs} labelWidth={130} columns={cardMode === 'Tile' ? formColumns : 4}>
            {/* {renderProps.map(item => (
              <Output name={item[0]}/>
            ))} */}
            <Output name='workOrderNum' />
            <Output name='identification' />
            <Output name='materialCode' />
            <Output name='opProcess' />
            <Output
              name='pitStopDate'
              renderer={({ value }) => {
                return value ? moment(value).format('YYYY-MM-DD HH:mm') : null;
              }}
            />
            <Output
              name='processedTime'
              renderer={() => {
                return time ? getTimes(time) : '';
              }}
            />
            <Output name='customerDesc' />
            <Output name='standardBeat' />
            <Output name='remark' />
            <Output name='materialName' />
          </Form>
          {/* <div style={{ fontSize: '14px', fontFamily: 'auto' }}>
            <span
              style={{
                color: 'rgb(112, 187, 243)',
              }}
            >
              完工数量：
              {workOrderData.completedQty}
            </span>
            <span style={{ color: '#fff' }}>+{workOrderData.eoQty}</span>
          </div> */}
          {/* <NumberCharts data={workOrderData} /> */}
          <div className={styles.cardFooter}>
            <div className={styles.completeText}>
              <span className={styles.completeNumObj}>完工数量:</span>
              <Tooltip title={workOrderData?.completedQty} theme="light">
                <span className={styles.completeValue}>{workOrderData?.completedQty || ''}</span>
                <span className={styles.completeValueAdd}>{trendsNum || ''}</span>
              </Tooltip>
            </div>
            <div className={styles.printButton}>
              <div className={styles.buttonContent}>
                <img src={onSite} alt='' />
                <div onClick={handleOpenSite}>站内在制</div>
              </div>
              <div className={styles.buttonContent}>
                <img src={codePrint} alt='' />
                <div onClick={handleOpenPrint}>条码打印</div>
              </div>
            </div>
          </div>
        </CardLayout.Content>
      </CardLayout.Layout>
      <MachinedPartPrint {...machinedPartPrintProps}/>
      <OnSiteProduction {...onSiteProductionProps}/>
    </div>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(MachinedPartCard);
