/*
 * @Description: 审批历史按钮-侧弹窗显示审批历史记录
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-04-07 14:42:14
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */

import React, { FC, useCallback } from 'react';
import { Button, Modal } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { History } from 'history';
import CloseImg from 'alm/assets/img/close.png';
import ApprovalHistory from './ApprovalHistory';
import styles from './index.module.less';

interface Props {
  history: History;
  style: React.CSSProperties | undefined;
  wkInstanceId: number | undefined;
}

const ApprovalHistoryBtn: FC<Props> = props => {
  const { style, wkInstanceId, history } = props;
  const handleApproveHistory = useCallback(() => {
    Modal.open({
      key: 'approvalHistory',
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      drawer: true,
      drawerBorder: false,
      style: {
        width: 560,
      },
      title: renderLogModalTitle(),
      footer: null,
      children: <ApprovalHistory instanceId={wkInstanceId} history={history} />,
    });

    function renderLogModalTitle() {
      return (
        <React.Fragment>
          <div className={styles['change-logs-modal-title']} onClick={handleCloseModal}>
            <span className={styles['title-desc']}>
              {intl.get(`alm.common.button.approveHistory`).d('审批历史')}
            </span>
            <a className={styles['detail-record']} onClick={handleDetailLogs}>
              {intl.get(`alm.common.button.detailRecord`).d('详细记录')}
            </a>
            <a>
              <img src={CloseImg} alt="" className={styles['title-img']} />
            </a>
          </div>
        </React.Fragment>
      );
    }

    function handleCloseModal() {
      Modal.destroyAll();
    }
    function handleDetailLogs() {
      const path = `/hwkf/submitted/detail/${wkInstanceId}`;
      history.push(path);
    }
  }, [wkInstanceId]);

  return (
    <Button style={style} onClick={handleApproveHistory}>
      {intl.get(`alm.common.button.approveHistory`).d('审批历史')}
    </Button>
  );
};

export default formatterCollections({
  code: ['alm.common', 'alm.component'],
})(ApprovalHistoryBtn);
