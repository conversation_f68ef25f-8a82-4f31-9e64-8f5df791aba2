import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { filterNullValueObject, getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import DashboardCard from '../DashboardCard.jsx';
import styles from '../../index.module.less';

const tenantId = getCurrentOrganizationId();
// 不合格笔数处置状态
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/quantity`;

const NgQty = ({isFullScreen, materialId, timers }) => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>([]);

  useEffect(()=>{
    let time;
    if(timers) {
      time = setInterval(() => {
        fetchData();
      }, (timers)*60000)
    } else if(materialId){
      fetchData();
    }
    return () => {
      clearInterval(time)
    }
  },[timers,materialId])

  useEffect(() => {
    fetchData();
  }, [materialId])

  const fetchData = useCallback(async () => {
    const params ={
      materialId,
    };
    const res = await request(url, {
      method: 'GET',
      query: filterNullValueObject(params),
    });
    setData(res);
  }, [materialId]);

  const data1 = [
    { name: '合格', value: data?.qualifiedQuantity||0 },
    { name: '不合格', value: data?.unQualifiedQuantity||0 },
  ];
  const option: any = useMemo(() => {
    return {
      color:['#5470c6', '#ef6567'],
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.9)'},
      series: {
        type: 'pie',
        radius: [50, 90],
        center: ['50%', '45%'],
        left: 'center',
        width: 450,
        itemStyle: { borderColor: '#fff', borderWidth: 1 },
        label: {
          padding: [0, -10],
          fontWeight: 'lighter',
          color: '#fff',
          fontSize: 18,
        },
        labelLine: {
          length: 20,
          length2: 50,
          lineStyle: {
            type: 'dashed', // 设置虚线类型
          },
        },
        data:data1,
      },
    };
  }, [data]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  return (
    <DashboardCard style={{ height: '100%' }}>
      <div className={styles['my-scroll-board-title']}>
        当日检验合格笔数/不合格笔数饼图
      </div>
      {isFullScreen?
        (<div style={{ width: '100%', height: '100%' }} className={styles['dashboard-right-chart-full-screen']}>
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>):
        <div style={{ width: '100%', height: '100%' }} className={styles['dashboard-right-chart']}>
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>
      }
    </DashboardCard>
  );
};
export default NgQty;
