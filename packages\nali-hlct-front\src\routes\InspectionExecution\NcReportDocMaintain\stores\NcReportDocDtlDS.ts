/**
 * @Description: 不良记录单管理平台 -详情界面DS
 * @Author: <EMAIL>
 * @Date: 2023/3/9 14:42
 */
import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment';
import uuid from 'uuid/v4';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.hwms.ncReportDocMaintain';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const disposalDs = new DataSet({
  autoQuery: true,
  paging: false,
  transport: {
    read: () => {
      return {
        url: `/hpfm/v1/${tenantId}/lovs/data?lovCode=MT.DISPOSAL_TYPE`,
        method: 'GET',
        params: { tenantId },
        transformResponse: data => {
          const _data = Array.isArray(data) ? data : JSON.parse(data);
          _data.push({
            meaning: intl.get(`${modelPrompt}.noDisposal`).d('无处置'),
            value: 'NO',
          });
          return _data;
        },
      };
    },
  },
});

const updateHeadNcDesc = record => {
  // 打平详情界面数据
  const _list = record?.toData();
  const remarkList = _list?.ncReportLineList.map(item => {
    return item?.ncDesc;
  });
  // 获取各层级的备注
  const _remarkList = remarkList?.filter(i => i !== '');
  return _remarkList?.join(';');
};

const updateLineNcDesc = record => {
  // 打平详情界面行数据
  let _list = record?.toData();
  _list = _list?.ncReportLineDtlList || [];
  // 获取各层级的备注
  const remarkList = _list.map(item => item?.remark || '').filter(i => i !== '');
  return remarkList?.join(';');
};

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/details/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.HEAD, ${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.LINE, ${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_DETAIL.DETAIL`,
        method: 'GET',
        transformResponse: data => {
          const { rows, success, message } = JSON.parse(data);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          if (rows?.partNcReportLineDisposalList && rows?.partNcReportLineDisposalList?.length) {
            rows?.partNcReportLineDisposalList.forEach(item => {
              if (item?.disposalFunctionList && item?.disposalFunctionList?.length) {
                item?.disposalFunctionList?.forEach(funItem => {
                  item[funItem.dispositionFunction] = funItem.qty;
                });
              }
            });
          }
          return {
            ...rows,
            disposalType: rows?.disposalType || 'NO',
            disposalList:
              rows?.disposalType === 'ALL' && rows?.allNcReportLineDisposal
                ? [rows?.allNcReportLineDisposal]
                : rows?.partNcReportLineDisposalList,
          };
        },
      };
    },
  },
  fields: [
    {
      name: 'ncReportId',
      type: FieldType.number,
    },
    {
      name: 'ncReportNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportNum`).d('不良记录单编码'),
      disabled: true,
    },
    {
      name: 'ncReportType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportType`).d('不良记录单类型'),
      required: true,
      defaultValue: 'NC_REPORT',
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=NC_REPORT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => record.get('ncReportStatus') !== 'NEW',
      },
    },
    {
      name: 'ncReportStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportStatus`).d('不良记录单状态'),
      disabled: true,
      defaultValue: 'NEW',
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_REPORT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      required: true,
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
      dynamicProps: {
        disabled: ({ record }) => record.get('ncReportStatus') !== 'NEW',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lookupCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      textField: 'inspectBusinessTypeDesc',
      valueField: 'inspectBusinessType',
      dynamicProps: {
        disabled: ({ record }) => !record?.get('siteId') || record.get('ncReportStatus') !== 'NEW',
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'reviewType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('评审规则类型'),
      lookupCode: 'MT.QMS.REVIEW_TYPE',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      disabled: true,
    },
    {
      name: 'ncReviewStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReviewStatus`).d('审核状态'),
      disabled: true,
      lookupCode: 'MT.QMS.REVIEW_STATUS',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'createMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createMethod`).d('创建方式'),
      disabled: true,
      defaultValue: 'MANUAL',
      lookupCode: 'MT.QMS.NC_REPORT_CREATE_METHOD',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'inspectSumQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.inspectSumQty`).d('报检总数'),
      min: 0,
      step: 1,
      nonStrictStep: true,
      dynamicProps: {
        disabled: ({ record }) => record.get('ncReportStatus') !== 'NEW',
      },
    },
    {
      name: 'okQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.okQty`).d('合格数'),
      min: 0,
      step: 1,
      nonStrictStep: true,
      dynamicProps: {
        disabled: ({ record }) => record.get('ncReportStatus') !== 'NEW',
      },
    },
    {
      name: 'ngQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.ngQty`).d('不合格数'),
      min: 0,
      step: 1,
      nonStrictStep: true,
      dynamicProps: {
        disabled: ({ record }) => record.get('ncReportStatus') !== 'NEW',
      },
    },
    {
      name: 'samplingQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.samplingQty`).d('抽样数'),
      min: 0,
      step: 1,
      nonStrictStep: true,
      dynamicProps: {
        disabled: ({ record }) => record.get('ncReportStatus') !== 'NEW',
      },
    },
    {
      name: 'disposalType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalType`).d('处置类型'),
      options: disposalDs,
      textField: 'meaning',
      valueField: 'value',
      defaultValue: 'NO',
      dynamicProps: {
        disabled: ({ dataSet, record }) => {
          const filterLine = dataSet.children.ncReportLineList.filter(
            _record => _record?.get('ncObjectType') !== 'INSPECT_DOC',
          );
          return (
            !dataSet.children.ncReportLineList.length ||
            !filterLine.length ||
            record.get('createMethod') === 'MES' ||
            !(record.get('ncReportStatus') === 'NEW' ||
              (record.get('ncReportStatus') === 'HANDLE' &&
                (!record.get('ncReviewStatus') ||
                  ['UNREVIEWED', 'REJECT'].includes(record.get('ncReviewStatus')))))
          );
        },
      },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialAndRevision`).d('物料/版本'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => !record?.get('siteId') || record.get('ncReportStatus') !== 'NEW',
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      bind: 'materialLov.materialName',
    },
    {
      name: 'revisionFlag',
      bind: 'materialLov.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
      bind: 'materialLov.currentRevisionCode',
      textField: 'description',
      valueField: 'description',
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: item?.kid || uuid(),
                  description: item?.description || item,
                };
              });
            }
            if (record) {
              if (firstlyQueryData.length > 0) {
                if (!record?.get('revisionCode')) {
                  record?.init('revisionCode', firstlyQueryData[0].description);
                }
              } else {
                record?.init('revisionCode');
              }
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record?.get('materialId') ||
            record?.get('revisionFlag') !== 'Y' ||
            record.get('ncReportStatus') !== 'NEW'
          );
        },
        required({ record }) {
          return record?.get('revisionFlag') === 'Y' && record?.get('materialId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record?.get('siteId')],
            materialId: record?.get('materialId') || undefined,
          };
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      dynamicProps: {
        disabled: ({ record }) => record.get('ncReportStatus') !== 'NEW',
      },
    },
    {
      name: 'ncDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncDesc`).d('不良描述'),
    },
  ],
  events: {
    update: ({ name, record, value }) => {
      switch (name) {
        case 'siteLov':
          record.init('inspectBusinessType');
          record.init('materialLov', undefined);
          break;
        case 'inspectBusinessType':
          if (!value) {
            record.init('reviewType');
            record.init('ncReviewStatus');
          }
          break;
        case 'reviewType':
          // 当评审规则类型为无需审核/线下审核时，展示为空；
          // 当评审规则类型为自动审核/手工审核时展示为未审核
          switch (value) {
            case 'AUTO_REVIEW':
            case 'MANUAL_REVIEW':
              record.init('ncReviewStatus', 'UNREVIEWED');
              break;
            default:
              record.init('ncReviewStatus', undefined);
          }
          break;
        default:
          break;
      }
    },
  },
});

const scanFormDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'scanInspectObj',
      type: FieldType.string,
      dynamicProps: {
        disabled: ({ dataSet }) =>
          !dataSet.parent?.current?.get('siteId') || !dataSet.parent?.current?.get('materialId'),
      },
    },
  ],
});

const ncRecordLineDS: () => DataSetProps = () => ({
  selection: false,
  autoCreate: false,
  primaryKey: 'ncReportLineId',
  paging: false,
  fields: [
    {
      name: 'ncReportLineId',
      type: FieldType.number,
    },
    {
      name: 'ncRecordType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
      required: true,
      lookupCode: 'MT.NC_RECORD_TYPE',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'ncObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncObjectType`).d('不良对象类型'),
      required: true,
      lookupCode: 'MT.QMS.NC_OBJECT_TYPE',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
      dynamicProps: {
        disabled: ({ record }) => !record?.get('ncRecordType'),
      },
    },
    {
      name: 'ncObjectLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncObjectCode`).d('不良对象编码'),
      required: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode: ({ record }) => {
          switch (record?.get('ncObjectType')) {
            case 'MATERIAL_LOT':
              return 'MT.MATERIAL_LOT';
            case 'EO':
              return 'MT.EO';
            default:
              return 'MT.MES.MAT_LOT_MATERIAL_LOT';
          }
        },
        lovPara: ({ record, dataSet }) => {
          const matCodes: any = [];
          const lotCodes: any = [];
          const materialLotCodes: any = [];
          const eoNums: any = [];
          dataSet.forEach(_rec => {
            if (_rec.id !== record.id) {
              if (_rec.get('ncObjectType') === 'MATERIAL_LOT') {
                materialLotCodes.push(_rec.get('ncObjectCode'));
              } else if (_rec.get('ncObjectType') === 'EO') {
                eoNums.push(_rec.get('ncObjectCode'));
              } else if (_rec.get('ncObjectType') === 'MAT') {
                matCodes.push(_rec.get('ncObjectCode'));
              } else if (_rec.get('ncObjectType') === 'LOT') {
                lotCodes.push(_rec.get('ncObjectCode'));
              }
            }
          });
          switch (record?.get('ncObjectType')) {
            case 'MATERIAL_LOT':
              return {
                tenantId,
                enableFlag: 'Y',
                siteId: dataSet.parent?.current?.get('siteId'),
                materialId: dataSet.parent?.current?.get('materialId'),
                revisionCode: dataSet.parent?.current?.get('revisionCode'),
                materialLotCodes,
              };
            case 'EO':
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
                materialId: dataSet.parent?.current?.get('materialId'),
                revisionCode: dataSet.parent?.current?.get('revisionCode'),
                eoNums,
              };
            case 'MAT':
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
                materialId: dataSet.parent?.current?.get('materialId'),
                revisionCode: dataSet.parent?.current?.get('revisionCode'),
                identifyType: 'MAT',
                materialLotCodes: matCodes,
              };
            default:
              return {
                tenantId,
                siteId: dataSet.parent?.current?.get('siteId'),
                materialId: dataSet.parent?.current?.get('materialId'),
                revisionCode: dataSet.parent?.current?.get('revisionCode'),
                identifyType: 'LOT',
                materialLotCodes: lotCodes,
              };
          }
        },
        textField: ({ record }) => {
          switch (record?.get('ncObjectType')) {
            case 'EO':
              return 'eoNum';
            case 'MATERIAL_LOT':
              return 'materialLotCode';
            default:
              return 'materialName';
          }
        },
        disabled: ({ record, dataSet }) => {
          const {
            siteId,
            materialId,
            revisionFlag,
            revisionCode,
          } = dataSet.parent?.current?.toData();
          return (
            !record?.get('ncObjectType') ||
            !siteId ||
            !materialId ||
            (materialId && revisionFlag === 'Y' && !revisionCode)
          );
        },
      },
    },
    {
      name: 'ncObjectId',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record?.get('ncObjectType')) {
            case 'EO':
              return 'ncObjectLov.eoId';
            default:
              return 'ncObjectLov.materialLotId';
          }
        },
      },
    },
    {
      name: 'ncObjectCode',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record?.get('ncObjectType')) {
            case 'EO':
              return 'ncObjectLov.eoNum';
            case 'MATERIAL_LOT':
              return 'ncObjectLov.materialLotCode';
            default:
              return 'ncObjectLov.materialName';
          }
        },
      },
    },
    {
      name: 'materialLotCode',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record?.get('ncObjectType')) {
            case 'MAT':
            case 'LOT':
              return 'ncObjectLov.materialLotCode';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'routerId',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record?.get('ncObjectType')) {
            case 'EO':
              return 'ncObjectLov.routerId';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'revisionFlag',
      bind: 'ncObjectLov.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectObjectRevisionCode`).d('不良对象版本'),
      bind: 'ncObjectLov.revisionCode',
    },
    {
      name: 'inspectSchemeObjectType',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          switch (record?.get('ncObjectType')) {
            case 'RESPECT_OBJECT':
              return 'ncObjectLov.inspectSchemeObjectType';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'sourceDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceDocNum`).d('来源单据'),
    },
    {
      name: 'sourceNcRecordNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceNcRecordNum`).d('来源不良记录编码'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
      required: true,
      min: 0,
      step: 1,
      nonStrictStep: true,
      bind: 'quantity',
    },
    {
      name: 'uomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
      lovCode: 'MT.COMMON.UOM',
      required: true,
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'uomName',
    },
    {
      name: 'uomId',
      type: FieldType.number,
      bind: 'uomLov.uomId',
    },
    {
      name: 'uomName',
      type: FieldType.string,
      bind: 'uomLov.uomName',
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'customerName',
      bind: 'customerLov.customerName',
    },
    {
      name: 'containerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerName`).d('容器'),
      lovCode: 'MT.CONTAINER',
      textField: 'containerName',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
    },
    {
      name: 'containerId',
      bind: 'containerLov.containerId',
    },
    {
      name: 'containerCode',
      bind: 'containerLov.containerCode',
    },
    {
      name: 'containerName',
      bind: 'containerLov.containerName',
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'workcellId',
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'workcellName',
      bind: 'workcellLov.workcellName',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      textField: 'locatorName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorName',
      bind: 'locatorLov.locatorName',
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'equipmentCode',
      bind: 'equipmentLov.equipmentCode',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    {
      name: 'shiftTeamLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.shiftTeamCode`).d('班组'),
      ignore: FieldIgnore.always,
      textField: 'shiftTeamName',
      lovCode: 'MT.SHIFTTEAM',
      lovPara: { tenantId },
    },
    {
      name: 'shiftTeamId',
      type: FieldType.string,
      bind: 'shiftTeamLov.shiftTeamId',
    },
    {
      name: 'shiftTeamCode',
      type: FieldType.string,
      bind: 'shiftTeamLov.shiftTeamCode',
    },
    {
      name: 'shiftTeamName',
      type: FieldType.string,
      bind: 'shiftTeamLov.shiftTeamName',
    },
    {
      name: 'shiftDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.shiftDate`).d('班次日期'),
      defaultValue: moment(moment().format('YYYY-MM-DD')),
      format: 'YYYY-MM-DD',
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
      valueField: 'shiftInfo',
      textField: 'shiftInfo',
      lookupAxiosConfig: ({ record }) => {
        if (record?.get('shiftTeamId')) {
          return {
            params: {
              shiftTeamId: record?.get('shiftTeamId'),
              date: moment(record?.get('shiftDate')).format('YYYY-MM-DD HH:mm:ss'),
            },
            url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-team-work-platform/shift-get/for/ui`,
            method: 'GET',
            transformResponse(data) {
              if (!data) {
                return;
              }
              // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
              if (data instanceof Array) {
                return data;
              }
              const { rows } = JSON.parse(data);
              return rows;
            },
          };
        }
        return { url: undefined };
      },
      dynamicProps: {
        disabled: ({ record }) => !record?.get('shiftTeamId') || !record?.get('shiftDate'),
      },
    },
    {
      name: 'ncStartUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncStartUserName`).d('不良记录人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'ncStartUserId',
      type: FieldType.string,
      bind: 'ncStartUserLov.id',
    },
    {
      name: 'ncStartUserName',
      type: FieldType.string,
      bind: 'ncStartUserLov.realName',
    },
    {
      name: 'ncStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncStartTime`).d('不良发生时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'ncDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncDesc`).d('不良记录'),
    },
  ],
});

const disposalDS: () => DataSetProps = () => ({
  selection: false,
  primaryKey: 'ncReportLineDisposalId',
  autoCreate: false,
  forceValidate: true,
  paging: false,
  fields: [
    {
      name: 'ncRecordType',
      type: FieldType.string,
    },
    {
      name: 'ncRecordTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
    },
    {
      name: 'ncObjectType',
      type: FieldType.string,
    },
    {
      name: 'ncObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncObjectType`).d('不良对象类型'),
    },
    {
      name: 'ncObjectId',
      type: FieldType.number,
    },
    {
      name: 'ncObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncObjectCodeAndRevision`).d('不良对象编码/版本'),
    },
    {
      name: 'optionList', // 用于记录装配实物下拉数据
    },
    {
      name: 'componentMaterialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentMaterialLotCode`).d('装配对象'),
      valueField: 'componentMaterialLotCode',
      textField: 'displayValue',
      dynamicProps: {
        required: ({ record }) => record?.get('ncRecordType') === 'EO_MATERIAL_NC',
        options: ({ record }) =>
          new DataSet({
            data: record.get('optionList'),
          }),
      },
    },
    {
      name: 'ncObjectRevisionCode',
      type: FieldType.string,
    },
    {
      name: 'ncReportLineDisposalId',
      type: FieldType.number,
    },
    {
      name: 'dispositionGroupLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dispositionGroup`).d('处置组'),
      lovCode: 'MT.DISPOSITION_GROUP',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'dispositionGroupId',
      type: FieldType.number,
      bind: 'dispositionGroupLov.dispositionGroupId',
    },
    {
      name: 'dispositionGroupDesc',
      type: FieldType.string,
      bind: 'dispositionGroupLov.dispositionGroup',
    },
    {
      name: 'dispositionFunctionLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dispositionFunction`).d('处置方法'),
      ignore: FieldIgnore.always,
      textField: 'description',
      lovCode: 'MT.METHOD.DISPOSITION_MEMBER_FUNCTION',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          dispositionGroupId: record?.get('dispositionGroupId'),
        }),
        required: ({ dataSet }) => dataSet.parent?.current?.get('disposalType') === 'ALL',
      },
    },
    {
      name: 'disposalFunctionId',
      type: FieldType.number,
      bind: 'dispositionFunctionLov.dispositionFunctionId',
    },
    {
      name: 'dispositionFunction',
      type: FieldType.string,
      bind: 'dispositionFunctionLov.dispositionFunction',
    },
    {
      name: 'dispositionFunctionDesc',
      type: FieldType.string,
      bind: 'dispositionFunctionLov.description',
    },
    {
      name: 'functionType',
      type: FieldType.string,
      bind: 'dispositionFunctionLov.functionType',
    },
    {
      name: 'functionTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.functionType`).d('处置方法类型'),
      bind: 'dispositionFunctionLov.functionTypeDesc',
      disabled: true,
    },
    {
      name: 'reworkRouterLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reworkRouterName`).d('返修工艺路线'),
      lovCode: 'MT.METHOD.ROUTER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          routerTypes: 'NC',
          siteIds: dataSet?.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet?.parent?.current?.get('siteId'),
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' && record.get('dispositionFunction') === 'REWORK_ROUTER') ||
            (disposalType === 'PART' && record.get('REWORK_ROUTER'))
          );
        },
      },
    },
    {
      name: 'reworkRouterId',
      bind: 'reworkRouterLov.routerId',
    },
    {
      name: 'reworkRouterName',
      bind: 'reworkRouterLov.routerName',
    },
    // {
    //   name: 'reworkStepName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reworkStepName`).d('返修步骤识别码'),
    // },
    {
      name: 'uomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
      lovCode: 'MT.COMMON.UOM',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'uomName',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.parent?.current?.get('disposalType') === 'PART',
      },
    },
    {
      name: 'uomId',
      type: FieldType.number,
      bind: 'uomLov.uomId',
    },
    {
      name: 'uomName',
      type: FieldType.string,
      bind: 'uomLov.uomName',
    },
    {
      name: 'reworkStepLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reworkStepDesc`).d('返修步骤'),
      lovCode: 'MT.ROUTER_STEP',
      ignore: FieldIgnore.always,
      textField: 'description',
      dynamicProps: {
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'REWORK_SOURCE' &&
              !record.get('reworkWorkcellId') &&
              !record.get('reworkOperationId')) ||
            (disposalType === 'PART' &&
              record.get('REWORK_SOURCE') &&
              !record.get('reworkWorkcellId') &&
              !record.get('reworkOperationId'))
          );
        },
        lovPara: ({ dataSet, record }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          const lineDs = dataSet.parent?.children?.ncReportLineList;
          let routerId;
          if (disposalType === 'ALL') {
            routerId = lineDs?.current?.get('routerId');
          } else if (disposalType === 'PART') {
            const parentRecord = (lineDs || []).find(
              _rec =>
                _rec.get('ncRecordType') === record?.get('ncRecordType') &&
                _rec.get('ncObjectType') === record?.get('ncObjectType') &&
                _rec.get('ncObjectCode') === record?.get('ncObjectCode'),
            );
            routerId = parentRecord?.get('routerId');
          }
          return {
            tenantId,
            siteId: dataSet.parent?.current?.get('siteId'),
            routerId,
          };
        },
        disabled: ({ dataSet, record }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          const lineDs = dataSet.parent?.children?.ncReportLineList || null;
          let disableFlag = record.get('reworkWorkcellId') || record.get('reworkOperationId');
          if (disableFlag) {
            return disableFlag;
          }
          if (disposalType === 'ALL') {
            // 整体处置行上需要是相同的EO
            let eoId;
            lineDs?.forEach(_record => {
              if (_record.get('ncObjectType') !== 'EO') {
                disableFlag = true;
              } else if (!eoId) {
                eoId = _record.get('ncObjectId');
                disableFlag = !_record.get('ncObjectId');
              } else if (eoId !== _record.get('ncObjectId')) {
                disableFlag = true;
              }
            });
          } else {
            // 部分处置行上不良对象类型需要为EO
            disableFlag = record.get('ncObjectType') !== 'EO';
          }
          return disableFlag || !dataSet.parent?.current?.get('siteId');
        },
      },
    },
    {
      name: 'reworkStepId',
      bind: 'reworkStepLov.routerStepId',
    },
    {
      name: 'reworkStepDesc',
      bind: 'reworkStepLov.description',
    },
    {
      name: 'reworkWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reworkWorkcellName`).d('返修工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'REWORK_SOURCE' &&
              !record.get('reworkStepId') &&
              !record.get('reworkOperationId')) ||
            (disposalType === 'PART' &&
              record.get('REWORK_SOURCE') &&
              !record.get('reworkStepId') &&
              !record.get('reworkOperationId'))
          );
        },
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet, record }) =>
          !dataSet.parent?.current?.get('siteId') ||
          record.get('reworkStepId') ||
          record.get('reworkOperationId'),
      },
    },
    {
      name: 'reworkWorkcellId',
      bind: 'reworkWorkcellLov.workcellId',
    },
    {
      name: 'reworkWorkcellName',
      bind: 'reworkWorkcellLov.workcellName',
    },
    {
      name: 'reworkOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reworkOperationName`).d('返修工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      textField: 'operationName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'REWORK_SOURCE' &&
              !record.get('reworkStepId') &&
              !record.get('reworkWorkcellId')) ||
            (disposalType === 'PART' &&
              record.get('REWORK_SOURCE') &&
              !record.get('reworkStepId') &&
              !record.get('reworkWorkcellId'))
          );
        },
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet, record }) =>
          !dataSet.parent?.current?.get('siteId') ||
          record.get('reworkStepId') ||
          record.get('reworkWorkcellId'),
      },
    },
    {
      name: 'reworkOperationId',
      bind: 'reworkOperationLov.operationId',
    },
    {
      name: 'reworkOperationName',
      bind: 'reworkOperationLov.operationName',
    },
    {
      name: 'degradeLevel',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_LEVEL',
      label: intl.get(`${modelPrompt}.degradeLevel`).d('降级等级'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('degradeMaterialId'),
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'DEGRADE' &&
              !record?.get('degradeMaterialId')) ||
            (disposalType === 'PART' && record.get('DEGRADE') && !record?.get('degradeMaterialId'))
          );
        },
      },
    },
    {
      name: 'degradeDegree',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_DEGREE',
      label: intl.get(`${modelPrompt}.degradeDegree`).d('降级程度'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('degradeMaterialId'),
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'DEGRADE' &&
              !record?.get('degradeMaterialId')) ||
            (disposalType === 'PART' && record.get('DEGRADE') && !record?.get('degradeMaterialId'))
          );
        },
      },
    },
    {
      name: 'degradeMaterialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.degradeMaterialName`).d('降级物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet, record }) =>
          !dataSet.parent?.current?.get('siteId') || record.get('degradeLevel'),
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'DEGRADE' &&
              !record?.get('degradeLevel')) ||
            (disposalType === 'PART' && record.get('DEGRADE') && !record?.get('degradeLevel'))
          );
        },
      },
    },
    {
      name: 'degradeMaterialId',
      bind: 'degradeMaterialLov.materialId',
    },
    {
      name: 'degradeMaterialName',
      bind: 'degradeMaterialLov.materialName',
    },
    {
      name: 'revisionFlag',
      bind: 'degradeMaterialLov.revisionFlag',
    },
    {
      name: 'degradeRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeRevisionCode`).d('降级物料版本'),
      bind: 'degradeMaterialLov.currentRevisionCode',
      textField: 'description',
      valueField: 'description',
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: item?.kid || uuid(),
                  description: item?.description || item,
                };
              });
            }
            if (record) {
              if (firstlyQueryData.length > 0) {
                if (!record?.get('revisionCode')) {
                  record?.init('revisionCode', firstlyQueryData[0].description);
                }
              } else {
                record?.init('revisionCode');
              }
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record?.get('degradeMaterialId') ||
            record?.get('revisionFlag') !== 'Y' ||
            record.get('degradeLevel')
          );
        },
        required: ({ record }) => {
          return record?.get('revisionFlag') === 'Y' && record?.get('degradeMaterialId');
        },
        lovPara: ({ record, dataSet }) => {
          return {
            tenantId,
            siteIds: [dataSet.parent?.current?.get('siteId')],
            materialId: record?.get('degradeMaterialId') || undefined,
          };
        },
      },
    },
    {
      name: 'dischargeWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dischargeWorkcellName`).d('排出工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          workcellType: 'STATION',
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' && record.get('dispositionFunction') === 'EO_DISCHARGE') ||
            (disposalType === 'PART' && record.get('EO_DISCHARGE'))
          );
        },
      },
    },
    {
      name: 'dischargeWorkcellId',
      bind: 'dischargeWorkcellLov.workcellId',
    },
    {
      name: 'dischargeWorkcellName',
      bind: 'dischargeWorkcellLov.workcellName',
    },
    {
      name: 'crossOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.crossOperationName`).d('跨订单拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      textField: 'operationName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'CROSS_WO_INTERCEPT') ||
            (disposalType === 'PART' && record.get('CROSS_WO_INTERCEPT'))
          );
        },
      },
    },
    {
      name: 'crossOperationId',
      bind: 'crossOperationLov.operationId',
    },
    {
      name: 'crossOperationName',
      bind: 'crossOperationLov.operationName',
    },
    {
      name: 'interceptOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.interceptOperationName`).d('拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      textField: 'operationName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'CONCESSION_INTERCEPTION' &&
              !record.get('interceptRouterStepId') &&
              !record.get('interceptWorkcellId')) ||
            (disposalType === 'PART' &&
              record.get('CONCESSION_INTERCEPTION') &&
              !record.get('interceptRouterStepId') &&
              !record.get('interceptWorkcellId'))
          );
        },
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet, record }) =>
          !dataSet.parent?.current?.get('siteId') ||
          record.get('interceptRouterStepId') ||
          record.get('interceptWorkcellId'),
      },
    },
    {
      name: 'interceptOperationId',
      bind: 'interceptOperationLov.operationId',
    },
    {
      name: 'interceptOperationName',
      bind: 'interceptOperationLov.operationName',
    },
    {
      name: 'interceptWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.interceptWorkcellName`).d('拦截工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'CONCESSION_INTERCEPTION' &&
              !record.get('interceptRouterStepId') &&
              !record.get('interceptOperationId')) ||
            (disposalType === 'PART' &&
              record.get('CONCESSION_INTERCEPTION') &&
              !record.get('interceptRouterStepId') &&
              !record.get('interceptOperationId'))
          );
        },
        disabled: ({ record }) =>
          record.get('interceptRouterStepId') || record.get('interceptOperationId'),
      },
    },
    {
      name: 'interceptWorkcellId',
      bind: 'interceptWorkcellLov.workcellId',
    },
    {
      name: 'interceptWorkcellName',
      bind: 'interceptWorkcellLov.workcellName',
    },
    {
      name: 'interceptRouterStepLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.interceptRouterStep`).d('拦截步骤'),
      lovCode: 'MT.ROUTER_STEP',
      ignore: FieldIgnore.always,
      textField: 'description',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record, dataSet }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          return (
            (disposalType === 'ALL' &&
              record.get('dispositionFunction') === 'CONCESSION_INTERCEPTION' &&
              !record.get('interceptWorkcellId') &&
              !record.get('interceptOperationId')) ||
            (disposalType === 'PART' &&
              record.get('CONCESSION_INTERCEPTION') &&
              !record.get('interceptWorkcellId') &&
              !record.get('interceptOperationId'))
          );
        },
        lovPara: ({ dataSet, record }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          const lineDs = dataSet.parent?.children?.ncReportLineList;
          let routerId;
          if (disposalType === 'ALL') {
            routerId = lineDs?.current?.get('routerId');
          } else if (disposalType === 'PART') {
            const parentRecord = (lineDs || []).find(
              _rec =>
                _rec.get('ncRecordType') === record?.get('ncRecordType') &&
                _rec.get('ncObjectType') === record?.get('ncObjectType') &&
                _rec.get('ncObjectCode') === record?.get('ncObjectCode'),
            );
            routerId = parentRecord?.get('routerId');
          }
          return {
            tenantId,
            siteId: dataSet.parent?.current?.get('siteId'),
            routerId,
          };
        },
        disabled: ({ dataSet, record }) => {
          const disposalType = dataSet.parent?.current?.get('disposalType');
          const lineDs = dataSet.parent?.children?.ncReportLineList || null;
          let disableFlag = record.get('interceptWorkcellId') || record.get('interceptOperationId');
          if (disableFlag) {
            return disableFlag;
          }
          if (disposalType === 'ALL') {
            // 整体处置行上需要是相同的EO
            let eoId;
            lineDs?.forEach(_record => {
              if (_record.get('ncObjectType') !== 'EO') {
                disableFlag = true;
              } else if (!eoId) {
                eoId = _record.get('ncObjectId');
                disableFlag = !_record.get('ncObjectId');
              } else if (eoId !== _record.get('ncObjectId')) {
                disableFlag = true;
              }
            });
          } else {
            // 部分处置行上不良对象类型需要为EO
            disableFlag = record.get('ncObjectType') !== 'EO';
          }
          return disableFlag || !dataSet.parent?.current?.get('siteId');
        },
      },
    },
    {
      name: 'interceptRouterStepId',
      bind: 'interceptRouterStepLov.routerStepId',
    },
    {
      name: 'interceptRouterStepDesc',
      bind: 'interceptRouterStepLov.description',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'disposalUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.disposalUserName`).d('处置意见给出人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'disposalUserId',
      type: FieldType.string,
      bind: 'disposalUserLov.id',
    },
    {
      name: 'disposalUserName',
      type: FieldType.string,
      bind: 'disposalUserLov.realName',
    },
    {
      name: 'disposalTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.disposalTime`).d('处置意见给出时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'disposalApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalApartment`).d('处置意见给出部门'),
    },
  ],
});

const lineDtlDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content',
  primaryKey: 'ncReportLineDtlId',
  fields: [
    {
      name: 'ncReportLineDtlId',
      type: FieldType.number,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      defaultValue: 10,
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      required: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'ncCodeId',
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCodeCode',
      bind: 'ncCodeLov.ncCode',
    },
    {
      name: 'defectLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.defectLevel`).d('缺陷等级'),
      lookupCode: 'MT.METHOD.NC_DEFECT_LEVEL',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('ncCodeLov');
        },
      },
    },

    {
      name: 'ncCodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码描述'),
      bind: 'ncCodeLov.description',
    },
    {
      name: 'componentMaterialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.componentMaterialAndRevision`).d('组件物料/版本'),
      lovCode: 'MT.MES.COMPONENT_MATERIAL',
      textField: 'materialName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          eoId: dataSet.parent?.current?.get('ncObjectId'),
          siteId: dataSet.parent?.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('ncObjectId'),
        required: ({ dataSet }) =>
          dataSet.parent?.current?.get('ncRecordType') === 'EO_MATERIAL_NC',
      },
    },
    {
      name: 'componentMaterialId',
      bind: 'componentMaterialLov.materialId',
    },
    {
      name: 'componentMaterialName',
      bind: 'componentMaterialLov.materialName',
    },
    {
      name: 'bomComponentId',
      bind: 'componentMaterialLov.bomComponentId',
    },
    {
      name: 'routerStepId',
      bind: 'componentMaterialLov.routerStepId',
    },
    {
      name: 'identifyType',
      bind: 'componentMaterialLov.identifyType',
    },
    {
      name: 'componentRevisionCode',
      type: FieldType.string,
      disabled: true,
      bind: 'componentMaterialLov.revisionCode',
    },
    {
      name: 'componentMaterialLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.componentMaterialLotCode`).d('组件物料批'),
      lovCode: 'MT.MES.COMPONENT_MATERIAL_LOT',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record, dataSet }) => ({
          tenantId,
          materialId: record?.get('componentMaterialId'),
          revisionCode: record?.get('componentRevisionCode'),
          eoId: dataSet.parent?.current?.get('ncObjectId'),
          bomComponentId: record.get('bomComponentId'),
          routerStepId: record.get('routerStepId'),
        }),
        disabled: ({ record }) => !record?.get('componentMaterialId'),
        required: ({ dataSet }) =>
          dataSet.parent?.current?.get('ncRecordType') === 'EO_MATERIAL_NC',
      },
    },
    {
      name: 'componentMaterialLotId',
      type: FieldType.number,
      bind: 'componentMaterialLotLov.materialLotId',
    },
    {
      name: 'componentMaterialLotCode',
      type: FieldType.string,
      bind: 'componentMaterialLotLov.materialLotCode',
    },
    {
      name: 'sumAssembleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumAssembleQty`).d('组件装配数量'),
      disabled: true,
      bind: 'componentMaterialLotLov.sumAssembleQty',
    },
    {
      name: 'rootCauseOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.rootCauseOperationName`).d('产生问题的原工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'rootCauseOperationId',
      bind: 'rootCauseOperationLov.operationId',
    },
    {
      name: 'rootCauseOperationName',
      bind: 'rootCauseOperationLov.operationName',
    },
    {
      name: 'rootCauseWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.rootCauseWorkcellName`).d('产生问题的原工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'rootCauseWorkcellId',
      bind: 'rootCauseWorkcellLov.workcellId',
    },
    {
      name: 'rootCauseWorkcellName',
      bind: 'rootCauseWorkcellLov.workcellName',
    },
    {
      name: 'rootCauseEquipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.rootCauseEquipmentName`).d('产生问题的原设备'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'rootCauseEquipmentId',
      bind: 'rootCauseEquipmentLov.equipmentId',
    },
    {
      name: 'rootCauseEquipmentCode',
      bind: 'rootCauseEquipmentLov.equipmentCode',
    },
    {
      name: 'defectQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.defectQty`).d('缺陷数量'),
      required: true,
      min: 0,
      step: 1,
      nonStrictStep: true,
    },
    {
      name: 'uomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
      required: true,
      lovCode: 'MT.COMMON.UOM',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'uomName',
    },
    {
      name: 'uomId',
      type: FieldType.number,
      bind: 'uomLov.uomId',
    },
    {
      name: 'uomName',
      type: FieldType.string,
      bind: 'uomLov.uomName',
    },
    {
      name: 'defectLocation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLocation`).d('具体位置'),
    },
    {
      name: 'defectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectReason`).d('不良原因'),
    },
    {
      name: 'responsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('不良责任人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
    },
    {
      name: 'responsibleUserId',
      bind: 'responsibleUserLov.id',
    },
    {
      name: 'responsibleUserName',
      bind: 'responsibleUserLov.realName',
    },
    {
      name: 'responsibleApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleApartment`).d('不良责任部门'),
    },
    {
      name: 'ncRecordUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecordUserName`).d('不良代码记录人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'ncRecordUserId',
      type: FieldType.string,
      bind: 'ncRecordUserLov.id',
    },
    {
      name: 'ncRecordUserName',
      type: FieldType.string,
      bind: 'ncRecordUserLov.realName',
    },
    {
      name: 'ncRecordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncRecordTime`).d('不良代码记录时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncDesc`).d('不良描述'),
    },
    {
      name: 'referenceArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.referenceArea`).d('参考区域'),
      bind: 'componentMaterialLotLov.referenceArea',
    },
    {
      name: 'referencePoint',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.referencePoint`).d('参考点'),
      bind: 'componentMaterialLotLov.referencePoint',
    },
  ],
  events: {
    update: ({ name, dataSet }) => {
      const detailDs = dataSet.parent.parent;
      const lineDs = dataSet.parent;
      if (name === 'remark') {
        lineDs.current?.init('ncDesc', updateLineNcDesc(lineDs.current));
        detailDs.current?.init('ncDesc', updateHeadNcDesc(detailDs.current));
      }
    },
  },
});

export { detailDS, ncRecordLineDS, disposalDS, lineDtlDS, scanFormDS };
