/**
 * @Description: 问题管理平台-登记问题页面组件
 * @Author: <EMAIL>
 * @Date: 2023/7/5 20:04
 */
import React, { useEffect } from 'react';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import {
  Attachment,
  Button,
  DatePicker,
  DateTimePicker,
  Dropdown,
  Form,
  Lov,
  Menu,
  NumberField,
  Select,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { observer } from 'mobx-react';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import styles from '../index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';

const RegisterProblemComponent = ({
  dataSet,
  leadProblemDs,
  problemGroup,
  userRole,
  problemStatus,
  handleSaveCategoryDetail,
  handleSubmitProblemInfo,
  queryMaterialLotInfo,
}) => {

  useEffect(()=>{
    if(problemStatus){
      dataSet.setState('problemStatus',problemStatus)
    }
  },[problemStatus])
  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'problem-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const handleCancel = dataSet => {
    dataSet.reset();
    dataSet.setState('canEdit', false);
    dataSet.setState('nextReportDateEdit', false);
  };

  const handleChangeMaterialLot = value => {
    if (value?.materialId) {
      queryMaterialLotInfo({
        params: value?.materialId,
        onSuccess: res => {
          dataSet.current?.set('batteryPackModel', res);
        },
      });
    } else {
      dataSet.current?.set('batteryPackModel', undefined);
    }
  };

  const RenderRegisterButton = observer(({ dataSet, problemStatus, userRole }) => {
    if (dataSet.getState('canEdit')) {
      return (
        <>
          <Button icon="close" funcType={FuncType.flat} onClick={() => handleCancel(dataSet)}>
            {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
          </Button>
          <Button
            icon="save"
            funcType={FuncType.flat}
            onClick={() => handleSaveCategoryDetail('register')}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </Button>
        </>
      );
    }

    const menu = (
      <Menu className={styles['split-menu']} style={{ width: '100px' }}>
        {(
          dataSet
            .getField('externalReportFlag')
            ?.getOptions(dataSet.current)
            ?.toData() || []
        ).map(item => {
          return (
            <Menu.Item key={item.value}>
              <a
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => handleSaveCategoryDetail('reportFlag', item.value)}
              >
                {item.meaning}
              </a>
            </Menu.Item>
          );
        })}
      </Menu>
    );

    return (
      <>
        {problemGroup === 'PREVIEW' && (
          <Dropdown
            overlay={menu}
            disabled={
              !userRole.includes('LEAD_PERSON') ||
              ['NEW', 'PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus)
            }
          >
            <Button
              funcType={FuncType.flat}
              disabled={
                !userRole.includes('LEAD_PERSON') ||
                ['NEW', 'PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus)
              }
            >
              {intl.get(`${modelPrompt}.button.externalReportFlag`).d('外审报告体现')}
            </Button>
          </Dropdown>
        )}
        <Button
          icon="send-o"
          funcType={FuncType.flat}
          disabled={problemStatus !== 'NEW' || !userRole.includes('REGISTER_PERSON')}
          onClick={() => handleSubmitProblemInfo('register')}
        >
          {intl.get(`${modelPrompt}.button.submit`).d('提交')}
        </Button>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={
            !(
              (userRole.includes('LEAD_PERSON') &&
                ['PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus)) ||
              (userRole.includes('REGISTER_PERSON') && ['NEW', 'DRAFT'].includes(problemStatus))
            )
          }
          onClick={() => dataSet.setState('canEdit', true)}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </Button>
      </>
    );
  });

  const RenderLeadButton = observer(({ dataSet, problemStatus, userRole }) => {
    if (dataSet.getState('canEdit') || dataSet.getState('nextReportDateEdit')) {
      return (
        <>
          <Button icon="close" funcType={FuncType.flat} onClick={() => handleCancel(dataSet)}>
            {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
          </Button>
          <Button
            icon="save"
            funcType={FuncType.flat}
            onClick={() => handleSaveCategoryDetail('lead')}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </Button>
        </>
      );
    }

    return (
      <>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={
            !(
              userRole.includes('LEAD_PERSON') &&
              ['PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus) &&
              !dataSet.getState('nextReportDateEdit')
            )
          }
          onClick={() => {
            dataSet.setState('canEdit', true);
          }}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </Button>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={
            !userRole.includes('LEAD_PERSON') ||
            dataSet.getState('canEdit')
          }
          onClick={() => {
            dataSet.setState('nextReportDateEdit', true);
          }}
        >
          {intl.get(`${modelPrompt}.button.nextReportDateEdit`).d('报告日期编辑')}
        </Button>
      </>
    );
  });

  return (
    <Collapse
      bordered={false}
      defaultActiveKey={['basicInfo', 'systemWo', 'lead']}
      collapsible="icon"
      className={styles['collapse-style']}
    >
      <Panel
        key="basicInfo"
        header={intl.get(`${modelPrompt}.title.basicInfo`).d('具体信息')}
        dataSet={dataSet}
        extra={
          <RenderRegisterButton
            dataSet={dataSet}
            userRole={userRole}
            problemStatus={problemStatus}
          />
        }
      >
        {problemGroup === 'QUALITY' && (
          <Form
            dataSet={dataSet}
            columns={3}
            disabled={
              !(
                (userRole.includes('LEAD_PERSON') &&
                  ['PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus)) ||
                (userRole.includes('REGISTER_PERSON') && ['NEW', 'DRAFT'].includes(problemStatus))
              )
            }
          >
            <Lov name="ncReportNum" />
            <Lov name="inspectDocNum" />
            <Select name="shiftCode" />
            <Select name="frequency" />
            <Select name="findingMethod" />
            <Select name="problemCategory" />
            <Select name="productMode" />
            <TextField name="trialProductionLine" />
            <Lov name="siteLov" />
            <Lov name="prodLineLov" />
            <Lov name="processWorkcellLov" />
            <Lov name="equipmentLov" />
            <Lov name="locatorCode" />
            <Select name="projectName" />
            <Select name="projectPhase" />
            <Lov name="materialLov" />
            <TextField name="traceBarcodeList" />
            <TextField name="itemGroupDesc" />
            <TextField name="materialName" />
            <Select name="ncType" />
            <Lov name="ncCodeLov" />
            <TextField name="ncCodeDescriptions" />
            <NumberField name="ncQuantity" />
            <Select name="qualityProblemType" />
            <Lov name="supplierLov" />
            <TextField name="qisProblemRequestCode" />
            <Attachment {...attachmentProps} />
          </Form>
        )}
        {problemGroup === 'MANUFACTURE' && (
          <Form
            dataSet={dataSet}
            columns={3}
            disabled={
              !(
                (userRole.includes('LEAD_PERSON') &&
                  ['PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus)) ||
                (userRole.includes('REGISTER_PERSON') && ['NEW', 'DRAFT'].includes(problemStatus))
              )
            }
          >
            <Lov name="ncReportNum" />
            <Lov name="inspectDocNum" />
            <Select name="shiftCode" />
            <Select name="frequency" />
            <Select name="findingMethod" />
            <Select name="problemCategory" />
            <Select name="productMode" />
            <TextField name="trialProductionLine" />
            <Lov name="siteLov" />
            <Lov name="prodLineLov" />
            <Lov name="processWorkcellLov" />
            <Lov name="equipmentLov" />
            <Select name="projectName" />
            <Select name="projectPhase" />
            <Lov name="materialLov" />
            <TextField name="traceBarcodeList" />
            <TextField name="itemGroupDesc" />
            <TextField name="materialName" />
            <Select name="ncType" />
            <Lov name="ncCodeLov" />
            <TextField name="ncCodeDescriptions" />
            <NumberField name="ncQuantity" />
            <Select name="qualityProblemType" />
            <Lov name="supplierLov" />
            <TextField name="qisProblemRequestCode" />
            <Attachment {...attachmentProps} />
          </Form>
        )}
        {problemGroup === 'MARKET' && (
          <Form
            dataSet={dataSet}
            columns={3}
            disabled={
              !(
                (userRole.includes('LEAD_PERSON') &&
                  ['PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus)) ||
                (userRole.includes('REGISTER_PERSON') && ['NEW', 'DRAFT'].includes(problemStatus))
              )
            }
          >
            <Lov name="siteLov" />
            <Select name="frequency" />
            <Select name="marketProblemType" />
            <Lov name="batteryMatLotLov" onChange={handleChangeMaterialLot} />
            <TextField name="batteryPackModel" />
            <Lov name="hostPlantLov" />
            <Select name="vehicleModel" searchable />
            <Select name="projectName" />
            <Select name="projectPhase" />
            <TextField name="vinNum" />
            <DateTimePicker name="vehicleProductionTime" />
            <DateTimePicker name="salesTime" />
            <DateTimePicker name="faultTime" />
            <DateTimePicker name="maintainTime" />
            <TextField name="faultMileage" />
            <TextField name="maintainReason" />
            <TextField name="maintainMeasure" />
            <TextField name="maintainShopCode" />
            <TextField name="maintainShopName" />
            <Select name="faultLevel" />
            <Select name="majorFaultMode" />
            <Select name="ware" />
            <TextField name="softwareVersion" />
            <Lov name="qualityFeedbackDocLov" />
            <TextField name="itemCode" />
            <TextField name="itemName" />
            <Lov name="materialLov" />
            <TextField name="materialName" />
            <Select name="majorDivision1" />
            <Select name="majorDivision2" />
            <Select name="majorDivision3" />
            <Attachment {...attachmentProps} />
            <TextArea name="faultDesc" colSpan={3} autoSize={{ minRows: 2, maxRows: 8 }} />
            <NumberField name="attribute2" />
          </Form>
        )}
        {problemGroup === 'PREVIEW' && (
          <Form
            dataSet={dataSet}
            columns={3}
            disabled={
              !(
                (userRole.includes('LEAD_PERSON') &&
                  ['PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus)) ||
                (userRole.includes('REGISTER_PERSON') && ['NEW', 'DRAFT'].includes(problemStatus))
              )
            }
          >
            <Select name="frequency" />
            <Select name="findingMethod" />
            <Select name="problemType" />
            <Lov name="siteLov" />
            <Lov name="prodLineLov" />
            <Lov name="processWorkcellLov" />
            <Lov name="equipmentLov" />
            <Select name="projectName" />
            <Select name="projectPhase" />
            <Lov name="sysReviewStandardLov" />
            <Select name="conformityLevel" />
            <Select name="problemProperty" />
            <TextField name="sysReviewPlanCode" />
            <Select name="influenceRange" />
            <Select name="externalReportFlag" />
            <Attachment {...attachmentProps} />
          </Form>
        )}
      </Panel>
      {problemGroup === 'MARKET' && (
        <Panel key="systemWo" header={intl.get(`${modelPrompt}.title.systemWo`).d('对接系统工单')}>
          <Form
            dataSet={dataSet}
            columns={3}
            disabled={
              !(
                (userRole.includes('LEAD_PERSON') &&
                  ['PUBLISH', 'FOLLOWING', 'RELEASED'].includes(problemStatus)) ||
                (userRole.includes('REGISTER_PERSON') && ['NEW', 'DRAFT'].includes(problemStatus))
              )
            }
          >
            <TextField name="alarmDocNum" />
            <TextField name="reworkWoNum" />
            <TextField name="warrantyClaimDocNum" />
            <TextField name="dataSource" />
          </Form>
        </Panel>
      )}
      <Panel
        key="lead"
        header={intl.get(`${modelPrompt}.title.lead`).d('跟进人填写')}
        extra={
          <RenderLeadButton
            dataSet={leadProblemDs}
            userRole={userRole}
            problemStatus={problemStatus}
          />
        }
      >
        <Form
          dataSet={leadProblemDs}
          columns={3}
          disabled={
            !userRole.includes('LEAD_PERSON') ||
            !['RELEASED', 'FOLLOWING', 'PUBLISH'].includes(problemStatus)
          }
        >
          {['QUALITY', 'PREVIEW', 'MANUFACTURE'].includes(problemGroup) && (
            <>
              <Select name="influenceLevel" />
              <Select name="severityLevel" />
              <TextField name="responsiblePersonUnitCompanyName" />
              <Select name="solutionTool" />
              <DatePicker name="nextReportDate" />
            </>
          )}
          {problemGroup === 'MARKET' && (
            <>
              <Select name="influenceLevel" />
              <Select name="severityLevel" />
              <TextField name="responsiblePersonUnitCompanyName" />
              <Select name="solutionTool" />
              <Select name="repeatFlag" />
              <Lov name="mergeProblemLov" />
              <DatePicker name="nextReportDate" />
            </>
          )}
        </Form>
      </Panel>
    </Collapse>
  );
};
export default RegisterProblemComponent;
