import moment from 'moment';
import { DataSetProps, DataToJSON } from 'choerodon-ui/pro/lib/data-set/interface';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { omit } from 'lodash';
import { HALM_ORI } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ORI}/v1/${organizationId}`;
// const apiPrefix = `http://127.0.0.1:4523/m1/964148-0-4e810015/v1/${organizationId}`;

const detailDs = (advDs): DataSetProps => ({
  autoCreate: true,
  autoQuery: false,
  autoQueryAfterSubmit: false,
  dataToJSON: DataToJSON.all,
  fields: [
    {
      label: getLang('PLAN_NAME'),
      name: 'maintainPlanName',
      type: FieldType.intl,
      maxLength: 240,
      required: true,
    },
    {
      label: getLang('PLAN_CODE'),
      name: 'maintainPlanCode',
      type: FieldType.string,
      maxLength: 40,
    },
    {
      label: getLang('CYCLE_TYPE'),
      name: 'cycleType',
      lookupCode: 'ALM.STRATEGIES_COMMON_TYPE',
      type: FieldType.string,
      disabled: true,
      required: true,
    },
    {
      label: getLang('CYCLE_UOM'),
      name: 'cycleUom',
      lookupCode: 'AMTC.STRATEGIES_COMMON_UOM',
      type: FieldType.string,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('cycleType') === 'TIME_CYCLE'; // 仪表型没用这个字段 用的 meterUom
        },
      },
    },
    {
      name: 'cycleInterval',
      type: FieldType.number,
      min: 1,
      precision: 0,
      required: true,
      dynamicProps: {
        label: ({ record }) => {
          return record.get('cycleType') === 'TIME_CYCLE'
            ? getLang('CYCLE')
            : getLang('CYCLE_WHEN_METER');
        },
        max: ({ record }) => {
          return record.get('cycleType') === 'TIME_CYCLE' ? 999 : 99999;
        },
      },
    },
    {
      label: getLang('WO_TYPE'),
      name: 'woTypeLov',
      type: FieldType.object,
      lovCode: 'AMTC.WORKORDERTYPES',
      lovPara: {
        // 只查 “保养/技改大修类” 且 启用的
        woBasicType: 'FAULT_MAINTAIN_TYPE',
      },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'woTypeId',
      type: FieldType.number,
      bind: 'woTypeLov.woTypeId',
    },
    {
      name: 'woTypeName',
      label: getLang('WO_TYPE'),
      type: FieldType.string,
      bind: 'woTypeLov.woTypeName',
    },
    {
      label: getLang('START_DATE'),
      name: 'startDate',
      type: FieldType.dateTime,
      required: true,
      defaultValue: new Date(),
      max: 'endDate',
    },
    {
      label: getLang('END_DATE'),
      name: 'endDate',
      type: FieldType.dateTime,
      min: 'startDate',
    },
    {
      name: 'enabledFlag',
      label: getLang('ENABLED_FLAG'),
      type: FieldType.boolean,
      trueValue: 1,
      falseValue: 0,
      defaultValue: 0,
    },
    // url带出
    {
      name: 'maintSiteId',
      type: FieldType.number,
      required: true,
    },
    {
      name: 'locationId',
      type: FieldType.number,
      required: true,
    },
    {
      name: 'plannerGroupId',
      type: FieldType.string,
      multiple: ',',
      bind: 'plannerGroupLov.workcenterId',
    },
    {
      name: 'plannerGroupName',
      label: getLang('PLANNER_GROUP'),
      type: FieldType.string,
      multiple: ',',
      bind: 'plannerGroupLov.workcenterName',
    },
    {
      name: 'plannerGroupLov',
      label: getLang('PLANNER_GROUP'),
      type: FieldType.object,
      lovCode: 'AMTC.WORKCENTERS',
      ignore: FieldIgnore.always,
      required: true,
      multiple: ',',
    },
    {
      name: 'plannerId',
      type: FieldType.number,
    },
    {
      name: 'plannerName',
      label: getLang('PLANNER'),
      type: FieldType.string,
    },
    {
      name: 'ownerGroupId',
      type: FieldType.string,
      bind: 'ownerGroupLov.workcenterId',
      multiple: ',',
    },
    {
      name: 'ownerGroupName',
      type: FieldType.string,
      label: getLang('OWNER_GROUP'),
      multiple: ',',
      bind: 'ownerGroupLov.workcenterName',
    },
    {
      name: 'ownerGroupLov',
      label: getLang('OWNER_GROUP'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'AMTC.WORKCENTERS',
      required: true,
      multiple: ',',
    },
    {
      name: 'ownerId',
      type: FieldType.number,
    },
    {
      name: 'ownerName',
      label: getLang('OWNER'),
      type: FieldType.string,
    },
    {
      name: 'defaultJobFlag',
      label: getLang('DEFAULT_JOB'),
      type: FieldType.boolean,
      trueValue: 1,
      falseValue: 0,
      defaultValue: 0,
    },
    {
      label: getLang('PLAN_SCHED_BASE'),
      name: 'planSchedBase',
      type: FieldType.string,
      lookupCode: 'ALM.BASE_PLAN_SCHED',
      required: true,
    },
    {
      label: getLang('LAST_WORK_TIME'),
      name: 'lastWorkTime',
      type: FieldType.dateTime,
      dynamicProps: {
        required: ({ record }) => {
          const cycleType = record.get('cycleType');
          const planSchedBase = record.get('planSchedBase');
          return cycleType === 'TIME_CYCLE' && planSchedBase === 'LAST_WORK_TIME';
        },
      },
      defaultValue: moment().startOf('day').set('hours', 8).set('minutes', 30),
    },
    {
      label: getLang('CYCLE_STANDARD_TIME'),
      name: 'cycleStandardTime',
      type: FieldType.dateTime,
      dynamicProps: {
        required: ({ record }) => {
          const cycleType = record.get('cycleType');
          return cycleType === 'TIME_CYCLE';
        },
      },
      bind: 'lastWorkTime',
    },
    {
      label: getLang('DURATION_SCHED'),
      name: 'durationScheduled',
      type: FieldType.number,
      step: 0.01,
      min: 0,
      defaultValue: 0,
      required: true,
      pattern: /^\d{1,3}(\.\d{1,2})?$/g,
    },
    {
      label: getLang('DURATION_UOM'),
      name: 'durationUom',
      type: FieldType.string,
      lookupCode: 'AMTC.DURATION_UNIT',
      defaultValue: 'HOUR',
      required: true,
    },
    {
      label: getLang('METER'),
      name: 'meterLov',
      lovCode: 'ALM.MAINTAIN_PLAN_METERS',
      type: FieldType.object,
      ignore: FieldIgnore.always,
      disabled: true,
    },
    {
      name: 'meterId',
      type: FieldType.number,
      bind: 'meterLov.meterId',
    },
    {
      label: getLang('METER'),
      name: 'meterName',
      type: FieldType.string,
      bind: 'meterLov.meterName',
    },
    {
      name: 'meterUom',
      type: FieldType.string,
    },
    {
      label: getLang('LAST_WORK_VALUE'),
      name: 'lastWorkValue',
      type: FieldType.number,
      dynamicProps: {
        required: ({ record }) => {
          const cycleType = record.get('cycleType');
          const planSchedBase = record.get('planSchedBase');
          return cycleType === 'METER_CYCLETRIGGE' && planSchedBase === 'LAST_WORK_VALUE';
        },
      },
      pattern: /^-?\d{1,11}(\.\d{1,5})?$/,
    },
    {
      label: getLang('STANDARD_VALUE'),
      name: 'standardValue',
      type: FieldType.number,
      pattern: /^-?\d{1,11}(\.\d{1,5})?$/,
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('cycleType') === 'METER_CYCLETRIGGE';
        },
      },
    },
    {
      label: getLang('NEXT_WORK_VALUE'),
      name: 'nextWorkValue',
      type: FieldType.number,
      disabled: true,
    },
    {
      label: getLang('LAST_READING_VALUE'),
      name: 'lastReadingValue',
      type: FieldType.number,
      disabled: true,
    },
    {
      label: getLang('PLAN_TIME_RULE'),
      name: 'planTimeRule',
      type: FieldType.string,
      lookupCode: 'ALM.PLAN_TIME_RULE',
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('cycleType') === 'METER_CYCLETRIGGE';
        },
      },
      defaultValue: 'TIME_TRIGGER', // 触发时间
    },
    {
      label: getLang('DELAY_TIME'),
      name: 'delayTime',
      type: FieldType.number,
      defaultValue: 1,
      min: 1,
      max: 99,
      precision: 0,
      defaultValidationMessages: {
        rangeUnderflow: getLang('LENGTH_RULE'),
        rangeOverflow: getLang('LENGTH_RULE'),
      },
      dynamicProps: {
        required: ({ record }) => {
          const cycleType = record.get('cycleType');
          const planTimeRule = record.get('planTimeRule');
          return (
            cycleType === 'METER_CYCLETRIGGE' &&
            ['OTHER_TIME', 'TRIGGER_TIME_DELAY_HOURS'].includes(planTimeRule)
          );
        },
      },
    },
    {
      label: getLang('PLAN_START_TIME'),
      name: 'planStartTime',
      type: FieldType.time,
      format: 'HH:mm:ss',
      dynamicProps: {
        required: ({ record }) => {
          const cycleType = record.get('cycleType');
          const planTimeRule = record.get('planTimeRule');
          return cycleType === 'METER_CYCLETRIGGE' && ['OTHER_TIME'].includes(planTimeRule);
        },
      },
    },
  ],
  transport: {
    read: ({ data, params }) => {
      const url = `${apiPrefix}/maintain-plans/${data.id}`;
      return {
        url,
        method: 'GET',
        params,
        transformResponse: res => {
          const originData = JSON.parse(res);
          const newData = {
            ...originData,
          };
          if (
            originData.cycleType === 'METER_CYCLETRIGGE' &&
            ['OTHER_TIME'].includes(originData.planTimeRule)
          ) {
            // 处理一下计划开始时间 拼上当前日期 （因为组件要有日期才能正常显示）
            const [_h, _m, _s] = originData.planStartTime.split(':');
            newData.planStartTime = moment().set('hour', _h).set('minute', _m).set('second', _s);
          }
          return newData;
        },
      };
    },
    submit: ({ data }) => {
      const advData = advDs.current.toData();
      const unwantedFields = ['_token', 'objectVersionNumber'];
      if (data[0].cycleType === 'TIME_CYCLE') {
        unwantedFields.push('writeBackRule');
      }
      const pureAdvData = omit(advData, unwantedFields);
      const current =
        data[0].cycleType === 'TIME_CYCLE'
          ? omit({ ...data[0] }, ['delayTime', 'planTimeRule'])
          : { ...data[0] };

      const newData = {
        ...current,
        ...pureAdvData,
        tenantId: organizationId,
        planStartTime: data[0].planStartTime?.split(' ')[1],
      };
      return {
        url: `${apiPrefix}/maintain-plans`,
        data: newData,
        method: newData.maintainPlanId ? 'PUT' : 'POST',
      };
    },
  },
});

const advDs = (): DataSetProps => ({
  autoCreate: true,
  autoQuery: false,
  fields: [
    {
      name: 'leadTime',
      type: FieldType.number,
      defaultValue: 0,
      min: 0,
      max: 999,
      precision: 0,
      required: true,
      dynamicProps: {
        label: ({ dataSet }) => {
          // @ts-ignore
          return dataSet?.cycleType === 'TIME_CYCLE' ? getLang('LEAD_TIME') : getLang('LEAD_VALUE');
        },
      },
    },
    {
      label: getLang('FORECAST_TIME_BASIS'),
      name: 'forecastTimeBasis',
      type: FieldType.string,
      lookupCode: 'AMTC.PREDICTION_TIME_BASIS',
      defaultValue: 'planned_start',
      required: true,
    },
    {
      label: getLang('DEFAULT_COMMIT'),
      name: 'defaultCommit',
      type: FieldType.number,
      trueValue: 1,
      falseValue: 0,
      defaultValue: 1,
    },
    {
      label: getLang('WRITE_BACK_RULE'),
      name: 'writeBackRule',
      type: FieldType.string,
      lookupCode: 'ALM.MAINTAIN_METER_VALUE_BACK',
      defaultValue: 'RELEASED_VALUE',
      dynamicProps: {
        required: ({ dataSet }) => {
          // @ts-ignore
          return dataSet?.cycleType === 'METER_CYCLETRIGGE';
        },
      },
    },
  ],
  transport: {
    read: ({ data, params }) => {
      const url = `${apiPrefix}/maintain-plan-pluss/${data.planId}`;
      return {
        url,
        method: 'GET',
        params,
      };
    },
  },
});

export { detailDs, advDs };
