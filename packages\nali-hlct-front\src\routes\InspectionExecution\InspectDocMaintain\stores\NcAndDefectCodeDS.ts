/**
 * @Description: 检验单维护-详情页 处置与不良记录DS
 * @Author: <EMAIL>
 * @Date: 2023/3/2 15:17
 */
import { BASIC } from '@utils/config';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hwms.inspectDocMaintain';
const tenantId = getCurrentOrganizationId();
const endUrl = ``;

const ncCodeDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-doc-nc-record/doc-nc-record/detail/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'inspectNcRecordDimensionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.inspectNcRecordDimension`).d('不良记录维度'),
    },
    {
      name: 'inspectTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.inspectTaskCode`).d('检验任务'),
    },
    {
      name: 'inspectItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.inspectItemCode`).d('检验项目'),
    },
    {
      name: 'inspectObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.inspectObjectCode`).d('检验对象'),
    },
    {
      name: 'ncRecordTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.ncRecordTypeDesc`).d('不良记录类型'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.qty`).d('不良数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.uomName`).d('单位'),
    },
    {
      name: 'ncRecordTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncRecord.ncRecordTime`).d('不良发生时间'),
    },
    {
      name: 'ncRecordUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.ncRecordUserName`).d('不良记录人'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.operationName`).d('不良发现工艺'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.workcellName`).d('不良发现工作单元'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.locatorName`).d('不良发现库位'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.equipmentName`).d('不良发现设备'),
    },
    {
      name: 'ncCodeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.ncCode`).d('不良代码'),
    },
    {
      name: 'defectLevelDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.defectLevel`).d('缺陷等级'),
    },
    {
      name: 'componentMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.materialName`).d('组件物料/版本'),
    },
    {
      name: 'componentRevisionCode',
      type: FieldType.string,
    },
    {
      name: 'defectLocation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.defectLocation`).d('缺陷位置'),
    },
    {
      name: 'defectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.defectReason`).d('不良原因'),
    },
    {
      name: 'interceptWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.interceptWorkcellName`).d('拦截工作单元'),
    },

    {
      name: 'interceptOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.interceptOperationName`).d('拦截工艺'),
    },
    {
      name: 'rootCauseWorkcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.rootCauseWorkcellName`).d('不良产生工作单元'),
    },
    {
      name: 'rootCauseOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.rootCauseOperationName`).d('不良产生工艺'),
    },
    {
      name: 'rootCauseEquipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.rootCauseEquipmentCode`).d('不良产生设备'),
    },
    {
      name: 'responsibleUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.responsibleUserName`).d('责任人'),
    },
    {
      name: 'responsibleApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.responsibleApartment`).d('责任部门'),
    },
    {
      name: 'shiftTeamCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.shiftTeamCode`).d('班组'),
    },
    {
      name: 'shiftDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.shiftDate`).d('班次日期'),
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.shiftCode`).d('班次编码'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.containerCode`).d('容器'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecord.remark`).d('备注'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.ncRecord.enclosure`).d('附件'),
    },
  ],
});

const defectFormDS: () => DataSetProps = () => ({
  paging: false,
  dataKey: 'rows',
  autoCreate: false,
  transport: {
    read: () => ({
      url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-doc-disposal/doc-list/ui`,
      method: 'GET',
      transformResponse: val => {
        const { rows, success } = JSON.parse(val);
        if (!success) {
          return;
        }

        rows?.forEach(item => {
          item.displayQty = item.inspectSumQty || 0;
          item.dispositionType = item.dispositionType || 'ALL';
          const lineList = item.docDispDtlList;
          if (lineList?.length) {
            lineList.forEach((i, index) => {
              item.displayQty += `/${i.qty}`;
              i.sequence = index * 10 + 10;
            });
          }
        });

        return rows;
      },
    }),
  },
  fields: [
    {
      name: 'inspectDocId',
    },
    {
      name: 'siteId',
    },
    {
      name: 'inspectTimes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.inspectTimes`).d('检验次数'),
      lookupCode: 'MT.QMS.INSPECT_TIMES',
    },
    {
      name: 'dispositionUserId',
      type: FieldType.string,
    },
    {
      name: 'dispositionUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.dispositionUserName`).d('处置人'),
    },
    {
      name: 'dispositionApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.dispositionApartment`).d('处置部门'),
    },
    {
      name: 'dispositionTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.defectForm.dispositionTime`).d('处置时间'),
    },
    {
      name: 'displayQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.inspectSumQty`).d('报检总数'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.remark`).d('备注'),
    },
    {
      name: 'inspectSumQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.inspectSumQty`).d('报检总数'),
    },
    {
      name: 'okQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.okQty`).d('接收'),
    },
    {
      name: 'dispositionType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.dispositionType`).d('处置类型'),
      lookupCode: 'MT.DISPOSAL_TYPE',
      valueField: 'value',
      textField: 'meaning',
      defaultValue: 'ALL',
      dynamicProps: {
        disabled: ({ record }) => {
          const disposalDtlList = record.get('disposalDtlList');
          return !(disposalDtlList?.length > 0);
        },
      },
    },
    {
      name: 'dispositionFunctionObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defectForm.dispositionFunction`).d('处置方法'),
      textField: 'description',
      valueField: 'dispositionFunctionId',
      lookupUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/all/disposition/function/get`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (!data) {
            return;
          }
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            inspectDocId: dataSet?.getState('inspectDocId'),
          };
        },
        required: ({ record }) => {
          return record.get('dispositionType') === 'ALL';
        },
      },
    },
    {
      name: 'dispositionFunctionId',
      bind: 'dispositionFunctionObject.dispositionFunctionId',
    },
    {
      name: 'dispositionFunction',
      bind: 'dispositionFunctionObject.dispositionFunction',
    },
    {
      name: 'description',
      bind: 'dispositionFunctionObject.description',
    },
    {
      name: 'marking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.marking`).d('MARKING'),
      lookupCode: 'QMS_MARKING',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defectForm.operationName`).d('返修工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defectForm.processWorkcellName`).d('返修工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        workcellType: 'PROCESS',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'processWorkcellId',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'processWorkcellName',
      bind: 'processWorkcellLov.workcellName',
    },
    {
      name: 'reworkRouterLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defectForm.reworkRouter`).d('返修工艺路线'),
      lovCode: 'MT.METHOD.USER_SITE_ROUTER',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteId: dataSet.getState('baseInfo')?.siteId,
            routerType: 'NC',
          };
        },
      },
      textField: 'routerName',
    },
    {
      name: 'reworkRouterId',
      bind: 'reworkRouterLov.routerId',
    },
    {
      name: 'reworkRouterName',
      bind: 'reworkRouterLov.routerName',
    },
    {
      name: 'stepName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectForm.stepName`).d('返修步骤识别码'),
    },
    {
      name: 'degradeMaterialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.task.degradeMaterialName`).d('降级物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: dataSet.getState('baseInfo')?.siteId,
          };
        },
        disabled: ({ record }) => {
          return record.get('degradeLevel');
        },
      },
      textField: 'materialName',
      valueField: 'materialId',
    },
    {
      name: 'degradeMaterialId',
      bind: 'degradeMaterialObj.materialId',
    },
    {
      name: 'degradeMaterialName',
      bind: 'degradeMaterialObj.materialName',
    },
    {
      name: 'revisionFlag',
      bind: 'degradeMaterialObj.revisionFlag',
    },
    {
      name: 'degradeRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.degradeRevisionCode`).d('降级物料版本'),
      noCache: true,
      valueField: 'revisionCode',
      textField: 'revisionCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return (rows || []).map(item => ({
            revisionCode: item,
          }));
        },
      },
      dynamicProps: {
        required: ({ record }) => record?.get('revisionFlag') === 'Y',
        disabled: ({ record }) => record?.get('revisionFlag') !== 'Y' || record.get('degradeLevel'),
        lookupUrl: ({ record }) => {
          const siteId = record?.get('siteId');
          const materialId = record?.get('degradeMaterialId');
          const revisionFlag = record?.get('revisionFlag');
          if (!!siteId && !!materialId && revisionFlag === 'Y') {
            return `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-task/item/nc/revision/code/ui?siteId=${siteId}&materialId=${materialId}`;
          }
          return '';
        },
      },
    },
    {
      name: 'degradeLevel',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_LEVEL',
      label: intl.get(`${modelPrompt}.task.degradeLevel`).d('降级等级'),
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => {
          return record.get('degradeMaterialId');
        },
      },
    },
    {
      name: 'degradeDegree',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_DEGREE',
      label: intl.get(`${modelPrompt}.task.degradeDegree`).d('降级程度'),
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => {
          return record.get('degradeMaterialId');
        },
      },
    },
    {
      name: 'concessionInterceptOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.concessionInterceptOperation`).d('让步拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      textField: 'operationName',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: dataSet.getState('baseInfo')?.siteId,
          };
        },
      },
    },
    {
      name: 'concessionInterceptOperationId',
      bind: 'concessionInterceptOperationObj.operationId',
    },
    {
      name: 'concessionInterceptOperationName',
      bind: 'concessionInterceptOperationObj.operationName',
    },
    {
      name: 'interceptWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.interceptWorkcellId`).d('拦截工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: dataSet.getState('baseInfo')?.siteId,
          };
        },
      },
    },
    {
      name: 'interceptWorkcellId',
      bind: 'interceptWorkcellObj.workcellId',
    },
    {
      name: 'interceptWorkcellName',
      bind: 'interceptWorkcellObj.workcellName',
    },
    {
      name: 'dischargeWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.dischargeWorkcellId`).d('排出工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: record?.get('siteId'),
          workcellType: 'STATION',
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'dischargeWorkcellId',
      bind: 'dischargeWorkcellObj.workcellId',
    },
    {
      name: 'dischargeWorkcellName',
      bind: 'dischargeWorkcellObj.workcellName',
    },
    {
      name: 'overInterceptOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.overInterceptOperation`).d('跨工单拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: dataSet.getState('baseInfo')?.siteId,
          };
        },
        disabled: ({ record }) => {
          return !(record.get('CROSS_WO_INTERCEPT') > 0);
        },
      },
      textField: 'operationName',
    },
    {
      name: 'overInterceptOperationId',
      bind: 'overInterceptOperationObj.operationId',
    },
    {
      name: 'overInterceptOperationName',
      bind: 'overInterceptOperationObj.operationName',
    },
  ],
});

const defectLineDS: () => DataSetProps = () => ({
  selection: false,
  paging: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLine.sequence`).d('序号'),
    },
    {
      name: 'disposalObjectType',
      type: FieldType.string,
    },
    {
      name: 'disposalObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLine.disposalObjectType`).d('检验对象类型'),
    },
    {
      name: 'disposalObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLine.disposalObjectCode`).d('处置对象'),
    },
    {
      name: 'disposalObjectQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.defectLine.disposalObjectQty`).d('报检数量'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.defectLine.qty`).d('处置数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLine.uomName`).d('单位'),
    },
    {
      name: 'disposalFunction',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLine.dispositionFunction`).d('处置方法'),
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defectForm.operationName`).d('返修工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => ({
          tenantId,
        }),
        disabled: ({ record }) => {
          return !(record.get('REWORK_SOURCE') > 0);
        },
      },
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defectForm.processWorkcellName`).d('返修工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        workcellType: 'PROCESS',
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => ({
          tenantId,
          workcellType: 'PROCESS',
        }),
        disabled: ({ record }) => {
          return !(record.get('REWORK_SOURCE') > 0);
        },
      },
    },
    {
      name: 'processWorkcellId',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'processWorkcellName',
      bind: 'processWorkcellLov.workcellName',
    },
    {
      name: 'reworkRouterLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defectForm.reworkRouter`).d('返修工艺路线'),
      lovCode: 'MT.METHOD.USER_SITE_ROUTER',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          routerType: 'NC',
        }),
        disabled: ({ record }) => {
          return !(record.get('REWORK_ROUTER') > 0);
        },
      },
    },
    {
      name: 'reworkRouterId',
      bind: 'reworkRouterLov.routerId',
    },
    {
      name: 'reworkRouterName',
      bind: 'reworkRouterLov.routerName',
    },
    {
      name: 'degradeMaterialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.task.degradeMaterialName`).d('降级物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: dataSet.getState('baseInfo')?.siteId,
          };
        },
        disabled: ({ record }) => {
          return record.get('degradeLevel') || !(record.get('DEGRADE') > 0);
        },
      },
      textField: 'materialName',
    },
    {
      name: 'degradeMaterialId',
      bind: 'degradeMaterialObj.materialId',
    },
    {
      name: 'degradeMaterialName',
      bind: 'degradeMaterialObj.materialName',
    },
    {
      name: 'revisionFlag',
      bind: 'degradeMaterialObj.revisionFlag',
    },
    {
      name: 'degradeRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.task.degradeRevisionCode`).d('降级物料版本'),
      noCache: true,
      valueField: 'revisionCode',
      textField: 'revisionCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return (rows || []).map(item => ({
            revisionCode: item,
          }));
        },
      },
      dynamicProps: {
        required: ({ record }) => record?.get('revisionFlag') === 'Y',
        disabled: ({ record }) =>
          record?.get('revisionFlag') !== 'Y' ||
          record.get('degradeLevel') ||
          !(record.get('DEGRADE') > 0),
        lookupUrl: ({ record }) => {
          const siteId = record?.get('siteId');
          const materialId = record?.get('degradeMaterialId');
          const revisionFlag = record?.get('revisionFlag');
          if (!!siteId && !!materialId && revisionFlag === 'Y') {
            return `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-task/item/nc/revision/code/ui?siteId=${siteId}&materialId=${materialId}`;
          }
          return '';
        },
      },
    },
    {
      name: 'degradeLevel',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_LEVEL',
      label: intl.get(`${modelPrompt}.task.degradeLevel`).d('降级等级'),
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => {
          return record.get('degradeMaterialId') || !(record.get('DEGRADE') > 0);
        },
      },
    },
    {
      name: 'degradeDegree',
      type: FieldType.string,
      lookupCode: 'QMS_DEGRADE_DEGREE',
      label: intl.get(`${modelPrompt}.task.degradeDegree`).d('降级程度'),
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => {
          return record.get('degradeMaterialId') || !(record.get('DEGRADE') > 0);
        },
      },
    },
    {
      name: 'concessionInterceptOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.concessionInterceptOperation`).d('让步拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      textField: 'operationName',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: dataSet.getState('baseInfo')?.siteId,
          };
        },
        disabled: ({ record }) => {
          return !(record.get('CONCESSION_INTERCEPTION') > 0);
        },
      },
    },
    {
      name: 'concessionInterceptOperationId',
      bind: 'concessionInterceptOperationObj.operationId',
    },
    {
      name: 'concessionInterceptOperationName',
      bind: 'concessionInterceptOperationObj.operationName',
    },
    {
      name: 'interceptWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.interceptWorkcellId`).d('拦截工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: dataSet.getState('baseInfo')?.siteId,
          };
        },
        disabled: ({ record }) => {
          return !(record.get('CONCESSION_INTERCEPTION') > 0);
        },
      },
    },
    {
      name: 'interceptWorkcellId',
      bind: 'interceptWorkcellObj.workcellId',
    },
    {
      name: 'interceptWorkcellName',
      bind: 'interceptWorkcellObj.workcellName',
    },
    {
      name: 'dischargeWorkcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.dischargeWorkcellId`).d('排出工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          enableFlag: 'Y',
          siteId: record?.get('siteId'),
          workcellType: 'STATION',
        }),
        disabled: ({ record }) => {
          return !(record.get('EO_DISCHARGE') > 0);
        },
      },
      textField: 'workcellName',
    },
    {
      name: 'dischargeWorkcellId',
      bind: 'dischargeWorkcellObj.workcellId',
    },
    {
      name: 'dischargeWorkcellName',
      bind: 'dischargeWorkcellObj.workcellName',
    },
    {
      name: 'overInterceptOperationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.overInterceptOperation`).d('跨工单拦截工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: dataSet.getState('baseInfo')?.siteId,
          };
        },
        disabled: ({ record }) => {
          return !(record.get('CROSS_WO_INTERCEPT') > 0);
        },
      },
      textField: 'operationName',
    },
    {
      name: 'overInterceptOperationId',
      bind: 'overInterceptOperationObj.operationId',
    },
    {
      name: 'overInterceptOperationName',
      bind: 'overInterceptOperationObj.operationName',
    },
    {
      name: 'reworkStepName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLine.reworkStepName`).d('返修步骤识别码'),
    },
    {
      name: 'disposalExecuteTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.defectLine.disposalExecuteTime`).d('处置执行时间'),
    },
    {
      name: 'disposalExecuteUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLine.disposalExecuteUserName`).d('处置执行人'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLine.remark`).d('备注'),
      dynamicProps: {
        required: ({ record }) => {
          return record.get('dispositionType')==="PART";
        },
      },
    },
  ],
  events: {
    update: ({ record, name, value }) => {
      if (name === 'REWORK_SOURCE') {
        if (!value) {
          record.set('operationLov', null);
          record.set('processWorkcellLov', null);
        }
      } else if (name === 'REWORK_ROUTER') {
        if (!value) {
          record.set('reworkRouterLov', null);
        }
      } else if (name === 'DEGRADE') {
        if (!value) {
          record.set('degradeMaterialObj', null);
          record.set('degradeRevisionCode', null);
          record.set('degradeLevel', null);
        }
      } else if (name === 'REWORK_SOURCE') {
        if (!value) {
          record.set('operationLov', null);
          record.set('processWorkcellLov', null);
        }
      } else if (name === 'CONCESSION_INTERCEPTION') {
        if (!value) {
          record.set('concessionInterceptOperationObj', null);
          record.set('interceptWorkcellObj', null);
        }
      } else if (name === 'EO_DISCHARGE') {
        if (!value) {
          record.set('dischargeWorkcellObj', null);
        }
      } else if (name === 'CROSS_WO_INTERCEPT') {
        if (!value) {
          record.set('overInterceptOperationObj', null);
        }
      }
    },
  },
});

export { defectFormDS, defectLineDS, ncCodeDS };

