import React, { useMemo, useState, useEffect } from 'react';
import { Button, DataSet, Table, Col, Row, Form,Attachment,  TextField, Lov, Icon, Select } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
// import { routerRedux } from 'dva/router';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { CancelList } from '../services';
import { tableDS } from '../stores/MarkMaintenanceDS';
import LovModal from "../../ProductBatchProcessCancellation/LovModal";
import InputLovDS from '../../../stores/InputLovDS';

const modelPrompt = 'tarzan.hmes.MarkMaintenance';
const markMaintenance = props => {
  const { run: cancelList, loading: cancelListLoading } = useRequest(CancelList(), {
    manual: true,
    needPromise: true,
  });

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);
  const [selectedRow, setSelectedRow] = useState([]);

  useEffect(() => {
    // 添加选中监听事件
    tableDs.addEventListener('select', handleDataSetSelect);
    tableDs.addEventListener('unSelect', handleDataSetSelect);
    tableDs.addEventListener('selectAll', handleDataSetSelect);
    tableDs.addEventListener('unSelectAll', handleDataSetSelect);
  }, []);

  const handleDataSetSelect = () => {
    setSelectedRow(tableDs.selected);
  };

  const toggleForm = () => {
    setExpandForm(!expandForm);
  }

  const applyBasisProps = {
    name: 'applyBasis',
    bucketName: 'hmes',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['image/*'],
    viewMode: 'popup',
    readOnly: true,
  }
  const attachmentProps = {
    name: 'attachments',
    bucketName: 'hmes',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.xlsx', '.xls','image/*', '.pptx'],
    viewMode: 'popup',
    readOnly: true,
  };

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}
          style={{
            display: 'flex',
            alignItems: 'flex-start',
          }}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="markingCode"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'markingCode', '标记编码', queryDataSet)}
                    />
                  </div>
                }
              />
              <Select name="statusList" />
              <Select name="type" />
              {expandForm && (
                <>
                  <Select name="markingContent" />
                  <Select name="enableFlag" />
                  <Lov name="operationObj" />
                  <TextField name="createdBy" />
                  <TextField name="updatedBy" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button funcType="link" icon={
                expandForm? 'expand_less':'expand_more'
              } onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  }
  const handleSearch = async () => {
    if(await tableDs?.queryDataSet.validate()){
      tableDs.query()
    }
  }
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
    }
  }

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const handleCreate = () => {
    props.history.push(`/hmes/mark-maintenance/detail/create`);
  };
  const toDetail = (record) => {
    props.history.push(`/hmes/mark-maintenance/detail/${record?.get('markingId')}`);
  }
  const columns = [
    // 站点
    {
      name: 'siteCode',
      align: 'left',
    },
    {
      name: 'markingCode',
      renderer: ({ value, record }) => (
        <a
          onClick={() => {
            toDetail(record);
          }}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'statusValue',
    },
    {
      name: 'typeValue',
    },
    {
      name: 'markingContentValue',
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      renderer: ({ value }) => (
        <Badge status={value === 'FORBIDDEN' ? 'error':value === 'INVALID'?'yellow' : 'success'}
          text={value === 'FORBIDDEN' ? '禁用':value === 'INVALID'?'无效' : '有效'}>
        </Badge>
      ),
    },
    {
      name: 'expirationDate',
    },
    {
      name: 'applyBasis',
      width: 120,
      renderer: ({record}) => {
        return <Attachment {...applyBasisProps} record={record} />
      },
    },
    {
      name: 'createdBy',
    },
    {
      name: 'operationDesc',
    },
    {
      name: 'interceptionDisposalWay',
    },
    {
      name: 'disposalResult',
    },
    {
      name: 'attachments',
      width: 120,
      renderer: ({record}) => {
        return <Attachment {...attachmentProps} record={record} />
      },
    },
    {
      name: 'updatedBy',
    },
  ];

  const getExportQueryParams = () => {
    return {
      ...tableDs.queryDataSet.current.toData(),
    }
  }

  const handleCancel = async () => {
    const noNewData = selectedRow.some(item => item?.data?.status !== 'NEW')
    if(noNewData){
      return notification.error({
        message: intl.get(`${modelPrompt}.error.cancel`).d('标记不为新建状态，不允许取消'),
      });
    }
    const res = await cancelList({
      params: tableDs.selected.map(item => item.data),
    })
    if(res&&!res.success){
      notification.error({
        message: res.message,
      });
    }else{
      notification.success({
        message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
      });
      tableDs.query()
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('标记维护')}>
        <Button Button disabled={selectedRow.length===0} onClick={handleCancel} style={{ marginRight: 15 }} color="primary" loading={cancelListLoading}>
          {intl.get('tarzan.common.button.cancel').d('取消')}
        </Button>
        <Button onClick={() => handleCreate()} style={{ marginRight: 15 }} icon="add" color="primary">
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${
            BASIC.HMES_BASIC
          }/v1/${getCurrentOrganizationId()}/hme-markings/export`}
          queryParams={getExportQueryParams}
          buttonText="导出"
        />
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          queryFieldsLimit={3}
          searchCode="MarkMaintenance"
          customizedCode="MarkMaintenance"
          queryBar={renderQueryBar}
        />
      </Content>
      <LovModal {...lovModalProps} />

    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.MarkMaintenance', 'tarzan.common'],
})(markMaintenance);
