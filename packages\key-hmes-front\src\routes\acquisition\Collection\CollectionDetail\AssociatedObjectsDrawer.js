/**
 * @Description: 数据收集组维护-关联对象-新建/编辑抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-04-19 10:19:56
 * @LastEditTime: 2022-06-29 16:14:50
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useEffect } from 'react';
import { Table, Button, Lov, Select } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import notification from 'utils/notification';
import { materialRevisionOptionDs } from '../stores/CollectionDS';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

export default ({ singleAssObjectDs, record: propsRecord, canEdit, parentSiteIds }) => {
  const [selected, setSelected] = useState({});
  const selectedProxy = useMemo(() => ({}), []);
  selectedProxy.selected = selected;
  const typeName = {
    MATERIAL: intl.get(`${modelPrompt}.MATERIAL`).d('物料'),
    OPERATION: intl.get(`${modelPrompt}.OPERATION`).d('工艺'),
    // ROUTER: intl.get(`${modelPrompt}.ROUTER`).d('工艺路线'),
    // ROUTER_STEP: intl.get(`${modelPrompt}.ROUTER_STEP`).d('工艺路线步骤'),
    WORKCELL: intl.get(`${modelPrompt}.WORKCELL`).d('工作单元'),
    NC_CODE: intl.get(`${modelPrompt}.NC_CODE`).d('NC代码'),
    // BOM: intl.get(`${modelPrompt}.BOM`).d('装配清单'),
    // BOM_COMPONENT: intl.get(`${modelPrompt}.BOM_COMPONENT`).d('装配清单组件'),
    SUBSTEP: intl.get(`${modelPrompt}.SUBSTEP`).d('子步骤'),
    WORK_ORDER: intl.get(`${modelPrompt}.WORK_ORDER`).d('生产指令'),
    EO: intl.get(`${modelPrompt}.EO`).d('执行作业'),
    MATERIAL_CATEGORY: intl.get(`${modelPrompt}.MATERIAL_CATEGORY`).d('物料类别'),
    MATERIAL_LOT: intl.get(`${modelPrompt}.MATERIAL_LOT`).d('物料批'),
    'MT.WO_ROUTER': intl.get(`${modelPrompt}.MT.WO_ROUTER`).d('工艺路线'),
    'MT.ROUTER_STEP': intl.get(`${modelPrompt}.MT.ROUTER_STEP`).d('工艺路线步骤'),
    'APEX_MES.USER_EQUIP_WKC_REL': intl.get(`${modelPrompt}.APEX_MES.USER_EQUIP_WKC_REL`).d('设备'),
  };

  useEffect(() => {
    const cacheObjectList = propsRecord.get('objectList') || [];
    const cacheSelected = {};
    let routerId = null;
    let bomId = null;
    cacheObjectList.forEach(item => {
      const { objectType } = item;
      if (objectType === 'MATERIAL' && item.objectRevision) {
        // 启用版本的物料，需要查一下
        materialRevisionOptionDs.setQueryParameter('siteIds', parentSiteIds.join(','));
        materialRevisionOptionDs.setQueryParameter('materialId', item.objectId);
        materialRevisionOptionDs.query();
      } else if (objectType === 'MT.WO_ROUTER') {
        // 需要给工艺路线步骤塞查询routerId
        routerId = item.objectId;
      } else if (objectType === 'BOM') {
        // 需要给装配清单组件塞查询bomId
        bomId = item.objectId;
      }
      cacheSelected[item.objectType] = true;
    });
    setSelected(cacheSelected);
    singleAssObjectDs.loadData(
      cacheObjectList.map(item => {
        if (item.objectType === 'MT.ROUTER_STEP') {
          return { ...item, uuid: uuid(), parentSiteIds, routerId };
        } if (item.objectType === 'BOM_COMPONENT') {
          return { ...item, uuid: uuid(), parentSiteIds, bomId };
        } if (item.objectType === 'MATERIAL' && item.objectRevision) {
          return { ...item, uuid: uuid(), parentSiteIds, revisionFlag: 'Y' };
        }
        return { ...item, uuid: uuid(), parentSiteIds };
      }),
    );
  }, []);

  const selectObjTypeDiv = type => {
    if (!canEdit) {
      return;
    }
    if (selected[type]) {
      // 已选过的类型，再次点击为删除行
      let newList = singleAssObjectDs.filter(record => record.get('objectType') !== type);
      const newSelected = {
        ...selectedProxy.selected,
        [type]: false,
      };
      if (
        type === 'MT.WO_ROUTER' &&
        singleAssObjectDs.filter(record => record.get('objectType') === 'MT.ROUTER_STEP').length
      ) {
        // 同时存在工艺路线和工艺路线步骤，需要删除工艺路线步骤
        newList = newList.filter(record => record.get('objectType') !== 'MT.ROUTER_STEP');
        newSelected['MT.ROUTER_STEP'] = false;
      }
      if (
        type === 'BOM' &&
        singleAssObjectDs.filter(record => record.get('objectType') === 'BOM_COMPONENT').length
      ) {
        // 同时存在装配清单和装配清单组件，需要删除装配清单组件
        newList = newList.filter(record => record.get('objectType') !== 'BOM_COMPONENT');
        newSelected.BOM_COMPONENT = false;
      }
      singleAssObjectDs.loadData(newList);
      setSelected(newSelected);
    } else {
      // 新增行
      const newRow = { uuid: uuid(), objectType: type, parentSiteIds };
      if (
        ['NC_CODE', 'WORK_ORDER', 'EO', 'MATERIAL_LOT'].includes(type) &&
        parentSiteIds.length !== 1
      ) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.justOneSite`)
            .d(`当数据收集组有且只有一个站点时，才可添加！`),
        });
        return;
      }
      if (type === 'MT.ROUTER_STEP') {
        if (
          !singleAssObjectDs.some(
            record => record.get('objectType') === 'MT.WO_ROUTER' && record.get('objectId'),
          )
        ) {
          // 添加工艺路线步骤，需先添加工艺路线
          notification.error({
            message: intl
              .get(`${modelPrompt}.message.routerRequired`)
              .d('添加工艺路线步骤，需先添加工艺路线！'),
          });
          return;
        }
        // 将选中的RouterId传过去
        newRow.selectedRouterId = singleAssObjectDs.toData().filter(item => item.objectType === 'MT.WO_ROUTER')[0].objectId
        // newRow.selectedRouterId = singleAssObjectDs
        //   .filter(record => record.get('objectType') === 'MT.ROUTER')[0]
        //   .get('objectId');
      }
      if (type === 'BOM_COMPONENT') {
        if (
          !singleAssObjectDs.some(
            record => record.get('objectType') === 'BOM' && record.get('objectId'),
          )
        ) {
          // 添加装配清单组件，需先添加装配清单
          notification.error({
            message: intl
              .get(`${modelPrompt}.error.bomRequired`)
              .d('添加装配清单组件，需先添加装配清单！'),
          });
          return;
        }
        // 将选中的BomId传过去
        newRow.selectedBomId = singleAssObjectDs
          .filter(record => record.get('objectType') === 'BOM')[0]
          .get('objectId');

      }
      singleAssObjectDs.create(newRow);
      setSelected({
        ...selectedProxy.selected,
        [type]: true,
      });
    }
  };

  const changeObject = async (lovRecords, record) => {
    if (record.get('objectType') === 'MATERIAL') {
      singleAssObjectDs.current.set('objectRevision', null);
    }
    if (record.get('objectType') === 'MT.WO_ROUTER') {
      // 切换工艺路线，需要删除工艺路线步骤
      // await Modal.confirm({
      //   title: intl.get(`common.platform.sysTools.tips`).d('提示'),
      //   children: (
      //     <p>
      //       {intl
      //         .get(`${modelPrompt}.message.confirm.changeRouter`)
      //         .d('切换“工艺路线”，会删除“工艺路线步骤”数据，是否继续？')}
      //     </p>
      //   ),
      // }).then((button) => {
      //   if (button === 'ok') {
      const newList = singleAssObjectDs.filter(rc => rc.get('objectType') !== 'MT.ROUTER_STEP');
      singleAssObjectDs.loadData(newList);
      setSelected({
        ...selectedProxy.selected,
        'MT.ROUTER_STEP': false,
      });
      // } else {
      //   record.set('objectLov', oldValue);
      // }
      // });
    } else if (record.get('objectType') === 'BOM') {
      // 切换装配清单，需要删除装配清单组件
      const newList = singleAssObjectDs.filter(rc => rc.get('objectType') !== 'BOM_COMPONENT');
      singleAssObjectDs.loadData(newList);
      setSelected({
        ...selectedProxy.selected,
        BOM_COMPONENT: false,
      });
    } else if (record.get('revisionFlag')) {
      materialRevisionOptionDs.setQueryParameter(
        'siteIds',
        (record.get('parentSiteIds') || []).join(','),
      );
      materialRevisionOptionDs.setQueryParameter('materialId', lovRecords.materialId);
      materialRevisionOptionDs.query();
    }
  };

  const columns = [
    {
      header: null,
      align: 'center',
      width: 70,
      renderer: ({ record }) => {
        const objType = record.get('objectType');
        if (objType === 'MT.WO_ROUTER') {
          return (
            <Popconfirm
              title={intl
                .get(`${modelPrompt}.message.confirm.deleteRouter`)
                .d(`是否确认删除关联对象类型为“工艺路线”的数据?可能同时删除“工艺路线步骤”数据。`)}
              onConfirm={() => {
                selectObjTypeDiv(objType);
              }}
            >
              <Button
                disabled={!canEdit}
                funcType="flat"
                icon="remove"
                shape="circle"
                size="small"
              />
            </Popconfirm>
          );
        }
        if (objType === 'BOM') {
          return (
            <Popconfirm
              title={intl
                .get(`${modelPrompt}.message.confirm.deleteBom`)
                .d(`是否确认删除关联对象类型为“装配清单”的数据?可能同时删除“装配清单组件”数据。`)}
              onConfirm={() => {
                selectObjTypeDiv(objType);
              }}
            >
              <Button
                disabled={!canEdit}
                funcType="flat"
                icon="remove"
                shape="circle"
                size="small"
              />
            </Popconfirm>
          );
        }
        return (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              selectObjTypeDiv(objType);
            }}
          >
            <Button disabled={!canEdit} funcType="flat" icon="remove" shape="circle" size="small" />
          </Popconfirm>
        );
      },
      lock: 'left',
    },
    {
      name: 'objectType',
      width: 120,
      renderer: ({ record }) => typeName[record.get('objectType')],
    },
    {
      name: 'objectLov',
      width: 180,
      renderer: ({ record }) => record.get('objectCode'),
      editor: record => {
        return (
          canEdit && (
            <Lov
              dataSet={singleAssObjectDs}
              name="objectLov"
              onChange={lovRecords => changeObject(lovRecords, record)}
            />
          )
        );
      },
    },
    {
      name: 'objectRevision',
      width: 120,
      editor: record => {
        return (
          canEdit &&
          record.get('revisionFlag') && <Select dataSet={singleAssObjectDs} name="objectRevision" />
        );
      },
    },
    {
      name: 'objectDesc',
    },
  ];

  return (
    <>
      <div className={styles['card-select-wrapper']}>
        {Object.keys(typeName).map(item => {
          if (selected[item]) {
            if (item === 'MT.WO_ROUTER') {
              return (
                <Popconfirm
                  title={intl
                    .get(`${modelPrompt}.message.confirm.deleteRouterSecond`)
                    .d(
                      `是否确认删除关联对象类型为“工艺路线”的数据?会删除“工艺路线”及“工艺路线步骤”数据`,
                    )}
                  onConfirm={() => {
                    selectObjTypeDiv(item);
                  }}
                >
                  <div
                    key={item}
                    className={styles['card-select']}
                    data-selected
                    disabled={!canEdit}
                  >
                    {typeName[item]}
                  </div>
                </Popconfirm>
              );
            }
            if (item === 'BOM') {
              return (
                <Popconfirm
                  title={intl
                    .get(`${modelPrompt}.message.confirm.deleteBomSecond`)
                    .d(
                      `是否确认删除关联对象类型为“装配清单”的数据?会删除“装配清单”及“装配清单组件”数据`,
                    )}
                  onConfirm={() => {
                    selectObjTypeDiv(item);
                  }}
                >
                  <div
                    key={item}
                    className={styles['card-select']}
                    data-selected
                    disabled={!canEdit}
                  >
                    {typeName[item]}
                  </div>
                </Popconfirm>
              );
            }
            return (
              <Popconfirm
                title={intl
                  .get(`${modelPrompt}.message.confirm.delete`, {
                    typeName: typeName[item],
                  })
                  .d(`是否确认删除关联对象类型为“${typeName[item]}”的数据?`)}
                onConfirm={() => {
                  selectObjTypeDiv(item);
                }}
              >
                <div key={item} className={styles['card-select']} data-selected disabled={!canEdit}>
                  {typeName[item]}
                </div>
              </Popconfirm>
            );
          }
          return (
            <div
              key={item}
              onClick={() => {
                selectObjTypeDiv(item);
              }}
              className={styles['card-select']}
              disabled={!canEdit}
            >
              {typeName[item]}
            </div>
          );
        })}
      </div>
      <Table dataSet={singleAssObjectDs} columns={columns} />
    </>
  );
};
