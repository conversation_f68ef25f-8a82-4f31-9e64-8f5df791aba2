/**
 * @Description: 容器类型维护（重构） - 入口页
 * @Author: <EMAIL>
 * @Date: 2022/7/11 15:12
 * @LastEditTime: 2023-05-18 16:08:50
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import { tableDS } from './stores/ListTable';

const ContainerTypeList = props => {
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;

  const columns: ColumnProps[] = [
    {
      name: 'containerTypeCode',
      width: 180,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(
                `/apex-hmes/hagd/container-type/detail/${record?.get('containerTypeId')}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    { name: 'containerTypeDescription', width: 230 },
    { name: 'containerClassificationDesc' },
    {
      name: 'enableFlag',
      width: 120,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    { name: 'packingLevelDesc' },
    { name: 'capacityQty' },
    { name: 'maxLoadWeight' },
    { name: 'weightUomName' },
  ];

  const onFieldEnterDown = () => {
    tableDs.query(props.tableDs.currentPage);
  }

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`tarzan.hagd.containerType.title.containerTypeMaintain`).d('容器类型维护')}
      >
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => props.history.push(`/apex-hmes/hagd/container-type/detail/create`)}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`tarzan.common.button.create`).d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_LIST.LIST`,
          },
          <Table
            searchCode="rqlxwh1"
            customizedCode="rqlxwh1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            dataSet={tableDs}
            columns={columns}
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.hagd.containerType', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_LIST.LIST`] }),
)(ContainerTypeList);
