import React, { <PERSON> } from 'react';
import { Button } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';

const Index: FC = props => {
  const { style, history, wkInstanceId } = props as any;

  function handleApproveHistory() {
    const path = `/hwkf/submitted/detail/${wkInstanceId}`;
    history.push(path);
  }

  return (
    <Button style={style} onClick={handleApproveHistory}>
      {intl.get(`alm.common.button.approveHistory`).d('审批历史')}
    </Button>
  );
};

export default formatterCollections({
  code: ['alm.common', 'alm.component'],
})(Index);
