/**
 * @feature 成品条码注册功能-入口页面
 * @date 2021-12-28
 * <AUTHOR>
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Modal, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import moment from 'moment';
import myInstance from '@utils/myAxios';
// import { FRPrintButton } from '@components/tarzan-ui';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import { useRequest } from '@components/tarzan-hooks';
import {TemplatePrintButton} from "../../../../components/tarzan-ui";
import { Drawer, DrawerProps } from './Drawer';
import { headerTableDS, lineTableDS } from '../stories/RegistrationListDS';
import { drawerFormDS, drawerTableDS } from '../stories/DrawerDS';
import styles from './index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inbound.barCodeRegistration';

const RegistrationList = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { path },
  } = props;
  const [beforeOpenDrawer, setOpenDrawerRecord] = useState<any>(0); // 用于保存选中的index
  const [confirmFlag, setConfirmFlag] = useState<boolean>(false); // 用于判断是否点击了确定按钮
  const [isSelect, setSelectFlag] = useState(false); // 用于判断“创建条码”按钮的禁用逻辑
  const [selectedMaterialLotIds, setSelectedMaterialLotIds] = useState<any[]>([]);
  const drawerFormDs: DataSet = useMemo(() => new DataSet(drawerFormDS()), []);
  const drawerTableDs: DataSet = useMemo(() => new DataSet(drawerTableDS()), []);
  const { run: handleDrawerOk, loading: confirmLoading } = useRequest(
    {
      url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-product-barcode-register/save/ui`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );
  let _modal;

  // 当在页签上右键刷新时，如果当前表格有勾选数据时，需要随之变按钮禁用状态
  useEffect(() => {
    if (headerTableDs.selected.length) {
      setSelectFlag(true);
      lineDataSetSelectUpdate();
    }
  }, []);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (headerTableDs) {
      const header = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销监听
      header.call(headerTableDs, 'select', handleHeaderTableDsSelect);
      header.call(headerTableDs, 'unselect', handleHeaderTableDsUnSelect);
      // 列表加载事件
      header.call(headerTableDs, 'load', resetHeaderDetail);
    }
    if (lineTableDs) {
      const line = flag ? lineTableDs.addEventListener : lineTableDs.removeEventListener;
      // 行选中和撤销监听
      line.call(lineTableDs, 'batchSelect', lineDataSetSelectUpdate);
      line.call(lineTableDs, 'batchUnSelect', lineDataSetSelectUpdate);
    }
  };

  // 处理头单选按钮选中事件
  const handleHeaderTableDsSelect = ({ record }) => {
    setOpenDrawerRecord(record.index);
    queryLineTable(record.toData().workOrderId);
    setSelectFlag(true);
  };

  // 处理头单选按钮撤销选中事件
  const handleHeaderTableDsUnSelect = () => {
    queryLineTable(null);
    setSelectFlag(false);
  };

  // 处理头列表加载事件
  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行详情数据否则空查
    if (dataSet?.current?.toData()) {
      if (confirmFlag) {
        headerRowClick(beforeOpenDrawer);
      } else {
        headerRowClick(dataSet.current);
      }
    } else {
      queryLineTable(null);
    }
  };

  // 行选中和撤销的更新事件
  const lineDataSetSelectUpdate = () => {
    const _selectedMaterialLotIds: string[] = [];
    lineTableDs.selected.forEach(item => {
      const { materialLotId } = item.toData();
      _selectedMaterialLotIds.push(materialLotId);
    });
    setSelectedMaterialLotIds(_selectedMaterialLotIds);
  };

  // 行列表数据查询
  const queryLineTable = workOrderId => {
    if (workOrderId) {
      lineTableDs.setQueryParameter('workOrderId', workOrderId);
    } else {
      lineTableDs.setQueryParameter('workOrderId', undefined);
    }
    lineTableDs.query();
  };

  const headerRowClick = record => {
    headerTableDs.select(record);
    setSelectFlag(headerTableDs.selected.length !== 0);
  };

  // 控制Model更新
  const headerDrawerChildren = (val, drawerRecord) => {
    // 抽屉传参
    const drawerProps: DrawerProps = {
      drawerRecord: {
        ...drawerRecord,
        productionDate: moment().format('YYYY-MM-DD HH:mm:ss'),
      },
      drawerFormDs,
      drawerTableDs,
      drawerTableFlag: val,
    };
    return (
      <>
        <Drawer {...drawerProps} />
      </>
    );
  };

  // 抽屉“确定”按钮回调
  const handleOk = async () => {
    setConfirmFlag(true);
    const newData: any = drawerFormDs.toData()[0];
    // 可创建条码数量为0时弹出提示
    if (newData.canCreateBarcodeQty === 0) {
      notification.warning({
        description: intl
          .get(`${modelPrompt}.notification.canCreateBarcodeQtyIsZero`)
          .d('可创建条码数量为0，无法创建新的条码'),
      });
      return false; // 用于阻止Model的自动关闭行为
    }
    drawerFormDs!.current!.set('newDate', new Date());
    const validate = await drawerFormDs.validate(false, true);
    if (!validate) {
      return false;
    }
    return handleDrawerOk({
      params: {
        ...newData,
      },
    }).then(async (res) => {
      if (res && res.success) {
        notification.success({});
        await headerTableDs.query(headerTableDs.currentPage).then(
          () => queryLineTable(headerTableDs.current.toData().workOrderId));
        // 保存成功时查询物料批数据
        drawerTableDs.setQueryParameter('materialLotIds', res.rows.join(','));
        drawerTableDs.query().then(() => {
          // c7n抽屉更新
          _modal.update({
            children: headerDrawerChildren(true, headerTableDs.selected[0].toData()),
          });
        });
        return false; // 用于阻止Model的自动关闭行为
      }
      return Promise.resolve(false);

    });
  };

  // 抽屉“取消”按钮回调
  const handleClose = () => {
    setConfirmFlag(false);
    setOpenDrawerRecord(0);
    drawerTableDs.loadData([{}]);
    _modal.update({
      children: headerDrawerChildren(false, headerTableDs.selected[0].toData()),
    });
    _modal.close();
  };

  // 点击“创建条码”按钮回调
  const handleCreateBarcode = () => {
    const record = headerTableDs.selected[0].toData();
    if (record.statusCode !== 'RELEASED') {
      notification.warning({
        description: intl
          .get(`${modelPrompt}.notification.statusCodeNotEoReleased`)
          .d('生产指令状态不为下达，不允许创建条码'),
      });
      return;
    }
    _modal = Modal.open({
      title: (
        <div style={{ marginRight: 30 }}>
          <span style={{ fontSize: '14px' }}>
            {intl.get(`${modelPrompt}.title.barCodeRegistration`).d('成品条码注册')}
          </span>
          <PermissionButton
            icon="save"
            type="c7n-pro"
            color={ButtonColor.primary}
            style={{ float: 'right', marginRight: 20 }}
            onClick={() => handleOk()}
            loading={confirmLoading}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </PermissionButton>
        </div>
      ),
      destroyOnClose: true,
      maskClosable: true,
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: headerDrawerChildren(false, headerTableDs.selected[0].toData()),
      onClose: () => setConfirmFlag(false),
      footer: (
        <Button onClick={() => handleClose()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  const headerTableColumns: ColumnProps[] = [
    {
      name: 'workOrderNum',
      width: 180,
      lock: ColumnLock.left,
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              // @ts-ignore
              const url = `/hmes/workshop/production-order-mgt/detail/${record.data.workOrderId}`;
              props.history.push(url);
            }}
          >
            {value}
          </a>
        );
      },
    },
    { name: 'statusDesc', align: ColumnAlign.center },
    { name: 'materialCode', width: 150 },
    { name: 'revisionCode', width: 150 },
    { name: 'materialName', width: 150 },
    { name: 'planStartTime', align: ColumnAlign.center, width: 150 },
    { name: 'quantity' },
    { name: 'sumQty', width: 120 },
    { name: 'storageQty', width: 120 },
    { name: 'canCreateBarcodeQty', width: 120 },
    { name: 'prodLineCode', width: 150 },
    { name: 'siteCode', width: 150 },
    { name: 'soNumber', width: 150 },
    { name: 'soLineNum' },
  ];

  const lineTableColumns: ColumnProps[] = [
    {
      name: 'materialLotCode',
      lock: ColumnLock.left,
      width: 180,
    },
    {
      name: 'materialCode',
      lock: ColumnLock.left,
      width: 150,
    },
    {
      name: 'revisionCode',
      lock: ColumnLock.left,
      width: 150,
    },
    {
      name: 'materialName',
      width: 150,
    },
    { name: 'materialLotStatus' },
    { name: 'uomCode' },
    { name: 'primaryUomQty' },
    { name: 'printTimes' },
    { name: 'createdByName' },
    { name: 'createdDate', align: ColumnAlign.center, width: 150 },
    { name: 'productionDate', align: ColumnAlign.center, width: 150 },
  ];

  // 删除物料批
  const handleDelete = () => {
    const url = `${
      BASIC.HWMS_BASIC
    }/v1/${getCurrentOrganizationId()}/mt-product-barcode-register/reserve-object/remove/ui`;
    return myInstance.post(url, selectedMaterialLotIds).then(async res => {
      if (res?.data?.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        // 更新物料批的行
        headerTableDs.query(props.headerTableDs.currentPage).then(
          () => queryLineTable(headerTableDs.current.toData().workOrderId));
      } else {
        notification.error({
          message: res?.data?.message,
        });
      }
    });
  };

  const onFieldEnterDown = () => {
    headerTableDs.query(props.headerTableDs.currentPage);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.barCodeRegistration`).d('成品条码注册')}>
        <PermissionButton
          icon="add"
          type="c7n-pro"
          color={ButtonColor.primary}
          onClick={handleCreateBarcode}
          disabled={!isSelect}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.createBarCode`).d('创建条码')}
        </PermissionButton>
        <PermissionButton
          icon="delete"
          type="c7n-pro"
          color={ButtonColor.default}
          onClick={handleDelete}
          disabled={!selectedMaterialLotIds.length}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.delete').d('删除')}
        </PermissionButton>

        {/* <FRPrintButton
          kid="MATERIAL_LOT_PRINT"
          queryParams={selectedMaterialLotIds}
          disabled={!selectedMaterialLotIds.length}
          printObjectType="MATERIAL_LOT"
        /> */}
        <TemplatePrintButton
          disabled={!selectedMaterialLotIds.length}
          printButtonCode='BARCODE_REGISTRATION'
          printParams={{ materialLotId: selectedMaterialLotIds.join(',') }}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
          searchCode="cptmzc1"
          customizedCode="cptmzc1"
          dataSet={headerTableDs}
          columns={headerTableColumns}
          highLightRow={false}
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
        />
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.materialLotList`).d('物料批列表')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            {lineTableDs && (
              <Table
                queryBar={TableQueryBarType.bar}
                queryBarProps={{
                  fuzzyQuery: false,
                }}
                customizedCode="cptmzc2"
                className={styles['expand-table']}
                dataSet={lineTableDs}
                highLightRow={false}
                columns={lineTableColumns}
                onRow={({ record }) => {
                  return {
                    onClick: () => {
                      lineTableDs.select(record);
                    },
                  };
                }}
              />
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inbound.barCodeRegistration', 'tarzan.common'],
})(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(RegistrationList),
);
