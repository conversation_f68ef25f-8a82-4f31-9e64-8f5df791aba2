import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import request from 'utils/request';
import { BASIC } from '@/utils/config';
import DashboardCard from '../DashboardCard.jsx';

const tenantId = getCurrentOrganizationId();
// 各车间验收确认情况分布URL
const url = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-ledger-summary/location-status`;

const WorkshopAcceptConfirm = (props) => {

  const { timers, assetSetId } = props;

  const chartBarRef = useRef(null);
  const [dimensionsData, setDimensionsData] = useState<any>([]);
  const [sourceData, setSourceData] = useState<any>([]);

  // 查询车间验收确认情况分布
  const fetchData = useCallback(async () => {
    const res = await request(url, {
      method: 'GET',
      query: { assetSetId },
    });
    const xTemp:any=['locationNames', '已验收', '未验收', '已确认', '未确认'];
    setDimensionsData(xTemp);
    if(res && res.locationNames) {
      const yTemp:any = res.locationNames.map((item, index) => {
        return {
          locationNames: item,
          '已验收': res.acceptNum[index],
          '未验收': res.nonAcceptNum[index],
          '已确认':  res.confirmNum[index],
          '未确认':  res.nonConfirmNum[index],
        }
      });
      setSourceData(yTemp);
    } else {
      setSourceData([]);
    }
  }, [assetSetId]);

  // 初始化数据查询
  useEffect(() => {
    fetchData();
  },[] );

  // 定时更新查询
  useEffect(()=>{
    let time;
    if(timers) {
      time = setInterval(() => {
        fetchData();
      }, (timers)*60000)
    } else if(assetSetId){
      fetchData();
    }
    return () => {
      clearInterval(time)
    }
  },[timers, assetSetId]);

  // 柱图表参数
  const optionBar: any = useMemo(() => {
    return {
      title:{
        top:'3%',
        text: '各车间验收确认情况分布',
        left: 'center',
        textStyle: {
          fontWeight: 'bold', // 加粗
          color: '#00fff4',
        },
      },
      color:['#5470c6', '#ef6567', '#709ea6', '#c9866b'],
      legend: {
        bottom: 'bottom',
        textStyle: {
          color: '#fff',
        },
      },
      tooltip: {},
      grid: {
        bottom: '3%',
        containLabel: true,
      },
      dataset: {
        dimensions: dimensionsData,
        // dimensions: ['product', '2015', '2016', '2017'],
        source: [
          ...sourceData,
          // { product: 'Matcha Latte', 2015: 43.3, 2016: 85.8, 2017: 93.7 },
          // { product: 'Milk Tea', 2015: 83.1, 2016: 73.4, 2017: 55.1 },
          // { product: 'Cheese Cocoa', 2015: 86.4, 2016: 65.2, 2017: 82.5 },
          // { product: 'Walnut Brownie', 2015: 72.4, 2016: 53.9, 2017: 39.1 },
        ],
      },
      xAxis: [
        {
          type: 'category',
          axisLine: { // 控制轴线样式
            lineStyle: {
              color: '#fff', // 设置轴线颜色
            },
          },
          axisLabel: { // 控制轴标签样式
            interval: 0, // 显示所有标签
            rotate: 45, // 旋转30度
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      yAxis: [
        {
          axisTick: { show: true },// 坐标刻度是否显示
          splitLine: { show: false }, // 是否显示背景分隔线
          axisLine: {show: true }, // 控制轴线
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      dataZoom: [
        {
          type: 'inside', // 可以选择 'inside' 或 'slider'
          show: true,
          xAxisIndex: [0],
        },
      ],
      series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }, { type: 'bar' }],
    };
  }, [dimensionsData, sourceData]);

  useEffect(() => {
    if (!chartBarRef.current) return;
    // 初始化echarts实例
    const myBarChart = echarts.init(chartBarRef.current);
    myBarChart.setOption(optionBar);

    const handleResize = debounce(() => {
      myBarChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartBarRef.current);

    return () => {
      observer.disconnect();
    };
  }, [optionBar]);

  return (
    <DashboardCard height="100%">
      <div style={{width: '100%', height: '100%', display: 'flex'}}>
        <div ref={chartBarRef} style={{width: '100%', height: '100%'}}/>
      </div>
    </DashboardCard>
  );
};
export default WorkshopAcceptConfirm;
