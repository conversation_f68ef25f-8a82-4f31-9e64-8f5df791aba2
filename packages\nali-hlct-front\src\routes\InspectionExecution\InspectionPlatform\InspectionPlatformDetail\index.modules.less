:global {
  .c7n-pro-popup.c7n-pro-popup-wrapper.c7n-pro-select-popup.c7n-pro-select-popup-single.c7n-pro-select-popup-placement-bottomLeft {
    z-index: 9999;
  }
  .c7n-pro-popup.c7n-pro-popup-wrapper.c7n-pro-form-tooltip.c7n-pro-tooltip-popup.c7n-pro-tooltip-popup-placement-bottomLeft {
    z-index: 888;
  }
}
.inspection-platform-detail {
  .input-color-red {
    :global {
      input {
        color: red !important;
      }
    }
  }

  .input-color-yellow {
    :global {
      input {
        color: orange !important;
      }
    }
  }

  .input-color-green {
    :global {
      input {
        color: green !important;
      }
    }
  }

  .select-color-red {
    :global {
      input {
        color: red !important;
      }

      .c7n-pro-select-multiple-block {
        color: red !important;
      }
    }
  }

  .select-color-green {
    :global {
      input {
        color: green !important;
      }

      .c7n-pro-select-multiple-block {
        color: green !important;
      }
    }
  }

  .button-text {
    border: none !important;
    margin-left: 5px;

    i {
      color: rgb(8, 64, 248) !important;
    }
  }

  .input-border-red {
    border: 1px solid red;
  }

  .table-col-attachment {
    margin-left: 0 !important;
  }

  .number-input-left {
    :global {
      input {
        text-align: left !important;
      }
    }
  }
}

.split-menu {
  :global {
    .c7n-menu-item > a,
    .c7n-menu-item > a:hover {
      color: #000;
    }

    .c7n-menu-item.c7n-menu-item-selected {
      background-color: inherit;
    }
  }
}

.table-tooltip-tag {
  font-size: 12px;
  margin: 2px 8px;
  padding: 0 4px;
  border-radius: 2px;
}
