import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Form, Lov, Table, TextField, NumberField } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Popconfirm } from 'choerodon-ui';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import uuid from 'uuid/v4';
import notification from 'utils/notification';
import { useMemoizedFn } from 'ahooks';
import { BASIC } from '@utils/config';
import myInstance from '@utils/myAxios';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { downloadFile } from '@services/api';
import { API_HOST } from '@/utils/constants';

import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import ExcelUpload, { ExcelUploadProps } from './components/ExcelUpload';
import { formDS } from '../stores/grr';
import { analyseResultDS } from '../stores/DetailDS';
import GRRGraphic from '../components/GRRGraphic';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

interface GraphicDataProps {
  grrVariationChartInfo: Object; // 变差构成图
  xBarChartRule: object; // xBar图rules
  rChartRule: object; // r图rules
  data: []; // xBar-r图数据
  grrUserColumnChartInfo: []; // 测量人与零件交互作用图数据
  msaResult: string; // 分析结果
  msaConclusion: string; // 分析结论
  grrUserChartInfo: []; // 测量人链图数据
  grrColumnChartInfo: []; // 零件链图
  grrTableInfo: {}; // 表格基本信息
  tableInfo: [];
}

// 默认数据
const apiDataDefault = [
  {
    measureDate: '',
    measureTableList: [
      {
        measureDataColumn: 1,
        measureDataRow: 1,
        measureDataValue: '',
      },
    ],
    range: '',
    average: '',
  },
];

const Grr = props => {
  const { msaStatus, currentUserFlag, checkLocation } = props;

  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
      }),
    [],
  );
  // 分析结果DS
  const analyseResultDs = useMemo(() => new DataSet(analyseResultDS()), []);
  const [graphicData, setGraphicData] = useState<GraphicDataProps>({
    grrVariationChartInfo: {}, // 变差构成图
    xBarChartRule: {}, // xBar图rules
    rChartRule: {}, // r图rules
    data: [], // xBar-r图数据
    msaResult: '', // 分析结果
    msaConclusion: '', // 分析结论
    grrColumnChartInfo: [], // 零件链图
    grrUserChartInfo: [], // 测量人链图
    grrUserColumnChartInfo: [], // 测量人与零件交互作用数据
    grrTableInfo: {}, // 表格基本信息
    tableInfo: [],
  });

  const templateData = [
    {
      measureDataRow: 1,
      measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
      uuid: uuid(),
      type: 'measureDataValue',
    },
  ];

  const [currentRow, setCurrentRow] = useState('');
  const [apiData, setApiData] = useState([]);
  const [defaultData, setDefaultData] = useState(templateData); // 有默认数据时初始化
  const [havaSaveData, setHaveSaveData] = useState(false);
  const [originData, setOriginData] = useState([]);

  const [defaultField, setDefaultField] = useState([
    {
      name: 'measureDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partNum`).d('零件编号'),
    },
    {
      name: 'measureDataRow',
      type: FieldType.string,
    },
    {
      name: 'measureDataColumn1',
      computedProps: {
        type: ({ record }) => {
          if (record.get('type') === 'measureDate') {
            return FieldType.dateTime;
          }
          return FieldType.number;
        },
        disabled: ({ record }) => {
          if (record.get('type') === 'average' || record.get('type') === 'range') {
            return true;
          }
        },
        required: ({ record }) => {
          return record.get('type') === 'measureDataValue';
        },
      },
    },
  ]);

  const userDs = useMemo(
    () =>
      new DataSet({
        forceValidate: true,
        autoCreate: false,
        selection: false,
        paging: false,
        primaryKey: 'uuid',
        data: defaultData,
        fields: defaultField,
      }),
    [],
  );

  const [currentColumns, setCurrentColumns] = useState([
    {
      name: 'measureDataRow',
      width: 60,
      lock: ColumnLock.left,
      align: ColumnAlign.center,
      renderer: ({ value }) => {
        if (value) {
          return (
            <>
              <span>{value}</span>
            </>
          );
        }
      },
    },
    {
      name: 'measureDataColumn1',
      editor: record => {
        return !(record.get('type') === 'average' || record.get('type') === 'range');
      },
      align: ColumnAlign.center,
      header: () => (
        <>
          <span>1</span>
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteColumn('measureDataColumn1')}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              funcType="flat"
              shape="circle"
              size="small"
            />
          </Popconfirm>
        </>
      ),
    },
  ]); // 实际columns

  useEffect(() => {
    myInstance
      .get(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/info/ui?msaTaskLineId=${props.msaTaskLineId}`,
      )
      .then(res => {
        if (res.data.success) {
          // 处理数据
          const { tolerance, sampleDescription } = res.data.rows;
          formDs.current?.set('checkLocation', checkLocation);
          if (tolerance) formDs.current?.set('tolerance', tolerance);
          if (sampleDescription) formDs.current?.set('sampleDescription', sampleDescription);
          if ((res.data.rows.tableInfo || []).length) {
            setApiData(res.data.rows.tableInfo);
            setHaveSaveData(true);
          } else {
            setApiData(apiDataDefault);
            setHaveSaveData(false);
          }
          handleUpdateChartData(res.data.rows || {});
        } else {
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          });
        }
      });
  }, []);

  useEffect(() => {
    const colums = (apiData || []).length; // 总共有多少列数据
    if (colums > 0) {
      const transformDataResult = [];
      let tiledData: any = [];

      apiData.forEach((item: any) => {
        tiledData = [...tiledData, ...item.measureTableList];
      });

      const userIdArr = [];
      tiledData.forEach(item => {
        if (!userIdArr.includes(item.measuredBy)) {
          userIdArr.push(item.measuredBy);
        }
      });

      // 按人分组
      let doubleArr: any = [];
      let maxRow = 0;
      userIdArr.forEach(userId => {
        const arr = [];
        tiledData.forEach(item => {
          if (item.measuredBy === userId) {
            arr.push(item);
          }
          if (item.measureDataRow > maxRow) {
            maxRow = item.measureDataRow;
          }
        });
        doubleArr = [...doubleArr, arr];
      });

      doubleArr.forEach(userItem => {
        // 生成测量值数据

        const groupUUid = uuid();
        for (let rowSeq = 0; rowSeq < maxRow; rowSeq++) {
          const obj = {
            measureDate: groupUUid,
            uuid: uuid(),
            type: 'measureDataValue',
            measureDataRow: '',
            measuredBy: '',
            measuredByName: '',
            saveStatus: true,
          };

          for (let colSeq = 0; colSeq < colums; colSeq++) {
            userItem.forEach((item: any) => {
              if (item.measureDataRow === rowSeq + 1 && item.measureDataColumn === colSeq + 1) {
                obj[`measureDataColumn${colSeq + 1}`] = item.measureDataValue;
                obj.measureDataRow = item.measureDataRow;
                obj.measuredBy = item.measuredBy;
                obj.measuredByName = item.measuredByName;
              }
            });
          }
          transformDataResult.push(obj);
        }
      });

      // 生产行列配置
      const feild = [
        {
          name: 'measureDate',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.partNum`).d('零件编号'),
        },
        {
          name: 'measureDataRow',
          type: FieldType.string,
        },
        {
          name: 'transferResponsibleUserLov',
          type: FieldType.object,
          lovCode: 'YP.QIS.USER.ORG',
          ignore: FieldIgnore.always,
          lovPara: { tenantId },
          textField: 'realName',
          required: true,
        },
        {
          name: 'measuredBy',
          bind: 'transferResponsibleUserLov.id',
          required: true,
        },
        {
          name: 'measuredByName',
          bind: 'transferResponsibleUserLov.realName',
        },
      ];

      const column = [
        {
          name: 'measureDataRow',
          width: 60,
          lock: ColumnLock.left,
          align: ColumnAlign.center,
          renderer: ({ value }) => {
            if (value) {
              return (
                <>
                  <span>{value}</span>
                </>
              );
            }
          },
        },
      ];
      for (let colSeq = 0; colSeq < colums; colSeq++) {
        feild.push({
          name: `measureDataColumn${colSeq + 1}`,
          type: FieldType.number,
          // @ts-ignore
          computedProps: {
            required: ({ record }) => {
              return record.get('type') === 'measureDataValue';
            },
          },
        });
        column.push({
          name: `measureDataColumn${colSeq + 1}`,
          editor: () => {
            return !['IMPROVING', 'COMPLETED'].includes(msaStatus) && currentUserFlag;
          },
          align: ColumnAlign.center,
          // @ts-ignore
          header: () => (
            <>
              <span>{colSeq + 1}</span>
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => deleteColumn(`measureDataColumn${colSeq + 1}`)}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <PermissionButton
                  type="c7n-pro"
                  icon="remove"
                  disabled={havaSaveData}
                  funcType="flat"
                  shape="circle"
                  size="small"
                />
              </Popconfirm>
            </>
          ),
        });
      }
      feild.forEach((item: any) => {
        userDs.addField(item.name, {
          ...item,
        });
      });
      setDefaultField(feild);
      setCurrentColumns(column);
      setDefaultData(transformDataResult);
      setOriginData(transformDataResult);
      userDs.loadData(transformDataResult);
    }
  }, [apiData, havaSaveData]);

  const handleUpdateChartData = dataSource => {
    const {
      tableInfo = [],
      grrVariationChartInfo = {},
      xBarChartRule = {},
      rChartRule = {},
      data = [],
      msaResult = '',
      msaConclusion = '',
      grrUserChartInfo = [],
      grrColumnChartInfo = [],
      grrUserColumnChartInfo = [],
      grrTableInfo = {},
    } = dataSource;
    setGraphicData({
      tableInfo,
      grrVariationChartInfo, // 变差构成图数据
      xBarChartRule, // xBar图rules
      rChartRule, // r图rules
      data, // xBar-r图数据
      msaResult, // 分析结果
      msaConclusion, // 分析结论
      grrColumnChartInfo, // 零件链图
      grrUserChartInfo, // 测量人链图
      grrUserColumnChartInfo, // 测量人与零件交互作用数据
      grrTableInfo, // 表格基本信息
    });
    analyseResultDs.loadData([{
      msaResult,
      msaConclusion,
    }])
  };

  const deleteColumn = useMemoizedFn(name => {
    setCurrentColumns(prevColumns => prevColumns.filter(item => item.name !== name));
    userDs.getField(name)?.set('required', false);
    userDs.getField(name)?.set('ignore', 'always');
  });

  const addColumn = async () => {
    const maxNumber = Number(
      currentColumns[currentColumns.length - 1].name.replace('measureDataColumn', ''),
    );
    const columnName = `measureDataColumn${maxNumber + 1}`;
    userDs.addField(columnName, {
      type: FieldType.number,
      required: true,
    });
    setDefaultField([
      ...defaultField,
      {
        name: columnName,
        type: FieldType.number,
        required: true,
      },
    ]);
    const newColumns: Array<object> = [
      ...currentColumns,
      {
        name: columnName,
        editor: true,
        align: ColumnAlign.center,
        header: () => (
          <>
            <span>{maxNumber + 1}</span>
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => deleteColumn(columnName)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <PermissionButton
                type="c7n-pro"
                icon="remove"
                funcType="flat"
                shape="circle"
                size="small"
              />
            </Popconfirm>
          </>
        ),
      },
    ];
    setCurrentColumns(newColumns);
  };

  const addMeasuredValue = () => {
    const data = userDs.toData();
    let max = 0;
    data.forEach((item: any) => {
      if (item.measureDataRow > max) {
        max = item.measureDataRow;
      }
    });

    userDs.loadData([
      ...userDs.toData(),
      {
        // @ts-ignore
        measureDataRow: Number(max) + 1,
        measureDate: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
        uuid: uuid(),
        type: 'measureDataValue',
      },
    ]);
  };

  // 添加测量人
  const addBy = () => {
    const data = userDs.toData();
    let maxRow = 0;
    data.forEach(item => {
      if (item.measureDataRow > maxRow) {
        maxRow = item.measureDataRow;
      }
    });
    const newLine: any = [];
    const groupUUid = uuid();
    for (let num = 0; num < maxRow; num++) {
      newLine.push({
        measureDataRow: num + 1,
        measureDate: groupUUid, // uuid相同的可以为一组
        uuid: uuid(),
        type: 'measureDataValue',
      });
    }

    userDs.loadData([...userDs.toData(), ...newLine]);
  };

  const deleteBy = record => {
    const groupId = record.get('measureDate');
    const data = userDs.toData();
    userDs.loadData(data.filter(item => item.measureDate !== groupId));
    notification.success({
      message: intl.get(`${modelPrompt}.notification.deleteSuccess`).d('删除成功'),
    });
  };

  // 添加测量次数
  const addMeasureCount = () => {
    // 找出所有测量人，每个人添加一个测量次数
    const data = userDs.toData();
    const byUuid = [];
    const userObj = [];
    let max = 0; // 找到最大的测量次数号
    data.forEach((item: any) => {
      if (item.measureDataRow > max) {
        max = item.measureDataRow;
      }
      if (!byUuid.includes(item.measureDate)) {
        byUuid.push(item.measureDate);
        userObj.push(item);
      }
    });
    // 给每行增加测量次数
    const newData: any = [];
    userObj.forEach(item => {
      newData.push({
        measureDataRow: Number(max) + 1,
        measureDate: item.measureDate,
        uuid: uuid(),
        type: 'measureDataValue',
        measuredBy: item.measuredBy,
        measuredByName: item.measuredByName,
      });
    });
    userDs.loadData([...userDs.toData(), ...newData]);
  };

  const deleteMeasureCount = () => {
    // 需要知道当前选中的行号 currentRow
    if (currentRow) {
      let maxRow = 0;
      originData.forEach((item: any) => {
        if (item.measureDataRow > maxRow) {
          maxRow = item.measureDataRow;
        }
      });
      if (Number(currentRow) === 1 && maxRow === 1) {
        notification.error({
          message: intl.get(`${modelPrompt}.notification.measureTimeMoreThanOne`).d('测量次数最少为一个'),
        });
        return false;
      }
      if (Number(currentRow) > maxRow) {
        const data = userDs.toData();
        const newData = data.filter((item: any) => item.measureDataRow !== currentRow);
        userDs.loadData(newData);
        notification.success({});
      } else {
        notification.error({
          message: intl.get(`${modelPrompt}.notification.deleteSavedData`).d('已保存的数据不允许删除'),
        });
      }
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.notification.selectMeasureTime`).d('请先选择对应的测量次数'),
      });
    }
  };

  const groups = [
    {
      name: 'measureDate',
      align: ColumnAlign.center,
      parentField: undefined,
      type: 'column',
      lock: ColumnLock.left,
      columnProps: {
        width: 150,
        header: () => (
          <>
            <span>{intl.get(`${modelPrompt}.partNum`).d('零件编号')}</span>
            <PermissionButton
              type="c7n-pro"
              icon="add"
              disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
              onClick={addColumn}
              funcType="flat"
              shape="circle"
              size="small"
            />
          </>
        ),
        renderer: ({ record }) => (
          <div className={styles['cell-group-lov']}>
            {/* <Lov
              name="transferResponsibleUserLov"
              disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
              record={record}
              onChange={value => {
                transferResponsibleUserLovChange(value, record);
              }}
            /> */}
            <TextField
              name="measuredBy"
              disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
              record={record}
              onChange={value => {
                transferResponsibleUserLovChange(value, record);
              }}
            />
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => deleteBy(record)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <PermissionButton
                type="c7n-pro"
                icon="remove"
                disabled={record.get('saveStatus')}
                funcType="flat"
                shape="circle"
                size="small"
              />
            </Popconfirm>
          </div>
        ),
      },
    },
  ];

  const transferResponsibleUserLovChange = (value, record) => {
    // c7n bug：需手动修改对应组下的所有测量人数据
    const data = userDs.toData();
    data.forEach((item: any) => {
      if (record.get('measureDate') === item.measureDate) {
        // item.measuredBy = value.id || '';
        // item.measuredByName = value.realName || '';
        item.measuredBy = value;
      }
    });
    userDs.loadData(data);
  };

  const handleSave = async () => {
    // @ts-ignore
    const validateResult = await userDs.validate();
    const formValidate = await formDs.validate();
    if (!validateResult || !formValidate) {
      return;
    }
    const originData = userDs.toData();
    let measureDataColumnArr = [];
    const measureDataRowArr = [];
    originData.forEach((item: any) => {
      if (item.type === 'measureDataValue' && measureDataColumnArr.length < 1) {
        // 打平key
        measureDataColumnArr = Object.keys(item).filter(
          word => word.indexOf('measureDataColumn') !== -1,
        );
      }
      if (item.type === 'measureDataValue') {
        measureDataRowArr.push(item.measureDataRow);
      }
    });

    const resultData: Array<any> = [];

    measureDataColumnArr.map(colums => {
      const measureTableList = [] as any;
      const uuidArr:any = [];
      measureDataRowArr.forEach(rowSquence => {
        originData.forEach((originDataItem: any) => {
          if (
            originDataItem.type === 'measureDataValue' &&
            originDataItem.measureDataRow === rowSquence
          ) {
            if (!uuidArr.includes(originDataItem.uuid)) {
              measureTableList.push({
                measureDataValue: originDataItem[colums],
                measureDataColumn: colums.replace('measureDataColumn', ''),
                measureDataRow: rowSquence,
                measuredBy: originDataItem.measuredBy,
                measuredByName: originDataItem.measuredByName,
              });
              uuidArr.push(originDataItem.uuid);
            }
          }
        });
      });
      resultData.push({
        measureTableList,
      });
    });
    const formData = formDs.toData();
    myInstance
      .post(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/save/ui`, {
        msaTaskLineId: props.msaTaskLineId,
        ...formData[0],
        tableInfo: resultData,
      })
      .then(res => {
        if (res.data.success) {
          // 处理数据
          setApiData(res.data.rows.tableInfo);
          setHaveSaveData(true);
          props.updateHeaderInfo(); // 更新头部
          handleUpdateChartData(res.data.rows || {});
          notification.success({});
        } else {
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          });
        }
      });
  };

  const headerRowClick = record => {
    setCurrentRow(record.get('measureDataRow'));
  };

  // 模板下载
  const downloadTemp = async () => {
    await downloadFile({
      requestUrl: `/himp/v1/${tenantId}/template/YP.QIS_MSA_IMPORT_GRR/excel`,
      queryParams: [{ name: 'tenantId', value: tenantId }],
      method: 'GET',
    });
  };

  const handleUploadSuccess = res => {
    setApiData(res.rows.tableInfo);
    setHaveSaveData(false);
    notification.success({
      message: intl.get(`${modelPrompt}.notification.importSuccess`).d('导入成功'),
    });
  };

  const excelUploadProps: ExcelUploadProps = {
    url: `${API_HOST}${
      BASIC.TARZAN_SAMPLING
    }/v1/${tenantId}/qis-msa-analysis/import/ui?msaTaskLineId=${
      props.msaTaskLineId
    }&tolerance=${formDs.current?.get('tolerance') || ''}&sampleDescription=${formDs.current?.get(
      'sampleDescription',
    ) || ''}`,
    params: {},
    onSuccess: res => handleUploadSuccess(res),
  };
  return (
    <div>
      <Button
        color={ButtonColor.primary}
        onClick={handleSave}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        {intl.get(`${modelPrompt}.button.save`).d('保存')}
      </Button>
      <Button onClick={downloadTemp} icon="get_app">
        {intl.get(`${modelPrompt}.templateDownload`).d('模板下载')}
      </Button>
      <ExcelUpload {...excelUploadProps} />
      <Form
        dataSet={formDs}
        columns={3}
        style={{ marginTop: 10 }}
        disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
      >
        <TextField name="checkLocation" />
        <NumberField name="tolerance" />
        <TextField name="sampleDescription" />
      </Form>
      <Table
        buttons={[
          <>
            <Button
              funcType={FuncType.flat}
              color={ButtonColor.blue}
              onClick={addBy}
              disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
            >
              {intl.get(`${modelPrompt}.button.addBy`).d('添加测量人')}
            </Button>
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={deleteMeasureCount}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <Button
                funcType={FuncType.flat}
                color={ButtonColor.blue}
                disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
              >
                {intl.get(`${modelPrompt}.button.deleteMeasureCount`).d('删除测量次数')}
              </Button>
            </Popconfirm>
            <Button
              funcType={FuncType.flat}
              color={ButtonColor.blue}
              onClick={addMeasureCount}
              disabled={['IMPROVING', 'COMPLETED'].includes(msaStatus) || !currentUserFlag}
            >
              {intl.get(`${modelPrompt}.button.addMeasureCount`).d('添加测量次数')}
            </Button>
          </>,
        ]}
        columnDraggable
        columnTitleEditable
        aggregation={false}
        border
        dataSet={userDs}
        columns={currentColumns as any}
        groups={groups as any}
        onRow={({ record }) => {
          return {
            onClick: () => {
              headerRowClick(record);
            },
          };
        }}
      />
      <GRRGraphic dataSoure={graphicData} analyseResultDs={analyseResultDs} />
    </div>
  );
};

export default Grr;
