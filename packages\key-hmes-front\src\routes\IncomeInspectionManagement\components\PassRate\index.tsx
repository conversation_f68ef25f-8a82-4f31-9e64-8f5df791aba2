import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { filterNullValueObject, getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import DashboardCard from '../DashboardCard.jsx';
import styles from '../../index.module.less';

const tenantId = getCurrentOrganizationId();
// 检验合格率趋势图查询URL
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/pass-rate`;

const PassRate = (props) => {

  const { materialId, timers } = props;

  const chartBarRef = useRef(null);
  const [xData1, setXData1] = useState<any>([]);
  const [yData1, setYData1] = useState<any>([]);
  const chartLineRef = useRef(null);
  const [xData2, setXData2] = useState<any>([]);
  const [yData2, setYData2] = useState<any>([]);

  // 初始化数据查询
  useEffect(() => {
    fetchData();
  }, [materialId]);

  // 定时更新查询
  useEffect(()=>{
    let time;
    if(timers) {
      time = setInterval(() => {
        fetchData();
      }, (timers)*60000)
    } else if(materialId){
      fetchData();
    }
    return () => {
      clearInterval(time)
    }
  },[timers,materialId]);

  // 查数据
  const fetchData = useCallback(async () => {
    const params ={
      materialId,
    };
    const res = await request(url, {
      method: 'GET',
      query: filterNullValueObject(params),
    });
    const x1Temp:any=[];
    const x2Temp:any=[];
    const y1Temp:any=[];
    const y2Temp:any=[];
    if(res?.barChartData?.inspectionDate?.length){
      res?.barChartData?.inspectionDate?.forEach((i)=>{
        x1Temp.push(i);
      })
      res?.barChartData?.qualificationRate?.forEach((i)=>{
        y1Temp.push(i || 0);
      })
    }
    setXData1(x1Temp);
    setYData1(y1Temp);
    if(res?.lineChartData?.inspectionDate?.length){
      res?.lineChartData?.inspectionDate?.forEach((i)=>{
        x2Temp.push(i);
      })
      res?.lineChartData?.qualificationRate?.forEach((i)=>{
        y2Temp.push(i || 0);
      })
    }
    setXData2(x2Temp);
    setYData2(y2Temp);
  }, [materialId]);

  // 转换成百分比
  const LabelFormatter = (value: { data }) => {
    if (value.data) {
      // return `${(value.data * 100).toFixed(2)}%`;
      return `${value.data}%`;
    }
    return 0;
  };

  // 柱图表参数
  const optionBar: any = useMemo(() => {
    return {
      title:{},
      grid: {
        left: '4%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data:xData1,
        axisLine: { // 控制轴线样式
          lineStyle: {
            color: '#fff', // 设置轴线颜色
          },
        },
        axisLabel: { // 控制轴标签样式
          inside: true,
          textStyle: {
            color: '#fff', // 设置轴标签文字颜色
          },
        },
        axisTick: {
          show: false,
        },
        z: 10,
      },
      yAxis: [
        {
          type: 'value',
          max: 100,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { // 分隔线样式
            lineStyle: {
              type: 'dashed', // 改变分隔线样式为虚线 默认是直线
            },
          },
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
            formatter: value => `${value}%`,
          },
        },
      ],
      series: [
        {
          type: 'bar',
          data: yData1,
          // data: [12.24, 50, 50],
          showBackground: true,
          barWidth: '20',
          barCategoryGap: '10%',
          label: {
            show: true,
            position: 'top',
            color:'#FFF',
            formatter: LabelFormatter,
          },
          backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)',
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1, // 渐变方向从上到下
              [
                {offset: 0, color: '#00A2FF'},
                {offset: 1, color: '#00CCD2'},
              ],
            ),
          },
        },
      ],
    };
  }, [xData1, yData1]);
  const optionLine: any = useMemo(() => {
    return {
      title:{
        top:'3%',
        text: '检验合格率趋势图',
        left: '10%',
        textStyle: {
          fontWeight: 'bold', // 加粗
          color: '#00fff4',
        },
      },
      xAxis: {
        type: 'category',
        data:xData2,
        axisLine: { // 控制轴线样式
          lineStyle: {
            color: '#fff', // 设置轴线颜色
          },
        },
        axisLabel: { // 控制轴标签样式
          textStyle: {
            color: '#fff', // 设置轴标签文字颜色
          },
        },
      },
      yAxis: [
        {
          type: 'value',
          max: 100,
          axisLine: { show: false },
          axisTick: { show: false },// 坐标刻度是否显示
          splitLine: { // 分隔线样式
            lineStyle: {
              type: 'dashed', // 改变分隔线样式为虚线 默认是直线
            },
          },
          axisLabel: { show: false },
          // axisLabel: { // 控制轴标签样式
          //   textStyle: {
          //     color: '#fff', // 设置轴标签文字颜色
          //   },
          //   formatter: value => `${(value * 100).toFixed(2)} %`,
          // },
        },
      ],
      series: [
        {
          type: 'line',
          smooth: true,
          data: yData2,
          // data: [0, 0, 50, 50, 0, 100, 0],
          color:'#00FFF4',
          label: {
            show: true,
            position: 'top',
            color:'#FFF',
            formatter: LabelFormatter,
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1, // 渐变方向从上到下
              [
                {offset: 0, color: '#0E5FFF'},
                {offset: 0.5, color: '#00FFF4'},
                {offset: 1, color: '#0B81FD'},
              ],
            ),
          },
          lineStyle: {
            width: 5,  // 设置线条的粗细为3
          },
        },
      ],
    };
  }, [xData2, yData2]);

  useEffect(() => {
    if (!chartBarRef.current) return;
    if (!chartLineRef.current) return;
    // 初始化echarts实例
    const myBarChart = echarts.init(chartBarRef.current);
    myBarChart.setOption(optionBar);

    const myLineChart = echarts.init(chartLineRef.current);
    myLineChart.setOption(optionLine);

    const handleResize = debounce(() => {
      myBarChart.resize();
      myLineChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartBarRef.current);
    observer.observe(chartLineRef.current);

    return () => {
      observer.disconnect();
    };
  }, [optionBar, optionLine]);

  return (
    <DashboardCard height="100%">
      <div className={styles['my-chart']}>
        <div style={{ width: '100%', height: '100%', display: 'flex' }}>
          <div ref={chartBarRef} style={{ width: '30%', height: '100%' }} />
          <div ref={chartLineRef} style={{ width: '70%', height: '100%' }} />
        </div>
      </div>
    </DashboardCard>
  );
};
export default PassRate;
