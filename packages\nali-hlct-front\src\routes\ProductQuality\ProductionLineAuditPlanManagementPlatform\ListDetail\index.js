/* eslint-disable array-callback-return */
/**
 * @Description: 产线审核计划管理平台-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useState, useMemo, useCallback } from 'react';
import {
  DataSet,
  Form,
  Lov,
  Select,
  Table,
  TextField,
  DatePicker,
  Menu,
  Dropdown,
  Modal,
  TextArea,
  Attachment,
} from 'choerodon-ui/pro';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Popconfirm } from 'choerodon-ui';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { Content, Header } from 'components/Page';
import moment from 'moment';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import { getCurrentOrganizationId, getCurrentUser } from 'hzero-front/lib/utils/utils';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import formatterCollections from 'utils/intl/formatterCollections';
import { ExpandCardC7n } from '@components/tarzan-ui';
import { useDataSetEvent } from 'utils/hooks';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import notification from 'utils/notification';
import {
  tableDetailDS,
  tableDetailLineDS,
} from '../stores/ProductionLineAuditPlanManagementPlatformDetailDS';

const tenantId = getCurrentOrganizationId();
const currentUser = getCurrentUser();
const modelPrompt = 'tarzan.prodlineReview.prodlineReviewPlan';
const statusList = ['NEW', 'REJECTED'];
const ProductionLineAuditPlanManagementPlatformDetail = props => {
  const {
    match: {
      params: { id },
    },
    location: { state = {} },
  } = props;
  const { status, prodlineReviewTmpId } = state;

  const tableDetailDs = useMemo(() => new DataSet(tableDetailDS()), []);

  const tableDetailLineDs = useMemo(() => new DataSet(tableDetailLineDS()), []);

  const [editFlag, setEditFlag] = useState(false);

  const [disabledFlag, setDisabledFlag] = useState(false);

  const [lineBtnFlag, setLineBtnFlag] = useState(false);

  const [saveFlag, setSaveFlag] = useState(false);

  const [judege, setJudge] = useState(true);

  const [delIdList, setDelIdList] = useState([]);

  const [ids, setId] = useState('');

  const [lineSelected, setLineSelected] = useState([]);

  const [prodLinePlanStatus, setStatus] = useState('');



  const handleDataSetSelect = () => {
    const data = tableDetailLineDs.selected.map((ele) => ele.toData());
    if (data.length > 0) {
      setLineSelected(tableDetailLineDs.selected)
    } else {
      setLineSelected([])
    }
  };
  useDataSetEvent(tableDetailLineDs, 'select', handleDataSetSelect);
  useDataSetEvent(tableDetailLineDs, 'selectAll', handleDataSetSelect);
  useDataSetEvent(tableDetailLineDs, 'unselect', handleDataSetSelect);
  useDataSetEvent(tableDetailLineDs, 'unselectAll', handleDataSetSelect);
  useEffect(() => {
    if (status === 'create') {
      setEditFlag(true);
      setDisabledFlag(false);
      setLineBtnFlag(false);
      setSaveFlag(false);
      const newObj = {
        status: 'NEW',
        creationDate: moment(moment().format('YYYY-MM-DD')),
        creationByName: currentUser.realName,
        createdBy: currentUser.id,
      };
      tableDetailDs.loadData([newObj]);
    } else if (status === 'module') {
      setEditFlag(true);
      setDisabledFlag(false);
      setLineBtnFlag(false);
      setSaveFlag(false);
      queryModule(prodlineReviewTmpId);
    } else {
      setEditFlag(false);
      setDisabledFlag(true);
      setLineBtnFlag(true);
      queryDetail();
    }
  }, [status]);
  // 保存
  const handSave = async () => {
    const flag = (await tableDetailDs.validate()) && (await tableDetailLineDs.validate());
    if (flag && tableDetailLineDs.toData().length > 0) {
      setJudge(!judege);
      await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/save/for/ui`, {
        method: 'POST',
        body: {
          ...tableDetailDs.toData()[0],
          lineList: tableDetailLineDs.toJSONData().filter(ele => {
            return ele._status !== 'delete';
          }),
          deleteLineIds: delIdList,
        },
      }).then(res => {
        if (res && !res.failed) {
          setJudge(!judege);
          notification.success({});
          setTimeout(() => {
            props.history.push({
              pathname: `/hwms/production-line-audit-plan-management-platform/create/${res.rows}`,
              state: {
                status: judege,
              },
            });
          }, 500);
        } else {
          return notification.error({ message: res.message });
        }
      });
    } else {
      return notification.error({
        message: intl.get(`${modelPrompt}.notification.inputProdlineReviewElement`).d('请填写产线审核要素'),
      });
    }
  };
  // 保存提交
  const handSaveSubmit = async () => {
    const flag = (await tableDetailDs.validate()) && (await tableDetailLineDs.validate());
    if (flag && tableDetailLineDs.toData().length > 0) {
      await request(
        `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/save/submit/for/ui`,
        {
          method: 'POST',
          body: {
            ...tableDetailDs.toData()[0],
            lineList: tableDetailLineDs.toJSONData().filter(ele => {
              return ele._status !== 'delete';
            }),
            deleteLineIds: delIdList,
          },
        },
      ).then(res => {
        if (res && !res.failed) {
          notification.success({});
          afterSubmit(res.rows);
        } else {
          return notification.error({ message: res.message });
        }
      });
    } else {
      return notification.error({
        message: intl.get(`${modelPrompt}.notification.inputProdlineReviewElement`).d('请填写产线审核要素'),
      });
    }
  };
  // 审核模板进入查询
  const queryModule = async value => {
    await request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/temp/import/head/line/query/for/ui`,
      {
        method: 'GET',
        query: {
          prodlineReviewTmpId: value,
        },
      },
    ).then(res => {
      if (res && !res.failed) {
        const newObj = {
          ...res.rows,
          status: 'NEW',
          creationDate: moment(moment().format('YYYY-MM-DD')),
          creationByName: currentUser.realName,
          createdBy: currentUser.id,
        };
        tableDetailDs.loadData([newObj]);
        const newList = [];
        if (res.rows.tmpElementList && res.rows.tmpElementList.length > 0) {
          res.rows.tmpElementList.forEach(ele => {
            newList.push({
              ...ele,
              status: 'NEW',
              deliveryTemplate: ele.deliveryTemplate ? ele.deliveryTemplate : uuid(),
              responseObj: {
                responsibleEmId: ele.responsibleDeptId,
                unitName: ele.responsibleDeptName,
              },
            });
          });
        }
        tableDetailLineDs.loadData(newList);
        tableDetailLineDs.all.forEach(v => {
          v.setState('myEditing', true);
        });
      } else {
        return notification.error({ message: res.message });
      }
    });
  };
  // 正常进入查询
  const queryDetail = async () => {
    setId(id);
    await request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/detail/query/for/ui`,
      {
        method: 'GET',
        query: {
          prodlineRevplanId: id,
        },
      },
    ).then(res => {
      if (res && !res.failed) {
        const newObj = {
          ...res.rows,
        };
        tableDetailDs.loadData([newObj]);
        const newList = [];
        setStatus(res.rows.status);
        if (statusList.includes(res.rows.status)) {
          setEditFlag(false);
          setSaveFlag(false);
        } else {
          setEditFlag(true);
          setSaveFlag(true);
        }
        if (res.rows.lineList && res.rows.lineList.length > 0) {
          res.rows.lineList.forEach(ele => {
            newList.push({
              ...ele,
              responseUserObj: {
                responsibleEmId: ele.responsibleEmId,
                realname: ele.responsibleEmName,
              },
              responseObj: {
                responsibleDeptId: ele.responsibleDeptId,
                unitName: ele.responsibleDeptName,
              },
            });
          });
        }
        tableDetailLineDs.loadData(newList);
      } else {
        return notification.error({ message: res.message });
      }
    });
  };
  // 保存提交后查询
  const afterSubmit = async id => {
    setId(id);
    await request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/detail/query/for/ui`,
      {
        method: 'GET',
        query: {
          prodlineRevplanId: id,
        },
      },
    ).then(res => {
      if (res && !res.failed) {
        setEditFlag(false);
        setDisabledFlag(true);
        setLineBtnFlag(true);
        const newObj = {
          ...res.rows,
        };
        tableDetailDs.loadData([newObj]);
        const newList = [];
        if (statusList.includes(res.rows.status)) {
          setEditFlag(false);
          setSaveFlag(false);
        } else {
          setEditFlag(true);
          setSaveFlag(true);
        }
        if (res.rows.lineList && res.rows.lineList.length > 0) {
          res.rows.lineList.forEach(ele => {
            newList.push({
              ...ele,
              responseUserObj: {
                responsibleEmId: ele.responsibleEmId,
                realname: ele.responsibleEmName,
              },
              responseObj: {
                responsibleDeptId: ele.responsibleDeptId,
                unitName: ele.responsibleDeptName,
              },
            });
          });
        }
        tableDetailLineDs.loadData(newList);
      } else {
        return notification.error({ message: res.message });
      }
    });
  };

  // 审批同意
  const tableApplicantAgree = async () => {
    await request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/examinea/approve/for/ui`,
      {
        method: 'POST',
        body: {
          prodlineRevplanIds: [tableDetailDs.current.get('prodlineRevplanId')],
          approveStatus: 'AGREE',
        },
      },
    ).then(res => {
      if (res && !res.failed) {
        setJudge(!judege);
        props.history.push({
          pathname: `/hwms/production-line-audit-plan-management-platform/create/${ids}`,
          state: {
            status: judege,
          },
        });
        notification.success({});
      } else {
        notification.error({ message: res.message });
      }
    });
  };
  // 审批拒绝
  const tableSubmitReject = async () => {
    const approvalDs = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'rejectReason',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
          required: true,
        },
      ],
    });
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.rejectReason`).d('驳回原因'),
      children: (
        <>
          <Form dataSet={approvalDs} columns={1}>
            <TextArea name="rejectReason" />
          </Form>
        </>
      ),
      onOk: async () => {
        const vFlag = await approvalDs.validate();
        if (!vFlag) {
          return false;
        }
        const rejectReason = approvalDs.current.get('rejectReason');
        await request(
          `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-revplan/examinea/approve/for/ui`,
          {
            method: 'POST',
            body: {
              prodlineRevplanIds: [tableDetailDs.current.get('prodlineRevplanId')],
              approveStatus: 'REJECTED',
              rejectReason,
            },
          },
        ).then(res => {
          if (res && !res.failed) {
            setJudge(!judege);
            props.history.push({
              pathname: `/hwms/production-line-audit-plan-management-platform/create/${ids}`,
              state: {
                status: judege,
              },
            });
            notification.success({});
          } else {
            notification.error({ message: res.message });
            return false
          }
        });
      },
    });
  };

  const attachmentProps = {
    name: 'deliveryTemplate',
    bucketName: 'qms',
    bucketDirectory: 'prodline_template_management_platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };
  const columnLine = [
    {
      name: 'elementNum',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'status',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'reviewDimension',
      width: 180,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'reviewItem',
      width: 180,
      editor: record => {
        return record.getState('myEditing') && <TextArea />;
      },
    },
    {
      name: 'reviewContent',
      width: 180,
      editor: record => {
        return record.getState('myEditing') && <TextArea />;
      },
    },
    {
      name: 'deliveryName',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'deliveryTemplate',
      width: 120,
      editor: record => record.getState('myEditing') && <Attachment {...attachmentProps} />,
    },
    {
      name: 'deliveryUuid',
      width: 120,
    },
    {
      name: 'responseObj',
      width: 120,
      editor: record => {
        return record.getState('myEditing') && <Lov onChange={() => record?.set('responseUserObj', undefined)} />;
      },
    },
    {
      name: 'responseUserObj',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'scheFinishTime',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    ['COMPLETED', 'EXECUTING'].includes(prodLinePlanStatus) && {
      name: 'applyFlag',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    ['COMPLETED', 'EXECUTING'].includes(prodLinePlanStatus) && {
      name: 'noApplyReason',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    ['COMPLETED', 'EXECUTING'].includes(prodLinePlanStatus) && {
      name: 'autualFinishTime',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    ['COMPLETED', 'EXECUTING'].includes(prodLinePlanStatus) && {
      name: 'reviewResult',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    ['COMPLETED', 'EXECUTING'].includes(prodLinePlanStatus) && {
      name: 'nonConTerm',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    ['COMPLETED', 'EXECUTING'].includes(prodLinePlanStatus) && {
      name: 'releaseClass',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
    ['COMPLETED', 'EXECUTING'].includes(prodLinePlanStatus) && {
      name: 'closeDate',
      width: 120,
      editor: record => {
        return record.getState('myEditing');
      },
    },
  ];
  const menu = () => {
    return (
      <Menu>
        <Menu.Item disabled={tableDetailDs.current?.get('status') !== 'IN_APPROVAL'}>
          <a onClick={() => tableApplicantAgree()}>
            {intl.get(`${modelPrompt}.menuItem.accept`).d('同意')}
          </a>
        </Menu.Item>
        <Menu.Item disabled={tableDetailDs.current?.get('status') !== 'IN_APPROVAL'}>
          <a onClick={() => tableSubmitReject()}>
            {intl.get(`${modelPrompt}.menuItem.reject`).d('驳回')}
          </a>
        </Menu.Item>
      </Menu>
    );
  };
  // 新增行
  const handleAddLine = () => {
    tableDetailLineDs.create(
      {
        _status: 'create',
        status: 'NEW',
        deliveryTemplate: uuid(),
      },
      0,
    );
    tableDetailLineDs.current.setState('myEditing', true);
  };
  // 删除行
  const handleBatchDelete = () => {
    const data = lineSelected.map(ele => ele.get('qisProdlineRevplanEId'));
    tableDetailLineDs.remove(lineSelected);
    setDelIdList(data);
  };
  // 编辑
  const handEdit = () => {
    setDisabledFlag(!disabledFlag);
    setLineBtnFlag(!lineBtnFlag);
    tableDetailLineDs.all.forEach(v => {
      v.setState('myEditing', disabledFlag);
    });
  };
  // 选择模板
  const changeModule = useCallback(e => {
    if (e) {
      queryModule(e.prodlineReviewTmpId);
    }
  }, []);

  const handCancel = () => {
    if (status === 'create' || status === 'module') {
      props.history.push('/hwms/production-line-audit-plan-management-platform');
    }else{
      queryDetail();
    }
    setDisabledFlag(!disabledFlag);
    setLineBtnFlag(!lineBtnFlag);
    tableDetailLineDs.all.forEach(v => {
      v.setState('myEditing', disabledFlag);
    });
  };

  return (
    <div style={{ height: '100%' }} className="hmes-style">
      <Header
        title={
          status === 'create'
            ? intl.get(`${modelPrompt}.title.create`).d('产线审核计划创建')
            : intl.get(`${modelPrompt}.title.detail`).d('产线审核计划详情')
        }
        backPath="/hwms/production-line-audit-plan-management-platform/list"
      >
        <>
          <PermissionButton
            type="c7n-pro"
            icon="save"
            color={ButtonColor.primary}
            permissionList={[
              {
                code: `button.submitSave`,
                type: 'button',
                meaning: '保存并提交',
              },
            ]}
            onClick={() => handSaveSubmit()}
            disabled={saveFlag}
          >
            {intl.get(`${modelPrompt}.button.saveAndSubmit`).d('保存并提交')}
          </PermissionButton>
          <Dropdown overlay={menu()} type="primary">
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              permissionList={[
                {
                  code: `button.applicant`,
                  type: 'button',
                  meaning: '审批按钮',
                },
              ]}
              disabled={tableDetailDs.current?.get('status') !== 'IN_APPROVAL'}
            >
              {intl.get(`${modelPrompt}.button.review`).d('审批')}
            </PermissionButton>
          </Dropdown>
          {!lineBtnFlag && (
            <>
              <PermissionButton
                type="c7n-pro"
                icon="save"
                color={ButtonColor.primary}
                permissionList={[
                  {
                    code: `button.save`,
                    type: 'button',
                    meaning: '保存按钮',
                  },
                ]}
                onClick={() => handSave()}
                disabled={saveFlag}
              >
                {intl.get(`${modelPrompt}.button.save`).d('保存')}
              </PermissionButton>
              <PermissionButton icon="close" onClick={() => handCancel()}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </PermissionButton>
            </>
          )}
          {lineBtnFlag && (
            <>
              <PermissionButton
                color={ButtonColor.primary}
                type="c7n-pro"
                permissionList={[
                  {
                    code: `button.edit`,
                    type: 'button',
                    meaning: '编辑按钮',
                  },
                ]}
                disabled={editFlag}
                onClick={() => handEdit()}
              >
                {intl.get(`${modelPrompt}.button.edit`).d('编辑')}
              </PermissionButton>
            </>
          )}
          <ApprovalInfoDrawer objectTypeList={['QIS_CXSH_LWS']} objectId={id} />
        </>
      </Header>
      <Content>
        <ExpandCardC7n
          showExpandIcon
          title={intl.get(`${modelPrompt}.title.basicInfo`).d('基础信息')}
        >
          <Form dataSet={tableDetailDs} columns={3} labelWidth={110} disabled={disabledFlag} >
            <TextField name="prodlineRevplanCode" />
            <Select name="status" />
            <Lov name="siteObj"/>
            <Lov
              name="prodlineReviewTmpNumObj"
              disabled={status !== 'create' && status !== 'module'}
              onChange={e => changeModule(e)}
            />
            <Select name="reviewType"  />
            <Select name="reviewStage"  />
            <Lov name="prodObj" colSpan={2} />
            <Select name="projectName" />
            {['COMPLETED', 'EXECUTING'].includes(prodLinePlanStatus) && <DatePicker name="closeDate" />}
            <DatePicker name="creationDate" />
            <TextField name="creationByName" />
          </Form>
        </ExpandCardC7n>
        <ExpandCardC7n
          showExpandIcon
          title={intl.get(`${modelPrompt}.title.prodlineReviewElement`).d('产线审核要素')}
        >
          <div>
            <Table
              customizable
              customizedCode="ProductionLineAuditPlanManagementPlatformTableDetailLine"
              dataSet={tableDetailLineDs}
              columns={columnLine}
              buttons={[
                <PermissionButton
                  type="c7n-pro"
                  color={ButtonColor.primary}
                  icon="add"
                  funcType="flat"
                  shape="circle"
                  size="small"
                  disabled={lineBtnFlag}
                  permissionList={[
                    {
                      code: `key.hwms.front.ProductionLineAuditPlanManagementPlatform.button.addLine`,
                      type: 'button',
                      meaning: '新建按钮',
                    },
                  ]}
                  onClick={() => handleAddLine()}
                >
                  {intl.get(`${modelPrompt}.button.create`).d('新建')}
                </PermissionButton>,
                <Popconfirm
                  title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                  onConfirm={() => handleBatchDelete()}
                  okText={intl.get('tarzan.common.button.confirm').d('确认')}
                  cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                >
                  <PermissionButton
                    type="c7n-pro"
                    color={ButtonColor.primary}
                    icon="delete_black-o"
                    funcType="flat"
                    shape="circle"
                    size="small"
                    permissionList={[
                      {
                        code: `key.hwms.front.ProductionLineAuditPlanManagementPlatform.button.deleteLine`,
                        type: 'button',
                        meaning: '删除按钮',
                      },
                    ]}
                    disabled={lineBtnFlag || lineSelected.length < 1}
                  >
                    {intl.get(`${modelPrompt}.button.delete`).d('删除')}
                  </PermissionButton>
                </Popconfirm>,
              ]}
              queryBar={TableQueryBarType.none}
              queryFieldsLimit={3}
              pagination={{ showPager: true }}
              queryBarProps={{ autoQueryAfterReset: false }}
              rowHeight={34}
              border
            />
          </div>
        </ExpandCardC7n>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(ProductionLineAuditPlanManagementPlatformDetail);
