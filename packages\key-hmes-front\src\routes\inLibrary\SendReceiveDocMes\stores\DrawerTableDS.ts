/**
 * @Description: 库存调拨平台 - 抽屉DS
 * @Author: <EMAIL>
 * @Date: 2022/3/8 10:14
 * @LastEditTime: 2023-05-18 16:33:39
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.inLibrary.sendReceiveDocMes';
const tenantId = getCurrentOrganizationId();

const drawerTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantityStatus`).d('质量状态'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('条码状态'),
    },
    {
      name: 'container',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerIdentification`).d('所在容器'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'sendLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sendLocatorCode`).d('发出货位'),
    },
    {
      name: 'sendDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromDate`).d('发出时间'),
    },
    {
      name: 'sendRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromPerson`).d('发出人'),
    },
    {
      name: 'receiveLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiceLocatorCode`).d('接收货位'),
    },
    {
      name: 'receiveDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toDate`).d('接收时间'),
    },
    {
      name: 'receivePerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toPerson`).d('接收人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-send-receive/material-lot/detail/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
        method: 'GET',
      };
    },
  },
});

const inventoryDrawerTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentification`).d('物料批标识'),
    },
    {
      name: 'containerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerIdentificationFlag`).d('容器标识'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantityStatus`).d('质量状态'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('条码状态'),
    },
    {
      name: 'container',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerIdentification`).d('所在容器'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'sendLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sendLocatorCode`).d('发出货位'),
    },
    {
      name: 'sendDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromDate`).d('发出时间'),
    },
    {
      name: 'sendRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromPerson`).d('发出人'),
    },
    {
      name: 'receiveLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiceLocatorCode`).d('接收货位'),
    },
    {
      name: 'receiveDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toDate`).d('接收时间'),
    },
    {
      name: 'receivePerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toPerson`).d('接收人'),
    },
    {
      name: 'targetMaterialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotAppoint`).d('指定物料批'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-send-receive/material-manage/detail/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
        method: 'GET',
      };
    },
  },
});

export { drawerTableDS, inventoryDrawerTableDS };
