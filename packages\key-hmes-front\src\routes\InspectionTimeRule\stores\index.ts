import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'InspectionTimeRule';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lookupCode: 'APEX_QMS.TIME_RULE.INSPECT_BUS_TYPE_DOC_CREATE',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        // workcellType: 'PROCESS',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'processWorkcellId',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'creationByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.creationByName`).d('创建人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'creationByLov.id',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
  ],
  fields: [
    {
      name: 'orderNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.orderNumber`).d('序号'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lookupCode: 'APEX_QMS.TIME_RULE.INSPECT_BUS_TYPE_DOC_CREATE',
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次'),
      lookupCode: 'APEX.QMS.SHIFT_CODE',
      dynamicProps: {
        required: ({ record }) => ['IPQC-F', 'IPQC-L'].includes(record.get('inspectBusinessType')),
        disabled: ({ record }) => !['IPQC-F', 'IPQC-L'].includes(record.get('inspectBusinessType')),
      },
    },
    {
      name: 'reminderLevel',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.reminderLevel`).d('提醒等级'),
      lookupCode: 'APEX_QMS.REMINDER_LEVEL',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record }) => ['IPQC-F', 'IPQC-L'].includes(record.get('inspectBusinessType')),
        disabled: ({ record }) => !['IPQC-F', 'IPQC-L'].includes(record.get('inspectBusinessType')),
      },
    },
    {
      name: 'processWorkcellId',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'processWorkcellName',
      bind: 'processWorkcellLov.workcellName',
    },
    {
      name: 'provingTime',
      type: FieldType.time,
      label: intl.get(`${modelPrompt}.provingTime`).d('规定检验时间'),
      range: ['setInspectTimeBegin', 'setInspectTimeEnd'],
      validator: (value, name, record) => {
        if (
          ['IPQC-F', 'IPQC-L'].includes(record?.get('inspectBusinessType')) &&
          (!value.setInspectTimeBegin || !value.setInspectTimeEnd)
        ) {
          return '请选择规定检验时间';
        }
        return true;
      },
      dynamicProps: {
        required: ({ record }) => ['IPQC-F', 'IPQC-L'].includes(record.get('inspectBusinessType')),
        disabled: ({ record }) => !['IPQC-F', 'IPQC-L'].includes(record.get('inspectBusinessType')),
      },
    },
    {
      name: 'firstReminderInterval',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.firstReminderInterval`).d('初次提醒间隔（分钟）'),
      step: 1,
      min: 0,
      dynamicProps: {
        required: ({ record }) => Number(record.get('reminderLevel')) >= 1,
        disabled: ({ record }) => Number(record.get('reminderLevel')) < 1,
      },
    },
    {
      name: 'firstReminderByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.firstReminderByName`).d('初次提醒人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
      multiple: ',',
      dynamicProps: {
        required: ({ record }) => Number(record.get('reminderLevel')) >= 1,
        disabled: ({ record }) => Number(record.get('reminderLevel')) < 1,
      },
    },
    {
      name: 'firstReminderBy',
      multiple: ',',
      bind: 'firstReminderByLov.id',
    },
    {
      name: 'firstReminderByNames',
      bind: 'firstReminderByLov.realName',
    },
    {
      name: 'secondReminderInterval',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.secondReminderInterval`).d('二次提醒间隔（分钟）'),
      step: 1,
      min: 0,
      dynamicProps: {
        required: ({ record }) => Number(record.get('reminderLevel')) >= 2,
        disabled: ({ record }) => Number(record.get('reminderLevel')) < 2,
      },
    },
    {
      name: 'secondReminderByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.secondReminderByName`).d('二次提醒人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
      multiple: ',',
      dynamicProps: {
        required: ({ record }) => Number(record.get('reminderLevel')) >= 2,
        disabled: ({ record }) => Number(record.get('reminderLevel')) < 2,
      },
    },
    {
      name: 'secondReminderBy',
      multiple: ',',
      bind: 'secondReminderByLov.id',
    },
    {
      name: 'secondReminderByNames',
      bind: 'secondReminderByLov.realName',
    },
    {
      name: 'thirdReminderInterval',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.thirdReminderInterval`).d('三次提醒间隔（分钟）'),
      step: 1,
      min: 0,
      dynamicProps: {
        required: ({ record }) => Number(record.get('reminderLevel')) >= 3,
        disabled: ({ record }) => Number(record.get('reminderLevel')) < 3,
      },
    },
    {
      name: 'threeReminderByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.threeReminderByName`).d('三次提醒人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      textField: 'realName',
      lovPara: { tenantId },
      multiple: ',',
      dynamicProps: {
        required: ({ record }) => Number(record.get('reminderLevel')) >= 3,
        disabled: ({ record }) => Number(record.get('reminderLevel')) < 3,
      },
    },
    {
      name: 'thirdReminderByNames',
      bind: 'threeReminderByLov.realName',
    },
    {
      name: 'thirdReminderBy',
      multiple: ',',
      bind: 'threeReminderByLov.id',
    },
    {
      name: 'creatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creatorName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      defaultValue: new Date(),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-inspect-time-rule/page/ui`,
        method: 'POST',
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          if (rows.content?.length) {
            rows.content.forEach(item => {
              item.provingTime = {
                setInspectTimeBegin: item.setInspectTimeBegin,
                setInspectTimeEnd: item.setInspectTimeEnd,
              };
            });
          }
          return rows.content;
        },
      };
    },
  },
});

export { tableDS };
