import React, { FC, useEffect, useState } from 'react';
import { RouteComponentProps } from 'react-router';
import { Button as PermissionButton } from 'components/Permission';
import {
  Attachment,
  Button,
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  Modal,
  Select,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import Axios from 'axios';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import notification from 'utils/notification';
import { Collapse } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import listPageFactory from '../stores/listPageFactory';
import modalFactory from '../stores/modalFactory';

const { Panel } = Collapse;

interface RaiseHandReportDetailProps extends RouteComponentProps {
  listDs: DataSet;
  modalDs: DataSet;
}

const modelPrompt = 'tarzan.qms.raiseHandReport';

const RaiseHandReportDetail: FC<RaiseHandReportDetailProps> = ({
  listDs,
  modalDs,
  history,
  match: { params },
}) => {
  const [editFlag, setEditFlag] = useState(false);
  const [disabledFlag, setDisabledFlag] = useState(true);
  const [reviewFlag, setReviewFlag] = useState(true);

  useEffect(() => {
    if ((params as any).id === 'create') {
      listDs.create({}, 0);
      setEditFlag(true);
    } else {
      listDs.setQueryParameter('problemApplyCode', (params as any).id);
      listDs.query().then(res => {
        if (res && res.success) {
          setDisabledFlag(res.rows.content[0].problemApplyStatus !== 'NEW');
          setReviewFlag(res.rows.content[0].problemApplyStatus !== 'REVIEWING');
        }
      });
      setEditFlag(false);
    }
  }, [params]);

  const handleCancel = () => {
    setEditFlag(false);
    listDs.current!.reset();
  };

  const handleSave = async () => {
    const flag = await listDs.current!.validate(true);
    if (flag) {
      const url =
        (params as any).id === 'create'
          ? 'qis-problem-apply/save/ui'
          : 'qis-problem-apply/update/ui';
      const res: any = await Axios.post(
        `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/${url}`,
        {
          ...listDs.current!.toJSONData(),
        },
      );
      if (res && res.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.message.success`).d('操作成功'),
          description: '',
        });
        setEditFlag(false);
        history.replace(`/hwms/raise-hand-report/detail/${res.rows?.problemApplyCode}`);
      } else {
        notification.error({
          message: res.message || intl.get(`${modelPrompt}.message.error`).d('操作失败'),
          description: '',
        });
      }
    }
  };

  const handleSubmit = () => {
    Axios.post(
      `${BASIC.TARZAN_SAMPLING
      }/v1/${getCurrentOrganizationId()}/qis-problem-apply/submit/ui?problemApplyId=${listDs.current!.get(
        `problemApplyId`,
      )}`,
    )
      .then((res: any) => {
        if (res && res.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.message.success`).d('操作成功'),
            description: '',
          });
          listDs.setQueryParameter('problemApplyCode', (params as any).id);
          listDs.query().then(res => {
            if (res && res.success) {
              setDisabledFlag(res.rows.content[0].problemApplyStatus !== 'NEW');
              setReviewFlag(res.rows.content[0].problemApplyStatus !== 'REVIEWING');
            }
          });
        }
      })
      .catch((res: any) => {
        notification.error({
          message: res.message || intl.get(`${modelPrompt}.message.error`).d('操作失败'),
          description: '',
        });
      });
  };

  const review = async () => {
    await Axios.post(
      `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-problem-apply/approve/ui`,
      {
        problemApplyId: listDs.current!.get(`problemApplyId`),
        reviewBy: getCurrentUserId(),
        ...modalDs.current!.toJSONData(),
      },
    )
      .then((res: any) => {
        if (res && res.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.message.success`).d('操作成功'),
            description: '',
          });
          listDs.setQueryParameter('problemApplyCode', (params as any).id);
          listDs.query().then(res => {
            if (res && res.success) {
              setDisabledFlag(res.rows.content[0].problemApplyStatus !== 'NEW');
              setReviewFlag(res.rows.content[0].problemApplyStatus !== 'REVIEWING');
            }
          });
        }
      })
      .catch((res: any) => {
        notification.error({
          message: res.message || intl.get(`${modelPrompt}.message.error`).d('操作失败'),
          description: '',
        });
      });
  };

  const handleChangeSite = () => {
    listDs.current?.set('prodLine', undefined);
    listDs.current?.set('operation', undefined);
  };

  const handleExamination = async () => {
    const handleReviewPass = async () => {
      modalDs.current!.set('reviewResult', 'PASS');
      await review();
      modal.close();
      modalDs.reset();
    };

    const handleReviewCancel = async () => {
      modalDs.current!.set('reviewResult', 'REJECT');
      await review();
      modal.close();
      modalDs.reset();
    };
    const modal = Modal.open({
      title: intl.get(`${modelPrompt}.modal.title`).d('审批意见'),
      children: (
        <>
          <Form dataSet={modalDs}>
            <TextArea
              name="reviewSuggestion"
              placeholder={intl
                .get(`${modelPrompt}.place.input.reviewSuggestion`)
                .d('请录入审批意见...')}
            />
          </Form>
        </>
      ),
      destroyOnClose: true,
      closable: true,
      footer: (
        <div style={{ display: 'flex', justifyContent: 'space-evenly', width: '100%' }}>
          <Button onClick={handleReviewCancel}>
            {intl.get(`${modelPrompt}.cancel`).d('审批拒绝')}
          </Button>
          <Button color={ButtonColor.primary} onClick={handleReviewPass}>
            {intl.get(`${modelPrompt}.ok`).d('审批通过')}
          </Button>
        </div>
      ),
    });
  };

  const headerButton =
    (params as any).id === 'create'
      ? [
        <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
          {intl.get('hzero.common.button.save').d('保存')}
        </Button>,
      ]
      : [
        editFlag ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
              {intl.get('hzero.common.button.save').d('保存')}
            </Button>
            <Button icon="cancel" onClick={handleCancel}>
              {intl.get('hzero.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <Button
            color={ButtonColor.primary}
            disabled={disabledFlag}
            icon="edit"
            onClick={() => setEditFlag(true)}
          >
            {intl.get('hzero.common.button.edit').d('编辑')}
          </Button>
        ),
        <Button onClick={handleSubmit} disabled={disabledFlag}>
          {intl.get('hzero.common.button.submit').d('提交')}
        </Button>,
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          disabled={reviewFlag}
          permissionList={[
            {
              code: `tarzan.qms.RaiseHandReportDetail.button.examination`,
              type: 'button',
              meaning: '审批',
            },
          ]}
          onClick={handleExamination}
        >
          {intl.get(`${modelPrompt}.header.button.examination`).d('审批')}
        </PermissionButton>,
      ];

  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    readOnly: !editFlag,
  };

  return (
    // 替换产品类名
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.raiseHandReportDetail`).d('举手申请详情')}
        backPath="/hwms/raise-hand-report/list"
      >
        {headerButton}
      </Header>
      <Content>
        <Collapse defaultActiveKey={['1', '2', '3']}>
          <Panel key="1" header={intl.get(`${modelPrompt}.panel.problem.title`).d('问题描述')}>
            <Form dataSet={listDs} columns={3} labelWidth={121}>
              <TextField name="problemApplyCode" disabled />
              <Select name="problemApplyStatus" disabled />
              <Lov name="siteObj" disabled={!editFlag} onChange={handleChangeSite} />
              <Lov
                name="apply"
                label={intl.get(`${modelPrompt}.form.apply`).d('申请人')}
                disabled={!editFlag}
              />
              <TextField name="applyByDept" disabled />
              <DateTimePicker name="creationDate" disabled />
              <Lov name="prodLine" disabled={!editFlag} />
              <Lov name="operation" disabled={!editFlag} />
              <Lov name="equipment" disabled={!editFlag} />
            </Form>
            <Form dataSet={listDs} labelWidth={121}>
              <TextField
                label={intl.get(`${modelPrompt}.problemTitle`).d('问题/隐患标题')}
                name="problemTitle"
                disabled={!editFlag}
              />
              <TextArea name="problemDetail" disabled={!editFlag} />
              <Attachment {...attachmentProps}/>,
            </Form>
          </Panel>
          <Panel key="2" header={intl.get(`${modelPrompt}.panel.measure`).d('建议措施')}>
            <Form dataSet={listDs} labelWidth={121}>
              <TextArea name="measure" disabled={!editFlag} />
            </Form>
          </Panel>
          <Panel key="3" header={intl.get(`${modelPrompt}.panel.reviewResult`).d('评审结果')}>
            <Form dataSet={listDs} columns={3} labelWidth={121}>
              <TextField name="reviewResult" disabled />
              <TextField
                name="reviewByName"
                label={intl.get(`${modelPrompt}.form.reviewByName`).d('审批人')}
                disabled
              />
              <DateTimePicker name="reviewTime" disabled />
            </Form>
            <Form dataSet={listDs} labelWidth={121}>
              <TextArea name="reviewSuggestion" disabled />
            </Form>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.qms.raiseHandReport', 'tarzan.common','hzero.common'],
})(
  withProps(
    () => {
      const listDs = listPageFactory();
      const modalDs = modalFactory();
      return {
        listDs,
        modalDs,
      };
    },
    { cacheState: true },
  )(RaiseHandReportDetail),
);
