/**
 * @Description: 检验项目维护-详情页DS
 * @Author: <EMAIL>
 * @Date: 2023/1/9 16:37
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.inspectItemMaintain';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectItemId',
  queryFields: [
    {
      name: 'inspectItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemCode`).d('检验项目编码'),
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemDesc`).d('检验项目描述'),
    },
    {
      name: 'inspectItemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemType`).d('检验项目类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSPECT_ITEM_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'inspectBasis',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBasis`).d('检验依据'),
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
      lookupCode: 'MT.QMS.QUALITY_CHARACTERISTIC_TYPE',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'inspectTool',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTool`).d('检验工具'),
      lookupCode: 'MT.QMS.INSPECT_TOOL',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'inspectMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('检验方法'),
      lookupCode: 'MT.QMS.INSPECT_METHOD',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
    {
      name: 'enterMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterMethod`).d('录入方式'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_COLLECTION_METHOD`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'dataType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataType`).d('数据类型'),
      lookupCode: 'MT.QMS.INSPECT_ITEM_DATA_TYPE',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'samplingMethodLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
      lovCode: 'MT.QMS.SAMPLING_METHOD',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'samplingMethodId',
      type: FieldType.number,
      bind: 'samplingMethodLov.samplingMethodId',
    },
    {
      name: 'sameGroupIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sameGroupIdentification`).d('同组标识'),
    },
    {
      name: 'outsourceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsourceFlag`).d('委外检验标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'destructiveExperimentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.destructiveExperimentFlag`).d('破坏性实验标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      name: 'inspectItemId',
      type: FieldType.number,
    },
    {
      name: 'inspectItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemCode`).d('检验项目编码'),
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemDesc`).d('检验项目描述'),
    },
    {
      name: 'inspectItemTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemType`).d('检验项目类型'),
    },
    {
      name: 'inspectBasis',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBasis`).d('检验依据'),
    },
    {
      name: 'qualityCharacteristicDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'inspectToolDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTool`).d('检验工具'),
    },
    {
      name: 'inspectMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('检验方法'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
    {
      name: 'enterMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterMethod`).d('录入方式'),
    },
    {
      name: 'dataTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataType`).d('数据类型'),
    },
    {
      name: 'inspectMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('检验方法'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.decimalNumber`).d('小数位数'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosureList`).d('附件'),
      bucketName: 'qms',
      bucketDirectory: 'inspect-item-maintain',
    },
    {
      name: 'processModeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processMode`).d('尾数处理'),
    },
    {
      name: 'valueLists',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
    },
    {
      name: 'trueValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
    },
    {
      name: 'falseValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
    },
    {
      name: 'warningValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warningValue`).d('预警值'),
    },
    {
      name: 'samplingMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
    },
    {
      name: 'sameGroupIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sameGroupIdentification`).d('同组标识'),
    },
    {
      name: 'outsourceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsourceFlag`).d('委外检验标识'),
    },
    {
      name: 'destructiveExperimentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.destructiveExperimentFlag`).d('破坏性实验标识'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-item/list/ui`,
        method: 'GET',
      };
    },
  },
});

const historyDS: () => DataSetProps = () => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  selection: false,
  primaryKey: 'inspectItemId',
  fields: [
    {
      name: 'operationTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationType`).d('操作类型'),
    },
    {
      name: 'inspectItemId',
      type: FieldType.number,
    },
    {
      name: 'inspectItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemCode`).d('检验项目编码'),
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemDesc`).d('检验项目描述'),
    },
    {
      name: 'inspectItemTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectItemType`).d('检验项目类型'),
    },
    {
      name: 'inspectBasis',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBasis`).d('检验依据'),
    },
    {
      name: 'qualityCharacteristicDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'inspectToolDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTool`).d('检验工具'),
    },
    {
      name: 'inspectMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('检验方法'),
    },
    {
      name: 'requiredFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requiredFlag`).d('必填项目'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
    {
      name: 'technicalRequirement',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.technicalRequirement`).d('技术要求'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'enterMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterMethod`).d('录入方式'),
    },
    {
      name: 'dataTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataType`).d('数据类型'),
    },
    {
      name: 'inspectMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectMethod`).d('检验方法'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.decimalNumber`).d('小数位数'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosureList`).d('附件'),
      bucketName: 'qms',
      bucketDirectory: 'inspect-item-maintain',
    },
    {
      name: 'processModeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processMode`).d('尾数处理'),
    },
    {
      name: 'valueLists',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
    },
    {
      name: 'trueValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
    },
    {
      name: 'falseValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
    },
    {
      name: 'warningValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warningValue`).d('预警值'),
    },
    {
      name: 'dataQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.dataQty`).d('记录值个数'),
    },
    {
      name: 'samplingMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
    },
    {
      name: 'ncCodeGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeGroup`).d('不良代码组'),
    },
    {
      name: 'employeePosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.employeePosition`).d('检测人员岗位'),
    },
    {
      name: 'inspectFrequency',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectFrequency`).d('检测频率'),
    },
    {
      // 频率参数M值
      name: 'm',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.m`).d('频率参数M'),
    },
    {
      // 频率参数N值
      name: 'n',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.n`).d('频率参数N'),
    },
    {
      name: 'actionItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actionItem`).d('行动项'),
    },
    {
      name: 'sameGroupIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sameGroupIdentification`).d('同组标识'),
    },
    {
      name: 'outsourceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsourceFlag`).d('委外检验标识'),
    },
    {
      name: 'destructiveExperimentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.destructiveExperimentFlag`).d('破坏性实验标识'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-item/detail/his/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_LIST.HISTORY`,
        method: 'GET',
      };
    },
  },
});

export { tableDS, historyDS };
