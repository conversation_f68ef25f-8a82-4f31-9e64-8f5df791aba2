.container {
  height: 100%;
  padding: 0 12px;

  .table-auto-height {
    height: 100%;

    :global {
      .c7n-collapse-item {
        height: 100%;

        .c7n-collapse-content {
          height: calc(100% - 12px - 22px);

          .c7n-collapse-content-box {
            height: 100%;

            .c7n-tabs-content {
              height: calc(100% - 30px);

              .c7n-tabs-tabpane {
                height: 100%;
              }
            }
          }
        }
      }
    }
  }
}

.customize-collapse {
  height: 100%;

  :global(.c7n-tabs-bar) {
    height: 30px;
    margin: 0;
  }

  :global(.c7n-tabs-nav-container) {
    height: 30px;
  }

  :global(.c7n-tabs-tab) {
    margin: 0 24px 0 0 !important;
    padding: 0 !important;
  }

  :global(.c7n-collapse-header) {
    padding: 12px 0 0 8px !important;
    border-bottom: 0 !important;

    &::before {
      top: calc(50% - 0.07rem + 6px) !important;
    }
  }
}
