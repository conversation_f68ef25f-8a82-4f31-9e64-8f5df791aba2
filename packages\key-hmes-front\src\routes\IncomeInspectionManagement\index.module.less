.center-loading {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
}

.screen-container {
  width: 100%;
  height: 100%;

  :global {
    .page-content-wrap {
      height: 100%;
      margin: 0;
    }

    .page-content {
      height: 100%;
      margin: 0;
    }
  }
}

.dashboard-container {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
  // padding: 20px 36px;
  overflow: hidden;
  color: #fff;
  background: url('./assets/bg.png') center center no-repeat;
  background-position: top;
  background-size: cover;
}

.dashboard-title {
  display: flex;
  width: 100%;
  height: 80px;
  height: 10vh;
  min-height: 80px;
  .dashboard-title-left {
    // display: flex;
    display: grid;
    // flex-grow: 0;
    // flex-shrink: 0;
    // align-items: flex-end;
    // justify-content: center;
    width: 30%;
    height: 10%;
  }

  .dashboard-title-center {
    display: flex;
    flex-grow: 0;
    align-items: center;
    justify-content: center;
    width: 40%;
    height: 70%;
    color: #65ffff;
    font-weight: bold;
    font-size: 32px;
    letter-spacing: 0.1em;
  }

  .dashboard-title-right {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    align-items: flex-end;
    justify-content: right;
    width: 30%;
    margin: 38px 10px;
    :global {
      .c7n-pro-select-wrapper.c7n-pro-select-wrapper {
        background-color: #040563;
        align-self: center;
      }
      .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-select {
        height: 36px;
      }
      .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-select-suffix .icon {
        color: #fff;
      }
      .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-suffix-button .c7n-pro-select-suffix .icon {
        font-size: 18px;
      }
      .c7n-pro-select-wrapper.c7n-pro-select-wrapper label input {
        color: #fff;
      }
    }
  }
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-wrap: wrap;
  width: 100%;
  height: calc(100% - 10vh);

  .dashboard-col-side {
    width: 30%;
    height: 100%;
  }

  .dashboard-col-center {
    width: 38%;
    height: 100%;
    // margin: 0 1% 0;
    // padding: 2%;
    // background-image: url('./assets/center-col-bg.png');
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
  }

  .dashboard-item-left-top {
    width: 100%;
    height: 40%;
    background-image: url('./assets/left.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .dashboard-item-left-bottom {
    width: 100%;
    height: 60%;
    margin: 1% 0;
    background-image: url('./assets/task.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .dashboard-item-center-top {
    width: 100%;
    height: 40%;
  }
  .dashboard-item-center-bottom {
    width: 100%;
    height: 60%;
    margin: 1% 0;
    background-image: url('./assets/centerBottom.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .dashboard-item {
    width: 100%;
    height: 50%;
  }
}

.dashboard-card {
  width: 100%;
  height: 100%;
  padding: 5px;
  overflow: hidden;
  // border: 1px solid red;
  .dashboard-card-title {
    width: 100%;
  }

  .dashboard-card-content {
    width: 100%;
    height: 100%;
  }
}

.top-select {
  width: 300px;
  height: 45px !important;

  :global {
    .c7n-pro-select-wrapper {
      width: 200px;
      height: 40px !important;
      background: transparent !important;
      background: linear-gradient(to right, #01427c, #000e27) !important;
      border: 1px solid #6ab5f8 !important;
    }

    .c7n-pro-select {
      height: 40px !important;
      color: #33c5ff !important;
      font-weight: 600 !important;
      // font-size: 18px !important;
      line-height: 40px !important;
      outline: none !important;
    }

    .c7n-pro-select:focus {
      border: none !important;
      box-shadow: none !important;
    }

    .c7n-pro-select:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .c7n-pro-select-multiple-block {
      background: #154ea0;
    }

    input::-webkit-input-placeholder {
      padding-left: 10px !important;
      /* placeholder颜色 */
      color: #33c5ff !important;
      font-weight: 600 !important;
      /* placeholder字体大小 */
      font-size: 18px !important;
      line-height: 40px !important;
    }
  }
}

.rank-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  padding: 10px 10px 20px;

  .rank-item {
    display: flex;
    align-items: center;
    width: 50%;
    height: 20%;
    padding-right: 25px;

    .rank-item-sort {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 20px;
      color: #fff;
      font-size: 12px;
      background: #4c6277;
      border-radius: 50%;
    }

    .rank-item-name {
      flex-shrink: 0;
      width: 100px;
      padding: 0 4px 0;
      color: #fff;
      font-size: 14px;
    }

    .rank-item-progress {
      padding: 0 4px 0;
    }

    :global {
      .c7n-progress {
        padding: 3px !important;
        background: #264160 !important;
        border-radius: 50px !important;
      }

      .c7n-progress-inner {
        background: #264160 !important;

        .c7n-progress-bg {
          height: 8px !important;
        }
      }

      .c7n-progress-outer {
        display: flex;
        align-items: center;
      }

      .c7n-progress-line {
        font-size: 12px !important;
      }
    }

    .rank-item-quantity {
      flex-shrink: 0;
      width: 50px;
      padding: 0 5px 0;
      text-align: right;
    }

    .rank-item-percent {
      flex-shrink: 0;
      width: 30px;
      padding: 0 5px 0;
      text-align: right;
    }
  }
}

.sumary-list {
  display: flex;
  justify-content: space-around;
  height: 100px;

  .sumary-item-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #a6a6a6;
    font-weight: 600;
    font-size: 18px;
    background: url('./assets/summary1.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }

  .sumary-item-2 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #47a5f2;
    font-weight: 600;
    font-size: 18px;
    background: url('./assets/summary2-new.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }

  .sumary-item-3 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #f3be58;
    font-weight: 600;
    font-size: 18px;
    background: url('./assets/summary3-new.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }

  .sumary-item-4 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #03f8f9;
    font-weight: 600;
    font-size: 18px;
    background: url('./assets/summary4-new.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }
}

.my-scroll-board-2 {
  :global {
    .dv-scroll-board {
      .header {
        .header-item {
          height: 2.5vw !important;
          font-weight: bold !important;
          font-size: 12px !important;
          line-height: 2.5vw !important;
        }
      }

      .rows {
        height: auto !important;
        .row-item {
          font-size: 12px !important;
          // border: 1px solid #1e3e67;
        }
      }
    }
  }
}
.my-scroll-board-title{
    width: 100%;
    height: 7%;
    opacity: 1;
    /** 文本1 */
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 28.96px;
    color: rgba(0, 255, 244, 1);
    text-align: center;
    vertical-align: top;
    margin-top: 1%;
}
.my-scroll-board-table {
  :global {
    .dv-scroll-board .header .header-item:nth-child(1) {
      width: 90px;
    }
    .dv-scroll-board .rows .ceil:nth-child(1) {
      width: 90px;
    }
    .dv-scroll-board {
      .rows {
        height: auto !important;
        .row-item {
          font-size: 14px !important;
          height: 45px !important;
          line-height: 45px !important;
        }
      }
    }

    .dv-scroll-board .rows .ceil {
      padding: 0 5px !important;
    }
  }
}

.checkSupplierText {
  display: block;
  margin-bottom: 20px; /* 留出足够的空间供背景图片显示 */
}

.checkSupplierImage {
  display: inline-block;
  width: 100px; /* 或者你想要的任何宽度 */
  height: 100px; /* 或者你想要的任何高度 */
  background-image: url('./assets/1.png');
  background-size: cover; /* 背景图片覆盖整个span区域 */
  background-position: center; /* 图片居中显示 */
  background-repeat: no-repeat; /* 不重复背景图片 */
}

.dashboard-right-chart{
  margin: 2%;
  border-radius: 50%; /* 圆形结构 */
  background-image: url('./assets/cicle.png');
  background-position: 50% 10%; /* 背景图片居中 */
  background-repeat: no-repeat; /* 不重复背景图片 */
  background-size: 59% 87%;
}

.dashboard-right-chart-full-screen{
  // margin:2%;
  border-radius: 50%; /* 圆形结构 */
  background-image: url('./assets/cicle.png');
  // background-size: 70% 90%; /* 背景图片覆盖整个元素 */
  // background-repeat: no-repeat; /* 不重复背景图片 */
  // background-position: 50% 100%; /* 背景图片居中 */

  background-size: 61% 80%;
  background-repeat: no-repeat;
  background-position: 50% 25%;
}

.my-ng-chart-filter,
.my-chart-filter{
  width: 100%;
  // height: 7%;
  opacity: 1;
  font-size: 12px;
  letter-spacing: 0px;
  vertical-align: top;
  margin-top: 1%;
  margin-left: 15px;
  :global {
    .c7n-pro-select-lov .c7n-pro-select-suffix .icon.icon-search::before {
      color: #fff;
    }
  }
}

.my-ng-chart-filter {
  margin-bottom: 15px;
}

.my-chart{
  width: 100%;
  height: 100%;
}



.container-inventory-select {
  display: flex;
  // top: 1.5px;
  // right: 10px;
  cursor: pointer;

  :global {
    .c7n-pro-calendar-picker-wrapper {
      background: transparent !important;
    }
    .c7n-pro-calendar-picker-suffix .icon-date_range:before {
      color: #33c5ff !important;
    }
    .c7n-pro-calendar-picker {
      color: #33c5ff !important;
      width: 100px !important;
      height: 35px !important;
    }
    .c7n-pro-select-wrapper {
      width: 100px !important;
      height: 35px !important;
      background: transparent !important;
      border: none !important;
      cursor: pointer !important;
    }

    .c7n-pro-select {
      height: 35px !important;
      color: #33c5ff !important;
      font-weight: 600 !important;
      // font-size: 18px !important;
      line-height: 35px !important;
      //outline: none !important;
    }

    .c7n-pro-select-suffix .icon-baseline-arrow_drop_down:before {
      color: #33c5ff !important;
    }

    .c7n-pro-select-multiple-block {
      background: #154ea0;
    }

    .c7n-pro-select-placeholder {
      color: #3ac2fc !important;
    }

    input::-webkit-input-placeholder {
      padding-left: 10px !important;
      /* placeholder颜色 */
      color: #33c5ff !important;
      font-weight: 600 !important;
      /* placeholder字体大小 */
      font-size: 18px !important;
      line-height: 35px !important;
    }
  }
}

.my-ng-chart-filter {
  margin-bottom: 15px;
  .container-inventory-select {
    :global {
      .c7n-pro-select-wrapper {
        width: 90px !important;
        height: 35px !important;
        background: transparent !important;
        border: none !important;
        cursor: pointer !important;
      }
    }
  }
}
