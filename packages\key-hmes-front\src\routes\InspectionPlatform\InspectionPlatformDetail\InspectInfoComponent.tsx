/**
 * @Description: 检验平台-检验项目信息
 * @Author: <<EMAIL>>
 * @Date: 2023-02-14 10:01:22
 * @LastEditTime: 2023-05-23 18:13:59
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import intl from 'utils/intl';
import {
  Attachment,
  Form,
  NumberField,
  Output,
  SelectBox,
  Table,
  TextField,
  Select,
  Lov,
} from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { LabelAlign, LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import InspectItemRowComponent from './InspectItemRowComponent';
import InspectItemRowObjComponent from './InspectItemRowObjComponent';
import PartDisposalComponent from './PartDisposalComponent';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.qms.inspectionPlatform';
const { ItemGroup, Item } = Form;

const InspectInfoComponent = props => {
  const {
    setMinOkQtyFlag,
    canEdit,
    ModeType,
    NcRecordDimension,
    path,
    inspectInfoDS,
    inspectItemRowDS,
    inspectItemRowObjValueDS,
    inspectItemColDS,
    inspectLovDS,
    disposalModeDS,
    customizeTable,
    customizeForm,
    itemColColumns,
    colShowFlag,
    minOkQtyFlag,
    cacheMinWidth,
    disposalType,
    disposalTypeDesc,
    disposalMode,
    setCacheMinWidth,
    onAutoEnterDefaultValue,
    setDisposalType,
    setDisposalMode,
    handleItemAddInspectObj,
    handleChangeValueColor,
    handleComputedQty,
    handleChangeInspectItem,
    handleItemScanInspectObj,
    handleChangeNgQty,
    handleBatchChangeData,
    disposalStatus,
    inspectObjDS,
    inspectResultNow,
    showNcReviewer,
  } = props;

  // 处置结论变更
  const handleChangeDisposalType = (value, oldValue) => {
    if (value !== oldValue) {
      setDisposalType(value);
      if (oldValue === 'PART') {
        inspectInfoDS.setState('partDisposalFlag', 'Y');
      }
    }
  };

  // 处置方法修改时更新处置方法编码字段
  const handleChangeDisposalMode = (value, oldValue) => {
    if (value !== oldValue) {
      let _dispositionFunction: any = null;
      if (value) {
        const _dispositionRecord = disposalModeDS.find(
          record => record.get('dispositionFunctionId') === value,
        );
        _dispositionFunction = _dispositionRecord?.get('dispositionFunction');
      }
      setDisposalMode(_dispositionFunction);
      inspectInfoDS.current.set('curDispFunction', _dispositionFunction);
    }
  };

  // 渲染符合值不符合值预警值
  const onRenderValueList = (value, inspectItemKey) => {
    return (value || []).map(item => (
      <Tag
        color={
          inspectItemKey === 'trueValues'
            ? 'green'
            : inspectItemKey === 'falseValues'
              ? 'red'
              : 'yellow'
        }
      >
        {item}
      </Tag>
    ));
  };

  const disposalTypeStatus = () => {
    if (inspectObjDS.length === 0) {
      return true;
    }
    let _status = true;
    inspectObjDS.forEach(record => {
      if (record.get('inspectFlag') === 'Y') {
        _status = false;
      }
    });
    return _status;
  };

  // 行模式
  const inspectItemRowProps = {
    NcRecordDimension,
    inspectInfoDS,
    inspectItemRowDS,
    customizeTable,
    cacheMinWidth,
    setCacheMinWidth,
    onRenderValueList,
    onAutoEnterDefaultValue,
    handleComputedQty,
    handleChangeValueColor,
    handleChangeNgQty,
    handleBatchChangeData,
  };
  // 行模式带对象
  const inspectItemRowObjProps = {
    NcRecordDimension,
    path,
    canEdit,
    inspectInfoDS,
    inspectItemRowDS,
    inspectItemRowObjValueDS,
    inspectLovDS,
    customizeTable,
    cacheMinWidth,
    setCacheMinWidth,
    onRenderValueList,
    handleChangeValueColor,
    handleComputedQty,
    handleChangeInspectItem,
    handleItemScanInspectObj,
    handleChangeNgQty,
    handleBatchChangeData,
  };
  // 部分处置
  const partDisposalProps = {
    inspectInfoDS,
    disposalTypeDesc,
    disposalStatus,
  };

  const attachmentProps1: any = {
    name: 'enclosure',
    labelLayout: 'float',
    bucketName: 'qms',
    bucketDirectory: 'inspection-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };
  const attachmentProps2: any = {
    name: 'programmeEnclosure',
    labelLayout: 'float',
    bucketName: 'qms',
    bucketDirectory: 'inspection-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    readOnly: true,
  };

  const handleOkQtyChange = () => {
    // 校验任务合格数是否小于0
    setMinOkQtyFlag(Number(inspectInfoDS.current?.get('okQty') || 0) < 0);
    if (Number(inspectInfoDS.current?.get('okQty') || 0) < 0) {
      setMinOkQtyFlag(true);
      notification.error({
        message: intl.get(`${modelPrompt}.message.validTaskMinOkQty`).d('合格数不能小于0'),
      });
    }else {
      setMinOkQtyFlag(false);
    }
  }

  const handleNgQtyChange = (value) => {
    const total = inspectInfoDS.current.get('samplQty');
    if (total) {
      inspectInfoDS.current.set('samplingOkQty', total - value)
    } else {
      inspectInfoDS.current.set('samplingNgQty', null);
      notification.error({
        message: intl.get(`${modelPrompt}.message.samplQty`).d('未输入抽样总数，请检查！'),
      });
    }
  };

  return (
    <>
      {customizeForm(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.HEAD`,
        },
        <Form
          dataSet={inspectInfoDS}
          columns={3}
          labelWidth={115}
          disabled={!canEdit}
          labelLayout={LabelLayout.horizontal}
          labelAlign={LabelAlign.right}
        >
          <ItemGroup
            // @ts-ignore
            name="itemGroup1"
            label={intl.get(`${modelPrompt}.label.qtyShow`).d('总数/OK/NG/报废')}
          >
            <Item>
              <NumberField name="inspectSumQty" disabled />
            </Item>
            <Item>
              <NumberField name="okQty" disabled />
            </Item>
            <Item>
              <NumberField
                name="ngQty"
                disabled={!canEdit || disposalStatus}
                style={{ border: minOkQtyFlag ? '1px solid red' : '' }}
                onChange={handleOkQtyChange}
              />
            </Item>
            <Item>
              <NumberField
                name="scrapQty"
                disabled={!canEdit || disposalStatus}
                style={{ border: minOkQtyFlag ? '1px solid red' : '' }}
                onChange={handleOkQtyChange}
              />
            </Item>
          </ItemGroup>
          <SelectBox name="inspectResult" disabled={!canEdit || disposalStatus} />
          {/** @ts-ignore */}
          <ItemGroup name="itemGroup2" label={intl.get(`${modelPrompt}.attach`).d('附件')}>
            <Item>
              <Attachment {...attachmentProps1} disabled={!canEdit || disposalStatus} />
            </Item>
            <Item>
              <Attachment {...attachmentProps2} disabled={!canEdit || disposalStatus} />
            </Item>
          </ItemGroup>
          {inspectInfoDS?.current?.get('sourceTaskCode') && (
            <Output
              colSpan={2}
              name="sourceTaskCode"
              renderer={({ value, record }) => (
                <span>
                  {value}&nbsp;&nbsp;(&nbsp;
                  {intl.get(`${modelPrompt}.sourceInspectResultDesc`).d('检验结果')}:
                  {record?.get('sourceInspectResultDesc')};&nbsp;
                  {intl.get(`${modelPrompt}.sourceOkQty`).d('合格品数')}:
                  {record?.get('sourceOkQty')}
                  ;&nbsp;
                  {intl.get(`${modelPrompt}.sourceNgQty`).d('不合格品数')}:
                  {record?.get('sourceNgQty')};&nbsp;
                  {intl.get(`${modelPrompt}.sourceScrapQty`).d('报废数')}:
                  {record?.get('sourceScrapQty')};&nbsp;)
                </span>
              )}
            />
          )}
          <ItemGroup
            // @ts-ignore
            name="itemGroup3"
            label={intl.get(`${modelPrompt}.sampling.qtyShow`).d('抽样总数/OK/NG')}
          >
            <Item>
              <NumberField name="samplQty" disabled={!canEdit || disposalStatus} />
            </Item>
            <Item>
              <NumberField name="samplingOkQty" disabled />
            </Item>
            <Item>
              <NumberField
                name="samplingNgQty"
                disabled={!canEdit || disposalStatus}
                onChange={handleNgQtyChange}
              />
            </Item>
          </ItemGroup>
          <Output name="materialCode" />
          <Output name="materialName" />
          <TextField name="remark" newLine disabled={!canEdit || disposalStatus} />
          {/* <Output name="stationWorkcellName" /> */}
          {showNcReviewer && <Lov name="ncReviewerLov" disabled={disposalStatus} />}
          <Output name="equipmentCode" />
          <Output name="inLocatorTime" />
          {inspectInfoDS.current?.get('inputDisposalFlag') === 'Y' && inspectInfoDS.current?.get('editDisposalFlag') === 'Y' && inspectResultNow === 'NG' && inspectInfoDS.current?.get('inspectBusinessType') !== 'ORT' && (
            <Select
              name="disposalType"
              onChange={(value, oldValue) => handleChangeDisposalType(value, oldValue)}
              disabled={disposalTypeStatus()}
            />
          )}
          {inspectInfoDS.current?.get('inspectBusinessType') === 'ORT' && (
            <>
              <Select
                name="disposalType"
                onChange={(value, oldValue) => handleChangeDisposalType(value, oldValue)}
                disabled={disposalTypeStatus()}
              />
              <Select
                name="curDispFunctionId"
                options={disposalModeDS}
                onChange={(value, oldValue) => handleChangeDisposalMode(value, oldValue)}
              />
            </>
          )}
          {inspectInfoDS.current?.get('inputDisposalFlag') === 'Y' && inspectInfoDS.current?.get('docOneTaskFlag') === 'Y' && disposalType === 'ALL' && (
            <>
              {inspectInfoDS.current?.get('editDisposalFlag') === 'Y' && inspectResultNow === 'NG' && inspectInfoDS.current?.get('inspectBusinessType') !== 'ORT' && (
                <Select
                  name="curDispFunctionId"
                  options={disposalModeDS}
                  onChange={(value, oldValue) => handleChangeDisposalMode(value, oldValue)}
                />
              )}
              {disposalMode === 'REWORK_SOURCE' && (
                <>
                  <Lov name="reworkOperationObj" />
                  <Lov name="reworkWorkcellObj" />
                </>
              )}
              {disposalMode === 'REWORK_ROUTER' && (
                <>
                  <Lov name="reworkRouterObj" />
                </>
              )}
              {disposalMode === 'DEGRADE' && (
                <>
                  <Lov name="degradeMaterialObj" />
                  <Select name="degradeRevisionCode" />
                  <TextField name="degradeLevel" />
                </>
              )}
              {disposalMode === 'CONCESSION_INTERCEPTION' && (
                <>
                  <Lov name="concessionInterceptOperationObj" />
                  <Lov name="interceptWorkcellObj" />
                  {/* <Lov name="routerStepObj" /> */}
                </>
              )}
              {disposalMode === 'EO_DISCHARGE' && (
                <>
                  <Lov name="dischargeWorkcellObj" />
                </>
              )}
              {disposalMode === 'CROSS_WO_INTERCEPT' && (
                <>
                  <Lov name="overInterceptOperationObj" />
                </>
              )}
            </>
          )}
          {inspectResultNow === 'NG' && (
            <>
              <Lov name="docDisposalUserLov" />
            </>
          )}
          {inspectInfoDS.current?.get('inputDisposalFlag') === 'Y' && inspectInfoDS.current?.get('docOneTaskFlag') === 'Y' && disposalType === 'PART' && inspectResultNow === 'NG' && (
            <Item name="partDisposalBtn">
              <PartDisposalComponent {...partDisposalProps} />
            </Item>
          )}
        </Form>,
      )}
      {inspectInfoDS.getState('modeType') === ModeType.col && colShowFlag && (
        <>
          {customizeTable(
            {
              code: 'MT.INSPECT_PLATFORM_DETAIL.COL',
            },
            <Table
              dataSet={inspectItemColDS}
              columns={itemColColumns}
              rowHeight={30}
              virtual
              virtualCell
              style={{ height: 600 }}
            />,
          )}
          <PermissionButton
            type="c7n-pro"
            icon="add"
            disabled={!canEdit}
            onClick={handleItemAddInspectObj}
            permissionList={[
              {
                code: `inspectionPlatform.dist.button.addInspectObj`,
                type: 'button',
                meaning: '详情页-添加检验对象',
              },
            ]}
            className={styles['button-text']}
          >
            {intl.get(`${modelPrompt}.button.addItemInspectObj`).d('添加检验对象')}
          </PermissionButton>
        </>
      )}
      {inspectInfoDS.getState('modeType') === ModeType.row && (
        <InspectItemRowComponent {...inspectItemRowProps} />
      )}
      {inspectInfoDS.getState('modeType') === ModeType.rowObj && (
        <InspectItemRowObjComponent {...inspectItemRowObjProps} />
      )}
    </>
  );
};

export default InspectInfoComponent;
