import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'NcReviewManagementPlatform';
const tenantId = getCurrentOrganizationId();

const tableQueryDS: () => DataSetProps = () => ({
  fields: [
    {
      name: 'ncReviewCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReviewCode`).d('不良评审单号'),
    },
    {
      name: 'sourceDocLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceDoc`).d('来源检验单号'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.REVIEW_INSPECT_DOC',
      lovPara: { tenantId },
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'inspectDocId',
      bind: 'sourceDocLov.inspectDocId',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lookupCode: 'APEX_QMS.NC_REPORT.INSPECT_BUS_TYPE_DOC_CREATE',
    },
    {
      name: 'approveStatus',
      type: FieldType.string,
      lookupCode: 'APEX_QMS.APPROVAL_STATUS',
      label: intl.get(`${modelPrompt}.approveStatus`).d('审批状态'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialName`).d('物料编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.MATERIAL',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('物料BOM'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specification`).d('规格'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      name: 'supplierObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierObject.supplierId',
    },
    {
      name: 'applicationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.applicationDateFrom`).d('创建时间从'),
      max: 'applicationDateTo',
    },
    {
      name: 'applicationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.applicationDateTo`).d('创建时间至'),
      min: 'applicationDateFrom',
    },
  ],
  events: {
    update: ({ record, name }) => {
      if (name === 'materialLov' && record.get('materialLov')) {
        const { materialBom, model } = record.get('materialLov');
        if (materialBom) {
          record.set('bomCode', materialBom);
          record.getField('bomCode').set('disabled', true);
        }
        if (model) {
          record.set('model', model);
          record.getField('model').set('disabled', true);
        }
      }
      if (name === 'materialLov' && !record.get('materialLov')) {
        record.set('model', null);
        record.getField('model').set('disabled', false);
        record.set('bomCode', null);
        record.getField('bomCode').set('disabled', false);
      }
    },
  },
});

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'ncReviewId',
  fields: [
    {
      name: 'ncReviewCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReviewCode`).d('不良评审单号'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
    },
    {
      name: 'approveStatusMeaning',
      type: FieldType.string,
      lookupCode: 'APEX_QMS.APPROVAL_STATUS',
      label: intl.get(`${modelPrompt}.approveStatusMeaning`).d('审批状态'),
    },
    {
      name: 'inspectDocCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocCode`).d('检验单号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'extendMonth',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.extendMonth`).d('延保月份'),
    },
    {
      name: 'supplyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplyFlag `).d('补料标识'),
      lookupCode: 'APEX_WMS.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'disposalProposal',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalProposal`).d('处置建议'),
    },
    {
      name: 'disposalMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalMethod`).d('处置方式'),
    },
    {
      name: 'name',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.name`).d('处置人'),
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('物料BOM'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'defectiveRatePercent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectiveRatePercent`).d('不良率'),
    },
    {
      name: 'applicantName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicantName`).d('申请人'),
    },
    {
      name: 'applicationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.applicationDate`).d('申请日期'),
    },
    {
      name: 'ncReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReason`).d('不良评审原因'),
    },
    {
      name: 'creatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creatorName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-review/page/ui`,
        method: 'POST',
      };
    },
  },
});

const lineTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'orderSeq',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'sourceInspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceInspectItemDesc`).d('检验项目名称'),
    },
    {
      name: 'inspectionStandard',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectionStandard`).d('检验标准'),
    },
    {
      name: 'trueValues',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
    },
    {
      name: 'falseValues',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
    },
    {
      name: 'warningValues',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warningValue`).d('预警值'),
    },
    {
      name: 'inspectValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.detectionValue`).d('检测值'),
    },
    {
      name: 'inspectResultMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResult`).d('检验结果'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-review-line/page/ui`,
        method: 'POST',
      };
    },
  },
});

export { tableDS, tableQueryDS, lineTableDS };
