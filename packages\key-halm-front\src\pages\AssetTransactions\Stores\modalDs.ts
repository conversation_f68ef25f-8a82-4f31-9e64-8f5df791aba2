/*
 * @Description:
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-03-30 11:59:06
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { mmtFields, atnFields } from 'alm/fields';
import { DataSetProps, DataToJSON } from 'choerodon-ui/pro/lib/data-set/interface';
import { FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum'; // FieldIgnore
import getLang from '../Langs';

const tenantId = getCurrentOrganizationId();

/**
 * 资产信息表格
 */
function assetModalDS(): DataSetProps {
  return {
    autoCreate: false,
    autoQuery: false,
    dataKey: 'content',
    selection: DataSetSelection.single,
    primaryKey: 'assetId',
    cacheSelection: true,
    queryFields: [
      {
        name: 'assetNum',
        type: FieldType.string,
        label: getLang('ASSET_EQP_NUM'),
      },
      {
        name: 'detailCondition',
        type: FieldType.string,
        label: getLang('ASSET_DESC'),
      },
    ],
    fields: [
      {
        name: 'assetNum',
        type: FieldType.string,
        label: getLang('ASSET_EQP_NUM'),
      },
      {
        name: 'assetDesc',
        type: FieldType.string,
        label: getLang('ASSET_DESC'),
      },
      {
        name: 'processStatus',
        type: FieldType.string,
      },
      {
        name: 'assetStatusName',
        type: FieldType.string,
        label: getLang('DEVICE_STATUS'),
      },
      {
        name: 'todo2',
        type: FieldType.string,
        // label: intl.get(`${promptCode}.todo2`).d('正在处理的事务单据'),
      },
      {
        name: 'lineNum', // 行号
        type: FieldType.string,
      },
      {
        name: 'changeNum', // 单号
        type: FieldType.string,
      },
      {
        name: 'transactionTypeName', // 类型
        type: FieldType.string,
      },
    ],
    dataToJSON: DataToJSON.selected,
    transport: {
      read: config => {
        const { params } = config;
        return {
          params: {
            ...params,
            bothFlag: 1,
          },
          url: `${HALM_ATN}/v1/${tenantId}/asset-info/list`,
          method: 'GET',
        };
      },
    },
    events: {
      load: ({ dataSet }) => {
        // 对不能选择的数据（行号lineNum有值并且状态不是CANCELED, COMPLETED都进行禁用
        dataSet.records.forEach(i => {
          const { lineNum, processStatus } = i.toData();
          if (lineNum && !['CANCELED', 'COMPLETED'].includes(processStatus)) {
            // eslint-disable-next-line no-param-reassign
            i.selectable = false;
          }
        });
      },
    },
  };
}

// 选择物料lovBtnDS
function lovBtnDS() {
  return {
    autoQuery: true,
    fields: [
      {
        ...mmtFields.itemLov,
        multiple: ',',
        computedProps: {
          lovPara: ({ dataSet }) => ({
            tenantId,
            itemCategoryScope: dataSet.getState('itemCategoryScope'),
          }),
        },
      },
      {
        ...mmtFields.itemId,
        multiple: ',',
      },
      {
        ...mmtFields.itemName,
        multiple: ',',
      },
      {
        ...atnFields.standardAssetLov,
        multiple: ',',
        computedProps: {
          lovPara: ({ dataSet }) => ({
            tenantId,
            toolingScope: dataSet.getState('toolingScope'),
          }),
        },
      },
      {
        ...atnFields.standardAssetId,
        multiple: ',',
      },
      {
        ...atnFields.standardAssetName,
        multiple: ',',
      },
    ],
  };
}
export { assetModalDS, lovBtnDS };
