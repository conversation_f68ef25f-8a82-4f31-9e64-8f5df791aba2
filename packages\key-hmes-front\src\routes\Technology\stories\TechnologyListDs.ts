/**
 * @Description: 工艺维护列表-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 10:27:17
 * @LastEditTime: 2023-03-23 17:08:07
 * @LastEditors: <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.process.technology.model.technology';

const tableDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation/list/ui`,
        method: 'GET',
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.method.domain.entity.MtSubstep';
      return {
        data: { substepId: record.get('substepId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_METHOD}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  queryFields: [
    {
      name: 'operationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationType`).d('工艺类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ROUTER&stateType=typeList&typeGroup=OPERATION_TYPE&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
    },
  ],
  fields: [
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('工艺描述'),
    },
    {
      name: 'operationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationType`).d('工艺类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ROUTER&stateType=typeList&typeGroup=OPERATION_TYPE&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'operationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationStatusDesc`).d('工艺状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ROUTER&statusGroup=OPERATION_STATUS&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
    },
    {
      name: 'completeInconformityFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completeInconformityFlag`).d('完工不一致标识'),
    },
    {
      name: 'currentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本标识'),
    },
    {
      name: 'dateFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间从'),
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('生效时间至'),
    },
    {
      name: 'workcellType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellType`).d('工作单元类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&stateType=workCellList&typeGroup=WORKCELL_TYPE&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defaultWorkcellCode`).d('默认工作单元'),
    },
    {
      name: 'standardReqdTimeInProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardReqdTimeInProcess`).d('工艺过程时间'),
    },
    {
      name: 'standardMaxLoop',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardMaxLoop`).d('最大循环次数'),
    },
    {
      name: 'standardSpecialIntroduction',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardSpecialIntroduction`).d('特殊指令'),
    },
  ],
});

export { tableDS }
