/*
 * @Description: 市场活动单-主界面DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-18 18:04:36
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-04 11:48:35
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.marketActivity.marketActivityDoc';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'marketActivityId',
  queryFields: [
    {
      name: 'marketActivityCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActivityCode`).d('活动单编号'),
    },
    {
      name: 'marketActivityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActivityStatus`).d('状态'),
      lookupCode: 'YP.QIS.MARKET_ACTIVITY_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'faultPhenomenon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultPhenomenon`).d('故障现象'),
    },
    {
      name: 'occurrence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.occurrence`).d('发生情况'),
    },
    {
      name: 'batteryPackModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryPackModel`).d('电池包型号'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'itemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemCode`).d('客户零件号'),
    },
    {
      name: 'itemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemName`).d('客户零件名称'),
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('重要度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
    },
    {
      name: 'createdPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdPerson`).d('创建人'),
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'createdPersonLov.id',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
  ],
  fields: [
    {
      name: 'marketActivityCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActivityCode`).d('活动单编号'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'marketActivityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marketActivityStatus`).d('状态'),
      lookupCode: 'YP.QIS.MARKET_ACTIVITY_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'responsibleDepartmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleDepartmentName`).d('责任部门'),
    },
    {
      name: 'faultPhenomenon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultPhenomenon`).d('故障现象'),
    },
    {
      name: 'occurrence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.occurrence`).d('发生情况'),
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('重要度'),
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      lovPara: { tenantId },
    },
    {
      name: 'batteryPackModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryPackModel`).d('电池包型号'),
    },
    {
      name: 'ypItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ypItemCode`).d('物料编码'),
    },
    {
      name: 'ypItemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ypItemName`).d('物料名称'),
    },
    {
      name: 'itemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemCode`).d('客户零件号'),
    },
    {
      name: 'itemName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemName`).d('客户零件名称'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题编码'),
    },
    {
      name: 'problemTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemTitle`).d('问题描述'),
    },
    {
      name: 'applyDepartmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyDepartmentName`).d('申请部门'),
    },
    {
      name: 'vehicleModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('车型'),
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      lovPara: { tenantId },
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('提案理由'),
    },
    {
      name: 'objectQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectQty`).d('对象数量'),
    },
    {
      name: 'objectRange',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.objectRange`).d('对象范围'),
      bucketName: 'qms',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
    },
    {
      name: 'activityOverview',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.activityOverview`).d('活动概述'),
    },
    {
      name: 'materialCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialCost`).d('材料费/元'),
    },
    {
      name: 'manageCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.manageCost`).d('管理费/元'),
    },
    {
      name: 'laborCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.laborCost`).d('工时费/元'),
    },
    {
      name: 'singleUnitCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.singleUnitCost`).d('单台费用/元'),
    },
    {
      name: 'measurementSumCost',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.measurementSumCost`).d('测算总费用/元'),
    },
    {
      name: 'startTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
    },
    {
      name: 'completionDegree',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.completionDegree`).d('完成度（百分比）'),
    },
    {
      name: 'endTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdPerson`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-activitys/page/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };