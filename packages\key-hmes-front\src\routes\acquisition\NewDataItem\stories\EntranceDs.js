/**
 * @feature 新数据收集项维护-表格列表以及查询的DS
 * @date 2021-4-16
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.acquisition.dataItem.model.dataItem';
const tenantId = getCurrentOrganizationId();

/**
 * 列表和详情页
 */
const entranceDS = () => ({
  primaryKey: 'tagCode',
  queryUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-assemble-group/list/ui`,
  autoQuery: true,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: 'multiple',
  queryFields: [
    {
      name: 'tagCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCode`).d('数据项编码'),
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('数据项描述'),
    },
    {
      name: 'valueType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueType`).d('数据类型'),
      options: valueTypePointOptionDs,
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'collectionMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.collectionMethod`).d('数据收集方式'),
      options: collectionMethodPointOptionDs,
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'displayValueFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.displayValueFlag`).d('是否显示标准值'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'allowUpdateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.allowUpdateFlag`).d('是否允许更新'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'valueAllowMissing',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueAllowMissing`).d('允许缺失值'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  fields: [
    {
      name: 'tagCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCode`).d('数据项编码'),
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('数据项描述'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'collectionMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.collectionMethod`).d('数据收集方式'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=GENERAL&typeGroup=TAG_COLLECTION_METHOD`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'enableFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'displayValueFlag', // 新增参数
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.displayValueFlag`).d('是否显示标准值'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'allowUpdateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.allowUpdateFlag`).d('是否允许更新'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'valueAllowMissing',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueAllowMissing`).d('允许缺失值'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'valueType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueType`).d('数据类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=GENERAL&typeGroup=TAG_VALUE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
    },
    // {
    //   name: 'minimumValue',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.minimumValue`).d('最小值'),
    // },
    // {
    //   name: 'maximalValue',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.maximalValue`).d('最大值'),
    // },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('计量单位'),
    },
    {
      name: 'valueList', // 新增参数
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
    },
    // {
    //   name: 'mandatoryNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.mandatoryNum`).d('必须的数据条数'),
    // },
    // {
    //   name: 'optionalNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.optionalNum`).d('可选的数据条数'),
    // },
    {
      name: 'dateFormat',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateFormat`).d('日期格式'),
    },
    {
      name: 'specialRecordFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialRecordFlag`).d('特殊采集标识'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'defaultNcCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defaultNcCode`).d('默认不良代码'),
    },
    {
      name: 'originOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.originOperationName`).d('特殊参数来源工艺'),
    },
    {
      name: 'modifyFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modifyFlag`).d('是否变更'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag/query/ui`,
        method: 'GET',
      };
    },
    submit: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag/edit/ui`,
        method: 'POST',
      };
    },
  },
});

const valueTypePointOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'TAG_VALUE_TYPE', module: 'GENERAL', tenantId },
      };
    },
  },
});

const collectionMethodPointOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'TAG_COLLECTION_METHOD', module: 'GENERAL', tenantId },
      };
    },
  },
});

const targetPointOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'DISTRIBUTION_COVER_LOCATION_TYPE', tenantId },
      };
    },
  },
});

export { entranceDS, targetPointOptionDs, collectionMethodPointOptionDs, valueTypePointOptionDs };
