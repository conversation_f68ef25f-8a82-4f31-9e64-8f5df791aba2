import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  routes: [
    {
      path: '/hspc/control-chart',
      priority: 1000,
      routes: [
        {
          path: '/hspc/control-chart/list',
          component: '@/routes/hspc/ControlChart/ControlChartList',
          priority: 1000,
        },
        {
          path: '/hspc/control-chart/detail/:id',
          component: '@/routes/hspc/ControlChart/ControlChartDetail',
          priority: 1000,
        },
        {
          path: '/hspc/control-chart/create-page/:code',
          component: '@/routes/hspc/ControlChart/ControlChartCreatePage',
          priority: 1000,
        },
        {
          path: '/hspc/control-chart/display/:id',
          component: '@/routes/hspc/ControlChart/ControlChartDisplay',
          priority: 1000,
        },
        {
          path: '/hspc/control-chart/history-chart/:id',
          component: '@/routes/hspc/ControlChart/HistoryChartDisplay',
          priority: 1000,
        },
        {
          path: '/hspc/control-chart/alarm-message/:id',
          component: '@/routes/hspc/ControlChart/AlarmMessages',
          priority: 1000,
        },
        {
          path: '/hspc/control-chart/manual-access/:id',
          component: '@/routes/hspc/ControlChart/DataAccess/ManualAccess',
          priority: 1000,
        },
      ],
    },
    {
      path: '/hwms/tooling-ledger/list',
      authorized: true,
      component: '@/routes/ToolingLedger',
    },
    {
      path: '/hmes/work-bench',
      priority: 10,
      component: '@/routes/calendar/Workbench',
    },
    {
      path: '/hspc/analysis-control-chart',
      priority: 1000,
      routes: [
        {
          path: '/hspc/analysis-control-chart/list',
          priority: 1000,
          component: '@/routes/hspc/AnalysisControlChart/AnalysisControlChartList',
        },
        {
          path: '/hspc/analysis-control-chart/new/display/',
          priority: 1000,
          component: '@/routes/hspc/AnalysisControlChart/AnalysisControlChartDisplayNew',
        },
        {
          path: '/hspc/analysis-control-chart/display/:type/:id',
          priority: 1000,
          component: '@/routes/hspc/AnalysisControlChart/AnalysisControlChartDisplay',
        },
        {
          path: '/hspc/analysis-control-chart/create-page/:id',
          priority: 1000,
          component: '@/routes/hspc/AnalysisControlChart/AnalysisControlChartCreatePage',
        },
      ],
    },
    // 不良记录管理平台
    {
      path: 'hmes/bad-record/platform',
      priority: 10,
      routes: [
        {
          path: '/hmes/bad-record/platform/list',
          priority: 10,
          component: '@/routes/badRecord/Platform/List',
        },
        {
          path: '/hmes/bad-record/platform/detail/:id',
          priority: 10,
          component: '@/routes/badRecord/Platform/Detail',
        },
        {
          path: '/hmes/bad-record/platform/num/:num',
          priority: 10,
          component: '@/routes/badRecord/Platform/Detail',
        },
      ],
    },
    // 每日计划
    {
      path: '/hmes/daily-schedule',
      component: '@/routes/DailySchedule',
    },
    {
      path: '/hmes/assemblyOrderDispatch',
      title: '装配工单派工',
      routes: [
        {
          path: '/hmes/assemblyOrderDispatch',
          component: '@/pages/AssemblyOrderDispatch',
        },
      ],
    },

    // 执行作业管理
    {
      path: '/hmes/workshop/execute-operation-management',
      routes: [
        {
          path: '/hmes/workshop/execute-operation-management/list',
          component: '@/pages/Execute/ExecuteList',
        },
        {
          path: '/hmes/workshop/execute-operation-management/detail/:id',
          component: '@/pages/Execute/ExecuteDetail',
        },
      ],
    },
    // 在制品批量冻结解冻
    {
      path: '/hmes/work-in-progress-freeze-thawe',
      component: '@/pages/WorkInProgressFreezeThawe',
    },
    // 在制品报表
    {
      path: '/hmes/work-in-progress-report',
      title: '在制品报表',
      component: '../routes/WorkInProgressReport',
    },
    // 工序作业平台
    {
      path: '/hmes/operation-platform',
      authorized: true,
      title: '工序作业平台',
      component: '../routes/OperationPlatform',
    },
    // 机加工序作业平台
    {
      authorized: true,
      path: '/pub/hmes/machine-operation-platform',
      routes: [
        {
          authorized: true,
          path: '/pub/hmes/machine-operation-platform/enter',
          component: '../routes/MachineOperationPlatform/EnterModal',
        },
        {
          authorized: true,
          path: '/pub/hmes/machine-operation-platform/list',
          component: '../routes/MachineOperationPlatform',
          models: '../routes/MachineOperationPlatform/model.ts',
        },
      ],
    },
    // 返修作业平台
    {
      path: '/hmes/repair-operation-platform',
      authorized: true,
      routes: [
        {
          path: '/hmes/repair-operation-platform/enter',
          component: '@/routes/RepairOperationPlatform/EnterModal',
        },
        {
          path: '/hmes/repair-operation-platform/list',
          component: '@/routes/RepairOperationPlatform',
          models: '@/routes/RepairOperationPlatform/model.ts',
        },
      ],
    },
    // 异常处理平台
    {
      path: '/hmes/exception-handling-platform/:workcellId',
      models: '@/models/exceptionHandlingPlatform',
      component: '@/routes/ExceptionHandlingPlatform',
      priority: 1000,
    },
    // 异常信息维护
    {
      path: '/hmes/abnormal-info',
      priority: 1000,
      routes: [
        {
          path: '/hmes/abnormal-info/list',
          component: '@/routes/AbnormalInfo',
          models: '@/models/abnormalInfo',
          priority: 1000,
        },
        {
          path: '/hmes/abnormal-info/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
          priority: 1000,
        },
      ],
    },
    // 异常收集组维护
    {
      path: '/hmes/abnormal-collection',
      priority: 1000,
      routes: [
        {
          path: '/hmes/abnormal-collection/list',
          component: '@/routes/AbnormalCollection/List',
          models: '@/models/abnormalCollection',
          priority: 1000,
        },
        {
          path: '/hmes/abnormal-collection/detail/:id',
          component: '@/routes/AbnormalCollection/Detail',
          models: '@/models/abnormalCollection',
          priority: 1000,
        },
        {
          path: '/hmes/abnormal-collection/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
          priority: 1000,
        },
      ],
    },
    {
      path: '/hmes/inspection-time-rule/list',
      component: '@/routes/InspectionTimeRule',
      authorized: true,
    },
    // 异常原因维护
    {
      path: '/hmes/abnormal-reason-maintain',
      routes: [
        {
          path: '/hmes/abnormal-reason-maintain/list',
          component: '@/routes/AbnormalReasonMaintain',
        },
        {
          path: '/hmes/abnormal-reason-maintain/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
        },
      ],
    },
    // 异常信息查看报表
    {
      path: '/hmes/abnormal-report',
      models: '@/models/abnormalReport',
      component: '@/routes/AbnormalReport',
    },
    // 安灯类型责任人维护
    {
      path: '/hmes/andeng-responsible-maintain',
      routes: [
        {
          path: '/hmes/andeng-responsible-maintain/list',
          component: '@/routes/AndengResponsible',
          models: ['@/models/andengResponsible'],
        },
        {
          path: '/hmes/andeng-responsible-maintain/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
        },
      ],
    },
    // 机加生产派工
    {
      authorized: true,
      path: '/hmes/machine-production-dispatch',
      component: '@/routes/MachineProductionDispatch',
    },
    // 自制件返修指令创建
    {
      // authorized: true,
      path: '/hmes/repair-instruction-created',
      component: '@/routes/RepairInstructionCreated',
    },
    // 工序报工实绩报表
    {
      authorized: true,
      path: '/hmes/process-report-performance-report',
      component: '@/routes/ProcessReportPerformanceReport',
    },
    // 工艺维护
    {
      path: '/hmes/process/technology',
      priority: 10,
      routes: [
        {
          path: '/hmes/process/technology/list',
          component: '@/routes/Technology/TechnologyList',
          priority: 10,
        },
        {
          path: '/hmes/process/technology/dist/:id',
          component: '@/routes/Technology/TechnologyDist',
          priority: 10,
        },
      ],
    },
    // 作业指导书管理
    {
      path: '/hmes/working-instruction-maintenance',
      routes: [
        {
          path: '/hmes/working-instruction-maintenance/list',
          component: '../routes/WorkingInstructionMaintenance/WorkingInstructionMaintenanceList',
        },
        {
          path: '/hmes/working-instruction-maintenance/detail/:id',
          component: '../routes/WorkingInstructionMaintenance/WorkingInstructionMaintenanceDetail',
        },
      ],
    },

    // 制造装配清单
    {
      path: '/hmes/product/manufacture-list',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/manufacture-list/list',
          priority: 10,
          component: '@/routes/product/C7nManufactureBom/index',
        },
        {
          path: '/hmes/product/manufacture-list/dist/:id',
          priority: 10,
          component: '@/routes/product/C7nManufactureBom/detail',
        },
      ],
    },
    // 物料装配清单
    {
      path: '/hmes/product/assembly-list',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/assembly-list/list',
          priority: 10,
          component: '@/routes/product/C7nMaterialBom/index',
        },
        {
          path: '/hmes/product/assembly-list/dist/:id',
          priority: 10,
          component: '@/routes/product/C7nMaterialBom/detail',
        },
      ],
    },
    // 检验业务类型规则维护
    {
      path: '/hwms/inspect-business-type-rule',
      routes: [
        {
          path: '/hwms/inspect-business-type-rule/list',
          component: '@/routes/InspectBusTypeRule/InspectBusList',
        },
        {
          path: '/hwms/inspect-business-type-rule/dist/:id',
          component: '@/routes/InspectBusTypeRule/InspectBusDetail',
        },
      ],
    },

    // 采购退货平台(重构版)
    {
      path: '/hmes/purchase/purchase-return-standard',
      routes: [
        {
          path: '/hmes/purchase/purchase-return-standard/list',
          priority: 10,
          component: '@/routes/PurchaseReturnStandard/PurchaseList',
        },
        {
          path: '/hmes/purchase/purchase-return-standard/detail/:id',
          priority: 10,
          component: '@/routes/PurchaseReturnStandard/PurchaseDetail',
        },
      ],
    },

    // 入库单查询（重构）
    {
      path: '/hmes/inbound/inbound-order-query-standard',
      routes: [
        {
          path: '/hmes/inbound/inbound-order-query-standard/list',
          priority: 10,
          component: '@/routes/InboundOrderQueryStandard/InboundOrderQueryList',
        },
      ],
    },

    // SIP文件管理
    {
      path: '/hwms/sip-file-manage',
      routes: [
        {
          path: '/hwms/sip-file-manage/list/:sipCode?',
          component: '@/routes/hwms/SIPFileManagement/SIPFileList',
        },
      ],
    },
    // 初期管理活动平台
    {
      path: '/hwms/initial-management-activity-platform',
      priority: 10,
      routes: [
        {
          path: '/hwms/initial-management-activity-platform/list',
          component:
            '@/routes/hwms/InitialManagementActivityPlatform/InitialManagementActivityList',
          priority: 10,
        },
        {
          path: '/hwms/initial-management-activity-platform/detail/:id',
          component:
            '@/routes/hwms/InitialManagementActivityPlatform/InitialManagementActivityDetail',
          priority: 10,
        },
        {
          path: '/pub/hwms/initial-management-activity-platform/detail/:id',
          component:
            '@/routes/hwms/InitialManagementActivityPlatform/InitialManagementActivityDetail',
          priority: 10,
        },
        {
          path: '/himp/commentImport/:code',
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },
    // 临时工艺变更单
    {
      path: '/hwms/temporary-process-change-orders',
      routes: [
        {
          path: '/hwms/temporary-process-change-orders/list',
          component: '@/routes/hwms/TemporaryProcessChangeOrders/list',
        },
        {
          path: '/hwms/temporary-process-change-orders/detail/:id',
          component: '@/routes/hwms/TemporaryProcessChangeOrders/details',
        },
        {
          path: '/pub/hwms/temporary-process-change-orders/detail/:id',
          component: '@/routes/hwms/TemporaryProcessChangeOrders/details',
        },
      ],
    },
    // 不良评审单管理平台
    {
      path: '/hwms/ncReport-doc-maintain-new',
      routes: [
        {
          path: '/hwms/ncReport-doc-maintain-new/list',
          component: '@/routes/hwms/NcReportDocMaintain/NcReportList',
        },
        {
          path: '/hwms/ncReport-doc-maintain-new/dist/:id',
          component: '@/routes/hwms/NcReportDocMaintain/NcReportDetail',
        },
      ],
    },
    // 不良评审单管理平台
    {
      path: '/hwms/ncReview-management-platform/list',
      component: '@/routes/NcReviewManagementPlatform',
      authorized: true,
    },
    // 不良代码维护
    {
      path: '/hmes/bad/defect-code',
      priority: 999,
      routes: [
        {
          path: '/hmes/bad/defect-code/list',
          priority: 999,
          component: '@/routes/bad/DefectCode/DefectCodeList',
        },
        {
          path: '/hmes/bad/defect-code/dist/:id',
          priority: 999,
          component: '@/routes/bad/DefectCode/DefectCodeDist',
        },
      ],
    },
    // 不良代码组维护
    {
      path: '/hmes/bad/defect-group',
      priority: 999,
      routes: [
        {
          path: '/hmes/bad/defect-group/list',
          priority: 999,
          component: '@/routes/bad/DefectGroup/DefectGroupList',
        },
        {
          path: '/hmes/bad/defect-group/dist/:id',
          priority: 999,
          component: '@/routes/bad/DefectGroup/DefectGroupDist',
        },
      ],
    },
    // 问题管理平台
    {
      path: '/hwms/problem-management/problem-management-platform',
      routes: [
        {
          path: '/hwms/problem-management/problem-management-platform/list',
          component:
            '@/routes/hwms/ProblemManagement/ProblemManagementPlatform/ProblemManagementList',
        },
        {
          path: '/hwms/problem-management/problem-management-platform/full-text-search',
          component: '@/routes/hwms/ProblemManagement/ProblemManagementPlatform/FullTextSearch',
        },
        {
          path: '/hwms/problem-management/problem-management-platform/dist/:problemId',
          component:
            '@/routes/hwms/ProblemManagement/ProblemManagementPlatform/ProblemManagementDist',
        },
        {
          path: '/pub/hwms/problem-management/problem-management-platform/dist/:problemId',
          component:
            '@/routes/hwms/ProblemManagement/ProblemManagementPlatform/ProblemManagementDist',
        },
      ],
    },
    // 问题管理-问题横展平台
    {
      path: '/hwms/problem-management/transverse-expand',
      routes: [
        {
          path: '/hwms/problem-management/transverse-expand/list',
          component: '@/routes/hwms/ProblemManagement/TransverseExpand/TransverseExpandList',
        },
        {
          path: '/hwms/problem-management/transverse-expand/detail/:id',
          component: '@/routes/hwms/ProblemManagement/TransverseExpand/TransverseExpandDetail',
        },
        {
          path: '/pub/hwms/problem-management/transverse-expand/detail/:id',
          component: '@/routes/hwms/ProblemManagement/TransverseExpand/TransverseExpandDetail',
        },
      ],
    },
    // 问题管理-问题警示平台
    {
      path: '/hwms/problem-management/warning',
      routes: [
        {
          path: '/hwms/problem-management/warning/list',
          component: '@/routes/hwms/ProblemManagement/Warning/WarningList',
        },
        {
          path: '/hwms/problem-management/warning/detail/:id',
          component: '@/routes/hwms/ProblemManagement/Warning/WarningDetail',
        },
        {
          path: '/pub/hwms/problem-management/warning/detail/:id',
          component: '@/routes/hwms/ProblemManagement/Warning/WarningDetail',
        },
      ],
    },
    // 问题复盘
    {
      path: '/hwms/problem-management/duplicate-problem',
      routes: [
        {
          path: '/hwms/problem-management/duplicate-problem/list',
          component: '@/routes/hwms/DuplicateProblem/List',
        },
        {
          path: '/hwms/problem-management/duplicate-problem/detail/:id',
          component: '@/routes/hwms/DuplicateProblem/Detail',
        },
      ],
    },
    // 故障树
    {
      path: '/hwms/problem-tree',
      component: '@/routes/hwms/ProblemTree',
    },
    // 举手填报
    {
      path: '/hwms/raise-hand-report',
      routes: [
        {
          path: '/hwms/raise-hand-report/list',
          component: '@/routes/hwms/RaiseHandReport/list',
        },
        {
          path: '/hwms/raise-hand-report/detail/:id',
          component: '@/routes/hwms/RaiseHandReport/detail',
        },
      ],
    },
    // 问题管理工作台
    {
      path: '/hwms/problem-table',
      component: '@/routes/hwms/ProblemTable/ProblemTableList',
      authorized: true,
    },
    // 检证库管理
    {
      path: '/hwms/verification-library-management-platform',
      routes: [
        {
          path: '/hwms/verification-library-management-platform/list',
          component: '@/routes/hwms/VerificationLibraryManagementPlatform/VerificationibraryList',
        },
        {
          path: '/hwms/verification-library-management-platform/dist/:id',
          component: '@/routes/hwms/VerificationLibraryManagementPlatform/VerificationibraryDetail',
        },
      ],
    },
    // SA/专项检证平台
    {
      path: '/hwms/inspect-execute/sa-verification-platform',
      routes: [
        {
          path: '/hwms/inspect-execute/sa-verification-platform/list',
          component: '@/routes/hwms/SaVerificationPlatform/List',
        },
        {
          path: '/hwms/inspect-execute/sa-verification-platform/dist/:verificationId',
          component: '@/routes/hwms/SaVerificationPlatform/Detail',
        },
        {
          path: '/pub/hwms/inspect-execute/sa-verification-platform/dist/:verificationId',
          component: '@/routes/hwms/SaVerificationPlatform/Detail',
        },
      ],
    },
    // 产线审核模板管理平台
    {
      path: '/hwms/prodline_template_management_platform',
      routes: [
        {
          path: '/hwms/prodline_template_management_platform/list',
          component: '@/routes/hwms/ProdlineTemplateManagementPlatform/ProdlineTemplateList',
        },
        {
          path: '/hwms/prodline_template_management_platform/dist/:id',
          component: '@/routes/hwms/ProdlineTemplateManagementPlatform/ProdlineTemplateDetail',
        },
      ],
    },
    // 产线审核计划管理平台
    {
      path: '/hwms/production-line-audit-plan-management-platform',
      routes: [
        {
          path: '/hwms/production-line-audit-plan-management-platform/list',
          component: '@/routes/hwms/ProductionLineAuditPlanManagementPlatform/ListTable',
        },
        {
          path: '/hwms/production-line-audit-plan-management-platform/create/:id',
          component: '@/routes/hwms/ProductionLineAuditPlanManagementPlatform/ListDetail',
        },
      ],
    },
    // 产线审核任务执行平台
    {
      path: '/hwms/production_line_audit_task_execution_platform',
      routes: [
        {
          path: '/hwms/production_line_audit_task_execution_platform/list',
          component:
            '@/routes/hwms/ProductionLineAuditTaskExecutionPlatform/ProductionLineAuditTaskExecutionList',
        },
        {
          path: '/hwms/production_line_audit_task_execution_platform/dist/:id',
          component:
            '@/routes/hwms/ProductionLineAuditTaskExecutionPlatform/ProductionLineAuditTaskExecutionDetail',
        },
      ],
    },
    // 标准体系文件维护
    {
      path: '/hwms/maintenance-of-standard-systemDocuments',
      routes: [
        {
          path: '/hwms/maintenance-of-standard-systemDocuments/list',
          component: '@/routes/hwms/MaintenanceOfStandardSystemDocuments/ListTable',
        },
        {
          path: '/hwms/maintenance-of-standard-systemDocuments/dist/:id',
          component: '@/routes/hwms/MaintenanceOfStandardSystemDocuments/ListDetail',
        },
      ],
    },
    // 体系文件管理
    {
      path: '/hwms/system-document-management',
      routes: [
        {
          path: '/hwms/system-document-management/list',
          component: '@/routes/hwms/SystemDocumentManagement/SystemDocumentList',
        },
        {
          path: '/hwms/system-document-management/detail/:id',
          component: '@/routes/hwms/SystemDocumentManagement/SystemDocumentListDetail',
        },
        {
          path: '/pub/hwms/system-document-management/detail/:id',
          component: '@/routes/hwms/SystemDocumentManagement/SystemDocumentListDetail',
        },
        {
          path: '/hwms/system-document-management/full-text-search',
          component: '@/routes/hwms/SystemDocumentManagement/FullTextSearch',
        },
      ],
    },
    // 体系审核管理
    {
      path: '/hwms/system-audit',
      routes: [
        {
          path: '/hwms/system-audit/list',
          component: '@/routes/hwms/SystemAudit/SystemAuditList',
        },
        {
          path: '/hwms/system-audit/detail/:id',
          component: '@/routes/hwms/SystemAudit/SystemAuditDetail',
        },
        {
          path: '/pub/hwms/system-audit/detail/:id',
          component: '@/routes/hwms/SystemAudit/SystemAuditDetail',
        },
      ],
    },
    // 量具种类维护
    {
      path: '/hmes/measure-have/king-maintenance',
      routes: [
        {
          path: '/hmes/measure-have/king-maintenance/list',
          component: '@/routes/hwms/measureHave/kingMaintenance',
        },
        {
          path: '/hmes/measure-have/comment-import/:code',
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },
    // 量具型号维护
    {
      path: '/hmes/measure-have/type-maintenance',
      routes: [
        {
          path: '/hmes/measure-have/type-maintenance/list',
          component: '@/routes/hwms/measureHave/typeMaintenance',
        },
        {
          path: '/hmes/measure-have/comment-import/:code',
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },
    // 量具检定平台
    {
      path: '/hmes/measure-have/platform',
      routes: [
        {
          path: '/hmes/measure-have/platform/list/:tab?',
          component: '@/routes/hwms/MeasureHavePlatform/List',
        },
        {
          path: '/hmes/measure-have/platform/dist/:id',
          component: '@/routes/hwms/MeasureHavePlatform/Detail',
        },
        {
          path: '/hmes/measure-have/platform/inspect/:verificationMethod/:inspectDocId',
          component: '@/routes/hwms/MeasureHavePlatform/Detail/inspectDetail',
        },
      ],
    },
    // 计量器具管理平台
    {
      path: '/hwms/meter-management-platform',
      routes: [
        {
          path: '/hwms/meter-management-platform/list',
          component: '@/routes/hwms/MeterManagementPlatform/MeterManagementList',
        },
      ],
    },
    // 量具转移平台
    {
      path: '/hwms/measuring-transfer-platform',
      routes: [
        {
          path: '/hwms/measuring-transfer-platform/list',
          component: '@/routes/hwms/MeasuringTransferPlatform/list',
        },
        {
          path: '/hwms/measuring-transfer-platform/detail/:id?',
          component: '@/routes/hwms/MeasuringTransferPlatform/detail',
        },
      ],
    },
    // 量具注销平台
    {
      path: '/hwms/measuring-scrap-platform',
      routes: [
        {
          path: '/hwms/measuring-scrap-platform/list',
          component: '@/routes/hwms/MeasuringScrapPlatform/list',
        },
        {
          path: '/hwms/measuring-scrap-platform/detail/:id',
          component: '@/routes/hwms/MeasuringScrapPlatform/detail',
        },
      ],
    },
    // 量具型号与MSA分析质量特性关系维护
    {
      path: '/hwms/model-management/model-msa-relation',
      routes: [
        {
          path: '/hwms/model-management/model-msa-relation/list',
          component: '@/routes/hwms/ModelMsaRelation/ModelMsaRelationList',
        },
      ],
    },
    // MSA分析管理平台
    {
      path: '/hwms/msa-analysis-management-platform',
      routes: [
        {
          path: '/hwms/msa-analysis-management-platform/list',
          component: '@/routes/hwms/MsaAnalysisManagementPlatform/List',
        },
        {
          path: '/hwms/msa-analysis-management-platform/detail/:id',
          component: '@/routes/hwms/MsaAnalysisManagementPlatform/Detail',
        },
        {
          path: '/hwms/msa-analysis-management-platform/analysisDetail/:id',
          component:
            '@/routes/hwms/MsaAnalysisManagementPlatform/MsaAnalysisManagementPlatformAnalysisDetail',
        },
      ],
    },
    // 委托实验平台
    {
      path: '/hwms/commissioned-experimental-platform',
      routes: [
        {
          path: '/hwms/commissioned-experimental-platform/list',
          component: '@/routes/hwms/CommissionedExperimentalPlatform/ListTable',
        },
        {
          path: '/hwms/commissioned-experimental-platform/create/:id',
          component: '@/routes/hwms/CommissionedExperimentalPlatform/ListDetail',
        },
        {
          path: '/hwms/commissioned-experimental-platform/line/:id',
          component: '@/routes/hwms/CommissionedExperimentalPlatform/ListLineDetail',
        },
      ],
    },
    // 产品拆解基础数据维护
    {
      path: '/hwms/product-disassembly-data-maintenance',
      routes: [
        {
          path: '/hwms/product-disassembly-data-maintenance/list',
          component: '@/routes/hwms/DataMaintenance/List',
        },
        {
          path: '/hwms/product-disassembly-data-maintenance/detail/:id',
          component: '@/routes/hwms/DataMaintenance/Detail',
        },
      ],
    },
    // 拆解申请单
    {
      path: '/hwms/product-teardown/teardown-apply-doc',
      routes: [
        {
          path: '/hwms/product-teardown/teardown-apply-doc/list',
          component: '@/routes/hwms/TeardownApplyDoc/TeardownApplyList',
        },
        {
          path: '/hwms/product-teardown/teardown-apply-doc/dist/:teardownApplyId',
          component: '@/routes/hwms/TeardownApplyDoc/TeardownApplyDetail',
        },
        {
          path: '/pub/hwms/product-teardown/teardown-apply-doc/dist/:teardownApplyId',
          component: '@/routes/hwms/TeardownApplyDoc/TeardownApplyDetail',
        },
      ],
    },
    // 拆解任务执行平台
    {
      path: '/hwms/disassemble/task-execution-platform',
      routes: [
        {
          path: '/hwms/disassemble/task-execution-platform/list',
          component: '@/routes/hwms/DisassembleTaskExecutionPlatform/List',
          title: '拆解任务执行平台',
        },
        {
          path: '/hwms/disassemble/task-execution-platform/detail/:id',
          component: '@/routes/hwms/DisassembleTaskExecutionPlatform/Detail',
          title: '拆解任务执行平台',
        },
      ],
    },
    // 分层审核项目
    {
      path: '/hwms/hierarchical-audit-project',
      title: '分层审核项目',
      authorized: true,
      component: '@/routes/hwms/HierarchicalAuditProject/list',
    },
    // 分层审核方案
    {
      path: '/hwms/layer-review/layer-review-scheme',
      routes: [
        {
          path: '/hwms/layer-review/layer-review-scheme/list',
          component: '@/routes/hwms/LayerReviewScheme/LayerReviewSchemeList',
        },
        {
          path: '/hwms/layer-review/layer-review-scheme/dist/:layerReviewSchemeId',
          component: '@/routes/hwms/LayerReviewScheme/LayerReviewSchemeDetail',
        },
        {
          path: '/pub/hwms/layer-review/layer-review-scheme/dist/:layerReviewSchemeId',
          component: '@/routes/hwms/LayerReviewScheme/LayerReviewSchemeDetail',
        },
      ],
    },
    // 分层审核计划
    {
      path: '/hwms/hierarchical-audit-plan',
      authorized: true,
      routes: [
        {
          path: '/hwms/hierarchical-audit-plan/audit-plan/list',
          component: '@/routes/hwms/HierarchicalAudit/AuditPlan/AuditPlanList',
        },
        {
          path: '/hwms/hierarchical-audit-plan/audit-plan/detail/:id',
          component: '@/routes/hwms/HierarchicalAudit/AuditPlan/AuditPlanDetail',
        },
        {
          path: '/pub/hwms/hierarchical-audit-plan/audit-plan/detail/:id',
          component: '@/routes/hwms/HierarchicalAudit/AuditPlan/AuditPlanDetail',
        },
      ],
    },
    // 分层审核任务
    {
      path: '/hwms/layer-review/layer-review-task',
      routes: [
        {
          path: '/hwms/layer-review/layer-review-task/list',
          component: '@/routes/hwms/LayerReviewTask/LayerReviewTaskList',
        },
        {
          path: '/hwms/layer-review/layer-review-task/dist/:layerReviewTaskId',
          component: '@/routes/hwms/LayerReviewTask/LayerReviewTaskDetail',
        },
      ],
    },
    // 产品审核-产品审核项目
    {
      path: '/hwms/product-review/product-review-item',
      routes: [
        {
          path: '/hwms/product-review/product-review-item/list',
          component: '@/routes/hwms/ProductReview/ProductReviewItem/ProductReviewItemList',
        },
      ],
    },
    // 产品审核-产品审核方案
    {
      path: '/hwms/product-review/product-review-scheme',
      routes: [
        {
          path: '/hwms/product-review/product-review-scheme/list',
          component: '@/routes/hwms/ProductReview/ProductReviewScheme/List',
        },
        {
          path: '/hwms/product-review/product-review-scheme/:id',
          component: '@/routes/hwms/ProductReview/ProductReviewScheme/Detail',
        },
        {
          path: '/pub/hwms/product-review/product-review-scheme/:id',
          component: '@/routes/hwms/ProductReview/ProductReviewScheme/Detail',
        },
      ],
    },
    // 产品审核-产品审核计划
    {
      path: '/hwms/product-review/product-review-plan',
      routes: [
        {
          path: '/hwms/product-review/product-review-plan/list',
          component: '@/routes/hwms/ProductReview/ProductReviewPlan/ProductReviewPlanList',
        },
        {
          path: '/hwms/product-review/product-review-plan/dist/:id',
          component: '@/routes/hwms/ProductReview/ProductReviewPlan/ProductReviewPlanDist',
        },
        {
          path: '/pub/hwms/product-review/product-review-plan/dist/:id',
          component: '@/routes/hwms/ProductReview/ProductReviewPlan/ProductReviewPlanDist',
        },
      ],
    },
    // 产品审核-产品审核任务
    {
      path: '/hwms/product-review/product-review-task',
      routes: [
        {
          path: '/hwms/product-review/product-review-task/list',
          component: '@/routes/hwms/ProductReview/ProductReviewTask/ProductReviewTaskList',
        },
        {
          path: '/hwms/product-review/product-review-task/dist/:id',
          component: '@/routes/hwms/ProductReview/ProductReviewTask/ProductReviewTaskDist',
        },
        {
          path: '/pub/hwms/product-review/product-review-task/dist/:id',
          component: '@/routes/hwms/ProductReview/ProductReviewTask/ProductReviewTaskDist',
        },
      ],
    },
    // ORT测试执行
    {
      path: '/hwms/ort/test-execution',
      routes: [
        {
          path: '/hwms/ort/test-execution/list',
          component: '@/routes/hwms/ORT/TestExecution/TestExecutionList',
        },
        {
          path: '/hwms/ort/test-execution/detail/:id',
          component: '@/routes/hwms/ORT/TestExecution/TestExecutionDetail',
        },
        // {
        //   path: '/pub/hwms/ort/test-execution/detail/:id',
        //   component: '@/routes/hwms/ORT/TestExecution/TestExecutionDetail',
        // },
      ],
    },
    // ORT检验方案维护
    {
      path: '/hmes/ort/inspection-scheme',
      routes: [
        {
          path: '/hmes/ort/inspection-scheme/list',
          component: '@/routes/hwms/ORT/InspectionScheme/List',
        },
        {
          path: '/hmes/ort/inspection-scheme/:id',
          component: '@/routes/hwms/ORT/InspectionScheme/Detail',
        },
      ],
    },
    // ORT测试申请
    {
      path: '/hwms/ort/test-application',
      routes: [
        {
          path: '/hwms/ort/test-application/list',
          component: '@/routes/hwms/ORT/TestApplication/TestApplicationList',
        },
        {
          path: '/hwms/ort/test-application/detail/:id',
          component: '@/routes/hwms/ORT/TestApplication/TestApplicationDetail',
        },
        {
          path: '/pub/hwms/ort/test-application/detail/:id',
          component: '@/routes/hwms/ORT/TestApplication/TestApplicationDetail',
        },
      ],
    },
    // 维修标准管理
    {
      path: '/hwms/maintenance-standard',
      component: '@/routes/hwms/MaintenanceStandard',
    },
    // 质量反馈平台
    {
      path: '/hwms/market-quality/quality-feedback-platform',
      routes: [
        {
          path: '/hwms/market-quality/quality-feedback-platform/list',
          component: '@/routes/hwms/QualityFeedbackPlatform/FeedbackPlatformList',
        },
        {
          path: '/hwms/market-quality/quality-feedback-platform/dist/:id',
          component: '@/routes/hwms/QualityFeedbackPlatform/FeedbackPlatformDist',
        },
      ],
    },
    // 一次解析责任判定
    {
      path: '/hwms/market-quality/liability-judgment',
      routes: [
        {
          path: '/hwms/market-quality/liability-judgment/list',
          component: '@/routes/hwms/LiabilityJudgment/List',
        },
        {
          path: '/hwms/market-quality/liability-judgment/:id',
          component: '@/routes/hwms/LiabilityJudgment/Detail',
        },
      ],
    },
    // 市场活动评估
    {
      path: '/hwms/market-quality/market-estimate-doc',
      routes: [
        {
          path: '/hwms/market-quality/market-estimate-doc/list',
          component: '@/routes/hwms/MarketEstimateDoc/MarketEstimateList',
        },
        {
          path: '/hwms/market-quality/market-estimate-doc/dist/:id',
          component: '@/routes/hwms/MarketEstimateDoc/MarketEstimateDist',
        },
      ],
    },
    // 市场活动管理
    {
      path: '/hwms/market-quality/market-activity-doc',
      routes: [
        {
          path: '/hwms/market-quality/market-activity-doc/list',
          component: '@/routes/hwms/MarketActivityDoc/MarketActivityList',
        },
        {
          path: '/hwms/market-quality/market-activity-doc/dist/:id',
          component: '@/routes/hwms/MarketActivityDoc/MarketActivityDist',
        },
        {
          path: '/pub/hwms/market-quality/market-activity-doc/dist/:id',
          component: '@/routes/hwms/MarketActivityDoc/MarketActivityDist',
        },
      ],
    },
    // 索赔管理
    {
      path: '/hwms/market-quality/claim-doc-management',
      routes: [
        {
          path: '/hwms/market-quality/claim-doc-management/list',
          component: '@/routes/hwms/ClaimDocManagement/ClaimDocList',
        },
        {
          path: '/hwms/market-quality/claim-doc-management/dist/:id',
          component: '@/routes/hwms/ClaimDocManagement/ClaimDocDist',
        },
      ],
    },
    // 原因分类维护
    {
      path: '/hwms/market-quality/reason-classify-maintain',
      routes: [
        {
          path: '/hwms/market-quality/reason-classify-maintain/list',
          component: '@/routes/hwms/ReasonClassifyMaintain/ReasonClassifyList',
        },
      ],
    },
    // 旧件台账管理
    {
      path: '/hwms/used-parts-management',
      component: '@/routes/hwms/UsedPartsManagement',
    },
    // 检验单维护
    {
      path: '/hwms/inspect-doc-maintain',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspect-doc-maintain/list',
          priority: 10,
          component: '@/routes/InspectDocMaintain/InspectDocList',
        },
        {
          path: '/hwms/inspect-doc-maintain/dist/:id',
          priority: 10,
          component: '@/routes/InspectDocMaintain/InspectDocDetail',
        },
      ],
    },
    // 检验单查询（对外）
    {
      path: '/hwms/check-list-query-external',
      authorized: true,
      routes: [
        {
          path: '/hwms/check-list-query-external/list',
          authorized: true,
          component: '@/routes/CheckListQueryExternal/CheckListQueryExternalList',
        },
        {
          path: '/hwms/check-list-query-external/dist/:id',
          authorized: true,
          component: '@/routes/CheckListQueryExternal/CheckListQueryExternalDetail',
        },
      ],
    },
    // 检验属性维护功能
    {
      path: '/hmes/product/inspection-attributes',
      routes: [
        {
          path: '/hmes/product/inspection-attributes/list',
          component: '@/routes/InspectionAttributes/InspectionAttributesList',
        },
      ],
    },
    // 用户检验权限维护
    {
      path: '/hwms/user-inspect-permission',
      routes: [
        {
          path: '/hwms/user-inspect-permission/list',
          component: '@/routes/UserInspectPermission',
        },
        {
          path: '/hwms/user-inspect-permission/detail/:id/:siteId',
          component: '@/routes/UserInspectPermission/userDetail',
        },
      ],
    },
    // 检验平台仓库
    {
      path: '/hwms/inspection-platform',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-platform/list',
          priority: 10,
          component: '@/routes/InspectionPlatform/InspectionPlatformList',
        },
        {
          path: '/hwms/inspection-platform/dist/:id',
          priority: 10,
          component: '@/routes/InspectionPlatform/InspectionPlatformDetail',
        },
        {
          path: '/hwms/inspection-platform/inspect-doc/:id',
          priority: 10,
          component: '@/routes/InspectDocMaintain/InspectDocDetail',
        },
        {
          path: '/hwms/inspection-platform/:code',
          priority: 10,
          component: '@/routes/InspectionPlatform/InspectionPlatformList/CommentImport',
        },
      ],
    },
    // 检验平台车间
    {
      path: '/hwms/inspection-platform-workshop',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-platform-workshop/list',
          priority: 10,
          component: '@/routes/InspectionPlatformWorkshop/InspectionPlatformList',
        },
        {
          path: '/hwms/inspection-platform-workshop/dist/:id',
          priority: 10,
          component: '@/routes/InspectionPlatformWorkshop/InspectionPlatformDetail',
        },
        {
          path: '/hwms/inspection-platform-workshop/inspect-doc/:id',
          priority: 10,
          component: '@/routes/InspectDocMaintain/InspectDocDetail',
        },
        {
          path: '/hwms/inspection-platform-workshop/:code',
          priority: 10,
          component: '@/routes/InspectionPlatformWorkshop/InspectionPlatformList/CommentImport',
        },
      ],
    },
    // 不良记录单管理平台
    {
      path: '/hwms/ncReport-doc-maintain',
      routes: [
        {
          path: '/hwms/ncReport-doc-maintain/list',
          component: '@/routes/NcReportDocMaintain/NcReportList',
        },
        {
          path: '/hwms/ncReport-doc-maintain/dist/:id',
          component: '@/routes/NcReportDocMaintain/NcReportDetail',
        },
      ],
    },
    // 检验项目维护
    {
      path: '/hwms/inspect-item-maintain',
      routes: [
        {
          path: '/hwms/inspect-item-maintain/list',
          component: '@/routes/InspectItemMaintain/InspectItemList',
        },
        {
          path: '/hwms/inspect-item-maintain/dist/:id',
          component: '@/routes/InspectItemMaintain/InspectItemDetail',
        },
      ],
    },
    // 检验项目组维护
    {
      path: '/hwms/inspect-group-maintenance',
      routes: [
        {
          path: '/hwms/inspect-group-maintenance/list',
          component: '@/routes/InspectGroupMaintenance/InspectGroupList',
        },
        {
          path: '/hwms/inspect-group-maintenance/dist/:id',
          component: '@/routes/InspectGroupMaintenance/InspectGroupDetail',
        },
      ],
    },
    // 检验方案维护
    {
      path: '/hwms/inspection-scheme-maintenance',
      routes: [
        {
          path: '/hwms/inspection-scheme-maintenance/list',
          component: '@/routes/InspectionScheme/InspectionSchemeList',
        },
        {
          path: '/hwms/inspection-scheme-maintenance/detail/:id',
          component: '@/routes/InspectionScheme/InspectionSchemeDetail',
        },
      ],
    },
    // 检验方案模板维护
    {
      path: '/hwms/inspection-scheme-template-maintenance',
      priority: 1000,
      routes: [
        {
          path: '/hwms/inspection-scheme-template-maintenance/list',
          component: '@/routes/InspectionSchemeTemplate/InspectionSchemeTemplateList',
          priority: 1000,
        },
        {
          path: '/hwms/inspection-scheme-template-maintenance/detail/:id',
          component: '@/routes/InspectionSchemeTemplate/InspectionSchemeTemplateDetail',
          priority: 1000,
        },
      ],
    },
    // 报检信息管理平台
    {
      path: '/hwms/inspection-info-management',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-info-management/list',
          component: '@/routes/InspectionInfoManagement/List',
          priority: 10,
        },
      ],
    },
    // OOC统计报表
    {
      path: '/hspc/ooc-report',
      priority: 999,
      component: '@/routes/OocReport/OocReportList',
    },

    // 生产指令管理
    {
      path: '/hmes/workshop/production-order-mgt',
      priority: 10,
      routes: [
        {
          path: '/hmes/workshop/production-order-mgt/list',
          priority: 10,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtList',
        },
        {
          path: '/hmes/workshop/production-order-mgt/detail/:id',
          priority: 10,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtDetail',
        },
      ],
    },

    // 销售订单管理(重构)
    {
      path: '/hmes/so-delivery/sell-order-manage-standard',
      priority: 10,
      routes: [
        {
          path: '/hmes/so-delivery/sell-order-manage-standard/list',
          component: '@/routes/soDelivery/SellOrderStandard',
          priority: 10,
        },
        {
          path: '/hmes/so-delivery/sell-order-manage-standard/detail/:id',
          component: '@/routes/soDelivery/SellOrderStandard/SellOrderDetail',
        },
      ],
    },

    // 处置方法维护
    {
      path: '/hmes/bad/disposition-method',
      priority: 999,
      component: '@/routes/DispositionMethod/DispositionMethodList',
    },

    // 站点维护
    {
      path: '/hmes/organization-modeling/site',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/site/list',
          component: '@/routes/org/Site/SiteList',
          priority: 10,
        },
        {
          path: '/hmes/organization-modeling/site/detail/:siteId',
          component: '@/routes/org/Site/SiteDetail',
          priority: 10,
        },
      ],
    },
    // 库位维护
    {
      path: '/hmes/organization-modeling/locator',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/locator/list',
          component: '@/routes/org/Locator/LocatorList',
          priority: 10,
        },
        {
          path: '/hmes/organization-modeling/locator/detail/:locatorId',
          component: '@/routes/org/Locator/LocatorDetail',
          priority: 10,
        },
      ],
    },
    // 组织关系维护
    {
      path: '/hmes/organization-modeling/relation-maintenance',
      component: '@/routes/org/RelationMaintain',
      model: '@/models/org/relationMaintain',
      priority: 10,
    },
    // 打印模板配置
    {
      path: '/hmes/print-template-configuration',
      authorized: true,
      routes: [
        {
          path: '/hmes/print-template-configuration/list',
          component: '@/routes/PrintTemplateConfiguration/index.js',
          authorized: true,
        },
        {
          path: '/hmes/print-template-configuration/detail/:templateId',
          component: '@/routes/PrintTemplateConfiguration/detail.js',
          authorized: true,
        },
      ],
    },

    // 杂项工作台(重构版)
    {
      path: '/hwms/in-library/miscellaneous-standard',
      routes: [
        {
          path: '/hwms/in-library/miscellaneous-standard/list',
          component: '@/routes/inLibrary/miscellaneousStandard',
        },
        {
          path: '/hwms/in-library/miscellaneous-standard/detail/:id',
          component: '@/routes/inLibrary/miscellaneousStandard/Detail',
        },
      ],
    },

    // 采购订单管理(重构版)
    {
      path: '/hmes/purchase/order-management-standard',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/order-management-standard/list',
          component: '@/routes/purchase/OrderStandard',
          priority: 10,
        },
      ],
    },

    // 库存调拨平台(重构版)
    {
      path: '/hwms/in-library/send-receive-doc-standard',
      routes: [
        {
          path: '/hwms/in-library/send-receive-doc-standard/list',
          component: '@/routes/inLibrary/SendReceiveDocStandard/SendReceiveList',
        },
        {
          path: '/hwms/in-library/send-receive-doc-standard/detail/:id',
          component: '@/routes/inLibrary/SendReceiveDocStandard/SendReceiveDetail',
        },
      ],
    },

    // 外协管理平台
    {
      path: '/hmes/purchase/outsourcing-manage',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/outsourcing-manage/list',
          component: '@/routes/purchase/Outsourcing/OutsourcingList/index',
          priority: 10,
        },
        // 创建外协发料单
        {
          path: '/hmes/purchase/outsourcing-manage/outsourcing-doc/create',
          component: '@/routes/purchase/Outsourcing/OrderCreate',
          priority: 10,
        },
        // 创建外协退料单
        {
          path: '/hmes/purchase/outsourcing-manage/outsourcing-returns/create',
          component: '@/routes/purchase/Outsourcing/OutsourcingReturn/index',
          priority: 10,
        },
        // 创建外协补料单
        {
          path: '/hmes/purchase/outsourcing-manage/supplement/:id',
          component: '@/routes/purchase/Outsourcing/SupplementBill/index',
          priority: 10,
        },
      ],
    },

    // 外协管理平台(重构版)
    {
      path: '/hmes/purchase/outsourcing-manage-standard',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/outsourcing-manage-standard/list',
          component: '@/routes/purchase/OutsourcingStandard/OutsourcingList/index',
          priority: 10,
        },
        // 创建外协发料单
        {
          path: '/hmes/purchase/outsourcing-manage-standard/outsourcing-doc/create',
          component: '@/routes/purchase/OutsourcingStandard/OrderCreate',
          priority: 10,
        },
        // 创建外协退料单
        {
          path: '/hmes/purchase/outsourcing-manage-standard/outsourcing-returns/create',
          component: '@/routes/purchase/OutsourcingStandard/OutsourcingReturn/index',
          priority: 10,
        },
        // 创建外协补料单
        {
          path: '/hmes/purchase/outsourcing-manage-standard/supplement/:id',
          component: '@/routes/purchase/OutsourcingStandard/SupplementBill/index',
          priority: 10,
        },
      ],
    },

    // 送货单管理(重构版)
    {
      path: '/hmes/purchase/delivery-management-standard',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/delivery-management-standard/list',
          component: '@/routes/purchase/DeliveryStandard',
          priority: 10,
        },
        {
          path: '/hmes/purchase/delivery-management-standard/detail/:id',
          component: '@/routes/purchase/DeliveryStandard/Detail',
          priority: 10,
        },
      ],
    },

    // 事件查询
    {
      path: '/hmes/event/quality/event/query',
      component: '@/routes/qualityEvent/EventQuery',
    },
    // 事件请求类型维护
    {
      path: '/hmes/event/quality/event-request-type',
      component: '@/routes/qualityEvent/EventRequestTypeDemo',
    },
    // 事件对象类型维护
    {
      path: '/hmes/event/quality/object-type',
      component: '@/routes/qualityEvent/ObjectTypeNew',
    },
    // 事件类型维护
    {
      path: '/hmes/event/quality/event-type',
      component: '@/routes/qualityEvent/EventType',
    },

    // 销售发运平台
    {
      path: '/hwms/so-delivery/so-delivery-platform-standard',
      routes: [
        {
          path: '/hwms/so-delivery/so-delivery-platform-standard/list',
          component: '@/routes/soDelivery/SoDeliveryPlatformStandard/SoDeliveryList',
        },
        {
          path: '/hwms/so-delivery/so-delivery-platform-standard/detail/:id',
          component: '@/routes/soDelivery/SoDeliveryPlatformStandard/SoDeliveryDetail',
        },
      ],
    },

    // 领退料平台(重构版)
    {
      path: '/hwms/receive/receive-return-standard',
      routes: [
        {
          path: '/hwms/receive/receive-return-standard/list',
          component: '@/routes/receive/ReceiveReturnStandard',
        },
        {
          path: '/hwms/receive/receive-return-standard/detail/:id/:docType/:docTypeTag',
          component: '@/routes/receive/ReceiveReturnStandard/Detail',
        },
      ],
    },

    // 用户权限维护
    {
      path: '/hmes/mes/user-rights',
      priority: 10,
      component: '@/routes/hmes/UserRights',
    },
    // 质量追溯报表
    {
      path: '/hmes/quality-traceability-report',
      component: '@/routes/QualityTraceabilityReport',
    },

    // 员工上下岗
    {
      path: '/group/employee-clock',
      priority: 999,
      routes: [
        {
          path: '/group/employee-clock/list',
          component: '@/routes/group/EmployeeClock/EmployeeClockList',
          priority: 999,
        },
      ],
    },
    // 检验严格度管理
    {
      path: '/hmes/management-of-inspection-strictness-status',
      component: '@/routes/ManagementOfInspectionStrictnessStatus',
    },
    // 消息处理查询
    {
      path: '/message/message-processing',
      title: '消息处理查询',
      component: '@/routes/message/MessageProcessing',
    },
    // 检验追溯报表
    {
      path: '/hwms/inspect-trace-report',
      title: '检验追溯报表',
      authorized: true,
      component: '@/routes/InspectTraceReport/InspectTraceList',
    },
    // Bartender模版维护
    {
      path: '/hmes/bartender-template-maintain',
      title: 'Bartender模版维护',
      component: '@/routes/BartenderTemplateMaintain/List',
    },
    // 指令模版维护
    {
      path: '/hmes/print-instruction-maintain',
      title: '指令模版维护',
      component: '@/routes/PrintInstructionMaintain/List',
    },
    // 抽样方式维护
    {
      path: '/sampling/sampling-method/sampling-mode',
      routes: [
        {
          path: '/sampling/sampling-method/sampling-mode/list',
          component: '@/routes/samplingMethod/SamplingMode/SamplingModeList',
        },
        {
          path: '/sampling/sampling-method/sampling-mode/detail/:id',
          component: '@/routes/samplingMethod/SamplingMode/SamplingModeDetail',
        },
      ],
    },

    // 报工事物报表平台
    {
      path: '/hmes/work-transaction-report/platform',
      component: '@/routes/transactionReport/WorkTransactionReportPlatform',
    },

    // 报工事物明细报表
    {
      path: '/hmes/work-transaction-report/transaction-detail-report',
      component: '@/routes/transactionReport/WorkTransactionDetailReport',
    },

    // 报工事件明细报表
    {
      path: '/hmes/work-transaction-report/mobile-event-detail-report',
      component: '@/routes/transactionReport/WorkMobileEventDetailReport',
    },

    // 注液性能数据报表
    {
      path: '/hmes/report/injection-performance-data-report',
      component: '@/routes/report/InjectionPerformanceDataReport',
    },

    {
      path: '/hspc/cpk-comparison-chart',
      component: '@/routes/hspc/CPKComparisonChart',
    },
    {
      path: '/hspc/cpk-trend-chart',
      component: '@/routes/hspc/CPKTrendChart',
    },
    {
      path: '/hspc/control-chart-process-object',
      routes: [
        {
          path: '/hspc/control-chart-process-object/list',
          component: '@/routes/hspc/ControlChartProcessObject',
          authoried: true,
          title: '控制图过程对象维护-列表页',
        },
      ],
    },
    // 对象数据收集组管关系查询
    {
      path: '/hmes/acquisition/tag-group-object',
      routes: [
        {
          path: '/hmes/acquisition/tag-group-object/list',
          component: '@/routes/TagGroupObjectQuery/TagGroupObjectList',
        },
      ],
    },

    // =======车间执行=====start=====车间执行=====start=====车间执行=====start=====车间执行=====start=====车间执行====start====

    // 产品加工履历查询报表
    {
      path: '/hmes/product-processing-history-query-report',
      component: '@/routes/ProductProcessingHistoryQueryReport',
    },

    // 设备加工参数查询报表
    {
      path: '/hmes/product-processing-parameter-report-query',
      component: '@/routes/ProductProcessingParameterReportQuery',
    },

    // 产品结果参数补录
    {
      path: '/hmes/product-processing-parameters-supplement',
      routes: [
        {
          path: '/hmes/product-processing-parameters-supplement/list',
          component: '../routes/ProductProcessingParametersSupplement',
        },
      ],
    },
    {
      path: '/hwms/supplier-inspection-percent/list',
      component: '@/routes/SupplierInspectionPercent',
    },

    // 入库检验合格率报表
    {
      path: '/hmes/receipt-inspection-percent/list',
      component: '@/routes/ReceiptInspectionPercent',
    },

    // 设备OEE报表
    {
      path: '/hmes/equipment-oee-report/list',
      component: '@/routes/EquipmentOeeReport',
    },

    // 产品质检记录
    {
      path: '/hmes/product-quality-inspection-record',
      routes: [
        {
          path: '/hmes/product-quality-inspection-record/list',
          component: '@/routes/ProductQualityInspectionRecord',
        },
        {
          path: '/hmes/product-quality-inspection-record/detail/:id',
          component: '@/routes/ProductQualityInspectionRecord/disposeDetail',
        },
      ],
    },

    // 产品工艺保质期维护
    {
      path: '/hmes/product-process-shelf-maintenance',
      routes: [
        {
          path: '/hmes/product-process-shelf-maintenance/list',
          component: '../routes/ProductProcessShelfMaintenance',
        },
      ],
    },

    // 产品加工参数查询报表
    {
      path: '/hmes/product-processing-parameter-query',
      component: '@/routes/ProductProcessingParameterQuery',
    },

    // 生产工艺折算系数维护
    {
      path: '/hmes/production-process-conversion',
      routes: [
        {
          path: '/hmes/production-process-conversion/list',
          component: '../routes/ProductionProcessConversion',
        },
      ],
    },

    // 产品降级记录查询
    {
      path: '/hmes/query-product-degradation-records',
      component: '@/routes/QueryProductDegradationRecords',
    },

    // 产品返修记录查询
    {
      path: '/hmes/query-product-rework-records',
      component: '@/routes/QueryProductReworkRecords',
    },

    // 原材料条码追溯报表
    {
      path: '/hmes/raw-material-barcode-traceability-report',
      component: '@/routes/RawMaterialBarcodeTraceabilityReport',
    },

    // 返修工单条码绑定
    {
      path: '/hmes/rebate-workOrder-barcode-binding',
      routes: [
        {
          path: '/hmes/rebate-workOrder-barcode-binding/list',
          component: '@/routes/RebateWorkOrderBarcodeBinding',
        },
      ],
    },

    // // 返修工单条码绑定-OA
    // {
    //   path: '/pub/hmes/rebate-workOrder-barcode-binding-oa',
    //   routes: [
    //     {
    //       title: '返修工单条码绑定',
    //       path: `/pub/hmes/rebate-workOrder-barcode-binding-oa/list/`,
    //       component: '@/routes/RebateWorkOrderBarcodeBindingOA',
    //     },
    //     {
    //       title: '返修工单条码绑定',
    //       path: `/pub/hmes/rebate-workOrder-barcode-binding-oa/list/:id`,
    //       component: '@/routes/RebateWorkOrderBarcodeBindingOA',
    //     },
    //   ],
    // },

    // 部分报废条码生成-MES
    {
      path: '/hmes/scrap-barcode-generation',
      component: '@/routes/ScrapBarcodeGeneration',
    },

    // 产品报废记录查询报表
    {
      path: '/hmes/product-scrap-record-query',
      component: '@/routes/ProductScrapRecordQuery',
    },

    // 发货报告打印项目维护
    {
      path: '/hmes/shipment-report-print-maintenance',
      routes: [
        {
          path: '/hmes/shipment-report-print-maintenance/list',
          component: '../routes/ShipmentReportPrintMaintenance',
        },
      ],
    },

    // 安灯监控看板
    {
      path: '/hmes/andeng-monitoring-signboard',
      component: '@/routes/AndengMonitoringSignboard',
    },

    // 仓库电芯查询
    {
      path: '/hmes/warehouse-cell-query',
      component: '@/routes/WarehouseCellQuery',
    },

    // 在制品导入
    {
      path: '/hmes/wip-import',
      component: '../routes/WipImport',
    },

    // 工单管理平台
    {
      path: '/hmes/workorder-management-platform',
      routes: [
        {
          path: '/hmes/workorder-management-platform/list',
          component: '../routes/WorkOrderManagementPlatform',
        },
      ],
    },

    // 订单确认
    {
      path: '/hmes/work-order-confirmation',
      component: '../routes/WorkOrderConfirmation',
    },

    // 进出站信息补录
    {
      path: '/hmes/additional-information-recording',
      routes: [
        {
          path: '/hmes/additional-information-recording/list',
          component: '../routes/AdditionalInformationRecording',
        },
      ],
    },

    // 浆料队列调整
    {
      path: '/hmes/adjustment-slurry-queue',
      routes: [
        {
          path: '/hmes/adjustment-slurry-queue/list',
          component: '../routes/AdjustmentSlurryQueue',
        },
      ],
    },

    // 装配记录查询
    {
      path: '/hmes/assembly-record-query',
      component: '@/routes/AssemblyRecordQuery',
    },

    // 班组设备关联关系
    {
      path: '/hmes/team-equipment-association-relationship',
      component: '@/routes/TeamEquipmentAssociationRelationship',
    },

    // 不良记录创建-MES
    {
      path: '/hmes/bad-record/platform-new',
      routes: [
        {
          path: '/hmes/bad-record/platform-new/list',
          component: '@/routes/badRecordCreate/Platform/List',
        },
        {
          path: '/hmes/bad-record/platform-new/detail/:id',
          component: '@/routes/badRecordCreate/Platform/Detail',
        },
        {
          path: '/hmes/bad-record/platform-new/print/:id',
          component: '@/routes/badRecordCreate/Platform/Print',
        },
      ],
    },

    // 条码标记绑定
    {
      path: '/hmes/barcode-marking-binding',
      component: '@/routes/BarcodeMarkingBinding',
    },

    // 物料批工位关系报表
    {
      path: '/hmes/barcode-workstation-binding-report',
      component: '../routes/BarcodeWorkstationBinding',
    },

    // 批量追溯报表（自制件）
    {
      path: '/hmes/batch-barcode-traceability-report',
      component: '@/routes/BatchBarcodeTraceabilityReport',
    },

    // 封装码与胶带码关系
    {
      path: '/hmes/blue-glue-code-national-standard-code',
      component: '@/routes/BlueGlueCodeNationalStandardCode',
    },

    // 电芯分选等级配置
    {
      path: '/hmes/cell-sorting-level-configuration',
      routes: [
        {
          path: '/hmes/cell-sorting-level-configuration/list',
          component: '../routes/CellSortingLevelConfiguration',
        },
        {
          path: '/hmes/cell-sorting-level-configuration/detail/:id',
          component: '../routes/CellSortingLevelConfiguration/Detail',
        },
      ],
    },

    // 变化点管理
    {
      path: '/hmes/change-point-management',
      component: '@/routes/ChangePointManagement',
    },

    // 班组工时确认
    {
      path: '/hmes/confirmation-team-working-hours',
      routes: [
        {
          path: '/hmes/confirmation-team-working-hours/list',
          component: '@/routes/ConfirmationTeamWorkingHours',
        },
        {
          path: '/hmes/confirmation-team-working-hours/detail/:id',
          component: '@/routes/ConfirmationTeamWorkingHours/Detail',
        },
      ],
    },

    // 生产日报
    {
      path: '/hmes/daily-production-report',
      component: '@/routes/DailyProductionReport',
    },

    // 设备接口调用记录
    {
      path: '/hmes/device-interface-call-record',
      routes: [
        {
          path: '/hmes/device-interface-call-record/list',
          component: '../routes/DeviceInterface',
        },
      ],
    },

    // 设备接口调用记录-MOM
    {
      path: '/hmes/device-interface-call-mom',
      routes: [
        {
          path: '/hmes/device-interface-call-mom/list',
          component: '../routes/DeviceInterfaceMom',
        },
      ],
    },

    // 设备查询
    {
      path: '/hmes/device-query',
      component: '@/routes/DeviceQuery',
    },

    // 异常品处置
    {
      path: '/hmes/disposal-abnormal-products',
      component: '@/routes/DisposalAbnormalProducts',
    },

    // 设备状态监控报表
    {
      path: '/hmes/equipment-condition-monitoring-report',
      component: '@/routes/EquipmentConditionMonitoringReport',
    },

    // 装配组维护
    {
      path: '/hmes/equipment-group-maintenance',
      routes: [
        {
          path: '/hmes/equipment-group-maintenance/list',
          component: '@/routes/EquipmentGroupMaintenance',
        },
        {
          path: '/hmes/equipment-group-maintenance/:id',
          component: '@/routes/EquipmentGroupMaintenance/Create',
        },
      ],
    },

    // 装配点维护
    {
      path: '/hmes/equipment-point-maintenance',
      routes: [
        {
          path: '/hmes/equipment-point-maintenance/list',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceList',
        },
        {
          path: '/hmes/equipment-point-maintenance/detail/:id',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceDetail',
        },
      ],
    },

    // 投料罐容量调整
    {
      path: '/hmes/feed-tank-capacity-adjustment',
      routes: [
        {
          path: '/hmes/feed-tank-capacity-adjustment/list',
          component: '../routes/FeedTankCapacityAdjustment',
        },
      ],
    },

    // 物料防呆防错配置
    {
      path: '/hmes/material-prevent-error',
      routes: [
        {
          path: '/hmes/material-prevent-error/list',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList',
        },
        {
          path: '/hmes/material-prevent-error/detail/:id',
          component: '../routes/MaterialPreventError/MaterialPreventErrorDetail',
        },
        {
          path: '/hmes/material-prevent-error/import/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
        },
      ],
    },

    // 进出站信息补录平台
    {
      path: '/hmes/information-supplementary-recording',
      component: '../routes/InformationSupplementaryRecording',
    },

    // 标记维护
    {
      path: '/hmes/mark-maintenance',
      routes: [
        {
          path: '/hmes/mark-maintenance/list',
          component: '../routes/MarkMaintenance/MarkMaintenanceList',
        },
        {
          path: '/hmes/mark-maintenance/detail/:id',
          component: '../routes/MarkMaintenance/MarkMaintenanceDetail',
        },
      ],
    },

    // // 标记维护-OA
    // {
    //   path: '/public/hmes/mark-maintenance/:code',
    //   component: '../routes/MarkMaintenance/MarkMaintenanceOA',
    //   authorized: true,
    // },

    // 国标码编码规则维护
    {
      path: '/hmes/national-coding-maintenance',
      // authorized: true,
      routes: [
        {
          title: '国标码编码规则维护',
          path: '/hmes/national-coding-maintenance/list',
          component: '../routes/NationalCodingMaintenance',
        },
        {
          path: '/hmes/national-coding-maintenance/:id',
          component: '../routes/NationalCodingMaintenance/detail',
        },
      ],
    },

    // 不良记录查询
    {
      path: '/hmes/nc-query-record',
      component: '@/routes/NcRecordQuery',
    },

    // 一件追溯报表
    {
      path: '/hmes/one-trace-report',
      component: '@/routes/OneTraceReport',
    },

    // 工序反馈参数项配置
    {
      path: '/hmes/process-feedback-configuration',
      routes: [
        {
          path: '/hmes/process-feedback-configuration/list',
          component: '../routes/ProcessFeedbackConfiguration',
        },
      ],
    },

    // 加工控制配置维护
    {
      path: '/hmes/processing-control-maintenance',
      routes: [
        {
          path: '/hmes/processing-control-maintenance/list',
          component: '../routes/ProcessingControlMaintenance',
        },
      ],
    },

    // 产品批量工序撤销
    {
      path: '/hmes/product-batch-process-cancellation',
      component: '../routes/ProductBatchProcessCancellation',
    },

    // 流转卡开卡
    {
      path: '/hmes/circulationCardActivate',
      routes: [
        {
          path: '/hmes/circulationCardActivate/list',
          component: '@/pages/CirculationCardActivate',
        },
        {
          path: '/hmes/circulationCardActivate/detail/:id',
          component: '@/pages/CirculationCardActivate/Detail',
        },
      ],
    },

    // 加工策略配置
    {
      path: '/hmes/processing-strategy-configuration',
      component: '@/routes/ProcessingStrategyConfig',
    },

    // 二次判定采集项维护
    {
      path: '/hmes/quadratic-decision/list',
      component: '@/routes/QuadraticDecision',
    },

    // 条码信息查询报表
    {
      path: '/hmes/barcode-info-query-report',
      routes: [
        {
          path: '/hmes/barcode-info-query-report/list',
          component: '@/routes/BarcodeInfoQueryReport/List',
        },
        {
          path: '/hmes/barcode-info-query-report/advanced-query/list',
          component: '@/routes/BarcodeInfoQueryReport/List/AdvancedQueryList',
        },
      ],
    },

    // 特殊参数查询报表
    {
      path: '/hmes/special-parameter-query',
      component: '@/routes/SpecialParameterQuery',
    },

    // 特殊参数同步
    {
      path: '/hmes/special-parameter-sync',
      component: '@/routes/SpecialParameterSync/List',
    },

    // 复采次数配置
    {
      path: '/hmes/wkc-repro-times',
      routes: [
        {
          path: '/hmes/wkc-repro-times/list',
          component: '@/routes/WkcReproTimes/WkcReproTimesList',
        },
      ],
    },

    // 发货报告打印平台
    {
      path: '/hmes/delivery-report-print',
      routes: [
        {
          path: '/hmes/delivery-report-print/list',
          component: '@/routes/DeliveryReportPrint/DeliveryReportPrintList',
        },
      ],
    },

    // 报检请求管理平台-MES
    {
      path: '/hmes/inspection/inspection-management',
      priority: 10,
      routes: [
        {
          path: '/hmes/inspection/inspection-management/list',
          component: '@/routes/inspection/InspectionManagementMes',
          priority: 10,
        },
      ],
    },

    // 在制品完工
    {
      path: '/hmes/work-in-process-completion',
      routes: [
        {
          path: '/hmes/work-in-process-completion/list',
          component: '@/routes/workInProcessCompletion',
        },
      ],
    },

    // 委外检验数据管理
    {
      path: '/hmes/outsourced-inspection-management',
      authorized: true,
      routes: [
        {
          path: '/hmes/outsourced-inspection-management/list',
          component: '@/routes/OutsourcedInspectionManagement',
          authorized: true,
        },
        {
          path: '/himp/commentImport/:code',
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },

    // 不良记录管理平台-mes
    {
      path: '/hmes/bad-record/platform',
      priority: 10,
      routes: [
        {
          path: '/hmes/bad-record/platform/list',
          component: '@/routes/badRecord/Platform/List',
          priority: 10,
        },
        {
          path: '/hmes/bad-record/platform/detail/:id',
          priority: 10,
          component: '@/routes/badRecord/Platform/Detail',
        },
      ],
    },

    // 数据收集项维护
    {
      path: '/hmes/acquisition/new-data-item-new',
      routes: [
        {
          path: '/hmes/acquisition/new-data-item-new/list',
          component: '@/routes/acquisition/NewDataItem',
        },
        {
          path: '/hmes/acquisition/new-data-item-new/detail/:id',
          component: '@/routes/acquisition/NewDataItem/Detail',
        },
      ],
    },
    // 数据收集组维护
    {
      path: '/hmes/acquisition/data-collection-new',
      routes: [
        {
          path: '/hmes/acquisition/data-collection-new/list',
          component: '@/routes/acquisition/Collection/CollectionList',
        },
        {
          path: '/hmes/acquisition/data-collection-new/detail/:id',
          component: '@/routes/acquisition/Collection/CollectionDetail',
        },
      ],
    },

    // 在制品报表
    {
      path: '/hmes/workshop/process-report',
      priority: 10,
      component: '@/routes/workshop/ProcessReport',
    },

    // 杂项工作台-MES
    {
      path: '/apex-hmes/in-library/miscellaneous',
      routes: [
        {
          path: '/apex-hmes/in-library/miscellaneous/list',
          component: '@/routes/inLibrary/miscellaneousMes',
        },
        {
          path: '/apex-hmes/in-library/miscellaneous/detail/:id',
          component: '@/routes/inLibrary/miscellaneousMes/Detail',
        },
      ],
    },

    // 成本中心维护-MES
    {
      path: '/apex-hmes/in-library/cost-order-maintain',
      routes: [
        {
          path: '/apex-hmes/in-library/cost-order-maintain/list',
          component: '@/routes/inLibrary/CostOrderMaintainMes/List',
        },
      ],
    },

    // 入库单查询-MES
    {
      path: '/apex-hmes/inbound/inbound-order-query',
      routes: [
        {
          path: '/apex-hmes/inbound/inbound-order-query/list',
          component: '@/routes/inbound/InboundOrderQueryMes/InboundOrderQueryList',
        },
      ],
    },

    // 物料批管理平台-mes
    {
      path: '/apex-hmes/product/material-lot-traceability',
      priority: 10,
      routes: [
        {
          path: '/apex-hmes/product/material-lot-traceability/list/:timer?',
          component: '@/routes/product/MaterialLotTraceMes',
          priority: 10,
        },
        {
          path: '/apex-hmes/product/material-lot-traceability/detail/:id',
          component: '@/routes/product/MaterialLotTraceMes/MaterialLotTraceDetail',
          priority: 10,
        },
        {
          path: '/apex-hmes/product/material-lot-traceability/comment-import/:code',
          priority: 10,
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },

    // 领退料平台-MES
    {
      path: '/apex-hmes/receive/receive-return',
      routes: [
        {
          path: '/apex-hmes/receive/receive-return/list',
          component: '@/routes/receive/ReceiveReturnMes',
        },
        {
          path: '/apex-hmes/receive/receive-return/detail/:id/:docType/:docTypeTag',
          component: '@/routes/receive/ReceiveReturnMes/Detail',
        },
      ],
    },

    // 实物库存初始化-MES
    {
      path: '/apex-hmes/inventory/initial',
      priority: 10,
      component: '@/routes/inventory/InventoryInitialMes',
    },

    // 库存调拨平台-mes
    {
      path: '/apex-hmes/in-library/send-receive-doc-new',
      routes: [
        {
          path: '/apex-hmes/in-library/send-receive-doc-new/list',
          component: '@/routes/inLibrary/SendReceiveDocMes/SendReceiveList',
        },
        {
          path: '/apex-hmes/in-library/send-receive-doc-new/detail/:id',
          component: '@/routes/inLibrary/SendReceiveDocMes/SendReceiveDetail',
        },
      ],
    },

    // 盘点工作台-mes
    {
      path: '/apex-hmes/inventory/inventory-workbench',
      routes: [
        {
          path: '/apex-hmes/inventory/inventory-workbench/list',
          component: '@/routes/stocktake/StocktakeWorkbenchMes/StocktakeWorkbenchList',
        },
        {
          path: '/apex-hmes/inventory/inventory-workbench/detail/:id',
          component: '@/routes/stocktake/StocktakeWorkbenchMes/StocktakeWorkbenchDetail',
        },
      ],
    },

    // 库存查询-mes
    {
      path: '/apex-hmes/inventory/query/:timer?',
      component: '@/routes/QueryMes/QueryList',
    },

    // 容器管理平台-MES
    {
      path: '/apex-hmes/product/container-management-platform',
      routes: [
        {
          path: '/apex-hmes/product/container-management-platform/list',
          component: '@/routes/product/ContainerManagePlatformMes',
        },
      ],
    },

    // 容器类型维护-MES
    {
      path: '/apex-hmes/hagd/container-type',
      routes: [
        {
          path: '/apex-hmes/hagd/container-type/list',
          component: '@/routes/hagd/ContainerMes',
        },
        {
          path: '/apex-hmes/hagd/container-type/detail/:id',
          component: '@/routes/hagd/ContainerMes/ContainerTypeDetail',
        },
      ],
    },

    // 库存日记账查询-MES
    {
      path: '/apex-hmes/inventory/journal/query',
      component: '@/routes/inventory/JournalQueryMes/JournalQueryList',
    },

    // 指令执行规则维护
    {
      path: '/apex-hmes/commonConfig/order-execute-rule',
      routes: [
        {
          path: '/apex-hmes/commonConfig/order-execute-rule/list',
          component: '@/routes/commonConfig/OrderExecuteRuleMes',
        },
        {
          path: '/apex-hmes/commonConfig/order-execute-rule/detail/:id',
          component: '@/routes/commonConfig/OrderExecuteRuleMes/Detail',
        },
      ],
    },

    // 报废单查询-mes
    {
      path: '/apex-hmes/scrap/doc/query',
      routes: [
        {
          path: '/apex-hmes/scrap/doc/query/list',
          component: '@/routes/ScrapDocQueryMes',
        },
        {
          path: '/apex-hmes/scrap/doc/query/detail/:id',
          component: '@/routes/ScrapDocQueryMes/Detail',
        },
      ],
    },

    // 工艺与工作单元维护
    {
      path: '/hmes/process/unit-work',
      priority: 66,
      routes: [
        {
          path: '/hmes/process/unit-work/list',
          component: '@/routes/process/UnitWork',
        },
        {
          path: '/hmes/process/unit-work/comment-import/:code',
          priority: 10,
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },

    // 入库属性维护
    {
      path: '/hmes/mes/receipt-attribute-maintenance',
      component: '@/routes/hmes/ReceiptAttributeMaintenance',
    },

    // 金蝶库存对照报表-MES
    {
      path: '/apex-hmes/report/mes-kingdee-difference',
      component: '@/routes/report/MesKingdeeDifference',
    },

    // 称重性能数据表
    {
      path: '/hmes/report/weighing-performance-data-report',
      component: '@/routes/report/WeighingPerformanceDataReport',
    },

    // 组装拆卸
    {
      path: '/hmes/assembly/and/disassembly',
      component: '@/routes/AssemblyAndDisassembly',
    },

    // 工单报工
    {
      path: '/hmes/work-order-report',
      component: '@/routes/WorkOrderReport',
    },

    // 容器装卸-MES
    {
      path: '/hmes/container-load-and-unload-mes',
      component: '@/routes/ContainerLoadAndUnloadMes',
    },

    // =======车间执行=====end=====车间执行=====end=====车间执行=====end=====车间执行=====end=====车间执行====end====


    // =======QMS看板=====start======QMS看板=====start======QMS看板=====start======QMS看板=====start======QMS看板=====start====
    // 原材料管理看板
    {
      path: '/hmes/income-inspection-management',
      component: '@/routes/IncomeInspectionManagement',
    },
    // 设备状态监控看板
    {
      path: '/hmes/device-status-monitoring-board',
      component: '@/routes/DeviceStatusMonitoringBoard',
    },

    // 设备台账汇总看板equipmentJournalSummaryBoard
    {
      path: '/hmes/equipment-journal-summary-board',
      component: '@/routes/EquipmentJournalSummaryBoard',
    },

    // 设备维保管理看板
    {
      path: '/hmes/equipment-maintenance-management-board',
      component: '@/routes/EquipmentMaintenanceManagementBoard',
    },

    // =======QMS看板=====end=====QMS看板=====end=====QMS看板=====end=====QMS看板=====end=====QMS看板=====end=====QMS看板=====end====


    // =======协同物流=====start=====协同物流=====start=====协同物流=====start=====协同物流=====start=====协同物流====start====

    // 事务类型接口关系
    {
      path: '/hmes/event-type-interface-relationship',
      component: '@/routes/EventTypeInterfaceRelationship/index',
    },

    // 指令执行规则维护
    {
      path: '/hmes/commonConfig/order-execute-rule',
      priority: 10,
      routes: [
        {
          path: '/hmes/commonConfig/order-execute-rule/list',
          component: '@/routes/commonConfig/OrderExecuteRule',
          priority: 10,
        },
        {
          path: '/hmes/commonConfig/order-execute-rule/detail/:id',
          component: '@/routes/commonConfig/OrderExecuteRule/Detail',
          priority: 10,
        },
      ],
    },
    // 指令执行规则维护 new
    {
      path: '/hmes/commonConfig/order-execute-rule-new',
      priority: 10,
      routes: [
        {
          path: '/hmes/commonConfig/order-execute-rule-new/list',
          component: '@/routes/commonConfig/OrderExecuteRuleNew',
          priority: 10,
        },
        {
          path: '/hmes/commonConfig/order-execute-rule-new/detail/:id',
          component: '@/routes/commonConfig/OrderExecuteRuleNew/Detail',
          priority: 10,
        },
      ],
    },

    // 静默期维护
    {
      path: '/hwms/silent-period-maintenance',
      component: '@/routes/SilentPeriodMaintenance',
    },

    // 事件请求类型维护-wms
    {
      path: '/hwms/event/event-request-type',
      priority: 10,
      component: '@/routes/event/EventRequestTypeDemo',
    },

    // 事件类型维护
    {
      path: '/hwms/event/event-type',
      component: '@/routes/event/EventType',
    },

    // 事件对象类型维护
    {
      path: '/hwms/event/object-type',
      component: '@/routes/event/ObjectTypeNew',
    },

    // 事件查询
    {
      path: '/hwms/event/query',
      component: '@/routes/event/EventQuery',
    },

    // 采购订单管理
    {
      path: '/hmes/purchase/order-management',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/order-management/list',
          component: '@/routes/purchase/OrderNew',
          priority: 10,
        },
      ],
    },

    // 采购退货平台
    {
      path: '/hmes/purchase/purchase-return',
      routes: [
        {
          path: '/hmes/purchase/purchase-return/list',
          priority: 10,
          component: '@/routes/PurchaseReturn/PurchaseList',
        },
        {
          path: '/hmes/purchase/purchase-return/detail/:id',
          priority: 10,
          component: '@/routes/PurchaseReturn/PurchaseDetail',
        },
      ],
    },

    // 送货单管理
    {
      path: '/hmes/purchase/delivery-management',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/delivery-management/list',
          component: '@/routes/purchase/Delivery',
          priority: 10,
        },
        {
          path: '/hmes/purchase/delivery-management/detail/:id',
          component: '@/routes/purchase/Delivery/Detail',
          priority: 10,
        },
      ],
    },

    // 成品库存汇总报表
    {
      path: '/hmes/inventory-summary/list',
      component: '@/routes/InventorySummary',
    },

    // 外协管理平台
    {
      path: '/hmes/purchase/outsourcing-manage',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/outsourcing-manage/list',
          component: '@/routes/purchase/Outsourcing/OutsourcingList/index',
          priority: 10,
        },
        // 创建外协发料单
        {
          path: '/hmes/purchase/outsourcing-manage/outsourcing-doc/create',
          component: '@/routes/purchase/Outsourcing/OrderCreate',
          priority: 10,
        },
        // 创建外协退料单
        {
          path: '/hmes/purchase/outsourcing-manage/outsourcing-returns/create',
          component: '@/routes/purchase/Outsourcing/OutsourcingReturn/index',
          priority: 10,
        },
        // 创建外协补料单
        {
          path: '/hmes/purchase/outsourcing-manage/supplement/:id',
          component: '@/routes/purchase/Outsourcing/SupplementBill/index',
          priority: 10,
        },
      ],
    },

    // 领退料平台-WMS
    {
      path: '/hwms/receive/receive-return',
      routes: [
        {
          path: '/hwms/receive/receive-return/list',
          component: '@/routes/receive/ReceiveReturn',
        },
        {
          path: '/hwms/receive/receive-return/detail/:id/:docType/:docTypeTag',
          component: '@/routes/receive/ReceiveReturn/Detail',
        },
      ],
    },

    // 库龄报表
    {
      path: '/hmes/report/inventory-aging',
      priority: 10,
      component: '@/routes/report/InventoryAging',
    },

    // 库存查询-WMS
    {
      path: '/hmes/inventory/query/:timer?',
      priority: 10,
      component: '@/routes/Query/QueryList',
    },

    // 容器管理平台-wms
    {
      path: '/hmes/product/container-management-platform',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/container-management-platform/list',
          component: '@/routes/product/ContainerManagePlatform',
          priority: 10,
        },
      ],
    },
    // 尾料处理平台
    {
      path: '/hwms/tail-processing-platform',
      component: '@/routes/TailProcessingPlatform',
    },
    // 实物库存初始化-WMS
    {
      path: '/hmes/inventory/initial',
      priority: 10,
      component: '@/routes/inventory/InventoryInitial',
    },

    // 库存日记账查询-wms
    {
      path: '/hmes/inventory/journal/query',
      priority: 10,
      component: '@/routes/inventory/JournalQuery/JournalQueryList',
    },

    // 库存预留日记账查询
    {
      path: '/hmes/inventory/reserve/query',
      component: '@/routes/inventory/ReserveQuery/ReserveQueryList',
      priority: 10,
    },

    // 呆滞报表
    {
      path: '/hmes/sluggish-report',
      priority: 10,
      component: '@/routes/sluggishReport/SluggishReport',
    },

    // 销单库存转移
    {
      path: '/hmes/inventory/so-stock-transfer',
      priority: 10,
      component: '@/routes/inventory/SOStockTransfer/SOStockTransferList',
    },

    // 超期报表
    {
      path: '/hmes/report/over-due',
      priority: 10,
      component: '@/routes/report/OverDue',
    },

    // 进销存报表
    {
      path: '/hmes/report/purchase-sale-stock',
      priority: 10,
      component: '@/routes/report/PurchaseSaleStock',
    },

    // 物料批管理平台-WMS
    {
      path: '/hmes/product/material-lot-traceability',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/material-lot-traceability/list/:timer?',
          component: '@/routes/product/MaterialLotTrace',
          priority: 10,
        },
        {
          path: '/hmes/product/material-lot-traceability/detail/:id',
          component: '@/routes/product/MaterialLotTrace/MaterialLotTraceDetail',
          priority: 10,
        },
        {
          path: '/hmes/product/material-lot-traceability/comment-import/:code',
          priority: 10,
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },

    // 库存查询
    {
      path: '/hmes/inventory-query/list',
      component: '@/routes/InventoryQuery',
      authorized: true,
    },

    // 容器类型维护-WMS
    {
      path: '/hmes/hagd/container-type',
      priority: 10,
      routes: [
        {
          path: '/hmes/hagd/container-type/list',
          component: '@/routes/hagd/Container',
          priority: 10,
        },
        {
          path: '/hmes/hagd/container-type/detail/:id',
          component: '@/routes/hagd/Container/ContainerTypeDetail',
          priority: 10,
        },
      ],
    },

    // 盘点工作台-WMS
    {
      path: '/hmes/inventory/inventory-workbench',
      priority: 10,
      routes: [
        {
          path: '/hmes/inventory/inventory-workbench/list',
          component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchList',
          priority: 10,
        },
        {
          path: '/hmes/inventory/inventory-workbench/detail/:id',
          component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchDetail',
          priority: 10,
        },
      ],
    },

    // 事件事务转换关系维护
    {
      path: '/hwms/event/transaction/type/rel-maintenance',
      component: '@/routes/event/TransactionTypeRelMaintenance',
    },

    // 事务报表平台
    {
      path: '/hwms/transaction-report/platform',
      component: '@/routes/transactionReport/TransactionReportPlatform',
    },

    // 事务明细报表
    {
      path: '/hwms/transaction-report/transaction-detail-report',
      component: '@/routes/transactionReport/TransactionDetailReport',
    },

    // 移动事件明细报表
    {
      path: '/hwms/transaction-report/mobile-event-detail-report',
      component: '@/routes/transactionReport/MobileEventDetailReport',
    },

    // 寻址策略
    {
      path: '/hmes/strategy/addressing',
      priority: 1000,
      routes: [
        {
          path: '/hmes/strategy/addressing/list',
          priority: 1000,
          component: '@/routes/strategy/Addressing/StrategyList',
        },
        {
          path: '/hmes/strategy/addressing/detail/:id',
          priority: 1000,
          component: '@/routes/strategy/Addressing/StrategyDetail',
        },
        {
          path: '/hmes/strategy/addressing/distribution/:id',
          priority: 1000,
          component: '@/routes/strategy/Addressing/StrategyDistribution',
        },
      ],
    },

    // 寻址策略分配
    {
      path: '/hmes/strategy/organization-strategy',
      priority: 10,
      routes: [
        {
          path: '/hmes/strategy/organization-strategy/list',
          component: '@/routes/strategy/OrganizationStrategy/OrganizationStrategyList',
        },
      ],
    },

    // 物料库位关系维护
    {
      path: '/hmes/strategy/locator-relation',
      priority: 10,
      routes: [
        {
          path: '/hmes/strategy/locator-relation/list',
          priority: 10,
          component: '@/routes/strategy/LocatorRelation/LocatorRelationList',
        },
        {
          path: '/hmes/strategy/locator-relation/batch-import',
          priority: 10,
          component: '@/routes/strategy/LocatorRelation/BatchImport',
        },
      ],
    },

    // 报检请求管理平台-WMS
    {
      path: '/hmes/inspection/inspection-management-new',
      routes: [
        {
          path: '/hmes/inspection/inspection-management-new/list',
          component: '@/routes/inspection/InspectionManagement',
        },
      ],
    },

    // 成品条码注册
    {
      path: '/hmes/inbound/bar-code-registration',
      priority: 10,
      routes: [
        {
          path: '/hmes/inbound/bar-code-registration/list',
          component: '@/routes/inbound/BarCodeRegistration/RegistrationList',
          priority: 10,
        },
      ],
    },

    // 入库单查询-WMS
    {
      path: '/hmes/inbound/inbound-order-query',
      priority: 44,
      routes: [
        {
          path: '/hmes/inbound/inbound-order-query/list',
          priority: 44,
          component: '@/routes/inbound/InboundOrderQuery/InboundOrderQueryList',
        },
      ],
    },

    // 销售订单管理
    {
      path: '/hmes/so-delivery/sell-order-manage',
      priority: 10,
      routes: [
        {
          path: '/hmes/so-delivery/sell-order-manage/list',
          component: '@/routes/soDelivery/SellOrder',
          priority: 10,
        },
        {
          path: '/hmes/so-delivery/sell-order-manage/detail/:id',
          priority: 10,
          component: '@/routes/soDelivery/SellOrder/SellOrderDetail',
        },
      ],
    },

    // 销售发运平台
    {
      path: '/hwms/so-delivery/so-delivery-platform',
      routes: [
        {
          path: '/hwms/so-delivery/so-delivery-platform/list',
          component: '@/routes/soDelivery/SoDeliveryPlatform/SoDeliveryList',
        },
        {
          path: '/hwms/so-delivery/so-delivery-platform/detail/:id',
          component: '@/routes/soDelivery/SoDeliveryPlatform/SoDeliveryDetail',
        },
      ],
    },

    // 杂项工作台-wms
    {
      path: '/hwms/in-library/miscellaneous',
      routes: [
        {
          path: '/hwms/in-library/miscellaneous/list',
          component: '@/routes/inLibrary/miscellaneous',
        },
        {
          path: '/hwms/in-library/miscellaneous/detail/:id',
          component: '@/routes/inLibrary/miscellaneous/Detail',
        },
      ],
    },

    // 库存调拨平台-WMS
    {
      path: '/hwms/in-library/send-receive-doc-new',
      routes: [
        {
          path: '/hwms/in-library/send-receive-doc-new/list',
          component: '@/routes/inLibrary/SendReceiveDoc/SendReceiveList',
        },
        {
          path: '/hwms/in-library/send-receive-doc-new/detail/:id',
          component: '@/routes/inLibrary/SendReceiveDoc/SendReceiveDetail',
        },
      ],
    },

    // 成本中心维护-WMS
    {
      path: '/hmes/in-library/cost-order-maintain',
      priority: 10,
      routes: [
        {
          path: '/hmes/in-library/cost-order-maintain/list',
          priority: 10,
          component: '@/routes/inLibrary/CostOrderMaintain/List',
        },
      ],
    },

    // 报废单查询-wms
    {
      path: '/hwms/scrap/doc/query',
      routes: [
        {
          path: '/hwms/scrap/doc/query/list',
          component: '@/routes/ScrapDocQuery',
        },
        {
          path: '/hwms/scrap/doc/query/detail/:id',
          component: '@/routes/ScrapDocQuery/Detail',
        },
      ],
    },
    // 班组交接班
    {
      path: '/hmes/team-handover',
      component: '@/routes/TeamHandover',
    },
    {
      path: '/group/group-main',
      priority: 10,
      routes: [
        {
          path: '/group/group-main/list',
          priority: 10,
          component: '@/routes/group/GroupMain',
        },
        {
          path: '/group/group-main/detail/:id',
          priority: 10,
          component: '@/routes/group/GroupMain/Detail',
        },
      ],
    },
    // 配方维护
    {
      path: '/hmes/formula-maintenance',
      component: '@/routes/FormulaMaintenance',
    },
    // 物料加工时长维护
    {
      path: '/hmes/material-processing-time',
      component: '@/routes/MaterialProcessingTime',
    },
    // 分档规则维护
    {
      path: '/hmes/grading-rules',
      routes: [
        {
          path: '/hmes/grading-rules/list',
          component: '@/routes/GradingRules/GradingRulesList',
        },
        {
          path: '/hmes/grading-rules/detail/:id',
          component: '@/routes/GradingRules/GradingRulesDetail',
        },
        {
          path: '/hmes/commentImport/:code',
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },
    // 设备与工作单元维护
    {
      path: '/hmes/equipment/equipment-workcell',
      priority: 10,
      routes: [
        {
          path: '/hmes/equipment/equipment-workcell/list',
          component: '@/routes/equipment/WorkCell',
          priority: 10,
        },
      ],
    },
    {
      path: '/hmes/organization-modeling/work-cell',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/work-cell/list',
          priority: 10,
          component: '@/routes/org/WorkCell/WorkCellList',
        },
        {
          path: '/hmes/organization-modeling/work-cell/detail/:workcellId',
          priority: 10,
          component: '@/routes/org/WorkCell/WorkCellDetail',
        },
        {
          path: '/hmes/commentImport/:code',
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },
    // 容器装置注册
    {
      path: '/hmes/container-load-register',
      routes: [
        {
          path: '/hmes/container-load-register/list',
          priority: 10,
          component: '@/routes/ContainerLoadRegister/ContainerLoadRegisterList',
        },
        {
          path: '/hmes/container-load-register/detail',
          priority: 10,
          component: '@/routes/ContainerLoadRegister/ContainerLoadRegisterDetail',
        },
      ],
    },
    // B品打包装箱
    {
      path: '/hmes/container-load-register-for-b',
      routes: [
        {
          path: '/hmes/container-load-register-for-b/list',
          component: '@/routes/ContainerLoadRegisterForB/ContainerLoadRegisterForBList',
        },
        {
          path: '/hmes/container-load-register-for-b/detail',
          component: '@/routes/ContainerLoadRegisterForB/ContainerLoadRegisterForBDetail',
        },
      ],
    },

    // 原材料报表
    {
      path: '/hwms/raw/material/report',
      component: '@/routes/report/RawMaterialReport/RawMaterialList',
    },

    // 电芯成品报表
    {
      path: '/hwms/cellFinishedProduct/report',
      component: '@/routes/report/CellFinishedProductReport',
    },

    // 出入库明细报表
    {
      path: '/hwms/receipt-issue-details/report',
      component: '@/routes/report/ReceiptIssueDetailsReport',
      authorized: true,
      title: '出入库明细报表',
    },

    // 容器装卸-WMS
    {
      path: '/hwms/container-load-and-unload',
      component: '@/routes/ContainerLoadAndUnload',
    },

    // 单据异步执行信息报表
    {
      path: '/hwms/salesShipmentAsyncExecution/main',
      component: '@/routes/SalesShipmentAsyncExecution',
    },

    // 金蝶库存对照报表
    {
      path: '/hwms/report/wms-kingdee-difference',
      component: '@/routes/report/WmsKingdeeDifference',
    },

    // MES-金蝶ERP入库对比报表
    {
      path: '/hmes/report/erp-kingdee-difference',
      component: '@/routes/report/ErpKingdeeDifference',
    },

    // 电芯出入库台账
    {
      path: '/hwms/battery-cell-issue-receipt-ledger/list',
      component: '@/routes/report/BatteryCellIssueReceiptLedger',
    },

    // 周报数据汇总-报表
    {
      path: '/hwms/report/weekly-report-data-summary',
      component: '@/routes/report/WeeklyReportDataSummary',
    },

    // 制程检验数据查询报表-报表
    {
      path: '/hmes/report/process-inspection-data-query-report',
      component: '@/routes/report/ProcessInspectionDataQueryReport',
    },

    // A品出货计划表
    {
      path: '/hwms/report/product-shipping-schedule',
      component: '@/routes/report/ProductShippingSchedule',
      authorized: true,
    },

    // MT
    // APEX_WMS
    // LOV_CODE_BEFORE
    // WMS_LOV_CODE_BEFORE
    // '@utils/config';
    // '@/utils/config';
    // HMES_BASIC
    // HWMS_BASIC

    // =======协同物流=====end=====协同物流=====end=====协同物流=====end=====协同物流=====end=====协同物流====end====
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
        },
      },
    ],
  ],
});
