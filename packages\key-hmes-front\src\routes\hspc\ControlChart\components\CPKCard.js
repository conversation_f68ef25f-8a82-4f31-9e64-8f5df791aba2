/**
 * @Description: 历史图形查询-过程能力分析卡片
 * @Author: <<EMAIL>>
 * @Date: 2021-06-02 11:32:44
 * @LastEditTime: 2021-12-09 16:01:17
 * @LastEditors: <<EMAIL>>
 */
import React, { Component } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { ExpandCardC7n } from 'hcm-components-front/lib/components/tarzan-ui';
import { CPKCommonDS } from 'hspc-front/lib/stores/CPKDS';
import styles from './index.module.less';
import CpkChart from './CpkChart';

const modelPrompt = 'tarzan.hspc.chartInfo';

const keyCNUS = {
  lowerSpecLimit: intl.get(`${modelPrompt}.lowerSpecLimit`).d('规格下限'),
  specTarget: intl.get(`${modelPrompt}.specTarget`).d('目标值'),
  upperSpecLimit: intl.get(`${modelPrompt}.upperSpecLimit`).d('规格上限'),
  xBar: intl.get(`${modelPrompt}.xBar`).d('样本均值'),
  sampleCount: intl.get(`${modelPrompt}.sampleCount`).d('样本N'),
  entiretySigma: intl.get(`${modelPrompt}.entiretySigma`).d('标准差(整体)'),
  groupSigma: intl.get(`${modelPrompt}.groupSigma`).d('标准差(组内)'),
  pp: intl.get(`${modelPrompt}.pp`).d('PP'),
  ppk: intl.get(`${modelPrompt}.ppk`).d('PPK'),
  ppl: intl.get(`${modelPrompt}.ppl`).d('PPL'),
  ppu: intl.get(`${modelPrompt}.ppu`).d('PPU'),
  cp: intl.get(`${modelPrompt}.cp`).d('CP'),
  cpk: intl.get(`${modelPrompt}.cpk`).d('CPK'),
  cpl: intl.get(`${modelPrompt}.cpl`).d('CPL'),
  cpu: intl.get(`${modelPrompt}.cpu`).d('CPU'),
};

export default class CPKCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      CpkChartData: null,
      cardExpandFlag: false,
    };
    this.ProcessDataDs = new DataSet({
      ...CPKCommonDS(),
    });
    this.TotalCapacityDs = new DataSet({
      ...CPKCommonDS(),
    });
    this.PotentialAbilityDs = new DataSet({
      ...CPKCommonDS(),
    });
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (JSON.stringify(nextProps.dataSource) !== JSON.stringify(this.state.CpkChartData)) {
      this.getCpkData(nextProps.dataSource);
      this.setState({
        CpkChartData: nextProps.dataSource,
      });
    }
  }

  getCpkData = data => {
    if (!data) {
      return;
    }
    const { processInfo, entiretyInfo, potentialInfo } = data;
    if (processInfo) {
      // 过程数据
      const processInfoList = [];
      Object.keys(processInfo).forEach(key => {
        const newObj = {
          name: keyCNUS[key],
          value: processInfo[key] || '*',
        };
        processInfoList.push(newObj);
      })
      this.ProcessDataDs.loadData(processInfoList);
    }
    if (entiretyInfo) {
      // CPK的整体能力
      const entiretyInfoList = [];
      Object.keys(entiretyInfo).forEach(key => {
        const newObj = {
          name: keyCNUS[key],
          value: entiretyInfo[key] || '*',
        };
        entiretyInfoList.push(newObj);
      })
      this.TotalCapacityDs.loadData(entiretyInfoList);
    }
    if (potentialInfo) {
      // 潜在（组内）能力
      const potentialInfoList = [];
      Object.keys(potentialInfo).forEach(key => {
        const newObj = {
          name: keyCNUS[key],
          value: potentialInfo[key] || '*',
        };
        potentialInfoList.push(newObj);
      })
      this.PotentialAbilityDs.loadData(potentialInfoList);
    }
  };

  // 过程能力table
  get processDataDSColumns() {
    const headerStyle = {
      display: 'none',
    };
    return [
      {
        header: intl.get(`${modelPrompt}.processData`).d('过程数据'),
        headerStyle: { height: '30px', lineHeight: '15px', padding: 0 },
        children: [
          {
            header: null,
            name: 'name',
            headerStyle,
            width: '65%',
          },
          {
            header: null,
            name: 'value',
            headerStyle,
            width: '35%',
          },
        ],
      },
    ];
  }

  // 整体能力table
  get totalCapacityDSColumns() {
    const headerStyle = {
      display: 'none',
    };
    return [
      {
        header: intl.get(`${modelPrompt}.entiretyAbility`).d('整体能力'),
        headerStyle: { height: '30px', lineHeight: '15px', padding: 0 },
        children: [
          { header: null, name: 'name', headerStyle, width: '65%' },
          { header: null, name: 'value', headerStyle, width: '35%' },
        ],
      },
    ];
  }

  // CPK潜在（组内）能力
  get potentialAbilityDSColumns() {
    const headerStyle = {
      display: 'none',
    };
    return [
      {
        header: intl.get(`${modelPrompt}.potentialAbility`).d('潜在（组内）能力'),
        headerStyle: { height: '30px', lineHeight: '15px', padding: 0 },
        children: [
          { header: null, name: 'name', headerStyle, width: '65%' },
          { header: null, name: 'value', headerStyle, width: '35%' },
        ],
      },
    ];
  }

  handleChangeCardExpand = () => {
    this.setState({
      cardExpandFlag: !this.state.cardExpandFlag,
    });
  };

  render() {
    const { CpkChartData, cardExpandFlag } = this.state;
    return (
      <div className={styles.cardContainer}>
        <ExpandCardC7n
          showExpandIcon
          title={intl.get(`${modelPrompt}.processCapabilityAnalysis`).d('过程能力分析')}
          expandFlag={cardExpandFlag}
          onChangeExpandFlag={this.handleChangeCardExpand}
        >
          {CpkChartData && CpkChartData.cpkChartInfo && CpkChartData.cpkChartInfo.allValueList && (
            <div className={styles.cpk}>
              <div className={styles.cpkLeft} id='cpkLeft'>
                <CpkChart cpkChartInfo={CpkChartData.cpkChartInfo} />
              </div>
              <div className={styles.cpkRight}>
                <Table
                  rowHeight={19}
                  selectionMode="none"
                  dataSet={this.ProcessDataDs}
                  columns={this.processDataDSColumns}
                  pagination={false}
                />
                <br />
                <Table
                  rowHeight={19}
                  selectionMode="none"
                  dataSet={this.TotalCapacityDs}
                  columns={this.totalCapacityDSColumns}
                  pagination={false}
                />
                <br />
                <Table
                  rowHeight={19}
                  selectionMode="none"
                  dataSet={this.PotentialAbilityDs}
                  columns={this.potentialAbilityDSColumns}
                  pagination={false}
                />
              </div>
            </div>
          )}
        </ExpandCardC7n>
      </div>
    );
  }
}
