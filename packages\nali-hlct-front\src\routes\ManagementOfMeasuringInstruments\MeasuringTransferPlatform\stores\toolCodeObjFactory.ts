import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.qms.MeasuringTransferPlatform.details';

const toolCodeObjFactory = () =>
  new DataSet({
    fields: [
      {
        name: 'toolCodeObjs',
        label: intl.get(`${modelPrompt}.table.toolCodeObjs`).d('量具编号'),
        type: FieldType.object,
        ignore: FieldIgnore.always,
        multiple: true,
        lovCode: 'YP.QIS.MS_TOOL_RESPON_AND_STATUS_LIMIT_LOV',
        dynamicProps: {
          lovPara: ({ dataSet }) => ({
            siteId: dataSet.getState('siteId'),
            tenantId: getCurrentOrganizationId(),
          }),
        },
      },
    ],
  });

export default toolCodeObjFactory;
