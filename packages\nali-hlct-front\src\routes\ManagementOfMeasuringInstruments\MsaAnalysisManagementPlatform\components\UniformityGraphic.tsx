/**
 * @Description: MSA分析管理平台-一致性图表界面
 * @Author: <EMAIL>
 * @Date: 2023/8/24 15:42
 */
import React, { useState, useEffect, useMemo } from 'react';
import { Form, TextField, TextArea } from 'choerodon-ui/pro';
import { Collapse, Row, Col } from 'choerodon-ui';
import intl from 'utils/intl';
import Table from './TableComponent';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const { Panel } = Collapse;

const UniformityGraphic = ({ dataSoure, analyseResultDs }) => {
  const {
    consistenceTableInfo: {
      kappaTableInfo,
      measuredByTableInfo,
      allConsistentPercentStr,
      allConsistentQty,
      measuredByAllConsistentPercentStr,
      measuredByAllConsistentQty,
    },
  } = dataSoure;
  const [userList, setUserList] = useState<any>([]);
  const [dataMap, setDataMap] = useState<any>({});

  useEffect(() => {
    if (!kappaTableInfo?.length && !measuredByTableInfo?.length) {
      return;
    }
    handleInitTable();
  }, [dataSoure?.data, dataSoure?.tableInfo]);

  const handleInitTable = () => {
    const _userList: any = [];
    const table1Data: any = [];
    const table2Data: any = [{}];
    const table3Data: any = [{}, {}];
    const table4Data: any = [{}, {}];
    const table5Data: any = [];

    (measuredByTableInfo || []).forEach(item => {
      const {
        measuredBy,
        measuredByName,
        consistentPercentStr,
        consistentQty,
        kappa,
        leakJudgmentPercentStr,
        measuredByConsistentPercentStr,
        measuredByConsistentQty,
        mistakeJudgmentPercentStr,
      } = item;
      _userList.push({ measuredBy, measuredByName });

      table1Data.push({ rowId: measuredBy, rowName: measuredByName, [measuredBy]: '-' });

      table2Data[0][measuredBy] = kappa;

      table2Data.forEach(item => {
        item.rowName = 'Kappa';
        item[measuredBy] = kappa;
      });

      table3Data.forEach((item, index) => {
        item.rowName = index
          ? intl.get(`${modelPrompt}.consistencyAndValidity`).d('一致性/有效性')
          : intl.get(`${modelPrompt}.consistentQty`).d('一致性零件数量');
        item[measuredBy] = index ? measuredByConsistentPercentStr : measuredByConsistentQty;
        item.all = index ? measuredByAllConsistentPercentStr : measuredByAllConsistentQty;
      });

      table4Data.forEach((item, index) => {
        item.rowName = index
          ? intl.get(`${modelPrompt}.consistencyAndValidity`).d('一致性/有效性')
          : intl.get(`${modelPrompt}.consistentQty`).d('一致性零件数量');
        item[measuredBy] = index ? consistentPercentStr : consistentQty;
        item.all = index ? allConsistentPercentStr : allConsistentQty;
      });

      table5Data.push({
        rowId: measuredBy,
        rowName: measuredByName,
        consistentPercentStr,
        leakJudgmentPercentStr,
        mistakeJudgmentPercentStr,
      });
    });
    (kappaTableInfo || []).forEach((item, index) => {
      const { measuredByColumn, humanKappa } = item;
      const rowIndex = Math.floor(index / (_userList?.length - 1));
      table1Data[rowIndex][measuredByColumn] = humanKappa;
    });

    table5Data.push({
      rowId: 'all',
      rowName: intl.get(`${modelPrompt}.systemConsistent`).d('系统的有效性'),
      consistentPercentStr: allConsistentPercentStr,
      leakJudgmentPercentStr: '-',
      mistakeJudgmentPercentStr: '-',
    });
    setUserList(_userList);
    setDataMap({
      table1Data,
      table2Data,
      table3Data,
      table4Data,
      table5Data,
    });
  };

  const table1Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: 'Kappa',
      },
      ...(userList || []).map(item => ({
        name: item?.measuredBy,
        title: item?.measuredByName,
      })),
    ],
    [userList?.length],
  );

  const table2Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: '',
      },
      ...(userList || []).map(item => ({
        name: item?.measuredBy,
        title: `${item?.measuredByName}${intl.get(`${modelPrompt}.andBasic`).d('与基准')}`,
      })),
    ],
    [userList?.length],
  );

  const table3Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: '',
      },
      ...(userList || []).map(item => ({
        name: item?.measuredBy,
        title: item?.measuredByName,
      })),
      {
        name: 'all',
        title: intl.get(`${modelPrompt}.allPerson`).d('所有评价人皆一致'),
      },
    ],
    [userList?.length],
  );

  const table4Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: '',
      },
      ...(userList || []).map(item => ({
        name: item?.measuredBy,
        title: item?.measuredByName,
      })),
      {
        name: 'all',
        title: intl.get(`${modelPrompt}.allPersonSameWithStandard`).d('所有评价人皆与标准一致'),
      },
    ],
    [userList?.length],
  );

  const table5Column: any = useMemo(
    () => [
      {
        name: 'rowName',
        title: '',
      },
      {
        name: 'consistentPercentStr',
        title: intl.get(`${modelPrompt}.effectiveness`).d('有效性'),
      },
      {
        name: 'leakJudgmentPercentStr',
        title: intl.get(`${modelPrompt}.leakJudgmentPercentStr`).d('漏判率'),
      },
      {
        name: 'mistakeJudgmentPercentStr',
        title: intl.get(`${modelPrompt}.mistakeJudgmentPercentStr`).d('误判率'),
      },
    ],
    [],
  );

  const RenderHelp = () => {
    const info = intl
      .get(`${modelPrompt}.uniformity.help`)
      .d(
        '1、评价人之间的kappa系数、评价人与基准之间的kappa系数>=0.9，良好；0.75<=kappa系数<0.9，条件接受；kappa系数<0.75，不能接受；<br />2、漏报比率<=1%，良好；1%<漏报比率<=5%，条件接受；漏报比率>5%，不能接受；<br />3、误报比率<=2%，良好；2%<漏报比率<=10%，条件接受；漏报比率>10%，不能接受；<br />4、有效性>=95%，良好；85%<=有效性<95%，条件接受；有效性<85%，不能接受；',
      );
    const descriptionList = info.split('<br />');

    const tableData = [
      {
        rowName: intl.get(`${modelPrompt}.cloumn.personKappa`).d('评价人之间的kappa'),
        good: '≥0.9',
        accept: '[0.75, 0.9)',
        reject: '<0.75',
      },
      {
        rowName: intl
          .get(`${modelPrompt}.cloumn.personWithStandardKappa`)
          .d('评价人与基准之间的kappa'),
        good: '≥0.9',
        accept: '[0.75, 0.9)',
        reject: '<0.75',
      },
      {
        rowName: intl.get(`${modelPrompt}.cloumn.missingReportRatio`).d('漏报比率'),
        good: '≤1%',
        accept: '(1%, 5%]',
        reject: '>5%',
      },
      {
        rowName: intl.get(`${modelPrompt}.cloumn.errorReportRatio`).d('误报比率'),
        good: '≤2%',
        accept: '(2%, 10%]',
        reject: '>10%',
      },
      {
        rowName: intl.get(`${modelPrompt}.cloumn.consistent`).d('有效性'),
        good: '≥0.95',
        accept: '[0.85, 0.95)',
        reject: '<0.85',
      },
    ];

    const tableColumn = [
      {
        name: 'rowName',
        title: '',
      },
      {
        name: 'good',
        title: intl.get(`${modelPrompt}.column.good`).d('良好'),
      },
      {
        name: 'accept',
        title: intl.get(`${modelPrompt}.column.accept`).d('条件接受'),
      },
      {
        name: 'reject',
        title: intl.get(`${modelPrompt}.column.reject`).d('不接受'),
      },
    ];
    return (
      <div>
        {descriptionList.map(item => (
          <p>{item}</p>
        ))}
        <Table data={tableData} columns={tableColumn} />
      </div>
    );
  };

  return (
    <div>
      {Boolean(dataSoure?.tableInfo?.length) && (
        <Collapse bordered={false} defaultActiveKey={['analyseContent']}>
          <Panel
            key="analyseContent"
            header={intl.get(`${modelPrompt}.analyseContent`).d('分析内容')}
          >
            <Row>
              <Col span={12}>
                <Table
                  data={dataMap.table1Data}
                  columns={table1Column}
                  title={intl.get(`${modelPrompt}.personKappa`).d('评价人之间的Kappa系数')}
                />
                <Table
                  data={dataMap.table2Data}
                  columns={table2Column}
                  title={intl
                    .get(`${modelPrompt}.personKappaWithStandard`)
                    .d('评价人与基准之间的Kappa系数')}
                />
                <Table
                  data={dataMap.table3Data}
                  columns={table3Column}
                  title={intl.get(`${modelPrompt}.personSelfConsistency`).d('评价人自我一致性')}
                />
                <Table
                  data={dataMap.table4Data}
                  columns={table4Column}
                  title={intl
                    .get(`${modelPrompt}.personSelfConsistencyAndStandard`)
                    .d('评价人自我一致且与基准一致性')}
                />
              </Col>
              <Col span={12}>
                <Table
                  data={dataMap.table5Data}
                  columns={table5Column}
                  title={intl.get(`${modelPrompt}.personAbilitySummarySheet`).d('评价人能力汇总表')}
                />
                <Form labelWidth={100} disabled dataSet={analyseResultDs}>
                  <TextField name="msaResult" help={<RenderHelp />} />
                  <TextArea name="msaConclusion" rows={7} />
                </Form>
              </Col>
            </Row>
          </Panel>
        </Collapse>
      )}
    </div>
  );
};

export default UniformityGraphic;
