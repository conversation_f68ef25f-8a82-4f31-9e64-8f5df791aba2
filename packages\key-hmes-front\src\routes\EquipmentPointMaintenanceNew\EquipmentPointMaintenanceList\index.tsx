import React, { useMemo, useCallback, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores/listPageDS';

const modelPrompt = 'tarzan.hmes.equipmentPointMaintenanceList';

const EquipmentPointMaintenanceList = observer((props) => {
  const {
    match: { path },
    tableDs,
    history,
  } = props;

  useEffect(() => {
    tableDs.query();
  },[])


  const columns: ColumnProps[] = useMemo(() => {
    return [
      // 站点
      {
        name: 'siteCode',
      },
      // 装配点编码
      {
        name: 'assemblePointCode',
        renderer: ({ value, record }) => (<a onClick={() => handleEdit(record?.get('assemblePointId'))}>{value} </a>),
      },
      // 装配点描述
      {
        name: 'description',
      },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
    ];
  }, []);

  const handleEdit = useCallback((id) => {
    history.push(`/hmes/equipment-point-maintenance/detail/${id}`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('装配点维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleEdit('create')}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          style={{height: '400px'}}
          dataSet={tableDs}
          columns={columns}
          searchCode="equipmentPointMaintenanceList"
        />
      </Content>
    </div>
  );
})

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(EquipmentPointMaintenanceList),
);

