/**
 * @Description: 检验项目组维护-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 15:59:30
 * @LastEditTime: 2023-05-18 16:50:58
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

const endUrl = '';

// 详情数据查询
export function FetchInspectGroupDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-group/detail/ui`,
    method: 'GET',
  };
}

// 详情数据保存
export function SaveInspectGroupDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-group/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.RELATION`,
    method: 'POST',
  };
}

// 详情行数据同步
export function SyncInspectItemDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-group/item/sync/ui`,
    method: 'GET',
  };
}
