/**
 * @Description: 体系审核管理维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSet } from 'choerodon-ui/pro';
import { FieldType, FieldIgnore, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.systemAudit';
const tenantId = getCurrentOrganizationId();

// 详情-审核记录表单
const auditRecordFormDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recordCode`).d('记录编号'),
      name: 'recordCode',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      name: 'recordStatus',
      lookupCode: 'YP.QIS.SYS_REVIEW_RECORD_STATUS',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.scheduleCode`).d('日程编号'),
      name: 'scheduleCodeObject',
      textField: 'scheduleCode',
      valueField: 'scheduleId',
      required: true,
      dynamicProps: {
        options: ({ record }) => {
          const reviewScheduleList = record.get('reviewScheduleList');
          return new DataSet({
            data: reviewScheduleList,
            paging: false,
            fields: [
              {
                name: 'scheduleCode',
              },
              {
                name: 'scheduleId',
              },
            ],
          });
        },
      },
    },
    {
      name: 'scheduleId',
      bind: 'scheduleCodeObject.scheduleId',
    },
    {
      name: 'scheduleCode',
      bind: 'scheduleCodeObject.scheduleCode',
    },
    {
      name: 'memberInfo',
      bind: 'scheduleCodeObject.memberInfo',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.group`).d('小组'),
      name: 'groupDescription',
      bind: 'scheduleCodeObject.groupDescription',
      disabled: true,
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.reviewDate`).d('审核日期'),
      name: 'reviewDate',
      required: true,
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.reviewTimeFrom`).d('审核开始时间'),
      name: 'reviewTimeFrom',
      max: 'reviewTimeTo',
      required: true,
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.reviewTimeTo`).d('审核结束时间'),
      name: 'reviewTimeTo',
      min: 'reviewTimeFrom',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revDapartment`).d('受审部门'),
      name: 'beRevDepartmentName',
      bind: 'scheduleCodeObject.beRevDepartmentName',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revContent`).d('受审内容'),
      name: 'beRevContent',
      bind: 'scheduleCodeObject.beRevContent',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revCompany`).d('审核公司'),
      name: 'reviewCor',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStandard`).d('审核依据'),
      name: 'sysReviewStandard',
      bind: 'scheduleCodeObject.reviewStandardName',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStandard`).d('审核依据'),
      name: 'sysReviewStandardStr',
      bind: 'scheduleCodeObject.sysReviewStandardStr',
      disabled: true,
    },
  ],
});
// 详情-审核记录-流程输入、资源、实施者、绩效管理，流程输出、风险&机会列表
const auditRecordEnterTableDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: DataSetSelection.multiple,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recordContent`).d('内容'),
      name: 'recordContent',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.conformLevel`).d('符合度等级'),
      name: 'conformLevel',
      lookupCode: 'YP.QIS.CONFORM_LEVEL',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.beRevperName`).d('迎审人'),
      name: 'beRevperName',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revperName`).d('审核人'),
      name: 'revperName',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sysReviewStandard`).d('审核标准'),
      name: 'sysReviewStandardLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.SYS_REVIEW_STANDARD',
      required: true,
      multiple: true,
      computedProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteId: dataSet.parent?.getState('siteId'),
            sysReviewPlanId: dataSet.parent?.getState('sysReviewPlanId'),
            scheduleId: dataSet.parent?.current?.get('scheduleId'),
          };
        },
      },
    },
    {
      name: 'sysReviewStandardIdList',
      bind: 'sysReviewStandardLov.uniqueCode',
    },
    {
      name: 'sysReviewStandardNameList',
      bind: 'sysReviewStandardLov.fileName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleResult`).d('抽样结果'),
      name: 'sampleResult',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemAttri`).d('问题属性'),
      name: 'problemAttri',
      lookupCode: 'YP.QIS.PROBLEM_ATTRI',
      required: true,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('conformLevel') !== 'CONFORM';
        },
      },
    },
  ],
});
// 详情-审核记录-方法列表
const auditRecordFunctionTableDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: DataSetSelection.multiple,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recordContent`).d('内容'),
      name: 'recordContent',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.conformLevel`).d('符合度等级'),
      name: 'conformLevel',
      lookupCode: 'YP.QIS.CONFORM_LEVEL',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.schemeFlag`).d('策划'),
      name: 'schemeFlag',
      lookupCode: 'YP.QIS.HAVE_NOT',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.beRevperName`).d('迎审人'),
      name: 'beRevperName',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revperName`).d('审核人'),
      name: 'revperName',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sysReviewStandard`).d('审核标准'),
      name: 'sysReviewStandardLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.SYS_REVIEW_STANDARD',
      required: true,
      multiple: true,
      computedProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteId: dataSet.parent?.getState('siteId'),
            sysReviewPlanId: dataSet.parent?.getState('sysReviewPlanId'),
            scheduleId: dataSet.parent?.current?.get('scheduleId'),
          };
        },
      },
    },
    {
      name: 'sysReviewStandardIdList',
      bind: 'sysReviewStandardLov.uniqueCode',
    },
    {
      name: 'sysReviewStandardNameList',
      bind: 'sysReviewStandardLov.fileName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.schemeOutput`).d('策划输出'),
      name: 'schemeOutput',
      required: true,
      computedProps: {
        required: ({ record }) => {
          return record.get('schemeFlag') === 'Y';
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemAttri`).d('问题属性'),
      name: 'problemAttri',
      lookupCode: 'YP.QIS.PROBLEM_ATTRI',
      required: true,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('conformLevel') !== 'CONFORM';
        },
      },
    },
  ],
});

export { auditRecordFormDS, auditRecordEnterTableDS, auditRecordFunctionTableDS };
