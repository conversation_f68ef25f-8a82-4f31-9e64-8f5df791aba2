import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
/**
 * 查询用户默认站点
 * @function GetDefaultSite
 * @returns {object} fetch Promise
 */
export function GetDefaultSite(): object {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/ui`,
    method: 'GET',
  };
}
// 新建
export function SaveProblemInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/update/ui`,
    method: 'POST',
  }
}
// 提交
export function SubmitInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/submit/ui`,
    method: 'POST',
  }
}
// 开始分析
export function StartAnalyzingInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/start-analyzing/ui`,
    method: 'POST',
  }
}
// 详情
export function DetailInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/detail/ui`,
    method: 'GET',
  }
}
// 取消
export function CancelInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/cancel/ui`,
    method: 'POST',
  }
}

// 行表数据
export function LineQueryDataInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/line/ui`,
    method: 'GET',
  }
}

// 分析完成
export function PostFinish(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/complete-analyzing/ui`,
    method: 'POST',
  }
}

// 分析完成
export function RefershMsa(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/refresh/ui`,
    method: 'POST',
  }
}

// 查询MSA分析类型数据
export function QueryMsaTypeData(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/info/ui`,
    method: 'GET',
  }
}

// 保存MSA分析类型数据
export function SaveMsaTypeData(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-analysis/save/ui`,
    method: 'POST',
  }
}

// 增加GRR类型的行
export function AddGrrTaskLine(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/grr-add/ui`,
    method: 'POST',
  }
}

// 删除GRR类型的行
export function DeleteGrrTaskLine(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/grr-delete/ui`,
    method: 'POST',
  }
}
