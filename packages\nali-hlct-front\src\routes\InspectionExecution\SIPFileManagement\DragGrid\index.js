/**
 * DragGrid - 可拖拽分栏
 * @date: 2021-1-15
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import React, { useState, useEffect, useRef } from 'react';
import styles from './index.module.less';

let containerWidth = 0;
let dragPositionLeft = 0;

function dragGrid(props = {}) {
  const [sliderValue, setSliderValue] = useState(sessionStorage.getItem('dragSliderValue') || 12);
  useEffect(() => {
    setTimeout(() => {
      // 容器宽度
      containerWidth = ref.current.clientWidth;
      // 距离容器左侧距离
      dragPositionLeft = (containerWidth / 100) * sliderValue;
    }, 0);
    window.addEventListener('resize', onResize);
    return () => {
      window.removeEventListener('resize', onResize);
    };
  }, []);

  const ref = useRef();
  const {
    article = undefined,
    aside = undefined,
    asideShow = false,
  } = props;

  const onResize = () => {
    containerWidth = ref.current.clientWidth;
  };

  const onSliderChange = left => {
    let newSliderValue = (left * 100) / containerWidth;
    if (newSliderValue > 90) {
      newSliderValue = 90;
    } else if (newSliderValue < 10) {
      newSliderValue = 10;
    }
    setSliderValue(newSliderValue);
    sessionStorage.setItem('dragSliderValue', newSliderValue);
    return newSliderValue;
  };

  // 计算是否超出屏幕
  const InWindow = (left, top, startPosX, startPosY) => {
    const H = document.body.clientHeight;
    const W = document.body.clientWidth;
    if (
      (left < 20 && startPosX > left) ||
      (left > W - 20 && startPosX < left) ||
      (top < 20 && startPosY > top) ||
      (top > H - 20 && startPosY < top)
    ) {
      return false;
    }
    return true;
  };

  const onMouseDown = e => {
    e.preventDefault();
    const startPosX = e.clientX;
    const startPosY = e.clientY;
    // 距离body的XY值
    let left = dragPositionLeft - startPosX + e.clientX;
    document.body.onmousemove = event => {
      // 移动后距离左侧的距离
      left = dragPositionLeft - startPosX + event.clientX;
      if (InWindow(event.clientX, event.clientY, startPosX, startPosY)) {
        onSliderChange(left);
      } else {
        document.body.onmousemove = null;
        dragPositionLeft = (containerWidth / 100) * onSliderChange(left);
      }
    };
    document.body.onmouseup = () => {
      document.body.onmouseup = null;
      document.body.onmousemove = null;
      dragPositionLeft = (containerWidth / 100) * onSliderChange(left);
    };
  };

  return (
    <div className={styles['half-container']} ref={ref}>
      <div className={styles['half-box']} style={{ width: asideShow ? `${sliderValue}%` : `100%` }}>
        {article}
      </div>
      {asideShow && (
        <div className={styles['half-container-drag']} onMouseDown={onMouseDown}>
          <div className={styles['drag-item']} />
        </div>
      )}
      {asideShow && (
        <div className={styles['half-box']} style={{ width: `${100 - sliderValue}%` }}>
          {aside}
        </div>
      )}
    </div>
  );
}

export default dragGrid;
