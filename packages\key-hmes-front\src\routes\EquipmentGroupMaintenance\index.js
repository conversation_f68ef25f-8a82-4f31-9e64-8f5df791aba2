import React from 'react';
import { Button, DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import {observer} from "mobx-react";
import { tableDS } from './stores/EquipmentGroupMaintenanceDs';

const EquipmentGroupMaintenance = props => {
  // const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  const {
    tableDs,
  } = props;

  const create = () => {
    props.history.push({
      pathname: `/hmes/equipment-group-maintenance/create`,
    });
  };

  const edit = record => {
    props.history.push({
      pathname: `/hmes/equipment-group-maintenance/${record.data.assembleGroupId}`,
    });
  };

  const columns = [
    // 站点
    {
      name: 'siteCode',
      align: 'left',
    },
    // 装配点编码
    {
      name: 'assembleGroupCode',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => edit(record)}>{value}</a>
          </span>
        );
      },
    },
    // 装配点描述
    {
      name: 'description',
      align: 'left',
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '有效' : '无效'}>
          {}
        </Badge>
      ),
    },
    // {
    //   name: 'groupTypeDesc',
    //   align: 'left',
    // },
    {
      name: 'lastUpdateByName',
      align: 'left',
    },
  ];

  return (
    <div className="hmes-style">
      <Header title="装配组维护">
        <Button onClick={create} style={{ marginRight: 15 }} icon="add" color="primary">
          新建
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
          dragRow
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={4}
          searchCode="EquipmentGroupMaintenance"
          customizedCode="EquipmentGroupMaintenance"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.EquipmentGroupMaintenance', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    observer(EquipmentGroupMaintenance),
  ),
);
