/**
 * @Description: 控制控制图维护-报警信息配置-编辑抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-07-30 15:09:47
 * @LastEditTime: 2021-12-03 15:41:52
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { Form, TextField, Switch, Select, Lov } from 'choerodon-ui/pro';

const EditMessageDrawer = props => {
  const { infoDs } = props;

  return (
    <>
      <Form dataSet={infoDs} columns={2}>
        <TextField name="configureName" />
        <Select name="messageType" />
        <Lov name="emailServerCodeObj" />
        <Switch name="enableFlag" />
        <Lov name="templateCodeObj" />
        <Lov name="receiverGroupCodeObj" />
        <TextField name="messageCommand" colSpan={2} />
      </Form>
    </>
  );
};

export default EditMessageDrawer;
