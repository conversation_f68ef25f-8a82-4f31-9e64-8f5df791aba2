/**
 * @Description: 量具检定平台-接口
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

// 详情数据查询
export function FetchInspectGroupDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/edit/query/ui`,
    method: 'GET',
  };
}

// 详情数据保存
export function SaveInspectGroupDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/save/ui`,
    method: 'POST',
  };
}

// 详情数据提交
export function SubmitInspectGroupDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/submit/ui`,
    method: 'POST',
  };
}

// 审批通过
export function handleApproval() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/approval/ui`,
    method: 'POST',
  };
}

// 审批驳回
export function handleunApproval() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/reject/ui`,
    method: 'POST',
  };
}

// 失效
export function handleunenabled() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/expired/ui`,
    method: 'POST',
  };
}

/**
 * 保存检证信息
 * @function SaveVerification
 * @returns {object} fetch Promise
 */
export function SaveVerification(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/application/save/ui`,
    method: 'POST',
  };
}

/**
 * 查询用户默认站点
 * @function GetDefaultSite
 * @returns {object} fetch Promise
 */
export function GetDefaultSite(): object {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/ui`,
    method: 'GET',
  };
}

/**
 * 刷新检测项目列表
 * @function InternalRefresh
 * @returns {object} fetch Promise
 */
export function InternalRefresh(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/internal-refresh/ui`,
    method: 'POST',
  };
}

/**
 * 领取检定单
 * @function ClaimInspectDoc
 * @returns {object} fetch Promise
 */
export function ClaimInspectDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/receive/ui`,
    method: 'POST',
  };
}

/**
 * 确认领取检定单
 * @function ConfirmClaimInspectDoc
 * @returns {object} fetch Promise
 */
export function ConfirmClaimInspectDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-platform/receive-confirm/ui`,
    method: 'POST',
  };
}

