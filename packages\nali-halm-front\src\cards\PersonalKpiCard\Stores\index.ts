import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const queryDS: () => DataSetProps = () => {
  return {
    autoCreate: true,
    fields: [
      {
        name: 'dateUnitCode',
        type: FieldType.string,
        lookupCode: 'HALM.DATE_UNIT',
        defaultValue: 'DAY',
      },
      {
        name: 'workCenterLov',
        type: FieldType.object,
        lovCode: 'AMTC.WORKCENTERS',
      },
      {
        name: 'workCenterId',
        type: FieldType.number,
        bind: 'workCenterLov.workcenterId',
      },
      {
        name: 'workCenterName',
        type: FieldType.string,
        bind: 'workCenterLov.workcenterName',
      },
    ],
  };
};
export default queryDS;
