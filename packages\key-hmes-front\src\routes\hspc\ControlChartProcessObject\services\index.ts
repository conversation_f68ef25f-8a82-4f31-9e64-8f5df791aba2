/**
 * @Description: 控制图过程对象维护-services
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 17:45:04
 * @LastEditTime: 2022-08-16 14:56:57
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { HZERO_PLATFORM } from 'utils/config';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// **************************    ListPage    **************************
// 控制图过程对象维护-控制控制图-删除控制控制图过程对象
export function DeleteControlProcessObject() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control-process-objects/delete/ui`,
    method: 'POST',
  };
}

// 控制图过程对象维护-分析控制图-删除分析控制图过程对象
export function DeleteAnalysisProcessObject() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis-process-objects/delete/ui`,
    method: 'POST',
  };
}
// **************************    ListPage    **************************

// **************************    ChartProcessObjectDrawer    **************************
// 控制图过程对象维护-批量获取值集信息
export function FetchLovsBatchInfo() {
  return {
    url: `${HZERO_PLATFORM}/v1/${tenantId}/lovs/batch/info`,
    method: 'GET',
  };
}

// 控制图过程对象维护-控制控制图过程对象详情查询
export function FetchControlProcessObjectsDetail() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control-process-objects/detail/ui`,
    method: 'GET',
  };
}

// 控制图过程对象维护-分析控制图过程对象详情查询
export function FetchAnalysisProcessObjectsDetail() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis-process-objects/detail/ui`,
    method: 'GET',
  };
}

// 控制图过程对象维护-控制控制图过程对象详情查询
export function SaveControlProcessObjects() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control-process-objects/save/ui`,
    method: 'POST',
  };
}

// 控制图过程对象维护-分析控制图过程对象详情保存
export function SaveAnalysisProcessObjects() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis-process-objects/save/ui`,
    method: 'POST',
  };
}
// **************************    ChartProcessObjectDrawer    **************************

// **************************    ChartProcessObjectDrawer    **************************
// 编辑过程对象-控制控制图-过程对象替换
export function ReplaceControlProcessObjects() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control-process-objects/replace/ui`,
    method: 'POST',
  };
}

// 编辑过程对象-分析控制图-过程对象替换
export function ReplaceAnalysisProcessObjects() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis-process-objects/replace/ui`,
    method: 'POST',
  };
}
// **************************    ChartProcessObjectDrawer    **************************
