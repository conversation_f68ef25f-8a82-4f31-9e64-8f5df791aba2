import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows', // 列表数据在接口返回json中的相对路径
  primaryKey: 'childEoId', // 表格唯一性主键
  idField: 'childEoId',
  parentField: 'parentEoId',
  // childrenField: 'childMaterialDtlList',
  paging: false,
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('执行作业'),
      required: true,
    },
    {
      name: 'allOpenFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.allOpenFlag`).d('全部展开'),
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
    },
    // allOpenFlag  是否全部展开
  ],
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'componentTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentTypeDesc`).d('组件类型描述'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次号'),
    },
    {
      name: 'sumAssembleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumAssembleQty`).d('汇总数量'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
    {
      name: 'bindDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.bindDate`).d('绑定时间'),
    },
    {
      name: 'overStationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.overStationDate`).d('过站时间'),
    },
    {
      name: 'overStationByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overStationByName`).d('过站操作员'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-trace-report/material/forward/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
