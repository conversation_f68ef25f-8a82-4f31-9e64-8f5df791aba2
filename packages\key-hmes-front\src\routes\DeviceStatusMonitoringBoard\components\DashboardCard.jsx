import React from 'react';
import styles from '../index.module.less';

const DashboardCard = ({ title, children, width, height }) => {
  return (
    <div className={styles['dashboard-card']} style={{ width, height }}>      
      {title ? <img src={title} alt="dashboard-card-title" /> : ''}
      {/* <span  className={styles['dashboard-card-title']} >{title}</span> */}
      <div className={styles['dashboard-card-content']}>{children}</div>
    </div>
  );
};

export default DashboardCard;
