import React, { useState, useEffect, useMemo } from 'react';
import request from 'utils/request';
import { Header, Content } from 'components/Page';
import { DataSet, Button, Form, TextField, Spin, Select } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import { getCurrentOrganizationId } from 'utils/utils';

import { DetailFormDS, DetailTableDS } from '../stories';
import InspectItemTab from './InspectItemTab';

const modelPrompt = 'tarzan.prodlineReview.prodlineReviewTask';

const tenantId = getCurrentOrganizationId();

const { Panel } = Collapse;

const ProductionLineAuditTaskExecutionDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    customizeForm,
    customizeTable,
    custConfig,
  } = props;

  const [loading, setLoading] = useState(false);
  const [tableStatus, setTableStatus] = useState('');
  const [reviewType, setReviewType] = useState('');
  const [canEdit, setCanEdit] = useState(false);

  const formDS = useMemo(() => new DataSet(DetailFormDS()), []);
  const tableDS = useMemo(() => new DataSet(DetailTableDS()), []);

  useEffect(() => {
    initPageData();
  }, [id]);

  // 获取查询数据
  const initPageData = async () => {
    setLoading(true);
    const params = {
      lineRevplanTaskId: id,
    };
    return request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-review-task-platform/detail/query`,
      {
        method: 'GET',
        query: params,
      },
    ).then(res => {
      setLoading(false);
      if (res && !res.failed) {
        const { lineList } = res;
        formDS.loadData([res]);
        setTableStatus(res.status);
        setReviewType(res.reviewType);
        tableDS.loadData(lineList);
      } else {
        setLoading(false);
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  const handleSubmit = async (validateFlag = false) => {
    if (validateFlag) {
      const tableValidate = await tableDS.validate();
      if (!tableValidate) {
        return Promise.resolve(false);
      }
    }

    setLoading(true);
    const ids = id;
    return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-review-task-platform/submit`, {
      method: 'POST',
      body: ids,
    })
      .then(res => {
        if (res && !res.failed) {
          notification.success({});
          initPageData();
        } else {
          setLoading(false);
          notification.error({ message: res.message });
          return false;
        }
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 数据保存
  const handleSaveData = async (submitFlag = false) => {
    const tableValidate = await tableDS.validate();
    if (submitFlag && !tableValidate) {
      return Promise.resolve(false);
    }
    setLoading(true);

    return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-review-task-platform/detail/save`, {
      method: 'POST',
      body: tableDS.toData(),
    }).then(res => {
      if (res && !res.failed) {
        notification.success({});
        initPageData();
        setLoading(false);
        if (submitFlag) {
          handleSubmit(false);
        }
        setCanEdit(false);
        return true;
      }
      setLoading(false);
      notification.error({ message: res.message });
      setLoading(false);
      return false;
    });
  };

  const handleCancel = () => {
    setCanEdit(false);
    initPageData();
  };

  // 检验项目组件传参
  const inspectItemTabProps = {
    path,
    id,
    canEdit,
    customizeTable,
    customizeForm,
    custConfig,
    tableDS,
    loading,
    tableStatus,
    reviewType,
  };

  return (
    <div className="hmes-style" style={{ height: '100%' }}>
      <Header
        title={intl.get(`${modelPrompt}.title.detail`).d('产线审核任务执行平台详情')}
        backPath="/hwms/production_line_audit_task_execution_platform"
      >
        {canEdit ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={() => handleSaveData(false)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <Button
            icon="edit-o"
            color={ButtonColor.primary}
            disabled={tableStatus === 'IN_APPROVAL' || tableStatus === 'COMPLETED'}
            onClick={() => setCanEdit(true)}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        )}
        <Button
          disabled={tableStatus !== 'EXECUTING' && tableStatus !== 'REJECTED'}
          color={ButtonColor.primary}
          icon="save"
          onClick={() => {
            if (canEdit) {
              handleSaveData(true);
            } else {
              handleSubmit(true);
            }
          }}
        >
          {intl.get(`${modelPrompt}.button.submit`).d('提交')}
        </Button>
        <ApprovalInfoDrawer objectTypeList={['QIS_CXSH_LWS_TASK']} objectId={id} />
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Collapse bordered={false} defaultActiveKey={['BASIC', 'ITEM']}>
            <Panel key="BASIC" header={intl.get(`${modelPrompt}.title.basicInfo`).d('基本信息')} dataSet={formDS}>
              <Form dataSet={formDS} columns={3} labelWidth={112} disabled>
                <TextField name="prodlineRevplanCode" disabled />
                <TextField name="lineRevplanTaskCode" disabled />
                <TextField name="siteName" disabled />
                <TextField name="prodLineNameList" colSpan={2} disabled />
                <TextField name="statusMeaning" disabled />
                <Select name="projectName" disabled />
                <TextField name="reviewTypeMeaning" disabled />
                <TextField name="reviewStageMeaning" disabled />
                <TextField name="createdByName" disabled />
                <TextField name="creationDate" disabled />
              </Form>
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.title.prodlineReviewElememt`).d('产线审核要素')} key="ITEM" dataSet={tableDS}>
              <InspectItemTab {...inspectItemTabProps} />
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(ProductionLineAuditTaskExecutionDetail);
