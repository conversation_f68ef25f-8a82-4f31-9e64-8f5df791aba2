/*
 * @Description: 产品审核任务-详情页
 * @Author: <<EMAIL>>
 * @Date: 2023-10-10 15:06:52
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-19 18:39:16
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  Modal,
  NumberField,
  Attachment,
  TextField,
  Select,
  DatePicker,
  DateTimePicker,
  Table,
  Switch,
  SelectBox,
  Row,
  Col,
  Dropdown,
  Menu,
  TextArea,
} from 'choerodon-ui/pro';
import { Badge, Collapse, Icon, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { FuncType } from 'choerodon-ui/pro/es/button/enum';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { getCurrentUser } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin, drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import SampleDrawer from './SampleDrawer';
import ProductDtlSampleDrawer from './ProductDtlSampleDrawer';
import {
  detailDS,
  productRevItemDS,
  barcodeReviewResultDS,
  addProjectItemDS,
  sampleDS,
  rejectReasonDS,
} from '../stores/DetailDS';
import {
  SaveProductRevTask,
  SaveProductItem,
  SaveProductDtlSample,
  CancelOrPickTask,
  QueryProductRevSchemeInfo,
  PublishProductRevTask,
  SaveProductSample,
  SubmitBWriteInfo,
  SubmitCWriteInfo,
  RejectProductRevTask,
} from '../services';
import styles from './index.module.less';

const { Panel } = Collapse;
const currentUserInfo = getCurrentUser();
const modelPrompt = 'tarzan.qms.productReview.productReviewTask';

const ProductReviewTaskDetail = props => {
  const {
    history,
    match: { params, path },
  } = props;
  const kid = params.id;

  // pub 路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  const [canEdit, setCanEdit] = useState(false);
  const [activeKey, setActiveKey] = useState<string[] | string>([
    'basicInfo',
    'measureInfo',
    'disposalResult',
  ]);
  const [itemTypeList, setItemTypeList] = useState([]); // 项目类型数据
  const [productRevTaskStatus, setStatus] = useState<string>('NEW'); // 产品审核项目状态
  const [productRevTaskType, setTaskType] = useState<string>('CUSTOMER_REQUEST'); // 产品审核项目类型
  // 当前用户角色列表
  // 创建人 'CREATED_PERSON'
  // 发布人 'PUBLISHED_PERSON'
  // 审核人 'REVIEWED_PERSON'
  // 发布管理员 'PUBLISHED_ADMIN'
  // 审核管理员 'REVIEWED_ADMIN'
  const [userRoleList, setUserRoleList] = useState<string[]>([]);
  const [adminInfo, setAdminInfo] = useState<any>();
  const detailDs = useMemo(() => new DataSet(detailDS()), [kid]);
  // 新建产品审核项目时用的DS
  const productRevItemDs = useMemo(() => new DataSet(productRevItemDS()), []);
  // 条码信息DS
  const sampleDs = useMemo(() => new DataSet(sampleDS()), []);
  // 条码审核结果DS
  const barcodeReviewResultDs = useMemo(() => new DataSet(barcodeReviewResultDS()), []);
  // 审批驳回DS
  const rejectReasonDs = useMemo(() => new DataSet(rejectReasonDS()), []);

  // 保存产品审核计划
  const { run: saveProductRevTask, loading: saveLoading } = useRequest(SaveProductRevTask(), {
    manual: true,
    needPromise: true,
  });
  // 查询产品审核方案信息
  const {
    run: queryProductRevSchemeInfo,
    loading: querySchemeLoading,
  } = useRequest(QueryProductRevSchemeInfo(), { manual: true, needPromise: true });
  // 新建产品审核项目
  const { run: saveProductReviewItem } = useRequest(SaveProductItem(), { manual: true });
  // 保存任务的样本信息
  const { run: saveProductSample } = useRequest(SaveProductSample(), { manual: true });
  // 保存任务的条码审核结果
  const { run: saveProductDtlSample } = useRequest(SaveProductDtlSample(), { manual: true });
  // 取消或领取产品审核任务
  const { run: cancelOrPickTask } = useRequest(CancelOrPickTask(), {
    manual: true,
    needPromise: true,
  });
  // 取消或领取产品审核任务
  const {
    run: publishProductRevTask,
    loading: publishAWriteLoading,
  } = useRequest(PublishProductRevTask(), { manual: true, needPromise: true });
  // b-write提交
  const { run: submitBWriteInfo, loading: submitBWriteLoading } = useRequest(SubmitBWriteInfo(), {
    manual: true,
    needPromise: true,
  });
  // c-write提交
  const { run: submitCWriteInfo, loading: submitCWriteLoading } = useRequest(SubmitCWriteInfo(), {
    manual: true,
    needPromise: true,
  });
  // 审批驳回
  const {
    run: rejectProductRevTask,
    loading: rejectTaskLoading,
  } = useRequest(RejectProductRevTask(), { manual: true, needPromise: true });
  // 查询产品社和任务管理员信息
  const { run: queryAdminList, loading: queryAdminLoading } = useRequest(
    { lovCode: 'YP.QIS.PRODUCT_REV_TASK_ADMIN' },
    { manual: true, needPromise: true },
  );

  useEffect(() => {
    handleInitAdminInfo();
    if (kid === 'create') {
      // 新建时
      setCanEdit(true);
      handleInitAdminInfo();
      return;
    }

    // 编辑时
    handleInitPage();
  }, [kid]);

  useEffect(() => {
    if (Object.keys(props?.location?.state || {}).length === 0 || props?.location?.state?._back) {
      return;
    }
    detailDs.reset();
    setItemTypeList([]);
    const {
      siteId,
      siteName,
      productRevPlanCode,
      productRevPlanId,
      productRevPlanDtlId,
      materialId,
      materialCode,
      materialName,
      workcellId,
      workcellCode,
      workcellName,
    } = props?.location?.state || {};
    detailDs.current?.set('siteLov', { siteId, siteName });
    detailDs.current?.set('productRevPlanCode', productRevPlanCode);
    detailDs.current?.set('productRevPlanId', productRevPlanId);
    detailDs.current?.set('productRevPlanDtlId', productRevPlanDtlId);
    detailDs.current?.set('materialId', materialId);
    detailDs.current?.set('planMaterialId', materialId);
    detailDs.current?.set('materialCode', materialCode);
    detailDs.current?.set('materialName', materialName);
    detailDs.current?.set('workcellId', workcellId);
    detailDs.current?.set('planWorkcellId', workcellId);
    detailDs.current?.set('workcellCode', workcellCode);
    detailDs.current?.set('workcellName', workcellName);
    detailDs.current?.set('productRevTaskType', 'QUALITY_SYS_REQUEST ');
    setTaskType('QUALITY_SYS_REQUEST');
    setTimeout(() => {
      history.replace({ ...history.location, state: {} });
    }, 200);
  }, [props?.location?.state]);

  // 初始化管理员信息
  const handleInitAdminInfo = async (createFlag = true) => {
    let _adminInfo = adminInfo;
    if (!_adminInfo?.publishedList?.length && !_adminInfo?.reviewedList?.length) {
      const adminRes = await queryAdminList({});
      if (adminRes?.length) {
        const _publishedList =
          adminRes.find(item => item.value === 'PUBLISHED_BY')?.tag?.split(',') || [];
        const _reviewedList =
          adminRes.find(item => item.value === 'REVIEWED_BY')?.tag?.split(',') || [];
        _adminInfo = { publishedList: _publishedList, reviewedList: _reviewedList };
        setAdminInfo(_adminInfo);
      }
    }
    const _userRoleList: string[] = createFlag ? ['CREATED_PERSON'] : [];
    if (_adminInfo?.publishedList?.includes(currentUserInfo.loginName)) {
      _userRoleList.push('PUBLISHED_ADMIN');
    }
    if (_adminInfo?.reviewedList?.includes(currentUserInfo.loginName)) {
      _userRoleList.push('REVIEWED_ADMIN');
    }
    setUserRoleList(_userRoleList);
    return _userRoleList;
  };

  const handleInitPage = async (productRevTaskId = kid) => {
    detailDs.setQueryParameter('productRevTaskId', productRevTaskId);
    const res = await detailDs.query();
    const {
      productRevTaskStatus,
      productRevTaskType,
      createdBy,
      publishedBy,
      reviewedBy,
      groupInfos,
    } = res || {};
    setStatus(productRevTaskStatus);
    setTaskType(productRevTaskType);

    const _userRoleList: string[] = await handleInitAdminInfo(false);
    if (createdBy === currentUserInfo.id) {
      _userRoleList.push('CREATED_PERSON');
    }
    if (publishedBy === currentUserInfo.id) {
      _userRoleList.push('PUBLISHED_PERSON');
    }
    if (reviewedBy === currentUserInfo.id) {
      _userRoleList.push('REVIEWED_PERSON');
    }
    setUserRoleList(_userRoleList);

    const _activityKey: string[] | string = ['basicInfo', 'measureInfo', 'disposalResult'];
    const _itemTypeList = (groupInfos || []).map(groupItem => {
      const { detailInfos, productReviewItemType, productReviewItemTypeDesc } = groupItem;
      _activityKey.push(productReviewItemType);
      // 产品审核项目DS
      const productRevItemDs = new DataSet({
        ...productRevItemDS(),
        data: detailInfos,
      });
      productRevItemDs.setState('productRevTaskStatus', productRevTaskStatus);

      return {
        productReviewItemType,
        productReviewItemTypeDesc,
        productRevItemDs,
      };
    });
    setItemTypeList(_itemTypeList);
    setActiveKey(_activityKey);
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(() => {
    if (kid === 'create') {
      history.push('/hwms/product-review/product-review-task/list');
    } else {
      setCanEdit(false);
      handleInitPage();
    }
  }, []);

  const handleValidateDtl = async () => {
    // 校验所有表单
    const validateFlag = await detailDs.validate();
    const normalValidate = await Promise.all(
      itemTypeList.map(async (item: any) => {
        const dtlValidateRes = await item.productRevItemDs.validate();
        return dtlValidateRes;
      }),
    );
    // 汇总校验结果
    return !validateFlag || !normalValidate.every(val => val);
  };

  const handleSave = async (submitFlag = false) => {
    const valRes = await handleValidateDtl();
    if (submitFlag && valRes) {
      return;
    }
    const tableData = itemTypeList.map((item: any) => {
      const { productRevItemDs, productReviewItemType, productReviewItemTypeDesc } = item;
      return {
        productReviewItemType,
        productReviewItemTypeDesc,
        detailInfos: productRevItemDs.toData(),
      };
    });
    const res = await saveProductRevTask({
      params: {
        baseInfo: {
          ...detailDs.current?.toData(),
          groupInfos: undefined,
        },
        groupInfos: tableData,
      },
    });
    if (res && res.success) {
      notification.success({
        message: intl.get(`${modelPrompt}.saveSuccess`).d('保存成功'),
      });
      if (submitFlag) {
        handleSubmitTask(false, res.rows);
      } else if (kid === 'create') {
        history.push(`/hwms/product-review/product-review-task/dist/${res.rows}`);
        setCanEdit(false);
      } else {
        setCanEdit(false);
        handleInitPage();
      }
      return true;
    }
    return false;
  };

  const handleAddRevItemLine = lineDataList => {
    const _itemTypeList: any = [...itemTypeList];
    const _activityKey: string[] | string = [...activeKey];
    lineDataList.forEach(lineData => {
      const { productReviewItemType, productReviewItemTypeDesc } = lineData || {};
      const _currentTypeIndex = _itemTypeList.findIndex(
        (item: any) => item.productReviewItemType === productReviewItemType,
      );
      if (_currentTypeIndex > -1) {
        const { productRevItemDs } = _itemTypeList[_currentTypeIndex] || {};
        if (
          !productRevItemDs.find(
            _record => _record?.get('productReviewItemId') === lineData?.productReviewItemId,
          )
        ) {
          // 在DS中创建一条数据
          productRevItemDs.create({ ...lineData }, 0);
          _itemTypeList[_currentTypeIndex] = {
            ..._itemTypeList[_currentTypeIndex],
            productRevItemDs,
          };
        }
      } else {
        // 产品审核项目DS
        const productRevItemDs = new DataSet(productRevItemDS());
        productRevItemDs.setState('productRevTaskStatus', productRevTaskStatus);
        productRevItemDs.create({ ...lineData }, 0);
        _itemTypeList.push({
          productReviewItemType,
          productReviewItemTypeDesc,
          productRevItemDs,
        });
        _activityKey.push(productReviewItemType);
      }
    });
    setItemTypeList(_itemTypeList);
    setActiveKey(_activityKey);
  };

  const handleSaveReviewItem = (dataSet, nextFlag = false) => {
    return new Promise(async resolve => {
      const valRes = await dataSet.validate();
      if (!valRes) {
        return resolve(false);
      }
      saveProductReviewItem({
        params: {
          ...dataSet.current?.toData(),
        },
        onSuccess: res => {
          const { productReviewItemType } = res || {};
          notification.success({});
          handleAddRevItemLine([ res ]);
          if (nextFlag) {
            dataSet.reset();
            productRevItemDs?.current?.set('productReviewItemType', productReviewItemType);
            return resolve(false);
          }
          return resolve(true);
        },
        onFailed: () => {
          return resolve(false);
        },
      });
    });
  };

  const handleCreateReviewItem = (modal, type = undefined) => {
    modal.close();
    if (type) {
      productRevItemDs?.current?.set('productReviewItemType', type);
    }
    Modal.open({
      ...drawerPropsC7n({
        ds: productRevItemDs,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.createReviewItem`).d('产品审核项目新建'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: () => handleSaveReviewItem(productRevItemDs),
      children: (
        <Form dataSet={productRevItemDs} columns={1}>
          <TextField name="productReviewItemCode" />
          <Select name="productReviewItemType" searchable />
          <TextField name="productReviewItemDesc" />
          <Select name="productReviewMethod" />
          <TextField name="specRequire" />
          <Select name="reviewFrequency" />
          <Select name="weightCoefficient" />
          <TextField name="remark" />
          <Switch name="recordDataFlag" />
          <Switch name="fromOrtFlag" />
          <Switch name="enableFlag" />
        </Form>
      ),
      footer: (okBtn, cancelBtn) => {
        return [
          cancelBtn,
          <Button onClick={() => handleSaveReviewItem(productRevItemDs, true)}>
            {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并新建下一条')}
          </Button>,
          okBtn,
        ];
      },
    });
  };

  const handleSaveDtlSample = (record, drawerDs) => {
    return new Promise(async resolve => {
      await drawerDs.validate();
      saveProductDtlSample({
        params: {
          dtlSampleInfos: drawerDs.toData(),
          productRevTaskId: kid,
          productRevTaskDtlId: record.get('productRevTaskDtlId'),
        },
        onSuccess: () => {
          notification.success({});
          handleInitPage();
          return resolve(true);
        },
        onFailed: () => {
          return resolve(false);
        },
      });
    });
  };

  // 保存样本信息
  const handleSaveSample = () => {
    return new Promise(async resolve => {
      saveProductSample({
        params: sampleDs.toData() || [],
        onSuccess: () => {
          notification.success({});
          return resolve(true);
        },
        onFailed: () => {
          return resolve(false);
        },
      });
    });
  };

  // 打开样本信息抽屉
  const handleOpenSampleDrawer = () => {
    sampleDs.setQueryParameter('productRevTaskId', kid);
    sampleDs.query();
    const disabled =
      !['EXECUTING', 'REVIEW_REJECT'].includes(productRevTaskStatus) ||
      !getBWritePermission();
    Modal.open({
      ...drawerPropsC7n({
        ds: sampleDs,
        canEdit: !disabled,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.sampleInfo`).d('样本信息'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      okText: intl.get(`${modelPrompt}.button.save`).d('保存'),
      onOk: () => handleSaveSample(),
      onCancel: () => handleCloseDrawer(sampleDs),
      children: (
        <SampleDrawer
          dataSet={sampleDs}
          detailDs={detailDs}
          productRevTaskId={kid}
          productRevTaskStatus={productRevTaskStatus}
          canEdit={canEdit}
          disabled={disabled}
        />
      ),
    });
  };

  const handleCloseDrawer = dataSet => {
    if (dataSet.dirty) {
      return new Promise(async resolve => {
        const res = await Modal.confirm({
          title: intl.get(`tarzan.common.title.tips`).d('提示'),
          children: (
            <p>
              {intl
                .get(`${modelPrompt}.info.closeDrawer`)
                .d('当前编辑的数据未保存，是否确认退出？')}
            </p>
          ),
        });
        if (res === 'ok') {
          return resolve(true);
        }
        return resolve(false);
      });
    }
    return true;
  };

  const handleOpenDtlSampleDrawer = record => {
    barcodeReviewResultDs.setQueryParameter('productRevTaskId', kid);
    barcodeReviewResultDs.setQueryParameter(
      'productRevTaskDtlId',
      record?.get('productRevTaskDtlId'),
    );
    barcodeReviewResultDs.query();
    barcodeReviewResultDs.setState('recordDataFlag', record?.get('recordDataFlag'));
    const disabled =
      !['EXECUTING', 'REVIEW_REJECT'].includes(productRevTaskStatus) ||
      !getBWritePermission();
    Modal.open({
      ...drawerPropsC7n({
        ds: barcodeReviewResultDs,
        canEdit: !disabled,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.brcodeReviewResultEnter`).d('条码审核结果录入'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      okText: intl.get(`${modelPrompt}.button.save`).d('保存'),
      onOk: () => handleSaveDtlSample(record, barcodeReviewResultDs),
      onCancel: () => handleCloseDrawer(barcodeReviewResultDs),
      children: (
        <ProductDtlSampleDrawer
          dataSet={barcodeReviewResultDs}
          parentRecord={record}
          productRevTaskStatus={productRevTaskStatus}
          detailDs={detailDs}
          disabled={disabled}
        />
      ),
    });
  };

  const handleDeleteLine = (dataSet, record, itemTypeIndex) => {
    dataSet.remove(record);
    if (!dataSet.length) {
      const _itemTypeList = [...itemTypeList];
      _itemTypeList.splice(itemTypeIndex, 1);
      setItemTypeList(_itemTypeList);
    }
  };

  const getItemColumns: any = (itemDs, productReviewItemType, itemTypeIndex, canEdit) => {
    return [
      {
        header: () => (
          <AddProjectRevItemButton
            canEdit={canEdit}
            productReviewItemType={productReviewItemType}
            productRevTaskStatus={productRevTaskStatus}
          />
        ),
        name: 'add',
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDeleteLine(itemDs, record, itemTypeIndex)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <Button
              icon="remove"
              disabled={
                !canEdit ||
                productRevTaskType !== 'CUSTOMER_REQUEST' ||
                productRevTaskStatus !== 'NEW'
              }
              funcType={FuncType.flat}
            />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'productReviewItemCode',
        width: 160,
      },
      {
        name: 'productReviewItemDesc',
      },
      {
        name: 'productReviewMethod',
      },
      {
        name: 'specRequire',
      },
      {
        name: 'reviewFrequency',
      },
      {
        name: 'weightCoefficient',
        width: 180,
      },
      {
        name: 'remark',
      },
      {
        name: 'fp',
      },
      {
        name: 'nf',
        width: 180,
      },
      {
        name: 'defectCount',
      },
      {
        name: 'reviewResult',
        editor:
          canEdit &&
          ['EXECUTING', 'REVIEW_REJECT'].includes(productRevTaskStatus) &&
          Boolean(getBWritePermission()),
      },
      {
        name: 'recordDataFlag',
        width: 160,
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
      {
        name: 'fromOrtFlag',
        align: ColumnAlign.center,
        width: 160,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 150,
        renderer: ({ record }) => (
          <Button
            funcType={FuncType.flat}
            disabled={productRevTaskStatus === 'NEW'}
            onClick={() => handleOpenDtlSampleDrawer(record)}
          >
            {intl.get(`${modelPrompt}.operation.brcodeReviewResultEnter`).d('条码审核结果录入')}
          </Button>
        ),
      },
    ];
  };

  const handleChangeSite = (_, oldValue) => {
    if (oldValue && detailDs.current?.get('productRevSchemeId')) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.clearSiteData`)
              .d(
                '将清空站点、审核方案编码及根据审核方案编码带入的任务头数据及带入的所有行项目，是否确认操作？',
              )}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          detailDs.current?.set('productRevSchemeLov', undefined);
          detailDs.current?.set('warehouseLov', undefined);
          detailDs.current?.set('samplePosition', undefined);
          setItemTypeList([]);
        } else {
          detailDs.current?.set('siteLov', oldValue);
        }
      });
    } else {
      detailDs.current?.set('warehouseLov', undefined);
      detailDs.current?.set('samplePosition', undefined);
      setItemTypeList([]);
    }
  };

  const handleQuerySchemeInfo = async productRevSchemeId => {
    const schemeRes = await queryProductRevSchemeInfo({
      params: { productRevSchemeId },
    });
    const { groupInfo } = schemeRes?.rows || {};
    const _activityKey: string[] | string = ['basicInfo', 'measureInfo', 'disposalResult'];
    const _itemTypeList = (groupInfo || []).map(groupItem => {
      const { productReviewItemType, dtlInfo } = groupItem;
      _activityKey.push(productReviewItemType);
      // 产品审核项目DS
      const productRevItemDs = new DataSet({
        ...productRevItemDS(),
        data: dtlInfo,
      });
      return {
        ...groupItem,
        productRevItemDs,
      };
    });
    setItemTypeList(_itemTypeList);
    setActiveKey(_activityKey);
  };

  const handleChangeRevScheme = (value, oldValue) => {
    if (oldValue && oldValue?.productRevSchemeId) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.clearRevSchemeData`)
              .d(
                '将清空审核方案编码及根据审核方案编码带入的任务头数据及带入的所有行项目，是否确认操作？',
              )}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          const { materialId, workcellId } = value || {};
          if (materialId && workcellId) {
            detailDs.current?.set('samplePosition', 'WORKSHOP');
          } else if (materialId) {
            detailDs.current?.set('samplePosition', 'WAREHOUSE');
          }
          if (value && value?.productRevSchemeId) {
            handleQuerySchemeInfo(value?.productRevSchemeId);
          } else {
            detailDs.current?.set('samplePosition', undefined);
            setItemTypeList([]);
          }
        } else {
          detailDs.current?.set('productRevSchemeLov', oldValue);
        }
      });
    } else {
      const { materialId, workcellId } = value || {};
      if (materialId && workcellId) {
        detailDs.current?.set('samplePosition', 'WORKSHOP');
      } else if (materialId) {
        detailDs.current?.set('samplePosition', 'WAREHOUSE');
      }
      if (value && value?.productRevSchemeId) {
        handleQuerySchemeInfo(value?.productRevSchemeId);
      } else {
        detailDs.current?.set('samplePosition', undefined);
        setItemTypeList([]);
      }
    }
  };

  const handleChangeMeasureFlag = type => {
    detailDs.current?.set(`${type}ExecutorLov`, undefined);
    detailDs.current?.set(`${type}ExecuteLoc`, undefined);
    detailDs.current?.set(`${type}ExecuteCon`, undefined);
  };

  const enclosureProps: any = {
    name: 'reviewUuid',
    bucketName: 'qms',
    bucketDirectory: 'product-review-task',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const AddProjectRevItemButton = observer(
    ({ canEdit, productRevTaskStatus, productReviewItemType = undefined }) => (
      <Lov
        dataSet={new DataSet(addProjectItemDS(productReviewItemType))}
        name="toolLov"
        noCache
        mode={ViewMode.button}
        clearButton={false}
        onChange={val => handleAddRevItemLine(val)}
        size={Size.small}
        funcType={FuncType.flat}
        disabled={
          !canEdit || productRevTaskType !== 'CUSTOMER_REQUEST' || productRevTaskStatus !== 'NEW'
        }
        modalProps={{
          footer: (okBtn, cancelBtn, modal) => {
            return [
              <Button onClick={() => handleCreateReviewItem(modal, productReviewItemType)}>
                {intl.get(`${modelPrompt}.button.createReviewItem`).d('新建产品审核项目')}
              </Button>,
              cancelBtn,
              okBtn,
            ];
          },
        }}
      >
        <Icon type="add" />
      </Lov>
    ),
  );

  const handleCancelOrPickTask = targetStatus => {
    return new Promise(async resolve => {
      const res = await cancelOrPickTask({
        params: {
          productRevTaskIds: [kid],
          productRevTaskStatus: targetStatus,
        },
      });
      if (res && res.success) {
        notification.success({});
        handleInitPage();
        return resolve(true);
      }
      return resolve(false);
    });
  };

  // 发布/提交任务
  const handleSubmitTask = (validateFlag = true, productRevTaskId = kid) => {
    return new Promise(async resolve => {
      if (validateFlag) {
        const valRes = await handleValidateDtl();
        if (valRes) {
          return resolve(false);
        }
      }
      let _run;
      if (productRevTaskStatus === 'NEW') {
        _run = publishProductRevTask;
      } else if (['EXECUTING', 'REVIEW_REJECT'].includes(productRevTaskStatus)) {
        _run = submitBWriteInfo;
      } else {
        _run = submitCWriteInfo;
      }
      const res = await _run({
        params: {
          productRevTaskId,
        },
      });
      if (res && res.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.submitSuccess`).d('提交成功'),
        });
        if (kid === 'create') {
          history.push(`/hwms/product-review/product-review-task/dist/${productRevTaskId}`);
          setCanEdit(false);
        } else {
          handleInitPage();
          setCanEdit(false);
        }
        return resolve(true);
      }
      return resolve(false);
    });
  };

  // 审批驳回确定按钮的回调
  const handleRejectReasonOk = () => {
    return new Promise(async resolve => {
      const valRes = await rejectReasonDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      const res = await rejectProductRevTask({
        params: {
          productRevTaskId: kid,
          qeRejectReason: rejectReasonDs.current?.get('rejectReason'),
        },
      });
      if (res && res.success) {
        notification.success({});
        handleInitPage();
        return resolve(true);
      }
      return resolve(false);
    });
  };

  // 审批驳回的回调
  const handleReject = () => {
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.rejectReason`).d('审批驳回'),
      destroyOnClose: true,
      style: {
        width: 420,
      },
      onOk: handleRejectReasonOk,
      children: (
        <Form dataSet={rejectReasonDs}>
          <TextArea name="rejectReason" autoSize={{ minRows: 2, maxRows: 8 }} />
        </Form>
      ),
    });
  };

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '120px' }}>
      <Menu.Item key="SUBMIT_APPROVAL">
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            if (canEdit) {
              handleSave(true);
            } else {
              handleSubmitTask();
            }
          }}
        >
          {intl.get(`${modelPrompt}.button.submitApproval`).d('提交审批')}
        </a>
      </Menu.Item>
      <Menu.Item key="REJECT">
        <a target="_blank" rel="noopener noreferrer" onClick={() => handleReject()}>
          {intl.get(`${modelPrompt}.button.reject`).d('驳回至审核员')}
        </a>
      </Menu.Item>
    </Menu>
  );

  // 校验A-White编辑权限
  const getAWritePermission = () => {
    const _permissionRole = ['CREATED_PERSON', 'PUBLISHED_PERSON', 'PUBLISHED_ADMIN'];
    return _permissionRole.find(item => userRoleList.includes(item));
  };

  // 校验B-White编辑权限
  const getBWritePermission = () => {
    const _permissionRole = ['REVIEWED_PERSON', 'REVIEWED_ADMIN'];
    return _permissionRole.find(item => userRoleList.includes(item));
  };

  // 校验C-White编辑权限
  const getCWritePermission = () => {
    const _permissionRole = ['PUBLISHED_PERSON', 'PUBLISHED_ADMIN'];
    return _permissionRole.find(item => userRoleList.includes(item));
  };

  return (
    <div className="hmes-style">
      <TarzanSpin
        dataSet={detailDs}
        spinning={
          saveLoading ||
          queryAdminLoading ||
          querySchemeLoading ||
          publishAWriteLoading ||
          submitBWriteLoading ||
          submitCWriteLoading ||
          rejectTaskLoading
        }
      >
        {pubFlag && (
          <Header title={intl.get(`${modelPrompt}.title.detail`).d('产品审核任务')}>
            <ApprovalInfoDrawer objectTypeList={['QIS_PRODUCT_REV_TASK']} objectId={kid} />
          </Header>
        )}
        {!pubFlag && (
          <Header
            title={intl.get(`${modelPrompt}.title.detail`).d('产品审核任务')}
            backPath="/hwms/product-review/product-review-task/list"
          >
            {canEdit ? (
              <>
                <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave()}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
                <Button icon="close" onClick={handleCancel}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
              </>
            ) : (
              <>
                <PermissionButton
                  type="c7n-pro"
                  icon="edit-o"
                  color={ButtonColor.primary}
                  onClick={handleEdit}
                  disabled={
                    !(
                      (productRevTaskStatus === 'NEW' && getAWritePermission()) ||
                      (['EXECUTING', 'REVIEW_REJECT'].includes(productRevTaskStatus) &&
                        getBWritePermission()) ||
                      (['REVIEWING', 'REJECTED'].includes(productRevTaskStatus) &&
                        getCWritePermission())
                    )
                  }
                  permissionList={[
                    {
                      code: `${modelPrompt}.dist.button.edit`,
                      type: 'button',
                      meaning: '详情页-编辑新建删除复制按钮',
                    },
                  ]}
                >
                  {intl.get('tarzan.common.button.edit').d('编辑')}
                </PermissionButton>
                {productRevTaskStatus === 'NEW' && (
                  <PermissionButton
                    type="c7n-pro"
                    disabled={!getAWritePermission()}
                    permissionList={[
                      {
                        code: `${modelPrompt}.dist.button.cancel`,
                        type: 'button',
                        meaning: '详情页-取消任务按钮',
                      },
                    ]}
                    onClick={() => handleCancelOrPickTask('CANCEL')}
                  >
                    {intl.get(`${modelPrompt}.button.cancelTask`).d('取消任务')}
                  </PermissionButton>
                )}
              </>
            )}
            <Button disabled={productRevTaskStatus === 'NEW'} onClick={handleOpenSampleDrawer}>
              {intl.get(`${modelPrompt}.button.sampleInfo`).d('样本信息')}
            </Button>
            {productRevTaskStatus === 'NEW' && (
              <>
                <PermissionButton
                  type="c7n-pro"
                  disabled={!getAWritePermission()}
                  permissionList={[
                    {
                      code: `${modelPrompt}.dist.button.publishAWrite`,
                      type: 'button',
                      meaning: '详情页-AWrite发布任务',
                    },
                  ]}
                  onClick={() => {
                    if (canEdit) {
                      handleSave(true);
                    } else {
                      handleSubmitTask();
                    }
                  }}
                >
                  {intl.get(`${modelPrompt}.button.publishTask`).d('发布任务')}
                </PermissionButton>
              </>
            )}
            {['EXECUTING', 'REVIEW_REJECT'].includes(productRevTaskStatus) && (
              <PermissionButton
                type="c7n-pro"
                disabled={!getBWritePermission()}
                permissionList={[
                  {
                    code: `${modelPrompt}.dist.button.submitBWrite`,
                    type: 'button',
                    meaning: '详情页-BWrite发布任务',
                  },
                ]}
                onClick={() => {
                  if (canEdit) {
                    handleSave(true);
                  } else {
                    handleSubmitTask();
                  }
                }}
              >
                {intl.get(`${modelPrompt}.button.submit`).d('提交')}
              </PermissionButton>
            )}
            {['REVIEWING', 'REJECTED'].includes(productRevTaskStatus) && (
              <Dropdown
                overlay={menu}
                placement={Placements.bottomRight}
                disabled={!getCWritePermission()}
              >
                <PermissionButton
                  type="c7n-pro"
                  disabled={!getCWritePermission()}
                  permissionList={[
                    {
                      code: `${modelPrompt}.dist.button.submitCWrite`,
                      type: 'button',
                      meaning: '详情页-CWrite发布任务',
                    },
                  ]}
                  onClick={() => {
                    if (canEdit) {
                      handleSave(true);
                    } else {
                      handleSubmitTask();
                    }
                  }}
                >
                  {intl.get(`${modelPrompt}.button.submit`).d('提交')}
                </PermissionButton>
              </Dropdown>
            )}
            <PermissionButton
              type="c7n-pro"
              disabled={productRevTaskStatus !== 'CLOSED'}
              permissionList={[
                {
                  code: `${modelPrompt}.dist.button.generateReport`,
                  type: 'button',
                  meaning: '详情页-生成报告按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.generateReport`).d('生成报告')}
            </PermissionButton>
            <ApprovalInfoDrawer objectTypeList={['QIS_PRODUCT_REV_TASK']} objectId={kid} />
          </Header>
        )}
        <Content>
          <Collapse bordered={false} activeKey={activeKey} onChange={val => setActiveKey(val)}>
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.basicInfo`).d('基础信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="productRevTaskCode" />
                <Lov name="siteLov" onChange={handleChangeSite} />
                <TextField name="productRevPlanCode" />
                <Lov
                  name="productRevSchemeLov"
                  autoSelectSingle={false}
                  onChange={handleChangeRevScheme}
                />
                <Select name="productRevTaskStatus" />
                <Select name="productRevTaskType" />
                <TextField name="materialCode" />
                <TextField name="materialName" />
                <TextField name="cusMaterialCode" />
                <TextField name="cusMaterialName" />
                <TextField name="workcellCode" />
                <TextField name="workcellName" />
                <TextField name="samplePosition" />
                <Lov name="warehouseLov" />
                <NumberField name="sampleQty" />
                <TextField name="reviewAim" />
                <TextField name="reviewDec" />
                <Attachment {...enclosureProps} />
                <Lov name="publishedPersonLov" />
                <Lov name="reviewedPersonLov" />
                <DatePicker name="executeDate" />
                <TextField name="qeRejectReason" />
                <DateTimePicker name="qeRejectTime" />
              </Form>
            </Panel>
            <AddProjectRevItemButton
              canEdit={canEdit}
              productRevTaskStatus={productRevTaskStatus}
            />
            {itemTypeList.map((itemTypeItem: any, itemTypeIndex: number) => (
              <Panel
                key={itemTypeItem?.productReviewItemType}
                header={itemTypeItem?.productReviewItemTypeDesc}
              >
                <Table
                  dataSet={itemTypeItem.productRevItemDs}
                  columns={getItemColumns(
                    itemTypeItem.productRevItemDs,
                    itemTypeItem?.productReviewItemType,
                    itemTypeIndex,
                    canEdit,
                  )}
                />
              </Panel>
            ))}
            {!['NEW', 'TO_ACCEPT', 'EXECUTING', 'REVIEW_REJECT', 'CANCEL'].includes(
              productRevTaskStatus,
            ) && (
              <Panel
                key="measureInfo"
                header={intl.get(`${modelPrompt}.title.measureInfo`).d('后续措施')}
              >
                <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                  <TextField name="qkz" />
                  <Select name="reviewResult" />
                </Form>
                <Form
                  dataSet={detailDs}
                  disabled={!canEdit}
                  labelLayout={LabelLayout.none}
                  showValidation={ShowValidation.tooltip}
                >
                  <Row>
                    <Col span={2}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.furtherStepsFlag`).d('需要进一步采取措施')}
                      </span>
                    </Col>
                    <Col span={2}>
                      <SelectBox
                        name="furtherStepsFlag"
                        onChange={() => handleChangeMeasureFlag('furtherSteps')}
                      />
                    </Col>
                    <Col span={20}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.by`).d('由')}
                      </span>
                      <Lov name="furtherStepsExecutorLov" />
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.at`).d('在')}
                      </span>
                      <span className={styles['form-loc-input']}>
                        <TextField name="furtherStepsExecuteLoc" />
                      </span>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.implement`).d('立即实施')}
                      </span>
                      <span className={styles['form-measure-input']}>
                        <TextField name="furtherStepsExecuteCon" />
                      </span>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.measure`).d('措施')}
                      </span>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={2}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.invHoldFlag`).d('封存所有库存')}
                      </span>
                    </Col>
                    <Col span={2}>
                      <SelectBox
                        name="invHoldFlag"
                        onChange={() => handleChangeMeasureFlag('invHold')}
                      />
                    </Col>
                    <Col span={20}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.by`).d('由')}
                      </span>
                      <Lov name="invHoldExecutorLov" />
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.at`).d('在')}
                      </span>
                      <span className={styles['form-loc-input']}>
                        <TextField name="invHoldExecuteLoc" />
                      </span>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.implement`).d('立即实施')}
                      </span>
                      <span className={styles['form-measure-input']}>
                        <TextField name="invHoldExecuteCon" />
                      </span>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.measure`).d('措施')}
                      </span>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={2}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.dischargeMeasFlag`).d('采取排出措施')}
                      </span>
                    </Col>
                    <Col span={2}>
                      <SelectBox
                        name="dischargeMeasFlag"
                        onChange={() => handleChangeMeasureFlag('dischargeMeas')}
                      />
                    </Col>
                    <Col span={20}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.by`).d('由')}
                      </span>
                      <Lov name="dischargeMeasExecutorLov" />
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.at`).d('在')}
                      </span>
                      <span className={styles['form-loc-input']}>
                        <TextField name="dischargeMeasExecuteLoc" />
                      </span>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.implement`).d('立即实施')}
                      </span>
                      <span className={styles['form-measure-input']}>
                        <TextField name="dischargeMeasExecuteCon" />
                      </span>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.measure`).d('措施')}
                      </span>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={2}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.updateDrawingsFlag`).d('开始更新图纸/工艺')}
                      </span>
                    </Col>
                    <Col span={2}>
                      <SelectBox
                        name="updateDrawingsFlag"
                        onChange={() => handleChangeMeasureFlag('updateDrawings')}
                      />
                    </Col>
                    <Col span={20}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.by`).d('由')}
                      </span>
                      <Lov name="updateDrawingsExecutorLov" />
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.at`).d('在')}
                      </span>
                      <span className={styles['form-loc-input']}>
                        <TextField name="updateDrawingsExecuteLoc" />
                      </span>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.implement`).d('立即实施')}
                      </span>
                      <span className={styles['form-measure-input']}>
                        <TextField name="updateDrawingsExecuteCon" />
                      </span>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.measure`).d('措施')}
                      </span>
                    </Col>
                  </Row>
                </Form>
              </Panel>
            )}
            {!['NEW', 'TO_ACCEPT', 'EXECUTING', 'REVIEW_REJECT', 'CANCEL'].includes(
              productRevTaskStatus,
            ) && (
              <Panel
                key="disposalResult"
                header={intl.get(`${modelPrompt}.title.disposalResult`).d('处置结论')}
              >
                <Form dataSet={detailDs} disabled={!canEdit} labelWidth={112}>
                  <Row>
                    <Col span={2}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.sampleMaterialDisposal`).d('抽样品处理')}
                      </span>
                    </Col>
                    <Col span={22}>
                      <Row>
                        <Col span={4}>
                          <span className={styles['form-products-label']}>
                            {intl.get(`${modelPrompt}.goodProducts`).d('可出货产品条码')}
                          </span>
                        </Col>
                        <Col span={20}>
                          <span className={styles['form-products-input']}>
                            <TextField name="goodProducts" />
                          </span>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={4}>
                          <span className={styles['form-products-label']}>
                            {intl.get(`${modelPrompt}.badProducts`).d('不可出货产品条码')}
                          </span>
                        </Col>
                        <Col span={20}>
                          <span className={styles['form-products-input']}>
                            <TextField name="badProducts" />
                          </span>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={2}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.inventoryInspect`).d('库存检验')}
                      </span>
                    </Col>
                    <Col span={22}>
                      <Row>
                        <Col span={4}>
                          <span className={styles['form-label']}>
                            {intl.get(`${modelPrompt}.finalpInspFlag`).d('成品仓库检验')}
                          </span>
                        </Col>
                        <Col span={2}>
                          <SelectBox name="finalpInspFlag" />
                        </Col>
                        <Col span={18}>
                          <span className={styles['form-disposal-measure-input']}>
                            <TextField name="finalpInspCon" />
                          </span>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={4}>
                          <span className={styles['form-label']}>
                            {intl.get(`${modelPrompt}.wipInspFlag`).d('在制品检验')}
                          </span>
                        </Col>
                        <Col span={2}>
                          <SelectBox name="wipInspFlag" />
                        </Col>
                        <Col span={18}>
                          <span className={styles['form-disposal-measure-input']}>
                            <TextField name="wipInspCon" />
                          </span>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={2}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.change`).d('更改')}
                      </span>
                    </Col>
                    <Col span={22}>
                      <Row>
                        <Col span={4}>
                          <span className={styles['form-label']}>
                            {intl.get(`${modelPrompt}.drawingsChangeFlag`).d('图纸更改')}
                          </span>
                        </Col>
                        <Col span={2}>
                          <SelectBox name="drawingsChangeFlag" />
                        </Col>
                        <Col span={18}>
                          <span className={styles['form-disposal-measure-input']}>
                            <TextField name="drawingsChangeCon" />
                          </span>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={4}>
                          <span className={styles['form-label']}>
                            {intl.get(`${modelPrompt}.manuTechFlag`).d('生产工艺更改')}
                          </span>
                        </Col>
                        <Col span={2}>
                          <SelectBox name="manuTechFlag" />
                        </Col>
                        <Col span={18}>
                          <span className={styles['form-disposal-measure-input']}>
                            <TextField name="manuTechCon" />
                          </span>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={2}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.publishedProduct`).d('已发/已用产品')}
                      </span>
                    </Col>
                    <Col span={22}>
                      <Row>
                        <Col span={4}>
                          <span className={styles['form-label']}>
                            {intl.get(`${modelPrompt}.cusInformFlag`).d('顾客通知')}
                          </span>
                        </Col>
                        <Col span={2}>
                          <SelectBox name="cusInformFlag" />
                        </Col>
                        <Col span={18}>
                          <span className={styles['form-disposal-measure-input']}>
                            <TextField name="cusInformCon" />
                          </span>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={4}>
                          <span className={styles['form-label']}>
                            {intl.get(`${modelPrompt}.cusProdRetroFlag`).d('顾客处产品追溯')}
                          </span>
                        </Col>
                        <Col span={2}>
                          <SelectBox name="cusProdRetroFlag" />
                        </Col>
                        <Col span={18}>
                          <span className={styles['form-disposal-measure-input']}>
                            <TextField name="cusProdRetroCon" />
                          </span>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={2}>
                      <span className={styles['form-label']}>
                        {intl.get(`${modelPrompt}.label.preventMeasure`).d('预防/纠正措施')}
                      </span>
                    </Col>
                    <Col span={22}>
                      <Row>
                        <Col offset={4} span={2}>
                          <SelectBox name="precautionFlag" />
                        </Col>
                        <Col span={18}>
                          <span className={styles['form-disposal-measure-input']}>
                            <TextField name="precautionCon" />
                          </span>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </Form>
              </Panel>
            )}
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(ProductReviewTaskDetail);
