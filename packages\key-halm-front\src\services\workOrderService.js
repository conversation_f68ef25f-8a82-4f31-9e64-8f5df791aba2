/**
 * service - 工作单
 * @date: 2019-4-18
 * @author: QH <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2019, Hand
 */
import request from 'utils/request';
import { getCurrentOrganizationId, parseParameters, filterNullValueObject } from 'utils/utils';
// import { HZERO_PLATFORM } from 'utils/config';
import { HALM_MTC, HALM_MMT } from 'alm/utils/config';

const prefix = `${HALM_MTC}/v1`;
const organizationId = getCurrentOrganizationId();

/**
 * 查询列表数据
 * @async
 * @function queryWorkOrdersList
 * @param {Object} params - 查询参数
 * @param {String} params.page - 页码
 * @param {String} params.size - 页数
 */
export async function queryWorkOrdersList(params) {
  const query = filterNullValueObject(parseParameters(params));
  return request(`${prefix}/${organizationId}/work-orders`, {
    method: 'GET',
    query,
  });
}

export async function getLovList(params){
  const query = filterNullValueObject(parseParameters(params));
  return request(`/hpfm/v1/${params.tenantId}/lovs/sql/data`,{
    method: 'GET',
    query,
  })
}

/**
 * 保存
 * @async
 * @function saveData
 * @param {string} params - 查询参数
 * @param {string} params.tenantId - 租户Id
 */
export async function saveData(params) {
  return request(`${prefix}/${params.tenantId}/work-orders`, {
    method: 'POST',
    body: { ...params },
  });
}
/**
 * 修改工单状态
 * @async
 * @function changeWoStatus
 * @param {string} params - 查询参数
 * @param {string} params.tenantId - 租户Id
 */
export async function changeWoStatus(params) {
  return request(`${prefix}/${params.tenantId}/work-orders/changeStatus`, {
    method: 'POST',
    body: { ...params },
  });
}
/**
 * 删除
 * @async
 * @function deleteData
 * @param {string} params - 查询参数
 * @param {string} params.tenantId - 租户Id
 */
export async function deleteData(params) {
  return request(`${prefix}/${params.tenantId}/work-orders`, {
    method: 'DELETE',
    body: params.data,
  });
}
/**
 * 明细页面查询
 * @async
 * @function fetchDetailInfo
 * @param {string} params.tenantId - 租户id
 * @param {string} params.woId - id
 */
export async function fetchDetailInfo(params) {
  return request(`${prefix}/${params.tenantId}/work-orders/${params.woId}`, {
    method: 'GET',
    query: {
      ...params,
    },
  });
}

/**
 * 明细页面全文检索
 * @async
 * @function searchByFullText
 * @param {string} params - 查询参数
 * @param {string} params.tenantId - 租户Id
 */
export async function searchByFullText(params) {
  const param = parseParameters(params);
  return request(`${prefix}/${param.tenantId}/work-orders`, {
    method: 'GET',
    query: param,
  });
}

/**
 * deleteData - 删除数据
 * @export
 * @param {Object} params
 * @returns
 */
export async function deleteWoopData(params) {
  return request(`${prefix}/${params.tenantId}/woops`, {
    method: 'DELETE',
    body: { ...params },
  });
}
/**
 * 查询当前用户在当前租户下的员工
 * @async
 * @function getCurrentEmployee
 * @param {string} params - 查询参数
 * @param {string} params.tenantId - 租户Id
 */
export async function getCurrentEmployee(params) {
  const param = parseParameters(params);
  return request(`${prefix}/${param.tenantId}/employee-info`, {
    method: 'GET',
    query: { enabledFlag: 1 },
  });
}

/**
 * 查询当前用户对应的工作中心人员和组
 * @async
 * @function getCurrentWorkcenterStaff
 * @param {string} params - 查询参数
 * @param {string} params.tenantId - 租户Id
 */
export async function getCurrentWorkcenterStaff(params) {
  const param = parseParameters(params);
  return request(`${prefix}/${param.tenantId}/work-orders/current`, {
    method: 'GET',
    query: {
      ...param,
    },
  });
}

/**
 * 查询当前sr对应的工作中心人员和组
 * @async
 * @function getCurrentWorkcenterStaff
 * @param {string} params - 查询参数
 * @param {string} params.tenantId - 租户Id
 */
export async function getPlanner(params) {
  const param = parseParameters(params);
  return request(`${prefix}/${param.tenantId}/sr/workcenter-people`, {
    method: 'GET',
    query: {
      ...param,
    },
  });
}

/**
 * 查询当前wo对应的工作中心人员和组
 * @async
 * @function getCurrentWorkcenterStaff
 * @param {string} params - 查询参数
 * @param {string} params.tenantId - 租户Id
 */
export async function getOwner(params) {
  const param = parseParameters(params);
  return request(`${prefix}/${param.tenantId}/work-orders/workcenter-people`, {
    method: 'GET',
    query: {
      ...param,
    },
  });
}

/**
 * 查询列表数据
 * @async
 * @function queryWorkOrdersList
 * @param {Object} params - 查询参数
 * @param {String} params.page - 页码
 * @param {String} params.size - 页数
 */
export async function queryWorkOrdersReportList(params) {
  const query = filterNullValueObject(parseParameters(params));
  return request(`${prefix}/${organizationId}/work-orders/report`, {
    method: 'GET',
    query,
  });
}
/**
 * 查询工单后续单据列表数据
 * @async
 * @function queryWorkOrdersFollowList
 * @param {Object} params - 查询参数
 * @param {String} params.page - 页码
 * @param {String} params.size - 页数
 */
export async function queryWorkOrdersFollowList(params) {
  const query = filterNullValueObject(parseParameters(params));
  return request(`${prefix}/${organizationId}/work-orders/listFollow`, {
    method: 'GET',
    query,
  });
}

// 物料物料查询
export async function queryItemList(params) {
  const query = filterNullValueObject(parseParameters(params));
  return request(`${HALM_MMT}/v1/${organizationId}/item/condition/list`, {
    method: 'GET',
    query,
  });
}

// 物料物料下存库现有量
export async function queryOnhandQtyList(params) {
  return request(`${HALM_MMT}/v1/${organizationId}/onhand-quantity/item`, {
    method: 'GET',
    query: params,
  });
}

// 物料物料列表查询
export async function queryItem(params) {
  const query = filterNullValueObject(parseParameters(params));
  return request(`${HALM_MMT}/v1/${organizationId}/item`, {
    method: 'GET',
    query,
  });
}

/**
 * 处理接口获取的数据，提取每个节点的层次路径
 * @param {array} collections - 页面展示数据
 * @param {array} levelPath - 特定组织的层级路径
 * @returns {object} 节点树和层次路径组成的对象
 */
export function renderItemTreeData(
  collections = [],
  levelPath = {},
  parentItemId = '',
  children = []
) {
  const pathMap = levelPath;
  const treeList = collections.map(item => {
    let temp = item;
    if (temp.itemId === parentItemId) {
      const childrenTemp = children.map(i => {
        return { ...i, children: i.childFlag === 1 ? [] : null };
      });
      temp = { ...temp, children: childrenTemp };
    }
    return temp;
  });
  return {
    treeList,
    pathMap,
  };
}

// bom设备结构清单树渲染
export function renderTreeData(collections = [], levelPath = {}, parentItemId = '', children = []) {
  const pathMap = levelPath;
  const treeList = collections.map(item => {
    let temp = item;
    pathMap[temp.itemId] = [...(pathMap[temp.parentItemId] || []), temp.itemId];
    if (temp.children) {
      temp.children = [
        ...renderTreeData(temp.children || [], pathMap, parentItemId, children).treeList,
      ];
    }
    if (temp.itemId === parentItemId) {
      const childrenTemp = children.map(i => {
        // 子级节点层次路径
        pathMap[i.itemId] = [...(pathMap[i.parentItemId] || []), i.itemId];
        return { ...i, children: i.childFlag === 1 ? [] : null };
      });
      temp = { ...temp, children: [...temp.children, ...childrenTemp] };
    }

    return temp;
  });
  return {
    treeList,
    pathMap,
  };
}

// 查询关联Sr列表
export async function fetchRelateSr(params) {
  const param = filterNullValueObject(parseParameters(params));
  return request(`${HALM_MTC}/v1/${organizationId}/sr`, {
    method: 'GET',
    query: param,
  });
}

// 模糊查询Sr列表
export async function fetchSrSelectPopup(params) {
  const param = filterNullValueObject(parseParameters(params));
  return request(`${HALM_MTC}/v1/${organizationId}/sr`, {
    method: 'GET',
    query: param,
  });
}

/**
 * 更新工单
 * @async
 * @function changeWOInfo
 * @param {string} params - 查询参数
 * @param {string} params.tenantId - 租户Id
 */
export async function changeWOInfo(params) {
  return request(`${HALM_MTC}/v1/${organizationId}/work-orders`, {
    method: 'POST',
    body: { ...params },
  });
}

/**
 * 工单提交
 * @async
 * @function submitWo
 * @param {object} params - 请求参数
 * @param {!string} params.tenantId - 租户Id
 * @param {!string} params.data - 工作任务信息
 * @returns {object} fetch Promise
 */
export async function submitWo(params) {
  return request(`${prefix}/${params.tenantId}/work-orders/submit`, {
    method: 'POST',
    body: { ...params },
  });
}

// 获取首个标准任务
export async function getFirstActOp(params) {
  const param = filterNullValueObject(parseParameters(params));
  return request(`${prefix}/${organizationId}/actOp/first-act-op/${params.actId}`, {
    method: 'GET',
    query: param,
  });
}

/**
 * 校验工单物料需求数量跟已发数量
 * @async
 * @function validWoMaterialCount
 * @param {object} params - 请求参数
 * @param {!string} params.tenantId - 租户Id
 * @param {!string} params.data - 工作任务信息
 * @returns {object} fetch Promise
 */

export async function validWoMaterialCount(params) {
  return request(`${prefix}/${organizationId}/wo-material/valid-wo-material-count/${params.woId}`, {
    method: 'GET',
  });
}

/**
 * 校验检查项故障
 * @async
 * @function validWChecklistMalfunction
 * @param {object} params - 请求参数
 * @param {!string} params.tenantId - 租户Id
 * @param {!string} params.data - 工作任务信息
 * @returns {object} fetch Promise
 */

export async function validWChecklistMalfunction(params) {
  return request(`${prefix}/${organizationId}/work-orders/valid-checkList-malfunction`, {
    method: 'POST',
    body: { ...params },
  });
}

// 刷新物料结算信息
export async function refreshMaterialsSettle(params) {
  return request(`${prefix}/${organizationId}/wo-material-billinginfos/related/agreement`, {
    method: 'PUT',
    body: params,
  });
}
