import { useState, useEffect } from 'react';

export const useResizeObserver = (callback, curDom) => {
  const [size, setSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        const { width, height } = entry.contentRect;
        if(size.width === width){
          return
        }
        setSize({ width, height });
        callback(entry.target, { width, height });
      });
    });

    const handleResize = () => {
      if(curDom) observer.observe(curDom);
      // observer.observe(document.querySelector(`#${id}`));
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
      observer.disconnect();
    };
  }, [callback]); // 注意这里使用依赖项数组来确保回调函数的变化会导致重新运行 useEffect 钩子。

  return [size];
}
