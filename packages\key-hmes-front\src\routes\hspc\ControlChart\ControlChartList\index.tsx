/**
 * @Description: 控制控制图-列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-11-15 14:29:27
 * @LastEditTime: 2023-04-03 16:51:41
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useCallback } from 'react';
import { DataSet, Table, Menu, Dropdown, Modal, Tooltip } from 'choerodon-ui/pro';
import { Badge, Popconfirm, Icon } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExport';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import queryString from 'querystring';
import withProps from 'utils/withProps';
import { openTab } from 'utils/menuTab';
import { drawerPropsC7n } from 'hcm-components-front/lib/components/tarzan-ui';
import { BASIC } from 'hcm-components-front/lib/utils/config';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import AutomaticAccess from '../DataAccess/AutomaticAccess';
import { tableDS, automaticAccessDS } from '../stores/ControlChartDS';
import { formDS } from '../stores/formDS';
import request from "utils/request";

const modelPrompt = 'tarzan.hspc.controlChartMaintain';
const tenantId = getCurrentOrganizationId();

const ControlChartList = (props: any) => {
  const {
    tableDs,
    match: { path },
  } = props;

  const sourceMarkSave = useRequest(
    {
      url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/source-mark/save/ui`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  const automaticAccessDs = useMemo(() => new DataSet({ ...automaticAccessDS() }), []);
  const formDs = useMemo(() => new DataSet(formDS()), []);

  useEffect(() => {
    tableDs.query(props.tableDs.currentPage);
  }, []);

  const goHistoricalChart = record => {
    const url = `/hspc/control-chart/history-chart/${record.get('controlId')}`;
    openTab({
      icon: '',
      title: intl.get(`${modelPrompt}.historyGraphicDisplay`).d('历史图形展示'),
      key: url,
      path: url,
      closable: true,
    });
  };

  const goAlarmMessage = record => {
    const url = `/hspc/control-chart/alarm-message/${record.get('controlId')}`;
    openTab({
      icon: '',
      title: intl.get(`${modelPrompt}.alarmMessageConfig`).d('报警消息配置'),
      key: url,
      path: url,
      closable: true,
    });
  };

  const handleDrawerConfirm = async () => {
    automaticAccessDs!.current!.set({ nowDate: new Date().getTime() });
    const validate = await automaticAccessDs.validate();
    const validateForm = await formDs.validate();
    if (!validate || !validateForm) {
      return false;
    }
    const data1 = formDs.toData()[0];
    const data2 = automaticAccessDs.toData()[0];
    const res = await sourceMarkSave.run({
      params: {
        ...data2,
        ...data1,
      },
    });

    if (res?.success) {
      notification.success({});
      tableDs.query(tableDs.currentPage);
      return true;
    }
    return false;
  };

  const tooltipText = (
    <p style={{ width: '280px', fontSize: '14px', lineHeight: '25px' }}>
      {intl
        .get(`${modelPrompt}.rightApiTips`)
        .d(
          '右侧展示基于当前控制控制图数据的接入API对应的请求和返回示例；API详细说明参见燕千云说明文档-',
        )}
      <a
        href="https://open.hand-china.com/publish-center/product/product-detail/10072/version/10130/document?docVersion=2.0&doc_id=22393"
        target="_blank"
        rel="noopener noreferrer"
      >
        《sampleDataAccess》
      </a>
    </p>
  );

  const handleManualDataEntry = kid => {
    const url = `/hspc/control-chart/manual-access/${kid}`;
    openTab({
      icon: '',
      title: intl.get(`${modelPrompt}.manualDataAccess`).d('数据手工录入'),
      key: url,
      path: url,
      closable: true,
    });
  };

  const handleAutomaticDataAccess = record => {
    request(`/tznm/v1/${tenantId}/mt-mod-site/user-organization/site/lov/ui?page=0&size=1000`, {
      method: 'GET',
    }).then(res => {
      if (res && res.content && res.content.length === 1) {
        formDs.current?.set('siteLov',
          {
            siteId: res.content[0].siteId,
            siteCode: res.content[0].siteCode,
            siteName: res.content[0].siteName,
          });
      } else {
        formDs.loadData([{}]);
      }
    });
    automaticAccessDs.loadData([
      {
        sourceMark: record.get('sourceMark'),
        sourceMarkFlag: record.get('sourceMarkFlag'),
        controlId: record.get('controlId'),
      },
    ]);
    Modal.open({
      ...drawerPropsC7n({ ds: automaticAccessDs }),
      key: Modal.key(),
      drawer: false,
      title: (
        <div>
          <span style={{ fontSize: '14px' }}>
            {intl.get(`${modelPrompt}.automaticDataAccess`).d('数据自动接入')}
          </span>
          <Tooltip placement="topLeft" title={tooltipText} theme="dark">
            <Icon
              type="help_outline"
              style={{
                marginBottom: '3px',
                color: 'black',
                opacity: '0.3',
                width: '18px',
                height: '18px',
              }}
            />
            &nbsp;&nbsp;&nbsp;&nbsp;
          </Tooltip>
        </div>
      ),
      style: {
        width: '1000px',
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      children: (
        <AutomaticAccess ds={automaticAccessDs} formDs={formDs} dataSource={record.toData()} />
      ),
      onOk: handleDrawerConfirm,
    });
  };

  const operationRender = record => {
    const menu = (
      <Menu>
        <Menu.Item key="historyGraph">
          <a onClick={() => goHistoricalChart(record)}>
            {intl.get(`${modelPrompt}.historicalGraphQuery`).d('历史图形查询')}
          </a>
        </Menu.Item>
        <Menu.Item key="alarmMessage">
          <PermissionButton
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
            type="text"
            onClick={() => goAlarmMessage(record)}
          >
            {intl.get(`${modelPrompt}.alarmMessage`).d('报警消息')}
          </PermissionButton>
        </Menu.Item>
        {record.get('accessType') === 'MANUAL' && (
          <Menu.Item key="dataAccess">
            <PermissionButton
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
              type="text"
              onClick={() => handleManualDataEntry(record.get('controlId'))}
            >
              {intl.get(`${modelPrompt}.manualDataAccess`).d('数据手工录入')}
            </PermissionButton>
          </Menu.Item>
        )}
        {record.get('accessType') === 'AUTOMATIC' && (
          <Menu.Item key="autoDataAccess">
            <PermissionButton
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
              type="text"
              onClick={() => handleAutomaticDataAccess(record)}
            >
              {intl.get(`${modelPrompt}.automaticDataAccess`).d('数据自动接入')}
            </PermissionButton>
          </Menu.Item>
        )}
        <Menu.Item key="delete">
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDelete(record)}
          >
            <PermissionButton
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
              type="text"
            >
              {intl.get('tarzan.common.button.delete').d('删除')}
            </PermissionButton>
          </Popconfirm>
        </Menu.Item>
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a>
          {intl.get(`${modelPrompt}.more`).d('更多操作')}
          <Icon type="expand_more" />
        </a>
      </Dropdown>
    );
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'serialNumber',
        align: ColumnAlign.center,
        width: 80,
        title: intl.get('tarzan.common.label.serialNumber').d('序号'),
        lock: ColumnLock.left,
        renderer: ({ record }) => {
          if (record) {
            return record?.index + 1;
          }
        },
      },
      {
        name: 'controlCode',
        width: 240,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return <a onClick={() => handleClickToDetailPage(record)}>{value}</a>;
        },
      },
      {
        name: 'controlDesc',
        width: 480,
      },
      {
        name: 'enableFlag',
        width: 100,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'accessTypeDesc',
        width: 200,
      },
      {
        name: 'chartTypeDesc',
        width: 200,
      },
      {
        name: 'subgroupSize',
        width: 200,
      },
      {
        name: 'maxPlotPoints',
        width: 160,
      },
      {
        name: 'analysisCode',
        width: 200,
      },
      {
        name: 'serviceCode',
        width: 200,
      },
      {
        name: 'chartTitle',
        width: 200,
      },
      {
        name: 'samplingTypeDesc',
        width: 100,
      },
      {
        name: 'groupBatchRuleDesc',
        width: 120,
      },
      {
        name: 'timeGroupBatch',
        width: 100,
      },
      {
        name: 'sampleGroupBatch',
        width: 100,
      },
      {
        name: 'unqualifiedGroupBatch',
        width: 120,
      },
      {
        name: 'samplingPositionDesc',
        width: 100,
      },
      {
        name: 'dataUpperLimit',
        width: 100,
      },
      {
        name: 'dataLowerLimit',
        width: 100,
      },
      {
        name: 'timeSampling',
        width: 100,
      },
      {
        name: 'timeSamplingNumber',
        width: 100,
      },
      {
        name: 'isometricSampling',
        width: 100,
      },
      {
        name: 'isometricSamplingNumber',
        width: 100,
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        width: 260,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          return (
            <span className="action-link">
              <a onClick={() => handleClickToEnterDetailPage(record)}>
                {intl.get(`${modelPrompt}.realTimeGraphicDisplay`).d('实时图形展示')}
              </a>
              {operationRender(record)}
            </span>
          );
        },
      },
    ],
    [],
  );

  const handleClickToDetailPage = record => {
    props.history.push(`/hspc/control-chart/detail/${record.get('controlId')}`);
  };

  const handleClickToEnterDetailPage = record => {
    const url = `/hspc/control-chart/display/${record.get('controlId')}`;
    openTab({
      icon: '',
      title: intl.get(`${modelPrompt}.realTimeGraphicDisplay`).d('实时图形展示'),
      key: url,
      path: url,
      closable: true,
    });
  };

  const handleDelete = useCallback(record => {
    // 设为false时不弹确认直接删除
    tableDs.delete([record], false).then(res => {
      tableDs.query(tableDs.currentPage);
      if (res && res.rows && res.rows.content.length > 0 && res.rows.content[0].success) {
        notification.success({});
      }
    });
  }, []);

  const getExportQueryParams = () => {
    // const { selected } = tableDs;
    const queryParams = tableDs.queryDataSet?.toData()[0] || {};
    return {
      ...queryParams,
    };
    // if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
    //   return {};
    // }
    // return {
    //   id: selected.map(item => item.get('id')),
    // };
  };

  const handleCreate = () => {
    props.history.push('/hspc/control-chart/create-page/new');
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/MT.SPC.CONTROL',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.controlChartMaintain`).d('控制控制图维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <ExcelExport
          method="GET"
          requestUrl={`${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
          icon="file_upload"
          onClick={goImport}
        >
          {intl.get('tarzan.common.button.import').d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          searchCode="kzkztwh1"
          customizedCode="kzkztwh1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hspc.controlChartMaintain', 'tarzan.hspc.chartInfo', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, keepOriginDataSet: true },
  )(ControlChartList),
);
