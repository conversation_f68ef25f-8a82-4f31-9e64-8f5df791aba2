import React, { useEffect, useMemo, useState, useRef } from 'react';
import {
  DataSet,
  Table,
  Button,
  Form,
  TextField,
  Lov,
  DateTimePicker,
  Attachment,
  TextArea,
  Dropdown,
  Select,
  Switch,
  Icon,
  Menu,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';
import { Steps, Popconfirm, Collapse, Badge, Popover } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from 'utils/intl';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import moment from 'moment/moment';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { getCurrentUser } from 'utils/utils';
import { observer } from 'mobx-react';
import { detailBasicDS, eventAxisDS, improvementDS } from '../stores/index';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import {
  fetchProblemReplayConfig,
  savePproblemReplayConfig,
  updatePproblemReplayConfig,
  issuedPproblemReplayConfig,
  publishPproblemReplayConfig,
  updatePproblemReplayAnalysisConfig,
  updatePproblemReplayEventConfig,
  submitPproblemReplayConfig,
} from '../services';

import styles from './index.module.less';

const Step = Steps.Step;
const { Panel } = Collapse;
const modelPrompt = 'tarzan.duplicateProblem';

const DuplicateProblem = props => {
  const {
    match: {
      path,
      params: { id },
    },
    history,
  } = props;

  const stepRowRef = useRef<any>();

  const [user] = useState(getCurrentUser()); // 用户详细信息

  // 头编辑状态
  const [headerEdit, setHeaderEdit] = useState(false);
  const [eventAxisEdit, setEventAxisEdit] = useState(false);
  const [improvementEdit, setImprovementEdit] = useState(false);
  // 事件轴选中行
  const [eventTablcIndex, setEventTablcIndex] = useState(-1);

  // 基础信息
  const detailBasicDs = useMemo(() => new DataSet(detailBasicDS()), []);
  // 事件轴
  const eventAxisDs = useMemo(() => new DataSet(eventAxisDS()), []);
  // 原因分析及改进
  const improvementDs = useMemo(() => new DataSet(improvementDS()), []);

  // 问题复盘详情
  const fetchProblemReplay = useRequest(fetchProblemReplayConfig(), {
    manual: true,
    needPromise: true,
  });
  // 问题复盘新建
  const savePproblemReplay = useRequest(savePproblemReplayConfig(), {
    manual: true,
    needPromise: true,
  });
  // 问题复盘更新
  const updatePproblemReplay = useRequest(updatePproblemReplayConfig(), {
    manual: true,
    needPromise: true,
  });
  // 问题复盘下达
  const issuedPproblemReplay = useRequest(issuedPproblemReplayConfig(), {
    manual: true,
    needPromise: true,
  });
  // 问题复盘发布
  const publishPproblemReplay = useRequest(publishPproblemReplayConfig(), {
    manual: true,
    needPromise: true,
  });
  // 问题复盘-原因分析及措施改进-新建
  const updatePproblemReplayAnalysis = useRequest(updatePproblemReplayAnalysisConfig(), {
    manual: true,
    needPromise: true,
  });
  // 问题复盘-事件轴-新建
  const updatePproblemReplayEvent = useRequest(updatePproblemReplayEventConfig(), {
    manual: true,
    needPromise: true,
  });
  // 问题复盘-提交审核
  const submitPproblemReplay = useRequest(submitPproblemReplayConfig(), {
    manual: true,
    needPromise: true,
  });

  // 初始化页面
  useEffect(() => {
    if (id === 'create') {
      setHeaderEdit(true);
      setEventAxisEdit(false);
      setImprovementEdit(false);
      detailBasicDs.loadData([
        {
          problemReplayStatus: 'NEW',
          createdLov: user,
          eventAxis: [],
          improvement: [],
        },
      ]);
      eventAxisDs.loadData([]);
      improvementDs.loadData([]);

      // 问题管理验证关闭 -> 问题复盘
      const lovObj = props.location.state;
      if (lovObj && lovObj?.sourcePage) {
        detailBasicDs.current?.set('siteLov', {
          siteId: lovObj.siteId,
          siteCode: lovObj.siteCode,
          siteName: lovObj.siteName,
        });
        detailBasicDs.current?.set('problemLov', {
          problemId: lovObj.problemId,
          problemCode: lovObj.problemCode,
          problemTitle: lovObj.problemTitle,
        });
      }
    } else {
      initPage(id);
    }
  }, [id]);

  const initPage = async key => {
    setHeaderEdit(false);
    setEventAxisEdit(false);
    setImprovementEdit(false);
    setEventTablcIndex(-1);
    const res = await fetchProblemReplay.run({
      tempUrl: fetchProblemReplayConfig(key).url,
    });
    if (res?.success) {
      let qisProblemReplayAnalysisListSum: any = [];
      const { qisProblemReplayEventList, ...othre } = res.rows || {};
      const _qisProblemReplayEventList: any = [];
      (qisProblemReplayEventList || []).forEach(item => {
        const qisProblemReplayAnalysisList = (item.qisProblemReplayAnalysisList || []).map(
          subItem => {
            return {
              ...subItem,
              problemReplayEventSequence: item.sequence,
            };
          },
        );

        _qisProblemReplayEventList.push({
          ...item,
          responsiblePerson: (item.responsiblePerson || '').split(','),
          responsiblePersonName: (item.responsiblePersonName || '').split(','),
          qisProblemReplayAnalysisList,
        });

        qisProblemReplayAnalysisListSum = [
          ...qisProblemReplayAnalysisListSum,
          ...qisProblemReplayAnalysisList,
        ];
      });
      detailBasicDs.loadData([othre]);
      eventAxisDs.loadData(_qisProblemReplayEventList);

      improvementDs.loadData(qisProblemReplayAnalysisListSum);
    } else {
      detailBasicDs.loadData([{}]);
      eventAxisDs.loadData([]);
      improvementDs.loadData([]);
    }
  };

  // 添加小组行
  const addScheduleRow = () => {
    eventAxisDs.create({
      differenceFlag: 'N',
    });
    setTimeout(() => {
      stepChange(eventAxisDs.length - 1);
    }, 0);
  };

  // 删除小组行
  const deleteScheduleRow = record => {
    eventAxisDs.delete(record, false);
  };

  // 事件轴
  const eventAxisColumns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!eventAxisEdit}
          funcType="flat"
          shape="circle"
          size="small"
          onClick={addScheduleRow}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      hidden: !eventAxisEdit,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteScheduleRow(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!eventAxisEdit}
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      width: 60,
      align: ColumnAlign.left,
      renderer: ({ record }) => {
        return <span>{record && record.index * 10 + 10}</span>;
      },
    },

    {
      name: 'occurTime',
      editor: eventAxisEdit,
    },
    {
      name: 'stepDesc',
      editor: eventAxisEdit,
      width: 200,
    },
    {
      name: 'specification',
      editor: eventAxisEdit,
    },
    {
      name: 'usedToDo',
      editor: eventAxisEdit,
    },
    {
      name: 'difference',
      editor: eventAxisEdit,
    },
    {
      name: 'differenceFlag',
      editor: eventAxisEdit && <Switch />,
      width: 140,
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'responsiblePersonLov',
      editor: eventAxisEdit,
    },
    {
      name: 'enclosure',
      editor: eventAxisEdit,
      renderer: ({ record }) => (
        <Attachment
          name="enclosure"
          record={record}
          buttons={[['download', { disabled: changeDisabled(record, 'eventAxisColumns') }]]}
          readOnly={!eventAxisEdit}
          {...attachmentProps}
          viewMode={viewModeChang(eventAxisDs, 'eventAxisColumns')}
        />
      ),
    },
  ];

  const changeDisabled = (record, type) => {
    const createBy = detailBasicDs.current?.get('createdBy');
    if (type === 'eventAxisColumns') {
      const responsiblePerson = record.get('responsiblePerson');
      if (responsiblePerson.includes(`${user.id}`) || Number(createBy) === user.id) {
        return false;
      }
      return true;
    }
    const responsiblePerson = record.get('responsibleUserId');
    if (responsiblePerson === user.id || Number(createBy) === user.id) {
      return false;
    }
    return true;
  };

  const attachmentProps: any = {
    bucketName: 'qms',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
  };

  const viewModeChang = (ds, type) => {
    const createBy = detailBasicDs.current?.get('createdBy');
    const principalUser = detailBasicDs.current?.get('principalUserId');
    if (type === 'eventAxisColumns') {
      // const responsiblePerson = ds.get('responsiblePerson');
      const responsiblePerson = ds.toData().map(item => item.responsiblePerson);
      const _responsiblePerson = [].concat(...responsiblePerson)
      if (
        Number(createBy) === user.id ||
        Number(principalUser) === user.id ||
        _responsiblePerson.includes(`${user.id}`)
      ) {
        return 'popup';
      }
      return 'none';
    }
    // const responsiblePerson = ds.get('responsibleUserId');
    const responsiblePerson = ds.toData().map(item => item.responsibleUserId);
    if (
      Number(createBy) === user.id ||
      Number(principalUser) === user.id ||
      responsiblePerson.includes(user.id)
    ) {
      return 'popup';
    }
    return 'none';
  };

  const viewModeFromChang = () => {
    const createBy = detailBasicDs.current?.get('createdBy');
    const principalUser = detailBasicDs.current?.get('principalUserId');
    const responsiblePerson = [
      ...new Set(
        [].concat(...eventAxisDs.map(record => record.get('responsiblePerson'))),
      ),
    ];
    if (Number(createBy) === user.id || Number(principalUser) === user.id || responsiblePerson.includes(`${user.id}`)) {
      return 'popup';
    }
    return 'none';
  };

  // 添加小组行
  const addImprovementRow = () => {
    const currentEventRecord = eventAxisDs.get(eventTablcIndex);
    improvementDs.create({
      responsibleUserLov: user,
      problemReplayEventId: currentEventRecord?.get('problemReplayEventId'),
      problemReplayEventSequence: currentEventRecord?.get('sequence'),
      reasonRecordTime: new Date(),
    });
  };

  // 删除小组行
  const deleteImprovementRow = record => {
    improvementDs.delete(record, false);
  };

  const improvementColumns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!improvementEdit}
          funcType="flat"
          shape="circle"
          size="small"
          onClick={addImprovementRow}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      hidden: !improvementEdit,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteImprovementRow(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!improvementEdit}
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      width: 60,
      align: ColumnAlign.left,
      renderer: ({ record }) => {
        return <span>{record && record.index * 10 + 10}</span>;
      },
    },

    {
      name: 'problemReplayEventSequence',
    },
    {
      name: 'responsibleUserLov',
    },
    {
      name: 'reason',
      editor: improvementEdit,
    },
    {
      name: 'replayAnalysisType',
      editor: improvementEdit,
    },
    {
      name: 'reasonRecordTime',
    },
    {
      name: 'measure',
      editor: improvementEdit,
    },
    {
      name: 'measureRecordTime',
    },

    {
      name: 'measureEnclosure',
      editor: improvementEdit,
      renderer: ({ record }) => (
        <Attachment
          name="measureEnclosure"
          record={record}
          buttons={[['download', { disabled: changeDisabled(record, 'improvementColumns') }]]}
          readOnly={!eventAxisEdit}
          {...attachmentProps}
          viewMode={viewModeChang(improvementDs, 'improvementColumns')}
        />
      ),
    },
  ];

  const handleSave = async () => {
    const pageValidate = await detailBasicDs.validate();
    if (!pageValidate) {
      return;
    }

    const params = detailBasicDs.toData()[0];

    let res;

    if (id === 'create') {
      res = await savePproblemReplay.run({
        params,
      });
    } else {
      res = await updatePproblemReplay.run({
        params,
      });
    }

    if (res?.success) {
      notification.success({});

      if (id === 'create') {
        history.push(
          `/hwms/problem-management/duplicate-problem/detail/${res?.rows?.problemReplayId}`,
        );
      } else {
        initPage(id);
      }
    }
  };

  // 取消
  const handleCancel = () => {
    if (id === 'create') {
      history.push('/hwms/problem-management/duplicate-problem/list');
    } else {
      setHeaderEdit(false);
      initPage(id);
    }
  };

  // 事件轴列表保存
  const handleEventAxisSave = async () => {
    const eventAxisValidate = await eventAxisDs.validate();
    if (!eventAxisValidate) {
      return;
    }

    const params = eventAxisDs.toData().map((item: any, index) => {
      return {
        ...item,
        sequence: index * 10 + 10,
        responsiblePerson: (item.responsiblePerson || []).join(','),
        responsiblePersonName: (item.responsiblePersonName || []).join(','),
      };
    });

    const res = await updatePproblemReplayEvent.run({
      queryParams: { problemReplayId: id },
      params,
    });

    if (res?.success) {
      notification.success({});
      initPage(id);
    }
  };

  // 事件轴列表取消编辑
  const handleEventAxisCancel = () => {
    setEventAxisEdit(prev => !prev);
    initPage(id);
  };

  // 原因分析及措施改进列表保存
  const handleImprovementSave = async () => {
    const improvementValidate = await improvementDs.validate();
    if (!improvementValidate) {
      return;
    }

    const params = improvementDs.toData().map((item: any, index) => {
      return {
        ...item,
        sequence: index * 10 + 10,
      };
    });

    const res = await updatePproblemReplayAnalysis.run({
      queryParams: {
        problemReplayId: id,
        problemReplayEventId: eventAxisDs.get(eventTablcIndex)?.get('problemReplayEventId'),
      },
      params,
    });

    if (res?.success) {
      notification.success({});
      initPage(id);
    }
  };

  // 原因分析及措施改进取消编辑
  const handleImprovementCancel = () => {
    setImprovementEdit(prev => !prev);
    initPage(id);
  };

  // 发布
  const handlePublish = async () => {
    const res = await publishPproblemReplay.run({
      params: id,
    });
    if (res?.success) {
      notification.success({});
      initPage(id);
    }
  };
  // 下达
  const handleIssued = async () => {
    const res = await issuedPproblemReplay.run({
      params: id,
    });
    if (res?.success) {
      notification.success({});
      initPage(id);
    }
  };

  // 提交审核
  const handleSubmit = async () => {
    const res = await submitPproblemReplay.run({
      params: id,
    });
    if (res?.success) {
      notification.success({});
      initPage(id);
    }
  };

  const headerEditAuth = () => {
    if (id === 'create') {
      return true;
    }
    if (
      `${user.id}` === `${detailBasicDs.current?.get('createdBy')}` &&
      detailBasicDs.current?.get('problemReplayStatus') === 'NEW'
    ) {
      return true;
    }
    return false;
  };

  // 审核权限
  const submitAuth = () => {
    let auth = true;

    if (`${user.id}` !== `${detailBasicDs.current?.get('principalUserId')}`) {
      auth = false;
    }

    if (detailBasicDs.current?.get('problemReplayStatus') !== 'EXECUTING') {
      auth = false;
    }

    if (eventAxisDs.length === 0) {
      auth = false;
    }

    eventAxisDs.forEach(record => {
      const qisProblemReplayAnalysisList = record.get('qisProblemReplayAnalysisList');
      const responsiblePerson = record.get('responsiblePerson');
      if ((qisProblemReplayAnalysisList || []).length === 0) {
        if ((responsiblePerson || []).length > 0) {
          auth = false;
        }
      } else {
        qisProblemReplayAnalysisList.forEach(subItem => {
          if (!subItem.reason || !subItem.measure) {
            auth = false;
          }
        });
      }
    });

    return auth;
  };

  // 发布权限
  const publishAuth = () => {
    if (
      `${user.id}` === `${detailBasicDs.current?.get('createdBy')}` &&
      detailBasicDs.current?.get('problemReplayStatus') === 'NEW'
    ) {
      return true;
    }
    return false;
  };

  // 下达权限
  const issuedAuth = () => {
    if (
      `${user.id}` === `${detailBasicDs.current?.get('principalUserId')}` &&
      detailBasicDs.current?.get('problemReplayStatus') === 'TO_EXECUTE'
    ) {
      return true;
    }
    return false;
  };

  const eventAxisEditAuth = () => {
    if (id === 'create') {
      return false;
    }
    if (
      `${user.id}` === `${detailBasicDs.current?.get('principalUserId')}` &&
      detailBasicDs.current?.get('problemReplayStatus') === 'TO_EXECUTE'
    ) {
      return true;
    }
    return false;
  };

  const improvementEditAuth = () => {
    if (id === 'create') {
      return false;
    }

    if (eventTablcIndex === -1) {
      return false;
    }

    if (
      eventAxisDs
        .get(eventTablcIndex)
        ?.get('responsiblePerson')
        .includes(`${user.id}`) &&
      detailBasicDs.current?.get('problemReplayStatus') === 'EXECUTING'
    ) {
      return true;
    }
    return false;
  };

  const stepHeader = () => {
    return <></>;
  };
  const stepIcon = () => {
    return <></>;
  };
  const stepTitle = ({ record, index }) => {
    return eventTablcIndex === index ? (
      <div className={styles['title-icon']}>
        {record.get('differenceFlag') === 'Y' ? (
          <Icon className={styles.active} type="star-fil" />
        ) : (
          <Icon className={styles.active} type="question_answer" />
        )}
      </div>
    ) : (
      <div className={styles['title-icon']}>
        {record.get('differenceFlag') === 'Y' ? (
          <Icon type="grade-o" />
        ) : (
          <Icon type="question_answer-o" />
        )}
      </div>
    );
  };
  const stepDescription = ({ record, index }) => {
    return (
      <div className={eventTablcIndex === index ? styles.active : ''}>
        <div className={styles.title}>
          {record.get('occurTime')
            ? moment(record.get('occurTime')).format('YYYY-MM-DD HH:mm:ss')
            : ''}
        </div>
        <div>
          <Popover content={`${record.get('stepDesc') || ''}`}>
            {intl.get(`${modelPrompt}.event`).d('事件')}{`${(index || 0) + 1}:${record.get('stepDesc') || ''}`}
          </Popover>
        </div>
        <div>
          <Popover content={`${record.get('specification') || ''}`}>
            {intl.get(`${modelPrompt}.norm`).d('规范')}{`:${record.get('specification') || ''}`}
          </Popover>
        </div>
        <div>
          <Popover content={`${record.get('usedToDo') || ''}`}>
            {intl.get(`${modelPrompt}.how.to.do.before`).d('以往如何做')}{`:${record.get('usedToDo') || ''}`}
          </Popover>
        </div>
        <div>
          <Popover content={`${record.get('difference') || ''}`}>
            {intl.get(`${modelPrompt}.difference`).d('不同点')}{`:${record.get('difference') || ''}`}
          </Popover>
        </div>
      </div>
    );
  };

  const stepChange = value => {
    setEventTablcIndex(value);
    if (value === -1) {
      improvementDsReset();
    } else {
      stepRowRef.current?.scrollTo((value || 0) * 200, 0);
      const focusRowData = eventAxisDs.get(value)?.toData() || {};
      improvementDs.loadData(focusRowData.qisProblemReplayAnalysisList || []);
    }
  };

  const setpClick = value => {
    if (!improvementEdit) {
      stepChange(value);
    }
  };

  const StepRow = observer(props => {
    const { dataSet } = props;
    return (
      <Steps
        onChange={setpClick}
        headerRender={stepHeader}
        size="small"
        current={-1}
        className={styles['steps-box']}
      >
        {dataSet.map((record, index) => (
          <Step
            icon={stepIcon()}
            title={stepTitle({ record, index })}
            description={stepDescription({ record, index })}
          />
        ))}
      </Steps>
    );
  });

  const improvementDsReset = () => {
    stepRowRef.current?.scrollTo(0, 0);
    let qisProblemReplayAnalysisListSum: any = [];
    eventAxisDs.forEach(record => {
      const recordData = record.toData();
      qisProblemReplayAnalysisListSum = [
        ...qisProblemReplayAnalysisListSum,
        ...(recordData.qisProblemReplayAnalysisList || []),
      ];
    });
    improvementDs.loadData(qisProblemReplayAnalysisListSum);
  };

  const AttachmentComponent = observer(({ name, dataSet }) => {
    const getMenu = (list = '') => {
      return (
        <Menu className={styles['split-menu']} style={{ minWidth: '3rem' }}>
          {list &&
            viewModeFromChang() !== 'none' &&
            JSON.parse(list).map((item: any, index) => (
              <Menu.Item key={String(index)}>
                <a
                  className={styles['attachment-menu']}
                  target="_blank"
                  rel="noopener noreferrer"
                  href={item.url}
                >
                  <span className={styles['attachment-file-name']}>{item.fileName}</span>
                  <Button icon="get_app" color={ButtonColor.primary} funcType={FuncType.link} />
                </a>
              </Menu.Item>
            ))}
          {!list && (
            <Menu.Item>
              <div>{intl.get(`${modelPrompt}.attachment.empty`).d('暂无附件')}</div>
            </Menu.Item>
          )}
        </Menu>
      );
    };
    return (
      <Dropdown overlay={getMenu(dataSet.current?.get(name))}>
        <Button icon="attach_file" funcType={FuncType.flat}>
          {intl.get(`${modelPrompt}.attachment`).d('查看附件')}
        </Button>
      </Dropdown>
    );
  });

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.type.create`).d('问题复盘')}
        backPath="/hwms/problem-management/duplicate-problem/list"
      >
        {headerEdit && (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!headerEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              disabled={!headerEditAuth() || eventAxisEdit || improvementEdit}
              onClick={() => {
                setHeaderEdit(prev => !prev);
              }}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              onClick={handleIssued}
              disabled={!issuedAuth() || eventAxisEdit || improvementEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.issued`).d('下达')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              onClick={handlePublish}
              disabled={!publishAuth() || eventAxisEdit || improvementEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.publish`).d('发布')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              onClick={handleSubmit}
              disabled={!submitAuth() || eventAxisEdit || improvementEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.submit`).d('提交审核')}
            </PermissionButton>
            <ApprovalInfoDrawer objectTypeList={['QIS_PROBLEM_REPLAY_CLOSE']} objectId={id} />
          </>
        )}
      </Header>
      <Content>
        <Collapse
          collapsible="icon"
          bordered={false}
          defaultActiveKey={['panel1', 'panel2', 'panel3', 'panel4']}
        >
          <Panel header={intl.get(`${modelPrompt}.basicInfo`).d('基础信息')} key="panel1">
            <Form
              dataSet={detailBasicDs}
              columns={3}
              labelWidth={112}
              disabled={
                !headerEdit ||
                (id !== 'create' && user.id !== detailBasicDs.current?.get('createdBy'))
              }
            >
              <TextField name="problemReplayCode" />
              <TextField name="problemReplayStatus" />
              <Lov name="createdLov" />

              <Lov name="siteLov" newLine />
              <Lov name="principalUserLov" />
              <Lov name="principalUserDept" />

              <Lov name="problemLov" newLine />
              <TextField name="problemTitle" colSpan={2} />
            </Form>
            <Form dataSet={detailBasicDs} columns={3} labelWidth={112}>
              <DateTimePicker
                name="planEndTime"
                newLine
                disabled={
                  !headerEdit ||
                  (id !== 'create' && user.id !== detailBasicDs.current?.get('createdBy'))
                }
              />
              <Select
                name="startReason"
                disabled={
                  !headerEdit ||
                  (id !== 'create' && user.id !== detailBasicDs.current?.get('createdBy'))
                }
              />
              <Attachment
                name="enclosure"
                viewMode={viewModeFromChang()}
                readOnly={
                  !headerEdit ||
                  (id !== 'create' && user.id !== detailBasicDs.current?.get('createdBy'))
                }
                {...attachmentProps}
              />
            </Form>
            <Form
              dataSet={detailBasicDs}
              columns={3}
              labelWidth={112}
              disabled={
                !headerEdit ||
                (id !== 'create' && user.id !== detailBasicDs.current?.get('createdBy'))
              }
            >
              <TextArea name="background" colSpan={3} newLine />
            </Form>
          </Panel>

          <Panel header={intl.get(`${modelPrompt}.eventAxis`).d('事件轴')} key="panel2">
            <Table
              highLightRow={false}
              dataSet={eventAxisDs}
              columns={eventAxisColumns}
              customizedCode="wtfp2"
              onRow={({ record }) => ({
                onClick: () => {
                  if (!improvementEdit) {
                    stepChange(record.index);
                  }
                },
              })}
              buttons={
                eventAxisEdit
                  ? [
                    <>
                      <Button onClick={handleEventAxisCancel}>
                        {intl.get('tarzan.common.button.cancel').d('取消')}
                      </Button>
                      <Button color={ButtonColor.primary} onClick={handleEventAxisSave}>
                        {intl.get('tarzan.common.button.save').d('保存')}
                      </Button>
                    </>,
                  ]
                  : [
                    <>
                      <Button
                        onClick={() => {
                          setEventAxisEdit(prev => !prev);
                        }}
                        disabled={!eventAxisEditAuth() || headerEdit || improvementEdit}
                      >
                        {intl.get('tarzan.common.button.edit').d('编辑')}
                      </Button>
                    </>,
                  ]
              }
            />
            {
              <div
                style={{
                  overflow: 'auto',
                }}
                className={styles['steps-row']}
                ref={stepRowRef}
              >
                <StepRow dataSet={eventAxisDs} />
              </div>
            }
          </Panel>

          <Panel
            header={intl.get(`${modelPrompt}.improvement`).d('原因分析及措施改进')}
            key="panel3"
          >
            <Table
              highLightRow={false}
              dataSet={improvementDs}
              columns={improvementColumns}
              customizedCode="wtfp3"
              buttons={
                improvementEdit
                  ? [
                    <>
                      <Button onClick={handleImprovementCancel}>
                        {intl.get('tarzan.common.button.cancel').d('取消')}
                      </Button>
                      <Button color={ButtonColor.primary} onClick={handleImprovementSave}>
                        {intl.get('tarzan.common.button.save').d('保存')}
                      </Button>
                    </>,
                  ]
                  : [
                    <>
                      <Button
                        onClick={() => {
                          stepChange(-1);
                        }}
                        disabled={
                          eventTablcIndex === -1 || improvementEdit || headerEdit || eventAxisEdit
                        }
                      >
                        {intl.get('hzero.common.button.reset').d('重置')}
                      </Button>
                      <Button
                        onClick={() => {
                          setImprovementEdit(prev => !prev);
                        }}
                        disabled={!improvementEditAuth() || headerEdit || eventAxisEdit}
                      >
                        {intl.get('tarzan.common.button.edit').d('编辑')}
                      </Button>
                    </>,
                  ]
              }
            />
          </Panel>
          <Panel header={intl.get(`${modelPrompt}.close`).d('确认关闭')} key="panel4">
            <Form dataSet={detailBasicDs} columns={3} labelWidth={112} disabled>
              <TextArea name="summarize" colSpan={3} />
              <AttachmentComponent name="summarizeEnclosure" dataSet={detailBasicDs} />
            </Form>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [
    'tarzan.qms.inspectGroupMaintenance',
    'tarzan.common',
    'hzero.common',
    'tarzan.duplicateProblem',
    'tarzan.qms.inspectGroupMaintenance.model',
  ],
})(DuplicateProblem);
