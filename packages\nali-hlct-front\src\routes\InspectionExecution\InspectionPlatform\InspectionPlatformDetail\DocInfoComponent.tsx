/**
 * @Description: 检验平台-单据信息
 * @Author: <<EMAIL>>
 * @Date: 2023-02-14 09:11:44
 * @LastEditTime: 2023-05-18 17:04:03
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import moment from 'moment/moment';
import intl from 'utils/intl';
import { Form, Output, Row, Col } from 'choerodon-ui/pro';
import { Collapse, Tag } from 'choerodon-ui';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { BASIC } from '@utils/config';
import OkPng from '@/assets/ok.png';
import NgPng from '@/assets/ng.png';
import PendingPng from '@/assets/pending.png';

const modelPrompt = 'tarzan.qms.inspectionPlatform';
const { Panel } = Collapse;

const DocInfoComponent = props => {
  const { docInfoDS, history, customizeForm } = props;

  // 跳转检验单管理平台
  const handleJumpInspectManagement = () => {
    history.push(`/hwms/inspect-doc-maintain/dist/${docInfoDS?.current?.get('inspectDocId')}`);
  };

  return (
    <Collapse bordered={false} defaultActiveKey={['BASIC', 'ACTUAL']}>
      <Panel
        header={intl.get(`${modelPrompt}.title.basic`).d('单据基本信息')}
        key="BASIC"
        dataSet={docInfoDS}
      >
        {customizeForm(
          {
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_DOC.BASIC`,
          },
          <Form
            dataSet={docInfoDS}
            columns={4}
            labelWidth={112}
            disabled
            labelLayout={LabelLayout.horizontal}
          >
            <Output
              name="inspectDocNum"
              style={{ color: 'rgb(8, 64, 248)', cursor: 'pointer' }}
              renderer={({ value }) => <a onClick={handleJumpInspectManagement}>{value}</a>}
            />
            <Output name="inspectBusinessTypeDesc" />
            <Output name="siteCode" />
            <Output
              name="inspectDocStatusDesc"
              renderer={({ value, record }) => (
                <>
                  {value && <Tag color="blue">{value}</Tag>}
                  {record?.get('urgentFlag') === 'Y' && (
                    <Tag color="red">{intl.get(`${modelPrompt}.urgent`).d('加急')}</Tag>
                  )}
                </>
              )}
            />
            <Output name="sourceObjectTypeDesc" />
            <Output name="sourceObjectAndLineCode" />
            <Output name="inspectInfoUserName" />
            <Output
              name="inspectInfoCreationDate"
              renderer={({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value)}
            />
            <Output name="materialCode" />
            <Output name="materialName" />
            <Output name="revisionCode" />
            <Output
              name="inspectSumQty"
              renderer={({ value, record }) => {
                if (value) {
                  return (
                    <>
                      {value}&nbsp;{record?.get('uomName') || ''}
                    </>
                  );
                }
                return '';
              }}
            />
            <Output
              name="samplingQty"
              renderer={({ value, record }) => {
                if (value) {
                  return (
                    <>
                      {value}&nbsp;{record?.get('uomName') || ''}
                    </>
                  );
                }
                return '';
              }}
            />
            <Output name="supplierName" />
            <Output name="customerName" />
            <Output name="areaName" />
            <Output name="prodLineName" />
            <Output name="operationName" />
            <Output name="processWorkcellName" />
            <Output name="stationWorkcellName" />
            <Output name="equipmentCode" />
            <Output name="locatorName" />
            <Output name="docCreateMethodDesc" />
            <Output name="acceptStandard" />
            <Output name="samplingMethodDesc" />
            <Output name="samplingDimensionDesc" />
            <Output name="strictnessDesc" />
            <Output name="inspectSchemeCode" />
            <Output name="reviewStatusDesc" />
            <Output name="reviewUserName" />
            <Output
              name="reviewTime"
              renderer={({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value)}
            />
            <Output name="validity" />
          </Form>,
        )}
      </Panel>
      <Panel
        header={intl.get(`${modelPrompt}.title.actual`).d('单据实绩信息')}
        key="ACTUAL"
        dataSet={docInfoDS}
      >
        <Row>
          <Col span={18}>
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_DOC.ACT`,
              },
              <Form
                dataSet={docInfoDS}
                columns={3}
                labelWidth={112}
                disabled
                labelLayout={LabelLayout.horizontal}
              >
                <Output name="okQty" />
                <Output name="ngQty" />
                <Output name="scrapQty" />
                <Output name="firstInspectResult" />
                <Output name="firstInspectorName" />
                <Output
                  name="firstInspectDate"
                  renderer={({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value)}
                />
                <Output name="lastInspectResult" />
                {docInfoDS.current?.get('recheckFlag') !== 'Y' && (
                  <Output name="lastInspectorName" />
                )}
                <Output
                  name="lastInspectDate"
                  renderer={({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value)}
                />
                <Output
                  name="actualStartTime"
                  renderer={({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value)}
                />
                <Output
                  name="actualEndTime"
                  renderer={({ value }) => (value ? moment(value).format('YYMMDD HH:mm') : value)}
                />
              </Form>,
            )}
          </Col>
          <Col span={1} />
          <Col span={5}>
            <img
              src={
                docInfoDS?.current?.get('lastInspectResult') === 'OK'
                  ? OkPng
                  : docInfoDS?.current?.get('lastInspectResult') === 'NG'
                  ? NgPng
                  : docInfoDS?.current?.get('firstInspectResult') === 'OK'
                  ? OkPng
                  : docInfoDS?.current?.get('firstInspectResult') === 'NG'
                  ? NgPng
                  : PendingPng
              }
              alt=""
              style={{
                width: '1.5rem',
                height: '1.5rem',
                verticalAlign: 'top',
              }}
            />
          </Col>
        </Row>
      </Panel>
    </Collapse>
  );
};

export default DocInfoComponent;
