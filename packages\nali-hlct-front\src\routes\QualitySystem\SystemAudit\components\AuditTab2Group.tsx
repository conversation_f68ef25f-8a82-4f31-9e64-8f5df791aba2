/**
 * @Description: 体系审核管理维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import React, { useState } from 'react';
import uuid from 'uuid/v4';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { DataSet, Form, Button, Lov, TextField, Table } from 'choerodon-ui/pro';
import { Card, Popconfirm } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { Button as PermissionButton } from 'components/Permission';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import { getCurrentUser } from 'utils/utils';
import notification from 'utils/notification';
import styles from '../index.modules.less';
import { auditGroupFormDS, auditGroupTableDS } from '../stores/AuditTab2GroupDS';
import noDataImg from '@/assets/noData.png';
import { saveAuditGroupConfig } from '../services';

const modelPrompt = 'tarzan.systemAudit';

const AuditGroup = props => {
  const {
    groupDataList,
    setGroupDataList,
    id,
    canEdit,
    setCanEdit,
    readonly,
    initPageData = () => {},
    sysReviewPlanStatus,
    commitOaSubmit,
    threeResList,
    pubFlag,
  } = props;

  // 审核小组保存
  const saveAuditGroup = useRequest(saveAuditGroupConfig(), {
    manual: true,
    needPromise: true,
  });

  const [user] = useState(getCurrentUser()); // 用户详细信息

  const handleCancel = () => {
    setCanEdit(false);
    initPageData('AuditTab2Group');
  };

  // 校验审核小组信息
  const validateAll = async () => {
    let allHaveRow = true;
    // 未添加小组报错
    if (groupDataList.length === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.groupNeed`).d('请添加审核小组'),
      });
      return false;
    }

    // 校验所有小组表单和列表的输入项值
    const waitList: any = [];

    groupDataList.forEach(item => {
      const { formDs, tableDs } = item;
      waitList.push(formDs.validate());
      waitList.push(tableDs.validate());
      // 若有小组未添加组员则记录
      if (tableDs.length === 0) {
        allHaveRow = false;
      }
    });

    const waitListResult = await Promise.all(waitList);

    // 未添加组员的报错提示
    if (!allHaveRow) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.groupNeedRow`).d('审核小组至少添加一个小组成员'),
      });
      return false;
    }
    // 小组表单和列表的校验结果
    return waitListResult.every(val => val);
  };

  const groupData = () => {
    const params: any = [];
    groupDataList.forEach(item => {
      const { formDs, tableDs } = item;
      const data = formDs.current.toData();
      // console.log(data, 'data')
      data.changeFlag = formDs.current.status === 'sync' ? 'N' : 'Y';
      const tableDataList: any = [];
      tableDs.forEach(record => {
        const tableData = record.toData();
        tableData.changeFlag = record.status === 'sync' ? 'N' : 'Y';
        tableDataList.push(tableData);
        // if (record.status !== 'sync') {
        //   data.changeFlag = 'Y';
        // }
      });
      data.groupMem = tableDataList;
      params.push(data);
    });
    return params;
  };

  const handleSave = async () => {
    const validateResult = await validateAll();
    if (!validateResult) {
      return;
    }

    const res = await saveAuditGroup.run({
      params: groupData(),
      queryParams: {
        sysReviewPlanId: id,
      },
    });
    if (res?.success) {
      // @ts-ignore
      notification.success();
      setCanEdit(false);
      initPageData('AuditTab2Group');
    }
  };

  // 添加小组
  const addGroup = () => {
    const _groupDataList: any = [];
    groupDataList.forEach(item => {
      _groupDataList.push({ ...item });
    });
    _groupDataList.push({
      uuid: uuid(),
      formDs: new DataSet(auditGroupFormDS()),
      tableDs: new DataSet(auditGroupTableDS()),
    });
    setGroupDataList(_groupDataList);
  };

  // 删除小组
  const deleteGroup = _uuid => {
    const _groupDataList: any = [];
    groupDataList.forEach(item => {
      if (_uuid !== item.uuid) {
        _groupDataList.push({ ...item });
      }
    });
    setGroupDataList(_groupDataList);
  };

  // 添加小组行
  const addGroupRow = headerProps => {
    const targetTableDs = headerProps?.dataSet;
    if (targetTableDs) {
      targetTableDs.create({});
    }
  };

  // 删除小组行
  const deleteGroupRow = rendererProps => {
    const targetTableDs = rendererProps?.dataSet;
    const targetRecord = rendererProps?.record;
    if (targetTableDs && targetRecord) {
      targetTableDs.delete(targetRecord, false);
    }
  };

  const columns: ColumnProps[] = [
    {
      header: headerProps => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          funcType="flat"
          shape="circle"
          size="small"
          onClick={() => {
            addGroupRow(headerProps);
          }}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      renderer: rendererProps => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteGroupRow(rendererProps)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit}
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'groupUserLov',
      editor: canEdit,
    },
    {
      name: 'groupMemEmployeeName',
      editor: canEdit,
    },
    {
      name: 'groupMemPhone',
      editor: canEdit,
    },
    {
      name: 'departmentName',
      editor: canEdit,
    },
    {
      name: 'positionName',
      editor: canEdit,
    },
  ];

  return (
    <div className={styles.tabsBody}>
      {!readonly && (
        <div className="control-row">
          <div>
            {canEdit && (
              <Button color={ButtonColor.primary} onClick={addGroup}>
                {
                  intl.get(`${modelPrompt}.createNewGroup`).d('新建小组')
                }
              </Button>
            )}
          </div>
          <div>
            {!canEdit && !pubFlag && (
              <Button
                disabled={
                  !['NEW', 'TO_RELEASE', 'REJECTED'].includes(sysReviewPlanStatus) ||
                  (threeResList && user.id !== threeResList[0]?.rows?.createdBy)
                }
                color={ButtonColor.primary}
                onClick={() => {
                  setCanEdit(true);
                }}
              >
                {intl.get('hzero.common.button.edit').d('编辑')}
              </Button>
            )}
            {canEdit && !pubFlag && (
              <>
                <Button onClick={handleCancel}>
                  {intl.get('hzero.common.button.cancel').d('取消')}
                </Button>
                <Button
                  disabled={commitOaSubmit.loading || saveAuditGroup.loading}
                  color={ButtonColor.primary}
                  onClick={handleSave}
                >
                  {intl.get('hzero.common.button.save').d('保存')}
                </Button>
              </>
            )}
          </div>
        </div>
      )}
      <div>
        {groupDataList &&
          groupDataList.length > 0 &&
          groupDataList.map(item => {
            const { formDs, tableDs } = item;
            return (
              <Card
                // extra 右上角
                // cover 整行
                key={item.uuid}
                extra={
                  canEdit && (
                    <div className="card-control">
                      <Popconfirm
                        title={intl.get(`${modelPrompt}.deleteGroup`).d('是否确认删除整个小组?')}
                        onConfirm={() => deleteGroup(item.uuid)}
                        okText={intl.get('tarzan.common.button.confirm').d('确认')}
                        cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                      >
                        <Button>
                          {
                            intl.get(`${modelPrompt}.button.deleteGroup`).d('删除小组')
                          }
                        </Button>
                      </Popconfirm>
                    </div>
                  )
                }
              >
                <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
                  <TextField name="groupNumber" />
                  <TextField name="groupDescription" />
                  <Lov name="groupLeaderLov" />
                </Form>
                <Table customizedCode="shtxshxz1" dataSet={tableDs} columns={columns}></Table>
              </Card>
            );
          })}
        {(!groupDataList || !groupDataList.length) && (
          <Card className="empty-card">
            <div>
              <img src={noDataImg} alt={intl.get(`${modelPrompt}.noData`).d('暂无数据')} />
            </div>
            <div>{intl.get(`${modelPrompt}.noData`).d('暂无数据')}</div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default AuditGroup;
