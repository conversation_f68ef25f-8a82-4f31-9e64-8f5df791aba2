/**
 * @Description: 检验方案模板维护-详情-折叠栏title
 * @Author: <<EMAIL>>
 * @Date: 2023-01-17 11:15:22
 * @LastEditTime: 2023-02-16 11:33:10
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { observer } from 'mobx-react';
import { DataSet, Icon } from 'choerodon-ui/pro';

const CollapsePanelTitle = observer(
  ({
    ds,
    itemDs,
    index,
    sortList,
    title,
    clickTitleCallback,
    activeKey,
    canEdit,
  }: {
    ds: DataSet;
    itemDs: DataSet;
    index: number;
    sortList: Function;
    title: string;
    clickTitleCallback: Function;
    activeKey;
    canEdit;
  }) => {
    return (
      <span
        style={{
          fontWeight: 400,
          marginLeft: '18px',
        }}
      >
        {activeKey === '' ||
        !canEdit ||
        !itemDs.current?.get('inspectBusinessType') ||
        !(ds.current?.get('inspectSchemeObjectType') === 'MATERIAL' || ds.current?.get('inspectSchemeObjectType') === 'OPERATION') ? (
            <>{title}</>
          ) : (
            <>
              <a
                onClick={() => {
                  clickTitleCallback('edit');
                }}
              >
                {title}
              </a>
              <Icon
                onClick={() => {
                  sortList('up', index);
                }}
                type="arrow_upward"
                style={{ marginLeft: '18px' }}
              />
              <Icon
                onClick={() => {
                  sortList('down', index);
                }}
                type="arrow_downward"
                style={{ marginLeft: '6px' }}
              />
            </>
          )}
      </span>
    );
  },
);

export default CollapsePanelTitle;
