/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-26 15:35:22
 * @LastEditTime: 2023-08-02 18:18:48
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      // 工单Lov的查询参数
      name: 'workOrderLovLimit',
    },
    {
      name: 'workOrderLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workOrderLov`).d('工单'),
      lovCode: 'HME.WORK_ORDER',
      textField: 'workOrderNum',
      valueField: 'workOrderId',
      lovPara: {
        tenantId,
      },
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            ...record.get('workOrderLovLimit'),
          };
        },
      },
    },
    {
      name: 'workOrderId',
      type: FieldType.number,
      bind: 'workOrderLov.workOrderId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      bind: 'workOrderLov.workOrderNum',
    },
    {
      name: 'materialLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLot`).d('物料批'),
      dynamicProps: {
        disabled({ record }) {
          return !record.get('workOrderId');
        },
      },
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料信息'),
    },
    {
      name: 'customerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerDesc`).d('客户信息'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('工单备注'),
    },
    {
      name: 'standardBeat',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardBeat`).d('标准节拍'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('在制标识'),
    },
    {
      name: 'pitStopDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.pitStopDate`).d('进站时间'),
    },
    {
      name: 'processedRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processedRemark`).d('调度备注'),
    },
    {
      name: 'processedTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processedTime`).d('加工时长'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单号'),
    },
    {
      name: 'woQtyInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woQtyInfo`).d('工单数量'),
    },
    {
      name: 'eoQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoQty`).d('在制数量'),
    },
  ],
});
export { detailDS };
