// import { Host } from '@/utils/config';
import { BASIC } from '@/utils/config';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';

// const Host = '/mes-41300'
const Host = `${
  BASIC.HMES_BASIC
}`
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.MarkMaintenance';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'markingId',
    paging: true,
    autoQuery: true,
    selection: 'multiple',
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'markingCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingCode`).d('标记编码'),
      },
      {
        name: 'statusValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.status`).d('标记状态'),
      },
      {
        name: 'typeValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.type`).d('标记类型'),
      },
      {
        name: 'markingContentValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingContent`).d('标记内容'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      },
      {
        name: 'expirationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.expirationDate`).d('有效期'),
      },
      {
        name: 'applyReason',
        type: 'string',
        label: intl.get(`${modelPrompt}.applyReason`).d('申请原因'),
      },
      {
        name: 'applyBasis',
        type: 'attachments',
        label: intl.get(`${modelPrompt}.applyBasis`).d('申请依据'),
      },
      {
        name: 'operationDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDesc`).d('拦截工艺'),
      },
      {
        name: 'interceptionDisposalWay',
        type: 'string',
        label: intl.get(`${modelPrompt}.interceptionDisposalWay`).d('拦截处置方法'),
      },
      {
        name: 'disposalResult',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalResult`).d('处置结果'),
      },
      {
        name: 'attachments',
        type: 'attachments',
        label: intl.get(`${modelPrompt}.attachments`).d('附件'),
      },
      {
        name: 'createdBy',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('申请人'),
      },
      {
        name: 'updatedBy',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationObj`).d('最后更新人'),
      },
    ],
    queryFields: [
      {
        name: 'markingCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingCode`).d('标记编码'),
      },
      {
        name: 'statusList',
        type: 'string',
        label: intl.get(`${modelPrompt}.status`).d('标记状态'),
        lookupCode: 'HME.MARKING_STATUS',
        multiple: true,
        defaultValue: ['NEW', 'AUDIT', 'PUBLISH'],
      },
      {
        name: 'type',
        type: 'string',
        label: intl.get(`${modelPrompt}.markType`).d('标记类型'),
        lookupCode: 'HME.MARKING_TYPE',
      },
      {
        name: 'markingContent',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingContent`).d('标记内容'),
        lookupCode: 'HME.MARKING_CONTENT',
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'HME.MARKING_ENABLE',
      },
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('拦截工艺'),
        lovCode: 'HME.INTERCEPT_OPERATION_LOV',
        ignore: 'always',
      },
      {
        name: 'operationName',
        bind: 'operationObj.value',
      },
      {
        name: 'createdBy',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('申请人'),
      },
      {
        name: 'updatedBy',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationObj`).d('最后更新人'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-markings/query/list`,
          method: 'GET',
        };
      },
    },
  };
};

const tableOADS = () => {
  return {
    name: 'tableOADS',
    primaryKey: 'markingId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'markingCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingCode`).d('标记编码'),
      },
      {
        name: 'statusValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.status`).d('标记状态'),
      },
      {
        name: 'typeValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.type`).d('标记类型'),
      },
      {
        name: 'markingContentValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingContent`).d('标记内容'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      },
      {
        name: 'expirationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.expirationDate`).d('有效期'),
      },
      {
        name: 'applyReason',
        type: 'string',
        label: intl.get(`${modelPrompt}.applyReason`).d('申请原因'),
      },
      {
        name: 'applyBasis',
        type: 'attachments',
        label: intl.get(`${modelPrompt}.applyBasis`).d('申请依据'),
      },
      {
        name: 'operationDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDesc`).d('拦截工艺'),
      },
      {
        name: 'interceptionDisposalWay',
        type: 'string',
        label: intl.get(`${modelPrompt}.interceptionDisposalWay`).d('拦截处置方法'),
      },
      {
        name: 'disposalResult',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalResult`).d('处置结果'),
      },
      {
        name: 'attachments',
        type: 'attachments',
        label: intl.get(`${modelPrompt}.attachments`).d('附件'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-markings/query/list`,
          method: 'GET',
        };
      },
    },
  };
};

const detailDS = () => {
  return {
    paging: false,
    autoQuery: false,
    selection: false,
    autoCreate: true,
    fields: [
      {
        name: 'siteLov',
        type: 'object',
        required: true,
        lovCode: 'MT.APS.SITE.URL',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'siteCode',
        bind: 'siteLov.siteCode',
      },
      {
        name: 'siteId',
        bind: 'siteLov.siteId',
      },
      {
        name: 'markingCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingCode`).d('标记编码'),
      },
      {
        name: 'status',
        type: 'string',
        defaultValue: 'NEW',
        label: intl.get(`${modelPrompt}.status`).d('标记状态'),
        lookupCode: 'HME.MARKING_STATUS',
      },
      {
        name: 'type',
        type: 'string',
        required: true,
        label: intl.get(`${modelPrompt}.markType`).d('标记类型'),
        lookupCode: 'HME.MARKING_TYPE',
      },
      {
        name: 'markingContent',
        required: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.markingContent`).d('标记内容'),
        lookupCode: 'HME.MARKING_CONTENT',
      },
      {
        name: 'enableFlag',
        required: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'HME.MARKING_ENABLE',
        defaultValue: 'VALID',
      },{
        name: 'expirationDate',
        required: true,
        type: 'dateTime',
        format: 'YYYY-MM-DD HH:mm:ss',
        min: moment(),
        label: intl.get(`${modelPrompt}.expirationDate`).d('有效期'),
      },
      {
        name: 'applyReason',
        type: 'string',
        required: true,
        label: intl.get(`${modelPrompt}.applyReason`).d('申请原因'),
      },
      {
        name: 'applyBasis',
        type: 'attachment',
        required: true,
        label: intl.get(`${modelPrompt}.applyBasisNew`).d('申请依据（需提交制造、质量、工艺同意证据）'),
      },
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('拦截工艺'),
        lovCode: 'HME.INTERCEPT_OPERATION_LOV',
        ignore: 'always',
        textField: 'value',
        dynamicProps: {
          required: ({record}) => {
            return record?.get('type') === 'INTERCEPT'
          },
          disabled: ({record}) => {
            return record?.get('type') !== 'INTERCEPT'
          },
        },
      },
      {
        name: 'interceptionOperationName',
        bind: 'operationObj.value',
      },
      {
        name: 'interceptionDisposalWay',
        type: 'string',
        label: intl.get(`${modelPrompt}.interceptionDisposalWay`).d('拦截处置方法'),
        dynamicProps: {
          required: ({record}) => {
            return record?.get('type') === 'INTERCEPT'
          },
          disabled: ({record}) => {
            return record?.get('type') !== 'INTERCEPT'
          },
        },
      },
      {
        name: 'disposalResult',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalResult`).d('处置结果'),
      },
      {
        name: 'attachments',
        type: 'attachment',
        label: intl.get(`${modelPrompt}.attachments`).d('附件'),
      },
    ],
    events: {
      update: ({ record, name, value })  => {
        if(name === 'type'&&value !== 'INTERCEPT'){
          record?.set('operationObj', null)
          record?.set('interceptionDisposalWay', null)
        }
      },
    },
  }
}

export { tableDS, detailDS, tableOADS };
