/*
 * @Description: 编辑抽屉组件
 * @Author: <<EMAIL>>
 * @Date: 2023-11-03 10:52:24
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-11-07 13:58:22
 */
import React from 'react';
import { Form, TextField, Select, Lov, DatePicker, Attachment, Table, Button } from 'choerodon-ui/pro';
import { Popconfirm, Collapse } from 'choerodon-ui';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { observer } from 'mobx-react';
import { getCurrentUser } from 'utils/utils';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TarzanS<PERSON> } from '@components/tarzan-ui';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.qms.systemDocumentManagement';
const { Panel } = Collapse;
const userInfo = getCurrentUser();

export default observer(props => {
  const { dataSet, adminFlag, releateForthFileDs, relatedFileDs, editModalHandleCancel } = props;

  const getAttachmentViewMode = record => {
    if (record?.status === 'add') {
      return 'popup';
    }
    const { fileLevel, editedBy } = record?.toData() || {};
    if (fileLevel === 'FORTH') {
      return 'popup';
    }

    if (['FIRST', 'SECOND', 'THIRD'].includes(fileLevel)) {
      if (editedBy === userInfo.id || adminFlag) {
        return 'popup';
      }
      return 'none';
    }

    return 'popup';
  };

  const getDisabled = record => {
    if (record?.status === 'add') {
      return true;
    }
    const { editedBy, fileStatus } = record?.toData() || {};
    return (
      (fileStatus === 'STAGING' && editedBy === userInfo.id) ||
      (fileStatus === 'UNPUBLISHED' &&
        (editedBy === userInfo.id || adminFlag))
    );
  };

  const getAttachmentProps: any = record => ({
    name: 'uuid',
    bucketName: 'qms',
    max: 1,
    record,
    bucketDirectory: 'system-document-management',
    accept: [
      '.doc',
      '.ppt',
      '.docx',
      '.xlsx',
      '.xls',
      '.deb',
      '.txt',
      '.pdf',
      'image/*',
      '.visio',
      '.project',
    ],
    labelLayout: LabelLayout.none,
    showValidation: ShowValidation.newLine,
    disabled: !getDisabled(record),
    readOnly: !getDisabled(record),
    viewMode: getAttachmentViewMode(record),
  });

  const handleChangeFileLevel = () => {
    dataSet.current?.set('levelLimitLov', undefined);
    dataSet.current?.set('affliatedProcess', undefined);
    dataSet.current?.set('processType', undefined);
    dataSet.current?.set('relatedFileList', undefined);
    relatedFileDs.loadData([]);
  };

  const handleUpdateProcessType = value => {
    if (value) {
      if (value.indexOf('S') !== -1) {
        dataSet.current.set('processType', 'SP');
      }
      if (value.indexOf('C') !== -1) {
        dataSet.current.set('processType', 'COP');
      }
      if (value.indexOf('M') !== -1) {
        dataSet.current.set('processType', 'MP');
      }
    } else {
      dataSet.current.set('processType', '');
    }
  };

  const handleChangeLevelLimit = value => {
    const fileLevel = dataSet.current?.get('fileLevel');
    const { affliatedProcess } = value || {};
    if (fileLevel === 'FORTH') {
      dataSet.current?.set('affliatedProcess', affliatedProcess);
      handleUpdateProcessType(affliatedProcess);
    }
  };

  const handleChangeRelatedFile = value => {
    relatedFileDs.loadData([...value]);
  };

  const handleAddLine = () => {
    let maxSeq = 0;
    releateForthFileDs.forEach((record) => {
      if (record?.get('sequence') > maxSeq) {
        maxSeq = record?.get('sequence');
      }
    })
    releateForthFileDs.create({
      sequence: maxSeq + 10,
    });
  }

  const getColumns: (fileStatus: string) => ColumnProps[] = (fileStatus) => [
    {
      header: () => (
        <Button
          icon="add"
          disabled={fileStatus !== 'UNPUBLISHED'}
          funcType={FuncType.flat}
          onClick={() => handleAddLine()}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => {
        if (!record?.get('fileId')) {
          // 未保存的数据允许删除
          return (
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              onConfirm={() => releateForthFileDs.remove(record)}
            >
              <Button
                icon="remove"
                funcType={FuncType.flat}
              />
            </Popconfirm>
          )
        }
        // 保存后的数据只允许做取消
        return (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.cancel`).d('是否确认取消?')}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            onConfirm={() => editModalHandleCancel(record)}
          >
            <Button
              icon="delete"
              disabled={!['STAGING', 'UNPUBLISHED'].includes(record.get('fileStatus'))}
              funcType={FuncType.flat}
            />
          </Popconfirm>
        )
      },
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      width: 80,
      align: ColumnAlign.left,
    },
    { name: 'fileCode' },
    {
      name: 'fileName',
      editor: record => !record?.get('fileId') || ['STAGING', 'UNPUBLISHED'].includes(record.get('fileStatus')),
    },
    {
      name: 'fileStatus',
      width: 100,
    },
    {
      name: 'uuid',
      renderer: ({ record }) => <Attachment {...getAttachmentProps(record)} />,
      editor: record => (!record?.get('fileId') || record?.get('fileStatus') === 'UNPUBLISHED') && <Attachment {...getAttachmentProps(record)} />,
    },
  ];

  const relatedFileColumns: ColumnProps[] = [
    { name: 'fileCode' },
    { name: 'fileName' },
  ];

  return (
    <TarzanSpin dataSet={dataSet}>
      <Form
        dataSet={dataSet}
        columns={2}
      >
        <TextField name="fileCode" />
        <TextField name="fileName" />
        <Select name="fileLevel" onChange={handleChangeFileLevel} />
        <Select name="processType" />
        <Lov name="departmentLov" />
        <TextField name="editedDepartmentParentName" />
        <Lov name="responsLov" />
        <DatePicker name="editedDate" />
        <Select name="affliatedProcess" onChange={handleUpdateProcessType} />
        <Attachment {...getAttachmentProps(dataSet.current)} />
        <Lov name="levelLimitLov" onChange={handleChangeLevelLimit} />
        <Lov name="relatedFileList" onChange={handleChangeRelatedFile} />
        <Select name="currentFlag" />
      </Form>
      {dataSet.current?.get('fileStatus') !== 'STAGING' &&
        ['SECOND', 'THIRD'].includes(dataSet.current?.get('fileLevel')) &&
        (
          <>
            <div style={{ marginTop: 10, marginBottom: -10, color: 'red' }}>如需添加四级表单，请在二、三级文件新建成功后，点击下方“+”</div>
            <Table columns={getColumns(dataSet.current?.get('fileStatus'))} dataSet={releateForthFileDs} />
          </>
        )
      }
      {['SECOND', 'THIRD'].includes(dataSet.current?.get('fileLevel')) &&
        <Collapse bordered={false} defaultActiveKey={['relatedFile']}>
          <Panel key="relatedFile" header={intl.get(`${modelPrompt}.title.relatedFile`).d('相关文件')}>
            <Table columns={relatedFileColumns} dataSet={relatedFileDs} />
          </Panel>
        </Collapse>}
    </TarzanSpin>
  );
});
