import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { BASIC } from '@utils/config';
import request from 'utils/request';


const modelPrompt = 'tarzan.receive.receiveReturn';
const tenantId = getCurrentOrganizationId();
const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/inventory-send-receive/identify/get/for/ui`;


const headerFormDS = () => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  dataKey: 'rows.instructionDoc',
  cacheSelection: true,
  transport: {
    read: () => {
      const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-receive-return-bill/edit/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.LINE`;
      return {
        url,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
      disabled: true,
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('领退料类型'),
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-instruction-doc/operation-type/limit/doc/type/list?operationType=MATERIAL_PICK_RETURN_DOC`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_REQUISITION`,
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      disabled: true,
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      noCache: true,
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      required: true,
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'prodLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('生产线'),
      noCache: true,
      lovCode: 'MT.MODEL.PRODLINE',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
          };
        },
      },
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      bind: 'prodLine.prodLineCode',
    },
    {
      name: 'prodLineId',
      type: FieldType.string,
      bind: 'prodLine.prodLineId',
    },
    {
      name: 'workOrder',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('来源工单号'),
      noCache: true,
      lovCode: `${BASIC.LOV_CODE_BEFORE}.RETURN.BILL.WORKORDER`,
      multiple: true,
      ignore: 'always',
      required: true,
      textField: 'workOrderNumber',
      valueField: 'workOrderId',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
            productionLineId: record?.get('prodLineId'),
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId') || !record?.get('prodLineId');
        },
      },
    },
    {
      name: 'workOrderIds',
      bind: 'workOrder.workOrderId',
    },
    {
      name: 'sourceOrderNumList',
      bind: 'workOrder.workOrderNumber',
    },
    {
      name: 'demandTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expectedArrivalTime`).d('需求时间'),
      required: true,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'instructionType',
    },
    {
      name: 'docTypeTag',
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      lookupCode: 'SOURCE_SYSTEM',
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
});

const lineTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  dataKey: 'content',
  selection: false,
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'fromIdentifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromIdentifyType`).d('来源管理模式'),
      lookupCode: 'MT.APS.GEN_TYPE_URL',
      lovPara: {
        typeGroup: 'IDENTITY_TYPE',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      valueField: 'typecode',
      textField: 'description',
    },
    {
      name: 'toIdentifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toIdentifyType`).d('目标管理模式'),
      lookupCode: 'MT.APS.GEN_TYPE_URL',
      lovPara: {
        typeGroup: 'IDENTITY_TYPE',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      valueField: 'typecode',
      textField: 'description',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'componentQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.componetQty`).d('需求数'),
    },
    {
      name: 'receivedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.receiveQty`).d('已领取'),
    },
    {
      name: 'signedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.actualQty`).d('已签收'),
    },
    {
      name: 'returnedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.returnQty`).d('已退料'),
    },
    {
      name: 'quantityMax',
      type: FieldType.number,
    },
    {
      name: 'haveDoneQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.haveDoneQty`).d('已制单数量'),
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.doneQty`).d('制单数量'),
      // min: 0,
      max: 'quantityMax',
      required: true,
      dynamicProps: {
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
      },
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_REQUISITION`,
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      disabled: true,
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('来源工单号'),
    },
    {
      name: 'productionLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLine`).d('生产线'),
    },
    {
      name: 'fromWarehouse',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromWarehouse`).d('来源仓库'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      noCache: true,
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('fromLocatorRequiredFlag') === 'Y'
          // return ((record?.get('instructionType') || []).indexOf('RECEIVE_TO_SITE') === -1 && record?.get('docTypeTag') === 'PICK') && record?.get('fromLocatorRequiredFlag') === 'Y';
        },
        disabled: ({ record }) => {
          // RETURN且只要有RECEIVE_TO_SITE，也是指来源仓库和来源库位都禁用
          return (record?.get('instructionType') || []).indexOf('RECEIVE_TO_SITE') !== -1 && record?.get('docTypeTag') === 'RETURN';
        },
        lovPara: ({ record }) => {
          const strategyList = record?.get('strategyList') || [];
          const sortStrategy = strategyList.sort((a, b) => {
            return a.sequence - b.sequence;
          });
          const { businessType } = sortStrategy[0] || {};
          return {
            tenantId,
            siteIds: record?.get('siteId'),
            locatorCategoryList: ['AREA'],
            businessTypes: businessType,
            queryType: 'SOURCE',
          };
        },
      },
    },
    {
      name: 'sourceLocatorId',
      type: FieldType.string,
      bind: 'fromWarehouse.locatorId',
    },
    {
      name: 'sourceLocatorCode',
      type: FieldType.string,
      bind: 'fromWarehouse.locatorCode',
    },
    {
      name: 'fromLocator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromLocator`).d('来源库位'),
      lovCode: 'MT.MODEL.SUB_LOCATOR',
      ignore: 'always',
      dynamicProps: {
        disabled: ({ record }) => {
          // RETURN且只要有RECEIVE_TO_SITE，也是指来源仓库和来源库位都禁用
          return (record?.get('instructionType') || []).indexOf('RECEIVE_TO_SITE') !== -1 && record?.get('docTypeTag') === 'RETURN';
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorIds: record?.get('sourceLocatorId') || null,
            locatorCategory: ['LOCATION', 'INVENTORY'],
          };
        },
      },
    },
    {
      name: 'sourceSubLocatorId',
      type: FieldType.string,
      bind: 'fromLocator.locatorId',
    },
    {
      name: 'sourceSubLocatorCode',
      type: FieldType.string,
      bind: 'fromLocator.locatorCode',
    },
    {
      name: 'sourceSumOnhandQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.fromQty`).d('来源库存现有量'),
    },
    {
      name: 'toWarehouse',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toWarehouse`).d('目标仓库'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      noCache: true,
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('toLocatorRequiredFlag') === 'Y';
          // return ((record?.get('instructionType') || []).indexOf('SENT_FROM_SITE') === -1 && record?.get('docTypeTag') === 'PICK') && record?.get('toLocatorRequiredFlag') === 'Y';
        },
        disabled: ({ record }) => {
          // PICK且只要有SENT_FROM_SITE,  目标仓库和目标货位都禁用
          return (record?.get('instructionType') || []).indexOf('SENT_FROM_SITE') !== -1 && record?.get('docTypeTag') === 'PICK';
        },
        lovPara: ({ record }) => {
          const strategyList = record?.get('strategyList') || [];
          const sortStrategy = strategyList.sort((a, b) => {
            return b.sequence - a.sequence;
          });
          const { businessType } = sortStrategy[0] || {};
          return {
            tenantId,
            siteIds: record?.get('siteId'),
            locatorCategoryList: ['AREA'],
            businessTypes: businessType,
            queryType: 'TARGET',
          };
        },
      },
    },
    {
      name: 'targetLocatorId',
      type: FieldType.string,
      bind: 'toWarehouse.locatorId',
    },
    {
      name: 'targetLocatorCode',
      type: FieldType.string,
      bind: 'toWarehouse.locatorCode',
    },
    {
      name: 'strategy',
      type: FieldType.string,
    },
    {
      name: 'strategyList',
    },
    {
      name: 'businessType',
      type: FieldType.string,
    },
    {
      name: 'instructionType',
    },
    {
      name: 'docTypeTag',
      type: FieldType.string,
    },
    {
      name: 'toLocator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toLocator`).d('目标货位'),
      lovCode: 'MT.MODEL.SUB_LOCATOR',
      noCache: true,
      dynamicProps: {
        disabled: ({ record }) => {
          // PICK且只要有SENT_FROM_SITE,  目标仓库和目标货位都禁用
          return (record?.get('instructionType') || []).indexOf('SENT_FROM_SITE') !== -1 && record?.get('docTypeTag') === 'PICK';
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorIds: record?.get('targetLocatorId') || null,
            locatorCategory: ['LOCATION', 'INVENTORY'],
          };
        },
      },
    },
    {
      name: 'toLocatorId',
      type: FieldType.string,
      bind: 'toLocator.locatorId',
    },
    {
      name: 'toLocatorCode',
      type: FieldType.string,
      bind: 'toLocator.locatorCode',
    },
    {
      name: 'targetSumOnhandQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.targetActualQty`).d('目标库存现有量'),
    },
    {
      name: 'siteId',
      type: FieldType.string,
    },
    // {
    //   name: 'instructionDocType',
    //   type: FieldType.string,
    // },
    {
      name: 'quantityMax',
      type: FieldType.string,
    },
    {
      name: 'findId',
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'toleranceType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('toleranceFlag') === 'Y';
        },
        disabled: ({ record }) => {
          return record?.get('toleranceFlag') === 'N';
        },
      },
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差'),
      min: 0,
      dynamicProps: {
        required: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'Y' &&
            (record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER')
          );
        },
        disabled: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'N' ||
            (record?.get('toleranceType') !== 'PERCENTAGE' &&
              record?.get('toleranceType') !== 'NUMBER')
          );
        },
        max: ({ record }) => {
          if (record?.get('toleranceType') === 'PERCENTAGE') {
            return 100;
          }
        },
      },
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差'),
      min: 0,
      dynamicProps: {
        required: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'Y' &&
            (record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER')
          );
        },
        max: ({ record }) => {
          if (record?.get('toleranceType') === 'PERCENTAGE') {
            return 100;
          }
        },
        disabled: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'N' ||
            (record?.get('toleranceType') !== 'PERCENTAGE' &&
              record?.get('toleranceType') !== 'NUMBER')
          );
        },
      },
    },
    {
      name: 'instructionStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'soLineId',
    },
    {
      name: 'soNumConfig',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNumConfig`).d('按单标识'),
      trueValue: 'Y',
      falseValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => {
          return !(Math.abs(record?.get('soLineId')) > 0);
        },
      },
    },
  ],
  events: {
    update: ({record, name}) => {
      if (
        name === 'materialLov' ||
        name === 'toSiteLov' ||
        name === 'fromWarehouse' ||
        name === 'toWarehouse' ||
        name === 'toLocator' ||
        name === 'fromLocator'
      ) {
        const {
          materialLov,
          toSiteLov,
          fromWarehouse,
          toWarehouse,
          toLocator,
          fromLocator,
        } = record.toData();
        const obj = {
          materialId: materialLov?.materialId || record.data.materialId,
          toSiteId: toSiteLov?.siteId || record.data.siteId,
          fromSiteId: toSiteLov?.siteId || record.data.siteId,
          organizationId: tenantId,
          fromLocatorId: fromLocator?.locatorId || fromWarehouse?.locatorId,
          toLocatorId: toLocator?.locatorId || toWarehouse?.locatorId,
        };
        const params = {};
        Object.entries(obj).forEach(item => {
          if (item[1] !== undefined) {
            params[item[0]] = item[1];
          }
        });
        request(url, {
          method: 'GET',
          query: {...params},
        }).then(res => {
          if (res?.success) {
            if (res?.rows) {
              if (res.rows?.fromIdentifyType) {
                record.init('fromIdentifyType', res.rows.fromIdentifyType);
              }
              if (res.rows?.toIdentifyType) {
                record.init('toIdentifyType', res.rows.toIdentifyType);
              }
            }
          }
        });
      }
    },
  },
});

export { headerFormDS, lineTableDS };
