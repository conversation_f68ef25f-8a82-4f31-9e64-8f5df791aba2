.operationPlatformEnter {
  display: contents;

  :global {
    .page-head {
      background-color: #38708f !important;
    }

    .page-container {
      border-radius: 0px !important;
    }

    .page-content-wrap {
      margin: 0 !important;
    }

    .page-head.page-head .page-head-title {
      color: rgba(1, 225, 239, 1) !important;
      // padding-left: 47% !important;
      position: absolute !important;
      font-size: 20px !important;
      font-weight: 700 !important;
    }
    .c7n-spin-nested-loading{
      height: 100% !important;
      .c7n-spin-container{
        height: 100% !important;
      }
    }
    .footerBackground {
      position: absolute;
      // top: 100;
      right: 0;
      height: 450px;
      width: 800px;
    }
  }

  .editing-pointer-none {
    pointer-events: none;
  }
}

.headers{
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-direction: row-reverse;
  .titleEnterInfo{
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 16px;
    color: #01e1ef;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    span{
      display: flex;
      align-items: center;
    }
  }
  button{
    margin-left: 12px;
  }
}

.card-container {
  padding: 0px;
  // position: relative;
  background-color: #fff;
  // box-shadow: 10px 10px 10px #ebebeb;
  display: flex;
  flex-direction: column;

  &-editing {
    border: 1px dashed #979797;
  }

  .card-lalala {
    // overflow-y: auto;
    /* flex-grow: 1;
    flex-shrink: 1;
    overflow: hidden; */
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .card-content {
    flex-grow: 1;
    width: 100%;
    height: 100%;
    overflow: auto;
  }

  .card-close-icon {
    position: absolute;
    pointer-events: all;
    right: 8px;
    top: 8px;
    width: 20px;
    height: 20px;
    transition: opacity 0.2s;
    cursor: pointer;
    z-index: 3;

    &:hover {
      opacity: 0.9;
    }
  }
}

.gridLayoutContainer {
  height: auto;
  &.gridLayoutContainerHalf{
    height: calc(100% - 58px) !important;
    overflow-y: auto;
  }
  .dragCard {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    visibility: visible;
    cursor: move;
  }

  .closeBtn {
    position: absolute;
    top: 5px;
    right: 15px;
    width: 20px;
    font-size: 22px;
    cursor: pointer;
    z-index: 10000;
  }

  .boxShadow {
    border-radius: 2px;
    transition: 0.5s;
    box-shadow: rgba(0, 34, 77, 0.1) 0 1px 3px;
  }

  .boxShadow:hover {
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);
  }

  :global {
    .react-grid-item {
      overflow: hidden;
      background-color: #fff;
      display: flex;
      flex-direction: column;
    }

    .react-grid-item.react-grid-placeholder {
      background-color: #909090;
      z-index: 0;
    }

    .ant-card-body {
      overflow: auto;
      flex: auto;
    }

    .react-grid-item.react-draggable {
      border: 1px dashed #999;
      padding: 1px;
    }
  }
}

.independentLayout{
  width: 100%;
  height: 100%;
  margin: auto;
  padding: 10px 10px 90px 10px;
  position: relative;
  .closeBtn {
    position: absolute;
    top: 5px;
    right: 15px;
    width: 20px;
    font-size: 22px;
    cursor: pointer;
    z-index: 10000;
  }
  .card-container{
    width: calc(100% - 100px);
    height: 100%;
  }
  .goods_sale_property_checked{
    width: 80%;
  }

  .independentOperation{
    width: 80px;
    max-height: 76%;
    position: fixed;
    right: 15px;
    bottom: 15px;
    // display: flex;
    // justify-content: flex-end;
    // flex-direction: column;
    // align-items: center;
    overflow-y: auto;
    .independentCards{
      width: 60px;
      height: 60px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 100%;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.75);
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      text-align: center;
      padding: 5px;
    }
    .independentSelect{
      background: rgba(17, 194, 207, 1);
      color: #fff;
      box-shadow: 5px 8px 12px  rgba(0, 0, 0, 0.16);
    }
    .cardSelect{
      width: 60px;
      height: 60px;
      background-color: rgba(75,227,238,1);
      border-radius: 100%;
      position: relative;
      overflow: hidden;
      cursor: pointer;
      .squareOne{
        width: 20px;
        height: 20px;
        background-color: #fff;
        position: absolute;
        bottom: 14px;
        left: 21px;
        border-radius: 2px;
        transform: rotateZ(135deg);
        .squareTwo{
          width: 25px;
          height: 25px;
          background-color: rgba(75,227,238,1);
          position: absolute;
          bottom: 4px;
          left: 4px;
          border-radius: 2px;
        }
      }
    }
  }
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 14px;
    background: #88a9bc;
  }
}

.independentButtons{
  width: calc(100% - 10px);
  height: 48px;
  background-color: #195674;
  position: absolute;
  bottom: 0;
  left: 10px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 20px;
}

.previewImgCard {
  border: none !important;
  box-shadow: 0 6px 9px 0 #19567491 !important;

  :global {
    .c7n-card-body {
      padding: 0 !important;
    }
  }

  .cardName {
    color: white;
    padding: 4px 6px;
    box-shadow: 0 -6px 10px 0 #195674;
  }
}
