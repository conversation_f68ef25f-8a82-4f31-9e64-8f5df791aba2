import React, { useMemo } from 'react';
import DashboardCard from '../DashboardCard.jsx';
import ScrollBoard from '../ScrollBoard.jsx';
import styles from '../../index.module.less';

const CheckSupplier = ({data}) => {
  const tableData = [];
  if (data.length) {
    data.forEach((val) => {
      const {
        inspectDate,
        supplierCode,
        bomCode,
        model,
        inspectItemDesc,
        disposalResult,
      } = val;
      tableData.push([
        `<Tooltip title='${inspectDate}'><span>${inspectDate}</span></Tooltip>`,
        `<Tooltip title='${supplierCode}'><span>${supplierCode}</span></Tooltip>`,
        `<Tooltip title='${bomCode}'><span>${bomCode}</span></Tooltip>`,
        `<Tooltip title='${model}'><span>${model}</span></Tooltip>`,
        `<Tooltip title='${inspectItemDesc}'><span>${inspectItemDesc}</span></Tooltip>`,
        `<Tooltip title='${disposalResult}'><span>${disposalResult}</span></Tooltip>`,
      ]);
    });
  }
  const config = useMemo(
    () => ({
      header: ['检验日期','供应商代码', 'BOM', '规格', '不合格检验项目描述', '处置结果'],
      data: tableData,
      rowNum: 11,
      align: ['center'],
      oddRowBGC: 'rgba(22,66,127,0.3)',
      headerBGC: 'rgb(3, 157, 206,0.3)',
      evenRowBGC: 'rgba(3,28,60, 0.3)',
      headerHeight: 40,
      columnWidth: [120, 120, 100, 100, 200, 120],
    }),
    [tableData],
  );
  return (
    <DashboardCard height="100%" >
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-scroll-board-title']}>
          来料检验不合格信息
        </div>
        <div className={styles['my-scroll-board-table']}>
          <ScrollBoard config={config} style={{ width: '100%', height: '100%', marginRight: '10px' }} />
        </div>
      </div>
    </DashboardCard>
  );
};

export default CheckSupplier;
