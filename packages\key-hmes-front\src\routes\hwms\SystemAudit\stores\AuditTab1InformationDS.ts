/**
 * @Description: 体系审核管理维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.systemAudit';
const tenantId = getCurrentOrganizationId();

// 详情-审核信息tab
const auditInformationDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewPlanCode`).d('计划编号'),
      name: 'sysReviewPlanCode',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.planCreater`).d('计划创建人'),
      name: 'planCreaterLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      disabled: true,
    },
    {
      name: 'createdBy',
      bind: 'planCreaterLov.id',
    },
    {
      name: 'createdByName',
      bind: 'planCreaterLov.realName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.leader`).d('组长'),
      name: 'leaderLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'groupLeader',
      bind: 'leaderLov.id',
    },
    {
      name: 'groupLeaderName',
      bind: 'leaderLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewTitle`).d('审核标题'),
      name: 'sysReviewTitle',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewType`).d('审核类型'),
      name: 'sysReviewType',
      lookupCode: 'YP.QIS.SYS_REVIEW_TYPE',
      required: true,
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.SYS_REVIEW_PLAN_STATUS',
      name: 'sysReviewPlanStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewAim`).d('审核目的'),
      name: 'sysReviewAim',
      required: true,
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.reviewUuid`).d('附件'),
      name: 'reviewUuid',
      bucketName: 'qms',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewScope`).d('审核范围'),
      name: 'sysReviewScope',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStandard`).d('审核标准'),
      name: 'sysReviewStandardNameList',
      required: true,
      multiple: true,
    },
    {
      name: 'sysReviewStandardIdList',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardMoreInfo`).d('标准补充说明'),
      name: 'standardMoreInfo',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherBasis`).d('其他依据'),
      name: 'otherBasis',
    },
  ],
});

export { auditInformationDS };
