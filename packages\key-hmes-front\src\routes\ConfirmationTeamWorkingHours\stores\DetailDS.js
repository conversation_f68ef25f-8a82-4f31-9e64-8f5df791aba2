import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment';


const modelPrompt = 'tarzan.receive.confirmationTeamWorkingHours';
const tenantId = getCurrentOrganizationId();
const HMES_BASIC = BASIC.HMES_BASIC;


const headerFormDS = () => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  dataKey: 'rows.instructionDoc',
  cacheSelection: true,
  fields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      required: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'site.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'handoverNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.handoverNum`).d('交班号'),
    },
    {
      name: 'shiftType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftType`).d('班次'),
      lookupCode: 'HME.HANDOVER_TIME',
      required: true,
      valueField: 'value',
      textField: 'meaning',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLov`).d('产线编码'),
      lovCode: 'HME.PERMISSION_PROD_LINE',
      noCache: true,
      required: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'productionLineId',
      type: FieldType.number,
      bind: 'prodLov.prodLineId',
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      bind: 'prodLov.prodLineCode',
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      bind: 'prodLov.prodLineName',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线描述'),
    },
    {
      name: 'totalManHour',
      type: 'number',
      required: true,
      label: intl.get(`${modelPrompt}.totalManHour`).d('总工时'),
      precision: 3,
      min: 0,
    },
    {
      name: 'userLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.userLov`).d('交班人'),
      lovCode: 'MT.USER.ORG',
      noCache: true,
      required: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'shiftUser',
      type: FieldType.number,
      bind: 'userLov.id',
    },
    {
      name: 'realName',
      type: FieldType.string,
      bind: 'userLov.realName',
    },
    { name: 'shiftDate',
      required: true,
      type: 'date',
      // format: 'YYYY-MM-DD',
      label: intl.get(`${modelPrompt}.shiftDate`).d('日期'),
    },
    {
      name: 'matters',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.matters`).d('问题点'),
    },
    {
      name: 'solution',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.solution`).d('对策'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('跟进事项'),
    },
  ],
  events: {
    update({ record, name  }) {
      // if (name === 'totalManHour') {
      //   if(record.get('totalManHour') === 0){
      //     record.set('totalManHour', null);
      //   }
      // }
      if(name === 'shiftType'){
        if(record.get('shiftType') === 'A'){
          record.set('shiftDate', moment().format('YYYY-MM-DD'));
        }else if(record.get('shiftType') === 'B'){
          // 判断时间如果在20:00:00-24:00:00时 日期为当天日期
          // 时间在00:00:00-20:00:00则日期减1
          const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
          const startTime = `${moment().format('YYYY-MM-DD')} 20:00:00`
          // const currentTime = moment().endOf('day')
          if(moment(currentTime).diff(moment(startTime))<0){
            record.set('shiftDate', moment().subtract(1, 'days').format('YYYY-MM-DD'));
          }else{
            record.set('shiftDate', moment().format('YYYY-MM-DD'));
          }
        }else{
          record.set('shiftDate', null);
        }
      }
    },
  },
});

const lineTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  dataKey: 'content',
  selection: false,
  transport: {
    read: () => {
      const url = `${HMES_BASIC}/v1/${tenantId}/hme-handovers/detail/ui`;
      return {
        url,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.quantity`).d('产出数量'),
    },
    {
      name: 'manHour',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.manHour`).d('分配工时'),
      required: true,
      precision: 3,
    },
    {
      name: 'sapFlagMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sapFlag`).d('上传状态'),
    },
  ],
  events: {
    update({ record, name  }) {
      if (name === 'manHour') {
        if(record.get('manHour') === 0){
          record.set('manHour', null);
        }
      }
    },
  },
});

export { headerFormDS, lineTableDS };
