import React, { FC, useEffect, useState } from 'react';
import { RouteComponentProps } from 'react-router';
import {
  Attachment,
  Button,
  DataSet,
  Form,
  Lov,
  NumberField,
  Select,
  Table,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import JsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { Collapse, Icon, Popconfirm } from 'choerodon-ui';
import axios from 'axios';
import notification from 'utils/notification';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId, getCurrentUser, getCurrentUserId } from 'utils/utils';
import basicPageFactry from '../stores/basicPageFactry';
import detailFactory from '../stores/detailFactory';
import './index.less';

import { fetchLocatorConfig, fetchDetailInfoConfig } from '../services';

const Panel = Collapse.Panel;

const modelPrompt = 'tarzan.qms.disassembleTaskExecutionPlatform';

interface InspectItemListProps extends RouteComponentProps {
  basicDs: DataSet;
  detailDs: DataSet;
  customizeTable: any;
}

let imageArr: any = {};

const DisassembleTaskExecutionPlatform: FC<InspectItemListProps> = props => {
  const {
    match: { params, path },
    basicDs,
    detailDs,
    history,
    customizeTable,
  } = props;

  const [disabledFlag, setDisabledFlag] = useState<boolean>(true); // 禁用标识-部分逻辑存在ds中

  const [pageSatus, setPageStatus] = useState<'create' | 'detail'>('create'); // 页面状态为新建或详情

  const [editFlag, setEditFLag] = useState(false); // 编辑按钮是否可用

  const [submitFlag, setSubmitFlag] = useState(false); // 提交按钮是否可用

  const [ok, setOk] = useState(false); // 完成按钮是否可用

  const [pdfPrintFlag, setPdfPrintFlag] = useState(false); // 生成拆解报告按钮是否可用，

  const [data, setData] = useState<any>({}); // 数据列表用于导出pdf

  const [lineInfoList, setLineInfoList] = useState([]); // 导出pdf中的缺陷信息table列

  const [photoList, setPhotoList] = useState([]); // 导出pdf中的图片列表

  const [showPdfDom, setShowPdfDom] = useState(false); // pdf打印列表的渲染。

  // 查询站点和产品对应库位
  const fetchLocator = useRequest(fetchLocatorConfig(), {
    manual: true,
    needPromise: true,
  });

  const fetchDetailInfo = useRequest(fetchDetailInfoConfig(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if ((params as any).id === 'create') {
      setPageStatus('create');
      setDisabledFlag(false);
    } else {
      setPageStatus('detail');
      getDetailsMessage();
    }
  }, [(params as any).id]);

  useEffect(() => {
    getCanEditRoles('YP.QIS.TEARDOWN_TASK_EDIT').then((res: any) => {
      basicDs.setState(
        'canEditRoles',
        res?.lookupCode.map(item => item.value),
      );
      detailDs.setState(
        'canEditRoles',
        res?.lookupCode.map(item => item.value),
      );
    });
    getCanEditRoles('YP.QIS.TEARDOWN_TASK_REVIEW').then((res: any) => {
      detailDs.setState(
        'reviewRoles',
        res?.lookupCode.map(item => item.value),
      );
      basicDs.setState(
        'reviewRoles',
        res?.lookupCode.map(item => item.value),
      );
    });
    detailDs.addEventListener('update', ({ dataSet, name }) => {
      if (name === 'engineerScore') {
        basicDs.current?.set(
          'teardownTaskScore',
          dataSet.toData().reduce((pre, current) => {
            return pre + (current.engineerScore || 0);
          }, 0),
        );
      }
    });
  }, []);

  useEffect(() => {
    setLineInfoList(renderLineInfos());
  }, [data]);

  // 切换查询树类型时，进行查询
  useDataSetEvent(basicDs, 'update', ({ name, record }) => {
    if (['productFormCode'].includes(name)) {
      getLocator(
        {
          siteId: record.get('siteId'),
          productFormCode: record.get('productFormCode'),
        },
        'update',
      );
    }

    if (name === 'materialLotCodeObj') {
      getDetailInfo(record.get('materialLotCode'));
      getLocator(
        {
          siteId: record.get('siteId'),
          productFormCode: record.get('productFormCode'),
        },
        'update',
      );
    }

    detailDs.setState('basicData', basicDs.current?.toData());
  });

  const getDetailsMessage = () => {
    if ((params as any).id === 'create') {
      return;
    }
    axios
      .get(
        `${
          BASIC.TARZAN_SAMPLING
        }/v1/${getCurrentOrganizationId()}/qis-teardown-task/detail/ui?teardownTaskId=${
          (params as any).id
        }`,
      )
      .then(async (res: any) => {
        if (res && res.success) {
          imageArr = {};
          const { headerInfo, lineInfos = [] } = res.rows;
          const _photoList: any = [];
          lineInfos?.forEach(async (item, index) => {
            item.index = index * 10 + 10;
            if (item?.enclosure) {
              const currentPhotoList = await getPhotoUrl(item?.enclosure);
              _photoList[index] = currentPhotoList;
            } else {
              _photoList[index] = [];
            }
            if (_photoList?.length === lineInfos?.length) {
              setPhotoList(() => renderPhotoList(_photoList));
            }
          });
          setData({
            headerInfo,
            lineInfos,
          });
          basicDs.loadData([headerInfo]);
          detailDs.loadData(lineInfos || []);
          detailDs.setState('basicData', headerInfo);
          editCLickFlag();
          getLocator(
            {
              siteId: headerInfo.siteId,
              productFormCode: headerInfo.productFormCode,
            },
            'init',
          );
        }
      })
      .catch((res: any) => {
        return notification.error({
          message: res.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          description: '',
        });
      });
  };

  const getCanEditRoles = async (lookupCode: string) => {
    const res = await axios.get(`/hpfm/v1/${getCurrentOrganizationId()}/lovs/value/batch`, {
      params: {
        lookupCode,
      },
    });
    return res;
  };

  const editCLickFlag = () => {
    setEditFLag(
      (basicDs.current!.get('teardownTaskStatus') === 'NEW' &&
        basicDs.current!.get('teardownTaskType') === 'MANUAL') ||
        (!!basicDs.getState('canEditRoles') &&
          basicDs.getState('canEditRoles').length > 0 &&
          basicDs.getState('canEditRoles').includes(getCurrentUser().currentRoleCode) &&
          basicDs.current?.get('teardownPersonId') === getCurrentUserId() &&
          basicDs.current?.get('teardownTaskStatus') === 'SPILTING') ||
        (!!detailDs.getState('reviewRoles') &&
          detailDs.getState('reviewRoles').length > 0 &&
          detailDs.getState('reviewRoles').includes(getCurrentUser().currentRoleCode) &&
          basicDs.current?.get('teardownTaskStatus') === 'REVIEWING'),
    );
    setSubmitFlag(
      (basicDs.getState('canEditRoles')?.includes(getCurrentUser().currentRoleCode) &&
        ['SPILTING'].includes(basicDs.current?.get('teardownTaskStatus'))) ||
        (basicDs.current!.get('teardownTaskStatus') === 'NEW' &&
          basicDs.current!.get('teardownTaskType') === 'MANUAL'),
    );
    setOk(
      basicDs.current?.get('teardownTaskStatus') === 'REVIEWING' &&
        detailDs.getState('reviewRoles')?.includes(getCurrentUser().currentRoleCode) &&
        detailDs.toData().length > 0,
    );
    setPdfPrintFlag(basicDs.current?.get('teardownTaskStatus') === 'COMPLETED');
  };

  const handleConfirm = () => {
    axios
      .post(
        `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-teardown-task/submit/ui`,
        {
          teardownTaskIds: [basicDs.current?.get('teardownTaskId')],
          teardownTaskStatus: basicDs.current?.get('teardownTaskStatus'),
          teardownTaskType: basicDs.current?.get('teardownTaskType'),
        },
      )
      .then((res: any) => {
        if (res && res?.success) {
          getDetailsMessage();
          return notification.success({
            message: res?.message || intl.get(`${modelPrompt}.notification.success`).d('操作成功'),
            description: '',
          });
        }
      })
      .catch((res: any) => {
        return notification.error({
          message: res.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          description: '',
        });
      });
  };

  // const handleExportPdf = () => {
  //   const pdf = new JsPDF('l', 'pt', 'a4');
  //   setShowPdfDom(true);
  //   const time = setTimeout(() => {
  //     if (document.querySelector('#pdf')) {
  //       html2canvas(document.querySelector('#pdf')!, {
  //         dpi: window.devicePixelRatio * 2,
  //         useCORS: true,
  //       } as any).then(canvas => {
  //         const ctx = canvas.getContext('2d');
  //         const a4w = 800;
  //         const a4h = 530; // A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
  //         const imgHeight = Math.floor((a4h * canvas.width) / a4w); // 按A4显示比例换算一页图像的像素高度
  //         let renderedHeight = 0;

  //         while (renderedHeight < canvas.height) {
  //           const page = document.createElement('canvas');
  //           page.width = canvas.width;
  //           page.height = Math.min(imgHeight, canvas.height - renderedHeight); // 可能内容不足一页

  //           // 用getImageData剪裁指定区域，并画到前面创建的canvas对象中
  //           page
  //             ?.getContext('2d')
  //             ?.putImageData(
  //               ctx!.getImageData(
  //                 0,
  //                 renderedHeight,
  //                 canvas.width,
  //                 Math.min(imgHeight, canvas.height - renderedHeight),
  //               ),
  //               0,
  //               0,
  //             );
  //           pdf.addImage(
  //             page.toDataURL('image/jpeg', 1.0),
  //             'JPEG',
  //             20,
  //             30,
  //             a4w,
  //             Math.min(a4h, (a4w * page.height) / page.width),
  //           );

  //           renderedHeight += 710;
  //           if (renderedHeight < canvas.height) {
  //             pdf.addPage(); // 如果后面还有内容，添加一个空页
  //           }
  //         }
  //         pdf.save('拆解报告.pdf');
  //         setShowPdfDom(false);
  //       });
  //     }
  //     clearTimeout(time);
  //   }, 100);
  // };

  const handleExportPdfTest = () => {
    return new Promise((resolve) => {
      setShowPdfDom(true);
      if (document.querySelector('#pdf')) {
        html2canvas(document.querySelector('#pdf')!, {
          dpi: window.devicePixelRatio * 2,
          useCORS: true,
        } as any).then(canvas => {
          // 未生成pdf的html页面高度
          let leftHeight = canvas.height;

          const a4Width = 800;
          const a4Height = 530; // A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
          // 一页pdf显示html页面生成的canvas高度;
          const a4HeightRef = Math.floor((canvas.width / a4Width) * a4Height);

          // pdf页面偏移
          let position = 0;

          const pageData = canvas.toDataURL('image/jpeg', 1.0);

          const pdf = new JsPDF('l', 'pt', 'a4');
          const canvas1 = document.createElement('canvas');
          let height;
          pdf.setDisplayMode('fullwidth', 'continuous', 'FullScreen');

          const pdfName = '拆解报告';

          function createImpl(canvas) {
            if (leftHeight > 0) {

              let checkCount = 0;
              if (leftHeight > a4HeightRef) {
                let i = position + a4HeightRef;
                for (i = position + a4HeightRef; i >= position; i--) {
                  let isWrite = true;
                  for (let j = 0; j < canvas.width; j++) {
                    const c = canvas.getContext('2d').getImageData(j, i, 1, 1).data;

                    if (c[0] !== 0xff || c[1] !== 0xff || c[2] !== 0xff) {
                      isWrite = false;
                      break;
                    }
                  }
                  if (isWrite) {
                    checkCount++;
                    if (checkCount >= 10) {
                      break;
                    }
                  } else {
                    checkCount = 0;
                  }
                }
                height = Math.round(i - position) || Math.min(leftHeight, a4HeightRef);
                if (height <= 0) {
                  height = a4HeightRef;
                }
              } else {
                height = leftHeight;
              }

              canvas1.width = canvas.width;
              canvas1.height = height;

              const ctx = canvas1.getContext('2d');
              ctx?.drawImage(canvas, 0, position, canvas.width, height, 0, 0, canvas.width, height);

              if (position !== 0) {
                pdf.addPage();
              }
              pdf.addImage(
                canvas1.toDataURL('image/jpeg', 1.0),
                'JPEG',
                20,
                30,
                a4Width,
                (a4Width / canvas1.width) * height,
              );
              leftHeight -= height;
              position += height;
              if (leftHeight > 0) {
                setTimeout(createImpl, 500, canvas);
              } else {
                pdf.save(`${pdfName}.pdf`);
              }
            }
          }

          // 当内容未超过pdf一页显示的范围，无需分页
          if (leftHeight < a4HeightRef) {
            pdf.addImage(pageData, 'JPEG', 0, 0, a4Width, (a4Width / canvas.width) * leftHeight);
            pdf.save(`${pdfName}.pdf`);
            return resolve(true);
          }
          try {
            pdf.deletePage(0);
            createImpl(canvas);
            return resolve(true);
          } catch (err) {
            console.log(err);
            return resolve(false);
          }
        });
      }
    })
  };

  const handleSave = async () => {
    if (disabledFlag) {
      return false;
    }
    const flag1 = await basicDs.current?.validate(true);
    const flag2 = await detailDs.validate();
    if (flag1 && flag2) {
      axios
        .post(
          `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-teardown-task/update/ui`,
          {
            headerInfo: {
              ...basicDs.current?.toJSONData(),
            },
            lineInfos: detailDs.toData(),
          },
        )
        .then((res: any) => {
          if (res && res?.success) {
            setDisabledFlag(true);
            history.push(`/hwms/disassemble/task-execution-platform/detail/${res.rows}`);
            getDetailsMessage();
            return notification.success({
              message:
                res?.message || intl.get(`${modelPrompt}.notification.success`).d('操作成功'),
              description: '',
            });
          }
        })
        .catch((res: any) => {
          return notification.error({
            message: res.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
            description: '',
          });
        });
    }
  };

  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.png', '.jpg', '.jpeg'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const handleLookSip = () => {
    const sipCode = basicDs.current?.get('sipCode');
    if (!sipCode) {
      return notification.error({
        message: intl.get(`${modelPrompt}.sipCode.error`).d('根据拆解类型未找到对应的SIP编码，请检查！'),
      });
    }
    history.push({
      pathname: '/hwms/sip-file-manage/list',
      state: { sipCode },
    });
  };

  const handleOk = async () => {
    const flag = await basicDs.current?.validate(true);
    const flag1 = await detailDs.validate();
    if (!flag || !flag1) {
      return;
    }
    if (!disabledFlag) {
      return notification.warning({
        message: intl.get(`${modelPrompt}.message.nosave`).d('请先保存数据'),
        description: '',
      });
    }
    axios
      .post(
        `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-teardown-task/complete/ui`,
        {
          teardownTaskIds: [basicDs.current?.get('teardownTaskId')],
          teardownReviewedBy: getCurrentUser().id,
          teardownReviewedName: getCurrentUser().realName,
        },
      )
      .then((res: any) => {
        if (res && res?.success) {
          getDetailsMessage();
          return notification.success({
            message: res?.message || intl.get(`${modelPrompt}.notification.success`).d('操作成功'),
            description: '',
          });
        }
      })
      .catch((res: any) => {
        return notification.error({
          message: res.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
          description: '',
        });
      });
  };

  const handleCancelEdit = () => {
    if (pageSatus === 'create') {
      history.push('/hwms/disassemble/task-execution-platform/list');
    } else {
      setDisabledFlag(true);
      basicDs.reset();
      detailDs.reset();
    }
  };

  const getLocator = async (params, type) => {
    const res = await fetchLocator.run({
      params,
    });

    if (type !== 'init') {
      detailDs.forEach(record => {
        record.set('teardownLocation', null);
        record.set('defectCode', null);
        record.set('defectLeave', null);
      });
    }

    detailDs.setState('locator', (res?.content || [])[0] || {});
  };

  const getDetailInfo = async key => {
    let rows: any = {};
    if (key) {
      const res = await fetchDetailInfo.run({
        params: {
          materialLotCode: key,
        },
      });

      if (res?.rows) {
        rows = res?.rows;
      }
    }

    basicDs.current?.set('siteCode', rows.siteCode || null);
    basicDs.current?.set('siteName', rows.siteName || null);
    basicDs.current?.set('prodLineCode', rows.prodLineCode || null);
    basicDs.current?.set('prodLineId', rows.prodLineId || null);
    basicDs.current?.set('prodLineName', rows.prodLineName || null);
    basicDs.current?.set('model', rows.model || null);
  };

  const headerButton = [
    <Button icon="save" color={ButtonColor.primary} onClick={handleSave} disabled={disabledFlag}>
      {intl.get('hzero.common.button.save').d('保存')}
    </Button>,
    // 编辑
    !disabledFlag ? (
      <Button onClick={handleCancelEdit}>
        {intl.get('hzero.common.button.cancel').d('取消')}
      </Button>
    ) : (
      <>
        <Button onClick={handleLookSip}>
          {intl.get(`${modelPrompt}.button.lookSip`).d('查看SIP')}
        </Button>
        <Button disabled={!editFlag} onClick={() => setDisabledFlag(false)}>
          {intl.get('hzero.common.button.edit').d('编辑')}
        </Button>
        <Popconfirm
          title={intl.get(`${modelPrompt}.sure.submit`).d('确认提交?')}
          onConfirm={handleConfirm}
          okText={intl.get('tarzan.common.button.ok').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button disabled={!submitFlag || !disabledFlag}>
            {intl.get(`${modelPrompt}.button.submit`).d('提交')}
          </Button>
        </Popconfirm>
        <Popconfirm
          title={intl.get(`${modelPrompt}.sure.submit`).d('确认完成拆解任务?')}
          onConfirm={handleOk}
          okText={intl.get('tarzan.common.button.ok').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button disabled={!ok || !disabledFlag}>
            {intl.get(`${modelPrompt}.button.ok`).d('完成')}
          </Button>
        </Popconfirm>

        <Button onClick={handleExportPdfTest} disabled={!pdfPrintFlag}>
          {intl.get(`${modelPrompt}.button.generate.analysis.report`).d('生成解析报告')}
        </Button>
      </>
    ),
  ];

  const handleAddLine = () => {
    let _maxSequence = 0;
    detailDs.forEach((_record) => {
      if (_record?.get('index') > _maxSequence) {
        _maxSequence = _record?.get('index');
      }
    })
    detailDs.create({index: _maxSequence + 10});
  };

  const detailColumns: ColumnProps[] = [
    {
      name: 'option',
      hidden: !(
        !disabledFlag && ['SPILTING'].includes(basicDs.current?.get('teardownTaskStatus'))
      ),
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={() => handleAddLine()}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => detailDs.remove(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'index',
      align: ColumnAlign.left,
    },
    {
      name: 'teardownLocation',
      editor: !disabledFlag,
    },
    {
      name: 'teardownTaskNcFlag',
      editor: !disabledFlag,
    },
    {
      name: 'defectCode',
      editor: !disabledFlag,
    },
    {
      name: 'teardownNcName',
    },
    {
      name: 'defectLeave',
      editor: !disabledFlag,
    },
    {
      name: 'teardownNcScore',
    },
    {
      name: 'enclosure',
      editor: !disabledFlag && <Attachment {...attachmentProps} />,
    },
    {
      name: 'engineerScore',
      editor: !disabledFlag,
    },
    {
      name: 'remarks',
      editor: !disabledFlag,
    },
  ];

  const judgeColumns = [
    <TextField name="teardownTaskScore" disabled />,
    <TextField name="teardownReviewedName" disabled />,
    <Select name="teardownTaskResult" disabled={disabledFlag} />,
  ];

  const renderLineInfos = () => {
    const page = Math.ceil((data?.lineInfos?.length - 10) / 20);
    const tableMap: any = [];
    for (let i = 0; i < page; i++) {
      tableMap.push(
        <div className="pdf-page-height">
          <table>
            <tbody>
              {data?.lineInfos?.slice(10 + i * 20, 10 + i * 20 + 20) ? (
                <tr>
                  <td className="width-1">序号</td>
                  <td className="width-2">拆解位置</td>
                  <td className="width-2">有无缺陷</td>
                  <td className="width-2">缺陷编码</td>
                  <td className="width-3">缺陷名称</td>
                  <td className="width-2">缺陷等级</td>
                  <td className="width-1">评分</td>
                  <td className="width-2">工程师评分</td>
                  <td className="width-4">备注</td>
                </tr>
              ) : (
                ''
              )}
              {data?.lineInfos?.slice(10 + i * 20, 10 + i * 20 + 20)?.map((item, index) => (
                <tr>
                  <td className="width-1">{(index + 1 + 10 + i * 20) * 10}</td>
                  <td className="width-2">{item?.teardownTaskLocationDesc}</td>
                  <td className="width-2">{item?.teardownTaskNcFlag === 'Y' ? '是' : '否'}</td>
                  <td className="width-2">{item?.teardownNcCode}</td>
                  <td className="width-3">{item?.teardownNcName}</td>
                  <td className="width-2">{item?.teardownNcLevelDesc}</td>
                  <td className="width-1">{item?.teardownNcScore}</td>
                  <td className="width-2">{item?.engineerScore}</td>
                  <td className="width-4">{item?.remarks}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>,
      );
    }
    return tableMap;
  };

  const renderPhotoList = photolist => {
    if (!photolist?.length) {
      return <></>;
    }
    return photolist.map((item, index) => {
      if (!item?.length) {
        return <></>;
      }
      return (
        <div className={item?.length === 1 ? 'img-div pdf-page-height' : 'img-div'}>
          <div className="line-number">{`序号${(index + 1) * 10}`}</div>
          {
            item.map((fileItem) => (
              <div
                className="img-container"
              >
                <img src={fileItem} className='image-element' alt="" />
              </div>
            ))
          }
        </div>
      )
    })
  };

  const getPhotoUrl = async uuid => {
    if (!uuid) {
      return;
    }
    const res: any = await axios.get(
      `/hfle/v1/${getCurrentOrganizationId()}/files/${uuid}/file?attachmentUUID=${uuid}`,
    );
    return (res || []).map((i: any) => i?.fileUrl);
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.list`).d('拆解任务执行平台')}
        backPath="/hwms/disassemble/task-execution-platform/list"
      >
        {headerButton}
      </Header>
      <Content>
        <Collapse activeKey={['basic', 'detail', 'judge']}>
          <Panel header={intl.get(`${modelPrompt}.panel.basic`).d('基础信息')} key="basic">
            <Form dataSet={basicDs} columns={3} labelWidth={112}>
              <TextField name="teardownTaskNum" disabled />,
              <TextField name="teardownApplyNum" disabled />,
              <Select name="materialLotType" disabled />
              <Lov name="materialLotCodeObj" disabled={disabledFlag} />,
              <TextField name="model" disabled />,
              <TextField name="materialCode" disabled />,
              <TextField name="materialName" disabled />,
              <TextField name="siteCode" disabled />,
              <TextField name="prodLineCode" disabled />,
              <Select name="productFormCode" disabled={disabledFlag} />,
              <TextField name="teardownTaskType" disabled />,
              <TextField name="teardownReason" disabled={disabledFlag} />,
              <TextField name="clientName" disabled />,
              <TextField name="sampleDeliveryTime" disabled />,
              <TextField name="teardownPersonName" disabled />,
              <TextField name="teardownTaskTime" disabled />,
              <NumberField name="electricVoltage" disabled={disabledFlag} />,
              <Select name="teardownStage" disabled={disabledFlag} />,
              <Lov name="operationObj" disabled={disabledFlag} />,
              <TextField name="operationDesc" />,
              <TextField name="teardownTaskStatus" disabled />,
              <TextArea name="cancelReason" autoSize={{ minRows: 1, maxRows: 8 }} />,
            </Form>
          </Panel>
          <Panel header={intl.get(`${modelPrompt}.panel.detail`).d('缺陷明细')} key="detail">
            {customizeTable(
              {
                filterCode: `${BASIC.CUSZ_CODE_BEFORE}.DISASSEMBLE_TASK_EXECUTION_PLATFORM.QUERY`,
                code: `${BASIC.CUSZ_CODE_BEFORE}.DISASSEMBLE_TASK_EXECUTION_PLATFORM.TABLE`,
              },
              <Table
                queryBar={TableQueryBarType.none}
                dataSet={detailDs}
                columns={detailColumns}
                searchCode="DisassembleTaskExecutionPlatformDetail"
                customizedCode="DisassembleTaskExecutionPlatformDetail"
              />,
            )}
          </Panel>
          <Panel header={intl.get(`${modelPrompt}.panel.judge`).d('缺陷判定')} key="judge">
            <Form dataSet={basicDs} columns={3} labelWidth={112}>
              {judgeColumns}
            </Form>
            <Form dataSet={basicDs} columns={1} labelWidth={112}>
              <TextArea name="judgeReason" disabled={disabledFlag} />,
            </Form>
          </Panel>
        </Collapse>
        {true ? (
          <div className="pdf-container">
            <div id="pdf">
              <div className="pdf-page-height">
                <h3 style={{ textAlign: 'center' }}>拆解任务</h3>
                <h3>基础信息</h3>
                <table>
                  <tbody>
                    <tr>
                      <td>拆解任务编码</td>
                      <td>{data?.headerInfo?.teardownTaskNum}</td>
                      <td>申请编码</td>
                      <td>{data?.headerInfo?.teardownApplyNum}</td>
                      <td>电芯条码</td>
                      <td>{data?.headerInfo?.materialLotCode}</td>
                    </tr>
                    <tr>
                      <td>电芯型号</td>
                      <td>{data?.headerInfo?.model}</td>
                      <td>物料编码</td>
                      <td>{data?.headerInfo?.materialCode}</td>
                      <td>物料名称</td>
                      <td>{data?.headerInfo?.materialName}</td>
                    </tr>
                    <tr>
                      <td>站点</td>
                      <td>{data?.headerInfo?.siteCode}</td>
                      <td>产线</td>
                      <td>{data?.headerInfo?.prodLineCode}</td>
                      <td>产品形式</td>
                      <td>{data?.headerInfo?.productFormCodeDesc}</td>
                    </tr>
                    <tr>
                      <td>拆解类型</td>
                      <td>{data?.headerInfo?.teardownTaskTypeDesc}</td>
                      <td>拆解原因</td>
                      <td>{data?.headerInfo?.teardownReason}</td>
                      <td>委托人</td>
                      <td>{data?.headerInfo?.clientName}</td>
                    </tr>
                    <tr>
                      <td>送样时间</td>
                      <td>{data?.headerInfo?.sampleDeliveryTime}</td>
                      <td>拆解员</td>
                      <td>{data?.headerInfo?.teardownPersonName}</td>
                      <td>拆解时间</td>
                      <td>{data?.headerInfo?.teardownTaskTime}</td>
                    </tr>
                    <tr>
                      <td>电量</td>
                      <td>{data?.headerInfo?.electricVoltage}</td>
                      <td>阶段</td>
                      <td>{data?.headerInfo?.teardownStageDesc}</td>
                      <td>来源工序编码</td>
                      <td>{data?.headerInfo?.operationName}</td>
                    </tr>
                    <tr>
                      <td>来源工序描述</td>
                      <td>{data?.headerInfo?.operationDesc}</td>
                      <td>拆解评分</td>
                      <td>{data?.headerInfo?.teardownTaskScore}</td>
                      <td>审核结果</td>
                      <td>{data?.headerInfo?.teardownTaskResult}</td>
                    </tr>
                    <tr>
                      <td>审核人</td>
                      <td>{data?.headerInfo?.teardownReviewedName}</td>
                      <td>判断理由</td>
                      <td colSpan={3}>{data?.headerInfo?.judgeReason}</td>
                    </tr>
                  </tbody>
                </table>
                <h3>缺陷信息</h3>
                <table>
                  <tbody>
                    <tr>
                      <td className="width-1">序号</td>
                      <td className="width-2">拆解位置</td>
                      <td className="width-2">有无缺陷</td>
                      <td className="width-2">缺陷编码</td>
                      <td className="width-3">缺陷名称</td>
                      <td className="width-2">缺陷等级</td>
                      <td className="width-1">评分</td>
                      <td className="width-2">工程师评分</td>
                      <td className="width-4">备注</td>
                    </tr>
                    {data?.lineInfos?.slice(0, 10)?.map((item, index) => (
                      <tr>
                        <td className="width-1">{(index + 1) * 10}</td>
                        <td className="width-2">{item?.teardownTaskLocationDesc}</td>
                        <td className="width-2">
                          {item?.teardownTaskNcFlag === 'Y' ? '是' : '否'}
                        </td>
                        <td className="width-2">{item?.teardownNcCode}</td>
                        <td className="width-3">{item?.teardownNcName}</td>
                        <td className="width-2">{item?.teardownNcLevelDesc}</td>
                        <td className="width-1">{item?.teardownNcScore}</td>
                        <td className="width-2">{item?.engineerScore}</td>
                        <td className="width-4">{item?.remarks}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {data?.lineInfos?.length > 10 ? lineInfoList : ''}
              {photoList}
            </div>
          </div>
        ) : (
          ''
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common','hzero.common'],
})(
  withProps(
    () => {
      const basicDs = basicPageFactry();
      const detailDs = detailFactory();
      return {
        basicDs,
        detailDs,
      };
    },
    { cacheState: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.DISASSEMBLE.TASK.EXECUTION.PLATFORM.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.DISASSEMBLE.TASK.EXECUTION.PLATFORM.TABLE`,
      ],
    })(DisassembleTaskExecutionPlatform as any),
  ),
);
