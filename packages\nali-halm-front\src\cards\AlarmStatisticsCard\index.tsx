import React, { useEffect, useState, useCallback, useMemo } from 'react';

import classnames from 'classnames';
import { <PERSON>lapse, Tabs, Icon } from 'choerodon-ui';
import { Dropdown, Menu, Spin } from 'choerodon-ui/pro/lib';
import { Action } from 'choerodon-ui/lib/trigger/enum';
import * as echarts from 'echarts';
import ReactEchartsCore from 'echarts-for-react/lib/core';
import axios from 'axios';
import { getCurrentOrganizationId, getDateTimeFormat } from 'utils/utils';
import { withRouter, RouteComponentProps } from 'react-router-dom';
import moment from 'moment';
import { getMaintSitesOfCurrentUser } from 'alm/services/api';
import { HALM_MTC } from 'alm/utils/config';

import newImg from './assets/new.svg';
import pendingImg from './assets/pending.svg';
import processingImg from './assets/processing.svg';
import styles from './index.module.less';
import EmptyDataPic from './EmptyDataPic';

const organizationId = getCurrentOrganizationId();

type CardColor = 'red' | 'yellow' | 'blue';

interface CardProps {
  imgSrc?: string;
  color: CardColor;
  title: string;
  value: number;
  onClick?: Function;
}

const alarmStatisticsCard = ({ history }: RouteComponentProps) => {
  const [maintSiteId, setMaintSiteId] = useState<number | undefined>(undefined);
  const [maintSiteName, setMaintSiteName] = useState<string>('');
  const [maintSiteList, setMaintSiteList] = useState<any[] | undefined>([]);
  const [statisticData, setStatisticData] = useState<any | undefined>({});
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (maintSiteId) {
      const currentItem = maintSiteList?.find(item => item.maintSiteId === maintSiteId);
      setMaintSiteName(currentItem?.maintSiteName);
      setLoading(true);
      axios({
        url: `${HALM_MTC}/v1/${organizationId}/alarm-records/table-card`,
        method: 'GET',
        params: { maintSiteId },
      }).then(res => {
        setLoading(false);
        setStatisticData(res);
      });
    }
  }, [maintSiteId]);

  const fetchData = useCallback(async () => {
    const res = await getMaintSitesOfCurrentUser();
    setMaintSiteList(res);
  }, []);

  const handleClickMaintSite = useCallback(({ key }) => {
    setMaintSiteId(Number(key));
  }, []);

  const jumpToAlarm = useCallback(params => {
    history.push({
      pathname: `/aori/alarm-record/list`,
      query: params,
    } as any);
  }, []);

  const menu = useMemo(() => {
    if (maintSiteList && maintSiteList?.length > 0) {
      setMaintSiteId(maintSiteList[0].maintSiteId);
    }
    return (
      <Menu onClick={handleClickMaintSite}>
        {maintSiteList?.map(item => (
          <Menu.Item key={item.maintSiteId}>{item.maintSiteName}</Menu.Item>
        ))}
      </Menu>
    );
  }, [maintSiteList]);

  return (
    <div className={styles.container}>
      <Spin spinning={loading}>
        <Collapse
          bordered={false}
          expandIconPosition="right"
          defaultActiveKey={['A']}
          trigger="icon"
          className={styles['customize-collapse']}
        >
          <Collapse.Panel
            key="A"
            showArrow={false}
            header={
              <div className={styles['collapse-header']} onClick={e => e.stopPropagation()}>
                <Dropdown overlay={menu} trigger={[Action.click]}>
                  <span>
                    {maintSiteName} <Icon type="expand_more" />
                  </span>
                </Dropdown>
              </div>
            }
          >
            <div className={styles['collapse-panel-content']}>
              <Card
                color="yellow"
                imgSrc={pendingImg}
                title="待处理告警"
                value={statisticData.pendingAlarmRecord}
                onClick={() => {
                  jumpToAlarm({ alarmStatusList: '01PENDING', maintSiteId });
                }}
              />
              <Card
                color="blue"
                imgSrc={processingImg}
                title="处理中告警"
                value={statisticData.processingAlarmRecord}
                onClick={() => {
                  jumpToAlarm({ alarmStatusList: '02PROCESSING', maintSiteId });
                }}
              />
              <Card
                color="red"
                imgSrc={newImg}
                title="今日新增告警"
                value={statisticData.newAlarmRecord}
                onClick={() => {
                  const today = new Date();
                  today.setHours(0, 0, 0);
                  const createStartTime = moment(today).format(getDateTimeFormat());
                  jumpToAlarm({ createStartTime, maintSiteId });
                }}
              />
            </div>
            <h3 className={styles['chart-title']}>今日告警数TOP5</h3>
            <Tabs>
              <Tabs.TabPane tab="按位置查看" key="locationTop5">
                {statisticData.locationRecordList?.length > 0 ? (
                  <Top5Chart
                    nameList={statisticData.locationNameList}
                    valueList={statisticData.locationRecordList}
                  />
                ) : (
                  <EmptyDataPic />
                )}
              </Tabs.TabPane>
              <Tabs.TabPane tab="按设备查看" key="assetTop5">
                {statisticData.assetRecordList?.length > 0 ? (
                  <Top5Chart
                    nameList={statisticData.assetNameList}
                    valueList={statisticData.assetRecordList}
                  />
                ) : (
                  <EmptyDataPic />
                )}
              </Tabs.TabPane>
            </Tabs>
          </Collapse.Panel>
        </Collapse>
      </Spin>
    </div>
  );
};

const Card = (props: CardProps) => {
  return (
    <div
      onClick={() => {
        if (props.onClick) {
          props.onClick();
        }
      }}
      className={classnames(styles[`card-container-${props.color}`], styles[`card-container`])}
    >
      <div className={styles['card-content']}>
        <div className={styles['card-title']}>
          <img src={props.imgSrc} alt="" />
          <span>{props.title}</span>
        </div>
        <p className={styles['card-value']}>{props.value}</p>
        <div
          className={classnames(
            styles.circle,
            styles[`circle-1`],
            styles[`circle-1-${props.color}`]
          )}
        />
        <div
          className={classnames(
            styles.circle,
            styles[`circle-2`],
            styles[`circle-2-${props.color}`]
          )}
        />
      </div>
    </div>
  );
};

interface Top5ChartProps {
  nameList: string[];
  valueList: number[];
}
const Top5Chart = (props: Top5ChartProps) => {
  const option = useMemo(() => {
    return {
      grid: {
        top: 25,
        left: 15,
        right: 15,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#F1F1F1',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#000000',
        },
        data: props.nameList,
      },
      yAxis: {
        type: 'value',
        minInterval: 1,
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#F1F1F1',
          },
        },
        axisLabel: {
          color: '#000000',
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      series: [
        {
          data: props.valueList,
          type: 'bar',
          barWidth: '35',
          itemStyle: {
            color: 'rgb(91,158,255)',
          },
        },
      ],
    };
  }, [props]);
  return (
    <ReactEchartsCore
      echarts={echarts}
      option={option}
      notMerge
      lazyUpdate
      style={{
        height: '100%',
      }}
    />
  );
};
export default withRouter(alarmStatisticsCard);
