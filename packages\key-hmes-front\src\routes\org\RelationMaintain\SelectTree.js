/**
 * SelectTree - 组织关系树
 * @date: 2021-1-13
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import React, { useState, useRef, useEffect } from 'react';
import { connect } from 'dva';
import { withRouter } from 'dva/router';
import { Tree, Spin } from 'choerodon-ui/pro';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { fetchPermission } from '@services/api';
import DragGrid from './DragGrid';
import TreeNodeIcon from './TreeNodeIcon';
import ChooseModal from './ChooseModal';
import styles from './index.module.less';
import AsideWapper from './AsideWapper';
import ModalLov from './ModalLov';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.model.org.relation';
const { TreeNode } = Tree;

// 第一级为tree node 节点, 第二级为拖拽节点能否释放
const dragDisabledMap = {
  ENTERPRISE: {
    ENTERPRISE: true,
    SITE: false,
    AREA: true,
    PROD_LINE: true,
    WORKCELL: true,
    LOCATOR: true,
  },
  SITE: {
    ENTERPRISE: true,
    SITE: true,
    AREA: false,
    PROD_LINE: true,
    WORKCELL: true,
    LOCATOR: false,
  },
  AREA: {
    ENTERPRISE: true,
    SITE: true,
    AREA: false,
    PROD_LINE: false,
    WORKCELL: true,
    LOCATOR: false,
  },
  PROD_LINE: {
    ENTERPRISE: true,
    SITE: true,
    AREA: true,
    PROD_LINE: true,
    WORKCELL: false,
    LOCATOR: false,
  },
  WORKCELL: {
    ENTERPRISE: true,
    SITE: true,
    AREA: true,
    PROD_LINE: true,
    WORKCELL: false,
    LOCATOR: false,
  },
  LOCATOR: {
    ENTERPRISE: true,
    SITE: true,
    AREA: true,
    PROD_LINE: true,
    WORKCELL: true,
    LOCATOR: false,
  },
};

function SelectTree(props) {
  const {
    expandedKeys = [],
    treeData = [],
    searchValue = '',
    autoExpandParent = false,
    halfContainerHeight = 0,
    dispatch,
    orgNodeCopy,
    addOriginDetailNode,
    addOriginDetailIcon,
    dragItem,
    dragGridAsideShow,
    focusType = undefined,
    loading,
    enableFlagVisible,
  } = props;

  const [pageColumns, setPageColumns] = useState(1);
  const [canEdit, setCanEdit] = useState(false);
  const [lovModalVisible, setLovModalVisible] = useState(false);
  const [lovCode, setLovCode] = useState('MT.MODEL.ASSIGNABLE.ORG');
  const [permissionTreeAction, setPermissionTreeAction] = useState(false);

  const [iconText] = useState({
    createRelation: intl.get(`${modelPrompt}.createRelation`).d('新增关系'),
    createSubordinateAndRelation: intl
      .get(`${modelPrompt}.createSubordinateAndRelation`)
      .d('新建下级及关系'),
    createRelationLocator: intl.get(`${modelPrompt}.createRelationLocator`).d('新增关联库位'),
    copy: intl.get(`tarzan.common.button.copy`).d('复制'),
    paste: intl.get(`${modelPrompt}.paste`).d('粘贴'),
    confirmDelete: intl.get(`tarzan.common.message.confirm.delete`).d('删除'),
    confirm: intl.get(`tarzan.common.button.confirm`).d('确定'),
    cancel: intl.get(`tarzan.common.button.cancel`).d('取消'),
    delete: intl.get(`tarzan.common.button.delete`).d('删除'),
  });

  const ref = useRef({});

  useEffect(() => {
    window.addEventListener('resize', onResize);
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        halfContainerHeight: ref.current.clientHeight - 2,
      },
    });
    return () => {
      window.removeEventListener('resize', onResize);
    };
  }, []);

  useEffect(() => {
    const {
      match: { path },
    } = props;
    fetchPermission(
      {
        permissionList: [
          {
            code: `${path}.tree.action`,
            type: 'table',
            meaning: '树节点操作',
          },
        ],
      },
      res => {
        const [_permissionTreeAction] = res;
        setPermissionTreeAction(_permissionTreeAction);
      },
    );
  }, []);

  const onResize = () => {
    const {
      history: {
        location: { pathname },
      },
      match: { path },
    } = props;
    if (pathname === path) {
      dispatch({
        type: 'relationMaintain/updateState',
        payload: {
          halfContainerHeight: ref.current.clientHeight - 2,
        },
      });
    }
  };

  // 展开收起节点
  const onExpand = keys => {
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        expandedKeys: keys,
        autoExpandParent: false,
      },
    });
  };

  // 拖拽事件
  const onDrop = info => {
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        loading: true,
      },
    });

    const dropNode = info.node; // 目标元素
    const dropKey = dropNode.organizationRelId; // 目标元素relId
    const { dropToGap } = info; // 拖拽在目标位置释放时的释放类型 放在元素边为 true / 放在元素内为false

    const { dragNode } = info; // 拖拽元素
    const dragKey = dragNode.organizationRelId; // 拖拽元素relId
    const dragNodeId = dragNode.organizationId; // 拖拽元素id

    const dropPos = dropNode.pos.split('-'); // 拖动元素起始位置信息
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]) === -1 ? 0 : 1; // 释放时元素的位置信息

    // 判断拖拽动作是否合法
    if (
      // 基本拖拽规则
      (!dropToGap &&
        dropCheck(
          dropNode.organizationType,
          dropKey,
          dropNode.detailType,
          dropNode.organizationId,
        )) ||
      // 附加拖拽规则 当为排序操作 目标元素  与拖拽元素 parentOrganizationId 不相同
      (dropToGap && dropNode.parentOrganizationId !== dragNode.parentOrganizationId) ||
      // 拖拽元素为禁用状态
      dragNode.enableFlag === 'N'
    ) {
      // 拖拽不合法结束执行
      resetDropState();
      onDragEnd();
      notification.error({ message: intl.get(`${modelPrompt}.actionError`).d('禁止的操作！') });
      return;
    }

    // 筛选拖拽目标 将目标relid 从data中删除
    const pickUpData = (targetData, targetOrganizationRelId) => {
      let pickItem;
      let pickData;
      const loopPickUp = (data, organizationRelId) => {
        data.forEach(dataItem => {
          if (dataItem.organizationRelId !== organizationRelId) {
            if (dataItem.organizationMessageList && dataItem.organizationMessageList.length > 0) {
              loopPickUp(dataItem.organizationMessageList, organizationRelId);
            }
          } else {
            pickItem = { ...dataItem };
            pickData = [...data];
          }
        });
      };
      loopPickUp(targetData, targetOrganizationRelId);
      return {
        pickItem,
        pickData,
      };
    };

    // 同级排序
    const getSortList = (targetData, targetOrganizationRelId, pickUpItem) => {
      const reorderList = [];
      let reorderListIndex = -1;

      targetData.forEach(dataItem => {
        if (pickUpItem.organizationRelId !== dataItem.organizationRelId) {
          reorderList.push(dataItem.organizationRelId);
        }
      });
      reorderList.forEach((listItem, _index) => {
        if (listItem === targetOrganizationRelId) {
          reorderListIndex = _index;
        }
      });

      reorderList.splice(reorderListIndex + dropPosition, 0, pickUpItem.organizationRelId);
      return reorderList;
    };

    if (dropToGap) {
      // 遍历原数组,保存拖动的元素, 并从元素组删除
      const pickUpDataResult = pickUpData(treeData, dragKey);
      // 排序操作
      const reorderList = getSortList(
        pickUpDataResult.pickData,
        dropKey,
        pickUpDataResult.pickItem,
      );
      dispatch({
        type: 'relationMaintain/reorderTreeNodes',
        payload: reorderList,
      }).then(res => {
        if (res && res.success) {
          getAllTreeData().then(() => {
            notification.success({
              message: intl.get(`${modelPrompt}.actionSuccess`).d('操作成功'),
            });
            resetDropState();
          });
        } else {
          resetDropState();
        }
      });
    } else {
      // 剪切操作
      dispatch({
        type: 'relationMaintain/cutTreeNodes',
        payload: {
          organizationId: dragNodeId,
          parentOrganizationId: dropNode.organizationId,
          parentPath: dropNode.path,
          path: dragNode.path,
        },
      }).then(res => {
        if (res && res.success) {
          expandedKeysAdd(dropKey);
          getAllTreeData().then(() => {
            notification.success({
              message: intl.get(`${modelPrompt}.actionSuccess`).d('操作成功'),
            });
            resetDropState();
          });
        } else {
          resetDropState();
        }
      });
    }
  };

  // 重置拖拽状态
  const resetDropState = () => {
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        loading: false,
        dragItem: {},
      },
    });
  };

  const onDragStart = e => {
    const {
      detailType,
      organizationType,
      organizationId,
      organizationRelId,
      parentOrganizationId,
    } = e.node;
    setTimeout(() => {
      // 拖拽时清空复制的节点(重置粘贴按钮状态)
      // 将拖拽目标缓存, 更新展开项状态
      setCanEdit(false);
      dispatch({
        type: 'relationMaintain/updateState',
        payload: {
          orgNodeCopy: {}, // 清空复制目标
          addOriginDetailIcon: {}, // 清空新增或详情目标
          addOriginDetailNode: {}, // 清空新增或详情目标
          focusType: undefined, // 清空新增或详情类型
          dragGridAsideShow: false, // 关闭分栏
          dragItem: {
            // 保存拖拽目标
            detailType,
            organizationType,
            organizationId,
            organizationRelId,
            parentOrganizationId,
          },
        },
      });
      expandedKeysDelete(organizationRelId);
    }, 0);
  };

  const onDragEnd = () => {
    // 拖拽结束清空保存的拖拽目标
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        dragItem: {},
      },
    });
  };

  // 拖拽时展开进入的项
  const onDragEnter = e => {
    expandedKeysAdd(e.node.key);
  };

  // 添加展开项key
  const expandedKeysAdd = key => {
    const newExpandedKeys = [];
    expandedKeys.push(`${key}`);
    expandedKeys.forEach(item => {
      if (newExpandedKeys.indexOf(item) === -1) {
        newExpandedKeys.push(item);
      }
    });
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        expandedKeys: newExpandedKeys,
      },
    });
  };

  // 删除展开项key
  const expandedKeysDelete = key => {
    const newExpandedKeys = [];
    expandedKeys.forEach(item => {
      if (newExpandedKeys.indexOf(item) === -1 && item !== `${key}`) {
        newExpandedKeys.push(item);
      }
    });
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        expandedKeys: newExpandedKeys,
      },
    });
  };

  // treeNode 点击事件
  const treeOnclick = (e, node) => {
    const { title } = node;
    if (title === 'empty-node') {
      return;
    }
    if (canEdit) {
      notification.error({ message: intl.get(`${modelPrompt}.confirmSave`).d('请先保存数据') });
      return;
    }
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        addOriginDetailNode: node,
        focusType: 'dist',
        dragGridAsideShow: true,
        dragGridAsideType: node.organizationType,
      },
    });
  };

  const iconClick = (e, detail) => {
    const exceptionType = 'AREA';
    const { organizationType } = detail;
    // 保存点击行数据
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        addOriginDetailIcon: detail,
      },
    });
    // 判断事件类型
    if (e === 'addSub') {
      // addSub若为区域需提示框确定下级类型
      if (canEdit) {
        notification.error({ message: intl.get(`${modelPrompt}.confirmSave`).d('请先保存数据') });
        return;
      }
      dispatch({
        type: 'relationMaintain/updateState',
        payload: {
          addOriginDetailNode: detail,
        },
      });
      if (organizationType === exceptionType) {
        dispatch({
          type: 'relationMaintain/updateState',
          payload: {
            chooseModalShow: true,
          },
        });
      } else {
        addTypeCheck(organizationType);
      }
      return;
    }
    // 普通新建 Lov
    if (e === 'addRelation') {
      setLovModalVisible(true);
      setLovCode('MT.MODEL.ASSIGNABLE.ORG');
      return;
    }
    // 库位新建 Lov
    if (e === 'addLocator') {
      setLovModalVisible(true);
      setLovCode('MT.MODEL.ASSIGNABLE.LOCATOR');
      return;
    }
    // 删除关系
    if (e === 'delete') {
      deleteRelation(detail);
      return;
    }
    // 复制关系
    if (e === 'copy') {
      notification.success({ message: intl.get(`${modelPrompt}.copySuccess`).d('复制成功') });
      dispatch({
        type: 'relationMaintain/updateState',
        payload: {
          orgNodeCopy: {
            organizationId: detail.organizationId,
            parentOrganizationId: detail.parentOrganizationId,
            parentPath: detail.parentPath,
            path: detail.path,
            organizationType: detail.organizationType,
            organizationRelId: detail.organizationRelId,
            detailType: detail.detailType,
          },
        },
      });
      return;
    }
    // 黏贴关系
    if (e === 'paste') {
      pasteRelation(detail);
    }
  };

  // lovModal 回调
  const onLovChange = val => {
    setLovModalVisible(false);
    if (val && val.length > 0) {
      addRelation(addOriginDetailIcon, val);
    }
  };

  // lovModal 关闭
  const onLovCancel = () => {
    setLovModalVisible(false);
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        addOriginDetailIcon: {},
      },
    });
  };

  // 粘贴树关系
  const pasteRelation = detail => {
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        loading: true,
      },
    });
    const { organizationId, path } = orgNodeCopy;
    dispatch({
      type: 'relationMaintain/copyTreeNodes',
      payload: {
        organizationId,
        parentOrganizationId: detail.organizationId,
        parentPath: detail.path,
        path,
      },
    }).then(res => {
      if (res && res.success) {
        getAllTreeData().then(() => {
          notification.success({ message: intl.get(`${modelPrompt}.actionSuccess`).d('操作成功') });
        });
      } else {
        dispatch({
          type: 'relationMaintain/updateState',
          payload: {
            loading: false,
          },
        });
      }
    });
  };

  const addTypeCheck = type => {
    const createTypeMap = {
      ENTERPRISE: 'SITE',
      SITE: 'AREA',
      AREA: 'PROD_LINE',
      PROD_LINE: 'WORKCELL',
      WORKCELL: 'WORKCELL',
    };
    // todo 拉起分栏组件
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        focusType: undefined,
        dragGridAsideShow: false,
        dragGridAsideType: undefined,
      },
    });
    setTimeout(() => {
      dispatch({
        type: 'relationMaintain/updateState',
        payload: {
          focusType: 'create',
          dragGridAsideShow: true,
          dragGridAsideType: createTypeMap[type],
        },
      });
    });
    setCanEdit(true);
  };

  // 新建关系
  const addRelation = (clickRow = {}, list = [], successNotice = true) => {
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        loading: true,
      },
    });
    const { organizationId, path, organizationRelId, organizationMessageList = [] } = clickRow;

    if (organizationId && path && list.length > 0) {
      dispatch({
        type: 'relationMaintain/addTreeNodes',
        payload: {
          parentOrganizationId: organizationId,
          parentPath: path,
          organizationIds: list,
          number: organizationMessageList ? organizationMessageList.length || 0 : 0,
        },
      }).then(res => {
        if (res && res.success) {
          expandedKeysAdd(organizationRelId);
          getAllTreeData().then(() => {
            if (successNotice) {
              notification.success({
                message: intl.get(`${modelPrompt}.actionSuccess`).d('操作成功'),
              });
            }
          });
        } else {
          dispatch({
            type: 'relationMaintain/updateState',
            payload: {
              loading: false,
            },
          });
        }
      });
    }
  };

  const getAllTreeData = () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'relationMaintain/allTreeData',
      }).then(res => {
        if (res) {
          dispatch({
            type: 'relationMaintain/updateState',
            payload: {
              loading: false,
              treeData: res,
              originData: res,
            },
          });
          resolve();
        } else {
          reject();
        }
      });
    });
  };

  // 删除关系
  const deleteRelation = clickRow => {
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        loading: true,
      },
    });
    const { organizationId, parentOrganizationId, path } = clickRow;
    if (organizationId && parentOrganizationId && path) {
      dispatch({
        type: 'relationMaintain/deleteTreeNodes',
        payload: {
          organizationId,
          parentOrganizationId,
          path,
        },
      }).then(res => {
        if (res && res.success) {
          if (addOriginDetailNode.path && addOriginDetailNode.path.indexOf(path) > -1) {
            dispatch({
              type: 'relationMaintain/updateState',
              payload: {
                addOriginDetailNode: {},
                addOriginDetailIcon: {},
                focusType: undefined,
                dragGridAsideShow: false,
                dragGridAsideType: undefined,
              },
            });
            setCanEdit(false);
          }
          getAllTreeData().then(() => {
            notification.success({
              message: intl.get(`${modelPrompt}.actionSuccess`).d('操作成功'),
            });
          });
        } else {
          dispatch({
            type: 'relationMaintain/updateState',
            payload: {
              loading: false,
            },
          });
        }
      });
    }
  };

  // 拖拽状态检查
  const dropCheck = (type, relId, dropType, id) => {
    const { organizationType, organizationRelId, detailType, parentOrganizationId } = dragItem;

    return (
      (organizationType &&
        dragDisabledMap[type] &&
        dragDisabledMap[type][organizationType] &&
        relId !== organizationRelId) ||
      (detailType && dropType !== detailType) ||
      (parentOrganizationId && parentOrganizationId === id)
    );
  };

  const asideSaveSuccess = id => {
    // todo 处理侧栏处理后的逻辑
    if (focusType === 'create') {
      addRelation(addOriginDetailNode, [id], false);
    } else {
      getAllTreeData();
    }
  };

  const nodeClass = item => {
    const classNames = ['tree-node'];
    if (
      item.enableFlag === 'N' ||
      dropCheck(item.organizationType, item.organizationRelId, item.detailType, item.organizationId)
    ) {
      classNames.push('disabled-style');
    }
    if (item.organizationRelId === addOriginDetailNode.organizationRelId) {
      classNames.push('focus-style');
    }
    return classNames.join(' ');
  };

  // treeNode
  const renderTreeItems = data => {
    if (Array.isArray(data)) {
      return data.map(item => {
        const key = item.organizationRelId;
        let title = [];
        const description = (item.description || '').split('');
        const searchValueLength = searchValue.length;
        if (searchValue && item.description.toUpperCase().indexOf(searchValue.toUpperCase()) > -1) {
          const stringArr = item.description.toUpperCase().split(searchValue.toUpperCase());
          title = stringArr.map((stringArrItem, index) => (
            <span key={`${stringArr.length - index}light${key}`}>
              {(description.splice(0, stringArrItem.length) || []).join('')}
              {index < stringArr.length - 1 && (
                <span style={{ color: '#f02b2b', fontSize: '14px' }}>
                  {(description.splice(0, searchValueLength) || []).join('')}
                </span>
              )}
            </span>
          ));
        } else {
          title.push(<span key={key}>{item.description}</span>);
        }
        if (permissionTreeAction) {
          title.push(
            <TreeNodeIcon
              key={`${item.organizationRelId}iconRow`}
              item={item}
              callback={iconClick}
              canEdit={canEdit}
              iconText={iconText}
            />,
          );
        }
        if (enableFlagVisible === 'N' && item.enableFlag === 'N') {
          return null;
        }
        return (
          <TreeNode
            title={title}
            key={key}
            className={nodeClass(item)}
            organizationRelId={key}
            organizationId={item.organizationId}
            organizationType={item.organizationType}
            detailType={item.detailType}
            parentOrganizationId={item.parentOrganizationId}
            parentPath={item.parentPath}
            path={item.path}
            enableFlag={item.enableFlag}
          >
            {item.organizationMessageList &&
              item.organizationMessageList.length > 0 &&
              renderTreeItems(item.organizationMessageList)}
          </TreeNode>
        );
      });
    }
  };

  return (
    <div className={styles['tree-container']} ref={ref}>
      <DragGrid
        article={
          <Spin spinning={loading}>
            {halfContainerHeight && halfContainerHeight > 0 && (
              <Tree
                className="draggable-tree"
                draggable={permissionTreeAction}
                showLine
                height={halfContainerHeight}
                expandedKeys={expandedKeys}
                autoExpandParent={autoExpandParent}
                onClick={treeOnclick}
                selectable={false}
                onExpand={onExpand}
                onDrop={onDrop}
                onDragStart={onDragStart}
                onDragEnd={onDragEnd}
                onDragEnter={onDragEnter}
              >
                {renderTreeItems(treeData)}
                <TreeNode disabled className="empty-node" key="empty-node-1" title="empty-node" />
                <TreeNode disabled className="empty-node" key="empty-node-2" title="empty-node" />
                <TreeNode disabled className="empty-node" key="empty-node-3" title="empty-node" />
              </Tree>
            )}
          </Spin>
        }
        aside={
          <AsideWapper
            handleSuccess={asideSaveSuccess}
            pageColumns={pageColumns}
            canEdit={canEdit}
            setCanEdit={setCanEdit}
          />
        }
        asideShow={dragGridAsideShow}
        changePageColumns={setPageColumns}
      />
      <ChooseModal callback={addTypeCheck} />
      <ModalLov
        visible={lovModalVisible}
        className={styles['tree-multiple-lov']}
        queryParams={{
          tenantId,
          parentOrganizationId: addOriginDetailIcon.organizationId,
          parentPath: addOriginDetailIcon.path,
          parentOrganizationType: addOriginDetailIcon.organizationType,
        }}
        width={600}
        code={lovCode}
        onChange={onLovChange}
        onCancel={onLovCancel}
      />
    </div>
  );
}

export default withRouter(
  formatterCollections({
    code: ['tarzan.model.org.relation', 'tarzan.common'],
  })(
    connect(({ relationMaintain }) => {
      return relationMaintain;
    })(SelectTree),
  ),
);
