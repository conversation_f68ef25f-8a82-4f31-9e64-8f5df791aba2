/*
 * @Description: 索赔管理-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-21 16:15:15
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-25 16:18:18
 */
import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.marketActivity.claimDocManagement';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'claimDocId',
  queryFields: [
    {
      name: 'claimDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.claimDocNum`).d('判断书编号'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'hostPlant',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.hostPlant`).d('主机厂'),
    },
    {
      name: 'title',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.title`).d('标题'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('是否生效'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'validFromFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.validFromFrom`).d('启动时间从'),
      max: 'validFromTo',
    },
    {
      name: 'validFromTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.validFromTo`).d('启动时间至'),
      min: 'validFromFrom',
    },
    {
      name: 'validToFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.validToFrom`).d('停用时间从'),
      max: 'validToTo',
    },
    {
      name: 'validToTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.validToTo`).d('停用时间至'),
      min: 'validToFrom',
    },
  ],
  fields: [
    {
      name: 'claimDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.claimDocNum`).d('判断书编号'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'hostPlant',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.hostPlant`).d('主机厂'),
    },
    {
      name: 'title',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.title`).d('标题'),
    },
    {
      name: 'aResponsibilityRatio',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.aResponsibilityRatio`).d('甲方责任比例(%)'),
    },
    {
      name: 'bResponsibilityRatio',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.bResponsibilityRatio`).d('乙方责任比例(%)'),
    },
    {
      name: 'dutyEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.dutyEnclosure`).d('责任区分技术判决书'),
      bucketName: 'qms',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('是否生效'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'validFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.validFrom`).d('启用时间'),
    },
    {
      name: 'validTo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.validTo`).d('停用时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'remarks',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remarks`).d('备注'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-claim-doc/page/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
