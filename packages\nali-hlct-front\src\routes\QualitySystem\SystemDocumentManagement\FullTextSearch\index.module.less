.full-text-search-page {
  .full-text-search-table {
    :global(>:not(:first-child)) {
      display: none;
    }
  }
}

.full-text-search-container {
  border: solid 1px rgb(225, 225, 225);
  font-size: 16px;
  color: #403a3a;
  background-color: rgb(242, 242, 242);
  padding: 5px 10px;

  .search-total-count {
    line-height: 32px;
    .total-count-num {
      color: rgb(245, 43, 43);
    }
  }
  
  .search-result-container {
    border: solid 1px rgb(181, 181, 181);
    background-color: #fff;
    display: flex;
    padding: 10px;

    .full-text-search-container-left {
      width: 85%;
      border-right: solid 1px rgb(188, 188, 188);

      .search-result {
        .search-result-item {
          padding: 5px 10px;

          .file-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 5px 0;

            .file-name {
              font-size: 16px;
              text-decoration: underline;
              margin-right: 15px;
              cursor: pointer;

              em {
                color: red;
                font-style: normal;
              }
            }
            .read-source-doc {
              color: rgb(105, 190, 193);
              flex-grow: 1;
              font-size: 13px;
              cursor: pointer;
            }
            .file-status {
              padding: 5px;
            }
          }

          .file-content {
            font-size: 14px;
            text-indent: 28px;
            padding: 5px 0;
            cursor: pointer;

            em {
              color: red;
              font-style: normal;
            }
          }

          .file-footer {
            display: flex;
            flex-wrap: wrap;
            font-size: 12px;
            padding: 5px 0;

            .file-footer-item {
              width: 20%;
              .file-footer-item-label {
                display: inline-block;
                width: 25%;
              }
            }
          }
        }

        .search-result-item:not(:last-child) {
          border-bottom: solid 1px rgb(188, 188, 188);
        }
      }
    }
    .full-text-search-container-right {
      width: 15%;
      
      .publish-time-title {
        font-size: 13px;
        color: #595959;
        font-weight: bold;
      }
    }
  }
}
