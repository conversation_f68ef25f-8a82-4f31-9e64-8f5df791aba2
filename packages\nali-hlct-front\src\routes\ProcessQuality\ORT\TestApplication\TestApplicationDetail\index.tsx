/**
 * @Description: ORT测试申请-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-09-18 10:38:58
 * @LastEditTime: 2023-09-18 10:38:58
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import intl from 'utils/intl';

import { Header, Content } from 'components/Page';
import {
  DataSet,
  Button,
  Form,
  TextField,
  Select,
  Switch,
  Lov,
  Table,
  DatePicker,
  NumberField,
} from 'choerodon-ui/pro';
import { Collapse, Badge, Popconfirm } from 'choerodon-ui';
import { observer } from 'mobx-react';
import uuid from 'uuid/v4';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useDataSetEvent } from 'utils/hooks';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { DragColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import {
  fetchApplyDetailConfig,
  saveApplyConfig,
  submitApplyConfig,
  QueryMaterialInfo,
  GetDefaultSite,
} from '../services';

import { headerDS, childrenDS } from '../stores/TestApplicationDS';

const modelPrompt = 'tarzan.qms.ort.testApplication';
const { Panel } = Collapse;

const TestApplicationDetail = props => {
  const {
    match: {
      params: { id },
      path,
    },
  } = props;

  // pub路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);

  // 是否显示外场物料编码
  const [internalProductFlag, setInternalProductFlag] = useState('N');
  // 单据状态
  const [inspectDocStatus, setInspectDocStatus] = useState('');
  // 申请类型
  const [inspectType, setInspectType] = useState();

  // 编辑状态
  const [canEdit, setCanEdit] = useState(false);

  const fetchApplyDetail = useRequest(fetchApplyDetailConfig(), {
    // 详情页数据查询
    manual: true,
    needPromise: true,
  });

  const saveApply = useRequest(saveApplyConfig(), {
    // 详情页数据保存
    manual: true,
    needPromise: true,
  });

  // 提交
  const submitApply = useRequest(submitApplyConfig(), {
    manual: true,
    needPromise: true,
  });

  // 查询物料批信息
  const { run: queryMaterialInfo } = useRequest(QueryMaterialInfo(), {
    manual: true,
  });
  // 查询物料批信息
  const { run: getDefaultSite } = useRequest(GetDefaultSite(), {
    manual: true,
    needPromise: true,
  });

  // dataKey
  // transport

  const childrenDs = useMemo(() => {
    const dsConfig = childrenDS('detail');
    delete dsConfig.dataKey;
    delete dsConfig.transport;
    return new DataSet(dsConfig);
  }, []);

  const headerDs = useMemo(
    () =>
      new DataSet({
        ...headerDS('detail'),
        children: {
          tableData: childrenDs,
        },
      }),
    [],
  );
  // 初始化页面
  useEffect(() => {
    if (id === 'create') {
      createInit();
    } else {
      // 查询详情
      initPageData();
    }
  }, [id]);

  const createInit = async () => {
    const res = await getDefaultSite({});
    const headerData: any = {
      inspectDocStatus: 'NEW',
      internalProductFlag: 'N',
    };
    if (res?.success && res?.rows) {
      headerData.siteLov = {
        ...res.rows,
      };
    }
    setCanEdit(true);
    // 新建是表单和列表添加默认数据
    headerDs.loadData([headerData]);
    childrenDs.loadData([]);
    setInternalProductFlag('N');
    setInspectDocStatus('NEW');
  };

  useDataSetEvent(headerDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteLov':
      case 'materialLov':
        record.set('inspectSchemeLov', {})
        childrenDs.loadData([])
        break;
      case 'inspectSchemeLov':
        childrenDs.loadData([])
        break;
      default:
        break;
    }
  })

  const tableColumn: ColumnProps[] = [
    {
      name: 'sequence',
      renderer: ({ record }: any) => {
        return record.index * 10 + 10;
      },
    },
    {
      name: 'inspectItem',
      editor: canEdit,
    },
    {
      name: 'inspectMethod',
      editor: canEdit,
    },
    {
      name: 'standardRequirement',
      editor: canEdit,
    },
    {
      name: 'inspectFrequency',
      editor: canEdit,
    },
    {
      name: 'outsourceFlag',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
      editor: canEdit && <Switch />,
    },
    {
      name: 'inspectQty',
      editor: canEdit,
    },
    {
      name: 'exSampleSolveMethod',
      editor: canEdit,
    },
    {
      name: 'enclosure',
      editor: canEdit,
    },
  ];

  // 初始化页面
  const initPageData = async () => {
    // 页面编辑状态和列表选中状态初始化
    setCanEdit(false);
    const res = await fetchApplyDetail.run({
      params: {
        ortInspectHeadId: id,
      },
    });

    // 查询到值给页面数据初始化
    if (res.success) {
      // 取出列表值和 表单值
      const { qisOrtInspectHeadVO, qisOrtInspectLineVO } = res.rows || {};

      // 表单值加载
      headerDs.loadData([qisOrtInspectHeadVO]);
      setInternalProductFlag(qisOrtInspectHeadVO.internalProductFlag);
      setInspectDocStatus(qisOrtInspectHeadVO.inspectDocStatus);
      setInspectType(qisOrtInspectHeadVO.inspectType);
      // 列表值加载并加工
      childrenDs.loadData(
        qisOrtInspectLineVO.map(item => {
          return {
            ...item,
            _uuid: uuid(),
          };
        }),
      );
      handleChangeMaterial({
        materialId: qisOrtInspectHeadVO.materialId,
      });
    }
  };

  const handleChangeMaterial = value => {
    if (value?.materialId) {
      queryMaterialInfo({
        params: value?.materialId,
        onSuccess: res => {
          headerDs.current?.init('productType', res);
        },
      });
    } else {
      headerDs.current?.init('productType', undefined);
    }
  };

  // 校验所有ds
  const validateAllDs = async () => {
    const itemValidate = await headerDs.validate();
    const tableValidate = await childrenDs.validate();
    if (!(childrenDs.length > 0)) {
      notification.error({ message: intl.get(`${modelPrompt}.lineErr`).d('请添加测试项目！') });
    }
    // 返回校验结果
    return itemValidate && tableValidate && childrenDs.length > 0;
  };

  // 组合数据
  const getAllData = () => {
    const headerDsData: any = headerDs.toData()[0] || {};
    const childrenDsData: any = (childrenDs.toData() || []).map((item, index) => {
      return {
        ...item,
        sequence: index * 10 + 10,
      };
    });

    if (headerDsData.headerDsData) {
      delete headerDsData.headerDsData;
    }
    return {
      qisOrtInspectHeadVO: headerDsData,
      qisOrtInspectLineVO: childrenDsData,
    };
  };

  // 保存
  const handleSave = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      setCanEdit(true);
      return;
    }

    const params = getAllData();

    const res = await saveApply.run({
      params,
    });

    if (res?.success) {
      notification.success({});
      if (id === 'create') {
        props.history.push(`/hwms/ort/test-application/detail/${res.rows}`);
      } else {
        initPageData();
      }
    }

    return res;
  };

  const isLoading = fetchApplyDetail.loading || saveApply.loading || submitApply.loading;

  // 取消
  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hwms/ort/test-application/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  // 头提交
  const handleSubmit = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      setCanEdit(true);
      return;
    }

    const params = getAllData();

    return submitApply.run({
      params,
      onSuccess: res => {
        notification.success({});
        if (id === 'create') {
          props.history.push(`/hwms/ort/test-application/detail/${res}`);
        } else {
          initPageData();
        }
      },
    });
  };

  const DeleteBtn = observer(props => {
    const { dataSet } = props;
    return (
      <Popconfirm
        title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
        onConfirm={deleteLine}
      >
        <Button
          icon="delete"
          funcType={FuncType.link}
          disabled={!canEdit || !(dataSet.selected?.length > 0)}
        >
          {intl.get(`${modelPrompt}.delete`).d('删除')}
        </Button>
      </Popconfirm>
    );
  });

  // 单独新增行
  const addLine = () => {
    childrenDs.create({
      outsourceFlag: 'N',
      _uuid: uuid(),
    });
  };

  // 批量新增
  const batchAddLine = value => {
    if (value?.length > 0) {
      value?.forEach(item => {
        childrenDs.create({
          ...item,
          _uuid: uuid(),
        });
      });
    }
    headerDs.current?.set('batchAddLineLov', null);
  };

  const deleteLine = () => {
    childrenDs.delete(childrenDs.selected, false);
  };

  const handleInspectTypeChange = value => {
    setInspectType(value);
    if (value !== 'ENTRUST') {
      const deleteList: any = [];
      childrenDs?.forEach(record => {
        if (!record.get('inspectItemId')) {
          deleteList.push(record);
        }
      });
      childrenDs.remove(deleteList);
    }
  };

  return (
    <div className="hmes-style">
      {pubFlag ? (
        <Header title={intl.get(`${modelPrompt}.InspectionSchemeMaintenance`).d('ORT测试申请')}>
          <ApprovalInfoDrawer
            objectTypeList={['QIS_ORT_INSPECT_APPLY']}
            objectId={id}
          />
        </Header>
      ) : (
        <Header
          title={intl.get(`${modelPrompt}.InspectionSchemeMaintenance`).d('ORT测试申请')}
          backPath={pubFlag ? '' : '/hwms/ort/test-application/list'}
        >
          {canEdit && (
            <>
              <Button
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
                loading={isLoading}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <PermissionButton
                permissionList={[
                  {
                    code: `ort.testApplication.list.submit`,
                    type: 'button',
                    meaning: '提交',
                  },
                ]}
                disabled={!['NEW', 'REJECTED'].includes(inspectDocStatus)}
                loading={isLoading}
                type="c7n-pro"
                onClick={handleSubmit}
              >
                {intl.get(`${modelPrompt}.button.release`).d('提交')}
              </PermissionButton>
              <Button loading={isLoading} icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && (
            <>
              <PermissionButton
                permissionList={[
                  {
                    code: `ort.testApplication.list.edit`,
                    type: 'button',
                    meaning: '编辑',
                  },
                ]}
                loading={isLoading}
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="edit-o"
                disabled={!['NEW', 'REJECTED'].includes(inspectDocStatus)}
                onClick={() => {
                  setCanEdit(prev => !prev);
                }}
              >
                {intl.get(`${modelPrompt}.button.release`).d('编辑')}
              </PermissionButton>

              <PermissionButton
                permissionList={[
                  {
                    code: `ort.testApplication.list.submit`,
                    type: 'button',
                    meaning: '提交',
                  },
                ]}
                disabled={!['NEW', 'REJECTED'].includes(inspectDocStatus)}
                loading={isLoading}
                type="c7n-pro"
                onClick={handleSubmit}
              >
                {intl.get(`${modelPrompt}.button.release`).d('提交')}
              </PermissionButton>
            </>
          )}
          <ApprovalInfoDrawer
            objectTypeList={['QIS_ORT_INSPECT_APPLY']}
            objectId={id}
          />
        </Header>
      )}
      <Content>
        <Collapse collapsible="icon" bordered={false} defaultActiveKey={['panel1', 'panel2']}>
          <Panel header={intl.get(`${modelPrompt}.headerTitle`).d('头数据')} key="panel1">
            <Form dataSet={headerDs} columns={4} labelWidth={112} disabled={!canEdit}>
              <TextField name="inspectDocNum" />
              <Select name="inspectDocStatus" />
              <Lov name="siteLov" />
              <Select name="inspectType" onChange={handleInspectTypeChange} />

              <Switch
                name="internalProductFlag"
                onChange={val => {
                  headerDs.current?.set('withoutMaterialCode', null);
                  setInternalProductFlag(val);
                }}
              />
              <Select name="sampleType" />
              <Lov name="materialLov" onChange={handleChangeMaterial} />
              <TextField name="materialName" />

              <TextField name="productType" />
              <DatePicker name="expectCompleteTime" />
              <Select name="projectStage" />
              <Select name="urgencyDegree" />

              <Lov name="prodLineLov" />
              <TextField name="ratedCapacity" />
              <TextField name="samplingMonth" />
              <NumberField name="sampleQty" />
              <Select name="inspectResultDemand" />

              <TextField name="inspectPurpose" />
              {internalProductFlag === 'N' && <TextField name="withoutMaterialCode" />}
              <TextField name="remark" />
              <Lov name="inspectSchemeLov" />
            </Form>
          </Panel>

          <Panel header={intl.get(`${modelPrompt}.childrenTitle`).d('测试项目')} key="panel2">
            <Table
              customizedCode="wtjs2"
              dataSet={childrenDs}
              columns={tableColumn}
              rowDraggable={canEdit}
              dragColumnAlign={DragColumnAlign.left}
              buttons={[
                <DeleteBtn dataSet={childrenDs} />,
                <Lov
                  icon="search"
                  placeholder={intl.get(`${modelPrompt}.chooseItem`).d('选择项目')}
                  mode={ViewMode.button}
                  dataSet={headerDs}
                  name="batchAddLineLov"
                  clearButton={false}
                  onChange={value => {
                    batchAddLine(value);
                  }}
                  disabled={!canEdit}
                />,
                <Button
                  icon="add"
                  onClick={addLine}
                  disabled={!canEdit || inspectType !== 'ENTRUST'}
                >
                  {intl.get(`${modelPrompt}.button.addNewProgram`).d('新增项目')}
                </Button>,
              ]}
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.qms.ort.testApplication', 'tarzan.common'],
})(TestApplicationDetail);
