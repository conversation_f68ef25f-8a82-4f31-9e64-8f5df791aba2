/**
 * @Description: 生产指令管理列表页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2023-02-24 16:52:24
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.equipment.workCell';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/equipments/property/list/ui`,
        method: 'get',
        data: {
          ...data,
          rows: data.rows?.map(item => {
            return {
              ...item,
              uuid: item.id,
            };
          }),
        },
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'uuid',
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'equipment',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'equipmentId',
      bind: 'equipment.equipmentId',
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      bind: 'equipment.equipmentCode',
    },
    {
      name: 'workcell',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元编码'),
      lovCode: 'MT.MODEL.WORKCELL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workcellId',
      type: FieldType.string,
      bind: 'workcell.workcellId',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      name: 'equipment',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'equipmentId',
      type: FieldType.string,
      bind: 'equipment.equipmentId',
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      bind: 'equipment.equipmentCode',
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      bind: 'equipment.equipmentName',
    },
    {
      name: 'workcell',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元编码'),
      lovCode: 'MT.MODEL.WORKCELL',
      noCache: true,
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workcellId',
      type: FieldType.string,
      bind: 'workcell.workcellId',
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      bind: 'workcell.workcellCode',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元名称'),
      bind: 'workcell.workcellName',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

export { tableDS };
