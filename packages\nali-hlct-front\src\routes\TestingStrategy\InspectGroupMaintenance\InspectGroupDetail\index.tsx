/**
 * @Description: 检验项目组维护明细界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:55:10
 * @LastEditTime: 2023-05-18 16:50:37
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import intl from 'utils/intl';
import { isJSONString } from '@/utils';
import { Header, Content } from 'components/Page';
import {
  DataSet,
  Button,
  Form,
  TextField,
  Select,
  Switch,
  Spin,
  IntlField,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { useRequest } from '@components/tarzan-hooks';
import { AttributeDrawer } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import { FetchInspectGroupDetail, SaveInspectGroupDetail } from '../services';
import { DetailFormDS, DetailTableDS } from '../stories';
import InspectItemTab from './InspectItemTab';

const modelPrompt = 'tarzan.qms.inspectGroupMaintenance';
const { Panel } = Collapse;

const InspectGroupDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    customizeForm,
    customizeTable,
    custConfig,
  } = props;

  const [canEdit, setCanEdit] = useState(false);
  const formDS = useMemo(() => new DataSet(DetailFormDS()), []);
  const tableDS = useMemo(() => new DataSet(DetailTableDS()), []);

  const queryDetail = useRequest(FetchInspectGroupDetail(), {
    // 详情页数据查询
    manual: true,
  });

  const saveDetail = useRequest(SaveInspectGroupDetail(), {
    // 详情页数据保存
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      return;
    }
    initPageData();
  }, [id]);

  // 获取查询数据
  const initPageData = () => {
    queryDetail.run({
      params: {
        customizeUnitCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.BASIC`,
        inspectGroupId: id,
      },
      onSuccess: res => {
        const { inspectGroupItemDTOList } = res;
        formDS.loadData([res]);
        const lineList = (inspectGroupItemDTOList || []).map(item => {
          if (item.inspectGroupItemId) {
            item.uuid = item.inspectGroupItemId;
          }
          if (['TEXT', 'DECISION_VALUE'].includes(item.dataType)) {
            return {
              ...item,
              trueValue:
                (item.trueValueList || []).length > 0 ? item.trueValueList[0].dataValue : null,
              falseValue:
                (item.falseValueList || []).length > 0 ? item.falseValueList[0].dataValue : null,
            };
          }
          if (item.dataType === 'VALUE_LIST') {
            return {
              ...item,
              trueValue:
                (item.trueValueList || []).length > 0
                  ? item.trueValueList.map(trueItem => trueItem.dataValue)
                  : null,
              falseValue:
                (item.falseValueList || []).length > 0
                  ? item.falseValueList.map(falseItem => falseItem.dataValue)
                  : null,
            };
          }
          if (['CALCULATE_FORMULA'].includes(item.dataType)) {
            const formula = isJSONString(item.formula || '');
            if (formula) {
              const { formulaId, formulaCode, formulaName, dimension, formulaList } = formula;
              return {
                ...item,
                formulaId,
                formulaCode,
                formulaName,
                dimension,
                formulaList,
              };
            }
            return {
              ...item,
              formulaId: null,
              formulaCode: null,
              formulaName: null,
              dimension: null,
              formulaList: null,
            };
          }
          return item;
        });
        tableDS.loadData(lineList);
      },
    });
  };

  // 保存
  const handleSave = async (flag = true) => {
    // flag为true保存后保留界面，为false保存后提示是否新建下一条
    const formValidate = await formDS.validate();
    const tableValidate = await tableDS.validate();
    if (!formValidate || !tableValidate) {
      return Promise.resolve(false);
    }
    let tableRecordValidateNeed = true;
    let tableRecordValidateMatch = true;
    let erroeRecordCode;
    const itemUuids = [];
    tableDS.toData().forEach((tableRecord: any) => {
      // @ts-ignore
      itemUuids.push(tableRecord.inspectItemId);
    });
    tableDS.toData().forEach((tableRecord: any) => {
      const formulaList = tableRecord.formulaList || [];
      if (formulaList?.length > 0) {
        formulaList.forEach(formulaListItem => {
          if (formulaListItem.isRequired === 'Y' && !formulaListItem.inspectItemId) {
            tableRecordValidateNeed = false;
            erroeRecordCode = tableRecord.inspectItemCode;
          }
          // @ts-ignore
          if (formulaListItem.inspectItemId && !itemUuids.includes(formulaListItem.inspectItemId)) {
            tableRecordValidateMatch = false;
            erroeRecordCode = tableRecord.inspectItemCode;
          }
        });
      }
    });

    if (!tableRecordValidateNeed) {
      notification.error({
        message: `${intl
          .get(`${modelPrompt}.message.needBindInspectItem`)
          .d('数据类型为计算公式时需要关联检验项目')}-${erroeRecordCode}`,
      });
      return Promise.resolve(false);
    }
    if (!tableRecordValidateMatch) {
      notification.error({
        message: `${intl
          .get(`${modelPrompt}.message.matchBindInspectItem`)
          .d('关联的检验项目未找到')}-${erroeRecordCode}`,
      });
      return Promise.resolve(false);
    }

    const formData = formDS.toData()[0];
    const inspectGroupItemDTOList = [] as object[];
    const deleteGroupItemIds = [] as number[];
    tableDS.toJSONData().forEach((item: any) => {
      item.trueValueList = item.trueValueList || [];
      item.falseValueList = item.falseValueList || [];
      item.warningValueList = item.warningValueList || [];
      if (item._status === 'create') {
        inspectGroupItemDTOList.push({
          ...item,
          inspectGroupItemId: null,
        });
      } else if (item._status === 'delete') {
        deleteGroupItemIds.push(item.inspectGroupItemId);
      } else {
        inspectGroupItemDTOList.push(item);
      }
    });

    const inspectGroupItemIds = [];
    tableDS.toData().forEach((item: any) => {
      if (item?.inspectGroupItemId && item?.inspectGroupItemId > 0)
        // @ts-ignore
        inspectGroupItemIds.push(item.inspectGroupItemId);
    });

    const params = {
      ...formData,
      deleteGroupItemIds,
      inspectGroupItemDTOList,
      inspectGroupItemIds,
    };
    return saveDetail.run({
      params,
      onSuccess: res => {
        if (flag) {
          // @ts-ignore
          notification.success();
          setCanEdit(prev => !prev);
          if (id === 'create') {
            props.history.push(`/hwms/inspect-group-maintenance/dist/${res}`);
          } else {
            initPageData();
          }
        } else {
          if (id !== 'create') {
            props.history.push(`/hwms/inspect-group-maintenance/dist/create`);
          }
          formDS.loadData([
            {
              enableFlag: 'Y',
            },
          ]);
          tableDS.loadData([]);
        }
      },
    });
  };

  // 取消
  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hwms/inspect-group-maintenance/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  // 检验项目组件传参
  const inspectItemTabProps = {
    path,
    id,
    canEdit,
    customizeTable,
    customizeForm,
    custConfig,
    tableDS,
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title`).d('检验项目组维护')}
        backPath="/hwms/inspect-group-maintenance/list"
      >
        {canEdit && (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave(true)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button onClick={() => handleSave(false)}>
              {intl.get(`${modelPrompt}.saveAndNext`).d('保存并新建下一条')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <AttributeDrawer
          serverCode={BASIC.TARZAN_SAMPLING}
          className="org.tarzan.qms.domain.entity.MtInspectGroup"
          kid={id}
          canEdit={canEdit}
          disabled={id === 'create'}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.ATTR`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <Spin spinning={queryDetail.loading || saveDetail.loading}>
          <Collapse bordered={false} defaultActiveKey={['BASIC', 'ITEM']}>
            <Panel
              key="BASIC"
              header={intl.get(`${modelPrompt}.title.basic`).d('基础属性')}
              dataSet={formDS}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.BASIC`,
                },
                <Form dataSet={formDS} columns={3} labelWidth={112} disabled={!canEdit}>
                  <TextField name="inspectGroupCode" />
                  <IntlField
                    name="inspectGroupDesc"
                    modalProps={{
                      title: intl.get(`${modelPrompt}.model.inspectGroupDesc`).d('检验项目组描述'),
                    }}
                  />
                  <Select name="inspectGroupType" />
                  <TextField name="remark" />
                  <Switch name="enableFlag" />
                </Form>,
              )}
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.item`).d('检验项目信息')}
              key="ITEM"
              dataSet={tableDS}
            >
              <InspectItemTab {...inspectItemTabProps} />
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.qms.inspectGroupMaintenance', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.BASIC`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.RELATION`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.ITEM`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.ATTR1`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.EDIT`,
    ],
    // @ts-ignore
  })(InspectGroupDetail),
);
