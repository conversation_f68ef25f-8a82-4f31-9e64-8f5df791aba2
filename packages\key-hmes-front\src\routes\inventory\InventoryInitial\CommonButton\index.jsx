/**
 * @feature 库存初始化-按钮相关
 * @date 2021-9-16
 * <AUTHOR>
 */
import React, { useState, useEffect } from 'react';
import { withRouter } from 'dva/router';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Upload } from 'choerodon-ui';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import notification from 'utils/notification';
import myInstance from '@utils/myAxios';
import { BASIC, API_HOST } from '@/utils/config';
import { PrintButton } from '@components/tarzan-ui';

import DownloadButton from './DownloadButton';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inventory.initial.model';

const CommonButton = props => {
  const [confirmStatus, changeConfirm] = useState(true);
  const [validateStatus, changeValidate] = useState(true);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [validateLoading, setValidateLoading] = useState(false);

  const {
    match: { path },
  } = props;

  useEffect(() => {
    props.dataSet.query().then(res => {
      if (res.success) {
        changeValidate((res.rows || {}).totalElements === 0);
        changeConfirm((res.rows || {}).allFlag !== 'Y' || (res.rows || {}).totalElements === 0);
      }
    });
  }, []);

  useDataSetEvent(props.dataSet, 'load', () => {
    changeValidate(props.dataSet.totalCount === 0);
  });

  const uploadFile = info => {
    setImportLoading(false);
    if (info.success) {
      props.dataSet.query().then(res => {
        if (res.success) {
          changeValidate((res.rows || {}).totalElements === 0);
        }
      });
    } else if (info.message) {
      notification.error({
        message: info.message,
        description: '',
      });
    }
  };

  const beforeUpload = file => {
    if (!['.csv', '.xlsx', '.xls'].some(child => file.name.indexOf(child) >= 0)) {
      notification.warning({
        description: '',
        message: intl.get(`${modelPrompt}.notification.import.validate`).d('上传文本格式不正确'),
      });
      return false;
    }
    changeConfirm(true);
    setImportLoading(true);
    return true;
  };

  // 数据验证
  const validateAction = () => {
    setValidateLoading(true);
    const validateUrl = `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-material-lot/validate/excel/import/ui`;
    myInstance.post(validateUrl).then(res => {
      const { success } = res.data;
      if (success) {
        notification.success();
        changeConfirm(false);
        props.dataSet.query().then(ress => {
          if (ress.success && ress.rows) {
            changeConfirm((ress.rows || {}).allFlag !== 'Y' || (res.rows || {}).totalElements === 0);
          }
        });
      } else if (res.data.message) {
        notification.error({
          message: res.data.message,
          description: '',
        });
      }
      setValidateLoading(false);
    });
  };

  // 数据上传
  const confirmDatas = () => {
    setConfirmLoading(true);
    const importUrl = `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-material-lot/save/excel/import/ui`;
    myInstance.post(importUrl, {}).then(res => {
      const { success } = res.data;
      if (success) {
        notification.success({
          message: intl.get(`${modelPrompt}.notification.import.success`).d('导入成功'),
          description: '',
        });
        props.dataSet.query();
        changeConfirm(true);
        changeValidate(true);
        setConfirmLoading(false);
      } else if (res.data.message) {
        notification.error({
          message: res.data.message,
          description: '',
        });
        setConfirmLoading(false);
      }
    });
  };

  // 删除
  const onDelete = async () => {
    const ress = await props.dataSet.delete(props.dataSet.selected);
    if (ress.success) {
      props.dataSet.query().then(res => {
        if (res.success) {
          changeValidate((res.rows || {}).totalElements === 0);
          changeConfirm((ress.rows || {}).allFlag !== 'Y' || (res.rows || {}).totalElements === 0);
        }
      });
      props.dataSet.clearCachedSelected();
      props.setState(true);
    }
  };

  const url = `${API_HOST}${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-material-lot/excel/import-attr/ui`;
  const uploadProps = {
    name: 'file',
    beforeUpload,
    showUploadList: false,
    headers: {
      authorization: `Bearer ${getAccessToken()}`,
    },
    accept: ['.xlsx', '.xls'],
    action: url,
    onSuccess: uploadFile,
    showUploadBtn: false,
    data: {},
  };
  return (
    <>
      <span style={{ margin: '-4px 0 0 8px' }}>
        <Upload {...uploadProps}>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            loading={importLoading}
            permissionList={[
              {
                code: `${path}.button.upload`,
                type: 'button',
                meaning: '列表页-数据上传按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.data.import`).d('数据上传')}
          </PermissionButton>
        </Upload>
      </span>
      <DownloadButton
        path={path}
        url={`${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-material-lot/download/model-attr/ui`}
      />
      <PermissionButton
        type="c7n-pro"
        icon="spellcheck"
        onClick={validateAction}
        disabled={validateStatus}
        loading={validateLoading}
        permissionList={[
          {
            code: `${path}.button.validatas`,
            type: 'button',
            meaning: '列表页-数据验证按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.button.validatas`).d('数据验证')}
      </PermissionButton>
      <PermissionButton
        type="c7n-pro"
        icon="file_upload"
        onClick={confirmDatas}
        disabled={confirmStatus}
        loading={confirmLoading}
        permissionList={[
          {
            code: `${path}.button.confirm`,
            type: 'button',
            meaning: '列表页-数据导入按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.button.data.confirm`).d('数据导入')}
      </PermissionButton>
      <PermissionButton
        type="c7n-pro"
        icon="delete"
        onClick={onDelete}
        disabled={props.deleteFlag}
        permissionList={[
          {
            code: `${path}.button.edit`,
            type: 'button',
            meaning: '列表页-编辑新建删除复制按钮',
          },
        ]}
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
      <PrintButton
        disabled={props.deleteFlag}
        path={path}
        permissionFlag
        dataSet={props.dataSet}
        labelTemplateCode="APEX_WMS.MATERIAL_LOT_TEMP_001"
      />
    </>
  );
};

export default withRouter(CommonButton);
