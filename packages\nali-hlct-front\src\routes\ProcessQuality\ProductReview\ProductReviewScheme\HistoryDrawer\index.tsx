/**
 * @Description: 产品审核方案-历史记录按钮
 */

import React, {useMemo} from 'react';
import {Button, DataSet, Modal} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { headDS  } from './stores';

import TableContainer from "./tableContainer";

const modelPrompt = 'tarzan.hwms.ProductReviewScheme';

export interface ApprovalDrawerProps {
  type?: 'button' | 'text';
  objectTypeList: String[];
  objectId: string;
  disabled?: boolean;
}

const AttributeDrawer: React.FC<ApprovalDrawerProps> = ({
  type = 'button',
  objectId,
  disabled = false,
}) => {
  const headDs = useMemo(() => new DataSet(headDS()), []);

  const handleOpenDrawer = () => {
    if (objectId !== 'create') {
      headDs.setQueryParameter('productRevSchemeId', objectId);
      headDs.query();
    }
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
        ds: headDs,
      }),
      title: intl.get(`${modelPrompt}.title.historyInfo`).d('历史查询'),
      style: {
        width: 1080,
      },
      // @ts-ignore
      children: <TableContainer objectId={objectId} headDs={headDs}/>,
    });
  };

  return (
    <>
      {type === 'text' ? (
        <span className="action-link">
          <Button funcType={FuncType.flat} onClick={handleOpenDrawer} disabled={disabled}>
            {intl.get(`${modelPrompt}.button.historyInfo`).d('历史查询')}
          </Button>
        </span>
      ) : (
        <Button onClick={handleOpenDrawer} disabled={disabled}>
          {intl.get(`${modelPrompt}.button.historyInfo`).d('历史查询')}
        </Button>
      )}
    </>
  );
};

export default AttributeDrawer;
