:global {
  .kb-doc-attachment {
    margin-top: 8px;

    :global {
      .c7n-upload-list {
        display: none;
      }
    }

    .kb-doc-attachment-list {
      display: flex;
      flex-wrap: wrap;

      :global {
        .c7n-col-8 {
          &:nth-child(3n + 1) {
            padding-right: 8px;
          }

          &:nth-child(3n + 2) {
            padding: 0 8px;
          }

          &:nth-child(3n + 3) {
            padding-left: 8px;
          }
        }
      }

      .kb-doc-file-item {
        display: flex;
        width: 100%;
        height: 64px;
        margin: 8px 0;
        background: #fff;
        border: 1px solid rgba(229, 229, 229, 1);
        border-radius: 2px;

        &.preview:hover {
          .kb-doc-file-name {
            color: #0840f8;
          }

          .kb-doc-file-operation-preview {
            opacity: 1;
            transition-duration: 0.5s;
            transition-property: opacity;
          }
        }

        .kb-doc-file-image {
          width: 64px;
          height: 64px;

          img {
            width: 48px;
            height: 48px;
            margin: 8px;
          }
        }

        .kb-doc-file-name {
          width: calc(100% - 132px);
          overflow: hidden;
          line-height: 64px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .kb-doc-file-operation {
          display: flex;
          align-items: center;
          width: 60px;

          &-preview {
            width: 16px;
            height: 16px;
            margin-right: 20px;
            margin-left: 8px;
            cursor: pointer;
            opacity: 0;
            transition-duration: 0.1s;
            transition-property: opacity;

            &.kb-doc-file-operation-preview-none {
              visibility: hidden;
            }
          }

          &-download {
            width: 16px;
            height: 16px;
            margin-left: 46px;
            cursor: pointer;
          }

          &-delete {
            width: 16px;
            height: 16px;
            margin-left: 46px;
            cursor: pointer;
          }
        }
      }
    }
  }

  .video-wrapper {
    position: relative;

    img {
      position: absolute;
      width: 36px;
      height: 36px;
      top: 16px;
      right: 20px;
    }
  }

  .audio-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-top: 103px;

    .audio-icon {
      width: 160px;
      height: 160px;
      margin-bottom: 44px;
    }

    img.close {
      position: absolute;
      width: 36px;
      height: 36px;
      top: 16px;
      right: 20px;
    }
  }
}
