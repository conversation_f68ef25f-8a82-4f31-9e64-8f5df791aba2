/**
 * @Description: 创建送货单抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-12-13 10:31:13
 * @LastEditTime: 2023-05-18 16:17:11
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { BASIC } from '@/utils/config';
import { materialLotDS } from '../stores/MaterialLotDS';

const MaterialLotDrawer = props => {
  const { instructionDocType, instructionId, identifyType } = props.record;
  const materialLotDs = useMemo(() => {
    return new DataSet(materialLotDS());
  }, []);

  useEffect(() => {
    materialLotDs.queryParameter = {
      instructionDocType,
      instructionId,
      customizeUnitCode: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_MATERIAL_LOT.QUERY`,
    };

    materialLotDs.query();
  }, [instructionDocType, instructionId]);

  // 组件息表配置
  const receiveColumns = [
    {
      name: 'materialIdentification',
      width: 180,
    },
    {
      name: 'materialLotStatusDesc',
      width: 120,
    },
    {
      name: 'containerIdentification',
      width: 80,
    },
    {
      name: 'sumActualQty',
      width: 80,
    },
    {
      name: 'uomCode',
      width: 60,
    },
    {
      name: 'lot',
      width: 80,
    },
    {
      name: 'locatorCode',
      width: 140,
    },
    {
      name: 'scrapDoc',
      width: 140,
    },
    {
      name: 'sourceInsCreateDate',
      width: 150,
      align: 'center',
    },
    {
      name: 'sourceInsCreatedBy',
      width: 80,
    },
  ];

  const receiveColumns2 = [
    {
      name: 'materialCode',
      width: 120,
    },
    // {
    //   name: 'materialName',
    //   width: 120,
    // },
    {
      name: 'qualityStatusDesc',
      width: 80,
    },
    {
      name: 'sumActualQty',
      width: 80,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'lot',
      width: 80,
    },
    {
      name: 'locatorCode',
      width: 140,
    },
    {
      name: 'scrapDoc',
      width: 140,
    },
    {
      name: 'sourceInsCreateDate',
      width: 150,
      align: 'center',
    },
    {
      name: 'sourceInsCreatedBy',
      width: 80,
    },
  ];

  const columsFun = identifyType === 'MATERIAL_LOT' ? receiveColumns : receiveColumns2;

  return props.customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_MATERIAL_LOT.QUERY`,
    },
    <Table dataSet={materialLotDs} columns={columsFun} />,
  )
};

export default MaterialLotDrawer;
