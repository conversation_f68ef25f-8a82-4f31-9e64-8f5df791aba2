import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';

import { getCurrentOrganizationId } from 'utils/utils';
import { Button as PermissionButton } from 'components/Permission';
import request from 'utils/request';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { Tag } from 'hzero-ui';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores/DataMaintenanceDS';

const modelPrompt = 'tarzan.hlct.dataMaintenance';

const DataMaintenaceList = props => {
  const {
    match: { path },
    tableDs,
  } = props;

  useEffect(() => {
    tableDs.query(props.tableDs.currentPage);
  }, []);

  const handleCreate = () => {
    props.history.push(`/hwms/product-disassembly-data-maintenance/detail/create`);
  };

  const handleEdit = record => {
    props.history.push(
      `/hwms/product-disassembly-data-maintenance/detail/${record.data.teardownHeadId}`,
    );
  };
  const handleDelete = record => {
    request(
      `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-teardown-head/${
        record.data.teardownHeadId
      }`,
      {
        method: 'DELETE',
      },
    ).then(res => {
      if (res && !res.failed) {
        tableDs.query(props.tableDs.currentPage);
        notification.success({});
      }
    });
  };

  const columns = [
    {
      name: 'seq',
      width: 60,
      renderer: ({ record }) => {
        return <span>{record && record.index + 1}</span>;
      },
    },
    {
      name: 'siteName',
    },
    {
      name: 'productFormCode',
    },
    {
      name: 'teardownLocationDescList',
      renderer: ({ value }) => (value || []).map(item => <Tag color="blue">{item}</Tag>),
    },
    {
      name: 'option',
      title: intl.get('hzero.common.button.action').d('操作'),
      align: 'center',
      lock: 'right',
      renderer: ({ record }) => {
        return (
          <div>
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => handleDelete(record)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <PermissionButton
                permissionList={[
                  {
                    code: `${path}.button.delete `,
                    type: 'button',
                    meaning: '列表页-删除',
                  },
                ]}
                type="text"
              >
                {intl.get('hzero.common.button.delete').d('删除')}
              </PermissionButton>
            </Popconfirm>
            <PermissionButton
              permissionList={[
                {
                  code: `${path}.button.edit `,
                  type: 'button',
                  meaning: '列表页-编辑',
                },
              ]}
              type="text"
              onClick={() => handleEdit(record, 'new')}
            >
              {intl.get('hzero.common.button.edit').d('编辑')}
            </PermissionButton>
          </div>
        );
      },
    },
  ];

  return (
    <div className="hmes-style" style={{ height: '100%' }}>
      <Header title={intl.get(`${modelPrompt}.title`).d('产品拆解基础数据维护')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.create`,
              type: 'button',
              meaning: '列表页-新建',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleCreate()}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <div style={{ width: '100%', display: 'flex' }}>
          <Table
            searchCode="yblzmwh1"
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
          />
        </div>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [
    'tarzan.hlct.dataMaintenance',
    'tarzan.common',
    'hzero.common',
  ],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });

      return {
        tableDs,
      };
    },
    { cacheState: true, keepOriginDataSet: true },
  )(DataMaintenaceList),
);
