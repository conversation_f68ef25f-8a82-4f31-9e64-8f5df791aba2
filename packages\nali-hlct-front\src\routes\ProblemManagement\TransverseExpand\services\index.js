/**
 * @Description: 问题横展平台-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-06-15 11:37:54
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

// 详情数据查询
export function fetchTransverseExpand() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/detail/query/for/ui`,
    method: 'GET',
  };
}

// 详情数据保存
export function saveTransverseExpand() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/save/for/ui`,
    method: 'POST',
  };
}

// 整单提交
export function transverseExpandSubmitConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/submit/for/ui`,
    method: 'POST',
  };
}

// 整单发布
export function transverseExpandPublishConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/published/for/ui`,
    method: 'POST',
  };
}

// 整单关闭
export function transverseExpandClosedConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/closed/for/ui`,
    method: 'POST',
  };
}

// 行提交
export function transverseExpandLineSubmitConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/task/submit/for/ui`,
    method: 'POST',
  };
}
// 行取消
export function transverseExpandLineCancelConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/cancel/for/ui`,
    method: 'POST',
  };
}

// 行评论
export function transverseExpandLineEvaluateConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/follow/up/evaluate/for/ui`,
    method: 'POST',
  };
}

// 行审核
export function transverseExpandLineReviewConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/review/for/ui`,
    method: 'POST',
  };
}

// 获取用户部门
export function getUserDepartmentConfig() {
  return {
    url: `/hpfm/v1/${tenantId}/employee-assigns`,
    method: 'GET',
  };
}

// 变更启用提醒
export function ChangeRemindFlagConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-problem-spread-list/remind-flag-update/ui`,
    method: 'POST',
  };
}
