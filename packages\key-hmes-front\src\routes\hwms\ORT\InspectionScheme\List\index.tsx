/**
 * @Description: ORT检验方案维护-主界面
 */
import React, { FC, useEffect, useMemo, useState } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { Badge, Collapse } from 'choerodon-ui';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import ExcelExport from 'components/ExcelExport';

import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { openTab } from 'utils/menuTab';
import queryString from 'query-string';
import { useRequest } from '@components/tarzan-hooks';
import { GetUserRole } from '../services';
import HistoryDrawer from '../HistoryDrawer';
import { headDS, lineDS } from '../stores';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.ort.InspectionScheme';
const { Panel } = Collapse;

interface TaskListProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
}

const InspectionSchemeList: FC<TaskListProps> = props => {
  const { headDs, lineDs, history } = props;
  const { run: getCreateRole } = useRequest({ lovCode: 'YP.QIS.ORT_SCHEME_CERATE_ROLE' }, {
    manual: true,
    needPromise: true,
  });
  const { run: getUserRole } = useRequest(GetUserRole(), {
    manual: true,
    needPromise: true,
  });
  const [createFlag, setCreateFlag] = useState<boolean>(false);

  useEffect(() => {
    handleInitCreateRole();
  }, []);

  const handleInitCreateRole = async () => {
    const res = await getCreateRole({});
    const currentRoleRes = await getUserRole({});
    const createRoleList = (res || []).map((item) => item.value);
    const currentRoleList = (currentRoleRes || []).map((item) => item.code);
    const createFlag = createRoleList.find((item) => currentRoleList.includes(item));
    setCreateFlag(Boolean(createFlag));
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('schemeId'));
    } else {
      queryLineTable(null);
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = schemeId => {
    lineDs.loadData([]);
    if (schemeId) {
      lineDs.setQueryParameter('schemeId', schemeId);
      lineDs.query();
    }
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectSchemeCode',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hmes/ort/inspection-scheme/${record!.get('schemeId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'inspectSchemeName',
        width: 130,
      },
      { name: 'siteName', width: 180, lock: ColumnLock.left },
      { name: 'materialCode', width: 180 },
      { name: 'materialName' },
      {
        name: 'enableFlag',
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      { name: 'enableDate', width: 150 },
      { name: 'disableDate', width: 150 },
      { name: 'createdName' },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 150,
        renderer: ({ record }) => (
          // @ts-ignore
          <HistoryDrawer objectId={record?.get('schemeId')} type="text" />
        ),
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'sequence' },
      { name: 'inspectItem', width: 180, lock: ColumnLock.left },
      { name: 'inspectMethod' },
      { name: 'standardRequirement', width: 150 },
      { name: 'inspectFrequency' },
      {
        name: 'outsourceFlag',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
      },
      { name: 'enclosure', width: 150, lock: ColumnLock.right },
    ],
    [],
  );

  const handleAdd = () => {
    history.push(`/hmes/ort/inspection-scheme/create`);
  };

  const handleQuerySearchForm = () => {
    return headDs.queryDataSet?.toData()[0];
  };
  const handleBatchExport = () => {
    openTab({
      key: `/hmes/measure-have/comment-import/YP.QIS_ORT_INSPECT_SCHEME_IMP`,
      title: 'hzero.common.title.import',
      search: queryString.stringify({
        action: intl.get(`${modelPrompt}.button.import`).d('导入'),
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('ORT检验方案维护')}>
        <Button color={ButtonColor.primary} icon="add" onClick={handleAdd} disabled={!createFlag}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <Button icon="file_upload" onClick={handleBatchExport}>
          {intl.get(`${modelPrompt}.button.import`).d('导入')}
        </Button>
        <ExcelExport
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ort-inspect-scheme/export/for/ui`}
          method="get"
          queryParams={handleQuerySearchForm}
        >
          {intl.get(`${modelPrompt}.button.export`).d('导出')}
        </ExcelExport>
      </Header>
      <Content>
        <Table
          customizedCode="ortjyfa1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headDs}
          columns={headColumns}
          searchCode="ortjyfa1"
          onRow={({ record }) => ({
            onClick: () => queryLineTable(record?.get('schemeId')),
          })}
        />
        <Collapse bordered={false} defaultActiveKey={['line']}>
          <Panel key="line" header={intl.get(`${modelPrompt}.title.testItem`).d('测试项目')}>
            <Table customizedCode="ortjyfa2" dataSet={lineDs} columns={lineColumns} />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(InspectionSchemeList as any),
);
