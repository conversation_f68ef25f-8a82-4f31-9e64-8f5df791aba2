/**
 * @Description: 检验项目组维护-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 15:59:30
 * @LastEditTime: 2023-05-18 16:50:58
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

// 详情数据查询
export function FetchInspectGroupDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/edit/query/ui`,
    method: 'GET',
  };
}

// 详情数据保存
export function SaveInspectGroupDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/save/ui`,
    method: 'POST',
  };
}

// 详情数据提交
export function SubmitInspectGroupDetail() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/submit/ui`,
    method: 'POST',
  };
}

// 审批通过
export function handleApproval() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/approval/ui`,
    method: 'POST',
  };
}

// 审批驳回
export function handleunApproval() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/reject/ui`,
    method: 'POST',
  };
}

// 失效
export function handleunenabled() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/expired/ui`,
    method: 'POST',
  };
}

/**
 * 附件复制
 * @function CopyEnclosure
 * @returns {object} fetch Promise
 */
export function CopyEnclosure(): object {
  return {
    url: `/hfle/v1/${tenantId}/files/copy-file`,
    method: 'POST',
  };
}
