import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Collapse } from 'choerodon-ui';
import { DataSet, Select, Lov } from 'choerodon-ui/pro/lib';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import ReactEchartsCore from 'echarts-for-react/lib/core';
import axios from 'axios';
import { HALM_ATN } from 'alm/utils/config';
import queryDS from './Stores';
import styles from './index.module.less';

const organizationId = getCurrentOrganizationId();
const url = `${HALM_ATN}/v1/${organizationId}/table-cards/personal-kpi`;

// 标签字符串改为10个字节一换行， 最多两行
const LabelFormatter = (value: string) => {
  let res = '';
  let realLength = 0;
  let subStart = 0;
  const valLength = value.length;
  for (let i = 0; i < valLength; i++) {
    let temp = '';
    const charCode = value.charCodeAt(i);
    let charLength = 0;
    if (charCode >= 0 && charCode <= 128) {
      charLength = 1;
    } else {
      charLength = 2;
    }
    if (realLength + charLength > 10) {
      realLength = charLength;
      if (subStart > 0) {
        temp = `${value.substring(subStart, i)}...`;
        res += temp;
        return res;
      } else {
        temp = `${value.substring(subStart, i)}\n`;
        res += temp;
        subStart = i;
      }
    } else {
      realLength += charLength;
    }
  }
  const temp = `${value.substring(subStart, valLength)}`;
  res += temp;
  return res;
};

const PersonalKpi = () => {
  const [data, setData] = useState<any>(undefined);

  useEffect(() => {
    fetchData();
  }, []);

  const queryDs = useMemo(() => {
    return new DataSet(queryDS());
  }, []);

  const fetchData = useCallback(async () => {
    const { dateUnitCode, workCenterId } = queryDs?.current?.toData();
    const res = await axios.get<any, any>(url, {
      params: { dateUnitCode, workCenterId },
    });
    const { workCenterName = null } = res;
    if (workCenterName !== null) {
      // eslint-disable-next-line no-unused-expressions
      queryDs?.current?.set('workCenterName', res.workCenterName);
    }
    setData(res.detailDTOList);
  }, []);

  const renderTooltip = useCallback(params => {
    const icon1 = `<span style="margin: 8px;height:12px;width:12px;display:inline-block;vertical-align:middle;position:relative;top:-1px;background-color:#00CCB9"></span>`;
    const value1 = `<span style="color:#333333;"> ${params[0].seriesName}：${params[0].value}</span><br/>`;
    const icon2 = `<span style="margin: 8px;height:12px;width:12px;display:inline-block;vertical-align:middle;position:relative;top:-1px;background-color:#B8C4CE"></span>`;
    const value2 = `<span style="color:#333333;">  ${params[1].seriesName}：${params[1].value}</span>`;
    return icon1 + value1 + icon2 + value2;
  }, []);

  const colorRenderer = useCallback(({ text }) => {
    return <span style={{ color: '#666' }}>{text}</span>;
  }, []);

  const option = useMemo(() => {
    return {
      grid: {
        top: 50,
        left: 60,
        right: 0,
        bottom: 45,
      },
      legend: {
        show: true,
        right: 0,
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          height: 12,
          fontSize: 12,
          padding: [3, 0, 0, 0],
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: '#FFF',
        extraCssText: 'box-shadow: 0 2px 10px 5px rgba(124,133,155,0.10);',
        formatter: params => renderTooltip(params),
      },
      xAxis: [
        {
          type: 'category',
          axisTick: { show: false },
          data: data?.map(i => i.empName),
          axisLabel: {
            interval: 0,
            color: '#A9A9A9',
            formatter: LabelFormatter,
          },
          axisLine: {
            lineStyle: {
              color: '#CCCCCC',
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '工作项计数',
          minInterval: 1,
          axisTick: { show: false },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#A9A9A9',
          },
          nameTextStyle: {
            color: '#A9A9A9',
            align: 'right',
          },
        },
      ],
      series: [
        {
          name: '已完成',
          type: 'bar',
          stack: 'one',
          barWidth: '10',
          itemStyle: {
            color: '#00CCB9',
          },
          data: data?.map(i => i.completedOrderCount),
        },
        {
          name: '未完成',
          type: 'bar',
          stack: 'one',
          barWidth: '10',
          itemStyle: {
            color: '#B8C4CE',
          },
          data: data?.map(i => i.unCompletedOrderCount),
        },
      ],
      dataZoom: data?.length > 7 && {
        realtime: true,
        height: 10,
        start: 0,
        end: 30,
        bottom: 0,
      },
    };
  }, [data]);
  return (
    <div className={styles.container}>
      <Collapse
        bordered={false}
        expandIconPosition="right"
        defaultActiveKey={['A']}
        trigger="icon"
        className={styles['customize-collapse']}
      >
        <Collapse.Panel key="A" showArrow={false} header="人员工作统计">
          <div className={styles['query-bar']}>
            <Lov
              dataSet={queryDs}
              name="workCenterLov"
              onChange={fetchData}
              renderer={colorRenderer}
            />
            <Select
              dataSet={queryDs}
              name="dateUnitCode"
              clearButton={false}
              onChange={fetchData}
              renderer={colorRenderer}
            />
          </div>
          <ReactEchartsCore
            echarts={echarts}
            option={option}
            notMerge
            lazyUpdate
            style={{
              height: '100%',
            }}
          />
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default PersonalKpi;
