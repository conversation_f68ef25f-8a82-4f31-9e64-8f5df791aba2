import React, { Component } from 'react';
import { <PERSON><PERSON>, Button } from 'hzero-ui';
import intl from 'utils/intl';
import { connect } from 'dva';
import { Table, DataSet, Form } from 'choerodon-ui/pro/lib';
import notification from 'utils/notification';
import { tableDs } from '../stores/HandQueryModaLDs';

import styles from '../index.less';
@connect(({ exceptionHandlingPlatform, loading }) => ({
  exceptionHandlingPlatform,
  getNowQtyLoading: loading.effects['exceptionHandlingPlatform/getNowQty'],
}))
export default class HandQueryModal extends Component {
  constructor(props) {
    super(props);
    this.tableDs = new DataSet(tableDs); // 表格dateSet
  }

  handleCancel = () => {
    this.props.handlecancel();
  };

  getLatestHandQuery = async queryDataSet => {
    const { getNowQty } = this.props;
    const isPass = await queryDataSet.validate();
    if (isPass) {
      // eslint-disable-next-line prefer-destructuring
      const siteCode = queryDataSet.records[0].data.plantCode;
      const materialCode = queryDataSet.records[0].data.materialId;
      const warehouseCode = queryDataSet.records[0].data.locatorCode;
      const info = { siteCode, warehouseCode, ...materialCode };
      getNowQty(info);
    }
  };

  /**
   * 自定义搜索
   * @param {*} props
   * @returns ReactNode
   */
  renderBar = props => {
    const { queryFields, queryDataSet, queryFieldsLimit, dataSet } = props;
    const { getNowQtyLoading } = this.props;
    // const { loading } = this.state;
    if (queryDataSet) {
      return (
        <React.Fragment>
          <Form columns={queryFieldsLimit} dataSet={queryDataSet}>
            {queryFields.slice(0, 2)}
            <div>
              <Button
                type="primary"
                className={styles['exception-handling-platform-btn-pad']}
                onClick={() => this.getLatestHandQuery(queryDataSet)}
                loading={getNowQtyLoading}
              >
                {intl.get('hmes.operationPlatform.view.message.handQuery.search').d('现有量获取')}
              </Button>
              <Button
                type="primary"
                style={{ width: '70px' }}
                onClick={() => {
                  if (
                    queryDataSet.records[0].data.plantCode &&
                    queryDataSet.records[0].data.materialId
                  ) {
                    dataSet.query();
                  } else {
                    notification.error({ message: '请输入必输值' });
                  }
                }}
              >
                查询
              </Button>
            </div>
            {queryFields.slice(2)}
          </Form>
          {/* <FilterBar {...props} buttons={buttons} /> */}
        </React.Fragment>
      );
    }
  };

  render() {
    const { visible } = this.props;
    const columns = [
      {
        name: 'plantCode',
      },
      {
        name: 'locatorCode',
      },
      {
        name: 'locatorType',
      },
      {
        name: 'locatorLocation',
      },
      {
        name: 'materialCode',
      },
      {
        name: 'quantity',
      },
      {
        name: 'uomCode',
      },
      {
        name: 'batchDate',
      },
    ];
    return (
      <Modal
        visible={visible}
        title="现有量查询"
        footer={null}
        onCancel={this.handleCancel}
        width={1000}
        mask={false}
        destroyOnClose
        bodyStyle={{
          height: '500px',
          overflowY: 'auto',
        }}
        wrapClassName={styles['hand-query-modal-prefix']}
      >
        <Table
          dataSet={this.tableDs}
          queryBar={this.renderBar}
          queryFieldsLimit={3}
          selectionMode="none"
          columns={columns}
        />
      </Modal>
    );
  }
}
