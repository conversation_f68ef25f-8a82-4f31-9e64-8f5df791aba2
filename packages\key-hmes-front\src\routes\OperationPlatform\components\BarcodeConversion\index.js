// 条码转换
import React, { useState, useEffect } from 'react';
import { Form, TextField, Tooltip, Icon } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import { CardLayout } from '../commonComponents';
import { useOperationPlatform } from '../../contextsStore';
import styles from './index.modules.less';

const tenantId = getCurrentOrganizationId();
let numInterval; // 加工时长定时器

const MachinedPartCard = props => {
  const { workOrderData, dispatch } = useOperationPlatform();
  const [identification, setIdentification] = useState(''); // 条码
  const [loading, setLoading] = useState(false); // 工单loading

  const [color, setColor] = useState('#cec8c8'); // 工单loading

  const [snCodes, setSnCodes] = useState(''); //

  useEffect(() => {
    if (workOrderData?.eoId && workOrderData?.cardWorkpiece === 'Y') {
      dispatch({
        type: 'update',
        payload: {
          workOrderData,
        },
      })
      setColor('#cec8c8');
      setSnCodes('');
    }else if(workOrderData?.cardClear === 'Y'){
      dispatch({
        type: 'update',
        payload: {
          workOrderData: {},
        },
      })
      setColor('#cec8c8');
      setSnCodes('');
      setIdentification('');
    }
  }, [workOrderData]);

  // 条码转换
  const onFetchProcessed = value => {
    clearInterval(numInterval);
    if (value) {
      setLoading(true);
      setIdentification(value);
      const params = {
        snCode: value,
        sourceCode: workOrderData?.identification,
      };
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-serial-numbers/scan`, {
        method: 'GET',
        query: params,
      }).then(res => {
        if (res && !res.failed) {
          props.handleAddRecords({
            cardId: props.cardId,
            messageType: 'SUCCESS',
            message: `条码转换成功`,
          });
          setLoading(false);
          setColor('#7fe17f');
          setSnCodes(value)
          props.nextPriority();
        } else {
          setLoading(false);
          notification.error({ message: res.message });
          setTimeout(() => {
            document.querySelector(`#operationPlatformInput${props.priorityLayout?.filter(item => item.i === '22')[0]?.priority}`).focus();
            document.querySelector(`#operationPlatformInput${props.priorityLayout?.filter(item => item.i === '22')[0]?.priority}`).select();
          }, 100);
          props.handleAddRecords({
            cardId: props.cardId,
            messageType: 'FAIL',
            message: `条码转换失败`,
          });
          setColor('red')
        }
      });
    }
  };

  return (
    <CardLayout.Layout className={styles.BarcodeConversion} spinning={loading}>
      <CardLayout.Header
        className='BarcodeConversionHead'
        title='条码转换'
        help={props?.cardUsage?.remark}
        content={
          <TextField
            placeholder="请扫描条码"
            id={`operationPlatformInput${props.priorityLayout?.filter(item => item.i === '22')[0]?.priority}`}
            name="identification"
            disabled={color === '#7fe17f'}
            value={identification}
            onEnterDown={e => onFetchProcessed(e.target.value)}
            onChange={value => (value ? null : onFetchProcessed(null))}
            prefix={<img src={scanIcon} alt='' style={{height: '19px'}}/>}
          />
        }
        addonAfter={
          <Icon style={{ color, fontSize: 26 }} type="wb_incandescent" />
        }
      />
      <CardLayout.Content className='BarcodeConversionForm'>
        <div className={styles.BarcodeConversionLine} id={styles.BarcodeConversionLineInput}>
          <Form
            labelAlign="center"
            labelWidth="auto"
            labelLayout="horizontal"
            columns={5}
            style={{ padding: '0px 5px 0px 5px' }}
          >
            <TextField
              label="原条码"
              disabled
              colSpan={5}
              value={workOrderData?.identification}
              renderer={({ value }) => (
                <Tooltip theme='dark' title={value}>
                  <span style={{ color: 'white', fontSize: 27, fontWeight: 'bold', display: 'inline-block', margin: '10px 0' }}>
                    {value}
                  </span>
                </Tooltip>
              )}
            />
            <TextField
              label="现条码"
              disabled
              id="BarcodeConversion"
              className={color === '#7fe17f' ? 'BarcodeConversionInputDisabled' : ''}
              value={snCodes}
              newLine
              colSpan={5}
              renderer={({ value }) => (
                <Tooltip theme='dark' title={value}>
                  <span style={{ fontSize: 27, color: '#47d4cd', fontWeight: 'bold', display: 'inline-block', margin: '10px 0' }}>
                    {value}
                  </span>
                </Tooltip>
              )}
            />
          </Form>
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(MachinedPartCard);
