/**
 * @Description: 体系审核管理维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import notification from 'utils/notification';
import { Form, Button, Select, Table, Output } from 'choerodon-ui/pro';
import { getCurrentUser } from 'utils/utils';
import { Steps, Popconfirm, Tag } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { Button as PermissionButton } from 'components/Permission';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import moment from 'moment';
import styles from '../index.modules.less';
import { saveAuditScheduleConfig } from '../services';

const modelPrompt = 'tarzan.systemAudit';

const Step = Steps.Step;

const setpList = ['NEW', 'RELEASED', 'REVIEWING', 'REVIEW_COMPLETED', 'CLOSED'];

const AuditSchedule = props => {
  const {
    groupDataList,
    auditScheduleFormDs,
    auditScheduleTableDs,
    id,
    canEdit,
    setCanEdit,
    threeResList,
    readonly,
    initPageData = () => {},
    sysReviewPlanStatus,
    commitOaSubmit,
    pubFlag,
  } = props;

  // 审核小组保存
  const saveAuditSchedule = useRequest(saveAuditScheduleConfig(), {
    manual: true,
    needPromise: true,
  });

  const [user] = useState(getCurrentUser()); // 用户详细信息

  useEffect(() => {
    createGroupList();
  }, [groupDataList]);

  const handleCancel = () => {
    setCanEdit(false);
    initPageData('AuditTab3Schedule');
  };

  const handleSave = async () => {
    const validateResult = await auditScheduleTableDs.validate();
    if (!validateResult) {
      return;
    }

    if (auditScheduleTableDs.length === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.message.scheduleNeed`).d('请添加审核日程'),
      });
      return;
    }

    const tableDataList: any = [];
    auditScheduleTableDs.forEach(record => {
      const tableData = record.toData();
      tableData.changeFlag = record.status === 'sync' ? 'N' : 'Y';
      tableData.scheduleDate = moment(tableData.scheduleDate).format('YYYY-MM-DD');
      tableData.scheduleTimeFrom = tableData.scheduleTime?.scheduleTimeFrom;
      tableData.scheduleTimeTo = tableData.scheduleTime?.scheduleTimeTo;
      tableData.sysReviewStandard = (tableData.sysReviewStandardIdList || []).join();
      tableData.beRevDeptPer= (tableData.beRevDeptPer || []).join(',');
      tableData.beRevDeptPerName= (tableData.beRevDeptPerName || []).join(',');
      tableDataList.push(tableData);
    });

    const res = await saveAuditSchedule.run({
      params: tableDataList,
      queryParams: {
        sysReviewPlanId: id,
      },
    });
    if (res?.success) {
      // @ts-ignore
      notification.success();
      setCanEdit(false);
      initPageData('AuditTab3Schedule');
    }
  };

  const createGroupList = () => {
    const groupList: any = [];
    (groupDataList || []).forEach(item => {
      const { formDs, tableDs } = item;
      const listItemData = (formDs.toData() || [])[0] || {};
      const groupUserId: any = [];
      const groupUserName: any = [];
      groupUserId.push(listItemData.groupLeader);
      groupUserName.push(listItemData.groupLeaderName);
      tableDs.forEach(record => {
        groupUserId.push(record.get('groupMemId'));
        groupUserName.push(record.get('groupMemName'));
      });
      listItemData.groupUserId = groupUserId;
      listItemData.groupUserName = groupUserName;
      groupList.push(listItemData);
    });
    auditScheduleTableDs.setState('groupList', groupList);
    if (threeResList) {
      auditScheduleTableDs.setState('siteId', threeResList[0]?.rows?.siteId);
    }
    auditScheduleTableDs.setState('sysReviewPlanId', id);
  };

  // 添加小组行
  const addScheduleRow = () => {
    auditScheduleTableDs.create({});
  };

  // 删除小组行
  const deleteScheduleRow = record => {
    auditScheduleTableDs.delete(record, false);
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          funcType="flat"
          shape="circle"
          size="small"
          onClick={addScheduleRow}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      hidden: !canEdit,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteScheduleRow(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit}
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      title: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
      renderer: ({ record }) => record!.index + 1,
      width: 80,
      align: ColumnAlign.right,
      lock: ColumnLock.left,
    },
    {
      name: 'scheduleCode',
      width: 200,
      lock: ColumnLock.left,
    },
    {
      name: 'groupObject',
      editor: canEdit && <Select />,
    },
    {
      name: 'memberInfo',
      width: 220,
      renderer: ({ value }) => (value || []).map(item => <Tag key={item}>{item}</Tag>),
    },
    {
      name: 'scheduleDate',
      width: 160,
      editor: canEdit,
    },
    {
      name: 'scheduleTime',
      width: 180,
      editor: canEdit,
    },
    {
      name: 'revDapartmentLov',
      editor: canEdit,
    },
    {
      name: 'revDapartmentPerLov',
      editor: canEdit,
    },
    {
      name: 'beRevContent',
      width: 220,
      editor: canEdit,
    },
    {
      name: 'sysReviewStandardLov',
      width: 320,
      editor: canEdit,
    },
  ];

  return (
    <div className={styles.tabsBody}>
      {!readonly && (
        <div className="control-row">
          <div></div>
          <div>
            {!canEdit && !pubFlag && (
              <Button
                disabled={
                  !['NEW', 'TO_RELEASE', 'REJECTED'].includes(sysReviewPlanStatus) ||
                  (threeResList && user.id !== threeResList[0]?.rows?.createdBy)
                }
                color={ButtonColor.primary}
                onClick={() => {
                  setCanEdit(true);
                }}
              >
                {intl.get('hzero.common.button.edit').d('编辑')}
              </Button>
            )}
            {canEdit && !pubFlag && (
              <>
                <Button onClick={handleCancel}>
                  {intl.get('hzero.common.button.cancel').d('取消')}
                </Button>
                <Button
                  disabled={commitOaSubmit.loading || saveAuditSchedule.loading}
                  color={ButtonColor.primary}
                  onClick={handleSave}
                >
                  {intl.get('hzero.common.button.save').d('保存')}
                </Button>
              </>
            )}
          </div>
        </div>
      )}
      <div>
        <Form dataSet={auditScheduleFormDs} columns={3} labelWidth={112} disabled={!canEdit}>
          <Output name="sysReviewPlanCode" />
          <Output name="sysReviewType" />
          <Output name="planCreaterLov" />
          <Output name="scheduledStartDateTime" />
          <Output name="scheduledEndDateTime" />
        </Form>
        <div className="steps-row">
          <Steps
            size="small"
            current={setpList.indexOf(auditScheduleFormDs.current.get('sysReviewPlanStatus'))}
          >
            <Step
              // status={stepCurrent === 1 ? 'process' : 'wait'}
              title={intl.get(`${modelPrompt}.planCreate`).d('计划创建')}
            />
            <Step
              // status={stepCurrent === 2 ? 'process' : 'wait'}
              title={intl.get(`${modelPrompt}.planApprove`).d('计划已审批')}
            />
            <Step
              // status={stepCurrent === 3 ? 'process' : 'wait'}
              title={intl.get(`${modelPrompt}.inApproval`).d('审核中')}
            />
            <Step
              // status={stepCurrent === 4 ? 'process' : 'wait'}
              title={intl.get(`${modelPrompt}.approvalCompleted`).d('审核完成')}
            />
            <Step
              // status={stepCurrent === 5 ? 'process' : 'wait'}
              title={intl.get(`${modelPrompt}.close`).d('关闭')}
            />
          </Steps>
        </div>
        <Table customizedCode="shtxshrc1" dataSet={auditScheduleTableDs} columns={columns}></Table>
      </div>
    </div>
  );
};

export default AuditSchedule;
