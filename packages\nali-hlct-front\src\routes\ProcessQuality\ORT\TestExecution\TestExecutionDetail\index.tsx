/**
 * @Description: ORT测试执行-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-09-20 10:38:58
 * @LastEditTime: 2023-09-20 10:38:58
 * @LastEditors: <<EMAIL>>
 */

import React, {useEffect, useMemo, useState} from 'react';
import intl from 'utils/intl';

import {Content, Header} from 'components/Page';
import {
  Attachment,
  Button,
  DataSet,
  DatePicker,
  Form,
  Lov,
  NumberField,
  Select,
  Switch,
  Table,
  TextField,
} from 'choerodon-ui/pro';
import {Badge, Collapse, Popconfirm} from 'choerodon-ui';
import {observer} from 'mobx-react';
import {Button as PermissionButton} from 'components/Permission';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import {useRequest} from '@components/tarzan-hooks';
import { TemplatePrintButton } from '@components/tarzan-ui';
import { useDataSetEvent } from 'utils/hooks';
import {ColumnProps} from 'choerodon-ui/pro/lib/table/Column';
import {ButtonColor} from 'choerodon-ui/pro/lib/button/enum';
import {ColumnAlign, ColumnLock} from 'choerodon-ui/pro/lib/table/enum';
import {fetchInspectTaskActConfig, QueryMaterialInfo, saveConfig, scanConfig, submitConfig } from '../services';

import {barCodeDS, childrenDS, headerDS} from '../stores/TestExecutionDS';

const modelPrompt = 'tarzan.qms.ort.testExecution';
const { Panel } = Collapse;

const TestExecutionDetail = props => {
  const {
    match: {
      params: { id },
      path,
    },
  } = props;

  // pub路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);

  // 编辑状态
  const [canEdit, setCanEdit] = useState(false);
  const [taskStatus, setTaskStatus] = useState('');
  const [submitAuth, setSubmitAuth] = useState(false);
  const [printLineInfo, setPrintLineInfo] = useState<any[]>([])
  const [printActInfo, setPrintActInfo] = useState<any[]>([])

  // 详情页数据查询
  const queryDetailRequest = useRequest(fetchInspectTaskActConfig(), {
    manual: true,
    needPromise: true,
  });

  // 扫描条码
  const scanRequest = useRequest(scanConfig(), {
    manual: true,
    needPromise: true,
  });

  // 保存
  const saveRequest = useRequest(saveConfig(), {
    manual: true,
    needPromise: true,
  });

  // 提交
  const submitRequest = useRequest(submitConfig(), {
    manual: true,
    needPromise: true,
  });

  // 查询物料批信息
  const { run: queryMaterialInfo } = useRequest(QueryMaterialInfo(), {
    manual: true,
  });

  const barCodeDs = useMemo(() => new DataSet(barCodeDS()), []);
  const childrenDs = useMemo(
    () =>
      new DataSet({
        ...childrenDS(),
        children: {
          qisOrtInspectTaskActs: barCodeDs,
        },
      }),
    [],
  );
  const headerDs = useMemo(
    () =>
      new DataSet({
        ...headerDS(),
        children: {
          qisOrtInspectTaskLineDTOS: childrenDs,
        },
      }),
    [],
  );

  // 初始化页面
  useEffect(() => {
    initPageData();
  }, [id]);

  const handleUpdateLineSelect = () => {
    const selectInfo = childrenDs.selected.map(record => record.get('ortInspectTaskLineId'));
    setPrintLineInfo(selectInfo);    
  };

  const handleUpdateActSelect = () => {
    // 在useDataSetEvent里拿不到最新的printActInfo，暂时使用这种方法
    let printActInfo: any[] = [];
    setPrintActInfo((pre) => {
      printActInfo = pre || [];
      return pre;
    })

    const actList = [ ...printActInfo ];
    barCodeDs.forEach((record) => {
      const actId = record?.get('qisOrtInspectTaskActId');
      const actIndex = actList.indexOf(actId);
      if (record.isSelected && actIndex === -1) {
        actList.push(actId);
      } else if (!record.isSelected && actIndex !== -1) {
        actList.splice(actIndex, 1);
      }
    })
    setPrintActInfo(actList);    
  };

  useDataSetEvent(childrenDs, 'batchSelect', handleUpdateLineSelect);
  useDataSetEvent(childrenDs, 'batchUnSelect', handleUpdateLineSelect);
  useDataSetEvent(barCodeDs, 'batchSelect', handleUpdateActSelect);
  useDataSetEvent(barCodeDs, 'batchUnSelect', handleUpdateActSelect);

  const handleBarCodeChange = record => {
    record?.dataSet?.parent?.current?.set('newDate', new Date());
  };

  const tableColumn: ColumnProps[] = [
    {
      name: 'sequence',
      renderer: ({ record }: any) => {
        return record.index * 10 + 10;
      },
    },
    {
      name: 'inspectItem',
    },
    {
      name: 'inspectMethod',
    },
    {
      name: 'standardRequirement',
    },
    {
      name: 'inspectFrequency',
    },
    {
      name: 'outsourceFlag',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'inspectQty',
    },
    {
      name: 'exSampleSolveMethod',
    },
    {
      name: 'acSampleSolveMethod',
      editor: canEdit,
    },
    {
      name: 'actualStartTime',
      editor: canEdit,
    },
    {
      name: 'inspectorLov',
    },
    {
      name: 'actualEndTime',
    },
  ];

  const barCodeColumn: ColumnProps[] = [
    {
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      renderer: rendererProps => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDeleteBarCode(rendererProps)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit || taskStatus === 'COMPLETED'}
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      renderer: ({ record }: any) => {
        return record.index * 10 + 10;
      },
    },
    {
      name: 'inspectObject',
    },
    {
      name: 'inspectResult',
      editor: record =>
        canEdit && (
          <Select
            onChange={() => {
              handleBarCodeChange(record);
            }}
          />
        ),
    },
  ];

  // 初始化页面
  const initPageData = async () => {
    // 页面编辑状态和列表选中状态初始化
    setCanEdit(false);
    const res = await queryDetailRequest.run({
      params: {
        ortInspectTaskId: id,
      },
    });

    // 查询到值给页面数据初始化
    if (res.success && res.rows[0] && res.rows[0]?.ortInspectTaskId) {
      // 取出列表值和 表单值
      const { qisOrtInspectTaskDTO, qisOrtInspectTaskLineDTOS, ortInspectTaskId } =
        res.rows[0] || {};

      setTaskStatus(qisOrtInspectTaskDTO.taskStatus || '');
      // 表单值加载
      headerDs.loadData([{ ...qisOrtInspectTaskDTO, ortInspectTaskId, qisOrtInspectTaskLineDTOS }]);
      handleChangeMaterial({
        materialId: qisOrtInspectTaskDTO.materialId,
      });
      setSubmitAuth(submitAuthChange(qisOrtInspectTaskLineDTOS));
    } else {
      setSubmitAuth(false);
    }
  };

  // 校验所有ds
  const validateAllDs = async () => {
    // 返回校验结果
    return await childrenDs.validate();
  };

  const handleChangeMaterial = value => {
    if (value?.materialId) {
      queryMaterialInfo({
        params: value?.materialId,
        onSuccess: res => {
          headerDs.current?.init('productType', res);
        },
      });
    } else {
      headerDs.current?.init('productType', undefined);
    }
  };

  // 组合数据
  const getAllData = () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { qisOrtInspectTaskLineDTOS, ...headerData }: any = headerDs.toData()[0] || {};
    const childrenData: any = [];
    childrenDs.forEach((record, index) => {
      const recordData = record.toData();
      if (record.status !== 'sync') {
        recordData.isUpdate = 'Y';
      }
      childrenData.push({
        ...recordData,
        sequence: index * 10 + 10,
      });
    });
    return {
      ortInspectTaskId: headerData.ortInspectTaskId,
      qisOrtInspectTaskDTO: headerData,
      qisOrtInspectTaskLineDTOS: childrenData,
    };
  };

  // 保存
  const handleSave = async isbefore => {
    const params = getAllData();

    const res = await saveRequest.run({
      params,
    });

    if (isbefore) {
      return res;
    }
    if (res?.success) {
      notification.success({});
      if (id === 'create') {
        props.history.push(`/hwms/ort/test-execution/detail/${res}`);
      } else {
        initPageData();
      }
    }

    return res;
  };

  const isLoading = queryDetailRequest.loading || saveRequest.loading || submitRequest.loading;

  // 取消
  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hwms/ort/test-execution/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  // 头提交
  const handleSubmit = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      setCanEdit(true);
      return;
    }

    let saveRes: any;
    if (canEdit) {
      saveRes = await handleSave(true);
    } else {
      saveRes = {
        success: true,
      };
    }

    if (saveRes?.success) {
      return submitRequest.run({
        params: { ortInspectTaskId: headerDs.current?.get('ortInspectTaskId') },
        onFailed: () => {
          return Promise.resolve(null);
        },
        onSuccess: () => {
          notification.success({});
          initPageData();
        },
      });
    }
  };

  const handleDeleteBarCode = async rendererProps => {
    const targetTableDs = rendererProps?.dataSet;
    const targetRecord = rendererProps?.record;
    if (targetTableDs && targetRecord) {
      targetTableDs.delete(targetRecord, false);
    }
  };

  const handleScan = async e => {
    const value = e.target.value;
    let had = false;

    barCodeDs.toData().forEach((item: any) => {
      if (`${value}` === `${item.inspectObject}`) {
        had = true;
      }
    });

    if (had) {
      notification.error({ message: intl.get(`${modelPrompt}.errorNote`).d('条码已存在') });
      return;
    }

    const res = await scanRequest.run({
      params: {
        internalProductFlag: headerDs.current?.get('internalProductFlag'),
        ortInspectTaskLineId: childrenDs.current?.get('ortInspectLineId'),
        inspectObject: value,
      },
    });
    if (res?.success) {
      notification.success({});
      barCodeDs.create({
        inspectObject: value,
      });
    }
  };

  const ScanInput = observer(props => {
    const { parentDataSet } = props;

    return (
      <TextField
        style={{
          width: '200px',
        }}
        placeholder={intl
          .get(`${modelPrompt}.scanInputPlaceholder`)
          .d('请扫描或输入测试的电芯条码')}
        disabled={!parentDataSet?.current || !canEdit || taskStatus === 'COMPLETED'}
        onEnterDown={handleScan}
        name="sacnInput"
      />
    );
  });

  const submitAuthChange = data => {
    let _submitAuth = true;
    if (data.length === 0) {
      _submitAuth = false;
    }
    data.forEach(record => {
      if ((record.qisOrtInspectTaskActs || []).length === 0) {
        _submitAuth = false;
      }
      (record.qisOrtInspectTaskActs || []).forEach(subItem => {
        if (!subItem.inspectResult) {
          _submitAuth = false;
        }
      });
    });
    return _submitAuth;
  };

  const printParams = useMemo(() => {
    return {
      ortInspectTaskLineIds: printLineInfo?.length ? printLineInfo.join(',') : 0,
      qisOrtInspectTaskActIds: printActInfo?.length ? printActInfo.join(',') : 0,
    };
  }, [childrenDs.selected, barCodeDs.selected]);

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.InspectionSchemeMaintenance`).d('ORT测试执行')}
        backPath={pubFlag ? '' : '/hwms/ort/test-execution/list'}
      >
        {canEdit && (
          <>
            <Button
              color={ButtonColor.primary}
              icon="save"
              onClick={() => {
                return handleSave(false);
              }}
              loading={isLoading}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button loading={isLoading} icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <>
            <PermissionButton
              permissionList={[
                {
                  code: `ort.testExecution.list.edit`,
                  type: 'button',
                  meaning: '编辑',
                },
              ]}
              loading={isLoading}
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              disabled={['CANCEL', 'NEW'].includes(taskStatus)}
              onClick={() => {
                setCanEdit(prev => !prev);
              }}
            >
              {intl.get(`${modelPrompt}.button.release`).d('编辑')}
            </PermissionButton>

            <PermissionButton
              permissionList={[
                {
                  code: `ort.testExecution.list.submit`,
                  type: 'button',
                  meaning: '提交',
                },
              ]}
              disabled={!['SAMPLE_RECEIVED', 'INSPECTING'].includes(taskStatus) || !submitAuth}
              loading={isLoading}
              type="c7n-pro"
              onClick={handleSubmit}
            >
              {intl.get(`${modelPrompt}.button.release`).d('提交')}
            </PermissionButton>
            <TemplatePrintButton
              disabled={!printLineInfo?.length && !printActInfo?.length}
              printButtonCode='ORT_TEST_EXECUTION_PRINT'
              printParams={printParams}
              permissionList={[
                {
                  code: `${modelPrompt}.list.button.printReport`,
                  type: 'button',
                  meaning: '列表页-打印报告按钮',
                },
              ]}
            />
          </>
        )}
      </Header>
      <Content>
        <Collapse collapsible="icon" bordered={false} defaultActiveKey={['panel1', 'panel2']}>
          <Panel header={intl.get(`${modelPrompt}.headerTitle`).d('头数据')} key="panel1">
            <Form dataSet={headerDs} columns={4} labelWidth={112} disabled={!canEdit}>
              <TextField name="inspectDocNum" />
              <Select name="taskStatus" />
              <Lov name="siteLov" />
              <Select name="inspectType" />

              <Switch name="internalProductFlag" />
              <Select name="sampleType" />
              <Lov name="materialLov" />
              <TextField name="materialName" />

              <TextField name="productType" />
              <DatePicker name="expectCompleteTime" />
              <Select name="projectStage" />
              <Select name="urgencyDegree" />

              <Lov name="prodLineLov" />
              <TextField name="ratedCapacity" />
              <TextField name="samplingMonth" />
              <NumberField name="sampleQty" />
              <Select name="inspectResultDemand" />

              <TextField name="inspectPurpose" />
              <TextField name="remark" />
              <Lov name="sampleReceiveByLov" />
              <DatePicker name="sampleReceiveDate" />
              <DatePicker name="actualStartTime" />
              <DatePicker name="actualEndTime" />

              <Attachment
                name="enclosure"
                readOnly={!canEdit || ['CANCEL', 'NEW', 'COMPLETED'].includes(taskStatus)}
              />
            </Form>
          </Panel>

          <Panel
            header={intl.get(`${modelPrompt}.childrenTitle`).d('测试项目')}
            key="panel2"
            extra={<ScanInput parentDataSet={childrenDs} />}
          >
            <div
              style={{
                display: 'flex',
              }}
            >
              <div
                style={{
                  width: '65%',
                  paddingRight: '24px',
                }}
              >
                <Table customizedCode="ortcszx3" dataSet={childrenDs} columns={tableColumn} />
              </div>
              <div
                style={{
                  width: '35%',
                }}
              >
                <Table customizedCode="ortcszx4" dataSet={barCodeDs} columns={barCodeColumn} />
              </div>
            </div>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(observer(TestExecutionDetail));
