// 机台物料
import React, {useEffect, useMemo } from 'react';
import {
  Form,
  DataSet,
  TextField,
  NumberField,
  Output,
  SelectBox,
  Switch,
  // Lov,
  Table,
} from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { ViewMode } from 'choerodon-ui/pro/lib/radio/enum';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import lovChoose from '@/assets/operationPlatformCard/lovChoose.svg';
import cardSvg from '@/assets/icons/operation.svg';
import { locatorDS } from './stores/ContainerDS';
import { CardLayout } from '../commonComponents';
import C7nModal from '../../C7nModal';
import styles from './index.modules.less';

interface CreateContainerModalProps {
  createDs: DataSet;
  selectBoxListRender: () => void;
  selectPackingListRender: () => void;
  changeContainerCode: (value: string) => void;
  flag: boolean,
  enterInfo: any,
}
const tenantId = getCurrentOrganizationId();
let locatorModal;
const CreateContainerModal = (props: CreateContainerModalProps) => {
  const Modal = C7nModal;
  const locatorDs = useMemo(() => new DataSet(locatorDS()), []);
  const { createDs, selectBoxListRender, selectPackingListRender, changeContainerCode, flag, enterInfo } = props;

  useEffect(() => {
    if(!flag){
      setTimeout(() => {
        (document.querySelector('#container-containerCode') as HTMLInputElement)?.focus();
        (document.querySelector('#container-containerCode') as HTMLInputElement)?.select();
      }, 100);
    }
    createDs.current?.reset();
  }, []);

  const handleChangeContainer = value => {
    const requestUrl = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container-type/detail/ui`;
    request(requestUrl, {
      method: 'GET',
      query: {
        containerTypeId: value,
      },
    }).then(res => {
      if (res && !res.failed) {
        createDs.current?.set('containerClassification', res.rows.containerClassification);
        createDs.current?.set('packingLevel', res.rows.packingLevel);
        createDs.current?.set('capacityQty', res.rows.capacityQty);
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  const columnLocator = [
    {
      name: 'locatorCode',
    },
    {
      name: 'locatorName',
    },
  ];

  const onLocatorRow = ({ record }) => {
    return {
      onClick: () => {
        locatorDs.data.forEach(item => {
          item.isSelected = false;
        });
        record.isSelected = true;
      },
      onDoubleClick: () => {
        locatorModal.close();
        createDs.current?.set('locatorId', record.get('locatorId'));
        createDs.current?.set('locatorCode', record.get('locatorCode'));
      }
    };
  };

  const handleFetchLocatorLovData = (value?) => {
    locatorDs.queryDataSet?.current?.reset();
    if(value === 'click'){
      locatorDs.setQueryParameter('locatorCode', null);
    }else if(value){
      locatorDs.setQueryParameter('locatorCode', value);
    }else{
      locatorDs.setQueryParameter('locatorCode', null);
      createDs.current?.set('locatorId', null);
      createDs.current?.set('locatorCode', null);
      return;
    }
    locatorDs.setQueryParameter('workcellId', enterInfo?.workStationId);
    locatorDs.setQueryParameter('siteId', enterInfo?.siteId);
    locatorDs.setQueryParameter('organizationId', enterInfo?.productionLineId);
    locatorDs.query().then(res => {
      if(res && !res.failed){
        if(res.content.length === 1){
          createDs.current?.set('locatorId', res.content[0].locatorId);
          createDs.current?.set('locatorCode', res.content[0].locatorCode);
          return
        }
        if(res.content.length === 0){
          return notification.error({ message: "未查询到库位" });
        }
        locatorModal = Modal.open({
          header: (
            <div style={{ display: 'flex' }}>
              <img src={cardSvg} alt="" className="titleIcon" />
              <div className="c7n-pro-modal-title">货位</div>
            </div>
          ),
          destroyOnClose: true,
          closable: false,
          style: {
            width: '70%',
          },
          mask: true,
          contentStyle: {
            background: '#38708F',
          },
          className: styles.workOrderTextModals,
          children: <Table dataSet={locatorDs} columns={columnLocator} onRow={onLocatorRow}/>,
          okProps: {
            style: {
              background: '#00D4CD !important',
              color: 'white !important',
              borderColor: '#00d4cd !important',
            },
          },
          onOk: () => {
            createDs.current?.set('locatorId', locatorDs.selected[0].get('locatorId'));
            createDs.current?.set('locatorCode', locatorDs.selected[0].get('locatorCode'));
          },
        })
      }
    });
  }

  return (
    <CardLayout.Layout className={styles.container}>
      <CardLayout.Content style={{ position: 'relative' }}>
        <Form
          columns={2}
          dataSet={createDs}
          className={styles.modalForm}
          // labelLayout="none"
          labelWidth={130}
        >
          <TextField name="containerCode" id='container-containerCode' onChange={changeContainerCode} />
          <TextField name="containerName" newLine />
          {/* <Lov
            modalProps={{
              contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
              className: styles.container,
              okProps: {
                style: {
                  background: '#1b7efc',
                },
              },
              cancelProps: {
                style: {
                  background: '#50819c',
                  color: 'white',
                },
              },
            }}
            name="locatorLov"
            newLine
          /> */}
          <TextField
            name="locatorCode"
            onChange={(value) => {handleFetchLocatorLovData(value)}}
            suffix={
              <img
                src={lovChoose}
                alt=''
                style={{height: '19px'}}
                onClick={() => {handleFetchLocatorLovData('click')}}
              />
            }
            newLine
          />
          <SelectBox
            name="containerTypeId"
            onChange={handleChangeContainer}
            colSpan={2}
            mode={ViewMode.button}
            newLine
          >
            {selectBoxListRender()}{' '}
          </SelectBox>
          <NumberField name="capacityQty" newLine />
          <SelectBox
            disabled
            label="装载对象"
            name="packingLevel"
            mode={ViewMode.button}
            multiple
            colSpan={2}
            newLine
          >
            {selectPackingListRender()}{' '}
          </SelectBox>
          <Output name="containerClassification" newLine />
          <Switch name='printFlag' newLine/>
        </Form>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(CreateContainerModal);
