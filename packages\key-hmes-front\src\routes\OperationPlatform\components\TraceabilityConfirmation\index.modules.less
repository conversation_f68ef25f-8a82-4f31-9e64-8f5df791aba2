.traceabilityConfirmation {

  background: rgba(56, 112, 143, 1);

  :global {
    .c7n-pro-field-label {
      color: white;
      font-size: unset !important;
    }

    .c7n-pro-textarea {
      border: none !important;
      color: white !important;
    }

    .c7n-pro-input {
      background: #32617f;
      border: none !important;
      color: white !important;
    }

    .c7n-pro-textarea-wrapper {
      background: #32617f;
    }

    .c7n-tree {
      background: rgba(56, 112, 143, 1) !important;
      color: white !important;
    }
    .c7n-tree.c7n-tree i.icon.icon-arrow_drop_down.c7n-tree-switcher-icon{
      color: rgba(255, 255, 255, 0.7) !important;
      width: auto !important;
      height: auto !important;
    }
    .c7n-tree.c7n-tree .c7n-tree-switcher_oclose i.icon.icon-arrow_drop_down.c7n-tree-switcher-icon {
      color: rgba(255, 255, 255, 0.7) !important;
      width: auto !important;
      height: auto !important;
    }
    .c7n-tree.c7n-tree .c7n-tree-switcher_open i.icon.icon-arrow_drop_down.c7n-tree-switcher-icon {
      color: rgba(51, 241, 255, 1) !important;
      width: auto !important;
      height: auto !important;
    }
  }

  #TraceabilityConfirmationIcon{
    :global{
      .icon{
        font-size: 2vw !important;
      }
    }
  }
  // .TraceabilityConfirmationTree :global(.c7n-tree-list-holder-inner) {
  //   border: 1px solid #ffffff4d !important;
  // }
  .childNode{
    span{
      margin-right: 8px;
      font-size: unset !important;
    }
  }
  :global{
    .c7n-tree-show-line .c7n-tree-switcher{
      background-color: transparent !important;
    }
    .c7n-tree-title.c7n-tree-title{
      color: rgba(255, 255, 255, 0.9) !important;
      font-size: unset !important;
    }
    .c7n-tree.c7n-tree .c7n-tree-treenode .c7n-tree-node-selected{
      background-color: rgba(17, 194, 207, 0.6) !important;
    }
    .c7n-tree.c7n-tree .c7n-tree-treenode .c7n-tree-node-content-wrapper.c7n-tree-node-content-wrapper:hover{
      background-color: rgba(17, 194, 207, 0.6) !important;
    }
    .c7n-tree.c7n-tree .c7n-tree-treenode .c7n-tree-node-selected .c7n-tree-title{
      color: #33F1FF !important;
    }
  }
}
