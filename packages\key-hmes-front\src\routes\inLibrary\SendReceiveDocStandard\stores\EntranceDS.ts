/**
 * @Description: 库存调拨平台 - 入口页面DS
 * @Author: <EMAIL>
 * @Date: 2022/3/8 10:13
 * @LastEditTime: 2023-05-18 16:18:42
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.inLibrary.sendReceiveDoc';
const tenantId = getCurrentOrganizationId();

// const lugeUrl = '-30607';
const lugeUrl = '';

const headerTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'instructionDocId',
  autoLocateFirst: true,
  queryFields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('调拨单类型'),
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-instruction-doc/operation-type/limit/doc/type/list?operationType=STOCK_TRANSFER_DOC`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_SEND_RECEIVE_EXECUTE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'fromSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromSite`).d('来源站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'fromSiteId',
      type: FieldType.number,
      bind: 'fromSiteLov.siteId',
    },
    {
      name: 'fromWareHouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromWareHouse`).d('来源仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('fromSiteId') || null,
            enableFlag: 'Y',
            locatorCategoryAreaFlag: 'Y',
          };
        },
      },
    },
    {
      name: 'fromWareHouseId',
      type: FieldType.number,
      bind: 'fromWareHouseLov.locatorId',
    },
    {
      name: 'fromLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromLocator`).d('来源库位'),
      lovCode: 'MT.MODEL.SUB_LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorIds: record?.get('fromWareHouseId') || null,
            locatorCategory: ['LOCATION', 'INVENTORY'],
          };
        },
      },
    },
    {
      name: 'fromLocatorId',
      type: FieldType.number,
      bind: 'fromLocatorLov.locatorId',
    },
    {
      name: 'toSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toSite`).d('目标站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'toSiteId',
      type: FieldType.number,
      bind: 'toSiteLov.siteId',
    },
    {
      name: 'toWareHouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toWareHouse`).d('目标仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('toSiteId') || null,
            enableFlag: 'Y',
            locatorCategoryAreaFlag: 'Y',
          };
        },
      },
    },
    {
      name: 'toWareHouseId',
      type: FieldType.number,
      bind: 'toWareHouseLov.locatorId',
    },
    {
      name: 'toLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toLocator`).d('目标库位'),
      lovCode: 'MT.MODEL.SUB_LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorIds: record?.get('toWareHouseId') || null,
            locatorCategory: ['LOCATION', 'INVENTORY'],
          };
        },
      },
    },
    {
      name: 'toLocatorId',
      type: FieldType.number,
      bind: 'toLocatorLov.locatorId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'soNumberLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.soNumber`).d('销单'),
      lovCode: 'MT.SO_NUMBER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'soId',
      type: FieldType.number,
      bind: 'soNumberLov.soId',
    },
    {
      name: 'demandDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandTimeFrom`).d('需求时间从'),
      max: 'demandDateTo',
    },
    {
      name: 'demandDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandTimeTo`).d('需求时间至'),
      min: 'demandDateFrom',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  fields: [
    {
      name: 'instructionDocId',
      type: FieldType.number,
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('调拨单类型'),
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
    },
    {
      name: 'instructionDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('状态'),
    },
    {
      name: 'fromSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromSite`).d('来源站点'),
    },
    {
      name: 'toSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toSite`).d('目标站点'),
    },
    {
      name: 'demandTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandTime`).d('需求时间'),
    },
    {
      name: 'printTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'createByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createByName`).d('创建人'),
    },
    {
      name: 'createDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdDate`).d('创建时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/wms-inventory-send-receive/doc/head/info/list/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.HEAD&operationType=STOCK_TRANSFER_DOC`,
        method: 'GET',
      };
    },
  },
  record: {
    dynamicProps: {
      // 关闭类型的单据不可选择
      selectable: record => !['CANCEL', 'CLOSED'].includes(record?.get('instructionDocStatus')),
    },
  },
});

const lineTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  paging: false,
  dataKey: 'rows',
  cacheSelection: true,
  primaryKey: 'instructionDocLineId',
  queryFields: [
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'fromWareHouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromWareHouse`).d('来源仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'locatorCode',
      valueField: 'locatorId',
    },
    {
      name: 'fromWareHouseId',
      type: FieldType.number,
      bind: 'fromWareHouseLov.locatorId',
    },
    {
      name: 'toWareHouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toWareHouse`).d('目标仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'locatorCode',
      valueField: 'locatorId',
    },
    {
      name: 'toWareHouseId',
      type: FieldType.number,
      bind: 'toWareHouseLov.locatorId',
    },
  ],
  fields: [
    {
      name: 'instructionDocLineId',
      type: FieldType.number,
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    // {
    //   name: 'identifyType',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
    //   lookupCode: 'MT.APS.GEN_TYPE_URL',
    //   lovPara: {
    //     typeGroup: 'IDENTITY_TYPE',
    //     tenantId: getCurrentOrganizationId(),
    //     userId: getCurrentUserId(),
    //   },
    //   valueField: 'typecode',
    //   textField: 'description',
    // },
    {
      name: 'fromIdentifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromIdentifyType`).d('来源管理模式'),
      lookupCode: 'MT.APS.GEN_TYPE_URL',
      lovPara: {
        typeGroup: 'IDENTITY_TYPE',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      valueField: 'typecode',
      textField: 'description',
    },
    {
      name: 'toIdentifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toIdentifyType`).d('目标管理模式'),
      lookupCode: 'MT.APS.GEN_TYPE_URL',
      lovPara: {
        typeGroup: 'IDENTITY_TYPE',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      valueField: 'typecode',
      textField: 'description',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'requiredQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.requiredQty`).d('调拨数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'statusCode',
      type: FieldType.string,
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('状态'),
    },
    {
      name: 'firstExecutedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.firstExecutedQty`).d('已发出'),
    },
    {
      name: 'secondExecutedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.secondExecutedQty`).d('已接收'),
    },
    {
      name: 'fromSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromSite`).d('来源站点'),
    },
    {
      name: 'fromWareHouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromWareHouse`).d('来源仓库'),
    },
    {
      name: 'fromLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromLocator`).d('来源库位'),
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNumber`).d('销单'),
    },
    {
      name: 'soLineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销单行'),
    },
    {
      name: 'toSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toSite`).d('目标站点'),
    },
    {
      name: 'toWareHouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toWareHouse`).d('目标仓库'),
    },
    {
      name: 'toLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toLocator`).d('目标库位'),
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
    },
    {
      name: 'toleranceType',
      type: FieldType.string,
    },
    {
      name: 'toleranceTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差值'),
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差值'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/wms-inventory-send-receive/doc/line/list/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.LINE`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && !datas.success) {
            if (datas.message) {
              notification.error({ message: datas.message });
            }
            return {
              rows: [],
            };
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
});

export { headerTableDS, lineTableDS };
