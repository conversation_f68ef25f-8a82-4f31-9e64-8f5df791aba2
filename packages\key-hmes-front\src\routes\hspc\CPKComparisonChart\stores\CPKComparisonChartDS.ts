/**
 * @Description: CPK对比图-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-04-19 16:09:43
 * @LastEditTime: 2022-11-30 10:31:24
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hspc.CPKComparisonChart';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  autoCreate: true,
  autoQuery: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('时间从'),
      max: 'dateTo',
      required: true,
      defaultValue: new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('chartType') === 'ANALYSIS';
        },
        required: ({ record }) => {
          return record.get('chartType') === 'CONTROL';
        },
      },
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('时间至'),
      min: 'dateFrom',
      required: true,
      defaultValue: new Date(),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('chartType') === 'ANALYSIS';
        },
        required: ({ record }) => {
          return record.get('chartType') === 'CONTROL';
        },
      },
    },
    {
      name: 'xName',
      label: intl.get(`${modelPrompt}.xName`).d('横坐标'),
      lookupCode: 'MT.SPC.CONTROL_ABSCISSA',
      type: FieldType.string,
      required: true,
      defaultValue: 'CONTROL_CODE',
    },
    {
      name: 'chartType',
      label: intl.get(`${modelPrompt}.chartType`).d('控制图类型'),
      lookupCode: 'MT.SPC.TYPE',
      type: FieldType.string,
      required: true,
      defaultValue: 'CONTROL',
    },
    {
      name: 'processObject',
      label: intl.get(`${modelPrompt}.processObject`).d('过程对象'),
      type: FieldType.object,
      lovCode: 'MT.SPC.ANALYSIS_PROCESS_OBJECT',
      dynamicProps: {
        lovCode: ({ record }) => {
          if (record.get('chartType') === 'CONTROL') {
            return 'MT.SPC.CONTROL_PROCESS_OBJECT';
          }
          return 'MT.SPC.ANALYSIS_PROCESS_OBJECT';
        },
      },
    },
    {
      name: 'processObjectId',
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('chartType') === 'CONTROL') {
            return 'processObject.controlId';
          }
          return 'processObject.analysisProcessObjectId';
        },
      },
    },
    {
      name: 'controlLov',
      label: intl.get(`${modelPrompt}.controlLov`).d('控制控制图'),
      lovCode: 'MT.SPC.CONTROL_CHART',
      type: FieldType.object,
      multiple: true,
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        sampleType: 'MEASURE',
      },
      required: true,
      dynamicProps: {
        lovCode: ({ record }) => {
          if (record.get('chartType') === 'CONTROL') {
            return 'MT.SPC.CONTROL_CHART';
          }
          return 'MT.SPC.ANALYSIS_CHART';
        },
        label: ({ record }) => {
          if (record.get('chartType') === 'CONTROL') {
            return intl.get(`${modelPrompt}.controlControlLov`).d('控制控制图');
          }
          return intl.get(`${modelPrompt}.analysisControlLov`).d('分析控制图');
        },
      },
    },
    {
      name: 'controlId',
      bind: 'controlLov.controlId',
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('chartType') === 'CONTROL') {
            return 'controlLov.controlId';
          }
          return 'controlLov.analysisId';
        },
      },
    },
  ],
});

export { detailDS };
