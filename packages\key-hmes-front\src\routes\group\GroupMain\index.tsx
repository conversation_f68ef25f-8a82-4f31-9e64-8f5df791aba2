/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-10-12
 * @description 班组维护
 */
import React, { FC, useEffect, useState } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Badge } from 'hzero-ui';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import {useRequest} from "@components/tarzan-hooks";
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { groupMainDS } from './stories/groupMain';
import './index.module.less';
import {getSynchronizeDevicesApi} from "./services";

const modelPrompt = 'tarzan.group.groupMain';

interface GroupMainProps extends RouteComponentProps {
  dataSet: DataSet;
  customizeTable: any;
}

const GroupMain: FC<GroupMainProps> = ({ dataSet, history, match: { path }, customizeTable }) => {

  const { run: getSynchronizeDevices, loading: getSynchronizeDevicesLoading } = useRequest(getSynchronizeDevicesApi(), { manual: true });


  const columns = [
    {
      name: 'shiftTeamCode',
      lock: 'left',
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => {
              history.push(`/group/group-main/detail/${record.data.shiftTeamId}`);
            }}
          >
            {record.data.shiftTeamCode}
          </a>
        );
      },
    },
    { name: 'shiftTeamName', align: 'left' },
    { name: 'shiftTeamDesc', align: 'left' },
    { name: 'siteCode', align: 'left' },
    { name: 'shiftTeamTypeDesc', align: 'left' },
    { name: 'shiftTeamLeaderName', align: 'left' },
    {
      name: 'enableFlag',
      renderer: ({ record }) => (
        <Badge
          status={record.get('enableFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('enableFlag') === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
      align: 'center',
    },
  ];

  const [currentColumns] = useState(columns); // 实际columns

  const goDetail = () => {
    history.push(`/group/group-main/detail/create`);
  };

  // 同步设备
  const handleSynchronizeDevices = () => {
    return getSynchronizeDevices({
      params: {},
      onSuccess: () => notification.success({}),
    });
  };

  useEffect(() => {
    dataSet.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.SHIFT_TEAM_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.SHIFT_TEAM_LIST.LIST`);

    dataSet.query(dataSet.currentPage).then(r => r);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.group-main`).d('班组维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={goDetail}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除分配组织按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          loading={getSynchronizeDevicesLoading}
          onClick={handleSynchronizeDevices}
          permissionList={[
            {
              code: `group-main.synchronize-devices.button`,
              type: 'button',
              meaning: '列表页-同步设备按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.synchronizeDevices').d('同步设备')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.SHIFT_TEAM_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.SHIFT_TEAM_LIST.LIST`,
          },
          <Table
            searchCode="bzwh1"
            customizedCode="bzwh1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={dataSet}
            columns={currentColumns as ColumnProps[]}
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.group.groupMain', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...groupMainDS(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.SHIFT_TEAM_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.SHIFT_TEAM_LIST.LIST`],
      // @ts-ignore
    })(GroupMain),
  ),
);
