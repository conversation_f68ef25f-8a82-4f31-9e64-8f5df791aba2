import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Spin, Modal } from 'choerodon-ui/pro';
import { Tabs } from 'choerodon-ui';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { AddGrrTaskLine, DeleteGrrTaskLine } from '../services';
// import Grr from './Grr';
// import Linear from "./Linear";
import CGK from "./CGK";

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const TabPane = Tabs.TabPane;

interface GrrTabsProps {
  msaTaskLineId: number;
  checkLocation: string;
}

const CGKTabs = (props, ref) => {
  const { msaTaskId, updateHeaderInfo, msaStatus, currentUserFlag, grrGroupInfo } = props;
  const [activityKey, setActivityKey] = useState('');
  const [tabList, setTabList] = useState<GrrTabsProps[]>([]);

  const { run: addGrrLine, loading: addGrrLoading } = useRequest(AddGrrTaskLine(), { manual: true });
  const { run: deleteGrrLine, loading: deleteGrrLoading } = useRequest(DeleteGrrTaskLine(), { manual: true });

  useImperativeHandle(ref, () => ({
    getCurrentTaskLineInfo: () => {
      const tabInfo = tabList.find((item) => String(item.msaTaskLineId) === activityKey);
      return tabInfo;
    },
  }));

  useEffect(() => {
    if (activityKey === '' && grrGroupInfo?.length) {
      setActivityKey(String(grrGroupInfo[0].msaTaskLineId));
    }
    const _tabList: GrrTabsProps[] = [];
    grrGroupInfo.forEach(item => {
      _tabList.push({
        msaTaskLineId: item.msaTaskLineId,
        checkLocation: item.checkLocation,
      });
    });
    setTabList(_tabList);
  }, [grrGroupInfo, activityKey]);

  const handleEditTabPane = (targetKey, action) => {
    if (action === 'add') {
      handleAddGrrLine();
    } else if (action === 'remove') {
      handleRemoveGrrLine(targetKey);
    }
  };

  const handleAddGrrLine = () => {
    let maxLocation = 1;
    const regex = /^测量位置(\d+)$/;
    tabList.forEach((item) => {
      const match = regex.exec(item.checkLocation);
      if (match && maxLocation < Number(match[1])) {
        maxLocation = Number(match[1]); // 获取匹配到的数字部分
      }
    })
    const _checkLocation = intl.get(`${modelPrompt}.checkLocation`).d('测量位置') + (maxLocation + 1);
    addGrrLine({
      params: {
        checkLocation: _checkLocation,
        msaTaskId,
        msaAnalysisMethod: 'CGK',
      },
      onSuccess: res => {
        setActivityKey(String(res));
        updateHeaderInfo();
      },
    })
  };

  const handleRemoveGrrLine = targetKey => {
    const currentIndex = tabList.findIndex((item) => String(item.msaTaskLineId) === targetKey);
    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <p>
          {intl.get(`${modelPrompt}.tip.confirmDeleteTaskLine`).d('是否确认删除测量点位')} {tabList[currentIndex].checkLocation}?
        </p>
      ),
    }).then(button => {
      if (button === 'ok') {
        handleConfirmRemove(targetKey, currentIndex);
      }
    });
  };

  const handleConfirmRemove = (targetKey, deleteIndex) => {
    deleteGrrLine({
      params: {
        msaTaskLineId: Number(targetKey),
      },
      onSuccess: () => {
        notification.success({});
        setActivityKey(String(tabList[deleteIndex - 1].msaTaskLineId));
        updateHeaderInfo();
      },
    })
  };

  const handleChangeTab = activityKey => {
    setActivityKey(activityKey);
  };

  return (
    <Spin spinning={deleteGrrLoading || addGrrLoading}>
      <Tabs
        activeKey={activityKey}
        onChange={handleChangeTab}
        // @ts-ignore
        type={!['IMPROVING', 'COMPLETED'].includes(msaStatus) && currentUserFlag ? 'editable-card' : 'card'}
        onEdit={handleEditTabPane}
      >
        {tabList.map(item => (
          <TabPane
            tab={item.checkLocation}
            key={String(item.msaTaskLineId)}
            closable={!['IMPROVING', 'COMPLETED'].includes(msaStatus) && currentUserFlag}
          >
            <CGK
              msaTaskLineId={item.msaTaskLineId}
              checkLocation={item.checkLocation}
              updateHeaderInfo={updateHeaderInfo}
              msaStatus={msaStatus}
              currentUserFlag={currentUserFlag}
            />
          </TabPane>
        ))}
      </Tabs>
    </Spin>
  );
};

export default forwardRef(CGKTabs);
