import React, { useMemo, useCallback } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { isNil } from 'lodash';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from '../stores';

const modelPrompt = 'GradingRules';
const tenantId = getCurrentOrganizationId();

const GradingRulesList = observer((props) => {
  const {
    match: { path },
    tableDs,
    history,
  } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'siteCode', width: 160 },
      { name: 'siteName', width: 160 },
      { name: 'materialCode', width: 160 },
      { name: 'materialName', width: 160 },
      { name: 'tagCode', width: 160 },
      { name: 'tagDescription', width: 160 },
      { name: 'ruleSort', width: 120 },
      {
        name: 'enableFlag',
        // width: 100,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? '是'
                : '否'
            }
          />
        ),
      },
      { name: 'createdUsername', width: 120 },
      { name: 'creationDate', width: 150, align: ColumnAlign.center },
      { name: 'lastUpdatedUsername', width: 120 },
      { name: 'lastUpdateDate', width: 150, align: ColumnAlign.center },
      {
        width: 150,
        align: ColumnAlign.center,
        header: intl.get('tarzan.aps.common.button.action').d('操作'),
        renderer: ({ record }) =>
          <Button color={ButtonColor.primary} funcType={FuncType.flat} onClick={() => handleJump2Detail(record)}>
            详情
          </Button>,
        lock: ColumnLock.right,
      },
    ];
  }, []);

  const handleJump2Detail = useCallback((record) => {
    history.push(`/hmes/grading-rules/detail/${record.get('gradingRuleId')}`);
  }, []);

  const handleAdd = useCallback(() => {
    history.push(`/hmes/grading-rules/detail/create`);
  }, []);

  const handlePreviewReport = () => {
    openTab({
      key: `/hmes/commentImport/APEX_MES.GRADING_RULE`,
      title: intl.get(`${modelPrompt}.import`).d('分档规则维护数据导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('分档规则维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button onClick={handlePreviewReport}>{intl.get(`${modelPrompt}.impotButton`).d('导入')}</Button>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-grading-rules/export`}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          queryParams={getExportQueryParams}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="page_searchCode"
          customizedCode="page_customizedCode"
        />
      </Content>
    </div>
  );
})

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(GradingRulesList),
);
