/**
 * @Description: 检验单维护-主界面
 * @Author: <EMAIL>
 * @Date: 2023/2/13 10:43
 */
import { useRequest } from '@components/tarzan-hooks';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { API_HOST, BASIC } from '@utils/config';
import { Badge, Collapse, Tag } from 'choerodon-ui';
import {
  Button,
  DataSet,
  Form,
  Modal,
  Select,
  Table,
} from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import ExcelExport from 'components/ExcelExport';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { isNil } from 'lodash';
import queryString from 'querystring';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { useDataSetEvent } from 'utils/hooks';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { openTab } from 'utils/menuTab';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import withProps from 'utils/withProps';
import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import {
  batchApprovalInfo,
  CancelInspectDoc,
  CloseInspectDoc,
  CompletedInspectDoc,
  CopyInspectDoc, GetDefaultSite,
  initiateNcReviewCalibration,
  initiateNcReviewSave,
  RepairInspectDoc,

} from '../services';
import { drawerDS, headDS, lineDS } from '../stores';

const modelPrompt = 'tarzan.hwms.inspectDocMaintain';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

interface InspectDocListProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
  customizeTable: any;
  location: any;
}

const InspectDocList: FC<InspectDocListProps> = props => {
  const {
    match: { path },
    headDs,
    lineDs,
    history,
    customizeTable,
    location,
  } = props;
  const drawerTableDs = useMemo(() => new DataSet(drawerDS()), []);
  const [selectDocIds, setSelectDocIds] = useState([]);
  const [cancelDisabledFlag, setCancelDisabledFlag] = useState<boolean>(true); // 取消按钮禁用状态
  const [completedDisabledFlag, setCompletedDisabledFlag] = useState<boolean>(true); // 取消按钮禁用状态
  const [copyDisabledFlag, setCopyDisabledFlag] = useState<boolean>(true); // 取消按钮禁用状态
  const [roleFlag, setRoleFlag] = useState<boolean>(false);

  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });

  const { run: querySelfRoles } = useRequest(
    {
      url: `/iam/hzero/v1/member-roles/self-roles?organizationId=0&unionLabel=false`,
      method: 'GET',
    },
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: queryPermissionsList } = useRequest(
    {
      url: `/hpfm/v1/${tenantId}/lovs/value/batch?permissionsList=INSPECT_CLOSED`,
      method: 'GET',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: InitiateNcReviewCalibration, loading: initiateNcReviewCalibrationLoading } = useRequest(
    initiateNcReviewCalibration(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: InitiateNcReviewSave, loading: initiateNcReviewSaveLoading } = useRequest(
    initiateNcReviewSave(),
    {
      manual: true,
      needPromise: true,
      showNotification: false,
    },
  );
  const { run: cancelInspectDoc, loading: cancelLoading } = useRequest(CancelInspectDoc(), {
    manual: true,
  });
  const { run: completedInspectDoc, loading: completedLoading } = useRequest(
    CompletedInspectDoc(),
    {
      manual: true,
    },
  );
  const { run: copyInspectDoc, loading: copyLoading } = useRequest(CopyInspectDoc(), {
    manual: true,
  });
  const { run: BatchApprovalInfo, loading: batchApprovalLoading } = useRequest(
    batchApprovalInfo(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: closeInspectDoc, loading: closeLoading } = useRequest(CloseInspectDoc(), {
    manual: true,
  });
  const { run: repairInspectDoc, loading: repairLoading } = useRequest(RepairInspectDoc(), {
    manual: true,
  });

  useEffect(() => {
    queryPermissionsList({}).then((permissionsRes) => {
      if (permissionsRes && permissionsRes.permissionsList.length > 0) {
        querySelfRoles({}).then((res) => {
          const allRolesId = res.map((item) => Number(item.id));
          const tempFlag = permissionsRes.permissionsList.some((ele) =>
            allRolesId.includes(Number(ele.meaning)),
          );
          setRoleFlag(tempFlag)
        })
      } else {
        setRoleFlag(false)
      }
    })
  }, []);

  useEffect(() => {
    if (location.state?.sourceDocId) {
      headDs?.queryDataSet.current?.set('inspectDocNum', location.state?.sourceDocNum);
    }
    if (location.state?.materialLotCode || location.state?.eoNum) {
      getDefaultSite({
        onSuccess: res => {
          if (res?.siteId) {
            // 从其他功能页面跳转到列表页，需使用路由中的传参，来设置表格查询参数
            const {
              materialLotCode,
              eoNum,
            } = props?.location?.state || {};
            const queryParams = {
              siteLov: res,
              materialLotCode,
              eoNum,
            };
            headDs.queryDataSet.loadData([queryParams]);
            headDs.query(headDs.currentPage).then(r => r);
          }
        },
      });
    }
  }, [location.state]);

  useEffect(() => {
    history.listen(location => {
      if (location.pathname === '/hwms/inspect-doc-maintain/list') {
        if (location.state?.inspectionDocNumber) {
          headDs.queryDataSet.current?.init(
            'inspectDocNum',
            location.state?.inspectionDocNumber?.split(','),
          );
          headDs.setQueryParameter(
            'customizeUnitCode',
            `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.LIST`,
          );
          headDs.query(headDs.currentPage).then(r => r);
        }
      }
    });
    headDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.LIST`,
    );
    headDs.query(headDs.currentPage).then(r => r);
  }, []);

  useDataSetEvent(headDs.queryDataSet, 'update', ({ name, record }) => {
    if (name === 'siteLov') {
      record?.init('inspectBusinessType', undefined);
    }
  });

  const handleUpdateSelect = () => {
    let _cancelDisabledFlag = !headDs.selected?.length;
    let _completedDisabledFlag = !headDs.selected?.length;
    let _copyDisabledFlag = headDs.selected?.length !== 1;
    const _selectDocIds: any = [];
    headDs.selected.forEach(_record => {
      _selectDocIds.push(_record.get('inspectDocId'));
      if (!['NEW', 'RELEASED'].includes(_record.get('inspectDocStatus'))) {
        _cancelDisabledFlag = true;
      }
      if (!['FIRST_COMPLETED'].includes(_record.get('inspectDocStatus'))) {
        _completedDisabledFlag = true;
      }
      if (['NEW', 'CANCEL'].includes(_record.get('inspectDocStatus'))) {
        _copyDisabledFlag = true;
      }
    });
    setSelectDocIds(_selectDocIds);
    setCancelDisabledFlag(_cancelDisabledFlag);
    setCompletedDisabledFlag(_completedDisabledFlag);
    setCopyDisabledFlag(_copyDisabledFlag);
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    handleUpdateSelect();
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('inspectDocId'));
    } else {
      queryLineTable(null);
    }
  };

  useDataSetEvent(headDs, 'batchSelect', handleUpdateSelect);
  useDataSetEvent(headDs, 'batchUnSelect', handleUpdateSelect);
  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = inspectDocId => {
    lineDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.TASK_LIST`,
    );
    if (inspectDocId) {
      lineDs.setQueryParameter('inspectDocId', inspectDocId);
    } else {
      lineDs.setQueryParameter('inspectDocId', 0);
    }
    lineDs.query();
  };

  const handleCancelInspectDoc = () => {
    cancelInspectDoc({
      params: selectDocIds,
      onSuccess: () => {
        notification.success({});
        headDs.query(headDs.currentPage);
      },
    });
  };

  const handleCompletedInspectDoc = () => {
    completedInspectDoc({
      params: { inspectDocIds: selectDocIds, status: 'COMPLETED' },
      onSuccess: () => {
        notification.success({});
        headDs.query(headDs.currentPage);
      },
    });
  };

  const handleCopyInspectDoc = () => {
    copyInspectDoc({
      params: { inspectDocId: selectDocIds[0] },
      onSuccess: res => {
        notification.success({});
        history.push(`/hwms/inspect-doc-maintain/dist/${res?.inspectDocId}`);
      },
    });
  };

  // 关闭工单
  const handleCloseInspectDoc = () => {
    closeInspectDoc({
      params: {
        inspectionAccTag: 'Y',
        inspectDocIdList: selectDocIds,
      },
      onSuccess: () => {
        notification.success({ message: '操作成功' });
        headDs.query(headDs.currentPage);
      },
    });
  };

  // 返修工单
  const handleRepairInspectDoc = () => {
    closeInspectDoc({
      params: {
        inspectionAccTag: 'N',
        inspectDocIdList: selectDocIds,
      },
      onSuccess: () => {
        notification.success({ message: '操作成功' });
        headDs.query(headDs.currentPage);
      },
    });
  };

  const handleOpenDrawer = inspectDocId => {
    drawerTableDs.setQueryParameter('inspectDocId', inspectDocId);
    drawerTableDs.query();
    Modal.open({
      ...drawerPropsC7n({
        ds: drawerTableDs,
        canEdit: false,
      }),
      title: intl.get(`${modelPrompt}.title.inspectObjectDetail`).d('报检明细'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      children: (
        <Table
          dataSet={drawerTableDs}
          columns={detailColumns}
          customizedCode="inspectDocMaintain-drawer"
        />
      ),
    });
  };

  const renderTag = (value, record) => {
    switch (record.get('inspectDocStatus')) {
      case 'NEW':
      case 'RELEASED':
        return <Tag color="green">{value}</Tag>;
      case 'INSPECTING':
      case 'REINSPECTING':
        return <Tag color="yellow">{value}</Tag>;
      case 'LAST_COMPLETED':
        return <Tag color="geekblue">{value}</Tag>;
      case 'FIRST_COMPLETED':
        return <Tag color="blue">{value}</Tag>;
      default:
        return <Tag color="gray">{value}</Tag>;
    }
  };

  const detailColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'sequence', width: 100 },
      { name: 'objectTypeDesc', width: 120 },
      { name: 'objectCode', width: 200 },
      { name: 'quantity', width: 120 },
      { name: 'uomName', width: 100 },
      { name: 'locatorName' },
      { name: 'lot' },
      { name: 'supplierLot' },
      { name: 'inspectInfoCode', width: 120 },
    ];
  }, []);

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectDocNum',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hwms/inspect-doc-maintain/dist/${record!.get('inspectDocId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'inspectDocStatusDesc',
        renderer: ({ value, record }) => renderTag(value, record),
      },
      { name: 'warehouseName', width: 150, align: ColumnAlign.center },
      {
        name: 'closedFlag',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('closedFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('closedFlag') === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      { name: 'inspectBusinessTypeDesc', width: 150 },
      { name: 'ncrCode', width: 150, align: ColumnAlign.center },
      {
        name: 'urgentFlag',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('urgentFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('urgentFlag') === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'inspectInfoUserNames',
        width: 180,
        renderer: ({ value }) => {
          if (value && value.length) {
            const set = new Set(value);
            return Array.from(set).join(',');
          }
          return null;
        },
      },
      { name: 'creationDate', width: 150, align: ColumnAlign.center },
      { name: 'materialName' },
      { name: 'materialCode' },
      { name: 'customerName' },
      { name: 'supplierName' },
      { name: 'processWorkcellName' },
      { name: 'equipmentName' },
      { name: 'siteName' },
      { name: 'inspectSchemeCode', width: 150 },
      { name: 'docCreateMethodDesc', width: 150 },
      { name: 'firstInspectResultDesc' },
      { name: 'lastInspectResultDesc' },
      {
        name: 'inspectInfoCodes',
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push({
                  pathname: `/hwms/inspection-info-management/list`,
                  state: {
                    inspectInfoCodes: record?.get('inspectInfoCodes'),
                  },
                });
              }}
            >
              {value?.join(',')}
            </a>
          );
        },
      },
      { name: 'actualStartTime', width: 150, align: ColumnAlign.center },
      { name: 'actualEndTime', width: 150, align: ColumnAlign.center },
      { name: 'reviewStatusDesc' },
      { name: 'reviewUserName' },
      { name: 'reviewTime', width: 150, align: ColumnAlign.center },
      { name: 'revisionCode' },
      { name: 'stationWorkcellName' },
      { name: 'operationName' },
      { name: 'areaName' },
      { name: 'prodLineName' },
      { name: 'locatorName' },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 150,
        renderer: ({ record }) => (
          <a onClick={() => handleOpenDrawer(record!.get('inspectDocId'))}>
            {intl.get(`${modelPrompt}.action.inspectObjectDetail`).d('报检明细')}
          </a>
        ),
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectTaskCode',
        width: 150,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                if (record?.get('operatingEnd') === 'PC-2') {
                  history.push(
                    `/hwms/inspection-platform-workshop/dist/${record?.get('inspectTaskId')}`,
                  );
                } else {
                  history.push(`/hwms/inspection-platform/dist/${record?.get('inspectTaskId')}`);
                }
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'inspectTaskStatusDesc', width: 120 },
      { name: 'inspectTaskTypeDesc', width: 120 },
      { name: 'inspectResult' },
      { name: 'inspectorName' },
      {
        name: 'sourceTaskId',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('sourceTaskId') > 0 ? 'success' : 'error'}
            text={
              record?.get('sourceTaskId') > 0
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'closedFlag',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('closedFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('closedFlag') === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      { name: 'outsourceSupplierName' },
      { name: 'actualStartTime', width: 150, align: ColumnAlign.center },
      { name: 'actualEndTime', width: 150, align: ColumnAlign.center },
      { name: 'scrapQty' },
      { name: 'okQty' },
      { name: 'ngQty' },
      { name: 'sourceTaskCode' },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    history.push(`/hwms/inspect-doc-maintain/dist/create`);
  }, []);

  const headerRowClick = record => {
    queryLineTable(record?.get('inspectDocId'));
  };

  // 处理导出按钮使用的查询参数
  const getExportQueryParams = () => {
    if (!headDs.queryDataSet || !headDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = headDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
    };
  };

  const handleApproval = async () => {
    const res = await BatchApprovalInfo({
      params: headDs.selected.map(record => record?.get('inspectDocId')),
    });
    if (res?.success) {
      notification.success({});
      headDs.query();
    }
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/MT.INSPECT_ITEM_VALUE_IMPORT',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  // const handleChangeStatus = type => {};

  // 发起不良审批
  const handleNcPreview = async () => {
    const res = await InitiateNcReviewCalibration({
      params: { inspectDocIds: headDs.selected.map(record => record?.get('inspectDocId')) },
    });
    if (res?.success) {
      const ds = new DataSet({
        autoQuery: false,
        autoCreate: true,
        fields: [
          {
            name: 'factory',
            label: '工厂',
            type: FieldType.string,
            lookupCode: 'QMS_PLANT',
            required: true,
          },
          {
            name: 'materialCategory',
            label: '物料类别',
            type: FieldType.string,
            lookupCode: 'QMS_ITEM_CLASS',
            required: true,
          },
        ],
      });

      Modal.open({
        title: '发起不良',
        closable: true,
        children: (
          <Form dataSet={ds}>
            <Select name="factory" />
            <Select name="materialCategory" />
          </Form>
        ),
        okText: '保存',
        onOk: async () => {
          const validate = await ds.validate();
          if (!validate) {
            return false;
          }
          // 新增弹框
          const tableDs = new DataSet({
            autoQuery: false,
            selection: false,
            fields: [
              {
                name: 'inspectDocNum',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单编码'),
              },
              {
                name: 'processCode',
                label: '工序',
                type: FieldType.string,
                lookupCode: 'QMS_OA_OPERATION',
              },
              {
                name: 'isMaterialProblem',
                label: '是否涉及原材料',
                type: FieldType.string,
                lookupCode: 'QMS_OA_MA',
              },
              {
                name: 'remark',
                label: '备注',
                type: FieldType.string,
              },
            ],
          });

          const data = headDs.selected.map(e => e.toData());
          tableDs.loadData(data);

          const columns = [
            {
              name: 'inspectDocNum',
            },
            {
              name: 'processCode',
              editor: true,
            },
            {
              name: 'isMaterialProblem',
              editor: true,
            },
            {
              name: 'remark',
              editor: true,
            },
          ];

          // 打开弹框
          Modal.open({
            title: '发起不良',
            closable: true,
            children: <Table dataSet={tableDs} columns={columns} />,
            style: {
              width: 800,
            },
            okText: '保存并发起',
            onOk: async () => {
              const factoryInfo = ds.current?.toData();
              const inspectDocDTOList = tableDs?.toData();
              // 调用后端接口
              const res = await InitiateNcReviewSave({
                params: {
                  platformType: "PC",
                  ...factoryInfo,
                  inspectDocDTOList,
                },
              });
              if (res?.success) {
                notification.success({ message: '操作成功！' });
                headDs.query(headDs.currentPage).then(r => r);
              } else {
                notification.error({ message: res.message || '操作失败！' });
                return false;
              }
            },
          });
        },
      });
    }
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('检验单管理')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          onClick={handleCloseInspectDoc}
          loading={closeLoading}
          disabled={
            !(
              headDs.selected.length === 1 &&
              headDs.selected.every(
                e => e.get('inspectDocStatus') === 'LAST_COMPLETED' && e.get('closedFlag') !== 'Y',
              ) &&
              roleFlag
            )
          }
          permissionList={[
            {
              code: `hzero.tarzan.hlct.inspect_doc_maintain.ps.button.close`,
              type: 'button',
              meaning: '列表页-关闭',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.close`).d('关闭')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          onClick={handleRepairInspectDoc}
          loading={repairLoading}
          disabled={
            !(
              headDs.selected.length === 1 &&
              headDs.selected.every(
                e => e.get('inspectDocStatus') === 'LAST_COMPLETED' && e.get('closedFlag') !== 'Y',
              ) &&
              roleFlag
            )
          }
          permissionList={[
            {
              code: `hzero.tarzan.hlct.inspect_doc_maintain.ps.button.repair`,
              type: 'button',
              meaning: '列表页-返工',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.repair`).d('返工')}
        </PermissionButton>
        {/* <Dropdown
          overlay={
            <Menu style={{ width: '100px' }} onClick={handleChangeStatus}>
              <Menu.Item key="CANCEL">
                <a disabled={cancelDisabledFlag} onClick={handleCancelInspectDoc}>
                  {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
                </a>
              </Menu.Item>
              <Menu.Item key="COMPLETED">
                <a disabled={completedDisabledFlag} onClick={handleCompletedInspectDoc}>{intl.get(`${modelPrompt}.button.completed`).d('完成')}</a>
              </Menu.Item>
            </Menu>
          }
          trigger={[Action.click]}
        >
          <PermissionButton
            type="c7n-pro"
            disabled={cancelDisabledFlag && completedDisabledFlag}
            loading={cancelLoading || completedLoading}
          >
            {intl.get(`${modelPrompt}.changeStatus`).d('状态变更')} <Icon type="expand_more" />
          </PermissionButton>
        </Dropdown> */}
        <PermissionButton
          type="c7n-pro"
          disabled={cancelDisabledFlag}
          onClick={handleCancelInspectDoc}
          loading={cancelLoading}
          permissionList={[
            {
              code: `list.button.cancel`,
              type: 'button',
              meaning: '列表页-取消',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </PermissionButton>
        <Button onClick={handleCopyInspectDoc} disabled={copyDisabledFlag} loading={copyLoading}>
          {intl.get('tarzan.common.button.copy').d('复制')}
        </Button>
        {/* <Button
          onClick={handleApproval}
          loading={batchApprovalLoading}
          disabled={!headDs.selected.length}
        >
          {intl.get(`${modelPrompt}.batch.approval`).d('批量合格')}
        </Button> */}
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          otherButtonProps={{
            disabled: true,
          }}
        />
        <PermissionButton
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
          permissionList={[
            {
              code: `${path}.button.valueImport`,
              type: 'button',
              meaning: '列表页-检测值导入',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.valueImport`).d('检测值导入')}
        </PermissionButton>
        <Button
          onClick={handleNcPreview}
          disabled={
            !(
              headDs.selected.length > 0 &&
              headDs.selected.every(
                e =>
                  e.get('inspectDocStatus') === 'LAST_COMPLETED' &&
                  e.get('reviewStatus') === 'UNREVIEWED',
              )
            )
          }
        >
          {intl.get(`${modelPrompt}.batch.nc.preview`).d('发起不良评审')}
        </Button>
        <TemplatePrintButton
          disabled={!headDs.selected.length}
          printButtonCode="INSPECT_DOC_PRINT"
          printParams={{
            docId: headDs.selected.map(record => record?.get('inspectDocId')).join(','),
          }}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              queryFieldsLimit: 17,
            }}
            dataSet={headDs}
            columns={headColumns}
            searchCode="inspectDocMaintain1"
            customizedCode="inspectDocMaintain-listHeader"
            onRow={({ record }) => ({
              onClick: () => headerRowClick(record),
            })}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['taskInfo']}>
          <Panel
            key="taskInfo"
            header={intl.get(`${modelPrompt}.title.taskInfo`).d('检验任务信息')}
          >
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.TASK_LIST`,
              },
              <Table
                dataSet={lineDs}
                columns={lineColumns}
                customizedCode="inspectDocMaintain-listLine"
              />,
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.LIST`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.TASK_LIST`,
      ],
    })(InspectDocList as any),
  ),
);
