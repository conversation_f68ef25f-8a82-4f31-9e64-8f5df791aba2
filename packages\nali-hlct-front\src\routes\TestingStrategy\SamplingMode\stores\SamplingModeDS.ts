/**
 * @Description: 抽样方式 DS
 * @Author: <<EMAIL>>
 * @Date: 2022-12-27 16:23:37
 * @LastEditTime: 2023-03-29 16:58:46
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.sampling.samplingMethod.samplingMode';
const tenantId = getCurrentOrganizationId();

const listTableDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-sampling-method-maintenance/query`,
        method: 'get',
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingMethodCode`).d('抽样方式编码'),
      name: 'samplingMethodCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingMethodDesc`).d('抽样方式描述'),
      name: 'samplingMethodDesc',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingType`).d('抽样类型'),
      name: 'samplingType',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=SAMPLING_TYPE`,
      // @ts-ignore
      noCache: true,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      name: 'samplingMethodCode',
      label: intl.get(`${modelPrompt}.samplingMethodCode`).d('抽样方式编码'),
    },
    {
      type: FieldType.string,
      name: 'samplingMethodDesc',
      label: intl.get(`${modelPrompt}.samplingMethodDesc`).d('抽样方式描述'),
    },
    {
      type: FieldType.string,
      name: 'samplingTypeDesc',
      label: intl.get(`${modelPrompt}.samplingType`).d('抽样类型'),
    },
    {
      type: FieldType.number,
      name: 'samplingMethodValue',
      label: intl.get(`${modelPrompt}.samplingMethodValue`).d('抽样方式参数值'),
    },
    {
      type: FieldType.string,
      name: 'processMode',
      label: intl.get(`${modelPrompt}.processMode`).d('尾数处理模式'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=DECIMAL_PROCESS_MODE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'insObjectBarcodeNumDimFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.insObjectBarcodeNumDimFlag`).d('计算维度：报检条码个数'),
    },
    {
      type: FieldType.string,
      name: 'samplingStandard',
      label: intl.get(`${modelPrompt}.samplingStandard`).d('抽样标准'),
      lookupCode: 'MT.SAMPLING.STANDARD',
      lovPara: {
        tenantId,
      },
    },
    {
      type: FieldType.string,
      name: 'samplingPlanType',
      label: intl.get(`${modelPrompt}.samplingPlanType`).d('抽样方案类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=SAMPLING_PLAN_TYPE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      type: FieldType.string,
      name: 'strictness',
      label: intl.get(`${modelPrompt}.strictness`).d('严格度'),
      lookupCode: 'MT.SAMPLING.STRICTNESS',
      lovPara: {
        tenantId,
      },
    },
    {
      type: FieldType.string,
      name: 'aql',
      label: intl.get(`${modelPrompt}.aql`).d('AQL'),
      lookupCode: 'MT.SAMPLING.QUALITY.LIMIT',
      lovPara: {
        tenantId,
      },
    },
    {
      type: FieldType.string,
      name: 'inspectionLevel',
      label: intl.get(`${modelPrompt}.inspectionLevel`).d('检验水平'),
      lookupCode: 'MT.SAMPLING.INSPECTION.LEVEL',
      lovPara: {
        tenantId,
      },
    },
    {
      type: FieldType.number,
      name: 'acceptQty',
      label: intl.get(`${modelPrompt}.acceptQty`).d('接收数'),
    },
    {
      type: FieldType.number,
      name: 'rejectQty',
      label: intl.get(`${modelPrompt}.rejectQty`).d('拒绝数'),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

const detailFormDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      name: 'samplingMethodId',
      type: FieldType.number,
    },
    {
      type: FieldType.string,
      name: 'samplingMethodCode',
      label: intl.get(`${modelPrompt}.samplingMethodCode`).d('抽样方式编码'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('samplingMethodId');
        },
      },
    },
    {
      type: FieldType.string,
      name: 'samplingMethodDesc',
      label: intl.get(`${modelPrompt}.samplingMethodDesc`).d('抽样方式描述'),
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingType`).d('抽样类型'),
      name: 'samplingType',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=SAMPLING_TYPE`,
      // @ts-ignore
      noCache: true,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
      dynamicProps: {
        disabled: ({ record }) => record.get('samplingMethodId'),
      },
    },
    {
      type: FieldType.number,
      name: 'samplingMethodValue',
      label: intl.get(`${modelPrompt}.samplingMethodValue`).d('抽样方式参数值'),
      min: 0,
      step: 1,
      nonStrictStep: true,
      dynamicProps: {
        precision: ({ record }) => {
          if (record.get('samplingType') === 'FIXED_VALUE_SAMPLING') {
            return 0;
          }
        },
        disabled: ({ record }) => {
          return (
            ['PERCENTAGE_SAMPLING', 'FIXED_VALUE_SAMPLING'].indexOf(record.get('samplingType')) ===
            -1
          );
        },
        required: ({ record }) => {
          return (
            ['PERCENTAGE_SAMPLING', 'FIXED_VALUE_SAMPLING'].indexOf(record.get('samplingType')) > -1
          );
        },
      },
    },
    {
      type: FieldType.string,
      name: 'processMode',
      label: intl.get(`${modelPrompt}.processMode`).d('尾数处理模式'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=DECIMAL_PROCESS_MODE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(
            [
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'FIXED_VALUE_SAMPLING',
              'NATIONAL_STANDARD_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
        required: ({ record }) => {
          return (
            [
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'FIXED_VALUE_SAMPLING',
              'NATIONAL_STANDARD_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
      },
    },
    {
      type: FieldType.string,
      name: 'samplingStandard',
      label: intl.get(`${modelPrompt}.samplingStandard`).d('抽样标准'),
      lookupCode: 'MT.SAMPLING.STANDARD',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.string,
      name: 'samplingPlanType',
      label: intl.get(`${modelPrompt}.samplingPlanType`).d('抽样方案类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=SAMPLING_PLAN_TYPE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.string,
      name: 'strictness',
      label: intl.get(`${modelPrompt}.strictness`).d('严格度'),
      lookupCode: 'MT.SAMPLING.STRICTNESS',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.string,
      name: 'aql',
      label: intl.get(`${modelPrompt}.aql`).d('AQL'),
      lookupCode: 'MT.SAMPLING.QUALITY.LIMIT',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.string,
      name: 'inspectionLevel',
      label: intl.get(`${modelPrompt}.inspectionLevel`).d('检验水平'),
      lookupCode: 'MT.SAMPLING.INSPECTION.LEVEL',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.number,
      name: 'acceptQty',
      label: intl.get(`${modelPrompt}.acceptQty`).d('接收数'),
      min: 0,
      step: 1,
      precision: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !(
            [
              'FIXED_VALUE_SAMPLING',
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'USER_DEFINED_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
        required: ({ record }) => {
          return (
            [
              'FIXED_VALUE_SAMPLING',
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'USER_DEFINED_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
      },
    },
    {
      type: FieldType.number,
      name: 'rejectQty',
      label: intl.get(`${modelPrompt}.rejectQty`).d('拒绝数'),
      step: 1,
      precision: 0,
      validator: (value, _, record: any) => {
        if (!record.get('acceptQty') || record.get('acceptQty') === value - 1) {
          return true;
        }
        return intl.get(`${modelPrompt}.quantityValidate`).d('拒绝数必须大于接收数，且只可相差1!');
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(
            [
              'FIXED_VALUE_SAMPLING',
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'USER_DEFINED_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
        required: ({ record }) => {
          return (
            [
              'FIXED_VALUE_SAMPLING',
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'USER_DEFINED_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'insObjectBarcodeNumDimFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.insObjectBarcodeNumDimFlag`).d('计算维度：报检条码个数'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'upperLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleSizeUpperLimit`).d('样本量上限值'),
      min: 'lowerLimit',
      step: 1,
      precision: 0,
    },
    {
      name: 'lowerLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleSizeLowerLimit`).d('样本量下限值'),
      min: 0,
      max: 'upperLimitq',
      step: 1,
      precision: 0,
    },
    {
      name: 'decimalProcessMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.decimalProcessMode`).d('样本量位数处理'),
      lookupCode: 'HPFM.DECIMAL_PROCESS_MODE',
    },
    {
      name: 'apartFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.apartFlag`).d('分批标识'),
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
  ],
});

const detailTableDS = (): DataSetProps => ({
  forceValidate: true,
  selection: false,
  paging: false,
  fields: [
    {
      type: FieldType.number,
      name: 'lowerLimit',
      label: intl.get(`${modelPrompt}.lowerLimit`).d('下限'),
      disabled: true,
      min: 0,
    },
    {
      type: FieldType.number,
      name: 'upperLimit',
      label: intl.get(`${modelPrompt}.upperLimit`).d('上限'),
      min: 'lowerLimit',
      help: intl.get(`${modelPrompt}.help.upperLimit`).d('若上限为空，则表示无穷大'),
      dynamicProps: {
        required: ({ dataSet, record }) => {
          return dataSet.totalCount - 1 !== record.index;
        },
      },
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingType`).d('抽样类型'),
      name: 'samplingType',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=SAMPLING_TYPE`,
      // @ts-ignore
      noCache: true,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      type: FieldType.number,
      name: 'samplingMethodValue',
      label: intl.get(`${modelPrompt}.samplingMethodValue`).d('抽样方式参数值'),
      min: 0,
      step: 1,
      nonStrictStep: true,
      dynamicProps: {
        precision: ({ record }) => {
          if (record.get('samplingType') === 'FIXED_VALUE_SAMPLING') {
            return 0;
          }
        },
        disabled: ({ record }) => {
          return (
            ['PERCENTAGE_SAMPLING', 'FIXED_VALUE_SAMPLING'].indexOf(record.get('samplingType')) ===
            -1
          );
        },
        required: ({ record }) => {
          return (
            ['PERCENTAGE_SAMPLING', 'FIXED_VALUE_SAMPLING'].indexOf(record.get('samplingType')) > -1
          );
        },
      },
    },
    {
      type: FieldType.string,
      name: 'processMode',
      label: intl.get(`${modelPrompt}.processMode`).d('尾数处理模式'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=DECIMAL_PROCESS_MODE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(
            [
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'FIXED_VALUE_SAMPLING',
              'NATIONAL_STANDARD_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
        required: ({ record }) => {
          return (
            [
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'FIXED_VALUE_SAMPLING',
              'NATIONAL_STANDARD_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
      },
    },
    {
      type: FieldType.string,
      name: 'samplingStandard',
      label: intl.get(`${modelPrompt}.samplingStandard`).d('抽样标准'),
      lookupCode: 'MT.SAMPLING.STANDARD',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.string,
      name: 'samplingPlanType',
      label: intl.get(`${modelPrompt}.samplingPlanType`).d('抽样方案类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=SAMPLING_PLAN_TYPE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.string,
      name: 'strictness',
      label: intl.get(`${modelPrompt}.strictness`).d('严格度'),
      lookupCode: 'MT.SAMPLING.STRICTNESS',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.string,
      name: 'aql',
      label: intl.get(`${modelPrompt}.aql`).d('AQL'),
      lookupCode: 'MT.SAMPLING.QUALITY.LIMIT',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.string,
      name: 'inspectionLevel',
      label: intl.get(`${modelPrompt}.inspectionLevel`).d('检验水平'),
      lookupCode: 'MT.SAMPLING.INSPECTION.LEVEL',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1);
        },
        required: ({ record }) => {
          return ['NATIONAL_STANDARD_SAMPLING'].indexOf(record.get('samplingType')) > -1;
        },
      },
    },
    {
      type: FieldType.number,
      name: 'acceptQty',
      label: intl.get(`${modelPrompt}.acceptQty`).d('接收数'),
      min: 0,
      step: 1,
      precision: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !(
            [
              'FIXED_VALUE_SAMPLING',
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'USER_DEFINED_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
        required: ({ record }) => {
          return (
            [
              'FIXED_VALUE_SAMPLING',
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'USER_DEFINED_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
      },
    },
    {
      type: FieldType.number,
      name: 'rejectQty',
      label: intl.get(`${modelPrompt}.rejectQty`).d('拒绝数'),
      min: 0,
      step: 1,
      precision: 0,
      validator: (value, _, record: any) => {
        if (!record.get('acceptQty') || record.get('acceptQty') === value - 1) {
          return true;
        }
        return intl.get(`${modelPrompt}.quantityValidate`).d('拒绝数必须大于接收数，且只可相差1!');
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !(
            [
              'FIXED_VALUE_SAMPLING',
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'USER_DEFINED_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
        required: ({ record }) => {
          return (
            [
              'FIXED_VALUE_SAMPLING',
              'PERCENTAGE_SAMPLING',
              'FULL_SAMPLING',
              'USER_DEFINED_SAMPLING',
            ].indexOf(record.get('samplingType')) > -1
          );
        },
      },
    },
  ],
});

export { detailFormDS, detailTableDS, listTableDS };
