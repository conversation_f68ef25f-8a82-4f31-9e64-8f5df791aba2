.operationPlatformEnter {
  display: contents;
  :global {
    .page-head {
      background-color: #38708f !important;
    }
    .page-container {
      border-radius: 0px !important;
    }
    .page-content-wrap {
      margin: 0 !important;
    }
    .page-head.page-head .page-head-title {
      color: rgba(1, 225, 239, 1) !important;
      padding-left: 50% !important;
      position: absolute !important;
      font-size: 20 !important;
      font-weight: 700 !important;
    }
  }
}
.operationPlatformEnterModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }
    .c7n-pro-modal-title {
      color: white !important;
    }
  }
}

.enterModalForm {
  :global {
    .c7n-pro-field-label {
      color: white !important;
      font-size: 18px;
    }
    .c7n-pro-output-wrapper{
      color: white !important;
    }
    .c7n-pro-input {
      color: white !important;
      // border-color: #50819c !important;
    }
    .c7n-pro-input-wrapper {
      background: #50819c !important;
    }
    .c7n-pro-radio-wrapper {
      color: white !important;
      background: #50819c !important;
    }
    .c7n-pro-radio-inner {
      color: white !important;
      background: #50819c !important;
    }
  }
}
