/**
 * @Description: 检验单维护-详情页 检验任务信息DS
 * @Author: <EMAIL>
 * @Date: 2023/2/17 15:00
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.inspectDocMaintain';
const tenantId = getCurrentOrganizationId();

const taskInfoDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-task/doc-task/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const data = JSON.parse(val);
          const { rows } = data;
          // 处理接口报错
          if (data && !data.success) {
            return {
              rows: [],
            };
          }
          rows?.forEach(item => {
            item.displayQty =
              !item?.okQty && !item?.ngQty && !item?.scrapQty
                ? ''
                : `${item?.okQty || ' '}/${item?.ngQty || ' '}/${item?.scrapQty || ' '}`;
          });
          return rows;
        },
      };
    },
  },
  fields: [
    {
      name: 'inspectTaskId',
      type: FieldType.number,
    },
    {
      name: 'inspectTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.inspectTaskCode`).d('检验任务编号'),
    },
    {
      name: 'inspectTaskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.inspectTaskStatus`).d('检验任务状态'),
    },
    {
      name: 'inspectTaskStatusDesc',
      type: FieldType.string,
    },
    {
      name: 'inspectTaskTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.inspectTaskType`).d('检验任务类别'),
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.inspectorName`).d('检验员'),
    },
    {
      name: 'actualStartTime',
      type: FieldType.string,
    },
    {
      name: 'actualEndTime',
      type: FieldType.string,
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.inspectResult`).d('检验结果'),
    },
    {
      name: 'sourceInspectTask',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.sourceTaskCode`).d('复检 关联任务'),
    },
    {
      name: 'displayQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.displayQty`).d('合格品数 不合格品数 报废数'),
    },
    {
      name: 'okQty',
      type: FieldType.string,
    },
    {
      name: 'ngQty',
      type: FieldType.string,
    },
    {
      name: 'scrapQty',
      type: FieldType.string,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.remark`).d('备注'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.inspectTask.enclosure`).d('附件'),
      bucketName: 'qms',
    },
  ],
});

const taskLineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: true,
  dataKey: 'rows',
  selection: false,
  paging: false,
  primaryKey: 'inspectItemId',
  fields: [
    {
      name: 'inspectItemId',
      type: FieldType.number,
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.taskLine.sequence`).d('序号'),
    },
    {
      name: 'inspectItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectItemCode`).d('检验项目编码'),
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectItemDesc`).d('检验项目描述'),
    },
    {
      name: 'inspectItemTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectItemType`).d('检验项目类型'),
    },
    {
      name: 'inspectGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectGroupDesc`).d('检验项目组描述'),
    },
    {
      name: 'itemSourceCreateDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectCreateSource`).d('检验项目创建来源'),
    },
    {
      name: 'okQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.taskLine.okQty`).d('合格数'),
    },
    {
      name: 'ngQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.taskLine.ngQty`).d('不合格数'),
    },
    {
      name: 'inspectResultDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectResultDesc`).d('检测结果'),
    },
    {
      name: 'inspectValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectValue`).d('检测值'),
    },
    {
      name: 'actRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.actRemark`).d('检测备注'),
    },
    {
      name: 'actEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.taskLine.actEnclosure`).d('检验附件'),
      bucketName: 'qms',
    },
    {
      name: 'lineEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.taskLine.lineEnclosure`).d('项目附件'),
      bucketName: 'qms',
    },
    {
      name: 'inspectBasis',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectBasis`).d('检验依据'),
    },
    {
      name: 'inspectMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectMethodDesc`).d('检验方法'),
    },
    {
      name: 'technicalRequirement',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.technicalRequirement`).d('技术要求'),
    },
    {
      name: 'inspectToolDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectTool`).d('检验工具'),
    },
    {
      name: 'qualityCharacteristicDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'dataTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.dataTypeDesc`).d('数据类型'),
    },
    {
      name: 'trueValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.trueValue`).d('符合值'),
    },
    {
      name: 'falseValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.falseValue`).d('不符合值'),
    },
    {
      name: 'warningValueList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.warningValue`).d('预警值'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.uomName`).d('单位'),
    },
    {
      name: 'decimalNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.decimalNumber`).d('小数位数'),
    },
    {
      name: 'processModeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.processModeDesc`).d('尾数处理'),
    },
    {
      name: 'enterMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.enterMethodDesc`).d('录入方式'),
    },
    {
      name: 'requiredFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.requiredFlag`).d('必填项目标识'),
    },
    {
      name: 'dataQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.dataQty`).d('记录值个数'),
    },
    {
      name: 'formula',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.formula`).d('计算公式'),
    },
    {
      name: 'samplingMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.samplingMethodDesc`).d('抽样方式'),
    },
    {
      name: 'samplingQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.samplingQty`).d('抽样数量'),
    },
    {
      name: 'acceptStandardAc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.acceptStandardAc`).d('接收数'),
    },
    {
      name: 'acceptStandardRe',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.acceptStandardRe`).d('拒绝数'),
    },
    {
      name: 'sameGroupIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.sameGroupIdentification`).d('同组标识'),
    },
    {
      name: 'destructiveExperimentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.destructiveExperimentFlag`).d('破坏性标识'),
    },
    {
      name: 'outsourceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.outsourceFlag`).d('委外检验表示'),
    },
    {
      name: 'actionItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.actionItem`).d('行动项'),
    },
    {
      name: 'employeePosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.employeePosition`).d('检测人员岗位'),
    },
    {
      name: 'inspectFrequencyDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.inspectFrequency`).d('检测频率'),
    },
    {
      name: 'm',
      type: FieldType.string,
    },
    {
      name: 'n',
      type: FieldType.string,
    },
    {
      name: 'ncGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.ncGroupDesc`).d('不良代码组'),
    },
    {
      name: 'lineRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLine.lineRemark`).d('项目备注'),
    },
    {
      name: 'defaultValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.defaultValue`).d('默认值'),
    },
    {
      name: 'dataQtyDispositionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectTask.dataQtyDisposition`).d('记录值个数配置'),
    },
  ],
});

export { taskInfoDS, taskLineDS };
