/**
 * @Description: 不良代码维护详情DS
 * @Author: <<EMAIL>>
 * @Date: 2022-07-12 10:39:28
 * @LastEditTime: 2023-05-11 14:55:45
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'hzero-front/src/utils/notification';

const modelPrompt = 'tarzan.badCode.defectCode.model.defectCode';
const tenantId = getCurrentOrganizationId();

const formDS = () => ({
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows.mtNcCode',
  selection: false,
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-nc-code/detail/ui`,
        method: 'get',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          return {
            ...rows?.mtNcCode,
            mtNcValidOperList: rows?.mtNcValidOperList,
          };
        },
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.method.domain.entity.MtNcCode';
      return {
        data: { ncCodeId: record.get('ncCodeId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_METHOD}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteId`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('ncCodeId');
        },
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'site.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      bind: 'site.siteName',
    },
    {
      name: 'ncType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncType`).d('不良代码类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?stateType=ncTypeList&typeGroup=NC_TYPE&module=NC_CODE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      // required: true,
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码编码'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('ncCodeId');
        },
      },
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码描述'),
      required: true,
    },
    {
      name: 'scrapDetail',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapDetail`).d('不良报废明细'),
      required: true,
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'dispositionGroupObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dispositionGroupDesc`).d('默认处置组'),
      lovCode: 'MT.DISPOSITION_GROUP',
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => !record || !record.get('siteId'),
      },
    },
    {
      name: 'dispositionGroupId',
      type: FieldType.number,
      bind: 'dispositionGroupObject.dispositionGroupId',
    },
    {
      name: 'dispositionGroup',
      type: FieldType.string,
      bind: 'dispositionGroupObject.dispositionGroup',
    },
    {
      name: 'defectLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLevel`).d('缺陷等级'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.METHOD.NC_DEFECT_LEVEL',
      // required: true,
    },
    {
      name: 'maxNcLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxNcLimit`).d('最大限制值'),
      min: 0,
      step: 1,
    },
    {
      name: 'autoCloseIncident',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoCloseIncident`).d('自动关闭事务'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'validAtAllOperations',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.validAtAllOperations`).d('对所有工艺有效'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'allowNoDisposition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.allowNoDisposition`).d('允许无处置'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'componentRequired',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentRequired`).d('是否需要组件'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
  ],
});

const technologyDS = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'ncValidOperationId',
      type: FieldType.number,
    },
    {
      name: 'operation',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺编码'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: 'always',
      required: true,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          const _excludeOperationIds = [];
          dataSet.forEach(_record => {
            if (_record.get('operationId')) {
              _excludeOperationIds.push(_record.get('operationId'));
            }
          })
          return {
            tenantId,
            excludeOperationIds: _excludeOperationIds,
          }
        },
      },
    },
    {
      name: 'operationId',
      type: FieldType.number,
      bind: 'operation.operationId',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      bind: 'operation.operationName',
    },
    {
      name: 'operationDesc',
      type: FieldType.string,
      bind: 'operation.operationDesc',
    },
    {
      name: 'dispositionGroupObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dispositionGroup`).d('处置组'),
      lovCode: 'MT.DISPOSITION_GROUP',
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteId: dataSet.parent.current.get('siteId'),
          };
        },
      },
    },
    {
      name: 'dispositionGroup',
      type: FieldType.string,
      bind: 'dispositionGroupObject.dispositionGroup',
    },
    {
      name: 'dispositionGroupId',
      type: FieldType.number,
      bind: 'dispositionGroupObject.dispositionGroupId',
    },
  ],
});

export { formDS, technologyDS };
