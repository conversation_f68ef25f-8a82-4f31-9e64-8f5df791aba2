/**
 * 间距
 */
@hzero-gutter-grow: 4px;

@hzero-gutter: 16px;
@hzero-gutter-md: @hzero-gutter - @hzero-gutter-grow; // 12px
@hzero-gutter-sm: @hzero-gutter-md - @hzero-gutter-grow; // 8px
@hzero-gutter-xs: @hzero-gutter-sm - @hzero-gutter-grow; // 4px
@hzero-gutter-lg: @hzero-gutter + @hzero-gutter-grow; // 20px
@hzero-gutter-xl: @hzero-gutter-lg + @hzero-gutter-grow; // 24px
@hzero-gutter-xxl: @hzero-gutter-xl + @hzero-gutter-grow; // 28px

// form layout gutter

@hzero-gutter-form-search: @hzero-gutter-md; // 12px
@hzero-gutter-form-edit: @hzero-gutter-xl; // 24px

// block gutter
@hzero-gutter-block: @hzero-gutter-xl; // 24px connect is small
@hzero-gutter-inline: @hzero-gutter; // 16px connect is big

@hzero-gutter-bgc: #b9d3ff;
@hzero-gutter-bgc-light-1: #eee;
@hzero-gutter-bgc-light-2: #f0f0f0;

/**
  * 字体
  */

@hzero-font-size-title: 14px;
@hzero-font-size-normal: 12px;

/**
  * 色彩
  */

// 主要颜色 用于导航栏按钮 或者 其他需要强调的 文字 按钮 操作等
@hzero-primary-color: #1e3255;

// 次级主要颜色 用于页面内部的主要按钮等
@hzero-primary-color-2: #29bece;

// 次要颜色 用于 icon 图表等
@hzero-primary-color-3-blue: #0687ff;
@hzero-primary-color-3-purple: #cb38ad;
@hzero-primary-color-3-yellow: #ffbc00;
@hzero-primary-color-3-red: #f02b2b;

// 低频颜色
@hzero-minor-color-blue: #daedfe;
@hzero-minor-color-purple: #f8e1f3;
@hzero-minor-color-yellow: #fff6d9;
@hzero-minor-color-red: #fddfdf;

// 背景色
@hzero-bgc-color: #b9d3ff;
@hzero-bgc-color-light: #eee;
@hzero-bgc-color-lightest: #f0f0f0;
@hzero-bgc-color-dark: #d5dae0;
@hzero-simple-bgc-color: #f4f6f8;

// 标题颜色
@hzero-title-second-color: #6d7a80;

// 主按钮颜色
@hzero-primary-btn-hover-color: #405477;

// disabled color

@hzero-color-disabled: #ccc;
@hzero-bgc-color-disabled: #f5f5f5;

// 表单相关
@form-item-has-error-color: #f13131;

// Input 颜色

// Tabs 相关
// FIXME: 暂时不能覆盖父级元素的 border 因为 overflow: hidden 了
@hzero-tabs-card-tab-padding: 9px 14px;
@hzero-tabs-card-tab-active-padding: 9px 14px;

// 水平
@hzero-tabs-line-vertical-tab-padding: 13px @hzero-gutter;
@hzero-tabs-line-vertical-tab-margin: 0 @hzero-gutter 0 0;

// 垂直
@hzero-tabs-line-horizontal-tab-padding: 10px @hzero-gutter;
// 颜色
@hzero-tabs-tab-color: #4c4c4c;

// 表单相关
@hzero-form-required-color: #fffbdf;

// 表格相关
@hzero-form-disabled-label-color: #666;
@hzero-form-disabled-wrapper-color: #333;

// notification
@hzero-notification-success-color: #4aa44e;
@hzero-notification-info-color: #3689f7;
@hzero-notification-warn-color: #f6bd41;
@hzero-notification-error-color: #dd4037;

// 所有的 z-index 必须在这里声明

// 菜单的 z-index
@hzero-layout-menu-z-index: 1100;

:global {
  .ant-tooltip {
    max-width: 500px !important;
  }

  .lov-modal {
    word-break: break-all;

    .ant-modal-title {
      font-size: 18px;
    }

    .ant-modal-header {
      padding: 16px 16px 0;
      border-bottom: none;
    }

    .ant-modal-footer {
      border-top: none;
      padding: 0 16px 16px;
      margin-top: 16px;
    }

    .ant-form-item {
      display: flex;
      margin-bottom: 0;
    }

    .ant-pagination-options-size-changer.ant-select {
      margin-right: 0;
    }

    .lov-modal-btn-container {
      flex-shrink: 0;
      display: flex;
      height: 40px;
      align-items: center;
    }
  }

  .mes-multiple-lov {
    width: 100%;
    min-height: 28px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    line-height: 28px;
    margin-top: 5px;
    padding: 0 34px 0 4px;
    vertical-align: middle;
    position: relative;
    top: -1px;
    overflow: hidden;
    max-height: 142px;

    .tag-wrapper {
      width: 100%;
      max-height: 142px;
      overflow: hidden;
    }

    > .lov-clear {
      margin-right: 8px;
      display: none;
    }

    > .lov-search {
      float: right;
      position: absolute;
      right: 10px;
      top: calc(50% - 6px);
      opacity: 0.4;
    }

    .ant-input-group-wrapper {
      vertical-align: middle;
    }

    .ant-input {
      // 两个 icon 的位置
      padding-right: 48px !important;
      transition: padding 0s, background-color 0.3s, border 0.3s;
    }

    .ant-input-suffix {
      // LOV 查询icon颜色和DatePicker等组件的icon颜色一样
      .anticon-search:before {
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }

  .mes-lov-disabled {
    color: #aaa !important;
    background-color: #f5f5f5;
    opacity: 1;
    cursor: not-allowed;
  }

  .lov-suffix {
    > .lov-clear {
      display: none;
      cursor: pointer;
      position: absolute;
      right: 14px;
      top: calc(50% - 6px);
    }

    &:hover {
      > .lov-clear {
        display: inline-block;
      }
    }
  }

  .ant-form-item-required .lov-suffix {
    &:hover {
      .ant-input-suffix {
        background: @hzero-form-required-color;
        display: block;
      }
    }
  }
}
