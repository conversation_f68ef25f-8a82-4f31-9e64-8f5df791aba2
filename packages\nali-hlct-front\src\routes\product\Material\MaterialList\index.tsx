/**
 * @Description: 物料维护-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 15:24:17
 * @LastEditTime: 2023-05-18 11:37:39
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useCallback } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { tableDS } from '../stories';

const MaterialList = (props) => {

  const {
    match: { path },
    tableDs,
    history,
    customizeTable,
  } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'materialCode',
        width: 200,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/hmes/product/material-manager/dist/${record!.get(
                    'materialId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'materialIdentifyCode',
        width: 200,
      },
      {
        name: 'materialName',
        width: 200,
      },
      {
        name: 'model',
        width: 150,
      },
      {
        name: 'materialDesignCode',
        width: 150,
      },
      {
        name: 'primaryUomName',
        width: 150,
      },
      {
        name: 'primaryUomCode',
        width: 150,
      },
      {
        name: 'secondaryUomName',
        width: 180,
      },
      {
        name: 'secondaryUomCode',
        width: 150,
      },
      {
        name: 'conversionRate',
        width: 150,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            { }
          </Badge>
        ),
      },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    history.push({
      pathname: '/hmes/product/material-manager/dist/create',
    });
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.product.materialManager.title.list').d('物料维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="Material"
            customizedCode="Material"
          />,
        )}
      </Content>
    </div>
  );
}

export default flow(
  formatterCollections({ code: ['tarzan.product.materialManager', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LIST.LIST`] }),
)(MaterialList);
