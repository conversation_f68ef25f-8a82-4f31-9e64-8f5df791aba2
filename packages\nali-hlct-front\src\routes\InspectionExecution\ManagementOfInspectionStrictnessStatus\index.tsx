/**
 * @Description: 检验严格度状态管理-查询界面
 * @Author: <<EMAIL>>
 * @Date: 2023-02-10 17:18:09
 * @LastEditTime: 2023-05-18 17:03:16
 * @LastEditors: <<EMAIL>>
 */
import React, { useState } from 'react';
import { Table, DataSet, Modal, Button } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withProps from 'utils/withProps';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useDataSetEvent } from 'utils/hooks';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { observer } from 'mobx-react';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { tableDS, detailDS } from './stores/managementOfInspectionStrictnessStatusDS';

const modelPrompt = 'tarzan.qms.ManagementOfInspectionStrictnessStatus';

let _inspectMaterialDrawer;

const ManagementOfInspectionStrictnessStatusList = props => {
  const { tableDs, detailDs } = props;
  const [canEdit, setCanEdit] = useState(false);
  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteObj':
        record.set('inspectBusinessTypeObj', null);
        break;
      default:
        break;
    }
  });

  useDataSetEvent(tableDs, 'load', () => {
    tableDs.forEach(record => {
      record.setState('selectable', true);
    });
  });

  const saveTable = useRequest({
    url: `${
      BASIC.TARZAN_SAMPLING
    }/v1/${getCurrentOrganizationId()}/mt-mat-splyr-strict/save/ui`,
    method: 'POST',
  }, {
    manual: true,
    needPromise: true,
  });

  const columns: ColumnProps[] = [
    {
      name: 'siteCode',
    },
    {
      name: 'siteName',
    },
    {
      name: 'inspectBusinessTypeDesc',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'materialCategoryCode',
    },
    {
      name: 'materialCategoryDesc',
    },
    {
      name: 'supplierCode',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'inspectSchemeCode',
      renderer: ({ record, value }) => {
        return (
          <Button funcType={FuncType.link} onClick={() => handleGoInspectScheme(record)}>
            {value}
          </Button>
        );
      },
    },
    {
      name: 'inspectItemCode',
      renderer: ({ record, value }) => {
        return (
          <Button funcType={FuncType.link} onClick={() => handleGoInspect(record)}>
            {value}
          </Button>
        );
      },
    },
    {
      name: 'inspectItemDesc',
    },
    {
      name: 'beforeStrictness',
    },
    {
      name: 'afterStrictness',
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'acceptLot',
    },
    {
      name: 'okLot',
      width: 200,
    },
    {
      name: 'ngLot',
      width: 260,
    },
    {
      name: 'transferScore',
      editor: record => {
        return record.getState('myEditing');
      },
    },
    {
      name: 'option',
      align: ColumnAlign.center,
      width: 120,
      lock: ColumnLock.right,
      renderer: ({ record }) => {
        return (
          <PermissionButton
            type="text"
            permissionList={[
              {
                code: `list.button.strictDetail`,
                type: 'button',
                meaning: '列表页-报检条码明细',
              },
            ]}
            onClick={() => handleStrictDetail(record)}
          >
            {intl.get(`${modelPrompt}.button.strictDetail`).d('严格度明细')}
          </PermissionButton>
        );
      },
    },
  ];

  const strictDetailColumns: ColumnProps[] = [
    {
      name: 'inspectDocNum',
    },
    {
      name: 'inspectResultDesc',
    },
    {
      name: 'beforeStrictness',
    },
    {
      name: 'afterStrictness',
    },
    // {
    //   name: 'acceptLot',
    // },
    {
      name: 'okLot',
      width: 200,
    },
    {
      name: 'ngLot',
      width: 260,
    },
    {
      name: 'samplTransferRuleCode',
      renderer: ({ record, value }) => {
        return (
          <Button funcType={FuncType.link} onClick={() => handleGoTransferRule(record)}>
            {value}
          </Button>
        );
      },
    },
    {
      name: 'samplTransferScoreCode',
      renderer: ({ record, value }) => {
        return (
          <Button funcType={FuncType.link} onClick={() => handleTransferScore(record)}>
            {value}
          </Button>
        );
      },
    },
    {
      name: 'beforeTransferScore',
    },
    {
      name: 'afterTransferScore',
    },
    {
      name: 'trxTransferScore',
    },
    {
      name: 'inspectSchemeCode',
      renderer: ({ record, value }) => {
        return (
          <Button funcType={FuncType.link} onClick={() => handleGoInspectScheme(record)}>
            {value}
          </Button>
        );
      },
    },
    {
      name: 'operationType',
    },
    {
      name: 'createdBy',
    },
    {
      name: 'creationDate',
    },
  ];

  const handleStrictDetail = record => {
    detailDs.setQueryParameter('materialSuppStrictId', record?.get('materialSuppStrictId'));
    detailDs.query();
    _inspectMaterialDrawer = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.inspectMaterialDetail`).d('严格度明细'),
      drawer: true,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <Table customizedCode="jyygdztgl2" dataSet={detailDs} columns={strictDetailColumns} />
      ),
      onCancel: () => {
        _inspectMaterialDrawer = undefined;
      },
      okButton: false,
    });
  };
  // 点击操作列编辑
  const handleEdit = () => {
    tableDs.forEach(record => {
      record.setState('selectable', false);
    });
    tableDs.selected.forEach(record => {
      record.setState('myEditing', true);
    });
    setCanEdit(!canEdit);
  };

  // 点击操作列取消
  const handleCancel = () => {
    tableDs.selected.forEach(record => {
      record.reset();
      record.setState('myEditing', false);
    });
    setCanEdit(!canEdit);
    tableDs.query();
  };

  const handleGoInspectScheme = record => {
    _inspectMaterialDrawer?.close();
    props.history.push(
      `/hwms/inspection-scheme-maintenance/detail/${record.get('inspectSchemeId')}`,
    );
  };
  const handleGoTransferRule = record => {
    _inspectMaterialDrawer?.close();
    props.history.push(
      `/sampling/sampling-method/transfer-rule/detail/${record.get('samplTransferRuleId')}`,
    );
  };
  const handleTransferScore = record => {
    _inspectMaterialDrawer?.close();
    props.history.push(
      `/sampling/sampling-method/transfer-score/detail/${record.get('samplTransferScoreId')}`,
    );
  };
  const handleGoInspect = record => {
    props.history.push(`/hwms/inspect-item-maintain/dist/${record.get('inspectItemId')}`);
  };

  const CheckBtn = observer(({ ds }) => {
    return (
      <PermissionButton
        type="c7n-pro"
        color={ButtonColor.primary}
        icon="spellcheck"
        onClick={handleEdit}
        permissionList={[
          {
            code: `hzero.tarzan.hlct.jycl.inspection-severity-status-management.ps.button.create`,
            type: 'button',
            meaning: '列表页-严格度修改',
          },
        ]}
        disabled={ds.selected.length === 0}
      >
        {intl.get(`${modelPrompt}.button.severityModification`).d('严格度修改')}
      </PermissionButton>
    );
  });

  const handleSave = async () => {
    const normalValidate = await Promise.all(
      tableDs.selected.map(async record => {
        const itemValidate = await record.validate('all');
        return itemValidate;
      }),
    );
    const normalResult = normalValidate.every(val => val);

    if (!normalResult) {
      return
    }

    const _data = tableDs.toJSONData()
    if (_data.length === 0) {
      setCanEdit(!canEdit);
      tableDs.query();
      return
    }
    const res = await saveTable.run({
      params: _data,
    })

    if (res?.success) {
      setCanEdit(!canEdit);
      notification.success({});
      tableDs.query();
    }
  }
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('检验严格度状态管理')}>
        <>
          {canEdit ? (
            <>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave

                }
                permissionList={[
                  {
                    code: `button.save`,
                    type: 'button',
                    meaning: '列表页-保存',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.save`).d('保存')}
              </PermissionButton>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="close"
                onClick={handleCancel}
                permissionList={[
                  {
                    code: `button.cancel`,
                    type: 'button',
                    meaning: '列表页-取消',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
              </PermissionButton>
            </>
          ) : (
            <CheckBtn ds={tableDs} />
          )}
        </>
      </Header>
      <Content>
        <Table
          customizedCode="jyygdztgl1"
          searchCode="jyygdztgl1"
          columns={columns}
          queryBar={TableQueryBarType.filterBar}
          dataSet={tableDs}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.qms.ManagementOfInspectionStrictnessStatus'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      const detailDs = new DataSet({
        ...detailDS(),
      });
      return {
        tableDs,
        detailDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ManagementOfInspectionStrictnessStatusList),
);
