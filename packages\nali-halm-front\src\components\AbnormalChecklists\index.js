/**
 * @since 2021-10-20
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { PureComponent } from 'react';
import { Button, Table } from 'choerodon-ui/pro';
import queryString from 'querystring';
import { Bind } from 'lodash-decorators';
import { observer } from 'mobx-react';

import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';
import notification from 'utils/notification';

import getLangs from './Langs';

const organizationId = getCurrentOrganizationId();
@observer
export default class AbnormalChecklists extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
  }

  /**
   * 确认告警记录状态
   */
  @Bind()
  handleCheckStatus(alarmRecordIdList, callback) {
    request(`${HALM_MTC}/v1/${organizationId}/alarm-records/check-status`, {
      method: 'POST',
      body: {
        alarmRecordIdList,
      },
    }).then(res => {
      if (res && res.failed) {
        notification.error({
          message: res.message,
        });
      } else {
        callback();
      }
    });
  }

  /**
   * 创建工作单 - 逻辑同告警工作台
   */
  @Bind()
  handleCreateWO() {
    const { abTableDs, detail, history } = this.props;
    const {
      maintSiteId,
      maintSiteName,
      assetLocationId,
      assetLocationName,
      assetId,
      descAndLabel,
    } = detail;
    const alarmRecordIds = [];
    const sourceList = abTableDs.selected.map(record => {
      alarmRecordIds.push(record.get('alarmRecordId'));
      return {
        sourceType: 'METER_ALARM',
        sourceId: record.get('alarmRecordId'),
        sourceNum: record.get('alarmRecordCode'),
        canRemoveFlag: false, // 由告警新建工作单时不可移除
      };
    });
    const params = {
      sourceParamType: 'ALARM',
    };
    // 操作前确认告警记录状态
    this.handleCheckStatus(alarmRecordIds, () => {
      // @ts-ignore
      history.push({
        pathname: `/amtc/work-order/create`,
        search: queryString.stringify(params),
        state: {
          detail: {
            alarmRecordIds,
            maintSiteId,
            maintSiteName,
            assetLocationId,
            assetLocationName,
            assetId,
            descAndLabel,
            sourceList,
          },
        },
      });
    });
  }

  /**
   * 创建服务申请单 - 逻辑同告警工作台
   */
  @Bind()
  handleCreateSR() {
    const { abTableDs, detail, history } = this.props;
    const {
      maintSiteId,
      maintSiteName,
      assetLocationId,
      assetLocationName,
      assetId,
      descAndLabel,
    } = detail;
    const alarmRecordIds = abTableDs.selected.map(i => i.get('alarmRecordId'));
    const alarmRecordCodes = abTableDs.selected.map(i => i.get('alarmRecordCode'));
    // 操作前确认告警记录状态
    this.handleCheckStatus(alarmRecordIds, () => {
      history.push({
        pathname: `/amtc/service-apply-current/create`,
        state: {
          detail: {
            alarmRecordIds,
            maintSiteId,
            maintSiteName,
            assetLocationId,
            assetLocationName,
            assetId,
            descAndLabel,
            sourceTypeCode: 'METER_ALARM',
            sourceReference: alarmRecordCodes.join('；'),
          },
        },
      });
    });
  }

  // 跳转到后续单据明细页
  @Bind()
  handleView(data) {
    // disableOptionFlag：禁止操作，仅查看顶层界面，审批表单时禁止跳转
    const { disableOptionFlag } = this.props;
    if (disableOptionFlag) {
      return;
    }
    const { subsequentProcessCode, processId, processNum } = data;
    if (subsequentProcessCode === 'WO') {
      this.props.history.push({
        pathname: `/amtc/work-order/detail/${processId}/${processNum}`,
      });
    } else if (subsequentProcessCode === 'SR') {
      this.props.history.push({
        pathname: `/amtc/service-apply-current/detail/${processId}`,
      });
    }
  }

  @Bind()
  toAlarmRecord(alarmRecordCode) {
    const { detail: { maintSiteId } = {} } = this.props;
    this.props.history.push({
      pathname: `/aori/alarm-record/list`,
      query: { alarmRecordCode, maintSiteId },
    });
  }

  get columns() {
    return [
      {
        name: 'parentTypeCode',
        width: 150,
        renderer: ({ record, text }) => {
          let value = null;
          switch (text) {
            case 'WO':
              value = '工作单';
              break;
            case 'WOOP':
              value = `任务：${record.get('woopNum')}`;
              break;
            case 'WO_CHECK':
              value = '单据检查项';
              break;
            case 'WOOP_CHECK':
              value = `点位检查项：${record.get('woopNum')}`;
              break;
            default:
              break;
          }
          return value;
        },
      },
      {
        name: 'checklistName',
      },
      {
        name: 'actValueDes',
      },
      {
        name: 'alarmRecordCode',
        width: 150,
        renderer: ({ text }) => {
          return <a onClick={() => this.toAlarmRecord(text)}>{text}</a>;
        },
      },
      {
        name: 'subsequentProcessMeaning',
      },
      {
        name: 'processNum',
        width: 150,
        renderer: ({ record, text }) => {
          return <a onClick={() => this.handleView(record.toData())}>{text}</a>;
        },
      },
      {
        name: 'processName',
        width: 150,
      },
      {
        name: 'processDesc',
      },
    ];
  }

  render() {
    const { disableBtn, hiddenBtn, abTableDs } = this.props;
    const noSelected = abTableDs.selected.length === 0;
    return (
      <React.Fragment>
        {!hiddenBtn && (
          <div style={{ textAlign: 'right', marginBottom: 8 }}>
            <Button
              icon="plus"
              style={{
                marginRight: '10px',
              }}
              type="primary"
              onClick={this.handleCreateWO}
              disabled={disableBtn || noSelected}
            >
              {getLangs('BTN_CREATE_WO')}
            </Button>
            <Button
              icon="plus"
              style={{
                marginRight: '10px',
              }}
              type="primary"
              onClick={this.handleCreateSR}
              disabled={disableBtn || noSelected}
            >
              {getLangs('BTN_CREATE_SR')}
            </Button>
          </div>
        )}
        <Table dataSet={abTableDs} columns={this.columns} />
      </React.Fragment>
    );
  }
}
