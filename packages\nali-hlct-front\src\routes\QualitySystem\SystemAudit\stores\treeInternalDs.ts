import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.systemAudit';

const ExternalFactory = () =>
  new DataSet({
    primaryKey: 'id',
    parentField: 'parentId',
    expandField: 'expand',
    idField: 'id',
    queryDataSet: new DataSet({
      fields: [{ name: 'text', type: FieldType.string }],
    }),
    fields: [
      { name: 'id', type: FieldType.number },
      { name: 'parentId', type: FieldType.number },
      {
        name: 'templateFileName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.button.templateFileName`).d('文件名称'),
      },
      {
        name: 'templateFileCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.button.templateFileCode`).d('文件编码'),
      },
      {
        name: 'all',
        type: FieldType.boolean,
      },
    ],
    events: {
      select: ({ record }) => {
        // if (record.get('all')) {
        //   record.set('all', false);
        // }
      },
      unSelect: ({ record }) => {
        if (record.get('all')) {
          record.set('all', false);
        }
      },
    },
  });

export default ExternalFactory;
