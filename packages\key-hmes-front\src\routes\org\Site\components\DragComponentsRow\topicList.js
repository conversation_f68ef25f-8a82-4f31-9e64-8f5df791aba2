/**
 * @Description: 列拖拽
 * @Author: <<EMAIL>>
 * @Date: 2021-09-14 16:17:56
 * @LastEditTime: 2022-05-17 15:11:54
 * @LastEditors: <<EMAIL>>
 */
import React, { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd-9.3.4';
import { Icon } from 'choerodon-ui';
import ItemTypes from './itemTypes';
import styles from '../index.module.less';

const TopicList = ({
  id,
  text,
  index,
  moveCard,
  previewList,
  draggingState,
  draggingStateChange,
}) => {
  const ref = useRef(null);

  const [, drop] = useDrop({
    // 定义拖拽的类型
    accept: ItemTypes.TOPIC,
    hover(item, monitor) {
      // 异常处理判断
      if (!ref.current) {
        return;
      }
      // 拖拽目标的Index
      const dragIndex = item.index;
      const dragId = item.id;
      let canMove = false;
      // 确认包含该元素
      previewList.forEach(_item => {
        if (_item.questionTuid === dragId) {
          canMove = true;
        }
      });
      if (!canMove) {
        return;
      }
      // 放置目标Index
      const hoverIndex = index;
      // 如果拖拽目标和放置目标相同的话，停止执行
      if (dragIndex === hoverIndex) {
        return;
      }
      // 如果不做以下处理，则卡片移动到另一个卡片上就会进行交换，下方处理使得卡片能够在跨过中心线后进行交换.
      // 获取卡片的边框矩形
      const hoverBoundingRect = ref.current.getBoundingClientRect();
      // 获取X轴中点
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      // 获取拖拽目标偏移量
      const clientOffset = monitor.getClientOffset();
      // console.log(clientOffset)
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      // 从上往下放置
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      // 从下往上放置
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      // 调用方法完成交换
      moveCard(dragIndex, hoverIndex, dragId);
      // 重新赋值index，否则会出现无限交换情况
      /* eslint-disable-next-line */
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    item: { type: ItemTypes.TOPIC, id, index },
    collect: monitor => {
      draggingStateChange(monitor.isDragging());
      return {
        isDragging: monitor.isDragging(),
      };
    },
  });

  const opacity = isDragging ? 0 : 1;

  drag(drop(ref));
  return (
    <div
      ref={ref}
      style={{ opacity }}
      className={draggingState ? styles['row-dragging-item'] : styles['row-drag-item']}
    >
      <div className={styles['row-drag-item-left']}>
        <Icon type="baseline-drag_indicator" />
        <span>{text}</span>
      </div>
    </div>
  );
};
export default TopicList;
