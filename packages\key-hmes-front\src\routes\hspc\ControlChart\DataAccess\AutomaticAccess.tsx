/**
 * @Description: 控制控制图-自动接入
 * @Author: <<EMAIL>>
 * @Date: 2021-12-01 10:00:52
 * @LastEditTime: 2021-12-13 16:29:55
 * @LastEditors: <<EMAIL>>
 */

import React, { useState } from 'react';
import { Tooltip, Switch, TextField, Form, DataSet, CodeArea, Select, Lov } from 'choerodon-ui/pro';
import { Icon, message } from 'choerodon-ui';
import JSONFormatter from 'choerodon-ui/pro/lib/code-area/formatters/JSONFormatter';
import { getCurrentOrganizationId } from 'utils/utils';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import intl from 'utils/intl';
import styles from './automaticAccess.module.less';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hspc.controlChartMaintain';

interface AutomaticAccess {
  ds: DataSet;
  formDs: DataSet;
  dataSource: any;
}

const AutomaticAccess = (props: AutomaticAccess) => {
  const { ds, dataSource, formDs } = props;
  const [sourceMarkFlag, setSourceMarkFlag] = useState(ds!.current!.get('sourceMarkFlag'));
  const [sourceMark, setSourceMark] = useState(ds!.current!.get('sourceMark'));
  const [dataOriginType, setDataOriginType] = useState('QMS');

  const analysisByColumn = () => {
    const renderTableInner: any = [];
    Object.keys(dataSource.tableStructure).forEach(key => {
      const value = dataSource.tableStructure[key];
      const element = (
        <tr key={key}>
          <td key="0">{value}</td>
          <td className={key !== '1' ? styles['deep-td'] : ''} key="1">
            XX
          </td>
          <td className={key !== '1' ? styles['shallow-td'] : ''} key="2">
            /
          </td>
        </tr>
      );
      renderTableInner.push(element);
    });
    return renderTableInner;
  };

  const analysisByRow = () => {
    const renderTableInner = (
      <>
        <tr key="1">
          <td>{dataSource.tableStructure[1]}</td>
          {Array(dataSource.subgroupSize * 2)
            .fill('1')
            .map((_, index) => {
              if (index === 0) {
                return <td>XX</td>;
              }
              return <td>/</td>;
            })}
        </tr>
        <tr key="2">
          <td>{dataSource.tableStructure[2]}</td>
          {Array(dataSource.subgroupSize * 2)
            .fill('1')
            .map((_, index) => {
              if (index === 0) {
                return <td className={styles['deep-td']}>XX</td>;
              }
              return (
                <td
                  className={
                    index < dataSource.subgroupSize ? styles['deep-td'] : styles['shallow-td']
                  }
                >
                  /
                </td>
              );
            })}
        </tr>
      </>
    );
    return renderTableInner;
  };

  const tooltipText = (
    <p style={{ width: '240px', fontSize: '14px' }}>
      {intl
        .get(`${modelPrompt}.sourceIdentificationTips`)
        .d(
          '外部系统中的唯一值，需与控制控制图一一对应，启用后即根据当前字段值直接匹配到唯一控制控制图。',
        )}
    </p>
  );

  const calculateRequestParams = () => {
    const requestParams: any = [
      {
        controlCode: 'string',
        sampleTime: 'string',
      },
    ];
    const sampleDataObj: any = {};
    Object.keys(dataSource.tableStructure).forEach(key => {
      const element = dataSource.tableStructure[key];
      sampleDataObj[element] = 'string';
    });
    requestParams[0].sampleData = sampleDataObj;
    if (sourceMarkFlag === 'Y') {
      requestParams[0].sourceMark = sourceMark;
    }
    return JSON.stringify(requestParams, null, 2);
  };

  const calculateResponseParams = `{
    "rows": {
      "failureMessage": {},
      "insertQuantity": 0,
      "samplingQuantity": 0,
      "totalQuantity": 0
    },
    "success": true
  }`;

  const handleCopy = () => {
    message.destroy();
    message.config({
      top: 100,
      duration: 3,
      placement: 'top',
    });
    message.success({});
  };

  const sourceMarkData = () => {
    const sourceMarkListFirstList: any = [];
    formDs.forEach(item => {
      if (item) {
        if (item?.get('dataOrigin')) {
          setDataOriginType(item?.get('dataOrigin'));
          sourceMarkListFirstList.push(item?.get('dataOrigin'));
        }
        if (item?.get('siteLov')) {
          sourceMarkListFirstList.push(item?.get('siteLov').siteCode);
          // sourceMarkListFirstList.push(item?.get('siteLov').siteName);
        }
        if (item?.get('inspectBusinessType')) {
          sourceMarkListFirstList.push(item?.get('inspectBusinessType'));
        }
        if (item?.get('inspectSchemeLov')) {
          // sourceMarkListFirstList.push(item?.get('inspectSchemeLov').inspectSchemeId);
          sourceMarkListFirstList.push(item?.get('inspectSchemeLov').inspectSchemeCode);
        }
        if (item?.get('inspectItemLov')) {
          sourceMarkListFirstList.push(item?.get('inspectItemLov').inspectItemCode);
        }
        if (item?.get('supplierLov')) {
          sourceMarkListFirstList.push(item?.get('supplierLov').supplierCode);
          // sourceMarkListFirstList.push(item?.get('supplierLov').supplierId);
        }
        if (item?.get('customerLov')) {
          // sourceMarkListFirstList.push(item?.get('customerLov').customerId);
          sourceMarkListFirstList.push(item?.get('customerLov').customerCode);
        }
        if (item?.get('processWorkcellLov')) {
          // sourceMarkListFirstList.push(item?.get('processWorkcellLov').workcellId);
          sourceMarkListFirstList.push(item?.get('processWorkcellLov').workcellCode);
        }
        if (item?.get('dataGroupLov')) {
          sourceMarkListFirstList.push(item?.get('dataGroupLov').tagGroupCode);
        }
        if (item?.get('dataItemLov')) {
          sourceMarkListFirstList.push(item?.get('dataItemLov').tagCode);
        }
        if (item?.get('equipmentLov')) {
          // sourceMarkListFirstList.push(item?.get('equipmentLov').equipmentId);
          sourceMarkListFirstList.push(item?.get('equipmentLov').equipmentCode);
        }
        if (item?.get('materialCodeLov')) {
          sourceMarkListFirstList.push(item?.get('materialCodeLov').materialCode);
        }
      }
    });
    const sourceMarkListFirst = sourceMarkListFirstList.join('-');
    if (ds?.current) {
      ds.current?.init('sourceMark', sourceMarkListFirst);
    }
  };

  const handleChange = (item) => {
    if (item === 'dataOrigin') {
      // formDs?.current?.set('siteLov', null);
      formDs?.current?.set('inspectBusinessType', null);
      formDs?.current?.set('inspectSchemeLov', null);
      formDs?.current?.set('inspectItemLov', null);
      formDs?.current?.set('materialCodeLov', null);
      formDs?.current?.set('supplierLov', null);
      formDs?.current?.set('customerLov', null);
      formDs?.current?.set('processWorkcellLov', null);
      formDs?.current?.set('equipmentLov', null);
      formDs?.current?.set('dataGroupLov', null);
      formDs?.current?.set('dataItemLov', null);
    }
    if (item === 'equipmentLov') {
      formDs?.current?.set('dataGroupLov', null);
      formDs?.current?.set('dataItemLov', null);
    }
    if (item === 'dataGroupLov') {
      formDs?.current?.set('dataItemLov', null);
    }
  };

  return (
    <div className={styles['automatic-access-modal']}>
      <div className={styles['left-part']}>
        <div className={styles['row-line']}>
          <div className={styles['col-left-form']}>
            {intl.get(`${modelPrompt}.tenantID`).d('租户ID')} :
          </div>
          <div className={styles['col-right-form']}>{tenantId}</div>
        </div>
        <div className={styles['row-line']}>
          <div className={styles['col-left-form']}>
            {intl.get(`${modelPrompt}.controlChartCode`).d('控制控制图编码')} :
          </div>
          <div className={styles['col-right-form']}>{dataSource.controlCode}</div>
        </div>
        <div className={styles['row-line']}>
          <div className={styles['col-left-form']}>
            {intl.get(`${modelPrompt}.sampleDataStructure`).d('样本数据结构')} :
          </div>
          <div className={styles['col-right-form']}>
            {JSON.stringify(dataSource.tableStructure)}
          </div>
        </div>
        <div className={styles['row-line-table']}>
          <table className={styles['access-table']}>
            <tbody>
              {Object.keys(dataSource.tableStructure).length &&
              Object.keys(dataSource.tableStructure).length === 2
                ? analysisByRow()
                : analysisByColumn()}
            </tbody>
          </table>
        </div>
        <div className={styles['row-line']}>
          <div className={styles['col-left-form']}>
            {intl.get(`${modelPrompt}.enableSourceMark`).d('启用来源标识')}
            <Tooltip placement="topLeft" title={tooltipText} theme="dark">
              &nbsp;
              <Icon
                type="help_outline"
                style={{
                  marginBottom: '2px',
                  color: 'black',
                  opacity: '0.3',
                  width: '18px',
                  height: '18px',
                }}
              />
            </Tooltip>{' '}
            :
          </div>
          <div className={styles['col-right-form']} style={{ marginTop: '-2px' }}>
            <Form dataSet={ds}>
              <Switch
                name="sourceMarkFlag"
                onChange={value => {
                  setSourceMarkFlag(value);
                }}
              />
            </Form>
          </div>
        </div>
        {sourceMarkFlag === 'Y' ? (
          <>
            <div className={styles['row-line']}>
              <div className={styles['col-left-form']}>
                <span style={{ color: 'red' }}>* </span>
                {intl.get(`${modelPrompt}.sourceMark`).d('来源标识')} :
              </div>
              <div
                className={styles['col-right-form']}
                style={{ marginTop: '-6px', paddingRight: '28px' }}
              >
                <Form dataSet={ds}>
                  <TextField
                    name="sourceMark"
                    required
                    onChange={value => {
                      setSourceMark(value);
                    }}
                  />
                </Form>
              </div>
            </div>
            <Form dataSet={formDs} columns={2} labelWidth={115}>
              <Select
                name="dataOrigin"
                onChange={async () => {
                  await handleChange('dataOrigin');
                  await sourceMarkData();
                }}
                colSpan={2} />
              <Lov newLine name="siteLov" onChange={sourceMarkData} colSpan={2} />

              {dataOriginType === 'QMS'
                ? (<>
                  <Select name="inspectBusinessType" onChange={sourceMarkData}/>
                  <Lov name="inspectSchemeLov" onChange={sourceMarkData}/>
                  <Lov name="inspectItemLov" onChange={sourceMarkData}/>
                  <Lov name="materialCodeLov" onChange={sourceMarkData}/>
                  <Lov name="supplierLov" onChange={sourceMarkData}/>
                  <Lov name="customerLov" onChange={sourceMarkData}/>
                  <Lov name="processWorkcellLov" onChange={sourceMarkData}/>
                </>)
                :
                (<>
                  <Lov
                    name="equipmentLov"
                    colSpan={2}
                    onChange={async () => {
                      await handleChange('equipmentLov');
                      await sourceMarkData();
                    }}
                  />
                  <Lov colSpan={2} name="materialCodeLov" onChange={sourceMarkData}/>
                  <Lov
                    name="dataGroupLov"
                    colSpan={2}
                    onChange={async () => {
                      await handleChange('dataGroupLov');
                      await sourceMarkData();
                    }}
                  />
                  <Lov name="dataItemLov" onChange={sourceMarkData} colSpan={2}/>
                </>
                )}
            </Form>
          </>
        ) : null}
      </div>
      <div className={styles['right-part']}>
        <div className={styles['row-line']}>
          <div className={styles['col-left']}>
            {intl.get(`${modelPrompt}.requestExample`).d('请求示例')} :
          </div>
          <div className={styles['col-right']}>
            <CodeArea
              formatter={JSONFormatter}
              value={calculateRequestParams()}
              style={{ height: 220 }}
            />
            <CopyToClipboard
              className={styles['code-copy-icon']}
              text={calculateRequestParams()} // 需要复制的文本
              onCopy={handleCopy} // 复制完成的回调
            >
              <Icon type="content_copy" />
            </CopyToClipboard>
          </div>
        </div>
        <div className={styles['row-line']}>
          <div className={styles['col-left']}>
            {intl.get(`${modelPrompt}.responseExample`).d('返回示例')} :
          </div>
          <div className={styles['col-right']}>
            <CodeArea
              formatter={JSONFormatter}
              value={calculateResponseParams}
              style={{ height: 220 }}
            />
            <CopyToClipboard
              className={styles['code-copy-icon']}
              text={calculateResponseParams} // 需要复制的文本
              onCopy={handleCopy} // 复制完成的回调
            >
              <Icon type="content_copy" />
            </CopyToClipboard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutomaticAccess;
