/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-25 14:04:04
 * @LastEditTime: 2023-07-28 16:39:34
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import uuid from 'uuid/v4';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.acquisition.collection';
const tenantId = getCurrentOrganizationId();

const objectLovCode = {
  MATERIAL: 'HME.SITE_MATERIAL',
  OPERATION: 'MT.METHOD.OPERATION',
  WORKCELL: 'MT.MODEL.WORKCELL_SITE',
  EQUIPMENT: 'MT.MODEL.EQUIPMENT',
  MATERIAL_CATEGORY: 'MT.METHOD.MATERIAL_CATEGORY_SITES',
};

const objectIdBind = {
  MATERIAL: 'objectLov.materialId',
  OPERATION: 'objectLov.operationId',
  WORKCELL: 'objectLov.workcellId',
  EQUIPMENT: 'objectLov.equipmentId',
  MATERIAL_CATEGORY: 'objectLov.materialCategoryId',
};

const objectCodeBind = {
  MATERIAL: 'objectLov.materialCode',
  OPERATION: 'objectLov.operationName',
  WORKCELL: 'objectLov.workcellCode',
  EQUIPMENT: 'objectLov.equipmentCode',
  MATERIAL_CATEGORY: 'objectLov.categoryCode',
};

const objectDescBind = {
  MATERIAL: 'objectLov.materialName',
  OPERATION: 'objectLov.description',
  WORKCELL: 'objectLov.workcellName',
  EQUIPMENT: 'objectLov.equipmentName',
  MATERIAL_CATEGORY: 'objectLov.description',
};

const objectRevisionBind = {
  MATERIAL: '',
  OPERATION: 'objectLov.revision',
  WORKCELL: '',
  EQUIPMENT: '',
  MATERIAL_CATEGORY: '',
};

const assObjectsDS: () => DataSetProps = () => ({
  primaryKey: 'tagGroupObjectId',
  autoCreate: false,
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'lineNumber',
      label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
      type: FieldType.number,
    },
    {
      name: 'detailList',
      label: intl.get(`${modelPrompt}.assObjArray`).d('关联对象组合'),
      type: FieldType.object,
    },
  ],
});

const assObjectsRemoveDS: () => DataSetProps = () => ({
  primaryKey: 'tagGroupObjectId',
  autoCreate: false,
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'lineNumber',
      label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
      type: FieldType.number,
    },
    {
      name: 'detailList',
      label: intl.get(`${modelPrompt}.assObjArray`).d('关联对象组合'),
      type: FieldType.object,
    },
  ],
});

const materialRevisionOptionDs = new DataSet({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
        method: 'GET',
        params: { tenantId },
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          let firstlyQueryData: any[] = [];
          const { rows } = JSON.parse(data);
          if (rows instanceof Array) {
            firstlyQueryData = rows.map(item => {
              return {
                kid: uuid(),
                description: item,
              };
            });
          }
          return firstlyQueryData;
        },
      };
    },
  },
});

const singleAssObjectDS: () => DataSetProps = () => ({
  primaryKey: 'uuid',
  autoCreate: true,
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'parentSiteId',
      type: FieldType.object,
    },
    {
      name: 'objectType',
      label: intl.get(`${modelPrompt}.objectType`).d('关联对象类型'),
      type: FieldType.string,
    },
    {
      name: 'objectLov',
      label: intl.get(`${modelPrompt}.objectCode`).d('关联对象编码'),
      type: FieldType.object,
      lovCode: '',
      required: true,
      dynamicProps: {
        lovCode: ({ record }) => {
          const objectType = record.get('objectType');
          return objectLovCode[objectType];
        },
        lovPara({ record }) {
          const queryPara: any = {
            tenantId,
          };
          const siteId = record.get('parentSiteId') || '';
          switch (record.get('objectType')) {
            case 'MATERIAL_CATEGORY':
            case 'EQUIPMENT':
            case 'MATERIAL':
              queryPara.siteId = siteId;
              break;
            case 'WORKCELL':
              queryPara.siteId = siteId;
              queryPara.userFlag = 'Y';
              break;
            default:
              // OPERATION
              break;
          }
          return queryPara;
        },
      },
    },
    {
      name: 'objectId',
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectIdBind[objectType];
        },
      },
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectCodeBind[objectType];
        },
      },
    },
    {
      name: 'objectDesc',
      label: intl.get(`${modelPrompt}.objectDesc`).d('关联对象描述'),
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectDescBind[objectType];
        },
      },
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'objectLov.revisionFlag',
    },
    {
      name: 'objectRevision',
      label: intl.get(`${modelPrompt}.objectRevision`).d('关联对象版本'),
      type: FieldType.string,
      required: false,
      textField: 'description',
      valueField: 'description',
      options: materialRevisionOptionDs,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('revisionFlag') === 'Y';
        },
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectRevisionBind[objectType];
        },
      },
    },
  ],
});

export { singleAssObjectDS, assObjectsDS, assObjectsRemoveDS, materialRevisionOptionDs };
