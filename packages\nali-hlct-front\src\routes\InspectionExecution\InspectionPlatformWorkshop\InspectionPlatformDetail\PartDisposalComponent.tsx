/**
 * @Description: 部分处置
 * @Author: <<EMAIL>>
 * @Date: 2023-03-17 13:45:47
 * @LastEditTime: 2023-05-23 11:39:40
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useState } from 'react';
import { Table, Modal, DataSet, Form, Output } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { LabelAlign, LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { getCurrentLanguage } from 'utils/utils';
import { SavePartDisposal } from '../services';
import { PartDisposalDS } from '../stories/DetailDS';

export interface PartDisposalComponentProps {
  inspectInfoDS?: any;
  [key: string]: any;
}

const modelPrompt = 'tarzan.qms.inspectionPlatform';
const language = getCurrentLanguage();

const PartDisposalComponent: React.FC<PartDisposalComponentProps> = props => {
  const { inspectInfoDS, disposalTypeDesc, disposalStatus, disposalType, curDispFunction, ...othersProps } = props;

  // 部分处置DS
  const partDisposalDS = useMemo(() => new DataSet({ ...PartDisposalDS() }), []);
  // 动态字段
  const [trendsColumns, setTrendsColumns] = useState([]);

  // 检验不良记录-查询
  const savePartDisposal = useRequest(SavePartDisposal(), {
    manual: true,
    needPromise: true,
  });

  // 部分处置-打开弹窗
  const handleOpenPartDisposition = async () => {
    partDisposalDS.setState('confirm', 'open');
    let _trendsColumns = trendsColumns;
    if (inspectInfoDS.getState('partDisposalFlag') === 'Y') {
      _trendsColumns = getColumns();

      inspectInfoDS.setState('partDisposalFlag', 'N');
    }

    Modal.open({
      ...drawerPropsC7n({
        canEdit: inspectInfoDS.getState('canEdit') || disposalStatus,
        ds: partDisposalDS,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.partDisposal`).d('部分处置'),
      destroyOnClose: true,
      drawer: true,
      style: {
        width: '70%',
      },
      keyboardClosable: false,
      onOk: handleSavePartDisposal,
      onCancel: handleClose,
      children: (
        <>
          <Form
            dataSet={partDisposalDS}
            record={partDisposalDS.get(0)}
            columns={1}
            labelWidth="auto"
            labelLayout={LabelLayout.horizontal}
            labelAlign={LabelAlign.right}
          >
            <Output
              name="gatherQty"
              renderer={() => {
                let _startFieldQty = '';
                let _endFieldQty = '';
                const disposalTypeMap = partDisposalDS.getState('disposalTypeMap') || {};
                Object.keys(disposalTypeMap).forEach(key => {
                  const { dispositionFunction } = disposalTypeMap[key];
                  if (dispositionFunction === 'PASS') {
                    _startFieldQty = `/${Number(
                      partDisposalDS.get(0)?.get('gatherQty') || 0,
                    )}${_startFieldQty}`;
                  } else if (
                    [
                      'REWORK_SOURCE',
                      'REWORK_ROUTER',
                      'DEGRADE',
                      'CONCESSION_INTERCEPTION时',
                      'EO_DISCHARGE',
                      'CROSS_WO_INTERCEPT',
                    ].includes(dispositionFunction)
                  ) {
                    _endFieldQty += `/${disposalTypeMap[key]?.sumQty || 0}`;
                  } else {
                    _startFieldQty += `/${disposalTypeMap[key]?.sumQty || 0}`;
                  }
                });
                return `${inspectInfoDS.current?.get('inspectSumQty') ||
                  0}${_startFieldQty}${_endFieldQty}`;
              }}
            />
          </Form>
          <Table dataSet={partDisposalDS} columns={_trendsColumns} />
        </>
      ),
    });

    await handleFetchPartDisposal();
  };

  // 处理动态字段
  const getColumns = () => {
    partDisposalDS.setState('siteId', inspectInfoDS.current.get('siteId'));
    partDisposalDS.setState('passId', null);
    // 处置方法ID -> 相关信息
    const disposalTypeMap: any = {};
    // 处理动态展示字段
    const _startTrendsColumns: Array<any> = [];
    const _endTrendsColumns: Array<any> = [];
    const dispFunctionList = inspectInfoDS.current.get('dispFunctionList') || [];
    const canEdit = inspectInfoDS.getState('canEdit');
    // 汇总字段名称
    let _startFieldLabel = '';
    let _endFieldLabel = '';
    dispFunctionList.forEach(item => {
      const { dispositionFunctionId, dispositionFunction, description } = item;
      const _fieldName = `DISPOSAL_${dispositionFunctionId}`;
      const _description = language === 'zh_CN' ? `${description}数` : description;
      if (!partDisposalDS.getField(_fieldName)) {
        partDisposalDS.addField(_fieldName, {
          name: _fieldName,
          type: FieldType.number,
          label: _description,
          min: 0,
          precision: 6,
        });
      }
      if (dispositionFunction === 'PASS') {
        // 处置方法为通过的ID
        partDisposalDS.setState('passId', _fieldName);
        _startTrendsColumns.unshift({
          name: _fieldName,
        });
        _startFieldLabel = `/${_description}${_startFieldLabel}`;
      } else if (
        [
          'REWORK_SOURCE',
          'REWORK_ROUTER',
          'DEGRADE',
          'CONCESSION_INTERCEPTION',
          'EO_DISCHARGE',
          'CROSS_WO_INTERCEPT',
        ].includes(dispositionFunction)
      ) {
        _endTrendsColumns.push({
          name: _fieldName,
          editor: canEdit || disposalStatus,
        });
        _endFieldLabel = `${_endFieldLabel}/${_description}`;
      } else {
        _startTrendsColumns.push({
          name: _fieldName,
          editor: canEdit || disposalStatus,
        });
        _startFieldLabel = `${_startFieldLabel}/${_description}`;
      }
      disposalTypeMap[_fieldName] = item;
    });
    partDisposalDS.setState('disposalTypeMap', disposalTypeMap);

    // 汇总字段名称
    const _gatherQtyField = partDisposalDS.getField('gatherQty');
    if (_gatherQtyField) {
      const _label = intl.get(`${modelPrompt}.model.disposal.inspectSumQty`).d('报检总数');
      _gatherQtyField.set('label', `${_label}${_startFieldLabel}${_endFieldLabel}`);
    }

    // 前部分固定数据
    const startFixedColumns: any = [
      {
        name: 'disposalObjectType',
        width: 120,
      },
      {
        name: 'disposalObjectCode',
        width: 130,
      },
      {
        name: 'supplierLot',
        width: 130,
      },
      {
        name: 'baseLot',
        width: 130,
      },
      {
        name: 'disposalObjectQty',
        width: 120,
      },
    ];

    // 后部分固定数据
    const endFixedColumns: any = [
      {
        name: 'interceptWorkcellObj',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'CONCESSION_INTERCEPTION',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('concessionInterceptionFlag')) ||
          disposalStatus,
      },
      {
        name: 'dischargeWorkcellObj',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'EO_DISCHARGE',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('eoDischargeFlag')) || disposalStatus,
      },
      {
        name: 'overInterceptOperationObj',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'CROSS_WO_INTERCEPT',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') &&
            (record.get('crossWoInterceptFlag') || record.get('concessionInterceptionFlag'))) ||
          disposalStatus,
      },
      {
        name: 'concessionInterceptOperationObj',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'CONCESSION_INTERCEPTION',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('concessionInterceptionFlag')) ||
          disposalStatus,
      },
      // {
      //   name: 'routerStepObj',
      //   hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
      //     item => item.dispositionFunction === 'CONCESSION_INTERCEPTION',
      //   ),
      //   editor: record =>
      //     (inspectInfoDS.getState('canEdit') && record.get('concessionInterceptionFlag')) ||
      //     disposalStatus,
      // },
      {
        name: 'reworkOperationObj',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'REWORK_SOURCE',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('reworkSourceFlag')) || disposalStatus,
      },
      {
        name: 'reworkWorkcellObj',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'REWORK_SOURCE',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('reworkSourceFlag')) || disposalStatus,
      },
      {
        name: 'reworkRouterObj',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'REWORK_ROUTER',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('reworkRouterFlag')) || disposalStatus,
        width: 120,
      },
      {
        name: 'degradeMaterialObj',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'DEGRADE',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('downgradeFlag')) || disposalStatus,
      },
      {
        name: 'degradeRevisionCode',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'DEGRADE',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('downgradeFlag')) || disposalStatus,
        width: 120,
      },
      {
        name: 'degradeLevel',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'DEGRADE',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('downgradeFlag')) || disposalStatus,
      },
      {
        name: 'degradeDegree',
        hidden: !(inspectInfoDS.current.get('dispFunctionList') || []).find(
          item => item.dispositionFunction === 'DEGRADE',
        ),
        editor: record =>
          (inspectInfoDS.getState('canEdit') && record.get('downgradeFlag')) || disposalStatus,
      },
      {
        name: 'uomName',
      },
      {
        name: 'remark',
        editor: () => inspectInfoDS.getState('canEdit') || disposalStatus,
      },
    ];
    const _trendsColumns = startFixedColumns
      .concat(_startTrendsColumns)
      .concat(_endTrendsColumns)
      .concat(endFixedColumns);
    setTrendsColumns(_trendsColumns);
    return _trendsColumns;
  };

  // 部分处置-查询
  const handleFetchPartDisposal = async () => {
    const disposalTypeMap = partDisposalDS.getState('disposalTypeMap') || {};
    Object.keys(disposalTypeMap).forEach(key => {
      disposalTypeMap[key].sumQty = 0;
    });
    partDisposalDS.setState('disposalTypeMap', disposalTypeMap);
    partDisposalDS.queryParameter = {
      inspectDocId: inspectInfoDS.current.get('inspectDocId'),
    };
    await partDisposalDS.query().then(res => {
      if ((res || []).length > 0) {
        const _newData: Array<any> = [];
        // 需要更新通过数的数据
        const _updatePassData: Array<any> = [];
        // 通过总数
        let _sumPassQty = 0;
        res.forEach((objItem, index) => {
          // 缓存检验对象下的处置方法查询数据用于保存传参
          const _objDisposalInfo: any = {};
          const { disposalDtlList = [], ...otherParams } = objItem;
          const _addInfo: any = {
            ...otherParams,
          };
          // 是否不存在通过的处置方法
          let _passDisposalFlag = false;
          // 其他处置方式数量
          let _otherDisposalSumQty = 0;
          (disposalDtlList || []).forEach((item: any) => {
            _objDisposalInfo[`OBJ_${objItem.disposalObjectId}_${item.disposalFunctionId}`] = item;
            const _fieldName = `DISPOSAL_${item.disposalFunctionId}`;
            const _qty = Number(item.qty || 0);
            _addInfo[_fieldName] = _qty;
            const _disposalTypeInfo = disposalTypeMap[_fieldName] || {};
            if (_disposalTypeInfo?.dispositionFunctionId) {
              const dispositionFunction = _disposalTypeInfo?.dispositionFunction;
              if (dispositionFunction === 'PASS') {
                _passDisposalFlag = true;
              } else {
                if (dispositionFunction === 'REWORK_SOURCE') {
                  _addInfo.reworkOperationId = item.operationId || null;
                  _addInfo.reworkOperationName = item.operationName;
                  _addInfo.reworkWorkcellId = item.workcellId || null;
                  _addInfo.reworkWorkcellName = item.workcellName;
                  _addInfo.reworkSourceFlag = _qty > 0;
                } else if (dispositionFunction === 'REWORK_ROUTER') {
                  _addInfo.reworkRouterId = item.routerId || null;
                  _addInfo.reworkRouterName = item.routerName;
                  _addInfo.reworkRouterFlag = _qty > 0;
                } else if (dispositionFunction === 'DEGRADE') {
                  _addInfo.degradeMaterialId = item.degradeMaterialId || null;
                  _addInfo.degradeMaterialName = item.degradeMaterialName;
                  _addInfo.revisionFlag = item.revisionFlag;
                  _addInfo.degradeRevisionCode = item.degradeRevisionCode;
                  _addInfo.degradeLevel = item.degradeLevel;
                  _addInfo.degradeDegree = item.degradeDegree;
                  _addInfo.downgradeFlag = _qty > 0;
                } else if (dispositionFunction === 'CONCESSION_INTERCEPTION') {
                  _addInfo.interceptWorkcellId = item.workcellId || null;
                  _addInfo.interceptWorkcellName = item.workcellName;
                  _addInfo.concessionInterceptOperationId = item.operationId || null;
                  _addInfo.concessionInterceptOperationName = item.operationName;
                  // _addInfo.routerStepId = item.routerStepId;
                  // _addInfo.routerStepDesc = item.routerStepDesc;
                  _addInfo.downgradeFlag = _qty > 0;
                } else if (dispositionFunction === 'EO_DISCHARGE') {
                  _addInfo.dischargeWorkcellId = item.workcellId || null;
                  _addInfo.dischargeWorkcellName = item.workcellName;
                  _addInfo.downgradeFlag = _qty > 0;
                } else if (dispositionFunction === 'CROSS_WO_INTERCEPT') {
                  _addInfo.overInterceptOperationId = item.operationId || null;
                  _addInfo.overInterceptOperationName = item.operationName;
                  _addInfo.downgradeFlag = _qty > 0;
                }
                _otherDisposalSumQty = (_otherDisposalSumQty * 100000 + _qty * 100000) / 100000;
              }

              // 计算数量
              disposalTypeMap[_fieldName] = {
                ..._disposalTypeInfo,
                sumQty: (Number(_disposalTypeInfo?.sumQty || 0) * 100000 + _qty * 100000) / 100000,
              };
            }
            _addInfo.inspectDocDisposalId = item.inspectDocDisposalId;
          });

          // 计算通过数
          const _passQty =
            Number(objItem.disposalObjectQty || 0) - Number(_otherDisposalSumQty || 0);
          if (!_passDisposalFlag) {
            const _fieldName = partDisposalDS.getState('passId');
            if (_fieldName) {
              if (_passQty) {
                _updatePassData.push({
                  index,
                  fieldName: _fieldName,
                  passQty: parseFloat(`${_passQty.toFixed(6)}`),
                });
              } else {
                _addInfo[_fieldName] = 0;
              }
            }
          }
          _sumPassQty += _passQty;

          _addInfo._objDisposalInfo = _objDisposalInfo;
          _addInfo.disposalType = disposalType;
          _addInfo.curDispFunction = curDispFunction;
          _newData.push(_addInfo);
        });
        partDisposalDS.loadData(_newData);
        // 更新数据通过数
        _updatePassData.forEach(item => {
          const { index, fieldName, passQty } = item;
          partDisposalDS.get(index)?.set(fieldName, passQty);
        });
        partDisposalDS.get(0)?.init('gatherQty', parseFloat(`${_sumPassQty.toFixed(6)}`));
        partDisposalDS.setState('disposalTypeMap', disposalTypeMap);
      }
    });
  };

  const handleClose = async () => {
    const _confirm = partDisposalDS.getState('confirm');
    if (_confirm === 'open') {
      return Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <>
            <p>{intl.get(`${modelPrompt}.close.notice`).d('当前处置明细未保存，是否要退出？')}</p>
          </>
        ),
      }).then(button => {
        return button === 'ok';
      });
    }
    return true;
  };

  // 部分处置-保存
  const handleSavePartDisposal = async () => {
    if (partDisposalDS.status === 'loading') {
      return Promise.resolve(false);
    }
    const validFlag = await partDisposalDS.validate();
    if (!validFlag) {
      return Promise.resolve(false);
    }
    // 处理传参数据
    const _data = partDisposalDS.toJSONData();
    const disposalTypeMap = partDisposalDS.getState('disposalTypeMap') || {};
    const _newData = _data.map((item: any) => {
      const {
        disposalObjectId,
        disposalObjectType,
        disposalObjectQty,
        uomId,
        remark,
        _objDisposalInfo,
      } = item;
      const _disposalDtlList: Array<any> = [];
      // 处理不同处置方法的数据
      Object.keys(disposalTypeMap).forEach(_fieldName => {
        const { dispositionFunctionId, dispositionFunction } = disposalTypeMap[_fieldName];
        const _cacheObjDisposalInfo =
          _objDisposalInfo[`OBJ_${disposalObjectId}_${dispositionFunctionId}`] || {};
        if (_cacheObjDisposalInfo?.inspectDocDisposalDtlId || item[_fieldName]) {
          const _disposalInfo = {
            ..._cacheObjDisposalInfo,
            qty: Number(item[_fieldName] || 0),
            disposalFunctionId: dispositionFunctionId,
            inspectDocDisposalId: item.inspectDocDisposalId,
          };
          _disposalInfo.operationId = null;
          // _disposalInfo.routerStepId = null;
          _disposalInfo.workcellId = null;
          _disposalInfo.routerId = null;
          _disposalInfo.degradeMaterialId = null;
          _disposalInfo.degradeRevisionCode = null;
          _disposalInfo.degradeLevel = null;
          _disposalInfo.degradeDegree = null;
          if (dispositionFunction === 'REWORK_SOURCE') {
            _disposalInfo.operationId = item.reworkOperationId;
            _disposalInfo.workcellId = item.reworkWorkcellId;
          } else if (dispositionFunction === 'REWORK_ROUTER') {
            _disposalInfo.routerId = item.reworkRouterId;
          } else if (dispositionFunction === 'DEGRADE') {
            _disposalInfo.degradeMaterialId = item.degradeMaterialId;
            _disposalInfo.degradeRevisionCode = item.degradeRevisionCode;
            _disposalInfo.degradeLevel = item.degradeLevel;
            _disposalInfo.degradeDegree = item.degradeDegree;
          } else if (dispositionFunction === 'EO_DISCHARGE') {
            _disposalInfo.workcellId = item.dischargeWorkcellId || null;
            _disposalInfo.workcellName = item.dischargeWorkcellName;
          } else if (dispositionFunction === 'CONCESSION_INTERCEPTION') {
            _disposalInfo.workcellId = item.interceptWorkcellId || null;
            _disposalInfo.workcellName = item.interceptWorkcellName;
            _disposalInfo.operationId = item.concessionInterceptOperationId || null;
            _disposalInfo.operationName = item.concessionInterceptOperationName;
            _disposalInfo.operationName = item.concessionInterceptOperationName;
            // _disposalInfo.routerStepId = item.routerStepId;
            // _disposalInfo.routerStepDesc = item.routerStepDesc;
          } else if (dispositionFunction === 'EO_DISCHARGE') {
            _disposalInfo.workcellId = item.dischargeWorkcellId || null;
            _disposalInfo.workcellName = item.dischargeWorkcellName;
          } else if (dispositionFunction === 'CROSS_WO_INTERCEPT') {
            _disposalInfo.operationId = item.overInterceptOperationId || null;
            _disposalInfo.operationName = item.overInterceptOperationName;
          }
          _disposalDtlList.push(_disposalInfo);
        }
      });
      return {
        disposalObjectId,
        disposalObjectType,
        disposalObjectQty,
        uomId,
        remark,
        disposalDtlList: _disposalDtlList,
      };
    });
    const res = await savePartDisposal.run({
      params: _newData,
      queryParams: {
        inspectTaskId: inspectInfoDS.current.get('inspectTaskId'),
        disposalType: inspectInfoDS.current.get('disposalType'),

        dispositionGroupId: inspectInfoDS.current.get('dispositionGroupId'),
      },
    });
    if (res && res.success) {
      notification.success({});
      await handleFetchPartDisposal();
      partDisposalDS.setState('confirm', 'ok');
    }

    return Promise.resolve(false);
  };

  return (
    <span className="action-link">
      <a onClick={handleOpenPartDisposition} {...othersProps}>
        {disposalTypeDesc}
        {intl.get(`${modelPrompt}.button.partDisposal`).d('对象处置请点击此处')}
      </a>
    </span>
  );
};

export default PartDisposalComponent;
