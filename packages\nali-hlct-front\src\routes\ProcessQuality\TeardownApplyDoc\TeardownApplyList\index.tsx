/**
 * @Description: 拆解申请单-列表页
 * @Author: <EMAIL>
 * @Date: 2023/8/14 10:51
 */
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useDataSetEvent } from 'utils/hooks';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from '../stores';
import { CancelTeardownApplyDoc } from '../services';

const modelPrompt = 'tarzan.hwms.teardownApplyDoc';

interface TeardownApplyListProps extends RouteComponentProps {
  tableDs: DataSet;
}

const TeardownApplyList: FC<TeardownApplyListProps> = props => {
  const { tableDs, history } = props;
  const [selectDocIds, setSelectDocIds] = useState([]);
  const { run: cancelTeardownApplyDoc, loading: cancelLoading } = useRequest(
    CancelTeardownApplyDoc(),
    {
      manual: true,
    },
  );

  useEffect(() => {
    tableDs.query(tableDs.currentPage).then(r => r);
  }, []);

  const handleUpdateSelect = () => {
    const _selectDocIds: any = tableDs.selected.map(_record => _record.get('teardownApplyId'));
    setSelectDocIds(_selectDocIds);
  };

  useDataSetEvent(tableDs, 'batchSelect', handleUpdateSelect);
  useDataSetEvent(tableDs, 'batchUnSelect', handleUpdateSelect);

  const renderStatusTag = (value, record, name) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
        className = 'blue';
        break;
      case 'REVIEWING':
        className = 'orange';
        break;
      case 'REJECT':
        className = 'red';
        break;
      case 'APPROVED':
        className = 'green';
        break;
      default:
        className = 'gray';
    }
    return <Tag color={className}>{record!.getField(name)!.getText()}</Tag>;
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'teardownApplyNum',
        lock: ColumnLock.left,
        width: 150,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/product-teardown/teardown-apply-doc/dist/${record!.get(
                    'teardownApplyId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'materialLotType' },
      { name: 'materialLotCode', width: 230 },
      { name: 'model' },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'productFormCode' },
      { name: 'siteCode' },
      { name: 'prodLineCode' },
      { name: 'teardownReason' },
      { name: 'teardownRemarks', width: 200 },
      { name: 'createdByName' },
      { name: 'sampleDeliveryTime', width: 150, align: ColumnAlign.center },
      { name: 'electricVoltage' },
      {
        name: 'teardownApplyStatus',
        width: 130,
        renderer: ({ value, record }) => renderStatusTag(value, record, 'teardownApplyStatus'),
      },
      { name: 'operationName' },
      { name: 'operationDesc' },
      { name: 'teardownStage' },
      { 
        name: 'teardownTaskNum',
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/disassemble/task-execution-platform/detail/${record!.get(
                    'teardownTaskId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'enclosure' },
    ],
    [],
  );

  const handleAdd = useCallback(() => {
    history.push(`/hwms/product-teardown/teardown-apply-doc/dist/create`);
  }, []);

  const handleCancel = () => {
    cancelTeardownApplyDoc({
      params: selectDocIds,
      onSuccess: () => {
        tableDs.query();
      },
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('拆解申请单')}>
        <Button color={ButtonColor.primary} icon="add" onClick={handleAdd}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <Button onClick={handleCancel} disabled={!selectDocIds.length} loading={cancelLoading}>
          {intl.get(`${modelPrompt}.button.cancel`).d('撤销')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="TeardownApplyList"
          customizedCode="TeardownApplyList"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(TeardownApplyList),
);
