
import React, { FC, useEffect, useState } from 'react';
import { RouteComponentProps } from 'react-router';
import { Button, DataSet, Form, Lov, Select, Table, TextField, Modal, TextArea, DateTimePicker } from 'choerodon-ui/pro';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { getCurrentOrganizationId, getCurrentUser, getCurrentUserId } from 'utils/utils';
import { DataSetSelection, FieldType } from 'choerodon-ui/dataset/data-set/enum';
import Axios from 'axios';
import notification from 'utils/notification';
import { Collapse } from 'choerodon-ui';
import { observer } from 'mobx-react';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { omit } from 'lodash';
import listPageFactory from '../stores/listPageFactory';
import lineTableFactory from '../stores/lineTableFactory';
import toolCodeObjFactory from '../stores/toolCodeObjFactory';
import style from './index.less';


interface HierarchicalAuditProjectProps extends RouteComponentProps {
  tableDs: DataSet;
  lineDs: DataSet;
  toolDs: DataSet;
  customizeTable: any;
}

const modelPrompt = 'tarzan.qms.MeasuringTransferPlatform';
let modal;

// const endUrl = '-138685';
const endUrl = '';

const docType = {
  docType: 'TRANSFER_DOC',
}

const HierarchicalAuditProject: FC<HierarchicalAuditProjectProps> = ({ tableDs, lineDs, toolDs, customizeTable, match: { params }, history }) => {

  const [editing, setEditing] = useState(false); // 编辑状态
  const [submitLoading, setSubmitLoading] = useState(false); // 编辑状态

  useEffect(() => {
    tableDs.reset();
    lineDs.reset();
    getDepartment();
    getUserSite();
    createLine();
    return () => {
      toolDs.removeEventListener('update', ({ value, record, dataSet }) => {
        if (value.length > 0) {
          value.forEach((item) => lineDs.create({ toolCodeObj: item }))
        }
        dataSet.remove(record);
      });
    }
  }, []);

  useEffect(() => {
    if ((params as any).id === 'create') {
      tableDs.create({}, 0);
      setEditing(true);
    }
    else {
      setEditing(false);
      getDetailsMessage()
    }
  }, [(params as any).id]);


  useEffect(() => {
    if (!editing) {
      lineDs.selection = false;
    } else {
      lineDs.selection = DataSetSelection.multiple;
    }
  }, [editing])

  const createLine = () => {
    toolDs.addEventListener('update', ({ value, record, dataSet }) => {
      if (value.length > 0) {
        value.forEach((item) => lineDs.create({ toolCodeObj: item }))
      }
      dataSet.remove(record);
    });
  };




  const getDepartment = () => {
    Axios.get(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/getUnit/ui`).then((res: any) => {
      tableDs.current?.set('departmentId', res.unitId);
      tableDs.current?.set('departmentIdName', res.unitName);
    }).catch((res: any) => {
      return notification.error({
        message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
        description: '',
      })
    })
  }

  const getUserSite = () => {
    Axios.get(`/tznm/v1/${getCurrentOrganizationId()}/mt-user-organization/user/default/site/ui`)
      .then((res: any) => {
        if (res && res.success) {
          const {
            siteId,
            siteName,
          } = res?.rows || {};
          tableDs.current?.set('siteId', siteId);
          tableDs.current?.set('siteIdName', siteName);
          lineDs.setState('siteId', siteId);
          toolDs.setState('siteId', siteId);
        }
      }).catch((res: any) => {
        return notification.error({
          message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
          description: '',
        })
      })
  };
  const getDetailsMessage = async (require = true, applyId?) => {
    if ((params as any).id === 'create' && require) {
      return;;
    }
    await Axios.get(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/list/details/ui`, {
      params: {
        id: applyId || (params as any).id,
      },
    }).then((res: any) => {
      const { qisMsToolChangeLineVOList = [], ...tableData } = res;
      tableDs.loadData([tableData]);
      lineDs.loadData(qisMsToolChangeLineVOList || []);
    }).catch((res: any) => {
      return notification.error({
        message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
        description: '',
      })
    });
  };


  const lineColumn: ColumnProps[] = [
    {
      name: 'toolCodeObj',
      editor: editing,
      minWidth: 150,
    },
    {
      name: 'speciesName',
      minWidth: 150,
    },
    {
      name: 'modelCode',
      minWidth: 150,
    },
    {
      name: 'modelName',
      minWidth: 150,
    },
    {
      name: 'lastVerificationDate',
      minWidth: 150,
    },
    {
      name: 'usingStatus',
      minWidth: 150,
    },
    {
      name: 'verificationStatus',
      minWidth: 150,
    },
    {
      name: 'currentUserId',
      minWidth: 150,
      renderer: ({ record }) => {
        return record?.get('currentUserName')
      },
    },
    {
      name: 'currentUseDepartmentName',
      minWidth: 150,
    },
    {
      name: 'currentProdlineName',
      minWidth: 150,
    },
    {
      name: 'currentProcessName',
      minWidth: 150,
    },
    {
      name: 'currentOtherPosition',
      minWidth: 150,
    },
    {
      name: 'targetRespon',
      editor: editing,
      minWidth: 150,
    },
    {
      name: 'targetUser',
      editor: editing,
      minWidth: 150,
    },
    {
      name: 'targetUseDepartment',
      editor: editing,
      minWidth: 150,
    },
    {
      name: 'targetSite',
      editor: editing,
      minWidth: 150,
    },
    {
      name: 'targetProdline',
      editor: editing,
      minWidth: 150,
    },
    {
      name: 'targetProcess',
      editor: editing,
      minWidth: 150,
    },
    {
      name: 'targetOtherPosition',
      editor: editing,
      minWidth: 150,
    },
  ];

  const handleApproveReject = () => {
    const ds = new DataSet({
      autoCreate: true,
      fields: [{
        name: 'reason',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.modal.reason`).d('驳回原因'),
      }],
      events: {
        update: ({ value }) => {
          tableDs.current?.set('rejectReason', value);
        },
      },
    });

    const handleOk = () => {
      Axios.put(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/submitReject/ui`, tableDs.toData().map(item => ({
        ...item,
        reviewBy: getCurrentUserId(),
        reviewByName: getCurrentUser().realName,
      }))).then(() => {
        getDetailsMessage();
        notification.success({
          message: intl.get(`${modelPrompt}.message.success`).d('操作成功!'),
          description: '',
        })

      }).catch((res: any) => {
        notification.error({
          message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
          description: '',
        })
      })
      modal.close();
    };
    modal = Modal.open({
      title: intl.get(`${modelPrompt}.modal.reject`).d('审批驳回'),
      children: <Form dataSet={ds}>
        <TextArea name='reason' />
      </Form>,
      onOk: handleOk,
      onCancel: () => modal.close(),
    });
  };

  const handleSave = async (save) => {
    const headerFlag = await tableDs.current?.validate(true);
    const lineFlag = await lineDs.validate();
    if (!headerFlag || !lineFlag) {
      return null;
    }
    if (lineDs.toData().length === 0) {
      return notification.warning({
        message: intl.get(`${modelPrompt}.message.add.lineMessage`).d('请添加申请进行转移的量具清单！'),
        description: '',
      });
    }
    const ids = lineDs.toData().map((item: any) => item.msToolManageId);
    const setIds = Array.from(new Set(ids));
    if (ids.length !== setIds.length) {
      return notification.warning({
        message: intl.get(`${modelPrompt}.message.add.sameMessage`).d('请勿重复添加相同量具！'),
        description: '',
      });
    }

    if (lineDs.toData().filter((item: any) => item.targetResponId || item.targetUserId || item.targetUseDepartmentId || item.targetSiteId || item.targetProdlineId || item.targetProcessId || item.targetOtherPosition).length !== lineDs.toData().length) {
      return notification.warning({
        message: intl.get(`${modelPrompt}.message.add.pleaseInput`).d('请输入目标责任人/目标使用人/目标使用部门目标工厂/目标产线/其他位置信息'),
        description: '',
      });
    }
    Axios.post(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/list/saveOrUpdate/ui`, {
      ...tableDs.current?.toJSONData(),
      ...docType,
      qisMsToolChangeLineVOList: lineDs.toJSONData().map((item: any) => ({
        ...item,
        applyId: tableDs.current?.get('applyId'),
      })).filter((item: any) => item._status !== 'delete'),
    }).then((res: any) => {
      setEditing(false);
      if (save) {
        history.push(`/hwms/measuring-transfer-platform/detail/${res.applyId}`);
        getDetailsMessage();
        notification.success({
          message: intl.get(`${modelPrompt}.message.success`).d('操作成功!'),
          description: '',
        })
      }
    }).catch((res: any) => {
      return notification.error({
        message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
        description: '',
      })
    })
  };

  const handleSubmit = async () => {
    const headerFlag = await tableDs.current?.validate(true);
    const lineFlag = await lineDs.validate();
    if (!headerFlag || !lineFlag) {
      return null;
    }
    if (lineDs.toData().length === 0) {
      return notification.warning({
        message: intl.get(`${modelPrompt}.message.add.lineMessage`).d('请添加申请进行申请的量具清单！'),
        description: '',
      });
    }
    const ids = lineDs.toData().map((item: any) => item.msToolManageId);
    const setIds = Array.from(new Set(ids));
    if (ids.length !== setIds.length) {
      return notification.warning({
        message: intl.get(`${modelPrompt}.message.add.sameMessage`).d('请勿重复添加相同量具！'),
        description: '',
      });
    }

    if (lineDs.toData().filter((item: any) => item.targetResponId || item.targetUserId || item.targetUseDepartmentId || item.targetSiteId || item.targetProdlineId || item.targetProcessId || item.targetOtherPosition).length !== lineDs.toData().length) {
      return notification.warning({
        message: intl.get(`${modelPrompt}.message.add.pleaseInput`).d('请输入目标责任人/目标使用人/目标使用部门目标工厂/目标产线/其他位置信息'),
        description: '',
      });
    }

    if ((params as any).id !== 'create') {
      return Axios.put(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/submit/ui`, tableDs.toData()).then(() => {
        getDetailsMessage();
        setEditing(false);
        notification.success({
          message: intl.get(`${modelPrompt}.message.success`).d('操作成功!'),
          description: '',
        })
      }).catch((res: any) => {
        return notification.error({
          message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
          description: '',
        })
      })
    }
    setSubmitLoading(true);
    await Axios.post(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/list/saveOrUpdate/ui`, {
      ...tableDs.current?.toJSONData(),
      ...docType,
      qisMsToolChangeLineVOList: lineDs.toJSONData().map((item: any) => ({
        ...item,
        applyId: tableDs.current?.get('applyId'),
      })).filter((item: any) => item._status !== 'delete'),
    }).then(async(res: any) => {
      await Axios.put(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/submit/ui`, [omit(res, ['qisMsToolChangeLineVOList'])]).then(() => {
        history.push(`/hwms/measuring-transfer-platform/detail/${res.applyId}`);
        notification.success({
          message: intl.get(`${modelPrompt}.message.success`).d('操作成功!'),
          description: '',
        })
      }).catch((res: any) => {
        return notification.error({
          message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
          description: '',
        })
      }).finally(()=>{
        setSubmitLoading(false);
      })
    }).catch((res: any) => {
      return notification.error({
        message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
        description: '',
      })
    })
    
  };

  const handleApprovePass = () => {
    Axios.put(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/submitTransfer/ui`, tableDs.toData().map(item => ({
      ...item,
      reviewBy: getCurrentUserId(),
      reviewByName: getCurrentUser().realName,
    }))).then(() => {
      getDetailsMessage();
      notification.success({
        message: intl.get(`${modelPrompt}.message.success`).d('操作成功!'),
        description: '',
      })
    }).catch((res: any) => {
      return notification.error({
        message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
        description: '',
      })
    })
  };

  const handleCancel = () => {
    Axios.put(`${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/cancel/ui`, tableDs.toData()).then(() => {
      getDetailsMessage();
      notification.success({
        message: intl.get(`${modelPrompt}.message.success`).d('操作成功!'),
        description: '',
      })
    }).catch((res: any) => {
      return notification.error({
        message: res?.message || intl.get(`${modelPrompt}.message.error`).d('操作失败!'),
        description: '',
      })
    })
  };

  const btnRender = () => {
    if ((params as any).id === 'create') {
      return '';
    }
    if (editing) {
      return <Button onClick={() => {
        lineDs.reset();
        tableDs.reset();
        setEditing(false)
      }}>
        {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
      </Button>
    }
    if (['NEW', 'REJECTED'].includes(tableDs.current?.get('applicationDocStatus'))) {
      return <Button color={ButtonColor.primary} disabled={tableDs.current?.get('createdBy') !== getCurrentUserId()}  onClick={() => setEditing(true)}>
        {intl.get(`${modelPrompt}.button.edit`).d('编辑')}
      </Button>
    }
  }

  const rednerBtn = () => {
    if (editing) {
      return '';
    }
    return <>
      {
        tableDs.current?.get('applicationDocStatus') === 'TO_APPROVAL' ?
          <>
            <PermissionButton
              type="c7n-pro"
              className={style['approve-pass-btn']}
              permissionList={[
                {
                  code: `qms.measuring.transfer.platform.button.approvePass`,
                  type: 'button',
                  meaning: '审批通过',
                },
              ]}
              onClick={handleApprovePass}
            >
              {intl.get(`${modelPrompt}.button.pass`).d('审批通过')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              className={style['approve-reject-btn']}
              permissionList={[
                {
                  code: `qms.measuring.transfer.platform.button.ApproveReject`,
                  type: 'button',
                  meaning: '审批驳回',
                },
              ]}
              onClick={handleApproveReject}
            >
              {intl.get(`${modelPrompt}.button.Approvereject`).d('审批驳回')}
            </PermissionButton>
          </> :
          ''
      }
      {
        (params as any).id !== 'create' ?
          <Button
            disabled={
              !['NEW', 'REJECTED'].includes(tableDs.current?.get('applicationDocStatus')) ||
              tableDs.current?.get('createdBy') !== getCurrentUserId()
            }
            onClick={handleCancel}
          >
            {intl.get(`${modelPrompt}.button.cancel`).d('取消申请')}
          </Button> : ''
      }
    </>
  };

  return (
    // 替换产品类名
    <div className="hmes-style">
      <Header title={intl.get(`modelPrompt.title`).d('量具转移平台')} backPath='/hwms/measuring-transfer-platform/list'>
        {
          editing ?
            <Button
              color={ButtonColor.primary}
              disabled={!['NEW', 'REJECTED'].includes(tableDs.current?.get('applicationDocStatus'))}
              onClick={handleSave}
            >
              {intl.get(`${modelPrompt}.button.save`).d('保存')}
            </Button> :
            ''
        }
        {
          btnRender()
        }
        <Button
          color={ButtonColor.primary}
          disabled={!['NEW', 'REJECTED'].includes(tableDs.current?.get('applicationDocStatus')) || tableDs.current?.get('createdBy') !== getCurrentUserId()}
          onClick={handleSubmit}
          loading={submitLoading}
        >
          {intl.get(`${modelPrompt}.button.submit`).d('提交')}
        </Button>
        {
          rednerBtn()
        }
      </Header>
      <Content>
        <Form dataSet={tableDs} columns={3} labelWidth={121}>
          <TextField name='applicationDocNum' disabled />
          <Select name='applicationDocStatus' disabled />
          <Lov name='modelSite' disabled />
          <Lov name='departmentUser' disabled />
          <Lov name='companyUnit' disabled />
          <DateTimePicker name='creationDate' disabled />
          {
            tableDs.current?.get('applicationDocStatus') === 'NEW' ? '' :
              <>
                <TextField name='reviewByName' disabled />
                <DateTimePicker name='reviewDate' disabled />
                <TextField name='rejectReason' disabled />
              </>
          }
        </Form>
        <Form dataSet={tableDs} labelWidth={121}>
          <TextField name='remark' disabled={!editing} />
        </Form>
        <Collapse defaultActiveKey={['lineTable']}>
          <Collapse.Panel header={intl.get(`${modelPrompt}.collapse.panel`).d('量具清单')} key='lineTable'>
            {customizeTable(
              {
                filterCode: `${BASIC.CUSZ_CODE_BEFORE}.MEASURING_SCRAP_PLATFORM.QUERY`,
                code: `${BASIC.CUSZ_CODE_BEFORE}.MEASURING_SCRAP_PLATFORM.TABLE`,
              },
              <Table
                buttons={[
                  'delete',
                  <Lov
                    dataSet={toolDs}
                    name="toolCodeObjs"
                    mode={ViewMode.button}
                    clearButton={false}
                    icon="add"
                    noCache
                    disabled={!editing || !['NEW', 'REJECTED'].includes(tableDs.current?.get('applicationDocStatus'))}
                  >
                    {intl.get(`${modelPrompt}.table.header.button.add`).d('新增')}
                  </Lov>,
                ] as any}
                dataSet={lineDs}
                columns={lineColumn}
                customizedCode="MeasuringScrapPlatformDetails"
                queryBar={TableQueryBarType.none}
              />,
            )}
          </Collapse.Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = listPageFactory();
      const lineDs = lineTableFactory();
      const toolDs = toolCodeObjFactory();
      return {
        lineDs,
        tableDs,
        toolDs,
      };
    },
    { cacheState: false },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.MEASURING_SCRAP_PLATFORM.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.MEASURING_SCRAP_PLATFORM.TABLE`],
    })(observer(HierarchicalAuditProject) as any),
  ),
);