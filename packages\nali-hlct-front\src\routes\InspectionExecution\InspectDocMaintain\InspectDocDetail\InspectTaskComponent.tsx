/**
 * @Description: 检验单维护-详情页 检验任务信息组件
 * @Author: <EMAIL>
 * @Date: 2023/2/17 16:19
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Attachment, Form, DataSet, Output, Table } from 'choerodon-ui/pro';
import { Badge, Collapse, Tag } from 'choerodon-ui';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { BASIC } from 'hcm-components-front/lib/utils/config';
import { AttributeDrawer } from 'hcm-components-front/lib/components/tarzan-ui';
import { taskLineDS } from '../stores/InspectTaskDtlDS';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.inspectDocMaintain';

const PanelTable = observer(({ record, customizeTable, custConfig }) => {
  const taskLineDs = useMemo(
    () =>
      new DataSet({
        ...taskLineDS(),
        data: record?.get('docLineList'),
      }),
    [],
  );

  const renderValueList = (name, record) => {
    const value = record.get(name);
    switch (record.get('dataType')) {
      case 'VALUE':
      case 'VALUE_LIST':
      case 'CALCULATE_FORMULA':
        return (
          value?.length &&
          value[0].dataValue &&
          value.map(item => (
            <Tag className="hcm-tag-blue" key={item.dataValue}>
              {item.dataValue}
            </Tag>
          ))
        );
      default:
        return value?.length && value[0].dataValue;
    }
  };

  const renderInspectValue = record => {
    const data = record?.get('actDtlList');
    if (!data || !data?.length) {
      return null;
    }

    return data.map(item => {
      const formatInspectValue = item => {
        const res = item.inspectResult?.split(',');
        const val = item.inspectValue?.split(',');

        const valueColor = i => {
          switch (res[i]) {
            case 'OK':
              return 'rgba(32, 212, 137, 1)';
            case 'NG':
              return 'rgba(230, 46, 163, 1)';
            default:
              return 'rgba(0, 0, 0, 0.85)';
          }
        };

        return val.map((i, index) => {
          if (index === val?.length - 1) {
            return <span style={{ color: valueColor(index) }}>{i}</span>;
          }
          return <span style={{ color: valueColor(index) }}>{i},</span>;
        });
      };

      return (
        <Tag>
          {item?.inspectObjectCode && <span>{item?.inspectObjectCode}</span>}(
          {formatInspectValue(item)})
        </Tag>
      );
    });
  };

  // @ts-ignore
  const taskLineColumns: ColumnProps[] = [
    {
      name: 'sequence',
      width: 100,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemCode',
      width: 150,
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItemDesc',
      width: 150,
    },
    { name: 'inspectItemTypeDesc', width: 120 },
    { name: 'inspectGroupDesc', width: 120 },
    { name: 'itemSourceCreateDesc', width: 120 },
    { name: 'okQty' },
    { name: 'ngQty' },
    {
      name: 'inspectResultDesc',
      renderer: ({ record, value }) => {
        switch (record?.get('inspectResult')) {
          case 'OK':
            return <Tag color="green">{value}</Tag>;
          case 'NG':
            return <Tag color="magenta">{value}</Tag>;
          default:
            return null;
        }
      },
    },
    {
      name: 'inspectValue',
      width: 150,
      renderer: ({ record }) => renderInspectValue(record),
    },
    { name: 'actRemark' },
    { name: 'inspectBasis' },
    { name: 'inspectMethodDesc' },
    { name: 'technicalRequirement' },
    { name: 'inspectToolDesc' },
    { name: 'qualityCharacteristicDesc' },
    { name: 'dataTypeDesc' },
    {
      name: 'trueValueList',
      width: 200,
      renderer: ({ record, name }) => renderValueList(name, record),
    },
    {
      name: 'falseValueList',
      width: 200,
      renderer: ({ record, name }) => renderValueList(name, record),
    },
    {
      name: 'warningValueList',
      width: 200,
      renderer: ({ record, name }) => renderValueList(name, record),
    },
    { name: 'defaultValue' },
    { name: 'dataQtyDispositionDesc', width: 120 },
    { name: 'uomName' },
    { name: 'decimalNumber' },
    { name: 'processModeDesc' },
    { name: 'enterMethodDesc', width: 150 },
    {
      name: 'requiredFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    { name: 'dataQty' },
    // { name: 'formula' },
    { name: 'samplingMethodDesc' },
    { name: 'samplingQty' },
    {
      name: 'acceptStandardAc',
      renderer: ({ record }) => {
        if (record?.get('acceptStandard')) {
          const data = JSON.parse(record?.get('acceptStandard'));
          return data?.ac;
        }
        return null;
      },
    },
    {
      name: 'acceptStandardRe',
      renderer: ({ record }) => {
        if (record?.get('acceptStandard')) {
          const data = JSON.parse(record?.get('acceptStandard'));
          return data?.re;
        }
        return null;
      },
    },
    {
      name: 'sameGroupIdentification',
      align: ColumnAlign.center,
      width: 120,
      // renderer: ({ value }) => (
      //   <Badge
      //     status={value === 'Y' ? 'success' : 'error'}
      //     text={
      //       value === 'Y'
      //         ? intl.get(`tarzan.common.label.enable`).d('启用')
      //         : intl.get(`tarzan.common.label.disable`).d('禁用')
      //     }
      //   />
      // ),
    },
    {
      name: 'destructiveExperimentFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'outsourceFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    { name: 'actionItem' },
    { name: 'employeePosition' },
    {
      name: 'inspectFrequencyDesc',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
        if (inspectFrequencyShow) {
          inspectFrequencyShow = value.replace('M', record?.get('m') || 'M');
          inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
          return inspectFrequencyShow;
        }
        return value;
      },
    },
    { name: 'ncGroupDesc' },
    { name: 'lineRemark' },
    { name: 'actEnclosure', lock: ColumnLock.right },
    { name: 'lineEnclosure', lock: ColumnLock.right },
    {
      name: 'attr',
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      title: intl.get(`${modelPrompt}.extend`).d('扩展属性'),
      renderer: ({ record }) => (
        <AttributeDrawer
          serverCode={BASIC.TARZAN_SAMPLING}
          className="org.tarzan.qms.domain.entity.MtInspectInfo"
          kid={record?.get('inspectItemId')}
          canEdit={false}
          disabled={!record?.get('inspectItemId')}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.LINE_ATTR`}
          custConfig={custConfig}
          type="text"
        />
      ),
    },
  ];

  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.LINE_BASIC`,
    },
    <Table
      dataSet={taskLineDs}
      columns={taskLineColumns}
      highLightRow={false}
      customizedCode="inspectDocMaintain-panelTable"
    />,
  );
});

const InspectTaskComponent = props => {
  const { customizeForm, customizeTable, custConfig, history, taskInfoDs, defaultKey } = props;
  const [activeKey, setActiveKey] = useState<any>(defaultKey);

  useEffect(() => setActiveKey(defaultKey), [defaultKey]);

  const CollapsePanelTitle = observer(({ record }) => {
    const renderStatusTag = record => {
      const { inspectTaskStatus, inspectTaskStatusDesc } = record.toData();
      switch (inspectTaskStatus) {
        case 'RELEASED':
          return <Tag color="orange">{inspectTaskStatusDesc}</Tag>;
        case 'CANCEL':
          return <Tag color="grey">{inspectTaskStatusDesc}</Tag>;
        case 'COMPLETED':
          return <Tag color="blue">{inspectTaskStatusDesc}</Tag>;
        default:
          return <Tag color="yellow">{inspectTaskStatusDesc}</Tag>;
      }
    };

    const handleJump = record => {
      if (record?.get('operatingEnd') === 'PC-2') {
        history.push(`/hwms/inspection-platform-workshop/dist/${record?.get('inspectTaskId')}`);
      } else {
        history.push(`/hwms/inspection-platform/dist/${record?.get('inspectTaskId')}`);
      }
    };

    return (
      <span>
        <a className={styles['panel-title-code']} onClick={() => handleJump(record)}>
          {record.get('inspectTaskCode')}
        </a>
        {renderStatusTag(record)}
        <span className={styles['panel-title-item']}>
          {intl.get(`${modelPrompt}.inspectTask.inspectTaskType`).d('检验任务类别：')}
          {record.get('inspectTaskTypeDesc')}
        </span>
        <span className={styles['panel-title-item']}>
          {intl.get(`${modelPrompt}.inspectTask.inspectorName`).d('检验员：')}
          {record.get('inspectorName') || ' '}
        </span>
        {(record.get('actualStartTime') || record.get('actualEndTime')) && (
          <span className={styles['panel-title-item']}>
            {intl.get(`${modelPrompt}.inspectTask.inspectionTime`).d('检验时间：')}
            {record.get('actualStartTime') || ' '}{' '}
            {record.get('actualEndTime')
              ? `~${record.get('actualEndTime')}`
              : record.get('actualEndTime')}
          </span>
        )}
        {record?.get('inspectResult') === 'OK' && (
          <Tag color="green">{record?.get('inspectResultDesc')}</Tag>
        )}
        {record?.get('inspectResult') === 'NG' && (
          <Tag color="magenta">{record?.get('inspectResultDesc')}</Tag>
        )}
        {record?.get('sourceTaskCode') && (
          <span className={styles['panel-title-item']}>
            <Tag color="blue">
              {intl.get(`${modelPrompt}.inspectTask.sourceTaskCode`).d('复检 关联任务:')}
              {record.get('sourceTaskCode') || ' '}
            </Tag>
          </span>
        )}
      </span>
    );
  });

  const handleChangePanel = props => setActiveKey(props);

  const panelDetailEnclosure: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const displayQtyValue = text => {
    if (text?.value && text?.value !== '') {
      const colorList = ['#11d954', '#ff3333', '#fbad00'];
      const textList = text.value.split('/');
      return (
        <>
          {textList.map((text, index) => {
            return (
              <span
                style={{
                  color: colorList[index],
                }}
              >
                {` ${text} `}
              </span>
            );
          })}
        </>
      );
    }
    return <></>;
  };

  return (
    <Collapse activeKey={activeKey} onChange={handleChangePanel}>
      {taskInfoDs?.length &&
        taskInfoDs.map(record => (
          <Panel
            key={String(record.get('inspectTaskId'))}
            header={<CollapsePanelTitle record={record} />}
            className={styles['panel-item']}
            extra={
              <AttributeDrawer
                serverCode={BASIC.TARZAN_SAMPLING}
                className="org.tarzan.qms.domain.entity.MtInspectInfoDtl"
                kid={record?.get('inspectTaskId')}
                canEdit={false}
                disabled={!record?.get('inspectTaskId')}
                custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.TASK_ATTR`}
                custConfig={custConfig}
                type="text"
              />
            }
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.TASK_BASIC`,
              },
              <Form record={record} labelWidth="auto" columns={6}>
                <Output name="displayQty" renderer={displayQtyValue} />
                <Output name="remark" />
                <Attachment readOnly {...panelDetailEnclosure} />
              </Form>,
            )}
            <PanelTable record={record} customizeTable={customizeTable} custConfig={custConfig} />
          </Panel>
        ))}
    </Collapse>
  );
};

export default InspectTaskComponent;
