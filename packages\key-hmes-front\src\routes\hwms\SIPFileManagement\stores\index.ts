/*
 * @Description: SIP文件管理-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-22 09:36:29
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-18 10:41:09
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { BASIC } from '@utils/config';
import moment from "moment/moment";

const modelPrompt = 'tarzan.qms.inspectExecute.SIPFileManagement';
const tenantId = getCurrentOrganizationId();

const tableDS: (tabType: string) => DataSetProps = (tabType) => ({
  selection: DataSetSelection.multiple,
  autoQuery: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
      lovPara: { tenantId },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'sipCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sipCode`).d('SIP编号'),
    },
    {
      name: 'sipName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sipName`).d('SIP名称'),
    },
    {
      name: 'sipType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sipType`).d('文件类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.SIP_FILETYPE_THIRD_LEVEL',
    },
    {
      name: 'sipStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sipStatus`).d('文件状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.SIP_STATUS',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentLov`).d('编制部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: { tenantId },
    },
    {
      name: 'authoredDpt',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsLov`).d('编制人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: { tenantId },
    },
    {
      name: 'authoredBy',
      bind: 'responsLov.id',
    },
    {
      name: 'editDateBegin',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.editDateBegin`).d('编制日期从'),
      max: 'editDateEnd',
    },
    {
      name: 'editDateEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.editDateEnd`).d('编制日期至'),
      min: 'editDateBegin',
    },
    // {
    //   name: 'currentFlag',
    //   type: FieldType.string,
    //   defaultValue: 'Y',
    // },
  ],

  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      required: true,
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record }) => !['NEW', 'REJECTED'].includes(record?.get('sipStatus')),
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      bind: 'siteLov.siteName',
    },
    {
      name: 'sipCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sipCode`).d('SIP编码'),
      disabled: true,
    },
    {
      name: 'version',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.version`).d('版本号'),
      defaultValue: 'A0',
      disabled: true,
    },
    {
      name: 'sipName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sipName`).d('SIP名称'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => !['NEW', 'REJECTED'].includes(record?.get('sipStatus')),
      },
    },
    {
      name: 'sipStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sipStatus`).d('文件状态'),
      defaultValue: 'NEW',
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.SIP_STATUS',
      disabled: true,
    },
    {
      name: 'sipType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sipType`).d('文件类型'),
      required: true,
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.SIP_FILETYPE_THIRD_LEVEL',
      dynamicProps: {
        disabled: ({ record }) => !['NEW', 'REJECTED'].includes(record?.get('sipStatus')),
      },
    },
    {
      name: 'sipFileUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.sipFileUuid`).d('SIP文件'),
      required: true,
      bucketName: 'qms',
      dynamicProps: {
        disabled: ({ record }) => !['NEW', 'REJECTED', 'AMENDING'].includes(record?.get('sipStatus')) && record?.getState('operationType') !== 'update',
        readOnly: ({ record }) => !['NEW', 'REJECTED', 'AMENDING'].includes(record?.get('sipStatus')) && record?.getState('operationType') !== 'update',
      },
    },
    // {
    //   name: 'upUuid',
    //   type: FieldType.attachment,
    //   label: intl.get(`${modelPrompt}.upUuid`).d('上传文档'),
    //   bucketName: 'qms',
    // },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentLov`).d('编制部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'unitName',
      valueField: 'unitId',
      lovPara: { tenantId },
      dynamicProps: {
        disabled: ({ record }) => !['NEW', 'REJECTED', 'AMENDING'].includes(record?.get('sipStatus')) && record?.getState('operationType') !== 'update',
      },
    },
    {
      name: 'authoredDpt',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'authoredDptName',
      label: intl.get(`${modelPrompt}.authoredDptName`).d('编制部门'),
      bind: 'departmentLov.unitName',
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsLov`).d('编制人'),
      lovCode: 'YP.QIS.UNIT_LIMIT_EMPLOYEE',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'realName',
      dynamicProps: {
        lovPara: ({record}) => {
          return {
            tenantId,
            unitId: record?.get('authoredDpt'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('authoredDpt') || (!['NEW', 'REJECTED', 'AMENDING'].includes(record?.get('sipStatus')) && record?.getState('operationType') !== 'update');
        },
      },
    },
    {
      name: 'authoredBy',
      bind: 'responsLov.id',
    },
    {
      name: 'authoredByName',
      label: intl.get(`${modelPrompt}.authoredByName`).d('编制人'),
      bind: 'responsLov.realName',
    },
    {
      name: 'editDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editDate`).d('编制日期'),
      required: true,
      defaultValue: moment(moment().format('YYYY-MM-DD 00:00:00')),
      dynamicProps: {
        disabled: ({ record }) => !['NEW', 'REJECTED', 'AMENDING'].includes(record?.get('sipStatus')) && record?.getState('operationType') !== 'update',
      },
    },
    {
      name: 'remarks',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remarks`).d('备注'),
      dynamicProps: {
        disabled: ({ record }) => !['NEW', 'REJECTED', 'AMENDING'].includes(record?.get('sipStatus')) && record?.getState('operationType') !== 'update',
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sip-file?tabType=${tabType}`,
        method: 'GET',
      };
    },
  },
  events: {
    update: ({ name, record }) => {
      if (name === 'departmentLov') {
        record.set('responsLov', null);
      }
    },
  },
});

const historyDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'version',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.version`).d('版本号'),
    },
    {
      name: 'sipStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sipStatus`).d('文件状态'),
      lookupCode: 'YP.QIS.SIP_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'authoredDptName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.authoredDptName`).d('编制部门'),
    },
    {
      name: 'authoredByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.authoredByName`).d('编制人'),
    },
    {
      name: 'editDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editDate`).d('编制日期'),
    },
    {
      name: 'sipFileUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.sipFileUuid`).d('SIP文件'),
      bucketName: 'qms',
    },
    {
      name: 'remarks',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remarks`).d('备注'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sip-file/list-version`,
        method: 'GET',
      };
    },
  },
});

export { tableDS, historyDS};
