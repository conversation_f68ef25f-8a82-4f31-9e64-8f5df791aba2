/*
 * @Description: 审批历史ds
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-04-07 14:56:39
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */
import { HALM_WKF } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const organizationId = getCurrentOrganizationId();

function approvalHistoryDS() {
  return {
    paging: false,
    transport: {
      read: () => {
        return {
          url: `${HALM_WKF}/v1/${organizationId}/personal-process/approve-history`,
          method: 'GET',
        };
      },
    },
  };
}

export { approvalHistoryDS };
