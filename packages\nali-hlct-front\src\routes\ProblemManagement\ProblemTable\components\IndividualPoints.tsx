import React from 'react';
import { Content } from 'components/Page';
import intl from 'utils/intl';
import { Row, Col } from 'choerodon-ui';
import './index.less';

const modelPrompt = 'problem.table';

const IndividualPoints = (props) => {
  const {
    // match: { path },
    ds,
    // history,
  } = props;

  // const handleAdd = useCallback(() => {
  //   history.push(`/page_route/create`);
  // }, []);

  return (
    <Content>
      <Row>
        <Col span={21}>
          <span className='individual_points_title'>{intl.get(`${modelPrompt}.individual_points`).d('个人积分')}</span>
        </Col>
        <Col span={3}>
          <a style={{ fontSize: '14px' }}>{intl.get(`${modelPrompt}.more`).d('更多')}</a>
        </Col>
      </Row>
      <div className='individual_points_container'>
        <Row className='individual_points_item'>
          <Col span={12}>
            <span>{intl.get(`${modelPrompt}.monthScore`).d('月度得分')}：</span>
            <span style={{ padding: '0 30% 0 0' }}>{ds.current.get('monthScore') || ''}</span>
          </Col>
          <Col span={12}>
            <span>{intl.get(`${modelPrompt}.monthRank`).d('月度排名')}：</span>
            <span>{ds.current.get('monthRank') || ''}</span>
          </Col>
        </Row>
        <Row className='individual_points_item'>
          <Col span={12}>
            <span>{intl.get(`${modelPrompt}.quarterScore`).d('季度得分')}：</span>
            <span style={{ padding: '0 30% 0 0' }}>{ds.current.get('quarterScore') || ''}</span>
          </Col>
          <Col span={12}>
            <span>{intl.get(`${modelPrompt}.quarterRank`).d('季度排名')}：</span>
            <span>{ds.current.get('quarterRank') || ''}</span>
          </Col>
        </Row>
        <Row className='individual_points_item'>
          <Col span={12}>
            <span>{intl.get(`${modelPrompt}.yearScore`).d('年度得分')}：</span>
            <span style={{ padding: '0 30% 0 0' }}>{ds.current.get('yearScore') || ''}</span>
          </Col>
          <Col span={12}>
            <span>{intl.get(`${modelPrompt}.yearRank`).d('年度排名')}：</span>
            <span>{ds.current.get('yearRank') || ''}</span>
          </Col>
        </Row>
      </div>
    </Content>
  );
}

export default IndividualPoints;