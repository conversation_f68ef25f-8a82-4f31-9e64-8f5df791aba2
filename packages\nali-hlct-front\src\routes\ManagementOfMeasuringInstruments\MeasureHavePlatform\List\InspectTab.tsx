/**
 * @Description: 量具汇总查询界面
 */

import React, { useMemo} from 'react';
import { Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import {ColumnAlign, ColumnLock, TableQueryBarType} from 'choerodon-ui/pro/lib/table/enum';
import { Tag} from "choerodon-ui";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
// const modelPrompt = 'tarzan.inspectExecute.MeasureHavePlatform';

const ApplyTab = props => {
  const { inspectDs, history  } = props;

  // useDataSetEvent(inspectDs, 'load', resetHeaderDetail);

  // const queryLineTable = applicationDocId => {
  //   if (applicationDocId) {
  //     lineDs.setQueryParameter('applicationDocId', applicationDocId);
  //     lineDs.query();
  //   }
  // };

  const renderStatusTag = (value, record, name) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'IN_APPROVAL':
        className = 'orange';
        break;
      case 'REJECTED':
        className = 'red';
        break;
      case 'RELEASED':
        className = 'purple';
        break;
      case 'COMPLETED':
        className = 'green';
        break;
      case 'NEW':
        className = 'blue';
        break;
      default:
        className = 'gray';
    }
    return <Tag color={className}>{record!.getField(name)!.getText()}</Tag>;
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectDocNum',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hmes/measure-have/platform/inspect/${record!.get(
                    'verificationMethod')}/${record!.get(
                      'inspectDocId',
                    )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'inspectDocStatus',
        width: 130,
        renderer: ({ value, record }) => renderStatusTag(value, record, 'inspectDocStatus'),
      },
      { name: 'siteName', width: 180, lock: ColumnLock.left },
      { name: 'docType' },
      { name: 'toolCode', align: ColumnAlign.center, width: 150 },
      { name: 'responName' },
      { name: 'speciesName' },
      { name: 'modelCode' },
      { name: 'modelName' },
      { name: 'verificationMethod' },
      { name: 'inspectorName' },
      { name: 'usingStatus', width: 150 },
      { name: 'applicationCreationDate' },
      { name: 'departmentName' },
      { name: 'applicationDocNum',
        width: 130,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hmes/measure-have/platform/dist/${record!.get(
                    'applicationDocId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'applicationCreatedByName' },
      { name: 'inspectPlace' },
      { name: 'inspectResult' },
      { name: 'appearanceResult' },
      { name: 'ngReason' },
      {
        name: 'servicedFlag',
        widtn: 150,
        align: ColumnAlign.center,
        renderer: ({ record, value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={record!.getField('servicedFlag')!.getText()}
            />
          )
        },
      },
      { name: 'completionTime', align: ColumnAlign.center, width: 150},
      { name: 'remark' },
      { name: 'createdByName' },
      { name: 'creationDate' },
      {
        name: 'cancelReason',
      },
      {
        name: 'cancelDate',
        width: 180,
      },
      { name: 'inspectReportUuid' },
      { name: 'graphUuid' },
    ];
  }, []);

  return (
    <div>
      <Table
        queryBar={TableQueryBarType.filterBar}
        queryBarProps={{
          fuzzyQuery: false,
        }}
        dataSet={inspectDs}
        columns={headColumns}
        searchCode="msPlatform2"
        customizedCode="msPlatform-listHeader2"
      />
    </div>
  );
};

export default ApplyTab;
