/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2021-12-14 16:12:24
 * @LastEditTime: 2022-03-31 19:46:01
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.receive.miscellaneousMes';
// const endUrl = '-30711';
const endUrl = '';

const tenantId = getCurrentOrganizationId();

const materialLotDS = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  selection: false,
  paging: false,
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/wms-miscellaneous/material-lot/get/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentification`).d('物料批标识'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatusDesc`).d('物料批状态'),
    },
    {
      name: 'containerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerIdentification`).d('容器'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumActualQty`).d('数量'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantityStatus`).d('质量状态'),
    },
    {
      name: 'sumActualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumActualQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locator`).d('库位'),
    },
    {
      name: 'sourceInsCreateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceInsCreateDate`).d('执行时间'),
    },
    {
      name: 'sourceInsCreatedBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceInsCreatedBy`).d('执行人'),
    },
  ],
});

export { materialLotDS };
