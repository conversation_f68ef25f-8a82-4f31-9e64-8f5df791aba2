/* eslint-disable no-unused-expressions */
// @ts-nocheck
/**
 * @Description: 工序作业平台
 * @Author: <<EMAIL>>
 * @Date: 2023-2-26 18:50:17
 */
import React, { useEffect, useState, useCallback } from 'react';
import { closeTab } from 'utils/menuTab';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { Divider } from 'choerodon-ui';
import { DataSet } from 'choerodon-ui/pro';
import { Icon } from 'hzero-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import request from 'utils/request';
import cardSvg from '@/assets/icons/operation.svg';
import footerBackground from '@/assets/operationPlatformCard/footerBackground.svg';
import { queryLov, queryLovData } from '../../../services/api';
import './index.module.less';
import { useOperationPlatform } from '../contextsStore';
import C7nModal from '../C7nModal';
import EnterInfo from './EnterInfo';

import { enterModalDS } from './stores';
import { HandleSave } from '../../ProcessingStrategyConfig/services';

let enterModal;

const tenantId = getCurrentOrganizationId();
const LoginModal = props => {
  const Modal = C7nModal;
  const { setLoginWkcInfo, enterModalDs } = props;
  const { dispatch, logined, workCellList, enterInfo } = useOperationPlatform();

  const [fetchLoading, setFetchLoading] = useState(false);
  const [operationSelectedId, setOperationSelectedId] = useState(''); // 选择工艺ID
  const [workcellSelectedId, setWorkcellSelectedId] = useState(''); // 选择工位ID
  const [operationList, setOperationList] = useState([]); // 全部工艺
  const [currentWorlCellList, setCurrentWorlCellList] = useState([]); // 当前工位
  const [currentWorkcellSelectedId, setCurrentWorkcellSelectedId] = useState(''); // 选择工位ID
  const handleSelectWorkcell = useCallback(
    cardInfo => {
      const _cardId = String(cardInfo.workcellId);
      if (workcellSelectedId === _cardId) {
        setWorkcellSelectedId(null);
        setOperationList([]);
        operationSelectedId(null);
        enterModalDs.current.set('workStationLov', null);
        return;
      }
      setWorkcellSelectedId(_cardId);
      setCurrentWorkcellSelectedId(null);
      enterModalDs.current.set('workStationLov', cardInfo);
      onFetchWorkStation(cardInfo.workcellCode, 'click');
    },
    [workcellSelectedId],
  );

  const handleSelectCurrentWorkcell = useCallback(
    cardInfo => {
      const _cardId = String(cardInfo.workcellId);
      if (currentWorkcellSelectedId === _cardId) {
        setCurrentWorkcellSelectedId(null);
        setOperationList([]);
        operationSelectedId(null);
        enterModalDs.current.set('workStationLov', null);
        return;
      }
      setCurrentWorkcellSelectedId(_cardId);
      setWorkcellSelectedId(null);
      enterModalDs.current.set('workStationLov', cardInfo);
      onFetchWorkStation(cardInfo.workcellCode, 'click');
    },
    [currentWorkcellSelectedId],
  );

  const handleSelectOperation = useCallback(
    cardInfo => {
      const _cardId = String(cardInfo.operationId);
      if (operationSelectedId === _cardId) {
        setOperationSelectedId('');
        return;
      }
      setOperationSelectedId(_cardId);
    },
    [operationSelectedId],
  );

  /**
   * 替换查询 Url 中的变量
   * @param {String} urls
   * @param {Object} data
   */
  const getUrl = (urls, data) => {
    let ret = urls;
    const organizationRe = /\{organizationId\}|\{tenantId\}/g;
    Object.keys(data).map(key => {
      const re = new RegExp(`{${key}}`, 'g');
      ret = ret.replace(re, data[key]);
      return ret;
    });
    if (organizationRe.test(ret)) {
      ret = ret.replace(organizationRe, getCurrentOrganizationId());
    }
    return ret;
  };

  useEffect(() => {
    queryLov({ viewCode: 'HME.USER_WORKCELL', tenantId }).then(res => {
      if (res) {
        const url = getUrl(res.queryUrl, {});
        queryLovData(url, { page: 0, size: 1000, tenantId }).then(response => {
          if (response) {
            const currentWorlCellIds =
              JSON.parse(window.localStorage.getItem('selectWorkCellIds')) || [];
            const currentWorlCellList = currentWorlCellIds
              .map(i => {
                if (response.content.filter(j => j.workcellId === i).length === 1) {
                  return response.content.filter(j => j.workcellId === i)[0];
                }
                return {};
              })
              .filter(item => item.workcellId);
            const storeWorkcellSelectedId = window.localStorage.getItem('workcellSelectedId') || '';
            dispatch({
              type: 'update',
              payload: {
                workCellList: response.content,
                // currentWorlCellList,
                // workcellSelectedId,
              },
            });
            setCurrentWorlCellList(currentWorlCellList);
            setCurrentWorkcellSelectedId(storeWorkcellSelectedId);
            if (storeWorkcellSelectedId) {
              onFetchWorkStation(
                currentWorlCellList.filter(
                  item => item.workcellId === Number(storeWorkcellSelectedId),
                )[0]?.workcellCode,
                'init',
              );
            }
          }
        });
      }
    });
    const enterInfoProps = {
      fetchLoading,
      enterModalDs,
      onFetchWorkStation,
      currentWorlCellList,
      workcellSelectedId,
      handleSelectWorkcell,
      workCellList,
      operationList,
      operationSelectedId,
      handleSelectOperation,
      currentWorkcellSelectedId,
      handleSelectCurrentWorkcell,
    };
    if (!logined) {
      enterModal = Modal.open({
        header: (
          <div style={{ display: 'flex' }} className="c7n-pro-modal-header">
            <img src={cardSvg} alt="" className="titleIcon" />
            <div className="c7n-pro-modal-title">工位登录</div>
          </div>
        ),
        destroyOnClose: true,
        closable: false,
        style: {
          width: '95%',
        },
        mask: true,
        contentStyle: {
          background: '#38708F',
        },
        className: 'operationPlatformEnterModal',
        children: <EnterInfo {...enterInfoProps} />,
        okProps: {
          loading: fetchLoading,
          style: {
            background: '#00D4CD',
            color: 'white',
            borderColor: '#00d4cd',
          },
        },
        // cancelButton: false,
        onOk: () => {
          return handleEnter();
        },
        onCancel: () => {
          if (enterInfo.workStationId) {
            dispatch({
              type: 'update',
              payload: {
                logined: true,
              },
            });
          }
          enterModal.close();
          closeTab('/hmes/operation-platform');
        },
      });
    }
    return () => {
      enterModal.close();
    };
  }, []);

  useEffect(() => {
    const enterInfoProps = {
      fetchLoading,
      enterModalDs,
      onFetchWorkStation,
      currentWorlCellList,
      workcellSelectedId,
      handleSelectWorkcell,
      workCellList,
      operationList,
      operationSelectedId,
      handleSelectOperation,
      currentWorkcellSelectedId,
      handleSelectCurrentWorkcell,
    };
    if (!logined) {
      enterModal.update({
        header: (
          <div style={{ display: 'flex' }}>
            <img src={cardSvg} alt="" className="titleIcon" />
            <div className="c7n-pro-modal-title">工位登录</div>
          </div>
        ),
        destroyOnClose: true,
        closable: false,
        style: {
          width: '95%',
        },

        mask: true,
        contentStyle: {
          background: '#38708F',
        },
        className: 'operationPlatformEnterModal',
        children: <EnterInfo {...enterInfoProps} />,
        okProps: {
          loading: fetchLoading,
          style: {
            background: '#00D4CD',
            color: 'white',
            borderColor: '#00d4cd',
          },
        },
        // cancelButton: false,
        onOk: () => {
          return handleEnter();
        },
        onCancel: () => {
          if (enterInfo.workStationId) {
            dispatch({
              type: 'update',
              payload: {
                logined: true,
              },
            });
          }
          enterModal.close();
          closeTab('/hmes/operation-platform');
        },
      });
    }
  }, [[workcellSelectedId, operationSelectedId]]);

  // 缓存最近登录工位
  const coachWorkCellIds = workStationId => {
    const currentWorlCellIds = JSON.parse(window.localStorage.getItem('selectWorkCellIds')) || [];
    window.localStorage.setItem('workcellSelectedId', workStationId);
    if (!currentWorlCellIds.includes(workStationId)) {
      if (currentWorlCellIds.length > 3) {
        const data = [workStationId, ...currentWorlCellIds.slice(0, -1)];
        window.localStorage.setItem('selectWorkCellIds', JSON.stringify(data));
      } else {
        const data = [workStationId, ...currentWorlCellIds];
        window.localStorage.setItem('selectWorkCellIds', JSON.stringify(data));
      }
    } else {
      const currentData = currentWorlCellIds.filter(i => i !== workStationId);
      const data = [workStationId, ...currentData];
      window.localStorage.setItem('selectWorkCellIds', JSON.stringify(data));
    }
  };

  // 确定进入工序作业平台
  const handleEnter = async () => {
    const enterModalData = enterModalDs?.toData()[0];
    const chooseOperation = operationList.filter(
      i => i.operationId === Number(operationSelectedId),
    );
    if (
      enterModalData &&
      (enterModalData.operationName || operationSelectedId) &&
      enterModalData.workStationCode
    ) {
      if (enterModalDs?.toData()[0]?.operationList?.length === 1) {
        enterModal.close();
        dispatch({
          type: 'update',
          payload: {
            enterInfo: {
              ...enterModalDs.toData()[0],
              selectOperation: enterModalDs?.toData()[0]?.operationList[0] || chooseOperation[0],
            },
            logined: true,
            // cardUsageList: JSON.parse(window.localStorage.getItem('cardUsageList')),
          },
        });
        coachWorkCellIds(enterModalData.workStationId);
      } else {
        const params = {
          workStationCode: enterModalData.workStationCode,
          operationFlag: 'Y',
          // operation: enterModalDs
          //   ?.toData()[0]
          //   ?.operationList?.filter(
          //     item => item.operationName === enterModalDs?.toData()[0]?.operationName,
          //   )[0],
          operation: chooseOperation[0],
        };
        return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-station`, {
          method: 'POST',
          body: params,
        }).then(res => {
          if (res && !res.failed) {
            enterModal.close();
            dispatch({
              type: 'update',
              payload: {
                enterInfo: {
                  ...res,
                  selectOperation: res.operationList[0],
                },
                logined: true,
                // cardUsageList: JSON.parse(window.localStorage.getItem('cardUsageList')),
              },
            });
            coachWorkCellIds(enterModalData.workStationId);
          } else {
            notification.error({ message: res.message });
            return false;
          }
        });
      }
    } else {
      notification.error({ message: '请检查扫描工位和选择工艺' });
      return false;
    }
  };

  // loginType 登录类型  lov/ click /  init
  const onFetchWorkStation = (value, loginType) => {
    if (value) {
      const params = {
        workStationCode: value,
      };
      setFetchLoading(true);
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-station`, {
        method: 'POST',
        body: params,
      }).then(res => {
        setFetchLoading(false);
        if (res && !res.failed) {
          setOperationList(res?.operationList);
          const storeWorkcellSelectedId = window.localStorage.getItem('workcellSelectedId') || '';
          if (storeWorkcellSelectedId) {
            setOperationSelectedId(res?.operationList[0].operationId.toString());
          }
          if (res?.operationList?.length === 1) {
            enterModalDs.loadData([{ ...res, operationName: res.operationList[0].operationName }]);
            setLoginWkcInfo({
              ...res,
            });
            if (loginType !== 'init') {
              enterModal.close();
              handleEnter();
            }
            if(loginType === 'lov'){
              setWorkcellSelectedId(res.workStationId.toString());
              setCurrentWorkcellSelectedId(null);
            }
          } else {
            if (loginType === 'lov') {
              setWorkcellSelectedId(res.workStationId.toString());
              setCurrentWorkcellSelectedId(null);
            }
            enterModalDs.loadData([res]);
            setLoginWkcInfo({
              ...res,
            });
            const enterInfoProps = {
              fetchLoading,
              enterModalDs,
              onFetchWorkStation,
              currentWorlCellList,
              workcellSelectedId,
              handleSelectWorkcell,
              workCellList,
              operationList,
              operationSelectedId,
              handleSelectOperation,
              currentWorkcellSelectedId,
              handleSelectCurrentWorkcell,
            };
            enterModal.update({
              header: (
                <div style={{ display: 'flex' }} className="c7n-pro-modal-header">
                  <img src={cardSvg} alt="" className="titleIcon" />
                  <div className="c7n-pro-modal-title">工位登录</div>
                </div>
              ),
              destroyOnClose: true,
              closable: false,
              style: {
                width: '95%',
              },

              mask: true,
              contentStyle: {
                background: '#38708F',
              },
              className: 'operationPlatformEnterModal',
              children: <EnterInfo {...enterInfoProps} />,
              okProps: {
                loading: fetchLoading,
                style: {
                  background: '#00D4CD',
                  color: 'white',
                  borderColor: '#00d4cd',
                },
              },
              // cancelButton: false,
              onOk: () => {
                return handleEnter();
              },
              onCancel: () => {
                if (enterInfo.workStationId) {
                  dispatch({
                    type: 'update',
                    payload: {
                      logined: true,
                    },
                  });
                }
                enterModal.close();
                closeTab('/hmes/operation-platform');
              },
            });
          }
        } else {
          notification.error({ message: res.message });
          enterModalDs.loadData([{ workStationCode: enterModalDs.toData()[0]?.workStationCode }]);
          setLoginWkcInfo({});
          // enterModal.update({
          //   children: (
          //     <Spin spinning={fetchLoading || false}>
          //       <Form
          //         className="enterModalForm"
          //         dataSet={enterModalDs}
          //         labelLayout="placeholder"
          //         labelWidth={80}
          //       >
          //         <TextField
          //           id="workStationCode"
          //           name="workStationCode"
          //           onEnterDown={e => onFetchWorkStation(e.target.value)}
          //           style={{ width: '421px', height: '52px' }}
          //           suffix={<Icon type="scan" style={{ fontSize: 14, color: 'white' }} />}
          //         />
          //       </Form>
          //       <div style={{ color: 'white', marginTop: '10px' }}>工位选择：</div>
          //       <div style={{ marginTop: '10px' }}>最近登录工位：</div>
          //       <TabCardsRender
          //         primaryCardId={primaryWorkcellKey}
          //         cardName={workcellCardName}
          //         cardsList={currentWorlCellList}
          //         selectedCardIds={currentWorkcellSelectedIds}
          //         handleSelectCard={handleSelectCurrentWorkcell}
          //       />
          //       <div style={{ marginTop: '10px' }}>全部工位：</div>
          //       <TabCardsRender
          //         primaryCardId={primaryWorkcellKey}
          //         cardName={workcellCardName}
          //         cardsList={workCellList}
          //         selectedCardIds={workcellSelectedIds}
          //         handleSelectCard={handleSelectWorkcell}
          //       />
          //       <div style={{ color: 'white', marginTop: '10px' }}>工艺：</div>
          //       <TabCardsRender
          //         primaryCardId={primaryOperationKey}
          //         cardName={operationCardName}
          //         cardsList={operationList}
          //         selectedCardIds={operationSelectedIds}
          //         handleSelectCard={handleSelectOperation}
          //       />
          //       {/* <Form
          //         className="enterModalForm"
          //         dataSet={enterModalDs}
          //         labelLayout="horizontal"
          //         columns={3}
          //         labelWidth="auto"
          //       >
          //         <Output name="workStationName" colSpan={6} />
          //         <Output label="工艺" />
          //       </Form> */}
          //     </Spin>
          //   ),
          // });
          setTimeout(() => {
            document.querySelector('#workStationCode').focus();
          }, 200);
        }
      });
    } else {
      setWorkcellSelectedId(null);
      setCurrentWorkcellSelectedId(null);
      setOperationList([]);
      setOperationSelectedId(null);
    }
  };

  return <></>;
};

// 工序作业平台
const OperationPlatformEnter = props => {
  const {
    enterModalDs, // 登录工位ds
  } = props;
  const { logined } = useOperationPlatform();

  const [loginWkcInfo, setLoginWkcInfo] = useState({}); // 工位信息

  // useEffect(() => {
  //   // 查询维度
  //   queryMapIdpValue({
  //     cardUsageList: 'HME_CARD_USAGE',
  //   }).then(res => {
  //     if (res && !res.failed) {
  //       window.localStorage.setItem('cardUsageList', JSON.stringify(res.cardUsageList));
  //     }
  //   });
  // }, []);

  return (
    <div id="acceptedPuted" className="operationPlatformEnter">
      <LoginModal {...props} setLoginWkcInfo={setLoginWkcInfo} enterModalDs={enterModalDs} />
      <Header
        title={
          <>
            <span>工序作业平台</span>
          </>
        }
      >
        {loginWkcInfo && loginWkcInfo.workStationName && (
          <div
            style={{
              display: 'flex',
              width: '30%',
              justifyContent: 'space-between',
              color: '#01e1ef',
            }}
          >
            <div>
              <span style={{ marginRight: '15px' }}>
                <Icon type="user" />
                &nbsp;人员&nbsp;-&nbsp;{loginWkcInfo.userName}
              </span>
              <Divider type="vertical" style={{ background: '#01e1ef' }} />
              <span style={{ marginLeft: '15px' }}>
                <Icon type="solution" />
                &nbsp;工位&nbsp;-&nbsp;{loginWkcInfo.workStationName}
              </span>
            </div>
          </div>
        )}
      </Header>
      <Content style={{ backgroundColor: '#2a445e', borderRadius: '0px' }} >
        <img src={footerBackground} alt='' className='footerBackground'/>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.workBench', 'tarzan.common'],
})(
  withProps(
    () => {
      const enterModalDs = new DataSet({
        ...enterModalDS(),
      });
      return {
        enterModalDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(OperationPlatformEnter),
);
