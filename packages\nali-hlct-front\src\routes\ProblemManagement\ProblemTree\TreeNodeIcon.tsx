/**
 * RelationMaintain - 故障树-icon
 * @date: 2023-8-24
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React from 'react';
import intl from 'utils/intl';
import { Button, Lov } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';

const modelPrompt = 'tarzan.qms.problemTree';

const iconText = {
  add: intl.get(`${modelPrompt}.text.add`).d('新增关系'),
  addSub: intl.get(`${modelPrompt}.text.addSub`).d('新建下级及关系'),
  copy: intl.get(`${modelPrompt}.text.copy`).d('复制'),
  paste: intl.get(`${modelPrompt}.text.paste`).d('粘贴'),
  confirmDelete: intl.get(`${modelPrompt}.text.confirmDelete`).d('删除'),
  confirm: intl.get(`${modelPrompt}.text.confirm`).d('确定'),
  cancel: intl.get(`${modelPrompt}.text.cancel`).d('取消'),
  delete: intl.get(`${modelPrompt}.text.delete`).d('删除'),
};

const treeNodeIcon = props => {
  const { callback, record, failureTreeTypeMap, copyRecord, user } = props;

  const failureTreeType = record.get('failureTreeType');

  const checkPaste = () => {
    return (
      !record.get('subCodeList').includes(copyRecord?.failureTreeCode) &&
      copyRecord?.failureTreeCode &&
      record.get('failureTreeCode') !== copyRecord?.failureTreeCode
    );
  };

  const checkCopyAndDelete = () => {
    return user?.id === record.get('userId');
  };

  const iconEventClick = (e, type, value) => {
    e.stopPropagation();
    callback(type, record, value);
  };

  return (
    <span
      key={`${record.get('path')}icon`}
      className="tree-node-icon"
      onClick={e => {
        e.stopPropagation();
      }}
    >
      {(failureTreeTypeMap[failureTreeType] || '').includes('add') && (
        <span className="tree-node-icon-item">
          <Lov
            modalProps={{
              title: intl.get(`${modelPrompt}.text.add`).d('新增关系'),
            }}
            mode={ViewMode.button}
            record={record}
            placeholder=" "
            name="addRelation"
            funcType={FuncType.link}
            onChange={value => {
              iconEventClick({ stopPropagation: () => {} }, 'addRelation', value);
            }}
            icon="add_box-o"
            clearButton={false}
          />
          <div className="node-info">{iconText.add}</div>
        </span>
      )}
      {(failureTreeTypeMap[failureTreeType] || '').includes('addSub') && (
        <span className="tree-node-icon-item">
          <Button
            funcType={FuncType.link}
            onClick={e => {
              iconEventClick(e, 'addSub', null);
            }}
            icon="library_add-o"
          />
          <div className="node-info">{iconText.addSub}</div>
        </span>
      )}
      {(failureTreeTypeMap[failureTreeType] || '').includes('copy') && (
        <span className="tree-node-icon-item">
          <Button
            funcType={FuncType.link}
            disabled={!checkCopyAndDelete()}
            onClick={e => {
              iconEventClick(e, 'copy', null);
            }}
            icon="content_copy"
          />
          <div className="node-info">{iconText.copy}</div>
        </span>
      )}
      {(failureTreeTypeMap[failureTreeType] || '').includes('paste') && (
        <span className="tree-node-icon-item">
          <Button
            funcType={FuncType.link}
            disabled={!checkPaste()}
            onClick={e => {
              iconEventClick(e, 'paste', null);
            }}
            icon="content_paste"
          />
          <div className="node-info">{iconText.paste}</div>
        </span>
      )}
      {(failureTreeTypeMap[failureTreeType] || '').includes('delete') && (
        <span className="tree-node-icon-item">
          <Popconfirm
            placement="left"
            title={iconText.confirmDelete}
            okText={iconText.confirm}
            cancelText={iconText.cancel}
            onConfirm={e => {
              iconEventClick(e, 'delete', null);
            }}
          >
            <Button
              funcType={FuncType.link}
              disabled={!checkCopyAndDelete()}
              icon="delete_black-o"
            />
          </Popconfirm>
          <div className="node-info">{iconText.delete}</div>
        </span>
      )}
    </span>
  );
};

export default treeNodeIcon;
