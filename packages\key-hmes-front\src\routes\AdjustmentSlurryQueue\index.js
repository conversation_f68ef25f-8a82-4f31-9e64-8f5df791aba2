/**
 * @Description: 浆料队列调整-列表页
 * @Author: <EMAIL>
 * @Date: 2023-06-15 10:19:56
 */
import React, { useState } from 'react';
import {
  DataSet,
  Table,
  NumberField,
  Spin,
  Button,
  Lov,
  TextField,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import request from 'utils/request';
import { tableDS } from './stores/TableDs';
import { Host } from '@/utils/config';
// const Host = '/mes-38546'

const modelPrompt = 'tarzan.hmes.adjustmentSlurryQueue';

const AdjustmentSlurryQueueList = props => {
  const { tableDs } = props;
  const [loading, setLoading] = useState(false);

  const columns = [
    {
      name: 'workCellLov',
      width: 150,
      align: 'center',
      editor: record => record.getState('editing') && record.status === 'add' && <Lov />,
    },
    {
      name: 'workCellName',
      width: 150,
      align: 'center',
    },
    {
      name: 'equipmentCodeTb',
      align: 'center',
      width: 150,
    },
    {
      name: 'equipmentNameTb',
      align: 'center',
      width: 150,
    },
    {
      name: 'materialLotCode',
      align: 'center',
      width: 200,
      editor: record => record.getState('editing') && record.status === 'add' && <TextField />,
    },
    {
      name: 'materialCode',
      align: 'center',
    },
    {
      name: 'materialName',
      align: 'center',
      width: 300,
    },
    {
      name: 'revisionCode',
      align: 'center',
    },
    {
      name: 'materialLotSequence',
      align: 'center',
      editor: record => {
        return (
          record.getState('editing') && record.status !== 'add' && <NumberField precision={0} />
        );
      },
    },
    {
      name: 'creationDate',
      align: 'center',
      width: 180,
      editor: record => record.getState('editing') && record.status === 'add' && <DateTimePicker />,
    },
    {
      name: 'outCreationDate',
      align: 'center',
      width: 180,
      editor: record => record.getState('editing') && record.status === 'add' && <DateTimePicker />,
    },
    {
      name: 'equipmentLov',
      align: 'center',
      width: 180,
      editor: record => record.getState('editing') && record.status === 'add' && <Lov />,
    },
    {
      name: 'equipmentName',
      align: 'center',
      width: 180,
    },
    {
      width: 200,
      align: 'center',
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      renderer: ({ record }) =>
        record.getState('editing') ? (
          <>
            <Spin spinning={loading}>
              <Button color="primary" funcType="flat" onClick={() => handleSave(record)}>

                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button color="primary" funcType="flat" onClick={() => handleEdit(record, false)}>取消</Button>
            </Spin>
          </>
        ) : (
          <>
            <a
              disabled={tableDs.some(record => record.getState('editing'))}
              onClick={() => handleEdit(record, true)}
            >
              队列调整
            </a>
            <Popconfirm
              title={intl.get(`${modelPrompt}.confirm.delete`).d('确认删除选中行？')}
              onConfirm={() => handleLineDelete(record)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <Button
                funcType="flat"
              >
                {intl.get(`tarzan.common.button.delete`).d('删除')}
              </Button>
            </Popconfirm>
          </>
          
        ),
      lock: 'right',
    },
  ];
  const handleEdit = (record, flag) => {
    tableDs.current.getField('materialLotSequence').set('required', true);
    // 调整的时候需要自动清空顺序字段
    if (!flag) {
      tableDs.loadData([])
      tableDs.query(tableDs.currentPage);
    } else {
      record.init('materialLotSequence', null);
    }
    record.setState('editing', flag);
  };
  const handleSave = async record => {
    if (await record.validate()) {
      let url = '';
      if (record.status === 'add') {
        url = `${Host}/v1/${getCurrentOrganizationId()}/hme-slurry-sequence-adjust-new/save/for/ui`;
      } else {
        url = `${Host}/v1/${getCurrentOrganizationId()}/hme-slurry-sequence-adjust/save/for/ui`;
      }
      setLoading(true);
      const res = await request(url, {
        body: record.toData(),
        method: 'POST',
      });
      setLoading(false);
      const response = getResponse(res);
      if (response) {
        record.setState('editing', false);
        tableDs.query(tableDs.currentPage);
      }
    }
  };

  const handleCreate = () => {
    tableDs.create({}, 0);
    tableDs.current.setState('editing', true);
    // 新建的时候顺序设为非必输
    tableDs.current.getField('materialLotSequence').set('required', false);
  };

  const handleLineDelete = async(record) => {
    setLoading(true);
    const res = await request(`${Host}/v1/${getCurrentOrganizationId()}/hme-slurry-sequence-adjust-new/delete/for/ui`, {
      body: record.toData(),
      method: 'POST',
    });
    setLoading(false);
    const response = getResponse(res);
    if (response) {
      record.setState('editing', false);
      tableDs.query(tableDs.currentPage);
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('浆料队列调整')}>
        <Button color="primary" onClick={handleCreate}>
          {intl.get('tarzan.common.button.created').d('新建')}
        </Button>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={6}
          searchCode="adjustmentSlurryQueueList"
          customizedCode="adjustmentSlurryQueueList"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.hmes.adjustmentSlurryQueue', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(AdjustmentSlurryQueueList),
);
