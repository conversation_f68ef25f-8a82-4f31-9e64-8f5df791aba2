/**
 * @Description: 上传文件类型对于的图片
 * @Author: <<EMAIL>>
 * @Date: 2021-04-30 14:11:05
 * @LastEditTime: 2021-05-06 18:00:38
 * @LastEditors: <<EMAIL>>
 */
import word from '@/assets/icons/fileTypeIcons/word.svg';
import excel from '@/assets/icons/fileTypeIcons/excel.svg';
import ppt from '@/assets/icons/fileTypeIcons/ppt.svg';
import zip from '@/assets/icons/fileTypeIcons/zip.svg';
import pdf from '@/assets/icons/fileTypeIcons/pdf.svg';
import txt from '@/assets/icons/fileTypeIcons/txt.svg';
import unknown from '@/assets/icons/fileTypeIcons/unknown.svg';

const docTypesArr = [
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
  'application/vnd.ms-word.document.macroEnabled.12',
  'application/vnd.ms-word.template.macroEnabled.12',
];

const xlsTypesArr = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
  'application/vnd.ms-excel.sheet.macroEnabled.12',
  'application/vnd.ms-excel.template.macroEnabled.12',
  'application/vnd.ms-excel.addin.macroEnabled.12',
  'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
];

const pptTypesArr = [
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/vnd.openxmlformats-officedocument.presentationml.template',
  'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
  'application/vnd.ms-powerpoint.addin.macroEnabled.12',
  'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
  'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
];

const zipTypesArr = ['application/zip', 'application/x-tar'];

const imageTypesArr = ['image/jpeg', 'image/png'];

const pdfType = 'application/pdf';
const txtType = 'text/plain';

const fileTypeIcon = item => {
  const { type, url } = item;
  if (docTypesArr.includes(type)) {
    return word;
  } if (xlsTypesArr.includes(type)) {
    return excel;
  } if (pptTypesArr.includes(type)) {
    return ppt;
  } if (zipTypesArr.includes(type)) {
    return zip;
  } if (imageTypesArr.includes(type)) {
    return url;
  } if (pdfType === type) {
    return pdf;
  } if (txtType === type) {
    return txt;
  }
  return unknown;
};

export default fileTypeIcon;
