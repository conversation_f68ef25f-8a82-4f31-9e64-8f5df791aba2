import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import {BASIC} from "@utils/config";

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

// 头信息
const DocInfoDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'msaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaCode`).d('MSA编号'),
      disabled: true,
    },
    {
      name: 'msaStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaStatus`).d('状态'),
      disabled: true,
      lookupCode: 'YP.QIS.MSA_STATUS',
      defaultValue: 'NEW',
    },
    {
      name: 'siteObj',
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      textField: 'siteName',
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteObj.siteName',
    },
    {
      name: 'msaType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaType`).d('MSA类型'),
      lookupCode: 'YP.QIS.MSA_TYPE',
      defaultValue: '1',
    },
    {
      name: 'toolModelIdObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toolModel`).d('量具型号'),
      lovCode: 'YP.QIS.MS_TOOL_MSA_REL',
      ignore: FieldIgnore.always,
      textField: 'modelCode',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('msaStatus') === 'NEW';
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return record.get('msaStatus') !== 'NEW';
        },
      },
    },
    {
      name: 'modelCode',
      bind: 'toolModelIdObj.modelCode',
    },
    {
      name: 'modelId',
      bind: 'toolModelIdObj.modelId',
    },
    // {
    //   name: 'toolModelId',
    //   type: FieldType.string,
    //   bind: 'toolModelIdObj.toolModelId',
    // },
    {
      name: 'modelRange',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measuringRange`).d('量具量程'),
    },
    {
      name: 'modelName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelName`).d('量具型号名称'),
      bind: 'toolModelIdObj.modelName',
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
      disabled: true,
      bind: 'toolModelIdObj.qualityCharacteristic',
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measuringToolSpecies`).d('量具种别'),
      disabled: true,
      bind: 'toolModelIdObj.speciesName',
    },
    {
      name: 'msaAnalysisMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaAnalysisMethod`).d('MSA分析方法'),
      disabled: true,
      bind: 'toolModelIdObj.msaAnalysisMethod',
    },
    {
      name: 'completeTimeLimit',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completeTimeLimit`).d('完成时限'),
      disabled: true,
      bind: 'toolModelIdObj.completeTimeLimit',
    },
    {
      name: 'measureToolNumObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.measureToolNum`).d('量具编码'),
      lovCode: 'YP.QIS.ALL_MT_TOOL',
      ignore: FieldIgnore.always,
      // textField: 'toolCode',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('msaStatus') === 'NEW';
        },
        disabled: ({ record }) => {
          return record.get('msaStatus') !== 'NEW';
        },
      },
    },
    {
      name: 'toolCode',
      bind: 'measureToolNumObj.toolCode',
    },
    {
      name: 'msToolManageId',
      bind: 'measureToolNumObj.msToolManageId',
    },
    // {
    //   name: 'measureToolNum',
    //   type: FieldType.string,
    //   bind: 'measureToolNumObj.measureToolNum',
    // },
    {
      name: 'measureToolByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measureToolByName`).d('量具责任人'),
      disabled: true,
      bind: 'measureToolNumObj.measureToolByName',
    },
    {
      name: 'onlineFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onlineFlag`).d('是否在线'),
      lookupCode: 'YP.QIS.ONLINE_FLAG',
      dynamicProps: {
        required: ({ record }) => {
          return record.get('msaStatus') === 'NEW';
        },
        disabled: ({ record }) => {
          return record.get('msaStatus') !== 'NEW';
        },
      },
    },
    {
      name: 'specialCharacteristicFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialCharacteristicsOrNot`).d('是否特殊特性'),
      lookupCode: 'YP.QIS.SPECIAL_CHARACTERISTIC_FLAG',
      dynamicProps: {
        required: ({ record }) => {
          return record.get('msaStatus') === 'NEW';
        },
        disabled: ({ record }) => {
          return record.get('msaStatus') !== 'NEW';
        },
      },
    },
    {
      name: 'projectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
      lookupCode: 'YP.QIS.YP.QIS.PROJECT_NAME',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('msaStatus') !== 'NEW';
        },
      },
    },
    {
      name: 'projectStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
      lookupCode: 'YP.QIS.PROJECT_STAGE',
      dynamicProps: {
        required: ({ record }) => {
          return record.get('msaStatus') === 'NEW';
        },
        disabled: ({ record }) => {
          return record.get('msaStatus') !== 'NEW';
        },
      },
    },
    {
      name: 'planStartTime',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
      dynamicProps: {
        required: ({ record }) => {
          return record.get('msaStatus') === 'NEW';
        },
        disabled: ({ record }) => {
          return record.get('msaStatus') !== 'NEW';
        },
      },
    },
    {
      name: 'planEndTime',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
      disabled: true,
    },
    {
      name: 'analyzedByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.analyzedByObj`).d('MSA分析人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('msaStatus') === 'NEW';
        },
        disabled: ({ record }) => {
          return record.get('msaStatus') !== 'NEW';
        },
      },
    },
    {
      name: 'analyzedByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analyzedByName`).d('分析人'),
    },
    {
      name: 'analyzedBy',
      bind: 'analyzedByObj.userId',
    },
    {
      name: 'assistantByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.assistantByObj`).d('协助人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('msaStatus') !== 'NEW';
        },
      },
    },
    {
      name: 'assistantBy',
      bind: 'assistantByObj.userId',
    },
    {
      name: 'sipFileUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.attachment`).d('SIP附件'),
      bucketName: 'qms',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'sourceMsaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceMsaCode`).d('来源MSA编号'),
      disabled: true,
    },
    {
      name: 'improveByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.improveByObj`).d('改善负责人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('msaType') === '2' && record.get('msaStatus') === 'TO_IMPROVE';
        },
        disabled: ({ record }) => {
          return !(record.get('msaType') === '2' && record.get('msaStatus') === 'TO_IMPROVE');
        },
      },
    },
    {
      name: 'improveBy',
      bind: 'improveByObj.userId',
    },
    {
      name: 'improveEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.improveEnclosure`).d('改善措施附件'),
      bucketName: 'qms',
    },
    {
      name: 'reasonAnalyzed',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonAnalyzed`).d('原因分析'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !(record.get('msaType') === '2' && record.get('msaStatus') === 'TO_IMPROVE');
        },
      },
    },
    {
      name: 'effectVerification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.effectVerification`).d('效果验证'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !(record.get('msaType') === '2' && record.get('msaStatus') === 'TO_IMPROVE');
        },
      },
    },
    {
      name: 'msaResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.result`).d('结果判定'),
      lookupCode: 'YP.QIS.MSA_RESULT',
    },
    {
      name: 'resolution',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.resolution`).d('分辨力'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      disabled: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/detail/ui`,
        method: 'GET',
      };
    },
  },
});

const finishAnalyseDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'transferResponsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transferResponsibleUserName`).d('改善责任人'),
      lovCode: 'YP.QIS.UNIT_USER',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'realName',
    },
    {
      name: 'responId',
      bind: 'transferResponsibleUserLov.id',
    },
  ],
});

const analyseResultDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  fields: [
    {
      name: 'checkLocation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.checkLocation`).d('测量位置'),
      required: true,
    },
    {
      name: 'msaResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaResult`).d('分析结果'),
      lookupCode: 'YP.QIS.MSA_RESULT',
      lovPara: { tenantId },
    },
    {
      name: 'msaConclusion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaConclusion`).d('分析结论'),
    },
  ],
});

export { DocInfoDS, finishAnalyseDS, analyseResultDS };
