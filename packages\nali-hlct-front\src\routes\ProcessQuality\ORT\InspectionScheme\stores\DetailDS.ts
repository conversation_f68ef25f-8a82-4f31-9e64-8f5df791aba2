/**
 * @Description: ORT检验方案维护-详情界面DS
 */
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.ort.InspectionScheme';

const detailDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'schemeId',
  fields: [
    {
      name: 'inspectSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectExecute.inspectSchemeCode`).d('检验方案编码'),
      disabled: true,
    },
    {
      name: 'inspectSchemeName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectExecute.inspectSchemeName`).d('检验方案名称'),
      required: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectExecute.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'siteName',
      lovPara: { tenantId },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'materialObj',
      label: intl.get(`${modelPrompt}.inspectExecute.materialObj`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      type: FieldType.object,
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'materialId',
      bind: 'materialObj.materialId',
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      bind: 'materialObj.materialCode',
      type: FieldType.string,
    },
    {
      name: 'materialName',
      type: FieldType.string,
      bind: 'materialObj.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      disabled: true,
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectExecute.enableFlag`).d('是否生效'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ort-inspect-scheme/act/ui`,
        method: 'GET',
      };
    },
  },
});

const taskDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  primaryKey: 'sequence',
  fields: [
    {
      name: 'sequence',
      label: intl.get(`${modelPrompt}.inspectExecute.sequence`).d('序号'),
      type: FieldType.number,
    },
    {
      name: 'inspectItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectExecute.inspectItem`).d('测试项目名称'),
      required: true,
    },
    {
      name: 'inspectMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectExecute.inspectMethod`).d('测试方法'),
      required: true,
    },
    {
      name: 'standardRequirement',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.inspectExecute.standardRequirement`).d('标准要求'),
    },
    {
      name: 'inspectFrequency',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.inspectExecute.inspectFrequency`).d('测试频率'),
    },
    {
      name: 'outsourceFlag',
      label: intl.get(`${modelPrompt}.inspectExecute.outsourceFlag`).d('委外标识'),
      lookupCode: 'YP.QIS.Y_N',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'enclosure',
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      type: FieldType.attachment,
      bucketName: 'qms',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    },
  ],
});


export { detailDS, taskDS };
