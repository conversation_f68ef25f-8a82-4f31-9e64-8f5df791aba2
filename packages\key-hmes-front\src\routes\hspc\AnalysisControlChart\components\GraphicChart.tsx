/**
 * @Description: 图形展示
 * @Author: <<EMAIL>>
 * @Date: 2021-11-22 16:16:19
 * @LastEditTime: 2021-12-13 16:55:19
 * @LastEditors: <<EMAIL>>
 */

import React, { useRef, useReducer, useEffect, ReactNode } from 'react';
import { Spin, Popconfirm, Icon } from 'choerodon-ui';
import intl from 'utils/intl';
import { max as lodashMax, min as lodashMin } from 'lodash';
import notification from 'utils/notification';
import { ExpandCardC7n } from '@components/tarzan-ui';
import excelAction from 'hspc-front/lib/utils/excelAction';
import Chart from './Chart';
import Table from './Table';
import ChartSettingModal from './ChartSettingModal';
import MeasureTypeModal from './MeasureTypeModal';
import CountTypeModal from './CountTypeModal';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hspc.chartInfo';

const dataStore = {};

interface GraphicChartProps {
  chartData: any;
  loading: boolean;
  chartUuid: string;
  title?: string | ReactNode | null;
}

interface graphicChartStateProps {
  graphDataList: [];
  realChartData: object;
  chartSetingModalVisible: boolean;
  sampleType: 'COUNT' | 'MEASURE' | null;
  chartToolsConfig: {
    showCL: boolean;
    showCLSIGMA: boolean;
    showSL: boolean;
    showCLTitle: boolean;
    showXAttr: boolean;
  };
  pointInfoModalProps: object;
  activePorintIndex: number | null;
  chartLoading: boolean;
  chartLoadFailedFlag: boolean;
}

// 表格使用的数据
const initialTableState: graphicChartStateProps = {
  graphDataList: [], // 后台返回的图形全部数据
  realChartData: {}, // 在全部数据处理之后，图形直接使用的数据
  chartSetingModalVisible: false, // 图形设置弹窗的显隐
  sampleType: null,
  chartToolsConfig: {
    // 图形设置的默认数据
    showCL: true,
    showCLSIGMA: false,
    showSL: false,
    showCLTitle: true,
    showXAttr: false,
  },
  pointInfoModalProps: {}, // 数据点详细信息modal的props
  activePorintIndex: null, // active点的索引
  chartLoading: true, // 是否在获取图形数据
  chartLoadFailedFlag: false, // 图形计算失败flag，失败才允许重新计算
};

function tableStateReducer(state, action) {
  switch (action.type) {
    case 'update':
      return { ...state, ...action.payload } as graphicChartStateProps;
    default:
      throw new Error();
  }
}

const GraphicChart = (props: GraphicChartProps) => {
  const { chartData, chartUuid, loading, title } = props;
  const [graphicChartState, dispatch] = useReducer(tableStateReducer, initialTableState); // 图形和表格使用的所有数据
  const graphicRef = useRef<any>(null);
  const tableRef = useRef<any>(null);

  useEffect(() => {
    // 处理图形数据
    if (chartData.sampleType === 'COUNT') {
      // 计数型
      getCountTypeData();
    } else {
      // 计量型
      getMeasureTypeData();
    }
  }, [chartData]);

  const getMeasureTypeData = () => {
    const {
      chartType,
      chartTitle,
      mainChartRule,
      secondaryChartRule,
      data,
      xTickLabel,
    } = chartData;
    let mainChartMax;
    let mainChartMin;
    let secondChartMax;
    let secondChartMin;
    const _dataList = (data || []).map(item => {
      const _data = { ...item };
      if (item.subDataInfo && item.subDataInfo.length) {
        item.subDataInfo.forEach((it, index) => {
          _data[`sampleValue${index}`] = it.dataValue;
        });
      }
      return _data;
    });
    dispatch({
      type: 'update',
      payload: {
        graphDataList: _dataList,
      },
    });
    dataStore[chartUuid] = _dataList;
    let mainConfig: any = {}; // 主图配置
    let secondConfig: any = {}; // 次图配置
    const dataIdList: any[] = []; // 数据Id列表
    const callipersList: any[] = []; // 组号列表
    const measure: any[] = []; // x轴实际坐标名
    const mainData: any[] = []; // 主图数据
    const secondData: any[] = []; // 次图数据
    const xTickLabelList: any[] = []; // x轴坐标名称
    const mainDataUcl: any[] = [];
    const mainDataLcl: any[] = [];
    const mainOccList: any[] = [];
    const secondOccList: any[] = [];
    // 提取配置
    if (mainChartRule) {
      // mainConfig = { ...mainChartRule };
      mainConfig = {
        entiretyCl: mainChartRule.centerLine,
        entiretyUcl: mainChartRule.upperControlLimit,
        entiretyLcl: mainChartRule.lowerControlLimit,
        entiretySl: mainChartRule.specTarget,
        entiretyUsl: mainChartRule.upperSpecLimit,
        entiretyLsl: mainChartRule.lowerSpecLimit,
        xAxisLabel: mainChartRule.xAxisLabel,
        yAxisLabel: mainChartRule.yAxisLabel,
      };
    }
    if (secondaryChartRule) {
      // secondConfig = { ...secondaryChartRule };
      secondConfig = {
        entiretyCl: secondaryChartRule.centerLine,
        entiretyUcl: secondaryChartRule.upperControlLimit,
        entiretyLcl: secondaryChartRule.lowerControlLimit,
        entiretySl: secondaryChartRule.specTarget,
        entiretyUsl: secondaryChartRule.upperSpecLimit,
        entiretyLsl: secondaryChartRule.lowerSpecLimit,
        xAxisLabel: secondaryChartRule.xAxisLabel,
        yAxisLabel: secondaryChartRule.yAxisLabel,
      };
    }
    if (data) {
      data.forEach((item, index) => {
        if (item.mainStatsValue > mainChartMax || !mainChartMax) {
          mainChartMax = item.mainStatsValue;
        }
        if (item.mainStatsValue < mainChartMin || !mainChartMin) {
          mainChartMin = item.mainStatsValue;
        }
        if (item.secondaryStatsValue > secondChartMax || secondChartMax === undefined) {
          secondChartMax = item.secondaryStatsValue;
        }
        if (item.secondaryStatsValue < secondChartMin || secondChartMin === undefined) {
          secondChartMin = item.secondaryStatsValue;
        }
        dataIdList.push(item.subgroupIndex);
        callipersList.push(item.subgroupIndex);
        measure.push(item.subDataInfo[item.subDataInfo.length - 1].dataColumnBatch);
        mainData.push(item.mainStatsValue);
        secondData.push(item.secondaryStatsValue);
        xTickLabelList.push(item.subgroupIndex);
        mainDataUcl.push(mainChartRule.upperControlLimit);
        mainDataLcl.push(mainChartRule.lowerControlLimit);
        if (item.mainChartOoc && item.mainChartOoc.length > 0) {
          const { judgementShortCode } = item.mainChartOoc[0];
          mainOccList.push({
            value:
              item.mainChartOoc.length > 1 ? `${judgementShortCode}+` : `${judgementShortCode}`,
            xAxis: index,
            yAxis: item.mainStatsValue,
          });
        }
        if (item.secondaryChartOoc && item.secondaryChartOoc.length > 0) {
          const { judgementShortCode } = item.secondaryChartOoc[0];
          secondOccList.push({
            value:
              item.secondaryChartOoc.length > 1
                ? `${judgementShortCode}+`
                : `${judgementShortCode}`,
            xAxis: index,
            yAxis: item.secondaryStatsValue,
          });
        }
      });
    }
    mainConfig.chartMax = mainChartMax;
    mainConfig.chartMin = mainChartMin;
    secondConfig.chartMax = secondChartMax;
    secondConfig.chartMin = secondChartMin;
    dispatch({
      type: 'update',
      payload: {
        realChartData: {
          chartTitle,
          chartType,
          mainConfig,
          secondConfig,
          dataId: dataIdList,
          callipers: xTickLabelList,
          measure,
          mainData,
          secondData,
          mainDataUcl,
          mainDataLcl,
          mainOccList,
          secondOccList,
          xTickLabel,
          xTickLabelList,
          uuid: chartUuid,
        },
      },
    });
  };

  const getCountTypeData = () => {
    const { data = [], chartType, chartTitle, mainChartRule, xTickLabel } = chartData;
    let mainChartMax;
    let mainChartMin;
    dispatch({
      type: 'update',
      payload: {
        graphDataList: data,
      },
    });
    dataStore[chartUuid] = data;
    const {
      centerLine, // centerLine 控制中心线 (控制线)
      lowerControlLimit, // lowerControlLimit 控制下线(不计算)
      upperControlLimit, // upperControlLimit 控制上限 (控制线)
    } = data[0] || {};

    // 保存配置项
    const mainConfig: any = {
      entiretyCl: centerLine,
      entiretyLcl: lowerControlLimit,
      entiretyUcl: upperControlLimit,
      xAxisLabel: mainChartRule.xAxisLabel,
      yAxisLabel: mainChartRule.yAxisLabel,
      entiretyUsl: mainChartRule.upperSpecLimit, // 规格上限
      entiretyLsl: mainChartRule.lowerSpecLimit, // 规格下限
      entiretySl: mainChartRule.specTarget, // 目标值线
    };

    const dataId: any[] = []; // 数据id
    const callipers: any[] = []; // x轴刻度说明
    const measure: any[] = []; // x轴刻度说明替换值 xTickLabel 为 ATTRIBUTE
    const mainData: any[] = []; // 数据值
    // P U 需要将控制上下限特殊额外保存
    const mainDataUcl: any[] = [];
    const mainDataLcl: any[] = [];
    const mainOccList: any[] = [];

    if (chartType === 'P' || chartType === 'U') {
      data.forEach((item, index) => {
        const mainValue = item.unqualifiedPercent;
        if (
          lodashMax([mainValue, item.upperControlLimit, mainChartRule.upperSpecLimit]) >
          mainChartMax ||
          mainChartMax === undefined
        ) {
          mainChartMax = lodashMax([
            mainValue,
            item.upperControlLimit,
            mainChartRule.upperSpecLimit,
          ]);
        }
        if (
          lodashMin([mainValue, item.lowerControlLimit, mainChartRule.lowerSpecLimit]) <
          mainChartMin ||
          mainChartMin === undefined
        ) {
          mainChartMin = lodashMin([
            mainValue,
            item.lowerControlLimit,
            mainChartRule.lowerSpecLimit,
          ]);
        }
        dataId.push(item.dataIndex);
        callipers.push(item.dataIndex + 1);
        measure.push(item.dataColumnBatch);
        mainData.push(mainValue);
        mainDataUcl.push(item.upperControlLimit);
        mainDataLcl.push(item.lowerControlLimit);
        if (item.mainChartOoc && item.mainChartOoc.length > 0) {
          const { judgementShortCode } = item.mainChartOoc[0];
          mainOccList.push({
            value:
              item.mainChartOoc.length > 1 ? `${judgementShortCode}+` : `${judgementShortCode}`,
            xAxis: index,
            yAxis: mainValue,
          });
        }
      });
    } else {
      data.forEach((item, index) => {
        const mainValue = item.unqualifiedQuantity;
        if (mainValue > mainChartMax || mainChartMax === undefined) {
          mainChartMax = mainValue;
        }
        if (mainValue < mainChartMin || mainChartMin === undefined) {
          mainChartMin = mainValue;
        }
        dataId.push(item.dataIndex);
        callipers.push(item.dataIndex + 1);
        measure.push(item.dataColumnBatch);
        mainData.push(mainValue);
        if (item.mainChartOoc && item.mainChartOoc.length > 0) {
          const { judgementShortCode } = item.mainChartOoc[0];
          mainOccList.push({
            value:
              item.mainChartOoc.length > 1 ? `${judgementShortCode}+` : `${judgementShortCode}`,
            xAxis: index,
            yAxis: mainValue,
          });
        }
      });
    }
    mainConfig.chartMax = mainChartMax;
    mainConfig.chartMin = mainChartMin;
    dispatch({
      type: 'update',
      payload: {
        realChartData: {
          chartTitle,
          chartType,
          mainConfig,
          dataId,
          callipers,
          measure,
          mainData,
          mainOccList,
          mainDataUcl,
          mainDataLcl,
          xTickLabel,
          uuid: chartUuid,
        },
      },
    });
  };

  const handleDownLoad = () => {
    const { graphDataList } = graphicChartState;
    const excelHeaders = tableRef.current.getTableColumns();
    if (!graphDataList.length) {
      notification.info({
        message: intl.get(`${modelPrompt}.noData`).d('暂无数据'),
      });
    } else {
      graphicRef.current.downloadImage();
      excelHeaders.shift();
      excelAction.exportExcel(
        excelHeaders.filter(ele => ele.dataIndex !== 'index'),
        graphDataList,
        '图形展示.xlsx',
      );
    }
  };

  const openSettingModal = () => {
    dispatch({
      type: 'update',
      payload: {
        chartSetingModalVisible: true,
      },
    });
  };

  // 点击节点，打开数据点详细信息弹窗
  const onHandleClick = (_, dataIndex, clickIndex) => {
    const _modalProps = {
      ...dataStore[chartUuid][dataIndex],
      clickIndex, // 判断是上图还是下图，0上1下
    };
    if (chartData.sampleType === 'MEASURE') {
      _modalProps.measureModalVisible = true;
    } else {
      _modalProps.countModalVisible = true;
    }
    dispatch({
      type: 'update',
      payload: {
        pointInfoModalProps: _modalProps,
      },
    });
  };

  // 鼠标放图上
  const onHover = (_, dataIndex) => {
    dispatch({
      type: 'update',
      payload: {
        activePorintIndex: dataIndex,
      },
    });
  };

  const chartProps = {
    onHandleClick,
    onHover,
    chartData: graphicChartState.realChartData,
    chartToolsConfig: graphicChartState.chartToolsConfig,
    ref: graphicRef,
  };

  const tableProps = {
    graphicInfo: { sampleType: chartData.sampleType === 'MEASURE' ? 'measure' : 'count' },
    activePorintIndex: graphicChartState.activePorintIndex,
    onTableRowClick: e => e,
    ref: tableRef,
    dataSource: graphicChartState.graphDataList,
  };

  const chartSettingModalProps = {
    title: intl.get(`${modelPrompt}.graphSetting`).d('图形设置'),
    modalVisible: graphicChartState.chartSetingModalVisible,
    initData: graphicChartState.chartToolsConfig,
    onCancel: () => {
      dispatch({
        type: 'update',
        payload: {
          chartSetingModalVisible: false,
        },
      });
    },
    getChartSettingData: (params = {}) => {
      dispatch({
        type: 'update',
        payload: {
          chartToolsConfig: params,
        },
      });
    },
  };

  const pointInfoModalCancel = () => {
    dispatch({
      type: 'update',
      payload: {
        pointInfoModalProps: {
          measureModalVisible: false,
          countModalVisible: false,
        },
      },
    });
  };

  return (
    <div className={styles['graphic-chart']}>
      <Spin spinning={loading}>
        <ExpandCardC7n
          showExpandIcon={false}
          title={
            title && typeof title === 'string'
              ? title
              : intl.get(`${modelPrompt}.graphicDisplay`).d('图形展示')
          }
          differTitle={title && typeof title === 'object' ? title : null}
          extra={
            <>
              <Popconfirm
                title={intl.get(`${modelPrompt}.message.whetherExport`).d('是否导出当前数据？')}
                okText={intl.get('tarzan.common.button.confirm').d('确定')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                onConfirm={handleDownLoad}
              >
                <Icon type="get_app" />
              </Popconfirm>
              <Icon type="settings" onClick={openSettingModal} />
            </>
          }
        >
          {chartData.data.length ? (
            <>
              <Chart {...chartProps} />
              <Table {...tableProps} />
            </>
          ) : (
            <></>
          )}
        </ExpandCardC7n>
      </Spin>
      <ChartSettingModal {...chartSettingModalProps} />
      <MeasureTypeModal
        {...graphicChartState.pointInfoModalProps}
        onCancel={pointInfoModalCancel}
      />
      <CountTypeModal {...graphicChartState.pointInfoModalProps} onCancel={pointInfoModalCancel} />
    </div>
  );
};

export default GraphicChart;
