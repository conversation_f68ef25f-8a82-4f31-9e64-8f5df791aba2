/**
 * @Description: 事件类型维护 接口
 * @Author: <<EMAIL>>
 * @Date: 2022-11-03 09:36:07
 * @LastEditTime: 2022-11-03 14:02:20
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const tenantId = getCurrentOrganizationId();

// 事件类型维护-保存
export function saveEventTypeConfig(): object {
  return {
    url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-event-type/save/ui`,
    method: 'POST',
  };
}

// 事件对象类型维护-展示列维护-保存
export function saveEventObjectTypeRelDrawerConfig(): object {
  return {
    url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-event-object-type-rel/save/object-type/ui`,
    method: 'POST',
  };
}
