import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { isUndefined } from 'lodash';
import uuid from 'uuid/v4';
import request from 'utils/request';

const modelPrompt = 'tarzan.ScrapDocQuery';
const tenantId = getCurrentOrganizationId();
const url = `${BASIC.HWMS_BASIC}/v1/${tenantId}/wms-scrap-doc-plantform/identify/get/ui`;

const headerFormDS = () => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  dataKey: 'rows.instructionDoc',
  cacheSelection: true,
  transport: {
    read: () => {
      const url = `${BASIC.HWMS_BASIC}/v1/${tenantId}/wms-scrap-doc-plantform/detail/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_DETAIL.LINE`;
      return {
        url,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
      disabled: true,
    },
    {
      name: 'instructionDocId',
      type: FieldType.string,
    },
    {
      name: 'instructionDocTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lovCode: `${BASIC.WMS_LOV_CODE_BEFORE}.MES.INSTRUCTION_DOC_BUSINESS`,
      valueField: 'instructionDocType',
      textField: 'instructionDocTypeDesc',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'accountCategory',
      type: FieldType.string,
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      bind: 'instructionDocTypeObj.instructionDocType',
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      bind: 'instructionDocTypeObj.instructionDocTypeDesc',
    },
    {
      name: 'businessTypes',
      type: FieldType.string,
      bind: 'instructionDocTypeObj.businessTypes',
    },
    {
      name: 'queryType',
      type: FieldType.string,
      bind: 'instructionDocTypeObj.queryType',
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_REQUISITION`,
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      disabled: true,
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      noCache: true,
      lovCode: 'APEX_WMS.MODEL.SITE',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      required: true,
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'accountTypeList',
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'accountType',
      type: FieldType.string,
    },
    {
      name: 'accountTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accountType`).d('结算类型'),
      // bind: 'instructionDocTypeObj.accountType',
      disabled: true,
      // textField: 'description',
      // valueField: 'typeCode',
      // required: true,
      // dynamicProps: {
      //   options: ({ record }) => {
      //     const data = [];
      //     const accountTypeList = record?.get('accountTypeList') || [];
      //     accountTypeList.forEach(item => {
      //       const itemkeys = Object.keys(item);
      //       itemkeys.forEach(keys => {
      //         data.push({
      //           description: item[keys],
      //           typeCode: keys,
      //         });
      //       });
      //     });
      //     return new DataSet({
      //       data,
      //     });
      //   },
      //   disabled: ({ record }) => {
      //     return !record?.get('instructionDocType');
      //   },
      // },
    },
    {
      name: 'costcenter',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.costcenter`).d('成本中心'),
      lovCode: `${BASIC.WMS_LOV_CODE_BEFORE}.MES.COST_CENTER`,
      textField: 'costcenterCode',
      valueField: 'costcenterId',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            accountType: 'COST_CENTER',
            accountCategoryList: record?.get('accountCategoryList'),
            siteId: record?.get('siteId'),
            enableFlag: 'Y',
            accountCategory: record?.get('accountCategory'),
          };
        },
        required: ({ record }) => {
          return record?.get('accountType') && record?.get('accountType') === 'COST_CENTER';
        },
        disabled: ({ record }) => {
          return (
            !record?.get('siteId') ||
            !record?.get('accountType') ||
            !(record?.get('accountType') === 'COST_CENTER')
          );
        },
      },
    },
    {
      name: 'costcenterId',
      type: FieldType.string,
      bind: 'costcenter.costcenterId',
    },
    {
      name: 'costcenterCode',
      type: FieldType.string,
      bind: 'costcenter.costcenterCode',
    },
    {
      name: 'costcenterCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.costcenterCategory`).d('成本中心类型'),
      disabled: true,
      // bind: 'costcenter.accountCategoryDesc',
    },
    {
      name: 'internalOrder',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.internalOrder`).d('内部订单'),
      lovCode: `${BASIC.WMS_LOV_CODE_BEFORE}.MES.INTERNAL_ORDER`,
      ignore: 'always',
      textField: 'costcenterCode',
      valueField: 'costcenterId',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            accountType: 'INTERNAL_ORDER',
            accountCategoryList: record?.get('accountCategoryList'),
            siteId: record?.get('siteId'),
            enableFlag: 'Y',
            accountCategory: record?.get('accountCategory'),
          };
        },
        required: ({ record }) => {
          return record?.get('accountType') && record?.get('accountType') === 'INTERNAL_ORDER';
        },
        disabled: ({ record }) => {
          return (
            !record?.get('siteId') ||
            !record?.get('accountType') ||
            !(record?.get('accountType') === 'INTERNAL_ORDER')
          );
        },
      },
    },
    {
      name: 'internalOrderId',
      bind: 'internalOrder.costcenterId',
    },
    {
      name: 'internalOrderCode',
      bind: 'internalOrder.costcenterCode',
    },
    {
      name: 'internalOrderCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.internalOrderCategory`).d('内部订单类型'),
      disabled: true,
      // bind: 'internalOrder.accountCategoryDesc',
    },
    {
      name: 'warehouse',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouse`).d('仓库'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR_BY_ORG',
      noCache: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: record?.get('siteId'),
            locatorCategoryList: ['AREA'],
            businessTypes: record?.get('businessTypes'),
            queryType: record?.get('queryType'),
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
      },
    },
    {
      name: 'warehouseId',
      bind: 'warehouse.locatorId',
    },
    {
      name: 'locator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locator`).d('库位'),
      lovCode: 'APEX_WMS.MODEL.SUB_LOCATOR',
      noCache: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorIds: record?.get('warehouseId'),
            queryType: 'ALL',
          };
        },
        disabled: ({ record }) => {
          return !record?.get('warehouseId');
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      lookupCode: 'SOURCE_SYSTEM',
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
});

const lineTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  dataKey: 'content',
  selection: false,
  fields: [
    {
      name: 'instructionDocLineId',
      type: FieldType.string,
    },
    {
      name: 'instructionId',
      type: FieldType.string,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      required: true,
      min: 0,
      validator: (value, name, record) => {
        let _flag = 0;
        record.dataSet.toData().forEach(item => {
          if (item.lineNumber === value) {
            _flag++;
          }
        });
        if (_flag > 1) {
          return intl.get(`${modelPrompt}.lineNumberRepeated`).d('行号重复');
        }
        return true;
      },
    },
    {
      name: 'identifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
      lookupCode: 'APEX_WMS.APS.GEN_TYPE_URL',
      lovPara: {
        typeGroup: 'IDENTITY_TYPE',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      valueField: 'typecode',
      textField: 'description',
    },
    {
      name: 'site',
      type: FieldType.object,
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'APEX_WMS.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'material.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'material.revisionFlag',
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
      bind: 'material.decimalNumber',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialRevisionCode`).d('物料版本'),
      textField: 'description',
      valueField: 'description',
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            materialId: record?.get('materialId'),
            siteIds: [record?.get('siteId')],
          };
        },
        disabled: ({ record }) => {
          return record?.get('revisionFlag') !== 'Y';
        },
        required: ({ record }) => {
          return record?.get('revisionFlag') === 'Y';
        },
        lookupAxiosConfig: ({ record }) => {
          return {
            transformResponse(data) {
              if (Array.isArray(data)) {
                return data;
              }
              if (!isUndefined(data) && data.length > 0) {
                const { rows } = JSON.parse(data);
                const firstlyQueryData = [];
                if ((rows || []).length) {
                  rows.forEach(item => {
                    firstlyQueryData.push({
                      kid: uuid(),
                      description: item,
                    });
                  });
                }
                if (record) {
                  if (firstlyQueryData.length > 0) {
                    if (!record?.get('revisionCode')) {
                      record.init('revisionCode', firstlyQueryData[0].description);
                    }
                  } else {
                    record.init('revisionCode', null);
                  }
                }
                return firstlyQueryData;
              }
            },
          };
        },
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'material.materialName',
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.quantity`).d('需求数量'),
      required: true,
      // min: 0,
      dynamicProps: {
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
      },
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      bind: 'material.uomCode',
    },
    {
      name: 'uomId',
      type: FieldType.string,
      bind: 'material.uomId',
    },
    {
      name: 'instructionStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_REQUISITION`,
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      disabled: true,
    },
    {
      name: 'soNumberObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.soNumber`).d('销售订单'),
      lovCode: `${BASIC.WMS_LOV_CODE_BEFORE}.MES.SO_LINE`,
      // valueField: 'soId',
      valueField: 'soLineId',
      // textField: 'soNumber',
      noCache: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId') || record?.get('instructionDocLineId');
        },
      },
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      bind: 'soNumberObj.soNumber',
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
      bind: 'soNumberObj.soLineNum',
    },
    {
      name: 'sourceOrderId',
      type: FieldType.string,
      bind: 'soNumberObj.soId',
    },
    {
      name: 'sourceOrderLineId',
      type: FieldType.string,
      bind: 'soNumberObj.soLineId',
    },
    {
      name: 'warehouse',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouse`).d('仓库'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR_BY_ORG',
      noCache: true,
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: record?.get('siteId'),
            locatorCategoryList: ['AREA'],
            businessTypes: record?.get('businessTypes'),
            queryType: record?.get('queryType'),
          };
        },
        required: ({ record }) => {
          return record?.get('locatorRequiredFlag') === 'Y';
        },
      },
    },
    {
      name: 'warehouseId',
      type: FieldType.string,
      bind: 'warehouse.locatorId',
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      bind: 'warehouse.locatorCode',
    },
    {
      name: 'locator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locator`).d('库位'),
      lovCode: 'APEX_WMS.MODEL.SUB_LOCATOR',
      noCache: true,
      textField: 'locatorCode',
      valueField: 'locatorId',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('warehouseId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorIds: record?.get('warehouseId'),
          };
        },
      },
    },
    {
      name: 'locatorId',
      type: FieldType.string,
      bind: 'locator.locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'locator.locatorCode',
    },
    {
      name: 'sourceSumOnhandQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.fromQty`).d('库存现有量'),
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'toleranceType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('toleranceFlag') === 'Y';
        },
        disabled: ({ record }) => {
          return record?.get('toleranceFlag') === 'N';
        },
      },
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差'),
      min: 0,
      dynamicProps: {
        required: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'Y' &&
            (record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER')
          );
        },
        disabled: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'N' ||
            (record?.get('toleranceType') !== 'PERCENTAGE' &&
              record?.get('toleranceType') !== 'NUMBER')
          );
        },
        max: ({ record }) => {
          if (record?.get('toleranceType') === 'PERCENTAGE') {
            return 100;
          }
        },
      },
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差'),
      min: 0,
      dynamicProps: {
        required: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'Y' &&
            (record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER')
          );
        },
        max: ({ record }) => {
          if (record?.get('toleranceType') === 'PERCENTAGE') {
            return 100;
          }
        },
        disabled: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'N' ||
            (record?.get('toleranceType') !== 'PERCENTAGE' &&
              record?.get('toleranceType') !== 'NUMBER')
          );
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  events: {
    update: ({record, name}) => {
      if (
        name === 'material' ||
        name === 'warehouse' ||
        name === 'locator'
      ) {
        const {
          material,
          warehouse,
          locator,
          siteId,
        } = record.toData();
        const obj = {
          materialId: material?.materialId,
          warehouseId: warehouse?.locatorId,
          locatorId: locator?.locatorId,
          siteId,
        };
        const params = {};
        Object.entries(obj).forEach(item => {
          if (item[1] !== undefined) {
            params[item[0]] = item[1];
          }
        });
        request(url, {
          method: 'GET',
          query: {...params},
        }).then(res => {
          if (res?.success) {
            if (res?.rows) {
              record.init('identifyType', res.rows);
            }
          }
        });
      }
    },
  },
});

export { headerFormDS, lineTableDS };
