// 关于检查项的一些共用方法

import React from 'react';
import { Popconfirm } from 'choerodon-ui';
import {
  DataSet,
  Modal,
  Form,
  Output,
  NumberField,
  Select,
  TextField,
  Button,
} from 'choerodon-ui/pro';
import classNames from 'classnames';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import ChecklistEditModal from 'alm/components/ChecklistEditModal';
import { getOptions, saveOptions } from 'alm//components/ChecklistEditModal/api';
import { omit } from 'lodash';
import FileUpload from '../FileUpload';
import getLangs from './Langs';
import { saveCheckList, deleteCheckList, createMeter } from './api';
import styles from './index.module.less';

const organizationId = getCurrentOrganizationId();

// 检查项table公共列
export function getColumns(withSeq, props, funcs) {
  const { type, isEditStatus, isEndStatus, parentStatus, isWoChecklist, woopStatus } = props;
  // isWoChecklist：区分是工单检查项 还是任务检查项 （对于点巡检 只有单据开/完工tab才是工单检查项  检查项tab是任务检查项）
  const { handleEdit, handleView } = funcs;

  //  记录按钮显示逻辑：
  //  1.单据检查项：单据状态为非“拟定、需改派、取消、工作完成”
  //  2.任务检查项：任务状态为执行中或任务状态为工作完成单据状态不为工作完成时
  const showRecordBtn = isWoChecklist
    ? !['DRAFT', 'COMPLETED', 'CANCELED', 'WRD'].includes(parentStatus)
    : woopStatus === 'INPRG' || (woopStatus === 'COMPLETED' && parentStatus !== 'COMPLETED');

  const cols = [
    {
      name: 'checklistName',
    },
    {
      name: 'businessScenarioMeaning',
      width: 150,
    },
    {
      name: 'columnTypeMeaning',
      width: 150,
    },
    {
      name: 'sourceType',
      width: 100,
      // 展示数据来源如果是标准检查组创建，则展示“标准检查组”，如果来源是手工创建则展示“手工”
      renderer: ({ record }) => {
        return record.get('sourceId') ? '标准检查组' : '手工';
      },
    },
    {
      name: 'sourceName',
      // 如果来源类型“手工”，这里为空。如果来源来行“标准检查组”，则展示标准检查组名称。
    },
    {
      name: 'actValue',
      renderer: ({ text, record }) => {
        const { columnTypeCode, actValueMeaning } = record.toData();
        let returnVal = '';
        switch (columnTypeCode) {
          case 'YESORNO':
          case 'LISTOFVALUE':
            returnVal = actValueMeaning;
            break;
          default:
            returnVal = text;
            break;
        }
        return returnVal;
      },
    },
    {
      name: 'description',
    },
    {
      header: getLangs('OPTION'),
      width: 160,
      lock: 'right',
      renderer: ({ record, dataSet }) => {
        const data = record.toData();
        return (
          <>
            {/* 拟定、需改派才可以编辑 */}
            {isEditStatus && (
              <a onClick={() => handleEdit(data)} style={{ marginRight: 8 }}>
                {getLangs('EDIT')}
              </a>
            )}
            {/* 除了拟定 需改派 都可以查看 */}
            {!['DRAFT', 'WRD'].includes(parentStatus) && (
              <a onClick={() => handleView(data)} style={{ marginRight: 8 }}>
                {getLangs('VIEW')}
              </a>
            )}
            {/* 非索引项才显示 */}
            {data.columnTypeCode !== 'INDEX' && showRecordBtn && (
              <a onClick={() => handleRecord(data, props)} style={{ marginRight: 8 }}>
                {getLangs('RECORD')}
              </a>
            )}
            {/* 当单据状态为工作完成、取消时，不可删除 */}
            {!isEndStatus && (
              <Popconfirm
                title={getLangs('DELETE_CONFIRM')}
                placement="topRight"
                onConfirm={() => handleDeleteCk(data, props)}
                okText={getLangs('YES')}
                cancelText={getLangs('NO')}
              >
                {/* 点巡检单点位的最后一个检查项删除按钮置灰 */}
                <a
                  style={{ marginRight: 8 }}
                  disabled={
                    type === 'PSC' &&
                    !isWoChecklist &&
                    !record.parent &&
                    (dataSet.totalCount === 1 || dataSet?.treeData?.length === 1)
                  }
                >
                  {getLangs('DELETE')}
                </a>
              </Popconfirm>
            )}
          </>
        );
      },
    },
  ];

  return withSeq
    ? [
        {
          name: 'itemSeq',
          width: 80,
        },
      ].concat(cols)
    : cols;
}

function handleDeleteCk(data, props) {
  deleteCheckList(data).then(res => {
    commonReqCallback(res, props);
  });
}

function commonReqCallback(res, props) {
  if (res && res.failed) {
    notification.warning({ message: res?.message });
  } else {
    notification.success();
    // 刷新列表
    props.tableDs?.query(); // eslint-disable-line
    // 刷新异常检查项
    props.abTableDs?.query(); // eslint-disable-line
  }
}

// ------------  记录实际值 start ------------

// 打开记录实际值model
export async function handleRecord(record, props) {
  const ds = initDs(record);
  const { checklistName, columnTypeCode, checklistId } = record;

  let valueList = [];
  const isListType = columnTypeCode === 'YESORNO' || columnTypeCode === 'LISTOFVALUE';
  if (isListType) {
    const res = await getOptions({
      checklistId,
      checklistType: 'WO', // 工单和点巡检是WO，标准检查组是BASE，其它都是ACT
    });
    if (res && !res.failed) {
      valueList = res;
    } else {
      notification.warning({ message: res.message });
    }
  }

  Modal.open({
    key: 'actualValue',
    destroyOnClose: true,
    closable: true,
    style: {
      width: 600,
    },
    title: getHeader(checklistName),
    children: getRecordModalChild(record, ds, valueList, props),
    onOk: () => handleRecordModalOk(record, ds, props),
    footer: (okBtn, cancelBtn) => (
      <>
        {cancelBtn}
        {okBtn}
      </>
    ),
  });
}

function initDs(record) {
  const {
    columnTypeCode,
    actValue,
    description,
    methodCode,
    standardReference,
    meterClassCode,
    meterName,
  } = record;

  const isSelectMeter = ['RUN_STATUS', 'MONITOR_STATUS'].includes(meterClassCode);
  const meterCodeMap = {
    RUN_STATUS: 'AMTR.METER_STATUS',
    MONITOR_STATUS: 'AMTC.READING_VALUE',
  };

  const standardReferenceList = columnTypeCode === 'VALUE' ? JSON.parse(standardReference) : standardReferenceList;

  const actValueProps =
    columnTypeCode === 'NUMBER' && !isSelectMeter
      ? {
          type: 'number',
          precision: 5,
          pattern: /^-?\d{1,11}(\.\d{1,5})?$/,
          defaultValidationMessages: {
            patternMismatch: getLangs('LEN_LIMIT'),
          },
        }
      : columnTypeCode === 'NUMBER' && isSelectMeter
      ? {
          type: 'string',
          lookupCode: meterCodeMap[meterClassCode],
        }
      : {
          type: 'string',
        };

  const actualValueDs = new DataSet({
    autoCreate: true,
    fields: [
      {
        name: 'actValue',
        label: getLangs('ACT_VALUE'),
        ...actValueProps,
      },
      {
        name: 'description',
        label: getLangs('DESC'),
        type: 'string',
        maxLength: 240,
      },
      {
        name: 'standardReference',
        label: getLangs('STANDARD'),
        type: 'string',
      },
      {
        name: 'methodCode',
        label: getLangs('METHOD'),
        type: 'string',
      },
      {
        name: 'meterName',
        label: getLangs('METER'),
        type: 'string',
      },
    ],
  });

  // 初始化值
  actualValueDs.current.set('actValue', actValue);
  actualValueDs.current.set('description', description);
  actualValueDs.current.set('standardReference', standardReferenceList);
  actualValueDs.current.set('methodCode', methodCode);
  actualValueDs.current.set('meterName', meterName);

  return actualValueDs;
}

function getHeader(title) {
  return (
    <div className={styles['modal-header-cus']}>
      <div className={styles['header-title']}>
        <span className={styles['modal-header-divider']} />
        <span className={styles['font-style']}>{getLangs('CHECKLIST_NAME')}：</span>
      </div>
      <div className={classNames('font-style', 'header-content')}>{title}</div>
    </div>
  );
}

function getRecordModalChild(record, ds, valueList, props) {
  const { columnTypeCode, methodCode, standardReference, meterClassCode, meterUomName, woId, businessScenarioCode, checklistId, woopId } = record;

  const isNumber = columnTypeCode === 'NUMBER';
  // 仪表类型为：监控状态、运行状态 时为下拉框
  const isSelectMeter = ['RUN_STATUS', 'MONITOR_STATUS'].includes(meterClassCode);
  const isListType = columnTypeCode === 'YESORNO' || columnTypeCode === 'LISTOFVALUE';

  return (
    <div>
      <Form dataSet={ds} columns={2} labelWidth={70} labelLayout="horizontal">
        {isNumber && <Output name="meterName" colSpan={2} />}
        {isListType ? (
          <Select name="actValue">
            {valueList.map(i => (
              <Select.Option key={`option_${i.actValue}`} value={i.actValue}>
                {i.description}
              </Select.Option>
            ))}
          </Select>
        ) : isNumber && !isSelectMeter ? (
          <NumberField
            name="actValue"
            placeholder={getLangs('PLEASE_INPUT')}
            suffix={meterUomName}
          />
        ) : isNumber && isSelectMeter ? (
          <Select name="actValue" />
        ) : (
          <TextField name="actValue" />
        )}
        <FileUpload
          uploadButtonName={{ code: 'asset-attachment-management', name: '附件管理' }}
          moduleName="amtc-check"
          moduleId={checklistId}
          woId={woId}
          woopId={woopId}
          collectionCode={`CHECK-${businessScenarioCode}`}
          parentTypeCode={props.sourceType || 'WO_CHECK'}
          showDeleteFlag
          type="CHECK_BUTTON"
        />
        <TextField name="description" colSpan={2} newLine />
        {methodCode && <Output name="methodCode" colSpan={2} />}
        {standardReference && <Output name="standardReference" colSpan={2} multiple={columnTypeCode === 'VALUE'} />}
      </Form>
    </div>
  );
}

function handleRecordModalOk(record, ds, props) {
  const { minimumWordLimit, columnTypeCode } = record;
  const detail = ds.current?.toData() || {};

  // 文本类型的 对字数进行校验
  if (columnTypeCode === 'TEXT' && detail.actValue.length < minimumWordLimit) {
    const msg = `${getLangs('INPUT')}${minimumWordLimit}${getLangs('WORDS')}`;
    notification.warning({ message: msg });
    return;
  }
  record.standardReference = JSON.stringify(record.standardReference);
  detail.standardReference = JSON.stringify(detail.standardReference);
  const checklistInfo = {
    tenantId: organizationId,
    ...record,
    ...detail,
    sourceType: props.sourceType || 'WO_CHECK', // 点巡检单WO_CHECK 工单WO
  };
  saveCheckList(checklistInfo).then(saveRes => {
    // 如果是仪表点类型/是否类型的话 需要走检查项创建仪表点的逻辑 否则直接走原本的回调处理逻辑（报错及刷新）：
    // 没有自定义回调recordOkCallback则使用commonReqCallback
    if (
      saveRes &&
      !saveRes.failed &&
      ['NUMBER', 'YESORNO'].includes(checklistInfo.columnTypeCode)
    ) {
      // 检查项创建仪表点
      createMeter(checklistInfo).then(res => {
        // 无论创建仪表点成功与否 都需要刷新数据 因为上一步记录了实际值后还没刷新
        if (props.recordOkCallback) {
          props.recordOkCallback(record, res);
        } else {
          // 刷新列表
          props.tableDs?.query(); // eslint-disable-line
          // 刷新异常检查项
          props.abTableDs?.query(); // eslint-disable-line
          if (res && res.failed) {
            notification.warning({ message: res?.message });
          } else {
            notification.success();
          }
        }
      });
    } else if (props.recordOkCallback) {
      props.recordOkCallback(record, saveRes);
    } else {
      commonReqCallback(saveRes, props);
    }
  });
}

// ------------ 记录实际值 end ------------

// ------------ 打开 检查项编辑/查看modal start ------------
// 目前仅点巡检在使用
export function openCkEditModal({ isNew, isEdit = false, currentData = {}, props }) {
  const { parentId, parentName, detail } = props;
  const { woStatus, maintSiteId } = detail;
  const modalProps = {
    isNew,
    tenantId: organizationId,
    editFlag: isEdit,
    compParentId: parentId,
    compParentName: parentName,
    compParentType: 'SPOT_CHECK',
    statusFlag: isNew || ['DRAFT', 'WRD'].includes(woStatus), // 控制部分字段在 新建 需改派 下才可编辑
    dataSource: currentData,
    maintSiteIds: [maintSiteId],
  };

  const editModalRef = React.createRef();

  const _editModal = Modal.open({
    key: 'editModal',
    destroyOnClose: true,
    maskClosable: !isNew && !isEdit,
    closable: true,
    drawer: true,
    style: {
      width: 700,
    },
    title: getLangs('TITLE_CHECKLIST'),
    okText: getLangs('SAVE'),
    onOk: () => handleCkEditModalOk({ editModalRef, props }),
    children: <ChecklistEditModal {...modalProps} ref={editModalRef} />,
    footer: (okBtn, cancelBtn) =>
      getCkEditModalFooter({ okBtn, cancelBtn, isNew, isEdit, _editModal }),
  });
}

function getCkEditModalFooter({ okBtn, cancelBtn, isNew, isEdit, _editModal }) {
  return isNew || isEdit ? (
    <div>
      {okBtn}
      {cancelBtn}
    </div>
  ) : (
    <Button key="back" onClick={() => _editModal.close()}>
      {getLangs('CLOSE')}
    </Button>
  );
}

async function handleCkEditModalOk({ editModalRef, props }) {
  const result = await editModalRef?.current?.detailDs.current.validate(true);

  if (result) {
    const {
      detail: { woId },
    } = props;
    const detail = editModalRef.current.detailDs.current.toData();
    const trueValue = editModalRef.current.trueNumberDs.toData();
    const alarmFlag = detail.isThereAlarm
    const { columnTypeCode, listValueCode } = detail;
    const isOk = columnTypeCode === 'LISTOFVALUE' ? true : await editModalRef?.current?.optionsTableDs.validate();
    const optionsList = editModalRef?.current?.optionsTableDs?.records?.map(i => i.toData());
    saveCheckList({
      ...omit(detail, 'standardReference'),
      standardReferenceList: trueValue,
      woId,
    }).then(res => {
      if (res && res.failed) {
        notification.warning({ message: res?.message });
      } else if (
        columnTypeCode === 'YESORNO' ||
        (columnTypeCode === 'LISTOFVALUE' && listValueCode) || alarmFlag !== null || alarmFlag !== undefined
      ) {
        if (isOk || alarmFlag !== null || alarmFlag !== undefined) {
          saveOptions({
            list: alarmFlag && columnTypeCode === 'VALUE' ? [{ alarmFlag }] : optionsList,
            checklistId: res.checklistId,
            compParentType: 'SPOT_CHECK',
          }).then(res1 => {
            if (res1 && !res1.failed) {
              notification.success();
              // 刷新列表
              props.tableDs?.query(); // eslint-disable-line
              // 刷新异常检查项
              props.abTableDs?.query(); // eslint-disable-line
            } else {
              notification.warning({
                message: res1.message,
              });
            }
          });
        }
      } else {
        notification.success();
        // 刷新列表
        props.tableDs?.query(); // eslint-disable-line
        // 刷新异常检查项
        props.abTableDs?.query(); // eslint-disable-line
      }
    });
  } else {
    return false;
  }
}

// ------------ 打开 检查项编辑/查看modal end ------------
