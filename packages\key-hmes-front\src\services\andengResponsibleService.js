/**
 * andengResponsible - 安灯类型责任人维护
 * @author: <EMAIL>
 * @version: 0.0.1
 */

import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, parseParameters } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const prefix = BASIC.HMES_BASIC;

export async function fetchHeadList(params) {
  const newParams = parseParameters(params);
  return request(`${prefix}/v1/${tenantId}/hme-exception-type-persons/query/ui`, {
    method: 'GET',
    query: newParams,
  });
}

export async function saveHeadList(params) {
  return request(`${prefix}/v1/${tenantId}/hme-exception-type-persons/save/ui`, {
    method: 'POST',
    body: params,
  });
}

export async function deleteHeadList(params) {
  return request(`${prefix}/v1/${tenantId}/hme-exception-type-persons/delete/ui`, {
    method: 'POST',
    body: params,
  });
}
