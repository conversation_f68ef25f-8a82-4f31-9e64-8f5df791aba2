import React, { Component } from 'react';
import { Table, Button, Modal } from 'choerodon-ui/pro';
import { Icon, Spin, Tag } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';
import request from 'utils/request';
import { omit } from 'lodash';
import notification from 'utils/notification';
import classNames from 'classnames';
import formatterCollections from 'utils/intl/formatterCollections';
import ChecklistEditModal from 'alm/components/ChecklistEditModal';
import { saveOptions } from 'alm//components/ChecklistEditModal/api';

import getLangs from '../../Langs';

const organizationId = getCurrentOrganizationId();
@formatterCollections({
  code: ['alm.common', 'alm.component', 'alm.checklistEditModal'],
})
export default class ChecklistList extends Component {
  constructor(props) {
    super(props);
    this.editModalRef = React.createRef();
  }

  // 打开 检查项modal
  @Bind
  openEditModal(isNew = false, editFlag = false, currentData = {}) {
    const { maintSiteIds } = this.props.detailDs.current.toData();
    const woChecklistEditProps = {
      isNew,
      editFlag,
      compParentType: 'CHECKLIST_GROUP',
      tenantId: organizationId,
      dataSource: currentData,
      maintSiteIds,
    };
    this._editModal = Modal.open({
      key: 'editModal',
      destroyOnClose: true,
      closable: true,
      drawer: true,
      style: {
        width: 700,
      },
      title: getLangs('TITLE_CHECK'),
      okText: getLangs('SAVE'),
      onOk: this.handleModalOk,
      children: <ChecklistEditModal {...woChecklistEditProps} ref={this.editModalRef} />,
      footer: (okBtn, cancelBtn) => this.getFooter(okBtn, cancelBtn),
    });
  }

  // 没有查看的情况，故暂不做处理
  getFooter(okBtn, cancelBtn) {
    return (
      <div>
        {cancelBtn}
        {okBtn}
      </div>
    );
  }

  @Bind
  handleGetParent() {
    const { checklistGroupId, groupTypeCode } = this.props;
    return {
      parentTypeCode: groupTypeCode, // 为了控制检查时点取值
      parentId: checklistGroupId, // 为了作为父项、检查时点查询接口的参数
    };
  }

  @Bind
  handleCreate() {
    const data = this.handleGetParent();
    this.openEditModal(true, false, data);
  }

  @Bind
  handleEdit(record) {
    const data = this.handleGetParent();
    this.openEditModal(false, true, {
      ...record.toData(),
      ...data,
    });
  }

  @Bind
  async handleModalOk() {
    const result = await this.editModalRef?.current?.detailDs.current.validate(true);
    if (result) {
      const { checklistGroupId, checklistListDs } = this.props;
      const detail = this.editModalRef.current.detailDs.current.toData();
      const trueValue = this.editModalRef.current.trueNumberDs.toData();
      const alarmFlag = detail.isThereAlarm
      const { columnTypeCode, listValueCode } = detail;
      const isOk = columnTypeCode === 'LISTOFVALUE' ? true : await this.editModalRef?.current?.optionsTableDs.validate();
      const optionsList = this.editModalRef?.current?.optionsTableDs?.records?.map(i => i.toData());

      return new Promise((resolve, reject) => {
        request(`${HALM_MTC}/v1/${organizationId}/base-checklists`, {
          method: 'POST',
          body: {
            ...omit(detail, 'standardReference'),
            standardReferenceList: trueValue,
            tenantId: organizationId,
            checklistGroupId,
          },
        }).then(async res => {
          if (res && !res.failed) {
            if (
              columnTypeCode === 'YESORNO' ||
              (columnTypeCode === 'LISTOFVALUE' && listValueCode) || alarmFlag !== null || alarmFlag !== undefined
            ) {
              if (isOk || alarmFlag !== null || alarmFlag !== undefined) {
                saveOptions({
                  list: alarmFlag && columnTypeCode === 'VALUE' ? [{ alarmFlag }] : optionsList,
                  checklistId: res.checklistId,
                  compParentType: 'CHECKLIST_GROUP',
                }).then(res1 => {
                  if (res1 && !res1.failed) {
                    notification.success();
                    checklistListDs.query();
                    resolve();
                  } else {
                    notification.warning({
                      message: res1.message,
                    });
                  }
                });
              }
            } else {
              notification.success();
              checklistListDs.query();
              resolve();
            }
          } else {
            notification.warning({
              message: res?.message,
            });
            reject();
          }
        });
      });
    } else {
      return false;
    }
  }

  // 移除
  @Bind
  handleDelete(record) {
    request(`${HALM_MTC}/v1/${organizationId}/base-checklists`, {
      method: 'DELETE',
      body: { ...record.toData() },
    }).then(res => {
      if (res && !res.failed) {
        // 删除最后一个检查项后，启用设为否
        const tempData = this.props.detailDs.current.toData();
        if (this.props.checklistListDs.length === 1) {
          Modal.warning({
            title: getLangs('NOTICE'),
            children: getLangs('NO_CHECK_LIST'),
            onOk: () => {
              this.props.detailDs.query().then(resData => {
                this.props.detailDs.current.set({
                  ...tempData,
                  objectVersionNumber: resData.objectVersionNumber,
                  enabledFlag: 0,
                });
              });
            },
          });
        }
        this.props.checklistListDs.query();
      } else {
        notification.warning({
          message: res?.message,
        });
      }
    });
  }

  @Bind
  handleDeleteConfirm(record) {
    Modal.warning({
      title: getLangs('NOTICE'),
      children: getLangs('DELETE_CONFIRM'),
      okCancel: true,
      onOk: () => {
        this.handleDelete(record);
      },
    });
  }

  @Bind
  handleLoadData({ record, dataSet }) {
    return new Promise(resolve => {
      if (!record.children) {
        record.setState('loadding', true);
        // 查询其下全部子数据（不是子孙数据）
        const checklistGroupId = record.get('checklistGroupId');
        const parentChecklistId = record.get('checklistId');

        // 查询下一级
        request(`${HALM_MTC}/v1/${organizationId}/base-checklists`, {
          method: 'GET',
          query: {
            tenantId: organizationId,
            checklistGroupId,
            parentChecklistId,
          },
        }).then(res => {
          if (res && !res.failed) {
            dataSet.appendData(res);
            record.setState('loadding', false);
          } else {
            notification.warning({
              message: res?.message,
            });
          }
        });
      } else {
        resolve();
      }
    });
  }

  // icon 渲染问题， 首先判断record的值和自定义状态来判断出叶节点和父节点进行不同的渲染
  expandicon({ prefixCls, expanded, expandable, record, onExpand }) {
    if (!record.get('childFlag')) {
      // 子结点渲染
      return <span style={{ paddingLeft: '0.18rem' }} />;
    }
    if (record.getState('loadding') === true) {
      // 自定义状态渲染
      return <Spin tip="loding" delay={200} size="small" />;
    }
    const iconPrefixCls = `${prefixCls}-expand-icon`;
    const classString = classNames(iconPrefixCls, {
      [`${iconPrefixCls}-expanded`]: expanded,
    });
    return (
      <Icon
        type="baseline-arrow_right"
        className={classString}
        onClick={onExpand}
        tabIndex={expandable ? 0 : -1}
      />
    );
  }

  get columns() {
    const { isEdit } = this.props;
    const cols = [
      {
        name: 'checklistName',
      },
      {
        name: 'checkTimingMeaning',
        width: 150,
      },
      {
        name: 'methodCode',
      },
      {
        name: 'standardReference',
        renderer: ({ value }) => {
          if (value) {
            const data = JSON.parse(value);
            return data.map(item => (<Tag>{item}</Tag>))
          }
          return '-';
        },
      },
      {
        name: 'columnTypeMeaning',
        width: 150,
      },
    ];
    const opCol = [
      {
        header: getLangs('OPTION'),
        width: 90,
        renderer: ({ record }) => {
          return (
            <>
              <a onClick={() => this.handleEdit(record)} style={{ marginRight: 8 }}>
                {getLangs('EDIT')}
              </a>
              <a onClick={() => this.handleDeleteConfirm(record)}>{getLangs('DELETE')}</a>
            </>
          );
        },
      },
    ];
    return isEdit ? cols.concat(opCol) : cols;
  }

  render() {
    const { isEdit, checklistListDs } = this.props;
    return (
      <React.Fragment>
        {isEdit && (
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button icon="add" onClick={this.handleCreate}>
              {getLangs('CREATE')}
            </Button>
          </div>
        )}
        <Table
          key="checklistGroupChecklist"
          customizedCode="AORI.CHECKLIST_GROUP.CHECKLIST"
          dataSet={checklistListDs}
          columns={this.columns}
          mode="tree"
          expandIcon={this.expandicon}
          treeLoadData={this.handleLoadData}
        />
      </React.Fragment>
    );
  }
}
