/**
 * @Description: 检验方案模板-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-02-15 10:49:38
 * @LastEditTime: 2023-03-09 10:04:25
 * @LastEditors: <<EMAIL>>
 */


import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

// 详情数据查询
export function fetchInspectSchemeTmp() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-scheme-tmp/detail/ui`,
    method: 'GET',
  };
}

// 详情数据保存
export function saveInspectSchemeTmp() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-scheme-tmp/save/ui`,
    method: 'POST',
  };
}

// 获取检验业务类型列表可输入权限
export function fetchRuleDtlConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-ins-bus-type-rule-dtl/list/ui`,
    method: 'GET',
  };
}

// 检验方案实例化检查
export function checkInspectSchemeTmpConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-scheme-tmp/instance/check/ui`,
    method: 'POST',
  };
}

// 检验方案实例化确认
export function inspectSchemeTmpConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-scheme-tmp/instance/ui`,
    method: 'POST',
  };
}

// 检验方案实例化必输值检查
export function checkAllInspectSchemeTmpConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-scheme-tmp/instance/check-all/ui`,
    method: 'POST',
  };
}

// 实例化
export function InstantiationInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-scheme-tmp/instance/detail/ui`,
    method: 'GET',
  }
}

// 检验项目详情
export function mtInspectItemDetailConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-item/detail/ui`,
    method: 'GET',
  };
}

