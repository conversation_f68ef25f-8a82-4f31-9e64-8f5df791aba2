import intl from 'utils/intl';
import { FieldIgnore, DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const tenantId = getCurrentOrganizationId();

const headTableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'msaTaskId',
  queryFields: [
    {
      name: 'msaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaCode`).d('MSA编号'),
    },
    {
      name: 'msaStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaStatus`).d('状态'),
      lookupCode: 'YP.QIS.MSA_STATUS',
    },
    {
      name: 'msaType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.masType`).d('MSA类型'),
      lookupCode: 'YP.QIS.MSA_TYPE',
    },
    {
      name: 'projectStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
      lookupCode: 'YP.QIS.PROJECT_STAGE',
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'modelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model`).d('量具型号'),
      lovCode: 'YP.QMS_TOOL_MODEL',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'modelName',
    },
    {
      name: 'modelId',
      bind: 'modelLov.toolModelId',
    },
    {
      name: 'msToolManageLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.msToolManage`).d('量具编号'),
      lovCode: 'YP.QIS.ALL_MT_TOOL',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
    },
    {
      name: 'msToolManageId',
      bind: 'msToolManageLov.msToolManageId',
    },
    {
      name: 'msToolManage',
      bind: 'msToolManageLov.toolCode',
    },
    {
      name: 'specialCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialCharacteristic`).d('特殊特性标识'),
      lookupCode: 'YP.QIS.SPECIAL_CHARACTERISTIC_FLAG',
    },
    {
      name: 'analyzedByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.analyzeBy`).d('分析人'),
      lovCode: 'YP.QIS.UNIT_USER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'realName',
    },
    {
      name: 'analyzedBy',
      bind: 'analyzedByLov.id',
    },
    {
      name: 'assistantByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.assistantBy`).d('协助人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
    },
    {
      name: 'assistantBy',
      bind: 'assistantByLov.userId',
    },
    {
      name: 'planStartTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planStartTimeFrom`).d('预计开始时间从'),
    },
    {
      name: 'planStartTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planStartTimeTo`).d('预计开始时间至'),
    },
    {
      name: 'planEndTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planEndTimeFrom`).d('预计结束时间从'),
    },
    {
      name: 'planEndTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planEndTimeTo`).d('预计结束时间至'),
    },
    {
      name: 'actualStartTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTimeFrom`).d('实际开始时间从'),
    },
    {
      name: 'actualStartTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTimeTo`).d('实际开始时间至'),
    },
    {
      name: 'actualFinishTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualFinishTimeFrom`).d('实际结束时间从'),
    },
    {
      name: 'actualFinishTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualFinishTimeTo`).d('实际结束时间至'),
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
    },
    {
      name: 'msaResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaResult`).d('分析结果'),
      lookupCode: 'YP.QIS.MSA_RESULT',
    },
    {
      name: 'sourceMsaTaskLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceMsaTask`).d('来源MSA'),
      lovCode: 'YP.QIS.MSA_CODE',
      ignore: FieldIgnore.always,
    },
    {
      name: 'sourceMsaTaskId',
      bind: 'sourceMsaTaskLov.msaTaskId',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteName',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'processLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      lovPara: { tenantId },
    },
    {
      name: 'processId',
      bind: 'processLov.workcellId',
    },
  ],
  fields: [
    {
      name: 'msaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaCode`).d('MSA编号'),
    },
    {
      name: 'msaStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaStatus`).d('状态'),
      lookupCode: 'YP.QIS.MSA_STATUS',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'msaTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.masType`).d('MSA类型'),
    },
    {
      name: 'projectStageDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectStage`).d('项目阶段'),
    },
    {
      name: 'projectNameDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectName`).d('项目名称'),
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
    },
    {
      name: 'specialCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialCharacteristic`).d('特殊特性标识'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('量具型号编码'),
    },
    {
      name: 'modelName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelName`).d('量具型号描述'),
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.speciesName`).d('量具种别描述'),
    },
    {
      name: 'msaAnalysisMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaAnalysisMethod`).d('MSA分析方法'),
      lookupCode: 'YP.QIS.MSA_ANALYSIS_METHOD',
      multiple: ',',
    },
    {
      name: 'toolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toolCode`).d('量具编码'),
    },
    {
      name: 'prodlineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodlineName`).d('产线'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序'),
    },
    {
      name: 'onlineFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onlineFlag`).d('是否在线'),
      lookupCode: 'YP.QIS.ONLINE_FLAG',
    },
    {
      name: 'analyzedByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analyzeBy`).d('分析人'),
    },
    {
      name: 'assistantByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assistantBy`).d('协助人'),
    },
    {
      name: 'completeTimeLimit',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completeTimeLimit`).d('完成时限'),
    },
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planStartTime`).d('预计开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planEndTime`).d('预计结束时间'),
    },
    {
      name: 'actualStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('实际开始时间'),
    },
    {
      name: 'actualFinishTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualFinishTime`).d('实际结束时间'),
    },
    {
      name: 'msaResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaResult`).d('分析结果'),
      lookupCode: 'YP.QIS.MSA_RESULT',
    },
    {
      name: 'sourceMsaTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceMsaTaskCode`).d('来源MSA'),
    },
    {
      name: 'improveByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.improveBy`).d('改善负责人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'creationByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationBy`).d('创建人'),
    },
    {
      name: 'sipFileUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.sipFileUuid`).d('SIP附件'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-msa-task/list/ui`,
        method: 'GET',
      };
    },
  },
});

const lineTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  fields: [
    {
      name: 'msaAnalysisMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaAnalysisMethod`).d('MSA分析方法'),
    },
    {
      name: 'checkLocation',
      type: FieldType.string,
    },
    {
      name: 'msaResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaResult`).d('分析结果'),
      lookupCode: 'YP.QIS.MSA_RESULT',
    },
    {
      name: 'msaConclusion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.msaConclusion`).d('分析结论'),
    },
    {
      name: 'analyzeDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.analyzexDate`).d('分析时间'),
    },
    {
      name: 'analyzeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analyzeStatus`).d('状态'),
    },
  ],
})

export { headTableDS, lineTableDS };
