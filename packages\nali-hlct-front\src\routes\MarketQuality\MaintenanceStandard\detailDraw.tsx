/**
 * RelationMaintain - 维修标准管理
 * @date: 2023-9-7
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
  DataSet,
  Form,
  TextField,
  Select,
  Lov,
  Button,
  Spin,
  DatePicker,
  Attachment,
  TextArea,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { maintainFileSaveConfig, QueryMaterialInfoConfig } from './services';
import styles from './index.module.less';
import { listTableManualDS, listTableProgrammeDS } from './stores/MaintenanceStandardDS';


const modelPrompt = 'tarzan.qms.maintenanceStandard';

function MaintenanceStandardEdit(props) {
  const { type, data, callBack, closeModal, user, activeTab } = props;

  // 新增保存维修手册
  const maintainFileSave = useRequest(maintainFileSaveConfig(), {
    manual: true,
    needPromise: true,
  });

  // 根据materialId获取物料相关信息
  const queryMaterialInfo = useRequest(QueryMaterialInfoConfig(), {
    manual: true,
    needPromise: true,
  });

  const editDs = useMemo(() => {
    if (activeTab === 'manual') {
      return new DataSet({
        ...listTableManualDS(),
      });
    }
    return new DataSet({
      ...listTableProgrammeDS(),
    });
  }, [type, activeTab]);

  const [canEdit, setCanEdit] = useState(false);

  useEffect(() => {
    if (type === 'create') {
      setCanEdit(true);
      editDs.loadData([
        { maintainFileStatus: 'NEW', createdBylov: user, creationDate: new Date() },
      ]);
    } else {
      setCanEdit(false);
      editDs.loadData([data]);
    }
  }, [data, type]);

  const handleChangeMaterial = value => {
    if (value?.materialId) {
      queryMaterialInfo.run({
        params: value?.materialId,
        onSuccess: res => {
          editDs.current?.set('batteryModel', res);
        },
      });
    } else {
      editDs.current?.set('batteryModel', undefined);
    }
  };

  const handleSave = async () => {
    const validateResult = await editDs.validate();
    if (!validateResult) {
      return false;
    }
    const parmas: any = editDs.toData()[0];

    let res;
    if (activeTab === 'manual') {
      res = await maintainFileSave.run({
        params: { ...parmas, maintainFileType: 'MANUAL' },
      });
    } else {
      res = await maintainFileSave.run({
        params: { ...parmas, maintainFileType: 'CASE' },
      });
    }

    if (res?.success) {
      notification.success({});
      if (type === 'create') {
        closeModal();
        callBack();
      } else {
        setCanEdit(prev => !prev);
        callBack();
      }
    }

    return false;
  };

  const handleClose = () => {
    closeModal();
  };

  const handleEdit = () => {
    setCanEdit(prev => !prev);
  };

  const handleCancel = () => {
    if (type === 'create') {
      closeModal();
    } else {
      setCanEdit(prev => !prev);
    }
  };

  return (
    <Spin spinning={maintainFileSave.loading}>
      <div className={styles.modalControl}>
        {!canEdit && (
          <>
            <Button color={ButtonColor.primary} onClick={handleClose}>
              {intl.get(`tarzan.common.button.close`).d('关闭')}
            </Button>
            <Button
              color={ButtonColor.primary}
              onClick={handleEdit}
              disabled={user?.id !== data?.createdBy || !['NEW', 'REJECTED'].includes(data.maintainFileStatus)}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          </>
        )}
        {canEdit && (
          <>
            <Button onClick={handleCancel}>
              {intl.get(`tarzan.aps.common.button.cancel`).d('取消')}
            </Button>
            <Button color={ButtonColor.primary} onClick={handleSave}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        )}
      </div>
      <Form dataSet={editDs} columns={2} labelWidth={112} disabled={!canEdit}>
        {activeTab === 'manual' && (
          <>
            <TextField name="maintainFileCode" />
            <TextField name="maintainFileName" />
            <Select name="maintainFileStatus" />
            <Lov name="batteryNumLov" onChange={handleChangeMaterial} />
            <TextField name="batteryModel" />
            <Select searchable name="vehicleModel" />
            <TextField name="startIntlCode" />
            <TextField name="endIntlCode" />
            <Lov name="replaceMaintainFileLov" />
            <Attachment name="maintainFileUuid" />
            <DatePicker name="creationDate" />
            <Lov name="createdBylov" />
          </>
        )}
        {activeTab === 'programme' && (
          <>
            <TextField name="maintainFileCode" />
            <Select name="maintainFileStatus" />
            <Lov name="batteryNumLov" onChange={handleChangeMaterial} />
            <TextField name="batteryModel" />
            <Select searchable name="vehicleModel" />
            <DatePicker name="faultTime" />
            <TextArea name="faultDescription" />
            <TextArea name="faultReason" />
            <TextArea name="checkScheme" />
            <TextArea name="maintainScheme" />
            <Attachment name="maintainFileUuid" />
            <DatePicker name="creationDate" />
            <Lov name="createdBylov" />
          </>
        )}
      </Form>
    </Spin>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(MaintenanceStandardEdit);
