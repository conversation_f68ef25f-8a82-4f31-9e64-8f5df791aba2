import React, { Component, Fragment } from 'react';
import { isEmpty, isUndefined } from 'lodash';
import { observer } from 'mobx-react';
import {
  DataSet,
  Modal,
  Table,
  Form,
  Button,
  TextField,
  Output,
  Lov,
  Select,
  NumberField,
  DatePicker,
  Tooltip,
} from 'choerodon-ui/pro';
import { Collapse, Divider } from 'choerodon-ui';
import FileUpload from 'alm/components/FileUpload';
import OrgPartnerLov from 'alm/components/OrgPartnerLov';
import { saveAssetTranLine } from 'alm/services/assetTransacionBasicTypeService';
import { queryOrgByEmployee } from 'alm/services/organizationService';
import { HALM_ORI } from 'alm/utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import { dateRender } from 'utils/renderer';
import { Bind } from 'lodash-decorators';
import intl from 'utils/intl';
import { handleSetExtraFieldsLine } from '../Stores/DetailDS';
import { tableDS, lovBtnDS } from '../Stores/AssetModalDS';
import './index.less';

const modalKey = Modal.key();
@observer
class InfoExhibit extends Component {
  constructor(props) {
    super(props);
    this.state = {
      collapseKeys: ['A', 'B'],
    };
    this.assetTableDS = new DataSet(tableDS());
    this.lovBtnDS = new DataSet(lovBtnDS());
  }

  /**
   * 显示资产模态框
   */
  @Bind()
  showAssetModal() {
    const { infoDS, infoTableDS } = this.props;
    const transactionTypeId = infoDS?.current?.get('transactionTypeId');
    this.assetTableDS.setQueryParameter('transactionTypeId', transactionTypeId);
    const removeAssetId = infoTableDS.map(record => record.get('objectId')).join(',');
    this.assetTableDS.setQueryParameter('removeAssetId', removeAssetId);
    this.assetTableDS.query();

    const assetModal = Modal.open({
      key: modalKey,
      style: {
        width: 650,
      },
      title: intl.get(`aatn.assetTransactionBasicType.modal.assetsOrEquipment`).d('设备/资产'),
      children: (
        <Fragment>
          <Table
            key="assetTransactionBasicTypeAsset"
            customizedCode="AORI.ASSET_TRANSACTION_BASIC_TYPE.ASSET"
            className="asset-table"
            style={{ height: 300 }}
            queryFieldsLimit={2}
            columns={this.assetColumns}
            dataSet={this.assetTableDS}
          />
        </Fragment>
      ),
      onOk: () => this.modelOk(assetModal),
      okText: intl.get('hzero.common.button.save').d('保存'),
    });
  }

  // 选择设备/资产Modal中展示的列
  @Bind()
  get assetColumns() {
    return [
      {
        name: 'assetNum',
        width: 150,
        renderer: this.columnsRender,
      },
      {
        name: 'assetDesc',
        renderer: this.columnsRender,
      },
      {
        name: 'assetStatusName',
        width: 100,
        renderer: this.columnsRender,
      },
    ];
  }

  /**
   *对不能选择的数据（行号lineNum有值并且状态是CANCELED,COMPLETED,RETURNED）进行特殊样式处理
   */
  @Bind()
  columnsRender({ value, record }) {
    const { lineNum, processStatus, transactionTypeName, changeNum } = record.toData();
    const flag = lineNum && !['CANCELED', 'COMPLETED', 'RETURNED'].includes(processStatus);
    if (flag) {
      const html =
        !transactionTypeName && !changeNum && !lineNum
          ? ''
          : `${transactionTypeName || ''}:${changeNum || ''}-${lineNum || ''}`;
      return (
        <Tooltip placement="topRight" title={html}>
          <span style={{ color: 'rgba(0, 0, 0, 0.25)' }}>{value}</span>
        </Tooltip>
      );
    }
    return value;
  }

  /**
   *资产模态框点击确定
   *
   * @memberof InfoExhibit
   */
  @Bind()
  async modelOk(assetModal) {
    const { transactionTypeId, changeHeaderId, infoTableDS, isNew, infoDS } = this.props;
    if (isNew) {
      this.handleCreateAsset(
        infoTableDS,
        this.assetTableDS.toJSONData(),
        infoDS?.current?.get('transactionTypeId')
      );
      return;
    }
    if (this.assetTableDS.selected.length > 0) {
      this.assetTableDS.transactionTypeId = transactionTypeId;
      this.assetTableDS.changeHeaderId = changeHeaderId;
      const headData = this.props.infoDS?.current.toData();
      this.assetTableDS.setState('headData', headData);
      const res = await this.assetTableDS.submit().catch(() => {
        assetModal.close();
      });
      if (res) {
        this.assetTableDS.clearCachedSelected();
        this.assetTableDS.unSelectAll();
        infoTableDS.query();
      }
    }
  }

  /**
   *编辑/详情表格行
   *
   * @memberof InfoExhibit
   */
  @Bind()
  async handleLineEdit({ record }, canEdit) {
    const { infoTableDS, isNew, editFlag, typeDS } = this.props;
    const typeDetail = typeDS.current.toData();
    const {
      statusUpdateFlag,
      targetAssetStatusScope,
      targetAssetStatusId,
      targetAssetStatusName,
    } = typeDetail;
    // 目标资产状态禁用flag
    let targetAssetStatusDisabled = false;
    // 目标资产状态是否可编辑
    if (statusUpdateFlag) {
      if (!isUndefined(targetAssetStatusId)) {
        const reg = /\[\]/;
        // 目标资产状态：有值，且“目标资产状态范围”为空时，则单据上“目标资产状态”不可编辑。默认为所选。
        if (isEmpty(targetAssetStatusScope) || reg.test(targetAssetStatusScope)) {
          targetAssetStatusDisabled = true;
        }
        if (isUndefined(record.get('newObjectStatusId'))) {
          record.set('newObjectStatusId', targetAssetStatusId);
          record.set('newAssetStatusName', targetAssetStatusName);
        }
      }
    } else {
      targetAssetStatusDisabled = true;
    }
    record.save(); // ! 第一次打开modal后修改数据点取消，修改不会被取消掉，故先保存一下以解决问题
    const lineDynamicProps = infoTableDS.lineDynamicProps || [];
    const currentRecord = lineDynamicProps.find(i => i.lineId === record.get('lineId')) || {};
    const { dynamicFields = [] } = currentRecord;

    const lineModal = Modal.open({
      drawer: true,
      width: 600,
      title: intl.get('aatn.assetTransactionBasicType.modal.transactionLines').d('事务处理行'),
      children: (
        <Fragment>
          {canEdit
            ? this.renderEditDrawer({
                record,
                dynamicFields,
                targetAssetStatusDisabled,
              })
            : this.renderViewDrawer({
                record,
                dynamicFields,
                targetAssetStatusDisabled,
              })}
        </Fragment>
      ),
      footer: (okBtn, cancelBtn) =>
        isNew || editFlag ? (
          <div>
            {okBtn}
            {cancelBtn}
          </div>
        ) : (
          <Button onClick={() => lineModal.close()}>
            {intl.get('hzero.common.button.close').d('关闭')}
          </Button>
        ),
      onOk: () => this.handleLineModalOk(record),
      onCancel: () => this.handleModalCancel(record),
    });
  }

  @Bind
  renderEditDrawer({ record, targetAssetStatusDisabled, dynamicFields }) {
    const { infoDS, basicCode } = this.props;
    const { formDisplayRender = [] } = dynamicFields;
    const receiptFlag = this.props?.basicCode === 'ACQUISITION';
    const objectType = record.get('objectType');
    return (
      <Form record={record}>
        <Output name="objectNum" />
        <Output name="objectDesc" />
        {basicCode === 'DISPOSAL' && (
          <Select
            name="disposalMethod"
            optionsFilter={i => {
              const disposalMethodsScopeList =
                infoDS?.current?.get('disposalMethodsScopeList') || [];
              return disposalMethodsScopeList.findIndex(e => i.get('value') === e) !== -1;
            }}
          />
        )}
        {basicCode === 'DISPOSAL' && <Select name="typeOfExpense" />}
        {basicCode === 'DISPOSAL' && <NumberField name="disposalMoney" precision={2} />}
        {receiptFlag && <Output name="objectType" />}
        {/* 资产类型数量不允许修改 */}
        {receiptFlag &&
          (objectType === 'ASSET' ? (
            <Output name="requisitionsNumber" />
          ) : (
            <NumberField name="requisitionsNumber" min={1} />
          ))}
        {receiptFlag && <DatePicker name="demandDate" renderer={({ text }) => dateRender(text)} />}
        {!targetAssetStatusDisabled && <Lov name="oldAssetStatus" disabled />}
        {!targetAssetStatusDisabled && (
          <Lov name="newAssetStatus" disabled={targetAssetStatusDisabled} />
        )}
        {formDisplayRender.map(i => i)}
      </Form>
    );
  }

  @Bind
  renderViewDrawer({ record, dynamicFields, targetAssetStatusDisabled }) {
    const { basicCode } = this.props;
    const { outputDisplayRender = [] } = dynamicFields;
    const receiptFlag = this.props?.basicCode === 'ACQUISITION';
    return (
      <Form record={record}>
        <Output name="objectNum" />
        <Output name="objectDesc" />
        {basicCode === 'DISPOSAL' && <Output name="disposalMethod" />}
        {basicCode === 'DISPOSAL' && <Output name="typeOfExpense" />}
        {basicCode === 'DISPOSAL' && <Output name="disposalMoney" />}
        {basicCode === 'DISPOSAL' && <Output name="currencyCodeMeaning" />}
        {receiptFlag && (
          <>
            <Output name="objectType" />
            <Output name="requisitionsNumber" />
            <Output name="demandDate" renderer={({ text }) => dateRender(text)} />
          </>
        )}
        {!targetAssetStatusDisabled && <Output name="oldAssetStatus" />}
        {!targetAssetStatusDisabled && <Output name="newAssetStatus" />}
        {outputDisplayRender.map(i => i)}
      </Form>
    );
  }

  /**
   * 事务行模态框确定按钮
   */
  @Bind()
  async handleLineModalOk(record) {
    const flag = await record.validate(true);
    if (flag) {
      record.save();
    } else {
      return false;
    }
  }

  /**
   * 新建事务单时创建行资产
   */
  @Bind()
  async handleCreateAsset(lineDs, records, transactionTypeId, extraConfig = {}) {
    const fetchAssetInfo = {
      countingLine: async () => {
        const countingUrl = `${HALM_ORI}/v1/${this.props.organizationId}/tp-change-lines/auto-value/unsaved-line`;
        const res = await request(countingUrl, {
          method: 'POST',
          data: extraConfig?.assetConfig,
        });
        return res;
      },
      default: async () => {
        const assetInfoUrl = `${HALM_ORI}/v1/${this.props.organizationId}/tp-change-lines/unsaved-line`;
        const res = await request(assetInfoUrl, {
          method: 'POST',
          query: {
            transactionTypeId,
          },
          data: records.map(record => record.assetId),
        });
        return res;
      },
    };
    // eslint-disable-next-line no-param-reassign
    lineDs.status = 'loading';
    const assetInfo = await (
      fetchAssetInfo?.[extraConfig?.moduleSource] ?? fetchAssetInfo.default
    )().catch(() => {
      // eslint-disable-next-line no-param-reassign
      lineDs.status = 'ready';
    });

    // eslint-disable-next-line no-param-reassign
    lineDs.status = 'ready';

    if (assetInfo.failed) {
      notification.error({
        message: assetInfo.message,
      });
      return;
    }
    if (assetInfo?.length) {
      this.assetTableDS.clearCachedSelected();
      this.assetTableDS.unSelectAll();
      records.forEach((record, index) => {
        const lineData = {
          ...record,
          ...assetInfo[index],
        };
        lineDs.create(lineData);
      });
      handleSetExtraFieldsLine(lineDs, true);
      lineDs.forEach((record, index) => {
        record.init('lineNum', index + 1);
      });
    }
  }

  /**
   * 取消模态框
   */
  @Bind()
  handleModalCancel(record) {
    record.restore();
  }

  /**
   *删除表格行
   *
   * @param {*} record
   */
  @Bind()
  handleDelete({ record }) {
    const { changeHeaderId, infoTableDS, infoDS } = this.props;
    // 出入库带出设备为临时数据，未保存
    if (record?.get('lineId')) {
      const headData = infoDS?.current?.toData();
      infoTableDS.setState('headData', headData);
      infoTableDS.setState('changeHeaderId', changeHeaderId);
      infoTableDS.delete(record);
    } else {
      infoTableDS.remove(record, true);
    }
  }

  // 资产事务行Table展示的列
  get columns() {
    const {
      pageReadonlyFlag = false,
      isNew,
      editFlag,
      infoDS,
      pageConfig,
      processStatus,
      basicCode,
    } = this.props;
    const promptCode = 'aatn.assetTransactionBasicType.model.assetTransactionBasicType';
    const tsTypeId = infoDS.current.toData().transactionTypeId;
    const id = infoDS.current.toData().changeHeaderId;
    const assetFileUploadAttribute = {
      data: [
        {
          name: intl.get(`${promptCode}.sourceType`).d('来源类型'),
          code: 'sourceType', // code必须与title中code保持一致
          value: '资产事务处理单',
        },
        {
          name: intl.get(`${promptCode}.sourceNumber`).d('来源单据号'),
          code: 'sourceNumber',
          value: infoDS.current.toData().changeNum,
          pathname: `/aatn/asset-transaction-basic-type/${pageConfig.routeType}/detail/${tsTypeId}/${id}`,
        },
      ],
    };

    const receiptFlag = basicCode === 'ACQUISITION';
    const cols = [
      { name: 'lineNum', width: 60 },
      {
        name: 'objectNum',
        width: 120,
      },
      {
        name: 'objectDesc',
        width: 200,
      },
      {
        name: 'description',
        tooltip: 'none',
        renderer: ({ value }) => {
          const html = <div dangerouslySetInnerHTML={{ __html: value }} />;
          return (
            <Tooltip placement="topLeft" title={html}>
              {html}
            </Tooltip>
          );
        },
      },
      { name: 'processStatus', width: 100 },
      {
        header: intl.get('hzero.common.button.action').d('操作'),
        // width: 150,
        width: isNew || editFlag ? 120 : 80,
        align: 'left',
        lock: 'right',
        renderer: rowDS => {
          const btns = [];
          if (rowDS.record.get('processStatus') === 'DRAFT' && (isNew || editFlag)) {
            btns.push(
              <a onClick={() => this.handleLineEdit(rowDS, true)}>
                {intl.get('hzero.common.button.edit').d('编辑')}
              </a>
            );
            btns.push(<Divider type="vertical" />);
            btns.push(
              <a style={{ color: editFlag && '#e25c6e' }} onClick={() => this.handleDelete(rowDS)}>
                {intl.get('hzero.common.button.delete').d('删除')}
              </a>
            );
          } else {
            btns.push(
              <a onClick={() => this.handleLineEdit(rowDS, false)}>
                {intl.get('hzero.common.button.detail').d('详情')}
              </a>
            );
          }
          return [<span>{btns}</span>];
        },
      },
      {
        header: intl.get(`${promptCode}.asset-attachment-management`).d('附件管理'),
        width: 100,
        align: 'left',
        lock: 'right',
        renderer: ({ record }) => {
          return isNew ? (
            ''
          ) : (
            <FileUpload
              uploadButtonName={{ code: 'asset-attachment-management', name: '' }}
              moduleName="aatn-asset-status-change-line"
              moduleId={record.data.lineId}
              attribute={assetFileUploadAttribute}
              showDeleteFlag={processStatus === 'DRAFT'}
            />
          );
        },
      },
    ];
    // 作为审批表单显示时不显示操作列
    if (pageReadonlyFlag) {
      cols.splice(-2, 1);
    }
    // 资产领用的时候新增领用数量
    if (receiptFlag) {
      cols.splice(
        3,
        0,
        { name: 'objectType', width: 100 },
        { name: 'requisitionsNumber', width: 100 },
        { name: 'demandDate', width: 150 }
      );
    }
    return cols;
  }

  @Bind()
  handleChangePrincipal(record) {
    if (record) {
      queryOrgByEmployee({
        tenantId: this.props.organizationId,
        employeeId: record?.employeeId,
      }).then(orgInfo => {
        this.props?.infoDS?.current?.set({
          usingOrgId: orgInfo?.unitId,
          usingOrgName: orgInfo?.unitName,
          usingOrgType: orgInfo?.orgType,
        });
        this.handleChangeUsingOrg(record, orgInfo?.orgType)
        this.props?.infoDS?.current?.set({
          unitCompanyName: record?.unitCompanyName,
          unitCompanyId: record?.unitCompanyId,
        });
      });
    }
  }

  @Bind()
  handleChangeUsingOrg(record, type) {
    const { infoDS } = this.props;
    infoDS.current.set('usingOrgId', type === 'PLATFORM' ? record.unitId : record.orgId);
    infoDS.current.set('usingOrgName', type === 'PLATFORM' ? record.unitName : record.orgName);
    infoDS.current.set('usingOrgType', type);
  }

  editFormRender() {
    const { infoDS, basicCode, headerDynamicProps } = this.props;
    const { formDisplayRender = [] } = headerDynamicProps;
    return (
      <Form dataSet={infoDS} labelWidth={120} columns={3}>
        <TextField name="changeNum" restrict="a-zA-Z0-9" />
        <TextField name="titleOverview" />
        <Lov name="transactionTypeLov" />
        <Select name="processStatus" />
        {basicCode === 'DISPOSAL' && (
          <Select
            name="disposalMethod"
            optionsFilter={i => {
              const disposalMethodsScopeList =
                infoDS?.current?.get('disposalMethodsScopeList') || [];
              return disposalMethodsScopeList.findIndex(e => i.get('value') === e) !== -1;
            }}
          />
        )}
        <Lov name="principalPersonLov" onChange={this.handleChangePrincipal} />
        <OrgPartnerLov required name="usingOrgName" handleOk={this.handleChangeUsingOrg} />,
        {basicCode === 'DISPOSAL' && <Select name="typeOfExpense" />}
        {basicCode === 'DISPOSAL' && <NumberField name="disposalMoney" precision={2} />}
        {basicCode === 'DISPOSAL' && <Lov name="currencyLov" />}
        {basicCode === 'IDLE' && [
          <Lov name="applyPersonLov" />,
          <DatePicker name="idleDate" />,
          <Select name="idleReason" />,
        ]}
        {basicCode === 'SCRAP' && [<TextField name="scrapReason" />]}
        <Lov name='applicantCompany'/>
        {formDisplayRender.map(i => i)}
      </Form>
    );
  }

  viewFormRender() {
    const { infoDS, basicCode, headerDynamicProps } = this.props;
    const { outputDisplayRender = [] } = headerDynamicProps;
    return (
      <Form dataSet={infoDS} labelWidth={120} columns={3}>
        {basicCode !== 'DISPOSAL' && <Output name="changeNum" />}
        {basicCode !== 'DISPOSAL' && <Output name="titleOverview" />}
        {basicCode !== 'DISPOSAL' && <Output name="transactionTypeIdMeaning" />}
        {basicCode !== 'DISPOSAL' && <Output name="processStatusMeaning" />}
        {basicCode === 'DISPOSAL' && <Output name="disposalMethod" />}
        <Output name="principalPersonLov" />
        <Output name="usingOrgName" />,{basicCode === 'DISPOSAL' && <Output name="typeOfExpense" />}
        {basicCode === 'DISPOSAL' && <Output name="disposalMoney" />}
        {basicCode === 'DISPOSAL' && <Output name="currencyCodeMeaning" />}
        {basicCode === 'IDLE' && [
          <Output name="applyPersonLov" />,
          <Output name="idleDate" />,
          <Output name="idleReason" />,
        ]}
        {basicCode === 'SCRAP' && [<Output name="scrapReason" />]}
        <Output name='applicantCompany'/>
        {outputDisplayRender.map(i => i)}
      </Form>
    );
  }

  @Bind()
  async chooseItem(records) {
    await this.handleAddLine('ITEM', records);
    this.lovBtnDS.reset();
  }

  @Bind()
  async chooseStandardAsset(records) {
    await this.handleAddLine('STANDARD_ASSET', records);
    this.lovBtnDS.reset();
  }

  @Bind()
  async handleAddLine(objectType, records) {
    const { transactionTypeId, changeHeaderId, organizationId, infoTableDS } = this.props;
    let id = null;
    let num = null;
    let desc = null;
    if (objectType === 'ITEM') {
      id = 'itemId';
      num = 'itemNum';
      desc = 'itemDesc';
    } else if (objectType === 'STANDARD_ASSET') {
      id = 'standardAssetId';
      num = 'assetNum';
      desc = 'assetDesc';
    }

    const data = records.map(i => ({
      objectId: i[id],
      objectNum: i[num],
      objectDesc: i[desc],
      objectType,
      requisitionsNumber: 1,
      transactionTypeId,
      changeHeaderId,
      tenantId: organizationId,
    }));
    const tpChangeLines = {
      tenantId: organizationId,
      data,
    };
    const headData = this.props.infoDS?.current.toData() ?? {};
    const params = { ...headData, tpChangeLines };
    await saveAssetTranLine(params);
    infoTableDS.query();
  }

  render() {
    const { collapseKeys } = this.state;
    const { isNew, editFlag, infoTableDS, basicCode, recipientsScopeList, typeDS } = this.props;
    // const displayFlag = isNew ? { display: 'none' } : { display: 'block' };
    const displayAssetFlag =
      !editFlag || !recipientsScopeList.includes('ASSET')
        ? { display: 'none' }
        : { display: 'block' };

    const displayStandardAsset =
      basicCode === 'ACQUISITION' && editFlag && recipientsScopeList.includes('STANDARD_ASSET');
    const displayItem =
      basicCode === 'ACQUISITION' && editFlag && recipientsScopeList.includes('ITEM');

    const itemCategoryScope = typeDS?.current?.get('itemCategoryScope');
    const toolingScope = typeDS?.current?.get('toolingScope');
    this.lovBtnDS.setState('itemCategoryScope', itemCategoryScope);
    this.lovBtnDS.setState('toolingScope', toolingScope);

    return (
      <React.Fragment>
        <Collapse bordered={false} defaultActiveKey={collapseKeys} className="form-collapse">
          <Collapse.Panel
            key="A"
            header={intl.get(`aatn.assetTransactionBasicType.panel.baseInfo`).d('基本信息')}
          >
            {isNew || editFlag ? this.editFormRender() : this.viewFormRender()}
          </Collapse.Panel>
          <Collapse.Panel
            key="B"
            header={intl
              .get(`aatn.assetTransactionBasicType.panel.transactionLines`)
              .d('事务处理行信息')}
          >
            <div style={{ display: 'flex', marginTop: 10 }}>
              <Button
                color="primary"
                icon="add"
                style={displayAssetFlag}
                onClick={this.showAssetModal}
                disabled={infoTableDS.length >= 1}
              >
                {intl.get(`aatn.assetTransactionBasicType.button.multiAdd`).d('选择资产')}
              </Button>
              {displayStandardAsset && (
                <Lov
                  noCache
                  color="primary"
                  style={displayAssetFlag}
                  dataSet={this.lovBtnDS}
                  name="standardAssetLov"
                  mode="button"
                  autoSelectSingle={false}
                  clearButton={false}
                  icon="add"
                  onChange={this.chooseStandardAsset}
                >
                  {intl
                    .get(`aatn.assetTransactionBasicType.button.chooseStandardAsset`)
                    .d('选择标准资产')}
                </Lov>
              )}
              {displayItem && (
                <Lov
                  color="primary"
                  noCache
                  dataSet={this.lovBtnDS}
                  autoSelectSingle={false}
                  name="itemLov"
                  mode="button"
                  clearButton={false}
                  icon="add"
                  onChange={this.chooseItem}
                >
                  {intl.get(`aatn.assetTransactionBasicType.button.chooseItem`).d('选择物料')}
                </Lov>
              )}
            </div>

            <Table
              key="assetTransactionBasicTypeChoose"
              customizedCode="AORI.ASSET_TRANSACTION_BASIC_TYPE.CHOOSE"
              style={{ marginTop: 10 }}
              dataSet={infoTableDS}
              columns={this.columns}
            />
          </Collapse.Panel>
        </Collapse>
      </React.Fragment>
    );
  }
}
export default InfoExhibit;
