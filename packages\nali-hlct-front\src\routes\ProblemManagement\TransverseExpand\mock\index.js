export default {
  rows: {
    problemSpreadListId: 12001,
    spreadListCode: '9001HZ230703012',
    status: 'RELEASED',
    statusDesc: '待发布',
    createMethodDesc: '问题管理',
    createMethod: 'PROBLEM',
    sourceProblem: 'sourceProblem',
    siteId: 62741001,
    siteName: '广州二厂',
    prodLineId: 7001,
    prodLineName: 'ZXL_P2',
    registUserId: 2,
    registUserName: '超级管理员',
    registUserDapartmentName: 'RegistUserDapartment',
    registDate: '2023-07-03 16:30:23',
    leadUserId: 2,
    leadUserName: '超级管理员',
    leadUserDapartmentName: 'LeadUserDapartment',
    followUpUserId: 2,
    followUpUserName: '超级管理员',
    followUpUserDapartmentName: 'FollowUpUserDapartment',
    spreadListTypeDesc: '工艺',
    spreadListType: 'OPERATION',
    spreadListContent: 'lalala1',
    implementationStandard: '哈哈',
    operationDesc: '',
    operationList: null,
    enclosure: '',
    managerId: '2',
    lineTaskList: [
      {
        problemSpreadTaskId: 5001,
        problemSpreadListId: 12001,
        sequence: 10,
        siteId: 62741001,
        siteName: '广州二厂',
        prodLineId: 36001,
        prodLineName: '加工线2',
        processId: [62865001, 62864001],
        processDesc: ['DCR测试-PACK段', 'EOL测试-PACK段'],
        equipmentId: [7, 9],
        equipmentDesc: ['ZXL_EQ7', 'ZXL_EQ9'],
        responsibilityUserId: 2,
        responsibilityUserName: '超级管理员',
        planStartDate: '2023-07-05 00:00:00',
        planEndDate: '2023-07-20 00:00:00',
        actualEndTime: '2023-07-14 00:00:00',
        status: 'AUDIT_REJECT',
        statusDesc: '新建',
        evidence: 'e3c4625e-9b1b-4f3f-b0b5-c2d3d633e8ac',
        enclosure: '4b69bae4-38f2-4f50-ba64-92c5497b096f',
        result: '',
        resultDesc: null,
        reason: '',
        remindFlag: '',
      },
    ],
  },
  success: true,
};
