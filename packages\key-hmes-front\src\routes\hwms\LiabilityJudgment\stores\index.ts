/**
 * @Description: 一次解析责任判定-主界面DS
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.LiabilityJudgment';


const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'prianalResjudgId',
  queryFields: [
    {
      name: 'prianalResjudgCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prianalResjudgCode`).d('一次解析单编号'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'feedbackNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.feedbackNum`).d('质量反馈单编号'),
    },
    {
      name: 'batteryMatLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.batteryMatLotCode`).d('电池包编码'),
      lovCode: 'YP_MES.MES.MATERIAL_LOT',
      lovPara: { tenantId },
      textField: "materialLotCode",
      ignore: FieldIgnore.always,
    },
    {
      name: 'batteryMatLotId',
      bind: 'batteryMatLotLov.materialLotId',
    },
    {
      name: 'batteryModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryModel`).d('电池包型号'),
    },

    {
      name: 'qualityProblemFlay',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityProblemFlay`).d('是否质量问题'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'analResDivision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analResDivision`).d('分析责任划分'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MARKET_PROBLEM_TYPE',
    },
    {
      name: 'accidentalTag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accidentalTag`).d('偶发/必现'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.ACCIDENTAL_TAG',
    },
    {
      name: 'frequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.frequency`).d('新发/再发'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_FREQUENCY',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('责任部门'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        specDept: 'N',
      },
    },
    {
      name: 'responsibleDeptId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsibleUserObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncRecord.responsibleUserId`).d('责任人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      textField: 'realName',
    },
    {
      name: 'responsibleEmId',
      bind: 'responsibleUserObj.id',
    },
    {
      name: 'reasonClassify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonClassify`).d('原因分类'),
      lookupCode: 'YP.QIS.REASON',
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
    },
    {
      name: 'reasonCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonCode`).d('原因编码'),
    },
    {
      name: 'bResponsibilityRatio',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bResponsibilityRatio`).d('乙方责任'),
    },
    {
      name: 'aResponsibilityRatio',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.aResponsibilityRatio`).d('甲方责任'),
    },
    {
      name: 'creationDateFrom',
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      type: FieldType.dateTime,
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'lastUpdateDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDateFrom`).d('最后更新时间从'),
      max: 'lastUpdateDateTo',
    },
    {
      name: 'lastUpdateDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDateTo`).d('最后更新时间至'),
      min: 'lastUpdateDateFrom',
    },
  ],
  fields: [
    {
      name: 'prianalResjudgCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prianalResjudgCode`).d('一次解析单编号'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'feedbackNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.feedbackNum`).d('质量反馈单编号'),
    },
    {
      name: 'theme',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.theme`).d('主题'),
    },
    {
      name: 'batteryMatLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryMatLotCode`).d('电池包编码'),
    },
    {
      name: 'batteryMatLotModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryMatLotModel`).d('电池包型号'),
    },
    {
      name: 'qualityProblemFlay',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityProblemFlay`).d('是否质量问题'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'severityLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.severityLevel`).d('严重程度'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_SEVERITY_LEVEL',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'analResDivision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analResDivision`).d('分析责任划分'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.MARKET_PROBLEM_TYPE',
    },
    {
      name: 'accidentalTag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accidentalTag`).d('偶发/必现'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.ACCIDENTAL_TAG',
    },
    {
      name: 'frequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.frequency`).d('新发/再发'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROBLEM_FREQUENCY',
    },
    {
      name: 'responsibleDeptName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleDeptName`).d('责任部门'),
    },
    {
      name: 'responsibleEmName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleEmName`).d('责任人'),
    },
    {
      name: 'reasonClassify',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonClassify`).d('原因分类'),
      lookupCode: 'YP.QIS.REASON',
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
    },
    {
      name: 'reasonCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reasonCode`).d('原因编码'),
    },
    {
      name: 'bResponsibilityRatio',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bResponsibilityRatio`).d('乙方责任'),
    },
    {
      name: 'aResponsibilityRatio',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.aResponsibilityRatio`).d('甲方责任'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
  transport: {
    read: ({data}) => {
      const _data = {
        ...data,
        start: data.proposeTimePeriod?.start,
        end: data.proposeTimePeriod?.end,
      };
      delete _data.proposeTimePeriod
      // const keyList = [
      //   'planEndTimeFrom',
      //   'planEndTimeTo',
      // ];
      // keyList.forEach(key => {
      //   if (_data[key]) {
      //     _data[key] = moment(_data[key]).format('YYYY-MM-DD');
      //   }
      // });
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prianal-resjudg/header/ui`,
        method: 'GET',
        data: _data,
      };
    },
  },
});


const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  // @ts-ignore
  selection: 'single',
  cacheSelection: false,
  dataKey: 'rows',
  primaryKey: 'prianalResjudgId',
  fields: [
    {
      name: 'prianalResjudgItemNum',
      label: intl.get(`${modelPrompt}.prianalResjudgItemNum`).d('序号'),
      type: FieldType.number,
    },
    {
      name: 'partsName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partsName`).d('客户零件号'),
    },
    {
      name: 'partsCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partsCode`).d('客户零件名称'),
    },
    {
      name: 'ypPartsCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ypPartsCode`).d('物料编码'),
    },
    {
      name: 'ypPartsName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ypPartsName`).d('物料名称'),
    },
    {
      name: 'primaryUnitFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUnitFlag`).d('主因件'),
      lookupCode: 'YP.QIS.YN_FLAG',
    },
    {
      name: 'majorFaultMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorFaultMode`).d('主故障模式'),
      lookupCode: 'YP.QIS.MAJOR_FAULT_MODE',
    },
    {
      name: 'majorDivision1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision1`).d('主要区分1'),
      lookupCode: 'YP.QIS.MAJOR_DIVISION1',
    },
    {
      name: 'majorDivision2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision2`).d('主要区分2'),
      lookupCode: 'YP.QIS.MAJOR_DIVISION2',
    },
    {
      name: 'majorDivision3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.majorDivision3`).d('主要区分3'),
      lookupCode: 'YP.QIS.MAJOR_DIVISION3',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('具体描述'),
    },
    {
      name: 'ware',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ware`).d('软件/硬件'),
      lookupCode: 'YP.QIS.WARE',
    },
    {
      name: 'softwareVersion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.softwareVersion`).d('软件版本'),
    },
    {
      name: 'problemCreateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCreateFlag`).d('是否触发问题管理'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('问题管理编号'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prianal-resjudg/line/ui`,
        method: 'GET',
      };
    },
  },
});

export { headDS, lineDS };
