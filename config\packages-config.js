/**
 * 打包的所有服务，可按需使用
 */

const packages = [
  // -- APPEND SUB MODULE ITEMS HERE --
  // 规则引擎服务
  { name: 'hzero-front-hres' },
  // mes相关服务
  { name: 'hcm-mes-front' },
  { name: 'hcm-message-front' },
  { name: 'hcm-method-front' },
  { name: 'hcm-common-front' },
  { name: 'hcm-model-front' },
  { name: 'hcm-api-front' },
  { name: 'hcm-report-front' },
  { name: 'hcm-sampling-front'},
  { name: 'hcm-hlct-front' },
  { name: 'hspc-front' },
  { name: 'hcm-hohr-front' }, // 人员技能模块从平台迁移到产品里了
  // aps相关服务
  { name: 'hcmp-front' },
  { name: 'hcmp-purchase' },
  { name: 'hcmp-plan' },
  { name: 'hcmp-front-common' },
  // 平台相关服务
  { name: 'jipaas-front-jfnt' },
  { name: 'hzero-front-hevt' },
  { name: 'hzero-front-hiam' },
  { name: 'hzero-front-hpfm' },
  { name: 'hzero-front-hmsg' },
  { name: 'hzero-front-himp' },
  { name: 'hzero-front-hsdr' },
  { name: 'hzero-front-hadm' },
  { name: 'hzero-front-hfile' },
  { name: 'hzero-front-hrpt' },
  { name: 'hzero-front-hprt' },
  { name: 'hzero-front-hitf' },
  { name: 'hzero-front-cusz' },
  { name: 'hippius-front' },
  { name: 'hippius-front-analyse' },
  { name: 'hippius-front-app' },
  { name: 'hippius-front-contact' },
  { name: 'hippius-front-msggroup' },
  { name: 'hippius-front-subapp' },
  { name: 'hippius-front-qnr' },
  { name: 'hippius-front-problem' },
  { name: 'hzero-front-hwkf' },
  // 其它产品
  { name: "iiot-front-ipfm" },
  { name: "kb-front" },
  // 本地模块
  { name: "key-halm-front" },
  { name: 'key-hmes-front' },
  { name: 'key-hcmp-front' },
];

module.exports = packages;
