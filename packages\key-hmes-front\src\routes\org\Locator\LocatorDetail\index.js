/**
 * @Description: 新库位维护-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-03-05 14:14:48
 * @LastEditTime: 2023-05-18 11:36:08
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useRef, useMemo } from 'react';
import { Button, DataSet } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { Popconfirm } from 'hzero-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Header, Content } from 'components/Page';
import { BASIC } from '@utils/config';
import { AttributeDrawer } from '@components/tarzan-ui';
import { BasicsAtttribtesDS } from '../stores/LocatorDetailDS';
import Detail from '../components/Detail';

// const TABLENAME = 'mt_mod_locator_attr';
const modelPrompt = 'tarzan.model.org.locator';

const LocatorDetail = props => {
  const {
    match,
    custConfig,
  } = props;
  const {
    path,
    params: { locatorId },
  } = match;
  const [canEdit, setCanEdit] = useState(locatorId === 'create');
  const [confirm, setConfirm] = useState(false);
  const childRef = useRef();

  const detailDS = useMemo(
    () =>
      new DataSet({
        ...BasicsAtttribtesDS(),
      }),
    [],
  );

  const handleSave = async () => {
    const { success, newKid } = await childRef.current.submit();
    if (success) {
      setCanEdit(prev => !prev);
      props.history.push(`/hmes/organization-modeling/locator/detail/${newKid}`);
    }
  };

  const handleCancel = () => {
    if (locatorId === 'create') {
      props.history.push('/hmes/organization-modeling/locator/list');
    } else {
      childRef.current.reset();
      setCanEdit(prev => !prev);
    }
  };

  const handleValue = (newId, oldId) => {
    if (newId !== oldId) {
      setConfirm(true);
    } else {
      setConfirm(false);
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.locationMaintenance`).d('库位维护')}
        backPath="/hmes/organization-modeling/locator/list"
      >
        {canEdit && (
          <>
            {!confirm || locatorId === 'create' ? (
              <PermissionButton
                type="c7n-pro"
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </PermissionButton>
            ) : (
              <Popconfirm
                title={intl
                  .get(`${modelPrompt}.saveConfirm`)
                  .d(
                    '区域库位坐标系修改将导致其下层库存库位与地点库位坐标值数据清空，确认修改吗？',
                  )}
                onConfirm={handleSave}
                arrowPointAtCenter
                placement="left"
              >
                <PermissionButton
                  type="c7n-pro"
                  permissionList={[
                    {
                      code: `${path}.button.edit`,
                      type: 'button',
                      meaning: '详情页-编辑新建删除复制按钮',
                    },
                  ]}
                  color={ButtonColor.primary}
                  icon="save"
                >
                  {intl.get('tarzan.common.button.save').d('保存')}
                </PermissionButton>
              </Popconfirm>
            )}
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <AttributeDrawer
          // tablename={TABLENAME}
          className="org.tarzan.model.domain.entity.MtModLocator"
          kid={locatorId}
          canEdit={canEdit}
          disabled={locatorId === 'create'}
          serverCode={BASIC.TARZAN_MODEL}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_DETAIL.BUTTON`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <Detail
          canEdit={canEdit}
          ref={childRef}
          kid={locatorId}
          columns={3}
          detailDS={detailDS}
          handleValue={handleValue}
          componentType="LOACTOR"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.model.org.locator', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_DETAIL.BUTTON`],
})(LocatorDetail));
