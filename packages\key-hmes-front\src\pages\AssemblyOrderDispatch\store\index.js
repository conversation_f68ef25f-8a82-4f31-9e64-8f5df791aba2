import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';

import { BASIC } from '@utils/config';
import intl from 'utils/intl';
import moment from 'moment';
import { isNumber } from 'lodash';

// const Host = '/ty-mes-39945';
const yongjun = '';
// const yongjun = '-30711'

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'fydstms.field.view.table.fields';

const DispatchedOrderDs = () => ({
  paging: false,
  primaryKey: 'instructionDocId',
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'customerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户描述'),
    },
    {
      name: 'customerNameAlt',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerNameAlt`).d('客户简称'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'taskProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskProcess`).d('任务进度'),
    },
    {
      name: 'dto5',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dto5`).d('异常'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('订单备注'),
    },
    {
      name: 'taskRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskRemark`).d('任务备注'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'woHistory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woHistory`).d('工单历史'),
    },
    {
      name: 'completedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completedQty`).d('完成数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'actualStartDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualStartDate`).d('实绩开始时间'),
    },
    {
      name: 'actualEndDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualEndDate`).d('实绩结束时间'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('工单状态'),
    },
  ],
  // data:[
  //   {docNum: 213214213431243, docStatus: 'NEW'},
  //   {docNum: 213214},
  //   {docNum: 213214},
  // ]
});

const WillDispatchOrderDs = () => ({
  primaryKey: 'instructionDocId',
  paging: false,
  expandField: 'expand',
  fields: [
    {
      name: 'dto4',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.dto4`).d('工单准备状态'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'customerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户描述'),
    },
    {
      name: 'customerNameAlt',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerNameAlt`).d('客户简称'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('订单备注'),
    },
    {
      name: 'taskRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskRemark`).d('任务备注'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'woHistory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woHistory`).d('工单历史'),
    },
    {
      name: 'releasedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.releasedQty`).d('下达数量'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('工单状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-production-inputs/component/info/ui`,
        method: 'POST',
      };
    },
  },
});
const queryDs = () => ({
  primaryKey: 'instructionDocId',
  paging: false,
  selection: false,
  autoQuery: false,
  queryFields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.siteId`).d('产线'),
      lovCode: 'HME.PERMISSION_PROD_LINE',
      noCache: true,
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      type: FieldType.string,
      bind: 'site.prodLineId',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialId`).d('物料'),
      lovCode: 'HME.PERMISSION_MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderNum`).d('WO编码'),
    },
    {
      name: 'statusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('状态'),
      textField: 'meaning',
      valueField: 'value',
      noCache: true,
      multiple: true,
      lookupCode: "HME.WO_DISPATCH_STATUS",
      // lovPara: { tenantId },
      defaultValue: ['EORELEASED', 'RELEASED'],
      // lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=WO_STATUS&type=workOrderStatusOptions`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     if (data instanceof Array) {
      //       return data;
      //     }
      //     const { rows } = JSON.parse(data);
      //     return rows;
      //   },
      // },
    },
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: intl
        .get(`${modelPrompt}.model.productionOrderMgt.planStartTime`)
        .d('计划开始时间'),
      max: 'planEndTime',
      defaultValue: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      required: true,
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl
        .get(`${modelPrompt}.model.productionOrderMgt.planEndTime`)
        .d('计划结束时间'),
      min: 'planStartTime',
      required: true,
      defaultValue:  moment(new Date).add(7,'d').format('YYYY-MM-DD 23:59:59'),
    },
    {
      name: 'customer',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.customerId`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'customerId',
      type: FieldType.string,
      bind: 'customer.customerId',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-assembly-dispatch/wo/list/ui`,
        method: 'POST',
      };
    },
  },
});
const orderInfoDs = () => ({
  primaryKey: 'instructionDocId',
  fields: [
    {
      name: 'numberSpan',
      type: FieldType.string,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('woId');
        },
      },
    },
    {
      name: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      type: FieldType.string,
    },
    {
      name: 'workOrderType',
      label: intl.get(`${modelPrompt}.workOrderType`).d('工单类型'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      type: FieldType.string,
    },
    {
      name: 'bomName',
      label: intl.get(`${modelPrompt}.bomName`).d('装配清单'),
      type: FieldType.string,
    },
    {
      name: 'prodLine',
      label: intl.get(`${modelPrompt}.prodLine`).d('生产线'),
      type: FieldType.string,
    },
    {
      name: 'customer',
      label: intl.get(`${modelPrompt}.customer`).d('客户'),
      type: FieldType.string,
    },
    {
      name: 'planStartTime',
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
      type: FieldType.string,
    },
    {
      name: 'planEndTime',
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
      type: FieldType.string,
    },
    {
      name: 'routerName',
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线'),
      type: FieldType.string,
    },
    {
      name: 'qty',
      label: intl.get(`${modelPrompt}.qty`).d('计划数量'),
      type: FieldType.string,
    },
    {
      name: 'completeSetQty',
      label: intl.get(`${modelPrompt}.completeSetQty`).d('齐套数量'),
      type: FieldType.string,
    },
    {
      name: 'scrappedQty',
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
      type: FieldType.string,
    },
    {
      name: 'completedQty',
      label: intl.get(`${modelPrompt}.completedQty`).d('完工数量'),
      type: FieldType.string,
    },
  ],
  // data:[
  //   {docNum: 213214213431243, docStatus: 'NEW'},
  //   {docNum: 213214},
  //   {docNum: 213214},
  // ]
});
const historyDs = () => ({
  primaryKey: 'eventId',
  selection: false,
  fields: [
    {
      name: 'eventType',
      label: intl.get(`${modelPrompt}.eventType`).d('事件类型'),
      type: FieldType.string,
    },
    {
      name: 'eventTime',
      label: intl.get(`${modelPrompt}.eventTime`).d('事件时间'),
      type: FieldType.string,
    },
    {
      name: 'eventBy',
      label: intl.get(`${modelPrompt}.eventBy`).d('事件人'),
      type: FieldType.string,
    },
    {
      name: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      type: FieldType.string,
    },
    {
      name: 'workOrderType',
      label: intl.get(`${modelPrompt}.workOrderType`).d('工单类型'),
      type: FieldType.string,
    },
    {
      name: 'siteCode',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      type: FieldType.string,
    },
    {
      name: 'siteName',
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
      type: FieldType.string,
    },
    {
      name: 'prodLineCode',
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
      type: FieldType.string,
    },
    {
      name: 'prodLineName',
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线描述'),
      type: FieldType.string,
    },
    {
      name: 'makeOrderNum',
      label: intl.get(`${modelPrompt}.makeOrderNum`).d('制造订单编码'),
      type: FieldType.string,
    },
    {
      name: 'productionVersion',
      label: intl.get(`${modelPrompt}.productionVersion`).d('生产版本'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      type: FieldType.string,
    },
    {
      name: 'revisionCode',
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      type: FieldType.string,
    },
    {
      name: 'qty',
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
      type: FieldType.string,
    },
    {
      name: 'rexQty',
      label: intl.get(`${modelPrompt}.rexQty`).d('变化数量'),
      type: FieldType.string,
    },
    {
      name: 'uomCode',
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      type: FieldType.string,
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      type: FieldType.string,
    },
    {
      name: 'planStartTime',
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
      type: FieldType.string,
    },
    {
      name: 'planEndTime',
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
      type: FieldType.string,
    },
    {
      name: 'locatorCode',
      label: intl.get(`${modelPrompt}.locatorCode`).d('默认完工库位编码'),
      type: FieldType.string,
    },
    {
      name: 'locatorName',
      label: intl.get(`${modelPrompt}.locatorName`).d('默认完工库位描述'),
      type: FieldType.string,
    },
    {
      name: 'bomName',
      label: intl.get(`${modelPrompt}.bomName`).d('装配清单'),
      type: FieldType.string,
    },
    {
      name: 'routerName',
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线'),
      type: FieldType.string,
    },
  ],
  // data:[
  //   {docNum: 213214213431243, docStatus: 'NEW'},
  //   {docNum: 213214},
  //   {docNum: 213214},
  // ]
});

const tableKittingDs = () => ({
  primaryKey: 'lineNumber',
  paging: false,
  selection: false,
  expandField: 'expand',
  fields: [
    {
      name: 'bomComponentName',
      type: FieldType.string,
    },
    {
      name: 'inputQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inputQty`).d('已投料套数/数量'),
    },
    {
      name: 'canInputQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.canInputQty`).d('未投料套数/数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
    },
    {
      name: 'inventoryQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inventoryQty`).d('剩余库存信息'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${yongjun}/v1/${tenantId}/hme-production-inputs/component/info/ui`,
        method: 'POST',
      };
    },
  },
});

const eoDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  paging: false,
  dataKey: 'rows',
  fields: [
    // 基本属性
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderNum`).d('WO编码'),
      disabled: true,
    },
    {
      name: 'totalQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.totalQty`).d('下达数量'),
      disabled: true,
    },
    // {
    //   name: 'totalQty',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.model.productionOrderMgt.releasedQty`).d('下达数量'),
    //   required: true,
    //   precision: 2,
    //   min: 0,
    //   max: 'canReleaseQty',
    //   defaultValidationMessages: {
    //     rangeOverflow: intl
    //       .get(`${modelPrompt}.model.productionOrderMgt.notLessMsg`)
    //       .d('下达数量不能大于可下达数量'),
    //   },
    //   validator: (value, _, record) => {
    //     const assembleList = record.get('assembleList');
    //     let overRecordNum = 0;
    //     if (assembleList && assembleList.length > 0) {
    //       assembleList.forEach(item => {
    //         if (value * (item.primaryUnitRatio || 0) > item.canReleaseQty) {
    //           overRecordNum++;
    //         }
    //       });
    //     }
    //     if (value === 0) {
    //       return intl
    //         .get(`${modelPrompt}.model.productionOrderMgt.validation.moreThanZero`)
    //         .d('数量必须大于0!');
    //     }
    //     if (overRecordNum > 0) {
    //       return intl
    //         .get(`${modelPrompt}.model.productionOrderMgt.validation.beforehandMax`)
    //         .d('预计生成数量不能大于可下达数量!');
    //     }
    //   },
    // },
    {
      name: 'eoQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.unitQty`).d('单位数量'),
      required: true,
      precision: 2,
      min: 0,
      max: 'totalQty',
      defaultValidationMessages: {
        rangeOverflow: intl
          .get(`${modelPrompt}.model.productionOrderMgt.notUomLessMsg`)
          .d('单位执行作业数量不能大于下达数量'),
      },
      validator: value => {
        if (value === 0) {
          return intl
            .get(`${modelPrompt}.model.productionOrderMgt.validation.moreThanZero`)
            .d('数量必须大于0!');
        }
      },
    },
    {
      name: 'mantissaDeal',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.mantissa`).d('尾数处理方式'),
      textField: 'description',
      valueField: 'typeCode',
      disabled: true,
      required: false,
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=EO_CREAT_MANTISSA_DEAL&type=eoMantissaOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled({ record }) {
          return !(
            isNumber(record.get('totalQty')) &&
            isNumber(record.get('eoQty')) &&
            record.get('totalQty') % record.get('eoQty') > 0
          );
        },
        required({ record }) {
          return (
            isNumber(record.get('totalQty')) &&
            isNumber(record.get('eoQty')) &&
            record.get('totalQty') % record.get('eoQty') > 0
          );
        },
      },
    },
    {
      name: 'eoCount',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.eoCount`).d('生成数量'),
      disabled: true,
      precision: 2,
    },
  ],
});

export { DispatchedOrderDs, WillDispatchOrderDs, queryDs, orderInfoDs, tableKittingDs, historyDs, eoDS };
