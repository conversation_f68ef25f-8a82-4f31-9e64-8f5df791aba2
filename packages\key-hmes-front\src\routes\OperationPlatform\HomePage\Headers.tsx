import React, { useMemo} from 'react';
import {
  Button,
  Tooltip,
  Icon,
} from 'choerodon-ui/pro';
import { Divider } from 'choerodon-ui';
// import { Icon as HIcon } from 'hzero-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import '../index.less';
import 'react-grid-layout/css/styles.css';
import { useOperationPlatform, DispatchType } from '../contextsStore';
import styles from '../index.module.less';
import user from '@/assets/operationPlatformCard/user.svg';
import solution from '@/assets/operationPlatformCard/solution.svg';
import clock from '@/assets/operationPlatformCard/clock.svg';


const modelPrompt = 'common.hmes.OperationPlatform';

const Headers = (props) => {
  const {
    screenFull,
    editing,
    handleEditLayout,
    handleSaveLayout,
    handleCancelLayout,
    loading,
    handleCardSetting,
    cardShow,
  } = props;

  const {
    dispatch,
    enterInfo,
    itemLayout,
  } = useOperationPlatform();
  /**
   * header 操作按钮 --- 编辑、保存等
   * 如果不需要，则可以删除这块内容
   */
  const headerActions = useMemo(() => {
    return editing ? (
      <div id='headerButton'>
        <Button
          color={ButtonColor.primary}
          icon="save"
          disabled={cardShow}
          onClick={handleSaveLayout}
          loading={loading}
          style={{marginLeft: '12px'}}
        >
          {intl.get(`${modelPrompt}.preservation`).d('保存')}
        </Button>
        <Button disabled={loading || cardShow} icon="remove_circle_outline" onClick={handleCancelLayout}>
          {intl.get(`${modelPrompt}.cancel`).d('取消')}
        </Button>
        <Button icon="brightness_5-o" disabled={cardShow} onClick={handleCardSetting}>
          {intl.get(`${modelPrompt}.card.settings`).d('卡片设置')}
        </Button>
      </div>
    ) : (
      <div id='headerButton'>
        <Button color={ButtonColor.primary} icon="table_chart-o" onClick={handleEditLayout}>
          {intl.get(`${modelPrompt}.set.layout`).d('设置布局')}
        </Button>
        <Button
          color={ButtonColor.primary}
          onClick={() => {
            window.localStorage.setItem('workcellSelectedId', JSON.stringify(''));
            dispatch({
              type: DispatchType.update,
              payload: {
                logined: false,
                woReportDetail: {},
                enterInfo: {},
                workOrderData: {},
              },
            });
          }}
        >
            工位切换
        </Button>
      </div>
    );
  }, [editing, handleEditLayout, handleSaveLayout, handleCancelLayout, loading, itemLayout]); // 注意依赖数组，该值的重新计算依赖于该数组内的值的变化

  return (
    <div className={styles.headers}>
      <Icon
        type="zoom_out_map-o"
        onClick={screenFull}
        style={{ marginLeft: 10, color: '#01e1ef', cursor: 'pointer' }}
      />
      {headerActions}
      {enterInfo.workStationName && (
        <Tooltip
          placement="bottom"
          title={
            <div style={{ display: 'inline', whiteSpace: 'nowrap' }}>
              <span>
                {/* <HIcon type="user" /> */}
                <img src={user} alt='' />
                        &nbsp;{enterInfo.userName}
              </span>
              <br />
              <span>
                {/* <HIcon type="solution" /> */}
                <img src={solution} alt='' />
                        &nbsp;{enterInfo.workStationName}
                        -{enterInfo.selectOperation.description}
              </span>
              <br />
              <span>
                {/* <HIcon type="clock-circle-o" /> */}
                <img src={clock} alt='' />
                        &nbsp;班次&nbsp;-&nbsp;{enterInfo.shiftCode}
              </span>
            </div>
          }
        >
          <div className={styles.titleEnterInfo}>
            <span>
              {/* <HIcon type="user" /> */}
              <img src={user} alt='' />
                      &nbsp;{enterInfo.userName}
            </span>
            <Divider type="vertical" style={{ background: '#01e1ef' }} />
            <span>
              {/* <HIcon type="solution" /> */}
              <img src={solution} alt='' />
                      &nbsp;{enterInfo.workStationName}
                      -{enterInfo.selectOperation.description}
            </span>
            <Divider type="vertical" style={{ background: '#01e1ef' }} />
            <span>
              {/* <HIcon type="clock-circle-o" /> */}
              <img src={clock} alt='' />
                      &nbsp;{enterInfo.shiftCode}
            </span>
          </div>
        </Tooltip>
      )}
    </div>
  )
}

export default Headers;
