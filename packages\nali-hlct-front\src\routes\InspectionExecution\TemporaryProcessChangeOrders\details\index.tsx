import React, { useMemo, useEffect, useState } from 'react';
import { DataSet, Spin, Button, Modal, Form, DateTimePicker, TextArea, Attachment } from 'choerodon-ui/pro';
import { useDataSetEvent } from 'utils/hooks';
import intl from 'utils/intl';
import { omit } from 'lodash';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import notification from 'utils/notification';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import { Popconfirm, Radio } from 'choerodon-ui';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import withProps from 'utils/withProps';
import { observer } from 'mobx-react';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { BasicMessageDS } from '../stores/DetailsDS';
import style from '../index.modules.less';
import OrderChangePage from './OrderChangePage';
import { PostPoneHeaderDS, PostPoneLineDS } from '../stores/PostPoneDS';
import PostPonePage from './PostPonePage';
import StopPage from './StopPage';
import { StopHeaderDS, StopLineDS } from '../stores/StopDS';
import { changeItemDS } from '../stores/changeItemDS';
import { BatchDS, inspectionItemBasisDS, ExtensionDS } from '../stores/BatchDS';
import ChangeMessageDS from '../stores/ChangeMessageDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`;

/**
 * @description ORDER_CHANGE:变更单内容 POSTPONE:延期信息 STOP:中止信息
 */
enum RadioMap {
  ORDER_CHANGE = 'ORDER_CHANGE', // 变更单内容
  POSTPONE = 'POSTPONE', // 延期信息
  STOP = 'STOP', // 中止信息
}

const urlMap = {
  // 变更内容详情页查询
  basicUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/info/ui`,
  // 延期/中止 头信息
  amendHeaderUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/amend-info/ui`,
  // 延期/中止 行信息
  amendLineUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/amend-line-info/ui`,
  // 保存接口
  saveUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/save/ui `,
  // 提交审批按钮接口
  submitUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/submit/ui`,
  // 修改信息项查询接口
  changeItemUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/change-item/ui`,
  // 获取配置值接口
  configurationFirstUrl: `/hpfm/v1/${tenantId}/profiles?page=0&profileName=YP.QMS_ENW_TIME_LIMIT&size=10`,
  configurationSecondUrl: `/hpfm/v1/${tenantId}/profiles/`,
  // 延期/中止提交（tempPermitAmendType传DELAY就是延期，传END就是中止）
  amendSubmitUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/amend-submit/ui`,
  // 根据用户id获取员工信息
  employeeList : `/hpfm/v1/${tenantId}/employee-users/employee/batch`,
};

const oneDayNum = 86400000;

const DetailsPage = ({
  basicDs,
  postPoneDs,
  postPoneLineDs,
  stopDs,
  stopLineDs,
  customizeForm,
  extensionDs,
  changeItemAddDs,
  changeItemDeleteDs,
  changeItemModifyDs,
  match: { path, params: { id } },
  history,
}) => {
  // pub路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  // 加载动画
  const [loading, setLoading] = useState<boolean>(false);
  // 当前显示信息页 变更单内容。延期信息 。中止信息
  const [currentPage, setCurrentPage] = useState<RadioMap>(RadioMap.ORDER_CHANGE);
  // 是否可编辑
  const [canEditFlag, setCanEditFlag] = useState<boolean>(false);
  // 变更单信息
  const [inspectBusinessTypeList, setInspectBusinessTypeList] = useState<any[]>([]);
  // 变更单信息备份
  const [inspectBusinessTypeList1, setInspectBusinessTypeList1] = useState<any[]>([]);
  // 编辑按钮是否禁用
  const [editBtnFlag, setEditBtnFlag] = useState(true);
  // 提交审批按钮是否禁用
  const [submitApprovcalFlag, setSubmitApprovcalFlag] = useState(true);
  // 获取到的配置值
  const [configurationValue, setConfigurationValue] = useState(0);

  useEffect(() => {
    if (id !== 'create') {
      setLoading(true);
      initData();
      setCanEditFlag(false);
    } else {
      getTime().then(()=>{
        setLoading(false);
        setCanEditFlag(true);
      })
    }
  }, [id]);

  useDataSetEvent(basicDs,'update',({name,record})=>{
    handleUpdateBasicData(name,record)
  })
  useDataSetEvent(postPoneDs, 'select', ({ record }) => {
    getLineData(record.get('tempPermitAmendId'), postPoneLineDs);
  })
  useDataSetEvent(postPoneDs, 'unselect', () => {
    postPoneLineDs.loadData([]);
  })
  useDataSetEvent(stopDs, 'select', ({ record }) => {
    getLineData(record.get('tempPermitAmendId'), stopLineDs);
  })
  useDataSetEvent(stopDs, 'unselect', () => {
    stopLineDs.loadData([]);
  })

  // 获取详情信息
  const initData = async () => {
    // TODO: 初始化数据
    const basicData = await request(urlMap.basicUrl, {
      method: 'GET',
      params: {
        temporaryPermitDocId: id,
      },
    })
    const delayData = await request(urlMap.amendHeaderUrl, {
      method: 'GET',
      params: {
        temporaryPermitDocId: id,
        tempPermitAmendType: 'DELAY',
      },
    })
    const endData = await request(urlMap.amendHeaderUrl, {
      method: 'GET',
      params: {
        temporaryPermitDocId: id,
        tempPermitAmendType: 'END',
      },
    })
    const configurationValue = await request(urlMap.configurationFirstUrl);
    const getEmployeeList = await request(urlMap.employeeList,{
      method: 'GET',
      params:{
        userIdList:[getCurrentUser().id],
      },
    })
    setLoading(false);
    if(getEmployeeList instanceof Array){
      basicDs.setState('employeeId', getEmployeeList[0].employeeId);
    }
    if (configurationValue && configurationValue?.totalPages === 1) {
      request(`${urlMap.configurationSecondUrl}${configurationValue.content[0].profileId}`).then(res => {
        setConfigurationValue(
          res?.profileValueDTOList[0]?.value ?
            res?.profileValueDTOList[0]?.value * oneDayNum :
            0,
        );
        basicDs.setState('configurationValue', res?.profileValueDTOList[0]?.value ?
          res?.profileValueDTOList[0]?.value * oneDayNum :
          0);
      })
    };
    if (basicData && basicData.success) {
      const { typeGroupList = [], ...basicDatas } = basicData.rows;
      typeGroupList.forEach(item => {
        item.inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());
        item.equipmentGroupList.forEach(items => {
          items.batchDs = new DataSet(BatchDS());
          items.ds = new DataSet(ChangeMessageDS());
          items.itemInfo.forEach(_itemInfo => {
            if (['TEXT', 'DECISION_VALUE'].includes(_itemInfo.dataType)) {
              _itemInfo.trueValue =
                (_itemInfo.trueValueList || []).length > 0
                  ? _itemInfo.trueValueList[0].dataValue
                  : null;
              _itemInfo.falseValue =
                (_itemInfo.falseValueList || []).length > 0
                  ? _itemInfo.falseValueList[0].dataValue
                  : null;
            }
            if (_itemInfo.dataType === 'VALUE_LIST') {
              _itemInfo.trueValue =
                (_itemInfo.trueValueList || []).length > 0
                  ? _itemInfo.trueValueList.map(trueItem => trueItem.dataValue)
                  : null;
              _itemInfo.falseValue =
                (_itemInfo.falseValueList || []).length > 0
                  ? _itemInfo.falseValueList.map(falseItem => falseItem.dataValue)
                  : null;
            }
          })
        })
      })
      setInspectBusinessTypeList(typeGroupList);
      setInspectBusinessTypeList1(typeGroupList);
      basicDatas.inspectBusinessTypeList = typeGroupList.map(item => item.inspectBusinessType);
      basicDs.loadData([{ ...basicDatas }]);
      setEditBtnFlag(!['NEW', 'AMENDING', 'REJECT'].includes(basicDs.current?.get('temporaryPermitStatus')));
      setSubmitApprovcalFlag(!['NEW', 'REJECT', 'AMENDING'].includes(basicDs.current?.get('temporaryPermitStatus')));
    }

    if (delayData && delayData.success) {
      const { rows = [] } = delayData;
      postPoneDs.loadData(rows)
      postPoneDs.select(postPoneDs.current);
    };

    if (endData && endData.success) {
      const { rows = [] } = endData;
      stopDs.loadData(rows)
      stopDs.select(stopDs.current)
    };

    // OA审批界面需要查询变更对象信息
    if(pubFlag) {
      changeItemAddDs.setQueryParameter('temporaryPermitDocId', id);
      changeItemAddDs.query().then((res) => {
        if (res?.rows) {
          const { addItemList, deleteItemList, modifyItemList } = res?.rows;
          changeItemAddDs.loadData(addItemList || []);
          changeItemDeleteDs.loadData(deleteItemList || []);
          changeItemModifyDs.loadData(modifyItemList || []);
        }
      })
    }
  };

  const getTime = async()=>{
    const configurationValue = await request(urlMap.configurationFirstUrl);
    if (configurationValue && configurationValue?.totalPages === 1) {
      request(`${urlMap.configurationSecondUrl}${configurationValue.content[0].profileId}`).then(res => {
        setConfigurationValue(
          res?.profileValueDTOList[0]?.value ?
            res?.profileValueDTOList[0]?.value * oneDayNum :
            0,
        );
        basicDs.setState('configurationValue', res?.profileValueDTOList[0]?.value ?
          res?.profileValueDTOList[0]?.value * oneDayNum :
          0);
      })
    };
  };

  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'temporary.process.change.orders',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  // 获取行信息
  const getLineData = async (id, dataSet: DataSet) => {
    const res = await request(urlMap.amendLineUrl, {
      method: 'GET',
      params: {
        tempPermitAmendId: id,
      },
    })
    if (res && res.success) {
      const { rows = [] } = res;
      dataSet.loadData(rows);
    }
  };

  const handleUpdateBasicData =(name, record)=>{
    switch (name){
      case 'inspectBusinessType':
        setInspectBusinessTypeList([]);
        break;
      case 'materialCodeObj':
        setInspectBusinessTypeList([]);
        break;
      case 'prodLineCodeObj':
        setInspectBusinessTypeList([]);
        break;
      case 'operationNameObj':
        setInspectBusinessTypeList([]);
        break;
      case 'inspectBusinessTypeList':
        setInspectBusinessTypeList([]);
        break;
      case 'equipmentScope':  
        setInspectBusinessTypeList([]);
        record.set('equipmentNameObj', null);
        break;
      default: 
        break;
    }
  };

  // 切换tab页
  const handlePageChange = (e) => {
    setCurrentPage(e.target?.value);
  };

  // 变更单内容
  const orderChangePageProps = {
    basicDs,
    inspectBusinessTypeList,
    canEdit: canEditFlag,
    customizeForm,
    setInspectBusinessTypeList,
    history,
    pubFlag,
    changeItemAddDs,
    changeItemDeleteDs,
    changeItemModifyDs,
  };

  // 延期信息
  const postPoneProps = {
    postPoneDs,
    postPoneLineDs,
  };

  // 中止信息
  const stopProps = {
    stopDs,
    stopLineDs,
  };

  // 取消编辑
  const handleCancelEdit = () => {
    if (id === 'create') {
      history.push(`/hwms/temporary-process-change-orders/list`);
    } else {
      basicDs.reset();
      setInspectBusinessTypeList(inspectBusinessTypeList1);
      setCanEditFlag(false);
    }
  };

  // 保存
  const handleSave = async () => {
    const flag = await basicDs.validate();
    if (flag) {
      const basicData = basicDs.current.toData();
      const typeGroupList = inspectBusinessTypeList.map(item => {
        const { equipmentGroupList, ..._item } = item;
        const newEquipmentGroupList = equipmentGroupList.map(items => {
          const { ds, ..._items } = items;
          const itemInfo = (ds?.toData() || []).map(i => ({
            ...i,
            trueValue: undefined,
            falseValue: undefined,
            warningValue: undefined,
          }));
          return {
            ...omit(_items, ['batchDs']),
            itemInfo,
          }
        })
        return {
          ...omit(_item, ['inspectionItemBasisDs']),
          equipmentGroupList: newEquipmentGroupList,
        }
      })

      // 校验变更内容必须有值
      const changeMessageFlag = typeGroupList.some(typeGroup => {
        return typeGroup?.equipmentGroupList?.length > 0 && typeGroup?.equipmentGroupList?.some(equipmentGroup => {
          return equipmentGroup?.itemInfo?.length > 0;
        });
      })
      if (typeGroupList.length < 0 || !changeMessageFlag) {
        return notification.error({
          message: intl.get(`${modelPrompt}.detail.please.add.changeMessage`).d('请添加变更内容！'),
        })
      }

      // 不能在一个维度下选2个相同的项目，否则报错：一个维度下不可维护重复项目，请检查！
      const sameFlag = typeGroupList.map(typeGroup=>{
        return typeGroup.equipmentGroupList.some((equipmentGroup)=>{
          return [...new Set(equipmentGroup.itemInfo.map(item=>item.inspectItemCode))].length<equipmentGroup.itemInfo.map(item=>item.inspectItemCode).length;
        })
      }).filter(Boolean).length>0;
      if (sameFlag) {
        return notification.error({
          message: intl.get(`${modelPrompt}.detail.please.cannot.same`).d('一个维度下不可维护重复项目，请检查！'),
        })
      }

      // 前端保存校验：若检验业务类型=GCJ或XJ，校验检测频率和频率参数字段必输，否则报错：过程检、巡检的检验频率必输
      const inspectFrequencyFlag = typeGroupList.filter(
        typeGroup => ['GCJ', 'XJ'].includes(typeGroup.inspectBusinessType),
      ).map(
        item => item.equipmentGroupList.map(
          equipmentGroup => equipmentGroup.itemInfo.every(
            item => item.inspectFrequency && item.m && item.n,
          ),
        ).filter(item => !item).length > 0,
      ).filter(item => item).length > 0;
      if(inspectFrequencyFlag){
        return notification.error({
          message: intl.get(`${modelPrompt}.detail.businessType.required`).d('检验业务类型为过程检/巡检的检验频率必输!'),
        })
      }

      const data = {
        ...basicData,
        typeGroupList,
      }
      setLoading(true);
      const res = await request(urlMap.saveUrl, {
        method: 'POST',
        data,
      })
      setLoading(false);
      if (res && res.success) {
        setCanEditFlag(false);
        if (id === 'create') {
          history.push(`/hwms/temporary-process-change-orders/detail/${res.rows}`)
          initData();
        } else {
          initData();
        }
      }
      else {
        return notification.error({
          message: res.message,
        })
      }
    }
  };

  // 提交审批按钮
  const handleSubmitApproval = async () => {
    setLoading(true);
    const res = await request(urlMap.submitUrl, {
      method: 'POST',
      params: {
        temporaryPermitDocId: id,
      },
    })
    setLoading(false);
    if (res && res.success) {
      initData();
      return notification.success({});
    }
    return notification.error({
      message: res.message,
    })
  };


  // 提交延期/中止申请
  const handleExtension = (tempPermitAmendType: string) => {
    const tempPermitItemIds: number[] = [];
    inspectBusinessTypeList.forEach(item => {
      item.equipmentGroupList.forEach(items => {
        items.ds.selected.forEach(record => {
          tempPermitItemIds.push(record.get('tempPermitItemId'));
        });
      })
    });
    extensionDs.setState('maxDate', new Date(+basicDs.current.get('scheduleStartDate') + configurationValue))
    extensionDs.setState('type', tempPermitAmendType);

    const handleSubmitExtension = async (modal) => {
      const flag = await extensionDs.validate();
      if(!flag){
        return
      }
      const res = await request(urlMap.amendSubmitUrl, {
        method: 'POST',
        data: {
          ...extensionDs.current?.toData(),
          tempPermitItemIds,
          tempPermitAmendType,
          temporaryPermitDocId: id,
        },
      })
      if (res && res.success) {
        modal.close();
        initData()
        return notification.success({});
      }
      return notification.error({
        message: res?.message,
      })

    };

    const modal = Modal.open({
      title: tempPermitAmendType === 'END' ?
        intl.get(`${modelPrompt}.detail.amendEndSubmit`).d('提交中止申请') :
        intl.get(`${modelPrompt}.detail.amendDelaySubmit`).d('提交延期申请'),
      children: <Form dataSet={extensionDs}>
        <DateTimePicker name='amendDate' />
        <TextArea name='amendRemark' />
        <Attachment {...attachmentProps} />
      </Form>,
      closable: true,
      onClose: ()=>extensionDs.reset(),
      footer: (_, cancelBtn) => {
        return [
          <Popconfirm
            title={intl.get(`${modelPrompt}.detail.cannotEdit.after.submit`).d('提交后不可修改，是否确认提交?')}
            okText={intl.get('tarzan.common.label.yes').d('是')}
            cancelText={intl.get('tarzan.common.label.no').d('否')}
            onConfirm={() => handleSubmitExtension(modal)}
          >
            <Button
              color={ButtonColor.primary}
              style={{ marginRight: 15 }}
            >
              {intl.get(`${modelPrompt}.detail.ok`).d('确认')}
            </Button>
          </Popconfirm>,
          cancelBtn,
        ];
      },
    });
  };

  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        {pubFlag ? (
          <Header title={intl.get(`${modelPrompt}.detail.title`).d('临时工艺变更单')} />
        ) : (
          <Header
          title={intl.get(`${modelPrompt}.detail.title`).d('临时工艺变更单')}
          backPath='/hwms/temporary-process-change-orders/list'
        >
          {
            canEditFlag &&
            <>
              <Button color={ButtonColor.primary} onClick={handleSave}>
                {intl.get('hzero.common.button.save').d('保存')}
              </Button>
              <Button onClick={handleCancelEdit}>
                {intl.get('hzero.common.button.cancel').d('取消')}
              </Button>
            </>
          }
          {
            !canEditFlag &&
            <Button
              color={ButtonColor.primary}
              onClick={() => setCanEditFlag(true)}
              disabled={editBtnFlag}
            >
              {intl.get('hzero.common.button.edit').d('编辑')}
            </Button>
          }
          <ApprovalInfoDrawer
            objectTypeList={['QIS_LSGYBG_CREATE']}
            objectId={id}
          />
          {
            id !== 'create' &&
            <>

              <Popconfirm
                title={intl.get(`${modelPrompt}.detail.sureSubmit`).d('是否确认提交?')}
                okText={intl.get('tarzan.common.label.yes').d('是')}
                cancelText={intl.get('tarzan.common.label.no').d('否')}
                onConfirm={handleSubmitApproval}
              >
                <Button disabled={submitApprovcalFlag}>
                  {
                    intl.get(`${modelPrompt}.detail.header.btn.submit.approval`).d('提交审批')
                  }
                </Button>
              </Popconfirm>
              <Button
                onClick={() => handleExtension('DELAY')}
                disabled={
                  !(
                    basicDs.current.get('temporaryPermitStatus') === 'EXECUTE' &&
                    postPoneDs.records.every((record) => record.get('reviewStatus') !== 'REVIEWING') &&
                    inspectBusinessTypeList.some(item => {
                      return item.equipmentGroupList.some(items => {
                        return items.ds.selected.length > 0;
                      })
                    })
                  )
                }
              >
                {intl.get(`${modelPrompt}.detail.header.btn.extension`).d('提交延期申请')}
              </Button>
              <Button
                onClick={() => handleExtension('END')}
                disabled={
                  !(
                    basicDs.current.get('temporaryPermitStatus') === 'EXECUTE' &&
                    stopDs.records.every((record) => record.get('reviewStatus') !== 'REVIEWING') &&
                    inspectBusinessTypeList.some(item => {
                      return item.equipmentGroupList.some(items => {
                        return items.ds.selected.length > 0;
                      })
                    })
                  )
                }
              >
                {intl.get(`${modelPrompt}.detail.header.btn.approval`).d('提交中止申请')}
              </Button>
            </>
          }
        </Header>
        )}
        <Content>
          <Radio.Group
            onChange={handlePageChange}
            value={currentPage}
            className={style['radio-group-margin']}
          >
            <Radio.Button value={RadioMap.ORDER_CHANGE}>
              {intl.get(`${modelPrompt}.detail.tab.orderChange`).d('变更单内容')}
            </Radio.Button>
            <Radio.Button value={RadioMap.POSTPONE} disabled={id === 'create'}>
              {intl.get(`${modelPrompt}.detail.tab.postPone`).d('延期信息')}
            </Radio.Button>
            <Radio.Button value={RadioMap.STOP} disabled={id === 'create'}>
              {intl.get(`${modelPrompt}.detail.tab.stop`).d('中止信息')}
            </Radio.Button>
          </Radio.Group>
          {/* 变更单内容 */}
          {
            currentPage === RadioMap.ORDER_CHANGE &&
            <OrderChangePage {...orderChangePageProps} />
          }
          {/* 延期信息 */}
          {
            currentPage === RadioMap.POSTPONE &&
            <PostPonePage {...postPoneProps} />
          }
          {
            currentPage === RadioMap.STOP &&
            <StopPage {...stopProps} />
          }
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [
    'tarzan.common',
    'hzero.common',
    'tarzan.qms.temporaryProcessChangeOrders',
  ],
})(withProps(
  () => {
    const basicDs = new DataSet(BasicMessageDS());
    const postPoneDs = new DataSet(PostPoneHeaderDS());
    const postPoneLineDs = new DataSet(PostPoneLineDS());
    const stopDs = new DataSet(StopHeaderDS());
    const stopLineDs = new DataSet(StopLineDS());
    const extensionDs = new DataSet(ExtensionDS());
    const changeItemAddDs = new DataSet(changeItemDS());
    const changeItemDeleteDs = new DataSet(changeItemDS());
    const changeItemModifyDs = new DataSet(changeItemDS());
    return {
      basicDs,
      postPoneDs,
      postPoneLineDs,
      stopDs,
      stopLineDs,
      extensionDs,
      changeItemAddDs,
      changeItemDeleteDs,
      changeItemModifyDs,
    };
  },
  { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
)(withCustomize({
  unitCode: [
    `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DETAIL`,
    `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.LINE_DETAIL`,
    `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.ITEM_DETAIL`,
    `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.DMS_DETAIL`,
    `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.ITEM_DRAWER`,
    `${BASIC.CUSZ_CODE_BEFORE}.INITIAL_MANAGEMENT_ACTIVITY.ATTR`,
  ],
})(observer(DetailsPage) as any),
));