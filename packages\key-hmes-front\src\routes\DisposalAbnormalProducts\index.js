import React, { useMemo, useState } from 'react';
import { DataSet, Table, Row, Col, Form, TextField,Lov, Icon, Button } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { observer } from 'mobx-react';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from './stores/DisposalAbnormalProductsDS';
import LovModal from "../ProductBatchProcessCancellation/LovModal";
import InputLovDS from '../../stores/InputLovDS';
import {Release} from './services'


const modelPrompt = 'tarzan.hmes.disposalAbnormalProducts';


const DisposalAbnormalProducts = observer(() => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  // const [expandForm, setExpandForm] = useState(false);

  // const toggleForm = () => {
  //   setExpandForm(!expandForm);
  // }
  const { run: release } = useRequest(
    Release(),
    {
      needPromise: true,
      manual: true,
    },
  );

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}
          style={{
            display: 'flex',
            alignItems: 'flex-start',
          }}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identifications', '条码号', queryDataSet)}
                    />
                  </div>
                }
              />
              <Lov name="materialLov" />
              <Lov name="operationLov" />
            </Form>
          </Col>
          <Col span={6}>
            <div>
              {/* <Button funcType="link" icon={
                expandForm? 'expand_less':'expand_more'
              } onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button> */}
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  }
  const handleSearch = async () => {
    const {operationId, materialId, identifications} = tableDs?.queryDataSet.toJSONData()[0]
    if(!identifications&&!operationId&&!materialId){
      notification.error({
        message: intl.get(`${modelPrompt}.enter.required`).d('请至少输入一个查询条件！'),
      })
    }else{
      tableDs.query()
    }
  }
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
    }
  }

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const columns = [
    {
      name: 'siteCode',
    },
    {
      name: 'identification',
    },
    {
      name: 'eoNum',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'concessiveInterceptionFlag',
    },
    {
      name: 'operationDesc',
    },
    {
      name: 'qualityStatusDesc',
    },
    {
      name: 'routerStepDesc',
    },
  ];

  const handleRelease = async () => {
    if(tableDs.selected.some(item => item?.get('concessiveInterceptionFlag') !== 'Y')){
      const arr = tableDs.selected.filter(item => item?.get('concessiveInterceptionFlag') !== 'Y')
      const str = arr.map(item => item.data.identification).join(',')
      notification.error({
        message: `${str}${intl.get(`${modelPrompt}.message.noRelease `).d(`不存在拦截标识，不需要放行！`)}`,
      })
    }else{
      const arrNew = tableDs.selected.filter(item => item?.get('concessiveInterceptionFlag') === 'Y')
      const res = await release({
        params: arrNew.map(item => item?.data),
      })
      if(res&&res.success){
        notification.success({
          message: intl.get(`${modelPrompt}.message.success`).d('操作成功！'),
        })
        tableDs.query()
      }else{
        // notification.error({
        //   message: res.message,
        // })
      }
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.realName`).d('异常品处置')}>
        <Button color="primary" onClick={handleRelease} disabled={tableDs.selected.length === 0}>
          {intl.get(`${modelPrompt}.release`).d('放行')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          queryBar={renderQueryBar}
          searchCode="DisposalAbnormalProducts"
          customizedCode="DisposalAbnormalProducts"
        />
      </Content>
      <LovModal {...lovModalProps} />
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.disposalAbnormalProducts', 'tarzan.common'],
})(DisposalAbnormalProducts);
