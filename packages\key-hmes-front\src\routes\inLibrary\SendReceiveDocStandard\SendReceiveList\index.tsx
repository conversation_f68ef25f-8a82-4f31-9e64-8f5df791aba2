/**
 * @Description: 库存调拨平台 - 列表页
 * @Author: <EMAIL>
 * @Date: 2022/3/8 10:11
 * @LastEditTime: 2023-05-18 16:21:34
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Dropdown, Menu, Modal, Table } from 'choerodon-ui/pro';
import { Badge, Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import { headerTableDS, lineTableDS } from '../stores/EntranceDS';
import { drawerTableDS, inventoryDrawerTableDS } from '../stores/DrawerTableDS';
import { HandleChangeStatus, HandleDeleteLine } from '../services';
import AppointMaterialLotPage from '../AppointMaterialLot';
import styles from './index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inLibrary.sendReceiveDoc';

function isAllEqualWithKeyWord(array: string[], keyWord: string) {
  // 勾选的都是下达状态，状态变更可点击，且下拉显示取消
  const cancelList: string[] = ['RELEASED'];
  // 这几种状态时，状态变更可点击，且下拉显示关闭
  const closeList: string[] = [
    'PROCESSING',
    '1_PROCESSING',
    '1_COMPLETED',
    '2_PROCESSING',
    'COMPLETED',
  ];
  if (array.length > 0) {
    if (keyWord === 'CANCEL') {
      return array.some(value => {
        return cancelList.indexOf(value) === -1;
      });
    }
    if (keyWord === 'CLOSED') {
      return array.some(value => {
        return closeList.indexOf(value) === -1;
      });
    }
  } else {
    return false;
  }
}

const SendReceiveList = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;
  // 动态列实际columns
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedInstructionDocIds, setSelectedInstructionDocIds] = useState<any[]>([]);
  const drawerTableDs: DataSet = useMemo(() => new DataSet(drawerTableDS()), []);
  const sendDrawerTableDs: DataSet = useMemo(() => new DataSet(inventoryDrawerTableDS()), []);
  const receiveDrawerTableDs: DataSet = useMemo(() => new DataSet(inventoryDrawerTableDS()), []);

  const { run: handleChangeStatus, loading: changeStatusLoading } = useRequest(
    HandleChangeStatus(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: handleDeleteLine } = useRequest(HandleDeleteLine(), {
    manual: true,
    needPromise: true,
  });
  let _copyDrawer;

  // 当保存成功回到页面时自动重查数据
  useEffect(() => {
    // 当在页签上右键刷新时，如果当前表格有勾选数据时，需要随之变按钮禁用状态
    handleDataSetSelectUpdate();
    if (props?.location?.state && props?.location?.state?.queryFlag === true) {
      headerTableDs.query(headerTableDs.currentPage);
    }
  }, []);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  // 进来不查询，当从详情返回后查询当前页面
  useEffect(() => {
    if (headerTableDs.toData().length > 0) {
      headerTableDs.query(headerTableDs.currentPage);
    }
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'batchSelect', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'batchUnSelect', handleDataSetSelectUpdate);
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    handleDataSetSelectUpdate();
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable(null);
    }
  };

  const headerRowClick = record => {
    const ds = lineTableDs.queryDataSet;
    ds.getField('fromWareHouseLov').set('lovPara', {
      tenantId,
      enableFlag: 'Y',
      siteId: record.get('fromSiteId'),
      locatorCategoryAreaFlag: 'Y',
    });
    ds.getField('toWareHouseLov').set('lovPara', {
      tenantId,
      enableFlag: 'Y',
      siteId: record.get('toSiteId'),
      locatorCategoryAreaFlag: 'Y',
    });
    queryLineTable(record?.toData()?.instructionDocId);
  };

  // 行列表数据查询
  const queryLineTable = instructionDocId => {
    if (instructionDocId) {
      lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
    } else {
      lineTableDs.setQueryParameter('instructionDocId', 0);
    }
    lineTableDs.query();
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _selectedStatus: string[] = [];
    const _instructionDocIds: string[] = [];
    headerTableDs.selected.forEach(item => {
      const { instructionDocStatus, instructionDocId } = item.toData();
      _instructionDocIds.push(instructionDocId);
      if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    setSelectedInstructionDocIds(_instructionDocIds);
    setSelectedStatus(_selectedStatus);
  };

  // 点击状态变更的回调
  const clickMenu = async key => {
    const selectItems: any = [];
    headerTableDs.selected.forEach(item => {
      const _selectItem = {
        instructionDocId: item?.toData()?.instructionDocId,
        instructionDocStatus: item?.toData()?.instructionDocStatus,
        instructionDocType: item?.toData()?.instructionDocType,
      };
      selectItems.push({ ..._selectItem });
    });

    return handleChangeStatus({
      params: {
        instructionDocList: selectItems,
        alterStatus: key,
      },
    }).then(res => {
      if (res && res.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        notification.success({});
        headerTableDs.clearCachedSelected();
        headerTableDs.query(headerTableDs.currentPage);
      }
    });
  };

  // 点击"行取消"的回调
  const clickDeleteLine = async record => {
    return handleDeleteLine({
      params: record.toData().instructionDocLineId,
    }).then(res => {
      if (res && res.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        notification.success({});
        headerTableDs.clearCachedSelected();
        headerTableDs.query(headerTableDs.currentPage);
      }
    });
  };

  const materialLotColumns: ColumnProps[] = [
    { name: 'identification', width: 150 },
    { name: 'materialLotStatusDesc', width: 150 },
    { name: 'container', width: 150 },
    { name: 'qty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'lot' },
    { name: 'sendLocatorCode', width: 150 },
    { name: 'sendDate', width: 150, align: ColumnAlign.center },
    { name: 'sendRealName' },
    { name: 'receiveLocatorCode', width: 150 },
    { name: 'receiveDate', width: 150, align: ColumnAlign.center },
    { name: 'receivePerson' },
  ];

  const sendColumns: ColumnProps[] = [
    { name: 'identification' },
    { name: 'containerIdentification' },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'qualityStatusDesc',
    },
    { name: 'qty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'lot' },
    { name: 'sendLocatorCode', width: 150 },
    { name: 'sendDate', width: 150, align: ColumnAlign.center },
    { name: 'sendRealName' },
    { name: 'targetMaterialLotCode' },
  ];
  const receiveColumns: ColumnProps[] = [
    { name: 'identification' },
    { name: 'containerIdentification' },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'qualityStatusDesc',
    },
    { name: 'qty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'lot' },
    { name: 'receiveLocatorCode', width: 150 },
    { name: 'receiveDate', width: 150, align: ColumnAlign.center },
    { name: 'receivePerson' },
    { name: 'targetMaterialLotCode' },
  ];

  // 点击“物料批明细”的回调
  const goLotDetail = record => {
    // 实物
    if (!(['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType))) {
      drawerTableDs.setQueryParameter('instructionDocLineId', record.toData().instructionDocLineId);
      drawerTableDs.setQueryParameter('instructionDocType', record.toData().instructionDocType);
      drawerTableDs.setQueryParameter('identifyType', record.toData().identifyType);
      drawerTableDs.setQueryParameter('toIdentifyType', record.toData().toIdentifyType);
      drawerTableDs.query();
    }

    // 非实物
    if (['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType)) {
      sendDrawerTableDs.setQueryParameter(
        'instructionDocLineId',
        record.toData().instructionDocLineId,
      );
      sendDrawerTableDs.setQueryParameter('instructionDocType', record.toData().instructionDocType);
      sendDrawerTableDs.setQueryParameter('identifyType', record.toData().identifyType);
      sendDrawerTableDs.setQueryParameter('toIdentifyType', record.toData().toIdentifyType);
      sendDrawerTableDs.setQueryParameter('detailCategory', 'SEND');
      sendDrawerTableDs.query();
      receiveDrawerTableDs.setQueryParameter(
        'instructionDocLineId',
        record.toData().instructionDocLineId,
      );
      receiveDrawerTableDs.setQueryParameter(
        'instructionDocType',
        record.toData().instructionDocType,
      );
      receiveDrawerTableDs.setQueryParameter('identifyType', record.toData().identifyType);
      receiveDrawerTableDs.setQueryParameter('toIdentifyType', record.toData().toIdentifyType);
      receiveDrawerTableDs.setQueryParameter('detailCategory', 'RECEIVE');
      receiveDrawerTableDs.query();
    }
    _copyDrawer = Modal.open({
      title:
        !(['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType))
          ? intl.get(`${modelPrompt}.materialBatch.details`).d('物料批信息')
          : intl.get(`${modelPrompt}.materialDetail`).d('物料明细'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          {!(['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType)) && (
            customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
              },
              <Table dataSet={drawerTableDs} columns={materialLotColumns} />,
            )
          )}
          {(['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType)) && (
            <>
              <Collapse bordered={false} defaultActiveKey={['sendDetail']}>
                <Panel
                  header={intl.get(`${modelPrompt}.send.detail`).d('发出明细')}
                  key="sendDetail"
                >
                  {customizeTable(
                    {
                      code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
                    },
                    <Table dataSet={sendDrawerTableDs} columns={sendColumns} />,
                  )}
                </Panel>
              </Collapse>
              <Collapse bordered={false} defaultActiveKey={['receiveDetail']}>
                <Panel
                  header={intl.get(`${modelPrompt}.receive.detail`).d('接收明细')}
                  key="receiveDetail"
                >
                  {customizeTable(
                    {
                      code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
                    },
                    <Table dataSet={receiveDrawerTableDs} columns={receiveColumns} />,
                  )}
                </Panel>
              </Collapse>
            </>
          )}
        </>
      ),
      footer: (
        <Button
          style={{ float: 'right' }}
          onClick={() => {
            _copyDrawer.close();
          }}
        >
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 打开指定物料批页面
  const handleAppointMaterialLot = async record => {
    const lineIndex=record.index;
    lineTableDs.setQueryParameter('instructionDocId', headerTableDs?.current?.toData()?.instructionDocId);
    let data = []
    lineTableDs.query().then(res=>{
      data=res.rows
      lineTableDs.loadData(data);
      const lineData=data[lineIndex] as any;
      if(!(!['CANCEL', 'CLOSED', 'COMPLETED'].includes(lineData.statusCode) && ["MATERIAL_LOT" , ''].includes(lineData.fromIdentifyType))){
        return false;
      }
      const recordData = record.toData();
      const appointProps = {
        instructionDocId: headerTableDs?.current?.toData().instructionDocId,
        instructionDocType: headerTableDs?.current?.toData().instructionDocType,
        instructionId: recordData.instructionId,
        instructionDocLineId: recordData.instructionDocLineId,
        materialId: recordData.materialId,
        revisionCode: recordData.revisionCode,
        siteId: recordData.toSiteId,
        locatorId: recordData.fromLocatorId || recordData.fromWareHouseId,
        ownerType: recordData.fromOwnerType,
        ownerId: recordData.fromOwnerId,
        toleranceFlag: recordData.toleranceFlag,
        toleranceType: recordData.toleranceType,
        toleranceMaxValue: recordData.toleranceMaxValue,
        toleranceMinValue: recordData.toleranceMinValue,
        quantity: recordData.requiredQty,
      }
      if (appointProps.instructionId) {
        Modal.open({
          title: intl.get(`${modelPrompt}.AppointMaterialLotPage`).d('指定物料批'),
          key: Modal.key(),
          className: 'hmes-style-modal',
          style: {
            width: 1000,
          },
          maskClosable: true,
          destroyOnClose: true,
          drawer: true,
          closable: true,
          okButton: false,
          cancelButton: false,
          children: (
            <AppointMaterialLotPage
              appointProps={appointProps}
              // customizeTable={customizeTable}
            />
          ),
        });
      }
    })
  };

  const headerTableColumns: ColumnProps[] = [
    {
      name: 'instructionDocNum',
      lock: ColumnLock.left,
      width: 180,
      renderer: ({ record, value }) => {
        if (record?.get('instructionDocStatus') === 'RELEASED') {
          return (
            <a
              onClick={() => {
                headerTableDs.batchUnSelect(headerTableDs.selected);
                props.history.push(
                  `/hwms/in-library/send-receive-doc-standard/detail/${record?.get('instructionDocId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        }
        return value;
      },
    },
    { name: 'instructionDocTypeDesc', width: 170 },
    { name: 'sourceSystem' },
    { name: 'instructionDocStatusDesc' },
    { name: 'fromSiteCode', width: 150 },
    { name: 'toSiteCode', width: 150 },
    {
      name: 'demandTime',
      width: 150,
      align: ColumnAlign.center,
    },
    { name: 'printTimes' },
    { name: 'createByName' },
    {
      name: 'createDate',
      width: 150,
      align: ColumnAlign.center,
    },
    { name: 'remark' },
  ];

  const lineTableColumns: ColumnProps[] = [
    { name: 'lineNumber', lock: ColumnLock.left, align: ColumnAlign.left },
    // {
    //   name: 'identifyType',
    //   lock: ColumnLock.left,
    //   width: 120,
    //   // renderer: ({ value }) => {
    //   //   if (value === 'LOT' || value === 'MAT') {
    //   //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
    //   //   } if (value === 'MATERIAL_LOT') {
    //   //     return intl.get('tarzan.common.physicalManage').d('实物管理');
    //   //   }
    //   //   return '';
    //   // },
    // },
    { name: 'materialCode', lock: ColumnLock.left, width: 150 },
    { name: 'revisionCode', lock: ColumnLock.left },
    {
      name: 'materialName',
      width: 150,
    },
    { name: 'requiredQty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'statusDesc' },
    {
      name: 'fromIdentifyType',
      width: 120,
    },
    {
      name: 'toIdentifyType',
      width: 120,
    },
    { name: 'firstExecutedQty' },
    { name: 'secondExecutedQty' },
    { name: 'fromSiteCode', width: 150 },
    { name: 'fromWareHouseCode', width: 150 },
    { name: 'fromLocatorCode', width: 150 },
    { name: 'soNumber' },
    { name: 'soLineNumber', width: 150 },
    { name: 'toSiteCode', width: 150 },
    { name: 'toWareHouseCode', width: 150 },
    { name: 'toLocatorCode', width: 150 },
    {
      name: 'toleranceFlag',
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    { name: 'toleranceTypeDesc' },
    { name: 'toleranceMaxValue', align: ColumnAlign.right },
    { name: 'toleranceMinValue', align: ColumnAlign.right },
    {
      name: 'poLineId',
      lock: ColumnLock.right,
      width: 200,
      align: ColumnAlign.center,
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goLotDetail(record)}>
              {intl.get(`${modelPrompt}.details`).d('明细')}
            </a>
            <a
              disabled={!(!['CANCEL', 'CLOSED', 'COMPLETED', '1_COMPLETED'].includes(record?.toData().statusCode) && ["MATERIAL_LOT" , ''].includes(record?.toData().fromIdentifyType))}
              onClick={() => handleAppointMaterialLot(record)}
            >
              {intl.get(`${modelPrompt}.create.materialLotAppoint`).d('指定物料批')}
            </a>
            <a disabled={record?.get('cancelFlag') !== 'Y'} onClick={() => clickDeleteLine(record)}>
              {intl.get(`${modelPrompt}.option.lineCancel`).d('行取消')}
            </a>
          </span>
        );
      },
    },
  ];

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item disabled={isAllEqualWithKeyWord(selectedStatus, 'CANCEL')} key="CANCEL">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CANCEL')}>
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={isAllEqualWithKeyWord(selectedStatus, 'CLOSED')} key="CLOSED">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CLOSED')}>
          {intl.get(`${modelPrompt}.button.close`).d('关闭')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const onFieldEnterDown = () => {
    headerTableDs.query(props.headerTableDs.currentPage);
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.sendReceiveDoc`).d('库存调拨平台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => props.history.push(`/hwms/in-library/send-receive-doc-standard/detail/create`)}
          permissionList={[
            {
              code: `${path}.button.create`,
              type: 'button',
              meaning: '列表页-新建按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.create`).d('创建单据')}
        </PermissionButton>
        <Dropdown
          overlay={menu}
          disabled={
            selectedStatus.length === 0 ||
            (isAllEqualWithKeyWord(selectedStatus, 'CLOSED') &&
              isAllEqualWithKeyWord(selectedStatus, 'CANCEL'))
          }
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={
              selectedStatus.length === 0 ||
              (isAllEqualWithKeyWord(selectedStatus, 'CLOSED') &&
                isAllEqualWithKeyWord(selectedStatus, 'CANCEL'))
            }
            loading={changeStatusLoading}
            permissionList={[
              {
                code: `${path}.button.changeStatus`,
                type: 'button',
                meaning: '列表页-状态变更按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <TemplatePrintButton
          disabled={!selectedInstructionDocIds.length}
          printButtonCode='SEND_RECEIVE_DOC'
          printParams={{ docId: selectedInstructionDocIds.join(',') }}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.HEAD`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            searchCode="kcdbpt1"
            customizedCode="kcdbpt1"
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            showCachedSelection={false}
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
          <Panel header={intl.get(`${modelPrompt}.title.line`).d('行信息')} key="basicInfo">
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.LINE`,
              },
              <Table
                customizedCode="kcdbpt2"
                className={styles['expand-table']}
                dataSet={lineTableDs}
                queryBar={TableQueryBarType.bar}
                highLightRow={false}
                columns={lineTableColumns}
                onRow={({ record }) => {
                  return {
                    onClick: () => {
                      lineTableDs.select(record);
                    },
                  };
                }}
              />,
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.inLibrary.sendReceiveDoc', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
    ],
  }),
)(SendReceiveList);
