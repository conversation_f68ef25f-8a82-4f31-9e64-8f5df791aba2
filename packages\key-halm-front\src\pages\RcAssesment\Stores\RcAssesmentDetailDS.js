/**
 * @since 2020/08/06
 * <AUTHOR> <kejie.lu@hand-china>
 */
import intl from 'utils/intl';
import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const organizationId = getCurrentOrganizationId();

function detailFormDS() {
  const promptCode = 'amtc.rcAssesment.model.rcAssesment';
  return {
    autoQuery: false,
    selection: false,
    primaryKey: 'faultSetId',
    paging: false,
    fields: [
      {
        name: 'faultSetLov',
        type: 'object',
        label: intl.get(`${promptCode}.faultSetName`).d('故障体系'),
        required: true,
        ignore: 'always',
        lovCode: 'AMTC.FAULTDEFECTS',
        lovPara: { organizationId },
      },
      {
        name: 'faultSetId',
        type: 'number',
        bind: 'faultSetLov.faultSetId',
      },
      {
        name: 'faultSetName',
        type: 'string',
        label: intl.get(`${promptCode}.faultSetName`).d('故障体系'),
        required: true,
        bind: 'faultSetLov.faultSetName',
      },
      {
        name: 'evalItemName',
        type: 'intl',
        label: intl.get(`${promptCode}.evalItemName`).d('字典名称'),
        required: true,
        maxLength: 40,
      },
      {
        name: 'evalItemCode',
        type: 'string',
        label: intl.get(`${promptCode}.evalItemCode`).d('字典代码'),
        required: true,
        maxLength: 40,
      },
      {
        name: 'enabledFlag',
        type: 'number',
        label: intl.get(`${promptCode}.enabledFlag`).d('启用'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      {
        name: 'description',
        type: 'intl',
        label: intl.get(`${promptCode}.description`).d('详细说明'),
        maxLength: 240,
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          data,
          url: `${HALM_MTC}/v1/${organizationId}/eval-item/${data.evalItemId}`,
          method: 'GET',
        };
      },
      update: ({ data }) => {
        return {
          data: data[0],
          url: `${HALM_MTC}/v1/${organizationId}/eval-item`,
          method: 'PUT',
        };
      },
      submit: ({ data }) => {
        return {
          data: data[0],
          url: `${HALM_MTC}/v1/${organizationId}/eval-item`,
          method: 'POST',
        };
      },
    },
  };
}

function objTableDS() {
  const promptCode = 'amtc.rcAssesment.model.rcAssesment';
  return {
    autoQuery: false,
    selection: false,
    primaryKey: 'asmtObjectsId',
    dataKey: 'content',
    fields: [
      {
        name: 'objectTypeName',
        type: 'object',
        label: intl.get(`${promptCode}.objectTypeName`).d('对象'),
        ignore: 'always',
        required: true,
        multiple: true,
        dynamicProps: {
          lovCode: ({ record }) => {
            const parentTypeCode = record.get('parentTypeCode');
            if (parentTypeCode === 'AOS_Assets') {
              return 'AAFM.ASSET_SET';
            } else if (parentTypeCode === 'HAT_Assets') {
              return 'AAFM.ASSET';
            } else if (parentTypeCode === 'HAT_Assets_Locations') {
              return 'AMDM.LOCATIONS';
            }
          },
          lovPara: ({ record }) => {
            const parentTypeCode = record.get('parentTypeCode');
            const param = { organizationId };
            if (parentTypeCode === 'HAT_Assets') {
              return { ...param, aclFlag: 1 };
            } else if (parentTypeCode === 'AOS_Assets') {
              return { ...param, effectiveAssetsClassFlag: 1, enabledFlag: 1 };
            } else {
              return param;
            }
          },
        },
      },
      {
        name: 'parentTypeCode',
        type: 'string',
        label: intl.get(`${promptCode}.parentTypeCode`).d('对象类型'),
        lookupCode: 'AMTC.FAULTDEFECT_OBJECTTYPE',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${promptCode}.description`).d('详细说明'),
      },
    ],
    transport: {
      destroy: ({ data }) => {
        return {
          data: data[0],
          url: `${HALM_MTC}/v1/${organizationId}/asmt-objects`,
          method: 'DELETE',
        };
      },
      read: ({ data }) => {
        return {
          data,
          url: `${HALM_MTC}/v1/${organizationId}/eval-item/evaluate-objects/${data.evalItemId}`,
          method: 'GET',
        };
      },
      submit: ({ data }) => {
        return {
          data,
          url: `${HALM_MTC}/v1/${organizationId}/asmt-objects`,
          method: 'POST',
        };
      },
    },
    events: {
      update: ({ name, value, record }) => {
        if (name === 'objectTypeName') {
          let parentId = null;
          let parentName = null;
          if (value) {
            const parentTypeCode = record.get('parentTypeCode');
            // const { assetSetId, assetId, assetSetName, assetDesc } = value;
            const assetSetIds = value.map(item => item.assetSetId)
            const assetIds = value.map(item => item.assetId)
            const assetSetNames = value.map(item => item.assetSetName)
            const assetDescs = value.map(item => item.assetDesc)
            if (parentTypeCode === 'AOS_Assets') {
              parentId = assetSetIds;
              parentName = assetSetNames;
            } else if (parentTypeCode === 'HAT_Assets') {
              parentId = assetIds;
              parentName = assetDescs;
            }
          }
          record.set('parentIds', parentId);
          record.set('parentNames', parentName);
        } else if (name === 'parentTypeCode') {
          record.set('objectTypeName', null);
        }
      },
    },
  };
}

function codeTableDS() {
  const promptCode = 'amtc.rcAssesment.model.rcAssesment';
  return {
    autoQuery: false,
    selection: false,
    primaryKey: 'asmtCodeId',
    dataKey: 'content',
    parentField: 'parentCodeId',
    idField: 'asmtCodeId',
    expandField: 'expand',
    fields: [
      {
        name: 'expand',
        type: 'boolean',
        ignore: 'always',
      },
      {
        name: 'hierarchiesLov',
        type: 'object',
        label: intl.get(`${promptCode}.hierarchiesName`).d('值类型'),
        ignore: 'always',
        required: true,
        textField: 'hierarchiesName',
        lovCode: 'AMTC.EVALUATION_HIERARCHIES',
        // lovPare: { tenantId, faultdefectId },
      },
      {
        name: 'asmtCodeName',
        type: 'intl',
        required: true,
        label: intl.get(`${promptCode}.asmtCodeName`).d('值名称'),
        maxLength: 40,
      },
      {
        name: 'hierarchiesName',
        type: 'string',
        bind: 'hierarchiesLov.hierarchiesName',
      },
      {
        name: 'faultCode',
        type: 'string',
        label: intl.get(`${promptCode}.faultCode`).d('故障代码'),
        maxLength: 40,
        required: true,
        dynamicProps: {
          disabled: ({ record }) => {
            return !record.getState('add');
          },
        },
      },
      {
        name: 'description',
        type: 'intl',
        label: intl.get(`${promptCode}.description`).d('详细说明'),
      },
      {
        name: 'add',
        type: 'boolean',
        ignore: 'always',
      },
      {
        name: 'editing',
        type: 'boolean',
        ignore: 'always',
      },
    ],
    transport: {
      destroy: ({ data }) => {
        return {
          data: data[0],
          url: `${HALM_MTC}/v1/${organizationId}/asmt-codes`,
          method: 'DELETE',
        };
      },
      read: ({ data }) => {
        return {
          data,
          url: `${HALM_MTC}/v1/${organizationId}/asmt-codes/tree/${data.evalItemId}`,
          method: 'GET',
        };
      },
      submit: ({ data }) => {
        return {
          data,
          url: `${HALM_MTC}/v1/${organizationId}/asmt-codes`,
          method: 'POST',
        };
      },
    },
  };
}

export { detailFormDS, objTableDS, codeTableDS };
