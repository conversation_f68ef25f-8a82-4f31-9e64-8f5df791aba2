import React, { useEffect, useState } from 'react';
import { DataSet, Spin, Button, Form, TextField, Select, Lov, DateTimePicker, Attachment, TextArea } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui'
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';

import { useRequest } from '@components/tarzan-hooks';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import {
  GetDefaultSite,
  SaveProblemInfo,
  SubmitInfo,
  StartAnalyzingInfo,
  DetailInfo,
} from '../services';
import { headFormDS } from '../stores/datailFormDS';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const Panel = Collapse.Panel;

const AnalysisManagementPlatformDetail = (props) => {
  const {
    match: { path, params },
    formDs,
    history,
  } = props;

  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });
  const { run: saveProblemInfo, loading: saveLoading } = useRequest(SaveProblemInfo(), {
    manual: true,
  });
  const { run: submitInfo, loading: submitLoading } = useRequest(SubmitInfo(), {
    manual: true,
  });
  const { run: startAnalyzingInfo, loading: startAnalyzingLoading } = useRequest(StartAnalyzingInfo(), {
    manual: true,
  });
  const { run: detailInfo, loading: detailLoading } = useRequest(DetailInfo(), {
    manual: true,
  });

  const kid = params.id;
  const [canEdit, setCanEdit] = useState(false);
  const [msaStatus, setMsaStatus] = useState('NEW');

  useEffect(() => {
    formDs.setState('id', kid);
    if (kid === "create") {
      getDefaultSite({
        onSuccess: res => {
          if (res?.siteId) {
            formDs.current.set('siteLov', res);
          }
        },
      });
      setCanEdit(true)
    } else {
      detainQuery()
      setCanEdit(false);
    }
  }, [kid])

  const detainQuery = () => {
    detailInfo({
      params: {
        msaTaskId: kid,
      },
      onSuccess: (res) => {
        if (res) {
          setMsaStatus(res.msaStatus)
          formDs.loadData([res])
        }
      },
    })
  }

  const handleSave = async () => {
    const validate = await formDs.validate();
    if (!validate) {
      return;
    }
    saveProblemInfo({
      params: {
        headerInfo: { ...formDs.toData()[0] },
      },
      onSuccess: (res) => {
        if (res === Number(kid)) {
          detainQuery()
          setCanEdit(false);
        } else {
          history.push(`/hwms/msa-analysis-management-platform/detail/${res}`);
        }
      },
    });
  }

  const attachmentProps: any = {
    bucketName: 'qms',
    bucketDirectory: 'msa-analysis-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    readOnly: !canEdit,
  };

  const handleSubmit = async () => {
    const validate = await formDs.validate();
    if (!validate) {
      notification.error({ message: intl.get(`${modelPrompt}.error.requiredItemEmpty`).d('有必输值未输！') })
      return;
    }
    submitInfo({
      params: {
        headerInfo: {
          ...formDs.toData()[0],
        },
      },
      onSuccess: (res) => {
        if (res === Number(kid)) {
          detainQuery()
          setCanEdit(false);
        } else {
          history.push(`/hwms/msa-analysis-management-platform/detail/${res}`);
        }
      },
    })
  }

  const handleStartAnalysis = () => {
    startAnalyzingInfo({
      params: {
        msaTaskIds: [formDs.current.get('msaTaskId')],
      },
      onSuccess: () => {
        // detainQuery()
        history.push(`/hwms/msa-analysis-management-platform/analysisDetail/${formDs.current.get('msaTaskId')}`);
      },
    })
  }

  const handlePlanstartTime = (value) => {
    const newDate = new Date(value._i)
    const date = newDate.setDate(newDate.getDate() + formDs.current.get('completeTimeLimit'));
    const time = new Date(date);
    formDs.current.set('planEndTime', time);

  }

  const handleChangeTool = value => {
    if (!value) {
      formDs.current?.set('processLov', undefined);
    }
    const {
      processId,
      processName,
    } = value;
    formDs.current?.set('processLov', {
      workcellId: processId,
      workcellName: processName,
    });
  }

  return (
    <div className="hmes-style">
      <Spin
        dataSet={formDs}
        spinning={
          siteLoading ||
          saveLoading ||
          detailLoading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.title.analysisTask`).d('MSA分析管理平台/MSA分析任务')}
          backPath="/hwms/msa-analysis-management-platform/list"
        >
          {canEdit ? (
            <>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                onClick={handleSave}
                permissionList={[
                  {
                    code: `${path}.button.save`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </PermissionButton>
              {canEdit && ['NEW', 'TO_IMPROVE'].includes(msaStatus) && (
                <Button onClick={handleSubmit} loading={submitLoading}>
                  {intl.get(`${modelPrompt}.button.submit`).d('提交')}
                </Button>
              )}
              <Button onClick={
                () => {
                  if (kid === 'create') {
                    history.push('/hwms/msa-analysis-management-platform/list')
                  } else {
                    setCanEdit(false);
                  }
                }
              }
              >
                {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              onClick={() => { setCanEdit(true) }}
              disabled={!['NEW', 'TO_ANALYZE', 'TO_IMPROVE'].includes(msaStatus)}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
          {!canEdit && ['NEW', 'TO_IMPROVE'].includes(msaStatus) && (
            <Button onClick={handleSubmit} loading={submitLoading}>
              {intl.get(`${modelPrompt}.button.submit`).d('提交')}
            </Button>
          )}
          {!canEdit && msaStatus === 'TO_ANALYZE' && (
            <Button onClick={handleStartAnalysis} loading={startAnalyzingLoading}>
              {intl.get(`${modelPrompt}.button.startTheAnalysis`).d('开始分析')}
            </Button>
          )}
          {!canEdit && ['ANALYZING', 'COMPLETED', 'IMPROVING'].includes(msaStatus) && (
            <Button
              onClick={() => {
                history.push(`/hwms/msa-analysis-management-platform/analysisDetail/${formDs.current.get('msaTaskId')}`);
              }}
            >
              {intl.get(`${modelPrompt}.button.analysisDetails`).d('分析详情')}
            </Button>
          )}
        </Header>
        <Content>
          <Collapse defaultActiveKey={["headData"]}>
            <Panel
              header={intl.get(`${modelPrompt}.headData`).d('头数据')}
              key="headData"
            >
              <Form disabled={!canEdit} dataSet={formDs} columns={3}>
                <TextField name='msaCode' />
                <Select name='msaStatus' />
                <Lov name='siteLov' />
                <Select name='msaType' />
                <Lov name='modelLov' />
                <TextField name='modelName' />
                <TextField name='qualityCharacteristic' />
                <TextField name='speciesName' />
                <TextField name='msaAnalysisMethodDesc' />
                <TextField name='completeTimeLimit' />
                <Lov name='toolCodeLov' onChange={handleChangeTool} />
                <Lov name='processLov' />
                <TextField name='responName' />
                <Select name='onlineFlag' />
                <Select name='specialCharacteristic' />
                <Select name='projectName' />
                <Select name='projectStage' />
                <DateTimePicker name='planStartTime' onChange={handlePlanstartTime} />
                <DateTimePicker name='planEndTime' />
                <Lov name='analyzedByLov' />
                <Lov name='assistantByLov' />
                <Attachment name='sipFileUuid' {...attachmentProps} />
              </Form>
              <Form disabled={!canEdit} dataSet={formDs}>
                <TextField name='remark' />
              </Form>
              {formDs.current?.get('msaType') === '2' && (
                <>
                  <Form disabled={!canEdit} dataSet={formDs} columns={3}>
                    <TextField name='sourceMsaTaskCode' />
                    <Lov name='improveLov' />
                    <Attachment name='improvementFileUuid' {...attachmentProps} />
                  </Form>
                  <Form disabled={!canEdit} dataSet={formDs}>
                    <TextArea name='cause' placeholder={intl.get(`${modelPrompt}.placeholder.pleaseInput`).d('请输入')} />
                    <TextArea name='effectVerification' placeholder={intl.get(`${modelPrompt}.placeholder.pleaseInput`).d('请输入')} />
                  </Form>
                </>
              )}
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const formDs = new DataSet({
        ...headFormDS(),
      });
      return {
        formDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(AnalysisManagementPlatformDetail),
);
