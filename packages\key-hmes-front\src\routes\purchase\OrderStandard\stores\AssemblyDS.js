/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2021-12-14 16:12:24
 * @LastEditTime: 2023-05-18 15:22:58
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.purchase.order';
const tenantId = getCurrentOrganizationId();

const assemblyDS = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows',
  selection: false,
  paging: false,
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-po-component/component/property/list?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_COMPONENT.QUERY`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
    },
    {
      name: 'materialName',
      type: FieldType.string,
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
    },
    {
      name: 'materialTypeName',
      type: FieldType.string,
    },
    {
      name: 'uomCode',
      type: FieldType.string,
    },
    {
      name: 'componentQty',
      type: FieldType.string,
    },
    {
      name: 'shipQty',
      type: FieldType.string,
    },
    {
      name: 'remaiInventoryQty',
      type: FieldType.string,
    },
    {
      name: 'deleteFlag',
      type: FieldType.string,
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
    },
    {
      name: 'option',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.option`).d('操作'),
    },
  ],
});

export { assemblyDS };
