import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

export function OverviewAndTodoListQueryInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/all/page-ui`,
    method: 'GET',
  };
}

export function PersonalTodoQueryInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/person/stay-do`,
    method: 'GET',
  };
}

export function UnderwayQueryInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/person/doing`,
    method: 'GET',
  };
}

export function ClosedIssueQueryInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/person/closed`,
    method: 'GET',
  };
}

export function LayoutSaveInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/hme-user-card-rels`,
    method: 'POST',
  }
}

export function LayoutQueryInfo() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/hme-user-card-rels`,
    method: 'GET',
  }
}