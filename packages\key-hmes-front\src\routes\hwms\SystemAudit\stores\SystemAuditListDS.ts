/**
 * @Description: 体系审核管理维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import moment from 'moment/moment';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.systemAudit';
const tenantId = getCurrentOrganizationId();

// 列表-ds
const listTableDS = (): DataSetProps => ({
  autoLocateFirst: true,
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: false,
  cacheSelection: true,
  primaryKey: 'sysReviewPlanId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: ({ data }) => {
      const _data = { ...data };
      const keyList = [
        'scheduleDateFrom',
        'scheduleDateTo',
        'reviewDateFrom',
        'reviewDateTo',
        'reviewDateEndFrom',
        'reviewDateEndTo',
      ];
      keyList.forEach(key => {
        if (_data[key]) {
          _data[key] = moment(_data[key]).format('YYYY-MM-DD');
        }
      });

      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-sys-review-platform/header/ui`,
        method: 'get',
        data: _data,
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewPlanCode`).d('计划编号'),
      name: 'sysReviewPlanCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewTitle`).d('审核标题'),
      name: 'sysReviewTitle',
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.SYS_REVIEW_PLAN_STATUS',
      name: 'sysReviewPlanStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewType`).d('审核类型'),
      name: 'sysReviewType',
      lookupCode: 'YP.QIS.SYS_REVIEW_TYPE',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.scheduleDateFrom`).d('计划时间从'),
      name: 'scheduleDateFrom',
      max: 'scheduleDateTo',
      format: 'YYYY-MM-DD',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.scheduleDateTo`).d('计划时间至'),
      name: 'scheduleDateTo',
      min: 'scheduleDateFrom',
      format: 'YYYY-MM-DD',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.reviewDateFrom`).d('实际开始时间从'),
      name: 'reviewDateFrom',
      max: 'reviewDateTo',
      format: 'YYYY-MM-DD',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.reviewDateTo`).d('实际开始时间至'),
      name: 'reviewDateTo',
      min: 'reviewDateFrom',
      format: 'YYYY-MM-DD',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.reviewDateEndFrom`).d('实际完成时间从'),
      name: 'reviewDateEndFrom',
      max: 'reviewDateEndTo',
      format: 'YYYY-MM-DD',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.reviewDateEndTo`).d('实际完成时间至'),
      name: 'reviewDateEndTo',
      min: 'reviewDateEndFrom',
      format: 'YYYY-MM-DD',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewPlanCode`).d('计划编号'),
      name: 'sysReviewPlanCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewTitle`).d('审核标题'),
      name: 'sysReviewTitle',
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.SYS_REVIEW_PLAN_STATUS',
      name: 'sysReviewPlanStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewType`).d('审核类型'),
      name: 'sysReviewType',
      lookupCode: 'YP.QIS.SYS_REVIEW_TYPE',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.scheduleDateStart`).d('计划开始时间'),
      name: 'scheduleDateStart',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.scheduleDateEnd`).d('计划结束时间'),
      name: 'scheduleDateEnd',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.reviewDateStart`).d('实际开始时间'),
      name: 'reviewDateStart',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.reviewDateEnd`).d('实际结束时间'),
      name: 'reviewDateEnd',
    },
  ],
});

export { listTableDS };
