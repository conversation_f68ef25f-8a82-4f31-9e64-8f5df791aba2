import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const Host = `${BASIC.HMES_BASIC}`;
// const Host = '/mes-41300'
const tenantId = getCurrentOrganizationId();
// 详情页-查询
export function QueryDetail() {
  return {
    url: `${Host}/v1/${tenantId}/hme-markings/query/detail`,
    method: 'GET',
  };
}
export function SaveDetail() {
  return {
    url: `${Host}/v1/${tenantId}/hme-markings/save`,
    method: 'POST',
  };
}
export function SubmitDetail() {
  return {
    url: `${Host}/v1/${tenantId}/hme-markings/submit/ui`,
    method: 'POST',
  };
}
// 主页面取消
export function CancelList() {
  return {
    url: `${Host}/v1/${tenantId}/hme-markings/cancel/ui`,
    method: 'POST',
  };
}
