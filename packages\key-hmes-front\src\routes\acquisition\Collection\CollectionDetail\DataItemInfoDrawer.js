/**
 * @Description: 数据收集组维护-数据项信息-新建/编辑抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-01-25 15:34:54
 * @LastEditTime: 2022-07-11 17:40:23
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { Form, TextField, Switch, NumberField, Lov, Select } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import NumberComponent from '../components/NumberComponent';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

export default ({ record, edit, trueNumberDs, falseNumberDs, handleChangeLov }) => {
  const [trueValueDisabled, setTrueDisabled] = useState(true);
  const [falseValueDisabled, setFalseDisabled] = useState(true);
  useEffect(() => {
    setTrueDisabled(false);
    setFalseDisabled(false);
    handleUpdateDisabled()
  })
  const handleUpdateDisabled = () => {
    const { valueType } = record?.toData();
    const trueValue = trueNumberDs?.current?.get('multipleValue');
    const falseValue = falseNumberDs?.current?.get('multipleValue');
    const _isFalseValueEdit = falseNumberDs.length > 1 || verifyHasValue(falseValue);
    if (['VALUE'].includes(valueType) && _isFalseValueEdit) {
      falseNumberDs?.getField('ncCodeLov')?.set('required', true);
    } else {
      falseNumberDs?.getField('ncCodeLov')?.set('required', false);
    }
    if (['VALUE'].includes(valueType) && verifyHasValue(trueValue)) {
      record?.getField('defaultNcCodeLov')?.set('required', true);
    } else if (['DECISION_VALUE'].includes(record?.get('valueType'))
      && (record?.get('trueValue') || record?.get('falseValue'))) {
      record?.getField('defaultNcCodeLov')?.set('required', true);
    } else {
      record?.getField('defaultNcCodeLov')?.set('required', false);
    }
    // const { valueType } = record.toData();
    // const trueValue = trueNumberDs.current?.get('multipleValue');
    // const falseValue = falseNumberDs.current?.get('multipleValue');
    // const _isTrueValueEdit = trueNumberDs.length > 1 || verifyHasValue(trueValue);
    // const _isFalseValueEdit = falseNumberDs.length > 1 || verifyHasValue(falseValue);
    // // 数据类型为“数值”： 符合值与不符合值可输入，且二者选其一，预警值只有在有符合值时才可输入
    // if (['VALUE'].includes(valueType)) {
    //   setTrueDisabled(_isFalseValueEdit);
    //   setFalseDisabled(_isTrueValueEdit);
    // } else {
    //   setTrueDisabled(true);
    //   setFalseDisabled(true);
    // }
  };
  // 根据值类型不同使用不同的判空方法
  const verifyHasValue = value => {
    if (value instanceof Object) {
      // 数据类型为数值-区间
      return value?.leftValue || value?.rightValue;
    }
    // 数据类型为数值-单值
    return value;
  };
  const handleChangeTag = value => {
    handleUpdateDisabled()
    if (value?.valueType === 'VALUE') {
      setTrueDisabled(false);
      setFalseDisabled(false);
    } else {
      setTrueDisabled(true);
      setFalseDisabled(true);
    }
    if (value) {
      console.log(value.originOperationId)
      console.log(value.originOperationName)
      record.set('allowUpdateFlag', value.allowUpdateFlag || null);
      record.set('valueAllowMissing', value.valueAllowMissing || null);
      record.set('displayValueFlag', value.displayValueFlag || null);
      record.set('collectionMethod', value.collectionMethod || null);
      record.set('trueValue', value.trueValue || null);
      record.set('falseValue', value.falseValue || null);
      record.set('minimumValue', value.minimumValue || null);
      record.set('maximalValue', value.maximalValue || null);
      record.set('uomCode', value.uomCode || null);
      record.set('uomId', value.uomId || null);
      // record.set('unit', value.unit || null);
      record.set('mandatoryNum', value.mandatoryNum || null);
      record.set('optionalNum', value.optionalNum || null);
      record.set('valueList', value.valueList || null);
      record.set('valueListDesc', value.valueListDesc || null);
      record.set('dateFormat', value.dateFormat || null);
      record.set('specialRecordFlag', value.specialRecordFlag || null);
      record.set('originOperationLov', {
        operationId: value.originOperationId,
        operationName: value.originOperationName,
      } || null);
      record.set('modifyFlag', value.modifyFlag || null);
      record.set('defaultNcCodeId', value.defaultNcCodeId || null);
      record.set('defaultNcCode', value.defaultNcCode || null);
      handleChangeLov(value.trueValueList ? value.trueValueList : [], value?.falseValueList ? value?.falseValueList : [])
    } else {
      record.set('allowUpdateFlag', null);
      record.set('valueAllowMissing', null);
      record.set('displayValueFlag', null);
      record.set('collectionMethod', null);
      record.set('trueValue', null);
      record.set('falseValue', null);
      record.set('minimumValue', null);
      record.set('maximalValue', null);
      record.set('uomCode', null);
      record.set('uomId', null);
      // record.set('unit', null);
      record.set('mandatoryNum', null);
      record.set('optionalNum', null);
      record.set('valueList', null);
      record.set('valueListDesc', null);
      record.set('dateFormat', null);
      record.set('specialRecordFlag', null);
      record.set('originOperationLov', null);
      record.set('modifyFlag', null);
      record.set('defaultNcCodeLov', {});
      record.set('defaultNcCodeId', null);
      record.set('defaultNcCode', null);
      handleChangeLov([], [])
    }

  };

  const handleChangeUom = value => {
    if (value) {
      record.set('tagLov', { ...record.get('tagLov'), ...value });
    } else {
      record.set('tagLov', {
        ...record.get('tagLov'),
        ...{ uomCode: null, uomId: null, uomDesc: null },
      });
    }
  };

  const handleChangeValueList = value => {
    record.set('valueListDesc', value || null);
  };

  const handleChangeText = () => {
    if (record.toData().falseValue || record.toData().trueValue) {
      record?.getField('defaultNcCodeLov')?.set('required', true);
    } else {
      record?.getField('defaultNcCodeLov')?.set('required', false);
    }
  }

  return (
    <Form record={record} columns={2} disabled={edit} labelWidth={130}>
      <NumberField name="serialNumber" />
      <Lov name="tagLov" onChange={handleChangeTag} />
      <TextField name="tagDescription" />
      <Select name="valueTypeDesc" />
      <Switch name="allowUpdateFlag" />
      <Switch name="valueAllowMissing" />
      <Switch name="enableFlag" />
      <Switch name="displayValueFlag" />
      <Select name="collectionMethod" />
      <Lov name="uomLov" onChange={handleChangeUom} />
      {/* <NumberField name="minimumValue" />
      <NumberField name="maximalValue" /> */}
      {record.get('valueType') === 'VALUE' ?
        <NumberComponent
          showStandard
          title={intl.get(`${modelPrompt}.synchronizeData`).d('符合值')}
          name="trueValue"
          parentDs={record}
          dataSet={trueNumberDs}
          disabled={trueValueDisabled}
          canEdit={!edit}
          handleUpdateDisabled={handleUpdateDisabled}
        /> : <TextField name="trueValue" onChange={handleChangeText} />}
      {record.get('valueType') === 'VALUE' ?
        <NumberComponent
          showNcCode
          title={intl.get(`${modelPrompt}.synchronizeData`).d('不符合值')}
          name="falseValue"
          parentDs={record}
          dataSet={falseNumberDs}
          disabled={falseValueDisabled}
          canEdit={!edit}
          handleUpdateDisabled={handleUpdateDisabled}
        /> : <TextField name="falseValue" onChange={handleChangeText} />}
      {/* <TextField name="trueValue" />
      <TextField name="falseValue" /> */}
      {/* <NumberField name="mandatoryNum" min={0} step={1} />
      <NumberField name="optionalNum" min={0} step={1} /> */}
      <TextField name="valueList" onChange={handleChangeValueList} />
      <Select name="dateFormat" />
      <Lov name="defaultNcCodeLov" />
      <Switch name="specialRecordFlag" />
      <Lov name="originOperationLov" />
      <Switch name="modifyFlag" />
    </Form>
  );
};
