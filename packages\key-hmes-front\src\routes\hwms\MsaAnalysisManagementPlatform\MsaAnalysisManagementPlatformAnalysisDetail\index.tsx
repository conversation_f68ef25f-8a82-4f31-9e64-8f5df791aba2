import React, { useEffect, useMemo, useState, useRef } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  TextField,
  Select,
  DateTimePicker,
  Attachment,
  Spin,
  Modal,
} from 'choerodon-ui/pro';
import JsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { <PERSON><PERSON>, <PERSON>ge, Alert, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { getCurrentUserId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { queryIdpValue } from '@services/api'
import Deflective from "./Deflective";
import TemplateDeflective from "./templateDeflective";
import Stability from "./Stability"
import TemplateStability from "./templateStability"
import GrrTabs from "./GrrTabs"
import TemplateGrr from "./templateGrr";
import Linear from "./Linear"
import TemplateLinear from "./templateLinear"
import Uniformity from "./Uniformity"
import Appearance from "./Appearance";
import SizeAppearance from "./SizeAppearance";
import CGK from "./CGK";
import TemplateUniformity from "./templateUniformity"
import TemplateAppearance from "./TemplateAppearance";
import TemplateSizeAppearance from "./TemplateSizeAppearance";
import TemplateCGK from "./TemplateCGK";
import {DocInfoDS, finishAnalyseDS} from '../stores/DetailDS';
import { GetDefaultSite, StartAnalyzingInfo, PostFinish, RefershMsa } from '../services';
import './index.modules.less';

const { TabPane } = Tabs;
const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const currentUserId = getCurrentUserId();

const MsaAnalysisManagementPlatformDetail = props => {
  const {
    match: { params },
  } = props;
  let _drawer;
  const childRef = useRef<any>(null);

  const detailDs = useMemo(
    () =>
      new DataSet({
        ...DocInfoDS(),
      }),
    [],
  );
  const finishAnalyseDs = useMemo(
    () =>
      new DataSet({
        ...finishAnalyseDS(),
      }),
    [],
  );



  const { run: getDefaultSite } = useRequest(GetDefaultSite(), {
    manual: true,
  });

  const { run: startAnalyzingInfo, loading: startAnalyzingLoading } = useRequest(StartAnalyzingInfo(), {
    manual: true,
  });

  const { run: refreshAnalyzingInfo, loading: refreshAnalyzingLoading } = useRequest(RefershMsa(), { manual: true, needPromise: true });

  const { run: postFinish, loading: postFinishLoading } = useRequest(PostFinish(), {
    manual: true,
  });

  const [loading, setLoadingStatus] = useState(false)

  const [methodArr, setMethodArr] = useState([])
  const [msaStatus, setMsaStatus] = useState('');
  const [currentUserFlag, setCurrentUserFlag] = useState(false);
  const [lineInfo, setLineInfo] = useState([])
  const [tabKey, setTabKey] = useState('STABILITY');
  const [showPdfDom, setShowPdfDom] = useState(false); // pdf打印列表的渲染。
  const kid = params.id;

  useDataSetEvent(detailDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialLov':
        record.set('productionVersionLov', {});
        record.set('defaultBomLov', {});
        record.set('defaultRouterLov', {});
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    detailDs.setQueryParameter('msaTaskId', kid);
    detailDs.query().then((res) => {
      setLineInfo(res.rows.lineInfo)
      setMsaStatus(res.rows.msaStatus)
      setCurrentUserFlag(res?.rows?.analyzedBy === currentUserId);
      if ((res.rows.lineInfo || []).length) {
        queryIdpValue('YP.QIS.MSA_ANALYSIS_METHOD').then((ress) => {
          const newArr = ress.sort(function(a,b){
            return a.orderSeq-b.orderSeq
          })
          const defaultArr = [];
          newArr.forEach((item) => {
            defaultArr.push(item.value)
          })
          setMethodArr(defaultArr);
          let minIndex = defaultArr.length - 1;
          res.rows.lineInfo.forEach((item) => {
            if (defaultArr.indexOf(item.msaAnalysisMethod) < minIndex) {
              minIndex = defaultArr.indexOf(item.msaAnalysisMethod)
            }
          })
          setTabKey(defaultArr[minIndex])
        })
      }
    });
    // 此处需修改为查询具体数据
    getDefaultSite({
      onSuccess: res => {
        if (res?.siteId) {
          detailDs.current?.set('siteObj', res);
        }
      },
    });
  }, [kid]);

  const updateHeaderInfo = () => {
    detailDs.setQueryParameter('msaTaskId', kid);
    detailDs.query().then((res) => {
      setLineInfo(res.rows.lineInfo)
      setMsaStatus(res.rows.msaStatus)
      setCurrentUserFlag(res?.rows?.analyzedBy === currentUserId);
    });
  }

  /**
   *
   * @param 判断是保存还是提交
   * @returns
   */
  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
  };

  const handleChangeActiveKey = async activeKey => {
    setTabKey(activeKey);
  }

  const lineInfoFun = (key) => {
    let haveKey = false
    lineInfo.forEach((item:any) => {
      if (item.msaAnalysisMethod === key) {
        haveKey = true
      }
    })
    return haveKey;
  }

  const msaTaskLineIdSearch = (key) => {
    let msaTaskLineId = ''
    lineInfo.forEach((item:any) => {
      if (item.msaAnalysisMethod === key) {
        msaTaskLineId = item.msaTaskLineId
      }
    })
    return msaTaskLineId;
  }

  const getGrrGroupInfo = () => {
    const _grrGroupInfo: any = [];
    lineInfo.forEach((item:any) => {
      if (item.msaAnalysisMethod === 'GRR') {
        _grrGroupInfo.push(item);
      }
    })
    return _grrGroupInfo;
  }

  const finishStatus = (key) => {
    let success = false
    lineInfo.forEach((item:any) => {
      if (item.msaAnalysisMethod === key && item.msaResult) {
        success = true
      }
    })
    if (success) {
      return 'success';
    }
    return 'warning';
  }

  const startAnalyse = () => {
    startAnalyzingInfo({
      params: {
        msaTaskIds: [kid],
      },
      onSuccess: () => {
        detailDs.query().then((res) => {
          setLineInfo(res.rows.lineInfo)
          setMsaStatus(res.rows.msaStatus)
          setCurrentUserFlag(res?.rows?.analyzedBy === currentUserId);
        });
      },
    })
  }

  const finishAnalyse = () => {
    // 校验是否所有分析页是否均已分析
    let analyse = true;
    const msaAnalysisMethodDesc = [];
    lineInfo.forEach((item:any) => {
      if (!item.msaResult) {
        analyse = false
        msaAnalysisMethodDesc.push(item.msaAnalysisMethodDesc)
      }
    })
    // 存在未分析完成的弹出弹窗
    if (!analyse) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {msaAnalysisMethodDesc.join()}{intl.get(`${modelPrompt}.tip.didNotRecordAndAnalyse`).d('未进行记录和分析，是否确定完成！')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          handleFinishAnalyse();
        }
      });
    } else {
      handleFinishAnalyse();
    }
  }

  const handleFinishAnalyse = () => {
    const projectStage = detailDs.current?.get('projectStage')
    const msaResult = Number(detailDs.current?.get('msaResult'))
    const specialCharacteristic = detailDs.current?.get('specialCharacteristic')
    if (projectStage === 'SOP' && (msaResult === 3 || msaResult ===4)) {
      drawerDetail();
    } else if (projectStage === 'PP' && specialCharacteristic === 'Y' && (msaResult === 3 || msaResult ===4) ){
      drawerDetail();
    } else if (projectStage === 'PP' && specialCharacteristic === 'N' && msaResult ===4 ) {
      drawerDetail();
    } else {
      // 不弹框，直接生成任务
      onOk('N');
    }
  }

  const drawerDetail = () => {
    _drawer = Modal.open({
      key: Modal.key(),
      destroyOnClose: true,
      style: {
        width: 500,
      },
      onOk: () => {
        onOk('Y')
      },
      children: (
        <>
          <Alert
            style={{ margin: '4px' }}
            message={intl.get(`${modelPrompt}.confirm.autoGenerateMsaTask`).d('当前MSA分析结果会自动生成一个改善型MSA分析任务，是否确认提交？')}
            type="warning"
            showIcon
          />
          <Form dataSet={finishAnalyseDs} columns={1}>
            <Lov
              name="transferResponsibleUserLov"
            />
          </Form>
        </>
      ),
    });
  }

  const onOk = (status) => {
    const data = finishAnalyseDs.toData();
    postFinish({
      params: {
        msaTaskId: kid,
        improveFlag: status,
        msaType: detailDs.current?.get('msaType'),
        responId: data.length > 0 ? data[0].responId : undefined,
      },
      onSuccess: () => {
        notification.success({});
        if(status === 'Y') {
          _drawer.close();
        }
        detailDs.query().then((res) => {
          setLineInfo(res.rows.lineInfo)
          setMsaStatus(res.rows.msaStatus)
          setCurrentUserFlag(res?.rows?.analyzedBy === currentUserId);
        });
      },
    })
  }

  const renderTab = (item, loading) => {
    if (item === "STABILITY" && lineInfoFun("STABILITY")) {
      return (
        <TabPane disabled={loading} tab={intl.get(`${modelPrompt}.tab.stability`).d('稳定性')} key="STABILITY" countRenderer={() => <Badge status={finishStatus('STABILITY')} />}>
          <Stability msaTaskLineId={msaTaskLineIdSearch('STABILITY')} updateHeaderInfo={updateHeaderInfo} msaStatus={msaStatus} currentUserFlag={currentUserFlag} />
        </TabPane>
      )
    } if (item === "BIASEDNESS" && lineInfoFun("BIASEDNESS")) {
      return (
        <TabPane disabled={loading} tab={intl.get(`${modelPrompt}.tab.bias`).d('偏倚')} key="BIASEDNESS"  countRenderer={() => <Badge status={finishStatus('BIASEDNESS')} />}>
          <Deflective msaTaskLineId={msaTaskLineIdSearch('BIASEDNESS')} updateHeaderInfo={updateHeaderInfo} msaStatus={msaStatus} currentUserFlag={currentUserFlag} />
        </TabPane>
      )
    } if (item === "LINEARITY" && lineInfoFun("LINEARITY")) {
      return (
        <TabPane disabled={loading} tab={intl.get(`${modelPrompt}.tab.linear`).d('线性')} key="LINEARITY" countRenderer={() => <Badge status={finishStatus('LINEARITY')} />}>
          <Linear msaTaskLineId={msaTaskLineIdSearch('LINEARITY')} updateHeaderInfo={updateHeaderInfo} msaStatus={msaStatus} currentUserFlag={currentUserFlag} />
        </TabPane>
      )
    } if (item === "GRR" && lineInfoFun("GRR")) {
      return (
        <TabPane disabled={loading} tab={intl.get(`${modelPrompt}.tab.grr`).d('重复性与再现性GRR')} key="GRR" countRenderer={() => <Badge status={finishStatus('GRR')} />}>
          <GrrTabs ref={childRef} msaTaskId={kid} grrGroupInfo={getGrrGroupInfo()} updateHeaderInfo={updateHeaderInfo} msaStatus={msaStatus} currentUserFlag={currentUserFlag} />
        </TabPane>
      )
    } if (item === "CONSISTENCY" && lineInfoFun("CONSISTENCY")) {
      return (
        <TabPane disabled={loading} tab={intl.get(`${modelPrompt}.tab.consistence`).d('一致性')} key="CONSISTENCY" countRenderer={() => <Badge status={finishStatus('CONSISTENCY')} />}>
          <Uniformity
            msaTaskLineId={msaTaskLineIdSearch('CONSISTENCY')}
            updateHeaderInfo={updateHeaderInfo}
            msaStatus={msaStatus}
            currentUserFlag={currentUserFlag}
          />
        </TabPane>
      )
    } if (item === "APPEARANCE" && lineInfoFun("APPEARANCE")) {
      return (
        <TabPane disabled={loading} tab={intl.get(`${modelPrompt}.tab.appearance`).d('外观对标分析')} key="APPEARANCE" countRenderer={() => <Badge status={finishStatus('APPEARANCE')} />}>
          <Appearance
            msaTaskLineId={msaTaskLineIdSearch('APPEARANCE')}
            updateHeaderInfo={updateHeaderInfo}
            msaStatus={msaStatus}
            currentUserFlag={currentUserFlag}
          />
        </TabPane>
      )
    } if (item === "SIZE" && lineInfoFun("SIZE")) {
      return (
        <TabPane disabled={loading} tab={intl.get(`${modelPrompt}.tab.size`).d('尺寸对标分析')} key="SIZE" countRenderer={() => <Badge status={finishStatus('SIZE')} />}>
          <SizeAppearance
            msaTaskLineId={msaTaskLineIdSearch('SIZE')}
            updateHeaderInfo={updateHeaderInfo}
            msaStatus={msaStatus}
            currentUserFlag={currentUserFlag}
          />
        </TabPane>
      )
    } if (item === "CGK" && lineInfoFun("CGK")) {
      return (
        <TabPane disabled={loading} tab={intl.get(`${modelPrompt}.tab.cgk`).d('Cgk分析')} key="CGK" countRenderer={() => <Badge status={finishStatus('CGK')} />}>
          <CGK
            msaTaskLineId={msaTaskLineIdSearch('CGK')}
            updateHeaderInfo={updateHeaderInfo}
            msaStatus={msaStatus}
            currentUserFlag={currentUserFlag}
          />
        </TabPane>
      )
    }
  }
  const actionScreen = () => {
    setShowPdfDom(true);
    setLoadingStatus(true);
    setTimeout(async () => {
      const scale = window.devicePixelRatio > 1 ? window.devicePixelRatio : 2;
      // 下载尺寸 a4 纸 比例
      const pdf = new JsPDF('p', 'pt', 'a4');
      const width = document.querySelector('#pdf')!.offsetWidth;
      const height = document.querySelector('#pdf')!.offsetHeight;

      const canvas = document.createElement('canvas');
      canvas.width = width * scale;
      canvas.height = height * scale;
      const contentWidth = canvas.width-20;
      const contentHeight = canvas.height-20;

      // 一页pdf显示html页面生成的canvas高度;
      const pageHeight = contentWidth / 592.28 * 841.89;
      // 未生成pdf的html页面高度
      let leftHeight = contentHeight;
      // 页面偏移
      let position = 0;
      // a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
      const imgWidth = 595.28;
      const imgHeight = 592.28 / contentWidth * contentHeight;

      const pdfCanvas = await html2canvas(document.querySelector('#pdf'), {
        useCORS: true,
        canvas,
        scale,
        width,
        height,
        x: 0,
        y: 0,
      });
      const imgDataUrl = pdfCanvas.toDataURL();

      if (leftHeight < pageHeight) {
        pdf.addImage(imgDataUrl, 'png', 10, 10, imgWidth, imgHeight);
      } else {
        while (leftHeight > 0) {
          pdf.addImage(imgDataUrl, 'png', 10, position, imgWidth, imgHeight)
          leftHeight -= pageHeight;
          position -= 841.89;
          if (leftHeight > 0) {
            pdf.addPage();
          }
        }
      }
      let fileName = ''
      if (tabKey === 'STABILITY') {
        fileName = `${intl.get(`${modelPrompt}.button.stabilityReport`).d('稳定性报告')}.pdf`;
      } else if (tabKey === 'BIASEDNESS') {
        fileName = `${intl.get(`${modelPrompt}.button.deflectiveReport`).d('偏倚性报告')}.pdf`;
      } else if (tabKey === 'LINEARITY') {
        fileName = `${intl.get(`${modelPrompt}.button.linearReport`).d('线性报告')}.pdf`;
      } else if (tabKey === 'GRR') {
        fileName = `${intl.get(`${modelPrompt}.button.grrReport`).d('GRR报告')}.pdf`;
      } else if (tabKey === 'CONSISTENCY') {
        fileName = `${intl.get(`${modelPrompt}.button.uniformityReport`).d('一致性报告')}.pdf`;
      } else if (tabKey === 'APPEARANCE') {
        fileName = `${intl.get(`${modelPrompt}.button.appearanceReport`).d('外观对标性报告')}.pdf`;
      } else if (tabKey === 'SIZE') {
        fileName = `${intl.get(`${modelPrompt}.button.sizeAppearanceReport`).d('尺寸对标性报告')}.pdf`;
      } else if (tabKey === 'CGK') {
        fileName = `${intl.get(`${modelPrompt}.button.cgkReport`).d('CGK报告')}.pdf`;
      }

      // 导出下载
      await pdf.save(fileName);
      setShowPdfDom(false);
      setLoadingStatus(false);
    }, 2500)
  }

  const handleRefreshData = () => {
    return new Promise(async (resolve) => {
      const res = await refreshAnalyzingInfo({
        params: {
          headerInfo: detailDs.current?.toData(),
        },
      });
      if (res.success) {
        notification.success({});
        detailDs.query().then((res) => {
          setLineInfo(res.rows.lineInfo)
          setMsaStatus(res.rows.msaStatus)
          setCurrentUserFlag(res?.rows?.analyzedBy === currentUserId);
        });
        return resolve(true);
      }
      resolve(false);
    })
  }

  const getGrrTaskLineId = () => {
    if (childRef.current) {
      return childRef.current.getCurrentTaskLineInfo()?.msaTaskLineId;
    }
    return null;
  };

  const getGrrCheckLocation = () => {
    if (childRef.current) {
      return childRef.current.getCurrentTaskLineInfo()?.checkLocation;
    }
    return null;
  };

  return (
    <div className="hmes-style">
      <Spin dataSet={detailDs} spinning={ startAnalyzingLoading || postFinishLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.msaAnalysis`).d('MSA分析管理平台/MSA分析')}
          backPath="/hwms/msa-analysis-management-platform/list"
        >
          {
            ['ANALYZING'].includes(msaStatus) && <Button
              style={{ backgroundColor: 'green', color: 'white'}}
              onClick={finishAnalyse}
              disabled={!currentUserFlag}
            >
              {intl.get(`${modelPrompt}.button.completeAnalysis`).d('完成分析')}
            </Button>
          }
          <Button onClick={actionScreen} loading={loading}>
            {intl.get(`${modelPrompt}.button.exportReport`).d('分析报告导出')}
          </Button>
          <Popconfirm
            title={intl.get(`${modelPrompt}.message.confirmRefresh`).d('刷新后分析数据将清除，是否继续？')}
            onConfirm={handleRefreshData}
            okText={intl.get(`tarzan.common.label.yes`).d('是')}
            cancelText={intl.get(`tarzan.common.label.no`).d('否')}
          >
            <Button loading={refreshAnalyzingLoading} disabled={msaStatus !== 'ANALYZING' || !currentUserFlag}>
              {intl.get(`${modelPrompt}.button.refresh`).d('刷新')}
            </Button>
          </Popconfirm>
        </Header>
        <Content>
          <Form dataSet={detailDs} columns={3}>
            <TextField name="msaCode" disabled />
            <Select name="msaStatus" disabled />
            <Select name="msaType" disabled />
            <Lov name="toolModelIdObj" disabled />
            <TextField name="resolution" disabled />
            <Lov name="measureToolNumObj" disabled />
            <TextField name="modelRange" disabled />
            <DateTimePicker name="planStartTime" disabled />
            <DateTimePicker name="planEndTime" disabled />
            <TextField name="qualityCharacteristic" disabled />
            <TextField name="analyzedByDesc" disabled />
            <TextField name="msaResult" disabled />
            <Attachment name="sipFileUuid" />
          </Form>
          <Tabs
            activeKey={tabKey}
            onChange={handleChangeActiveKey}
            id='STABILITY'
          >
            {/* 对已知的五个tab进行排序 */}
            { renderTab(methodArr[0], loading) }
            { renderTab(methodArr[1], loading) }
            { renderTab(methodArr[2], loading) }
            { renderTab(methodArr[3], loading) }
            { renderTab(methodArr[4], loading) }
            { renderTab(methodArr[5], loading) }
            { renderTab(methodArr[6], loading) }
            { renderTab(methodArr[7], loading) }
          </Tabs>
          {/* 打印的模板渲染 */}
          {showPdfDom && (
            <div className="pdf-container">
              <div id="pdf">
                {
                  msaTaskLineIdSearch('STABILITY') && tabKey === 'STABILITY' &&
                  <TemplateStability
                    msaTaskLineId={msaTaskLineIdSearch('STABILITY')}
                    updateHeaderInfo={updateHeaderInfo}
                    msaStatus={msaStatus}
                    currentUserFlag={currentUserFlag}
                    detailDs={detailDs}
                  />
                }
                {
                  msaTaskLineIdSearch('BIASEDNESS') && tabKey === 'BIASEDNESS' &&
                  <TemplateDeflective
                    msaTaskLineId={msaTaskLineIdSearch('BIASEDNESS')}
                    updateHeaderInfo={updateHeaderInfo}
                    msaStatus={msaStatus}
                    currentUserFlag={currentUserFlag}
                    detailDs={detailDs}
                  />
                }
                {
                  msaTaskLineIdSearch('LINEARITY') && tabKey === 'LINEARITY' &&
                  <TemplateLinear
                    msaTaskLineId={msaTaskLineIdSearch('LINEARITY')}
                    updateHeaderInfo={updateHeaderInfo}
                    msaStatus={msaStatus}
                    currentUserFlag={currentUserFlag}
                    detailDs={detailDs}
                  />
                }
                {
                  msaTaskLineIdSearch('GRR') && tabKey === 'GRR' &&
                  <TemplateGrr
                    msaTaskLineId={getGrrTaskLineId()}
                    checkLocation={getGrrCheckLocation()}
                    updateHeaderInfo={updateHeaderInfo}
                    msaStatus={msaStatus}
                    currentUserFlag={currentUserFlag}
                    detailDs={detailDs}
                  />
                }
                {
                  msaTaskLineIdSearch('CONSISTENCY') && tabKey === 'CONSISTENCY' &&
                  <TemplateUniformity
                    msaTaskLineId={msaTaskLineIdSearch('CONSISTENCY')}
                    updateHeaderInfo={updateHeaderInfo}
                    msaStatus={msaStatus}
                    currentUserFlag={currentUserFlag}
                    detailDs={detailDs}
                  />
                }
                {
                  msaTaskLineIdSearch('APPEARANCE') && tabKey === 'APPEARANCE' &&
                  <TemplateAppearance
                    msaTaskLineId={msaTaskLineIdSearch('APPEARANCE')}
                    updateHeaderInfo={updateHeaderInfo}
                    msaStatus={msaStatus}
                    currentUserFlag={currentUserFlag}
                    detailDs={detailDs}
                  />
                }
                {
                  msaTaskLineIdSearch('SIZE') && tabKey === 'SIZE' &&
                  <TemplateSizeAppearance
                    msaTaskLineId={msaTaskLineIdSearch('SIZE')}
                    updateHeaderInfo={updateHeaderInfo}
                    msaStatus={msaStatus}
                    currentUserFlag={currentUserFlag}
                    detailDs={detailDs}
                  />
                }
                {
                  msaTaskLineIdSearch('CGK') && tabKey === 'CGK' &&
                  <TemplateCGK
                    msaTaskLineId={msaTaskLineIdSearch('CGK')}
                    updateHeaderInfo={updateHeaderInfo}
                    msaStatus={msaStatus}
                    currentUserFlag={currentUserFlag}
                    detailDs={detailDs}
                  />
                }
              </div>
            </div>
          )}
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(MsaAnalysisManagementPlatformDetail);
