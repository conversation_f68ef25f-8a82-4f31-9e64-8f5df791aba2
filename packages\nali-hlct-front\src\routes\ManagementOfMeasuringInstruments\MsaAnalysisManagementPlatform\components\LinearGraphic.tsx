/**
 * @Description: MSA分析管理平台-线性图表界面
 * @Author: <EMAIL>
 * @Date: 2023/8/24 15:42
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, TextField, TextArea } from 'choerodon-ui/pro';
import { Collapse, Row, Col } from 'choerodon-ui';
import echarts from 'echarts';
import {
  max as lodashMax,
  min as lodashMin,
  floor as lodashFloor,
  ceil as lodashCeil,
} from 'lodash';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const { Panel } = Collapse;
let linearChart; // 线性图chart

const colorList = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
];

const LinearGraphic = ({ dataSoure, analyseResultDs }) => {
  const [pageInit, setPageInit] = useState(false);
  const linearChartRef = useRef(null);

  useEffect(() => {
    if (!dataSoure.linearChartInfo?.length) {
      return;
    }
    if (!pageInit) {
      linearChart = echarts.init(linearChartRef.current);
      window.onresize = () => {
        setTimeout(() => {
          linearChart.resize();
        }, 300);
      };
    }
    handleInitLinearChart(dataSoure);
  }, [dataSoure?.data, dataSoure?.tableInfo]);

  const handleInitLinearChart = dataSource => {
    const {
      xTickLabelList,
      yDataList,
      yLowerDataList,
      yUpperDataList,
      maxValue,
      minValue,
      legendList,
    } = handleFormatLinearData(dataSource);
    const option = {
      tooltip: {
        trigger: 'axis',
      },
      color: colorList,
      legend: {
        top: '10%',
        left: 'center',
        data: legendList,
      },
      xAxis: {
        type: 'category',
        data: xTickLabelList,
        axisTick: {
          show: true,
          alignWithLabel: true,
        },
        // splitLine: {
        //   show: true,
        //   lineStyle: {
        //     color: 'rgba(0,0,0,0.03)',
        //   },
        // },
      },
      yAxis: {
        type: 'value',
        max: lodashCeil(lodashMax([maxValue, 0]) * 100) / 100,
        min: lodashFloor(lodashMin([minValue, 0]) * 100) / 100,
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.03)',
          },
        },
      },
      series: [
        {
          name: legendList[0],
          type: 'line',
          data: yUpperDataList,
          symbolSize: 8,
          smooth: true,
        },
        {
          name: legendList[1],
          type: 'line',
          data: yDataList,
          symbolSize: 8,
          smooth: true,
          markLine: {
            silent: true,
            symbol: 'none',
            data: [
              {
                name: 'y = 0',
                yAxis: 0,
                label: {
                  formatter: 'y = 0',
                  color: '#000',
                  fontSize: 10,
                },
                lineStyle: {
                  color: colorList[3],
                  width: 1.2,
                },
              },
            ],
          },
        },
        {
          name: legendList[2],
          type: 'line',
          data: yLowerDataList,
          symbolSize: 8,
          smooth: true,
        },
      ],
    };
    linearChart.setOption(option, true);
    // 初始化chart事件绑定
    if (!pageInit) {
      setPageInit(true);
    }
  };

  const handleFormatLinearData = dataSource => {
    const { linearChartInfo } = dataSource;
    const xTickLabelList: any[] = []; // x轴坐标名称
    const yDataList: any = []; // 拟合直线数据
    const yLowerDataList: any = []; // 置信区间下限数据
    const yUpperDataList: any = []; // 置信区间上限数据
    const legendList: string[] = ['置信区间上限', '拟合直线', '置信区间下限']; // 图例数据
    let maxValue;
    let minValue;
    (linearChartInfo || []).forEach(colItem => {
      const { xInfo, yInfo, yLowerInfo, yUpperInfo } = colItem;
      const _maxValue = Math.max(yInfo, yLowerInfo, yUpperInfo);
      const _minValue = Math.min(yInfo, yLowerInfo, yUpperInfo);
      if (maxValue < _maxValue || !maxValue) {
        maxValue = _maxValue;
      }
      if (minValue > _minValue || !minValue) {
        minValue = _minValue;
      }
      xTickLabelList.push(xInfo);
      yDataList.push(yInfo);
      yLowerDataList.push(yLowerInfo);
      yUpperDataList.push(yUpperInfo);
    });
    return {
      xTickLabelList,
      yDataList,
      yLowerDataList,
      yUpperDataList,
      maxValue,
      minValue,
      legendList,
    };
  };

  const RenderHelp = () => {
    const info = intl.get(`${modelPrompt}.linear.help`).d('1、曲线y=0完全处于上下限两条曲线中间；<br />2、斜率的统计量和截距的统计量均小于临界值；<br />同时满足以上条件，证明测量系统不存在线性问题');
    const descriptionList = info.split('<br />');
    return (
      <div>
        {descriptionList.map(item => (
          <p>{item}</p>
        ))}
      </div>
    );
  };

  return (
    <div>
      {Boolean(dataSoure?.tableInfo?.length) && (
        <Collapse bordered={false} defaultActiveKey={['analyseContent']}>
          <Panel key="analyseContent" header={intl.get(`${modelPrompt}.analyseContent`).d('分析内容')}>
            <Row>
              <Col span={16}>
                <div
                  id="linear-chart"
                  ref={linearChartRef}
                  style={{
                    height: '300px',
                  }}
                />
              </Col>
              <Col span={8}>
                <Form labelWidth={100} disabled>
                  <TextField label={intl.get(`${modelPrompt}.label.ev`).d('重复性EV')} value={dataSoure?.linearTableInfo?.ev} />
                  <TextField label={intl.get(`${modelPrompt}.label.evPercent`).d('EV%')} value={dataSoure?.linearTableInfo?.evPercent} />
                  <TextField label={intl.get(`${modelPrompt}.label.fitCurve`).d('拟合直线')} value={dataSoure?.linearTableInfo?.fitCurve} />
                  <TextField label={intl.get(`${modelPrompt}.label.t`).d('临界值')} value={dataSoure?.linearTableInfo?.t} />
                  <TextField label={intl.get(`${modelPrompt}.label.ta`).d('斜率统计值')} value={dataSoure?.linearTableInfo?.ta} />
                  <TextField label={intl.get(`${modelPrompt}.label.tb`).d('截距统计值')} value={dataSoure?.linearTableInfo?.tb} />
                </Form>
              </Col>
            </Row>
            <Form labelWidth={100} disabled dataSet={analyseResultDs}>
              <TextField name="msaResult" help={<RenderHelp />} />
              <TextArea name="msaConclusion" rows={7} />
            </Form>
          </Panel>
        </Collapse>
      )}
    </div>
  );
};

export default LinearGraphic;
