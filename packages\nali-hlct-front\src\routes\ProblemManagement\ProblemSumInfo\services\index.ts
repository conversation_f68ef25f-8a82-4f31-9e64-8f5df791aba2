/**
 * @Description: 问题管理监控看板-service
 * @Author: <<EMAIL>>
 * @Date: 2023-11-01 09:47:46
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-11-01 09:54:00
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 查询看板界面所有数据
 * @function QueryProblemInfo
 * @returns {object} fetch Promise
 */
export function QueryProblemInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem-sum-info/report-all/ui`,
    method: 'POST',
  };
}

/**
 * 查询问题数量占比数据
 * @function QueryProblemRatio
 * @returns {object} fetch Promise
 */
export function QueryProblemRatio(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem-sum-info/report-ratio/ui`,
    method: 'POST',
  };
}
