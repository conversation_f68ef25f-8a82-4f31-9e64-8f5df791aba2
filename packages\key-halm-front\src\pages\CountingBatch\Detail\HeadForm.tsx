import React, { <PERSON> } from 'react';
import {
  Form,
  Lov,
  TextField,
  Switch,
  Output,
  DatePicker,
  Select,
  TextArea,
} from 'choerodon-ui/pro';
import { dateRender, dateTimeRender, yesOrNoRender } from 'utils/renderer';
import { tagRenderer } from 'alm/utils/renderer';
import { ViewMode } from 'choerodon-ui/pro/lib/date-picker/enum';
import { ResizeType } from 'choerodon-ui/pro/lib/text-area/enum';
import C7nOrgPartnerLov from 'alm/components/OrgPartnerLov/c7nLov';

interface Props {
  [key: string]: any;
}

const Index: FC<Props> = props => {
  const { editFlag, dataSet } = props;
  return (
    <>
      {editFlag ? (
        <Form dataSet={dataSet} columns={3}>
          <Lov name="countingTypeLov" />
          <TextField name="batchName" />
          <TextField name="batchNumber" disabled />
          <Select name="batchStatus" disabled />
          <DatePicker name="planedStartDate" renderer={({ text }) => dateRender(text)} />
          <DatePicker name="planedEndDate" renderer={({ text }) => dateRender(text)} />
          <Select name="countingModeCode" />
          <Switch name="needPhotoFlag" />
          <DatePicker
            mode={ViewMode.dateTime}
            name="snapshotTime"
            renderer={({ text }) => dateRender(text)}
            disabled
          />
          <TextField name="creatorName" disabled />
          <C7nOrgPartnerLov name="initiateOrgLov" record={dataSet.current} />
          <DatePicker
            mode={ViewMode.dateTime}
            name="creationDate"
            renderer={({ text }) => dateTimeRender(text)}
            disabled
          />
          <TextArea
            name="description"
            newLine
            colSpan={3}
            showLengthInfo
            rows={1}
            resize={ResizeType.both}
            style={{ width: 'calc((100% - 217px) /3)' }}
          />
        </Form>
      ) : (
        <Form dataSet={dataSet} columns={3}>
          <Output name="countingTypeName" />
          <Output name="batchName" />
          <Output name="batchNumber" />
          <Output name="batchStatus" renderer={tagRenderer} />
          <Output name="planedStartDate" renderer={({ text }) => dateRender(text)} />
          <Output name="planedEndDate" renderer={({ text }) => dateRender(text)} />
          <Output name="countingModeCode" />
          <Output name="needPhotoFlag" renderer={({ value }) => yesOrNoRender(value)} />
          <Output name="snapshotTime" renderer={({ text }) => dateRender(text)} />
          <Output name="creatorName" />
          <Output name="initiateOrgLov" />
          <Output name="creationDate" renderer={({ text }) => dateTimeRender(text)} />
          <Output name="description" newLine colSpan={3} />
        </Form>
      )}
    </>
  );
};
export default Index;
