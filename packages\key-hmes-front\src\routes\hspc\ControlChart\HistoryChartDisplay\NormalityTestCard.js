/**
 * @Description: 控制控制图维护 - 历史图形查询-正态检验卡片
 * @Author: <<EMAIL>>
 * @Date: 2021-07-06 11:32:44
 * @LastEditTime: 2022-12-13 16:43:52
 * @LastEditors: <<EMAIL>>
 */
import React, { Component } from 'react';
import { ExpandCardC7n } from 'hcm-components-front/lib/components/tarzan-ui';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Spin } from 'hzero-ui';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { get as chainGet } from 'lodash';
import { Bind } from 'lodash-decorators';
import { BASIC } from "hcm-components-front/lib/utils/config";
import styles from './index.module.less';
import NormalityTestChart from './NormalityTestChart';

const modelPrompt = 'tarzan.hspc.controlChartMaintain';

const tenantId = getCurrentOrganizationId();

export default class NormalityTestCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      buildDataLoading: false,
      openNormality: false,
      normalTestGraph: [],
      scatterPoint: [],
      extent: [],
      min: 0,
      max: 0,
      yValues: [],
    };
    this.ProcessDataDS = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'name',
        },
        {
          name: 'value',
        },
      ],
    });
  }

  resetChart = () => {
    this.setState({
      openNormality: false,
      buildDataLoading: false,
      normalTestGraph: [],
      scatterPoint: [],
      extent: [],
      min: 0,
      max: 0,
      yValues: [],
    });
  };

  collapse = () => {
    const { openNormality } = this.state;
    // 收起正太校验卡片
    this.setState({ openNormality: !openNormality });
  };

  handleSearch = params => {
    // 展开正态校验卡片
    const { buildDataLoading } = this.state;
    if (buildDataLoading) {
      return;
    }

    this.setState({
      buildDataLoading: true,
    });
    request(`${BASIC.TARZAN_HSPC}/v1/${tenantId}/ad/control-calculate/ui`, {
      method: 'POST',
      body: {
        ...params,
      },
    })
      .then(res => {
        if (res && res.success) {
          const xPoint = (chainGet(res, 'rows.scatterPoint', []) || []).map(ele => ele.xAbscissa);
          const xLine = (chainGet(res, 'rows.normalTestGraph', []) || []).map(ele => ele.xAbscissa);
          this.setState({
            normalTestGraph: chainGet(res, 'rows.normalTestGraph', []) || [],
            scatterPoint: chainGet(res, 'rows.scatterPoint', []) || [],
            yValues: chainGet(res, 'rows.yValues', []) || [],
            min: Math.min(Math.min(...xPoint), Math.min(...xLine)),
            max: Math.max(Math.max(...xPoint), Math.max(...xLine)),
            extent: [Math.min(...xLine), Math.max(...xLine)],
          });
          if (params.openNormality) {
            this.setState({
              openNormality: true,
            });
          }
          if (res.rows.gaussianData) {
            // 给左边的表格赋值
            const processDataVo = JSON.parse(JSON.stringify(res.rows?.gaussianData || {}));
            // 统计值
            const processDataObjList = [];
            const processDataVoKeys = Object.keys(processDataVo);
            const processDataVoValues = Object.values(processDataVo);
            for (let i = 0; i < processDataVoKeys.length; i++) {
              if (processDataVoKeys[i] === 'meanValue') {
                const obj = {};
                obj.name = intl.get(`${modelPrompt}.meanValue`).d('平均值');
                obj.value = processDataVoValues[i] || '*';
                processDataObjList[0] = obj;
              } else if (processDataVoKeys[i] === 'sigma') {
                const obj = {};
                obj.name = intl.get(`${modelPrompt}.sigma`).d('标准差');
                obj.value = processDataVoValues[i] || '*';
                processDataObjList[1] = obj;
              } else if (processDataVoKeys[i] === 'count') {
                const obj = {};
                obj.name = intl.get(`${modelPrompt}.countN`).d('N');
                obj.value = processDataVoValues[i] || '*';
                processDataObjList[2] = obj;
              } else if (processDataVoKeys[i] === 'ad') {
                const obj = {};
                obj.name = intl.get(`${modelPrompt}.ad`).d('AD');
                obj.value = processDataVoValues[i] || '*';
                processDataObjList[3] = obj;
              } else if (processDataVoKeys[i] === 'pValue') {
                const obj = {};
                obj.name = intl.get(`${modelPrompt}.pValue`).d('P');
                obj.value = processDataVoValues[i] || '<0.001';
                processDataObjList[4] = obj;
              }
            }
            this.ProcessDataDS.data = processDataObjList;
          }
        } else {
          notification.error({
            message: res.message,
            placement: 'bottomRight',
          });
        }
        this.setState({
          buildDataLoading: false,
        });
      })
      .catch(() => {
        this.setState({
          buildDataLoading: false,
        });
      });
  };

  // 过程能力table
  get processDataDSColumns() {
    const headerStyle = {
      display: 'none',
    };
    return [
      {
        header: intl.get(`${modelPrompt}.value`).d('统计值'),
        headerStyle: { height: '30px', lineHeight: '15px', padding: 0 },
        children: [
          {
            header: null,
            name: 'name',
            headerStyle,
            width: '65%',
          },
          {
            header: null,
            name: 'value',
            headerStyle,
            width: '35%',
          },
        ],
      },
    ];
  }

  @Bind()
  // param 为 g2Paint
  getG2Paint = () => {
    this.setState({});
  };

  render() {
    const {
      openNormality,
      normalTestGraph,
      extent,
      scatterPoint,
      min,
      max,
      yValues,
      buildDataLoading,
    } = this.state;
    const { showTitle, controlChartCode, controlChartDesc } = this.props;
    return (
      <div className={styles.cardContainer}>
        <ExpandCardC7n
          showExpandIcon
          title={intl.get(`${modelPrompt}.normalityTest`).d('正态检验')}
          expandFlag={openNormality}
          onChangeExpandFlag={this.collapse}
          loading={buildDataLoading}
        >
          {openNormality && normalTestGraph?.length > 0 && (
            <Spin spinning={buildDataLoading}>
              <div id={`type-${controlChartCode}`}>
                <div className="left" style={{ display: showTitle ? 'block' : 'none' }}>
                  <div
                    className={styles.analys_code}
                    style={{ width: '45%', display: 'inline-block', fontSize: '14px' }}
                  >
                    <span className="analys-label">
                      {intl.get(`${modelPrompt}.controlChartCode`).d('控制控制图编码:')}:{' '}
                    </span>
                    <span> {controlChartCode}</span>
                  </div>
                  <div
                    className={styles.analys_desc}
                    style={{ width: '45%', display: 'inline-block', fontSize: '14px' }}
                  >
                    <span className="analys-label">
                      {intl.get(`${modelPrompt}.controlChartDesc`).d('控制控制图描述:')}:{' '}
                    </span>
                    <span> {controlChartDesc}</span>
                  </div>
                </div>
                <div className={styles.cpk} style={{ alignItems: 'center' }}>
                  <div className={styles.cpkChartLeft} id='cpkChartLeft'>
                    <NormalityTestChart
                      points={scatterPoint}
                      line={normalTestGraph}
                      extent={extent}
                      min={min}
                      max={max}
                      getG2Paint={this.getG2Paint}
                      ticks={normalTestGraph.map(ele => ele.zOrdinates)}
                      yValues={yValues}
                    />
                  </div>
                  <div className={styles.cpkRight}>
                    <Table
                      rowHeight={19}
                      selectionMode="none"
                      dataSet={this.ProcessDataDS}
                      columns={this.processDataDSColumns}
                      pagination={false}
                    />
                  </div>
                </div>
              </div>
            </Spin>
          )}
        </ExpandCardC7n>
      </div>
    );
  }
}
