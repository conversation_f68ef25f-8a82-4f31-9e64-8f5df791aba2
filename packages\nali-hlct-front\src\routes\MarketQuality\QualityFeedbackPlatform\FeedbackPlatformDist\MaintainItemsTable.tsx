/*
 * @Description: 质量反馈单-详情页维修项目信息
 * @Author: <<EMAIL>>
 * @Date: 2024-03-11 16:28:50
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-12 14:39:39
 */
import React, { useMemo, useCallback } from 'react';
import intl from 'utils/intl';
import { Table, Button, Switch } from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';

export default ({ ds, canEdit }) => {
  const handleAdd = useCallback(() => {
    let _maxSeq = 0;
    ds.forEach(_record => {
      if (_record?.get('sequence') > _maxSeq) {
        _maxSeq = _record?.get('sequence');
      }
    });
    ds.create({ sequence: _maxSeq + 10 });
  }, [ds]);

  const handleDeleteLine = record => {
    if (record?.get('feedbackProjectId')) {
      record?.set('deleteFlag', true);
    } else {
      ds.remove(record);
    }
    // 已经存在表里的行，序号不变，其他序号递增
    let _maxSeq = 0;
    ds.forEach(_record => {
      if (_record?.get('sequence') > _maxSeq && _record?.get('feedbackProjectId')) {
        _maxSeq = _record?.get('sequence');
      } else {
        _record.set('sequence', _maxSeq + 10);
        _maxSeq += 10;
      }
    });
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <Button
            icon="add"
            disabled={!canEdit}
            funcType={FuncType.flat}
            onClick={handleAdd}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        width: 60,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDeleteLine(record)}
          >
            <Button icon="remove" disabled={!canEdit} funcType={FuncType.flat} size={Size.small} />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      { name: 'sequence', align: ColumnAlign.left },
      {
        name: 'maintainItems',
        editor: () => canEdit,
      },
    ],
    [canEdit],
  );

  return <Table dataSet={ds} columns={columns} filter={record => !record?.get('deleteFlag')} />;
};
