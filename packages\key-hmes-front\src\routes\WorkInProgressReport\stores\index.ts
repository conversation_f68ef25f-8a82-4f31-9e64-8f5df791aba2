import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: true,
  selection: false,
  dataKey: 'rows.content', // 列表数据在接口返回json中的相对路径
  totalKey: 'rows.totalElements',
  primaryKey: 'materialId', // 表格唯一性主键
  queryFields: [
    {
      name: 'productionLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.productionLine`).d('生产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      noCache: true,
      required: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      type: FieldType.number,
      bind: 'productionLine.prodLineId',
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      bind: 'productionLine.prodLineCode',
    },
    {
      name: 'operation',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.router`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      noCache: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          const prodLineId = record.get('prodLineId');
          return !prodLineId;
        },
      }
    },
    {
      name: 'operationId',
      type: FieldType.number,
      bind: 'operation.operationId',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'areaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaCode`).d('区域'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元'),
    },
    {
      name: 'waitProcessingQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.waitProcessingQty`).d('待加工数量'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
    },
    {
      name: 'wipQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wipQty`).d('在制数量'),
    },
    {
      name: 'inventoryQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inventoryQty`).d('库存数量'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-step-wip/list/ui`,
        method: 'GET',
      };
    },
  },
});

const toBeProcessedDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content', // 列表数据在接口返回json中的相对路径
  totalKey: 'rows.totalElements',
  primaryKey: 'materialId', // 表格唯一性主键
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('执行作业标识'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('待加工数量'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'distanceToNow',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.distanceToNow`).d('距现时长'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('操作人员'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-step-wip/detail/ui`,
        method: 'GET',
      };
    },
  },
});

export { headDS, toBeProcessedDS };
