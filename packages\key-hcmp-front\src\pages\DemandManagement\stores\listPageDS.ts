import { FieldIgnore, FieldType } from 'choerodon-ui/dataset/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { BASE_SERVER } from '@/utils/constants';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';


const intlPrefix = 'tarzan.key.focus.aps.demand';

const listPageDS: () => DataSetProps = () => ({
  autoQuery: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'demandId',
      label: intl.get(`${intlPrefix}.head.demandId`).d('需求ID'),
      type: FieldType.string,
    },
    {
      name: 'demandNum',
      label: intl.get(`${intlPrefix}.head.demandNum`).d('需求编码'),
      type: FieldType.string,
    },
    {
      name: 'demandTypeMeaning',
      label: intl.get(`${intlPrefix}.head.demandTypeMeaning`).d('需求类型'),
      type: FieldType.string,
    },
    {
      name: 'scheduleSiteId',
      bind: 'scheduleSiteLov.siteId',
    },
    {
      name: 'scheduleSiteCode',
      bind: 'scheduleSiteLov.siteCode',
      label: intl.get(`${intlPrefix}.head.scheduleSiteCode`).d('计划站点'),
    },
    {
      name: 'scheduleSiteLov',
      label: intl.get(`${intlPrefix}.head.scheduleSiteLov`).d('计划站点'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'HPS.SCHEDULE_SITE',
      valueField: 'siteId',
      textField: 'siteCode',
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId: getCurrentOrganizationId(),
            userId: getCurrentUserId(),
            materialId: record?.get('materialId'),
          };
        },
      },
    },
    {
      name: 'manufacturingSiteId',
      bind: 'manufacturingSiteLov.siteId',
    },
    {
      name: 'manufacturingSiteCode',
      bind: 'manufacturingSiteLov.siteCode',
      label: intl.get(`${intlPrefix}.head.manufacturingSiteCode`).d('生产站点'),
    },
    {
      name: 'manufacturingSiteLov',
      label: intl.get(`${intlPrefix}.head.manufacturingSiteLov`).d('生产站点'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'HPS.MANUFACTURING_SITE',
      valueField: 'siteId',
      textField: 'siteCode',
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId: getCurrentOrganizationId(),
            userId: getCurrentUserId(),
            materialId: record?.get('materialId'),
          };
        },
      },
    },
    {
      name: 'ooLineNum',
      label: intl.get(`${intlPrefix}.head.ooLineNum`).d('机会订单编码'),
      type: FieldType.string,
    },
    {
      name: 'mtoFlag',
      label: intl.get(`${intlPrefix}.head.mtoFlag`).d('按单标识'),
      type: FieldType.string,
      lookupCode: 'HPS.MTO_FLAG',
    },
    {
      name: 'linkOrderNums',
      label: intl.get(`${intlPrefix}.head.linkOrderNums`).d('关联订单编码'),
      type: FieldType.string,
      multiple: true,
    },
    {
      name: 'demandStatusMeaning',
      label: intl.get(`${intlPrefix}.head.demandStatusMeaning`).d('订单状态'),
      type: FieldType.string,
    },
    {
      name: 'demandStatus',
      label: intl.get(`${intlPrefix}.head.demandStatus`).d('订单状态'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${intlPrefix}.head.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'materialMeaning',
      label: intl.get(`${intlPrefix}.head.materialMeaning`).d('物料描述'),
      type: FieldType.string,
    },
    {
      name: 'materialRevisionCode',
      label: intl.get(`${intlPrefix}.head.materialRevisionCode`).d('物料版本'),
      type: FieldType.string,
    },
    {
      name: 'eigenvalueJson',
      label: intl.get(`${intlPrefix}.head.eigenvalueJson`).d('特征值明细'),
      type: FieldType.string,
    },
    {
      name: 'demandQty',
      label: intl.get(`${intlPrefix}.head.demandQty`).d('需求数量'),
      type: FieldType.string,
    },
    {
      name: 'uomCode',
      label: intl.get(`${intlPrefix}.head.uomCode`).d('单位'),
      type: FieldType.string,
    },
    {
      name: 'customerCode',
      label: intl.get(`${intlPrefix}.head.customerCode`).d('客户编码'),
      type: FieldType.string,
    },
    {
      name: 'customerName',
      label: intl.get(`${intlPrefix}.head.customerName`).d('客户名称'),
      type: FieldType.string,
    },
    {
      name: 'customerSite',
      label: intl.get(`${intlPrefix}.head.customerSite`).d('客户地点'),
      type: FieldType.string,
    },
    {
      name: 'promiseTime',
      label: intl.get(`${intlPrefix}.head.promiseTime`).d('承诺日期'),
      type: FieldType.date,
    },
    {
      name: 'demandDate',
      label: intl.get(`${intlPrefix}.head.demandDate`).d('交货日期'),
      type: FieldType.date,
    },
    {
      name: 'priorityMeaning',
      label: intl.get(`${intlPrefix}.head.priorityMeaning`).d('优先级'),
      type: FieldType.string,
    },
    {
      name: 'remark',
      label: intl.get(`${intlPrefix}.head.remark`).d('备注'),
      type: FieldType.string,
    },
    {
      name: 'createdByName',
      label: intl.get(`${intlPrefix}.head.createdByName`).d('创建人'),
      type: FieldType.string,
    },
    {
      name: 'createdBy',
      label: intl.get(`${intlPrefix}.head.createBy`).d('创建人ID'),
      type: FieldType.string,
    },
    {
      name: 'creationDate',
      label: intl.get(`${intlPrefix}.head.creationDate`).d('创建时间'),
      type: FieldType.string,
    },
    {
      name: 'lastUpdatedByName',
      label: intl.get(`${intlPrefix}.head.lastUpdatedByName`).d('修改人'),
      type: FieldType.string,
    },
    {
      name: 'lastUpdateDate',
      label: intl.get(`${intlPrefix}.head.lastUpdateDate`).d('修改时间'),
      type: FieldType.string,
    },
    {
      name: 'eigenvalueDetail',
      label: intl.get(`${intlPrefix}.head.eigenvalueDetail`).d('特征值明细'),
      type: FieldType.string,
    },
  ],
  queryFields: [
    {
      name: 'manufacturingSiteId',
      bind: 'manufacturingSiteLov.siteId',
    },
    {
      name: 'manufacturingSiteLov',
      label: intl.get(`${intlPrefix}.head.manufacturingSiteLov`).d('生产站点'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'MT.APS.SITE',
      valueField: 'siteId',
      textField: 'siteCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'demandTypes',
      label: intl.get(`${intlPrefix}.head.demandTypes`).d('需求类型'),
      type: FieldType.string,
      lookupCode: 'MT.APS.GEN_TYPE_URL',
      lovPara: {
        typeGroup: 'DEMAND_TYPE',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      valueField: 'typecode',
      textField: 'description',
      multiple: true,
    },
    {
      name: 'demandNums',
      label: intl.get(`${intlPrefix}.head.demandNums`).d('需求编码'),
      type: FieldType.string,
      multiple: true,
    },
    {
      name: 'linkOrderNums',
      label: intl.get(`${intlPrefix}.head.linkOrderNums`).d('关联订单编码'),
      type: FieldType.string,
      multiple: true,
    },
    {
      name: 'materialLov',
      label: intl.get(`${intlPrefix}.head.materialLov`).d('物料编码'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'MT.APS.MATERIAL',
      valueField: 'materialId',
      textField: 'materialCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'demandStatuses',
      label: intl.get(`${intlPrefix}.head.demandStatuses`).d('订单状态'),
      type: FieldType.string,
      lookupCode: 'MT.APS.GEN_STATUS.URL',
      lovPara: {
        statusGroup: 'DEMAND_STATUS',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      textField: 'meaning',
      valueField: 'statuscode',
      multiple: true,
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASE_SERVER}/v1/${getCurrentOrganizationId()}/hps-demands/query/for/ui`,
        data,
        method: 'POST',
      };
    },
    submit: ({ data }) => {
      return {
        url: `${BASE_SERVER}/v1/${getCurrentOrganizationId()}/hps-demands/update/for/ui`,
        data: data[0],
        method: 'POST',
        transformResponse: (response) => {
          try {
            const jsonData = JSON.parse(response);
            if (jsonData.success === false) {
              return {
                type: 'warn',
                failed: true,
                message: jsonData.message,
              };
            } 
            return jsonData;
            
          } catch {
            /**/
          }
          return response;
        },
      };
    },
  },
});
export default listPageDS;
