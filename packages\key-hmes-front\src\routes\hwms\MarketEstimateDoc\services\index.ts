/*
 * @Description: 市场活动评估单-service
 * @Author: <<EMAIL>>
 * @Date: 2023-09-15 11:27:01
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-19 17:41:41
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

/**
 * 用户信息Lov
 * @function QueryUserInfo
 * @returns {object} fetch Promise
 */
export function QueryUserInfo(): object {
  return {
    url: `/hpfm/v1/${tenantId}/lovs/sql/data`,
    method: 'GET',
  };
}

/**
 * 根据materialId获取物料相关信息
 * @function QueryMaterialInfo
 * @returns {object} fetch Promise
 */
export function QueryMaterialInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-qa-feedbacks/mt-material-ca`,
    method: 'POST',
  };
}

/**
 * 保存市场评估单
 * @function SaveMarketEstimate
 * @returns {object} fetch Promise
 */
export function SaveMarketEstimate(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-estimates/save/ui`,
    method: 'POST',
  };
}

/**
 * 提交市场评估单
 * @function SubmitMarketEstimate
 * @returns {object} fetch Promise
 */
export function SubmitMarketEstimate(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-estimates/submit/ui`,
    method: 'GET',
  };
}

/**
 * 审核市场评估单
 * @function ReviewMarketEstimate
 * @returns {object} fetch Promise
 */
export function ReviewMarketEstimate(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-estimates/reject/ui`,
    method: 'GET',
  };
}

/**
 * 发起市场活动
 * @function StartMarketActive
 * @returns {object} fetch Promise
 */
export function StartMarketActive(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-market-estimates/launch/ui`,
    method: 'POST',
  };
}

/**
 * 查询当前用户的员工信息
 * @function QueryUserInfo
 * @returns {object} fetch Promise
 */
export function QueryUserEmployeeInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem/unit-position/by-user`,
    method: 'GET',
  };
}
