/*
 * @Description: 质量反馈单-services
 * @Author: <<EMAIL>>
 * @Date: 2023-09-13 14:37:40
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-18 19:18:37
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const endUrl = "";

/**
 * 保存质量反馈单
 * @function SaveFeedbackDoc
 * @returns {object} fetch Promise
 */
export function SaveFeedbackDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-qa-feedbacks/save`,
    method: 'POST',
  };
}

/**
 * 根据materialId获取物料批对应的物料相关信息
 * @function QueryMaterialLotInfo
 * @returns {object} fetch Promise
 */
export function QueryMaterialLotInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-qa-feedbacks/mt-material-ca`,
    method: 'POST',
  };
}
