/**
 * @Description: 不良代码组维护列表
 * @Author: <<EMAIL>>
 * @Date: 2022-07-12 10:00:07
 * @LastEditTime: 2023-05-18 15:11:48
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Button as PermissionButton } from 'components/Permission';
import { Badge } from 'choerodon-ui';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores/DefectGroupListDS';

const tenantId = getCurrentOrganizationId();

const DefectGroupList = props => {
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;
  useEffect(() => {
    tableDs.query('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_LIST.LIST`);
    tableDs.query(props.tableDs.currentPage);
  }, []);
  const columns = [
    {
      name: 'ncGroupCode',
      width: 200,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push({
                pathname: `/hmes/bad/defect-group/dist/${record.data.ncGroupId}`,
              });
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'description',
      width: 300,
    },
    {
      name: 'siteCode',
      width: 180,
    },
    {
      name: 'siteName',
      width: 180,
    },
    {
      name: 'enableFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => {
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.enable').d('启用')
                : intl.get('tarzan.common.label.disable').d('禁用')
            }
          />
        );
      },
    },
    {
      name: 'createdByName',
    },
    {
      name: 'creationDate',
      width: 180,
    },
    {
      name: 'lastUpdatedByName',
    },
    {
      name: 'lastUpdateDate',
      width: 180,
    },
  ];

  const handleCreate = () => {
    props.history.push(`/hmes/bad/defect-group/dist/create`);
  };

  const handleImport = () => {
    openTab({
      key: '/himp/commentImport/MT.METHOD.NC_GROUP',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.badCode.defectGroup.title.defectGroup').d('不良代码组维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
          icon="file_upload"
          onClick={handleImport}
        >
          {intl.get('tarzan.common.button.import').d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_LIST.LIST`,
          },
          <Table
            searchCode="bldmwh1"
            customizedCode="bldmwh1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
          />,
        )}
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.badCode.defectGroup', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.NC_GROUP_LIST.LIST`],
    })(DefectGroupList),
  ),
);
