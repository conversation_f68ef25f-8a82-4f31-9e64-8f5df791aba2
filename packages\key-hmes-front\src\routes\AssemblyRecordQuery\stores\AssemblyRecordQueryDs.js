import { getCurrentOrganizationId } from 'utils/utils';
import { Host } from '@/utils/config';
import moment from 'moment';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.assemblyRecordQuery';
// const Host = `/mes-41300`;

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'assembleProcessActualId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      },
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      },
      {
        name: 'eoStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoStatusDesc`).d('执行作业状态'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      },
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('工单数量'),
      },
      {
        name: 'workOrderStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderStatusDesc`).d('工单状态'),
      },
      {
        name: 'productionLineCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.productionLineCode`).d('生产线编码'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
      },
      {
        name: 'operationDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDesc`).d('工艺描述'),
      },
      {
        name: 'lineNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineNumber`).d('组件行号'),
      },
      {
        name: 'componentMaterialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.componentMaterialCode`).d('组件物料编码'),
      },
      {
        name: 'componentRevisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.componentRevisionCode`).d('组件物料版本'),
      },
      {
        name: 'componentMaterialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.componentMaterialName`).d('组件物料描述'),
      },
      {
        name: 'componentMaterialUomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.componentMaterialUomCode`).d('组件物料单位编码'),
      },
      {
        name: 'componentMaterialUomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.componentMaterialUomName`).d('组件物料单位名称'),
      },
      {
        name: 'sumAssembleQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.sumAssembleQty`).d('装配总数'),
      },
      {
        name: 'sumScrapQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.sumScrapQty`).d('报废总数'),
      },
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('组件物料批编码'),
      },
      {
        name: 'assembleQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.assembleQty`).d('装配数量'),
      },
      {
        name: 'referencePoint',
        type: 'string',
        label: intl.get(`${modelPrompt}.referencePoint`).d('装配位置'),
      },
      {
        name: 'scrapQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.scrapQty`).d('报废数量'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('装配时间'),
      },

      {
        name: 'workcellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellCode`).d('装配工位编码'),
      },
      {
        name: 'workcellDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellDescription`).d('装配工位描述'),
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('装配人'),
      },
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('装配设备编码'),
      },
      {
        name: 'eventId',
        type: 'string',
        label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
      },
      // {
      //   name: 'eventTypeId',
      //   type: 'string',
      //   label: intl.get(`${modelPrompt}.eventTypeId`).d('事件类型ID'),
      // },
      {
        name: 'eventTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型描述'),
      },
      // {
      //   name: 'eventRequestTypeId',
      //   type: 'string',
      //   label: intl.get(`${modelPrompt}.eventRequestTypeId`).d('事件请求类型Id'),
      // },
      {
        name: 'eventRequestTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.eventRequestTypeDesc`).d('事件请求类型描述'),
      },
    ],
    queryFields: [
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
        dynamicProps: {
          required: ({ record }) => {
            return !record?.get('identifications') && !record?.get('materialId')
          },
        },
      },
      {
        name: 'identifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.identifications`).d('条码号'),
        dynamicProps: {
          required: ({ record }) => {
            return !record?.get('workOrderNum') && !record?.get('materialId')
          },
        },
      },
      {
        name: 'materialLov',
        type: 'object',
        lovCode: 'HME.PERMISSION_MATERIAL',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
        dynamicProps: {
          required: ({ record }) => {
            return !record?.get('workOrderNum') && !record?.get('identifications')
          },
        },
      },
      {
        name: 'materialId',
        bind: 'materialLov.materialId',
      },
      {
        name: 'materialCode',
        bind: 'materialLov.materialCode',
      },
      {
        name: 'prodLineLov',
        type: 'object',
        lovCode: 'HME.PERMISSION_PROD_LINE',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.prodLineLov`).d('生产线编码'),
      },
      {
        name: 'productionLineId',
        bind: 'prodLineLov.prodLineId',
      },
      {
        name: 'productionLineCode',
        bind: 'prodLineLov.prodLineCode',
      },

      {
        name: 'operationLov',
        type: 'object',
        lovCode: 'MT.OPERATION',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.operationLov`).d('工艺编码'),
      },
      {
        name: 'operationId',
        bind: 'operationLov.operationId',
      },
      {
        name: 'operationName',
        bind: 'operationLov.operationName',
      },
      {
        name: 'comMaterialLov',
        type: 'object',
        lovCode: 'HME.PERMISSION_MATERIAL',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.comMaterialLov`).d('组件物料编码'),
      },
      {
        name: 'componentMaterialId',
        bind: 'comMaterialLov.materialId',
      },
      {
        name: 'componentMaterialCode',
        bind: 'comMaterialLov.materialCode',
      },
      {
        name: 'materialLotCodes',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCodes`).d('组件物料批编码'),
      },
      {
        name: 'dateFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.dateFrom`).d('装配时间从'),
        max: 'dateTo',
        dynamicProps: {
          required: ({ record }) => record?.get('materialId') || record?.get('dateTo'),
          min: ({ record }) => record?.get('dateTo') ? moment(record?.get('dateTo')).subtract(3, 'd') : null,
        },
      },
      {
        name: 'dateTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.dateTo`).d('装配时间至'),
        min: 'dateFrom',
        dynamicProps: {
          required: ({ record }) => record?.get('materialId') || record?.get('dateFrom'),
          max: ({ record }) => record?.get('dateFrom') ? moment(record?.get('dateFrom')).add(3, 'd') : null,
        },
      },
      {
        name: 'workcellSiteLov',
        type: 'object',
        lovCode: 'MT.MODEL.WORKCELL_SITE',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.workcellSiteLov`).d('装配工位编码'),
      },
      {
        name: 'workcellId',
        bind: 'workcellSiteLov.workcellId',
      },
      {
        name: 'workcellCode',
        bind: 'workcellSiteLov.workcellCode',
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('装配人'),
      },
      {
        name: 'equipmentLov',
        type: 'object',
        lovCode: 'MT.MODEL.EQUIPMENT',
        label: intl.get(`${modelPrompt}.equipmentLov`).d('装配设备编码'),
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId'
      }
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-assemble-record-query/record/info`,
          method: 'GET',
        };
      },
    },
  };
};


export { tableDS };
