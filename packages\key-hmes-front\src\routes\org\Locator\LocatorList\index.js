/**
 * @Description: 库位维护-列表页-个性化测试
 * @Author: <<EMAIL>>
 * @Date: 2021-03-05 14:14:48
 * @LastEditTime: 2023-05-18 11:35:05
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Badge, Tag } from 'choerodon-ui';
import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { LocatorListDS } from '../stores/LocatorListDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.model.org.locator';

const LocatorList = props => {
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;

  useEffect(() => {
    tableDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_LIST.LIST`)
    tableDs.query(props.tableDs.currentPage);
  }, []);

  const columns = [
    {
      name: 'locatorCode',
      width: 150,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(
                `/hmes/organization-modeling/locator/detail/${record.data.locatorId}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'locatorName',
      tooltip: 'overflow',
      width: 200,
    },
    {
      name: 'locatorType',
      align: 'center',
    },
    {
      name: 'locatorCategory',
      align: 'center',
    },
    {
      name: 'parentLocatorName',
      width: 150,
    },
    {
      name: 'siteNameList',
      width: 200,
      tooltip: 'overflow',
      renderer: ({ value }) =>
        (value || []).map(item => {
          return (
            <Tag color="blue" key={item}>
              {item}
            </Tag>
          );
        }),
    },
    {
      name: 'identification',
      width: 110,
    },
    {
      name: 'coordinateCode', // 位置坐标系
    },
    {
      name: 'xValue', // 位置坐标值
      align: 'center',
      renderer: ({ record }) => {
        switch (record.get('coordinateType')) {
          case '3D':
            if (record.get('xValue') && record.get('yValue') && record.get('zValue')) {
              return `( ${record.get('xValue')}, ${record.get('yValue')}, ${record.get(
                'zValue',
              )} )`;
            }
            break;
          case '2D':
            if (record.get('xValue') && record.get('yValue')) {
              return `( ${record.get('xValue')}, ${record.get('yValue')} )`;
            }
            break;
          case '1D':
            return record.get('xValue') ? `( ${record.get('xValue')} )` : null;
          default:
            return null;
        }
      },
    },
    {
      name: 'plantCodeList',
      width: 200,
      tooltip: 'overflow',
      renderer: ({ value }) =>
        (value || []).map(item => {
          return (
            <Tag color="blue" key={item}>
              {item}
            </Tag>
          );
        }),
    },
    {
      name: 'subinvList',
      width: 200,
      tooltip: 'overflow',
      renderer: ({ value }) =>
        (value || []).map(item => {
          return (
            <Tag color="blue" key={item}>
              {item}
            </Tag>
          );
        }),
    },
    {
      name: 'enableFlag',
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'negativeFlag',
      align: 'center',
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  const handleCreate = () => {
    props.history.push('/hmes/organization-modeling/locator/detail/create');
  };

  const handleImport = () => {
    openTab({
      key: '/himp/commentImport/MT.MODEL.LOCATOR',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.locationMaintenance`).d('库位维护')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
          icon="file_upload"
          onClick={handleImport}
        >
          {intl.get('tarzan.common.button.import').d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_LIST.LIST`,
          },
          <Table
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="LocatorList"
            customizedCode="LocatorList"
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.model.org.locator', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...LocatorListDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_LIST.LIST`] }),
)(LocatorList);
