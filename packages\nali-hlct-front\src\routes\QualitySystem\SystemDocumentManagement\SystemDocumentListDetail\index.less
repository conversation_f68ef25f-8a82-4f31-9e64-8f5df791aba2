.sop-up-tb {
  margin-top: 30px !important;

  :global {
    .c7n-table {
      .c7n-table-thead {
        height: 29px;

        tr > th {
          background-color: #ffff !important;
          color: #ffff !important;
          border-bottom: 1px solid #ffff !important;
          padding: 0 8px;

          i.c7n-table-sort-icon {
            color: #ffff !important;
          }
        }
      }

      .c7n-table-body {
        overflow-y: auto !important;
      }

      .c7n-table-placeholder {
        background-color: #ffff !important;
        color: #ffff !important;
      }

      .c7n-table-tbody {
        background-color: #ffff !important;

        .c7n-table-sticky-column {
          background-color: #ffff !important;
        }

        tr > td {
          color: #ffff !important;

          .action-link a {
            color: #ffff;
            padding-left: 0;
            padding-right: 0;
          }
        }
      }
    }
  }
}

.form-body {
  // width: 100%;
  // height: 100%;
  // position: absolute;
  // margin-top: 15px;
  // background-color: rgb(186, 192, 204) ;
  border: 1px solid rgb(119, 119, 119) !important;
  // z-index: 1999;
  // top: 0;
}

.preview-wrap {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #ffff;
  z-index: 1999;
  top: 0;
}

.preview-header {
  height: 28px;
  display: flex;
  justify-content: space-between;
  margin: 4px 8px;
  color: black;
}

.preview-header-title{
  line-height: 28px;
}
.preview-inner-iframe {
  width: 100%;
  height: 100%;
  // height: calc(100% - 40px);;
  border: 0;
}

.link-active {
  a {
    font-weight: 600;
    border-bottom: 2px solid rgb(8, 64, 248);
  }
}
