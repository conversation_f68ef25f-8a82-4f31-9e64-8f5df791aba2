import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.rohs.manage';
const tenantId = getCurrentOrganizationId();

// 列表-ds
const tableDS = (): DataSetProps => ({
  autoQuery: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-rohs`,
        method: 'get',
        data: data,
      };
    },
    submit: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-rohs`,
        method: 'POST',
        data: data[0],
      };
    },
  },
  queryFields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.marketActive.ypItemCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
    },
    {
      label: intl.get(`${modelPrompt}.endTime`).d('到期时间'),
      name: 'endTime',
      type: FieldType.dateTime,
      range: ['endTimeFrom', 'endTimeTo'],
      ignore: FieldIgnore.always,
      format: 'YYYY-MM-DD',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.endTimeFrom`).d('到期时间从'),
      name: 'endTimeFrom',
      bind: 'endTime.endTimeFrom',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.endTimeTo`).d('到期时间至'),
      name: 'endTimeTo',
      bind: 'endTime.endTimeTo',
    },
  ],
  fields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.marketActive.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.marketActive.materialName`).d('物料名称'),
      bind: 'materialLov.materialName',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierCode',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'supplierName',
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
      bind: 'supplierLov.supplierName',
    },
    {
      type: FieldType.string,
      name: 'filePath',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.roHsFlag`).d('文件是否上传'),
      name: 'roHsFlag',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.endTime`).d('文件到期时间'),
      name: 'endTime',
      format: 'YYYY-MM-DD',
      required: true,
      min: new Date(),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uploadTime`).d('文件上传时间'),
      name: 'uploadTime',
    },
    {
      name: 'enabledFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.enabledFlag`).d('是否启用'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
});

export { tableDS };
