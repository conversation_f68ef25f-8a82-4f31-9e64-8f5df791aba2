# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## 0.0.2 (2022-03-29)


### Bug Fixes

* iqc 采购退货联动修改 ([c406986](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c4069866310e895f045b7e934141ef4e0d223e2c))
* iqc 联动采购退货 ([0ea8a4f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0ea8a4ff71158266b895077797c133ef58afc071))
* iqc 联动采购退货取值问题处理 ([209cdee](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/209cdeeedfdbb2860a35ae26caf63f9081e439fb))
* iqc 领退料联动 修改 ([735c953](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/735c9531b6b81a2fa167f61ccab09cafe6811863))
* iqc 修改 ([7660875](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7660875466f3f5bb2969b747cb1f64443827e304))
* iqc 优化 ([aeca949](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/aeca949b472f88126c5b660be493142cba74bb8e))
* iqc修改 ([89bdb70](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/89bdb709a89e5f9c9a077a7549c4b797c99589b4))



## 4.2.10 (2022-03-25)


### Bug Fixes

* 抽样方法列表对齐调整 ([3380954](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3380954ac3236eff75534b94fc40601af2fb8f3d))
* 送货单管理修改物料批字段宽度 ([7dacf6f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7dacf6f9c30e252d08ec1b1b981d37f06ef4f655))
* 修改物料检验计划按钮逻辑 ([bfdc81b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bfdc81b99f24d36d4192730b5a2cf5ec6cf09053))
* iqc 物料检验计划 样式优化 描述字段替换 ([7e291d1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7e291d113a88f2fa6e7bc39efeb9d78b425d41c1))
* iqc 优化 ([aff0c95](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/aff0c95fa56f5b31d388aa5bb93dca93c01716ef))
* iqc该字段 ([234e570](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/234e570f664e73c1ea6bf0510d0ab8981ee31052))



## 4.2.9 (2022-03-25)


### Bug Fixes

* iqc优化 ([1995b41](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1995b41363739bed0ac4cbbc229bc0d11bf5e44d))



## 4.2.8 (2022-03-25)


### Bug Fixes

* 修改日期卡片样式 ([22e8ba9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/22e8ba9ca3557a0e0fd7a554a21516345dba87f7))
* 只保留两位小数 ([b1e07b6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b1e07b692432b8cbdbb7a9653f4c63e716d79981))
* iqc 优化点处理 ([c9ba702](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c9ba70250d33ff41dcb054542cffb1d045cbe843))



## 4.2.7 (2022-03-24)



## 4.2.6-beta.5 (2022-03-24)


### Bug Fixes

*  杂项工作台 单据状态变更按钮权限修改 ([9a8776d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9a8776d5737412b53b06a1c88dc579ecfaa3ea76))
* 处理杂项工作台详情头类型填充逻辑 ([e608a24](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e608a248f5288c109b3b9b176ddaf0bdf5bf4d1e))
* 单据类型回填 ([249838d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/249838d1fd799956d82bbf432261f018fbeca08a))
* 高度问题 ([28360ff](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/28360ffa949b15efe8035a556e51ab83d484c0c1))
* 库位编码查不出来，给" " ([7abb0a5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7abb0a5e5f65fa8082f657c54628e71b1b038321))
* 领退料工作台物料批明细查询修改参数 ([d50de73](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d50de73ed7d684734173fbcb42d405741dfd5df3))
* 全部反选存在问题 ([fb9afab](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fb9afab2b91e3f7543d66d6980e593fe1b66dceb))
* 人员仓库权限功能中英文样式问题，修改物料批管理平台表格高度 ([2124eef](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2124eef71d016f5569931f8f5d788003d2d10abe))
* 送货单管理 采购订单管理 领退料工作台 采购退货平台 杂项工作台 MT.MODEL.SITE lov 添加参数 siteType:MANUFACTURING ([6a52abb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6a52abbec0deffb56a3204e4f7217d65350cdf33))
* 送货单管理修改关闭状态允许范围 ([4d8767d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4d8767d7ec5689448f55b4dad4a77d82f7f061ae))
* 修改成品发运明细展示字段 ([3fbf8cd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3fbf8cd858668abb4c183a6d919eb7837bdf1482))
* 修改杂项工作台编辑状态删除行添加行的行号逻辑 ([626dbe0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/626dbe054321b6e5514d4af6c8014aef95c87c57))
* 杂项工作台 查询条件创建人修改 ([35375d7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/35375d748772bd59c0355295907198e5d0a66f27))
* 杂项工作台 查询条件lov显示字段修改 ([c38101b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c38101b58eee9c4514c36c3daf21ae52f239c014))
* 杂项工作台 列宽调整 ([e1cf7fe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e1cf7fea512fa1f065fce56c7af4c316bf283531))
* 杂项工作台 优化点处理 ([76939c8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/76939c82d5f2ce91b892e9f18ecb3a352dc5e938))
* 杂项工作台查询参数值修改 ([c9479ce](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c9479ceb56c4de4fdf52cc8ede4a7bb55b3f6d0e))
* 杂项工作台多语言去重 ([67fc8ae](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/67fc8ae7e82412edbd90e4396c78acfb7b25884f))
* 杂项工作台删除执行人执行时间 ([cebebb9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cebebb993d08acc2b76a3a4cb60729aedb8c5398))
* 杂项工作台问题处理 ([61cd888](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/61cd888062c6b689f58e1270580f7bc80d533484))
* 杂项工作台新建行状态字段修改 ([c01ed1f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c01ed1f9184f654c53931b1df8cdee5812ee416b))
* 杂项工作台行号问题处理 ([c107c40](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c107c405c0f5a025aa032ea478604c29c09e7031))
* 杂项工作台修改状态传参修改 ([9a458d6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9a458d68024202611518975f8725bfbdd2e4695a))
* 杂项工作台优化处理 ([2a284fe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2a284fe0ec3747363dd5227ddd6aee69c88996a1))
* 杂项工作台bug处理 ([3de8399](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3de8399639f103ff473bbe6ce78a42946d72bc35))
* 杂项工作台bug处理 ([df8a488](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/df8a488e0dfe30d89a4286543f22c65c13dc120d))
* 杂项下拉框内容查询 ([13404a5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/13404a53610cd9b488f080c9f0214571d2a062b5))
* icq modal 确认取消文字 ([3d04744](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3d04744d707ae57c95af0c69f664289fa2d6e024))



## 4.2.6-beta.4 (2022-03-18)


### Bug Fixes

* 处理允差接口 ([410bfeb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/410bfeba12068b489e8aca9e93f64c724dab9e6c))
* 杂项工作台 修改行数量限制 ([ad2659a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ad2659afa2b2033534d57e8ef0e8ed203470663c))
* 杂项工作台保存 ([fee46b2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fee46b2c69a00afc60e4ad27b3d94a16b5b99e51))
* 杂项工作台编辑页跳转和参数处理 ([c306318](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c3063187c8ae4444dafed65460436c1dfd24e5ff))
* 杂项工作台基本完毕 ([a0f6c93](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a0f6c935b12a18c11f7f12d8df6fafb7a37b77a0))
* 杂项工作台切换状态修改 ([1352ff9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1352ff90d07757f9bcd45e96e7de92e0e295c909))
* 杂项工作台遗留问题处理 ([c6f9e9e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c6f9e9ee3ded996ff6d755db99021bb86cbccdf4))



## 4.2.6-beta.3 (2022-03-17)


### Bug Fixes

* 临时修复图标不居中的问题 ([cb93e48](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cb93e4808d6d72fc3ffebb3de662e7f045b29277))
* 修改外协发料单创建参数修改 ([4991ab5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4991ab5c3a8d1fc8dfef5e8fb2c839ed2a4e1b86))
* 杂项工作台接口修改, 列表页头 行表格字段联调 ([03bce54](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/03bce54bcbbe319979b2e1e71a6f9a84b094eeae))



## 4.2.6-beta.2 (2022-03-16)


### Bug Fixes

*  无需权限站点lovCode更换为需要权限lovCode ([40f007c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/40f007cafb6603daf0cc27f34ca42a6474217cc3))
* 创建外协 发料单/退料单 值集替换 ([e3fd459](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e3fd45911ddc0447b6414f7a327e1a545e4e0224))
* 创建外协发料单 采购订单样式处理 ([888fba9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/888fba99b044ad4ae48d37ead2e7c11525d59d81))
* 创建外协发料单 选择行号只能添加一次问题处理 ([e2da480](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e2da4800acae277eb2908ba4dd4f80092a6294b1))
* 定义课题样本数据保存失败修改 ([617c182](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/617c182818d0dce3caf03ebeafdce02bd144dc2f))
* 课题维护添加保存 ([22ae29e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/22ae29e629774ce2e41cf497498f2ac11ed088d7))
* 领涂料工作台取消行后刷新头 ([ecff211](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ecff211c9eda366d365d246aead467f0bfca819e))
* 领退料单维护 行号逻辑处理 ([4ef85cf](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4ef85cf23b3d15ac78b8a0b9c1b43b63dde49b3a))
* 领退料工作台 添加目标库位 ([21f2aef](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/21f2aefe48296eb0fa326b8544a6b5feab25de7d))
* 领退料工作台参数修改 ([e463813](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e463813a8f267beb8be309f63f1ac8a0bf92adc3))
* 领退料工作台查询字段修改 ([318cd17](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/318cd179964f7e530b6253468fb6c167c56def4f))
* 送货单管理创建物料批点击条件修改 ([4dce54c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4dce54cefe1ee0419a5e5a4cd1dec93dce313934))
* 替换 hlct 接口 ([f9e1f80](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f9e1f80e44098628478f6708de95388bf71ee642))
* 物料检验计划 查询条件恢复原状 ([7d375e6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7d375e6cea17b8e0b09860be7cda73c2eace9968))
* 物料检验计划 头表字段修改 ([ad1e068](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ad1e06842d7440f75560c78252057e329ed725bb))
* 物料检验计划 IQC检验平台 添加版本字段 ([18c4980](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/18c4980aa2b7a8d65e5189722e0f959d9d45e533))
* 杂项工作台修改字段 ([0d3b084](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0d3b0844830d24f249db88c80c5d57b3a2f924ec))
* 质检相关页面 lov 值集修改 ([c300e03](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c300e037d0aabdc25bc0c218594807e8c06b22a7))
* hlct lov 数据接口替换 ([91db868](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/91db868885a83ebc59a44cd01251e118ead70416))


### Reverts

* Revert "feature:c7n相关升级" ([f8e2bf7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f8e2bf7f67af433c620924b9fefbfdc5f5734685))



## 4.2.6-beta.1 (2022-03-14)


### Bug Fixes

* 创建外协发料单 采购订单样式处理 ([4d84a1e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4d84a1edfb704c0fb5b3249f0a166cebd5b28000))
* 领退料工作台 添加目标库位 ([34e3b20](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/34e3b201a367af750203b8c18a398b022e81c729))


### Reverts

* Revert "feature:c7n相关升级" ([02adfce](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/02adfce3a49c6c90a09135f945ae63557312d414))



## 4.2.6-beta.0 (2022-03-14)


### Bug Fixes

*  无需权限站点lovCode更换为需要权限lovCode ([2d7dbfe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2d7dbfee125a138a8927d57e84c5b8089fe097ff))
* 采购订单管理 创建送货单加图标 ([08b3ca6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/08b3ca6ce02fee47ef2bb50c601abfac666d8ff3))
* 采购订单管理 创建送货单添加icon ([2f87a17](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2f87a174d74a8dd9011ad370788e7e018f04da7b))
* 采购订单管理 送货单管理, 预先处理查询结果 ([1ae94ad](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1ae94ad8aafcfa032098d04353730dbbb3bc1be6))
* 采购订单预计到货时间 expectedArrivalTime 字段修改 ([308ce99](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/308ce99ba206825a7940450cd29818fb762d437d))
* 创建外协 发料单/退料单 值集替换 ([9ec0cf4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9ec0cf49dfd27eebfa3425f150fa70265ffcd0c4))
* 创建外协发料单 修改采购订单交互处理 ([30f6189](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/30f6189040d232d0b05c1cb6271dd54884600546))
* 创建外协发料单 选择行号只能添加一次问题处理 ([e32cfbd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e32cfbd8d10d63eef472cd7a8fe18db191b9e51e))
* 创建外协发料单添加一个字段 ([33fc83c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/33fc83c661728e04f8a9ef5ff08e7a43d8ab74f0))
* 创建外协发料单lov参数修改 ([860b257](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/860b257aa0ad91f363f1478faa82e8aa59490b91))
* 代码修正 ([5bbbc94](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5bbbc9427ce25c3359c70c71a298d10b39b96401))
* 定义课题样本数据保存失败修改 ([583a54f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/583a54f9880d15ff37f27ee9188d635d0fa52fee))
* 多语言模块 tarzan.purchase => tarzan.hmes.purchase ([fde89af](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fde89af4625492948bc3a50ad54ae61ce5bd9fc4))
* 分析控制图问题处理 ([c0555d6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c0555d6d9c13add63a0ee253b5708e728b3e481a))
* 课题维护添加保存 ([e8ffa20](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e8ffa20fa45c54495cc2e2aa94468c2128a04595))
* 领涂料工作台取消行后刷新头 ([cd313e7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cd313e7f6533daf5ce1a49b7a6c00b04c14df1a8))
* 领退料单维护 行号逻辑处理 ([1fa07c2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1fa07c25ee0ba01a03a43b60d0c161cb10def2b6))
* 领退料单维护行字段校验修改 ([d414bb2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d414bb2babfa15bd7319b1b45324596e4ffdd9a8))
* 领退料工作台 目标来源仓库数量问题修复 ([1508e55](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1508e55aeebbbde8e13b53683000c9de0d473b96))
* 领退料工作台参数修改 ([04027d1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/04027d1feb496ef6321acb5f02dc7b97d0b50427))
* 领退料工作台查询字段修改 ([127fd3c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/127fd3ce3168d8ef012635f46777fd692222ede3))
* 领退料工作台头头列表添加需求时间 ([dd71cf6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/dd71cf6b5985bbc9d51fa0cc8f4d6ca33358f5cb))
* 领退料工作台详情添加站点 ([ede3412](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ede3412c57d4e3bf4333392b84c5ddeb0c7c6fe9))
* 领退料工作台详情修改 ([3673edc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3673edc6710fd46511f4921777b97a127362f1b9))
* 领退料工作台修改 ([70637e5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/70637e5dc0e65e89d625e71a313e55e459a2d24d))
* 容器类型维护添加容器分类字段 ([6f512c8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6f512c87aa7321e5049ba4496c8fabe263aa957a))
* 送货单管理 接口参数修改 ([3818a47](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3818a4775113cea7d532beb5d78b0f61cf975b48))
* 送货单管理 领退料工作台 列表样式修改 ([a1e078d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a1e078dfd5375424cc4a23d83924baf19cd0f2cc))
* 送货单管理列表查询字段修改 ([9f577b2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9f577b2cbfd89d60ec90e0a475cbe14752f7e8a9))
* 送货单管理行字段删减 ([a956c3e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a956c3e12c7c8d17e1f8490fe69b6fd60fd90761))
* 替换 hlct 接口 ([d9be97f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d9be97ff3d7ab3cc108d067f56ae3117983f300b))
* 外协管理平台 查询物料批参数修改 ([31a0de7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/31a0de7cec33e569362a6e0ea2107039e52aac1a))
* 外协管理平台按钮添加图标 ([ef6a1d2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ef6a1d2ddf82e375b55e2eecf6c3ea9a28c0b86f))
* 物料检验计划 查询条件恢复原状 ([c797f04](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c797f0454254495d58b5f5a91501b6f9359a1d82))
* 物料检验计划 头表字段修改 ([c655fda](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c655fda43a3af926c2ac5712944c38d854e309a1))
* 物料检验计划 IQC检验平台 添加版本字段 ([1d49805](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1d498051e10927961eeb9d641df333cc1c48bef9))
* 物料检验计划编辑页面修改 ([dd989a9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/dd989a903492782e69928118c50dffa30349affc))
* 修改成品类功能接口url ([5578234](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/55782347d5e7b9e00e7af7bce5126abcd6a34140))
* 修改外协平台接口地址 ([2934519](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2934519704c9e7bfc8d08f73804fd7b2fa6c48e7))
* 杂项工作台修改 ([c61a026](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c61a026f69e27c5e380a3dc032e204365324c0b8))
* 杂项工作台修改字段 ([12b44c3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/12b44c3ef25a2cd121eee8bfa256fdf175981525))
* 质检相关页面 lov 值集修改 ([aec93b6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/aec93b6501b15850d7e5d39cbbc2f65bee2cd128))
* hlct lov 数据接口替换 ([abd9ebe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/abd9ebeb8c37016cceeddbf77fad17d15f450694))



## 4.2.5 (2022-02-23)



## 4.2.4 (2022-02-23)



## 4.2.3 (2022-02-22)


### Bug Fixes

* 创建外协发料单报错消息添加 ([6bebe84](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6bebe84c391b5131c542ecf401987c57018a71fc))



## 4.2.2 (2022-02-21)



## 4.2.1 (2022-02-21)



# 4.2.0 (2022-02-18)


### Bug Fixes

*  领退料状态对不修复 ([80f2ba8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/80f2ba886e6060ab934d28b31013c19aaf93d6fb))
* 报错引起lov无法加载修复 ([06bbd72](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/06bbd720e3fa4e6e951e221789fc76e989c5d7ab))
* 采购订单管理 替换供应商 供应商地点 字段 ([ff82c34](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ff82c348cfd46f4c269e01ec44de7420144a8bf5))
* 采购订单管理 用监听事件替换table onChange事件 ([a4b198e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a4b198e51efc1dcc7b97b62007229f04a309f31b))
* 采购订单管理行字段添加 ([af28cef](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/af28cef5e686bc41744d7b9ff3a1a57844d81ffb))
* 采购订单管理隐藏接收方式 ([b5e16d5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b5e16d5cf21800c547ed018ea7e7d61b9319fcf2))
* 采购订单管理增加查询参数 ([17d3283](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/17d3283857eb7d10cbe8cda2eb426acdc061977d))
* 采购订单样式处理 ([b406c3d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b406c3df7f85d0499fcf29a6c1479e108ed408ff))
* 处理领退料单维护报错丢失问题 ([4583e9c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4583e9cfc15d0180276837fca43b67bb9164246c))
* 处理sonarqube提示的bug ([8c851ab](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8c851abb76824ac5c442856065416f6710742ccb))
* 创建送货单限制最小值 ([ede0d9c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ede0d9c0d45ae14bc4fe9bea44a2135c41263d42))
* 创建送货单验证信息修改 ([f38e953](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f38e95342e318c947218387f75402d1db2b40939))
* 创建外协补料单允差标识保存后禁用 ([58305f4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/58305f4a047cccde8d2ec63be8936931fba2f1ef))
* 创建外协发料单 限制套数最小值和小数位 ([2daa621](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2daa6212f04283a36f5a861dc0d243bb701da85d))
* 创建外协发料单, 取消成功后重查, 直接填入 instructionDocNum ([ca258e3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ca258e353e71f2d7b73a66ea5ccd3156f157d5fa))
* 创建外协发料单表单顺序替换 ([b4a302a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b4a302add871597920056ebb9891d01d4582e542))
* 创建外协退料单允差标识保存后禁用 ([92d99b6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/92d99b6d19595c8d3a781f7eac5e28bdac6a853e))
* 多语言和列表字段修改 ([3741702](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/374170201ed5218a938e693c5dccbbc8d2c31fe7))
* 领涂料平台详情子库位lov替换 ([794683d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/794683df5b0491efc170c3b462040cea8547c9e2))
* 领涂料平台优化 ([85adf54](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/85adf5475493ca426edc2d2a2e24ee75c4edfd2e))
* 领退料保存校验逻辑修改 ([ebb849f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ebb849fac397c3a3181e0a68bdc80e091c2d7d18))
* 领退料编辑保存接口 ([e665a73](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e665a732c39da5a5f0f39b61956a941c9961cbc4))
* 领退料编辑保存接口传参替换 ([d6e0418](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d6e0418d100f0d64cd84556c2363d6f45cf67909))
* 领退料创建切换类型时 清空目标仓库和库位 ([b96e196](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b96e1965865c88527645a56e024d3730ac9a2215))
* 领退料更新详情字段 ([2cd4431](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2cd4431158b55a3b7d2253629407587c506dbbb4))
* 领退料接口改成晓龙的 ([7df4343](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7df4343d1581e08299e5ea76ab8e4238aa0dadbc))
* 领退料平台请求增加参数 ([a3493d7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a3493d7fdd9b1b334f7f7e86d75dc80c6da88d66))
* 领退料切换站点生产线清空行 ([84fa561](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/84fa561a3cc2eadd0310ae657554f7c10fc23af1))
* 领退料详情 编辑后保存修改页面状态 ([28c8837](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/28c88378e0b0c972612f4c073ea62b1d518c7c6e))
* 领退料详情 行和来源工单的关联提前至行详细请求之前 ([018682f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/018682f2c23f801c593159314eabdbb1221e401d))
* 领退料新建头默认信息处理 ([6c8feb6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6c8feb67ccc12a1eb4802f5edc7c553fc0664a0a))
* 领退料行删除扩展属性 ([2f90dde](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2f90dde609a0e3c850c668deb1509fdc07c82062))
* 领退料遗留问题修复 ([73fef37](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/73fef376609998411ce75b48c3a0d9631bae9666))
* 领退料优化 ([ea52dad](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ea52dad02cd0eefe64f2fd48dec69c65869be845))
* 领退料状态切换添加提示 ([8f88305](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8f88305a147502bf63f9ee89fb953ccee92058f6))
* 领退料loading 优化 ([316b164](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/316b16416ea28ba951c654e39159a0ef0e4c0a27))
* 判断是否进行了二次盘点，需要根据是否有返回值来判断 ([2b384e2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2b384e2d589a43f464ea7f9f1905df1bea06be1c))
* 签收日期字段错误修改 ([df4ac1d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/df4ac1dbd1766eb1aef0e840549a70734e631438))
* 删除备注, 添加生产指令详情跳转 ([c1584af](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c1584afe35ff38692071ea2a5cd7736c365a05fc))
* 删除无用log ([b6ccaf3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b6ccaf32cca9740814fb1a72440fdc9c23745a42))
* 外协发料单回填单号值修改 ([cb0b9f7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cb0b9f742bf513a2da12a088c1429212f81a3a11))
* 完善切换状态和删除来源工单号逻辑 ([33c34f0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/33c34f0e058af689bf05a93c888e00abdd76d7cb))
* 修改采购订单条件位置 ([e59a2d4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e59a2d4a08a3c24ac96a167d812687d015079a4d))
* 修改领退料多语言 ([5e16cda](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5e16cda14b3f762a7cb8af82d9961fa00da0040c))
* 修改领退料多语言 ([0e590cf](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0e590cf4a6688edf2034cf3b6a0a147c483432e1))
* 修改领退料工作台 扩展属性表名 ([a52f71a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a52f71a023ea66832de45c54a060c8971d556e67))
* 修改领退料平台 物料批明细文案 ([3dda093](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3dda0935e6ff716b7115dfcd25978f32ba8975dd))
* 修改领退料详情表单布局 ([1e329b8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1e329b8a1f395b5bfe59ab6c21319a8e9343dfcf))
* 允差标识文字修改 ([1516e9a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1516e9a2653084e7498b4de0d8bf73d714f83ac3))
* 指定规则维护详情页行内修改重复 ([43b3f6c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/43b3f6c3cbc5fe86db945a00138974804fa8d363))
* c7n table 新增批量取消选中 按钮事件监听 ([d66ffae](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d66ffae46050435c442709ef6b3819d12970e01a))


### Reverts

* Revert "test: app.ts 内容注销" ([788a619](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/788a619185ebd2846c3dd6639b4a10bbc94b7965))
* Revert "Revert "feature: 修改env.yml"" ([0db4145](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0db414560aa8ddb97ae53ff3a6619c3bf71af735))
* Revert "Revert "package.json"" ([e9d7cdd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e9d7cdd91f5c5b19bbac8170d9ae8da1ea82e465))
* Revert "fix: 领退料遗留问题修复" ([eb4b9f1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/eb4b9f11e07bfb093f1588c42536b81f76c83322))
* Revert "fix: 修改领退料接口地址" ([3167f67](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3167f67169e534bb9797ef88a5eb08235d13bece))



## 4.1.1 (2022-01-11)


### Bug Fixes

*  采购订单管理 行 组件信息按钮判断逻辑修改 ([60cb6fb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/60cb6fbae7d14d9c3d4f1b225d5f0def4d3cfcfa))
* 保存后不要重查，防止新建数据丢失 ([40e8692](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/40e8692a5477b57f9a43ba615c171745f9be66e1))
* 不配置站点时页面禁用 ([1b7fd3e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1b7fd3e28d428f72072dea10c02def10f618a106))
* 采购订单行选中项异常修复 ([7e94f5a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7e94f5a4b6a90a920b0886c09c7e5f868cdd5324))
* 处理多语言不查询的问题 ([12093f5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/12093f52cbd602991614a4d6674d93b31131fb95))
* 处理供应商地点问题 ([ecf942d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ecf942d5a560121bb2366ca6438f894f9698ccd7))
* 处理课题维护第一列的渲染问题 ([4a65ec2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4a65ec2adf922c1b63911a2aefd84d82a01bf2fb))
* 处理制单数量默认值和小数位问题, 过滤已删除列表行 ([c2c01b0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c2c01b000d3d780ee108b5b72694d6a0669b5e34))
* 创建送货单默认带出制单数量 ([ed58738](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ed587388e51469fd0110364b6a617549a047f544))
* 多语言问题 ([42248b2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/42248b28e6fe9311937de922cb26403ae1aa1d89))
* 多语言问题 ([273a7ad](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/273a7adb88c4761edcdfa4cbe4e916ea34966ebe))
* 供应商地点问题处理 ([a4fb62c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a4fb62c6647b138a9f020f4d331c5f0c9dd6bcd0))
* 供应商地点ID 处理 ([5ec7719](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5ec77197cf152b4103ca4be6f2004b3174bd622f))
* 解决物料计划属性功能，保存时无法清空bom和router的问题 ([5baf236](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5baf236dcfae73320524564b6b68dd685aa07115))
* 禁用字段禁用错了 ([c0c57e5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c0c57e57d82ef94029c4cb1f0bda587397aa86ac))
* 精度问题 ([997f074](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/997f074176993fa7ddccc9b723d1fb1b0bdfa0f6))
* 精度问题 ([bd10528](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bd10528b081809db9f6ac92d1a0ca1b380f36daf))
* 删除创建送货单是允差限制 ([1a92026](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1a92026188f1a65e9d7bdeb4861f47adb2a6418e))
* 设置为6位小数 ([6deea16](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6deea1688788f46649ae66f159a228c986dbbdd1))
* 设置为6位小数 ([99f93ef](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/99f93efd69853218fb812c9da139f43b80d67f5e))
* 升级后全局样式异常问题 ([176b32c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/176b32cb0185260ff7b6321bc7acd7c58e983484))
* 送货单编辑失效修复 采购订单组件信息按钮禁用状态判断修改 ([a35f34b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a35f34b370099ed77e73433cbdc108ea2c581d63))
* 送货单管理行信息取值字段修改 ([e5201f6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e5201f6dff5b2959440293b6a5c9dd746232f801))
* 送货单管理状态变更修改 ([1d88027](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1d88027763eacbc88f2d50d0b3134bdcff42fa4f))
* 送货单号列固定 ([9d9dd7e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9d9dd7ec94a04a26571312563b81ec39dbd214c8))
* 送货单列表查询条件删除加急默认值 ([4db7a28](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4db7a28cb0eb3d6c2980a288eb71e7e304bdee7b))
* 送货单列表返回时刷新, 送货单详情时间框改为日期时间 ([b197190](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b1971905abf75b9798e2274893ed94c0c717aa84))
* 送货单列表行添加站点字段 头列表站点删除 ([b163dc9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b163dc90fe2c4440da011ad167f3c89f3ea4b318))
* 送货单物料批数量限制修改 ([0480cd7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0480cd7783aae1123e7804175cbfadd45027826c))
* 送货单详情行列表字段修改 ([6049b8c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6049b8ca8703920cb1152d862b5a847c3e27cbe4))
* 送货单行前三行锁定 ([00fd1ae](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/00fd1ae05f33333df9fae7e0879707b2bcf126fc))
* 添加必输项逻辑，修改输入框前后缀 ([29c4e1d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/29c4e1d41d89a4b3273ddcafd8677d86014af1ca))
* 物料计划属性维护 清空站点 物料时报错修复 ([07e0897](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/07e08973acf91b10b90a8457e52e1c97515f3920))
* 物料站点属性功能，从启用版本变为不器用版本时，要把默认版本带出来 ([a77ab14](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a77ab14a8905337c83e5d54126dafe38830c514e))
* 新建分析控制图，保存后跳回到列表页 ([93c4d7e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/93c4d7e75bc05c22832ce23ad8402b61223f36cc))
* 行信息接收库位可修改 ([d8b90c5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d8b90c50d297ad3fe56f43c26d3f051b312f067f))
* 修改 送货单管理 目标库位使用字段 ([2de9c23](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2de9c238c7027e837945a5b0d392281678b594c3))
* 修改创建物料批数量限制 ([c386817](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c38681740c99c1a7d7f765edf44272e3db5eb2d9))
* 修改来源系统字段 ([9b56328](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9b56328bf326d99828bb7b5e5e41dcfa5e7468e2))
* 修改送货单编辑页字段 ([d6c46d2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d6c46d2bb4a8f895b91a6c26e8e05eeecee20760))
* 修改送货单创建选中判断 ([9ace87d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9ace87d591d9cf888ae2c0ad5b85b5a5be71cbb0))
* 修改送货单管理 状态切换问题 ([739b8f3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/739b8f330eebf9512eb4bc34bf136fb393532f5c))
* 修改送货单管理创建人字段 ([8f0b4be](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8f0b4be2fb738b16e532d7d9dacf2b35e04ab92e))
* 修改送货单管理列表的初始查询判断 ([9a518ac](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9a518acd77223d21af52328b900f41af07da008e))
* 修改送货单管理详情页面行信息字段 ([f992b36](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f992b36d5d7a639d75fd03715d2f71e44f76b766))
* 修改送货单状态lovcode ([7241647](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7241647f4fcb8b32d0b0da29a52b626d0b0b094a))
* 修改跳转路径和列名 ([aa5e5da](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/aa5e5dad8db1a1cd53455ce80cbc9430c66075ab))
* 修改小数位数 ([b9f3590](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b9f3590a71b94600c915ebbb0f41d9b6578fe445))
* 修改需求日期字段 ([89318b7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/89318b77a29cb7d6bdeb25c8b0377bab847e608c))
* 修改站点名称逻辑 ([b22c080](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b22c08016e5f9f875d75f7b71aefd949fa8d69e4))
* 指令执行规则维护 排休判断修改 ([214dfa4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/214dfa4f83a10256e4bd924d899684d73c8338d0))
* 装配清单取消编辑后，组件行上的数量没有重新计算 ([99909db](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/99909db6796fcda01f8343c5c645fe5b46d3158a))
* build时colors报错 ([b2e4e24](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b2e4e24de9a8d89e056b4a500f9e71b50ea2a951))
* cpk展开后无法关闭的问题 ([cf1eac3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cf1eac379ffa00490b59e060d658020235b68612))
* iqc 平台列表 结论字段编辑状态修改 ([9e29390](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9e2939012115b7189108dab323eac4b1a0bf673f))
* iqc检验平台 库位LOV添加一个参数 ([6456ad0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6456ad03a66365e9530357cdb2fa512755f0c086))
* iqc修改 ([ffcb863](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ffcb86342e0060f7d5e797bd72f3faef408d4852))
* modal提交按钮添加loading ([a62648e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a62648e082f2697ac1d6cc0cd05782dcd42a978c))



# 4.0.0 (2021-11-17)


### Bug Fixes

* 扩展列入口按钮样式异常 ([39b4a2c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/39b4a2c14687ab70fd7688d52922de095d7e172f))
* 生产指令 执行作业 实绩信息刷新处理 ([6103918](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6103918c15efc621da3c4d27980a150e42b35b5a))
* c7n table bar bug 处理 ([142732f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/142732fa8961995b6eec9443fb72c12d97beb889))



# 4.0.0-beta.11 (2021-11-16)


### Reverts

* Revert "update: hzero-front" ([e5b8650](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e5b8650286f95fc8a5a756b1329fda2c2ee454e4))



# 4.0.0-beta.10 (2021-11-15)


### Bug Fixes

* 生产指令也存在这个问题 ([0d9c446](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0d9c4463040753733197833516c3a466a0845d89))



# 4.0.0-beta.9 (2021-11-15)


### Bug Fixes

* 解决传参时undefined转为字符串的问题 ([5071c09](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5071c09813f986d2adbc50fa44cedebdf57dfab4))


### Reverts

* Revert "修正" ([7be57de](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7be57de3a28d749c025102fa4c5b797cc3806aa4))



# 4.0.0-beta.8 (2021-11-12)


### Bug Fixes

* 由于oracle数据库没有空字符串，所以传给前台接到是undefined，dataSet又自动转成了' undefined'字符串 ([f9cd6f9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f9cd6f99886c1e5130bb73190008effd185ef23f))



# 4.0.0-beta.7 (2021-11-12)


### Bug Fixes

* 按钮错位 ([c33b01c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c33b01cf3988d72ec4760478e51d0ceffb6d8d86))
* 物料配送属性维护详情显示异常 ([15ee584](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/15ee584712a78f1966cca0ae0085a44dba1f74d4))



# 4.0.0-beta.6 (2021-11-11)



# 4.0.0-beta.5 (2021-11-11)


### Bug Fixes

* 班次作业实绩报表-限制物料及物料版本 ([833f373](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/833f37344644117374cfa64837875bca65bd4ced))



# 4.0.0-beta.4 (2021-11-10)


### Bug Fixes

* 隔天显示会换行 ([cb1c08e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cb1c08e1ee78f4c3cb57537cbec8a2e21098e6fc))
* 工序作业平台获取站点 ([b945ac0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b945ac097f136a7ba948464ea61d935929fe024e))
* 生产物料默认带出的版本是时间靠后的版本，而不是当前版本，文档要求是当前版本 ([4eb98d2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4eb98d2cc3bf16e9ff0cd9615aa1b5df5becbfbc))
* 无desc时不要显示null ([46064d9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/46064d954b990785e4b9e08bd485952160df1efe))
* null -> undefined ([e814dd4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e814dd4f334b1638a6ad4d824b137cdc9f7df4d2))
* remove cookies ([ea280a6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ea280a6fc009f08ee43c677bc80d2a5ca9153b80))



# 4.0.0-beta.3 (2021-11-10)


### Reverts

* Revert "fix:升级人员技能依赖" ([c74ff4d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c74ff4d2c3107d62635ff761f488d6ecb2aef48a))



# 4.0.0-beta.2 (2021-11-09)


### Bug Fixes

* 班组维护样式修改 ([a6c9495](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a6c9495e863a678e99407a61ace92d31d8f1da87))



# 4.0.0-beta.1 (2021-11-09)


### Bug Fixes

* 班组维护样式问题 ([41fdd94](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/41fdd94eedc3396bffe3f17784d1b77ed6b87225))
* 多语言code修改 ([c27d63b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c27d63b3667092bd84f77f643767332ff5ebf84a))
* sonar bug ([4dde50f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4dde50f67b26fe36ffd8f56fa4c559bc7b943a3b))
* sonar bug ([b5f125d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b5f125d3aab091b05673ccb08347566931ac2e46))
* sonar codeSmell ([1985ef6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1985ef6116215e54861cacc7c13e6336027b7b67))



# 4.0.0-beta.0 (2021-11-08)


### Bug Fixes

* 1.点击拆分合并按钮，再点击执行作业拆分，退出，再点击执行作业合并，退出，再点击返回按钮，界面白屏。2.执行作业装配清单/工艺路线变换选择指令，工艺路线，保存时异常报错 ([513b615](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/513b6150a104ed0a15520614a4ae0ff1f91b6905))
* 处理物料站点属性维护问题——物料没有生产版本是，也不能修改启用生产版本吧标识 ([b089a6a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b089a6ab2f191ed3a30f8c6870b0d5c3d30f5c96))
* 处理在非第二套主题下，多语言地球图标不见，无法修改多语言的问题 ([c7f1179](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c7f1179d919fa2a8b808e5d4472f5a599383738a))
* 拿回yarn.lock ([bae0e01](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bae0e01472bb7e4c35433f8d3e5d4670d00c5de4))
* 请求返回success为false时，不进行下面操作 ([2988b94](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2988b94c5df940ff2cca40dedbc6e4e9914eeb0b))
* 使用useRequest时，如果不传needReturnObj，则接口报错时不返回值 ([ba99e69](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ba99e695a7c5b47496f3ba707e10f1cffbe81543))
* 数据收集组，当关联对象为物料时，修改编码却无法清空关联版本 ([28d8ae1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/28d8ae10828fc77f6f7424586dd64adf0beeb017))
* 修改备注 ([7230c43](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7230c433fb83e7ae36f837e37d56744ebddd3e2a))
* 修改错误多语言，修改物料版本时，清空生产版本 ([e91d280](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e91d28039ca9b8f12872d2b18f66f2052a886828))
* 需要区分bom和router的类型 ([ad60e3f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ad60e3f04ef87053946a7e0033dbd8e36e1053bb))
* 寻址策略子策略坐标系无法保存处理 ([71eff92](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/71eff92990d838faf2d3d366b1d30e197e6d2342))
* 站点id可能为空 ([8c3ea3d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8c3ea3d3cb4f34d680886f3b38171d20cdf0fac4))
* bug fix , 修复无法展开的问题 ([d40b462](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d40b462c8d5db885f30ba7a7ae9f62ac06d2c467))
* sonar提示的bug ([780bed7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/780bed79ec174d5b1da59dc1a7521f0f0977ce4c))


### Reverts

* Revert "feature: 添加前端性能监控" ([a86134d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a86134d5558ae0096ecab8b8b57cb86723f069aa))
* Revert "feature: 退版本" ([ba63c1b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ba63c1b2116a211b8b43af9c2fd6c021cc5dc390))
* Revert "no message" ([b5f1306](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b5f13064fd33a0631bdab77e305dd7339c78b545))
* Revert "feature: 物料站点属性维护router bom列表字段修改" ([2ab1274](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2ab127437903de2a9e3ac1374628598900f88336))
* Revert "feature: 修改物料站点维护bom router 版本异常" ([bff4ad1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bff4ad17ba54ca6ea70ffe24934fb6661d8fbccd))



## 3.3.1 (2021-10-08)



# 3.3.0 (2021-09-26)


### Bug Fixes

* 多语言问题 ([98a4fcb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/98a4fcba74a5b4488b1280b887674b72e2d00166))
* 分配组织显示问题 ([0380131](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0380131006355cb2d31cefd6b21413a59a8db9fc))
* 工序作业平台查询报错 ([adf8349](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/adf8349f21dbd034a9504a24d5afa156cc05bcd5))
* 工序作业平台登录select组件key重复 ([9175f81](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9175f81f93db8581bdb43352c1de7096c0840e8e))
* 工艺路线维护操作工艺顺序反了 ([145c889](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/145c889bacac797f129f2f2510e493b0a66edccf))
* 工艺路线维护操作工艺顺序反了 ([7bbe732](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7bbe732f4fbdcbed441b5d696a76f90cfe7bb39f))
* 少传modelName ([e2eabf8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e2eabf816cf4c15444ab447001b7f50c8a09be7b))
* 数据收集组扩展属性报错 ([4f1253e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4f1253e78bd9d4aea93b44ea96fb5ee0cb25d58c))
* 图标删多了导致的报错 ([48602d0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/48602d035b436cc5eac38854d8abaf0fcd7e5334))
* 图形化点击连线报错 ([62e149f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/62e149fec7483e7eeb7314fb28cc3af9287ed812))
* 图形化类型组typeGroup替换 ([9f68d2c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9f68d2c4fe3a74692b4706da5ab65c71792836b6))
* 图形化嵌套工艺路线lov为传统lov ([c7ff99c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c7ff99cda8a91fe97c4df71129d0f98fd8f39aea))
* 图形化确定不能点的问题 ([8260a83](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8260a83966610c72488e58753e5515a032daac49))
* 制造工艺路线接口报错 ([ef5ae23](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ef5ae234da68b2b4dd13ea64e8e73b1771b2fade))
* 制造装配清单版本接口报错 ([db46384](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/db46384728365b1133209a463ec6996596033c9a))
* 装配清单，modelname组件没传进去 ([a8c68c8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a8c68c8f46690324009485d20b273a4488a26fa9))
* 装配清单多语言 ([3f268f7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3f268f734cc7306a284fd43c7501a669facf11c8))
* 字应用管理报错 ([3901b82](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3901b8204ebe4309fcc65fff1ac9fa75189c9ccc))
* loading error ([20f25d9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/20f25d9e487c824490fdc365131780118ca3d178))
* loading问题 ([fd87e34](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fd87e3417f2bb9e991d8395c5c22add8a9774854))
* MT.METHOD.SUBSTEP修改 ([9a5206a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9a5206adc8ad211580bfc24b9b318b7eaa327f9c))
* path ([09f46a4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/09f46a4664349e8441bcc476dea5ba2b11087d6c))


### Reverts

* Revert "feature: !canEdit时禁止选中" ([704f43d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/704f43d0e62628e672dd7f1e86757d8be2c5d50e))
* Revert "feature:库存日记账取消库位编码的站点限制" ([7a3f40f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7a3f40f3cc69933255acbe6cee6e6860960c058b))
* Revert "optimize: 重命名" ([e91adc8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e91adc8683fbaae8bf22ca5f26e6c4ce94677a06))



## 3.2.2 (2021-07-23)



## 3.2.1 (2021-07-22)



# 3.2.0 (2021-07-21)


### Bug Fixes

* .hzerorc.js ([4b91045](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4b9104535a789bb85a8a683c3c90163a1e8e36ae))
* 变量取错 ([352f2c8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/352f2c80a4db366b5473ead8a95029a4e58a403c))
* 表格高度 ([366640b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/366640bd302aabde8ef8dd49db7d67ecc0224505))
* 采购安全库存周期逻辑 ([4050cca](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4050cca36fc226bf5f3b807d395c059bac96ffe5))
* 采购属性帮助文档取消 ([3b8c2b6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3b8c2b6338ca2409c8f928dbee0bf78a9e63a125))
* 处理为null的情况 ([d778416](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d778416551d2504bf5e648cf4f14ee4017aadbaa))
* 处理只保存样本标题取值报错问题 ([99d5494](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/99d54942974dd1c64eb4be832aea8f54e21e62e5))
* 更新数据时重新渲染 ([b03aa31](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b03aa31974cdcdf5c7d3b1ff6f6cd6946d04c774))
* 工作日历列表 跳转按钮样式修改 ([d60cd43](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d60cd43b477bc10e32f929f182d006b90b6f49b0))
* 供应商地点多语言 错别字修改 ([679292c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/679292c9e6eead0dacea0251e96d6a9e257dcfc6))
* 供应商地点多语言 错别字修改 ([8ff1353](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8ff1353db83a3c3adf39cc46631a08d7a7c4c5c2))
* 供应商地点多语言 错别字修改 ([ae21a94](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ae21a94bc08f9d5f5d6a2f4e23520a14a44c4d94))
* 规格判断完善 ([337fa79](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/337fa791ec01550bbcdfc13fac2549040414a5d1))
* 计量型的没有存数据 ([bd1dd46](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bd1dd4659589c19c728a860d85f53f65e059787d))
* 课题维护，修改样本名称不保存表格数据 ([e1fac9c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e1fac9c8d07b9a467c62ead43a361b22da342702))
* 漏掉目标值线 ([41ae4f4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/41ae4f4c5acef992b77396fde6af66cd7977979d))
* 取值错误 ([bd50c3d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bd50c3d7e5e2c394590c22bfbb1dba8e63673bf7))
* 删除测试数据 ([005f7ae](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/005f7aee674853a32f28bb5fe9f611b4290ffda8))
* 删除测试数据 ([2f96c81](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2f96c81ee43d97cd79af6181e45e1fd3bc95547b))
* 删除多余冒号 ([5b37cbe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5b37cbe5d8d1802ca43e99e61d0b7df942d4fd2c))
* 删除样本时间列 ([18819f1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/18819f19f8b3f59f9f01b633f0ef71551a977e7f))
* 删除console ([267bcc6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/267bcc6b08f1e184876f34045412619047eed2e9))
* 删除log ([cba7c3d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cba7c3defa02ca6261032ded566c4a85ee634276))
* 事务事务范围维护 新增页报错修复 ([271f515](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/271f51520d6a46915b0cf408130e1b5c798945f3))
* 添加 SL线 ([e0f3e06](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e0f3e0665dd92bff42c033274dde6d0a105e7083))
* 跳转路由错误 ([5885bb0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5885bb0d03f9dd98821ba102e43ea26b1cb7f8c2))
* 图形化连线报错 ([f7a10b3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f7a10b388f21f6a57c274ec3b0796dcf70007afd))
* 忘记处理null ([7d59683](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7d59683f29e77fc806a11a0f7af15372ba9fc71c))
* 忘记return ([edcd233](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/edcd233e13552988814340222173e97597c2f9a2))
* 物料采购属性 ([39b97ab](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/39b97ab2a55b81ee0771aaa1c3098299acdb3085))
* 物料存储属性维护 添加物料类别描述 ([2b05533](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2b055336fb0ecdd1b180cc4149ef8734dc6d3024))
* 修改上传时Spin出现的时间点 ([ff56edb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ff56edbdd8bb1f82e09979435b0de82f03e57129))
* 修改sl线名称 ([a0fecf8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a0fecf86a7720b4ded0ccd960cda6d997b773704))
* 样本属性要进行解析 ([ae1980f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ae1980fdd33c3c96dbe80b22ccc825e89e14dfc1))
* 原课题维护跳转新建逻辑，没有存subjectDataHeadId ([b20cf4f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b20cf4f5a3a6472c9c1f079a82d26afb55bff916))
* 字段名重复覆盖了原始数据 ([ae394a1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ae394a156e0562cb3fc3b9384427d8d718bb2227))
* 组件开关样式 ([5c4e899](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5c4e89951f8a413bd3afcdca43049245598f8a7a))
* bug fix ([50927cd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/50927cd1cfd429737eb1a4b30846bc5f411a64bb))
* c7n升级1.4.0后样,旧版本样式污染处理 ([836cfd5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/836cfd5247440baa7339a5e42321e79e8a7bd35a))
* card展开组件字体大小 ([d3c88ed](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d3c88edcc942635faa906091122b3f1fca1a4328))
* card展开组件字体大小 ([7a518ec](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7a518ecf2160d6b848c326fc7da9c73a94ebb239))
* js方法报错改用lodash ([f04c2d5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f04c2d52442ca45c7255f49bf696b0ae51a9938f))
* js方法报错改用lodash ([a6e1ccd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a6e1ccdf18408fa30809e5784bb69bd4fe68111a))
* MT.MODEL.LOCATOR传参 ([b9b9ac5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b9b9ac5252fbee4caab3c956d1ceb094c8698f93))
* spc多语言修改 ([c3c4621](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c3c4621753a3825021ee93aedb695cc67a88ec39))
* webSocket有个坑，本地开发时，会走layout里的webSocketManager.initWebSocket()，但发布后不会 ([2d897bb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2d897bb579a1dfe1598a7654d3d74fd66cd2d00c))


### Features

* 修改pro组件自定义查询组件 ([c130286](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c130286beff290f989598b62fa6ff69d016e64af))
* 自定义组件列案例 ([581e40c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/581e40c281bdba016e5d99ad41d999eb15a672d7))


### Reverts

* Revert "feature: 控制控制图controlChartData/graphicShowControlCountService 接口新增参数" ([1a309d0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1a309d0e2d777bcf7f4eb4d7a830b17305dbcf74))
* Revert "feature: 回滚table 和 models修改" ([3e64ba6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3e64ba6bc26e0bbf78045a6fca89f7c3494d782d))



# 3.0.0 (2021-05-28)


### Bug Fixes

* 排队列表查询问题 ([bd3e5c9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bd3e5c9f6bc1d6029d433712f6efc5d5c5ef9d8a))
* 排队问题 ([e913366](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e9133661896194874c71a123eb874618fda324e4))



# 3.0.0-beta.16 (2021-05-28)



# 3.0.0-beta.15 (2021-05-28)



# 3.0.0-beta.14 (2021-05-28)



# 3.0.0-beta.13 (2021-05-27)


### Bug Fixes

* 修改问题 ([bec4dcb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bec4dcb0a1e5cd05d3ed3e39087fd1130e3f238a))



# 3.0.0-beta.12 (2021-05-27)


### Bug Fixes

* 中位数未设置offset ([fd347bc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fd347bc80c9ac679cea9ef54a24d8fb7ab554a30))



# 3.0.0-beta.11 (2021-05-27)



# 3.0.0-beta.10 (2021-05-27)



# 3.0.0-beta.9 (2021-05-26)



# 3.0.0-beta.8 (2021-05-26)



# 3.0.0-beta.7 (2021-05-26)


### Bug Fixes

* 装配清单，下载，把租户Id固定死了，现在修改 ([df162f3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/df162f32e9f58a9c4b5044876c6ce4a905be284e))
* font-family添加通用的字体族名 ([a56c141](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a56c141c8f99398792ecceb914a9e8f21428e489))
* iconfont在任何主题下都需引入 ([ad44464](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ad44464e3e33c06e985988d96a7a3b3616065bfb))



# 3.0.0-beta.6 (2021-05-26)



## 2.9.10 (2021-05-25)


### Bug Fixes

* c7n 1.3.2 表格头右边框处理 ([33472f0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/33472f095419fb3057d99895b5f631b14c55dcf2))
* c7n 1.3.2 表格头右边框处理(空表格) ([314efd6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/314efd68bda9f94f830f6d1913b61a308190b906))



# 3.0.0-beta.3 (2021-05-25)



# 3.0.0-beta.2 (2021-05-25)



# 3.0.0-beta.1 (2021-05-24)



# 3.0.0-beta.0 (2021-05-24)


### Bug Fixes

*  临时处理c7n lov 搜索条件无法显示6个汉字 ([4f6cef5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4f6cef50a43390e42fb104a4daed92e177f6c02a))
*  临时处理c7n lov 搜索条件无法显示6个汉字	label宽度改为84px ([f147ed8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f147ed8a28e10ea993b07547e2025bb66d9a7a65))
*  修复C7N table 首列显示异常问题 ([379d444](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/379d44486ce221d6d0c9dc14dd4fec5c1906cab6))
*  修复C7N table 首列显示异常问题 ([c222f19](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c222f19c1cfb111589686c72f6efd76f1b566007))
*  HrefTitle 样式丢失 ([1b7db3d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1b7db3d8cb70ad715abecd432e82c2adc1da1875))
*  HrefTitle 样式丢失 ([a040cca](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a040cca4141bf8df9e8945666c67e19b78cbec90))
* .gitlab-ci.yml ([346bbb1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/346bbb1a15b7abf58a4c635dbdb0d88a00050f99))
* "hzero-front": "1.5.3-beta.1" 样式更新 ([8d0ddc6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8d0ddc66f78cbe6016e9ac951b4c21f9be5cdc6b))
* 本地冲突 ([910345a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/910345a1dc5cfb6be6c22095baff9e2a1c080fd5))
* 编码规则维护号段组合「清空icon」显示问题 ([cf979a6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cf979a6cddd41ba8fee6fb0ee8e04fcec033729e))
* 表格右border重叠的问题 ([22f9417](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/22f9417a9631e865116fd05bc14873b18667441a))
* 不良代码维护/不良代码组维护 样式处理 ([aa46ba5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/aa46ba5f0edc7fc9b26edf29b054e108095f867a))
* 不良代码组维护 tab切换报错修复 ([f094011](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f094011056934417ab1b3d6bd0905ca6c87335bf))
* 产品追溯，行key用的不是唯一主键，改为使用uuid ([69c808b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/69c808be6cf7a9a48b31920a9f57b92582e7364d))
* 处理 hspc 被影响问题 ([4e72d19](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4e72d19869d20f0a1e4991b8bc6f84b4a4262f62))
* 处理 hspc 被影响问题 ([b8fced9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b8fced95e05361bc1b04d91280356ca5134bc67a))
* 处理了c7n站点维护的一些bug ([aea8c4e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/aea8c4edeb27ecfe01c17249e900af9e491c79d8))
* 当最大值最小值同时存在时，最小值不能大于最大值 ([2a29e29](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2a29e295e175e8d14712a21a86aca302b59559e4))
* 当common模块有改动时，并发编译会失败 ([fd60321](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fd603211b24e374ed8ea8c92db0abf2a8ee81ddc))
* 调度平台配置 在制品报表问题 ([0ee633e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0ee633eabbcdf87fd4707a3c699f31b36512a682))
* 多次展开会生成多条数据 ([9530ea6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9530ea623323bc2c6560c3ea8d7dab8bf6b8611e))
* 多个tab会触发多个query ([2e8a286](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2e8a2866a292cf8f98d5e9a46ca83b74e08b4f46))
* 多语言需要以tarzan开头 ([a2e2432](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a2e2432b9bd21553f5aebaf61971151cb5a42506))
* 多语言需要以tarzan开头	补全 ([0d69385](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0d69385cca61a74237995f20e347c4a13f4583d4))
* 多语言用错 ([8e4c059](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8e4c0596baff6da8f0181d42984ac0ccc2e2932b))
* 方法名改错 ([2376910](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2376910211686c4efca28ae17c2d320e30675843))
* 方法名写错 ([c84b656](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c84b6564d4357c8eb1c5a991325e666930783768))
* 分析控制图slider在菜单折叠展开时不会自适应 ([9e56c0c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9e56c0c4828eb3de264d7ec8d32bd77c44bf55f1))
* 隔离modal和page-content样式 ([edbe108](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/edbe1089741abe497ac62c8be925e80f9c0c9c89))
* 工序作业平台 生产任务列表退出时清除 ([63ded54](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/63ded54d9db4960ded361f05b88032e7a1973cc0))
* 工序作业平台 装配 添加 revisionCode ([6a4bdf7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6a4bdf7034771ab8a417891c62fd123797f3eff8))
* 工序作业平台-页签点击刷新后班次和工艺未清空 ([ff9fd97](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ff9fd9711f1dc95ea45166a8990c234973e05c4b))
* 工序作业平台，没有最大最小值，数值类型应也可以输入 ([30a2881](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/30a2881199b1560c506b5276f3bb59d3ef5b75ff))
* 工序作业平台防抖函数导致排队报错 ([4831c1c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4831c1c13154efd320b34f1b64232331e5f9d965))
* 工艺路线维护 表格样式处理 ([ae412ae](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ae412ae73683a366c9ff3e3ecc7263589fc5dbdf))
* 工艺路线维护 表格样式处理 ([af5772c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/af5772c90175c49a8050be99f9b501195685990c))
* 工艺路线维护 语法错误修复 ([585f319](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/585f31943fe14e68dcb042a5d4833098a4955797))
* 工艺维护 问题处理 ([f4c8d09](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f4c8d090c0be872d47834caf37f363ec3995a716))
* 工艺维护 子步骤处理 ([baf20fe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/baf20fe12eeb96ba768b03a55e35287422e45f75))
* 工作日历按钮权限 ([5adba0a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5adba0a3833176bbe6780b0e1e87a7cd8912846b))
* 解决登录后页面空荡荡 ([f6cc00e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f6cc00e3f63a2482f0dc801abf8ef763cb7f428c))
* 解决复制后没有跳转的bug，以及编辑状态时复制按钮应该无法点击 ([8bbcbee](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8bbcbee167eb2aaeaad0aec3971ba03d90951f52))
* 解决了生成excel下载时，数字0不显示，不显示序号的问题 ([bf27042](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bf2704262ed4703068e9277b6b7b4af3cd9ca872))
* 解决拖拽表格，由于行实例化在render方法中，导致form表单一直重新渲染无法输入的问题 ([adc558f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/adc558fc18982a3232b6ee9c8bdb912bbd5e2265))
* 解决新建计数类型的控制图时，如果没有默认判异规则组，流程无法走到第三部，处理样板数据的问题 ([db8d217](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/db8d217c7fbbbd1f617392a53d7dec4337fe3bd7))
* 解决样式冲突 ([f07bb0f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f07bb0ffe6380165e625862f8b732bda329bc114))
* 禁止图片拖拽 ([8048dd3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8048dd3cd4b48193da52cab218ff3942111ebbde))
* 客户维护 c7n 按钮颜色 ([f3caac0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f3caac0868f09861b676563d5f630b42a157c325))
* 客户维护去除多余样html标签 ([7dfc842](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7dfc842f811b3649aa5f066f49c90885237892e3))
* 课题维护表格数据展示错乱 ([7386323](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/738632342c6722d76c81ac4faf2b564e9bf8fc4a))
* 库存查询 英文环境详情抽屉样式错位处理 ([03e1503](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/03e150345c3471e8ccf68af110a06f845fb7fa74))
* 库存初始化 工序作业平台配置 问题处理 ([91a627a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/91a627a73105b6125fd2d0cfb1aad7781c043416))
* 扩展属性维护 抽屉样式统一 ([8975304](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/89753045019825650214ed78b6ba1b6434e9d714))
* 列表查询限制键盘事件为回车键 ([492ebd6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/492ebd67cfeef6f9fa4edac95e0042a2a039c2fb))
* 列表数值靠右 ([1c4cb40](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1c4cb40fa110217e962252aaac948b42f41b1b76))
* 配送属性详情页面类型可选 ([0b30387](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0b30387693665f7f36aee7189f033247f3be50aa))
* 配送属性修改 ([833ffaa](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/833ffaa9141c7d7030ede2e966a9e57c65324132))
* 配送属性修改 ([6ff0552](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6ff055242f28ef3cbaf18919ba3fdcec0418b6ea))
* 配送属性修改 ([30ee01a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/30ee01ad6f781b714fc71a7a73af8126429e916c))
* 配送属性页面调整 ([e29006e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e29006e1504cffb57334f8b99c22c27ddc33fb2c))
* 配送属性页面定时配送-定时补货类型，配送周期默认单位，从小时改成分钟 ([e9042b5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e9042b5d2afee4273bbddcf045660b285170999d))
* 配送属性页面定时配送-select clear ([1ed0a23](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1ed0a23639f3e2b7f2af33e055adb18acc778049))
* 配送属性页面定时配送列表样式修改 ([4485eb7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4485eb7e85362cd701a203bb7fb0d0215fb3894c))
* 配送属性页面定时配送列表增加organizationCode ([ee2b233](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ee2b2330ae4bdde22f495fa6ed3132d4e3e9629b))
* 配送属性页面修改 ([fd5026f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fd5026f14efe481d8220258b1b1b28e2ccf476e6))
* 配送属性页面修改 ([b9b1ecf](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b9b1ecf9b468f847e88a2493c1a6af4e732ecb62))
* 配送属性页面样式修改 ([af486f7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/af486f75629e40cea8cd425b0376d29729dea0f1))
* 配送属性页面样式修改 ([382bd8b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/382bd8bf0e06a2c7273971dc83093c01ec322082))
* 配送属性页面bugfix ([ba04e0a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ba04e0a4f329841bbedb50f2dc33f8039514158e))
* 配送属性页面bugfix ([d0b58cc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d0b58ccf801f11f6cd6aec19d106aaf66584676b))
* 配送属性bug ([434ccdb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/434ccdb7c341bec0ad1baf5078e20d6ef8dd13b6))
* 企业维护table操作列权限 ([99afa71](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/99afa716b1e2257552b609fa490b89822fc3b12d))
* 启动项目smock报错 ([60165f2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/60165f2731acd568e2dcfe26c2d5370d7029aa13))
* 取消改完返回 ([66f8efb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/66f8efb82c972f1f00dc9fb0a354ca3c76b17744))
* 删除错误参数 ([c8794e7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c8794e707890a3a63a2198c2d1cb77480343f80b))
* 事件查询 抽屉样式统一, 英文环境 ([4f81675](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4f81675f46faca56152785eae4243374fc6a631f))
* 事件对象类型维护页 点击行编辑后,再点击查询语句修改其中内容保存时报错卡死修复 ([c93c8c9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c93c8c922dd31229f4eb704227bd7bc00a21d34a))
* 事件类型维护 modal 添加 className ([c18d7fe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c18d7fe8b582a3ef2b66b969280c5f3de20dcd9f))
* 是启用版本控制，而不是启用状态 ([24771ac](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/24771ac05ce0d60ebda7e022cf906928dc9f7f0b))
* 数据收集组多语言 ([aaaef87](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/aaaef871f6aec92eea96e623dc79d5975e908b75))
* 数据收集组维护 问题修复 ([351ad2c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/351ad2c05034161c9545bfe57eae5f8a8b7d713c))
* 数据收集组维护 样式处理 ([634690c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/634690cd26dd0f3b006ef824166ab0ddc8735f9d))
* 刷新页面会闪一下404的问题 ([83ba581](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/83ba581418434700eb32bb2caf60689b0e6afa85))
* 提升表格按钮权重 ([da5b80a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/da5b80a1897448c01ec93043479256e2279ad19d))
* 替换有问题的 c7n button color ([8c5d227](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8c5d227c0754f80d46470df85194eb52e6721cb6))
* 添加公共颜色 ([f4e61b8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f4e61b8056732d7cb98dadce7cea72ce1b2ad225))
* 通用设置的按钮权限 ([1da4b3c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1da4b3c036d8b7b894a603e39a7341a887c98150))
* 为了使用权限扫描工具，而做的修改 ([d6e9e9a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d6e9e9a9e1912e121082922e9dce9b1cb543d644))
* 物料版本多语言没有进行处理 ([bc36f50](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bc36f501e331026563abb2917df023a67f6ab138))
* 物料类别抽屉 样式修复 ([04dca94](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/04dca94d6419d6c8c741e40e516ea19e9a35082f))
* 物料配送属性交互 ([509df25](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/509df25b5ba31bd3669ee75fa377fb60920c969d))
* 物料配送属性交互修改 ([18753f6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/18753f691373cc111c6e1026b2eee47543c757a6))
* 物料维护和物料站点维护bug fix ([05a8e43](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/05a8e43114701ecb9b4d0b4fb677ba24b610649f))
* 物料维护样式调整 ([c3af73e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c3af73ebd96ff728eae4c445381e6bc7c3c9eaf7))
* 物料维护站点分配二次开发 ([3358282](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/335828274e291a6b4e44c10357118519cdb2182d))
* 物料站点属性维护问题点修改 ([0790735](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0790735a0c9b787ca328cee595e7c42a3d39b813))
* 下载的excel没有序号 ([c65707e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c65707eb5bedb4758f988fcfd1760dbcfe4f5bd9))
* 修复当容器下同时装载容器和物料批时，点开容器的展开按钮，容器行消失的问题 ([168184a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/168184a54e910c9047ea541402c25ee72743ab41))
* 修复消息维护功能分页问题 ([5492ebe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5492ebed02b3e798279b58d322db82df2068c025))
* 修复站点维护页面报错 ([3abc884](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3abc88430502c79ec0acd22587fcaa908cd6c7db))
* 修复之前改错的问题 ([092717c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/092717c9e2ff7ac9b5519bf500ece951e912a610))
* 修改 permissionList code ([2911b0b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2911b0bdd285fc61613f96849f0ede87f121716e))
* 修改编码规则维护号段组合「清空icon」显示规则 ([1ed8f2b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1ed8f2b934653bd9f8f10bf5ba86e7571a2bf0c2))
* 修改开放文档链接 ([9f23a82](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9f23a821060975c6456403be455f7d59f2286eae))
* 修改配送路线多语言 ([e5d196f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e5d196f2a7b07d81a289eb90c16b4995f5bc7440))
* 修改是否可以继续输入的判断逻辑 ([9a32c6e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9a32c6eef362740ce4d03ddedc8044bad5e2929b))
* 修改预览图标，与采集结果状态badge ([b6cd365](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b6cd365f517bac5a9459ca5bb388e2abb0150a45))
* 修改bug ([69b0006](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/69b000608627420275e6991afd10327d5c054b0c))
* 修改routers信息 ([b0e847d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b0e847dea52f294f92bb9784b7f2cb7a5c1629f6))
* 修正修改代码时改错的bug ([c351029](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c3510290c0de51affd47af81c72cbefb4fd80816))
* 页面报错处理 ([cb07e07](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cb07e07d8f2f1fc8db14fe6a366926be9b381bdd))
* 移动实绩追溯 ul样式改用类 ([eba073a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/eba073a3ddd736c990359e39fecf611583ea9cb0))
* 用户权限的按钮权限 ([92a8f56](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/92a8f56be71123c19a1b09df042b4ff1889a2636))
* 重新工序作业平台登录，以解决一些bug ([04fa0c8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/04fa0c8695f8f0b05b03c89232ac91a8b86b8ab1))
* 重置时，清空页面内容，添加帮助文档 ([63cd992](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/63cd9927b32a97b95cb09920e8df9242987a83fc))
* 装配清单维护 编辑时删除站点不能进入组件行编辑,点省略号也不能 ([07d0f63](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/07d0f63c04e34463b76c3a755e22aef825bcdbc7))
* 装配清单维护 table和抽屉table 样式处理 ([0586410](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0586410527cd9c0012e389cde1169e4fc42f288b))
* 状态维护/类型维护样式未加载处理 ([b5da37d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b5da37d2784e72936b55b3b308f6e481884c6bc7))
* 组织关系维护 企业维护多语言处理 ([ca275c1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ca275c101b066e6ff066efe338366741ffe6d3ac))
* 组织建模表格按钮权限 ([d7a0e59](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d7a0e592fb6b3cd23d3d0a8b789a06f13bb4caa8))
* apiVersion:apps/v1 ([f4f403c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f4f403cc519f5ea117d0efbd5e0dba9d1c3f8766))
* bug fix ([0cc216d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0cc216d1bb2b11002f26f22f6148ce037cc2f2e5))
* bug fix ([0651e2b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0651e2baae6d8760e4653b2037bb35d43b37c3ec))
* bug fix ([78a6d7a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/78a6d7a81517a1427d609c4f387e38c6f64034f2))
* bug fix ([2a78a66](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2a78a66fac6d3526af214c0b81e04a11ddbef96e))
* c7n ([bce942a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bce942a32096f679bd7eb4c20dadacba0f6aee60))
* c7n 0.36rem 表格编辑状态对齐处理 ([4a2a5f5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4a2a5f5f3ca561bdd2c9a42c25e9a0262578e3cd))
* c7n 0.36rem 表格高度 抽屉颜色 ([7b136c9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7b136c985900d476f35779aa40c22d72e5e61c2b))
* c7n 0.36rem 表格相关颜色替换为变量 ([a33e241](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a33e2412f400949230ae6e906953f99b13544c38))
* c7n 0.36rem 表格样式修复 ([a02a0fe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a02a0fe36a069526c8b7044b5575a036414a9343))
* c7n 按钮色偏 ([320bee6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/320bee6b4afe1d6932f3758b5a9fe26498e226bc))
* c7n 表格 布局 样式调整 ([3bcc6c6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3bcc6c60ab01eb168a6af1c2f82b3288f4243cc2))
* c7n 测试环境样式污染处理 ([57a5c45](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/57a5c450766e3e9a5844affb7601b6d059da3eb6))
* c7n btn focus 样式处理 ([4a55ea5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4a55ea5f13104ff4507c00b944afe952da241d8a))
* c7n btn focus 样式处理 ([672af7b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/672af7b15baa6d493d074871bec873d3ea4c4984))
* c7n edit table styles ([2388568](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/23885685a7849599811367f059fd04483a4e7fb6))
* c7n modal footer style ([4cb5037](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4cb50371fdf8e9df3792fea75cb1ecf2b440de40))
* c7n to hzero 测试 ([d33a04e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d33a04e69987937b440386a873939e89a0f92cab))
* c7n查询支持keyDown触发(忘记指定enter) ([f264205](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f264205db2b9e0dd1c2e18a6f698be449067e474))
* copy drawer样式问题 ([5c37673](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5c37673c03165a55bce741abfea61338f99ac685))
* DEMO环境的问题优化，全部处理完 ([f1ca5f7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f1ca5f7f41839c5065f3817fd46b0601659ecdfb))
* enabled报错 ([be4af14](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/be4af14adf358fb97696f472c874bfc9e74018e0))
* eslint error ([5891421](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/58914214a9d06c4b2b11a20f90cded27b76e386c))
* hcm-api-front中eslint waring ([85c1311](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/85c1311e956446ee7a0470a3ef21ce9f2b18e963))
* hcm-component-front中eslint waring ([58202cf](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/58202cff2eb6fe508d7bc5b2c2060cb88490ed8a))
* hcm-model-front中eslint waring ([ed93a3e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ed93a3e6669cc8bb53748269af31f2de1177cb31))
* hcm-report-front中eslint warning ([069512c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/069512c5ade7f89b3cc0311dd02f812cc3312fcd))
* hedk边缘程序维护页面渲染异常 ([e4da6dc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e4da6dc667d6f3e2fd748dcb52caf1887c2e2001))
* hedk场景维护列表操作栏渲染异常 ([51cc8d5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/51cc8d5ebb081e0cbda6162fcceed83263b2a923))
* hmes-front中eslint waring ([d871849](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d871849489d1b43e5885ce284ab872a57f0ca3f6))
* hspc-front中eslint warning ([56fbc14](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/56fbc144fdc1a1157ff51fa8473e6d8ebc181eb0))
* hzero-front-hfnt: 1.5.0-beta.0 ([a3b84f5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a3b84f562c51712699075898dc44aab8bf9e8d13))
* less中语法警告 ([0126616](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/012661687e370ffb76fba2e9ad17011f999a668d))
* level改成serviceLevel ([89f1bf1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/89f1bf1690caf179a3a33f676adcebd92931e61c))
* spc eslint error ([bae227a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bae227a3741f6c03d9b11fc8b81fdd6969b66070))
* spc控制控制图openTab的title多语言显示 ([062159e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/062159e9742ed87833937395b9ca84bb5b145875))
* spc控制控制图openTab的title多语言显示 ([1d0098a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1d0098aef617102578fa210250e42422c2a9f6e9))
* spc跳转显示中文问题 ([f74abfe](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f74abfe5db4b122f82edd883726eaf89fd73be86))
* Spin 组件样式处理 ([bbc151c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bbc151cd0fad76aaee3d6ad2b36091983472cca5))
* Spin 组件样式处理 ([746435f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/746435f332f2d40c8a0b28cebef0a84604b45f48))
* table 表格 首列 add 和 delete 按钮样式处理 ([156c3d1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/156c3d15908fbbbba6c9619054bb3ad0ac7a09ce))
* table 行颜色统一 ([ae11572](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ae115728caed357721137b0c5ff296264c804595))


### Features

* 工序作业平台配置优化 ([b9d193b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b9d193b239a6f29d8c3155005378c47cc7f0b032))
* 合并冲突 ([1d500f6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1d500f608be42ed60dea34253b46eab704506a2f))
* 生产追溯优化 ([acdd111](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/acdd111de26c17740556a91f0d3b5cdd12e3db43))
* 实物库存优化 ([9353aa7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9353aa7cdb8e7d9366484f0e31c034118cbbf59a))
* 数据收集项维护及API转换优化 ([5846373](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/584637378170bda8a18f567658b0f32b3f122b93))
* 数据收集组维护代码优化 ([e7ba995](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e7ba995352dc516c170426dc6cf94db879f21798))
* 数据收集组维护优化 ([789d9b7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/789d9b78baf678dc5fbcdfa26f210b08111b13dc))
* 通过链接跳转到物料站点属性维护需刷新页面 ([e53ae4f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e53ae4ff8fc7e55f38de7ca5ed8f6d9792ecfdfb))
* 新区域维护 ([2b6fbe5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2b6fbe5cf3aa1b4f00df560e36e401622c60a65b))
* 状态维护和类型维护编辑抽屉权限集按钮名称修改 ([95870b8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/95870b83fb3270ac3f85bdf7a792d62ab5e770e1))
* 状态维护和类型维护的列表页抽屉权限按钮code修改 ([14a85a1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/14a85a1b0f64836a375e32dd6a9e09ad0424c3f8))
* 状态维护和类型维护的列表页抽屉权限按钮code修改 ([e19b4a9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e19b4a9dd1d23b32093587b4de0c51cde2a368c3))


### Reverts

* hcm-api-front ([92cec49](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/92cec49eae7fdd0d339e0f9dacebd8fc0f45aaeb))
* Revert "feature: 物料配送属性维护 来源库位,目标点位,存储库位 lovCode修改" ([09aaa00](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/09aaa00a828befec83f8509df5dec2e79fc4d2fd))
* Revert "feature: 区分c7n版本" ([c66efee](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c66efee3e3b6b6cad984d4e4f3e92a109b238b05))
* Revert "[feature] 添加依赖" ([e3974e5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e3974e5b17c79ce6d0b4926d258a7781d37581ac))
* Revert "[feature] 处理编辑后组织说明变更" ([98d2541](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/98d25412840b76f3b480830dc4b13b511f303f3e))
* Revert "revert Merge branch 'model-update-16539' into develop	4fa84d30	hdy <<EMAIL>>	2021年2月19日 上午10:14" ([3ce9c06](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3ce9c0666475dc730b64bbdbfe5b996d372e974f))
* Revert "update: 装配清单维护 详情页抽屉权限处理" ([15a2219](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/15a2219358eaec2a958ce0002cf96d7405b4f992))
* Revert "测试 utils/index.module.less 影响" ([7873b26](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7873b26cc3558cbfdb9cdd881c7a94a21ae9da72))
* Revert "fix: 换肤导致按钮填充色不对" ([6762362](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/67623627c3f96907b589af0f1fda9719cc0d345e))
* Revert "test: c7n 污染" ([077616f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/077616f4e164b7055a2748fe64a241f15e9f2b95))



## 2.8.9 (2020-11-17)


### Bug Fixes

* 编码规则维护 样式修复 ([3852050](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/385205064350c189ba241776a198460a1049bd13))
* 编码规则维护 样式修复 ([13504f1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/13504f129121112c650c4e7ce835e7adf226ba9f))
* 编码规则维护 DEMO环境测试情况问题 ([471da86](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/471da86009524dade045201571c2b5b36820ce2a))
* 不良代码维护，次级不良编码和工艺分配tab bug fix ([31d26f4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/31d26f47a17c2723266930467b1e83ed9ec3a434))
* 不良代码组，不良代码和工艺分配tab bug fix ([720625f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/720625f1cc3cecd5a395308cc9f0e3b525f829a5))
* 处理一批多语言问题 ([2822614](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/282261421cde41cf0faa420ab020b719b7a67119))
* 单位维护 1、“收起查询”和“更多查询”按钮的设置反向 修复 ([b938deb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b938deb3d6335896aa37630dcfd00de8da173367))
* 工序作业平台配置 1、查询条件框放置宽度不规范修复 ([916f04a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/916f04a3a0c65e01ac9ff906e2a96ac2993a1d35))
* 工艺路线和图形化，切换装配清单报错 ([0fe455a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0fe455a31cba66d457c4a1d98a90d4dad5bbcac0))
* 工作单元维护 多语言问题处理 ([bb2f559](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bb2f5595a514c616a8865a0bd6134f46ca99b901))
* 将ncSecondaryCodeId修改为uuid ([6868aef](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6868aefd7550be473da743846192ce1a4d0eb030))
* 可同时编辑多行的列表组件使用 'getEditRecord'方法时对比信息只传入对应的单行信息,避免整表校验及报错 ([5b421c3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5b421c3b309967a591976eb298af0a6279d245b5))
* 客户维护描述 ([dba43bc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/dba43bc69d53fefe6c847aa012a467031870da95))
* 库存日记账查询 开始结束时间限制 ([4163bb2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4163bb2b0f5d2598889d9f828955102c1ad3e10b))
* 库存日记账查询 切换站点时重置相关选项 ([d804653](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d8046534a85845a858d4a7795e26864a893e5dd4))
* 库位维护 多语言问题和bug处理 ([f829d20](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f829d20cf2f5591c7d888d546b99e79200b35893))
* 扩展属性维护 多语言问题处理 ([b80b0b4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b80b0b41c01777e2b38088dc6dad1fb923d5198b))
* 类型维护 样式及多语言问题处理 ([08d23e6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/08d23e6d7a64f51f888984acba851a0a3013a0ce))
* 配送模式，指令业务类型下拉框初始值处理 ([fe5a2a7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fe5a2a76ce2975b3c097f13cc21721fc768eb3a6))
* 企业维护 多语言问题修复 ([1e270e5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1e270e5b2b47fa255db5a4b66a90a25849aced0c))
* 切换站点编码，清空物料编码和库位编码 ([a783079](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a7830791ce07caef835281799defda2e6091b613))
* 区域维护 多语言问题处理 ([43652f4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/43652f4ce3e4d3c2f31dfc112d315ed2b89e757e))
* 设备采集配置在新建时，后台传的校验规则可能为空，不能直接转字符串使用，需要判空 ([b51c854](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b51c8542bb5e5621f3351e2e424cf808bd55c25a))
* 设备采集配置在新建时，后台传的校验规则可能为空，不能直接转字符串使用，需要判空 ([91eed3d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/91eed3d0392bd90a24821e8c70c36875def23646))
* 生产线维护 多语言问题维护 ([c81c0ab](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c81c0ab6a31836f82ca495ab7720af691f2c61a1))
* 生产指令管理 切换站点清空关联输入框 ([9bf96dc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9bf96dc972facc321d1bc875b7cbf8b5fe5dfa22))
* 生产指令管理 样式问题处理 ([209678d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/209678dc115260472e7d99fba47f7dbcb684f0cb))
* 事件查询，父事件主键ID为0时不应显示，英文问题 ([06c6096](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/06c6096ec36fdf423c697231eb338d9906e670af))
* 事件查询页 列表操作按钮错误修复 ([01176d4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/01176d4c2b23677e97246d2178a9a986e90ab1de))
* 事件对象类型维护 取消列宽和固定列 修复多出一列的问题 ([e0371a0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e0371a07150f7a7e0a447f6b5028b1d3705120dc))
* 事件类型维护 对象类型非必填项 ([29fcbea](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/29fcbea02f4cae79df10c36da9faaa8a1373bd9d))
* 事件类型维护 多语言 ([24437e5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/24437e5bf42df1fb607527f8c58ffce62e012d07))
* 物料生产属性维护 清空站点时，默认装配清单和工艺路线的版本号未清空 ([33b109e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/33b109e0d260918c374a5fa58356c35866b70bfa))
* 物料物料配送属性收起时 未保存数据丢失 ([9a4c2f6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9a4c2f675e7f1e6283c96db43b6b6bf9fa057a31))
* 消息维护 多语言问题处理 ([61458d1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/61458d186871b041d0ab14f8cabbcd494b9727b7))
* 修复 库存查询 清空或切换站点后 重置库位和无聊编码 处理页面崩溃问题 ([2ace2b4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2ace2b41f9142212116929b863a18f621aebc395))
* 修复 库存查询 清空或切换站点后 重置库位和无聊编码 处理页面崩溃问题 ([3cfe4d1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3cfe4d1d16f85fda24c40b66e2434f6cd68a56eb))
* 站点关系维护 删除成功后清空选中行 ([2108a83](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2108a83d96f07fff2e08ef926f46c54a1f76fa48))
* 站点维护 多语言问题修复 ([3c993c0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3c993c0ee028b38059c8540e8ec7336fc9fa4223))
* 装配控制维护tab组件渲染error ([e0830f1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e0830f1cb2069fbdbfb56a5ba61289b2391c3aa9))
* 状态维护 多语言问题处理 ([4c90a0b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4c90a0b401ea4548122d58db9cfadc65f555db39))
* 组织关系维护 样式问题和多语言问题处理 ([ced1aae](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ced1aae81442330f5169731282ea4b3eb13dde88))
* 组织关系维护 样式问题和多语言问题处理 ([a56597d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a56597d5ebd573a1689ea4ccbea93159eb40d7d5))


### Reverts

* Revert "fix: 库存预留日记账 搜索条件 开始结束时间处理" ([d874670](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d87467076c1e388aa3d19b7ef30a6689e35b6433))
* Revert "fix: 库存日记账 搜索条件 开始结束时间处理" ([abe195b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/abe195b78337d7201e0a515cb14d2c2bc03e84af))



## 2.8.4 (2020-11-06)



## 2.8.3 (2020-11-06)


### Bug Fixes

* 调度平台多语言问题处理 ([c85edf2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c85edf20b5046fe8bf64d2c515c62c93897f8902))
* 调度平台配置/组织调度策略查询 多语言问题处理 ([21bafde](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/21bafde087eddf8c8d909d847878ef2bd3036cfe))
* 格式问题 ([0346eda](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0346eda3a7fcc2a76af8495173eb518cf4d168a9))
* 工序作业平台登陆, 工作单元/班次/工艺下拉选项无说明信息时显示对应ID或CODE ([2785a30](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2785a30b660f1db07b7a9d7eef57594e9b84dce8))
* 工序作业平台多语言问题处理 ([041b5ef](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/041b5ef9388f579ab76107f10de579bbb3711815))
* 工序作业平台生产任务tab 搜索按钮不对齐修复 ([ba028a4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ba028a44b414ae5bdd99ceacd7f25018da4c4774))
* 工艺路线维护 添加工艺步骤无报错提示问题修复 ([5533b58](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5533b589499eb1efba248f0d2bd9fbbaf94f2b0a))
* 工作日历菜单下相关bug修改以及优化 ([54b2040](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/54b2040b78bb5eff12c9dbb3e48a447f4fe45d81))
* 删除重复报错提示 ([19db127](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/19db12739c08c0e763b985918192f38d67f8976c))
* 数据收集组 数据项加减号非编辑状态应有禁用效果 ([9c16fe5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9c16fe5029a9f199dad9e6f28fef38ae5f73aa0e))
* 数据收集组维护 编辑或新建-关联对象tab 带版本的Lov项, 版本和Lov本体错位 问题修复 ([d783a3c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d783a3cd845b560b336aaa6b4da8740e3b4835dc))
* 数据收集组维护和数据收集项维护新增编辑页 'TLEditor' 组件报错问题 ([4641334](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/464133447d2e005a406598724444e867bf7b7cd5))
* 数据项维护多语言问题 ([d73c6e2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d73c6e27b9ce0a359bc3ff555385ba357ccabea8))
* 修复点击扩展属性页面崩溃问题 ([d8eeaec](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d8eeaecf0cfa598c4d7cfa13e86739d1409949d5))
* 修复调度平台登录后再返回生产线选项丢失问题 ([c92435c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c92435ccfcc057178ea593a97cc53013fcdc3e17))
* 修复调度平台配置按钮错位问题 ([8a2533a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8a2533af5c2b1b94ba4217b0a7dc1a484c153cfd))
* 修复生产指令管理详情样式问题 ([d3f83dd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d3f83ddef64ef90fed6838e23a2c885ec5a16c2a))
* 修复数据收集项删除无效的问题 ([d766e76](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d766e76d322dd06c9caebe028b2d47e0cb933eb6))
* 修复同步无效问题 ([a817de4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a817de4962e7df6a0c05ae1d7722f0a37c152873))
* 修改历史时间选择器多语言问题 ([2599c81](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2599c8188d64e99b0bb93d1658d9074d34322b9a))
* 在制品报表多语言问题处理 ([1fea017](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1fea017d2176a67d10672df253f1692065b1e6b1))
* 站点关系维护页面问题修改 ([16be039](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/16be0399fab0be37c88b88dbe76883af13bbdef4))
* eslint error ([670f3b6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/670f3b61540107efb5d52fa62ec6da17eddda22b))
* lov不对齐问题 ([3a56532](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3a565322ac5c8f6c334790e813c61666c05f33f9))



## 2.8.1 (2020-10-28)


### Bug Fixes

*   lovWithVersion 公共样式修改 ([b1679bb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b1679bb598d72ea2d527eebce73cda83f570ecc8))
*   lovWithVersion 公共样式修改 mac win 不统一问题 ([3b6bee8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3b6bee85774f38f8ac54194089b2c9c2446802bb))
*  编码规则详情，多语言框未传objectId问题 ([8b2f1e9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8b2f1e954c54e319e35c8718406a44c492b0a90f))
*  工作日历bug fix，以及代码整理 ([2fe141a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2fe141a93a7f53df461b56b70ad95cdc2a3a59fc))
*  号码段取消时需要重置表单 ([f6e3e4f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f6e3e4f7a607978dde4b73a73786c36d8295a649))
*  新建属性Tag样式修改，序列号段页面逻辑 ([1fe194a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1fe194ae440edcf7e4561e4c93a0fc483b4a632d))
*  执行作业管理改用通用扩展属性抽屉 ([28b1415](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/28b1415cbdee26e8384a597d446ee66a2d3cc647))
*  装配清单，将state的数据作为生效时间和失效时间的默认值了 ([de2a7b2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/de2a7b2f06288e83b48e7deee880154af2d9e95b))
*  装配清单父子表格bug fix ([023c5b5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/023c5b59c66f9c03aed53c46c1bc7f4378b1431f))
*  装配清单bug fix ([cdffdcf](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/cdffdcfe420ca868f7764d0d30ce19afe814ec80))
*  组织关系维护进行过一次未知的修改，导致分组组织时会出问题，回退了方法 ([47cd5cd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/47cd5cd6c75728751718e6f8615241b626f048b9))
* 保存处理itemCount ([fc7312b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fc7312bf8d88a1077c6f4a2e24810abe36d5c15b))
* 保存draft数据时，需要保存copiedFromRouterId和copiedFromRouterRevision ([0afeacb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0afeacb6eefc7030bd568f29575d8664df329d61))
* 编辑按钮加取消，输入框样式修改 ([df9abbc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/df9abbc90c3563f8f6857a78407065a531d6858e))
* 编码规则维护 ([36b8b0d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/36b8b0d39264618e8d2fb1ac1d7972701fd02215))
* 编码规则维护，给_backUpRule设置默认值 ([be5ea73](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/be5ea7373e6a4162d247bcb035a9e2829da15011))
* 编码规则维护，解决了全局流水号规则，修改当前序列号后，仍显示之前的值的问题 ([b9ec713](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b9ec71384c2bf5b760ea89ad7e821d99f87ad2e2))
* 编码规则bug fix ([c61831a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c61831ae569d0c680bf9766bb6e3aad6209709ed))
* 表单顺序调整 ([3bd992b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3bd992be8cd13f293e84d87b7a4a1ae4a58abb6c))
* 表格列居中，最大最小值校验问题 ([7a813d2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7a813d201914a71e7727595f410f6e2a0fb91218))
* 步骤组下步骤，保存时存item.mtRouterOperationDTO.operationName ([3a23084](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3a23084eb32ceffb6667c345b109138461aa536d))
* 步骤组下工艺，需要传stepName，还解决了切换画布报错问题 ([5469c4b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5469c4be8c4456a1521b5856f9b3a95fb09b760a))
* 尝试修复 lov 组件在mac 和 win 下对不齐的问题 ([ebda271](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ebda27141641ba1d29d1429335c21c967fc10e26))
* 处理进入新建装配清单页面时，版本由非必输变为必输，一闪而过的问题 ([d6bb707](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d6bb707260ef202f1a08c64a6effd6a1921e7160))
* 处理图形化切换画布时，数据丢失的问题 ([037aa5e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/037aa5e987d5109c6d51de864223d9aa1e66bc14))
* 处理新建时候，点击取消按钮跳转到其他功能的问题 ([2df3462](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2df3462e7b40a4dbbbe578d0ce2e8dbda864951a))
* 处理组件分配抽屉的样式与删除关系时的问题 ([0257d52](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0257d520aa5361ec2c4a36589feef87ae0be7d75))
* 代码错误 ([b874e7e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b874e7ed8634623c04345561bcd09fde30677e09))
* 当下一步骤策略为COPRODUCT时，code=MT.BOM_COMPONENT的Lov应取materialId，默认取的是componentId，进行了修改 ([617cdf2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/617cdf21a7bd54292c78c41303ff78164d02557f))
* 调度平台 fix ([faad205](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/faad20572f45dc61f8ffff1b57212ce36e99e7ab))
* 调度平台，在发布后，会把已调度执行作业表格中的状态列，由未发布改为已发布 ([42f4d50](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/42f4d501ed2ee8dc76a0d9989f86f65ee8e3daae))
* 调度平台配置bug fix ([aeb6744](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/aeb6744e2805e5cf036fa9e2ce8835232130999f))
* 调度平台bug fix ([bf257f0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/bf257f048df1986a87bb77aa58757c70609e75ea))
* 调度平台bug fix ([f6ac2e2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f6ac2e2d0737a2b1df1e977eb9aff2afc0376e05))
* 调度平台bug fix ([a1925bc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a1925bcc8fe3fce74096787b285d65977edb9f78))
* 多了个border-top ([86c075f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/86c075f80b2e2fa95ee049870b01de529826d6de))
* 多语言代码使用错误 ([51aa043](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/51aa043a5a9a2aac6f236df4fafdd5e0523bf9aa))
* 多语言限制字符加长 ([f23677e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f23677e6992c572536b87c1b4d4ee6477e78502e))
* 二次开发bug修改 ([c564536](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c564536ac94d765721129ed68036909a2c92c85e))
* 改一下c7n的冒号字体 ([f9baa4f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f9baa4f7aa34e2d05fe9999aef1499c15176c18b))
* 给图形化标识添加默认属性 ([4d4c892](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4d4c892766f4d7f33b4e756b97d0dcf349562601))
* 更改组件分配抽屉，编码在前，识别码在后 ([44cb1f0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/44cb1f0ed6ddc6c8d94f8969fbb926527e41d387))
* 工序作业报错 ([42fd4b6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/42fd4b688c1c6fa02113af5082cf5fdeedc83662))
* 工序作业平台-生产任务 列表 rowkey 重复 修复 ([77cad52](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/77cad52378c796ee04b57cf0a636ccb5713f3d9e))
* 工序作业平台，保存后路由跳转 ([8ee3494](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/8ee34944fec5e00f0b752c82abca09d54a1ef5d7))
* 工序作业平台，登录页问题修改 ([6eb03ef](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6eb03ef02842d46f45ec698208340d979275c7ca))
* 工艺路线，保留6位小数，数据收集组维护，关联对象tab字段属性修改 ([6b2bd7d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6b2bd7db95d4a46dff1bd6a3d2ad92fd648cb932))
* 工艺路线，路径选择，取消修改时，需还原数据 ([b2ab140](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b2ab140d0eb4980a7dcfc341c71eaa56cd441f5f))
* 工艺路线维护，下一步骤属性未设初始值，倒是map方法报错 ([eaa4934](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/eaa4934d22708d77cca88296d39d1aebef31313a))
* 工艺路线下一步骤，把没有的值从' '改为null ([699a705](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/699a705ed8e5d3f3ac76f697ff945bc4b12aee5f))
* 工艺路线下一步骤由传code变为传id ([038d5af](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/038d5aff4042ff9c95d85f431a175efac1f001c9))
* 工艺路线已经工艺路线图形化，步骤顺序问题 ([d08ea2e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d08ea2ee094b805d9f737f94cac9e3aaf07c2249))
* 工艺路线以及图形化一些问题的修改 ([e73a298](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e73a298c0ca72c1ea512a71a343a7e6817d2c58c))
* 工艺路线组件分配抽屉，不能查询 ([6449da6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6449da65fb7b98bcc3dfef15cde09e87463ed7ee))
* 工艺相同，步骤识别码必填 ([7181ac7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7181ac7aa359439e36eee7d205536cc09e90eda3))
* 工作单位维护bug fix ([ebad9ae](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ebad9ae84521a339ba7f087075706bd00c896d01))
* 工作日历，分配组织问题 ([3cbfdd8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3cbfdd86c9c16a9c1f44a2905e7d56c42f00677e))
* 工作日历，新建时出现了两个新建按钮 ([da527aa](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/da527aa16c38464986c07cf09aa8659e01b4d9de))
* 工作日历初始日期默认值问题修复 ([01490df](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/01490df6ed51a10e7e6caab281197329e1d9dc44))
* 工作日历bug fix ([d65048e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d65048ed28ba1aa1e99d5e4a0efc89fe4c2481e7))
* 工作日历shiftcode传值问题 ([9942d11](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9942d11fffd964e145c08cb838e635ebc29d54f3))
* 号码段对象属性值代码回滚 ([630cdcc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/630cdcc163adace2501012b8deae900e343f7851))
* 号码段bug fix ([5e05f0e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5e05f0e41e33b5ea72f5c142d2d840647e7d5855))
* 画布切换问题已解决 ([ad616c3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ad616c3b67de1d7c529fa8b17d2caca9feeb785b))
* 解决了图形化，元素拖不到画布上的问题，给画布增加了缩放，多选，和缩略图 ([3948459](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3948459b83091e1a1c03e425c9940763e73c1851))
* 解决图形化使用工艺路线数据是，基础数据不对的问题，没有选择工艺路线时，不允许进行查询 ([6a909d3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6a909d30fcec4fa640df901dec10dcfcb665601f))
* 解决新建页面，多个多语言只能保存最后填写的一个的问题 ([9a121bc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9a121bcb004bdac9067ece9e630872d5671f31ed))
* 客户维护新增数据后，返回列表不会自动刷新 ([41c465d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/41c465d575166ed9fcda1a435dde9f4ced9e4e59))
* 库位维护，保存按钮加loading ([3c5bd5b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3c5bd5b255f1ac017f9aac21d23ec09ec1a16c1e))
* 扩展属性参数名改错 ([c261222](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c2612225575008d3b62a734b9d06a969f53a8879))
* 扩展属性抽屉 ([be549aa](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/be549aaf5afb11a6398a42783562b664273e3e1d))
* 扩展属性抽屉应分为是否禁用，和可否编辑两种情况 ([a6f9bdc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a6f9bdc4364faabdd961910519b95a72cfdb9734))
* 扩展属性维护功能无法修改扩展属性 ([f5f880e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f5f880e3d4298be7e257f8b0e0baedf7b961e955))
* 没有考虑选择路径时，表格为空数组的情况 ([675471c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/675471c145ce36f2bd42721b33a0503efd67a7e2))
* 没有选站点时，不传组织类型 ([d4ae392](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d4ae392fa313ebb59c2082841a2101a7344d98dd))
* 没有routerId时，设置为空字符串 ([423f2fd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/423f2fd34ec3cb905b6ae6f04a082f98b5135562))
* 判断条件错误，修改判断条件 ([10ec8ab](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/10ec8ab26703940e4bb2acbc066aa0a2160de050))
* 切换工艺判断该工艺是否已使用，需要去除自己 ([1c7ed0b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1c7ed0bf6d1252e2c4d90646c1b678b6cb30db18))
* 切换画布，工艺丢失问题处理 ([04edc7d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/04edc7dd57aceb351a3c1b91e061fd7f18176ab5))
* 切换图形化的适合，应该重置itemCount ([561a526](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/561a5268f30b080e7e7c604eee9ed3facca082d4))
* 请求事件维护修改数据后，后台保存成功，前台页面没有刷新 ([201d957](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/201d957b9b2fed6a34bb052e6c110745f383e413))
* 区域维护，未传全部数据 ([2c61a47](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2c61a47c6464e53eacfb2b0a47e5f03e227735c5))
* 区域维护返回list页面查询条件问题 ([426d221](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/426d2211468d3b7fce2d34f2eab2d5c4a2e9cde4))
* 区域维护bug fix ([19a7b14](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/19a7b148a128a62a19ef130445c12d48cfe3140c))
* 区域维护bug fix ([e012d61](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e012d61f0793b667d14cbc6916b633518726086c))
* 区域维护bugfix ([87be43f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/87be43fe291f4fe360c04083aeae5dd3c65bce71))
* 去掉console.log ([169c41b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/169c41ba5e648e1542943502fc60ed2a91424d21))
* 日期组件校验规则修改 ([03fab85](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/03fab8501093a5fa35ae70c1800c47c5498a03c2))
* 删除console ([2f46660](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2f466604f5547ab880e367ef4f3815ca116bf007))
* 删除git分支上同时存在大小写文件 ([ae289e1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ae289e1b3efba2c540964b96b4d6a46dc1dd3e0c))
* 升级依赖了，处理start报错问题 ([0a1ff83](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0a1ff83f9a35fca0f5730b7b0ed2ddba717e1be0))
* 生产指令管理 多语言处理, 添加取消逻辑等 ([ead9b15](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ead9b15c16ed14f5710c5dd6353973f1a3024782))
* 生产指令管理 多语言处理, 页面报错处理 ([eabd10d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/eabd10d6965ed1d15d9b01e21311d3268da0ffa2))
* 生产指令管理 多语言和列表样式维护 ([ac0aad5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ac0aad515ddd80fe9ba75eabc28b2f4f1853071a))
* 生产指令管理 列名显示不全修复 ([99f3838](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/99f38389aeb5e198dd41cf07d8e573e8b60e12ad))
* 生产指令管理 增加帮助文档跳转 ([5c56051](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5c560515dcaaa21d8023d639f1237748e71ec101))
* 生产指令管理 装配清单编码 工艺路线编码 lov 传入 siteId ([19fc620](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/19fc620c0277951251243171de56133dceeaa84d))
* 时间校验 ([063c37f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/063c37fd6ff681b3387d927d9c198b218bbffe64))
* 识别码取错 ([a9da89f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a9da89fc53bb02dd1a561460b5d71825a56abd6e))
* 事件查询，将页码信息放在post请求头部 ([1c46505](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1c46505a5454c50acf939f2fc28eb7edd38e4b5a))
* 数据收集组，关联对象tab打开报错fix ([64385cf](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/64385cf76b607022ec89c9d18885a92b6e77be93))
* 替代组只能有一个为启用状态 ([64df319](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/64df3190d97b1bcf3e6955d6158282b67ed5e812))
* 图标引入方式 ([4b7ecab](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4b7ecab5e7ad0de45191c70cf0bcceb5125d77af))
* 图形化，元素直接只允许存在一条连线 ([31f6620](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/31f66205fb4df1cc82228e1b6239e681ccca9abc))
* 图形化，在删除节点后，应把保存节点信息的mtRouterOperationComponentStash变量更新 ([6761cc7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6761cc72a8f891780917c1c0de7a4f0826188d02))
* 图形化监听alt按键时，冒泡处理吧键盘按键事件盖住了，以及新建逻辑的修改 ([fc7f8dc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fc7f8dc5218ed390f04e04e01fa9b56d1f3dd1a9))
* 图形化路由变换白屏问题处理 ([dc6dfa3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/dc6dfa393691fb6b0015c7ddd8a1dd6d0781b328))
* 图形化详情抽屉打开报错问题 ([d73c0b9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d73c0b97d08aea49928155036adc841e02a6b9ce))
* 图形化新建页面一直loading ([ccffc87](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ccffc876d8922dccbaaa534159e26782783eb10e))
* 图形化装配清单相关问题 ([e4c0380](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e4c0380d771eee060e8bde18a4dd7e2766bd3a5c))
* 图形化组件分配，未选择装配清单时，也需要列出工艺列表 ([4eda8dd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4eda8ddcd430b88b351833fa0570473751759e6a))
* 图形化bug修改 ([abd99f6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/abd99f6e951d945959bdbaf45d0922f280e1b829))
* 物料配送属性维护，复制成功跳转 ([5aa4898](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5aa4898ee4c79104bc8c3d86f5bcecccf0843cb4))
* 物料生产属性维护 编辑-扩展属性多语言修复 ([de0915c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/de0915c02f92f524005ca421f00d14f253a03630))
* 物料生产属性维护 表单误删字段恢复 ([6eba847](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6eba847d2edcb0725533d192a0a412f9a960bfef))
* 物料生产属性维护 表单校验优化 ([4de2e39](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/4de2e39761977163fcf4be87f406b4934f8c799a))
* 物料生产属性维护 复制/编辑 组件修复 Lov 分页多语言问题 ([d1f857d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d1f857d6d659bdd55dbce896eb62ef9f5a53e325))
* 物料生产属性维护 默认装配清单版本列默认列名修改 ([233eeaf](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/233eeaf118d5daaf35038602b23088eccd5bc095))
* 物料生产属性维护 区分编辑和新建时取消按钮点击操作 ([7da4668](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7da46681fedd1308fa2e715b33cafec17e3edef0))
* 物料生产属性维护 取消按钮添加判断 限制值根据类型限制输入条件 ([d0e67c4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d0e67c489c70bc553a76fb33b30e4d09af8247c2))
* 物料生产属性维护 站点值与其他参数添加关联 ([c42b03f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c42b03f9e7945f6de231bdaee37461a5145bb168))
* 物料生产属性维护页, 帮助文档地址更换 ([9d4147d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9d4147d21e4157158ca0216300628266000a21b7))
* 物料维护，需要注意当tab没打开，元素不存在的情况 ([c8e7c36](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c8e7c36ad8cb6a187e89372d45ba1160f741987b))
* 物料维护增加tab页校验 ([7442617](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/74426178b795caef1fff267e6ca1ac20b1d6461a))
* 现在图形化的工艺子步骤又可以使用了 ([261dcb9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/261dcb95ec182a10401db78c5bc962c179ce011c))
* 新建工艺路线可以编辑自动升版本 ([6c1a57e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6c1a57e2d3aa031fd2aadbca197c1d9d36560782))
* 修复版本跟详情不对齐问题 ([7d28724](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/7d287244b6449d95434d5ea0eb2f6017eb0db75e))
* 修复多语言，在input框修改值后，form表单里的值未改变的bug ([9aa7de4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9aa7de40e849c150bb91ba5163f48db2d37280a5))
* 修复新建步骤后，直接复制导致的报错问题 ([1f143e4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1f143e4c5db95fed3a318e175a71696a5f67e74b))
* 修复windows环境 "stylelint" 报错问题 ([410459f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/410459f7d15ccaf1bea84c728f81584559b472b9))
* 修改传参字段名 ([3d16665](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3d166658aa181af4f44a7b10e5c3dbfd71f2b626))
* 修改卡片与内容的间距 ([f58d6f3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f58d6f35134dccd5fb4ad5143cb573cd3526f261))
* 修改了切换布局后，删除，撤销会引起的页面崩溃问题 ([59ec90e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/59ec90efa3c28a021ee95e7de857ff1d2ea30663))
* 修改嵌套工艺路线不能保存的问题 ([a350b2a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a350b2a663bc9169a761630f55ed5de5b0b2710f))
* 修改日期传参 ([744426b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/744426b2d7c7db8d0439fe5413c89e682e446960))
* 修改日期组件禁用时间 ([a6ec417](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a6ec417ae3abf88d9cf218a2f2342c85de6459bb))
* 修改详细内容后，修改相应树节点名称 ([b123a3c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b123a3c0807bc18d769819f6bf1bdf616dc07150))
* 修改样式 ([ae135d5](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ae135d5081c7b49383deb173f310e0bab609f893))
* 修改页面的布局样式 ([6c0c42e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6c0c42ea9013d9af033d46066bc19e60ff7b5a18))
* 修改esLint检验报错 ([b4c80cf](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b4c80cf61ee3baba2bc59ef2a198f2510290e05d))
* 修改stepName的传值 ([6deaf8b](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6deaf8b8d612b833ebd05c498fd6f8e19f2ec3a0))
* 修改url ([e34a1be](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e34a1beb0d9e1569cfd2245bb573f67747fb0ff8))
* 选中工艺时，未给组件分配中工艺默认值 ([b9063f2](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/b9063f224d9790f9db20cc9eac0f2934d1dc8d06))
* 业务类型与移动类型关系维护新建数据保存无反应bug fix ([1b1a2a0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1b1a2a09a7f37e6df2296e6f2aa128a318cd8306))
* 一些调整 ([af533ab](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/af533ab4018bdd31cad1077d3821452530d953a0))
* 用户权限维护bug修改，eslint修改 ([c97d9a8](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/c97d9a8e73ac34da408e2a451b49b4216cf8d3b9))
* 由于hzero-ui组件库升级，组件库中TimePicker组件样式有问题，添加全局样式修改 ([a9c5d06](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a9c5d0641382eb5df774f7955792aba39cf0cf0c))
* 站点类型传错 ([d256207](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d2562078ab8a9769ca91ba91e2e716f2424d6316))
* 站点维护传参错误修改，生产线维护bug fix ([a9454e1](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a9454e1215580fc77f11daeb2eccb4e012c727dc))
* 执行作业管理，未使用Form表单管理数据，导致onChange逻辑缺失，进行修改 ([d0fd99f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d0fd99f871d35bc56de25784f5b0b21113127947))
* 执行作业管理多语言 ([2da76cd](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/2da76cd9b13b3761a9a92989f2c0c41f998b836b))
* 重写工艺路线路径选择抽屉 ([5533289](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5533289d5a910f284f66bcab3e4af5e07a2bc2ec))
* 装配点快速点击保存报错 ([ede0422](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/ede04226f70d0c5dbaf85162bd67c14414157534))
* 装配点快速点击保存报错 ([f549900](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f54990081d2cb68ccc1da99fcf0a3da534bd8d33))
* 装配清单，保存后应用新数据来重置一下表单 ([3f784b9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3f784b9f049c26ab109736b96a7d13720b89c076))
* 装配清单的一些问题修改 ([f5496af](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f5496afa8b136fe9f60d2977280f75ef9a720179))
* 装配清单和工艺路线的修改 ([d9f530f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d9f530f42780c9a3d6f82e0fc69106db2a6fe4e0))
* 装配清单和工作作业平台bug ([d44aeab](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d44aeab1105d726c7a160dc4aab1d0fa752a57b9))
* 装配清单刷新问题 ([49aed80](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/49aed802977ea5e9d035ac1b3c13cc71df2bef81))
* 装配清单小修改 ([9960cbc](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9960cbc3a8e91418a991b3d632e7016f5d1ddedd))
* 装配清单组件行抽屉，关闭时应清空state ([3c188ff](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3c188ffebf0029287f860295200056e07bf7fcae))
* 装配清单bug fix ([551f500](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/551f5003dc88e6c5107aaea1e9ecba877abed595))
* 装配清单bug fix ([d3563d4](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d3563d4667d48d2d8dc276bc943a29025b2b3bdd))
* 状态维护和类型维护的bug修改，还有一些eslint的报错没改完 ([f0431d0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f0431d012b4378a58504e1875b15ce6a21aa034b))
* 组件分配抽屉，列表显示名称 ([5f6eca3](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/5f6eca3c522e4b8f1fbdc3d7dbc6a2ee21686857))
* 组织调度策略查询页，如果没有选站点，请求中不传组织类型 ([72074cb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/72074cb4bd33dfecf297da6c3b3023e9afde1f2b))
* 组织关系维护 ([d881872](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d881872df3116e7a6c33da84a751ead2d728b6d6))
* 组织关系维护，解决树节点展开后删除，无法再次展开的问题 ([6aa56f9](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6aa56f9a9ba9fb7c8272e64479d67ae3c7981d80))
* 组织关系维护，新建时设置orgId为create ([9d07f12](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/9d07f12fc11fc7f7c76b2323419fdf75b702b392))
* 组织关系维护样式 ([895864a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/895864ab4c38e80d0cf6ce2382be7ea53b7e049e))
* 组织关系维护bug fix ([96951a7](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/96951a77be0fb8dcf4aa9e547c2c28a620695532))
* 组织建模中所有esLint报错修改 ([fbb7f1d](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/fbb7f1ddd7d9916f36058cc4a7501ea6bce810f0))
* aps跳工艺路线是的问题修改，以及一些eslint问题 ([0ea3415](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/0ea341555b43aa21da077ae2ec466c87ec62433b))
* await方法报错 ([4729839](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/47298393c65519797d56f4ce3d6f1cc9148cad76))
* bomid 变化后步骤组内工艺的组件分配未清空 ([72d2cae](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/72d2caecfb7b6b9c6ab9833daebd4f94b383540e))
* bug fix ([1ddfaed](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/1ddfaed5fa63528327060e1fd19085796f178185))
* bug fix ([f708b49](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f708b4954f946f8adc7f3fd4dad884ca6b082feb))
* bug fix ([6d0456f](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6d0456fde15050558aa7d30ecfd0c1f5843fbb9b))
* eslint error ([f58975a](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/f58975ab4f837d2c9ae78d9b9b852c69ea08f3ba))
* iconfont线上不显示的问题 ([3d5d1c0](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3d5d1c02a63aaa7d4b0ed265a8686743b7203813))
* id都转成string进行比较，update: 添加处理请求返回报错的getResponse方法 ([3452f88](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/3452f8856664ef1a96b8c4600f29ad7e5fbaf6e5))
* LOV code "MT.ROUTER" to "MT.ROUTER_SITE" ([a0b309c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a0b309cd7a05dfe08a762ba6beedd63591dfa428))
* Lov加查询参数 ([6da40bb](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6da40bbeba9a2bf68f7ce18af5ca1c8843f1b866))
* mtRouterReturnStepDTO为null，会导致mtRouterReturnStepDTO.returnType保存，页面崩溃 ([caa65d6](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/caa65d6bf0b95166d85a6d6e1b556e91f302f63d))
* routers中物料配送属性页面引用错component ([e57414c](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/e57414c6fcdf216501471547cee229b092d185f8))
* Select添加key ([6265782](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/6265782aa4966c16ea3fe86ad9f5f63f6c96e052))
* updateLayout->layout，切换布局应用layout方法 ([d2cdd0e](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/d2cdd0e206a0776799ada7d7bbba0df96f470fc8))


### Reverts

* Revert "fix: 物料生产属性维护-复制(抽屉) 删除目标物料/站点/生产线默认值" ([a0a6191](https://code.choerodon.com.cn/happs-tarzan-til/hmes-front/commits/a0a6191f374c1d5eef1b525577152844ae1edf09))
