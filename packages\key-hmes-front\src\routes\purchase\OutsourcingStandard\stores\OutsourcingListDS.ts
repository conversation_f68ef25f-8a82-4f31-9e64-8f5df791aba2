/**
 * @Description: 采购退货平台
 * @Author: <<EMAIL>>
 * @Date: 2021-12-21 14:14:48
 * @LastEditTime: 2023-05-18 15:31:39
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.purchase.outsourcingManage';

const tenantId = getCurrentOrganizationId();

const OutsourcingList = (): DataSetProps => ({
  autoQuery: false,
  pageSize: 10,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-out-source/out-source/page/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST.HEAD&operationType=OUTSOURCE_DOC`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'instructionDocId',
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.instructionDocNum`).d('单据编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.supplierName`).d('供应商'),
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.supplierSiteName`).d('供应商地点'),
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.instructionDocTypeDesc`).d('单据类型'),
    },
    {
      name: 'instructionDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.instructionDocStatusDesc`).d('单据状态'),
    },
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.poNumber`).d('采购订单'),
    },
    /* {
      name: 'outSourceReturnFlagMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.outSourceReturnFlagMeaning`).d('是否补料'),
    },
    {
      name: 'replenishmentNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.replenishmentNum`).d('补料单号'),
    }, */

    {
      name: 'reasonMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.reasonMeaning`).d('退料原因'),
    },
    {
      name: 'demandTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.table.demandTime`).d('需求时间'),
    },
    {
      name: 'receivingAdress',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.receivingAdress`).d('地址'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.remark`).d('备注'),
    },
    {
      name: 'printTimes',
      type: FieldType.string,
      label: intl.get(`tarzan.common.printTimes`).d('打印次数'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.realName`).d('创建人'),
    },
    {
      name: 'createdDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.createdDate`).d('创建时间'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
  queryFields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.instructionDocNum`).d('单据编码'),
    },
    {
      name: 'siteIdLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.table.parentLocatorLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            siteType: 'MANUFACTURING',
          };
        },
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteIdLov.siteId',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.table.supplierLov`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
      },
    },
    {
      name: 'supplierId',
      type: FieldType.number,
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.table.supplierSiteName`).d('供应商地点'),
      lovCode: 'MT.MODEL.SUPPLIER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            supplierId: record?.get('supplierId'),
          };
        },
        disabled: ({ record }) => {
          return !record?.get('supplierId');
        },
      },
    },
    {
      name: 'supplierSiteId',
      type: FieldType.number,
      bind: 'supplierSiteLov.supplierSiteId',
    },

    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.instructionDocType`).d('单据类型'),
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-instruction-doc/operation-type/limit/doc/type/list?operationType=OUTSOURCE_DOC`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.instructionDocStatus`).d('单据状态'),
      textField: 'description',
      valueField: 'statusCode',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('instructionDocType');
        },
        lookupUrl: ({ record }) => {
          if (record?.get('instructionDocType') === 'OUTSOURCE_DOC') {
            return `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_OUTSOURCE`;
          } if (record?.get('instructionDocType') === 'OUTSOURCING_RETURN_DOC') {
            return `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_OUTSOURCING_RETURN`;
          }
          return `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_REPLENISHMENT`;

        },
      },
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'poOrderLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.table.poOrderLov`).d('采购订单'),
      lovCode: 'MT.OUT_SOURCE_PO',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
      },
    },
    {
      name: 'poHeaderId',
      type: FieldType.number,
      bind: 'poOrderLov.poHeaderId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.table.materialLov`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.revisionCode`).d('物料版本'),
    },
    /* {
      name: 'outSourceReturnFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.outSourceReturnFlag`).d('是否补料'),
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'replenishmentNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.replenishmentNum`).d('补料单号'),
    }, */
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.reason`).d('退货原因'),
      lookupCode: 'MT.OUTSOURCING_REASON',
    },
    {
      name: 'printFlag',
      type: FieldType.string,
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`tarzan.common.printFlag`).d('打印标识'),
    },
  ],
});

const LineDs = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-out-source/out-source/line/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST.LINE`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.table.lineNumber`).d('行号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.materialCode`).d('物料'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.materialName`).d('物料描述'),
    },

    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.siteCode`).d('站点'),
    },
    {
      name: 'instructionStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.instructionStatus`).d('状态'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.quantity`).d('制单数量'),
    },
    {
      name: 'actualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.actualQtyLine`).d('执行数量'),
    },
    {
      name: 'usedOverDeliveryQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.usedOverDeliveryQty`).d('占用超发数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.uomCode`).d('单位'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.locatorCode`).d('库位'),
    },
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.poNumber`).d('采购订单号'),
    },
    {
      name: 'poLineNum',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.table.poLineNum`).d('采购订单行号'),
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.toleranceFlag`).d('允差标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'toleranceTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.toleranceType`).d('允差类型'),
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.table.toleranceMaxValue`).d('上允差'),
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.table.toleranceMinValue`).d('下允差'),
    },
  ],
});

const DetailDs = (): DataSetProps => ({
  autoQuery: false,
  paging: false,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-out-source/out-source/material-lot/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_LIST_MATERIAL_LOT.QUERY`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows',
  primaryKey: 'identification',
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatusDesc`).d('物料批状态'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('所在容器'),
    },
    {
      name: 'actualQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.actualQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.realName`).d('操作人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('操作时间'),
    },
  ],
});

export { OutsourcingList, LineDs, DetailDs };
