import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Badge, Collapse, Icon, Popconfirm, Tabs, Tag } from 'choerodon-ui';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { Form, TextField, Select, Lov, Switch, DateTimePicker, TextArea, Attachment, Table, Button, Modal, DataSet, Output } from 'choerodon-ui/pro';
import DetailComponent from '@/routes/TestingStrategy/InspectItemMaintain/components/DetailComponent';
import { LabelAlign, LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { TabsType } from 'choerodon-ui/lib/tabs/enum';
import { ColumnAlign } from 'choerodon-ui/dataset/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock } from 'choerodon-ui/pro/es/table/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { Size } from 'choerodon-ui/lib/_util/enum';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import ChangeItemComponent from './ChangeItemComponent';
import { NumberDS, formulaListTableDS, DetailTableDS } from '../stores';
import { BatchDS, inspectionItemBasisDS } from '../stores/BatchDS';
import ChangeMessageDS from '../stores/ChangeMessageDS';
import InspectItemInfoDrawer from '../../InitialManagementActivityPlatform/components/DimensionDetailTableList/InspectItemInfoDrawer';
import style from '../index.modules.less';

const tenantId = getCurrentOrganizationId();
const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`;
let batchAddInspectionDimensionModal;

const _ignoreKeys = [
  'creationDate',
  'createdBy',
  'lastUpdateDate',
  'lastUpdatedBy',
  'objectVersionNumber',
  '_token',
  'inspectGroupItemId',
  'sequence',
];

const OrderChangePage = ({
  basicDs,
  inspectBusinessTypeList,
  canEdit,
  customizeForm,
  history,
  setInspectBusinessTypeList,
  pubFlag,
  changeItemAddDs,
  changeItemDeleteDs,
  changeItemModifyDs,
}) => {
  const childRef = useRef<any>();
  const [activeKey, setActiveKey] = useState('');
  useEffect(() => {
    setActiveKey(inspectBusinessTypeList[0]?.inspectBusinessType);
  }, [inspectBusinessTypeList.length]);

  // 数值类型-预警值DS
  const warnNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  // 数值类型-符合值DS
  const trueNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  // 数值类型-不符合值DS
  const falseNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  //  单独编辑表单
  const editTableFormDS = useMemo(() => new DataSet(DetailTableDS({ drawer: true })), []);
  // 公式参数列表
  const formulaListTableDs = useMemo(() => new DataSet(formulaListTableDS()), []);


  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'temporary.process.change.orders',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    readOnly: !canEdit,
  };

  const getDataValueShow = (record, name) => {
    const _dataType = record?.get('dataType');
    const _valueData = record?.get(name) || [];
    const _dataShow = _valueData.length > 0 ? _valueData[0].dataValue : '';
    return ['CALCULATE_FORMULA', 'VALUE', 'VALUE_LIST'].includes(_dataType)
      ? _valueData.map(item => <Tag>{item.dataValue}</Tag>)
      : _dataShow;
  };

  /**
   * @description 从检验方案获取检验项
   */
  const handleGetInspectionScheme = async () => {
    if (
      !basicDs.current?.get('inspectBusinessTypeList') ||
      !basicDs.current?.get('materialId') ||
      !basicDs.current?.get('prodLineId') ||
      !basicDs.current?.get('operationId') ||
      !basicDs.current?.get('equipmentScope')
    ) {
      return notification.error({
        message: intl.get(`${modelPrompt}.detail.check.businessType.thenObtain.inspection items`).d('检验业务类型、物料、产线、工艺、设备范围后，再获取检验项!'),
      })
    }
    const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/item-by-scheme/ui`
    const data = {
      equipmentIdList: basicDs.current.get('equipmentIdList'),
      equipmentScope: basicDs.current.get('equipmentScope'),
      inspectBusinessTypeList: basicDs.current.get('inspectBusinessTypeList'),
      materialCode: basicDs.current.get('materialCode'),
      materialId: basicDs.current.get('materialId'),
      materialName: basicDs.current.get('materialName'),
      operationId: basicDs.current.get('operationId'),
      prodLineId: basicDs.current.get('prodLineId'),
      siteId: basicDs.current.get('siteId'),
      siteName: basicDs.current.get('siteName'),
    };
    const res = await request(url, {
      method: 'POST',
      data,
    });
    if (res && res.success) {
      const { typeGroupList, inspectSchemeId } = res.rows;
      typeGroupList.forEach(item => {
        item.inspectionItemBasisDs = new DataSet(inspectionItemBasisDS());
        item.equipmentGroupList.forEach(items => {
          items.itemInfo.forEach(_itemInfo => {
            _itemInfo.tempPermitItemStatus = 'ENABLE';
            _itemInfo.inspectionItemRowUuid = uuid();
            if (['TEXT', 'DECISION_VALUE'].includes(_itemInfo.dataType)) {
              _itemInfo.trueValue =
                (_itemInfo.trueValueList || []).length > 0
                  ? _itemInfo.trueValueList[0].dataValue
                  : null;
              _itemInfo.falseValue =
                (_itemInfo.falseValueList || []).length > 0
                  ? _itemInfo.falseValueList[0].dataValue
                  : null;
            }
            if (_itemInfo.dataType === 'VALUE_LIST') {
              _itemInfo.trueValue =
                (_itemInfo.trueValueList || []).length > 0
                  ? _itemInfo.trueValueList.map(trueItem => trueItem.dataValue)
                  : null;
              _itemInfo.falseValue =
                (_itemInfo.falseValueList || []).length > 0
                  ? _itemInfo.falseValueList.map(falseItem => falseItem.dataValue)
                  : null;
            }
          })
          items.batchDs = new DataSet(BatchDS());
          items.ds = new DataSet(ChangeMessageDS());
        })
      })
      setInspectBusinessTypeList(typeGroupList);
      basicDs.current?.set('inspectSchemeId', inspectSchemeId);
    }
    else {
      return notification.error({
        message: res.message,
      })
    }
  };

  // 切换业务类型
  const handleTabsChange = (key) => {
    setActiveKey(key);
  };

  // 新增变更信息
  const handleAdd = (dataSet, value, batchDs?) => {
    value.forEach(valueItem => {
      const _valueItem = { ...valueItem };
      _valueItem.requiredFlag = _valueItem.requiredFlag || 'Y';
      _valueItem.destructiveExperimentFlag = _valueItem.destructiveExperimentFlag || 'N';
      _valueItem.outsourceFlag = _valueItem.outsourceFlag || 'N';
      if (['TEXT', 'DECISION_VALUE'].includes(_valueItem.dataType)) {
        _valueItem.trueValue =
          (_valueItem.trueValueList || []).length > 0
            ? _valueItem.trueValueList[0].dataValue
            : null;
        _valueItem.falseValue =
          (_valueItem.falseValueList || []).length > 0
            ? _valueItem.falseValueList[0].dataValue
            : null;
      }
      if (_valueItem.dataType === 'VALUE_LIST') {
        _valueItem.trueValue =
          (_valueItem.trueValueList || []).length > 0
            ? _valueItem.trueValueList.map(trueItem => trueItem.dataValue)
            : null;
        _valueItem.falseValue =
          (_valueItem.falseValueList || []).length > 0
            ? _valueItem.falseValueList.map(falseItem => falseItem.dataValue)
            : null;
      }
    });
    value.forEach((item) => {
      if (!dataSet.toData().map(items => items.inspectItemId).includes(item.inspectItemId)) {
        item.sequence = (dataSet.toData().length + 1) * 10;
        item.inspectionItemRowUuid = uuid();
        item.tempPermitItemStatus = 'ENABLE';
        dataSet.create(item);
      }
    })
    if (batchDs) {
      batchDs.reset();
    }
  };

  // 删除变更信息
  const handleDelete = (dataSet, record) => {
    dataSet.remove(record);
    dataSet.records.filter(item => item.status !== 'delete').forEach((item, index) => {
      item.set("sequence", (index + 1) * 10)
    })
  };


  // 新建检验项目点击确定的回调函数
  const handleCreateInspectionCallback = async (ds) => {
    const { success, rows } = await childRef.current?.submit(false);
    if (success) {
      const res = await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-item/detail/ui?inspectItemId=${rows}`)
      if (res?.success && ds) {
        if (Array.isArray(ds) && ds.length > 0) {
          ds.forEach(item => handleAdd(item, [res.rows]))
        } else {
          handleAdd(ds, [res.rows]);
        }
      }
      if (batchAddInspectionDimensionModal) {
        batchAddInspectionDimensionModal.close();
      }
      return Promise.resolve(true);
    }
    return Promise.resolve(false);
  };

  // 新建检验项目点击确定的回调函数
  const handleCreateInspectionCallbackKeep = async (ds) => {
    const { success, rows } = await childRef.current?.submit(true);
    if (success) {
      const res = await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-item/detail/ui?inspectItemId=${rows}`)
      if (res?.success && ds) {
        if (Array.isArray(ds) && ds.length > 0) {
          ds.forEach(item => handleAdd(item, [res.rows]))
        } else {
          handleAdd(ds, [res.rows]);
        }
      }
      return Promise.resolve(false);
    }
    return Promise.resolve(false);
  };

  // 新建检验项目
  const handleCreateInspection = (modal, inspectBusinessType, ds) => {
    modal.close();
    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.detail.inspectSchemeObjectCreate`).d('新建检验项目'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      onOk: () => {
        handleCreateInspectionCallback(ds)
      },
      footer: (okBtn, cancelBtn) => {
        return [
          cancelBtn,
          <Button
            onClick={() => {
              return handleCreateInspectionCallbackKeep(ds);
            }}
          >
            {intl.get(`${modelPrompt}.detail.button.saveAndCreate`).d('保存并新建下一条')}
          </Button>,
          okBtn,
        ];
      },
      children: (
        <DetailComponent
          ref={childRef}
          canEdit={canEdit}
          schemeType=""
          inspectBusinessType={inspectBusinessType}
          kid="create"
          column={3}
          customizeForm={customizeForm}
          requiredField={['enterMethod', 'dataQty', 'samplingMethodLov']}
          validateType="InspectionScheme"
          operationType="SCHEME_FUNC_CREATE"
        />
      ),
    });
  };

  // 批量新增检验项目
  const handleInspectionItemObjectChange = (value, ds, inspectBusinessType) => {
    inspectBusinessTypeList?.
      filter(item => item.inspectBusinessType === inspectBusinessType)[0].
      equipmentGroupList?.map(equipmentGroup => equipmentGroup.ds).
      forEach(ds => handleAdd(ds, value))
    ds.reset();
  };

  // 批量新增检验项目LOV
  const batchAddInspectionDimension = inspectBusinessType => {
    inspectBusinessTypeList.forEach(item => {
      if (inspectBusinessType === item.inspectBusinessType) {
        item.inspectionItemBasisDs.current.set('inspectionDimensionObject', []);
        batchAddInspectionDimensionModal = Modal.open({
          key: Modal.key(),
          title: intl.get(`${modelPrompt}.detail.batchAddInspectionDimension`).d('批量新增检验项目'),
          destroyOnClose: true,
          style: {
            width: 360,
          },
          okCancel: false,
          okText: intl.get(`${modelPrompt}.detail.close`).d('关闭'),
          children: (
            <Form
              dataSet={item.inspectionItemBasisDs}
              columns={1}
              labelWidth={112}
              disabled={!canEdit}
            >
              <Select name="inspectionDimensionObject" colSpan={2}>
                {item.equipmentGroupList &&
                  item.equipmentGroupList.length > 0 &&
                  item.equipmentGroupList?.map(equipmentGroup => (
                    <Select.Option value={`${equipmentGroup.materialId}-${equipmentGroup.equipmentId}`}>
                      {`${equipmentGroup.materialName}+${equipmentGroup.equipmentName}`}
                    </Select.Option>
                  ))}
              </Select>
              <Lov
                autoSelectSingle={false}
                noCache
                name="inspectionItemObject"
                onChange={value => {
                  handleInspectionItemObjectChange(value, item.inspectionItemBasisDs, inspectBusinessType);
                }}
                modalProps={{
                  footer: (okBtn, cancelBtn, modal) => {
                    return [
                      <Button
                        onClick={() => {
                          handleCreateInspection(
                            modal,
                            inspectBusinessType,
                            inspectBusinessTypeList?.
                              filter(item => item.inspectBusinessType === inspectBusinessType)[0].
                              equipmentGroupList?.map(equipmentGroup => equipmentGroup.ds),
                          );
                        }}
                      >
                        {intl.get(`${modelPrompt}.detail.inspectSchemeObjectCreate`).d('新建检验项目')}
                      </Button>,
                      cancelBtn,
                      okBtn,
                    ];
                  },
                }}
                tableProps={{
                  queryFieldsLimit: 10,
                }}
              />
            </Form>
          ),
        });
      }
    });
  };

  const handleEdit = (tableDS, formDs, record, inspectBusinessType) => {
    const recordData = record.toData();
    editTableFormDS.loadData([{ ...recordData, isNewRow: false }]);

    const inspectItemDrawerProps = {
      canEdit,
      _ignoreKeys,
      tenantId,
      customizeForm,
      warnNumberDS,
      trueNumberDS,
      falseNumberDS,
      editTableFormDS,
      formDs,
      tableDS,
      formulaListTableDs,
      statisticsTableUpdate: '',
      schemeType: '',
      inspectBusinessType,
    };

    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      destroyOnClose: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.detail.title.itemEdit`).d('编辑检验项目'),
      drawer: true,
      style: {
        width: '1080px',
      },
      className: 'hmes-style-modal',
      children: <InspectItemInfoDrawer {...inspectItemDrawerProps} />,
      onOk: () => {
        return handleInspectItemDrawerSubmit(tableDS);
      },
      footer: (okBtn, cancelBtn) => {
        return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
      },
    });
  };

  const handleInspectItemDrawerSubmit = async (tableDS) => {
    // 检验项目字段校验
    let validateResult;
    const record = editTableFormDS.current;
    if (!record) {
      return false;
    }

    let validValueTypeFlag = true;
    // 数值类型的符合值、不符合值必输校验
    const dataType = record.get('dataType');

    validateResult = await formulaListTableDs.validate();
    if (!validateResult) {
      return false;
    }
    validateResult = await editTableFormDS.validate();
    if (!validateResult) {
      return false;
    }

    if (['CALCULATE_FORMULA', 'VALUE'].includes(dataType)) {
      if (trueNumberDS.length < 1 && falseNumberDS.length < 1) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.detail.message.inputTrueOrFalseValue`)
            .d('请输入符合值或不符合值'),
        });
        validValueTypeFlag = false;
      } else {
        const _targeDS = trueNumberDS.length > 0 ? trueNumberDS : falseNumberDS;
        const _newData = _targeDS.records.filter(
          item =>
            item.get('singleValued') ||
            (item.get('multiValued') &&
              (item.get('multiValued')?.start || item.get('multiValued')?.end)),
        );
        if (_newData.length < 1) {
          notification.error({
            message:
              trueNumberDS.length > 0
                ? intl.get(`${modelPrompt}.detail.message.inputTrueValue`).d('请输入符合值')
                : intl.get(`${modelPrompt}.detail.message.inputFalseValue`).d('请输入不符合值'),
          });
          validValueTypeFlag = false;
        }
      }
    }
    if (!validValueTypeFlag) {
      return false;
    }

    const { sequence, inspectItemId, inspectGroupItemId } = record.toData();
    const sameSerialNumberFlag = tableDS
      .toData()
      .some(item => item.sequence === sequence && item.inspectGroupItemId !== inspectGroupItemId);
    const sameInspectItemFlag = tableDS
      .toData()
      .some(
        item =>
          item.inspectItemId === inspectItemId && item.inspectGroupItemId !== inspectGroupItemId,
      );
    if (sameSerialNumberFlag) {
      notification.error({
        message: intl.get(`${modelPrompt}.detail.message.sameSerialNumber`).d('当前序号已存在'),
      });
      return false;
    }
    if (sameInspectItemFlag) {
      notification.error({
        message: intl.get(`${modelPrompt}.detail.message.sameInspectItemFlag`).d('当前检验项目已选用'),
      });
      return false;
    }

    let _trueValueList: any = [];
    let _falseValueList: any = [];
    let _warningValueList: any = [];

    // 如果数据类型为数值类型，需要做赋值处理
    if (['CALCULATE_FORMULA', 'VALUE'].includes(dataType)) {
      const _valueHandle = targetDS => {
        return targetDS
          .toData()
          .filter(
            (item: any) =>
              item.singleValued ||
              (item.multiValued && (item.multiValued?.start || item.multiValued?.end)),
          )
          .map((item: any) => {
            if (
              item.valueType !== 'single' &&
              (!item.multiValued?.start || !item.multiValued?.end)
            ) {
              if (!item.multiValued?.start) {
                item.multiValued.start = '-∞';
                item.leftValue = '-∞';
              }
              if (!item.multiValued?.end) {
                item.multiValued.end = '+∞';
                item.rightValue = '+∞';
              }
              item.dataValue = `${item.leftChar}${item.multiValued.start},${item.multiValued.end}${item.rightChar}`;
              const _leftCharTip = item.leftChar === '(' ? '<' : '≤';
              const _rightCharTip = item.rightChar === ')' ? '<' : '≤';
              item.valueShow = `${item.multiValued.start}${_leftCharTip}X${_rightCharTip}${item.multiValued.end}`;
            }
            return item;
          });
      };

      _trueValueList = _valueHandle(trueNumberDS);
      _falseValueList = _valueHandle(falseNumberDS);
      _warningValueList = _valueHandle(warnNumberDS);
    } else if (dataType === 'VALUE_LIST') {
      _trueValueList = (record.get('trueValue') || []).map(item => ({
        dataValue: item,
      }));
      _falseValueList = (record.get('falseValue') || []).map(item => ({
        dataValue: item,
      }));
    } else {
      _trueValueList = record.get('trueValue')
        ? [
          {
            dataValue: record.get('trueValue'),
          },
        ]
        : [];
      _falseValueList = record.get('falseValue')
        ? [
          {
            dataValue: record.get('falseValue'),
          },
        ]
        : [];
    }

    if (dataType === 'CALCULATE_FORMULA') {
      record.set(
        'formula',
        JSON.stringify({
          formulaSourceId: record.get('formulaSourceId'),
          formulaMode: record.get('formulaMode'),
          formulaDisplayPosition: record.get('formulaDisplayPosition'),
          formulaId: record.get('formulaId'),
          formulaCode: record.get('formulaCode'),
          formulaName: record.get('formulaName'),
          dimension: record.get('dimension'),
          formulaList: (formulaListTableDs.toData() || []).map((formulaListItem: any) => {
            if (formulaListItem) {
              return {
                fieldCode: formulaListItem.fieldCode,
                fieldName: formulaListItem.fieldName,
                inspectItemId: formulaListItem.inspectItemId,
                inspectItemDesc: formulaListItem.inspectItemDesc,
                isRequired: formulaListItem.isRequired,
              };
            }
            return {};
          }),
        }),
      );
      record.set('formulaList', formulaListTableDs.toData());
    } else {
      record.set('formula', '');
      record.set('formulaList', []);
    }

    record.set('trueValueList', _trueValueList);
    record.set('falseValueList', _falseValueList);
    record.set('warningValueList', _warningValueList);

    const recordData = record.toData();
    if (recordData.isNewRow) {
      tableDS.create({
        ...recordData,
        isNewRow: false,
      });
    } else {
      tableDS.forEach(focusRecord => {
        if (focusRecord.get('inspectionItemRowUuid') === recordData.inspectionItemRowUuid) {
          Object.keys(recordData).forEach(recordDataKey => {
            focusRecord.set(recordDataKey, recordData[recordDataKey]);
          });
          focusRecord.set('trueValue', null);
          focusRecord.set('falseValue', null);
          focusRecord.set('trueValue', recordData.trueValue);
          focusRecord.set('falseValue', recordData.falseValue);
          focusRecord.set('tempPermitItemStatus', 'ENABLE');
        }
      });
    }

    // 添加一个判断数据类型和任务类别的对象, 用来判断是否清除对应计算公式的关联关系
    // 若计算公式的formulaSourceId 的任务类别跟绑定的检验对象类型不同 或者绑定的检验对象不是数据类型 清除 formulaList 里绑定的id
    const tableRecordMap = {};

    const parentsMap = {};
    tableDS.forEach(subRecord => {
      const subRecordData = subRecord.toData();
      tableRecordMap[subRecordData.inspectItemId] = {
        dataType: subRecordData.dataType,
        taskCategory: subRecordData.taskCategory,
      };
      const _inspectionItemRowUuid = subRecordData.inspectionItemRowUuid;
      if (!parentsMap[_inspectionItemRowUuid]) {
        parentsMap[_inspectionItemRowUuid] = {
          front: subRecordData.sequence,
          after: 1,
        };
      }
    });

    tableDS.forEach(subRecord => {
      const subRecordData = subRecord.toData();
      const _formulaSourceId = subRecordData.formulaSourceId;
      const _formulaList = subRecordData.formulaList || [];

      if (subRecord.get('dataType') === 'CALCULATE_FORMULA') {
        if (`${_formulaSourceId}` === `${recordData.inspectItemId}`) {
          subRecord.set('requiredFlag', recordData.requiredFlag);
        }
        if (_formulaSourceId && parentsMap[_formulaSourceId]) {
          subRecord.set(
            'sequence',
            parentsMap[_formulaSourceId].front + parentsMap[_formulaSourceId].after,
          );
          parentsMap[_formulaSourceId].after += 1;
        }

        if (_formulaSourceId) {
          subRecord.set('taskCategory', tableRecordMap[_formulaSourceId]?.taskCategory || '');
        }

        _formulaList.forEach(formulaListItem => {
          if (
            !tableRecordMap[formulaListItem.inspectItemId] ||
            tableRecordMap[formulaListItem.inspectItemId].dataType !== 'VALUE' ||
            ((tableRecordMap[formulaListItem.inspectItemId].taskCategory ||
              tableRecordMap[_formulaSourceId].taskCategory) &&
              tableRecordMap[formulaListItem.inspectItemId].taskCategory !==
              tableRecordMap[_formulaSourceId].taskCategory)
          ) {
            formulaListItem.inspectItemId = undefined;
            formulaListItem.inspectItemDesc = undefined;
            formulaListItem.inspectItemObject = undefined;
          }
        });

        subRecord.set('formulaList', _formulaList);

        subRecord.set(
          'formula',
          JSON.stringify({
            formulaSourceId: subRecord.get('formulaSourceId'),
            formulaMode: subRecord.get('formulaMode'),
            formulaDisplayPosition: subRecord.get('formulaDisplayPosition'),
            formulaId: subRecord.get('formulaId'),
            formulaCode: subRecord.get('formulaCode'),
            formulaName: subRecord.get('formulaName'),
            dimension: subRecord.get('dimension'),
            formulaList: (_formulaList || []).map((formulaListItem: any) => {
              if (formulaListItem) {
                return {
                  fieldCode: formulaListItem.fieldCode,
                  fieldName: formulaListItem.fieldName,
                  inspectItemId: formulaListItem.inspectItemId,
                  inspectItemDesc: formulaListItem.inspectItemDesc,
                  isRequired: formulaListItem.isRequired,
                };
              }
              return {};
            }),
          }),
        );
      }
    });
  };

  // 变更内容下检验业务类型列
  const columns = useMemo(() => (ds: any, batchDs: any, inspectBusinessType: string) => {
    return [
      {
        header: () => (
          <Lov
            dataSet={batchDs}
            name="inspectionItemObject"
            noCache
            mode={ViewMode.button}
            clearButton={false}
            onChange={value => {
              handleAdd(ds, value, batchDs);
            }}
            size={Size.small}
            funcType={FuncType.flat}
            disabled={!canEdit}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return [
                  <Button
                    onClick={() => {
                      handleCreateInspection(modal, inspectBusinessType, ds);
                    }}
                  >
                    {intl.get(`${modelPrompt}.detail.inspectSchemeObjectCreate`).d('新建检验项目')}
                  </Button>,
                  cancelBtn,
                  okBtn,
                ];
              },
            }}
            tableProps={{
              queryFieldsLimit: 10,
            }}
          >
            <Icon type="add" />
          </Lov>
        ),
        name: 'add',
        align: ColumnAlign.center,
        width: 50,
        renderer: ({ dataSet, record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDelete(dataSet, record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <Button
              icon="remove"
              disabled={!canEdit}
              funcType={FuncType.flat}
              size={Size.small}
            />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'sequence',
        width: 70,
        lock: ColumnLock.left,
      },
      {
        name: 'inspectItemCode',
        minWidth: 160,
        renderer: ({ record }) => (
          <a onClick={() => handleEdit(ds, batchDs, record, inspectBusinessType)} >{record?.get('inspectItemCode')}</a>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'inspectItemDesc',
        minWidth: 160,
      },
      {
        name: 'validDateFrom',
        minWidth: 160,
      },
      {
        name: 'validDateTo',
        minWidth: 160,
      },
      {
        name: 'tempPermitItemStatus',
        minWidth: 120,
      },
      {
        name: 'inspectItemTypeDesc',
        minWidth: 160,
      },
      {
        name: 'subItemType',
        minWidth: 160,
      },
      {
        name: 'itemPattern',
        minWidth: 160,
      },
      {
        name: 'featureType',
        minWidth: 160,
      },
      {
        name: 'trueValue',
        width: 200,
        renderer: ({ record }) => getDataValueShow(record, 'trueValueList'),
      },
      {
        name: 'falseValue',
        width: 200,
        renderer: ({ record }) => getDataValueShow(record, 'falseValueList'),
      },
      {
        name: 'warningValue',
        width: 200,
        renderer: ({ record }) => getDataValueShow(record, 'warningValueList'),
      },
      {
        name: 'uomLov',
        align: ColumnAlign.center,
      },
      {
        name: 'dataQty',
      },
      {
        name: 'samplingMethodLov',
        align: ColumnAlign.center,
      },
      {
        name: 'requiredFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'inspectGroupDesc',
        width: 120,
      },
      {
        name: 'inspectBasis',
      },
      {
        name: 'inspectMethod',
        align: ColumnAlign.center,
        width: 120,
      },
      {
        name: 'technicalRequirement',
      },
      {
        name: 'inspectTool',
        align: ColumnAlign.center,
        width: 120,
      },
      {
        name: 'qualityCharacteristic',
        align: ColumnAlign.center,
        width: 120,
      },
      {
        name: 'dataType',
        align: ColumnAlign.center,
        width: 120,
      },

      {
        name: 'processMode',
        align: ColumnAlign.center,
        width: 120,
      },
      {
        name: 'enterMethod',
        align: ColumnAlign.center,
      },
      {
        name: 'decimalNumber',
      },
      {
        name: 'ncCodeGroupLov',
      },
      {
        name: 'ncCodeObj',
      },

      {
        name: 'sameGroupIdentification',
      },
      {
        name: 'destructiveExperimentFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'outsourceFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'inspectFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'spcReleaseFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'srmReleaseFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'actionItem',
      },
      {
        name: 'employeePosition',
        width: 120,
      },
      {
        name: 'inspectFrequency',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record }) => {
          let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
          if (inspectFrequencyShow) {
            inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
            inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
            return inspectFrequencyShow;
          }
          return value;
        },
      },
      {
        name: 'remark',
      },
      {
        name: 'dataStorage',
      },
      {
        name: 'controlMethod',
      },
      {
        name: 'enclosure',
        width: 160,
        renderer: ({ record }) => <Attachment {...attachmentProps} record={record} />,
      },
    ] as ColumnProps[];
  }, [canEdit])


  const renderTaps = useMemo(() => {
    return inspectBusinessTypeList.map((item) => {
      item.inspectionItemBasisDs.loadData([{
        inspectBusinessType: item.inspectBusinessType,
        inspectBusinessTypeDesc: item.inspectBusinessTypeDesc,
      }]);
      return (
        <Tabs.TabPane tab={item.inspectBusinessTypeDesc} key={item.inspectBusinessType}>
          <Form columns={3} dataSet={item.inspectionItemBasisDs} disabled>
            <Lov name='inspectBusinessTypeObject' />
          </Form>
          <Collapse defaultActiveKey={item.equipmentGroupList.map(items => {
            return `${items.materialName}+${items.equipmentName}`
          })}>
            {
              item.equipmentGroupList.map(equipmentGroup => {
                const { itemInfo = [], data = {} } = equipmentGroup
                itemInfo.forEach(_itemInfo => _itemInfo.inspectionItemRowUuid = uuid());
                equipmentGroup.ds.loadData(itemInfo || []);
                equipmentGroup.batchDs.loadData([data] || []);
                return (
                  <Collapse.Panel
                    header={`${equipmentGroup.materialName}+${equipmentGroup.equipmentName}`}
                    key={`${equipmentGroup.materialName}+${equipmentGroup.equipmentName}`}
                  >
                    <Table
                      dataSet={equipmentGroup.ds}
                      columns={columns(equipmentGroup.ds, equipmentGroup.batchDs, item.inspectBusinessType)}
                      virtual
                      virtualCell
                      style={{ height: 300 }}
                    />
                  </Collapse.Panel>
                )
              })}
          </Collapse>
        </Tabs.TabPane>
      )
    })
  }, [inspectBusinessTypeList, canEdit]);

  return (
    <Collapse
      collapsible="icon"
      defaultActiveKey={pubFlag ? ['changeItem', 'basic', 'changeMessage'] : ['basic', 'changeMessage']}
    >
      {pubFlag && (
        <Collapse.Panel
          header={intl.get(`${modelPrompt}.detail.basic.collapse.changeItem`).d('修改信息项')}
          key='changeItem'
        >
          <ChangeItemComponent changeItemAddDs={changeItemAddDs} changeItemDeleteDs={changeItemDeleteDs} changeItemModifyDs={changeItemModifyDs} />
        </Collapse.Panel>
      )}
      <Collapse.Panel
        header={intl.get(`${modelPrompt}.detail.basic.collapse.panel`).d('基础信息')}
        key='basic'
      >
        <Form
          dataSet={basicDs}
          columns={3}
          labelWidth={121}
          labelAlign={LabelAlign.right}
          disabled={!canEdit}
        >
          <TextField name='temporaryPermitNum' />
          <Select name='temporaryPermitStatus' />
          <Lov name='siteObj' />
          <TextField name='createdByName' />
          <Lov name='unitNameObj' />
          <Select name='inspectBusinessTypeList' />
          <Lov name='materialCodeObj' />
          <Lov name='prodLineCodeObj' />
          <Lov name='operationNameObj' />
          <TextField name='materialName' />
          <TextField name='prodLineName' />
          <TextField name='operationDescription' />
          <Select name='equipmentScope' />
          <Lov name='equipmentNameObj' />
          <Switch name='guideBookFlag' />
          <DateTimePicker name='scheduleStartDate' />
          <DateTimePicker name='scheduleEndDate' />
          <DateTimePicker name='creationDate' />
        </Form>
        <Form
          dataSet={basicDs}
          columns={3}
          labelWidth={121}
          labelAlign={LabelAlign.right}
        >
          <TextArea rowSpan={2} colSpan={2} name='impactDesc' disabled={!canEdit} />
          <Output name="inspectSchemeCode" style={{ color: 'blue', cursor: 'pointer' }} onClick={() => history.push(`/hwms/inspection-scheme-maintenance/detail/${basicDs.current.get('inspectSchemeId')}`)} />
          <Attachment {...attachmentProps} />
        </Form>
      </Collapse.Panel>
      <Collapse.Panel
        header={intl.get(`${modelPrompt}.detail.changeMessage.collapse.panel`).d('变更内容')}
        key='changeMessage'
      >
        {
          canEdit &&
          <a className={style['a-btn-size']} onClick={handleGetInspectionScheme}>
            +{intl.get(`${modelPrompt}.detail.getMessageByInspectionScheme`).d('从检验方案获取检验项')}
          </a>
        }
        {
          !canEdit &&
          <a className={style['a-btn-size']} disabled>
            +{intl.get(`${modelPrompt}.detail.getMessageByInspectionScheme`).d('从检验方案获取检验项')}
          </a>
        }
        <Tabs
          onChange={handleTabsChange}
          type={TabsType.card}
          activeKey={activeKey}
          tabBarExtraContent={
            <Button
              icon='add'
              onClick={() => {
                batchAddInspectionDimension(activeKey);
              }}
              disabled={!canEdit}
            >
              {intl.get(`${modelPrompt}.detail.batchAddInspectionDimension`).d('批量新增检验项目')}
            </Button>
          }
        >
          {renderTaps}
        </Tabs>
      </Collapse.Panel>
    </Collapse>
  )
};

export default OrderChangePage;
