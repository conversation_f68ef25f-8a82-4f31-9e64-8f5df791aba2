/*
 * @Description: 产品审核项目-列表页
 * @Author: <<EMAIL>>
 * @Date: 2023-09-28 10:29:09
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-04 15:36:07
 */
import React, {useMemo} from 'react';
import {Button, DataSet, Select, Switch, Table} from 'choerodon-ui/pro';
import {Content, Header} from 'components/Page';
import withProps from 'utils/withProps';
import ExcelExport from 'components/ExcelExport';
import intl from 'utils/intl';
import {Badge} from 'choerodon-ui';
import {ColumnProps} from 'choerodon-ui/pro/lib/table/Column';
import {ColumnAlign, TableQueryBarType} from 'choerodon-ui/pro/lib/table/enum';
import {ColumnLock} from 'choerodon-ui/pro/es/table/enum';
import {ButtonColor} from 'choerodon-ui/pro/es/button/enum';
import {FuncType} from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import {getCurrentOrganizationId} from 'utils/utils';
import {isNil} from 'lodash';
import formatterCollections from 'utils/intl/formatterCollections';
import {useRequest} from '@components/tarzan-hooks';
import {openTab} from 'utils/menuTab';
import queryString from 'querystring';
import {BASIC} from '@utils/config';
import {tableDS} from '../stores/tableDS';
import {SaveProductItem} from '../services';

const modelPrompt = 'tarzan.qms.productReview.productReviewItem';
const tenantId = getCurrentOrganizationId();

const ProductReviewItemList = props => {
  const { tableDs } = props;

  const { run: saveProductReviewItem } = useRequest(SaveProductItem(), {
    manual: true,
  });

  const handleValidate = async () => {
    // 校验所有表单
    const normalValidate = await Promise.all(
      tableDs.map(async record => {
        if (record.status === 'add' || record?.getState('editing')) {
          // eslint-disable-next-line no-return-await
          return await record.validate(true);
        }
        return true;
      }),
    );

    // 汇总校验结果
    return normalValidate.every(val => val);
  };

  const handleSave = async () => {
    const valRes = await handleValidate();
    if (!valRes) {
      return;
    }

    const data: any = [];
    tableDs.forEach(record => {
      if (record.status === 'add' || record?.getState('editing')) {
        data.push(record.toData());
      }
    });
    saveProductReviewItem({
      params: data,
      onSuccess: () => {
        notification.success({});
        tableDs.query(tableDs.currentPage);
      },
    });
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'productReviewItemCode',
        width: 180,
      },
      {
        name: 'productReviewItemType',
        editor: record => (record.status === 'add' || record?.getState('editing')) && <Select name="productReviewItemType" searchable searchFieldInPopup />,
      },
      {
        name: 'productReviewItemDesc',
        width: 230,
        editor: record => record.status === 'add' || record?.getState('editing'),
      },
      {
        name: 'productReviewMethod',
        editor: record => record.status === 'add' || record?.getState('editing'),
      },
      {
        name: 'specRequire',
        editor: record => record.status === 'add' || record?.getState('editing'),
      },
      {
        name: 'reviewFrequency',
        editor: record => record.status === 'add' || record?.getState('editing'),
      },
      {
        name: 'weightCoefficient',
        editor: record => record.status === 'add' || record?.getState('editing'),
        width: 180,
      },
      {
        name: 'remark',
        width: 230,
        editor: record => record.status === 'add' || record?.getState('editing'),
      },
      {
        name: 'recordDataFlag',
        editor: record => (record.status === 'add' || record?.getState('editing'))  && <Switch />,
        width: 180,
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
      },
      {
        name: 'fromOrtFlag',
        editor: record => (record.status === 'add' || record?.getState('editing')) && <Switch />,
        width: 180,
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
      },
      {
        name: 'enableFlag',
        width: 230,
        align: ColumnAlign.center,
        editor: record => (record.status === 'add' || record?.getState('editing')) && <Switch />,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.enable`).d('启用')
                  : intl.get(`tarzan.common.label.disable`).d('禁用')
              }
            />
          );
        },
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 120,
        renderer: ({ record, dataSet }: { record?; dataSet? }) => {
          if (record?.status === 'add' || record?.getState('editing')) {
            return (
              <>
                <Button
                  funcType={FuncType.flat}
                  onClick={() => {
                    if (record.status === 'add') {
                      dataSet?.remove(record);
                    } else {
                      record.reset();
                      record.setState('editing', false);
                    }
                  }}
                >
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
                <Button funcType={FuncType.flat} onClick={() => handleSave()}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
              </>
            );
          }
          return (
            <Button
              funcType={FuncType.flat}
              onClick={() => {
                record?.setState('editing', true);
              }}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          );
        },
      },
    ],
    [],
  );

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/YP.QIS_PRODUCT_REVIEW_ITEM_IMP',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('产品审核项目')}>
        <Button color={ButtonColor.primary} icon="add" onClick={() => tableDs.create({}, 0)}>
          {intl.get(`${modelPrompt}.button.create`).d('新建')}
        </Button>
        <Button icon="file_upload" onClick={goImport}>
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </Button>
        <ExcelExport
          exportAsync
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-review-item/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get('tarzan.common.button.export').d('导出')}
        />
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          queryBar={TableQueryBarType.filterBar}
          queryFields={{
            productReviewItemType: <Select name="productReviewItemType" searchable searchFieldInPopup />,
          }}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          searchCode="productReviewItem-list"
          customizedCode="productReviewItem-list"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(() => {
    const tableDs = new DataSet(tableDS());
    return {
      tableDs,
    };
  })(ProductReviewItemList),
);
