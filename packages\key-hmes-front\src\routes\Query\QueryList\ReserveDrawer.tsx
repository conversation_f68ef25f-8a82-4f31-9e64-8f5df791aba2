/**
 * @Description: 预留详情信息drawer
 * @Author: <EMAIL>
 * @Date: 2022/7/8 13:47
 * @LastEditTime: 2022/7/8 13:47
 * @LastEditors: <EMAIL>
 */
import React from 'react';
import intl from 'utils/intl';
import { Collapse } from 'choerodon-ui';
import { Form, Table, TextField, DataSet, NumberField } from 'choerodon-ui/pro';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/es/table/enum';

const { Panel } = Collapse;
interface DetailModalProps {
  headDs: DataSet;
  tableDs: DataSet;
}

const ReserveDrawer: React.FC<DetailModalProps> = ({ headDs, tableDs }) => {
  // 物料的表格columns
  const columns: ColumnProps[] = [
    { name: 'holdTypeDesc' },
    { name: 'orderTypeDesc' },
    {
      name: 'orderCode',
      renderer: ({ value }) => {
        if (value === 0) {
          return '';
        } 
        return value;
        
      },
    },
    { name: 'holdQuantity' },
  ];

  return (
    <div className="hmes-style">
      <Form dataSet={headDs} labelLayout={LabelLayout.horizontal} labelWidth={112} columns={2}>
        <TextField name="siteCode" />
        <TextField name="siteName" />
        <TextField name="materialCode" />
        <TextField name="materialDesc" />
        <TextField name="locatorCode" />
        <TextField name="locatorDesc" />
        <TextField name="ownerTypeDesc" />
        <TextField name="ownerCode" />
        <TextField name="lotCode" />
        <NumberField name="holdQty" />
      </Form>
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get('tarzan.inventory.query.title.details').d('预留详细信息')}
          key="basicInfo"
          dataSet={tableDs}
        >
          <Table queryBar={TableQueryBarType.none} dataSet={tableDs} columns={columns} />
        </Panel>
      </Collapse>
    </div>
  );
};
export default ReserveDrawer;
