/**
 * @Description: 容器管理平台-装载历史抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-04-07 13:51:43
 * @LastEditTime: 2022-04-11 16:38:20
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';

export default ({ ds }) => {
  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'loadObjectTypeDesc',
        width: 180,
      },
      {
        name: 'eventId',
        width: 180,
      },
      {
        name: 'eventTypeCode',
        width: 180,
      },
      {
        name: 'eventTypeDesc',
        width: 180,
      },
      {
        name: 'eventRequestId',
        width: 150,
      },
      {
        name: 'requestTypeCode',
        width: 150,
      },
      {
        name: 'requestTypeDesc',
        width: 180,
      },
      {
        name: 'eventBy',
        width: 180,
      },
      {
        name: 'eventTime',
        width: 180,
        align: ColumnAlign.center,
      },
      {
        name: 'loadObjectCode',
        minWidth: 180,
      },
      {
        name: 'loadQty',
        width: 120,
        renderer: ({ value, record }) => {
          if (record?.get('loadObjectType') === 'CONTAINER') {
            return '';
          } if (record?.get('loadObjectType') === 'MATERIAL_LOT') {
            return record?.get('primaryUomQty');
          }
          return value;

        },
      },
      {
        name: 'primaryUomCode',
        width: 120,
      },
      {
        name: 'loadSequence',
        width: 90,
        align: ColumnAlign.right,
      },
      {
        name: 'locationRow',
        width: 120,
      },
      {
        name: 'locationColumn',
        width: 120,
      },
      {
        name: 'trxLoadQty',
        width: 120,
      },
    ];
  }, []);

  return <Table dataSet={ds} columns={columns} />;
};
