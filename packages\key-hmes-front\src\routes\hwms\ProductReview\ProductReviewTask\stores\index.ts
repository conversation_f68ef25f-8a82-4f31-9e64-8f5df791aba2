/**
 * @Description: 产品审核任务-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-10-09 17:58:05
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-11-22 16:57:10
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.productReview.productReviewTask';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'productRevTaskId',
  queryFields: [
    {
      name: 'productRevTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskCode`).d('产品审核任务编码'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'productRevPlanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanCode`).d('产品审核计划编码'),
    },
    {
      name: 'productRevSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeCode`).d('产品审核方案编码'),
    },
    {
      name: 'productRevTaskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'cusMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件编码'),
    },
    {
      name: 'cusMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialName`).d('客户零件描述'),
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工序编码'),
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: {
        tenantId,
        workcellType: 'PROCESS',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'workcellId',
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'productRevTaskType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskType`).d('任务类别'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'publishedPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.publishedPerson`).d('审核发布人'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      lovPara: { tenantId },
    },
    {
      name: 'publishedBy',
      bind: 'publishedPersonLov.id',
    },
    {
      name: 'samplePosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplePosition`).d('抽样地点'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_SAMPLE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'warehouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('完工仓库编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
        locatorType: 'PRODUCTION_WAREHOUSE',
      },
    },
    {
      name: 'warehouseId',
      bind: 'warehouseLov.locatorId',
    },
    {
      name: 'reviewedPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reviewPerson`).d('审核员'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      lovPara: { tenantId },
    },
    {
      name: 'reviewedBy',
      bind: 'reviewedPersonLov.id',
    },
    {
      name: 'executeDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.executeDateFrom`).d('执行时间从'),
      max: 'executeDateTo',
    },
    {
      name: 'executeDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.executeDateTo`).d('执行时间至'),
      min: 'executeDateFrom',
    },
  ],
  fields: [
    {
      name: 'productRevTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskCode`).d('产品审核任务编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'productRevPlanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanCode`).d('产品审核计划编码'),
    },
    {
      name: 'productRevSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeCode`).d('产品审核方案编码'),
    },
    {
      name: 'productRevTaskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'cusMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件编码'),
    },
    {
      name: 'cusMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialName`).d('客户零件描述'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工序编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序描述'),
    },
    {
      name: 'productRevTaskType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskType`).d('任务类别'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'publishedName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.publishedPerson`).d('审核发布人'),
    },
    {
      name: 'samplePosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplePosition`).d('抽样地点'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_SAMPLE_POSITION',
      lovPara: { tenantId },
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('完工仓库编码'),
    },
    {
      name: 'sampleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleQty`).d('抽样数量'),
    },
    {
      name: 'reviewedName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewPerson`).d('审核员'),
    },
    {
      name: 'executeDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.executeDate`).d('执行时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
