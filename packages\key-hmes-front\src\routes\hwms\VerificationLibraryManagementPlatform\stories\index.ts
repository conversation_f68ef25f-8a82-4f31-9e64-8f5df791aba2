/**
 * @Description: 检验项目组维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-01-10 16:54:12
 * @LastEditTime: 2023-05-18 16:49:50
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.verificationLibraryManagementPlatform';
const tenantId = getCurrentOrganizationId();
// const prefix = '/tznq-33178';

// 检验组列表
const ListTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'verificationId',
  queryFields: [
    // {
    //   name: 'inspectItemLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.site`).d('公司',
    //   lovCode: 'MT.QMS.INSPECT_ITEM',
    //   ignore: FieldIgnore.always,
    //   lovPara: {
    //     dataType: 'CALCULATE_FORMULA',
    //     tenantId,
    //   },
    // },
    // {
    //   name: 'inspectItemId',
    //   bind: 'inspectItemLov.inspectItemId',
    // },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'verificationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationCode`).d('检证项目模板编码'),
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('状态'),
      lookupCode: 'YP.QIS.VERIFICATION_STATUS',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    // {
    //   name: 'productObject',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.site`).d('产品对象',
    //   // lookupCode: 'YP.QIS.PRODUCT_OBJECT',
    // },
    {
      name: 'verificationFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationFrom`).d('检证来源'),
      lookupCode: 'YP.QIS.VERIFICATION_FROM',
      noCache: true,
    },
    {
      name: 'verificationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationType`).d('检证类型'),
      lookupCode: 'YP.QIS.VERIFICATION_TYPE',
      noCache: true,
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
      lookupCode: 'YP.QIS.PRODUCT_TYPE',
    },
    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检证阶段'),
      lookupCode: 'YP.QIS.VERIFICATION_PERIOD',
    },
    {
      name: 'tempProblemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tempProblemLov`).d('关联问题编号'),
      lovCode: 'YP.QIS.PROBLEM_LIST',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'problemId',
      bind: 'tempProblemLov.problemId',
    },
    {
      name: 'problemCode',
      bind: 'tempProblemLov.problemCode',
    },
    {
      name: 'createLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createLov`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        // dataType: 'CALCULATE_FORMULA',
        tenantId,
      },
    },
    {
      name: 'createdBy',
      bind: 'createLov.id',
    },
  ],
  fields: [
    // {
    //   name: 'unit',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.unit`).d('公司'),
    // },
    // {
    //   name: 'inspectGroupDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.siteCode`).d('lalala'),
    // },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'verificationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationCode`).d('检证项目模板编码'),
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('状态'),
      lookupCode: 'YP.QIS.VERIFICATION_STATUS',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'itemGroup',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productObjectMeaning`).d('产品对象'),
      lookupCode: 'YP.QMS.ITEM_GROUP',
    },
    {
      name: 'verificationFromMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationFromMeaning`).d('检证来源'),
    },
    {
      name: 'verificationTypeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationTypeMeaning`).d('检证类型'),
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
      lookupCode: 'YP.QIS.PRODUCT_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'verificationPeriodMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriodMeaning`).d('检证阶段'),
    },
    {
      name: 'problemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemCode`).d('关联问题编码'),
    },
    {
      name: 'fromProcessName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromProcess`).d('问题归属工序'),
    },
    {
      name: 'startupDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.startupDate`).d('启用日期'),
    },
    {
      name: 'expireDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expireDate`).d('失效日期'),
    },
    {
      name: 'failureModeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureModeMeaning`).d('失效模式'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建日期'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    // {
    //   name: 'rejectIdName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.rejectIdName`).d('驳回人'),
    // },
    {
      name: 'rejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
    },
    // {
    //   name: 'rejectTime',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.rejectTime`).d('驳回时间'),
    // },
    {
      name: 'reviewByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewByRealName`).d('审核人'),
    },
    {
      name: 'reviewDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewDate`).d('审核时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/list/ui`,
        method: 'GET',
      };
    },
  },
});

// 检验组详情
const DetailFormDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'verificationId',
  fields: [
    {
      name: 'verificationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationCode`).d('检证项目模板编码'),
      disabled: true,
    },
    // {
    //   name: 'inspectItemLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.site`).d('公司',
    //   lovCode: 'MT.QMS.INSPECT_ITEM',
    //   ignore: FieldIgnore.always,
    //   required: true,
    //   lovPara: {
    //     dataType: 'CALCULATE_FORMULA',
    //     tenantId,
    //   },
    // },
    // {
    //   name: 'inspectItemId',
    //   bind: 'inspectItemLov.inspectItemId',
    // },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'verificationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationStatus`).d('状态'),
      required: true,
      lookupCode: 'YP.QIS.VERIFICATION_STATUS',
      noCache: true,
    },
    {
      name: 'verificationFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationFrom`).d('检证来源'),
      required: true,
      lookupCode: 'YP.QIS.VERIFICATION_FROM',
      noCache: true,
    },
    {
      name: 'verificationPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationPeriod`).d('检证阶段'),
      required: true,
      lookupCode: 'YP.QIS.VERIFICATION_PERIOD',
    },
    {
      name: 'verificationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.verificationType`).d('检证类型'),
      lookupCode: 'YP.QIS.VERIFICATION_TYPE',
      required: true,
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
      lookupCode: 'YP.QIS.PRODUCT_TYPE',
      required: true,
    },
    {
      name: 'failureMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.failureMode`).d('失效模式'),
      lookupCode: 'YP.QIS.FAILURE_MODE',
    },
    {
      name: 'applyArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyArea`).d('运用业务'),
      lookupCode: 'YP.QIS.APPLY_AREA',
      multiple: true,
    },
    {
      name: 'applicableProductLine',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicableProductLine`).d('适用产品线'),
      lookupCode: 'YP.QIS.APPLICABLE_PRODUCT_LINE',
      multiple: true,
    },
    {
      name: 'applicableChemicalSystem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicableChemicalSystem`).d('适用化学体系'),
      lookupCode: 'YP.QIS.APPLICABLE_CHEMICAL_SYSTEM',
      multiple: true,
    },
    {
      name: 'applicableProductStructure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicableProductStructure`).d('适用产品结构'),
      lookupCode: 'YP.QIS.APPLICABLE_PRODUCT_STRUCTURE',
      multiple: true,
    },
    {
      name: 'startupDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.startupDate`).d('启用日期'),
    },
    {
      name: 'expireDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expireDate`).d('失效日期'),
    },
    // {
    //   name: 'rejectBy',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.site`).d('驳回人',
    // },
    {
      name: 'rejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
    },
    // {
    //   name: 'rejectTime',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.site`).d('驳回时间',
    // },
    {
      name: 'reviewByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewByRealName`).d('审核人'),
    },
    {
      name: 'reviewDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewDate`).d('审核时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建日期'),
    },
    {
      name: 'tempProblemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tempProblemLov`).d('关联问题编号'),
      lovCode: 'YP.QIS.PROBLEM_LIST',
      ignore: FieldIgnore.always,
      textField: 'problemCode',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
      // lovPara: {
      //   // dataType: 'CALCULATE_FORMULA',
      //   tenantId,
      // },
    },
    {
      name: 'problemId',
      bind: 'tempProblemLov.problemId',
    },
    {
      name: 'problemCode',
      bind: 'tempProblemLov.problemCode',
    },
    {
      name: 'itemGroupDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemGroupDesc`).d('产品对象'),
      disabled: true,
      bind: 'materialLov.itemGroupDesc',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialCode',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
    },
    {
      name: 'processFromLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processFromLov`).d('问题归属工序'),
      lovCode: 'MT.MODEL.WORKCELL_SITE',
      ignore: FieldIgnore.always,
      textField: 'workcellName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'fromProcessId',
      bind: 'processFromLov.workcellId',
    },
    {
      name: 'fromProcessName',
      bind: 'processFromLov.workcellName',
    },
    {
      name: 'responsibleArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleArea`).d('问题责任领域'),
      lookupCode: 'YP.QIS.QUALITY_PROBLEM_TYPE',
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDescription`).d('问题描述'),
      dynamicProps: {
        required: ({ record }) => {
          return record.get('problemId');
        },
      },
    },
    {
      name: 'verificationUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.verificationUuid`).d('相关照片'),
      // max: 9,
    },
    // {
    //   name: 'enableFlag',
    //   type: FieldType.boolean,
    //   label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
    //   trueValue: 'Y',
    //   falseValue: 'N',
    //   defaultValue: 'Y',
    // },
  ],
});

const problemReasonDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      dynamicProps: {
        defaultValue: ({ dataSet }) => {
          let maxNum = 0;
          dataSet.forEach(_record => {
            if (_record?.get('sequence') > maxNum) {
              maxNum = _record?.get('sequence');
            }
          });
          return maxNum + 10;
        },
      },
    },
    {
      name: 'occur',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.occur`).d('发生原因'),
      required: true,
    },
    {
      name: 'escape',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.escape`).d('逃逸原因'),
      required: true,
    },
  ],
});

const measureDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      dynamicProps: {
        defaultValue: ({ dataSet }) => {
          let maxNum = 0;
          dataSet.forEach(_record => {
            if (_record?.get('sequence') > maxNum) {
              maxNum = _record?.get('sequence');
            }
          });
          return maxNum + 10;
        },
      },
    },
    {
      name: 'measure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.measure`).d('措施'),
      required: true,
    },
  ],
});

// 检验组关联检验项
const DetailTableDS: () => DataSetProps = () => ({
  autoCreate: false,
  forceValidate: true,
  paging: false,
  primaryKey: 'verificationTaskId',
  fields: [
    {
      name: 'selectList',
      type: FieldType.object,
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      min: 1,
      step: 1,
      // required: true,
      disabled: true,
    },
    {
      name: 'taskContent',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.taskContent`).d('检证内容'),
      required: true,
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('检证责任部门'),
      required: true,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'departmentId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'departmentName',
      bind: 'departmentLov.unitName',
    },
  ],
});

const approveDS: () => DataSetProps = () => ({
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'rejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
      required: true,
    },
  ],
});

const modalDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'problemLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.problemLov`).d('问题编号'),
      lovCode: 'YP.QIS.PROBLEM_LIST',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
    },
    {
      name: 'problemId',
      bind: 'problemLov.problemId',
    },
    {
      name: 'problemCode',
      bind: 'problemLov.problemCode',
    },
  ],
});

export { ListTableDS, DetailFormDS, problemReasonDS, measureDS, DetailTableDS, approveDS, modalDS };
