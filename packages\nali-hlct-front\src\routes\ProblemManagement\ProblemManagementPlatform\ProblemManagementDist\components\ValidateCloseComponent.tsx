/**
 * @Description: 问题管理平台-验证关闭页面组件
 * @Author: <EMAIL>
 * @Date: 2023/7/6 15:27
 */
import React, { useEffect, useMemo } from 'react';
import {
  Button,
  DateTimePicker,
  Form,
  Lov,
  NumberField,
  Table,
  Tabs,
  TextField,
  TextArea,
  Select,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentUser } from 'utils/utils';
import notification from 'utils/notification';
import { Collapse, Icon, Tag } from 'choerodon-ui';
import { observer } from 'mobx-react';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { getProductWithPrecision } from '@/utils';
import styles from '../index.module.less';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const currentUserInfo = getCurrentUser();
const modelPrompt = 'tarzan.problemManagement.problemManagementPlatform';

const ValidateCloseComponent = ({
  problemCloseDs,
  preCloseDs,
  overtimeCloseTableDs,
  problemCloseTableDs,
  finallyScoreFormDs,
  finallyScoreTableDs,
  history,
  userRole,
  problemStatus,
  totalScoreInfo,
  perpSubmitFlag,
  handleUpdateTotalScore,
  handleSaveEvaluation,
  handleAddVerificationLibrary,
  handleAddProblemWarning,
  handleAddProblemExpand,
  handleAddProblemDuplicate,
  handleSavePreCloseInfo,
  handleSubmitPreCloseInfo,
}) => {
  useEffect(() => {
    finallyScoreTableDs.forEach(_record => {
      handleChangeScoreRatio(_record?.get('scoreRatio'), _record);
    });
  }, [totalScoreInfo?.totalScore]);

  // 分配比例变化后更新对应得分
  const handleChangeScoreRatio = (value, record) => {
    const _computeRes = getProductWithPrecision(totalScoreInfo?.totalScore, value || 0);
    const score = value ? getProductWithPrecision(_computeRes, 100, 2, 'division') : 0;
    record.set('score', score);
    finallyScoreFormDs.validate();
  };

  const finallyScoreColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'sequence', align: ColumnAlign.left, width: 120 },
      { name: 'responsiblePersonLov' },
      { name: 'unitCompanyName' },
      { name: 'positionName' },
      { name: 'phone' },
      {
        name: 'scoreRatio',
        editor: record =>
          record.dataSet.parent?.getState('canEdit') &&
          problemStatus === 'CLOSED' &&
          userRole.includes('LEAD_PERSON') && (
            <NumberField onChange={value => handleChangeScoreRatio(value, record)} addonAfter="%" />
          ),
        renderer: ({ value }) => (value ? `${value}%` : ''),
      },
      { name: 'score' },
    ],
    [problemStatus, userRole?.length, totalScoreInfo?.totalScore],
  );

  const EnableFlagComponents = ({ dataSet, name }) => {
    // 最终有效性按钮样式
    const getStyle = (dataSet, name) => {
      const enableStyle = {
        color: 'rgb(17, 217, 84)',
        borderColor: 'rgb(17, 217, 84)',
        backgroundColor: 'rgb(230, 255, 234)',
      };
      const disEnableStyle = {
        color: 'rgb(242, 58, 80)',
        borderColor: 'rgb(242, 58, 80)',
        backgroundColor: 'rgb(255, 240, 240)',
      };
      const emptyStyle = {
        color: 'rgb(47, 84, 235)',
        borderColor: 'rgb(47, 84, 235)',
        backgroundColor: 'rgb(240, 248, 255)',
      };
      const disableStyle = {
        opacity: 0.5,
        cursor: 'not-allowed',
      };

      if (dataSet.current?.get(name)) {
        return dataSet.current?.get(name) === 'Y'
          ? { ...enableStyle, ...disableStyle }
          : { ...disEnableStyle, ...disableStyle };
      }
      return { ...emptyStyle, ...disableStyle };
    };

    const renderTextContent = (name, enableFlag) => {
      if (name === 'tempMeasureExeEnableFlag') {
        if (enableFlag) {
          return enableFlag === 'Y'
            ? intl.get(`${modelPrompt}.title.tempEnable`).d('临时措施有效')
            : intl.get(`${modelPrompt}.title.tempDisable`).d('临时措施无效');
        }
        return intl.get(`${modelPrompt}.title.isTempEnable`).d('临时措施是否有效');
      }
      if (name === 'perpMeasureExeEnableFlag') {
        if (enableFlag) {
          return enableFlag === 'Y'
            ? intl.get(`${modelPrompt}.title.perpEnable`).d('长期措施有效')
            : intl.get(`${modelPrompt}.title.perpDisable`).d('长期措施无效');
        }
        return intl.get(`${modelPrompt}.title.isPerpEnable`).d('长期措施是否有效');
      }
      if (enableFlag) {
        return enableFlag === 'Y'
          ? intl.get(`${modelPrompt}.title.findReason`).d('找到根本原因')
          : intl.get(`${modelPrompt}.title.disFindReason`).d('未找到根本原因');
      }
      return intl.get(`${modelPrompt}.title.whetherFindReason`).d('是否找到根本原因');
    };

    return (
      <div className={styles['enable-flag-button']} style={getStyle(dataSet, name)}>
        {dataSet.current?.get(name) && (
          <Icon type={dataSet.current?.get(name) === 'Y' ? 'thumb_up' : 'thumb_down'} />
        )}
        <span>{renderTextContent(name, dataSet.current?.get(name))}</span>
      </div>
    );
  };

  const handleCancelScore = () => {
    finallyScoreFormDs.reset();
    finallyScoreTableDs.reset();
    handleUpdateTotalScore(finallyScoreFormDs.current?.get('scoringCoefficient'));
    finallyScoreFormDs.setState('canEdit', false);
  };

  const RenderScoreButtonGroup = observer(({ dataSet, problemStatus, userRole }) => {
    if (dataSet.getState('canEdit')) {
      return (
        <>
          <Button icon="close" funcType={FuncType.flat} onClick={handleCancelScore}>
            {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
          </Button>
          <Button
            icon="save"
            funcType={FuncType.flat}
            onClick={() => handleSaveEvaluation(dataSet)}
          >
            {intl.get(`${modelPrompt}.button.save`).d('保存')}
          </Button>
        </>
      );
    }

    return (
      <>
        <Button
          icon="edit-o"
          funcType={FuncType.flat}
          disabled={
            problemStatus !== 'CLOSED' ||
            (!userRole.includes('SENIOR_TECHNICAL_MANAGER') && !userRole.includes('LEAD_PERSON'))
          }
          onClick={() => dataSet.setState('canEdit', true)}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </Button>
      </>
    );
  });

  const ExtraButtonGroup = observer(({ userRole, problemStatus }) => {
    return (
      <>
        <Button
          disabled={problemStatus !== 'CLOSED' || !userRole.includes('LEAD_PERSON')}
          onClick={() => handleAddVerificationLibrary()}
        >
          {intl.get(`${modelPrompt}.button.verificationLibrary`).d('纳入检证库')}
        </Button>
        <Button
          disabled={
            ['DRAFT', 'NEW', 'PUBLISH'].includes(problemStatus) || !userRole.includes('LEAD_PERSON')
          }
          onClick={() => handleAddProblemWarning()}
        >
          {intl.get(`${modelPrompt}.button.problemWarning`).d('问题警示')}
        </Button>
        <Button
          disabled={problemStatus !== 'CLOSED' || !userRole.includes('LEAD_PERSON')}
          onClick={() => handleAddProblemExpand()}
        >
          {intl.get(`${modelPrompt}.button.problemExpand`).d('问题横展')}
        </Button>
        <Button
          disabled={problemStatus !== 'CLOSED' || !userRole.includes('LEAD_PERSON')}
          onClick={() => handleAddProblemDuplicate()}
        >
          {intl.get(`${modelPrompt}.button.problemDuplicate`).d('问题复盘')}
        </Button>
      </>
    );
  });

  const renderReviewStatusTag = (value, record) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
        className = 'blue';
        break;
      case 'REVIEWING':
        className = 'yellow';
        break;
      case 'PASS':
        className = 'green';
        break;
      default:
        className = 'red';
    }
    return <Tag color={className}>{record!.getField('reviewStatus')!.getText()}</Tag>;
  };

  const overtimeCloseColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'sequence', align: ColumnAlign.left, width: 120 },
      {
        name: 'overtimeDay',
        editor: record => record?.status === 'add' || record?.getState('editing'),
        width: 150,
      },
      {
        name: 'requestReason',
        editor: record => record?.status === 'add' || record?.getState('editing'),
      },
      {
        name: 'reviewStatus',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ value, record }) => renderReviewStatusTag(value, record),
      },
      {
        name: 'creationDate',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'createdPersonLov',
        width: 150,
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 150,
        renderer: ({ record, dataSet }: { record?; dataSet? }) => {
          if (record?.status === 'add' || record?.getState('editing')) {
            return (
              <>
                <Button
                  funcType={FuncType.flat}
                  onClick={() => {
                    if (record.status === 'add') {
                      dataSet?.remove(record);
                    } else {
                      record.reset();
                      record.setState('editing', false);
                    }
                  }}
                >
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
                <Button
                  funcType={FuncType.flat}
                  onClick={() => handleSavePreCloseInfo(dataSet, 'OVERTIME')}
                >
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
              </>
            );
          }
          return (
            <>
              <Button
                funcType={FuncType.flat}
                disabled={
                  record?.get('reviewStatus') !== 'NEW' ||
                  !userRole.includes('MAJOR_RESPONSIBLE_PERSON') ||
                  !perpSubmitFlag
                }
                onClick={() => {
                  record?.setState('editing', true);
                }}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
              <Button
                funcType={FuncType.flat}
                disabled={
                  record?.get('reviewStatus') !== 'NEW' ||
                  !userRole.includes('MAJOR_RESPONSIBLE_PERSON') ||
                  !perpSubmitFlag
                }
                onClick={() =>
                  handleSubmitPreCloseInfo(dataSet, record?.get('problemOvertimeCloseId'))
                }
              >
                {intl.get('tarzan.common.button.submit').d('提交')}
              </Button>
            </>
          );
        },
      },
    ],
    [perpSubmitFlag, userRole],
  );

  const otherInfoColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'sequence', align: ColumnAlign.left, width: 120 },
      {
        name: 'objectType',
        align: ColumnAlign.center,
        renderer: ({ record, value }) => {
          let className = '';
          switch (value) {
            case 'VERIFICATION':
              className = 'green';
              break;
            case 'REPLAY':
              className = 'blue';
              break;
            case 'SPREAD':
              className = 'purple';
              break;
            case 'WARN':
              className = 'yellow';
              break;
            default:
              className = 'red';
          }
          return <Tag color={className}>{record!.getField('objectType')!.getText()}</Tag>;
        },
      },
      {
        name: 'objectCode',
        renderer: ({ record, value }) => {
          const { objectType, objectId } = record?.toData();
          let path = '';
          switch (objectType) {
            case 'VERIFICATION':
              path = `/hwms/verification-library-management-platform/dist/${objectId}`;
              break;
            case 'REPLAY':
              path = `/hwms/problem-management/duplicate-problem/detail/${objectId}`;
              break;
            case 'SPREAD':
              path = `/hwms/problem-management/transverse-expand/detail/${objectId}`;
              break;
            case 'WARN':
              path = `/hwms/problem-management/warning/detail/${objectId}`;
              break;
            default:
              path = `/hwms/problem-tree`;
          }
          return (
            <a
              onClick={() => {
                history.push({
                  pathname: path,
                  state: objectType === 'FAILURE' ? { searchValue: value } : undefined,
                });
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'objectStatus' },
      { name: 'createdByDesc' },
      { name: 'creationDate', width: 150, align: ColumnAlign.center },
    ],
    [],
  );

  const RenderPreCloseButtonGroup = observer(
    ({ dataSet, problemStatus, userRole, perpSubmitFlag }) => {
      const handleCancel = () => {
        dataSet.setState('canEdit', false);
        dataSet.reset();
      };

      if (dataSet.getState('canEdit')) {
        return (
          <>
            <Button icon="close" funcType={FuncType.flat} onClick={handleCancel}>
              {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
            </Button>
            <Button
              icon="save"
              funcType={FuncType.flat}
              onClick={() => handleSavePreCloseInfo(dataSet, 'PRE_CLOSE')}
            >
              {intl.get(`${modelPrompt}.button.save`).d('保存')}
            </Button>
          </>
        );
      }

      return (
        <>
          <Button
            icon="send-o"
            funcType={FuncType.flat}
            disabled={
              problemStatus !== 'FOLLOWING' ||
              !userRole.includes('MAJOR_RESPONSIBLE_PERSON') ||
              !perpSubmitFlag ||
              (dataSet.current?.get('reviewStatus') &&
                !['NEW', 'REJECT'].includes(dataSet.current?.get('reviewStatus')))
            }
            onClick={() => handleSubmitPreCloseInfo(dataSet, dataSet.current?.get('problemOvertimeCloseId'))}
          >
            {intl.get(`${modelPrompt}.button.submitReview`).d('提交审批')}
          </Button>
          <Button
            icon="edit-o"
            funcType={FuncType.flat}
            disabled={
              problemStatus !== 'FOLLOWING' ||
              !userRole.includes('MAJOR_RESPONSIBLE_PERSON') ||
              !perpSubmitFlag ||
              (dataSet.current?.get('reviewStatus') &&
                dataSet.current?.get('reviewStatus') !== 'NEW')
            }
            onClick={() => {
              dataSet.setState('canEdit', true);
              dataSet.current?.set('createdPersonLov', currentUserInfo);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        </>
      );
    },
  );

  const RenderOvertimeCloseButtonGroup = observer(
    ({ dataSet, problemStatus, userRole, perpSubmitFlag }) => {
      const handleAdd = () => {
        const addFlag =
          dataSet.find(_record => !['PASS', 'REJECT'].includes(_record?.get('reviewStatus'))) ===
          undefined;
        if (!addFlag) {
          return notification.warning({
            message: '仅问题下所有数据的审批状态为 审批通过 或 审批拒绝 时可点击，请检查！',
          });
        }
        dataSet.create(
          {
            createdPersonLov: currentUserInfo,
            sequence: (dataSet?.length + 1) * 10,
          },
          0,
        );
      };

      return (
        <Button
          icon="playlist_add"
          funcType={FuncType.flat}
          disabled={
            problemStatus !== 'FOLLOWING' ||
            !userRole.includes('MAJOR_RESPONSIBLE_PERSON') ||
            !perpSubmitFlag
          }
          onClick={() => handleAdd()}
        >
          {intl.get(`${modelPrompt}.button.add`).d('新增')}
        </Button>
      );
    },
  );

  const RenderOvertimeCloseHeader = observer(({ dataSet }) => {
    const overtimeDayAll = dataSet.getState('overtimeDayAll') || 0;
    return (
      <span>
        {intl.get(`${modelPrompt}.title.overtimeCloseInfo`).d('延期关闭信息')}（
        {intl
          .get(`${modelPrompt}.title.overtimeDayAll`, { overtimeDayAll })
          .d(`延期关闭天数总计：${overtimeDayAll}天`)}
        ）
      </span>
    );
  });

  return (
    <Tabs
      defaultActiveKey="problemClose"
      tabBarExtraContent={<ExtraButtonGroup userRole={userRole} problemStatus={problemStatus} />}
    >
      <TabPane tab={intl.get(`${modelPrompt}.title.problemClose`).d('问题关闭')} key="problemClose">
        <Collapse
          bordered={false}
          defaultActiveKey={['basicInfo', 'preCloseInfo', 'overtimeCloseInfo', 'otherInfo']}
          collapsible="icon"
          className={styles['collapse-style']}
        >
          <Panel key="basicInfo" header={intl.get(`${modelPrompt}.title.basicInfo`).d('具体信息')}>
            <Form dataSet={problemCloseDs} columns={3}>
              <TextField name="verifyClosedInfo" colSpan={3} />
              <DateTimePicker name="verifyClosedTime" />
              <Lov name="verifyPersonLov" />
            </Form>
            <div className={styles['enable-flag-buttons-group']}>
              <div className={styles['enable-flag-buttons-group-label']}>
                {intl.get(`${modelPrompt}.label.comment`).d('评价')}：
              </div>
              <EnableFlagComponents dataSet={problemCloseDs} name="tempMeasureExeEnableFlag" />
              <EnableFlagComponents dataSet={problemCloseDs} name="perpMeasureExeEnableFlag" />
              <EnableFlagComponents dataSet={problemCloseDs} name="rootReasonConfirmFlag" />
            </div>
          </Panel>
          <Panel
            key="preCloseInfo"
            header={intl.get(`${modelPrompt}.title.preCloseInfo`).d('预关闭信息')}
            extra={
              <RenderPreCloseButtonGroup
                dataSet={preCloseDs}
                userRole={userRole}
                problemStatus={problemStatus}
                perpSubmitFlag={perpSubmitFlag}
              />
            }
          >
            <Form dataSet={preCloseDs} columns={3}>
              <NumberField name="overtimeDay" />
              <TextArea
                name="requestReason"
                newLine
                colSpan={3}
                autoSize={{ minRows: 2, maxRows: 5 }}
              />
              <Select name="reviewStatus" />
              <DateTimePicker name="creationDate" />
              <Lov name="createdPersonLov" />
            </Form>
          </Panel>
          <Panel
            key="overtimeCloseInfo"
            header={<RenderOvertimeCloseHeader dataSet={overtimeCloseTableDs} />}
            extra={
              <RenderOvertimeCloseButtonGroup
                dataSet={overtimeCloseTableDs}
                userRole={userRole}
                problemStatus={problemStatus}
                perpSubmitFlag={perpSubmitFlag}
              />
            }
          >
            <Table dataSet={overtimeCloseTableDs} columns={overtimeCloseColumns} />
          </Panel>
          <Panel key="otherInfo" header={intl.get(`${modelPrompt}.title.otherInfo`).d('其他信息')}>
            <Table dataSet={problemCloseTableDs} columns={otherInfoColumns} />
          </Panel>
        </Collapse>
      </TabPane>
      <TabPane tab={intl.get(`${modelPrompt}.title.finallyScore`).d('最终评分')} key="finallyScore">
        <div className={styles['problem-score-header']}>
          <div className={styles['problem-score-header-label']}>
            {intl.get(`${modelPrompt}.label.finalScore`).d('最终评分')}：
          </div>
          <div
            className={styles['problem-score-header-score']}
          >{`${totalScoreInfo?.totalScore}分`}</div>
          <div className={styles['problem-score-remark']}>
            {intl.get(`${modelPrompt}.label.compose`).d('组成')}:
            {intl
              .get(`${modelPrompt}.label.composeContext`, {
                influenceLevelScore: totalScoreInfo?.influenceLevelScore,
                severityLevelScore: totalScoreInfo?.severityLevelScore,
              })
              .d(
                `（严重程度分${totalScoreInfo?.severityLevelScore}分+影响程度${totalScoreInfo?.influenceLevelScore}分）*评分系数)`,
              )}
          </div>
        </div>
        <Collapse
          bordered={false}
          defaultActiveKey={['problemScore']}
          collapsible="icon"
          className={styles['collapse-style']}
        >
          <Panel
            key="problemScore"
            header={intl.get(`${modelPrompt}.title.problemScore`).d('问题评分')}
            extra={
              <RenderScoreButtonGroup
                dataSet={finallyScoreFormDs}
                userRole={userRole}
                problemStatus={problemStatus}
              />
            }
          >
            <div className={styles['score-panel-content']}>
              <Form dataSet={finallyScoreFormDs} labelWidth={70} columns={3}>
                <NumberField
                  name="scoringCoefficient"
                  onChange={value => handleUpdateTotalScore(value)}
                />
                <TextField name="coefficientConfirmBasis" colSpan={2} />
              </Form>
              <Table dataSet={finallyScoreTableDs} columns={finallyScoreColumns} />
            </div>
          </Panel>
        </Collapse>
      </TabPane>
    </Tabs>
  );
};
export default ValidateCloseComponent;
