/**
 * 工单类型明细页
 * @date 2020/07/01
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2019,Hand
 */

import { connect } from 'dva';
import React, { Component } from 'react';
import { routerRedux } from 'dva/router';
import { Tabs, Spin } from 'choerodon-ui';
import { Button, DataSet } from 'choerodon-ui/pro';
import { observer } from 'mobx-react';

import { isUndefined } from 'lodash';
import { Bind } from 'lodash-decorators';

import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import IconSelectModal from 'alm/components/IconSelect/select';
import formatterCollections from 'utils/intl/formatterCollections';
import { EDIT_DEFAULT_CLASSNAME, DETAIL_DEFAULT_CLASSNAME } from 'alm/utils/constants';

import './index.less';
import Basic from './Basic';
import ReminderRules from './ReminderRules';
import InspectList from './Inspect';
import { detailFormDS, reminderRulesDS } from '../Stores/WoTypeDetailDS';

@connect(() => ({
  tenantId: getCurrentOrganizationId(),
}))
@formatterCollections({ code: ['alm.common', 'alm.component', 'amtc.woType'] })
@observer
class Detail extends Component {
  constructor(props) {
    super(props);
    const { state } = props.location;
    this.state = {
      editFlag: state && state.isEdit ? state.isEdit : false,
      iconsModalVisible: false,
      woTypeDetailAboutName: '', // 工单类型详情-名称
      fetchDetailInfoLoading: true,
    };
    this.detailFormDS = new DataSet(detailFormDS());
    this.reminderRulesDS = new DataSet(reminderRulesDS());
  }

  componentDidMount() {
    this.initFormData();
  }

  /**
   * 初始化表单数据
   */
  @Bind()
  async initFormData() {
    const self = this;
    const {
      match: { params },
    } = self.props;
    const { id } = params;
    if (!isUndefined(id)) {
      self.detailFormDS.setQueryParameter('id', id);
      await self.detailFormDS.query();
      self.setState({
        woTypeDetailAboutName: self.detailFormDS.current.get('woTypeName'),
        fetchDetailInfoLoading: false,
      });
      self.reminderRulesDS.setQueryParameter('id', id);
      await self.reminderRulesDS.query();
      const reminderRulesData = self.reminderRulesDS.toData();
      self.reminderRulesDS.loadData([{
        ...reminderRulesData[0],
        woTypeId: reminderRulesData?.length ? reminderRulesData[0].woTypeId : id,
      }]);
    } else {
      self.detailFormDS.create();
      self.setState({
        fetchDetailInfoLoading: false,
      });
    }
  }

  /**
   * 打开图标选择modal
   */
  @Bind()
  openIconModal = () => {
    this.setState({
      iconsModalVisible: true,
    });
  };

  /**
   * 选中图标
   * @param {string} icon 图标码
   */
  @Bind()
  onIconSelect(icon) {
    this.detailFormDS.current.set('icon', icon);
    this.setState({
      iconsModalVisible: false,
    });
  }

  /**
   * 关闭图标选择modal
   */
  @Bind()
  closeIconsModal() {
    this.setState({
      iconsModalVisible: false,
    });
  }

  /**
   * 编辑
   */
  @Bind()
  handleEdit() {
    const { editFlag } = this.state;
    // 如果是从编辑切换到查看状态 要重置ds 清空改动
    if (editFlag) {
      this.detailFormDS.current.reset();
      this.reminderRulesDS.current.reset();
    }
    this.setState({ editFlag: !editFlag });
  }

  /**
   * 保存
   */
  @Bind()
  async handleSave() {
    const {
      match: { params },
      dispatch,
    } = this.props;
    const { id } = params;
    if (await this.detailFormDS.validate(false, false)) {
      this.detailFormDS.requestType = isUndefined(id) ? 'POST' : 'PUT';
      await this.reminderRulesDS.submit()
      const res = await this.detailFormDS.submit();
      if (isUndefined(id)) {
        dispatch(
          routerRedux.push({
            pathname: `/amtc/wo-type/detail/${res.content[0].woTypeId}`,
          }),
        );
      } else {
        this.setState({
          fetchDetailInfoLoading: true,
        });
        await this.detailFormDS.query();
        await this.reminderRulesDS.query();
        this.setState({
          woTypeDetailAboutName: this.detailFormDS.current.get('woTypeName'),
          fetchDetailInfoLoading: false,
          editFlag: false,
        });
      }
    }
  }

  render() {
    const commonPromptCode = 'amtc.woType.view';
    const { TabPane } = Tabs;
    const {
      editFlag,
      iconsModalVisible,
      woTypeDetailAboutName,
      fetchDetailInfoLoading,
    } = this.state;
    const {
      match: { params, url },
    } = this.props;
    const { id } = params;
    const isNew = url.indexOf('create') !== -1;
    const displayEditFlag = isNew || editFlag ? { display: 'none' } : { display: 'block' };
    const displayFlagBtn = isNew || editFlag ? { display: 'block' } : { display: 'none' };
    const displayCloseBtn = isNew || !editFlag ? { display: 'none' } : { display: 'block' };
    const iconsModalProps = {
      visible: iconsModalVisible,
      onSelect: this.onIconSelect,
      onCancel: this.closeIconsModal,
    };
    return (
      <React.Fragment>
        <Header
          title={intl.get(`${commonPromptCode}.title.woType`).d('工单类型')}
          backPath="/amtc/wo-type/list"
        >
          <Button icon="save" color="primary" style={displayFlagBtn} onClick={this.handleSave}>
            {intl.get('hzero.common.button.save').d('保存')}
          </Button>
          <Button
            icon="mode_edit"
            color="primary"
            style={displayEditFlag}
            onClick={this.handleEdit}
          >
            {intl.get('hzero.common.button.edit').d('编辑')}
          </Button>
          <Button icon="close" style={displayCloseBtn} onClick={this.handleEdit}>
            {intl.get('hzero.common.button.cancel').d('取消')}
          </Button>
        </Header>
        <Content>
          <Spin spinning={fetchDetailInfoLoading}>
            <Tabs
              defaultActiveKey="1"
              className={`wo-type ${
                isNew || editFlag ? EDIT_DEFAULT_CLASSNAME : DETAIL_DEFAULT_CLASSNAME
              }`}
            >
              <TabPane tab={intl.get(`${commonPromptCode}.tab.basicTab`).d('基本')} key="1">
                <Basic
                  detailFormDS={this.detailFormDS}
                  isNew={isNew}
                  editFlag={editFlag}
                  openIconModal={this.openIconModal}
                />
              </TabPane>
              {isNew ? null : (
                <TabPane tab={intl.get(`${commonPromptCode}.tab.checklist`).d('检查项')} key="2">
                  <InspectList parentId={id} parentName={woTypeDetailAboutName} />
                </TabPane>
              )}
              {isNew ? null : (
                <TabPane tab={intl.get(`${commonPromptCode}.tab.reminderRules`).d('提醒规则')} key="3">
                  <ReminderRules
                    reminderRulesDS={this.reminderRulesDS}
                    isNew={isNew}
                    editFlag={editFlag}
                  />
                </TabPane>
              )}
            </Tabs>
          </Spin>
        </Content>
        <IconSelectModal {...iconsModalProps} />
      </React.Fragment>
    );
  }
}
export default Detail;
