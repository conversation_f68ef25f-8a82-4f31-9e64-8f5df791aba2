/**
 * @Description: 查看产品拆解基础数据维护-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

// ${ BASIC.TARZAN_SAMPLING }

// 保存产品拆解基础数据
export function saveTeardownConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-teardown-head/save`,
    method: 'POST',
  };
}

// 更新产品拆解基础数据
export function updateTeardownConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-teardown-head/update`,
    method: 'POST',
  };
}

// 查询产品拆解基础数据信息
export function fetchTeardownConfig(id) {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-teardown-head/${id}`,
    method: 'GET',
  };
}
