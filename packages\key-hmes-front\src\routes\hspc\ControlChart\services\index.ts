/**
 * @Description: 控制控制图-service
 * @Author: <<EMAIL>>
 * @Date: 2021-11-25 17:25:39
 * @LastEditTime: 2021-12-13 15:04:37
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from 'hcm-components-front/lib/utils/config';

const tenantId = getCurrentOrganizationId();

// 控制控制图-获取控制图详细数据
export function FetchControlChartDetail() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/detail/ui`,
    method: 'GET',
  };
}

// 控制控制图-从分析控制图来时，查询分析控制图信息
export function FetchAnalysisControlChartDetail() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis/detail/ui`,
    method: 'GET',
  };
}

// 控制控制图-创建或保存控制控制图
export function SaveControlChartDetail() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/save/ui`,
    method: 'POST',
  };
}

// 控制控制图-定义控制图信息-获取默认的判异规则组
export function FetchDefaultJudgementGroup() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/judgement-group/default/ui`,
    method: 'GET',
  };
}

// 控制控制图-实时图形展示
export function FetchControlChartGraphicData() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/chart/ui`,
    method: 'GET',
  };
}

// 控制控制图-历史图形展示
export function FetchHistoryControlChartGraphicData() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/history-chart/ui`,
    method: 'GET',
  };
}

// 控制控制图-历史图形CPK展示
export function FetchCPKData() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/cpk/control-chart/ui`,
    method: 'GET',
  };
}

// 控制控制图-报警消息-获取报警消息明细
export function FetchControlChartAlarmMessageDetail() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control-message/detail/ui`,
    method: 'GET',
  };
}

// 控制控制图-报警消息-保存报警消息
export function SaveControlChartAlarmMessage() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control-message/save/ui`,
    method: 'POST',
  };
}

// 控制控制图-报警消息-删除报警消息
export function DeleteControlChartAlarmMessage() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control-message/delete/ui`,
    method: 'POST',
  };
}

// 控制控制图-手工录入图形展示
export function FetchControlChartManaulAccessGraphicData() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/chart/ui`,
    method: 'GET',
  };
}

// 控制控制图-手工录入图形展示
export function SaveontrolChartManaulAccessGraphicData() {
  return {
    url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/input-save/ui`,
    method: 'POST',
  };
}

// 控制控制图-隐藏接口
export function HideControl() {
  return {
    url: `/wr-tznl/v1/${tenantId}/hme-control-measure-record/hide-record/ui`,
    method: 'POST',
  };
}

// 控制控制图-全部隐藏接口
export function HideControlAll() {
  return {
    url: `/wr-tznl/v1/${tenantId}/hme-control-measure-record/show-hide-record/ui`,
    method: 'POST',
  };
}
