/*
 * @Description: 新建问题-抽屉
 * @Author: <<EMAIL>>
 * @Date: 2023-10-30 09:40:58
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-01-09 11:44:35
 */
import React, { useEffect, useState } from 'react';
import {
  Select,
  Form,
  TextField,
  Lov,
  TextArea,
  DateTimePicker,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import {getCurrentUserId, getCurrentOrganizationId, getCurrentUser} from 'utils/utils';
import moment from "moment/moment";
import { QueryUserInfo, QueryUserEmployeeInfo } from '../services';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const CreateProblemPage = ({problemInfoDs, drawerState}) => {
  // 员工部门或者上级部门编码是否为“90000020”
  const [unitFlag, setUnitFlag] = useState<string | null>(null);
  const { run: queryUserInfo } = useRequest(QueryUserInfo(), {
    manual: true,
    needPromise: true,
  });

  const { run: queryUserEmployeeInfo } = useRequest(QueryUserEmployeeInfo(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    handleQueryUser();
    handleQueryEmployee();

    if (Object.keys(drawerState || {}).length === 0) {
      return;
    }
    const {
      siteId,
      siteName,
      theme,
      prianalResjudgItemId,
      inspectDocLineId,
      inspectItemCode,
    } = drawerState || {};
    if (prianalResjudgItemId) {
      problemInfoDs.current?.set('problemCategory', 'SALES');
      problemInfoDs.current?.set('problemTitle', theme);
      problemInfoDs.current?.set('siteName', siteName);
      problemInfoDs.current?.set('siteId', siteId);
      problemInfoDs.current?.set('registerPerson', userInfo.id);
      problemInfoDs.current?.set('registerPersonRealName', userInfo.realName);
      problemInfoDs.current?.set('registerTime', moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
      problemInfoDs.current?.set('prianalResjudgItemId', prianalResjudgItemId);
    }
    if (inspectDocLineId) {
      problemInfoDs.current?.set('inspectDocLineId', inspectDocLineId);
      problemInfoDs.current?.set('problemTitle', inspectItemCode);
    }
  }, [drawerState]);

  const handleQueryUser = () => {
    queryUserInfo({
      queryParams: {
        lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
        page: 0,
        size: 10,
        tenantId,
        userId: getCurrentUserId(),
      },
    }).then(res => {
      const { content, empty, failed, message } = res;
      if (res && failed) {
        notification.error({
          message: message || intl.get('hzero.common.notification.error').d('操作失败'),
        });
        return;
      }
      if (empty) {
        return;
      }
      const userInfo = content?.length ? content[0] : {};
      problemInfoDs.current?.set('registerPersonLov', {
        ...userInfo,
      });
      // problemInfoDs.current?.set('proposePersonLov', {
      //   ...userInfo,
      // });
    });
  };

  const handleQueryEmployee = () => {
    queryUserEmployeeInfo({
      queryParams: {
        userId: getCurrentUserId(),
      },
    }).then(res => {
      if (res?.success) {
        const { rows } = res;
        problemInfoDs.current?.set('proposePersonLov', {
          ...rows || {},
        });
        if (rows?.parentUnitCode === '90000020' || rows?.unitCode === '90000020') {
          setUnitFlag('Y');
        } else {
          setUnitFlag('N');
        }
      }
    });
  };

  const handleChangeProblemApply = value => {
    if (!value?.problemApplyId) {
      return;
    }
    problemInfoDs.current?.set('problemTitle', value.problemTitle);
    problemInfoDs.current?.set('problemDescription', value.problemDetail);
    problemInfoDs.current?.set('proposePersonLov', {
      employeeId: value.applyBy,
      name: value.applyByName,
    });
    problemInfoDs.current?.set('proposePersonCompanyName', value.unitName);
  };

  return (
    <Form dataSet={problemInfoDs} labelWidth={112}>
      <Select
        name="problemCategory"
        disabled={drawerState?.prianalResjudgItemId}
        optionsFilter={record => {
          console.log(unitFlag);
          if (unitFlag === 'Y') {
            return record?.get('value') === 'MANUFACTURE';
          }
          return record?.get('value') !== 'MANUFACTURE';
        }}
      />
      <Lov name="siteLov" disabled={drawerState?.prianalResjudgItemId} />
      <Lov name="problemApplyLov" onChange={handleChangeProblemApply} />
      <Select name="problemStatus" />
      <TextField name="problemTitle" />
      <TextArea name="problemDescription" rowSpan={3} />
      <Lov name="leadPersonLov" />
      <Lov name="registerPersonLov" />
      <TextField name="registerPersonCompanyName" />
      <Lov name="proposePersonLov" />
      <TextField name="proposePersonCompanyName" />
      <DateTimePicker name="proposeTimePeriodStart" />
      <DateTimePicker name="proposeTimePeriodEnd" />
    </Form>
  );
};

export default CreateProblemPage;