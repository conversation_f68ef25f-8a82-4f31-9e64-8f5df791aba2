import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'OutsourcedInspectionManagement';
const tenantId = getCurrentOrganizationId();

const productionDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'cellModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cellModel`).d('电芯型号'),
    },
    {
      name: 'orderNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNumber`).d('订单号'),
    },
    {
      name: 'smallBatchNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.smallBatchNo`).d('小批次号'),
    },
    {
      name: 'windOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windOperator`).d('卷绕作业人'),
    },
    {
      name: 'windEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windEquipmentLineNo`).d('卷绕设备线体号'),
    },
    {
      name: 'windStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.windStartTime`).d('卷绕开始时间'),
      range: ['windStartTimeFrom', 'windStartTimeTo'],
    },
    {
      name: 'windEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.windEndTime`).d('卷绕结束时间'),
      range: ['windEndTimeFrom', 'windEndTimeTo'],
    },
    {
      name: 'packageOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageOperator`).d('封装作业人'),
    },
    {
      name: 'packageEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageEquipmentLineNo`).d('封装设备线体号'),
    },
    {
      name: 'packageStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.packageStartTime`).d('封装开始时间'),
      range: ['packageStartTimeFrom', 'packageStartTimeTo'],
    },
    {
      name: 'packageEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.packageEndTime`).d('封装结束时间'),
      range: ['packageEndTimeFrom', 'packageEndTimeTo'],
    },
    {
      name: 'injectionOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionOperator`).d('注液作业人'),
    },
    {
      name: 'injectionEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionEquipmentLineNo`).d('注液设备线体号'),
    },
    {
      name: 'injectionStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.injectionStartTime`).d('注液开始时间'),
      range: ['injectionStartTimeFrom', 'injectionStartTimeTo'],
    },
    {
      name: 'injectionEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.injectionEndTime`).d('注液结束时间'),
      range: ['injectionEndTimeFrom', 'injectionEndTimeTo'],
    },
    {
      name: 'transformOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformOperator`).d('化成作业人'),
    },
    {
      name: 'transformEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformEquipmentLineNo`).d('化成设备线体号'),
    },
    {
      name: 'transformStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.transformStartTime`).d('化成开始时间'),
      range: ['transformStartTimeFrom', 'transformStartTimeTo'],
    },
    {
      name: 'transformEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.transformEndTime`).d('化成结束时间'),
      range: ['transformEndTimeFrom', 'transformEndTimeTo'],
    },
    {
      name: 'combinationOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationOperator`).d('六合一作业人'),
    },
    {
      name: 'combinationEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationEquipmentLineNo`).d('六合一设备线体号'),
    },
    {
      name: 'combinationStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.combinationStartTime`).d('六合一开始时间'),
      range: ['combinationStartTimeFrom', 'combinationStartTimeTo'],
    },
    {
      name: 'combinationEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.combinationEndTime`).d('六合一结束时间'),
      range: ['combinationEndTimeFrom', 'combinationEndTimeTo'],
    },
    {
      name: 'sortOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortOperator`).d('分选作业人'),
    },
    {
      name: 'sortEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortEquipmentLineNo`).d('分选设备线体号'),
    },
    {
      name: 'sortStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sortStartTime`).d('分选开始时间'),
      range: ['sortStartTimeFrom', 'sortStartTimeTo'],
    },
    {
      name: 'sortEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sortEndTime`).d('分选结束时间'),
      range: ['sortEndTimeFrom', 'sortEndTimeTo'],
    },
  ],
  fields: [
    {
      name: 'cellModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cellModel`).d('电芯型号'),
    },
    {
      name: 'orderNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNumber`).d('订单号'),
    },
    {
      name: 'smallBatchNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.smallBatchNo`).d('小批次号'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('数量'),
    },
    {
      name: 'windInputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windInputQuantity`).d('卷绕投入数量'),
    },
    {
      name: 'windOutputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windOutputQuantity`).d('卷绕产出数量'),
    },
    {
      name: 'windDefectiveQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windDefectiveQuantity`).d('卷绕不良数量'),
    },
    {
      name: 'windYieldRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windYieldRate`).d('卷绕良品率'),
    },
    {
      name: 'windOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windOperator`).d('卷绕作业人'),
    },
    {
      name: 'windEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windEquipmentLineNo`).d('卷绕设备线体号'),
    },
    {
      name: 'windStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windStartTime`).d('卷绕开始时间'),
    },
    {
      name: 'windEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.windEndTime`).d('卷绕结束时间'),
    },
    {
      name: 'packageInputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageInputQuantity`).d('封装投入数量（报工数）'),
    },
    {
      name: 'packageOutputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageOutputQuantity`).d('封装产出数量（良品数）'),
    },
    {
      name: 'packageDefectiveQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageDefectiveQuantity`).d('封装不良数量'),
    },
    {
      name: 'packageYieldRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageYieldRate`).d('封装良品率'),
    },
    {
      name: 'packageOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageOperator`).d('封装作业人'),
    },
    {
      name: 'packageEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageEquipmentLineNo`).d('封装设备线体号'),
    },
    {
      name: 'packageStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageStartTime`).d('封装开始时间'),
    },
    {
      name: 'packageEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageEndTime`).d('封装结束时间'),
    },
    {
      name: 'injectionInputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionInputQuantity`).d('注液投入数量'),
    },
    {
      name: 'injectionOutputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionOutputQuantity`).d('注液产出数量（良品数）'),
    },
    {
      name: 'injectionDefectiveQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionDefectiveQuantity`).d('注液不良数量'),
    },
    {
      name: 'injectionYieldRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionYieldRate`).d('注液良品率'),
    },
    {
      name: 'injectionOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionOperator`).d('注液作业人'),
    },
    {
      name: 'injectionEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionEquipmentLineNo`).d('注液设备线体号'),
    },
    {
      name: 'injectionStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionStartTime`).d('注液开始时间'),
    },
    {
      name: 'injectionEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionEndTime`).d('注液结束时间'),
    },
    {
      name: 'transformInputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformInputQuantity`).d('化成投入数量（报工数）'),
    },
    {
      name: 'transformOutputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformOutputQuantity`).d('化成产出数量（良品数）'),
    },
    {
      name: 'transformDefectiveQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformDefectiveQuantity`).d('化成不良数量'),
    },
    {
      name: 'transformYieldRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformYieldRate`).d('化成良品率'),
    },
    {
      name: 'transformOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformOperator`).d('化成作业人'),
    },
    {
      name: 'transformEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformEquipmentLineNo`).d('化成设备线体号'),
    },
    {
      name: 'transformStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformStartTime`).d('化成开始时间'),
    },
    {
      name: 'transformEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transformEndTime`).d('化成结束时间'),
    },
    {
      name: 'combinationInputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationInputQuantity`).d('六合一投入数量（报工数）'),
    },
    {
      name: 'combinationOutputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationOutputQuantity`).d('六合一产出数量（良品数）'),
    },
    {
      name: 'combinationDefectiveQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationDefectiveQuantity`).d('六合一不良数量'),
    },
    {
      name: 'combinationYieldRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationYieldRate`).d('六合一良品率'),
    },
    {
      name: 'combinationOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationOperator`).d('六合一作业人'),
    },
    {
      name: 'combinationEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationEquipmentLineNo`).d('六合一设备线体号'),
    },
    {
      name: 'combinationStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationStartTime`).d('六合一开始时间'),
    },
    {
      name: 'combinationEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.combinationEndTime`).d('六合一结束时间'),
    },
    {
      name: 'sortInputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortInputQuantity`).d('分选投入数量（报工数)'),
    },
    {
      name: 'sortOutputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortOutputQuantity`).d('分选产出数量（良品数）'),
    },
    {
      name: 'sortDefectiveQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortDefectiveQuantity`).d('分选不良数量'),
    },
    {
      name: 'sortYieldRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortYieldRate`).d('分选良品率'),
    },
    {
      name: 'sortOperator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortOperator`).d('分选作业人'),
    },
    {
      name: 'sortEquipmentLineNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortEquipmentLineNo`).d('分选设备线体号'),
    },
    {
      name: 'sortStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortStartTime`).d('分选开始时间'),
    },
    {
      name: 'sortEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortEndTime`).d('分选结束时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const paramsData = {
        ...data,
        ...data?.windStartTime,
        ...data?.windEndTime,
        ...data?.packageStartTime,
        ...data?.packageEndTime,
        ...data?.injectionStartTime,
        ...data?.injectionEndTime,
        ...data?.transformStartTime,
        ...data?.transformEndTime,
        ...data?.combinationStartTime,
        ...data?.combinationEndTime,
        ...data?.sortStartTime,
        ...data?.sortEndTime,
      };
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-outsourcing-inspect-product/page/ui`,
        method: 'POST',
        data: paramsData,
      };
    },
  },
});

const materialInformationDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'cellModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cellModel`).d('电芯型号'),
    },
    {
      name: 'cellBatchNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cellBatchNo`).d('电芯批次号'),
    },
    {
      name: 'orderNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNumber`).d('订单号'),
    },
    {
      name: 'processWorkcell',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processWorkcell`).d('工序'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialBomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBomCode`).d('物料BOM号'),
    },
    {
      name: 'rawMaterialBatchNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rawMaterialBatchNo`).d('原材料批次号'),
    },
    {
      name: 'startFeedTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startFeedTime`).d('开始投料时间'),
      range: ['startFeedTimeFrom', 'startFeedTimeTo'],
    },
  ],
  fields: [
    {
      name: 'cellModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cellModel`).d('电芯型号'),
    },
    {
      name: 'cellBatchNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cellBatchNo`).d('电芯批次号'),
    },
    {
      name: 'orderNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNumber`).d('订单号'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('数量'),
    },
    {
      name: 'processWorkcell',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processWorkcell`).d('工序'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialBomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBomCode`).d('物料BOM号'),
    },
    {
      name: 'rawMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rawMaterialName`).d('原材料名称'),
    },
    {
      name: 'rawMaterialBatchNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rawMaterialBatchNo`).d('原材料批次号'),
    },
    {
      name: 'inputQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inputQuantity`).d('投料数量'),
    },
    {
      name: 'startFeedTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startFeedTime`).d('开始投料时间'),
    },
    {
      name: 'operator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operator`).d('投料人'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const paramsData = {
        ...data,
        ...data?.startFeedTime,
      };
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-outsourcing-inspect-material/page/ui`,
        method: 'POST',
        data: paramsData,
      };
    },
  },
});

const equipmentInformationDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'orderNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNumber`).d('订单号'),
    },
    {
      name: 'materialBomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBomCode`).d('物料BOM号'),
    },
    {
      name: 'processRoute',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processRoute`).d('工艺路线'),
    },
    {
      name: 'processWorkcell',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processWorkcell`).d('工序'),
    },
    {
      name: 'lineBodyNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineBodyNumber`).d('线体号'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
  ],
  fields: [
    {
      name: 'orderNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNumber`).d('订单号'),
    },
    {
      name: 'materialBomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialBomCode`).d('物料BOM号'),
    },
    {
      name: 'processRoute',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processRoute`).d('工艺路线'),
    },
    {
      name: 'processWorkcell',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processWorkcell`).d('工序'),
    },
    {
      name: 'lineBodyNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineBodyNumber`).d('线体号'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-outsourcing-inspect-equipment/page/ui`,
        method: 'POST',
      };
    },
  },
});

const batteryPerformanceDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'cellNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cellNumber`).d('电池编号'),
    },
    {
      name: 'forkPlateNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.forkPlateNumber`).d('叉板号'),
    },
    {
      name: 'packageNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageNo`).d('包装箱号'),
    },
    {
      name: 'processWorkcell',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.processWorkcell`).d('打包日期'),
      range: ['packagingDateFrom', 'packagingDateTo'],
    },
  ],
  fields: [
    {
      name: 'cellNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cellNumber`).d('电池编号'),
    },
    {
      name: 'injectionVolume',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.injectionVolume`).d('注液量'),
    },
    {
      name: 'netVolume',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.netVolume`).d('净液量'),
    },
    {
      name: 'capacity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.capacity`).d('容量'),
    },
    {
      name: 'ocv1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ocv1`).d('OCV1'),
    },
    {
      name: 'ir1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ir1`).d('IR1'),
    },
    {
      name: 'ocv1TestTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ocv1TestTime`).d('OCV1测试时间'),
    },
    {
      name: 'ocv2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ocv2`).d('OCV2'),
    },
    {
      name: 'ir2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ir2`).d('IR2'),
    },
    {
      name: 'ocv2TestTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ocv2TestTime`).d('OCV2测试时间'),
    },

    {
      name: 'kvalue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.kvalue`).d('K值'),
    },
    {
      name: 'sortingInternalResistance',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortingInternalResistance`).d('分选内阻'),
    },
    {
      name: 'sortingVoltage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sortingVoltage`).d('分选电压'),
    },
    {
      name: 'shellVoltage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shellVoltage`).d('壳电压'),
    },
    {
      name: 'thickness',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.thickness`).d('厚度'),
    },
    {
      name: 'width',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.width`).d('宽度'),
    },
    {
      name: 'positivePoleGlueHeight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.positivePoleGlueHeight`).d('正极耳胶高'),
    },
    {
      name: 'negativePoleGlueHeight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.negativePoleGlueHeight`).d('负极耳胶高'),
    },
    {
      name: 'positivePoleDistanceEdge',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.positivePoleDistanceEdge`).d('正极耳距边'),
    },
    {
      name: 'negativePoleDistanceEdge',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.negativePoleDistanceEdge`).d('负极耳距边'),
    },
    {
      name: 'forkPlateNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.forkPlateNumber`).d('叉板号'),
    },
    {
      name: 'packageNo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageNo`).d('包装箱号'),
    },
    {
      name: 'forkPlateQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.forkPlateQuantity`).d('叉板数量'),
    },
    {
      name: 'packagingDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.packagingDate`).d('打包日期'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const paramsData = {
        ...data,
        ...data?.processWorkcell,
      };
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/aprs-outsourcing-inspect-cell/page/ui`,
        method: 'POST',
        data: paramsData,
      };
    },
  },
});

export { productionDS, materialInformationDS, equipmentInformationDS, batteryPerformanceDS };
