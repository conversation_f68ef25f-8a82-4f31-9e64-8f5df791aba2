/*
 * @Description: 条码信息查询报表-高级查询
 * @Author: <<EMAIL>>
 * @Date: 2024-01-09 17:56:43
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-01-23 11:32:00
 */
import React, { useEffect, useState } from 'react';
import { Table, DataSet, TextField, Icon } from 'choerodon-ui/pro';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import ExcelExport from 'components/ExcelExport';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import queryString from 'query-string';
import { overrideTableBar } from '@components/tarzan-ui';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import LovModal from '@/components/BatchInput/LovModal';
import { advancedQueryDS } from '../stores/advancedQueryDS';

const modelPrompt = 'tarzan.hmes.barcodeInfoQueryReport';
const tenantId = getCurrentOrganizationId();
const endUrl = "";

const AdvancedQueryList = props => {
  const {
    tableDs,
    location,
  } = props;
  const inputLovDS = new DataSet(InputLovDS());
  const [columnList, setColumnList] = useState<ColumnProps[]>([]);

  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);

  const handleQueryCallback = ({ dataSet, data }) => {
    if (data.length && !columnList?.length) {
      const _columnList: ColumnProps[] = [];
      Object.keys(data[0] || {}).forEach((item) => {
        dataSet.addField(item, {
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.advancedQuery.${item}`).d(item),
        })
        _columnList.push({
          name: item,
          width: 150,
        });
      });
      setColumnList(_columnList);
    }
  };

  useDataSetEvent(tableDs, 'beforeLoad', handleQueryCallback);

  useEffect(() => {
    if (location.search) {
      const { identificationList, workOrderNum, containerLov } = queryString.parse(location.search.substring(1)) || {};
      const _containerLov: any = typeof containerLov === 'string' ? JSON.parse(containerLov) : undefined;
      tableDs.queryDataSet?.loadData([{
        identificationList,
        workOrderNum,
        containerLov: _containerLov,
      }]);
      tableDs.query(tableDs.currentPage);
    }
  }, [location.search]);

  // const columns: ColumnProps[] = useMemo(() => {
  //   return [
  //     { name: 'eoId' },
  //     { name: 'eoNum' },
  //     { name: 'eoIdentification' },
  //     { name: 'materialLotId' },
  //     { name: 'materialLotCode' },
  //     { name: 'materialLotIdentification' },
  //     { name: 'materialId' },
  //     { name: 'materialCode' },
  //     { name: 'materialName' },
  //     { name: 'eoQty' },
  //     {
  //       name: 'qualityStatus',
  //       width: 140,
  //       align: ColumnAlign.center,
  //       renderer: props => <GenStatusRender {...props} />,
  //     },
  //     {
  //       name: 'status',
  //       width: 140,
  //       align: ColumnAlign.center,
  //       renderer: props => <GenStatusRender {...props} />,
  //     },
  //     { name: 'workcellId' },
  //     { name: 'workcellCode' },
  //     { name: 'workcellName' },
  //     { name: 'equipmentId' },
  //     { name: 'equipmentCode' },
  //     { name: 'routerStepId' },
  //     { name: 'routerStepDesc' },
  //     { name: 'routerStepName' },
  //     {
  //       name: 'routerStepStatus',
  //       width: 140,
  //       align: ColumnAlign.center,
  //       renderer: props => <GenStatusRender {...props} />,
  //     },
  //     { name: 'workOrderId' },
  //     { name: 'workOrderNum' },
  //     { name: 'prodLineId' },
  //     { name: 'prodLineCode' },
  //     { name: 'marking' },
  //     { name: 'disposalFunctionDesc' },
  //     { name: 'ncIncidentId' },
  //     { name: 'ncIncidentNum' },
  //     { name: 'ncRecordId' },
  //     { name: 'ncRecordNum' },
  //     {
  //       name: 'reworkFlag',
  //       align: ColumnAlign.center,
  //       width: 120,
  //       renderer: ({ value, record, name }) => (
  //         <Badge
  //           status={value === 'Y' ? 'success' : 'error'}
  //           text={record!.getField(name)!.getText()}
  //         />
  //       ),
  //     },
  //     { name: 'operationName' },
  //     { name: 'endOperationName' },
  //     {
  //       name: 'interceptionFlag',
  //       align: ColumnAlign.center,
  //       width: 120,
  //       renderer: ({ value, record, name }) => (
  //         <Badge
  //           status={value === 'Y' ? 'success' : 'error'}
  //           text={record!.getField(name)!.getText()}
  //         />
  //       ),
  //     },
  //     { name: 'interceptionOperationName' },
  //     {
  //       name: 'degradeFlag',
  //       align: ColumnAlign.center,
  //       width: 120,
  //       renderer: ({ value, record, name }) => (
  //         <Badge
  //           status={value === 'Y' ? 'success' : 'error'}
  //           text={record!.getField(name)!.getText()}
  //         />
  //       ),
  //     },
  //     { name: 'mtModLocatorCode' },
  //     { name: 'mtModLocatorName' },
  //     { name: 'lot' },
  //   ];
  // }, []);

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
      // handleSearch()
    }
  };

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i]) || i.includes('Lov') || i === '__dirty') {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('条码信息查询报表—高级信息')}>
        <ExcelExport
          allBody
          // exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}${endUrl}/v1/${tenantId}/hme-identification/detailed/export`}
          method="post"
          queryParams={getExportQueryParams}
        >
          {intl.get('htms.FrontConfig.button.export').d('导出')}
        </ExcelExport>
      </Header>
      <Content>
        <Table
          queryBar={overrideTableBar}
          queryFields={{
            identificationList: (
              <TextField
                name="identificationList"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(
                          true,
                          'identificationList',
                          intl.get(`${modelPrompt}.identification`).d('条码号'),
                        )
                      }
                    />
                  </div>
                }
              />
            ),
            workOrderNum: (
              <TextField
                name="workOrderNum"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(
                          true,
                          'workOrderNum',
                          intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
                        )
                      }
                    />
                  </div>
                }
              />
            ),
            lotNumList: (
              <TextField
                name="lotNumList"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(
                          true,
                          'lotNumList',
                          intl.get(`${modelPrompt}.lotNumList`).d('批次号'),
                        )
                      }
                    />
                  </div>
                }
              />
            ),
          }}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columnList}
          customizedCode="barcodeInfoQueryReportAdvancedQuery_customizedCode"
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...advancedQueryDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(AdvancedQueryList),
);
