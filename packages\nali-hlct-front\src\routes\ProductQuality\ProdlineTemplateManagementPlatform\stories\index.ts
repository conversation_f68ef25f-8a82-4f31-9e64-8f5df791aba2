/**
 * @Description: 检验项目组维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-01-10 16:54:12
 * @LastEditTime: 2023-05-18 16:49:50
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.prodlineReview.prodlineReviewTemp';
const tenantId = getCurrentOrganizationId();

// 检验组列表
const ListTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'verificationId',
  queryFields: [
    {
      name: 'prodlineReviewTmpNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodlineReviewTmpNum`).d('产线审核模板编号'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('审核状态'),
      lookupCode: 'YP.QIS.STATUS',
    },
    {
      name: 'reviewType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('审核类型'),
      lookupCode: 'YP.QIS.REVIEW_TYPE',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      textField: 'siteCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'createLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdRealName`).d('创建人'),
      lovCode: 'HIAM.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        // dataType: 'CALCULATE_FORMULA',
        tenantId,
      },
    },
    {
      name: 'createdBy',
      bind: 'createLov.id',
    },
    {
      name: 'reviewStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStage`).d('审核阶段'),
      lookupCode: 'YP.QIS.REVIEW_STAGE',
    },
  ],
  fields: [
    {
      name: 'prodlineReviewTmpNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodlineReviewTmpNum`).d('产线审核模板编号'),
    },
    {
      name: 'statusMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'reviewTypeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('审核类型'),
    },
    {
      name: 'reviewStageMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStage`).d('审核阶段'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdRealName`).d('创建人'),
    },
    {
      name: 'reviewByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewByRealName`).d('审批人'),
    },
    {
      name: 'reviewDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewDate`).d('审批时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-review-tmps/list/for/ui`,
        // url: `${prefix}/v1/${tenantId}/qis-prodline-review-tmps/list/for/ui`,
        method: 'GET',
      };
    },
  },
});

// 检验组详情
const DetailFormDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'qisProdlineReviewTmpId',
  fields: [
    {
      name: 'prodlineReviewTmpNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodlineReviewTmpNum`).d('产线审核模板编号'),
      disabled: true,
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('审核状态'),
      lookupCode: 'YP.QIS.STATUS',
      noCache: true,
    },
    {
      name: 'reviewType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('审核类型'),
      required: true,
      lookupCode: 'YP.QIS.REVIEW_TYPE',
    },
    {
      name: 'reviewStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewStage`).d('审核阶段'),
      required: true,
      lookupCode: 'YP.QIS.REVIEW_STAGE',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建日期'),
    },
    {
      name: 'createdRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdRealName`).d('创建人'),
    },
    {
      name: 'reviewRemarks',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewRemarks`).d('审批备注'),
    },
    {
      name: 'reviewByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewByRealName`).d('审批人'),
    },
    {
      name: 'reviewDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewDate`).d('审批时间'),
    },
  ],
});

// 检验组关联检验项
const DetailTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  forceValidate: true,
  paging: false,
  primaryKey: 'prodReviewTmpElementId',
  fields: [
    {
      name: 'elementNum',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.elementNum`).d('要素编号'),
    },
    {
      name: 'reviewDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewDimension`).d('审核维度'),
      required: true,
      lookupCode: 'YP.QIS.REVIEW_DIMENSION',
    },
    {
      name: 'reviewItem',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewItem`).d('审核项目'),
      required: true,
    },
    {
      name: 'reviewContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewContent`).d('评价要素'),
      required: true,
    },
    {
      name: 'deliveryName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.deliveryName`).d('交付物名称'),
      required: true,
    },
    {
      name: 'deliveryTemplate',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.deliveryTemplate`).d('交付物模板'),
      bucketName: 'qms',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentName`).d('责任部门'),
      required: true,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'responsibleDeptId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsibleDeptName',
      bind: 'departmentLov.unitName',
    },
  ],
});

const approveDS: () => DataSetProps = () => ({
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'reviewRemarks',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewRemarks`).d('驳回原因'),
      required: true,
    },
  ],
});

export { ListTableDS, DetailFormDS, DetailTableDS, approveDS };
