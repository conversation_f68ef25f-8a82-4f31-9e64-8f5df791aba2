import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const organizationId = getCurrentOrganizationId();

const myTodoDS: () => DataSetProps = () => {
  return {
    autoQuery: true,
    selection: false,
    fields: [
      {
        label: '单据编号',
        name: 'orderCode',
        type: FieldType.string,
      },
      {
        label: '单据名称',
        name: 'orderName',
        type: FieldType.string,
      },
      {
        label: '单据类型',
        name: 'orderTypeName',
        type: FieldType.string,
      },
      {
        label: '负责人',
        name: 'headerName',
        type: FieldType.string,
      },
      {
        label: '计划完成时间',
        name: 'scheduledFinishDate',
        type: FieldType.string,
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/table-cards/todo-order/my-todo`,
          method: 'GET',
          params,
        };
      },
    },
  };
};
const mySubmitDS: () => DataSetProps = () => {
  return {
    autoQuery: true,
    selection: false,
    fields: [
      {
        label: '单据编号',
        name: 'orderCode',
        type: FieldType.string,
      },
      {
        label: '单据名称',
        name: 'orderName',
        type: FieldType.string,
      },
      {
        label: '单据类型',
        name: 'orderTypeName',
        type: FieldType.string,
      },
      {
        label: '负责人',
        name: 'headerName',
        type: FieldType.string,
      },
      {
        label: '计划完成时间',
        name: 'scheduledFinishDate',
        type: FieldType.string,
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/table-cards/todo-order/my-submit`,
          method: 'GET',
          params,
        };
      },
    },
  };
};
const lastFinishDS: () => DataSetProps = () => {
  return {
    autoQuery: true,
    selection: false,
    fields: [
      {
        label: '单据编号',
        name: 'orderCode',
        type: FieldType.string,
      },
      {
        label: '单据名称',
        name: 'orderName',
        type: FieldType.string,
      },
      {
        label: '单据类型',
        name: 'orderTypeName',
        type: FieldType.string,
      },
      {
        label: '负责人',
        name: 'headerName',
        type: FieldType.string,
      },
      {
        label: '计划完成时间',
        name: 'scheduledFinishDate',
        type: FieldType.string,
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/table-cards/todo-order/last-finish`,
          method: 'GET',
          params,
        };
      },
    },
  };
};
export { myTodoDS, mySubmitDS, lastFinishDS };
