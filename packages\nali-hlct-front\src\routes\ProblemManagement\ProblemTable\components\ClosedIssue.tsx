import React, { useEffect, useMemo, useRef, useCallback } from 'react';
import { Table } from 'choerodon-ui/pro';
import { Row, Col } from 'choerodon-ui';
import { Content } from 'components/Page';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import './index.less';

const modelPrompt = 'problem.table';

const Underway = props => {
  let page = 0;
  let totalPages = 1;
  const { ds, closedIssueQueryInfo, closedIssueLoading, history } = props;

  const tableRef = useRef<any>(null);

  useEffect(() => {
    loadData(0);
  }, []);

  const loadData = page => {
    if (page > 0) {
      const data = ds.toData();
      closedIssueQueryInfo({
        params: {
          page,
          size: 10,
        },
        onSuccess: res => {
          if (res) {
            totalPages = res.totalPages;
            const dataList = data.concat(res.content);
            ds.loadData(dataList);
          }
        },
      });
    } else {
      closedIssueQueryInfo({
        params: {
          page,
          size: 10,
        },
        onSuccess: res => {
          if (res) {
            totalPages = res.totalPages;
            ds.loadData(res.content);
          }
        },
      });
    }
  };

  const handleScroll = e => {
    const { scrollTop, clientHeight, scrollHeight } = e.target;
    if (scrollTop > 0 && Math.ceil(scrollTop + clientHeight) >= scrollHeight) {
      if (page < totalPages) {
        page += 1;
        loadData(page);
      }
    }
  };

  useEffect(() => {
    if (tableRef.current) {
      tableRef.current?.element?.addEventListener('scroll', handleScroll, true);
    }
    return () => tableRef.current?.element?.removeEventListener('scroll', handleScroll, true);
  }, []);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'problemCode',
        width: 180,
        renderer: ({ record, value }) => (
          <a
            onClick={() => {
              history.push(
                `/hwms/problem-management/problem-management-platform/dist/${record?.get(
                  'problemId',
                )}`,
              );
            }}
          >
            {value}
          </a>
        ),
      },
      {
        name: 'problemTitle',
      },
      {
        name: 'proposePersonName',
      },
      {
        name: 'proposeDepartment',
      },
      {
        name: 'registerTime',
        width: 150,
      },
      {
        name: 'leadPersonName',
      },
      {
        name: 'responsiblePersonName',
      },
      {
        name: 'responsibleDepartment',
      },
      {
        name: 'problemCategory',
      },
      {
        name: 'severityLevel',
      },
      {
        name: 'score',
      },
    ];
  }, []);

  const handleJumpToList = (state = {}) => {
    history.push({
      pathname: `/hwms/problem-management/problem-management-platform/list`,
      state: {
        stateType: 'query',
        ...state,
      },
    });
  };

  return (
    <Content>
      <Row>
        <Col span={23}>
          <span className="title">{intl.get(`${modelPrompt}.closed.issue`).d('已关闭的问题')}</span>
        </Col>
        <Col span={1}>
          <a
            onClick={() =>
              handleJumpToList({
                problemStatus: 'CLOSED',
                onlyForMeFalg: 'Y',
              })
            }
            style={{ fontSize: '14px' }}
          >
            {intl.get(`${modelPrompt}.more`).d('更多')}
          </a>
        </Col>
      </Row>
      <Table
        dataSet={ds}
        columns={columns}
        ref={tableRef}
        virtual
        virtualCell
        style={{
          height: 'calc(100% - 70px)',
        }}
      />
      {closedIssueLoading ? (
        <p style={{ textAlign: 'center', padding: '0', margin: '0' }}>{intl.get(`${modelPrompt}.loading`).d('加载中...')}</p>
      ) : (
        ''
      )}
    </Content>
  );
};

export default Underway;
