/**
 * 采购订单管理-入口文件
 * @date 2023-1-3
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { DataSet, Table, Modal, Button } from 'choerodon-ui/pro';
import { <PERSON><PERSON>, Collapse, Spin } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import { AttributeDrawer } from '@components/tarzan-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import myInstance from '@utils/myAxios';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import notification from 'utils/notification';
import { BASIC, API_HOST } from '@utils/config';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import {TemplatePrintButton} from "../../../components/tarzan-ui";
import { headerTableDS, lineTableDS } from './stores/OrderListDS';
import { headDS, tableDS } from './stores/MaterialBatchDS';
import CreateShippingDrawer from './CreateShippingDrawer';
import CreateMaterial from './CreateMaterialDrawer';
import AssemblyDrawer from './AssemblyDrawer';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.purchase.order';
const tenantId = getCurrentOrganizationId();

// 用以DS传参
const dataSets = {
  poHeaderId: 0,
};

let modalMaterial;
let modalCreateShipping;
let modalAssembly;
let headerResult;

const Order = props => {

  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
    custConfig,
  } = props;

  const headDs = useMemo(() => new DataSet(headDS()), []); // 物料批头ds
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 物料批行ds
  const [deliverheaderDetail, setDeliverheaderDetail] = useState({});
  const [deliverheaderDetailDrawer, setDeliverheaderDetailDrawer] = useState({});
  const [deliverListSupplierId, setDeliverListSupplierId] = useState(undefined);
  const [deliverListSiteId, setDeliverListSiteId] = useState(undefined);
  const [deliverListSupplierSiteIds, setDeliverListSupplierSiteIds] = useState([]);
  // 判断头搜索条件切换
  const [supplierId, setSupplierId] = useState();
  // 选中头详情(单选)
  const [headerDetail, setHeaderDetail] = useState({});
  // 选中行数量
  const [selectedLength, setSelectedLength] = useState(0);
  // 送货单ref
  const shippingDrawerRef = useRef();
  // 页面权限
  // eslint-disable-next-line no-unused-vars
  const [createInstructionDocLineId, setCreateInstructionDocLineId] = useState(); // TODO 可能有bug
  const [loading, setLoading] = useState(false);
  // 行选中的数据，用来控制创建物料批新建相关
  const [selectedItem, setSelectedItems] = useState([]);
  // 创建多行物料批里的表格Ds
  const [tableDsArr, setTableDsArr] = useState([]);

  useEffect(() => {
    if (tableDs) {
      tableDs.addEventListener('batchSelect', handleSelect);
      tableDs.addEventListener('batchUnSelect', handleSelect);
    }
    if (lineTableDs) {
      lineTableDs.addEventListener('batchSelect', handleSelectLineTable);
      lineTableDs.addEventListener('batchUnSelect', handleSelectLineTable);
    }
    return () => {
      if (tableDs) {
        tableDs.removeEventListener('batchSelect', handleSelect);
        tableDs.removeEventListener('batchUnSelect', handleSelect);
      }
      if (lineTableDs) {
        lineTableDs.removeEventListener('batchSelect', handleSelectLineTable);
        lineTableDs.removeEventListener('batchUnSelect', handleSelectLineTable);
      }
    };
  }, []);

  // 创建多行物料批弹框内 物料批明细的选中事件监听
  useEffect(() => {
    for (let i = 0; i < selectedItem.length; i++) {
      tableDsArr[i].addEventListener('batchSelect', handleSelectLineTableArr);
      tableDsArr[i].addEventListener('batchUnSelect', handleSelectLineTableArr);
    }

    return () => {
      for (let i = 0; i < selectedItem.length; i++) {
        tableDsArr[i].removeEventListener('batchSelect', handleSelectLineTableArr);
        tableDsArr[i].removeEventListener('batchUnSelect', handleSelectLineTableArr);
      }
    };
  }, [tableDsArr]);

  // 创建多行物料批弹框内 物料批明细的选中事件，用来手机选中的物料批id(materialLotId)
  const handleSelectLineTableArr = () => {
    let selectedList = [];
    for (let i = 0; i < selectedItem.length; i++) {
      const selected = tableDsArr[i].selected.map(item => {
        return item.get('materialLotId');
      });
      selectedList = [...selectedList, ...selected];
    }
    updateModalTitle(selectedList);
  };

  // 行的选中事件，用来获取选中的instructionDocLineId
  const handleSelectLineTable = () => {
    let noMaterialLot = false;
    const selected = lineTableDs.selected.map(item => {
      // 非实物不可操作
      if (item.get('identifyType') !== 'MATERIAL_LOT') {
        noMaterialLot = true;
      }
      return item.get('poLineId');
    });
    if (noMaterialLot) {
      setSelectedItems([]);
    } else {
      setSelectedItems(selected);
    }
  };

  const handleSelect = () => {
    const selected = tableDs.selected.map(item => {
      return item.get('materialLotId');
    });
    updateModalTitle(selected, createInstructionDocLineId);
  };

  const updateModalTitle = (selected, selectedId) => {
    modalMaterial.update({
      title: createModalTitle(selected, selectedId),
    });
  };
  const createModalTitle = (selected = []) => {
    return (
      <div>
        <span style={{ fontSize: '14px' }}>
          {intl.get(`${modelPrompt}.create.materialBatch`).d('创建物料批')}
        </span>
        <div
          style={{
            float: 'right',
            display: 'flex',
            flexDirection: 'row-reverse',
            alignItems: 'center',
            marginRight: '0.3rem',
          }}
        >
          <PermissionButton
            style={{
              marginLeft: '0.1rem',
              marginRight: '0.2rem',
            }}
            icon="save"
            type="c7n-pro"
            color={ButtonColor.primary}
            onClick={() => handleEstablish()}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.create.establish`).d('创建')}
          </PermissionButton>
          {/* <FRPrintButton
            kid="MATERIAL_LOT_PRINT" // 使用打印组件的功能的唯一标识，业务提供
            queryParams={selected} // 查询数据
            disabled={selected.length === 0}
            printObjectType="INSTRUCTION_DOC"
          /> */}
          <TemplatePrintButton
            disabled={!selected.length}
            printButtonCode='PURCHASE_ORDER_MANAGEMENT_IN'
            printParams={{ materialLotId: selected.join(',') }}
          />
        </div>
      </div>
    );
  };

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // DS事件监听
  useEffect(() => {
    if (props?.location?.query?.poNumber && props?.location?.query?.poNumber !== '') {
      const poNumber = props?.location?.query?.poNumber;
      headerTableDs.queryDataSet.loadData([
        {
          poNumber,
        },
      ]);
      headerTableDs.query();
    }
  }, [props?.location?.query?.poNumber]);

  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    if (headerTableDs?.selected?.length > 0) {
      setHeaderDetail(headerTableDs?.selected[0]?.toData() || {});
    }
    if (lineTableDs?.selected?.length > 0) {
      setSelectedLength(lineTableDs?.selected?.length || 0);
    }
  }, []);

  // 生成行列表DS查询项
  const getQueryFields = () => {
    const queryFields = [
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.material`).d('物料'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
      },
      {
        name: 'materialTypeName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialTypeName`).d('物料类别描述'),
      },
      {
        name: 'demandDateFrom',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.demandDateFrom`).d('需要日期从'),
        max: 'demandDateTo',
      },
      {
        name: 'demandDateTo',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.demandDateTo`).d('需要日期至'),
        min: 'demandDateFrom',
      },
      {
        name: 'site',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.site`).d('站点'),
        lovCode: 'MT.MODEL.PURCHASE.SITE',
        noCache: true,
        ignore: 'always',
        textField: 'siteCode',
        valueField: 'siteId',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId,
              poHeaderId: dataSets.poHeaderId,
            };
          },
        },
      },
    ];
    const ds = new DataSet({
      fields: queryFields,
    });
    lineTableDs.queryDataSet = ds;
  };

  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.queryDataSet.addEventListener
        : headerTableDs.queryDataSet.removeEventListener;
      handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'select', handleHeaderTableDsSelect);
      handler.call(headerTableDs, 'unSelect', handleHeaderTableDsUnSelect);
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
    }
    // 行监听
    if (lineTableDs) {
      const handler = flag ? lineTableDs.addEventListener : lineTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(lineTableDs, 'load', resetLineDetail);
      handler.call(lineTableDs, 'batchSelect', handleLineTableChange);
      handler.call(lineTableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (data.supplierId !== supplierId) {
      setSupplierId(data.supplierId);
      record.set('supplierSite', {});
    }
  };

  // 头单选按钮选中
  const handleHeaderTableDsSelect = ({ record }) => {
    setDeliverheaderDetail(record?.toData());
    headerTableDs.current = record;
    queryLineTable(record?.toData()?.poHeaderId);
    setHeaderDetail(record?.toData() || {});
  };
  // 头单选按钮撤销
  const handleHeaderTableDsUnSelect = () => {
    setHeaderDetail({});
    setDeliverheaderDetail({});
    queryLineTable();
  };
  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 列表刷新清除头单选状态
    setHeaderDetail({});
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable();
    }
  };

  // 行选中事件, 更新选中行数量
  const resetLineDetail = ({ dataSet }) => {
    dataSet.forEach(lineRecored => {
      if (deliverListSupplierId === undefined) {
        // eslint-disable-next-line
        lineRecored.selectable =
          lineRecored?.data?.poHeaderApprovedFlag !== 'N' &&
          lineRecored?.data?.poHeaderDeleteFlag !== 'Y' &&
          lineRecored?.data?.poHeaderClosedFlag !== 'Y' &&
          lineRecored?.data?.returnFlag !== 'Y' &&
          lineRecored?.data?.deleteFlag !== 'Y' &&
          lineRecored?.data?.completeFlag !== 'Y';
      } else {
        // eslint-disable-next-line
        lineRecored.selectable =
          deliverListSupplierId === lineRecored?.data?.supplierId &&
          deliverListSiteId === lineRecored?.data?.siteId &&
          lineRecored?.data?.poHeaderApprovedFlag !== 'N' &&
          lineRecored?.data?.poHeaderDeleteFlag !== 'Y' &&
          lineRecored?.data?.poHeaderClosedFlag !== 'Y' &&
          lineRecored?.data?.returnFlag !== 'Y' &&
          lineRecored?.data?.deleteFlag !== 'Y' &&
          lineRecored?.data?.completeFlag !== 'Y';
      }
    });
  };
  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    if (dataSet?.selected?.length > 0) {
      const firstSupplierId = dataSet?.selected[0]?.data?.supplierId;
      const firstSiteId = dataSet?.selected[0]?.data?.siteId;
      setDeliverheaderDetailDrawer(
        firstSupplierId === deliverheaderDetail.supplierId ? deliverheaderDetail : {},
      );
      setSelectedLength(dataSet?.selected?.length || 0);
      setDeliverListSupplierId(firstSupplierId);
      setDeliverListSiteId(firstSiteId);
      setDeliverListSupplierSiteIds([
        ...deliverListSupplierSiteIds,
        deliverheaderDetail.supplierSiteId,
      ]);
      dataSet.forEach(lineRecored => {
        // eslint-disable-next-line
        lineRecored.selectable =
          firstSupplierId === lineRecored?.data?.supplierId &&
          firstSiteId === lineRecored?.data?.siteId &&
          lineRecored?.data?.poHeaderApprovedFlag !== 'N' &&
          lineRecored?.data?.poHeaderDeleteFlag !== 'Y' &&
          lineRecored?.data?.poHeaderClosedFlag !== 'Y' &&
          lineRecored?.data?.returnFlag !== 'Y' &&
          lineRecored?.data?.deleteFlag !== 'Y';
      });
    } else {
      setDeliverListSupplierId(undefined);
      setDeliverListSiteId(undefined);
      setDeliverListSupplierSiteIds([]);
      setSelectedLength(0);
      setDeliverheaderDetailDrawer({});
      dataSet.forEach(lineRecored => {
        // eslint-disable-next-line
        lineRecored.selectable =
          lineRecored?.data?.poHeaderApprovedFlag !== 'N' &&
          lineRecored?.data?.poHeaderDeleteFlag !== 'Y' &&
          lineRecored?.data?.poHeaderClosedFlag !== 'Y' &&
          lineRecored?.data?.returnFlag !== 'Y' &&
          lineRecored?.data?.deleteFlag !== 'Y';
      });
    }
  };

  // 行列表数据查询
  const queryLineTable = poHeaderId => {
    dataSets.poHeaderId = poHeaderId || 0;
    lineTableDs.setQueryParameter('poHeaderId', poHeaderId);
    lineTableDs.query();
  };

  // 创建物料批
  const handleCreateMaterial = async () => {
    const arr = [];
    // eslint-disable-next-line array-callback-return
    selectedItem.map(() => {
      arr.push(new DataSet(tableDS()));
    });
    setTableDsArr([...arr]);
    headDs.setQueryParameter('poLineIds', selectedItem.join(','));
    setLoading(true);
    const res = await headDs.query();
    setLoading(false);
    if (res.success) {
      headerResult = res.rows;
      modalMaterial = Modal.open({
        title: createModalTitle([]),
        className: 'hmes-style-modal',
        maskClosable: true,
        destroyOnClose: true,
        drawer: true,
        closable: true,
        style: {
          width: 1080,
        },
        onClose: () => {
          tableDs.batchUnSelect(tableDs.selected);
        },
        children: (
          <CreateMaterial
            headDs={headDs}
            tableDs={tableDs}
            batchList={[]}
            resultData={res.rows}
            tableDsArr={arr}
            selectedItem={selectedItem}
            customizeTable={customizeTable}
          />
        ),
        footer: (
          <Button onClick={() => modalMaterial.close()}>
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        ),
      });
    } else {
      notification.error({
        message: res?.message,
      });
    }
  };

  // 物料批创建
  const handleEstablish = async () => {
    const validate = await headDs.validate();
    if (validate) {
      const data = {
        organizationId: getCurrentOrganizationId(),
        poHeaderId: headDs.toData()[0].poHeaderId,
        poNumber: headDs.toData()[0].poNumber,
        poLineList: headDs.toData(),
      };
      const url = `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-po-line/material-lot/create/ui`;
      return myInstance.post(url, data).then(async res => {
        if (res?.data?.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
          });
          const batchLists = res?.data?.rows;
          const arr = [];
          // eslint-disable-next-line array-callback-return
          selectedItem.map(() => {
            arr.push(new DataSet(tableDS()));
          });
          setTableDsArr([...arr]);
          modalMaterial.update({
            children: (
              <CreateMaterial
                headDs={headDs}
                tableDs={tableDs}
                batchList={batchLists}
                resultData={headerResult}
                tableDsArr={arr}
                selectedItem={selectedItem}
                customizeTable={customizeTable}
              />
            ),
          });
          for (let i = 0; i < selectedItem.length; i++) {
            arr[i].setQueryParameter('poLineId', selectedItem[i]);
            arr[i].query();
          }
          headDs.setQueryParameter('poLineIds', selectedItem.join(','));
          setLoading(true);
          await headDs.query();
          setLoading(false);
        } else {
          notification.error({
            message: res?.data?.message,
          });
        }
      });
    }
  };

  const shippingHandleSubmit = () => {
    return shippingDrawerRef.current.handleSubmit();
  };

  // 创建送货单
  const handleCreateCreateShipping = async () => {
    let deliverList;
    const supplierSiteId = [];
    const supplierSiteCode = [];
    if (lineTableDs?.selected?.length > 0) {
      deliverList = lineTableDs.selected.map(item => {
        if (supplierSiteId.indexOf(item.data.supplierSiteId) === -1) {
          supplierSiteId.push(item.data.supplierSiteId);
          supplierSiteCode.push(item.data.supplierSiteCode);
        }
        return item.data;
      });
    }

    modalCreateShipping = Modal.open({
      title: intl.get(`${modelPrompt}.createDelivery`).d('创建送货单'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <CreateShippingDrawer
          ref={shippingDrawerRef}
          deliverList={deliverList}
          deliverheaderDetailDrawer={{
            ...deliverheaderDetailDrawer,
            supplierSiteId: supplierSiteId.length === 1 ? supplierSiteId[0] : undefined,
            supplierSiteCode: supplierSiteCode.length === 1 ? supplierSiteCode[0] : undefined,
          }}
          handleSuccess={handleSuccess}
          customizeTable={customizeTable}
        />
      ),
      footer: (
        <>
          <Button onClick={() => modalCreateShipping.close()}>
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
          <PermissionButton
            key="create"
            type="c7n-pro"
            color={ButtonColor.primary}
            onClick={shippingHandleSubmit}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.createDelivery`).d('创建送货单')}
          </PermissionButton>
        </>
      ),
    });
  };

  // 组件信息
  const handleCreateAssembly = async record => {
    const assemblyDetail = record?.toData() || {};
    modalAssembly = Modal.open({
      title: intl.get(`${modelPrompt}.assemblyInformation`).d('组件信息'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: <AssemblyDrawer record={assemblyDetail} customizeTable={customizeTable} />,
      footer: (
        <Button onClick={() => modalAssembly.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 操作列渲染
  const optionRender = record => (
    <span className="action-link">
      <a
        disabled={!(record.data.poComponentExists !== 'N')}
        onClick={() => {
          handleCreateAssembly(record);
        }}
      >
        {intl.get(`${modelPrompt}.assemblyInformation`).d('组件信息')}
      </a>
      <AttributeDrawer
        disabled={!record?.data?.poLineId}
        type="text"
        className="org.tarzan.mes.domain.entity.MtPoLine"
        kid={record?.data?.poLineId}
        serverCode={BASIC.HMES_BASIC}
        canEdit={false}
        custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_BUTTON.LINE`}
        custConfig={custConfig}
      />
    </span>
  );

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'poNumber',
      width: 130,
    },
    {
      name: 'supplierName',
      width: 240,
    },
    {
      name: 'supplierSiteName',
      width: 180,
    },
    {
      name: 'buyerCode',
      width: 100,
    },
    {
      name: 'approvedFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'deleteFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    /* {
      name: 'returnFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    }, */
    {
      name: 'closedFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'poCategory',
      width: 120,
    },
    {
      name: 'poOrderType',
      width: 120,
    },
    {
      name: 'currencyCode',
      width: 80,
    },
    {
      name: 'transferSiteCode',
      width: 120,
    },
    {
      name: 'receivingAddress',
      width: 160,
    },
    {
      name: 'poCreateDate',
      align: 'center',
      width: 160,
    },
    {
      name: 'description',
      width: 160,
    },
  ];

  // 行信息表配置
  let lineTableColumns = [];
  const lineTableColumnsFront = [
    {
      name: 'revisionCode',
      dataIndex: 'revisionCode',
      width: 80,
      title: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'materialName',
      dataIndex: 'materialName',
      width: 150,
      title: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialTypeName',
      dataIndex: 'materialTypeName',
      width: 160,
      title: intl.get(`${modelPrompt}.materialTypeName`).d('物料类别描述'),
    },
    {
      name: 'uomCode',
      dataIndex: 'uomCode',
      width: 60,
      title: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'quantityOrdered',
      dataIndex: 'quantityOrdered',
      align: 'right',
      width: 70,
      title: intl.get(`${modelPrompt}.quantityOrdered`).d('数量'),
    },
    {
      name: 'processedOrdered',
      dataIndex: 'processedOrdered',
      align: 'right',
      width: 100,
      title: intl.get(`${modelPrompt}.deliveredQuantity`).d('已制单数量'),
    },
  ];

  const lineTableColumnsMiddle = [
    {
      name: 'actualQuantity',
      dataIndex: 'actualQuantity',
      width: 140,
      align: 'right',
      title: intl.get(`${modelPrompt}.actualQuantity`).d('已创建物料批数量'),
    },
  ];
  const lineTableColumnsAfter = [
    {
      name: 'demandDate',
      dataIndex: 'demandDate',
      align: 'center',
      width: 140,
      title: intl.get(`${modelPrompt}.demandDate`).d('需要日期'),
    },
    {
      name: 'siteCode',
      dataIndex: 'siteCode',
      width: 160,
      title: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'locatorCode',
      dataIndex: 'locatorCode',
      width: 100,
      title: intl.get(`${modelPrompt}.receiveLocator`).d('接收库位'),
    },
    {
      name: 'quantityReceived',
      dataIndex: 'quantityReceived',
      align: 'right',
      width: 100,
      title: intl.get(`${modelPrompt}.quantityReceived`).d('接收数量'),
    },
    {
      name: 'quantityDelivered',
      dataIndex: 'quantityDelivered',
      width: 130,
      align: 'right',
      title: intl.get(`${modelPrompt}.quantityDelivered`).d('已入库数量'),
    },
    {
      name: 'completeFlag',
      dataIndex: 'completeFlag',
      width: 80,
      title: intl.get(`${modelPrompt}.completeFlag`).d('交货完成标识'),
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'returnFlag',
      dataIndex: 'returnFlag',
      width: 80,
      title: intl.get(`${modelPrompt}.returnFlag`).d('退货行标识'),
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'deleteFlag',
      dataIndex: 'deleteFlag',
      width: 80,
      title: intl.get(`${modelPrompt}.deleteFlag`).d('删除标识'),
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'consignedFlag',
      dataIndex: 'consignedFlag',
      width: 80,
      title: intl.get(`${modelPrompt}.consignedFlag`).d('寄售标识'),
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'releaseNum',
      dataIndex: 'releaseNum',
      width: 100,
      title: intl.get(`${modelPrompt}.releaseNum`).d('一揽子发放号'),
    },
    {
      name: 'shipmentNum',
      dataIndex: 'shipmentNum',
      width: 80,
      title: intl.get(`${modelPrompt}.shipmentNum`).d('发运行号'),
    },
    {
      name: 'expirationDate',
      dataIndex: 'expirationDate',
      align: 'center',
      width: 140,
      title: intl.get(`${modelPrompt}.expirationDate`).d('超期日'),
    },
    {
      name: 'poNumber',
      dataIndex: 'poNumber',
      width: 130,
      title: intl.get(`${modelPrompt}.poNumber`).d('采购订单'),
    },
    {
      name: 'soNum',
      dataIndex: 'soNum',
      width: 130,
      title: intl.get(`${modelPrompt}.soNum`).d('销售订单号'),
    },
    {
      name: 'soLineNum',
      dataIndex: 'soLineNum',
      width: 130,
      title: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
    },
    {
      name: 'lineDescription',
      dataIndex: 'lineDescription',
      width: 130,
      title: intl.get(`${modelPrompt}.lineDescription`).d('行说明'),
    },
  ];

  if (
    props.permissionDetail?.approve === false &&
    props.permissionDetail?.controllerType === 'hidden'
  ) {
    lineTableColumns = [...lineTableColumnsFront, ...lineTableColumnsAfter];
  } else {
    lineTableColumns = [
      ...lineTableColumnsFront,
      ...lineTableColumnsMiddle,
      ...lineTableColumnsAfter,
    ];
  }

  // 固定不动列
  const otherColumn = [
    {
      name: 'lineNum',
      dataIndex: 'lineNum',
      width: 80,
      title: intl.get(`${modelPrompt}.lineNum`).d('行号'),
      fixed: 'left',
      lock: 'left',
    },
    {
      name: 'identifyType',
      dataIndex: 'identifyType',
      width: 120,
      title: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
      fixed: 'left',
      lock: 'left',
      // renderer: ({ value }) => {
      //   if (value === 'LOT' || value === 'MAT') {
      //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
      //   }
      //   if (value === 'MATERIAL_LOT') {
      //     return intl.get('tarzan.common.physicalManage').d('实物管理');
      //   }
      //   return '';
      // },
    },
    {
      name: 'lineType',
      dataIndex: 'lineType',
      width: 110,
      title: intl.get(`${modelPrompt}.lineType`).d('行类型'),
      fixed: 'left',
      lock: 'left',
    },
    {
      name: 'materialCode',
      dataIndex: 'materialCode',
      width: 130,
      title: intl.get(`${modelPrompt}.material`).d('物料'),
      fixed: 'left',
      lock: 'left',
    },
  ];

  // 航信息操作列配置
  const optionColumn = {
    name: 'poLineId',
    fixed: 'right',
    lock: 'right',
    width: 200,
    align: 'center',
    title: intl.get(`${modelPrompt}.option`).d('操作'),
    renderer: ({ record }) => optionRender(record),
  };

  lineTableColumns = [...otherColumn, ...lineTableColumns, optionColumn];

  const headerRowClick = record => {
    getQueryFields();
    headerTableDs.select(record);
    queryLineTable(record?.toData()?.poHeaderId);
  };

  // modal 部分

  // 创建送货单成功回调
  const handleSuccess = id => {
    lineTableDs.batchUnSelect(lineTableDs.selected);
    lineTableDs.clearCachedSelected();
    setDeliverheaderDetailDrawer({});
    setDeliverListSupplierId(undefined);
    setDeliverListSiteId(undefined);
    setDeliverListSupplierSiteIds([]);
    setSelectedLength(0);
    queryLineTable(headerTableDs?.current?.data?.poHeaderId);
    modalCreateShipping.close();
    props.history.push(`/hmes/purchase/delivery-management-standard/detail/${id}`);
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/MT.MES.PO',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const extraButton = (
    <Button onClick={handleCreateMaterial} disabled={!selectedItem.length} color="primary">
      {intl.get(`${modelPrompt}.createMaterialBatch`).d('创建物料批')}
    </Button>
  );

  const onFieldEnterDown = () => {
    headerTableDs.query(props.headerTableDs.currentPage);
  }

  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        <Header title={intl.get(`${modelPrompt}.title.purchaseOrderManagement`).d('采购订单管理')}>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={handleCreateCreateShipping}
            disabled={selectedLength === 0}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.createDelivery`).d('创建送货单')}
          </PermissionButton>
          <PermissionButton
            type="c7n-pro"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            icon="file_upload"
            onClick={goImport}
          >
            {intl.get('tarzan.common.button.import').d('导入')}
          </PermissionButton>
          <AttributeDrawer
            className="org.tarzan.mes.domain.entity.MtPoHeader"
            kid={headerDetail?.poHeaderId}
            serverCode={BASIC.HMES_BASIC}
            disabled={!headerDetail?.poHeaderId}
            canEdit={false}
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_BUTTON.HEAD`}
            custConfig={custConfig}
          />
        </Header>
        <Content className={styles['order-content']}>
          {customizeTable(
            {
              filterCode: `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.QUERY`,
              code: `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.LIST`,
            },
            <Table
              searchCode="cgddgl1"
              customizedCode="cgddgl1"
              queryBar="filterBar"
              queryBarProps={{
                fuzzyQuery: false,
                autoQuery: false,
                onFieldEnterDown,
              }}
              dataSet={headerTableDs}
              columns={headerTableColumns}
              highLightRow={false}
              queryFieldsLimit={7}
              onRow={({ record }) => {
                return {
                  onClick: () => {
                    headerRowClick(record);
                  },
                };
              }}
            />,
          )}
          <Collapse bordered={false} collapsible="icon" defaultActiveKey={['basicInfo']}>
            <Panel
              header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
              key="basicInfo"
              extra={extraButton}
              dataSet={lineTableDs}
            >
              {lineTableDs && (
                customizeTable(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.LINE`,
                  },
                  <Table
                    customizedCode="cgddgl2"
                    className={styles['expand-table']}
                    queryBar="bar"
                    dataSet={lineTableDs}
                    highLightRow={false}
                    columns={lineTableColumns}
                  />,
                )
              )}
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.hmes.purchase.order', 'tarzan.common', 'hzero.c7nProUI'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.LIST`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.LINE`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_BUTTON.HEAD`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_BUTTON.LINE`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_MATERIAL_LOT.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_COMPONENT.QUERY`],
  }),
)(Order);
