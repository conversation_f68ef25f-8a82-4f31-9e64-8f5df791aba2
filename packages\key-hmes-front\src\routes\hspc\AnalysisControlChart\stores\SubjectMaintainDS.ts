/**
 * @Description: 分析控制图维护维护列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2021-11-15 14:41:53
 * @LastEditTime: 2022-11-09 14:39:19
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hspc.analysisControlChartMaintain';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'analysisCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisCode`).d('分析控制图编码'),
    },
    {
      name: 'analysisDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisDesc`).d('分析控制图描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
    {
      name: 'subjectDataTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subjectDataTitle`).d('样本数据标题'),
    },
    {
      name: 'chartType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chartType`).d('控制图类型'),
      lookupCode: 'MT.SPC.CHART_TYPE',
    },
  ],
  fields: [
    {
      name: 'analysisCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisCode`).d('分析控制图编码'),
      disabled: true,
    },
    {
      name: 'analysisDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisDesc`).d('分析控制图描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'isStable',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.isStable`).d('是否稳态'),
    },
    {
      name: 'subjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subjectCode`).d('课题编码'),
    },
    {
      name: 'subjectDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subjectDesc`).d('课题描述'),
    },
    {
      name: 'subjectDataTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subjectDataTitle`).d('样本数据标题'),
    },
    {
      name: 'chartTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chartType`).d('控制图类型'),
    },
    {
      name: 'analysisDimensionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysDimension`).d('分析维度'),
    },
    {
      name: 'subgroupSize',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subgroupSize`).d('子组大小'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis/list/ui`,
        data,
        method: 'GET',
      };
    },
    submit: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis/update/ui`,
        method: 'POST',
        data: data[0],
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return getResponse(parsedData);
          }
        },
      };
    },
    destroy: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/analysis/delete/ui?analysisId=${data[0].analysisId}`,
        method: 'POST',
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return getResponse(parsedData);
          }
        },
      };
    },
  },
});

export { tableDS };
