/**
 * @Description: C7N 表单项多值组件
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 15:05:44
 * @LastEditTime: 2021-08-16 16:12:11
 * @LastEditors: <<EMAIL>>
 * import { C7nFormItemSort } from '@components/tarzan-ui';
 * name 为需要展示label项的值 itemWidth为每项占据的宽度 children 项若需要使用 链接样式, 配置itemType="link"
 * 需要注意 配置了链接样式
 * <C7nFormItemSort name="workOrderNum" itemWidth={['70%', '15%', '15%']}>
 *   <TextField
 *     name="workOrderNum"
 *   />
 *   <Lov
 *     name="site"
 *   />
 *   <Icon type="link2" itemType="link" />
 * </C7nFormItemSort>
 */

import React from 'react';
import { TextField } from 'choerodon-ui/pro';
import styles from './index.modules.less';

const C7nFormItemSort = props => {
  const { children, itemWidth } = props;
  return (
    <div className={styles['c7n-sort-wrapper']}>
      {children.map((item, index) => {
        if (item && item.props && item.props.itemType === 'link') {
          return (
            <div
              className="c7n-sort-item c7n-sort-item-border"
              style={{ width: itemWidth && itemWidth[index] ? itemWidth[index] : 'auto' }}
              key={item.name}
            >
              <TextField name="null" disabled={item.props.iconDisabled} />
              <div className="border-box" />
              <div className="link-box">{item}</div>
            </div>
          );
        } if (item && item.props && item.props.itemType === 'item-slot') {
          return item;
        } 
        return (
          <div
            className="c7n-sort-item"
            style={{ width: itemWidth && itemWidth[index] ? itemWidth[index] : 'auto' }}
            key={item.name}
          >
            {item}
          </div>
        );
        
      })}
    </div>
  );
};

export default C7nFormItemSort;
