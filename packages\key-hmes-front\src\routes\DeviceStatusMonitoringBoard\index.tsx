import React, { useEffect, useState, useMemo, useRef } from 'react';
import moment from 'moment';
import { Spin } from 'choerodon-ui';
import { DataSet, Lov } from 'choerodon-ui/pro';
import { Content } from 'components/Page';
import { FullScreenContainer } from '@jiaminghi/data-view-react';
import request from 'utils/request';
import intl from 'utils/intl';
import notification from "utils/notification";
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import DeviceStatusDistribution from './components/DeviceStatusDistribution';
import { TopFilterDS } from './Stores';
import styles from './index.module.less';


const modelPrompt = 'tarzan.wms.DeviceStatusMonitoringBoard';
const tenantId = getCurrentOrganizationId();
// 设备状态Info
const deviceStatusUrl = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-board/status-monitor/query`;
// oee和基本信息
const oeeBaseUrl = `${BASIC.API_PREFIX}/v1/${tenantId}/asset-board/status-monitor/detail`;


const CurrentTime = () => {
  const [nowDate, setNowDate] = useState(moment(new Date()).format('YYYY-MM-DD'));
  const [nowWeek, setNowWeek] = useState('');
  const [nowTime, setNowTime] = useState(moment(new Date()).format('HH:mm:ss'));

  const weekDay = () => {
    const dateList = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    setNowWeek(dateList[new Date().getDay()]);
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setNowDate(moment(new Date()).format('YYYY-MM-DD'));
      weekDay();
      setNowTime(moment(new Date()).format('HH:mm:ss'));
    }, 1000)

    return () => {  // 每次卸载都执行此函数，清楚定时器
      clearTimeout(timer)
    }
  }, []);
  return <span className={styles['dashboard-title-right-two-time-text']}> {nowDate}&nbsp;&nbsp;{nowWeek}&nbsp;&nbsp;{nowTime} </span>;
};

const Main = ({ isFullScreen }) => {

  const [initLoading, setInitLoading] = useState<boolean>(false);
  const [timers, setTimers] = useState<any>(null);
  // 设备位置
  // const [equipmentLocationId, setEquipmentLocationId] = useState<any>(null);

  const topFilterDs = useMemo(() => new DataSet(TopFilterDS()), []);
  // 设备状态list
  const [deviceStatusList, setDeviceStatusList] = useState<any>([]);
  const [gridTemplateRows, setGridTemplateRows] = useState<any>(5);
  // 设备状态分布饼图
  const [deviceStatusDistributionInfo, setDeviceStatusDistributionInfo] = useState<any>({});
  // 设备oee信息
  const [oeeInfo, setOeeInfo] = useState<any>(null);
  // 基本信息
  const [baseInfo, setBaseInfo] = useState<any>(null);

  const scrollContainerRef = useRef<any>(null);

  const getTimers = async () => {
    const url = `/hpfm/v1/${tenantId}/lovs/value/batch?QMS.MANAGEMENT_FREQUENCY=QMS.MANAGEMENT_FREQUENCY`
    const result = await request(url, {
      method: 'GET',
    });
    const data = result['QMS.MANAGEMENT_FREQUENCY'].filter(item => item.value === 'INCOME')
    if (data.length > 0) {
      setTimers(Number(data[0].meaning))
    }
  }

  useEffect(() => {
    getTimers();
    topFilterDs.current?.set("equipmentLocationLov", {});
    topFilterDs.current?.set("equipmentLocationId", null);
    getDeviceStatusList();
  }, []);

  useEffect(() => {
    let time
    if (timers) {
      time = setInterval(() => {
        getDeviceStatusList();
      }, timers * 60000)
    }
    return () => {
      clearInterval(time)
    }
  }, [timers])

  // 每五秒向下自动滚动一行
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (scrollContainerRef.current) {
        const currentScrollTop = scrollContainerRef.current.scrollTop;
        const maxHeight = scrollContainerRef.current.scrollHeight - scrollContainerRef.current.clientHeight;
        // 滚动到下一格
        if (currentScrollTop < maxHeight) {
          scrollContainerRef.current.scrollTop += scrollContainerRef.current.clientHeight / 5;
        }
      }
    }, 5000); // 每 5 秒滚动一次
  
    // 清理定时器
    return () => clearInterval(intervalId);
  }, []);

  const handleChangeEquipmentLocationLov = (value) => {
    if (value?.assetLocationId) {
      // setEquipmentLocationId(value.equipmentLocationId);
      getDeviceStatusList();
    }
  };

  /**
   * 不合格信息滚动播报
   */
  const getDeviceStatusList = async () => {
    setInitLoading(true);
    const result = await request(deviceStatusUrl, {
      method: 'GET',
      query: { assetLocationId: topFilterDs.current?.get('equipmentLocationId') },

    });
    if (result && !result.failed) {
      const { statusList, ...distributionInfo } = result;
      const rowLineNum = Math.ceil(Number(statusList?.length) / 6);
      setGridTemplateRows(rowLineNum);
      setDeviceStatusList(statusList);
      setDeviceStatusDistributionInfo(distributionInfo);
    } else {
      notification.warning({ description: result.message });
    }
    setInitLoading(false)
  };

  // 点击设备查询oee和基本信息
  const getOeeBaseInfo = async (item: any) => {
    setInitLoading(true);
    const result = await request(oeeBaseUrl, {
      method: 'GET',
      query: { assetId: item.assetId },

    });
    if (result && !result.failed) {
      const oeeInfo = {
        oee: result.oee,
        timeActivationRateStr: result.timeActivationRateStr,
        performanceEfficiencyStr: result.performanceEfficiencyStr,
        qualityQualificationRateStr: result.qualityQualificationRateStr,
      };
      const baseInfo = {
        equFieldIdentification: result.equFieldIdentification,
        personCharge: result.personCharge,
        installationPosition: result.installationPosition,
      };
      setOeeInfo(oeeInfo);
      setBaseInfo(baseInfo);
    } else {
      notification.warning({ description: result.message });
    }
    setInitLoading(false)
  };

  const assetStatusListRender = (item) => {
    switch (item.assetStatus) {
      // 运行 故障 待机 停机
      case 'OPERATING':
        return (
          <div className={styles['class-main-item-status']}>
            <span className={styles['class-main-item-status-badge']} style={{ color: "#adde8b" }} />
            <span className={styles['class-main-item-status-meaning']}
              style={{ color: "#adde8b" }}>{item.assetStatusMeaning}</span>
          </div>
        );
      case 'FAULT':
        return (
          <div className={styles['class-main-item-status']}>
            <span className={styles['class-main-item-status-badge']} style={{ color: "red" }} />
            <span className={styles['class-main-item-status-meaning']}
              style={{ color: "red" }}>{item.assetStatusMeaning}</span>
          </div>
        );
      case 'PAUSE':
        return (
          <div className={styles['class-main-item-status']}>
            <span className={styles['class-main-item-status-badge']} style={{ color: "#92d1f0" }} />
            <span className={styles['class-main-item-status-meaning']}
              style={{ color: "#92d1f0" }}>{item.assetStatusMeaning}</span>
          </div>
        );
      case 'SHUTDOWN':
        return (
          <div className={styles['class-main-item-status']}>
            <span className={styles['class-main-item-status-badge']} style={{ color: "#ee7975" }} />
            <span className={styles['class-main-item-status-meaning']}
              style={{ color: "#ee7975" }}>{item.assetStatusMeaning}</span>
          </div>
        );
      case 'CHANGEOVER':
        return (
          <div className={styles['class-main-item-status']}>
            <span className={styles['class-main-item-status-badge']} style={{ color: "yellow" }} />
            <span className={styles['class-main-item-status-meaning']}
              style={{ color: "yellow" }}>{item.assetStatusMeaning}</span>
          </div>
        );
      default:
        return null;
    }
  };

  const handleClearEquipmentLocationLov = () => {
    if(topFilterDs?.current){
      topFilterDs.current.set("equipmentLocationId", null);
    }
    getDeviceStatusList();
  }


  return (
    <>
      <Content style={{ padding: 0, margin: 0, height: '100%' }}>
        {/* loading */}
        <Spin spinning={initLoading}>
          <div className={styles['dashboard-container']}>
            {/* header */}
            <div className={styles['dashboard-title']}>
              <div className={styles['dashboard-title-left']}>
                <div className={styles['dashboard-title-left-one']} />
                <div className={styles['dashboard-title-left-two']} />
                <div className={styles['dashboard-title-left-three']}>
                  {intl.get(`${modelPrompt}.title.DeviceStatusMonitoringBoard`).d('设备状态监控看板')}
                </div>
              </div>
              <div className={styles['dashboard-title-right']}>
                <div className={styles['dashboard-title-right-one']}>
                  <div className={styles['dashboard-title-right-one-text']}>
                    {intl.get(`${modelPrompt}.field.factory`).d('设备位置')}
                  </div>
                  <div className={styles['dashboard-title-left-one-dom']}>
                    <div className={styles['top-select']}>
                      <Lov
                        dataSet={topFilterDs}
                        onChange={handleChangeEquipmentLocationLov}
                        name="equipmentLocationLov"
                        onClear={handleClearEquipmentLocationLov}
                      />
                    </div>
                  </div>
                </div>
                <div className={styles['dashboard-title-right-two']}>
                  <div className={styles['dashboard-title-right-two-time']}>
                    <CurrentTime />
                  </div>
                  <div className={styles['dashboard-title-right-two-home']} />
                </div>
              </div>
            </div>

            {/* Content */}
            <div className={styles['dashboard-content']}>
              <div className={styles['dashboard-item-left']}>
                {/* 设备状态 */}
                <div className={styles['class-main-title']}>
                  <div className={styles['class-main-title-icon']} />
                  <div className={styles['class-main-title-text']}>
                    {intl.get(`${modelPrompt}.field.deviceStatus`).d('设备状态')}
                  </div>
                </div>
                <div ref={scrollContainerRef} className={styles['class-main']} style={{
                  gridTemplateRows: `repeat(${gridTemplateRows}, 16%)`,
                }}>
                  {deviceStatusList?.map((item) => {
                    return (
                      <div onClick={() => getOeeBaseInfo(item)} className={styles['class-main-item']}>
                        <div
                          className={`${styles['class-main-item-name']} ${item.assetName.length > 6 ? styles['animated'] : ''}`}
                          data-text={item.assetName}
                        >
                          {item.assetName.length <= 6 ? item.assetName : ''}
                        </div>
                        <div className={styles['class-main-item-code']}>编号：{item.assetNum}</div>
                        {assetStatusListRender(item)}
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className={styles['dashboard-item-right']}>
                <div className={styles['dashboard-item-right-one']}>
                  <div className={styles['dashboard-item-right-one-title']}>
                    <div className={styles['dashboard-item-right-one-title-text']}>
                      {intl.get(`${modelPrompt}.field.deviceStatusDistribution`).d('设备状态分布')}
                    </div>
                  </div>
                  <div className={styles['dashboard-item-right-one-content']}>
                    <DeviceStatusDistribution isFullScreen={isFullScreen}
                      deviceStatusDistributionInfo={deviceStatusDistributionInfo} />
                  </div>
                </div>
                <div className={styles['dashboard-item-right-two']}>
                  <div className={styles['dashboard-item-right-two-title']}>
                    <div className={styles['dashboard-item-right-two-title-text']}>
                      {intl.get(`${modelPrompt}.field.equipmentOEE`).d('设备OEE')}
                    </div>
                  </div>
                  <div className={styles['dashboard-item-right-two-content']}>
                    <div className={styles['dashboard-item-right-two-content-item']}>
                      <div className={styles['dashboard-item-right-two-content-text']}>
                        {intl.get(`${modelPrompt}.field.equipmentOEE`).d('设备OEE')}
                      </div>
                      <div className={styles['dashboard-item-right-two-content-number']}>
                        {oeeInfo?.oee || null}
                        <span className={styles['dashboard-item-right-two-content-number-text']}>=</span>
                      </div>
                    </div>
                    <div className={styles['dashboard-item-right-two-content-item']}>
                      <div className={styles['dashboard-item-right-two-content-text']}>
                        {intl.get(`${modelPrompt}.field.equipmentOEE`).d('时间开动率')}
                      </div>
                      <div className={styles['dashboard-item-right-two-content-number']}>
                        {oeeInfo?.timeActivationRateStr || null}
                        <span className={styles['dashboard-item-right-two-content-number-text']}>x</span>
                      </div>
                    </div>
                    <div className={styles['dashboard-item-right-two-content-item']}>
                      <div className={styles['dashboard-item-right-two-content-text']}>
                        {intl.get(`${modelPrompt}.field.equipmentOEE`).d('性能开动率')}
                      </div>
                      <div className={styles['dashboard-item-right-two-content-number']}>
                        {oeeInfo?.performanceEfficiencyStr || null}
                        <span className={styles['dashboard-item-right-two-content-number-text']}>x</span>
                      </div>
                    </div>
                    <div className={styles['dashboard-item-right-two-content-item']}>
                      <div className={styles['dashboard-item-right-two-content-text']}>
                        {intl.get(`${modelPrompt}.field.equipmentOEE`).d('质量合格率')}
                      </div>
                      <div className={styles['dashboard-item-right-two-content-number']}>
                        {oeeInfo?.qualityQualificationRateStr || null}
                      </div>
                    </div>
                  </div>
                </div>
                <div className={styles['dashboard-item-right-three']}>
                  <div className={styles['dashboard-item-right-three-title']}>
                    <div className={styles['dashboard-item-right-three-title-text']}>
                      {intl.get(`${modelPrompt}.field.basicInformation`).d('基本信息')}
                    </div>
                  </div>
                  <div className={styles['dashboard-item-right-three-content']}>
                    <div className={styles['dashboard-item-right-three-content-flag']}>
                      <div className={styles['dashboard-item-right-three-content-name']}>
                        {intl.get(`${modelPrompt}.field.equFieldIdentification`).d('设备现场标识')}
                      </div>
                      <div className={styles['dashboard-item-right-three-content-value']}>
                        {baseInfo?.equFieldIdentification || null}
                      </div>
                    </div>
                    <div className={styles['dashboard-item-right-three-content-person']}>
                      <div className={styles['dashboard-item-right-three-content-name']}>
                        {intl.get(`${modelPrompt}.field.personCharge`).d('设备负责人')}
                      </div>
                      <div className={styles['dashboard-item-right-three-content-value']}>
                        {baseInfo?.personCharge || null}
                      </div>
                    </div>
                    <div className={styles['dashboard-item-right-three-content-local']}>
                      <div className={styles['dashboard-item-right-three-content-name']}>
                        {intl.get(`${modelPrompt}.field.installationPosition`).d('安装位置')}
                      </div>
                      <div className={styles['dashboard-item-right-three-content-value']}>
                        {baseInfo?.installationPosition || null}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Spin>
      </Content>
    </>
  );
};

const DeviceStatusMonitoringBoard = () => {
  const [isFullScreen, setIsFullScreen] = useState(false); // 是否全屏

  const windowFullScreenChange = () => {
    if (document.fullscreenElement) {
      // console.log('进入全屏');
      setIsFullScreen(true);
    } else {
      // console.log('退出全屏');
      setIsFullScreen(false);
    }
  };
  useEffect(() => {
    document.addEventListener('fullscreenchange', windowFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', windowFullScreenChange);
    };
  }, []);

  return (
    <>
      <div className={styles['screen-container']}>
        {isFullScreen ? (
          <FullScreenContainer>
            <Main
              isFullScreen={isFullScreen}
            />
          </FullScreenContainer>
        ) : (
          <Main
            isFullScreen={isFullScreen}
          />
        )}
      </div>
    </>
  )
    ;
};
export default DeviceStatusMonitoringBoard;
