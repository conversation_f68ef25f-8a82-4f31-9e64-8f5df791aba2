.content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
}
.content-inner {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  background-color: #fff;
  overflow: hidden;
}

.btnCol {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.btn {
  margin-left: 8px;
}
.text-field {
  width: 100%;
  :global {
    .c7n-pro-input-suffix {
      padding: 4px;
      width: auto;
      white-space: nowrap;
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
.search {
  flex-grow: 0;
  margin-bottom: 12px;
}
.tree {
  flex-grow: 1;
  border: 1px solid #d9d9d9;
}
.tree-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  :global {
    .c7n-tree,
    .c7n-tree-list {
      height: 100%;
    }
    .c7n-tree.c7n-tree .c7n-tree-treenode .c7n-tree-node-content-wrapper:hover, .c7n-tree.c7n-tree li .c7n-tree-node-content-wrapper:hover{
      opacity: 1;
    }
  }
}

:global {
  .c7n-tree-show-line {
    .c7n-tree-list-holder-inner {
      > .tree-node {
        > .c7n-tree-indent {
          > .c7n-tree-indent-unit:nth-of-type(1) {
            opacity: 0;
          }
        }
      }
    }
  }
  .c7n-spin-nested-loading {
    flex-grow: 1;
  }

  .empty-node {
    opacity: 0;
    .c7n-tree-switcher,
    .c7n-tree-node-content-wrapper {
      cursor: default !important;
    }
  }
  .draggable-tree {
    .tree-node {
      width: 100%;
      &:hover {
        .tree-node-icon {
          display: inline-block;
        }
        .tree-node-icon-item {
          z-index: 999;
        }
      }
      .tree-node-icon {
        display: none;
        .tree-node-icon-item {
          position: relative;
          top: -1px;
          padding: 0 4px;
          color: #394f51;
          .available-img {
            height: 16px;
            color: #394f51;
            opacity: 0.6;
          }
          .available-img:hover {
            opacity: 1;
          }
          .un-available-img {
            opacity: 0.2;
            cursor: no-drop;
          }
          &:hover {
            .node-add-change,
            .node-info {
              display: block;
            }
          }
          .node-add-change {
            position: absolute;
            top: 90%;
            left: 0;
            padding-top: 2px;
            display: none;
            z-index: 1;
            .node-add-change-inner {
              background: #fff;
              border: 1px solid rgba(0, 0, 0, 0.1);
              .node-add-change-row {
                position: relative;
                padding: 2px 0.5em;
                white-space: nowrap;
              }
              .node-add-change-row-disabled {
                color: rgba(0, 0, 0, 0.45);
              }
              .node-add-change-row:hover {
                background: #d6fffe;
              }
              .node-add-change-row-disabled:hover {
                background: #fff;
                cursor: auto;
              }
            }
          }
          .node-info {
            position: absolute;
            top: 90%;
            left: 0;
            padding: 2px 0.5em;
            display: none;
            white-space: nowrap;
            background: #fff;
            border: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
          }
        }
      }
      .c7n-tree-node-content-wrapper:hover {
        color: #29bece;
      }
    }
    .disabled-style {
      color: rgba(0, 0, 0, 0.45);
      .icon-note:before {
        opacity: 0.45;
      }
    }
    .focus-style {
      .tree-node-icon {
        display: inline-block;
      }
      .c7n-tree-node-content-wrapper {
        background-color: rgba(0, 0, 0, 0.04);
        color: #29bece;
      }
    }
    .c7n-tree-node-selected {
      .available-img {
        opacity: 0.5;
      }
    }
    .c7n-tree {
      .c7n-tree-switcher {
        line-height: 24px;
        height: 24px;
      }
    }
    .c7n-tree-list-holder-inner {
      padding-top: 10px;
    }
    .icon-note:before {
      display: block;
      content: ' ';
      height: 18px;
      width: 18px;
      background-image: url('../../../assets/icon_file.svg');
      background-size: 90%;
      background-position: center center;
      background-repeat: no-repeat;
    }
  }

  .c7n-tree.c7n-tree .c7n-tree-treenode .c7n-tree-node-content-wrapper:hover, .c7n-tree.c7n-tree li .c7n-tree-node-content-wrapper:hover{
    opacity: 1;
  }
}

.choose-type-modal {
  :global {
    .c7n-modal-title {
      font-size: 18px;
    }
  }
  .choose-container {
    padding-top: 30px;
    display: flex;
    justify-content: center;
    .choose-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: 0 13px;
      width: 123px;
      height: 130px;
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 0.12);
      cursor: pointer;
      .choose-type-modal-img,
      .choose-type-modal-img-hover {
        display: block;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 10px;
        height: 46px;
      }
      .choose-type-modal-img-hover {
        display: none;
      }
      .choose-type-modal-text {
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.12);
      }
      &:hover {
        border-color: #34bece;
        .choose-type-modal-text {
          color: #333;
        }
        .choose-type-modal-img {
          display: none;
        }
        .choose-type-modal-img-hover {
          display: block;
        }
      }
    }
  }
}

.tree-multiple-lov {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 24px !important;
  border: none !important;
  outline: none !important;
  opacity: 0 !important;
  &:hover {
    border: none !important;
    outline: none !important;
  }
  :global {
    .lov-search {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 0;
      margin: 0;
      width: 100%;
      height: 100%;
      min-height: 24px;
      opacity: 0;
    }
  }
}
.loading-container {
  flex-grow: 1;
}
