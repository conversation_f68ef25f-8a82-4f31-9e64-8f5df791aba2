/**
 * @Description: 月度来料合格率报表
 */

import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hmes.MonthlyIncomingMaterialPassRateReport';
const tenantId = getCurrentOrganizationId();

const monthlyIncomingMaterialPassRateReportTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  forceValidate: true,
  paging: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      textField: 'siteName',
      required: true,
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      required: true,
      lookupCode: 'MT.QMS.PERMISSION.INSPECT_BUS_TYPE_RULE',
      textField: 'inspectBusinessTypeDesc',
      valueField: 'inspectBusinessType',
      dynamicProps: {
        disabled: ({ record }) => !record?.get('siteId'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'startMateria',
      type: FieldType.month,
      label: intl.get(`${modelPrompt}.startMateria`).d('查询月份从'),
      max: 'endMateria',
      required: true,
    },
    {
      name: 'endMateria',
      type: FieldType.month,
      label: intl.get(`${modelPrompt}.endMateria`).d('查询月份至'),
      min: 'startMateria',
      required: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCategoryObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCategory`).d('物料类别'),
      lovCode: 'MT.METHOD.MATERIAL_CATEGORY',
      textField: 'description',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialCategoryId',
      bind: 'materialCategoryObject.materialCategoryId',
    },
    {
      name: 'materialCategoryCode',
      bind: 'materialCategoryObject.categoryCode',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
  ],
  fields: [
    {
      name: 'formattedDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.formattedDate`).d('时间'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'inspectCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectCount`).d('报检批次数'),
    },
    {
      name: 'inspectSumQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSumQty`).d('报检总数量'),
    },
    {
      name: 'checkoutCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.checkoutCount`).d('检验批次数'),
    },
    {
      name: 'checkoutSumQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.checkoutSumQty`).d('检验总数量'),
    },
    {
      name: 'initialCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialCount`).d('初检合格批次数'),
    },
    {
      name: 'initialYield',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialYield`).d('初检合格率'),
    },
    {
      name: 'ultimateCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ultimateCount`).d('最终合格批次数'),
    },
    {
      name: 'ultimateYield',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ultimateYield`).d('最终合格率'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/quality-monthly-material/query-yield`,
        method: 'GET',
      };
    },
  },
});


export {
  monthlyIncomingMaterialPassRateReportTableDS,
};
