import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

// 获取用户默认站点
export function GetDefaultSite(): object {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/ui`,
    method: 'GET',
  };
}

// 获取表格动态列
export function GetTableColumn(): object {
  return {
    url: `/hpfm/v1/${tenantId}/lovs/value/batch`,
    method: 'GET',
  }
}

export function SaveInfo(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-old-part-management/save/ui`,
    method: 'POST',
  }
}
