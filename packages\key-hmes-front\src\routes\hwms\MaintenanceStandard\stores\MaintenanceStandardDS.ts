/**
 * RelationMaintain - 维修标准管理-DS
 * @date: 2023-9-7
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.maintenanceStandard';
const tenantId = getCurrentOrganizationId();

// 列表-ds
const listTableManualDS = (): DataSetProps => ({
  autoLocateFirst: true,
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  primaryKey: 'sysReviewPlanId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: ({ data }) => {
      const _data = { ...data };
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-maintain-file/page/ui`,
        method: 'get',
        data: { ..._data, maintainFileType: 'MANUAL' },
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainFileCode`).d('维修手册编码'),
      name: 'maintainFileCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainFileName`).d('维修手册名称'),
      name: 'maintainFileName',
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.MAINTAIN_FILE_STATUS',
      name: 'maintainFileStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.batteryNum`).d('适配电池包编号'),
      name: 'batteryNumLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP_MES.MES.MATERIAL_LOT',
      textField: 'materialLotCode',
      valueField: 'materialLotId',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'batteryNum',
      bind: 'batteryNumLov.materialLotCode',
    },
    {
      name: 'batteryMatLotId',
      bind: 'batteryNumLov.materialLotId',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryModel`).d('电池包型号'),
      name: 'batteryModel',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('适用车型'),
      name: 'vehicleModel',
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.startIntlCode`).d('开始国标码'),
      name: 'startIntlCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.endIntlCode`).d('结束国标码'),
      name: 'endIntlCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.replaceMaintainFile`).d('替代手册编码'),
      name: 'replaceMaintainFileLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.REPLACE_MAINTAIN_FILE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'replaceMaintainFileId',
      bind: 'replaceMaintainFileLov.maintainFileId',
    },
    {
      name: 'replaceMaintainFileCode',
      bind: 'replaceMaintainFileLov.maintainFileCode',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      name: 'creationDateFrom',
      max: 'creationDateTo',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      name: 'creationDateTo',
      min: 'creationDateFrom',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      name: 'createdBylov',
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      type: FieldType.number,
      bind: 'createdBylov.id',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      bind: 'createdBylov.realName',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainFileCode`).d('维修手册编码'),
      name: 'maintainFileCode',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainFileName`).d('维修手册名称'),
      name: 'maintainFileName',
      required: true,
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.MAINTAIN_FILE_STATUS',
      name: 'maintainFileStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      disabled: true,
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.batteryNum`).d('适配电池包编号'),
      name: 'batteryNumLov',
      lovCode: 'YP_MES.MES.MATERIAL_LOT',
      textField: 'materialLotCode',
      valueField: 'materialLotId',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'batteryNum',
      bind: 'batteryNumLov.materialLotCode',
    },
    {
      name: 'batteryMatLotId',
      bind: 'batteryNumLov.materialLotId',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryModel`).d('电池包型号'),
      name: 'batteryModel',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('适用车型'),
      name: 'vehicleModel',
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.startIntlCode`).d('开始国标码'),
      name: 'startIntlCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.endIntlCode`).d('结束国标码'),
      name: 'endIntlCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.replaceMaintainFile`).d('替代手册编码'),
      name: 'replaceMaintainFileLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.REPLACE_MAINTAIN_FILE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'replaceMaintainFileId',
      bind: 'replaceMaintainFileLov.maintainFileId',
    },
    {
      name: 'replaceMaintainFileCode',
      bind: 'replaceMaintainFileLov.maintainFileCode',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      name: 'creationDate',
      format: 'YYYY-MM-DD',
      disabled: true,
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.maintainFileUuid`).d('维修手册'),
      name: 'maintainFileUuid',
      bucketName: 'qms',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      name: 'createdBylov',
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: {
        tenantId,
      },
      disabled: true,
    },
    {
      name: 'createdBy',
      type: FieldType.number,
      bind: 'createdBylov.id',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      bind: 'createdBylov.realName',
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => {
        return ['NEW', 'REJECTED'].includes(record.get('maintainFileStatus'));
      },
    },
  },
});
const listTableProgrammeDS = (): DataSetProps => ({
  autoLocateFirst: true,
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  primaryKey: 'sysReviewPlanId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: ({ data }) => {
      const _data = { ...data };
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-maintain-file/page/ui`,
        method: 'get',
        data: { ..._data, maintainFileType: 'CASE' },
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainFileCode`).d('维修案例编码'),
      name: 'maintainFileCode',
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.MAINTAIN_FILE_STATUS',
      name: 'maintainFileStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.batteryNum`).d('电池包编号'),
      name: 'batteryNumLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP_MES.MES.MATERIAL_LOT',
      textField: 'materialLotCode',
      valueField: 'materialLotId',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'batteryNum',
      bind: 'batteryNumLov.materialLotCode',
    },
    {
      name: 'batteryMatLotId',
      bind: 'batteryNumLov.materialLotId',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryModel`).d('电池包型号'),
      name: 'batteryModel',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('车型'),
      name: 'vehicleModel',
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultDescription`).d('故障现象'),
      name: 'faultDescription',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.faultTimeFrom`).d('故障时间从'),
      name: 'faultTimeFrom',
      max: 'faultTimeTo',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.faultTimeTo`).d('故障时间至'),
      name: 'faultTimeTo',
      min: 'faultTimeFrom',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultReason`).d('故障原因'),
      name: 'faultReason',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      name: 'creationDateFrom',
      max: 'creationDateTo',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      name: 'creationDateTo',
      min: 'creationDateFrom',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      name: 'createdBylov',
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      type: FieldType.number,
      bind: 'createdBylov.id',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      bind: 'createdBylov.realName',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainFileCode`).d('维修案例编码'),
      name: 'maintainFileCode',
      required: true,
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.MAINTAIN_FILE_STATUS',
      name: 'maintainFileStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      required: true,
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.batteryNum`).d('电池包编号'),
      name: 'batteryNumLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP_MES.MES.MATERIAL_LOT',
      textField: 'materialLotCode',
      valueField: 'materialLotId',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'batteryNum',
      bind: 'batteryNumLov.materialLotCode',
    },
    {
      name: 'batteryMatLotId',
      bind: 'batteryNumLov.materialLotId',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryModel`).d('电池包型号'),
      name: 'batteryModel',
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('车型'),
      name: 'vehicleModel',
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultDescription`).d('故障现象'),
      name: 'faultDescription',
      required: true,
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.faultTime`).d('故障时间'),
      name: 'faultTime',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultReason`).d('故障原因'),
      name: 'faultReason',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.checkScheme`).d('排查方案'),
      name: 'checkScheme',
      required: true,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainScheme`).d('维修方案'),
      name: 'maintainScheme',
      required: true,
    },
    {
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.maintainFileUuid`).d('附件'),
      name: 'maintainFileUuid',
      bucketName: 'qms',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      name: 'creationDate',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      name: 'createdBylov',
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: {
        tenantId,
      },
      disabled: true,
    },
    {
      name: 'createdBy',
      type: FieldType.number,
      bind: 'createdBylov.id',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      bind: 'createdBylov.realName',
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => {
        return ['NEW', 'REJECTED'].includes(record.get('maintainFileStatus'));
      },
    },
  },
});

export { listTableManualDS, listTableProgrammeDS };
