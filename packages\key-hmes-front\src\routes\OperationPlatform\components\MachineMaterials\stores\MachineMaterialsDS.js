// 加工件（工单）DS
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
// import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hmes.ProcessWorkorderMachinedPart';
// const tenantId = getCurrentOrganizationId();

const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'scanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scanCode`).d('物料信息'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.scanCode`).d('数量'),
      min: 0,
    },
  ],
});

const tableDs = () => ({
  primaryKey: 'serialNumber',
  selection: 'single',
  fields: [
    {
      name: 'serialNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
    },
    {
      name: 'assemblePointCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
    },
  ],
});

const saveTableDs = () => ({
  // primaryKey: 'serialNumber',
  selection: 'multiple',
  fields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('eo'),
    },
  ],
});

export { detailDS, tableDs, saveTableDs };
