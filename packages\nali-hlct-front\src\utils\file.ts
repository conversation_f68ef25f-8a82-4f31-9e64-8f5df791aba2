import { HZERO_FILE } from 'utils/config';
import {
  getAccessToken,
  getAttachmentUrl,
  getResponse,
  getCurrentOrganizationId,
} from 'utils/utils';
import { createUploadLog } from '../services/api';
import DOC_PNG from '@/assets/doc.png';
import PDF_PNG from '@/assets/pdf.png';
import OTHER_PNG from '@/assets/qita.png';
import XLS_PNG from '@/assets/xls.png';

const organizationId = getCurrentOrganizationId();

// 根据文件名获取文件类型
function getFileTypeByFileName(fileName: string): string {
  let type = '';
  const lastIndex = fileName?.lastIndexOf('.');
  if (lastIndex !== -1) {
    type = fileName?.substring(lastIndex + 1)?.toLowerCase();
  }
  return type;
}

// 获取文件名后缀
export function getFileSuffix(fileName) {
  return fileName?.replace(/.+\./, '')?.toLowerCase();
}

// 判断文件是否支持onlyoffice在线预览
export function canPreviewByOnlyOffice(fileName: string) {
  let canPreviewFlag = true;
  const type = getFileTypeByFileName(fileName);
  switch (type) {
    case 'jpeg':
    case 'jpg':
    case 'png':
    case 'epub':
    case 'fodt':
    case 'html':
    case 'mht':
    case 'odt':
    case 'ott':
    case 'rtf':
    case 'txt':
    case 'xps':
    case 'csv':
    case 'fods':
    case 'ods':
    case 'ots':
    case 'fodp':
    case 'odp':
    case 'otp':
    case 'pot':
    case 'potm':
    case 'potx':
    case 'pptx':
    case 'pps':
    case 'ppsm':
    case 'ppsx':
    case 'doc':
    case 'docm':
    case 'docx':
    case 'dot':
    case 'dotm':
    case 'dotx':
    case 'pdf':
    case 'xls':
    case 'xlsx':
      break;
    // 以上类型支持查看，其他的直接下载
    default:
      canPreviewFlag = false;
      break;
  }
  return canPreviewFlag;
}

// onlyoffice文件在线预览
export function previewFileByOnlyOffice(record) {
  window.open(
    `${HZERO_FILE}/v1/${record.tenantId}/file-preview/by-url?url=${encodeURIComponent(
      record.fileUrl
    )}&bucketName=${record.bucketName}${
      record.storageCode ? `&storageCode=${record.storageCode}` : ''
    }&access_token=${getAccessToken()}`
  );
}

/**
 * 下载文件插并且入日志
 * @param record 文件记录
 * @param currentEmployeeId 当前用户empId
 * @param attribute 日志附加字段信息
 * @param collectionCode
 */
export function downloadFile(
  record: any,
  currentEmployeeId: number,
  attribute = [],
  collectionCode?
) {
  const { bucketName, fileUrl, tenantId, bucketDirectory, storageCode } = record;
  const assetId = record.moduleId;
  window.open(getAttachmentUrl(fileUrl, bucketName, tenantId, bucketDirectory, storageCode));
  // 插入日志
  addFileLog(
    { name: record.fileName, fileUrl: record.fileUrl, status: 'P' },
    bucketName,
    assetId,
    currentEmployeeId,
    attribute,
    collectionCode
  );
}

// 添加文件下载日志
export function addFileLog(
  data,
  moduleName,
  moduleId,
  employeeId,
  attribute: any,
  collectionCode?
) {
  getResponse(
    createUploadLog({
      tenantId: organizationId,
      moduleName,
      moduleId,
      collectionCode,
      status: data.status,
      fileUrl: data.fileUrl || '',
      operatorId: employeeId,
      attribute: JSON.stringify(attribute.data),
      description:
        data.status === 'I'
          ? `新增"${data.name}"文档`
          : data.status === 'U'
          ? `更新"${data.name}"文档`
          : data.status === 'D'
          ? `删除"${data.name}"文档`
          : data.status === 'P'
          ? `下载"${data.name}"文档`
          : ``,
    })
  );
}

/**
 * 预览或者下载
 * 对支持onlyoffice预览进行预览，否则下载
 * @param record 文件记录
 * @param currentEmployeeId 当前用户employeeId
 * @param attribute 添加日志的附加字段
 * @param collectionCode
 */
export function handleFilePreviewOrDownload(
  record: any,
  currentEmployeeId,
  attribute = [],
  collectionCode?
) {
  const canPreviewFlag = canPreviewByOnlyOffice(record.fileName);
  if (canPreviewFlag) {
    previewFileByOnlyOffice(record);
  } else {
    downloadFile(record, currentEmployeeId, attribute, collectionCode);
  }
}

/**
 * 根据文件名设置文件图标
 * @param fileName 文件名
 */
export function getFileIcon(fileName: string) {
  const type = getFileTypeByFileName(fileName);
  let icon = OTHER_PNG;
  switch (type) {
    case 'doc':
    case 'docm':
    case 'docx':
    case 'dot':
    case 'dotm':
    case 'dotx':
      icon = DOC_PNG;
      break;
    case 'pdf':
      icon = PDF_PNG;
      break;
    case 'xls':
    case 'xlsx':
      icon = XLS_PNG;
      break;
    default:
      break;
  }
  return icon;
}
