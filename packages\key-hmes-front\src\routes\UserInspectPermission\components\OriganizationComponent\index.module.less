.treeTableWrap {
  margin-top: 18px;
  flex-grow: 1;
  display: flex;
}

.leftTree {
  flex-grow: 1;
  flex-shrink: 0;
  border: 1px solid rgb(240, 240, 240);
  overflow-x: auto;
}

.centerButton {
  flex-grow: 0;
  flex-shrink: 0;
  width: 80px;
  font-size: 30px;
  margin-top: 30px;

  .centerButtonRow {
    text-align: center;
  }
}

.rightTable {
  flex-grow: 0;
  flex-shrink: 0;
  border: 1px solid rgb(240, 240, 240);
  border-top: none;
  width: 600px;
  overflow-x: auto;
}

.centerButtonItem {
  width: 25px !important;
  min-width: 25px !important;
  height: 25px !important;
  min-height: 25px !important;
  padding: 0 !important;
}

.tableToolHide {
  :global {
    .c7n-pro-table-toolbar {
      display: none !important;
    }
  }
}
