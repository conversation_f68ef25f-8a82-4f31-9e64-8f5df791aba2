import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.purchase.outsourcingManage';
const tenantId = getCurrentOrganizationId();

const headDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows.outSourceReplenishmentSaveHeader',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-out-source/out-source/Init/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_SUPPLEMENT.HEAD,${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_SUPPLEMENT.LINE`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.instructionDocNum`).d('外协补料单号'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.supplierCode`).d('供应商'),
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.supplierSiteCode`).d('供应商地点'),
    },
    {
      name: 'expectedArrivalTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.expectedArrivalTime`).d('预计发货时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.remark`).d('备注'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      lookupCode: 'SOURCE_SYSTEM',
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
});

const tableDS = (): DataSetProps => {
  return {
    autoQuery: false,
    autoCreate: false,
    paging: false,
    selection: false,
    autoLocateFirst: false,
    forceValidate: true,
    dataKey: 'rows',
    primaryKey: 'lineNumber',
    fields: [
      {
        name: 'lineNumber',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.lineNumber`).d('行号'),
      },
      {
        name: 'materialId',
        type: FieldType.number,
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.materialCode`).d('物料'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.revisionCode`).d('物料版本'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.materialName`).d('物料描述'),
      },
      {
        name: 'siteId',
        type: FieldType.number,
      },
      {
        name: 'siteCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.siteCode`).d('站点'),
      },
      {
        name: 'quantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.quantity`).d('制单数量'),
        required: true,
        min: 0,
        // @ts-ignore
        validator: (...args: Array<any>) => {
          const {
            data: { quantity, actualQty },
          } = args[2];
          if (quantity > actualQty) {
            return `${intl
              .get(`${modelPrompt}.not.exceed.actualQty`, {
                actualQty,
              })
              .d(`不能超过默认制单数量${actualQty}`)}`;
          }
        },
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.uomCode`).d('单位'),
      },
      {
        name: 'fromLocatorLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.table.fromLocatorLov`).d('发货仓库'),
        lovCode: 'APEX_WMS.MODEL.LOCATOR_BY_ORG',
        ignore: FieldIgnore.always,
        required: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              locatorCategoryList: ['AREA'],
              businessTypes: 'REPLENISHMENT',
              queryType: 'SOURCE',
              siteIds: [record?.get('siteId')].join(','),
            };
          },
        },
      },
      {
        name: 'fromLocatorId',
        type: FieldType.number,
        bind: 'fromLocatorLov.locatorId',
      },
      {
        name: 'fromLocatorCode',
        type: FieldType.string,
        bind: 'fromLocatorLov.locatorCode',
      },
      {
        name: 'sumAvailableQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.sumAvailableQty`).d('库位现存量'),
      },
      {
        name: 'toleranceFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.toleranceFlag`).d('允差标识'),
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'toleranceType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.toleranceType`).d('允差类型'),
        textField: 'description',
        valueField: 'typeCode',
        lovPara: { tenantId },
        dynamicProps: {
          required: ({ record }) => {
            return record?.get('toleranceFlag') === 'Y';
          },
          disabled: ({ record }) => {
            return record?.get('toleranceFlag') !== 'Y';
          },
        },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'toleranceMaxValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.toleranceMaxValue`).d('上允差值'),
        min: 0,
        dynamicProps: {
          required: ({ record }) => {
            return (
              record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER'
            );
          },
          disabled: ({ record }) => {
            return (
              record?.get('toleranceFlag') !== 'Y' ||
              record?.get('toleranceType') === 'OVER_MATERIAL_LOT' ||
              record?.get('toleranceType') === 'UNLIMITED'
            );
          },
        },
      },
      {
        name: 'toleranceMinValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.toleranceMinValue`).d('下允差值'),
        min: 0,
        dynamicProps: {
          required: ({ record }) => {
            return (
              record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER'
            );
          },
          disabled: ({ record }) => {
            return (
              record?.get('toleranceFlag') !== 'Y' ||
              record?.get('toleranceType') === 'OVER_MATERIAL_LOT' ||
              record?.get('toleranceType') === 'UNLIMITED'
            );
          },
        },
      },
    ],
  };
};

export { headDS, tableDS };
