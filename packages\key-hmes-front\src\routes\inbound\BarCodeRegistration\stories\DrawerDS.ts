/**
 * @feature 成品条码注册功能-抽屉DS
 * @date 2021-12-28
 * <AUTHOR>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { isNull, isUndefined } from 'lodash';

const modelPrompt = 'tarzan.inbound.barCodeRegistration';
const tenantId = getCurrentOrganizationId();

const drawerFormDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  fields: [
    {
      name: 'workOrderObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
      lovCode: 'APEX_WMS.WORK_ORDER_NUM',
      lovPara: {
        tenantId,
      },
      textField: 'workOrderNum',
      valueField: 'workOrderId',
      ignore: FieldIgnore.always,
      disabled: true,
    },
    {
      name: 'workOrderId',
      type: FieldType.number,
      bind: 'workOrderObj.workOrderId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      bind: 'workOrderObj.workOrderNum',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      disabled: true,
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      disabled: true,
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      disabled: true,
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.quantity`).d('生产指令数量'),
      disabled: true,
    },
    {
      name: 'sumQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumQty`).d('已创建数量'),
      disabled: true,
    },
    {
      name: 'storageQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.storageQty`).d('已入库数量'),
      disabled: true,
    },
    {
      name: 'canCreateBarcodeQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.createdQty`).d('可创建条码数量'),
      disabled: true,
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      disabled: true,
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryBarcodeQuantity`).d('每张条码数量'),
      required: true,
      // min: 0.000001,
      defaultValidationMessages: {
        rangeUnderflow: intl.get(`${modelPrompt}.greater.than.zero`).d('必须大于零'),
      },
      // validator: (...args: Array<any>) => {
      //   const {
      //     data: { barcodePageQuantity = 0, primaryUomQty, canCreateBarcodeQty },
      //   } = args[2];
      //   const inputQty = isUndefined(barcodePageQuantity) ? 0 : barcodePageQuantity * primaryUomQty;
      //   if (isUndefined(barcodePageQuantity) || isNull(barcodePageQuantity)) {
      //     return true;
      //   }
      //   if (inputQty > canCreateBarcodeQty) {
      //     return intl
      //       .get(`${modelPrompt}.quantity.verification`)
      //       .d('张数*条码数量需小于等于可创建条码数量');
      //   }
      // },
      dynamicProps: {
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
      },
    },
    {
      name: 'barcodePageQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.barCodeQty`).d('条码张数'),
      required: true,
      min: 1,
      step: 1,
      validator: (...args: Array<any>) => {
        const {
          data: { primaryUomQty = 0, barcodePageQuantity, canCreateBarcodeQty },
        } = args[2];
        const inputQty = isUndefined(primaryUomQty) ? 0 : barcodePageQuantity * primaryUomQty;
        if (isUndefined(primaryUomQty) || isNull(primaryUomQty)) {
          return true;
        }
        if (inputQty > canCreateBarcodeQty) {
          return intl
            .get(`${modelPrompt}.quantity.verification`)
            .d('张数*条码数量需小于等于可创建条码数量');
        }
      },
    },
    {
      name: 'productionDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
      required: true,
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
  ],
});

const drawerTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identfication`).d('物料批标识'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'productionDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-product-barcode-register/material-lot/info/ui`,
        method: 'GET',
      };
    },
  },
});

export { drawerFormDS, drawerTableDS };
