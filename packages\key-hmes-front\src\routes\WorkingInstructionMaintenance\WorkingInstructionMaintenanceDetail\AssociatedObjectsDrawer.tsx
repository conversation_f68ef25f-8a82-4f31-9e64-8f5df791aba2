/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-25 14:02:50
 * @LastEditTime: 2023-07-25 21:11:39
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useMemo, useEffect } from 'react';
import { Table, Button, Lov, Select } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { materialRevisionOptionDs } from '../stores/AssociatedObjectDS';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

export default ({ singleAssObjectDs, record: propsRecord, canEdit, parentSiteId }) => {
  const [selected, setSelected] = useState({});
  const selectedProxy = useMemo(() => ({ selected }), []);
  selectedProxy.selected = selected;
  const typeName = {
    MATERIAL: intl.get(`${modelPrompt}.MATERIAL`).d('物料'),
    MATERIAL_CATEGORY: intl.get(`${modelPrompt}.MATERIAL_CATEGORY`).d('物料类别'),
    OPERATION: intl.get(`${modelPrompt}.OPERATION`).d('工艺'),
    WORKCELL: intl.get(`${modelPrompt}.WORKCELL`).d('工作单元'),
    EQUIPMENT: intl.get(`${modelPrompt}.EQUIPMENT`).d('设备'),
  };

  useEffect(() => {
    const cacheObjectList = propsRecord.get('detailList') || [];
    const cacheSelected = {};
    cacheObjectList.forEach(item => {
      const { objectType } = item;
      if (objectType === 'MATERIAL' && item.objectRevision) {
        // 启用版本的物料，需要查一下
        materialRevisionOptionDs.setQueryParameter('siteIds', parentSiteId);
        materialRevisionOptionDs.setQueryParameter('materialId', item.objectId);
        materialRevisionOptionDs.query();
      }
      cacheSelected[item.objectType] = true;
    });
    setSelected(cacheSelected);
    singleAssObjectDs.loadData(
      cacheObjectList.map(item => {
        if (item.objectType === 'MATERIAL' && item.objectRevision) {
          return { ...item, uuid: uuid(), parentSiteId, revisionFlag: 'Y' };
        }
        return { ...item, uuid: uuid(), parentSiteId };
      }),
    );
  }, []);

  const selectObjTypeDiv = type => {
    if (!canEdit) {
      return;
    }
    if (selected[type]) {
      // 已选过的类型，再次点击为删除行
      const newList = singleAssObjectDs.filter(record => record.get('objectType') !== type);
      const newSelected = {
        ...selectedProxy.selected,
        [type]: false,
      };
      singleAssObjectDs.loadData(newList);
      setSelected(newSelected);
    } else {
      // 新增行
      const newRow = { uuid: uuid(), objectType: type, parentSiteId };
      singleAssObjectDs.create(newRow);
      setSelected({
        ...selectedProxy.selected,
        [type]: true,
      });
    }
  };

  const changeObject = async (lovRecords, record) => {
    if (record.get('objectType') === 'MATERIAL') {
      singleAssObjectDs.current.set('objectRevision', null);
    } else if (record.get('revisionFlag')) {
      materialRevisionOptionDs.setQueryParameter(
        'siteIds',
        (record.get('parentSiteId') || []).join(','),
      );
      materialRevisionOptionDs.setQueryParameter('materialId', lovRecords.materialId);
      materialRevisionOptionDs.query();
    }
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        header: null,
        align: ColumnAlign.center,
        width: 70,
        renderer: ({ record }) => {
          const objType = record?.get('objectType');
          return (
            <Popconfirm
              title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => {
                selectObjTypeDiv(objType);
              }}
            >
              <Button disabled={!canEdit} funcType={FuncType.flat} icon="remove" size={Size.small} />
            </Popconfirm>
          );
        },
        lock: ColumnLock.left,
      },
      {
        name: 'objectType',
        width: 120,
        renderer: ({ record }) => typeName[record?.get('objectType')],
      },
      {
        name: 'objectLov',
        width: 180,
        renderer: ({ record }) => record?.get('objectCode'),
        editor: record => {
          return (
            canEdit && (
              <Lov
                dataSet={singleAssObjectDs}
                onChange={lovRecords => changeObject(lovRecords, record)}
              />
            )
          );
        },
      },
      {
        name: 'objectRevision',
        width: 120,
        editor: record => {
          return (
            canEdit &&
            record.get('revisionFlag') && <Select dataSet={singleAssObjectDs} />
          );
        },
      },
      {
        name: 'objectDesc',
      },
    ],
    [canEdit, selected, selectedProxy],
  );

  return (
    <>
      <div className={styles['card-select-wrapper']}>
        {Object.keys(typeName).map(item => {
          if (selected[item]) {
            return (
              <Popconfirm
                title={intl
                  .get(`${modelPrompt}.message.confirm.delete`, {
                    typeName: typeName[item],
                  })
                  .d(`是否确认删除关联对象类型为“${typeName[item]}”的数据?`)}
                onConfirm={() => {
                  selectObjTypeDiv(item);
                }}
              >
                <div key={item} className={styles['card-select']} data-selected disabled={!canEdit}>
                  {typeName[item]}
                </div>
              </Popconfirm>
            );
          }
          return (
            <div
              key={item}
              onClick={() => {
                selectObjTypeDiv(item);
              }}
              className={styles['card-select']}
              disabled={!canEdit}
            >
              {typeName[item]}
            </div>
          );
        })}
      </div>
      <Table dataSet={singleAssObjectDs} columns={columns} />
    </>
  );
};
