/**
 * @Description: 检验业务类型规则维护-详情页DS
 * @Author: <EMAIL>
 * @Date: 2023/1/30 17:13
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';



const modelPrompt = 'tarzan.hwms.inspectBusTypeRule';
const tenantId = getCurrentOrganizationId();

const endUrl = '';

const formDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  dataKey: 'rows.content',
  primaryKey: 'inspectBusinessTypeRuleId',
  fields: [
    { name: 'userLov', type: FieldType.object },
    {
      name: 'inspectBusinessTypeRuleId',
      type: FieldType.number,
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessTypeCode`).d('业务类型编码'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => record.get('inspectBusinessTypeRuleId'),
      },
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessTypeDesc`).d('业务类型描述'),
      required: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      ignore: FieldIgnore.always,
      required: true,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'operatingMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operatingMode`).d('操作模式'),
      required: true,
      lookupCode: 'MT.QMS.OPERATING_MODE',
      lovPara: { tenantId },
    },
    {
      name: 'reviewType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewType`).d('不良记录单评审规则类型'),
      required: true,
      lookupCode: 'MT.QMS.REVIEW_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'workFlowCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workFlowCode`).d('工作流编码'),
      dynamicProps: {
        required: ({ record }) =>
          !['NO_REVIEW', 'MANUAL_REVIEW', 'OFFLINE_REVIEW'].includes(record.get('reviewType')),
        disabled: ({ record }) => record.get('reviewType') === 'NO_REVIEW',
      },
    },
    {
      name: 'recheckFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.recheckFlag`).d('复检标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'recheckTaskCreateNode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recheckTaskCreateNode`).d('复检任务生成节点'),
      lookupCode: 'MT.QMS.RECHECK_TASK_CREATE_NODE',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => record.get('recheckFlag') === 'Y',
        disabled: ({ record }) => record.get('recheckFlag') === 'N',
      },
    },
    {
      name: 'recheckResultDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recheckResultDimension`).d('复检结果记录维度'),
      lookupCode: 'MT.QMS.RESULT_DIMENSION',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => record.get('recheckFlag') === 'Y',
        disabled: ({ record }) => record.get('recheckFlag') === 'N',
      },
    },
    {
      name: 'taskAssignType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskAssignType`).d('任务分配类型'),
      lookupCode: 'MT.QMS.TASK_ASSIGN_TYPE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'recheckTaskAssignType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recheckTaskAssignType`).d('复检任务分配类型'),
      lookupCode: 'MT.QMS.TASK_ASSIGN_TYPE',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => record.get('recheckFlag') === 'Y',
        disabled: ({ record }) => record.get('recheckFlag') === 'N',
      },
    },
    {
      name: 'docCompleteFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.docCompleteFlag`).d('检验单自动完成标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      // dynamicProps: {
      //   disabled: ({ record }) =>
      //     record.get('reviewType') === 'NO_REVIEW' &&
      //     record.get('taskSplitMethod') === 'SPLIT_BY_GROUP',
      // },
    },
    {
      name: 'mergeFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.mergeFlag`).d('合批标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'mergeDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mergeDimension`).d('合批维度'),
      lookupCode: 'MT.QMS.MERGE_DIMENSION',
      multiple: true,
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => record.get('mergeFlag') === 'Y',
        disabled: ({ record }) => record.get('mergeFlag') === 'N',
      },
    },
    {
      name: 'resultDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.resultDimension`).d('结果记录维度'),
      required: true,
      lookupCode: 'MT.QMS.RESULT_DIMENSION',
      lovPara: { tenantId },
    },
    {
      name: 'transferFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.transferFlag`).d('启用转移规则标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'transferDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferDimension`).d('转移维度'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.TRANSFER_DIMENSION',
      dynamicProps: {
        required: ({ record }) => record.get('transferFlag') === 'Y',
        disabled: ({ record }) => record.get('transferFlag') === 'N',
      },
    },
    {
      name: 'transferRuleLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.transferRuleCode`).d('转移规则编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.SAMPLING.RULE',
      lovPara: { tenantId },
      dynamicProps: {
        required: ({ record }) => record.get('transferFlag') === 'Y',
        disabled: ({ record }) => record.get('transferFlag') === 'N',
      },
    },
    {
      name: 'transferRuleId',
      type: FieldType.number,
      bind: 'transferRuleLov.samplingTransferRuleId',
    },
    {
      name: 'transferRuleCode',
      type: FieldType.string,
      bind: 'transferRuleLov.samplingTransferRuleCode',
    },
    {
      name: 'taskSplitMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskSplitMethod`).d('检验任务拆分方式'),
      required: true,
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.TASK_SPLIT_METHOD',
    },
    {
      name: 'ncCodeFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.ncCodeFlag`).d('不良代码管理标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'blankInspectOrderFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.blankInspectOrderFlag`).d('空白检验单标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'ngAssignRuleType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngAssignRuleType`).d('不合格品分配原则'),
      required: true,
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.ASSIGN_RULE_TYPE',
    },
    {
      name: 'scrapAssignRuleType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapAssignRuleType`).d('报废品分配原则'),
      required: true,
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.ASSIGN_RULE_TYPE',
    },
    {
      name: 'dispositionGroupLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dispositionGroup`).d('处置组'),
      lovCode: 'MT.DISPOSITION_GROUP',
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record.get('siteId'),
        }),
        disabled: ({ record }) => !record.get('siteId'),
      },
    },
    {
      name: 'dispositionGroupId',
      type: FieldType.number,
      bind: 'dispositionGroupLov.dispositionGroupId',
    },
    {
      name: 'dispositionGroupDesc',
      type: FieldType.string,
      bind: 'dispositionGroupLov.dispositionGroup',
    },
    {
      name: 'autoCreateNcReportFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoCreateNcReportFlag`).d('自动生成不良记录单标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => {
          return ['NO_REVIEW', 'AUTO_REVIEW'].includes(record.get('reviewType'));
        },
      },
    },
    {
      name: 'ncReportType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReportType`).d('不良记录单单据类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=NC_REPORT_TYPE`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        required: ({ record }) => record.get('autoCreateNcReportFlag') === 'Y',
        disabled: ({ record }) => record.get('autoCreateNcReportFlag') === 'N',
      },
    },
    {
      name: 'inspectNcRecordDimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectNcRecordDimension`).d('检验不良记录维度'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.INSPECT_NC_RECORD_TYPE',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.actualInspectBusinessType`).d('实际获取检验方案的检验业务类型'),
      name: 'actualInspectBusinessTypeLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'actualInspectBusinessType',
      bind: 'actualInspectBusinessTypeLov.inspectBusinessType',
    },
    {
      name: 'actualInspectBusinessTypeDesc',
      bind: 'actualInspectBusinessTypeLov.inspectBusinessTypeDesc',
    },
    {
      type: FieldType.string,
      label: intl
        .get(`${modelPrompt}.operatingEnd`)
        .d('操作端'),
      name: 'operatingEnd',
      lookupCode: 'MT.QMS.OPERATING_END',
      multiple: ',',
    },
    {
      name: 'templateType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.templateType`).d('模板类型'),
      lovPara: { tenantId },
      lookupCode: 'MT.QMS.TEMPLATE_TYPE',
    },
    {
      name: 'printTemplate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.printTemplate`).d('打印模板'),
      dynamicProps: {
        required: ({ record }) => record.get('templateType'),
        disabled: ({ record }) => !record.get('templateType'),
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'oneselfFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oneselfFlag`).d('是否强校验检验员'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'enterFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterFlag`).d('回车增加记录框'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.messageCode`).d('消息代码'),
      name: 'messageLov',
      lovCode: 'HMSG.TEMPLATE_SERVER',
      textField: 'messageCode',
      valueField: 'messageCode',
    },
    {
      name: 'messageCode',
      bind: 'messageLov.messageCode',
    },
    {
      name: 'messageName',
      bind: 'messageLov.messageName',
    },
    {
      name: 'messageId',
      bind: 'messageLov.tempServerId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.receiverType`).d('接收组'),
      name: 'receiverTypeLov',
      lovCode: 'HMSG.RECEIVER_TYPE_VIEW',
      textField: 'typeCode',
      valueField: 'typeCode',
    },
    {
      name: 'receiverTypeCode',
      bind: 'receiverTypeLov.typeCode',
    },
    {
      name: 'receiverTypeCodeName',
      bind: 'receiverTypeLov.typeName',
    },
    {
      name: 'receiverTypeId',
      bind: 'receiverTypeLov.receiverTypeId',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-bus-type-rule/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          return {
            ...rows?.header,
            lines: rows?.lines,
          };
        },
      };
    },
  },
  events: {
    update: ({ record, name }) => {
      switch (name) {
        case 'siteLov':
          record.init('dispositionGroupLov', undefined);
          record.init('actualInspectBusinessTypeLov', undefined);
          break;
        case 'reviewType':
          record.set('workFlowCode', undefined);
          break;
        case 'recheckFlag':
          record.set('recheckTaskCreateNode', undefined);
          record.set('recheckResultDimension', undefined);
          record.set('recheckTaskAssignType', undefined);
          break;
        case 'mergeFlag':
          if (record.get('mergeFlag') === 'Y') {
            record.set('mergeDimension', ['INSPECT_OBJECT']);
          } else {
            record.set('mergeDimension', undefined);
          }
          break;
        case 'transferFlag':
          record.init('transferRuleLov', undefined);
          record.init('transferDimension', undefined);
          break;
        case 'templateType':
          record.set('printTemplate', undefined);
          break;
        case 'autoCreateNcReportFlag':
          record.set('ncReportType', undefined);
          break;
        default:
          break;
      }
    },
  },
});

// 若第一条数据为工序、工位、产线、区域、工艺、设备，后续的数据也只能维护工序、工位、产线、区域、工艺、设备
// 若第一条数据为供应商，后续的数据也只能维护供应商
// 若第一条数据为客户，后续的数据也只能维护客户
const validateFlag = ({ dataSet, name }) => {
  // 找出第一行有维度的数据（基准行）
  const dimensionFieldList = [
    'operationFlag',
    'processWorkcellFlag',
    'stationWorkcellFlag',
    'prodLineFlag',
    'areaFlag',
    'supplierFlag',
    'customerFlag',
    'equipmentFlag',
  ];
  const _dimensionRecord = dataSet.find(_currentRecord => {
    return dimensionFieldList.some(item => _currentRecord.get(item) === 'Y');
  });
  // 如果只有一条数据或未找到有维度的数据，不做限制
  if (dataSet.length === 1 || _dimensionRecord?.id === undefined) {
    return false;
  }
  // 拿基准行对其他行做禁用限制
  const operationFieldList = [
    'operationFlag',
    'processWorkcellFlag',
    'stationWorkcellFlag',
    'prodLineFlag',
    'areaFlag',
    'equipmentFlag',
  ];
  const supplierField = 'supplierFlag';
  const customerField = 'customerFlag';
  let _openCount: number = 0;
  if (operationFieldList.includes(name)) {
    operationFieldList.forEach(item => {
      if (_dimensionRecord.get(item) === 'Y') {
        _openCount++;
      }
    });
  } else if (name === supplierField && _dimensionRecord.get(supplierField) === 'Y') {
    _openCount++;
  } else if (name === customerField && _dimensionRecord.get(customerField) === 'Y') {
    _openCount++;
  }
  return _openCount === 0;
};

const lineDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: true,
  selection: false,
  paging: false,
  dataKey: 'rows.content',
  primaryKey: 'inspectBusTypeRuleDtlId',
  fields: [
    {
      name: 'inspectBusTypeRuleDtlId',
      type: FieldType.number,
    },
    {
      name: 'priority',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.priority`).d('优先级'),
      required: true,
      min: 1,
      step: 1,
      defaultValue: 10,
    },
    {
      name: 'materialFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.materialFlag`).d('按物料匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'materialCategoryFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.materialCategoryFlag`).d('按物料类别匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'operationFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.operationFlag`).d('按工艺匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: validateFlag,
      },
    },
    {
      name: 'processWorkcellFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.processWorkcellFlag`).d('按工序匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: validateFlag,
      },
    },
    {
      name: 'stationWorkcellFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.stationWorkcellFlag`).d('按工位匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: validateFlag,
      },
    },
    {
      name: 'equipmentFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.equipmentFlag`).d('按设备匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: validateFlag,
      },
    },
    {
      name: 'prodLineFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.prodLineFlag`).d('按产线匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: validateFlag,
      },
    },
    {
      name: 'areaFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.areaFlag`).d('按区域匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: validateFlag,
      },
    },
    {
      name: 'supplierFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.supplierFlag`).d('按供应商匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: validateFlag,
      },
    },
    {
      name: 'customerFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.customerFlag`).d('按客户匹配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: validateFlag,
      },
    },
  ],
  events: {
    update: ({ record, name, value }) => {
      if (name === 'materialFlag' && value === 'Y') {
        record.set('materialCategoryFlag', 'N');
      }
      if (name === 'materialCategoryFlag' && value === 'Y') {
        record.set('materialFlag', 'N');
      }
    },
  },
});

export { formDS, lineDS };
