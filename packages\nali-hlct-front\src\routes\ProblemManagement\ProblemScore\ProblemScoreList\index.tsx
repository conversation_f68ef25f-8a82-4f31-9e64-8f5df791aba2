/*
 * @Description: 问题积分报表-列表页
 * @Author: <<EMAIL>>
 * @Date: 2023-11-30 13:47:43
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-06 09:29:09
 */
import React, { useMemo, useState } from 'react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { isNil } from 'lodash';
import ExcelExport from 'components/ExcelExport';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType, ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import moment from 'moment';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { SaveProblemScore } from '../services';
import { tableDS } from '../stores';

const modelPrompt = 'tarzan.problemManagement.problemScore';
const tenantId = getCurrentOrganizationId();

const ProblemScoreList = props => {
  const { tableDs } = props;

  const [canEdit, setCanEdit] = useState<boolean>(false);
  const { run: saveProblemScore } = useRequest(SaveProblemScore(), {
    manual: true,
    needPromise: true,
  });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'userName', lock: ColumnLock.left },
      { name: 'userNumber', lock: ColumnLock.left },
      { name: 'userPost', width: 120 },
      { name: 'department', width: 120 },
      { name: 'year', width: 120, align: ColumnAlign.center },
      {
        name: 'first',
        title: intl.get(`${modelPrompt}.column.first`).d('第一季度'),
        style: { backgroundColor: 'rgb(255, 248, 240)' },
        children: [
          {
            name: 'firstScore',
            width: 150,
            style: { backgroundColor: 'rgb(255, 248, 240)' },
          },
          {
            name: 'firstRanking',
            width: 150,
            style: { backgroundColor: 'rgb(255, 248, 240)' },
          },
        ],
      },
      {
        name: 'second',
        title: intl.get(`${modelPrompt}.column.second`).d('第二季度'),
        style: { backgroundColor: 'rgb(230, 255, 255)' },
        children: [
          {
            name: 'secondScore',
            width: 150,
            style: { backgroundColor: 'rgb(230, 255, 255)' },
          },
          {
            name: 'secondRanking',
            width: 150,
            style: { backgroundColor: 'rgb(230, 255, 255)' },
          },
        ],
      },
      {
        name: 'third',
        title: intl.get(`${modelPrompt}.column.third`).d('第三季度'),
        style: { backgroundColor: 'rgb(252, 255, 230)' },
        children: [
          {
            name: 'thirdScore',
            width: 150,
            style: { backgroundColor: 'rgb(252, 255, 230)' },
          },
          {
            name: 'thirdRanking',
            width: 150,
            style: { backgroundColor: 'rgb(252, 255, 230)' },
          },
        ],
      },
      {
        name: 'fourth',
        title: intl.get(`${modelPrompt}.column.fourth`).d('第四季度'),
        style: { backgroundColor: 'rgb(230, 255, 234)' },
        children: [
          {
            name: 'fourthScore',
            width: 150,
            style: { backgroundColor: 'rgb(230, 255, 234)' },
          },
          {
            name: 'fourthRanking',
            width: 150,
            style: { backgroundColor: 'rgb(230, 255, 234)' },
          },
        ],
      },
      {
        name: 'year',
        title: intl.get(`${modelPrompt}.column.year`).d('年度'),
        style: { backgroundColor: 'rgb(245, 240, 255)' },
        children: [
          {
            name: 'yearScore',
            width: 150,
            style: { backgroundColor: 'rgb(245, 240, 255)' },
          },
          {
            name: 'yearRanking',
            width: 150,
            style: { backgroundColor: 'rgb(245, 240, 255)' },
          },
        ],
      },
      { name: 'siteName' },
      { name: 'remark', editor: canEdit },
    ];
  }, [canEdit]);

  const handleSave = () => {
    return new Promise(async (resolve) => {
      const data = (tableDs.toData() || []).map((item => {
        const { userId, siteId, remark } = item;
        return {
          userId,
          siteId,
          remark,
        };
      }))
      const res = await saveProblemScore({
        params: data,
      });
      if (res?.success) {
        notification.success({});
        tableDs.query(tableDs.currentPage);
        setCanEdit(false);
        return resolve(true);
      }
      return resolve(false);
    });
  };

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      } else if (i === 'year') {
        queryParmas[i] = queryParmas[i] ? moment(queryParmas[i]).format('yyyy') : undefined;
      }
    });
    return queryParmas;
  };

  const handleCancel = () => {
    tableDs.query(tableDs.currentPage);
    setCanEdit(false);
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('问题积分表')}>
        {canEdit ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave()}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            icon="edit-o"
            color={ButtonColor.primary}
            onClick={() => setCanEdit(true)}
            permissionList={[
              {
                code: `${modelPrompt}.list.button.edit`,
                type: 'button',
                meaning: '详情页-编辑按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <ExcelExport
          exportAsync
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem-scores/export/ui`}
          queryParams={getExportQueryParams}
        >
          {intl.get(`${modelPrompt}.button.export`).d('导出')}
        </ExcelExport>
      </Header>
      <Content>
        <Table
          border
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="problemScore_searchCode"
          customizedCode="problemScore_customizedCode"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ProblemScoreList),
);
