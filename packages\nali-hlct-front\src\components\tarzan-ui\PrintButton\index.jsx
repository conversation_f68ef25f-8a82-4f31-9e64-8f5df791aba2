/**
 * @Description: 打印预览组件，支持将后端返回过来的html预览并调用浏览器打印
 * <AUTHOR> <<EMAIL>>
 *
 * @example
 *  import { PrintButton } from '@/components/tarzan-ui'
 *  <PrintButton dataSet={props.dataSet} labelTemplateCode='MT.MATERIAL_LOT_TEMP_002'/>
 */
import React from 'react';
import { Button, Modal } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import myInstance from '@/utils/myAxios';
import { BASIC, API_HOST } from '@/utils/config';
import PrintElement from './PrintElement';

const tenantId = getCurrentOrganizationId();

const PrintButton = props => {
  /* eslint-disable */
  const openPdfModal = async () => {
    const select = props.dataSet.selected;
    if ((select || []).length === 0) {
      notification.warning({
        message: intl.get(`tarzan.common.info.lessOne`).d('请至少勾选一条数据！'),
      });
      return;
    }
    let identifications = '';
    select.map((item, index) => {
      if (index === 0) {
        identifications = item.data.identification;
      } else {
        identifications = identifications + ',' + item.data.identification;
      }
    });
    const printDomRef = React.createRef(null);
    const html = await getHtml(identifications);
    await Modal.open({
      drawer: true,
      maskClosable: true,
      key: 'ModalKey',
      destroyOnClose: true,
      closable: true,
      style: {
        width: 720,
      },
      title: intl.get('tarzan.common.title.preview').d('预览'),
      children: (
        <div
          id="pdf"
          style={{ paddingBottom: 20, overflow: 'hidden' }}
          ref={printDomRef}
          dangerouslySetInnerHTML={{ __html: html }}
        />
      ),
      footer: () => (
        <div>
          <Button
            icon="print"
            disabled={!select.length}
            onClick={() => {
              PrintElement({
                content: document.getElementById('pdf'),
              });
            }}
          >
            {intl.get('tarzan.common.button.print').d('打印')}
          </Button>
        </div>
      ),
    });
  };

  const getHtml = identifications => {
    if (!identifications) {
      return;
    }
    const url = `${API_HOST}${BASIC.HRPT_COMMON}/v1/${tenantId}/label-prints/view/html?labelTemplateCode=${props.labelTemplateCode}&tenantId=${tenantId}&labelTenantId=${tenantId}&identifications=${identifications}`;
    myInstance.get(url).then(res => {
      if (res.statusText === 'OK' && (res.data || {}).label) {
        document.getElementById('pdf').innerHTML = res.data.label.replace(/↵/gm, ''); // 去掉回车换行;
      } else {
        notification.error({
          message: res.data.message,
          description: '',
        });
      }
    });
  };
  return (
    <Button icon="print" onClick={openPdfModal} disabled={props.disabled}>
      {intl.get('tarzan.common.button.print').d('打印')}
    </Button>
  );
};

export default PrintButton;
