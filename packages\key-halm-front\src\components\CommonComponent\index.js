/**
 * 逻辑通用组件
 * @since 2021/09/06
 * <AUTHOR> <kejie.lu@hand-china>
 */
import React from 'react';
import { Lov, Output, TextField, NumberField, Select, DateTimePicker } from 'choerodon-ui/pro';
import { Icon } from 'choerodon-ui';

// import AssetLov from 'alm/components/AssetLov';
import EmployeesLov from "../EmployeesLov";

import { handleChangeAsset, handleChangeDescAndLabel } from './utils/asset';
import { handleOpenMapModal, handleChangeLocation, handleOpenLocTreeModal } from './utils/location';
import { handleChangeMaintSite } from './utils/maintSite';
import {
  handleChangePlanner,
  handleChangePlannerGroup,
  handleChangeOwnerGroup,
  handleChangeOwner,
} from './utils/employee';
import {
  handleDurationScheduledChange,
  handleDurationUomChange,
  handleScheduledStartDateChange,
  handleScheduledFinishDateChange,
} from './utils/scheduledTime';

import './index.module.less';

const CommonComponent = ({ name, isEdit = false, dataSet, ...props }) => {
  const {
    required,
    disabled,
    newLine = false,
    queryParams,
    moreProps = {},
    moreFun,
    callback,
  } = props;

  const { searchItem = {}, providedObjectCode, changeTarget = false } = moreProps;
  const { sourceParamType } = searchItem;
  const record = dataSet?.current ? dataSet.current : dataSet;
  const style = {
    width: '100%',
  };
  const styleProps = { style, disabled };
  let component;

  switch (name) {
    // 计划员组（通用模块1）
    case 'plannerGroupName':
      // 通过record获取服务区域
      component = isEdit ? (
        <EmployeesLov
          name="plannerGroupName"
          disabled={disabled}
          record={record}
          onOk={e => handleChangePlannerGroup(e, record, callback)}
        />
      ) : (
        <Output name="plannerGroupName" />
      );
      break;
    // 计划员（通用模块2）
    case 'plannerName':
      component = isEdit ? (
        <EmployeesLov
          name="plannerName"
          disabled={disabled}
          record={record}
          onOk={e => handleChangePlanner(e, record, callback)}
        />
      ) : (
        <Output name="plannerName" />
      );
      break;
    // 设备
    case 'assetLov':
      component = <Output name="assetLov" />;
      if (isEdit && sourceParamType === 'SUB') {
        component = (
          // 通用模块4：委外
          <Lov
            name="assetLov"
            disabled={disabled}
            required={required}
            style={style}
            onChange={lovRecord => handleChangeDescAndLabel(lovRecord, record, moreProps, moreFun)}
          />
        );
      } else if (isEdit && sourceParamType === 'SR') {
        component = (
          // 通用模块3：服务申请
          <Lov
            name="assetLov"
            disabled={disabled}
            required={required}
            style={style}
            onChange={lovRecord => handleChangeAsset(lovRecord, record, moreProps, moreFun)}
          />
        );
      } else {
        component = null;
      }
      break;
    // 设备（工单曾用过，20221026现无处使用）
    // case 'descAndLabel':
    //   component = isEdit ? (
    //     <AssetLov
    //       name={name}
    //       disabled={disabled}
    //       queryParams={{
    //         ...queryParams,
    //         maintSiteId: record.get('maintSiteId'),
    //         assetLocationId: record.get('assetLocationId'),
    //       }}
    //       onOk={lovRecord => handleChangeDescAndLabel(lovRecord, record, moreProps)}
    //       actId={actId}
    //     />
    //   ) : (
    //     <Output name={name} />
    //   );
    //   break;
    // 位置（通用模块5，已适配：服务申请）
    case 'assetLocationName':
      component = isEdit ? (
        <TextField
          name="assetLocationName"
          className="asset-location-field"
          required={required}
          disabled={disabled}
          clearButton={record?.get('assetLocationName')}
          onClear={() => handleChangeLocation(null, record, moreProps, moreFun)}
          suffix={
            <>
              <Icon
                type="search"
                style={{
                  margin: '14px 0 0 ',
                }}
                onClick={() =>
                  providedObjectCode !== 'NOT_ALLOW' &&
                  handleOpenLocTreeModal(record, moreProps, moreFun)
                }
              />
              <Icon
                type="room"
                style={{
                  color: record?.get('assetLocationName') ? '#3889ff' : '#595959',
                  background: providedObjectCode === 'NOT_ALLOW' ? '#f5f5f5' : 'transparent',
                }}
                onClick={() =>
                  record?.get('assetLocationName') && providedObjectCode !== 'NOT_ALLOW'
                    ? handleOpenMapModal(record, moreProps)
                    : false
                }
              />
            </>
          }
        />
      ) : (
        <Output name="assetLocationName" />
      );
      break;
    // 位置（通用模块6，已适配：委外）
    case 'assetLocationLov':
      component = isEdit ? (
        <Lov
          name="assetLocationLov"
          disabled={disabled}
          style={style}
          onChange={e => handleChangeLocation(e, record, moreProps)}
        />
      ) : (
        <Output name="assetLocationLov" />
      );
      break;
    // 服务区域（通用模块7）
    case 'maintSiteLov':
      component = isEdit ? (
        <Lov
          name="maintSiteLov"
          autoSelectSingle
          disabled={disabled}
          style={style}
          onChange={lovRecord =>
            handleChangeMaintSite(lovRecord, record, moreProps, moreFun, callback)
          }
        />
      ) : (
        <Output name="maintSiteLov" />
      );
      break;
    // 计划工期(通用模块10 已适配：点巡检单)
    case 'durationScheduled':
      component = (
        <NumberField
          {...styleProps}
          name={name}
          onChange={value => handleDurationScheduledChange(value, record, isEdit, changeTarget)}
        />
      );
      break;
    // 工期单位(通用模块11 已适配：点巡检单)
    case 'durationUom':
      component = (
        <Select
          {...styleProps}
          name={name}
          onChange={value => handleDurationUomChange(value, record, isEdit, changeTarget)}
        />
      );
      break;
    // 计划开始时间(通用模块8 已适配：点巡检单)
    case 'scheduledStartDate':
      component = (
        <DateTimePicker
          {...styleProps}
          name={name}
          onChange={value => handleScheduledStartDateChange(value, record, isEdit, changeTarget)}
        />
      );
      break;
    // 计划完成时间(通用模块9 已适配：点巡检单)
    case 'scheduledFinishDate':
      component = (
        <DateTimePicker
          {...styleProps}
          name={name}
          onChange={value => handleScheduledFinishDateChange(value, record, isEdit, changeTarget)}
        />
      );
      break;
    // 负责人组（通用模块18 已适配：点巡检单）
    case 'ownerGroupName':
      // 通过record获取服务区域
      component = isEdit ? (
        <EmployeesLov
          newLine={newLine}
          name={name}
          disabled={disabled}
          record={record}
          onOk={e => handleChangeOwnerGroup(e, record, callback)}
          queryParams={queryParams}
        />
      ) : (
        <Output name={name} />
      );
      break;
    // 负责人（通用模块19 已适配：点巡检单）
    case 'ownerName':
      component = isEdit ? (
        <EmployeesLov
          name={name}
          disabled={disabled}
          record={record}
          onOk={e => handleChangeOwner(e, record, callback)}
          queryParams={queryParams}
        />
      ) : (
        <Output name={name} />
      );
      break;
    default:
      break;
  }

  return component;
};

export default CommonComponent;
