import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { getResponse } from '@utils/utils';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.inventory.initial.model';

const tenantId = getCurrentOrganizationId();

const initialDs = () => ({
  primaryKey: 'lineNumber',
  queryUrl: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-customer/list/ui`,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批'),
    },
    {
      name: 'materialCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'APEX_WMS.METHOD.MATERIAL',
      lovPara: { tenantId },
      textField: 'materialCode',
      valueField: 'materialId',
    },
    {
      name: 'revisionCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      multiple: true,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      bind: 'materialCode.materialId',
    },
    {
      name: 'locatorCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位编码'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR_CATEGORY',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
        locatorCategory: ['INVENTORY'],
      },
      textField: 'locatorCode',
      valueField: 'locatorId',
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位编码'),
      bind: 'locatorCode.locatorId',
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      step: 0.01,
      min: 0,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('是否成功'),
      textField: 'meaning',
      valueField: 'typecode',
      options: new DataSet({
        data: [
          {
            meaning: intl.get(`${modelPrompt}.yes`).d('是'),
            typecode: 'Y',
          },
          {
            meaning: intl.get(`${modelPrompt}.no`).d('否'),
            typecode: 'N',
          },
        ],
        selection: 'single',
        autoQuery: true,
      }),
    },
  ],
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('序号'),
    },
    {
      name: 'importMsg',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.importMsg`).d('导入信息'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批'),
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyType`).d('存储类型'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('主计量单位'),
    },
    {
      name: 'secondaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomCode`).d('辅助计量单位'),
    },
    {
      name: 'secondaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomQty`).d('辅助计量单位下的数量'),
    },
    // {
    //   name: 'assemblePointCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点'),
    // },
    // {
    //   name: 'assembleToolCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.assembleToolCode`).d('装配器具'),
    // },
    {
      name: 'loadTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadTime`).d('生效时间'),
      align: 'center',
    },
    {
      name: 'unloadTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unloadTime`).d('失效时间'),
      align: 'center',
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者'),
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
    },
    {
      name: 'ovenNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ovenNumber`).d('炉号'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商'),
    },
    {
      name: 'supplierSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteCode`).d('供应商地点'),
    },
    {
      name: 'customerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户'),
    },
    {
      name: 'customerSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerSiteCode`).d('客户地点'),
    },
    {
      name: 'createReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createReason`).d('创建的原因代码'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('来源EO'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('入库时间'),
      align: 'center',
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结标识'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'inSiteTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inSiteTime`).d('入厂时间'),
      align: 'center',
    },
    {
      name: 'currentContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentContainerCode`).d('当前容器'),
    },
    {
      name: 'productionDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    },
    {
      name: 'expirationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expirationDate`).d('到期日期'),
    },
    {
      name: 'extendedShelfLifeTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.extendedShelfLifeTimes`).d('延保次数'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
  ],
  transport: {
    read: ({ data }) => {
      let queryData = JSON.parse(JSON.stringify(data));
      delete queryData.revisionCodes;
      if (data.revisionCodes.length) {
        queryData = {
          ...queryData,
          revisionCodes: data.revisionCodes.join(','),
        };
      }
      return {
        method: 'GET',
        url: `${
          BASIC.HWMS_BASIC
        }/v1/${getCurrentOrganizationId()}/mt-material-lot/select/import/ui`,
        data: queryData,
      };
    },
    submit: config => {
      return {
        ...config,
        url: `${
          BASIC.HWMS_BASIC
        }/v1/${getCurrentOrganizationId()}/mt-material-lot/save/excel/import/ui`,
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return getResponse(parsedData);
          }
        },
      };
    },
    destroy: config => {
      return {
        ...config,
        url: `${
          BASIC.HWMS_BASIC
        }/v1/${getCurrentOrganizationId()}/mt-material-lot/delete/import/ui`,
      };
    },
  },
});
const searchFormDS = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批'),
    },
    {
      name: 'materialCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'APEX_WMS.METHOD.MATERIAL',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      required: true,
    },
    {
      name: 'locatorCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位编码'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR_CATEGORY',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
        locatorCategory: ['INVENTORY'],
      },
      required: true,
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
      min: 0,
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('是否成功'),
      textField: 'meaning',
      valueField: 'typecode',
      options: new DataSet({
        data: [
          {
            meaning: intl.get(`${modelPrompt}.yes`).d('是'),
            typecode: 'Y',
          },
          {
            meaning: intl.get(`${modelPrompt}.no`).d('否'),
            typecode: 'N',
          },
        ],
        selection: 'single',
        autoQuery: true,
      }),
    },
  ],
  transport: {
    read: config => {
      return {
        ...config,
        method: 'GET',
        url: `${
          BASIC.HWMS_BASIC
        }/v1/${getCurrentOrganizationId()}/mt-mo-actual-dtl-templates/list/ui?sort=message,desc&sort=moActualDetailTemplateId,asc`,
      };
    },
    submit: config => {
      return {
        ...config,
        data: config.data[0],
        url: `${
          BASIC.HWMS_BASIC
        }/v1/${getCurrentOrganizationId()}/mt-material-lot/save/excel/import/ui`,
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return getResponse(parsedData);
          }
        },
      };
    },
    destroy: config => {
      const params = {
        moActualDetailTemplateId: config.data[0].moActualDetailTemplateId,
        batchId: config.data[0].batchId,
      };
      return {
        ...config,
        params,
        url: `${
          BASIC.HWMS_BASIC
        }/v1/${getCurrentOrganizationId()}/mt-mo-actual-dtl-templates/remove/ui`,
      };
    },
  },
});

export { initialDs, searchFormDS };
