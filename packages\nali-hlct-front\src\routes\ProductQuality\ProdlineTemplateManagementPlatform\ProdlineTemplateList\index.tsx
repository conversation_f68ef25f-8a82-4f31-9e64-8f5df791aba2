/**
 * @Description: 检验项目组维护查询界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:29:43
 * @LastEditTime: 2023-05-24 16:56:17
 * @LastEditors: <<EMAIL>>
 */

import React, { useCallback, useEffect, useState } from 'react';
import { DataSet, Menu, Dropdown, Table, Modal, TextArea, Form } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import request from 'utils/request';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { openTab } from 'utils/menuTab';
import queryString from 'query-string';

import { ListTableDS, approveDS } from '../stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.prodlineReview.prodlineReviewTemp';

const ProdlineTemplateList = props => {
  const {
    tableDS,
    approvalDs,
    history,
  } = props;

  // 表格勾选数据
  const [tableSelectList, setTableSelectList] = useState(Array);
  const [tableSelectStatus, setTableSelectStatus] = useState([]);

  const handleTableSelect = ({ dataSet }) => {
    setTableSelectList(dataSet.selected || []);
    const statusList = dataSet.selected.map(item => item.data.status);
    setTableSelectStatus(statusList);
  };

  useDataSetEvent(tableDS, 'select', handleTableSelect);
  useDataSetEvent(tableDS, 'selectAll', handleTableSelect);
  useDataSetEvent(tableDS, 'unselect', handleTableSelect);
  useDataSetEvent(tableDS, 'unselectAll', handleTableSelect);

  useEffect(() => {
    tableDS.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.LIST`,
    );

    if (tableDS?.currentPage) {
      tableDS.query(props.tableDS.currentPage);
    } else {
      tableDS.query();
    }
  }, []);

  const handleAdd = useCallback(() => {
    history.push({
      pathname: '/hwms/prodline_template_management_platform/dist/create',
    });
  }, []);

  // 取消
  const handleUnenabled = () => {
    const ids = tableDS.selected.map(item => item.data.prodlineReviewTmpId);
    return request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-review-tmps/cancel/for/ui`,
      {
        method: 'POST',
        body: ids,
      },
    ).then(res => {
      if (res && !res.failed) {
        notification.success({});
        tableDS.query();
        setTableSelectList([]);
        setTableSelectStatus([]);
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
    // return unenabled.run({
    //   params,
    //   onSuccess: res => {
    //     if (res) {
    //       // @ts-ignore
    //       notification.success();
    //     }
    //   },
    // });
  };

  // 提交
  const handleSubmit = () => {
    const ids = tableDS.selected.map(item => item.data.prodlineReviewTmpId);
    return request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-review-tmps/submit/for/ui`,
      {
        method: 'POST',
        body: ids,
      },
    ).then(res => {
      if (res && !res.failed) {
        notification.success({});
        tableDS.query();
        setTableSelectList([]);
        setTableSelectStatus([]);
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
    // return submitDetail.run({
    //   params,
    //   onSuccess: res => {
    //     if (res) {
    //       // @ts-ignore
    //       notification.success();
    //     }
    //   },
    // });
  };

  // 审核同意
  const handleApprove = () => {
    const ids = tableDS.selected.map(item => item.data.prodlineReviewTmpId);
    return request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-review-tmps/approved/for/ui`,
      {
        method: 'POST',
        body: ids,
      },
    ).then(res => {
      if (res && !res.failed) {
        notification.success({});
        tableDS.query();
        setTableSelectList([]);
        setTableSelectStatus([]);
        // setTableSelectList(Array)
        // setTableSelectStatus([])
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
    // return approval.run({
    //   params,
    //   onSuccess: res => {
    //     if (res) {
    //       // @ts-ignore
    //       notification.success();
    //     }
    //   },
    // });
  };

  // 审核驳回
  const handleunApprove = () => {
    if (tableSelectList.length > 1) {
      notification.error({
        message: intl.get(`${modelPrompt}.notification.selectOneData`).d('不允许批量驳回，驳回只能选择1条数据'),
      });
    } else {
      approvalDs.create({});
      const modal = Modal.open({
        title: intl.get(`${modelPrompt}.title.rejectReason`).d('驳回原因'),
        destroyOnClose: true,
        children: (
          <Form dataSet={approvalDs}>
            <TextArea name="reviewRemarks" required />,
          </Form>
        ),
        // drawer: true,
        onOk: async () => {
          if (!approvalDs.toData()[0].reviewRemarks) {
            notification.error({ message: intl.get(`${modelPrompt}.notification.pleaseInputReason`).d('请输入驳回原因') });
            return false;
          }
          const params = {
            qisProdlineReviewTmpId: tableDS.selected[0].data.prodlineReviewTmpId,
            reviewRemarks: approvalDs.toData()[0].reviewRemarks,
          };
          return request(
            `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-review-tmps/reject/for/ui`,
            {
              method: 'POST',
              body: params,
            },
          ).then(res => {
            if (res && !res.failed) {
              notification.success({});
              tableDS.query();
              modal.close();
              setTableSelectList([]);
              setTableSelectStatus([]);
            } else {
              notification.error({ message: res.message });
              return false;
            }
          });
          // return unApproval.run({
          //   params,
          //   onSuccess: res => {
          //     if (res) {
          //       // @ts-ignore
          //       notification.success();
          //     }
          //   },
          // });
        },
        // onCancel: () => {
        //   headInfoDs.reset();
        // },
      });
    }
  };

  const columns: ColumnProps[] = [
    {
      name: 'prodlineReviewTmpNum',
      align: ColumnAlign.center,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(
                `/hwms/prodline_template_management_platform/dist/${record?.get(
                  'prodlineReviewTmpId',
                )}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'statusMeaning',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewTypeMeaning',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewStageMeaning',
      align: ColumnAlign.center,
    },
    {
      name: 'siteName',
      align: ColumnAlign.center,
    },
    {
      name: 'creationDate',
      align: ColumnAlign.center,
    },
    {
      name: 'createdRealName',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewByRealName',
      align: ColumnAlign.center,
    },
    {
      name: 'reviewDate',
      align: ColumnAlign.center,
    },
  ];

  const menu = (
    <Menu>
      <Menu.Item
        disabled={
          tableSelectList.length === 0 || tableSelectStatus.some(item => item !== 'REVIEWING')
        }
      >
        <a target="_blank" rel="noopener noreferrer" onClick={handleApprove}>
          {intl.get(`${modelPrompt}.menuItem.agree`).d('同意')}
        </a>
      </Menu.Item>
      <Menu.Item
        disabled={
          tableSelectList.length === 0 || tableSelectStatus.some(item => item !== 'REVIEWING')
        }
      >
        <a target="_blank" rel="noopener noreferrer" onClick={handleunApprove}>
          {intl.get(`${modelPrompt}.menuItem.reject`).d('驳回')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const goImport = () => {
    openTab({
      key: `/himp/commentImport/YP.QIS_PROD_LINE_REVIEW_TMP`,
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
      }),
    });
  };

  return (
    <div className="hmes-style" style={{ height: '100%' }}>
      <Header title={intl.get(`${modelPrompt}.title.list`).d('产线审核模板管理平台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          permissionList={[
            {
              code: `hzero.tarzan.hlct.jyzx.prodline_template_management_platform.ps.button.cancel`,
              type: 'button',
              meaning: '取消',
            },
          ]}
          disabled={
            tableSelectList.length === 0 || tableSelectStatus.some(item => item === 'REVIEWING')
          }
          onClick={handleUnenabled}
        >
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </PermissionButton>
        <Dropdown overlay={menu}>
          <PermissionButton
            disabled={
              tableSelectList.length === 0 || tableSelectStatus.some(item => item !== 'REVIEWING')
            }
            type="c7n-pro"
            color={ButtonColor.primary}
            permissionList={[
              {
                code: `hzero.tarzan.hlct.jyzx.prodline_template_management_platform.ps.button.check`,
                type: 'button',
                meaning: '审批按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.review`).d('审批')}
          </PermissionButton>
        </Dropdown>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          permissionList={[
            {
              code: `hzero.tarzan.hlct.jyzx.prodline_template_management_platform.ps.button.submit`,
              type: 'button',
              meaning: '提交按钮',
            },
          ]}
          disabled={tableSelectList.length === 0 || tableSelectStatus.some(item => item !== 'NEW')}
          onClick={handleSubmit}
        >
          {intl.get(`${modelPrompt}.button.submit`).d('提交')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `hzero.tarzan.hlct.jyzx.prodline_template_management_platform.ps.button.new`,
              type: 'button',
              meaning: '新建按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.create`).d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `hzero.tarzan.hlct.jyzx.prodline_template_management_platform.ps.button.export`,
              type: 'button',
              meaning: '导入',
            },
          ]}
          onClick={goImport}
        >
          {intl.get(`${modelPrompt}.button.import`).d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDS}
          columns={columns}
          searchCode="ProdlineTemplateManagementPlatform"
          customizedCode="ProdlineTemplateManagementPlatform"
        />
        ,
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [
    'tarzan.common',
    modelPrompt,
  ],
})(
  withProps(
    () => {
      const tableDS = new DataSet({
        ...ListTableDS(),
      });
      const approvalDs = new DataSet({
        ...approveDS(),
      });
      return {
        tableDS,
        approvalDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ProdlineTemplateList),
);
