/**
 * @Description: 体系审核管理维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { DataSet, Tabs, Button } from 'choerodon-ui/pro';
import { TabsType } from 'choerodon-ui/lib/tabs/enum';
import { } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { getCurrentUser } from 'utils/utils';

import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import uuid from 'uuid/v4';
import notification from 'utils/notification';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import AuditTab1Information from '../components/AuditTab1Information';
import AuditTab2Group from '../components/AuditTab2Group';
import AuditTab3Schedule from '../components/AuditTab3Schedule';
import AuditTab4Record from '../components/AuditTab4Record';
import AuditTab5Problem from '../components/AuditTab5Problem';
import AuditTab6Close from '../components/AuditTab6Close';

import { auditInformationDS } from '../stores/AuditTab1InformationDS';
import { auditGroupFormDS, auditGroupTableDS } from '../stores/AuditTab2GroupDS';
import { auditScheduleFormDS, auditScheduleTableDS } from '../stores/AuditTab3ScheduleDS';

import {
  fetchAuditInformationConfig,
  fetchAuditGroupConfig,
  fetchAuditScheduleConfig,
  commitOaSubmitConfig,
  commitAuditProblemConfig,
} from '../services';

const modelPrompt = 'tarzan.systemAudit';

const { TabPane } = Tabs;

const tabsMap = [
  {
    number: 1,
    key: 'AuditTab1Information',
    titleKey: 'AuditTab1Information',
    titleDesc: intl.get(`${modelPrompt}.AuditTab1Information`).d('审核信息'),
  },
  {
    number: 2,
    key: 'AuditTab2Group',
    titleKey: 'AuditTab2Group',
    titleDesc: intl.get(`${modelPrompt}.AuditTab2Group`).d('审核小组'),
  },
  {
    number: 3,
    key: 'AuditTab3Schedule',
    titleKey: 'AuditTab3Schedule',
    titleDesc: intl.get(`${modelPrompt}.AuditTab3Schedule`).d('审核日程'),
  },
  {
    number: 4,
    key: 'AuditTab4Record',
    titleKey: 'AuditTab4Record',
    titleDesc: intl.get(`${modelPrompt}.AuditTab4Record`).d('审核记录'),
  },
  {
    number: 5,
    key: 'AuditTab5Problem',
    titleKey: 'AuditTab5Problem',
    titleDesc: intl.get(`${modelPrompt}.AuditTab5Problem`).d('审核问题'),
  },
  {
    number: 6,
    key: 'AuditTab6Close',
    titleKey: 'AuditTab6Close',
    titleDesc: intl.get(`${modelPrompt}.AuditTab6Close`).d('审核关闭'),
  },
];

const SystemAuditTable = props => {
  const {
    match: {
      path,
      params: { id },
    },
    history,
  } = props;

  const [user] = useState(getCurrentUser()); // 用户详细信息

  const [problemStatus, setProblemStatus]: any = useState();

  // 提交审批
  const commitOaSubmit = useRequest(commitOaSubmitConfig(), {
    manual: true,
    needPromise: true,
  });

  // 查询审核信息
  const fetchAuditInformation = useRequest(fetchAuditInformationConfig(), {
    manual: true,
    needPromise: true,
  });
  // 查询审核小组
  const fetchAuditGroup = useRequest(fetchAuditGroupConfig(), { manual: true, needPromise: true });
  // 查询审核日程
  const fetchAuditSchedule = useRequest(fetchAuditScheduleConfig(), {
    manual: true,
    needPromise: true,
  });

  // 提交审核问题
  const commitAuditProblem = useRequest(commitAuditProblemConfig(), {
    manual: true,
    needPromise: true,
  });

  // pub 路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);

  // 审核信息的ds
  const auditInformationDs = useMemo(() => new DataSet(auditInformationDS()), []);
  // 审核日程的ds
  const auditScheduleFormDs = useMemo(() => new DataSet(auditScheduleFormDS()), []);
  const auditScheduleTableDs = useMemo(() => new DataSet(auditScheduleTableDS()), []);

  // 编辑状态
  const [canEdit, setCanEdit] = useState(false);

  // 当前tabs key
  const [activeKey, setActiveKey] = useState('AuditTab1Information');

  // 状态
  const [sysReviewPlanStatus, setSysReviewPlanStatus] = useState('');

  // tabs 列表
  const [tabsList, setTabsList]: any = useState([]);

  // 审核小组的数据
  const [groupDataList, setGroupDataList]: any = useState([]);

  // 保存原始数据
  const [threeResList, setthreeResList]: any = useState([]);

  // 初始化页面
  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      setTabsList(tabsMap.slice(0, 1));
    } else {
      initPageData('AuditTab1Information');
    }
  }, [id]);

  // 初始化页面
  const initPageData = key => {
    setCanEdit(false);
    // 需要根据状态判断有几个tab
    setActiveKey(key || 'AuditTab1Information');

    getThreeInfo();
  };

  const getThreeInfo = async () => {
    if (id !== 'create') {
      const threeRes = await Promise.all([
        fetchAuditInformation.run({
          params: { sysReviewPlanId: id },
        }),
        fetchAuditGroup.run({
          params: { sysReviewPlanId: id },
        }),
        fetchAuditSchedule.run({
          params: { sysReviewPlanId: id },
        }),
      ]);

      setthreeResList(threeRes);

      const _sysReviewPlanStatus = threeRes[0]?.rows?.sysReviewPlanStatus || '';

      // 从审核信息内容保存审核状态 设置 tabs显示内容
      setSysReviewPlanStatus(_sysReviewPlanStatus);
      if (threeRes[2]?.rows?.reviewScheduleList?.length > 0) {
        setTabsList(tabsMap.slice(0, 6));
      } else if (threeRes[1]?.rows?.length > 0) {
        setTabsList(tabsMap.slice(0, 3));
      } else {
        setTabsList(tabsMap.slice(0, 2));
      }

      // 审核信息内容初始化
      initAuditInformation(threeRes[0]);
      // 审核小组内容初始化
      initAuditGroup(threeRes[1]);
      // 审核日程内容初始化
      initAuditSchedule(threeRes[2]);
    } else {
      setthreeResList([]);

      initAuditInformation(null);
      initAuditGroup([]);
      initAuditSchedule(null);
    }
  };

  const initAuditInformation = async res => {
    if (res?.success) {
      auditInformationDs.loadData([res?.rows || {}]);
    } else {
      auditInformationDs.loadData([{}]);
    }
  };

  const initAuditGroup = async res => {
    if (res?.success) {
      setGroupDataList(
        (res?.rows || []).map(item => {
          const { groupMem, ...data } = item;
          const groupFormDs = new DataSet(auditGroupFormDS());
          const groupTableDs = new DataSet(auditGroupTableDS());
          groupFormDs.loadData([data || {}]);
          groupTableDs.loadData(groupMem || []);
          return {
            uuid: uuid(),
            formDs: groupFormDs,
            tableDs: groupTableDs,
          };
        }),
      );
    } else {
      setGroupDataList([]);
    }
  };

  const initAuditSchedule = async res => {
    if (res?.success) {
      const { reviewScheduleList, ...data } = res?.rows || {};
      auditScheduleFormDs.loadData([data || {}]);
      auditScheduleTableDs.loadData(
        reviewScheduleList.map(item => {
          const { scheduleTimeFrom, scheduleTimeTo, ...other } = item;
          return {
            ...other,
            scheduleTime: {
              scheduleTimeFrom,
              scheduleTimeTo,
            },
            memberInfo: (item.memberInfo || '').split(','),
            beRevDeptPer: (item.beRevDeptPer.toString() || '').split(','),
            beRevDeptPerName: (item.beRevDeptPerName || '').split(','),
          };
        }) || [],
      );
    } else {
      auditScheduleFormDs.loadData([{}]);
      auditScheduleTableDs.loadData([]);
    }
  };

  const handleSubmitApproval = async () => {
    const res = await commitOaSubmit.run({
      queryParams: { sysReviewPlanId: id },
    });
    if (res?.success) {
      // @ts-ignore
      notification.success();
    }
    initPageData(activeKey);
    return true;
  };

  const getAuthLeader = () => {
    const memberInfoIds: any = [];

    if (threeResList[2] && threeResList[2].rows?.reviewScheduleList) {
      const reviewScheduleList = threeResList[2].rows.reviewScheduleList || [];
      reviewScheduleList.forEach(item => {
        if (item.memberInfoId?.length > 0) {
          memberInfoIds.push(item.memberInfoId[0]);
        }
      });
    }
    return memberInfoIds.includes(user.id) && problemStatus === 'DRAFT';
  };

  const submitAuth = () => {
    const [res1, res2, res3] = threeResList;
    const res1Auth = res1?.success;
    let res2Auth = false;
    if (res2?.success && res2.rows?.length > 0) {
      res2Auth = true;
    }
    let res3Auth = false;
    if (res3?.success && res3.rows?.reviewScheduleList?.length > 0) {
      res3Auth = true;
    }
    return (
      res1Auth &&
      res2Auth &&
      res3Auth &&
      !canEdit &&
      (sysReviewPlanStatus === 'NEW' || sysReviewPlanStatus === 'REJECTED')
    );
  };

  const problemSubmit = async () => {
    const res = await commitAuditProblem.run({
      queryParams: {
        sysReviewPlanId: id,
      },
    });

    if (res?.success) {
      // @ts-ignore
      notification.success();
    }
  };

  const componentsProps: any = {
    id,
    canEdit,
    setCanEdit,
    activeKey,
    history,
    initPageData,
    sysReviewPlanStatus,
    commitOaSubmit,
    threeResList,
    pubFlag,
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.SystemAuditMaintenance`).d('体系审核管理维护')}
        backPath={pubFlag ? '' : '/hwms/system-audit/list'}
      >
        {!pubFlag && (
          <Button
            disabled={!submitAuth()}
            color={ButtonColor.primary}
            onClick={handleSubmitApproval}
          >
            {intl.get(`${modelPrompt}.button.submitApproval`).d('提交审批')}
          </Button>
        )}
        {activeKey === 'AuditTab5Problem' && !pubFlag && (
          <Button disabled={!getAuthLeader()} color={ButtonColor.primary} onClick={problemSubmit}>
            {
              intl.get(`${modelPrompt}.problemSubmit`).d('审核问题提交')
            }
          </Button>
        )}
        <ApprovalInfoDrawer
          objectTypeList={['QIS_SYS_REVIEW_PLAN_ISSUE', 'QIS_SYS_REVIEW_PLAN_CLOSE']}
          objectId={id}
        />
      </Header>
      <Content>
        <Tabs
          type={TabsType.card}
          defaultActiveKey="AuditTab1Information"
          activeKey={activeKey}
          onChange={tabkey => {
            setActiveKey(tabkey);
          }}
        >
          {tabsList &&
            tabsList.length &&
            tabsList.map(item => (
              <TabPane
                disabled={canEdit && activeKey !== item.key}
                title={intl.get(`${modelPrompt}.${item.titleKey}`).d(item.titleDesc)}
                key={item.key}
              >
                {item.key === 'AuditTab1Information' && (
                  <AuditTab1Information
                    {...componentsProps}
                    auditInformationDs={auditInformationDs}
                    fetchAuditInformation={fetchAuditInformation}
                  />
                )}
                {item.key === 'AuditTab2Group' && (
                  <AuditTab2Group
                    {...componentsProps}
                    groupDataList={groupDataList}
                    setGroupDataList={setGroupDataList}
                  />
                )}
                {item.key === 'AuditTab3Schedule' && (
                  <AuditTab3Schedule
                    {...componentsProps}
                    groupDataList={groupDataList}
                    auditScheduleFormDs={auditScheduleFormDs}
                    auditScheduleTableDs={auditScheduleTableDs}
                  />
                )}
                {item.key === 'AuditTab4Record' && <AuditTab4Record {...componentsProps} />}
                {item.key === 'AuditTab5Problem' && (
                  <AuditTab5Problem
                    {...componentsProps}
                    setProblemStatus={setProblemStatus}
                    commitAuditProblem={commitAuditProblem}
                  />
                )}
                {item.key === 'AuditTab6Close' && <AuditTab6Close {...componentsProps} />}
              </TabPane>
            ))}
        </Tabs>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(SystemAuditTable);
