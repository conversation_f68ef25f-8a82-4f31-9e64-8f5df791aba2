import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';

const modelPrompt = 'receiptInspectionPercent';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  queryFields: [
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备'),
      lovCode: 'APRS.AAFM.ASSET_PRODUCT',
      textField: 'assetName',
      lovPara: {
        tenantId,
        assetSetNum: "ProductioneQuipment",
      },
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'assetIds',
      bind: 'equipmentLov.assetId',
    },
    {
      name: 'startDate',
      type: FieldType.month,
      label: intl.get(`${modelPrompt}.startDate`).d('查询时间从'),
      max: 'endDate',
      defaultValue: moment(moment().format('YYYY-MM')),
      required: true,
    },
    {
      name: 'endDate',
      type: FieldType.month,
      label: intl.get(`${modelPrompt}.endDate`).d('查询时间至'),
      min: 'startDate',
      defaultValue: moment(moment().format('YYYY-MM')),
      required: true,
    },
    {
      name: 'timePeriod',
      type: FieldType.string,
      lookupCode: 'APRS.AAFM.ASSET_OEE_TIME_PERIOD',
      defaultValue: 'DAY',
      label: intl.get(`${modelPrompt}.timePeriod`).d('查询维度'),
    },
  ],
  fields: [
    {
      name: 'assetNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'assetDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备描述'),
    },
  ],
});

// lineChart
const lineChartDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备'),
      lovCode: 'APRS.AAFM.ASSET_PRODUCT',
      textField: 'assetName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'assetIds',
      bind: 'equipmentLov.assetId',
    },
    {
      name: 'startDate',
      type: FieldType.month,
      label: intl.get(`${modelPrompt}.startDate`).d('查询时间从'),
      max: 'endDate',
      defaultValue: moment(moment().format('YYYY-MM')),
      required: true,
    },
    {
      name: 'endDate',
      type: FieldType.month,
      label: intl.get(`${modelPrompt}.endDate`).d('查询时间至'),
      min: 'startDate',
      defaultValue: moment(moment().format('YYYY-MM')),
      required: true,
    },
    {
      name: 'timePeriod',
      type: FieldType.string,
      lookupCode: 'APRS.AAFM.ASSET_OEE_TIME_PERIOD',
      label: intl.get(`${modelPrompt}.timePeriod`).d('查询维度'),
      defaultValue: 'DAY',
    },
  ],
});

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'timeActivationRateStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.timeActivationRate`).d('时间开动率'),
    },
    {
      name: 'performanceEfficiencyStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.performanceEfficiency`).d('性能开动率'),
    },
    {
      name: 'qualityQualificationRateStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityQualificationRate`).d('质量合格率'),
    },
  ],
});



export { tableDS, lineChartDS, detailDS };
