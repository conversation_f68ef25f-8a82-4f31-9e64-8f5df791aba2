/**
 * @Description: 区域维护-列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-02-05 11:29:44
 * @LastEditTime: 2023-05-18 11:28:32
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import AreaListDS from '../stores/AreaListDS';

const modelPrompt = 'tarzan.model.org.area';

const AreaList = props => {
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;

  useEffect(() => {
    tableDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.AREA_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.AREA_LIST.LIST`)
    tableDs.query(props.tableDs.currentPage);
  }, []);

  const columns = [
    {
      name: 'areaCode',
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(`/hmes/organization-modeling/area/dist/${record.data.areaId}`);
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'areaName',
    },
    {
      name: 'description',
    },
    {
      name: 'areaType',
      align: 'center',
    },
    {
      name: 'areaCategory',
      align: 'center',
    },
    {
      name: 'enableFlag',
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  const handleCreate = () => {
    props.history.push('/hmes/organization-modeling/area/dist/create');
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.componentName`).d('区域维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.AREA_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.AREA_LIST.LIST`,
          },
          <Table
            queryBar='filterBar'
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="AreaList"
            customizedCode="AreaList"
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.model.org.area', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...AreaListDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.AREA_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.AREA_LIST.LIST`] }),
)(AreaList);
