/**
 * @Description: 检验单维护 - 详情页
 * @Author: <EMAIL>
 * @Date: 2023/2/13 10:44
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Button,
  DataSet,
  DatePicker,
  DateTimePicker,
  Form,
  Lov,
  Modal,
  NumberField,
  Select,
  Switch,
  Table,
  Dropdown,
  Menu,
  TextField,
  Spin,
  Icon,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm, Radio } from 'choerodon-ui';
import request from 'utils/request';
import { DataSetStatus, FieldType } from 'choerodon-ui/dataset/data-set/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Action } from 'choerodon-ui/es/trigger/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import moment from 'moment/moment';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { C7nFormItemSort, AttributeDrawer } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import scanImg from '@/assets/icons/scan-o.svg';
import InspectTaskComponent from './InspectTaskComponent';
import NcAndDefectComponent from './NcAndDefectComponent';
import { drawerDS } from '../stores/index';
import { inspectDocDtlDS, scanFormDS, sourceDocDS } from '../stores/InspectDocDtlDS';
import { taskInfoDS } from '../stores/InspectTaskDtlDS';
import { inspectionReportDS } from '../stores';
import {
  GetAllTaskList,
  SaveInspectDoc,
  CancelInspectDoc,
  GetDefaultSite,
  ScanInspectObject,
  SubmitInspect,
  AuditReview,
  GetFunctionList,
  CompletedInspectDoc,
} from '../services';
import styles from './index.module.less';

import { ncCodeDS, defectFormDS, defectLineDS } from '../stores/NcAndDefectCodeDS';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.inspectDocMaintain';
const tenantId = getCurrentOrganizationId();

const InspectDocDetail = props => {
  const {
    history,
    match: { path, params },
    location: { state },
    custConfig,
    customizeForm,
    customizeTable,
  } = props;
  const kid = params.id;
  const inspectionPlatformFlag = state?.inspectionPlatformFlag;

  const [canEdit, setCanEdit] = useState(kid === 'create');
  const [docStatus, setDocStatus] = useState('');
  const [scanObjectType, setScanObjectType] = useState(''); // 扫描报检对象类型
  const [originalTypeLovShow, setOriginalTypeLovShow] = useState(false); // 扫描报检对象类型
  // 基础信息&实绩信息切换
  const [pageSwitch, setPageSwitch] = useState('left');
  const [deleteFlag, setDeleteFlag] = useState(false);

  // 是否处置状态
  const [disposalStatus, setDisposalStatus] = useState(false);
  const [completedButton, setCompletedButton] = useState(true); // 是否都完成状态
  const [auditButton, setAuditButton] = useState(true); // 处置审核按钮是否可点击
  const [editButton, setEditButton] = useState(false); // 编辑按钮是否可点击

  const [defectLineDss, setDefectLineDss] = useState<any>([]);

  const [functionList, setFunctionList] = useState([]);

  const inspectInfoDs = useMemo(
    () =>
      new DataSet({
        ...drawerDS(),
        paging: false,
        transport: undefined,
      }),
    [],
  );

  const inspectionReportDs = useMemo(()=> new DataSet(inspectionReportDS()),[]);

  const sourceDocDs = useMemo(() => new DataSet(sourceDocDS()), []);

  const scanFormDs = useMemo(() => new DataSet(scanFormDS()), []);

  const detailDs = useMemo(
    () =>
      new DataSet({
        ...inspectDocDtlDS(),
        children: {
          inspectInfoList: sourceDocDs,
          inspectInfoDtlList: inspectInfoDs,
          scanFormInfoList: scanFormDs,
        },
      }),
    [],
  );
  const taskInfoDs = useMemo(() => new DataSet(taskInfoDS()), []);

  const ncCodeDs = useMemo(() => new DataSet(ncCodeDS()), []);
  const defectRecordsDs = useMemo(() => new DataSet(defectFormDS()), []);

  const { run: saveInspectDoc, loading: saveLoading } = useRequest(SaveInspectDoc(), {
    manual: true,
  });
  const { run: cancelInspectDoc, loading: cancelLoading } = useRequest(CancelInspectDoc(), {
    manual: true,
  });
  const { run: completedInspectDoc, loading: completedLoading } = useRequest(CompletedInspectDoc(), {
    manual: true,
  });
  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });
  const { run: scanInspectObject, loading: scanLoading } = useRequest(ScanInspectObject(), {
    manual: true,
    needPromise: true,
  });

  const { run: submitInspect, loading: submitLoading } = useRequest(SubmitInspect(), {
    manual: true,
  });

  const { run: auditReview, loading: reviewLoading } = useRequest(AuditReview(), {
    manual: true,
  });

  const { run: getFunctionList, loading: getFunctionListLoading } = useRequest(GetFunctionList(), {
    manual: true,
    needPromise: true,
  });

  const { run: getAllTaskList } = useRequest(GetAllTaskList(), {
    manual: true,
    needPromise: true,
  });

  const scanInspectCodeRef = useRef<any>(null);

  useEffect(() => {
    if (kid === 'create' || deleteFlag) {
      getDefaultSite({
        onSuccess: res => {
          if (res?.siteId) {
            detailDs.current?.set('siteLov', res);
            scanFormDs.current?.init('siteId', res.siteId);
          }
        },
      });
      sourceDocDs.setState('docCreateMethod', detailDs.current?.get('docCreateMethod'));
      scanFormDs.setState('docCreateMethod', detailDs.current?.get('docCreateMethod'));
      return;
    }
    // 编辑时
    initPage(kid);
  }, [kid, deleteFlag]);

  const initPage = id => {
    handleQueryDetail(id);
  };

  const handleQueryDetail = id => {
    detailDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.BASIC, ${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.BASIC_HIS`,
    );
    detailDs.setQueryParameter('inspectDocId', id);
    detailDs.query().then(res => {
      scanFormDs.current?.init('siteId', detailDs.current?.get('siteId'));
      scanFormDs.current?.init('materialId', detailDs.current?.get('materialId'));
      scanFormDs.current?.init('revisionCode', detailDs.current?.get('revisionCode'));
      scanFormDs.current?.init('inspectBusinessType', detailDs.current?.get('inspectBusinessType'));
      setDocStatus(detailDs.current?.get('inspectDocStatus'));
      setOriginalTypeLovShow(detailDs.current?.get('inspectBusinessType') === 'EXC-QC');

      // 提交按钮判断(编辑按钮同逻辑)
      if (res.reviewType === 'NO_REVIEW') {
        if (res.taskSplitMethod === 'SPLIT_BY_GROUP' && res.inspectDocStatus !== 'LAST_COMPLETED') {
          setEditButton(false);
        } else {
          setEditButton(true);
        }
      } else if (res.reviewType === 'MANUAL_REVIEW' || res.reviewType === 'AUTO_REVIEW') {
        if (
          res.taskSplitMethod === 'SPLIT_BY_GROUP' &&
          res.inspectDocStatus !== 'CANCEL' &&
          (res.reviewStatus === '' || res.reviewStatus === null)
        ) {
          setEditButton(false);
        } else {
          setEditButton(true);
        }
      } else {
        setEditButton(true);
      }

      // 处置审核按钮判断
      if (
        res.disposalReviewFlag === 'Y' &&
        res.reviewType === 'MANUAL_REVIEW' &&
        res.autoCreateReportFlag === 'N' &&
        res.reviewStatus === 'UNDISPOSAL' &&
        res.lastInspectResult === 'NG'
      ) {
        setAuditButton(false);
      } else {
        setAuditButton(true);
      }
    });
    handleQueryTaskDetail(id);
    handleQueryNcDetail(id);
  };

  const handleQueryNcDetail = async id => {
    ncCodeDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.NC_RECORD`,
    );
    ncCodeDs.setQueryParameter('inspectDocId', id);
    ncCodeDs.query();

    defectRecordsDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL_LINE`,
    );
    defectRecordsDs.setQueryParameter('inspectDocId', id);

    let _functionList = await getFunctionList({
      params: {
        inspectDocId: id,
        tenantId,
      },
    });

    if (_functionList?.success) {
      _functionList = _functionList.rows || [];
      const ___functionList: any = [];

      _functionList.forEach(item => {
        if (item.dispositionFunction === 'PASS') {
          ___functionList.unshift(item);
        } else {
          ___functionList.push(item);
        }
      });
      _functionList = ___functionList;
      setFunctionList(_functionList);
    } else {
      _functionList = [];
    }

    defectRecordsDs.query().then(res => {
      const _defectLineDss: any = [];
      if (res) {
        res.forEach(item => {
          const { disposalDtlList } = item;
          const _defectLineDs = new DataSet(defectLineDS());

          _functionList.forEach(functionItem => {
            _defectLineDs.addField(functionItem.dispositionFunction, {
              name: functionItem.dispositionFunction,
              type: FieldType.number,
              label: `${functionItem.description}数`,
              defaultValue: 0,
              min: 0,
              max: item.disposalObjectQty,
              validator: (value, _, record: any) => {
                // 处置方法为“通过”时直接跳过校验
                if (record?.name === 'PASS') {
                  return true;
                }
                // 当前行除“通过”外其余总数
                let sumQty = 0;
                _functionList.forEach((_functionListSum: any) => {
                  if (_functionListSum?.dispositionFunction !== 'PASS') {
                    sumQty += Number(record.get(_functionListSum?.dispositionFunction) || 0);
                  }
                });

                // 所填总数大于不良行上的数量时报错
                if (sumQty > record.get('disposalObjectQty')) {
                  return intl
                    .get(`${modelPrompt}.validator.disposalFunction`, {
                      functionDesc: functionItem?.description,
                      count: record.get('disposalObjectQty') - sumQty + value,
                    })
                    .d(
                      `${functionItem?.description}项所填数量必须小于等于${record.get(
                        'disposalObjectQty',
                      ) -
                        sumQty +
                        value}`,
                    );
                }
                return true;
              },
            });
          });

          _defectLineDs.loadData(
            disposalDtlList.map(disposalDtlListItem => {
              const _disposalDtlListItem = { ...disposalDtlListItem };
              const { disposalFunctionList } = _disposalDtlListItem;
              if (disposalFunctionList?.length > 0) {
                disposalFunctionList.forEach(disposalFunctionListItem => {
                  _disposalDtlListItem[disposalFunctionListItem.disposalFunction] =
                    disposalFunctionListItem.qty;
                });
              }

              return _disposalDtlListItem;
            }),
          );

          _defectLineDss.push(_defectLineDs);
        });
        setDefectLineDss(_defectLineDss);
      } else {
        setDefectLineDss([]);
      }
    });
    defectRecordsDs.setState('inspectDocId', id);
  };

  const handleQueryTaskDetail = id => {
    taskInfoDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.TASK_BASIC,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.LINE_BASIC`,
    );
    taskInfoDs.setQueryParameter('inspectDocId', id);
    taskInfoDs.query().then(res => {
      if (res?.length > 0) {
        let _flag = false;
        res.forEach(item => {
          if (item.inspectTaskStatus !== 'COMPLETED') {
            _flag = true;
          }
        });
        setCompletedButton(_flag);
      }
    });
  };

  const handleEdit = () => setCanEdit(true);

  const handleCancel = () => {
    if (inspectionPlatformFlag === 'PC') {
      history.push('/hwms/inspection-platform/list');
      return;
    }
    if (inspectionPlatformFlag === 'PC-2') {
      history.push('/hwms/inspection-platform-workshop/list');
      return;
    }
    if (kid === 'create') {
      history.push('/hwms/inspect-doc-maintain/list');
    } else {
      setCanEdit(false);
      initPage(kid);
    }
  };

  const validateDimension = () => {
    return (
      detailDs.current?.get('materialId') ||
      detailDs.current?.get('customerId') ||
      detailDs.current?.get('supplierId') ||
      detailDs.current?.get('processWorkcellId') ||
      detailDs.current?.get('stationWorkcellId') ||
      detailDs.current?.get('operationId') ||
      detailDs.current?.get('areaId') ||
      detailDs.current?.get('prodLineId') ||
      detailDs.current?.get('equipmentId')
    );
  };

  const pageValidate = async () => {
    // 校验无需弹窗的表单
    const defectLineDssValidate = await Promise.all(
      defectLineDss.map(async (validateDsListItem: any) => {
        const itemValidate = await validateDsListItem.validate();
        return itemValidate;
      }),
    );

    const defectRecordsDsValidate = await defectRecordsDs.validate();

    const normalResult = [...defectLineDssValidate, defectRecordsDsValidate].every(val => val);
    return normalResult;
  };

  const handleSave = async createFlag => {
    const validat = await pageValidate();
    if (!validat) {
      return;
    }
    const data = detailDs.current?.toData();

    const defectRecordsData = defectRecordsDs.toData().map((defectRecordsDataItem, index) => {
      const _defectRecordsDataItem: any = { ...defectRecordsDataItem };
      if (defectLineDss[index]) {
        const defectLineDsData = defectLineDss[index].toData().map(defectLineDsDataItem => {
          const _defectLineDsDataItem = { ...defectLineDsDataItem };

          const _disposalFunctionList: any = [];
          functionList.forEach((item: any) => {
            _disposalFunctionList.push({
              disposalFunctionId: item.dispositionFunctionId,
              disposalFunction: item.dispositionFunction,
              description: item.description,
              qty: defectLineDsDataItem[item.dispositionFunction] || 0,
            });
          });

          _defectLineDsDataItem.disposalFunctionList = _disposalFunctionList;

          return _defectLineDsDataItem;
        });
        _defectRecordsDataItem.disposalDtlList = defectLineDsData;
      }
      return _defectRecordsDataItem;
    });
    saveInspectDoc({
      params: {
        baseInfo: {
          ...data,
          shiftDate: detailDs.current?.get('shiftDate')
            ? moment(detailDs.current?.get('shiftDate')).format('yyyy-MM-DD')
            : null,
          inspectInfoList: undefined,
          inspectInfoDtlList: undefined,
          siteLov: undefined,
          materialLov: undefined,
          originalTypeLov: undefined,
          customerLov: undefined,
          supplierLov: undefined,
          processWorkcellLov: undefined,
          stationWorkcellLov: undefined,
          operationLov: undefined,
          areaLov: undefined,
          prodLineLov: undefined,
          locatorLov: undefined,
          equipmentLov: undefined,
          uomLov: undefined,
          inspectorUserLov: undefined,
          shiftTeamLov: undefined,
        },
        inspectInfoList: data?.inspectInfoList,
        inspectInfoDtlList: (data?.inspectInfoDtlList || []).filter(
          item => item.objectType !== 'CONTAINER',
        ),
        inspectDocDisposalList: defectRecordsData,
      },
      onSuccess: res => {
        notification.success({});
        const inspectTaskCodes = res.mtInspectTaskDTO6List.map((i) => i.inspectTaskCode);
        let newInspectTaskIds = [];
        if (createFlag) {
          if (kid === 'create') {
            detailDs.reset();
            sourceDocDs.loadData([]);
            inspectInfoDs.loadData([]);
          } else {
            history.push(`/hwms/inspect-doc-maintain/dist/create`);
          }
        } else if (inspectionPlatformFlag === 'PC') {
          // @ts-ignore
          const promisesList = inspectTaskCodes.map(async (ele) => {
            const params = {
              inspectTaskCode: ele,
              page: 0,
              size: 10,
              tableKey: 'ALL',
            };
            await getAllTaskList({
              params,
              onSuccess: (taskList) => {
                if (taskList?.content?.length > 0) {
                  const tempId = taskList?.content?.filter((q) => q.inspectTaskCode === ele)[0].inspectTaskId;
                  // @ts-ignore
                  newInspectTaskIds = [tempId, ...newInspectTaskIds];
                }
              },
            })
          });

          // 使用 Promise.all 等待所有异步函数执行完毕
          Promise.all(promisesList)
            .then(() => {
              // 这里可以放后续需要执行的代码
              if (newInspectTaskIds?.length > 0) {
                history.push(`/hwms/inspection-platform/dist/${newInspectTaskIds[0]}`);
              } else {
                history.push('/hwms/inspection-platform/list');
              }
            });
        } else if (inspectionPlatformFlag === 'PC-2') {
          if ((res.inspectTaskIds || []).length > 0) {
            history.push(`/hwms/inspection-platform-workshop/dist/${res.inspectTaskIds[0]}`);
          } else {
            history.push('/hwms/inspection-platform-workshop/list');
          }
        } else {
          setCanEdit(false);
          history.push(`/hwms/inspect-doc-maintain/dist/${res.inspectDocId}`);
          initPage(res.inspectDocId);
        }
      },
    });
  };

  const handleValidateAndSave = async (createFlag = false) => {
    let sumQty = 0;
    sourceDocDs.forEach(_record => {
      if (_record.get('quantity')) {
        sumQty += _record.get('quantity');
      }
    });
    detailDs.current?.set('inspectSumQty', sumQty);
    const validateFlag = await detailDs.validate();

    const _inspectSumQty = detailDs.current?.get('inspectSumQty');
    const _okQty = detailDs.current?.get('okQty');
    const _ngQty = detailDs.current?.get('ngQty');
    const _scrapQty = detailDs.current?.get('scrapQty');
    const _inspectDocId = detailDs.current?.get('inspectDocId');

    if (_inspectDocId && _inspectSumQty - _okQty - _ngQty - _scrapQty !== 0) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.inspectSumQtyError`)
          .d('合格数+不合格数+报废数之和需等于报检总数！'),
      });
      return false;
    }
    if (!validateFlag) {
      return false;
    }
    if (!validateDimension()) {
      notification.error({
        message: intl.get(`${modelPrompt}.error.dimension`).d('检验维度不可全部为空，请维护！'),
      });
      return false;
    }
    if (['MAT', 'LOT'].includes(detailDs.current?.get('identifyType')) && !inspectInfoDs?.length) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.inspectInfoRequired`)
          .d('非实物管理填报检验单时至少有一条报检明细数据，请检查！'),
      });
      return false;
    }
    if (docStatus !== 'NEW') {
      handleSave(createFlag);
      return;
    }
    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <p>
          {intl
            .get(`${modelPrompt}.info.save`)
            .d('保存后会下达检验任务，所有字段将不可修改，确认保存？')}
        </p>
      ),
    }).then(button => {
      if (button === 'ok') {
        handleSave(createFlag);
      }
    });
  };

  const handleChangeSite = (val, oldVal) => {
    detailDs.current?.init('inspectBusinessType', undefined);
    if (!oldVal?.siteId) {
      scanFormDs.current?.init('siteId', val.siteId);
      return;
    }
    Modal.confirm({
      title: intl.get(`tarzan.common.title.tips`).d('提示'),
      children: (
        <p>
          {intl
            .get(`${modelPrompt}.info.clearLineData`)
            .d('来源单据/报检信息与报检明细会清空，确定更换站点？')}
        </p>
      ),
    }).then(button => {
      if (button === 'ok') {
        scanFormDs.current?.init('siteId', val?.siteId);
        detailDs.current?.init('inspectBusinessType', undefined);
        detailDs.current?.init('processWorkcellLov', undefined);
        detailDs.current?.init('stationWorkcellLov', undefined);
        detailDs.current?.init('areaLov', undefined);
        detailDs.current?.init('prodLineLov', undefined);
        detailDs.current?.init('materialLov', undefined);
        detailDs.current?.init('revisionCode', undefined);
        sourceDocDs.loadData([]);
        sourceDocDs.create({});
        inspectInfoDs.loadData([]);
        scanFormDs.current?.init('siteId', val?.siteId);
        scanFormDs.current?.init('materialId', undefined);
        scanFormDs.current?.init('revisionCode', undefined);
        scanFormDs.current?.init('inspectBusinessType', undefined);
        handleResetScanInfo();
      } else {
        detailDs.current?.set('siteLov', oldVal);
        scanFormDs.current?.init('siteId', oldVal.siteId);
      }
    });
  };

  const handleChangeBusinessType = value => {
    const data = detailDs.current?.getField('inspectBusinessType')?.getLookupData()?.tag;
    if (value) {
      sourceDocDs.setState('docCreateMethod', data);
      scanFormDs.setState('docCreateMethod', data);
      setOriginalTypeLovShow(value === 'EXC-QC');
    }
    scanFormDs.current?.init('inspectBusinessType', value);
    detailDs.current?.init('docCreateMethod', data);
    detailDs.current?.init('materialLov', undefined);
    detailDs.current?.init('processWorkcellLov', undefined);
    detailDs.current?.init('stationWorkcellLov', undefined);
    detailDs.current?.init('areaLov', undefined);
    detailDs.current?.init('prodLineLov', undefined);
  };

  const handleResetScanInfo = () => {
    setScanObjectType('');
    scanFormDs.current?.init('objectType', undefined);
    scanFormDs.current?.init('scanInspectCode', undefined);
    scanFormDs.current?.init('scanInspectEoLov', undefined);
    scanFormDs.current?.init('scanInspectMatLov', undefined);
  };

  const handleChangeMaterial = value => {
    scanFormDs.current?.init('materialId', value?.materialId);
    scanFormDs.current?.init('revisionCode', undefined);
    sourceDocDs.forEach(record => record.set('quantity', 0));
    inspectInfoDs.loadData([]);
    handleResetScanInfo();
  };

  const handleChangeRevision = value => {
    scanFormDs.current?.init('revisionCode', value);
    sourceDocDs.forEach(record => record.set('quantity', 0));
    inspectInfoDs.loadData([]);
    handleResetScanInfo();
  };

  const handleAddLine = () => {
    let _maxSeq = 0;
    sourceDocDs.forEach((_record: any) => {
      if (_record.get('sequence') > _maxSeq) {
        _maxSeq = _record.get('sequence');
      }
    });
    sourceDocDs.create({
      sequence: Math.floor(_maxSeq / 10) * 10 + 10,
    });
  };

  const deleteRecord = record => {
    const { sourceObjectType, sourceObjectId, sourceObjectLineCode } = record.toData();
    if (sourceObjectType && sourceObjectId) {
      if (
        !['WO', 'CONTAINER'].includes(sourceObjectType) &&
        sourceObjectId &&
        sourceObjectLineCode
      ) {
        removeInspectInfo(sourceObjectType, sourceObjectId, sourceObjectLineCode);
      } else if (['WO', 'CONTAINER'].includes(sourceObjectType)) {
        removeInspectInfo(sourceObjectType, sourceObjectId);
      }
    }
    sourceDocDs.remove(record);
  };

  const removeInspectInfo = (type, removeId, removeLineCode = null) => {
    if (type && removeId && removeLineCode) {
      inspectInfoDs.forEach(_rec => {
        if (
          _rec.get('sourceObjectType') === type &&
          _rec.get('sourceObjectId') === removeId &&
          _rec.get('sourceObjectLineCode') === removeLineCode
        ) {
          inspectInfoDs.remove(_rec);
        }
      });
    } else if (type && removeId && !removeLineCode) {
      inspectInfoDs.forEach(_rec => {
        if (_rec.get('sourceObjectId') === removeId && _rec.get('sourceObjectType') === type) {
          inspectInfoDs.remove(_rec);
        }
      });
    }
  };

  const handleChangeSourceObjectType = (_, oldVal, record) => {
    removeInspectInfo(oldVal, record.get('sourceObjectId'), record?.get('sourceObjectLineCode'));
    record.init('sourceObjectLov', undefined);
    record.init('sourceObjectLineLov', undefined);
    if (oldVal === 'WO') {
      record.init('workOrderLov', undefined);
    }
    return true;
  };

  const validateRepeat = record => {
    const { sourceObjectType, sourceObjectId, sourceObjectLineCode } = record.toData();
    let validateResult: any = true;
    sourceDocDs.forEach(_record => {
      if (
        ['WO', 'CONTAINER'].includes(sourceObjectType) &&
        sourceObjectType === _record?.get('sourceObjectType') &&
        sourceObjectId === _record?.get('sourceObjectId') &&
        record.id !== _record?.id
      ) {
        validateResult = false;
      } else if (
        !['WO', 'CONTAINER'].includes(sourceObjectType) &&
        sourceObjectType === _record?.get('sourceObjectType') &&
        sourceObjectId === _record?.get('sourceObjectId') &&
        sourceObjectLineCode === _record?.get('sourceObjectLineCode') &&
        record.id !== _record?.id
      ) {
        validateResult = false;
      }
    });
    return validateResult;
  };

  const handleChangeSourceObjLov = (val, oldVal, record) => {
    if (!val) {
      record.set('quantity', 0);
    }
    if (!validateRepeat(record)) {
      notification.error({
        message: intl.get(`${modelPrompt}.validation.repeat`).d('来源单据不可重复'),
      });
      record.init('sourceObjectLov', undefined);
      return false;
    }
    const _objectType = record.get('sourceObjectType');
    if (oldVal) {
      let removeId;
      switch (_objectType) {
        case 'WO':
          removeId = oldVal?.workOrderId;
          break;
        case 'CONTAINER':
          removeId = oldVal?.containerId;
          break;
        default:
          removeId = oldVal?.instructionDocId;
      }
      removeInspectInfo(_objectType, removeId, record?.get('sourceObjectLineCode'));
      record.init('sourceObjectLineLov', undefined);
    }
    if (val && _objectType === 'WO') {
      record.set('workOrderLov', val);
    }
  };

  const handleChangeSourceLineLov = (val, record, oldValue) => {
    if (!validateRepeat(record)) {
      notification.error({
        message: intl.get(`${modelPrompt}.validation.repeat`).d('来源单据不可重复'),
      });
      record.init('sourceObjectLineLov', undefined);
      return false;
    }
    if (oldValue) {
      removeInspectInfo(
        record.get('sourceObjectType'),
        record.get('sourceObjectId'),
        oldValue.lineNumber,
      );
    }
    if (val) {
      const { poLineId, poLineNum, poNum, soId, soLineId, soLineNum, soNum } = val;
      if (poNum && poLineNum) {
        record.set('poLineId', poLineId);
        record.set('poLineNum', poLineNum);
        record.set('poNumber', poNum);
        record.set('poNumberAndLine', `${poNum}/${poLineNum}`);

        record.set('soId', null);
        record.set('soLineId', null);
        record.set('soLineNum', null);
        record.set('soNumber', null);
        record.set('soNumContent', null);
      } else if (soNum && soLineNum) {
        record.set('soId', soId);
        record.set('soLineId', soLineId);
        record.set('soLineNum', soLineNum);
        record.set('soNumber', soNum);
        record.set('soNumContent', `${soNum}/${soLineNum}`);

        record.set('poLineId', null);
        record.set('poLineNum', null);
        record.set('poNumber', null);
        record.set('poNumberAndLine', null);
      }
    }
  };

  const sourceDocColumns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <PermissionButton
            type="c7n-pro"
            icon="add"
            disabled={!canEdit || !detailDs.current?.get('siteId') || kid !== 'create'}
            onClick={handleAddLine}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        ),
        align: ColumnAlign.center,
        lock: ColumnLock.left,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteRecord(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              disabled={!canEdit || kid !== 'create'}
              funcType="flat"
              shape="circle"
              size="small"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            />
          </Popconfirm>
        ),
      },
      {
        name: 'sequence',
        align: ColumnAlign.left,
        lock: ColumnLock.left,
        width: 60,
      },
      {
        name: 'sourceObjectType',
        width: 120,
        editor: record =>
          canEdit &&
          kid === 'create' && (
            <Select
              onBeforeChange={(val, oldValue) =>
                handleChangeSourceObjectType(val, oldValue, record)
              }
            />
          ),
      },
      {
        name: 'sourceObjectLov',
        width: 150,
        editor: record =>
          canEdit &&
          kid === 'create' && (
            <Lov onChange={(val, oldValue) => handleChangeSourceObjLov(val, oldValue, record)} />
          ),
        renderer: ({ record }) => record?.get('sourceObjectCode'),
      },
      {
        name: 'sourceObjectLineLov',
        width: 100,
        editor: record =>
          canEdit &&
          kid === 'create' && (
            <Lov onChange={(val, oldValue) => handleChangeSourceLineLov(val, record, oldValue)} />
          ),
        renderer: ({ record }) => record?.get('sourceObjectLineCode'),
      },
      {
        name: 'quantity',
        width: 60,
        editor: canEdit && kid === 'create' && <NumberField />,
      },
      {
        name: 'workOrderLov',
        editor: canEdit && kid === 'create' && <Lov />,
        renderer: ({ record }) => record?.get('woNumber'),
      },
      {
        name: 'soLineLov',
        editor: canEdit && kid === 'create' && <Lov />,
        renderer: ({ record }) => record?.get('soNumContent'),
      },
      {
        name: 'poLineLov',
        editor: canEdit && kid === 'create' && <Lov />,
        renderer: ({ record }) => record?.get('poNumberAndLine'),
      },
      {
        name: 'inspectInfoCode',
        width: 150,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push({
                  pathname: `/hwms/inspection-info-management/list`,
                  state: {
                    inspectInfoCodes: record?.get('inspectInfoCode'),
                  },
                });
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'inspectInfoUserName' },
      {
        name: 'inspectInfoCreationDate',
        align: ColumnAlign.center,
        width: 150,
      },
    ],
    [canEdit],
  );

  const getParentRecord = (currentRecord, parentDataSet, recordFlag = true) => {
    let sourceObjectType;
    let sourceObjectId;
    let sourceObjectLineCode;
    if (recordFlag) {
      sourceObjectType = currentRecord?.get('sourceObjectType');
      sourceObjectId = currentRecord?.get('sourceObjectId');
      sourceObjectLineCode = currentRecord?.get('sourceObjectLineCode');
    } else {
      sourceObjectType =
        currentRecord?.objectType === 'EO' ? 'WO' : currentRecord?.instructionDocType;
      sourceObjectId =
        currentRecord?.objectType === 'EO'
          ? currentRecord?.workOrderId
          : currentRecord?.instructionDocId;
      sourceObjectLineCode = currentRecord?.lineNumber;
    }
    return parentDataSet.find(
      _rec =>
        (['WO', 'CONTAINER'].includes(sourceObjectType) &&
          sourceObjectType === _rec?.get('sourceObjectType') &&
          sourceObjectId === _rec?.get('sourceObjectId')) ||
        (!['WO', 'CONTAINER'].includes(sourceObjectType) &&
          sourceObjectType === _rec?.get('sourceObjectType') &&
          sourceObjectId === _rec?.get('sourceObjectId') &&
          sourceObjectLineCode === _rec?.get('sourceObjectLineCode')),
    );
  };

  const handleDeleteInfo = record => {
    if (inspectInfoDs.length === 1) {
      scanFormDs.reset();
      setScanObjectType('')
      sourceDocDs.loadData([]);
      handleAddLine()
      detailDs.reset();
      setDeleteFlag(true);
    }
    inspectInfoDs.remove(record);
    // 容器类型删除行时同步删除装载对象信息
    if (record.get('objectType') === 'CONTAINER') {
      inspectInfoDs.forEach(_record => {
        if (_record.get('parentContainerCode') === record.get('objectCode')) {
          inspectInfoDs.remove(_record);
        }
      });
    }
    if (!record?.get('sourceObjectType') && !record?.get('sourceObjectCode')) {
      sourceDocDs.current?.set(
        'quantity',
        Number(sourceDocDs.current?.get('quantity')) - Number(record?.get('quantity')),
      );
    } else {
      const parentRecord = getParentRecord(record, sourceDocDs);
      parentRecord?.set(
        'quantity',
        Number(parentRecord.get('quantity')) - Number(record?.get('quantity')),
      );
    }
  };

  const handleChangeDtlQty = (val, oldVal, record) => {
    const { sourceObjectType, sourceObjectCode, sourceObjectLineCode } =
      sourceDocDs.current?.toData() || {};
    const sourceDsFlag =
      sourceDocDs.length > 1 ||
      (sourceDocDs.length === 1 &&
        sourceObjectType &&
        sourceObjectCode &&
        (['WO', 'CONTAINER'].includes(sourceObjectType) || sourceObjectLineCode));
    const parentRecord = getParentRecord(record, sourceDocDs, true);
    if (sourceDsFlag) {
      parentRecord?.set('quantity', Number(parentRecord.get('quantity')) - oldVal + val);
    } else {
      sourceDocDs.current?.set(
        'quantity',
        Number(sourceDocDs.current?.get('quantity')) - oldVal + val,
      );
    }
  };

  const inspectInfoColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        lock: ColumnLock.left,
        align: ColumnAlign.center,
        width: 50,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDeleteInfo(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              disabled={!canEdit || kid !== 'create'}
              funcType="flat"
              shape="circle"
              size="small"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            />
          </Popconfirm>
        ),
      },
      { name: 'sequence', width: 60, lock: ColumnLock.left },
      {
        name: 'objectType',
        width: 120,
        lock: ColumnLock.left,
      },
      {
        name: 'objectCode',
        width: 180,
        lock: ColumnLock.left,
        renderer: ({ record, value }) => {
          if (['MAT', 'LOT'].includes(record?.get('objectType'))) {
            return ``;
          }
          return value;
        },
      },
      {
        name: 'inLocatorTime',
        width: 150,
      },
      {
        name: 'property',
      },
      {
        name: 'identification',
        width: 150,
      },
      {
        name: 'quantity',
        width: 120,
        align: ColumnAlign.right,
        editor: record =>
          canEdit &&
          kid === 'create' &&
          ['MAT', 'LOT'].includes(record.get('objectType')) && (
            <NumberField onChange={(val, oldVal) => handleChangeDtlQty(val, oldVal, record)} />
          ),
      },
      { name: 'uomName', width: 80 },
      { name: 'qualityStatusDesc' },
      { name: 'locatorName' },
      { name: 'lot', width: 100 },
      { name: 'supplierLot', width: 100 },
      { name: 'sourceObjectTypeDesc', width: 150 },
      {
        name: 'sourceObjectCode',
        width: 200,
        renderer: ({ record }) => {
          if (record?.get('sourceObjectLineCode')) {
            return `${record?.get('sourceObjectCode')}/${record?.get('sourceObjectLineCode')}`;
          }
          return record?.get('sourceObjectCode');
        },
      },
      { name: 'inspectInfoCode' },
    ];
  }, [canEdit, kid]);

  const handleUpdateHeaderData = val => {
    detailDs.current?.set('siteId', val.siteId);
    detailDs.current?.set('siteName', val.siteName);
    detailDs.current?.set('materialId', val.materialId);
    scanFormDs.current?.set('materialId', val.materialId);
    detailDs.current?.set('materialName', val.materialName);
    detailDs.current?.set('revisionFlag', val.revisionFlag);
    detailDs.current?.set('revisionCode', val.revisionCode);
    scanFormDs.current?.set('revisionCode', val.revisionCode);
    detailDs.current?.set('customerId', val.customerId);
    detailDs.current?.set('customerName', val.customerName);
    detailDs.current?.set('supplierId', val.supplierId);
    detailDs.current?.set('supplierName', val.supplierName);
    detailDs.current?.set('locatorId', val.locatorId);
    detailDs.current?.set('locatorName', val.locatorName);
    detailDs.current?.set('prodLineId', val.productionLineId);
    detailDs.current?.set('prodLineName', val.productionLineName);
    // detailDs.current?.set('stationWorkcellId', val.workcellId);
    // detailDs.current?.set('stationWorkcellName', val.workcellName);
    detailDs.current?.set('uomId', val.uomId);
    detailDs.current?.set('uomName', val.uomName);
  };

  const handleValidateHeadData = val => {
    const objectType = val.objectType;
    const {
      siteId,
      materialId,
      revisionCode,
      customerId,
      supplierId,
      locatorId,
      prodLineId,
    } = detailDs.current?.toData();
    if (objectType === 'EO') {
      return (
        (siteId && siteId !== val.siteId) ||
        (materialId && materialId !== val.materialId) ||
        (revisionCode && revisionCode !== val.revisionCode) ||
        (prodLineId && prodLineId !== val.productionLineId)
      );
    }
    return (
      (siteId && siteId !== val.siteId) ||
      (materialId && materialId !== val.materialId) ||
      (revisionCode && revisionCode !== val.revisionCode) ||
      (customerId && customerId !== val.customerId) ||
      (supplierId && supplierId !== val.supplierId) ||
      (locatorId && locatorId !== val.locatorId)
    );
  };

  const handleRepeatValidate = val => {
    const findRecord = inspectInfoDs.find(_record => _record.get('objectCode') === val?.objectCode);
    return findRecord !== undefined;
  };

  const handleAddInspectInfo = val => {
    let typeDesc;
    const typeDs = sourceDocDs.getField('sourceObjectType')?.getOptions(sourceDocDs.current);
    typeDs?.forEach(_rec => {
      if (_rec?.get('value') === 'WO') {
        typeDesc = _rec?.get('meaning');
      }
    });
    inspectInfoDs.create({
      sequence: inspectInfoDs.length * 10 + 10,
      objectType: val.objectType,
      objectTypeDesc: val.objectTypeDesc,
      objectCode: val.objectCode,
      identification: val.identification,
      materialName: val.materialName,
      revisionCode: val.revisionCode,
      identifyType: val.identifyType,
      maxQuantity: val.objectType === 'CONTAINER' ? val.loadQty : val.quantity,
      quantity: val.objectType === 'CONTAINER' ? val.loadQty : val.quantity,
      parentContainerCode: val.parentContainerCode,
      uomId: val.uomId,
      uomName: val.uomName,
      qualityStatus: val.qualityStatus,
      qualityStatusDesc: val.qualityStatusDesc,
      locatorId: val.locatorId,
      locatorName: val.locatorName,
      lot: val.lot,
      supplierLot: val.supplierLot,
      sourceObjectType: val.objectType === 'EO' ? 'WO' : val?.instructionDocType,
      sourceObjectTypeDesc: val.objectType === 'EO' ? typeDesc : val?.instructionDocTypeDesc,
      sourceObjectId: val.objectType === 'EO' ? val?.workOrderId : val?.instructionDocId,
      sourceObjectCode: val.objectType === 'EO' ? val?.workOrderNum : val?.instructionDocNum,
      sourceObjectLineCode: val?.lineNumber,
    });
  };

  const sourceDocObj = val => ({
    sequence: 10,
    sourceObjectType: val.objectType === 'EO' ? 'WO' : val?.instructionDocType,
    sourceObjectId: val.objectType === 'EO' ? val?.workOrderId : val?.instructionDocId,
    sourceObjectCode: val.objectType === 'EO' ? val?.workOrderNum : val?.instructionDocNum,
    workOrderId: val.objectType === 'EO' ? val?.workOrderId : undefined,
    woNumber: val.objectType === 'EO' ? val?.workOrderNum : undefined,
    sourceObjectLineCode: val?.lineNumber,
    quantity: val.quantity,
    soId: val?.soId,
    soNumber: val?.soNumber,
    soLineId: val?.soLineId,
    soLineNum: val?.soLineNumber,
    soNumContent: val?.soNumber ? `${val?.soNumber}/${val?.soLineNumber}` : undefined,
    poId: val?.poId,
    poNumber: val?.poNumber,
    poLineId: val?.poLineId,
    poLineNum: val?.poLineNumber,
    poNumberAndLine: val?.poNumber ? `${val?.poNumber}/${val?.poLineNumber}` : undefined,
  });

  const handleChangeObjectType = val => {
    setDeleteFlag(false);
    setScanObjectType(val);
    scanFormDs.current?.init('scanInspectCode');
    scanFormDs.current?.init('scanInspectEoLov', undefined);
    scanFormDs.current?.init('scanInspectEoLovBatch', undefined);
    scanFormDs.current?.init('scanInspectMatLov', undefined);
  };

  const handleScanInspectCode = () => {
    const val = scanFormDs.current?.get('scanInspectCode');
    const docCreateMethod = detailDs.current?.get('docCreateMethod');
    scanInspectObject({
      params: {
        lovCode: docCreateMethod === 'LES_CREATE' ? 'MT.WMS.EOMATERIALLOT' : 'MT.MES.EOMATERIALLOT',
        inspectBusinessType: detailDs.current?.get('inspectBusinessType'),
        objectCode: val,
        page: 0,
        siteId: detailDs.current?.get('siteId'),
        size: 10,
        tenantId,
      },
    }).then(res => {
      if (res?.message || !res?.length) {
        scanInspectCodeRef?.current?.focus();
        return;
      }
      const objectType = res[0]?.objectType;
      scanFormDs.current?.set('objectType', objectType);
      setScanObjectType(objectType);
      if (['MATERIAL_LOT', 'EO', 'CONTAINER'].includes(objectType)) {
        scanFormDs.current?.set('scanInspectEoLov', res[0]);
      }
      if (['MAT', 'LOT'].includes(objectType)) {
        scanFormDs.current?.set('scanInspectMatLov', res[0]);
      }
      handleScanInspectObj(res[0]);
      scanInspectCodeRef?.current?.focus();
    });
  };

  const handleValidateContainerInfo = containerInfo => {
    const {
      fixBoxFlag, // eo和物料批混装标识
      objectBoxFlag, // 物料和版本是否一致标识
      eoDimissionFlag, // eo维度标识
      materialLotDimissionFlag, // 物料批维度标识
      sourceDocFlag, // 来源单据标识
      materialLotMatFlag, // 实物非实物标识
      materialLotList = [], // 容器内装载的物料批
      eoList = [], // 容器内装载的eo
    } = containerInfo;
    let loadObjectList: any = []; // 容器内装载的对象列表
    if (materialLotList?.length) {
      loadObjectList = materialLotList;
    } else if (eoList?.length) {
      loadObjectList = eoList;
    }
    //  空容器则直接绕过后续校验
    if (!loadObjectList.length) {
      notification.error({
        message: intl.get(`${modelPrompt}.error.loadEmpty`).d('容器未装载EO或物料批，请检查！'),
      });
      return;
    }
    // 先根据后台返回的标识，对容器内的装载物料批或EO做一系列的校验
    if (fixBoxFlag === 'Y') {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.eoMaterialLotMixedLoading`)
          .d('容器物料批和EO混装，请检查！'),
      });
      return;
    }
    if (objectBoxFlag === 'Y') {
      notification.error({
        message: intl.get(`${modelPrompt}.error.materialMixedLoading`).d('容器物料混装，请检查！'),
      });
      return;
    }
    if (eoDimissionFlag === 'Y') {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.eoDimensionMixedLoading`)
          .d('容器下装载的EO维度不同，请检查！'),
      });
      return;
    }
    if (materialLotDimissionFlag === 'Y') {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.materialLotDimensionMixedLoading`)
          .d('容器下装载的物料批维度不同，请检查！'),
      });
      return;
    }
    if (materialLotMatFlag === 'Y') {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.materialMatMixedLoading`)
          .d('容器内装的物料批存在实物与非实物混装，请检查！'),
      });
      return;
    }
    // 容器内部校验通过后，校验装载对象与当前界面上已选的信息是否一致
    if (handleValidateHeadData(loadObjectList[0])) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.headDiff`)
          .d('扫描条码与检验单头信息不一致，请重新扫描！'),
      });
      return;
    }
    const validateRepeatList = [...loadObjectList, { ...containerInfo }];
    let repeatFlag = false;
    for (let i = 0; i < validateRepeatList.length; i++) {
      if (handleRepeatValidate(validateRepeatList[i])) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.exist`)
            .d('所扫描/选择的报检对象已存在，请检查！'),
        });
        repeatFlag = true;
        break;
      }
    }
    if (repeatFlag) {
      return;
    }
    // 来源单据行是否有值(有值时校验是否重复，无值时回填信息)
    // 来源单据行大于一行时认为是有值
    // 来源单据行只有一行，来源单据类型为WO或CONTAINER，且sourceObjectType、sourceObjectCode同时存在时默认为有值
    // 来源单据行只有一行，来源单据类型为其他，且sourceObjectType、sourceObjectCode、sourceObjectLineCode同时存在时默认为有值
    const { sourceObjectType, sourceObjectCode, sourceObjectLineCode } =
      sourceDocDs.current?.toData() || {};
    const sourceDsFlag =
      sourceDocDs.length > 1 ||
      (sourceDocDs.length === 1 &&
        sourceObjectType &&
        sourceObjectCode &&
        (['WO', 'CONTAINER'].includes(sourceObjectType) || sourceObjectLineCode));

    // 非实物标识，非实物情况下不校验来源单据行，也不带出来源单据
    const unPhysicalFlag = ['MAT', 'LOT'].includes(loadObjectList[0].objectType);

    if (sourceDsFlag && !unPhysicalFlag) {
      if (sourceDocFlag === 'Y') {
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.sourceDocMixedLoading`)
            .d('容器下装载对象的来源单据不同，请检查！'),
        });
        return;
      }
      if (
        (loadObjectList[0]?.objectType !== 'EO' &&
          loadObjectList[0]?.instructionDocType &&
          loadObjectList[0]?.instructionDocNum) ||
        (loadObjectList[0]?.objectType === 'EO' && loadObjectList[0]?.workOrderId)
      ) {
        const parentRecord = getParentRecord(loadObjectList[0], sourceDocDs, false);
        if (parentRecord) {
          parentRecord?.set(
            'quantity',
            Number(parentRecord.get('quantity')) + Number(containerInfo.loadQty),
          );
          // 将container加到报检信息明细里
          handleAddInspectInfo(containerInfo);
          // 将container下的装载对象加到报检信息明细里
          loadObjectList.forEach(item => {
            handleAddInspectInfo({ ...item, parentContainerCode: containerInfo.objectCode });
          });
          handleUpdateHeaderData(loadObjectList[0]);
        } else {
          notification.error({
            message: intl
              .get(`${modelPrompt}.error.underDoc`)
              .d('所扫描/选择的报检对象必须关联在所选的来源单据下，请检查！'),
          });
        }
      } else {
        // 来源单据行有数据，但扫描的对象无来源单据类型和来源单据编码时，报错
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.underDoc`)
            .d('所扫描/选择的报检对象必须关联在所选的来源单据下，请检查！'),
        });
      }
    } else {
      // 将container加到报检信息明细里
      handleAddInspectInfo(containerInfo);
      // 将container下的装载对象加到报检信息明细里
      loadObjectList.forEach(item => {
        handleAddInspectInfo({ ...item, parentContainerCode: containerInfo.objectCode });
      });
      const oldQty = Number(sourceDocDs.current?.get('quantity'));
      sourceDocDs.current?.set('quantity', Number(oldQty) + containerInfo.loadQty);
      handleUpdateHeaderData(loadObjectList[0]);
    }
  };

  const handleScanInspectObjBatch = values => {
    if (values?.length > 0) {
      values.forEach(val => {
        handleScanInspectObj(val);
      });
    }
  };

  const handleScanInspectObj = val => {
    if (!val) {
      return;
    }
    // scanFormDs.current?.init('scanInspectCode');
    // scanFormDs.current?.init('scanInspectEoLov', undefined);
    // scanFormDs.current?.init('scanInspectEoLovBatch', undefined);
    // scanFormDs.current?.init('scanInspectMatLov', undefined);
    if (val.objectType === 'CONTAINER') {
      // 容器的检验逻辑
      handleValidateContainerInfo(val);
      return;
    }
    if (handleValidateHeadData(val)) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.headDiff`)
          .d('扫描条码与检验单头信息不一致，请重新扫描！'),
      });
      return;
    }
    if (handleRepeatValidate(val)) {
      notification.error({
        message: intl.get(`${modelPrompt}.error.exist`).d('所扫描/选择的报检对象已存在，请检查！'),
      });
      return;
    }
    // 来源单据行是否有值(有值时校验是否重复，无值时回填信息)
    // 来源单据行大于一行时认为是有值
    // 来源单据行只有一行，来源单据类型为WO或CONTAINER，且sourceObjectType、sourceObjectCode同时存在时默认为有值
    // 来源单据行只有一行，来源单据类型为其他，且sourceObjectType、sourceObjectCode、sourceObjectLineCode同时存在时默认为有值
    const { sourceObjectType, sourceObjectCode, sourceObjectLineCode } =
      sourceDocDs.current?.toData() || {};
    const sourceDsFlag =
      sourceDocDs.length > 1 ||
      (sourceDocDs.length === 1 &&
        sourceObjectType &&
        sourceObjectCode &&
        (['WO', 'CONTAINER'].includes(sourceObjectType) || sourceObjectLineCode));
    // 非实物标识，非实物情况下不校验来源单据行，也不带出来源单据
    const unPhysicalFlag = ['MAT', 'LOT'].includes(val.objectType);


    if (sourceDsFlag && !unPhysicalFlag) {
      if (
        (val.objectType !== 'EO' && val.instructionDocType && val.instructionDocNum) ||
        (val.objectType === 'EO' && val.workOrderId)
      ) {
        const parentRecord = getParentRecord(val, sourceDocDs, false);
        if (parentRecord) {
          parentRecord?.set(
            'quantity',
            Number(parentRecord.get('quantity')) + Number(val.quantity),
          );
          handleAddInspectInfo(val);
          handleUpdateHeaderData(val);
        } else {
          notification.error({
            message: intl
              .get(`${modelPrompt}.error.underDoc`)
              .d('所扫描/选择的报检对象必须关联在所选的来源单据下，请检查！'),
          });
        }
      } else {
        // 来源单据行有数据，但扫描的对象无来源单据类型和来源单据编码时，报错
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.underDoc`)
            .d('所扫描/选择的报检对象必须关联在所选的来源单据下，请检查！'),
        });
      }
    } else {
      if (
        inspectInfoDs.length &&
        !unPhysicalFlag &&
        ((val.objectType !== 'EO' && val.instructionDocType && val.instructionDocNum) ||
          (val.objectType === 'EO' && val.workOrderId))
      ) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.underDoc`)
            .d('所扫描/选择的报检对象必须关联在所选的来源单据下，请检查！'),
        });
        return;
      }
      handleAddInspectInfo(val);
      if (inspectInfoDs.length === 1 && !unPhysicalFlag) {
        sourceDocDs.loadData([sourceDocObj(val)]);
      } else {
        const oldQty = Number(sourceDocDs.current?.get('quantity'));
        sourceDocDs.current?.set('quantity', Number(oldQty) + val.quantity);
      }
      handleUpdateHeaderData(val);
    }
  };

  const handleCancelInspectDoc = () => {
    cancelInspectDoc({
      params: [detailDs.current?.get('inspectDocId')],
      onSuccess: () => {
        notification.success({});
        initPage(kid);
      },
    });
  };

  const handleCompletedInspectDoc = () => {
    completedInspectDoc({
      params: {inspectDocIds: [detailDs.current?.get('inspectDocId')], status: 'COMPLETED'},
      onSuccess: () => {
        notification.success({});
        initPage(kid);
      },
    });
  };

  const AcceptStandardComponent = props => {
    return (
      <div data-name={props.name} className={styles['accept-standard']}>
        <Form dataSet={detailDs} columns={2} labelWidth={20} useColon={false}>
          <NumberField name="ac" />
          <NumberField name="re" />
        </Form>
      </div>
    );
  };

  const handlePageChange = e => {
    setPageSwitch(e.target.value);
  };

  // 点击提交
  const clickSubmit = () => {
    // debugger
    // 子页面处置数据
    const disposalData: any = defectRecordsDs.toData();

    if (
      detailDs.current?.get('lastInspectResult') === 'OK' ||
      (disposalData[0]?.dispositionType === 'ALL' &&
        disposalData[0]?.dispositionFunction &&
        disposalData[0]?.dispositionFunctionId) ||
      (disposalData[0]?.dispositionType === 'PART' && disposalData[0]?.disposalDtlList?.length > 0)
    ) {
      Modal.open({
        title: '提示',
        children: '提交后所有字段不可修改，确认提交？',
        onOk: () => {
          submitInspect({
            params: {
              inspectDocId: detailDs.current?.get('inspectDocId'),
              reviewType: detailDs.current?.get('reviewType'),
              inspectSumQty: detailDs.current?.get('inspectSumQty'),
            },
            onSuccess: () => {
              notification.success({ message: '提交成功' });
              initPage(kid);
            },
          });
        },
      });
    } else {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.message.disposalRequired`)
          .d('检验结果为不合格时，必须添加处置信息！'),
      });
    }
  };

  // 处置审核选择
  const dispositionAudits = ({ key }) => {
    // eslint-disable-next-line default-case
    switch (key) {
      case 'APPROVING':
        handleAdverseApproval();
        break;
      case 'DISPOSAL':
        handleDirectDisposal();
        break;
      default:
        break;
    }
  };

  // 发起不良审批
  const handleAdverseApproval = () => {
    // console.log('不良审批');
    auditReview({
      params: {
        disposalReviewType: 'REVIEW',
        inspectDocId: detailDs.current?.get('inspectDocId'),
      },
      onSuccess: () => {
        notification.success({ message: '操作成功' });
        handleQueryDetail(kid);
      },
    });
  };

  // 直接处置
  const handleDirectDisposal = () => {
    // console.log('直接处置');
    setDisposalStatus(true);
    setCanEdit(true);
  };

  // 直接处置提交按钮
  const handleNcReviewSubmit = async () => {
    const validat = await pageValidate();
    if (!validat) {
      return;
    }
    Modal.open({
      title: '提示',
      children: '提交后所有字段不可修改，确认提交？',
      onOk: () => {
        const defectRecordsData = defectRecordsDs.toData().map((defectRecordsDataItem, index) => {
          const _defectRecordsDataItem: any = { ...defectRecordsDataItem };
          if (defectLineDss[index]) {
            const defectLineDsData = defectLineDss[index].toData().map(defectLineDsDataItem => {
              const _defectLineDsDataItem = { ...defectLineDsDataItem };

              const _disposalFunctionList: any = [];
              functionList.forEach((item: any) => {
                _disposalFunctionList.push({
                  disposalFunctionId: item.dispositionFunctionId,
                  disposalFunction: item.dispositionFunction,
                  description: item.description,
                  qty: defectLineDsDataItem[item.dispositionFunction] || 0,
                });
              });

              _defectLineDsDataItem.disposalFunctionList = _disposalFunctionList;

              return _defectLineDsDataItem;
            });
            _defectRecordsDataItem.disposalDtlList = defectLineDsData;
          }
          return _defectRecordsDataItem;
        });

        auditReview({
          params: {
            disposalReviewType: 'DISPOSAL',
            inspectDocId: detailDs.current?.get('inspectDocId'),
            disposalList: defectRecordsData,
          },
          onSuccess: () => {
            notification.success({ message: '操作成功' });
            handleQueryDetail(kid);
          },
        });
        setDisposalStatus(false);
        setCanEdit(false);
      },
    });
  };

  // 直接处置取消按钮
  const handleNcReviewSubmitCancel = () => {
    setDisposalStatus(false);
    setCanEdit(false);
  };

  const backPathUrl = () => {
    if (inspectionPlatformFlag === 'PC') {
      return '/hwms/inspection-platform/list'
    }
    if (inspectionPlatformFlag === 'PC-2') {
      return '/hwms/inspection-platform-workshop/list'
    }
    return '/hwms/inspect-doc-maintain/list'
  }

  const handleDocCreateMethod = value => {
    sourceDocDs.setState('docCreateMethod', value);
    scanFormDs.setState('docCreateMethod', value);
  }

  const handleInspectionReport = () => {
    Modal.open({
      title: '原材料检验报告',
      maskClosable: false, // 点击蒙层是否允许关闭
      keyboardClosable: false, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      children: (
        <Form columns={1} dataSet={inspectionReportDs}>
          <NumberField name='boxesReceivedQty' />
          <NumberField name='boxesSampledQty' />
        </Form>
      ),
      onOk: handleReportOk,
      onCancel:()=>{inspectionReportDs.reset()},
    })
  }

  const handleReportOk = () => {
    request(`${BASIC.HRPT_COMMON}/v1/${tenantId}/reports`,{
      method: "GET",
      query: {
        reportCode: 'APEX.RAW_MATERIAL_RPT',
      },
    }).then(res => {
      if(res?.content){
        const reportUuid = res.content[0].reportUuid;
        const params:any = {
          ...inspectionReportDs.toData()[0],
          inspectDocId: detailDs.current?.get('inspectDocId'),
        }
        if(reportUuid){
          request(`${BASIC.HRPT_COMMON}/v1/${tenantId}/reports/export/${reportUuid}/PRINT?boxesReceivedQty=${params.boxesReceivedQty}&boxesSampledQty=${params.boxesSampledQty}&inspectDocId=${params.inspectDocId}`, {
            method: 'POST',
            body: params,
            responseType: 'blob',
          }).then(res=>{
            inspectionReportDs.reset();
            if (res && !res?.failed) {
              if (res.type === 'application/json') {
                const file = new FileReader();
                file.readAsText(res, 'utf-8');
                file.onload = () => {
                  if (typeof file.result === 'string') {
                    const message = JSON.parse(file.result);
                    return notification.error({ message: message.message });
                  }
                };
              } else {
                const file = new Blob([res], { type: 'application/pdf' });
                const fileURL = URL.createObjectURL(file);
                const newWindow = window.open(fileURL);
                if (newWindow) {
                  // newWindow.print();
                  return true;
                }
                notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
                return false;
              }
            }
          })
        }else{
          notification.error({})
        }
      }else{
        notification.error({})
      }
    })
  }

  const onStationWorkcellChange = (lovRecord) => {
    if (detailDs.current?.get("inspectBusinessType") === 'EXC-QC' && lovRecord.equipmentId) {
      // 对设备赋值;
      detailDs.current?.set('equipmentLov', {
        equipmentId: lovRecord.equipmentId,
        equipmentCode: lovRecord.equipmentCode,
        equipmentName: lovRecord.equipmentName,
      });
    }
  }

  return (
    <div className="hmes-style">
      <Spin
        dataSet={detailDs}
        spinning={
          detailDs.status !== DataSetStatus.ready ||
          ncCodeDs.status !== DataSetStatus.ready ||
          defectRecordsDs.status !== DataSetStatus.ready ||
          ncCodeDs.status !== DataSetStatus.ready ||
          defectRecordsDs.status !== DataSetStatus.ready ||
          saveLoading ||
          cancelLoading ||
          siteLoading ||
          scanLoading ||
          getFunctionListLoading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('检验单维护')}
          backPath={backPathUrl()}
        >
          {!disposalStatus &&
            (canEdit ? (
              <>
                <Button
                  color={ButtonColor.primary}
                  icon="save"
                  loading={saveLoading}
                  onClick={() => handleValidateAndSave(false)}
                >
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
                <Button icon="save" onClick={() => handleValidateAndSave(true)}>
                  {intl.get(`tarzan.common.button.saveAndCreate`).d('保存并新建下一条')}
                </Button>
                <Button icon="close" onClick={handleCancel}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
              </>
            ) : (
              <>
                {/* <PermissionButton
                  type="c7n-pro"
                  icon="edit-o"
                  color={ButtonColor.primary}
                  disabled={editButton || completedButton}
                  onClick={handleEdit}
                  permissionList={[
                    {
                      code: `${path}.button.edit`,
                      type: 'button',
                      meaning: '详情页-编辑新建删除复制按钮',
                    },
                  ]}
                >
                  {intl.get('tarzan.common.button.edit').d('编辑')}
                </PermissionButton> */}
                {/* <Dropdown
                  overlay={
                    <Menu style={{ width: '100px' }} onClick={handleChangeStatus}>
                      <Menu.Item key="CANCEL">
                        <a disabled={!['NEW', 'RELEASED'].includes(detailDs.current?.get('inspectDocStatus'))} onClick={handleCancelInspectDoc}>
                          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
                        </a>
                      </Menu.Item>
                      <Menu.Item key="COMPLETED">
                        <a disabled={!['FIRST_COMPLETED'].includes(detailDs.current?.get('inspectDocStatus'))} onClick={handleCompletedInspectDoc}>{intl.get(`${modelPrompt}.button.completed`).d('完成')}</a>
                      </Menu.Item>
                    </Menu>
                  }
                  trigger={[Action.click]}
                >
                  <PermissionButton
                    type="c7n-pro"
                    disabled={!['NEW', 'RELEASED','FIRST_COMPLETED'].includes(detailDs.current?.get('inspectDocStatus'))}
                    loading={cancelLoading || completedLoading}
                  >
                    {intl.get(`${modelPrompt}.changeStatus`).d('状态变更')} <Icon type="expand_more" />
                  </PermissionButton>
                </Dropdown> */}
                <Button
                  onClick={handleCancelInspectDoc}
                  disabled={!['NEW', 'RELEASED'].includes(detailDs.current?.get('inspectDocStatus'))}
                  loading={cancelLoading}
                >
                  {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
                </Button>
                <AttributeDrawer
                  serverCode={BASIC.TARZAN_SAMPLING}
                  className="org.tarzan.qms.domain.entity.MtInspectDoc"
                  kid={kid}
                  canEdit={false}
                  disabled={kid === 'create'}
                  custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.ATTR`}
                  custConfig={custConfig}
                />
                {/* <PermissionButton
                  type="c7n-pro"
                  color={ButtonColor.primary}
                  disabled={editButton || completedButton}
                  onClick={clickSubmit}
                  loading={submitLoading}
                >
                  {intl.get('tarzan.common.button.submit').d('提交')}
                </PermissionButton> */}
                {/* <Dropdown
                  overlay={
                    <Menu style={{ width: '100px' }} onClick={dispositionAudits}>
                      <Menu.Item key="APPROVING">
                        <a>
                          {intl.get(`${modelPrompt}.button.NGExamineApprove`).d('发起不良审批')}
                        </a>
                      </Menu.Item>
                      <Menu.Item key="DISPOSAL">
                        <a>{intl.get(`${modelPrompt}.button.disposal`).d('直接处置')}</a>
                      </Menu.Item>
                    </Menu>
                  }
                  trigger={[Action.click]}
                > */}
                <PermissionButton
                  type="c7n-pro"
                  color={ButtonColor.primary}
                  disabled={auditButton}
                  loading={reviewLoading}
                  onClick={() => { dispositionAudits({ key:'DISPOSAL' }) }}
                >
                  {intl.get(`${modelPrompt}.disposalApproving`).d('处置审核')}
                </PermissionButton>
                {/* </Dropdown> */}
                { detailDs.current?.get('inspectBusinessType') === 'IQC-YC' && (
                  <Button onClick={handleInspectionReport}>
                    {intl.get(`${modelPrompt}.inspectionReport`).d('原材料检验报告')}
                  </Button>
                )}
              </>
            ))}
          {disposalStatus && (
            <>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                onClick={handleNcReviewSubmit}
              >
                {intl.get(`${modelPrompt}.button.justSubmit`).d('提交')}
              </PermissionButton>
              <PermissionButton type="c7n-pro" onClick={handleNcReviewSubmitCancel}>
                {intl.get(`tarzan.common.button.cancel`).d('取消')}
              </PermissionButton>
            </>
          )}
        </Header>
        <Content>
          {kid !== 'create' && (
            <div>
              <Radio.Group
                onChange={handlePageChange}
                value={pageSwitch}
                style={{ marginBottom: 8 }}
              >
                <Radio.Button value="left">
                  {intl.get(`${modelPrompt}.tab.inspectDocAndObject`).d('检验单及报检信息')}
                </Radio.Button>
                <Radio.Button value="center">
                  {intl.get(`${modelPrompt}.tab.inspectTaskInfo`).d('检验任务信息')}
                </Radio.Button>
                <Radio.Button value="right">
                  {intl.get(`${modelPrompt}.tab.ncAndDefect`).d('不良与处置记录')}
                </Radio.Button>
              </Radio.Group>
            </div>
          )}
          <div style={{ display: pageSwitch === 'left' ? 'block' : 'none' }}>
            <Collapse
              bordered={false}
              defaultActiveKey={['basic', 'sourceDocAndInfo', 'inspectInfoDtl']}
            >
              <Panel key="basic" header={intl.get(`${modelPrompt}.title.basic`).d('基础信息')}>
                {customizeForm(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.BASIC`,
                  },
                  <Form dataSet={detailDs} columns={3} disabled={!canEdit}>
                    <TextField name="inspectDocNum" />
                    <Select name="inspectDocStatus" />
                    <Lov name="siteLov" onChange={handleChangeSite} />
                    <Select
                      name="inspectBusinessType"
                      onChange={handleChangeBusinessType}
                      noCache
                    />
                    <C7nFormItemSort name="materialLov" itemWidth={['70%', '30%']}>
                      <Lov name="materialLov" onChange={handleChangeMaterial} />
                      <Select name="revisionCode" onChange={handleChangeRevision} />
                    </C7nFormItemSort>
                    <TextField name="materialCode" />
                    <TextField name="model" />
                    <TextField name="bomCode" />
                    {originalTypeLovShow && <Lov name="originalTypeLov"/>}
                    <Lov name="customerLov" />
                    <Lov name="supplierLov" />
                    <Lov name="processWorkcellLov" />
                    <Lov name="stationWorkcellLov" onChange={onStationWorkcellChange} />
                    <Lov name="operationLov" />
                    <Lov name="areaLov" />
                    <Lov name="prodLineLov" />
                    <Lov name="locatorLov" />
                    <Lov name="equipmentLov" />
                    <Select name="docCreateMethod" disabled onChange={handleDocCreateMethod}/>
                    <NumberField name="inspectSumQty" />
                    <Lov name="uomLov" />
                    <Switch name="urgentFlag" />
                    <Lov name="shiftTeamLov" />
                    <DatePicker name="shiftDate" />
                    <Select name="shiftCode" />
                    {kid === 'create' && <Lov name="inspectorUserLov" />}
                    <DateTimePicker name="creationDate" />
                    {kid !== 'create' && (
                      <>
                        <TextField name="inspectSchemeCode" />
                        <TextField name="samplingMethodDesc" />
                        <TextField name="strictnessDesc" />
                        <TextField name="samplingDimensionDesc" />
                        <NumberField name="samplingQty" />
                        <AcceptStandardComponent name="acceptStandard" />
                        <DateTimePicker name="actualStartTime" />
                        <DateTimePicker name="actualEndTime" />
                        <NumberField name="okQty" />
                        <NumberField name="ngQty" />
                        <NumberField name="scrapQty" />
                        <Select name="firstInspectResult" />
                        <TextField name="firstInspectorName" />
                        <DateTimePicker name="firstInspectDate" />
                        <Select name="lastInspectResult" />
                        <TextField name="lastInspectorName" />
                        <DateTimePicker name="lastInspectDate" />
                      </>
                    )}
                    <TextField name="remark" />
                  </Form>,
                )}
              </Panel>
              <Panel
                key="sourceDocAndInfo"
                header={intl.get(`${modelPrompt}.title.sourceDocAndInfo`).d('来源单据/报检信息')}
                className={styles['source-doc-title']}
              >
                <Table
                  dataSet={sourceDocDs}
                  columns={sourceDocColumns}
                  customizedCode="inspectDocMaintain-detailSourceDoc"
                />
              </Panel>
              <Panel
                key="inspectInfoDtl"
                header={intl.get(`${modelPrompt}.title.inspectInfoDtl`).d('报检信息明细')}
              >
                {kid === 'create' && (
                  <Form dataSet={scanFormDs} columns={4}>
                    <Select name="objectType" onChange={handleChangeObjectType} />
                    {!scanObjectType && (
                      <TextField
                        ref={scanInspectCodeRef}
                        className={styles['login-scan-form']}
                        name="scanInspectCode"
                        placeholder={intl
                          .get(`${modelPrompt}.placeholder.scanInspectObjLov`)
                          .d('请选择/扫描报检对象')}
                        suffix={<img alt="" src={scanImg} />}
                        onEnterDown={() => handleScanInspectCode()}
                      />
                    )}
                    {['CONTAINER'].includes(scanObjectType) && (
                      <Lov
                        name="scanInspectEoLov"
                        placeholder={intl
                          .get(`${modelPrompt}.placeholder.scanInspectObjLov`)
                          .d('请选择/扫描报检对象')}
                        onChange={handleScanInspectObj}
                      />
                    )}
                    {['MATERIAL_LOT', 'EO'].includes(scanObjectType) && (
                      <Lov
                        name="scanInspectEoLovBatch"
                        placeholder={intl
                          .get(`${modelPrompt}.placeholder.scanInspectObjLov`)
                          .d('请选择/扫描报检对象')}
                        onChange={handleScanInspectObjBatch}
                      />
                    )}
                    {['MAT', 'LOT'].includes(scanObjectType) && (
                      <Lov
                        name="scanInspectMatLov"
                        placeholder={intl
                          .get(`${modelPrompt}.placeholder.scanInspectObjLov`)
                          .d('请选择/扫描报检对象')}
                        onChange={handleScanInspectObj}
                      />
                    )}
                  </Form>
                )}
                <Table
                  dataSet={inspectInfoDs}
                  columns={inspectInfoColumns}
                  customizedCode="inspectDocMaintain-detailInspectInfo"
                  filter={record => !record.get('parentContainerCode')}
                />
              </Panel>
            </Collapse>
          </div>
          <div style={{ display: pageSwitch === 'center' ? 'block' : 'none' }}>
            <InspectTaskComponent
              id={kid}
              history={history}
              taskInfoDs={taskInfoDs}
              customizeForm={customizeForm}
              customizeTable={customizeTable}
              custConfig={custConfig}
              defaultKey={taskInfoDs.map(rec => String(rec.get('inspectTaskId')))}
            />
          </div>
          <div style={{ display: pageSwitch === 'right' ? 'block' : 'none' }}>
            <NcAndDefectComponent
              ncCodeDs={ncCodeDs}
              defectRecordsDs={defectRecordsDs}
              customizeForm={customizeForm}
              customizeTable={customizeTable}
              custConfig={custConfig}
              canEdit={canEdit}
              defectLineDss={defectLineDss}
              setDefectLineDss={setDefectLineDss}
              functionList={functionList}
            />
          </div>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.BASIC`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.TASK_BASIC`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.LINE_BASIC`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.BASIC_HIS`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.TASK_ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.LINE_ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.NC_RECORD`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL_LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.NC_RECORD_ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL_ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_DETAIL.DISPOSAL_LINE_ATTR`,
    ],
  })(InspectDocDetail as any),
);
