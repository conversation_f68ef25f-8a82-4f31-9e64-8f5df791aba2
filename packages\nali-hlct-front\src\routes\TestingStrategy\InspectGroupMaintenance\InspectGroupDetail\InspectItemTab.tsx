/**
 * @Description: 检验项目组维护明细界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:55:10
 * @LastEditTime: 2023-05-24 16:31:25
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useState } from 'react';
import intl from 'utils/intl';
import { DataSet, Table, Spin, Modal, Attachment, Button } from 'choerodon-ui/pro';
import { Badge, Popconfirm, Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import { ColumnAlign, ColumnLock, DragColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { Buttons } from 'choerodon-ui/pro/lib/table/Table';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useRequest } from '@components/tarzan-hooks';
import myInstance from '@utils/myAxios';
import { AttributeDrawer, drawerPropsC7n } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import { SyncInspectItemDetail } from '../services';
import InspectItemInfoDrawer from './InspectItemInfoDrawer';
import InspectItemBatchEditDrawer from './InspectItemBatchEditDrawer';
import { NumberDS, DetailTableDS, formulaListTableDS } from '../stories';

const modelPrompt = 'tarzan.qms.inspectGroupMaintenance';
const tenantId = getCurrentOrganizationId();

const InspectItemTab = props => {
  const { path, id, canEdit, customizeTable, customizeForm, custConfig, tableDS } = props;

  enum EditType {
    add = 'add',
    edit = 'edit',
  }

  // 表格勾选数据
  const [tableSelectList, setTableSelectList] = useState([]);
  // 数值类型-预警值DS
  const warnNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  // 数值类型-符合值DS
  const trueNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  // 数值类型-不符合值DS
  const falseNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  //  单独编辑表单
  const singleTableFormDS = useMemo(() => new DataSet(DetailTableDS()), []);
  // 批量编辑表单
  const batchTableFormDS = useMemo(() => new DataSet(DetailTableDS()), []);
  // 公式参数列表
  const formulaListTableDs = useMemo(() => new DataSet(formulaListTableDS()), []);
  let lineModal;

  // 监听关系表格勾选数据
  const handleTableSelect = ({ dataSet }) => {
    setTableSelectList(dataSet.selected || []);
  };
  useDataSetEvent(tableDS, 'select', handleTableSelect);
  useDataSetEvent(tableDS, 'selectAll', handleTableSelect);
  useDataSetEvent(tableDS, 'unselect', handleTableSelect);
  useDataSetEvent(tableDS, 'unselectAll', handleTableSelect);

  const _ignoreKeys = [
    'creationDate',
    'createdBy',
    'lastUpdateDate',
    'lastUpdatedBy',
    'objectVersionNumber',
    '_token',
    'inspectGroupItemId',
    'sequence',
  ];

  const syncData = useRequest(SyncInspectItemDetail(), {
    // 详情页同步行数据
    manual: true,
    needPromise: true,
  });

  // 行同步
  const handleSync = record => {
    if (!canEdit) {
      return;
    }
    return syncData.run({
      params: {
        inspectItemId: record.get('inspectItemId'),
      },
      onSuccess: res => {
        // @ts-ignore
        notification.success();
        if (res) {
          const _fields = tableDS.fields || [];
          _fields.forEach(field => {
            if (field && field.name && !_ignoreKeys.includes(field.name)) {
              record.set(
                field.name,
                (res && res[field.name]) || (res?.flex || {})[field.name] || '',
              );
            }
          });
          const dataType = record.get('dataType');
          const trueValueList = record.get('trueValueList') || [];
          const falseValueList = record.get('falseValueList') || [];
          if (['TEXT', 'DECISION_VALUE'].includes(dataType)) {
            record.set('trueValue', trueValueList.length > 0 ? trueValueList[0].dataValue : null);
            record.set(
              'falseValue',
              falseValueList.length > 0 ? falseValueList[0].dataValue : null,
            );
          } else if (dataType === 'VALUE_LIST') {
            record.set(
              'trueValue',
              trueValueList.length > 0 ? trueValueList.map(trueItem => trueItem.dataValue) : null,
            );
            record.set(
              'falseValue',
              falseValueList.length > 0
                ? falseValueList.map(falseItem => falseItem.dataValue)
                : null,
            );
          }
          // 同步附件
          if (res.enclosure) {
            // 复制附件uuid
            myInstance
              .post(`hfle/v1/${tenantId}/files/copy-file`, { uuidList: [res.enclosure] })
              .then(uuidRes => {
                if (uuidRes && uuidRes.data && uuidRes.data[res.enclosure]) {
                  record.set('enclosure', uuidRes.data[res.enclosure]);
                }
              });
          } else {
            record.set('enclosure', null);
          }
        }
      },
    });
  };

  // 行勾选删除
  const handleBatchDelete = () => {
    if ((tableDS.selected || []).length < 1) {
      return;
    }
    const deleteIds = [];
    tableDS.selected.forEach(record => {
      const recordData = record.toData();
      // @ts-ignore
      deleteIds.push(recordData.uuid);
    });
    tableDS.remove(tableDS.selected);
    tableDS.forEach(record => {
      const recordData = record.toData();
      const { dataType, formulaId, formulaCode, formulaName, dimension, formulaList } = recordData;
      if (dataType === 'CALCULATE_FORMULA') {
        if (formulaList?.length > 0) {
          const newFormulaList = formulaList.map(item => {
            return {
              ...item,
              // @ts-ignore
              inspectItemId: deleteIds.includes(item.inspectItemId) ? null : item.inspectItemId,
              // @ts-ignore
              inspectItemDesc: deleteIds.includes(item.inspectItemId) ? null : item.inspectItemDesc,
            };
          });

          record.set(
            'formula',
            JSON.stringify({
              formulaId,
              formulaCode,
              formulaName,
              dimension,
              formulaList: newFormulaList,
            }),
          );

          record.set('formulaList', newFormulaList);
        }
      }
    });

    setTableSelectList([]);

    setTimeout(() => {
      handleDragEnd();
    }, 10);
  };

  // 行新增或编辑
  const handleAddEdit = (editType, record) => {
    if (editType === EditType.add) {
      const _lastNumber = tableDS
        .toData()
        .sort((a: any, b: any) => Number(b.sequence || 0) - Number(a.sequence || 0))[0] || {
        sequence: 0,
      };
      const newLineNumber = _lastNumber.sequence || 0;
      const uuid = new Date().getTime();
      singleTableFormDS.loadData([
        {
          isNewRow: true,
          uuid,
          inspectGroupItemId: -uuid,
          sequence: parseInt(`${newLineNumber / 10}`, 10) * 10 + 10,
        },
      ]);
    } else if (record) {
      const recordData = record.toData();
      singleTableFormDS.loadData([{ ...recordData, isNewRow: false }]);
    }

    const handleSaveAndNew = async () => {
      const saveRes = await handleInspectItemDrawerSubmit(true);
      if (!saveRes && saveRes !== undefined) {
        return;
      }
      lineModal?.close();
      handleAddEdit(EditType.add, false);
    };

    const inspectItemDrawerProps = {
      canEdit,
      _ignoreKeys,
      tenantId,
      customizeForm,
      warnNumberDS,
      trueNumberDS,
      falseNumberDS,
      editTableFormDS: singleTableFormDS,
      tableDS,
      custConfig,
      formulaListTableDs,
    };

    lineModal = Modal.open({
      key: Modal.key(),
      title:
        editType === EditType.add
          ? intl.get(`${modelPrompt}.title.itemCreate`).d('新增检验项目')
          : intl.get(`${modelPrompt}.title.itemEdit`).d('编辑检验项目'),
      drawer: true,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: '1080px',
      },
      className: 'hmes-style-modal',
      children: <InspectItemInfoDrawer {...inspectItemDrawerProps} />,
      onOk: () => {
        return handleInspectItemDrawerSubmit(true);
      },
      footer: (okBtn, cancelBtn) => {
        const NextButton = () => (
          <Button onClick={handleSaveAndNew}>
            {intl.get(`tarzan.common.button.saveAndCreate`).d('保存并新建下一条')}
          </Button>
        );
        return canEdit ? [cancelBtn, NextButton(), okBtn] : [cancelBtn];
      },
    });
  };

  const handleBatchEditConfirm = () => {
    if ((tableDS.selected || []).length < 1) {
      return;
    }
    let calculateFormulaSum = 0;
    tableDS.selected.forEach(record => {
      if (record.get('dataType') === 'CALCULATE_FORMULA') {
        calculateFormulaSum++;
      }
    });
    if (calculateFormulaSum > 0) {
      return Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: intl
          .get(`${modelPrompt}.BatchEditTips`)
          .d('批量编辑不会对数据类型为“计算公式”的检验项目生效。'),
        onOk: handleBatchEdit,
        cancelButton: false,
      });
    }
    return handleBatchEdit();
  };

  const handleBatchEdit = () => {
    if ((tableDS.selected || []).length < 1) {
      return;
    }
    batchTableFormDS.getField('sequence')?.set('required', false);
    batchTableFormDS.getField('inspectItemLov')?.set('required', false);
    batchTableFormDS.getField('dataType')?.set('required', false);
    batchTableFormDS.create({
      inspectItemLov: {},
      requiredFlag: 'Y',
      destructiveExperimentFlag: 'N',
      outsourceFlag: 'N',
    });
    const inspectItemDrawerProps = {
      canEdit,
      _ignoreKeys,
      tenantId,
      customizeForm,
      warnNumberDS,
      trueNumberDS,
      falseNumberDS,
      tableDS: batchTableFormDS,
      custConfig,
      formulaListTableDs,
    };

    Modal.open({
      ...drawerPropsC7n({
        canEdit,
        ds: batchTableFormDS,
      }),
      key: Modal.key(),
      title: (
        <>
          <span>{intl.get(`${modelPrompt}.itemBatchEdit`).d('批量编辑检验项目')}</span>
          <span style={{ float: 'right', marginRight: 50 }}>
            <span>{(tableSelectList || []).length}</span>
            {intl.get(`${modelPrompt}.title.itemResetSelect`).d('项已选中')}
          </span>
        </>
      ),
      drawer: true,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: '1080px',
      },
      className: 'hmes-style-modal',
      children: <InspectItemBatchEditDrawer {...inspectItemDrawerProps} />,
      onOk: () => {
        return handleInspectItemDrawerSubmit(false);
      },
    });
  };

  // 确定保存检验项目
  const handleInspectItemDrawerSubmit = async singleFlag => {
    let validateResult;
    let record;
    if (singleFlag) {
      record = singleTableFormDS.current;
    } else {
      record = batchTableFormDS.current;
    }

    if (!record) {
      return false;
    }
    // 检验项目字段校验
    const dataType = record.get('dataType');
    if (singleFlag) {
      validateResult = await formulaListTableDs.validate();
      if (!validateResult) {
        return false;
      }
      validateResult = await singleTableFormDS.validate();
      if (!validateResult) {
        return false;
      }
    } else {
      const validateResult = await batchTableFormDS.validate();
      if (!validateResult) {
        return false;
      }
    }
    if (singleFlag) {
      const { sequence, inspectItemId, inspectGroupItemId } = record.toData();
      const sameSerialNumberFlag = tableDS
        .toData()
        .some(item => item.sequence === sequence && item.inspectGroupItemId !== inspectGroupItemId);
      const sameInspectItemFlag = tableDS
        .toData()
        .some(
          item =>
            item.inspectItemId === inspectItemId && item.inspectGroupItemId !== inspectGroupItemId,
        );
      if (sameSerialNumberFlag) {
        notification.error({
          message: intl.get(`${modelPrompt}.message.sameSerialNumber`).d('当前序号已存在'),
        });
        return false;
      }
      if (sameInspectItemFlag) {
        notification.error({
          message: intl.get(`${modelPrompt}.message.sameInspectItemFlag`).d('当前检验项目已选用'),
        });
        return false;
      }
    }

    let _trueValueList: any = [];
    let _falseValueList: any = [];
    let _warningValueList: any = [];
    // 如果数据类型为数值类型，需要做赋值处理
    if (['CALCULATE_FORMULA', 'VALUE'].includes(dataType)) {
      const _valueHandle = targetDS => {
        return targetDS
          .toData()
          .filter(
            (item: any) =>
              item.singleValued ||
              (item.multiValued && (item.multiValued?.start || item.multiValued?.end)),
          )
          .map((item: any) => {
            if (
              item.valueType !== 'single' &&
              (!item.multiValued?.start || !item.multiValued?.end)
            ) {
              if (!item.multiValued?.start) {
                item.multiValued.start = '-∞';
                item.leftValue = '-∞';
              }
              if (!item.multiValued?.end) {
                item.multiValued.end = '+∞';
                item.rightValue = '+∞';
              }
              item.dataValue = `${item.leftChar}${item.multiValued.start},${item.multiValued.end}${item.rightChar}`;
              const _leftCharTip = item.leftChar === '(' ? '<' : '≤';
              const _rightCharTip = item.rightChar === ')' ? '<' : '≤';
              item.valueShow = `${item.multiValued.start}${_leftCharTip}X${_rightCharTip}${item.multiValued.end}`;
            }
            return item;
          });
      };

      _trueValueList = _valueHandle(trueNumberDS);
      _falseValueList = _valueHandle(falseNumberDS);
      _warningValueList = _valueHandle(warnNumberDS);
    } else if (dataType === 'VALUE_LIST') {
      _trueValueList = (record.get('trueValue') || []).map(item => ({
        dataValue: item,
      }));
      _falseValueList = (record.get('falseValue') || []).map(item => ({
        dataValue: item,
      }));
    } else {
      _trueValueList = record.get('trueValue') ? [{ dataValue: record.get('trueValue') }] : [];
      _falseValueList = record.get('falseValue') ? [{ dataValue: record.get('falseValue') }] : [];
    }

    if (dataType === 'CALCULATE_FORMULA') {
      record.set(
        'formula',
        JSON.stringify({
          formulaId: record.get('formulaId'),
          formulaCode: record.get('formulaCode'),
          formulaName: record.get('formulaName'),
          dimension: record.get('dimension'),
          formulaList: (formulaListTableDs.toData() || []).map((formulaListItem: any) => {
            if (formulaListItem) {
              return {
                fieldCode: formulaListItem.fieldCode,
                fieldName: formulaListItem.fieldName,
                inspectItemId: formulaListItem.inspectItemId,
                inspectItemDesc: formulaListItem.inspectItemDesc,
                isRequired: formulaListItem.isRequired,
              };
            }
            return {};
          }),
        }),
      );
      record.set('formulaList', formulaListTableDs.toData());
    } else {
      record.set('formula', '');
      record.set('formulaList', []);
    }

    record.set('trueValueList', _trueValueList);
    record.set('falseValueList', _falseValueList);
    record.set('warningValueList', _warningValueList);
    if (singleFlag) {
      const recordData = record.toData();
      if (recordData.isNewRow) {
        tableDS.create({
          ...recordData,
          isNewRow: false,
        });
      } else {
        tableDS.forEach(focusRecord => {
          if (focusRecord.get('uuid') === recordData.uuid) {
            Object.keys(recordData).forEach(recordDataKey => {
              focusRecord.set(recordDataKey, recordData[recordDataKey]);
            });
          }
        });
      }
    } else {
      if (_falseValueList.length > 0) {
        record.set('warningValueList', []);
      } else if (_warningValueList.length > 0) {
        record.set('warningValueList', _warningValueList);
      } else if (record.get('earlyWarningValue_select')) {
        record.set('warningValueList', []);
      }
      // 批量编辑处理数据

      // ds fields 项
      const _fields = tableDS.fields || [];
      // 忽略更新的项
      const _newIgnoreKeys = _ignoreKeys.concat([
        'selectList',
        'inspectItemLov',
        'inspectItemId',
        'inspectItemCode',
        'inspectItemDesc',
        'enclosure',
        'inspectFrequencyDesc',
      ]);
      // 特殊处理的项
      const _listField = ['trueValueList', 'falseValueList', 'warningValueList'];
      // 与dataType相关的需要处理的项
      const _aboutDataTypeField = [
        'uomLov',
        'decimalNumber',
        'processMode',
        'valueLists',
        'trueValue',
        'falseValue',
        'earlyWarningValue',
        'trueValueList',
        'falseValueList',
        'warningValueList',
        'formulaLov',
        'formulaList',
        'dimension',
      ];

      const { selectList } = record.toData();
      const selectListKeys = Object.keys(selectList || {});

      // 遍历选中的需要批量更新的项
      tableDS.selected.forEach(item => {
        const itemData = item.toData();
        if (itemData?.dataType !== 'CALCULATE_FORMULA') {
          // 在 ds fields中遍历
          _fields.forEach(field => {
            // 如果不在忽略列表中
            if (field && field.name && !_newIgnoreKeys.includes(field.name)) {
              // 先判断是否dataType有值
              if (record?.get('dataType')) {
                // 如果在dataType关联字段中
                if (_aboutDataTypeField.includes(field.name)) {
                  // 直接更新该字段
                  item.set(field.name, record.get(field.name));
                  // 不在dataType关联字段中, 有值的话更新
                } else if (record.get(field.name)) {
                  item.set(field.name, record.get(field.name));
                }
                // 如果dataType无值, 该字段有值, 又是不需要特殊处理的项 直接更新该字段
              } else if (record.get(field.name) && !_listField.includes(field.name)) {
                item.set(field.name, record.get(field.name));
              }
              selectListKeys.forEach(key => {
                if (selectList[key]) {
                  item.set(key, null);
                }
              });
            }
          });
        }
      });
    }
  };

  const getDataValueShow = (record, name) => {
    const _dataType = record?.get('dataType');
    const _valueData = record?.get(name) || [];
    const _dataShow = _valueData.length > 0 ? _valueData[0].dataValue : '';
    return ['CALCULATE_FORMULA', 'VALUE', 'VALUE_LIST'].includes(_dataType)
      ? _valueData.map(item => <Tag>{item.dataValue}</Tag>)
      : _dataShow;
  };

  const attachmentProps: any = {
    name: 'enclosure',
    bucketName: 'qms',
    bucketDirectory: 'inspect-group-maintain',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    disabled: !canEdit,
    readOnly: !canEdit,
  };

  const buttons: Buttons[] = [
    <PermissionButton
      type="c7n-pro"
      icon="edit-o"
      disabled={!canEdit || tableSelectList.length < 1}
      onClick={handleBatchEditConfirm}
      funcType="flat"
      shape="circle"
      size="small"
      permissionList={[
        {
          code: `${path}.button.edit`,
          type: 'button',
          meaning: '详情页-编辑新建删除复制按钮',
        },
      ]}
    >
      {intl.get('hzero.common.button.batchEdit').d('批量编辑')}
    </PermissionButton>,
    <Popconfirm
      title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
      onConfirm={() => handleBatchDelete()}
      okText={intl.get('tarzan.common.button.confirm').d('确认')}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
    >
      <PermissionButton
        type="c7n-pro"
        icon="delete_black-o"
        disabled={!canEdit || tableSelectList.length < 1}
        funcType="flat"
        shape="circle"
        size="small"
        permissionList={[
          {
            code: `dist.button.delete`,
            type: 'button',
            meaning: '详情页-删除按钮',
          },
        ]}
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
    </Popconfirm>,
    <PermissionButton
      type="c7n-pro"
      icon="add"
      disabled={!canEdit}
      onClick={() => handleAddEdit(EditType.add, false)}
      funcType="flat"
      shape="circle"
      size="small"
      permissionList={[
        {
          code: `${path}.button.edit`,
          type: 'button',
          meaning: '详情页-编辑新建删除复制按钮',
        },
      ]}
    >
      {intl.get('hzero.common.button.add').d('新增')}
    </PermissionButton>,
  ];

  const handleDragEnd = () => {
    tableDS.forEach((record, index) => {
      record.set('sequence', index * 10 + 10);
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'sequence',
      width: 80,
      align: ColumnAlign.center,
    },
    {
      name: 'inspectItemLov',
      width: 200,
      renderer: ({ record }) => (
        <a onClick={() => handleAddEdit(EditType.edit, record)}>{record?.get('inspectItemCode')}</a>
      ),
    },
    {
      name: 'inspectItemDesc',
      width: 200,
    },
    {
      name: 'inspectItemType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'inspectBasis',
    },
    {
      name: 'inspectMethod',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'technicalRequirement',
    },
    {
      name: 'inspectTool',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'qualityCharacteristic',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'dataType',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'trueValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'trueValueList'),
    },
    {
      name: 'falseValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'falseValueList'),
    },
    {
      name: 'earlyWarningValue',
      width: 200,
      renderer: ({ record }) => getDataValueShow(record, 'warningValueList'),
    },
    {
      name: 'uomLov',
      align: ColumnAlign.center,
    },
    {
      name: 'processMode',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'enterMethod',
      align: ColumnAlign.center,
    },
    {
      name: 'decimalNumber',
    },
    {
      name: 'requiredFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'dataQty',
    },
    {
      name: 'samplingMethodLov',
      align: ColumnAlign.center,
    },
    {
      name: 'sameGroupIdentification',
    },
    {
      name: 'destructiveExperimentFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'outsourceFlag',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'actionItem',
    },
    {
      name: 'employeePosition',
      width: 120,
    },
    {
      name: 'inspectFrequencyDesc',
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
        if (inspectFrequencyShow) {
          inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
          inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
          return inspectFrequencyShow;
        }
        return value;
      },
    },
    {
      name: 'ncCodeGroupLov',
      align: ColumnAlign.center,
    },
    {
      name: 'remark',
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      width: 230,
      lock: ColumnLock.right,
      renderer: ({ record }) => {
        return (
          <>
            <Attachment {...attachmentProps} record={record} />
            <PermissionButton
              type="text"
              permissionList={[
                {
                  code: `dist.button.syncData`,
                  type: 'button',
                  meaning: '详情页-同步数据',
                },
              ]}
              onClick={() => handleSync(record)}
              disabled={!canEdit}
              style={{ marginLeft: 20 }}
            >
              {intl.get(`${modelPrompt}.syncData`).d('同步数据')}
            </PermissionButton>
          </>
        );
      },
    },
    {
      header: intl.get('tarzan.common.title.extendField').d('扩展属性'),
      name: 'extendField',
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      width: 100,
      renderer: ({ record }) => {
        return (
          <>
            <AttributeDrawer
              serverCode={BASIC.TARZAN_SAMPLING}
              className="org.tarzan.qms.domain.entity.MtInspectGroupItem"
              kid={record?.get('inspectGroupItemId')}
              canEdit={canEdit}
              disabled={id === 'create'}
              custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.ATTR1`}
              custConfig={custConfig}
              type="text"
            />
          </>
        );
      },
    },
  ];

  return (
    <Spin spinning={syncData.loading}>
      {customizeTable(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.RELATION`,
        },
        <Table
          filter={record => {
            return record.get('dataType') !== 'CALCULATE_FORMULA';
          }}
          rowDraggable={canEdit}
          dragColumnAlign={DragColumnAlign.left}
          onDragEnd={handleDragEnd}
          dataSet={tableDS}
          columns={columns}
          buttons={buttons}
          highLightRow
          customizedCode="InspectGroupItem"
        />,
      )}
    </Spin>
  );
};

export default InspectItemTab;
