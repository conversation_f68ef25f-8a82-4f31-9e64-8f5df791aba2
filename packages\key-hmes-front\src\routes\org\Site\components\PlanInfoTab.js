/**
 * @Description: 站点维护-计划属性Tab
 * @Author: <<EMAIL>>
 * @Date: 2021-02-03 18:44:43
 * @LastEditTime: 2021-10-26 16:42:55
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, NumberField, DateTimePicker, Select } from 'choerodon-ui/pro';
import { isNull } from 'lodash';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.model.org.site';

const PlanInfoTab = props => {
  const { ds, canEdit, columns = 1, focus = true } = props;

  // 改为两位小数
  const handleChangeNumber = (field, value) => {
    const data = {};
    if (!isNull(value)) {
      data[field] = value.toFixed(2);
      ds.current.set(data);
    }
  };

  const bomSourceSiteTypeChange = val => {
    ds.current.set('routerSourceSiteType', val);
  };
  const routerSourceSiteTypeChange = val => {
    ds.current.set('bomSourceSiteType', val);
  };

  return (
    <Form
      disabled={!canEdit || focus}
      dataSet={ds}
      columns={columns}
      labelLayout="horizontal"
      labelWidth={112}
    >
      <DateTimePicker name="planStartTime" />
      <NumberField
        name="forwardPlanningTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('forwardPlanningTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <NumberField
        name="orderTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('orderTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <NumberField
        name="demandTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('demandTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <NumberField
        name="releaseTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('releaseTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      {/* <NumberField
        name="frozenTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={(value) => {
          handleChangeNumber('frozenTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof(value) == 'number' ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}` : ''
        }
      /> */}
      <NumberField
        name="fixTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('fixTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <Select name="schedulingNode" />
      <Select name="orgSelectionAlgorithm" />
      <NumberField
        name="rollingPeriod"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('rollingPeriod', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <Select name="forceCompleteCycle" />
      <Select name="schedulingAlgorithm" />
      <Select name="bomSourceSiteType" onChange={bomSourceSiteTypeChange} />
      <Select name="routerSourceSiteType" onChange={routerSourceSiteTypeChange} />
    </Form>
  );
};

export default PlanInfoTab;
