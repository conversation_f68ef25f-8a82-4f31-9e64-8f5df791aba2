/**
 * @Description: 委托实验平台-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-06-21
 * @LastEditTime: 2022-06-25
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect } from 'react';
import {
  DataSet,
  Table,
  Modal,
  Form,
  TextArea,
  // Attachment,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ExpandCardC7n } from '@components/tarzan-ui';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
// import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId, getCurrentUser } from 'hzero-front/lib/utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import { tableDS, tableLineDS } from '../stores/CommissionedExperimentalPlatformTableDS';

const tenantId = getCurrentOrganizationId();
const currentUser = getCurrentUser();
const modelPrompt = 'key.hwms.front.CommissionedExperimentalPlatform';
// ${BASIC.TARZAN_SAMPLING}

const CommissionedExperimentalPlatformTable = observer(props => {
  const { tableDs, tableLineDs } = props;
  const [headSelected, setHeadSelected] = useState([]);
  const [lineSelected, setLineSelected] = useState([]);

  useEffect(() => {
    tableDs.query(tableDs.currentPage).then(r => r);
  }, []);

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('entrustApplyId'));
    } else {
      queryLineTable(null);
    }
  };

  const queryLineTable = entrustApplyId => {
    if (entrustApplyId) {
      tableLineDs.setQueryParameter('entrustApplyId', entrustApplyId);
    } else {
      tableLineDs.setQueryParameter('entrustApplyId', 0);
    }
    tableLineDs.query();
  };

  const handleDataSetSelect = () => {
    const data = tableDs.selected.map(ele => ele.toData());
    if (data.length > 0) {
      setHeadSelected(tableDs.selected);
    } else {
      tableLineDs.loadData([]);
      setHeadSelected([]);
    }
  };
  useDataSetEvent(tableDs, 'load', resetHeaderDetail);
  useDataSetEvent(tableDs, 'select', handleDataSetSelect);
  useDataSetEvent(tableDs, 'selectAll', handleDataSetSelect);
  useDataSetEvent(tableDs, 'unselect', handleDataSetSelect);
  useDataSetEvent(tableDs, 'unselectAll', handleDataSetSelect);

  const handleLineDataSetSelect = () => {
    const data = tableLineDs.selected.map(ele => ele.toData());
    if (data.length > 0) {
      setLineSelected(tableLineDs.selected);
    } else {
      setLineSelected([]);
    }
  };
  useDataSetEvent(tableLineDs, 'select', handleLineDataSetSelect);
  useDataSetEvent(tableLineDs, 'selectAll', handleLineDataSetSelect);
  useDataSetEvent(tableLineDs, 'unselect', handleLineDataSetSelect);
  useDataSetEvent(tableLineDs, 'unselectAll', handleLineDataSetSelect);

  const columns = [
    {
      name: 'entrustApplyCode',
      width: 160,
      renderer: ({ record }) => {
        return <a onClick={() => jumpToDetail(record)}>{record.get('entrustApplyCode')}</a>;
      },
    },
    { name: 'siteCode', width: 120 },
    { name: 'entrustApplyStatusDesc', width: 120 },
    { name: 'oaApplyStatusDesc', width: 120 },
    { name: 'oaApplyDesc', width: 120 },
    { name: 'entrustUnitName', width: 120 },
    { name: 'entrustUserName', width: 120 },
    { name: 'entrustTime', width: 120 },
    { name: 'sampleQty', width: 120 },
    { name: 'sampleUom', width: 120 },
    { name: 'expectCompleteTime', width: 120 },
    { name: 'sampleDisposalMethodDesc', width: 120 },
    { name: 'inspectApproveStatusDesc', width: 140 },
    { name: 'materialCode', width: 140 },
    { name: 'materialName', width: 120 },
    { name: 'creationDate', width: 140 },
    { name: 'lastUpdateDate', width: 140 },
    { name: 'lastUpdatedByName', width: 120 },
    { name: 'reason', width: 120 },
    { name: 'remark', width: 120 },
    {
      name: 'relatedFileUuid',
      width: 140,
      // renderer: ({ record }) => {
      //   return (
      //     <Attachment {...attachmentProps} record={record} name="enclosure" readOnly disabled />
      //   );
      // },
    },
  ];
  const columnLine = [
    {
      name: 'taskNum',
      width: 140,
      renderer: ({ record }) => {
        return <a onClick={() => jumpToLineDetail(record)}>{record.get('taskNum')}</a>;
      },
    },
    { name: 'taskStatusDesc', width: 120 },
    { name: 'applyStatusDesc', width: 120 },
    { name: 'applyUserName', width: 120 },
    { name: 'applyTime', width: 150 },
    { name: 'inspectUserName', width: 120 },
    {
      name: 'receiveSampleTime',
      width: 150,
    },
    {
      name: 'startTime',
      width: 150,
    },
    { name: 'endTime', width: 150 },
    { name: 'applyOpinion', width: 120 },
    { name: 'creationDate', width: 150 },
    { name: 'lastUpdatedByName', width: 120 },
    { name: 'lastUpdateDate', width: 150 },
  ];

  const jumpToDetail = record => {
    props.history.push({
      pathname: `/hwms/commissioned-experimental-platform/create/${record.get('entrustApplyId')}`,
    });
  };
  const jumpToLineDetail = record => {
    props.history.push({
      pathname: `/hwms/commissioned-experimental-platform/line/${record.get('taskNum')}`,
    });
  };
  // 提交
  const tableSubmit = async () => {
    const noCurrentUser = tableDs.selected.filter(
      ele => ele.get('entrustUserId') !== currentUser.id,
    );
    if (noCurrentUser.length > 0) {
      return notification.error({
        message: intl.get(`${modelPrompt}.message.onlyEditMySelfSubmit`).d('仅允许操作本账号提交的委托申请，请检查!'),
      });
    }
    const noCurrentStatus = tableDs.selected.filter(
      ele => ele.get('entrustApplyStatus') !== 'NEW' || (ele.get('oaApplyStatus') !== 'NEW' && ele.get('oaApplyStatus') !== 'REJECT'),
    );
    if (noCurrentStatus.length > 0) {
      return notification.error({
        message: intl.get(`${modelPrompt}.message.onlyAudit.some.type`).d('仅允许审核检验状态为“检验完成”、且审核状态为“待审核”或“驳回”的检验任务，请检查!'),
      });
    }
    const data = tableDs.selected.map(ele => ele.get('entrustApplyId'));
    await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-apply/approve`, {
      method: 'POST',
      body: {
        entrustApplyIdList: data,
      },
    }).then(res => {
      if (res && !res.failed) {
        notification.success({});
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
    });
  };
  // 撤销
  const tableCancel = async () => {
    const noCurrentUser = tableDs.selected.filter(
      ele => ele.get('entrustUserId') !== currentUser.id,
    );
    if (noCurrentUser.length > 0) {
      return notification.error({
        message: intl.get(`${modelPrompt}.message.onlyEditMySelfSubmit`).d('仅允许操作本账号提交的委托申请，请检查!'),
      });
    }
    const noCurrentStatus = tableDs.selected.filter(
      ele => ele.get('entrustApplyStatus') !== 'NEW' || ele.get('oaApplyStatus') !== 'NEW',
    );
    if (noCurrentStatus.length > 0) {
      return notification.error({
        message: intl.get(`${modelPrompt}.message.onleRevocation.some.type`).d('仅允许撤销申请单状态为“新建”，OA审批状态为“待审批”状态的申请单，请检查！'),
      });
    }
    const data = tableDs.selected.map(ele => ele.get('entrustApplyId'));
    await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-apply/revoke`, {
      method: 'POST',
      body: {
        entrustApplyIdList: data,
      },
    }).then(res => {
      if (res && !res.failed) {
        notification.success({});
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
    });
  };
  // 审批拒绝
  const tableSubmitReject = async (applyStatus) => {
    const data = tableLineDs.selected.map(ele => ele.get('entrustTaskId'));
    const noCurrentStatus = tableLineDs.selected.filter(
      ele =>
        ele.get('taskStatus') !== 'COMPLETED' ||
        (ele.get('applyStatus') !== 'NEW' && ele.get('applyStatus') !== 'REJECT'),
    );
    if (noCurrentStatus.length > 0) {
      return notification.error({
        message: intl.get(`${modelPrompt}.message.onlyAudit.some.type.one`).d('仅允许审核检验状态为“检验完成”、且审核状态为“新建”或“驳回”的检验任务，请检查！'),
      });
    }
    // if (data.length > 1) {
    //   return notification.error({ message: '不允许批量驳回，驳回只能选择单条数据' });
    // }
    const approvalDs = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'taskApproveStatus',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.taskApproveStatus`).d('审批意见'),
          // required: true,
        },
      ],
    });
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.taskApproveStatus`).d('审批意见'),
      children: (
        <>
          <Form dataSet={approvalDs} columns={1}>
            <TextArea name="taskApproveStatus" />
          </Form>
        </>
      ),
      onOk: async () => {
        // const vFlag = await approvalDs.validate();
        // if (!vFlag) {
        //   return false;
        // }
        const taskApproveStatus = approvalDs.current.get('taskApproveStatus');
        await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-entrust-task/approve`, {
          method: 'POST',
          body: {
            entrustTaskIdList: data,
            applyStatus,
            taskApproveStatus,
          },
        }).then(res => {
          if (res && !res.failed) {
            notification.success({});
            const currentKey = tableDs.current?.get('entrustApplyId');
            tableDs.query(tableDs.currentPage).then(() => {
              const _recordIndex = tableDs.findIndex((record) => record?.get('entrustApplyId') === currentKey);
              tableDs.locate(_recordIndex);
              headerRowClick(tableDs?.current);
            })
          } else {
            notification.error({ message: res.message });
            return false;
          }
        });
      },
    });
  };

  const headerRowClick = record => {
    queryLineTable(record?.get('entrustApplyId'));
  };

  const handleSearch = () => {
    tableLineDs.loadData([]);
  };
  useDataSetEvent(tableDs, 'query', handleSearch);
  return (
    <div style={{ height: '100%' }} className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.CommissionedExperimentalPlatform.title`).d('委托实验平台')}
      >
        <>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            permissionList={[
              {
                code: `${modelPrompt}.list.button.create`,
                type: 'button',
                meaning: '列表页-新建按钮',
              },
            ]}
            onClick={() => {
              props.history.push({
                pathname: `/hwms/commissioned-experimental-platform/create/create`,
                state: {
                  status: 'create',
                },
              });
            }}
          >
            {intl.get('hzero.common.button.create').d('新建')}
          </PermissionButton>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            permissionList={[
              {
                code: `${modelPrompt}.list.button.cancel`,
                type: 'button',
                meaning: '列表页-撤销按钮',
              },
            ]}
            disabled={headSelected.length < 1}
            // disabled={
            //   headSelected.length < 1 ||
            //   headSelected.find(
            //     item => item.get('status') !== 'NEW' && item.get('status') !== 'REJECTED',
            //   )
            // }
            onClick={() => tableCancel()}
          >
            {intl.get(`${modelPrompt}.button.cancel`).d('撤销')}
          </PermissionButton>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            permissionList={[
              {
                code: `${modelPrompt}.list.button.submit`,
                type: 'button',
                meaning: '列表页-提交按钮',
              },
            ]}
            // disabled={
            //   headSelected.length < 1 ||
            //   headSelected.find(
            //     item => item.get('status') !== 'NEW' && item.get('status') !== 'REJECTED',
            //   )
            // }
            disabled={headSelected.length < 1}
            onClick={() => tableSubmit()}
          >
            {intl.get(`${modelPrompt}.button.submitOA`).d('提交OA审批')}
          </PermissionButton>
        </>
      </Header>
      <Content>
        <Table
          customizedCode="CommissionedExperimentalPlatformTable"
          searchCode="CommissionedExperimentalPlatformTable"
          queryBar={TableQueryBarType.filterBar}
          dataSet={tableDs}
          columns={columns}
          groups={{
            name: 'reviewStageName',
            type: 'column',
          }}
          queryBarProps={{ onQuery: handleSearch, fuzzyQuery: false }}
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
        />
        <ExpandCardC7n
          showExpandIcon
          title={intl.get(`${modelPrompt}.CommissionedExperimentalPlatformLine`).d('检验任务信息')}
        >
          <div style={{ display: 'inline-block', float: 'right', marginBottom: 10 }}>
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${modelPrompt}.list.button.pass`,
                  type: 'button',
                  meaning: '列表页-通过按钮',
                },
              ]}
              disabled={lineSelected.length < 1}
              onClick={() => tableSubmitReject('PASS')}
            >
              {intl.get(`${modelPrompt}.flims.NewProjectPlanApplication.button.pass`).d('通过')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${modelPrompt}.list.button.reject`,
                  type: 'button',
                  meaning: '列表页-驳回按钮',
                },
              ]}
              disabled={lineSelected.length < 1}
              onClick={() => tableSubmitReject('REJECT')}
            >
              {intl.get(`${modelPrompt}.flims.NewProjectPlanApplication.button.reject`).d('驳回')}
            </PermissionButton>
          </div>
          <div style={{ clear: 'both' }}>
            <Table
              customizable
              customizedCode="CommissionedExperimentalPlatformTableLine"
              dataSet={tableLineDs}
              columns={columnLine}
              queryBar={TableQueryBarType.none}
              queryFieldsLimit={3}
              pagination={{ showPager: true }}
              queryBarProps={{ autoQueryAfterReset: false }}
              rowHeight={34}
              border
            />
          </div>
        </ExpandCardC7n>
      </Content>
    </div>
  );
});
export default formatterCollections({
  code: [
    'hzero.common',
    'key.hwms.front.CommissionedExperimentalPlatform',
  ],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      const tableLineDs = new DataSet({
        ...tableLineDS(),
      });
      return {
        tableDs,
        tableLineDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(CommissionedExperimentalPlatformTable),
);
