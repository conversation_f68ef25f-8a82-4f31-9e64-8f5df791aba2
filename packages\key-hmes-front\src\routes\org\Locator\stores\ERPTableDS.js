/*
 * @Description: ERP 属性 DS
 * @Author: YinWQ
 * @Date: 2023-07-19 17:19:20
 * @LastEditors: YinWQ
 * @LastEditTime: 2023-07-20 15:26:54
 */
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.model.org.locator';

const tenantId = getCurrentOrganizationId();

const erpTableDS = () => ({
  fields: [
    {
      name: 'siteId',
      type: 'number',
      label: intl.get(`${modelPrompt}.siteIds`).d('分配站点'),
      required: true,
      // defaultValue: 180624001,
    },
    {
      name: 'siteName',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.siteName`).d('站点编码'),
    },
    {
      name: 'plantSiteObj',
      type: 'object',
      lovCode: 'MT.MODEL.SITE_PLANT',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: 'always',
      label: intl.get(`${modelPrompt}.plantCode`).d('ERP站点'),
      cascadeMap: { siteIds: 'siteId' },
    },
    {
      name: 'plantCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.plantCode`).d('ERP站点编码'),
      bind: 'plantSiteObj.plantCode',
    },
    {
      name: 'subinv',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.subinv`).d('ERP库位编码'),
      dynamicProps: {
        disabled: ({ record }) => {
          let flag = true;
          if (record?.get('plantCode')) {
            flag = false;
          } else {
            flag = true;
          }
          return flag;
        },
      },
    },
  ],
});
export default erpTableDS;
