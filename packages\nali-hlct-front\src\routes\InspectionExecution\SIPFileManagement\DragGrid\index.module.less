.half-container {
  position: relative;
  flex-grow: 1;
  display: flex;
  overflow: hidden;
  .half-box {
    flex-grow: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .half-box:first-child {
    border-right: 1px solid #e4e4e4;
  }
  .half-container-drag {
    width: 0;
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 5;
    .drag-item {
      content: '';
      position: absolute;
      border-top: 0.07rem solid white;
      border-bottom: 0.07rem solid white;
      background: #A7ADD0;
      z-index: 2;
      height: 0.26rem;
      top: 50%;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
      width: 0.02rem;
      margin-left: -0.04rem;
      // width: 13px;
      // height: 13px;
      // margin-left: -1px;
      // border-radius: 50%;
      // background-color: #fff;
      // border: 2px solid #34bece;
      cursor: col-resize;
    }
  }
}
