// 加工件（在制品标识）DS
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.hmes.ProcessWorkorderMachinedPart';

const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'identificationSerch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identificationSerch`).d('在制标识'),
    },
    {
      name: 'materaialInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materaialInfo`).d('物料信息'),
      // required: true,
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('返修工单'),
    },
    {
      name: 'customerInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerInfo`).d('客户信息'),
    },
    {
      name: 'workOrderRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderRemark`).d('工单备注'),
    },
    {
      name: 'standardTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardTime`).d('标准节拍'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('在制标识'),
    },
    {
      name: 'inTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inTime`).d('进站时间'),
    },
    {
      name: 'currentOperationInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentOperationInfo`).d('当前工序'),
    },
    {
      name: 'upOperationInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.upOperationInfo`).d('上一工序'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('处置备注'),
    },
    {
      name: 'duration',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processedTime`).d('加工时长'),
    },
  ],
});

const tableDs = () => ({
  primaryKey: 'sequence',
  selection: 'single',
  fields: [
    {
      name: 'stepName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stepName`).d('步骤名称'),
    },
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sequence`).d('步骤顺序'),
    },
  ],
});

export { detailDS, tableDs };
