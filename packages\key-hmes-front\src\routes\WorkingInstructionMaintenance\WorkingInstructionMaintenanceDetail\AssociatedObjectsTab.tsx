/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-25 14:01:41
 * @LastEditTime: 2023-07-25 21:50:02
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo } from 'react';
import { observer } from 'mobx-react';
import { Table, Button, Modal, DataSet } from 'choerodon-ui/pro';
import { Tag, Popconfirm } from 'choerodon-ui';
import { ButtonColor, ButtonType, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import AssociatedObjectsDrawer from './AssociatedObjectsDrawer';
import { singleAssObjectDS } from '../stores/AssociatedObjectDS';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

const AssociatedObjectsTab = observer(props => {
  const { canEdit, assObjectsDs, detailDs, assObjectsRemoveDs } = props;
  const singleAssObjectDs = useMemo(() => new DataSet(singleAssObjectDS()), []);
  const typeName = {
    MATERIAL: intl.get(`${modelPrompt}.MATERIAL`).d('物料'),
    MATERIAL_CATEGORY: intl.get(`${modelPrompt}.MATERIAL_CATEGORY`).d('物料类别'),
    OPERATION: intl.get(`${modelPrompt}.OPERATION`).d('工艺'),
    WORKCELL: intl.get(`${modelPrompt}.WORKCELL`).d('工作单元'),
    EQUIPMENT: intl.get(`${modelPrompt}.EQUIPMENT`).d('设备'),
  };

  let _dataItemDrawer;

  const associatedObjectsDrawerClose = record => {
    if (!record) {
      assObjectsDs.remove(assObjectsDs.current);
    }
    _dataItemDrawer.close();
  };

  const associatedObjectsDrawerSubmit = async record => {
    const validate = await singleAssObjectDs.validate(false, true);
    if (!validate) {
      return;
    }
    if (singleAssObjectDs.toData().length === 0) {
      _dataItemDrawer.close();
      assObjectsDs.remove(assObjectsDs.current);
      return;
    }
    if (!record.get('lineNumber')) {
      const newLineNumber =
        (assObjectsDs.toData().sort((a, b) => b.lineNumber - a.lineNumber)[0].lineNumber || 0) + 10;
      record.set('lineNumber', newLineNumber);
    }
    record.set('detailList', singleAssObjectDs.toData());
    assObjectsDs.loadData((assObjectsDs.toData()));
    _dataItemDrawer.close();
  };

  const associatedObjectsModify = (record?) => {
    if (!record) {
      assObjectsDs.create({ tagGroupObjectId: -Date.parse(String(new Date())) });
    }
    _dataItemDrawer = Modal.open({
      key: Modal.key(),
      title: record
        ? intl.get(`${modelPrompt}.associatedObjectsEdit`).d('关联对象组合编辑')
        : intl.get(`${modelPrompt}.associatedObjectsCreate`).d('新建关联对象组合'),
      drawer: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <AssociatedObjectsDrawer
          singleAssObjectDs={singleAssObjectDs}
          canEdit={canEdit}
          record={record || assObjectsDs.current}
          parentSiteId={detailDs.current.get('siteId')}
        />
      ),
      onClose: () => {
        assObjectsDs.reset()
      },
      footer: !canEdit ? (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              associatedObjectsDrawerClose(record);
            }}
          >
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        </div>
      ) : (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              associatedObjectsDrawerClose(record);
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            type={ButtonType.submit}
            onClick={() => {
              associatedObjectsDrawerSubmit(record || assObjectsDs.current);
            }}
            color={ButtonColor.primary}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <Button
            icon="add"
            disabled={!canEdit || !detailDs.current.get('siteId')}
            funcType={FuncType.flat}
            onClick={() => associatedObjectsModify()}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        width: 70,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              assObjectsDs.remove(record);
              if(!record?.get('tagGroupObjectId')){
                assObjectsRemoveDs.loadData(assObjectsRemoveDs.toData().concat(record?.toData()));
              }
            }}
          >
            <Button icon="remove" disabled={!canEdit} funcType={FuncType.flat} size={Size.small} />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'lineNumber',
        align: ColumnAlign.left,
        width: 120,
        renderer: ({ value, record }) => (
          <a
            style={{ minWidth: '100%', display: 'inline-block' }}
            onClick={() => {
              associatedObjectsModify(record);
            }}
          >
            {value}
          </a>
        ),
      },
      {
        name: 'detailList',
        renderer: ({ record }) => {
          return (record?.get('detailList') || []).length && (record?.get('detailList') || []).map(item => (
            <Tag key={item.objectId} color="blue">
              {`${typeName[item.objectType]}：${item.objectDesc ? item.objectDesc : item.objectCode}`}
            </Tag>
          ));
        },
      },
    ],
    [canEdit],
  );

  return <Table dataSet={assObjectsDs} columns={columns} />;
});

export default AssociatedObjectsTab;
