import React, { useEffect, useRef, useCallback, useMemo, useState } from 'react';
import { filterNullValueObject, getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import DashboardCard from '../DashboardCard.jsx';
// import title from '../../assets/left.png';

// const modelPrompt = 'common.hmes.IncomeInspectionManagement';

const tenantId = getCurrentOrganizationId();
// 日到货单数趋势信息查询URL
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/daily-delivery/list`;

const DailyDelivery = (props) => {

  const { materialId ,timers} = props;
  const chartRef = useRef(null);
  const [xData, setXData] = useState<any>([]);
  const [inspectionQuantityData, setInspectionQuantityData] = useState<any>([]);
  const [checkQuantityData, setCheckQuantityData] = useState<any>([]);

  useEffect(()=>{
    let time;
    if(timers) {
      time = setInterval(() => {
        fetchData();
      }, (timers)*60000)
    } else if(materialId){
      fetchData();
    }

    return () => {
      clearInterval(time)
    }
  },[timers,materialId]);

  useEffect(() => {
    fetchData();
  }, [materialId])

  const fetchData = useCallback(async () => {
    const res = await request(url, {
      method: 'GET',
      query: filterNullValueObject({materialId}),
    });
    if (res.length) {
      setXData(res.map(i => i.inspectionDate));
      setInspectionQuantityData(res.map(i => i.inspectionQuantity));
      setCheckQuantityData(res.map(i => i.checkQuantity));
    }
  }, [materialId]);

  const option: any = useMemo(() => {
    return {
      title:{
        top:'3%',
        text: '日到货单数趋势图',
        left: '13%',
        textStyle: {
          fontWeight: 'bold', // 加粗
          color: '#00fff4',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
      },
      legend: {
        data: ['报检笔数', '检验笔数'],
        right: '5%',
        top: '5%',
        icon: 'circle',
        itemWidth: 14,
        itemHeight: 14,
        textStyle: {
          color: '#fff',
        },
      },
      grid: {
        top: '10%',
        left: '4%',
        right: '4%',
        bottom:'2%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: xData,
          // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          axisTick: { show: false },// 坐标刻度是否显示
          axisLine: { // 控制轴线样式
            lineStyle: {
              color: '#fff', // 设置轴线颜色
            },
          },
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisTick: { show: false },// 坐标刻度是否显示
          splitLine: { show:false }, // 是否显示背景分隔线
          axisLine: {show:false }, // 控制轴线
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      series: [
        {
          name: '报检笔数',
          type: 'line',
          color:'#0077ff',
          label: {
            show: true,
          },
          data: inspectionQuantityData,
        },
        {
          name: '检验笔数',
          type: 'line',
          color:'#00FFF4',
          label: {
            show: true,
          },
          data:checkQuantityData,
        },
      ],
    };
  }, [xData, inspectionQuantityData, checkQuantityData]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  return (
    <DashboardCard height="100%">
      <div style={{ width: '100%', height: '100%' }}>
        <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
      </div>
    </DashboardCard>
  );
};
export default DailyDelivery;
