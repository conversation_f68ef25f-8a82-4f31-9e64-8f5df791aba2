import React, {useEffect, useMemo, useRef, useState} from 'react';
import {
  Table,
  DataSet,
  Form,
  Lov,
  Output,
  TextField,
  Button,
  NumberField,
  Select,
  Modal,
} from 'choerodon-ui/pro';
import { Collapse, Spin, Popconfirm } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import myInstance from '@utils/myAxios';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { observer } from 'mobx-react';
import { useDataSetEvent } from 'utils/hooks';
import request from 'utils/request';
import { useRequest } from '@/components/tarzan-hooks';
import { tableDS, scanFormDS } from '../stores';
import {
  GetEquipmentDetail,
  // GetContainerInfo,
  GetBarcodeInfo,
  PackingMaterialLotInfo,
  // UnbindMaterialLotInfo,
  GetOnlyDefaultEquipmentInfo,
} from '../services';
import '../index.module.less';


const modelPrompt = 'tarzan.hmes.containerLoadRegister';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;
let printerModal;
const { Option } = Select;

const PopConfirmButton = observer(
  ({
    dataSet,
    disabled = false,
    confirmCallback,
    confirmMessage,
    icon,
    funcType,
    color,
    buttonText,
  }) => (
    <Popconfirm
      title={confirmMessage}
      onConfirm={() => confirmCallback(dataSet)}
      okText={intl.get('tarzan.common.button.confirm').d('确认')}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
    >
      <Button
        disabled={!dataSet.selected?.length || disabled}
        icon={icon}
        funcType={funcType}
        color={color}
      >
        {buttonText}
      </Button>
    </Popconfirm>
  ),
);

const ContainerLoadRegisterList = props => {
  const { scanFormDs, tableDs, history } = props;

  const containerBarRef = useRef<any>(null);
  const unbindBarcodeRef = useRef<any>(null);

  // 查询当前用户默认的唯一设备
  const { run: getOnlyDefaultEquipmentInfo, loading: getOnlyDefaultEquipmentLoading } = useRequest(
    GetOnlyDefaultEquipmentInfo(),
    {
      manual: true,
    },
  );

  // 获取容器装载信息
  const { run: getEquipmentDetail, loading: getEquipmentDetailLoading } = useRequest(
    GetEquipmentDetail(),
    {
      manual: true,
    },
  );

  // 获取容器装载信息
  const { run: getBarcodeInfo } = useRequest(GetBarcodeInfo(), {
    needPromise: true,
    showNotification: false,
    manual: true,
  });

  // 解绑物料批/Eo信息
  const { run: packingMaterialLotInfo, loading: packingLoading } = useRequest(
    PackingMaterialLotInfo(),
    {
      manual: true,
      needPromise: true,
      showNotification: false,
    },
  );

  const [unbindLoading,setUnbindLoading] = useState(false);

  useEffect(() => {
    getOnlyDefaultEquipmentInfo({
      onSuccess: res => {
        if (res.equipmentId) {
          handleChangeEquipment(res);
        }
      },
    });
  }, []);

  useDataSetEvent(scanFormDs, 'update', ({ name, record }) => {
    if (name === 'containerTypeObj' && record.get('containerTypeObj')) {
      record.init('containerCode', null);
      record.init('barcode', null);
      tableDs.loadData([]);
    }
  });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        title: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
        width: 120,
        lock: ColumnLock.left,
        renderer: ({ record, dataSet }) => {
          if (dataSet && record) {
            return dataSet.totalCount - record?.index;
          }
        },
      },
      {
        name: 'identification',
        lock: ColumnLock.left,
        minWidth: 180,
      },
      {
        name: 'materialName',
        minWidth: 180,
      },
      {
        name: 'qualityStatus',
        minWidth: 120,
      },
      {
        name: 'degradeLevel',
        minWidth: 100,
      },
      {
        name: 'degradeLevel1',
        align: ColumnAlign.center,
        minWidth: 100,
      },
      {
        name: 'degradeLevel2',
        minWidth: 100,
      },
      {
        name: 'degradeLevel3',
        minWidth: 100,
      },
      {
        name: 'degradeLevel4',
        minWidth: 100,
      },
      {
        name: 'degradeLevel5',
        minWidth: 100,
      },
      {
        name: 'degradeLevel6',
        minWidth: 100,
      },
      {
        name: 'degradeLevel7',
        minWidth: 100,
      },
      {
        name: 'degradeLevel8',
        minWidth: 100,
      },
      {
        name: 'degradeLevel9',
        minWidth: 100,
      },
      {
        name: 'degradeLevel10',
        minWidth: 100,
      },
      {
        name: 'qty',
        minWidth: 80,
      },
      {
        name: 'uomCode',
        minWidth: 80,
      },
      {
        name: 'materialCode',
        minWidth: 180,
      },
      {
        name: 'workOrderNum',
        minWidth: 180,
      },
      {
        name: 'scanIdentification',
        minWidth: 180,
      },
      {
        name: 'modelCode',
        minWidth: 180,
      },
    ];
  }, []);

  const messageModal = (message, message2, type) => {
    Modal.confirm({
      cancelButton: false,
      children: (
        <div>
          <p style={{ textAlign: 'center' }}>{message}</p>
          {message2 && <p style={{ textAlign: 'center' }}>{message2}</p>}
        </div>
      ),
      autoCenter: true,
      className: 'messageModal',
      onOk: () => {
        if (type === 'containerBarRef') {
          containerBarRef?.current?.focus();
        }
      },
    })
  }

  // 扫条码
  const handleScanBarcode = async () => {
    const _barcode = scanFormDs.current?.get('containerBarCode')?.trim() || scanFormDs.current?.get('identification');
    if (_barcode) {
      const _containerTypeId = scanFormDs.current?.get('containerTypeId');
      const _identificationId = scanFormDs.current?.get('identificationId') || null;
      const currentMaterialId = tableDs.records[0]?.get('materialId') || null;
      const currentDegradeLevel = tableDs.records[0]?.get('degradeLevel') || null;
      const oldIdentification = tableDs.records[0]?.get('identification') || null;
      const tempQty = tableDs.totalCount || 0;
      if (tableDs.data.every(item => item.get('identification') !== _barcode)) {
        containerBarRef?.current?.blur();
        const timeStart = new Date();
        const res = await getBarcodeInfo({
          params: {
            identification: _barcode,
            containerTypeId: _containerTypeId,
            scannedContainerId: _identificationId,
            packingLevel: scanFormDs.current?.get('packingLevel'),
            materialId: currentMaterialId,
            degradeLevel: currentDegradeLevel,
            oldIdentification,
          },
          // onFailed: () => {
          //   Modal.open({
          //     width: 300,
          //     children: (
          //       <Form record={scanFormDs.current}>
          //         <Output name="containerBarCode" disabled />
          //       </Form>
          //     ),
          //     onOk: () => {
          //       handleScanBarcode();
          //       containerBarRef?.current?.focus();
          //       containerBarRef?.current?.select();
          //     },
          //     onCancel: () => {
          //       containerBarRef?.current?.focus();
          //       containerBarRef?.current?.select();
          //     },
          //   });
          // },
        });
          // 判断扫描的是箱码还是条码
          // 扫的箱码就需要把当前的表格清空，
        if (res?.success) {
          if (res.rows?.identificationId) {
            handleReset().then(() => {
              scanFormDs.current?.set('standardLoadQty', res.rows?.standardLoadQty);
              // 更新箱码信息
              scanFormDs.current?.set('containerId', res.rows?.containerId || null);
              scanFormDs.current?.set('identification', res.rows?.identification || null);
              scanFormDs.current?.set('identificationId', res.rows?.identificationId || null);
              // 更新条码表格
              if (res.rows.barcodeList?.length) {
                tableDs.loadData([...res.rows.barcodeList]);
                scanFormDs.current?.set('currentQty', Number(tempQty) + Number(res.barcodeList?.length));
                console.log('success1', new Date() - timeStart);
              }
              containerBarRef?.current?.focus();
              containerBarRef?.current?.select();
            })
          } else {
            // 校验当前条码数量是否小于等于标准装箱数量；
            if (Number(res.rows?.standardLoadQty) >= Number(tempQty) + Number(res.rows.barcodeList?.length || 0)) {
              scanFormDs.current?.set('standardLoadQty', res.rows?.standardLoadQty);
              // 更新条码表格
              if (res.rows.barcodeList?.length) {
                res.rows.barcodeList?.forEach(item => {
                  tableDs.create({ ...item }, 0);
                });
                scanFormDs.current?.set('currentQty', Number(tempQty) + Number(res.rows.barcodeList?.length));
              }
              console.log('success2', new Date() - timeStart);
              containerBarRef?.current?.focus();
              containerBarRef?.current?.select();
            } else {
              containerBarRef?.current?.focus();
              containerBarRef?.current?.select();
              messageModal('装箱条码数量已达上限！', false, 'NO')
              // notification.error({ message: '装箱条码数量已达上限！', duration: 100, className: "notification-style" });
              return;
            }
          }
        } else {
          messageModal(`扫描箱码或条码：${_barcode}`, res?.message, 'containerBarRef')
        }
      } else {
        messageModal('已存在该条码，不允许重复扫描！', false, 'containerBarRef')
        // notification.error({ message: '已存在该条码，不允许重复扫描！', duration: 100, className: "notification-style" });
      }
    }
  };

  // 重置表格行
  const handleReset = async () => {
    tableDs.deleteAll(false).then(() => scanFormDs.current?.set('currentQty', null));
  };

  // 删除条码行
  const handleDelete = () => {
    const tempQty = tableDs.totalCount;
    const flag = tableDs.selected.every(item => !item.get('scanContainerId'));
    if (!flag) {
      messageModal('存在条码已绑定容器，无法删除！', false, "NO")
      // notification.error({
      //   message: '存在条码已绑定容器，无法删除！',
      //   duration: 100,
      //   className: "notification-style",
      // })
      return;
    }
    scanFormDs.current?.set('currentQty', Number(tempQty) - Number(tableDs.selected.length));
    tableDs.selected.forEach(record => tableDs.remove(record));
  };

  // 拿去解绑
  const handleUnbind = () => {
    const tempQty = tableDs.totalCount;
    if (!tableDs.selected.length || !tableDs.selected.every(item => !!item.get('scanContainerId'))) {
      messageModal('存在条码未绑定容器！', false, "NO")
      // notification.error({
      //   message: '存在条码未绑定容器！',
      //   duration: 100,
      //   className: "notification-style",
      // })
      return;
    }
    setUnbindLoading(true)
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/box/unload/ui`,{
      method: "POST",
      body: tableDs.selected.map(item => item.data),
    }).then(res=>{
      if(res?.success) {
        scanFormDs.current?.set('currentQty', Number(tempQty) - Number(tableDs.selected.length));
        handleScanBarcode();
        tableDs.selected.forEach(record => tableDs.remove(record));
        notification.success({});
      }
      setUnbindLoading(false)
    })
  };

  // 打包完工
  const handlePacking = async() => {
    const res = await packingMaterialLotInfo({
      params: {
        ...scanFormDs.current?.toData(),
        materialLotEoInfoList: tableDs.toData(),
      },
    });
    if (res?.success) {
      // notification.success({});
      messageModal('操作成功',false, 'NO')
      scanFormDs.current.set('standardLoadQty', res?.standardLoadQty || null);
      scanFormDs.current.set('identification', res?.identification);
      scanFormDs.current.set('containerId', res?.containerId);
      tableDs.loadData([]);
      scanFormDs.current.set('currentQty', null);
    } else {
      messageModal(res?.message, false, 'NO')
    }
  };

  // 跳转到装托卸托
  const handleGoToDetail = () => {
    if (!scanFormDs.current?.get('workcellId')) {
      messageModal('请先选择设备', false, 'NO')
      // notification.error({
      //   message: '请先选择设备',
      //   duration: 100,
      //   className: "notification-style",
      // })
      return;
    }
    history.push({
      pathname: '/hmes/container-load-register/detail',
      state: {
        workcellId: scanFormDs.current?.get('workcellId'),
        equipmentId: scanFormDs.current?.get('equipmentId'),
      },
    })
  };

  // 清除箱码
  const handleClearBoxCode = () => {
    scanFormDs.current?.init('containerBarCode', null);
    scanFormDs.current?.init('identification', null);
    scanFormDs.current?.init('identificationId', null);
    scanFormDs.current?.init('standardLoadQty', null);
    tableDs.loadData([]);
    scanFormDs.current?.init('currentQty', null);
  };

  const selectPrinter = (printerList) => {
    printerModal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.info.printer`).d('请选择打印机'),
      destroyOnClose: true,
      closable: true,
      style: {
        width: 400,
      },
      children: (
        <React.Fragment>
          <Form>
            <Select
              clearButton={false}
              onChange={(value) => handlePrint(value)}
              placeholder={intl.get(`${modelPrompt}.info.printer`).d('请选择打印机')}
            >
              {printerList.map(i => (
                <Option key={i.value} value={i}>
                  {i.meaning}
                </Option>
              ))}
            </Select>
          </Form>

        </React.Fragment>
      ),
      footer: null,
    });
  };

  // 打印
  const handlePrint = (printer) => {
    // const printCalibration = tableDs.selected.every((ele) => ele.get('containerCategory') && ele.get('containerCategory') === tableDs.selected[0].get('containerCategory'));
    // if (!printCalibration) {
    //   return notification.error({ message: intl.get(`${modelPrompt}.error.print`).d('请选择容器类别有值且相同的容器!') });
    // }
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/box/barcode-print`;
    myInstance
      .post(url, {
        workcellId: scanFormDs.current?.get('workcellId'),
        containerIdList: [scanFormDs.current?.get('containerId')],
        printer,
        codeType: 'Y',
      })
      .then(res => {
        if (res.data.success) {
          // notification.success({
          //   message: intl.get(`${modelPrompt}.success.print`).d('打印成功!'),
          // });
          messageModal(intl.get(`${modelPrompt}.success.print`).d('打印成功!'), false, 'NO')
          scanFormDs.current?.init('containerBarCode', null);
          scanFormDs.current?.set('identificationId', null)
          scanFormDs.current?.set('identification', null)
          scanFormDs.current?.init('standardLoadQty', null);
          tableDs.loadData([]);
          scanFormDs.current?.init('currentQty', null);
          printerModal?.close();
        } else {
          if (res.data.statusCode === "PRINTER_CHOOSE") {
            return selectPrinter(res.data.attr);
          }
          printerModal?.close();
          messageModal(res.data.message || intl.get(`${modelPrompt}.error.print`).d('打印失败!'), false, 'NO')
          // notification.error({
          //   message: res.data.message || intl.get(`${modelPrompt}.error.print`).d('打印失败!'),
          //   duration: 100,
          //   className: "notification-style",
          // });
        }
      });
  };

  // 打印
  const handlePrintCode = (printer) => {
    // const printCalibration = tableDs.selected.every((ele) => ele.get('containerCategory') && ele.get('containerCategory') === tableDs.selected[0].get('containerCategory'));
    // if (!printCalibration) {
    //   return notification.error({ message: intl.get(`${modelPrompt}.error.print`).d('请选择容器类别有值且相同的容器!') });
    // }
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/box/barcode-print`;
    myInstance
      .post(url, {
        workcellId: scanFormDs.current?.get('workcellId'),
        containerIdList: [scanFormDs.current?.get('containerId')],
        printer,
        codeType: 'N',
      })
      .then(res => {
        if (res.data.success) {
          // notification.success({
          //   message: intl.get(`${modelPrompt}.success.print`).d('打印成功!'),
          // });
          messageModal(intl.get(`${modelPrompt}.success.print`).d('打印成功!'), false, 'NO')
          scanFormDs.current?.init('containerBarCode', null);
          scanFormDs.current?.set('identificationId', null)
          scanFormDs.current?.set('identification', null)
          scanFormDs.current?.init('standardLoadQty', null);
          tableDs.loadData([]);
          scanFormDs.current?.init('currentQty', null);
          printerModal?.close();
        } else {
          if (res.data.statusCode === "PRINTER_CHOOSE") {
            return selectPrinter(res.data.attr);
          }
          printerModal?.close();
          messageModal(res.data.message || intl.get(`${modelPrompt}.error.print`).d('打印失败!'), false, 'NO')
          // notification.error({
          //   message: res.data.message || intl.get(`${modelPrompt}.error.print`).d('打印失败!'),
          //   duration: 100,
          //   className: "notification-style",
          // });
        }
      });
  };

  const handleChangeEquipment = value => {
    if (!value) {
      return;
    }
    getEquipmentDetail({
      params: {
        equipmentCode: value.equipmentCode,
        workcellCode: value.workcellCode,
      },
      onSuccess: res => {
        scanFormDs.current?.set('equpimentObj', res);
      },
      onFailed: () => {
        scanFormDs.current?.set('equpimentObj', null);
      },
    });
  };

  const handleUnbindBarcodeRef = () => {
    const unbindBarcode = scanFormDs.current?.get('unbindBarcode');
    const index = tableDs.findIndex(
      r => r.get('identification') === unbindBarcode,
    );
    tableDs.select(index);
    unbindBarcodeRef?.current?.focus();
    unbindBarcodeRef?.current?.select();
  };

  return (
    <div className="hmes-style">
      <Spin
        spinning={
          getOnlyDefaultEquipmentLoading ||
          getEquipmentDetailLoading ||
          unbindLoading ||
          packingLoading
        }
      >
        <Header title={intl.get(`${modelPrompt}.title.list`).d('打包装箱-MES')}>
          <Button
            color={ButtonColor.primary}
            onClick={handleUnbind}
            loading={unbindLoading}
          >
            {intl.get(`${modelPrompt}.button.unbind`).d('拿取解绑')}
          </Button>
          <Button
            color={ButtonColor.primary}
            onClick={handlePacking}
            loading={packingLoading}
            disabled={!tableDs.length}
          >
            {intl.get(`${modelPrompt}.button.packing`).d('打包完工')}
          </Button>
          <Button color={ButtonColor.primary} onClick={handleGoToDetail} loading={packingLoading} disabled={!scanFormDs.current?.get('workcellId')}>
            {intl.get(`${modelPrompt}.button.register`).d('装托卸托')}
          </Button>
          <Button color={ButtonColor.primary} onClick={handleClearBoxCode} disabled={!scanFormDs.current?.get('identificationId')}>
            {intl.get(`${modelPrompt}.button.register`).d('清除箱码')}
          </Button>
          <Button
            color={ButtonColor.primary}
            onClick={handlePrint}
            loading={packingLoading}
            disabled={!scanFormDs.current?.get('identification')}
          >
            {intl.get(`${modelPrompt}.button.print`).d('打印')}
          </Button>
          <Button
            color={ButtonColor.primary}
            onClick={handlePrintCode}
            loading={packingLoading}
            disabled={!scanFormDs.current?.get('identification')}
          >
            {intl.get(`${modelPrompt}.button.print-code`).d('打印-条形码')}
          </Button>
        </Header>
        <Content>
          <Form dataSet={scanFormDs} columns={4}>
            <Lov name="equpimentObj" onChange={handleChangeEquipment} />
            <Output name="equipmentName" newLine />
            <Output name="equipmentName" />
            <Output name="workcellName" />
            <Output name="workcellCode" />
            <Output name="operationDesc" />
            <Output name="operationName" />
            <Output name="prodLineName" />
            <Output name="prodLineCode" />
          </Form>
          <Collapse bordered={false} defaultActiveKey={['barcodeInfo']}>
            <Panel
              key="barcodeInfo"
              header={intl.get(`${modelPrompt}.panel.barcodeInfo`).d('装载条码信息')}
            >
              <Form dataSet={scanFormDs} columns={3}>
                <Lov name="containerTypeObj" />
                <TextField name="identification" disabled />
                <NumberField name="currentQty" disabled />
                <TextField
                  ref={containerBarRef}
                  name="containerBarCode"
                  onEnterDown={handleScanBarcode}
                />
                <NumberField name="standardLoadQty" disabled />
                <TextField
                  newLine
                  ref={unbindBarcodeRef}
                  name="unbindBarcode"
                  onEnterDown={handleUnbindBarcodeRef}
                />
              </Form>
              <Table
                buttons={[
                  <Button icon="delete" disabled={!tableDs.records.length} onClick={handleReset}>
                    {intl.get('tarzan.common.button.reset').d('重置')}
                  </Button>,
                  <PopConfirmButton
                    dataSet={tableDs}
                    confirmCallback={handleDelete}
                    confirmMessage={intl
                      .get(`tarzan.common.message.confirm.delete`)
                      .d('是否确认删除?')}
                    icon="delete"
                    funcType={FuncType.flat}
                    color={ButtonColor.red}
                    buttonText={intl.get('tarzan.common.button.delete').d('删除')}
                    disabled={false}
                  />,
                ]}
                dataSet={tableDs}
                columns={columns}
                searchCode="containerLoadRegisterSearch"
                customizedCode="containerLoadRegisterCust"
                virtual
                style={{ height: 500 }}
              />
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const scanFormDs = new DataSet({ ...scanFormDS() });
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        scanFormDs,
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true },
  )(ContainerLoadRegisterList),
);
