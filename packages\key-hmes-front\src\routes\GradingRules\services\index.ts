import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 查明细
export function GetGradingRulesDetail() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-grading-rules/query`,
    method: 'GET',
  };
}

// Post请求
export function PostMethod() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/save/ui`,
    method: 'POST',
  };
}

/**
 * 保存分档规则
 * @function SaveGradingRules
 */
export function SaveGradingRules() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-grading-rules`,
    method: 'POST',
  };
}
