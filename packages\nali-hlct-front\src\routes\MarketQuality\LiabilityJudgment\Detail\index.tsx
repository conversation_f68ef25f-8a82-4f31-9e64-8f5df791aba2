/**
 * @Description: 一次解析责任判定-详情
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  TextField,
  Table, Switch, Select, DateTimePicker, Modal,
} from 'choerodon-ui/pro';
import {Badge, Collapse} from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { useDataSetEvent } from 'utils/hooks';
import { Header, Content } from 'components/Page';
import {ButtonColor, FuncType} from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import {observer} from "mobx-react";
import QuestionButton from '../components/QuestionButton';
import { SaveVerification, GetDefaultSite, QueryMaterialLotInfo } from '../services';
import {
  detailDS,
  taskDS,
} from '../stores/DetailDS';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.LiabilityJudgment';


const TaskDetail = props => {
  const {
    history,
    match: { params },
  } = props;
  const prianalResjudgId = params.id;

  const [canEdit, setCanEdit] = useState(false);
  const taskDs = useMemo(() => new DataSet(taskDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          itemList: taskDs,
        },
      }),
    [],
  );

  const { run: saveVerification, loading: saveLoading } = useRequest(SaveVerification(), {
    manual: true,
  });
  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });
  // 查询物料批信息
  const { run: queryMaterialLotInfo, loading: queryLoading } = useRequest(QueryMaterialLotInfo(), {
    manual: true,
  });

  useDataSetEvent(detailDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialLov':
        record.set('productionVersionLov', {});
        record.set('defaultBomLov', {});
        record.set('defaultRouterLov', {});
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    if (prianalResjudgId === 'create') {
      // 新建时
      setCanEdit(true);
      getDefaultSite({
        onSuccess: res => {
          if (res?.siteId) {
            detailDs.current?.set('siteLov', res);
          }
        },
      });
      return;
    }
    // 编辑时
    handleQueryDetail(prianalResjudgId);
  }, [prianalResjudgId]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('prianalResjudgId', id);
    detailDs.query();
  };

  const handleEdit = () => {
    setCanEdit(true);
  };

  const handleCancel = useCallback(() => {
    if (prianalResjudgId === 'create') {
      history.push('/hwms/market-quality/liability-judgment/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(prianalResjudgId);
    }
  }, []);

  const taskColumns: any = useMemo(
    () => [
      { name: 'prianalResjudgItemNum' },
      {
        name: 'partsCode',
        width: 130,
        editor: canEdit,
      },
      {
        name: 'partsName',
        width: 130,
        editor: canEdit,
      },
      {
        name: 'materialLov',
        width: 130,
        editor: canEdit,
      },
      {
        name: 'ypPartsName',
        width: 130,
      },
      {
        name: 'primaryUnitFlag',
        width: 130,
        editor: canEdit && <Switch />,
        renderer: ({ value, record }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={record!.getField('primaryUnitFlag')!.getText()}
            />
          );
        },
      },
      {
        name: 'majorFaultMode',
        width: 130,
        editor: canEdit,
      },
      {
        name: 'majorDivision1',
        width: 130,
        editor: canEdit,
      },
      {
        name: 'majorDivision2',
        width: 130,
        editor: canEdit,
      },
      {
        name: 'majorDivision3',
        width: 130,
        editor: canEdit,
      },
      {
        name: 'description',
        width: 130,
        editor: canEdit,
      },
      {
        name: 'ware',
        width: 130,
        editor: record => canEdit && <Select onChange={() => record?.set('softwareVersion', undefined)} />,
      },
      {
        name: 'softwareVersion',
        width: 130,
        editor: canEdit,
      },
      {
        name: 'problemCreateFlag',
        width: 150,
      },
      {
        name: 'problemCode',
        width: 130,
        renderer: ({ value, record }) => {
          if (!value) {
            return;
          }
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/problem-management/problem-management-platform/dist/${record!.get(
                    'problemId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
    ],
    [canEdit],
  );

  // @ts-ignore
  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    saveVerification({
      params: detailDs.toData()[0],
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        if (prianalResjudgId === 'create') {
          history.push(`/hwms/market-quality/liability-judgment/${res}`);
        } else {
          handleQueryDetail(res);
        }
      },
    });
  };

  const RenderDeleteAddButton = observer(({ dataSet, detailDs, canEdit }) => {
    return (
      <>
        <Button
          icon="delete"
          funcType={FuncType.flat}
          color={ButtonColor.red}
          disabled={!canEdit || !dataSet?.selected.length}
          onClick={() => dataSet.remove(dataSet?.selected)}
        >
          {intl.get(`${modelPrompt}.button.delete`).d('删除')}
        </Button>
        <Button
          icon="playlist_add"
          disabled={!canEdit}
          funcType={FuncType.flat}
          onClick={() => {
            let maxNum = 0;
            dataSet.forEach(_record => {
              if (_record?.get('itemSequence') > maxNum) {
                maxNum = _record?.get('itemSequence');
              }
            });
            dataSet.create({
              itemSequence: maxNum + 1,
              partsCode: detailDs.current.get('partsCode'),
              partsName: detailDs.current.get('partsName'),
              ypPartsCode: detailDs.current.get('ypPartsCode'),
              ypPartsName: detailDs.current.get('ypPartsName'),
              ypPartsId: detailDs.current.get('ypPartsId'),
            });
          }}
        >
          {intl.get(`${modelPrompt}.button.add`).d('新增')}
        </Button>
      </>
    );
  });

  const handleChangeReason = () => {
    detailDs.current?.set('reasonObj', undefined);
    detailDs.current?.set('aResponsibilityRatio', undefined);
    detailDs.current?.set('bResponsibilityRatio', undefined);
  }

  const changeReasonObj = (value) => {
    const regex = /%/g;
    if (value) {
      const { aResponsibilityRatio, bResponsibilityRatio } = value;
      detailDs.current?.set('aResponsibilityRatio', aResponsibilityRatio.replace(regex, ''));
      detailDs.current?.set('bResponsibilityRatio', bResponsibilityRatio.replace(regex, ''));
    } else {
      detailDs.current?.set('aResponsibilityRatio', undefined);
      detailDs.current?.set('bResponsibilityRatio', undefined);
    }
  }

  // 清空质量反馈单带出的关联信息
  const handleUpdateRelateInfo: (value?: any, siteFlag?: boolean) => void = (value = {}, siteFlag = false) => {
    const {
      theme = undefined,
      batteryMatLotId = undefined,
      batteryMatLotCode = undefined,
      batteryMatLotProductType = undefined,
      items = [],
    } = value || {};
    if (siteFlag) {
      detailDs.current?.set('QaNumObj', undefined);
    }
    detailDs.current?.set('theme', theme);
    detailDs.current?.set('batteryMatLotId', batteryMatLotId);
    detailDs.current?.set('batteryMatLotCode', batteryMatLotCode);
    detailDs.current?.set('batteryMatLotModel', batteryMatLotProductType);
    taskDs.loadData([]);
    (items || []).forEach((item) => {
      const _record = taskDs.create({});
      _record?.set('partsCode', item?.partsCode);
      _record?.set('partsName', item?.partsName);
      _record?.set('ypPartsCode', item?.ypPartsCode);
      _record?.set('ypPartsName', item?.ypPartsName);
      _record?.set('ypPartsId', item?.ypPartsId);
      _record?.set('primaryUnitFlag', item?.primaryUnitFlag);
    })
  }

  const handleChangeSiteLov = (_, oldValue) => {
    if (oldValue && detailDs.current?.get('feedbackId')) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.clearQualityFeedbackDataAnd`)
              .d('将清空质量反馈单及根据质量反馈单带入的数据，是否确认操作？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          handleUpdateRelateInfo(undefined, true);
        } else {
          detailDs.current?.set('siteLov', oldValue);
        }
      });
    } else {
      handleUpdateRelateInfo(undefined, true);
    }
  };

  const handleChangeQaNumObj = (value, oldValue) => {
    if (oldValue) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.clearQualityFeedbackData`)
              .d('将清空根据质量反馈单带入的数据，是否确认操作？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          handleUpdateRelateInfo(value);
        } else {
          detailDs.current?.set('QaNumObj', oldValue);
        }
      });
    } else {
      handleUpdateRelateInfo(value);
    }
  };

  const handleChangeMaterialLot = value => {
    if (value?.materialId) {
      queryMaterialLotInfo({
        params: value?.materialId,
        onSuccess: res => {
          detailDs.current?.set('batteryMatLotModel', res);
        },
      });
    } else {
      detailDs.current?.set('batteryMatLotModel', undefined);
    }
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || siteLoading || queryLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('一次解析责任判定详情')}
          backPath="/hwms/market-quality/liability-judgment/list"
        >
          {
            canEdit ? (
              <>
                <Button
                  color={ButtonColor.primary}
                  icon="save"
                  onClick={handleSave}
                >
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
                <Button icon="close" onClick={handleCancel}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
              </>
            ) : (
              <>
                <PermissionButton
                  type="c7n-pro"
                  permissionList={[
                    {
                      code: `LiabilityJudgment.button.edit`,
                      type: 'button',
                      meaning: '一次解析责任判定详情-编辑',
                    },
                  ]}
                  color={ButtonColor.primary}
                  onClick={handleEdit}
                >
                  {intl.get('hzero.common.button.edit').d('编辑')}
                </PermissionButton>
                {prianalResjudgId && <QuestionButton lineDs={taskDs} headDs={detailDs} history={history} />}
              </>
            )
          }
        </Header>
        <Content>
          <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
            <TextField name="prianalResjudgCode" />
            <Lov name="siteLov" onChange={handleChangeSiteLov} />
            <Lov name="QaNumObj" onChange={handleChangeQaNumObj}/>
            <TextField name="theme" />
            <Lov name="batteryMatLotLov" onChange={handleChangeMaterialLot} />
            <TextField name="batteryMatLotModel" />
            <Select name='qualityProblemFlay' />
            <Select name='analResDivision' />
            <Select name='severityLevel' />
            <Select name='accidentalTag' />
            <Select name='frequency' />
            <Lov name="departmentLov" onChange={() => detailDs.current?.set('responsibleUserObj', undefined)} />
            <Lov name="responsibleUserObj" />
            <Select name='reasonClassify' onChange={handleChangeReason} />
            <Lov name="reasonObj" onChange={changeReasonObj} />
            <TextField name="reason" />
            <TextField name="bResponsibilityRatio" suffix="%" />
            <TextField name="aResponsibilityRatio" suffix="%" />
            <DateTimePicker name="creationDate" />
            <DateTimePicker name="lastUpdateDate" />
          </Form>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'taskInfo',
            ]}
          >
            <Panel key="taskInfo" header={intl.get(`${modelPrompt}.title.partsInfo`).d('零部件信息')}>
              <Table
                buttons={[
                  <>
                    <RenderDeleteAddButton dataSet={taskDs} detailDs={detailDs} canEdit={canEdit} />
                  </>,
                ]}
                dataSet={taskDs}
                columns={taskColumns}
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(TaskDetail);
