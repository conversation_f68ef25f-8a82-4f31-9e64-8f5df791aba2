/**
 * @Description: MSA分析管理平台-稳定性图表界面
 * @Author: <EMAIL>
 * @Date: 2023/8/24 15:42
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, TextField, TextArea } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import echarts from 'echarts';
import {
  max as lodashMax,
  min as lodashMin,
  floor as lodashFloor,
  ceil as lodashCeil,
} from 'lodash';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';
const { Panel } = Collapse;
let xBarRChart; // xBarR图chart

const StabilityGraphic = ({ dataSoure, analyseResultDs }) => {
  const [pageInit, setPageInit] = useState(false);
  const xBarRChartRef = useRef(null);

  useEffect(() => {
    if (!dataSoure.data?.length) {
      return;
    }
    if (!pageInit) {
      xBarRChart = echarts.init(xBarRChartRef.current);
      window.onresize = () => {
        setTimeout(() => {
          xBarRChart.resize();
        }, 300);
      };
    }
    handleInitXBarRChart(dataSoure);
  }, [dataSoure?.data, dataSoure?.tableInfo]);

  const getRulePercision = (dataRule: any) => {
    const { centerLine, upperControlLimit, lowerControlLimit } = dataRule;
    const data = [centerLine, upperControlLimit, lowerControlLimit];
    let maxDigits;
    data.forEach((item: number) => {
      const _digits = item.toString().split('.')[1]?.length || 0;
      if (_digits > maxDigits || !maxDigits) {
        maxDigits = _digits;
      }
    });
    return maxDigits;
  };

  const handleInitXBarRChart = dataSource => {
    const {
      data,
      mainChartRule,
      secondaryChartRule,
      mainChartMax,
      mainChartMin,
      secondChartMax,
      secondChartMin,
      callipers,
      mainData,
      secondData,
    } = handleFormatXBarData(dataSource);
    const formatLabel = {
      formatter: value => {
        return `${value.name}:${value.data.yAxis}`;
      },
      color: '#000',
      fontSize: 10,
    };
    const option: any = {
      backgroundColor: '#fff',
      title: [
        {
          text: intl.get(`${modelPrompt}.title.Xbar`).d('XBar控制图'),
          left: 'center',
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
        {
          top: '45%',
          text: intl.get(`${modelPrompt}.title.r`).d('R控制图'),
          left: 'center',
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
      ],
      dataZoom:
        data.length > 50
          ? [
              {
                realtime: true,
                startValue: 0,
                endValue: data.length > 1000 ? 1000 : data.length, // 要根据数据量改
                minValueSpan: 50,
                maxValueSpan: data.length > 1000 ? 1000 : data.length,
                xAxisIndex: [0, 1],
              },
              {
                type: 'inside',
                realtime: true,
                startValue: 0,
                endValue: data.length > 1000 ? 1000 : data.length, // 要根据数据量改
                minValueSpan: 50,
                maxValueSpan: data.length > 1000 ? 1000 : data.length,
                xAxisIndex: [0, 1],
              },
            ]
          : [],
      yAxis: [
        {
          type: 'value',
          max: lodashCeil(lodashMax([mainChartRule.upperControlLimit, mainChartMax]) * 100) / 100,
          min: lodashFloor(lodashMin([mainChartRule.lowerControlLimit, mainChartMin]) * 100) / 100,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.03)',
            },
          },
        },
        {
          gridIndex: 1,
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.03)',
            },
          },
          max:
            lodashCeil(lodashMax([secondaryChartRule.upperControlLimit, secondChartMax]) * 100) /
            100,
          min:
            lodashFloor(lodashMin([secondaryChartRule.lowerControlLimit, secondChartMin]) * 100) /
            100,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: callipers,
          axisTick: {
            show: true,
            alignWithLabel: true,
          },
        },
        {
          gridIndex: 1,
          type: 'category',
          data: callipers,
          axisTick: {
            show: true,
            alignWithLabel: true,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          animation: false,
        },
        borderColor: '#fff',
        backgroundColor: '#fff',
        extraCssText: 'box-shadow: 5px 5px 5px #888888;',
        textStyle: {
          color: '#4c4c4c',
        },
        formatter: '{b0}<br />{a0}: {c0}<br />{a1}: {c1}',
      },
      axisPointer: {
        link: { xAxisIndex: 'all' },
      },
      grid: [
        {
          id: 'main',
          left: 70,
          right: 100,
          top: '5%',
          height: '35%',
        },
        {
          id: 'second',
          left: 70,
          right: 100,
          top: '50%',
          height: '35%',
        },
      ],
      series: [
        {
          name: intl.get(`${modelPrompt}.title.average`).d('均值'),
          type: 'line',
          symbol: 'circle',
          symbolSize: 6,
          hoverAnimation: false,
          color: '#1890ff',
          markLine: {
            silent: true,
            symbol: 'none',
            precision: getRulePercision(mainChartRule),
            data: [
              {
                name: 'CL',
                yAxis: lodashFloor(mainChartRule.centerLine, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#b8b8b8',
                  width: 1.2,
                },
              },
              {
                name: 'UCL',
                yAxis: lodashFloor(mainChartRule.upperControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
              {
                name: 'LCL',
                yAxis: lodashFloor(mainChartRule.lowerControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
            ],
          },
          data: mainData,
        },
        {
          name: intl.get(`${modelPrompt}.title.range`).d('极差'),
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 1,
          symbol: 'circle',
          symbolSize: 6,
          hoverAnimation: false,
          color: '#1890ff',
          markLine: {
            silent: true,
            symbol: 'none',
            precision: getRulePercision(secondaryChartRule),
            data: [
              {
                name: 'CL',
                yAxis: lodashFloor(secondaryChartRule.centerLine, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#b8b8b8',
                  width: 1.2,
                },
              },
              {
                name: 'UCL',
                yAxis: lodashFloor(secondaryChartRule.upperControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
              {
                name: 'LCL',
                yAxis: lodashFloor(secondaryChartRule.lowerControlLimit, 4),
                label: formatLabel,
                lineStyle: {
                  color: '#f71a1b',
                  width: 1.2,
                },
              },
            ],
          },
          data: secondData,
        },
      ],
    };
    xBarRChart.setOption(option, true);
    // 初始化chart事件绑定
    if (!pageInit) {
      setPageInit(true);
    }
  };

  const handleFormatXBarData = dataSource => {
    const { data, xBarChartRule, rChartRule } = dataSource;
    const dataIdList: any[] = []; // 数据Id列表
    const mainData: any[] = []; // 主图数据
    const secondData: any[] = []; // 次图数据
    const xTickLabelList: any[] = []; // x轴坐标名称
    let mainChartMax;
    let mainChartMin;
    let secondChartMax;
    let secondChartMin;
    if (data) {
      data.forEach(item => {
        if (item.xBarStatsValue > mainChartMax || !mainChartMax) {
          mainChartMax = item.xBarStatsValue;
        }
        if (item.xBarStatsValue < mainChartMin || !mainChartMin) {
          mainChartMin = item.xBarStatsValue;
        }
        if (item.rStatsValue > secondChartMax || secondChartMax === undefined) {
          secondChartMax = item.rStatsValue;
        }
        if (item.rStatsValue < secondChartMin || secondChartMin === undefined) {
          secondChartMin = item.rStatsValue;
        }
        dataIdList.push(item.subgroupIndex);
        mainData.push(item.xBarStatsValue);
        secondData.push(item.rStatsValue);
        xTickLabelList.push(item.subgroupIndex);
      });
    }
    return {
      data,
      mainChartRule: xBarChartRule,
      secondaryChartRule: rChartRule,
      dataIdList,
      mainData,
      secondData,
      callipers: xTickLabelList,
      mainChartMax,
      mainChartMin,
      secondChartMax,
      secondChartMin,
    };
  };

  const RenderHelp = () => {
    const info = intl.get(`${modelPrompt}.stability.help`).d('遵循控制图八项判异准则：<br />1、1点落在A区以外<br />2、连续3点中有2点在中心线同一侧的B区外<即A区内><br />3、连续5点中有4点在中心线同一侧的C区以外<br />4、连续6点递增或递减，即连成一串<br />5、连续8点在中心线两侧，但没有一点在C区中<br />6、连续9点落在中心线同一侧<br />7、连续14点相邻点上下交替<br />8、连续15点在C区中心线上下，即全部在C区内');
    const descriptionList = info.split('<br />');
    return (
      <div>
        {descriptionList.map(item => (
          <p>{item}</p>
        ))}
      </div>
    );
  };

  return (
    <div>
      {Boolean(dataSoure?.tableInfo?.length) && (
        <Collapse bordered={false} defaultActiveKey={['analyseContent']}>
          <Panel key="analyseContent" header={intl.get(`${modelPrompt}.analyseContent`).d('分析内容')}>
            <div
              id="bias-xBarR-chart"
              ref={xBarRChartRef}
              style={{
                height: '500px',
              }}
            />
            <Form labelWidth={100} disabled dataSet={analyseResultDs}>
              <TextField name="msaResult" help={<RenderHelp />} />
              <TextArea name="msaConclusion" rows={7} />
            </Form>
          </Panel>
        </Collapse>
      )}
    </div>
  );
};

export default StabilityGraphic;
