/*
 * @Description: 市场活动评估单-主界面DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-15 10:57:49
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-05 10:17:49
 */
import React, { useMemo, useCallback } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import ExcelExport from 'components/ExcelExport';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { isNil } from 'lodash';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnLock, ColumnAlign } from 'choerodon-ui/pro/es/table/enum';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores';

const modelPrompt = 'tarzan.qms.marketActive.marketEstimateDoc';
const tenantId = getCurrentOrganizationId();

const MarketEstimateList = props => {
  const { tableDs, history } = props;

  const renderStatusTag = record => {
    const { marketEstimateStatus } = record.toData();
    if (!marketEstimateStatus) {
      return;
    }
    let color;
    switch (marketEstimateStatus) {
      case 'NEW':
        color = 'green';
        break;
      case 'REVIEWING':
        color = 'orange';
        break;
      case 'CLOSED':
        color = 'gray';
        break;
      default:
        color = 'geekblue';
    }
    return <Tag color={color}>{record.getField('marketEstimateStatus').getText()}</Tag>
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'marketEstimateCode',
        width: 180,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/market-quality/market-estimate-doc/dist/${record!.get(
                    'marketEstimateId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'siteName', width: 180 },
      { 
        name: 'marketEstimateStatus',
        renderer: ({ record }) => renderStatusTag(record),
      },
      { name: 'marketEstimateResult' },
      { name: 'responsibleDepartmentName' },
      { name: 'faultPhenomenon' },
      { name: 'occurrence' },
      { name: 'severityLevel' },
      { name: 'batteryPackModel' },
      { name: 'ypItemCode', width: 180 },
      { name: 'ypItemName', width: 180 },
      { name: 'itemCode' },
      { name: 'itemName', width: 180 },
      { name: 'customerName' },
      { name: 'problemCode', width: 180 },
      { name: 'problemTitle', width: 230 },
      { name: 'applyDepartmentName' },
      { name: 'vehicleModel' },
      { name: 'reason', width: 230 },
      { name: 'objectQty' },
      { name: 'objectRange' },
      { name: 'enclosure' },
      { name: 'createdByName' },
      { name: 'creationDate', align: ColumnAlign.center, width: 150 },
      { name: 'reviewInfo', width: 230 },
      { name: 'reviewByName' },
      { name: 'reviewTime', align: ColumnAlign.center, width: 150 },
      { name: 'reviewEnclosure' },
    ];
  }, []);

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
      marketEstimateIdList: tableDs.selected?.map(_record => _record?.get('marketEstimateId')),
    };
  };

  const handleAdd = useCallback(() => {
    history.push(`/hwms/market-quality/market-estimate-doc/dist/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('市场活动评估')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${modelPrompt}.list.button.add`,
              type: 'button',
              meaning: '列表页-新建按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <ExcelExport
          exportAsync
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-market-estimates/export/ui`}
          queryParams={getExportQueryParams}
        >
          {intl.get(`${modelPrompt}.button.export`).d('导出')}
        </ExcelExport>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="marketEstimateDoc_searchCode"
          customizedCode="marketEstimateDoc_customizedCode"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MarketEstimateList),
);
