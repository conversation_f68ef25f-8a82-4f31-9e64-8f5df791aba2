/*
 * @Description: 事务类型接口关系DS
 * @Author: YinWQ
 * @Date: 2023-07-19 09:53:38
 * @LastEditors: YinWQ
 * @LastEditTime: 2023-07-20 09:39:05
 */
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.model.event.type.interface.relationship.table';
const API = `${BASIC.HWMS_BASIC}/v1/${getCurrentOrganizationId()}`;
// const API = `/key-focus-mes-30607/v1/${getCurrentOrganizationId()}`;

const MainTableDS = () => ({
  transport: {
    read: config => {
      return {
        url: `${API}/trans-type/rel/list/ui`,
        method: 'POST',
        config,
        transformResponse: res => {
          const result = JSON.parse(res).rows || {
            content: [],
            empty: true,
            number: 0,
            numberOfElements: 0,
            size: 10,
            totalElements: 0,
            totalPages: 1,
          };
          return result;
        },
      };
    },
  },
  queryFields: [
    {
      name: 'nameSpace',
      type: 'string',
      label: intl.get(`${modelPrompt}.nameSpace`).d('服务命名空间'),
    },
    {
      name: 'serverCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.serverCode`).d('服务代码'),
    },
    {
      name: 'interfaceCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.interfaceCode`).d('接口代码'),
    },
    {
      name: 'erpSystemType',
      type: 'string',
      label: intl.get(`${modelPrompt}.erpSystemType`).d('ERP系统名称'),
    },
    {
      name: 'transTypeCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.transTypeCode`).d('ERP事务类型'),
    },
    {
      name: 'transcationTransferNum',
      type: 'number',
      label: intl.get(`${modelPrompt}.transcationTransferNum`).d('一次传输数据量'),
    },
  ],
  fields: [
    {
      name: 'nameSpace',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.nameSpace`).d('服务命名空间'),
    },
    {
      name: 'serverCode',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.serverCode`).d('服务代码'),
    },
    {
      name: 'interfaceCode',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.interfaceCode`).d('接口代码'),
    },
    {
      name: 'erpSystemType',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.erpSystemType`).d('ERP系统名称'),
    },
    {
      name: 'transTypeCode',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.transTypeCode`).d('ERP事务类型'),
    },
    {
      name: 'transcationTransferNum',
      type: 'number',
      label: intl.get(`${modelPrompt}.transcationTransferNum`).d('一次传输数据量'),
    },
  ],
});
export default MainTableDS;
