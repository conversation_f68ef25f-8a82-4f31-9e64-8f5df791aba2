/**
 * @Description: 检验方案模板维护-历史DS
 * @Author: <EMAIL>
 * @Date: 2023/3/29 19:35
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.inspectionSchemeTemplate';
const modelPromptGroup = 'tarzan.qms.inspectGroupMaintenance.model';
const tenantId = getCurrentOrganizationId();

// 检验方案-ds
const inspectSchemeTemplateDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  primaryKey: 'inspectSchemeTmpId',
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-scheme-tmp/detail/his/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'operationTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationType`).d('操作类型'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
    {
      type: FieldType.string,
      name: 'inspectSchemeTmpCode',
      label: intl.get(`${modelPrompt}.inspectSchemeTmpCode`).d('检验方案模板编码'),
    },
    {
      type: FieldType.string,
      name: 'siteName',
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectSchemeTmpObjectType`).d('检验模板对象类型'),
      name: 'inspectSchemeObjectTypeDesc',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      name: 'remark',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
    },
  ],
});

// 检验业务类型-ds
const inspectBusTypeDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  primaryKey: 'inspectSchemeId',
  dataKey: 'rows',
  fields: [
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeDesc',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingDimension`).d('抽样维度'),
      name: 'samplingDimension',
      lookupCode: 'MT.QMS.SAMPLING_DIMENSION',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
      name: 'samplingMethodDesc',
    },
  ],
});

// 详情-新增检验维度的DS
const dimensionTableDS = (): DataSetProps => ({
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.area`).d('区域'),
      name: 'areaName',
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.process`).d('工序'),
      name: 'processWorkcellName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.station`).d('工位'),
      name: 'stationWorkcellName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipment`).d('设备'),
      name: 'equipmentName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      name: 'operationName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      name: 'supplierName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customer`).d('客户'),
      name: 'customerName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.otherObject`).d('其他对象'),
      name: 'otherObject',
    },
  ],
});

// 检验组关联检验项
const inspectItemDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  primaryKey: 'inspectGroupItemId',
  fields: [
    {
      name: 'inspectItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.inspectItemCode`).d('检验项目编码'),
    },
    {
      name: 'inspectItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.inspectItemDesc`).d('检验项目描述'),
    },
    {
      name: 'inspectItemType',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.inspectItemType`).d('检验项目类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSPECT_ITEM_TYPE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'inspectBasis',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.inspectBasis`).d('检验依据'),
    },
    {
      name: 'dataQtyDisposition',
      type: FieldType.string,
      lookupCode: 'MT.QMS_DATA_QTY_DISPOSITION',
      label: intl.get(`${modelPrompt}.dataQtyDisposition`).d('记录值个数配置'),
    },
    {
      name: 'qualityCharacteristic',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.qualityCharacteristic`).d('质量特性'),
      lookupCode: 'MT.QMS.QUALITY_CHARACTERISTIC_TYPE',
    },
    {
      name: 'inspectTool',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.inspectTool`).d('检验工具'),
      lookupCode: 'MT.QMS.INSPECT_TOOL',
    },
    {
      name: 'inspectMethod',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.inspectMethod`).d('检验方法'),
      lookupCode: 'MT.QMS.INSPECT_METHOD',
    },
    {
      name: 'requiredFlag',
      label: intl.get(`${modelPromptGroup}.requiredFlag`).d('必填项目标识'),
      type: FieldType.string,
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
    },
    {
      name: 'technicalRequirement',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.technicalRequirement`).d('技术要求'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.remark`).d('备注'),
    },
    {
      name: 'enterMethod',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.enterMethod`).d('录入方式'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_COLLECTION_METHOD`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'dataType',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.dataType`).d('数据类型'),
      lookupCode: 'MT.QMS.INSPECT_ITEM_DATA_TYPE',
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.uomCode`).d('单位'),
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
      label: intl.get(`${modelPromptGroup}.decimalNumber`).d('小数位数'),
    },
    {
      name: 'processMode',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.processMode`).d('尾数处理'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=DECIMAL_PROCESS_MODE`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'valueLists',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.valueLists`).d('值列表'),
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.trueValue`).d('符合值'),
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.falseValue`).d('不符合值'),
    },
    {
      name: 'warningValue',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.earlyWarningValue`).d('预警值'),
    },
    {
      name: 'dataQty',
      type: FieldType.number,
      label: intl.get(`${modelPromptGroup}.dataQty`).d('记录值个数'),
    },
    {
      name: 'samplingMethodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.samplingMethod`).d('抽样方式'),
    },
    {
      name: 'ncCodeGroupDesc',
      type: FieldType.object,
      label: intl.get(`${modelPromptGroup}.ncCodeGroup`).d('不良代码组'),
    },
    {
      name: 'employeePosition',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.employeePosition`).d('检测人员岗位'),
    },
    {
      name: 'inspectFrequency',
      type: FieldType.object,
      label: intl.get(`${modelPromptGroup}.inspectFrequency`).d('检测频率'),
    },
    {
      // 频率参数M值
      name: 'm',
      type: FieldType.number,
      label: intl.get(`${modelPromptGroup}.m`).d('频率参数M'),
    },
    {
      // 频率参数N值
      name: 'n',
      type: FieldType.number,
      label: intl.get(`${modelPromptGroup}.n`).d('频率参数N'),
    },
    {
      name: 'actionItem',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.actionItem`).d('行动项'),
    },
    {
      name: 'sameGroupIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.sameGroupIdentification`).d('同组标识'),
    },
    {
      name: 'destructiveExperimentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.destructiveExperimentFlag`).d('破坏性检验标识'),
    },
    {
      name: 'outsourceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.outsourceFlag`).d('委外检验标识'),
    },

    {
      name: 'formula',
      type: FieldType.string,
      label: intl.get(`${modelPromptGroup}.formula`).d('计算公式'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPromptGroup}.attachment`).d('附件'),
    },
  ],
});

export { inspectSchemeTemplateDS, inspectBusTypeDS, dimensionTableDS, inspectItemDS };
