import React from 'react';
import DashboardCard from '../DashboardCard.jsx';
import PassRate from '../PassRate';
import styles from './index.module.less';

const TotalQty = (props) => {

  const { materialId, timers } = props;

  return (
    <DashboardCard style={{ height: '50%'}}>
      <div style={{ width: '100%', height: '100%', display: 'flex' }} className={styles['item-center-bottom']}>
        <PassRate materialId={materialId} timers={timers} />
      </div>
    </DashboardCard>
  );
};
export default TotalQty;
