/**
 * @Description: 检验方案模板-实例化
 * @Author: <<EMAIL>>
 * @Date: 2023-02-15 14:41:11
 * @LastEditTime: 2023-02-16 15:07:23
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo } from 'react';
import { Table, Form, Lov, Select, Dropdown, Menu, Icon, Button } from 'choerodon-ui/pro';
import { useDataSetEvent } from 'utils/hooks';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from 'utils/intl';
import { Popconfirm } from 'choerodon-ui';

const modelPrompt = 'tarzan.inspectionSchemeTemplate';

const DimensionTableList = props => {
  const { formDs, tableDs } = props;

  const [selectedRecords, setSelectedRecords] = useState([]);

  const columns: ColumnProps[] = useMemo(() => {
    const formDsData = formDs.toData()[0];
    const needRevision = formDsData.inspectSchemeObjectType === 'MATERIAL';
    return [
      {
        name: 'inspectSchemeObject',
      },
      {
        name: 'inspectSchemeObjectDesc',
      },
      ...(needRevision
        ? [
          needRevision && {
            name: 'revisionCode',
            editor: () => <Select />,
          },
        ]
        : []),
      {
        name: 'fullUpdate',
        editor: () => <Select />,
      },
      {
        title: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteRecord(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <a>{intl.get('tarzan.common.button.delete').d('删除')}</a>
          </Popconfirm>
        ),
      },
    ];
  }, []);

  useDataSetEvent(formDs, 'update', ({ name, record, value }) => {
    switch (name) {
      case 'siteObject':
        record.init('inspectSchemeObject', null);
        tableDs.loadData([]);
        break;
      case 'inspectSchemeObject':
        record.init('inspectSchemeObject', null);
        if (value) {
          addRecord(value);
        }
        break;
      default:
        break;
    }
  });

  useDataSetEvent(tableDs, 'batchSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  useDataSetEvent(tableDs, 'batchUnSelect', ({ dataSet }) => {
    setSelectedRecords(dataSet.selected);
  });

  const deleteRecord = record => {
    tableDs.delete(record, false);
  };

  const lovIdKey = type => {
    switch (type) {
      case 'MATERIAL':
        return 'materialId';
      case 'WORKCELL':
        return 'workcellId';
      case 'PROCESS_WORKCELL':
        return 'workcellId';
      case 'PROD_LINE':
        return 'prodLineId';
      case 'AREA':
        return 'areaId';
      case 'OPERATION':
        return 'operationId';
      default:
        return 'materialId';
    }
  };

  const addRecord = list => {
    const formDsData = formDs.toData()[0];
    if (list?.length > 0) {
      // 已有数据转为对象进行对比
      const inDsCodeMap: Array<any> = [];
      tableDs.forEach(recordItem => {
        inDsCodeMap.push(recordItem.get('inspectSchemeObjectId'));
      });

      // 取得的value值还需要再处理
      list.forEach(valueItem => {
        const _valueItem = { ...valueItem };

        if (inDsCodeMap.indexOf(valueItem[lovIdKey(formDsData.inspectSchemeObjectType)]) === -1) {
          tableDs.create({
            siteId: formDsData.siteId,
            inspectSchemeObjectType: formDsData.inspectSchemeObjectType,
            inspectSchemeObjectLov: _valueItem,
            fullUpdate: 'Y',
          });
        }
      });
    }

    formDs.current.init('materialObject', null);
  };

  const batchChangeRecord = value => {
    selectedRecords.forEach((record: any) => {
      record.set('fullUpdate', value.key);
    });
  };

  const menu = (
    <Menu onClick={batchChangeRecord}>
      <Menu.Item key="Y">{intl.get(`${modelPrompt}.fullUpdate`).d('全量更新')}</Menu.Item>
      <Menu.Item key="N">{intl.get(`${modelPrompt}.incrementalUpdate`).d('增量更新')}</Menu.Item>
    </Menu>
  );

  return (
    <>
      <Form dataSet={formDs} columns={2} labelWidth={112}>
        <Lov name="siteObject" />
        <Lov name="inspectSchemeObject" />
      </Form>
      <Table
        style={{
          marginTop: '10px',
        }}
        dataSet={tableDs}
        columns={columns}
        buttons={[
          <Dropdown overlay={menu} disabled={selectedRecords.length === 0}>
            <Button funcType="flat" disabled={selectedRecords.length === 0}>
              {intl.get(`${modelPrompt}.batchOptions`).d('批量编辑')}
              <Icon type="expand_more" />
            </Button>
          </Dropdown>,
        ]}
      />
    </>
  );
};
export default DimensionTableList;
