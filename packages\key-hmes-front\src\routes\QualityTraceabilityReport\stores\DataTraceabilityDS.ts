import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { Record } from 'choerodon-ui/dataset';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

function filterDisabled(type: 'mod1' | 'mod2', record: Record) {
  if (type === 'mod1') {
    // 模式一根据模式二的数据是否有值进行判断是否禁用
    if (
      record.get('recordDateFrom') ||
      record.get('recordDateTo') ||
      record.get('tagGroupId') ||
      record.get('materialId')
    ) {
      return true;
    }
    return false;
  }
  // 模式二
  if (record.get('identification')) {
    return true;
  }
  return false;
}

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('执行作业'),
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod1', record);
        },
        required: ({ record }) => {
          return !filterDisabled('mod1', record);
        },
      },
    },
    {
      name: 'recordDateFrom',
      type: FieldType.dateTime,
      max: 'recordDateTo',
      label: intl.get(`${modelPrompt}.recordDateFrom`).d('时间段从'),
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod2', record);
        },
        required: ({ record }) => {
          return !filterDisabled('mod2', record);
        },
      },
    },
    {
      name: 'recordDateTo',
      type: FieldType.dateTime,
      min: 'recordDateFrom',
      label: intl.get(`${modelPrompt}.recordDateTo`).d('时间段至'),
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod2', record);
        },
        required: ({ record }) => {
          return !filterDisabled('mod2', record);
        },
      },
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellName`).d('工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: {
        tenantId,
        workcellType: 'STATION',
      },
      multiple: true,
      ignore: FieldIgnore.always,
      computedProps: {
        required: ({ record }) => {
          return !filterDisabled('mod1', record);
        },
      },
    },
    {
      name: 'workcellIds',
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'tagGroupLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagGroupName`).d('数据收集组'),
      lovCode: 'MT.TAG_GROUP',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod2', record);
        },
        required: ({ record }) => {
          return !filterDisabled('mod2', record);
        },
      },
    },
    {
      name: 'tagGroupId',
      type: FieldType.number,
      bind: 'tagGroupLov.tagGroupId',
    },
    {
      name: 'tagCalculateResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCalculateResult`).d('判定值'),
      lookupCode: 'MT.INSPECTION_RESULT',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod2', record);
        },
      },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL.PERMISSION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => {
          return filterDisabled('mod2', record);
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
  ],
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('执行作业'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工位'),
    },
    {
      name: 'recordDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.recordDate`).d('数据采集时间'),
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('数据采集项'),
    },
    {
      name: 'tagValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagValue`).d('数据采集值'),
      computedProps: {
        type: ({ record }) => {
          if (record.get('valueType') === 'ENCLOSURE') {
            return FieldType.attachment;
          }
          return FieldType.string;
        },
      },
    },
    {
      name: 'tagCalculateResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCalculateResult`).d('判定值'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('完工状态'),
    },
    {
      name: 'inStockStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inStockStatus`).d('入库状态'),
    },
    {
      name: 'userName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userName`).d('操作员'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const newData = {
        ...data,
        workcellIds: (data.workcellIds || []).join(','),
      };
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-trace-report/data/record/ui`,
        method: 'GET',
        data: newData,
      };
    },
  },
});

export { tableDS };
