/* eslint-disable no-unused-vars */
/* eslint-disable no-lonely-if */
/* eslint-disable jsx-a11y/alt-text */
import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import {
  DataSet,
  Table,
  Button,
  notification,
  Form,
  Spin,
  Modal,
  Lov,
  TextField,
  Select,
  Icon,
  Progress,
  NumberField,
} from 'choerodon-ui/pro';
import { Row, Col, Collapse, Tabs, Tag } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import request from 'utils/request';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
// import { getResponse } from '@/utils/utils';
import { closeTab } from 'utils/menuTab';
import FileGreen from '@/assets/icons/file_green.png';
import FileRed from '@/assets/icons/file_red.png';
import BoxGreen from '@/assets/icons/box_green.png';
import PersonnelGreen from '@/assets/icons/personnel_green.png';
import LocationBlue from '@/assets/icons/location_blue.png';
import DragGrid from './DragGrid';
import {
  tableDS,
  formDS,
  bomListDS,
  dispatchListDS,
  sendTableDS,
  splitOrderDS,
  splitWorkCellDS,
  machinePlatformDS,
} from './stores/TableDS';
import './index.less';
// import MachineLoad from './Component/MachineLoad';
import MachineLoadNew from './Component/MachineLoadNew';

const tenantId = getCurrentOrganizationId();
const theName = getCurrentUser().realName;
const Host = BASIC.HMES_BASIC;
// const Host = '/key-focus-mes-33275';
const { Panel } = Collapse;
let workCellLoginModal;
let remarkModal;
let modalTab;
let workOrderModal;

const modelPrompt = 'tarzan.workshop.MachineProductionDispatch';

const MachineProductionDispatch = withProps(
  () => {
    const dataSet = new DataSet({ ...tableDS() });
    const formDs = new DataSet({ ...formDS() });
    const bomListDs = new DataSet({ ...bomListDS() });
    const dispatchListDs = new DataSet({ ...dispatchListDS() });
    const sendTableDs = new DataSet({ ...sendTableDS() });
    const splitOrderDs = new DataSet({ ...splitOrderDS() });
    const splitWorkCellDs = new DataSet({ ...splitWorkCellDS() });
    const machinePlatformDs = new DataSet({ ...machinePlatformDS() });
    return {
      dataSet,
      sendTableDs,
      formDs,
      bomListDs,
      dispatchListDs,
      splitOrderDs,
      splitWorkCellDs,
      machinePlatformDs,
    };
  },
  { cacheState: true },
)(props => {
  const {
    // match: { path },
    // history,
    dataSet,
    sendTableDs,
    workOrderInfoDs,
    returnDs,
    formDs,
    bomListDs,
    dispatchListDs,
    splitOrderDs,
    splitWorkCellDs,
    machinePlatformDs,
  } = props;

  const remakeDs = useMemo(
    () =>
      new DataSet({
        fields: [
          {
            name: 'inspectionTaskNum',
            type: 'string',
            label: '备注',
            required: true,
          },
          {
            name: 'inspectionTab',
            type: 'string',
            label: '标识',
            lookupCode: 'HME.WO_TAG_COLOR',
            required: true,
          },
        ],
      }),
    [remakeDs],
  );

  const workCellDs = useMemo(
    () =>
      new DataSet({
        autoCreate: true,
        fields: [
          {
            label: '派工产线',
            name: 'prodLineId',
            type: 'string',
            lovPara: { tenantId },
            lookupCode: 'HME_PROD_LINE',
            required: true,
            valueField: 'prodLineId',
            textField: 'prodLineName',
          },
          {
            label: '派工工段',
            name: 'workcellId',
            type: 'string',
            lookupCode: 'HME_WORKCELL',
            valueField: 'workcellId',
            textField: 'workcellName',
            dynamicProps: {
              disabled: ({ record }) => {
                return !record.get('prodLineId');
              },
              lovPara: ({ record }) => {
                return {
                  prodLineId: record.get('prodLineId'),
                  tenantId,
                };
              },
            },
          },
        ],
      }),
    [workCellDs],
  );

  // const machinePlatformDs = useMemo(
  //   () =>
  //     new DataSet({
  //       autoCreate: true,
  //       fields: [
  //         {
  //           name: 'operationObj',
  //           type: 'object',
  //           ignore: 'always',
  //           lovCode: 'HME.DISPATCH_OPERATION',
  //           lovPara: {
  //             tenantId,
  //             prodLineId: window.localStorage.getItem('dispatchProdLineId') || '',
  //             workcellId: window.localStorage.getItem('dispatchWorkCellId') || '',
  //           },
  //           label: '工艺',
  //         },
  //         {
  //           name: 'operationName',
  //           bind: 'operationObj.operationName',
  //         },
  //         {
  //           name: 'operationId',
  //           bind: 'operationObj.operationId',
  //         },
  //       ],
  //     }),
  //   [machinePlatformDs],
  // );

  const machineLoadDs = useMemo(
    () =>
      new DataSet({
        autoCreate: true,
        fields: [
          {
            name: 'workOrderNum',
            type: 'string',
            label: intl.get(`${modelPrompt}.workOrderNum`).d('工单'),
          },
          {
            name: 'b',
            type: 'string',
            label: intl.get(`${modelPrompt}.b`).d('工艺'),
          },
          {
            name: 'actionDispatchQty',
            type: 'string',
            label: intl.get(`${modelPrompt}.actionDispatchQty`).d('已发布数'),
          },
          {
            name: 'processDispatchQty',
            type: 'string',
            label: intl.get(`${modelPrompt}.processDispatchQty`).d('未发布数'),
          },
        ],
      }),
    [machineLoadDs],
  );

  const [workOrderInfo, setWorkOrderInfo] = useState([]); // 登录工位信息
  const [lineName, setLineName] = useState(''); // 登录工段
  const [btnFlag, setBtnFlag] = useState(false);
  const [loading, setLoading] = useState(false);
  const [pageColumns, setPageColumns] = useState(1);
  const [listData, setListData] = useState([]);

  const [isEchart, setIsEchart] = useState(true);
  const [tabletotal, setTableTotal] = useState(0);
  const [sendTableTotal, setSendTableTotal] = useState(0);
  const [tableRate, setTableRate] = useState('');
  const [sendTableRate, setSendTableRate] = useState('');
  const [tabKey, setTabKey] = useState('send');

  const [currentShiftCode, setCurrentShiftCode] = useState(''); // 当前班次
  const [currentShiftDate, setCurrentShiftDate] = useState(''); // 当前日期

  const [machineLoadSpin, setMachineLoadSpin] = useState(false); // 机台负荷spin
  const [machineLoadTable, setMachineLoadTable] = useState(false); // 机台负荷列表数据

  const [stationValue, setStationValue] = useState([]); // 机台工位
  const [stationOperation, setStationOperation] = useState([]); // 机台工艺
  const [stationWorkCellList, setStationWorkCellList] = useState([]); // 机台工位加工艺

  const [stationWorkCellCheck, setStationWorkCellCheck] = useState([]); // 机台工单勾选数据

  const [tableDataSelectTag, setTableDataSelectTag] = useState([]); // 待派工单选择数据的标签
  const [dispatchDataSelectTag, setDispatchDataSelectTag] = useState([]); // 已派工单选择数据的标签

  const [willDispatchSpin, setWillDispatchSpin] = useState(false); // 待派工单spin
  const [dispatchedSpin, setDispatchedSpin] = useState(false); // 已派工单spin

  useEffect(() => {
    login();

    dataSet.addEventListener('select', handleWillDispatchDataSelect);
    dataSet.addEventListener('unSelect', handleWillDispatchDataSelect);
    dataSet.addEventListener('selectAll', handleWillDispatchDataSelect);
    dataSet.addEventListener('unSelectAll', handleWillDispatchDataSelect);
    sendTableDs.addEventListener('select', handleDispatchedDataSelect);
    sendTableDs.addEventListener('unSelect', handleDispatchedDataSelect);
    sendTableDs.addEventListener('selectAll', handleDispatchedDataSelect);
    sendTableDs.addEventListener('unSelectAll', handleDispatchedDataSelect);
    // setTableDataSelectTag([]);
    // setDispatchDataSelectTag([]);
  }, []);

  useEffect(() => {
    dataSet.addEventListener('query', () => {
      setTimeout(() => {
        setTableTotal(dataSet.toData()[0]?.workOrderCount || 0);
        setTableRate(dataSet.toData()[0]?.dispatchRate || '');
        setTableDataSelectTag([]);
      }, 1000);
    });
    sendTableDs.addEventListener('query', () => {
      setTimeout(() => {
        setSendTableTotal(sendTableDs.toData()[0]?.workOrderCount || 0);
        setSendTableRate(sendTableDs.toData()[0]?.dispatchedRate || '');
        setDispatchDataSelectTag([]);
      }, 1000);
    });
  }, [dataSet, sendTableDs]);

  // 待派工单选择数据监听
  const handleWillDispatchDataSelect = () => {
    // setWillDispatchDataSelect(WillDispatchOrderDsData.selected);
    setTableDataSelectTag(dataSet.selected.map(v => v.data.tag));
  };
  // 已派工单选择数据监听
  const handleDispatchedDataSelect = () => {
    // setDispatchedDataSelect(DispatchedOrderData.selected);
    setDispatchDataSelectTag(sendTableDs.selected.map(v => v.data.tag));
  };

  const login = type => {
    workCellLoginModal = Modal.open({
      title: '登录页面',
      destroyOnClose: true,
      children: (
        <Form dataSet={workCellDs} id="workCellLogin" labelWidth={80}>
          <Select name="prodLineId" onChange={changeProdLine} />
          <Select name="workcellId" />
        </Form>
      ),
      footer: (
        <>
          <Button
            onClick={() => {
              handleWorkCellLoginClose(type);
            }}
          >
            取消
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleWorkCellLoginConfirm(type);
            }}
          >
            登录
          </Button>
        </>
      ),
    });
  };

  // 查询
  const getLoginWkcInfo = (value, type) => {
    // window.localStorage.removeItem('dispatchWorkCellId');
    // window.localStorage.removeItem('dispatchProdLineId');
    machinePlatformDs.setState('dispatchWorkCellId', null);
    machinePlatformDs.setState('dispatchProdLineId', null);
    const params = {
      prodLineId: value.prodLineId,
      workcellId: value.workcellId,
    };
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/organization/info`, {
      method: 'GET',
      query: params,
    }).then(res => {
      if (res && !res.failed) {
        if (!value.workcellId) {
          setLineName('');
        } else {
          const workcellName = res.lineList[0].lineName;
          setLineName(workcellName);
        }
        // window.localStorage.setItem('dispatchWorkCellId', res.workcellId);
        // window.localStorage.setItem('dispatchProdLineId', res.prodLineId);
        machinePlatformDs.setState('dispatchWorkCellId', res.workcellId);
        machinePlatformDs.setState('dispatchProdLineId', res.prodLineId);
        setWorkOrderInfo(res);
        setCurrentShiftCode(res.shiftCode);
        setCurrentShiftDate(res.shiftDate);

        dataSet.setQueryParameter('opIds', res.opIds);
        dataSet.query();
        sendTableDs.setQueryParameter('opIds', workOrderInfo.opIds);
        sendTableDs.query();

        handleProcessData(res, res.shiftCode, res.shiftDate);
        workCellLoginModal.close();
      } else {
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  const reset = () => {
    returnDs.current.reset();
    workOrderInfoDs?.current?.reset();
    // 工单查询参数
    workOrderInfoDs?.current.set('prodLineId', workCellDs.current.toData());
    dataSet.loadData([]);
  };

  const handleWorkCellLoginConfirm = async type => {
    const validate = await workCellDs.validate();
    if (validate) {
      getLoginWkcInfo(workCellDs.current.toData(), type);
    }
  };

  const changeProdLine = () => {
    workCellDs.current.set('workcellId', '');
  };

  const changeEchartData = () => {
    setIsEchart(true);
  };

  const changeListData = () => {
    setIsEchart(false);
  };

  const lastShift = () => {
    const params = {
      shiftCode: currentShiftCode,
      shiftDate: currentShiftDate,
    };
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/calendar/previous/shift`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        setCurrentShiftCode(res.shiftCode);
        setCurrentShiftDate(res.shiftDate);
        handleProcessData(workOrderInfo, res.shiftCode, res.shiftDate);
      } else {
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 机台升位
  const toUp = () => {
    if (stationWorkCellCheck.length !== 1) {
      return notification.error({
        message: '请勾选一条数据进行优先级调整',
      });
    }
    let current = {};
    let top = {};
    let isUp = false;
    listData.forEach(item => {
      if (item.stationId === stationWorkCellCheck[0].workcellId) {
        item.productionCapacityVO9List.forEach((item1, index) => {
          if (
            item1.stepName === stationWorkCellCheck[0].stepName &&
            item1.workOrderNum === stationWorkCellCheck[0].workOrderNum
          ) {
            if (index === 0) {
              isUp = true;
              return;
            }
            current = item.productionCapacityVO9List[index];
            top = item.productionCapacityVO9List[index - 1];
          }
        });
      }
    });
    if (isUp) {
      return notification.error({
        message: '请检查勾选数据优先级是否符合升位要求',
      });
    }
    setMachineLoadSpin(true);
    const params = [
      {
        priority: current.priority,
        processDispatchId: top.processDispatchId,
        actionDispatchId: top.actionDispatchId,
      },
      {
        priority: top.priority,
        processDispatchId: current.processDispatchId,
        actionDispatchId: current.actionDispatchId,
        selectFlag: 'Y',
      },
    ];
    const queryParams = [];
    workOrderInfo.lineList.forEach(item => {
      item.processList.forEach(item1 => {
        item1.stationList.forEach(item2 => {
          queryParams.push({
            operationId: item1.operationId,
            stationId: item2.stationId,
            shiftCode: currentShiftCode,
            shiftDate: currentShiftDate,
            shiftStartTime: workOrderInfo.shiftStartTime,
            shiftEndTime: workOrderInfo.shiftEndTime,
            opId: machinePlatformDs?.current?.toData()?.operationId,
          });
        });
      });
    });
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/sort/update/ui`, {
      method: 'POST',
      // body: params,
      body: {
        dto8s: params,
        dto5List: queryParams,
      },
    }).then(res => {
      if (res && !res.failed) {
        // handleProcessData(workOrderInfo, currentShiftCode, currentShiftDate);
        setListData(res);
        setStationValue([]);
        setStationOperation([]);
        setMachineLoadSpin(false);
      } else {
        setMachineLoadSpin(false);
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 机台降位
  const toDown = () => {
    if (stationWorkCellCheck.length !== 1) {
      return notification.error({
        message: '请勾选一条数据进行优先级调整',
      });
    }
    let current = {};
    let down = {};
    let isDown = false;
    listData.forEach(item => {
      if (item.stationId === stationWorkCellCheck[0].workcellId) {
        item.productionCapacityVO9List.forEach((item1, index) => {
          if (
            item1.stepName === stationWorkCellCheck[0].stepName &&
            item1.workOrderNum === stationWorkCellCheck[0].workOrderNum
          ) {
            if (index === item.productionCapacityVO9List.length - 1) {
              isDown = true;
              return;
            }
            current = item.productionCapacityVO9List[index];
            down = item.productionCapacityVO9List[index + 1];
          }
        });
      }
    });
    if (isDown) {
      return notification.error({
        message: '请检查勾选数据优先级是否符合降位要求',
      });
    }
    setMachineLoadSpin(true);
    const params = [
      {
        priority: current.priority,

        processDispatchId: down.processDispatchId,
        actionDispatchId: down.actionDispatchId,
      },
      {
        priority: down.priority,
        selectFlag: 'Y',
        processDispatchId: current.processDispatchId,
        actionDispatchId: current.actionDispatchId,
      },
    ];

    const queryParams = [];
    workOrderInfo.lineList.forEach(item => {
      item.processList.forEach(item1 => {
        item1.stationList.forEach(item2 => {
          queryParams.push({
            operationId: item1.operationId,
            stationId: item2.stationId,
            shiftCode: currentShiftCode,
            shiftDate: currentShiftDate,
            shiftStartTime: workOrderInfo.shiftStartTime,
            shiftEndTime: workOrderInfo.shiftEndTime,
            opId: machinePlatformDs?.current?.toData()?.operationId,
          });
        });
      });
    });
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/sort/update/ui`, {
      method: 'POST',
      body: {
        dto8s: params,
        dto5List: queryParams,
      },
    }).then(res => {
      if (res && !res.failed) {
        console.log(res);
        setListData(res);
        setStationValue([]);
        setStationOperation([]);
        setMachineLoadSpin(false);
        // handleProcessData(workOrderInfo, currentShiftCode, currentShiftDate);
      } else {
        setMachineLoadSpin(false);
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 机台撤销
  const toCancel = () => {
    const array = [];
    machineLoadTable.forEach(item => {
      item.newDate.selected.forEach(item1 => {
        array.push(item1.toData());
      });
    });
    if (stationWorkCellCheck.length === 0) {
      return notification.error({
        message: '请至少勾选一条数据进行撤销',
      });
    }
    const params = {
      shiftCode: currentShiftCode,
      shiftDate: currentShiftDate,
      prodLineId: workOrderInfo.prodLineId,
      workOrderList: stationWorkCellCheck,
    };
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/cancel/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        handleProcessData(workOrderInfo, currentShiftCode, currentShiftDate);
      } else {
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 调度撤销
  const toDispatchCancel = () => {
    const array = [];
    machineLoadTable.forEach(item => {
      item.newDate.selected.forEach(item1 => {
        array.push(item1.toData());
      });
    });
    if (stationWorkCellCheck.length === 0) {
      return notification.error({
        message: '请至少勾选一条数据进行撤销',
      });
    }
    const params = {
      shiftCode: currentShiftCode,
      shiftDate: currentShiftDate,
      prodLineId: workOrderInfo.prodLineId,
      workOrderList: stationWorkCellCheck,
    };
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/dispatch/cancel/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        handleProcessData(workOrderInfo, currentShiftCode, currentShiftDate);
        if (tabKey === 'send') {
          dataSet.query();
        } else {
          sendTableDs.query();
        }
      } else {
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 发布
  const handelPublish = () => {
    // const array = [];
    // machineLoadTable.forEach(item => {
    //   item.newDate.selected.forEach(item1 => {
    //     array.push(item1.toData());
    //   });
    // });
    if (stationWorkCellCheck.length === 0) {
      return notification.error({
        message: '请至少勾选一条数据进行发布',
      });
    }
    const params = {
      shiftCode: currentShiftCode,
      shiftDate: currentShiftDate,
      prodLineId: workOrderInfo.prodLineId,
      workOrderList: stationWorkCellCheck,
    };
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/publish/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        handleProcessData(workOrderInfo, currentShiftCode, currentShiftDate);
      } else {
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 调度
  const handleDispatch = () => {
    if (stationValue.length !== 1) {
      return notification.error({ message: '请只勾选一个机台！' });
    }
    if (dataSet.selected.length === 0 && sendTableDs.selected.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    const params = {
      shiftCode: currentShiftCode,
      shiftDate: currentShiftDate,
      prodLineId: workOrderInfo.prodLineId,
      workOrderList: btnFlag
        ? sendTableDs.selected.map(i => i.toData())
        : dataSet.selected.map(i => i.toData()),
      workCellId: stationValue[0],
      operationId: stationOperation[0],
    };
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/dispatch/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        handleProcessData(workOrderInfo, currentShiftCode, currentShiftDate);
        dataSet.setQueryParameter('opIds', workOrderInfo.opIds);
        sendTableDs.setQueryParameter('opIds', workOrderInfo.opIds);
        dataSet.query();
        sendTableDs.query();
      } else {
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 拆分调度
  const handleSplitDispatch = () => {
    if (stationValue.length === 0) {
      return notification.error({ message: '请勾选机台工位！' });
    }
    if (dataSet.selected.length === 0 && sendTableDs.selected.length === 0) {
      return notification.error({ message: '请勾选工单数据！' });
    }
    if (
      stationValue.length > 1 &&
      ((tabKey === 'send' && dataSet.selected.length > 1) ||
        (tabKey === 'sended' && sendTableDs.selected.length > 1))
    ) {
      return notification.error({
        message: '只能多工单拆分调度到一个工位，或者一个工单拆分调度到多个工位!',
      });
    }
    const array =
      tabKey === 'send'
        ? dataSet.selected.map(i => i.toData())
        : sendTableDs.selected.map(i => i.toData());
    const map = new Map();
    const newTable = array.filter(v => !map.has(v.operationId) && map.set(v.operationId, 1));
    const newOperation = Array.from(new Set(stationOperation));
    if (newTable.length > 1 || newOperation.length > 1) {
      return notification.error({
        message: '勾选数据存在工艺不一致的情况，请检查！',
      });
    }
    if (
      newTable.length === 1 &&
      newOperation.length === 1 &&
      newTable[0].operationId !== newOperation[0]
    ) {
      return notification.error({
        message: '勾选数据存在工艺不一致的情况，请检查！',
      });
    }
    console.log(stationWorkCellList, 'stationWorkCellList');
    splitOrderDs.loadData(array);
    splitWorkCellDs.loadData(stationWorkCellList);

    const splitOrderColumns =
      stationValue.length > 1
        ? [
          {
            name: 'workOrderNum',
            width: 135,
          },
          {
            name: 'operationName',
          },
          {
            name: 'stepName',
          },
          {
            name: 'dispatchQty',
          },
        ]
        : [
          {
            name: 'workOrderNum',
            width: 135,
          },
          {
            name: 'operationName',
          },
          {
            name: 'stepName',
          },
          {
            name: 'dispatchQty',
          },
          {
            name: 'splitDispatchQty',
            editor: record => <NumberField />,
          },
        ];

    const splitWorkCellColumns =
      stationValue.length > 1
        ? [
          {
            name: 'stationName',
          },
          {
            name: 'sumDispatchQty',
          },
          {
            name: 'splitDispatchQty',
            editor: record => <NumberField />,
          },
        ]
        : [
          {
            name: 'stationName',
          },
          {
            name: 'sumDispatchQty',
          },
        ];

    workOrderModal = Modal.open({
      title: '拆分调度',
      destroyOnClose: true,
      style: {
        width: 1100,
      },
      closable: true,
      children: (
        <div style={{ display: 'flex' }}>
          <div style={{ width: '65%', padding: 10 }}>
            <div>待调度工单</div>
            <Table dataSet={splitOrderDs} columns={splitOrderColumns} />
          </div>
          <div style={{ width: '35%', padding: 10 }}>
            <div>待分配工位</div>
            <Table dataSet={splitWorkCellDs} columns={splitWorkCellColumns} />
          </div>
        </div>
      ),
      footer: (
        <>
          <Button onClick={() => workOrderModal.close()}>
            {intl.get('tarzan.aps.common.button.cancel').d('取消')}
          </Button>
          <Button onClick={onOkSplitDispatch} color="primary">
            {intl.get('tarzan.aps.common.button.sure').d('确定')}
          </Button>
        </>
      ),
    });
  };

  // 确认拆分调度
  const onOkSplitDispatch = () => {
    const params = {
      shiftCode: currentShiftCode,
      shiftDate: currentShiftDate,
      prodLineId: workOrderInfo.prodLineId,
      workCellList: splitWorkCellDs.toData(),
      workOrderList: splitOrderDs.toData(),
    };
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/split/dispatch/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        workOrderModal.close();
        handleProcessData(workOrderInfo, currentShiftCode, currentShiftDate);
        dataSet.setQueryParameter('opIds', workOrderInfo.opIds);
        sendTableDs.setQueryParameter('opIds', workOrderInfo.opIds);
        dataSet.query();
        sendTableDs.query();
        notification.success({ message: '操作成功' });
      } else {
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 更改机台工位勾选
  const handleChangeStation = (value, item) => {
    console.log(value, item);
    let data = [...stationValue];
    let operationIds = [...stationOperation];
    let workCellList = [...stationWorkCellList];
    const list = [...listData];
    if (value) {
      data.push(item.stationId);
      operationIds.push(item.operationId);
      workCellList.push({
        stationId: item.stationId,
        operationId: item.operationId,
        sumDispatchQty: item.sumDispatchQty,
        stationName: item.stationName,
      });
      list.forEach(val => {
        if (val.stationId === item.stationId && val.operationId === item.operationId) {
          val.selectStationFlag = 'Y';
        }
      });
    } else {
      data = data.filter(e => e !== item.stationId);
      operationIds = operationIds.filter(e => e !== item.operationId);
      workCellList = workCellList.filter(e => e.stationId !== item.stationId);
      list.forEach(val => {
        if (val.stationId === item.stationId && val.operationId === item.operationId) {
          val.selectStationFlag = 'N';
        }
      });
    }
    setStationValue(data);
    setStationOperation(operationIds);
    setStationWorkCellList(workCellList);
    setListData(list);
    console.log('operationIds', operationIds);
    console.log('listlist', list);
  };

  const nextShift = () => {
    const params = {
      shiftCode: currentShiftCode,
      shiftDate: currentShiftDate,
    };
    request(`${Host}/v1/${tenantId}/hme-production-capacitys/calendar/next/shift`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        setCurrentShiftCode(res.shiftCode);
        setCurrentShiftDate(res.shiftDate);
        handleProcessData(workOrderInfo, res.shiftCode, res.shiftDate);
      } else {
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  const handleShiftWorkCell = () => {
    login('exChange');
  };

  // 查询右侧-机台负荷数据
  const handleProcessData = async (data, shiftCode, shiftDate, queryParams, val) => {
    const params = [];
    setMachineLoadSpin(true);
    data.lineList.forEach(item => {
      item.processList.forEach(item1 => {
        item1.stationList.forEach(item2 => {
          params.push({
            operationId: item1.operationId,
            stationId: item2.stationId,
            shiftCode,
            shiftDate,
            shiftStartTime: data.shiftStartTime,
            shiftEndTime: data.shiftEndTime,
            opId: queryParams?.operationId,
            tagWorkOrderNum: val?.workOrderNum,
            tagRouterStepId: val?.routerStepId,
          });
        });
      });
    });
    await request(`${Host}/v1/${tenantId}/hme-production-capacitys/machine/load/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        res.forEach(val => {
          val.selectStationFlag = 'N';
          val.productionCapacityVO9List.forEach(val2 => {
            val2.selectFlag = 'N';
          });
        });
        setListData(res);
        setMachineLoadSpin(false);
        setStationValue([]);
        setStationOperation([]);
        setStationWorkCellList([]);
        setStationWorkCellCheck([]);
        const listTable = [];
        res.forEach(item => {
          item.newDate = new DataSet({
            fields: [
              {
                name: 'workOrderNum',
                type: 'string',
                label: '工单',
              },
              {
                name: 'operationName',
                type: 'string',
                label: '工艺',
              },
              {
                name: 'actionDispatchQty',
                type: 'string',
                label: '已发布数',
              },
              {
                name: 'processDispatchQty',
                type: 'string',
                label: '未发布数',
              },
            ],
          });
          item.newDate.data = item.productionCapacityVO9List;
          listTable.push(item);
        });
        setMachineLoadTable(listTable);
      } else {
        setMachineLoadSpin(false);
        setListData([]);
        setStationValue([]);
        setStationOperation([]);
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  const updateModal = (type, loading) => {
    workCellLoginModal.update({
      footer: (
        <>
          <Button
            disabled={loading}
            onClick={() => {
              handleWorkCellLoginClose(type);
            }}
          >
            取消
          </Button>
          <Button disabled={loading} color="primary" onClick={handleWorkCellLoginConfirm}>
            登录
          </Button>
        </>
      ),
    });
  };

  // 切换可调度工单和已调度工单
  const changeTab = async e => {
    if (e === 'send') {
      setTabKey(e);
      setBtnFlag(false);
      dataSet.setQueryParameter('opIds', workOrderInfo.opIds);

      dataSet.query();
    } else if (e === 'sended') {
      setTabKey(e);
      setBtnFlag(true);
      sendTableDs.setQueryParameter('opIds', workOrderInfo.opIds);
      sendTableDs.query();
    }
    // this.tableDs.addEventListener('select', this.handleDataSetSelect);
    // this.tableDs.addEventListener('unSelect', this.handleDataSetSelect);
    // this.tableDs.addEventListener('selectAll', this.handleDataSetSelect);
    // this.tableDs.addEventListener('unSelectAll', this.handleDataSetSelect);
  };

  const handleWorkCellLoginClose = type => {
    workCellLoginModal.close();
    if (!type) {
      closeTab(`/hmes/machine-production-dispatch`);
    }
  };

  const handelAddRemark = flag => {
    if (!flag && dataSet.selected.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    if (flag && sendTableDs.selected.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    remarkModal = Modal.open({
      title: '待派工单新增备注',
      destroyOnClose: true,
      closable: true,
      children: (
        <TextField
          name="inspectionTaskNum"
          placeholder="备注信息"
          dataSet={remakeDs}
          required
          clearButton
          onEnterDown={e => onFetchRemake(e.target.value, flag)}
          colSpan={1}
          style={{ width: '100%' }}
        />
      ),
      onOk: () => onFetchRemake(flag),
    });
  };

  // 新增备注
  const onFetchRemake = async flag => {
    if (remakeDs.toData().length === 0 || !remakeDs.toData()[0].inspectionTaskNum) {
      notification.error({ message: '请输入备注信息！' });
    } else {
      // eslint-disable-next-line no-lonely-if
      if (flag) {
        const params = sendTableDs.selected.map(item => {
          return {
            workOrderId: item.data.workOrderId,
            routerStepId: item.data.routerStepId,
            // remarks: remakeDs.toData()[0].inspectionTaskNum,
            notes: [...(item.data.notes || []), remakeDs.toData()[0].inspectionTaskNum],
          };
        });

        const res = await request(
          `${Host}/v1/${tenantId}/hme-production-capacitys/note/update/ui`,
          {
            method: 'POST',
            body: params,
          },
        );
        if (getResponse(res)) {
          notification.success({ message: '操作成功' });
          sendTableDs.setQueryParameter('opIds', workOrderInfo.opIds);
          sendTableDs.query();
          remarkModal.close();
        }
      } else {
        const params = dataSet.selected.map(item => {
          return {
            workOrderId: item.data.workOrderId,
            routerStepId: item.data.routerStepId,
            notes: [...(item.data.notes || []), remakeDs.toData()[0].inspectionTaskNum],
          };
        });
        const res = await request(
          `${Host}/v1/${tenantId}/hme-production-capacitys/note/update/ui`,
          {
            method: 'POST',
            body: params,
          },
        );
        if (getResponse(res)) {
          notification.success({ message: '操作成功' });
          dataSet.setQueryParameter('opIds', workOrderInfo.opIds);
          dataSet.query();
          remarkModal.close();
        }
      }
    }
  };

  // 删除备注
  const handleDelRemark = async (value, i, type) => {
    console.log(value);
    const _current = JSON.parse(JSON.stringify(value.notes));
    _current.splice(i, 1);
    console.log(_current);
    const _list = [];
    _list.push({
      workOrderId: value.workOrderId,
      remarkList: _current,
    });
    const params = [
      {
        // workOrderIdList: [value.workOrderId],
        // remarkInfoList: _list,
        workOrderId: value.workOrderId,
        routerStepId: value.routerStepId,
        notes: _current,
      },
    ];
    const res = await request(`${Host}/v1/${tenantId}/hme-production-capacitys/note/update/ui`, {
      method: 'POST',
      body: params,
    });
    if (getResponse(res)) {
      notification.success({ message: '操作成功' });
      if (type === 'send') {
        dataSet.setQueryParameter('opIds', workOrderInfo.opIds);
        dataSet.query();
      }
      if (type === 'sended') {
        sendTableDs.setQueryParameter('opIds', workOrderInfo.opIds);
        sendTableDs.query();
      }
    }
  };

  // 标识
  const handelAddtab = flag => {
    if (!flag && dataSet.selected.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    if (flag && sendTableDs.selected.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    modalTab = Modal.open({
      title: '待派工单新增标识',
      destroyOnClose: true,
      closable: true,
      children: (
        <Select
          name="inspectionTab"
          placeholder="标识信息"
          dataSet={remakeDs}
          required
          clearButton
          colSpan={1}
          style={{ width: '100%' }}
        />
      ),
      onOk: () => onFetchTab(flag),
    });
  };

  // 新增标识
  const onFetchTab = async flag => {
    if (remakeDs.toData().length === 0 || !remakeDs.toData()[0].inspectionTab) {
      notification.error({ message: '请选择标识信息！' });
    } else {
      if (flag) {
        const params = sendTableDs.selected.map(item => {
          return {
            workOrderId: item.data.workOrderId,
            routerStepId: item.data.routerStepId,
            tag: remakeDs.toData()[0].inspectionTab,
          };
        });

        const res = await request(
          `${Host}/v1/${tenantId}/hme-production-capacitys/note/update/ui`,
          {
            method: 'POST',
            body: params,
          },
        );
        if (getResponse(res)) {
          notification.success();
          sendTableDs.setQueryParameter('opIds', workOrderInfo.opIds);
          sendTableDs.query();
          modalTab.close();
        }
      } else {
        const params = dataSet.selected.map(item => {
          return {
            workOrderId: item.data.workOrderId,
            routerStepId: item.data.routerStepId,
            tag: remakeDs.toData()[0].inspectionTab,
          };
        });

        const res = await request(
          `${Host}/v1/${tenantId}/hme-production-capacitys/note/update/ui`,
          {
            method: 'POST',
            body: params,
          },
        );
        if (getResponse(res)) {
          notification.success();
          dataSet.setQueryParameter('opIds', workOrderInfo.opIds);
          dataSet.query();
          modalTab.close();
        }
      }
    }
  };

  // 待派工单删除标记
  const handelDelTag = async flag => {
    if (flag === 'send' && dataSet.selected.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    if (flag === 'sended' && sendTableDs.selected.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    const params =
      flag === 'send'
        ? dataSet.selected.map(item => {
          return {
            workOrderId: item.data.workOrderId,
            routerStepId: item.data.routerStepId,
            tag: 'DELETE',
          };
        })
        : sendTableDs.selected.map(item => {
          return {
            workOrderId: item.data.workOrderId,
            routerStepId: item.data.routerStepId,
            tag: 'DELETE',
          };
        });
    // const params = {
    //   workOrderIdList:
    //     flag === 'send'
    //       ? dataSet.selected.map(v => v.data.workOrderId)
    //       : sendTableDs.selected.map(v => v.data.workOrderId),
    //   tag: 'DELETE',
    // };
    setWillDispatchSpin(true);
    setDispatchedSpin(true);
    const res = await request(`${Host}/v1/${tenantId}/hme-production-capacitys/note/update/ui`, {
      method: 'POST',
      body: params,
    });
    if (getResponse(res)) {
      notification.success();
      if (flag === 'send') {
        dataSet.setQueryParameter('opIds', workOrderInfo.opIds);
        dataSet.query();
      }
      if (flag === 'sended') {
        sendTableDs.setQueryParameter('opIds', workOrderInfo.opIds);
        sendTableDs.query();
      }
      // queryDateDs.query(queryDateDs.currentPage);
    } else {
      setWillDispatchSpin(false);
      setDispatchedSpin(false);
    }
  };

  // 关闭
  const handleCloseOrder = async flag => {
    if (!flag && dataSet.selected.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    if (flag && sendTableDs.selected.length === 0) {
      return notification.error({ message: '请勾选数据！' });
    }
    if (flag) {
      const res = await request(`${Host}/v1/${tenantId}/hme-production-capacitys/close/update/ui`, {
        method: 'POST',
        body: sendTableDs.selected.map(v => v.data.workOrderId),
      });
      if (getResponse(res)) {
        notification.success({ message: '操作成功' });
        sendTableDs.setQueryParameter('opIds', workOrderInfo.opIds);
        sendTableDs.query();
      }
    } else {
      const res = await request(`${Host}/v1/${tenantId}/hme-production-capacitys/close/update/ui`, {
        method: 'POST',
        body: dataSet.selected.map(v => v.data.workOrderId),
      });
      if (getResponse(res)) {
        notification.success({ message: '操作成功' });
        dataSet.setQueryParameter('opIds', workOrderInfo.opIds);
        dataSet.query();
      }
    }
  };

  // 撤销
  const handleCancelOrder = async () => {
    if (sendTableDs.selected.length === 0) {
      notification.error({ message: '请选择数据！' });
    } else {
      const workOrderList = sendTableDs.selected.map(v => v.data);
      const res = await request(`${Host}/v1/${tenantId}/hme-production-capacitys/cancel/ui`, {
        method: 'POST',
        body: {
          shiftDate: workOrderInfo.shiftDate,
          shiftCode: workOrderInfo.shiftCode,
          workOrderList,
        },
      });
      if (getResponse(res)) {
        notification.success({ message: '操作成功' });
        sendTableDs.query();
      }
    }
  };

  // 工单详情
  const orderDetail = id => {
    formDs.setQueryParameter('workOrderId', id);
    formDs.query();
    bomListDs.setQueryParameter('workOrderId', id);
    bomListDs.setQueryParameter('prodLineId', workOrderInfo.prodLineId);
    bomListDs.query();
    dispatchListDs.setQueryParameter('workOrderId', id);
    dispatchListDs.query();
    Modal.open({
      title: '工单信息',
      destroyOnClose: true,
      drawer: true,
      closable: true,
      maskClosable: true,
      children: (
        <div>
          <Form disabled dataSet={formDs} columns={3} labelLayout="horizontal" labelWidth={110}>
            <TextField name="workOrderNum" />
            <TextField name="siteCode" />
            <TextField name="siteName" />
            <TextField name="workOrderType" />
            <TextField name="statusMeaning" />
            <TextField name="remark" />
            <TextField name="materialCode" />
            <TextField name="materialName" />
            <TextField name="qty" />
            <TextField name="customerCode" />
            <TextField name="planStartTime" />
            <TextField name="planEndTime" />
            <TextField name="prodLineCode" />
            <TextField name="prodLineName" />
            <TextField name="bomName" />
            <TextField name="routerName" />
          </Form>
          <Collapse bordered={false} defaultActiveKey={['feeding', 'dispatch']}>
            <Panel header="投料进度" key="feeding">
              <Table dataSet={bomListDs} columns={bomColumns} />
            </Panel>
            <Panel header="派工信息" key="dispatch">
              <Table dataSet={dispatchListDs} columns={dispatchColumns} />
            </Panel>
          </Collapse>
        </div>
      ),
      footer: null,
      style: {
        width: 1400,
      },
    });
  };

  // 定位左侧已派工单
  const locationWorkOrder = data => {
    setTabKey('sended');
    sendTableDs.setQueryParameter('opIds', workOrderInfo?.opIds);
    sendTableDs.setQueryParameter('tagWorkOrderNum', data?.workOrderNum);
    sendTableDs.setQueryParameter('tagOperationId', data?.operationId);
    sendTableDs.query();
  };

  const handleChangeWorkOrder = (flag, item) => {
    let workCellCheck = [...stationWorkCellCheck];
    console.log(workCellCheck);
    console.log(item, flag, '------');
    const list = [...listData];
    if (flag) {
      workCellCheck.push(item);
      list.forEach(val => {
        workCellCheck.forEach(val1 => {
          if (val.operationId === val1.operationId && val.stationId === val1.workcellId) {
            val.productionCapacityVO9List.forEach(val2 => {
              if (val2.woKeyId === val1.woKeyId) {
                val2.selectFlag = 'Y';
              }
            });
          }
        });
      });
    } else {
      workCellCheck = workCellCheck.filter(
        e =>
          e.workOrderId !== item.workOrderId ||
          e.workcellId !== item.workcellId ||
          e.routerStepId !== item.routerStepId ||
          e.operationId !== item.operationId,
      );
      list.forEach(val => {
        if (val.operationId === item.operationId && val.stationId === item.workcellId) {
          val.productionCapacityVO9List.forEach(val1 => {
            if (item.workOrderId === val1.workOrderId && item.routerStepId === val1.routerStepId) {
              val1.selectFlag = 'N';
            }
          });
        }
      });
    }
    setListData(list);
    setStationWorkCellCheck(workCellCheck);
  };

  const handleBatchChangeWorkOrder = (flag, changeListData) => {
    let workCellCheck = [...stationWorkCellCheck];
    console.log(workCellCheck, '4444');
    console.log(changeListData, '5555', flag);
    const list = [...listData];
    for (let i = 0; i < changeListData.length; i++) {
      workCellCheck = workCellCheck.filter(j => !(j.woKeyId === changeListData[i].woKeyId && j.processDispatchId === changeListData[i].processDispatchId));
    }
    if (flag) {
      workCellCheck.push(...changeListData);
      list.forEach(val => {
        workCellCheck.forEach(val1 => {
          if (val.operationId === val1.operationId && val.stationId === val1.workcellId) {
            val.productionCapacityVO9List.forEach(val2 => {
              if (val2.woKeyId === val1.woKeyId) {
                val2.selectFlag = 'Y';
              }
            });
          }
        });
      });
    } else {
      list.forEach(val => {
        if (
          val.operationId === changeListData[0].operationId &&
          val.stationId === changeListData[0].workcellId
        ) {
          val.productionCapacityVO9List.forEach(val1 => {
            val1.selectFlag = 'N';
          });
        }
      });
    }
    workCellCheck = workCellCheck.filter(j => j.selectFlag === 'Y');
    setListData(list);
    setStationWorkCellCheck(workCellCheck);
  };

  const columnsCell = record => {
    return {
      className: record.record.data.tagFlag === 'Y' ? 'highLightRow' : '',
    };
  };

  const columns = [
    {
      name: 'dto4',
      align: 'center',
      width: 100,
      renderer: ({ record }) => {
        return (
          <div>
            <img
              src={
                record.data.status === 'RELEASED' || record.data.status === 'EORELEASED'
                  ? FileGreen
                  : FileRed
              }
            ></img>
            <img style={{ margin: '0 10px' }} src={BoxGreen}></img>
            <img src={PersonnelGreen}></img>
          </div>
        );
      },
    },
    {
      name: 'workOrderNum',
      width: 100,
      renderer: ({ value, record }) => {
        return (
          <div>
            <a
              onClick={() => {
                orderDetail(record.data.workOrderId);
              }}
            >
              {value}
            </a>
            {record.get('tag') && <Icon style={{ color: record.get('tag') }} type="emoji_flags" />}
          </div>
        );
      },
    },
    {
      name: 'operationName',
      width: 100,
    },
    {
      name: 'operationDescription',
      width: 100,
    },
    {
      name: 'planStartTime',
      width: 100,
    },
    {
      name: 'planEndTime',
      width: 100,
    },
    {
      name: 'stepName',
      width: 100,
    },
    { name: 'statusMeaning' },
    {
      name: 'dispatchQty',
      width: 100,
    },
    {
      name: 'qty',
      width: 180,
    },
    {
      name: 'materialCode',
      width: 100,
    },
    {
      name: 'materialName',
      width: 130,
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'model',
      width: 180,
    },
    {
      name: 'note',
      width: 100,
      renderer: ({ record }) => {
        // onClick={() => handleFetch(record.data)}
        return record.data.notes?.map((v, i) => {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <Tag key={i} closable afterClose={() => handleDelRemark(record.data, i, 'send')}>
              {v}
            </Tag>
          );
        });
      },
    },
    {
      name: 'remark',
      width: 120,
    },
    {
      name: 'customerName',
      width: 100,
    },
  ];

  const sendColumns = [
    {
      name: 'workOrderNum',
      width: 100,
      renderer: ({ value, record }) => {
        return (
          <div>
            <a
              onClick={() => {
                orderDetail(record.data.workOrderId);
              }}
            >
              {value}
            </a>
            {record.get('tag') && <Icon style={{ color: record.get('tag') }} type="emoji_flags" />}
          </div>
        );
      },
      onCell: columnsCell,
    },
    {
      name: 'operationName',
      onCell: columnsCell,
      width: 100,
    },
    {
      name: 'operationDescription',
      onCell: columnsCell,
      width: 100,
    },
    {
      name: 'planStartTime',
      onCell: columnsCell,
      width: 100,
    },
    {
      name: 'planEndTime',
      onCell: columnsCell,
      width: 100,
    },
    {
      name: 'stepName',
      onCell: columnsCell,
      width: 100,
    },
    {
      name: 'qty',
      onCell: columnsCell,
      width: 100,
    },
    {
      name: 'dispatchQty',
      onCell: columnsCell,
      width: 100,
    },
    {
      name: 'dispatchedQty',
      onCell: columnsCell,
      width: 100,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              handleProcessData(
                workOrderInfo,
                currentShiftCode,
                currentShiftDate,
                null,
                record.data,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'processDispatchedQty',
      onCell: columnsCell,
    },
    {
      name: 'actionDispatchedQty',
      onCell: columnsCell,
      width: 100,
    },
    {
      name: 'completedQtySumRate',
      width: 100,
      onCell: columnsCell,
      renderer: ({ value }) => (
        <div>
          <span style={{ display: 'inline-block', width: 30 }}>{value}</span>
          <Progress value={Number(value)} />
        </div>
      ),
    },
    {
      name: 'materialCode',
      onCell: columnsCell,
      width: 100,
    },
    {
      name: 'materialName',
      onCell: columnsCell,
      width: 130,
    },
    {
      name: 'revisionCode',
      onCell: columnsCell,
      width: 180,
    },
    {
      name: 'model',
      onCell: columnsCell,
    },
    {
      name: 'note',
      onCell: columnsCell,
      renderer: ({ record }) => {
        // onClick={() => handleFetch(record.data)}
        return record.data.notes?.map((v, i) => {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <Tag key={i} closable afterClose={() => handleDelRemark(record.data, i, 'sended')}>
              {v}
            </Tag>
          );
        });
      },
    },
    {
      name: 'remark',
      onCell: columnsCell,
    },
    {
      name: 'planStartTime',
      onCell: columnsCell,
    },
    {
      name: 'planEndTime',
      onCell: columnsCell,
    },
    {
      name: 'completedQty',
      onCell: columnsCell,
    },
    {
      name: 'scrappedQty',
      onCell: columnsCell,
    },
    { name: 'actualStartDate', onCell: columnsCell },
    { name: 'actualEndDate', onCell: columnsCell },
  ];

  const bomColumns = [
    {
      name: 'bomComponentName',
      align: 'left',
    },
    {
      name: 'inputQty',
      align: 'left',
    },
    {
      name: 'canInputQty',
      align: 'left',
    },
    {
      name: 'scrappedQty',
      align: 'left',
    },
    {
      name: 'inventoryQty',
      align: 'left',
    },
  ];

  const dispatchColumns = [
    {
      name: 'operationName',
      align: 'left',
    },
    {
      name: 'stepName',
      align: 'left',
    },
    {
      name: 'qty',
      align: 'left',
    },
    {
      name: 'dispatchedQty',
      align: 'left',
    },
    {
      name: 'dispatchQty',
      align: 'left',
    },
    // {
    //   name: 'workcellName',
    //   align: 'left',
    // },
    // {
    //   name: 'realName',
    //   align: 'left',
    // },
  ];

  const machineLoadProps = {
    listData,
    workOrderInfo,
    changeListData,
    changeEchartData,
    isEchart,
    lastShift,
    nextShift,
    currentShiftCode,
    currentShiftDate,
    machineLoadSpin,
    machineLoadDs,
    toUp,
    toDown,
    toCancel,
    handleChangeStation,
    locationWorkOrder,
    machineLoadTable,
    stationValue,
  };

  const machineLoadNewProps = {
    listData,
    workOrderInfo,
    changeListData,
    changeEchartData,
    isEchart,
    lastShift,
    nextShift,
    currentShiftCode,
    currentShiftDate,
    machineLoadSpin,
    machineLoadDs,
    toUp,
    toDown,
    toCancel,
    toDispatchCancel,
    handleChangeStation,
    locationWorkOrder,
    machineLoadTable,
    stationValue,
    handleChangeWorkOrder,
    handleBatchChangeWorkOrder,
  };

  return (
    <div className='hmes-style'>
      <Header title="机加生产派工">
        <Button color="primary" icon="add" onClick={() => handelPublish()}>
          发布
        </Button>
        <Button onClick={() => handleSplitDispatch()}>拆分调度</Button>
        <Button onClick={() => handleDispatch()}>调度</Button>
        <Button funcType="raised" color="yellow" onClick={toCancel}>
          发布撤销
        </Button>
        <Button funcType="raised" color="yellow" onClick={toDispatchCancel}>
          调度撤销
        </Button>
        <Button funcType="raised" color="blue" onClick={toDown}>
          降位
        </Button>
        <Button funcType="raised" color="yellow" onClick={toUp}>
          升位
        </Button>
        <Button onClick={handleShiftWorkCell}>组织切换</Button>
        <div className="account_circle_title" style={{ position: 'absolute', left: '150px' }}>
          <span>产线：{workOrderInfo.prodLineName}</span>
          <span style={{ padding: '0 10px' }}>工段：{lineName}</span>
          <Icon type="account_circle-o" style={{ marginRight: '10px' }} />
          <span>{theName}</span>
        </div>
      </Header>
      <Content style={{ height: '100%' }}>
        <div style={{ height: '100%'}}>
          <DragGrid
            article={
              <Spin spinning={loading}>
                <div
                  style={{
                    marginTop: 20,
                    marginLeft: 10,
                    position: 'absolute',
                    right: 10,
                    zIndex: 100,
                  }}
                >
                  <Button color="primary" onClick={() => handelAddRemark(btnFlag)}>
                    备注
                  </Button>
                  <Button color="primary" onClick={() => handelAddtab(btnFlag)}>
                    标记
                  </Button>
                  {btnFlag ? (
                    <Button
                      style={{
                        display:
                          dispatchDataSelectTag.length === 0 ||
                          dispatchDataSelectTag.includes(null) ||
                          dispatchDataSelectTag.includes('') > 0
                            ? 'none'
                            : 'inline-block',
                      }}
                      color="primary"
                      onClick={() => handelDelTag('sended')}
                    >
                      删除标记
                    </Button>
                  ) : (
                    <Button
                      style={{
                        display:
                          tableDataSelectTag.length === 0 ||
                          tableDataSelectTag.includes(null) ||
                          tableDataSelectTag.includes('') > 0
                            ? 'none'
                            : 'inline-block',
                      }}
                      color="primary"
                      onClick={() => handelDelTag('send')}
                    >
                      删除标记
                    </Button>
                  )}
                  <Button color="primary" onClick={() => handleCloseOrder(btnFlag)}>
                    关闭
                  </Button>
                  {/* {btnFlag && (
                    <Button color="primary" onClick={() => handleCancelOrder()}>
                      撤销
                    </Button>
                  )} */}
                </div>
                <div style={{ padding: 10}}>
                  <Tabs activeKey={tabKey} defaultActiveKey="1" onChange={changeTab}>
                    <Tabs.TabPane tab="待派工单" key="send">
                      <Spin spinning={loading}>
                        <div style={{ marginBottom: 10 }}>
                          <img
                            style={{ marginRight: 10 }}
                            src={LocationBlue}
                            // src={record.data.workOrderStatus !== 'RELEASED' ? arrowheadGreen : arrowheadRed}
                          ></img>
                          <span style={{ marginRight: 20 }}>工单总数：{tabletotal}</span>
                          {/* <span>当前已派工进度：{tableRate}</span> */}
                        </div>
                        <div>
                          <Table
                            queryBar="filterBar"
                            queryBarProps={{
                              fuzzyQuery: false,
                            }}
                            dataSet={dataSet}
                            columns={columns}
                            searchCode="MachineProductionDispatch"
                            customizedCode="MachineProductionDispatch"
                            style={{ height: 500 }}
                          ></Table>
                        </div>
                      </Spin>
                    </Tabs.TabPane>
                    <Tabs.TabPane tab="已派工单" key="sended">
                      <Spin spinning={loading}>
                        <div style={{ marginBottom: 10 }}>
                          <img
                            style={{ marginRight: 10 }}
                            src={LocationBlue}
                            // src={record.data.workOrderStatus !== 'RELEASED' ? arrowheadGreen : arrowheadRed}
                          ></img>
                          <span style={{ marginRight: 20 }}>工单总数：{sendTableTotal}</span>
                          <span>当前已派工进度：{sendTableRate}</span>
                        </div>
                        <Table
                          queryBar="filterBar"
                          queryBarProps={{
                            fuzzyQuery: false,
                          }}
                          // onRow={({ record }) => {
                          //   return {
                          //     onClick: () => {
                          //       handleRowClick(record);
                          //     },
                          //   };
                          // }}
                          dataSet={sendTableDs}
                          columns={sendColumns}
                          searchCode="MachineProductionDispatch"
                          customizedCode="MachineProductionDispatch"
                          style={{ height: 400 }}
                        />
                      </Spin>
                    </Tabs.TabPane>
                  </Tabs>
                </div>
              </Spin>
            }
            aside={
              <div style={{ height: '100%', overflowX: 'hidden', overflowY: 'auto' }}>
                <Row gutter={12}>
                  <div>
                    <div style={{ width: '70%', float: 'left' }}>
                      <Form dataSet={machinePlatformDs} labelWidth={80}>
                        <Lov name="operationObj" />
                      </Form>
                    </div>
                    <div style={{ width: '30%', float: 'left', textAlign: 'right', padding: 15 }}>
                      <Button
                        onClick={() =>
                          handleProcessData(
                            workOrderInfo,
                            currentShiftCode,
                            currentShiftDate,
                            machinePlatformDs?.current?.toData(),
                          )
                        }
                      >
                        {intl.get('tarzan.aps.common.button.search').d('查询')}
                      </Button>
                    </div>
                  </div>
                </Row>
                <Row gutter={12}>
                  <Col span={24}>
                    <MachineLoadNew {...machineLoadNewProps} />
                    {/* <MachineLoad {...machineLoadProps} /> */}
                  </Col>
                </Row>
              </div>
            }
            asideShow
            changePageColumns={setPageColumns}
          />
        </div>
      </Content>
    </div>
  );
});
export default formatterCollections()(MachineProductionDispatch);
