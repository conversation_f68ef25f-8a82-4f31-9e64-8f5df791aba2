import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
// import { BASIC } from '@utils/config';
import { Host } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.equipmentPointMaintenanceList';

const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
      required: true,
      textField: 'siteCode',
      valueField: 'siteId',
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'assemblePointCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
      // pattern: '/[\u4e00-\u9fa5]/g',
      required: true,
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('装配点描述'),
    },
    {
      name: 'assemblePointTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.assemblePointTypeLov`).d('装配点类型'),
      lovCode: 'HME.ASSEMBLE_POINT_TYPE_LOV',
      textField: 'assemblePointTypeName',
      valueField: 'assemblePointTypeCode',
    },
    {
      name: 'assemblePointTypeCode',
      bind: 'assemblePointTypeLov.assemblePointTypeCode',
    },
    {
      name: 'assemblePointTypeName',
      bind: 'assemblePointTypeLov.assemblePointTypeName',
    },
    {
      type: FieldType.string,
      name: 'enableFlag',
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
});


const lineTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  forceValidate: true,
  paging: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'siteId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'HME.ASSEMBL_EPOINT_SITE_MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialCode',
      required: true,
      dynamicProps: {
        lovPara: ({record}) => {
          return {
            tenantId: getCurrentOrganizationId(),
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialSiteId',
      type: FieldType.number,
      bind: 'materialLov.materialSiteId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'materialLov.revisionFlag',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      bind: 'materialLov.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'uomId',
      type: FieldType.number,
      bind: 'materialLov.uomId',
    },
    {
      name: 'materialSiteId',
      type: FieldType.number,
      bind: 'materialLov.materialSiteId',
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('物料单位'),
      bind: 'materialLov.uomName',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      bind: 'materialLov.revisionCode',
      textField: 'revisionCode',
      valueField: 'revisionCode',
      lookupUrl: `${Host}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
      lookupAxiosConfig: ({ record }) => {
        const _params = record?.toData() || {};
        return {
          params: {
            materialSiteId: _params.materialSiteId,
            siteId: record?.get('siteId'),
          },
          transformResponse(data) {
            // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
            if (data instanceof Array) {
              return data;
            }
            if (data.failed) {
              return [];
            }
            const rows = JSON.parse(data);
            return rows;

          },
        };
      },
      dynamicProps: {
        disabled: ({record}) => {
          return record.toData()?.revisionFlag !== 'Y'
        },
      },
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
      bind: 'materialLov.bomCode',
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
      bind: 'materialLov.model',
    },
    {
      name: 'maxQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxQty`).d('最大装载量'),
    },
    {
      type: FieldType.string,
      name: 'enableFlag',
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-assemble-point-materials/get/assemble/point/material`,
        method: 'GET',
      };
    },
  },
});

export { detailDS, lineTableDS };


