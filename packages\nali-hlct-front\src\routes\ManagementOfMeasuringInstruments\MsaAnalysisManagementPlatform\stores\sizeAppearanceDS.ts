/*
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2024-02-02 14:49:10
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-02-04 15:49:00
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';

const tenantId = getCurrentOrganizationId();

const formDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'checkLocation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.checkLocation`).d('测量位置'),
      required: true,
    },
    {
      name: 'tolerance',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.tolerance`).d('公差'),
      required: true,
    },
    {
      name: 'sampleDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleDescription`).d('基准件名称'),
    },
    {
      name: 'improveByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.measuredBy`).d('测量人'),
      lovCode: 'YP.QIS.UNIT_USER',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'measuredBy',
      bind: 'improveByObj.id',
      // defaultValue: userInfo.id,
    },
    {
      name: 'measuredByName',
      bind: 'improveByObj.realName',
      // defaultValue: userInfo.realName,
    },
  ],
});

const getFieldsGroup = (columnIndex) => ([
  {
    name: `sequence_${columnIndex}`,
    type: FieldType.number,
    label: intl.get(`${modelPrompt}.sequence`).d('序号'),
  },
  {
    name: `measureDataValue_${columnIndex}`,
    type: FieldType.number,
    label: intl.get(`${modelPrompt}.measureDataValue`).d('测量值'),
    dynamicProps: {
      required: ({ record }) => record?.get(`standardValue_${columnIndex}`),
    },
  },
  {
    name: `standardValue_${columnIndex}`,
    type: FieldType.number,
    label: intl.get(`${modelPrompt}.standardMeasureValue`).d('标准件测量值'),
    dynamicProps: {
      required: ({ record }) => record?.get(`measureDataValue_${columnIndex}`),
    },
  },
  {
    name: `absoluteErrorValue_${columnIndex}`,
    type: FieldType.number,
    label: intl.get(`${modelPrompt}.absoluteErrorValue`).d('样本误差'),
  },
])

const tableDS: (columnGroupQty) => DataSetProps = (columnGroupQty) => {
  let _fields: any = [];
  for (let i = 0; i < columnGroupQty; i++) {
    _fields = [
      ..._fields,
      ...getFieldsGroup(i),
    ];
  }
  return {
    forceValidate: true,
    autoCreate: false,
    selection: false,
    paging: false,
    fields: _fields,
  }
}

export { formDS, tableDS };
