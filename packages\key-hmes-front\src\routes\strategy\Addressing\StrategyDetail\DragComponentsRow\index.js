/**
 * @Description: 列拖拽
 * @Author: <<EMAIL>>
 * @Date: 2021-09-14 15:41:22
 * @LastEditTime: 2021-09-17 17:44:27
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
// import { isArray } from 'lodash';
import DragContainer from './dragContainer';

// const childListFormat = (_list) => {
//   const _childListMap = {};
//   _list.forEach((item) => {
//     if (item.child && isArray(item.child)) {
//       _childListMap[item.questionTuid] = item.child;
//     }
//   });
//   return _childListMap;
// };

const DragComponents = ({ list, canEdit }, ref) => {
  useEffect(() => {
    setPreviewList([...list]);
    // setChild(childListFormat(list));
  }, [list]);

  const [previewList, setPreviewList] = useState(list);

  // const [childList, setChild] = useState(childListFormat(list));

  const handleChildListChange = (questionTuid, newChildList) => {
    const _previewList = [];
    previewList.forEach(item => {
      const _item = { ...item };
      if (`${item.questionTuid}` === `${questionTuid}`) {
        _item.child = newChildList;
      }
      _previewList.push(_item);
    });
    setPreviewList([..._previewList]);

    // const _childList = { ...childList };
    // _childList[questionTuid] = newChildList;
    // setChild(_childList);
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    // resetList: () => {
    //   setPreviewList(list)
    // },
    getCoordinateList: () => {
      return {
        rowList: previewList,
        // colList: childList,
      };
    },
  }));

  const handlePreviewList = _list => {
    setPreviewList([..._list]);
  };

  return (
    <DragContainer
      handlePreviewList={handlePreviewList}
      previewList={previewList}
      handleChildListChange={handleChildListChange}
      canEdit={canEdit}
    />
  );
};

export default forwardRef(DragComponents);
