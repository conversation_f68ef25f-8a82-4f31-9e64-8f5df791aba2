/**
 * @since 2020-03-10
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { mmtFields, atnFields } from 'alm/fields';

const tenantId = getCurrentOrganizationId();
const promptCode = 'aatn.assetTransactionBasicType.model.assetTransactionBasicType';

/**
 * 资产信息表格
 */
function tableDS() {
  return {
    autoCreate: false,
    autoQuery: false,
    dataKey: 'content',
    selection: 'single', // single
    primaryKey: 'assetId',
    cacheSelection: true,
    queryFields: [
      {
        name: 'assetNum',
        type: 'string',
        label: intl.get(`${promptCode}.assetNum`).d('设备/资产编号'),
      },
      {
        name: 'detailCondition',
        type: 'string',
        label: intl.get(`${promptCode}.assetDesc`).d('资产全称'),
      },
    ],
    fields: [
      {
        name: 'assetNum',
        type: 'string',
        label: intl.get(`${promptCode}.assetNum`).d('设备/资产编号'),
      },
      {
        name: 'assetDesc',
        type: 'string',
        label: intl.get(`${promptCode}.assetDesc`).d('资产全称'),
      },
      {
        name: 'processStatus',
        type: 'string',
      },
      {
        name: 'assetStatusName',
        type: 'string',
        label: intl.get(`${promptCode}.deviceStatus`).d('设备状态'),
      },
      {
        name: 'todo2',
        type: 'string',
        label: intl.get(`${promptCode}.todo2`).d('正在处理的事务单据'),
      },
      {
        name: 'lineNum', // 行号
        type: 'string',
      },
      {
        name: 'changeNum', // 单号
        type: 'string',
      },
      {
        name: 'transactionTypeName', // 类型
        type: 'string',
      },
    ],
    dataToJSON: 'selected',
    transport: {
      read: config => {
        const { params } = config;
        return {
          params: {
            ...params,
            bothFlag: 1,
          },
          url: `${HALM_ATN}/v1/${tenantId}/asset-info/list`,
          method: 'GET',
        };
      },
      submit: ({ params, data, dataSet }) => {
        const { transactionTypeId, changeHeaderId } = dataSet;
        const url = `${HALM_ATN}/v1/${tenantId}/tp-change-lines/createTemplateByAssetId`;
        const tpChangeLines = data.map(i => ({
          objectId: i.assetId,
          objectNum: i.assetNum,
          objectDesc: i.assetDesc,
          objectType: 'ASSET',
          requisitionsNumber: 1,
          transactionTypeId,
          changeHeaderId,
          tenantId,
          currencyCode: 'CNY', // 处置新增行时币种的默认值
        }));
        const headData = dataSet.getState('headData') ?? {};
        const newData = { ...headData, tpChangeLines };
        return {
          url,
          data: newData, // { ...currentObj, enabledFlag },
          params,
          method: 'POST',
        };
      },
    },
    events: {
      load: ({ dataSet }) => {
        // 对不能选择的数据（行号lineNum有值并且状态不是CANCELED, COMPLETED, RETURNED都进行禁用
        dataSet.records.forEach(i => {
          const { lineNum, processStatus } = i.toData();
          if (lineNum && !['CANCELED', 'COMPLETED', 'RETURNED'].includes(processStatus)) {
            // eslint-disable-next-line no-param-reassign
            i.selectable = false;
          }
        });
      },
    },
  };
}

// 选择物料lovBtnDS
function lovBtnDS() {
  return {
    autoQuery: true,
    fields: [
      {
        ...mmtFields.itemLov,
        multiple: ',',
        computedProps: {
          lovPara: ({ dataSet }) => ({
            tenantId,
            itemCategoryScope: dataSet.getState('itemCategoryScope'),
          }),
        },
      },
      {
        ...mmtFields.itemId,
        multiple: ',',
      },
      {
        ...mmtFields.itemName,
        multiple: ',',
      },
      {
        ...atnFields.standardAssetLov,
        multiple: ',',
        computedProps: {
          lovPara: ({ dataSet }) => ({
            tenantId,
            toolingScope: dataSet.getState('toolingScope'),
          }),
        },
      },
      {
        ...atnFields.standardAssetId,
        multiple: ',',
      },
      {
        ...atnFields.standardAssetName,
        multiple: ',',
      },
    ],
  };
}
export { tableDS, lovBtnDS };
