/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2021-12-20 10:38:20
 * @LastEditTime: 2021-12-20 14:20:15
 * @LastEditors: <<EMAIL>>
 */
/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-10-12
 * @description 执行执行规则维护-DS
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';
import { onPassageLocatorTypeOptionDs } from './detailDs';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.commonConfig.orderExecuteRuleMes';

const typeOptions = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_TOLERANCE_TYPE', tenantId },
      };
    },
  },
});

const strategyOptions = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'INSTRUCTION_EXE_STRATEGY', tenantId },
      };
    },
  },
});

const tableDetailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'instruction',
      type: FieldType.object,
      lovCode: 'MT.COMMON.INSTRUCTION_TYPE',
      label: intl.get(`${modelPrompt}.instructionType`).d('移动类型'),
      required: true,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      ignore: 'always',
    },
    {
      name: 'instructionType',
      bind: 'instruction.typeCode',
    },
    {
      name: 'instructionTypeDesc',
      bind: 'instruction.description',
    },
    {
      name: 'business',
      type: FieldType.object,
      lovCode: 'MT.COMMON.BUSINESS_TYPE',
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      lovPara: { tenantId },
      ignore: 'always',
    },
    {
      name: 'businessType',
      bind: 'business.typeCode',
    },
    {
      name: 'businessTypeDesc',
      bind: 'business.description',
    },
    {
      name: 'instructionExeStrategyObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionExeStrategy`).d('指令执行策略'),
      options: strategyOptions,
      textField: 'description',
      valueField: 'typeCode',
      required: 'true',
    },
    {
      name: 'instructionExeStrategyDesc',
      bind: 'instructionExeStrategyObj.description',
    },
    {
      name: 'instructionExeStrategy',
      bind: 'instructionExeStrategyObj.typeCode',
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      required: true,
    },
    {
      name: 'addStrategyFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.uniqueMaterialLotFlag`).d('启用寻址策略'),
      defaultValue: 'Y',
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允许允差'),
      defaultValue: 'Y',
    },
    {
      name: 'toleranceTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toleranceFlagType`).d('允差类型'),
      options: typeOptions,
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('toleranceFlag') !== 'Y';
        },
        required: ({ record }) => {
          return record.get('toleranceFlag') === 'Y';
        },
      },
    },
    {
      name: 'toleranceTypeDesc',
      bind: 'toleranceTypeObj.description',
    },
    {
      name: 'toleranceType',
      bind: 'toleranceTypeObj.typeCode',
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差值'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !(
            record.get('toleranceType') === 'PERCENTAGE' || record.get('toleranceType') === 'NUMBER'
          );
        },
        required: ({ record }) => {
          return (
            record.get('toleranceType') === 'PERCENTAGE' || record.get('toleranceType') === 'NUMBER'
          );
        },
      },
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差值'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !(
            record.get('toleranceType') === 'PERCENTAGE' || record.get('toleranceType') === 'NUMBER'
          );
        },
        required: ({ record }) => {
          return (
            record.get('toleranceType') === 'PERCENTAGE' || record.get('toleranceType') === 'NUMBER'
          );
        },
      },
    },
    {
      name: 'onPassageLocatorDirectionObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.onPassageLocatorDirection`).d('在途库位方向'),
      lookupCode: 'MT.ON_PASSAGE_LOCATOR_DIRECTION',
      valueField: 'value',
      textField: 'meaning',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('instructionDocExeStrategy') !== 'TWO_STEP' || record.get('onPassageLocatorId');
        },
        required: ({ record }) => {
          return record.get('onPassageLocatorTypeDesc').length;
        },
      },
    },
    {
      name: 'onPassageLocatorDirection',
      bind: 'onPassageLocatorDirectionObj.value',
    },
    {
      name: 'onPassageLocatorDirectionDesc',
      bind: 'onPassageLocatorDirectionObj.meaning',
    },
    {
      name: 'onPassageLocatorTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.onPassageLocatorType`).d('在途库位类型'),
      options: onPassageLocatorTypeOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      multiple: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('instructionDocExeStrategy') !== 'TWO_STEP'  || record.get('onPassageLocatorId');
        },
        required: ({ record }) => {
          return record.get('onPassageLocatorDirection');
        },
      },
    },
    {
      name: 'onPassageLocatorType',
      bind: 'onPassageLocatorTypeObj.typeCode',
    },
    {
      name: 'onPassageLocatorTypeDesc',
      bind: 'onPassageLocatorTypeObj.description',
    },
    {
      name: 'onPassageLocatorObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.onPassageLocator`).d('在途库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('instructionDocExeStrategy') !== 'TWO_STEP' || record.get('onPassageLocatorDirection') || record.get('onPassageLocatorTypeDesc').length;
        },
      },
    },
    {
      name: 'onPassageLocatorId',
      bind: 'onPassageLocatorObj.locatorId',
    },
    {
      name: 'onPassageLocatorCode',
      bind: 'onPassageLocatorObj.locatorCode',
    },
    {
      name: 'transferFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.transferFlag`).d('生成事务'),
      defaultValue: 'N',
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.initialFlag`).d('初始化'),
      defaultValue: 'N',
      disabled: true,
    },
    {
      name: 'instructionDocExeStrategy',
    },
  ],
  transport: {},
});

export { tableDetailDS };
