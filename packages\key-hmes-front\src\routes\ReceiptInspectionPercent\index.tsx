import React, { useEffect, useState } from 'react';
import { Table, DataSet, YearPicker, Button } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { useDataSetEvent } from 'utils/hooks';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import { tableDS, footTableDS } from './stores';
import './index.module.less';

const modelPrompt = 'receiptInspectionPercent';
const tenantId = getCurrentOrganizationId();

const ReceiptInspectionPercent = (props) => {
  const {
    tableDs,
    footTableDs,
  } = props;

  const [columns, setColumns] = useState([
    {
      name: 'period',
      align: ColumnAlign.center,
    },
  ])

  useEffect(() => {
    getQueryList();
  },[])


  const getQueryList = () => {
    const data = tableDs.queryDataSet.toData()[0];
    const queryYears = data.queryYears.map(item => item.replace('00:00:00', '').trim());
    request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/report/fqc/query`, {
      method: 'POST',
      data: {
        ...data,
        queryYears,
      },
    }).then(res => {
      if (res?.success) {
        const dates = res.rows.dates || []
        const dateDescList = res.rows.dateDescList || []
        const materialContent = res.rows.materialContent || []
        dates.unshift('时间维度')
        const dateList = dates.map((item, index) => {
          if (item === '时间维度') {
            return {
              date: 'timeDimension',
              label: item,
            }
          }
          return {
            date: item,
            label: `${dateDescList[index - 1]}`,
          }
        })
        const _children: any[] = [];
        dateList.forEach(item => {
          if (item.date === 'timeDimension') {
            _children.push({
              name: item.date,
              width: 150,
            });
          }else{
            _children.push({
              name: item.date,
            });
          }
          tableDs.addField(item.date, {
            name: item.date,
            label: item.label,
            type: 'string',
          })
          footTableDs.addField(item.date, {
            name: item.date,
            label: item.label,
            type: 'string',
          })
        })
        _children.push({
          header: intl.get(`${modelPrompt}.total`).d('汇总'),
          renderer: ({ record, dataSet }) => {
            let total = 0
            let totalReat: any = '0%'
            let value: any = 0;
            let oldValue: any = 0;
            const data = record.toData();
            const filterList = dataSet.toData().filter(item => item.materialName === data.materialName)
            dates.forEach(item => {
              if (data[item]) {
                total += data[item] * 1
              }
              if (filterList.length === 3 && data.timeDimension === 'LAR') {
                value += Number(filterList[1][item] ? filterList[1][item] : 0);
                oldValue += Number(filterList[0][item] ? filterList[0][item] : 0);
              } else if (filterList.length === 4 && data.timeDimension === 'LAR') {
                value += Number(filterList[2][item] ? filterList[2][item] : 0);
                oldValue += Number(filterList[1][item] ? filterList[1][item] : 0);
              }
            })
            totalReat = `${((value / oldValue) * 100).toFixed(2)}%`
            if (data.timeDimension === '时间维度') {
              return '汇总'
            }
            if (data.timeDimension === 'LAR') {
              return totalReat;
            }
            return total;
          },
        })

        const timeDimensionList = ['检验批数', '合格批数', 'LAR'];
        const footTableList = ['时间维度','检验批数', '合格批数', 'LAR'];
        const contentList = materialContent || [];
        const materialList = [...new Set(contentList.map((sup: any) => sup.materialName))];
        const content = materialList.map(str => {
          const list = timeDimensionList.map(ele => {
            const obj: any = {};
            dates.forEach(item => {
              obj.materialName = str;
              obj.timeDimension = ele;
              obj[item] = null;
            })
            return obj;
          })
          return list
        }).flat()
        const _content = content.map(item => {
          contentList.forEach(obj => {
            if (item.materialName === obj.materialName && item.timeDimension === timeDimensionList[0]) {
              item[obj.date] = obj.numberOfQuantity
            }
            if (item.materialName === obj.materialName && item.timeDimension === timeDimensionList[1]) {
              item[obj.date] = obj.numberOfQualifiedQuantity
            }
            if (item.materialName === obj.materialName && item.timeDimension === timeDimensionList[2]) {
              item[obj.date] = obj.lar
            }
          })
          return item;
        })
        const footDataList = footTableList.map(ele => {
          const obj: any = {
            timeDimension: ele,
          };
          dates.forEach((item,index) => {
            if (obj.timeDimension === 'LAR') {
              obj[item] = '0%'
            } else if (obj.timeDimension === '时间维度') {
              obj[item] = dateDescList[index - 1]
            } else{
              obj[item] = 0
            }
          })
          return obj;
        })
        const footTotalData = footDataList.map(item => {
          item.materialName = '汇总';
          _content.forEach(obj => {
            if (item.timeDimension === obj.timeDimension) {
              dates.forEach(key => {
                if (item.timeDimension !== 'LAR') {
                  item[key] += obj[key] ? Number(obj[key]) : 0
                }
              })
            }
          })
          return item;
        })
        dates.forEach(item => {
          if (footTotalData[2][item] && footTotalData[1][item]) {
            footTotalData[3][item] = `${((footTotalData[2][item] / footTotalData[1][item]) * 100).toFixed(2)}%`
          } else {
            footTotalData[3][item] = '0.00%'
          }
        })
        setColumns([
          {
            name: 'period',
            align: ColumnAlign.center,
            children: _children,
          },
        ])
        setTimeout(() => {
          tableDs.loadData(_content)
          footTableDs.loadData(footTotalData)
        })
      }
    })
  }

  useDataSetEvent(tableDs.queryDataSet, 'update', async({ name, record }) => {
    if (name === 'timePeriod'  && record.get('timePeriod') === 'WEEK') {
      record.set('queryYears', null);
    }
    if (name === 'timePeriod' && record.get('timePeriod') !== 'WEEK') {
      record.set('startTime', null);
      record.set('endTime', null);
    }
    const validate = await tableDs.queryDataSet.validate();
    if (!validate) {
      return;
    }
    getQueryList();
  });

  const groups = [
    {
      name: 'materialName',
      type: 'column',
      columnProps: {
        width: 240,
        align: 'center',
      },
    },
  ];

  const handleExport = () => {
    const params = tableDs.queryDataSet.toData()[0];
    request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/report/fqc/export`, {
      method: "POST",
      body: {
        ...params,
        queryYears: params.queryYears.map(item => item.replace('00:00:00', '').trim()),
      },
      responseType: 'blob',
    }).then((response) => {
      if (response) {
        const fileName = `入库检验合格率报表`;
        const elink = document.createElement('a');
        elink.download = fileName ? window.decodeURI(fileName) : `入库检验合格率报表`;
        elink.style.display = 'none';
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        document.body.removeChild(elink);
      }
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('入库检验合格率报表')}>
        <Button color={ButtonColor.primary} onClick={handleExport}>{intl.get(`${modelPrompt}.export`).d('导出')}</Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            onRefresh: () => {
              getQueryList();
              return false;
            },
          }}
          queryFields={{
            queryYears: <YearPicker />,
          }}
          dataSet={tableDs}
          columns={columns}
          groups={groups as any}
          searchCode="receiptInspectionPercent"
          customizedCode="receiptInspectionPercent"
          virtual
          style={{ height: 'calc(100vh - 415px)' }}
        />
        <Table className='footer-table' dataSet={footTableDs} columns={columns} groups={groups as any} />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      const footTableDs = new DataSet({
        ...footTableDS(),
      })
      return {
        tableDs,
        footTableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ReceiptInspectionPercent),
);
