/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-06-28 15:05:32
 * @LastEditTime: 2023-06-29 14:56:10
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 获取指令执行规则
 * @function FetchInstructionRule
 * @returns {object} fetch Promise
 */
export function FetchInstructionRule(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-purchase-return-platform/purchase-return/exe/rule/query`,
    method: 'GET',
  };
}

/**
 * 保存采购退货单
 * @function SaveInstructionReturnDoc
 * @returns {object} fetch Promise
 */
export function SaveInstructionReturnDoc(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-purchase-return-platform/purchase-return/save/ui`,
    method: 'POST',
  };
}

/**
 * 状态变更
 * @function StatusAlter
 * @returns {object} fetch Promise
 */
export function StatusAlter(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-purchase-return-platform/purchase-return/status/alter/ui`,
    method: 'POST',
  };
}
