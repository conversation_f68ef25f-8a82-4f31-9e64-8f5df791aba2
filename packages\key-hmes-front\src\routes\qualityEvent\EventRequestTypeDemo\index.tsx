/**
 * @Description: 事件请求类型维护（重构） - 列表页
 * @Author: <EMAIL>
 * @Date: 2022-09-06 15:44:38
 */
import React from 'react';
import { Badge } from 'choerodon-ui';
import { Table, Switch, TextField, DataSet } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';

import intl from 'utils/intl';
import withProps from "utils/withProps";
import formatterCollections from 'utils/intl/formatterCollections';
import notification from "utils/notification";
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';

import { useRequest } from '@components/tarzan-hooks';
import { eventRequestTypeCus } from '@/hcmMesCus';
import { ContextWrapper } from './utils/cusdevHelper';
import { ContextConsumer } from './context';
import { SaveEventRequestType } from './services';
import { tableDS } from "./stores/TableDS";

const EventRequestTypeList = (props) => {
  const {
    tableDs,
    match: { path },
    context,
  } = props;
  const saveEventRequestType = useRequest(SaveEventRequestType(),{ manual: true });

  const handleSave = async record => {
    const validateResult = await record?.validate();
    if (validateResult) {
      saveEventRequestType.run({
        params: {...record.toData()},
        onSuccess: () => {
          notification.success({});
          tableDs.query(tableDs.currentPage);
        },
      })
    }
  };

  const handleCancel = (record) => {
    if (record.status === 'add') {
      tableDs.remove(record);
    } else {
      record.reset();
      record.setState('editing', false);
    }
  };

  // 新增的回调
  const handleCreate = () => {
    if (tableDs.created.length === 0) {
      const record = tableDs.create({},0);
      record.setState('editing', true);
    }
  };

  const columns: ColumnProps[] = [
    {
      name: 'requestTypeCode',
      // eslint-disable-next-line
      editor: record => record.getState('editing') && <TextField restrict={/[^\w\.\-\//]/ig} />,
    },
    {
      name: 'description',
      editor: record => record.getState('editing'),
    },
    {
      name: 'enableFlag',
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
      editor: record => record.getState('editing') && <Switch />,
    },
    ...context?.cusTableColumns || [],
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      width: 200,
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            {record?.getState('editing') ? (
              <>
                <a onClick={() => handleCancel(record)}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </a>
                <PermissionButton
                  type="text"
                  permissionList={[
                    {
                      code: `${path}.button.edit`,
                      type: 'button',
                      meaning: '列表页-编辑新建删除复制按钮',
                    },
                  ]}
                  onClick={() => handleSave(record)}
                >
                  {intl.get('tarzan.common.button.save').d('保存')}
                </PermissionButton>
              </>
            ) : (
              <PermissionButton
                type="text"
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
                onClick={() => record?.setState('editing', true)}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </PermissionButton>
            )}
          </span>
        );
      },
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.event.requestType.title.list').d('事件请求类型维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`tarzan.common.button.create`).d('新建')}
        </PermissionButton>
        {context?.cusPagePrintButton && context?.cusPagePrintButton()}
      </Header>
      <Content>
        <Table
          searchCode="sjqqlxwh1"
          customizedCode="sjqqlxwh1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.event.requestType', 'tarzan.common'],
})(withProps(
  () => {
    const dsProps = eventRequestTypeCus?.tableDsPropsBefHook
      ? eventRequestTypeCus?.tableDsPropsBefHook(tableDS())
      : tableDS;
    const tableDs = new DataSet({
      ...dsProps(),
    });
    return {
      tableDs,
    };
  },
  { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
)(ContextWrapper(ContextConsumer, EventRequestTypeList)));
