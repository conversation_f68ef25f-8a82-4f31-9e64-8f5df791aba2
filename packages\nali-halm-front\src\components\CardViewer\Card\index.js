/**
 * 卡片
 * @since 2021-01-05
 * <AUTHOR>
 * @copyright Copyright (c) 2021, Hand
 */
import React from 'react';
import { connect } from 'dva';
import intl from 'utils/intl';
import Bind from 'lodash-decorators/bind';
import { routerRedux } from 'dva/router';
import classnames from 'classnames';
import { DataSet } from 'choerodon-ui/pro';
import { Tag, Divider, Row, Col, Tooltip } from 'choerodon-ui';

import selectPng from 'alm/assets/select-square.png';
import viewPng from 'alm/assets/icon-view.png';
import editPng from 'alm/assets/icon-edit.png';
import CardDS from './CardDS';
import styles from './index.module.less';

/**
 * 页面多语言加载key，请勿随意修改。
 */
const promptCode = 'aatn.equipmentAsset.model.equipmentAsset';
const intlHzeroCommon = 'hzero.common';

@connect()
export default class AccessoryCard extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
    this.cardDS = new DataSet(CardDS());
    this.fields = CardDS().fields;
  }

  componentDidMount() {}

  @Bind()
  handleGoToDetail(e, key, editFlag) {
    const { dispatch } = this.props;
    e.stopPropagation();
    dispatch(
      routerRedux.push({
        pathname: `/aafm/equipment-asset/detail/${key}`,
        state: {
          isEdit: editFlag,
        },
      })
    );
  }

  render() {
    const { record = {}, isSelected = false } = this.props;
    const { assetStatusList = [] } = record;
    return (
      <div
        className={classnames({
          [styles['card-basic']]: true,
          [styles['card-select']]: isSelected,
        })}
      >
        <div className={styles['card-header']}>
          <div className={styles['card-header-right']}>
            <img
              style={{ visibility: isSelected ? 'initial' : 'hidden' }}
              className={styles['card-check']}
              src={selectPng}
              alt=""
            />
          </div>
          <div className={styles['card-header-description']}>
            <div className={styles['card-header-title']}>
              <Tooltip title={record.assetName}>{record.assetName}</Tooltip>
            </div>
            <div className={styles['card-status-tag']}>
              {assetStatusList && assetStatusList.length > 0
                ? assetStatusList.map(i => {
                    return (
                      <Tooltip title={i.assetStatusName}>
                        <Tag
                          style={{
                            color: i.fontColor || '#000',
                            border: 0,
                          }}
                          color={i.backgroundColor || '#fff'}
                        >
                          {i.assetStatusName}
                        </Tag>
                      </Tooltip>
                    );
                  })
                : ''}
            </div>
          </div>
        </div>
        <div className={styles['card-body']}>
          <Row className={styles['card-row']}>
            <Col span={12}>
              <Row className={styles['card-field']}>
                <div className={styles['field-label']}>
                  {intl.get(`${promptCode}.assetNum`).d('资产编号')}
                </div>
                <div className={styles['field-value']}>
                  <Tooltip title={record.assetNum}>{record.assetNum}</Tooltip>
                </div>
              </Row>
            </Col>
            <Col span={12}>
              <Row className={styles['card-field']}>
                <div className={styles['field-label']}>
                  {intl.get(`${promptCode}.assetLocation`).d('资产位置')}
                </div>
                <div className={styles['field-value']}>
                  <Tooltip title={record.assetLocationName}>{record.assetLocationName}</Tooltip>
                </div>
              </Row>
            </Col>
          </Row>
          <Row className={styles['card-row']}>
            <Col span={12}>
              <Row className={styles['card-field']}>
                <div className={styles['field-label']}>
                  {intl.get(`${promptCode}.usingOrg`).d('使用组织')}
                </div>
                <div className={styles['field-value']}>
                  <Tooltip title={record.usingOrgName}>{record.usingOrgName}</Tooltip>
                </div>
              </Row>
            </Col>
            <Col span={12}>
              <Row className={styles['card-field']}>
                <div className={styles['field-label']}>
                  {intl.get(`${promptCode}.userPerson`).d('使用人')}
                </div>
                <div className={styles['field-value']}>
                  <Tooltip title={record.userPersonName}>{record.userPersonName}</Tooltip>
                </div>
              </Row>
            </Col>
          </Row>
          <Row className={styles['card-row']}>
            <Col span={12}>
              <Row className={styles['card-field']}>
                <div className={styles['field-label']}>
                  {intl.get(`${promptCode}.model`).d('规格/型号')}
                </div>
                <div className={styles['field-value']}>
                  <Tooltip title={record.model}>{record.model}</Tooltip>
                </div>
              </Row>
            </Col>
            <Col span={12}>
              <Row className={styles['card-field']}>
                <div className={styles['field-label']}>
                  {intl.get(`${promptCode}.useYear`).d('使用年限')}
                </div>
                <div className={styles['field-value']}>
                  <Tooltip title={record.year}>{record.useYear}</Tooltip>
                </div>
              </Row>
            </Col>
          </Row>
        </div>
        <div className={styles['card-footer']}>
          <div
            className={classnames('footer-view')}
            onClick={e => this.handleGoToDetail(e, record.assetId, false)}
          >
            <img src={viewPng} alt="" />
            <span>{intl.get(`${intlHzeroCommon}.button.view`).d('查看')}</span>
          </div>
          <Divider type="vertical" />
          <div
            className={classnames('footer-edit')}
            onClick={e => this.handleGoToDetail(e, record.assetId, true)}
          >
            <img src={editPng} alt="" />
            <span>{intl.get(`${intlHzeroCommon}.button.edit`).d('编辑')}</span>
          </div>
        </div>
      </div>
    );
  }
}
