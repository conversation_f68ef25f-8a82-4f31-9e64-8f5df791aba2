/*
 * @Description: 异常处理平台
 * @Version: 0.0.1
 * @Autor: <EMAIL>
 * @Date: 2020-06-07 18:10:19
 */

import { getResponse } from 'utils/utils';
import { queryMapIdpValue, queryUnifyIdpValue } from 'services/api';
import {
  enterSite,
  commitException,
  getSiteList,
  enterEquipment,
  createExceptionRecord,
  showExceptionRecordModal,
  enterMaterial,
  fetchLineList,
  fetchPositionData,
  asyncSetData,
  fetchUserData,
  closeException,
  responseException,
  queryEmployee,
  getNowQty,
  queryDefaultResponsible,
  refreshUpgrade,
} from '@/services/exceptionHandlingPlatformService';

export default {
  namespace: 'exceptionHandlingPlatform',
  state: {
    defaultSite: {},
    exceptionList: [],
    equipmentInfo: {}, // 设备信息
    exceptionRecord: {}, // 历史记录
    materialInfo: {}, // 物料信息
    exceptionStatus: '', // 异常信息状态
    exceptionResultList: [], // 异常处理结果类型
    solvedResult: '', // 异常处理结果
    employName: '', // 操作人员ID
    materialCode: '', // 物料编码
    abnormalTypeList: [],
    localWorkCellObj: {}, // 用来存储选择的工作单元
  },
  effects: {
    // 获取默认工厂
    *getSiteList({ payload }, { call, put }) {
      const result = getResponse(yield call(getSiteList, payload));
      if (result) {
        yield put({
          type: 'updateState',
          payload: {
            defaultSite: result,
          },
        });
      }
      return result;
    },
    // 输入工位
    *enterSite({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          exceptionList: [],
        },
      });
      const result = yield call(enterSite, payload);
      const data = [];
      result.forEach((item) => {
        if(item.exceptionType === 'MAN'){
          data.push({
            ...item,
            exceptionTypeMeaning: '工艺异常',
          })
          
        }
        if(item.exceptionType === 'EQUIPMENT'){
          data.push({
            ...item,
            exceptionTypeMeaning: '设备异常',
          })
        } 
        if(item.exceptionType === 'MATERIAL'){
          data.push({
            ...item,
            exceptionTypeMeaning: '物资异常',
          })
        } 
        if(item.exceptionType === 'METHOD'){
          data.push({
            ...item,
            exceptionTypeMeaning: '品质异常',
          })
        } 
      })
      if (result && !result.failed) { 
        yield put({
          type: 'updateState',
          payload: {
            exceptionList: data,
          },
        });
      }
      return data;
    },
    // 获取Hzero值集
    *querySelect({ payload }, { call, put }) {
      const response = yield call(queryMapIdpValue, payload);
      const res = getResponse(response);

      const abnormalTypeList = getResponse(yield call(queryUnifyIdpValue, 'HME.EXCEPTION_TYPE'));
      yield put({
        type: 'updateState',
        payload: {
          exceptionResultList: res.exceptionResult || [],
          abnormalTypeList,
        },
      });
      return res;
    },
    // 异常提交
    *commitException({ payload }, { call }) {
      const result = getResponse(yield call(commitException, payload));
      return result;
    },
    // 扫描设备
    *enterEquipment({ payload }, { call, put }) {
      const result = getResponse(yield call(enterEquipment, payload));
      if (result) {
        yield put({
          type: 'updateState',
          payload: {
            equipmentInfo: result,
          },
        });
      }
      return result;
    },
    // 扫描物料
    *enterMaterial({ payload }, { call, put }) {
      const result = getResponse(yield call(enterMaterial, payload));
      if (result) {
        yield put({
          type: 'updateState',
          payload: {
            materialInfo: result,
          },
        });
      }
      return result;
    },
    // 创建异常消息记录
    *createExceptionRecord({ payload }, { call }) {
      const result = getResponse(yield call(createExceptionRecord, payload));
      return result;
    },
    // 查看历史
    *showExceptionRecordModal({ payload }, { call, put }) {
      const result = getResponse(yield call(showExceptionRecordModal, payload));
      if (result) {
        yield put({
          type: 'updateState',
          payload: {
            exceptionRecord: result,
            // 异常处理结果异常关闭点开默认已解决，异常响应未解决
            // solvedResult: result.attribute1 || 'N',
            solvedResult: result.attribute1 || result.attribute5 !== '1' ? 'N' : 'Y',
          },
        });
      }
      return result;
    },
    *fetchLineList({ payload }, { call }) {
      const res = getResponse(yield call(fetchLineList, payload));
      return res;
    },

    *fetchPositionData({ payload }, { call }) {
      const res = getResponse(yield call(fetchPositionData, payload));
      return res;
    },

    *asyncSetData({ payload }, { call }) {
      const res = getResponse(yield call(asyncSetData, payload));
      return res;
    },

    *fetchUserData({ payload }, { call }) {
      const res = getResponse(yield call(fetchUserData, payload));
      return res;
    },
    // 异常取消/异常关闭
    *closeException({ payload }, { call }) {
      const res = getResponse(yield call(closeException, payload));
      return res;
    },
    // 异常响应/异常升级
    *responseException({ payload }, { call }) {
      const res = getResponse(yield call(responseException, payload));
      return res;
    },
    // 扫描员工
    *queryEmployee({ payload }, { call }) {
      const res = getResponse(yield call(queryEmployee, payload));
      return res;
    },
    // 现有量获取
    *getNowQty({ payload }, { call }) {
      const result = getResponse(yield call(getNowQty, payload));
      return result;
    },
    // 查询默认责任人
    *queryDefaultResponsible({ payload }, { call }) {
      const result = getResponse(yield call(queryDefaultResponsible, payload));
      return result;
    },
    // 查询自动升级信息
    *refreshUpgrade({ payload }, { call }) {
      const result = getResponse(yield call(refreshUpgrade, payload));
      return result;
    },
  },

  reducers: {
    updateState(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
