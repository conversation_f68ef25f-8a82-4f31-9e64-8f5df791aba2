import React, { FC, useState } from 'react';
import { RouteComponentProps } from 'react-router';
import { Button, DataSet, Switch, Table } from 'choerodon-ui/pro';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { Buttons } from 'choerodon-ui/pro/lib/table/Table';
import axios from 'axios';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import notification from 'utils/notification';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Badge } from 'choerodon-ui';
import { BASIC } from '@utils/config';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import listPageFactory from '../stores/listPageFactory';


interface HierarchicalAuditProjectProps extends RouteComponentProps {
  tableDs: DataSet;
  customizeTable: any;
}

const modelPrompt = 'tarzan.qms.hierarchicalAuditProject';

const HierarchicalAuditProject: FC<HierarchicalAuditProjectProps> = ({ tableDs, customizeTable }) => {

  const [editing, setEditing] = useState(false);

  const columns: ColumnProps[] = [
    {
      name: 'layerReviewItemCode',
    },
    {
      name: 'projectName',
      editor: (record) => record.getState('editing') || editing,
    },
    {
      name: 'projectClassify',
      editor: (record) => record.getState('editing') || editing,
    },
    {
      name: 'projectFrom',
      editor: (record) => record.getState('editing') || editing,
    },
    {
      name: 'operationLov',
      width: 230,
      editor: (record) => record.getState('editing') || editing,
    },
    {
      name: 'levelFlays',
      width: 230,
      editor: (record) => record.getState('editing') || editing,
    },
    {
      name: 'layerReviewItemFrequency',
      editor: (record) => record.getState('editing') || editing,
    },
    {
      name: 'layerReviewItemStatus',
      editor: (record) => (record.getState('editing') || editing) && <Switch name='layerReviewItemStatus' record={record} />,
      renderer: ({ value }) =>
        value === 'Y' ?
          <Badge status="success" text={intl.get(`${modelPrompt}.table.trueBadge`).d('启用')} /> :
          <Badge status="error" text={intl.get(`${modelPrompt}.table.falseBadge`).d('禁用')} />,
    },
  ];


  const handleAdd = () => {
    const record = tableDs.create({}, 0);
    record.setState('editing', true);
  };

  const handleSubmit = async () => {
    if (tableDs.toJSONData().length > 0) {
      const flag = await tableDs.validate();
      if (flag) {
        axios.post(`${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-layer-review-item`, tableDs.toJSONData()).then(() => {
          tableDs.query();
          setEditing(false);
        }).catch(res => {
          if (res && res.failed) {
            notification.error({
              message: res?.message || intl.get(`${modelPrompt}.notification.error`).d('操作失败'),
              description: '',
            })
          }
        });
      }
    }
  };

  const tableButtons = [
    editing ?
      <>
        <Button onClick={handleSubmit} color={ButtonColor.primary}>
          {intl.get('hzero.common.button.save').d('保存')}
        </Button>
        <Button color={ButtonColor.primary} onClick={handleAdd} key="add" >
          {intl.get('hzero.common.button.add').d('新增')}
        </Button>

        <Button onClick={() => {
          tableDs.reset();
          setEditing(false);
        }}>
          {intl.get('hzero.common.button.cancel').d('取消')}
        </Button>
      </> :
      <Button color={ButtonColor.primary} onClick={() => setEditing(true)}>
        {intl.get('hzero.common.button.edit').d('编辑')}
      </Button>
    ,
  ] as Buttons[];

  return (
    // 替换产品类名
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.hierarchicalAudit`).d('分层审核项目')}>
        {tableButtons}
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.HIERARCFICAL.AIDIT.PROJECT.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.HIERARCFICAL.AIDIT.PROJECT.TABLE`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="HierarchicalAuditProject"
            customizedCode="HierarchicalAuditProject"
          />,
        )}

      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common','hzero.common'],
})(
  withProps(
    () => {
      const tableDs = listPageFactory();
      return {
        tableDs,
      };
    },
    { cacheState: true },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.HIERARCFICAL.AIDIT.PROJECT.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.HIERARCFICAL.AIDIT.PROJECT.TABLE`],
    })(HierarchicalAuditProject as any),
  ),
);