.container {
  height: 100%;
  padding: 0 12px;
}
.query-bar {
  position: fixed;
  top: 8px;
  right: 12px;
  width: 85px;
}
.statistic-container {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin: 50px 0 30px 0;
  .statistic-item {
    text-align: center;
    h1 {
      color: #333;
      font-size: 12px;
      font-family: PingFangSC-Regular, serif;
    }
    p {
      margin: 0;
      font-size: 24px;
      font-family: DINAlternate-Bold, sans-serif;
      &::after {
        display: block;
        width: 60px;
        height: 5.5px;
        border-radius: 2px;
        content: '';
      }
    }

    .approved {
      color: #ffb72e;
      &::after {
        background: rgba(255, 183, 46, 0.48);
        border: 1px solid rgba(255, 183, 46, 1);
      }
    }

    .completed {
      color: #54cebb;
      &::after {
        background: rgba(84, 206, 187, 0.24);
        border: 1px solid rgba(84, 206, 187, 1);
      }
    }

    .ongoing {
      color: #4283fe;
      &::after {
        background: rgba(66, 131, 254, 0.48);
        border: 1px solid rgba(66, 131, 254, 1);
      }
    }
  }
}

.chart-title {
  padding-left: 4px;
  color: #505a70;
  font-weight: 400;
  font-size: 14px;
  font-family: PingFangSC-Regular, sans-serif;
  line-height: 22px;
}

.customize-collapse {
  display: grid;
  grid-template-rows: auto 40px 1fr;
  height: 100%;
  :global(.c7n-collapse-header) {
    padding: 12px 0 4px 8px !important;
    &::before {
      top: calc(50% - 0.07rem + 4px) !important;
    }
  }
}
