/**
 * @Description: 库存日记账查询（重构） - 入口页DS
 * @Author: <EMAIL>
 * @Date: 2022/7/5 13:56
 * @LastEditTime: 2023-07-19 14:20:51
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.inventory.journalQuery.model.journalQuery';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): DataSetProps => ({
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'APEX_WMS.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'startTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
      max: 'endTime',
      required: true,
      defaultValue: '',
    },
    {
      name: 'endTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
      min: 'startTime',
      required: true,
      defaultValue: '',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialId`).d('物料'),
      lovCode: 'APEX_WMS.METHOD.MATERIAL.PERMISSION',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            enableFlag: 'Y',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      multiple: true,
    },
    {
      name: 'areaLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLocator`).d('仓库编码'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR_BY_ORG',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')],
            type: 'LOCATOR',
            locatorCategoryList: ['AREA'],
            topAreaFlag: 'Y',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'warehouseIds',
      bind: 'areaLocatorLov.locatorId',
    },
    {
      name: 'orgLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.orgId`).d('库位'),
      lovCode: 'APEX_WMS.MODEL.LOCATOR_BY_ORG',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')],
            type: 'LOCATOR',
            // queryLocatorCategoryList: ['AREA'],
            locatorCategoryList: ['INVENTORY', 'LOCATION'],
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'orgId',
      bind: 'orgLov.locatorId',
    },
    {
      name: 'lotCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次号'),
      multiple: true,
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
      textField: 'description',
      valueField: 'typeCode',
      options: new DataSet({
        autoQuery: true,
        dataKey: 'rows',
        paging: false,
        transport: {
          read: () => {
            return {
              url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
              method: 'GET',
              params: { tenantId },
              transformResponse: val => {
                const data = JSON.parse(val);
                data.rows.push({
                  description: intl.get(`tarzan.common.ownerType`).d('自有'),
                  typeCode: 'ALL',
                  typeGroup: 'OWNER_TYPE',
                });
                return {
                  ...data,
                };
              },
            };
          },
        },
      }),
    },
    {
      name: 'ownerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerId`).d('所有者查询'),
      lovCode: 'APEX_WMS.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'APEX_WMS.MODEL.CUSTOMER';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'APEX_WMS.MODEL.SUPPLIER';
            case 'OI':
              return `${BASIC.WMS_LOV_CODE_BEFORE}.MES.SO_LINE`;
            default:
              return 'APEX_WMS.MES.EMPTY';
          }
        },
        textField({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'customerCode';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'supplierCode';
            case 'OI':
              return 'soNumContent';
            default:
              return 'noData';
          }
        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerLov.customerId',
      dynamicProps: {
        bind({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'ownerLov.customerId';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'ownerLov.supplierId';
            case 'OI':
              return 'ownerLov.soLineId';
            default:
              return 'ownerLov.customerId';
          }
        },
      },
    },
    {
      name: 'eventTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eventTypeId`).d('事件类型'),
      ignore: FieldIgnore.always,
      lovCode: `${BASIC.WMS_LOV_CODE_BEFORE}.EVENT_TYPE`,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'eventTypeId',
      bind: 'eventTypeLov.eventTypeId',
    },
    {
      name: 'eventTypeCode',
      bind: 'eventTypeLov.eventTypeCode',
    },
    {
      name: 'eventIdList',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventIdList`).d('事件主键'),
      multiple: true,
    },
    {
      name: 'operationByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationBy`).d('操作人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'operationBy',
      bind: 'operationByLov.id',
    },
    {
      name: 'requestTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.requestTypeId`).d('事件请求类型'),
      ignore: FieldIgnore.always,
      lovCode: `${BASIC.WMS_LOV_CODE_BEFORE}.EVENT_REQUEST`,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'requestTypeId',
      bind: 'requestTypeLov.requestTypeId',
    },
    {
      name: 'requestIdList',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.requestIdList`).d('事件请求主键'),
      multiple: true,
    },
  ],
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('Bom号'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格'),
    },
    {
      name: 'brand',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.brand`).d('品牌'),
    },
    {
      name: 'eventTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTime`).d('库存变化时间'),
    },
    {
      name: 'changeQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.changeQuantity`).d('库存变化数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uom`).d('单位'),
    },
    {
      name: 'onhandQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.onhandQuantity`).d('库存变化后数量'),
    },
    {
      name: 'areaLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaLocator`).d('仓库编码'),
    },
    {
      name: 'areaLocatorDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaLocatorDesc`).d('仓库描述'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorDesc`).d('库位描述'),
    },
    {
      name: 'demandTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandTime`).d('计划退货日期'),
    },
    {
      name: 'lotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次号'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
    {
      name: 'ownerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerDesc`).d('所有者描述'),
    },
    {
      name: 'eventType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventType`).d('事件类型'),
    },
    {
      name: 'eventTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型描述'),
    },
    {
      name: 'eventRequestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventRequestTypeCode`).d('事件请求类型'),
    },
    {
      name: 'eventRequestTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventRequestTypeDesc`).d('事件请求类型描述'),
    },
    {
      name: 'eventRequestId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestIdList`).d('事件请求主键'),
    },
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件主键'),
    },
    {
      name: 'eventByUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventByUserName`).d('操作人'),
    },
    {
      name: 'costcenterDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.costcenterDesc`).d('成本中心描述'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'applyEquipment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyEquipment`).d('出库设备'),
    },
    {
      name: 'applyPerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyPerson`).d('出/入库人'),
    },
    {
      name: 'purpose',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.purpose`).d('领料用途'),
    },
    {
      name: 'toLocator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toLocator`).d('目标仓库'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inv-journal/property/list/ui`,
        method: 'POST',
        data: {
          ...data,
          orgType: data.orgId ? 'LOCATOR' : 'SITE',
          orgId: data.orgId ? data.orgId : data.siteId,
          lotCodes: data.lotCodes || undefined,
        },
      };
    },
  },
});

const detailDS = () => ({
  primaryKey: 'updateDate',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  autoQuery: false,
  autoCreate: false,
  selection: false,
  queryFields: [
    {
      name: 'invJournalId',
    },
  ],
  fields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'productionDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    },
    {
      name: 'uomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomQty`).d('主单位数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('主单位'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'lotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
    {
      name: 'ownerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerDesc`).d('所有者描述'),
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('指令单据编码'),
    },
    {
      name: 'instructionNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionNum`).d('指令编码'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
    },
    {
      name: 'topContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerCode`).d('顶层容器编码'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inv-journal/property/detail/ui`,
        method: 'GET',
      };
    },
  },
});

const documentDS = () => ({
  primaryKey: 'updateDate',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  autoQuery: false,
  autoCreate: false,
  selection: false,
  queryFields: [
    {
      name: 'invJournalId',
    },
  ],
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNumber`).d('单据编码'),
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
    },
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'businessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
    },
    {
      name: 'instructionStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionStatus`).d('指令状态'),
    },
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unit`).d('单位'),
    },
    {
      name: 'trxActualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trxActualQty`).d('变化数量'),
    },
    {
      name: 'fromSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromSiteCode`).d('来源站点'),
    },
    {
      name: 'toSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toSiteCode`).d('目标站点'),
    },
    {
      name: 'fromWarehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromWarehouseCode`).d('来源仓库'),
    },
    {
      name: 'fromLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromLocatorCode`).d('来源库位'),
    },
    {
      name: 'toWarehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toWarehouseCode`).d('目标仓库'),
    },
    {
      name: 'toLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toLocatorCode`).d('目标库位'),
    },
    {
      name: 'fromOwnerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromOwnerType`).d('来源所有者类型'),
    },
    {
      name: 'fromOwnerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromOwnerCode`).d('来源所有者编码'),
    },
    {
      name: 'toOwnerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toOwnerType`).d('目标所有者类型'),
    },
    {
      name: 'toOwnerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toOwnerCode`).d('目标所有者编码'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-inv-journal/property/doc/detail/ui`,
        method: 'GET',
      };
    },
  },
});

export { entranceDS, detailDS, documentDS };
