.wapper-header {
  padding: 0 16px;
  height: 48px;
  min-width: 300px;
  flex-grow: 0;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  line-height: 48px;
  border-bottom: 1px solid #e8e8e8;
  .wapper-header-left {
    display: flex;
    align-items: center;
    font-size: 16px;
    white-space: nowrap;
    .wapper-header-icon {
      margin-right: 10px;
      cursor: pointer;
      font-size: 22px;
    }
  }
  .wapper-header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .wapper-header-btn {
      margin-left: 8px;
    }
  }
}
.wapper-aside {
  padding: 0 16px;
  flex-grow: 1;
  min-width: 300px;
  overflow-y: auto;
  :global {
    .c7n-card.ued-detail-card > .c7n-card-body {
      padding-bottom: 10px;
    }
  }
}
.wapper-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  overflow-x: auto;
}
