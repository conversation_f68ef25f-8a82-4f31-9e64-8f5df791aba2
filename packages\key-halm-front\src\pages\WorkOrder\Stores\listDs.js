/**
 * 工作单-列表页Ds
 * @since 2020-09-18
 * <AUTHOR>
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import {FieldType} from "choerodon-ui/pro/lib/data-set/enum";

const organizationId = getCurrentOrganizationId();

const modelPrompt = 'amtc.workOrder.model.workOrder';
// 查询列表数据
const queryListUrl = `${HALM_MTC}/v1/${organizationId}/work-orders`;

// 列表页table
function tableDs() {
  return {
    autoQuery: true,
    primaryKey: 'woId',
    queryFields: [
      {
        name: 'assetLov',
        label: intl.get(`${modelPrompt}.asset`).d('设备'),
        type: 'object',
        lovCode: 'AAFM.ASSET_RECEIPT',
        dynamicProps: {
          lovPara: ({ record }) => {
            const maintSiteId = record?.get('maintSiteId');
            return {
              maintSiteId,
              organizationId,
              aclFlag: 1,
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'assetId',
        type: 'number',
        bind: 'assetLov.assetId',
      },
      {
        name: 'assetLocationLov',
        label: intl.get(`${modelPrompt}.assetLocation`).d('位置'),
        type: 'object',
        lovCode: 'AMDM.LOCATIONS',
        dynamicProps: {
          lovPara: ({ record }) => {
            const maintSiteId = record?.get('maintSiteId');
            return {
              maintSiteId,
              organizationId,
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'assetLocationId',
        type: 'number',
        bind: 'assetLocationLov.assetLocationId',
      },
      {
        name: 'woStatusList',
        label: intl.get(`${modelPrompt}.woStatus`).d('工单状态'),
        type: 'string',
        lookupCode: 'AMTC.WORKORDERSTATUS',
        multiple: true,
        defaultValue: ['APPROVED', 'INPRG'],
      },
      {
        name: 'priorityLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.priority`).d('优先级'),
        ignore: 'always',
        lovCode: 'AMTC.PRIORITIES',
      },
      {
        name: 'priorityId',
        type: 'number',
        bind: 'priorityLov.priorityId',
      },
      {
        name: 'woNameIgnore',
        label: intl.get(`${modelPrompt}.woNameIgnore`).d('工单概述'),
        type: 'string',
      },
      {
        name: 'woNum',
        label: intl.get(`${modelPrompt}.woNum`).d('工单编号'),
        type: 'string',
      },
      {
        name: 'maintSiteLov',
        label: intl.get(`${modelPrompt}.maintSite`).d('服务区域'),
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        lovPara: {
          tenantId: organizationId,
        },
        ignore: 'always',
      },
      {
        name: 'maintSiteId',
        type: 'number',
        bind: 'maintSiteLov.maintSiteId',
      },
      {
        name: 'woTypeLov',
        label: intl.get(`${modelPrompt}.woType`).d('工单类型'),
        type: 'object',
        lovCode: 'AMTC.WORKORDERTYPES',
        lovPara: {
          organizationId,
        },
        ignore: 'always',
      },
      {
        name: 'woTypeId',
        type: 'number',
        bind: 'woTypeLov.woTypeId',
      },
      {
        name: 'plannerGroupName',
        label: intl.get(`${modelPrompt}.plannerGroup`).d('计划员组'),
        type: 'string',
        ignore: 'always',
      },
      {
        name: 'plannerName',
        label: intl.get(`${modelPrompt}.planner`).d('计划员'),
        type: 'string',
        ignore: 'always',
      },
      {
        name: 'ownerGroupName',
        label: intl.get(`${modelPrompt}.ownerGroup`).d('负责人组'),
        type: 'string',
        ignore: 'always',
      },
      {
        name: 'ownerName',
        label: intl.get(`${modelPrompt}.owner`).d('负责人'),
        type: 'string',
        ignore: 'always',
      },
      {
        name: 'scheduledStartDate',
        label: intl.get(`${modelPrompt}.scheduledStartDate`).d('计划开始时间'),
        type: 'dateTime',
        range: ['scheduledStartDateFrom', 'scheduledStartDateTo'],
        ignore: 'always',
      },
      {
        name: 'scheduledStartDateFrom',
        type: 'dateTime',
        bind: 'scheduledStartDate.scheduledStartDateFrom',
      },
      {
        name: 'scheduledStartDateTo',
        type: 'dateTime',
        bind: 'scheduledStartDate.scheduledStartDateTo',
      },
      {
        name: 'reworkFlag',
        label: intl.get(`${modelPrompt}.reworkFlag`).d('存在返工'),
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'creationDateFrom',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.model.execute.creationDateFrom`).d('创建日期从'),
        max: 'creationDateTo',
      },
      {
        name: 'creationDateTo',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.model.execute.creationDateTo`).d('创建日期至'),
        min: 'creationDateFrom',
      },
      {
        name: 'actualStartDateFrom',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.model.execute.actualStartDateFrom`).d('实际开始时间从'),
        max: 'actualStartDateTo',
      },
      {
        name: 'actualStartDateTo',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.model.execute.actualStartDateTo`).d('实际开始时间至'),
        min: 'actualStartDateFrom',
      },
      {
        name: 'actualFinishDateFrom',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.model.execute.actualFinishDateFrom`).d('实际结束时间从'),
        max: 'actualFinishDateTo',
      },
      {
        name: 'actualFinishDateTo',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.model.execute.actualFinishDateTo`).d('实际结束时间至'),
        min: 'actualFinishDateFrom',
      },
    ],
    fields: [
      {
        name: 'woTypeIcon',
        label: intl.get(`${modelPrompt}.woTypeIcon`).d('图标'),
        type: 'string',
      },
      {
        name: 'woNum',
        label: intl.get(`${modelPrompt}.woNum`).d('工单编号'),
        type: 'string',
      },
      {
        name: 'woName',
        label: intl.get(`${modelPrompt}.woName`).d('工单概述'),
        type: 'string',
      },
      {
        name: 'woStatusMeaning',
        label: intl.get(`${modelPrompt}.woStatus`).d('工单状态'),
        type: 'string',
      },
      {
        name: 'descAndLabel', // 资产全称assetDesc 及 资产标签 visualLabel 拼接而成
        label: intl.get(`${modelPrompt}.asset`).d('设备'),
        type: 'string',
      },
      {
        name: 'assetLocationName',
        label: intl.get(`${modelPrompt}.assetLocation`).d('位置'),
        type: 'string',
      },
      {
        name: 'priorityName',
        type: 'string',
        label: intl.get(`${modelPrompt}.priority`).d('优先级'),
      },
      {
        name: 'woTypeName',
        label: intl.get(`${modelPrompt}.woType`).d('工单类型'),
        type: 'string',
      },
      {
        name: 'ownerName',
        label: intl.get(`${modelPrompt}.owner`).d('负责人'),
        type: 'string',
      },
      {
        name: 'plannerName',
        label: intl.get(`${modelPrompt}.planner`).d('计划员'),
        type: 'string',
      },
      {
        name: 'scheduledStartDate',
        label: intl.get(`${modelPrompt}.scheduledStartDate`).d('计划开始时间'),
        type: 'dateTime',
      },
      {
        name: 'scheduledFinishDate',
        label: intl.get(`${modelPrompt}.scheduledFinishDate`).d('计划完成时间'),
        type: 'dateTime',
      },
      {
        name: 'durationScheduled',
        label: intl.get(`${modelPrompt}.durationScheduled`).d('计划时长'),
        type: 'string',
      },
      {
        name: 'reworkFlag',
        label: intl.get(`${modelPrompt}.reworkFlag`).d('存在返工'),
        type: 'number',
        defaultValue: 0,
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'maintSiteName',
        label: intl.get(`${modelPrompt}.maintSite`).d('服务区域'),
        type: 'string',
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.model.execute.creationDate`).d('创建日期'),
      },
      {
        name: 'actualStartDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.model.execute.actualStartDate`).d('实际开始时间'),
      },
      {
        name: 'actualFinishDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.model.execute.actualFinishDate`).d('实际结束时间'),
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: queryListUrl,
          method: 'GET',
          params: {
            ...params,
            tenantId: organizationId,
          },
        };
      },
    },
    events: {
      load: ({ dataSet }) => {
        // 非拟定状态下不可选
        dataSet.records.forEach(i => {
          if (i.get('woStatus') !== 'DRAFT' && i.get('woStatus') !== 'APPROVED') {
            // eslint-disable-next-line no-param-reassign
            i.selectable = false;
          }
        });
        dataSet.modifyBtnsClickStatus();
      },
      select: ({ dataSet }) => {
        dataSet.modifyBtnsClickStatus();
      },
      unSelect: ({ dataSet }) => {
        dataSet.modifyBtnsClickStatus();
      },
      selectAll: ({ dataSet }) => {
        dataSet.modifyBtnsClickStatus();
      },
      unSelectAll: ({ dataSet }) => {
        dataSet.modifyBtnsClickStatus();
      },
    },
  };
}

export { tableDs };
