import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.purchase.outsourcingManage';
const tenantId = getCurrentOrganizationId();
// const _urlEnd = '-24175';
const _urlEnd = '';

const headDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}${_urlEnd}/v1/${tenantId}/mt-instruction/instruction/header/get/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.outSourceDocNum`).d('外协发料单号'),
    },
    {
      name: 'outSourcePoLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.outSourcePoLov`).d('外协采购订单'),
      lovCode: 'APEX_WMS.OUT_SOURCE_PO',
      textField: 'poUniqueCode',
      multiple: true,
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
      },
    },
    {
      name: 'poHeaderId',
      type: FieldType.number,
    },
    {
      name: 'poNumber',
      type: FieldType.string,
    },
    {
      name: 'siteId',
      type: FieldType.number,
    },
    {
      name: 'lineNums',
      type: FieldType.string,
      bind: 'outSourcePoLov.lineNum',
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.supplierLov`).d('供应商'),
    },
    {
      name: 'supplierId',
      type: FieldType.number,
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.supplierSiteLov`).d('供应商地点'),
    },
    {
      name: 'supplierSiteId',
      type: FieldType.number,
    },
    {
      name: 'demandTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.demandTime`).d('需求时间'),
    },
    {
      name: 'receivingAddress',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.receivingAddress`).d('发货地点'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.remark`).d('备注'),
    },
    {
      name: 'setQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.form.setNumber`).d('套数'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('poHeaderId');
        },
      },
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      lookupCode: 'SOURCE_SYSTEM',
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
});

const tableDS = (): DataSetProps => {
  return {
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    paging: false,
    selection: false,
    autoLocateFirst: false,
    forceValidate: true,
    transport: {
      read: () => {
        return {
          url: `${BASIC.HWMS_BASIC}${_urlEnd}/v1/${tenantId}/mt-instruction/instruction/limit/line/get/ui`,
          method: 'GET',
        };
      },
    },
    dataKey: 'rows',
    // totalKey: 'rows.totalElements',
    primaryKey: 'originLineNum',
    fields: [
      {
        name: 'lineNumber',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.lineNumber`).d('行号'),
      },
      {
        name: 'siteCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.siteLov`).d('站点'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.revisionCode`).d('物料版本'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.materialName`).d('物料描述'),
      },
      {
        name: 'componentQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.componentQty`).d('需求数量'),
      },
      {
        name: 'sumAvailableQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.moreSendQty`).d('超发数量'),
      },
      {
        name: 'haveSendQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.quantityExists`).d('已制单数量'),
      },
      {
        name: 'quantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.quantity`).d('制单数量'),
        required: true,
        // min: 0,
        dynamicProps: {
          max: ({ record }) => {
            const quantity = (record?.get('componentQty') || 0) - (record?.get('haveSendQty') || 0);
            return quantity > 0 ? quantity : 0;
          },
          min: ({ record }) => {
            return parseFloat(
              (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
            );
          },
          precision: ({ record }) => {
            return record?.get('decimalNumber');
          },
          step: ({ record }) => {
            return parseFloat(
              (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
            );
          },
        },
      },
      {
        name: 'decimalNumber',
        type: FieldType.number,
      },
      {
        name: 'usedOverDeliveryQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.usedOverDeliveryQty`).d('占用超发数量'),
        max: 'sumAvailableQty',
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.uomCode`).d('单位'),
      },
      {
        name: 'locatorLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.table.sendLocator`).d('发料仓库'),
        lovCode: 'APEX_WMS.MODEL.LOCATOR_BY_ORG',
        ignore: FieldIgnore.always,
        required: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              enableFlag: 'Y',
              locatorCategoryList: ['AREA'],
              businessTypes: 'OUTSOURCE',
              queryType: 'SOURCE',
              siteIds: [record?.get('siteId')].join(','),
            };
          },
          required: ({ record }) => {
            return record?.get('toLocatorRequiredFlag') === 'Y';
          },
        },
      },
      {
        name: 'locatorId',
        type: FieldType.number,
        bind: 'locatorLov.locatorId',
      },
      {
        name: 'locatorCode',
        type: FieldType.string,
        bind: 'locatorLov.locatorCode',
      },
      {
        name: 'onhandQuantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.sumAvailableQty`).d('库存现有量'),
      },
      {
        name: 'poNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.poNumber`).d('采购订单'),
      },
      {
        name: 'poLineNum',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.poLineNum`).d('采购订单行号'),
      },
      {
        name: 'toleranceFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.toleranceFlag`).d('允差标识'),
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'toleranceType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.toleranceType`).d('允差类型'),
        textField: 'description',
        valueField: 'typeCode',
        lovPara: { tenantId },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
        dynamicProps: {
          required: ({ record }) => {
            return record?.get('toleranceFlag') === 'Y';
          },
          disabled: ({ record }) => {
            return record?.get('toleranceFlag') === 'N';
          },
        },
      },
      {
        name: 'toleranceMaxValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.toleranceMaxValue`).d('上允差值'),
        min: 0,
        dynamicProps: {
          required: ({ record }) => {
            return (
              record?.get('toleranceFlag') === 'Y' &&
              (record?.get('toleranceType') === 'PERCENTAGE' ||
                record?.get('toleranceType') === 'NUMBER')
            );
          },
          max: ({ record }) => {
            if (record?.get('toleranceType') === 'PERCENTAGE') {
              return 100;
            }
          },
          disabled: ({ record }) => {
            return (
              record?.get('toleranceFlag') === 'N' ||
              (record?.get('toleranceType') !== 'PERCENTAGE' &&
                record?.get('toleranceType') !== 'NUMBER')
            );
          },
        },
      },
      {
        name: 'toleranceMinValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.toleranceMinValue`).d('下允差值'),
        min: 0,
        dynamicProps: {
          required: ({ record }) => {
            return (
              record?.get('toleranceFlag') === 'Y' &&
              (record?.get('toleranceType') === 'PERCENTAGE' ||
                record?.get('toleranceType') === 'NUMBER')
            );
          },
          disabled: ({ record }) => {
            return (
              record?.get('toleranceFlag') === 'N' ||
              (record?.get('toleranceType') !== 'PERCENTAGE' &&
                record?.get('toleranceType') !== 'NUMBER')
            );
          },
          max: ({ record }) => {
            if (record?.get('toleranceType') === 'PERCENTAGE') {
              return 100;
            }
          },
        },
      },
    ],
  };
};

export { headDS, tableDS };
