/**
 * @Description: 分层审核计划-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-08-16 11:31:24
 * @LastEditTime: 2023-08-16 11:31:24
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hierarchicalAuditPlan';
const tenantId = getCurrentOrganizationId();

// 详情-计划明细tab
const formDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'layerReviewPlanId',
    },
    {
      name: 'layerReviewPlanCode',
      label: intl.get(`${modelPrompt}.layerReviewPlanCode`).d('分层审核计划编码	'),
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
      ignore: FieldIgnore.always,
    },
    {
      type: FieldType.string,
      lookupCode: 'YP.QIS.LAYER_REVIEW_PLAN_STATUS',
      name: 'layerReviewPlanStatus',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      required: true,
      disabled: true,
    },
    {
      type: FieldType.month,
      label: intl.get(`${modelPrompt}.planDate`).d('月份'),
      name: 'planDate',
      format: 'YYYY-MM',
      required: true,
    },
  ],
});

// 详情-计划明细tab
const detailWeekTableDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'layerReviewGroupId',
    },
    {
      type: FieldType.object,
      name: 'leaderLov',
      lovCode: 'YP.QIS.EMPLOYEE',
      textField: 'name',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      type: FieldType.object,
      name: 'membersLov',
      lovCode: 'YP.QIS.EMPLOYEE',
      textField: 'name',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
  ],
});

const operationNumDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'num',
      label: intl.get(`${modelPrompt}.num`).d('本月预审核次数'),
    },
    {
      name: 'operationId',
    },
    {
      name: 'operationName',
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
  ],
});

const timeDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'beginDate',
      label: intl.get(`${modelPrompt}.beginDate`).d('开始时间'),
      type: FieldType.date,
      required: true,
      max: 'endDate',
    },
    {
      name: 'endDate',
      label: intl.get(`${modelPrompt}.endDate`).d('结束时间'),
      type: FieldType.date,
      required: true,
      min: 'beginDate',
    },
  ],
});

const tableItemDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
      lovCode: 'YP.QIS.LAYER_REVIEW_OPERATION',
      textField: 'description',
      valueField: 'operationId',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.description',
    },
    {
      name: 'taskCreationDate',
      label: intl.get(`${modelPrompt}.taskCreationDate`).d('任务创建时间'),
      type: FieldType.date,
      required: true,
    },
  ],
});
export { formDS, detailWeekTableDS, operationNumDS, timeDS, tableItemDS };
