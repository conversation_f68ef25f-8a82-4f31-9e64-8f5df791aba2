/**
 * @Description: 生产指令管理-修改装配清单和工艺路线的抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-10-18 19:22:30
 * @LastEditTime: 2021-11-01 17:38:35
 * @LastEditors: <<EMAIL>>
 */

import React, { useState } from 'react';
import { Form, Lov, SelectBox, TextField } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { C7nFormItemSort } from '@components/tarzan-ui';

const modelPrompt = 'tarzan.workshop.productionOrderMgt';

const { Option } = SelectBox;

export default ({ ds }) => {
  const [type, setType] = useState('designChange');

  const handleChange = value => {
    setType(value);
    ds.current.set('bom', {});
    ds.current.set('router', {});
  };

  const handleChangeProductionVersion = val => {
    if (val) {
      ds.current.set('bom', {
        bomId: val.bomId,
        bomName: val.bomName,
        revision: val.bomRevision,
      });
      ds.current.set('router', {
        routerId: val.routerId,
        routerName: val.routerName,
        revision: val.routerRevision,
      });
    } else {
      ds.current.set('bom', {});
      ds.current.set('router', {});
    }
  };

  return (
    <>
      <SelectBox
        name="selectType"
        dataSet={ds}
        mode="button"
        onChange={handleChange}
        style={{ margin: '6px 0px 14px 24px' }}
      >
        <Option value="designChange">{intl.get(`${modelPrompt}.designChange`).d('重读')}</Option>
        <Option value="textMaterial">{intl.get(`${modelPrompt}.textMaterial`).d('物料')}</Option>
      </SelectBox>
      <Form labelWidth={112} dataSet={ds} columns={2}>
        {type === 'designChange' ? (
          <>
            {ds.current.get('ownProductionVersion') && (
              <TextField name="designProductionVersionCode" />
            )}
            <C7nFormItemSort name="designBom" itemWidth={['80%', '20%']} newLine>
              <TextField name="designBomName" />
              <TextField name="designBomRevision" />
            </C7nFormItemSort>
            <C7nFormItemSort name="designRouter" itemWidth={['80%', '20%']}>
              <TextField name="designRouterName" />
              <TextField name="designRouterRevision" />
            </C7nFormItemSort>
          </>
        ) : (
          <>
            {ds.current.get('ownProductionVersion') && (
              <Lov name="productionVersion" onChange={handleChangeProductionVersion} />
            )}
            <C7nFormItemSort name="bom" itemWidth={['80%', '20%']} newLine>
              <Lov name="bom" />
              <TextField name="bomRevision" />
            </C7nFormItemSort>
            <C7nFormItemSort name="router" itemWidth={['80%', '20%']}>
              <Lov name="router" />
              <TextField name="routerRevision" />
            </C7nFormItemSort>
          </>
        )}
      </Form>
    </>
  );
};
