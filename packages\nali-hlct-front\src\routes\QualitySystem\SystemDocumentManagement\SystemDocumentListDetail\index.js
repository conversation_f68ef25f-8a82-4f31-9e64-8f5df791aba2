import React from 'react';
import { Bind } from 'lodash-decorators';
import { HZERO_FILE } from 'utils/config';
import { getResponse, getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import { DataSet, Form, Output, Button } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { queryFileList } from '@services/api';
import { openTab } from 'utils/menuTab';
import intl from 'utils/intl';
import { Content, Header } from 'components/Page';
import { formDS } from '../stores/SystemDocumentManagementDS';
import './index.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.qms.systemDocumentManagement';

const organizationId = getCurrentOrganizationId();
// @formatterCollections({
//   code: ['MT.APS.SOP.SOPTemplate', 'tarzan.aps.common', 'MT.APS.SOP.SOPTemplate'],
// })
export default class PendingMessageCard extends React.Component {
  constructor(props) {
    super(props);
    const formDs = new DataSet(formDS());
    this.state = {
      //   tableDs,
      formDs,
      loading: false,
      currentPreviewUuid: '',
      historyVersion: [],
      subFileList: [],
      relatedFileList: [],
      showFileModalProps: {
        visible: false,
        fileName: [],
        fileUrl: [],
      },
      previewInfoList: [],
    };
  }

  componentDidMount() {
    this.handleSearch();
  }

  @Bind
  handleBackPath() {
    this.props.history.push(`/hwms/system-document-management`);
  }

  @Bind
  handleSearch() {
    const { formDs } = this.state;
    const {
      match: { params },
    } = this.props;
    formDs.setQueryParameter('fileId', params?.id);
    formDs.query().then(() => {
      const { history, uuid, subFileList, relatedFileList } = formDs.current?.toData();
      subFileList?.forEach((item) => {
        item.fileUrl = `${HZERO_FILE}/v1/${organizationId}/file-preview/by-url?url=${encodeURIComponent(
          item.uuidFileUrl,
        )}&bucketName=qms&access_token=${encodeURIComponent(getAccessToken())}`
      })
      this.setState({
        historyVersion: history,
        subFileList: subFileList || [],
        relatedFileList: relatedFileList || [],
      });
      this.handlePreview(uuid);
    });
  }

  @Bind
  handlePreview(uid) {
    if (!uid) {
      return;
    }
    const params = {
      attachmentUUID: uid,
      bucketName: 'qms',
      bucketDirectory: 'system-document-management',
    };
    this.setState({
      loading: true,
      currentPreviewUuid: uid || '',
    });
    queryFileList(params).then(fileList => {
      if (getResponse(fileList)) {
        if (fileList.length > 0) {
          const token = getAccessToken();
          const url = [];
          for (let i = 0; i < fileList.length; i++) {
            url.push(
              `${HZERO_FILE}/v1/${organizationId}/file-preview/by-url?url=${encodeURIComponent(
                fileList[i].fileUrl,
              )}&bucketName=qms&access_token=${encodeURIComponent(token)}`,
            );
          }
          const fileNameList = fileList.map(item => item.fileName);
          this.setState({
            showFileModalProps: {
              visible: true,
              fileName: fileNameList,
              fileUrl: url,
            },
          });
          const map = [];
          const map1 = [];
          fileNameList.forEach(item => {
            map.push(Object.assign({}, { fileName: item }));
          });
          url.forEach(item => {
            map1.push(Object.assign({}, { fileUrl: item }));
          });
          map1.forEach((item, index) => {
            map[index].fileUrl = item.fileUrl;
          });
          this.setState({ previewInfoList: map });
        } else {
          this.setState({
            showFileModalProps: {
              visible: false,
              fileName: [],
              fileUrl: [],
            },
          });
        }
      }
    });
    this.setState({ loading: false });
  }

  @Bind
  getBucketName() {
    // return BKT_APS;
    return '434343';
  }

  @Bind
  historyRenderer() {
    const { historyVersion, loading, currentPreviewUuid } = this.state;
    if (historyVersion && historyVersion.length > 0) {
      return historyVersion.map((item, index) => (
        <span
          className={currentPreviewUuid === item.uuid ? 'link-active' : ''}
          style={{ padding: '5px' }}
        >
          <a disabled={loading} onClick={() => this.handlePreview(item.uuid)}>
            {item.version}
          </a>{' '}
          {index < (historyVersion.length || 0) - 1 && ','}
        </span>
      ));
    }
    return null;
  }

  @Bind
  subFileListRenderer() {
    const { subFileList } = this.state;
    if (subFileList && subFileList.length > 0) {
      return subFileList.map((item, index) => (
        <span style={{ padding: '5px' }}>
          {`${item.fileName} `}
          {index < (subFileList.length || 0) - 1 && ','}
        </span>
      ));
    }
    return null;
  }

  @Bind
  handleOpenDetailTab(fileItem) {
    openTab({
      key: `/hwms/system-document-management/detail/${fileItem?.fileId}`,
      title: intl.get(`${modelPrompt}.title.detail`).d('体系文件管理'),
    });
  }

  @Bind
  relatedFileListRenderer() {
    const { relatedFileList, loading } = this.state;
    if (relatedFileList && relatedFileList.length > 0) {
      return relatedFileList.map((item, index) => (
        <span style={{ padding: '5px' }} onClick={() => this.handleOpenDetailTab(item)}>
          <a disabled={loading} onClick={() => this.handleOpenDetailTab(item)}>
            {item.fileName}
          </a>{' '}
          {index < (relatedFileList.length || 0) - 1 && ','}
        </span>
      ));
    }
    return null;
  }

  render() {
    const { showFileModalProps, formDs, previewInfoList, subFileList } = this.state;
    const {
      match: { path },
    } = this.props;
    // pub 路由标识
    const pubFlag = path.startsWith('/pub');

    const handleFullTextSearch = () => {
      openTab({
        key: `/hwms/system-document-management/full-text-search`,
        title: intl.get(`${modelPrompt}.fullTextSearch`).d('体系文件管理-全文检索'),
      });
    };

    return (
      <div className="preview-wrap hmes-style">
        <Header title={intl.get(`${modelPrompt}.fullText`).d('体系文件管理')} backPath={pubFlag ? '' : '/hwms/system-document-management'}>
          {!pubFlag && (
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${path}.button.hightCheck`,
                  type: 'button',
                  meaning: '列表页-高级检索按钮',
                },
              ]}
              onClick={() => handleFullTextSearch()}
            >
              {intl.get(`${modelPrompt}.fullTextSearch`).d('全文检索')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['fileInfo']}>
            <Panel
              key="fileInfo"
              header={intl.get(`${modelPrompt}.title.fileInfo`).d('文件信息')}
            >
              <div className="form-body">
                <Form dataSet={formDs} labelWidth={112} columns={4}>
                  <Output name="fileCode" />
                  <Output name="editedDepartmentName" />
                  <Output name="editedDepartmentParentName" />
                  <Output name="editedByRealName" />
                  <Output name="editedDate" />
                  <Output name="publishDate" />
                  <Output name="version" />
                  <Output name="affliatedProcess" />
                  <Output name="fileLevel" />
                  <Output name="history" renderer={this.historyRenderer} />
                  <Output name="subFileList" renderer={this.subFileListRenderer} />
                  <Output name="relatedFileList" renderer={this.relatedFileListRenderer} />
                  <Output name="abandonReason" />
                </Form>
              </div>
            </Panel>
          </Collapse>
          <div
            style={{
              display: showFileModalProps.visible ? 'block' : 'none',
              height: pubFlag ? '73%' : '70%',
            }}
          >
            {!pubFlag && (
              <Button
                color="primary"
                onClick={this.handleBackPath}
                style={{ float: 'right', margin: '2px' }}
              >
                {intl.get('hzero.common.button.back').d('返回')}
              </Button>
            )}
            {/* </div> */}
            {previewInfoList &&
              previewInfoList.length > 0 &&
              previewInfoList.map(item => (
                <>
                  <a
                    onClick={() => {
                      window.open(item.fileUrl);
                    }}
                    className="preview-header-title"
                  >
                    {formDs.current?.get('fileCode')} {formDs.current?.get('version')}:{' '}
                    {item.fileName}
                  </a>
                  <iframe
                    className="preview-inner-iframe"
                    src={item.fileUrl}
                    title={`${formDs.current?.get('fileCode')} ${formDs.current?.get('version')}: ${
                      item.fileName
                    }`}
                  />
                </>
              ))}
            {subFileList?.length > 0 &&
              subFileList.map(item => (
                <>
                  <a
                    onClick={() => {
                      window.open(item.fileUrl);
                    }}
                    className="preview-header-title"
                  >
                    {item.fileCode} {item.version}: {item.uuidFileName}
                  </a>
                  <iframe
                    className="preview-inner-iframe"
                    src={item.fileUrl}
                    title={`${item.fileCode} ${item.version}: ${item.uuidFileName}`}
                  />
                </>
              ))}
          </div>
        </Content>
      </div>
    );
  }
}
