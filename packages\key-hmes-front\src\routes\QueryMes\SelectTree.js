/**
 * 暂时未使用此文件
 * ListTable - 层级树
 * @date: 2019-8-6
 * @author: jrq <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2019, Hand
 */
import React from 'react';
import { connect } from 'dva';
import { Tree, Spin } from 'hzero-ui';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.inventory.query.mes.model.query';

const { TreeNode } = Tree;

@connect(({ query, loading }) => ({
  query,
  fetchLoading: loading.effects[('query/asynQueryTree', 'query/asynQueryTreeChildren')],
}))
export default class SelectTree extends React.Component {
  /**
   * 渲染树的子节点
   */
  renderTreeItems = nodes => {
    return nodes.map(item => {
      if (item.childrens) {
        return (
          <TreeNode
            title={this.renderTitle(item)}
            key={`${item.id}-${item.type}-${item.parentId}-${item.parentType}`}
            id={item.id}
            type={item.type}
            parentId={item.parentId}
            parentType={item.parentType}
            dataRef={item}
          >
            {this.renderTreeItems(item.childrens)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          title={this.renderTitle(item)}
          key={`${item.id}-${item.type}-${item.parentId}-${item.parentType}`}
          id={item.id}
          type={item.type}
          parentId={item.parentId}
          parentType={item.parentType}
          dataRef={item}
          isLeaf={!item.children}
        />
      );

    });
  };

  // 渲染树节点名称
  renderTitle = obj => {
    let title = '';
    if (!obj || !obj.type) {
      return;
    }
    this.props.query.orgTypeList.forEach(item => {
      if (item.typeCode === obj.type) {
        title = `${item.description}-${obj.code}`;
      }
    });
    const returnValue = title || `${intl.get(`${modelPrompt}.siteCode`).d('站点')}-${obj.code}`;
    return returnValue;
  };

  // 选中树节点
  onCheck = (checkedKeys, { checkedNodes }) => {
    const {
      dispatch,
      query: { queryCriteria },
    } = this.props;
    const list = [];
    checkedNodes.forEach(item => {
      list.push({
        orgId: item.props.id,
        orgType: item.props.type,
        children: !item.props.isLeaf,
      });
    });
    dispatch({
      type: 'query/updateState',
      payload: {
        checkedKeys,
        checkedNodesInfoList: list,
      },
    });
    dispatch({
      type: 'query/queryBillList',
      payload: {
        ...queryCriteria,
        orgList: list,
      },
    });
  };

  // 异步加载树
  onLoadData = treeNode => {
    const {
      dispatch,
      query: { queryCriteria = {}, checkedNodesInfoList = [], checkedKeys = [] },
    } = this.props;
    return new Promise(resolve => {
      dispatch({
        type: 'query/asynQueryTreeChildren',
        payload: {
          isOnhand: 'N',
          parentOrganizationId: treeNode.props.id,
          parentOrganizationType: treeNode.props.type,
          topSiteId: treeNode.props.dataRef.topSiteId,
        },
      }).then(res => {
        if (res) {
          const selectedTreeNodes = [];
          const keys = [];
          res.rows.forEach(item => {
            selectedTreeNodes.push({
              orgId: item.id,
              orgType: item.type,
              children: item.children,
            });
            keys.push(`${item.id}-${item.type}-${item.parentId}-${item.parentType}`);
          });
          setTimeout(() => {
            /* eslint-disable */
            treeNode.props.dataRef.childrens = res.rows;
            /* eslint-enable */
            this.props.dispatch({
              type: 'query/updateState',
              payload: {
                treeJSON: [...this.props.query.treeJSON],
                loadedKeysArray: [...this.props.query.loadedKeysArray, treeNode.props.eventKey],
                checkedKeys: [...checkedKeys, ...keys],
                checkedNodesInfoList: [...checkedNodesInfoList, ...selectedTreeNodes],
              },
            });
            dispatch({
              type: 'query/queryBillList',
              payload: {
                ...queryCriteria,
                orgList: [...checkedNodesInfoList, ...selectedTreeNodes],
              },
            });
            resolve();
          }, 500);
        }
      });
    });
  };

  //  记录展开树节点
  onExpandNode = expandedKeys => {
    this.props.dispatch({
      type: 'query/updateState',
      payload: {
        expandedKeysArray: expandedKeys,
      },
    });
  };

  /**
   * 渲染方法
   * @returns
   */
  render() {
    const {
      query: { treeJSON = [], checkedKeys = [], loadedKeysArray = [], expandedKeysArray = [] },
      fetchLoading,
    } = this.props;
    return (
      <Spin spinning={fetchLoading || false}>
        <Tree
          checkable
          showLine
          loadData={this.onLoadData}
          loadedKeys={loadedKeysArray}
          onExpand={this.onExpandNode}
          expandedKeys={expandedKeysArray}
          onCheck={this.onCheck}
          checkedKeys={checkedKeys}
        >
          {this.renderTreeItems(treeJSON)}
        </Tree>
      </Spin>
    );
  }
}
