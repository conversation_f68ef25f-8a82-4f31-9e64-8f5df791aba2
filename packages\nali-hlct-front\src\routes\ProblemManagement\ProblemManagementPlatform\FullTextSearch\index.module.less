.full-text-search-page {
  .full-text-search-table {
    :global(>:not(:first-child)) {
      display: none;
    }
  }
  .back-top {
    position: fixed;
    z-index: 9;
    img {
      position: fixed;
      border-radius: 100%;
      color: #fff;
      bottom: 65px;
      right: 60px;
      width: 60px;
      height: 60px;
    }
  }
}

.full-text-search-container {
  border: solid 1px rgb(225, 225, 225);
  font-size: 16px;
  color: #403a3a;
  background-color: rgb(242, 242, 242);
  padding: 5px 10px;

  .search-total-count {
    line-height: 32px;
    .total-count-num {
      color: rgb(245, 43, 43);
    }
  }
  
  .search-result-container {
    border: solid 1px rgb(181, 181, 181);
    background-color: #fff;
    display: flex;
    padding: 10px;

    .full-text-search-container-left {
      width: 85%;
      border-right: solid 1px rgb(188, 188, 188);

      .search-result {
        .search-result-item {
          padding: 5px 10px;

          .problem-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 5px 0;
            font-weight: 600;

            .problem-title {
              font-size: 16px;
              margin-right: 15px;
              cursor: pointer;

              em {
                color: red;
                font-style: normal;
              }
            }
          }

          .render-component {
            padding: 0 10px;
            font-size: 14px;
            .render-component-title {
            }

            .render-component-item {
              padding: 5px 10px;
              .render-component-item-title {

              }

              .render-component-item-content,
              .render-component-item-attachemnt {
                .render-component-item-content-label {
                  font-weight: 500;
                  width: 25%;
                }
                .render-component-item-content-right {
                  width: 75%;
                  em {
                    color: red;
                    font-style: normal;
                  }
                }
              }

              .render-component-item-content {
                display: inline-block;
                width: 33%;
              }

              .render-component-item-attachemnt {
                .render-component-item-file-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-end;

                  .render-component-item-file-header-name {
                    text-decoration: underline;
                    margin-right: 15px;
                    cursor: pointer;

                    em {
                      color: red;
                      font-style: normal;
                    }
                  }
                  .render-component-item-read-source-doc {
                    color: rgb(105, 190, 193);
                    flex-grow: 1;
                    font-size: 13px;
                    cursor: pointer;
                  }
                }
                .render-component-item-file-content {
                  font-size: 14px;
                  text-indent: 28px;
                  padding: 5px 10px;
                  cursor: pointer;
                  word-wrap: break-word;

                  em {
                    color: red;
                    font-style: normal;
                  }
                }
              }
            }
          }

          .file-content {
            font-size: 14px;
            text-indent: 28px;
            padding: 5px 0;
            cursor: pointer;

            em {
              color: red;
              font-style: normal;
            }
          }

          .problem-footer {
            display: flex;
            flex-wrap: wrap;
            font-size: 12px;
            padding: 5px 0;

            .problem-footer-item {
              width: 20%;
              .problem-footer-item-label {
                display: inline-block;
                width: 25%;
              }
            }
          }
        }

        .search-result-item:not(:last-child) {
          border-bottom: solid 1px rgb(188, 188, 188);
        }
      }

      .loading-text {
        display: none;
        font-size: 12px;
        text-align: center;
      }
    }
    .full-text-search-container-right {
      width: 15%;
      
      .publish-time-title {
        font-size: 13px;
        color: #595959;
        font-weight: bold;
      }
    }
  }
}
