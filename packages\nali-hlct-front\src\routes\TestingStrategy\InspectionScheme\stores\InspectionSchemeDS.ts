/**
 * @Description: 检验方案维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-06-15 14:16:55
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import uuid from 'uuid/v4';

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.inspectionScheme';
const tenantId = getCurrentOrganizationId();
const endUrl = '';


// 列表-ds
const listTableDS = (): DataSetProps => ({
  forceValidate: true,
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  primaryKey: 'inspectSchemeId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  modifiedCheck: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-inspect-scheme/page/ui`,
        method: 'post',
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
      name: 'inspectSchemeCode',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      bind: 'siteObject.siteId',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectSchemeObjectType`).d('检验对象类型'),
      name: 'inspectSchemeObjectTypeObject',
      lookupCode: 'MT.QMS.INSPECT_SCHEME_OBJECT_TYPE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'inspectSchemeObjectType',
      bind: 'inspectSchemeObjectTypeObject.value',
    },
    {
      name: 'inspectSchemeObjectTypeDesc',
      bind: 'inspectSchemeObjectTypeObject.meaning',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectSchemeObject`).d('检验对象'),
      name: 'inspectSchemeObject',
      ignore: FieldIgnore.always,
      computedProps: {
        lovCode: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'MT.METHOD.MATERIAL_CATEGORY';
            case 'MATERIAL':
              return 'MT.MATERIAL';
            case 'WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROCESS_WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROD_LINE':
              return 'MT.MODEL.PRODLINE';
            case 'AREA':
              return 'MT.MODEL.AREA';
            case 'OPERATION':
              return 'MT.METHOD.ROUTER_OPERATION';
            default:
              return 'MT.MATERIAL';
          }
        },
        lovPara: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            case 'MATERIAL':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            case 'WORKCELL':
              return {
                tenantId,
                siteId: record?.get('siteId'),
                workcellType: 'STATION',
              };
            case 'PROCESS_WORKCELL':
              return {
                tenantId,
                siteId: record?.get('siteId'),
                workcellType: 'PROCESS',
              };
            case 'PROD_LINE':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            case 'AREA':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            case 'OPERATION':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            default:
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
          }
        },
        disabled: ({ record }) => {
          return !record?.get('inspectSchemeObjectType') || !record?.get('siteId');
        },
      },
    },
    {
      name: 'inspectSchemeObjectId',
      computedProps: {
        bind: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'inspectSchemeObject.materialCategoryId';
            case 'MATERIAL':
              return 'inspectSchemeObject.materialId';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellId';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellId';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineId';
            case 'AREA':
              return 'inspectSchemeObject.areaId';
            case 'OPERATION':
              return 'inspectSchemeObject.operationId';
            default:
              return 'inspectSchemeObject.materialId';
          }
        },
      },
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      // lovPara: {
      //   tenantId,
      // },
      multiple: true,
      computedProps: {
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
          };
        },
      },
    },
    {
      name: 'inspectBusinessTypes',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.process`).d('工序'),
      name: 'processObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.WORKCELL',
      multiple: true,
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
            workcellType: 'PROCESS',
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
      },
    },
    {
      name: 'processWorkcellIds',
      bind: 'processObject.workcellId',
    },
    {
      name: 'lastUpdatedByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
      lovCode: 'HIAM.USER.ORG',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'lastUpdatedById',
      type: FieldType.string,
      bind: 'lastUpdatedByLov.id',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.supplier`).d('供应商'),
      name: 'supplierObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'supplierIds',
      bind: 'supplierObject.supplierId',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      name: 'revisionCode',
      computedProps: {
        disabled: ({ record }) => {
          return record?.get('inspectSchemeObjectType') !== 'MATERIAL';
        },
      },
    },
    {
      type: FieldType.string,
      name: 'manufactureFlag',
      label: intl?.get(`${modelPrompt}.manufactureFlag`).d('制造/采购标识'),
      lookupCode: 'MT.METHOD.MAKE_BUY_CODE1',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectSchemeTmplt`).d('检验方案模板'),
      name: 'inspectSchemeTmpltObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_SCHEME_TMP',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'inspectSchemeTmpIds',
      bind: 'inspectSchemeTmpltObject.inspectSchemeTmpId',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.customer`).d('客户'),
      name: 'customerObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'customerIds',
      bind: 'customerObject.customerId',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.area`).d('区域'),
      name: 'areaObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.AREA',
      multiple: true,
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
      },
    },
    {
      name: 'areaIds',
      bind: 'areaObject.areaId',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      multiple: true,
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
      },
    },
    {
      name: 'prodLineIds',
      bind: 'prodLineObject.prodLineId',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.station`).d('工位'),
      name: 'stationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.WORKCELL',
      multiple: true,
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
            workcellType: 'STATION',
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
      },
    },
    {
      name: 'stationWorkcellIds',
      bind: 'stationObject.workcellId',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.operation`).d('工艺'),
      name: 'operationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'operationIds',
      bind: 'operationObject.operationId',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.documentNum`).d('文件号'),
      name: 'documentNum',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.documentRevision`).d('文件版本号'),
      name: 'documentRevision',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.controlledNum`).d('受控号'),
      name: 'controlledNum',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      name: 'currentFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.inspectSchemeStatus`).d('发布状态'),
      name: 'status',
      lookupUrl: `${BASIC.TARZAN_COMMON}${endUrl}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSPECT_SCHEME_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.schemeCategory`).d('检验方案类别'),
      name: 'schemeCategory',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.SCHEME_CATEGORY',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      name: 'inspectSchemeCode',
      label: intl?.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
    },
    {
      type: FieldType.string,
      name: 'siteName',
      label: intl?.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.inspectSchemeObjectType`).d('检验对象类型'),
      name: 'inspectSchemeObjectTypeDesc',
    },
    {
      type: FieldType.string,
      name: 'inspectSchemeObjectName',
      label: intl?.get(`${modelPrompt}.inspectSchemeObject`).d('检验对象'),
    },
    {
      type: FieldType.string,
      name: 'inspectSchemeObjectCode',
      label: intl?.get(`${modelPrompt}.inspectSchemeObjectCode`).d('检验对象编码'),
    },
    {
      type: FieldType.string,
      name: 'revisionCode',
      label: intl?.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      type: FieldType.object,
      name: 'manufactureFlag',
      label: intl?.get(`${modelPrompt}.manufactureFlag`).d('制造/采购标识'),
      lookupCode: 'MT.METHOD.MAKE_BUY_CODE1',
      lovPara: {
        tenantId,
      },
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectSchemeTmplt`).d('检验方案模板'),
      name: 'inspectSchemeTmpCode',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeDesc',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.inspectSchemeStatus`).d('发布状态'),
      name: 'statusDesc',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.documentNum`).d('文件号'),
      name: 'documentNum',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.documentRevision`).d('文件版本号'),
      name: 'documentRevision',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.controlledNum`).d('受控号'),
      name: 'controlledNum',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      name: 'currentFlag',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.remark`).d('备注'),
      name: 'remark',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.schemeCategory`).d('检验方案类别'),
      name: 'schemeCategory',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.SCHEME_CATEGORY',
    },
  ],
  record: {
    dynamicProps: {
      selectable: record =>
        record?.get('status') === 'UNPUBLISHED' && record?.get('enableFlag') === 'Y',
    },
  },
});

// 详情-检验方案信息ds
const detailFormDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.string,
      name: 'inspectSchemeCode',
      label: intl?.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案编码'),
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.site`).d('站点'),
      name: 'siteObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        siteType: 'MANUFACTURING',
      },
      required: true,
      computedProps: {
        disabled: ({ record }) => {
          return record?.get('inspectSchemeId');
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteObject.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteObject.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteObject.siteName',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      name: 'currentFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectSchemeObjectType`).d('检验对象类型'),
      name: 'inspectSchemeObjectTypeObject',
      lookupCode: 'MT.QMS.INSPECT_SCHEME_OBJECT_TYPE',
      lovPara: {
        tenantId,
      },
      required: true,
      computedProps: {
        disabled: ({ record }) => {
          return record?.get('inspectSchemeId');
        },
      },
    },
    {
      name: 'inspectSchemeObjectType',
      bind: 'inspectSchemeObjectTypeObject.value',
    },
    {
      name: 'inspectSchemeObjectTypeDesc',
      bind: 'inspectSchemeObjectTypeObject.meaning',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectSchemeObject`).d('检验对象'),
      name: 'inspectSchemeObject',
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        lovCode: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'MT.METHOD.MATERIAL_CATEGORY';
            case 'MATERIAL':
              return 'MT.MATERIAL';
            case 'WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROCESS_WORKCELL':
              return 'MT.MODEL.WORKCELL';
            case 'PROD_LINE':
              return 'MT.MODEL.PRODLINE';
            case 'AREA':
              return 'MT.MODEL.AREA';
            case 'OPERATION':
              return 'MT.METHOD.ROUTER_OPERATION';
            default:
              return 'MT.MATERIAL';
          }
        },
        lovPara: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            case 'MATERIAL':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            case 'WORKCELL':
              return {
                tenantId,
                siteId: record?.get('siteId'),
                workcellType: 'STATION',
              };
            case 'PROCESS_WORKCELL':
              return {
                tenantId,
                siteId: record?.get('siteId'),
                workcellType: 'PROCESS',
              };
            case 'PROD_LINE':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            case 'AREA':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            case 'OPERATION':
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            default:
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
          }
        },
        textField: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'description';
            case 'MATERIAL':
              return 'materialName';
            case 'WORKCELL':
              return 'workcellName';
            case 'PROCESS_WORKCELL':
              return 'workcellName';
            case 'PROD_LINE':
              return 'prodLineName';
            case 'AREA':
              return 'areaName';
            case 'OPERATION':
              return 'description';
            default:
              return 'materialName';
          }
        },
        valueField: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'materialCategoryId';
            case 'MATERIAL':
              return 'materialId';
            case 'WORKCELL':
              return 'workcellId';
            case 'PROCESS_WORKCELL':
              return 'workcellId';
            case 'PROD_LINE':
              return 'prodLineId';
            case 'AREA':
              return 'areaId';
            case 'OPERATION':
              return 'operationId';
            default:
              return 'materialId';
          }
        },
        disabled: ({ record }) => {
          return (
            !record?.get('inspectSchemeObjectType') ||
            !record?.get('siteId') ||
            record?.get('inspectSchemeId')
          );
        },
      },
    },
    {
      name: 'inspectSchemeObjectId',
      computedProps: {
        bind: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'inspectSchemeObject.materialCategoryId';
            case 'MATERIAL':
              return 'inspectSchemeObject.materialId';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellId';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellId';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineId';
            case 'AREA':
              return 'inspectSchemeObject.areaId';
            case 'OPERATION':
              return 'inspectSchemeObject.operationId';
            default:
              return 'inspectSchemeObject.materialId';
          }
        },
      },
    },
    {
      name: 'inspectSchemeObjectCode',
      label: intl?.get(`${modelPrompt}.inspectSchemeObjectCode`).d('检验对象编码'),
      disabled: true,
      computedProps: {
        bind: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'inspectSchemeObject.categoryCode';
            case 'MATERIAL':
              return 'inspectSchemeObject.materialCode';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellCode';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellCode';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineCode';
            case 'AREA':
              return 'inspectSchemeObject.areaCode';
            case 'OPERATION':
              return 'inspectSchemeObject.operationName';
            default:
              return 'inspectSchemeObject.materialCode';
          }
        },
      },
    },
    {
      name: 'inspectSchemeObjectName',
      label: intl?.get(`${modelPrompt}.inspectSchemeObjectName`).d('检验对象描述'),
      computedProps: {
        bind: ({ record }) => {
          switch (record?.get('inspectSchemeObjectType')) {
            case 'MATERIAL_CATEGORY':
              return 'inspectSchemeObject.description';
            case 'MATERIAL':
              return 'inspectSchemeObject.materialName';
            case 'WORKCELL':
              return 'inspectSchemeObject.workcellName';
            case 'PROCESS_WORKCELL':
              return 'inspectSchemeObject.workcellName';
            case 'PROD_LINE':
              return 'inspectSchemeObject.prodLineName';
            case 'AREA':
              return 'inspectSchemeObject.areaName';
            case 'OPERATION':
              return 'inspectSchemeObject.description';
            default:
              return 'inspectSchemeObject.materialName';
          }
        },
      },
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'inspectSchemeObject.revisionFlag',
    },
    {
      // 物料版本
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      name: 'revisionCode',
      textField: 'description',
      valueField: 'description',
      // @ts-ignore
      noCache: true,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: item?.kid ? item.kid : uuid(),
                  description: item?.description ? item?.description : item,
                };
              });
            }
            if (record && firstlyQueryData.length === 1 && !record?.get('revisionCode')) {
              record?.set('revisionCode', firstlyQueryData[0].description);
            }
            return firstlyQueryData;
          },
        };
      },
      computedProps: {
        lookupUrl: ({ record }) => {
          if (
            !(record?.get('inspectSchemeObjectType') === 'MATERIAL') ||
            !record?.get('inspectSchemeObjectId') ||
            !record?.get('siteId') ||
            !(record?.get('revisionFlag') === 'Y')
          ) {
            return;
          }
          return `${BASIC.TARZAN_METHOD
            }${endUrl}/v1/${tenantId}/mt-material/site-material/limit/lov/ui?materialId=${record?.get(
              'inspectSchemeObjectId',
            )}&siteIds=${record?.get('siteId')}`;
        },
        disabled: ({ record }) => {
          return (
            !(record?.get('inspectSchemeObjectType') === 'MATERIAL') ||
            !record?.get('inspectSchemeObjectId') ||
            !record?.get('siteId') ||
            !(record?.get('revisionFlag') === 'Y') ||
            record?.get('inspectSchemeId')
          );
        },
        required: ({ record }) => {
          return (
            record?.get('inspectSchemeObjectType') === 'MATERIAL' &&
            record?.get('inspectSchemeObjectId') &&
            record?.get('siteId') &&
            record?.get('revisionFlag') === 'Y'
          );
        },
      },
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.documentNum`).d('文件号'),
      name: 'documentNum',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.documentRevision`).d('文件版本号'),
      name: 'documentRevision',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.controlledNum`).d('受控号'),
      name: 'controlledNum',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.inspectSchemeStatus`).d('发布状态'),
      name: 'status',
      lookupUrl: `${BASIC.TARZAN_COMMON}${endUrl}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSPECT_SCHEME_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      disabled: true,
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      name: 'enableFlag',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl?.get(`${modelPrompt}.enclosure`).d('附件'),
      // max: 9,
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.remark`).d('备注'),
      name: 'remark',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.schemeCategory`).d('检验方案类别'),
      name: 'schemeCategory',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.SCHEME_CATEGORY',
    },
  ],
});

// 详情-检验项目tab的基础信息ds
const inspectionItemBasisDS = (): DataSetProps => ({
  forceValidate: true,
  autoCreate: true,
  autoLocateFirst: true,
  fields: [
    { name: 'siteId' },
    { name: 'inspectBusinessTypeObjectIgnore', type: FieldType.string },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      lovCode: 'MT.QMS.PERMISSION.INSPECT_BUS_TYPE_RULE_EDIT',
      required: true,
      computedProps: {
        lovPara: ({ record }) => {
          let inspectBusinessTypes = record?.get('inspectBusinessTypeObjectIgnore') || null;

          if (inspectBusinessTypes && inspectBusinessTypes !== '') {
            inspectBusinessTypes = (record?.get('inspectBusinessTypeObjectIgnore') || '').split(',');
          }
          return {
            tenantId,
            notInInspectBusinessTypes: inspectBusinessTypes,
            siteId: record?.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
      },
    },
    {
      name: 'inspectBusinessType',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'inspectBusinessTypeDesc',
      bind: 'inspectBusinessTypeObject.inspectBusinessTypeDesc',
    },
    {
      name: 'inspectBusinessTypeRuleId',
      bind: 'inspectBusinessTypeObject.inspectBusinessTypeRuleId',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.samplingDimension`).d('抽样维度'),
      name: 'samplingDimension',
      lookupCode: 'MT.QMS.SAMPLING_DIMENSION',
      defaultValue: 'INSPECT_ITEM_SAMPLING',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.samplingMethod`).d('抽样方式'),
      name: 'samplingMethodLov',
      lovCode: 'MT.QMS.SAMPLING_METHOD',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      textField: 'samplingMethodDesc',
      computedProps: {
        required: ({ record }) => {
          return record?.get('samplingDimension') === 'ALL_SAMPLING';
        },
      },
    },
    {
      name: 'samplingMethodId',
      bind: 'samplingMethodLov.samplingMethodId',
    },
    {
      name: 'samplingMethodDesc',
      type: FieldType.string,
      bind: 'samplingMethodLov.samplingMethodDesc',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectionDimension`).d('检验维度'),
      name: 'inspectionDimensionObject',
      multiple: true,
      computedProps: {
        disabled: ({ record }) => {
          return !record?.get('inspectSchemeObjectType') || !record?.get('inspectSchemeObjectId');
        },
      },
    },
    {
      name: 'inspectionDimension',
      bind: 'inspectionDimensionObject.value',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectionItem`).d('检验项目'),
      name: 'inspectionItemObject',
      lovCode: 'MT.QMS.INSPECT_ITEM_INFO',
      multiple: true,
      lovPara: { tenantId, dataType: 'CALCULATE_FORMULA' },
      // @ts-ignore
      noCache: true,
      computedProps: {
        disabled: ({ record }) => {
          return record?.get('inspectionDimensionObject').length === 0;
        },
      },
    },
  ],
});

// 详情-新增检验维度的DS
const dimensionTableDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.object,
      name: 'ruleDtl',
    },
    {
      type: FieldType.string,
      name: 'disabledListWork',
    },
    {
      type: FieldType.string,
      name: 'disabledListMapRelationship',
    },
    {
      type: FieldType.number,
      label: intl?.get(`${modelPrompt}.sequence`).d('序号'),
      name: 'sequence',
    },

    // 从formDs传入的定值
    {
      name: 'siteId',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.materialCode`).d('物料编码'),
      name: 'materialCode',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.materialName`).d('物料描述'),
      name: 'materialName',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      name: 'materialId',
    },
    // 从formDs传入的定值
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      name: 'revisionCode',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.categoryCode`).d('物料类别'),
      name: 'categoryCode',
    },
    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.materialCategoryName`).d('物料类别描述'),
      name: 'materialCategoryName',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.area`).d('区域'),
      name: 'areaObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.AREA',
      textField: 'areaName',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return (
            !record?.get('siteId') ||
            (record?.get('disabledListWork') || '').split(',').indexOf('areaObject') > -1 ||
            (record?.get('ruleDtl') || {}).areaFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'areaId',
      bind: 'areaObject.areaId',
    },
    {
      name: 'areaCode',
      bind: 'areaObject.areaCode',
    },
    {
      name: 'areaName',
      bind: 'areaObject.areaName',
    },

    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.prodLine`).d('产线'),
      name: 'prodLineObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineName',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return (
            !record?.get('siteId') ||
            (record?.get('disabledListWork') || '').split(',').indexOf('prodLineObject') > -1 ||
            (record?.get('ruleDtl') || {}).prodLineFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineObject.prodLineId',
    },
    {
      name: 'prodLineCode',
      bind: 'prodLineObject.prodLineCode',
    },
    {
      name: 'prodLineName',
      bind: 'prodLineObject.prodLineName',
    },

    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.process`).d('工序'),
      name: 'processObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.ROUTER_WORKCELL',
      textField: 'workcellName',
      optionsProps: dsProps => {
        const { queryFields, ...other } = dsProps;
        const newQueryFields = [
          {
            type: FieldType.object,
            label: intl?.get(`${modelPrompt}.router`).d('工艺路线'),
            name: 'routerObject',
            ignore: FieldIgnore.always,
            lovCode: 'MT.METHOD.ROUTER',
            lovPara: { tenantId },
            textField: 'routerName',
            valueField: 'routerId',
          },
          {
            name: 'routerId',
            bind: 'routerObject.routerId',
          },
          {
            name: 'routerName',
            bind: 'routerObject.routerName',
          },
        ];
        (queryFields || []).forEach((item: any) => {
          if (item.name !== 'routerId') {
            newQueryFields.push(item);
          }
        });
        return {
          ...other,
          queryFields: newQueryFields,
        };
      },
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
            workcellType: 'PROCESS',
          };
        },
        disabled: ({ record }) => {
          return (
            !record?.get('siteId') ||
            (record?.get('disabledListWork') || '').split(',').indexOf('processObject') > -1 ||
            (record?.get('ruleDtl') || {}).processWorkcellFlag !== 'Y'
          );
        },
      },
    },

    {
      name: 'processWorkcellId',
      bind: 'processObject.workcellId',
    },
    {
      name: 'processWorkcellName',
      bind: 'processObject.workcellName',
    },
    {
      name: 'processWorkcellCode',
      bind: 'processObject.workcellCode',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.station`).d('工位'),
      name: 'stationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.ROUTER_STATION',
      textField: 'workcellName',
      optionsProps: dsProps => {
        const { queryFields, ...other } = dsProps;
        const newQueryFields = [
          {
            type: FieldType.object,
            label: intl?.get(`${modelPrompt}.router`).d('工艺路线'),
            name: 'routerObject',
            ignore: FieldIgnore.always,
            lovCode: 'MT.METHOD.ROUTER',
            lovPara: { tenantId },
            textField: 'routerName',
            valueField: 'routerId',
          },
          {
            name: 'routerId',
            bind: 'routerObject.routerId',
          },
          {
            name: 'routerName',
            bind: 'routerObject.routerName',
          },
        ];
        (queryFields || []).forEach((item: any) => {
          if (item.name !== 'routerId') {
            newQueryFields.push(item);
          }
        });
        return {
          ...other,
          queryFields: newQueryFields,
        };
      },
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId'),
            workcellType: 'STATION',
          };
        },
        disabled: ({ record }) => {
          return (
            !record?.get('siteId') ||
            (record?.get('disabledListWork') || '').split(',').indexOf('stationObject') > -1 ||
            (record?.get('ruleDtl') || {}).stationWorkcellFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'stationWorkcellId',
      bind: 'stationObject.workcellId',
    },
    {
      name: 'stationWorkcellCode',
      bind: 'stationObject.workcellCode',
    },
    {
      name: 'stationWorkcellName',
      bind: 'stationObject.workcellName',
    },

    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.equipment`).d('设备'),
      name: 'equipmentObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentName',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return (record?.get('ruleDtl') || {}).equipmentFlag !== 'Y';
        },
      },
    },
    {
      name: 'equipmentId',
      bind: 'equipmentObject.equipmentId',
    },
    {
      name: 'equipmentName',
      bind: 'equipmentObject.equipmentName',
    },

    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.operation`).d('工艺'),
      name: 'operationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.ROUTER_OPERATION',
      textField: 'description',
      lovPara: {
        tenantId,
      },
      optionsProps: dsProps => {
        const { queryFields, ...other } = dsProps;
        const newQueryFields = [
          {
            type: FieldType.object,
            label: intl?.get(`${modelPrompt}.router`).d('工艺路线'),
            name: 'routerObject',
            ignore: FieldIgnore.always,
            lovCode: 'MT.METHOD.ROUTER',
            lovPara: { tenantId },
            textField: 'routerName',
            valueField: 'routerId',
          },
          {
            name: 'routerId',
            bind: 'routerObject.routerId',
          },
          {
            name: 'routerName',
            bind: 'routerObject.routerName',
          },
        ];
        (queryFields || []).forEach((item: any) => {
          if (item.name !== 'routerId') {
            newQueryFields.push(item);
          }
        });
        return {
          ...other,
          queryFields: newQueryFields,
        };
      },
      computedProps: {
        disabled: ({ record }) => {
          return (record?.get('ruleDtl') || {}).operationFlag !== 'Y';
        },
      },
    },
    {
      name: 'operationId',
      bind: 'operationObject.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationObject.operationName',
    },
    {
      name: 'operationDesc',
      bind: 'operationObject.description',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.supplier`).d('供应商'),
      name: 'supplierObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      textField: 'supplierName',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return (
            (record?.get('disabledListMapRelationship') || '').split(',').indexOf('supplierObject') >
            -1 || (record?.get('ruleDtl') || {}).supplierFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierObject.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierObject.supplierName',
    },

    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.customer`).d('客户'),
      name: 'customerObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return (
            (record?.get('disabledListMapRelationship') || '').split(',').indexOf('customerObject') >
            -1 || (record?.get('ruleDtl') || {}).customerFlag !== 'Y'
          );
        },
      },
    },
    {
      name: 'customerId',
      bind: 'customerObject.customerId',
    },
    {
      name: 'customerName',
      bind: 'customerObject.customerName',
    },

    {
      type: FieldType.string,
      label: intl?.get(`${modelPrompt}.otherObject`).d('其他对象'),
      name: 'otherObject',
      computedProps: {
        disabled: ({ record }) => {
          return (record?.get('ruleDtl') || {}).otherObjectFlag !== 'Y';
        },
      },
    },
  ],
});

const copyDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.sourceInspectScheme`).d('来源检验方案'),
      name: 'inspectSchemeObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_SCHEME',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return record?.get('inspectSchemeTmpObject');
        },
        required: ({ record }) => {
          return !record?.get('inspectSchemeTmpObject');
        },
      },
    },
    {
      name: 'inspectSchemeId',
      bind: 'inspectSchemeObject.inspectSchemeId',
    },
    {
      name: 'inspectSchemeCode',
      bind: 'inspectSchemeObject.inspectSchemeCode',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.inspectSchemeTmplt`).d('检验方案模板'),
      name: 'inspectSchemeTmpObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_SCHEME_TMP',
      lovPara: {
        tenantId,
      },
      computedProps: {
        disabled: ({ record }) => {
          return record?.get('inspectSchemeObject');
        },
        required: ({ record }) => {
          return !record?.get('inspectSchemeObject');
        },
      },
    },
    {
      name: 'inspectSchemeTmpId',
      bind: 'inspectSchemeTmpObject.inspectSchemeTmpId',
    },
  ],
});

// 复制检验业务tab
const copyBusinessTypeDS: () => DataSetProps = () => ({
  forceValidate: true,
  autoCreate: true,
  fields: [
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.originInspectBusinessType`).d('来源检验业务类型'),
      name: 'originInspectBusinessTypeObject',
      required: true,
    },
    {
      name: 'originInspectBusinessType',
      bind: 'originInspectBusinessTypeObject.value',
    },
    {
      name: 'originInspectBusinessTypeDesc',
      bind: 'originInspectBusinessTypeObject.meaning',
    },
    {
      type: FieldType.object,
      label: intl?.get(`${modelPrompt}.targetInspectBusinessType`).d('目标检验业务类型'),
      name: 'targetInspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.PERMISSION.INSPECT_BUS_TYPE_RULE_EDIT',
      required: true,
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            notInInspectBusinessTypes: record?.get('inspectBusinessTypes'),
            siteId: record?.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
      },
    },
    {
      name: 'targetInspectBusinessType',
      bind: 'targetInspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'targetInspectBusinessTypeDesc',
      bind: 'targetInspectBusinessTypeObject.inspectBusinessTypeDesc',
    },
  ],
});

export {
  listTableDS,
  detailFormDS,
  inspectionItemBasisDS,
  dimensionTableDS,
  copyDS,
  copyBusinessTypeDS,
};
