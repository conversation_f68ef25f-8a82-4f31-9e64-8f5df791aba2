import React, { useMemo, useRef, useState} from 'react';
import {Button, DataSet, Form, Lov, Modal, Output, Select, Table, TextField} from 'choerodon-ui/pro';
import {Popconfirm, Spin} from 'choerodon-ui';
import {Content, Header} from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import {ColumnProps} from 'choerodon-ui/pro/lib/table/Column';
import {ColumnAlign, ColumnLock} from 'choerodon-ui/pro/lib/table/enum';
import {ButtonColor, FuncType} from 'choerodon-ui/pro/lib/button/enum';
import {getCurrentOrganizationId} from 'utils/utils';
import {BASIC} from '@/utils/config';
import {observer} from 'mobx-react';
import myInstance from '@utils/myAxios';
import {useDataSetEvent} from 'utils/hooks';
import request from 'utils/request';
import {useRequest} from '@/components/tarzan-hooks';
import {scanFormDS, tableDS} from '../stores';
import {GetBarcodeInfo, PackingMaterialLotInfo} from '../services';
import '../index.module.less';

const modelPrompt = 'tarzan.hmes.containerLoadRegisterForB';
const tenantId = getCurrentOrganizationId();
let printerModal;


const PopConfirmButton = observer(
  ({
    dataSet,
    disabled = false,
    confirmCallback,
    confirmMessage,
    icon,
    funcType,
    color,
    buttonText,
  }) => (
    <Popconfirm
      title={confirmMessage}
      onConfirm={() => confirmCallback(dataSet)}
      okText={intl.get('tarzan.common.button.confirm').d('确认')}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
    >
      <Button
        disabled={!dataSet.selected?.length || disabled}
        icon={icon}
        funcType={funcType}
        color={color}
      >
        {buttonText}
      </Button>
    </Popconfirm>
  ),
);

const ContainerLoadRegisterForBList = props => {
  const { scanFormDs, tableDs, history } = props;

  const [unbindLoading,setUnbindLoading] = useState(false);
  // const [processing, setProcessing] = useState(false);
  // const [waitRequestCodeList, setWaitRequestCodeList] = useState<any>([]);

  const containerBarRef = useRef<any>(null);
  const unbindBarcodeRef = useRef<any>(null);

  // 获取容器装载信息
  const { run: getBarcodeInfo } = useRequest(GetBarcodeInfo(), {
    manual: true,
  });

  // 解绑物料批/Eo信息
  const { run: packingMaterialLotInfo, loading: packingLoading } = useRequest(
    PackingMaterialLotInfo(),
    {
      manual: true,
    },
  );

  useDataSetEvent(scanFormDs, 'update', ({ name, record }) => {
    if (name === 'containerTypeObj' && record.get('containerTypeObj')) {
      tableDs.loadData([]);
      scanFormDs.current?.set('identification', null);
      scanFormDs.current?.set('identificationId', null);
      scanFormDs.current?.set('currentQty', null);
    }
  });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        title: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
        width: 120,
        lock: ColumnLock.left,
        renderer: ({ record, dataSet }) => {
          if (dataSet && record) {
            return dataSet.totalCount - record?.index;
          }
        },
      },
      {
        name: 'identification',
        lock: ColumnLock.left,
        minWidth: 180,
      },
      {
        name: 'materialName',
        minWidth: 180,
      },
      {
        name: 'qualityStatus',
        minWidth: 120,
      },
      {
        name: 'degradeLevel',
        minWidth: 100,
      },
      {
        name: 'degradeLevel1',
        align: ColumnAlign.center,
        minWidth: 100,
      },
      {
        name: 'degradeLevel2',
        minWidth: 100,
      },
      {
        name: 'degradeLevel3',
        minWidth: 100,
      },
      {
        name: 'degradeLevel4',
        minWidth: 100,
      },
      {
        name: 'degradeLevel5',
        minWidth: 100,
      },
      {
        name: 'degradeLevel6',
        minWidth: 100,
      },
      {
        name: 'degradeLevel7',
        minWidth: 100,
      },
      {
        name: 'degradeLevel8',
        minWidth: 100,
      },
      {
        name: 'degradeLevel9',
        minWidth: 100,
      },
      {
        name: 'degradeLevel10',
        minWidth: 100,
      },
      {
        name: 'qty',
        minWidth: 80,
      },
      {
        name: 'uomCode',
        minWidth: 80,
      },
      {
        name: 'materialCode',
        minWidth: 180,
      },
      {
        name: 'workOrderNum',
        minWidth: 180,
      },
      {
        name: 'scanIdentification',
        minWidth: 180,
      },
      {
        name: 'modelCode',
        minWidth: 180,
      },
    ];
  }, []);

  const handleScanBarcode = async () => {
    const flag = await scanFormDs.current?.validate();
    if (!flag) {
      return;
    }
    const _barcode = scanFormDs.current?.get('containerBarCode')?.trim() || scanFormDs.current?.get('identification');
    if (!_barcode) {
      return;
    }
    const _locatorCode = scanFormDs.current?.get('locatorCode');
    const _bProductReason = scanFormDs.current?.get('bProductReason');
    const _printer = scanFormDs.current?.get('printer');
    const _containerTypeId = scanFormDs.current?.get('containerTypeId');
    const _identificationId = scanFormDs.current?.get('identificationId') || null;
    const currentMaterialId = tableDs.current?.get('materialId') || null;
    const currentDegradeLevel = tableDs.current?.get('degradeLevel') || null;
    const tempQty = tableDs.totalCount;
    if (tableDs.data.some(item => item.get('identification') === _barcode)) {
      notification.error({
        message: '已存在该条码，不允许重复扫描！' ,
        duration: 100,
        className: "notification-style",
      });
      return;
    }
    containerBarRef?.current?.blur();
    const initialTime = new Date();
    await getBarcodeInfo({
      params: {
        locatorCode: _locatorCode,
        bProductReason: _bProductReason,
        printer: _printer,
        identification: _barcode,
        containerTypeId: _containerTypeId,
        scannedContainerId: _identificationId,
        packingLevel: scanFormDs.current?.get('packingLevel'),
        materialId: currentMaterialId,
        degradeLevel: currentDegradeLevel,
      },
      onFailed: () => {
        containerBarRef?.current?.focus();
        containerBarRef?.current?.select();
        Modal.open({
          width: 300,
          children: (
            <Form record={scanFormDs.current}>
              <Output name="containerBarCode" disabled />
            </Form>
          ),
          onOk: () => {
            handleScanBarcode()
          },
          onCancel: () => {},
        });
      },
      onSuccess: res => {
        containerBarRef?.current?.focus();
        containerBarRef?.current?.select();
        console.log("=接口耗时=", new Date() - initialTime);
        // 判断扫描的是箱码还是条码
        // 扫的箱码就需要把当前的表格清空，
        if (res?.identificationId) {
          handleReset().then(() => {
            // 更新箱码信息
            scanFormDs.current?.set('identification', res?.identification || null);
            scanFormDs.current?.set('identificationId', res?.identificationId || null);
            // 更新条码表格
            if (res.barcodeList?.length) {
              tableDs.loadData([ ...res.barcodeList ]);
              console.log("=接口耗时+渲染耗时1=", new Date() - initialTime);
              scanFormDs.current?.set('currentQty', Number(tempQty) + Number(res.barcodeList?.length));
            }
          })
        } else {
          // 更新条码表格
          res.barcodeList?.forEach(item => {
            tableDs.create({ ...item }, 0);
          });
          scanFormDs.current?.set('currentQty', Number(tempQty) + Number(res.barcodeList?.length || 0));
          console.log("=接口耗时+渲染耗时2=", new Date() - initialTime);
        }
      },
    }); // 调用处理条码的接口

  };

  // 删除条码行
  const handleDelete = () => {
    const tempQty = tableDs.totalCount;
    const flag = tableDs.selected.every(item => !item.get('scanContainerId'));
    if(!flag){
      notification.error({
        message: '存在条码已绑定容器，无法删除！',
        duration: 100,
        className: "notification-style",
      })
      return;
    }
    scanFormDs.current?.set('currentQty', Number(tempQty) - Number(tableDs.selected.length));
    tableDs.selected.forEach(record => tableDs.remove(record));
  };

  // 重制表格行
  const handleReset = async () => {
    tableDs.deleteAll(false).then(() => scanFormDs.current?.set('currentQty', null));
  };

  // 拿取解绑
  const handleUnbind = () => {
    const tempQty = tableDs.totalCount;
    if(!tableDs.selected.length || !tableDs.selected.every(item => !!item.get('scanContainerId'))){
      notification.error({
        message: '存在条码未绑定容器！',
        duration: 100,
        className: "notification-style",
      })
      return;
    }
    setUnbindLoading(true)
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-to-b/box/unload/ui`,{
      method: "POST",
      body: tableDs.selected.map(item => item.data),
    }).then(res=>{
      if(res?.success) {
        scanFormDs.current?.set('currentQty', Number(tempQty) - Number(tableDs.selected.length));
        tableDs.selected.forEach(record => tableDs.remove(record));
        notification.success({});
      }
      setUnbindLoading(false)
    })
  };

  // 打包完工
  const handlePacking = () => {
    packingMaterialLotInfo({
      params: {
        ...scanFormDs.current?.toData(),
        materialLotEoInfoList: tableDs.toData(),
      },
      onSuccess: res => {
        notification.success({});
        scanFormDs.current.set('identification', res?.identification);
        tableDs.loadData([]);
        scanFormDs.current.set('currentQty', null);
      },
    });
  };

  // 跳转到装托卸托
  const handleGoToDetail = () => {
    if(!scanFormDs.current?.get('locatorCode')){
      notification.error({
        message: '请先选择完工货位',
        duration: 100,
        className: "notification-style",
      })
      return;
    }
    if(!scanFormDs.current?.get('printer')){
      notification.error({
        message: '请先选择打印机',
        duration: 100,
        className: "notification-style",
      })
      return;
    }
    history.push({
      pathname: '/hmes/container-load-register-for-b/detail',
      state: {
        locatorCode: scanFormDs.current?.get('locatorCode'),
        printer: scanFormDs.current?.get('printer'),
      },
    })
  };

  // 清除箱码
  const handleClearBoxCode = () => {
    scanFormDs.current?.init('containerBarCode', null);
    scanFormDs.current?.init('identification', null);
    scanFormDs.current?.init('identificationId', null);
    scanFormDs.current?.init('unbindBarcode', null);
    tableDs.loadData([]);
    scanFormDs.current?.init('currentQty', null);
  };

  const handleUnbindBarcodeRef = () => {
    const unbindBarcode = scanFormDs.current?.get('unbindBarcode');
    const index = tableDs.findIndex(
      r => r.get('identification') === unbindBarcode,
    );
    tableDs.select(index);
    unbindBarcodeRef?.current?.focus();
    unbindBarcodeRef?.current?.select();
  };


  // 打印
  const handlePrint = () => {
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-to-b/box/barcode-print`;
    myInstance
      .post(url, {
        containerIdList: [scanFormDs.current?.get('identificationId')],
        printer: {
          value: scanFormDs.current?.get('printer') || null,
        },
      })
      .then(res => {
        if (res.data.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.success.print`).d('打印成功!'),
          });
          scanFormDs.current?.init('containerBarCode', null);
          scanFormDs.current?.set('identificationId', null)
          scanFormDs.current?.set('identification', null)
          tableDs.loadData([]);
          scanFormDs.current?.init('currentQty', null);
          printerModal?.close();
        } else {
          // if (res.data.statusCode === "PRINTER_CHOOSE") {
          //   return selectPrinter(res.data.attr);
          // }
          printerModal?.close();
          notification.error({
            message: res.data.message || intl.get(`${modelPrompt}.error.print`).d('打印失败!'),
            duration: 100,
            className: "notification-style",
          });
        }
      });
  };


  return (
    <div className="hmes-style">
      <Spin
        spinning={
          unbindLoading ||
          packingLoading
        }
      >
        <Header title={intl.get(`${modelPrompt}.title.list`).d('B品打包装箱-MES')}>
          <Button
            color={ButtonColor.primary}
            onClick={handleUnbind}
            loading={unbindLoading}
          >
            {intl.get(`${modelPrompt}.button.unbind`).d('拿取解绑')}
          </Button>
          <Button
            color={ButtonColor.primary}
            onClick={handlePacking}
            loading={packingLoading}
            disabled={!tableDs.length}
          >
            {intl.get(`${modelPrompt}.button.packing`).d('打包完工')}
          </Button>
          <Button color={ButtonColor.primary} onClick={handleGoToDetail}>
            {intl.get(`${modelPrompt}.button.register`).d('装托卸托')}
          </Button>
          <Button color={ButtonColor.primary} onClick={handleClearBoxCode} disabled={!scanFormDs.current?.get('identificationId')}>
            {intl.get(`${modelPrompt}.button.register`).d('清除箱码')}
          </Button>
          <Button
            color={ButtonColor.primary}
            onClick={handlePrint}
            loading={packingLoading}
            disabled={!scanFormDs.current?.get('identification')}
          >
            {intl.get(`${modelPrompt}.button.print`).d('打印')}
          </Button>
        </Header>
        <Content>
          <Form dataSet={scanFormDs} columns={4}>
            <Select name="locatorCode" />
            <Select name="bProductReason" />
            <Lov name="containerTypeObj" />
            <Select name="printer" />
            <TextField
              ref={containerBarRef}
              name="containerBarCode"
              onEnterDown={handleScanBarcode}
            />
            <Output name="identification" />
            <Output name="currentQty" />
            <TextField
              ref={unbindBarcodeRef}
              name="unbindBarcode"
              onEnterDown={handleUnbindBarcodeRef}
            />
          </Form>
          <Table
            buttons={[
              <Button icon="delete" disabled={!tableDs.records.length} onClick={handleReset}>
                {intl.get('tarzan.common.button.reset').d('重置')}
              </Button>,
              <PopConfirmButton
                dataSet={tableDs}
                confirmCallback={handleDelete}
                confirmMessage={intl
                  .get(`tarzan.common.message.confirm.delete`)
                  .d('是否确认删除?')}
                icon="delete"
                funcType={FuncType.flat}
                color={ButtonColor.red}
                buttonText={intl.get('tarzan.common.button.delete').d('删除')}
                disabled={false}
              />,
            ]}
            dataSet={tableDs}
            columns={columns}
            searchCode="containerLoadRegisterForBSearch"
            customizedCode="containerLoadRegisterForBCust"
            virtual
            style={{ height: 600 }}
          />
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const scanFormDs = new DataSet({ ...scanFormDS() });
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        scanFormDs,
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true },
  )(ContainerLoadRegisterForBList),
);
