/**
 * @Description: 问题管理监控看板-主界面
 * @Author: <<EMAIL>>
 * @Date: 2023-11-01 09:47:40
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-06 17:55:44
 */
import React, { useMemo, useEffect, useRef, useState } from 'react';
import { DataSet, Table, Form, Select } from 'choerodon-ui/pro';
import { Row, Col, Popover } from 'choerodon-ui';
import intl from 'utils/intl';
import echarts from 'echarts';
import moment from 'moment';
import withProps from 'utils/withProps';
import { useDataSetEvent } from 'utils/hooks';
import { Content, Header } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { TableQueryBarType } from 'choerodon-ui/pro/es/table/enum';
import { TarzanS<PERSON> } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getProductWithPrecision } from '@/utils';
import { observer } from 'mobx-react';
import { problemDS, typeDimensionDS } from '../stores';
import { QueryProblemInfo, QueryProblemRatio } from '../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.problemManagement.problemSumInfo';
let ratioChart; // 问题数量占比chart
let statChart; // 问题解决进度统计chart
let trendChart; // 问题关闭率趋势chart
const colorList = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
];
const problesStatusList = [
  intl.get(`${modelPrompt}.stat.create`).d('已创建'),
  intl.get(`${modelPrompt}.stat.handle`).d('处理中'),
  intl.get(`${modelPrompt}.stat.overtime`).d('已逾期'),
  intl.get(`${modelPrompt}.stat.closed`).d('已关闭'),
  intl.get(`${modelPrompt}.stat.freeze`).d('已冻结'),
];

const ProblemSumInfoList = props => {
  const { searchDs, problemDs, typeDimensionDs, history } = props;
  const [pageInit, setPageInit] = useState(false);
  const ratioChartRef = useRef(null);
  const statChartRef = useRef(null);
  const trendChartRef = useRef(null);

  // 查询看板界面所有数据
  const { run: queryProblemInfo, loading: queryLoading } = useRequest(QueryProblemInfo(), {
    manual: true,
  });
  // 查询问题数量占比数据
  const { run: queryProblemRatio, loading: queryRatioLoading } = useRequest(QueryProblemRatio(), {
    manual: true,
  });

  useEffect(() => {
    if (!pageInit) {
      ratioChart = echarts.init(ratioChartRef.current);
      statChart = echarts.init(statChartRef.current);
      trendChart = echarts.init(trendChartRef.current);
      window.onresize = () => {
        setTimeout(() => {
          ratioChart.resize();
          statChart.resize();
          trendChart.resize();
        }, 300);
      };
    }
    handleQuery();
  }, []);

  const handleQuery = (selectFlag = false) => {
    const { problemStatusList, ...others } = searchDs.queryDataSet.current?.toData();
    const typeDimension = typeDimensionDs?.current?.get('typeDimension');
    const _run = selectFlag ? queryProblemRatio : queryProblemInfo;
    _run({
      queryParams: {
        typeDimension,
      },
      params: {
        ...others,
        problemStatusList: problemStatusList?.length ? problemStatusList : undefined,
      },
      onSuccess: res => {
        if (selectFlag) {
          handleInitRatioChart(res);
        } else {
          const { ratioInfo, statInfo, tableInfo, trendInfo } = res;
          problemDs.loadData([...tableInfo]);
          handleInitRatioChart(ratioInfo);
          handleInitStatChart(statInfo);
          handleInitTrendChart(trendInfo);
          setPageInit(true);
        }
      },
    });
  };

  const handleUpdateNextReportDate = ({ dataSet, name, value }) => {
    if (!value) {
      handleProblemListQuery();
      return;
    }
    if (name === 'nextReportDateFrom') {
      dataSet.current?.set('nextReportDateFrom', `${moment(value).format('YYYY-MM-DD')} 00:00:00`);
    } else if (name === 'nextReportDateTo') {
      dataSet.current?.set('nextReportDateTo', `${moment(value).format('YYYY-MM-DD')} 23:59:59`);
    }
    handleProblemListQuery();
  }

  useDataSetEvent(searchDs.queryDataSet, 'update', () => handleQuery());
  useDataSetEvent(problemDs.queryDataSet, 'update', handleUpdateNextReportDate);
  useDataSetEvent(problemDs, 'load', () => handleProblemListQuery());

  const handleInitRatioChart = dataSource => {
    const { data } = handleFormatRatioData(dataSource);
    const option: any = {
      dataset: {
        dimensions: ['groupType', 'ratioPercent'],
        source: data,
      },
      color: colorList,
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {d}%',
      },
      label: {
        formatter: (params) => {
          const { groupType, groupQty, ratioPercent } = params?.data || {};
          return `${groupType}：${groupQty || 0} (${ratioPercent || 0}%)`;
        },
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        top: '15%',
        left: '80%',
        pageIcons: {
          horizontal: ['path/to/left-icon.png', 'path/to/right-icon.png'],
        },
        pageFormatter: '{current}/{total}页',
        pageTextStyle: {
          color: '#333',
          fontSize: 12,
        },
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          center: ['40%', '50%'],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          encode: {
            itemName: 'groupType',
            value: 'ratioPercent',
          },
        },
      ],
    };
    ratioChart.setOption(option, true);
  };

  const handleFormatRatioData = data => {
    const _data = [...data];
    _data.forEach(item => {
      const { ratio } = item;
      item.ratioPercent = ratio
        ? getProductWithPrecision(ratio, 100, 6, 'multiply')
        : 0;
    });
    return {
      data: _data,
    };
  };

  const handleInitStatChart = data => {
    const option: any = {
      color: colorList,
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: [intl.get(`${modelPrompt}.stat.sum`).d('总数'), ...problesStatusList],
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          type: 'bar',
          label: {
            show: true,
            position: 'top',
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 1, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 0, color: '#188df0' },
            ]),
          },
          data: [
            data?.sumQty,
            data?.createQty,
            data?.handleQty,
            data?.overtimeQty,
            data?.closedQty,
            data?.freezeQty,
          ],
        },
      ],
    };
    statChart.setOption(option, true);
  };

  const handleInitTrendChart = sourceData => {
    const { data } = handleFormatTrendData(sourceData);
    const option: any = {
      dataset: {
        dimensions: [
          'monthWeekInfo',
          'createQty',
          'handleQty',
          'overtimeQty',
          'closedQty',
          'freezeQty',
          'closedRatePercent',
        ],
        source: data,
      },
      color: [
        '#73c0de',
        '#5470c6',
        '#ee6666',
        '#91cc75',
        '#8c8c8c',
        '#3ba272',
      ],
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        left: 'center',
        bottom: 0,
      },
      xAxis: {
        type: 'category',
      },
      yAxis: [
        {
          type: 'value',
        },
        {
          type: 'value',
          axisLabel: {
            formatter: '{value} %',
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        { name: problesStatusList[0], type: 'bar', stack: 'Ad' },
        { name: problesStatusList[1], type: 'bar', stack: 'Ad' },
        { name: problesStatusList[2], type: 'bar', stack: 'Ad' },
        { name: problesStatusList[3], type: 'bar', stack: 'Ad' },
        { name: problesStatusList[4], type: 'bar', stack: 'Ad' },
        {
          name: intl.get(`${modelPrompt}.stat.closeRatio`).d('关闭率'),
          type: 'line',
          yAxisIndex: 1,
          tooltip: {
            valueFormatter: value => `${value} %`,
          },
        },
      ],
    };
    trendChart.setOption(option, true);
  };

  const handleFormatTrendData = data => {
    const _data = [...data];
    _data.forEach(item => {
      const { closedRate } = item;
      item.closedRatePercent = closedRate
        ? getProductWithPrecision(closedRate, 100, 6, 'multiply')
        : 0;
    });
    return {
      data: _data,
    };
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'problemCode',
        width: 120,
        renderer: ({ record, value }) => (
          <a
            onClick={() => {
              history.push(
                `/hwms/problem-management/problem-management-platform/dist/${record?.get(
                  'problemId',
                )}`,
              );
            }}
          >
            {value}
          </a>
        ),
      },
      {
        name: 'problemTitle',
      },
      {
        name: 'proposeDepartment',
      },
      {
        name: 'registerTime',
        width: 150,
      },
      {
        name: 'leadPersonName',
      },
      {
        name: 'responsiblePersonName',
      },
      {
        name: 'responsibleDepartment',
      },
      {
        name: 'problemCategory',
      },
      {
        name: 'severityLevel',
      },
      {
        name: 'influenceLevel',
      },
      {
        name: 'nextReportDate',
        align: ColumnAlign.center,
        width: 150,
        hidden: true,
      },
      {
        name: 'schedule',
        width: 250,
        align: ColumnAlign.center,
        help: intl.get(`${modelPrompt}.help.schedule`).d('绿色：按时完成；蓝色：进行中；红色：逾期；灰色：未开始'),
        renderer: ({ record }) => (
          <div className={styles.schedule_container}>
            <Popover
              content={intl.get(`${modelPrompt}.problem.creation`).d('问题创建')}
              trigger="hover"
            >
              <div
                className={styles.schedule_status}
              />
            </Popover>
            <div className={styles.schedule_course} />
            <Popover
              content={intl
                .get(
                  `${modelPrompt}.responsibility.team.formation`,
                )
                .d('责任小组组建')}
              trigger="hover"
            >
              <div className={styles.schedule_status} />
            </Popover>
            <div className={styles.schedule_course} />
            <Popover
              content={intl.get(`${modelPrompt}.reason.analysis`).d('原因分析')}
              trigger="hover"
            >
              <div
                className={styles.schedule_status}
                style={
                  record?.get('causeAnalysis') === 'red'
                    ? { backgroundColor: 'red' }
                    : record?.get('causeAnalysis') === 'green'
                      ? { backgroundColor: '#11d954' }
                      : record?.get('causeAnalysis') === 'blue'
                        ? { backgroundColor: 'blue' }
                        : { backgroundColor: 'gray' }
                }
              />
            </Popover>
            <div className={styles.schedule_course} />
            <Popover
              content={intl
                .get(
                  `${modelPrompt}.countermeasure.making`,
                )
                .d('对策制定')}
              trigger="hover"
            >
              <div
                className={styles.schedule_status}
                style={
                  record?.get('measureEnact') === 'red'
                    ? { backgroundColor: 'red' }
                    : record?.get('measureEnact') === 'green'
                      ? { backgroundColor: '#11d954' }
                      : record?.get('measureEnact') === 'blue'
                        ? { backgroundColor: 'blue' }
                        : { backgroundColor: 'gray' }
                }
              />
            </Popover>
            <div className={styles.schedule_course} />
            <Popover
              content={intl.get(`${modelPrompt}.effect.verification`).d('效果验证')}
              trigger="hover"
            >
              <div
                className={styles.schedule_status}
                style={
                  record?.get('resultVerification') === 'red'
                    ? { backgroundColor: 'red' }
                    : record?.get('resultVerification') === 'green'
                      ? { backgroundColor: '#11d954' }
                      : record?.get('resultVerification') === 'blue'
                        ? { backgroundColor: 'blue' }
                        : { backgroundColor: 'gray' }
                }
              />
            </Popover>
            <div className={styles.schedule_course} />
            <Popover
              content={intl.get(`${modelPrompt}.verification.off`).d('问题关闭')}
              trigger="hover"
            >
              <div
                className={styles.schedule_status}
                style={
                  record?.get('verifyClosed') === 'red'
                    ? { backgroundColor: 'red' }
                    : record?.get('verifyClosed') === 'green'
                      ? { backgroundColor: '#11d954' }
                      : record?.get('verifyClosed') === 'blue'
                        ? { backgroundColor: 'blue' }
                        : { backgroundColor: 'gray' }
                }
              />
            </Popover>
          </div>
        ),
      },
    ];
  }, []);

  // 对查询条回车事件的处理
  const handleProblemListQuery = () => {
    const {
      nextReportDateFrom,
      nextReportDateTo,
    } = problemDs.queryDataSet?.current?.toData();
    const _nextReportDateFrom = nextReportDateFrom ? moment(nextReportDateFrom).format('X') : null;
    const _nextReportDateTo = nextReportDateTo ? moment(nextReportDateTo).format('X') : null;
    problemDs.forEach(record => {
      const nextReportDate = record.get('nextReportDate') ? moment(`${record.get('nextReportDate')} 00:00:00`).format('X') : null;
      if (
        (nextReportDate &&
        (!_nextReportDateFrom || _nextReportDateFrom <= nextReportDate) &&
        (!_nextReportDateTo || _nextReportDateTo >= nextReportDate)) ||
        (!_nextReportDateFrom && !_nextReportDateTo)
      ) {
        record.setState('displayFlag', 'Y');
      } else {
        record.setState('displayFlag', 'N');
      }
    });
  };

  const TitleComponent: React.FC<{ title: string; extraComponent?: any }> = ({
    title,
    extraComponent,
  }) => {
    return (
      <div className={styles['title-component']}>
        <div className={styles['title-component-title']}>{title}</div>
        {extraComponent}
      </div>
    );
  };

  const SelectComponent = observer(({ dataSet }) => {
    return (
      <Form dataSet={dataSet} className={styles['title-component-form']}>
        <Select name="typeDimension" clearButton={false} onChange={() => handleQuery(true)} />
      </Form>
    );
  });

  return (
    <div className="hmes-style problem-sum-info">
      <TarzanSpin dataSet={searchDs} spinning={queryLoading || queryRatioLoading}>
        <Header title={intl.get(`${modelPrompt}.title.list`).d('问题管理监控看板')} />
        <Content className={styles['problem-sum-info']}>
          <Row>
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryFields={{
                typeDimension: <></>,
              }}
              queryBarProps={{
                onRefresh: () => handleQuery(),
                onReset: () => handleQuery(),
                onQuery: () => handleQuery(),
                fuzzyQuery: false,
              }}
              dataSet={searchDs}
              columns={[]}
              queryFieldsLimit={10}
              className={styles['problem-sum-info-query-bar']}
            />
          </Row>
          <Row>
            <Col span={11}>
              <TitleComponent
                title={intl.get(`${modelPrompt}.title.problemRatio`).d('问题数量占比')}
                extraComponent={<SelectComponent dataSet={typeDimensionDs} />}
              />
              <div
                id="problemRatioChart"
                ref={ratioChartRef}
                className={styles['problem-sum-info-card']}
                style={{
                  height: '300px',
                }}
              />
            </Col>
            <Col span={11} offset={2}>
              <TitleComponent
                title={intl.get(`${modelPrompt}.title.problemStat`).d('问题解决进度统计')}
              />
              <div
                id="problemStatChart"
                ref={statChartRef}
                className={styles['problem-sum-info-card']}
                style={{
                  height: '300px',
                }}
              />
            </Col>
          </Row>
          <Row>
            <TitleComponent
              title={intl.get(`${modelPrompt}.title.problemList`).d('重点问题清单')}
            />
            <Table
              queryBar={TableQueryBarType.bar}
              dataSet={problemDs}
              columns={columns}
              customizedCode="problemSumInfo-customizedCode"
              className={styles['problem-sum-info-card']}
              // queryBarProps={{
              //   onQuery: handleProblemListQuery,
              // }}
              virtual
              virtualCell
              style={{
                height: 300,
              }}
              filter={record => {
                return (
                  (!record.getState('displayFlag') || record.getState('displayFlag') === 'Y')
                );
              }}
            />
          </Row>
          <Row>
            <TitleComponent
              title={intl.get(`${modelPrompt}.title.problemTrend`).d('问题关闭率趋势')}
            />
            <div
              id="problemTrendChart"
              ref={trendChartRef}
              className={styles['problem-sum-info-card']}
              style={{
                height: '300px',
              }}
            />
          </Row>
        </Content>
      </TarzanSpin>
    </div>
  );
};
export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const searchDs = new DataSet({
        ...problemDS(),
        fields: undefined,
      });
      const problemDs = new DataSet({
        ...problemDS(),
        queryFields: [
          {
            name: 'nextReportDateFrom',
            type: FieldType.date,
            label: intl.get(`${modelPrompt}.nextReportDateFrom`).d('下次报告日期从'),
            max: 'nextReportDateTo',
            dynamicProps: {
              defaultValue: () => {
                const now = new Date();
                const time = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
                return moment(time).format('YYYY-MM-DD HH:mm:ss');
              },
            },
          },
          {
            name: 'nextReportDateTo',
            type: FieldType.date,
            label: intl.get(`${modelPrompt}.nextReportDateTo`).d('下次报告日期至'),
            min: 'nextReportDateFrom',
            dynamicProps: {
              defaultValue: () => {
                const now = new Date();
                const time = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                return moment(time).format('YYYY-MM-DD HH:mm:ss');
              },
            },
          },
        ],
      });
      const typeDimensionDs = new DataSet(typeDimensionDS());
      return {
        searchDs,
        problemDs,
        typeDimensionDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ProblemSumInfoList),
);
