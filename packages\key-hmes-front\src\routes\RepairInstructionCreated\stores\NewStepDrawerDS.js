/**
 * 详情页表格，步骤的新建编辑ds
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */


import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'hzero-front/lib/utils/intl';
import { getCurrentOrganizationId, getCurrentLanguage } from "hzero-front/lib/utils/utils";
import { DataSet } from 'choerodon-ui/pro';
import { routeStepOptionDS, returnStepsOptionDS } from './CommonDS';


const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.process.routes.model.routes';


/**
 * 工艺tab
 */
const operationFormDS = ( operationLov ) => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  autoCreate: true,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationId`).d('工艺'),
      lovCode: operationLov,
      required: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    {
      name: 'operationRevision',
    },
    {
      name: 'stepName',
      label: intl.get(`${modelPrompt}.stepName`).d('识别码'),
      type: FieldType.string,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('stepNameRequired') === true;
        },
      },
    },
    {
      name: 'stepNameRequired', // 用来记录识别码的状态
      type: FieldType.boolean,
    },
    {
      name: 'requiredTimeInProcess',
      label: intl.get(`${modelPrompt}.requiredTimeInProcessNo`).d('标准工时'),
      type: FieldType.number,
    },
    // 时间类型
    {
      name: 'timeUom',
      type: FieldType.string,
      options: new DataSet({
        data: [
          { value: 's', key: intl.get('tarzan.common.date.unit.second').d('秒') },
          { value: 'm', key: intl.get('tarzan.common.date.unit.minute').d('分钟') },
          { value: 'h', key: intl.get('tarzan.common.date.unit.hour').d('小时') },
          { value: 'd', key: intl.get('tarzan.common.date.unit.day').d('天') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
      defaultValue: 'm',
    },
    {
      name: 'stepGroup',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stepGroup`).d('所属步骤组'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
    },
    {
      name: 'specialInstruction',
      label: intl.get(`${modelPrompt}.specialInstruction`).d('特殊指令'),
      type: FieldType.string,
    },
    {
      name: 'stepFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.entryStepFlag`).d('步骤属性'),
      highlight: true,
      multiple: true,
      defaultValue: 'keyStepFlag',
    },
    {
      name: 'returnType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.returnType`).d('返回策略'),
      options: new DataSet({...returnStepsOptionDS()}),
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        required: ({ record }) => {
          return !!record.get('stepFlag').includes('mtRouterReturnStepDTO');
        },
      },
    },
    {
      name: 'returnOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.returnOperationId`).d('返回目标工艺'),
      lovCode: operationLov,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('returnType') === 'DESIGNATED_OPERATION';
        },
      },
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'returnOperationId',
      bind: 'returnOperationLov.operationId',
    },
    {
      name: 'returnOperationName',
      bind: 'returnOperationLov.operationName',
    },
  ],
});

/**
 * 步骤组tab
 */
const groupFormDS = (operationLov) => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  autoCreate: true,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'description',
      label: intl.get(`${modelPrompt}.description`).d('描述'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'routerStepGroupType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerStepGroupType`).d('步骤组类型'),
      options: new DataSet({...routeStepOptionDS('ROUTER_STEP_GROUP_TYPE')}),
      textField: 'description',
      valueField: 'typeCode',
      required: true,
    },
    {
      name: 'stepFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.entryStepFlag`).d('步骤属性'),
      highlight: true,
      multiple: true,
      defaultValue: 'keyStepFlag',
    },
    {
      name: 'returnType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.returnType`).d('返回策略'),
      options: new DataSet({...returnStepsOptionDS()}),
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        required: ({ record }) => {
          return !!record.get('stepFlag').includes('mtRouterReturnStepDTO');
        },
      },
    },
    {
      name: 'returnOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.returnOperationId`).d('返回目标工艺'),
      lovCode: operationLov,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('returnType') === 'DESIGNATED_OPERATION';
        },
      },
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'returnOperationId',
      bind: 'returnOperationLov.operationId',
    },
    {
      name: 'returnOperationName',
      bind: 'returnOperationLov.operationName',
    },
  ],
});



/**
 * 子工艺路线tab
 */
const routerFormDS = (routerLov, operationLov) => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  autoCreate: true,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'routerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.routerId`).d('工艺路线'),
      lovCode: routerLov,
      required: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'routerName',
      bind: 'routerLov.routerName',
    },
    {
      name: 'routerId',
      bind: 'routerLov.routerId',
    },
    {
      name: "revision",
    },
    {
      name: 'stepFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.entryStepFlag`).d('步骤属性'),
      highlight: true,
      multiple: true,
      defaultValue: 'keyStepFlag',
    },
    {
      name: 'returnType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.returnType`).d('返回策略'),
      options: new DataSet({...returnStepsOptionDS()}),
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        required: ({ record }) => {
          return !!record.get('stepFlag').includes('mtRouterReturnStepDTO');
        },
      },
    },
    {
      name: 'returnOperationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.returnOperationId`).d('返回目标工艺'),
      lovCode: operationLov,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => {
          return record.get('returnType') === 'DESIGNATED_OPERATION';
        },
      },
    },
    {
      name: 'returnOperationId',
      bind: 'returnOperationLov.operationId',
    },
    {
      name: 'returnOperationName',
      bind: 'returnOperationLov.operationName',
    },
  ],
});

export { operationFormDS, routerFormDS, groupFormDS };
