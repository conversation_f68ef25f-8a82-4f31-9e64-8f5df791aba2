/**
 * @Description: 在制品批量冻结解冻
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:40:22
 * @LastEditTime: 2023-01-10 13:56:55
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { DataSet, Table, Modal, Button, Form, TextField, Lov, Row, Col, Select, Icon } from 'choerodon-ui/pro';
import { isNil, uniq } from 'lodash';
import formatterCollections from 'utils/intl/formatterCollections';
import { getResponse } from '@utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import LovModal from './LovModal';
import { tableDS, modalDS, modalProcessDS, InputLovDS } from './stores';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'work.in.progress.freeze.thawe';

const ExecuteList = props => {
  const {
    dataSet,
    modalDs,
    modalProcessDs,
    inputLovDS,
    // match: { path },
    // customizeTable,
  } = props;

  // 选中列表项执行作业状态 (选中条状态不同时为空字符)
  const [selectObjs, setSelectObjs] = useState([]);

  const [expandForm, setExpandForm] = useState(true);

  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);

  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const columns = [
    // {
    //   name: 'orderNumber',
    //   title: intl.get('hzero.common.model.orderSeq').d('序号'),
    //   renderer: (data) => {
    //     return (data?.dataSet.currentPage - 1) * data?.dataSet.pageSize + data?.record?.index + 1;
    //   },
    // },
    {
      name: 'identification',
      width: 220,
    },
    {
      name: 'identificationTypeDesc',
    },
    {
      name: 'workcellCode',
    },
    {
      name: 'workcellName',
    },
    {
      name: 'modelCode',
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 200,
    },
    {
      name: 'qty',
      width: 150,
      align: 'right',
    },
    {
      name: 'operationDesc',
      width: 100,
    },
    {
      name: 'specifyOperationName',
      width: 100,
    },
    {
      name: 'prodLineCode',
      width: 200,
    },
    {
      name: 'prodLineName',
      width: 150,
    },
    {
      name: 'workOrderNum',
      width: 150,
    },
    {
      name: 'siteCode',
      width: 200,
    },
    {
      name: 'freezeStatus',
      width: 120,
    },
    {
      name: 'freezeReason',
      width: 120,
    },
    {
      name: 'freezeTime',
      width: 120,
      align: 'center',
    },
    {
      name: 'operatorName',
      width: 120,
    },
  ];

  // 监听C7Npro列表选中操作
  useEffect(() => {
    if (dataSet) {
      dataSet.addEventListener('select', handleDataSetSelectUpdate);
      dataSet.addEventListener('unSelect', handleDataSetSelectUpdate);
      dataSet.addEventListener('selectAll', handleDataSetSelectUpdate);
      dataSet.addEventListener('unSelectAll', handleDataSetSelectUpdate);
    }
    return () => {
      if (dataSet) {
        dataSet.removeEventListener('select', handleDataSetSelectUpdate);
        dataSet.removeEventListener('unSelect', handleDataSetSelectUpdate);
        dataSet.removeEventListener('selectAll', handleDataSetSelectUpdate);
        dataSet.removeEventListener('unSelectAll', handleDataSetSelectUpdate);
      }
    };
  });

  // 处理选中条执行作业状态
  const handleDataSetSelectUpdate = () => {
    if (dataSet && dataSet.selected) {
      const selectList = dataSet.selected;
      if (selectList && selectList.length > 0) {
        // const newArr = [];
        // selectList.forEach((item) => {
        //   newArr.push({
        //     eoId: item.get('eoId'),
        //     materialId: item.get('materialId'),
        //     workOrderId: item.get('workOrderId'),
        //     qty: item.get('qty'),
        //     operationId: item.get('operationId'),
        //   })
        // })
        const newArr = selectList.map((ele) => ele.toData());
        setSelectObjs(newArr)
      } else {
        setSelectObjs([]);
      }
    } else {
      setSelectObjs([]);
    }
  };

  // 解冻
  const handleThawing = async () => {
    if(!dataSet.selected.length){
      notification.error({
        message: '请勾选数据',
      })
      return
    }
    const flag = dataSet.selected.every(item => item.get('freezeStatus') === 'Y');
    if(!flag){
      notification.error({
        message: '只能解冻已冻结的数据',
      });
      return;
    }
    modalDs.reset();
    Modal.open({
      title: intl.get(`${modelPrompt}.thawing`).d('解冻原因'),
      children: (
        <Form dataSet={modalDs}>
          <TextField name="reason" />
        </Form>
      ),
      onOk: async () => {
        const flag = modalDs.validate();
        if(!flag){
          return false;
        }
        const params = selectObjs.map((item) => {
          return {
            ...item,
            reason: modalDs.current?.get('reason'),
            status: 'N',
          }
        })
        const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-freeze-records/insert`, {
          method: 'POST',
          body: params,
        });
        const res = getResponse(response);
        if (res) {
          notification.success();
          dataSet.query(props.dataSet.currentPage);
        }
        return response;
      },
    })
  };

  // 冻结
  const handleFreeze = async () => {
    if(!dataSet.selected.length){
      notification.error({
        message: '请勾选数据',
      })
      return
    }
    const flag = dataSet.selected.every(item => item.get('freezeStatus') !== 'Y');
    if(!flag){
      notification.error({
        message: '只能冻结未冻结的数据',
      });
      return;
    }
    modalDs.reset();
    Modal.open({
      title: intl.get(`${modelPrompt}.freeze`).d('冻结原因'),
      children: (
        <Form dataSet={modalDs}>
          <TextField name="reason" />
        </Form>
      ),
      onOk: async () => {
        const flag = modalDs.validate();
        if(!flag){
          return false;
        }
        const params = selectObjs.map((item) => {
          return {
            ...item,
            reason: modalDs.current?.get('reason'),
            status: 'Y',
          }
        })
        const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-freeze-records/insert`, {
          method: 'POST',
          body: params,
        });
        const res = getResponse(response);
        if (res) {
          notification.success();
          dataSet.query(props.dataSet.currentPage);
        }
        return response;
      },
    })
  };

  // 批量解冻
  const handleBatchThawing = async () => {
    if(!dataSet.queryDataSet || !dataSet.queryDataSet.current || !dataSet.queryDataSet.current.get('freezeStatus')){
      notification.error({
        message: '请输入冻结解冻状态及其他查询条件！',
      })
      return;
    }
    const queryParams = dataSet.queryDataSet.current.toJSONData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    const flag = dataSet.queryDataSet.current.get('freezeStatus') === 'Y';
    if(!flag){
      notification.error({
        message: '只能解冻已冻结的数据',
      });
      return;
    }
    modalDs.reset();
    Modal.open({
      title: intl.get(`${modelPrompt}.thawing`).d('解冻原因'),
      children: (
        <Form dataSet={modalDs}>
          <TextField name="reason" />
        </Form>
      ),
      onOk: async () => {
        const flag = modalDs.validate();
        if(!flag){
          return false;
        }
        const params = {
          ...queryParams,
          reason: modalDs.current?.get('reason'),
          status: 'N',
        };
        const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-freeze-records/batch/freeze-thaw`, {
          method: 'POST',
          body: params,
        });
        const res = getResponse(response);
        if (res) {
          notification.success();
          dataSet.query(props.dataSet.currentPage);
        }
        return response;
      },
    })
  };

  // 批量冻结
  const handleBatchFreeze = async () => {
    if(!dataSet.queryDataSet || !dataSet.queryDataSet.current || !dataSet.queryDataSet.current.get('freezeStatus')){
      notification.error({
        message: '请输入冻结解冻状态及其他查询条件！',
      })
      return;
    }
    const queryParams = dataSet.queryDataSet.current.toJSONData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    const flag = dataSet.queryDataSet.current.get('freezeStatus') === 'N';
    if(!flag){
      notification.error({
        message: '只能冻结未冻结的数据',
      });
      return;
    }
    modalDs.reset();
    Modal.open({
      title: intl.get(`${modelPrompt}.freeze`).d('冻结原因'),
      children: (
        <Form dataSet={modalDs}>
          <TextField name="reason" />
        </Form>
      ),
      onOk: async () => {
        const flag = modalDs.validate();
        if(!flag){
          return false;
        }
        const params = {
          ...queryParams,
          reason: modalDs.current?.get('reason'),
          status: 'Y',
        };
        const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-freeze-records/batch/freeze-thaw`, {
          method: 'POST',
          body: params,
        });
        const res = getResponse(response);
        if (res) {
          notification.success();
          dataSet.query(props.dataSet.currentPage);
        }
        return response;
      },
    })
  };

  // 指定工艺冻结
  const handleProcessFreezing = async () => {
    if(!dataSet.queryDataSet || !dataSet.queryDataSet.current || !dataSet.queryDataSet.current.get('freezeStatus')){
      notification.error({
        message: '请输入冻结解冻状态及其他查询条件！',
      })
      return;
    }
    const queryParams = dataSet.queryDataSet.current.toJSONData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    const flag = dataSet.queryDataSet.current.get('freezeStatus') === 'N';
    if(!flag){
      notification.error({
        message: '只能冻结未冻结的数据',
      });
      return;
    }
    modalProcessDs.reset();
    Modal.open({
      title: intl.get(`${modelPrompt}.ProcessFreezingReason`).d('指定工艺冻结原因'),
      children: (
        <Form dataSet={modalProcessDs}>
          <Lov name="operationLov" />
          <TextField name="reason" />
          <TextField name='workOrderNum' />
        </Form>
      ),
      onOk: async () => {
        const flag = modalProcessDs.validate();
        if(!flag){
          return false;
        }
        const params = {
          // 没勾选的时候传查询条件；
          ...queryParams,
          // 勾选的时候，传勾选的行；
          specifyCheckList: dataSet.selected.map((ele) => ele.toData()),
          reason: modalProcessDs.current?.get('reason'),
          specifyOperationId: modalProcessDs.current?.get('operationId'),
          workOrderNum: modalProcessDs.current?.get('workOrderNum'),
          status: 'Y',
        };
        const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-freeze-records/batch/freeze/with/operation`, {
          method: 'POST',
          body: params,
        });
        const res = getResponse(response);
        if (res) {
          notification.success();
          dataSet.query(props.dataSet.currentPage);
        }
        return response;
      },
    })
  };

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      dataSet.query();
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: dataSet,
    onOpenInputModal,
  };

  const handleOnSearch = (value, oldValue, dataListName) => {
    const flag = value ? value.every(e => oldValue.includes(e)) : false;
    if (value && value.length > 0 && !flag) {
      const newList = [].concat(...value.map(e => e.split(/[ ]+/)));
      const uniqueList = uniq(oldValue.concat(newList));
      dataSet.queryDataSet.current.set(`${dataListName}`, uniqueList)
    }
  };

  const renderBar = ({ queryFields, buttons, dataSet, queryDataSet }) => {
    if (queryDataSet) {
      const firstFlowFields = queryFields.slice(1, 3);
      const otherFields = queryFields.slice(3, queryFields.length);
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              {/* <Select
                name="identifications"
                multiple
                combo
                onChange={(value, oldValue) => handleOnSearch(value, oldValue, 'identifications')}
              /> */}
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'identifications', '条码号', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              {firstFlowFields}
            </Form>
            {expandForm && (
              <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
                {otherFields}
              </Form>
            )}
          </Col>
          <Col span={6}>
            <div
              style={{
                flexShrink: 0,
                display: 'flex',
                alignItems: 'center',
                marginTop: '0.04rem',
              }}
            >
              <Button onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起查询')
                  : intl.get(`hzero.common.button.viewMore`).d('更多查询')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button
                onClick={() => {
                  dataSet.query();
                }}
                color='primary'
              >
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('在制品批量冻结解冻')}>
        <Button
          disabled={!dataSet.selected.length}
          onClick={handleThawing}
        >
          {intl.get(`${modelPrompt}.thawing`).d('解冻')}
        </Button>
        <Button
          disabled={!dataSet.selected.length}
          onClick={handleFreeze}
        >
          {intl.get(`${modelPrompt}.freeze`).d('冻结')}
        </Button>
        <Button
          onClick={handleBatchThawing}
        >
          {intl.get(`${modelPrompt}.thawing`).d('批量解冻')}
        </Button>
        <Button
          onClick={handleBatchFreeze}
        >
          {intl.get(`${modelPrompt}.freeze`).d('批量冻结')}
        </Button>
        <Button
          onClick={handleProcessFreezing}
        >
          {intl.get(`${modelPrompt}.processFreezing`).d('指定工艺冻结')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={renderBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={dataSet}
          columns={columns}
          searchCode="ExecuteList"
          customizedCode="ExecuteList"
        />
        <LovModal  {...lovModalProps} />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['work.in.progress.freeze.thawe', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...tableDS() });
      const modalDs = new DataSet({ ...modalDS() });
      const modalProcessDs = new DataSet({ ...modalProcessDS() });
      const inputLovDS = new DataSet(InputLovDS());
      return {
        dataSet,
        modalDs,
        modalProcessDs,
        inputLovDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )((ExecuteList)),
);
