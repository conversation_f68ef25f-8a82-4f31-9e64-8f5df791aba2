/**
 * @Description: 检验项目组维护明细界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:55:10
 * @LastEditTime: 2023-05-18 16:50:37
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import intl from 'utils/intl';
import request from 'utils/request';
import { Header, Content } from 'components/Page';
import { DataSet, Form, TextField, Select, Spin, Lov } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

import { DetailFormDS, DetailTableDS } from '../stories';
import InspectItemTab from './InspectItemTab';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.prodlineReview.prodlineReviewTemp';
const { Panel } = Collapse;

const ProdlineTemplateDetailProdlineTemplateDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    customizeForm,
    customizeTable,
    custConfig,
  } = props;

  const [canEdit, setCanEdit] = useState(false);
  const [formObj, setFormObj] = useState(Object);
  const [loading, setLoading] = useState(false);
  const formDS = useMemo(() => new DataSet(DetailFormDS()), []);
  const tableDS = useMemo(() => new DataSet(DetailTableDS()), []);

  useEffect(() => {
    if (id === 'create') {
      request(`/tznm/v1/${tenantId}/mt-mod-site/user-organization/site/lov/ui?page=0&size=10`, {
        method: 'GET',
      }).then(res => {
        if (res && res.content && res.content.length === 1) {
          formDS.loadData([
            {
              status: 'NEW',
              siteId: res.content[0].siteId,
              siteCode: res.content[0].siteCode,
              siteName: res.content[0].siteName,
            },
          ]);
        } else {
          formDS.loadData([{ status: 'NEW' }]);
        }
      });
      setCanEdit(true);
      return;
    }
    initPageData();
  }, [id]);

  // 获取查询数据
  const initPageData = async () => {
    setLoading(true);
    const params = {
      qisProdlineReviewTmpId: id,
    };
    return request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-review-tmps/query/header/and/line/for/ui`,
      {
        method: 'GET',
        query: params,
      },
    ).then(res => {
      setLoading(false);
      if (res && !res.failed) {
        const { lines } = res;
        setFormObj(res.header);
        formDS.loadData([res.header]);
        tableDS.loadData(lines);
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  // 保存
  const handleSave = async () => {
    // flag为true保存后保留界面，为false保存后提示是否新建下一条
    const formValidate = await formDS.validate();
    // if (tableDS.toData().length === 0) {
    //   notification.error({
    //     message: `最少新建一条行数据！`,
    //   });
    //   return Promise.resolve(false);
    // }
    const tableValidate = await tableDS.validate();
    if (!formValidate || !tableValidate) {
      return Promise.resolve(false);
    }
    const formData = formDS.toData()[0] as any;
    const qisVerificationTempTaskList = [] as object[];
    const delTaskIds = [] as number[];
    tableDS.toData().forEach((item: any) => {
      qisVerificationTempTaskList.push(item);
    });
    tableDS.toJSONData().forEach((item: any) => {
      if (item._status === 'delete') {
        delTaskIds.push(item.prodReviewTmpElementId);
      }
    });

    const inspectGroupItemIds = [];
    tableDS.toData().forEach((item: any) => {
      if (item?.prodReviewTmpElementId && item?.prodReviewTmpElementId > 0)
        // @ts-ignore
        inspectGroupItemIds.push(item.prodReviewTmpElementId);
    });
    const qisVerificationTempTaskFinalList = qisVerificationTempTaskList.map((item, index) => {
      return {
        ...item,
        elementNum: (index + 1) * 10,
      };
    });

    const params = {
      header: formData,
      deleteProdReviewTmpElementIds: delTaskIds,
      lines: qisVerificationTempTaskFinalList,
    };
    setLoading(true);
    return request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-review-tmps/save/data/for/ui`,
      {
        method: 'POST',
        body: params,
      },
    ).then(res => {
      if (res && !res.failed) {
        setCanEdit(prev => !prev);
        notification.success({});
        if (id === 'create') {
          props.history.push(`/hwms/prodline_template_management_platform/dist/${res}`);
        } else {
          initPageData();
        }
      } else {
        notification.error({ message: res.message });
        return false;
      }
      setLoading(false);
    });
  };

  const handleSaveSubmit = async () => {
    // flag为true保存后保留界面，为false保存后提示是否新建下一条
    const formValidate = await formDS.validate();
    if (tableDS.toData().length === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.notification.createOneData`).d('最少新建一条行数据！'),
      });
      return Promise.resolve(false);
    }
    const tableValidate = await tableDS.validate();
    if (!formValidate || !tableValidate) {
      return Promise.resolve(false);
    }
    const formData = formDS.toData()[0] as any;
    const qisVerificationTempTaskList = [] as object[];
    const delTaskIds = [] as number[];
    tableDS.toData().forEach((item: any) => {
      qisVerificationTempTaskList.push(item);
    });
    tableDS.toJSONData().forEach((item: any) => {
      if (item._status === 'delete') {
        delTaskIds.push(item.prodReviewTmpElementId);
      }
    });

    const inspectGroupItemIds = [];
    tableDS.toData().forEach((item: any) => {
      if (item?.prodReviewTmpElementId && item?.prodReviewTmpElementId > 0)
        // @ts-ignore
        inspectGroupItemIds.push(item.prodReviewTmpElementId);
    });

    const qisVerificationTempTaskFinalList = qisVerificationTempTaskList.map((item, index) => {
      return {
        ...item,
        elementNum: (index + 1) * 10,
      };
    });

    const params = {
      header: formData,
      deleteProdReviewTmpElementIds: delTaskIds,
      lines: qisVerificationTempTaskFinalList,
    };
    setLoading(true);
    return request(
      `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-prodline-review-tmps/save/and/submit/data/for/ui`,
      {
        method: 'POST',
        body: params,
      },
    ).then(res => {
      if (res && !res.failed) {
        setCanEdit(prev => !prev);
        notification.success({});
        if (id === 'create') {
          props.history.push(`/hwms/prodline_template_management_platform/dist/${res}`);
        } else {
          initPageData();
        }
      } else {
        notification.error({ message: res.message });
        return false;
      }
      setLoading(false);
    });
  };

  // 取消
  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hwms/prodline_template_management_platform/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  // 检验项目组件传参
  const inspectItemTabProps = {
    path,
    id,
    canEdit,
    customizeTable,
    customizeForm,
    custConfig,
    tableDS,
    loading,
  };

  return (
    <div className="hmes-style" style={{ height: '100%' }}>
      <Header
        title={id === 'create'
          ? intl.get(`${modelPrompt}.title.create`).d('产线审核模板管理新建')
          : intl.get(`${modelPrompt}.title.detail`).d('产线审核模板管理编辑')}
        backPath="/hwms/prodline_template_management_platform"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={() => handleSaveSubmit()}
              permissionList={[
                {
                  code: `hzero.tarzan.hlct.jyzx.prodline_template_management_platform.ps.button.saveSubmit`,
                  type: 'button',
                  meaning: '详情页-保存并提交按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.saveAndSubmit`).d('保存并提交')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={() => handleSave()}
              permissionList={[
                {
                  code: `hzero.tarzan.hlct.jyzx.prodline_template_management_platform.ps.button.save`,
                  type: 'button',
                  meaning: '详情页-保存按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.save`).d('保存')}
            </PermissionButton>
            <PermissionButton
              icon="close"
              onClick={handleCancel}
              permissionList={[
                {
                  code: `hzero.tarzan.hlct.jyzx.prodline_template_management_platform.ps.button.cancelEdit`,
                  type: 'button',
                  meaning: '详情页-取消按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
            </PermissionButton>
          </>
        )}
        {!canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              onClick={() => {
                setCanEdit(prev => !prev);
              }}
              disabled={formObj?.status === 'REVIEWING' || formObj?.status === 'CANCEL'}
              permissionList={[
                {
                  code: `hzero.tarzan.hlct.jyzx.prodline_template_management_platform.ps.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Collapse bordered={false} defaultActiveKey={['BASIC', 'ITEM']}>
            <Panel key="BASIC" header={intl.get(`${modelPrompt}.panel.prodlineReviewTemp`).d('产线审核模板')} dataSet={formDS}>
              <Form dataSet={formDS} columns={3} labelWidth={112} disabled={!canEdit}>
                <TextField name="prodlineReviewTmpNum" disabled />
                <Select name="status" disabled />
                <Select name="reviewType" required />
                <Select name="reviewStage" required />
                <Lov name="siteLov" required />
                <TextField name="creationDate" disabled />
                <TextField name="createdRealName" disabled />
                <TextField name="reviewRemarks" disabled />
                <TextField name="reviewByRealName" disabled />
                <TextField name="reviewDate" disabled />
              </Form>
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.panel.prodlineReviewElement`).d('产线审核模板要素')} key="ITEM" dataSet={tableDS}>
              <InspectItemTab {...inspectItemTabProps} />
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.qms.prodlineTemplateDetailProdlineTemplateDetail', 'tarzan.common'],
})(ProdlineTemplateDetailProdlineTemplateDetail);
