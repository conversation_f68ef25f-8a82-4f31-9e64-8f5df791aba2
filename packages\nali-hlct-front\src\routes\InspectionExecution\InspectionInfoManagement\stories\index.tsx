/**
 * @Description: 检验业务类型规则维护-列表页DS
 * @Author: <EMAIL>
 * @Date: 2023/1/30 15:46
 */
import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { FieldProps } from 'choerodon-ui/dataset/data-set/Field';

const modelPrompt = 'tarzan.hwms.inspectionInfoManagement';
const tenantId = getCurrentOrganizationId();

const statusOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui`,
        method: 'GET',
        params: { statusGroup: 'INSPECT_INFO_STATUS', tenantId },
      };
    },
  },
});

const fields: () => FieldProps[] = () => [
  {
    name: 'inspectInfoCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.inspectInfoCode`).d('报检信息编码'),
  },
  {
    name: 'dealStatus',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.dealStatus`).d('处理状态'),
    lookupCode: 'MT.QMS.MESSAGE.DEAL_STATUS',
  },
  {
    name: 'dealMessage',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.dealMessage`).d('处理消息'),
  },
  {
    name: 'siteCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
  },
  {
    name: 'inspectBusinessTypeDesc',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
  },
  {
    name: 'inspectDocReleasedFlag',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.inspectDocReleasedFlag`).d('检验单下达标识'),
  },
  {
    name: 'inspectInfoStatusDesc',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.inspectInfoStatus`).d('状态'),
  },
  {
    name: 'urgentFlag',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
    lookupCode: 'MT.FLAG_YN',
  },
  {
    name: 'quantity',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.quantity`).d('报检数量'),
  },
  {
    name: 'uomName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.uomName`).d('单位'),
  },
  {
    name: 'materialCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
  },
  {
    name: 'materialName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
  },
  {
    name: 'revisionCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
  },
  {
    name: 'woNumber',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.order`).d('工单'),
  },
  {
    name: 'sourceObjectTypeDesc',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源单据类型'),
  },
  {
    name: 'sourceNumber',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.sourceNumber`).d('来源单据编码#来源单据行号'),
  },
  {
    name: 'sourceObjectLineCode',
    type: FieldType.string,
  },
  {
    name: 'inspectionDocNumber',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.inspectionDocNumber`).d('检验单号'),
  },
  {
    name: 'inspectInfoUserName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.applicant`).d('报检人'),
  },
  {
    name: 'inspectInfoLot',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.inspectInfoLot`).d('报检批次'),
  },
  {
    name: 'inspectInfoCreationDate',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.inspectInfoCreationDate`).d('报检时间'),
  },
  {
    name: 'supplierName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
  },
  {
    name: 'customerName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.customer`).d('客户'),
  },
  {
    name: 'areaName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.areaLov`).d('区域'),
  },
  {
    name: 'prodLineName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.proLine`).d('产线'),
  },
  {
    name: 'operationName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.technics`).d('工艺'),
  },
  {
    name: 'processWorkcellName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.procedure`).d('工序'),
  },
  {
    name: 'stationWorkcellName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.station`).d('工位'),
  },
  {
    name: 'equipmentCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.equipmentCode`).d('设备'),
  },
  {
    name: 'locatorName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
  },
  {
    name: 'shiftTeamCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.shiftTeamCode`).d('班组'),
  },
  {
    name: 'shiftDate',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.shiftDate`).d('班次日期'),
  },
  {
    name: 'shiftCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
  },
  {
    name: 'poHeadLineNumber',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.poHeadLineNumber`).d('采购订单号#行号'),
  },
  {
    name: 'poLineNumber',
    type: FieldType.string,
  },
  {
    name: 'soHeadLineNumber',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.soNumber`).d('销售订单号#行号'),
  },
  {
    name: 'soLineNumber',
    type: FieldType.string,
  },
  {
    name: 'infoCreateMethodDesc',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.infoCreateMethod`).d('报检原因'),
  },
  {
    name: 'remark',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.remark`).d('备注'),
  },
];

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'inspectInfoCodes',
      multiple: true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfoCode`).d('报检信息编码'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.USER_SITE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      // lovPara: {
      //   tenantId,
      // },
      computedProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'inspectBusinessType',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'inspectInfoStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfoStatus`).d('状态'),
      options: statusOptionDs,
      textField: 'description',
      valueField: 'statusCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
      ignore: FieldIgnore.always,
      multiple: true,
      lovCode: 'MT.MATERIAL',
      textField: 'materialName',
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },

    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'sourceObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源单据类型'),
      lookupCode: 'MT.SOURCE_OBJECT_TYPE',
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectCode`).d('来源单据号'),
    },
    {
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('报检时间从'),
      name: 'creationDateFrom',
      type: FieldType.date,
      max: 'creationDateTo',
    },
    {
      label: intl.get(`${modelPrompt}.creationDateTo`).d('报检时间至'),
      name: 'creationDateTo',
      type: FieldType.date,
      min: 'creationDateFrom',
    },
    {
      name: 'inspectInfoLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectInfoLot`).d('报检批次'),
    },
    {
      name: 'inspectInfoUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.applicant`).d('报检人'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      lovPara: {
        tenantId,
      },
      textField: 'realName',
    },
    {
      name: 'inspectInfoUserId',
      bind: 'inspectInfoUserLov.id',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'supplierIds',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customer`).d('客户'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      multiple: true,
      textField: 'customerName',
    },
    {
      name: 'customerIds',
      bind: 'customerLov.customerId',
    },
    {
      name: 'woNumberLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.order`).d('工单'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.WORK_ORDER',
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },
    {
      name: 'woNumbers',
      bind: 'woNumberLov.workOrderNum',
    },
    {
      name: 'areaLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLov`).d('区域'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.AREA',
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },
    {
      name: 'areaIds',
      bind: 'areaLov.areaId',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.proLine`).d('产线'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },
    {
      name: 'prodLineIds',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.technics`).d('工艺'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.OPERATION',
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },
    {
      name: 'operationIds',
      bind: 'operationLov.operationId',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.procedure`).d('工序'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.WORKCELL',
      multiple: true,
      textField: 'workcellName',
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
          workcellType: 'PROCESS',
        }),
      },
    },
    {
      name: 'processWorkcellIds',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'stationWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.station`).d('工位'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.WORKCELL',
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
          workcellType: 'STATION',
        }),
      },
    },
    {
      name: 'stationWorkcellIds',
      bind: 'stationWorkcellLov.workcellId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.LOCATOR',
      multiple: true,
      textField: 'locatorName',
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },
    {
      name: 'locatorIds',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectionDocNumber`).d('检验单号'),
    },
    {
      name: 'materialLot',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLot`).d('物料批'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MATERIAL_LOT',
      textField: 'materialLotCode',
      multiple: true,
      lovPara: { tenantId },
    },
    {
      name: 'materialLotCodes',
      bind: 'materialLot.materialLotCode',
    },
    {
      name: 'eoLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.execute`).d('执行作业'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.EO',
      multiple: true,
      lovPara: { tenantId },
    },
    {
      name: 'eoCodes',
      bind: 'eoLov.eoNum',
    },
  ],
  fields: fields(),
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-info/list/ui`,
        method: 'GET',
      };
    },
  },
});

const messageDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: fields(),
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-info-wait/list/ui`,
        method: 'GET',
      };
    },
  },
});

const detailFields: () => FieldProps[] = () => [
  {
    name: 'objectTypeDesc',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.objectType`).d('报检对象类型'),
  },
  {
    name: 'objectCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.objectCode`).d('报检对象编码'),
  },
  {
    name: 'materialCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
  },
  {
    name: 'materialName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
  },
  {
    name: 'revisionCode',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
  },
  {
    name: 'quantity',
    type: FieldType.number,
    label: intl.get(`${modelPrompt}.quantity`).d('报检数量'),
  },
  {
    name: 'uomName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.uomName`).d('单位'),
  },
  {
    name: 'locatorName',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
  },
  {
    name: 'supplierLot',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
  },
  {
    name: 'lot',
    type: FieldType.string,
    label: intl.get(`${modelPrompt}.lot`).d('厂内批次'),
  },
];

const detailTableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectBusinessTypeRuleId',
  fields: detailFields(),
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-info/info/detail/ui`,
        method: 'GET',
      };
    },
  },
});

const messageDetailDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectBusinessTypeRuleId',
  fields: detailFields(),
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-info-line-wait/info/detail/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS, messageDS, detailTableDS, messageDetailDS };
