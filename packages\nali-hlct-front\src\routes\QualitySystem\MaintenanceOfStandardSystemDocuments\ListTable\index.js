/*
 * @Description: 
 * @Author: <<EMAIL>>
 * @Date: 2023-12-04 14:09:25
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-04 14:13:29
 */
/**
 * @Description: 标准体系文件维护-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-06-27
 * @LastEditTime: 2022-06-28
 * @LastEditors: <<EMAIL>>
 */
import React, {useMemo } from 'react';
import {
  DataSet,
  Table,
  Button,
} from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { tableDS } from '../stores/MaintenanceOfStandardSystemDocumentsTableDS';

const MaintenanceOfStandardSystemDocumentsTable = (props) => {

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  /** @type {ColumnProps}  行列 */
  const columns = [
    {
      name: 'templateFileCode', renderer: ({ record }) => {
        return <a onClick={() => jumpToDetail(record)}>{record.get('templateFileCode')}</a>;
      },
    },
    { name: 'templateFileName' },
    {
      name: 'statusName', renderer: ({ record }) => (
        <Badge
          status={record.get('statusName') === '启用' ? 'success' : 'error'}
          text={
            record.get('statusName') === '启用'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    { name: 'remark' },
    { name: 'createdByName' },
    { name: 'creationDate' },
  ];
  const jumpToDetail = (record) => {
    props.history.push({
      pathname: `/hwms/maintenance-of-standard-systemDocuments/dist/${record.get('templateFileId')}`,
    });
  };

  return (
    <div style={{ height: '100%' }} className="hmes-style">
      <Header title={intl.get('key.hwms.front.MaintenanceOfStandardSystemDocuments.title').d('标准体系文件维护')}>
        <>
          <Button
            style={{
              backgroundColor: '#0840f8',
              color: 'white',
            }}
            icon="add"
            onClick={() => {
              props.history.push({
                pathname: `/hwms/maintenance-of-standard-systemDocuments/dist/create`,
              });
            }}
          >
            {intl.get('key.hwms.front.MaintenanceOfStandardSystemDocuments.button.create').d('新建')}
          </Button>
        </>
      </Header>
      <Content>
        <Table
          customizable
          customizedCode="ProductionLineAuditPlanManagementPlatformTable"
          queryBar={TableQueryBarType.filterBar}
          dataSet={tableDs}
          className="MainTable"
          columns={columns}
          queryFieldsLimit={3}
          pagination={{ showPager: true }}
          queryBarProps={{ autoQueryAfterReset: false }}
          rowHeight={35}
          border
        />
      </Content>
    </div >
  )
};
export default formatterCollections({
  code: ['key.hwms.front.MaintenanceOfStandardSystemDocuments', 'tarzan.common'],
})(MaintenanceOfStandardSystemDocumentsTable);