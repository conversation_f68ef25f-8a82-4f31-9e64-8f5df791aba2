/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-24 09:21:22
 * @LastEditTime: 2023-07-28 17:05:19
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Tabs,
  Button,
  Form,
  Lov,
  TextField,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import uuid from 'uuid/v4';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanS<PERSON> } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { fetchDefaultSite } from '@services/api';
import AssociatedFilesTable from './AssociatedFilesTable';
import AssociatedObjectsTab from './AssociatedObjectsTab';
import { detailDS, fileTableDS } from '../stores/DetailDS';
import { assObjectsDS, assObjectsRemoveDS } from '../stores/AssociatedObjectDS';
import { InitAttachmentUUID, FetchWorkingInstruction, SaveWorkingInstruction } from '../services';

const { Panel } = Collapse;
const TabPane = Tabs.TabPane;
const modelPrompt = 'hmes.workingInstructionMaintenance';

const WorkingInstructionMaintenanceDetail = (props) => {
  const {
    history,
    match: { path, params },
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState(false);
  const [siteLoading, setSiteLoading] = useState(false); // 获取默认站点的loading
  const [fileUploadLoading, setFileUploadLoading] = useState(false);
  const [attachmentUUID, setAttachmentUUID] = useState('');
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const fileTableDs = useMemo(() => new DataSet(fileTableDS()), []);
  const assObjectsDs = useMemo(() => new DataSet(assObjectsDS()), []);
  const assObjectsRemoveDs = useMemo(() => new DataSet(assObjectsRemoveDS()), []);

  const { run: fetchOrkingInstruction, loading: fetchLoading } = useRequest(FetchWorkingInstruction(), { manual: true, needPromise: true });
  const { run: saveWorkingInstruction, loading: saveLoading } = useRequest(SaveWorkingInstruction(), { manual: true, needPromise: true });
  const initAttachmentUUID = useRequest(InitAttachmentUUID(), { manual: true, needPromise: true });

  useEffect(() => {
    if (kid === 'create') {
      // 新建时
      setCanEdit(true);
      setSiteLoading(true);
      initAttachmentUUID.run({}).then(res => {
        if (res.content) {
          setAttachmentUUID(res.content)
        }
      })
      fetchDefaultSite().then(res => {
        if (res && res.success) {
          detailDs.current?.set('siteLov', {
            ...res.rows,
          })
          const _object = props.location.state || {};
          if (_object.form === 'operation') {
            // 从工艺维护跳转过来
            assObjectsDs.loadData([{
              lineNumber: 10,
              tagGroupObjectId: -Date.parse(String(new Date())),
              detailList: [{
                objectCode: _object.operationName,
                objectDesc: _object.description,
                objectId: _object.operationId,
                objectRevision: _object.revision,
                objectType: "OPERATION",
                parentSiteId: res.rows?.siteId,
                uuid: uuid(),
              }],
            }])
          }
        }
      }).finally(() => {
        setSiteLoading(false);
      })
      return;
    }
    // 编辑时
    query();
  }, [kid]);

  const query = useCallback(
    (saveId?) => {
      fetchOrkingInstruction({
        params: {
          sopHeaderId: saveId || kid,
        },
      }).then(res => {
        detailDs.loadData([res.hmeSopHeader])
        setAttachmentUUID(res.hmeSopHeader.attachmentUuid)
        fileTableDs.loadData(res.sopFiles)
        assObjectsDs.loadData(res.sopObjectList)
        assObjectsRemoveDs.loadData([]);
      })
    },
    [kid],
  );

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(
    () => {
      if (kid === 'create') {
        history.push('/hmes/working-instruction-maintenance/list')
      } else {
        setCanEdit(false);
        query();
      }
    },
    [],
  );

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const queryParams = {
      hmeSopHeader: {
        sopHeaderId: kid === 'create' ? null : kid,
        attachmentUuid: attachmentUUID,
        ...detailDs!.current!.toData(),
      },
      sopObjectList: assObjectsDs.toData().concat(assObjectsRemoveDs.data.map(item => {
        return {
          ...item.toData(),
          _status: 'delete',
        }
      })),
      sopFiles: fileTableDs.toData().concat(fileTableDs.destroyed.map(item => {
        return {
          ...item.toData(),
          _status: 'delete',
        }
      })),
    };
    const res = await saveWorkingInstruction({ params: { ...queryParams } });
    if (res && !res.failed) {
      notification.success({});
      setCanEdit(false);
      history.push(`/hmes/working-instruction-maintenance/detail/${res}`);
      query(res);
      return true;
    }
    return false;
  }

  return (
    <div className='hmes-style'>
      <TarzanSpin dataSet={detailDs} spinning={siteLoading || fetchLoading || saveLoading || fileUploadLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.list`).d('作业指导书管理')}
          backPath='/hmes/working-instruction-maintenance/list'
        >
          {canEdit ? (
            <>
              <Button
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button
                icon="close"
                onClick={handleCancel}
              >
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'basic',
            ]}
          >
            <Panel
              key="basic"
              header={intl.get(`${modelPrompt}.title.basic`).d('作业指导书')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <Lov name='siteLov' />
                <TextField name='sopCode' />
                <TextField name='sopName' />
                <DateTimePicker name='startDate' />
                <DateTimePicker name='endDate' />
                <TextField name='remark' />
              </Form>
            </Panel>
          </Collapse>
          <Tabs defaultActiveKey="tabKey1">
            <TabPane tab={intl.get(`${modelPrompt}.tabName1`).d('关联文件')} key="tabKey1" forceRender>
              <AssociatedFilesTable attachmentUUID={attachmentUUID} canEdit={canEdit} ds={fileTableDs} setFileUploadLoading={setFileUploadLoading} />
            </TabPane>
            <TabPane tab={intl.get(`${modelPrompt}.tabName2`).d('关联对象')} key="tabKey2" forceRender>
              <AssociatedObjectsTab canEdit={canEdit} detailDs={detailDs} assObjectsDs={assObjectsDs} assObjectsRemoveDs={assObjectsRemoveDs}/>
            </TabPane>
          </Tabs>
        </Content>
      </TarzanSpin>
    </div>
  )
};


export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(WorkingInstructionMaintenanceDetail);
