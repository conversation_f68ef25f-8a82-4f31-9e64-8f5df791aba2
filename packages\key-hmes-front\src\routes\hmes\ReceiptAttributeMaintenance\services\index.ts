/**
 * @Description: 入库属性维护-services
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const tenantId = getCurrentOrganizationId();

// 入库属性维护-删除消息
export function DeleteMessage() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-in-warehouses/delete/ui`,
    method: 'POST',
  };
}

// 入库属性维护-保存消息
export function SaveMessage() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-in-warehouses/update/ui`,
    method: 'POST',
  };
}
