/**
 * @Description: 量具型号与MSA分析质量特性关系维护-主界面
 * @Author: <EMAIL>
 * @Date: 2023/8/16 14:49
 */
import React, { useMemo, useState } from 'react';
import { Button, DataSet, Lov, Switch, Table } from 'choerodon-ui/pro';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import notification from 'utils/notification';
import { Badge } from 'choerodon-ui';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { isNil } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores';
import { GetDefaultSite, SaveMsaRelation } from '../services';

const modelPrompt = 'tarzan.modelManagement.modelMsaRelation';
const tenantId = getCurrentOrganizationId();

const ModelMsaRelationList = props => {
  const { tableDs } = props;
  const [canEdit, setCanEdit] = useState(false);
  // 用户默认站点
  const { data: defaultSiteInfo } = useRequest(GetDefaultSite());
  const { run: saveMsaRelation } = useRequest(SaveMsaRelation(), {
    manual: true,
  });

  useDataSetEvent(tableDs, 'query', () => setCanEdit(false));

  const handleCancel = () => {
    setCanEdit(false);
    tableDs.reset();
  };

  const handleSave = async () => {
    return new Promise(async resolve => {
      const valRes = await tableDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      saveMsaRelation({
        params: tableDs.toData(),
        onSuccess: () => {
          setCanEdit(false);
          notification.success({});
          tableDs.query(tableDs.currentPage);
          return resolve(true);
        },
        onFailed: () => {
          return resolve(false);
        },
      });
    });
  };

  const handleChangeSite = record => {
    record.set('modelLov', undefined);
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'siteLov',
        editor: record => canEdit && <Lov onChange={() => handleChangeSite(record)} />,
      },
      {
        name: 'modelLov',
        editor: canEdit,
      },
      { name: 'modelName' },
      { name: 'speciesName' },
      {
        name: 'qualityCharacteristic',
        editor: canEdit,
      },
      {
        name: 'msaAnalysisMethod',
        width: 250,
        editor: canEdit,
      },
      {
        name: 'completeTimeLimit',
        editor: canEdit,
        width: 150,
      },
      {
        name: 'enableFlag',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
        editor: () => canEdit && <Switch />,
      },
      {
        name: 'fileUuid',
        editor: () => canEdit,
      },
    ];
  }, [canEdit]);

  const handleAdd = () => {
    tableDs.create(
      {
        siteId: defaultSiteInfo.siteId,
        siteName: defaultSiteInfo.siteName,
      },
      0,
    );
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/YP.QIS_MS_MODEL_MSA_REL_IMPORT',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('量具型号与MSA分析质量特性关系维护')}>
        {canEdit ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave()}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="add" onClick={handleAdd}>
              {intl.get(`${modelPrompt}.button.create`).d('新建')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <>
            <Button
              icon="edit-o"
              color={ButtonColor.primary}
              loading={false}
              onClick={() => setCanEdit(true)}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Button icon="file_upload" onClick={goImport}>
              {intl.get(`tarzan.common.button.import`).d('导入')}
            </Button>
            <ExcelExport
              exportAsync
              requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-model-msa-rel/export`}
              queryParams={getExportQueryParams}
            >
              {intl.get('tarzan.common.button.export').d('导出')}
            </ExcelExport>
          </>
        )}
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="modelMsaRelationList"
          customizedCode="modelMsaRelationList"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ModelMsaRelationList),
);
