/**
 * @Description: 容器类型维护（重构）-详情页
 * @Author: <EMAIL>
 * @Date: 2022/7/12 10:21
 * @LastEditTime: 2023-05-18 16:09:24
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useEffect } from 'react';
import {
  Button,
  Form,
  TextField,
  NumberField,
  Switch,
  DataSet,
  Lov,
  Select,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { AttributeDrawer } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@/utils/config';
import { detailDS } from '../stores/DetailDS';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hagd.containerType.model.containerType';

const ContainerTypeDetail = props => {
  const {
    history,
    match: {
      path,
      params: { id },
    },
    customizeForm,
    custConfig,
  } = props;
  const detailDs = useMemo(() => new DataSet({ ...detailDS() }), []);
  const [canEdit, setCanEdit] = useState<boolean>(false);
  const { run: handleSaveType, loading: changeSaveLoading } = useRequest(
    {
      url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-container-type/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_DETAIL.BASIC`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      return;
    }
    initPageData();
  }, [props.match.params.id]);

  const initPageData = () => {
    detailDs.setQueryParameter('containerTypeId', id);
    detailDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_DETAIL.BASIC`);
    detailDs.query();
  };

  const handleCancel = () => {
    if (id === 'create') {
      history.push('/hmes/hagd/container-type/list');
    } else {
      setCanEdit(prev => !prev);
      initPageData();
    }
  };

  const handleSave = async () => {
    const validate = await detailDs.validate();
    if (!validate) {
      return;
    }
    return handleSaveType({
      params: {
        ...detailDs.toData()[0],
      },
    }).then(res => {
      if (res && res.success) {
        notification.success({});
        setCanEdit(false);
        if (id === 'create') {
          props.history.push(`/hmes/hagd/container-type/detail/${res.rows}`);
        } else {
          initPageData();
        }
      }
    });
  };

  // 位置管理变化的回调
  const handleLocationChange = () => {
    detailDs.current!.init('locationRow', undefined);
    detailDs.current!.init('locationColumn', undefined);
  };

  // 尺寸单位变化的回调
  const handleSizeUomChange = () => {
    detailDs.current!.init('length', undefined);
    detailDs.current!.init('width', undefined);
    detailDs.current!.init('height', undefined);
  };

  // 重量单位变化的回调
  const handleWeightUomChange = () => {
    detailDs.current!.init('weight', undefined);
    detailDs.current!.init('maxLoadWeight', undefined);
  };

  const processNumber = (number: number, processMode: string, decimalPlaces: number): number => {
    // 四舍五入
    if (processMode === 'ROUND') {
      return Math.round(number * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
    }
    // 去尾法
    else if (processMode === 'ROUND_DOWN') {
      return Math.floor(number * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
    }
    // 进一法
    else if (processMode === 'ROUND_UP') {
      return Math.ceil(number * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
    }
    // 其他情况，直接返回原值
    else {
      return number;
    }
  }

  // 尺寸单位的小数位和值的处理
  const handleSizeChange = (detailDs, value, name) => {
    const { sizeDecimalNumber, sizeProcessMode } = detailDs.toData()[0];
    if (sizeProcessMode === 'ROUND') {// 四舍五入
      detailDs.current.set(name, processNumber(value, 'ROUND', sizeDecimalNumber));
    }else if (sizeProcessMode === 'ROUND_DOWN') {// 去尾法
      detailDs.current.set(name, processNumber(value, 'ROUND_DOWN', sizeDecimalNumber));
    }else if (sizeProcessMode === 'ROUND_UP') {// 进一法
      detailDs.current.set(name, processNumber(value, 'ROUND_UP', sizeDecimalNumber));
    }else {// 其他情况，直接返回原值
      detailDs.current.set(name, value);
    }
  };

  // 重量单位的小数位和值的处理
  const handleWeightChange = (detailDs, value, name) => {
    const { weightDecimalNumber, weightProcessMode } = detailDs.toData()[0];
    if (weightProcessMode === 'ROUND') {// 四舍五入
      detailDs.current.set(name, processNumber(value, 'ROUND', weightDecimalNumber));
    }else if (weightProcessMode === 'ROUND_DOWN') {// 去尾法
      detailDs.current.set(name, processNumber(value, 'ROUND_DOWN', weightDecimalNumber));
    }else if (weightProcessMode === 'ROUND_UP') {// 进一法
      detailDs.current.set(name, processNumber(value, 'ROUND_UP', weightDecimalNumber));
    }else {// 其他情况，直接返回原值
      detailDs.current.set(name, value);
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`tarzan.hagd.containerType.title.containerTypeMaintain`).d('容器类型维护')}
        backPath="/hmes/hagd/container-type/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              loading={changeSaveLoading}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`tarzan.common.button.save`).d('保存')}
            </PermissionButton>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`tarzan.common.button.edit`).d('编辑')}
          </PermissionButton>
        )}
        <AttributeDrawer
          serverCode={BASIC.HWMS_BASIC}
          className="org.tarzan.mes.domain.entity.MtContainerType"
          kid={id}
          canEdit={canEdit}
          disabled={id === 'create'}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_DETAIL.BUTTON`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['baseInfo', 'sizeFormInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.basicAttr`).d('基础属性')}
            key="baseInfo"
            dataSet={detailDs}
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_DETAIL.BASIC`,
              },
              <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={3}>
                <TextField name="containerTypeCode" />
                <TextField name="containerTypeDescription" />
                <Select name="packingLevel" />
                <Switch name="locationEnabledFlag" onChange={handleLocationChange} />
                <NumberField name="locationRow" />
                <NumberField name="locationColumn" />
                <Switch name="enableFlag" />
                <Select name="forbidFlagValues" multiple />
                <Select name="containerClassification" />
              </Form>,
            )}
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.size`).d('规格尺寸')}
            key="sizeFormInfo"
            dataSet={detailDs}
          >
            <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={4}>
              <Lov name="sizeUomLov" onChange={handleSizeUomChange} />
              <NumberField name="length" onChange={(value) => handleSizeChange(detailDs, value, 'length')} />
              <NumberField name="width" onChange={(value) => handleSizeChange(detailDs, value, 'width')} />
              <NumberField name="height" onChange={(value) => handleSizeChange(detailDs, value, 'height')} />
              <Lov name="weightUomLov" onChange={handleWeightUomChange} />
              <NumberField name="weight" onChange={(value) => handleWeightChange(detailDs, value, 'weight')} />
              <NumberField name="maxLoadWeight" onChange={(value) => handleWeightChange(detailDs, value, 'maxLoadWeight')} />
              <NumberField name="capacityQty" onChange={(value) => handleWeightChange(detailDs, value, 'capacityQty')} />
            </Form>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hagd.containerType', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_TYPE_DETAIL.BUTTON`],
  // @ts-ignore
})(ContainerTypeDetail));
