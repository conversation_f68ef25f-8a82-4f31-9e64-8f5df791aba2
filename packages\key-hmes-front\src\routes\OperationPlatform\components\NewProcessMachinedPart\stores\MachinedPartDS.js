// 加工件（在制品标识）DS
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.hmes.ProcessWorkorderMachinedPart';

const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('在制标识'),
    },
    {
      name: 'opProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.opProcess`).d('工序进度'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      // required: true,
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'customerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerDesc`).d('客户信息'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('工单备注'),
    },
    {
      name: 'standardBeat',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.standardBeat`).d('标准节拍'),
    },
    {
      name: 'identificationField',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identificationField`).d('在制品标识'),
    },
    {
      name: 'pitStopDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.pitStopDate`).d('进站时间'),
    },
    {
      name: 'currentProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentProcess`).d('当前工序'),
    },
    {
      name: 'nextProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.nextProcess`).d('下一工序'),
    },
    {
      name: 'processedRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('调度备注'),
    },
    {
      name: 'processedTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processedTime`).d('加工时长'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.workOrderObj`).d('生产工单'),
    },
    {
      name: 'woQtyInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.woQtyInfo`).d('工单数量'),
    },
    {
      name: 'eoQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoQty`).d('在制数量'),
    },
  ],
});

const tableDs = () => ({
  primaryKey: 'sequence',
  selection: 'single',
  fields: [
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('当前工艺名称'),
    },
    {
      name: 'nextOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.nextOperationName`).d('下一工艺名称'),
    },
    {
      name: 'routerStepName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerStepName`).d('后续步骤名称'),
    },
  ],
});

export { detailDS, tableDs };
