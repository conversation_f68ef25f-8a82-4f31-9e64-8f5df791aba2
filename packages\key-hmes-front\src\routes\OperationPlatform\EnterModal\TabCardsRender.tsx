import React from 'react';
import styles from './index.module.less';

const TabCardsRender = ({ cardsList, selectedCardId, primaryCardId, cardCode, cardName, handleSelectCard }) => {
  return (
    <div className={styles.tabContent}>
      {
        cardsList.map(item => {
          let _className;
          const _cardId = String(item[primaryCardId])
          if (selectedCardId === _cardId) {
            _className = styles.previewImgCardSelected
          } else {
            _className = styles.previewImgCard
          }
          return (
            <div
              className={_className}
              onClick={() => handleSelectCard(item)}
              style={{ height: cardCode ? '50px' : '25px' }}
            >
              {item[cardCode] && (<div className={styles.cardName}>
                {item[cardCode]}
              </div>)}
              <div className={styles.cardName}>
                {item[cardName]}
              </div>
              {
                selectedCardId === _cardId && (
                  <div className={styles.cardSelect}>
                    <div className={styles.squareOne}>
                      <div className={styles.squareTwo}></div>
                    </div>
                  </div>
                )
              }
              {/* {
                tempCardMode === 'Independent' && primaryCardId === _cardId && (
                  <div className={styles.cardSelect2}>
                    <div className={styles.squareOne}>
                      <div className={styles.squareTwo2}></div>
                    </div>
                  </div>
                )
              } */}
            </div>
          )
        })
      }
    </div>
    // <Row>
    //   {item.length && item.map(listItem => {
    //     if (!listItem.mainChecked) {
    //       return (
    //         <Col span={5}>
    //           <div
    //             onClick={() => handleSelect(listItem, listItem?.checked)}
    //             style={{ border: !listItem?.checked ? 'none' : '2px solid rgba(75, 227, 238, 1)' }}
    //             className={styles.previewImgCard}
    //           >
    //             <div className={styles.previewImgDiv} style={{ background: `url(${listItem.cardPreviewUrl})` }}></div>
    //             <div className={styles.cardName}>
    //               {listItem.cardName}
    //               {
    //                 currentRecord.cardId === listItem.cardId && listItem.checked && '(当前选择)'
    //               }
    //             </div>
    //             {
    //               !!listItem?.checked && (
    //                 <div className={styles.cardSelect}>
    //                   <div className={styles.squareOne}>
    //                     <div className={styles.squareTwo}></div>
    //                   </div>
    //                 </div>
    //               )
    //             }
    //           </div>
    //           <br />
    //         </Col>
    //       );
    //     }
    //     return (
    //       <Col span={5}>
    //         <div
    //           onClick={() => handleSelect(listItem, listItem?.checked)}
    //           style={{ border: !listItem?.checked ? 'none' : '2px solid rgba(255, 216, 110, 1)' }}
    //           className={styles.previewImgCard}
    //         >
    //           <div className={styles.previewImgDiv} style={{ background: `url(${listItem.cardPreviewUrl})` }}></div>
    //           <div className={styles.cardName2}>
    //             {listItem.cardName}
    //             {
    //               currentRecord.cardId === listItem.cardId && '(主卡片)'
    //             }
    //           </div>
    //           {
    //             !!listItem?.checked && (
    //               <div className={styles.cardSelect2}>
    //                 <div className={styles.squareOne}>
    //                   <div className={styles.squareTwo2}></div>
    //                 </div>
    //               </div>
    //             )
    //           }
    //         </div>
    //         <br />
    //       </Col>
    //     );
    //   })}
    // </Row>
  )
}

export default TabCardsRender;
