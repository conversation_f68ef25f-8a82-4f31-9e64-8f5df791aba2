/*
 * @Description: 打印模板配置界面 详情界面
 * @Author: YinWQ
 * @Date: 2023-07-18 10:28:36
 * @LastEditors: 20379 <EMAIL>
 * @LastEditTime: 2024-01-29 11:19:49
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Form, Select, TextField, Table, Lov } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Collapse, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { getResponse } from 'utils/utils';
import { MainTableDS, DetailedLineDS } from './stores/index';
import { detailQuery, detailSave } from './services/index';

const { Panel } = Collapse;

const modelPrompt = 'tarzan.model.print.template.configuration';

const detail = props => {
  const { match } = props;
  const {
    params: { templateId },
    path,
  } = match;

  /** @type {boolean} 删除按钮禁用开关 */
  const [deleteDisabled, setDeleteDisabled] = useState(false);

  /** @type {boolean} 编辑开关 */
  const [editFlag, setEditFlag] = useState(false);

  // 打印渠道
  const [printChannel, setPrintChannel] = useState();

  const [tableIds, setTableIds] = useState([])

  const headerFormDS = useMemo(() => new DataSet(MainTableDS('detail')), []);
  const lineTableDS = useMemo(() => new DataSet(DetailedLineDS()), []);

  useEffect(() => {
    headerFormDS.addEventListener('update', handleChangeHeaderFormValue);
    return () => {
      headerFormDS.removeEventListener('update', handleChangeHeaderFormValue);
    };
  }, []);

  useEffect(() => {
    lineTableDS.addEventListener('batchSelect', handleTableSelect);
    lineTableDS.addEventListener('batchUnSelect', handleTableSelect);
    return () => {
      lineTableDS.removeEventListener('batchSelect', handleTableSelect);
      lineTableDS.removeEventListener('batchUnSelect', handleTableSelect);
    };
  }, []);

  useEffect(() => {
    if (templateId !== 'create') {
      handleQueryDetailData();
    }
    setEditFlag(templateId === 'create');
  }, [templateId]);

  // 监听关系表格勾选数据
  const handleTableSelect = ({ dataSet }) => {
    setDeleteDisabled(dataSet.selected.length <= 0);
  };

  /**
   * @description 头字段更改
   * <AUTHOR>
   */
  const handleChangeHeaderFormValue = ({ name, value }) => {
    // 头上打印渠道改变时的逻辑
    if (name === "printChannel") {
      lineTableDS.loadData(undefined);
      setPrintChannel(value);
      headerFormDS.getField('printChannelPath').set('required', ["DIRECT", "INSTRUCT-JAVA", "PDF", "BARTENDER"].includes(value));
      lineTableDS.current?.set("printers", null);
      lineTableDS.getField('printers')?.set('required', ["DIRECT", "INSTRUCT-JAVA", "INSTRUCT-FRONT"].includes(value));

      if (["PDF", "FR"].includes(value)) {
        lineTableDS.getField('printTemplateName')?.set("bind", "");
        lineTableDS.getField('printTemplateCode')?.set("bind", "printTemplateObject");
      } else {
        lineTableDS.getField('printTemplateObject')?.set("lovPara", { printChannel: value });
        lineTableDS.getField('printTemplateName')?.set("bind", "printTemplateObject.templateName");
        lineTableDS.getField('printTemplateCode')?.set("bind", "printTemplateObject.templateCode");
      }
    }
  };

  // 查询详情界面数据
  const handleQueryDetailData = async () => {
    const res = await detailQuery({
      printFunctionId: templateId,
    });
    if (res && res?.success) {
      const headerFormData = res.rows;
      delete headerFormDS.printTemplateList;
      headerFormDS.create(headerFormData);
      lineTableDS.loadData(res.rows.printTemplateList);
      const ids = (res.rows.printTemplateList || []).map(item => item.printFunctionTemplateId)
      setTableIds(ids)
      setPrintChannel(headerFormData.printChannel);
      if (["PDF", "FR"].includes(headerFormData.printChannel)) {
        lineTableDS.getField('printTemplateName').set("bind", "");
        lineTableDS.getField('printTemplateCode').set("bind", "printTemplateObject");
      } else {
        lineTableDS.getField('printTemplateObject').set("lovPara", { printChannel: headerFormData.printChannel });
        lineTableDS.getField('printTemplateName').set("bind", "printTemplateObject.templateName");
        lineTableDS.getField('printTemplateCode').set("bind", "printTemplateObject.templateCode");
      }
    }
    else {
      notification.error({
        message: res?.message,
      });
    }
  };

  const lineTableColumns = useMemo(
    () => [
      {
        name: 'printTemplateGroup',
        editor: editFlag,
      },
      {
        name: 'printTemplateObject',
        editor: () => {
          if (!editFlag) {
            return false;
          }
          if (!["FR", "PDF"].includes(headerFormDS.current.get('printChannel'))) {
            return (
              <Lov />
            );
          }
          return <TextField />;
        },
      },
      {
        name: 'printTemplateName',
        // editor: () => !(!editFlag || headerFormDS.current.get('printChannel') === 'H0'),
        editor: () => !(!editFlag || ['REPORT','BARTENDER','DIRECT','INSTRUCT-JAVA','INSTRUCT-FRONT','PDA','H0'].includes(headerFormDS.current.get('printChannel'))),
      },
      {
        name: 'printers',
        editor: () => {
          if (editFlag && ["DIRECT", "INSTRUCT-JAVA", "INSTRUCT-FRONT"].includes(headerFormDS.current.get('printChannel'))) {
            return (
              <Select />
            );
          }
        },
      },
      {
        name: 'docCategory',
        editor: editFlag,
        width: 160,
      },
      {
        name: 'docTypeLov',
        editor: editFlag,
      },
      {
        name: 'siteLov',
        editor: editFlag,
        width: 130,
      },
      {
        name: 'materialLov',
        editor: editFlag,
        width: 130,
      },
      {
        name: 'materialCategoryLov',
        editor: editFlag,
        width: 150,
      },
      {
        name: 'workcellLov',
        editor: editFlag,
      },
      {
        name: 'customerLov',
        editor: editFlag,
        width: 160,
      },
      {
        name: 'supplierLov',
        editor: editFlag,
        width: 130,
      },
    ],
    [editFlag],
  );

  // 取消按钮
  const handleCancel = useCallback(() => {
    if (templateId === 'create') {
      setEditFlag(false);
      props.history.push(`/hmes/print-template-configuration/list`);
    } else {
      setEditFlag(false);
      handleQueryDetailData();
    }
  }, []);

  // 保存按钮
  const handleSave = useCallback(async () => {
    const validataFlag = await headerFormDS.current.validate();
    const validateLineTable = await lineTableDS.validate();
    let cIds// 初始所有的id
    setTableIds(prev => {
      cIds = prev
      return prev
    })
    // 当前页面显示的id
    const xIds = lineTableDS.toData().map(item => item.printFunctionTemplateId)
    // 删除的id
    const ssIds = cIds.filter(item => !xIds.includes(item))
    const deleteList=ssIds.map(item => {
      return { 'printFunctionTemplateId': item, 'deleteFlag': 'Y' }
    })
    const list = [
      ...deleteList,
      ...lineTableDS.updated.map(item=>item.toData()),
      ...lineTableDS.created.map(item=>item.toData()),
    ]
    if (validataFlag && validateLineTable) {
      const params = {
        ...headerFormDS.current.toData(),
        printTemplateList: list,
      };
      return detailSave(params).then(res => {
        if (res.success) {
          if (templateId === 'create') {
            setEditFlag(false);
            props.history.push(`/hmes/print-template-configuration/detail/${res.rows}`);
          } else {
            setEditFlag(false);
            handleQueryDetailData();
          }
        } else {
          notification.error({
            message: res.message,
          });
        }
      });
    }
    notification.warning({
      description: intl.get(`${modelPrompt}.message.validateMessage`).d('请将数据填写完整后保存'),
    });
    return false;
  }, []);

  // 编辑按钮
  const handleEdit = useCallback(() => {
    setEditFlag(true);
  }, []);

  // 新增模板按钮
  const handleAddTemplate = () => {
    // 根据打印渠道进行跳转
    const printChannel = headerFormDS.current.get('printChannel');
    switch (printChannel) {
      case 'H0':
        return props.history.push(`/hiam/label-management`);
      case 'REPORT':
        return props.history.push(`/hrpt/report-definition/list`);
      case 'BARTENDER':
        return props.history.push(`/hmes/bartender-template-maintain`);
      case 'INSTRUCT-FRONT':
        return props.history.push(`/hmes/print-instruction-maintain`);
      case 'INSTRUCT-JAVA':
        return props.history.push(`/hmes/print-instruction-maintain`);
      case 'PDA':
        return props.history.push(`/hmes/print-instruction-maintain`);
      default:
        return null;
    }
  };

  const handleBatchDelete = () => {
    lineTableDS.remove(lineTableDS.selected);
  };
  const handleAddEdit = () => {
    if(!['FR','PDF'].includes(headerFormDS?.current?.get('printChannel'))){
      lineTableDS.getField('printers').set('required', ["DIRECT", "INSTRUCT-JAVA", "INSTRUCT-FRONT"].includes(headerFormDS?.current?.get('printChannel')));
      lineTableDS.getField('printTemplateObject').set("lovPara", { printChannel: headerFormDS?.current?.get('printChannel') });
      lineTableDS.getField('printTemplateName').set("bind", "printTemplateObject.templateName");
      lineTableDS.getField('printTemplateCode').set("bind", "printTemplateObject.templateCode");
    }else{
      lineTableDS.getField('printTemplateName').set("bind", undefined);
    }
    lineTableDS.create({}, 0);
  };
  // 行表按钮
  const buttons = [
    <Popconfirm
      title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
      onConfirm={() => handleBatchDelete()}
      okText={intl.get('tarzan.common.button.confirm').d('确认')}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
    >
      <PermissionButton
        type="c7n-pro"
        icon="delete_black-o"
        disabled={!editFlag || deleteDisabled}
        funcType="flat"
        shape="circle"
        size="small"
        permissionList={[
          {
            code: `dist.button.delete`,
            type: 'button',
            meaning: '详情页-删除按钮',
          },
        ]}
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
    </Popconfirm>,
    <PermissionButton
      type="c7n-pro"
      icon="add"
      disabled={!editFlag}
      onClick={() => handleAddEdit()}
      funcType="flat"
      shape="circle"
      size="small"
      permissionList={[
        {
          code: `${path}.button.edit`,
          type: 'button',
          meaning: '详情页-编辑新建删除复制按钮',
        },
      ]}
    >
      {intl.get('hzero.common.button.add').d('新增')}
    </PermissionButton>,
  ];

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.function.detailed.title`).d('打印模板详情')}
        backPath="/hmes/print-template-configuration/list"
      >
        {editFlag && (
          <PermissionButton
            type="c7n-pro"
            color="primary"
            icon="save"
            onClick={handleSave}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.save').d('保存')}
          </PermissionButton>
        )}
        {editFlag && (
          <Button icon="close" onClick={handleCancel}>
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
        )}
        {!editFlag && (
          <>
            <PermissionButton
              type="c7n-pro"
              color="primary"
              icon="edit-o"
              onClick={handleEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              color="primary"
              icon="add"
              disabled={['FR', 'PDF', 'DIRECT'].includes(headerFormDS?.current?.get('printChannel'))}
              onClick={handleAddTemplate}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.add.template').d('新增模板')}
            </PermissionButton>
          </>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'templateList']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.basicInfo`).d('基础信息')}
            key="basicInfo"
            dataSet={headerFormDS}
          >
            <Form dataSet={headerFormDS} columns={3} disabled={!editFlag} labelWidth={112}>
              <TextField name="printButtonCode" />
              <TextField name="description" />
              <Select name="printChannel" />
              <TextField name="printChannelPath" colSpan={2} />
            </Form>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.title.template.list`).d('打印模版名称列')}
            key="templateList"
            dataSet={lineTableDS}
          >
            <Table
              dataSet={lineTableDS}
              columns={lineTableColumns}
              buttons={buttons}
              searchCode="PrintTemplateConfigurationDetail"
              customizedCode="PrintTemplateConfigurationDetail"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.model.print.template.configuration', 'tarzan.common'],
})(detail);
