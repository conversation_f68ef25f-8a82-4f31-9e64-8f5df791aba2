/**
 * @Description: 抽样方案前台功能
 * @Author: <<EMAIL>>
 * @Date: 2021-08-31 10:27:01
 * @LastEditTime: 2023-05-18 16:37:30
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import {
  DataSet,
  Button,
  Form,
  Table,
  TextField,
  Select,
  Switch,
  NumberField,
  Spin,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Popconfirm } from 'choerodon-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useDataSetEvent } from 'utils/hooks';
import { BASIC } from '@utils/config';
import { AttributeDrawer } from '@components/tarzan-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { fetchsamplingModeDetail, savesamplingModeDetail } from '../services';
import { detailFormDS, detailTableDS } from '../stores/SamplingModeDS';

const modelPrompt = 'tarzan.sampling.samplingMethod.samplingMode';

const SamplingTable = props => {
  const {
    match: {
      path,
      params: { id },
    },
    custConfig,
    customizeForm,
  } = props;
  const [canEdit, setCanEdit] = useState(false);
  const [samplingType, setSamplingType] = useState('');
  const queryDetail = useRequest(fetchsamplingModeDetail(), {
    // 详情页数据查询
    manual: true,
  });

  const saveDetail = useRequest(savesamplingModeDetail(), {
    // 详情页数据保存
    manual: true,
    needPromise: true,
  });

  const formDs = useMemo(() => new DataSet(detailFormDS()), []);
  const tableDs = useMemo(() => new DataSet(detailTableDS()), []);

  useDataSetEvent(formDs, 'update', ({ name, record, value }) => {
    if (name === 'samplingType') {
      setSamplingType(value);
      record.init('samplingMethodValue', null);
      record.init('processMode', null);
      record.init('samplingStandard', null);
      record.init('samplingPlanType', null);
      record.init('aql', null);
      record.init('inspectionLevel', null);

      if (value === 'NATIONAL_STANDARD_SAMPLING') {
        formDs.current!.set('strictness', 'NORMAL');
      } else {
        formDs.current!.init('strictness', null);
      }

      if (
        [
          'FULL_SAMPLING',
          'USER_DEFINED_SAMPLING',
          'PERCENTAGE_SAMPLING',
          'FIXED_VALUE_SAMPLING',
        ].includes(value)
      ) {
        formDs.current!.set('acceptQty', 0);
        formDs.current!.set('rejectQty', 1);
      } else {
        record.init('acceptQty', null);
        record.init('rejectQty', null);
      }
    }
  });

  useDataSetEvent(tableDs, 'update', ({ name, record, value }) => {
    if (name === 'samplingType') {
      record.init('samplingMethodValue', null);
      record.init('processMode', null);
      record.init('samplingStandard', null);
      record.init('samplingPlanType', null);
      record.init('strictness', null);
      record.init('aql', null);
      record.init('inspectionLevel', null);
      if (
        [
          'FULL_SAMPLING',
          'USER_DEFINED_SAMPLING',
          'PERCENTAGE_SAMPLING',
          'FIXED_VALUE_SAMPLING',
        ].includes(value)
      ) {
        record.set('acceptQty', 0);
        record.set('rejectQty', 1);
      } else {
        record.init('acceptQty', null);
        record.init('rejectQty', null);
      }
    }
  });

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      return;
    }
    initPageData();
  }, [id]);

  const initPageData = () => {
    queryDetail.run({
      params: {
        customizeUnitCode: `${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_DETAIL.BASIC`,
        samplingMethodId: id,
      },
      onSuccess: res => {
        const { samplingMethodDtlList } = res;
        formDs.loadData([res]);
        tableDs.loadData(samplingMethodDtlList || []);
        setSamplingType(res.samplingType);
      },
    });
  };

  const handleSave = async createFlag => {
    const formValidate = await formDs.validate();
    const tableValidate = await tableDs.validate();
    if (!formValidate || !tableValidate) {
      return Promise.resolve(false);
    }
    const formData = formDs.toData()[0];
    const tableData = tableDs.toData();

    if (samplingType === 'INTERVAL_SAMPLING' && tableData.length === 0) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.requirements`)
          .d('必须维护至少一个区间的抽样方式！'),
      });
      return Promise.resolve(false);
    }

    return saveDetail.run({
      params: {
        ...formData,
        // @ts-ignore
        samplingMethodDtlList: formData.samplingType === 'INTERVAL_SAMPLING' ? tableData : [],
      },
      onSuccess: res => {
        // @ts-ignore
        notification.success();
        if (createFlag) {
          if (id === 'create') {
            formDs.reset();
          } else {
            props.history.push(`/sampling/sampling-method/sampling-mode/detail/create`);
            formDs.loadData([{ enableFlag: 'Y' }]);
            tableDs.loadData([]);
          }
        } else {
          setCanEdit(prev => !prev);
          if (id === 'create') {
            props.history.push(`/sampling/sampling-method/sampling-mode/detail/${res}`);
          } else {
            initPageData();
          }
        }
      },
    });
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/sampling/sampling-method/sampling-mode/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  const handleDelete = record => {
    const recordIndex = record.index;
    const previousRecord = tableDs.get(recordIndex - 1);
    const nextRecord = tableDs.get(recordIndex + 1);

    if (recordIndex === 0 && nextRecord) {
      nextRecord.set('lowerLimit', 0);
    }
    if (recordIndex > 0 && nextRecord && previousRecord) {
      nextRecord.set('lowerLimit', previousRecord.get('upperLimit'));
    }
    tableDs.delete(record, false);
  };
  const handleAdd = () => {
    const tableData = tableDs.toData();

    const tableLength = tableData?.length;
    if (tableLength === 0) {
      tableDs.create(
        {
          lowerLimit: 0,
        },
        0,
      );
    }
    if (tableLength && tableLength > 0) {
      tableDs.create(
        {
          // @ts-ignore
          lowerLimit: tableData[tableLength - 1]?.upperLimit,
        },
        tableLength,
      );
    }
  };

  const handleRecordLimitChange = record => {
    const nextRecord = tableDs.get(record.index + 1);
    if (nextRecord) {
      nextRecord.set('lowerLimit', record.get('upperLimit'));
    }
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          onClick={handleAdd}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'lowerLimit',
      editor: canEdit && <NumberField />,
    },
    {
      name: 'upperLimit',
      editor: record =>
        canEdit && (
          <NumberField
            onChange={() => {
              handleRecordLimitChange(record);
            }}
          />
        ),
    },

    {
      name: 'samplingType',
      editor: () =>
        canEdit && (
          <Select optionsFilter={_record => _record.get('typeCode') !== 'INTERVAL_SAMPLING'} />
        ),
    },
    {
      name: 'samplingMethodValue',
      width: 120,
      editor: canEdit && <NumberField />,
    },
    {
      name: 'processMode',
      width: 120,
      editor: canEdit && <Select />,
    },
    {
      name: 'samplingStandard',
      editor: canEdit && <Select />,
    },
    {
      name: 'samplingPlanType',
      width: 120,
      editor: canEdit && <Select />,
    },
    {
      name: 'strictness',
      editor: canEdit && <Select />,
    },
    {
      name: 'aql',
      editor: canEdit && <Select />,
    },
    {
      name: 'inspectionLevel',
      editor: canEdit && <Select />,
    },
    {
      name: 'acceptQty',
      editor: canEdit && <NumberField />,
    },
    {
      name: 'rejectQty',
      editor: canEdit && <NumberField />,
    },
  ];

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.samplingModeMaintenance`).d('抽样方式维护')}
        backPath="/sampling/sampling-method/sampling-mode/list"
      >
        {canEdit && (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave(false)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="save" onClick={() => handleSave(true)}>
              {intl.get(`tarzan.common.button.saveAndCreate`).d('保存并新建下一条')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <AttributeDrawer
          serverCode={BASIC.TARZAN_SAMPLING}
          className="org.tarzan.qms.domain.entity.MtSamplingMethod"
          kid={id}
          canEdit={canEdit}
          disabled={id === 'create'}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_DETAIL.ATTR`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <Spin spinning={queryDetail.loading || saveDetail.loading}>
          {customizeForm(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_DETAIL.BASIC`,
            },
            <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
              <TextField name="samplingMethodCode" />
              <TextField name="samplingMethodDesc" />
              <Select name="samplingType" />
              <NumberField name="samplingMethodValue" />
              <Select name="processMode" />
              <Select name="samplingStandard" />
              <Select name="samplingPlanType" />
              <Select name="strictness" />
              <Select name="aql" />
              <Select name="inspectionLevel" />
              <NumberField name="acceptQty" />
              <NumberField name="rejectQty" />
              <Switch name="enableFlag" />
            </Form>,
          )}
          {samplingType === 'INTERVAL_SAMPLING' && <Table dataSet={tableDs} columns={columns} />}
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.sampling.samplingMethod.samplingMode', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_DETAIL.ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.SAMPLING_METHOD_DETAIL.BASIC`,
    ],
    // @ts-ignore
  })(SamplingTable),
);
