/*
 * @Description: 索赔管理-详情页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-21 16:46:01
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-25 13:50:58
 */
import intl from 'utils/intl';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import {DataSetProps} from 'choerodon-ui/pro/lib/data-set/DataSet';
import {getCurrentOrganizationId, getCurrentUser} from 'utils/utils';
import {BASIC} from '@utils/config';
import {getProductWithPrecision} from '@/utils';

const modelPrompt = 'tarzan.qms.marketActivity.claimDocManagement';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  primaryKey: 'claimDocId',
  fields: [
    {
      name: 'claimDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.claimDocNum`).d('判断书编号'),
      required: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'hostPlant',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.hostPlant`).d('主机厂'),
      required: true,
    },
    {
      name: 'title',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.title`).d('标题'),
      required: true,
    },
    {
      name: 'aResponsibilityRatio',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.aResponsibilityRatio`).d('甲方责任比例'),
      max: 100,
      min: 0,
      validator: (value, _, record: any) => {
        record?.set('bResponsibilityRatio', getProductWithPrecision(100, value, 6, 'subtraction'));
        return true;
      },
      required: true,
    },
    {
      name: 'bResponsibilityRatio',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.bResponsibilityRatio`).d('乙方责任比例'),
      max: 100,
      min: 0,
      validator: (value, _, record: any) => {
        record?.set('aResponsibilityRatio', getProductWithPrecision(100, value, 6, 'subtraction'));
        return true;
      },
      required: true,
    },
    {
      name: 'dutyEnclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.dutyEnclosure`).d('责任区分技术判决书'),
      bucketName: 'qms',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('是否生效'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'validFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.validFrom`).d('启用时间'),
      required: true,
      max: 'validTo',
    },
    {
      name: 'validTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.validTo`).d('停用时间'),
      min: 'validFrom',
    },
    {
      name: 'remarks',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remarks`).d('备注'),
    },
    {
      name: 'createPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      disabled: true,
      lovPara: { tenantId },
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'createdBy',
      bind: 'createPersonLov.id',
    },
    {
      name: 'createdByName',
      bind: 'createPersonLov.realName',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-claim-doc/info/ui`,
        method: 'GET',
      };
    },
  },
});

const tableDS: () => DataSetProps = () => ({
  selection: false,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  primaryKey: 'claimLineId',
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'vehicalModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicalModel`).d('车型'),
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      lovPara: { tenantId },
    },
    {
      name: 'cusMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件号'),
      required: true,
    },
    {
      name: 'cusMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialName`).d('客户零件名称'),
      required: true,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          siteId: dataSet.parent?.current?.get('siteId'),
          tenantId,
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'batteryModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryModel`).d('电池包型号'),
      disabled: true,
    },
  ],
});

export { detailDS, tableDS };
