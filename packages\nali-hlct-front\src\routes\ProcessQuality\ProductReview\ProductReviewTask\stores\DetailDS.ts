/*
 * @Description: 产品审核任务-详情页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-10-11 10:14:17
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-19 16:00:01
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.productReview.productReviewTask';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  // dataKey: 'rows',
  fields: [
    // 基础信息
    {
      name: 'productRevTaskCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskCode`).d('产品审核任务编码'),
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        disabled: ({ record }) => record?.get('productRevTaskId'),
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'productRevPlanId',
      type: FieldType.number,
    },
    {
      name: 'productRevPlanCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevPlanCode`).d('产品审核计划编码'),
      disabled: true,
    },
    {
      name: 'productRevSchemeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.productRevSchemeCode`).d('产品审核方案编码'),
      lovCode: 'YP.QIS.PRODUCT_REV_SCHEME',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      textField: 'productRevSchemeCode',
      computedProps: {
        lovPara: ({ record }) => {
          if (record?.get('productRevTaskType') === 'QUALITY_SYS_REQUEST') {
            return {
              tenantId,
              siteId: record?.get('siteId'),
              materialId: record?.get('planMaterialId'),
              workcellId: record?.get('planWorkcellId') || undefined,
            };
          }
          return {
            tenantId,
            siteId: record?.get('siteId'),
          }
        },
        disabled: ({ record }) => !record?.get('siteId') || record?.get('productRevTaskStatus') !== 'NEW',
      },
    },
    {
      name: 'productRevSchemeId',
      bind: 'productRevSchemeLov.productRevSchemeId',
    },
    {
      name: 'productRevSchemeCode',
      bind: 'productRevSchemeLov.productRevSchemeCode',
    },
    {
      name: 'productRevTaskStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_STATUS',
      lovPara: { tenantId },
      defaultValue: 'NEW',
      disabled: true,
    },
    {
      name: 'productRevTaskType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevTaskType`).d('任务类别'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_TYPE',
      lovPara: { tenantId },
      defaultValue: 'CUSTOMER_REQUEST',
      disabled: true,
    },
    // 计划带过来的materialId
    {
      name: 'planMaterialId',
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'productRevSchemeLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      bind: 'productRevSchemeLov.materialCode',
      disabled: true,
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'productRevSchemeLov.materialName',
      disabled: true,
    },
    {
      name: 'cusMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件编码'),
      bind: 'productRevSchemeLov.cusMaterialCode',
      disabled: true,
    },
    {
      name: 'cusMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialName`).d('客户零件描述'),
      bind: 'productRevSchemeLov.cusMaterialName',
      disabled: true,
    },
    // 计划带过来的workcellId
    {
      name: 'planWorkcellId',
    },
    {
      name: 'workcellId',
      bind: 'productRevSchemeLov.workcellId',
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工序编码'),
      bind: 'productRevSchemeLov.workcellCode',
      disabled: true,
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序描述'),
      bind: 'productRevSchemeLov.workcellName',
      disabled: true,
    },
    {
      name: 'samplePosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplePosition`).d('抽样地点'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_SAMPLE_POSITION',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'warehouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('完工仓库编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.LOCATOR',
      textField: 'locatorName',
      required: true,
      computedProps: {
        lovPara: ({ record }) => ({
          tenantId,
          locatorType: 'PRODUCTION_WAREHOUSE',
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => !record?.get('siteId') || record?.get('productRevTaskStatus') !== 'NEW',
        required: ({ record }) => record?.get('samplePosition') === 'WAREHOUSE',
      },
    },
    {
      name: 'warehouseId',
      bind: 'warehouseLov.locatorId',
    },
    {
      name: 'warehouseName',
      bind: 'warehouseLov.locatorName',
    },
    {
      name: 'sampleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleQty`).d('抽样数量'),
      required: true,
      min: 0,
      computedProps: {
        disabled: ({ record }) => record?.get('productRevTaskStatus') !== 'NEW',
      },
    },
    {
      name: 'reviewAim',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewAim`).d('审核目的'),
      required: true,
      computedProps: {
        disabled: ({ record }) => record?.get('productRevTaskStatus') !== 'NEW',
      },
    },
    {
      name: 'reviewDec',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewDec`).d('审核依据'),
      computedProps: {
        disabled: ({ record }) => record?.get('productRevTaskStatus') !== 'NEW',
      },
    },
    {
      name: 'reviewUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.reviewUuid`).d('审核依据附件'),
      computedProps: {
        disabled: ({ record }) => record?.get('productRevTaskStatus') !== 'NEW',
      },
    },
    // 创建人
    {
      name: 'createdBy',
    },
    {
      name: 'publishedPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.publishedPerson`).d('审核发布人'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'publishedBy',
      bind: 'publishedPersonLov.id',
    },
    {
      name: 'publishedName',
      bind: 'publishedPersonLov.realName',
    },
    {
      name: 'reviewedPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reviewPerson`).d('审核员'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'reviewedBy',
      bind: 'reviewedPersonLov.id',
    },
    {
      name: 'reviewedName',
      bind: 'reviewedPersonLov.realName',
    },
    {
      name: 'executeDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.executeDate`).d('执行时间'),
      computedProps: {
        required: ({ record }) => ['EXECUTING', 'REVIEW_REJECT'].includes(record?.get('productRevTaskStatus')),
        disabled: ({ record }) => !['EXECUTING', 'REVIEW_REJECT'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'qeRejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qeRejectReason`).d('QE驳回原因'),
      disabled: true,
    },
    {
      name: 'qeRejectTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.qeRejectTime`).d('QE驳回时间'),
      disabled: true,
    },
    // 后续措施
    {
      name: 'qkz',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qkz`).d('QKZ'),
      disabled: true,
    },
    {
      name: 'reviewResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewResult`).d('审核结果'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_RESULT',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'furtherStepsFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.furtherStepsFlag`).d('需要进一步采取措施'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'furtherStepsExecutorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.furtherStepsExecutor`).d('需要进一步采取措施人员'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.EMPLOYEE',
      textField: 'name',
      lovPara: { tenantId },
      computedProps: {
        disabled: ({ record }) => record?.get('furtherStepsFlag') !== 'Y',
        required: ({ record }) => record?.get('furtherStepsFlag') === 'Y',
      },
    },
    {
      name: 'furtherStepsExecutor',
      bind: 'furtherStepsExecutorLov.employeeId',
    },
    {
      name: 'furtherStepsExecutorName',
      bind: 'furtherStepsExecutorLov.name',
    },
    {
      name: 'furtherStepsExecuteLoc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.furtherStepsExecuteLoc`).d('需要进一步采取措施地点'),
      computedProps: {
        disabled: ({ record }) => record?.get('furtherStepsFlag') !== 'Y',
        required: ({ record }) => record?.get('furtherStepsFlag') === 'Y',
      },
    },
    {
      name: 'furtherStepsExecuteCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.furtherStepsExecuteCon`).d('需要进一步采取措施措施'),
      computedProps: {
        disabled: ({ record }) => record?.get('furtherStepsFlag') !== 'Y',
        required: ({ record }) => record?.get('furtherStepsFlag') === 'Y',
      },
    },
    {
      name: 'invHoldFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.invHoldFlag`).d('封存所有库存'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'invHoldExecutorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.invHoldExecutor`).d('封存所有库存人员'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.EMPLOYEE',
      textField: 'name',
      lovPara: { tenantId },
      computedProps: {
        disabled: ({ record }) => record?.get('invHoldFlag') !== 'Y',
        required: ({ record }) => record?.get('invHoldFlag') === 'Y',
      },
    },
    {
      name: 'invHoldExecutor',
      bind: 'invHoldExecutorLov.employeeId',
    },
    {
      name: 'invHoldExecutorName',
      bind: 'invHoldExecutorLov.name',
    },
    {
      name: 'invHoldExecuteLoc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.invHoldExecuteLoc`).d('封存所有库存地点'),
      computedProps: {
        disabled: ({ record }) => record?.get('invHoldFlag') !== 'Y',
        required: ({ record }) => record?.get('invHoldFlag') === 'Y',
      },
    },
    {
      name: 'invHoldExecuteCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.invHoldExecuteCon`).d('封存所有库存措施'),
      computedProps: {
        disabled: ({ record }) => record?.get('invHoldFlag') !== 'Y',
        required: ({ record }) => record?.get('invHoldFlag') === 'Y',
      },
    },
    {
      name: 'dischargeMeasFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dischargeMeasFlag`).d('采取排出措施'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'dischargeMeasExecutorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.dischargeMeasExecutor`).d('采取排出措施人员'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.EMPLOYEE',
      textField: 'name',
      lovPara: { tenantId },
      computedProps: {
        disabled: ({ record }) => record?.get('dischargeMeasFlag') !== 'Y',
        required: ({ record }) => record?.get('dischargeMeasFlag') === 'Y',
      },
    },
    {
      name: 'dischargeMeasExecutor',
      bind: 'dischargeMeasExecutorLov.employeeId',
    },
    {
      name: 'dischargeMeasExecutorName',
      bind: 'dischargeMeasExecutorLov.name',
    },
    {
      name: 'dischargeMeasExecuteLoc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dischargeMeasExecuteLoc`).d('采取排出措施地点'),
      computedProps: {
        disabled: ({ record }) => record?.get('dischargeMeasFlag') !== 'Y',
        required: ({ record }) => record?.get('dischargeMeasFlag') === 'Y',
      },
    },
    {
      name: 'dischargeMeasExecuteCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dischargeMeasExecuteCon`).d('采取排出措施'),
      computedProps: {
        disabled: ({ record }) => record?.get('dischargeMeasFlag') !== 'Y',
        required: ({ record }) => record?.get('dischargeMeasFlag') === 'Y',
      },
    },
    {
      name: 'updateDrawingsFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.updateDrawingsFlag`).d('开始更新图纸/工艺'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'updateDrawingsExecutorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.updateDrawingsExecutor`).d('开始更新图纸/工艺人员'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.EMPLOYEE',
      textField: 'name',
      lovPara: { tenantId },
      computedProps: {
        disabled: ({ record }) => record?.get('updateDrawingsFlag') !== 'Y',
        required: ({ record }) => record?.get('updateDrawingsFlag') === 'Y',
      },
    },
    {
      name: 'updateDrawingsExecutor',
      bind: 'updateDrawingsExecutorLov.employeeId',
    },
    {
      name: 'updateDrawingsExecutorName',
      bind: 'updateDrawingsExecutorLov.name',
    },
    {
      name: 'updateDrawingsExecuteLoc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.updateDrawingsExecuteLoc`).d('开始更新图纸/工艺地点'),
      computedProps: {
        disabled: ({ record }) => record?.get('updateDrawingsFlag') !== 'Y',
        required: ({ record }) => record?.get('updateDrawingsFlag') === 'Y',
      },
    },
    {
      name: 'updateDrawingsExecuteCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.updateDrawingsExecuteCon`).d('开始更新图纸/工艺措施'),
      computedProps: {
        disabled: ({ record }) => record?.get('updateDrawingsFlag') !== 'Y',
        required: ({ record }) => record?.get('updateDrawingsFlag') === 'Y',
      },
    },

    // 处理结论
    {
      name: 'goodProducts',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.goodProducts`).d('可出货产品条码'),
      multiple: true,
      disabled: true,
    },
    {
      name: 'badProducts',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.badProducts`).d('不可出货产品条码'),
      multiple: true,
      disabled: true,
    },
    {
      name: 'finalpInspFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.finalpInspFlag`).d('成品仓库检验'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'finalpInspCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.finalpInspCon`).d('成品仓库检验结论'),
    },
    {
      name: 'wipInspFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wipInspFlag`).d('在制品检验'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'wipInspCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wipInspCon`).d('在制品检验结论'),
    },
    {
      name: 'drawingsChangeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.drawingsChangeFlag`).d('图纸更改'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'drawingsChangeCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.drawingsChangeCon`).d('图纸更改结论'),
    },
    {
      name: 'manuTechFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manuTechFlag`).d('生产工艺更改'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'manuTechCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manuTechCon`).d('生产工艺更改结论'),
    },
    {
      name: 'cusInformFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusInformFlag`).d('顾客通知'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'cusInformCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusInformCon`).d('顾客通知结论'),
    },
    {
      name: 'cusProdRetroFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusProdRetroFlag`).d('顾客处产品追溯'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'cusProdRetroCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusProdRetroCon`).d('顾客处产品追溯结论'),
    },
    {
      name: 'precautionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.precautionFlag`).d('预防纠正措施'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => ['REVIEWING', 'REJECTED'].includes(record?.get('productRevTaskStatus')),
      },
    },
    {
      name: 'precautionCon',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.precautionCon`).d('预防纠正措施结论'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          return {
            ...rows?.baseInfo,
            groupInfos: rows?.groupInfos,
          };
        },
      };
    },
  },
});

// 产品审核项目DS
const productRevItemDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  selection: false,
  forceValidate: true,
  primaryKey: 'productReviewItemId',
  fields: [
    {
      name: 'productReviewItemId',
    },
    {
      name: 'productReviewItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemCode`).d('产品审核项目编码'),
      disabled: true,
    },
    {
      name: 'productReviewItemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemDesc`).d('项目描述'),
      required: true,
    },
    {
      name: 'productReviewItemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemType`).d('项目类别'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_ITEM_TYPE',
      lovPara: { tenantId },
      required: true,
      dynamicProps: {
        disabled: ({ record }) => record?.get('type'),
      },
    },
    {
      name: 'productReviewMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewMethod`).d('测试方法'),
      lookupCode: 'MT.QMS.INSPECT_METHOD',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'specRequire',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specRequire`).d('规格要求'),
    },
    {
      name: 'reviewFrequency',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewFrequency`).d('测试频率'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_FREQUENCY',
      lovPara: { tenantId },
    },
    {
      name: 'weightCoefficient',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.weightCoefficient`).d('特征加权系数'),
      lookupCode: 'YP.QIS.PRODUCT_REVIEW_WEIGHT_COEFFICIENT',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'fp',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.fp`).d('缺陷点数FP'),
    },
    {
      name: 'nf',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.nf`).d('加权的抽样数nF'),
    },
    {
      name: 'defectCount',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.defectCount`).d('缺陷个数'),
    },
    {
      name: 'reviewResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewResult`).d('审核结果'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_RESULT_BARCODE',
      lovPara: { tenantId },
      computedProps: {
        required: ({ dataSet }) => ['EXECUTING', 'REVIEW_REJECT'].includes(dataSet?.getState('productRevTaskStatus')),
        disabled: ({ dataSet }) => !['EXECUTING', 'REVIEW_REJECT'].includes(dataSet?.getState('productRevTaskStatus')),
      },
    },
    {
      name: 'recordDataFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recordDataFlag`).d('是否记录测试数据'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'fromOrtFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromOrtFlag`).d('是否来源于ORT'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('是否启用'),
      lookupCode: 'MT.YES_NO',
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

// 条码审核结果DS
const barcodeReviewResultDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  selection: false,
  primaryKey: 'produceDtlSampleId',
  dataKey: 'rows',
  queryFields: [
    {
      name: 'productRevSampleCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSampleCode`).d('条码'),
    },
    {
      name: 'reviewResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewResult`).d('审核结果'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_RESULT_BARCODE',
      lovPara: { tenantId },
    },
  ],
  fields: [
    {
      name: 'produceDtlSampleId',
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'productRevSampleCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSampleCode`).d('条码'),
    },
    {
      name: 'testData',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.testData`).d('测试数据'),
      computedProps: {
        required: ({ dataSet }) => dataSet.getState('recordDataFlag') === 'Y',
      },
    },
    {
      name: 'reviewResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reviewResult`).d('审核结果'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_RESULT_BARCODE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => record?.get('reviewResult') !== 'NG',
        required: ({ record }) => record?.get('reviewResult') === 'NG',
      },
    },
    {
      name: 'ncCodeId',
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCodeCode',
      bind: 'ncCodeLov.ncCode',
    },
    {
      name: 'ncCodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码描述'),
      bind: 'ncCodeLov.description',
    },
    {
      name: 'defectLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectLevel`).d('缺陷等级'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_DEFECT_LEVEL',
      lovPara: { tenantId },
      computedProps: {
        required: ({ record }) => record?.get('reviewResult') === 'NG',
      },
    },
    {
      name: 'defectPoint',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.defectPoint`).d('缺陷个数'),
      min: 0,
      computedProps: {
        required: ({ record }) => record?.get('reviewResult') === 'NG',
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/dtl-sample-get/ui`,
        method: 'GET',
      };
    },
  },
});

// 新增产品审核项目的DS
const addProjectItemDS: (type?: string) => DataSetProps = (type = undefined) => ({
  selection: false,
  autoCreate: true,
  paging: false,
  fields: [
    {
      name: 'toolLov',
      type: FieldType.object,
      lovCode: 'YP.QIS.PRODUCT_REVIEW_ITEM',
      lovPara: { tenantId },
      multiple: true,
      required: true,
      optionsProps: dsProps => {
        const { queryFields, ...other } = dsProps;
        const newQueryFields: any = (queryFields || []).map((item: any) => {
          if (item.name !== 'productReviewItemType') {
            return item;
          }
          return {
            name: 'productReviewItemType',
            type: FieldType.string,
            label: intl.get(`${modelPrompt}.productReviewItemType`).d('项目类别'),
            lookupCode: 'YP.QIS.PRODUCT_REVIEW_ITEM_TYPE',
            lovPara: { tenantId },
            disabled: type,
            defaultValue: type,
          };
        });
        return {
          ...other,
          queryFields: newQueryFields,
        };
      },
    },
  ],
})

// 样本信息DS
const sampleDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'produceRevSampleId',
  queryFields: [
    {
      name: 'productRevSampleBarcode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSampleBarcode`).d('条码'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_RESULT_BARCODE',
      lovPara: { tenantId },
    },
  ],
  fields: [
    {
      name: 'produceRevSampleId',
    },
    {
      name: 'productRevSampleBarcode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSampleBarcode`).d('条码'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_TASK_RESULT_BARCODE',
      lovPara: { tenantId },
    },
    {
      name: 'productReviewItemCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemCodes`).d('不良项目编码'),
    },
    {
      name: 'productReviewItemDecs',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productReviewItemDecs`).d('不良项目描述'),
    },
    {
      name: 'ncCodeCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeCodes`).d('不良代码'),
    },
    {
      name: 'fromOrtFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromOrtFlag`).d('是否来源于ORT'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-task/sample-get/ui`,
        method: 'GET',
      };
    },
  },
});

const rejectReasonDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'rejectReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
      required: true,
    },
  ],
});

export { detailDS, productRevItemDS, barcodeReviewResultDS, addProjectItemDS, sampleDS, rejectReasonDS };