/**
 * @Description: ORT测试执行-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-09-20 10:38:58
 * @LastEditTime: 2023-09-20 10:38:58
 * @LastEditors: <<EMAIL>>

*/
import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import withProps from 'utils/withProps';
import { observer } from 'mobx-react';
import { useRequest } from '@components/tarzan-hooks';
import { headerDS, childrenDS } from '../stores/TestExecutionDS';

import { fetchInspectTaskActLineConfig, confirmReceiptConfig, cancelTestConfig } from '../services';

const { Panel } = Collapse;

const modelPrompt = 'tarzan.qms.ort.testExecution';

const TestExecutionList = props => {
  const { headerDs, childrenDs } = props;

  useEffect(() => {
    if (headerDs?.currentPage) {
      headerDs.query(props.headerDs.currentPage);
    } else {
      headerDs.query();
    }
  }, []);

  useDataSetEvent(headerDs, 'load', ({ dataSet }) => {
    headerRowClick(dataSet.current);
  });

  // 行明细查询
  const queryLine = useRequest(fetchInspectTaskActLineConfig(), {
    manual: true,
    needPromise: true,
  });
  // 确认收样
  const confirmReceipt = useRequest(confirmReceiptConfig(), {
    manual: true,
    needPromise: true,
  });
  // 取消收样
  const cancelTest = useRequest(cancelTestConfig(), {
    manual: true,
    needPromise: true,
  });

  const headerRowClick = async record => {
    const res = await queryLine.run({
      params: {
        ortInspectTaskId: record?.get('ortInspectTaskId'),
      },
    });

    if (res.success) {
      childrenDs.loadData(res.rows);
    } else {
      childrenDs.loadData([]);
    }
  };

  const columnsHeader: ColumnProps[] = [
    {
      name: 'inspectDocNum',
      lock: ColumnLock.left,
      width: 260,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'taskStatus',
    },
    {
      name: 'siteLov',
    },
    {
      name: 'inspectType',
    },
    {
      name: 'materialLov',
    },
    {
      name: 'materialName',
    },
    {
      name: 'productType',
    },
    {
      name: 'createdByLov',
    },
    {
      name: 'department',
    },
    {
      name: 'inspectPurpose',
    },
    {
      name: 'remark',
    },
    {
      name: 'sampleQty',
    },
    {
      name: 'sampleType',
    },
    {
      name: 'expectCompleteTime',
    },
    {
      name: 'urgencyDegree',
    },
    {
      name: 'projectStage',
    },
    {
      name: 'internalProductFlag',
    },
    {
      name: 'samplingMonth',
    },
    {
      name: 'prodLineLov',
    },
    {
      name: 'ratedCapacity',
    },
    {
      name: 'inspectResultDemand',
    },
    {
      name: 'withoutMaterialCode',
    },
    {
      name: 'sampleReceiveByLov',
    },
    {
      name: 'sampleReceiveDate',
    },
    {
      name: 'actualStartTime',
    },
    {
      name: 'actualEndTime',
    },
    {
      name: 'enclosure',
      lock: ColumnLock.right,
    },
  ];
  const columnsChildren: ColumnProps[] = [
    {
      name: 'sequence',
      lock: ColumnLock.left,
    },
    {
      name: 'inspectItem',
    },
    {
      name: 'inspectMethod',
    },
    {
      name: 'standardRequirement',
    },
    {
      name: 'inspectFrequency',
    },
    {
      name: 'outsourceFlag',
    },
    {
      name: 'inspectQty',
    },
    {
      name: 'exSampleSolveMethod',
    },
    {
      name: 'acSampleSolveMethod',
    },
    {
      name: 'inspectorLov',
    },
    {
      name: 'actualStartTime',
    },
    {
      name: 'actualEndTime',
    },
    {
      name: 'inspectObject',
    },
  ];

  const handleEdit = record => {
    props.history.push(`/hwms/ort/test-execution/detail/${record.get('ortInspectTaskId')}`);
  };

  const handleCancel = async () => {
    const res = await cancelTest.run({
      params: headerDs?.selected.map(record => {
        return record.toData();
      }),
    });
    if (res?.success) {
      notification.success({});
      headerDs.batchUnSelect(headerDs.selected);
      if (headerDs?.currentPage) {
        headerDs.query(props.headerDs.currentPage);
      } else {
        headerDs.query();
      }
    }
  };
  const handleConfirm = async () => {
    const res = await confirmReceipt.run({
      params: headerDs?.selected.map(record => {
        return record.toData();
      }),
    });
    if (res?.success) {
      notification.success({});
      headerDs.batchUnSelect(headerDs.selected);
      if (headerDs?.currentPage) {
        headerDs.query(props.headerDs.currentPage);
      } else {
        headerDs.query();
      }
    }
  };

  const CancelBtn = observer(props => {
    const { dataSet } = props;
    const buttonAuth = () => {
      if (!(dataSet?.selected?.length > 0)) {
        return false;
      }
      let statusOk = true;
      const statusList = ['NEW', 'SAMPLE_RECEIVED', 'INSPECTING'];
      dataSet?.selected.forEach(record => {
        if (!statusList.includes(record.get('taskStatus'))) {
          statusOk = false;
        }
      });
      return statusOk;
    };
    return (
      <PermissionButton type="c7n-pro" disabled={!buttonAuth()} onClick={handleCancel}>
        {intl.get(`${modelPrompt}.cancelTest`).d('取消测试')}
      </PermissionButton>
    );
  });
  const ConfirmBtn = observer(props => {
    const { dataSet } = props;
    const buttonAuth = () => {
      if (!(dataSet?.selected?.length > 0)) {
        return false;
      }
      let statusOk = true;
      const statusList = ['NEW'];
      dataSet?.selected.forEach(record => {
        if (!statusList.includes(record.get('taskStatus'))) {
          statusOk = false;
        }
      });
      return statusOk;
    };
    return (
      <PermissionButton
        type="c7n-pro"
        color={ButtonColor.primary}
        disabled={!buttonAuth()}
        onClick={handleConfirm}
      >
        {intl.get(`${modelPrompt}.confirm`).d('确认收样')}
      </PermissionButton>
    );
  });

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.testExecution`).d('ORT测试执行')}>
        <ConfirmBtn dataSet={headerDs} />
        <CancelBtn dataSet={headerDs} />
      </Header>
      <Content>
        <Table
          searchCode="ortcszx1"
          customizedCode="ortcszx1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headerDs}
          columns={columnsHeader}
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
        />
        <Collapse bordered={false} defaultActiveKey={['line']}>
          <Panel key="line" header={intl.get(`${modelPrompt}.childrenTitle`).d('测试项目')}>
            <Table customizedCode="ortcszx2" dataSet={childrenDs} columns={columnsChildren} />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.qms.ort.testExecution', 'tarzan.common'],
})(
  withProps(
    () => {
      const childrenDs = new DataSet({
        ...childrenDS(),
      });
      const headerDs = new DataSet({
        ...headerDS(),
        children: {
          operationDesc: childrenDs,
        },
      });
      return {
        headerDs,
        childrenDs,
      };
    },
    { cacheState: true, keepOriginDataSet: true },
  )(TestExecutionList),
);
