import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';


const modelPrompt = 'tarzan.qms.disassembleTaskExecutionPlatform';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    autoQuery: true,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          label: intl.get(`${modelPrompt}.form.teardownTaskNum`).d('拆解任务编码'),
          name: 'teardownTaskNum',
          type: FieldType.string,
        },
        {
          name: 'materialLotType',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.materialLotType`).d('电芯类型'),
          lookupCode: 'YP_QIS_TEARDOWN_BARCODE_TYPE',
          lovPara: { tenantId },
        },
        {
          label: intl.get(`${modelPrompt}.form.teardownApplyCode`).d('申请编码'),
          name: 'teardownApplyCode',
          lovCode: 'YP.QIS.TEARDOWN_APPLY',
          type: FieldType.object,
          ignore: FieldIgnore.always,
        },
        {
          name: 'teardownApplyId',
          bind: 'teardownApplyCode.teardownApplyId',
        },
        {
          label: intl.get(`${modelPrompt}.form.materialLotObj`).d('厂内电芯条码'),
          name: 'materialLotObj',
          lovCode: 'YP_MES.MES.MATERIAL_LOT',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          computedProps: {
            disabled: ({ record }) => record?.get('outsideMaterialLotCode'),
          },
        },
        {
          name: 'materialLotCode',
          bind: 'materialLotObj.materialLotCode',
        },
        {
          name: 'outsideMaterialLotCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.outsideMaterialLotCode`).d('厂外电芯条码'),
          computedProps: {
            disabled: ({ record }) => record?.get('materialLotCode'),
          },
        },
        {
          label: intl.get(`${modelPrompt}.form.materialObj`).d('厂内物料编码'),
          name: 'materialObj',
          lovCode: 'MT.METHOD.MATERIAL',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          computedProps: {
            disabled: ({ record }) => record?.get('outsideMaterialCode'),
          },
        },
        {
          name: 'materialId',
          bind: 'materialObj.materialId',
        },
        {
          name: 'materialCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.materialCode`).d('厂外物料编码'),
          computedProps: {
            disabled: ({ record }) => record?.get('materialId'),
          },
        },
        {
          label: intl.get(`${modelPrompt}.form.teardownTaskTimeFrom`).d('拆解日期从'),
          name: 'teardownTaskTimeFrom',
          type: FieldType.dateTime,
          max: 'teardownTaskTimeTo',
        },
        {
          label: intl.get(`${modelPrompt}.form.teardownTaskTimeTo`).d('拆解日期至'),
          name: 'teardownTaskTimeTo',
          type: FieldType.dateTime,
          min: 'teardownTaskTimeFrom',
        },
        {
          label: intl.get(`${modelPrompt}.form.siteObj`).d('站点'),
          name: 'siteObj',
          lovCode: 'MT.MODEL.SITE',
          type: FieldType.object,
          ignore: FieldIgnore.always,
        },
        {
          name:'siteId',
          bind:'siteObj.siteId',
        },
        {
          label: intl.get(`${modelPrompt}.form.prodLine`).d('产线'),
          name: 'prodLine',
          lovCode: 'MT.MODEL.PRODLINE',
          type: FieldType.object,
          ignore: FieldIgnore.always,
        },
        {
          name:'prodlineId',
          bind: 'prodLine.prodLineId',
        },
        {
          label: intl.get(`${modelPrompt}.form.userOrg`).d('委托人'),
          name: 'userOrg',
          lovCode: 'MT.USER.ORG',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          textField: 'realName',
        },
        {
          name: 'clientId',
          bind: 'userOrg.id',
        },
        {
          name: 'clientName',
          bind: 'userOrg.realName',
        },
        {
          label: intl.get(`${modelPrompt}.form.teardownTaskStatus`).d('拆解任务状态'),
          name: 'teardownTaskStatus',
          lookupCode: 'YP.QIS.TEARDOWN_TASK_STATUS',
          type: FieldType.string,
        },
      ],
    }),
    fields: [
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskNum`).d('拆解任务编码'),
        name: 'teardownTaskNum',
        type: FieldType.string,
      },
      {
        name: 'materialLotType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotType`).d('电芯类型'),
        lookupCode: 'YP_QIS_TEARDOWN_BARCODE_TYPE',
        lovPara: { tenantId },
        disabled: true,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownApplyNum`).d('申请编码'),
        name: 'teardownApplyNum',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.materialLotCode`).d('电芯条码'),
        name: 'materialLotCode',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.model`).d('电芯型号'),
        name: 'model',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.materialCode`).d('物料编码'),
        name: 'materialCode',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.materialName`).d('物料名称'),
        name: 'materialName',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.siteCode`).d('站点'),
        name: 'siteCode',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.prodLineCode`).d('产线'),
        name: 'prodLineCode',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.productFormCode`).d('产品形式'),
        name: 'productFormCode',
        lookupCode: 'YP.QIS.PRODUCT_FORM',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskType`).d('拆解类型'),
        name: 'teardownTaskType',
        lookupCode: 'YP.QIS.TEARDOWN_TASK_TYPE',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownReason`).d('拆解原因'),
        name: 'teardownReason',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.clientName`).d('委托人'),
        name: 'clientName',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.sampleDeliveryTime`).d('送样时间'),
        name: 'sampleDeliveryTime',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownPersonName`).d('拆解员'),
        name: 'teardownPersonName',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskTime`).d('拆解时间'),
        name: 'teardownTaskTime',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.electricVoltage`).d('电量'),
        name: 'electricVoltage',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.operationName`).d('来源工序编码'),
        name: 'operationName',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.operationDesc`).d('来源工序描述'),
        name: 'operationDesc',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskStatus`).d('拆解任务状态'),
        name: 'teardownTaskStatus',
        lookupCode: 'YP.QIS.TEARDOWN_TASK_STATUS',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskScore`).d('拆解评分'),
        name: 'teardownTaskScore',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownTaskResult`).d('审核结果'),
        name: 'teardownTaskResult',
        lookupCode: 'YP.QIS_TASK_REVIEW_RESULT',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.judgeReason`).d('判断理由'),
        name: 'judgeReason',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownReviewedName`).d('审核人'),
        name: 'teardownReviewedName',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.teardownStage`).d('阶段'),
        name: 'teardownStage',
        lookupCode: 'YP.QIS.NC_REPORT_STAGE',
        type: FieldType.string,
      },
      {
        label: intl.get(`${modelPrompt}.table.cancelReason`).d('取消原因'),
        name: 'cancelReason',
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        const { data } = config;
        return {
          ...config,
          data: {
            ...data,
            materialLotCode: data?.materialLotCode || data?.outsideMaterialLotCode,
          },
          url: `${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qis-teardown-task/list/ui`,
          method: 'GET',
          transformResponse: value => {
            let listData: any = {};
            try {
              listData = JSON.parse(value);
            } catch (err) {
              listData = {
                message: err,
              };
            }
            if (!listData.success) {
              return {
                ...listData,
                failed: true,
              };
            }
            return listData;
          },
        };
      },
    },
  });

const cancelReasonDS = () => ({
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'cancelReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消原因'),
      required: true,
    },
  ],
});

export { listPageFactory, cancelReasonDS };
