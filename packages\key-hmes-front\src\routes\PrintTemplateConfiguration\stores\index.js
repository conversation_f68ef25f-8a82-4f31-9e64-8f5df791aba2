/*
 * @Description: 打印模板配置 DS 文件
 * @Author: YinWQ
 * @Date: 2023-07-18 10:34:10
 * @LastEditors: 20379 <EMAIL>
 * @LastEditTime: 2024-01-29 11:59:23
 */
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { isUndefined, isNull } from 'lodash';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.model.print.template.configuration';
const API = `${BASIC.TARZAN_COMMON}/v1/${getCurrentOrganizationId()}`;

const MainTableDS = flag => ({
  autoCreate: true,
  autoQuery: flag !== 'detail',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: () => ({
      url: `${API}/mt-print-functions/list/ui`,
      method: 'GET',
    }),
  },
  queryFields: [
    {
      name: 'printButtonCode',
      type: 'string',
      labelWidth: '120',
      label: intl.get(`${modelPrompt}.printButtonCode`).d('打印按钮编码'),
    },
    {
      name: 'description',
      type: 'string',
      labelWidth: '120',
      label: intl.get(`${modelPrompt}.description`).d('打印按钮描述'),
    },
    {
      name: 'printChannel',
      type: 'string',
      lookupCode: 'MT.COMMON.PRINT_CHANNEL',
      labelWidth: '120',
      label: intl.get(`${modelPrompt}.printChannel`).d('打印渠道'),
    },
  ],
  fields: [
    {
      name: 'printButtonCode',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.printButtonCode`).d('打印按钮编码'),
    },
    {
      name: 'description',
      type: 'string',
      label: intl.get(`${modelPrompt}.description`).d('打印按钮描述'),
    },
    {
      name: 'printChannel',
      type: 'string',
      lookupCode: 'MT.COMMON.PRINT_CHANNEL',
      required: true,
      label: intl.get(`${modelPrompt}.printChannel`).d('打印渠道'),
    },
    {
      name: 'printChannelPath',
      type: 'string',
      label: intl.get(`${modelPrompt}.printChannelPath`).d('打印渠道路径'),
    },
  ],
});

const DetailedLineDS = () => ({
  fields: [
    {
      name: 'printTemplateGroup',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.printTemplateGroup`).d('打印模版组'),
    },
    {
      name: 'printTemplateObject',
      type: 'object',
      required: true,
      ignore: "always",
      lovCode: 'MT.COMMON.PRINT_SELECTABLE_TEMPLATES',
      label: intl.get(`${modelPrompt}.printTemplateCode`).d('打印模板编码'),
    },
    {
      name: 'printTemplateCode',
      bind: 'printTemplateObject.templateCode',
      type: 'string',
      required: true,
      label: intl.get(`${modelPrompt}.printTemplateCode`).d('打印模板编码'),
    },
    {
      name: 'printTemplateUuid',
      bind: 'printTemplateObject.templateUuid',
    },
    {
      name: 'printTemplateName',
      type: 'string',
      required: true,
      bind: 'printTemplateObject.templateName',
      label: intl.get(`${modelPrompt}.printTemplateName`).d('打印模板名称'),
    },
    {
      name: 'printers',
      type: 'string',
      lookupCode: "MT.COMMON.PRINT_ALL_PRINTER",
      multiple: true,
      label: intl.get(`${modelPrompt}.printChannelPath`).d('可选打印机'),
    },
    {
      name: 'workcellLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.stationWorkcellName`).d('工位'),
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: {
        tenantId,
      },
      ignore: "always",
    },
    {
      name: 'workcellId',
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'workcellCode',
      bind: 'workcellLov.workcellCode',
    },
    {
      name: 'customerLov',
      type: 'object',
      label: intl.get('tarzan.aps.supply.balance.view.customerLov').d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      ignore: 'always',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'customerCode',
      bind: 'customerLov.customerCode',
    },
    {
      name: 'supplierLov',
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      type: 'object',
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierId',
      type: 'number',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'siteLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.siteIdLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
    },
    {
      name: 'siteId',
      type: 'number',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.materialId`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return ((isUndefined(record.get('siteId')) ||
          isNull(record.get('siteId')) ) || (
            !isUndefined(record.get('materialCategoryId')) &&
            !isNull(record.get('materialCategoryId'))))
        },
        lovPara({ record }) {
          return {
            tenantId: getCurrentOrganizationId(),
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: 'number',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialCategoryLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.materialCategoryId`).d('物料类别'),
      lovCode: 'MT.METHOD.MATERIAL_CATEGORY',
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return  ((isUndefined(record.get('siteId')) ||
          isNull(record.get('siteId')) ) || (
            !isUndefined(record.get('materialId')) &&
             !isNull(record.get('materialId'))));
        },
        lovPara({ record }) {
          return {
            tenantId: getCurrentOrganizationId(),
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialCategoryId',
      bind: 'materialCategoryLov.materialCategoryId',
    },
    {
      name: 'materialCategoryCode',
      bind: 'materialCategoryLov.categoryCode',
    },
    {
      name: 'docCategory',
      label: intl.get(`${modelPrompt}.docCategory`).d('对象类型'),
      lookupCode: 'MT.DOC_CATEGORY',
    },
    {
      name: 'docTypeLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.docType`).d('单据对象'),
      ignore: "always",
      dynamicProps: {
        lovCode: ({ record }) => {
          const  res = record?.getField('docCategory')?.getLookupData(record.get('docCategory'));
          const tag = res?.tag;
          return tag;
        },
        disabled:  ({ record }) => {
          const  res = record?.getField('docCategory')?.getLookupData(record.get('docCategory'));
          const tag = res?.tag;
          return !tag;
        },
      },
    },
    {
      name: 'docType',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('docCategory')) {
            case 'CONTAINER_TYPE':
              return 'docTypeLov.containerTypeCode';
            default:
              return 'docTypeLov.value';
          }
        },
      },
    },
    {
      name: 'docTypeMeaning',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('docCategory')) {
            case 'CONTAINER_TYPE':
              return 'docTypeLov.containerTypeDescription';
            default:
              return 'docTypeLov.meaning';
          }
        },
      },
    },
  ],
  events: {
    update: ({ record, name}) => {
      if (name === 'siteLov') {
        record.set('materialLov', null);
        record.set('materialCategoryLov', null);
      }
    },
  },
});

export { MainTableDS, DetailedLineDS };
