import React, {useEffect, useState} from 'react';
import {
  Table,
  DataSet,
  YearPicker,
  Button,
  Form,
  Row,
  Col,
  MonthPicker,
  Lov,
  Select,
  Modal,
} from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import {ColumnAlign, TableQueryBarType} from 'choerodon-ui/pro/lib/table/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import request from 'utils/request';
import {ColumnProps} from "choerodon-ui/pro/lib/table/Column";
import { tableDS, lineChartDS, detailDS } from './stores';
import DailyDelivery from "./components/DailyDelivery";
import {useDataSetEvent} from "utils/hooks";

const modelPrompt = 'equipmentOeeReport';
const tenantId = getCurrentOrganizationId();

const EquipmentOeeReport = (props) => {
  const {
    tableDs,
    lineChartDs,
    detailDs,
  } = props;

  const [columns, setColumns] = useState([
    {
      name: 'assetNum',
      align: ColumnAlign.center,
    },
    {
      name: 'assetDesc',
      align: ColumnAlign.center,
    },
  ]);
  const [xData, setXData] = useState<any>([]);
  const [seriesData, setSeriesData] = useState<any>([]);

  useEffect(() => {
    getQueryList();
    lineChartQuery();
  }, [])

  useDataSetEvent(tableDs.queryDataSet, 'update', async({ name, record }) => {
    if (name === 'timePeriod') {
      record.set('startDate', null);
      record.set('endDate', null);
    }
    const validate = await tableDs.queryDataSet.validate();
    if (!validate) {
      return;
    }
    getQueryList();
  });

  useDataSetEvent(lineChartDs, 'update', async({ name, record }) => {
    if (name === 'timePeriod') {
      record.set('startDate', null);
      record.set('endDate', null);
    }
    await lineChartQuery();
  });

  const columnsDetail: ColumnProps[] = [
    {
      name: 'timeActivationRateStr',
    },
    {
      name: 'performanceEfficiencyStr',
    },
    {
      name: 'qualityQualificationRateStr',
    },
  ];

  const handleOpenDetail = value => {
    detailDs.loadData([value]);
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.detail`).d('明细'),
      drawer: false,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: 500,
      },
      children: (
        <Table
          customizedCode="BatteryCellIssueReceiptLedgerDetail"
          dataSet={detailDs}
          columns={columnsDetail}
          virtual
          border
        />
      ),
    });
  };

  // 设备OEE报表数据查询
  const getQueryList = () => {
    const data = tableDs.queryDataSet.toData()[0];
    request(`${BASIC.API_PREFIX}/v1/${tenantId}/asset-info/oee/report`, {
      method: 'POST',
      data: {
        ...data,
      },
    }).then(res => {
      if (res?.length > 0) {
        const dataKeys = Object.keys(res[0]);
        const newColumns = dataKeys.slice(3, dataKeys.length).map((ele) => {
          return {
            title: ele,
            name: ele,
            align: ColumnAlign.center,
            renderer: ({value}) => (<a onClick={()=>{handleOpenDetail(value)}}>{(value && 'oee' in value) ? value.oee : null}</a>),
          }
        });
        setColumns([...columns, ...newColumns]);
        setTimeout(() => {
          tableDs.loadData(res)
        }, 20);
      } else {
        setColumns([...columns]);
        setTimeout(() => {
          tableDs.loadData([]);
        }, 20);
      }
    });
  }

  // 设备OEE趋势图数据查询
  const lineChartQuery = async () => {
    const validate = await lineChartDs.validate();
    if (!validate) {
      return;
    }
    const data = lineChartDs.current.toData();
    request(`${BASIC.API_PREFIX}/v1/${tenantId}/asset-info/oee/report`, {
      method: 'POST',
      data: {
        ...data,
      },
    }).then(res => {
      if (res?.length > 0) {
        const dataKeys = Object.keys(res[0]);
        setXData(dataKeys.slice(3, dataKeys.length));
        setSeriesData(res);
      } else {
        setXData([]);
        setSeriesData([]);
      }
    })
  }

  // 导出
  const handleExport = () => {
    const params = tableDs.queryDataSet.toData()[0];
    request(`${BASIC.API_PREFIX}/v1/${tenantId}/asset-info/oee/export`, {
      method: "POST",
      body: {
        ...params,
      },
      responseType: 'blob',
    }).then((response) => {
      if (response) {
        const fileName = `设备OEE报表`;
        const elink = document.createElement('a');
        elink.download = fileName ? window.decodeURI(fileName) : `设备OEE报表`;
        elink.style.display = 'none';
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        document.body.removeChild(elink);
      }
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('设备OEE报表')}>
        <Button color={ButtonColor.primary} onClick={handleExport}>{intl.get(`${modelPrompt}.export`).d('导出')}</Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            onRefresh: () => {
              getQueryList();
              return false;
            },
          }}
          queryFields={{
            queryYears: <YearPicker />,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="equipmentOeeReport"
          customizedCode="equipmentOeeReport"
          virtual
          style={{ height: '400px' }}
        />
        <div>
          <div style={{marginTop: '1rem'}}>
            <Row gutter={24}>
              <Col span={18}>
                <Form columns={4} dataSet={lineChartDs} labelWidth={120}>
                  <Lov
                    name="equipmentLov"
                  />
                  <MonthPicker name="startDate" />
                  <MonthPicker name="endDate" />
                  <Select name='timePeriod' />
                </Form>
              </Col>
              <Col span={6}>
                <div
                  style={{
                    flexShrink: 0,
                    display: 'flex',
                    alignItems: 'center',
                    marginTop: '0.04rem',
                  }}
                >
                  <Button
                    onClick={() => {
                      lineChartDs.current.reset();
                    }}
                  >
                    {intl.get('hzero.common.button.reset').d('重置')}
                  </Button>
                  <Button
                    onClick={() => lineChartQuery()}
                    color={ButtonColor.primary}
                  >
                    {intl.get('hzero.common.button.search').d('查询')}
                  </Button>
                </div>
              </Col>
            </Row>
          </div>
          <div style={{ height: '500px' }}>
            <DailyDelivery xData={xData} seriesData={seriesData} />
          </div>
        </div>
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      const lineChartDs = new DataSet({
        ...lineChartDS(),
      });
      const detailDs = new DataSet({
        ...detailDS(),
      });
      return {
        tableDs,
        lineChartDs,
        detailDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(EquipmentOeeReport),
);
