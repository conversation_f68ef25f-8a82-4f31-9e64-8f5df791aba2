import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentUser, getCurrentUserId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.qms.MeasuringScrapPlatform';
// const endUrl = '-138685';
const endUrl = '';

const listPageFactory = () =>
  new DataSet({
    autoQuery: false,
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'applicationDocNum',
          label: intl.get(`${modelPrompt}.form.applicationDocNum`).d('注销单号'),
          type: FieldType.string,
        },
        {
          name: 'applicationDocStatus',
          label: intl.get(`${modelPrompt}.form.applicationDocStatus`).d('状态'),
          lookupCode: 'YP.QIS.MS_TOOL_CHANGE_APPLY_STATUS',
          type: FieldType.string,
          lovPara:{
            tenantId: getCurrentOrganizationId(),
          },
        },
        {
          name: 'companyUnit',
          label: intl.get(`${modelPrompt}.form.companyUnit`).d('申请部门'),
          type: FieldType.object,
          lovCode: 'YP.QIS.COMPANY_UNIT',
          lovPara:{
            tenantId: getCurrentOrganizationId(),
          },
          ignore: FieldIgnore.always,
        },
        {
          name: 'departmentId',
          bind: 'companyUnit.unitId',
        },
        {
          name: 'departmentIdName',
          bind: 'companyUnit.unitName',
        },
        {
          name: 'departmentUser',
          label: intl.get(`${modelPrompt}.form.departmentUser`).d('申请人'),
          type: FieldType.object,
          lovPara:{
            tenantId: getCurrentOrganizationId(),
          },
          lovCode: 'YP.QIS.USER.ORG',
          textField: 'realName',
          ignore: FieldIgnore.always,
        },
        {
          name: 'createdBy',
          bind: 'departmentUser.userId',
        },
        {
          name: 'createdName',
          bind: 'departmentUser.realName',
        },
        {
          name: 'startDate',
          label: intl.get(`${modelPrompt}.form.startDate`).d('申请时间从'),
          type: FieldType.dateTime,
          max: 'endDate',
        },
        {
          name: 'endDate',
          label: intl.get(`${modelPrompt}.form.endDate`).d('申请时间至'),
          type: FieldType.dateTime,
          min: 'startDate',
        },
        {
          name: 'modelSite',
          label: intl.get(`${modelPrompt}.form.modelSite`).d('站点'),
          type: FieldType.object,
          lovCode: 'MT.MODEL.SITE',
          lovPara:{
            tenantId: getCurrentOrganizationId(),
          },
          ignore: FieldIgnore.always,
        },
        {
          name: 'siteId',
          bind: 'modelSite.siteId',
        },
      ],
    }),
    fields: [
      {
        name: 'applicationDocNum',
        label: intl.get(`${modelPrompt}.table.applicationDocNum`).d('注销单号'),
        type: FieldType.string,
      },
      {
        name: 'applicationDocStatus',
        label: intl.get(`${modelPrompt}.table.applicationDocStatus`).d('状态'),
        lookupCode: 'YP.QIS.MS_TOOL_CHANGE_APPLY_STATUS',
        type: FieldType.string,
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
        defaultValue: 'NEW',
      },
      {
        name: 'modelSite',
        label: intl.get(`${modelPrompt}.table.modelSite`).d('站点'),
        type: FieldType.object,
        lovCode: 'MT.MODEL.SITE',
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
        ignore: FieldIgnore.always,
        textField: 'siteName',
      },
      {
        name: 'siteId',
        bind: 'modelSite.siteId',
      },
      {
        name: 'siteIdName',
        bind: 'modelSite.siteName',
      },
      {
        name: 'creationDate',
        label: intl.get(`${modelPrompt}.table.creationDate`).d('申请时间'),
        type: FieldType.dateTime,
        defaultValue: new Date(),
      },
      {
        name: 'departmentUser',
        label: intl.get(`${modelPrompt}.form.departmentUser`).d('申请人'),
        type: FieldType.object,
        lovCode: 'YP.QIS.USER.ORG',
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
        textField: 'realName',
        ignore: FieldIgnore.always,
      },
      {
        name: 'createdBy',
        defaultValue: getCurrentUserId(),
        bind: 'departmentUser.userId',
      },
      {
        name: 'createdName',
        defaultValue: getCurrentUser().realName,
        bind: 'departmentUser.realName',
      },
      {
        name: 'companyUnit',
        label: intl.get(`${modelPrompt}.table.companyUnit`).d('申请部门'),
        type: FieldType.object,
        lovCode: 'YP.QIS.COMPANY_UNIT',
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'departmentId',
        bind: 'companyUnit.unitId',
      },
      {
        name: 'departmentIdName',
        bind: 'companyUnit.unitName',
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.remark`).d('备注'),
      },
      {
        name: 'reviewByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.reviewByName`).d('审批人'),
      },
      {
        name: 'reviewDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.table.reviewDate`).d('审批时间'),
      },
      {
        name: 'rejectReason',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.rejectReason`).d('驳回原因'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-apply/list/ui`,
          method: 'GET',
          data: {...config.data, docType: 'SCRAP_DOC'},
        };
      },
    },
  });

export default listPageFactory;
