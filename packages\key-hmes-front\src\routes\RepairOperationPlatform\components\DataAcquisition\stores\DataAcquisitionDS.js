/*
 * @Author: ‘兰宁辉’ ‘<EMAIL>’
 * @Date: 2023-04-20 10:09:51
 * @LastEditors: ‘兰宁辉’ ‘<EMAIL>’
 * @LastEditTime: 2023-04-23 19:25:55
 * @FilePath: \guanjiansm\packages\key-hmes-front\src\routes\RepairOperationPlatform\components\DataAcquisition\stores\DataAcquisitionDS.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @Description: 数据收集组DS
 * @Author: <EMAIL>
 * @Date: 2023-03-14 10:19:56
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.materialPreventError';
const tenantId = getCurrentOrganizationId();

// 物料防呆防错详情ds
const tableDS = () => ({
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'seq',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyCode`).d('序号'),
      renderer: ({ record }) => {
        return record.index + 1;
      },
      // required: true,
    },
    {
      name: 'tagCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCode`).d('数据项编码'),
      // required: true,
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('数据项描述'),
      // required: true,
    },
    {
      name: 'typeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.typeDesc`).d('类型'),
    },
    {
      name: 'minimumValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.minimumValue`).d('下限'),
    },
    {
      name: 'standardValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardValue`).d('标准值'),
    },
    {
      name: 'maximalValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maximalValue`).d('上限'),
    },
    {
      name: 'tagValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagValue`).d('结果'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-rework-station/tag/list/ui`,
        method: 'POST',
      };
    },
  },
  // data: [
  //   {
  //     valueType: 'VALUE',
  //   },
  //   {
  //     valueType: 'VALUE',
  //   },
  //   {
  //     valueType: 'VALUE',
  //   },
  // ],
});

const addDs = () => ({
  fields: [
    {
      name: 'workOrder',
      type: FieldType.object,
      lovCode: 'HME.REWORK_TAG',
      lovPara: { tenantId },
      // noCache: true,
      ignore: 'always',
      multiple: true,
      // required: true,
    },
    {
      name: 'workOrderId',
      type: FieldType.string,
      bind: ' workOrder.workOrderId',
    },
  ],
});

export { tableDS, addDs };
