/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-12-14
 * @description 执行执行规则维护-详情页
 */
import React, { FC, useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  Form,
  Select,
  Table,
  Lov,
  Switch,
  TextField,
  Modal,
  NumberField,
} from 'choerodon-ui/pro';
import { Badge, Popconfirm, Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import myInstance from '@utils/myAxios';
import { Header, Content } from 'components/Page';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import uuid from 'uuid/v4';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { detailDS } from '../stories/detailDs';
import { tableDS } from '../stories/tableDs';
import { tableDetailDS } from '../stories/tableDetailDs';
import '../index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.commonConfig.orderExecuteRuleMes';

interface ControlDetailProps extends RouteComponentProps {
  dataSet: DataSet;
  customizeForm: any;
  customizeTable: any;
}

const ControlDetail: FC<ControlDetailProps> = (props) => {

  const {
    history,
    match: { path, params },
    customizeForm,
    customizeTable,
  } = props;

  const formDs: DataSet = useMemo(() => new DataSet(detailDS()), []);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const tableDetailDs = useMemo(() => new DataSet(tableDetailDS()), []);

  // eslint-disable-next-line prefer-destructuring
  const id = (params as any).id;
  const create = id === 'create';
  const [saveLoading, setSaveLoading] = useState(false);
  const [canEdit, setCanEdit] = useState(create);

  const changeStatus = () => {
    setCanEdit(true);
  };

  const queryTable = async instructionDocExeRuleId => {
    formDs.queryParameter = {
      instructionDocExeRuleId,
    };
    tableDs.queryParameter = {
      instructionDocExeRuleId,
    };
    formDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.BASIC`);
    await formDs.query();
    const data = formDs.toData();
    // @ts-ignore
    if (data[0].businessObj?.typeCode === 'NO_INSTRUCTION_DOC') {
      formDs.current!.set('instructionDocExeStrategy', 'No_Exe_Strategy');
    }
    tableDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DETAIL,${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DRAWER`);
    await tableDs.query();
    setSaveLoading(false);
  };

  useEffect(() => {
    if (!create) {
      queryTable(id);
    }
  }, []);

  const handleSaveList = async () => {
    formDs!.current!.set('newDate', new Date());
    const data = await formDs!.current!.toData();
    const list: Array<any> = await tableDs.toData();
    const instructionExeRuleIds = tableDs.destroyed.map(item => {
      return item?.toData()?.instructionExeRuleId;
    });
    const { instructionDocExeStrategy } = data;
    if (instructionDocExeStrategy === 'ONE_STEP' && list.length !== 1) {
      notification.warning({
        description: intl
          .get(`${modelPrompt}.notification.oneStepMessage`)
          .d('一步法时至少创建一条行数据'),
      });
      return;
    }
    if (instructionDocExeStrategy === 'TWO_STEP' && list.length !== 2) {
      notification.warning({
        description: intl
          .get(`${modelPrompt}.notification.twoStepMessage`)
          .d('两步法时至少创建两条行数据'),
      });
      return;
    }
    let minSequenceObj: any = { sequence: 1000000000 }
    list.forEach(j => {
      if (Number(j.sequence) < Number(minSequenceObj.sequence)) {
        minSequenceObj = JSON.parse(JSON.stringify(j))
      }
      j.enableFlagCheck = 'UNLIMITED';
      j.executedMaterialLotStatus = 'USABLE';
      j.freezeFlagCheck = 'UNLIMITED';
      j.inspectionNodeFlag = 'N';
      j.materialLotStatusCheck = 'USABLE';
      j.qualityStatusCheck = ['OK', 'NG', 'PENDING'];
      j.revisionCheck = 'STRONG_CHECK';
    });
    if (instructionDocExeStrategy === 'TWO_STEP') {
      if (!minSequenceObj.onPassageLocatorDirection && !(minSequenceObj.onPassageLocatorType || []).length && !minSequenceObj.onPassageLocatorCode) {
        notification.warning({
          description: intl
            .get(`${modelPrompt}.notification.validateMessage`)
            .d('最小的行上的在途库位方向、在途库位类型、在途库位不能同时为空。'),
        });
        return;
      }
    }
    getResult().then(response => {
      if (response) {
        const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-instruction-doc-exe-rules/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DETAIL,${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DRAWER`;
        myInstance
          .post(url, {
            ...data,
            instructionExeRuleList: list,
            instructionExeRuleIds,
          })
          .then(res => {
            if (res.data.success) {
              notification.success({});
              queryTable(res.data.rows.instructionDocExeRuleId);
              setCanEdit(false);
              history.push(
                `/apex-hmes/commonConfig/order-execute-rule/detail/${res.data.rows.instructionDocExeRuleId}`,
              );
            } else if (res.data.message) {
              notification.error({
                description: res.data.message,
              });
            }
          });
      }
    });
  };

  const getResult = async () => {
    // eslint-disable-next-line no-return-await
    return await formDs.validate(false, true);
  };

  const onCancel = async () => {
    if (id === 'create') {
      history.push(`/apex-hmes/commonConfig/order-execute-rule/list`);
    } else {
      queryTable(id);
      setCanEdit(false);
    }
  };

  // 定义弹窗
  let _modal;

  // 关闭弹窗
  const closeModal = () => {
    _modal.close();
  };

  // 分配组员抽屉中-保存
  const saveMember = async () => {
    _modal.update({
      footer: handleFooter(true),
    });
    tableDetailDs!.current!.set('newDate', new Date());
    const validate = await tableDetailDs.validate(false, true);
    if (validate) {
      let data: any = tableDetailDs.toData()[0];
      data = {
        ...data,
        onPassageLocatorId: data.onPassageLocatorId || '',
        onPassageLocatorCode: data.onPassageLocatorCode || '',
        onPassageLocatorType: data.onPassageLocatorType || '',
        onPassageLocatorTypeDesc: data.onPassageLocatorTypeDesc || '',
        onPassageLocatorDirection: data.onPassageLocatorDirection || '',
        onPassageLocatorDirectionDesc: data.onPassageLocatorDirectionDesc || '',
      }
      // @ts-ignore
      const tableDataSequence = tableDs.toData().map(item => {
        if (
          // @ts-ignore
          item.instructionExeRuleId !== data.instructionExeRuleId ||
          // @ts-ignore
          item.uuid !== data.uuid
        ) {
          // @ts-ignore
          return item.sequence;
        }
        return '';

      });
      if (tableDataSequence.indexOf(data.sequence) > -1) {
        notification.error({
          description: intl.get(`${modelPrompt}.sequenceRepetition`).d('sequence重复'),
        });
        _modal.update({
          footer: handleFooter(false),
        });
        return;
      }
      if (data.instructionExeRuleId || data.uuid) {
        tableDs.forEach(item => {
          if (
            item.get('instructionExeRuleId') &&
            item.get('instructionExeRuleId') === data.instructionExeRuleId
          ) {
            Object.keys(data).forEach(i => item.set(i, data[i]));
          } else if (item.get('uuid') === data.uuid) {
            Object.keys(data).forEach(i => item.set(i, data[i]));
          }
        });
      } else {
        tableDs.create(
          {
            ...data,
            uuid: uuid(),
            toleranceFlag: data.toleranceFlag || 'N',
            addStrategyFlag: data.addStrategyFlag || 'N',
            initialFlag: data.initialFlag || 'N',
          },
          0,
        );
      }
      _modal.close();
    } else {
      _modal.update({
        footer: handleFooter(false),
      });
    }
  };
  const handleFooter = (val: boolean) => {
    return (
      <>
        <div style={{ float: 'right' }}>
          <Button onClick={closeModal}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
          <Button
            onClick={saveMember}
            type={ButtonType.submit}
            color={ButtonColor.primary}
            loading={val}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      </>
    );
  };

  // 允差类型清空时，上下允差也清空
  const changeToleranceType = val => {
    if (val!) {
      tableDetailDs.current?.init('toleranceMaxValue');
      tableDetailDs.current?.init('toleranceMinValue');
    }
  };

  // 允许允差从Y变N时，下面三个字段都清空
  const changeToleranceFlag = val => {
    if (val === 'N') {
      tableDetailDs.current?.set('toleranceTypeDesc', null);
      tableDetailDs.current?.set('toleranceType', null);
      tableDetailDs.current?.set('toleranceMaxValue', null);
      tableDetailDs.current?.set('toleranceMinValue', null);
    }
  };

  const onPassageLocatorObjChange = (val) => {
    if (!val) {
      onPassageLocatorDirectionObjChange()
    }
    tableDetailDs.current?.set('onPassageLocatorTypeObj', null);
    tableDetailDs.current?.set('onPassageLocatorDirectionObj', null);
  };

  const onPassageLocatorDirectionObjChange = () => {
    tableDetailDs.current?.set('onPassageLocatorObj', null);
  };

  const onChangeOnPassageLocatorTypeObj = () => {
    tableDetailDs.current?.set('onPassageLocatorObj', null);
  };

  const handleAddAssemblePoint = async record => {
    if (!record.data) {
      let maxLineNumber = 0;
      const data = tableDs.toData();
      data.forEach((item: any) => {
        if (maxLineNumber < item.sequence) {
          maxLineNumber = item.sequence;
        }
      });
      tableDetailDs.loadData([{}]);
      tableDetailDs.current!.set('sequence', parseInt(String(maxLineNumber / 10), 10) * 10 + 10);
      tableDetailDs.current!.set('instructionDocExeStrategy', formDs!.current!.toData().instructionDocExeStrategy);
    } else {
      const data = await record.toData()
      const id = Number(data.onPassageLocatorId) !== 0 ? data.onPassageLocatorId : undefined;
      tableDetailDs.loadData([{
        ...data,
        onPassageLocatorId: id,
      }]);
      tableDetailDs.current!.set('instructionDocExeStrategy', formDs!.current!.toData().instructionDocExeStrategy);
    }
    _modal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.distributionMember`).d('指令规则维护'),
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        customizeForm(
          {
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DRAWER`,
          },
          <Form dataSet={tableDetailDs} columns={2} labelWidth={112}>
            <Lov name='instruction' placeholder=' ' />
            <Lov name='business' placeholder=' ' />
            <Select name='instructionExeStrategyObj' />
            <NumberField name='sequence' precision={0} step={1} />
            <Switch name='addStrategyFlag' />
            <Switch name='toleranceFlag' onChange={changeToleranceFlag} />
            <Select name='toleranceTypeObj' onChange={changeToleranceType} />
            <NumberField name='toleranceMaxValue' precision={0} />
            <NumberField name='toleranceMinValue' precision={0} />
            <Select name='onPassageLocatorDirectionObj' onChange={onPassageLocatorDirectionObjChange} />
            <Select name='onPassageLocatorTypeObj' onChange={onChangeOnPassageLocatorTypeObj} />
            <Lov name='onPassageLocatorObj' placeholder=' ' onChange={onPassageLocatorObjChange} />
            <Switch name='transferFlag' />
            <Switch name='initialFlag' />
          </Form>,
        )
      ),
      footer: handleFooter(false),
    });
  };
  const deleteRecord = record => {
    tableDs.remove(record);
  };

  const column: ColumnProps[] = useMemo(() => {
    return [
      {
        header: () => (
          <PermissionButton
            type='c7n-pro'
            icon='add'
            disabled={!canEdit}
            onClick={handleAddAssemblePoint}
            funcType='flat'
            shape='circle'
            size='small'
          />
        ),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              deleteRecord(record);
            }}
          >
            <PermissionButton
              type='c7n-pro'
              icon='remove'
              disabled={!canEdit}
              funcType='flat'
              shape='circle'
              size='small'
            />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'sequence',
        width: 80,
        align: ColumnAlign.left,
        renderer: ({ record }) => {
          return (
            <a
              onClick={() => {
                handleAddAssemblePoint(record);
              }}
              disabled={!canEdit}
            >
              {record!.get('sequence')}
            </a>
          );
        },
      },
      {
        name: 'instructionTypeDesc',
      },
      {
        name: 'businessTypeDesc',
      },
      {
        name: 'instructionExeStrategyDesc',
      },
      {
        name: 'addStrategyFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          return (
            // @ts-ignore
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
      {
        name: 'toleranceFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          return (
            // @ts-ignore
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.enable').d('启用')
                  : intl.get('tarzan.common.label.disable').d('禁用')
              }
            />
          );
        },
      },
      {
        name: 'toleranceTypeDesc',
        align: ColumnAlign.center,
      },
      {
        name: 'toleranceMaxValue',
        align: ColumnAlign.right,
      },
      {
        name: 'toleranceMinValue',
        align: ColumnAlign.right,
      },
      {
        name: 'onPassageLocatorDirection',
        align: ColumnAlign.left,
        width: 130,
        renderer: ({ record }) => {
          // @ts-ignore
          return record.data.onPassageLocatorDirectionDesc;
        },
      },
      {
        name: 'onPassageLocatorType',
        align: ColumnAlign.left,
        width: 130,
        renderer: ({ record }) => {
          // @ts-ignore
          return (record.data.onPassageLocatorTypeDesc || []).toString();
        },
      },
      {
        name: 'onPassageLocatorId',
        align: ColumnAlign.left,
        width: 100,
        renderer: ({ record }) => {
          // @ts-ignore
          return record.data.onPassageLocatorCode;
        },
      },
      // {
      //   name: 'accountCategoryDesc',
      //   align: ColumnAlign.center,
      // },
      {
        name: 'transferFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          return (
            // @ts-ignore
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
      {
        name: 'initialFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          return (
            // @ts-ignore
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`tarzan.common.label.yes`).d('是')
                  : intl.get(`tarzan.common.label.no`).d('否')
              }
            />
          );
        },
      },
    ];
  }, [canEdit]);

  const changeInstruction = val => {
    if (val === 'ONE_STEP') {
      formDs.current?.init('instructionCreateMode');
      formDs.current?.init('onPassageLocatorType');
      formDs.current?.init('onPassageLocatorDirection');
    }
    tableDs.loadData([]);
  };

  const changeLov = val => {
    if (val.typeCode === 'NO_INSTRUCTION_DOC') {
      formDs.current!.set('instructionDocExeStrategy', 'No_Exe_Strategy');
      formDs.current!.set(
        'instructionDocExeStrategyDesc',
        intl.get(`${modelPrompt}.NoExeStrategy`).d('无执行策略'),
      );
    } else {
      formDs.current?.init('instructionDocExeStrategy');
    }
  };
  return (
    <div className='hmes-style'>
      <Header
        title={intl.get(`${modelPrompt}.title.order-execute-rule`).d('指令执行规则维护')}
        backPath='/apex-hmes/commonConfig/order-execute-rule/list'
      >
        {canEdit ? (
          <>
            <PermissionButton
              type='c7n-pro'
              color={ButtonColor.primary}
              icon='save'
              onClick={handleSaveList}
              loading={saveLoading}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button color={ButtonColor.default} icon='close' onClick={onCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type='c7n-pro'
            color={ButtonColor.primary}
            icon='edit-o'
            onClick={changeStatus}
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.billsRule`).d('指令单据规则')}
            key='basicInfo'
            dataSet={formDs}
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.BASIC`,
              },
              <Form dataSet={formDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <Lov name='businessObj' required onChange={changeLov} />
                <TextField name='description' disabled />
                <Lov name='statusGroupObj' placeholder=' ' />
                <Select name='instructionDocExeStrategy' required onChange={changeInstruction} />
                <Select name='instructionCreateMode' />
                <Switch name='fromLocatorRequiredFlag' />
                <Switch name='toLocatorRequiredFlag' />
                <Switch name='initialFlag' disabled />
                <Select name='accountCategoryObj' />
              </Form>,
            )}
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.title.orderRule`).d('指令规则')}
            key='location'
            dataSet={tableDs}
          >
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DETAIL`,
              },
              <Table
                dataSet={tableDs}
                columns={column as ColumnProps[]}
                filter={record => record.status !== 'delete'}
              />,
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.commonConfig.orderExecuteRuleMes', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DETAIL`, `${BASIC.CUSZ_CODE_BEFORE}.INSTRU_DOC_EXE_RULE_DETAIL.DRAWER`],
  // @ts-ignore
})(ControlDetail));
