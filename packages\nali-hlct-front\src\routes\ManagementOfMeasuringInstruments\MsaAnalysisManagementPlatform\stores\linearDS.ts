import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import {getCurrentOrganizationId, getCurrentUser} from 'utils/utils';

const modelPrompt = 'tarzan.inspectExecute.msaAnalysisManagementPlatform';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();


const formDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'checkLocation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.checkLocation`).d('测量位置'),
      required: true,
    },
    {
      name: 'expectedDeterioration',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.expectedDeteriorationLiner`).d('预期过程变差(或1/6公差)'),
      required: true,
    },
    {
      name: 'sampleDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleDescription`).d('基准件名称'),
    },
    {
      name: 'improveByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.measuredBy`).d('测量人'),
      lovCode: 'YP.QIS.UNIT_USER',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'measuredBy',
      bind: 'improveByObj.id',
      // defaultValue: userInfo.id,
    },
    {
      name: 'measuredByName',
      bind: 'improveByObj.realName',
      // defaultValue: userInfo.realName,
    },
  ],
});


export { formDS };
