/**
 * @Description: 体系审核管理维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import intl from 'utils/intl';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { drawerPropsC7n } from '@components/tarzan-ui';
import {
  DataSet,
  Form,
  Button,
  Select,
  Table,
  DateTimePicker,
  Tooltip,
  TextField,
  Spin,
  Modal,
  Icon,
  Lov,
} from 'choerodon-ui/pro';
import { Badge, Menu, Card, Tag } from 'choerodon-ui';
import { getCurrentUser } from 'utils/utils';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import notification from 'utils/notification';
import styles from '../index.modules.less';
import {
  auditProblemFormDS,
  distributeDS,
  measureDS,
  reasonAnalysisDS,
  influenceRangeDS,
  changeObjectDS,
  validateCloseDS,
  problemInfoDS,
} from '../stores/AuditTab5ProblemDS';
import noDataImg from '@/assets/noData.png';
import ProblemCreateDraw from './ProblemCreateDraw';

import {
  fetchAuditProblemListConfig,
  saveAuditProblemConfig,
  fetchAuditProblemBaseDetailConfig,
  fetchEvaluationConfig,
  fetchCategoryDetailConfig,
} from '../services';

const modelPrompt = 'tarzan.systemAudit';

const AuditProblem = props => {
  const {
    id,
    canEdit,
    activeKey,
    threeResList,
    pubFlag,
    commitAuditProblem,
    setProblemStatus,
  } = props;

  // 查询审核问题列表
  const fetchAuditProblemList = useRequest(fetchAuditProblemListConfig(), {
    manual: true,
    needPromise: true,
  });

  // 查询审核额外信息
  const fetchCategoryDetail = useRequest(fetchCategoryDetailConfig(), {
    manual: true,
    needPromise: true,
  });

  // 查询审核问题基础信息
  const fetchAuditProblemBaseDetail = useRequest(fetchAuditProblemBaseDetailConfig(), {
    manual: true,
    needPromise: true,
  });

  // 查询验证关闭
  const fetchEvaluation = useRequest(fetchEvaluationConfig(), {
    manual: true,
    needPromise: true,
  });

  // 保存审核问题
  const saveAuditProblem = useRequest(saveAuditProblemConfig(), {
    manual: true,
    needPromise: true,
  });

  const [user] = useState(getCurrentUser()); // 用户详细信息

  const [menuSwitch, setMenuSwitch] = useState(true); // 菜单开关

  const [problemList, setProblemList] = useState<Array<any>>([]); // 审核记录列表

  const [selectIndex, setSelectIndex] = useState<Array<any>>([]); // 选中的列表

  const formDs = useMemo(() => new DataSet(auditProblemFormDS()), []);
  const modalDs = useMemo(() => new DataSet(problemInfoDS()), []);

  // 责任小组
  const distributeDs = useMemo(() => new DataSet(distributeDS()), []);
  // 临时措施
  const temporaryDs = useMemo(() => new DataSet(measureDS()), []);
  // 原因分析
  const reasonAnalysisDs = useMemo(() => new DataSet(reasonAnalysisDS()), []);
  // 止呼待范围
  const influenceRangeDs = useMemo(() => new DataSet(influenceRangeDS()), []);
  // 长期措施
  const permanentlyDs = useMemo(() => new DataSet(measureDS()), []);
  // 变更对象
  const changeObjectDs = useMemo(() => new DataSet(changeObjectDS()), []);
  // 验证关闭
  const validateCloseDs = useMemo(() => new DataSet(validateCloseDS()), []);

  useEffect(() => {
    if (activeKey === 'AuditTab5Problem') {
      queryProblemList('');
      setSelectIndex([]);
    }
  }, [id, activeKey]);

  const handleModalCancel = () => {
    modalDs.loadData([]);
  };

  const handleModalSave = async () => {
    const validateResult = await modalDs.validate();
    if (!validateResult) {
      return false;
    }

    const formData: any = modalDs.toData()[0];
    formData.proposeTimePeriod = `${formData?.proposeTimePeriod?.start}~${formData?.proposeTimePeriod?.end}`;
    const res = await saveAuditProblem.run({
      params: formData,
      queryParams: {
        sysReviewPlanId: id,
      },
    });
    if (res?.success) {
      // @ts-ignore
      notification.success();
      queryProblemList('');
      if (res?.rows) {
        props.history.push(
          `/hwms/problem-management/problem-management-platform/dist/${res?.rows}`,
        );
      }
    }
  };

  const queryProblemList = async problemId => {
    const res = await fetchAuditProblemList.run({
      params: {
        sysReviewPlanId: id,
      },
    });

    const problemIdList: any = [];

    const newProblemList = (res?.rows || []).map(item => {
      problemIdList.push(`${item.problemId}`);
      return {
        ...item,
        key: item.problemId,
      };
    });

    setProblemList(newProblemList);
    if (problemId) {
      selectListClick({ key: problemId });
    } else if (problemIdList[0]) {
      selectListClick({ key: problemIdList[0] });
    } else {
      selectListClick({ key: null });
    }
  };

  // 登记问题
  const addProblem = () => {
    modalDs.loadData([
      {
        problemCategory: 'PREVIEW',
        sysReviewType: threeResList[0]?.rows?.sysReviewType,
        registerTime: new Date(),
        problemStatus: 'DRAFT',
        registerPerson: user.id,
        registerPersonRealName: user.realName,
        // proposePerson: user.id,
        // proposePersonRealName: user.realName,
      },
    ]);
    Modal.open({
      ...drawerPropsC7n({}),
      destroyOnClose: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.registrationProblem`).d('登记问题'),
      drawer: true,
      style: {
        width: '360px',
      },
      className: 'hmes-style-modal',
      children: <ProblemCreateDraw formDs={modalDs} />,
      onOk: handleModalSave,
      afterClose: handleModalCancel,
    });
  };

  // 责任小组
  const columnsDistribute: ColumnProps[] = [
    { name: 'sequence' },
    {
      name: 'responsiblePersonLov',
    },
    { name: 'unitCompanyName' },
    { name: 'positionName' },
    { name: 'phone' },
    {
      name: 'principalPersonFlag',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
  ];

  // 临时措施,长期措施
  const columnsMeasure: ColumnProps[] = [
    { name: 'sequence', align: ColumnAlign.left, width: 70 },
    {
      name: 'measureDescription',
    },
    {
      name: 'measureVerification',
    },
    {
      name: 'responsiblePersonLov',
      width: 80,
    },
    {
      name: 'planEndTime',
      width: 150,
      align: ColumnAlign.center,
    },
    {
      name: 'actualEndTime',
      width: 150,
      align: ColumnAlign.center,
    },
    {
      name: 'enableFlag',
      width: 100,
      align: ColumnAlign.center,

      renderer: ({ value }) => {
        if (!value) {
          return;
        }
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`${modelPrompt}.label.enable`).d('有效')
                : intl.get(`${modelPrompt}.label.disable`).d('无效')
            }
          />
        );
      },
    },
    {
      name: 'measureStatus',
      width: 90,
      renderer: ({ record, value }) => renderMeasureStatusTag(value, record),
    },
    {
      name: 'evidence',
      width: 120,
      align: ColumnAlign.center,
    },
  ];

  // 原因分析
  const columnsReasonAnalysis: ColumnProps[] = [
    { name: 'sequence', align: ColumnAlign.left, width: 70 },
    {
      name: 'occurrenceReason',
    },
    {
      name: 'outflowReason',
    },
    {
      name: 'planEndTime',
      width: 150,
      align: ColumnAlign.center,
    },
    { name: 'responsiblePersonLov', width: 80 },
    {
      name: 'recordTime',
      width: 150,
      align: ColumnAlign.center,
    },
    {
      name: 'rationalityFlag',
      width: 100,
      align: ColumnAlign.center,
      renderer: ({ value }) => {
        if (!value) {
          return;
        }
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`${modelPrompt}.label.enable`).d('合理')
                : intl.get(`${modelPrompt}.label.disable`).d('不合理')
            }
          />
        );
      },
    },
    {
      name: 'reasonStatus',
      width: 90,
      renderer: ({ record, value }) => renderReasonStatusTag(value, record),
    },
    {
      name: 'enclosure',
      width: 120,
      align: ColumnAlign.center,
    },
  ];

  // 止呼待范围
  const influenceRangeColumns: ColumnProps[] = [
    { name: 'sequence', align: ColumnAlign.left, width: 70 },
    {
      name: 'supplierLov',
      width: 150,
    },
    {
      name: 'manufacturingProcess',
    },
    {
      name: 'marketplace',
    },
    { name: 'responsiblePersonLov', width: 80 },
    { name: 'recordTime', align: ColumnAlign.center, width: 150 },
  ];

  // 变更对象
  const changeObjectColumns: ColumnProps[] = [
    { name: 'sequence', align: ColumnAlign.left, width: 70 },
    {
      name: 'objectType',
      width: 130,
    },
    {
      name: 'objectCode',
      width: 200,
    },
    {
      name: 'objectDescription',
    },
    { name: 'responsiblePersonLov', width: 80 },
    { name: 'recordTime', align: ColumnAlign.center, width: 150 },
    { name: 'measureType' },
  ];

  // 验证关闭
  const validateCloseColumns: ColumnProps[] = [
    {
      name: 'verifyClosedInfo',
      width: 130,
    },
    {
      name: 'verifyClosedPersonRealName',
      width: 200,
    },
    {
      name: 'verifyClosedTime',
    },
    { name: 'enclosure8d' },
  ];

  const getProblemInfo = async key => {
    let res = await fetchAuditProblemBaseDetail.run({
      params: {
        problemId: key,
      },
    });

    let resOther = await fetchCategoryDetail.run({
      params: {
        problemId: key,
      },
    });

    res = res?.rows || {};
    resOther = resOther?.rows?.previewInfo || {};

    setProblemStatus(res.problemStatus);

    formDs.loadData([{ ...res, ...resOther }]);
    distributeDs.setQueryParameter('problemId', key);
    distributeDs.query();

    temporaryDs.setQueryParameter('problemId', key);
    temporaryDs.setQueryParameter('measureType', 'TEMP');
    temporaryDs.query().then(temporaryRes => {
      const { measureInfo } = temporaryRes.rows || {};
      temporaryDs.loadData(measureInfo || []);
    });

    permanentlyDs.setQueryParameter('problemId', key);
    permanentlyDs.setQueryParameter('measureType', 'PERP');
    permanentlyDs.query().then(permanentlyRes => {
      const { changeObjectInfo, measureInfo } = permanentlyRes.rows || {};
      permanentlyDs.loadData(measureInfo || []);
      changeObjectDs.loadData(changeObjectInfo || []);
    });

    reasonAnalysisDs.setQueryParameter('problemId', key);
    reasonAnalysisDs.query().then(reasonAnalysisRes => {
      const { reasonInfo, influenceRangeInfo } = reasonAnalysisRes.rows || {};
      reasonAnalysisDs.loadData(reasonInfo || []);
      influenceRangeDs.loadData(influenceRangeInfo || []);
    });

    const evaluationRes = await fetchEvaluation.run({
      params: { problemId: key },
    });

    validateCloseDs.loadData(evaluationRes?.rows?.responPersonInfo || []);

    setSelectIndex([`${key}`]);
  };

  const selectListClick = ({ key }) => {
    if (key === 'add') {
      addProblem();
      setProblemStatus();
    } else if (key) {
      getProblemInfo(key);
    } else {
      setProblemStatus();
      setSelectIndex([]);
      formDs.loadData([]);
      distributeDs.loadData([]);
      temporaryDs.loadData([]);
      reasonAnalysisDs.loadData([]);
      influenceRangeDs.loadData([]);
      permanentlyDs.loadData([]);
      changeObjectDs.loadData([]);
      validateCloseDs.loadData([]);
    }
  };

  const renderMeasureStatusTag = (value, record) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
        className = 'green';
        break;
      case 'EVALUATING':
        className = 'yellow';
        break;
      default:
        className = 'blue';
    }
    return <Tag color={className}>{record!.getField('measureStatus')!.getText()}</Tag>;
  };

  const renderReasonStatusTag = (value, record) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
        className = 'green';
        break;
      case 'EVALUATING':
        className = 'yellow';
        break;
      default:
        className = 'blue';
    }
    return <Tag color={className}>{record!.getField('reasonStatus')!.getText()}</Tag>;
  };

  const getAuth = () => {
    let memberInfoId: any = [];
    if (threeResList[2] && threeResList[2].rows?.reviewScheduleList) {
      const reviewScheduleList = threeResList[2].rows.reviewScheduleList || [];
      reviewScheduleList.forEach(item => {
        memberInfoId = [...memberInfoId, ...(item.memberInfoId || [])];
      });
    }
    return memberInfoId.includes(user.id);
  };

  return (
    <div className={styles.tabsBody}>
      <Spin spinning={fetchAuditProblemList.loading || commitAuditProblem.loading}>
        <div className="audit-record">
          <div
            className="audit-record-list"
            style={{
              display: menuSwitch ? 'block' : 'none',
            }}
          >
            <Menu
              style={{ width: '100%' }}
              defaultOpenKeys={['sub1']}
              mode="inline"
              selectedKeys={selectIndex}
              onClick={selectListClick}
            >
              <Menu.Item disabled key="title">
                <div className="record-list-title">
                  {intl.get(`${modelPrompt}.problemList`).d('审核问题列表')}
                </div>
              </Menu.Item>
              {problemList &&
                problemList.length > 0 &&
                problemList.map(item => (
                  <Menu.Item disabled={canEdit} key={item.key}>
                    <div className="record-list-item">
                      <Tooltip title={`${item.displayCode}`} placement="right">
                        <div className="record-list-item-text">{item.displayCode} </div>
                      </Tooltip>
                    </div>
                  </Menu.Item>
                ))}
              <Menu.Item disabled={canEdit || pubFlag || !getAuth()} key="add">
                <div className="record-list-add">
                  <Button disabled={canEdit || !getAuth()} funcType={FuncType.link} icon="add">
                    {intl.get(`${modelPrompt}.createProblem`).d('生成问题')}
                  </Button>
                </div>
              </Menu.Item>
            </Menu>
          </div>

          {problemList?.length > 0 && (
            <div className="audit-record-detial">
              <div className="control-row">
                <div>
                  <div className="menu-switch-icon">
                    <div
                      onClick={() => {
                        setMenuSwitch(!menuSwitch);
                      }}
                    >
                      <Icon
                        type={menuSwitch ? 'caidanzhedie' : 'caidanzhankai'}
                        style={{ fontSize: 18 }}
                      />
                    </div>
                  </div>
                </div>
                <div></div>
              </div>
              <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
                <TextField name="problemCode" />
                <TextField name="problemTitle" />
                <Select name="problemStatus" />
                <TextField name="registerPersonRealName" />

                <DateTimePicker name="registerTime" />
                <TextField name="proposePersonRealName" />
                <TextField name="proposePersonCompanyName" />
                <DateTimePicker name="proposeTimePeriod" />

                <Select name="findingMethod" />
                <Select name="frequency" />
                <TextField name="leadPersonRealName" />
                <TextField name="problemProperty" />

                <TextField name="influenceRange" />
                <Lov name="sysReviewStandardLov" />
                <TextField name="conformityLevel" />
                <TextField name="projectName" />

                <Select name="projectPhase" />
                <TextField name="siteName" />
                <TextField name="prodLineName" />
                <TextField name="processWorkcellName" />

                <TextField name="equipmentName" />
                <TextField name="problemDescription" />
                <Select name="problemType" />
                <Select name="influenceLevel" />

                <Select name="severityLevel" />
                <TextField name="responsiblePersonUnitCompanyName" />
                <Select name="solutionTool" />
              </Form>

              <Table
                header={intl.get(`${modelPrompt}.responsibilityGroup`).d('责任小组')}
                customizedCode="shtxshwt1"
                className="audit-record-table"
                dataSet={distributeDs}
                columns={columnsDistribute}
              ></Table>

              <Table
                header={intl.get(`${modelPrompt}.interimMeasure`).d('临时措施')}
                customizedCode="shtxshwt2"
                className="audit-record-table"
                dataSet={temporaryDs}
                columns={columnsMeasure}
              ></Table>

              <Table
                header={intl.get(`${modelPrompt}.causeAnalysis`).d('原因分析')}
                customizedCode="shtxshwt3"
                className="audit-record-table"
                dataSet={reasonAnalysisDs}
                columns={columnsReasonAnalysis}
              ></Table>

              <Table
                header={intl.get(`${modelPrompt}.stopCallRange`).d('止呼待范围')}
                customizedCode="shtxshwt4"
                className="audit-record-table"
                dataSet={influenceRangeDs}
                columns={influenceRangeColumns}
              ></Table>
              <Table
                header={intl.get(`${modelPrompt}.longTermMeasures`).d('长期措施')}
                customizedCode="shtxshwt5"
                className="audit-record-table"
                dataSet={permanentlyDs}
                columns={columnsMeasure}
              ></Table>
              <Table
                header={intl.get(`${modelPrompt}.changeObjectList`).d('变更对象列表')}
                customizedCode="shtxshwt6"
                className="audit-record-table"
                dataSet={changeObjectDs}
                columns={changeObjectColumns}
              ></Table>
              <Table
                header={intl.get(`${modelPrompt}.verificationOff`).d('验证关闭')}
                customizedCode="shtxshwt7"
                className="audit-record-table"
                dataSet={validateCloseDs}
                columns={validateCloseColumns}
              ></Table>
            </div>
          )}

          {(!problemList || !problemList.length) && (
            <div className="audit-record-detial">
              <div
                onClick={() => {
                  setMenuSwitch(!menuSwitch);
                }}
              >
                <Icon
                  type={menuSwitch ? 'caidanzhedie' : 'caidanzhankai'}
                  style={{ fontSize: 18 }}
                />
              </div>
              <Card className="empty-card" bordered={false}>
                <div>
                  <img src={noDataImg} alt={intl.get(`${modelPrompt}.noData`).d('暂无数据')} />
                </div>
                <div>{intl.get(`${modelPrompt}.noData`).d('暂无数据')}</div>
              </Card>
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
};

export default AuditProblem;
