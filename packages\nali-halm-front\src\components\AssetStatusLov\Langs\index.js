/**
 * 多语言
 * <AUTHOR>
 * @date 2021-03-01
 * @version: 0.0.1
 * @copyright: Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLang = key => {
  const PREFIX = 'aatn.assetStatus';
  const LANGS = {
    PREFIX,
    ...getCommonLangs(),
    // alm.common
    ASSET_STATUS: intl.get(`${PREFIX}.view.title.assetStatus`).d('资产状态'),
    OFF_REASON: intl.get(`${PREFIX}.view.title.offReason`).d('停机原因'),
    OFF_TYPE: intl.get(`${PREFIX}.view.title.offType`).d('停机类型'),
  };
  return LANGS[key];
};

export default getLang;
