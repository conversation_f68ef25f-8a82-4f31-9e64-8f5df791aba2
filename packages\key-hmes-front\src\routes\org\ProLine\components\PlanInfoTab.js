/**
 * @Description: 站点维护-计划属性Tab
 * @Author: <<EMAIL>>
 * @Date: 2021-02-03 18:44:43
 * @LastEditTime: 2021-02-05 14:36:04
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, NumberField } from 'choerodon-ui/pro';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.model.org.prodLine';

const PlanInfoTab = props => {
  const { ds, canEdit, columns = 1, focus = true } = props;

  // 改为两位小数
  const handleChangeNumber = (field, value) => {
    ds.current.set(field, typeof value === 'number' && value >= 0 ? value.toFixed(2) : '');
    // const data = {};
    // data[field] = parseFloat(value.toFixed(2));
    // ds.current.set(data);
  };

  return (
    <Form
      disabled={!canEdit || focus}
      dataSet={ds}
      columns={columns}
      labelLayout="horizontal"
      labelWidth={112}
    >
      <NumberField
        name="orderTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('orderTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <NumberField
        name="releaseTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('releaseTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <NumberField
        name="demandTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('demandTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      <NumberField
        name="fixTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('fixTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
      {/* <NumberField
        name="frozenTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={(value) => {
          handleChangeNumber('frozenTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof(value) == 'number' ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}` : ''
        }
      /> */}
      <NumberField
        name="forwardPlanningTimeFence"
        nonStrictStep
        min={0}
        step={1}
        onChange={value => {
          handleChangeNumber('forwardPlanningTimeFence', value);
        }}
        renderer={({ value }) =>
          typeof value === 'number'
            ? `${value.toFixed(2)} ${intl.get(`${modelPrompt}.day`).d('天')}`
            : ''
        }
      />
    </Form>
  );
};

export default PlanInfoTab;
