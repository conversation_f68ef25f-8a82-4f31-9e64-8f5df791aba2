/* eslint-disable array-callback-return */
/* eslint-disable jsx-a11y/alt-text */
// 追溯确认
import React, { useState, useEffect, useMemo } from 'react';
import { TextField, DataSet, Tree } from 'choerodon-ui/pro';
// import uuid from 'uuid/v4';
// import { isEmpty } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import deleteLine from '@/assets/operationPlatformCard/deleteLine.png';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import formatterCollections from 'utils/intl/formatterCollections';
import { treeDS, formAddDS } from './stores/TraceabilityConfirmationDS';
import { useOperationPlatform } from '../../contextsStore';
import { CardLayout } from '../commonComponents';
import styles from './index.modules.less';

const tenantId = getCurrentOrganizationId();

const TraceabilityConfirmation = props => {
  const treeDs = useMemo(
    () =>
      new DataSet({
        ...treeDS(),
      }),
    [],
  );
  const { workOrderData } = useOperationPlatform();
  const formAddDs = useMemo(() => new DataSet(formAddDS()), []);
  // const [traceInformation, setTraceInformation] = useState([]); // 工单数据
  const [checkedKeys, setCheckedKeys] = useState([]); // 工单数据
  // const [initChange, setInitChange] = useState(false); // 是否对最后一行进行编辑
  const [loading, setLoading] = useState(false);

  // 查询树
  useEffect(() => {
    if (workOrderData?.eoId) {
      initData();
    }
  }, [workOrderData]);

  const initData = (type, highLights) => {
    treeDs.setQueryParameter('routerStepId', workOrderData?.routerStepId);
    treeDs.setQueryParameter('eoId', workOrderData?.eoId);
    setCheckedKeys([]);
    // setTraceInformation([]);
    treeDs.query().then((res) => {
      if (res && !res.failed) {
        if(type === 'add'){
          formAddDs.current.reset();
          res.forEach(item => {
            if(item.traceId === highLights[0]){
              setCheckedKeys([item]); // 有待调整，默认新增的那一条
            }
          })
        }
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          recordType: 'query',
          message: `表单数据查询成功`,
        });
      } else {
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'FAIL',
          recordType: 'query',
          message: `表单数据查询失败`,
        });
      }
    });
  }

  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  // 树节点
  const nodeRenderer = ({ record }) => {
    // 子节点
    if (record.get('seqNum')) {
      return (
        <div className={styles.childNode}>
          <span>
            {record.get('seqNum')}
          </span>
          <span>
            {record.get('materialLotCode')}/{record.get('lotCode')}
          </span>
          <span onClick={() => updateFrom('remove', record.data)}>
            <img src={deleteLine} alt='' />
          </span>
        </div>
      );
    }
    // 父节点
    if (record.get('parentId')) {
      return (
        <span>
          {record.get('categoryCode')}-{record.get('suMaterial')?.suRevisionCode}-{record.get('unitQty')}-{record.get('suMaterial')?.suMaterialName}
        </span>
      );
    }
    return (
      <span>
        {record.get('categoryCode')}-{record.get('bomMaterialCode')}-{record.get('unitQty')}-{record.get('bomMaterialName')}
      </span>
    );
  };

  // 选中节点
  const onCheck = keys => {
    formAddDs.current.reset();
    const _record = treeDs.toData().find(item => item.id === keys[keys.length - 1]);
    // console.log('_record', _record, treeDs, keys);
    setCheckedKeys([_record]);
    // const treeData = treeDs.selected.map(item => item.toData());
    // let materialId = null;
    // treeData.forEach(item => {
    //   if (item.id === keys[keys.length - 1]) {
    //     if (item.parentId) {
    //       materialId = item?.suMaterial?.suMaterialId;
    //     } else {
    //       materialId = item.bomMaterialId;
    //     }
    //   }
    // });
    // const params = {
    //   routerStepId: workOrderData?.routerStepId,
    //   eoId: workOrderData?.eoId,
    //   materialId,
    // };
    // request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-material-traces/trace-information/ui`, {
    //   method: 'GET',
    //   query: params,
    // }).then(res => {
    //   if (res && !res.failed) {
    //     document.getElementById('textMaterialLotCode').value = null;
    //     document.getElementById('textLot').value = null;
    //     document.getElementById('textQty').value = null;
    //     // 赋值标识
    //     const newRes = [];
    //     res.forEach(item => {
    //       newRes.push({
    //         ...item,
    //         index: uuid(),
    //       });
    //     });
    //     // setTraceInformation(newRes);
    //     props.handleAddRecords({
    //       cardId: props.cardId,
    //       messageType: 'SUCCESS',
    //       recordType: 'query',
    //       message: `表单数据查询成功`,
    //     });
    //   } else {
    //     notification.error({ message: res.message });
    //     props.handleAddRecords({
    //       cardId: props.cardId,
    //       messageType: 'FAIL',
    //       recordType: 'query',
    //       message: `表单数据查询失败`,
    //     });
    //   }
    // });
  };

  // 增加表单
  // const addForm = () => {
  //   setTraceInformation([...traceInformation, { index: new Date().getTime(), _status: 'create' }]);
  // };

  // 删除表单
  // const removeForm = value => {
  // if (value.traceId) {
  //   const newTraceInformation = traceInformation.map(item => {
  //     return {
  //       ...item,
  //       deleteFlag: item.index === value.index ? 'Y' : 'N',
  //     }
  //   });
  //   setTraceInformation([...newTraceInformation]);
  // } else {
  //   const newTraceInformation = traceInformation.filter(item => !(item.index === value.index));
  //   setTraceInformation([...newTraceInformation]);
  // }
  // };

  // 增加表单
  const updateFrom = (type, record) => {
    const params = [];
    if(type === 'add' && !formAddDs.current.get('materialLotCode')){
      return notification.error({ message: '条码必输' });
    }
    if(type === 'add'){
      params.push({
        bomComponentId: checkedKeys[0]?.bomComponentId,
        traceId: checkedKeys[0]?.traceId,
        materialLotCode: formAddDs.current.get('materialLotCode'),
        materialId: checkedKeys[0]?.suMaterial?.suMaterialId || checkedKeys[0]?.bomMaterialId,
        lot: checkedKeys[0]?.lotCode,
        routerStepId: workOrderData?.routerStepId,
        eoId: workOrderData?.eoId,
        workOrderId: workOrderData?.workOrderId,
      })
    }
    if(type === 'remove'){
      params.push({
        bomComponentId: record?.bomComponentId,
        traceId: record?.traceId,
        materialId: record?.suMaterial?.suMaterialId || record?.bomMaterialId,
        lot: record?.lotCode,
        routerStepId: workOrderData?.routerStepId,
        eoId: workOrderData?.eoId,
        workOrderId: workOrderData?.workOrderId,
        deleteFlag: 'Y',
      });
    }
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-material-traces/save/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        notification.success();
        initData(type, res);
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          message: `表单数据保存成功`,
        });
      } else {
        notification.error({ message: res.message });
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'FAIL',
          message: `表单数据保存失败`,
        });
      }
    });
  };

  // 更改输入框值
  // const changeValue = (value, id, field) => {
  //   if (!value && isEmpty(document.getElementById('textLot')?.value) &&
  //     isEmpty(document.getElementById('textMaterialLotCode')?.value)) {
  //     setInitChange(false);
  //   };
  //   if (id === 'textMaterialLotCode' || id === 'textLot' || id === 'textQty') {
  //     document.getElementById(id).value = value;
  //     if (value) {
  //       setInitChange(true);
  //     };
  //   } else {
  //     traceInformation.forEach(item => {
  //       if (item.index === id) {
  //         item[field] = value;
  //       }
  //     });
  //     setTraceInformation([...traceInformation]);
  //   }
  // };

  // 保存表单
  // const saveData = async () => {
  //   console.log('save', treeDs.selected);
  //   const treeData = treeDs.selected.map(item => item.toData());
  //   let materialId = null;
  //   let bomComponentId = null;
  //   treeData.forEach(item => {
  //     if (item.id === checkedKeys[0].keys) {
  //       if (item.parentId) {
  //         materialId = item?.suMaterial?.suMaterialId;
  //         bomComponentId = item.bomComponentId;
  //       } else {
  //         materialId = item.bomMaterialId;
  //         bomComponentId = item.bomComponentId;
  //       }
  //     }
  //   });
  //   const params = [];
  //   if (traceInformation.length === 0 && document.getElementById('textMaterialLotCode')?.value || document.getElementById('textLot')?.value) {
  //     params.push({
  //       materialLotCode: document.getElementById('textMaterialLotCode')?.value || null,
  //       lot: document.getElementById('textLot')?.value || null,
  //       qty: document.getElementById('textQty')?.value || null,
  //       routerStepId: workOrderData?.routerStepId,
  //       eoId: workOrderData?.eoId,
  //       workOrderId: workOrderData?.workOrderId,
  //       materialId,
  //       bomComponentId,
  //     });
  //   } else {
  //     await traceInformation.forEach((item, index) => {
  //       params.push({
  //         ...item,
  //         routerStepId: workOrderData?.routerStepId,
  //         eoId: workOrderData?.eoId,
  //         workOrderId: workOrderData?.workOrderId,
  //         materialId,
  //         bomComponentId,
  //       });
  //       if (traceInformation.length === index + 1) {
  //         if (document.getElementById('textMaterialLotCode')?.value || document.getElementById('textLot')?.value) {
  //           params.push({
  //             materialLotCode: document.getElementById('textMaterialLotCode')?.value || null,
  //             lot: document.getElementById('textLot')?.value || null,
  //             qty: document.getElementById('textQty')?.value || null,
  //             routerStepId: workOrderData?.routerStepId,
  //             eoId: workOrderData?.eoId,
  //             workOrderId: workOrderData?.workOrderId,
  //             materialId,
  //             bomComponentId,
  //           });
  //         }
  //       }
  //     });
  //   }

  //   if (initChange && isEmpty(document.getElementById('textLot')?.value) &&
  //     isEmpty(document.getElementById('textMaterialLotCode')?.value)) {
  //     notification.error({ message: '条码和批次必输其一，请检查' });
  //   } else {
  //     request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-material-traces/save/ui`, {
  //       method: 'POST',
  //       body: params,
  //     }).then(res => {
  //       if (res && !res.failed) {
  //         notification.success();
  //         setInitChange(false);
  //         onCheck(checkedKeys[0].keys);
  //         props.handleAddRecords({
  //           cardId: props.cardId,
  //           messageType: 'SUCCESS',
  //           message: `表单数据保存成功`,
  //         });
  //       } else {
  //         notification.error({ message: res.message });
  //         props.handleAddRecords({
  //           cardId: props.cardId,
  //           messageType: 'FAIL',
  //           message: `表单数据保存失败`,
  //         });
  //       }
  //     });
  //   }
  // };

  return (
    <CardLayout.Layout className={styles.traceabilityConfirmation} spinning={loading}>
      <CardLayout.Header
        className='TraceabilityConfirmationHead'
        title='追溯确认'
        help={props?.cardUsage?.remark}
        addonAfter={
          // <Button
          //   color="primary"
          //   onClick={saveData}
          //   disabled={!checkedKeys[0]}
          // >
          //   确认
          // </Button>
          <TextField
            disabled={!checkedKeys.length || !!checkedKeys[0]?.seqNum}
            placeholder="请输入条码添加"
            name="materialLotCode"
            dataSet={formAddDs}
            onEnterDown={() => {updateFrom('add')}}
            prefix={<img src={scanIcon} alt='' style={{height: '19px'}}/>}
          />
        }
      />
      <CardLayout.Content className='TraceabilityConfirmationForm'>
        <div id={styles.TraceabilityConfirmationIcon}>
          <Tree
            showLine={{
              showLeafIcon: false,
            }}
            defaultExpandAll
            showIcon={false}
            className={styles.TraceabilityConfirmationTree}
            dataSet={treeDs}
            renderer={nodeRenderer}
            selectable
            onSelect={onCheck}
            selectedKeys={[checkedKeys[0]?.id]}
          // draggable
          />
        </div>
        {/* {workOrderData?.eoId && (
          <>
            <Col span={8}>
              <Tree
                // showLine
                defaultExpandAll
                className={styles.TraceabilityConfirmationTree}
                dataSet={treeDs}
                renderer={nodeRenderer}
                selectable
                onSelect={onCheck}
                selectedKeys={checkedKeys}
              // draggable
              />
            </Col>
            <Col span={16}>
              <Form
                labelAlign="left"
                labelWidth="auto"
                labelLayout="placeholder"
                columns={17}
                style={{ padding: '0px 5px 0px 5px' }}
              >
                {traceInformation?.map((item) => {
                  if (item.deleteFlag !== 'Y') {
                    return (
                      <>
                        <TextField
                          key={item.index}
                          label="条码"
                          colSpan={7}
                          name="materialLotCode"
                          onChange={value => changeValue(value, item.index, 'materialLotCode')}
                          disabled={item?.sourceType === 'FEED'}
                          value={
                            traceInformation.filter(traceItem => traceItem.index === item.index)[0]
                              .materialLotCode
                          }
                        />
                        <TextField
                          key={item.index}
                          label="批次"
                          colSpan={5}
                          disabled={item?.sourceType === 'FEED'}
                          onChange={value => changeValue(value, item.index, 'lot')}
                          name="lot"
                          value={
                            traceInformation.filter(traceItem => traceItem.index === item.index)[0].lot
                          }
                        />
                        <TextField
                          key={item.index}
                          label="数量"
                          colSpan={3}
                          disabled={item?.sourceType === 'FEED'}
                          onChange={value => changeValue(value, item.index, 'qty')}
                          name="qty"
                          value={
                            traceInformation.filter(traceItem => traceItem.index === item.index)[0].qty
                          }
                        />
                        <Button
                          colSpan={2}
                          disabled={item?.sourceType === 'FEED'}
                          funcType="flat"
                          icon="remove"
                          color="green"
                          shape="circle"
                          size="small"
                          onClick={() => removeForm(item)}
                        />
                      </>
                    );
                  }
                })}
                <>
                  <TextField
                    label="条码"
                    id="textMaterialLotCode"
                    key="textMaterialLotCode"
                    onChange={value => changeValue(value, 'textMaterialLotCode')}
                    colSpan={7}
                    name="materialLotCode"
                    value={document.getElementById('textMaterialLotCode')?.value}
                  />
                  <TextField
                    label="批次"
                    id="textLot"
                    key="textLot"
                    onChange={value => changeValue(value, 'textLot')}
                    colSpan={5}
                    name="lot"
                    value={document.getElementById('textLot')?.value}
                  />
                  <TextField
                    label="数量"
                    id="textQty"
                    key="textQty"
                    colSpan={3}
                    onChange={value => changeValue(value, 'textQty')}
                    name="qty"
                    value={document.getElementById('textQty')?.value}
                  />
                  <Button
                    colSpan={2}
                    onClick={addForm}
                    icon="add"
                    color="green"
                    funcType="flat"
                    shape="circle"
                    size="small"
                  />
                </>
              </Form>
            </Col>
          </>
        )} */}
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(TraceabilityConfirmation);
