import React, { useState, useEffect, useMemo } from 'react';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import intl from 'utils/intl';
import { DataSet, Table, Button, Lov, Select, TextField } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { useRequest } from '@components/tarzan-hooks';
import { DeleteRows, SaveTable, GetChannelType } from './services';
import { tableDS } from '../../stores/ChannelDrawerDS';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

const ChannelDrawer = props => {
  const { id } = props.match.params;
  const { path } = props.match;
  const tableDs = useMemo(() => new DataSet(tableDS(id)), []);
  useEffect(() => {
    handleQuery();
    handleGetChannelType();
  }, [id]);

  const deleteRows = useRequest(DeleteRows(), { manual: true });
  const saveTable = useRequest(SaveTable(), { manual: true });
  const getChannelType = useRequest(GetChannelType(), { manual: true });

  const [canEdit, setCanEdit] = useState(false);
  const [selectList, setSelectList] = useState([]);
  const [channelTypeOptions, setChannelTypeOptions] = useState([]);

  const handleGetChannelType = () => {
    getChannelType.run({
      onSuccess: res => {
        if (res && res.length > 0) {
          setChannelTypeOptions(res);
        } else {
          setChannelTypeOptions([]);
        }
      },
    });
  };

  const handleQuery = () => {
    tableDs.setQueryParameter('tagGroupId', id);
    tableDs.query();
  };

  const handleCreate = () => {
    const newRowDetail = {};
    if (channelTypeOptions.length === 1) {
      newRowDetail.channelType = channelTypeOptions[0].typeCode;
    }
    tableDs.create(newRowDetail);
  };

  const handleDelete = () => {
    const _deleteList = [];
    selectList.forEach(item => {
      if (item.get('tagAssignChannelId')) {
        _deleteList.push(item.get('tagAssignChannelId'));
      }
    });
    if (_deleteList.length === 0) {
      tableDs.delete(selectList, false);
      setSelectList([]);
    } else {
      deleteRows.run({
        params: _deleteList,
        onSuccess: () => {
          tableDs.delete(selectList, false);
          setSelectList([]);
        },
      });
    }
  };

  const handleSave = async () => {
    const handleSav = await tableDs.validate();
    if (!handleSav) {
      return;
    }
    const _data = (tableDs.toData() || []).map(item => {
      return {
        ...item,
        tagCodes: (item.tagCodes || '').split(','),
        tagIds: (item.tagIds || '').split(','),
      };
    });
    saveTable.run({
      queryParams: {
        tagGroupId: id,
      },
      params: _data,
      onSuccess: () => {
        notification.success();
        setCanEdit(false);
        handleQuery();
      },
    });
  };

  const handleSecect = () => {
    const _selectList = [];
    tableDs.selected.forEach(item => {
      _selectList.push(item);
    });
    setSelectList(_selectList);
  };

  const handleReset = () => {
    tableDs.query();
    setCanEdit(false);
  };

  const columns = [
    {
      name: 'tagObject',
      editor: () => canEdit && <Lov noCache />,
    },
    {
      name: 'channelType',
      editor: () => canEdit && <Select />,
    },
    {
      name: 'channelObject',
      editor: () => canEdit && <TextField />,
    },
    {
      name: 'checkType',
      editor: () => canEdit && <Select />,
    },
  ];
  const tableButtons = [
    <Popconfirm
      title={intl
        .get(`${modelPrompt}.confirm.delete`, {
          count: selectList.length,
        })
        .d(`总计${selectList.length}条数据，是否确认删除?`)}
      onConfirm={handleDelete}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
      okText={intl.get('tarzan.common.button.confirm').d('确定')}
    >
      <PermissionButton
        type="c7n-pro"
        funcType="link"
        style={{ marginLeft: '8px' }}
        icon="delete"
        disabled={!selectList || selectList.length === 0}
        permissionList={[
          {
            code: `${path}.button.edit`,
            type: 'button',
            meaning: '详情页-编辑新建删除复制按钮',
          },
        ]}
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
    </Popconfirm>,
    <Button style={{ marginLeft: '8px' }} icon="add" onClick={handleCreate} disabled={!canEdit}>
      {intl.get('tarzan.common.button.create').d('新增')}
    </Button>,
    ...(!canEdit
      ? [
        <PermissionButton
          type="c7n-pro"
          style={{ marginLeft: '8px' }}
          icon="edit"
          onClick={() => {
            setCanEdit(true);
          }}
          disabled={id === 'create'}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </PermissionButton>,
      ]
      : [
        <Button style={{ marginLeft: '8px' }} onClick={handleReset}>
          {intl.get('tarzan.common.button.cancel').d('取消')}
        </Button>,
        <Button style={{ marginLeft: '8px' }} icon="preview" onClick={handleSave}>
          {intl.get('tarzan.common.button.save').d('保存')}
        </Button>,
      ]),
  ];

  return (
    <>
      <Table
        filter={record => record.status !== 'delete'}
        dataSet={tableDs}
        columns={columns}
        buttons={tableButtons}
        onChange={handleSecect}
      />
    </>
  );
};

export default ChannelDrawer;
