import intl from 'utils/intl';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const scanFormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  fields: [
    {
      name: 'equpimentObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equpimentCode`).d('选择设备'),
      lovCode: 'APEX_MES.EQUIPMENT_USER',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'equipmentId',
      bind: 'equpimentObj.equipmentId',
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      bind: 'equpimentObj.equipmentName',
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      bind: 'equpimentObj.equipmentCode',
    },
    {
      name: 'workcellId',
      bind: 'equpimentObj.workcellId',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工位名称'),
      bind: 'equpimentObj.workcellName',
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
      bind: 'equpimentObj.workcellCode',
    },
    {
      name: 'operationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺名称'),
      bind: 'equpimentObj.operationDesc',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
      bind: 'equpimentObj.operationName',
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线名称'),
      bind: 'equpimentObj.prodLineName',
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
      bind: 'equpimentObj.prodLineCode',
    },
    {
      name: 'containerTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerTypeObj`).d('选择容器类型'),
      lovCode: 'HME.CONTAINER_TYPE_CODE',
      lovPara: { tenantId, containerCategory: 'X' },
      // required: true,
    },
    {
      name: 'packingLevel',
      bind: 'containerTypeObj.packingLevel',
    },
    {
      name: 'containerTypeId',
      bind: 'containerTypeObj.containerTypeId',
    },
    {
      name: 'containerBarCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerBarCode`).d('扫描箱码或条码'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('扫描箱码'),
    },
    {
      name: 'attribute',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute`).d('入库属性'),
    },
    {
      name: 'barcode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.barcode`).d('扫描条码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('箱码'),
    },
    {
      name: 'identificationId',
      type: FieldType.string,
    },
    {
      name: 'currentQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentQty`).d('实时装箱数量'),
    },
    {
      name: 'standardLoadQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardLoadQty`).d('标准装箱数量'),
      min: 0,
    },
    {
      name: 'unbindBarcode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unbindBarcodeSearch`).d('解绑条码搜索'),
    },
  ],
});

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码编码'),
    },
    {
      name: 'materialId',
      type: FieldType.string,
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'degradeLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel`).d('等级'),
    },
    {
      name: 'degradeLevel1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel1`).d('容量'),
    },
    {
      name: 'degradeLevel2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel2`).d('电压'),
    },
    {
      name: 'degradeLevel3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel3`).d('内阻'),
    },
    {
      name: 'degradeLevel4',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel4`).d('壳电压'),
    },
    {
      name: 'degradeLevel5',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel5`).d('壳电阻'),
    },
    {
      name: 'degradeLevel6',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel6`).d('净液量'),
    },
    {
      name: 'degradeLevel7',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel7`).d('K值'),
    },
    {
      name: 'degradeLevel8',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel8`).d('厚度'),
    },
    {
      name: 'degradeLevel9',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel9`).d('正极耳长度'),
    },
    {
      name: 'degradeLevel10',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel10`).d('负级耳长度'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'scanIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scanIdentification`).d('箱码'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号代码'),
    },
  ],
});

const detailScanFormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  forceValidate: true,
  fields: [
    {
      name: 'containerTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerTypeObj`).d('选择容器类型'),
      lovCode: 'HME.CONTAINER_TYPE_CODE',
      lovPara: { tenantId, containerCategory: 'T' },
      required: true,
    },
    {
      name: 'packingLevel',
      bind: 'containerTypeObj.packingLevel',
    },
    {
      name: 'containerTypeId',
      bind: 'containerTypeObj.containerTypeId',
    },
    {
      name: 'barcode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.barcode`).d('扫描箱码/条码'),
    },
    {
      name: 'topContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerCode`).d('托条码'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('装箱数量'),
    },
  ],
});

const detailTableDS: () => DataSetProps = () => ({
  autoCreate: false,
  selection: DataSetSelection.multiple,
  paging: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'uuid',
  fields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('箱条码'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.numberOfCells`).d('电芯数量'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号代码'),
    },
    {
      name: 'gradingStatusDes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gearStatus`).d('分档状态'),
    },
    {
      name: 'gradingLabel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gradingLabel`).d('分档标签'),
      lookupCode: 'APEX_MES.GRADING_LABEL',
    },
    {
      name: 'gradingLabelPrint',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gradingLabelPrint`).d('分档标签打印项目'),
      lookupCode: 'APEX_MES.GRADING_LABEL_PRINT',
      multiple: ',',
    },
    {
      name: 'assembleMarking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMarking`).d('装配喷码'),
    },
    {
      name: 'gradingMarking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gradingMarking`).d('分档喷码'),
    },
    {
      name: 'voltage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.voltage`).d('电压'),
    },
    {
      name: 'stickGlue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stickGlue`).d('贴胶'),
      lookupCode: 'APEX_MES.STICK_GLUE',
    },
    {
      name: 'breakSide',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.breakSide`).d('折边'),
      lookupCode: 'APEX_MES.BREAK_SIDE',
    },
    {
      name: 'specialRequirements',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialRequirements`).d('特殊要求'),
    },
  ],
  record: {},
});

export { scanFormDS, tableDS, detailTableDS, detailScanFormDS };
