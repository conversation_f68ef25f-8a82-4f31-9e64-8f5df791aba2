import React, { useState, useMemo, useEffect } from 'react';
import { DataSet, Table, Spin, Button, Switch, NumberField, Lov, Select } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import request from 'utils/request';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import intl from 'utils/intl';
import moment from 'moment';
import { useRequest } from '@components/tarzan-hooks';
import { Host } from '@/utils/config';
import { GetOperationByEquipment } from './services';
import { tableDS } from './stores/tableDS';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const modelPrompt = 'tarzan.hmes.ProductionProcessConversion';

const ProductionProcessConversion = () => {
  const [saveLock, setSaveLock] = useState(true);
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const { run: fetchOperation } = useRequest(GetOperationByEquipment(), { manual: true });

  useEffect(() => {
    tableDs.query();
  }, []);

  const handleChangeEquipment = value => {
    setSaveLock(false);
    if (value?.equipmentCode) {
      fetchOperation({
        params: {
          equipmentCode: value?.equipmentCode,
        },
        onSuccess: res => {
          tableDs.current?.set('operationObj', {
            ...res,
            description: res.operationDescription,
          });
        },
      })
    } else {
      tableDs.current?.set('operationObj', undefined);
    }
  };

  const handleChangeStretchNumber = (value, record) => {
    setSaveLock(false);
    if (!value) {
      return '';
    }
    // 截取小数点后三位，而非四舍五入
    const newValue = typeof value === 'string' ? value : String(value);
    const pointIndex = newValue.indexOf('.');
    if (pointIndex === -1) {
      return;
    }
    record.set('stretchNumber', newValue.substring(0, pointIndex + 4)); // 截取小数点后三位
  }

  const columns = [
    {
      name: 'siteObj',
      align: 'center',
      editor: editing && (
        <Lov dataSet={tableDs} name="siteObj" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'materialObj',
      align: 'center',
      editor: editing && (
        <Lov dataSet={tableDs} name="materialObj" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'materialName',
      align: 'center',
    },
    {
      name: 'revisionCode',
      align: 'center',
      editor: record =>
        (record.status === 'add' || (editing && record.get('revisionFlag') === 'Y')) && (
          <Select dataSet={tableDs} name="revisionCode" onChange={() => setSaveLock(false)} />
        ),
    },
    {
      name: 'equipmentObj',
      align: 'center',
      editor: editing && (
        <Lov dataSet={tableDs} name="equipmentObj" onChange={(val) => handleChangeEquipment(val)} />
      ),
    },
    {
      name: 'equipmentName',
      align: 'center',
    },
    {
      name: 'operationObj',
      align: 'center',
    },
    {
      name: 'description',
      align: 'center',
    },
    // {
    //   name: 'singleUsage',
    //   align: 'center',
    //   editor: editing && <NumberField name="singleUsage" onChange={() => setSaveLock(false)} />,
    // },
    // {
    //   name: 'eaQty',
    //   align: 'center',
    //   editor: editing && <NumberField name="eaQty" onChange={() => setSaveLock(false)} />,
    // },
    // {
    //   name: 'slittingMultiple',
    //   align: 'center',
    //   editor: editing && (
    //     <NumberField name="slittingMultiple" onChange={() => setSaveLock(false)} />
    //   ),
    // },
    // {
    //   name: 'stripeNumber',
    //   align: 'center',
    //   editor: editing && <NumberField name="stripeNumber" onChange={() => setSaveLock(false)} />,
    // },
    // {
    //   name: 'maxCompletedQty',
    //   align: 'center',
    //   editor: editing && <NumberField name="maxCompletedQty" onChange={() => setSaveLock(false)} />,
    // },
    {
      name: 'stretchNumber',
      align: 'center',
      editor: record => editing && <NumberField name="stretchNumber" onChange={(value) => handleChangeStretchNumber(value, record)} />,
    },
    {
      name: 'enableFlag',
      align: 'center',
      editor: editing && <Switch name="enableFlag" onChange={() => setSaveLock(false)} />,
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '有效' : '无效'}>
          {}
        </Badge>
      ),
    },
    {
      name: 'createdName',
      align: 'center',
    },
    {
      name: 'creationDate',
      align: 'center',
    },
    {
      name: 'lastUpdatedName',
      align: 'center',
    },
    {
      name: 'lastUpdateDate',
      align: 'center',
    },
  ];

  const handleAdd = () => {
    setSaveLock(false);
    tableDs.create(
      {
        createdName: userInfo?.realName,
        creationDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        lastUpdatedName: userInfo?.realName,
        lastUpdateDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      },
      0,
    );
  };

  const handleSave = async () => {
    const validateBase = await tableDs.validate();
    if (validateBase) {
      const data = tableDs.toJSONData();
      const validateData = data
        .map(item => {
          const { siteCode, materialCode, revisionCode, operationName } = item;
          return `${siteCode}站点+${materialCode}物料编码+${revisionCode}物料版本+${operationName}工艺编码`;
        })
        .reduce((value, item) => {
          if (value[item]) {
            value[item]++;
          } else {
            value[item] = 1;
          }
          return value;
        }, {});
      if (Object.values(validateData).filter(item => item > 1).length > 0) {
        for (const key in validateData) {
          if (validateData[key] > 1) {
            notification.warning({ message: intl
              .get(`${modelPrompt}.error.operation`)
              .d(`${key}数据重复，请检查！`) });
          }
        }
      } else {
        setLoading(true);
        const temp = data.map(item => {
          if (item._status !== 'create') {
            return {
              ...item,
              lastUpdatedName: userInfo?.realName,
              lastUpdateDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            };
          }
          return item;
        });
        request(`${Host}/v1/${tenantId}/hme-operation-conversions/save/ui`, {
          method: 'POST',
          body: temp,
        }).then(res => {
          if (res && !res.failed) {
            notification.success();
            setEditing(false);
            tableDs.query();
            setSaveLock(true);
          } else {
            notification.error({ message: res.message });
          }
          setLoading(false);
        });
      }
    }
  };
  return (
    <React.Fragment>
      <Header title={intl.get(`${modelPrompt}.title`).d('生产工艺折算系数维护')}>
        {!editing ? (
          <Button onClick={() => setEditing(true)} style={{ marginRight: 15 }} color="primary">
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        ) : (
          <>
            <Button
              onClick={() => {
                setEditing(false);
                tableDs.query(tableDs.currentPage);
              }}
              style={{ marginRight: 15 }}
              color="primary"
            >
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button onClick={handleAdd} style={{ marginRight: 15 }} color="primary">
              {intl.get('tarzan.common.button.create').d('新建')}
            </Button>
            <Button
              disabled={saveLock}
              onClick={handleSave}
              style={{ marginRight: 15 }}
              color="primary"
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Table
            dataSet={tableDs}
            columns={columns}
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            queryFieldsLimit={4}
            searchCode="ProductionProcessConversion"
            customizedCode="ProductionProcessConversion"
          />
        </Spin>
      </Content>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.ProductionProcessConversion', 'tarzan.common'],
})(ProductionProcessConversion);
