import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.toolModelMaintain';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  selection: false,
  queryFields: [
    {
      name: 'modelObj',
      lovCode: 'YP.QMS_TOOL_MODEL',
      type: FieldType.object,
      ignore: FieldIgnore.always,
      textField: 'modelCode',
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      bind: 'modelObj.modelCode',
    },
    {
      name: 'speciesObj',
      lovCode: 'YP.QMS_YP_TYPE_TEST',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
      textField: 'speciesName',
    },
    {
      name: 'toolTypeId',
      type: FieldType.string,
      bind: 'speciesObj.toolTypeId',
    },
    {
      name: 'categoryCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.categoryName`).d('类别描述'),
      lookupCode: 'QIS.MS_TOOL_CTGR',
      lovPara: { tenantId },
    },
    {
      name: 'enableFlag',
      lookupCode: 'MT.YES_NO',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'siteObj',
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      textField: 'siteName',
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObj.siteCode',
    },
  ],
  fields: [
    {
      name: 'siteObj',
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      textField: 'siteCode',
      required: true,
      lovPara: { tenantId },
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObj.siteCode',
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      bind: 'siteObj.siteName',
      required: true,
    },
    {
      name: 'modelCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号编码'),
      required: true,
    },
    {
      name: 'modelName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelName`).d('型号描述'),
    },
    {
      name: 'speciesObj',
      type: FieldType.object,
      lovCode: 'YP.QMS_YP_TYPE_TEST',
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
      required: true,
    },
    {
      name: 'toolTypeId',
      type: FieldType.string,
      bind: 'speciesObj.toolTypeId',
    },
    {
      name: 'speciesCode',
      type: FieldType.string,
      bind: 'speciesObj.speciesCode',
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      bind: 'speciesObj.speciesName',
    },
    {
      name: 'categoryName',
      type: FieldType.string,
      bind: 'speciesObj.categoryName',
      label: intl.get(`${modelPrompt}.categoryName`).d('类别描述'),
      required: true,
    },
    {
      name: 'operationType',
      type: FieldType.string,
    },
    {
      name: 'modelRange',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.range`).d('量程'),
    },
    {
      name: 'resolution',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.resolution`).d('分辨力'),
    },
    {
      name: 'enableFlag',
      lookupCode: 'MT.YES_NO',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'graphUuid',
      type: FieldType.attachment,
      bucketName: 'qms',
      label: intl.get(`${modelPrompt}.graphUuid`).d('图纸附件'),
    },
  ],
  transport: {
    read: {
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-models`,
      method: 'get',
    },
    submit: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-models`,
        method: 'post',
        data,
      };
    },
  },
});

const itemTypeDS: () => DataSetProps = () => ({
  autoQuery: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'sequenceNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'inspectDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDescription`).d('检测项目'),
      required: true,
    },

    {
      name: 'dataType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataType`).d('数据类型'),
      required: true,
      lovPara: {
        tenantId,
      },
      lookupCode: 'YP.QIS.MEASURE_INSPECT_ITEM_DATA_TYPE',
    },

    {
      name: 'uomCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.uom`).d('单位'),
      lovCode: 'MT.COMMON.UOM',
      ignore: 'always',
      textField: 'uomName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'uomName',
      bind: 'uomCodeLov.uomName',
    },
    {
      name: 'uomId',
      bind: 'uomCodeLov.uomId',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      required: true,
      defaultValue: 'Y',
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
    },

    {
      name: 'modelCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.modelCode`).d('量具型号编码'),
    },
    {
      name: 'modelName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelName`).d('量具型号描述'),
    },
    {
      name: 'speciesObj',
      type: FieldType.object,
      lovCode: 'YP.QMS_YP_TYPE_TEST',
      label: intl.get(`${modelPrompt}.speciesName`).d('种别描述'),
    },
    {
      name: 'toolTypeId',
      type: FieldType.string,
      bind: 'speciesObj.toolTypeId',
    },
    {
      name: 'speciesCode',
      type: FieldType.string,
      bind: 'speciesObj.speciesCode',
    },
    {
      name: 'speciesName',
      type: FieldType.string,
      bind: 'speciesObj.speciesName',
    },
    {
      name: 'categoryName',
      type: FieldType.string,
      bind: 'speciesObj.categoryName',
      label: intl.get(`${modelPrompt}.categoryName`).d('类别描述'),
    },
    {
      name: 'operationType',
      type: FieldType.string,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-inspect-rel/inspectRelQuery`,
        method: 'get',
      };
    },
    // submit: ({ data }) => {
    //   return {
    //     url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-tool-inspect-rel/inspectRelSave`,
    //     method: 'post',
    //
    //   }
    // },
  },
});
export { tableDS, itemTypeDS };
