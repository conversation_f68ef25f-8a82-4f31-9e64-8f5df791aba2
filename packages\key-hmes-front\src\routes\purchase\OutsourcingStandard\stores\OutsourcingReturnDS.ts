import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { isUndefined } from 'lodash';
import uuid from 'uuid/v4';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.purchase.outsourcingManage';
const tenantId = getCurrentOrganizationId();

const headDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'outSourceReturnNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.outSourceReturnNum`).d('外协退料单号'),
    },
    {
      name: 'outSourcePoLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.outSourcePoLov`).d('外协采购订单'),
      lovCode: 'MT.OUT_SOURCE_PO',
      textField: 'poUniqueCode',
      multiple: true,
      required: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
      },
    },
    {
      name: 'outSourcePoId',
      type: FieldType.number,
    },
    {
      name: 'outSourcePoNum',
      type: FieldType.string,
    },
    {
      name: 'lineNums',
      type: FieldType.string,
      bind: 'outSourcePoLov.lineNum',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.table.supplierLov`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
        required: ({ record }) => {
          return !record?.get('outSourcePoId');
        },
        disabled: ({ record }) => {
          return record?.get('outSourcePoId');
        },
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'supplierCode',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'supplierSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.table.supplierSiteLov`).d('供应商地点'),
      lovCode: 'MT.MODEL.SUPPLIER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            supplierId: record?.get('supplierId'),
          };
        },
        disabled: ({ record }) => {
          if (record?.get('outSourcePoId') || !record?.get('supplierId')) {
            return true;
          } if (record?.get('supplierId')) {
            return false;
          }
        },
      },
    },
    {
      name: 'supplierSiteId',
      bind: 'supplierSiteLov.supplierSiteId',
    },
    {
      name: 'supplierSiteCode',
      bind: 'supplierSiteLov.supplierSiteCode',
    },
    {
      name: 'supplierSiteName',
      bind: 'supplierSiteLov.supplierSiteName',
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.reason`).d('退货原因'),
      required: true,
      lookupCode: 'MT.OUTSOURCING_REASON',
    },
    {
      name: 'setNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.setNumber`).d('套数'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('outSourcePoId');
        },
      },
    },
    {
      name: 'demandTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.demandTime`).d('需求时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.remark`).d('备注'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      lookupCode: 'SOURCE_SYSTEM',
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
});

const tableDS = (): DataSetProps => {
  return {
    autoQuery: false,
    autoCreate: false,
    paging: false,
    selection: false,
    autoLocateFirst: false,
    forceValidate: true,
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-out-source/out-source-return/line/query/ui`,
          method: 'GET',
        };
      },
    },
    dataKey: 'rows',
    // totalKey: 'rows.totalElements',
    primaryKey: 'lineNumber',
    fields: [
      {
        name: 'lineNumber',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.lineNumber`).d('行号'),
      },
      {
        name: 'originLineNum',
        type: FieldType.number,
      },
      {
        name: 'siteLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.table.siteLov`).d('站点'),
        lovCode: 'MT.MODEL.SITE',
        required: true,
        ignore: FieldIgnore.always,
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId,
              siteType: 'MANUFACTURING',
            };
          },
        },
      },
      {
        name: 'siteId',
        type: FieldType.number,
        bind: 'siteLov.siteId',
      },
      {
        name: 'siteCode',
        type: FieldType.string,
        bind: 'siteLov.siteCode',
      },
      {
        name: 'materialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
        lovCode: 'MT.METHOD.BOM_MATERIAL',
        ignore: FieldIgnore.always,
        required: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              siteIds: record?.get('siteId'),
            };
          },
          disabled: ({ record }) => {
            return !record?.get('siteId');
          },
        },
      },
      {
        name: 'materialId',
        type: FieldType.number,
        bind: 'materialLov.materialId',
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        bind: 'materialLov.materialCode',
      },
      {
        name: 'revisionFlag',
        type: FieldType.string,
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.revisionCode`).d('物料版本'),
        textField: 'description',
        valueField: 'description',
        lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
        lookupAxiosConfig: () => {
          return {
            transformResponse(data) {
              if (!isUndefined(data) && data.length > 0) {
                const { rows } = JSON.parse(data);
                const firstlyQueryData = [] as Array<object>;
                if (rows instanceof Array) {
                  rows.forEach(item => {
                    firstlyQueryData.push({
                      kid: uuid(),
                      description: item,
                    });
                  });
                }
                return firstlyQueryData;
              }
            },
          };
        },
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              materialId: record?.get('materialId'),
              siteIds: [record?.get('siteId')],
            };
          },
          disabled: ({ record }) => {
            return record?.get('revisionFlag') !== 'Y';
          },
          required: ({ record }) => {
            return record?.get('revisionFlag') === 'Y';
          },
        },
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.materialName`).d('物料描述'),
      },

      {
        name: 'isQuantity',
        type: FieldType.string,
      },
      {
        name: 'quantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.quantity`).d('制单数量'),
        required: true,
        min: 0,
        validator: (...args: Array<any>) => {
          const {
            data: { quantity, actualQty, quantityExists, isQuantity },
          } = args[2];
          if (isQuantity !== 'Y') {
            const qty = actualQty - quantityExists;
            if (quantity > qty) {
              return intl
                .get(`${modelPrompt}.quantity.greater.than.actualQty`)
                .d('制单数量不能大于已发数量');
            }
          }
        },
      },
      {
        name: 'actualQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.actualQty`).d('已发数量'),
      },
      {
        name: 'quantityExists',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.quantityExists`).d('已制单数量'),
      },
      {
        name: 'uomId',
        type: FieldType.number,
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.uomCode`).d('单位'),
      },
      {
        name: 'toSiteLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.table.toSiteLov1`).d('退货仓库'),
        lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
        ignore: FieldIgnore.always,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              locatorCategoryList: ['AREA'],
              businessTypes: 'OUTSOURCING_RETURN',
              queryType: 'TARGET',
              siteIds: [record?.get('siteId')].join(','),
            };
          },
          required: ({ record }) => {
            return record?.get('toLocatorRequiredFlag') === 'Y';
          },
          disabled: ({ record }) => {
            return !record?.get('siteId') || !record?.get('materialId');
          },
        },
      },
      {
        name: 'toLocatorId',
        type: FieldType.number,
        bind: 'toSiteLov.locatorId',
      },
      {
        name: 'toLocatorCode',
        type: FieldType.string,
        bind: 'toSiteLov.locatorCode',
      },

      {
        name: 'sumAvailableQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.sumAvailableQty`).d('库位现存量'),
      },
      {
        name: 'poNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.poNumber`).d('采购订单号'),
      },
      {
        name: 'poLineNum',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.poLineNum`).d('采购订单行号'),
      },
      {
        name: 'toleranceFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.toleranceFlag`).d('允差标识'),
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'toleranceType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.toleranceType`).d('允差类型'),
        textField: 'description',
        valueField: 'typeCode',
        lovPara: { tenantId },
        dynamicProps: {
          required: ({ record }) => {
            return record?.get('toleranceFlag') === 'Y';
          },
          disabled: ({ record }) => {
            return record?.get('toleranceFlag') !== 'Y';
          },
        },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'toleranceMaxValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.toleranceMaxValue`).d('上允差值'),
        min: 0,
        dynamicProps: {
          required: ({ record }) => {
            return (
              record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER'
            );
          },
          disabled: ({ record }) => {
            return (
              record?.get('toleranceFlag') !== 'Y' ||
              record?.get('toleranceType') === 'OVER_MATERIAL_LOT' ||
              record?.get('toleranceType') === 'UNLIMITED'
            );
          },
        },
      },
      {
        name: 'toleranceMinValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.toleranceMinValue`).d('下允差值'),
        min: 0,
        dynamicProps: {
          required: ({ record }) => {
            return (
              record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER'
            );
          },
          disabled: ({ record }) => {
            return (
              record?.get('toleranceFlag') !== 'Y' ||
              record?.get('toleranceType') === 'OVER_MATERIAL_LOT' ||
              record?.get('toleranceType') === 'UNLIMITED'
            );
          },
        },
      },
    ],
  };
};

export { headDS, tableDS };
