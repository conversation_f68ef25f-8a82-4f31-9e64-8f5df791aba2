import React, { useState, useMemo } from 'react';
import {
  DataSet,
  Table,
  Button,
  Row,
  Col,
  TextField,
  Form,
  Lov,
  DateTimePicker,
  Select,
  Icon,
} from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import notification from 'utils/notification';
import { observer } from 'mobx-react';
// import { Host, API_HOST } from '@/utils/config';
import { BASIC } from '@utils/config';
import { isNil } from 'lodash';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import moment from 'moment';
import { tableDS } from './stores';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.ProductProcessingParameterReportQuery';

const ProductProcessingParameterReportQuery = observer(() => {
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds

  const columns = [
    {
      name: 'equipmentCode',
      lock: 'left',
      width: 200,
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'tagCode',
    },
    {
      name: 'tagDescription',
    },
    {
      name: 'tagValue',
    },
    {
      name: 'tagCalculateResultMeaning',
    },
    {
      name: 'trueValue',
      width: 200,
      renderer: ({ value, record }) => {
        if (record.get('valueType') === 'VALUE') {
          if (record.get('trueValueList') && record.get('trueValueList').length) {
            const temp = record.get('trueValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue && <Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {
      name: 'falseValue',
      width: 200,
      renderer: ({ value, record }) => {
        if (record.get('valueType') === 'VALUE') {
          if (record.get('falseValueList') && record.get('falseValueList').length) {
            const temp = record.get('falseValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue && <Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {
      name: 'recordDate',
    },
    {
      name: 'createByName',
    },
    {
      name: 'identification',
    },
    {
      name: 'tagGroupCode',
    },
    {
      name: 'tagGroupDescription',
    },
    {
      name: 'uomCode',
    },
    {
      name: 'uomName',
    },
    {
      name: 'creationDate',
      width: 150,
      align: 'center',
    },
    {
      name: 'lastUpdateDate',
      width: 150,
      align: 'center',
    },
  ];
  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'flex-start',
          }}
        >
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <Lov name="equipmentLov" />
              <DateTimePicker
                name="startDate"
                min={queryDataSet.current?.get('endDate') ? moment(queryDataSet.current?.get('endDate').format())?.subtract(3, 'days') : null}
                max={queryDataSet.current?.get('endDate')}
              />
              <DateTimePicker
                name="endDate"
                min={queryDataSet.current?.get('startDate')}
                max={queryDataSet.current?.get('startDate') ? moment(queryDataSet.current?.get('startDate').format())?.add(3, 'days') : null}
              />
              {expandForm && (
                <>
                  <Lov name="tagLov" />
                  <Lov name="tagGroupLov" />
                  <TextField
                    name="identifications"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          onClick={() =>
                            onOpenInputModal(true, 'identifications', '条码号', queryDataSet)
                          }
                        />
                      </div>
                    }
                  />
                  <Select name="tagCalculateResult" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                funcType="link"
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                  tableDs.loadData([]);
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleSearch = async () => {
    const {
      equipmentId,
      startDate,
      endDate,
      tagCode,
      tagGroupCode,
    } = tableDs?.queryDataSet?.toJSONData()[0];
    if (startDate && endDate) {
      if (moment(endDate).diff(startDate, 'days') > 3) {
        notification.error({
          message: intl.get(`${modelPrompt}.timeIntervalPrompt`).d('查找时间间隔不能超过三天！'),
        });
        return;
      }
      if (!tagCode.length && !tagGroupCode.length && !equipmentId.length) {
        notification.error({
          message: intl.get(`${modelPrompt}.fiveQueryField`).d('请至少输入设备编码、收集项编码、收集组编码其中一个进行查询'),
        });
        return;
      }
      tableDs.query();
    } else {
      tableDs.loadData([]);
      notification.error({
        message: intl.get(`${modelPrompt}.twoqueryField`).d('请输入查询条件'),
      });
    }
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleSearch();
    }
  };
  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const getExportQueryParams = () => {
    const queryParams = tableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i]) || i.includes('Lov') || i === '__dirty') {
        delete queryParams[i];
      }
    });
    return queryParams;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('设备加工参数查询报表')}>
        <ExcelExport
          method="POST"
          allBody
          exportAsync={false}
          otherButtonProps={{ disabled: !tableDs.toData().length }}
          queryParams={getExportQueryParams}
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-product-param-collect/equipment/export`}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="ProductProcessingParameterReportQuery"
          customizedCode="ProductProcessingParameterReportQuery"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.ProductProcessingParameterReportQuery', 'tarzan.common'],
})(ProductProcessingParameterReportQuery);
