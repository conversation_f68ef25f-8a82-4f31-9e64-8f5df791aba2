.title-left {
  font-size: 14px;
}

.title-right {
  float: right;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  margin-right: 0.5rem;
}

.title-right-info {
  margin-right: 10px;
}

.login-scan-form {
  :global {
    .c7n-pro-input {
      height: 40px !important;
      font-size: 14px !important;
      &::placeholder {
        font-size: 14px !important;
      }
    }
    label .c7n-pro-input:not(textarea):not(:last-child) {
      padding-right: 36px!important;
    }
    label .c7n-pro-input-suffix img {
      width: 20px;
      margin-right: 8px;
    }
  }
}

.split-menu {
  :global {
    .c7n-menu-item > a,
    .c7n-menu-item > a:hover {
      color: #000;
    }
    .c7n-menu-item.c7n-menu-item-selected {
      background-color: inherit;
    }
  }
}
