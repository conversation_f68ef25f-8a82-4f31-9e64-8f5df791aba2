/**
 * @Description: 生产线维护-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 11:36:42
 */

import React, { useState, useRef } from 'react';
import { Button } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Header, Content } from 'components/Page';
import { AttributeDrawer } from '@components/tarzan-ui';
import Detail from '../components/Detail';

// const TABLENAME = 'mt_mod_production_line_attr';
const modelPrompt = 'tarzan.model.org.prodLine';

const ProLineDetail = props => {
  const {
    match,
    custConfig,
  } = props;
  const {
    path,
    params: { proLineId },
  } = match;
  const [canEdit, setCanEdit] = useState(proLineId === 'create');
  const childRef = useRef();

  const handleSave = async () => {
    const { success, newKid } = await childRef.current.submit();
    if (success) {
      setCanEdit(prev => !prev);
      props.history.push(`/hmes/organization-modeling/pro-line/detail/${newKid}`);
    }
  };

  const handleCancel = () => {
    if (proLineId === 'create') {
      props.history.push('/hmes/organization-modeling/pro-line/list');
    } else {
      childRef.current.reset();
      setCanEdit(prev => !prev);
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.prodLineMaintenance`).d('生产线维护')}
        backPath="/hmes/organization-modeling/pro-line/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <AttributeDrawer
          // tablename={TABLENAME}
          className="org.tarzan.model.domain.entity.MtModProductionLine"
          kid={proLineId}
          canEdit={canEdit}
          disabled={proLineId === 'create'}
          serverCode={BASIC.TARZAN_MODEL}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.PROLINE_DETAIL.BUTTON`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <Detail canEdit={canEdit} ref={childRef} kid={proLineId} columns={3} componentType="PROLINE" />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.model.org.prodLine', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.PROLINE_DETAIL.BUTTON`],
})(ProLineDetail));
