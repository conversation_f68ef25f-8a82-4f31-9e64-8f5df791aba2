/**
 * @Description: 问题警示平台-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-06-15 14:09:31
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import intl from 'utils/intl';

import { Header, Content } from 'components/Page';
import {
  DataSet,
  Button,
  Form,
  TextField,
  Select,
  Switch,
  Lov,
  Attachment,
  Table,
  DatePicker,
  TextArea,
  Modal,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm } from 'choerodon-ui';
import uuid from 'uuid/v4';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import moment from 'moment';
import { getCurrentUser } from 'utils/utils';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { useDataSetEvent } from 'utils/hooks';
import AttributeDrawer from '@/components/ApprovalInfoDrawer';
import {
  fetchWarning,
  saveWarning,
  warningPublishConfig,
  warningClosedConfig,
  warningSubmitConfig,
  warningLineSubmitConfig,
  warningLineCancelConfig,
  warningLineReviewConfig,
  getUserDepartmentConfig,
  ChangeRemindFlagConfig,
} from '../services';
import { detailFormDS, detailTableDS, modalDS } from '../stores/WarningDS';

const modelPrompt = 'tarzan.warning';
const { Panel } = Collapse;

// 权限关系定义
const authMap = {
  CREATE: ['BASE', 'HEADER_EDIT'],
  NEW: ['BASE', 'HEADER_EDIT', 'HEADER_SUBMIT'],
  TO_RELEASE1: [
    'DETAIL',
    'HEADER_EDIT',
    'HEADER_RELEASED',
    'TABLE_CREATE',
    'TABLE',
    'TABLE_ENCLOSURE_NEEDEDIT',
    'TABLE_ADD',
  ],
};

const WarningTable = props => {
  const {
    match: {
      params: { id },
      path,
    },
  } = props;

  // pub路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  const [user] = useState(getCurrentUser()); // 用户详细信息

  const [authList, setAuthList] = useState<any>([]); // 是否可点击和显示的权限列表
  const [pageStatus, setPageStatus] = useState(); // 单据状态
  const [selected, setSelected] = useState<any>([]); // 选中行

  const [printObject, setPrintObject] = useState<any>({}); // 选中行

  // 编辑状态
  const [canEdit, setCanEdit] = useState(false);

  const queryDetail = useRequest(fetchWarning(), {
    // 详情页数据查询
    manual: true,
    needPromise: true,
  });

  const saveDetail = useRequest(saveWarning(), {
    // 详情页数据保存
    manual: true,
    needPromise: true,
  });

  // 整单提交
  const warningSubmit = useRequest(warningSubmitConfig(), {
    manual: true,
    needPromise: true,
  });

  // 整单发布
  const warningPublish = useRequest(warningPublishConfig(), {
    manual: true,
    needPromise: true,
  });

  // 整单关闭
  const warningClosed = useRequest(warningClosedConfig(), {
    manual: true,
    needPromise: true,
  });

  // 行提交
  const warningLineSubmit = useRequest(warningLineSubmitConfig(), {
    manual: true,
    needPromise: true,
  });
  // 行取消
  const warningLineCancel = useRequest(warningLineCancelConfig(), {
    manual: true,
    needPromise: true,
  });
  // 行审核
  const warningLineReview = useRequest(warningLineReviewConfig(), {
    manual: true,
    needPromise: true,
  });
  // 行审核
  const getUserDepartment = useRequest(getUserDepartmentConfig(), {
    manual: true,
    needPromise: true,
  });
  // 变更启用提醒
  const changeRemindFlag = useRequest(ChangeRemindFlagConfig(), {
    manual: true,
  });

  const formDs = useMemo(() => new DataSet(detailFormDS()), []);

  const tableDs = useMemo(() => new DataSet(detailTableDS(formDs)), []);

  // 模态框ds
  const modalDs = useMemo(() => new DataSet(modalDS()), []);

  // 初始化页面
  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      // 新建是表单和列表添加默认数据
      formDs.loadData([
        {
          registUserId: user.id,
          registUserName: user.realName,
          registDate: moment(new Date()).format('YYYY-MM-DD'),
        },
      ]);
      tableDs.loadData([]);
      // 选中项清空
      setSelected([]);

      // 设置新建时表单 列表 按钮各部分的显示,禁用,编辑权限
      setAuthList(authMap.CREATE);
      tableDs.setState('authList', authMap.CREATE);

      userChange({ id: user.id }, 'registUserDapartmentName');

      // 保存页面状态
      setPageStatus(undefined);

      // 打印内容清空
      setPrintObject({});

      // 问题管理验证关闭 -> 问题警示
      const lovObj = props.location.state;
      if (lovObj && lovObj?.sourcePage) {
        formDs.current?.set('createMethod', lovObj.problemCategory);
        formDs.current?.set('sourceProblemLov', {
          problemId: lovObj.problemId,
          problemCode: lovObj.problemCode,
          severityLevel: lovObj.severityLevel,
        });
        formDs.current?.set('problemDesc', lovObj.problemDescription);
        formDs.current?.set('siteLov', {
          siteId: lovObj.siteId,
          siteCode: lovObj.siteCode,
          siteName: lovObj.siteName,
        });
        formDs.current?.set('prodLineLov', {
          prodLineId: lovObj.productionLineId,
          prodLineName: lovObj.prodLineName,
        });
      }
    } else {
      // 查询详情
      initPageData();
    }
  }, [id]);

  const handleTableSelect = (params: any) => {
    setSelected(params.dataSet.selected || []);
  };

  const handleAttachmentsChange = () => {
    setSelected(tableDs.selected || []);
  };

  // 附件配置
  const attachmentProps: any = {
    bucketName: 'qms',
    bucketDirectory: 'inspect-item-maintain',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
    onAttachmentsChange: handleAttachmentsChange,
  };

  useDataSetEvent(tableDs, 'select', handleTableSelect);
  useDataSetEvent(tableDs, 'selectAll', handleTableSelect);
  useDataSetEvent(tableDs, 'unselect', handleTableSelect);
  useDataSetEvent(tableDs, 'unselectAll', handleTableSelect);

  const handleChangeRemindFlag = record => {
    if (canEdit) {
      return;
    }
    changeRemindFlag.run({
      params: {
        problemWarnTaskId: record?.get('problemWarnTaskId'),
        remindFlag: record?.get('remindFlag'),
      },
      onSuccess: () => {
        notification.success({});
        initPageData();
      },
    })
  }

  const tableColumn: ColumnProps[] = [
    {
      hidden: !(authList.includes('TABLE_ADD') && canEdit),
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={addLine}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteLine(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      renderer: ({ record }: any) => {
        return record.index * 10 + 10;
      },
    },
    {
      name: 'siteLov',
      editor: canEdit && authList.includes('TABLE'),
    },
    {
      name: 'prodLineLov',
      editor: canEdit && authList.includes('TABLE'),
    },
    {
      name: 'processLov',
      editor: canEdit && authList.includes('TABLE'),
    },
    {
      name: 'equipmentLov',
      editor: canEdit && authList.includes('TABLE'),
    },
    {
      name: 'responsibilityUserLov',
      editor: record =>
        canEdit &&
        authList.includes('TABLE') && (
          <Lov
            onChange={value => {
              tableUserChange(value, record, 'responsibilityDepartmentName');
            }}
          />
        ),
    },
    {
      name: 'responsibilityDepartmentName',
    },
    {
      name: 'planStartDate',
      width: 150,
      editor: canEdit && authList.includes('TABLE'),
    },
    {
      name: 'planEndDate',
      width: 150,
      editor: canEdit && authList.includes('TABLE'),
    },
    {
      name: 'status',
      editor: canEdit && authList.includes('TABLE'),
    },
    {
      name: 'evidence',
      editor: (authList.includes('TABLE_ENCLOSURE') ||
        (authList.includes('TABLE_ENCLOSURE_NEEDEDIT') && canEdit)) && (
        <Attachment {...attachmentProps} />
      ),
      width: 200,
    },
    {
      name: 'enclosure',
      editor: (authList.includes('TABLE_ENCLOSURE') ||
        (authList.includes('TABLE_ENCLOSURE_NEEDEDIT') && canEdit)) && (
        <Attachment {...attachmentProps} />
      ),
      width: 200,
    },
    {
      name: 'reason',
    },
    {
      name: 'remindFlag',
      editor: record => {
        return authList.includes('REMIND_EDIT') && <Switch name="remindFlag" record={record} onChange={() => handleChangeRemindFlag(record)} />
      },
    },
  ];

  // 初始化页面
  const initPageData = async () => {
    // 页面编辑状态和列表选中状态初始化
    setCanEdit(false);
    setSelected([]);
    const res = await queryDetail.run({
      params: {
        problemWarnListId: id,
      },
    });

    // 查询到值给页面数据初始化
    if (res.success) {
      // 取出列表值和 表单值
      const { lineTaskList, ...detail } = res.rows || {};

      // 表单值加载
      formDs.loadData([detail]);
      // 列表值加载并加工
      tableDs.loadData(
        lineTaskList.map(item => {
          return {
            ...item,
            // 以下两种状态列表不能选中
            selectAuth: !['NEW', 'TO_RELEASE'].includes(detail.status),
          };
        }),
      );

      formDs.setState('userId', user.id);
      tableDs.setState('userId', user.id);

      // 保存页面状态
      setPageStatus(detail.status);

      // 保存打印内容
      setPrintObject({
        sourceProblem: detail.warnListCode,
        problemDesc: detail.problemDesc,
        reason: detail.reason,
        containmentAction: detail.containmentAction,
      });

      // 以下是权限判断
      const status = detail.status;
      const registUserId = detail.registUserId; // 11 登记人
      const leadUserId = detail.leadUserId; // 22 主导人人
      const managerIdList = detail.managerIdList; // 55 上级领导

      let authList: any = [];
      // 新建状态
      if (status === 'NEW') {
        if (user.id === registUserId) {
          // 只可编辑保存 警示单信息  和 使用提交按钮
          authList = [...authList, ...authMap.NEW];
        }
      }

      // 待发布状态
      if (status === 'TO_RELEASE') {
        if (user.id === leadUserId) {
          authList = [...authList, ...authMap.TO_RELEASE1, 'REMIND_EDIT'];
        }
      }

      // 发布状态
      if (status === 'RELEASED') {
        if (user.id === leadUserId) {
          authList = [...authList, 'TABLE_CANCEL', 'REMIND_EDIT'];
        }
        if (managerIdList?.includes(user.id)) {
          authList = [...authList, 'TABLE_APPROVE'];
        }
        lineTaskList.forEach(item => {
          if (user.id === item.responsibilityUserId) {
            authList = [...authList, 'TABLE_ENCLOSURE', 'TABLE_SUBMIT', 'TABLE_ENCLOSURE_REQUIRED'];
          }
        });
      }

      // 执行状态
      if (status === 'EXECUTING') {
        if (user.id === leadUserId) {
          authList = [...authList, 'TABLE_CANCEL', 'HEADER_CLOSE', 'REMIND_EDIT'];
        }
        if (managerIdList?.includes(user.id)) {
          authList = [...authList, 'TABLE_APPROVE'];
        }
        // 是否均为取消和通过
        lineTaskList.forEach(item => {
          if (user.id === item.responsibilityUserId) {
            authList = [...authList, 'TABLE_ENCLOSURE', 'TABLE_SUBMIT', 'TABLE_ENCLOSURE_REQUIRED'];
          }
        });
      }

      setAuthList(authList);
      formDs.setState('authList', authList);
      tableDs.setState('authList', authList);
    }
  };

  // 校验所有ds
  const validateAllDs = async () => {
    const itemValidate = await formDs.validate();
    const tableValidate = await tableDs.validate();
    let tableLength = true;
    if ((tableDs.getState('authList') || []).includes('TABLE_CREATE') && tableDs.length === 0) {
      tableLength = false;
      notification.error({
        message: intl.get(`${modelPrompt}.validate.table.length`).d('请添加警示展范围数据'),
      });
    }

    // 返回校验结果
    return itemValidate && tableValidate && tableLength;
  };

  // 组合数据
  const getAllData = () => {
    const params: any = formDs.toData()[0] || {};

    try {
      delete params.registUserLov;
      delete params.siteLov;
      delete params.prodLineLov;
      delete params.leadUserLov;
    } catch (e) {
      // 尝试删除多余属性
    }

    params.lineTaskList = (tableDs.toData() || []).map((item, index) => {
      const _item: any = { ...(item || {}) };
      try {
        delete _item.equipmentLov;
        delete _item.prodLineLov;
        delete _item.responsibilityUserLov;
        delete _item.siteLov;
        delete _item.processLov;
      } catch (e) {
        // 尝试删除多余属性
      }

      return {
        ..._item,
        planStartDate: moment(_item.planStartDate).format('YYYY-MM-DD'),
        planEndDate: moment(_item.planEndDate).format('YYYY-MM-DD'),
        sequence: 10 + index * 10,
      };
    });
    return {
      ...params,
      registDate: moment(params.registDate).format('YYYY-MM-DD'),
    };
  };

  // 保存
  const handleSave = async () => {

    const params = getAllData();

    await saveDetail.run({
      params,
      onSuccess: res => {
        // @ts-ignore
        notification.success();
        if (id === 'create') {
          props.history.push(`/hwms/problem-management/warning/detail/${res}`);
        } else {
          initPageData();
        }
      },
    });
  };

  const isLoading =
    queryDetail.loading ||
    saveDetail.loading ||
    warningSubmit.loading ||
    warningPublish.loading ||
    warningClosed.loading ||
    warningLineSubmit.loading ||
    warningLineCancel.loading ||
    warningLineReview.loading ||
    getUserDepartment.loading ||
    getUserDepartment.loading ||
    changeRemindFlag.loading;

  // 取消
  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hwms/problem-management/warning/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  // 发布
  const handleRelease = async () => {
    const pageValidate = await validateAllDs();
    if (!pageValidate) {
      setCanEdit(true);
      return;
    }
    return warningPublish.run({
      params: { problemWarnListId: formDs.current?.get('problemWarnListId') },
      onFailed: () => {
        return Promise.resolve(null);
      },
      onSuccess: () => {
        // @ts-ignore
        notification.success();
        initPageData();
      },
    });
  };

  // 头提交
  const handleSubmit = async () => {
    return warningSubmit.run({
      params: { problemWarnListId: formDs.current?.get('problemWarnListId') },
      onFailed: () => {
        return Promise.resolve(null);
      },
      onSuccess: () => {
        // @ts-ignore
        notification.success();
        initPageData();
      },
    });
  };

  // 头关闭
  const handleCloseButton = async () => {
    return warningClosed.run({
      params: { problemWarnListId: formDs.current?.get('problemWarnListId') },
      onFailed: () => {
        return Promise.resolve(null);
      },
      onSuccess: () => {
        // @ts-ignore
        notification.success();
        initPageData();
      },
    });
  };

  // 新增警示范围行
  const addLine = () => {
    tableDs.create({
      problemWarnListId: formDs.current?.get('problemWarnListId'),
      selectAuth: false,
      status: 'NEW',
      evidence: uuid(),
      enclosure: uuid(),
      remindFlag: 'N',
    });
  };

  const deleteLine = record => {
    tableDs.delete(record, false);
  };

  // 列表 取消按钮是否禁用
  const cancelAuth = () => {
    if (selected?.length > 0) {
      let authNo = false;
      selected.forEach(record => {
        if (!['TO_EXECUTE', 'AUDIT_REJECT'].includes(record.get('status'))) {
          authNo = true;
        }
      });
      return authNo;
    }
    return true;
  };

  // 列表审批按钮是否禁用
  const approveAuth = () => {
    if (selected?.length > 0) {
      let authNo = false;
      selected.forEach(record => {
        if (!['AUDITING'].includes(record.get('status'))) {
          authNo = true;
        }
      });
      return authNo;
    }
    return true;
  };

  // 头关闭按钮是否禁用
  const closeAuth = () => {
    let authNo = false;
    tableDs.forEach(record => {
      if (!['CANCEL', 'PASS'].includes(record.get('status'))) {
        authNo = true;
      }
    });
    return authNo;
  };

  // 列表提交按钮是否禁用
  const submitAuth = () => {
    if (selected?.length > 0) {
      const evidenceField = tableDs.getField('evidence');
      let authNo = false;
      selected.forEach(record => {
        if (!['TO_EXECUTE', 'AUDIT_REJECT'].includes(record.get('status'))) {
          authNo = true;
        }
        if (!evidenceField?.getAttachmentCount(record)) {
          authNo = true;
        }
      });
      return authNo;
    }
    return true;
  };

  const getSelectId = () => {
    const taskIds: any = [];
    (selected || []).forEach(record => {
      const _problemWarnTaskId = record.get('problemWarnTaskId');
      if (_problemWarnTaskId) {
        taskIds.push(_problemWarnTaskId);
      }
    });
    return taskIds;
  };

  // 行提交
  const lineSubmit = async () => {
    const taskIds = getSelectId();
    return warningLineSubmit.run({
      params: { problemWarnListId: formDs.current?.get('problemWarnListId'), taskIds },
      onFailed: () => {
        return Promise.resolve(null);
      },
      onSuccess: () => {
        // @ts-ignore
        notification.success();
        initPageData();
      },
    });
  };

  // iframe 打印
  const handlePrint = () => {
    // 判断当前人员是否允许打印
    const responsibilityUserIdList: any = [];
    tableDs.selected.forEach(record => {
      if (!responsibilityUserIdList.includes(record.get('responsibilityUserId'))) {
        responsibilityUserIdList.push(record.get('responsibilityUserId'))
      }
    });
    if (formDs.current?.get('leadUserId') !== user.id && (responsibilityUserIdList.length !== 1 || responsibilityUserIdList[0] !== user.id)) {
      return notification.error({
        message: intl.get(`${modelPrompt}.error.print`).d('只有主导人或当前责任人可以打印,请检查!'),
      });
    }
    const printHtml = document?.getElementById('wtjsPrintDiv')?.innerHTML; // 获取指定打印区域
    let iframe = document.getElementById('print-iframe');
    let doc = null;

    if (!iframe) {
      const el = document.getElementById('printcontent');
      iframe = document.createElement('IFRAME');
      iframe.setAttribute('id', 'print-iframe');
      iframe.setAttribute(
        'style',
        'position: absolute; width: 0px; height: 0px;left:0px;top:0px;color: red',
      );
      document.body.appendChild(iframe);
      // @ts-ignore
      doc = iframe.contentWindow.document;
      // 这里可以自定义样式
      // @ts-ignore
      doc.write('<style media="print">@page {size: auto; margin: 0 mm;} .wtjsPrintDivItem {page-break-before: always}</style>'); // 解决出现页眉页脚和路径的问题
      // @ts-ignore
      doc.write(`${printHtml}`);
      // @ts-ignore
      doc.close();
      // @ts-ignore
      iframe.contentWindow.focus();
    } else {
      // @ts-ignore
      doc = iframe.contentWindow.document;
      // 这里可以自定义样式
      // @ts-ignore
      doc.write('<style media="print">@page {size: auto; margin: 0 mm;} .wtjsPrintDivItem {page-break-before: always}</style>'); // 解决出现页眉页脚和路径的问题
      // @ts-ignore
      doc.write(`${printHtml}`);
      // @ts-ignore
      doc.close();
      // @ts-ignore
      iframe.contentWindow.focus();
    }
    // @ts-ignore
    iframe.contentDocument.body.setAttribute('style', '-webkit-print-color-adjust: exact;');

    setTimeout(function() {
      // @ts-ignore
      iframe.contentWindow.print();
    }, 50); // 解决第一次样式不生效的问题
    if (navigator.userAgent.indexOf('MSIE') > 0) {
      document.body.removeChild(iframe);
    }
  };

  // 行取消
  const lineCancel = async () => {
    modalDs.loadData([{}]);
    modalDs.setState('modalType', 'cancelReason');
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.cancel`).d('取消任务'),
      drawer: false,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: 500,
      },
      contentStyle: {
        width: 500,
      },
      className: 'hmes-style-modal',
      children: (
        <Form dataSet={modalDs} columns={1} labelWidth={80} style={{ marginRight: 20 }}>
          <TextField name="cancelReason" maxLength={999} showLengthInfo required />
        </Form>
      ),
      onOk: async () => {
        const modalValidate = await modalDs.validate();
        if (!modalValidate) {
          return false;
        }
        const taskIds = getSelectId();
        const reason = modalDs?.current?.get('cancelReason');
        return warningLineCancel.run({
          params: {
            problemWarnListId: formDs.current?.get('problemWarnListId'),
            taskIds,
            reason,
          },
          onFailed: () => {
            return Promise.resolve(null);
          },
          onSuccess: () => {
            // @ts-ignore
            notification.success();
            initPageData();
          },
        });
      },
    });
  };

  // 行审核
  const lineReview = async () => {
    modalDs.loadData([{}]);
    modalDs.setState('modalType', 'rejectReason');
    const taskIds = getSelectId();
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.reject`).d('驳回任务'),
      drawer: false,
      destroyOnClose: true,
      closable: true,
      maskClosable: true,
      style: {
        width: 500,
      },
      contentStyle: {
        width: 500,
      },
      className: 'hmes-style-modal',
      children: (
        <Form dataSet={modalDs} columns={1} labelWidth={80} style={{ marginRight: 20 }}>
          <TextField name="rejectReason" maxLength={999} showLengthInfo required />
        </Form>
      ),
      footer: (okBtn, cancelBtn, modal) => {
        return [
          <Button
            onClick={async () => {
              const modalValidate = await modalDs.validate();
              if (!modalValidate) {
                return false;
              }
              const reason = modalDs?.current?.get('rejectReason');
              return warningLineReview.run({
                params: {
                  problemWarnListId: formDs.current?.get('problemWarnListId'),
                  taskIds,
                  reason,
                  passedRejectFlag: 'REJECT',
                },
                onFailed: () => {
                  return Promise.resolve(null);
                },
                onSuccess: () => {
                  modal.close();
                  // @ts-ignore
                  notification.success();
                  initPageData();
                },
              });
            }}
          >
            {intl.get(`${modelPrompt}.modal.button.reject`).d('审批驳回')}
          </Button>,
          <Button
            onClick={async () => {
              return warningLineReview.run({
                params: {
                  problemWarnListId: formDs.current?.get('problemWarnListId'),
                  taskIds,
                  reason: '',
                  passedRejectFlag: 'PASS',
                },
                onFailed: () => {
                  return Promise.resolve(null);
                },
                onSuccess: () => {
                  modal.close();
                  // @ts-ignore
                  notification.success();
                  initPageData();
                },
              });
            }}
          >
            {intl.get(`${modelPrompt}.modal.button.pass`).d('审批通过')}
          </Button>,
        ];
      },
    });
  };

  const tableButtons = [
    <Button
      loading={isLoading}
      onClick={handlePrint}
      disabled={!selected.length}
    >
      {intl.get(`${modelPrompt}.button.print`).d('打印')}
    </Button>,
    <Button
      loading={isLoading}
      style={{
        display: authList.includes('TABLE_CANCEL') ? 'inline-block' : 'none',
      }}
      color={ButtonColor.primary}
      disabled={cancelAuth()}
      onClick={lineCancel}
    >
      {intl.get(`${modelPrompt}.table.button.cancel`).d('取消任务')}
    </Button>,
    <Button
      loading={isLoading}
      style={{
        display: authList.includes('TABLE_APPROVE') ? 'inline-block' : 'none',
      }}
      disabled={approveAuth()}
      color={ButtonColor.primary}
      onClick={lineReview}
    >
      {intl.get(`${modelPrompt}.table.button.approve`).d('审批')}
    </Button>,
    <Button
      loading={isLoading}
      style={{
        display: authList.includes('TABLE_SUBMIT') ? 'inline-block' : 'none',
      }}
      color={ButtonColor.primary}
      disabled={submitAuth()}
      onClick={lineSubmit}
    >
      {intl.get(`${modelPrompt}.table.button.submit`).d('提交')}
    </Button>,
  ];

  const userChange = async (value, name) => {
    if (value?.id) {
      const res = await getUserDepartment.run({
        params: {
          employeeId: value.id,
        },
      });

      if (res?.length > 0) {
        formDs?.current?.set(name, res[0].unitName);
      } else {
        formDs?.current?.set(name, null);
      }
    } else {
      formDs?.current?.set(name, null);
    }
  };

  const tableUserChange = async (value, record, name) => {
    if (value?.id) {
      const res = await getUserDepartment.run({
        params: {
          employeeId: value.id,
        },
      });

      if (res?.length > 0) {
        record.set(name, res[0].unitName);
      } else {
        record.set(name, null);
      }
    } else {
      record.set(name, null);
    }
  };

  const handleChangeCreateMethod = () => {
    formDs.current?.set('sourceProblemLov', undefined);
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.InspectionSchemeMaintenance`).d('问题警示平台')}
        backPath={pubFlag ? '' : '/hwms/problem-management/warning/list'}
      >
        {canEdit && (
          <>
            <Button
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              loading={isLoading}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button loading={isLoading} icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            {/* <Button onClick={handleSaveAndNext} loading={saveLoading}>
              {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并新建下一条')}
            </Button> */}
          </>
        )}
        {!canEdit && (
          <>
            {authList.includes('HEADER_EDIT') && (
              <PermissionButton
                loading={isLoading}
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="edit-o"
                onClick={() => {
                  setCanEdit(prev => !prev);
                }}
                // permissionList={[
                //   {
                //     code: `${path}.button.edit`,
                //     type: 'button',
                //     meaning: '详情页-编辑新建删除复制按钮',
                //   },
                // ]}
                disabled={!['NEW', 'TO_RELEASE'].includes(pageStatus || '')}
              >
                {intl.get(`${modelPrompt}.button.edit`).d('编辑')}
              </PermissionButton>
            )}
            {authList.includes('HEADER_RELEASED') && (
              <PermissionButton
                loading={isLoading}
                type="c7n-pro"
                color={ButtonColor.primary}
                onClick={handleRelease}
                // permissionList={[
                //   {
                //     code: `${path}.button.edit`,
                //     type: 'button',
                //     meaning: '详情页-编辑新建删除复制按钮',
                //   },
                // ]}
                disabled={pageStatus !== 'TO_RELEASE'}
              >
                {intl.get(`${modelPrompt}.button.release`).d('发布')}
              </PermissionButton>
            )}
            {authList.includes('HEADER_SUBMIT') && (
              <PermissionButton
                loading={isLoading}
                type="c7n-pro"
                color={ButtonColor.primary}
                onClick={handleSubmit}
                // permissionList={[
                //   {
                //     code: `${path}.button.edit`,
                //     type: 'button',
                //     meaning: '详情页-编辑新建删除复制按钮',
                //   },
                // ]}
                disabled={pageStatus !== 'NEW'}
              >
                {intl.get(`${modelPrompt}.button.submit`).d('提交')}
              </PermissionButton>
            )}
            {authList.includes('HEADER_CLOSE') && (
              <PermissionButton
                loading={isLoading}
                type="c7n-pro"
                color={ButtonColor.primary}
                onClick={handleCloseButton}
                disabled={closeAuth()}
                // permissionList={[
                //   {
                //     code: `${path}.button.edit`,
                //     type: 'button',
                //     meaning: '详情页-编辑新建删除复制按钮',
                //   },
                // ]}
              >
                {intl.get(`${modelPrompt}.button.close`).d('关闭')}
              </PermissionButton>
            )}
            <AttributeDrawer objectTypeList={['QIS_PROBLEM_WARN_APPRO']} objectId={id} />
          </>
        )}
      </Header>
      <Content>
        <Collapse
          collapsible="icon"
          bordered={false}
          defaultActiveKey={['panel1', 'panel2', 'panel3']}
        >
          <Panel
            header={intl.get(`${modelPrompt}.warningInformationTab`).d('警示单信息')}
            key="panel1"
          >
            <Form
              dataSet={formDs}
              columns={4}
              labelWidth={112}
              disabled={!canEdit || !authList.includes('BASE')}
            >
              <TextField name="warnListCode" />
              <Select name="status" />
              <Select name="createMethod" onChange={() => handleChangeCreateMethod()} />
              <Lov name="sourceProblemLov" />
              <Select name="severityLevel" />

              <Lov
                name="registUserLov"
                onChange={value => {
                  userChange(value, 'registUserDapartmentName');
                }}
              />
              <TextField name="registUserDapartmentName" />
              <DatePicker name="registDate" />
              <Lov name="siteLov" />

              <Lov name="prodLineLov" />
              <Lov
                name="leadUserLov"
                onChange={value => {
                  userChange(value, 'leadUserDapartmentName');
                }}
              />
              <TextField name="leadUserDapartmentName" />
            </Form>
          </Panel>

          <Panel
            header={intl.get(`${modelPrompt}.warningContent`).d('警示内容')}
            key="panel2"
          >
            <Form
              dataSet={formDs}
              columns={4}
              labelWidth={112}
              disabled={!canEdit || !authList.includes('DETAIL')}
            >
              <Select name="productLevel" />
              <Select name="warnListType" />

              <Attachment {...attachmentProps} name="enclosure" />

              <TextArea newLine colSpan={4} name="problemDesc" />
              <TextArea newLine colSpan={4} name="reason" />
              <TextArea newLine colSpan={4} name="containmentAction" />
            </Form>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.warningProjectDistributionTab`).d('警示范围')}
            key="panel3"
          >
            <Table
              customizedCode="wtjs2"
              buttons={tableButtons}
              dataSet={tableDs}
              columns={tableColumn}
            />
          </Panel>
        </Collapse>
      </Content>
      <div id="wtjsPrintDiv" style={{ display: 'none' }}>
        {(selected || []).map((_record) => (
          <div
            style={{
              padding: '20px',
              fontSize: '40px',
              color: '#333333',
              backgroundColor: '#ecea42',
            }}
            className='wtjsPrintDivItem'
          >
            <div style={{ fontSize: '80px', textAlign: 'center' }}>质量警示</div>
            <div>
              <span style={{ fontSize: '50px' }}>编号: </span> {printObject.sourceProblem || ''}
            </div>
            <div>
              <span style={{ fontSize: '50px' }}>问题描述: </span> {printObject.problemDesc || ''}
            </div>
            <div>
              <span style={{ fontSize: '50px' }}>原因分析: </span> {printObject.reason || ''}
            </div>
            <div>
              <span style={{ fontSize: '50px' }}>警示措施: </span>{' '}
              {printObject.containmentAction || ''}
            </div>
            <div>
              <span style={{ fontSize: '50px' }}>计划开始时间: </span>{' '}
              {moment(_record?.get('planStartDate')).format('YYYY-MM-DD') || ''}
            </div>
            <div>
              <span style={{ fontSize: '50px' }}>计划完成时间: </span>{' '}
              {moment(_record?.get('planEndDate')).format('YYYY-MM-DD') || ''}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.warning', 'tarzan.common'],
})(WarningTable);
