/**
 * @Description: 列拖拽
 * @Author: <<EMAIL>>
 * @Date: 2021-09-14 15:41:22
 * @LastEditTime: 2022-05-17 15:11:02
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect } from 'react';
import TopicList from './topicList';
import styles from '../index.module.less';

const Container = ({ previewList, handlePreviewList, getChildList, canEdit }) => {
  const [topic, setTopic] = useState(previewList);
  const [isDragging, setIsDragging] = useState(false);
  useEffect(() => {
    setTopic(previewList);
  }, [previewList]);

  const handleDND = (dragIndex, hoverIndex, dragId) => {
    let canMove = false;
    // 确认包含该元素
    previewList.forEach(item => {
      if (item.questionTuid === dragId) {
        canMove = true;
      }
    });
    if (!canMove) {
      return;
    }
    // 临时储存文件
    const tmp = previewList[dragIndex];
    // 移除拖拽项
    previewList.splice(dragIndex, 1);
    // 插入放置项
    previewList.splice(hoverIndex, 0, tmp);
    handlePreviewList(previewList);
  };
  const draggingStateChange = state => {
    if (isDragging !== state) {
      setIsDragging(state);
    }
  };

  const renderCard = (item, index) => {
    return (
      <TopicList
        previewList={previewList}
        key={item.questionTuid}
        index={index}
        id={item.questionTuid}
        text={item.questionContent}
        moveCard={handleDND}
        getChildList={getChildList}
        draggingState={isDragging}
        draggingStateChange={draggingStateChange}
      />
    );
  };
  return (
    <div className={styles['row-drag-container']}>
      {topic.map((item, i) => renderCard(item, i))}
      {!canEdit && <div className={styles['row-drag-container-mask']} />}
    </div>
  );
};

export default Container;
