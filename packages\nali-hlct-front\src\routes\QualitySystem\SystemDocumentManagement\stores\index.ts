/*
 * @Description: 质量体系文件管理-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-11-02 14:25:07
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-11-06 20:00:03
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.qms.systemDocumentManagement';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const tableDS: (listFlag: boolean) => DataSetProps = listFlag => ({
  autoQuery: listFlag,
  autoCreate: !listFlag,
  selection: DataSetSelection.multiple,
  dataKey: listFlag ? 'content' : undefined,
  totalKey: 'totalElements',
  primaryKey: 'fileId',
  paging: listFlag,
  queryFields: [
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编号'),
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件名称'),
    },
    {
      name: 'fileLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileLevel`).d('文件级别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_LEVEL',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.editedDepartment`).d('编制专业'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: { tenantId },
    },
    {
      name: 'editedDepartment',
      type: FieldType.number,
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.editedBy`).d('编制人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: { tenantId },
    },
    {
      name: 'editedBy',
      bind: 'responsLov.id',
    },
    {
      name: 'fileStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileStatus`).d('文件状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_STATUS',
      defaultValue: listFlag ? 'PUBLISHED' : undefined,
    },
    {
      name: 'editedDateFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.editedDateFrom`).d('编制日期从'),
      max: 'editedDateTo',
    },
    {
      name: 'editedDateTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.editedDateTo`).d('编制日期至'),
      min: 'editedDateFrom',
    },
    {
      name: 'publishDateFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.publishDateFrom`).d('发布日期从'),
      max: 'publishDateTo',
    },
    {
      name: 'publishDateTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.publishDateTo`).d('发布日期至'),
      min: 'publishDateFrom',
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.affliatedProcess`).d('所属过程'),
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
      lovPara: { tenantId },
    },
    {
      name: 'currentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentFlag`).d('是否最新'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
      defaultValue: listFlag ? 'Y' : undefined,
    },
  ],
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
    },
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编码'),
      disabled: true,
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件名称'),
      required: true,
      // computedProps: {
      //   disabled: ({ record }) => record?.get('fileStatus') !== 'STAGING',
      // },
      computedProps: {
        disabled: ({ record, dataSet }) => {
          // fileStatus为STAGING时可编辑
          if (record?.get('fileStatus') === 'STAGING') {
            return false;
          }
          // fileStatus为UNPUBLISHED，且编制人为当前操作人或管理员时可编辑
          if (
            record.get('fileStatus') === 'UNPUBLISHED' &&
            (record?.get('editedBy') === userInfo.id || dataSet.getState('adminFlag'))
          ) {
            return false;
          }
          // 其余情况不可编辑
          return true;
        },
      },
    },
    {
      name: 'fileLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileLevel`).d('文件级别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_LEVEL',
      required: true,
      computedProps: {
        disabled: ({ record }) => record?.get('fileStatus') !== 'STAGING',
      },
    },
    {
      name: 'version',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.version`).d('版本号'),
    },
    {
      name: 'processType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processType`).d('程序类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROCESS_TYPE',
      disabled: true,
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.editedDepartment`).d('编制专业'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'unitName',
      lovPara: { tenantId },
      computedProps: {
        disabled: ({ record, dataSet }) => {
          // fileStatus为STAGING且编制人为当前操作人时可编辑
          // fileStatus为UNPUBLISHED，fileLevel为SECOND且编制人为当前操作人或管理员时可编辑
          // 其余情况不可编辑
          return !(
            (record.get('fileStatus') === 'STAGING' && record?.get('editedBy') === userInfo.id) ||
            (record.get('fileStatus') === 'UNPUBLISHED' &&
              ['FIRST', 'SECOND', 'THIRD'].includes(record.get('fileLevel')) &&
              (record?.get('editedBy') === userInfo.id || dataSet.getState('adminFlag')))
          );
        },
        lovPara: ({ record }) => {
          if (record.get('fileStatus') === 'UNPUBLISHED' && record.get('fileLevel') === 'THIRD') {
            return {
              tenantId,
              editedDepartmentParent: record?.get('originEditedDepartmentParent'),
            };
          }
          return { tenantId };
        },
      },
    },
    {
      name: 'editedDepartment',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'editedDepartmentName',
      label: intl.get(`${modelPrompt}.editedDepartment`).d('编制专业'),
      bind: 'departmentLov.unitName',
    },
    // 打开抽屉时初始的编制部门
    {
      name: 'originEditedDepartmentParent',
    },
    {
      name: 'editedDepartmentParent',
      bind: 'departmentLov.parentUnitId',
    },
    {
      name: 'editedDepartmentParentName',
      label: intl.get(`${modelPrompt}.editedDepartmentParentName`).d('编制部门'),
      bind: 'departmentLov.parentUnitName',
      disabled: true,
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.editedBy`).d('编制人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: { tenantId },
      required: true,
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
      computedProps: {
        disabled: ({ record, dataSet }) => {
          // fileStatus为STAGING 且编制人为当前操作人时可编辑
          // fileStatus为UNPUBLISHED，fileLevel为SECOND或THIRD 且编制人为当前操作人或管理员时可编辑
          // 其余情况不可编辑
          return !(
            (record.get('fileStatus') === 'STAGING' && record?.get('editedBy') === userInfo.id) ||
            (record.get('fileStatus') === 'UNPUBLISHED' &&
              ['FIRST', 'SECOND', 'THIRD'].includes(record.get('fileLevel')) &&
              (record?.get('editedBy') === userInfo.id || dataSet.getState('adminFlag')))
          );
        },
      },
    },
    {
      name: 'editedBy',
      bind: 'responsLov.id',
    },
    {
      name: 'editedByRealName',
      label: intl.get(`${modelPrompt}.editedBy`).d('编制人'),
      bind: 'responsLov.realName',
    },
    {
      name: 'editedDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.editedDate`).d('编制日期'),
      required: true,
      computedProps: {
        disabled: ({ record, dataSet }) => {
          // fileStatus为STAGING 且编制人为当前操作人时可编辑
          // fileStatus为UNPUBLISHED，fileLevel为SECOND或THIRD 且编制人为当前操作人或管理员时可编辑
          // 其余情况不可编辑
          return !(
            (record.get('fileStatus') === 'STAGING' && record?.get('editedBy') === userInfo.id) ||
            (record.get('fileStatus') === 'UNPUBLISHED' &&
              ['FIRST', 'SECOND', 'THIRD'].includes(record.get('fileLevel')) &&
              (record?.get('editedBy') === userInfo.id || dataSet.getState('adminFlag')))
          );
        },
      },
    },
    {
      name: 'publishDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.publishDate`).d('发布日期'),
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.affliatedProcess`).d('所属过程'),
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
      lovPara: { tenantId },
      computedProps: {
        disabled: ({ record }) => {
          return (
            !record.get('fileLevel') ||
            record.get('fileLevel') === 'FORTH' ||
            record?.get('fileStatus') !== 'STAGING'
          );
        },
        required: ({ record }) => {
          return !['FIRST', 'FORTH'].includes(record.get('fileLevel'));
        },
      },
    },
    {
      name: 'uuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.uuid`).d('上传文档'),
      bucketName: 'qms',
    },
    {
      name: 'levelLimitLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.levelLimitLov`).d('关联上级文件'),
      lovCode: 'YP.QIS.FILE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('fileLevel') !== 'FORTH' || record?.get('fileStatus') !== 'STAGING';
        },
        required: ({ record }) => {
          return record.get('fileLevel') === 'FORTH';
        },
      },
    },
    {
      name: 'parentFile',
      bind: 'levelLimitLov.fileId',
    },
    {
      name: 'parentFileName',
      bind: 'levelLimitLov.fileName',
    },
    {
      name: 'parentUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.parentUuid`).d('关联文件'),
      bucketName: 'qms',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lookupCode: 'YP.QIS.YN_FLAG',
      defaultValue: 'N',
      disabled: true,
    },
    {
      name: 'fileStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileStatus`).d('文件状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_STATUS',
      defaultValue: 'STAGING',
    },
    {
      name: 'currentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentFlag`).d('是否最新'),
      lookupCode: 'YP.QIS.YN_FLAG',
      lovPara: { tenantId },
      defaultValue: 'Y',
      disabled: true,
      required: true,
    },
    {
      name: 'cancelReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消原因'),
    },
    {
      name: 'abandonReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.abandonReason`).d('废弃理由'),
    },
    {
      name: 'relatedFileList',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.relatedFileList`).d('相关文件'),
      lovCode: 'YP.QIS.RELATED_FILE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        limitFileLevel: 'SECOND,THIRD',
      },
      multiple: true,
      dynamicProps: {
        disabled: ({ record, dataSet }) => {
          // fileLevel为为二级、三级
          // fileStatus为【暂存】、【待发布】时，编制人可新增或编辑
          // fileStatus为【已发布】时，文件管理员角色可新增或编辑
          // 其余情况不可编辑
          return !(
            ((['STAGING', 'UNPUBLISHED'].includes(record.get('fileStatus')) &&
              record?.get('editedBy') === userInfo.id) ||
              (record.get('fileStatus') === 'PUBLISHED' && dataSet.getState('adminFlag'))) &&
            ['SECOND', 'THIRD'].includes(record.get('fileLevel'))
          );
        },
      },
    },
    {
      name: 'relatedFileId',
      bind: 'relatedFileList.fileId',
    },
    {
      name: 'relatedFileCode',
      label: intl.get(`${modelPrompt}.relatedFileCode`).d('文件编码'),
      bind: 'relatedFileList.fileCode',
    },
    {
      name: 'relatedFileName',
      label: intl.get(`${modelPrompt}.relatedFileName`).d('文件名称'),
      bind: 'relatedFileList.fileName',
    },
  ],
  transport: {
    read: () => {
      return {
        url: listFlag
          ? `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/list/for/ui`
          : `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/detail/for/ui`,
        method: 'GET',
      };
    },
  },
});

const updateDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'fileId',
      type: FieldType.number,
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
    },
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编码'),
      disabled: true,
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件名称'),
      disabled: true,
    },
    {
      name: 'fileLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileLevel`).d('文件级别'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_LEVEL',
      disabled: true,
    },
    {
      name: 'processType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processType`).d('程序类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.PROCESS_TYPE',
      disabled: true,
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.editedDepartment`).d('编制专业'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'unitName',
      lovPara: { tenantId },
      computedProps: {
        disabled: ({ record }) => {
          return !['FIRST', 'SECOND', 'THIRD'].includes(record.get('fileLevel'));
        },
        lovPara: ({ record }) => {
          if (record.get('fileLevel') === 'THIRD') {
            return {
              tenantId,
              editedDepartmentParent: record?.get('originEditedDepartmentParent'),
            };
          }
          return { tenantId };
        },
      },
    },
    {
      name: 'editedDepartment',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'editedDepartmentName',
      label: intl.get(`${modelPrompt}.editedDepartment`).d('编制专业'),
      bind: 'departmentLov.unitName',
    },
    // 打开抽屉时初始的编制部门
    {
      name: 'originEditedDepartmentParent',
    },
    {
      name: 'editedDepartmentParent',
      bind: 'departmentLov.parentUnitId',
    },
    {
      name: 'editedDepartmentParentName',
      label: intl.get(`${modelPrompt}.editedDepartmentParentName`).d('编制部门'),
      bind: 'departmentLov.parentUnitName',
      disabled: true,
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.editedBy`).d('编制人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: { tenantId },
      required: true,
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'editedBy',
      bind: 'responsLov.id',
    },
    {
      name: 'editedByRealName',
      label: intl.get(`${modelPrompt}.editedBy`).d('编制人'),
      bind: 'responsLov.realName',
    },
    {
      name: 'editedDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.editedDate`).d('编制日期'),
      required: true,
    },
    {
      name: 'publishDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.publishDate`).d('发布日期'),
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.affliatedProcess`).d('所属过程'),
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
      lovPara: { tenantId },
      disabled: true,
    },
    {
      name: 'uuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.uuid`).d('上传文档'),
      bucketName: 'qms',
      required: true,
    },
    {
      name: 'upgradeType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.upgradeType`).d('升版类型'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.UPGRADE_TYPE',
      required: true,
    },
    {
      name: 'levelLimitLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.levelLimitLov`).d('关联上级文件'),
      lovCode: 'YP.QIS.FILE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        limitFileLevel: 'SECOND,THIRD',
      },
      disabled: true,
    },
    {
      name: 'parentFile',
      bind: 'levelLimitLov.fileId',
    },
    {
      name: 'parentFileName',
      bind: 'levelLimitLov.fileName',
    },
    {
      name: 'parentUuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.parentUuid`).d('关联文件'),
      bucketName: 'qms',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lookupCode: 'YP.QIS.YN_FLAG',
      defaultValue: 'N',
      disabled: true,
    },
    {
      name: 'fileStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileStatus`).d('文件状态'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_STATUS',
      defaultValue: 'STAGING',
    },
    {
      name: 'relatedFileList',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.relatedFileList`).d('相关文件'),
      lovCode: 'YP.QIS.RELATED_FILE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        limitFileLevel: 'SECOND,THIRD',
      },
      multiple: true,
      disabled: true,
    },
    {
      name: 'relatedFileId',
      bind: 'relatedFileList.fileId',
    },
    {
      name: 'relatedFileCode',
      label: intl.get(`${modelPrompt}.relatedFileCode`).d('文件编码'),
      bind: 'relatedFileList.fileCode',
    },
    {
      name: 'relatedFileName',
      label: intl.get(`${modelPrompt}.relatedFileName`).d('文件名称'),
      bind: 'relatedFileList.fileName',
    },
  ],
});

const historyDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  fields: [
    {
      name: 'seq',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编码'),
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件名称'),
    },
    {
      name: 'version',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.version`).d('版本号'),
    },
    {
      name: 'editedDepartmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedDepartmentName`).d('编制专业'),
    },
    {
      name: 'editedDepartmentParentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedDepartmentParentName`).d('编制部门'),
    },
    {
      name: 'editedByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedByRealName`).d('编制人'),
    },
    {
      name: 'editedDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedDate`).d('编制日期'),
    },
    {
      name: 'publishDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.publishDate`).d('发布日期'),
    },
    {
      name: 'affliatedProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.affliatedProcess`).d('所属过程'),
      lookupCode: 'YP.QIS.AFFLIATED_PROCESS',
      lovPara: { tenantId },
    },
    {
      name: 'fileStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileStatus`).d('状态'),
      lookupCode: 'YP.QIS.FILE_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByRealName`).d('最后更新人'),
    },
    {
      name: 'operationTypeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationType`).d('操作类型'),
      // lookupCode: 'MT.SAMPLING.STANDARD',
      // lovPara: {
      //   tenantId,
      // },
    },
    {
      name: 'uuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.uuid`).d('上传文档'),
      // max: 9,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-file-hiss/list/for/ui`,
        method: 'GET',
      };
    },
  },
});

const approveViewDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  fields: [
    {
      name: 'seq',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编码'),
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件名称'),
    },
    {
      name: 'version',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.version`).d('版本号'),
    },
    {
      name: 'operationTypeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationTypeMeaning`).d('审批类型'),
    },
    {
      name: 'approvedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.approvedByName`).d('审批人'),
    },
    {
      name: 'approveResultMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.approveResultMeaning`).d('审批结果'),
    },
    {
      name: 'approvedReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.approvedReason`).d('审批意见'),
    },
    {
      name: 'approvedDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.approvedDate`).d('审批时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-system-files/approve/view/for/ui`,
        method: 'GET',
      };
    },
  },
});

const examineDS: () => DataSetProps = () => ({
  autoCreate: true,
  selection: false,
  autoQuery: false,
  fields: [
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.departmentLov`).d('审核部门'),
      required: true,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      multiple: true,
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'unitId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsibleDeptCode',
      bind: 'departmentLov.unitCode',
    },
    {
      name: 'responsibleDeptName',
      bind: 'departmentLov.unitName',
    },
    {
      name: 'responsLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsLov`).d('通知人员'),
      required: true,
      lovCode: 'YP.SYSTEM_FILE_NOTICE',
      multiple: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      // optionsProps:  (dsProps) => {
      //   return {
      //     ...dsProps,
      //     primaryKey: 'id',
      //   }
      // },
    },
    {
      name: 'noticeId',
      bind: 'responsLov.id',
    },
    {
      name: 'applicationReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applicationReason`).d('申请理由'),
      required: true,
    },
  ],
});

const cancelReasonDS: () => DataSetProps = () => ({
  autoCreate: true,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'cancelReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cancelReason`).d('取消原因'),
    },
  ],
});

const releateForthFileDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  forceValidate: true,
  selection: false,
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编码'),
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件名称'),
      required: true,
    },
    {
      name: 'uuid',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.uuidOnlyRead`).d('附件'),
      bucketName: 'qms',
      bucketDirectory: 'system-document-management',
    },
    {
      name: 'fileLevel',
      type: FieldType.string,
      defaultValue: 'FORTH',
    },
    {
      name: 'fileStatus',
      label: intl.get(`${modelPrompt}.fileStatus`).d('文件状态'),
      type: FieldType.string,
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.FILE_STATUS',
    },
  ],
});

const relatedFileDS: () => DataSetProps = () => ({
  autoCreate: false,
  paging: false,
  forceValidate: true,
  selection: false,
  fields: [
    {
      name: 'fileCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileCode`).d('文件编码'),
    },
    {
      name: 'fileName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fileName`).d('文件名称'),
      required: true,
    },
  ],
});

export {
  tableDS,
  historyDS,
  approveViewDS,
  examineDS,
  cancelReasonDS,
  updateDS,
  releateForthFileDS,
  relatedFileDS,
};
