import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.deliverQuery';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'equipmentId',
    autoQuery: true,
    selection: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      },
      {
        name: 'equipmentDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentDesc`).d('设备描述'),
      },
      {
        name: 'workcellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
      },
      {
        name: 'workcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellName`).d('工位名称'),
      },
      {
        name: 'equipmentStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentStatusDesc`).d('状态'),
      },
      {
        name: 'equipmentBrand',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentBrand`).d('品牌'),
      },
      {
        name: 'equipmentModel',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentModel`).d('型号'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('标识'),
      },
      {
        name: 'equipmentLocationCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentLocationCode`).d('位置编码'),
      },
      {
        name: 'equipmentLocationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentLocationName`).d('位置名称'),
      },
      {
        name: 'equipmentPhysicsStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentPhysicsStatusDesc`).d('物理状态描述'),
      },
      {
        name: 'equipmentFinanceStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentFinanceStatusDesc`).d('财务状态'),
      },
      {
        name: 'equipmentRegcheckStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentRegcheckStatusDesc`).d('定检状态描述'),
      },
    ],
    queryFields: [
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/equipments/list/query-by-condition`,
          method: 'GET',
        };
      },
    },
  };
};



export {tableDS};
