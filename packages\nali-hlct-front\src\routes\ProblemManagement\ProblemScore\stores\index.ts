/*
 * @Description: 问题积分报表-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-11-30 13:48:02
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-06 09:24:15
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.problemManagement.problemScore';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'year',
      type: FieldType.year,
      label: intl.get(`${modelPrompt}.year`).d('年份'),
      required: true,
      defaultValue: new Date().getFullYear(),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      required: true,
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'siteName',
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'orderType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderType`).d('排序方式'),
      lookupCode: 'YP.QIS.ORDER_TYPE',
      lovPara: { tenantId },
      required: true,
      dynamicProps: {
        defaultValue: () => {
          // 拿到当前月份(得到的月份是从0开始)
          const currentMonth = new Date().getMonth();
          switch (Math.floor(currentMonth / 3)) {
            case 0:
              return 'FIRST';
            case 1:
              return 'SECOND';
            case 2:
              return 'THIRD';
            case 3:
              return 'FOURTH';
            default:
              return 'YEAR';  
          }
        },
      },
    },
    {
      name: 'employeeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.employeeName`).d('员工姓名'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      lovPara: { tenantId },
      textField: 'realName',
      multiple: true,
    },
    {
      name: 'userId',
      bind: 'employeeLov.id',
    },
    {
      name: 'unitLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.unitName`).d('科室'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      lovPara: {
        specDept: 'Y',
        tenantId,
      },
    },
    {
      name: 'unitId',
      bind: 'unitLov.unitId',
    },
    {
      name: 'top',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.top`).d('TOP'),
      min: 1,
      precision: 0,
    },
  ],
  fields: [
    {
      name: 'userName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userName`).d('姓名'),
    },
    {
      name: 'userNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userNumber`).d('工号'),
    },
    {
      name: 'userPost',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userPost`).d('岗位'),
    },
    {
      name: 'department',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.department`).d('科室'),
    },
    {
      name: 'year',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.year`).d('年份'),
    },
    {
      name: 'firstScore',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstScore`).d('第一季度得分'),
    },
    {
      name: 'firstRanking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstRanking`).d('第一季度排名'),
    },
    {
      name: 'secondScore',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondScore`).d('第二季度得分'),
    },
    {
      name: 'secondRanking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondRanking`).d('第二季度排名'),
    },
    {
      name: 'thirdScore',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.thirdScore`).d('第三季度得分'),
    },
    {
      name: 'thirdRanking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.thirdRanking`).d('第三季度排名'),
    },
    {
      name: 'fourthScore',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fourthScore`).d('第四季度得分'),
    },
    {
      name: 'fourthRanking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fourthRanking`).d('第四季度排名'),
    },
    {
      name: 'yearScore',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.yearScore`).d('年度总得分'),
    },
    {
      name: 'yearRanking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.yearRanking`).d('总得分排名'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-problem-scores/problem/points/report`,
        method: 'POST',
        data: {
          ...data,
          year: data?.year ? moment(data?.year).format('yyyy') : undefined,
        },
      };
    },
  },
});

export { tableDS };