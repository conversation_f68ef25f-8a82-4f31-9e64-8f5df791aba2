import { BASIC } from '@/utils/config';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import {DataSet} from "choerodon-ui/pro";

const modelPrompt = 'tarzan.inLibrary.sendReceiveDocMes';
const tenantId = getCurrentOrganizationId();

// const endUrl = '-30607';
const endUrl = '';

const headerBindTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: false,
  primaryKey: 'instructionDocId',
  autoLocateFirst: true,
  transport: {
    read: ({ data }) => {
      const newData = {
        ...data,
        locatorId: (data.locatorIds && data.locatorIds.length > 0) ? null : data.locatorId,
      };
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/mt-product-delivery-platform/line/specify/for/ui`,
        data: newData,
        method: 'POST',
      };
    },
  },
  events: {
    load: ({ dataSet }) => {
      dataSet.records.map(item => {
        item.selectable = item.get('instructionDetailFlag') !== 'Y';
        return item;
      });
    },
  },
  queryFields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'lotCodes',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
      multiple: true,
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      textField: 'meaning',
      valueField: 'value',
      lookupCode: 'MT.ENABLE_FLAG',
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      multiple: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'locatorIds',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorCodes',
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierLots',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
      multiple: true,
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'inLocatorTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inLocatorTimeFrom`).d('入库时间从'),
      max: 'inLocatorTimeTo',
    },
    {
      name: 'inLocatorTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inLocatorTimeTo`).d('入库时间至'),
      min: 'inLocatorTimeFrom',
    },
    {
      name: 'inSiteTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inSiteTimeFrom`).d('入站时间从'),
      max: 'inSiteTimeTo',
    },
    {
      name: 'inSiteTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inSiteTimeTo`).d('入站时间至'),
      min: 'inSiteTimeFrom',
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
      options: new DataSet({
        autoQuery: true,
        dataKey: 'rows',
        paging: false,
        transport: {
          read: () => {
            return {
              url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
              method: 'GET',
              params: { tenantId },
              transformResponse: val => {
                const data = JSON.parse(val);
                data.rows.push({
                  description: intl.get(`tarzan.common.ownerType`).d('自有'),
                  typeCode: 'ALL',
                  typeGroup: 'OWNER_TYPE',
                });
                return {
                  ...data,
                };
              },
            };
          },
        },
      }),
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'ownerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        lovCode({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'MT.MODEL.CUSTOMER';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'MT.MODEL.SUPPLIER';
            case 'OI':
              return 'MT.MES.SO_LINE';
            default:
              return 'MT.MES.EMPTY';
          }
        },
        textField({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'customerCode';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'supplierCode';
            case 'OI':
              return 'soNumContent';
            default:
              return 'noData';
          }
        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI', 'OD'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerLov.customerId',
      dynamicProps: {
        bind({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'ownerLov.customerId';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'ownerLov.supplierId';
            case 'OI':
              return 'ownerLov.soLineId';
            default:
              return 'ownerLov.customerId';
          }
        },
      },
    },
    {
      name: 'reservedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
      textField: 'meaning',
      valueField: 'value',
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'reservedObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectTypeDesc`).d('预留对象类型'),
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=RESERVE_OBJECT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'reservedObjectLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'MT.MODEL.CUSTOMER';
            case 'WO':
              return 'MT.WORK_ORDER';
            case 'EO':
              return 'MT.EO';
            case 'PO_LINE':
              return 'MT.MES.PO_LINE';
            default:
              return 'MT.MES.EMPTY';
          }
        },
        textField: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'customerCode';
            case 'WO':
              return 'workOrderNum';
            case 'EO':
              return 'eoNum';
            case 'PO_LINE':
              return 'poNumberAndLine';
            default:
              return 'noData';
          }
        },
        disabled: ({ record }) => {
          return (
            !record.get('reservedObjectType') ||
            ['OO', 'DRIVING'].includes(record.get('reservedObjectType'))
          );
        },
      },
    },
    {
      name: 'reservedObjectId',
      type: FieldType.number,
      bind: 'reservedObjectLov.customerId',
      dynamicProps: {
        bind({ record }) {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'reservedObjectLov.customerId';
            case 'WO':
              return 'reservedObjectLov.workOrderId';
            case 'EO':
              return 'reservedObjectLov.eoId';
            case 'PO_LINE':
              return 'reservedObjectLov.poLineId';
            default:
              return 'reservedObjectLov.customerId';
          }
        },
      },
    },
  ],
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('主单位数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('主单位'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      textField: 'meaning',
      valueField: 'value',
      lookupCode: 'MT.ENABLE_FLAG',
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'productionDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    },
    {
      name: 'expirationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expirationDate`).d('到期日期'),
    },
    {
      name: 'extendedShelfLifeTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.extendedShelfLifeTimes`).d('延保次数'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierDesc`).d('供应商描述'),
    },
    {
      name: 'supplierSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteCode`).d('供应商地点编码'),
    },
    {
      name: 'supplierSiteDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteDesc`).d('供应商地点描述'),
    },
    {
      name: 'customerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
    },
    {
      name: 'customerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerDesc`).d('客户描述'),
    },
    {
      name: 'customerSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerSiteCode`).d('客户地点编码'),
    },
    {
      name: 'customerSiteDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerSiteDesc`).d('客户地点描述'),
    },
    {
      name: 'reservedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
    },
    {
      name: 'reservedObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectTypeDesc`).d('预留对象类型'),
    },
    {
      name: 'reservedObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
    },
    {
      name: 'createReasonDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createReasonDesc`).d('创建原因'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('入库时间'),
    },
    {
      name: 'inSiteTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inSiteTime`).d('入站时间'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
    {
      name: 'ownerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerDesc`).d('所有者描述'),
    },
    {
      name: 'createdUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdUsername`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedUsername`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
});

const lineBindTableDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    selection: 'multiple',
    cacheSelection: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    primaryKey: 'materialLotCode',
    autoLocateFirst: true,
    transport: {
      read: () => {
        return {
          // 已绑定物料批
          url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/mt-product-delivery-platform/line/specify-ins/for/ui`,
          method: 'POST',
        };
      },
    },
    queryFields: [
      {
        name: 'materialLotCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'identification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
      },
      {
        name: 'lotCodes',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.lot`).d('批次'),
        multiple: true,
      },
      {
        name: 'materialLotStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
        textField: 'description',
        valueField: 'statusCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
    ],
    fields: [
      {
        name: 'identification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
      },
      {
        name: 'materialLotCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'primaryUomQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.primaryUomQty`).d('主单位数量'),
      },
      {
        name: 'primaryUomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.primaryUomCode`).d('主单位'),
      },
      {
        name: 'lot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lot`).d('批次'),
      },
      {
        name: 'materialLotStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
        textField: 'description',
        valueField: 'statusCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
    ],
  };
};

export { headerBindTableDS, lineBindTableDS };
