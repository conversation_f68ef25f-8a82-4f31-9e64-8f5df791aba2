/*
 * @Description: 事务类型接口关系 调用后端接口
 * @Author: YinWQ
 * @Date: 2023-07-19 10:36:05
 * @LastEditors: YinWQ
 * @LastEditTime: 2023-07-19 15:22:39
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const API = `${BASIC.HWMS_BASIC}/v1/${getCurrentOrganizationId()}`;
// const API = `/key-focus-mes-30607/v1/${getCurrentOrganizationId()}`;
// 工艺与工作单元关系维护-删除关系
export function DeleteItems() {
  return {
    url: `${API}/trans-type/rel/delete/ui`,
    method: 'POST',
  };
}

// 工艺与工作单元关系维护-保存关系
export function SaveItem() {
  return {
    url: `${API}/trans-type/rel/save/ui`,
    method: 'POST',
  };
}
