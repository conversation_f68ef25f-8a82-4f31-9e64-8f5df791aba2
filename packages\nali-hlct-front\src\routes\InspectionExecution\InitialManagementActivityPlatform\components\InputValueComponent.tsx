/**
 * @Description: 检验组维护-数值类型多值或单值组件
 * @Author: <<EMAIL>>
 * @Date: 2023-01-12 10:56:36
 * @LastEditTime: 2023-05-08 14:02:32
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { NumberField, Tooltip, Button, TextField, CheckBox } from 'choerodon-ui/pro';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { isNumber } from 'lodash';
import intl from 'utils/intl';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.initialManagementActivity';

const InputValueComponent = props => {
  const {
    clearFlag,
    disabledFlag,
    name,
    canEdit,
    numberDS,
    numberList,
    setNumberList,
    showStandard,
  } = props;

  // 数值-值列表增加
  const handleNumberAdd = () => {
    const _newDataInfo = {
      valueType: 'single',
      leftValue: '',
      rightValue: '',
      leftChar: '[',
      rightChar: ']',
      valueShow: '≤X≤',
      singleValued: '',
      standard: '',
    };
    numberDS.create(_newDataInfo);
    const _newNumberList = [...numberList, _newDataInfo];
    setNumberList(_newNumberList);
  };

  // 数值-值列表删除
  const handleNumberDelete = index => {
    const _newNumberList = [...numberList];
    _newNumberList.splice(index, 1);
    setNumberList(_newNumberList);
    numberDS.remove(numberDS.get(index));
  };

  // 数值-值的类型切换
  const setValueCategory = (valueType, index) => {
    const _leftChar = valueType === 'section' ? '[' : '';
    const _rightChar = valueType === 'section' ? ']' : '';
    const _valueShow = valueType === 'section' ? '≤X≤' : '';
    const _dataValue = `${_leftChar},${_rightChar}`;
    numberDS.get(index).set(`valueType`, valueType);
    numberDS.get(index).set(`leftValue`, '');
    numberDS.get(index).set(`rightValue`, '');
    numberDS.get(index).set(`leftChar`, _leftChar);
    numberDS.get(index).set(`rightChar`, _rightChar);
    numberDS.get(index).set(`valueShow`, _valueShow);
    numberDS.get(index).set(`dataValue`, _dataValue);
    numberDS.get(index).set(`standard`, '');
    const current = {
      start: '',
      end: '',
    };
    numberDS.get(index).set(`multiValued`, valueType === 'section' ? current : '');
    numberList[index].valueType = valueType;
    numberList[index].leftValue = '';
    numberList[index].rightValue = '';
    numberList[index].leftChar = _leftChar;
    numberList[index].rightChar = _rightChar;
    numberList[index].valueShow = _valueShow;
    numberList[index].multiValued = valueType === 'section' ? current : '';
    numberList[index].standard = '';
    setNumberList([...numberList]);
  };

  // 数值-包含符号切换
  const handPrefix = (val, index, type) => {
    const _leftValue = numberDS.get(index).get(`multiValued`).start;
    const _rightValue = numberDS.get(index).get(`multiValued`).end;
    let _leftCharTip = '';
    let _rightCharTip = '';
    if (type === 'prefix') {
      _leftCharTip = val === '(' ? '≤' : '<';
      _rightCharTip = numberDS.get(index).get(`rightChar`) === ')' ? '<' : '≤';
      const _leftChar = val === '(' ? '[' : '(';
      numberList[index].leftChar = _leftChar;
      numberDS.get(index).set(`leftChar`, _leftChar);
    } else {
      _leftCharTip = numberDS.get(index).get(`leftChar`) === '(' ? '<' : '≤';
      _rightCharTip = val === ')' ? '≤' : '<';
      const _rightChar = val === ')' ? ']' : ')';
      numberList[index].rightChar = _rightChar;
      numberDS.get(index).set(`rightChar`, _rightChar);
    }
    const _leftValueShow = isNumber(_leftValue) ? _leftValue.toString() : _leftValue || '';
    const _rightValueShow = isNumber(_rightValue) ? _rightValue.toString() : _rightValue || '';
    const _valueShow = _leftValueShow.concat(_leftCharTip, 'X', _rightCharTip, _rightValueShow);
    numberList[index].valueShow = _valueShow;
    setNumberList([...numberList]);
    numberDS.get(index).set(`valueShow`, _valueShow);
    const _dataValue = `${numberDS
      .get(index)
      .get(`leftChar`)}${_leftValueShow},${_rightValueShow}${numberDS.get(index).get(`rightChar`)}`;
    numberDS.get(index).set(`dataValue`, _dataValue);
  };

  // 按钮
  const handleTag = key => {
    if (canEdit && key >= 0) {
      return (
        <div className={styles['icon-right-box']}>
          <Button
            onClick={() => handleNumberDelete(key)}
            icon="remove"
            size={Size.small}
            funcType={FuncType.flat}
          />
          <Button
            onClick={handleNumberAdd}
            icon="add"
            size={Size.small}
            funcType={FuncType.flat}
            style={{ visibility: key === 0 ? 'visible' : 'hidden' }}
          />
        </div>
      );
    }
    if (canEdit && key === -1) {
      return (
        <>
          <div className={styles['icon-right-box']} style={{ width: 26 }}>
            <Button
              onClick={handleNumberAdd}
              icon="add"
              size={Size.small}
              funcType={FuncType.flat}
            />
          </div>
          <span
            className={styles['icon-add-text']}
            style={{ width: '100%' }}
            onClick={handleNumberAdd}
          >
            {intl.get(`${modelPrompt}.add`).d('添加')}
          </span>
        </>
      );
    }
    return <div className={styles['hcm-dataItem-group-add-null']} />;
  };

  const handleNumber = (value, oldValue, index) => {
    if (value !== oldValue) {
      const _valueType = numberDS.get(index)?.get('valueType');
      if (_valueType === 'section') {
        const { start = '', end = '' } = value || {};
        const _leftChar = numberDS.get(index)?.get(`leftChar`);
        const _rightChar = numberDS.get(index)?.get(`rightChar`);
        const _leftCharTip = _leftChar === '(' ? '<' : '≤';
        const _rightCharTip = _rightChar === ')' ? '<' : '≤';
        const _leftValueShow = isNumber(start) ? start.toString() : start || '';
        const _rightValueShow = isNumber(end) ? end.toString() : end || '';
        const _valueShow = _leftValueShow.concat(_leftCharTip, 'X', _rightCharTip, _rightValueShow);
        numberList[index].valueShow = _valueShow;
        setNumberList([...numberList]);
        numberDS.get(index)?.set(`valueShow`, _valueShow);
        const _dataValue = `${_leftChar}${_leftValueShow},${_rightValueShow}${_rightChar}`;
        numberDS.get(index).set(`dataValue`, _dataValue);
        numberDS.get(index).set(`leftValue`, _leftValueShow);
        numberDS.get(index).set(`rightValue`, _rightValueShow);
      } else {
        numberDS.get(index).set(`dataValue`, value);
      }
    }
  };

  const handleStandard = (value, oldValue, index) => {
    if (value !== oldValue) {
      numberList[index].standard = value;
      setNumberList([...numberList]);
    }
  };

  return (
    <div className={styles['input-group-maintenance-value']}>
      {numberList.map((val, index) => {
        const { valueType, leftChar, rightChar, valueShow } = val;
        return (
          <>
            <div
              className={styles['input-number']}
              style={{
                marginTop: index > 0 && numberList[index - 1]?.valueType !== 'section' ? 12 : 0,
              }}
            >
              {canEdit && (
                <div style={{ width: 15, flexShrink: 0, textAlign: 'right', marginRight: 10 }}>
                  {valueType === 'section' && (
                    <span
                      className={styles['copy-radio']}
                      onClick={() => {
                        setValueCategory('single', index);
                      }}
                    >
                      <Tooltip
                        placement="bottom"
                        title={intl.get(`${modelPrompt}.switch.single`).d('点击切换单值')}
                      >
                        (X)
                      </Tooltip>
                    </span>
                  )}
                  {valueType === 'single' && (
                    <span
                      className={styles['copy-radio']}
                      onClick={() => {
                        setValueCategory('section', index);
                      }}
                    >
                      <Tooltip
                        placement="bottom"
                        title={intl.get(`${modelPrompt}.switch.range`).d('点击切换范围值')}
                      >
                        X
                      </Tooltip>
                    </span>
                  )}
                </div>
              )}
              {valueType === 'section' ? (
                <div className={styles['input-number-inner']}>
                  <NumberField
                    name="multiValued"
                    record={numberDS.get(index)}
                    disabled={!canEdit}
                    prefix={
                      canEdit ? (
                        <Tooltip
                          placement="bottom"
                          title={
                            leftChar === '('
                              ? intl.get(`${modelPrompt}.contain`).d('点击切换为包含')
                              : intl.get(`${modelPrompt}.not.contain`).d('点击切换为不包含')
                          }
                        >
                          <div
                            className={canEdit ? 'icon IconFront' : 'icon IconDisFront'}
                            onClick={() => handPrefix(leftChar, index, 'prefix')}
                          >
                            {leftChar}
                          </div>
                        </Tooltip>
                      ) : (
                        <div className={canEdit ? 'icon IconFront' : 'icon IconDisFront'}>
                          {leftChar}
                        </div>
                      )
                    }
                    suffix={
                      canEdit ? (
                        <Tooltip
                          placement="bottom"
                          title={
                            rightChar === ')'
                              ? intl.get(`${modelPrompt}.contain`).d('点击切换为包含')
                              : intl.get(`${modelPrompt}.not.contain`).d('点击切换为不包含')
                          }
                        >
                          <div
                            className={canEdit ? 'icon IconFront' : 'icon IconDisFront'}
                            onClick={() => handPrefix(rightChar, index, 'suffix')}
                          >
                            {rightChar}
                          </div>
                        </Tooltip>
                      ) : (
                        <div className={canEdit ? 'icon IconFront' : 'icon IconDisFront'}>
                          {rightChar}
                        </div>
                      )
                    }
                    onChange={(value, oldValue) => handleNumber(value, oldValue, index)}
                  />

                  {showStandard && (
                    <NumberField
                      placeholder={intl.get(`${modelPrompt}.standard`).d('标准值')}
                      name="standard"
                      record={numberDS.get(index)}
                      onChange={(value, oldValue) => handleStandard(value, oldValue, index)}
                    />
                  )}
                </div>
              ) : (
                <NumberField
                  name="singleValued"
                  record={numberDS.get(index)}
                  disabled={!canEdit}
                  onChange={(value, oldValue) => handleNumber(value, oldValue, index)}
                />
              )}
              {canEdit && handleTag(index)}
              {clearFlag && (
                <CheckBox
                  disabled={disabledFlag}
                  name={`${name}_select`}
                  style={{ marginLeft: 5, visibility: index === 0 ? 'visible' : 'hidden' }}
                />
              )}
            </div>
            <div style={{ textAlign: 'left' }}>{valueType === 'section' ? valueShow : ''}</div>
          </>
        );
      })}
      {numberList && numberList.length === 0 && canEdit && (
        <div className={styles['input-text']}>
          <div style={{ width: 15, flexShrink: 0 }} />
          {handleTag(-1)}
          {clearFlag && (
            <CheckBox disabled={disabledFlag} name={`${name}_select`} style={{ marginLeft: 5 }} />
          )}
        </div>
      )}
      {numberList && numberList.length === 0 && !canEdit && (
        <div className={styles['input-text']}>
          <TextField disabled />
          {clearFlag && (
            <CheckBox disabled={disabledFlag} name={`${name}_select`} style={{ marginLeft: 10 }} />
          )}
        </div>
      )}
    </div>
  );
};

export default InputValueComponent;
