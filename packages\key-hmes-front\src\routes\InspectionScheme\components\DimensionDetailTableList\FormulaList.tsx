/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-03-29 14:01:15
 * @LastEditTime: 2023-05-24 17:07:28
 * @LastEditors: <<EMAIL>>
 */
/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-02-02 15:18:39
 * @LastEditTime: 2023-03-29 14:00:46
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useState } from 'react';
import uuid from 'uuid/v4';
import intl from 'utils/intl';
import { DataSet, Table, Modal } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { drawerPropsC7n } from '@components/tarzan-ui';
import notification from 'utils/notification';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { getCurrentOrganizationId } from 'utils/utils';
import InspectItemInfoFormulaDrawer from './InspectItemInfoFormulaDrawer';
import { NumberDS, formulaListTableDS, DetailTableDS } from '../stores';

const modelPrompt = 'tarzan.qms.inspectGroupMaintenance';
const tenantId = getCurrentOrganizationId();

const _ignoreKeys = [
  'creationDate',
  'createdBy',
  'lastUpdateDate',
  'lastUpdatedBy',
  'objectVersionNumber',
  '_token',
  'inspectGroupItemId',
  'sequence',
];

const FormulaList = props => {
  const { path, tableDS, formDs, canEdit, statisticsTableUpdate } = props;

  const [inspectItemId] = useState((formDs.toData()[0] || {}).inspectItemId);

  // 数值类型-预警值DS
  const warnNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  // 数值类型-符合值DS
  const trueNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  // 数值类型-不符合值DS
  const falseNumberDS = useMemo(() => new DataSet({ ...NumberDS() }), []);
  //  单独编辑表单
  const editTableFormDS = useMemo(() => new DataSet(DetailTableDS({})), []);
  // 公式参数列表
  const formulaListTableDs = useMemo(() => new DataSet(formulaListTableDS()), []);

  const deleteFormula = record => {
    if (record?.get('dataType') === 'CALCULATE_FORMULA') {
      tableDS.delete(record, false);
      setTimeout(() => {
        tableSort();
      }, 10);
    }
  };

  const tableSort = () => {
    const recordData = formDs.toData()[0];

    let maxSequenceAfter = 1;
    const _sequence = recordData.sequence;
    tableDS.forEach(record => {
      if (recordData.inspectItemId === record?.get('formulaSourceId')) {
        // @ts-ignore
        record.set('sequence', _sequence + maxSequenceAfter);
        maxSequenceAfter += 1;
      }
    });
    // @ts-ignore
    return _sequence + maxSequenceAfter;
  };
  const formulaSum = () => {
    const recordData = formDs.toData()[0];
    const formulaSourceId = recordData.inspectItemId;
    let sum = 0;
    tableDS.forEach(record => {
      if (record?.get('formulaSourceId') === formulaSourceId) {
        sum++;
      }
    });
    return sum;
  };

  const addFormula = () => {
    editTableFormDS?.getField('formulaMode')?.set('required', true);
    editTableFormDS?.getField('formulaDisplayPosition')?.set('required', true);
    editTableFormDS?.getField('samplingMethodLov')?.set('required', true);

    formulaListTableDs.loadData([]);
    const recordData = formDs.toData()[0];

    const maxSequenceAfter = tableSort();

    recordData.sequence = maxSequenceAfter;
    recordData.operationType = 'SCHEME_FUNC_CREATE';

    recordData.formulaSourceId = recordData.inspectItemId;
    recordData.formulaId = null;
    recordData.formulaCode = null;
    recordData.formulaName = null;
    recordData.dimension = null;
    recordData.formulaList = null;

    recordData.inspectionItemRowUuid = uuid();
    recordData.dataType = 'CALCULATE_FORMULA';
    recordData.formulaMode = 'CURRENT_ITEM';
    recordData.formulaDisplayPosition = 'CURRENT_ITEM_AFTER';
    recordData.enterMethod = 'AUTOMATIC_COLLECTION';
    recordData.outsourceFlag = 'N';
    recordData.destructiveExperimentFlag = 'N';

    recordData.inspectObjectDmsItemId = null;
    recordData.inspectItemId = null;
    recordData.inspectItemCode = null;
    recordData.inspectItemDesc = null;
    recordData.inspectItemLov = {};
    recordData.samplingMethodLov = {};
    recordData.samplingMethodId = null;
    recordData.samplingMethodDesc = null;
    recordData.defaultValue = null;
    recordData.dataQtyDisposition = null;

    editTableFormDS.loadData([{ ...recordData, isNewRow: true }]);

    const inspectItemDrawerProps = {
      canEdit,
      _ignoreKeys,
      tenantId,
      warnNumberDS,
      trueNumberDS,
      falseNumberDS,
      editTableFormDS,
      formDs,
      tableDS,
      formulaListTableDs,
    };

    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      destroyOnClose: true,
      key: Modal.key(),
      title: intl?.get(`${modelPrompt}.title.itemFormulaAdd`).d('新建检验项目计算公式'),
      drawer: true,
      style: {
        width: '720px',
      },
      className: 'hmes-style-modal',
      children: <InspectItemInfoFormulaDrawer {...inspectItemDrawerProps} />,
      onOk: () => {
        return handleInspectItemDrawerSubmit();
      },
      afterClose: () => {
        resetEditFormDS();
      },
      footer: (okBtn, cancelBtn) => {
        return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
      },
    });
  };

  const editFormula = record => {
    editTableFormDS?.getField('formulaMode')?.set('required', true);
    editTableFormDS?.getField('formulaDisplayPosition')?.set('required', true);
    editTableFormDS?.getField('dataQty')?.set('required', false);
    const recordData = record.toData();
    editTableFormDS.loadData([{ ...recordData, isNewRow: false }]);

    const inspectItemDrawerProps = {
      canEdit,
      _ignoreKeys,
      tenantId,
      warnNumberDS,
      trueNumberDS,
      falseNumberDS,
      editTableFormDS,
      formDs,
      tableDS,
      formulaListTableDs,
    };

    Modal.open({
      ...drawerPropsC7n({
        canEdit,
      }),
      destroyOnClose: true,
      key: Modal.key(),
      title: intl?.get(`${modelPrompt}.title.itemFormulaEdit`).d('编辑检验项目计算公式'),
      drawer: true,
      style: {
        width: '720px',
      },
      className: 'hmes-style-modal',
      children: <InspectItemInfoFormulaDrawer {...inspectItemDrawerProps} />,
      onOk: () => {
        return handleInspectItemDrawerSubmit();
      },
      footer: (okBtn, cancelBtn) => {
        return canEdit ? [cancelBtn, okBtn] : [cancelBtn];
      },
    });
  };

  const resetEditFormDS = () => {
    editTableFormDS.loadData([{}]);
  };

  // 确定保存检验项目
  const handleInspectItemDrawerSubmit = async () => {
    // 检验项目字段校验
    let validateResult;
    const record = editTableFormDS.current;

    if (!record) {
      return false;
    }

    let validValueTypeFlag = true;
    // 数值类型的符合值、不符合值必输校验
    const dataType = record?.get('dataType');

    validateResult = await formulaListTableDs.validate();
    if (!validateResult) {
      return false;
    }
    validateResult = await editTableFormDS.validate();
    if (!validateResult) {
      return false;
    }

    if (['CALCULATE_FORMULA', 'VALUE'].includes(dataType)) {
      if (trueNumberDS.length < 1 && falseNumberDS.length < 1) {
        notification.error({
          message: intl
            ?.get(`${modelPrompt}.message.inputTrueOrFalseValue`)
            .d('请输入符合值或不符合值'),
        });
        validValueTypeFlag = false;
      } else {
        const _targeDS = trueNumberDS.length > 0 ? trueNumberDS : falseNumberDS;
        const _newData = _targeDS.records.filter(
          item =>
            item?.get('singleValued') ||
            (item?.get('multiValued') &&
              (item?.get('multiValued')?.start || item?.get('multiValued')?.end)),
        );
        if (_newData.length < 1) {
          notification.error({
            message:
              trueNumberDS.length > 0
                ? intl?.get(`${modelPrompt}.message.inputTrueValue`).d('请输入符合值')
                : intl?.get(`${modelPrompt}.message.inputFalseValue`).d('请输入不符合值'),
          });
          validValueTypeFlag = false;
        }
      }
    }
    if (!validValueTypeFlag) {
      return false;
    }

    const { sequence, inspectItemId, inspectGroupItemId } = record.toData();
    const sameSerialNumberFlag = tableDS
      .toData()
      .some(item => item.sequence === sequence && item.inspectGroupItemId !== inspectGroupItemId);
    const sameInspectItemFlag = tableDS
      .toData()
      .some(
        item =>
          item.inspectItemId === inspectItemId && item.inspectGroupItemId !== inspectGroupItemId,
      );
    if (sameSerialNumberFlag) {
      notification.error({
        message: intl?.get(`${modelPrompt}.message.sameSerialNumber`).d('当前序号已存在'),
      });
      return false;
    }
    if (sameInspectItemFlag) {
      notification.error({
        message: intl?.get(`${modelPrompt}.message.sameInspectItemFlag`).d('当前检验项目已选用'),
      });
      return false;
    }

    let _trueValueList: any = [];
    let _falseValueList: any = [];
    let _warningValueList: any = [];

    // 如果数据类型为数值类型，需要做赋值处理
    if (['CALCULATE_FORMULA', 'VALUE'].includes(dataType)) {
      const _valueHandle = targetDS => {
        return targetDS
          .toData()
          .filter(
            (item: any) =>
              item.singleValued ||
              (item.multiValued && (item.multiValued?.start || item.multiValued?.end)),
          )
          .map((item: any) => {
            if (
              item.valueType !== 'single' &&
              (!item.multiValued?.start || !item.multiValued?.end)
            ) {
              if (!item.multiValued?.start) {
                item.multiValued.start = '-∞';
                item.leftValue = '-∞';
              }
              if (!item.multiValued?.end) {
                item.multiValued.end = '+∞';
                item.rightValue = '+∞';
              }
              item.dataValue = `${item.leftChar}${item.multiValued.start},${item.multiValued.end}${item.rightChar}`;
              const _leftCharTip = item.leftChar === '(' ? '<' : '≤';
              const _rightCharTip = item.rightChar === ')' ? '<' : '≤';
              item.valueShow = `${item.multiValued.start}${_leftCharTip}X${_rightCharTip}${item.multiValued.end}`;
            }
            return item;
          });
      };

      _trueValueList = _valueHandle(trueNumberDS);
      _falseValueList = _valueHandle(falseNumberDS);
      _warningValueList = _valueHandle(warnNumberDS);
    } else if (dataType === 'VALUE_LIST') {
      _trueValueList = (record?.get('trueValue') || []).map(item => ({
        dataValue: item,
      }));
      _falseValueList = (record?.get('falseValue') || []).map(item => ({
        dataValue: item,
      }));
    } else {
      _trueValueList = record?.get('trueValue')
        ? [
          {
            dataValue: record?.get('trueValue'),
          },
        ]
        : [];
      _falseValueList = record?.get('falseValue')
        ? [
          {
            dataValue: record?.get('falseValue'),
          },
        ]
        : [];
    }

    if (dataType === 'CALCULATE_FORMULA') {
      record.set(
        'formula',
        JSON.stringify({
          formulaSourceId: record?.get('formulaSourceId'),
          formulaMode: record?.get('formulaMode'),
          formulaDisplayPosition: record?.get('formulaDisplayPosition'),
          formulaId: record?.get('formulaId'),
          formulaCode: record?.get('formulaCode'),
          formulaName: record?.get('formulaName'),
          dimension: record?.get('dimension'),
          formulaList: (formulaListTableDs.toData() || []).map((formulaListItem: any) => {
            if (formulaListItem) {
              return {
                fieldCode: formulaListItem.fieldCode,
                fieldName: formulaListItem.fieldName,
                inspectItemId: formulaListItem.inspectItemId,
                inspectItemDesc: formulaListItem.inspectItemDesc,
                isRequired: formulaListItem.isRequired,
              };
            }
            return {};
          }),
        }),
      );
      record.set('formulaList', formulaListTableDs.toData());
    } else {
      record.set('formula', '');
      record.set('formulaList', []);
    }

    record.set('trueValueList', _trueValueList);
    record.set('falseValueList', _falseValueList);
    record.set('warningValueList', _warningValueList);

    const recordData = record.toData();
    if (recordData.isNewRow) {
      tableDS.create({
        ...recordData,
        isNewRow: false,
      });
    } else {
      tableDS.forEach(focusRecord => {
        if (focusRecord?.get('inspectionItemRowUuid') === recordData.inspectionItemRowUuid) {
          Object.keys(recordData).forEach(recordDataKey => {
            focusRecord.set(recordDataKey, recordData[recordDataKey]);
          });
        }
      });
    }

    statisticsTableUpdate();
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit || formulaSum() > 8}
          onClick={addFormula}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl?.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteFormula(record)}
          okText={intl?.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl?.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      width: 80,
      align: ColumnAlign.center,
      lock: ColumnLock.left,
    },
    {
      name: 'formulaLov',
      width: 200,
      renderer: ({ record }) => (
        <a onClick={() => editFormula(record)}>{record?.get('formulaCode')}</a>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'formulaName',
      width: 200,
    },
    {
      title: `${intl?.get(`${modelPrompt}.params`).d('参数')}`,
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ record }) => {
        return (record?.get('formulaList') || [])
          .filter(item => item.fieldCode !== 'decimalNumber')
          .map(item => <p style={{ margin: '0.5em 0' }}>{`${item.fieldName || ''}`}</p>);
      },
    },
    {
      title: `${intl?.get(`${modelPrompt}.aboutitem`).d('关联检验项目')}`,
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ record }) => {
        return (record?.get('formulaList') || [])
          .filter(item => item.fieldCode !== 'decimalNumber')
          .map(item => <p style={{ margin: '0.5em 0' }}>{`${item.inspectItemDesc || ''}`}</p>);
      },
    },
  ];

  return (
    <Table
      filter={record => {
        return (
          record?.get('dataType') === 'CALCULATE_FORMULA' &&
          record?.get('formulaSourceId') === inspectItemId
        );
      }}
      dataSet={tableDS}
      columns={columns}
      rowHeight="auto"
      highLightRow
    />
  );
};

export default FormulaList;
