/**
 * @Description: 容器类型维护（重构）-详情页面DS
 * @Author: <EMAIL>
 * @Date: 2022/7/11 17:45
 * @LastEditTime: 2022/7/11 17:45
 * @LastEditors: <EMAIL>
 */

import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { packingLevelOptionDs } from './ListTable';

const modelPrompt = 'tarzan.hagd.containerType.model.containerType';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'containerTypeId', // 主键
      type: FieldType.number,
    },
    // 基础属性 start
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('容器类型编码'),
      required: true,
    },
    {
      name: 'containerTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeDescription`).d('容器类型描述'),
      required: true,
    },
    {
      name: 'packingLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packingLevel`).d('包装等级'),
      options: packingLevelOptionDs,
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'locationEnabledFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.locationEnabledFlag`).d('位置状态'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'locationRow',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locationRow`).d('容器行'),
      min: 1,
      precision: 0,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('locationEnabledFlag') !== 'Y';
        },
        required: ({ record }) => {
          return record.get('locationEnabledFlag') === 'Y';
        },
      },
    },
    {
      name: 'locationColumn',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locationColumn`).d('容器列'),
      min: 1,
      precision: 0,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('locationEnabledFlag') !== 'Y';
        },
        required: ({ record }) => {
          return record.get('locationEnabledFlag') === 'Y';
        },
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'forbidFlagValues',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.forbidFlagValues`).d('禁止混合装载对象'),
      textField: 'text',
      valueField: 'type',
      options: new DataSet({
        data: [
          {
            type: 'mixedMaterialFlag',
            text: intl.get(`${modelPrompt}.material`).d('物料'),
          },
          {
            type: 'mixedEoFlag',
            text: intl.get(`${modelPrompt}.eo`).d('EO'),
          },
          {
            type: 'mixedWoFlag',
            text: intl.get(`${modelPrompt}.wo`).d('WO'),
          },
          {
            type: 'mixedOwnerFlag',
            text: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
          },
        ],
        selection: DataSetSelection.multiple,
        autoQuery: true,
      }),
      defaultValue: ['mixedMaterialFlag', 'mixedOwnerFlag'],
    },
    {
      name: 'containerClassification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerClassification`).d('容器类型'),
      lookupCode: 'APEX_WMS.CONTAINER_CLASSIFICATION',
      lovPara: { tenantId },
      textField: 'meaning',
      valueField: 'value',
    },
    // 基础属性 end
    // 规格尺寸 start
    {
      name: 'sizeUomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sizeUomId`).d('长度单位'),
      lovCode: 'APEX_WMS.COMMON.UOM',
      lovPara: {
        tenantId,
        uomType: 'LENGTH',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'sizeUomId',
      bind: 'sizeUomLov.uomId',
    },
    {
      name: 'sizeUomCode',
      bind: 'sizeUomLov.uomCode',
    },
    {
      name: 'sizeDecimalNumber',
      bind: 'sizeUomLov.decimalNumber',
    },
    {
      name: 'sizeProcessMode',
      bind: 'sizeUomLov.processMode',
    },
    {
      name: 'length',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.length`).d('容器长'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('sizeUomId');
        },
        // precision: ({ record }) => {
        //   return record?.get('sizeDecimalNumber');
        // },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('sizeDecimalNumber')).toFixed(record?.get('sizeDecimalNumber')),
          );
        },
      },
    },
    {
      name: 'width',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.width`).d('容器宽'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('sizeUomId');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('sizeDecimalNumber')).toFixed(record?.get('sizeDecimalNumber')),
          );
        },
      },
    },
    {
      name: 'height',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.height`).d('容器高'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('sizeUomId');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('sizeDecimalNumber')).toFixed(record?.get('sizeDecimalNumber')),
          );
        },
      },
    },
    {
      name: 'weightUomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.weightUomId`).d('重量单位'),
      lovCode: 'APEX_WMS.COMMON.UOM',
      lovPara: {
        tenantId,
        uomType: 'WEIGHT',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'weightUomId',
      bind: 'weightUomLov.uomId',
    },
    {
      name: 'weightUomCode',
      bind: 'weightUomLov.uomCode',
    },
    {
      name: 'weightDecimalNumber',
      bind: 'weightUomLov.decimalNumber',
    },
    {
      name: 'weightProcessMode',
      bind: 'weightUomLov.processMode',
    },
    {
      name: 'weight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.weight`).d('容器重量'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('weightUomId');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('weightDecimalNumber')).toFixed(record?.get('weightDecimalNumber')),
          );
        },
      },
    },
    {
      name: 'maxLoadWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxLoadWeight`).d('最大承重'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('weightUomId');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('weightDecimalNumber')).toFixed(record?.get('weightDecimalNumber')),
          );
        },
      },
    },
    {
      name: 'capacityQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.capacityQty`).d('最大装载数量'),
      min: 0,
      dynamicProps: {
        precision: ({ record }) => {
          return record?.get('weightDecimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('weightDecimalNumber')).toFixed(record?.get('weightDecimalNumber')),
          );
        },
      },
    },
    // 规格尺寸 end
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HWMS_BASIC}/v1/${tenantId}/mt-container-type/detail/ui`,
        method: 'GET',
      };
    },
  },
  events: {
    update: ({record, name}) => {
      if (name === 'sizeUomLov') {
        record.set('length', null);
        record.set('width', null);
        record.set('height', null);
      }else if(name === 'weightUomLov') {
        record.set('weight', null);
        record.set('maxLoadWeight', null);
        record.set('capacityQty', null);
      }
    },
  },
});

export { detailDS };
