{"name": "key-halm-front", "version": "0.0.1", "homepage": "", "license": "ISC", "directories": {"lib": "lib"}, "dependencies": {"ahooks": "2.10.0", "rc-bmap": "^1.0.5", "react": "^16.8.3", "react-amap": "^1.1.1", "react-color": "^2.17.3", "react-dom": "^16.8.3", "react-zmage": "0.8.5", "bizcharts": "3.5.6", "echarts": "^4.9.0", "echarts-for-react": "^2.0.14", "file-saver": "^2.0.2"}, "peerDependencies": {"choerodon-ui": "*"}, "files": ["lib", "config/config.ts", "!.umi", "!lib/config/.env.*", "!lib/config/alias.js", "!lib/config/theme.js", "!.umi-production"], "scripts": {"start": "cross-env UMI_ENV=dev umi dev", "start:test": "cross-env UMI_ENV=test umi dev", "transpile": "umi hzero-transpile", "build": "umi hzero-build", "build:ms": "cross-env UMI_ENV=dev umi hzero-build --only-build-micro", "build:ms-all": "umi hzero-build --only-build-micro --all-packages", "hzero-build:dep-all": "umi hzero-build-dep --all", "build:app": "cross-env BUILD_SINGLE_PUBLIC_MS=true umi hzero-build --only-build-parent", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "start:additional": "cross-env ADDITIONAL=true umi dev", "build:additional": "cross-env ADDITIONAL=true umi hzero-build", "build:additional-dep": "cross-env ADDITIONAL_NAME=halm-front micro-prepare dep", "build:dep": "micro-prepare dep", "build:hzero": "micro-prepare hzero", "test:coverage": "umi-test --coverage"}, "husky": {"hooks": {"pre-commit": "npm run lint-staged", "post-merge": "git submodule update", "post-checkout": "git submodule update"}}, "browserslist": [">0.2%", "not dead", "not ie <= 10", "not op_mini all"], "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "resolver": "jest-pnp-resolver", "setupFiles": ["react-app-polyfill/jsdom"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/?(*.)(spec|test).{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "testURL": "http://localhost", "transform": {"^.+\\.(js|jsx|ts|tsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["<rootDir>/node_modules/jest-watch-typeahead/filename.js", "<rootDir>/node_modules/jest-watch-typeahead/testname.js"]}, "lint-staged": {"src/**/*.{js,jsx,tsx,ts}": "eslint --quiet --fix", "src/**/*.less": "stylelint --syntax less", "src/**/*.{js,ts,tsx,jsx,less}": ["prettier --write", "git add"]}}