/**
 * @Description: 库存调拨平台-services
 * @Author: <EMAIL>
 * @Date: 2022/7/20 11:33
 * @LastEditTime: 2023-05-18 16:22:24
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const tenantId = getCurrentOrganizationId();

// 库存调拨平台-状态变更
export function HandleChangeStatus() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-send-receive/status/alter/for/ui`,
    method: 'POST',
  };
}

// 库存调拨平台-行取消
export function HandleDeleteLine() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-send-receive/line/cancel/for/ui`,
    method: 'POST',
  };
}

// 库存调拨平台-保存
export function HandleSaveInstruction() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-send-receive/save/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.LINE`,
    method: 'POST',
  };
}

// 库存调拨平台-查询允差信息
export function GetToleranceInfo() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-send-receive/stock-instruction-tolerance/get/for/ui`,
    method: 'GET',
  };
}
