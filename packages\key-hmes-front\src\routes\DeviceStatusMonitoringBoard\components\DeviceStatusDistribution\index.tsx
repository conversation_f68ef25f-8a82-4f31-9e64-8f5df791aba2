import React, { useEffect, useRef, useMemo } from 'react';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import DashboardCard from '../DashboardCard.jsx';
import styles from '../../index.module.less';

const DeviceStatusDistribution = ({isFullScreen, deviceStatusDistributionInfo }) => {
  const chartRef = useRef(null);

  const data1 = [
    { name: '运行', value: deviceStatusDistributionInfo?.operatingRate||0 },
    { name: '故障', value: deviceStatusDistributionInfo?.faultRate||0 },
    { name: '待机', value: deviceStatusDistributionInfo?.pauseRate||0 },
    { name: '停机', value: deviceStatusDistributionInfo?.shutdownRate||0 },
    { name: '换型', value: deviceStatusDistributionInfo?.changeoverRate||0 },
  ];
  const option: any = useMemo(() => {
    return {
      title:{
        text: '设备状态',
        top: 'middle',
        left: 'center',
        textStyle: {
          fontSize: 14,
          fontWeight: 400,
          color: '#ffffff',
        },
      },
      color:['#adde8b','#f9dd74','#92d1f0','#ee7975','#627ad3'],
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.9)',
      },
      legend: {
        show: true,
        bottom: 'bottom',
        textStyle: {
          color: '#fff',
        },
      },
      series: {
        type: 'pie',
        radius: ['60%', '85%'],
        center: ['50%', '42%'],
        width: "100%",
        height: "80%",
        left: 'center',
        top: 'middle',
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(255,255,255,0)',
          borderWidth: 2,
        },
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data:data1,
      },
    };
  }, [deviceStatusDistributionInfo]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  return (
    <DashboardCard style={{ height: '100%' }}>
      {isFullScreen?
        (<div style={{ width: '100%', height: '100%' }} className={styles['dashboard-right-chart-full-screen']}>
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>):
        <div style={{ width: '100%', height: '100%' }} className={styles['dashboard-right-chart']}>
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>
      }
    </DashboardCard>
  );
};
export default DeviceStatusDistribution;
