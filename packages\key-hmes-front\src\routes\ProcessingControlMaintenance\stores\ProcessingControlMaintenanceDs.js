import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.processingControlMaintenance';

// const Host = `/yp-mes-20000`;
// lov配置信息
// const objectLovCode = {
//   MATERIAL: 'HME.ASSEMBL_EPOINT_SITE_MATERIAL', // 物料
//   MATERIAL_CATEGORY: 'MT.MATERIAL_CATEGORY_SITES', // 物料类别
//   OPERATION: 'MT.APS.OPERATION', // 工艺
//   WORKCELL: 'MT.MODEL.WORKCELL', // 工作单元
//   EQUIPMENT: 'MT.MODEL.EQUIPMENT', // 设备
//   EQUIPMENT_CATEGORY: 'MT.MODEL.PRODLINE', // 设备类别
// };

const objectIdBind = {
  MATERIAL: 'objectLov.materialId', // 物料
  MATERIAL_CATEGORY: 'objectLov.materialCategoryId', // 物料类别
  OPERATION: 'objectLov.operationId', // 工艺
  WORKCELL: 'objectLov.workcellId', // 工作单元
  EQUIPMENT: 'objectLov.equipmentId', // 设备
  EQUIPMENT_CATEGORY: 'objectLov.equipmentCategory', // 设备类别
};

// const objectCodeBind = {
//   MATERIAL: 'objectLov.materialCode',
//   MATERIAL_CATEGORY: 'objectLov.categoryCode',
//   OPERATION: 'objectLov.operationName',
//   WORKCELL: 'objectLov.workcellCode',
//   EQUIPMENT: 'objectLov.equipmentCode',
//   EQUIPMENT_CATEGORY: 'objectLov.equipmentCategory',
// };

// const objectDescBind = {
//   MATERIAL: 'objectLov.materialName',
//   MATERIAL_CATEGORY: 'objectLov.description',
//   OPERATION: 'objectLov.operationDesc',
//   WORKCELL: 'objectLov.workcellName',
//   EQUIPMENT: 'objectLov.equipmentName',
//   EQUIPMENT_CATEGORY: 'objectLov.equipmentCategory',
// };

const tableDS = xFormDS => {
  return {
    name: 'tableDS',
    primaryKey: 'processCtrlConfigId',
    paging: true,
    autoQuery: false,
    selection: 'single',
    fields: [
      {
        name: 'prodLineObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineObj`).d('产线'),
        lovCode: 'MT.MODEL.PRODLINE',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        textField: 'prodLineCode',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'prodLineCode',
        type: 'string',
        bind: 'prodLineObj.prodLineCode',
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线描述'),
        bind: 'prodLineObj.prodLineName',
      },
      {
        name: 'prodLineId',
        type: 'number',
        bind: 'prodLineObj.prodLineId',
      },
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料'),
        lovCode: 'HME.PERMISSION_MATERIAL',
        labelWidth: 150,
        textField: 'materialCode',
        dynamicProps: {
          lovPara: () => {
            const siteId = xFormDS.current?.data.siteId;
            return {
              tenantId: getCurrentOrganizationId(),
              siteId,
            };
          },
        },
      },
      {
        name: 'materialCode',
        type: 'string',
        bind: 'materialObj.materialCode',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'materialObj.materialName',
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'materialSiteId',
        type: 'string',
        bind: 'materialObj.materialSiteId',
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        bind: 'materialObj.revisionCode',
        textField: 'revisionCode',
        valueField: 'revisionCode',
        lookupUrl: `${Host}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
        lookupAxiosConfig: ({ record }) => {
          const _params = record?.toData() || {};
          const siteId = xFormDS.current?.data.siteId;
          if (_params && _params.materialSiteId) {
            return {
              params: {
                materialSiteId: _params.materialSiteId,
                siteId,
              },
              transformResponse(data) {
                // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
                if (data instanceof Array) {
                  return data;
                }
                if (data.failed) {
                  return [];
                }
                const rows = JSON.parse(data);
                return rows;
              },
            };
          }
        },
        dynamicProps: {
          required: record => {
            const _params = record?.record.data || {};
            if (_params && _params.revisionFlag && _params.revisionFlag === 'Y') {
              return true;
            }
            return false;
          },
        },
      },
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('工艺'),
        lovCode: 'MT.APS.OPERATION',
        labelWidth: 150,
        ignore: 'always',
        textField: 'operationName',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'operationName',
        type: 'string',
        bind: 'operationObj.operationName',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('工艺描述'),
        bind: 'operationObj.description',
      },
      {
        name: 'operationId',
        type: 'number',
        bind: 'operationObj.operationId',
      },
      {
        name: 'revision',
        type: 'string',
        bind: 'operationObj.revision',
        label: intl.get(`${modelPrompt}.revision`).d('工艺版本'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
    ],
    queryFields: [
      {
        name: 'prodLineCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineCode`).d('产线'),
        lovCode: 'MT.MODEL.PRODLINE',
        labelWidth: 150,
      },
      {
        name: 'prodLineId',
        type: 'number',
        bind: 'prodLineCode.prodLineId',
      },
      {
        name: 'materialCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
        lovCode: 'HME.PERMISSION_MATERIAL',
        labelWidth: 150,
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialCode.materialId',
      },
      {
        name: 'operationCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationCode`).d('工艺'),
        lovCode: 'MT.APS.OPERATION',
        labelWidth: 150,
      },
      {
        name: 'operationId',
        type: 'number',
        bind: 'operationCode.operationId',
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-process-ctrl-configs/list/ui`,
          method: 'GET',
        };
      },
    },
    // events: {
    //   update({ record, dataSet  }) {
    //     if (record.data && record.data.materialSiteId) {
    //       const materialSiteId = record.data.materialObj.materialSiteId
    //         ? record.data.materialObj.materialSiteId
    //         : record.data.materialSiteId;
    //       dataSet.addField('revisionCode', {
    //         name: 'revisionCode',
    //         label: '物料版本',
    //         textField: 'revisionCode',
    //         valueField: 'revisionCode',
    //         computedProps: {
    //           lookupAxiosConfig: ({ record }) =>
    //             record && {
    //               url: `${Host}/v1/${tenantId}/hme-assemble-points/get/material/revision?materialSiteId=${materialSiteId}&siteId=${xFormDS.current?.data.siteId}`,
    //               transformResponse(data) {
    //                 try {
    //                   const jsonData = JSON.parse(data);
    //                   if (data.failed) {
    //                     return [];
    //                   }
    //                   return jsonData;
    //                 } catch (e) {
    //                   return data;
    //                 }
    //               },
    //             },
    //         },
    //       });
    //     }
    //   },
    // },
  };
};

const assObjectsDS = () => {
  return {
    name: 'assObjectsDS',
    primaryKey: 'processCtrlConfigLId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('控制项编码'),
        lovCode: 'HME.CONTROL_ITEM_CODE',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        textField: 'controlItemCode',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'controlItemCode',
        type: 'string',
        bind: 'operationObj.controlItemCode',
      },
      {
        name: 'controlItemName',
        type: 'string',
        label: intl.get(`${modelPrompt}.controlItemName`).d('控制项描述'),
        bind: 'operationObj.controlItemName',
      },
      // {
      //   name: 'operationId',
      //   type: 'number',
      //   bind: 'operationObj.operationId',
      // },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        required: true,
        lookupCode: 'MT.ENABLE_FLAG',
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-process-ctrl-config-ls/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const singleAssObjectDS = () => {
  return {
    primaryKey: 'processCtrlConfigDtlId',
    autoCreate: true,
    selection: false,
    autoQuery: false,
    paging: false,
    fields: [
      {
        name: 'parentSiteIds',
        type: 'object',
      },
      {
        name: 'objectType',
        label: intl.get(`${modelPrompt}.objectType`).d('关联对象类型'),
        type: 'string',
        required: true,
        lookupCode: 'HME.CTRL_CONFIG_OBJECT',
      },
      {
        name: 'objectLov',
        label: intl.get(`${modelPrompt}.objectLov`).d('关联对象编码'),
        type: 'object',
        required: true,
        ignore: 'always',
        textField: 'operationName',
        dynamicProps: {
          lovCode: ({record}) => {
            if(record.get('objectType') === 'OPERATION'){
              return 'MT.APS.OPERATION';
            }
          },
          disabled: ({ record }) => {
            return !record.get('objectType');
          },

        },
      },
      {
        name: 'operationId',
        type: 'number',
        dynamicProps: {
          bind: ({ record }) => {
            const objectType = record.get('objectType');
            return objectIdBind[objectType];
          },
        },
      },
      {
        name: 'operationName',
        type: 'string',
        bind: 'objectLov.operationName',
        // dynamicProps: {
        //   bind: ({ record }) => {
        //     const objectType = record.get('objectType');
        //     return objectDescBind[objectType];
        //   },
        // },
      },
      {
        name: 'operationDesc',
        label: intl.get(`${modelPrompt}.operationDesc`).d('关联对象描述'),
        type: 'string',
        bind: 'objectLov.description',
        // dynamicProps: {
        //   bind: ({ record }) => {
        //     const objectType = record.get('objectType');
        //     return objectDescBind[objectType];
        //   },
        // },
      },
      {
        name: 'revisionFlag',
        type: 'string',
        bind: 'objectLov.revisionFlag',
      },
      {
        name: 'operationRevision',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationRevision`).d('关联对象版本'),
        bind: 'objectLov.revision',
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        required: true,
        lookupCode: 'MT.ENABLE_FLAG',
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
    ],
    events: {
      update: ({ record, name, value })  => {
        if(name === 'objectType'&& !value){
          record.set('objectLov', null);
          record.set('operationId', null);
          record.set('operationName', null);
          record.set('operationDesc', null);
          record.set('operationRevision', null);
          record.set('revisionFlag', null);
        }
      },
    },
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-process-ctrl-config-dtls/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const siteDS = () => {
  return {
    name: 'siteDS',
    paging: true,
    autoQuery: true,
    selection: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
      },
      {
        name: 'siteId',
        type: 'string',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-assemble-points/get/user/def/site`,
          method: 'GET',
        };
      },
    },
  };
};

export { tableDS, singleAssObjectDS, assObjectsDS, siteDS };
