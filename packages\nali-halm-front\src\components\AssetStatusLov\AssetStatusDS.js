/**
 * @since 2021/03/08
 * <AUTHOR> <zhiqiang.quan@hand-china>
 */
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const organizationId = getCurrentOrganizationId();

function dataDS() {
  return {
    autoQuery: false,
    selection: 'single',
    paging: false,
    primaryKey: 'assetStatusId',
    dataKey: 'content',
    fields: [
      {
        name: 'assetStatusId',
        type: 'string',
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          params,
          url: `${HALM_ATN}/v1/${organizationId}/asset-status/list`,
          method: 'GET',
        };
      },
    },
  };
}

export { dataDS };
