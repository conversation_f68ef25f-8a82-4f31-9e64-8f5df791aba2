/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-12-14
 * @description 执行执行规则维护-DS
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import uuid from 'uuid/v4';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.commonConfig.orderExecuteRuleMes';
const tenantId = getCurrentOrganizationId();
const tableDS = () => ({
  primaryKey: 'uuid',
  queryUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-instruction-exe-rules/instruction/exe/rules/query/ui`,
  autoQuery: false,
  selection: false,
  paging: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      step: 1,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      order: 'asc',
    },
    {
      name: 'instructionTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionType`).d('移动类型'),
    },
    {
      name: 'businessTypeDesc',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
    },
    {
      name: 'instructionExeStrategyDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionExeStrategy`).d('指令执行策略'),
    },
    {
      name: 'addStrategyFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.uniqueMaterialLotFlag`).d('寻址策略标识'),
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
    },
    {
      name: 'toleranceTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlagType`).d('允差类型'),
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差值'),
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差值'),
    },
    {
      name: 'onPassageLocatorDirection',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onPassageLocatorDirection`).d('在途库位方向'),
    },
    {
      name: 'onPassageLocatorType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onPassageLocatorType`).d('在途库位类型'),
    },
    {
      name: 'onPassageLocatorId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onPassageLocator`).d('在途库位'),
    },
    // {
    //   name: 'accountCategoryDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.accountCategory`).d('结算类别'),
    // },
    {
      name: 'transferFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.transferFlag`).d('生成事务'),
    },
    {
      name: 'transferFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.transferFlag`).d('生成事务'),
      defaultValue: 'N',
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.initialFlag`).d('初始化'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-instruction-exe-rules/instruction/exe/rules/query/ui`,
        method: 'get',
        transformResponse: data => {
          const { rows } = JSON.parse(data);
          if (rows?.content instanceof Array) {
            const content = rows.content.map(item => {
              return {
                uuid: uuid(),
                ...item,
              };
            });
            return {
              ...rows,
              content,
            };
          }
          return rows;
        },
      };
    },
  },
});

export { tableDS };
