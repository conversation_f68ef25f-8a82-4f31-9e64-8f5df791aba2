/**
 * @description: 工艺路线(制造/物料)-组件分配
 * @date 2022-8-24
 * <AUTHOR> <<EMAIL>>
 */
import React, { Component } from 'react';
import { cloneDeep } from 'lodash';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { Select, AutoComplete, Input, Icon, Radio, Checkbox } from 'hzero-ui';
import notification from 'utils/notification';
import rightIcon from '@/assets/icons/rightIcon.svg';
import rightIconChoose from '@/assets/icons/rightIconChoose.svg';
import myInstance from '@/utils/myAxios';
import styles from './ComponentDistributionDrawer.module.less';

const modelPrompt = 'tarzan.process.routes.model.routes';
const Host = BASIC.HMES_BASIC;
export default class ComponentDistributionDrawer extends Component {
  state = {
    currentOperation: '', // 当前选中工艺
    currentComponent: [], // 当前选中组件
    operationList: [], // 下拉框选中的当前要显示的哪部分数据
    initOperationList: [], // 初始工艺数据
    componentList: [], // 组件下拉框选中的当前要显示的哪部分数据
    operationStatus: 'all', // 下拉框当前是什么状态
    componentStatus: 'all', // 组件下拉框当前是什么状态
    indeterminate: false,
    checkAll: false,
  };

  componentDidMount() {
    this.initHandleData();
  }

  componentWillUnmount() {
    this.setState({
      currentOperation: '', // 当前选中工艺
      currentComponent: [], // 当前选中组件
      operationList: [], // 下拉框选中的当前要显示的哪部分数据
      initOperationList: [], // 初始工艺数据
      componentList: [], // 组件下拉框选中的当前要显示的哪部分数据
      operationStatus: 'all', // 下拉框当前是什么状态
      componentStatus: 'all', // 组件下拉框当前是什么状态
      indeterminate: false,
      checkAll: false,
    });
  }

  initHandleData = async (data, lists) => {
    const {
      routerId,
      compList,
      // serveCode,
      tenantId,
      stepsList,
      currentBomId,
    } = this.props;
    const that = this;
    if ((data || compList).length > 0) {
      that.setState({
        componentList: cloneDeep(data || compList),
      });
    } else {
      // 获取工艺可分配组件
      const url = `${Host}/v1/${tenantId}/mt-router/bom/limit/component/list/ui?bomId=${currentBomId}&routerId=${routerId === 'create' ? '' : routerId}`;

      const res = await myInstance.get(url);
      if (res && res.data.rows) {
        const enableFlagMap = {};
        stepsList.forEach(item => {
          if (
            item.mtRouterOperationDTO &&
            item.mtRouterOperationDTO.mtRouterOperationComponentDTO
          ) {
            (item.mtRouterOperationDTO.mtRouterOperationComponentDTO || []).forEach(subItem => {
              if (subItem.bomComponentId && subItem.enableFlag) {
                enableFlagMap[subItem.bomComponentId] = subItem.enableFlag;
              }
            });
          }
        });
        const componentList = [];
        res.data.rows
          .sort((a, b) => a.lineNumber - b.lineNumber)
          .forEach(item => {
            componentList.push({
              ...item,
              enableFlag: enableFlagMap[item.bomComponentId],
            });
          });
        that.setState({
          componentList,
        });
      }
    }
    const newList = [];
    const list = cloneDeep(lists || stepsList); // 深拷贝一下,否则会改变stepsList
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if ((item.mtRouterOperationDTO || {}).mtRouterOperationComponentDTO) {
        item.mtRouterOperationDTO.mtRouterOperationComponentDTO = [];
      }
      if (
        item.routerStepType === 'GROUP' &&
        ((item.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO || []).length > 0
      ) {
        for (
          let index = 0;
          index < item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.length;
          index++
        ) {
          const it = item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO[index];
          it.mtRouterOperationDTO.mtRouterOperationComponentDTO = [];
        }
      }
    }
    list.forEach((item, indexs) => {
      if (item.routerStepType === 'OPERATION') {
        // 如果是工艺类型则直接取出来,把索引加进去,保存时直接根据索引来查找
        newList.push({ ...item, indexs });
      } else if (
        item.routerStepType === 'GROUP' &&
        ((item.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO || []).length > 0
      ) {
        item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.forEach((it, index) => {
          newList.push({ ...it, indexs, stepGroup: true, index }); // 是步骤组类型且子步骤不为空,取数据时,加入步骤组的索引和子步骤的索引stepGroup,表明是步骤组下的工艺,
        });
      }
    });
    this.setState({
      operationList: newList.sort((a, b) => a.sequence - b.sequence),
      initOperationList: newList,
    });
  };

  onOk = () => {
    const {
      stepsList,
      UpdateCompList,
      UpdateStepList,
    } = this.props;
    const { componentList, initOperationList } = this.state;
    const assignList = componentList.filter(i => i.routerStepId);
    initOperationList.forEach(item => {
      assignList.forEach(it => {
        if (it.routerStepId === item.routerStepId) {
          // 根据outerStepId来判断是否有分配,有则插入
          if(((item.mtRouterOperationDTO || {}).mtRouterOperationComponentDTO || []).length > 0) {
            ((item.mtRouterOperationDTO || {}).mtRouterOperationComponentDTO || []).push({
              ...it,
              sequence: it.sequence || 1,
            });
          } else {
            (item.mtRouterOperationDTO || {}).mtRouterOperationComponentDTO = [{
              ...it,
              sequence: it.sequence || 1,
            }]
          }
        }
      });
    });
    initOperationList.forEach(item => {
      if (item.stepGroup) {
        // 根据stepGroup判断是否是步骤组下的工艺,根据索引来找到在stepsList中要替换的数据
        stepsList[item.indexs].mtRouterStepGroupDTO.mtRouterStepGroupStepDTO[item.index] = item;
      } else {
        stepsList[item.indexs] = item;
      }
    });

    UpdateCompList(componentList);
    UpdateStepList(stepsList);
    notification.success();
    this.initHandleData(componentList, stepsList);
  };

  // 判断该工艺下是否有分配组件
  hasAssign = val => {
    const { componentList = [] } = this.state;
    return componentList.filter(i => i.routerStepId === val).length > 0;
  };

  // 渲染工艺列表
  renderOperationList = list => {
    const stepOptions = [];
    const that = this;
    const { operationStatus, currentOperation } = this.state;
    list.forEach(item => {
      if (
        item.routerStepType === 'OPERATION' &&
        (operationStatus === 'all' ||
          (operationStatus === 'assigned' && that.hasAssign(item.routerStepId)) ||
          (operationStatus === 'unassigned' && !that.hasAssign(item.routerStepId)))
      ) {
        stepOptions.push(
          <div
            style={{
              backgroundColor:
                currentOperation === item.routerStepId ? 'rgba(214,255,253,1)' : 'inherit',
            }}
            className={styles.customBox}
          >
            <Radio value={item.routerStepId} className={styles.radioBox}>
              {item.sequence}-{item.description}
              {item.stepName && '/'}
              {item.stepName}
              <Icon
                type="check"
                className={styles.containerItemIcon}
                style={{ visibility: that.hasAssign(item.routerStepId) ? 'visible' : 'hidden' }}
              />
            </Radio>
          </div>,
        );
      }
    });
    return stepOptions;
  };

  // 渲染组件列表
  renderComponentList = list => {
    const stepOptions = [];
    const { editStatus } = this.props;
    const { currentOperation } = this.state;
    list.forEach((item, index) => {
      stepOptions.push(
        <div
          className={styles.containerComponentItem}
          key={item.bomComponentId}
          style={{
            display: item.cusStyle,
            backgroundColor:
              currentOperation === item.routerStepId ? 'rgba(214,255,253,1)' : 'inherit',
          }}
        >
          <div className={styles.leftCheckBox}>
            <Checkbox
              className={styles.checkBox}
              value={item.bomComponentId}
              key={item.bomComponentId}
            >
              {item.lineNumber}-{item.materialCode}
              {item.revisionCode && `/${item.revisionCode}`}
              {item.materialName && `/${item.materialName}`}
            </Checkbox>
          </div>
          <div className={styles.rightDescBox}>
            <span
              style={{ color: editStatus || !currentOperation ? 'rgba(0, 0, 0, 0.25)' : 'inherit' }}
            >
              {item.routerStepDesc}
              {item.routerStepName && '/'}
              {item.routerStepName || ''}
            </span>
            <Icon
              type="close"
              style={{ display: editStatus && item.routerStepId ? '' : 'none' }}
              className={styles.deleteIcon}
              disabled={editStatus}
              onClick={() => {
                this.cancelComponent(item.bomComponentId, index);
              }}
            />
          </div>
        </div>,
      );
    });
    return stepOptions;
  };

  // 取消分配
  cancelComponent = id => {
    const { componentList = [], currentComponent } = this.state;
    for (let i = 0; i < componentList.length; i++) {
      const item = componentList[i];
      if (item.bomComponentId === id) {
        item.routerStepId = '';
        item.routerStepDesc = '';
        item.routerStepName = '';
        item.sequence = '';
        item.enableFlag = 'N';
      }
    }
    this.setState({
      componentList,
      currentComponent: currentComponent.filter(i => i !== id),
    });
  };

  changeCheckBox = checkedValues => {
    const { componentList, currentOperation, operationList } = this.state;
    this.setState({
      currentComponent: checkedValues,
      indeterminate: !!checkedValues.length && checkedValues.length < componentList.length,
      checkAll: checkedValues.length === componentList.length,
    });
    const operationArr = operationList.filter(i => i.routerStepId === currentOperation);
    if (checkedValues.length === 0) {
      for (let i = 0; i < componentList.length; i++) {
        const item = componentList[i];
        if (currentOperation === item.routerStepId) {
          item.routerStepId = '';
          item.routerStepDesc = '';
          item.routerStepName = '';
          item.sequence = '';
          item.enableFlag = 'N';
        }
      }
    } else {
      for (let index = 0; index < componentList.length; index++) {
        const item = componentList[index];
        checkedValues.forEach(i => {
          if (i === item.bomComponentId) {
            item.routerStepId = operationArr[0].routerStepId;
            item.routerStepDesc = operationArr[0].description;
            item.routerStepName = operationArr[0].stepName;
            item.sequence = operationArr[0].sequence;
            item.enableFlag = 'Y';
          } else if (
            currentOperation === item.routerStepId &&
            checkedValues.indexOf(item.bomComponentId) === -1
          ) {
            item.routerStepId = '';
            item.routerStepDesc = '';
            item.routerStepName = '';
            item.sequence = '';
            item.enableFlag = 'N';
          }
        });
      }
    }
    this.setState({
      componentList,
    });
  };

  // 选择工艺时，将当前工艺已分配的组件置顶且按照组件行号进行排序；
  // 将未分配的组件置于中间，按组件行号排序；
  // 将其他工艺已分配的组件置于最后，先按步骤顺序排序，再按组件行号排序。
  changeRadio = e => {
    const { componentList } = this.state;
    const componentArr = [];
    const assignList = []; // 已分配组件
    const otherAssignedList = []; // 其他工艺已分配组件
    const unassignedList = []; // 未分配组件
    componentList.forEach(item => {
      if (item.routerStepId === e.target.value) {
        assignList.push(item);
        componentArr.push(item.bomComponentId);
      } else if (item.routerStepId) {
        otherAssignedList.push(item);
      } else {
        unassignedList.push(item);
      }
    });
    unassignedList.sort((a, b) => a.lineNumber - b.lineNumber);
    otherAssignedList
      .sort((a, b) => a.sequence - b.sequence)
      .sort((a, b) => a.lineNumber - b.lineNumber);
    const newList = assignList
      .sort((a, b) => a.lineNumber - b.lineNumber)
      .concat(unassignedList)
      .concat(otherAssignedList);
    this.setState({
      currentOperation: e.target.value,
      currentComponent: componentArr,
      componentList: newList,
    });
  };

  // 切换全部工艺下拉框
  changeOperationOptions = val => {
    const { initOperationList } = this.state;
    const that = this;
    if (val === 'all') {
      that.setState({
        operationList: initOperationList,
        operationStatus: val,
      });
    } else {
      that.setState({
        operationStatus: val,
      });
    }
  };

  // 切换全部组件下拉框
  changeComponentOptions = val => {
    const { componentList } = this.state;
    const that = this;
    if (val === 'all') {
      that.setState({
        componentList: componentList.map(item => {
          return {
            ...item,
            cusStyle: '',
          };
        }),
        componentStatus: val,
      });
    } else if (val === 'assigned') {
      that.setState({
        componentList: componentList.map(item => {
          if (!item.routerStepId) {
            return {
              ...item,
              cusStyle: 'none',
            };
          }
          return {
            ...item,
            cusStyle: '',
          };

        }),
        componentStatus: val,
      });
    } else {
      that.setState({
        componentList: componentList.map(item => {
          if (item.routerStepId) {
            return {
              ...item,
              cusStyle: 'none',
            };
          }
          return {
            ...item,
            cusStyle: '',
          };

        }),
        componentStatus: val,
      });
    }
  };

  // 切换自动完成
  changeAutoOptions = val => {
    const { initOperationList, operationStatus } = this.state;
    if (!val) {
      this.changeOperationOptions(operationStatus);
      return null;
    }
    this.setState({
      operationList: initOperationList.filter(
        i => (i.sequence + i.description + i.stepName).indexOf(val) !== -1,
      ),
    });
  };

  // 组件切换自动完成
  changeComponentAutoOptions = val => {
    const { componentStatus, componentList } = this.state;
    if (!val) {
      this.changeComponentOptions(componentStatus);
      return null;
    }
    this.setState({
      componentList: componentList.map(item => {
        if ((item.lineNumber + item.materialCode + item.materialName).indexOf(val) !== -1) {
          return {
            ...item,
            cusStyle: '',
          };
        }
        return {
          ...item,
          cusStyle: 'none',
        };

      }),
    });
  };

  // 全选
  onCheckAllChange = e => {
    const { componentList = [], operationList = [], currentOperation } = this.state;
    const allArr = [];
    componentList.forEach(item => {
      allArr.push(item.bomComponentId);
    });
    if (e.target.checked) {
      const operationArr = operationList.filter(i => i.routerStepId === currentOperation);
      for (let i = 0; i < componentList.length; i++) {
        const item = componentList[i];
        item.routerStepId = operationArr[0].routerStepId;
        item.routerStepDesc = operationArr[0].description;
        item.routerStepName = operationArr[0].stepName;
        item.sequence = operationArr[0].sequence;
        item.enableFlag = 'Y';
      }
    } else {
      for (let i = 0; i < componentList.length; i++) {
        const item = componentList[i];
        item.routerStepId = '';
        item.routerStepDesc = '';
        item.routerStepName = '';
        item.sequence = '';
        item.enableFlag = 'N';
      }
    }
    this.setState({
      currentComponent: e.target.checked ? allArr : [],
      indeterminate: false,
      checkAll: e.target.checked,
      componentList,
    });
  };

  render() {
    const { editStatus } = this.props;

    const {
      currentOperation,
      currentComponent,
      operationList = [],
      componentList = [],
      componentStatus,
      operationStatus,
    } = this.state;
    const options = operationList.map(item => (
      <AutoComplete.Option
        style={{ display: item.routerStepType === 'OPERATION' ? '' : 'none' }}
        key={item.routerStepId}
        text={`${item.sequence}-${item.description} ${item.stepName ? '/' && item.stepName : ''}`}
      >
        {item.sequence}-{item.description} {item.stepName ? '/' && item.stepName : ''}
      </AutoComplete.Option>
    ));
    const nextOptions = componentList.map(item => (
      <AutoComplete.Option
        key={item.bomComponentId}
        text={`${item.lineNumber}-${item.materialCode}/${item.materialName}`}
      >
        {item.lineNumber}-{item.materialCode}/{item.materialName}
      </AutoComplete.Option>
    ));
    return (
      <div className={styles.componentDistribution}>
        <div className={styles.leftWrapper}>
          <div className={styles.stepHeader}>
            <span>{intl.get(`${modelPrompt}.operationList`).d('工艺列表')}</span>
            <div className={styles.stepTitleDropdown}>
              <Select
                value={operationStatus}
                style={{ width: 102 }}
                onChange={this.changeOperationOptions}
              >
                <Select.Option value="all">
                  {intl.get(`${modelPrompt}.operationAll`).d('全部工艺')}
                </Select.Option>
                <Select.Option value="assigned">
                  {intl.get(`${modelPrompt}.operationAssigned`).d('已分配工艺')}
                </Select.Option>
                <Select.Option value="unassigned">
                  {intl.get(`${modelPrompt}.operationUnassigned`).d('未分配工艺')}
                </Select.Option>
              </Select>
            </div>
          </div>
          <div className={styles.stepBottom}>
            <div className={styles.searchInput}>
              <AutoComplete
                dropdownStyle={{ width: 300 }}
                style={{ width: 'calc(100% - 16px)' }}
                placeholder={intl.get(`${modelPrompt}.inputSearch`).d('请输入搜索内容')}
                optionLabelProp="text"
                dataSource={options}
                allowClear
                onChange={this.changeAutoOptions}
                open={false}
              >
                <Input suffix={<Icon type="search" />} dbc2sbc={false} />
              </AutoComplete>
            </div>
            <div className={styles.boxWrapper}>
              <Radio.Group onChange={this.changeRadio} value={currentOperation}>
                {this.renderOperationList(operationList)}
              </Radio.Group>
            </div>
          </div>
        </div>
        <div className={styles.iconWrapper}>
          <img
            alt=""
            src={currentOperation && !editStatus ? rightIconChoose : rightIcon}
            width="20px"
            height="18px"
          />
        </div>
        <div className={styles.rightWrapper}>
          <div className={styles.componentHeader}>
            <Checkbox
              indeterminate={this.state.indeterminate}
              onChange={this.onCheckAllChange}
              checked={this.state.checkAll}
              disabled={editStatus || !currentOperation}
            >
              {intl.get(`${modelPrompt}.componentList`).d('组件列表')}
            </Checkbox>
            <div className={styles.stepTitleDropdown}>
              <Select
                value={componentStatus}
                style={{ width: 102 }}
                onChange={this.changeComponentOptions}
              >
                <Select.Option value="all">
                  {intl.get(`${modelPrompt}.componentAll`).d('全部组件')}
                </Select.Option>
                <Select.Option value="assigned">
                  {intl.get(`${modelPrompt}.componentAssigned`).d('已分配组件')}
                </Select.Option>
                <Select.Option value="unassigned">
                  {intl.get(`${modelPrompt}.componentUnassigned`).d('未分配组件')}
                </Select.Option>
              </Select>
            </div>
          </div>
          <div className={styles.componentBottom}>
            <div className={styles.searchInput}>
              <AutoComplete
                dropdownStyle={{ width: 300 }}
                style={{ width: 'calc(100% - 16px)' }}
                placeholder={intl.get(`${modelPrompt}.inputSearch`).d('请输入搜索内容')}
                optionLabelProp="text"
                dataSource={nextOptions}
                allowClear
                onChange={this.changeComponentAutoOptions}
                open={false}
              >
                <Input suffix={<Icon type="search" />} dbc2sbc={false} />
              </AutoComplete>
            </div>
            <div className={styles.boxComponentWrapper}>
              <Checkbox.Group
                onChange={this.changeCheckBox}
                value={currentComponent}
                disabled={editStatus || !currentOperation}
              >
                {this.renderComponentList(componentList)}
              </Checkbox.Group>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
