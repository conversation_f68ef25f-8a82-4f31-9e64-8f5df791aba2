/**
 * 报检明细
 * @date 2023-5-15
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState } from 'react';
import intl from 'utils/intl';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Collapse, Breadcrumb } from 'choerodon-ui';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';

import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { flow } from 'lodash';
import formatterCollections from "utils/intl/formatterCollections";
import withProps from "utils/withProps";
import styles from '../index.module.less';
import { headDS, tableDS } from "../stores/MaterialBatchDS";

const { Panel } = Collapse;

interface DetailModalProps {
  inspectRequestId: number;
  headDs: DataSet,
  tableDs: DataSet,
}

const modelPrompt = 'tarzan.hmes.inspection.inspection-management';

const CreateMaterial: React.FC<DetailModalProps> = ({
  headDs,
  inspectRequestId,
}) => {
  useEffect(() => {
    handleQuery();
  }, []);
  const [currentCrumb, setCurrentCrumb] = useState()
  const [resultData, setResultData] = useState([]);
  const [tableDsArr, setTableDsArr] = useState([]);

  const handleQuery = async () => {
    headDs.setQueryParameter('inspectRequestId', inspectRequestId);
    await headDs.query();
    const data = headDs.toData();

    // @ts-ignore
    setResultData(data)
    const arr = [];
    data.forEach(() => {
      // @ts-ignore
      return arr.push(new DataSet(tableDS()));
    })
    setTableDsArr([...arr]);
    // @ts-ignore
    for (let i = 0; i < arr.length; i++) {
      // @ts-ignore
      arr[i].loadData([...data[i].disposalList]);
    }
    setTableDsArr([...arr]);
  };



  const headColumns: ColumnProps[] = [
    {
      name: 'objectTypeDesc',
      width: 150,
    },
    {
      name: 'objectCode',
      width: 150,
      renderer: ({ value ,record }) => {
        if (record?.get('objectType') === 'MATERIAL_LOT') {
          return value;
        }
      },
    },
    {
      name: 'quantity',
      width: 60,
      align: ColumnAlign.right,
    },
    {
      name: 'uomCode',
      width: 70,
    },
    {
      name: 'locatorCode',
      width: 120,
    },
    {
      name: 'locatorName',
      width: 140,
    },
    {
      name: 'lot',
      width: 120,
    },
    {
      name: 'supplierLot',
      width: 120,
    },
    {
      name: 'okQuantity',
      width: 120,
      align: ColumnAlign.right,
      renderer: ({ value  }) => {
        if (value > 0) {
          return value;
        }
      },
    },
    {
      name: 'ngQuantity',
      width: 120,
      align: ColumnAlign.right,
      renderer: ({ value  }) => {
        if (value > 0) {
          return value;
        }
      },
    },
  ];

  const columns: ColumnProps[] = [
    {
      name: 'qualityStatusDesc',
      width: 120,
    },
    {
      name: 'quantity',
      width: 60,
      align: ColumnAlign.right,
    },
    {
      name: 'uomName',
      width: 70,
    },
    {
      name: 'disposalFunctionDesc',
      width: 90,
    },
    {
      name: 'disposalUserName',
      width: 120,
    },
    {
      name: 'disposalTime',
      width: 130,
      align: ColumnAlign.center,
    },
    {
      name: 'disposalApartment',
      width: 120,
    },
    {
      name: 'degradeLevel',
      width: 120,
    },
    {
      name: 'degradeMaterialName',
      width: 120,
    },
    {
      name: 'degradeRevisionCode',
      width: 120,
    },
    {
      name: 'disposalExecuteUserName',
      width: 120,
    },
    {
      name: 'disposalExecuteTime',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'disposalExecuteDocNum',
      width: 120,
      align: ColumnAlign.left,
    },
    {
      name: 'finalExecuteObjectCode',
      width: 150,
      renderer: ({ value }) => {
        // @ts-ignore
        if (resultData[0]?.objectType === 'MATERIAL_LOT') {
          return value;
        }
      },
    },
    {
      name: 'remark',
      width: 120,
    },
  ];

  const crumbClick = (e) => {
    setCurrentCrumb(e);
    // @ts-ignore
    document.getElementById("table-container").scroll({
      // @ts-ignore
      top: document.getElementById(`number${e}`).offsetTop - 37,
      behavior: "smooth",
    });
  }

  const renderCrumbItem = () => {
    return resultData.map((item: any) =>
      <Breadcrumb.Item
        onClick={() => crumbClick(item.objectCode)}
        className={currentCrumb === item.objectCode ? styles.cusCrumb : styles.cusCrumbNormal}
      >
        {resultData[0]?.objectType === 'MATERIAL_LOT' && item.objectCode}
      </Breadcrumb.Item>,
    )
  }

  const renderTable = () => {
    return tableDsArr.map((item, index) => (
      <>
        <p
          // @ts-ignore
          id={`number${resultData[index]?.objectCode}`}
          // @ts-ignore
          className={currentCrumb === resultData[index]?.objectCode && styles.cusPcolor}
        >
          {/* @ts-ignore */}
          { resultData[index]?.objectType === 'MATERIAL_LOT' && resultData[index]?.objectCode}
        </p>
        <Table dataSet={item} columns={columns} />
      </>
    ));
  }

  return (
    <div className="hmes-style">
      <Table dataSet={headDs} columns={headColumns} />
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.detail.result`).d('报检请求处置结果')}
          key="basicInfo"
        >
          <Breadcrumb className={styles['cus-bread-crumb']} separator=' '>
            {renderCrumbItem()}
          </Breadcrumb>
          <div className={styles['table-container']} id='table-container'>
            {renderTable()}
            <div className={styles['empty-container']} />
          </div>
        </Panel>
      </Collapse>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.hmes.inspection.inspection-management', 'tarzan.common'] }),
  withProps(
    () => {
      const headDs = new DataSet({ ...headDS() });
      return {
        headDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(CreateMaterial);
