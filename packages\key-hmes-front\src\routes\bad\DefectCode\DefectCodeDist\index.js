/**
 * @Description: 不良代码维护详情
 * @Author: <<EMAIL>>
 * @Date: 2022-07-12 10:59:30
 * @LastEditTime: 2023-05-18 15:10:59
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  DataSet,
  Table,
  Spin,
  Button,
  Form,
  TextField,
  Lov,
  Select,
  IntlField,
  Switch,
  NumberField,
} from 'choerodon-ui/pro';
import { Popconfirm, Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { RecordStatus } from "choerodon-ui/dataset/data-set/enum";
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { AttributeDrawer } from '@components/tarzan-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { SubmitData } from '../services';
import { formDS, technologyDS } from '../stores/DefectCodeDetailDS';

const { Panel } = Collapse;

const modelPrompt = 'tarzan.badCode.defectCode.model.defectCode';

const DefectCodeDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    custConfig,
    customizeForm,
  } = props;

  const submitData = useRequest(SubmitData(), { manual: true });

  const technologyDs = useMemo(() => new DataSet(technologyDS()), []);
  const formDs = useMemo(() => new DataSet({
    ...formDS(),
    children: { mtNcValidOperList: technologyDs },
  }), []);

  const [canEdit, setCanEdit] = useState(id === 'create');
  const [loading, setLoading] = useState(false);
  const [deleteIds, setDeleteIds] = useState([]); // 删除的工艺处置组关系主键

  useEffect(() => {
    if (id === 'create') {
      return;
    }
    handleQuery();
  }, [id]);

  const handleQuery = () => {
    setLoading(true);
    setCanEdit(false);
    formDs.setQueryParameter('ncCodeId', id);
    formDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.NC_CODE_DETAIL.BASIC`);
    formDs
      .query()
      .then(() => {
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const changEditStatus = () => {
    setCanEdit(true);
  };

  const handleSaveBomList = async () => {
    const formDsResult = await formDs.validate();
    const technologyDsResult = await technologyDs.validate();
    if (formDsResult && technologyDsResult) {
      const formDsData = formDs.current.toData();
      const technologyData = [];
      technologyDs.forEach(record => {
        if ([RecordStatus.add, RecordStatus.update].includes(record.status)) {
          technologyData.push(record.toData());
        }
      })

      submitData.run({
        params: {
          deleteNcValidOperationIds: deleteIds,
          mtNcCode: {
            ...formDsData,
            mtNcValidOperList: undefined,
          },
          mtNcValidOperList: technologyData,
        },
        onSuccess: res => {
          notification.success();
          setDeleteIds([]);
          if (id === 'create') {
            props.history.push({
              pathname: `/hmes/bad/defect-code/dist/${res}`,
            });
          } else {
            handleQuery();
          }
        },
      });
    }
  };

  const onCancel = () => {
    if (id === 'create') {
      props.history.push({
        pathname: `/hmes/bad/defect-code/list`,
      });
    } else {
      handleQuery();
      setDeleteIds([]);
    }
  };

  const handleAddRowTechnology = () => {
    technologyDs.create({},0);
  };

  const deleteRecordTechnology = record => {
    if (record.get('ncValidOperationId')) {
      const _deleteIds = deleteIds || [];
      _deleteIds.push(record.get('ncValidOperationId'));
      setDeleteIds(_deleteIds);
    }
    technologyDs.remove(record);
  };

  const handleSiteChange = () => {
    if (technologyDs.toData().length > 0) {
      notification.info({
        message: intl
          .get(`tarzan.badCode.defectCode.message.deleteMessage`)
          .d('因更换站点，已删除次级不良代码与工艺维护里面的所有新增状态数据'),
      });
      technologyDs.loadData([]);
    }
    formDs.current.set('dispositionGroupObject', null);
  };

  const technologyColumns = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit || !formDs.current.get('siteId')}
          funcType="flat"
          onClick={handleAddRowTechnology}
          shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          disabled={!canEdit || !formDs.current.get('siteId')}
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          okText={intl.get('tarzan.common.button.confirm').d('确定')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          onConfirm={() => deleteRecordTechnology(record)}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit || !formDs.current.get('siteId')}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'operation',
      editor: record => canEdit && record.status === 'add' && <Lov />,
    },
    {
      name: 'dispositionGroupObject',
      editor: () => canEdit && <Lov />,
    },
  ];

  return (
    <div className="hmes-style">
      <Spin spinning={submitData.loading || loading}>
        <Header
          title={intl.get('tarzan.badCode.defectCode.title.defectCode').d('不良代码维护')}
          backPath="/hmes/bad/defect-code/list"
        >
          {!canEdit ? (
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              onClick={changEditStatus}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          ) : (
            <>
              <Button
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="save"
                loading={submitData.loading}
                onClick={handleSaveBomList}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button type="button" icon="close" onClick={onCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          <AttributeDrawer
            serverCode={BASIC.TARZAN_METHOD}
            canEdit={canEdit}
            disabled={id === 'create'}
            kid={id}
            className="org.tarzan.method.domain.entity.MtNcCode"
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.NC_CODE_DETAIL.BUTTON`}
            custConfig={custConfig}
          />
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={['basic', 'technology']}
          >
            <Panel
              header={intl.get(`${modelPrompt}.basic`).d('基础属性')}
              key="basic"
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_CODE_DETAIL.BASIC`,
                },
                <Form disabled={!canEdit} dataSet={formDs} columns={3}>
                  <TextField name="ncCode" />
                  <IntlField
                    name="description"
                    modalProps={{
                      title: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码描述'),
                    }}
                  />
                  <Lov name="site" onChange={handleSiteChange} />
                  <TextField name="scrapDetail" />
                  <Select name="ncType" />
                  <NumberField name="maxNcLimit" />
                  <Lov name="dispositionGroupObject" />
                  <Select name="defectLevel" />
                  <Switch name="enableFlag" />
                  <Switch name="autoCloseIncident" />
                  <Switch name="validAtAllOperations" />
                  <Switch name="allowNoDisposition" />
                  <Switch name="componentRequired" />
                </Form>,
              )}
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.technology`).d('工艺分配')}
              key="technology"
            >
              <Table dataSet={technologyDs} columns={technologyColumns} />
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.badCode.defectCode', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.NC_CODE_DETAIL.BUTTON`, `${BASIC.CUSZ_CODE_BEFORE}.NC_CODE_DETAIL.BASIC`],
  })(DefectCodeDetail),
);
