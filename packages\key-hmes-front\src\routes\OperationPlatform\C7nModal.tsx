/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-28 11:12:04
 * @LastEditTime: 2023-08-21 15:32:29
 * @LastEditors: <<EMAIL>>
 */
import { Modal as C7nModal } from 'choerodon-ui/pro';
import ModalManager from 'choerodon-ui/pro/lib/modal-manager';

const openModal = (modalOpen) => (props) => {
  // 获取所有实例
  const { containerInstances, removeInstance, addInstance } = ModalManager as any;
  const operationPlatformContainer = document.querySelector('.c7n-tabs-tabpane-active #operationPlatform');
  let instance;
  if (operationPlatformContainer) {
    // 找到需要打开弹窗的实例
    const container = containerInstances.find(x => x.getOffsetContainer() === document.getElementById('operationPlatform'));
    // 暂时移出全局 modal
    const bodyInstance = containerInstances.filter(
      (x) =>
        typeof x.props.getContainer === 'function' &&
        x.props.getContainer()?.parentElement?.tagName.toLowerCase() === 'body',
    );
    bodyInstance.forEach(instance => {
      removeInstance(instance);
    });
    // 需要打开弹窗的实例置顶
    container?.top();
    // 执行打开
    instance = modalOpen(props);
    // 最后将全局实例添加回来。
    if (bodyInstance.length) {
      bodyInstance.forEach(x => {
        addInstance(x);
      });
    }
  } else {
    instance = modalOpen(props);
  }
  // 最后返回打开实例
  return instance;
};

C7nModal.open = openModal(C7nModal.open)

export default C7nModal;
