/**
 * @Description: 检验方案-列表
 * @Author: <<EMAIL>>
 * @Date: 2023-01-05 10:38:58
 * @LastEditTime: 2023-05-18 16:38:12
 * @LastEditors: <<EMAIL>>

*/
import React, { useEffect, useState } from 'react';
import { DataSet, Spin, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import request from 'utils/request';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { copyDS, listTableDS } from '../stores/InspectionSchemeDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.initialManagementActivity';

const InspectionSchemeList = props => {
  const {
    tableDs,
    match: { path },
  } = props;

  const [selectedRecords, setSelectedRecords] = useState<Array<any>>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (tableDs?.currentPage) {
      tableDs.query(props.tableDs.currentPage);
      tableDs.unSelectAll();
    } else {
      tableDs.query();
      tableDs.unSelectAll();
    }
  }, []);

  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteObject':
        record.set('inspectSchemeObject', null);
        record.set('areaObject', null);
        record.set('prodLineObject', null);
        record.set('processObject', null);
        record.set('stationObject', null);
        break;
      case 'inspectSchemeObjectTypeObject':
        record.set('inspectSchemeObject', null);
        record.set('revisionCode', null);
        break;
      default:
        break;
    }
  });

  const handleInspectObjTableSelect = ({ dataSet }) => {
    setSelectedRecords(dataSet.selected || []);
  };
  useDataSetEvent(tableDs, 'select', handleInspectObjTableSelect);
  useDataSetEvent(tableDs, 'selectAll', handleInspectObjTableSelect);
  useDataSetEvent(tableDs, 'unselect', handleInspectObjTableSelect);
  useDataSetEvent(tableDs, 'unselectAll', handleInspectObjTableSelect);

  const columns: ColumnProps[] = [
    {
      name: 'initialManagementNum',
      lock: ColumnLock.left,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          {value}
        </a>
      ),
      minWidth: 160,
    },
    {
      name: 'initialManagementDesc',
      minWidth: 160,
    },
    {
      name: 'initialManagementStatus',
      minWidth: 120,
    },
    {
      name: 'createdByDesc',
      minWidth: 120,
    },
    {
      name: 'creationDate',
      minWidth: 160,
    },
    {
      name: 'siteCode',
      minWidth: 160,
    },
    {
      name: 'model',
      minWidth: 220,
    },
    {
      name: 'materialCode',
      minWidth: 220,
    },
    {
      name: 'materialName',
      minWidth: 220,
    },
    {
      name: 'phaseDesc',
      minWidth: 220,
    },
    {
      name: 'scheduleStartDate',
      minWidth: 220,
    },
    {
      name: 'scheduleEndDate',
      minWidth: 220,
    },
    {
      name: 'actInitiationReason',
      minWidth: 220,
    },
    {
      name: 'actObject',
      minWidth: 220,
    },
  ];

  const handleEdit = record => {
    props.history.push(
      `/hwms/initial-management-activity-platform/detail/${record.get('initialManagementId')}`,
    );
  };

  const handleCreate = () => {
    props.history.push(`/hwms/initial-management-activity-platform/detail/create`);
  };

  const handleCancel = () => {
    setLoading(true);
    request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/initial-managements/cancel/ui`, {
      method: 'POST',
      data: selectedRecords.map(item => item.data.initialManagementId),
    }).then(res => {
      if (res.failed) {
        notification.error({
          message: `${res.message}`,
        });
      } else {
        notification.success({
          message: intl.get(`${modelPrompt}.notification.success`).d('操作成功'),
        });
        tableDs.unSelectAll();
        tableDs.query(props.tableDs.currentPage);
      }
      setLoading(false);
    });
  };

  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        <Header title={intl.get(`${modelPrompt}.title.list`).d('初期管理活动平台')}>
          <PermissionButton
            type="c7n-pro"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
            color={ButtonColor.primary}
            icon="add"
            onClick={() => handleCreate()}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
          <PermissionButton
            type="c7n-pro"
            permissionList={[
              {
                code: `${path}.button.cancel`,
                type: 'button',
                meaning: '列表页-取消按钮',
              },
            ]}
            color={ButtonColor.primary}
            disabled={selectedRecords.length === 0}
            onClick={() => handleCancel()}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </PermissionButton>
        </Header>
        <Content>
          <Table
            searchCode="InitialManagementActivityPlatform"
            customizedCode="InitialManagementActivityPlatform"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
          />
        </Content>
      </Spin>
    </div>
  );
};
export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...listTableDS(),
      });
      const copyDs = new DataSet({
        ...copyDS(),
      });
      return {
        tableDs,
        copyDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    InspectionSchemeList as any,
  ),
);
