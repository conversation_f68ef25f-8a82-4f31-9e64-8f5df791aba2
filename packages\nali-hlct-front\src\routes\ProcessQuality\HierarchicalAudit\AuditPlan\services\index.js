/**
 * @Description: 分层审核计划-接口
 * @Author: <<EMAIL>>
 * @Date: 2023-08-16 11:31:24
 * @LastEditTime: 2023-08-16 11:31:24
 * @LastEditors: <<EMAIL>>
 */

import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const endUrl = '';

// ${ BASIC.TARZAN_SAMPLING }

// 查询审核计划明细信息
export function fetchReviewPlanConfig(layerReviewPlanId) {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-review-plan/${layerReviewPlanId}/ui`,
    method: 'GET',
  };
}

// 保存审核计划明细信息
export function saveReviewPlanConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-review-plan/save/ui`,
    method: 'POST',
  };
}

// 更新审核计划明细信息
export function updateReviewPlanConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-review-plan/update/ui`,
    method: 'POST',
  };
}

// 提交审核计划
export function submitReviewPlanConfig() {
  return {
    url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-layer-review-plan/submit/ui`,
    method: 'POST',
  };
}