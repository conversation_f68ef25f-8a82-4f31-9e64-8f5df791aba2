/**
 * @Description: ORT检验方案维护-详情
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { DataSet, Button, Form, Lov, TextField, Table, Switch } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import notification from 'utils/notification';

import { useDataSetEvent } from 'utils/hooks';
import { Header, Content } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { observer } from 'mobx-react';
import { DragColumnAlign } from 'choerodon-ui/pro/lib/table/enum';

import { SaveVerification, GetDefaultSite, GetUserRole } from '../services';
import { detailDS, taskDS } from '../stores/DetailDS';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.ort.InspectionScheme';

const TaskDetail = props => {
  const {
    history,
    match: { params },
  } = props;
  const schemeId = params.id;

  const [canEdit, setCanEdit] = useState(false);
  const [createFlag, setCreateFlag] = useState<boolean>(false);
  const taskDs = useMemo(() => new DataSet(taskDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          qisOrtInspectSchemeLines: taskDs,
        },
      }),
    [],
  );

  const { run: saveVerification, loading: saveLoading } = useRequest(SaveVerification(), {
    manual: true,
  });
  const { run: getDefaultSite, loading: siteLoading } = useRequest(GetDefaultSite(), {
    manual: true,
  });
  const { run: getCreateRole, loading: createRoleLoading } = useRequest({ lovCode: 'YP.QIS.ORT_SCHEME_CERATE_ROLE' }, {
    manual: true,
    needPromise: true,
  });
  const { run: getUserRole, loading: userRoleLoading } = useRequest(GetUserRole(), {
    manual: true,
    needPromise: true,
  });

  useDataSetEvent(detailDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialLov':
        record.set('productionVersionLov', {});
        record.set('defaultBomLov', {});
        record.set('defaultRouterLov', {});
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    if (schemeId === 'create') {
      // 新建时
      setCanEdit(true);
      getDefaultSite({
        onSuccess: res => {
          if (res?.siteId) {
            detailDs.current?.set('siteLov', res);
          }
        },
      });
      return;
    }
    // 编辑时
    handleQueryDetail(schemeId);
  }, [schemeId]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('schemeId', id);
    detailDs.query();
    handleInitCreateRole();
  };

  const handleInitCreateRole = async () => {
    const res = await getCreateRole({});
    const currentRoleRes = await getUserRole({});
    const createRoleList = (res || []).map((item) => item.value);
    const currentRoleList = (currentRoleRes || []).map((item) => item.code);
    const createFlag = createRoleList.find((item) => currentRoleList.includes(item));
    setCreateFlag(Boolean(createFlag));
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(() => {
    if (schemeId === 'create') {
      history.push('/hmes/ort/inspection-scheme/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(schemeId);
    }
  }, []);

  const taskColumns: any = useMemo(
    () => [
      {
        name: 'sequence',
        renderer: ({ record }: any) => {
          return record.index * 10 + 10;
        },
      },
      {
        name: 'inspectItem',
        editor: canEdit,
      },
      {
        name: 'inspectMethod',
        editor: canEdit,
      },
      {
        name: 'standardRequirement',
        editor: canEdit,
      },
      {
        name: 'inspectFrequency',
        editor: canEdit,
      },
      {
        name: 'outsourceFlag',
        editor: canEdit && <Switch />,
      },
      {
        name: 'enclosure',
        editor: canEdit,
      },
    ],
    [canEdit],
  );

  // @ts-ignore
  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const data: any = detailDs.toData()[0] || {};
    if ((data.qisOrtInspectSchemeLines || []).length === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.saveErr`).d('请添加测试项目'),
      });

      return false;
    }
    data.qisOrtInspectSchemeLines = (data.qisOrtInspectSchemeLines || []).map((item, index) => {
      return {
        ...item,
        sequence: 10 * index + 10,
      };
    });

    saveVerification({
      params: data,
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        if (schemeId === 'create') {
          history.push(`/hmes/ort/inspection-scheme/${res}`);
        } else {
          handleQueryDetail(res);
        }
      },
    });
  };

  const RenderDeleteAddButton = observer(({ dataSet, canEdit }) => {
    return (
      <>
        <Button
          icon="delete"
          funcType={FuncType.flat}
          color={ButtonColor.red}
          disabled={!canEdit || !dataSet.selected.length}
          onClick={() => dataSet.remove(dataSet.selected)}
        >
          {intl.get(`${modelPrompt}.button.delete`).d('删除')}
        </Button>
        <Button
          icon="playlist_add"
          funcType={FuncType.flat}
          disabled={!canEdit}
          onClick={() => {
            dataSet.create({});
          }}
        >
          {intl.get(`${modelPrompt}.button.add`).d('新增')}
        </Button>
      </>
    );
  });

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || siteLoading ||createRoleLoading || userRoleLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.dist`).d('ORT检验方案维护')}
          backPath="/hmes/ort/inspection-scheme/list"
        >
          {canEdit ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <Button icon="edit-o" color={ButtonColor.primary} onClick={handleEdit} disabled={!createFlag}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
        </Header>
        <Content>
          <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
            <TextField name="inspectSchemeCode" />
            <TextField name="inspectSchemeName" />
            <Lov name="siteLov" />
            <Lov name="materialObj" />
            <TextField name="materialName" />
            <Switch name="enableFlag" />
          </Form>
          <Collapse bordered={false} defaultActiveKey={['taskInfo']}>
            <Panel key="taskInfo" header={intl.get(`${modelPrompt}.title.taskInfo`).d('测试项目')}>
              <Table
                customizedCode="ortjyfa3"
                rowDraggable={canEdit}
                dragColumnAlign={DragColumnAlign.left}
                buttons={[
                  <>
                    <RenderDeleteAddButton dataSet={taskDs} canEdit={canEdit} />
                  </>,
                ]}
                dataSet={taskDs}
                columns={taskColumns}
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(TaskDetail);
