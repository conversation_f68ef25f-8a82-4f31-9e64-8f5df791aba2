/*
 * @Description: 市场活动单-主界面
 * @Author: <<EMAIL>>
 * @Date: 2023-09-18 18:04:36
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-04 11:46:42
 */
import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { isNil } from 'lodash';
import moment from 'moment';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnAlign } from 'choerodon-ui/dataset/enum';
import { ColumnLock } from 'choerodon-ui/pro/es/table/enum';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores';

const modelPrompt = 'tarzan.qms.marketActivity.marketActivityDoc';
const tenantId = getCurrentOrganizationId();

const MarketActivityList = props => {
  const { tableDs, history } = props;

  const renderStatusTag = record => {
    const { marketActivityStatus } = record.toData();
    if (!marketActivityStatus) {
      return;
    }
    let color;
    switch (marketActivityStatus) {
      case 'NEW':
        color = 'green';
        break;
      case 'APPROVING':
        color = 'orange';
        break;
      case 'EXECUTING':
        color = 'yellow';
        break;
      case 'COMPLETED':
        color = 'blue';
        break;
      default:
        color = 'geekblue';
    }
    return <Tag color={color}>{record.getField('marketActivityStatus').getText()}</Tag>
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'marketActivityCode',
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/market-quality/market-activity-doc/dist/${record!.get(
                    'marketActivityId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'siteName', width: 180 },
      {
        name: 'marketActivityStatus',
        renderer: ({ record }) => renderStatusTag(record),
      },
      { name: 'responsibleDepartmentName' },
      { name: 'faultPhenomenon', width: 230 },
      { name: 'occurrence', width: 230 },
      { name: 'severityLevel' },
      { name: 'batteryPackModel' },
      { name: 'ypItemCode', width: 180 },
      { name: 'ypItemName', width: 180 },
      { name: 'itemCode', width: 180 },
      { name: 'itemName', width: 180 },
      { name: 'customerName' },
      { name: 'problemCode', width: 180 },
      { name: 'problemTitle', width: 230 },
      { name: 'applyDepartmentName' },
      { name: 'vehicleModel' },
      { name: 'reason', width: 230 },
      { name: 'objectQty' },
      { name: 'objectRange' },
      { name: 'enclosure' },
      { name: 'activityOverview', width: 230 },
      { name: 'materialCost' },
      { name: 'manageCost' },
      { name: 'laborCost' },
      { name: 'singleUnitCost' },
      { name: 'measurementSumCost', width: 180 },
      { name: 'startTime', align: ColumnAlign.center, width: 150 },
      {
        name: 'completionDegree',
        width: 180,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return `${value}%`;
        },
      },
      { name: 'endTime', align: ColumnAlign.center, width: 150 },
      { name: 'createdByName' },
      { name: 'creationDate', align: ColumnAlign.center, width: 150 },
    ];
  }, []);

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
      marketActivityIdList: tableDs.selected?.map(_record => _record?.get('marketActivityId')),
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('市场活动单')}>
        <ExcelExport
          exportAsync
          requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-market-activitys/export/ui`}
          queryParams={getExportQueryParams}
        >
          {intl.get(`${modelPrompt}.button.export`).d('导出')}
        </ExcelExport>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="marketActivityDoc_searchCode"
          customizedCode="marketActivityDoc_customizedCode"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [
    modelPrompt,
    'tarzan.common',
  ],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MarketActivityList),
);
