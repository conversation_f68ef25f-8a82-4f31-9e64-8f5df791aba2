/*
 * @Description: 调拨单据
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-03-28 11:40:08
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */
import React, { FC, useEffect, useState, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { useEditFlag } from 'alm/hooks';
// import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { DataSet, Form, Table, Spin, Lov } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { isUndefined } from 'lodash';
import { History } from 'history';
import formatterCollections from 'utils/intl/formatterCollections';
import { useDataSet } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
// 调拨特有 start
import OrgPartnerLov from 'alm/components/OrgPartnerLov';
import { queryOrgDefaultManager } from 'alm/services/organizationService';
// 调拨特有 end
import HeadBtns from '../../Components/HeadBtns';
import {
  useDsQueryById,
  useLineColumns,
  useInitNewDetail,
  useSave,
  useDataSetBeforeLoad,
  useSaveLine,
  useDeleteLine,
  useAssetTransactionType,
  useOpenLineModal,
  useWithdraw,
  useSubmit,
  useUpdateStatus,
  useDynamicProps,
  useHeadBtnsStyle,
} from '../../Hooks/detail';
import { lineDS } from '../../Stores/detailDs';
import { transferDetailDS } from '../Stores';
import { baseViewFormRender, baseEditFormRender } from '../../utils/renderer';
import { getAssetTransUploadAttrData, handleChangeOrgLov, handleCreateAsset } from '../../utils';
import { AssetTransRouteCode } from '../../enum';
import AssetBtn from '../../Components/AssetBtn';
import getLang from '../../Langs';
import OutInCard from './OutInCard';

const organizationId = getCurrentOrganizationId();

interface Props {
  match: any;
  history: History;
  location: any;
}
const Index: FC<Props> = props => {
  const {
    history,
    match: { url },
  } = props;

  // 返回跳转的界面
  const backUrl =
    url.indexOf('execute-handle') > -1
      ? `/aatn/asset-transaction-basic-type/execute-handle/transfer/list`
      : `/aatn/asset-transaction-basic-type/transfer/list`;

  // 路由类型编码
  const routeType = AssetTransRouteCode.transfer;

  // 页面loading: 头 + 动态字段加载时显示
  const [pageLoading, setPageLoading] = useState(false);

  // 是否为编辑状态
  const { isNew, editFlag, setEditFlag, id } = useEditFlag(props);

  const detailDs = useDataSet(() => new DataSet(transferDetailDS())); // 详情ds
  const lineDs = useDataSet(() => new DataSet({ ...lineDS(), paging: !isNew })); // 行ds 新建时不分页

  // 根据资产事务头id变化时重新查询详情
  useDsQueryById(detailDs, id);
  // 根据资产事务头id变化查询行数据
  useDsQueryById(lineDs, id);

  // 详情、动态字段配置信息变化时，重新解析动态字段配置
  const { dynamicProps, setAttrField } = useDynamicProps(detailDs);

  // 新建时设置数据默认值
  useInitNewDetail(detailDs, props, setPageLoading, setAttrField, lineDs);

  // 监听详情数据加载事件设置详情状态
  const detail = useDataSetBeforeLoad(detailDs);

  const headBtnsStyle = useHeadBtnsStyle(detail, isNew, editFlag);

  // 获取资产事务头行附件上传附加参数
  const assetTransUploadAttrData = getAssetTransUploadAttrData({ ...detail, routeType });

  // 设置是否处于加载状态
  useEffect(() => {
    setPageLoading(detailDs.status !== 'ready');
  }, [detailDs.status]);

  // 行操作
  const handleAddLine = useSaveLine(lineDs, detail);
  const handleDeleteLine = useDeleteLine(lineDs, detailDs);
  // 添加设备资产
  const handleAddAsset = items => {
    if (isNew) {
      handleCreateAsset(lineDs, items, detailDs?.current?.get('transactionTypeId'));
    } else {
      handleAddLine('ASSET', items);
    }
  };

  // 获取资产事务类型详情
  const { assetTransType, assetTransactionTypeDs } = useAssetTransactionType(detail, detailDs);

  // 行编辑、详情查看
  const { handleOpenLineModal } = useOpenLineModal(lineDs, assetTransType);

  // 资产事务行表格显示的列，如需扩展列请基于这个构建，或者重写
  const columns = useLineColumns(
    editFlag,
    assetTransUploadAttrData,
    detail?.processStatus,
    handleOpenLineModal,
    handleDeleteLine
  );

  // 单据保存
  const { saveLoading, handleSave } = useSave(detailDs, lineDs, assetTransactionTypeDs);
  // 保存接口调用完成后,这里暂不提取是因为关联的参数太多
  const handleSaveAfter = res => {
    if (res && res.success) {
      if (isUndefined(id)) {
        const { transactionTypeId, changeHeaderId } = res.content[0];
        history.push({
          pathname: `/aatn/asset-transaction-basic-type/${routeType}/detail/${transactionTypeId}/${changeHeaderId}`,
          state: {
            isEdit: false,
          },
        });
      } else {
        setEditFlag(false);
        detailDs.query();
        lineDs.query();
      }
    }
  };

  // 保存按钮
  const handleSaveBtn = async () => {
    const result = await handleSave();
    await handleSaveAfter(result);
  };

  const handleWithdraw = useWithdraw(detail, detailDs, lineDs);
  const handleSubmit = useSubmit(detail, detailDs, lineDs);
  const handleUpdateStatus = useUpdateStatus(detailDs, lineDs);

  // 编辑、取消编辑按钮
  const handleSwitchEdit = () => {
    if (editFlag) {
      // 关闭：取消编辑时恢复原数据
      detailDs.reset();
      lineDs.reset();
    }
    setEditFlag(!editFlag);
  };

  // 设备资产按钮样式
  const assetBtnSty =
    editFlag && assetTransType?.recipientsScopeList?.includes('ASSET')
      ? { display: 'block' }
      : { display: 'none' };

  // 行面板样式：新建时不显示行信息
  // const linePanelSty = isNew ? { display: 'none' } : { display: 'block' };

  // === 调拨特有的部分 start ===
  const transferHdBtnSty = useMemo(() => {
    // 调拨单非编辑状态单据为待处理、待调出、待调入、审批中显示撤回按钮
    const recallBtnSty =
      !editFlag &&
      ['PENDING', 'APPROVING', 'TRANSFER_OUT', 'TRANSFER_IN'].includes(detail?.processStatus)
        ? { display: 'block' }
        : { display: 'none' };

    return {
      ...headBtnsStyle,
      recallBtnSty,
    };
  }, [headBtnsStyle]);

  const handleDefaultManager = async (orgId, fieldName) => {
    const result = await queryOrgDefaultManager({
      tenantId: organizationId,
      orgId,
    });
    if (!result?.failed && result.length === 1) {
      detailDs.current!.set(`${fieldName}Id`, result[0]?.employeeId);
      detailDs.current!.set(`${fieldName}Name`, result[0]?.employeeName);
    }
    //  else {
    //   detailDs.current!.set(`${fieldName}Id`, undefined);
    //   detailDs.current!.set(`${fieldName}Name`, undefined);
    // }
  };

  const handleChangeTransOutOrg = (record, type) => {
    handleChangeOrgLov(detailDs, 'transferOutOrg', record, type);
    handleDefaultManager(record?.orgId, 'transferOutMan');
  };

  const handleChangeTransInOrg = (record, type) => {
    handleChangeOrgLov(detailDs, 'transferInOrg', record, type);
    handleDefaultManager(record?.orgId, 'transferInMan');
  };

  const customRender = (
    <>
      <OrgPartnerLov name="transferOutOrgName" handleOk={handleChangeTransOutOrg} />
      <Lov name="transferOutManLov" />
      <OrgPartnerLov name="transferInOrgName" handleOk={handleChangeTransInOrg} />
      <Lov name="transferInManLov" />
      <Lov name="dispatchUserLov" />
      <Lov name="previousUserLov" />
      <Lov name="dispatchAssetLocationLov" />
      <Lov name="previousAssetLocationLov" />
    </>
  );

  // === 调拨特有的部分 end ===

  return (
    <React.Fragment>
      <Header title={getLang('TRANSFER_DETAIL')} backPath={backUrl}>
        <HeadBtns
          detail={detail}
          history={history}
          isNew={isNew}
          headBtnsStyle={transferHdBtnSty}
          onSwitchEdit={handleSwitchEdit}
          onSave={handleSaveBtn}
          saveLoading={saveLoading}
          assetTransUploadAttrData={assetTransUploadAttrData}
          onWithdraw={handleWithdraw}
          onSubmit={handleSubmit}
          onCancel={handleUpdateStatus}
        />
      </Header>
      <Content>
        <Spin spinning={pageLoading}>
          <Collapse bordered={false} defaultActiveKey={['A', 'B']} className="form-collapse">
            <Collapse.Panel key="A" header={getLang('BASIC')}>
              <Form dataSet={detailDs} labelWidth={120} columns={4}>
                {editFlag
                  ? baseEditFormRender(detailDs, dynamicProps, customRender)
                  : baseViewFormRender(dynamicProps)}
              </Form>
              {editFlag ? null : <OutInCard detail={detail} />}
            </Collapse.Panel>
            <Collapse.Panel key="B" header={getLang('TRANS_LINE')}>
              <div
                style={{
                  display:
                    editFlag && assetTransType?.recipientsScopeList?.length > 0 ? 'flex' : 'none',
                  marginTop: 10,
                }}
              >
                <AssetBtn
                  style={assetBtnSty}
                  transactionTypeId={detailDs?.current?.get('transactionTypeId')}
                  assetIdList={lineDs.map(record => record.get('objectId')).join(',')}
                  onOk={handleAddAsset}
                  disabled={lineDs.length>=1}
                />
              </div>

              <Table
                key="assetTransactionsTransfer"
                customizedCode="AORI.ASSET_TRANSACTIONS.TRANSFER"
                dataSet={lineDs}
                columns={columns}
              />
            </Collapse.Panel>
          </Collapse>
        </Spin>
      </Content>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['aatn.assetTransactionBasicType', 'alm.common', 'alm.component'],
})(observer(Index));
