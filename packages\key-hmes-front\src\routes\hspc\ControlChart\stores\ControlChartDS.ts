/**
 * @Description: 分析控制图维护维护列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2021-11-15 14:41:53
 * @LastEditTime: 2022-11-09 14:39:47
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import Decimal from "decimal.js";
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from 'hcm-components-front/lib/utils/utils';
import { BASIC } from 'hcm-components-front/lib/utils/config';

const modelPrompt = 'tarzan.hspc.controlChartMaintain';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'controlCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlCode`).d('控制控制图编码'),
    },
    {
      name: 'controlDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlDesc`).d('控制控制图描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
    {
      name: 'chartType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chartType`).d('控制图类型'),
      lookupCode: 'MT.SPC.CHART_TYPE',
    },
    {
      name: 'analysisCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisCode`).d('分析控制图编码'),
    },
  ],
  fields: [
    {
      name: 'controlCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlCode`).d('控制控制图编码'),
      disabled: true,
    },
    {
      name: 'controlDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlDesc`).d('控制控制图描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'accessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accessTypeDesc`).d('接入方式'),
    },
    {
      name: 'chartTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chartTypeDesc`).d('控制图类型'),
      lookupCode: 'MT.SPC.CHART_TYPE',
    },
    {
      name: 'subgroupSize',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.subgroupSize`).d('子组大小'),
    },
    {
      name: 'maxPlotPoints',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxPlotPoints`).d('最大绘点数'),
    },
    {
      name: 'analysisCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.analysisCode`).d('分析控制图编码'),
    },
    {
      name: 'serviceCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceCode`).d('服务编码'),
    },
    {
      name: 'chartTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chartTitle`).d('控制图标题'),
    },
    {
      name: 'samplingTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingTypeDesc`).d('抽样类型'),
    },
    {
      name: 'timeSampling',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.timeSamplingInterval`).d('时间抽样间隔'),
    },
    {
      name: 'timeSamplingNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.timeSamplingNumber`).d('时间抽样个数'),
    },
    {
      name: 'isometricSampling',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.isometricSamplingInterval`).d('等距抽样间隔'),
    },
    {
      name: 'isometricSamplingNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.isometricSamplingNum`).d('等距抽样个数'),
    },
    {
      name: 'groupBatchRuleDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.groupBatchRule`).d('组批规则'),
    },
    {
      name: 'timeGroupBatch',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.timeGroupBatch`).d('按时间组批'),
    },
    {
      name: 'sampleGroupBatch',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleGroupBatch`).d('按样本数组批'),
    },
    {
      name: 'unqualifiedGroupBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unqualifiedGroupBatch`).d('按不合格数组批'),
    },
    {
      name: 'samplingPositionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingPositionDesc`).d('取值位置'),
    },
    {
      name: 'dataUpperLimit',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataUpperLimit`).d('数据过滤上限'),
    },
    {
      name: 'dataLowerLimit',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataLowerLimit`).d('数据过滤下限'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/list/ui`,
        data,
        method: 'GET',
      };
    },
    destroy: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_HSPC}/v1/${tenantId}/control/delete/ui?controlId=${data[0].controlId}`,
        method: 'POST',
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return getResponse(parsedData);
          }
        },
      };
    },
  },
});

const controlChartDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'controlCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlCode`).d('控制控制图编码'),
      required: true,
    },
    {
      name: 'controlDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.controlDesc`).d('控制控制图描述'),
    },
    {
      name: 'accessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.accessType`).d('数据接入方式'),
      lookupCode: 'MT.SPC.ACCESS_TYPE',
      required: true,
    },
    {
      name: 'serviceCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceCode`).d('服务编码'),
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
});

const controlChartInfoDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'chartType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chartType`).d('控制图类型'),
      lookupCode: 'MT.SPC.CHART_TYPE',
      required: true,
    },
    {
      name: 'subgroupSize',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.subgroupSize`).d('子组大小'),
      required: true,
      step: 1,
      dynamicProps: {
        min: ({ record }) => {
          if (['XBAR-R', 'XBAR-S', 'Me-R'].includes(record?.get('chartType'))) {
            return 2;
          }
          return 0;
        },
      },
    },
    {
      name: 'maxPlotPoints',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxPlotPoints`).d('最大绘点数'),
      defaultValue: 50,
      required: true,
    },
    {
      name: 'chartTitle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chartTitle`).d('控制图标题'),
    },
    {
      name: 'xTickLabel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.xTickLabel`).d('X轴刻度标签'),
      lookupCode: 'MT.SPC.X_TICK_LABEL',
    },
    {
      name: 'attribute3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute3`).d('CPK标准值'),
    },
  ],
});

// @ts-ignore
const controlChartDetailInfoDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'analysisCode',
      // 有analysisCode时，证明时跳转过来的，控制线不做校验
    },
    {
      name: 'upperControlLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.upperControlLimit`).d('控制上限'),
      step: 0.0001,
      dynamicProps: {
        min({ record }) {
          if (record.get('analysisCode')) {
            return;
          }
          if (record.get('centerLine')) {
            return new Decimal(0.0001).add(new Decimal(Number(record.get('centerLine'))));
          } if (record.get('lowerControlLimit')) {
            return new Decimal(0.0001).add(new Decimal(Number(record.get('lowerControlLimit'))));
          }
        },
        required({ record }) {
          return !record.get('analysisCode');
        },
      },
    },
    {
      name: 'upperSpecLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.upperSpecLimit`).d('规格上限'),
      step: 0.0001,
      dynamicProps: {
        min({ record }) {
          if (record.get('specTarget')) {
            return new Decimal(0.0001).add(new Decimal(Number(record.get('specTarget'))));
          } if (record.get('lowerSpecLimit')) {
            return new Decimal(0.0001).add(new Decimal(Number(record.get('lowerSpecLimit'))));
          }
        },
        required:({dataSet})=>(dataSet.getState('required')),
      },
    },
    {
      name: 'judgementGroup',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.judgementGroup`).d('判异规则组'),
      lovCode: 'MT.SPC.JUDGEMENT_GROUP',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'judgementGroupCode',
      type: FieldType.string,
      bind: 'judgementGroup.judgementGroupCode',
    },
    {
      name: 'centerLine',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.centerLine`).d('中心线'),
      step: 0.0001,
      dynamicProps: {
        max({ record }) {
          if (record.get('analysisCode')) {
            return;
          }
          if (record.get('upperControlLimit')) {
            return new Decimal(Number(record.get('upperControlLimit'))).sub(new Decimal(0.0001));
          }
        },
        min({ record }) {
          if (record.get('analysisCode')) {
            return;
          }
          if (record.get('lowerControlLimit')) {
            return new Decimal(0.0001).add(new Decimal(Number(record.get('lowerControlLimit'))));
          }
        },
        required({ record }) {
          return !record.get('analysisCode');
        },
      },
    },
    {
      name: 'specTarget',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.specTarget`).d('目标值'),
      step: 0.0001,
      dynamicProps: {
        max({ record }) {
          if (record.get('upperSpecLimit')) {
            return new Decimal(Number(record.get('upperSpecLimit'))).sub(new Decimal(0.0001));
          }
        },
        min({ record }) {
          if (record.get('lowerSpecLimit')) {
            return new Decimal(0.0001).add(new Decimal(Number(record.get('lowerSpecLimit'))));
          }
        },
      },
    },
    {
      name: 'xAxisLabel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.xAxisLabel`).d('X轴标签'),
    },
    {
      name: 'lowerControlLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lowerControlLimit`).d('控制下限'),
      step: 0.0001,
      dynamicProps: {
        required({ record }) {
          return !record.get('analysisCode');
        },
      },
    },
    {
      name: 'lowerSpecLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lowerSpecLimit`).d('规格下限'),
      step: 0.0001,
      dynamicProps: {
        required:({dataSet})=>(dataSet.getState('required')),
      },
    },
    {
      name: 'yAxisLabel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.yAxisLabel`).d('Y轴标签'),
    },
  ],
});

const sampleDataFilteringRulesDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'samplingType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingType`).d('抽样类型'),
      lookupCode: 'MT.SPC.SAMPLING_TYPE',
    },
    {
      name: 'samplingPosition',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingPosition`).d('取值位置'),
      lookupCode: 'MT.SPC.SAMPLING_POSITION',
      dynamicProps: {
        required({ record }) {
          return record.get('samplingType');
        },
      },
    },
    {
      name: 'dataUpperLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.dataUpperLimit`).d('数据过滤上限'),
      step: 0.0001,
      dynamicProps: {
        min({ record }) {
          if (record.get('dataLowerLimit')) {
            return new Decimal(0.0001).add(new Decimal(Number(record.get('dataLowerLimit'))));
          }
        },
      },
    },
    {
      name: 'timeSampling',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.timeSamplingInterval`).d('时间抽样间隔'),
      dynamicProps: {
        required({ record }) {
          return record.get('samplingType') === 'TIME';
        },
        disabled({ record }) {
          return record.get('samplingType') === 'ISOMETRIC';
        },
      },
    },
    {
      name: 'timeSamplingNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.timeSamplingNumber`).d('时间抽样个数'),
      min: 0,
      step: 1,
      dynamicProps: {
        required({ record }) {
          return record.get('samplingType') === 'TIME';
        },
        disabled({ record }) {
          return record.get('samplingType') === 'ISOMETRIC';
        },
        max({ record }) {
          return record.get('timeSampling');
        },
      },
    },
    {
      name: 'dataLowerLimit',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.dataLowerLimit`).d('数据过滤下限'),
      step: 0.0001,
      dynamicProps: {
        max({ record }) {
          if (record.get('dataUpperLimit')) {
            return new Decimal(Number(record.get('dataUpperLimit'))).sub(new Decimal(0.0001));
          }
        },
      },
    },
    {
      name: 'isometricSampling',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.isometricSamplingInterval`).d('等距抽样间隔'),
      dynamicProps: {
        required({ record }) {
          return record.get('samplingType') === 'ISOMETRIC';
        },
        disabled({ record }) {
          return record.get('samplingType') === 'TIME';
        },
      },
    },
    {
      name: 'isometricSamplingNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.isometricSamplingNum`).d('等距抽样个数'),
      min: 0,
      step: 1,
      dynamicProps: {
        required({ record }) {
          return record.get('samplingType') === 'ISOMETRIC';
        },
        disabled({ record }) {
          return record.get('samplingType') === 'TIME';
        },
        max({ record }) {
          if (record.get('isometricSampling')) {
            return record.get('isometricSampling');
          }
        },
      },
    },
  ],
});

const groupBatchRuleDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'groupBatchRule',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.groupBatchRule`).d('组批规则'),
      lookupCode: 'MT.GROUP_BATCH_RULE',
    },
    {
      name: 'timeGroupBatch',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.timeGroupBatch`).d('按时间组批'),
      min: 0,
      step: 1,
      dynamicProps: {
        required({ record }) {
          return record.get('groupBatchRule') === 'TIME_GROUP_BATCH';
        },
        disabled({ record }) {
          return record.get('groupBatchRule') !== 'TIME_GROUP_BATCH';
        },
      },
    },
    {
      name: 'sampleGroupBatch',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleGroupBatch`).d('按样本数组批'),
      min: 0,
      step: 1,
      dynamicProps: {
        required({ record }) {
          return record.get('groupBatchRule') === 'SAMPLE_GROUP_BATCH';
        },
        disabled({ record }) {
          return record.get('groupBatchRule') !== 'SAMPLE_GROUP_BATCH';
        },
      },
    },
    {
      name: 'unqualifiedGroupBatch',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.unqualifiedGroupBatch`).d('按不合格数组批'),
      min: 0,
      step: 1,
      dynamicProps: {
        required({ record }) {
          return record.get('groupBatchRule') === 'UNQUALIFIED_GROUP_BATCH';
        },
        disabled({ record }) {
          return record.get('groupBatchRule') !== 'UNQUALIFIED_GROUP_BATCH';
        },
      },
    },
  ],
});

const automaticAccessDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'sourceMarkFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'sourceMark',
      type: FieldType.string,
      maxLength: 200,
      disabled: true,
      dynamicProps: {
        required({ record }) {
          if (record.get('sourceMarkFlag') === 'Y') {
            return true;
          }
          return false;

        },
      },
    },
  ],
});

export {
  tableDS,
  controlChartDS,
  controlChartInfoDS,
  controlChartDetailInfoDS,
  sampleDataFilteringRulesDS,
  groupBatchRuleDS, // 组批规则
  automaticAccessDS,
};
