import React, { useEffect, useState, useMemo } from 'react';
import {
  Button,
  DataSet,
  Table,
  Spin,
  Lov,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import request from 'utils/request';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { tableDS  } from './stores';
import { Host } from '@/utils/config';
import {fetchDefaultSite} from '../../services/api'


const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.scrapBarcodeGeneration';

const ScrapBarcodeGeneration = observer(() => {
  const [loading, setLoading] = useState(false);
  const [defaultSite, setDefaultSite] = useState({});

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  useEffect(() => {
    fetchDefaultSite().then(res => {
      if(res&&res.success){
        setDefaultSite(res.rows)
      }
    })
  }, []);

  const handleChangeEquip = async (val) => {
    if(val){
      setLoading(true)
      const res = await request(`${Host}/v1/${tenantId}/hme-partial-scrap/prod/query?equipmentCode=${val.equipmentCode}`, {
        method: 'GET',
      })
      setLoading(false)
      const result = getResponse(res);
      if(result){
        tableDs.current?.set('prodLineId', result.organizationId)
        tableDs.current?.set('prodLineCode', result.organizationCode)
        tableDs.current?.set('prodLineName', result.organizationName)
        tableDs.current?.set('operationId', result.operationId)
      }
    }
  }
  const columns = [
    {
      name: 'siteCode',
      width: 100,
    },
    {
      name: 'siteName',
      width: 200,
    },
    {
      name: 'equipLov',
      editor: record => record.getState('editing')&&<Lov onChange={handleChangeEquip} />,
      renderer: ({ record }) => record.get('equipmentCode'),
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'prodLineCode',
    },
    {
      name: 'prodLineName',
    },
    {
      name: 'workOrderLov',
      editor: record => record.getState('editing'),
      renderer: ({ record }) => record.get('workOrderNum'),
      width: 200,
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'uomName',
    },
    {
      name: 'eoQty',
      editor: record => record.getState('editing'),
    },
    {
      name: 'ncCodeLov',
      width: 150,
      renderer: ({ record }) => record.get('ncCode'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'identification',
    },
    {
      name: 'realName',
    },
  ];
  const handleCreate = () => {
    tableDs.create({
      siteId: defaultSite.siteId,
      siteCode: defaultSite.siteCode,
      siteName: defaultSite.siteName,
    }, 0);
    tableDs.current?.setState('editing', true);
    tableDs.select(tableDs.current)
  }

  const handleCancel = () => {
    tableDs.selected.forEach(item => {
      tableDs.remove(item);
    });
    tableDs.clearCachedSelected();
  }

  const handleSave = async () => {
    const p = tableDs.selected.map(item => item?.validate())
    const resp = await Promise.all(p)
    if(resp.some(item => !item))return
    setLoading(true)
    const temp = tableDs.selected.filter(item => item.status === 'add')
    const res = await request(`${Host}/v1/${tenantId}/hme-partial-scrap/generate/scrap`, {
      method: 'POST',
      body: temp.map(item => item.toData()),
    })
    setLoading(false)
    const result = getResponse(res);
    if(result){
      notification.success();
      tableDs.query();
    }
  }

  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        <Header title={intl.get(`${modelPrompt}.title`).d('部分报废条码生成-MES')}>
          <Button disabled={!tableDs.selected.length||!tableDs.selected.every(item => item.status === 'add')} onClick={handleSave} color="primary">{intl.get(`${modelPrompt}.title`).d('报废条码生成')}</Button>
          <Button icon="add" color="primary" onClick={handleCreate}>{intl.get(`tarzan.common.button.create`).d('新建')}</Button>
          <Button disabled={!tableDs.selected.length||!tableDs.selected.every(item => item.status === 'add')} onClick={handleCancel}>{intl.get(`tarzan.common.button.cancel`).d('取消')}</Button>
        </Header>
        <Content>
          <Table
            searchCode="ScrapBarcodeGeneration"
            customizedCode="ScrapBarcodeGeneration"
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            style={{ height: 400 }}
          />

        </Content>
      </Spin>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.scrapBarcodeGeneration', 'tarzan.common'],
})(ScrapBarcodeGeneration);
