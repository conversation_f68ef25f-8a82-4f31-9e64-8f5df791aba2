/**
 * @Description: 标准体系文件维护-表格DS
 * @Author: <<EMAIL>>
 * @Date: 2023-06-27
 * @LastEditTime: 2022-06-28
 * @LastEditors: <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';


const modelPrompt = 'key.hwms.front.MaintenanceOfStandardSystemDocuments';
const tableDetailDS = () => ({
  autoQuery: false,
  selection: false,
  autoCreate: true,
  fields: [
    {
      name: 'templateFileCode',
      label: intl.get(`${modelPrompt}.templateFileCode`).d('编码'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'templateFileName',
      label: intl.get(`${modelPrompt}.templateFileName`).d('名称'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      type: FieldType.boolean,
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'remark',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      type: FieldType.string,
    },
    {
      name: 'uuid',
      label: intl.get(`${modelPrompt}.uuid`).d('附件'),
      type: FieldType.string,
    },
  ],
});
const tableDetailLineDS = () => ({
  autoQuery: false,
  selection: false,
  parentField: 'parentTemplateFileDId',
  idField: 'templateFileDId',
  primaryKey: 'templateFileDId',
  fields: [
    {
      name: 'templateFileDId',
      label: intl.get(`${modelPrompt}.templateFileDId`).d('模板明细ID'),
      type: FieldType.string,
    },
    {
      name: 'nodeCode',
      label: intl.get(`${modelPrompt}.nodeCode`).d('节点编码'),
      type: FieldType.string,
    },
    {
      name: 'nodeName',
      label: intl.get(`${modelPrompt}.nodeName`).d('节点'),
      type: FieldType.string,
    },
    {
      name: 'nodeLevel',
      label: intl.get(`${modelPrompt}.nodeLevel`).d('节点层级'),
      type: FieldType.number,
    },
    {
      name: 'parentTemplateFileDId',
      label: intl.get(`${modelPrompt}.parentTemplateFileDId`).d('上级节点'),
      type: FieldType.string,
    },
    {
      name: 'uuid',
      type: FieldType.string,
    },
    {
      name: 'prentUuid',
      type: FieldType.string,
    },
    {
      name: 'sequence',
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      type: FieldType.number,
    },
  ],
});
export { tableDetailDS, tableDetailLineDS }