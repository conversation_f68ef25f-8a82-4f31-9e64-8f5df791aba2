/**
 * @Description: 拆解申请单-列表页DS
 * @Author: <EMAIL>
 * @Date: 2023/8/14 11:03
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.teardownApplyDoc';
const tenantId = getCurrentOrganizationId();
const endurl = '';

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'teardownApplyId',
  queryFields: [
    {
      name: 'teardownApplyNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownApplyNum`).d('申请编码'),
    },
    {
      name: 'materialLotType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotType`).d('电芯类型'),
      lookupCode: 'YP_QIS_TEARDOWN_BARCODE_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'materialLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('厂内电芯条码'),
      lovCode: 'YP_WMS.MES.MATERIAL_LOT',
      textField: 'materialLotCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => record?.get('outsideMaterialLotCode'),
      },
    },
    {
      name: 'materialLotCode',
      bind: 'materialLotLov.materialLotCode',
    },
    {
      name: 'outsideMaterialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.outsideMaterialLotCode`).d('厂外电芯条码'),
      computedProps: {
        disabled: ({ record }) => record?.get('materialLotCode'),
      },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('厂内物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      computedProps: {
        disabled: ({ record }) => record?.get('outsideMaterialCode'),
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('厂外物料编码'),
      computedProps: {
        disabled: ({ record }) => record?.get('materialId'),
      },
    },
    {
      name: 'productFormCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productFormCode`).d('产品形式'),
      lookupCode: 'YP.QIS.PRODUCT_FORM',
      lovPara: { tenantId },
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'prodlineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'createPersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('委托人'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'createPersonLov.id',
    },
    {
      name: 'sampleDeliveryTimeBegin',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sampleDeliveryTimeBegin`).d('送样时间从'),
      max: 'sampleDeliveryTimeEnd',
    },
    {
      name: 'sampleDeliveryTimeEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sampleDeliveryTimeEnd`).d('送样时间至'),
      min: 'sampleDeliveryTimeBegin',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('来源工序编码'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'operationName',
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'electricVoltage',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.electricVoltage`).d('电量'),
      min: 0,
      step: 1,
      max: 100,
    },
    {
      name: 'teardownApplyStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownApplyStatus`).d('状态'),
      lookupCode: 'YP.QIS.APPLY_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'teardownStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownStage`).d('阶段'),
      lookupCode: 'YP.QIS.NC_REPORT_STAGE',
      lovPara: { tenantId },
    },
  ],
  fields: [
    {
      name: 'teardownApplyId',
      type: FieldType.number,
    },
    {
      name: 'teardownApplyNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownApplyNum`).d('申请编码'),
    },
    {
      name: 'materialLotType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotType`).d('电芯类型'),
      lookupCode: 'YP_QIS_TEARDOWN_BARCODE_TYPE',
      lovPara: { tenantId },
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('电芯条码'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('电芯型号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'productFormCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productFormCode`).d('产品形式'),
      lookupCode: 'YP.QIS.PRODUCT_FORM',
      lovPara: { tenantId },
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线'),
    },
    {
      name: 'teardownReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownReason`).d('拆解原因'),
    },
    {
      name: 'teardownRemarks',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownRemarks`).d('拆解要求说明'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('委托人'),
    },
    {
      name: 'sampleDeliveryTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleDeliveryTime`).d('送样时间'),
    },
    {
      name: 'electricVoltage',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.electricVoltage`).d('电量'),
    },
    {
      name: 'teardownApplyStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownApplyStatus`).d('状态'),
      lookupCode: 'YP.QIS.APPLY_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('来源工序编码'),
    },
    {
      label: intl.get(`${modelPrompt}.table.operationDesc`).d('来源工序描述'),
      name: 'operationDesc',
      type: FieldType.string,
    },
    {
      name: 'teardownStage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownStage`).d('阶段'),
      lookupCode: 'YP.QIS.NC_REPORT_STAGE',
      lovPara: { tenantId },
    },
    {
      name: 'teardownTaskNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.teardownTaskNum`).d('拆解任务编号'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'qms',
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endurl}/v1/${tenantId}/qis-teardown-apply/ui`,
        method: 'GET',
        data: {
          ...data,
          materialLotCode: data?.materialLotCode || data?.outsideMaterialLotCode,
        },
      };
    },
  },
  record: {
    dynamicProps: {
      selectable: record => ['NEW', 'REJECT'].includes(record.get('teardownApplyStatus')),
    },
  },
});

export { tableDS };
