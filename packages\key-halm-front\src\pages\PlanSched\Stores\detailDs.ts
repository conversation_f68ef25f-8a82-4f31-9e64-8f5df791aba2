import moment from 'moment';
import { DataSetProps, DataToJSON } from 'choerodon-ui/pro/lib/data-set/interface';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { HALM_ORI } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ORI}/v1/${organizationId}`;
// const apiPrefix = `http://127.0.0.1:4523/m1/964148-0-4e810015/v1/${organizationId}`;

const detailDs = (): DataSetProps => ({
  autoCreate: true,
  autoQuery: false,
  autoQueryAfterSubmit: false,
  dataToJSON: DataToJSON.all,
  fields: [
    {
      label: getLang('SCHED_TYPE'),
      name: 'schedTypeCode',
      lookupCode: 'ALM.PLAN_SCHED_TYPE',
      type: FieldType.string,
      defaultValue: 'MAINTAIN_PLAN',
      disabled: true,
      required: true,
    },
    {
      label: getLang('SCHED_NAME'),
      name: 'schedName',
      type: FieldType.intl,
      required: true,
      maxLength: 40,
    },
    {
      label: getLang('SCHED_CODE'),
      name: 'schedCode',
      type: FieldType.string,
      maxLength: 40,
      disabled: true,
    },
    {
      label: getLang('SCHED_START_DATE'),
      name: 'schedStartDate',
      type: FieldType.dateTime,
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('cycleTypeCode') === 'TIME_CYCLE';
        },
      },
      defaultValue: moment().startOf('day').set('hours', 8).set('minutes', 30), // 时间类型才有 且默认当前日期零点
      max: 'schedEndDate',
    },
    {
      label: getLang('PLAN_DURING_DAY'),
      name: 'planDuringDay',
      type: FieldType.number,
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('cycleTypeCode') === 'TIME_CYCLE';
        },
      },
      pattern: /^\d{1,3}(\.\d{1,2})?$/g,
      min: 0.01,
    },
    {
      label: getLang('SCHED_END_DATE'),
      name: 'schedEndDate',
      type: FieldType.dateTime,
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('cycleTypeCode') === 'TIME_CYCLE';
        },
      },
      min: 'schedStartDate',
    },
    {
      name: 'maintSiteLov',
      label: getLang('MAINTSITE'),
      type: FieldType.object,
      lovCode: 'AMDM.ASSET_MAINT_SITE',
      lovPara: {
        tenantId: organizationId,
      },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'maintSiteName',
      label: getLang('MAINTSITE'),
      type: FieldType.string,
      bind: 'maintSiteLov.maintSiteName',
    },
    {
      name: 'maintSiteId',
      type: FieldType.number,
      bind: 'maintSiteLov.maintSiteId',
    },
    {
      label: getLang('CYCLE_FLAG'),
      name: 'cycleFlag',
      type: FieldType.boolean,
      trueValue: 1,
      falseValue: 0,
      defaultValue: 0,
    },
    {
      label: getLang('SCHED_INTERVAL'),
      name: 'schedInterval',
      type: FieldType.number,
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('cycleTypeCode') === 'TIME_CYCLE' && record?.get('cycleFlag');
        },
      },
      precision: 0,
      pattern: /^[1-9]\d{0,2}$/g,
      defaultValidationMessages: {
        patternMismatch: getLang('LIMIT_999'),
      },
    },
    {
      name: 'schedIntervalUom',
      type: FieldType.string,
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('cycleTypeCode') === 'TIME_CYCLE' && record?.get('cycleFlag');
        },
      },
      lookupCode: 'AMTC.DURATION_UNIT',
      defaultValue: 'DAY',
    },
    {
      label: getLang('LAST_PLAN_TIME'),
      name: 'lastPlanTime',
      type: FieldType.dateTime,
    },
    {
      label: getLang('CYCLE_TYPE'),
      name: 'cycleTypeCode',
      lookupCode: 'ALM.MAINTAIN_WAY',
      type: FieldType.string,
      required: true,
    },
    {
      label: getLang('CATCH_TIME'),
      name: 'catchTime',
      type: FieldType.number,
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('cycleTypeCode') === 'METER_CYCLETRIGGE';
        },
      },
      min: 1,
      max: 99,
      precision: 0,
    },
  ],
  transport: {
    read: ({ data, params }) => {
      const url = `${apiPrefix}/plan-schedules/${data.id}`;
      return {
        url,
        method: 'GET',
        params,
      };
    },
    submit: ({ data }) => {
      // 对 schedStartDate 做处理 如果是非时间类型 则不传值
      const newData = {
        ...data[0],
        tenantId: organizationId,
        schedStartDate: data[0].cycleTypeCode !== 'TIME_CYCLE' ? null : data[0].schedStartDate,
      };
      return {
        url: `${apiPrefix}/plan-schedules`,
        data: newData,
        method: newData.schedId ? 'PUT' : 'POST',
      };
    },
  },
});

export { detailDs };
