/**
 * @Description: 采购订单管理-生成送货单
 * @Author: <<EMAIL>>
 * @Date: 2021-12-14 10:50:11
 * @LastEditTime: 2022-04-25 14:14:35
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.purchase.order';
const tenantId = getCurrentOrganizationId();

const shippingDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  fields: [
    // 基本属性
    {
      name: 'instructionDocCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocCode`).d('送货单号'),
      disabled: true,
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      disabled: true,
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSite`).d('供应商地点'),
      disabled: true,
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('送货单类型'),
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-instruction-doc/operation-type/limit/doc/type/list?operationType=PO_DOC`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      name: 'demandTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.arrivalDate`).d('预计到货时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('备注'),
    },
    {
      name: 'sourceSystem',
      type: FieldType.string,
      lookupCode: 'SOURCE_SYSTEM',
      label: intl.get(`${modelPrompt}.sourceSystem`).d('来源系统'),
    },
  ],
});

const shippingListDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'poLineId',
  autoLocateFirst: false,
  fields: [
    {
      name: 'instructionLineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNum`).d('行号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'identifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
      lookupCode: 'MT.APS.GEN_TYPE_URL',
      lovPara: {
        typeGroup: 'IDENTITY_TYPE',
        tenantId: getCurrentOrganizationId(),
        userId: getCurrentUserId(),
      },
      valueField: 'typecode',
      textField: 'description',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'quantityOrdered',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.demandQuantity`).d('需求数量'),
    },
    {
      name: 'processedOrdered',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.deliveredQuantity`).d('已制单数量'),
    },
    {
      name: 'quantityMax',
      type: FieldType.number,
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.productionQuantity`).d('制单数量'),
      required: true,
      defaultValidationMessages: {
        rangeUnderflow: intl.get(`${modelPrompt}.greater.than.zero`).d('必须大于零'), // 正则不匹配的报错信息
      },
      validator: (...args) => {
        const {
          data: { quantity, quantityMax },
        } = args[2];
        if (quantityMax === 0) {
          return intl
            .get(`${modelPrompt}.quantity.verify`)
            .d('该行已制单数量已达到需求数量，不能继续建单');
        } if (quantity > quantityMax) {
          return intl
            .get(`hzero.c7nProUI.Validator.range_overflow`, {
              label: intl.get(`${modelPrompt}.productionQuantity`).d('制单数量'),
              max: quantityMax,
            })
            .d(`{label}必须小于或等于{max}。`);
        }
      },
      dynamicProps: {
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
      },
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.receiveWarehouse`).d('接收仓库'),
      // lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            // siteIds: [record?.get('siteId')].join(','),
            siteId: record?.get('siteId'),
            // locatorCategoryList: ['AREA'],
            businessTypes: record?.get('businessType'),
            queryType: 'TARGET',
            locatorCategories: 'AREA',
          };
        },
        required: ({ record }) => {
          return record?.get('toLocatorRequiredFlag') === 'Y';
        },
      },
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'receiveLocatorId',
      type: FieldType.string,
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },

    {
      name: 'toleranceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'toleranceType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('toleranceFlag') === 'Y';
        },
        disabled: ({ record }) => {
          return record?.get('toleranceFlag') === 'N';
        },
      },
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差'),
      min: 0,
      dynamicProps: {
        required: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'Y' &&
            (record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER')
          );
        },
        disabled: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'N' ||
            (record?.get('toleranceType') !== 'PERCENTAGE' &&
              record?.get('toleranceType') !== 'NUMBER')
          );
        },
        max: ({ record }) => {
          if (record?.get('toleranceType') === 'PERCENTAGE') {
            return 100;
          }
        },
      },
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差'),
      min: 0,
      dynamicProps: {
        required: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'Y' &&
            (record?.get('toleranceType') === 'PERCENTAGE' ||
              record?.get('toleranceType') === 'NUMBER')
          );
        },
        max: ({ record }) => {
          if (record?.get('toleranceType') === 'PERCENTAGE') {
            return 100;
          }
        },
        disabled: ({ record }) => {
          return (
            record?.get('toleranceFlag') === 'N' ||
            (record?.get('toleranceType') !== 'PERCENTAGE' &&
              record?.get('toleranceType') !== 'NUMBER')
          );
        },
      },
    },
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
    },
    {
      name: 'lineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poLineNum`).d('采购订单行号'),
    },
    {
      name: 'soNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNum`).d('销售订单号'),
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
    },
    {
      name: 'demandDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandDate`).d('需要日期'),
    },
  ],
  // record: {
  //   dynamicProps: {
  //     disabled: record => {
  //       return !record?.get('permissionFlag') || record?.get('permissionFlag') === 'N';
  //     },
  //   },
  // },
});

export { shippingDS, shippingListDS };
