/**
 * @Description: 用户权限编辑抽屉-左侧树
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 20:26:51
 * @LastEditTime: 2022-10-14 10:57:22
 * @LastEditors: <<EMAIL>>
 */
import React, { useCallback, useMemo } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Tree } from 'choerodon-ui/pro';
// import intl from 'utils/intl';

const { TreeNode } = Tree;
// const modelPrompt = 'tarzan.userInspectPermission';

/**
 * 树节点类型
 * @export
 * @interface OrgTreeNode
 */
export interface OrgTreeNode {
  organizationRelId: number; // 节点唯一Id
  organizationId: number; // 节点Id
  organizationCode: string; // 节点编码
  organizationDesc: string | null; // 节点描述
  organizationType: string; // 节点类型
  organizationTypeDesc: string; // 节点类型描述
  alreadyDefined: boolean | null; // 是否已分配
  sequence: number | null; // 排序
  subUserOrgRelList: OrgTreeNode[]; // 子节点信息
}

interface OrganizationTreeProps {
  ds: DataSet;
  orgTreeConfig: OrgTreeNode[];
  organizationTableDs: DataSet;
  canEdit: boolean;
  expendKey: string[];
}

const OrganizationTree = observer((props: OrganizationTreeProps) => {
  const { ds, orgTreeConfig, canEdit, organizationTableDs, expendKey } = props;

  const getDisableFlag = useCallback(
    item => {
      const organizationList = ds.current?.get('organizationList') || [];
      const checkedList = ds.current?.get('checkedKeys') || [];
      return (
        !canEdit ||
        ['ENTERPRISE', 'SITE'].includes(item.organizationType) ||
        undefined ||
        organizationTableDs?.findIndex(
          record => record?.get('organizationId') === item.organizationId,
        ) > -1 ||
        (ds.selected?.length < 2 &&
          organizationList?.findIndex(i => i.organizationId === item.organizationId) > -1) ||
        (checkedList.length && checkedList[0].organizationType !== item.organizationType) ||
        (organizationList.length && organizationList[0].organizationType !== item.organizationType)
      );
    },
    [ds.current?.id, canEdit, ds.selected?.length],
  );

  // const getSelectAllFlag = node => {
  //   // 将当前节点和下层数据打平
  //   const _list = node.subUserOrgRelList.concat([node]);
  //   let _selectAllFlag = true;
  //   const _checkedKey = ds.current?.get('checkedKeys');
  //   _list.forEach(item => {
  //     const index = _checkedKey?.findIndex(i => i.organizationId === item?.organizationId);
  //     if (
  //       index === undefined ||
  //       (index < 0 && !['ENTERPRISE', 'SITE'].includes(item.organizationType))
  //     ) {
  //       _selectAllFlag = false;
  //     }
  //   });
  //   return _selectAllFlag;
  // };

  // const selectAll = useMemo(() => intl.get(`${modelPrompt}.selectAll`).d('全选'), []);

  // const handleSelectAll = (_treeNode: OrgTreeNode) => {
  //   const _organizationList = ds!.current!.get('organizationList') || [];
  //   const _expandedKeys = ds!.current!.get('expandedKeys') || [];
  //   const _checkedKeys = ds!.current!.get('checkedKeys') || [];
  //   // 将当前节点和下层数据打平
  //   const _list = _treeNode.subUserOrgRelList.concat([_treeNode]);
  //   // 展开当前节点
  //   const expendedIndex = _expandedKeys?.findIndex(
  //     item => item?.organizationId === _treeNode?.organizationId,
  //   );
  //   if (expendedIndex < 0) {
  //     _expandedKeys.push(dataObj(_treeNode)); // 待更新
  //   }
  //   const allSelectFlag = getSelectAllFlag(_treeNode);
  //   if (allSelectFlag) {
  //     // 取消全选
  //     _list.forEach(item => {
  //       const _checkedIndex = _checkedKeys?.findIndex(
  //         i => i?.organizationId === item?.organizationId,
  //       );
  //       if (_checkedIndex > -1) {
  //         _checkedKeys.splice(_checkedIndex, 1);
  //       }
  //     });
  //   } else {
  //     // 全选
  //     _list.forEach(item => {
  //       const _originationIndex = _organizationList?.findIndex(
  //         i => i?.organizationId === item?.organizationId,
  //       );
  //       const _checkedIndex = _checkedKeys?.findIndex(
  //         i => i?.organizationId === item?.organizationId,
  //       );
  //       if (
  //         _checkedIndex < 0 &&
  //         _originationIndex < 0 &&
  //         !['ENTERPRISE', 'SITE'].includes(item.organizationType)
  //       ) {
  //         _checkedKeys.push(dataObj(item));
  //       }
  //     });
  //   }
  //   if (ds.selected.length) {
  //     ds.selected.forEach(record => {
  //       record?.set('expandedKeys', _expandedKeys);
  //       record?.set('checkedKeys', _checkedKeys);
  //     });
  //   } else {
  //     ds.current?.set('expandedKeys', _expandedKeys);
  //     ds.current?.set('checkedKeys', _checkedKeys);
  //   }
  // };

  const renderTreeItems = (nodes: OrgTreeNode[] | null) => {
    if (!nodes || !nodes.length) {
      return;
    }
    return nodes.map(item => {
      return (
        <TreeNode
          title={
            <div>
              {item.organizationTypeDesc}-{item.organizationCode}
              {/* {item.subUserOrgRelList && item.subUserOrgRelList.length > 0 && ( */}
              {/*  <a */}
              {/*    onClick={() => handleSelectAll(item)} */}
              {/*    style={{ marginLeft: '8px' }} */}
              {/*    disabled={!canEdit} */}
              {/*  > */}
              {/*    {selectAll} */}
              {/*  </a> */}
              {/* )} */}
            </div>
          }
          key={`${item.organizationId}`}
          id={item.organizationId}
          dataRef={item}
          disableCheckbox={getDisableFlag(item)}
        >
          {item.subUserOrgRelList && renderTreeItems(item.subUserOrgRelList)}
        </TreeNode>
      );
    });
  };

  const dataObj = (nodeData: OrgTreeNode) => ({
    organizationId: nodeData.organizationId,
    organizationType: nodeData.organizationType,
    permissionFlag: 'Y',
    organizationRelId: nodeData.organizationRelId,
    organizationCode: nodeData.organizationCode,
    organizationTypeDesc: nodeData.organizationTypeDesc,
  });

  const handleExpand = (_, { node }) => {
    const nodeData = node.dataRef;
    const _expendedKeys = ds.current?.get('expandedKeys') || [];
    const _currentIndex = _expendedKeys?.findIndex(
      item => item.organizationId === nodeData.organizationId,
    );
    if (_currentIndex < 0) {
      _expendedKeys.push(dataObj(nodeData));
    } else {
      _expendedKeys.splice(_currentIndex, 1);
    }
    if (ds.selected?.length) {
      ds.selected.forEach(_record => _record.set('expandedKeys', _expendedKeys));
    } else {
      ds.current?.set('expandedKeys', _expendedKeys);
    }
  };

  const handleCheck = (_, { node }) => {
    const nodeData = node.dataRef;
    const _checkedKeys = ds.current?.get('checkedKeys') || [];
    const _currentIndex = _checkedKeys?.findIndex(
      item => item.organizationId === nodeData.organizationId,
    );
    if (_currentIndex < 0) {
      _checkedKeys.push(dataObj(nodeData));
    } else {
      _checkedKeys.splice(_currentIndex, 1);
    }
    if (ds.selected?.length) {
      ds.selected.forEach(_record => _record.set('checkedKeys', _checkedKeys));
    } else {
      ds.current?.set('checkedKeys', _checkedKeys);
    }
  };

  const getExpandedKeys = useMemo(() => {
    const data = ds.current?.get('expandedKeys');
    if (data?.length) {
      return (data || []).map(item => String(item.organizationId));
    }
    return expendKey;
  }, [ds.current?.get('expandedKeys')?.length, expendKey?.length]);

  const getCheckedKeys = useMemo(() => {
    const data = ds.current?.get('checkedKeys');
    return {
      checked: (data || []).map(item => String(item.organizationId)),
      halfChecked: [],
    };
  }, [ds.current?.get('checkedKeys')?.length]);

  return (
    <Tree
      checkable
      onExpand={handleExpand}
      expandedKeys={getExpandedKeys}
      onCheck={handleCheck}
      checkedKeys={getCheckedKeys}
      checkStrictly
    >
      {renderTreeItems(orgTreeConfig)}
    </Tree>
  );
});

export default OrganizationTree;
