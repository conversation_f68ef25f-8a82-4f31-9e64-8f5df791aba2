/*
 * @Description: 产品审核计划-详情页
 * @Author: <<EMAIL>>
 * @Date: 2023-10-09 14:15:43
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-19 17:26:07
 */
import React, { useState, useEffect, useMemo } from 'react';
import {
  DataSet,
  YearPicker,
  Table,
  Button,
  Form,
  Lov,
  TextField,
  Select,
  TextArea,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanS<PERSON> } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { DataSetSelection } from 'choerodon-ui/dataset/data-set/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import moment from 'moment';
import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import { detailDS, tableDS, reviewTaskDS } from '../stores/DetailDS';
import { SaveProductRevPlan, SubmitProductRevPlan } from '../services';

const { Panel } = Collapse;
const { Option } = Select;
const modelPrompt = 'tarzan.qms.productReview.productReviewPlan';

const ProductReviewPlanDist = props => {
  const {
    history,
    match: { params, path },
  } = props;
  const kid = params.id;

  // pub 路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  const [canEdit, setCanEdit] = useState(false);
  const [productRevPlanStatus, setStatus] = useState('NEW');
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const reviewTaskDs = useMemo(() => new DataSet(reviewTaskDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          lines: tableDs,
          tasks: reviewTaskDs,
        },
      }),
    [],
  );
  const { run: saveProductRevPlan, loading: saveLoading } = useRequest(SaveProductRevPlan(), {
    manual: true,
    needPromise: true,
  });
  const { run: submitProductRevPlan, loading: submitLoading } = useRequest(SubmitProductRevPlan(), {
    manual: true,
  });

  useEffect(() => {
    if (kid === 'create') {
      // 新建时
      setCanEdit(true);
      return;
    }
    // 编辑时
    handleInitDetail(kid);
  }, [kid]);

  const handleInitDetail = (id = kid) => {
    detailDs.setQueryParameter('productRevPlanId', id);
    detailDs.query().then(res => {
      const { productRevPlanStatus } = res || {};
      setStatus(productRevPlanStatus);
      if (productRevPlanStatus === 'PUBLISHED') {
        tableDs.selection = DataSetSelection.multiple;
      }
    });
  };

  const handleEdit = () => {
    setCanEdit(true);
  };

  const handleCancel = () => {
    if (kid === 'create') {
      history.push('/hwms/product-review/product-review-plan/list');
    } else {
      setCanEdit(false);
      handleInitDetail();
    }
  };

  const handleSave = async (submitFlag = false) => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const { revYear, lines, ...others } = detailDs.current?.toData();
    const res = await saveProductRevPlan({
      params: {
        headerInfo: {
          ...others,
          revYear: moment(revYear).format('YYYY'),
          tasks: undefined,
        },
        lineInfos: lines,
      },
    });
    if (res && res.success) {
      notification.success({});
      if (submitFlag) {
        await handleSubmit(res.rows);
      } else {
        setCanEdit(false);
        if (kid === 'create') {
          history.push(`/hwms/product-review/product-review-plan/dist/${res.rows}`);
        } else {
          handleInitDetail(res.rows);
        }
      }
      return true;
    }
    return false;
  };

  const handleSubmit = async productRevPlanId => {
    submitProductRevPlan({
      params: productRevPlanId,
      onSuccess: () => {
        notification.success({});
        setCanEdit(false);
        handleInitDetail(productRevPlanId);
      },
    });
  };

  const handleGenerateTask = () => {
    if (tableDs.selected?.length !== 1) {
      notification.error({
        message: tableDs.selected?.length
          ? intl.get(`${modelPrompt}.generateTask.onlySelectOneData`).d('只能选择一条明细数据!')
          : intl.get(`${modelPrompt}.generateTask.selectOneData`).d('需选择一条明细数据!'),
      });
      return;
    }
    const { siteId, siteName, productRevPlanCode, productRevPlanId } = detailDs.current?.toData();
    const {
      productRevPlanDtlId,
      materialId,
      materialCode,
      materialName,
      workcellId,
      workcellCode,
      workcellName,
    } = tableDs.selected[0].toData();
    history.push({
      pathname: '/hwms/product-review/product-review-task/dist/create',
      state: {
        siteId,
        siteName,
        productRevPlanCode,
        productRevPlanId,
        productRevPlanDtlId,
        materialId,
        materialCode,
        materialName,
        workcellId,
        workcellCode,
        workcellName,
      },
    });
  };

  const handleDeleteLine = record => {
    tableDs.remove(record);
  };

  const planDtlColumns: any = useMemo(
    () => [
      canEdit && {
        header: () => (
          <Button
            icon="add"
            disabled={!canEdit}
            funcType={FuncType.flat}
            onClick={() => tableDs.create({}, 0)}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        width: 60,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDeleteLine(record)}
          >
            <Button icon="remove" disabled={!canEdit} funcType={FuncType.flat} size={Size.small} />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'materialLov',
        editor: canEdit,
      },
      { name: 'materialName' },
      {
        name: 'workcellLov',
        editor: canEdit,
      },
      {
        name: 'workcellName',
      },
      {
        name: 'cusMaterialCode',
        editor: canEdit,
      },
      {
        name: 'cusMaterialName',
        editor: canEdit,
      },
      {
        name: 'reviewMonth',
        editor: canEdit && (
          <Select>
            <Option value="1">{`1${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="2">{`2${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="3">{`3${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="4">{`4${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="5">{`5${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="6">{`6${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="7">{`7${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="8">{`8${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="9">{`9${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="10">{`10${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="11">{`11${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
            <Option value="12">{`12${intl.get(`${modelPrompt}.label.month`).d('月')}`}</Option>
          </Select>
        ),
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return `${value}${intl.get(`${modelPrompt}.label.month`).d('月')}`;
        },
      },
      {
        name: 'reviewLeaderLov',
        editor: canEdit,
      },
      {
        name: 'remark',
        editor: canEdit && <TextArea autoSize={{ minRows: 1, maxRows: 8 }} />,
      },
      { name: 'executeStatus' },
    ],
    [canEdit],
  );

  const taskColumns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'productRevTaskCode',
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/product-review/product-review-task/dist/${record!.get(
                    'productRevTaskId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'workcellCode' },
      { name: 'workcellName' },
      { name: 'productRevTaskStatus' },
      { name: 'reviewResult' },
      { name: 'creationDate', align: ColumnAlign.center, width: 150 },
    ],
    [],
  );

  const handleChangeSite = () => {
    if (!tableDs.length) {
      return;
    }
    tableDs.forEach(record => {
      record.set('materialLov', undefined);
      record.set('workcellLov', undefined);
    });
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading || submitLoading}>
        {pubFlag ? (
          <Header title={intl.get(`${modelPrompt}.title.dist`).d('产品审核计划')}>
            <ApprovalInfoDrawer objectTypeList={['QIS_PRODUCT_REV_PLAN']} objectId={kid} />
          </Header>
        ) : (
          <Header
            title={intl.get(`${modelPrompt}.title.dist`).d('产品审核计划')}
            backPath="/hwms/product-review/product-review-plan/list"
          >
            {canEdit ? (
              <>
                <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave(false)}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
                <Button icon="close" onClick={handleCancel}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
              </>
            ) : (
              <PermissionButton
                type="c7n-pro"
                icon="edit-o"
                color={ButtonColor.primary}
                onClick={handleEdit}
                disabled={!['NEW', 'REJECTED'].includes(productRevPlanStatus)}
                // permissionList={[
                //   {
                //     code: `${modelPrompt}.dist.button.edit`,
                //     type: 'button',
                //     meaning: '详情页-编辑按钮',
                //   },
                // ]}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </PermissionButton>
            )}
            <Button
              icon="send-o"
              disabled={!['NEW', 'REJECTED'].includes(productRevPlanStatus) || kid === 'create'}
              loading={submitLoading}
              onClick={() => {
                if (canEdit) {
                  handleSave(true);
                } else {
                  handleSubmit(kid);
                }
              }}
            >
              {intl.get(`${modelPrompt}.button.submit`).d('提交')}
            </Button>
            <Button
              icon="send-o"
              disabled={productRevPlanStatus !== 'PUBLISHED'}
              onClick={handleGenerateTask}
            >
              {intl.get(`${modelPrompt}.button.generateTask`).d('生成任务')}
            </Button>
            <ApprovalInfoDrawer objectTypeList={['QIS_PRODUCT_REV_PLAN']} objectId={kid} />
          </Header>
        )}
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={['basicInfo', 'reviewPlanDtl', 'reviewTaskInfo']}
          >
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.basicInfo`).d('基础信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="productRevPlanCode" />
                <Lov name="siteLov" onChange={handleChangeSite} />
                <TextArea name="productRevPlanDesc" autoSize={{ minRows: 1, maxRows: 8 }} />
                <YearPicker name="revYear" />
                <Select name="productRevPlanStatus" />
                <DateTimePicker name="creationDate" />
              </Form>
            </Panel>
            <Panel
              key="reviewPlanDtl"
              header={intl.get(`${modelPrompt}.title.reviewPlanDtl`).d('审核计划明细')}
            >
              <Table dataSet={tableDs} columns={planDtlColumns} />
            </Panel>
            {productRevPlanStatus === 'PUBLISHED' && (
              <Panel
                key="reviewTaskInfo"
                header={intl.get(`${modelPrompt}.title.reviewTaskInfo`).d('审核任务信息')}
              >
                <Table dataSet={reviewTaskDs} columns={taskColumns} />
              </Panel>
            )}
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(ProductReviewPlanDist);
