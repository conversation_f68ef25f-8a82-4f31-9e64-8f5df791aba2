/************************** index.js ************************/
.hcm-collection-group-card {
  list-style: none;
  padding: 8px 16px;
  background: #e9f9fa;
  margin: 0 0 0 24px;
  width: 324px;

  li {
    padding: 8px 0;

    span {
      color: #333;
    }
  }
}

.hcm-collection-list-card {
  :global(.c7n-card-body) {
    padding-top: 8px !important;
  }
}

/************************** lalala ************************/

.hcm-dataItem-group {
  display: flex;
  align-items: baseline;

  :global {
    .c7n-input-wrapper.c7n-input-has-border {
      top: -2px;
      width: 120px;
    }
  }

  &-add {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    // align-items: center;
    align-items: baseline;

    :global(.c7n-tag) {
      margin: 4px 8px 4px 0;
      padding: 0;
      line-height: 20px;

      i {
        margin-top: -2px;
      }
    }
  }

  &-add-text {
    margin: 0 8px 0 26px;

    &:before {
      content: '*';
      display: inline-block;
      margin-right: 0.02rem;
      line-height: 1;
      color: #d50000;
      opacity: 0;
    }

    &:after {
      content: ':';
      margin-left: 2px;
    }
  }

  &-add-picList {
    text-align: center;
    margin: 0 10px 6px 0;
    padding-top: 10px;
    width: 80px;

    &-picName {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    img {
      height: 42px;
    }
  }

  &-add-text-required {
    &:before {
      opacity: 1;
    }
  }

  &-add-input {
    height: 22px;
    line-height: 22px;
    width: 120px;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    background: #fff;

    &[data-input-type='required'] {
      border: 1px solid #ffbc00;
      background: #fffbdf;
    }
  }

  &-add-input-number {
    width: 120px;
    border-radius: 2px;
    border: 1px solid #e0e0e0;

    &:hover {
      border-color: #4fd2db;
    }

    :global {
      .c7n-input-wrapper.c7n-input-has-suffix.c7n-input-has-border {
        top: 0;

        &:before {
          border: none !important;
        }

        .c7n-input-content {
          display: block;

          input {
            height: 20px;
            line-height: 20px;
          }
        }

        .c7n-input-suffix > .c7n-input-number-handler-wrap {
          right: 2px;

          i {
            font-size: 16px;
          }
        }
      }
    }
  }

  &-add-input-number-required {
    border: 1px solid #ffbc00;
    background: #fffbdf;

    :global {
      .c7n-input-suffix > .c7n-input-number-handler-wrap {
        background: #fffbdf;
      }
    }
  }

  &-add-select {
    width: 120px;
    border-radius: 2px;
    border: 1px solid #e0e0e0;

    &:before {
      border: none !important;
    }

    &:hover {
      border-color: #4fd2db;
    }

    :global {
      .c7n-select-selection__rendered {
        height: 20px;
        line-height: 20px;
        border: none;

        .c7n-select-arrow {
          color: #333;
        }
      }
    }

    // &[data-select-type='required'] {
    //   border: 1px solid #ffbc00;
    //   background: #fffbdf;
    // }
  }

  &-add-select-required {
    border: 1px solid #ffbc00;
    background: #fffbdf;

    &:hover {
      border-color: #ffbc00;
    }
  }

  &-add-upload {
    width: 50px;
    height: 50px;
    line-height: 44px;
    display: block;
    border: 1px dashed #e0e0e0;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    padding-top: 0;

    i {
      color: #b5b5b5;
    }

    &:hover {
      border-color: #4fd2db;

      i {
        color: #4fd2db;
      }
    }
  }

  &-add-upload-required {
    border: 1px solid #ffbc00;
    background: #fffbdf;

    i {
      color: #ffbc00;
    }

    &:hover {
      border-color: #ffbc00;

      i {
        color: #ffbc00;
      }
    }
  }

  &-add-tag {
    border-style: dashed;
    background: #fff;
    color: #d9d9d9;
    width: 22px;
    text-align: center;
    padding: 0;

    &:hover {
      border: 1px dashed #29bece;
      color: #29bece;
    }

    :global(.icon-add) {
      margin-top: -4px !important;
    }
  }
}

.hcm-row-header-wrapper {
  display: flex;
  align-items: center;
  font-size: 12px;

  i {
    color: #333;
    margin-right: 8px;
    opacity: 0.65;
  }

  .hcm-row-header-title {
    color: #333;
    width: 240px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 16px;
  }

  .hcm-row-header-item-info {
    color: #666;
    min-width: 80px;
    margin-right: 16px;
  }
}
