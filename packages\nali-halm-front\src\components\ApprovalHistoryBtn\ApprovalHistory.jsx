/*
 * @Description:
 * @Author: DCY <<EMAIL>>
 * @Date: 2022-04-07 14:53:56
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2021, Hand
 */
import React, { Component } from 'react';
import { DataSet, Tooltip, Spin } from 'choerodon-ui/pro';
import { Col, Row, Timeline } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';

import { approvalHistoryDS } from './Stores';
import styles from './index.module.less';

class ApprovalHistory extends Component {
  constructor(props) {
    super(props);
    this.approvalHistoryDs = new DataSet(approvalHistoryDS());
    this.state = {
      data: [],
    };
  }

  componentDidMount() {
    this.initData();
  }

  @Bind()
  async initData() {
    this.approvalHistoryDs.setQueryParameter('instanceId', this?.props?.instanceId);
    await this.approvalHistoryDs.query();
    this.setState({
      data: this.approvalHistoryDs.toData(),
    });
  }

  /**
   * 处理字段变更值：多选情况下需要对返回值做特殊处理
   * @param value
   * @param item
   * @returns {*}
   */
  @Bind()
  handleFieldValue(value, item) {
    if (['MULTI_SELECT', 'MULTI_LOV'].includes(item.fieldTypeCode) && !!value) {
      return JSON.parse(value).join(',');
    }
    return value;
  }

  @Bind()
  renderItem(data) {
    const { statusMeaning, assignee, endDate, commentContent } = data;
    return (
      <Timeline.Item>
        <Row className={styles['change-logs-box-title']}>
          <Col span={8} className={styles['label-style']}>
            <Tooltip title={statusMeaning}>{statusMeaning}</Tooltip>
          </Col>
        </Row>
        <Row className={styles['change-logs-line']}>
          <Col span={8}>{assignee || '-'}</Col>
          <Col>{endDate}</Col>
        </Row>
        <Row className={styles['change-logs-line']}>
          <Col>{commentContent}</Col>
        </Row>
      </Timeline.Item>
    );
  }

  render() {
    const { data } = this.state;
    return (
      <React.Fragment>
        <Spin dataSet={this.approvalHistoryDs}>
          <Timeline className={styles['change-logs-modal']}>
            {data.map(i => this.renderItem(i))}
          </Timeline>
        </Spin>
      </React.Fragment>
    );
  }
}

export default ApprovalHistory;
