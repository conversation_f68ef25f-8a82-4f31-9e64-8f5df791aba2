import React, { useState, useMemo, useEffect } from 'react';
import {
  DataSet,
  Table,
  Button,
  Row,
  Col,
  TextField,
  Form,
  Icon,
  Lov,
  DateTimePicker,
  Select,
} from 'choerodon-ui/pro';
// import notification from 'utils/notification';
import { closeTab } from 'utils/menuTab';

import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { tableDS } from './stores';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.ProductQualityInspectionRecord';

const ProductQualityInspectionRecord = observer(props => {
  const { history } = props;
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds

  useEffect(() => {
    const queryData = props?.location?.state;
    if (queryData) {
      tableDs.queryDataSet.create({
        identificationstr: queryData.identificationstr || '',
        materialId: queryData.materialId || '',
        materialCode: queryData.materialCode || '',
        qualityStatus: queryData.qualityStatus || '',
        workOrderNumstr: queryData.workOrderNumstr || '',
        prodLineId: queryData.prodLineId || '',
        prodLineCode: queryData.prodLineCode || '',
        startTime: queryData.startTime || '',
        endTime: queryData.endTime || '',
      });
      tableDs.query();
    }
  }, []);

  const goDisoseDetail = record => {
    const { data } = record;
    history.push({
      pathname: `/hmes/product-quality-inspection-record/detail/${record.data.materialLotId}`,
      state: {
        data,
        queryList: tableDs.queryDataSet.current.toData(),
      },
    });
  };
  const goTestDetail = record => {
    closeTab('/hmes/inspection/inspection-management/list');
    const { data } = record;
    history.push({
      pathname: '/hmes/inspection/inspection-management/list',
      state: {
        data,
      },
    });
  };
  const goNcDetail = record => {
    closeTab('/hmes/nc-query-record');
    const { data } = record;
    history.push({
      pathname: '/hmes/nc-query-record',
      state: {
        data,
      },
    });
  };

  const columns = [
    {
      header: intl.get(`${modelPrompt}.diposalRecord`).d('处置记录'),
      align: 'center',
      lock: 'left',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => {
                goDisoseDetail(record);
              }}
            >
              {intl.get(`${modelPrompt}.detail`).d('明细')}
            </a>
          </span>
        );
      },
    },
    {
      header: intl.get(`${modelPrompt}.inspectRecord`).d('检验记录'),
      align: 'center',
      lock: 'left',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => {
                goTestDetail(record);
              }}
            >
              {intl.get(`${modelPrompt}.detail`).d('明细')}
            </a>
          </span>
        );
      },
    },
    {
      header: intl.get(`${modelPrompt}.ncDetail`).d('不良明细'),
      align: 'center',
      lock: 'left',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => {
                goNcDetail(record);
              }}
            >
              {intl.get(`${modelPrompt}.detail`).d('明细')}
            </a>
          </span>
        );
      },
    },
    {
      name: 'siteCode',
    },
    {
      name: 'identification',
      width: 200,
    },
    {
      name: 'eoNum',
      width: 200,
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'qualityStatusDesc',
      width: 200,
    },
    {
      name: 'workOrderNum',
      width: 170,
    },
    {
      name: 'prodLineName',
    },
  ];
  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identificationstr"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'identificationstr', '条码号', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <Lov name="materialLov" />
              <Select name="qualityStatus" />
              {expandForm && (
                <>
                  <TextField
                    name="workOrderNumstr"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          onClick={() =>
                            onOpenInputModal(true, 'workOrderNumstr', '生产指令', queryDataSet)
                          }
                        />
                      </div>
                    }
                  />
                  <Lov name="prodLineLov" />
                  {/* <DateTimePicker name="startTime" />
                  <DateTimePicker name="endTime" /> */}
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                funcType="link"
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  queryDataSet.create();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleSearch = async () => {
    // const {
    // identificationstr,
    //   materialId,
    //   qualityStatus,
    //   workOrderNumstr,
    //   prodLineId,
    //   startTime,
    //   endTime,
    // } = tableDs?.queryDataSet?.toJSONData()[0];
    // if (
    //   !identificationstr &&
    //   !materialId &&
    //   !qualityStatus &&
    //   !workOrderNumstr &&
    //   !prodLineId &&
    //   !startTime &&
    //   !endTime
    // ) {
    //   notification.error({
    //     message: '请至少输入一个查询条件',
    //   });
    //   return;
    // }
    tableDs.query();
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleSearch();
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('产品质检记录')}></Header>
      <Content>
        <Table
          searchCode="ProductQualityInspectionRecord"
          customizedCode="ProductQualityInspectionRecord"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.ProductQualityInspectionRecord', 'tarzan.common'],
})(ProductQualityInspectionRecord);
