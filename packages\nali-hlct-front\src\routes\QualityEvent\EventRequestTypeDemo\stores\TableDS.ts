/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2022-10-27 14:41:39
 * @LastEditTime: 2022-11-09 14:34:27
 * @LastEditors: <<EMAIL>>
 */
import { FieldFormat, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.event.requestType.model.requestType';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'materialCategorySiteId',
  queryFields: [
    {
      name: 'requestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('事件请求类型编码'),
      format: FieldFormat.uppercase,
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('事件请求类型描述'),
    },
  ],
  fields: [
    {
      name: 'requestTypeId',
      type: FieldType.number,
    },
    {
      name: 'requestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('事件请求类型编码'),
      required: true,
      format: FieldFormat.uppercase,
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.description`).d('事件请求类型描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: { tenantId },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-event-request-type/query/ui`,
        method: 'GET',
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.mes.domain.entity.MtEventRequestType';
      return {
        data: { requestTypeId: record.get('requestTypeId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_SAMPLING}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
});

export { tableDS };
