/**
 * @Description: 分层审核计划-详情
 * @Author: <<EMAIL>>
 * @Date: 2023-08-16 11:31:24
 * @LastEditTime: 2023-08-16 11:31:24
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import {
  DataSet,
  Button,
  Form,
  Lov,
  DatePicker,
  Table,
  Modal,
  Select,
  TextField,
  MonthPicker,
  Spin,
} from 'choerodon-ui/pro';

import ApprovalInfoDrawer from '@/components/ApprovalInfoDrawer';
import { Collapse, Popconfirm, Row, Col, Popover } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import uuid from 'uuid/v4';
import moment from 'moment';

import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import styles from '../index.modules.less';

import { formDS, detailWeekTableDS, operationNumDS, timeDS, tableItemDS } from '../stores/DetailDS';

import {
  fetchReviewPlanConfig,
  saveReviewPlanConfig,
  updateReviewPlanConfig,
  submitReviewPlanConfig,
} from '../services';

const { Panel } = Collapse;

const modelPrompt = 'tarzan.hierarchicalAuditPlan';

const HierarchicalAuditPlan = props => {
  const {
    match: {
      path,
      params: { id },
    },
    history,
  } = props;

  // pub路由标识
  const pubFlag = useMemo(() => path.startsWith('/pub'), [path]);
  // 编辑状态
  const [canEdit, setCanEdit] = useState(false);

  const [colHeaderList, setColHeaderList]: any = useState([]);

  const [detailWeekTableDs, setDetailWeekTableDs] = useState(
    new DataSet({
      ...detailWeekTableDS(),
    }),
  );

  // 创建头信息
  const formDs = useMemo(() => new DataSet({ ...formDS() }), []);
  // 工艺信息
  const operationNumDs = useMemo(() => new DataSet({ ...operationNumDS() }), []);
  // 创建列modal
  const timeDs = useMemo(() => new DataSet({ ...timeDS() }), []);

  // 创建编辑表格项
  const tableItemDs = useMemo(() => new DataSet({ ...tableItemDS() }), []);

  // 查询审核计划明细信息
  const fetchReviewPlan = useRequest(fetchReviewPlanConfig(), {
    manual: true,
    needPromise: true,
  });
  // 保存审核计划明细信息
  const saveReviewPlan = useRequest(saveReviewPlanConfig(), {
    manual: true,
    needPromise: true,
  });
  // 更新审核计划明细信息
  const updateReviewPlan = useRequest(updateReviewPlanConfig(), {
    manual: true,
    needPromise: true,
  });
  // 提交审核计划
  const submitReviewPlan = useRequest(submitReviewPlanConfig(), {
    manual: true,
  });

  // 初始化页面
  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      const _detailWeekTableDs = new DataSet({
        ...detailWeekTableDS(),
      });
      formDs.loadData([
        {
          layerReviewPlanStatus: 'NEW',
        },
      ]);
      operationNumDs.loadData([]);
      setColHeaderList([]);
      _detailWeekTableDs.loadData([]);
      setDetailWeekTableDs(_detailWeekTableDs);
    } else {
      initPageData(id);
    }
  }, [id]);

  // 初始化页面
  const initPageData = async key => {
    setCanEdit(false);
    getAuditInformation(key);
  };

  const addRow = () => {
    detailWeekTableDs.create({ uuid: uuid() });
  };
  const deleteRow = record => {
    detailWeekTableDs.remove([record]);
  };

  const addCol = () => {
    timeDs.reset();
    Modal.open({
      destroyOnClose: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.title.addCol`).d('请选择周开始/结束时间'),
      className: 'hmes-style-modal',
      style: {
        width: '360px',
      },
      children: (
        <Form dataSet={timeDs} columns={1} labelWidth={112}>
          <DatePicker name="beginDate" />
          <DatePicker name="endDate" />
        </Form>
      ),
      onOk: async () => {
        const timeValidate = await timeDs.validate();
        if (!timeValidate) {
          return false;
        }
        let beginDate = timeDs.current?.get('beginDate');
        let endDate = timeDs.current?.get('endDate');
        beginDate = beginDate.format('yyyy-MM-DD');
        endDate = endDate.format('yyyy-MM-DD');
        detailWeekTableDs.addField(`qisLayerReviewWeekListItem${colHeaderList.length}`, {
          type: FieldType.object,
        });
        setColHeaderList([
          ...colHeaderList,
          {
            showFlag: true,
            beginDate,
            endDate,
            layerReviewPlanId: undefined,
            layerReviewWeekId: undefined,
          },
        ]);
      },
    });
  };
  const deleteCol = colRecord => {
    const deleteIndex = (colRecord.name || '').split('qisLayerReviewWeekListItem')[1];
    const _colHeaderList: any = [];
    colHeaderList.forEach((item, index) => {
      const _item = { ...item };
      if (`${index}` === `${deleteIndex}`) {
        _item.showFlag = false;
      }
      _colHeaderList.push(_item);
    });
    detailWeekTableDs.forEach(record => {
      record.set(colRecord.name, null);
    });
    setColHeaderList(_colHeaderList);
  };

  const addItemRow = () => {
    tableItemDs.create({ uuid: uuid() });
  };
  const deleteItemRow = record => {
    tableItemDs.remove(record);
  };

  const itemColumn: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          funcType="flat"
          shape="circle"
          size="small"
          onClick={addItemRow}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      hidden: !canEdit,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteItemRow(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit}
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    { name: 'operationLov', editor: canEdit },
    { name: 'taskCreationDate', editor: canEdit },
  ];

  const rowItemEdit = (record, name) => {
    let recordData = record.toData() || {};
    recordData = recordData[name] || [];
    tableItemDs.loadData(recordData);

    timeDs.reset();
    Modal.open({
      destroyOnClose: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.title.edit`).d('编辑工艺/创建时间'),
      className: 'hmes-style-modal',
      drawer: true,
      style: {
        width: '720px',
      },
      children: <Table dataSet={tableItemDs} columns={itemColumn} />,
      onOk: async () => {
        const tableItemValidate = await tableItemDs.validate();
        if (!tableItemValidate) {
          return false;
        }
        record.set(name, tableItemDs.toData());
      },
    });
  };

  const getAuditInformation = async key => {
    const _detailWeekTableDs = new DataSet({
      ...detailWeekTableDS(),
    });
    if (key) {
      const _res = await fetchReviewPlan.run({
        tempUrl: fetchReviewPlanConfig(key).url,
      });

      if (_res?.success) {
        const _colHeaderList: any = [];
        const { qisLayerReviewGroupList, operationNumDTOList, ...other } = _res.rows;
        other.planDate = `${other.planYear}-${other.planMonth}`;
        formDs.loadData([other]);
        operationNumDs.loadData(operationNumDTOList);
        const newqisLayerReviewGroupList: any = [];
        (qisLayerReviewGroupList || []).forEach(item => {
          const { qisLayerReviewWeekList, ...other } = item;
          (qisLayerReviewWeekList || []).forEach((subItem, index) => {
            other[`qisLayerReviewWeekListItem${index}`] = [
              ...(subItem.qisLayerReviewPlandtlList || []),
            ];
            if (!_colHeaderList[index]) {
              _colHeaderList.push({
                showFlag: true,
                beginDate: subItem.beginDate,
                endDate: subItem.endDate,
                layerReviewPlanId: subItem.layerReviewPlanId,
                layerReviewWeekId: subItem.layerReviewWeekId,
              });
            } else {
              if (!_colHeaderList[index].beginDate) {
                _colHeaderList[index].beginDate = subItem.beginDate;
              }
              if (!_colHeaderList[index].endDate) {
                _colHeaderList[index].endDate = subItem.endDate;
              }
            }
          });
          let employeeList: any = [];
          if (typeof other.groupMember === 'string') {
            try {
              employeeList = [...(JSON.parse(other.groupMember)?.groupMember || [])];
            } catch (e) {
              // 尝试删除多余属性
            }
          }
          let leaderLov = {};
          const membersLov: any = [];

          employeeList.forEach(employeeItem => {
            if (employeeItem.leaderFlag) {
              leaderLov = {
                ...employeeItem,
              };
            } else {
              membersLov.push(employeeItem);
            }
          });

          other.leaderLov = leaderLov;
          other.membersLov = membersLov;
          newqisLayerReviewGroupList.push(other);
        });
        setColHeaderList(_colHeaderList);
        _detailWeekTableDs.loadData(newqisLayerReviewGroupList);
      } else {
        formDs.loadData([]);
        operationNumDs.loadData([]);
        setColHeaderList([]);
        _detailWeekTableDs.loadData([]);
      }
      setDetailWeekTableDs(_detailWeekTableDs);
    } else {
      formDs.loadData([]);
      operationNumDs.loadData([]);
      setColHeaderList([]);
      _detailWeekTableDs.loadData([]);
      setDetailWeekTableDs(_detailWeekTableDs);
    }
  };

  const handleSave = async () => {
    let needUpdate = false;
    const validateFormResult = await formDs.validate();
    const validateTableResult = await detailWeekTableDs.validate();
    if (!validateFormResult || !validateTableResult) {
      return false;
    }
    if (detailWeekTableDs.length === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.add.group`).d('请添加小组'),
      });
      return false;
    }
    let emptyItem = false;
    const qisLayerReviewGroupList: any = [];
    formDs.forEach(record => {
      if (record.status !== 'sync') {
        needUpdate = true;
      }
    });
    detailWeekTableDs.forEach(record => {
      if (record.status !== 'sync') {
        needUpdate = true;
      }
      const { leaderLov, membersLov, layerReviewGroupId, layerReviewPlanId } =
        record.toData() || {};

      const qisLayerReviewWeekList: any = [];
      colHeaderList.forEach((colItem, index) => {
        const _name = `qisLayerReviewWeekListItem${index}`;
        const recordData = record.get(_name);
        if (colItem.showFlag) {
          if (!recordData || recordData.length === 0) {
            emptyItem = true;
          }
          qisLayerReviewWeekList.push({
            beginDate: moment(colItem.beginDate).format('yyyy-MM-DD'),
            endDate: moment(colItem.endDate).format('yyyy-MM-DD'),
            layerReviewPlanId: colItem.layerReviewPlanId,
            layerReviewWeekId: colItem.layerReviewWeekId,
            qisLayerReviewPlandtlList: recordData,
          });
        }
      });
      const employeeList = [
        {
          ...leaderLov,
          leaderFlag: true,
        },
        ...membersLov,
      ];

      qisLayerReviewGroupList.push({
        groupMember: JSON.stringify({ groupMember: employeeList }),
        layerReviewGroupId,
        layerReviewPlanId,
        qisLayerReviewWeekList,
      });
    });

    if (emptyItem) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.add.operationAndTime`).d('请添工艺和时间'),
      });
      return false;
    }

    const data: any = formDs.toData()[0];
    if (data.planDate) {
      data.planYear = moment(data.planDate).format('yyyy');
    }
    if (data.planDate) {
      data.planMonth = moment(data.planDate).format('MM');
    }

    data.qisLayerReviewGroupList = qisLayerReviewGroupList;

    if (!needUpdate) {
      initPageData(id);
      return;
    }

    let res;
    if (id === 'create') {
      res = await saveReviewPlan.run({
        params: data,
      });
    } else {
      res = await updateReviewPlan.run({
        params: data,
      });
    }

    if (res?.success) {
      notification.success({});
      if (id === 'create') {
        history.push(
          `/hwms/hierarchical-audit-plan/audit-plan/detail/${res.rows?.layerReviewPlanId}`,
        );
      } else {
        initPageData(id);
      }
    }
  };

  const handleSubmit = () => {
    return submitReviewPlan.run({
      params: id,
      onSuccess: () => {
        notification.success({});
        initPageData(id);
      },
    });
  };
  const handleCancel = () => {
    setCanEdit(prev => !prev);
    if (id === 'create') {
      history.push(`/hwms/hierarchical-audit-plan/audit-plan/list`);
    } else {
      initPageData(id);
    }
  };

  const createOtherColumns = () => {
    const otherColumns: ColumnProps[] = [];
    let weekindex = 0;
    colHeaderList.forEach((item, index) => {
      if (item.showFlag) {
        weekindex++;
        otherColumns.push({
          name: `qisLayerReviewWeekListItem${index}`,
          title: `${intl.get(`${modelPrompt}.number.squence`).d('第')}${weekindex}${intl.get(`${modelPrompt}.number.week`).d('周')}`,
          hidden: !item.showFlag,
          width: 400,
          align: ColumnAlign.center,
          header: (record: any) => {
            return (
              <div>
                <div>
                  {record?.title}
                  <Popconfirm
                    title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                    onConfirm={() => deleteCol(record)}
                    okText={intl.get('tarzan.common.button.confirm').d('确认')}
                    cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                  >
                    <PermissionButton
                      type="c7n-pro"
                      icon="remove"
                      disabled={!canEdit}
                      funcType="flat"
                      shape="circle"
                      size="small"
                    />
                  </Popconfirm>
                  <br />
                  {`${moment(item.beginDate).format('yyyy-MM-DD')} - ${moment(item.endDate).format(
                    'yyyy-MM-DD',
                  )}`}
                </div>
              </div>
            );
          },
          renderer: ({ record }) => {
            const _list = record?.get(`qisLayerReviewWeekListItem${index}`) || [];
            if (canEdit) {
              if (_list.length > 0) {
                return _list.map(item => {
                  return (
                    <Row key={item.uuid}>
                      <Col span={12}>
                        <Button
                          onClick={() => {
                            rowItemEdit(record, `qisLayerReviewWeekListItem${index}`);
                          }}
                          funcType={FuncType.link}
                        >
                          {item.operationName}
                        </Button>
                      </Col>
                      <Col span={12}>
                        <Button
                          onClick={() => {
                            rowItemEdit(record, `qisLayerReviewWeekListItem${index}`);
                          }}
                          funcType={FuncType.link}
                        >
                          {item.taskCreationDate}
                        </Button>
                      </Col>
                    </Row>
                  );
                });
              }
              return (
                <Button
                  onClick={() => {
                    rowItemEdit(record, `qisLayerReviewWeekListItem${index}`);
                  }}
                  funcType={FuncType.link}
                >
                  编辑
                </Button>
              );
            }
            return _list.map(item => {
              return (
                <Row key={item.uuid}>
                  <Col span={12}>{item.operationName}</Col>
                  <Col span={12}>
                    <Popover content={intl.get(`${modelPrompt}.taskCreateDate`).d('任务创建日期')}>{item.taskCreationDate}</Popover>
                  </Col>
                </Row>
              );
            });
          },
        });
      }
    });
    return otherColumns;
  };

  const detailWeekTableColumns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          funcType="flat"
          shape="circle"
          size="small"
          onClick={addRow}
        />
      ),
      name: 'add',
      align: ColumnAlign.center,
      width: 80,
      hidden: !canEdit,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRow(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit}
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'layerReviewGroupId',
      title: intl.get(`${modelPrompt}.groupWeek`).d('小组/周次'),
      children: [
        {
          name: `leaderLov`,
          title: intl.get(`${modelPrompt}.column.leader`).d('组长'),
          width: 200,
          editor: canEdit,
        },
        {
          name: `membersLov`,
          title: intl.get(`${modelPrompt}.column.groupmen`).d('组员'),
          width: 200,
          editor: canEdit,
        },
      ],
    },
    ...createOtherColumns(),
  ];
  const operationNumColumns: ColumnProps[] = [
    {
      name: `operationName`,
      width: 200,
    },
    {
      name: `num`,
      width: 200,
    },
  ];

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.HierarchicalAuditPlan`).d('分层审核计划')}
        backPath={pubFlag ? '' : '/hwms/hierarchical-audit-plan/audit-plan/list'}
      >
        {canEdit && !pubFlag && (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && !pubFlag && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              onClick={() => {
                setCanEdit(prev => !prev);
              }}
              disabled={!['NEW', 'REJECTED'].includes(formDs.current?.get('layerReviewPlanStatus'))}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              onClick={handleSubmit}
              disabled={!['NEW', 'REJECTED'].includes(formDs.current?.get('layerReviewPlanStatus'))}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.submit`).d('提交')}
            </PermissionButton>
            <ApprovalInfoDrawer objectTypeList={['QIS_LAYER_REVIEW_PLAN']} objectId={id} />
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={fetchReviewPlan.loading}>
          <Collapse collapsible="icon" bordered={false} defaultActiveKey={['panel1', 'panel2']}>
            <Panel
              header={intl
                .get(`${modelPrompt}.hierarchicalAuditPlanInformation`)
                .d('分层审核计划信息')}
              key="panel1"
            >
              <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
                <TextField name="layerReviewPlanCode" />
                <Lov name="siteLov" />
                <Select name="layerReviewPlanStatus" />
                <MonthPicker name="planDate" />
              </Form>
              <div className={styles['table-conrtal-box']}>
                <div className="table-box">
                  <Table
                    headerRowHeight="auto"
                    rowHeight="auto"
                    autoWidth
                    border
                    dataSet={detailWeekTableDs}
                    columns={detailWeekTableColumns}
                  />
                </div>
                {canEdit && (
                  <div className="add-button">
                    <PermissionButton
                      type="c7n-pro"
                      icon="add"
                      funcType="flat"
                      shape="circle"
                      size="small"
                      onClick={addCol}
                    />
                  </div>
                )}
              </div>
            </Panel>
            {!canEdit && (
              <Panel
                header={intl
                  .get(`${modelPrompt}.statisticsOfProcessPreAuditTimes`)
                  .d('工艺预审核次数统计')}
                key="panel2"
              >
                <Table autoWidth border dataSet={operationNumDs} columns={operationNumColumns} />
              </Panel>
            )}
          </Collapse>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(HierarchicalAuditPlan);
