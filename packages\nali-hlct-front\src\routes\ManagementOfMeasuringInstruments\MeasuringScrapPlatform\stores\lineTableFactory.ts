import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.qms.MeasuringScrapPlatform';
const tenantId = getCurrentOrganizationId();

// const endUrl = '-138685';
const endUrl = '';

const lineTableFactory = () =>
  new DataSet({
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    paging: false,
    fields: [
      {
        name: 'toolCodeObjs',
        label: intl.get(`${modelPrompt}.table.toolCodeObjs`).d('量具编号'),
        type: FieldType.object,
        ignore: FieldIgnore.always,
        multiple: true,
        lovCode: 'YP.QIS.MS_TOOL_RESPON_AND_STATUS_LIMIT_LOV',
        dynamicProps: {
          lovPara: ({ dataSet }) => ({
            siteId: dataSet.getState('siteId'),
            tenantId: getCurrentOrganizationId(),
          }),
        },
      },
      {
        name: 'toolCodeObj',
        label: intl.get(`${modelPrompt}.table.toolCodeObj`).d('量具编号'),
        type: FieldType.object,
        ignore: FieldIgnore.always,
        required: true,
        lovCode: 'YP.QIS.MS_TOOL_RESPON_AND_STATUS_LIMIT_LOV',
        dynamicProps: {
          lovPara: ({ dataSet }) => ({
            siteId: dataSet.getState('siteId'),
            tenantId: getCurrentOrganizationId(),
          }),
        },
      },
      {
        name: 'msToolManageId',
        bind: 'toolCodeObj.msToolManageId',
      },
      {
        name: 'msToolManageCode',
        bind: 'toolCodeObj.toolCode',
      },
      {
        name: 'speciesName',
        label: intl.get(`${modelPrompt}.table.speciesName`).d('种别描述'),
        type: FieldType.string,
        bind: 'toolCodeObj.speciesName',
      },
      {
        name: 'modelCode',
        label: intl.get(`${modelPrompt}.table.modelCode`).d('型号编码'),
        type: FieldType.string,
        bind: 'toolCodeObj.modelCode',
      },
      {
        name: 'modelName',
        label: intl.get(`${modelPrompt}.table.modelName`).d('型号描述'),
        type: FieldType.string,
        bind: 'toolCodeObj.modelName',
      },
      {
        name: 'lastVerificationDate',
        label: intl.get(`${modelPrompt}.table.lastVerificationDate`).d('上次检定日期'),
        type: FieldType.string,
        bind: 'toolCodeObj.lastVerificationDate',
      },
      {
        name: 'usingStatus',
        label: intl.get(`${modelPrompt}.table.usingStatus`).d('量具使用状态'),
        lookupCode: 'QIS.MS_TOOL_USING_STATUS',
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
        type: FieldType.string,
        bind: 'toolCodeObj.usingStatus',
      },
      {
        name: 'verificationStatus',
        lookupCode: 'QIS.MS_TOOL_VRFCT_STATUS',
        label: intl.get(`${modelPrompt}.table.verificationStatus`).d('检定状态'),
        type: FieldType.string,
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
        bind: 'toolCodeObj.verificationStatus',
      },
      {
        name: 'currentUserId',
        label: intl.get(`${modelPrompt}.table.currentUserName`).d('使用人'),
        bind: 'toolCodeObj.userId',
      },
      {
        name: 'currentUserName',
        bind: 'toolCodeObj.userName',
      },
      {
        name: 'currentUseDepartmentName',
        label: intl.get(`${modelPrompt}.table.currentUseDepartmentName`).d('使用部门'),
        type: FieldType.string,
        bind: 'toolCodeObj.usingDepartmentName',
      },
      {
        name: 'currentUseDepartmentId',
        type: FieldType.string,
        bind: 'toolCodeObj.usingDepartmentId',
      },
      {
        name: 'currentProdlineName',
        label: intl.get(`${modelPrompt}.table.currentProdlineName`).d('产线'),
        type: FieldType.string,
        bind: 'toolCodeObj.prodLineName',
      },
      {
        name: 'currentProdlineId',
        bind: 'toolCodeObj.prodLineId',
      },
      {
        name: 'currentProcessName',
        label: intl.get(`${modelPrompt}.table.currentProcessName`).d('工序'),
        type: FieldType.string,
        bind: 'toolCodeObj.processName',
      },
      {
        name: 'currentProcessId',
        bind: 'toolCodeObj.processId',
      },
      {
        name: 'otherPosition',
        label: intl.get(`${modelPrompt}.table.otherPosition`).d('其他位置信息'),
        type: FieldType.string,
        bind: 'toolCodeObj.otherPosition',
        lookupCode: 'YP.QIS.MANAGE_OTHER_POSITION',
        lovPara: { tenantId },
      },
      {
        name: 'reason',
        label: intl.get(`${modelPrompt}.table.reason`).d('注销原因'),
        type: FieldType.string,
        required: true,
      },
      {
        name: 'enclosureUuid',
        label: intl.get(`${modelPrompt}.table.enclosureUuid`).d('附件'),
        type: FieldType.attachment,
        bucketName: 'qms',
        bucketDirectory: 'measuring-scrap-platform',
        required: true,
      },
    ],
    transport: {
      destroy: ({ data }) => {
        return {
          data,
          url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-line/remove/ui`,
          method: 'DELETE',
        };
      },
    },
  });

export default lineTableFactory;
