import React, { Fragment, useEffect, useState, useRef } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import {
  DataSet,
  Table,
  Button,
  notification,
  Form,
  // Spin,
  Modal,
  Lov,
  TextField,
  Select,
  // Icon,
  NumberField,
  DateTimePicker,
  TextArea,
  Switch,

} from 'choerodon-ui/pro';
import { Collapse, Tabs, Tag, Popconfirm } from 'choerodon-ui';
import { Icon, Tree } from 'hzero-ui';
import { TableMode } from 'choerodon-ui/pro/lib/table/enum';
// import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import request from 'utils/request';
// import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { C7nFormItemSort, AttributeDrawer } from '@components/tarzan-ui';
import {
  // tableDS,
  formDS,
  materialDS,
  materialCreateDS,
  detailTableDS,
  // dataCollectionGroupDS,
  dataCollectionGroupCreateDS,
  routerHeadDS,
} from './stores/TableDS';
import {
  // copyDrawerDS,
  bomHeadDS,
  lineDS,
  referencePointDrawerDS,
  // siteListDS,
  substituteDrawerDS,
} from './stores/DetailDS';
import { stepOptionDS } from './stores/CommonDS';
// import { childStepDrawerDS } from './stores/ChildStepDrawerDS';
import NewStepDrawer from './Components/NewStepDrawer';
import OperationComponentDrawer from './Components/OperationComponentDrawer';
import { ComponentLineTable } from './ComponentLineTable';
import ComponentDistributionDrawer from './Components/ComponentDistributionDrawer';
import TagGroupTree from './Components/TagGroupTree';
// import RouterLineTable from './RouterLineTable';
// import './index.less';

const tenantId = getCurrentOrganizationId();
const Host = BASIC.HMES_BASIC;
// const Host = '/key-focus-mes-33275';
const serveCode = BASIC.TARZAN_METHOD;
const { Panel } = Collapse;
// const { Column } = Table;
const { TreeNode } = Tree;
let changeCheckData;
let changeCheckId;
const selectedRouterType = []
const modelPrompt = 'tarzan.workshop.MachineProductionDispatch';

const MachineProductionDispatch = withProps(
  () => {
    // const dataCollectionGroupDs = new DataSet({ ...dataCollectionGroupDS() });
    const dataCollectionGroupCreateDs = new DataSet({ ...dataCollectionGroupCreateDS() });

    // const dataSet = new DataSet({ ...tableDS() });
    const formDs = new DataSet({ ...formDS() });

    const materialDs = new DataSet({ ...materialDS() });
    const materialCreateDs = new DataSet({ ...materialCreateDS() });

    const routerHeadDs = new DataSet({ ...routerHeadDS() });
    const detailTableDs = new DataSet({ ...detailTableDS() });

    const stepOptionDs = new DataSet({ ...stepOptionDS() });
    // const childStepDrawerDs = new DataSet({ ...childStepDrawerDS() });

    const lineDs = new DataSet({
      ...lineDS(),
      children: {
        mtBomSubstituteList: substituteDrawerDs,
        mtBomReferencePointList: referencePointDrawerDs,
      },
    });
    const bomHeadDs = new DataSet({
      ...bomHeadDS(),
      children: {
        mtBomComponentList: lineDs,
      },
    });
    const substituteDrawerDs = new DataSet({ ...substituteDrawerDS() });
    const referencePointDrawerDs = new DataSet({ ...referencePointDrawerDS() });

    return {
      // dataSet,
      formDs,
      materialDs,
      materialCreateDs,
      detailTableDs,
      stepOptionDs,
      // childStepDrawerDs,
      lineDs,
      substituteDrawerDs,
      referencePointDrawerDs,
      bomHeadDs,
      // dataCollectionGroupDs,
      dataCollectionGroupCreateDs,
      routerHeadDs,
    };
  },
  { cacheState: true },
)(props => {
  const {
    match: { path },
    // history,
    formDs,
    materialDs,
    materialCreateDs,
    detailTableDs,
    stepOptionDs,

    lineDs,
    substituteDrawerDs,
    referencePointDrawerDs,
    bomHeadDs,
    // dataCollectionGroupDs,
    dataCollectionGroupCreateDs,
    routerHeadDs,
  } = props;

  // const [loading, setLoading] = useState(false);
  const [tabKey, setTabKey] = useState('materialInfo');

  const [materialList, setMaterialList] = useState([]); // 物料批列表数据
  const [siteIds, setSiteIds] = useState([]); // 用于存放站点id

  const [stepsList, setStepsList] = useState([]);
  // const [selectedRouterType, setSelectedRouterType] = useState([]);
  const [stepOption, setStepOption] = useState([]);

  const [compList, setCompList] = useState([]); // 组件列表
  const [currentBomId, setCurrentBomId] = useState('');
  const [routerId, setRouterId] = useState('');

  const [canEdit, setCanEdit] = useState(false); // 装配清单是否可编辑
  const [bomSaveFlag, setBomSaveFlag] = useState(false); // 装配清单是否保存
  const [bomTab, setBomTab] = useState(false); // 装配清单页签是否可点击

  const [routerTab, setRouterTab] = useState(false); // 装配清单页签是否可点击
  const [canCreate, setCanCreate] = useState(false); // 是否创建
  const [editStatus, setEditStatus] = useState(false); // 工艺路线是否可编辑

  const [routerSaveFlag, setRouterSaveFlag] = useState(false); // 工艺路线是否保存

  const [forceFlag, setForceFlag] = useState(false); // 强制工艺路线开关

  const [currentBomComponentId, setBomComponentId] = useState(0);
  const [selectedTagList, setSelectedTagList] = useState([]); // 数据收集组勾选
  const [checkedList, setCheckedList] = useState([]);
  // const [selectedList, setSelectedList] = useState([]);

  // const [currentTagGroup, setCurrentTagGroup] = useState({}); // 当前打开的工艺路线数据收集组
  const [siteInfo, setSiteInfo] = useState({}); // 站点存储信息
  const [materialInfo, setMaterialInfo] = useState({}); // 物料存储信息
  // 步骤抽屉的ref
  const newStepDrawerRef = useRef();
  // 分配对象的ref
  const openComponentDistributionDrawerRef = useRef();

  // 数据收集组抽屉
  const tagGroupDrawerRef = useRef();
  const checkRef = useRef(); // table的ref

  // 工艺步骤抽屉用的数据源
  let tableList = []; // 所有工艺类型数据
  let currentDataSource = []; // 当前数据源

  /**
   * 定义弹窗(好几个抽屉公用
   */
  let _Modal;

  /**
   * 定义弹窗(数据收集组新建)
   */
  let groupModal;

  // 关闭弹窗
  const closeModal = () => {
    _Modal.close();
  };

  const closeGroupModal = () => {
    groupModal.close();
  };

  useEffect(() => {
    getDefaultSite();
    stepOptionDs.query();
    const stepOptionList = stepOptionDs.toData();
    setStepOption(stepOptionList);
    setBomComponentId(0);
  }, []);


  // 勾选数据收集项
  useEffect(() => {
    checkRef.current = checkedList;
  }, [checkedList]);

  // useEffect(() => {
  //   dataSet.addEventListener('query', () => {
  //     setTimeout(() => {
  //       setTableTotal(dataSet.toData()[0]?.workOrderCount || 0);
  //       setTableRate(dataSet.toData()[0]?.dispatchRate || '');
  //     }, 1000);
  //   });
  //   sendTableDs.addEventListener('query', () => {
  //     setTimeout(() => {
  //       setSendTableTotal(sendTableDs.toData()[0]?.workOrderCount || 0);
  //       setSendTableRate(sendTableDs.toData()[0]?.dispatchedRate || '');
  //     }, 1000);
  //   });
  // }, [dataSet, sendTableDs]);

  // 查询默认站点
  const getDefaultSite = () => {
    const params = {
      tenantId,
      defaultOrganizationFlag: 'Y',
    };
    request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/hme-mod-site/permission/site/repare/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        // console.log('resSite', res);
        if (res.content.length === 0) {
          formDs.current.init('siteCode', undefined);
          formDs.current.init('siteId', undefined);
          setSiteIds([]);
          setSiteInfo({});
          window.localStorage.setItem('repairInstructionSiteId', '');
          // bomHeadDs.current.init('siteIds', '');
        } else {
          formDs.current.init('siteCode', res.content[0].siteCode);
          formDs.current.init('siteId', res.content[0].siteId);
          setSiteIds([res.content[0].siteId]);
          setSiteInfo(res.content[0]);
          window.localStorage.setItem('repairInstructionSiteId', res.content[0].siteId);
          // bomHeadDs.current.init('siteIds', res.content[0].siteId);
          // lineDs.current.init('siteIds', res.content[0].siteId);
        }
      } else {
        setSiteIds([]);
        window.localStorage.setItem('repairInstructionSiteId', '');
        // lineDs.current.init('siteIds', '');
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 查询装配清单
  const getBomList = async id => {
    bomHeadDs.setQueryParameter('bomId', id);
    await bomHeadDs.query();
  };

  // 查询工艺路线
  const getRouterList = async id => {
    routerHeadDs.setQueryParameter('routerId', id);
    routerHeadDs.query();
    detailTableDs.setQueryParameter('routerId', id);
    detailTableDs.setQueryParameter('materialId', formDs.current.get('materialId'));
    detailTableDs.setQueryParameter('revisionCode', formDs.current.get('revisionCode'));
    await detailTableDs.query().then(res => {
      if (res && !res.failed) {
        res.forEach(item => {
          item.newDate = new DataSet({
            selection: false,
            fields: [
              {
                name: 'tagGroupCode',
                type: 'string',
                label: '编码',
              },
              {
                name: 'tagGroupDescription',
                type: 'string',
                label: '描述',
              },
              {
                name: 'tagGroupTypeDescription',
                type: 'string',
                label: '收集组类型',
              },
              {
                name: 'collectionTimeControlDescription',
                type: 'string',
                label: '收集时间点',
              },
            ],
          });
          item.newDate.data = item.tagGroupList || [];
        });
        // console.log(res);
        setStepsList(operateData(res));
      } else {
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 编辑装配清单列表
  const handelEditBomList = () => {
    setCanEdit(true);
  };
  // 保存装配清单
  const handelSaveBomList = () => {
    request(`${Host}/v1/${tenantId}/hme-repair-task-create/bom/save/all/data/ui`, {
      method: 'POST',
      body: {
        ...bomHeadDs.toData()[0],
        siteIds,
        bomSaveFlag,
        materialCode: formDs.current.get('materialCode'),
        revisionCode: formDs.current.get('revisionCode'),
        routerId: routerSaveFlag ? formDs.current.get('routerId') : '',
      },
    }).then(res => {
      if (res && !res.failed) {
        setCanEdit(false);
        setBomSaveFlag(true);
        setCurrentBomId(res.bomId);
        // bomHeadDs.loadData([res]);
        formDs.current.init('bomId', res.bomId);
        formDs.current.init('bomName', res.bomName);
      } else {
        setBomSaveFlag(false);
        setCanEdit(true);
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 编辑工艺路线列表
  const handelEditRouterList = () => {
    setEditStatus(true);
  };

  // 保存工艺路线清单
  const handelSaveRouterList = () => {
    const data = routerHeadDs.toData()[0];
    delete data.mtRouterStepDTO;
    request(`${serveCode}/v1/${tenantId}/mt-router/sort/ui`, {
      method: 'POST',
      body: {
        mtRouterStepDTO: detailTableDs.toData().map((v) => {
          return {
            ...v,
            newDate: null,
          }
        }),
        ...data,
        routerSaveFlag,
        // bomId: bomSaveFlag ? formDs.current.get('bomId') : '',
        bomId: currentBomId,
        materialCode: formDs.current.get('materialCode'),
        revisionCode: formDs.current.get('revisionCode'),
      },
    }).then(res => {
      if (res && !res.failed) {
        request(`${Host}/v1/${tenantId}/hme-repair-task-create/router-step/save/ui`, {
          method: 'POST',
          body: res.rows,
        }).then((ok) => {
          if(ok && !ok.failed){
            setEditStatus(false);
            setRouterSaveFlag(true);

            formDs.current.init('routerName', ok.routerName);
            formDs.current.init('routerId', ok.routerId);

            ok.mtRouterStepDTO.forEach(item => {
              item.newDate = new DataSet({
                selection: false,
                fields: [
                  {
                    name: 'tagGroupCode',
                    type: 'string',
                    label: '编码',
                  },
                  {
                    name: 'tagGroupDescription',
                    type: 'string',
                    label: '描述',
                  },
                  {
                    name: 'tagGroupTypeDescription',
                    type: 'string',
                    label: '收集组类型',
                  },
                  {
                    name: 'collectionTimeControlDescription',
                    type: 'string',
                    label: '收集时间点',
                  },
                ],
              });

              item.newDate.data = item.tagGroupList || [];
            });
            // console.log(res.mtRouterStepDTO);
            routerHeadDs.loadData([ok]);
            setStepsList(operateData(ok.mtRouterStepDTO));

            detailTableDs.loadData([...operateData(ok.mtRouterStepDTO)]);
          }else{
            setRouterSaveFlag(false);
            setEditStatus(true);
            return notification.error({
              message: ok.message,
            });
          }
        })

      } else {
        setRouterSaveFlag(false);
        setEditStatus(true);
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  // 改变工艺路线开关
  const changeSwitchValue = val => {
    setForceFlag(val);
  };

  const handleReset = () => {
    formDs.reset();
    formDs.loadData([{
      workOrderTypeMeaning: '返修',
    }]);
    materialCreateDs.reset();
    materialDs.loadData([]);
    setTabKey('materialInfo');
    setCanCreate(false);
    setCanEdit(false);
    setBomSaveFlag(false);
    setRouterSaveFlag(false);
    setBomTab(true);
    setRouterTab(true);
  };

  // 创建
  const handleCreate = async () => {
    const array = [];
    const tagIdArray = selectedTagList.map(item => item.routerStepId);
    detailTableDs.toData().forEach(item => {
      if (tagIdArray.indexOf(item.routerStepId) === -1) {
        array.push({
          routerStepId: item.routerStepId,
          tagArray: item?.tagArray?.filter((v) => v.isChange === 'Y') || [],
        });
      }
    });
    const routerStepList = selectedTagList.concat(array);
    // if (materialCreateDs.toData().length === 0) {
    //   return notification.error({
    //     message: '请先创建物料批信息',
    //   });
    // }
    const validate = await formDs.validate();
    if (validate) {
      const params = {
        ...formDs.current.toData(),
        materialLotList: materialDs.toData(),
        routerSaveFlag: routerSaveFlag ? 'Y' : 'N',
        bomSaveFlag: bomSaveFlag ? 'Y' : 'N',
        forceFlag,
        routerStepList: routerStepList.filter((v) => v.isChange === 'Y') || [],
      };
      await request(`${Host}/v1/${tenantId}/hme-repair-task-create/wo/create/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          setCanCreate(true);
          formDs.current.init('workOrderNum', res[0].workOrderNum);
          formDs.current.init('workOrderTypeMeaning', '返修');
          formDs.current.init('bomName', res[0].bomName);
          formDs.current.init('routerName', res[0].routerName);
        } else {
          setCanCreate(false);
          setMaterialList([]);
          return notification.error({
            message: res.message,
          });
        }
      });
    }
  };

  const submitDataGroup = record => {
    groupModal.close();
    const currentData = record[0]?.tagGroupList || [];
    const array = [];
    const selectData = dataCollectionGroupCreateDs.selected.map(item => item.toData());
    const tagGroupIds = currentData.map(item => item.tagGroupId);
    // currentData.forEach(item => {
    selectData.forEach(item => {
      if (tagGroupIds.indexOf(item.tagGroupId) === -1) {
        array.push(item);
      }
    });
    const allData = array.concat(currentData);
    // record[0].newDate.data = allData;
    const lineData = detailTableDs.toData();
    // console.log('detailTableDs.toData()', detailTableDs.toData());
    (lineData || []).forEach(item => {
      // (item.routerStepId || []).forEach((it, index) => {
      if (item.routerStepId === record[0].routerStepId) {
        item.tagGroupList = allData;
      }
    });
    setStepsList(lineData);
    detailTableDs.loadData(lineData);

    const currentList = lineData.filter(item => item.routerStepId === record[0]?.routerStepId);
    modalUpdate(currentList);
  };

  // 切换页签
  const changeTab = async e => {
    setTabKey(e);
    setCanEdit(false);
    setEditStatus(false);
  };

  // 扫描物料批
  const onScanMaterial = async val => {
    if (val.trim()) {
      // // console.log(materialCreateDs.current.toData());
      // console.log(materialList, 'materialList');
      // if(val.includes('，')){
      //   return notification.error({message: '请使用英文逗号或使用空格区分！'});
      // }
      const params = {
        materialLotCodes: val.replace(/\s+/g,",").split(',' || '，'),
        materialId: formDs.current.get('materialId'),
        revisionCode: formDs.current.get('revisionCode'),
        materialLotList: materialList,
      };
      await request(`${Host}/v1/${tenantId}/hme-repair-task-create/scan/material-lot/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          // console.log('1res', res);
          notification.success({ message: '操作成功' });
          setMaterialList(res);
          materialDs.loadData(res);
          materialCreateDs.reset();
          if (res.length > 0) {
            formDs.current.set('materialId', res[0].materialId);
            formDs.current.set('materialCode', res[0].materialCode);
            formDs.current.set('materialName', res[0].materialName);
            formDs.current.set('revisionCode', res[0].revisionCode);
            formDs.current.set('primaryUomQtySum', res[0].primaryUomQtySum);
          }
        } else {
          setMaterialList([]);
          return notification.error({
            message: res.message,
          });
        }
      });
    } else {
      setMaterialList([]);
      notification.error({
        message: '请输入物料批',
      });
    }
  };

  const handleDelete = record => {
    const params = {
      materialId: formDs.current.get('materialId'),
      revisionCode: formDs.current.get('revisionCode'),
      materialLotList: materialList,
      delMaterialLotCode: record.toData().materialLotCode,
    };
    request(`${Host}/v1/${tenantId}/hme-repair-task-create/scan/material-lot/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        notification.success();
        setMaterialList(res);
        materialDs.loadData(res);
      } else {
        setMaterialList([]);
        return notification.error({
          message: res.message,
        });
      }
    });
  };

  const columns = [
    {
      name: 'materialLotCode',
      width: 240,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    { name: 'materialName', width: 240 },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'uomCode',
      width: 180,
    },
    {
      name: 'locatorCode',
      width: 100,
    },
    {
      name: 'ncCode',
      width: 100,
    },
    {
      name: 'description',
      width: 100,
    },
    {
      name: 'ncRecordNum',
      width: 150,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            props.history.push({
              pathname: `/hmes/bad-record/platform/list`,
              state: {
                ncRecordNum: record.data.ncRecordNum,
              },
            });
          }}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'remark',
      width: 100,
    },
    {
      name: 'workOrderNum',
      width: 150,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            props.history.push(
              `/hmes/workshop/production-order-mgt/detail/${record.data.workOrderId}`,
            );
          }}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'customerName',
      width: 180,
    },
    {
      name: 'soLineNum',
      width: 100,
    },
    {
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      width: 110,
      lock: 'right',
      align: 'center',
      renderer: ({ record }) => (
        <a onClick={() => handleDelete(record)} disabled={canCreate}>
          删除
        </a>
      ),
    },
  ];

  const siteChange = val => {
    // console.log(val);
    if (val) {
      setSiteIds([val.siteId]);
      setSiteInfo(val)
      window.localStorage.setItem('repairInstructionSiteId', val.siteId);
    } else {
      Modal.open({
        title: '当前操作会清空已输入物料批，是否继续执行?',
        autoCenter: true,
        onOk: () => {
          setSiteIds([]);
          materialCreateDs?.current?.set('materialLotCodes', null)
          window.localStorage.setItem('repairInstructionSiteId', '');
          setSiteInfo({});
          materialDs.loadData([]);
        },
        onCancel: () => {
          formDs.current.set('site', siteInfo);
          formDs.current.set('material', materialInfo);
        },
      });

    }
    setTabKey('materialInfo');
    setCurrentBomId('');
    setRouterId('');
    setBomSaveFlag(false);
    setRouterSaveFlag(false);
    formDs.data = [
      {
        site: val,
        workOrderTypeMeaning: '返修',
        material: {},
        bom: {},
        router: {},
        prodLine: {},
        locator: {},
        revisionCode: '',
        primaryUomQtySum: '',
        uomCode: '',
        productionVersionCode: '',
        planStartTime: '',
        planEndTime: '',
        remark: '',
      },
    ];
    // formDs.current.init('material', undefined);
    // formDs.current.init('productionLine', undefined);
    // formDs.current.init('productionVersion', undefined);
    // formDs.current.init('bom', undefined);
    // formDs.current.init('router', undefined);
    // formDs.current.set('productionVersionRequire', 'N');
    // formDs.current.set('productionVersionDisable', 'N');
    // formDs.current.init('soNumberObj', undefined);
  };

  // 更改物料
  const materialChange = val => {
    // console.log('val', val);
    if(!val && materialCreateDs?.current?.get('materialLotCodes')){
      return Modal.open({
        title: '当前操作会清空已输入物料批，是否继续执行?',
        autoCenter: true,
        onCancel: () => {
          formDs.current.set('material', materialInfo);
        },
        onOk: () => {
          materialDs.loadData([]);
          formDs.reset();
          materialCreateDs.reset();
          formDs.current.set('site', siteInfo);
          setMaterialInfo({});
        },
      });
    }
    if (materialDs.toData().length > 0) {
      Modal.confirm({
        title: '当前修改的物料信息与已输入的物料批不符，修改后会清空物料批，是否继续修改?',
        onOk: () => {
          setMaterialList([]);
          materialDs.loadData([]);
          materialCreateDs.reset();
        },
        onCancel: () => {
          // console.log('112');
          // record.set('inspectItem', null);
          // record.set('sourceObjectCode', null);
        },
      });
    }
    setMaterialInfo(val);
    formDs.current.init('productionVersionCode', undefined);
    formDs.current.init('bom', undefined);
    formDs.current.init('router', undefined);
    formDs.current.init('revisionCode', undefined);
    formDs.current.init('primaryUomQtySum', undefined);

  };

  // 更改装备清单
  const bomChange = val => {
    if (val) {
      getBomList(val.bomId);
      setBomTab(true);
      setCurrentBomId(val.bomId);
      setRouterSaveFlag(false);

    } else {
      setBomTab(false);
      setTabKey('materialInfo');
      setCurrentBomId('');
      formDs.current.set('router', {});
      setRouterTab(false);
      setRouterId('');
      setRouterSaveFlag(false);

    }
  };

  // 更改工艺路线
  const routerChange = val => {
    if (val) {
      getRouterList(val.routerId);
      setRouterTab(true);
      setRouterId(val.routerId);
    } else {
      setRouterTab(false);
      setTabKey('materialInfo');
      setRouterId('');
    }
  };

  const handleChangeRevisionCode = () => {
    formDs.current.set('productionVersion', null);
    formDs.current.set('bom', {});
    formDs.current.set('router', {});
  };

  const handleChangeProductVersion = () => {
    formDs.current.set('bom', {});
    formDs.current.set('router', {});
  };

  /**
   * 步骤信息-抽屉的footer
   */
  const handleNewStepDrawerFooter = () => {
    return (
      <>
        <div style={{ float: 'right' }}>
          {editStatus ? (
            <>
              <Button onClick={closeModal}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button onClick={handleNewStepDrawerFooterOk} type="submit" color="primary">
                {intl.get('tarzan.common.button.confirm').d('确定')}
              </Button>
            </>
          ) : (
            <Button onClick={closeModal}>{intl.get('tarzan.common.button.back').d('返回')}</Button>
          )}
        </div>
      </>
    );
  };

  /**
   * 步骤信息-抽屉的保存按钮
   */
  const handleNewStepDrawerFooterOk = () => {
    // @ts-ignore
    newStepDrawerRef.current.handleOK();
  };

  /**
   * 步骤信息抽屉
   * @param record: 行的数据
   */
  const handleAddTableData = record => {
    let data = {};
    if (record) {
      data = record.toData();
    }

    /**
     * 步骤抽屉需要的数据
     */
    const NewStepDrawerProps = {
      editStatus,
      dataSource: data || {},
      stepsList, // 步骤列表
      routerLov: 'MT.METHOD.ROUTER',
      UpdateStepList,
      closeModal,
      serveCode,
      operationLov: 'MT.METHOD.OPERATION',
      tenantId,
      selectedRouterType,
      methodServeCode: serveCode,
    };

    _Modal = Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.process.routes.title.stepDrawer').d('步骤信息'),
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <NewStepDrawer ref={newStepDrawerRef} {...NewStepDrawerProps} />
        </>
      ),
      footer: handleNewStepDrawerFooter(),
    });
  };

  /**
   * 详情页表格-删除步骤
   */
  const deleteRecord = async record => {
    await detailTableDs.remove(record);
    let currentList = detailTableDs.toData();
    const currentRouterStepId = record.get('routerStepId');
    const currentRouterStepGroupStepId = record.get('routerStepGroupStepId');
    if (currentRouterStepId) {
      (currentList || []).forEach(item => {
        (item.mtRouterStepGroupStepDTO || []).forEach((it, index) => {
          if (it.routerStepGroupStepId === currentRouterStepGroupStepId) {
            (item.mtRouterStepGroupStepDTO || []).splice(index, 1);
          }
        });
        item.mtRouterStepGroupDTO = {
          ...item.mtRouterStepGroupDTO,
          mtRouterStepGroupStepDTO: item.mtRouterStepGroupStepDTO,
        };
      });
    } else {
      currentList = currentList.filter(item => item.routerStepId !== currentRouterStepId);
    }
    for (let index = 0; index < currentList.length; index++) {
      const item = currentList[index];
      if ((item.mtRouterNextStepDTO || []).length > 0) {
        item.mtRouterNextStepDTO = [];
      }
    }
    // @ts-ignore
    setStepsList(operateData(currentList));
    detailTableDs.loadData([...operateData(currentList)]);
  };

  /**
   * 处理打平后的数据
   * @param list
   */
  const operateData = list => {
    const newList = [];
    list.forEach(i => {
      if (!i.routerStepGroupStepId) {
        newList.push(i);
      }
    });
    newList.forEach(i => {
      if (i.mtRouterStepGroupDTO?.routerStepGroupId) {
        const newGroupList = [];
        i.mtRouterStepGroupDTO?.mtRouterStepGroupStepDTO.forEach(j => {
          list.forEach(q => {
            if (j.routerStepGroupStepId === q.routerStepGroupStepId) {
              newGroupList.push({
                ...j,
                ...q,
              });
            }
          });
        });
        i.mtRouterStepGroupDTO = {
          ...i.mtRouterStepGroupDTO,
          mtRouterStepGroupStepDTO: [...newGroupList],
        };
      }
    });
    return newList;
  };

  /**
   * 更新StepList
   * @param val
   */
  const UpdateStepList = val => {
    val.forEach(i => {
      if (i.mtRouterStepGroupDTO?.routerStepGroupId) {
        i.mtRouterStepGroupStepDTO = i.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO;
      }
    });
    setStepsList(val);
    detailTableDs.loadData([...val]);
  };

  /**
   * 工艺步骤-工艺组件抽屉
   * @param record
   */
  const setOperationComponent = record => {
    const data = record.toData();
    const { routerStepId } = data;
    const newList = [];
    (stepsList || []).forEach(item => {
      if (item.routerStepType === 'OPERATION') {
        // 如果是工艺类型则直接取出来
        newList.push({ ...item });
      } else if (
        item.routerStepType === 'GROUP' &&
        ((item.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO || []).length > 0
      ) {
        item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.forEach(it => {
          newList.push({ ...it }); // 是步骤组类型且子步骤不为空,取步骤组下的工艺,
        });
      }
    });

    let currentData;
    (newList || []).forEach(item => {
      if (routerStepId === item.routerStepId) {
        currentData = item;
        currentDataSource = item;
      }
    });
    tableList = newList;
    const { description, stepName } = currentData;

    const operationComponentDrawerProps = {
      currentDataSource: currentData,
      description,
      stepName,
    };

    _Modal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.operationComponent`).d('工艺组件'),
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <OperationComponentDrawer {...operationComponentDrawerProps} />
        </>
      ),
      footer: operationComponentDrawerFooter(),
    });
  };

  const onCheck = (checkedKeys, { checkedNodes }, id) => {
    const tagList = selectedTagList;
    const list = [];
    const params = [];
    checkedNodes.forEach(item => {
      if (item.props?.routerStepId) {
        list.push(item.props.tagId);
      }
    });

    const changeData = tagList.filter(v => v.routerStepId === id);

    if (changeData.length === 0 || tagList.length === 0) {
      params.push({
        routerStepId: id,
        isChange: 'Y',
        tagArray: list,
      });

      const newParams = params.concat(tagList);
      setSelectedTagList(newParams);
    } else {
      tagList.forEach(item => {
        if (item.routerStepId === id) {
          item.tagArray = list;
          item.isChange = 'Y';
        }
      });
      setSelectedTagList(tagList);
    }

    setCheckedList(checkedKeys);
    changeCheckData = checkedKeys;
    changeCheckId = list;
    const currentList = detailTableDs.toData().filter(item => item.routerStepId === id);
    modalUpdate(currentList);
  };

  const modalUpdate = (currentList) => {


    const tagGroupDrawerProps = {
      getDataCollectionGroup,
      onCheck,
      renderTreeItems,
      currentList,
      currentKey: changeCheckData,
      canCreate,
      editStatus,
      routerId,
      formDs,
      // currentKey: checkedList,
    };

    _Modal.update({
      key: Modal.key(),
      title: '数据收集组',
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <TagGroupTree ref={tagGroupDrawerRef} {...tagGroupDrawerProps} />
        </>
      ),
      footer: (
        <div id="hcmpModal">
          <Button onClick={closeModal}>
            {intl.get(`tarzan.aps.common.button.cancel`).d('取消')}
          </Button>
          <Button color="primary" disabled={canCreate || !editStatus} onClick={() => handleOkDataGroup(currentList)}>
            {intl.get(`tarzan.aps.common.button.sure`).d('确定')}
          </Button>
        </div>
      ),
    });
  };

  // const groupcolumns = [
  //   {
  //     name: 'tagGroupCode',
  //   },
  //   {
  //     name: 'tagGroupDescription',
  //   },
  //   {
  //     name: 'tagGroupTypeDescription',
  //   },
  //   { name: 'collectionTimeControlDescription' },
  // ];

  /**
   * 工艺步骤-数据收集组抽屉
   * @param record
   */
  const openDataGroupModal = record => {
    const currentList = stepsList.filter(
      item => item.routerStepId === record.toData().routerStepId,
    );
    changeCheckData = currentList[0].tagGroupArray;

    // setCurrentTagGroup(currentList[0]);
    const tagGroupDrawerProps = {
      getDataCollectionGroup,
      onCheck,
      renderTreeItems,
      currentList,
      routerId,
      // currentKey: checkRef.current,
      // currentKey: changeCheckData,
      currentKey: changeCheckData,
      canCreate,
      editStatus,
      formDs,
    };

    _Modal = Modal.open({
      key: Modal.key(),
      title: '数据收集组',
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <TagGroupTree ref={tagGroupDrawerRef} {...tagGroupDrawerProps} />
        </>
      ),
      footer: (
        <div id="hcmpModal">
          <Button onClick={closeModal}>
            {intl.get(`tarzan.aps.common.button.cancel`).d('取消')}
          </Button>
          <Button color="primary" disabled={canCreate || !editStatus} onClick={() => handleOkDataGroup(currentList)}>
            {intl.get(`tarzan.aps.common.button.sure`).d('确定')}
          </Button>
        </div>
      ),
    });
  };

  // // 树节点
  // const nodeRenderer = ({ record }) => {
  //   console.log(record);
  //   // ${obj.tagGroupCode} ${obj.tagGroupDescription} ${obj.tagGroupTypeDescription} ${obj.collectionTimeControlDescription}
  //   // 父节点
  //   if (record.get('tagList')) {
  //     return (
  //       <span>
  //         {record.get('tagGroupCode')}&nbsp;{record.get('tagGroupDescription')}&nbsp;
  //         {record.get('tagGroupTypeDescription')}&nbsp;
  //         {record.get('collectionTimeControlDescription')}
  //       </span>
  //     );
  //   }
  //   // 子节点
  //   return (
  //     <span>
  //       {record.get('bomMaterialCode')}/{record.get('bomMaterialName')}
  //     </span>
  //   );
  // };

  /**
   * 工艺步骤-获取新建数据收集组列表
   * @param record
   */
  const getDataCollectionGroup = record => {
    dataCollectionGroupCreateDs.setQueryParameter('routerStepId', record[0]?.routerStepId);
    dataCollectionGroupCreateDs.query();
    const groupCreateColumns = [
      {
        name: 'tagGroupCode',
      },
      {
        name: 'tagGroupDescription',
      },
    ];

    groupModal = Modal.open({
      key: Modal.key(),
      title: '新增数据收集组',
      destroyOnClose: true,
      style: {
        width: 720,
      },
      children: (
        <>
          <Table dataSet={dataCollectionGroupCreateDs} columns={groupCreateColumns} />
        </>
      ),
      footer: (
        <div id="hcmpModal">
          <Button onClick={closeGroupModal}>
            {intl.get(`tarzan.aps.common.button.cancel`).d('取消')}
          </Button>
          <Button color="primary" onClick={() => submitDataGroup(record)}>
            {intl.get(`tarzan.aps.common.button.sure`).d('确定')}
          </Button>
        </div>
      ),
    });
  };

  // const expandedRowRenderer = ({ record }) => {
  //   const data = record.get('tagList') || [];
  //   return (
  //     <Table dataSet={new DataSet({ paging: false, selection: 'multiple' }).loadData(data)}>
  //       <Column name="tagCode" header="编码" />
  //       <Column name="tagDescription" header="描述" />
  //       <Column name="tagGroupTypeDescription" header="收集组类型" />
  //       <Column name="collectionTimeControlDescription" header="收集时间点" />
  //     </Table>
  //   );
  // };

  /**
   * 工艺组件-展示抽屉
   */
  const operationComponentDrawerFooter = () => {
    return (
      <>
        <div style={{ float: 'right' }}>
          {
            <>
              <Button onClick={handleBack}>
                {intl.get(`${modelPrompt}.backOperation`).d('上一工艺')}
              </Button>
              <Button onClick={handleNext}>
                {intl.get(`${modelPrompt}.nextOperation`).d('下一工艺')}
              </Button>
              <Button onClick={closeModal}>
                {intl.get('tarzan.common.button.back').d('返回')}
              </Button>
            </>
          }
        </div>
      </>
    );
  };
  /**
   * 工艺组件-展示抽屉-上一工艺
   */
  const handleBack = () => {
    const { routerStepId } = currentDataSource;
    let currentData;
    tableList.forEach((item, index) => {
      if (routerStepId === item.routerStepId) {
        currentData = index > 0 ? tableList[index - 1] : tableList[tableList.length - 1];
        currentDataSource = currentData;
      }
    });

    const { description, stepName } = currentData;
    const operationComponentDrawerProps = {
      currentDataSource: currentData,
      description,
      stepName,
    };

    _Modal.update({
      children: (
        <>
          <OperationComponentDrawer {...operationComponentDrawerProps} />
        </>
      ),
    });
  };

  /**
   * 工艺组件-展示抽屉-下一工艺
   */
  const handleNext = () => {
    const { routerStepId } = currentDataSource;
    let currentData;
    tableList.forEach((item, index) => {
      if (routerStepId === item.routerStepId) {
        currentData = tableList.length > index + 1 ? tableList[index + 1] : tableList[0] || {};
        currentDataSource = currentData;
      }
    });

    const { description, stepName } = currentData;
    const operationComponentDrawerProps = {
      currentDataSource: currentData,
      description,
      stepName,
    };

    _Modal.update({
      children: (
        <>
          <OperationComponentDrawer {...operationComponentDrawerProps} />
        </>
      ),
    });
  };

  const detailTableColumn = [
    {
      header: () => (
        // <PermissionButton
        //   type="c7n-pro"
        //   icon="add"
        //   disabled={editStatus}
        //   onClick={() => {
        //     // handleAddTableData(false);
        //   }}
        //   funcType="flat"
        //   shape="circle"
        //   size="small"
        //   permissionList={[
        //     {
        //       code: `tarzan${path}.button.edit`,
        //       type: 'button',
        //       meaning: '详情页-编辑新建删除复制按钮',
        //     },
        //   ]}
        // />
        <Button
          style={{marginLeft: 30}}
          type="c7n-pro"
          icon="add"
          disabled={!editStatus}
          onClick={() => {
            handleAddTableData(false);
          }}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      align: 'left',
      lock: 'left',
      width: 80,
      hideable: false,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button
            type="c7n-pro"
            icon="remove"
            disabled={!editStatus}
            funcType="flat"
            shape="circle"
            size="small"
          />
          {/* <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!editStatus}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          /> */}
        </Popconfirm>
      ),
    },
    {
      name: 'sequence',
      width: 120,
      editor: () => editStatus && <NumberField step={1} dataSet={detailTableDs} name="sequence" />,
    },
    {
      name: 'description',
      width: 150,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              handleAddTableData(record);
            }}
          >
            {record.get('stepName') ? `${value} / ${record.get('stepName')}` : value}
          </a>
        );
      },
    },
    {
      name: 'routerDoneStepFlag',
      width: 200,
      renderer: ({ record }) => {
        const mtRouterOperationDTO = record.get('mtRouterOperationDTO');
        const mtRouterLinkDTO = record.get('mtRouterLinkDTO');
        if (mtRouterOperationDTO) {
          return (mtRouterOperationDTO || {}).operationName && (mtRouterOperationDTO || {}).revision
            ? `${(mtRouterOperationDTO || {}).operationName} / ${mtRouterOperationDTO.revision}`
            : '-';
        }
        if (mtRouterLinkDTO) {
          return ((mtRouterLinkDTO || {}).stepName || (mtRouterLinkDTO || {}).routerName) &&
            (mtRouterLinkDTO || {}).revision
            ? `${mtRouterLinkDTO.stepName || (mtRouterLinkDTO || {}).routerName} / ${
              mtRouterLinkDTO.revision
            }`
            : '-';
        }
        return '-';
      },
    },
    {
      name: 'routerStepType',
      width: 160,
      renderer: ({ value }) => {
        return (stepOption.filter(ele => ele.typeCode === value)[0] || {}).description;
      },
    },
    {
      name: 'entryStepFlag',
      width: 240,
      renderer: ({ record }) => {
        return (
          <>
            <Tag className={record.get('keyStepFlag') === 'Y' ? 'hcm-tag-green' : 'hcm-tag-red'}>
              {record.get('keyStepFlag') === 'Y'
                ? intl.get(`${modelPrompt}.keyStep`).d('关键')
                : intl.get(`${modelPrompt}.NotKeyStep`).d('非关键')}
            </Tag>
            {record.get('entryStepFlag') === 'Y' && (
              <Tag className="hcm-tag-blue">{intl.get(`${modelPrompt}.entryStep`).d('入口')}</Tag>
            )}
            {record.get('mtRouterReturnStepDTO')?.returnType && (
              <Tag className="hcm-tag-blue">{intl.get(`${modelPrompt}.returnStep`).d('返回')}</Tag>
            )}
            {record.get('routerDoneStepFlag') === 'Y' && (
              <Tag className="hcm-tag-blue">
                {intl.get(`${modelPrompt}.routerDoneStep`).d('完成')}
              </Tag>
            )}
          </>
        );
      },
    },
    {
      name: 'requiredTimeInProcess',
      width: 130,
      renderer: ({ record }) => {
        const data = record.get('mtRouterOperationDTO');
        if (data?.requiredTimeInProcess) {
          return fomatFloat(data?.requiredTimeInProcess, 6);
        }
        return '-';
      },
    },
    {
      width: 250,
      header: intl.get(`tarzan.common.label.action`).d('操作'),
      lock: 'right',
      align: 'center',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              disabled={record.get('routerStepType') !== 'OPERATION'}
              onClick={() => setOperationComponent(record)}
            >
              {intl.get(`${modelPrompt}.operationComponent`).d('工艺组件')}
            </a>
            <a
              onClick={() => openDataGroupModal(record)}
              // disabled={record.get('_status') === 'create'}
            >
              数据收集组
            </a>
            {record.get('_status') === 'create' ? (
              <a disabled>{intl.get('tarzan.common.button.extendedField').d('扩展属性')}</a>
            ) : (
              <AttributeDrawer
                type="text"
                canEdit={editStatus}
                disabled={!record.get('routerStepId')}
                kid={record.get('routerStepId')}
                // tablename="mt_router_step_attr"
                className="org.tarzan.method.domain.entity.MtRouterStep"
                serveCode
              />
            )}
          </span>
        );
      },
    },
  ];

  // 数组收集组确定按钮
  const handleOkDataGroup = record => {
    const lineData = detailTableDs.toData();
    // console.log('detailTableDs.toData()', detailTableDs.toData());
    (lineData || []).forEach(item => {
      // (item.routerStepId || []).forEach((it, index) => {
      if (item.routerStepId === record[0].routerStepId) {
        item.tagGroupArray = changeCheckData;
        item.tagArray = changeCheckId;
      }
    });
    // @ts-ignore
    setStepsList(operateData(lineData));
    detailTableDs.loadData([...operateData(lineData)]);
    // setStepsList(lineData);
    // detailTableDs.loadData(lineData);
    _Modal.close();
    const currentList = lineData.filter(item => item.routerStepId === record[0]?.routerStepId);
    modalUpdate(currentList)
  };

  const fomatFloat = (src, pos) => {
    // eslint-disable-next-line
    return Math.round(src * Math.pow(10, pos)) / Math.pow(10, pos);
  };

  /**
   * 更新CompList
   * @param val
   */
  const UpdateCompList = val => {
    setCompList(val);
  };

  /**
   * 组件分配-抽屉
   */
  const openComponentDistributionDrawer = () => {
    const newList = operateData(detailTableDs.toData());
    // @ts-ignore
    setStepsList(newList);
    const componentDistributionDrawerProps = {
      editStatus: !editStatus,
      routerId,
      stepsList: newList, // 步骤列表
      compList,
      serveCode,
      tenantId,
      UpdateCompList,
      UpdateStepList,
      currentBomId,
    };

    _Modal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.componentDistribution`).d('组件分配'),
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <ComponentDistributionDrawer
            ref={openComponentDistributionDrawerRef}
            {...componentDistributionDrawerProps}
          />
        </>
      ),
      footer: openComponentDistributionDrawerFooter(false),
    });
  };

  /**
   * 组件分配-抽屉的footer
   * @param val
   */
  const openComponentDistributionDrawerFooter = val => {
    return (
      <>
        <div style={{ float: 'right' }}>
          {editStatus ? (
            <>
              <Button onClick={closeModal}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button
                onClick={openComponentDistributionDrawerFooterOk}
                type="submit"
                color="primary"
                loading={val}
              >
                {intl.get('tarzan.common.button.confirm').d('确定')}
              </Button>
            </>
          ) : (
            <Button onClick={closeModal}>{intl.get('tarzan.common.button.back').d('返回')}</Button>
          )}
        </div>
      </>
    );
  };

  const renderTitle = obj => {
    const returnValue = obj.tagGroupCode
      ? `${obj.tagGroupCode} ${obj.tagGroupDescription} ${obj.tagGroupTypeDescription} ${obj.collectionTimeControlDescription}`
      : `${obj.tagCode} ${obj.tagDescription}`;
    return returnValue;
  };

  /**
   * 渲染树的子节点
   */
  const renderTreeItems = (nodes, val) => {
    return (
      nodes &&
      nodes.map(item => {
        if (item.tagList) {
          return (
            <TreeNode
              title={renderTitle(item)}
              // title="111"
              icon={<Icon type="layout" />}
              key={item.routerStepTagGroupId}
              id={item.routerStepTagGroupId}
              // type={item.tagCode}
              parentId={item.routerStepTagGroupId}
              // parentType={item.tagCode}
              dataRef={item}
            >
              {renderTreeItems(item.tagList, val)}
            </TreeNode>
          );
          // eslint-disable-next-line no-else-return
        } else {
          return (
            <TreeNode
              title={renderTitle(item)}
              icon={<Icon type="laptop" />}
              key={item.groupTagId}
              id={item.groupTagId}
              // type={item.tagCode}
              parentId={item.routerStepTagGroupId}
              // parentType={item.tagCode}
              dataRef={item}
              isLeaf={!item.children}
              tagId={item.tagId}
              routerStepId={val}
            />
          );
        }
      })
    );
  };

  /**
   * 组件分配OKbutton
   */
  const openComponentDistributionDrawerFooterOk = () => {
    // @ts-ignore
    openComponentDistributionDrawerRef.current.onOk();
  };

  const lineProps = {
    canEdit,
    lineDs,
    substituteDrawerDs,
    referencePointDrawerDs,
    path,
    id: 'bomId',
    modelPrompt,
    siteIds,
    currentBomComponentId,
    setBomComponentId,
    attributeServerCode: serveCode,
  };

  // const routerLineProps = {
  //   detailTableDs,
  //   detailTableColumn,
  // };

  return (
    <div className='hmes-style'>
      <Header title="自制件返修指令创建">
        <Button color="primary" icon="add" onClick={() => handleCreate()} disabled={canCreate}>
          创建
        </Button>
        <Button onClick={() => handleReset()}>重置</Button>
      </Header>
      <Content>
        <div>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
            <Panel header="基础信息" key="basicInfo" dataSet={formDs}>
              <Form
                disabled={canCreate}
                dataSet={formDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <Lov name="site" onChange={val => siteChange(val)} />
                <TextField name="workOrderNum" />
                <TextField name="workOrderTypeMeaning" />
              </Form>
            </Panel>
            <Panel header="工单信息" key="location" dataSet={formDs}>
              <Form
                disabled={canCreate}
                dataSet={formDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <C7nFormItemSort name="material" itemWidth={['70%', '30%']}>
                  <Lov name="material" onChange={materialChange} />
                  <Select
                    name="revisionCode"
                    onChange={handleChangeRevisionCode}
                    dropdownMatchSelectWidth={false}
                    dropdownMenuStyle={{ width: '200px' }}
                  />
                </C7nFormItemSort>
                <TextField name="materialName" />
                <NumberField name="primaryUomQtySum" disabled={materialList.length > 0}/>
                <TextField name="uomCode" />
                <Select name="productionVersionCode" onChange={handleChangeProductVersion} />
                <Lov name="bom" onChange={bomChange} disabled={bomSaveFlag} />
                <Lov name="router" onChange={routerChange} disabled={routerSaveFlag} />
                <Lov name="prodLine" />
                <Lov name="locator" />
                <DateTimePicker name="planStartTime" />
                <DateTimePicker name="planEndTime" />
                <TextField name="remark" />
              </Form>
            </Panel>
          </Collapse>
          <Tabs activeKey={tabKey} onChange={changeTab}>
            <Tabs.TabPane tab="物料批信息" key="materialInfo">
              <Form
                disabled={canCreate}
                dataSet={materialCreateDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <TextArea
                  name="materialLotCodes"
                  placeholder="请输入物料批进行扫描"
                  required
                  clearButton
                  disabled={siteIds.length === 0}
                  onEnterDown={e => onScanMaterial(e.target.value)}
                />
              </Form>
              <Table
                queryBar="filterBar"
                queryBarProps={{
                  fuzzyQuery: false,
                }}
                // onRow={({ record }) => {
                //   return {
                //     onClick: () => {
                //       handleRowClick(record);
                //     },
                //   };
                // }}
                dataSet={materialDs}
                columns={columns}
                searchCode="RepairInstructionCreated"
                customizedCode="RepairInstructionCreated"
              />
            </Tabs.TabPane>
            <Tabs.TabPane tab="装配清单" key="bom" disabled={!bomTab}>
              <div style={{ float: 'right', marginBottom: '10px' }}>
                {canEdit && (
                  <Button color="primary" onClick={() => {setCanEdit(false)}}>
                    取消
                  </Button>
                )}
                {canEdit ? (
                  <Button color="primary" onClick={() => handelSaveBomList()}>
                    保存
                  </Button>
                ) : (
                  <Button color="primary" onClick={() => handelEditBomList()} disabled={canCreate}>
                    编辑
                  </Button>
                )}
              </div>
              <ComponentLineTable {...lineProps} />
            </Tabs.TabPane>
            <Tabs.TabPane tab="工艺路线" key="router" disabled={!routerTab}>
              <div style={{ float: 'right', marginBottom: '10px' }}>
                {editStatus && (
                  <Button color="primary" onClick={() => {
                    setEditStatus(false);
                    // getRouterList(routerId);
                  }}>
                    取消
                  </Button>
                )}
                {editStatus ? (
                  <Button color="primary" onClick={() => handelSaveRouterList()}>
                    保存
                  </Button>
                ) : (
                  <Button
                    color="primary"
                    onClick={() => handelEditRouterList()}
                    disabled={canCreate}
                  >
                    编辑
                  </Button>
                )}
                <Button onClick={openComponentDistributionDrawer} icon="fenpeiquanxian-o">
                  组件分配
                </Button>
                <Switch
                  onChange={val => changeSwitchValue(val)}
                  style={{ padding: '0 10px' }}
                  disabled={!editStatus}
                />
                <span>强制工艺路线</span>
              </div>
              {/* <RouterLineTable {...routerLineProps} /> */}
              {/* <Spin spinning={loading}> */}
              <div style={{ marginTop: 30 }}>
                <Table
                  // disabled={editStatus}
                  dataSet={detailTableDs}
                  columns={detailTableColumn}
                  mode={TableMode.tree}
                  indentSize={30}
                  defaultRowExpanded
                />
              </div>

              {/* </Spin> */}
            </Tabs.TabPane>
          </Tabs>
        </div>
      </Content>
    </div>
  );
});
export default formatterCollections()(MachineProductionDispatch);
