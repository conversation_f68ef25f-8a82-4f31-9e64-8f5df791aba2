/**
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2023/8/8 20:08
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存检证信息
 * @function SaveVerification
 * @returns {object} fetch Promise
 */
export function SaveVerification(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-platform/save/ui`,
    method: 'POST',
  };
}

/**
 * 查询用户默认站点
 * @function GetDefaultSite
 * @returns {object} fetch Promise
 */
export function GetDefaultSite(): object {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/ui`,
    method: 'GET',
  };
}

/**
 * 附件复制
 * @function CopyEnclosure
 * @returns {object} fetch Promise
 */
export function CopyEnclosure(): object {
  return {
    url: `/hfle/v1/${tenantId}/files/copy-file`,
    method: 'POST',
  };
}
