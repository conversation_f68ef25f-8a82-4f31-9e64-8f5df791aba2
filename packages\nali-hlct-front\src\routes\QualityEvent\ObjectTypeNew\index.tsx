/**
 * @Description: 事件对象类型维护
 * @Author: <<EMAIL>>
 * @Date: 2022-10-19 09:53:45
 * @LastEditTime: 2022-12-26 15:52:11
 * @LastEditors: <<EMAIL>>
 */

import React, { FC, useMemo, useEffect, useState } from 'react';
import { RouteComponentProps } from 'react-router';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import {
  DataSet,
  Table,
  TextField,
  TextArea,
  Switch,
  IntlField,
  Output,
  Modal,
  Form,
} from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { EnableRender } from '@components/tarzan-ui';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ResizeType } from 'choerodon-ui/pro/lib/text-area/enum';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from './stories/ObjectTypeDs';
import {
  saveEventObjectTypeConfig,
  deleteEventObjectTypeConfig,
  getObjectTypeConfig,
} from './services';

import TableDrawer from './TableDrawer';

const modelPrompt = 'tarzan.event.objectType.model.objectType';

let objectSQLDrawer;

const ObjectType: FC<RouteComponentProps> = ({ match: { path } }) => {
  const [selectList, setSelectList] = useState([]);
  const saveEventObjectType = useRequest(saveEventObjectTypeConfig(), {
    manual: true,
    needPromise: true,
  });

  const deleteEventObjectType = useRequest(deleteEventObjectTypeConfig(), {
    manual: true,
    needPromise: true,
  });

  const getObjectType = useRequest(getObjectTypeConfig(), {
    manual: true,
    needPromise: true,
  });

  const tableDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  const objectSQLDrawerDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  const objectTypeDrawerDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  useEffect(() => {
    if (tableDs) {
      tableDs.addEventListener('batchSelect', handleSelect);
      tableDs.addEventListener('batchUnSelect', handleSelect);
    }
    return () => {
      if (tableDs) {
        tableDs.removeEventListener('batchSelect', handleSelect);
        tableDs.removeEventListener('batchUnSelect', handleSelect);
      }
    };
  }, []);

  const handleSelect = () => {
    const _selectedList = [];
    tableDs.selected.forEach(record => {
      // @ts-ignore
      _selectedList.push(record);
    });
    setSelectList(_selectedList);
  };

  const handleCreateEventRequestType = () => {
    const newLine = tableDs.create({}, 0);
    newLine.setState('editing', true);
  };

  const handleDeleteObjectType = () => {
    const _deleteList = [];
    selectList.forEach(record => {
      // @ts-ignore
      if (record.get('objectTypeId')) {
        // @ts-ignore
        _deleteList.push(record.get('objectTypeId'));
      }
    });
    if (_deleteList.length === 0) {
      tableDs.delete(selectList, false);
      setSelectList([]);
      // @ts-ignore
      notification.success();
    } else {
      deleteEventObjectType.run({
        params: _deleteList,
        onSuccess: () => {
          tableDs.delete(selectList, false);
          setSelectList([]);
          // @ts-ignore
          notification.success();
        },
      });
    }
  };

  const handleEditMessage = record => {
    record.setState('editing', true);
  };

  const handleCleanLine = record => {
    if (record.get('objectTypeId')) {
      record.reset();
      record.setState('editing', false);
    } else {
      tableDs.delete(record, false);
    }
  };

  const handleSave = async record => {
    const recordData = record.toData();
    const validate = await record.validate();
    if (!validate) {
      return;
    }
    recordData.description = recordData.description || '';

    if (recordData._tls) {
      const newTls = { ...recordData._tls };
      Object.keys(newTls).forEach(_fieldName => {
        if (
          newTls[_fieldName] &&
          Object.keys(newTls[_fieldName]) &&
          Object.keys(newTls[_fieldName]).length > 0
        ) {
          Object.keys(newTls[_fieldName]).forEach(_tlsKey => {
            newTls[_fieldName][_tlsKey] = newTls[_fieldName][_tlsKey] || '';
          });
        }
      });
      recordData._tls = newTls;
    }
    return saveEventObjectType.run({
      params: recordData,
      onSuccess: res => {
        record.reset();
        record.setState('editing', false);
        Object.keys(recordData).forEach(_key => {
          record.init(_key, recordData[_key]);
        });
        Object.keys(res).forEach(_key => {
          record.init(_key, res[_key]);
        });
        // @ts-ignore
        record.status = 'sync';
        // @ts-ignore
        notification.success();
        if (objectSQLDrawer) {
          objectSQLDrawer.close();
          objectSQLDrawer = undefined;
        }
      },
    });
  };

  const handleObjectSQLDrawerShow = record => {
    const drawerEditing = record.getState('editing');
    objectSQLDrawerDs.loadData([{
      ...record.toData(),
      // eslint-disable-next-line no-template-curly-in-string
      notice: intl.get(`${modelPrompt}.noticeInfo`).d('注：条件语句需维护租户，示例：AND tenant_id = ${tenant_id}。(${}为文本替换匹配规则字符)'),
    }]);
    objectSQLDrawer = Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.event.objectType.title.objectQuerySQL').d('对象语句查询'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      onOk: () => {
        const drawerRecordData: any = objectSQLDrawerDs.toData()[0];
        // eslint-disable-next-line no-template-curly-in-string
        if ((drawerRecordData.conditionClause || "").split('${tenant_id}').length < 2) {
          notification.error({
            message: intl.get(`${modelPrompt}.tenantIdRequired
          `).d('条件语句需维护租户!'),
          })
          return false;
        }
        record.set('tableName', drawerRecordData.tableName);
        record.set('conditionClause', drawerRecordData.conditionClause);
      },
      children: (
        <Form dataSet={objectSQLDrawerDs} columns={1}>
          <TextArea
            disabled={!drawerEditing}
            name="tableName"
            autoSize={{ minRows: 4 }}
            resize={ResizeType.vertical}
          />
          <TextArea
            disabled={!drawerEditing}
            name="conditionClause"
            autoSize={{ minRows: 4 }}
            resize={ResizeType.vertical}
          />
          <Output name="notice" />
        </Form>
      ),
      footer: (okBtn, cancelBtn) => {
        if (drawerEditing) {
          return [cancelBtn, okBtn];
        }
        return [cancelBtn];
      },
    });
  };
  const handleTableDrawerShow = record => {
    Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.event.objectType.title.exhibitionCol').d('展示列维护'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      // @ts-ignore
      children: <TableDrawer objectTypeId={record.get('objectTypeId')} path={path} />,
      footer: (okBtn, cancelBtn) => {
        return [cancelBtn];
      },
    });
  };

  const handleObjectTypeDrawerShow = record => {
    objectTypeDrawerDs.loadData([record.toData()]);
    getObjectType.run({
      params: { objectTypeId: record.get('objectTypeId') },
      onSuccess: res => {
        objectTypeDrawerDs.current!.set('eventTypeQuerySql', res.eventTypeQuerySql);
        objectTypeDrawerDs.current!.set('eventTypeQuerySql', res.eventTypeQuerySql);
      },
    });

    Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.event.objectType.title.objectPreview').d('对象展示预览'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <Form dataSet={objectTypeDrawerDs} columns={2}>
          <TextField disabled name="objectTypeCode" />
          <TextField disabled name="description" />
          <TextArea
            disabled
            name="eventTypeQuerySql"
            colSpan={2}
            autoSize={{ minRows: 4 }}
            resize={ResizeType.vertical}
          />
        </Form>
      ),
      footer: (okBtn, cancelBtn) => {
        return [cancelBtn];
      },
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'objectTypeCode',
      // width: 150,
      editor: record => record.getState('editing') && <TextField />,
    },
    {
      name: 'description',
      // width: 200,
      editor: record =>
        record.getState('editing') && (
          <IntlField
            modalProps={{ title: intl.get(`${modelPrompt}.description`).d('对象类型描述') }}
          />
        ),
    },
    {
      name: 'enableFlag',
      // width: 120,
      renderer: props => <EnableRender {...props} />,
      editor: record => record.getState('editing') && <Switch />,
    },
    {
      name: 'objectQuerySQL',
      // width: 120,
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => handleObjectSQLDrawerShow(record)}
            disabled={!record!.get('objectTypeId')}
          >
            {intl.get(`${modelPrompt}.querySQL`).d('查询语句')}
          </a>
        );
      },
    },
    {
      name: 'objectExhibitionCol',
      // width: 250,
      renderer: ({ record }) => {
        return (
          <a onClick={() => handleTableDrawerShow(record)} disabled={!record!.get('objectTypeId')}>
            {intl.get(`${modelPrompt}.exhibitionCol`).d('展示列')}
          </a>
        );
      },
    },
    {
      name: 'objectPreview',
      // width: 90,
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => handleObjectTypeDrawerShow(record)}
            disabled={!record!.get('objectTypeId')}
          >
            {intl.get(`${modelPrompt}.preview`).d('对象类型')}
          </a>
        );
      },
    },
    {
      name: 'operator',
      // width: 120,
      align: ColumnAlign.center,
      renderer: ({ record }) =>
        record?.getState('editing') ? (
          <>
            <a onClick={() => handleCleanLine(record)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </a>
            <a style={{ marginLeft: '10px' }} onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </a>
          </>
        ) : (
          <>
            <a onClick={() => handleEditMessage(record)}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </a>
          </>
        ),
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.event.objectType.view.title.objectType').d('对象类型维护')}>
        <PermissionButton
          type="c7n-pro"
          icon="add"
          color={ButtonColor.primary}
          onClick={handleCreateEventRequestType}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Popconfirm
          title={intl
            .get(`${modelPrompt}.confirm.delete`, {
              count: selectList.length,
            })
            .d(`总计${selectList.length}条数据，是否确认删除?`)}
          onConfirm={handleDeleteObjectType}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          okText={intl.get('tarzan.common.button.confirm').d('确定')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="delete_black-o"
            disabled={selectList.length === 0}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.delete').d('删除')}
          </PermissionButton>
        </Popconfirm>
      </Header>
      <Content>
        <Table
          searchCode="dxlxwh1"
          customizedCode="dxlxwh1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.event.objectType', 'tarzan.common'],
})(ObjectType);
