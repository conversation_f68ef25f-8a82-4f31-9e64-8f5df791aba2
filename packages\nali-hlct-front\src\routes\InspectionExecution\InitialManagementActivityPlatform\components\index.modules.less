.input-group-maintenance-value {
  .input-number {
    width: 100%;
    display: flex;
    :global {
      .number-label {
        width: 25px;
        height: 23px;
        margin-right: 10px;
        border: 1px solid #0003;
        background: #fff;
        color: #d9d9d9;
        text-align: center;
        padding: 0;
        margin-top: 3px;
      }
    }

    :global {
      .c7n-pro-radio-wrapper {
        margin-top: -6px;
        padding-left: 6px;
        padding-right: 5px;
        color: #333;
        .c7n-pro-radio-inner {
          visibility: hidden;
          position: relative;
          display: inline-block;
          vertical-align: middle;
          background-color: #fff;
          color: #fff;
          border: 0;
          border-radius: 50%;
          -webkit-transition: all 0.3s;
          transition: all 0.3s;
        }
      }
    }

    :global {
      .c7n-pro-input-number-wrapper {
        width: 100% !important;
        .c7n-pro-input-number {
          .c7n-pro-input-number-range-text {
            .c7n-pro-input-number-range-split {
              opacity: 0;
            }
          }
          .c7n-pro-input-number-range-text::after {
            content: ',';
            position: absolute;
            left: 50%;
            top: 18%;
          }
        }
        .c7n-pro-input-number-prefix {
          .IconFront {
            cursor: pointer;
          }
          .IconDisFront {
            cursor: not-allowed;
            color: #d9d9d9;
          }
        }
        .c7n-pro-input-number-suffix {
          .IconFront {
            cursor: pointer;
            color: rgb(89, 89, 89) !important;
          }
          .IconDisFront {
            cursor: not-allowed;
            color: #d9d9d9;
          }
        }
      }
      .TipStyle {
        display: initial;
        margin-top: 28px;
        margin-left: 112px;
        position: absolute;
        color: #b5b5b5;
      }
      .addIcon {
        color: rgb(8, 64, 248) !important;
        border: none !important;
        margin-top: 5px;
        width: 16px !important;
        height: 16px !important;
        margin-left: 8px;
        .icon-add {
          font-size: 14px;
          margin-left: -4px;
          margin-top: -2px;
        }
      }
      .c7n-btn-circle.c7n-btn-sm,
      .c7n-btn-circle-outline.c7n-btn-sm {
        width: 16px;
        height: 16px;
        padding: 0 2px 0 5px;
        font-size: 12px;
        border-radius: 50%;
      }
      .removeIcon {
        padding: 1;
        color: rgb(8, 64, 248) !important;
        border: none !important;
        margin-top: 5px;
        width: 16px !important;
        height: 16px !important;
        margin-left: 10px;
        .icon-remove {
          font-size: 14px;
          margin-left: -4px;
          margin-top: -2px;
        }
        .c7n-btn-circle.c7n-btn-sm,
        .c7n-btn-circle-outline.c7n-btn-sm {
          width: 16px;
          height: 16px;
          padding: 0 2px 0 5px;
          font-size: 12px;
          border-radius: 50%;
        }
      }
    }
  }

  .input-text {
    width: 100%;
    display: flex;
    :global {
      .c7n-pro-input-wrapper {
        width: 100% !important;
      }
    }
    :global {
      .addIcon {
        color: rgb(8, 64, 248) !important;
        border: none !important;
        margin-top: 5px;
        width: 16px !important;
        height: 16px !important;
        margin-left: 8px;
        .icon-add {
          font-size: 14px;
          margin-left: -4px;
          margin-top: -2px;
        }
      }
      .c7n-btn-circle.c7n-btn-sm,
      .c7n-btn-circle-outline.c7n-btn-sm {
        width: 16px;
        height: 16px;
        padding: 0 2px 0 5px;
        font-size: 12px;
        border-radius: 50%;
      }
      .removeIcon {
        padding: 1;
        color: rgb(8, 64, 248) !important;
        border: none !important;
        margin-top: 5px;
        width: 16px !important;
        height: 16px !important;
        margin-left: 10px;
        .icon-remove {
          font-size: 14px;
          margin-left: -4px;
          margin-top: -2px;
        }
        .c7n-btn-circle.c7n-btn-sm,
        .c7n-btn-circle-outline.c7n-btn-sm {
          width: 16px;
          height: 16px;
          padding: 0 2px 0 5px;
          font-size: 12px;
          border-radius: 50%;
        }
      }
    }
  }

  .hcm-dataItem-group {
    display: flex;
    align-items: baseline;

    &-add-null {
      width: 27px;
      flex-shrink: 0;
      height: 25px;
      text-align: center;
      padding: 0;
      margin-top: 2px;
    }
  }

  .copy-radio {
    display: inline-block;
    width: 20px;
    text-align: center;
    line-height: 26px;
    cursor: pointer;
    color: rgb(8, 64, 248);
  }

  .icon-add-text {
    position: relative;
    margin-left: 6px;
    color: rgb(8, 64, 248);
    line-height: 28px;
    cursor: pointer;
  }
  .icon-right-box {
    display: flex;
    width: 50px;
    flex-shrink: 0;
    align-items: baseline;
  }
}

.input-group-maintenance-batch-edit {
  :global {
    .c7n-pro-field-wrapper .c7n-pro-form-item-group .c7n-pro-form-item-group-item:first-child {
      width: 100%;
      span {
        width: 100%;
      }
    }
  }
}
