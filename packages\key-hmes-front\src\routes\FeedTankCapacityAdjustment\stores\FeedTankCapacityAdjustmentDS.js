import { Host } from '@/utils/config';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/yp-mes-38510`;
const modelPrompt = 'tarzan.hmes.FeedTankCapacityAdjustment';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'workcellMaterialLotId',
    paging: true,
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'equipmentLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('搅拌机编码'),
        lovCode: 'HME.JB_EQUIPMENT',
        labelWidth: 150,
        textField: 'equipmentCode',
        required: true,
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'equipmentCode',
        type: 'string',
        bind: 'equipmentLov.equipmentCode',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('搅拌机编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('搅拌机名称'),
        bind: 'equipmentLov.equipmentName',
      },
      {
        name: 'workcellCode',
        type: 'string',
        bind: 'equipmentLov.workcellCode',
      },
      {
        name: 'workcellId',
        type: 'string',
        bind: 'equipmentLov.workcellId',
      },
      {
        name: 'assembleObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.assemblePointCode`).d('投料罐编码'),
        lovCode: 'HME.JB_PERMISSION_ASSEMBLE_POINT_LOV',
        labelWidth: 150,
        textField: 'assemblePointCode',
        required: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: getCurrentOrganizationId(),
              workcellCode: record.get('workcellCode'),
              equipmentCode: record.get('equipmentCode'),
            };
          },
          disabled: ({ record }) => {
            return !record.get('workcellCode');
          },
        },
      },
      {
        name: 'assemblePointId',
        type: 'number',
        bind: 'assembleObj.assemblePointId',
      },
      {
        name: 'assemblePointCode',
        type: 'string',
        bind: 'assembleObj.assemblePointCode',
        label: intl.get(`${modelPrompt}.assemblePointCode`).d('投料罐编码'),
      },
      {
        name: 'assemblePointName',
        type: 'string',
        label: intl.get(`${modelPrompt}.assemblePointName`).d('投料罐名称'),
        bind: 'assembleObj.assemblePointName',
      },
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
        lovCode: 'HME.ASSEMBLE_GROUP_MATERIAL_LOV',
        labelWidth: 150,
        textField: 'materialCode',
        required: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: getCurrentOrganizationId(),
              assemblePointId: record.get('assemblePointId'),
              workcellId: record.get('workcellId'),
            };
          },
          disabled: ({ record }) => {
            return !record.get('assemblePointId');
          },
        },
      },
      {
        name: 'materialCode',
        type: 'string',
        bind: 'materialObj.materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
        bind: 'materialObj.materialName',
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        bind: 'materialObj.revisionCode',
      },
      {
        name: 'materialLotCode',
        type: 'string',
        required: true,
        label: intl.get(`${modelPrompt}.materialLotCode`).d('原材料条码'),
      },
      {
        name: 'primaryUomQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.primaryUomQty`).d('剩余数量'),
      },
      {
        name: 'creationDate',
        type:'dateTime',
        required: true,
        label: intl.get(`${modelPrompt}.creationDate`).d('投料时间'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        required: true,
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
      {
        name: 'createdBy',
        type: 'string',
        label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
        
      },
      {
        name: 'createdRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
        defaultValue:  getCurrentUser().realName,
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
      {
        name: 'lastUpdatedRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdatedRealName`).d('最后更新人'),
      },
    ],
    queryFields: [
      {
        name: 'assembleLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.assembleLov`).d('投料罐编码'),
        lovCode: 'HME.JB_ASSEMBLE_POINT_LOV',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        dynamicProps: {
          required: ({ record }) =>
            !record.get('equipLov') && !record.get('materialLov')?.length && !record.get('identifications'),
        },
      },
      {
        name: 'assemblePointCode',
        type: 'string',
        bind: 'assembleLov.assemblePointCode',
      },
      {
        name: 'equipLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.equipLov`).d('设备编码'),
        lovCode: 'HME.JB_EQUIPMENT',
        labelWidth: 150,
        ignore: 'always',
        dynamicProps: {
          required: ({ record }) =>
            !record.get('assembleLov') && !record.get('materialLov')?.length&& !record.get('identifications'),
        },
      },
      {
        name: 'workcellId',
        type: 'number',
        bind: 'equipLov.workcellId',
      },
      {
        name: 'materialLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        lovCode: 'MT.MATERIAL.PERMISSION',
        lovPara: { tenantId },
        ignore: 'always',
        multiple: true,
        dynamicProps: {
          required: ({ record }) =>
            !record.get('assembleLov') && !record.get('equipLov') && !record.get('identifications'),
        },
      },
      {
        name: 'materialIds',
        bind: 'materialLov.materialId',
      },
      {
        name: 'identifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('原材料条码'),
        dynamicProps: {
          required: ({ record }) =>
            !record.get('assembleLov') && !record.get('equipLov') && !record.get('materialLov')?.length,
        },
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-adjustment-of-feeding-tank-capacity/query/list`,
          method: 'GET',
        };
      },
    },
  };
};


export { tableDS };
