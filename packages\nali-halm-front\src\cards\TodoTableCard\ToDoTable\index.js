import React, { Component } from 'react';
import { withRouter } from 'react-router';
import { Bind } from 'lodash-decorators';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Collapse, Tabs, Tooltip } from 'choerodon-ui';
import intl from 'utils/intl';
import cls from 'classnames';
import { ASSET_TRANSACTION_BASIC_TYPE } from 'alm/pages/AssetTransactionBasicType/transactionInfoConfig';
import { myPendingDS, proposeDS, finishDS } from 'alm/pages/AssetWorkBench/Stores';
import styles from './index.module.less';

const viewPrompt = `aatn.assetWorkBench.view.message`;

@withRouter
class ToDoTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      myPendingCount: 0, // 我的待办数量
      proposeCount: 0, // 我发起的数量
    };
    this.myPendingDS = new DataSet(myPendingDS(this._setState));
    this.proposeDS = new DataSet(proposeDS(this._setState));
    this.finishDS = new DataSet(finishDS());
  }

  @Bind()
  _setState(object) {
    this.setState(object);
  }

  @Bind()
  handleGoToDetail(key, editFlag = false, record) {
    const typeCode = record.get('typeCode');
    const transTypeCode = record.get('transTypeCode');
    const transType = ASSET_TRANSACTION_BASIC_TYPE.find(i => i.basicCode === transTypeCode) || {};
    let pathname = '';
    switch (typeCode) {
      // 验收单
      case 'ACCEPTANCE':
        pathname = `/arcv/acceptance/detail/${key}`;
        break;
      // 盘点任务
      case 'COUNTING':
        pathname = `/actn/counting-tasks/detail/${key}`;
        break;
      // 资产事务处理
      case 'TRANSACTION':
        pathname = `/aatn/asset-transaction-basic-type/execute-handle/${transType.routeType}/list`;
        break;
      default:
        pathname = `/aafm/equipment-asset/detail/${key}`;
        break;
    }
    this.props.history.push({
      pathname,
      state: {
        editFlag,
        changeNum: record.get('code'),
      },
    });
  }

  // 获取表格展示列
  get countingColumns() {
    return [
      {
        name: 'code',
        align: 'center',
        tooltip: 'overflow',
        renderer: ({ text, record }) => (
          <a onClick={() => this.handleGoToDetail(record.get('id'), false, record)}>{text}</a>
        ),
      },
      {
        name: 'name',
        align: 'center',
        tooltip: 'overflow',
      },
      { name: 'type', align: 'center', tooltip: 'overflow' },
      { name: 'director', align: 'center', tooltip: 'overflow' },
      { name: 'date', align: 'center', tooltip: 'overflow' },
      { name: 'count', align: 'center', tooltip: 'overflow' },
      {
        name: 'description',
        renderer: ({ text }) => (
          <Tooltip placement="topLeft" title={text}>
            {text}
          </Tooltip>
        ),
      },
    ];
  }

  render() {
    return (
      <div className={styles['to-do-list']}>
        <Collapse
          bordered={false}
          expandIconPosition="right"
          defaultActiveKey={['A']}
          trigger="icon"
          className={cls(styles['customize-collapse'], styles['table-auto-height'])}
        >
          <Collapse.Panel
            key="A"
            showArrow={false}
            header={intl.get(`${viewPrompt}.panel.toDoList`).d('待办单据-资产')}
          >
            <Tabs defaultActiveKey="1" style={{ height: '100%' }}>
              <Tabs.TabPane
                tab={`${intl.get(`${viewPrompt}.panel.myTodo`).d('我的待办')}(${
                  this.state.myPendingCount
                })`}
                key="1"
              >
                <Table
                  virtual
                  virtualCell
                  autoHeight={{ type: 'maxHeight', diff: 10 }}
                  key="myPendingTable"
                  dataSet={this.myPendingDS}
                  columns={this.countingColumns}
                />
              </Tabs.TabPane>
              <Tabs.TabPane
                tab={`${intl.get(`${viewPrompt}.panel.requestOfMine`).d('我发起的')}(${
                  this.state.proposeCount
                })`}
                key="2"
              >
                <Table
                  virtual
                  virtualCell
                  autoHeight={{ type: 'maxHeight', diff: 10 }}
                  key="proposeTable"
                  dataSet={this.proposeDS}
                  columns={this.countingColumns}
                />
              </Tabs.TabPane>
              <Tabs.TabPane
                tab={`${intl.get(`${viewPrompt}.panel.recentlyCompleted`).d('最近完成')}`}
                key="3"
              >
                <Table
                  virtual
                  virtualCell
                  autoHeight={{ type: 'maxHeight', diff: 10 }}
                  key="finishTable"
                  dataSet={this.finishDS}
                  columns={this.countingColumns}
                />
              </Tabs.TabPane>
            </Tabs>
          </Collapse.Panel>
        </Collapse>
      </div>
    );
  }
}

export default ToDoTable;
