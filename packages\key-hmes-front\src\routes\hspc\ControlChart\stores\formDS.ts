import intl from 'utils/intl';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import {BASIC} from "@utils/config";

const modelPrompt = 'tarzan.hspc.controlChartMaintain';

const tenantId = getCurrentOrganizationId();

const formDS: () => DataSetProps = () => ({
  forceValidate: true,
  selection: false,
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'dataOrigin',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataOrigin`).d('数据来源系统'),
      lookupCode: 'MT.SPC.SYSTEM_SPC',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'inspectBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      lookupUrl: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-bus-type-rule/lov`,
      textField: 'inspectBusinessTypeDesc',
      valueField: 'inspectBusinessType',
      dynamicProps: {
        disabled: ({ record }) => !record?.get('dataOrigin'),
        required: ({ record }) => record?.get('dataOrigin') === 'QMS',
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          page: -1,
        }),
      },
    },
    {
      name: 'inspectSchemeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectSchemeCode`).d('检验方案'),
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.QMS.INSPECT_SCHEME',
      dynamicProps: {
        required: ({ record }) => record?.get('dataOrigin') === 'QMS',
        lovPara: ({ record }) => ({
          tenantId,
          inspectBusinessType: record?.get('inspectBusinessType'),
          materialId: record?.get('materialId'),
        }),
      },
    },
    {
      name: 'inspectSchemeId',
      bind: 'inspectSchemeLov.inspectSchemeId',
    },
    {
      name: 'inspectSchemeCode',
      bind: 'inspectSchemeLov.inspectSchemeCode',
    },
    {
      name: 'inspectSchemeDesc',
      bind: 'inspectSchemeLov.inspectSchemeObjectTypeDesc',
    },
    {
      name: 'inspectItemLov',
      label: intl.get(`${modelPrompt}.inspectionItem`).d('检验项目'),
      lovCode: 'APRS.QMS.INSPECT_ITEM_SCHEME',
      type: FieldType.object,
      ignore: FieldIgnore.always,
      textField: 'inspectItemCode',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('inspectSchemeId');
        },
        required: ({ record }) => record?.get('dataOrigin') === 'QMS',
        lovPara: ({ record }) => {
          return {
            tenantId,
            inspectSchemeId: record?.get('inspectSchemeId'),
            inspectBusinessType: record?.get('inspectBusinessType'),
          };
        },
      },
    },
    {
      name: 'inspectItemCode',
      bind: 'inspectItemLov.inspectItemCode',
    },
    {
      name: 'inspectItemDesc',
      bind: 'inspectItemLov.inspectItemDesc',
    },
    {
      name: 'materialCodeLov',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'APEX_WMS.MATERIAL',
      textField: 'materialCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      bind: 'materialCodeLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialCodeLov.materialCode',
    },
    {
      name: 'materialDesc',
      bind: 'materialCodeLov.materialName',
    },
    {
      name: 'supplierLov',
      label: intl.get(`${modelPrompt}.supplier`).d('供应商编码'),
      type: FieldType.object,
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SUPPLIER',
      textField: 'supplierCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierCode',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierDesc',
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'customerCode',
      bind: 'customerLov.customerCode',
    },
    {
      name: 'customerName',
      bind: 'customerLov.customerName',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        workcellType: 'PROCESS',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'processWorkcellId',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'processWorkcellCode',
      bind: 'processWorkcellLov.workcellCode',
    },
    {
      name: 'processWorkcellName',
      bind: 'processWorkcellLov.workcellTypeDesc',
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备编码'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'equipmentCode',
      bind: 'equipmentLov.equipmentCode',
    },
    {
      name: 'equipmentName',
      bind: 'equipmentLov.equipmentDesc',
    },
    {
      name: 'dataGroupLov',
      label: intl.get(`${modelPrompt}.dataGroup`).d('数据采集组编码'),
      type: FieldType.object,
      lovCode: 'MT.SPC.INSPECT_GROUP_TAG',
      textField: 'tagGroupCode',
      dynamicProps: {
        required: ({ record }) => record?.get('dataOrigin') === 'MES',
        lovPara: ({ record }) => {
          return {
            tenantId,
            equipmentId: record.get('equipmentId'),
          };
        },
      },
    },
    {
      name: 'tagGroupCode',
      bind: 'dataGroupLov.tagGroupCode',
    },
    {
      name: 'tagGroupId',
      bind: 'dataGroupLov.tagGroupId',
    },
    {
      name: 'tagGroupDesc',
      bind: 'dataGroupLov.tagGroupDescription',
    },
    {
      name: 'dataItemLov',
      label: intl.get(`${modelPrompt}.dataItem`).d('数据采集项编码'),
      lovCode: 'MT.SPC.INSPECT_TAG',
      type: FieldType.object,
      textField: 'tagCode',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('dataGroupLov');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            tagGroupId: record.get('tagGroupId'),
          };
        },
        required: ({ record }) => record?.get('dataOrigin') === 'MES',
      },
    },
    {
      name: 'tagCode',
      bind: 'dataItemLov.tagCode',
    },
    {
      name: 'tagId',
      bind: 'dataItemLov.tagId',
    },
    {
      name: 'tagDesc',
      bind: 'dataItemLov.tagDescription',
    },
  ],
});

export { formDS };
