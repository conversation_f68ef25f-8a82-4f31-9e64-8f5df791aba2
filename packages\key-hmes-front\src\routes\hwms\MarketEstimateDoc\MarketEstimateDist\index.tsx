/*
 * @Description: 市场活动评估单-详情界面
 * @Author: <<EMAIL>>
 * @Date: 2023-09-15 11:26:11
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-12-04 13:54:47
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  NumberField,
  TextField,
  Select,
  Attachment,
  Dropdown,
  Menu,
  TextArea,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentUserId, getCurrentOrganizationId } from 'utils/utils';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import { detailDS } from '../stores/DetailDS';
import {
  QueryUserInfo,
  SaveMarketEstimate,
  SubmitMarketEstimate,
  ReviewMarketEstimate,
  StartMarketActive,
  QueryMaterialInfo,
  QueryUserEmployeeInfo,
} from '../services';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.qms.marketActive.marketEstimateDoc';
const tenantId = getCurrentOrganizationId();
const userId = getCurrentUserId();

const MarketEstimateDetail = props => {
  const {
    history,
    match: { params },
    location: { state },
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState<boolean>(false);
  const [marketEstimateStatus, setStatus] = useState<string>('NEW');
  const [marketEstimateResult, setResult] = useState<string>('');
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const { run: queryUserInfo } = useRequest(QueryUserInfo(), {
    manual: true,
    needPromise: true,
  });
  // 查询物料批信息
  const { run: queryMaterialInfo, loading: queryLoading } = useRequest(QueryMaterialInfo(), {
    manual: true,
  });
  const { run: saveMarketEstimate, loading: saveLoading } = useRequest(SaveMarketEstimate(), {
    manual: true,
    needPromise: true,
  });
  const { run: submitMarketEstimate, loading: submitLoading } = useRequest(SubmitMarketEstimate(), {
    manual: true,
  });
  const { run: reviewMarketEstimate, loading: reviewLoading } = useRequest(ReviewMarketEstimate(), {
    manual: true,
  });
  const { run: startMarketActive, loading: startActiveLoading } = useRequest(StartMarketActive(), {
    manual: true,
  });
  const { run: queryUserEmployeeInfo } = useRequest(QueryUserEmployeeInfo(), {
    manual: true,
    needPromise: true,
  });

  useDataSetEvent(detailDs, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteLov':
        record.set('materialLov', undefined);
        record.set('materialName', undefined);
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    if (kid === 'create') {
      // 新建时
      setCanEdit(true);
      setStatus('NEW');
      handleQueryUser();
      handleQueryEmployee();
      return;
    }
    // 编辑时
    handleQueryDetail();
  }, [kid]);

  useEffect(() => {
    if (Object.keys(props?.location?.state || {}).length === 0) {
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const {
      problemId,
      problemCode,
      problemTitle,
      severityLevel,
      batteryPackModel,
      siteId,
      siteCode,
      siteName,
      materialId,
      materialCode,
      materialName,
      itemCode,
      itemName,
      hostPlant,
      hostPlantName,
      vehicleModel,
      responsiblePersonUnitCompanyId,
      responsiblePersonUnitCompanyName,
    } = state || {};
    detailDs.current?.set('problemId', problemId);
    detailDs.current?.set('problemCode', problemCode);
    detailDs.current?.set('problemTitle', problemTitle);
    detailDs.current?.set('siteId', siteId);
    detailDs.current?.set('siteCode', siteCode);
    detailDs.current?.set('siteName', siteName);
    detailDs.current?.set('faultPhenomenon', problemTitle);
    detailDs.current?.set('severityLevel', severityLevel);
    detailDs.current?.set('batteryPackModel', batteryPackModel);
    detailDs.current?.set('materialId', materialId);
    detailDs.current?.set('materialCode', materialCode);
    detailDs.current?.set('materialName', materialName);
    detailDs.current?.set('itemCode', itemCode);
    detailDs.current?.set('itemName', itemName);
    detailDs.current?.set('customerId', hostPlant);
    detailDs.current?.set('customerName', hostPlantName);
    detailDs.current?.set('vehicleModel', vehicleModel);
    detailDs.current?.set('responsibleDepartmentId', responsiblePersonUnitCompanyId);
    detailDs.current?.set('responsibleDepartmentName', responsiblePersonUnitCompanyName);
    history.replace({ ...history.location, state: undefined });
  }, [props?.location?.state]);

  const handleQueryEmployee = () => {
    queryUserEmployeeInfo({
      queryParams: { userId },
    }).then(res => {
      if (res?.success) {
        detailDs.current?.set('createdBy', userId);
        detailDs.current?.set('createdByName', res?.rows?.name);
      }
    });
  };

  const handleQueryUser = () => {
    queryUserInfo({
      queryParams: {
        lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
        page: 0,
        size: 10,
        tenantId,
        userId,
      },
    }).then(res => {
      const { content, empty, failed, message } = res;
      if (res && failed) {
        notification.error({
          message: message || intl.get('hzero.common.notification.error').d('操作失败'),
        });
        return;
      }
      if (empty) {
        return;
      }
      const userInfo = content?.length ? content[0] : {};
      detailDs.current?.set('applyDepartmentLov', {
        ...userInfo,
      });
    });
  };

  const handleQueryDetail = (marketEstimateId = kid) => {
    detailDs.setQueryParameter('marketEstimateId', marketEstimateId);
    detailDs.query().then(res => {
      const { marketEstimateStatus, marketEstimateResult } = res?.rows || {};
      setStatus(marketEstimateStatus);
      setResult(marketEstimateResult);
    });
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(() => {
    if (kid === 'create') {
      history.push('/hwms/market-quality/market-estimate-doc/list');
    } else {
      setCanEdit(false);
      handleQueryDetail();
    }
  }, []);

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const data = detailDs!.current!.toData();
    const res = await saveMarketEstimate({ params: { ...data } });
    if (res && res.success) {
      notification.success({});
      setCanEdit(false);
      if (kid === 'create') {
        history.push(`/hwms/market-quality/market-estimate-doc/dist/${res.rows}`);
      } else {
        handleQueryDetail();
      }
      return true;
    }
    return false;
  };

  // 提交按钮的回调
  const handleSubmit = () => {
    submitMarketEstimate({
      params: {
        marketEstimateId: kid,
      },
      onSuccess: () => {
        notification.success({});
        handleQueryDetail();
      },
    });
  };

  // 发起市场活动按钮的回调
  const handleStartActive = () => {
    startMarketActive({
      params: detailDs!.current!.toData(),
      onSuccess: () => {
        notification.success({});
        handleQueryDetail();
      },
    });
  };

  const handleChangeMaterial = value => {
    if (value?.materialId) {
      queryMaterialInfo({
        params: value?.materialId,
        onSuccess: res => {
          detailDs.current?.set('batteryPackModel', res);
        },
      });
    } else {
      detailDs.current?.set('batteryPackModel', undefined);
    }
  };

  // 点击审批按钮的回调
  const clickMenu = key => {
    reviewMarketEstimate({
      params: {
        marketEstimateId: kid,
        marketEstimateResult: key,
      },
      onSuccess: () => {
        notification.success({});
        handleQueryDetail();
      },
    });
  };

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item key="PASS">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('PASS')}>
          {intl.get(`${modelPrompt}.button.pass`).d('通过')}
        </a>
      </Menu.Item>
      <Menu.Item key="REJECT">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('REJECT')}>
          {intl.get(`${modelPrompt}.button.reject`).d('驳回')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const enclosureProps: any = {
    bucketName: 'qms',
    bucketDirectory: 'market-estimate-doc',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  return (
    <div className="hmes-style">
      <TarzanSpin
        dataSet={detailDs}
        spinning={saveLoading || submitLoading || reviewLoading || queryLoading}
      >
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('市场活动评估')}
          backPath="/hwms/market-quality/market-estimate-doc/list"
        >
          {canEdit ? (
            <>
              <Button
                color={ButtonColor.primary}
                loading={saveLoading}
                icon="save"
                onClick={handleSave}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <>
              {marketEstimateStatus === 'NEW' && (
                <PermissionButton
                  type="c7n-pro"
                  icon="edit-o"
                  color={ButtonColor.primary}
                  onClick={handleEdit}
                  permissionList={[
                    {
                      code: `${modelPrompt}.dist.button.edit`,
                      type: 'button',
                      meaning: '详情页-编辑新建删除复制按钮',
                    },
                  ]}
                >
                  {intl.get('tarzan.common.button.edit').d('编辑')}
                </PermissionButton>
              )}
              {marketEstimateStatus === 'REVIEWING' && (
                <PermissionButton
                  type="c7n-pro"
                  icon="edit-o"
                  color={ButtonColor.primary}
                  onClick={handleEdit}
                  permissionList={[
                    {
                      code: `${modelPrompt}.dist.button.reviewEdit`,
                      type: 'button',
                      meaning: '详情页-评估信息编辑按钮',
                    },
                  ]}
                >
                  {intl.get(`${modelPrompt}.button.reviewEdit`).d('评估信息编辑')}
                </PermissionButton>
              )}
              <PermissionButton
                type="c7n-pro"
                icon="send-o"
                onClick={handleSubmit}
                loading={submitLoading}
                disabled={marketEstimateStatus !== 'NEW'}
                permissionList={[
                  {
                    code: `${modelPrompt}.dist.button.submit`,
                    type: 'button',
                    meaning: '详情页-提交按钮',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.submit`).d('提交')}
              </PermissionButton>
              <Dropdown
                overlay={menu}
                placement={Placements.bottomRight}
                disabled={marketEstimateStatus !== 'REVIEWING'}
              >
                <PermissionButton
                  type="c7n-pro"
                  icon="check"
                  disabled={marketEstimateStatus !== 'REVIEWING'}
                  loading={reviewLoading}
                  permissionList={[
                    {
                      code: `${modelPrompt}.dist.button.review`,
                      type: 'button',
                      meaning: '详情页-审批按钮',
                    },
                  ]}
                >
                  {intl.get(`${modelPrompt}.button.review`).d('审批')}
                </PermissionButton>
              </Dropdown>
              <PermissionButton
                type="c7n-pro"
                onClick={handleStartActive}
                loading={startActiveLoading}
                disabled={marketEstimateResult !== 'PASS'}
                permissionList={[
                  {
                    code: `${modelPrompt}.dist.button.startActive`,
                    type: 'button',
                    meaning: '详情页-发起市场评估活动按钮',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.startActive`).d('发起市场活动')}
              </PermissionButton>
            </>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basic', 'estimateInfo', 'reviewInfo']}>
            <Panel key="basic" header={intl.get(`${modelPrompt}.title.basic`).d('基本信息')}>
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="marketEstimateCode" />
                <Select name="marketEstimateStatus" />
                <Select name="marketEstimateResult" />
                <Lov name="siteLov" />
                <Lov name="problemLov" />
                <TextField name="problemTitle" />
                <TextField name="createdByName" />
                <DateTimePicker name="creationDate" />
              </Form>
            </Panel>
            <Panel
              key="estimateInfo"
              header={intl.get(`${modelPrompt}.title.estimateInfo`).d('填案信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <Lov name="applyDepartmentLov" />
                <TextField name="faultPhenomenon" />
                <TextField name="occurrence" />
                <Lov name="materialLov" onChange={handleChangeMaterial} />
                <TextField name="ypItemName" />
                <Lov name="customerLov" />
                <TextField name="itemCode" />
                <TextField name="itemName" />
                <Lov name="responsibleDepartmentLov" />
                <Select name="vehicleModel" searchable searchFieldInPopup />
                <TextField name="batteryPackModel" />
                <Select name="severityLevel" />
                <NumberField name="objectQty" />
                <Attachment name="objectRange" {...enclosureProps} />
                <Attachment name="enclosure" {...enclosureProps} />
                <TextArea name="reason" colSpan={3} autoSize={{ minRows: 2, maxRows: 8 }} />
              </Form>
            </Panel>
            <Panel
              key="reviewInfo"
              header={intl.get(`${modelPrompt}.title.reviewInfo`).d('评估信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit || marketEstimateStatus !== 'REVIEWING'} labelWidth={112}>
                <TextArea name="reviewInfo" colSpan={3} autoSize={{ minRows: 2, maxRows: 8 }} />
                <TextField name="reviewByName" />
                <DateTimePicker name="reviewTime" />
                <Attachment name="reviewEnclosure" {...enclosureProps} />
              </Form>
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(MarketEstimateDetail);
