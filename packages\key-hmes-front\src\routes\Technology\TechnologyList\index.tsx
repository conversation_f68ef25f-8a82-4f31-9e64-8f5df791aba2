/**
 * @Description: 工艺维护-列表
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 10:27:56
 * @LastEditTime: 2023-05-18 15:07:09
 * @LastEditors: <<EMAIL>>
 */

import React, { FC, useEffect } from 'react';
import { RouteComponentProps } from 'react-router';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { tableDS } from '../stories/TechnologyListDs';

const modelPrompt = 'tarzan.process.technology.model.technology';

interface ChildStepsProps extends RouteComponentProps {
  tableDs: DataSet;
  customizeTable: any;
}

const ChildSteps: FC<ChildStepsProps> = (props) => {
  const {
    history,
    tableDs,
    match: { path },
    customizeTable,
  } = props;

  useEffect(() => {
    tableDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.OPERATION_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.OPERATION_LIST.LIST`)
    tableDs.query(tableDs.currentPage);
  }, []);

  const createWorkcell = () => {
    history.push(`/hmes/process/technology/dist/create`);
  };

  const showWorkcellDist = record => {
    history.push(`/hmes/process/technology/dist/${record.get('operationId')}`);
  };

  const columns: ColumnProps[] = [
    {
      name: 'operationName',
      width: 200,
      renderer: ({ record, value }) => (
        <>
          <a onClick={() => showWorkcellDist(record)}>{value}</a>
        </>
      ),
    },
    {
      name: 'description',
      width: 200,
    },
    {
      name: 'operationType',
      width: 90,
      align: ColumnAlign.center,
    },
    {
      name: 'operationStatus',
      width: 110,
      align: ColumnAlign.center,
    },
    {
      name: 'revision',
      width: 100,
    },
    {
      name: 'completeInconformityFlag',
      width: 130,
      align: ColumnAlign.center,
      renderer: ({ value }) => {
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`${modelPrompt}.enable`).d('启用')
                : intl.get(`${modelPrompt}.unable`).d('禁用')
            }
          />
        );
      },
    },
    {
      name: 'currentFlag',
      width: 130,
      align: ColumnAlign.center,
      renderer: ({ value }) => {
        return (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`${modelPrompt}.enable`).d('启用')
                : intl.get(`${modelPrompt}.unable`).d('禁用')
            }
          />
        );
      },
    },
    {
      name: 'dateFrom',
      width: 160,
      align: ColumnAlign.center,
    },
    {
      name: 'dateTo',
      width: 160,
      align: ColumnAlign.center,
    },
    {
      name: 'workcellType',
      width: 110,
      align: ColumnAlign.center,
    },
    {
      name: 'workcellCode',
      width: 200,
      align: ColumnAlign.center,
    },
    {
      name: 'standardReqdTimeInProcess',
      width: 160,
      align: ColumnAlign.center,
    },
    {
      name: 'standardMaxLoop',
      width: 110,
      align: ColumnAlign.center,
    },
    {
      name: 'standardSpecialIntroduction',
      align: ColumnAlign.center,
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.techTitle`).d('工艺维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => {
            createWorkcell();
          }}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.OPERATION_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.OPERATION_LIST.LIST`,
          },
          <Table
            searchCode="gywh1"
            customizedCode="gywh1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.process.technology', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.OPERATION_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.OPERATION_LIST.LIST`] }),
)(ChildSteps);
