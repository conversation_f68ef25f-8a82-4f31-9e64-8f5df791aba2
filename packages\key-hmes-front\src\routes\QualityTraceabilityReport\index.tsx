import React from 'react';
import { Tabs } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import {
  MaterialTraceabilityForward,
  MaterialTraceabilityReverse,
  NCCodeQuery,
  DataTraceability,
} from './ReportComponent';

const TabPane = Tabs.TabPane;

const modelPrompt = 'modelPrompt_code';

const QualityTraceabilityReport = () => {

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('质量追溯报表')} />
      <Content>
        <Tabs defaultActiveKey="1">
          <TabPane tab="数据追溯" key="1">
            <DataTraceability />
          </TabPane>
          <TabPane tab="物料追溯正向" key="2">
            <MaterialTraceabilityForward />
          </TabPane>
          <TabPane tab="物料追溯反向" key="3">
            <MaterialTraceabilityReverse />
          </TabPane>
          <TabPane tab="不良代码查询" key="4">
            <NCCodeQuery />
          </TabPane>
        </Tabs>
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(QualityTraceabilityReport);
