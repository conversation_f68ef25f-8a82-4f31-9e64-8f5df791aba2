import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLangs = key => {
  const PREFIX = 'alm.abnormalCkList';
  const MODELPREFIX = `${PREFIX}.model.abnormalCkList`;
  const LANGS = {
    ...getCommonLangs(),
    BTN_CREATE_WO: intl.get(`${PREFIX}.view.button.createWo`).d('创建工作单'),
    BTN_CREATE_SR: intl.get(`${PREFIX}.view.button.createSr`).d('创建服务申请单'),
    TITLE_CREATE_WO: intl.get(`${PREFIX}.view.message.createWoConfirm`).d('是否创建工单？'),
    TITLE_CREATE_SR: intl.get(`${PREFIX}.view.button.createSrConfirm`).d('是否创建SR？'),

    SOURCE_TYPE: intl.get(`${MODELPREFIX}.sourceType`).d('来源'),
    CK_NAME: intl.get(`${MODELPREFIX}.checklistName`).d('异常检查项名称'),
    CK_VALUE: intl.get(`${MODELPREFIX}.actValueDes`).d('异常检查项结果'),
    SUB_SEQ_PROCESS: intl.get(`${MODELPREFIX}.subsequentProcess`).d('后续处理类型'),
    PROCESS_NUM: intl.get(`${MODELPREFIX}.processNum`).d('后续编号'),
    PROCESS_NAME: intl.get(`${MODELPREFIX}.processName`).d('单据名称'),
    PROCESS_DESC: intl.get(`${MODELPREFIX}.processDesc`).d('描述'),
    ALARM_RECORD_CODR: intl.get(`${MODELPREFIX}.alarmRecordCode`).d('告警记录编码'),

    ERROR_NOT_UNIFIED_WO: intl
      .get(`${PREFIX}.view.error.notUnified.WO`)
      .d('设备/位置不同的告警，无法合并创建WO！'),
    ERROR_NOT_UNIFIED_SR: intl
      .get(`${PREFIX}.view.error.notUnified.SR`)
      .d('设备/位置不同的告警，无法合并创建SR！'),
  };
  return LANGS[key];
};

export default getLangs;
