import React from 'react';
import { Badge } from 'hzero-ui';
import intl from 'utils/intl';
import _Badge from 'hzero-ui/lib/badge';

const statusMap = ['error', 'success'];
const commonStyle = {
  width: 40,
  height: 20,
  fontSize: 12,
  lineHeight: '20px',
  display: 'inline-block',
};


/**
 * 返回 启用/禁用 对应的多语言 并加上状态
 * @param {0|1} v 启用状态
 * return 1 ? enable(多语言) : disabled(多语言)
 */
export function enableRender(v) {
  return (
    <Badge
      status={v === 'Y' ? 'success' : 'error'}
      text={
        v === 'Y' ? intl.get('hzero.common.status.enable') : intl.get('hzero.common.status.disable')
      }
    />
  );
}

/**
 * 返回 是/否 多语言 并加上对应的状态
 * @param {1|any} v 值
 * @return 1 -> yes(多语言), other -> no(多语言)
 */
export function yesOrNoRender(v) {
  return v === 1 ? (
    <span style={{ ...commonStyle, color: '#15A22E', backgroundColor: '#D9F0DD' }}>
      {intl.get('hzero.common.status.yes').d('是')}
    </span>
  ) : (
    <span style={{ ...commonStyle, color: '#FF7070', backgroundColor: '#FFE8E8' }}>
      {intl.get('hzero.common.status.no').d('否')}
    </span>
  );
}
/**
 * 返回 通过/不通过 多语言 并加上对应的状态
 * @param {1|any} v 值
 * @return 1 -> yes(多语言), other -> no(多语言)
 */
export function isPassRender(v) {
  return React.createElement(_Badge, {
    status: statusMap[v],
    text:
      v === 1
        ? intl.get('alm.common.status.pass').d('通过')
        : intl.get('alm.common.status.noPass').d('不通过'),
  });
}
