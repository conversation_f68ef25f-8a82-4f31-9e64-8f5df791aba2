/**
 * @Description: 站点维护-生产属性Tab
 * @Author: <<EMAIL>>
 * @Date: 2021-02-04 11:54:54
 * @LastEditTime: 2023-02-28 11:15:08
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, Select, SelectBox, Switch } from 'choerodon-ui/pro';
import { DndProvider } from 'react-dnd-9.3.4';
import HTMLBackend from 'react-dnd-html5-backend-9.3.4';
import intl from 'utils/intl';
import DragComponentsRow from './DragComponentsRow';
import styles from './index.module.less';

const modelPrompt = 'tarzan.model.org.site';
const { Option } = SelectBox;

const ProduceInfoTab = props => {
  const {
    ds,
    canEdit,
    columns = 1,
    focus = true,
    dragList = [],
    setDragList = e => e,
    retrievalOrderRef,
  } = props;

  const attritionCalculateStrategyChange = currentValue => {
    if (currentValue) {
      const _newList = currentValue.map(item => {
        return {
          questionTuid: item,
          questionContent: intl.get(`${modelPrompt}.${item}`),
        };
      });
      setDragList(_newList);
    } else {
      setDragList([]);
    }
  };

  return (
    <>
      <Form
        disabled={!canEdit || focus}
        dataSet={ds}
        columns={columns}
        labelLayout="horizontal"
        labelWidth={112}
      >
        <Select name="attritionCalculateStrategy" />
        <Switch name="ncrCreateFlag" />
        <SelectBox
          name="compWarehousingPrioStrtySeq"
          mode="button"
          className={styles['select-box-address']}
          newLine
          onChange={attritionCalculateStrategyChange}
        >
          <Option value="material">{intl.get(`${modelPrompt}.material`).d('物料')}</Option>
          <Option value="prodLine">{intl.get(`${modelPrompt}.prodLine`).d('生产线')}</Option>
        </SelectBox>
      </Form>
      <DndProvider backend={HTMLBackend}>
        <DragComponentsRow list={dragList} ref={retrievalOrderRef} canEdit={canEdit} />
      </DndProvider>
    </>
  );
};

export default ProduceInfoTab;
