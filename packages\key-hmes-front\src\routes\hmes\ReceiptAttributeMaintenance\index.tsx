/**
 * @Description: 入库属性维护
 */
import React, { useMemo, useCallback } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, TextField, Button, Spin, Lov, Select, Switch, NumberField } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Popconfirm } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import notification from 'utils/notification';
import {ColumnAlign, ColumnLock, TableQueryBarType} from 'choerodon-ui/pro/lib/table/enum';
import { PermissionProvider } from '@components/tarzan-ui';
import { useUserRole, useRequest } from '@components/tarzan-hooks';
import { tableDS } from './stores';
import { DeleteMessage, SaveMessage } from './services';

const modelPrompt = 'tarzan.hmes.receipt.attribute.maintenance';

const DeleteBtn = observer(({ ds }: { ds: DataSet }) => {
  const selectedRows = ds.selected;

  const { run: runDelete, loading } = useRequest(DeleteMessage(), { manual: true });

  // @ts-ignore
  window.dataset = ds

  const deleteMessage = () => {
    const delDataList = selectedRows.filter(item => item.get('inWarehourseId')).map(item => {
      return {
        inWarehourseId: item.get('inWarehourseId'),
      };
    });
    if (!delDataList.length) {
      ds.query();
      return;
    }
    runDelete({
      params: delDataList,
      onSuccess: () => {
        notification.success({
          message: intl.get(`${modelPrompt}.deleteSuccess`).d('删除成功!'),
        });
        ds.query();
      },
    });
  };

  return (
    <Popconfirm
      title={intl
        .get(`${modelPrompt}.confirm.delete`, {
          count: selectedRows.length,
        })
        .d(`总计${selectedRows.length}条数据，是否确认删除?`)}
      onConfirm={deleteMessage}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
      okText={intl.get('tarzan.common.button.confirm').d('确定')}
    >
      <Button loading={loading} icon="delete_black-o" disabled={!selectedRows.length}>
        {intl.get('tarzan.common.button.delete').d('删除')}
      </Button>
    </Popconfirm>
  );
});

const ReceiptAttributeMaintenance = props => {
  const {
    match: { path },
    permissionDetail,
  } = props;

  const userRole = useUserRole();
  const tableDs = useMemo(() => new DataSet(tableDS(permissionDetail!.approve)), []);
  const { run: runSave, loading: saveLoading } = useRequest(SaveMessage(), { manual: true });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'materialLov',
        width: 180,
        editor: record => record.getState('editing') && <Lov name="materialLov" />,
      },
      {
        name: 'materialName',
        width: 180,
      },
      {
        name: 'modelCode',
        width: 180,
      },
      {
        name: 'gradingStatus',
        width: 180,
        editor: record => record.getState('editing') && <Switch name="gradingStatus" />,
      },
      {
        name: 'gradingLabel',
        width: 180,
        editor: record => record.getState('editing') && <Select name="gradingLabel" />,
      },
      {
        name: 'gradingLabelPrint',
        width: 180,
        editor: record => record.getState('editing') && <Select name="gradingLabelPrint" />,
      },
      {
        name: 'gatherItem',
        width: 180,
        editor: record => record.getState('editing') && <Select name="gatherItem" />,
      },
      {
        name: 'assembleMarking',
        width: 180,
        editor: record => record.getState('editing') && <TextField name="assembleMarking"/>,
      },
      {
        name: 'gradingMarking',
        width: 180,
        editor: record => record.getState('editing') && <TextField name="gradingMarking"/>,
      },
      {
        name: 'standardLoadQty',
        width: 180,
        editor: record => record.getState('editing') && <NumberField name="standardLoadQty"/>,
      },
      {
        name: 'positiveTabLength',
        width: 180,
        editor: record => record.getState('editing') && <TextField name="positiveTabLength"/>,
      },
      {
        name: 'negativeTabLength',
        width: 180,
        editor: record => record.getState('editing') && <TextField name="negativeTabLength"/>,
      },
      {
        name: 'tabAppear',
        width: 180,
        editor: record => record.getState('editing') && <Select name="tabAppear" />,
      },
      {
        name: 'voltage',
        width: 180,
        editor: record => record.getState('editing') && <TextField name="voltage"/>,
      },
      {
        name: 'stickGlue',
        width: 180,
        editor: record => record.getState('editing') && <Select name="stickGlue" />,
      },
      {
        name: 'breakSide',
        editor: record => record.getState('editing') && <Select name="breakSide" />,
      },
      {
        name: 'specialRequirements',
        width: 250,
        editor: record =>
          record.getState('editing') && <TextField name="specialRequirements"/>,
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 120,
        renderer: ({ record }: { record?}) => {
          if (record && record.getState('editing')) {
            return (
              <>
                <a onClick={() => handleCancel(record)} style={{ marginRight: '0.1rem' }}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </a>
                <a onClick={() => handleSubmit(record)}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </a>
              </>
            );
          }
          return (
            <PermissionButton
              permissionList={[
                {
                  code: `hmes.receipt.attribute.maintenance.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
              type="text"
              disabled={record.get('initialFlag') === 'Y' && userRole !== 'Y'}
              onClick={() => handleEdit(record)}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          );

        },
      },
    ];
  }, [userRole]);

  const handleAdd = useCallback(() => {
    const newRow = tableDs.create({}, 0);
    newRow.setState('editing', true);
  }, [tableDs]);

  const handleEdit = record => {
    record.setState('editing', true);
  };

  const handleCancel = useCallback(
    record => {
      if (record.status === 'add') {
        tableDs.remove(record);
      } else {
        record.reset();
        record.setState('editing', false);
      }
    },
    [tableDs],
  );

  const handleSubmit = async record => {
    const validateResult = await record?.validate(true);
    if (validateResult) {
      runSave({
        params: [record.toData()],
        onSuccess: (res) => {
          record.setState('editing', false);
          const _data = record.toData();
          record.init({
            ..._data,
            ...res[0],
          });
          notification.success({
            message: intl.get(`${modelPrompt}.saveSuccess`).d('保存成功!'),
          });
          record.reset();
          record.status = 'sync';
        },
      });
    }
  };

  return (
    <div className="hmes-style">
      <Spin spinning={saveLoading}>
        <Header title={intl.get('tarzan.hmes.receipt.attribute.maintenance.title.list').d('入库属性维护')}>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={handleAdd}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
          <DeleteBtn ds={tableDs} />
        </Header>
        <Content>
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="ReceiptAttributeMaintenance"
            customizedCode="ReceiptAttributeMaintenance"
          />
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.receipt.attribute.maintenance', 'tarzan.common'],
})(PermissionProvider({ noDisplayCode: 'button.edit' })(ReceiptAttributeMaintenance));
