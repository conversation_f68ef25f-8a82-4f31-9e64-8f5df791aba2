/**
 * @Description: 量具汇总查询界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:29:43
 * @LastEditTime: 2023-05-24 16:56:17
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import withProps from 'utils/withProps';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { allTabledDS } from '../stories';

const MeterManagementCollect = props => {
  const { tableDS } = props;

  useEffect(() => {
    tableDS.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_LIST.LIST`,
    );

    if (tableDS?.currentPage) {
      tableDS.query(props.tableDS.currentPage);
    } else {
      tableDS.query();
    }
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'siteCode',
      align: ColumnAlign.center,
    },
    {
      name: 'siteName',
      align: ColumnAlign.center,
    },
    {
      name: 'categoryDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'speciesDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'modelCode',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'usingStatusDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationStatusDesc',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'responName',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'range',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'resolution',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'manageCount',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationPeriod',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'verificationMethodDesc',
      width: 120,
      align: ColumnAlign.center,
    },
  ];

  return (
    <Table
      queryBar={TableQueryBarType.filterBar}
      queryBarProps={{
        fuzzyQuery: false,
      }}
      dataSet={tableDS}
      columns={columns}
      searchCode="MeterManagementCollect"
      customizedCode="MeterManagementCollect"
    />
    // <div className="hmes-style">
    //   <Content>
    //     <Table
    //       queryBar={TableQueryBarType.filterBar}
    //       queryBarProps={{
    //         fuzzyQuery: false,
    //       }}
    //       dataSet={tableDS}
    //       columns={columns}
    //       searchCode="MeterManagementCollect"
    //       customizedCode="MeterManagementCollect"
    //     />
    //     ,
    //   </Content>
    // </div>
  );
};

export default withProps(
  () => {
    const tableDS = new DataSet({
      ...allTabledDS(),
    });
    return {
      tableDS,
    };
  },
  { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
)(MeterManagementCollect);
