/**
 * @Description: 控制控制图-新建页
 * @Author: <<EMAIL>>
 * @Date: 2021-11-26 13:55:24
 * @LastEditTime: 2021-12-13 16:44:25
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useReducer, useMemo } from 'react';
import { Button, DataSet } from 'choerodon-ui/pro';
import { Spin, Steps } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import DefineSampleDataStructure from './StepsTabs/DefineSampleDataStructure';
import DefineControlChartInfo from './StepsTabs/DefineControlChartInfo';
import DefineControlChart from './StepsTabs/DefineControlChart';
import {
  controlChartDS,
  controlChartInfoDS,
  controlChartDetailInfoDS,
  sampleDataFilteringRulesDS,
  groupBatchRuleDS,
} from '../stores/ControlChartDS';
import { SaveControlChartDetail, FetchAnalysisControlChartDetail } from '../services';

const { Step } = Steps;
const modelPrompt = 'tarzan.hspc.controlChartMaintain';

function tableStructureReducer(state, action) {
  switch (action.type) {
    case 'update/MEASURE':
      return { ...state, MEASURE: action.payload };
    case 'update/COUNT':
      return { ...state, COUNT: action.payload };
    case 'update':
      return { ...state, ...action.payload };
    default:
      throw new Error();
  }
}

const initialTableState = {
  selectedSampleType: 'MEASURE',
  MEASURE: {
    1: intl.get(`${modelPrompt}.time/sample`).d('时间/样本属性'),
    2: `${intl.get(`${modelPrompt}.additionalProp`).d('研究特性')}1`,
  },
  COUNT: {
    1: intl.get(`${modelPrompt}.time/sample`).d('时间/样本属性'),
    2: intl.get(`${modelPrompt}.sampleData`).d('样本数'),
    3: intl.get(`${modelPrompt}.unqualifiedNumber`).d('不合格数'),
  },
};

const AnalysisControlChartCreatePage = props => {
  const { match, history } = props;
  const { code: analysisChartCode } = match.params;

  const [currentStepIndex, setCurrentStepIndex] = useState(0); // 记录当前是第几部  0为第一步
  const [tableStructure, tableStructureDispatch] = useReducer(
    tableStructureReducer,
    initialTableState,
  ); // 图形和表格使用的所有数据

  const controlChartDs = useMemo(() => new DataSet({ ...controlChartDS() }), []);
  const controlChartInfoDs = useMemo(() => new DataSet({ ...controlChartInfoDS() }), []);
  const mainControlChartDetailInfoDs = useMemo(
    () => new DataSet({ ...controlChartDetailInfoDS() }),
    [],
  );
  const secondaryControlChartDetailInfoDs = useMemo(
    () => new DataSet({ ...controlChartDetailInfoDS() }),
    [],
  );
  const sampleDataFilteringRulesDs = useMemo(
    () => new DataSet({ ...sampleDataFilteringRulesDS() }),
    [],
  );
  const groupBatchRuleDs = useMemo(() => new DataSet({ ...groupBatchRuleDS() }), []);

  const saveControlChartDetail = useRequest(SaveControlChartDetail(), {
    manual: true,
    needPromise: true,
  });
  const fetchAnalysisControlChartDetail = useRequest(FetchAnalysisControlChartDetail(), {
    manual: true,
  });

  // const pageLoading =
  // fetchSubjectSampleDataTableList.loading ||
  // fetchAnalysisChartCodeAndDesc.loading ||
  // calcThenFetchCountChartData.loading ||
  // calcThenFetchMeasureChartData.loading ||
  // fetchCPKData.loading ||
  // createAnalysisControlChart.loading;

  useEffect(() => {
    // 路由发生改变时，跳转到第三步
    if (analysisChartCode === 'new') {
      return;
    }
    resetCreatePageInfo();
  }, [analysisChartCode]);

  const resetCreatePageInfo = () => {
    fetchAnalysisControlChartDetail.run({
      params: {
        analysisCode: analysisChartCode,
      },
      onSuccess: res => {
        tableStructureDispatch({
          type: 'update',
          payload: {
            selectedSampleType: res.sampleType,
            [res.sampleType]: res.tableStructure,
          },
        });
        controlChartInfoDs!.current!.set({
          chartType: res.chartType,
          subgroupSize: res.subgroupSize,
          maxPlotPoints: 50,
          chartTitle: res.chartTitle,
          xTickLabel: res.xTickLabel,
          attribute3: res.attribute3,
        });
        mainControlChartDetailInfoDs!.current!.set({
          analysisCode: analysisChartCode,
          ...res.mainChartConfigure,
        });
        if (res.sampleType === 'MEASURE') {
          secondaryControlChartDetailInfoDs!.current!.set({
            analysisCode: analysisChartCode,
            ...res.secondaryChartConfigure,
          });
        }
        setCurrentStepIndex(2);
        if (res.attribute3) {
          mainControlChartDetailInfoDs.setState('required', true);
          secondaryControlChartDetailInfoDs.setState('required', true);
        } else {
          mainControlChartDetailInfoDs.setState('required', false);
          secondaryControlChartDetailInfoDs.setState('required', false);
        }
      },
    });
  };

  const steps = [
    {
      stepIndex: 0,
      title: intl.get(`${modelPrompt}.step1`).d('定义样本数据结构'),
      description: intl.get(`${modelPrompt}.step1Desc`).d('定义控制阶段样本数据结构'),
      content: (
        <DefineSampleDataStructure
          tableStructure={tableStructure}
          dispatch={tableStructureDispatch}
          analysisChartCode={analysisChartCode}
          history={history}
        />
      ),
    },
    {
      stepIndex: 1,
      title: intl.get(`${modelPrompt}.step2`).d('定义控制图信息'),
      description: intl.get(`${modelPrompt}.step2Desc`).d('定义控制图类型、判异规则等信息'),
      content: (
        <DefineControlChartInfo
          analysisChartCode={analysisChartCode}
          controlChartInfoDs={controlChartInfoDs}
          mainControlChartDetailInfoDs={mainControlChartDetailInfoDs}
          secondaryControlChartDetailInfoDs={secondaryControlChartDetailInfoDs}
          tableStructure={tableStructure}
        />
      ),
    },
    {
      stepIndex: 2,
      title: intl.get(`${modelPrompt}.step3`).d('定义控制控制图'),
      description: intl.get(`${modelPrompt}.step3Desc`).d('定义控制图信息、数据筛选规则'),
      content: (
        <DefineControlChart
          controlChartDs={controlChartDs}
          sampleDataFilteringRulesDs={sampleDataFilteringRulesDs}
          groupBatchRuleDs={groupBatchRuleDs}
          tableStructure={tableStructure}
        />
      ),
    },
  ];

  /**
   * 进入第「stepIndex」步
   * 每次进入下一步，需要校验之前的所有步骤
   * @param {number} stepIndex
   */
  const handleChangeStep = async (stepIndex: number) => {
    if ([0, 1].includes(stepIndex)) {
      // 进入第1步
      setCurrentStepIndex(stepIndex);
    }
    if (stepIndex === 2) {
      // 进入第3步
      const validate2 = await validateStep2();
      if (!validate2) {
        return;
      }
      setCurrentStepIndex(stepIndex);
    }
  };

  const validateStep2 = async () => {
    const validate1 = await controlChartInfoDs.validate();
    const validate2 = await mainControlChartDetailInfoDs.validate();
    if (tableStructure.selectedSampleType === 'MEASURE') {
      const validate3 = await secondaryControlChartDetailInfoDs.validate();
      return validate1 && validate2 && validate3;
    }
    return validate1 && validate2;
  };

  const validateStep3 = async () => {
    const validate1 = await controlChartDs.validate();
    const validate2 = await sampleDataFilteringRulesDs.validate();
    const validate3 = await validateStep2();
    const groupBatchRuleDsValidate = await groupBatchRuleDs.validate(); // 组批信息
    return validate1 && validate2 && validate3 && groupBatchRuleDsValidate;
  };

  const prev = () => {
    handleChangeStep(currentStepIndex - 1);
  };
  const next = () => {
    handleChangeStep(currentStepIndex + 1);
  };
  const complete = async () => {
    const validate = await validateStep3();
    if (!validate) {
      return;
    }
    const saveParams = {
      ...controlChartDs!.current!.toData(),
      ...controlChartInfoDs!.current!.toData(),
      ...sampleDataFilteringRulesDs!.current!.toData(),
      ...groupBatchRuleDs!.current!.toData(),
      mainChartConfigure: { ...mainControlChartDetailInfoDs!.current!.toData() },
      tableStructure: tableStructure[tableStructure.selectedSampleType],
    };
    if (tableStructure.selectedSampleType === 'MEASURE') {
      saveParams.secondaryChartConfigure = {
        ...secondaryControlChartDetailInfoDs!.current!.toData(),
      };
    }
    await saveControlChartDetail.run({
      params: saveParams,
      onSuccess: () => {
        notification.success({});
        history.push('/hspc/control-chart/list');
      },
    });
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.createControlChartMaintain`).d('新建分析控制图')}
        backPath="/hspc/control-chart/list"
      />
      <Content>
        <Steps current={currentStepIndex}>
          {steps.map(step => {
            return (
              <Step
                style={{ cursor: 'pointer' }}
                title={step.title}
                description={step.description}
                onClick={() => handleChangeStep(step.stepIndex)}
              />
            );
          })}
        </Steps>
        <div>
          <Spin spinning={false}>
            {steps[currentStepIndex].content}
            <div style={{ float: 'right', marginTop: '24px' }}>
              {currentStepIndex > 0 && (
                <Button onClick={prev}>{intl.get(`${modelPrompt}.previous`).d('上一步')}</Button>
              )}
              {currentStepIndex < steps.length - 1 && (
                <Button color={ButtonColor.primary} onClick={next} style={{ marginLeft: '8px' }}>
                  {intl.get(`${modelPrompt}.next`).d('下一步')}
                </Button>
              )}
              {currentStepIndex === steps.length - 1 && (
                <Button color={ButtonColor.primary} onClick={complete}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
              )}
            </div>
          </Spin>
        </div>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hspc.controlChartMaintain', 'tarzan.hspc.chartInfo', 'tarzan.common'],
})(AnalysisControlChartCreatePage);
