import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { Host } from '@/utils/config';

const modelPrompt = 'QuadraticDecision';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  primaryKey: 'doubleCheckTagId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'tagGroupLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagGroup`).d('收集组编码'),
      lovCode: 'YP_MES.TAG_GROUP',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'tagGroupCode',
    },
    {
      name: 'tagGroupId',
      type: FieldType.number,
      bind: 'tagGroupLov.tagGroupId',
    },
    {
      name: 'tagGroupCode',
      type: FieldType.string,
      bind: 'tagGroupLov.tagGroupCode',
    },
    {
      name: 'tagGroupDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupDescription`).d('收集组描述'),
    },
    {
      name: 'tagLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tag`).d('收集项编码'),
      lovCode: 'HME_MT_TAG',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'tagCode',
    },
    {
      name: 'tagId',
      type: FieldType.number,
      bind: 'tagLov.tagId',
    },
    {
      name: 'tagCode',
      type: FieldType.string,
      bind: 'tagLov.tagCode',
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagGroupDescription`).d('收集项描述'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'materialCode',
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'equipmentCode',
    },
    {
      name: 'equipmentId',
      type: FieldType.number,
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      bind: 'equipmentLov.equipmentCode',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
  ],
  fields: [
    {
      name: 'tagGroupLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagGroup`).d('收集组编码'),
      lovCode: 'HME.DOUBLE_CHECK_TAG_GROUP',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      textField: 'tagGroupCode',
      required: true,
    },
    {
      name: 'tagGroupId',
      type: FieldType.number,
      bind: 'tagGroupLov.tagGroupId',
    },
    {
      name: 'tagGroupCode',
      type: FieldType.string,
      bind: 'tagGroupLov.tagGroupCode',
    },
    {
      name: 'tagGroupDescription',
      type: FieldType.string,
      bind: 'tagGroupLov.tagGroupDescription',
      label: intl.get(`${modelPrompt}.tagGroupDescription`).d('收集组描述'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'tagGroupLov.materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('关联物料编码'),
    },
    {
      name: 'materialId',
      bind: 'tagGroupLov.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      bind: 'tagGroupLov.revisionCode',
      label: intl.get(`${modelPrompt}.revisionCode`).d('关联物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      bind: 'tagGroupLov.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('关联物料名称'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      bind: 'tagGroupLov.equipmentCode',
      label: intl.get(`${modelPrompt}.equipmentCode`).d('关联设备编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      bind: 'tagGroupLov.equipmentName',
      label: intl.get(`${modelPrompt}.equipmentName`).d('关联设备名称'),
    },
    {
      name: 'tagLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tag`).d('收集项编码'),
      lovCode: 'HME.DOUBLE_CHECK_TAG',
      ignore: FieldIgnore.always,
      textField: 'tagCode',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tagGroupId: record.get('tagGroupId'),
            tenantId,
          };
        },
        disabled: ({ record }) => !record.get('tagGroupId'),
      },
      required: true,
    },
    {
      name: 'tagId',
      type: FieldType.number,
      bind: 'tagLov.tagId',
    },
    {
      name: 'tagCode',
      type: FieldType.string,
      bind: 'tagLov.tagCode',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
      defaultValue: 'Y',
      required: true,
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      bind: 'tagLov.tagDescription',
      label: intl.get(`${modelPrompt}.tagDescription`).d('收集项描述'),
    },
    {
      name: 'valueType',
      type: FieldType.string,
      bind: 'tagLov.valueType',
    },
    {
      name: 'valueTypeMeaning',
      type: FieldType.string,
      bind: 'tagLov.valueTypeMeaning',
      label: intl.get(`${modelPrompt}.valueType`).d('收集项类型'),
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      bind: 'tagLov.trueValue',
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
    },
    {
      name: 'trueValueList',
      bind: 'tagLov.trueValueList',
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      bind: 'tagLov.falseValue',
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
    },
    {
      name: 'falseValueList',
      bind: 'tagLov.falseValueList',
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      bind: 'tagLov.uomCode',
      label: intl.get(`${modelPrompt}.uomCode`).d('计量单位'),
    },
    {
      name: 'valueList',
      type: FieldType.string,
      bind: 'tagLov.valueList',
      label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
    },
    {
      name: 'dateFormat',
      type: FieldType.string,
      bind: 'tagLov.dateFormat',
      label: intl.get(`${modelPrompt}.dateFormat`).d('日期格式'),
      lookupCode: 'MT.MES_DATE_FORMAT',
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      bind: 'tagLov.ncCode',
      label: intl.get(`${modelPrompt}.ncCode`).d('默认不良代码'),
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.ncCode`).d('默认不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      textField: 'ncCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'ncCodes',
      bind: 'ncCodeLov.ncCode',
    },
    {
      name: 'uomCodeLov',
      type: FieldType.object,
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.uomCode`).d('计量单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
      },
      textField: 'uomCode',
    },
    {
      name: 'uomCodes',
      bind: 'uomCodeLov.uomCode',
    },
    {
      name: 'updateFlag',
      type: FieldType.string,
      defaultValue: 'Y',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-double-check-tags/list/ui`,
        method: 'POST',
      };
    },
  },
});


const numberListDS: () => DataSetProps = () => ({
  autoCreate: true,
  autoLocateFirst: true,
  dataKey: 'rows',
  fields: [
    { name: 'dataValue' },
    {
      name: 'multipleValue',
      dynamicProps: {
        range: ({ record }) => {
          return record.get('valueType') === 'section' ? ['leftValue', 'rightValue'] : false;
        },
      },
    },
    {
      name: 'leftChar',
      type: FieldType.string,
      defaultValue: '[',
    },
    {
      name: 'leftValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.leftValue';
          }
        },
      },
    },
    {
      name: 'rightChar',
      type: FieldType.string,
      defaultValue: ']',
    },
    {
      name: 'rightValue',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('valueType') === 'section') {
            return 'multipleValue.rightValue';
          }
        },
      },
    },
    {
      name: 'valueType',
      type: FieldType.string,
      defaultValue: 'single',
    },
    {
      name: 'valueShow',
      type: FieldType.string,
    },
    {
      name: 'standard',
      type: FieldType.string,
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeLov`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: FieldIgnore.always,
    },
    {
      name: 'ncCodeId',
      type: FieldType.string,
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      bind: 'ncCodeLov.ncCode',
    },
  ],
  events: {
    load: ({ dataSet }) => {
      if (!dataSet.length) {
        dataSet.loadData([{ leftChar: '(', rightChar: ')', valueType: 'single' }]);
        return;
      }
      dataSet.forEach(record => {
        if (record?.get('valueType') === 'section') {
          record?.set('multipleValue', {
            leftValue: record?.get('leftValue'),
            rightValue: record?.get('rightValue'),
          });
        } else {
          record?.set('multipleValue', record?.get('dataValue'));
        }
      });
    },
    update: ({ record, name }) => {
      switch (name) {
        case 'valueType':
        case 'leftValue':
        case 'rightValue':
        case 'leftChar':
        case 'rightChar':
        case 'multipleValue':
          handleUpdateRangeValue(record, name);
          break;
        default:
          break;
      }
    },
  },
});

const handleUpdateRangeValue = (record, name) => {
  if (record.get('valueType') === 'section') {
    if (!record.get('leftChar')) {
      record.set('leftChar', '(');
    }
    if (!record.get('rightChar')) {
      record.set('rightChar', ')');
    }
    const leftValue = record.get('leftValue') || '';
    const rightValue = record.get('rightValue') || '';
    const leftChar = record.get('leftChar') === '(' ? '<' : '≤';
    const rightChar = record.get('rightChar') === ')' ? '<' : '≤';
    record.set('valueShow', `${leftValue}${leftChar}X${rightChar}${rightValue}`);
  } else if (name === 'valueType') {
    record.set('multipleValue', undefined);
  }
};

export { tableDS, numberListDS };
