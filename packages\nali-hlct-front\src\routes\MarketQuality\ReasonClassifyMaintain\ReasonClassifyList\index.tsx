/*
 * @Description: 原因分类维护-列表界面
 * @Author: <<EMAIL>>
 * @Date: 2023-09-11 14:07:22
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-15 17:54:40
 */
import React, { useMemo } from 'react';
import { Button, DataSet, Table, Switch, NumberField } from 'choerodon-ui/pro';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { Badge } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/es/button/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from '../stores/tableDS';
import { SaveReasonClassify } from '../services';

const modelPrompt = 'tarzan.qms.reasonClassifyMaintain';

const ReasonClassifyList = props => {
  const { tableDs } = props;

  const { run: saveReasonClassify } = useRequest(SaveReasonClassify(), {
    manual: true,
  });

  const handleValidate = async () => {
    // 校验所有表单
    const normalValidate = await Promise.all(
      tableDs.map(async record => {
        if (record.status === 'add' || record?.getState('editing')) {
          const res = await record.validate(true);
          return res;
        }
        return true;
      }),
    );
    // 汇总校验结果
    return normalValidate.every(val => val);
  };

  const handleSave = async () => {
    const valRes = await handleValidate();
    if (!valRes) {
      return;
    }

    const data: any = [];
    tableDs.forEach(record => {
      if (record.status === 'add' || record?.getState('editing')) {
        data.push(record.toData());
      }
    });
    saveReasonClassify({
      params: data,
      onSuccess: () => {
        notification.success({});
        tableDs.query(tableDs.currentPage);
      },
    });
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'reasonCode',
      },
      {
        name: 'reasonClassify',
        width: 180,
        editor: record => record.status === 'add' || record?.getState('editing'),
      },
      {
        name: 'reason',
        editor: record => record.status === 'add' || record?.getState('editing'),
      },
      {
        name: 'aResponsibilityRatio',
        width: 180,
        editor: record =>
          (record.status === 'add' || record?.getState('editing')) && <NumberField suffix="%" />,
        renderer: ({ value, record }) => {
          if (record?.status === 'add' || record?.getState('editing')) {
            return `${value || 0}%`;
          }
          if (!value) {
            return;
          }
          return `${value}%`;
        },
      },
      {
        name: 'bResponsibilityRatio',
        width: 180,
        editor: record =>
          (record.status === 'add' || record?.getState('editing')) && <NumberField suffix="%" />,
        renderer: ({ value, record }) => {
          if (record?.status === 'add' || record?.getState('editing')) {
            return `${value || 0}%`;
          }
          if (!value) {
            return;
          }
          return `${value}%`;
        },
      },
      {
        name: 'reasonStatus',
        width: 180,
        align: ColumnAlign.center,
        editor: record => (record.status === 'add' || record?.getState('editing')) && <Switch />,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get(`${modelPrompt}.reasonStatus.Y`).d('生效')
                  : intl.get(`${modelPrompt}.reasonStatus.N`).d('失效')
              }
            />
          );
        },
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ record, dataSet }: { record?; dataSet? }) => {
          if (record?.status === 'add' || record?.getState('editing')) {
            return (
              <>
                <Button
                  funcType={FuncType.flat}
                  onClick={() => {
                    if (record.status === 'add') {
                      dataSet?.remove(record);
                    } else {
                      record.reset();
                      record.setState('editing', false);
                    }
                  }}
                >
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
                <Button funcType={FuncType.flat} onClick={() => handleSave()}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
              </>
            );
          }
          return (
            <Button
              funcType={FuncType.flat}
              onClick={() => {
                record?.setState('editing', true);
              }}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          );
        },
      },
    ],
    [],
  );

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('原因分类维护')}>
        <Button color={ButtonColor.primary} icon="add" onClick={() => tableDs.create({}, 0)}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          searchCode="reasonClassifyMaintain-list"
          customizedCode="reasonClassifyMaintain-list"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(() => {
    const tableDs = new DataSet(tableDS());
    return {
      tableDs,
    };
  })(ReasonClassifyList),
);
