/**
 * @Description: 事件查询
 * @Author: <<EMAIL>>
 * @Date: 2022-10-27 16:24:34
 * @LastEditTime: 2023-02-01 17:15:13
 * @LastEditors: <<EMAIL>>
 */

import React, { FC, useMemo, useState, useRef } from 'react';
import { RouteComponentProps } from 'react-router';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import moment from 'moment';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import { Button, DataSet, Table, DatePicker, Modal } from 'choerodon-ui/pro';
import { ViewMode } from 'choerodon-ui/pro/lib/date-picker/enum';
import { EXPANDED_KEY } from 'choerodon-ui/dataset/data-set/Record';
import { Header, Content } from 'components/Page';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableMode, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { tableDS, parentEventDrawerDS } from './stores';
import ParentEventDrawer from './ParentEventDrawer';
import EventDetailsDrawer from './EventDetailsDrawer';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.event.eventQuery.model.eventQuery';

const ChildSteps: FC<RouteComponentProps> = () => {
  const { run: getEventDetails } = useRequest(
    {
      url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-event-object-type-rel/limit-type/property/list/ui`,
      method: 'GET',
    },
    {
      manual: true,
      showNotification: false,
      needPromise: true,
    },
  );

  const tableDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  const inputRef = useRef<HTMLInputElement>(null);
  const parentEventDrawerDs = useMemo(() => new DataSet(parentEventDrawerDS()), []);

  const [expendedAll, setExpendedAll] = useState(false);

  useDataSetEvent(tableDs, 'query', () => {
    setExpendedAll(false);
  });

  const showFatherEventDetails = async value => {
    parentEventDrawerDs.setQueryParameter('parentEventId', value);
    await parentEventDrawerDs.query();
    Modal.open({
      ...drawerPropsC7n({
        ds: parentEventDrawerDs,
        canEdit: false,
      }),
      key: Modal.key(),
      title: intl.get('tarzan.event.eventQuery.title.parentEvent').d('父子事件'),
      style: {
        width: 720,
      },
      children: <ParentEventDrawer ds={parentEventDrawerDs} />,
    });
  };

  const handleEventDetailsDrawerShow = async record => {
    getEventDetails({
      params: {
        eventId: record.get('kid'),
      },
    }).then(res => {
      if (res?.success && res.rows.length > 0) {
        Modal.destroyAll();
        Modal.open({
          ...drawerPropsC7n({
            canEdit: false,
          }),
          key: Modal.key(),
          title: intl.get('tarzan.event.eventQuery.title.effectObj').d('事件影响对象'),
          style: {
            width: 1080,
          },
          children: <EventDetailsDrawer dataSource={res.rows} />,
        });
      } else {
        notification.error({
          message: intl.get(`${modelPrompt}.noDetail`).d('暂无对象详细信息'),
        });
      }
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'rowStyle',
      width: 160,
      renderer: ({ record }) => (
        <span>
          {record?.get('eventFlag')
            ? intl.get(`${modelPrompt}.event`).d('事件')
            : intl.get(`${modelPrompt}.eventQuery`).d('事件请求')}
        </span>
      ),
    },
    {
      name: 'kid',
      width: 120,
    },
    {
      name: 'typeCode',
      width: 200,
    },
    {
      name: 'typeDesc',
      width: 140,
    },
    {
      name: 'operationTime',
      width: 160,
      align: ColumnAlign.center,
    },
    {
      name: 'operationUserName',
      width: 120,
    },
    {
      name: 'workcellCode',
      width: 120,
    },
    {
      name: 'locatorCode',
      width: 120,
    },
    {
      name: 'shiftDate',
      width: 120,
      align: ColumnAlign.center,
    },
    {
      name: 'shiftCode',
      width: 120,
    },
    {
      name: 'parentEventId',
      width: 200,
      align: ColumnAlign.center,
      renderer: ({ value }) => {
        if (!value) {
          return;
        }
        return <a onClick={() => showFatherEventDetails(value)}>{value}</a>;
      },
    },
    {
      name: 'eventDetails',
      width: 110,
      header: intl.get(`${modelPrompt}.eventDetails`).d('对象详细信息'),
      align: ColumnAlign.center,
      renderer: ({ record }) => {
        if (!record || !record.get('eventFlag')) {
          return;
        }
        return (
          <a onClick={() => handleEventDetailsDrawerShow(record)}>
            {intl.get(`${modelPrompt}.lookOver`).d('查看')}
          </a>
        );
      },
    },
  ];

  const handleExpendedAll = () => {
    setExpendedAll(true);
    tableDs.forEach(item => {
      item.setState(EXPANDED_KEY, true);
    });
  };

  const handleUnExpendedAll = () => {
    setExpendedAll(false);
    tableDs.forEach(item => {
      item.setState(EXPANDED_KEY, false);
    });
  };

  const handleRecentMinute = async (count) => {
    const nowTime = new Date().getTime();
    const oneMins = moment(nowTime).subtract(count, 'minutes').format('YYYY-MM-DD HH:mm:ss')
    tableDs!.queryDataSet!.current!.set('startTime', oneMins)
    tableDs!.queryDataSet!.current!.set('endTime', moment(nowTime))
    // setTimeout(() => {
    //   const objArr: HTMLCollection = document.getElementsByClassName('c7n-pro-popup c7n-pro-popup-wrapper c7n-pro-calendar-picker-popup c7n-pro-calendar-picker-popup-datetime c7n-pro-calendar-picker-popup-placement-bottomLeft');
    //   for (let i = 0; i < objArr.length; i++) {
    //     const element = objArr[i];
    //     element.setAttribute('hidden', 'true');
    //     inputRef!.current!.focus()
    //   }
    // }, 300);
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.event.eventQuery.view.title.eventQuery').d('事件查询')}>
        {!expendedAll ? (
          <Button onClick={handleExpendedAll}>
            {intl.get('tarzan.common.button.open').d('全部展开')}
          </Button>
        ) : (
          <Button onClick={handleUnExpendedAll}>
            {intl.get('tarzan.common.button.retract').d('全部收起')}
          </Button>
        )}
      </Header>
      <Content>
        <Table
          mode={TableMode.tree}
          searchCode="eventQuery"
          customizedCode="eventQuery"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFields={{
            'startTime': <DatePicker
              mode={ViewMode.dateTime}
              renderExtraFooter={() => {
                return (
                  <div style={{ float: 'left', paddingLeft: '0.03rem' }}>
                    <Button funcType={FuncType.flat} onClick={() => handleRecentMinute(1)}>{intl.get(`${modelPrompt}.oneMinute`).d('近一分钟')}</Button>
                    <Button funcType={FuncType.flat} onClick={() => handleRecentMinute(3)}>{intl.get(`${modelPrompt}.threeMinute`).d('近三分钟')}</Button>
                  </div>
                )
              }}
              extraFooterPlacement='top'
            />,
            'endTime': <DatePicker
              mode={ViewMode.dateTime}
              renderExtraFooter={() => {
                return (
                  <div style={{ float: 'left', paddingLeft: '0.03rem' }}>
                    <Button funcType={FuncType.flat} onClick={() => handleRecentMinute(1)}>{intl.get(`${modelPrompt}.oneMinute`).d('近一分钟')}</Button>
                    <Button funcType={FuncType.flat} onClick={() => handleRecentMinute(3)}>{intl.get(`${modelPrompt}.threeMinute`).d('近三分钟')}</Button>
                  </div>
                )
              }}
              extraFooterPlacement='top'
            />,
          }}
          dataSet={tableDs}
          columns={columns}
        />
        <input ref={inputRef} style={{ opacity: 0 }} />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.event.eventQuery', 'tarzan.common'],
})(ChildSteps);
