/**
 * @Description: 创建外协发料单-新版
 * @Author: <EMAIL>
 * @Date: 2022/2/22 15:41
 * @LastEditTime: 2023-05-18 15:31:26
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useState, useEffect } from 'react';
import { Header, Content } from 'components/Page';
import {
  DataSet,
  Form,
  TextField,
  DateTimePicker,
  Table,
  Button,
  Lov,
  NumberField,
  Spin,
  Select,
  Switch,
  Icon,
  Modal,
} from 'choerodon-ui/pro';
import { Popconfirm, Badge, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/lib/_util/enum';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { ColumnAlign, TableColumnTooltip, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { isNull, flow, isEmpty } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { C7nFormItemSort } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import request from 'utils/request';
import { BASIC, API_HOST } from '@utils/config';
import { queryMapIdpValue } from 'services/api';
import { Record } from 'choerodon-ui/dataset';
import { headDS, tableDS } from '../stores/OutsourcingDS';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.purchase.outsourcingManage';
const _urlEnd = '';

// 校验
export function SaveCheck() {
  return {
    url: `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-instruction/instruction/save/verify/ui`,
    method: 'GET',
  };
}

// 行查询接口
export function QueryLine() {
  return {
    url: `${BASIC.HMES_BASIC}${_urlEnd}/v1/${getCurrentOrganizationId()}/wms-out-source/line/create/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_OUTSOURCING.HEAD,${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_OUTSOURCING.LINE`,
    method: 'GET',
  };
}

// 保存
export function Preservation() {
  return {
    url: `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wms-out-source/out-source-return/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_OUTSOURCING.HEAD,${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_OUTSOURCING.LINE`,
    method: 'POST',
  };
}

// 根据库位获取现有量的接口
export function AvailableQuantity() {
  return {
    url: `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-inv-onhand-quantity/sum-available-qty/ui`,
    method: 'POST',
  };
}

// 根据库位获取现有量的接口
export function SaveData() {
  return {
    url: `${API_HOST}${BASIC.HMES_BASIC}${_urlEnd}/v1/${getCurrentOrganizationId()}/mt-instruction/save/ui`,
    method: 'POST',
  };
}

const CreateDetail = props => {
  const {
    match: { path },
    customizeForm,
    customizeTable,
  } = props;

  const headDs = useMemo(() => new DataSet(headDS()), []);

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const [statue, setStatus] = useState(false); // 用于控制行上的数据是否可编辑

  const [loading, setLoading] = useState(false);

  const [isSubmit, setIsSubmit] = useState(false); // 是否已保存

  const [poNumber, setPoNumber] = useState(); // 是否已保存

  const preservation = useRequest(Preservation(), {
    manual: true,
  });

  const availableQuantity = useRequest(AvailableQuantity(), {
    manual: true,
  });

  const queryLine = useRequest(QueryLine(), {
    manual: true,
  });

  const saveData = useRequest(SaveData(), {
    manual: true,
  });

  const saveCheck = useRequest(SaveCheck(), {
    manual: true,
  });

  useEffect(() => {
    handleFetchDefaultSourceSystem();
  }, []);

  // 行删除
  const handleDelete = record => {
    tableDs.delete(record, false);
    setTimeout(() => {
      lineNumFormat();
    }, 0);
  };

  const lineNumFormat = () => {
    const lineNums = [];
    tableDs.forEach(item => {
      // @ts-ignore
      lineNums.push(`${item.get('poLineNum')}`);
    });
    // @ts-ignore
    const { outSourcePoLov } = headDs.toData()[0];
    const _outSourcePoLov = [];
    if (outSourcePoLov) {
      outSourcePoLov.forEach(item => {
        // @ts-ignore
        if (lineNums.indexOf(`${item.lineNum}`) > -1) {
          // @ts-ignore
          _outSourcePoLov.push(item);
        }
      });
    }
    if (_outSourcePoLov.length === 0) {
      setPoNumber(undefined);
      headDs!.current!.set('poHeaderId', null);
      headDs!.current!.set('poNumber', null);
      headDs!.current!.set('siteId', null);
      headDs!.current!.set('supplierName', null);
      headDs!.current!.set('supplierCode', null);
      headDs!.current!.set('supplierId', null);
      headDs!.current!.set('supplierSiteName', null);
      headDs!.current!.set('supplierSiteCode', null);
      headDs!.current!.set('supplierSiteId', null);
      headDs!.current!.set('receivingAddress', null);
      headDs!.current!.set('sourceOrderId', null);
    }
    // eslint-disable-next-line no-unused-expressions
    headDs.current?.set('outSourcePoLov', _outSourcePoLov);
    tableDs.forEach((item, index) => {
      item.init('lineNumber', (index + 1) * 10);
    });
  };

  const handleFetchDefaultSourceSystem = () => {
    queryMapIdpValue({
      sourceSystemList: 'SOURCE_SYSTEM',
    }).then(res => {
      if(res) {
        const defaultSourceSystem = res.sourceSystemList.find(e => e.tag === "Y");
        if(defaultSourceSystem) {
          headDs.current.set('sourceSystem', defaultSourceSystem.value);
        };
      }
    });
  };

  const getPageDetail = record => {
    const { materialId, revisionCode, siteId, locatorId } = record.data;
    availableQuantity.run({
      params: {
        materialId,
        revisionCode,
        locatorId,
        siteId,
      },
      onSuccess: res => {
        record.set('onhandQuantity', res);
      },
    });
  };

  const getPageLineDetail = record => {
    const { materialId = '', revisionCode = '', siteId = '', locatorId = '' } = record.data;
    setLoading(true);
    request(
      `${API_HOST}${BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/mt-inv-onhand-quantity/sum-available-qty/ui`,
      {
        method: 'POST',
        body: {
          materialId,
          revisionCode,
          locatorId,
          siteId,
        },
      },
    )
      .then(res => {
        setLoading(false);

        if (res && res.success) {
          record.init('onhandQuantity', res.rows);
        } else if (res && !res.success) {
          notification.error({
            message: res.message,
          });
        }
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 发货库位点击时
  const handleLocator = (record, data) => {
    if (!isNull(data)) {
      getPageDetail(record);
    } else {
      record.set('onhandQuantity', 0);
    }
  };

  const clearLerance = (record, keys) => {
    if (keys?.length > 0) {
      keys.forEach(item => {
        record.set(item, undefined);
      });
    }
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          funcType="flat"
          shape="circle"
          size="small"
          disabled
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      name: 'name',
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button icon="remove" disabled={isSubmit} funcType={FuncType.flat} size={Size.small} />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'siteCode',
      align: ColumnAlign.left,
      width: 120,
    },
    {
      name: 'materialCode',
      align: ColumnAlign.left,
      width: 150,
    },
    {
      name: 'revisionCode',
      align: ColumnAlign.left,
      width: 120,
    },
    {
      name: 'materialName',
      align: ColumnAlign.left,
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'componentQty',
      width: 120,
      align: ColumnAlign.right,
    },
    {
      name: 'sumAvailableQty',
      width: 100,
      align: ColumnAlign.right,
    },
    {
      name: 'haveSendQty',
      width: 120,
      align: ColumnAlign.right,
    },
    {
      name: 'quantity',
      width: 120,
      align: ColumnAlign.right,
      editor: () => !statue && !loading && <NumberField nonStrictStep precision={6} step={1} />,
    },
    {
      name: 'usedOverDeliveryQty',
      width: 120,
      align: ColumnAlign.right,
      editor: () => !statue && !loading && <NumberField nonStrictStep precision={6} step={1} />,
    },
    {
      name: 'uomCode',
      align: ColumnAlign.left,
      width: 150,
    },
    {
      name: 'locatorLov',
      width: 150,
      align: ColumnAlign.left,
      editor: record =>
        !statue && !loading && <Lov onChange={data => handleLocator(record, data)} noCache />,
    },
    {
      name: 'onhandQuantity',
      align: ColumnAlign.right,
      width: 120,
    },
    {
      name: 'poNumber',
      align: ColumnAlign.left,
      width: 150,
    },
    {
      name: 'poLineNum',
      align: ColumnAlign.left,
      width: 160,
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: ColumnAlign.center,
      editor: record =>
        !statue &&
        !loading && (
          <Switch
            onChange={() => {
              clearLerance(record, ['toleranceType', 'toleranceMinValue', 'toleranceMaxValue']);
            }}
          />
        ),
      renderer: ({ value }) => {
        return (
          // @ts-ignore
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.enable').d('启用')
                : intl.get('tarzan.common.label.disable').d('禁用')
            }
          />
        );
      },
    },
    {
      name: 'toleranceType',
      width: 140,
      editor: record =>
        !statue &&
        !loading && (
          <Select
            onChange={() => {
              clearLerance(record, ['toleranceMinValue', 'toleranceMaxValue']);
            }}
          />
        ),
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
      editor: () => !statue && !loading && <NumberField nonStrictStep precision={6} step={1} />,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
      editor: () => !statue && !loading && <NumberField nonStrictStep precision={6} step={1} />,
    },
  ];

  const handleSaveClick = () => {
    handleSaveCheck();
  };

  // 保存校验
  const handleSaveCheck = async () => {
    setLoading(true);
    const validateHeader = await headDs.validate();
    const validateTable = await tableDs.validate();
    setLoading(false);
    if (validateHeader && validateTable) {
      const headData: any = headDs.toData()[0];
      const { poHeaderId } = headData;
      saveCheck.run({
        params: {
          poHeaderId,
        },
        onSuccess: async res => {
          if (!isEmpty(res)) {
            const code = res.join(',');
            if (
              (await Modal.confirm({
                title: (
                  <div>
                    <span style={{ fontSize: 18 }}>
                      <Icon
                        type="info_outline"
                        style={{ fontSize: 22, color: '#FAAD04', marginRight: 10 }}
                      />
                      {intl.get(`tarzan.common.title.tips`).d('提示')}
                    </span>
                  </div>
                ),
                bodyStyle: { marginLeft: 7, marginRight: 7 },
                children: (
                  <div style={{ marginLeft: 32 }}>
                    <span style={{ wordBreak: 'break-all' }}>
                      {`${intl
                        .get(`${modelPrompt}.not.completed.outsourcing`, {
                          code,
                        })
                        .d(`之前还有未完成的外协发料单${code}, 是否还要继续建单？`)}`}
                    </span>
                  </div>
                ),
              })) !== 'cancel'
            ) {
              handleSave();
            }
          } else {
            handleSave();
          }
        },
        onFailed: () => {
          // eslint-disable-next-line no-unused-expressions
          headDs.current?.set('outSourcePoLov', undefined);
        },
      });
    }
  };

  // 保存
  const handleSave = () => {
    const headData = headDs?.current?.toData();
    const listData = [];
    tableDs!.toData().forEach(item => {
      // @ts-ignore
      if (item.quantity > 0 || item.usedOverDeliveryQty > 0) {
        // @ts-ignore
        listData.push({
          ...item,
        });
      }
    });
    // eslint-disable-next-line
    const { outSourcePoLineLov, outSourcePoLov, ...other } = headData;
    const data = {
      instructionDTO5List: listData,
      instructionDocDTO4: {
        ...other,
      },
    };
    saveData.run({
      params: data,
      onSuccess: res => {
        headDs!.current!.set('instructionDocNum', res[0].instructionDocNum);
        setStatus(true);
        setIsSubmit(true);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        tableDs.loadData(listData);
      },
    });
  };

  // 取消
  const handleCancel = () => {
    props.history.push({
      pathname: `/hmes/purchase/outsourcing-manage-standard/list`,
      state: {
        queryFlag: isSubmit,
      },
    });
  };

  // 选择单据前判断
  const outSourcePoLovOnBeforeSelect = records => {
    const poHeaderIds = [];
    records.forEach(item => {
      // @ts-ignore
      if (poHeaderIds.indexOf(item.toData().poHeaderId) === -1) {
        // @ts-ignore
        poHeaderIds.push(item.toData().poHeaderId);
      }
    });
    if (!(poHeaderIds.length === 1)) {
      notification.error({
        message: intl.get(`${modelPrompt}.operation.error`).d('请选择相同采购订单号'),
      });
    }
    return poHeaderIds.length === 1;
  };

  // 选择外协采购订单lov
  const handleSource = (value, oldValue) => {
    if (value) {
      if (value && oldValue && value[0]?.poNumber !== oldValue[0]?.poNumber) {
        tableDs.loadData([]);
      }
      headDs!.current!.set('poHeaderId', value[0].poHeaderId);
      headDs!.current!.set('poNumber', value[0].poNumber);
      headDs!.current!.set('siteId', value[0].siteId);
      headDs!.current!.set('supplierCode', value[0].supplierCode);
      headDs!.current!.set('supplierName', value[0].supplierName);
      headDs!.current!.set('supplierId', value[0].supplierId);
      headDs!.current!.set('supplierSiteName', value[0].supplierSiteName);
      headDs!.current!.set('supplierSiteCode', value[0].supplierSiteCode);
      headDs!.current!.set('supplierSiteId', value[0].supplierSiteId);
      headDs!.current!.set('receivingAddress', value[0].receivingAddress);
      headDs!.current!.set('sourceOrderId', value[0].poHeaderId);

      setPoNumber(value[0].poNumber);

      const poHeaderIds = [];
      const lineNums = [];
      value.forEach(item => {
        // @ts-ignore
        if (poHeaderIds.indexOf(`${item.poHeaderId}`) === -1) {
          // @ts-ignore
          poHeaderIds.push(`${item.poHeaderId}`);
        }
        // @ts-ignore
        if (lineNums.indexOf(`${item.lineNum}`) === -1) {
          // @ts-ignore
          lineNums.push(`${item.lineNum}`);
        }
      });

      const addedLineNums = [];

      const toDeleteRecords: Record[] = [];

      tableDs.forEach(record => {
        // @ts-ignore
        if (lineNums.indexOf(`${record.get('poLineNum')}`) === -1) {
          toDeleteRecords.push(record);
        } else {
          // @ts-ignore
          addedLineNums.push(`${record.get('poLineNum')}`);
        }
      });

      const queryLineMuns = [];

      lineNums.forEach(item => {
        // @ts-ignore
        if (addedLineNums.indexOf(`${item}`) === -1) {
          // @ts-ignore
          queryLineMuns.push(`${item}`);
        }
      });

      tableDs.delete(toDeleteRecords, false);

      tableDs.forEach((record, index) => {
        record.init('lineNumber', (index + 1) * 10);
      });

      if (queryLineMuns.length === 0) {
        return;
      }
      queryLine.run({
        params: {
          instructionDocType: 'OUTSOURCE_DOC',
          poHeaderIds,
          lineNums: queryLineMuns,
        },
        onSuccess: res => {
          const _res = res.map(item => {
            return {
              ...item,
              usedOverDeliveryQty: 0,
              quantity: 0,
            };
          });
          _res.forEach(item => {
            const newRecord = tableDs.create(item, tableDs.length);
            getPageLineDetail(newRecord);
          });
          tableDs.forEach((record, index) => {
            record.init('lineNumber', (index + 1) * 10);
          });
        },
        onFailed: () => {
          // eslint-disable-next-line no-unused-expressions
          headDs.current?.set('outSourcePoLov', undefined);
        },
      });
    } else {
      tableDs.loadData([]);
      headDs!.current!.set('poHeaderId', null);
      headDs!.current!.set('poNumber', null);
      headDs!.current!.set('siteId', null);
      headDs!.current!.set('supplierName', null);
      headDs!.current!.set('supplierCode', null);
      headDs!.current!.set('supplierId', null);
      headDs!.current!.set('supplierSiteName', null);
      headDs!.current!.set('supplierSiteCode', null);
      headDs!.current!.set('supplierSiteId', null);
      headDs!.current!.set('receivingAddress', null);
      headDs!.current!.set('sourceOrderId', null);
      setPoNumber(undefined);
    }
  };

  const revisionLinkBom = () => {
    const _num = headDs?.current?.get('poNumber');
    if (_num) {
      props.history.push({
        pathname: `/hmes/purchase/order-management-standard/list`,
        // @ts-ignore
        query: {
          poNumber: _num,
        },
      });
    }
  };

  // 套数变更
  const setQtyChange = newVal => {
    tableDs.forEach(dsItem => {
      dsItem.init('setQty', newVal);
      if (newVal > 0) {
        const newQuantity =
          ((dsItem.get('componentQty') || 0) / (dsItem.get('quantityOrdered') || 0)) * newVal;
        dsItem.set('quantity', newQuantity);
      } else {
        const quantity =
          (dsItem.get('componentQty') || 0) -
          (dsItem.get('sumAvailableQty') || 0) -
          (dsItem.get('haveSendQty') || 0);
        dsItem.set('quantity', quantity < 0 ? 0 : quantity);
      }
    });
  };

  return (
    <div className="hmes-style">
      <Spin spinning={saveData.loading || loading}>
        <Header
          title={intl.get(`${modelPrompt}.create.outsourcingOrder`).d('创建外协发料单')}
          backPath="/hmes/purchase/outsourcing-manage-standard/list"
          onBack={handleCancel}
        >
          {!isSubmit && (
            <div>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <PermissionButton
                icon="save"
                type="c7n-pro"
                color={ButtonColor.primary}
                loading={preservation.loading || loading}
                onClick={handleSaveClick}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </PermissionButton>
            </div>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
            <Panel
              header={intl.get(`${modelPrompt}.title.head`).d('头信息')}
              key="basicInfo"
              dataSet={headDs}
            >
              <Spin dataSet={headDs}>
                {customizeForm(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_OUTSOURCING.HEAD`,
                  },
                  <Form
                    dataSet={headDs}
                    labelLayout={LabelLayout.horizontal}
                    disabled={isSubmit}
                    labelWidth={112}
                    columns={3}
                  >
                    <TextField disabled name="instructionDocNum" />
                    <C7nFormItemSort name="outSourcePoLov" itemWidth={['92%', '8%']} colSpan={2}>
                      <Lov
                        name="outSourcePoLov"
                        onChange={(value, oldValue) => {
                          handleSource(value, oldValue);
                        }}
                        // @ts-ignore
                        onBeforeSelect={outSourcePoLovOnBeforeSelect}
                      />
                      <Icon
                        type="link2"
                        // @ts-ignore
                        itemType="link"
                        onClick={revisionLinkBom}
                        style={{
                          color: poNumber ? '#29bece' : 'rgba(0,0,0,0.25)',
                        }}
                        iconDisabled={!poNumber}
                      />
                    </C7nFormItemSort>
                    {/* <Lov name="outSourcePoLineLov" onChange={(value) => handleLine(value)} /> */}
                    <TextField disabled name="supplierName" />
                    <TextField disabled name="supplierSiteName" />
                    <TextField disabled name="receivingAddress" />
                    <NumberField
                      name="setQty"
                      onChange={setQtyChange}
                      nonStrictStep
                      precision={6}
                      step={1}
                    />
                    <DateTimePicker name="demandTime" />
                    <Select name='sourceSystem' disabled />
                    <TextField name="remark" />
                  </Form>,
                )}
              </Spin>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.line`).d('行信息')}
              key="location"
              dataSet={tableDs}
            >
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_OUTSOURCING.LINE`,
                },
                <Table
                  dataSet={tableDs}
                  columns={columns}
                  filter={record => {
                    return record.status !== 'delete';
                  }}
                />,
              )}
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.hmes.purchase.outsourcingManage', 'tarzan.common'] }),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_OUTSOURCING.HEAD`, `${BASIC.CUSZ_CODE_BEFORE}.OUTSOURCING_OUTSOURCING.LINE`] }),
)(CreateDetail);
