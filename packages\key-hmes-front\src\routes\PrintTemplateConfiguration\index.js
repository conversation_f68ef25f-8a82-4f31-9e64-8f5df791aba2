/*
 * @Description: 打印模板配置入口文件
 * @Author: YinWQ
 * @Date: 2023-07-18 10:24:49
 * @LastEditors: YinWQ
 * @LastEditTime: 2023-07-19 16:35:42
 */
import React, { useMemo } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import { MainTableDS } from './stores/index';

const modelPrompt = 'tarzan.model.print.template.configuration';

const printTemplateConfiguration = props => {
  // 解构参数
  const {
    mainTableDS,
    match: { path },
  } = props;

  // useEffect(() => {
  //   mainTableDS.loadData([
  //     {
  //       description: '描述',
  //       printButtonCode: '按钮编码',
  //       printChannel: '模式',
  //       printChannelPath: '路径',
  //       printFunctionId: 0,
  //     },
  //   ]);
  // })

  const mainTableColumns = useMemo(
    () => [
      {
        name: 'printButtonCode',
      },
      {
        name: 'description',
      },
      {
        name: 'printChannel',
      },
      {
        name: 'printChannelPath',
      },
      {
        header: '操作',
        align: 'center',
        renderer: ({record}) => (<a onClick={() => handleGotoDetailedPage(record)}>模板列表</a>),
      },
    ],
    [],
  );

  // 转跳详情界面
  const handleGotoDetailedPage = (record) => {
    props.history.push(
      `/hmes/print-template-configuration/detail/${record.get('printFunctionId')}`,
    );
  };

  // 新建按钮
  const handleCreate = () => {
    props.history.push(`/hmes/print-template-configuration/detail/create`);
  }

  return (
    <div>
      <Header title={intl.get(`${modelPrompt}.function.title`).d('打印模板配置')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={mainTableDS}
          columns={mainTableColumns}
          selectionMode="none"
          searchCode="PrintTemplateConfiguration"
          customizedCode="PrintTemplateConfiguration"
        />
      </Content>
    </div>
  );
};
export default flow(
  formatterCollections({ code: ['tarzan.model.print.template.configuration', 'tarzan.common'] }),
  withProps(
    () => {
      const mainTableDS = new DataSet({
        ...MainTableDS(),
      });
      return {
        mainTableDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(printTemplateConfiguration);
