import intl from 'utils/intl';

const modelPrompt = 'tarzan.hwms.ContainerLoadAndUnload';

const containerQueryDS = () => {
  return {
    autoQuery: false,
    autoCreate: true,
    paging: false,
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.pleaseScan.container`).d('请输入容器'),
        required: true,
      },
    ],
  }
};

const containerInfoDS = () => {
  return {
    autoCreate:true,
    fields: [
      {
        name: 'locatorCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
      },
      {
        name: 'packingLevelDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.packingLevelDesc`).d('允许装载对象'),
      },
      {
        name: 'mixedLoadObject',
        type: 'string',
        label: intl.get(`${modelPrompt}.mixedLoadObject`).d('混合装载对象'),
      },
      {
        name: 'capacityQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.capacityQty`).d('最大装载数量'),
      },
      {
        name: 'maxLoadWeight',
        type: 'string',
        label: intl.get(`${modelPrompt}.maxLoadWeight`).d('最大承重'),
      },
      {
        name: 'currentContainerIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.currentContainerIdentification`).d('当前容器'),
      },
      {
        name: 'topContainerIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.topContainerIdentification`).d('顶层容器'),
      },
    ],

  };
}

const loadDS = (containerInfoDs) => {
  return {
    autoQuery: false,
    paging: false,
    selection: 'single',
    primaryKey: 'identification',
    queryFields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('请扫描装载对象'),
        required: true,
        dynamicProps: {
          disabled: () => !containerInfoDs.current?.get('containerId'),
        },
      },
    ],
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('编码'),
      },
      {
        name: 'qualityStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('状态'),
      },
      {
        name: 'loadObjectType',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderTypeDesc`).d('类型'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('描述'),
      },
      {
        name: 'qty',
        type: 'string',
        label: intl.get(`${modelPrompt}.qty`).d('待装数量'),
      },
      {
        name: 'primaryUomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.primaryUomCode`).d('单位'),
      },
      {
        name: 'soNumSoLinNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.soNumber`).d('销售订单'),
      },
    ],
    record: {
      dynamicProps: {
        // 设备带出的工单不可选择
        // selectable: record => !record?.get('isEquipment'),
      },
    },
  };
};

const unLoadDS = (containerInfoDs) => {
  return {
    autoQuery: false,
    paging: false,
    selection: 'single',
    primaryKey: 'identification',
    queryFields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('请扫描装载对象'),
        required: true,
        dynamicProps: {
          disabled: () => !containerInfoDs.current?.get('containerId'),
        },
      },
    ],
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('编码'),
      },
      {
        name: 'qualityStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('状态'),
      },
      {
        name: 'loadObjectType',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderTypeDesc`).d('类型'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('描述'),
      },
      {
        name: 'qty',
        type: 'string',
        label: intl.get(`${modelPrompt}.qtyLoad`).d('已装数量'),
      },
      {
        name: 'primaryUomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.primaryUomCode`).d('单位'),
      },
      {
        name: 'soNumSoLinNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.soNumber`).d('销售订单'),
      },
    ],
    record: {
      dynamicProps: {
        // 设备带出的工单不可选择
        // selectable: record => !record?.get('isEquipment'),
      },
    },
  };
};


export {
  containerQueryDS,
  containerInfoDS,
  loadDS,
  unLoadDS,
};
