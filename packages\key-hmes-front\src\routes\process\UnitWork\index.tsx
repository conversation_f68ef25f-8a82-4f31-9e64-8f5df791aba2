/**
 * @Description: 工艺与工作单元关系维护
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 09:18:34
 * @LastEditTime: 2022-11-14 16:00:33
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useCallback } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, TextField, Button, Spin, Lov, NumberField } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import {openTab} from "utils/menuTab";
import queryString from "query-string";
import { tableDS } from './stores';
import { DeleteItems, SaveItem } from './services';

const modelPrompt = 'tarzan.process.unitWork.model.unitWork';

const DeleteBtn = observer(({ ds }: { ds: DataSet }) => {
  const selectedRows = ds.selected;

  const { run: runDelete, loading } = useRequest(DeleteItems(), { manual: true });

  const deleteMessage = () => {
    const delDataList = selectedRows
      .filter(item => item.get('operationWkcDispatchRelId'))
      .map(item => item.get('operationWkcDispatchRelId'));
    if (!delDataList.length) {
      ds.query();
      return;
    }
    runDelete({
      params: delDataList,
      onSuccess: () => {
        ds.query();
      },
    });
  };

  return (
    <Popconfirm
      title={intl
        .get(`${modelPrompt}.confirm.delete`, {
          count: selectedRows.length,
        })
        .d(`总计${selectedRows.length}条数据，是否确认删除?`)}
      onConfirm={deleteMessage}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
      okText={intl.get('tarzan.common.button.confirm').d('确定')}
    >
      <Button loading={loading} icon="delete_black-o" disabled={!selectedRows.length}>
        {intl.get('tarzan.common.button.delete').d('删除')}
      </Button>
    </Popconfirm>
  );
});

const ErrorMessage = props => {
  const {
    match: { path },
  } = props;

  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const { run: runSave } = useRequest(SaveItem(), { manual: true, needPromise: true });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'operationLov',
        width: 180,
        editor: record => record.getState('editing') && <Lov name="operationLov" />,
      },
      {
        name: 'description',
      },
      {
        name: 'workcellLov',
        width: 180,
        editor: record => record.getState('editing') && <Lov name="workcellLov" />,
      },
      {
        name: 'workcellName',
      },
      {
        name: 'priority',
        width: 180,
        editor: record => record.getState('editing') && <NumberField name="priority" />,
      },
      {
        name: 'stepName',
        width: 180,
        editor: record => record.getState('editing') && <TextField name="stepName" />,
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ record }: { record? }) => {
          if (record && record.getState('editing')) {
            return record.getState('loading') ? (
              <div style={{ zoom: 0.5 }}>
                <Spin spinning />
              </div>
            ) : (
              <>
                <a onClick={() => handleCancel(record)} style={{ marginRight: '0.1rem' }}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </a>
                <a onClick={() => handleSubmit(record)}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </a>
              </>
            );
          }
          return (
            <PermissionButton
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
              type="text"
              onClick={() => handleEdit(record)}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          );
        },
      },
    ];
  }, []);

  const hanleAdd = useCallback(() => {
    const newRow = tableDs.create({}, 0);
    newRow.setState('editing', true);
  }, [tableDs]);

  const handleEdit = record => {
    record.setState('editing', true);
  };

  const handleCancel = useCallback(
    record => {
      if (record.status === 'add') {
        tableDs.remove(record);
      } else {
        record.reset();
        record.setState('editing', false);
      }
    },
    [tableDs],
  );

  const handleSubmit = record => {
    runSave({
      params: record.toData(),
      onSuccess: res => {
        record.setState('loading', false);
        record.init('operationLov', null);
        record.init('workcellLov', null);
        record.init({
          ...res,
        });
        record.setState('editing', false);
        record.status = 'sync';
        notification.success({});
      },
    });
  };

  const handleBatchExport = () => {
    openTab({
      key: `/hmes/process/unit-work/comment-import/MT.MES.OPERATION_WKC`,
      title: 'hzero.common.title.import',
      search: queryString.stringify({
        action: intl.get(`${modelPrompt}.mes.button.import`).d('工艺与工作单元关系维护-导入'),
      }),
    });
  };


  return (
    <div className="hmes-style">
      <Header title={intl.get(`tarzan.process.unitWork.title.unitWork`).d('工艺与工作单元关系维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={hanleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `hmes.common.unitWork.mes.button.export`,
              type: 'button',
              meaning: '导入',
            },
          ]}
          onClick={handleBatchExport}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </PermissionButton>
        <DeleteBtn ds={tableDs} />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="UnitWork"
          customizedCode="UnitWork"
          // onRow={() => ({ style: { background: 'red' } })}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.process.unitWork', 'tarzan.common'],
})(ErrorMessage);
