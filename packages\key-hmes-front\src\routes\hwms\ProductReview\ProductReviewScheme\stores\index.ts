/**
 * @Description: 产品审核方案-主界面DS
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.ProductReviewScheme';


const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'productRevSchemeId',
  queryFields: [
    {
      name: 'productRevSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeCode`).d('产品审核方案编码'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'productRevSchemeName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeName`).d('方案名称'),
    },
    {
      name: 'productRevSchemeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_SCHEME_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialName',
      lovPara: { tenantId },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'processWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defectForm.processWorkcellName`).d('工序编码'),
      lovCode: 'MT.MODEL.WORKCELL',
      textField: 'workcellName',
      lovPara: {
        tenantId,
        workcellType: 'PROCESS',
        userFlag: '',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'workcellId',
      bind: 'processWorkcellLov.workcellId',
    },
    {
      name: 'cusMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件编码'),
    },
    {
      name: 'cusMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialName`).d('客户零件描述'),
    },
    {
      name: 'productDateFrom',
      label: intl.get(`${modelPrompt}.productDateFrom`).d('量产时间从'),
      type: FieldType.dateTime,
      max: 'productDateTo',
      format: 'YYYY-MM-DD',
    },
    {
      name: 'productDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productDateTo`).d('量产时间至'),
      min: 'productDateFrom',
      format: 'YYYY-MM-DD',
    },
  ],
  fields: [
    {
      name: 'productRevSchemeId', // 行id
    },
    {
      name: 'productRevSchemeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeCode`).d('产品审核方案编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'productRevSchemeName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeName`).d('方案名称'),
    },
    {
      name: 'productRevSchemeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productRevSchemeStatus`).d('状态'),
      lookupCode: 'YP.QIS.PRODUCT_REV_SCHEME_STATUS',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工序编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工序描述'),
    },
    {
      name: 'productDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productDate`).d('量产时间'),
      format: 'YYYY-MM-DD',
    },
    {
      name: 'cusMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialCode`).d('客户零件编码'),
    },
    {
      name: 'cusMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.cusMaterialName`).d('客户零件描述'),
    },
  ],
  transport: {
    read: ({data}) => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-product-rev-scheme/header/ui`,
        method: 'GET',
        data,
      };
    },
  },
});

export { headDS };
