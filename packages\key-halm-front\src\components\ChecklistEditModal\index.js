/**
 * 检查项-编辑Modal
 * @since 2021-01-20
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 * 客制化参数
 * @param {Boolean} isNew
 * @param {Boolean} editFlag
 * @param {Boolean} statusFlag // 控制部分字段的disable 默认true 可编辑
 * @param {String} compParentType // 引用当前组件的模块 'WO' 'ACT' 'CHECKLIST_GROUP' 'MAINTAIN_PLAN'
 * @param {Number} compParentId // 工单id 标准作业id
 * @param {String} compParentName
 * @param {Number} tenantId
 * @param {Object} dataSource // 数据明细
 * @param {Number} maintSiteIds // 取单据服务区域id的数组 用于仪表点lov参数
 */
import React, { Component } from 'react';
import { observer } from 'mobx-react';
import { isBoolean } from 'lodash';
import { Bind } from 'lodash-decorators';
import {
  DataSet,
  Form,
  NumberField,
  Select,
  TextField,
  Lov,
  Output,
  Table,
  Switch,
} from 'choerodon-ui/pro';
import { Collapse, Row, Col } from 'choerodon-ui';

import intl from 'utils/intl';
import { queryMapIdpValue } from 'services/api';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { HALM_MTC } from 'alm/utils/config';
import { ruleConfigDs } from 'alm/pages/Meters/Stores/metersDs';
import NumberComponent from './NumberComponent';
import RuleConfigTable from './RuleConfigTable';
import styles from './index.module.less';
import { detailDs, optionsTableDs, numberListDS } from './woChecklistModalDs';
import { getOptions } from './api';

const viewPrompt = 'alm.checklistEditModal.view.message';

const organizationId = getCurrentOrganizationId();
@observer
class WoChecklistEdit extends Component {
  constructor(props) {
    super(props);
    this.state = {
      prevColumnType: null,
      existParent: false, // 存在同一个检查时点类型的索引项型检查项
    };
    const { compParentId, compParentType, dataSource, maintSiteIds, isNew, editFlag } = props;
    this.detailDs = new DataSet(detailDs({ compParentId, compParentType, maintSiteIds }));
    this.trueNumberDs = new DataSet(numberListDS());
    // 初始化数据
    this.detailDs.data = [{ ...this.detailDs.current?.toData?.(), ...dataSource }];
    if (dataSource?.columnTypeCode === 'VALUE') {
      this.trueNumberDs.data = dataSource?.standardReferenceList || [];
    }

    this.ruleConfigDs = new DataSet(ruleConfigDs()); // 仪表点-告警规则表格
    this.optionsTableDs = new DataSet(optionsTableDs(!isNew && !editFlag));
  }

  componentDidMount() {
    this.init();
  }

  @Bind()
  init() {
    // 编辑时，要查询有是否符合条件的父项 且需要初始化prevColumnType
    const { editFlag, dataSource, isNew, compParentType } = this.props;
    const { businessScenarioCode, checkTiming, meterId, columnTypeCode } = dataSource || {};
    if (editFlag) {
      if (businessScenarioCode || checkTiming) {
        this.handleQueryParent(businessScenarioCode || checkTiming);
      }
      this.setState({
        prevColumnType: columnTypeCode,
      });
    }
    // 非新建时 是仪表类型则要查询仪表信息; 是否类型、值列表类型 要处理选项table
    if (!isNew) {
      if (columnTypeCode === 'NUMBER' && meterId) {
        this.setMeterInfo(meterId);
      }
      const listValueCode = this.detailDs.current.get('listValueCode');
      if (columnTypeCode === 'LISTOFVALUE' && !listValueCode) {
        this.optionsTableDs.data = [];
        return;
      }
      if (['LISTOFVALUE', 'YESORNO', 'VALUE'].includes(columnTypeCode)) {
        const checklistType =
          compParentType === 'CHECKLIST_GROUP'
            ? 'BASE'
            : ['WO', 'WOOP', 'SPOT_CHECK'].includes(compParentType)
            ? 'WO'
            : 'ACT';
        const checklistId = this.detailDs.current.get('checklistId');
        getOptions({
          checklistId,
          checklistType, // 工单和点巡检是WO，标准检查组是BASE，其它都是ACT
        }).then(res => {
          if (res && !res.failed) {
            if(columnTypeCode === 'VALUE'){
              this.detailDs.current.set('isThereAlarm', res[0]?.alarmFlag)
            }else{
              this.optionsTableDs.data = res;
            }
          } else {
            notification.warning({ message: res.message });
          }
        });
      }
    }
  }

  // parentTypeCode 值改变清空
  @Bind()
  handleParentTypeChange(record) {
    if (record && ['WO', 'ACT', 'MAINTAIN_PLAN'].includes(record)) {
      const { compParentId, compParentName, compParentType } = this.props;
      this.detailDs.current.set('parentId', compParentId);

      if (compParentType === 'WO' && record === 'WO') {
        this.detailDs.current.set('parentName', { woName: compParentName, woId: compParentId });
      } else if (compParentType === 'ACT' && record === 'ACT') {
        this.detailDs.current.set('parentName', { actName: compParentName, actId: compParentId });
      } else if (compParentType === 'MAINTAIN_PLAN' && record === 'MAINTAIN_PLAN') {
        this.detailDs.current.set('parentName', {
          maintainPlanName: compParentName,
          maintainPlanId: compParentId,
        });
      }
    } else {
      this.detailDs.current.set('parentId', null);
      this.detailDs.current.set('parentName', {});
    }
    // 清空业务场景
    this.detailDs.current.init('businessScenarioCode', null);
  }

  @Bind
  handleParentChange(record) {
    if (record) {
      const { compParentType } = this.props;
      const parentTypeCode = this.detailDs.current.get('parentTypeCode');
      const businessScenarioCode = this.detailDs.current.get('businessScenarioCode');

      if (compParentType === 'WO' && parentTypeCode === 'WOOP') {
        this.detailDs.current.set('parentId', record.woopId);
      } else if (
        (compParentType === 'ACT' && parentTypeCode === 'ACT_OP') ||
        (compParentType === 'MAINTAIN_PLAN' && parentTypeCode === 'MAINTAIN_PLAN_STEP')
      ) {
        this.detailDs.current.set('parentId', record.actOpId);
      }

      // praentId 变化 要重新查询父项
      if (businessScenarioCode) {
        this.handleQueryParent(businessScenarioCode);
      }
    } else {
      this.detailDs.current.set('parentId', null);
    }
  }

  /**
   * 仪表类型改变
   */
  @Bind
  onMeterTypeChange(lovRecord) {
    const { meterUomId, meterUom } = lovRecord || {};
    this.detailDs.current.set('meterUomId', meterUomId);
    this.detailDs.current.set('meterUomName', meterUom);
  }

  /**
   * meterId 仪表改变
   * @param {*} lovRecord lov选中的行
   */
  @Bind()
  onMeterChange(lovRecord) {
    const { meterUomId, meterUomName } = lovRecord || {};
    this.detailDs.current.set('meterUomId', meterUomId);
    this.detailDs.current.set('meterUomName', meterUomName);
    if (lovRecord) {
      // 查询仪表点明细 并设置相关信息
      this.setMeterInfo(lovRecord.meterId);
    } else {
      // 清空时 要清空仪表规则table 及 相关字段
      this.detailDs.current.set('enableAlarmFlag', null);
      this.detailDs.current.set('meterClassCode', null);
      this.detailDs.current.set('alarmAccordType', null);
      this.ruleConfigDs.loadData([]);
    }
  }

  @Bind()
  setMeterInfo(meterId) {
    request(`${HALM_MTC}/v1/${organizationId}/meters/${meterId}`, {
      method: 'GET',
      query: {
        alarmRuleStatus: 1,
      },
    }).then(res => {
      if (res && !res.failed) {
        this.detailDs.current.set('enableAlarmFlag', res.enableAlarmFlag);
        this.detailDs.current.set('meterClassCode', res.meterClassCode);
        if (res.enableAlarmFlag) {
          // 告警状态影响
          this.detailDs.current.set('alarmAccordType', res.alarmAccordType);
          // 告警规则
          this.loadAlertRule(res.alarmRuleList);
        }
      }
    });
  }

  // 告警规则
  @Bind()
  loadAlertRule(data) {
    const newData = [];
    let order = 1;
    for (const record of data) {
      newData.push({ ...record, ...JSON.parse(record?.handleContent ?? '{}'), order });
      order++;
    }
    this.ruleConfigDs.loadData(newData);
  }

  // listValueCode 值列表的值改变
  @Bind
  handleValueListChange(val) {
    if (val) {
      this.handleQueryValueList(val);
    } else {
      this.optionsTableDs.data = [];
    }
  }

  @Bind
  handleQueryValueList(code) {
    queryMapIdpValue({
      tenantId: organizationId,
      valueListOfListValueCode: code,
    }).then(res => {
      // 查询成功
      const { valueListOfListValueCode = [] } = res;
      const enabledData = valueListOfListValueCode.filter(i => i.enabledFlag);
      this.optionsTableDs.data = enabledData.map(i => ({
        description: i.meaning,
        exceptionFlag: 0,
        alarmFlag: 0,
        actValue: i.value,
      }));
    });
  }

  // columnTypeCode 字段类型 改变
  @Bind
  handleColumnTypeChange(value) {
    const { prevColumnType } = this.state;
    // 各种情况下 界面会显示的字段
    const obj = {
      TEXT: ['minimumWordLimit'],
      LISTOFVALUE: ['listValueCode'],
      NUMBER: [
        'meterLov',
        'meterUomName',
        'meterUomId',
        'alarmAccordType',
        'enableAlarmFlag',
        'meterClassCode',
      ],
      INDEX: [],
      YESORNO: [],
    };

    // 清空上一个类型的字段的值
    if (prevColumnType) {
      obj[prevColumnType]?.forEach(i => {
        this.detailDs.current.init(i);
      });
      if (prevColumnType === 'LISTOFVALUE' || prevColumnType === 'YESORNO') {
        this.optionsTableDs.loadData([]);
      } else if (prevColumnType === 'NUMBER') {
        // 清除规则table的数据
        this.ruleConfigDs.loadData([]);
      }
    }

    // 字段类型columnTypeCode是索引型时，去掉检测方式methodCode 参考标准standardReference 父项 的值
    if (value === 'INDEX') {
      this.detailDs.current.set({
        methodCode: null,
        // standardReference: null,
      });
      this.detailDs.current.init('parentChecklistLov');
    } else if (value === 'YESORNO') {
      // 默认两条数据
      this.optionsTableDs.data = [
        {
          description: '是',
          exceptionFlag: 0,
          alarmFlag: 0,
          actValue: '1',
        },
        {
          description: '否',
          exceptionFlag: 1,
          alarmFlag: 0,
          actValue: '0',
        },
      ];
    }

    this.setState({
      prevColumnType: value,
    });
  }

  // 筛选 检查时点 数据
  @Bind
  businessScenarioFilter(record) {
    const detail = this.detailDs?.current?.toData?.() || {};
    const { parentTypeCode } = detail;
    return ['WO', 'ACT', 'PRE_POST_CHECK', 'WO_CHECK', 'WO_TYPE', 'MAINTAIN_PLAN'].includes(
      parentTypeCode
    )
      ? record.get('tag') === 'WO'
      : ['WOOP', 'ACT_OP', 'IN_PROCESS_CHECK', 'WOOP_CHECK', 'MAINTAIN_PLAN_STEP'].includes(
          parentTypeCode
        )
      ? record.get('tag') === 'WOOP'
      : record;
  }

  @Bind
  handleBusinessScenarioChange(value) {
    this.handleQueryParent(value);
  }

  // 查询是否有某种检查时点类型的索引项
  @Bind
  handleQueryParent(type) {
    const { parentTypeCode, parentId } = this.detailDs?.current?.toData?.() || {};
    request(`${HALM_MTC}/v1/${organizationId}/wo-checklists/exist-checklist?`, {
      method: 'GET',
      query: {
        tenantId: organizationId,
        parentTypeCode, // WO WOOP(工单); ACT ACT_OP(标准作业); PRE_POST_CHECK IN_PROCESS_CHECK(标准检查组); WO_CHECK WOOP_CHECK(点巡检单)
        parentId, // 对象id
        checkListType: parentTypeCode,
        businessScenarioCode: type,
        columnTypeCode: 'INDEX',
      },
    }).then(res => {
      if (isBoolean(res)) {
        this.setState({
          existParent: res,
        });
      }
    });
  }

  // 标准作业引用的时候要筛选以下数据
  @Bind
  parentTypeCodeFilter(record) {
    const { compParentType } = this.props;
    if (compParentType === 'ACT') {
      return record.get('tag') === 'ACT';
    } else if (compParentType === 'MAINTAIN_PLAN') {
      return record.get('tag') === 'MAINTAIN_PLAN';
    }
    return record;
  }

  get optionsTableColumns() {
    const columnTypeCode = this.detailDs.current.get('columnTypeCode');
    const { isNew, editFlag } = this.props;
    return [
      {
        name: 'description',
        editor: columnTypeCode !== 'LISTOFVALUE' && (isNew || editFlag),
      },
      {
        name: 'exceptionFlag',
        editor: true,
      },
      {
        name: 'alarmFlag',
        editor: true,
      },
    ];
  }

  render() {
    const { isNew, editFlag, statusFlag = true, compParentType } = this.props;

    const { existParent } = this.state;
    const detail = this.detailDs?.current?.toData?.() || {};
    const {
      parentChecklistId,
      parentTypeCode,
      parentId,
      businessScenarioCode,
      columnTypeCode,
      checkTiming,
      enableAlarmFlag,
      meterClassCode,
    } = detail;
    // 父项的disabled
    let parentChecklistDis = false;
    if (['WO', 'ACT', 'SPOT_CHECK', 'WO_TYPE', 'WOOP', 'MAINTAIN_PLAN'].includes(compParentType)) {
      // 工单、标准作业、点巡检单为该禁用逻辑
      parentChecklistDis =
        !existParent || !statusFlag || !parentTypeCode || !parentId || !businessScenarioCode;
    } else if (compParentType === 'CHECKLIST_GROUP') {
      parentChecklistDis = !existParent || !statusFlag || !checkTiming;
    }
    // 启用告警管理
    const isEnableAlarmFlag = columnTypeCode === 'NUMBER' && enableAlarmFlag === 1;
    // 存在仪表点规则
    const hasMeterRule = isEnableAlarmFlag && this.ruleConfigDs.records.length > 0;
    const ruleConfigTableProps = {
      meterClassCode,
      ruleConfigDs: this.ruleConfigDs,
    };
    return (
      <React.Fragment>
        {isNew || editFlag ? (
          <>
            <Form dataSet={this.detailDs} columns={2}>
              {editFlag && <NumberField name="itemSeq" />}
              {/* 标准检查组'CHECKLIST_GROUP'、点巡检单引用时 不可见；工单、标准作业可见 */}
              {['ACT', 'WO', 'MAINTAIN_PLAN'].includes(compParentType) && (
                <div name="parentTypeCode" newLine={editFlag}>
                  <Row gutter={8}>
                    <Col span={11}>
                      <Select
                        name="parentTypeCode"
                        dropdownMatchSelectWidth={false}
                        disabled={parentChecklistId || !statusFlag || editFlag}
                        onChange={this.handleParentTypeChange}
                        optionsFilter={this.parentTypeCodeFilter}
                      />
                    </Col>
                    <Col span={13}>
                      <Lov
                        name="parentName"
                        tableProps={{ className: styles['table-long-label'] }}
                        onChange={this.handleParentChange}
                        disabled={
                          !parentTypeCode ||
                          ['WO', 'ACT', 'MAINTAIN_PLAN'].includes(parentTypeCode) ||
                          (parentId && parentTypeCode && parentChecklistId) ||
                          !statusFlag ||
                          editFlag
                        }
                      />
                    </Col>
                  </Row>
                </div>
              )}
              {/* 工单、标准作业、点巡检单的检查时点 */}
              {['ACT', 'WO', 'SPOT_CHECK', 'WO_TYPE', 'WOOP', 'MAINTAIN_PLAN'].includes(
                compParentType
              ) && (
                <Select
                  name="businessScenarioCode"
                  disabled={parentChecklistId || !statusFlag || !parentTypeCode || !parentId}
                  optionsFilter={this.businessScenarioFilter}
                  onChange={this.handleBusinessScenarioChange}
                />
              )}
              {/* 标准检查组的检查时点 */}
              {compParentType === 'CHECKLIST_GROUP' && (
                <Select
                  name="checkTiming"
                  disabled={parentChecklistId || !statusFlag}
                  optionsFilter={this.businessScenarioFilter}
                  onChange={this.handleBusinessScenarioChange}
                />
              )}
              <TextField name="checklistName" disabled={!statusFlag} />
              {columnTypeCode !== 'INDEX' && <TextField name="methodCode" disabled={!statusFlag} />}
              {/* {columnTypeCode !== 'INDEX' && (
                <TextField name="standardReference" disabled={!statusFlag} />
              )} */}
              {columnTypeCode !== 'INDEX' && (
                <Lov name="parentChecklistLov" disabled={parentChecklistDis} />
              )}
              <Select
                name="columnTypeCode"
                disabled={!statusFlag || (editFlag && columnTypeCode === 'INDEX')}
                onChange={this.handleColumnTypeChange}
              />
              {columnTypeCode === 'TEXT' && (
                <TextField name="minimumWordLimit" disabled={!statusFlag} />
              )}
              {columnTypeCode === 'LISTOFVALUE' && (
                <Select name="listValueCode" onChange={this.handleValueListChange} />
              )}
              {/* 标准作业、工单类型、标准检查组 维保计划显示仪表点类型 */}
              {columnTypeCode === 'NUMBER' &&
                (['ACT', 'WO_TYPE', 'CHECKLIST_GROUP', 'MAINTAIN_PLAN'].includes(compParentType) ? (
                  <Lov
                    name="meterTypeLov"
                    disabled={!statusFlag}
                    onChange={this.onMeterTypeChange}
                  />
                ) : (
                  <Lov name="meterLov" disabled={!statusFlag} onChange={this.onMeterChange} />
                ))}
              {columnTypeCode === 'VALUE' && (
                <>
                  <NumberComponent
                    showStandard
                    name="trueValue"
                    parentDs={this.detailDs}
                    dataSet={this.trueNumberDs}
                    canEdit={statusFlag}
                  />
                  <Switch name='isThereAlarm' />
                </>
              )}
              {columnTypeCode === 'NUMBER' && <TextField name="meterUomName" disabled />}
              {isEnableAlarmFlag && <Select name="alarmAccordType" disabled />}
            </Form>
            {hasMeterRule && <RuleConfigTable {...ruleConfigTableProps} />}
          </>
        ) : (
          <Collapse
            bordered={false}
            defaultActiveKey={['A', 'B']}
            expandIconPosition="right"
            expandIcon="text"
          >
            <Collapse.Panel key="A" header={intl.get(`${viewPrompt}.panel.basic`).d('基础信息')}>
              <Form dataSet={this.detailDs} columns={2}>
                {/* 标准检查组'CHECKLIST_GROUP'、点巡检单引用时 不可见；工单、标准作业可见 */}
                {['ACT', 'WO', 'MAINTAIN_PLAN'].includes(compParentType) && (
                  <Output
                    name="parentTypeCode"
                    tooltip="none"
                    renderer={() => (
                      <Row gutter={8}>
                        <Col span={11}>
                          <Output name="parentTypeCode" />
                        </Col>
                        <Col span={13}>
                          <Output name="parentName" />
                        </Col>
                      </Row>
                    )}
                  />
                )}
                {/* 工单、标准作业、点巡检单的检查时点 */}
                {['ACT', 'WO', 'SPOT_CHECK', 'MAINTAIN_PLAN'].includes(compParentType) && (
                  <Output name="businessScenarioCode" />
                )}
                {/* 标准检查组的检查时点 */}
                {compParentType === 'CHECKLIST_GROUP' && <Output name="checkTiming" />}
                <Output name="checklistName" />
                {columnTypeCode !== 'INDEX' && <Output name="methodCode" />}
                {/* {columnTypeCode !== 'INDEX' && <Output name="standardReference" />} */}
                {columnTypeCode !== 'INDEX' && <Output name="parentChecklistName" />}
                <Output name="columnTypeCode" />
                {columnTypeCode === 'TEXT' && <Output name="minimumWordLimit" />}
                {columnTypeCode === 'LISTOFVALUE' && <Output name="listValueCode" />}
                {columnTypeCode === 'NUMBER' &&
                  (['ACT', 'WO_TYPE', 'CHECKLIST_GROUP', 'MAINTAIN_PLAN'].includes(
                    compParentType
                  ) ? (
                    <Output name="meterTypeLov" />
                  ) : (
                    <Output name="meterName" />
                    ))}
                {columnTypeCode === 'VALUE' && (
                  <>
                    <NumberComponent
                      showStandard
                      name="trueValue"
                      parentDs={this.detailDs}
                      dataSet={this.trueNumberDs}
                      canEdit={editFlag || statusFlag}
                    />
                    <Output name='isThereAlarm' />
                  </>
                )}
                {columnTypeCode === 'NUMBER' && <Output name="meterUomName" />}
                {isEnableAlarmFlag && <Output name="alarmAccordType" />}
              </Form>
              {hasMeterRule && <RuleConfigTable {...ruleConfigTableProps} />}
            </Collapse.Panel>
            {['WO', 'WOOP', 'SPOT_CHECK'].includes(compParentType) && (
              <Collapse.Panel key="B" header={intl.get(`${viewPrompt}.panel.actValue`).d('实际值')}>
                <Form dataSet={this.detailDs} columns={3}>
                  {['YESORNO', 'LISTOFVALUE'].includes(columnTypeCode) ? (
                    <Output name="actValueMeaning" />
                  ) : (
                    <Output name="actValue" />
                  )}
                  <Output colSpan={3} name="description" newLine />
                </Form>
              </Collapse.Panel>
            )}
          </Collapse>
        )}
        {(columnTypeCode === 'YESORNO' || columnTypeCode === 'LISTOFVALUE') && (
          <>
            可选项
            <Table
              dataSet={this.optionsTableDs}
              columns={this.optionsTableColumns}
              pagination={false}
            />
          </>
        )}
      </React.Fragment>
    );
  }
}
export default WoChecklistEdit;
