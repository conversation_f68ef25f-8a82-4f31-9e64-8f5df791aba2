/**
 * @Description: 产品审核任务-列表页
 * @Author: <<EMAIL>>
 * @Date: 2023-10-09 17:57:58
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-18 09:43:07
 */
import React, { useMemo, useCallback, useState } from 'react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { useDataSetEvent } from 'utils/hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnLock } from 'choerodon-ui/pro/es/table/enum';
import notification from 'utils/notification';
import { tableDS } from '../stores';
import { CancelOrPickTask } from '../services';

const modelPrompt = 'tarzan.qms.productReview.productReviewTask';

const ProductReviewTaskList = (props) => {
  const {
    tableDs,
    history,
  } = props;
  const [selectTaskIds, setSelectTaskIds] = useState<any>([]);
  const [selectTaskStatus, setSelectTaskStatus] = useState<any>([]);
  const { run: cancelOrPickTask } = useRequest(CancelOrPickTask(), { manual: true, needPromise: true });

  const handleUpdateSelect = () => {
    const _selectTaskIds: any = [];
    const _selectTaskStatus: any = [];
    tableDs.selected.forEach(record => {
      const { productRevTaskId, productRevTaskStatus } = record.toData();
      _selectTaskIds.push(productRevTaskId);
      if (!_selectTaskStatus.includes(productRevTaskStatus)) {
        _selectTaskStatus.push(productRevTaskStatus);
      }
    })
    setSelectTaskIds(_selectTaskIds);
    setSelectTaskStatus(_selectTaskStatus);
  };

  useDataSetEvent(tableDs, 'batchSelect', handleUpdateSelect);
  useDataSetEvent(tableDs, 'batchUnSelect', handleUpdateSelect);

  const renderStatusTag = (value, record) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
        className = 'blue';
        break;
      case 'TO_ACCEPT':
        className = 'green';
        break;
      case 'EXECUTING':
        className = 'magenta';
        break;
      case 'REVIEWING':
        className = 'yellow';
        break;
      case 'IN_APPROVAL':
        className = 'orange';
        break;
      case 'REVIEW_REJECT':
      case 'REJECTED':
        className = 'red';
        break;
      default:
        className = 'gray';
    }
    return <Tag color={className}>{record!.getField('productRevTaskStatus')!.getText()}</Tag>;
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'productRevTaskCode',
        width: 180,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hwms/product-review/product-review-task/dist/${record!.get('productRevTaskId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'siteName' },
      { name: 'productRevPlanCode', width: 180 },
      { name: 'productRevSchemeCode', width: 180 },
      {
        name: 'productRevTaskStatus',
        align: ColumnAlign.center,
        renderer: ({ value, record }) => renderStatusTag(value, record),
      },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'cusMaterialCode', width: 180 },
      { name: 'cusMaterialName', width: 180 },
      { name: 'workcellCode', width: 180 },
      { name: 'workcellName', width: 180 },
      { name: 'productRevTaskType', width: 180 },
      { name: 'publishedName' },
      { name: 'samplePosition' },
      { name: 'warehouseCode', width: 180 },
      { name: 'sampleQty' },
      { name: 'reviewedName' },
      { name: 'executeDate', width: 150, align: ColumnAlign.center },
    ];
  }, []);

  const handleCancelOrPickTask = (targetStatus) => {
    return new Promise(async resolve => {
      const res = await cancelOrPickTask({ params: {
        productRevTaskIds: selectTaskIds,
        productRevTaskStatus: targetStatus,
      }});
      if (res && res.success) {
        notification.success({});
        tableDs.query()
        return resolve(true);
      }
      return resolve(false);
    })
  }

  const handleAdd = useCallback(() => {
    history.push(`/hwms/product-review/product-review-task/dist/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('产品审核任务')}>
        <Button
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <Button
          disabled={!selectTaskIds?.length || selectTaskStatus.length !== 1 || !selectTaskStatus.includes('NEW')}
          onClick={() => handleCancelOrPickTask('CANCEL')}
        >
          {intl.get(`${modelPrompt}.button.cancelTask`).d('取消任务')}
        </Button>
        <Button
          disabled={!selectTaskIds?.length || selectTaskStatus.length !== 1 || !selectTaskStatus.includes('TO_ACCEPT')}
          onClick={() => handleCancelOrPickTask('EXECUTING')}
        >
          {intl.get(`${modelPrompt}.button.pickTask`).d('领取任务')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="productReviewTaskList_searchCode"
          customizedCode="productReviewTaskList_customizedCode"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ProductReviewTaskList),
);
