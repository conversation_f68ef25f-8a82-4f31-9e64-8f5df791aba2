/*
 * @Description: 条码审核结果录入-抽屉
 * @Author: <<EMAIL>>
 * @Date: 2023-10-12 15:39:14
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-10-17 18:43:33
 */
import React, { useRef } from 'react';
import { observer } from 'mobx-react';
import { Table, Lov, Select, Button, Form, TextField } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import scanImg from '@/assets/icons/scan-o.svg';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { FuncType } from 'choerodon-ui/pro/es/button/enum';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import { QueryBarcodeInfo } from '../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.qms.productReview.productReviewTask';

export default observer(({ dataSet, parentRecord, detailDs, disabled }) => {
  const { run: queryBarcodeInfo, loading: queryBarcodeLoading } = useRequest(QueryBarcodeInfo(), { manual: true });
  const scanBarcodeRef = useRef<any>(null);

  const handleChangeResult = (record) => {
    record.init('ncCodeLov', undefined);
  }

  const handleDeleteLine = record => {
    dataSet.remove(record);
  }

  const getColumns: any = (recordDataFlag, disabled, fromOrtFlag) => ([
    fromOrtFlag === 'Y' && {
      name: 'delete',
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDeleteLine(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button icon="remove" disabled={disabled} funcType={FuncType.flat} />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      width: 80,
      align: ColumnAlign.left,
      renderer: ({ record }) => record.index + 1,
    },
    {
      name: 'productRevSampleCode',
      width: 230,
    },
    recordDataFlag === 'Y' && {
      name: 'testData',
      editor: !disabled,
    },
    {
      name: 'reviewResult',
      editor: record => !disabled && <Select onChange={() => handleChangeResult(record)} />,
    },
    {
      name: 'ncCodeLov',
      editor: !disabled && <Lov />,
    },
    {
      name: 'ncCodeDesc',
    },
    {
      name: 'defectLevel',
      editor: !disabled,
    },
    {
      name: 'defectPoint',
      editor: !disabled,
    },
  ]);

  const handleScanBarcode = e => {
    const scanValue = e.target.value;
    if (!scanValue) {
      return;
    }
    if (dataSet.find((_record) => _record?.get('productRevSampleCode') === scanValue.trim())) {
      notification.error({
        message: intl.get(`${modelPrompt}.barcode.exist`).d('扫描的条码已存在，请检查！'),
      });
      return;
    }
    queryBarcodeInfo({
      params: {
        barcode: scanValue.trim(),
        queryType: detailDs.current?.get('samplePosition') === 'WORKSHOP' ? 'MES' : 'WMS',
        materialId: detailDs.current?.get('materialId'),
      },
      onSuccess: (res) => {
        const { barcode, barcodeId } = res;
        if (barcode) {
          dataSet.create({
            productRevTaskId: detailDs.current?.get('productRevTaskId'),
            productRevSampleCode: barcode,
            productRevSampleBarcodeId: barcodeId,
          }, 0);
        } else {
          notification.error({
            message: intl.get(`${modelPrompt}.barcode.error`).d('扫描的条码不存在，请检查！'),
          })
        }
        scanBarcodeRef?.current?.focus();
      },
    })
  }

  return (
    <TarzanSpin dataSet={dataSet} spinning={queryBarcodeLoading}>
      {parentRecord.get('fromOrtFlag') === 'Y' && (
        <Form columns={2}>
          <TextField
            ref={scanBarcodeRef}
            className={styles['login-scan-form']}
            label={intl.get(`${modelPrompt}.productRevSampleBarcode`).d('条码')}
            placeholder={intl.get(`${modelPrompt}.placeholder.barcode`).d('请扫描/输入条码')}
            suffix={<img alt="" src={scanImg} />}
            onEnterDown={handleScanBarcode}
            disabled={disabled}
          />
        </Form>
      )}
      <Table
        dataSet={dataSet}
        columns={getColumns(parentRecord.get('recordDataFlag'), disabled, parentRecord.get('fromOrtFlag'))}
        queryBar={TableQueryBarType.bar}
      />
    </TarzanSpin>
  )
})