/**
 * @Description: 量具型号与MSA分析质量特性关系维护-service
 * @Author: <EMAIL>
 * @Date: 2023/8/16 15:10
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 查询用户默认站点
 * @function GetDefaultSite
 * @returns {object} fetch Promise
 */
export function GetDefaultSite(): object {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/ui`,
    method: 'GET',
  };
}

/**
 * 保存数据
 * @function SaveMsaRelation
 * @returns {object} fetch Promise
 */
export function SaveMsaRelation(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-ms-model-msa-rel/relSave`,
    method: 'POST',
  };
}
