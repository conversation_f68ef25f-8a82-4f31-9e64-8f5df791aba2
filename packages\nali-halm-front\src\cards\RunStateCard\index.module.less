.container {
  height: 100%;
  padding: 0 12px;
}

.collapse-panel {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 17px;
}
.ball {
  position: relative;
  z-index: 0;
  display: inline-block;
  flex: none;
  width: 120px;
  height: 120px;
  overflow: hidden;
  border: 2px solid #3b87f5;
  border-radius: 50%;

  &::after {
    position: absolute;
    top: 0;
    width: 116px;
    height: 116px;
    border: 2px solid #fff;
    border-radius: 50%;
    content: '';
  }
}

.percent {
  position: absolute;
  top: 10px;
  width: 100%;
  color: #333;
  font-weight: 700;
  text-align: center;
  span {
    font-size: 18px !important;
    font-family: D-DINExp-Bold, sans-serif;
  }
}

.data-container {
  display: inline-block;
  flex: none;
  margin-left: 50px;

  .data-item {
    color: #333;
    font-weight: 400;
    font-size: 12px;
    font-family: PingFangSC-Regular, sans-serif;

    span {
      color: #333;
      font-weight: 700;
      font-size: 16px;
      font-family: D-DINExp-Bold, sans-serif;
    }
  }
}

.customize-collapse {
  height: 100%;
  :global(.c7n-collapse-item) {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
  }
  :global(.c7n-collapse-content-box) {
    height: 100%;
  }
  :global(.c7n-collapse-header) {
    padding: 12px 0 4px 8px !important;
    &::before {
      top: calc(50% - 0.07rem + 4px) !important;
    }
  }
}
