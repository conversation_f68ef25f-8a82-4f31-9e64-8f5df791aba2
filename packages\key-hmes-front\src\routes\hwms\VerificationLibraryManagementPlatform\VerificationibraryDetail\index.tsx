/**
 * @Description: 检验项目组维护明细界面
 * @Author: <<EMAIL>>
 * @Date: 2023-01-11 09:55:10
 * @LastEditTime: 2023-05-18 16:50:37
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo } from 'react';
import intl from 'utils/intl';
import request from 'utils/request';
import { Header, Content } from 'components/Page';
import {
  DataSet,
  Form,
  TextField,
  Select,
  Attachment,
  Spin,
  Lov,
  Menu,
  Dropdown,
  Modal,
  TextArea,
  Table,
  Button,
  Icon,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

import { TableButtonType } from 'choerodon-ui/pro/lib/table/enum';
import { observer } from 'mobx-react';
import { ColumnAlign } from 'choerodon-ui/pro/es/table/enum';
import { C7nFormItemSort } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import InspectItemTab from './InspectItemTab';
import { CopyEnclosure } from '../services';
import { DetailFormDS, DetailTableDS, approveDS, problemReasonDS, measureDS } from '../stories';

const modelPrompt = 'tarzan.qms.verificationLibraryManagementPlatform';
const tenantId = getCurrentOrganizationId();

const { Panel } = Collapse;

const InspectGroupDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    customizeForm,
    customizeTable,
    custConfig,
  } = props;

  const [canEdit, setCanEdit] = useState(false);
  const [formObj, setFormObj] = useState(Object);
  const [loading, setLoading] = useState(false);
  const tableDs = useMemo(() => new DataSet(DetailTableDS()), []);
  const problemReasonDs = useMemo(() => new DataSet(problemReasonDS()), []);
  const measureDs = useMemo(() => new DataSet(measureDS()), []);
  const formDs = useMemo(
    () =>
      new DataSet({
        ...DetailFormDS(),
        children: {
          problemReasonInfo: problemReasonDs,
          measureInfo: measureDs,
          taskInfo: tableDs,
        },
      }),
    [],
  );
  const approvalDs = useMemo(() => new DataSet(approveDS()), []);
  const { run: copyEnclosure } = useRequest(CopyEnclosure(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if (id === 'create') {
      const lovObj = props.location.state;
      if (lovObj) {
        const { problemReasonInfo, measureInfo } = lovObj;
        (problemReasonInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
        (measureInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
        formDs.loadData([
          {
            verificationStatus: 'NEW',
            materialCode: lovObj.materialCode,
            materialId: lovObj.materialId,
            materialName: lovObj.materialName,
            itemGroup: lovObj.itemGroup,
            itemGroupDesc: lovObj.itemGroupDesc,
            responsibleArea: lovObj.qualityProblemType,
            problemCode: lovObj.problemCode,
            problemDescription: lovObj.problemDescription,
            problemId: lovObj.problemId,
            fromProcessId: lovObj.processId,
            fromProcessName: lovObj.processName,
            siteId: lovObj.siteId,
            siteCode: lovObj.siteCode,
            siteName: lovObj.siteName,
            problemReasonInfo,
            measureInfo,
          },
        ]);
        handleCopyEnclosure(lovObj.uuid);
      } else {
        formDs.loadData([{ verificationStatus: 'NEW' }]);
      }
      setCanEdit(true);
      return;
    }
    initPageData();
  }, [id]);

  const handleCopyEnclosure = async uuid => {
    if (!uuid) {
      formDs.current?.set('verificationUuid', null);
    }
    const res = await copyEnclosure({
      params: {
        uuidList: [uuid],
      },
    });
    if (res && Object.keys(res).length) {
      formDs.current?.set('verificationUuid', res[uuid]);
    }
  };

  // 获取查询数据
  const initPageData = async () => {
    setLoading(true);
    const params = {
      verificationId: id,
    };
    return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/edit/query/ui`, {
      method: 'GET',
      query: params,
    }).then(res => {
      setLoading(false);
      if (res && !res.failed) {
        const { taskInfo, measureInfo, problemReasonInfo } = res;
        (problemReasonInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
        (measureInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
        const formFinalData = {
          ...res,
          applicableChemicalSystem: res.applicableChemicalSystem.split(','),
          applicableProductLine: res.applicableProductLine.split(','),
          applicableProductStructure: res.applicableProductStructure.split(','),
          applyArea: res.applyArea.split(','),
        };
        setFormObj(formFinalData);
        formDs.loadData([formFinalData]);
        tableDs.loadData(taskInfo);
        problemReasonDs.loadData(problemReasonInfo);
        measureDs.loadData(measureInfo);
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  // 保存
  const handleSave = async flag => {
    // flag为true保存后保留界面，为false保存后提示是否新建下一条
    if (tableDs.toData().length === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.create.leastone`).d(`最少新建一条行数据！`),
      });
      return Promise.resolve(false);
    }
    const formValidate = await formDs.validate();
    if (!formValidate) {
      return Promise.resolve(false);
    }
    const formData = formDs.toData()[0] as any;
    const taskInfo = [] as object[];
    const delTaskIds = [] as number[];
    tableDs.toData().forEach((item: any) => {
      taskInfo.push(item);
    });
    tableDs.toJSONData().forEach((item: any) => {
      if (item._status === 'delete') {
        delTaskIds.push(item.verificationTaskId);
      }
    });

    const inspectGroupItemIds = [];
    tableDs.toData().forEach((item: any) => {
      if (item?.verificationTaskId && item?.verificationTaskId > 0)
        // @ts-ignore
        inspectGroupItemIds.push(item.verificationTaskId);
    });
    const formFinalData = {
      ...formData,
      applicableChemicalSystem: formData.applicableChemicalSystem.join(','),
      applicableProductLine: formData.applicableProductLine.join(','),
      applicableProductStructure: formData.applicableProductStructure.join(','),
      applyArea: formData.applyArea.join(','),
    };

    const params = {
      ...formFinalData,
      delTaskIds,
      taskInfo,
    };
    setLoading(true);
    return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/save/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      setLoading(false);
      if (res && !res.failed) {
        setCanEdit(prev => !prev);
        notification.success({});
        const params1 = [res.verificationId];
        if (flag === 'submit') {
          setTimeout(() => {
            return request(
              `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/submit/ui`,
              {
                method: 'POST',
                body: params1,
              },
            ).then(resSubmit => {
              if (resSubmit && !resSubmit.failed) {
                // setCanEdit(prev => !prev);
                notification.success({});
                if (id === 'create') {
                  props.history.push(
                    `/hwms/verification-library-management-platform/dist/${res.verificationId}`,
                  );
                } else {
                  initPageData();
                }
              } else {
                notification.error({ message: resSubmit.message });
                return false;
              }
            });
          }, 50);
        } else if (id === 'create') {
          props.history.push(
            `/hwms/verification-library-management-platform/dist/${res.verificationId}`,
          );
        } else {
          initPageData();
        }
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
  };

  const handleSubmit = async res1 => {
    if (canEdit) {
      handleSave('submit');
    } else {
      const formValidate = await formDs.validate();
      if (tableDs.toData().length === 0) {
        notification.error({
          message: intl.get(`${modelPrompt}.create.leastone`).d(`最少新建一条行数据！`),
        });
        return Promise.resolve(false);
      }
      const tableValidate = await tableDs.validate();
      if (!formValidate || !tableValidate) {
        return Promise.resolve(false);
      }
      setLoading(true);
      const params = [res1.verificationId ? res1.verificationId : id];
      return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/submit/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        setLoading(false);
        if (res && !res.failed) {
          // setCanEdit(prev => !prev);
          notification.success({});
          if (id === 'create') {
            props.history.push(
              `/hwms/verification-library-management-platform/dist/${res.verificationId}`,
            );
          } else {
            initPageData();
          }
        } else {
          notification.error({ message: res.message });
          return false;
        }
      });
    }
  };

  // 取消
  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hwms/verification-library-management-platform/list');
      return;
    }
    setCanEdit(false);
    initPageData();
  };

  const handleApprove = () => {
    setLoading(true);
    const params = [id];
    return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/approval/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      setLoading(false);
      if (res && !res.failed) {
        // setCanEdit(prev => !prev);
        notification.success({});
        if (id === 'create') {
          props.history.push(
            `/hwms/verification-library-management-platform/dist/${res.verificationId}`,
          );
        } else {
          initPageData();
        }
      } else {
        notification.error({ message: res.message });
        return false;
      }
    });
    // return approval.run({
    //   params,
    //   onSuccess: res => {
    //     if (res) {
    //       // @ts-ignore
    //       notification.success();
    //     }
    //   },
    // });
  };

  const handleunApprove = () => {
    approvalDs.create({});
    const modal = Modal.open({
      title: intl.get(`${modelPrompt}.rejectReason`).d('驳回原因'),
      destroyOnClose: true,
      children: (
        <Form dataSet={approvalDs}>
          <TextArea name="rejectReason" required />,
        </Form>
      ),
      afterClose: () => approvalDs.reset(),
      // drawer: true,
      onOk: async () => {
        const formValidate = await approvalDs.validate();
        if (!formValidate) {
          notification.error({
            message:  intl.get(`${modelPrompt}.pleaseEnter.rejectReason`).d('请输入驳回原因'),
          });
          return false;
        }
        const params = {
          verificationId: id,
          ...approvalDs.toData()[0],
        };
        setLoading(true);
        return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-verification-temps/reject/ui`, {
          method: 'POST',
          body: params,
        }).then(res => {
          if (res && !res.failed) {
            // setCanEdit(prev => !prev);
            notification.success({});
            modal.close();
            if (id === 'create') {
              props.history.push(
                `/hwms/verification-library-management-platform/dist/${res.verificationId}`,
              );
            } else {
              initPageData();
            }
          } else {
            notification.error({ message: res.message });
            return false;
          }
          setLoading(false);
        });
        // return unApproval.run({
        //   params,
        //   onSuccess: res => {
        //     if (res) {
        //       // @ts-ignore
        //       notification.success();
        //     }
        //   },
        // });
      },
      // onCancel: () => {
      //   headInfoDs.reset();
      // },
    });
  };

  const handleLov = value => {
    if (value) {
      formDs.current?.set('materialId', value.materialId);
      formDs.current?.set('materialCode', value.materialCode);
      formDs.current?.set('materialName', value.materialName);
      formDs.current?.set('fromProcessId', value.processId);
      formDs.current?.set('fromProcessName', value.processName);
      formDs.current?.set('problemDescription', value.problemDescription);
      formDs.current?.set('responsibleArea', value.qualityProblemType);
      formDs.current?.set('problemReason', value.problemReason);
      formDs.current?.set('escapeReason', value.escapeReason);
      formDs.current?.set('measure', value.measure);
      formDs.current?.set('itemGroupDesc', value.itemGroupDesc);

      const { measureInfo, problemReasonInfo } = value;
      (problemReasonInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
      (measureInfo || []).forEach((item, index) => (item.sequence = (index + 1) * 10));
      problemReasonDs.loadData(problemReasonInfo || []);
      measureDs.loadData(measureInfo || []);

      handleCopyEnclosure(value.uuid);
    } else {
      formDs.current?.set('materialId', null);
      formDs.current?.set('materialCode', '');
      formDs.current?.set('materialName', '');
      formDs.current?.set('fromProcessId', null);
      formDs.current?.set('fromProcess', '');
      formDs.current?.set('problemDescription', '');
      formDs.current?.set('responsibleArea', '');
      formDs.current?.set('problemReason', '');
      formDs.current?.set('escapeReason', '');
      formDs.current?.set('measure', '');
      formDs.current?.set('verificationUuid', null);
      formDs.current?.set('responsibleArea', '');
      formDs.current?.set('itemGroupDesc', '');
      problemReasonDs.loadData([]);
      measureDs.loadData([]);
    }
  };

  const handleSiteLov = (val, oldValue) => {
    if (formDs?.current?.get('problemCode') && oldValue && oldValue.siteId !== val.siteId)
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: <p>{intl.get(`${modelPrompt}.changeSiteCode`).d('切换站点会清空问题描述内容，确定切换站点吗')}？</p>,
      }).then(button => {
        if (button === 'ok') {
          formDs.current?.set('problemId', null);
          formDs.current?.set('problemCode', '');
          formDs.current?.set('materialId', null);
          formDs.current?.set('materialCode', '');
          formDs.current?.set('materialName', '');
          formDs.current?.set('fromProcessId', null);
          formDs.current?.set('fromProcessName', '');
          formDs.current?.set('problemDescription', '');
          formDs.current?.set('responsibleArea', '');
          formDs.current?.set('problemReason', '');
          formDs.current?.set('escapeReason', '');
          formDs.current?.set('measure', '');
          formDs.current?.set('verificationUuid', null);
          formDs.current?.set('itemGroupDesc', '');
          problemReasonDs.loadData([]);
          measureDs.loadData([]);
        } else {
          formDs.current?.set('siteId', oldValue.siteId);
          formDs.current?.set('siteCode', oldValue.siteCode);
        }
      });
  };

  // 检验项目组件传参
  const inspectItemTabProps = {
    path,
    id,
    canEdit,
    customizeTable,
    customizeForm,
    custConfig,
    tableDs,
    loading,
    verificationStatus: formObj?.verificationStatus,
  };

  const menu = (
    <Menu>
      <Menu.Item disabled={formObj?.verificationStatus !== 'IN_APPROVAL'}>
        <a target="_blank" rel="noopener noreferrer" onClick={handleApprove}>
          {intl.get(`${modelPrompt}.agree`).d('同意')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={formObj?.verificationStatus !== 'IN_APPROVAL'}>
        <a target="_blank" rel="noopener noreferrer" onClick={handleunApprove}>
          {intl.get(`${modelPrompt}.reject`).d('驳回')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const problemReasonColumns: any = useMemo(
    () => [
      { name: 'sequence', width: 100, align: ColumnAlign.left },
      { name: 'occur', editor: canEdit },
      { name: 'escape', editor: canEdit },
    ],
    [canEdit],
  );

  const measureColumns: any = useMemo(
    () => [
      { name: 'sequence', width: 100, align: ColumnAlign.left },
      { name: 'measure', editor: canEdit },
    ],
    [canEdit],
  );

  // 附件配置
  const attachmentProps: any = {
    name: 'verificationUuid',
    bucketName: 'qms',
    bucketDirectory: 'verification-library-management-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const RenderDeleteButton = observer(({ dataSet, canEdit }) => {
    return (
      <>
        <Button
          icon="delete"
          funcType={FuncType.flat}
          color={ButtonColor.red}
          disabled={
            !canEdit ||
            !dataSet.selected.length ||
            !['NEW', 'REJECTED'].includes(dataSet.parent.current.get('verificationStatus'))
          }
          onClick={() => dataSet.remove(dataSet.selected)}
        >
          {intl.get('hzero.common.button.delete').d('删除')}
        </Button>
      </>
    );
  });

  const ProblemConponent = observer(({ dataSet, canEdit }) => {
    const handleJump = () => {
      props.history.push(`/hwms/problem-management/problem-management-platform/dist/${dataSet.current?.get('problemId')}`);
    };

    const getColor = (name, dataSet, canEdit) => {
      if (!dataSet.current?.get(name) || canEdit) {
        return `rgba(0,0,0,0.25)`;
      }
      return '#29bece';
    };

    return (
      <C7nFormItemSort name="tempProblemLov" itemWidth={['92%', '7%']}>
        <Lov name="tempProblemLov" onChange={value => handleLov(value)} />
        <Icon
          type="link2"
          // @ts-ignore
          itemType="link"
          onClick={handleJump}
          style={{ color: getColor('problemId', dataSet, canEdit) }}
          iconDisabled
        />
      </C7nFormItemSort>
    )
  })

  return (
    <div className="hmes-style">
      <Header
        title={
          id === 'create' ?
            intl.get(`${modelPrompt}.verifyProject.creation`).d('检证项目新建') :
            intl.get(`${modelPrompt}.verifyProject.editing`).d('检证项目编辑')
        }
        backPath="/hwms/verification-library-management-platform"
      >
        {/* {user.currentRoleLabels && user.currentRoleLabels.includes('YP.QMS.PROQ_ENGINNER') && ( */}
        <Popconfirm
          title={intl.get(`${modelPrompt}.sureSubmit`).d('是否确认提交?')}
          onConfirm={() => {
            // this.tableDs.remove(record);
            handleSubmit({});
          }}
        >
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="save"
            permissionList={[
              {
                code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.saveSubmit`,
                type: 'button',
                meaning: '保存并提交',
              },
            ]}
            disabled={
              formObj?.verificationStatus === 'IN_APPROVAL' ||
              formObj?.verificationStatus === 'EXPIRED'
            }
            // onClick={() => handleSave('save')}
          >
            {intl.get(`${modelPrompt}.saveAndSubmit`).d('保存并提交')}
          </PermissionButton>
        </Popconfirm>
        {/* )} */}

        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              permissionList={[
                {
                  code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.save`,
                  type: 'button',
                  meaning: '保存',
                },
              ]}
              disabled={
                formObj?.verificationStatus === 'IN_APPROVAL' ||
                formObj?.verificationStatus === 'EXPIRED'
              }
              onClick={() => handleSave('save')}
            >
              {intl.get('hzero.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              icon="close"
              permissionList={[
                {
                  code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.cancel`,
                  type: 'button',
                  meaning: '取消',
                },
              ]}
              onClick={handleCancel}
            >
              {intl.get('hzero.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        )}
        {!canEdit && (
          <>
            {/* {user.currentRoleLabels &&
              user.currentRoleLabels.includes('YP.QMS.PROQ_SENIOR_MANAGER') && ( */}
            <Dropdown overlay={menu}>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                permissionList={[
                  {
                    code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.check`,
                    type: 'button',
                    meaning: '审批按钮',
                  },
                ]}
                disabled={formObj?.verificationStatus !== 'IN_APPROVAL'}
                // onClick={handleImport}
              >
                {intl.get(`${modelPrompt}.approve`).d('审批')}
              </PermissionButton>
            </Dropdown>
            {/* )} */}
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              onClick={() => {
                setCanEdit(prev => !prev);
              }}
              disabled={
                formObj?.verificationStatus === 'IN_APPROVAL' ||
                formObj?.verificationStatus === 'EXPIRED'
              }
              permissionList={[
                {
                  code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
            {/* {user.currentRoleLabels && user.currentRoleLabels.includes('YP.QMS.PROQ_ENGINNER') && (
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="edit-o"
                onClick={() => {
                  setCanEdit(prev => !prev);
                }}
                disabled={formObj?.verificationStatus === 'IN_APPROVAL' || formObj?.verificationStatus === 'EXPIRED'}
                permissionList={[
                  {
                    code: `hzero.tarzan.hlct.jyzx.verification_library_management_platform.ps.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </PermissionButton>
            )} */}
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Collapse
            bordered={false}
            defaultActiveKey={['BASIC', 'DESC', 'problemReason', 'problemMeasure', 'ITEM']}
          >
            <Panel
              key="BASIC"
              header={intl.get(`${modelPrompt}.verification.information`).d("检证信息")}
              dataSet={formDs}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.BASIC`,
                },
                <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
                  <TextField name="verificationCode" disabled />
                  {/* <Lov name="inspectItemLov" required /> */}
                  <Lov
                    name="siteLov"
                    required
                    onChange={(val, oldValue) => handleSiteLov(val, oldValue)}
                  />
                  <Select name="verificationStatus" disabled />
                  <Select name="verificationFrom" required />
                  <Select name="verificationPeriod" required />
                  <Select name="verificationType" required />
                  <Select name="productType" />
                  <Select name="failureMode" required />
                  <Select name="applyArea" />
                  <Select name="applicableProductLine" />
                  <Select name="applicableChemicalSystem" />
                  <Select name="applicableProductStructure" />
                  <TextField name="startupDate" disabled />
                  <TextField name="expireDate" disabled />
                  {/* <TextField name="rejectBy" disabled /> */}
                  <TextField name="rejectReason" disabled />
                  <TextField name="reviewByName" disabled />
                  <TextField name="reviewDate" disabled />
                  {/* <TextField name="rejectTime" disabled /> */}
                  <TextField name="createdByName" disabled />
                  <TextField name="creationDate" disabled />
                </Form>,
              )}
            </Panel>
            <Panel
              key="DESC"
              header={intl.get(`${modelPrompt}.problem.description`).d("问题描述")}
              dataSet={formDs}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.BASIC`,
                },
                <Form dataSet={formDs} columns={3} labelWidth={112} disabled={!canEdit}>
                  <ProblemConponent name="tempProblemLov" dataSet={formDs} canEdit={canEdit} />
                  <Lov name="materialLov" />
                  <TextField name="materialName" disabled />
                  <Select name="itemGroupDesc" />
                  <Lov name="processFromLov" />
                  <Select name="responsibleArea" />
                  <TextArea name="problemDescription" />
                  <Attachment {...attachmentProps} />
                </Form>,
              )}
            </Panel>
            <Panel
              key="problemReason"
              header={intl.get(`${modelPrompt}.problem.reason`).d("问题原因")}
            >
              <Table
                buttons={[
                  <RenderDeleteButton dataSet={problemReasonDs} canEdit={canEdit} />,
                  [TableButtonType.add, { disabled: !canEdit, icon: 'add' }],
                ]}
                dataSet={problemReasonDs}
                columns={problemReasonColumns}
              />
            </Panel>
            <Panel
              key="problemMeasure"
              header={intl.get(`${modelPrompt}.problem.measure`).d("问题措施")}
            >
              <Table
                buttons={[
                  <RenderDeleteButton dataSet={measureDs} canEdit={canEdit} />,
                  [TableButtonType.add, { disabled: !canEdit, icon: 'add' }],
                ]}
                dataSet={measureDs}
                columns={measureColumns}
              />
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.verification.task`).d("检证任务" )}
              key="ITEM"
              dataSet={tableDs}
            >
              <InspectItemTab {...inspectItemTabProps} />
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [
    'tarzan.qms.verificationLibraryManagementPlatform',
    'tarzan.common',
    'hzero.common',
  ],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.BASIC`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.ATTR`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.RELATION`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.ITEM`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.ATTR1`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_GROUP_DETAIL.EDIT`,
    ],
    // @ts-ignore
  })(InspectGroupDetail),
);
