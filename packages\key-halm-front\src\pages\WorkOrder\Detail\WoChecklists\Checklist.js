/**
 * @since 2021-01-20
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */

import React, { PureComponent } from 'react';
import { <PERSON><PERSON>, Modal, Table, NumberField } from 'choerodon-ui/pro';
import { Popconfirm, Collapse, Spin, Icon } from 'choerodon-ui';
import { omit } from 'lodash';
import classNames from 'classnames';
import { Bind } from 'lodash-decorators';

import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { HALM_MTC } from 'alm/utils/config';
import request from 'utils/request';
import ChecklistEditModal from 'alm/components/ChecklistEditModal';
import { handleRecord } from 'alm/utils/checklistUtils';
import { saveOptions } from 'alm//components/ChecklistEditModal/api';

import UnFinishedModal from './UnFinishedModal';
import CheckFileUploadButton from '../FileUploadButton/check';
import styles from './checklist.module.less';

const organizationId = getCurrentOrganizationId();

const viewPrompt = 'amtc.woChecklists.view.message';

const saveUrl = `${HALM_MTC}/v1/${organizationId}/wo-checklists`;
const deleteUrl = `${HALM_MTC}/v1/${organizationId}/wo-checklists`;
const queryChildUrl = `${HALM_MTC}/v1/${organizationId}/wo-checklists/list/c7n-tree`;

export default class Checklist extends PureComponent {
  constructor(props) {
    super(props);
    const { unFinishedTableDs } = props;
    this.state = {
      editFlag: false,
      noData: true, // unFinishedTableDs 没有数据
    };

    unFinishedTableDs.handleChangeState = this.handleChangeState;

    this.editModalRef = React.createRef();
  }

  @Bind
  handleChangeState(records) {
    this.setState({
      ...records,
    });
  }

  @Bind()
  handleRefreshTables(type) {
    if (type === 'WO') {
      // 刷新工单检查项列表
      this.props.woTableDs.query();
    } else if (type === 'WOOP') {
      this.props.woopTableDs.query();
    }
    // 刷新异常检查项列表
    this.props.abTableDs.query();
    // 刷新未结检查项
    this.props.unFinishedTableDs.query();
  }

  //  ---------  检查项新增/编辑/查看  start -----------
  /**
   * 新增
   */
  @Bind()
  handleCreateWoChecklist() {
    const { parentId, parentName } = this.props;
    this.openEditModal(false, {
      parentTypeCode: 'WO', // 工单 还是 任务
      parentId,
      parentName,
    });
  }

  /**
   * 编辑
   */
  @Bind()
  handleEditLine(record) {
    this.openEditModal(true, record);
  }

  /**
   * 查看
   */
  @Bind()
  handleViewLine(record) {
    this.openEditModal(false, record);
  }

  // 打开 检查项编辑/查看modal
  @Bind
  openEditModal(editFlag = false, currentData = {}) {
    const { parentId, parentName, status, maintSiteId } = this.props;
    const isNew = !(typeof currentData.checklistId === 'number');

    // 控制部分字段在 新建 DRAFT APPROVED 下才可编辑
    const statusFlag = isNew || status === 'DRAFT' || status === 'APPROVED';
    const woChecklistEditProps = {
      isNew,
      editFlag,
      compParentId: parentId, // woId
      compParentName: parentName,
      compParentType: 'WO',
      statusFlag,
      tenantId: organizationId,
      dataSource: currentData,
      maintSiteIds: [maintSiteId],
    };

    this._editModal = Modal.open({
      key: 'editModal',
      destroyOnClose: true,
      closable: true,
      drawer: true,
      style: {
        width: 700,
      },
      title: intl.get(`${viewPrompt}.title.checkList`).d('检查项'),
      children: <ChecklistEditModal {...woChecklistEditProps} ref={this.editModalRef} />,
      footer: this.getFooter(isNew, editFlag),
    });
  }

  getFooter(isNew, editFlag) {
    return isNew || editFlag
      ? [
        <Button
          key="cancel"
          onClick={() => {
            this._editModal.close();
          }}
        >
          {intl.get('hzero.common.button.cancel').d('取消')}
        </Button>,
        <Button
          key="submit"
          color="primary"
          onClick={() => {
            this.handleSave();
          }}
        >
          {intl.get('hzero.common.button.save').d('保存')}
        </Button>,
      ]
      : [
        <Button
          key="back"
          onClick={() => {
            this._editModal.close();
          }}
        >
          {intl.get('hzero.common.button.back').d('返回')}
        </Button>,
      ];
  }

  /**
   * 保存检查项
   */
  @Bind()
  async handleSave() {
    const _ref = this.editModalRef?.current;
    if (_ref) {
      const result = await _ref.detailDs.current.validate(true);
      if (result) {
        const detail = _ref.detailDs.current.toData();
        const trueValue = _ref.trueNumberDs.toData();
        const alarmFlag = detail.isThereAlarm
        const { columnTypeCode, listValueCode } = detail;
        const isOk = columnTypeCode === 'LISTOFVALUE' ? true : await _ref?.optionsTableDs.validate();
        const optionsList = _ref?.optionsTableDs?.records?.map(i => i.toData());

        request(saveUrl, {
          method: 'POST',
          body: {
            tenantId: organizationId,
            ...omit(detail, 'parentName'),
            // ...omit(detail, 'standardReference'),
            standardReferenceList: trueValue,
            woId: this.props.woId,
          },
        }).then(async res => {
          if (res && !res.failed) {
            if (
              columnTypeCode === 'YESORNO' ||
              (columnTypeCode === 'LISTOFVALUE' && listValueCode) || alarmFlag !== null || alarmFlag !== undefined
            ) {
              if (isOk || alarmFlag !== null || alarmFlag !== undefined) {
                saveOptions({
                  list: alarmFlag && columnTypeCode === 'VALUE' ? [{ alarmFlag }] : optionsList,
                  checklistId: res.checklistId,
                  compParentType: 'WO',
                }).then(res1 => {
                  if (res1 && !res1.failed) {
                    notification.success();
                    // 刷新列表
                    this.props.woTableDs.query();
                    this.props.woopTableDs.query();
                    // 刷新未结检查项
                    this.props.unFinishedTableDs.query();
                    this._editModal.close();
                  } else {
                    notification.warning({
                      message: res1.message,
                    });
                  }
                });
              }
            } else {
              notification.success();
              // 刷新列表
              this.props.woTableDs.query();
              this.props.woopTableDs.query();
              // 刷新未结检查项
              this.props.unFinishedTableDs.query();
              this._editModal.close();
            }
          } else {
            notification.warning({
              message: res.message,
            });
          }
        });
      }
    }
  }

  //  ---------  检查项新增/编辑/查看  end -----------
  exportWoButton(record, type) {
    const { woId, woopId, staticButtonList } = this.props;
    const btns = [];

    const FileUploadButtonProps = {
      type: 'CHECK',
      showContentFlag: true,
      woId,
      moduleId: woopId,
      parentTypeCode: 'WO',
    };
    btns.push(
      <CheckFileUploadButton {...FileUploadButtonProps} />,
    );

    if (staticButtonList?.includes('checklistEditAndSort')) {
      btns.push(
        <a onClick={() => this.handleEditLine(record)}>
          {intl.get('hzero.common.button.edit').d('编辑')}
        </a>,
      );
    }

    if (staticButtonList?.includes('viewChecklist')) {
      btns.push(
        <a onClick={() => this.handleViewLine(record, true)}>
          {intl.get('hzero.common.button.view').d('查看')}
        </a>,
      );
    }

    if (record.columnTypeCode !== 'INDEX' && staticButtonList?.includes('woRecordChecklist')) {
      btns.push(
        <a onClick={() => this.handleActualValueModal(record)}>
          {intl.get(`${viewPrompt}.button.record`).d('记录')}
        </a>,
      );
    }

    if (staticButtonList?.includes('deleteChecklist')) {
      btns.push(
        <Popconfirm
          title={intl.get(`${viewPrompt}.confirmDelete`).d('是否删除该记录?')}
          placement="topRight"
          onConfirm={() => this.handleDeleteLine(record, type)}
          okText={intl.get('hzero.common.status.yes').d('是')}
          cancelText={intl.get('hzero.common.status.no').d('否')}
        >
          <a>{intl.get('hzero.common.button.delete').d('删除')}</a>
        </Popconfirm>,
      );
    }
    return <span className="action-link">{btns}</span>;
  }

  /**
   * 任务检查项表格-操作列
   */
  @Bind()
  exportWoopButton(record, type) {
    const { staticButtonList } = this.props;
    const { woId, woopId, staticButtonList: woopStaticBtnList } = record;
    const btns = [];

    const FileUploadButtonProps = {
      type: 'CHECK',
      showContentFlag: true,
      woId,
      moduleId: woopId,
      parentTypeCode: 'WO',
    };
    btns.push(
      <CheckFileUploadButton {...FileUploadButtonProps} />,
    );

    if (staticButtonList?.includes('checklistEditAndSort')) {
      btns.push(
        <a onClick={() => this.handleEditLine(record)}>
          {intl.get('hzero.common.button.edit').d('编辑')}
        </a>,
      );
    }

    if (staticButtonList?.includes('viewChecklist')) {
      btns.push(
        <a onClick={() => this.handleViewLine(record, true)}>
          {intl.get('hzero.common.button.view').d('查看')}
        </a>,
      );
    }

    if (record.columnTypeCode !== 'INDEX' && woopStaticBtnList?.includes('recordChecklist')) {
      btns.push(
        <a onClick={() => this.handleActualValueModal(record)}>
          {intl.get(`${viewPrompt}.button.record`).d('记录')}
        </a>,
      );
    }

    if (woopStaticBtnList?.includes('editTaskAndChecklist')) {
      btns.push(
        <Popconfirm
          title={intl.get(`${viewPrompt}.confirmDelete`).d('是否删除该记录?')}
          placement="topRight"
          onConfirm={() => this.handleDeleteLine(record, type)}
          okText={intl.get('hzero.common.status.yes').d('是')}
          cancelText={intl.get('hzero.common.status.no').d('否')}
        >
          <a>{intl.get('hzero.common.button.delete').d('删除')}</a>
        </Popconfirm>,
      );
    }
    return <span className="action-link">{btns}</span>;
  }

  /**
   * 删除
   */
  @Bind()
  handleDeleteLine(record, type) {
    request(deleteUrl, {
      method: 'DELETE',
      body: record,
    }).then(res => {
      if (res && res.failed) {
        notification.warning({ message: res.message });
      } else {
        notification.success();
        // 刷新列表
        this.handleRefreshTables(type);
      }
    });
  }

  // ------------ 记录实际值modal start ----------------
  /**
   * 打开记录实际值model
   */
  @Bind()
  handleActualValueModal(record) {
    handleRecord(record, {
      sourceType: 'WO',
      recordOkCallback: this.handleRecordOk,
    });
  }

  // 将记录检查项实际值 及 检查项创建仪表点拆分为两步了 先记录再创建 所以无论创建仪表点成功与否 都需要刷新数据
  @Bind()
  handleRecordOk(record, res) {
    const type = record.parentTypeCode; // WO 或 WOOP
    this.handleRefreshTables(type);
    if (res && res.failed) {
      notification.warning({ message: res.message });
      return Promise.reject();
    }
    notification.success();

  }
  // ----------- 记录实际值modal end -------------

  // -----------显示未完结检查项 start ------
  @Bind
  showChecklist() {
    const { unFinishedTableDs, status, staticButtonList } = this.props;
    const unFinishedModalProps = {
      status,
      unFinishedTableDs,
      staticButtonList,
      onActualValueModal: this.handleActualValueModal,
    };
    this._unFinishedModal = Modal.open({
      key: 'unFinishedModal',
      destroyOnClose: true,
      maskClosable: true,
      closable: true,
      drawer: true,
      style: {
        width: 600,
      },
      title: intl.get(`${viewPrompt}.title.unFinishedChecklist`).d('未结检查项'),
      children: <UnFinishedModal {...unFinishedModalProps} ref={this.unFinishedModalRef} />,
      footer: () => {
        return [
          <Button
            key="close"
            onClick={() => {
              this._unFinishedModal.close();
            }}
          >
            {intl.get('hzero.common.button.close').d('关闭')}
          </Button>,
        ];
      },
    });
  }
  // -----------显示未完结检查项 end ------

  @Bind
  handleExpandWoChecklist(expanded, record) {
    this.handleExpand(expanded, record, 'WO');
  }

  @Bind
  handleExpandWoopChecklist(expanded, record) {
    this.handleExpand(expanded, record, 'WOOP');
  }

  // 点击展开
  @Bind
  async handleExpand(expanded, record, type) {
    // 判断节点是否异步加载子结点
    if (expanded && record.get('childFlag') && !record.children) {
      record.setState('loadding', true);
      request(queryChildUrl, {
        method: 'GET',
        query: {
          parentChecklistId: record.get('checklistId'),
        },
      }).then(res => {
        const recordsChildren = res.content;

        if (type === 'WO') {
          this.props.woTableDs.data = [...this.props.woTableDs.toData(), ...recordsChildren];
        } else if (type === 'WOOP') {
          this.props.woopTableDs.data = [...this.props.woopTableDs.toData(), ...recordsChildren];
        }
        record.setState('loadding', false);
      });
    }
  }

  // icon 渲染问题， 首先判断record的值和自定义状态来判断出叶节点和父节点进行不同的渲染
  expandicon({ prefixCls, expanded, expandable, record, onExpand }) {
    if (!record.get('childFlag')) {
      // 子结点渲染
      return <span style={{ paddingLeft: '0.18rem' }} />;
    }
    if (record.getState('loadding') === true) {
      // 自定义状态渲染
      return <Spin tip="loding" delay={200} size="small" />;
    }
    const iconPrefixCls = `${prefixCls}-expand-icon`;
    const classString = classNames(iconPrefixCls, {
      [`${iconPrefixCls}-expanded`]: expanded,
    });
    return (
      <Icon
        type="baseline-arrow_right"
        className={classString}
        onClick={onExpand}
        tabIndex={expandable ? 0 : -1}
      />
    );
  }

  get columns() {
    const { status } = this.props;
    const basicColumns = [
      {
        name: 'businessScenarioMeaning',
        width: 150,
      },
      {
        name: 'checklistName',
      },
      {
        name: 'columnTypeMeaning',
        width: 150,
      },
    ];
    const othersColumns = [
      {
        name: 'actValue',
        width: 150,
        renderer: ({ text, record }) => this.renderActValue(text, record.toData()),
      },
      {
        name: 'description',
      },
    ];
    return status === 'DRAFT' ? basicColumns : basicColumns.concat(othersColumns);
  }

  // 字段 actValue 的渲染
  renderActValue(value, record) {
    let returnVal = '';
    switch (record.columnTypeCode) {
      case 'YESORNO':
      case 'LISTOFVALUE':
        returnVal = record.actValueMeaning;
        break;
      default:
        returnVal = value;
        break;
    }
    return returnVal;
  }

  @Bind
  handleSearchByBtn() {
    this.props.woopTableDs.query();
  }

  @Bind
  handleKeyDown(e) {
    if (e.keyCode === 13) {
      this.handleSearchByBtn();
    }
  }

  render() {
    const { noData } = this.state;
    // viewFlag: 计划调度仅查看，不显示别的按钮; disableOptionFlag禁止任何操作，仅显示顶层界面查看
    const {
      // status,
      isEndFlag,
      woTableDs,
      woopTableDs,
      woopSearchDs,
      viewFlag,
      disableOptionFlag,
      staticButtonList = [],
    } = this.props;

    const woColumns = [
      ...this.columns,
      {
        header: intl.get('hzero.common.button.action').d('操作'),
        name: 'action',
        width: 200,
        lock: 'right',
        align: 'center',
        renderer: ({ record }) => this.exportWoButton(record.toData(), 'WO'),
      },
    ];
    const woopColumns = [
      {
        name: 'woopNum',
        width: 80,
      },
      ...this.columns,
      {
        header: intl.get('hzero.common.button.action').d('操作'),
        name: 'action',
        width: 200,
        lock: 'right',
        align: 'center',
        renderer: ({ record }) => this.exportWoopButton(record.toData(), 'WOOP'),
      },
    ];

    // 审批表单显示时，不显示操作列
    if (disableOptionFlag) {
      woColumns.pop();
      woopColumns.pop();
    }

    return (
      <React.Fragment>
        <Collapse
          bordered={false}
          defaultActiveKey={['A', 'B']}
          className={styles['checklist-tables']}
        >
          <Collapse.Panel
            key="A"
            header={intl.get(`${viewPrompt}.panel.woChecklist`).d('工单检查项')}
          >
            {!viewFlag && (
              <div style={{ textAlign: 'right', marginBottom: 8 }}>
                <Button icon="show" onClick={this.showChecklist} disabled={noData}>
                  {intl.get(`${viewPrompt}.button.showChecklist`).d('显示未结检查项')}
                </Button>
                {staticButtonList.includes('newChecklist') ? (
                  <Button icon="add" onClick={this.handleCreateWoChecklist} disabled={isEndFlag}>
                    {intl.get(`hzero.common.button.add`).d('新增')}
                  </Button>
                ) : null}
              </div>
            )}

            <Table
              key="woCheckList"
              customizedCode="AORI.WORK_ORDER.WO_CHECK_LIST"
              dataSet={woTableDs}
              columns={woColumns}
              mode="tree"
              className="halm-tree-table"
              expandIcon={this.expandicon}
              onExpand={this.handleExpandWoChecklist}
              expandedRowRenderer={() => false}
              expandIconColumnIndex={1}
            />
          </Collapse.Panel>
          <Collapse.Panel
            key="B"
            header={intl.get(`${viewPrompt}.panel.woopChecklist`).d('任务检查项')}
          >
            <div className={styles['content-search']}>
              {intl.get(`${viewPrompt}.button.searchByWoopNum`).d('查询任务号')}：
              <NumberField
                dataSet={woopSearchDs}
                name="woopNum"
                clearButton
                style={{ width: 150 }}
                onKeyDown={this.handleKeyDown}
              />
              <Button
                style={{
                  backgroundColor: '#3889FF',
                  border: '1px solid #3889FF',
                  color: '#fff',
                  fontSize: 12,
                  borderRadius: '0px 4px 4px 0px',
                }}
                onClick={this.handleSearchByBtn}
                key="create"
              >
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
            </div>
            <Table
              key="woWoopChecklist"
              customizedCode="AORI.WORK_ORDER.WOOP_CHECK_LIST"
              dataSet={woopTableDs}
              columns={woopColumns}
              mode="tree"
              className="halm-tree-table"
              expandIcon={this.expandicon}
              onExpand={this.handleExpandWoopChecklist}
              expandedRowRenderer={() => false}
              expandIconColumnIndex={2}
            />
          </Collapse.Panel>
        </Collapse>
      </React.Fragment>
    );
  }
}
