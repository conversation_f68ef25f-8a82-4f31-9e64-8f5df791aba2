import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.qms.MeasuringTransferPlatform';
const tenantId = getCurrentOrganizationId();

// const endUrl = '-138685';
const endUrl = '';

const lineTableFactory = () =>
  new DataSet({
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    paging: false,
    fields: [
      {
        name: 'toolCodeObjs',
        label: intl.get(`${modelPrompt}.table.toolCodeObjs`).d('量具编号'),
        type: FieldType.object,
        ignore: FieldIgnore.always,
        multiple: true,
        lovCode: 'YP.QIS.MS_TOOL_RESPON_AND_STATUS_LIMIT_LOV',
        dynamicProps: {
          lovPara: ({ dataSet }) => ({
            siteId: dataSet.getState('siteId'),
            tenantId: getCurrentOrganizationId(),
          }),
        },
      },
      {
        name: 'toolCodeObj',
        label: intl.get(`${modelPrompt}.table.toolCodeObj`).d('量具编号'),
        type: FieldType.object,
        ignore: FieldIgnore.always,
        required: true,
        lovCode: 'YP.QIS.MS_TOOL_RESPON_AND_STATUS_LIMIT_LOV',
        dynamicProps: {
          lovPara: ({ dataSet }) => ({
            siteId: dataSet.getState('siteId'),
            tenantId: getCurrentOrganizationId(),
          }),
        },
      },
      {
        name: 'msToolManageId',
        bind: 'toolCodeObj.msToolManageId',
      },
      {
        name: 'msToolManageCode',
        bind: 'toolCodeObj.toolCode',
      },
      {
        name: 'speciesName',
        label: intl.get(`${modelPrompt}.table.speciesName`).d('种别描述'),
        type: FieldType.string,
        bind: 'toolCodeObj.speciesName',
      },
      {
        name: 'modelCode',
        label: intl.get(`${modelPrompt}.table.modelCode`).d('型号编码'),
        type: FieldType.string,
        bind: 'toolCodeObj.modelCode',
      },
      {
        name: 'modelName',
        label: intl.get(`${modelPrompt}.table.modelName`).d('型号描述'),
        type: FieldType.string,
        bind: 'toolCodeObj.modelName',
      },
      {
        name: 'lastVerificationDate',
        label: intl.get(`${modelPrompt}.table.lastVerificationDate`).d('上次检定日期'),
        type: FieldType.string,
        bind: 'toolCodeObj.lastVerificationDate',
      },
      {
        name: 'usingStatus',
        label: intl.get(`${modelPrompt}.table.usingStatus`).d('量具使用状态'),
        lookupCode: 'QIS.MS_TOOL_USING_STATUS',
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
        type: FieldType.string,
        bind: 'toolCodeObj.usingStatus',
      },
      {
        name: 'verificationStatus',
        lookupCode: 'QIS.MS_TOOL_VRFCT_STATUS',
        label: intl.get(`${modelPrompt}.table.verificationStatus`).d('检定状态'),
        type: FieldType.string,
        bind: 'toolCodeObj.verificationStatus',
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
      },
      {
        name: 'currentUserId',
        label: intl.get(`${modelPrompt}.table.currentUserName`).d('使用人'),
        bind: 'toolCodeObj.userId',
      },
      {
        name: 'currentUserName',
        bind: 'toolCodeObj.userName',
      },
      {
        name: 'currentUseDepartmentName',
        label: intl.get(`${modelPrompt}.table.currentUseDepartmentName`).d('使用部门'),
        type: FieldType.string,
        bind: 'toolCodeObj.usingDepartmentName',
      },
      {
        name: 'currentUseDepartmentId',
        type: FieldType.string,
        bind: 'toolCodeObj.usingDepartmentId',
      },
      {
        name: 'currentProdlineName',
        label: intl.get(`${modelPrompt}.table.currentProdlineName`).d('产线'),
        type: FieldType.string,
        bind: 'toolCodeObj.prodLineName',
      },
      {
        name: 'currentProdlineId',
        bind: 'toolCodeObj.prodLineId',
      },
      {
        name: 'currentProcessName',
        label: intl.get(`${modelPrompt}.table.currentProcessName`).d('工序'),
        type: FieldType.string,
        bind: 'toolCodeObj.processName',
      },
      {
        name: 'currentProcessId',
        bind: 'toolCodeObj.processId',
      },
      {
        name: 'currentOtherPosition',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.currentOtherPosition`).d('其他位置信息'),
        lookupCode: 'YP.QIS.MANAGE_OTHER_POSITION',
        bind: 'toolCodeObj.otherPosition',
        lovPara: { tenantId },
      },
      {
        name: 'targetRespon',
        label: intl.get(`${modelPrompt}.table.targetRespon`).d('目标责任人'),
        lovCode: 'HIAM.USER.ORG',
        type: FieldType.object,
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
        ignore: FieldIgnore.always,
        textField: 'realName',
      },
      {
        name: 'targetResponId',
        bind: 'targetRespon.id',
      },
      {
        name: 'targetResponIdName',
        bind: 'targetRespon.realName',
      },
      {
        name: 'targetUser',
        label: intl.get(`${modelPrompt}.table.targetUser`).d('目标使用人'),
        lovCode: 'LOV_EMPLOYEE',
        multiple: ',',
        type: FieldType.object,
        lovPara:{
          tenantId: getCurrentOrganizationId(),
        },
        ignore: FieldIgnore.always,
        textField: 'name',
        dynamicProps: {
          required: ({ record }) => record.get('targetUseDepartmentId'),
        },
      },
      {
        name: 'targetUserId',
        multiple: ',',
        bind: 'targetUser.employeeId',
      },
      {
        name: 'targetUserName',
        multiple: ',',
        bind: 'targetUser.name',
      },
      {
        name: 'targetUseDepartment',
        label: intl.get(`${modelPrompt}.table.targetUseDepartment`).d('目标使用部门'),
        lovCode: 'YP.QIS.COMPANY_UNIT',
        type: FieldType.object,
        ignore: FieldIgnore.always,
        textField: 'unitName',
        dynamicProps: {
          lovPara: ({ dataSet }) => ({
            siteId: dataSet.getState('siteId'),
            tenantId: getCurrentOrganizationId(),
          }),
          required: ({ record }) => {
            return record.get('targetUserId').length
          },
        },
      },
      {
        name: 'targetUseDepartmentId',
        bind: 'targetUseDepartment.unitId',
      },
      {
        name: 'targetUseDepartmentIdName',
        bind: 'targetUseDepartment.unitName',
      },
      {
        name: 'targetSite',
        label: intl.get(`${modelPrompt}.table.targetSite`).d('目标工厂'),
        lovCode: 'MT.APS.PLANT',
        type: FieldType.object,
        ignore: FieldIgnore.always,
        textField: 'siteName',
        dynamicProps: {
          lovPara: ({ dataSet }) => ({
            siteId: dataSet.getState('siteId'),
            tenantId: getCurrentOrganizationId(),
          }),
        },
      },
      {
        name: 'targetSiteId',
        bind: 'targetSite.siteId',
      },
      {
        name: 'targetSiteIdName',
        bind: 'targetSite.siteName',
      },
      {
        name: 'targetProdline',
        label: intl.get(`${modelPrompt}.table.targetProdline`).d('目标产线'),
        lovCode: 'MT.MODEL.PRODLINE',
        type: FieldType.object,
        ignore: FieldIgnore.always,
        textField: 'prodLineName',
        valueField: 'prodLineId',
        lovPara: {
          tenantId: getCurrentOrganizationId(),
        },
        dynamicProps: {
          required: ({ record }) => record?.get('targetProcessId'),
          disabled: ({ record }) => record?.get('targetOtherPosition'),
        },
      },
      {
        name: 'targetProdlineId',
        bind: 'targetProdline.prodLineId',
      },
      {
        name: 'targetProdlineIdName',
        bind: 'targetProdline.prodLineName',
      },
      {
        name: 'targetProcess',
        label: intl.get(`${modelPrompt}.table.targetProcess`).d('目标工序'),
        lovCode: 'MT.MODEL.WORKCELL',
        type: FieldType.object,
        ignore: FieldIgnore.always,
        textField: 'workcellName',
        lovPara: {
          tenantId: getCurrentOrganizationId(),
        },
        dynamicProps: {
          required: ({ record }) => record?.get('targetProdlineId'),
          disabled: ({ record }) => record?.get('targetOtherPosition'),
        },
      },
      {
        name: 'targetProcessId',
        bind: 'targetProcess.workcellId',
      },
      {
        name: 'targetProcessIdName',
        bind: 'targetProcess.workcellName',
      },
      {
        name: 'targetOtherPosition',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.targetOtherPosition`).d('目标其他位置信息'),
        lookupCode: 'YP.QIS.MANAGE_OTHER_POSITION',
        lovPara: { tenantId },
        dynamicProps: {
          disabled: ({ record }) => record?.get('targetProdlineId') || record?.get('targetProcessId'),
        },
      },
    ],
    transport: {
      destroy: ({ data }) => {
        return {
          data,
          url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${getCurrentOrganizationId()}/qis-ms-tool-change-line/remove/ui`,
          method: 'DELETE',
        };
      },
    },
  });

export default lineTableFactory;
