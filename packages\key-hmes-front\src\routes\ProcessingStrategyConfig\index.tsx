// 加工策略配置
import React, { useMemo, useCallback } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Select, Button } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import { HandleSave, HandleDelete } from './services';
import { tableDS } from './stores';

const modelPrompt = 'tarzan.ProcessingStrategyConfig';

const ProcessingStrategyConfig = observer(props => {
  const { tableDs } = props;
  // 保存
  const { run: fetchSave } = useRequest(HandleSave(), {
    manual: true,
    needPromise: true,
  }); // 手动查询，需要promise

  // 删除
  const { run: fetchDelete, loading: Loading } = useRequest(HandleDelete(), {
    manual: true,
    needPromise: true,
  }); // 手动查询，需要promise

  // 编辑
  const handleEdit = record => {
    if (tableDs.toData().filter(v => v._status === 'create').length > 0) {
      return notification.warning({ message: '请先保存头信息' });
    }
    if (tableDs.records.filter(v => v.getState('editing')).length > 0) {
      return notification.warning({ message: '只能同时编辑一条' });
    }
    record.setState('editing', true);
  };

  // 保存
  const handleSave = useCallback(async record => {
    const validate = await record.validate();
    if (validate) {
      const saveData = record.toData();
      fetchSave({
        params: [saveData],
      }).then(res => {
        if (res && !res.failed) {
          tableDs.query(tableDs.currentPage);
        }
      });
    }
  }, []);

  // 新增行
  const handleAdd = () => {
    if (tableDs.toData().filter(v => v._status === 'create').length > 0) {
      return notification.warning({ message: '请先保存头信息' });
    }
    if (tableDs.records.filter(v => v.getState('editing')).length > 0) {
      return notification.warning({ message: '编辑时不可新建' });
    }
    tableDs.create(
      {
        _status: 'create',
      },
      0,
    );
  };

  // 取消保存
  const handleCancel = record => {
    if (record) {
      if (record.status === 'add') {
        tableDs.remove(record);
      } else {
        record.reset();
        record.setState('editing', false);
      }
    } else {
      tableDs.reset();
      tableDs.records.forEach(item => {
        item.setState('editing', false);
      });
    }
  };

  // 删除
  const handleDelete = useCallback(() => {
    if (tableDs.selected.length === 0) {
      return;
    }
    const data = tableDs.selected.map(item => {
      return {
        ...item.data,
        inType: null,
        outType: null,
        signalSource: null,
      };
    });
    fetchDelete({
      params: data,
    }).then(res => {
      if (res && !res.failed) {
        tableDs.query();
      }
    });
  }, []);

  // 切换出站策略
  const changeOuttype = (value, record) => {
    if (value) {
      record.set('signalSource', null);
    }
    if (record.get('inType') === 'INSPECT' && (value === 'LINKED_OUT' || value === 'MANUAL_OUT')) {
      record.set('outType', null);
      return notification.warning({
        message: '当进站策略为查看时，出站策略仅可以选择手动进出站与联动进出站',
      });
    }
    if (record.get('inType') === 'IN' && (value === 'LINKED_CROSS' || value === 'MANUAL_CROSS')) {
      record.set('outType', null);
      return notification.warning({
        message: '当进站策略为进站时，出站策略仅可以选择手动出站与联动出站',
      });
    }
  };

  // 切换进站策略
  const changeIntype = (value, record) => {
    if (value) {
      record.set('outType', null);
      record.set('signalSource', null);
    }
    if (value === 'CROSS') {
      record.set('outType', null);
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'operation',
        align: ColumnAlign.center,
        editor: record => record.get('_status') === 'create',
      },
      {
        name: 'inType',
        align: ColumnAlign.center,
        editor: record => {
          if (record.get('_status') === 'create' || record.getState('editing')) {
            return <Select onChange={value => changeIntype(value, record)}></Select>;
          }
        },
      },
      {
        name: 'outType',
        align: ColumnAlign.center,
        editor: record => {
          if (record.get('_status') === 'create' || record.getState('editing')) {
            return <Select onChange={value => changeOuttype(value, record)}></Select>;
          }
        },
      },
      {
        name: 'signalSource',
        align: ColumnAlign.center,
        editor: record => record.get('_status') === 'create' || record.getState('editing'),
      },
      {
        name: 'containerLoadType',
        align: ColumnAlign.center,
        editor: record => record.get('_status') === 'create' || record.getState('editing'),
      },
      {
        name: 'containerLoadConfig',
        align: ColumnAlign.center,
        editor: record => record.get('_status') === 'create' || record.getState('editing'),
      },
      {
        header: intl.get('tarzan.aps.common.button.action').d('操作'),
        width: 150,
        lock: ColumnLock.right,
        align: ColumnAlign.center,
        renderer: ({ record }) =>
          record?.getState('editing') || record?.get('_status') === 'create' ? (
            <>
              <a onClick={() => handleCancel(record)} style={{ marginRight: '0.1rem' }}>
                {intl.get(`tarzan.aps.common.button.cancel`).d('取消')}
              </a>
              <a onClick={() => handleSave(record)}>
                {intl.get(`tarzan.aps.common.button.save`).d('保存')}
              </a>
            </>
          ) : (
            <a onClick={() => handleEdit(record)}>
              {intl.get(`tarzan.aps.common.button.edit`).d('编辑')}
            </a>
          ),
      },
    ];
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('加工策略配置')}>
        <Button color={ButtonColor.primary} onClick={handleAdd}>
          {intl.get('hzero.common.button.add').d('新建')}
        </Button>
        <Button
          color={ButtonColor.primary}
          disabled={tableDs.selected.length === 0}
          onClick={handleDelete}
          loading={Loading}
        >
          {intl.get('hzero.common.button.delete').d('删除')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            queryFieldsLimit: 2,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="ProcessingStrategyConfig"
          customizedCode="ProcessingStrategyConfig"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ProcessingStrategyConfig),
);
