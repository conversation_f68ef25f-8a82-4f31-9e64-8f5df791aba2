/**
 * @Description: 组装拆卸
 */
import intl from 'utils/intl';
import {DataSetProps} from 'choerodon-ui/pro/lib/data-set/DataSet';
import {DataSetSelection, FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import {BASIC} from '@/utils/config';
import {getCurrentOrganizationId} from 'utils/utils';

const modelPrompt = 'tarzan.AssemblyAndDisassembly';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'materialLotId',
  queryFields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('条码号'),
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号代码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
  ],
  fields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('条码号'),
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号代码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locator`).d('库位'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'enableFlagMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'qualityStatusMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-disassemble/list/ui`,
        method: 'GET',
      };
    },
  },
});

const lineDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  forceValidate: true,
  fields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'HME.MATERIAL_INFO_LOV',
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            enableFlag: 'Y',
          };
        },
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialLov.materialName',
    },
    {
      name: 'bomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomCode`).d('BOM号'),
      bind: 'materialLov.bomCode',
    },
    {
      name: 'modelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.modelCode`).d('型号代码'),
      bind: 'materialLov.modelCode',
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
      required: true,
      min: 0,
      dynamicProps: {
        max: ({record, dataSet }) => {
          const otherLine = dataSet.records
            .filter((ele) => ele.get('materialId') !== record?.get('materialId'));
          const otherLineQty = otherLine.length > 0 ? otherLine
            .map((i) => Number(i.get('qty')))
            .reduce((prev,curr)=> prev + curr) : 0;
          return Number(record.get('selectedAllNum')) - otherLineQty;
        },
      },
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('条码号'),
    },
  ],
});

export { entranceDS, lineDS };
