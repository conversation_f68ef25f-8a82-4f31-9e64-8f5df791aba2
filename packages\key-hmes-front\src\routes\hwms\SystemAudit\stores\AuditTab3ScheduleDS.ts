/**
 * @Description: 体系审核管理维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2023-07-20 11:13:24
 * @LastEditTime: 2023-07-20 17:08:53
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSet } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.systemAudit';
const tenantId = getCurrentOrganizationId();

// 详情-审核日程表单
const auditScheduleFormDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewPlanCode`).d('计划编号'),
      name: 'sysReviewPlanCode',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sysReviewType`).d('审核类型'),
      name: 'sysReviewType',
      lookupCode: 'YP.QIS.SYS_REVIEW_TYPE',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.planCreater`).d('计划创建人'),
      name: 'planCreaterLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      bind: 'planCreaterLov.id',
    },
    {
      name: 'createdByName',
      bind: 'planCreaterLov.realName',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.scheduledStartDateTime`).d('计划开始时间'),
      name: 'scheduledStartDateTime',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.scheduledEndDateTime`).d('计划开始时间'),
      name: 'scheduledEndDateTime',
    },
  ],
});
// 详情-审核日程列表
const auditScheduleTableDS = (): DataSetProps => ({
  forceValidate: true,
  paging: false,
  selection: false,
  fields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scheduleCode`).d('日程编号'),
      name: 'scheduleCode',
      disabled: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.group`).d('组别'),
      name: 'groupObject',
      textField: 'groupDescription',
      valueField: 'groupId',
      required: true,
      dynamicProps: {
        options: ({ dataSet }) => {
          const groupList = dataSet.getState('groupList');
          return new DataSet({
            data: groupList,
            fields: [
              {
                name: 'groupDescription',
              },
              {
                name: 'groupId',
              },
            ],
          });
        },
      },
    },
    {
      name: 'groupId',
      disabled: true,
      bind: 'groupObject.groupId',
    },
    {
      name: 'groupDescription',
      disabled: true,
      bind: 'groupObject.groupDescription',
    },
    {
      type: FieldType.object,
      name: 'groupUserId',
      disabled: true,
      bind: 'groupObject.groupUserId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.groupUser`).d('小组成员'),
      name: 'memberInfo',
      disabled: true,
      bind: 'groupObject.groupUserName',
    },
    {
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.date`).d('日期'),
      name: 'scheduleDate',
      format: 'YYYY-MM-DD',
      required: true,
    },

    {
      type: FieldType.time,
      label: intl.get(`${modelPrompt}.time`).d('时间'),
      name: 'scheduleTime',
      range: ['scheduleTimeFrom', 'scheduleTimeTo'],
      format: 'HH:mm:ss',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.revDapartment`).d('受审部门'),
      name: 'revDapartmentLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'beRevDepartmentId',
      bind: 'revDapartmentLov.unitId',
    },
    {
      name: 'beRevDepartmentName',
      bind: 'revDapartmentLov.unitName',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.revDapartmentPer`).d('受审部门对接人'),
      name: 'revDapartmentPerLov',
      ignore: FieldIgnore.always,
      multiple:true,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'beRevDeptPer',
      bind: 'revDapartmentPerLov.id',
    },
    {
      name: 'beRevDeptPerName',
      bind: 'revDapartmentPerLov.realName',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.beRevContent`).d('受审内容'),
      name: 'beRevContent',
      required: true,
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reviewStandard`).d('审核依据'),
      name: 'sysReviewStandardLov',
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.SYS_REVIEW_STANDARD',
      lovPara: {
        tenantId,
      },
      multiple: true,
      computedProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteId: dataSet.getState('siteId'),
            sysReviewPlanId: dataSet.getState('sysReviewPlanId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('groupId');
        },
      },
    },
    {
      name: 'sysReviewStandardIdList',
      bind: 'sysReviewStandardLov.uniqueCode',
    },
    {
      name: 'sysReviewStandardNameList',
      bind: 'sysReviewStandardLov.fileName',
    },
  ],
});

export { auditScheduleFormDS, auditScheduleTableDS };
