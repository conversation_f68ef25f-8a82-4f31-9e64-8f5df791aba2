/*
 * @Description: 质量反馈单-详情页DS
 * @Author: <<EMAIL>>
 * @Date: 2023-09-13 14:38:41
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-12 14:28:47
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import moment from 'moment/moment';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.hwms.qualityFeedbackPlatform';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();
const endUrl = "";

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    // 来源信息部分
    {
      name: 'feedbackNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.feedbackNum`).d('质量反馈单编号'),
      required: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
      textField: 'siteName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'theme',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.theme`).d('主题'),
    },
    {
      name: 'warnNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warnNum`).d('告警单编号'),
    },
    {
      name: 'serviceNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceNum`).d('维修工单编号'),
    },
    {
      name: 'claimNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.claimNum`).d('质保索赔单编号'),
    },
    {
      name: 'dataSource',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dataSource`).d('数据来源'),
    },

    // 基本信息部分
    {
      name: 'batteryNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryNum`).d('电池包编码'),
      required: true,
    },
    {
      name: 'batteryModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batteryModel`).d('电池包型号'),
      required: true,
    },
    {
      name: 'hostPlantLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.hostPlant`).d('主机厂'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'hostPlant',
      bind: 'hostPlantLov.customerId',
    },
    {
      name: 'hostPlantCode',
      bind: 'hostPlantLov.customerCode',
    },
    {
      name: 'hostPlantName',
      bind: 'hostPlantLov.customerName',
    },
    {
      name: 'vinNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vinNum`).d('VIN号'),
      required: true,
    },
    {
      name: 'vehicleModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vehicleModel`).d('车型'),
      lookupCode: 'YP.QIS.VEHICAL_MODEL',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'productionDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionDateFrom`).d('车辆生产时间'),
    },
    {
      name: 'soldDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.soldDate`).d('销售时间'),
    },
    {
      name: 'faultDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.faultDate`).d('故障时间'),
    },
    {
      name: 'maintainDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.maintainDate`).d('维修时间'),
      required: true,
    },
    {
      name: 'faultMileage',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.faultMileage`).d('故障里程'),
      required: true,
    },
    {
      name: 'serviceSitusNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceSitusNum`).d('维修网点编号'),
      required: true,
    },
    {
      name: 'serviceSitusName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serviceSitusName`).d('维修网点名称'),
      required: true,
    },
    {
      name: 'shopNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shopNum`).d('4S店编号'),
      required: true,
    },
    {
      name: 'shopName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shopName`).d('4S店名称'),
      required: true,
    },
    {
      name: 'faultDec',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultDec`).d('故障描述'),
      required: true,
    },
    {
      name: 'faultReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultReason`).d('故障原因'),
      required: true,
    },
    {
      name: 'faultMeasure',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultMeasure`).d('维修措施'),
      required: true,
    },
    {
      name: 'faultLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.faultLevel`).d('故障等级'),
      lovPara: { tenantId },
      lookupCode: 'YP.QIS.QA_FEEDBACK_FAULT_LEVEL',
      required: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('记录时间'),
      defaultValue: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      disabled: true,
    },
    {
      name: 'createdLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
      disabled: true,
      defaultValue: {
        id: userInfo.id,
        realName: userInfo.realName,
      },
    },
    {
      name: 'createdBy',
      bind: 'createdLov.id',
    },
    {
      name: 'createdByName',
      bind: 'createdLov.realName',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-qa-feedbacks/query`,
        method: 'GET',
        transformResponse: val => {
          const data = JSON.parse(val);
          if (!data?.content?.length) {
            notification.error({
              message: intl.get(`${modelPrompt}.error`).d('操作失败'),
            });
          }
          return {
            rows: { ...data.content[0] },
          };
        },
      };
    },
  },
});

const detailLineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: undefined, // 列表数据在接口返回json中的相对路径
  // totalKey: 'rows.totalElements',
  // primaryKey: 'feedbackItemId', // 表格唯一性主键
  fields: [
    {
      name: 'feedbackItemNum',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.feedbackItemNum`).d('序号'),
      disabled: true,
    },
    {
      name: 'partsCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partsCode`).d('零部件编号'),
    },
    {
      name: 'partsName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partsName`).d('零部件名称'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('因湃零部件编号'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      computedProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.getState('siteId'),
        }),
        disabled: ({ dataSet }) =>
          !dataSet.getState('siteId'),
      },
    },
    {
      name: 'ypPartsId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('因湃零部件名称'),
      bind: 'materialLov.materialName',
    },
    {
      name: 'primaryUnitFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.primaryUnitFlag`).d('主因件'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('数量'),
      required: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-qa-feedbacks/detail`,
        method: 'GET',
      };
    },
  },
});

const maintainItemDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  dataKey: 'rows.content', // 列表数据在接口返回json中的相对路径
  totalKey: 'rows.totalElements',
  primaryKey: 'feedbackProjectId', // 表格唯一性主键
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('序号'),
    },
    {
      name: 'maintainItems',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maintainItems`).d('维修项目'),
      required: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/qis-qa-feedback-projects/detail`,
        method: 'GET',
      };
    },
  },
});

export { detailDS, detailLineDS, maintainItemDS };