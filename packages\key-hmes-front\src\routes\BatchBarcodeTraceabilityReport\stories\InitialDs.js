// import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.inventory.initial.model';

// const prefix = '/yp-mes-38546'

const initialDs = () => ({
  autoQuery: false,
  primaryKey: 'lineNumber',
  selection: 'false',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'processBarcodeListStr',
      type: 'string',
      label: intl.get(`${modelPrompt}.processBarcodeListStr`).d('产品条码'),
    },
    {
      name: 'orderType',
      lookupCode: 'HME.TRACE_DIRECATION',
      label: intl.get(`${modelPrompt}.orderType`).d('追溯方向'),
      type: 'string',
    },
    {
      name: 'identificationLevel',
      type: 'string',
      label: intl.get(`${modelPrompt}.identificationLevel`).d('条码层级'),
    },
    {
      name: 'traceLevel',
      type: 'string',
      label: intl.get(`${modelPrompt}.traceLevel`).d('追溯层级'),
    },
  ],
  fields: [
    // {
    //   name: 'seq',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.seq`).d('电池'),
    // },
    // {
    //   name: 'status',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.status`).d('电芯'),
    // },
    // {
    //   name: 'LDX',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.LDX`).d('裸电芯'),
    // },
    // {
    //   name: 'GF',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.GF`).d('辑分卷'),
    // },
    // {
    //   name: 'TB',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.TB`).d('涂布卷'),
    // },
    // {
    //   name: 'JL',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.JL`).d('浆料'),
    // },
  ],
  transport: {
    read: ({data}) => {
      return {
        method: 'POST',
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-trace-rel-priors/query/ui`,
        data: {
          ...data,
          traceLevel: null,
        },
      };
    },
  },
});


export { initialDs };
