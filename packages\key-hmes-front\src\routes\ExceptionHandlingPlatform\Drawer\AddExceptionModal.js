import React, { Component } from 'react';
import { Modal, Row, Col, Form, Input, Button } from 'hzero-ui';
import { connect } from 'dva';
import { Bind } from 'lodash-decorators';
// import UploadModal from 'components/Upload/index';
import Lov from 'components/Lov';
import { getCurrentOrganizationId } from 'utils/utils';
import styles from '../index.less';
// import scannerImageMat from '@/assets/scannerImageMat.png';

const { TextArea } = Input;
@Form.create({ fieldNameProp: null })
@connect(({ exceptionHandlingPlatform }) => ({
  exceptionHandlingPlatform,
}))
export default class AddExceptionModal extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() {
    const { queryDefaultResponsible } = this.props;
    if (queryDefaultResponsible) {
      queryDefaultResponsible();
    }
  }

  // 扫描设备
  @Bind()
  enterEquipment(modalType) {
    const { enterEquipment, form } = this.props;
    if (enterEquipment) {
      form.validateFields((err, values) => {
        if (!err) {
          // 如果验证成功,则执行enterSite
          enterEquipment(values, modalType);
        }
      });
    }
  }

  @Bind()
  hideModal() {
    this.props.hideModal(false, false, '');
  }

  @Bind()
  handleOk(exceptionLabelListDetail) {
    const { handleOk, form, modalType } = this.props;
    if (handleOk) {
      form.validateFields((err, values) => {
        if (!err) {
          // 如果验证成功,则执行enterSite
          handleOk(values, exceptionLabelListDetail, modalType);
        }
      });
    }
  }

  render() {
    const {
      exceptionModal,
      form,
      modalType,
      exceptionLabelListDetail = {},
      // hmeSignInOutRecordDTO1List = [],
      createExceptionRecordLoading,
      enterSiteInfo,
      defaultResponsible,
    } = this.props;
    const formLayout = {
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
    };
    const { getFieldDecorator } = form;
    // const peopleList = []; // 获取操作员列表
    // const peopleId = []; // 提交传操作员Id
    // eslint-disable-next-line array-callback-return
    // hmeSignInOutRecordDTO1List.map((ele) => {
    //   peopleList.push(ele.employeeName); // 遍历取出操作员名称
    // });
    // eslint-disable-next-line array-callback-return
    // hmeSignInOutRecordDTO1List.map((ele) => {
    //   peopleId.push(ele.employeeId); // 遍历取出操作员Id
    // });
    // const handerId = peopleId.toString(); // 类型转换
    // const handerList = peopleList.toString(); // 类型转换
    // this.props.exceptionHandlingPlatform.employName = handerId;
    return (
      <Modal
        title="异常提交"
        visible={exceptionModal}
        onOk={() => this.handleOk(exceptionLabelListDetail)}
        onCancel={() => this.hideModal()}
        confirmLoading={createExceptionRecordLoading}
        okText="提交"
        cancelText="取消"
        className={styles['exception-model']}
        style={{ paddingBottom: '0px' }}
      >
        <Form className={styles['exception-model-form']}>
          <Row>
            <Col span={12}>
              <Form.Item {...formLayout} label="异常项">
                {exceptionLabelListDetail.exceptionName}
              </Form.Item>
            </Col>
            {/* <Col span={12}>
              <Form.Item {...formLayout} label="班次">
                {exceptionLabelListDetail.shiftCode}
              </Form.Item>
            </Col> */}
          </Row>
          <Row>
            <Col span={12}>
              <Form.Item {...formLayout} label="工作单元">
                {enterSiteInfo.workcellCode}
              </Form.Item>
            </Col>
            <Col span={12}>
              {modalType === 'EQUIPMENT' &&
              <Form.Item labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} label="设备">
                {getFieldDecorator('assetIds', {
                  initialValue:
                    defaultResponsible && defaultResponsible.assetDesc
                      ? defaultResponsible.assetDesc
                      : null,
                  rules: [
                    {
                      required: true,
                      message: '设备不能为空',
                    },
                  ],
                })(
                  <Lov
                    code="HME.EQUIPMENT"
                    textValue={defaultResponsible && defaultResponsible.assetDesc}
                    queryParams={{ tenantId: getCurrentOrganizationId(), locationCode: enterSiteInfo.workcellCode }}
                    onChange={(value, records) => {
                      this.props.form.setFieldsValue({
                        ...records,
                      });
                    }}
                  />,
                )}
              </Form.Item>}

            </Col>
            {/* <Col span={12}>
              <Form.Item {...formLayout} label="指令单">
                {enterSiteInfo.taskOrderNum}
              </Form.Item>
            </Col> */}
            {/* <Col span={12} /> */}
            {/* <Col span={12}>
              <Form.Item {...formLayout} label="出勤员工">
                {handerList}
              </Form.Item>
            </Col> */}
          </Row>
          <Row>
            <Col span={12}>
              <Form.Item labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} label="负责人">
                {getFieldDecorator('exceptionUserId', {
                  initialValue:
                    defaultResponsible && defaultResponsible.realName
                      ? defaultResponsible.userId
                      : null,
                  rules: [
                    {
                      required: true,
                      message: '负责人不能为空',
                    },
                  ],
                })(
                  <Lov
                    code="HME.EXCEPTION_TYPE_PERSON"
                    textValue={defaultResponsible && defaultResponsible.realName}
                    queryParams={{ tenantId: getCurrentOrganizationId(), exceptionType: modalType }}
                    onChange={(value, records) => {
                      this.props.form.setFieldsValue({
                        realName: records.realName,
                        userId: records.userId,
                      });
                    }}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col style={{ display: 'none' }}>
              <Form.Item>
                {getFieldDecorator('realName', {
                  initialValue:
                    defaultResponsible && defaultResponsible.realName
                      ? defaultResponsible.realName
                      : null,
                })(<Input />)}
              </Form.Item>
            </Col>
            <Col style={{ display: 'none' }}>
              <Form.Item>
                {getFieldDecorator('userId', {
                  initialValue:
                    defaultResponsible && defaultResponsible.userId
                      ? defaultResponsible.userId
                      : null,
                })(<Input />)}
              </Form.Item>
            </Col>
            <Col style={{ display: 'none' }}>
              <Form.Item>
                {getFieldDecorator('assetNum', {
                  initialValue:
                    defaultResponsible && defaultResponsible.assetNum
                      ? defaultResponsible.assetNum
                      : null,
                })(<Input />)}
              </Form.Item>
            </Col>
            <Col style={{ display: 'none' }}>
              <Form.Item>
                {getFieldDecorator('assetDesc', {
                  initialValue:
                    defaultResponsible && defaultResponsible.assetDesc
                      ? defaultResponsible.assetDesc
                      : null,
                })(<Input />)}
              </Form.Item>
            </Col>
            <Col style={{ display: 'none' }}>
              <Form.Item>
                {getFieldDecorator('assetId', {
                  initialValue:
                    defaultResponsible && defaultResponsible.assetId
                      ? defaultResponsible.assetId
                      : null,
                })(<Input />)}
              </Form.Item>
            </Col>
            <Col style={{ display: 'none' }}>
              <Form.Item>
                {getFieldDecorator('assetLocationId', {
                  initialValue:
                    defaultResponsible && defaultResponsible.assetLocationId
                      ? defaultResponsible.assetLocationId
                      : null,
                })(<Input />)}
              </Form.Item>
            </Col>
            <Col style={{ display: 'none' }}>
              <Form.Item>
                {getFieldDecorator('locationName', {
                  initialValue:
                    defaultResponsible && defaultResponsible.locationName
                      ? defaultResponsible.locationName
                      : null,
                })(<Input />)}
              </Form.Item>
            </Col>
            <Col style={{ display: 'none' }}>
              <Form.Item>
                {getFieldDecorator('maintSiteId', {
                  initialValue:
                    defaultResponsible && defaultResponsible.maintSiteId
                      ? defaultResponsible.maintSiteId
                      : null,
                })(<Input />)}
              </Form.Item>
            </Col>
          </Row>
          {/* <Row>
            <Col>
              <Form.Item labelCol={{ span: 5 }} wrapperCol={{ span: 16 }} label="附件名称">
                {getFieldDecorator('attachmentUuid', {})(<UploadModal bucketName="file-mes" />)}
              </Form.Item>
            </Col>
          </Row> */}
          {/* {modalType === 'EQUIPMENT' && (
            <Row>
              <Col>
                <Form.Item labelCol={{ span: 5 }} wrapperCol={{ span: 16 }} label="设备编码">
                  {getFieldDecorator('equipmentCode', {
                    rules: [
                      {
                        required: true,
                        message: '设备编码不能为空',
                      },
                    ],
                  })(
                    <Input
                      placeholder="请扫描条码"
                      suffix={<img style={{ width: '20px' }} src={scannerImageMat} alt="" />}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
          )}
          {modalType === 'MATERIAL' && (
            <Row>
              <Col span={12}>
                <Form.Item labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} label="物料条码">
                  {getFieldDecorator(
                    'materialLotCode',
                    {}
                  )(
                    <Input
                      placeholder="请扫描条码"
                      suffix={<img style={{ width: '20px' }} src={scannerImageMat} alt="" />}
                      disabled={getFieldValue('materialId')}
                      onChange={() => {
                        setFieldsValue({
                          materialId: null,
                        });
                      }}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} label="物料编码">
                  {getFieldDecorator('materialId', {
                    rules: [
                      {
                        required: !getFieldValue('materialLotCode'),
                        message: '物料编码不能为空',
                      },
                    ],
                  })(
                    <Lov
                      code="HMES.MATERIAL"
                      disabled={getFieldValue('materialLotCode')}
                      queryParams={{ tenantId: getCurrentOrganizationId(), enableFlag: 'Y' }}
                      onChange={(value, ele) => {
                        setFieldsValue({
                          materialLotCode: '',
                        });
                        this.props.exceptionHandlingPlatform.materialCode = ele.materialCode;
                      }}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
          )} */}
          <Row>
            <Col>
              <Form.Item labelCol={{ span: 5 }} wrapperCol={{ span: 16 }} label="异常描述">
                {getFieldDecorator('exceptionRemark', {})(<TextArea rows={4} />)}
              </Form.Item>
              <Form.Item>
                <Button
                  style={{
                    display: 'none',
                  }}
                  htmlType="submit"
                  onClick={() => this.enterEquipment(modalType)}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
}
