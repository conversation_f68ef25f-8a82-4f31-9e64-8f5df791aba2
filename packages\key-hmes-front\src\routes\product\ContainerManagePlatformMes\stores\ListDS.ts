/**
 * @Description: 物料批管理平台DS
 * @Author: <<EMAIL>>
 * @Date: 2022-01-20 17:28:53
 * @LastEditTime: 2023-05-30 10:09:26
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSet } from 'choerodon-ui/pro';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
// import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.product.containerManagePlatformMes';

const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  primaryKey: 'containerId',
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  pageSize: 20,
  queryFields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('容器标识'),
    },
    {
      name: 'containerTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
      lovCode: 'HME.CONTAINER_TYPE_CODE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'containerTypeId',
      type: FieldType.number,
      bind: 'containerTypeLov.containerTypeId',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'areaLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        locatorCategory: 'AREA',
      },
    },
    {
      name: 'areaLocatorId',
      type: FieldType.number,
      bind: 'areaLocatorLov.locatorId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'topContainerFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerFlag`).d('是否顶层容器'),
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'emptyContainerFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.emptyContainerFlag`).d('是否空容器'),
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'materialLotIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotIdentification`).d('物料批'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('容器状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=CONTAINER_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'containerClassification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerClassification`).d('容器分类'),
      lookupCode: 'MT.CONTAINER_CLASSIFICATION',
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
    },
    {
      name: 'ownerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        lovCode({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'MT.MODEL.CUSTOMER';
            case 'SI':
            case 'IIS':
              return 'MT.MODEL.SUPPLIER';
            case 'OI':
              return 'MT.MES.SO_LINE';
            default:
              return 'MT.MES.EMPTY';
          }
        },
        textField({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'customerCode';
            case 'SI':
            case 'IIS':
              return 'supplierCode';
            case 'OI':
              return 'soNumContent';
            default:
              return 'noData';
          }
        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerLov.customerId',
      dynamicProps: {
        bind({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'ownerLov.customerId';
            case 'SI':
            case 'IIS':
              return 'ownerLov.supplierId';
            case 'OI':
              return 'ownerLov.soLineId';
            default:
              return 'ownerLov.customerId';
          }
        },
      },
    },
    {
      name: 'reservedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
      textField: 'meaning',
      valueField: 'value',
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'reservedObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectTypeDesc`).d('预留对象类型'),
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=RESERVE_OBJECT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'reservedObjectLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'MT.MODEL.CUSTOMER';
            case 'WO':
              return 'MT.WORK_ORDER';
            case 'EO':
              return 'MT.EO';
            case 'PO_LINE':
              return 'MT.MES.PO_LINE';
            default:
              return 'MT.MES.EMPTY';
          }
        },
        textField: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'customerCode';
            case 'WO':
              return 'workOrderNum';
            case 'EO':
              return 'eoNum';
            case 'PO_LINE':
              return 'poNumberAndLine';
            default:
              return 'noData';
          }
        },
        disabled: ({ record }) => {
          return (
            !record.get('reservedObjectType') ||
            ['OO', 'DRIVING'].includes(record.get('reservedObjectType'))
          );
        },
      },
    },
    {
      name: 'reservedObjectId',
      type: FieldType.number,
      bind: 'reservedObjectLov.customerId',
      dynamicProps: {
        bind({ record }) {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'reservedObjectLov.customerId';
            case 'WO':
              return 'reservedObjectLov.workOrderId';
            case 'EO':
              return 'reservedObjectLov.eoId';
            case 'PO_LINE':
              return 'reservedObjectLov.poLineId';
            default:
              return 'reservedObjectLov.customerId';
          }
        },
      },
    },
  ],
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('容器标识'),
    },
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
    },
    {
      name: 'containerClassificationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerClassification`).d('容器分类'),
    },
    {
      name: 'containerCategoryMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCategory`).d('容器类别'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('容器状态'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'areaLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'lastLoadTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastLoadTime`).d('最后装载时间'),
    },
    {
      name: 'lastUnloadTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUnloadTime`).d('最后卸载时间'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
    {
      name: 'reservedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
    },
    {
      name: 'reservedObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectTypeDesc`).d('预留对象类型'),
    },
    {
      name: 'reservedObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
    },
    {
      name: 'currentContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentContainerCode`).d('当前装载容器'),
    },
    {
      name: 'topContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerCode`).d('顶层装载容器'),
    },
    {
      name: 'printTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedBy`).d('最后更新人'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
    },
    {
      name: 'containerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerName`).d('容器名称'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('容器描述'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.LIST`,
        method: 'GET',
      };
    },
  },
});

const containerLoadDetailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  dataKey: 'rows',
  primaryKey: 'uuid',
  parentField: 'parentUuid',
  idField: 'uuid',
  expandField: 'expand',
  fields: [
    { name: 'uuid' },
    { name: 'containerId', type: FieldType.number },
    { name: 'parentUuid', type: FieldType.string, parentFieldName: 'uuid' },
    { name: 'expand', type: FieldType.boolean, defaultValue: false },
    {
      name: 'loadObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadObjectType`).d('装载对象类型'),
    },
    {
      name: 'loadObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadObjectCode`).d('装载对象编码'),
    },
    {
      name: 'loadQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.loadQty`).d('装载数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('单位编码'),
    },
    {
      name: 'loadSequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadSequence`).d('装载顺序'),
    },
    {
      name: 'locationRow',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locationRow`).d('容器行'),
    },
    {
      name: 'locationColumn',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locationColumn`).d('容器列'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container-load-detail/query/ui`,
        method: 'get',
      };
    },
  },
});

const containerHistoryTableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件主键'),
    },
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
    },
    {
      name: 'eventTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型描述'),
    },
    {
      name: 'eventRequestId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventRequestId`).d('事件请求ID'),
    },
    {
      name: 'requestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('事件请求类型编码'),
    },
    {
      name: 'requestTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeDesc`).d('事件请求类型描述'),
    },
    {
      name: 'eventBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventBy`).d('操作人'),
    },
    {
      name: 'eventTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTime`).d('操作时间'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('容器标识'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
    },
    {
      name: 'containerClassificationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerClassification`).d('容器分类'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('容器状态'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'lastLoadTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastLoadTime`).d('最后装载时间'),
    },
    {
      name: 'lastUnloadTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUnloadTime`).d('最后卸载时间'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },

    {
      name: 'reservedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
    },
    {
      name: 'reservedObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectTypeDesc`).d('预留对象类型'),
    },
    {
      name: 'reservedObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
    },
    {
      name: 'currentContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentContainerCode`).d('当前装载容器'),
    },
    {
      name: 'topContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerCode`).d('顶层装载容器'),
    },
    {
      name: 'printTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedBy`).d('最后更新人'),
    },
    {
      name: 'containerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerName`).d('容器名称'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('容器描述'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/his/query/ui`,
        method: 'get',
      };
    },
  },
});

const containerLoadHisDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'loadObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadObjectType`).d('装载对象类型'),
    },
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件主键'),
    },
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
    },
    {
      name: 'eventTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型描述'),
    },
    {
      name: 'eventRequestId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventRequestId`).d('事件请求ID'),
    },
    {
      name: 'requestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('事件请求类型编码'),
    },
    {
      name: 'requestTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeDesc`).d('事件请求类型描述'),
    },
    {
      name: 'eventBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventBy`).d('操作人'),
    },
    {
      name: 'eventTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTime`).d('操作时间'),
    },
    {
      name: 'loadObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadObjectCode`).d('装载对象编码'),
    },
    {
      name: 'loadQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.loadQty`).d('装载数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('单位编码'),
    },
    {
      name: 'loadSequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadSequence`).d('装载顺序'),
    },
    {
      name: 'locationRow',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locationRow`).d('容器行'),
    },
    {
      name: 'locationColumn',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locationColumn`).d('容器列'),
    },
    {
      name: 'trxLoadQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.trxLoadQty`).d('本次变更数量'),
    },
    // {
    //   name: 'materialCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    // },
    // {
    //   name: 'revisionCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    // },
    // {
    //   name: 'lot',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.lot`).d('批次'),
    // },
    // {
    //   name: 'materialLotStatusDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
    // },
    // {
    //   name: 'qualityStatusDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    // },
    // {
    //   name: 'enableFlag',
    //   type: FieldType.string,
    //   label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
    // },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container-load-detail/his/query/ui`,
        method: 'get',
      };
    },
  },
});

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  fields: [
    { name: 'containerId' },
    // 新建 start
    {
      name: 'containerTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
      lovCode: 'HME.CONTAINER_TYPE_CODE',
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'containerTypeId',
      bind: 'containerTypeLov.containerTypeId',
    },
    {
      name: 'containerTypeCode',
      bind: 'containerTypeLov.containerTypeCode',
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('容器状态'),
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'NEW',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=CONTAINER_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled({ record }) {
          if (!record.get('siteId')) {
            return true;
          }
          return false;
        },
        required({ record }) {
          if (record.get('siteId')) {
            return true;
          }
          return false;
        },
      },
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorCode',
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
      textField: 'description',
      valueField: 'typeCode',
      defaultValue: '',
      options: new DataSet({
        autoQuery: true,
        dataKey: 'rows',
        paging: false,
        transport: {
          read: () => {
            return {
              url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
              method: 'GET',
              params: { tenantId },
              transformResponse: val => {
                const data = JSON.parse(val);
                data.rows.push({
                  description: intl.get(`tarzan.common.ownerType`).d('自有'),
                  typeCode: '',
                  typeGroup: 'OWNER_TYPE',
                });
                return {
                  ...data,
                };
              },
            };
          },
        },
      }),
    },
    {
      name: 'ownerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'MT.MODEL.CUSTOMER';
          }
          if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'MT.MODEL.SUPPLIER';
          }
          if (record.get('ownerType') === 'OI') {
            return 'MT.MES.SO_LINE';
          }
          return '';
        },
        textField({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'customerCode';
          }
          if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'supplierCode';
          }
          if (record.get('ownerType') === 'OI') {
            return 'soNumContent';
          }
          return '';
        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
        },
        required({ record }) {
          return ['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerLov.customerId',
      dynamicProps: {
        bind({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'ownerLov.customerId';
          }
          if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'ownerLov.supplierId';
          }
          if (record.get('ownerType') === 'OI') {
            return 'ownerLov.soLineId';
          }
          return 'ownerLov.customerId';
        },
      },
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'ownerLov.customerCode';
          }
          if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'ownerLov.supplierCode';
          }
          if (record.get('ownerType') === 'OI') {
            return 'ownerLov.soNumContent';
          }
          return '';
        },
      },
    },
    {
      name: 'createCount',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.createCount`).d('个数'),
      step: 1,
      min: 1,
      defaultValue: 1,
    },
    // 新建 end
    // 编辑 start
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.identification`).d('容器标识'),
      type: FieldType.string,
    },
    {
      name: 'reservedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'reservedObjectType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=RESERVE_OBJECT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.reservedObjectTypeDesc`).d('预留对象类型'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('reservedFlag') !== 'Y';
        },
        required: ({ record }) => {
          return record.get('reservedFlag') === 'Y';
        },
      },
    },
    {
      name: 'reservedObjectLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
      lovCode: 'MT.MODEL.CUSTOMER', // 必须得有默认值，不然就会变成一个Input而不是Lov了
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('reservedObjectType') ||
            ['OO', 'DRIVING'].includes(record.get('reservedObjectType'))
          );
        },
        required: ({ record }) => {
          return (
            record.get('reservedObjectType') &&
            !['OO', 'DRIVING'].includes(record.get('reservedObjectType'))
          );
        },
        lovCode: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'MT.MODEL.CUSTOMER';
            case 'WO':
              return 'MT.WORK_ORDER';
            case 'EO':
              return 'MT.EO';
            case 'PO_LINE':
              return 'MT.MES.PO_LINE';
            default:
              return '';
          }
        },
        textField: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'customerCode';
            case 'WO':
              return 'workOrderNum';
            case 'EO':
              return 'eoNum';
            case 'PO_LINE':
              return 'poNumberAndLine';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'reservedObjectId',
      bind: '',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'reservedObjectLov.customerId';
            case 'WO':
              return 'reservedObjectLov.workOrderId';
            case 'EO':
              return 'reservedObjectLov.eoId';
            case 'PO_LINE':
              return 'reservedObjectLov.poLineId';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'reservedObjectCode',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'reservedObjectLov.customerCode';
            case 'WO':
              return 'reservedObjectLov.workOrderNum';
            case 'EO':
              return 'reservedObjectLov.eoNum';
            case 'PO_LINE':
              return 'reservedObjectLov.poNumberAndLine';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'currentContainerCode',
      label: intl.get(`${modelPrompt}.currentContainerCode`).d('当前装载容器'),
    },
    {
      name: 'topContainerCode',
      label: intl.get(`${modelPrompt}.topContainerCode`).d('顶层装载容器'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
    },
    {
      name: 'containerName',
      label: intl.get(`${modelPrompt}.containerName`).d('容器名称'),
    },
    {
      name: 'description',
      label: intl.get(`${modelPrompt}.description`).d('容器描述'),
    },
    {
      name: 'printTimes',
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedBy`).d('最后更新人'),
    },
  ],
});

export { tableDS, containerLoadDetailDS, containerHistoryTableDS, containerLoadHisDS, detailDS };
