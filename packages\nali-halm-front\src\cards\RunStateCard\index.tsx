import React, { useEffect, useState, useCallback } from 'react';
import { Collapse } from 'choerodon-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import axios from 'axios';
import { HALM_ATN } from 'alm/utils/config';

import WaterWave from './components/WaterWave';
import styles from './index.module.less';

const organizationId = getCurrentOrganizationId();
const url = `${HALM_ATN}/v1/${organizationId}/asset-cards/run-status-analysis`;

const AssetUsageCard = () => {
  const [data, setData] = useState<any>(undefined);
  const [percent, setPercent] = useState<number>(0);
  useEffect(() => {
    fetchData();
  }, []);
  const fetchData = useCallback(async () => {
    const res = await axios.get<any, any>(url);
    setData(res);
    setPercent(res.percent);
  }, []);
  return (
    <div className={styles.container}>
      <Collapse
        bordered={false}
        expandIconPosition="right"
        defaultActiveKey={['A']}
        trigger="icon"
        className={styles['customize-collapse']}
      >
        <Collapse.Panel key="A" showArrow={false} header="运行状态统计">
          <div className={styles['collapse-panel']}>
            <div className={styles.ball}>
              <WaterWave
                onlyWave
                height={120}
                percent={percent}
                percentRender={() => (
                  <div className={styles.percent}>
                    <span>{percent}%</span>
                  </div>
                )}
              />
            </div>
            <div className={styles['data-container']}>
              <h6 className={styles['data-item']}>
                设备总数：<span>{data?.allAssetCount}</span>
              </h6>
              <h6 className={styles['data-item']}>
                正常运行：<span>{data?.runningCount}</span>
              </h6>
              <h6 className={styles['data-item']} style={{ margin: '0 0 0 1em' }}>
                停机中：<span>{data?.stoppedCount}</span>
              </h6>
            </div>
          </div>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default AssetUsageCard;
