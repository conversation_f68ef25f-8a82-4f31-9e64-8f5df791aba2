import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config'

const tenantId = getCurrentOrganizationId();
const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`

const ListPageDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  cacheSelection: true,
  primaryKey: 'temporaryPermitDocId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-temporary-permit-doc/page/ui`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.temporaryPermitNum`).d('变更单号'),
      name: 'temporaryPermitNum',
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.temporaryPermitStatus`).d('状态'),
      name: 'temporaryPermitStatus',
      lookupCode: 'YP.QIS.TEMP_PERMIT_DOC_STATUS',
      lovPara: { tenantId },
    },
    {
      name: 'inspectScheme',
      lovCode: 'MT.QMS.INSPECT_SCHEME',
      label: intl.get(`${modelPrompt}.form.inspectScheme`).d('检验方案编码'),
      lovPara: { tenantId },
      type: FieldType.object,
      ignore: FieldIgnore.always,
      textField: 'inspectSchemeCode',
      valueField: 'inspectSchemeId',
    },
    {
      name: 'inspectSchemeId',
      bind: 'inspectScheme.inspectSchemeId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.materialName`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.prodLine`).d('产线'),
      name: 'prodLineObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.PRODLINE',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineObject.prodLineId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.operation`).d('工艺'),
      name: 'operationObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.OPERATION',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'operationId',
      bind: 'operationObject.operationId',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.equipment`).d('设备'),
      name: 'equipmentObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'equipmentId',
      bind: 'equipmentObject.equipmentId',
    },
    {
      name: 'departmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.departmentLov`).d('发起专业'),
      lovCode: 'YP.QIS.COMPANY_UNIT',
      ignore: FieldIgnore.always,
      textField: 'unitName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'temporaryPermitDeptId',
      bind: 'departmentLov.unitId',
    },
    {
      name: 'responsiblePersonLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.responsiblePersonLov`).d('发起人'),
      ignore: FieldIgnore.always,
      lovCode: 'YP.QIS.USER_LIMIT_EMPLOYEE_POSITION',
      textField: 'realName',
      lovPara: { tenantId },
    },
    {
      name: 'createdBy',
      bind: 'responsiblePersonLov.id',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'scheduleStartDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.scheduleStartDateFrom`).d('发起日从'),
      max: 'scheduleStartDateTo',
    },
    {
      name: 'scheduleStartDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.scheduleStartDateTo`).d('发起日至'),
      min: 'scheduleStartDateFrom',
    },
    {
      name: 'scheduleEndDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.scheduleEndDateFrom`).d('到期日从'),
      max: 'scheduleEndDateTo',
    },
    {
      name: 'scheduleEndDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.scheduleEndDateTo`).d('到期日至'),
      min: 'scheduleEndDateFrom',
    },
  ],
  fields: [
    {
      type: FieldType.string,
      name: 'temporaryPermitNum',
      label: intl.get(`${modelPrompt}.table.temporaryPermitNum`).d('变更单号'),
    },
    {
      type: FieldType.string,
      name: 'temporaryPermitStatus',
      lookupCode: 'YP.QIS.TEMP_PERMIT_DOC_STATUS',
      label: intl.get(`${modelPrompt}.table.temporaryPermitStatus`).d('状态'),
    },
    {
      type: FieldType.string,
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.table.materialCode`).d('物料'),
    },
    {
      type: FieldType.string,
      name: 'prodLineCode',
      label: intl.get(`${modelPrompt}.table.prodLineCode`).d('产线'),
    },
    {
      type: FieldType.string,
      name: 'operationName',
      label: intl.get(`${modelPrompt}.table.operationName`).d('工艺'),
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.table.equipment`).d('设备'),
      name: 'equipmentName',
      ignore: FieldIgnore.always,
      multiple: true,
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'equipmentIdList',
      bind: 'equipmentName.equipmentId',
    },
    {
      name: 'equipmentCodeList',
      bind: 'equipmentName.equipmentCode',
    },
    {
      type: FieldType.string,
      name: 'inspectSchemeCode',
      label: intl.get(`${modelPrompt}.table.inspectSchemeCode`).d('检验方案编码'),
    },
    {
      type: FieldType.string,
      name: 'temporaryPermitDeptName',
      label: intl.get(`${modelPrompt}.table.temporaryPermitDeptName`).d('发起专业'),
    },
    {
      type: FieldType.string,
      name: 'createdByName',
      label: intl.get(`${modelPrompt}.table.createdByName`).d('发起人'),
    },
    {
      type: FieldType.string,
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.table.creationDate`).d('创建时间'),
    },
    {
      type: FieldType.string,
      name: 'scheduleStartDate',
      label: intl.get(`${modelPrompt}.table.scheduleStartDate`).d('发起日'),
    },
    {
      type: FieldType.string,
      name: 'scheduleEndDate',
      label: intl.get(`${modelPrompt}.table.scheduleEndDate`).d('到期日'),
    },
  ],
});



export default ListPageDS;