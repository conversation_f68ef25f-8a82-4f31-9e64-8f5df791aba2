/**
 * @Description: 计量器具管理平台查询界面
 */

import React, { useEffect, useState } from 'react';
import { DataSet, Modal } from 'choerodon-ui/pro';
import { Tabs } from 'choerodon-ui';
import request from 'utils/request';
import { Button as PermissionButton } from 'components/Permission';
import { openTab } from 'utils/menuTab';
import queryString from 'query-string';
import notification from 'utils/notification';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import ExcelExport from '@/components/PermissionExcelExport';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { HZERO_PLATFORM, HZERO_IAM } from 'utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import { BASIC } from '@utils/config';
import PermissionProvider from '@/components/PermissionProvider';
import { allTabledDS, detailTabledDS } from '../stories';
import MeterManagementCollect from '../MeterManagementCollect';
import MeterManagementDetail from '../MeterManagementDetail';
import EditDrawer from '../MeterManagementDetail/EditDrawer';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.qms.meterManagementPlatform';

// interface IStepRef {
//   hasStep: Function;
//   refresh: Function;
// }

const TabPane = Tabs.TabPane;

const MeterManagementList = props => {
  const {
    match: { path },
    history,
    // tableDS,
    detailDS,
    permissionDetail,
  } = props;

  const [tabKey, setTabKey] = useState('2');

  const [display, setDisplay] = useState(false);

  useEffect(() => {
    getUserCheck();
  }, []);

  const getUserCheck = async () => {
    const data = await request(`${HZERO_PLATFORM}/v1/${getCurrentOrganizationId()}/lovs/value`, {
      query: {
        lovCode: 'QIS.MS_TOOL_ROLE',
      },
    });
    if (data && data.length > 0) {
      await request(`${HZERO_IAM}/hzero/v1/${tenantId}/member-roles/user-all-roles/${getCurrentUserId()}`, {
        method: 'GET',
      }).then(res => {
        if (res) {
          const result = compareList(data.map((ele) => { return ele.value }), res.map((item) => { return item.code }))
          if (result) {
            setDisplay(true)
          } else {
            setDisplay(false)
          }
        }
      });
    }
  };

  const compareList = (array1, array2) => {
    // 遍历 array1
    for (let i = 0; i < array1.length; i++) {
      // 如果 array2 包含 array1 的当前元素，返回 true
      if (array2.includes(array1[i])) {
        return true;
      }
    }
    // array1 的所有元素都不在 array2 中，返回 false
    return false;
  };

  const handleBatchExport = () => {
    openTab({
      key: '/himp/commentImport/YP.QIS_MS_TOOL_MANAGE',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const handleAdd = (_, flag) => {
    if (!flag) {
      detailDS.create({});
    }
    setTimeout(() => {
      Modal.open({
        ...drawerPropsC7n({ ds: detailDS }),
        key: Modal.key(),
        title: flag
          ? intl.get(`${modelPrompt}.title.edit`).d('编辑')
          : intl.get(`${modelPrompt}.title.create`).d('新建'),
        style: {
          width: 720,
        },
        children: <EditDrawer ds={detailDS} permissionDetail={permissionDetail} />,
        footer: (_, cancelButton, modal) => ([
          cancelButton,
          <PermissionButton
            color={ButtonColor.primary}
            type="c7n-pro"
            permissionList={[
              {
                code: `${modelPrompt}.modelDetail.editConfirm`,
                type: 'button',
                meaning: '量具明细-编辑确认按钮',
              },
            ]}
            onClick={() => handleDrawerConfirm(modal)}
          >
            {intl.get('tarzan.common.button.confirm').d('确认')}
          </PermissionButton>,
        ]),
      });
    }, 100);
  };

  const handleDrawerConfirm = async (modal) => {
    detailDS.current.set({ nowDate: new Date().getTime() });
    const validate = await detailDS.validate();
    if (!validate) {
      return false;
    }
    const params = {
      ...detailDS.current.toJSONData(),
    };
    return request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/ms-tool-manages/update/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        notification.success({});
        detailDS.query(detailDS.currentPage);
        modal.close();
        return true;
      }
      notification.error({ message: res.message });
      return false;
    });
  };

  const changeTab = key => {
    setTabKey(key);
  };

  // 导出传参
  const handleQuerySearchForm = () => {
    return detailDS.queryDataSet?.toData()[0];
  };

  const handDelete = async () => {
    if (detailDS.selected.length < 1) {
      Modal.warning(
        intl.get(`${modelPrompt}.line.not.required`).d('请至少选择一条数据！'),
      );
      return;
    }
    Modal.warning({
      drawer: false,
      closable: true,
      children: (
        <h3>{intl.get(`${modelPrompt}.sendOn.wechat.error`).d('确定删除？')}</h3>
      ),
      okText: intl.get(`${modelPrompt}.delete`).d('删除'),
      onOk: () => handleDelete(),
    });
  };

  const handleDelete = async () => {
    await request(`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/ms-tool-manages/delete/ui`, {
      method: 'POST',
      body: { msToolManageId: detailDS.selected.map((ele) => { return ele.get('msToolManageId') }) },
    }).then(res => {
      if (res && !res.failed) {
        notification.success({});
        detailDS.query(detailDS.currentPage);
        return true;
      }
      notification.error({ message: res.message });
      return false;
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('计量器具管理平台')}>
        {tabKey === '2' && (
          <>
            <ExcelExport
              requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/ms-tool-manages/export/ui`}
              method="get"
              otherButtonProps={{ type: 'primary' }}
              queryParams={handleQuerySearchForm}
              permissionList={[
                {
                  code: `${modelPrompt}.list.button.export`,
                  type: 'button',
                  meaning: '导出',
                },
              ]}
              buttonText={intl.get('tarzan.common.button.export').d('导出')}
            />
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${modelPrompt}.list.button.import`,
                  type: 'button',
                  meaning: '导入量具',
                },
              ]}
              onClick={handleBatchExport}
            >
              {intl.get(`${modelPrompt}.button.import`).d('导入量具')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="add"
              onClick={handleAdd}
              permissionList={[
                {
                  code: `${modelPrompt}.list.button.create`,
                  type: 'button',
                  meaning: '新建按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.createMeter`).d('新建量具')}
            </PermissionButton>
            {
              display && (
                <PermissionButton
                  type="c7n-pro"
                  color={ButtonColor.red}
                  icon="delete"
                  onClick={handDelete}
                  permissionList={[
                    {
                      code: `${path}.button.delete`,
                      type: 'button',
                      meaning: '删除',
                    },
                  ]}
                >
                  删除
                </PermissionButton>
              )
            }
          </>
        )}
      </Header>
      <Content>
        <Tabs defaultActiveKey="2" style={{ backgroundColor: '#ffff' }} onChange={changeTab}>
          <TabPane tab={intl.get(`${modelPrompt}.tabPane.meterSum`).d('量具汇总')} key="1">
            <MeterManagementCollect />
          </TabPane>
          <TabPane tab={intl.get(`${modelPrompt}.tabPane.meterDetail`).d('量具明细')} key="2">
            <MeterManagementDetail ds={detailDS} handleAdd={handleAdd} history={history} />
          </TabPane>
        </Tabs>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.common', modelPrompt],
})(
  withProps(
    () => {
      const tableDS = new DataSet({
        ...allTabledDS(),
      });
      const detailDS = new DataSet({
        ...detailTabledDS(),
      });
      return {
        tableDS,
        detailDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    PermissionProvider({ noDisplayCode: 'modelDetail.editConfirm', modelPrompt })(MeterManagementList),
  ),
);
