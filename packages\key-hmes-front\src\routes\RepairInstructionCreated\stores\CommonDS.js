/**
 * 公共状态类型ds
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 获取工艺路线类型
const routeStepOptionDS = (typeGroup) => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          typeGroup,
          module: 'ROUTER',
        },
      };
    },
  },
});

// 获取工艺路线状态
const routeStatusOptionDS = () => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          module: 'ROUTER',
          statusGroup: 'ROUTER_STATUS',
        },
        transformResponse: data => {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      };
    },
  },
})

// 获取步骤类型
const stepOptionDS = () => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          module: 'ROUTER',
          typeGroup: 'ROUTER_STEP_TYPE',
        },
      };
    },
  },
})

// 获取步骤组类型
const stepGroupOptionDS = () => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          module: 'ROUTER',
          typeGroup: 'ROUTER_STEP_GROUP_TYPE',
        },
      };
    },
  },
})


// 获取步骤决策
const stepDecisionOptionDS = () => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          module: 'ROUTER',
          typeGroup: 'QUEUE_DECISION_TYPE',
        },
      };
    },
  },
})

// 获取返回步骤下拉
const returnStepsOptionDS = () => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          module: 'ROUTER',
          typeGroup: 'RETURN_TYPE',
        },
      };
    },
  },
})

// 站点类型 用于 code->description
const siteTypeOptionDS = () => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          typeGroup: 'ORGANIZATION_REL_TYPE',
          module: 'MODELING',
        },
      };
    },
  },
})

// 下一步骤设置 选择策略下拉
const nextStepDecisionOptionDS = () => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          typeGroup: 'NEXT_DECISION_TYPE',
          module: 'ROUTER',
        },
      };
    },
  },
})

// 复制 目标类型
const copyTypesOptionDS = (typeGroup) => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui`,
        method: 'GET',
        params: {
          tenantId,
          typeGroup,
        },
      };
    },
  },
})


export {
  routeStatusOptionDS, // 获取工艺路线类型
  routeStepOptionDS, // 获取工艺路线状态
  stepOptionDS, // 获取步骤类型
  stepGroupOptionDS, // 获取步骤组类型
  stepDecisionOptionDS, // 获取步骤决策
  returnStepsOptionDS, // 获取返回步骤下拉
  siteTypeOptionDS, // 获取站点类型 用于 code->description
  nextStepDecisionOptionDS, // 下一步骤设置 选择策略下拉
  copyTypesOptionDS, // 复制 目标类型
};
