import React, { FC, useMemo, useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import queryString from 'querystring';
import { Button, DataSet, Modal, Switch } from 'choerodon-ui/pro';
import { Collapse, Tabs } from 'choerodon-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { useEditFlag } from 'alm/hooks';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { HALM_ORI } from 'alm/utils/config';

import Material from 'alm/pages/Act/Detail/Material';
import Check from 'alm/pages/Act/Detail/Check';

import request from "utils/request";
import {getCurrentOrganizationId} from "utils/utils";
import notification from "utils/notification";
import getLang from '../Langs';
import { detailDs as _detailDs, advDs as _advDs } from '../Stores/detailDs';
import showTooltipWhenOverflow from '../../../utils/tooltipUtils';

import styles from './index.modules.less';
import BasicForm from './BasicForm';
import AdvancedConfiguration from './AdvancedConfiguration';
import Step from './Step';

const { showTooltip, hideTooltip } = showTooltipWhenOverflow();

interface IStepRef {
  hasStep: Function;
  refresh: Function;
}

interface IOtherRef {
  materialRef?: any;
  checkRef?: any;
}

const otherRef: IOtherRef = {
  materialRef: null,
  checkRef: null,
};

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ORI}/v1/${organizationId}`;

const MaintainPlanDetail: FC = props => {
  const {
    history,
    location: { search },
  } = props as any;
  const { isNew, editFlag, setEditFlag, id } = useEditFlag(props); // 注意：新建时editFlag也为true

  const [activeKey, setActiveKey] = useState('step'); // 默认在维保步骤页签
  const stepRef = useRef<IStepRef>({
    hasStep: () => {},
    refresh: () => {},
  });

  const advDs = useMemo(() => new DataSet(_advDs()), []);
  const detailDs = useMemo(() => new DataSet(_detailDs(advDs)), []);
  // @ts-ignore
  const detail = detailDs.current?.toData() || {};
  const { assetId, locationId, cycleType, checklistsFlag, woChecklistFlag } = detail;

  useEffect(() => {
    if (isNew && detailDs.current) {
      const searchItem = queryString.parse(search.substring(1)) || {};
      Object.keys(searchItem).forEach(key => {
        // @ts-ignore
        detailDs.current.set(key, searchItem[key]);
      });
      // 根据当前类型设置一些字段的默认值
      if (searchItem.cycleType === 'TIME_CYCLE') {
        detailDs.current.set('planSchedBase', 'LAST_WORK_TIME'); // 计划预测基于 - 最近作业时间
        detailDs.current.set('cycleUom', 'DAY'); // 周期 - 天
      } else if (searchItem.cycleType === 'METER_CYCLETRIGGE') {
        detailDs.current.set('planSchedBase', 'LAST_WORK_VALUE'); // 最近作业读书
      }
    }
  }, []);

  useEffect(() => {
    if (!isNew) {
      detailDs.setQueryParameter('id', id);
      detailDs.query();
      advDs.setQueryParameter('planId', id);
      advDs.query();
    }
  }, [id]);

  useEffect(() => {
    // @ts-ignore
    advDs.cycleType = cycleType;
  }, [cycleType]);

  // 表单panel的头
  const getFormHeader = useMemo(() => {
    let text = '';
    const { assetDesc, visualLabel, locationName, locationCode } = detail;
    if (assetId) {
      text = visualLabel ? `${assetDesc}-${visualLabel}` : assetDesc;
    } else if (locationId) {
      text = `${locationName}-${locationCode}`;
    }
    return (
      <span
        className={styles['panel-title']}
        onMouseEnter={e => showTooltip(e, text)}
        onMouseLeave={hideTooltip}
      >
        {text}
      </span>
    );
  }, [detail.assetDesc, detail.visualLabel, detail.locationName, detail.locationCode]);

  // 表单panel右侧
  const getExtra = () => {
    return (
      <div className={styles['panel-extra']}>
        <div className={styles['panel-extra-btn']}>
          {/* 无步骤时不可编辑 详情页不可编辑 */}
          {getLang('ENABLED_FLAG')}
          <Switch
            name="enabledFlag"
            dataSet={detailDs}
            disabled={!stepRef.current.hasStep() || !editFlag}
            onClick={e => e.stopPropagation()}
          />
        </div>
        <div
          className={styles['panel-extra-text']}
          onMouseEnter={e => showTooltip(e, detail.maintSiteName)}
          onMouseLeave={hideTooltip}
        >
          {detail.maintSiteName}
        </div>
      </div>
    );
  };

  const handleBack = () => {
    if (isNew) {
      history.push('/aori/maintain-plans/list');
    } else {
      detailDs.reset();
      advDs.reset();
      setEditFlag(false);
      if (activeKey !== 'step') {
        setActiveKey('step');
      }
    }
  };

  const handleNext = async () => {
    if (detailDs.current) {
      const ok = await detailDs.current.validate();
      if (ok) {
        detailDs.submit().then(res => {
          if (res && res.success) {
            history.push(`/aori/maintain-plans/detail/${res.content[0].maintainPlanId}`, {
              isEdit: true,
            });
          }
        });
      }
    }
  };

  // 同步修改
  const handleSynchronousModification = async () => {
    const url = `${apiPrefix}/act/batch-check-sync`;
    Modal.confirm({
      key: Modal.key(),
      title: getLang('NOTICE'),
      children: getLang('SYNCHRONOUS_MODIFICATION_INFO'),
      onOk: () => {
        return new Promise(resolve => {
          request(url, {
            method: 'POST',
            body: {maintainPlanId: id},
          })
            .then(res => {
              if (res && !res.failed) {
                resolve(res);
                notification.success({});
              } else {
                notification.warning({
                  message: res.message,
                });
              }
            })
            .catch(err => {
              notification.error({
                message: err,
              });
            });
        });
      },
    });
  };

  const handleSave = async () => {
    if (detailDs.current && advDs.current) {
      const ok = await detailDs.current.validate();
      const advOk = await advDs.current.validate();
      if (!ok || !advOk) {
        if (!advOk) setActiveKey('advanced');
        return;
      }
      // 校验是否存在维保步骤
      // 如果为空，则提示“当前维保计划缺少维保步骤，保存后不可用于维护维保计划预测，是否保存？”
      // 如果点击是，则保存，界面跳转至详情。如果为否，则继续处于编辑状态。
      if (!stepRef.current.hasStep()) {
        Modal.confirm({
          key: Modal.key(),
          title: getLang('NOTICE'),
          children: getLang('SAVE_CONFIRM'),
          onOk: () => {
            confirmSave();
          },
        });
      } else {
        confirmSave();
      }
    }
  };

  const confirmSave = () => {
    detailDs.submit().then(res => {
      if (res && res.success) {
        setEditFlag(false);
        detailDs.query();
        advDs.query();
        // 工单类型改变会导致检查项数据改变 故需刷新检查项数据
        otherRef.checkRef?.refresh();
        // 步骤单位取头上的 所以也需要刷新
        stepRef.current.refresh();
      }
    });
  };

  const getBtns = () => {
    // 新建： 返回 下一步； 编辑： 返回 保存； 查看：编辑 删除
    const isView = !editFlag;
    return (
      <>
        <Button onClick={handleNext} color={ButtonColor.primary} hidden={!isNew} key="next">
          {getLang('NEXT')}
        </Button>
        <Button
          onClick={handleSave}
          color={ButtonColor.primary}
          hidden={isNew || isView}
          key="save"
        >
          {getLang('SAVE')}
        </Button>
        <Button onClick={handleBack} hidden={isView} key="back">
          {getLang('BACK')}
        </Button>
        <Button
          color={ButtonColor.primary}
          onClick={() => setEditFlag(true)}
          hidden={editFlag}
          key="edit"
        >
          {getLang('EDIT')}
        </Button>
        <Button hidden={editFlag} onClick={handleSynchronousModification} color={ButtonColor.primary}  key="synchronousModification">
          {getLang('SYNCHRONOUS_MODIFICATION')}
        </Button>
      </>
    );
  };

  const materialProps = {
    headerId: id,
    header: detail,
    headerTypeCode: 'MAINTAIN_PLAN',
  };
  return (
    <PageHeaderWrapper
      title={getLang('HEADER')}
      header={getBtns()}
      headerProps={{ backPath: '/aori/maintain-plans/list' }}
    >
      <Collapse bordered={false} defaultActiveKey={['A']} className={styles['plan-collapse']}>
        <Collapse.Panel key="A" header={getFormHeader} extra={getExtra()}>
          <BasicForm detailDs={detailDs} editFlag={editFlag} detail={detail} />
        </Collapse.Panel>
      </Collapse>
      {!isNew && (
        <Tabs activeKey={activeKey} onTabClick={key => setActiveKey(key)}>
          <Tabs.TabPane tab={getLang('TAB_STEP')} key="step">
            <Step
              headEditFlag={editFlag}
              header={detail}
              detailDs={detailDs}
              ref={stepRef}
              otherRef={otherRef}
            />
          </Tabs.TabPane>
          {(!!checklistsFlag || !!woChecklistFlag) && (
            <Tabs.TabPane tab={getLang('TAB_CHECK')} key="check">
              <Check
                {...materialProps}
                onRef={ref => {
                  otherRef.checkRef = ref;
                }}
              />
            </Tabs.TabPane>
          )}
          <Tabs.TabPane tab={getLang('TAB_MATERIAL')} key="material">
            <Material
              {...materialProps}
              onRef={ref => {
                otherRef.materialRef = ref;
              }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab={getLang('TAB_ADV')} key="advanced">
            <AdvancedConfiguration detailDs={advDs} editFlag={editFlag} header={detail} />
          </Tabs.TabPane>
        </Tabs>
      )}
    </PageHeaderWrapper>
  );
};

export default formatterCollections({
  code: ['alm.common', 'alm.component', 'aori.maintainPlans', 'amtc.act', 'alm.checklistEditModal'],
})(observer(MaintainPlanDetail));
