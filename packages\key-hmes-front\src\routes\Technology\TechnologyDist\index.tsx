/**
 * @Description: 工艺维护-详情
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 11:03:09
 * @LastEditTime: 2023-07-31 09:41:50
 * @LastEditors: <<EMAIL>>
 */

import React, { FC, useEffect, useMemo, useState, useCallback } from 'react';
import { RouteComponentProps } from 'react-router';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import {
  DataSet,
  Table,
  TextField,
  Select,
  Lov,
  IntlField,
  Switch,
  Form,
  NumberField,
  TextArea,
  DateTimePicker,
  Button,
  Spin,
} from 'choerodon-ui/pro';

import { Collapse, Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { AttributeDrawer } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { formDS, tableDS } from '../stories/TechnologyDistDs';
import {
  saveOperationConfig,
  getOperationConfig,
  deleteOperationSubSetpConfig,
  GetWorkingInstruction,
} from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.process.technology.model.technology';

const ChildSteps: FC<RouteComponentProps> = ({
  history,
  match: {
    path,
    // @ts-ignore
    params: { id },
  },
  // @ts-ignore
  customizeForm,
}) => {
  const saveOperation = useRequest(saveOperationConfig(), { manual: true, needPromise: true });
  const getOperation = useRequest(getOperationConfig(), { manual: true, needPromise: true });
  const deleteOperationSubSetp = useRequest(deleteOperationSubSetpConfig(), {
    manual: true,
    needPromise: true,
  });
  const getWorkingInstruction = useRequest(GetWorkingInstruction(), { manual: true, needPromise: true });

  const formDs = useMemo(() => {
    return new DataSet(formDS());
  }, []);

  const tableDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  const [canEdit, setCanEdit] = useState(false);

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
    } else {
      setCanEdit(false);
      initPageData();
    }
  }, [id]);

  const handleCancel = () => {
    if (id === 'create') {
      history.push('/hmes/process/technology/list');
    } else {
      setCanEdit(prev => !prev);
      initPageData();
    }
  };

  const handleJumpWorkingInstruction = useCallback(
    async () => {
      return getWorkingInstruction.run({
        params: {
          operationId: id,
        },
      }).then(res => {
        if (!res?.failed) {
          if (res?.sopHeaderId) {
            history.push(`/hmes/working-instruction-maintenance/detail/${res.sopHeaderId}`)
          } else {
            history.push({
              pathname: `/hmes/working-instruction-maintenance/detail/create`,
              search: `timer=${new Date().getTime()}`,
              state: {
                form: 'operation',
                operationId: id,
                operationName: formDs.current?.get('operationName'),
                description: formDs.current?.get('description'),
                revision: formDs.current?.get('revision'),
              },
            })
          }
        }
      })
    },
    [id],
  )

  const initPageData = () => {
    setCanEdit(false);
    getOperation.run({
      params: {
        operationId: id,
        customizeUnitCode: `${BASIC.CUSZ_CODE_BEFORE}.OPERATION_DETAIL.BASIC`,
      },
      onSuccess: res => {
        formDs.loadData([res]);
      },
      onFailed: () => {
        formDs.loadData([]);
      },
    });
    tableDs.setQueryParameter('operationId', id);
    tableDs.query();
  };

  const handleAddLine = () => {
    const newLine = tableDs.create({}, 0);
    newLine.setState('editing', true);
  };

  const handleEditMessage = record => {
    record.setState('editing', true);
  };

  const handleDeleteRecord = record => {
    if (record.get('operationSubstepId')) {
      deleteOperationSubSetp.run({
        params: record.get('operationSubstepId'),
        onSuccess: () => {
          tableDs.delete(record, false);
          // @ts-ignore
          notification.success();
        },
      });
    } else {
      tableDs.delete(record, false);
    }
  };

  const handleCleanLine = record => {
    if (record.get('operationSubstepId')) {
      record.reset();
      record.setState('editing', false);
    } else {
      tableDs.delete(record, false);
    }
  };

  const handleSave = async () => {
    const validateForm = await formDs.validate();
    const validateTable = await tableDs.validate();
    if (!validateForm || !validateTable) {
      return;
    }
    const formData = formDs.toData()[0];
    const mtOperationSubstepList = [];
    tableDs.forEach(record => {
      if (record.status === 'add' || record.status === 'update') {
        // @ts-ignore
        mtOperationSubstepList.push({
          operationId: id,
          ...record.toData(),
        });
      }
    });
    // @ts-ignore
    formData.mtOperationSubstepList = mtOperationSubstepList;
    return saveOperation.run({
      params: formData,
      onSuccess: res => {
        // @ts-ignore
        if (formData.operationId) {
          initPageData();
        } else {
          history.push(`/hmes/process/technology/dist/${res}`);
        }
        // @ts-ignore
        notification.success();
      },
    });
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit || id === 'create'}
          onClick={handleAddLine}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDeleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!canEdit || id === 'create'}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sequence',
      width: 100,
      editor: record => record.getState('editing') && <NumberField />,
    },
    {
      name: 'substepObject',
      width: 180,
      renderer: ({ record }) => {
        return record?.get('substepName');
      },
      editor: record => record.getState('editing') && <Lov />,
    },
    {
      name: 'description',
    },
    {
      name: 'extendAttrs',
      width: 120,
      renderer: ({ record }) => {
        return (
          <AttributeDrawer
            disabled={!record?.get('operationSubstepId')}
            type="text"
            // tablename="mt_operation_substep_attr"
            className="org.tarzan.method.domain.entity.MtOperationSubstep"
            kid={record!.get('operationSubstepId')}
            canEdit={canEdit}
            serverCode={BASIC.TARZAN_METHOD}
          />
        );
      },
    },
    {
      name: 'operator',
      width: 150,
      align: ColumnAlign.center,
      renderer: ({ record }) =>
        record?.getState('editing') ? (
          <a onClick={() => handleCleanLine(record)}>
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </a>
        ) : (
          <>
            <a disabled={!canEdit} onClick={() => handleEditMessage(record)}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </a>
          </>
        ),
    },
  ];

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.techTitle`).d('工艺维护')}
        backPath="/hmes/process/technology/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            disabled={saveOperation.loading || getOperation.loading || tableDs.status === 'loading'}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        {
          id !== 'create' && !canEdit && (
            <Button color={ButtonColor.primary} icon="link2" onClick={handleJumpWorkingInstruction}>
              {intl.get(`${modelPrompt}.sop`).d('SOP上传')}
            </Button>
          )
        }
      </Header>
      <Spin
        spinning={saveOperation.loading || getOperation.loading || tableDs.status === 'loading'}
      >
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'subStep']}>
            <Panel header={intl.get(`${modelPrompt}.basicInfo`).d('基础信息')} key="basicInfo">
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.OPERATION_DETAIL.BASIC`,
                },
                <Form disabled={!canEdit} dataSet={formDs} labelWidth={112} columns={3}>
                  <TextField name="operationName" />
                  <TextField name="revision" />
                  <Switch name="currentFlag" />
                  <Select name="operationStatus" />
                  <Select name="operationType" />
                  <Switch name="completeInconformityFlag" />
                  <IntlField
                    name="description"
                    modalProps={{ title: intl.get(`${modelPrompt}.description`).d('工艺描述') }}
                  />
                  <Select
                    name="workcellType"
                    onChange={() => {
                      formDs.current?.set('workCellObject', null);
                    }}
                  />
                  <Lov name="workCellObject" />
                  <DateTimePicker name="dateFrom" />
                  <DateTimePicker name="dateTo" />
                  <NumberField
                    name="standardReqdTimeInProcess"
                    suffix={
                      <span style={{ width: '80px' }}>
                        {intl.get(`tarzan.common.date.unit.minute`).d('分钟')}
                      </span>
                    }
                  />
                  <TextArea name="standardSpecialIntroduction" colSpan={2} />
                  <NumberField name="standardMaxLoop" />
                </Form>,
              )}
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.setSites`).d('子步骤')} key="subStep">
              <Table dataSet={tableDs} columns={columns} />
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div >
  );
};

export default formatterCollections({
  code: ['tarzan.process.technology', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.OPERATION_DETAIL.BASIC`],
  // @ts-ignore
})(ChildSteps));
