/**
 * @Description: 分层审核方案-service
 * @Author: <EMAIL>
 * @Date: 2023/8/8 20:08
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存分层审核方案
 * @function SaveScheme
 * @returns {object} fetch Promise
 */
export function SaveScheme(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-layer-review-scheme/save/ui`,
    method: 'POST',
  };
}

/**
 * 保存检证项目
 * @function SaveSchemeItem
 * @returns {object} fetch Promise
 */
export function SaveSchemeItem(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-layer-review-scheme/review-item/lov/insert/ui`,
    method: 'POST',
  };
}
