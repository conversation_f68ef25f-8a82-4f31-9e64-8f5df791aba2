// 加工策略配置
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 保存
export function HandleSave() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation-cas`,
    method: 'POST',
  };
}

// 删除
export function HandleDelete() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation-cas`,
    method: 'POST',
  };
}
