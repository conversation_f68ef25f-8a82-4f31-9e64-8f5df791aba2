/**
 * @Description:  检验项目维护-列表页
 * @Author: <EMAIL>
 * @Date: 2023/1/9 14:32
 */
import React, { FC, useEffect, useMemo } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { DataSet, Modal, Table } from 'choerodon-ui/pro';
import { Badge, Tag } from 'choerodon-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import { historyDS, tableDS } from '../stores';

const modelPrompt = 'tarzan.hwms.inspectItemMaintain';
const tenantId = getCurrentOrganizationId();

interface InspectItemListProps extends RouteComponentProps {
  tableDs: DataSet;
  customizeTable: any;
}

const InspectItemList: FC<InspectItemListProps> = ({
  match: { path },
  tableDs,
  history,
  customizeTable,
}) => {
  const historyDs = useMemo(() => new DataSet(historyDS()), []);

  useEffect(() => {
    tableDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_LIST.LIST`,
    );
    tableDs.query(tableDs.currentPage).then(r => r);
  }, []);

  const renderValueList = (name, record) => {
    const value = record.get(name);
    switch (record.get('dataType')) {
      case 'VALUE':
      case 'VALUE_LIST':
      case 'CALCULATE_FORMULA':
        return (
          value?.length &&
          value[0].dataValue &&
          value.map(item => (
            <Tag className="hcm-tag-blue" key={item.dataValue}>
              {item.dataValue}
            </Tag>
          ))
        );
      default:
        return value?.length && value[0].dataValue;
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectItemCode',
        width: 150,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hwms/inspect-item-maintain/dist/${record!.get('inspectItemId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'inspectItemDesc',
        width: 150,
        lock: ColumnLock.left,
      },
      { name: 'inspectItemTypeDesc', width: 120 },
      { name: 'inspectBasis' },
      { name: 'qualityCharacteristicDesc' },
      { name: 'inspectToolDesc' },
      { name: 'inspectMethodDesc' },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      { name: 'enterMethodDesc', width: 150 },
      { name: 'dataTypeDesc' },
      { name: 'uomName' },
      { name: 'decimalNumber' },
      { name: 'processModeDesc' },
      {
        name: 'valueLists',
        width: 200,
        renderer: ({ value }) => {
          if (value) {
            return (
              value?.length &&
              value.map(item => {
                return (
                  <Tag className="hcm-tag-blue" key={item}>
                    {item}
                  </Tag>
                );
              })
            );
          }
        },
      },
      {
        name: 'trueValueList',
        width: 200,
        renderer: ({ record, name }) => renderValueList(name, record),
      },
      {
        name: 'falseValueList',
        width: 200,
        renderer: ({ record, name }) => renderValueList(name, record),
      },
      {
        name: 'warningValueList',
        width: 200,
        renderer: ({ record, name }) => renderValueList(name, record),
      },
      { name: 'samplingMethodDesc' },
      { name: 'sameGroupIdentification' },
      {
        name: 'outsourceFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yse`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'destructiveExperimentFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yse`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'enclosure',
        width: 120,
        lock: ColumnLock.right,
        align: ColumnAlign.center,
      },
      {
        header: intl.get(`${modelPrompt}.operation.history`).d('历史查询'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 120,
        renderer: ({ record }) => (
          <span className="action-link">
            <a onClick={() => handleOpenHistoryDrawer(record)}>
              {intl.get(`${modelPrompt}.operation.history`).d('历史查询')}
            </a>
          </span>
        ),
      },
    ];
  }, []);

  const handleAdd = () => {
    history.push(`/hwms/inspect-item-maintain/dist/create`);
  };

  const historyColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'lastUpdateDate', width: 150, align: ColumnAlign.center, lock: ColumnLock.left },
      { name: 'lastUpdatedByName', lock: ColumnLock.left },
      { name: 'operationTypeDesc' },
      {
        name: 'inspectItemCode',
        width: 150,
      },
      {
        name: 'inspectItemDesc',
        width: 150,
      },
      { name: 'inspectItemTypeDesc', width: 120 },
      { name: 'inspectBasis' },
      { name: 'qualityCharacteristicDesc' },
      { name: 'inspectToolDesc' },
      { name: 'inspectMethodDesc' },
      {
        name: 'requiredFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      { name: 'technicalRequirement' },
      { name: 'remark' },
      { name: 'enterMethodDesc', width: 150 },
      { name: 'dataTypeDesc' },

      { name: 'uomName' },
      { name: 'decimalNumber' },
      { name: 'processModeDesc' },
      {
        name: 'valueLists',
        width: 200,
        renderer: ({ value }) => {
          if (value?.length) {
            return value.map(item => {
              return (
                <Tag className="hcm-tag-blue" key={item}>
                  {item}
                </Tag>
              );
            });
          }
        },
      },
      {
        name: 'trueValueList',
        width: 200,
        renderer: ({ record, name }) => renderValueList(name, record),
      },
      {
        name: 'falseValueList',
        width: 200,
        renderer: ({ record, name }) => renderValueList(name, record),
      },
      {
        name: 'warningValueList',
        width: 200,
        renderer: ({ record, name }) => renderValueList(name, record),
      },
      { name: 'dataQty' },
      { name: 'samplingMethodDesc' },
      { name: 'ncCodeGroupDesc' },
      {
        name: 'employeePosition',
        width: 120,
      },
      {
        name: 'inspectFrequency',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record }) => {
          let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
          if (inspectFrequencyShow) {
            inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
            inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
            return inspectFrequencyShow;
          }
          return value;
        },
      },
      {
        name: 'actionItem',
      },
      { name: 'sameGroupIdentification' },
      {
        name: 'outsourceFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yse`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'destructiveExperimentFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yse`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      { name: 'creationDate', width: 150, align: ColumnAlign.center },
      { name: 'createdByName' },
      {
        name: 'enclosure',
        width: 100,
        lock: ColumnLock.right,
      },
    ];
  }, []);

  const handleOpenHistoryDrawer = record => {
    historyDs.loadData([]);
    historyDs.setQueryParameter('inspectItemId', record.get('inspectItemId'));
    historyDs.query();

    Modal.open({
      ...drawerPropsC7n({
        ds: historyDs,
        canEdit: false,
      }),
      title: intl.get(`${modelPrompt}.title.queryHistory`).d('历史查询'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      children: (
        <>
          {customizeTable(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_LIST.HISTORY`,
            },
            <Table
              dataSet={historyDs}
              columns={historyColumns}
              customizedCode="inspectItemMaintain-drawer"
            />,
          )}
        </>
      ),
    });
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/MT.QMS.INSPECT_ITEM',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('检验项目维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
          permissionList={[
            {
              code: `tarzan${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            filter={record => {
              return record.get('dataType') !== 'CALCULATE_FORMULA';
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="inspectItemMaintain"
            customizedCode="inspectItemMaintain"
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_LIST.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_LIST.LIST`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_ITEM_LIST.HISTORY`,
      ],
    })(InspectItemList as any),
  ),
);
