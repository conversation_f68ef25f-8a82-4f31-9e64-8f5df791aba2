/**
 * @feature 库存初始化
 * @date 2021-9-16
 * <AUTHOR>
 */
import React, { useState } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { useDynamicColumn } from '@components/tarzan-ui';
import { initialDs } from './stories/InitialDs';
import CommonButton from './CommonButton';

const { Column } = Table;

const modelPrompt = 'tarzan.inventory.initial.model';
/**
 * 头行结构的表单示例
 */
const Initial = props => {
  const [deleteFlag, setState] = useState(true);
  const DynamicColumn = useDynamicColumn({ tableName: 'mt_material_lot_attr' });

  // 标识
  const renderFlagColumn = ({ value }) => {
    return (
      (value === 'Y' && intl.get('tarzan.common.label.yes').d('是')) ||
      (value === 'N' && intl.get('tarzan.common.label.no').d('否'))
    );
  };

  const handleChange = () => {
    setState(props.dataSet.selected.length === 0);
  };

  const onFieldEnterDown = () => {
    props.dataSet.query(props.dataSet.currentPage);
  }

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get(`${modelPrompt}.initial.title.list`).d('库存初始化')}
        header={
          <CommonButton dataSet={props.dataSet} deleteFlag={deleteFlag} setState={setState} />
        }
      >
        <Table
          bordered={false}
          dataSet={props.dataSet}
          onChange={handleChange}
          queryBar='filterBar'
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
          searchCode="swkccsh"
          customizedCode="swkccsh"
        >
          <Column name="lineNumber" width={100} />
          <Column name="status" renderer={renderFlagColumn} align="center" />
          <Column name="importMsg" width={250} />
          <Column name="siteCode" width={200} />
          <Column name="identification" width={200} />
          <Column name="materialCode" width={200} />
          <Column name="revisionCode" width={100} />
          <Column name="materialName" width={200} />
          <Column name="locatorCode" width={200} />
          <Column name="primaryUomQty" width={100} align="right" />
          <Column name="lot" width={200} />
          <Column name="qualityStatus" width={100} />
          <Column name="primaryUomCode" width={200} />
          <Column name="secondaryUomCode" width={200} />
          <Column name="secondaryUomQty" width={200} align="right" />
          <Column name="loadTime" width={200} align="center" />
          <Column name="unloadTime" width={200} align="center" />
          <Column name="ownerCode" width={200} />
          <Column name="ownerType" width={150} />
          <Column name="ovenNumber" width={150} />
          <Column name="supplierCode" width={200} />
          <Column name="supplierSiteCode" width={200} />
          <Column name="customerCode" width={200} />
          <Column name="customerSiteCode" width={200} />
          <Column name="createReason" width={200} />
          <Column name="eoNum" width={200} />
          <Column name="inLocatorTime" width={200} align="center" />
          <Column name="freezeFlag" width={100} align="center" />
          <Column name="materialLotStatus" width={150} align="center" />
          <Column name="inSiteTime" width={200} align="center" />
          <Column name="currentContainerCode" width={200} />
          <Column name="productionDate" width={200} align="center" />
          <Column name="expirationDate" width={200} align="center" />
          <Column name="extendedShelfLifeTimes" width={150} align="right" />
          <Column name="supplierLot" width={150} />
          {DynamicColumn}
        </Table>
      </PageHeaderWrapper>
    </div>
  );
};

export default formatterCollections({ code: ['tarzan.inventory.initial.model', 'tarzan.common'] })(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...initialDs(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Initial),
);
