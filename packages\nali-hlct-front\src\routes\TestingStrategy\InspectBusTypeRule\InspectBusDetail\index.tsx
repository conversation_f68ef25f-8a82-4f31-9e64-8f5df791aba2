/**
 * @Description: 检验业务类型规则维护-详情页
 * @Author: <EMAIL>
 * @Date: 2023/1/30 17:38
 */
import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Collapse, Popconfirm } from 'choerodon-ui';
import {
  Button,
  DataSet,
  Form,
  Lov,
  NumberField,
  Select,
  Switch,
  Table,
  TextField,
  Modal,
} from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import UserRightsComponentDrawer from '@/components/UserRightsComponentDrawer';
import { DragColumnAlign } from 'choerodon-ui/pro/es/table/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import { TarzanSpin, AttributeDrawer, drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { getCurrentUser } from 'utils/utils';

import { formDS, lineDS } from '../stores/DetailDS';
import { SaveInspectBusTypeRule } from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.inspectBusTypeRule';

const InspectBusDetail = props => {
  const {
    history,
    match: { path, params },
    custConfig,
    customizeForm,
    customizeTable,
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState(kid === 'create');
  const lineDs = useMemo(() => new DataSet(lineDS()), [kid]);
  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
        children: { lines: lineDs },
      }),
    [kid],
  );
  const { run: saveInspectBusTypeRule, loading: saveLoading } = useRequest(
    SaveInspectBusTypeRule(),
    { manual: true },
  );

  useEffect(() => {
    if (kid === 'create') {
      getUserInfo();
      return;
    }
    // 编辑时
    handleQuery(kid);
  }, [kid]);

  const getUserInfo = async () => {
    const userLov = await getCurrentUser();
    formDs.current?.set('userLov', userLov);
  };

  const handleQuery = async id => {
    formDs.setQueryParameter('inspectBusinessTypeRuleId', id);
    formDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_DETAIL.DTL.BASIC`,
    );
    await formDs.query();
    getUserInfo();
  };

  const handleUpdateLine = (record, name) => {
    if (record.get(name) === 'N') {
      return;
    }
    // 工序、工位、产线、区域、供应商、客户六个按钮只能同时存在一个开启
    // 工艺、供应商、客户三个按钮只能同时存在一个开启
    // 设备、供应商、客户三个按钮只能同时存在一个开启
    switch (name) {
      case 'operationFlag':
        validateOperationFlag(record, name);
        break;
      case 'equipmentFlag':
        validateEquipmentFlag(record, name);
        break;
      case 'supplierFlag':
      case 'customerFlag':
        validateOperationFlag(record, name);
        validateWorkcellFlag(record, name);
        validateEquipmentFlag(record, name);
        break;
      default:
        validateWorkcellFlag(record, name);
    }
  };

  const validateOperationFlag = (record, name) => {
    const operationList = ['operationFlag', 'supplierFlag', 'customerFlag'];
    let _operationCount = 0;
    operationList.forEach(item => {
      if (record.get(item) === 'Y') {
        _operationCount++;
      }
    });
    if (_operationCount > 1) {
      record.set(name, 'N');
      notification.warning({
        message: intl
          .get(`${modelPrompt}.warning.operation`)
          .d('工艺、供应商、客户三个按钮只能同时存在一个开启，请检查！'),
      });
    }
  };

  const validateWorkcellFlag = (record, name) => {
    const workcellList = [
      'processWorkcellFlag',
      'stationWorkcellFlag',
      'prodLineFlag',
      'areaFlag',
      'supplierFlag',
      'customerFlag',
    ];
    let _workcellCount = 0;
    workcellList.forEach(item => {
      if (record.get(item) === 'Y') {
        _workcellCount++;
      }
    });
    if (_workcellCount > 1) {
      record.set(name, 'N');
      notification.warning({
        message: intl
          .get(`${modelPrompt}.warning.workCell`)
          .d('工序、工位、产线、区域、供应商、客户六个按钮只能同时存在一个开启，请检查！'),
      });
    }
  };

  const validateEquipmentFlag = (record, name) => {
    const operationList = ['equipmentFlag', 'supplierFlag', 'customerFlag'];
    let _operationCount = 0;
    operationList.forEach(item => {
      if (record.get(item) === 'Y') {
        _operationCount++;
      }
    });
    if (_operationCount > 1) {
      record.set(name, 'N');
      notification.warning({
        message: intl
          .get(`${modelPrompt}.warning.equipment`)
          .d('设备、供应商、客户三个按钮只能同时存在一个开启，请检查！'),
      });
    }
  };

  const handleEdit = () => {
    setCanEdit(true);
  };

  const handleCancel = () => {
    if (kid === 'create') {
      history.push('/hwms/inspect-business-type-rule/list');
    } else {
      setCanEdit(false);
      handleQuery(kid);
    }
  };

  const handleValidate = async () => {
    const formRes = await formDs.validate();
    const lineRes = await lineDs.validate();
    return formRes && lineRes;
  };

  const handleSave = async (createFlag: boolean) => {
    const validateFlag = await handleValidate();
    if (!validateFlag) {
      return false;
    }
    // 校验所有的维度必须至少开启一个，否则报错
    const lineData = lineDs.toData();
    let lineWarning = false;
    lineData.forEach(item => {
      const fieldArr = [
        'materialFlag',
        'materialCategoryFlag',
        'operationFlag',
        'processWorkcellFlag',
        'stationWorkcellFlag',
        'equipmentFlag',
        'prodLineFlag',
        'areaFlag',
        'supplierFlag',
        'customerFlag',
      ];
      const recordFlag = fieldArr.find(i => item[i] === 'Y');
      lineWarning = Boolean(!recordFlag);
    });
    if (lineWarning) {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.warning.lineValidate`)
          .d('所有的检验维度至少开启一个，请检查！'),
      });
      return false;
    }
    const data = formDs.current?.toData();
    saveInspectBusTypeRule({
      params: {
        header: {
          ...data,
          lines: undefined,
        },
        lines: lineDs.toData(),
      },
      onSuccess: res => {
        notification.success({});
        if (createFlag) {
          if (kid === 'create') {
            formDs.reset();
            getUserInfo();
          } else {
            history.push(`/hwms/inspect-business-type-rule/dist/create`);
          }
        } else {
          setCanEdit(false);
          history.push(`/hwms/inspect-business-type-rule/dist/${res}`);
          handleQuery(res);
        }
      },
    });
  };

  const handleAddLine = () => {
    let _maxSeq = 0;
    lineDs.forEach((_record: any) => {
      if (_record.get('priority') > _maxSeq) {
        _maxSeq = _record.get('priority');
      }
    });
    lineDs.create({
      priority: Math.floor(_maxSeq / 10) * 10 + 10,
    });
  };

  const handleDragEnd = () => {
    lineDs.forEach((record, index) => {
      record.set('priority', 10 + index * 10);
    });
  };

  const deleteRecord = record => lineDs.remove(record);

  const lineColumns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <PermissionButton
            type="c7n-pro"
            icon="add"
            disabled={!canEdit}
            onClick={handleAddLine}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        ),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteRecord(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              disabled={!canEdit}
              funcType="flat"
              shape="circle"
              size="small"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'priority',
        align: ColumnAlign.left,
        width: 120,
        editor: canEdit && <NumberField />,
      },
      {
        name: 'materialFlag',
        // width: 100,
        align: ColumnAlign.center,
        editor: canEdit && <Switch />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'materialCategoryFlag',
        align: ColumnAlign.center,
        editor: canEdit && <Switch />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'operationFlag',
        // width: 100,
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <Switch onChange={() => handleUpdateLine(record, 'operationFlag')} />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'processWorkcellFlag',
        // width: 100,
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <Switch onChange={() => handleUpdateLine(record, 'processWorkcellFlag')} />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'stationWorkcellFlag',
        // width: 100,
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <Switch onChange={() => handleUpdateLine(record, 'stationWorkcellFlag')} />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'equipmentFlag',
        // width: 100,
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <Switch onChange={() => handleUpdateLine(record, 'equipmentFlag')} />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'prodLineFlag',
        // width: 100,
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <Switch onChange={() => handleUpdateLine(record, 'prodLineFlag')} />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'areaFlag',
        // width: 100,
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <Switch onChange={() => handleUpdateLine(record, 'areaFlag')} />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'supplierFlag',
        // width: 100,
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <Switch onChange={() => handleUpdateLine(record, 'supplierFlag')} />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'customerFlag',
        // width: 100,
        align: ColumnAlign.center,
        editor: record =>
          canEdit && <Switch onChange={() => handleUpdateLine(record, 'customerFlag')} />,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
    ],
    [canEdit],
  );

  const handleReviewTypeChange = val => {
    if (['NO_REVIEW', 'MANUAL_REVIEW', 'OFFLINE_REVIEW'].includes(val)) {
      formDs.current!.set('autoCreateNcReportFlag', 'N');
    }
    if (val === 'AUTO_REVIEW') {
      formDs.current!.set('autoCreateNcReportFlag', 'Y');
    }
    // handleUpdateAutoFlag();
  };

  // const handleUpdateAutoFlag = () => {
  //   const { reviewType, taskSplitMethod } = formDs?.current?.toData();
  //   if (reviewType === 'NO_REVIEW' && taskSplitMethod === 'SPLIT_BY_GROUP') {
  //     formDs?.current?.set('docCompleteFlag', 'N');
  //   }
  // };

  const handleResetDimension = () => {
    formDs?.current?.set('inspectNcRecordDimension', null);
  };

  const addUserSitePermission = (_modal, tableDs) => {
    const { userLov } = tableDs.toData()[0];
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.addUserSitePermission`).d('新增用户站点权限'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      cancelText: intl.get(`tarzan.common.button.close`).d('关闭'),
      onClose: () => {
        _modal.props.children.props.dataSet.query();
      },
      children: <UserRightsComponentDrawer userInfo={userLov} />,
    });
  };

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={formDs} spinning={saveLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.list`).d('检验业务类型规则维护')}
          backPath="/hwms/inspect-business-type-rule/list"
        >
          {canEdit ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={() => handleSave(false)}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button icon="save" onClick={() => handleSave(true)}>
                {intl.get(`${modelPrompt}.button.saveAndCreate`).d('保存并创建下一条')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              // permissionList={[
              //   {
              //     code: `${path}.button.edit`,
              //     type: 'button',
              //     meaning: '详情页-编辑新建删除复制按钮',
              //   },
              // ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
          <AttributeDrawer
            serverCode={BASIC.TARZAN_SAMPLING}
            className="org.tarzan.qms.domain.entity.MtInspectItem"
            kid={kid}
            canEdit={canEdit}
            disabled={kid === 'create'}
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_DETAIL.ATTR`}
            custConfig={custConfig}
          />
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'ruleDetail', 'basicInfo1']}>
            <Panel
              key="basicInfo"
              header={intl.get(`${modelPrompt}.title.basicInfo`).d('基础信息')}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_DETAIL.BASIC`,
                },
                <Form dataSet={formDs} columns={3} disabled={!canEdit} labelWidth={200}>
                  <TextField name="inspectBusinessType" />
                  <TextField name="inspectBusinessTypeDesc" />
                  <Lov
                    name="siteLov"
                    noCache
                    modalProps={{
                      footer: (okBtn, cancelBtn, modal) => {
                        return [
                          <Button
                            onClick={() => {
                              addUserSitePermission(modal, formDs);
                            }}
                          >
                            {intl.get(`${modelPrompt}.addUserSitePermission`).d('新增用户站点权限')}
                          </Button>,
                          cancelBtn,
                          okBtn,
                        ];
                      },
                    }}
                    tableProps={{
                      queryFieldsLimit: 10,
                    }}
                  />
                  <Select name="operatingMode" />
                  <Select name="reviewType" onChange={handleReviewTypeChange} />
                  <TextField name="workFlowCode" />
                  <Switch name="recheckFlag" />
                  <Select name="recheckTaskCreateNode" />
                  <Select name="recheckCreateItem" />
                  <Select name="recheckResultDimension" onChange={handleResetDimension} />
                  <Select name="taskAssignType" />
                  <Select name="recheckTaskAssignType" />
                  {/* <Switch name="docCompleteFlag" /> */}
                  <Switch name="blankInspectOrderFlag" />
                  <Select name="blankInspectRule" />
                  <Switch name="mergeFlag" newLine />
                  <Select
                    name="mergeDimension"
                    onOption={({ record }) => ({
                      disabled: record.get('value') === 'INSPECT_OBJECT',
                    })}
                  />
                  {/* <Select name="resultDimension" onChange={handleResetDimension} /> */}
                  <Select name="resultDimension" />
                  <Switch name="transferFlag" />
                  <Select name="transferDimension" />
                  <Lov name="transferRuleLov" />
                  {/* <Select name="taskSplitMethod" onChange={handleUpdateAutoFlag} /> */}
                  <Select name="taskSplitMethod" />
                  <Switch name="ncCodeFlag" />
                  <Select name="ngAssignRuleType" />
                  <Select name="scrapAssignRuleType" />
                  <Lov name="dispositionGroupLov" />
                  <Switch name="autoCreateNcReportFlag" />
                  <Select name="ncReportType" />
                  <Select name="ncReportRule" />
                  <Select
                    name="inspectNcRecordDimension"
                    onOption={({ record }) => {
                      let recheckResultDimension;
                      let resultDimension;
                      if (formDs?.current) {
                        recheckResultDimension = formDs.current.get('recheckResultDimension');
                        resultDimension = formDs.current.get('resultDimension');
                      }
                      return {
                        disabled:
                          ((recheckResultDimension &&
                            recheckResultDimension !== 'RECORD_SAMPLE_VALUE') ||
                            (resultDimension && resultDimension !== 'RECORD_SAMPLE_VALUE')) &&
                          ['OBJECT_NC', 'OBJECT_ITEM_NC'].includes(record.get('value')),
                      };
                    }}
                  />
                  <Lov name="actualInspectBusinessTypeLov" />
                  <Select name="operatingEnd" />
                  <Select name="templateType" />
                  <TextField name="printTemplate" />
                  <Lov name="messageLov" />
                  <Lov name="receiverTypeLov" />
                  <Switch name="enableFlag" />
                  <Switch name="oneselfFlag" />
                  <Switch name="enterFlag" />
                  <TextField name="remark" />
                  <Switch name="deleteDecreaseRecordBox" />
                </Form>,
              )}
            </Panel>
            <Panel
              key="basicInfo1"
              header={intl.get(`${modelPrompt}.title.basicInfo1`).d('检验任务超时提醒规则')}
            >
              <Form dataSet={formDs} columns={3} disabled={!canEdit} labelWidth={200}>
                <NumberField name="taskOpen" addonAfter="分钟" />
                <NumberField name="taskAccept" addonAfter="分钟" />
                <NumberField name="taskDoing" addonAfter="分钟" />
                <NumberField name="taskComplete" addonAfter="分钟" />
              </Form>
            </Panel>
            <Panel
              key="ruleDetail"
              header={intl.get(`${modelPrompt}.title.ruleDetail`).d('检验业务类型规则明细')}
            >
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_DETAIL.DTL`,
                },
                <Table
                  rowDraggable={canEdit}
                  dragColumnAlign={DragColumnAlign.left}
                  onDragEnd={handleDragEnd}
                  dataSet={lineDs}
                  columns={lineColumns}
                />,
              )}
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_DETAIL.BASIC`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_DETAIL.DTL`,
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_DETAIL.ATTR`,
    ],
  })(InspectBusDetail as any),
);
