/**
 * @Description: 分层审核方案-主界面
 * @Author: <EMAIL>
 * @Date: 2023/8/17 11:22
 */
import React, { FC, useCallback, useMemo } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { Badge, Collapse, Tag } from 'choerodon-ui';
import { Button, CheckBox, DataSet, Form, Table } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { headDS, lineDS } from '../stores';

const modelPrompt = 'tarzan.layerReview.layerReviewScheme';
const { Panel } = Collapse;

interface LayerReviewSchemeProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
}

const LayerReviewSchemeList: FC<LayerReviewSchemeProps> = props => {
  const { headDs, lineDs, history } = props;

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('layerReviewSchemeId'));
    } else {
      queryLineTable(null);
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = layerReviewSchemeId => {
    if (layerReviewSchemeId) {
      lineDs.setQueryParameter('layerReviewSchemeId', layerReviewSchemeId);
    } else {
      lineDs.setQueryParameter('layerReviewSchemeId', 0);
    }
    lineDs.query();
  };

  const renderStatusTag = (value, record, name) => {
    if (!value) {
      return;
    }
    let className;
    switch (value) {
      case 'NEW':
        className = 'blue';
        break;
      case 'PUBLISHED':
        className = 'green';
        break;
      case 'REVIEWING':
        className = 'orange';
        break;
      case 'EMERGENCY_PUBLISHED':
        className = 'red';
        break;
      case 'AMENDING':
        className = 'magenta';
        break;
      default:
        className = 'gray';
    }
    return <Tag color={className}>{record!.getField(name)!.getText()}</Tag>;
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'layerReviewSchemeCode',
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/layer-review/layer-review-scheme/dist/${record!.get(
                    'layerReviewSchemeId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'operationName' },
      {
        name: 'layerReviewSchemeStatus',
        renderer: ({ value, record }) => renderStatusTag(value, record, 'layerReviewSchemeStatus'),
      },
      { name: 'layerReviewSchemeVersion' },
      {
        name: 'currentFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          if (!value) {
            return;
          }
          return (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          );
        },
      },
    ];
  }, []);

  const lineColumns: any = useMemo(
    () => [
      { name: 'layerReviewItemCode', width: 180, lock: ColumnLock.left },
      { name: 'projectName' },
      { name: 'projectClassify' },
      { name: 'projectFrom' },
      {
        name: 'level',
        align: ColumnAlign.center,
        width: 400,
        renderer: ({ record }) => (
          <Form record={record} useColon={false} labelWidth={40} columns={4}>
            <CheckBox name="levelL1Flay" />
            <CheckBox name="levelL2Flay" />
            <CheckBox name="levelL3Flay" />
            <CheckBox name="levelL4Flay" />
          </Form>
        ),
      },
      {
        name: 'layerReviewItemFrequency',
      },
      {
        name: 'emergencyFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record }) => {
          if (!value) {
            return;
          }
          return <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={record!.getField('emergencyFlag')!.getText()}
          />
        }
      },
    ],
    [],
  );

  const handleAdd = useCallback(() => {
    history.push(`/hwms/layer-review/layer-review-scheme/dist/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('分层审核方案')}>
        <Button color={ButtonColor.primary} icon="add" onClick={handleAdd}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headDs}
          columns={headColumns}
          searchCode="layerReviewScheme1"
          customizedCode="layerReviewScheme-listHeader"
          onRow={({ record }) => ({
            onClick: () => queryLineTable(record?.get('layerReviewSchemeId')),
          })}
        />
        <Collapse bordered={false} defaultActiveKey={['line']}>
          <Panel key="line" header={intl.get(`${modelPrompt}.title.line`).d('分层审核项目')}>
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="layerReviewScheme-listLine"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(LayerReviewSchemeList as any),
);
