/**
 * 设备/资产Lov
 *
 * 客户化参数如下:
 * @param {Object} queryParams lov查询参数
 * @param {Boolean} disabled 禁用
 * @param {Function} onOk 确认时的操作
 * @param {Number} actId 标准作业id - 根据该值控制 查询条件‘仅展示与标准作业相关’的默认值
 *
 */

import React, { Component } from 'react';
import { Bind } from 'lodash-decorators';
import { Modal, DataSet, Form, Table, TextField, Lov, Select, Button } from 'choerodon-ui/pro';
import { Icon } from 'choerodon-ui';
import { isFunction, omit } from 'lodash';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_ATN } from 'alm/utils/config';

import getLangs from './Langs';
import styles from './index.module.less';

const organizationId = getCurrentOrganizationId();

const queryUrl = `${HALM_ATN}/v1/${organizationId}/asset-info`;
const queryUrlReceipt = `${HALM_ATN}/v1/${organizationId}/asset-info/for-receipt`;

const modalKey = Modal.key();
export default class AssetLov extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  /**
   * 点击搜索图标
   */
  @Bind()
  async handleClickSearchBtn() {
    const { disabled } = this.props;
    if (disabled) return;
    this.handleOpenModal();
  }

  /**
   * 打开Modal
   */
  @Bind()
  handleOpenModal() {
    const { queryParams = {}, actId, isLimited } = this.props;
    const formDs = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'assetDesc',
          type: 'string',
          label: getLangs('ASSET_DESC'),
        },
        {
          name: 'visualLabel',
          type: 'string',
          label: getLangs('VISUAL_LABEL'),
        },
        {
          name: 'locationLov',
          type: 'object',
          label: getLangs('ASSET_LOCATION'),
          lovCode: 'AMDM.LOCATIONS',
          lovPara: {
            tenantId: organizationId,
          },
        },
        {
          name: 'assetLocationId',
          type: 'number',
          bind: 'locationLov.assetLocationId',
        },
        {
          name: 'onlyRelateAct',
          type: 'number',
          label: getLangs('ONLY_ACT'),
          defaultValue: actId ? 1 : 0, // 如果标准作业有值的话 默认1 否则0
          trueValue: 1,
          falseValue: 0,
          lookupCode: 'HPFM.FLAG',
        },
      ],
    });
    const tableDs = new DataSet({
      autoQuery: true,
      primaryKey: 'assetId',
      selection: 'single',

      fields: [
        {
          name: 'assetDesc',
          type: 'string',
          label: getLangs('ASSET_DESC'),
        },
        {
          name: 'visualLabel',
          type: 'string',
          label: getLangs('VISUAL_LABEL'),
        },
        {
          name: 'assetLocationName',
          type: 'string',
          label: getLangs('ASSET_LOCATION'),
        },
      ],
      transport: {
        read: ({ params }) => {
          const formData = formDs.current.toData();
          let formParams = omit(formData, ['locationLov', '__dirty', 'onlyRelateAct']);
          // 仅展示与标准作业相关 值为1时 传actId 为0时不传
          if (formData.onlyRelateAct) {
            formParams = {
              ...formParams,
              relActId: actId,
            };
          }

          if (formParams?.visualLabel) {
            formParams.visualLabelLike = formParams.visualLabel;
            delete formParams.visualLabel;
          }

          return {
            params: {
              ...queryParams, // 代码中写入的条件
              ...params, // 分页信息
              ...formParams, // 表单里的查询条件
              aclFlag: 1,
            },
            url: isLimited ? queryUrlReceipt : queryUrl,
            method: 'GET',
          };
        },
      },
    });
    const modalColumns = [
      {
        name: 'assetDesc',
        width: 250,
      },
      {
        name: 'visualLabel',
        width: 200,
      },
      {
        name: 'assetLocationName',
        width: 250,
      },
    ];
    const modal = Modal.open({
      key: modalKey,
      destroyOnClose: true,
      closable: true,
      style: {
        width: 900,
      },
      title: getLangs('CHOOSE_ASSET'),
      children: (
        <React.Fragment>
          <div className={styles['query-box']}>
            <div className={styles['query-form']}>
              <Form dataSet={formDs} columns={2}>
                <TextField name="assetDesc" />
                <TextField name="visualLabel" />
                <Lov name="locationLov" />
                <Select name="onlyRelateAct" />
              </Form>
            </div>
            <div className={styles['query-buttons']}>
              <Button onClick={() => formDs.reset()}>{getLangs('RESET')}</Button>
              <Button color="primary" onClick={() => tableDs.query()}>
                {getLangs('SEARCH')}
              </Button>
            </div>
          </div>
          <Table
            alwaysShowRowBox
            onDoubleClick={() => this.onDoubleClick(tableDs, modal, this.props.onOk)}
            dataSet={tableDs}
            columns={modalColumns}
          />
        </React.Fragment>
      ),
      onOk: () => {
        if (tableDs.selected.length > 0) {
          const { onOk } = this.props;
          if (isFunction(onOk)) {
            const record = tableDs.selected[0].toData();
            onOk(record);
          }
        } else {
          notification.error({
            message: getLangs('NO_SELECT'),
          });
          return false;
        }
      },
    });
  }

  onDoubleClick(tableDs, modal, onOk) {
    if (tableDs.current) {
      const record = tableDs.current.toData();
      onOk(record);
      modal.close();
    }
  }

  @Bind()
  handleChangeText(value) {
    const { onOk } = this.props;
    // 点击清除按钮时 清空值
    if (value === null) {
      onOk({});
    }
  }

  render() {
    const { name, disabled = false, newLine } = this.props;
    const suffix = (
      <Icon
        type="search"
        onClick={this.handleClickSearchBtn}
        style={{
          fontSize: 14,
          color: '#333',
          height: 'auto',
        }}
        // className={styles['c7n-pro-input-suffix']}
      />
    );
    return (
      <React.Fragment>
        <TextField
          name={name}
          clearButton
          suffix={suffix}
          style={{ width: '100%' }}
          disabled={disabled}
          onChange={this.handleChangeText}
          newLine={newLine}
        />
      </React.Fragment>
    );
  }
}
