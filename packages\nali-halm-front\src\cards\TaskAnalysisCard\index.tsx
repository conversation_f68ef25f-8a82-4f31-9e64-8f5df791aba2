import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Collapse } from 'choerodon-ui';
import { DataSet, Select } from 'choerodon-ui/pro/lib';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import ReactEchartsCore from 'echarts-for-react/lib/core';
import axios from 'axios';
import { HALM_ATN } from 'alm/utils/config';
import queryDS from './Stores';
import styles from './index.module.less';

const organizationId = getCurrentOrganizationId();
const url = `${HALM_ATN}/v1/${organizationId}/table-cards/task-analysis`;

const TaskAnalysisCard = () => {
  const [data, setData] = useState<any>(undefined);
  const [approvedCount, setApprovedCount] = useState<number>(0);
  const [ongoingCount, setOngoingCount] = useState<number>(0);
  const [completedCount, setCompletedCount] = useState<number>(0);
  useEffect(() => {
    fetchData();
  }, []);

  const queryDs = useMemo(() => {
    return new DataSet(queryDS());
  }, []);

  const fetchData = useCallback(async () => {
    const dateUnitCode = queryDs?.current?.get('dateUnitCode');
    const res = await axios.get<any, any>(url, {
      params: queryDs?.current?.data,
    });
    setData(res.detailDTOS);
    if (dateUnitCode === 'DAY') {
      const len = res.detailDTOS.length;
      setApprovedCount(res.detailDTOS[len - 1].approvedCount);
      setOngoingCount(res.detailDTOS[len - 1].ongoingCount);
      setCompletedCount(res.detailDTOS[len - 1].completedCount);
    } else {
      setApprovedCount(res.approvedCount);
      setOngoingCount(res.ongoingCount);
      setCompletedCount(res.completedCount);
    }
  }, []);

  const renderTooltip = useCallback(params => {
    const line1 = `<span style="color:#282828;font-weight:bold;margin:0 20px">完成率</span><br/>`;
    const percentText = `<span style="color:#3B87F5;margin: 8px 0"> ${params[0].value}%</span>`;
    return line1 + percentText;
  }, []);

  const colorRenderer = useCallback(({ text }) => {
    return <span style={{ color: '#666' }}>{text}</span>;
  }, []);

  const option = useMemo(() => {
    return {
      grid: {
        top: 10,
        left: 40,
        right: 0,
      },
      legend: {
        show: true,
        right: 0,
        itemWidth: 10,
        itemHeight: 10,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#FFF',
        extraCssText: 'box-shadow: 0 2px 10px 5px rgba(124,133,155,0.10);text-align: center;',
        axisPointer: {
          type: 'none',
        },
        formatter: params => renderTooltip(params),
      },
      xAxis: [
        {
          type: 'category',
          axisTick: {
            alignWithLabel: true,
          },
          data: data?.map(i => i.dt),
          axisLabel: {
            color: '#A9A9A9',
          },
          axisLine: {
            lineStyle: {
              color: '#CCCCCC',
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          minInterval: 1,
          max: 100,
          axisTick: { show: false },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            color: '#A9A9A9',
            formatter: '{value}%',
          },
        },
      ],
      series: [
        {
          type: 'line',
          itemStyle: {
            color: '#4C77FE',
          },
          data: data?.map(i => i.taskPercent),
        },
      ],
      dataZoom: data?.length > 7 && {
        realtime: true,
        height: 10,
        start: 0,
        end: 20,
      },
    };
  }, [data]);
  return (
    <div className={styles.container}>
      <Collapse
        bordered={false}
        expandIconPosition="right"
        defaultActiveKey={['A', 'B']}
        trigger="icon"
        className={styles['customize-collapse']}
      >
        <Collapse.Panel key="A" showArrow={false} header="任务统计">
          <div className={styles['query-bar']}>
            <Select
              dataSet={queryDs}
              name="dateUnitCode"
              clearButton={false}
              onChange={fetchData}
              renderer={colorRenderer}
            />
          </div>
          <div className={styles['statistic-container']}>
            <div className={styles['statistic-item']}>
              <h1>已完成</h1>
              <p className={styles.completed}>{completedCount}</p>
            </div>
            <div className={styles['statistic-item']}>
              <h1>未完成</h1>
              <p className={styles.ongoing}>{ongoingCount}</p>
            </div>
            <div className={styles['statistic-item']}>
              <h1>待处理</h1>
              <p className={styles.approved}>{approvedCount}</p>
            </div>
          </div>
        </Collapse.Panel>
        <h3 className={styles['chart-title']}>任务完成率</h3>
        <ReactEchartsCore
          echarts={echarts}
          option={option}
          notMerge
          lazyUpdate
          style={{
            height: '100%',
          }}
        />
      </Collapse>
    </div>
  );
};
export default TaskAnalysisCard;
