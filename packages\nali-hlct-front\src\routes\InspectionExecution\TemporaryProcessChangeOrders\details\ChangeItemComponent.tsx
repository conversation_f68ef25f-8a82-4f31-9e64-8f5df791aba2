/*
 * @Description: 工程暂允-修改信息项
 * @Author: <<EMAIL>>
 * @Date: 2023-09-26 14:09:05
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2023-09-26 14:48:26
 */
import React, { useMemo } from 'react';
import { Tabs, Table } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TabsType } from 'choerodon-ui/lib/tabs/enum';

const { TabPane } = Tabs;
const modelPrompt = `tarzan.qms.temporaryProcessChangeOrders`;

export default ({ changeItemAddDs, changeItemDeleteDs, changeItemModifyDs }) => {
  const getDataValueShow = (record, name) => {
    const _dataType = record?.get('dataType');
    const _valueData = record?.get(name) || [];
    const _dataShow = _valueData.length > 0 ? _valueData[0].dataValue : '';
    return ['CALCULATE_FORMULA', 'VALUE', 'VALUE_LIST'].includes(_dataType)
      ? _valueData.map(item => <Tag>{item.dataValue}</Tag>)
      : _dataShow;
  };

  const initColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'inspectBusinessTypeDesc' },
      { name: 'operationName' },
      { name: 'equipmentName' },
      { name: 'inspectItemCode' },
      { name: 'inspectItemDesc' },
      {
        name: 'trueValueList',
        width: 200,
        renderer: ({ record }) => getDataValueShow(record, 'trueValueList'),
      },
      {
        name: 'inspectFrequency',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record }) => {
          let inspectFrequencyShow = record!.getField('inspectFrequency')!.getText();
          if (inspectFrequencyShow) {
            inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
            inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
            return inspectFrequencyShow;
          }
          return value;
        },
      },
    ],
    [],
  );

  const changeInfoColumn: ColumnProps[] = useMemo(() => [{ name: 'changeInfo' }], []);

  return (
    <Tabs type={TabsType.card}>
      <TabPane key="add" tab={intl.get(`${modelPrompt}.detail.tab.addChangeItem`).d('新增')}>
        <Table dataSet={changeItemAddDs} columns={initColumns} />
      </TabPane>
      <TabPane key="modify" tab={intl.get(`${modelPrompt}.detail.tab.modifyChangeItem`).d('修改')}>
        <Table dataSet={changeItemModifyDs} columns={[...changeInfoColumn, ...initColumns]} />
      </TabPane>
      <TabPane key="delete" tab={intl.get(`${modelPrompt}.detail.tab.deleteChangeItem`).d('删除')}>
        <Table dataSet={changeItemDeleteDs} columns={initColumns} />
      </TabPane>
    </Tabs>
  );
};
