/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-26 15:35:22
 * @LastEditTime: 2023-08-02 18:18:48
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    // {
    //   // 工单Lov的查询参数
    //   name: 'workOrderLovLimit',
    // },
    // {
    //   name: 'workOrderLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.workOrderLov`).d('生产工单'),
    //   // lovCode: 'HME.WORK_ORDER',
    //   lovCode: 'HME.REPORT_WO_LOV',
    //   textField: 'workOrderNum',
    //   valueField: 'workOrderId',
    //   lovPara: {
    //     tenantId,
    //   },
    //   computedProps: {
    //     lovPara: ({ record }) => {
    //       return {
    //         tenantId,
    //         ...record.get('workOrderLovLimit'),
    //       };
    //     },
    //   },
    // },
    // {
    //   name: 'workOrderId',
    //   type: FieldType.number,
    //   bind: 'workOrderLov.workOrderId',
    // },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      // bind: 'workOrderLov.workOrderNum',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产工单'),
    },
    {
      name: 'materialLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLot`).d('物料批'),
      dynamicProps: {
        disabled({ record }) {
          return !record.get('workOrderId');
        },
      },
    },
    {
      name: 'barCode',
    },
    {
      name: 'qtyInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qtyInfo`).d('完工进度'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户信息'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('工单备注'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('当前工序'),
    },
    {
      name: 'lastCompletedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastCompletedQty`).d('本班次当前工单本工序作业数量'),
    },
  ],
});

const processDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'materialLotId',
    },
    {
      name: 'requestTypeCode',
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('扫描条码'),
    },
    {
      name: 'barCode',
      label: intl.get(`${modelPrompt}.barCode`).d('条码'),
      required: true,
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
      dynamicProps: {
        disabled({ record }) {
          return !record.get('barCode');
        },
        max: ({ record }) => {
          return record.get('maxQty');
        },
      },
      min: 1,
      step: 1,
    },
    {
      name: 'maxQty',
      label: intl.get(`${modelPrompt}.barCode`).d('条码数量'),
    },
  ],
});

const stepDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'routerStep',
      label: intl.get(`${modelPrompt}.routerStep`).d('工序'),
    },
  ],
});

const printDS: () => DataSetProps = () => ({
  selection: DataSetSelection.multiple,
  autoQuery: false,
  autoCreate: true,
  paging: true,
  primaryKey: 'materialLotId',
  // cacheSelection: true,
  // cacheModified: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'materialLotId',
    },
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.identification`).d('条码'),
    },
    {
      name: 'primaryUomQty',
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'qualityStatusDesc',
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'printTimes',
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'printStatus',
      label: intl.get(`${modelPrompt}.printStatus`).d('打印状态'),
    },
    {
      name: 'productionDate',
      label: intl.get(`${modelPrompt}.productionDate`).d('更新日期'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-wo-report/print/list/get`,
        method: 'GET',
      };
    },
  },
  // data: [
  //   {
  //     materialLotId: 1,
  //     materialLotCode: '457670',
  //     primaryUomQty: 1,
  //     materialLotStatus: '待上架',
  //     qualityStatus: '合格',
  //     printTimes: '2',
  //     printStatus: '已打印',
  //     productionDate: '12.22 7:45:41',
  //   },
  //   {
  //     materialLotId: 2,
  //     materialLotCode: '457670',
  //     primaryUomQty: 1,
  //     materialLotStatus: '待上架',
  //     qualityStatus: '合格',
  //     printTimes: '2',
  //     printStatus: '已打印',
  //     productionDate: '12.22 7:45:41',
  //   },
  //   {
  //     materialLotId: 3,
  //     materialLotCode: '457670',
  //     primaryUomQty: 1,
  //     materialLotStatus: '待上架',
  //     qualityStatus: '合格',
  //     printTimes: '2',
  //     printStatus: '未打印',
  //     productionDate: '12.22 7:45:41',
  //   },
  // ],
});

const materialDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  fields: [
    {
      name: 'materialLotId',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码'),
    },
    {
      name: 'scanQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.scanQty`).d('数量'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('materialLotId');
        },
      },
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'materialCodeInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCodeInfo`).d('物料/版本'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'qtyInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qtyInfo`).d('数量'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locator`).d('货位'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('质量状态'),
    },
    {
      name: 'substituteMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.substituteMaterialCode`).d('物料批编码'),
    },
  ],
});

const workOrderLovDS: () => DataSetProps = () => ({
  selection: DataSetSelection.single,
  autoQuery: false,
  autoCreate: false,
  paging: true,
  dataKey: 'content',
  queryFields: [
    {
      name: 'workOrderNum',
      labelWidth: 100,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'materialInfo',
      labelWidth: 150,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialInfo`).d('物料编码/版本'),
    }
  ],
  fields: [
    {
      name: 'materialId',
    },
    {
      name: 'materialInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialInfo`).d('物料编码/版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'planTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planTime`).d('计划开始时间-结束时间'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'woRenderQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woRenderQty`).d('完工/工单数量'),
    },
    {
      name: 'woCompletedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woCompletedQty`).d('工单完工数量'),
    },
    {
      name: 'woQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woQty`).d('工单数量'),
    },
    {
      name: 'workOrderId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-wo-report/order/lov`,
        method: 'POST',
      };
    }
  },
});

export { detailDS, processDS, printDS, stepDS, materialDS, workOrderLovDS };
