{"name": "key-hmes-front", "version": "1.0.0", "license": "MIT", "scripts": {"start": "cross-env UMI_ENV=dev UMI_DEV_ENV=TRUE node ../../node_modules/umi/bin/umi.js dev", "build": "cross-env UMI_ENV=dev node ../../node_modules/umi/bin/umi.js hzero-build", "build:ms": "cross-env BUILD_SINGLE_PUBLIC_MS=true node ../../node_modules/umi/bin/umi.js hzero-build", "build:app": "cross-env BUILD_SINGLE_PUBLIC_MS=true node ../../node_modules/umi/bin/umi.js hzero-build --only-build-parent", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "dependencies": {"bizcharts": "^3.5.8", "bizcharts-dnd-3.5.8": "npm:bizcharts@3.5.8"}, "files": ["lib", "config/config.ts", "!lib/config/alias.js", "!lib/config/babel-config.js", "!.umi", "!lib/config/.env.*", "!lib/config/alias.js", "!lib/config/alias.js", "!lib/config/theme.js", "!lib/config/babel-config.js", "!.umi-production"], "peerDependencies": {"@hzero-front-ui/cfg": "*", "axios": "*", "choerodon-ui": "*", "dva": "*", "hzero-front": "*", "hzero-ui": "*", "mobx": "*", "react": "*", "react-dom": "*", "react-intl-universal": "*", "mobx-react": "*"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write", "git add"], "*.ts?(x)": ["prettier --parser=typescript --write", "git add"]}}