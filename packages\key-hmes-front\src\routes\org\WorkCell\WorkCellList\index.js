/**
 * @Description: 工作单元维护-列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 16:54:28
 * @LastEditTime: 2023-05-18 11:32:43
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores/WorkCellDS';

const modelPrompt = 'tarzan.model.org.workcell';

const WorkCellList = props => {
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;

  useEffect(() => {
    tableDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_LIST.LIST`)
    tableDs.query(props.tableDs.currentPage);
  }, []);

  const columns = [
    {
      name: 'workcellCode',
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(
                `/hmes/organization-modeling/work-cell/detail/${record.data.workcellId}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'workcellName',
    },
    {
      name: 'description',
    },
    {
      name: 'workcellTypeDesc',
      align: 'center',
    },
    {
      name: 'workcellLocation',
    },
    {
      name: 'enableFlag',
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  const handleCreate = () => {
    props.history.push('/hmes/organization-modeling/work-cell/detail/create');
  };

  const handlePreviewReport = () => {
    openTab({
      key: `/hmes/commentImport/MT.MES.WORKCELL`,
      title: intl.get(`${modelPrompt}.import`).d('工作单元维护数据导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.workcellMaintenance`).d('工作单元维护')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button color={ButtonColor.primary} onClick={handlePreviewReport}>{intl.get(`${modelPrompt}.impotButton`).d('导入')}</Button>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_LIST.LIST`,
          },
          <Table
            queryBar='filterBar'
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="WorkCellList"
            customizedCode="WorkCellList"
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.model.org.workcell', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_LIST.LIST`] }),
)(WorkCellList);
