/**
 * @Description: 检验业务类型规则维护-列表页
 * @Author: <EMAIL>
 * @Date: 2023/1/30 15:47
 */
import React, { FC, useCallback, useMemo, useEffect } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge, Tag } from 'choerodon-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores';

const modelPrompt = 'tarzan.hwms.inspectBusTypeRule';

interface InspectItemListProps extends RouteComponentProps {
  tableDs: DataSet;
  customizeTable: any;
}

const InspectBusList: FC<InspectItemListProps> = props => {
  const {
    match: { path },
    tableDs,
    history,
    customizeTable,
  } = props;

  useEffect(() => {
    tableDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_LIST.TABLE`,
    );
    tableDs.query(tableDs.currentPage).then(r => r);
  }, []);

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'inspectBusinessType',
        lock: ColumnLock.left,
        width: 150,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(
                  `/hwms/inspect-business-type-rule/dist/${record!.get(
                    'inspectBusinessTypeRuleId',
                  )}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'inspectBusinessTypeDesc',
        width: 200,
        lock: ColumnLock.left,
      },
      { name: 'siteName' },
      { name: 'operatingModeDesc' },
      { name: 'reviewTypeDesc', width: 150 },
      { name: 'workFlowCode', width: 150 },
      {
        name: 'recheckFlag',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('recheckFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('recheckFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      { name: 'recheckTaskCreateNodeDesc', width: 150 },
      { name: 'recheckResultDimensionDesc', width: 150 },
      { name: 'taskAssignType', width: 150 },
      { name: 'recheckTaskAssignType', width: 150 },
      {
        name: 'docCompleteFlag',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('docCompleteFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('docCompleteFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'mergeFlag',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('mergeFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('mergeFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'mergeDimensionDesc',
        width: 300,
        renderer: ({ value }) => {
          return value?.length
            ? value.map((item, index) => <Tag key={item}>{value[index]}</Tag>)
            : null;
        },
      },
      { name: 'resultDimensionDesc', width: 150 },
      { name: 'taskSplitMethodDesc', width: 150 },
      {
        name: 'transferFlag',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('transferFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('transferFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      { name: 'transferDimension', width: 150 },
      { name: 'transferRuleCode', width: 150 },
      {
        name: 'ncCodeFlag',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('ncCodeFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('ncCodeFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'blankInspectOrderFlag',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('blankInspectOrderFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('blankInspectOrderFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      { name: 'ngAssignRuleTypeDesc', width: 150 },
      { name: 'scrapAssignRuleTypeDesc', width: 150 },
      { name: 'dispositionGroupDesc', width: 150 },
      {
        name: 'autoCreateNcReportFlag',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('autoCreateNcReportFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('autoCreateNcReportFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      { name: 'ncReportType', width: 150 },
      { name: 'inspectNcRecordDimension', width: 150 },
      { name: 'actualInspectBusinessTypeLov', width: 150 },
      { name: 'operatingEndDesc', width: 150 },
      { name: 'templateTypeDesc', width: 150 },
      {
        name: 'oneselfFlag',
        width: 180,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('oneselfFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('oneselfFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      { name: 'printTemplate' },
      { name: 'messageCode' },
      { name: 'receiverTypeCode' },
      {
        name: 'enableFlag',
        width: 140,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      { name: 'remark' },
      {
        name: 'enterFlag',
        width: 140,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record!.get('enterFlag') === 'Y' ? 'success' : 'error'}
            text={
              record!.get('enterFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
    ],
    [],
  );

  const handleAdd = useCallback(() => {
    history.push(`/hwms/inspect-business-type-rule/dist/create`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('检验业务类型规则维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_LIST.TABLE`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="InspectBusTypeRule"
            customizedCode="InspectBusTypeRule"
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_BUS_TYPE_RULE_LIST.TABLE`],
    })(InspectBusList as any),
  ),
);
